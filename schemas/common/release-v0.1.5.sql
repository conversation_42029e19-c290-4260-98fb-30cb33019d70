-- 核算科目表
CREATE TABLE `ledger_account` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `account` varchar(100) DEFAULT NULL COMMENT '科目号',
                                  `name_en` varchar(255) DEFAULT NULL COMMENT '英文名称',
                                  `name_cn` varchar(255) DEFAULT NULL COMMENT '中文名称',
                                  `name_th` varchar(255) DEFAULT NULL COMMENT '泰文名称',
                                  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `uk_account` (`account`) USING BTREE COMMENT '科目号唯一'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核算科目表';

INSERT INTO ledger_account (`account`,`name_en`,`name_cn`,`name_th`,`created_at`,`updated_at`) VALUES ('********','Expense-Own fleet-fuel','自有车队-油费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expense-Own fleet-parking','自有车队-停车费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expense-Own fleet-tolls','自有车队-过路费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Labour Services-Outsourcing','劳务费-外包','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Labour Services-External Courier','第三方运费-外协派送及其他','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Staff Benefits','福利费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Team Building','团建费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Recruiting','招聘费-招聘软件、广告等','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Annual Dinner','年会费用','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('66022500','Celebration','公司活动费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('66022300','Expenses-Conference Fee','会议费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011903','Expenses-Marketing and Advertising-Gifts','广告宣传费-礼品','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010909','Expenses-Office Expenses- Water&Electricity','办公费-水电费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('66011100','Expenses-Entertainment','业务招待费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011201','Expenses-Freight-Fuel','运费成本-油料','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011299','Expenses-Freight-Others','运费-其他','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011402','Expenses-Rent-Vehicle','租赁-车辆租赁费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64012000','Expenses-Labor Protection Supplies','劳保用品','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011905','Expenses-Marketing and Advertising-Star Endorsement','广告宣传费-明星代言费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011901','Expenses-Marketing and Advertising-Advertising','广告宣传费-广告费-线上','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011902','Expenses-Marketing and Advertising-Venue','广告宣传费-会场费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011904','Expenses-Marketing and Advertising-Printing','广告宣传费-印刷','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011208','Expense-Own fleet-Auto Insurance','自有车队-车险','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011209','Expense-Own fleet-maintenance','自有车队-维修保养','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010908','Expenses-Office Expenses- Telecommunication','办公费-通信费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010902','Low-value Consumables','低值易耗品','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011401','Expenses-Rent-Equipment','租赁-设备租赁费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64012100','Expenses-Modelling','模具费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('17010100','Intangible Assets','无形资产','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011500','Expenses-Maintenance & Repairs','维修保养','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010800','Fixed Assets-Electronic Equipment','固定资产-电子设备','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010400','Fixed Assets-Office Furniture','固定资产-办公家具','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010900','Software License','软件使用费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010600','Fixed Assets-Office Equipment','固定资产-办公设备','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010300','Fixed Assets-Machinery Equipment','固定资产-机器设备','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010500','Fixed Assets-Instruments and Tools','固定资产-器具工具','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Marketing and Advertising-Offline','广告费-线下','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Fixed Assets-Transportation Equipment','固定资产-运输设备','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Office Expenses-Office Supplies','办公费-办公用品','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Financial expenses-Bank Charge','财务费用 -手续费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Office Expenses- Miscellaneous','办公费-其他','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Office Expenses- Office Leasing expense','办公费-房租','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Office Expenses-Security','办公费-保安费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Visa Management','签证管理费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Staff-Commercial Insurance','员工-商业保险','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expenses-Staff Training Fund-Training','员工培训费-培训','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Expense-Own fleet-driver rent','自有车队-司机房租','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('********','Inventory','库存商品','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011602','Expenses-Professional Service-Auditing','专业服务-审计费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011202','Expenses-Third Party Freight-Line Haul','第三方运费-支干线运费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010600','Warehousing Expense','仓储费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010914','Expense-Branches Registration fee','网点注册费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011601','Expenses-Professional Service-Consulting','专业服务-咨询费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011215','Expense-Own fleet-Goods insurance','自有车队-货物险','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011605','Expenses-Professional Service-Financial Advisor','专业服务-融资','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011603','Expenses-Professional Service-Lawyer','专业服务-律师费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010903','Expenses-Office Expenses- Drinking Water','办公费-饮用水','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010913','Expenses-Office Expenses- Parking','办公费-车位费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011001','Expenses-Travel expenses-Transportation','差旅费-交通费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011002','Expenses-Travel expenses-Hotel','差旅费-住宿费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010922','Expenses-Medical examination fee','签证体检费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030100','Tax and Associate Charge-Stamp Duty','税金及附加-印花税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030200','Tax and Associate Charge-Billboard Tax','税金及附加-广告牌税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030300','Tax and Associate Charge-Business Tax','税金及附加-营业税-WHT','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030400','Tax and Associate Charge-Employment tax','税金及附加-雇佣税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030500','Tax and Associate Charge-Land tax','税金及附加-土地税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64030600','Tax and Associate Charge-Vehicle License Plate Tax','税金及附加-车牌税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011300','Expenses-Local Taxi','交通费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010907','Expenses-Office Expenses-Cleaning','办公费-保洁费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010911','Expenses-Office Expenses- Property Fee','办公费-物业费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010904','Expenses-Office Expenses- Express','办公费-快递费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010905','Expenses-Office Expenses- Printing','办公费-打印费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010912','Expenses-Office Expenses- Network','办公费-网络费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011204','Expense-Cross-island','跨岛费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('16010900','Long-term Deferred Expenses-Decoration','长期待摊费用-装修','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010925','Expenses-Demolition fee','拆卸费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010101','Expenses-Salary-Basic Salary','工资-基本工资','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('22210501','Tax Payables-WHT(Manual)','应交税费-代扣代缴税（手工）','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('22210200','Tax Payables-Unpaid VAT','应交税费-未交增值税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('22210503','Tax Payables-VAT-Foreign-PP36','应交税费-代扣代缴税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('22210502','Tax Payables-WHT(Manual)-PND54','应交税费-代扣代缴税','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010924','Expenses-COVID-19 inspection fee','新冠检测费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010919','Expenses-Office Expenses-Document storage fee','办公费-文件存储','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64010918','Expenses-Office Expenses-Copy','办公费-抄资料费','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011911','Expenses-PR Communication-government cooperation','公关传播-政府合作','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011910','Expenses-PR Communication-Maintain media Relations','公关传播-媒体公关维护','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011909','Expenses-PR Communication-Media Cooperation','公关传播-媒体合作','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011908','Expenses-PR Communication-Print & Network Media','公关传播-平面&网络媒体传播','','2021-05-10 18:47:19','2021-05-10 18:47:19'), ('64011403','Expenses-Rent-Copier','租赁费-打印机','','2021-05-10 18:47:19','2021-05-10 18:47:19');



CREATE TABLE `budget_object_ledger` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `budget_id` int(11) DEFAULT NULL COMMENT '预算科目Id',
                                        `ledger_id` int(11) DEFAULT NULL COMMENT '核算科目id',
                                        `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                        `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                        PRIMARY KEY (`id`),
                                        KEY `idx_budget_ledger` (`budget_id`,`ledger_id`) USING BTREE COMMENT '根据预算科目id找核算科目id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算科目关联核算科目表。';

INSERT INTO budget_object_ledger (`budget_id`,`ledger_id`) VALUES ('2','1'), ('3','2'), ('4','3'), ('6','4'), ('7','5'), ('8','6'), ('9','7'), ('10','8'), ('11','9'), ('12','10'), ('13','11'), ('15','12'), ('16','13'), ('18','14'), ('19','15'), ('20','16'), ('21','17'), ('22','18'), ('23','19'), ('24','20'), ('25','21'), ('26','22'), ('28','23'), ('29','24'), ('30','25'), ('31','26'), ('32','27'), ('33','25'), ('34','28'), ('35','29'), ('36','30'), ('37','31'), ('38','32'), ('39','33'), ('40','34'), ('41','35'), ('42','36'), ('43','37'), ('44','38'), ('45','39'), ('46','40'), ('47','41'), ('49','42'), ('50','43'), ('52','44'), ('53','45'), ('54','42'), ('56','46'), ('58','47'), ('59','24'), ('60','48'), ('61','48'), ('62','49'), ('63','50'), ('64','51'), ('65','52'), ('66','40'), ('67','53'), ('68','24'), ('69','54'), ('72','15'), ('79','17'), ('80','55'), ('81','56');



CREATE TABLE `budget_object_product_ledger` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `product_id` int(11) DEFAULT NULL COMMENT '预算科目产品Id（budget_ojbect_product）',
                                                `ledger_id` int(11) DEFAULT NULL COMMENT '核算科目id',
                                                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                                                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                                PRIMARY KEY (`id`),
                                                KEY `idx_product_ledger` (`product_id`,`ledger_id`) USING BTREE COMMENT '根据预算科目产品id找核算科目id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算科目产品关联核算科目表。';

INSERT INTO budget_object_product_ledger (`product_id`,`ledger_id`) VALUES ('1','39'), ('2','57'), ('3','58'), ('4','26'), ('5','16'), ('6','16'), ('7','59'), ('8','59'), ('9','59'), ('10','59'), ('11','59'), ('12','59'), ('14','59'), ('15','60'), ('16','61'), ('17','44'), ('18','14'), ('19','59'), ('20','62'), ('21','63'), ('22','64'), ('23','65'), ('24','66'), ('25','67'), ('26','68'), ('27','59'), ('28','69'), ('29','70'), ('30','71'), ('31','72'), ('32','43'), ('33','57'), ('34','58'), ('35','30'), ('36','52'), ('37','73'), ('38','59'), ('39','74'), ('40','29'), ('41','29'), ('42','29'), ('43','29'), ('44','29'), ('45','29'), ('46','30'), ('47','29'), ('48','29'), ('49','30'), ('50','26'), ('51','30'), ('52','26'), ('53','26'), ('54','30'), ('55','30'), ('56','30'), ('57','26'), ('58','30'), ('59','30'), ('60','26'), ('61','26'), ('62','26'), ('63','30'), ('64','30'), ('65','26'), ('66','26'), ('67','26'), ('68','26'), ('69','26'), ('70','26'), ('71','26'), ('72','26'), ('73','26'), ('74','26'), ('75','35'), ('76','26'), ('77','30'), ('78','30'), ('79','26'), ('80','32'), ('81','26'), ('82','26'), ('83','26'), ('84','26'), ('85','26'), ('86','26'), ('87','30'), ('88','30'), ('89','30'), ('90','30'), ('91','30'), ('92','26'), ('93','35'), ('94','26'), ('95','35'), ('96','30'), ('97','26'), ('98','35'), ('99','35'), ('100','30'), ('101','75'), ('102','26'), ('103','30'), ('104','30'), ('105','30'), ('106','30'), ('107','30'), ('108','30'), ('109','31'), ('110','30'), ('111','30'), ('112','30'), ('113','30'), ('114','30'), ('115','31'), ('116','30'), ('117','30'), ('118','31'), ('119','30'), ('120','75'), ('121','30'), ('122','75'), ('123','30'), ('124','26'), ('125','30'), ('126','30'), ('127','30'), ('128','30'), ('129','30'), ('130','31'), ('131','30'), ('132','30'), ('133','30'), ('134','76'), ('135','76'), ('136','75'), ('137','30'), ('138','30'), ('139','30'), ('140','33'), ('141','35'), ('142','33'), ('143','30'), ('144','31'), ('145','29'), ('146','30'), ('147','29'), ('148','34'), ('149','29'), ('150','29'), ('151','35'), ('152','35'), ('153','35'), ('154','35'), ('155','36'), ('156','26'), ('157','35'), ('158','35'), ('159','35'), ('160','35'), ('161','35'), ('162','35'), ('163','34'), ('164','36'), ('165','36'), ('166','34'), ('167','35'), ('168','34'), ('169','26'), ('170','34'), ('171','34'), ('172','26'), ('173','34'), ('174','32'), ('175','32'), ('176','26'), ('177','34'), ('178','36'), ('179','26'), ('180','26'), ('181','34'), ('182','34'), ('183','32'), ('184','34'), ('185','26'), ('186','26'), ('187','26'), ('188','26'), ('189','26'), ('190','34'), ('191','34'), ('192','34'), ('193','34'), ('194','26'), ('195','26'), ('196','34'), ('197','35'), ('198','30'), ('199','34'), ('200','26'), ('201','35'), ('202','34'), ('203','34'), ('204','34'), ('205','34'), ('206','30'), ('207','32'), ('208','34'), ('209','34'), ('210','26'), ('211','34'), ('213','36'), ('214','34'), ('215','34'), ('216','34'), ('217','35'), ('218','30'), ('219','30'), ('220','32'), ('221','32'), ('222','30'), ('223','32'), ('224','32'), ('225','32'), ('226','26'), ('227','32'), ('228','32'), ('229','35'), ('230','35'), ('231','26'), ('232','35'), ('233','30'), ('234','30'), ('235','30'), ('236','30'), ('237','32'), ('238','32'), ('239','35'), ('240','32'), ('241','32'), ('242','32'), ('243','32'), ('244','32'), ('245','32'), ('246','32'), ('247','30'), ('248','32'), ('249','32'), ('250','32'), ('251','32'), ('252','32'), ('253','32'), ('254','30'), ('255','32'), ('256','30'), ('257','34'), ('258','30'), ('259','26'), ('260','38'), ('261','38'), ('262','38'), ('263','38'), ('264','38'), ('265','38'), ('266','35'), ('267','35'), ('268','35'), ('269','35'), ('270','35'), ('271','34'), ('272','35'), ('273','35'), ('274','35'), ('275','35'), ('276','35'), ('277','35'), ('278','35'), ('279','26'), ('280','35'), ('281','26'), ('282','26'), ('283','35'), ('284','34'), ('285','35'), ('286','35'), ('287','35'), ('288','35'), ('289','35'), ('290','26'), ('291','26'), ('292','35'), ('293','26'), ('294','34'), ('295','35'), ('296','34'), ('297','35'), ('298','35'), ('299','35'), ('300','35'), ('301','35'), ('302','34'), ('303','26'), ('304','35'), ('305','26'), ('306','34'), ('307','35'), ('308','34'), ('309','34'), ('310','34'), ('311','35'), ('312','35'), ('313','35'), ('314','35'), ('315','35'), ('316','34'), ('317','35'), ('318','35'), ('319','35'), ('320','35'), ('321','35'), ('322','35'), ('323','35'), ('324','35'), ('325','34'), ('326','35'), ('327','35'), ('328','35'), ('329','35'), ('330','39'), ('331','35'), ('332','34'), ('333','35'), ('334','35'), ('335','31'), ('336','34'), ('337','35'), ('338','34'), ('339','35'), ('340','35'), ('341','35'), ('342','35'), ('343','35'), ('344','34'), ('345','35'), ('346','34'), ('347','34'), ('348','30'), ('349','34'), ('350','26'), ('351','34'), ('352','30'), ('353','32'), ('354','34'), ('355','30'), ('356','32'), ('357','34'), ('358','30'), ('359','34'), ('360','26'), ('361','34'), ('362','26'), ('363','26'), ('364','26'), ('365','26'), ('366','26'), ('367','30'), ('368','26'), ('369','30'), ('370','26'), ('371','26'), ('372','26'), ('373','26'), ('374','35'), ('375','35'), ('376','26'), ('377','26'), ('378','26'), ('379','39'), ('380','32'), ('381','26'), ('382','26'), ('383','26'), ('384','32'), ('385','26'), ('386','35'), ('387','26'), ('388','26'), ('389','26'), ('390','26'), ('391','26'), ('392','26'), ('393','35'), ('394','39'), ('395','42'), ('396','77'), ('397','78'), ('398','78'), ('399','79'), ('400','80'), ('401','81'), ('402','82'), ('404','39'), ('405','73'), ('406','83'), ('407','33'), ('408','84'), ('409','85'), ('410','86'), ('411','87'), ('412','88'), ('413','37'), ('414','35'), ('415','35'), ('416','35'), ('417','48'), ('418','48'), ('419','48'), ('420','48'), ('421','48'), ('422','48'), ('423','48'), ('424','48'), ('425','48'), ('426','48'), ('427','48'), ('428','48'), ('429','48'), ('430','48'), ('431','48'), ('432','48'), ('433','48'), ('434','48'), ('435','48'), ('436','48'), ('437','48'), ('439','48'), ('440','48'), ('441','48'), ('442','48'), ('443','48'), ('444','48'), ('445','48'), ('446','48'), ('447','48'), ('448','48'), ('449','48'), ('450','48'), ('451','48'), ('452','48'), ('453','48'), ('454','48'), ('455','48'), ('456','48'), ('457','48'), ('458','48'), ('459','48'), ('460','48'), ('461','48'), ('462','48'), ('463','48'), ('464','48'), ('465','48'), ('466','48'), ('467','48'), ('468','48'), ('469','59'), ('470','59'), ('471','61'), ('472','64'), ('473','65'), ('474','67'), ('475','66'), ('476','59'), ('479','33'), ('480','59'), ('481','44'), ('483','43'), ('484','44'), ('485','69'), ('486','69'), ('487','69'), ('488','69'), ('489','69'), ('490','69'), ('491','69'), ('492','30'), ('493','89'), ('494','73'), ('495','33'), ('496','29'), ('497','59'), ('498','77'), ('499','78'), ('500','78'), ('501','81'), ('502','79'), ('503','80'), ('504','62'), ('505','66'), ('506','42'), ('507','64'), ('508','67'), ('509','65'), ('510','44'), ('511','85'), ('512','86'), ('513','87'), ('514','88'), ('515','48'), ('516','41'), ('517','33'), ('518','58'), ('519','52'), ('522','69'), ('523','30'), ('524','33'), ('526','33');



ALTER TABLE `purchase_apply_product`
    ADD COLUMN `ledger_account_id` int(11) NULL DEFAULT 0 COMMENT '核算科目id' AFTER `level_code`;


ALTER TABLE `purchase_order_product`
    ADD COLUMN `ledger_account_id` int(11) NULL DEFAULT 0 COMMENT '核算科目id' AFTER `level_code`;

ALTER TABLE `purchase_payment_receipt`
    ADD COLUMN `ledger_account_id` int(11) NULL DEFAULT 0 COMMENT '核算科目id' AFTER `level_code`;

-- crm审批表
create table crm_quotation_apply (
                                     `id` int unsigned not null auto_increment comment '自增ID',
                                     `crmno` varchar(25) not null default '' comment 'crm报价审批编号',
                                     `quoted_price_list_sn` varchar(16) NOT NULL DEFAULT '' COMMENT '报价单号',
                                     `customer_id` varchar(50) not null default '' comment '客户ID',
                                     `customer_name` varchar(50) not null default '' comment '客户名称',
                                     `customer_mobile` varchar(30) not null default '' comment '手机号',
                                     `customer_type_category` tinyint not null default 0 comment '客户类型  1 小c 2 ka 3 crm',
                                     `create_id` int  not null default 0 comment '创建人ID',
                                     `create_name` varchar(50) not null default '' comment '创建人名称',
                                     `apply_user_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人工号(flash_user_id)',
                                     `apply_user_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人姓名',
                                     `apply_user_department` int NOT NULL DEFAULT 0 COMMENT '申请人部门id',
                                     `apply_user_department_type` tinyint NOT NULL DEFAULT 0 COMMENT '申请人部门类型 1 sales 2 pmd 3 shop 4 network 5 ffm 6 other',
                                     `total_discount_rate` float(6,2) unsigned not null default 0000.00 comment '综合折扣率，保留2为小数 0-100',
    `return_discount_rate` float(6,2) unsigned not null default 0000.00 comment '退件折扣率,保留2为小数',
    `calculation_method` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '计费方式 1 仅按重量 2 仅按尺寸 3 按体积重',
    `calculation_method_value` int(10) unsigned DEFAULT '0' COMMENT '计费方式 值',
    `quoted_price_type` tinyint not null default 0 comment '报价类型 1 固定折扣 2 阶梯折扣 3 特殊折扣',
    `credit_period` int(2) unsigned NOT NULL DEFAULT '0' COMMENT '信用期限',
    `cod_fee_rate` float(6,2) unsigned not null default 0000.00 comment 'CDO 手续费费率，保留2为小数',
    `coupon_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '优惠卷类型',
    `coupon_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠卷数量',
    `coupon_expire` tinyint(1) NOT NULL DEFAULT '0' COMMENT '有效期时间',
    `discount_valid_type` tinyint(1) unsigned DEFAULT NULL COMMENT '折扣有效期类型  枚举 【1】与主合同保持一致 【2】固定有效期 【3】 自定义有效期',
    `valid_time` datetime DEFAULT NULL COMMENT '生效时间',
    `invalid_time` datetime DEFAULT NULL COMMENT '失效时间',
    `valid_days` int unsigned DEFAULT 0 COMMENT '有效天数',
    `price_type` tinyint unsigned DEFAULT 0 comment '价格表类型 1是普通 2是折扣 3是特殊',
    `return_fee_method` tinyint(1) unsigned not null default 0 COMMENT '返利形式 枚举 【1】固定金额返利 【2】固定形式返利',
    `faraway_fee` int(10) not null default 0  COMMENT '偏远地区费用 整数 直接存储',
    `lowest_discount_rate` float(6,2) unsigned not null default 0000.00 comment '申请运费最低折扣',
    `valid_promise_num` varchar(25) not null default '' comment '折扣期内承诺单量',
    `created_at` datetime not null default CURRENT_TIMESTAMP comment '创建时间',
    `updated_at` datetime not null default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '编辑时间',
    `state` tinyint null comment '当前状态，1待审批，2驳回 ，3通过，4撤回',
    primary key (id),
    unique key idx_quoted_price_list_sn(`quoted_price_list_sn`)
)ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT 'crm报价审批';

-- 翻译
insert into translations (lang, t_key, t_value) values
('en', 'quoted_price_type_1', 'Fixed Discount'),
('th', 'quoted_price_type_1', 'ส่วนลดคงที่'),
('zh-CN', 'quoted_price_type_1', '固定折扣'),
('en', 'quoted_price_type_2', 'Tiered Discount'),
('th', 'quoted_price_type_2', 'ส่วนลดขั้นบันได'),
('zh-CN', 'quoted_price_type_2', '阶梯折扣'),
('en', 'quoted_price_type_3', 'Special Discount'),
('th', 'quoted_price_type_3', 'ส่วนลดพิเศษ'),
('zh-CN', 'quoted_price_type_3', '特殊折扣');

-- 权限工号
-- FEX总经理（21848）PMD总经理（28602）Network director（31849）user operation man-- ager（21318）sales manager（33916）sales director（33288）
-- 添加权限工号
update staff_permission set permission_ids = concat_ws(',', '323,324,325,326,327', permission_ids) where staff_id in (21848, 28602, 31849, 21318, 33916, 33288);


-- oa 菜单权限配置
insert into permission (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`)
    value (323, 'menu.quotation', '报价管理', '', 0, 1, 1);
insert into permission (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`)
    value (324, 'menu.quotation.audit', '报价审核', '', 323, 1, 1);
-- oa 操作权限
insert into permission (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`)
    value (325, 'action.crm.apply.list', '审核列表', '', 324, 2, 0);
insert into permission (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`)
    value (326, 'action.crm.apply.audit', '审核操作', '', 324, 2, 0);
insert into permission (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`)
    value (327, 'action.crm.apply.view', '详情操作', '', 324, 2, 0);

##########################兴武：

#ordinary_payment 表添加字段

ALTER TABLE ordinary_payment ADD `pay_type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '普通付款类型，0其他，1是探亲费或团建费';


-- 相关业务主表 增加汇率字段(汇率需固化)
ALTER TABLE `contract`
    ADD COLUMN `exchange_rate` decimal(10, 3) UNSIGNED NOT NULL DEFAULT 0.000 COMMENT '当前币种对应默认币种的汇率' AFTER `payment_currency`;

ALTER TABLE `contract_store_renting`
    ADD COLUMN `exchange_rate` decimal(10, 3) UNSIGNED NOT NULL DEFAULT 0.000 COMMENT '当前币种对应默认币种的汇率' AFTER `money_symbol`;

ALTER TABLE `loan`
    ADD COLUMN `exchange_rate` decimal(10, 3) UNSIGNED NOT NULL DEFAULT 0.000 COMMENT '当前币种对应默认币种的汇率' AFTER `currency`;

ALTER TABLE `reimbursement`
    ADD COLUMN `exchange_rate` decimal(10, 3) UNSIGNED NOT NULL DEFAULT 0.000 COMMENT '当前币种对应默认币种的汇率' AFTER `currency`;

ALTER TABLE `payment_store_renting`
    ADD COLUMN `exchange_rate` decimal(10, 3) UNSIGNED NOT NULL DEFAULT 0.000 COMMENT '当前币种对应默认币种的汇率' AFTER `currency`;