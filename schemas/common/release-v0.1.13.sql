-- 泰国-菲律宾同步执行

-- 供应商二期需求
-- 变更操作与菜单关系
update  permission set ancestry = 382 where id in (10,17,18,19);
update  permission set `name` = '新增' where id = 10;

-- 新增二级菜单
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (382, 'action.vendor.info', '供应商信息', '供应商信息', 4, 2, 1, '2021-06-04 07:23:15');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (383, 'action.vendor.audit', '供应商审核', '供应商审核', 4, 2, 1, '2021-06-04 07:26:57');

-- 供应商审批流
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (67, '供应商信息审批 - v202106', 59, '供应商信息审批 - v202106', '2021-06-03 21:34:57');

-- 供应商审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (983, 67, '结束', 6, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (982, 67, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '54677', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (981, 67, '北京财务', 3, 0, NULL, NULL, NULL, NULL, 1, '55849,57625,61682,62721,62306,66553,66979,62358,64234,64377,70080', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (980, 67, '泰国财务', 3, 0, NULL, NULL, NULL, NULL, 1, '54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294,33306,17178,20254', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (979, 67, '采购', 3, 0, NULL, NULL, NULL, NULL, 1, '41081,52731', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (978, 67, '员工提交', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL);

-- 供应商审批节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 978, 979, NULL, NULL, '提交->采购节点', 10, '2021-06-03 14:04:46', '2021-06-03 14:04:46');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 979, 980, NULL, NULL, '采购节点->泰国财务', 10, '2021-06-03 14:05:07', '2021-06-03 14:05:07');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 980, 981, NULL, NULL, '泰国财务->北京财务', 10, '2021-06-03 14:05:22', '2021-06-03 14:05:22');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 981, 982, NULL, NULL, '北京财务->Finance Senior Manager', 10, '2021-06-03 14:05:42', '2021-06-03 14:05:42');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 982, 983, NULL, NULL, 'Finance Senior Manager->结束', 10, '2021-06-03 14:05:58', '2021-06-03 14:05:58');

-- 供应商表
-- 修改原表字段约束
ALTER TABLE `vendor`
MODIFY COLUMN `vendor_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商名称' AFTER `vendor_id`;

ALTER TABLE `vendor`
DROP INDEX `vendor_id_inx`,
ADD UNIQUE INDEX `idx_vendor_id`(`vendor_id`) USING BTREE;

ALTER TABLE `vendor`
ADD UNIQUE INDEX `idx_vendor_name`(`vendor_name`);

ALTER TABLE `vendor`
MODIFY COLUMN `bank_account_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行账户名称' AFTER `bank_no`,
MODIFY COLUMN `sap_supplier_no` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'SAP供应商号码' AFTER `bank_account_name`;

ALTER TABLE `vendor`
DROP INDEX `status_inx`;

ALTER TABLE `vendor`
MODIFY COLUMN `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '供应商审批终态: 1-待审核; 2-已驳回; 3-已通过; 4-已撤回（默认1）' AFTER `attachment`;

-- 新增字段
ALTER TABLE `vendor`
ADD COLUMN `audit_stage` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '审批阶段: 1-未审批；2-审批中；3-审批结束（默认1）' AFTER `updated_at`,
ADD COLUMN `last_audit_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '最新审批时间' AFTER `audit_stage`,
ADD COLUMN `last_auditor_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最近审批人工号' AFTER `last_audit_time`,
ADD COLUMN `audit_rejected_count` mediumint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批驳回次数（终态）' AFTER `last_auditor_id`,
ADD COLUMN `audit_approved_count` mediumint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批通过次数（终态）' AFTER `audit_rejected_count`,
ADD COLUMN `updated_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人工号' AFTER `updated_at`;

ALTER TABLE `vendor`
ADD COLUMN `approval_version_no` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审批通过版本号' AFTER `audit_approved_count`;

ALTER TABLE `vendor`
MODIFY COLUMN `vendor_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码' AFTER `id`;

-- 审批状态变更为 终态 通过
update vendor set `status` = 3 where 1;

-- 审批阶段变更为 审批结束
update vendor set `audit_stage` = 3 where 1;

-- 最新审批时间 初始化为 创建时间
update vendor set `last_audit_time` = `created_at` where 1;

-- 审批通过次数 初始化为 1
update vendor set `audit_approved_count` = 1 where 1;

-- 审批通过版本号
update vendor set `approval_version_no` = 1 where 1;


-- 历史审批通过版本表
CREATE TABLE `vendor_history_approval_version` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `src_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '供应商原始id',
  `vendor_id` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码',
  `vendor_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商名称',
  `company_website` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司官网',
  `ownership` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '供应商归属地，1:中国，2:泰国',
  `company_nature` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '公司性质，1：自然人；2：公司',
  `certificate_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '证件类型，1：社会信用代码证件；2：护照；3：身份证',
  `identification_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '证件号码',
  `artificial_person` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '法人代表',
  `company_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司电话',
  `company_address` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商地址',
  `contact` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人电话',
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人邮箱',
  `bank_code` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '银行编码',
  `bank_no` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行卡号',
  `bank_account_name` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行账户名称',
  `sap_supplier_no` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'SAP供应商号码',
  `attachment` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件,多个使用","分割',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '供应商审批终态: 1-待审核; 2-已驳回; 3-已通过; 4-已撤回（默认1）',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '申请人姓名',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `updated_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '更新人工号',
  `audit_stage` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '审批阶段: 1-未审批；2-审批中；3-审批结束（默认1）',
  `last_audit_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最新审批时间',
  `last_auditor_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最近审批人工号',
  `audit_rejected_count` mediumint(5) unsigned NOT NULL DEFAULT '0' COMMENT '审批驳回次数（终态）',
  `audit_approved_count` mediumint(5) unsigned NOT NULL DEFAULT '0' COMMENT '审批通过次数（终态）',
  `approval_version_no` int(10) unsigned NOT NULL DEFAULT '1' COMMENT '审批通过版本号',
  `archive_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '版本归档时间',
  `archiver_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '归档人',
  `application_module_ids` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商应用模块id集合,json',
  `attachment_files` text COLLATE utf8mb4_unicode_ci COMMENT '附件,json',
  PRIMARY KEY (`id`),
  KEY `idx_vendor_id` (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商-历史审批通过的版本数据';

-- 供应商修改日志表
CREATE TABLE `vendor_modify_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `vendor_id` varchar(32) NOT NULL DEFAULT '' COMMENT '供应商编号',
  `before_data` text COMMENT '修改前数据',
  `after_data` text NOT NULL COMMENT '修改后数据',
  `create_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建者工号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_vendor_id` (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商数据-修改日志表';


-- 供应商与应用模块关系表
CREATE TABLE `vendor_application_module_rel` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `vendor_id` varchar(32) NOT NULL DEFAULT '' COMMENT '供应商编号',
  `application_module_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '应用模块id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_vendor_id` (`vendor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商与应用模块关系表';

-- 应用模块配置表
CREATE TABLE `vendor_application_module` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `translation_code` varchar(128) NOT NULL DEFAULT '' COMMENT '应用模块code标识（唯一），同词库翻译key',
  `name_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '应用模块名称',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-未删除；1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_translation_code` (`translation_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商-应用模块';


-- 初始化数据
INSERT INTO `vendor_application_module`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (1, 'application_module.1', '采购', 0, '2021-06-04 07:05:18');
INSERT INTO `vendor_application_module`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (2, 'application_module.2', '普通付款', 0, '2021-06-04 07:05:34');