
-- 刘文超8710
ALTER TABLE reimbursement_detail ADD serial_no varchar(30) DEFAULT NULL COMMENT '外协--审批编号' , ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE reimbursement_detail ADD `applicant_staff_name` varchar(255) DEFAULT '' COMMENT '外协--工作订单申请人' , ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE reimbursement_detail ADD   `sys_store_name` varchar(255) DEFAULT '' COMMENT '外协--所属网点' , ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE reimbursement_detail ADD `final_audit_num` int(3) DEFAULT NULL COMMENT '外协--雇佣人数' , ALGORITHM=INPLACE, LOCK=NONE;

ALTER TABLE reimbursement_detail ADD `employment_days` int(3) DEFAULT NULL COMMENT '外协--雇佣天数' , ALGORITHM=INPLACE, LOCK=NONE;

UPDATE `budget_object` SET `template_type`='1' WHERE `id`in(6,7);
-- 索引
ALTER TABLE `reimbursement_detail` ADD INDEX `idx_serial_no` (`serial_no`), ALGORITHM=INPLACE, LOCK=NONE;


-- 徐世龙8476
CREATE TABLE `sys_dept_operate_logs` (
                                         `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '操作日志表',
                                         `operater` int(10) NOT NULL COMMENT '操作者ID',
                                         `operater_name` varchar(50) NOT NULL DEFAULT '' COMMENT '操作者姓名',
                                         `business_id` int(10) DEFAULT NULL COMMENT '业务id',
                                         `type` varchar(32) NOT NULL DEFAULT '' COMMENT '操作类型',
                                         `request_body` text COMMENT '请求内容',
                                         `before` text COMMENT '变更之前',
                                         `after` text COMMENT '变更之后',
                                         `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                                         PRIMARY KEY (`id`),
                                         KEY `idx_operater` (`operater`),
                                         KEY `idx_business_id` (`business_id`),
                                         KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='部门信息变更记录表';


INSERT INTO `permission`(`key`, `name`, `description`, `ancestry`, `type`, `sort`) VALUES ('menu.organization.dept_sap_edit', '部门sap信息编辑', '部门sap信息编辑', 59, 2, 1);