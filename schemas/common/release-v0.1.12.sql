ALTER TABLE `purchase_order_product`
    ADD COLUMN `wht_cate` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'WHT类别：0无，1 : PND3, 2 : PND53',
ADD COLUMN `wht_rate` int(3) NOT NULL DEFAULT '0' COMMENT 'WHT税率：值为整数(0-100), 单位 %',
ADD COLUMN `wht_amount` bigint(20) NOT NULL DEFAULT '0'  COMMENT 'WHT金额=不含税金额*WHT税率';


ALTER TABLE `purchase_order`
    ADD COLUMN `wht_total_amount` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'wht总金额';

-- 薪酬扣款
INSERT INTO `workflow` (`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (68, '薪酬扣款-其他公司-v9034', 15, '薪酬扣款-其他公司-v9034', now());

-- 薪资发放
INSERT INTO `workflow` (`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (69, '薪资发放-其他公司-v9034', 43, '薪资发放-其他公司-v9034', now());


-- 薪酬扣款审批节点
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (921, 68, '开始', 1, 0, NULL, NULL, NULL, '1', 1, '', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (922, 68, 'CPO', 3, 0, NULL, NULL, NULL, '1', 1, '56780', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (923, 68, 'CEO', 3, 0, NULL, NULL, NULL, '1', 1, '17008', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (924, 68, 'AP(泰国)', 3, 0, NULL, NULL, NULL, '1', 1, '54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,32739', NULL, '{\"wages\":[\"wht_category\",\"wht_tax_rate\",\"wht_amount\",\"actually_amount\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (925, 68, 'AP Supervisor(泰国)', 3, 0, NULL, NULL, NULL, '1', 1, '17178,33306', NULL, '{\"wages\":[\"wht_category\",\"wht_tax_rate\",\"wht_amount\",\"actually_amount\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (926, 68, 'AP Supervisor(北京)', 3, 0, NULL, NULL, NULL, '1', 1, '72336', NULL, '{\"wages\":[\"wht_category\",\"wht_tax_rate\",\"wht_amount\",\"actually_amount\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (927, 68, 'Finance Manager', 3, 0, NULL, NULL, NULL, '1', 1, '20254', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (928, 68, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, '1', 1, '35805', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (929, 68, 'Finance Director', 3, 0, NULL, NULL, NULL, '1', 1, '17152', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (930, 68, '结束', 6, 0, NULL, NULL, NULL, '1', 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (948, 68, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (949, 68, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '17152', NULL, '', NULL);


-- 薪资发放审批节点
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (931, 69, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (932, 69, 'CPO(56780)', 3, 0, NULL, NULL, NULL, NULL, 1, '56780', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (933, 69, 'CEO(17008)', 3, 0, NULL, NULL, NULL, NULL, 1, '17008', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (934, 69, 'AP(泰国)', 3, 0, NULL, NULL, NULL, NULL, 1, '54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,32739', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (935, 69, 'AP Supervisor(泰国)', 3, 0, NULL, NULL, NULL, NULL, 1, '17178,33306', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (936, 69, 'AP Supervisor(北京)', 3, 0, NULL, NULL, NULL, NULL, 1, '72336', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (937, 69, 'Finance Manager  20254', 3, 0, NULL, NULL, NULL, NULL, 1, '20254', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (938, 69, 'Finance Senior Manager=35805', 3, 0, NULL, NULL, NULL, NULL, 1, '35805', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (939, 69, 'Finance Director   17152', 3, 0, NULL, NULL, NULL, NULL, 1, '17152', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (940, 69, '结束节点', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (947, 69, '指定工号（32122）', 3, 0, NULL, NULL, NULL, NULL, 1, '32122', NULL, '', NULL);

-- 薪酬扣款审批判断
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 921, 948, NULL, NULL, '申请->直接上级', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 948, 922, '($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)', 'getAmount,getCurrency', '直接上级->CPO', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 948, 924, '($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1<10000 && $p2==3)', 'getAmount,getCurrency', '直接上级->AP(泰国)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 922, 924, NULL, NULL, 'CPO->AP(泰国)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 924, 925, NULL, NULL, 'AP(泰国)->APS(泰国)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 925, 926, NULL, NULL, 'APS(泰国)->APS(北京)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 926, 930, '($p1<2000 && $p2==1) || ($p1<60 && $p2==2) || ($p1<400 && $p2==3)', 'getAmount,getCurrency', 'APS(北京)->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 926, 927, '($p1>=2000 && $p2==1) || ($p1>=60 && $P2==2) || ($p1>=400 && $p2==3)', 'getAmount,getCurrency', 'APS(北京)->FM', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 927, 928, NULL, NULL, 'FM->FSM', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 928, 929, '($p1>=20000 && $p2==1) || ($p1>=600 && $p2==2) || ($p1>=4000 && $p2==3)', 'getAmount,getCurrency', 'FSM->FD', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 928, 930, '($p1<20000 && $p2==1)  || ($p1<600 && $p2==2) || ($p1<4000 && $p2==3)', 'getAmount,getCurrency', 'FSM->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 929, 949, '($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)', 'getAmount,getCurrency', 'FD->CFO', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 929, 930, '($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1< 10000 && $p2==3)', 'getAmount,getCurrency', 'FD->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 949, 923, NULL, NULL, 'CFO->CEO', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 68, 923, 930, NULL, NULL, 'CEO->结束', 10, now(), now());

-- 薪资发放判断
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 931, 932, NULL, NULL, '开始->CPO(56780)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 932, 933, '($p1>2000000000 && $p2==1) || ($p1>66000000 && $p2==2) || ($p1>4100000000 && $p2==3)', 'getAmount,getCurrency', '泰铢> 2000000 CPO->CEO', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 932, 934, NULL, NULL, 'CPO(56780)->AP泰国', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 933, 934, NULL, NULL, 'CEO->AP泰国', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 934, 935, NULL, NULL, 'AP泰国->APS(泰国)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 935, 936, NULL, NULL, 'APS泰国->APS(北京)', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 936, 940, '($p1<=2000000 && $p2==1) || ($p1<=60000 && $p2==2) || ($p1<=400000 && $p2==3)', 'getAmount,getCurrency', '泰铢<2000 APS(北京)->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 936, 937, NULL, NULL, 'APS北京->FM', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 937, 938, NULL, NULL, 'FM->FSM', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 938, 940, '($p1<=20000000 && $p2==1)  || ($p1<=600000 && $p2==2) || ($p1<=4000000 && $p2==3)', 'getAmount,getCurrency', '泰铢<20000 FSM->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 938, 939, NULL, NULL, 'FSM->FD', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 939, 940, NULL, NULL, 'FD->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 931, 947, '$p1 == 23357', 'getSubmitterId', '申请人为jeab（23357）,测试的时候用22627', 20, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 69, 947, 932, '', '', '32122->CPO', 20, now(), now());


-- 普通付款，没根据设计的字段，设置只能改的字段。
ALTER TABLE `ordinary_payment_detail`
    ADD COLUMN `ledger_account_id` int(11) NULL DEFAULT 0 COMMENT '核算科目id' AFTER `level_code`;


ALTER TABLE `reimbursement_detail`
    ADD COLUMN `ledger_account_id` int(11) NULL DEFAULT 0 COMMENT '核算科目id' AFTER `level_code`;


-- 报销需要添加{"main":[],"meta":["ledger_account_id"]}
update workflow_node set can_edit_field='{"main":[],"meta":["ledger_account_id"]}' where flow_id in (39,40,41,42,44,57) and id in (446,485,504,534,556,733);


-- 普通付款需要找产品确认下，审批节点: 可编辑的字段: AP, APS 泰, APS 北 可编辑WHT类别/WHT税率，要不就这3个节点也都可以改，反正北京最后一个，以北京的为准
update workflow_node set can_edit_field = '{"amount_detail":["wht_category","wht_rate","ledger_account_id"]}' where flow_id in (38,51,52,53,54,55,56) and id in (429,627,646,666,683,698,713);

----增加权限
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (381, 'action.organization.job_manage.joblog.list', '列表查看', '职位体系-变更记录-列表查看', 380, 2, 1, '2021-06-03 12:33:39');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (380, 'menu.organization.job_manage.joblog', '变更记录', '职位体系-变更记录', 331, 1, 1, '2021-06-03 12:33:39');