-- 修改销售合同付款币种字段
alter table contract modify payment_currency tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '付款币种，1:泰铢，2:美元，3:人民币，4:比索';
-- 币种表
CREATE TABLE `currency` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_code` varchar(30) NOT NULL COMMENT '国家编码',
  `currency_symbol` varchar(50) NOT NULL COMMENT '货币编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
insert into currency(`id`,`country_code`,`currency_symbol`) values (1,'TH','THB'), (2,'US','USD'), (3,'CN','CNY'), (4,'PH','PHP');
-- 币种转换表
CREATE TABLE `setting_exchange_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `base_currency` int(11) NOT NULL COMMENT '基准币种数字代码',
  `format_currency` int(11) NOT NULL COMMENT '转换币种数字代码',
  `rate` float(10,5) NOT NULL COMMENT '汇率换算',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
-- 添加币种转换
insert into setting_exchange_rate(`id`,`base_currency`,`format_currency`,`rate`) values
(1,1,1,1),(2,1,2,31.423),(3,1,3,4.571),(4,1,4,0.630),
(5,4,1,1.526), (6,4,2,47.847), (7,4,3,7.411),(8,4,4,1);


ALTER TABLE sys_attachment ADD `sub_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '子类型 id';

-- 王洪伟v8382
CREATE TABLE `reimbursement_rel_loan` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `re_id` int(11) DEFAULT NULL COMMENT '报销主键id',
                                          `loan_id` int(11) DEFAULT NULL COMMENT '借款主键id',
                                          `amount` bigint(20) DEFAULT '0' COMMENT '借款抵扣金额',
                                          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          PRIMARY KEY (`id`),
                                          KEY `idx_rel_id_loan_id` (`re_id`,`loan_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='报销用的借款关联表';


ALTER TABLE `loan`
ADD COLUMN `is_used_by_re` tinyint(1) DEFAULT '0' COMMENT '是否被报销核销过，0没有，1有' AFTER `rejected_at`,
ADD COLUMN `re_amount` bigint(20) DEFAULT '0' COMMENT '报销抵扣金额' AFTER `is_used_by_re`,
ADD COLUMN `back_amount` bigint(20) DEFAULT '0' COMMENT '归还金额' AFTER `re_amount`,
ADD COLUMN `back_status` tinyint(2) NULL DEFAULT 1 COMMENT '还款状态-1未归还，2归还中，3已归还（需要与pay_status=2联查）' AFTER `back_amount`,
ADD COLUMN `back_mark` varchar(1000) NULL DEFAULT '' COMMENT '归还-备注' AFTER `back_status`,
ADD COLUMN `back_audit_status` tinyint(2) NULL DEFAULT 1 COMMENT '归还审核状态-1待审核，2拒绝（驳回），3同意，4撤回' AFTER `back_mark`,
ADD COLUMN `back_approved_at` datetime NULL COMMENT '归还通过时间' AFTER `back_audit_status`,
ADD COLUMN `back_rejected_at` datetime NULL COMMENT '归还拒绝时间' AFTER `back_approved_at`,
ADD COLUMN `back_refuse_reason` varchar(1000) NULL COMMENT '归还拒绝原因' AFTER `back_rejected_at`,
ADD COLUMN `back_cancel_reason` varchar(1000) NULL COMMENT '归还撤销原因' AFTER `back_refuse_reason`;
-- 对于已支付的借款全改成，已归还，归还金额是借款金额
update loan set back_amount=amount,back_status=3,back_audit_status=3,back_mark='auto' where pay_status=2 and `status`=3;
-- 把特定的几个，改成归还待审核，归还金额0
update loan set back_amount=0,back_status=1, back_audit_status=1, back_mark='' where pay_status=2 and `status`=3 and lno in ('202102100005','202103140001','202103170006','202103230002','202104020005','202104180002','202104240006','202105020001','202105020007','202105020008','202105030003','202105030004','202105040004','202105100001','202105150001');

