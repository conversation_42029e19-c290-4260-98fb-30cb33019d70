-- 王琦
-- 修改销售合同付款币种字段
alter table contract modify payment_currency tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '付款币种，1:泰铢，2:美元，3:人民币，4:比索';

# 币种表
CREATE TABLE `currency` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `country_code` varchar(30) NOT NULL COMMENT '国家编码',
  `currency_symbol` varchar(50) NOT NULL COMMENT '货币编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;

insert into currency(`id`,`country_code`,`currency_symbol`) values (1,'TH','THB'), (2,'US','USD'), (3,'CN','CNY'), (4,'PH','PHP');

# 币种转换表
CREATE TABLE `setting_exchange_rate` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `base_currency` int(11) NOT NULL COMMENT '基准币种数字代码',
  `format_currency` int(11) NOT NULL COMMENT '转换币种数字代码',
  `rate` float(10,5) NOT NULL COMMENT '汇率换算',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

# 添加币种转换
insert into setting_exchange_rate(`id`,`base_currency`,`format_currency`,`rate`) values
(1,1,1,1),(2,1,2,31.423),(3,1,3,4.571),(4,1,4,0.630),
(5,4,1,1.526), (6,4,2,47.847), (7,4,3,7.411),(8,4,4,1);

## 修改网点租房合同币种字段
alter table contract_store_renting modify money_symbol tinyint(4) DEFAULT '1' COMMENT '币种，1:泰铢，2:美元，3:人民币，4:比索';


-- 菲律宾单独执行：


## 审批流
insert into `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) values
(1,'采购合同审批流程',null,'采购合同审批流程',null),
(3,'采购合同审批流程_20200617',null,'采购合同审批流程_20200617',null),
(12,'借款申请审批流程_20200715',null,'借款申请审批流程_20200715',null),
(14,'借款申请审批流程_20200812',null,'借款申请审批流程_20200812',null),
(15,'业务付款-采购申请单_20200812',null,'业务付款-采购申请单_20200812',null),
(16,'业务付款-采购付款申请单_20200813',null,'业务付款-采购付款申请单_20200813',null),
(17,'报销',null,'报销',null),
(18,'报销（网点）',null,'报销（网点）',null),
(19,'租房合同',null,'租房合同',null),
(24,'合同审批流程-20201010',1,'合同审批流程-20201010','2020-10-20 12:12:44'),
(25,'薪酬审批流程-20201026',14,'薪酬审批流程-20201026','2020-10-31 13:40:59'),
(26,'报销网点（Network）',13,'报销网点_v7038',null),
(27,'报销网点（门店shop）',13,'报销网点_v7038',null),
(28,'报销网点（hub）',13,'报销网点_v7038',null),
(29,'报销总部',13,'报销总部_v7038',null),
(30,'采购付款申请单',9,'业务付款-采购付款申请单_v7038',null),
(31,'借款',8,'借款_v7038',null),
(32,'合同审批流程',1,'合同审批_v7038',null),
(33,'租房合同',1,'租房合同_v7038',null),
(34,'采购订单&采购付款申请单',11,'采购订单&采购付款申请单_v6870_v5',null),
(35,'网点租房付款审批流程 - 202012',29,'付款管理 - 网点租房付款审批流程',null),
(36,'报销部门(Flash Fullfillment、Flash Laos)',13,'报销部门(Flash Fullfillment、Flash Laos)',null),
(37,'采购订单&采购付款申请单（Flash Fullfillment、Flash Laos）',11,'采购订单&采购付款申请单（Flash Fullfillment、Flash Laos）',null),
(38,'普通付款',30,'普通付款',null),
(39,'报销 - 总部',13,'报销_总部_v2_7944_20210202',null),
(40,'报销 - 网点（Network）',13,'报销_网点_network_v2_7944_20210218',null),
(41,'报销 - 网点（门店Shop）',13,'报销_网点_门店shop_v2_7944_20210218',null),
(42,'报销 - 网点（HUB）',13,'报销_网点_HUB_v2_7944_20210218',null),
(43,'薪资发放审批',43,'薪资发放审批',null),
(44,'报销 - 部门（Flash Fullfillment、Flash Laos）',13,'报销_部门_v2_7944_20210219',null),
(45,'取数需求工单审批',50,'取数需求工单审批 - 取数工单系统 - 2021-03',null),
(46,'网点备用金申请审批',51,'网点备用金申请审批_8311_202103',null),
(47,'网点备用金归还审批-network部门',52,'网点备用金归还审批-network部门_8311_2021-03',null),
(48,'网点备用金归还审批-hub部门',52,'网点备用金归还审批-hub部门_8311_2021-03',null),
(49,'网点备用金归还审批-shop部门',52,'网点备用金归还审批-shop部门_8311_2021-03',null),
(50,'报价审批',53,'crm报价审批',null),
(51,'普通付款（Network）',30,'普通付款网点_v7038',null),
(52,'普通付款（门店shop）',30,'普通付款网点_v7038',null),
(53,'普通付款（hub）',30,'普通付款网点_v7038',null),
(54,'普通付款（FFM）',30,'普通付款（FFM）','2021-05-25 12:58:48'),
(55,'普通付款（Money,Laos,FCommerce）',30,'普通付款（Money,Laos,FCommerce）','2021-05-25 12:59:18'),
(56,'普通付款（Pay）',30,'普通付款（Pay）','2021-05-25 12:49:30'),
(57,'报销-针对税金及附加且明细是广告牌',13,'报销-针对税金及附加且明细是广告牌-v8756',null),
(58,'借款-归还',58,'借款-归还-v8382',null),
(59,'销售标准主合同定结',20,'销售合同-标准主合同-定结',null),
(60,'销售标准主合同现结',21,'销售合同-标准主合同-现结',null),
(61,'销售非标准主合同',22,'销售合同-非标准主合同',null),
(62,'销售附属合同授权内',23,'销售合同-附属合同-授权内',null),
(63,'销售附属合同授权外',24,'销售合同-附属合同-授权外',null),
(64,'合同审批流程(FFM)',1,'合同审批_FFM','2021-05-20 10:41:10'),
(65,'租房合同(FFM)',1,'租房合同_FFM','2021-05-20 10:41:14'),
(66,'网点租房付款审批流程(FFM) - 202105',29,'付款管理 - 网点租房付款审批流程(FFM)','2021-05-20 10:41:18');


## 审批节点
insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`) values
(1,1,'提交申请',1,0,null,3,10,'1',1,null,null,'',null),
(2,1,'检查合同金额',2,0,'ContractFlowService,parseCond',4,5,'1',1,null,null,'',null),
(3,1,'采购经理审批',3,0,null,2,10,'1',1,'37901',null,'',null),
(4,1,'采购总监审批',3,0,null,6,10,'1',1,'17152',null,'',null),
(5,1,'财务经理审批',3,0,null,7,10,'1',1,'20254',null,'',null),
(6,1,'财务总监审批',3,0,null,8,10,'1',1,'17152',null,'',null),
(7,1,'法务经理审批',3,0,null,11,10,'1',1,'28532',null,'',null),
(8,1,'法务总监审批',3,0,null,9,10,'1',1,'20258',null,'',null),
(9,1,'CEO审批',3,0,null,11,10,'1',1,'17008',null,'',null),
(10,1,'驳回',4,0,null,null,null,'1',1,null,null,'',null),
(11,1,'通过',5,0,null,null,null,'1',1,null,null,'',null),
(12,2,'总部普通员工',1,0,null,null,null,'2',1,null,null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(13,2,'直接上级',3,0,null,null,null,'2',3,null,null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(14,2,'部门经理',3,0,null,null,null,'2',4,null,null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(15,2,'指定工号20254',3,0,null,null,null,'2',1,'20254',null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(16,2,'指定工号17152',3,0,null,null,null,'2',1,'17152',null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(17,2,'指定工号17245',3,0,null,null,null,'2',1,'17245',null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(18,2,'指定工号17008',3,0,null,null,null,'2',1,'17008',null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(19,2,'结束',6,0,null,null,null,'2',1,null,null,'','{"type":"task","left":"26px","top":"161px","ico":"el-icon-user-solid"}'),
(20,3,'提交申请',1,0,null,21,27,'1',1,null,null,'',null),
(21,3,'法务总监',3,0,null,22,27,'1',1,'51286',null,'',null),
(22,3,'采购总监',3,0,null,23,27,'1',1,'52731',null,'',null),
(23,3,'检查合同金额',2,0,'App\\Modules\\Contract\\Services\\ContractFlowService,parseCond',25,24,'1',null,null,null,'',null),
(24,3,'财务经理',3,0,null,28,27,'1',1,'20254',null,'',null),
(25,3,'财务总监',3,0,null,26,27,'1',1,'17152',null,'',null),
(26,3,'CEO',3,0,null,28,27,'1',1,'17008',null,'',null),
(27,3,'驳回',4,0,null,null,null,'1',null,null,null,'',null),
(28,3,'通过',5,0,null,null,null,'1',null,null,null,'',null),
(52,12,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(53,12,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(54,12,'部门经理',3,0,null,null,null,null,4,null,null,'',null),
(55,12,'指定工号20254',3,0,null,null,null,null,1,'20254',null,'',null),
(56,12,'指定工号17152',3,0,null,null,null,null,1,'17152',null,'',null),
(57,12,'指定工号17245',3,0,null,null,null,null,1,'17245',null,'',null),
(58,12,'指定工号55356',3,0,null,null,null,null,1,'17152',null,'',null),
(59,12,'指定工号17008',3,0,null,null,null,null,1,'17008',null,'',null),
(60,12,'结束',6,0,null,null,null,null,1,null,null,'',null),
(64,14,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(65,14,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(66,14,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(67,14,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(68,14,'指定工号17245（COO）或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(69,14,'指定工号20254（Fianace Mangager）',3,0,null,null,null,null,1,'20254',null,'',null),
(70,14,'指定工号17152（Finace Director）',3,0,null,null,null,null,1,'17152',null,'',null),
(71,14,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(72,14,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(73,14,'结束',6,0,null,null,null,null,1,null,null,'',null),
(74,15,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(75,15,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(76,15,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(77,15,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(78,15,'指定工号17245（COO）或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(79,15,'指定工号41081,46774(PM)',3,0,null,null,null,'3',1,'41081',null,'',null),
(80,15,'指定工号52731（PSM）',3,0,null,null,null,null,1,'52731',null,'',null),
(81,15,'指定工号17152（PD）',3,0,null,null,null,null,1,'17152',null,'',null),
(82,15,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(83,15,'结束',6,0,null,null,null,null,1,null,null,'',null),
(84,16,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(85,16,'指定工号41081,46774（PM）',3,0,null,null,null,null,1,'41081,46774',null,'',null),
(86,16,'指定工号52731（PSM）',3,0,null,null,null,null,1,'52731',null,'',null),
(87,16,'指定工号（AP）',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(88,16,'指定工号33306,17178（APS）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(89,16,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(90,16,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(91,16,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(92,16,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(93,16,'结束',6,0,null,null,null,null,1,null,null,'',null),
(94,17,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(95,17,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(96,17,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(97,17,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(98,17,'指定工号17245（COO）或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(99,17,'指定工号（AP）',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(100,17,'指定工号33306,17178（APS）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(101,17,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(102,17,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(103,17,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(104,17,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(105,17,'结束',6,0,null,null,null,null,1,null,null,'',null),
(106,18,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(107,18,'网点主管',3,0,null,null,null,null,8,null,null,'',null),
(108,18,'Supervisor(网点LEVEL1)',3,0,null,null,null,null,9,null,null,'',null),
(109,18,'Manager(网点Level2)',3,0,null,null,null,'',10,null,null,'',null),
(110,18,'Senior Manager(Level3)',3,0,null,null,null,null,11,null,null,'',null),
(111,18,'Director(Level4)',3,0,null,null,null,null,12,null,null,'',null),
(112,18,'指定工号17245（COO）或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(113,18,'指定工号(AP)',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(114,18,'指定工号33306&17178（APS）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(115,18,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(116,18,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(117,18,'结束',6,0,null,null,null,null,1,null,null,'',null),
(118,19,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(119,19,'语言总审核',3,0,null,null,null,null,1,'57335',null,'',null),
(120,19,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(121,19,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(123,19,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(124,19,'coo/cpo',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(125,19,'中文审批',3,0,null,null,null,null,1,'57335',null,'',null),
(126,19,'英文审批',3,0,null,null,null,null,1,'56883',null,'',null),
(127,19,'泰语审批',3,0,null,null,null,null,1,'28532',null,'',null),
(128,19,'中文、英文审批',3,0,null,null,null,null,1,'57335',null,'',null),
(129,19,'泰语审批',3,0,null,null,null,null,1,'58040',null,'',null),
(130,19,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(131,19,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,57625,62721,62306,66553',null,'',null),
(132,19,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(133,19,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(134,19,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(135,19,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(136,19,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(137,19,'结束',6,0,null,null,null,null,1,null,null,'',null),
(138,20,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(139,20,'指定工号41081,46774（PM）',3,0,null,null,null,'3',1,'41081',null,'',null),
(140,20,'指定工号52731（PSM）',3,0,null,null,null,null,1,'52731',null,'',null),
(141,20,'指定工号（AP菲律宾）',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(142,20,'指定工号33306,17178（APS菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(143,20,'指定工号55849,48480,57625,57626,57624,54678（APS北京',3,0,null,null,null,null,1,'55849,57625,62721,62306,66553',null,'',null),
(144,20,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(145,20,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(146,20,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(147,20,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(148,20,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(149,20,'结束',6,0,null,null,null,null,1,null,null,'',null),
(150,21,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(151,21,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(152,21,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(153,21,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(154,21,'指定工号17245（COO）或56780（CPO）',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(155,21,'指定工号（AP菲律宾）',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(156,21,'指定工号33306,17178（APS菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(157,21,'指定工号（APS北京）',3,0,null,null,null,null,1,'55849,57625,62721,62306,66553',null,'',null),
(158,21,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(159,21,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(160,21,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(161,21,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(162,21,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(163,21,'结束',6,0,null,null,null,null,1,null,null,'',null),
(164,22,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(165,22,'网点主管',3,0,null,null,null,null,8,null,null,'',null),
(166,22,'Supervisor(网点LEVEL1)',3,0,null,null,null,null,9,null,null,'',null),
(167,22,'Manager(网点Level2)',3,0,null,null,null,'',10,null,null,'',null),
(168,22,'Senior Manager(Level3)',3,0,null,null,null,null,11,null,null,'',null),
(169,22,'Director(Level4)',3,0,null,null,null,null,12,null,null,'',null),
(170,22,'指定工号(AP菲律宾)',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739',null,'',null),
(171,22,'指定工号33306,17178（APS菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(172,22,'指定工号55849,48480,57625,57626,57624,54678（APS北京',3,0,null,null,null,null,1,'55849,57625,62721,62306,66553',null,'',null),
(173,22,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(174,22,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(175,22,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(176,22,'结束',6,0,null,null,null,null,1,null,null,'',null),
(177,23,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(178,23,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(179,23,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(180,23,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(181,23,'指定工号17245（COO）或56780(CPO)',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(182,23,'指定工号20254（Fianace Mangager）',3,0,null,null,null,null,1,'20254',null,'',null),
(183,23,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(184,23,'指定工号17152（Finace Director）',3,0,null,null,null,null,1,'17152',null,'',null),
(185,23,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(186,23,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(187,23,'结束',6,0,null,null,null,null,1,null,null,'',null),
(188,24,'员工提交',1,0,null,null,null,null,1,null,'2020-10-20 12:12:50','',null),
(189,24,'直接上级',3,0,null,null,null,null,3,null,'2020-10-20 12:12:51','',null),
(190,24,'二级部门负责人',3,0,null,null,null,null,5,null,'2020-10-20 12:12:52','',null),
(191,24,'一级部门负责人',3,0,null,null,null,null,6,null,'2020-10-20 12:12:52','',null),
(192,24,'coo/cpo',3,0,null,null,null,null,7,'17245,56780','2020-10-20 12:12:53','',null),
(193,24,'28532审批',3,0,null,null,null,null,1,'28532','2020-10-20 12:12:54','',null),
(194,24,'58040审批',3,0,null,null,null,null,1,'58040','2020-10-20 12:12:58','',null),
(195,24,'56883审批',3,0,null,null,null,null,1,'56883','2020-10-20 12:12:58','',null),
(196,24,'57335审批',3,0,null,null,null,null,1,'57335','2020-10-20 12:12:59','',null),
(198,24,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178','2020-10-20 12:13:00','',null),
(199,24,'AP Supervisor(北京)',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080','2020-10-20 12:13:00','',null),
(200,24,'Finance Manager',3,0,null,null,null,null,1,'20254','2020-10-20 12:13:01','',null),
(201,24,'Finance Senior Manager',3,0,null,null,null,null,1,'54677','2020-10-20 12:13:02','',null),
(202,24,'Finance Director',3,0,null,null,null,null,1,'17152','2020-10-20 12:13:03','',null),
(203,24,'CFO',3,0,null,null,null,null,1,'17152','2020-10-20 12:13:03','',null),
(204,24,'CEO',3,0,null,null,null,null,1,'17008','2020-10-20 12:13:04','',null),
(205,24,'结束',6,0,null,null,null,null,1,null,'2020-10-20 12:13:04','',null),
(222,25,'开始',1,0,null,null,null,'1',1,'','2020-10-31 13:41:01','',null),
(223,25,'直接上级',3,0,null,null,null,'1',3,'','2020-10-31 13:41:02','',null),
(224,25,'CPO',3,0,null,null,null,'1',1,'56780','2020-10-31 13:41:03','',null),
(225,25,'AP(菲律宾)',3,0,null,null,null,'1',1,'54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,32739','2020-10-31 13:41:04','{"wages":["wht_category","wht_tax_rate","wht_amount","actually_amount"]}',null),
(226,25,'AP Supervisor(菲律宾)',3,0,null,null,null,'1',1,'33306,17178','2020-10-31 13:41:06','{"wages":["wht_category","wht_tax_rate","wht_amount","actually_amount"]}',null),
(227,25,'AP Supervisor(北京)',3,0,null,null,null,'1',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080','2020-10-31 13:41:07','{"wages":["wht_category","wht_tax_rate","wht_amount","actually_amount"]}',null),
(228,25,'Finance Manager',3,0,null,null,null,'1',1,'20254','2020-10-31 13:41:09','',null),
(229,25,'Finance Senior Manager',3,0,null,null,null,'1',1,'54677','2020-10-31 13:41:10','',null),
(230,25,'Finance Director',3,0,null,null,null,'1',1,'17152','2020-10-31 13:41:11','',null),
(231,25,'CFO',3,0,null,null,null,'1',1,'17152','2020-10-31 13:41:12','',null),
(232,25,'CEO',3,0,null,null,null,'1',1,'17008','2020-10-31 13:41:14','',null),
(233,25,'结束',6,0,null,null,null,'1',1,null,'2020-10-31 13:41:15','',null),
(234,26,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(235,26,'网点主管',3,0,null,null,null,null,13,null,null,'',null),
(236,26,'业务线Manager',3,0,null,null,null,null,14,null,null,'',null),
(237,26,'业务线Senior Manager',3,0,null,null,null,null,15,null,null,'',null),
(238,26,'职能线Supervisor',3,0,null,null,null,null,1,'38671,38668,24901,60758',null,'',null),
(239,26,'职能线manager',3,0,null,null,null,null,1,'30344',null,'',null),
(240,26,'指定工号28916',3,0,null,null,null,null,1,'28916',null,'',null),
(241,26,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(242,26,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(243,26,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(244,26,'AP(菲律宾)',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(245,26,'APS(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(246,26,'APS(北京)',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(247,26,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(248,26,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(249,26,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(250,26,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(251,26,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(252,26,'结束',6,0,null,null,null,null,1,null,null,'',null),
(253,27,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(254,27,'网点主管',3,0,null,null,null,null,8,null,null,'',null),
(255,27,'Senior Manager(Area manager)',3,0,null,null,null,null,17,null,null,'',null),
(256,27,'Supervisor',3,0,null,null,null,null,1,'26478,30605,34118,20114,25481',null,'',null),
(257,27,'Manager',3,0,null,null,null,null,1,'19878',null,'',null),
(258,27,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(259,27,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(260,27,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(261,27,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(262,27,'AP(菲律宾)',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(263,27,'APS(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(264,27,'APS(北京)',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(265,27,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(266,27,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(267,27,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(268,27,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(269,27,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(270,27,'结束',6,0,null,null,null,null,1,null,null,'',null),
(271,28,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(272,28,'直线上级',3,0,null,null,null,null,3,null,null,'',null),
(273,28,'网点主管',3,0,null,null,null,null,8,null,null,'',null),
(274,28,'Supervisor',3,0,null,null,null,null,1,'19685',null,'',null),
(275,28,'manager',3,0,null,null,null,null,1,'40450',null,'',null),
(276,28,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(277,28,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(278,28,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(279,28,'HRBP',3,0,null,null,null,null,1,'39462',null,'',null),
(280,28,'AP(菲律宾)',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(281,28,'APS(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(282,28,'APS(北京)',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(283,28,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(284,28,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(285,28,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(286,28,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(287,28,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(288,28,'结束',6,0,null,null,null,null,1,null,null,'',null),
(290,29,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(291,29,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(292,29,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(293,29,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(294,29,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(295,29,'指定工号17245（COO）或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(296,29,'HRBP',3,0,null,null,null,null,1,'39462',null,'',null),
(297,29,'指定工号（AP菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(298,29,'指定工号33306,17178（APS菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(299,29,'指定工号（APS北京）',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(300,29,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(301,29,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(302,29,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(303,29,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(304,29,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(305,29,'结束',6,0,null,null,null,null,1,null,null,'',null),
(306,30,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(307,30,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(308,30,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(309,30,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(310,30,'公司所属负责人',3,0,null,null,null,null,16,null,null,'',null),
(311,30,'指定工号17245 (COO)或CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(312,30,'指定工号41081(PM)',3,0,null,null,null,'3',1,'41081,60879',null,'',null),
(313,30,'指定工号52731（PSM）',3,0,null,null,null,null,1,'52731',null,'',null),
(314,30,'指定工号17152（PD）',3,0,null,null,null,null,1,'17152',null,'',null),
(315,30,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(316,30,'结束',6,0,null,null,null,null,1,null,null,'',null),
(317,31,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(318,31,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(319,31,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(320,31,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(321,31,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(322,31,'指定工号17245（COO）或56780(CPO)',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(323,31,'指定工号20254（Fianace Mangager）',3,0,null,null,null,null,1,'20254',null,'',null),
(324,31,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(325,31,'指定工号17152（Finace Director）',3,0,null,null,null,null,1,'17152',null,'',null),
(326,31,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(327,31,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(328,31,'结束',6,0,null,null,null,null,1,null,null,'',null),
(329,32,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(330,32,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(331,32,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(332,32,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(333,32,'所属部门负责人',3,0,null,null,null,null,16,null,null,'',null),
(334,32,'coo/cpo',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(335,32,'58040审批 (legal1泰文，legal2相同跳过。审批完到57335legal3)',3,0,null,null,null,null,1,'58040,69767',null,'',null),
(336,32,'51286审批 (没了)',3,0,null,null,null,null,1,'51286',null,'',null),
(337,32,'56883审批（legal1英文，审批完到57335）',3,0,null,null,null,null,1,'56883,69767',null,'',null),
(338,32,'57335审批（根据跳过逻辑，57335审批完到APS就行）',3,0,null,null,null,null,1,'57335',null,'',null),
(339,32,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(340,32,'AP Supervisor(北京)',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(341,32,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(342,32,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(343,32,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(344,32,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(345,32,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(346,32,'结束',6,0,null,null,null,null,1,null,null,'',null),

(367,34,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(368,34,'指定工号41081（PM）',3,0,null,null,null,'3',1,'41081,60879',null,'',null),
(369,34,'指定工号52731（PSM）',3,0,null,null,null,null,1,'52731',null,'',null),
(370,34,'指定工号（AP泰国）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'{"main":["wht_amount","real_amount","is_tax"],"meta":["wht_type","wht_ratio","wht_amount","real_amount","category_a","category_b","finance_code"]}',null),
(371,34,'指定工号33306,17178（APS泰国）',3,0,null,null,null,null,1,'33306,17178',null,'{"main":["wht_amount","real_amount","is_tax"],"meta":["wht_type","wht_ratio","wht_amount","real_amount","category_a","category_b","finance_code"]}',null),
(372,34,'指定工号55849,48480,57625,57626,57624（APS北京)',3,0,null,null,null,'4',1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'{"main":["wht_amount","real_amount","is_tax"],"meta":["wht_type","wht_ratio","wht_amount","real_amount","category_a","category_b","finance_code"]}',null),
(373,34,'指定工号20254（FM）',3,0,null,null,null,null,1,'20254',null,'',null),
(374,34,'指定工号54677（FSM）',3,0,null,null,null,null,1,'54677',null,'',null),
(375,34,'指定工号17152（FD）',3,0,null,null,null,null,1,'17152',null,'',null),
(376,34,'指定工号55356（CFO）',3,0,null,null,null,null,1,'17152',null,'',null),
(377,34,'指定工号17008（CEO）',3,0,null,null,null,null,1,'17008',null,'',null),
(378,34,'结束',6,0,null,null,null,null,1,null,null,'',null),

(421, 38, '总部普通员工', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(422, 38, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL),
(423, 38, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, NULL, NULL, '', NULL),
(424, 38, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL),
(425, 38, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(426, 38, 'COO/CPO', 3, 0, NULL, NULL, NULL, NULL, 7, '', NULL, '', NULL),
(427, 38, 'AP（菲律宾）', 3, 0, NULL, NULL, NULL, '5', 1, '', NULL, '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}', NULL),
(428, 38, 'AP Supervisor（泰国）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}', NULL),
(429, 38, 'AP Supervisor（北京）', 3, 0, NULL, NULL, NULL, '', 1, '', NULL, '{\"amount_detail\":[\"wht_category\",\"wht_rate\",\"ledger_account_id\"]}', NULL),
(430, 38, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(431, 38, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(432, 38, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(433, 38, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(434, 38, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(435, 38, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),

(436,39,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(437,39,'直线上级',3,0,null,null,null,null,3,null,null,'',null),
(438,39,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(439,39,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(440,39,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(441,39,'所属组织负责人',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(442,39,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(443,39,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(444,39,'AP（菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(445,39,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(446,39,'AP Supervisor（北京）',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(447,39,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(448,39,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(449,39,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(450,39,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(451,39,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(452,39,'结束',6,0,null,null,null,null,1,null,null,'',null),
(473,41,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(474,41,'网点主管 - MS-网点管理负责人',3,0,null,null,null,null,8,null,null,'',null),
(475,41,'Senior Manager（area Manager）',3,0,null,null,null,null,17,null,null,'',null),
(476,41,'Supervisor',3,0,null,null,null,null,1,'26478',null,'',null),
(477,41,'Manager',3,0,null,null,null,null,1,'19878',null,'',null),
(478,41,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(479,41,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(480,41,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(481,41,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(482,41,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(483,41,'AP （菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(484,41,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(485,41,'AP Supervisor（北京）',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(486,41,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(487,41,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(488,41,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(489,41,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(490,41,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(491,41,'结束',6,0,null,null,null,null,1,null,null,'',null),
(492,42,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(493,42,'直线上级',3,0,null,null,null,null,3,null,null,'',null),
(494,42,'网点主管 - MS-网点管理-负责人',3,0,null,null,null,null,8,null,null,'',null),
(495,42,'Supervisor - 支持部门',3,0,null,null,null,null,1,'19685',null,'',null),
(496,42,'Manager - 支持部门',3,0,null,null,null,null,1,'40450',null,'',null),
(497,42,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(498,42,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(499,42,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(500,42,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(501,42,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(502,42,'AP（菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(503,42,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(504,42,'AP Supervisor（北京）',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(505,42,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(506,42,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(507,42,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(508,42,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(509,42,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(510,42,'结束',6,0,null,null,null,null,1,null,null,'',null),
(511,43,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(512,43,'指定工号(32122)',3,0,null,null,null,null,1,'32122',null,'',null),
(513,43,'CPO(56780)',3,0,null,null,null,null,1,'56780',null,'',null),
(514,43,'CEO(17008)',3,0,null,null,null,null,1,'17008',null,'',null),
(515,43,'AP(菲律宾)',3,0,null,null,null,null,1,'54255,54249,33306,28989,26808,24905,24904,24902,23116,21958,19432,17348,17178,32739',null,'',null),
(516,43,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'17178,33306',null,'',null),
(517,43,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,57625,62721,62306,66553',null,'',null),
(518,43,'Finance Manager  20254',3,0,null,null,null,null,1,'20254',null,'',null),
(519,43,'Finance Senior Manager  54677',3,0,null,null,null,null,1,'54677',null,'',null),
(520,43,'Finance Director   17152',3,0,null,null,null,null,1,'17152',null,'',null),
(523,43,'结束节点',6,0,null,null,null,null,1,null,null,'',null),
(524,44,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(525,44,'直线上级',3,0,null,null,null,null,3,null,null,'',null),
(526,44,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(527,44,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(528,44,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(529,44,'组织负责人',3,0,null,null,null,null,7,null,null,'',null),
(530,44,'HRBP Director',3,0,null,null,null,null,1,'62965',null,'',null),
(531,44,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(532,44,'AP（菲律宾）',3,0,null,null,null,'5',1,'28989',null,'',null),
(533,44,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'31588',null,'',null),
(534,44,'AP Supervisor（北京）',3,0,null,null,null,null,1,'65230',null,'',null),
(535,44,'Finance Manager',3,0,null,null,null,null,1,'33306',null,'',null),
(536,44,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(537,44,'Finance Director',3,0,null,null,null,null,1,'20254',null,'',null),
(538,44,'AVP/CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(539,44,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(540,44,'结束',6,0,null,null,null,null,1,null,null,'',null),
(542,40,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(543,40,'网点主管([16]branch supervisor) - 业务线',3,0,null,null,null,null,13,null,null,'',null),
(544,40,'Manager（District manager）- 业务线',3,0,null,null,null,null,14,null,null,'',null),
(545,40,'Senior Manager（Reginoal manager）- 业务线',3,0,null,null,null,null,15,null,null,'',null),
(546,40,'Supervisor - 职能线 - 网络支持部',3,0,null,null,null,null,1,'38671,38668,24901,60758',null,'',null),
(547,40,'Supervisor_2_(外协) - 职能线 - 网络支持部',3,0,null,null,null,null,1,'29984,31783,51019',null,'',null),
(548,40,'TSM-（福利、团结、其他）',3,0,null,null,null,null,1,'19616',null,'',null),
(549,40,'ATD-（福利、团建、其他）',3,0,null,null,null,null,1,'31849',null,'',null),
(550,40,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(551,40,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(552,40,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(553,40,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(554,40,'AP（菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(555,40,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(556,40,'AP Supervisor（北京）',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(557,40,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(558,40,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(559,40,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(560,40,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(561,40,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(562,40,'结束',6,0,null,null,null,null,1,null,null,'',null),
(563,45,'开始',1,0,null,null,null,null,1,null,null,'',null),
(564,45,'需求部门负责人（取数工单系统）',3,0,null,null,null,null,18,'',null,'',null),
(565,45,'数据部门负责人（取数工单系统）',3,0,null,null,null,null,19,'',null,'',null),
(566,45,'相关部门负责人（财务|HR|COO - 取数工单系统）',3,1,null,null,null,null,20,null,null,'',null),
(567,45,'结束',6,0,null,null,null,null,1,null,null,'',null),
(568,46,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(569,46,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(570,46,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(571,46,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(572,46,'指定工号（Finance Manager 20254）',3,0,null,null,null,null,1,'20254',null,'',null),
(573,46,'指定工号（Finance Senior Manager 54677）',3,0,null,null,null,null,1,'54677',null,'',null),
(574,46,'指定工号（Finance Director 17152）',3,0,null,null,null,null,1,'17152',null,'',null),
(575,46,'结束',6,0,null,null,null,null,1,null,null,'',null),
(576,47,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(577,47,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(578,47,'指定工号 (Net Work Support组 24901)',3,0,null,null,null,null,1,'24901,22490',null,'',null),
(579,47,'指定工号（Finance Manager 20254）',3,0,null,null,null,null,1,'20254',null,'',null),
(580,47,'指定工号（Finance Senior Manager 54677）',3,0,null,null,null,null,1,'54677',null,'',null),
(581,47,'指定工号（Finance Director 17152）',3,0,null,null,null,null,1,'17152',null,'',null),
(582,47,'Account Payable Officer（出纳） ',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,58292,58294,60277',null,'',null),
(583,47,'结束',6,0,null,null,null,null,1,null,null,'',null),
(584,48,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(585,48,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(586,48,'指定工号 (Hub Director（30286）)',3,0,null,null,null,null,1,'30286',null,'',null),
(587,48,'指定工号（Finance Manager 20254）',3,0,null,null,null,null,1,'20254',null,'',null),
(588,48,'指定工号（Finance Senior Manager 54677）',3,0,null,null,null,null,1,'54677',null,'',null),
(589,48,'指定工号（Finance Director 17152）',3,0,null,null,null,null,1,'17152',null,'',null),
(590,48,'Account Payable Officer（出纳） ',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,58292,58294,60277',null,'',null),
(591,48,'结束',6,0,null,null,null,null,1,null,null,'',null),
(592,49,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(593,49,'Shop Area Manager',3,0,null,null,null,null,14,null,null,'',null),
(594,49,'指定工号 (Shop operations Manager(20467))',3,0,null,null,null,null,1,'20467',null,'',null),
(595,49,'指定工号 (Assistant Shop MD 17574)',3,0,null,null,null,null,1,'17574',null,'',null),
(596,49,'指定工号（Finance Manager 20254）',3,0,null,null,null,null,1,'20254',null,'',null),
(597,49,'指定工号（Finance Senior Manager 54677）',3,0,null,null,null,null,1,'54677',null,'',null),
(598,49,'指定工号（Finance Director 17152）',3,0,null,null,null,null,1,'17152',null,'',null),
(599,49,'Account Payable Officer（出纳）',3,0,null,null,null,null,1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,58292,58294,60277',null,'',null),
(600,49,'结束',6,0,null,null,null,null,1,null,null,'',null),
(601,40,'TSM-（外协）',3,0,null,null,null,null,1,'30344',null,'',null),
(602,40,'ATD-（外协）',3,0,null,null,null,null,1,'28916',null,'',null),

(603, 51, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(604, 51, '网点主管([16]branch supervisor) - 业务线', 3, 0, NULL, NULL, NULL, NULL, 13, NULL, NULL, '', NULL),
(605, 51, 'Manager（District manager）- 业务线', 3, 0, NULL, NULL, NULL, NULL, 14, NULL, NULL, '', NULL),
(606, 51, 'Senior Manager（Reginoal manager）- 业务线', 3, 0, NULL, NULL, NULL, NULL, 15, NULL, NULL, '', NULL),
(607, 51, 'Supervisor - 职能线 - 网络支持部', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(608, 51, 'Supervisor_2_(外协) - 职能线 - 网络支持部', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(609, 51, 'TSM-（福利、团结、其他）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(610, 51, 'ATD-（福利、团建、其他）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(611, 51, 'TSM-（外协）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(612, 51, 'ATD-（外协）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(613, 51, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(614, 51, 'COO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(615, 51, 'HRBP Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(616, 51, 'CPO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(617, 51, 'AP（菲律宾）', 3, 0, NULL, NULL, NULL, '5', 1, '', NULL, '', NULL),
(618, 51, 'AP Supervisor（菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(619, 51, 'AP Supervisor（北京）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(620, 51, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(621, 51, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(622, 51, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(623, 51, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(624, 51, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(625, 51, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(626, 52, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(627, 52, '网点主管 - MS-网点管理负责人', 3, 0, NULL, NULL, NULL, NULL, 8, NULL, NULL, '', NULL),
(628, 52, 'Senior Manager（area Manager）', 3, 0, NULL, NULL, NULL, NULL, 17, NULL, NULL, '', NULL),
(629, 52, 'Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(630, 52, 'Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(631, 52, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL),
(632, 52, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(633, 52, 'COO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(634, 52, 'HRBP Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(635, 52, 'CPO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(636, 52, 'AP （菲律宾）', 3, 0, NULL, NULL, NULL, '5', 1, '', NULL, '', NULL),
(637, 52, 'AP Supervisor（菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(638, 52, 'AP Supervisor（北京）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(639, 52, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(640, 52, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(641, 52, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(642, 52, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(643, 52, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(644, 52, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(645, 53, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(646, 53, '直线上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL),
(647, 53, '网点主管 - MS-网点管理-负责人', 3, 0, NULL, NULL, NULL, NULL, 8, NULL, NULL, '', NULL),
(648, 53, 'Supervisor - 支持部门', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(649, 53, 'Manager - 支持部门', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(650, 53, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, NULL, NULL, '', NULL),
(651, 53, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL),
(652, 53, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(653, 53, 'COO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(654, 53, 'HRBP Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(655, 53, 'CPO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(656, 53, 'AP（菲律宾）', 3, 0, NULL, NULL, NULL, '5', 1, '', NULL, '', NULL),
(657, 53, 'AP Supervisor（菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(658, 53, 'AP Supervisor（北京）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(659, 53, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(660, 53, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(661, 53, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(662, 53, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(663, 53, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(664, 53, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(670,53,'CFO',3,0,null,null,null,null,1,'',null,'',null),
(671,53,'CEO',3,0,null,null,null,null,1,'',null,'',null),
(672,53,'结束',6,0,null,null,null,null,1,null,null,'',null),
(673,53, '区域经理', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),

(675,54,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(676,54,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(677,54,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(678,54,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(679,54,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(680,54,'COO/CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(681,54,'AP（菲律宾）',3,0,null,null,null,'5',1,'34102,31588',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(682,54,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'60277',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(683,54,'AP Supervisor（北京）',3,0,null,null,null,'4',1,'65230',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(684,54,'Finance Manager',3,0,null,null,null,null,1,'33306,28989',null,'',null),
(685,54,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(686,54,'Finance Director',3,0,null,null,null,null,1,'20254',null,'',null),
(687,54,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(688,54,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(689,54,'结束',6,0,null,null,null,null,1,null,null,'',null),
(690,55,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(691,55,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(692,55,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(693,55,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(694,55,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(695,55,'COO/CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(696,55,'AP（菲律宾）',3,0,null,null,null,'5',1,'60277',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(697,55,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'60277',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(698,55,'AP Supervisor（北京）',3,0,null,null,null,'4',1,'65230',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(699,55,'Finance Manager',3,0,null,null,null,null,1,'33306,28989',null,'',null),
(700,55,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(701,55,'Finance Director',3,0,null,null,null,null,1,'20254',null,'',null),
(702,55,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(703,55,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(704,55,'结束',6,0,null,null,null,null,1,null,null,'',null),
(705,56,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(706,56,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(707,56,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(708,56,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(709,56,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(710,56,'COO/CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(711,56,'AP（菲律宾）',3,0,null,null,null,'5',1,'62265',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(712,56,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'60277',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(713,56,'AP Supervisor（北京）',3,0,null,null,null,'4',1,'65230',null,'{"amount_detail":["wht_category","wht_rate"]}',null),
(714,56,'Finance Manager',3,0,null,null,null,null,1,'33306,28989',null,'',null),
(715,56,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(716,56,'Finance Director',3,0,null,null,null,null,1,'20254',null,'',null),
(717,56,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(718,56,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(719,56,'结束',6,0,null,null,null,null,1,null,null,'',null),
(720,30,'指定工号（APS北京)',3,0,null,null,null,'4',1,'55849,57625,61682,62306,62358,62721,64234,64377,66553,66979,69101,70080',null,'{"main":[],"meta":["ledger_account_id"]}',null),
(721,57,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(722,57,'网点主管 - MS-网点管理负责人',3,0,null,null,null,null,8,null,null,'',null),
(723,57,'Senior Manager（area Manager）',3,0,null,null,null,null,17,null,null,'',null),
(724,57,'Supervisor',3,0,null,null,null,null,1,'20114,34118',null,'',null),
(725,57,'Manager',3,0,null,null,null,null,1,'25481',null,'',null),
(726,57,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(727,57,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(728,57,'COO',3,0,null,null,null,null,1,'17245',null,'',null),
(729,57,'HRBP Director',3,0,null,null,null,null,1,'39462',null,'',null),
(730,57,'CPO',3,0,null,null,null,null,1,'56780',null,'',null),
(731,57,'AP （菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'',null),
(732,57,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(733,57,'AP Supervisor（北京）',3,0,null,null,null,null,1,'64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,69101,70080',null,'',null),
(734,57,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(735,57,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(736,57,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(737,57,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(738,57,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(739,57,'结束',6,0,null,null,null,null,1,null,null,'',null),
(801,50,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(802,50,'sales部门 sales manager职位',3,0,null,null,null,null,21,'83',null,'',null),
(803,50,'sales部门 sales director角色',3,0,null,null,null,null,1,'33288',null,'',null),
(804,50,'FEX GM',3,0,null,null,null,null,1,'21848',null,'',null),
(805,50,'pmd部门 直线上级',3,0,null,null,null,null,3,null,null,'',null),
(806,50,'pmd部门 pmd总经理',3,0,null,null,null,null,1,'28602',null,'',null),
(807,50,'shop部门 area manager',3,0,null,null,null,null,17,null,null,'',null),
(808,50,'shop部门 shop operation manager 职位',3,0,null,null,null,null,1,'20467',null,'',null),
(809,50,'shop部门 assistant shop management director 职位',3,0,null,null,null,null,1,'17574',null,'',null),
(810,50,'network部门 user operation manager 职位',3,0,null,null,null,null,1,'21318',null,'',null),
(811,50,'network部门 network director 角色',3,0,null,null,null,null,1,'31849',null,'',null),
(812,50,'结束',6,0,null,null,null,null,null,null,null,'',null),
(813,44,'固定工号 70782',3,0,null,null,null,null,1,'70782',null,'',null),
(814,58,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(815,58,'Finance Manager(指定工号20254)',3,0,null,null,null,null,1,'20254',null,'',null),
(816,58,'Finance Senior Manager',3,0,null,null,null,null,1,'54677',null,'',null),
(817,58,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(818,58,'Account Payable Officer(出纳)',3,0,null,null,null,null,1,'32739,58292',null,'',null),
(819,58,'结束',6,0,null,null,null,null,null,null,null,'',null),
(820,50,'sales部门 直线上级',3,0,null,null,null,null,3,null,null,'',null),
(821,59,'员工提交',1,0,null,null,null,null,1,'',null,'',null),
(822,59,'部门合规审批',3,0,null,null,null,null,1,'33477',null,'',null),
(823,59,'部门商务',3,0,null,null,null,null,1,'35387',null,'',null),
(824,59,'法务',3,0,null,null,null,null,1,'57335',null,'',null),
(825,59,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(826,59,'一级部门负责人',3,0,null,null,null,null,6,'',null,'',null),
(827,59,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,48480,57625,57626,57624,54678,61499,61682',null,'',null),
(828,59,'所属公司负责人',3,0,null,null,null,null,16,'',null,'',null),
(829,59,'结束',6,0,null,null,null,null,1,'',null,'',null),
(830,60,'员工提交',1,0,null,null,null,null,1,'',null,'',null),
(831,60,'部门商务',3,0,null,null,null,null,1,'35387',null,'',null),
(832,60,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(833,60,'一级部门负责人',3,0,null,null,null,null,6,'',null,'',null),
(834,60,'结束',6,0,null,null,null,null,1,'',null,'',null),
(835,61,'员工提交',1,0,null,null,null,null,1,'',null,'',null),
(836,61,'部门合规审批',3,0,null,null,null,null,1,'33477',null,'',null),
(837,61,'部门商务',3,0,null,null,null,null,1,'35387',null,'',null),
(838,61,'法务',3,0,null,null,null,null,1,'57335',null,'',null),
(839,61,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(840,61,'一级部门负责人',3,0,null,null,null,null,6,'',null,'',null),
(841,61,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,48480,57625,57626,57624,54678,61499,61682',null,'',null),
(842,61,'所属公司负责人',3,0,null,null,null,null,16,'',null,'',null),
(843,61,'结束',6,0,null,null,null,null,1,'',null,'',null),
(844,62,'员工提交',1,0,null,null,null,null,1,'',null,'',null),
(845,62,'部门合规审批',3,0,null,null,null,null,1,'33477',null,'',null),
(846,62,'部门商务',3,0,null,null,null,null,1,'35387',null,'',null),
(847,62,'法务',3,0,null,null,null,null,1,'57335',null,'',null),
(848,62,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(849,62,'一级部门负责人',3,0,null,null,null,null,6,'',null,'',null),
(850,62,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,48480,57625,57626,57624,54678,61499,61682',null,'',null),
(851,62,'结束',6,0,null,null,null,null,1,'',null,'',null),
(852,63,'员工提交',1,0,null,null,null,null,1,'',null,'',null),
(853,63,'部门合规审批',3,0,null,null,null,null,1,'33477',null,'',null),
(854,63,'部门商务',3,0,null,null,null,null,1,'35387',null,'',null),
(855,63,'法务',3,0,null,null,null,null,1,'57335',null,'',null),
(856,63,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(857,63,'一级部门负责人',3,0,null,null,null,null,6,'',null,'',null),
(858,63,'AP Supervisor(北京)',3,0,null,null,null,null,1,'55849,48480,57625,57626,57624,54678,61499,61682',null,'',null),
(859,63,'所属公司负责人',3,0,null,null,null,null,16,'',null,'',null),
(860,63,'结束',6,0,null,null,null,null,1,'',null,'',null),
(861,64,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(862,64,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(863,64,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(864,64,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(865,64,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(866,64,'coo/cpo',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(867,64,'58040审批 (legal1泰文，legal2相同跳过。审批完到57335legal3)',3,0,null,null,null,null,1,'58040,69767',null,'',null),
(868,64,'51286审批 (没了)',3,0,null,null,null,null,1,'51286',null,'',null),
(869,64,'56883审批（legal1英文，审批完到57335）',3,0,null,null,null,null,1,'56883,69767',null,'',null),
(870,64,'57335审批（根据跳过逻辑，57335审批完到APS就行）',3,0,null,null,null,null,1,'57335',null,'',null),
(871,64,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(872,64,'AP Supervisor(北京)',3,0,null,null,null,null,1,'65230',null,'',null),
(873,64,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(874,64,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(875,64,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(876,64,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(877,64,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(878,64,'结束',6,0,null,null,null,null,1,null,null,'',null),
(879,65,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(880,65,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(881,65,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(882,65,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(883,65,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(884,65,'coo/cpo',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(885,65,'中文审批（legal1中文）',3,0,null,null,null,null,1,'57335',null,'',null),
(886,65,'英文审批（legal1英文）',3,0,null,null,null,null,1,'56883',null,'',null),
(887,65,'泰语审批（legal1泰文）',3,0,null,null,null,null,1,'58040',null,'',null),
(888,65,'中文、英文审批（legal2 中文）',3,0,null,null,null,null,1,'57335',null,'',null),
(889,65,'泰语审批 （legal2泰文）',3,0,null,null,null,null,1,'58040',null,'',null),
(890,65,'语言总审核',3,0,null,null,null,null,1,'57335',null,'',null),
(891,65,'AP Supervisor(菲律宾)',3,0,null,null,null,null,1,'33306,17178',null,'',null),
(892,65,'AP Supervisor(北京)',3,0,null,null,null,null,1,'65230',null,'',null),
(893,65,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(894,65,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(895,65,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(896,65,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(897,65,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(898,65,'结束',6,0,null,null,null,null,1,null,null,'',null),
(899,66,'总部普通员工',1,0,null,null,null,null,1,null,null,'',null),
(900,66,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(901,66,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(902,66,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(903,66,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(904,66,'COO/CPO',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(905,66,'AP（菲律宾）',3,0,null,null,null,'5',1,'54255,54249,28989,26808,24905,24904,24902,23116,21958,19432,17348,32739,60277,58292,58294',null,'{"money_detail":["wht_category","wht_tax_rate"]}',null),
(906,66,'AP Supervisor（菲律宾）',3,0,null,null,null,null,1,'33306,17178',null,'{"money_detail":["wht_category","wht_tax_rate"]}',null),
(907,66,'AP Supervisor（北京）',3,0,null,null,null,'4',1,'65230',null,'{"money_detail":["wht_category","wht_tax_rate"]}',null),
(908,66,'Finance Manager',3,0,null,null,null,null,1,'20254',null,'',null),
(909,66,'Finance Senior Manager',3,0,null,null,null,null,1,'35805',null,'',null),
(910,66,'Finance Director',3,0,null,null,null,null,1,'17152',null,'',null),
(911,66,'CFO',3,0,null,null,null,null,1,'17152',null,'',null),
(912,66,'CEO',3,0,null,null,null,null,1,'17008',null,'',null),
(913,66,'结束',6,0,null,null,null,null,1,null,null,'',null),
(914,39,'Flash HR、CPO下菲律宾部门HRBP',3,0,null,null,null,null,1,'28228',null,'',null);

## 审批关系
insert into `workflow_node_relate`(`id`,`flow_id`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`updated_at`,`created_at`) values
(1,2,12,13,'$p1 != 17','getSubmitterDepartment','申请人非财务部门',10,'2020-05-12 08:51:18','2020-04-30 10:16:37'),
(9,2,13,15,'$p1 != 17 && ($p2 < 2000000 && $p2 > 0 && $p6 == 1 || $p3 < 100000 && $p3 > 0 && $p6 == 2) && $p4 <= 0 || $p5 == 17','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getSubmitterDepartment,getCurrency','申请人非财务部门 and 小于2000/100 and 职等小于等于 20254的职等 or 申请人财务部门',10,'2020-05-21 07:42:04','2020-04-30 11:00:49'),
(10,2,13,16,'$p1 != 17 && ($p2 < 2000000 && $p2 > 0 && $p5 == 1|| $p3 < 100000 && $p3 > 0 && $p5 == 2) && $p4 == 1','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getCurrency','申请人非财务部门 and 小于2000/100 and 职等大于 20254的职等 ',10,'2020-05-21 07:42:10','2020-04-30 11:01:39'),
(13,2,13,14,'$p1 != 17 && ($p2 >= 2000000 && $p4 == 1|| $p3 >= 100000 && $p4 == 2) ','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人非财务部门 and 大于等于2000/100',10,'2020-05-21 07:42:17','2020-04-30 11:06:19'),
(14,2,14,15,'$p1 != 17 && ($p2 < 500000000 && $p2 >= 0 && $p6 == 1 || $p3 < 15000000 && $p3 > 0 && $p6 == 2) && $p4 <= 0 || $p5 == 17','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getSubmitterDepartment,getCurrency','申请人非财务部门 and 大于等于2000/100 小于500000/15000 and 职等小于等于20254',10,'2020-05-21 07:42:25','2020-04-30 11:06:48'),
(15,2,14,16,'$p1 != 17 && ($p2 < 500000000 && $p2 > 0 && $p6 == 1 || $p3 < 15000000 && $p3 > 0 && $p6 == 2) && $p4 == 1 || $p5 == 17','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getSubmitterDepartment,getCurrency','申请人非财务部门 and 大于等于2000/100 小于500000/15000 and 职等大于20254',10,'2020-05-21 07:42:30','2020-04-30 11:06:54'),
(16,2,14,17,'$p1 != 17 && ($p2 >= 500000000 && $p4 == 1|| $p3 >= 15000000 && $p4 == 2) ','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人非财务部门 and 大于500000/15000',10,'2020-05-21 07:42:35','2020-04-30 11:08:05'),
(17,2,15,16,'$p1 != 17 && ($p2 >= 50000000 && $p5 == 1|| $p3 >= 15000000 && $p5 == 2) || $p4 == 17','getSubmitterDepartment,getTHB,getUSD,getSubmitterDepartment,getCurrency','申请人非财务部门 and 大于等于50000/1500 or 申请人财务部门',10,'2020-05-21 07:42:40','2020-04-30 11:08:49'),
(18,2,16,19,'$p1 < 500000000 && $p3 == 1 || $p2 < 15000000 && $p3 == 2','getTHB,getUSD,getCurrency','小于 500000/15000',10,'2020-05-21 07:42:46','2020-04-30 11:10:26'),
(19,2,18,19,null,null,'',10,'2020-05-09 08:46:57','2020-04-30 11:10:48'),
(21,2,17,15,'$p1 != 17 && ($p2 >= 500000000 && $p5 == 1|| $p3 >= 15000000 && $p5 == 2) && $p4 <= 0','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getCurrency','大于等于500000/15000 and 职等小于等于20254',10,'2020-05-21 08:08:35','2020-04-30 11:13:14'),
(23,2,12,15,'$p1 == 17','getSubmitterDepartment','申请人财务部门',10,'2020-05-15 08:11:17','2020-04-30 11:14:28'),
(25,2,17,18,'$p1 == 17 && ($p2 >= 500000000 && $p4 == 1|| $p3 >= 15000000 && $p4 == 2) ','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人财务部门 and 大于等于500000/15000',10,'2020-05-21 07:42:59','2020-05-07 07:50:50'),
(28,2,15,19,'$p1 != 17 && ($p2 < 50000000 && $p2 > 0 && $p4 == 1|| $p3 < 1500000 && $p3 > 0 && $p4 == 2)','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人非财务部门 and 小于50000/1500 ',10,'2020-05-21 07:43:05','2020-05-09 07:42:04'),
(29,2,16,18,'$p1 != 17 && ($p2 >= 500000000 && $p4 == 1|| $p3 >= 15000000 && $p4 == 2) ','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人非财务部门 and 大于等于 500000/15000',10,'2020-05-21 07:43:09','2020-05-09 07:46:37'),
(30,2,16,17,'$p1 == 17 && ($p2 >= 500000000 && $p4 == 1|| $p3 >= 15000000 && $p4 == 2) ','getSubmitterDepartment,getTHB,getUSD,getCurrency','申请人财务部门 and 大于等于500000/15000 ',10,'2020-05-21 07:43:14','2020-05-09 07:51:10'),
(31,2,17,16,'$p1 != 17 && ($p2 >= 500000000 && $p5 == 1|| $p3 >= 15000000 && $p5 == 2) && $p4 > 0','getSubmitterDepartment,getTHB,getUSD,compareJobLevel,getCurrency','申请人财务部门 and 大于等于500000/15000',10,'2020-05-21 12:19:47','2020-05-07 07:50:50'),
(77,12,52,53,'$p1 != 17','getSubmitterDepartment','申请人非财务部门 ->直接上级',10,'2020-07-15 07:48:05','2020-07-15 07:46:56'),
(78,12,53,54,null,null,'直接上级->部门最高级',10,'2020-07-15 07:48:22','2020-07-15 07:47:29'),
(79,12,54,55,'$p1 != 17&&(($p2<*********&&$p3==1)||($p4<3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','非财务部门&&((<100000 and 泰铢)||(<3000 and 美元))      =部门最高级->20254',10,'2020-07-15 07:55:48','2020-07-15 07:49:56'),
(80,12,54,57,'$p1 != 17&&(($p2>=*********&&$p3==1)||($p4>=3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','非财务部门&&((>=100000 and 泰铢)||(>=3000 and 美元))      =部门最高级->17245',10,'2020-07-15 07:56:13','2020-07-15 07:54:43'),
(81,12,57,55,'$p1 != 17','getSubmitterDepartment','非财务部门 17245->20254',10,'2020-07-15 08:18:38','2020-07-15 07:56:34'),
(82,12,55,56,null,null,'20254->17152',10,'2020-07-15 07:57:49','2020-07-15 07:57:12'),
(83,12,56,60,'$p1 != 17&&(($p2<*********&&$p3==1)||($p4<3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','非财务部门&&((<100000 and 泰铢)||(<3000 and 美元))   =17152->结束',10,'2020-07-15 08:03:17','2020-07-15 07:59:33'),
(84,12,56,58,'$p1 != 17&&(($p2>=*********&&$p3==1)||($p4>=3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','非财务部门&&((>=100000 and 泰铢)||(>=3000 and 美元))   =17152->55356',10,'2020-07-15 08:07:58','2020-07-15 08:05:30'),
(85,12,58,60,'($p1<500000000&&$p2==1)||($p3<15000000&&$p2==2)','getTHB,getCurrency,getUSD','((<500000 and 泰铢)||(<15000 and 美元))   = 55356->结束',10,'2020-07-15 08:23:11','2020-07-15 08:08:19'),
(86,12,58,59,'($p1>=500000000&&$p2==1)||($p3>=15000000&&$p2==2)','getTHB,getCurrency,getUSD','((>=500000 and 泰铢)||(>=15000 and 美元))   = 55356->17008',10,'2020-07-15 08:23:16','2020-07-15 08:11:05'),
(87,12,59,60,null,null,'17008->结束',10,'2020-07-15 08:12:17','2020-07-15 08:12:17'),
(88,12,52,55,'$p1 == 17','getSubmitterDepartment','申请人财务部门 ->20254',10,'2020-07-15 08:14:32','2020-07-15 08:13:48'),
(89,12,56,60,'$p1 == 17&&(($p2<*********&&$p3==1)||($p4<3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','财务部门&&((<100000 and 泰铢)||(<3000 and 美元))   =17152->结束',10,'2020-07-15 08:16:33','2020-07-15 08:15:18'),
(90,12,56,57,'$p1 == 17&&(($p2>=*********&&$p3==1)||($p4>=3000000&&$p3==2))','getSubmitterDepartment,getTHB,getCurrency,getUSD','财务部门 &&((>=100000 and 泰铢)||(>=3000 and 美元))   =17152->17245',10,'2020-07-15 08:20:06','2020-07-15 08:17:25'),
(91,12,57,58,'$p1 == 17','getSubmitterDepartment','财务部门 17245->55356',10,'2020-07-15 08:23:58','2020-07-15 08:21:01'),
(94,14,64,65,'$p1 !=17','getSubmitterDepartment','不是财务部门，开始->直接上级',10,'2020-08-12 07:47:51','2020-08-12 06:05:16'),
(95,14,64,69,'$p1 == 17','getSubmitterDepartment','财务部门，开始->FM 20254',10,'2020-08-12 06:07:59','2020-08-12 06:06:35'),
(96,14,65,66,null,null,'直接上级->二级部门负责人',10,'2020-08-12 06:09:45','2020-08-12 06:09:28'),
(97,14,66,67,null,null,'二级部门负责人->一级部门负责人',10,'2020-08-12 06:10:16','2020-08-12 06:10:01'),
(98,14,67,68,null,null,'一级部门负责人->coo',10,'2020-08-12 06:11:01','2020-08-12 06:10:35'),
(99,14,68,69,null,null,'COO->FM',10,'2020-08-12 06:11:26','2020-08-12 06:11:26'),
(100,14,69,70,null,null,'FM->FD',10,'2020-08-12 06:15:23','2020-08-12 06:11:39'),
(101,14,70,71,null,null,'FD->CFO',10,'2020-08-12 06:15:40','2020-08-12 06:11:59'),
(102,14,71,72,null,null,'CFO->CEO',10,'2020-08-12 06:15:47','2020-08-12 06:12:28'),
(103,14,72,73,null,null,'CEO->结束',10,'2020-08-12 06:16:07','2020-08-12 06:15:58'),
(104,15,74,75,null,null,'开始->直接上级',10,'2020-08-12 12:00:51','2020-08-12 12:00:51'),
(105,15,75,76,null,null,'直接上级->二级部门负责人',10,'2020-08-12 12:01:27','2020-08-12 12:01:11'),
(106,15,76,79,'$p1==0 && (($p3<50000000&&$p4==1)||($p5<1576790&&$p4==2)||($p6<11037530&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告&&((<50,000 and 泰铢)||(<1,576.79 and 美元)||(< 11,037.53 and 人民币))   二级负责人->PM',10,'2020-11-06 08:49:06','2020-08-12 12:05:32'),
(107,15,76,77,'$p1==0 && (($p3>=50000000&&$p4==1)||($p5>=1576790&&$p4==2)||($p6>=11037530&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告&&((>=50,000 and 泰铢)||(>=1,576.79 and 美元)||(>= 11,037.53 and 人民币))  二级负责人->一级负责人',10,'2020-11-06 08:49:06','2020-08-12 12:11:49'),
(108,15,77,78,'$p1==0 && $p2==0 && (($p3>=500000000&&$p4==1)||($p5>=16000000&&$p4==2)||($p6>=110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((>=500,000 and 泰铢)||(>=16,000 and 美元)||(>= 110,000 and 人民币))    一级负责人->COO',10,'2020-08-12 12:17:54','2020-08-12 12:14:51'),
(109,15,77,79,'$p1==0 && $p2==0 && (($p3<500000000&&$p4==1)||($p5<16000000&&$p4==2)||($p6<110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<500,000 and 泰铢)||(<16,000 and 美元)||(<110,000 and 人民币))    一级负责人->PM',10,'2020-08-12 12:19:22','2020-08-12 12:18:29'),
(110,15,79,80,null,null,'PM->PSM',10,'2020-08-12 12:20:07','2020-08-12 12:19:54'),
(111,15,80,83,'(($p1<300000000&&$p2==1)||($p3<9460740&&$p2==2)||($p4<66225170&&$p2==3))','getTHB,getCurrency,getUSD,getCNY','((<300,000 and 泰铢)||(<9,460.74 and 美元)||(<66,225.17and 人民币))    PSM->结束       (3种类型都走)',10,'2020-08-12 12:58:23','2020-08-12 12:41:54'),
(112,15,80,81,'(($p1>=300000000&&$p2==1)||($p3>=9460740&&$p2==2)||($p4>=66225170&&$p2==3))','getTHB,getCurrency,getUSD,getCNY','((>=300,000 and 泰铢)||(>=9,460.74 and 美元)||(>=66,225.17and 人民币))    PSM->PD     (3种类型都走)',10,'2020-08-12 13:00:26','2020-08-12 12:44:26'),
(113,15,81,83,'(($p1<500000000&&$p2==1)||($p3<16000000&&$p2==2)||($p4<110000000&&$p2==3))','getTHB,getCurrency,getUSD,getCNY','((<500,000 and 泰铢)||(<16,000 and 美元)||(<110,000 and 人民币))    PD->结束     (3种类型都走)',10,'2020-08-12 13:00:29','2020-08-12 12:46:03'),
(114,15,81,82,'(($p1>=500000000&&$p2==1)||($p3>=16000000&&$p2==2)||($p4>=110000000&&$p2==3))','getTHB,getCurrency,getUSD,getCNY','((>=500,000 and 泰铢)||(>=16,000 and 美元)||(>= 110,000 and 人民币))    PD->CFO     (3种类型都走)',10,'2020-08-12 13:00:32','2020-08-12 12:47:18'),
(115,15,82,83,null,null,null,10,'2020-08-12 12:48:12','2020-08-12 12:48:12'),
(116,15,76,79,'$p1==1 && (($p2<*********&&$p3==1)||($p4<3153580&&$p3==2)||($p5<22075060&&$p3==3))','isAd,getTHB,getCurrency,getUSD,getCNY','广告 && ((<100,000 and 泰铢)||(<3,153.58 and 美元)||(< 22,075.06 and 人民币))   二级负责人->PM',10,'2020-08-12 12:52:02','2020-08-12 12:49:47'),
(117,15,76,77,'$p1==1 && (($p2>=*********&&$p3==1)||($p4>=3153580&&$p3==2)||($p5>=22075060&&$p3==3))','isAd,getTHB,getCurrency,getUSD,getCNY','广告 && ((>=100,000 and 泰铢)||(>=3,153.58 and 美元)||(>= 22,075.06 and 人民币))   二级负责人->一级负责人',10,'2020-08-12 12:53:17','2020-08-12 12:52:35'),
(118,15,77,79,null,null,'一级负责人->PM',10,'2020-08-12 12:54:22','2020-08-12 12:54:22'),
(119,15,78,79,null,null,'COO->PM',10,'2020-08-17 13:22:20','2020-08-17 13:16:02'),
(120,16,84,85,null,null,'开始->PM',10,'2020-08-12 13:18:56','2020-08-12 13:14:38'),
(121,16,85,86,null,null,'PM->PSM',10,'2020-08-12 13:19:15','2020-08-12 13:19:07'),
(122,16,86,87,null,null,'PSM->AP',10,'2020-08-12 13:19:34','2020-08-12 13:19:34'),
(123,16,87,88,null,null,'AP->APS',10,'2020-08-12 13:19:57','2020-08-12 13:19:45'),
(124,16,88,89,'','','APS->FM',10,'2020-08-12 13:21:58','2020-08-12 13:20:10'),
(125,16,89,93,'$p1==0 && $p2==0 && (($p3<300000000&&$p4==1)||($p5<9460740&&$p4==2)||($p6<66225170&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<300,000 and 泰铢)||(<9,460.74 and 美元)||(< 66,225.17 and 人民币)) FM->结束',10,'2020-08-12 13:27:38','2020-08-12 13:21:02'),
(126,16,89,93,'$p1==1 && (($p2<*********&&$p3==1)||($p4<3153580&&$p3==2)||($p5<22075060&&$p3==3))','isAd,getTHB,getCurrency,getUSD,getCNY','广告 && ((<100,000 and 泰铢)||(<3,153.58 and 美元)||(< 22,075.06 and 人民币))  FM->结束',10,'2020-08-12 13:23:30','2020-08-12 13:22:36'),
(127,16,89,93,'$p1==1 && (($p2<50000000&&$p3==1)||($p4<1576790&&$p3==2)||($p5<11037530&&$p3==3))','isAsset,getTHB,getCurrency,getUSD,getCNY','咨询费&&((<50,000 and 泰铢)||(<1,576.79 and 美元)||(< 11,037.53 and 人民币)) FM->结束',10,'2020-08-12 13:24:56','2020-08-12 13:23:53'),
(128,16,89,90,null,null,'FM->FD  上面的都不满足的话',10,'2020-08-12 13:32:50','2020-08-12 13:28:03'),
(129,16,90,93,'$p1==0 && $p2==0 && (($p3<500000000&&$p4==1)||($p5<16000000&&$p4==2)||($p6<110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<500,000 and 泰铢)||(<16,000 and 美元)||(<110,000 and 人民币))  FD->结束',10,'2020-08-12 13:30:16','2020-08-12 13:28:34'),
(130,16,90,93,'($p1==1 || $p2==1) && (($p3<300000000&&$p4==1)||($p5<9460740&&$p4==2)||($p6<66225170&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','(广告或咨询费)&&((<300,000 and 泰铢)||(<9,460.74 and 美元)||(<66,225.17and 人民币))   FD->结束 ',10,'2020-08-12 13:32:13','2020-08-12 13:30:35'),
(131,16,90,91,null,null,'FD->CFO  上面的都不满足的话',10,'2020-08-12 13:33:02','2020-08-12 13:32:35'),
(132,16,91,93,'($p1==1 || $p2==1) && (($p3<500000000&&$p4==1)||($p5<16000000&&$p4==2)||($p6<110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','(广告或咨询费)&&((<500,000 and 泰铢)||(<16,000 and 美元)||(<110,000 and 人民币))  CFO->结束',10,'2020-08-12 13:34:38','2020-08-12 13:33:32'),
(133,16,91,92,null,null,'CFO->CEO',10,'2020-08-12 13:35:06','2020-08-12 13:34:50'),
(134,16,92,93,null,null,'CEO->结束',10,'2020-08-12 13:35:25','2020-08-12 13:35:16'),
(135,17,94,95,null,null,'开始->直接上级',10,'2020-08-13 08:41:11','2020-08-13 08:40:58'),
(136,17,95,96,null,null,'直接上级->二级部门负责人',10,'2020-08-13 08:42:07','2020-08-13 08:41:49'),
(137,17,96,99,'$p1<5000000 && $p2==1','getTHB,getCurrency','泰铢<5000    二级部门负责人->AP',10,'2020-08-13 08:44:25','2020-08-13 08:42:34'),
(138,17,96,97,'$p1>=5000000 && $p2==1','getTHB,getCurrency','泰铢>=5000    二级部门负责人->一级部门负责人',10,'2020-08-13 08:45:37','2020-08-13 08:45:12'),
(139,17,97,99,'$p1<20000000 && $p2==1','getTHB,getCurrency','泰铢<20000    一级部门负责人->AP',10,'2020-08-13 08:52:38','2020-08-13 08:46:14'),
(140,17,97,98,'$p1>=20000000 && $p2==1','getTHB,getCurrency','泰铢>=20000    一级部门负责人->COO',10,'2020-08-13 08:53:20','2020-08-13 08:52:53'),
(141,17,98,99,null,null,'COO->AP',10,'2020-08-13 08:54:04','2020-08-13 08:53:39'),
(142,17,99,100,null,null,'AP->APS',10,'2020-08-13 08:55:35','2020-08-13 08:54:25'),
(143,17,100,105,'$p1<5000000 && $p2==1','getTHB,getCurrency','泰铢<5000  APS->结束',10,'2020-08-13 09:04:11','2020-08-13 08:57:10'),
(144,17,100,101,'$p1>=5000000 && $p2==1','getTHB,getCurrency','泰铢>=5000 APS->FM',10,'2020-08-13 09:04:19','2020-08-13 08:57:57'),
(145,17,101,102,null,null,' FM->FD',10,'2020-08-13 09:04:29','2020-08-13 08:58:41'),
(146,17,102,105,'$p1<10000000 && $p2==1','getTHB,getCurrency','泰铢<10000    FD->结束',10,'2020-08-13 09:06:22','2020-08-13 09:02:18'),
(147,17,102,103,'$p1>=10000000 && $p2==1','getTHB,getCurrency','泰铢>=10000    FD->CFO',10,'2020-08-13 09:07:14','2020-08-13 09:06:44'),
(148,17,103,105,'$p1<20000000 && $p2==1','getTHB,getCurrency','泰铢<20000    CFO->结束',10,'2020-08-13 09:07:59','2020-08-13 09:07:32'),
(149,17,103,104,'$p1>=20000000 && $p2==1','getTHB,getCurrency','泰铢>=20000 CFO->CEO',10,'2020-08-13 09:08:43','2020-08-13 09:08:12'),
(150,17,104,105,null,null,'CEO->结束',10,'2020-08-13 09:09:05','2020-08-13 09:08:55'),
(151,18,106,107,null,null,'开始->网点主管',10,'2020-09-01 13:05:48','2020-09-01 13:05:29'),
(152,18,107,108,null,null,'网点主管->Supervisor(level1)',10,'2020-09-01 13:06:31','2020-09-01 13:06:08'),
(153,18,108,113,'$p1<3000000 && $p2==1','getTHB,getCurrency','泰铢<3000    Supervisor(level1)->AP',10,'2020-09-01 13:08:17','2020-09-01 13:07:18'),
(154,18,108,109,'$p1>=3000000 && $p2==1','getTHB,getCurrency','泰铢>=3000    Supervisor(level1)->Manager(level2)',10,'2020-09-01 13:09:17','2020-09-01 13:08:32'),
(155,18,109,113,'$p1<5000000 && $p2==1','getTHB,getCurrency','泰铢<5000    Manager(level2)->AP',10,'2020-09-01 13:10:12','2020-09-01 13:09:41'),
(156,18,109,110,'$p1>=5000000 && $p2==1','getTHB,getCurrency','泰铢>=5000    Manager(level2)->Senior Manager(level3)',10,'2020-09-01 13:11:11','2020-09-01 13:10:26'),
(157,18,110,113,'$p1<10000000 && $p2==1','getTHB,getCurrency','泰铢<10000    Senior Manager(level3)->AP',10,'2020-09-01 13:12:24','2020-09-01 13:11:34'),
(158,18,110,111,'$p1>=10000000 && $p2==1','getTHB,getCurrency','泰铢>=10000    Senior Manager(level3)->Director(level4)',10,'2020-09-01 13:13:54','2020-09-01 13:13:17'),
(159,18,111,113,null,null,'Direcotr(level4)->AP',10,'2020-09-01 13:14:47','2020-09-01 13:14:13'),
(160,18,113,114,null,null,' AP->APS',10,'2020-09-01 13:16:35','2020-09-01 13:14:25'),
(161,18,114,117,'$p1<5000000 && $p2==1','getTHB,getCurrency','泰铢<5000 APS->结束',10,'2020-09-01 13:16:38','2020-09-01 13:15:12'),
(162,18,114,115,'$p1>=5000000 && $p2==1','getTHB,getCurrency','泰铢>=5000 APS->FM',10,'2020-09-01 13:16:48','2020-09-01 13:15:58'),
(163,18,115,117,'$p1<10000000 && $p2==1','getTHB,getCurrency','泰铢<10000 FM->结束',10,'2020-09-01 13:17:52','2020-09-01 13:17:28'),
(164,18,115,116,'$p1>=10000000 && $p2==1','getTHB,getCurrency','泰铢>=10000 FM->FD',10,'2020-09-01 13:18:31','2020-09-01 13:18:08'),
(165,18,116,117,null,null,'FD->结束',10,'2020-09-01 13:18:48','2020-09-01 13:18:41'),
(166,15,76,77,'$p1==25 || $p1==18','getSubmitterDepartment','如果是hub部门，二级负责人->一级负责人',11,'2020-10-14 11:53:23','2020-09-09 07:01:55'),
(167,17,96,97,'$p1==25','getSubmitterDepartment','hub部门，二级部门负责人->一级部门负责人',11,'2020-09-09 07:05:17','2020-09-09 07:05:17'),
(168,19,118,120,'','','员工->直接上级',10,'2020-09-15 17:14:58','2020-09-15 17:14:25'),
(169,19,120,121,null,null,'直接上级->二级部门负责人',10,'2020-09-15 17:18:28','2020-09-15 17:15:47'),
(170,19,121,123,'((($p1<30000 && ($p2==25 || $p2== 18) ) || $p1>=30000) && $p3==1) || ((($p1<300000 && ($p2==25 || $p2== 18) ) || $p1>=300000) && $p3==2) ','getTHB,getSubmitterDepartment,getContractType','二级部门->一级部门负责人',10,'2020-10-28 04:32:14','2020-09-15 17:18:23'),
(171,19,121,125,'(($p1<30000 && $p3==1 && $p4 != 18 && $p4 != 25) || ($p1<300000 && $p3==2 && $p4 != 18 && $p4 != 25)) && $p2==3','getTHB,getLang,getContractType,getSubmitterDepartment','二级部门->中文审批',10,'2020-10-29 04:25:14','2020-09-15 17:22:10'),
(172,19,121,126,'(($p1<30000 && $p3==1 && $p4 != 18 && $p4 != 25) || ($p1<300000 && $p3==2 && $p4 != 18 && $p4 != 25)) && $p2==2 ','getTHB,getLang,getContractType,getSubmitterDepartment','二级部门->英文',10,'2020-10-29 04:25:34','2020-09-15 17:29:24'),
(173,19,121,127,'(($p1<30000 && $p3==1 && $p4 != 18 && $p4 != 25) || ($p1<300000 && $p3==2 && $p4 != 18 && $p4 != 25)) && $p2==1','getTHB,getLang,getContractType','二级部门->泰语',10,'2020-10-29 04:29:11','2020-09-15 17:33:33'),
(174,19,123,125,'$p1==3 && (($p3==1 && $p2 <500000) || ($p3==2 && $p2 <5000000))','getLang,getTHB,getContractType','一级部门->中文审批',10,'2020-09-15 21:18:12','2020-09-15 17:38:47'),
(175,19,123,126,'$p1==2 && (($p3==1 && $p2 <500000) || ($p3==2 && $p2 <5000000))','getLang,getTHB,getContractType','一级部门->英文审批',10,'2020-09-15 21:18:16','2020-09-15 17:45:05'),
(176,19,123,127,'$p1==1 && (($p3==1 && $p2 <500000) || ($p3==2 && $p2 <5000000))','getLang,getTHB,getContractType','一级部门->泰文审批',10,'2020-10-29 05:42:26','2020-09-15 17:45:15'),
(177,19,123,124,'($p1>=500000 && $p2 == 1 ) || ($p1 >=5000000 && $p2==2)','getTHB,getContractType','一级部门->coo/cpo',10,'2020-10-29 04:31:28','2020-09-15 17:51:49'),
(178,19,124,125,'$p1==3 ','getLang,getContractType','coo/cpo->中',10,'2020-09-15 20:03:13','2020-09-15 17:58:15'),
(179,19,124,126,'$p1==2','getLang,getContractType','coo/cpo->英',10,'2020-09-15 20:03:08','2020-09-15 17:58:27'),
(180,19,124,127,'$p1==1','getLang,getContractType','coo/cpo->泰',10,'2020-10-29 05:45:07','2020-09-15 17:59:24'),
(181,19,125,128,'','','中->中英',10,'2020-09-15 18:04:24','2020-09-15 18:03:27'),
(182,19,126,128,null,null,'英->中英',10,'2020-09-15 18:04:48','2020-09-15 18:04:11'),
(183,19,127,129,null,null,'泰文->泰文二级',10,'2020-10-29 06:00:47','2020-09-15 18:05:39'),
(184,19,128,130,null,null,'中英->AP Supervisor(菲律宾)',10,'2020-09-15 18:07:14','2020-09-15 18:07:14'),
(185,19,129,119,null,null,'泰文二级>语言总审核',10,'2020-10-29 06:01:29','2020-09-15 18:09:15'),
(186,19,130,131,null,null,'AP Supervisor(菲律宾)->AP Supervisor(北京)',10,'2020-09-15 18:12:57','2020-09-15 18:11:42'),
(187,19,131,132,'($p1>=30000 && $p2 == 1 ) || ($p1 >=300000 && $p2 ==2 )','getTHB,getContractType','AP Supervisor(北京)->Finance Manager',10,'2020-09-15 21:19:07','2020-09-15 18:12:40'),
(188,19,132,133,'',null,'Finance Manager->Finance Senior Manager',10,'2020-09-15 19:22:53','2020-09-15 18:13:47'),
(189,19,133,134,'($p1>=100000 && $p2 == 1) || ($p1 >= 1000000 && $p2==2)','getTHB,getContractType','Finance Senior Manager->Finance Director',10,'2020-09-15 21:19:48','2020-09-15 18:15:48'),
(190,19,134,135,'($p1>=500000 && $p2 == 1) || ($p1 >= 5000000 && p2 == 2)','getTHB,getContractType','Finance Director->CFO',10,'2020-10-29 15:41:29','2020-09-15 18:16:51'),
(191,19,135,136,null,null,'CFO->CEO',10,'2020-09-15 19:04:30','2020-09-15 18:17:47'),
(192,19,136,137,null,null,'CEO->结束',10,'2020-09-15 19:05:00','2020-09-15 19:05:00'),
(193,19,131,137,'($p1<30000 && $p2==1) || ($p1<300000 && $p2 == 2)','getTHB,getContractType','AP Supervisor(北京)->结束',10,'2020-09-15 21:20:38','2020-09-15 19:14:37'),
(194,19,133,137,'($p1>=30000 && $p1<100000 && $p2 == 1) || ($p1>=300000 && $p1<1000000 && $p2==2)','getTHB,getContractType','Finance Senior Manager->结束',10,'2020-09-15 21:21:49','2020-09-15 19:19:45'),
(195,19,134,137,'($p1>=100000 && $p1<500000 && $p2 == 1) || ($p1>=1000000 && $p1< 5000000 && $p2 ==2)','getTHB,getContractType','Finance Director->结束',10,'2020-09-15 21:22:12','2020-09-15 19:24:19'),
(196,20,138,139,null,null,'员工提交->PM',10,'2020-09-24 09:13:44','2020-09-24 09:13:26'),
(197,20,139,140,null,null,'PM->PSM',10,'2020-09-24 09:14:21','2020-09-24 09:14:09'),
(198,20,140,141,null,null,'PSM->AP(菲律宾)',10,'2020-09-24 09:16:16','2020-09-24 09:15:43'),
(199,20,141,142,null,null,'AP(菲律宾）->APS（菲律宾）',10,'2020-09-24 09:17:17','2020-09-24 09:16:48'),
(200,20,142,143,null,null,'APS(菲律宾)->APS(北京)',10,'2020-09-24 09:19:22','2020-09-24 09:17:39'),
(201,20,143,144,null,null,'APS(北京)->FM',10,'2020-09-24 09:19:59','2020-09-24 09:19:59'),
(202,20,144,149,'$p1==0 && $p2==0 && (($p3<50000000&&$p4==1)||($p5<1576790&&$p4==2)||($p6<11037530&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<500,000 and 泰铢)||(<1576.79 and 美元)||(< 11037.53 and 人民币)) FM->结束',10,'2020-09-24 09:47:29','2020-09-24 09:21:22'),
(203,20,144,149,'$p1==1 && (($p2<*********&&$p3==1)||($p4<3153580&&$p3==2)||($p5<22075060&&$p3==3))','isAd,getTHB,getCurrency,getUSD,getCNY','广告 && ((<100,000 and 泰铢)||(<3,153.58 and 美元)||(< 22,075.06 and 人民币))  FM->结束',10,'2020-09-24 09:47:32','2020-09-24 09:28:47'),
(204,20,144,149,'$p1==1 && (($p2<50000000&&$p3==1)||($p4<1576790&&$p3==2)||($p5<11037530&&$p3==3))','isAsset,getTHB,getCurrency,getUSD,getCNY','咨询费&&((<50,000 and 泰铢)||(<1,576.79 and 美元)||(< 11,037.53 and 人民币)) FM->结束',10,'2020-09-24 09:47:38','2020-09-24 09:29:52'),
(205,20,144,145,null,null,'FM->FSM',10,'2020-09-24 09:30:45','2020-09-24 09:30:31'),
(206,20,145,149,'$p1==0 && $p2==0 && (($p3<300000000&&$p4==1)||($p5<9460740&&$p4==2)||($p6<66225170&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<300000 and 泰铢)||(<9,460.74 and 美元)||(< 66225.17 and 人民币)) FSM->结束',10,'2020-09-24 09:47:42','2020-09-24 09:31:06'),
(207,20,145,146,null,null,'FSM->FD',10,'2020-09-24 09:37:11','2020-09-24 09:36:45'),
(208,20,146,149,'$p1==0 && $p2==0 && (($p3<500000000&&$p4==1)||($p5<16000000&&$p4==2)||($p6<110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','非广告 &&非咨询费&&((<500000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币)) FD->结束',10,'2020-09-24 09:47:45','2020-09-24 09:37:28'),
(209,20,146,149,'($p1==1 || $p2==1) && (($p3<300000000&&$p4==1)||($p5<9460740&&$p4==2)||($p6<66225170&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','(广告|| 咨询费)&&((<300000 and 泰铢)||(<9,460.74 and 美元)||(< 66225.17 and 人民币)) FD->结束',10,'2020-09-24 09:47:47','2020-09-24 09:42:37'),
(210,20,146,147,null,null,'FD->CFO',10,'2020-09-24 09:46:31','2020-09-24 09:46:15'),
(211,20,147,149,'($p1==1 || $p2==1) && (($p3<500000000&&$p4==1)||($p5<16000000&&$p4==2)||($p6<110000000&&$p4==3))','isAd,isAsset,getTHB,getCurrency,getUSD,getCNY','(广告|| 咨询费)&&((<500000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币)) CFO->结束',10,'2020-09-24 09:49:20','2020-09-24 09:47:13'),
(212,20,147,148,null,null,'CFO-CEO',10,'2020-09-24 09:49:45','2020-09-24 09:49:36'),
(213,20,148,149,null,null,'CEO->结束',10,'2020-09-24 09:50:00','2020-09-24 09:49:53'),
(214,21,150,151,null,null,'员工提交->直接上级',10,'2020-09-24 11:53:36','2020-09-24 11:53:21'),
(215,21,151,152,null,null,'直接上级->二级部门负责人',10,'2020-09-24 11:54:01','2020-09-24 11:53:48'),
(216,21,152,153,'$p1==25 || $p1==18','getSubmitterDepartment','hub&flash home部门，二级部门负责人->一级部门负责人',10,'2020-09-24 12:28:57','2020-09-24 11:54:28'),
(217,21,152,155,'$p1<2000000 && $p2==1','getTHB,getCurrency','泰铢<2000    二级部门负责人->AP',10,'2020-09-24 11:59:13','2020-09-24 11:56:15'),
(218,21,152,153,'$p1>=2000000 && $p2==1','getTHB,getCurrency','泰铢>=2000    二级部门负责人->一级部门负责人',10,'2020-09-24 11:59:17','2020-09-24 11:57:15'),
(219,21,153,155,'$p1<50000000 && $p2==1','getTHB,getCurrency','泰铢<50000    一级部门负责人->AP',10,'2020-09-24 12:00:14','2020-09-24 11:57:53'),
(220,21,153,154,'$p1>=50000000 && $p2==1','getTHB,getCurrency','泰铢>=50000    一级部门负责人->COO/CPO',10,'2020-09-24 12:01:05','2020-09-24 12:00:28'),
(221,21,154,155,null,null,'COO/CPO->AP',10,'2020-09-24 12:02:12','2020-09-24 12:01:59'),
(222,21,155,156,null,null,'AP->APS(菲律宾)',10,'2020-09-24 12:02:49','2020-09-24 12:02:35'),
(223,21,156,157,null,null,'APS(菲律宾)->APS(北京)',10,'2020-09-24 12:04:34','2020-09-24 12:03:25'),
(224,21,157,163,'$p1<2000000 && $p2==1','getTHB,getCurrency','APS(北京)->结束',10,'2020-09-24 12:05:13','2020-09-24 12:04:06'),
(225,21,157,158,'$p1>=2000000 && $p2==1','getTHB,getCurrency','APS(北京)->FM',10,'2020-09-24 12:06:55','2020-09-24 12:05:33'),
(226,21,158,159,null,null,'FM->FSM',10,'2020-09-24 12:06:14','2020-09-24 12:06:00'),
(227,21,159,163,'$p1<20000000 && $p2==1','getTHB,getCurrency','FSM->结束',10,'2020-09-24 12:07:07','2020-09-24 12:06:33'),
(228,21,159,160,'$p1>=20000000 && $p2==1','getTHB,getCurrency','FSM->FD',10,'2020-09-24 12:07:38','2020-09-24 12:07:19'),
(229,21,160,163,'$p1<50000000 && $p2==1','getTHB,getCurrency','FD->结束',10,'2020-09-24 12:26:49','2020-09-24 12:26:33'),
(230,21,160,161,'$p1>=50000000 && $p2==1','getTHB,getCurrency','FD->CFO',10,'2020-09-24 12:27:20','2020-09-24 12:27:03'),
(231,21,161,162,null,null,'CFO->CEO',10,'2020-09-24 12:27:38','2020-09-24 12:27:31'),
(232,21,162,163,null,null,'CEO->结束',10,'2020-09-24 12:27:58','2020-09-24 12:27:47'),
(233,22,164,165,null,null,'开始->网点主管',10,'2020-09-24 12:57:32','2020-09-24 12:57:19'),
(234,22,165,166,'',null,'网点主管->Supervisor(level1)',10,'2020-09-24 12:58:52','2020-09-24 12:58:52'),
(235,22,166,170,'$p1<3000000 && $p2==1','getTHB,getCurrency','泰铢<3000    Supervisor(level1)->AP',10,'2020-09-24 13:00:18','2020-09-24 12:59:21'),
(236,22,166,167,'$p1>=3000000 && $p2==1','getTHB,getCurrency','泰铢>=3000    Supervisor(level1)->Manager(level2)',10,'2020-09-24 13:00:45','2020-09-24 13:00:29'),
(237,22,167,170,'$p1<5000000 && $p2==1','getTHB,getCurrency','泰铢<5000    Manager(level2)->AP',10,'2020-09-24 13:01:44','2020-09-24 13:01:12'),
(238,22,167,168,'$p1>=5000000 && $p2==1','getTHB,getCurrency','泰铢>=5000    Manager(level2)->Senior Manager(level3)',10,'2020-09-24 13:02:30','2020-09-24 13:01:57'),
(239,22,168,170,'$p1<10000000 && $p2==1','getTHB,getCurrency','泰铢<10000    Senior Manager(level3)->AP',10,'2020-09-24 13:02:41','2020-09-24 13:02:21'),
(240,22,168,169,'$p1>=10000000 && $p2==1','getTHB,getCurrency','泰铢>=10000    Senior Manager(level3)->Director(level4)',10,'2020-09-24 13:03:08','2020-09-24 13:02:52'),
(241,22,169,170,null,null,'Direcotr(level4)->AP',10,'2020-09-24 13:03:28','2020-09-24 13:03:21'),
(242,22,170,171,null,null,'AP->APS（菲律宾）',10,'2020-09-24 13:03:57','2020-09-24 13:03:46'),
(243,22,171,172,null,null,'APS（菲律宾）->APS(北京)',10,'2020-09-24 13:04:22','2020-09-24 13:04:08'),
(244,22,172,176,'$p1<3000000 && $p2==1','getTHB,getCurrency','APS(北京)->结束',10,'2020-09-24 13:05:14','2020-09-24 13:04:39'),
(245,22,172,173,'$p1>=3000000 && $p2==1','getTHB,getCurrency','APS(北京)->FM',10,'2020-09-24 13:05:43','2020-09-24 13:05:26'),
(246,22,173,176,'$p1<5000000 && $p2==1','getTHB,getCurrency','FM->结束',10,'2020-09-24 13:06:20','2020-09-24 13:05:56'),
(247,22,173,174,'$p1>=5000000 && $p2==1','getTHB,getCurrency','FM->FSM',10,'2020-09-24 13:06:47','2020-09-24 13:06:31'),
(248,22,174,176,'$p1<10000000 && $p2==1','getTHB,getCurrency','FSM->结束',10,'2020-09-24 13:07:19','2020-09-24 13:07:02'),
(249,22,174,175,'$p1>=10000000 && $p2==1','getTHB,getCurrency','FSM->FD',10,'2020-09-24 13:07:42','2020-09-24 13:07:29'),
(250,22,175,176,null,'getTHB,getCurrency','FD->结束',10,'2020-09-24 13:08:00','2020-09-24 13:07:51'),
(251,23,177,178,null,null,'开始->直接上级',10,'2020-09-25 03:53:08','2020-09-25 03:52:57'),
(252,23,178,179,null,null,'直接上级->二级部门负责人',10,'2020-09-25 03:53:40','2020-09-25 03:53:29'),
(253,23,179,180,null,null,'二级部门负责人->一级部门负责人',10,'2020-09-25 03:54:15','2020-09-25 03:53:50'),
(254,23,180,182,'$p1<50000000 && $p2==1','getTHB,getCurrency','一级部门负责人->FM',10,'2020-09-25 03:56:11','2020-09-25 03:55:11'),
(255,23,180,181,null,null,'一级部门负责人->CPO/COO',10,'2020-09-25 03:56:44','2020-09-25 03:56:26'),
(256,23,181,182,null,null,'CPO/COO->FM',10,'2020-09-25 03:57:03','2020-09-25 03:56:54'),
(257,23,182,183,null,null,'FM->FSM',10,'2020-09-25 03:57:27','2020-09-25 03:57:20'),
(258,23,183,187,'$p1<50000000 && $p2==1','getTHB,getCurrency','FSM->结束',10,'2020-09-25 03:59:53','2020-09-25 03:58:20'),
(259,23,183,184,null,null,'FSM->FD',10,'2020-09-25 04:00:15','2020-09-25 04:00:04'),
(260,23,184,187,'$p1<********* && $p2==1','getTHB,getCurrency','FD->结束',10,'2020-09-25 04:00:56','2020-09-25 04:00:26'),
(261,23,184,185,null,null,'FD->CFO',10,'2020-09-25 04:01:15','2020-09-25 04:01:05'),
(262,23,185,187,'$p1<500000000 && $p2==1','getTHB,getCurrency','CFO->结束',10,'2020-09-25 04:01:55','2020-09-25 04:01:24'),
(263,23,185,186,null,null,'CFO->CEO',10,'2020-09-25 04:02:32','2020-09-25 04:02:21'),
(264,23,186,187,null,null,'CEO->结束',10,'2020-09-25 04:02:47','2020-09-25 04:02:40'),
(265,24,188,189,null,null,'员工提交->直接上级',10,'2020-10-20 12:13:12','2020-10-20 12:13:12'),
(266,24,189,190,null,null,'直接上级->二级部门负责人',10,'2020-10-20 12:13:13','2020-10-20 12:13:13'),
(284,24,190,191,'(($p1>=50000000 && $p2==1) || ($p1>=1576790 && $p2==2)||($p1>=11037530 && $p2==3)) || ($p3==25 || $p3==18)','getAmount,getCurrency,getSubmitterDepartment',null,10,'2020-10-20 12:13:13','2020-10-20 12:13:13'),
(285,24,190,193,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'th\'','getAmount,getCurrency,getSubmitterDepartment,getLang',null,10,'2020-10-20 12:13:14','2020-10-20 12:13:14'),
(286,24,190,195,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'en\'','getAmount,getCurrency,getSubmitterDepartment,getLang',null,10,'2020-10-20 12:13:15','2020-10-20 12:13:15'),
(287,24,190,196,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'zh-CN\'','getAmount,getCurrency,getSubmitterDepartment,getLang',null,10,'2020-10-20 12:13:16','2020-10-20 12:13:16'),
(288,24,191,192,'($p1>=*********0 && $p2==\'1\') || ($p1>=31535800 && $p2==\'2\') || ($p1>=220750600 && $p2==\'3\') ','getAmount,getCurrency',null,10,'2020-10-20 12:13:16','2020-10-20 12:13:16'),
(289,24,192,193,'$p1==\'th\'','getLang',null,10,'2020-10-20 12:13:17','2020-10-20 12:13:17'),
(290,24,192,195,'$p1==\'en\'','getLang',null,10,'2020-10-20 12:13:18','2020-10-20 12:13:18'),
(291,24,192,196,'$p1==\'zh-CN\'','getLang',null,10,'2020-10-20 12:13:19','2020-10-20 12:13:19'),
(292,24,193,194,null,null,null,10,'2020-10-20 12:13:20','2020-10-20 12:13:20'),
(293,24,194,196,null,null,null,10,'2020-10-20 12:13:21','2020-10-20 12:13:21'),
(294,24,195,196,null,null,null,10,'2020-10-20 12:13:21','2020-10-20 12:13:21'),
(295,24,191,193,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'th\' ','getAmount,getCurrency,getLang',null,10,'2020-10-20 12:13:22','2020-10-20 12:13:22'),
(296,24,191,195,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'en\' ','getAmount,getCurrency,getLang',null,10,'2020-10-20 12:13:23','2020-10-20 12:13:23'),
(297,24,191,196,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'zh-CN\' ','getAmount,getCurrency,getLang',null,10,'2020-10-20 12:13:23','2020-10-20 12:13:23'),
(298,24,196,198,null,null,null,10,'2020-10-20 12:13:24','2020-10-20 12:13:24'),
(299,24,198,199,null,null,null,10,'2020-10-20 12:13:25','2020-10-20 12:13:25'),
(300,24,199,200,'($p1>=50000000 && $p2==1) || ($p1>=1576790 && $p2==2)|| ($p1>=11037530 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:26','2020-10-20 12:13:26'),
(301,24,199,205,'($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:27','2020-10-20 12:13:27'),
(302,24,200,201,null,null,null,10,'2020-10-20 12:13:27','2020-10-20 12:13:27'),
(303,24,201,202,'($p1>=500000000 && $p2==1) || ($p1>=16000000 && $p2==2) || ($p1>=110000000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:28','2020-10-20 12:13:28'),
(304,24,201,205,'($p1<500000000 && $p2==1) || ($p1<16000000 && $p2==2) || ($p1<110000000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:29','2020-10-20 12:13:29'),
(305,24,202,203,'($p1>=*********0 && $p2==1) || ($p1>=31535800 && $p2==2) || ($p1>=220750600 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:29','2020-10-20 12:13:29'),
(306,24,202,205,'($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-20 12:13:30','2020-10-20 12:13:30'),
(307,24,203,204,null,null,null,10,'2020-10-20 12:13:30','2020-10-20 12:13:30'),
(308,24,204,205,null,null,null,10,'2020-10-20 12:13:31','2020-10-20 12:13:31'),
(313,25,222,223,null,null,null,10,'2020-10-27 04:28:25','2020-10-27 04:28:25'),
(314,25,223,224,'($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 04:28:25'),
(315,25,223,225,'($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1<10000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 04:28:25'),
(316,25,224,225,null,null,null,10,'2020-10-27 04:28:25','2020-10-27 04:28:25'),
(317,25,225,226,null,null,null,10,'2020-10-27 04:28:25','2020-10-27 04:28:25'),
(318,25,226,227,null,null,null,10,'2020-10-27 04:28:25','2020-10-27 04:28:25'),
(319,25,227,233,'($p1<2000 && $p2==1) || ($p1<60 && $p2==2) || ($p1<400 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 04:28:25'),
(320,25,227,228,'($p1>=2000 && $p2==1) || ($p1>=60 && $P2==2) || ($p1>=400 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 04:28:25'),
(321,25,228,229,null,null,null,10,'2020-10-27 05:49:23','2020-10-27 05:49:23'),
(322,25,229,230,'($p1>=20000 && $p2==1) || ($p1>=600 && $p2==2) || ($p1>=4000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 05:49:23'),
(323,25,229,233,'($p1<20000 && $p2==1)  || ($p1<600 && $p2==2) || ($p1<4000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 05:49:23'),
(324,25,230,231,'($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 05:49:23'),
(325,25,230,233,'($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1< 10000 && $p2==3)','getAmount,getCurrency',null,10,'2020-10-28 12:44:28','2020-10-27 05:49:23'),
(326,25,231,232,null,null,null,10,'2020-10-27 05:49:23','2020-10-27 05:49:23'),
(327,25,232,233,null,null,null,10,'2020-10-27 05:49:23','2020-10-27 05:49:23'),
(329,19,119,130,'','','语言总审核->AP Supervisor（菲律宾）',10,'2020-10-29 06:01:59','2020-10-29 05:47:35'),
(331,26,234,235,null,null,'员工提交->网点主管',10,'2020-11-04 02:28:18','2020-11-04 02:27:49'),
(332,26,235,238,'$p1<1000000','getAmount','<1000 网点主管-> 职能线Supervisor',10,'2020-11-04 02:39:30','2020-11-04 02:37:51'),
(333,26,235,236,'$p1>=1000000','getAmount','>=1000 网点主管->业务线Manager',10,'2020-11-04 02:42:49','2020-11-04 02:39:08'),
(334,26,236,238,'$p1<3000000','getAmount','< 3000  业务线Manager->职能线Supervisor',10,'2020-11-04 02:43:06','2020-11-04 02:42:04'),
(335,26,236,237,'$p1>=3000000','getAmount','>=3000 业务线Manager->业务线Senior Manager',10,'2020-11-04 02:44:55','2020-11-04 02:43:36'),
(336,26,237,238,null,null,'业务线Senior Manager->职能线Supervisor',10,'2020-11-04 02:45:40','2020-11-04 02:45:20'),
(337,26,238,243,'$p1<3000000 && $p2==1','getAmount,isFamilyOrMorale','<3000 && 是探亲或者团建费 职能线Supervisor->HRBP',10,'2020-11-04 02:48:26','2020-11-04 02:46:40'),
(338,26,238,244,'$p1<3000000 && $p2==0','getAmount,isFamilyOrMorale','<3000 && 不是探亲或者团建费 职能线Supervisor->AP（菲律宾）',10,'2020-11-04 02:49:33','2020-11-04 02:48:50'),
(339,26,238,239,'$p1>=300000','getAmount','>=3000 职能线Supervisor->职能线Manager',10,'2020-11-04 02:52:14','2020-11-04 02:50:05'),
(340,26,239,243,'$p1<5000000 && $p2==1','getAmount,isFamilyOrMorale','<5000 && 是探亲或者团建费 职能线Manager->HRBP',10,'2020-11-04 02:52:23','2020-11-04 02:51:23'),
(341,26,239,244,'$p1<5000000 && $p2==0','getAmount,isFamilyOrMorale','<5000 && 不是探亲或者团建费 职能线Manager->AP（菲律宾）',10,'2020-11-04 02:53:14','2020-11-04 02:52:38'),
(342,26,239,240,'$p1>=5000000','getAmount','>=5000  职能线Manager->一级部门负责人',10,'2020-11-04 02:55:29','2020-11-04 02:53:45'),
(343,26,240,243,'$p1<20000000 && $p2==1','getAmount,isFamilyOrMorale','<20000 && 是探亲或者团建费 一级部门负责人->HRBP',10,'2020-11-04 02:57:48','2020-11-04 02:55:47'),
(344,26,240,244,'$p1<20000000 && $p2==0','getAmount,isFamilyOrMorale','<20000 && 不是探亲或者团建费 一级部门负责人->AP（菲律宾）',10,'2020-11-04 02:58:31','2020-11-04 02:58:07'),
(345,26,240,241,'$p1>=20000000','getAmount','>=20000  一级部门负责人->公司负责人',10,'2020-11-04 02:59:33','2020-11-04 02:58:52'),
(346,26,241,243,'$p1<********* && $p2==1','getAmount,isFamilyOrMorale','<100000 && 是探亲或者团建费  公司负责人->HRBP',10,'2020-11-04 03:01:43','2020-11-04 03:00:06'),
(347,26,241,244,'$p1<********* && $p2==0','getAmount,isFamilyOrMorale','<100000 && 不是探亲或者团建费  公司负责人->AP（菲律宾）',10,'2020-11-04 03:02:25','2020-11-04 03:01:56'),
(348,26,241,242,'$p1>=*********','getAmount,isFamilyOrMorale','>=100000  公司负责人->COO',10,'2020-11-04 03:03:31','2020-11-04 03:02:44'),
(349,26,242,243,'$p1==1','isFamilyOrMorale','是探亲或者团建费  COO->HRBP',10,'2020-11-04 03:04:07','2020-11-04 03:03:44'),
(350,26,242,244,'$p1==0','isFamilyOrMorale','不是探亲或者团建费  COO->AP(菲律宾)',10,'2020-11-04 03:04:42','2020-11-04 03:04:20'),
(351,26,243,244,null,null,'HRBP->AP(菲律宾)',10,'2020-11-04 03:05:02','2020-11-04 03:04:51'),
(352,26,244,245,null,null,'AP(菲律宾)->APS(菲律宾)',10,'2020-11-04 06:09:50','2020-11-04 06:09:15'),
(353,26,245,246,'',null,'APS(菲律宾)->APS(北京)',10,'2020-11-04 06:48:08','2020-11-04 06:09:43'),
(354,26,246,252,'$p1<1000000','getAmount','<1000 APS北京->结束',10,'2020-11-04 06:48:34','2020-11-04 06:18:07'),
(355,26,246,247,'$p1>=1000000','getAmount','>=1000 APS北京->FM',10,'2020-11-04 06:50:34','2020-11-04 06:50:06'),
(356,26,247,252,'$p1<3000000','getAmount','<3000 FM->结束',10,'2020-11-04 06:51:26','2020-11-04 06:51:13'),
(357,26,247,248,'$p1>=3000000','getAmount','>=3000 FM->FSM',10,'2020-11-04 06:59:26','2020-11-04 06:51:44'),
(358,26,248,252,'$p1<5000000','getAmount','<5000 FSM->结束',10,'2020-11-04 07:00:02','2020-11-04 06:59:48'),
(359,26,248,249,'$p1>=5000000','getAmount','>=5000 FSM->FD',10,'2020-11-04 07:00:36','2020-11-04 07:00:20'),
(360,26,249,252,'$p1<20000000','getAmount','<20000 FD->结束',10,'2020-11-04 07:02:09','2020-11-04 07:00:58'),
(361,26,249,250,'$p1>=20000000','getAmount','>=20000 FD->CFO',10,'2020-11-04 07:02:45','2020-11-04 07:02:05'),
(362,26,250,252,'$p1<*********','getAmount','<100000 CFO->结束',10,'2020-11-04 07:28:05','2020-11-04 07:02:58'),
(363,26,250,251,'$p1>=*********','getAmount','>=100000 CFO->CEO',10,'2020-11-04 07:28:21','2020-11-04 07:26:57'),
(364,26,251,252,null,null,'CEO->结束',10,'2020-11-04 07:28:50','2020-11-04 07:28:36'),
(365,27,253,254,null,null,'员工提交->网点主管',10,'2020-11-04 08:31:22','2020-11-04 08:31:01'),
(366,27,254,256,'($p1<1000000 && $p2==0) ||($p1<3000000 && $p2==1)','getAmount,isFamilyOrMorale','(<1000 不是探亲费或者团建费 ) || （<3000 是探亲费或者团建费) 网点主管->supervisor',10,'2020-11-04 08:38:26','2020-11-04 08:32:00'),
(367,27,254,255,'($p1>=1000000 && $p2==0) ||($p1>=3000000 && $p2==1)','getAmount,isFamilyOrMorale','(>=1000 不是探亲费或者团建费 ) || （>=3000 是探亲费或者团建费) 网点主管->Senior Manager',10,'2020-11-04 08:38:53','2020-11-04 08:32:58'),
(368,27,255,256,null,null,'Senior Manager->supervisor',10,'2020-11-04 08:34:10','2020-11-04 08:33:47'),
(369,27,256,261,'$p1<3000000  && $p2==1','getAmount,isFamilyOrMorale','<3000 是探亲费或者团建费  supervisor->HRBP',10,'2020-11-04 08:43:23','2020-11-04 08:34:42'),
(370,27,256,262,'$p1<1000000  && $p2==0','getAmount,isFamilyOrMorale','<1000 不是探亲费或者团建费 supervisor->AP(菲律宾)',10,'2020-11-04 08:44:25','2020-11-04 08:43:58'),
(371,27,256,257,'($p1>=1000000 && $p2==0)||($p1>=3000000 && $p2==1)','getAmount,isFamilyOrMorale','(>=1000 不是探亲费或者团建费 ) || （>=3000 是探亲费或者团建费) supervisor->manager',10,'2020-11-04 08:46:09','2020-11-04 08:45:01'),
(372,27,257,261,'$p1<10000000  && $p2==1','getAmount,isFamilyOrMorale','<10000 是探亲费或者团建费  manager->HRBP',10,'2020-11-04 08:47:40','2020-11-04 08:46:22'),
(373,27,257,262,'$p1<3000000  && $p2==0','getAmount,isFamilyOrMorale','<3000 不是探亲费或者团建费  manager->AP(菲律宾)',10,'2020-11-04 08:48:53','2020-11-04 08:47:52'),
(374,27,257,258,'($p1>= 3000000 && $p2==0)||($p1>= 10000000 && $p2==1)','getAmount,isFamilyOrMorale','(>=3000 不是探亲费或者团建费 ) || （>=10000 是探亲费或者团建费) manager->一级部门负责人',10,'2020-11-04 08:50:31','2020-11-04 08:49:08'),
(375,27,258,261,'$p1<50000000  && $p2==1','getAmount,isFamilyOrMorale','<50000 是探亲费或者团建费  一级部门负责人->HRBP',10,'2020-11-04 08:52:04','2020-11-04 08:51:10'),
(376,27,258,262,'$p1<10000000  && $p2==0','getAmount,isFamilyOrMorale','<10000 不是探亲费或者团建费  一级部门负责人->AP(菲律宾)',10,'2020-11-04 09:04:07','2020-11-04 08:52:14'),
(377,27,258,259,'($p1>= 10000000 && $p2==0)||($p1>= 50000000 && $p2==1)','getAmount,isFamilyOrMorale','(>= 10000 不是探亲费或者团建费 ) || （>= 50000 是探亲费或者团建费) 一级部门负责人->所属公司负责人',10,'2020-11-04 09:17:07','2020-11-04 09:07:45'),
(378,27,259,261,'$p1<*********  && $p2==1','getAmount,isFamilyOrMorale','<100000 是探亲费或者团建费  公司负责人->HRBP',10,'2020-11-04 10:03:13','2020-11-04 10:01:24'),
(379,27,259,262,'$p1<50000000  && $p2==0','getAmount,isFamilyOrMorale','<50000 不是探亲费或者团建费  公司负责人->AP(菲律宾)',10,'2020-11-04 10:04:38','2020-11-04 10:03:28'),
(380,27,259,260,'($p1>= 50000000 && $p2==0)||($p1>= ********* && $p2==1)','getAmount,isFamilyOrMorale','(>= 50000 不是探亲费或者团建费 ) || （>= 100000 是探亲费或者团建费) 所属公司负责人->COO',10,'2020-11-04 10:07:52','2020-11-04 10:06:48'),
(381,27,260,261,'$p1==1','isFamilyOrMorale','是探亲费或者团建费 COO->HRBP',10,'2020-11-04 10:08:59','2020-11-04 10:08:15'),
(382,27,260,262,'$p1==0','isFamilyOrMorale','不是探亲费或者团建费      COO->AP(菲律宾)',10,'2020-11-04 10:10:12','2020-11-04 10:09:14'),
(383,27,261,262,null,null,'HRBP->AP(菲律宾)',10,'2020-11-04 10:10:29','2020-11-04 10:10:01'),
(384,27,262,263,null,null,'AP(菲律宾)->APS(菲律宾)',10,'2020-11-04 10:11:13','2020-11-04 10:10:57'),
(385,27,263,264,null,null,'APS(菲律宾)->APS(北京)',10,'2020-11-04 10:11:41','2020-11-04 10:11:27'),
(386,27,264,270,'$p1<1000000 && $p2==0','getAmount,isFamilyOrMorale','APS(北京)->结束',10,'2020-11-04 10:22:32','2020-11-04 10:11:49'),
(387,27,264,265,null,null,'APS(北京)->FM',10,'2020-11-04 10:20:40','2020-11-04 10:20:21'),
(388,27,265,270,'$p1<3000000','getAmount','<3000 FM->结束（小于3000，两个都结束）',10,'2020-11-04 10:23:37','2020-11-04 10:20:52'),
(389,27,265,266,'$p1>=3000000','getAmount','>=3000 FM->FSM',10,'2020-11-04 10:24:22','2020-11-04 10:24:05'),
(390,27,266,270,'$p1<10000000','getAmount','<10000 FSM->结束',10,'2020-11-04 10:25:47','2020-11-04 10:24:45'),
(391,27,266,267,'$p1>=10000000','getAmount','>=10000 FSM->FD',10,'2020-11-04 11:25:19','2020-11-04 10:26:04'),
(392,27,267,270,'$p1<50000000','getAmount','<50000 FD->结束',10,'2020-11-04 11:29:04','2020-11-04 11:28:29'),
(393,27,267,268,'$p1>=50000000','getAmount','>=50000 FD->CFO',10,'2020-11-04 11:29:53','2020-11-04 11:29:30'),
(394,27,268,270,'$p1<********* && $p2==1','getAmount,isFamilyOrMorale','<100,000 是探亲费或者团建费 COO->结束',10,'2020-11-04 11:31:58','2020-11-04 11:30:44'),
(395,27,268,269,null,null,'COO->CEO',10,'2020-11-04 11:32:37','2020-11-04 11:32:07'),
(396,27,269,270,null,null,'CEO->结束',10,'2020-11-04 11:32:44','2020-11-04 11:32:34'),
(397,28,271,272,null,null,'员工提交->直线上级',10,'2020-11-04 11:50:55','2020-11-04 11:50:32'),
(398,28,272,274,'($p1<1000000 && $p2==0) || ($p1<3000000 && $p2==1)','getAmount,isFamilyOrMorale','(<1000 不是探亲费或者团建费 ) || （<3000 是探亲费或者团建费)直接上级->Supervisor',10,'2020-11-04 11:53:13','2020-11-04 11:52:26'),
(399,28,272,273,'($p1>=1000000 && $p2==0) || ($p1>=3000000 && $p2==1)','getAmount,isFamilyOrMorale','(>=1000 不是探亲费或者团建费 ) || （>=3000 是探亲费或者团建费) 直接上级->网点主管',10,'2020-11-04 11:54:23','2020-11-04 11:53:40'),
(400,28,273,274,null,null,'网点主管->Supervisor',10,'2020-11-04 11:55:03','2020-11-04 11:54:43'),
(401,28,274,276,'($p1<1000000 && $p2==0) || ($p1<3000000 && $p2==1)','getAmount,isFamilyOrMorale','(<1000 不是探亲费或者团建费 ) || （<3000 是探亲费或者团建费) Supervisor->一级部门负责人',10,'2020-11-04 11:56:01','2020-11-04 11:55:27'),
(402,28,274,275,'($p1>=1000000 && $p2==0) || ($p1>=3000000 && $p2==1)','getAmount,isFamilyOrMorale','(>=1000 不是探亲费或者团建费 ) || （>=3000 是探亲费或者团建费) Supervisor->manager',10,'2020-11-04 11:56:55','2020-11-04 11:56:16'),
(403,28,275,276,null,null,'manager->一级部门负责人',10,'2020-11-04 11:57:26','2020-11-04 11:57:10'),
(404,28,276,280,'$p1<10000000 && $p2==0','getAmount,isFamilyOrMorale','<10000 不是探亲费或者团建费,一级部门负责人->AP（菲律宾）',10,'2020-11-04 12:05:34','2020-11-04 11:57:55'),
(405,28,276,279,'$p1<50000000 && $p2==1','getAmount,isFamilyOrMorale','<50000 是探亲费或者团建费,一级部门负责人->HRBP',10,'2020-11-04 12:01:29','2020-11-04 11:59:44'),
(406,28,276,277,null,null,'一级部门负责人->所属公司负责人',10,'2020-11-04 12:00:44','2020-11-04 12:00:23'),
(407,28,277,280,'$p1<50000000 && $p2==0','getAmount,isFamilyOrMorale','<50000 不是探亲费或者团建费,所属公司负责人->AP(菲律宾)',10,'2020-11-04 12:05:44','2020-11-04 12:01:01'),
(408,28,277,279,'$p1<********* && $p2==1','getAmount,isFamilyOrMorale','<100000 是探亲费或者团建费,所属公司负责人->HRBP',10,'2020-11-04 12:02:42','2020-11-04 12:01:51'),
(409,28,277,278,null,null,'所属公司负责人->COO',10,'2020-11-04 12:03:17','2020-11-04 12:02:57'),
(410,28,278,280,'$p1==0','isFamilyOrMorale','COO->AP(菲律宾)',10,'2020-11-04 12:05:51','2020-11-04 12:03:41'),
(411,28,278,279,'$p2==1','isFamilyOrMorale','COO->HRBP',10,'2020-11-04 12:04:23','2020-11-04 12:04:10'),
(412,28,279,280,null,null,'HRBP->AP(菲律宾)',10,'2020-11-04 12:05:56','2020-11-04 12:04:47'),
(413,28,280,281,null,null,'AP(菲律宾)->APS(菲律宾)',10,'2020-11-04 12:06:07','2020-11-04 12:05:06'),
(414,28,281,282,null,null,'APS(菲律宾)->APS(北京)',10,'2020-11-04 12:16:38','2020-11-04 12:16:26'),
(415,28,282,288,'$p1<1000000 && $p2==0','getAmount,isFamilyOrMorale','APS(北京)->结束',10,'2020-11-04 12:17:37','2020-11-04 12:17:02'),
(416,28,282,283,null,null,'APS(北京)->FM',10,'2020-11-04 12:18:08','2020-11-04 12:17:55'),
(417,28,283,288,'$p1<3000000','getAmount','FM->结束',10,'2020-11-04 12:19:19','2020-11-04 12:18:36'),
(418,28,283,284,null,null,'FM->FSM',10,'2020-11-04 12:19:50','2020-11-04 12:19:29'),
(419,28,284,288,'$p1<10000000','getAmount','FSM->结束',10,'2020-11-04 12:21:01','2020-11-04 12:20:41'),
(420,28,284,285,null,null,'FSM->FD',10,'2020-11-04 12:21:24','2020-11-04 12:21:17'),
(421,28,285,288,'$p1<50000000','getAmount','FD->结束',10,'2020-11-04 12:22:04','2020-11-04 12:21:35'),
(422,28,285,286,null,null,'FD->CFO',10,'2020-11-04 12:22:22','2020-11-04 12:22:15'),
(423,28,286,288,'$p1<********* && $p2==1','getAmount,isFamilyOrMorale','<100000 是探亲费或者团建费,CFO->结束',10,'2020-11-04 12:23:34','2020-11-04 12:22:50'),
(424,28,286,287,null,null,'CFO->CEO',10,'2020-11-04 12:23:53','2020-11-04 12:23:47'),
(425,28,287,288,null,null,'CEO->结束',10,'2020-11-04 12:24:12','2020-11-04 12:24:05'),
(426,29,290,291,null,null,'员工提交->直接上级',10,'2020-11-05 02:27:32','2020-11-05 02:27:21'),
(427,29,291,292,null,null,'直接上级->二级部门负责人',10,'2020-11-05 02:28:09','2020-11-05 02:27:57'),
(428,29,292,293,'$p1==25 || $p1==18','getSubmitterDepartment','hub&flash home部门，二级部门负责人->一级部门负责人',10,'2020-11-05 02:28:53','2020-11-05 02:28:23'),
(429,29,292,297,'$p1<2000000 && $p2==0','getAmount,isFamilyOrMorale','<2000 不是探亲费 二级部门负责人->AP(菲律宾)',10,'2020-11-05 05:58:41','2020-11-05 02:30:44'),
(430,29,292,296,'$p1<2000000 && $p2==1','getAmount,isFamilyOrMorale','<2000 是探亲费 二级部门负责人->HRBP',10,'2020-11-05 05:58:58','2020-11-05 05:57:51'),
(431,29,292,293,null,null,'二级部门负责人->一级部门负责人',10,'2020-11-05 05:59:33','2020-11-05 05:59:14'),
(432,29,293,297,'$p1<10000000 && $p2==0','getAmount,isFamilyOrMorale','<10000 不是探亲费 一级部门负责人->AP(菲律宾)',10,'2020-11-05 06:01:37','2020-11-05 06:00:27'),
(433,29,293,296,'$p1<10000000 && $p2==1','getAmount,isFamilyOrMorale','<10000 是探亲费 一级部门负责人->HRBP',10,'2020-11-05 06:02:02','2020-11-05 06:01:31'),
(434,29,293,294,null,null,'一级部门负责人->公司负责人',10,'2020-11-05 06:02:32','2020-11-05 06:02:20'),
(435,29,294,297,'$p1<50000000 && $p2==0','getAmount,isFamilyOrMorale','<50000 不是探亲费 公司负责人->AP(菲律宾)',10,'2020-11-05 06:04:27','2020-11-05 06:03:39'),
(436,29,294,296,'$p1<50000000 && $p2==1','getAmount,isFamilyOrMorale','<50000 是探亲费 公司负责人->HRBP',10,'2020-11-05 06:05:08','2020-11-05 06:04:44'),
(437,29,294,295,null,null,'公司负责人->COO/CPO',10,'2020-11-05 06:05:55','2020-11-05 06:05:21'),
(438,29,295,297,'$p1==0','isFamilyOrMorale','不是探亲费->AP(菲律宾)',10,'2020-11-05 06:06:46','2020-11-05 06:06:17'),
(439,29,295,296,'$p1==1','isFamilyOrMorale','探亲费->HRBP',10,'2020-11-05 06:07:15','2020-11-05 06:07:00'),
(440,29,296,297,null,null,'HRBP->AP(菲律宾)',10,'2020-11-05 06:07:37','2020-11-05 06:07:27'),
(441,29,297,298,null,null,'AP(菲律宾)->APS(菲律宾)',10,'2020-11-05 06:08:36','2020-11-05 06:08:15'),
(442,29,298,299,null,null,'APS(菲律宾)->APS(北京)',10,'2020-11-05 06:09:14','2020-11-05 06:08:59'),
(443,29,299,305,'$p1<2000000','getAmount','APS(北京)->结束',10,'2020-11-05 06:10:40','2020-11-05 06:10:05'),
(444,29,299,300,'$p1>=2000000','getAmount','APS(北京)->FM',10,'2020-11-05 06:11:28','2020-11-05 06:10:50'),
(445,29,300,301,null,null,'FM->FSM',10,'2020-11-05 06:11:49','2020-11-05 06:11:43'),
(446,29,301,305,'$p1<10000000','getAmount','FSM->结束',10,'2020-11-05 06:12:24','2020-11-05 06:12:13'),
(447,29,301,302,'$p1>=10000000','getAmount','FSM->FD',10,'2020-11-05 06:13:00','2020-11-05 06:12:42'),
(448,29,302,305,'$p1<50000000','getAmount','FD->结束',10,'2020-11-05 06:13:49','2020-11-05 06:13:13'),
(449,29,302,303,'$p1>=50000000','getAmount','FD->CFO',10,'2020-11-05 06:14:36','2020-11-05 06:14:19'),
(450,29,303,304,null,null,'CFO->CEO',10,'2020-11-05 06:14:59','2020-11-05 06:14:52'),
(451,29,304,305,null,null,'CEO->结束',10,'2020-11-05 06:15:18','2020-11-05 06:15:08'),
(452,30,720,307,null,null,'APS(北京)->直接上级',10,'2021-05-12 13:08:51','2020-11-05 06:35:47'),
(453,30,307,308,null,null,'直接上级->二级部门负责人',10,'2020-11-05 06:37:32','2020-11-05 06:36:08'),
(454,30,308,309,'$p1==25 || $p1==18','getSubmitterDepartment','hub&flash home部门，二级部门负责人->一级部门负责人',10,'2020-11-05 06:39:03','2020-11-05 06:38:09'),
(455,30,308,312,'($p1<50000000&&$p2==1)||($p3<1576790&&$p2==2)||($p4<11037530&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((<50,000 and 泰铢)||(<1,576.79 and 美元)||(< 11,037.53 and 人民币))   二级负责人->PM',10,'2020-11-05 06:47:40','2020-11-05 06:39:44'),
(456,30,308,309,'($p1>=50000000&&$p2==1)||($p3>=1576790&&$p2==2)||($p4>=11037530&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((>=50,000 and 泰铢)||(>=1,576.79 and 美元)||(>=11,037.53 and 人民币))   二级负责人->一级负责人',10,'2020-11-05 06:49:23','2020-11-05 06:48:09'),
(457,30,309,312,'($p1<*********&&$p2==1)||($p3<3153580&&$p2==2)||($p4<22075060&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((<100,000 and 泰铢)||(<3153.58 and 美元)||(< 22075.06 and 人民币))   一级负责人->PM',10,'2020-11-05 06:52:57','2020-11-05 06:49:44'),
(458,30,309,310,'($p1>=*********&&$p2==1)||($p3>=3153580&&$p2==2)||($p4>=22075060&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((>=100,000 and 泰铢)||(>=3153.58 and 美元)||(>=22075.06 and 人民币))   一级负责人->所属公司负责人',10,'2020-11-05 06:55:17','2020-11-05 06:53:19'),
(459,30,310,312,'($p1<500000000&&$p2==1)||($p3<16000000&&$p2==2)||($p4<110000000&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((<500,000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币))   所属公司负责人->PM',10,'2020-11-05 06:56:58','2020-11-05 06:55:35'),
(460,30,310,311,'($p1>=500000000&&$p2==1)||($p3>=16000000&&$p2==2)||($p4>=110000000&&$p2==3)',null,'((<500,000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币))   所属公司负责人->COO/CPO',10,'2020-11-05 06:58:41','2020-11-05 06:58:14'),
(461,30,311,312,null,null,'COO/CPO->PM',10,'2020-11-05 06:59:02','2020-11-05 06:58:51'),
(462,30,312,313,null,null,'PM->PSM',10,'2020-11-05 06:59:24','2020-11-05 06:59:13'),
(463,30,313,316,'($p1<*********&&$p2==1)||($p3<3153580&&$p2==2)||($p4<22075060&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((<100,000 and 泰铢)||(<3153.58 and 美元)||(< 22075.06 and 人民币))   PSM->结束',10,'2020-11-05 07:00:15','2020-11-05 06:59:40'),
(464,30,313,314,'($p1>=*********&&$p2==1)||($p3>=3153580&&$p2==2)||($p4>=22075060&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((>=100,000 and 泰铢)||(>=3153.58 and 美元)||(>=22075.06 and 人民币))   PSM->PD',10,'2020-11-05 07:01:11','2020-11-05 07:00:25'),
(465,30,314,316,'($p1<500000000&&$p2==1)||($p3<16000000&&$p2==2)||($p4<110000000&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','((<500,000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币))   PD->结束',10,'2020-11-05 07:02:01','2020-11-05 07:01:35'),
(466,30,314,315,'($p1>=500000000&&$p2==1)||($p3>=16000000&&$p2==2)||($p4>=110000000&&$p2==3)',null,'((>=500,000 and 泰铢)||(>=16000 and 美元)||(>= 110000 and 人民币))   PD->CFO',10,'2020-11-05 07:02:43','2020-11-05 07:02:10'),
(467,30,315,316,null,null,'CFO->结束',10,'2020-11-05 07:03:00','2020-11-05 07:02:52'),
(468,31,317,318,null,null,'员工提交->直接上级',10,'2020-11-05 07:25:14','2020-11-05 07:21:36'),
(469,31,318,319,null,null,'直接上级->二级部门负责人',10,'2020-11-05 07:25:39','2020-11-05 07:25:27'),
(470,31,319,320,null,null,'二级部门负责人->一级部门负责人',10,'2020-11-05 07:26:09','2020-11-05 07:25:47'),
(471,31,320,323,'$p1<10000000','getAmount','<10000 一级部门负责人->FM',10,'2020-11-05 07:35:24','2020-11-05 07:26:37'),
(472,31,320,321,'$p1>=10000000','getAmount','>=10000 一级部门负责人->所属公司负责人',10,'2020-11-05 07:35:30','2020-11-05 07:29:48'),
(473,31,321,323,'$p1<50000000','getAmount','<50000 所属公司负责人->FM',10,'2020-11-05 07:35:47','2020-11-05 07:30:20'),
(474,31,321,322,'$p1>=50000000','getAmount','>=50000 所属公司负责人->COO/CPO',10,'2020-11-05 07:35:54','2020-11-05 07:31:02'),
(475,31,322,323,null,null,'COO/CPO->FM',10,'2020-11-05 07:34:23','2020-11-05 07:31:37'),
(476,31,323,324,null,null,'FM->FSM',10,'2020-11-05 07:39:07','2020-11-05 07:34:07'),
(477,31,324,328,'$p1<10000000','getAmount','<10000 FSM->结束',10,'2020-11-05 07:39:10','2020-11-05 07:34:47'),
(478,31,324,325,'$p1>=10000000','getAmount','>=10000 FSM->FD',10,'2020-11-05 07:39:13','2020-11-05 07:36:33'),
(479,31,325,328,'$p1<50000000','getAmount','<50000 FD->结束',10,'2020-11-05 07:39:16','2020-11-05 07:37:17'),
(480,31,325,326,'$p1>=50000000','getAmount','>=50000 FD->CFO',10,'2020-11-05 07:39:20','2020-11-05 07:37:54'),
(481,31,326,328,null,null,'CFO->结束',10,'2020-11-05 07:39:22','2020-11-05 07:38:34'),
(482,32,329,330,null,null,'员工提交->直线上级',10,'2020-11-05 08:09:03','2020-11-05 08:08:37'),
(483,32,330,331,'',null,'直接上级->二级部门负责人',10,'2020-11-05 08:15:07','2020-11-05 08:09:19'),
(484,32,331,332,'(($p1>=50000000 && $p2==1) || ($p1>=1576790 && $p2==2)||($p1>=11037530 && $p2==3)) || ($p3==25 || $p3==18)','getAmount,getCurrency,getSubmitterDepartment','（（>=50000泰铢）|| >=1,576.79 美元||>=11,037.53人民币）|| （提交部门是hub或者flashhome）二级部门负责人->一级部门负责人',10,'2020-11-05 08:48:18','2020-11-05 08:09:50'),
(485,32,331,335,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'th\'','getAmount,getCurrency,getSubmitterDepartment,getLang','（（<50000泰铢）|| <1,576.79 美元||<111,037.53人民币）&&提交部门不是hub&&不是flashhome）&&泰语    二级部门负责人->法务部门1=58040',10,'2020-11-07 11:33:54','2020-11-05 08:35:16'),
(486,32,331,337,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'en\'','getAmount,getCurrency,getSubmitterDepartment,getLang','（（<50000泰铢）|| <1,576.79 美元||<11,037.53人民币）&&提交部门不是hub&&不是flashhome）&&英语    二级部门负责人->法务部门1=56883',10,'2020-11-05 08:48:24','2020-11-05 08:38:02'),
(487,32,331,338,'(($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3))&& $p3!=25 && $p3!=18 && $p4==\'zh-CN\'','getAmount,getCurrency,getSubmitterDepartment,getLang','（（<50000泰铢）|| <1,576.79 美元||<11,037.53人民币）&&提交部门不是hub&&不是flashhome）&&中文    二级部门负责人->法务部门1=57335',10,'2020-11-05 08:48:30','2020-11-05 08:39:55'),
(488,32,332,333,'($p1>=********* && $p2==1) || ($p1>=6443300 && $p2==2)||($p1>=42841680 && $p2==3)','getAmount,getCurrency','（>=200000泰铢）|| >=6,443.30 美元||>=42,841.68人民币  一级部门负责人->所属公司负责人',10,'2020-11-11 01:52:06','2020-11-05 08:45:05'),
(489,32,332,335,'(($p1<********* && $p2==1) || ($p1<6443300 && $p2==2) || ($p1<42841680 && $p2==3)) && $p3==\'th\'','getAmount,getCurrency,getLang','（（<200000泰铢）|| <6,443.30 美元||<42,841.68人民币&&泰语     一级部门负责人->法务部门1=58040',10,'2020-11-11 01:52:10','2020-11-05 08:50:31'),
(490,32,332,337,'(($p1<********* && $p2==1) || ($p1<6443300 && $p2==2) || ($p1<42841680 && $p2==3)) && $p3==\'en\'','getAmount,getCurrency,getLang','（（<200000泰铢）|| <6,443.30 美元||<42,841.68人民币&&英语    一级部门负责人->法务部门1=56883',10,'2020-11-11 01:52:24','2020-11-05 08:56:54'),
(491,32,332,338,'(($p1<********* && $p2==1) || ($p1<6443300 && $p2==2) || ($p1<42841680 && $p2==3)) && $p3==\'zh-CN\'','getAmount,getCurrency,getLang','（（<200000泰铢）|| <6,443.30 美元||<42,841.68人民币&&中文    一级部门负责人->法务部门1=57335',10,'2020-11-11 01:52:25','2020-11-05 08:58:10'),
(492,32,333,334,'($p1>=*********0 && $p2==1) || ($p1>= 31535800 && $p2==2)||($p1>= 220750600 && $p2==3)','getAmount,getCurrency','（>=1000000泰铢）|| >=31,535.80 美元||>=220,750.60人民币  所属公司负责人->COO/CPO',10,'2020-11-11 01:55:46','2020-11-05 08:59:38'),
(493,32,333,335,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'th\'','getAmount,getCurrency,getLang','（（<1000000泰铢）|| <31,535.80 美元||<220,750.60人民币&&泰语     所属公司负责人->法务部门1=58040',10,'2020-11-11 01:54:20','2020-11-05 09:03:12'),
(494,32,333,337,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'en\'','getAmount,getCurrency,getLang','（（<1000000泰铢）|| <31,535.80 美元||<220,750.60人民币&&英语     所属公司负责人->法务部门1= 56883',10,'2020-11-11 01:54:22','2020-11-05 09:05:21'),
(495,32,333,338,'(($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)) && $p3==\'zh-CN\'','getAmount,getCurrency,getLang','（（<1000000泰铢）|| <31,535.80 美元||<220,750.60人民币&&中文     所属公司负责人->法务部门1= 57335',10,'2020-11-11 01:54:23','2020-11-05 09:05:51'),
(496,32,334,335,'$p1==\'th\'','getLang','COO/CPO->法务部门1=58040',10,'2020-11-07 11:35:00','2020-11-05 09:07:29'),
(497,32,334,337,'$p1==\'en\'','getLang','COO/CPO->法务部门1=56883',10,'2020-11-05 09:08:37','2020-11-05 09:08:19'),
(498,32,334,338,'$p1==\'zh-CN\'','getLang','COO/CPO->法务部门1=57335',10,'2020-11-05 09:09:23','2020-11-05 09:09:02'),
(499,32,335,338,null,null,'法务部门1泰语=58040->法务部门2泰语=58040(跳过)->57335',10,'2020-11-07 11:36:31','2020-11-05 09:10:38'),
(501,32,337,338,null,null,'法务部门1英语=56883->法务部门2英语=57335',10,'2020-11-05 09:14:53','2020-11-05 09:14:04'),
(502,32,338,339,null,null,'57335->AP(菲律宾)',10,'2020-11-05 09:18:20','2020-11-05 09:16:54'),
(503,32,339,340,null,null,'AP(菲律宾)->APS(北京)',10,'2020-11-05 09:18:39','2020-11-05 09:18:28'),
(504,32,340,346,'($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3)','getAmount,getCurrency','APS(北京)->结束',10,'2020-11-05 09:23:22','2020-11-05 09:21:56'),
(505,32,340,341,null,null,'APS(北京)->FM',10,'2020-11-05 09:23:45','2020-11-05 09:23:32'),
(506,32,341,342,null,null,'FM->FSM',10,'2020-11-05 09:24:16','2020-11-05 09:23:55'),
(507,32,342,346,'($p1<********* && $p2==1) || ($p1<6443300 && $p2==2) || ($p1<42841680 && $p2==3)','getAmount,getCurrency','FSM->结束',10,'2020-11-11 01:48:56','2020-11-05 09:24:30'),
(508,32,342,343,null,null,'FSM->FD',10,'2020-11-05 09:25:42','2020-11-05 09:25:07'),
(509,32,343,346,'($p1<*********0 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3)','getAmount,getCurrency','FD->结束',10,'2020-11-10 04:22:52','2020-11-05 09:25:55'),
(510,32,343,344,null,null,'FD->CFO',10,'2020-11-05 09:26:38','2020-11-05 09:26:32'),
(511,32,344,345,null,null,'CFO->CEO',10,'2020-11-05 09:26:52','2020-11-05 09:26:47'),
(512,32,345,346,null,null,'CEO->结束',10,'2020-11-05 09:27:09','2020-11-05 09:26:59'),

(547,34,367,368,null,null,'员工提交->PM',10,'2020-11-19 06:28:26','2020-11-19 06:27:32'),
(548,34,368,369,null,null,'PM->PSM',10,'2020-11-19 06:29:00','2020-11-19 06:28:36'),
(549,34,369,370,null,null,'PSM->AP(菲律宾)',10,'2020-11-19 06:29:29','2020-11-19 06:29:17'),
(550,34,370,371,null,null,'AP(菲律宾)->APS(菲律宾)',10,'2020-11-19 06:29:57','2020-11-19 06:29:43'),
(551,34,371,372,null,null,'APS(菲律宾)->APS(北京)',10,'2020-11-19 06:30:52','2020-11-19 06:30:34'),
(552,34,372,373,null,null,'APS(北京)->FM',10,'2020-11-19 06:31:16','2020-11-19 06:31:04'),
(553,34,373,378,'($p1<50000000&&$p2==1)||($p3<1576790&&$p2==2)||($p4<11037530&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','FM->结束 (<50,000 and 泰铢)||(<1,576.79 and 美元)||(< 11,037.53 and 人民币)',10,'2020-11-19 06:36:19','2020-11-19 06:31:49'),
(554,34,373,374,null,null,'FM->FSM',10,'2020-11-19 06:35:00','2020-11-19 06:34:47'),
(555,34,374,378,'($p1<300000000&&$p2==1)||($p3<9460740&&$p2==2)||($p4<66225170&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','FSM->结束(<300000 and 泰铢)||(<9,460.74 and 美元)||(< 66225.17 and 人民币)',10,'2020-11-19 06:36:50','2020-11-19 06:35:19'),
(556,34,374,375,null,null,'FSM->FD',10,'2020-11-19 06:38:49','2020-11-19 06:38:42'),
(557,34,375,378,'($p1<500000000&&$p2==1)||($p3<16000000&&$p2==2)||($p4<110000000&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','FD->结束(<500000 and 泰铢)||(<16000 and 美元)||(< 110000 and 人民币)',10,'2020-11-19 06:39:54','2020-11-19 06:39:10'),
(558,34,375,376,null,null,'FD->CFO',10,'2020-11-19 06:40:20','2020-11-19 06:40:13'),
(559,34,376,377,null,null,'CFO->CEO',10,'2020-11-19 06:40:37','2020-11-19 06:40:28'),
(560,34,377,378,null,null,'CEO->结束',10,'2020-11-19 06:40:54','2020-11-19 06:40:45'),

(582,36,394,395,null,null,'员工提交->直线上级',10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(583,36,395,396,null,null,'直线上级->二级部门负责人',10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(584,36,396,400,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(585,36,396,397,'$p1>=2000000','getAmount','>=2000  二级部门负责人->一级部门负责人',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(586,36,397,398,'$p1>=10000000','getAmount','>=10000 一级部门负责人->所属公司负责人',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(587,36,398,399,'$p1>=50000000','getAmount','>=50000 所属公司负责人->组织负责人',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(588,36,399,400,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(589,36,398,400,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(590,36,397,400,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(591,36,400,401,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(592,36,401,402,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(593,36,402,403,'$p1>=2000000','getAmount','>=2000  aps(北京)->指定工号(FM)',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(594,36,403,404,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(595,36,404,405,'$p1>=10000000','getAmount','>=10000  指定工号(FSM)->指定工号(FD)',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(596,36,405,406,'$p1>=50000000','getAmount','>=50000  指定工号(AVP/VFO)->指定工号(CEO)',20,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(597,36,406,407,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(598,36,407,408,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(599,36,405,408,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(600,36,404,408,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(601,36,402,408,null,null,null,10,'2021-01-20 13:59:03','2021-01-20 13:59:03'),
(602,37,409,410,null,null,'员工提交 -> PM',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(603,37,410,411,null,null,'PM -> PSM',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(604,37,411,412,null,null,'PSM -> AP',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(605,37,412,413,null,null,'AP -> APS(菲律宾)',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(606,37,413,414,null,null,'APS(菲律宾) -> APS(北京)',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(607,37,414,415,null,null,'APS(北京) -> FM',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(608,37,415,416,'$p1 >= 50000000','getAmount','FM -> FSM',20,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(609,37,415,420,null,null,'FM -> 结束',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(610,37,416,417,'$p1 >= 300000000','getAmount','FSM -> FD',20,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(611,37,416,420,null,null,'FSM -> 结束',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(612,37,417,418,'$p1 >= 500000000','getAmount','FD -> CFO',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(613,37,417,420,null,null,'FD -> 结束',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(614,37,418,419,null,null,'CFO -> CEO',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),
(615,37,419,420,null,null,'CEO -> 结束',10,'2021-01-27 11:40:15','2021-01-27 11:40:15'),

(616, 38, 421, 422, '', '', '员工提交->直接上级', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(617, 38, 422, 423, '', '', '直接上级->二级部门负责人', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(620, 38, 423, 424, '', '', '二级部门负责人->一级部门负责人', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(621, 38, 424, 427, '$p1 < 75000', 'getTotalAmount', '金额 < 75000, 一级部门负责人->AP(菲律宾)', 10, '2021-06-07 08:44:15', '2021-01-28 06:07:11'),
(622, 38, 424, 425, '', '', '一级部门负责人->所属公司负责人', 9, '2021-06-07 07:42:34', '2021-01-28 06:07:11'),
(623, 38, 425, 427, '$p1 < 300000', 'getTotalAmount', '金额 < 300000, 所属公司负责人->AP(菲律宾)', 10, '2021-06-07 08:44:17', '2021-01-28 06:07:11'),
(624, 38, 425, 426, '', '', '所属公司负责人->COO/CPO', 9, '2021-06-07 07:42:42', '2021-01-28 06:07:11'),
(625, 38, 426, 427, '', '', 'COO/CPO -> AP(菲律宾)', 10, '2021-06-07 08:44:04', '2021-01-28 06:07:11'),
(626, 38, 427, 428, '', '', 'AP(菲律宾)->AP Supervisor(菲律宾)', 10, '2021-06-07 08:44:08', '2021-01-28 06:07:11'),
(627, 38, 428, 429, '', '', 'AP Supervisor(菲律宾)->AP Supervisor(北京)', 10, '2021-06-07 08:44:26', '2021-01-28 06:07:11'),
(628, 38, 429, 435, '$p1 < 30000', 'getTotalAmount', '金额 < 30000, AP Supervisor(北京) -> 结束', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(629, 38, 429, 430, '', '', 'AP Supervisor（北京）-> Finance Manager', 9, '2021-06-07 07:42:49', '2021-01-28 06:07:11'),
(630, 38, 430, 431, '', '', 'Finance Manager->Finance Senior Manager', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(631, 38, 431, 435, '$p1 < 75000', 'getTotalAmount', '金额 < 75000, Finance Senior Manager->结束', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(632, 38, 431, 432, '', '', 'Finance Senior Manager -> Finance Director', 9, '2021-06-07 07:43:13', '2021-01-28 06:07:11'),
(633, 38, 432, 435, '$p1 < 300000', 'getTotalAmount', '金额 < 300000, Finance Director -> 结束', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(634, 38, 432, 433, '', '', 'Finance Director -> CFO', 9, '2021-06-07 07:43:17', '2021-01-28 06:07:11'),
(635, 38, 433, 434, '', '', 'CFO -> CEO', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),
(636, 38, 434, 435, '', '', 'CEO -> 结束', 10, '2021-01-28 06:07:11', '2021-01-28 06:07:11'),

(657,39,436,437,null,null,'员工提交->直线上级',10,'2021-02-05 09:42:36','2021-02-05 09:42:36'),
(658,39,437,438,null,null,'直线上级->二级部门负责人',10,'2021-02-05 09:43:07','2021-02-05 09:43:07'),
(659,39,438,439,'$p1==25 || $p1==18','getSubmitterDepartment','申请人一级部门为 hub（25）或 flash home（18）的，二级部门负责人->一级部门负责人',10,'2021-02-05 09:45:35','2021-02-05 09:44:28'),
(660,39,438,444,'$p1 != 1 && $p2 < 2000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 2000: 二级部门负责人->AP（菲律宾）',10,'2021-02-20 07:39:39','2021-02-05 09:50:11'),
(661,39,438,442,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 10000: 二级部门负责->HRBP Director',10,'2021-02-20 07:38:03','2021-02-05 09:50:53'),
(662,39,438,439,null,null,'其他情况：二级部门负责人->一级部门负责人',10,'2021-02-05 09:51:54','2021-02-05 09:51:29'),
(663,39,439,444,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 10000: 一级部门负责人->AP（菲律宾）',10,'2021-02-20 07:40:08','2021-02-05 09:57:32'),
(664,39,439,442,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 100000：一级部门负责人->HRBP Director',10,'2021-02-20 07:38:11','2021-02-05 10:02:07'),
(665,39,439,440,null,null,'其他情况：一级部门负责人->所属公司负责人',10,'2021-02-05 10:05:15','2021-02-05 10:03:45'),
(666,39,440,444,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 50000: 所属公司负责人->AP（菲律宾）',10,'2021-02-20 07:41:40','2021-02-05 10:07:38'),
(667,39,440,442,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 200000: 所属公司负责人->HRBP Director',10,'2021-02-20 07:38:18','2021-02-05 10:10:50'),
(668,39,440,441,null,null,'其他情况：所属公司负责人->所属组织负责人',10,'2021-02-05 10:12:28','2021-02-05 10:12:01'),
(669,39,441,444,'$p1 != 1 && $p2 >= 50000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 >= 50000: 所属组织负责人->AP（菲律宾）',10,'2021-02-20 07:41:54','2021-02-05 10:14:29'),
(670,39,441,442,null,null,'所属组织负责人->HRBP Director',10,'2021-02-05 10:16:26','2021-02-05 10:16:26'),
(671,39,442,444,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 100000: HRBP Director->AP（菲律宾）',10,'2021-02-20 07:43:33','2021-02-05 10:18:13'),
(672,39,442,443,null,null,'HRBP Director->CPO',10,'2021-02-20 07:43:59','2021-02-05 10:19:46'),
(673,39,443,444,null,null,'CPO->AP（菲律宾）',10,'2021-02-05 10:21:15','2021-02-05 10:21:15'),
(674,39,444,445,null,null,'AP（菲律宾）-> AP Supervisor（菲律宾）',10,'2021-02-05 10:22:07','2021-02-05 10:22:07'),
(675,39,445,446,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-02-05 10:22:49','2021-02-05 10:22:49'),
(676,39,446,452,'$p1 != 1 && $p2 < 2000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 2000: AP Supervisor（北京）-> 结束',10,'2021-02-20 07:42:02','2021-02-05 10:24:08'),
(677,39,446,452,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 10000: AP Supervisor（北京）-> 结束',10,'2021-02-20 07:38:31','2021-02-05 10:26:10'),
(678,39,446,447,null,null,'其他情况：AP Supervisor（北京）-> Finance Manager',10,'2021-02-05 10:27:39','2021-02-05 10:27:39'),
(679,39,447,448,null,null,'Finance Manager->Finance Senior Manager',10,'2021-02-05 10:29:13','2021-02-05 10:29:13'),
(680,39,448,452,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 10000: Finance Senior Manager->结束',10,'2021-02-20 07:42:07','2021-02-05 10:30:30'),
(681,39,448,452,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 100000: Finance Senior Manager->结束',10,'2021-02-20 07:38:37','2021-02-05 10:31:02'),
(682,39,448,449,null,null,'Finance Senior Manager->Finance Director',10,'2021-02-05 10:32:19','2021-02-05 10:32:19'),
(683,39,449,452,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利/团建费 且 金额 < 50000: Finance Director->结束',10,'2021-02-20 07:42:13','2021-02-05 10:33:19'),
(684,39,449,452,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 < 200000: Finance Director->结束',10,'2021-02-20 07:46:20','2021-02-05 10:34:55'),
(685,39,449,450,null,null,'Finance Director->CFO',10,'2021-02-05 10:36:02','2021-02-05 10:36:02'),
(686,39,450,451,null,null,'CFO->CEO',10,'2021-02-05 10:36:28','2021-02-05 10:36:28'),
(687,39,451,452,null,null,'CEO->结束',10,'2021-02-05 10:36:47','2021-02-05 10:36:47'),
(740,44,524,525,null,null,'员工提交->直线上级',10,'2021-02-19 05:37:33','2021-02-19 05:37:33'),
(741,44,525,526,null,null,'直线上级->二级部门负责人',10,'2021-02-19 05:38:01','2021-02-19 05:38:01'),
(742,44,526,532,'$p1 != 1 && $p2 < 2000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 2000: 二级部门负责人->AP （菲律宾）',10,'2021-02-20 07:49:21','2021-02-19 05:39:24'),
(743,44,526,530,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 10000:二级部门负责人->HRBP Director',10,'2021-02-20 07:48:31','2021-02-19 05:42:22'),
(744,44,526,527,null,null,'二级部门负责人->一级部门负责人',10,'2021-02-19 05:43:25','2021-02-19 05:43:25'),
(745,44,527,532,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:一级部门负责人->AP(菲律宾)',10,'2021-02-20 07:49:27','2021-02-19 05:45:11'),
(746,44,527,530,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:一级部门负责人->HRBP Director',10,'2021-02-20 07:48:39','2021-02-19 05:46:12'),
(747,44,527,528,null,null,'一级部门负责人->所属公司负责人',10,'2021-02-19 05:47:42','2021-02-19 05:47:42'),
(748,44,528,532,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:所属公司负责人->AP （菲律宾）',10,'2021-02-20 07:49:32','2021-02-19 05:48:58'),
(749,44,528,530,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 200000:所属公司负责人->HRBP Director',10,'2021-02-20 07:48:45','2021-02-19 05:50:02'),
(750,44,528,529,null,null,'所属公司负责人->组织负责人',10,'2021-02-19 05:50:30','2021-02-19 05:50:30'),
(751,44,529,532,'$p1 != 1','getReimbursementType','非员工福利费/团建费:组织负责人->AP（菲律宾）',10,'2021-02-20 07:49:38','2021-02-19 05:52:58'),
(752,44,529,530,'','','组织负责人->HRBP Director',10,'2021-02-19 05:54:53','2021-02-19 05:54:10'),
(753,44,530,532,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:HRBP DIrector-> AP (菲律宾)',10,'2021-02-20 07:48:54','2021-02-19 05:56:58'),
(754,44,530,531,null,null,'HRBP Director->CPO',10,'2021-02-19 05:57:34','2021-02-19 05:57:34'),
(755,44,531,532,null,null,'CPO->AP（菲律宾）',10,'2021-02-19 05:57:55','2021-02-19 05:57:55'),
(756,44,532,533,null,null,'AP（菲律宾）->AP Supervisor（菲律宾）',10,'2021-02-19 06:00:46','2021-02-19 06:00:46'),
(757,44,533,534,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-02-19 06:01:14','2021-02-19 06:01:14'),
(758,44,534,540,'$p1 != 1 && $p2 < 2000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 2000:AP Supervisor（北京）->结束',10,'2021-02-20 07:49:43','2021-02-19 06:03:38'),
(759,44,534,540,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 10000:AP Supervisor（北京）-> 结束',10,'2021-02-20 07:49:00','2021-02-19 06:04:56'),
(760,44,534,535,null,null,'AP Supervisor（北京）->Finance Manager',10,'2021-02-19 06:05:54','2021-02-19 06:05:54'),
(761,44,535,536,null,null,'Finance Manager->Finance Senior Manager',10,'2021-02-19 06:32:23','2021-02-19 06:32:23'),
(762,44,536,540,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束',10,'2021-02-20 07:49:50','2021-02-19 06:33:53'),
(763,44,536,540,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:Finance Senior Manager->结束',10,'2021-02-20 07:49:06','2021-02-19 06:34:44'),
(764,44,536,537,null,null,'Finance Senior Manager->Finance Director',10,'2021-02-19 06:35:58','2021-02-19 06:35:58'),
(765,44,537,540,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:Finance Director->结束',10,'2021-02-20 07:49:55','2021-02-19 06:36:36'),
(766,44,537,540,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 200000:Finance Director->结束',10,'2021-02-20 07:49:12','2021-02-19 06:36:46'),
(767,44,537,538,null,null,'Finance Director->AVP/CFO',10,'2021-02-19 06:38:58','2021-02-19 06:38:58'),
(768,44,538,539,null,null,'AVP/CFO->CEO',10,'2021-02-19 06:39:31','2021-02-19 06:39:31'),
(769,44,539,540,null,null,'CEO->结束',10,'2021-02-19 06:39:45','2021-02-19 06:39:45'),
(770,41,473,474,null,null,'员工提交->网点主管',10,'2021-02-19 08:09:53','2021-02-19 08:09:53'),
(771,41,474,476,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000: 网点主管->Supervisor',10,'2021-02-20 08:01:56','2021-02-19 08:11:41'),
(772,41,474,476,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000: 网点主管->Supervisor',10,'2021-02-20 08:00:36','2021-02-19 08:12:16'),
(773,41,474,475,null,null,'网点主管->Senior Manager',10,'2021-02-19 08:12:44','2021-02-19 08:12:44'),
(774,41,475,476,null,null,'Senior Manager->Supervisor',10,'2021-02-19 08:16:35','2021-02-19 08:16:35'),
(775,41,476,483,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000: Supervisor->AP（菲律宾）',10,'2021-02-20 08:02:02','2021-02-19 08:25:49'),
(776,41,476,481,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000:Supervisor->HRBP Director',10,'2021-02-20 08:00:44','2021-02-19 08:37:02'),
(777,41,476,477,null,null,'Supervisor->Manager',10,'2021-02-19 08:41:03','2021-02-19 08:41:03'),
(778,41,477,483,'$p1 != 1 && $p2 < 3000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 3000:Manager->AP（菲律宾）',10,'2021-02-20 08:02:08','2021-02-19 08:54:43'),
(779,41,477,481,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 10000:Manager->HRBP Director',10,'2021-02-20 08:00:51','2021-02-19 08:55:48'),
(780,41,477,478,null,null,'Manager->一级部门负责人',10,'2021-02-19 08:56:17','2021-02-19 08:56:17'),
(781,41,478,483,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:一级部门负责人->AP（菲律宾）',10,'2021-02-20 08:02:13','2021-02-19 08:58:04'),
(782,41,478,481,'$p1 == 1 && $p2 < 50000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 50000:一级部门负责人->HRBP Director',10,'2021-02-20 08:00:57','2021-02-19 08:59:02'),
(783,41,478,479,null,null,'一级部门负责人->所属公司负责人',10,'2021-02-19 08:59:20','2021-02-19 08:59:20'),
(784,41,479,483,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:所属公司负责人->AP（菲律宾）',10,'2021-02-20 08:02:20','2021-02-19 09:02:06'),
(785,41,479,481,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director',10,'2021-02-20 08:01:03','2021-02-19 09:02:56'),
(786,41,479,480,null,null,'所属公司负责人->COO',10,'2021-02-19 09:03:15','2021-02-19 09:03:15'),
(787,41,480,483,'$p1 != 1','getReimbursementType','非员工福利费/团建费：COO->AP（菲律宾）',10,'2021-02-20 08:02:25','2021-02-19 09:05:30'),
(788,41,480,481,'$p1 == 1','getReimbursementType','员工福利费/团建费：COO->HRBP Director',10,'2021-02-20 08:01:16','2021-02-19 09:06:01'),
(789,41,481,483,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）',10,'2021-02-20 08:01:06','2021-02-19 09:07:45'),
(790,41,481,482,'','','HRBP Director->CPO',10,'2021-02-20 08:18:29','2021-02-19 09:08:27'),
(791,41,482,483,null,null,'CPO->AP（菲律宾）',10,'2021-02-19 09:11:02','2021-02-19 09:11:02'),
(792,41,483,484,null,null,'AP（菲律宾）->AP Supervisor（菲律宾）',10,'2021-02-19 09:11:36','2021-02-19 09:11:36'),
(793,41,484,485,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-02-19 09:12:00','2021-02-19 09:12:00'),
(794,41,485,491,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000: AP Supervisor（北京）->结束',10,'2021-02-20 08:02:33','2021-02-19 09:13:25'),
(795,41,485,486,null,null,'AP Supervisor（北京）->Finance Manager',10,'2021-02-19 09:14:18','2021-02-19 09:14:18'),
(796,41,486,491,'$p1 != 1 && $p2 < 3000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 3000:Finance Manager->结束',10,'2021-02-20 08:02:37','2021-02-19 09:15:35'),
(797,41,486,491,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000:Finance Manager->结束',10,'2021-02-20 08:01:30','2021-02-19 09:16:30'),
(798,41,486,487,null,null,'Finance Manager->Finance Senior Manager',10,'2021-02-19 09:17:06','2021-02-19 09:17:06'),
(799,41,487,491,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束',10,'2021-02-20 08:02:44','2021-02-19 09:19:04'),
(800,41,487,491,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 10000: Finance Senior Manager->结束',10,'2021-02-20 08:01:36','2021-02-19 09:19:53'),
(801,41,487,488,null,null,'Finance Senior Manager->Finance Director',10,'2021-02-19 09:20:21','2021-02-19 09:20:21'),
(802,41,488,491,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:Finance Director->结束',10,'2021-02-20 08:02:51','2021-02-19 09:23:24'),
(803,41,488,491,'$p1 == 1 && $p2 < 50000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 50000:Finance Director->结束',10,'2021-02-20 08:01:42','2021-02-19 09:24:10'),
(804,41,488,489,null,null,'Finance Director->CFO',10,'2021-02-19 09:24:29','2021-02-19 09:24:29'),
(805,41,489,491,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:CFO->结束',10,'2021-02-20 08:01:45','2021-02-19 09:28:39'),
(806,41,489,490,null,null,'CFO->CEO',10,'2021-02-19 09:28:56','2021-02-19 09:28:56'),
(807,41,490,491,null,null,'CEO->结束',10,'2021-02-19 09:29:15','2021-02-19 09:29:15'),
(808,42,492,493,null,null,'员工提交->直线上级',10,'2021-02-19 10:22:26','2021-02-19 10:22:26'),
(809,42,493,495,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000: 直线上级->Supervisor',10,'2021-02-20 06:31:19','2021-02-19 10:23:42'),
(810,42,493,495,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000:直线上级->Supervisor',10,'2021-02-20 06:27:07','2021-02-19 10:24:03'),
(811,42,493,494,null,null,'直线上级->网点主管',10,'2021-02-19 10:25:39','2021-02-19 10:24:12'),
(812,42,494,495,null,null,'网点主管->Supervisor',10,'2021-02-19 10:26:10','2021-02-19 10:26:10'),
(813,42,495,497,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000: Supervisor->一级部门负责人',10,'2021-02-20 07:18:16','2021-02-19 10:26:51'),
(814,42,495,497,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000:Supervisor->一级部门负责人',10,'2021-02-20 07:18:12','2021-02-19 10:27:22'),
(815,42,495,496,null,null,'Supervisor->Manager',10,'2021-02-19 10:27:49','2021-02-19 10:27:49'),
(816,42,496,497,null,null,'Manager->一级部门负责人',10,'2021-02-19 10:28:23','2021-02-19 10:28:23'),
(817,42,497,502,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:一级部门负责人->AP（菲律宾）',10,'2021-02-20 07:22:55','2021-02-19 10:30:07'),
(818,42,497,500,'$p1 == 1 && $p2 < 50000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 50000:一级部门负责人->HRBP Director',10,'2021-02-20 07:23:24','2021-02-19 10:31:06'),
(819,42,497,498,'(($p1 == 1 && $p2 >= 50000)||($p1!=1 && $p2>=10000))','getReimbursementType,getAmount','一级部门负责人->所属公司负责人',10,'2021-05-25 09:11:57','2021-02-19 10:31:51'),
(820,42,498,502,'($p1 != 1 && $p2 < 50000000)','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:所属公司负责人->AP（菲律宾）',10,'2021-05-25 09:11:58','2021-02-19 10:32:54'),
(821,42,498,500,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director',10,'2021-02-20 07:24:28','2021-02-19 10:33:31'),
(822,42,498,499,null,null,'所属公司负责人->COO',10,'2021-02-19 10:33:51','2021-02-19 10:33:51'),
(823,42,499,502,'$p1 != 1','getReimbursementType','非员工福利费/团建费:COO->AP（菲律宾）',10,'2021-02-20 07:26:00','2021-02-19 10:34:48'),
(824,42,499,500,'$p1 == 1','getReimbursementType','员工福利费/团建费:COO->HRBP Director',10,'2021-02-20 07:27:31','2021-02-19 10:35:30'),
(825,42,500,502,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）',10,'2021-02-20 07:28:01','2021-02-19 10:36:19'),
(826,42,500,501,'$p1 == 1 && $p2 >= *********','getReimbursementType,getAmount','员工福利/团建费 且 金额 >= 100000:HRBP Director->CPO',10,'2021-02-20 07:28:33','2021-02-19 10:36:55'),
(827,42,501,502,null,null,'CPO->AP（菲律宾）',10,'2021-02-19 10:37:19','2021-02-19 10:37:19'),
(828,42,502,503,null,null,'AP（菲律宾）->AP Supervisor（菲律宾）',10,'2021-02-19 10:37:43','2021-02-19 10:37:43'),
(829,42,503,504,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-02-19 10:38:16','2021-02-19 10:38:16'),
(830,42,504,510,'$p1 != 1 && $p2 < 1000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 1000:AP Supervisor（北京）->结束',10,'2021-02-20 07:29:20','2021-02-19 10:39:12'),
(831,42,504,505,null,null,'AP Supervisor（北京）->Finance Manager',10,'2021-02-19 10:39:45','2021-02-19 10:39:45'),
(832,42,505,510,'$p1 != 1 && $p2 < 3000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 3000:Finance Manager->结束',10,'2021-02-20 07:29:55','2021-02-19 10:40:57'),
(833,42,505,510,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 3000:Finance Manager->结束',10,'2021-02-20 07:30:10','2021-02-19 10:41:31'),
(834,42,505,506,null,null,'Finance Manager->Finance Senior Manager',10,'2021-02-19 10:41:55','2021-02-19 10:41:55'),
(835,42,506,510,'$p1 != 1 && $p2 < 10000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束',10,'2021-02-20 07:30:57','2021-02-19 10:42:27'),
(836,42,506,510,'$p1 == 1 && $p2 < 10000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束',10,'2021-02-20 07:31:14','2021-02-19 10:42:36'),
(837,42,506,507,null,null,'Finance Senior Manager->Fiance Director',10,'2021-02-19 10:44:03','2021-02-19 10:42:43'),
(838,42,507,510,'$p1 != 1 && $p2 < 50000000','getReimbursementType,getAmount','非员工福利费/团建费 且 金额 < 50000:Finance Director->结束',10,'2021-02-20 07:31:53','2021-02-19 10:44:21'),
(839,42,507,510,'$p1 == 1 && $p2 < 50000000','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 50000:Finance Director->结束',10,'2021-02-20 07:32:12','2021-02-19 10:44:29'),
(840,42,507,508,null,null,'Finance Director->CFO',10,'2021-02-19 10:45:42','2021-02-19 10:44:36'),
(841,42,508,510,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','员工福利费/团建费 且 金额 < 100000:CFO->结束',10,'2021-02-20 07:32:50','2021-02-19 10:46:28'),
(842,42,508,509,null,null,'CFO->CEO',10,'2021-02-19 10:46:56','2021-02-19 10:46:38'),
(843,42,509,510,null,null,'CEO->结束',10,'2021-02-19 10:47:32','2021-02-19 10:47:32'),
(844,40,542,543,null,null,'员工提交->网点主管',10,'2021-02-20 09:35:07','2021-02-20 09:35:07'),
(845,40,543,547,'$p1 == 2 && $p2 < 1000000','getReimbursementType,getAmount','外协 且 金额 < 1000: 网点主管->Supervisor_2',10,'2021-02-20 09:43:08','2021-02-20 09:36:52'),
(846,40,543,546,'$p1 < 1000000','getAmount','金额 < 1000:网点主管->Supervisor',10,'2021-02-20 09:45:24','2021-02-20 09:38:22'),
(847,40,543,544,'',null,'网点主管->Manager',10,'2021-02-20 09:41:36','2021-02-20 09:38:54'),
(848,40,544,547,'$p1 == 2 && $p2 < 3000000','getReimbursementType,getAmount','外协 且 金额 < 3000:Manager->Supervisor_2',10,'2021-02-20 09:48:37','2021-02-20 09:39:19'),
(849,40,544,546,'$p1 < 3000000','getAmount','金额 < 3000:Manager->Supervisor',10,'2021-02-20 09:48:33','2021-02-20 09:47:00'),
(850,40,544,545,null,null,'Manager->Senior Manager',10,'2021-02-20 09:48:00','2021-02-20 09:47:21'),
(851,40,545,547,'$p1 == 2','getReimbursementType','外协：Senior Manager->Supervisor_2',10,'2021-02-20 09:51:03','2021-02-20 09:50:13'),
(852,40,545,546,null,null,'Senior Manager->Supervisor',10,'2021-02-20 09:50:43','2021-02-20 09:50:43'),
(853,40,546,552,'$p1 == 1 && $p2 < 3000000','getReimbursementType,getAmount','福利费/团建费 且 金额 < 3000:Supervisor->HRBP Director',10,'2021-02-20 10:00:33','2021-02-20 09:53:19'),
(854,40,546,554,'$p1 == 0 && $p2 < 3000000','getReimbursementType,getAmount','其他 且 金额 < 3000:Supervisor->AP（菲律宾）',10,'2021-02-20 10:00:35','2021-02-20 09:54:10'),
(855,40,546,548,null,null,'非外协：Supervisor->Manager',10,'2021-02-20 09:57:29','2021-02-20 09:54:58'),
(856,40,547,554,'$p1 < 3000000','getAmount','外协: 金额 < 3000:Supervisor_2->AP（菲律宾）',10,'2021-02-20 10:00:41','2021-02-20 09:56:43'),
(857,40,547,601,null,null,'外协：Supervisor_2->Manager',10,'2021-04-20 11:33:42','2021-02-20 09:57:18'),
(858,40,548,554,'in_array($p1, [2,0]) && $p2 < 5000000','getReimbursementType,getAmount','外协/其他 且 金额 < 5000:Manager->AP（菲律宾）',10,'2021-02-20 10:08:29','2021-02-20 10:04:56'),
(859,40,548,552,'$p1 == 1 && $p2 < 5000000','getReimbursementType,getAmount','福利费/团建费 且 金额 < 5000:Manager->HRBP Director',10,'2021-02-20 10:08:31','2021-02-20 10:06:43'),
(860,40,548,549,null,null,'Manager->/',10,'2021-02-20 10:07:11','2021-02-20 10:06:53'),
(861,40,549,554,'in_array($p1, [2,0]) && $p2 < 20000000','getReimbursementType,getAmount','外协/其他 且 金额 < 20000:/->AP（菲律宾）',10,'2021-02-20 10:12:28','2021-02-20 10:09:47'),
(862,40,549,552,'$p1 == 1 && $p2 < 20000000','getReimbursementType,getAmount','福利费/团建费 且 金额 < 20000:/->HRBP Director',10,'2021-02-20 10:12:30','2021-02-20 10:11:07'),
(863,40,549,550,null,null,'/->所属公司负责人',10,'2021-02-20 10:12:51','2021-02-20 10:11:34'),
(864,40,550,554,'in_array($p1, [2, 0]) && $p2 < *********','getReimbursementType,getAmount','外协/其他 且 金额 < 100000:所属公司负责人->AP（菲律宾）',10,'2021-02-20 10:17:11','2021-02-20 10:14:27'),
(865,40,550,552,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director',10,'2021-02-20 10:17:13','2021-02-20 10:15:40'),
(866,40,550,551,null,null,'所属公司负责人->COO',10,'2021-02-20 10:16:16','2021-02-20 10:16:16'),
(867,40,551,554,'in_array($p1, [2, 0])','getReimbursementType','外协/其他：COO->AP（菲律宾）',10,'2021-02-20 11:21:51','2021-02-20 10:18:28'),
(868,40,551,552,null,null,'COO->HRBP Director',10,'2021-02-20 11:21:15','2021-02-20 11:21:03'),
(869,40,552,554,'$p1 == 1 && $p2 < *********','getReimbursementType,getAmount','福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）',10,'2021-02-20 11:24:57','2021-02-20 11:23:47'),
(870,40,552,553,null,null,'HRBP Director->CPO',10,'2021-02-20 11:24:34','2021-02-20 11:24:34'),
(871,40,553,554,null,null,'CPO->AP（菲律宾）',10,'2021-02-20 11:25:31','2021-02-20 11:25:31'),
(872,40,554,555,null,null,'AP（菲律宾）->AP Supervisor（菲律宾）',10,'2021-02-20 11:26:08','2021-02-20 11:26:08'),
(873,40,555,556,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-02-20 11:26:43','2021-02-20 11:26:43'),
(874,40,556,562,'$p1 < 1000000','getAmount','金额 < 1000:AP Supervisor（北京）->结束',10,'2021-02-22 01:51:11','2021-02-22 01:50:52'),
(875,40,556,557,null,null,'AP Supervisor（北京）->Finance Manager',10,'2021-02-22 01:51:47','2021-02-22 01:51:47'),
(876,40,557,562,'$p1 < 3000000','getAmount','金额 < 3000:Finance Manager->结束',10,'2021-02-22 01:54:23','2021-02-22 01:52:48'),
(877,40,557,558,null,null,'Finance Manager->Finance Senior Manager',10,'2021-02-22 01:53:14','2021-02-22 01:53:14'),
(878,40,558,562,'$p1 < 5000000','getAmount','金额 < 5000:Finance Senior Manager->结束',10,'2021-02-22 01:54:13','2021-02-22 01:54:13'),
(879,40,558,559,null,null,'Finance Senior Manager->Fiance Director',10,'2021-02-22 01:54:50','2021-02-22 01:54:50'),
(880,40,559,562,'$p1 < 20000000','getAmount','金额 < 20000:Finance Director->结束',10,'2021-02-22 01:56:06','2021-02-22 01:56:06'),
(881,40,559,560,'$p1 >= 20000000','getAmount','Finance Director->CFO',10,'2021-05-12 13:13:12','2021-02-22 01:56:24'),
(882,40,560,562,'$p1 < *********','getAmount','金额 < 100000:CFO->结束',10,'2021-02-22 01:57:19','2021-02-22 01:57:19'),
(883,40,560,561,null,null,'CFO->CEO',10,'2021-02-22 01:57:32','2021-02-22 01:57:32'),
(884,40,561,562,null,null,'CEO->结束',10,'2021-02-22 01:57:50','2021-02-22 01:57:50'),
(885,43,511,512,'$p1 == 23357','getSubmitterId','申请人为jeab（23357）',20,'2021-03-24 13:15:05','2021-02-26 11:57:09'),
(886,43,512,513,null,null,'CPO(56780)',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(887,43,511,513,null,null,'CPO(56780)',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(888,43,513,514,'$p1 == 2 && $p2 > 66000000 || $p1 == 1 && $p2 > *********0','getCurrency,getAmount','CEO(17008)',20,'2021-03-24 13:15:06','2021-02-26 11:57:09'),
(889,43,514,515,null,null,'AP(菲律宾)',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(890,43,515,516,null,null,'AP Supervisor(菲律宾)',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(891,43,516,517,null,null,'AP Supervisor(北京)',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(892,43,517,523,'$p1 == 2 && $p2 <= 60000 || $p1 == 1 && $p2 <= 2000000 ','getCurrency,getAmount','泰铢小于等于2000, 美元小于等于60',20,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(893,43,517,518,null,null,'Finance Manager  20254',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(894,43,518,519,null,null,'Finance Senior Manager  54677',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(895,43,519,523,'$p1 == 2 && $p2 <= 600000 || $p1 == 1 && $p2 <= 20000000 ','getCurrency,getAmount','泰铢小于等于20000, 美元小于等于600',20,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(896,43,519,520,null,null,'Finance Director   17152',10,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(897,43,520,523,'$p1 == 2 && $p2 <= 1600000 || $p1 == 1 && $p2 <= 50000000 ','getCurrency,getAmount','泰铢小于等于50000, 美元小于等于1600',20,'2021-02-26 11:57:09','2021-02-26 11:57:09'),
(898,43,520,523,null,null,'CFO(17008)',10,'2021-03-24 13:15:09','2021-02-26 11:57:09'),
(901,43,513,515,null,null,'AP(菲律宾)',10,'2021-03-24 13:15:07','2021-03-24 13:15:07'),
(902,45,563,564,null,null,'需求部门员工提交->需求部门负责人',10,'2021-03-10 14:05:34','2021-03-10 14:04:20'),
(903,45,564,565,null,null,'需求部门负责人->数据部门负责人',10,'2021-03-10 14:05:00','2021-03-10 14:05:00'),
(904,45,565,566,null,null,'数据部门负责人->相关部门负责人',10,'2021-03-10 14:05:28','2021-03-10 14:05:28'),
(905,45,566,567,null,null,'相关部门负责人->结束',10,'2021-03-10 14:06:00','2021-03-10 14:06:00'),
(906,46,568,569,null,null,'员工提交 -> 直接上级',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(907,46,569,570,null,null,'直接上级 -> 二级部门',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(908,46,570,571,null,null,'二级部门 -> 一级部门',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(909,46,571,572,null,null,'一级部门 -> FM',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(910,46,572,573,null,null,'FM -> FSM',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(911,46,573,574,'($p1>=10000000&&$p2==1)||($p3>=320000&&$p2==2)||($p4>=2100000&&$p2==3)','getTHB,getCurrency,getUSD,getCNY','FSM -> FD. ((>10,000 and 泰铢)||(>320 and 美元)||(>2100 and 人民币))   ',20,'2021-03-30 09:37:29','2021-03-30 07:57:35'),
(912,46,573,575,null,null,'FSM -> 结束',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(913,46,574,575,null,null,'FD -> 结束',10,'2021-03-30 07:57:35','2021-03-30 07:57:35'),
(914,47,576,577,null,null,'员工提交 -> 上级',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(915,47,577,578,null,null,'上级 -> network support',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(916,47,578,579,null,null,'network support -> FM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(917,47,579,580,null,null,'FM -> FSM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(918,47,580,581,null,null,'FSM -> FD',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(919,47,581,582,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(920,47,582,583,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(921,47,576,577,null,null,'员工提交 -> 上级',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(922,47,577,578,null,null,'上级 -> network support',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(923,47,578,579,null,null,'network support -> FM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(924,47,579,580,null,null,'FM -> FSM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(925,47,580,581,null,null,'FSM -> FD',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(926,47,581,582,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(927,47,582,583,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(928,48,584,585,null,null,'员工提交 -> 上级',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(929,48,585,586,null,null,'上级 -> HD',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(930,48,586,587,null,null,'HD -> FM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(931,48,587,588,null,null,'FM -> FSM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(932,48,588,589,null,null,'FSM -> FD',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(933,48,589,590,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(934,48,590,591,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(935,48,594,595,null,null,'SM -> ASMD',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(936,48,595,596,null,null,'ASMD -> FM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(937,48,596,597,null,null,'FM -> FSM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(938,48,597,598,null,null,'FSM -> FD',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(939,48,598,599,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(940,48,599,600,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(941,48,584,585,null,null,'员工提交 -> 上级',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(942,48,585,586,null,null,'上级 -> HD',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(943,48,586,587,null,null,'HD -> FM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(944,48,587,588,null,null,'FM -> FSM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(945,48,588,589,null,null,'FSM -> FD',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(946,48,589,590,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(947,48,590,591,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(948,49,592,593,null,null,'员工提交 -> AM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(949,49,593,594,null,null,'AM -> SM',10,'2021-04-01 10:04:54','2021-04-01 10:04:54'),
(950,49,592,593,null,null,'员工提交 -> AM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(951,49,593,594,null,null,'AM -> SM',10,'2021-04-01 10:13:40','2021-04-01 10:13:40'),
(952,49,594,595,null,null,'SM -> ASMD',10,'2021-04-01 10:15:18','2021-04-01 10:13:40'),
(953,49,595,596,null,null,'ASMD -> FM',10,'2021-04-01 10:15:16','2021-04-01 10:13:40'),
(954,49,596,597,null,null,'FM -> FSM',10,'2021-04-01 10:15:13','2021-04-01 10:13:40'),
(955,49,597,598,null,null,'FSM -> FD',10,'2021-04-01 10:15:11','2021-04-01 10:13:40'),
(956,49,598,599,null,null,'FD -> APO(出纳)',10,'2021-04-01 10:15:07','2021-04-01 10:13:40'),
(957,49,599,600,null,null,'APO(出纳-> 结束',10,'2021-04-01 10:15:05','2021-04-01 10:13:40'),
(958,40,601,554,'$p1 < 5000000','getAmount',null,10,'2021-04-20 11:33:43','2021-04-20 11:33:43'),
(959,40,601,602,'$p1 >= 5000000','getAmount',null,10,'2021-04-20 11:33:43','2021-04-20 11:33:43'),
(960,40,602,554,'$p1 < 20000000','getAmount',null,10,'2021-04-20 11:33:44','2021-04-20 11:33:44'),
(961,40,602,550,'$p1 >= 20000000','getAmount',null,10,'2021-04-20 11:33:44','2021-04-20 11:33:44'),
(962,30,306,720,null,null,'员工提交->APS北京',11,'2021-05-07 02:48:11','2021-05-07 02:48:11'),
(963,50,801,820,'$p1 == 1','getKEY','sales 部门一级审批人',20,'2021-05-15 14:08:34','2021-05-12 13:08:51'),
(964,50,820,803,'$p1 != 0 || $p2 > 50 || $p3 < 2 && $p3 >0 || $p4 != 0 || $p5 >10 || $p6 == 3 || in_array($p7, [1, 2])','getKEY1,getKEY3,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8','sales 部门二级审批人',20,'2021-05-15 14:08:33','2021-05-12 13:08:51'),
(965,50,820,812,null,null,'',0,'2021-05-15 14:08:33','2021-05-12 13:08:51'),
(966,50,803,804,'$p1 > 15 || $p2 > 80 || $p3 <= 1.5 && $p3 >0  || $p4 != 0 || $p5 > 15 ||  $p6 == 3 || in_array($p7, [1, 2])','getKEY1,getKEY3,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8','sales 部门三级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(967,50,803,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(968,50,804,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(969,50,801,805,'$p1 == 2','getKEY','pmd 部门一级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(970,50,805,806,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(971,50,806,804,'$p1 > 15 || $p2 < 1 && $p2 >0 || $p3 != 0 || $p4 > 20 || $p5 == 3 || in_array($p6, [1, 2]) ','getKEY1,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8','',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(972,50,806,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(973,50,801,807,'$p1 == 3','getKEY','shop部门一级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(974,50,807,808,null,null,'shop部门二级审批人',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(975,50,808,809,'$p1 > 50 || $p2 < 2 && $p2 > 0 || $p3 != 0 || $p4 > 10 || $p5 == 3 || in_array($p6, [1, 2]) ','getKEY3,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8','shop 部门三级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(976,50,809,804,'$p1 > 80 || $p2 < 1.5 && $p2 > 0 || $p3 != 0 || $p4 > 15 || $p5 == 3 || in_array($p6, [1, 2])','getKEY3,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8','shop 部门四级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(977,50,808,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(978,50,809,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(979,50,801,810,'$p1 == 4','getKEY','network 部门一级审批人',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(980,50,810,811,'$p1 > 5 || $p2 || $p3 == 3 || in_array($p4, [1, 2])','getKEY4,getKEY7,getKEY2,getKEY8','network 部门运费折扣率 <= 5',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(981,50,811,804,'$p1 > 15 || $p2 || $p3 == 3 || in_array($p4, [1, 2])','getKEY4,getKEY7,getKEY2,getKEY8','network',20,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(982,50,811,812,null,null,'network',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(983,50,810,812,null,null,'network',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),
(984,50,804,812,null,null,'',0,'2021-05-12 13:08:51','2021-05-12 13:08:51'),

(985, 51, 603, 604, NULL, NULL, '员工提交->网点主管', 10, '2021-02-20 09:35:07', '2021-02-20 09:35:07'),
(986, 51, 604, 608, '$p1 == 2 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '外协 且 金额 < 1500: 网点主管->Supervisor_2', 10, '2021-05-07 06:25:35', '2021-02-20 09:36:52'),
(987, 51, 604, 607, '$p1 < 1500', 'getTotalAmount', '金额 < 1500:网点主管->Supervisor', 9, '2021-06-07 06:29:29', '2021-02-20 09:38:22'),
(988, 51, 604, 605, '', NULL, '网点主管->Manager', 8, '2021-06-07 06:29:32', '2021-02-20 09:38:54'),
(989, 51, 605, 608, '$p1 == 2 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '外协 且 金额 < 4500:Manager->Supervisor_2', 10, '2021-05-07 06:25:43', '2021-02-20 09:39:19'),
(990, 51, 605, 607, '$p1 < 4500', 'getTotalAmount', '金额 < 4500:Manager->Supervisor', 9, '2021-06-07 06:29:34', '2021-02-20 09:47:00'),
(991, 51, 605, 606, NULL, NULL, 'Manager->Senior Manager', 8, '2021-06-07 06:29:39', '2021-02-20 09:47:21'),
(992, 51, 606, 608, '$p1 == 2', 'getOrdinaryPaymentType', '外协：Senior Manager->Supervisor_2', 10, '2021-02-20 09:51:03', '2021-02-20 09:50:13'),
(993, 51, 606, 607, NULL, NULL, 'Senior Manager->Supervisor', 9, '2021-06-07 06:29:42', '2021-02-20 09:50:43'),
(994, 51, 607, 615, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '福利费/团建费 且 金额 < 4500:Supervisor->HRBP Director', 10, '2021-05-07 06:25:54', '2021-02-20 09:53:19'),
(995, 51, 607, 617, '$p1 == 0 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '其他 且 金额 < 4500:Supervisor->AP（菲律宾）', 9, '2021-06-07 06:29:47', '2021-02-20 09:54:10'),
(996, 51, 607, 609, NULL, NULL, '非外协：Supervisor->Manager', 8, '2021-06-07 06:29:49', '2021-02-20 09:54:58'),
(997, 51, 608, 617, '$p1 < 4500', 'getTotalAmount', '外协: 金额 < 4500:Supervisor_2->AP（菲律宾）', 10, '2021-06-04 04:25:04', '2021-02-20 09:56:43'),
(998, 51, 608, 611, NULL, NULL, '外协：Supervisor_2->Manager', 9, '2021-06-07 06:29:52', '2021-02-20 09:57:18'),
(999, 51, 609, 617, 'in_array($p1, [2,0]) && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '外协/其他 且 金额 < 7500:Manager->AP（菲律宾）', 10, '2021-06-04 04:26:43', '2021-02-20 10:04:56'),
(1000, 51, 609, 615, '$p1 == 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '福利费/团建费 且 金额 < 7500:Manager->HRBP Director', 9, '2021-06-07 06:29:56', '2021-02-20 10:06:43'),
(1001, 51, 609, 610, NULL, NULL, 'Manager->/', 8, '2021-06-07 06:29:59', '2021-02-20 10:06:53'),
(1002, 51, 610, 617, 'in_array($p1, [2,0]) && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '外协/其他 且 金额 < 30000:/->AP（菲律宾）', 10, '2021-06-04 04:26:48', '2021-02-20 10:09:47'),
(1003, 51, 610, 615, '$p1 == 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '福利费/团建费 且 金额 < 30000:/->HRBP Director', 9, '2021-06-07 06:30:04', '2021-02-20 10:11:07'),
(1004, 51, 610, 613, NULL, NULL, '/->所属公司负责人', 8, '2021-06-07 06:30:08', '2021-02-20 10:11:34'),
(1005, 51, 613, 617, 'in_array($p1, [2, 0]) && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '外协/其他 且 金额 < 150000:所属公司负责人->AP（菲律宾）', 10, '2021-06-04 04:26:53', '2021-02-20 10:14:27'),
(1006, 51, 613, 615, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '福利费/团建费 且 金额 < 150000:所属公司负责人->HRBP Director', 9, '2021-06-07 06:30:10', '2021-02-20 10:15:51'),
(1007, 51, 613, 614, NULL, NULL, '所属公司负责人->COO', 8, '2021-06-07 06:30:14', '2021-02-20 10:16:16'),
(1008, 51, 614, 617, 'in_array($p1, [2, 0])', 'getOrdinaryPaymentType', '外协/其他：COO->AP（菲律宾）', 10, '2021-06-04 04:27:03', '2021-02-20 10:18:28'),
(1009, 51, 614, 615, NULL, NULL, 'COO->HRBP Director', 9, '2021-06-07 06:30:16', '2021-02-20 11:21:03'),
(1010, 51, 615, 617, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '福利费/团建费 且 金额 < 150000:HRBP Director->AP（菲律宾）', 10, '2021-06-04 04:27:28', '2021-02-20 11:23:47'),
(1011, 51, 615, 616, NULL, NULL, 'HRBP Director->CPO', 9, '2021-06-07 06:30:18', '2021-02-20 11:24:34'),
(1012, 51, 616, 617, NULL, NULL, 'CPO->AP（菲律宾）', 10, '2021-06-04 04:27:21', '2021-02-20 11:25:31'),
(1013, 51, 617, 618, NULL, NULL, 'AP（菲律宾）->AP Supervisor（菲律宾）', 10, '2021-06-04 04:27:18', '2021-02-20 11:26:08'),
(1014, 51, 618, 619, NULL, NULL, 'AP Supervisor（菲律宾）->AP Supervisor（北京）', 10, '2021-06-04 04:27:11', '2021-02-20 11:26:43'),
(1015, 51, 619, 625, '$p1 < 1500', 'getTotalAmount', '金额 < 1500:AP Supervisor（北京）->结束', 10, '2021-05-07 06:26:46', '2021-02-22 01:50:52'),
(1016, 51, 619, 620, NULL, NULL, 'AP Supervisor（北京）->Finance Manager', 9, '2021-06-07 06:30:25', '2021-02-22 01:51:47'),
(1017, 51, 620, 625, '$p1 < 4500', 'getTotalAmount', '金额 < 4500:Finance Manager->结束', 10, '2021-05-07 06:26:50', '2021-02-22 01:52:48'),
(1018, 51, 620, 621, NULL, NULL, 'Finance Manager->Finance Senior Manager', 9, '2021-06-07 06:30:27', '2021-02-22 01:53:14'),
(1019, 51, 621, 625, '$p1 < 7500', 'getTotalAmount', '金额 < 7500:Finance Senior Manager->结束', 10, '2021-05-07 06:26:57', '2021-02-22 01:54:13'),
(1020, 51, 621, 622, NULL, NULL, 'Finance Senior Manager->Fiance Director', 9, '2021-06-07 06:30:32', '2021-02-22 01:54:50'),
(1021, 51, 622, 625, '$p1 < 30000', 'getTotalAmount', '金额 < 30000:Finance Director->结束', 10, '2021-05-07 08:29:13', '2021-02-22 01:56:06'),
(1022, 51, 622, 623, '$p1 >= 30000', 'getTotalAmount', 'Finance Director->CFO', 9, '2021-06-07 06:30:36', '2021-02-22 01:56:24'),
(1023, 51, 623, 625, '$p1 < 150000', 'getTotalAmount', '金额 < 150000:CFO->结束', 10, '2021-05-07 08:29:30', '2021-02-22 01:57:19'),
(1024, 51, 623, 624, NULL, NULL, 'CFO->CEO', 9, '2021-06-07 06:30:40', '2021-02-22 01:57:32'),
(1025, 51, 624, 625, NULL, NULL, 'CEO->结束', 10, '2021-02-22 01:57:50', '2021-02-22 01:57:50'),
(1026, 51, 611, 617, '$p1 < 7500', 'getTotalAmount', NULL, 10, '2021-05-07 08:29:39', '2021-04-20 11:33:43'),
(1027, 51, 611, 612, '$p1 >= 7500', 'getTotalAmount', NULL, 9, '2021-06-07 06:30:48', '2021-04-20 11:33:43'),
(1028, 51, 612, 617, '$p1 < 30000', 'getTotalAmount', NULL, 10, '2021-05-07 08:29:48', '2021-04-20 11:33:44'),
(1029, 51, 612, 613, '$p1 >= 30000', 'getTotalAmount', NULL, 9, '2021-06-07 06:30:50', '2021-04-20 11:33:44'),

(1030, 52, 626, 627, NULL, NULL, '员工提交->网点主管', 10, '2021-02-19 08:09:53', '2021-02-19 08:09:53'),
(1031, 52, 627, 629, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500: 网点主管->Supervisor', 10, '2021-05-07 06:27:12', '2021-02-19 08:11:52'),
(1032, 52, 627, 629, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500: 网点主管->Supervisor', 9, '2021-06-07 06:31:19', '2021-02-19 08:12:16'),
(1033, 52, 627, 628, NULL, NULL, '网点主管->Senior Manager', 8, '2021-06-07 06:31:22', '2021-02-19 08:12:44'),
(1034, 52, 628, 629, NULL, NULL, 'Senior Manager->Supervisor', 10, '2021-02-19 08:16:35', '2021-02-19 08:16:35'),
(1035, 52, 629, 636, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500: Supervisor->AP（菲律宾）', 10, '2021-06-07 09:05:03', '2021-02-19 08:25:49'),
(1036, 52, 629, 634, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500:Supervisor->HRBP Director', 9, '2021-06-07 06:31:29', '2021-02-19 08:37:02'),
(1037, 52, 629, 630, NULL, NULL, 'Supervisor->Manager', 8, '2021-06-07 06:31:32', '2021-02-19 08:52:03'),
(1038, 52, 630, 636, '$p1 != 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 4500:Manager->AP（菲律宾）', 10, '2021-06-07 09:05:07', '2021-02-19 08:54:43'),
(1039, 52, 630, 634, '$p1 == 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 7500:Manager->HRBP Director', 9, '2021-06-07 06:31:34', '2021-02-19 08:55:48'),
(1040, 52, 630, 631, NULL, NULL, 'Manager->一级部门负责人', 8, '2021-06-07 06:31:37', '2021-02-19 08:56:17'),
(1041, 52, 631, 636, '$p1 != 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 7500:一级部门负责人->AP（菲律宾）', 10, '2021-06-07 09:05:11', '2021-02-19 08:58:04'),
(1042, 52, 631, 634, '$p1 == 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 30000:一级部门负责人->HRBP Director', 9, '2021-06-07 06:31:39', '2021-02-19 08:59:02'),
(1043, 52, 631, 632, NULL, NULL, '一级部门负责人->所属公司负责人', 8, '2021-06-07 06:31:41', '2021-02-19 08:59:20'),
(1044, 52, 632, 636, '$p1 != 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 30000:所属公司负责人->AP（菲律宾）', 10, '2021-06-07 09:05:14', '2021-02-19 09:02:06'),
(1045, 52, 632, 634, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:所属公司负责人->HRBP Director', 9, '2021-06-07 06:31:43', '2021-02-19 09:02:56'),
(1046, 52, 632, 633, NULL, NULL, '所属公司负责人->COO', 8, '2021-06-07 06:31:45', '2021-02-19 09:03:15'),
(1047, 52, 633, 636, '$p1 != 1', 'getOrdinaryPaymentType', '非员工福利费/团建费：COO->AP（菲律宾）', 10, '2021-06-07 09:05:18', '2021-02-19 09:05:30'),
(1048, 52, 633, 634, '$p1 == 1', 'getOrdinaryPaymentType', '员工福利费/团建费：COO->HRBP Director', 9, '2021-06-07 07:59:21', '2021-02-19 09:06:01'),
(1049, 52, 634, 636, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:HRBP Director->AP（菲律宾）', 10, '2021-06-07 09:05:20', '2021-02-19 09:07:45'),
(1050, 52, 634, 635, '', '', 'HRBP Director->CPO', 9, '2021-06-07 07:59:28', '2021-02-19 09:08:27'),
(1051, 52, 635, 636, NULL, NULL, 'CPO->AP（菲律宾）', 10, '2021-06-07 09:05:25', '2021-02-19 09:11:02'),
(1052, 52, 636, 637, NULL, NULL, 'AP（菲律宾）->AP Supervisor（菲律宾）', 10, '2021-06-07 09:05:32', '2021-02-19 09:11:36'),
(1053, 52, 637, 638, NULL, NULL, 'AP Supervisor（菲律宾）->AP Supervisor（北京）', 10, '2021-06-07 09:05:34', '2021-02-19 09:12:00'),
(1054, 52, 638, 644, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500: AP Supervisor（北京）->结束', 10, '2021-05-07 06:28:11', '2021-02-19 09:13:25'),
(1055, 52, 638, 639, NULL, NULL, 'AP Supervisor（北京）->Finance Manager', 9, '2021-06-07 07:59:30', '2021-02-19 09:14:18'),
(1056, 52, 639, 644, '$p1 != 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 4500:Finance Manager->结束', 10, '2021-05-07 06:28:15', '2021-02-19 09:15:35'),
(1057, 52, 639, 644, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500:Finance Manager->结束', 9, '2021-06-07 07:59:33', '2021-02-19 09:16:30'),
(1058, 52, 639, 640, NULL, NULL, 'Finance Manager->Finance Senior Manager', 8, '2021-06-07 07:59:35', '2021-02-19 09:17:06'),
(1059, 52, 640, 644, '$p1 != 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 7500:Finance Senior Manager->结束', 10, '2021-05-07 06:28:22', '2021-02-19 09:19:04'),
(1060, 52, 640, 644, '$p1 == 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 7500: Finance Senior Manager->结束', 9, '2021-06-07 07:59:42', '2021-02-19 09:19:53'),
(1061, 52, 640, 641, NULL, NULL, 'Finance Senior Manager->Finance Director', 8, '2021-06-07 07:59:44', '2021-02-19 09:20:21'),
(1062, 52, 641, 644, '$p1 != 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 30000:Finance Director->结束', 10, '2021-05-07 06:28:33', '2021-02-19 09:23:24'),
(1063, 52, 641, 644, '$p1 == 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 30000:Finance Director->结束', 9, '2021-06-07 07:59:52', '2021-02-19 09:24:10'),
(1064, 52, 641, 642, NULL, NULL, 'Finance Director->CFO', 8, '2021-06-07 07:59:56', '2021-02-19 09:24:29'),
(1065, 52, 642, 644, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:CFO->结束', 10, '2021-05-07 06:28:52', '2021-02-19 09:28:39'),
(1066, 52, 642, 643, NULL, NULL, 'CFO->CEO', 9, '2021-06-07 07:59:59', '2021-02-19 09:28:56'),
(1067, 52, 643, 644, NULL, NULL, 'CEO->结束', 10, '2021-02-19 09:29:15', '2021-02-19 09:29:15'),

(1068, 53, 645, 646, NULL, NULL, '员工提交->直线上级', 10, '2021-02-19 10:22:26', '2021-02-19 10:22:26'),
(1069, 53, 646, 648, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500: 直线上级->Supervisor', 10, '2021-05-07 08:37:30', '2021-02-19 10:23:53'),
(1070, 53, 646, 648, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500:直线上级->Supervisor', 9, '2021-06-07 08:00:30', '2021-02-19 10:24:03'),
(1071, 53, 646, 647, NULL, NULL, '直线上级->网点主管', 8, '2021-06-07 08:00:33', '2021-02-19 10:24:12'),
(1072, 53, 647, 648, NULL, NULL, '网点主管->Supervisor', 10, '2021-02-19 10:26:10', '2021-02-19 10:26:10'),
(1073, 53, 648, 650, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500:Supervisor->二级部门负责人', 10, '2021-05-07 08:37:37', '2021-02-19 10:27:22'),
(1074, 53, 648, 649, NULL, NULL, 'Supervisor->Manager', 8, '2021-06-07 08:00:35', '2021-02-19 10:27:49'),
(1075, 53, 648, 650, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500: Supervisor->一级部门负责人', 9, '2021-06-07 08:00:38', '2021-02-19 10:26:51'),
(1076, 53, 649, 650, NULL, NULL, 'Manager->二级部门负责人', 10, '2021-02-19 10:28:23', '2021-02-19 10:28:23'),
(1077, 53, 650, 651, NULL, NULL, '二级部门负责人->一级部门负责人', 10, '2021-02-19 10:27:49', '2021-02-19 10:27:49'),
(1078, 53, 651, 652, '(($p1 == 1 && $p2 >= 30000)||($p1!=1 &&$p2>=7500))', 'getOrdinaryPaymentType,getTotalAmount', '一级部门负责人->所属公司负责人', 10, '2021-05-25 09:11:56', '2021-02-19 10:31:51'),
(1079, 53, 651, 656, '$p1 != 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 7500:一级部门负责人->AP（菲律宾）', 9, '2021-06-07 09:01:48', '2021-02-19 10:30:07'),
(1080, 53, 651, 654, '$p1 == 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 30000:一级部门负责人->HRBP Director', 8, '2021-06-07 08:00:50', '2021-02-19 10:31:06'),
(1081, 53, 652, 656, '$p1 != 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 30000:所属公司负责人->AP（菲律宾）', 10, '2021-06-07 09:01:52', '2021-02-19 10:32:54'),
(1082, 53, 652, 654, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:所属公司负责人->HRBP Director', 9, '2021-06-07 08:00:57', '2021-02-19 10:33:31'),
(1083, 53, 652, 653, NULL, NULL, '所属公司负责人->COO', 8, '2021-06-07 08:01:00', '2021-02-19 10:33:51'),
(1084, 53, 653, 656, '$p1 != 1', 'getOrdinaryPaymentType', '非员工福利费/团建费:COO->AP（菲律宾）', 10, '2021-06-07 09:01:54', '2021-02-19 10:34:48'),
(1085, 53, 653, 654, '$p1 == 1', 'getOrdinaryPaymentType', '员工福利费/团建费:COO->HRBP Director', 9, '2021-06-07 08:01:23', '2021-02-19 10:35:30'),
(1086, 53, 654, 656, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:HRBP Director->AP（菲律宾）', 10, '2021-06-07 09:02:05', '2021-02-19 10:36:19'),
(1087, 53, 654, 655, '$p1 == 1 && $p2 >= 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利/团建费 且 金额 >= 150000:HRBP Director->CPO', 9, '2021-06-07 08:01:26', '2021-02-19 10:36:55'),
(1088, 53, 655, 656, NULL, NULL, 'CPO->AP（菲律宾）', 10, '2021-06-07 09:02:09', '2021-02-19 10:37:19'),
(1089, 53, 656, 657, NULL, NULL, 'AP（菲律宾）->AP Supervisor（菲律宾）', 10, '2021-06-07 09:02:12', '2021-02-19 10:37:43'),
(1090, 53, 657, 658, NULL, NULL, 'AP Supervisor（菲律宾）->AP Supervisor（北京）', 10, '2021-06-07 09:02:15', '2021-02-19 10:38:16'),
(1091, 53, 658, 664, '$p1 != 1 && $p2 < 1500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 1500:AP Supervisor（北京）->结束', 10, '2021-05-07 08:36:37', '2021-02-19 10:39:12'),
(1092, 53, 658, 659, NULL, NULL, 'AP Supervisor（北京）->Finance Manager', 9, '2021-06-07 08:01:36', '2021-02-19 10:39:45'),
(1093, 53, 659, 664, '$p1 != 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 4500:Finance Manager->结束', 10, '2021-05-07 08:36:42', '2021-02-19 10:40:57'),
(1094, 53, 659, 664, '$p1 == 1 && $p2 < 4500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 4500:Finance Manager->结束', 9, '2021-06-07 08:01:38', '2021-02-19 10:41:31'),
(1095, 53, 659, 660, NULL, NULL, 'Finance Manager->Finance Senior Manager', 8, '2021-06-07 08:01:53', '2021-02-19 10:41:55'),
(1096, 53, 660, 664, '$p1 != 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 7500:Finance Senior Manager->结束', 10, '2021-05-07 08:36:55', '2021-02-19 10:53:27'),
(1097, 53, 660, 664, '$p1 == 1 && $p2 < 7500', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 7500:Finance Senior Manager->结束', 9, '2021-06-07 08:02:00', '2021-02-19 10:53:36'),
(1098, 53, 660, 661, NULL, NULL, 'Finance Senior Manager->Fiance Director', 8, '2021-06-07 08:02:02', '2021-02-19 10:53:43'),
(1099, 53, 661, 664, '$p1 != 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '非员工福利费/团建费 且 金额 < 30000:Finance Director->结束', 10, '2021-05-07 08:37:05', '2021-02-19 10:44:21'),
(1100, 53, 661, 664, '$p1 == 1 && $p2 < 30000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 30000:Finance Director->结束', 9, '2021-06-07 08:02:06', '2021-02-19 10:44:29'),
(1101, 53, 661, 662, NULL, NULL, 'Finance Director->CFO', 8, '2021-06-07 08:02:08', '2021-02-19 10:44:36'),
(1102, 53, 662, 664, '$p1 == 1 && $p2 < 150000', 'getOrdinaryPaymentType,getTotalAmount', '员工福利费/团建费 且 金额 < 150000:CFO->结束', 10, '2021-05-07 08:37:14', '2021-02-19 10:46:28'),
(1103, 53, 662, 663, NULL, NULL, 'CFO->CEO', 9, '2021-06-07 08:02:10', '2021-02-19 10:46:38'),
(1104, 53, 663, 664, NULL, NULL, 'CEO->结束', 10, '2021-02-19 10:47:32', '2021-02-19 10:47:32'),

(1105,44,526,813,'$p1 == 1 && $p2 < 10000000 && in_array($p3, [30001, 60001, 50001])','getReimbursementType,getAmount,getCompanyId',null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1106,44,527,813,'$p1 == 1 && $p2 < ********* && in_array($p3, [30001, 60001, 50001])','getReimbursementType,getAmount,getCompanyId',null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1107,44,528,813,'$p1 == 1 && $p2 < ********* && in_array($p3, [30001, 60001, 50001])','getReimbursementType,getAmount,getCompanyId',null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1108,44,529,813,'in_array($p1, [30001, 60001, 50001])','getCompanyId',null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1109,44,813,532,'$p1 == 1 && $p2 < ********* && in_array($p3, [30001, 60001, 50001])','getReimbursementType,getAmount,getCompanyId',null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1110,44,813,531,null,null,null,20,'2021-05-14 13:23:42','2021-05-14 13:23:42'),
(1111,57,721,722,null,null,'员工提交->网点主管',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1112,57,722,724,'$p1 < 1000000','getAmount','金额 < 1000: 网点主管->Supervisor',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1113,57,722,723,null,null,'网点主管->Senior Manager',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1114,57,723,724,null,null,'Senior Manager->Supervisor',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1115,57,724,731,'$p1 < 1000000','getAmount','金额 < 1000: Supervisor->AP（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1116,57,724,725,null,null,'Supervisor->Manager',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1117,57,725,731,'$p1 < 3000000','getAmount',' 金额 < 3000:Manager->AP（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1118,57,725,726,null,null,'Manager->一级部门负责人',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1119,57,726,731,'$p1 < 10000000','getAmount','金额 < 10000:一级部门负责人->AP（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1120,57,726,727,null,null,'一级部门负责人->所属公司负责人',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1121,57,727,731,'$p1 < 50000000','getAmount','金额 < 50000:所属公司负责人->AP（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1122,57,727,728,null,null,'所属公司负责人->COO',10,'2021-02-19 09:03:15','2021-02-19 09:03:15'),
(1123,57,728,731,null,null,'COO->AP（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1124,57,731,732,null,null,'AP（菲律宾）->AP Supervisor（菲律宾）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1125,57,732,733,null,null,'AP Supervisor（菲律宾）->AP Supervisor（北京）',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1126,57,733,739,'$p1 < 1000000','getAmount','金额 < 1000: AP Supervisor（北京）->结束',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1127,57,733,734,null,null,'AP Supervisor（北京）->Finance Manager',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1128,57,734,739,'$p1 < 3000000','getAmount','金额 < 3000:Finance Manager->结束',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1129,57,734,735,null,null,'Finance Manager->Finance Senior Manager',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1130,57,735,739,'$p1 < 10000000','getAmount','金额 < 10000:Finance Senior Manager->结束',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1131,57,735,736,null,null,'Finance Senior Manager->Finance Director',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1132,57,736,739,'$p1 < 50000000','getAmount','金额 < 50000:Finance Director->结束',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1133,57,736,737,null,null,'Finance Director->CFO',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1134,57,737,738,null,null,'CFO->CEO',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1135,57,738,739,null,null,'CEO->结束',10,'2021-05-19 14:31:51','2021-05-19 14:31:51'),
(1248,59,821,822,null,null,'员工提交->部门合规审批',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1249,59,822,823,null,null,'部门合规审批->部门商务',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1250,59,823,824,null,null,'部门合规审批->法务',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1251,59,824,825,null,null,'法务->AP Supervisor(菲律宾)',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1252,59,825,826,null,null,'AP Supervisor(菲律宾)->一级部门负责人',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1253,59,826,827,null,null,'一级部门负责人->AP Supervisor(北京)',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1254,59,827,828,null,null,'AP Supervisor(北京)->所属公司负责人',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1255,59,828,829,null,null,'所属公司负责人->结束',10,'2021-05-24 15:39:09','2021-05-24 15:39:09'),
(1256,60,830,831,null,null,'员工提交->部门商务',10,'2021-05-24 15:39:10','2021-05-24 15:39:10'),
(1257,60,831,832,null,null,'部门商务->AP Supervisor(菲律宾)',10,'2021-05-24 15:39:10','2021-05-24 15:39:10'),
(1258,60,832,833,null,null,'AP Supervisor(菲律宾)->一级部门负责人',10,'2021-05-24 15:39:10','2021-05-24 15:39:10'),
(1259,60,833,834,null,null,'一级部门负责人-> 结束',10,'2021-05-24 15:39:10','2021-05-24 15:39:10'),
(1260,61,835,836,null,null,'员工提交->部门合规审批',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1261,61,836,837,null,null,'部门合规审批->部门商务',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1262,61,837,838,null,null,'部门合规审批->法务',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1263,61,838,839,null,null,'法务->AP Supervisor(菲律宾)',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1264,61,839,840,null,null,'AP Supervisor(菲律宾)->一级部门负责人',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1265,61,840,841,null,null,'一级部门负责人->AP Supervisor(北京)',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1266,61,841,842,null,null,'AP Supervisor(北京)->所属公司负责人',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1267,61,842,843,null,null,'所属公司负责人->结束',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1268,62,844,845,null,null,'员工提交->部门合规审批',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1269,62,845,846,null,null,'部门合规审批->部门商务',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1270,62,846,847,null,null,'部门合规审批->法务',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1271,62,847,848,null,null,'法务->AP Supervisor(菲律宾)',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1272,62,848,849,null,null,'AP Supervisor(菲律宾)->一级部门负责人',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1273,62,849,850,null,null,'一级部门负责人->AP Supervisor(北京)',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1275,62,850,851,null,null,'所属公司负责人->结束',10,'2021-05-24 15:39:11','2021-05-24 15:39:11'),
(1276,63,852,853,null,null,'员工提交->部门合规审批',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1277,63,853,854,null,null,'部门合规审批->部门商务',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1278,63,854,855,null,null,'部门合规审批->法务',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1279,63,855,856,null,null,'法务->AP Supervisor(菲律宾)',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1280,63,856,857,null,null,'AP Supervisor(菲律宾)->一级部门负责人',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1281,63,857,858,null,null,'一级部门负责人->AP Supervisor(北京)',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1282,63,858,859,null,null,'AP Supervisor(北京)->所属公司负责人',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1283,63,859,860,null,null,'所属公司负责人->结束',10,'2021-05-24 15:39:12','2021-05-24 15:39:12'),
(1284,56,705,706,'','','员工提交->直接上级',10,'2021-05-25 12:49:40','2021-05-25 12:49:40'),
(1285,56,706,707,'','','直接上级->二级部门负责人',10,'2021-05-25 12:49:40','2021-05-25 12:49:40'),
(1286,56,707,711,'$p1 < 20000','getTotalAmount','金额 < 20000，二级部门负责人->AP(菲律宾)',10,'2021-05-25 12:49:41','2021-05-25 12:49:41'),
(1287,56,707,708,'','','二级部门负责人->一级部门负责人',10,'2021-05-25 12:49:42','2021-05-25 12:49:42'),
(1288,56,708,711,'$p1 < 50000','getTotalAmount','金额 < 50000, 一级部门负责人->AP(菲律宾)',10,'2021-05-25 12:49:42','2021-05-25 12:49:42'),
(1289,56,708,709,'','','一级部门负责人->所属公司负责人',10,'2021-05-25 12:49:43','2021-05-25 12:49:43'),
(1290,56,709,711,'$p1 < 200000','getTotalAmount','金额 < 200000, 所属公司负责人->AP(菲律宾)',10,'2021-05-25 12:49:43','2021-05-25 12:49:43'),
(1291,56,709,710,'','','所属公司负责人->COO/CPO',10,'2021-05-25 12:49:44','2021-05-25 12:49:44'),
(1292,56,710,711,'','','COO/CPO -> AP(菲律宾)',10,'2021-05-25 12:49:45','2021-05-25 12:49:45'),
(1293,56,711,712,'','','AP(菲律宾)->AP Supervisor(菲律宾)',10,'2021-05-25 12:49:45','2021-05-25 12:49:45'),
(1294,56,712,713,'','','AP Supervisor(菲律宾)->AP Supervisor(北京)',10,'2021-05-25 12:49:46','2021-05-25 12:49:46'),
(1295,56,713,719,'$p1 < 20000','getTotalAmount','金额 < 20000, AP Supervisor(北京) -> 结束',10,'2021-05-25 12:49:46','2021-05-25 12:49:46'),
(1296,56,713,714,'','','AP Supervisor（北京）-> Finance Manager',10,'2021-05-25 12:49:47','2021-05-25 12:49:47'),
(1297,56,714,715,'','','Finance Manager->Finance Senior Manager',10,'2021-05-25 12:49:48','2021-05-25 12:49:48'),
(1298,56,715,719,'$p1 < 50000','getTotalAmount','金额 < 50000, Finance Senior Manager->结束',10,'2021-05-25 12:49:48','2021-05-25 12:49:48'),
(1299,56,715,716,'','','Finance Senior Manager -> Finance Director',10,'2021-05-25 12:49:49','2021-05-25 12:49:49'),
(1300,56,716,719,'$p1 < 200000','getTotalAmount','金额 < 200000, Finance Director -> 结束',10,'2021-05-25 12:49:49','2021-05-25 12:49:49'),
(1301,56,716,717,'','','Finance Director -> CFO',10,'2021-05-25 12:49:50','2021-05-25 12:49:50'),
(1302,56,717,718,'','','CFO -> CEO',10,'2021-05-25 12:49:51','2021-05-25 12:49:51'),
(1303,56,718,719,'','','CEO -> 结束',10,'2021-05-25 12:49:51','2021-05-25 12:49:51'),
(1304,54,675,676,'','','员工提交->直接上级',10,'2021-05-25 12:58:58','2021-05-25 12:58:58'),
(1305,54,676,677,'','','直接上级->二级部门负责人',10,'2021-05-25 12:58:58','2021-05-25 12:58:58'),
(1306,54,677,681,'$p1 < 20000','getTotalAmount','金额 < 20000，二级部门负责人->AP(菲律宾)',10,'2021-05-25 12:58:59','2021-05-25 12:58:59'),
(1307,54,677,678,'','','二级部门负责人->一级部门负责人',10,'2021-05-25 12:59:00','2021-05-25 12:59:00'),
(1308,54,678,681,'$p1 < 50000','getTotalAmount','金额 < 50000, 一级部门负责人->AP(菲律宾)',10,'2021-05-25 12:59:00','2021-05-25 12:59:00'),
(1309,54,678,679,'','','一级部门负责人->所属公司负责人',10,'2021-05-25 12:59:01','2021-05-25 12:59:01'),
(1310,54,679,681,'$p1 < 200000','getTotalAmount','金额 < 200000, 所属公司负责人->AP(菲律宾)',10,'2021-05-25 12:59:02','2021-05-25 12:59:02'),
(1311,54,679,680,'','','所属公司负责人->COO/CPO',10,'2021-05-25 12:59:02','2021-05-25 12:59:02'),
(1312,54,680,681,'','','COO/CPO -> AP(菲律宾)',10,'2021-05-25 12:59:03','2021-05-25 12:59:03'),
(1313,54,681,682,'','','AP(菲律宾)->AP Supervisor(菲律宾)',10,'2021-05-25 12:59:04','2021-05-25 12:59:04'),
(1314,54,682,683,'','','AP Supervisor(菲律宾)->AP Supervisor(北京)',10,'2021-05-25 12:59:04','2021-05-25 12:59:04'),
(1315,54,683,689,'$p1 < 20000','getTotalAmount','金额 < 20000, AP Supervisor(北京) -> 结束',10,'2021-05-25 12:59:05','2021-05-25 12:59:05'),
(1316,54,683,684,'','','AP Supervisor（北京）-> Finance Manager',10,'2021-05-25 12:59:05','2021-05-25 12:59:05'),
(1317,54,684,685,'','','Finance Manager->Finance Senior Manager',10,'2021-05-25 12:59:06','2021-05-25 12:59:06'),
(1318,54,685,689,'$p1 < 50000','getTotalAmount','金额 < 50000, Finance Senior Manager->结束',10,'2021-05-25 12:59:07','2021-05-25 12:59:07'),
(1319,54,685,686,'','','Finance Senior Manager -> Finance Director',10,'2021-05-25 12:59:07','2021-05-25 12:59:07'),
(1320,54,686,689,'$p1 < 200000','getTotalAmount','金额 < 200000, Finance Director -> 结束',10,'2021-05-25 12:59:08','2021-05-25 12:59:08'),
(1321,54,686,687,'','','Finance Director -> CFO',10,'2021-05-25 12:59:09','2021-05-25 12:59:09'),
(1322,54,687,688,'','','CFO -> CEO',10,'2021-05-25 12:59:09','2021-05-25 12:59:09'),
(1323,54,688,689,'','','CEO -> 结束',10,'2021-05-25 12:59:10','2021-05-25 12:59:10'),
(1324,55,690,691,'','','员工提交->直接上级',10,'2021-05-25 12:59:28','2021-05-25 12:59:28'),
(1325,55,691,692,'','','直接上级->二级部门负责人',10,'2021-05-25 12:59:29','2021-05-25 12:59:29'),
(1326,55,692,696,'$p1 < 20000','getTotalAmount','金额 < 20000，二级部门负责人->AP(菲律宾)',10,'2021-05-25 12:59:29','2021-05-25 12:59:29'),
(1327,55,692,693,'','','二级部门负责人->一级部门负责人',10,'2021-05-25 12:59:30','2021-05-25 12:59:30'),
(1328,55,693,696,'$p1 < 50000','getTotalAmount','金额 < 50000, 一级部门负责人->AP(菲律宾)',10,'2021-05-25 12:59:31','2021-05-25 12:59:31'),
(1329,55,693,694,'','','一级部门负责人->所属公司负责人',10,'2021-05-25 12:59:31','2021-05-25 12:59:31'),
(1330,55,694,696,'$p1 < 200000','getTotalAmount','金额 < 200000, 所属公司负责人->AP(菲律宾)',10,'2021-05-25 12:59:32','2021-05-25 12:59:32'),
(1331,55,694,695,'','','所属公司负责人->COO/CPO',10,'2021-05-25 12:59:32','2021-05-25 12:59:32'),
(1332,55,695,696,'','','COO/CPO -> AP(菲律宾)',10,'2021-05-25 12:59:33','2021-05-25 12:59:33'),
(1333,55,696,697,'','','AP(菲律宾)->AP Supervisor(菲律宾)',10,'2021-05-25 12:59:34','2021-05-25 12:59:34'),
(1334,55,697,698,'','','AP Supervisor(菲律宾)->AP Supervisor(北京)',10,'2021-05-25 12:59:34','2021-05-25 12:59:34'),
(1335,55,698,704,'$p1 < 20000','getTotalAmount','金额 < 20000, AP Supervisor(北京) -> 结束',10,'2021-05-25 12:59:35','2021-05-25 12:59:35'),
(1336,55,698,699,'','','AP Supervisor（北京）-> Finance Manager',10,'2021-05-25 12:59:35','2021-05-25 12:59:35'),
(1337,55,699,700,'','','Finance Manager->Finance Senior Manager',10,'2021-05-25 12:59:36','2021-05-25 12:59:36'),
(1338,55,700,704,'$p1 < 50000','getTotalAmount','金额 < 50000, Finance Senior Manager->结束',10,'2021-05-25 12:59:37','2021-05-25 12:59:37'),
(1339,55,700,701,'','','Finance Senior Manager -> Finance Director',10,'2021-05-25 12:59:37','2021-05-25 12:59:37'),
(1340,55,701,704,'$p1 < 200000','getTotalAmount','金额 < 200000, Finance Director -> 结束',10,'2021-05-25 12:59:38','2021-05-25 12:59:38'),
(1341,55,701,702,'','','Finance Director -> CFO',10,'2021-05-25 12:59:39','2021-05-25 12:59:39'),
(1342,55,702,703,'','','CFO -> CEO',10,'2021-05-25 12:59:39','2021-05-25 12:59:39'),
(1343,55,703,704,'','','CEO -> 结束',10,'2021-05-25 12:59:40','2021-05-25 12:59:40'),
(1344,64,861,862,null,null,'员工提交->直线上级',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1345,64,862,863,'',null,'直接上级->二级部门负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1346,64,863,864,'','','二级部门负责人->一级部门负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1347,64,864,865,'','','一级部门负责人->所属公司负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1348,64,865,866,'','','所属公司负责人->COO/CPO',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1349,64,866,867,'$p1==\'th\'','getLang','COO/CPO->法务部门1=58040',10,'2020-11-07 11:35:00','2020-11-05 09:07:29'),
(1350,64,866,869,'$p1==\'en\'','getLang','COO/CPO->法务部门1=56883',10,'2020-11-05 09:08:37','2020-11-05 09:08:19'),
(1351,64,866,970,'$p1==\'zh-CN\'','getLang','COO/CPO->法务部门1=57335',10,'2020-11-05 09:09:23','2020-11-05 09:09:02'),
(1352,64,867,870,null,null,'法务部门1泰语=58040->法务部门2泰语=58040(跳过)->57335',10,'2020-11-07 11:36:31','2020-11-05 09:10:38'),
(1353,64,869,870,null,null,'法务部门1英语=56883->法务部门2英语=57335',10,'2020-11-05 09:14:53','2020-11-05 09:14:04'),
(1354,64,870,871,null,null,'57335->AP(菲律宾)',10,'2020-11-05 09:18:20','2020-11-05 09:16:54'),
(1355,64,871,872,null,null,'AP(菲律宾)->APS(北京)',10,'2020-11-05 09:18:39','2020-11-05 09:18:28'),
(1356,64,872,878,'$p1<50000000','getAmount','APS(北京)->结束',10,'2020-11-05 09:23:22','2020-11-05 09:21:56'),
(1357,64,872,873,'$p1>=50000000','getAmount','APS(北京)->FM',10,'2020-11-05 09:23:45','2020-11-05 09:23:32'),
(1358,64,873,874,'$p1>=50000000','getAmount','FM->FSM',10,'2020-11-05 09:24:16','2020-11-05 09:23:55'),
(1359,64,874,878,'$p1<*********','getAmount','FSM->结束',10,'2020-11-11 01:48:56','2020-11-05 09:24:30'),
(1360,64,874,875,'$p1>=*********','getAmount','FSM->FD',10,'2020-11-05 09:25:42','2020-11-05 09:25:07'),
(1361,64,875,878,'$p1<*********0','getAmount','FD->结束',10,'2020-11-10 04:22:52','2020-11-05 09:25:55'),
(1362,64,875,876,'$p1>=*********0','getAmount','FD->CFO',10,'2020-11-05 09:26:38','2020-11-05 09:26:32'),
(1363,64,876,877,null,null,'CFO->CEO',10,'2020-11-05 09:26:52','2020-11-05 09:26:47'),
(1364,64,877,878,null,null,'CEO->结束',10,'2020-11-05 09:27:09','2020-11-05 09:26:59'),
(1365,65,879,880,null,null,'员工提交->直线上级',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1366,65,880,881,'',null,'直接上级->二级部门负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1367,65,881,882,'','','二级部门负责人->一级部门负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1368,65,882,883,'','','一级部门负责人->所属公司负责人',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1369,65,883,884,'','','所属公司负责人->COO/CPO',10,'2021-05-20 08:15:07','2021-05-20 08:15:07'),
(1370,65,884,885,'$p1==3','getLang','COO/CPO->法务部门1中文',10,'2020-11-07 11:35:00','2020-11-05 09:07:29'),
(1371,65,884,886,'$p1==2','getLang','COO/CPO->法务部门1英文',10,'2020-11-05 09:08:37','2020-11-05 09:08:19'),
(1372,65,884,887,'$p1==1','getLang','COO/CPO->法务部门1泰文',10,'2020-11-05 09:09:23','2020-11-05 09:09:02'),
(1373,65,885,888,null,null,'法务部门1中文->法务部门2 中英文',10,'2020-11-06 07:08:15','2020-11-06 07:07:55'),
(1374,65,886,888,null,null,'法务部门1英文->法务部门2 中英文',10,'2020-11-06 07:08:44','2020-11-06 07:08:31'),
(1375,65,887,889,null,null,'法务部门1泰文->法务部门2 泰文',10,'2020-11-06 07:09:32','2020-11-06 07:08:53'),
(1376,65,888,890,null,null,'法务部门2 中英文->语言总审核',10,'2020-11-06 07:10:08','2020-11-06 07:09:49'),
(1377,65,889,890,null,null,'法务部门2 泰文->语言总审核',10,'2020-11-06 07:10:30','2020-11-06 07:10:18'),
(1378,65,890,891,null,null,'语言总审核->APS（菲律宾）',10,'2020-11-06 07:11:11','2020-11-06 07:10:48'),
(1379,65,891,892,null,null,'APS(菲律宾)->APS(北京)',10,'2020-11-06 07:11:38','2020-11-06 07:11:38'),
(1380,65,892,898,'$p1<200000','getAmount','APS(北京)->结束',10,'2020-11-05 09:23:22','2020-11-05 09:21:56'),
(1381,65,892,893,'$p1>=200000','getAmount','APS(北京)->FM',10,'2020-11-05 09:23:45','2020-11-05 09:23:32'),
(1382,65,893,894,'$p1>=200000','getAmount','FM->FSM',10,'2020-11-05 09:24:16','2020-11-05 09:23:55'),
(1383,65,894,898,'$p1<600000','getAmount','FSM->结束',10,'2020-11-11 01:48:56','2020-11-05 09:24:30'),
(1384,65,894,895,'$p1>=600000','getAmount','FSM->FD',10,'2020-11-05 09:25:42','2020-11-05 09:25:07'),
(1385,65,895,898,'($p1>=600000&&$p1<2000000)','getAmount','FD->结束',10,'2020-11-10 04:22:52','2020-11-05 09:25:55'),
(1386,65,895,896,'$p1>=2000000','getAmount','FD->CFO',10,'2020-11-05 09:26:38','2020-11-05 09:26:32'),
(1387,65,896,897,'$p1>=2000000',null,'CFO->CEO',10,'2020-11-05 09:26:52','2020-11-05 09:26:47'),
(1388,65,897,898,'$p1>=2000000',null,'CEO->结束',10,'2020-11-05 09:27:09','2020-11-05 09:26:59'),
(1389,66,899,900,'','','员工提交->直接上级',10,'2020-12-14 12:18:33','2020-12-14 07:13:33'),
(1390,66,900,901,'','','直接上级->二级部门负责人',10,'2020-12-14 12:18:27','2020-12-14 07:15:02'),
(1391,66,901,902,'','','二级部门负责人->一级部门负责人',10,'2020-12-14 11:30:26','2020-12-14 07:15:48'),
(1392,66,902,903,'','','一级部门负责人->所属公司负责人',10,'2020-12-14 12:10:41','2020-12-14 08:28:25'),
(1393,66,903,904,'','','所属公司负责人->COO/CPO',10,'2020-12-14 12:29:54','2020-12-14 08:32:51'),
(1394,66,904,905,'','','COO/CPO -> AP(菲律宾)',10,'2020-12-14 12:29:58','2020-12-14 08:33:51'),
(1395,66,905,906,'','','AP(菲律宾)->AP Supervisor(菲律宾)',10,'2020-12-14 12:30:01','2020-12-14 08:36:30'),
(1396,66,906,907,'','','AP Supervisor(菲律宾)->AP Supervisor(北京)',10,'2020-12-14 12:30:03','2020-12-14 08:36:34'),
(1397,66,907,913,'$p1<20000','getTotalAmount','金额 < 20000, AP Supervisor(北京) -> 结束',10,'2021-01-07 04:26:00','2020-12-14 08:52:36'),
(1398,66,907,908,'$p1>=20000','getTotalAmount','AP Supervisor（北京）-> Finance Manager',10,'2020-12-14 12:30:10','2020-12-14 08:40:54'),
(1399,66,908,909,'','','Finance Manager->Finance Senior Manager',10,'2020-12-14 12:30:13','2020-12-14 08:42:39'),
(1400,66,909,913,'$p1<50000','getTotalAmount','金额 < 50000, Finance Senior Manager->结束',10,'2021-01-07 04:26:03','2020-12-14 08:43:51'),
(1401,66,909,910,'','','Finance Senior Manager -> Finance Director',10,'2020-12-14 12:30:19','2020-12-14 08:45:11'),
(1402,66,910,913,'$p1 < 200000','getTotalAmount','金额 < 200000, Finance Director -> 结束',10,'2021-01-07 04:26:05','2020-12-14 08:47:37'),
(1403,66,910,911,'$p1 >=200000','getTotalAmount','Finance Director -> CFO',10,'2020-12-14 12:30:25','2020-12-14 08:49:13'),
(1404,66,911,912,'','','CFO -> CEO',10,'2020-12-14 12:30:29','2020-12-14 08:49:43'),
(1405,66,912,913,'','','CEO -> 结束',10,'2020-12-14 12:30:32','2020-12-14 08:54:03'),
(1406,58,814,815,null,null,'员工提交-> FM',10,'2021-05-14 09:53:09','2021-05-14 09:53:09'),
(1407,58,815,816,null,null,'FM->FSM',10,'2021-05-14 09:53:28','2021-05-14 09:53:18'),
(1408,58,816,817,null,null,'FSM->FD',10,'2021-05-14 09:53:57','2021-05-14 09:53:45'),
(1409,58,817,818,null,null,'FD->APO',10,'2021-05-14 09:54:16','2021-05-14 09:54:08'),
(1410,58,818,819,null,null,'APO->结束',10,'2021-05-14 09:54:38','2021-05-14 09:54:27');

## 配置表
insert into `setting_env`(`id`,`code`,`val`,`content`,`created_at`,`updated_at`) values
(24,'store_renting_payment_pay_staff_id','','付款管理 - 网点租房付款支付人清单','2020-12-14 03:20:43','2020-12-14 12:36:16'),
(26,'payment_store_renting_upload_template_zh-CN','https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623149178-96e0ccab2af548548bdd4adc00816a47.xlsx','付款管理 - 网点租房付款 - 金额详情上传模板 - 中文','2020-12-16 15:11:22','2021-05-24 15:39:02'),
(27,'payment_store_renting_upload_template_en','https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623149297-a1914218523046ff8c8ba4b3c07047dc.xlsx','付款管理 - 网点租房付款 - 金额详情上传模板 - 英文','2020-12-16 15:11:57','2021-05-24 15:39:02'),
(28,'payment_store_renting_upload_template_th','https://fex-ph-asset-dev.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1623149266-2e1f030852f84b799cb5bb22ac3a15c9.xlsx','付款管理 - 网点租房付款 - 金额详情上传模板 - 泰文','2020-12-16 15:12:30','2021-05-24 15:39:03'),
(30,'ordinary_payment_pay_staff_id','','普通付款付款支付人清单','2020-12-14 03:20:43','2021-02-01 02:25:28'),
(35,'ordinary_payment_vat7_rate','10,12','普通付款-vat7税率','2021-04-21 07:18:10','2021-04-21 07:20:14'),
(39,'payment_wht_tax','{"1":{"name":"房租","tax":[5]},"2":{"name":"专业服务","tax":[10]}}','WHT类别和对应汇率','2021-05-19 14:31:57','2021-05-19 14:31:57'),
(40, 'budget_status', '0', '预算开关', '2021-06-09 02:29:50', '2021-06-09 02:29:50'),
(41, 'contract_attachment_address', '<EMAIL>', '合同邮件发送', '2021-06-09 02:30:32', '2021-06-09 03:35:36');



insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`) values (347, 33, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(348, 33, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL),
(349, 33, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, NULL, NULL, '', NULL),
(350, 33, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL),
(351, 33, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(352, 33, 'coo/cpo', 3, 0, NULL, NULL, NULL, NULL, 7, '', NULL, '', NULL),
(353, 33, '中文审批（legal1中文）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(354, 33, '英文审批（legal1英文）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(355, 33, '菲律宾审批（legal1菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(356, 33, '中文、英文审批（legal2 中文）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(357, 33, '菲律宾审批 （legal2菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(358, 33, '语言总审核', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(359, 33, 'AP Supervisor(菲律宾)', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(360, 33, 'AP Supervisor(北京)', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(361, 33, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(362, 33, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(363, 33, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(364, 33, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(365, 33, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(366, 33, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),

(379, 35, '总部普通员工', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL),
(380, 35, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL),
(381, 35, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, NULL, NULL, '', NULL),
(382, 35, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL),
(383, 35, '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL),
(384, 35, 'COO/CPO', 3, 0, NULL, NULL, NULL, NULL, 7, '', NULL, '', NULL),
(385, 35, 'AP（菲律宾）', 3, 0, NULL, NULL, NULL, '5', 1, '', NULL, '{\"money_detail\":[\"wht_category\",\"wht_tax_rate\"]}', NULL),
(386, 35, 'AP Supervisor（菲律宾）', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '{\"money_detail\":[\"wht_category\",\"wht_tax_rate\"]}', NULL),
(387, 35, 'AP Supervisor（北京）', 3, 0, NULL, NULL, NULL, '4', 1, '', NULL, '{\"money_detail\":[\"wht_category\",\"wht_tax_rate\"]}', NULL),
(388, 35, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(389, 35, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(390, 35, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(391, 35, 'CFO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(392, 35, 'CEO', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(393, 35, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);

-- 审批关系
insert into `workflow_node_relate`(`id`,`flow_id`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`updated_at`,`created_at`) values(513, 33, 347, 348, NULL, NULL, '员工提交->直线上级', 10, '2020-11-06 06:12:51', '2020-11-06 06:12:37'),
(514, 33, 348, 349, NULL, NULL, '直接上级->二级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(515, 33, 349, 350, NULL, NULL, '二级部门负责人->一级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(516, 33, 350, 351, NULL, NULL, '一级部门负责人->所属公司负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(517, 33, 351, 353, '$p1<800000 && $p2==3', 'getAmount,getLang', '所属公司负责人->中文审批（legal1中文）', 10, '2021-06-04 12:50:03', '2020-11-06 06:13:37'),
(518, 33, 351, 354, '$p1<800000 && $p2==2', 'getAmount,getLang', '所属公司负责人->英文审批（legal1英文）', 9, '2021-06-04 12:50:05', '2020-11-06 06:13:37'),
(519, 33, 351, 355, '$p1<800000 && $p2==1', 'getAmount,getLang', '所属公司负责人->菲律宾语言审批（legal1菲律宾）', 8, '2021-06-04 12:50:08', '2020-11-06 06:13:37'),
(520, 33, 351, 352, '$p1<1500000', 'getAmount', '所属公司负责人->所属组织负责人', 7, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(521, 33, 351, 352, '$p1>=1500000', 'getAmount', '所属公司负责人->所属组织负责人', 6, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(522, 33, 352, 353, '$p1==3', 'getLang', '所属组织负责人-> 中文审批（legal1中文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(523, 33, 352, 354, '$p1==2', 'getLang', '所属组织负责人-> 英文审批（legal1英文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(524, 33, 352, 355, '$p1==1', 'getLang', '所属组织负责人-> 菲律宾语言审批（legal1菲律宾）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(525, 33, 353, 356, '$p1==3 || $p1==2', 'getLang', '中文审批（legal1中文）->中文、英文审批（legal2 中文）        3', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(526, 33, 353, 357, '$p1==1', 'getLang', '中文审批（legal1中文）->菲律宾语言审批 （legal2菲律宾）', 9, '2021-06-07 07:29:55', '2020-11-06 06:13:37'),
(527, 33, 354, 356, '$p1==3 || $p1==2', 'getLang', '英文审批（legal1英文）-> 中文、英文审批（legal2 中文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(528, 33, 354, 357, '$p1==1', 'getLang', '英文审批（legal1英文）-> 菲律宾语言审批 （legal2菲律宾）', 9, '2021-06-07 07:30:05', '2020-11-06 06:13:37'),
(529, 33, 355, 356, '$p1==3 || $p1==2', 'getLang', '菲律宾语言审批（legal1菲律宾）-> 中文、英文审批（legal2 中文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(530, 33, 355, 357, '$p1==1', 'getLang', '菲律宾语言审批（legal1菲律宾）-> 中文、英文审批（legal2 中文）', 9, '2021-06-07 07:30:10', '2020-11-06 06:13:37'),
(531, 33, 356, 358, NULL, NULL, '中文、英文审批（legal2 中文）->语言总审核', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(532, 33, 357, 358, NULL, NULL, '菲律宾语言审批（legal1菲律宾）-> 语言总审核', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(533, 33, 358, 359, NULL, NULL, '语言总审核->AP Supervisor(菲律宾)', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(534, 33, 359, 360, NULL, NULL, 'AP Supervisor(菲律宾) ->AP Supervisor(北京)', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(535, 33, 360, 361, NULL, NULL, 'AP Supervisor(北京)->Finance Manager', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(536, 33, 361, 362, NULL, NULL, 'Finance Manager->Finance Senior Manager', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(537, 33, 362, 363, NULL, NULL, 'Finance Senior Manager->Finance Director', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(538, 33, 363, 364, NULL, NULL, 'Finance Director->CFO', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(539, 33, 364, 365, '$p1>=1500000', 'getAmount', 'CFO->CEO', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(540, 33, 364, 366, NULL, NULL, 'CFO->结束', 9, '2021-06-07 07:30:14', '2020-11-06 06:13:37'),
(541, 33, 365, 366, NULL, NULL, 'CEO->结束', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),

(561, 35, 379, 380, NULL, NULL, '员工提交->直线上级', 10, '2020-11-06 06:12:51', '2020-11-06 06:12:37'),
(562, 35, 380, 381, NULL, NULL, '直接上级->二级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(563, 35, 381, 382, NULL, NULL, '二级部门负责人->一级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(564, 35, 382, 383, NULL, NULL, '一级部门负责人->所属公司负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(565, 35, 383, 385, '$p1<800000', 'getTotalAmount', '所属公司负责人->AP（菲律宾）', 10, '2021-06-04 12:50:41', '2020-11-06 06:13:37'),
(566, 35, 383, 384, '$p1<1500000', 'getTotalAmount', '所属公司负责人->所属组织负责人', 9, '2021-06-07 07:31:15', '2020-11-06 06:13:37'),
(567, 35, 383, 384, '$p1>=1500000', 'getTotalAmount', '所属公司负责人->所属组织负责人', 8, '2021-06-07 07:33:29', '2020-11-06 06:13:37'),
(568, 35, 384, 385, NULL, NULL, '所属组织负责人->AP（菲律宾）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(569, 35, 385, 386, NULL, NULL, 'AP（菲律宾）-> AP Supervisor（菲律宾）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(570, 35, 386, 387, NULL, NULL, 'AP Supervisor(菲律宾)->AP Supervisor（北京）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(571, 35, 387, 388, NULL, NULL, 'AP Supervisor(北京)->Finance Manager', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(572, 35, 388, 389, NULL, NULL, 'Finance Manager->Finance Senior Manager', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(573, 35, 389, 390, NULL, NULL, 'Finance Senior Manager->Finance Director', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(574, 35, 390, 391, NULL, NULL, 'Finance Director->CFO', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(575, 35, 391, 392, '$p1>=1500000', 'getTotalAmount', 'CFO->CEO', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37'),
(576, 35, 391, 393, NULL, NULL, 'CFO->结束', 9, '2021-06-07 07:42:16', '2020-11-06 06:13:37'),
(577, 35, 392, 393, NULL, NULL, 'CEO->结束', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');

-- 泰国单独执行：OA

-- 普通付款和网点合同配置
insert into `setting_env`(`code`,`val`,`content`,`created_at`,`updated_at`) values
('payment_wht_tax','{"1":{"name":"PND3","tax":[2,3,5]},"2":{"name":"PND53","tax":[1,2,3,5]},"3":{"name":"/","tax":[0]}}','WHT类别和对应汇率','2021-05-19 14:31:57','2021-05-19 14:31:57'),
('budget_status', '1', '预算开关', '2021-06-09 02:29:50', '2021-06-09 02:39:23'),
('contract_attachment_address', '<EMAIL>', '合同邮件发送', '2021-06-09 02:30:32', '2021-06-09 02:31:20');

-- 泰国

-- pro生产环境需要执行的SQL


--  PMD部门调整sql:


update workflow_node set name = 'pmd部门  35387（Commercial and Contract Manager）', `auditor_type` = 1, `auditor_id` = '35387' where `id`  = 805;



INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES ('915', '50', 'pmd部门  33477（Deputy Director）', '3', '0', NULL, NULL, NULL, NULL, '1', '33477', NULL, '', NULL);


INSERT INTO `workflow_node_relate` (`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ('50', '915', '806', NULL, NULL, '', '0', '2021-05-07 07:33:12', '2021-04-27 09:58:28');


update workflow_node_relate set `to_node_id`  = '915'  where `id`  = 970 and `flow_id` = 50;




--  SHOP部门：

        update workflow_node_relate set `valuate_formula`  = '$p1 > 50 || $p2 < 2 && $p2 > 0 || $p3 != 0 || $p4 > 10 || $p5 == 3 || in_array($p6, [1, 2]) || $p7', `valuate_code` = 'getKEY3,getKEY5,getKEY6,getKEY4,getKEY2,getKEY8,getKEY7'   where `id`  = 975 and `flow_id` = 50;


-- network部门:

        update workflow_node_relate set `valuate_formula`  = '$p1 > 15 || $p2 == 3 || in_array($p3, [1, 2])', `valuate_code` = 'getKEY4,getKEY2,getKEY8'   where `id`  = 981 and `flow_id` = 50;








