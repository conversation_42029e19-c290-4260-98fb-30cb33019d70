-- oa 数据库
ALTER TABLE wages ADD `exp_company_id` int(10) DEFAULT NULL COMMENT '费用所属公司id';
ALTER TABLE wages ADD `exp_company_name` varchar(100) DEFAULT NULL COMMENT '费用所属公司名称';
--

-- 供应商 start
CREATE TABLE `supplier_certificate_type` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `translation_code` varchar(128) NOT NULL DEFAULT '' COMMENT '证件类型code标识（唯一），同词库翻译key',
  `name_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '证件类型：中文名',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-未删除；1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_translation_code` (`translation_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商 - 证件类型';

INSERT INTO `supplier_certificate_type`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (1, 'certificate_type.1', 'Tax ID', 0, '2021-05-30 14:15:52');
INSERT INTO `supplier_certificate_type`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (2, 'certificate_type.2', '统一社会信用代码证', 0, '2021-05-30 14:15:59');
INSERT INTO `supplier_certificate_type`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (3, 'certificate_type.3', '身份证', 0, '2021-05-30 14:16:06');
INSERT INTO `supplier_certificate_type`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (4, 'certificate_type.4', '护照', 0, '2021-05-30 14:16:12');


CREATE TABLE `supplier_company_nature` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `translation_code` varchar(128) NOT NULL DEFAULT '' COMMENT '证件类型code标识（唯一），同词库翻译key',
  `name_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '证件类型：中文名',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-未删除；1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_translation_code` (`translation_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商 - 公司性质';

INSERT INTO `supplier_company_nature`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (1, 'company_nature.1', '自然人', 0, '2021-05-30 14:23:49');
INSERT INTO `supplier_company_nature`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (2, 'company_nature.2', '公司', 0, '2021-05-30 14:23:55');


CREATE TABLE `supplier_ownership` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `translation_code` varchar(128) NOT NULL DEFAULT '' COMMENT '证件类型code标识（唯一），同词库翻译key',
  `name_zh` varchar(255) NOT NULL DEFAULT '' COMMENT '证件类型：中文名',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0-未删除；1-已删除',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_translation_code` (`translation_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商 - 归属地';

INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (1, 'vendor_ownership.1', '中国', 0, '2021-05-30 14:27:38');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (2, 'vendor_ownership.2', '泰国', 0, '2021-05-30 14:27:45');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (3, 'vendor_ownership.3', '中国(香港)', 0, '2021-05-30 14:28:54');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (4, 'vendor_ownership.4', '老挝', 0, '2021-05-30 14:28:49');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (5, 'vendor_ownership.5', '菲律宾', 0, '2021-05-30 14:29:04');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (6, 'vendor_ownership.6', '马来西亚', 0, '2021-05-30 14:29:18');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (7, 'vendor_ownership.7', '越南', 0, '2021-05-30 14:29:31');
INSERT INTO `supplier_ownership`(`id`, `translation_code`, `name_zh`, `is_del`, `create_time`) VALUES (8, 'vendor_ownership.8', '新加坡', 0, '2021-05-30 14:29:42');

CREATE TABLE `supplier_bank` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `bank_code` varchar(64) NOT NULL DEFAULT '' COMMENT '多语言key',
  `bank_name` varchar(128) NOT NULL DEFAULT '' COMMENT '开户行名称',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_bank_code` (`bank_code`)
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COMMENT='供应商-开户行';

INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (34, 'bank.34', '曼谷银行', 0, '2021-05-30 13:56:29');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (35, 'bank.35', '摩根大通集团', 0, '2021-05-30 13:56:54');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (36, 'bank.36', '三井住友银行', 0, '2021-05-30 13:57:05');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (37, 'bank.37', '兆丰国际商业银行股份有限公司', 0, '2021-05-30 13:57:16');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (38, 'bank.38', '美国银行，国家', 0, '2021-05-30 13:57:27');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (39, 'bank.39', '德意志银行', 0, '2021-05-30 13:57:36');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (40, 'bank.40', '泰国的政府住房银行', 0, '2021-05-30 13:57:44');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (41, 'bank.41', '农业合作银行', 0, '2021-05-30 13:57:54');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (42, 'bank.42', '瑞穗实业银行', 0, '2021-05-30 13:58:05');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (43, 'bank.43', '法国巴黎银行', 0, '2021-05-30 13:58:14');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (44, 'bank.44', '泰国伊斯兰银行', 0, '2021-05-30 13:58:25');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (45, 'bank.45', '泰国信贷零售银行', 0, '2021-05-30 13:58:33');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (46, 'bank.46', '澳新银行', 0, '2021-05-30 13:58:41');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (47, 'bank.47', '三菱日联银行', 0, '2021-05-30 13:58:49');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (48, 'bank.48', '华侨银行', 0, '2021-05-30 13:58:59');


ALTER TABLE `vendor`
ADD COLUMN `bank_account_name` varchar(255) NOT NULL DEFAULT '' COMMENT '银行账户名称' AFTER `bank_no`,
ADD COLUMN `sap_supplier_no` varchar(255) NOT NULL DEFAULT '' COMMENT 'SAP供应商号码' AFTER `bank_account_name`;

-- 供应商 end

-- 预算 start
CREATE TABLE `budget_source_data` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `creator_id` int(10) unsigned NOT NULL COMMENT '创建者工号',
  `creator_name` varchar(128) NOT NULL COMMENT '创建者姓名',
  `creator_department_id` int(10) unsigned NOT NULL COMMENT '创建者部门id',
  `creator_department_name` varchar(128) NOT NULL DEFAULT '' COMMENT '创建者部门名称',
  `create_date` date NOT NULL COMMENT '创建日期',
  `budget_object_name` varchar(128) NOT NULL COMMENT '预算科目名称',
  `budget_object_code` varchar(128) NOT NULL DEFAULT '' COMMENT '预算科目标识',
  `budget_department_name` varchar(255) NOT NULL COMMENT '预算部门名称',
  `budget_department_id` int(11) NOT NULL COMMENT '预算部门id',
  `budget_department_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '预算部门类型：对应sys_department表的type',
  `budget_department_level` int(10) unsigned NOT NULL COMMENT '预算部门层级：对应sys_department表的level',
  `organization_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '组织架构类型：1-网点；2-总部',
  `amount_type` tinyint(1) unsigned NOT NULL COMMENT '预算额度是否共用：1-不共用；2-共用',
  `budget_year` year(4) NOT NULL COMMENT '预算年份',
  `jan_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '一月预算额度，单位 元*1000倍',
  `feb_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '二月预算额度，单位 元*1000倍',
  `mar_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '三月预算额度，单位 元*1000倍',
  `apr_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '四月预算额度，单位 元*1000倍',
  `may_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '五月预算额度，单位 元*1000倍',
  `jun_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '六月预算额度，单位 元*1000倍',
  `jul_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '七月预算额度，单位 元*1000倍',
  `aug_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '八月预算额度，单位 元*1000倍',
  `sep_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '九月预算额度，单位 元*1000倍',
  `oct_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '十月预算额度，单位 元*1000倍',
  `nov_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '十一月预算额度，单位 元*1000倍',
  `dec_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '十二月预算额度，单位 元*1000倍',
  `currency` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '预算金额币种：默认泰铢',
  `data_source` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '数据来源：1-导入；2-表单创建；0-未知',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `base_source_data_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '基础原数据id：针对表单复制新增的数据',
  PRIMARY KEY (`id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_create_date` (`create_date`),
  KEY `idx_budget_year` (`budget_year`),
  KEY `idx_budget_department_id` (`budget_department_id`),
  KEY `idx_budget_object_code` (`budget_object_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预算源数据记录表';


update budget_object_department_amount set amount= 0, amount_left = 0 where department_id = 15 and month in ('2021-07', '2021-08', '2021-09', '2021-10', '2021-11', '2021-12');

-- 预算 end


-- 报销总部 申请
insert into workflow_node (id, flow_id, name, type, auditor_type, auditor_id) value
    (914, 39, 'Flash HR、CPO下泰国部门HRBP', 3, 1, 28228);
insert into workflow_node_relate (flow_id, from_node_id, to_node_id, valuate_formula, valuate_code, remark, sort)
values
(39, 441, 914, '$p1 == 1', 'getKEY', '所属组织负责人->HRBP Director Flash HR、CPO下泰国部门HRBP',20),
(39, 440, 914, '$p1 == 1 && $p2 < 200000000 && $p3 == 1', 'getReimbursementType,getAmount,getKEY', '员工福利/团建费 且 金额 < 200000: 所属公司负责人->HRBP Director Flash HR、CPO下泰国部门HRBP', 20),
(39, 439, 914, '$p1 == 1 && $p2 < 100000000 && $p3 == 1', 'getReimbursementType,getAmount,getKEY', '员工福利/团建费 且 金额 < 100000：一级部门负责人->HRBP Director Flash HR、CPO下泰国部门HRBP',20),
(39, 438, 914, '$p1 == 1 && $p2 < 10000000 && $p3 == 1', 'getReimbursementType,getAmount,getKEY', '员工福利/团建费 且 金额 < 10000: 二级部门负责->HRBP Director Flash HR、CPO下泰国部门HRBP',20),
(39, 914, 443, '$p1 == 1', 'getKEY', 'HRBP Director->CPO Flash HR、CPO下泰国部门HRBP ', 20),
(39, 914, 444, '$p1 == 1 && $p2 < 100000000 && $p3 == 1', 'getReimbursementType,getAmount,getKEY', '员工福利/团建费 且 金额 < 100000: HRBP Director->AP（泰国）Flash HR、CPO下泰国部门HRBP', 20)
;