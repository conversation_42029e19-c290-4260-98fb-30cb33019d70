-- 角色权限表
CREATE TABLE `role_permission` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
                                   `permission_ids` text COMMENT '角色权限列表',
                                   `is_granted` tinyint(4) DEFAULT NULL,
                                   `last_updated_at` datetime DEFAULT NULL,
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限表';


-- 所有国家
-- 9576
ALTER TABLE `contract_store_renting`
ADD COLUMN `due_date` varchar(12) DEFAULT '' COMMENT '应付日期（菲律宾）',
ADD COLUMN `amount_paid` float(10, 0) NULL DEFAULT 0 COMMENT '已付金额（菲律宾）',
ADD COLUMN `contract_tax_no` varchar(100) NULL COMMENT '房东税号',
ADD COLUMN `land_tax_payer` tinyint(1) NULL DEFAULT NULL COMMENT '土地税付款人1房东2公司',
ADD COLUMN `fire_insurance_payer` tinyint(1) NULL DEFAULT NULL COMMENT '火灾保险费付款人1房东2公司',
ADD COLUMN `antimoth_payer` tinyint(1) NULL DEFAULT NULL COMMENT '房屋杀虫防蛀费付款人1房东2公司',
ADD COLUMN `land_tax_amount` varchar(50) NOT NULL DEFAULT 0 COMMENT '土地税金额',
ADD COLUMN `fire_insurance_amount` varchar(50) NOT NULL DEFAULT 0 COMMENT '火灾保险费金额',
ADD COLUMN `antimoth_amount` varchar(50) NOT NULL DEFAULT 0 COMMENT '房屋杀虫防蛀费金额';

ALTER TABLE `contract_store_renting`
MODIFY COLUMN `contract_deadline` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '合同期限',
MODIFY COLUMN `contract_effect_date` date NULL COMMENT '合同生效日期',
MODIFY COLUMN `pay_amount` float(10, 2) NOT NULL DEFAULT 0 COMMENT '付多少',
MODIFY COLUMN `signer_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签署人联系电话',
MODIFY COLUMN `contract_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '合同名称',
MODIFY COLUMN `contract_total_amount` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL  COMMENT '合同总金额改名合同总金额（含WHT含VAT)';


-- 审批流
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (77, '租房合同（LOI意向书的审批流）-network部门', 1, '租房合同（LOI意向书的审批流-network部门', '2021-08-04 12:34:27');
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (78, '租房合同（LOI意向书的审批流）-hub部门', 1, '租房合同（LOI意向书的审批流）-hub部门',  '2021-08-04 12:35:25');
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (79, '租房合同（LOI意向书的审批流）-other', 1, '租房合同（LOI意向书的审批流）-other', '2021-08-04 12:36:22');

INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1456, 77,  '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1457, 77,  '指定工号117197（Tim）', 3, 0, NULL, NULL, NULL, NULL, 1, '117197', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1458, 77,  '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1459, 77,  '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1460, 77,  'AP Supervisor(菲律宾)', 3, 0, NULL, NULL, NULL, NULL, 1, '117259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1461, 77,  'Inigo / Niquee', 3, 0, NULL, NULL, NULL, NULL, 1, '118754,118991', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1462, 77,  'Niquee / Amanda', 3, 0, NULL, NULL, NULL, NULL, 1, '118991,57335', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1463, 77,  '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);


INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1456, 1457, NULL, NULL, '员工提交 -> 指定工号117197（Tim）', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1457, 1458, NULL, NULL, '指定工号117197（Tim） -> 一级部门负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1458, 1459, NULL, NULL, '一级部门负责人 -> 所属公司负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1459, 1460, NULL, NULL, '所属公司负责人 -> AP Supervisor(菲律宾)', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1460, 1461, NULL, NULL, 'AP Supervisor(菲律宾) -> Inigo / Niquee', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1461, 1462, NULL, NULL, 'Inigo / Niquee -> Niquee / Amanda', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 77, 1462, 1463, NULL, NULL, 'Niquee / Amanda -> 结束', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');



INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1464, 78,  '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1465, 78,  '指定工号117157（Jay）', 3, 0, NULL, NULL, NULL, NULL, 1, '117157', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1466, 78,  '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1467, 78,  '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1468, 78,  'AP Supervisor(菲律宾)', 3, 0, NULL, NULL, NULL, NULL, 1, '119127', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1469, 78,  'Inigo / Niquee', 3, 0, NULL, NULL, NULL, NULL, 1, '118754,118991', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1470, 78,  'Niquee / Amanda', 3, 0, NULL, NULL, NULL, NULL, 1, '118991,57335', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1471, 78,  '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);


INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1464, 1465, NULL, NULL, '员工提交 -> 指定工号20017157（Jay））', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1465, 1466, NULL, NULL, '指定工号117157（Jay）） -> 一级部门负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1466, 1467, NULL, NULL, '一级部门负责人 -> 所属公司负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1467, 1468, NULL, NULL, '所属公司负责人 -> AP Supervisor(菲律宾)', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1468, 1469, NULL, NULL, 'AP Supervisor(菲律宾) -> Inigo / Niquee', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1469, 1470, NULL, NULL, 'Inigo / Niquee -> Niquee / Amanda', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 78, 1470, 1471, NULL, NULL, 'Niquee / Amanda -> 结束', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');




INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1472, 79,  '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1473, 79,  '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1474, 79,  '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1475, 79,  '所属公司负责人', 3, 0, NULL, NULL, NULL, NULL, 16, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1476, 79,  'AP Supervisor(菲律宾)', 3, 0, NULL, NULL, NULL, NULL, 1, '119127', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1477, 79,  'Inigo / Niquee', 3, 0, NULL, NULL, NULL, NULL, 1, '118754,118991', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1478, 79,  'Niquee / Amanda', 3, 0, NULL, NULL, NULL, NULL, 1, '118991,57335', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`,  `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1479, 79,  '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);


INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1472, 1473, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1473, 1474, NULL, NULL, '直接上级 -> 一级部门负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1474, 1475, NULL, NULL, '一级部门负责人 -> 所属公司负责人', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1475, 1476, NULL, NULL, '所属公司负责人 -> AP Supervisor(菲律宾)', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1476, 1477, NULL, NULL, 'AP Supervisor(菲律宾) -> Inigo / Niquee', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1477, 1478, NULL, NULL, 'Inigo / Niquee -> Niquee / Amanda', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 79, 1478, 1479, NULL, NULL, 'Niquee / Amanda -> 结束', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');

-- 银行流水需求 Start
-- 银行表
CREATE TABLE `bank_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_name` varchar(100) NOT NULL DEFAULT '' COMMENT '银行名字',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0不是，1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bank_name` (`bank_name`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='银行列表';


-- 账号表
CREATE TABLE `bank_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '银行id',
  `company_name` varchar(128) NOT NULL DEFAULT '' COMMENT '公司名字',
  `account` varchar(100) NOT NULL DEFAULT '' COMMENT '银行账号',
  `currency` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '1泰铢，2美元，3人民币',
  `left_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '剩余金额',
  `left_amount_date` date DEFAULT NULL COMMENT '账号余额更新日期',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0不是，1是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新人工号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account` (`account`) USING BTREE COMMENT '银行账号唯一索引',
  KEY `idx_bank_id` (`bank_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行账号及剩余金额';

-- 费用类型表
CREATE TABLE `bank_flow_expense` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL COMMENT '费用类型名字',
  `name_lang_key` varchar(64) NOT NULL COMMENT '费用类型lang key',
  `is_pay` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否是付款',
  `is_get` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否是收款',
  `is_batch` tinyint(2) unsigned NOT NULL DEFAULT '1' COMMENT '是否允许批量操作',
  `relation_item` varchar(256) NOT NULL DEFAULT '' COMMENT '该费用类型关联的枚举项：空表示无',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除：0-否；1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_name_lang_key` (`name_lang_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行流水 - 费用类型配置表';

-- 枚举扩展配置表
CREATE TABLE `bank_flow_enum` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item` varchar(32) NOT NULL DEFAULT '' COMMENT '枚举key(比如jp_type)',
  `value_lang_key` varchar(64) NOT NULL DEFAULT '' COMMENT '枚举值对应lang_key',
  `value` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '枚举值',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '枚举名',
  `sort` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '排序，正序',
  `is_default` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否默认:0-否；1-是',
  `is_deleted` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除:0-否；1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_value_lang_key` (`value_lang_key`),
  KEY `idx_item_sort` (`item`,`sort`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行流水 - 通用枚举表';

-- 银行流水主表
CREATE TABLE `bank_flow` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '1收入，2支出',
  `bank_id` int(11) NOT NULL COMMENT '银行id',
  `bank_name` varchar(255) NOT NULL DEFAULT '' COMMENT '银行名字',
  `bank_account_id` int(11) NOT NULL COMMENT '银行账号id',
  `bank_account` varchar(255) NOT NULL DEFAULT '' COMMENT '银行账号',
  `date` date NOT NULL COMMENT '交易日期 2021-05-21',
  `time` time NOT NULL COMMENT '交易时间 18:00:00（填的什么是什么） 银行时间',
  `ticket_no` varchar(100) NOT NULL DEFAULT '' COMMENT '支票号码',
  `get_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '收款金额',
  `pay_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '付款金额',
  `real_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '实际金额，带上type，有正负',
  `bank_left_amount` decimal(20,2) NOT NULL DEFAULT '0.00' COMMENT '银行账号余额',
  `currency` tinyint(2) NOT NULL DEFAULT '1' COMMENT '币种，通用的枚举类型（1泰铢，2美元，3人民币，）',
  `trade_desc` varchar(160) NOT NULL DEFAULT '' COMMENT '描述(长度160)',
  `bank_flow_expense_id` int(11) NOT NULL DEFAULT '0' COMMENT '银行流水费用类型id，默认0，灭有',
  `edit_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '编辑状态，1待编辑，2已编辑',
  `confirm_status` tinyint(2) DEFAULT '1' COMMENT '确认状态，1待确认，2已确认',
  `updated_staff_id` int(11) DEFAULT NULL COMMENT '更新人',
  `updated_staff_name` varchar(255) DEFAULT NULL COMMENT '更新人名字',
  `created_staff_id` int(11) DEFAULT NULL COMMENT '创建人',
  `created_staff_name` varchar(255) DEFAULT NULL COMMENT '创建人名字',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_ticket_no` (`ticket_no`),
  KEY `idx_desc` (`trade_desc`),
  KEY `idx_trade_date` (`date`),
  KEY `idx_type_account_date` (`type`,`bank_account_id`,`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行流水日志表';

-- 流水附件表
CREATE TABLE `bank_flow_attachment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_flow_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '银行流水id',
  `bucket_name` varchar(255) NOT NULL DEFAULT '',
  `object_key` varchar(255) NOT NULL DEFAULT '',
  `file_name` varchar(255) NOT NULL DEFAULT '',
  `is_deleted` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除，0不是，1是',
  `created_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '上传人id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
  `deleted_id` int(11) unsigned DEFAULT '0' COMMENT '删除人工号',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_bank_flow_id` (`bank_flow_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行流水附件表';

-- 流水扩展信息表
CREATE TABLE `bank_flow_meta` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_flow_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT 'bank_flow表id',
  `item` varchar(64) NOT NULL DEFAULT '' COMMENT 'key',
  `val` varchar(128) NOT NULL DEFAULT '' COMMENT 'value',
  `created_id` int(11) unsigned NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_bank_item_val` (`bank_flow_id`,`item`,`val`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流水日志扩展表 - 编辑页费用类型的联动字段';

-- 收款流水明细表
CREATE TABLE `bank_flow_get_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_flow_id` int(11) DEFAULT NULL COMMENT '银行流水id',
  `no` varchar(255) DEFAULT NULL COMMENT '账单号',
  `ms_client` varchar(255) DEFAULT NULL COMMENT 'ms客户',
  `store` varchar(255) DEFAULT NULL COMMENT '网点',
  `pay_type` varchar(255) DEFAULT NULL COMMENT '支付类型',
  `fbi_id` varchar(255) DEFAULT NULL COMMENT 'fbi汇款id',
  `link_name` varchar(255) DEFAULT NULL COMMENT '关联方名称',
  `client_code` varchar(255) DEFAULT NULL COMMENT '客户代码',
  `amount` varchar(255) DEFAULT NULL COMMENT '金额',
  `created_id` int(11) DEFAULT NULL COMMENT '操作人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_bank_flow_id` (`bank_flow_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行收款流水-关联明细';

-- 付款流水明细表
CREATE TABLE `bank_flow_pay_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_flow_id` int(11) DEFAULT NULL COMMENT '银行流水id',
  `exv_id` varchar(100) DEFAULT NULL COMMENT 'exv号',
  `apply_amount` varchar(255) DEFAULT NULL COMMENT '申请金额',
  `no` varchar(255) DEFAULT NULL COMMENT '差额报销单号',
  `diff_amount` varchar(255) DEFAULT NULL COMMENT '差额金额',
  `apply_staff_id` varchar(255) DEFAULT NULL COMMENT '申请人工号',
  `store` varchar(255) DEFAULT NULL COMMENT '网点',
  `link_name` varchar(255) DEFAULT NULL COMMENT '关联方名称',
  `created_id` int(11) DEFAULT NULL COMMENT '创建人id',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_exv_id` (`exv_id`) USING BTREE,
  KEY `idx_bank_flow_id` (`bank_flow_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行付款流水-关联明细';

-- 付款流水系统单号表
CREATE TABLE `bank_flow_oa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `bank_flow_id` int(11) DEFAULT NULL COMMENT '银行流水id',
  `oa_type` int(11) DEFAULT NULL COMMENT '关联模块type(暂定同审批流type)',
  `oa_value` int(11) DEFAULT NULL COMMENT '关联模块主键id',
  `no` varchar(255) DEFAULT NULL COMMENT '关联单号',
  `amount` decimal(20,2) DEFAULT NULL COMMENT '金额',
  `currency` tinyint(2) DEFAULT NULL COMMENT '币种',
  `real_amount` decimal(20,2) DEFAULT NULL COMMENT '转换成银行流水，对应的货币金额',
  `diff_amount` decimal(20,2) DEFAULT NULL COMMENT '差异金额，一致的有用，不一致的时候只是展示',
  `diff_info` varchar(255) DEFAULT '' COMMENT '差异说明',
  `is_cancel` tinyint(2) DEFAULT '0' COMMENT '是否撤销，0不是，1是',
  `is_sure` tinyint(2) DEFAULT '0' COMMENT '可抵扣确认，0不是，1是',
  `created_id` int(11) DEFAULT NULL COMMENT '创建人id',
  `updated_id` int(11) DEFAULT NULL COMMENT '更新人id',
  `bank_account_id` int(11) DEFAULT NULL COMMENT '银行流水交易账号id（费用表线上）',
  `date` date DEFAULT NULL COMMENT '银行流水交易日期(费用表线上)',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bank_flow_id` (`bank_flow_id`) USING BTREE,
  KEY `idx_oa_type_date` (`oa_type`,`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行付款流水关联系统单号';

-- 日志表
CREATE TABLE `bank_flow_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_uri` varchar(100) DEFAULT NULL COMMENT '接口路径',
  `request_params` varchar(255) DEFAULT NULL COMMENT '请求参数',
  `marks` varchar(255) DEFAULT NULL COMMENT '备注',
  `created_id` int(11) DEFAULT NULL COMMENT '操作人工号',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_uri` (`request_uri`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 银行清单
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (1, 'TMB', 0, '2021-06-22 12:36:44');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (2, 'SCB', 0, '2021-06-22 12:36:50');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (3, 'HSBC', 0, '2021-06-22 12:36:57');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (4, 'KBANK', 0, '2021-06-22 12:37:05');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (5, 'BAY', 0, '2021-06-22 12:37:11');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (6, 'BBL', 0, '2021-06-22 12:37:17');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (7, 'Tbank', 0, '2021-06-22 12:37:24');
INSERT INTO `bank_list`(`id`, `bank_name`, `is_deleted`, `created_at`) VALUES (8, 'BOC', 0, '2021-06-22 12:37:30');

-- 账号清单
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (1, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 0.00, '2021-07-29', 0, '2021-06-22 12:49:58', '2021-07-29 11:56:37', 20508);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (2, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, *********.18, '2021-07-20', 0, '2021-06-22 12:50:08', '2021-07-23 08:24:16', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (3, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, *********.60, '2021-07-20', 0, '2021-06-22 12:50:47', '2021-07-23 08:24:23', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (4, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 4713293.49, '2021-07-20', 0, '2021-06-22 12:50:56', '2021-07-23 08:24:31', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (5, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 12500.00, '2021-07-20', 0, '2021-06-22 12:51:24', '2021-07-23 08:37:24', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (6, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 4095927.00, '2021-07-20', 0, '2021-06-22 12:51:34', '2021-07-23 08:38:59', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (7, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 56632.43, '2021-07-20', 0, '2021-06-22 12:51:42', '2021-07-23 08:39:08', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (8, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 171505.87, '2021-07-20', 0, '2021-06-22 12:51:49', '2021-07-23 08:39:16', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (9, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 718390.74, '2021-07-20', 0, '2021-06-22 12:52:00', '2021-07-23 08:39:23', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (10, 1, 'FLASH EXPRESS CO.,LTD', '*********', 1, 70219.19, '2021-07-20', 0, '2021-06-22 12:52:08', '2021-07-23 08:39:30', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (11, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, 100.00, '2021-07-30', 0, '2021-06-22 12:52:46', '2021-07-30 02:03:47', 20508);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (12, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, *********.65, '2021-07-20', 0, '2021-06-22 12:52:55', '2021-07-23 07:10:10', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (13, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.32, '2021-07-20', 0, '2021-06-22 12:53:03', '2021-07-23 07:27:02', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (14, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.95, '2021-07-20', 0, '2021-06-22 12:53:11', '2021-07-23 07:33:22', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (15, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, 939469.07, '2021-07-20', 0, '2021-06-22 12:53:18', '2021-07-23 07:27:24', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (16, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.79, '2021-07-20', 0, '2021-06-22 12:53:25', '2021-07-23 07:27:32', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (17, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, 39721.28, '2021-07-20', 0, '2021-06-22 12:53:32', '2021-07-23 07:27:41', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (18, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, *********.59, '2021-07-20', 0, '2021-06-22 12:53:41', '2021-07-23 07:27:49', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (19, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.07, '2021-07-19', 0, '2021-06-22 12:53:51', '2021-07-22 06:37:29', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (20, 2, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.58, '2021-07-20', 0, '2021-06-22 12:53:58', '2021-07-23 07:30:57', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (21, 2, 'FLASH EXPRESS CO.,LTD', '*************', 2, 396.00, '2021-07-20', 0, '2021-06-22 12:54:06', '2021-07-23 08:15:29', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (22, 3, 'FLASH EXPRESS CO.,LTD', '************', 1, 50.00, '2021-07-29', 0, '2021-06-22 12:54:38', '2021-07-29 12:32:13', 20508);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (23, 3, 'FLASH EXPRESS CO.,LTD', '************', 2, 0.00, '2022-07-20', 0, '2021-06-22 12:54:45', '2021-07-23 08:56:29', 23656);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (24, 4, 'FLASH EXPRESS CO.,LTD', '**********', 1, 50.00, '2021-07-30', 0, '2021-06-22 12:55:15', '2021-07-30 05:52:24', 10000);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (25, 4, 'FLASH EXPRESS CO.,LTD', '**********', 1, ********.02, '2021-07-19', 0, '2021-06-22 12:55:22', '2021-07-23 07:49:59', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (26, 4, 'FLASH EXPRESS CO.,LTD', '**********', 2, 0.00, '2021-07-19', 0, '2021-06-22 12:55:30', '2021-07-22 06:32:52', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (27, 5, 'FLASH EXPRESS CO.,LTD', '**********', 1, 0.00, '2021-07-19', 0, '2021-06-22 12:55:58', '2021-07-22 06:32:52', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (28, 5, 'FLASH EXPRESS CO.,LTD', '**********', 1, 0.00, '2021-07-19', 0, '2021-06-22 12:56:06', '2021-07-22 06:32:52', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (29, 5, 'FLASH EXPRESS CO.,LTD', '**********', 2, 0.00, '2021-07-19', 0, '2021-06-22 12:56:13', '2021-07-22 06:32:52', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (30, 6, 'FLASH EXPRESS CO.,LTD', '*********', 1, 35036.17, '2021-07-19', 0, '2021-06-22 12:56:38', '2021-07-22 06:37:29', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (31, 6, 'FLASH EXPRESS CO.,LTD', '*********', 1, 439601.36, '2021-07-19', 0, '2021-06-22 12:56:45', '2021-07-22 06:37:29', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (32, 7, 'FLASH EXPRESS CO.,LTD', '**********', 1, 188543.29, '2021-07-19', 0, '2021-06-22 12:57:14', '2021-07-22 06:37:30', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (33, 7, 'FLASH EXPRESS CO.,LTD', '**********', 1, 1000.00, '2021-07-19', 0, '2021-06-22 12:57:21', '2021-07-22 06:37:30', 0);
INSERT INTO `bank_account`(`id`, `bank_id`, `company_name`, `account`, `currency`, `left_amount`, `left_amount_date`, `is_deleted`, `created_at`, `updated_at`, `updated_id`) VALUES (34, 8, 'FLASH EXPRESS CO.,LTD', '***************', 1, 0.00, '2021-07-19', 0, '2021-06-22 12:57:38', '2021-07-22 06:32:52', 0);


-- 费用类型
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (1, '内部转账', 'bank_flow.expense_type.1', 1, 1, 0, 'from_bank_acct_id,to_bank_acct_id', 0, '2021-06-22 14:24:56');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (2, '财务费用-银行服务费', 'bank_flow.expense_type.2', 1, 0, 1, '', 0, '2021-06-22 14:25:21');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (3, '财务费用-支票购买', 'bank_flow.expense_type.3', 1, 0, 1, '', 0, '2021-06-22 14:25:48');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (4, '一进一出', 'bank_flow.expense_type.4', 1, 1, 1, '', 0, '2021-06-22 14:26:15');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (5, '费用表支出-OA', 'bank_flow.expense_type.5', 1, 0, 1, '', 0, '2021-06-22 14:26:48');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (6, '赔款', 'bank_flow.expense_type.6', 1, 0, 1, '', 0, '2021-06-23 03:04:38');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (7, '油卡充值', 'bank_flow.expense_type.7', 1, 0, 1, '', 0, '2021-06-23 03:05:18');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (8, 'Line Haul加班车', 'bank_flow.expense_type.8', 1, 0, 1, '', 0, '2021-06-23 03:05:35');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (9, '支付工资', 'bank_flow.expense_type.9', 1, 0, 1, '', 0, '2021-06-23 03:05:50');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (10, '支付提成', 'bank_flow.expense_type.10', 1, 0, 1, '', 0, '2021-06-23 03:06:05');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (11, '利息代扣税', 'bank_flow.expense_type.11', 1, 0, 1, '', 0, '2021-06-23 03:06:21');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (12, '网络费-银行托收', 'bank_flow.expense_type.12', 1, 0, 1, '', 0, '2021-06-23 03:06:40');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (13, '还关联方款', 'bank_flow.expense_type.13', 1, 0, 1, '', 0, '2021-06-23 03:06:57');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (14, '财务费用-利息收入', 'bank_flow.expense_type.14', 0, 1, 1, '', 0, '2021-06-23 03:13:12');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (15, '定结收入款', 'bank_flow.expense_type.15', 0, 1, 1, '', 0, '2021-06-23 03:13:28');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (16, '现结收入款', 'bank_flow.expense_type.16', 0, 1, 1, '', 0, '2021-06-23 03:13:46');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (17, 'COD收货款', 'bank_flow.expense_type.17', 0, 1, 1, '', 0, '2021-06-23 03:14:03');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (18, '加盟商收款-余额充值', 'bank_flow.expense_type.18', 0, 1, 1, '', 0, '2021-06-23 03:14:17');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (19, '线下卖耗材收入', 'bank_flow.expense_type.19', 0, 1, 1, '', 0, '2021-06-23 03:14:36');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (20, '卖垃圾收入', 'bank_flow.expense_type.20', 0, 1, 1, '', 0, '2021-06-23 03:14:54');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (21, '卖工服收入', 'bank_flow.expense_type.21', 0, 1, 1, '', 0, '2021-06-23 03:15:08');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (22, '自动贩卖机服务费收款', 'bank_flow.expense_type.22', 0, 1, 1, '', 0, '2021-06-23 03:17:01');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (23, '自动贩卖机电费收款', 'bank_flow.expense_type.23', 0, 1, 1, '', 0, '2021-06-23 03:17:15');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (24, '无头件收入', 'bank_flow.expense_type.24', 0, 1, 1, '', 0, '2021-06-23 03:17:36');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (25, '房租', 'bank_flow.expense_type.25', 0, 1, 1, '', 0, '2021-06-23 03:17:51');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (26, '仓储服务费', 'bank_flow.expense_type.26', 0, 1, 1, '', 0, '2021-06-23 03:23:15');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (27, '出租食堂收入', 'bank_flow.expense_type.27', 0, 1, 1, '', 0, '2021-06-23 03:23:31');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (28, '场地费收入', 'bank_flow.expense_type.28', 0, 1, 1, '', 0, '2021-06-23 03:23:44');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (29, '宿舍房租收入', 'bank_flow.expense_type.29', 0, 1, 1, '', 0, '2021-06-23 03:24:13');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (30, '备用金还款-网点', 'bank_flow.expense_type.30', 0, 1, 1, '', 0, '2021-06-23 03:24:30');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (31, '还总部借款', 'bank_flow.expense_type.31', 0, 1, 1, '', 0, '2021-06-23 03:24:45');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (32, '押金退款', 'bank_flow.expense_type.32', 0, 1, 0, 'refund_type,refund_type_mark', 0, '2021-06-23 03:25:01');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (33, '包裹丢失赔偿', 'bank_flow.expense_type.33', 0, 1, 1, '', 0, '2021-06-23 03:25:15');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (34, '工资退款', 'bank_flow.expense_type.34', 0, 1, 1, '', 0, '2021-06-23 03:25:30');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (35, '提成退款', 'bank_flow.expense_type.35', 0, 1, 1, '', 0, '2021-06-23 03:25:45');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (36, 'JP保险返款', 'bank_flow.expense_type.36', 0, 1, 0, 'jp_type', 0, '2021-06-23 03:26:01');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (37, '网点自动贩卖机收款', 'bank_flow.expense_type.37', 0, 1, 0, 'store_auto_type', 0, '2021-06-23 03:26:16');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (38, '石油公司返款', 'bank_flow.expense_type.38', 0, 1, 1, '', 0, '2021-06-23 03:26:33');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (39, '油卡丢失赔偿', 'bank_flow.expense_type.39', 0, 1, 0, 'store_name', 0, '2021-06-23 03:26:49');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (40, '员工诚实保险款退回', 'bank_flow.expense_type.40', 0, 1, 1, '', 0, '2021-06-23 03:27:05');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (41, '员工赔偿电力局罚款', 'bank_flow.expense_type.41', 0, 1, 1, '', 0, '2021-06-23 03:27:23');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (42, '员工车贷押金', 'bank_flow.expense_type.42', 0, 1, 1, '', 0, '2021-06-23 03:27:38');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (43, '退税', 'bank_flow.expense_type.43', 0, 1, 1, '', 0, '2021-06-23 03:27:54');
INSERT INTO `bank_flow_expense`(`id`, `name`, `name_lang_key`, `is_pay`, `is_get`, `is_batch`, `relation_item`, `is_deleted`, `created_at`) VALUES (44, '收关联方款', 'bank_flow.expense_type.44', 0, 1, 1, '', 0, '2021-06-23 03:28:09');

-- 扩展枚举
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (1, 'refund_type', 'expense_type.refund_type_expand.1', 1, '房租', 0, 0, 0, '2021-06-23 04:14:52');
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (2, 'refund_type', 'expense_type.refund_type_expand.100', 100, '其他', 0, 0, 0, '2021-06-23 04:15:25');
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (3, 'jp_type', 'expense_type. jp_type.1', 1, '理赔款', 0, 0, 0, '2021-06-23 04:16:03');
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (4, 'jp_type', 'expense_type. jp_type.2', 2, '佣金收入', 0, 0, 0, '2021-06-23 04:16:24');
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (5, 'store_auto_type', 'expense_type. store_auto_type.1', 1, '水电费', 0, 0, 0, '2021-06-23 04:16:32');
INSERT INTO `bank_flow_enum`(`id`, `item`, `value_lang_key`, `value`, `name`, `sort`, `is_default`, `is_deleted`, `created_at`) VALUES (6, 'store_auto_type', 'expense_type. store_auto_type.2', 2, '服务费', 0, 0, 0, '2021-06-23 04:17:09');



-- 菜单权限。
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (390, 'menu.bank_flow', '银行流水管理', '银行流水管理', 0, 1, 1, '2021-06-21 11:34:06');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (391, 'menu.bank_flow.upload', '流水上传', '银行流水管理-流水上传', 390, 1, 1, '2021-06-21 11:35:11');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (392, 'menu.bank_flow.manage', '流水管理', '银行流水管理-流水管理', 390, 1, 2, '2021-06-21 11:35:56');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (393, 'menu.bank_flow.manage.pay', '付款流水', '银行流水管理-流水管理-付款流水', 392, 1, 1, '2021-06-21 11:36:37');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (394, 'menu.bank_flow.manage.get', '收款流水', '银行流水管理-流水管理-收款流水', 392, 1, 2, '2021-06-21 11:40:46');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (395, 'menu.bank_flow.day_expense', '费用报表', '银行流水管理-费用日报', 390, 1, 3, '2021-06-21 11:41:45');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (396, 'menu.bank_flow.day_money', '资金日报', '银行流水管理-资金日报', 390, 1, 4, '2021-06-21 11:42:20');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (397, 'action.bank_flow.upload.list', '流水查询', '银行流水管理-流水上传-流水列表', 391, 2, 1, '2021-06-21 11:54:01');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (398, 'action.bank_flow.upload.import', '流水导入', '银行流水管理-流水上传-流水导入', 391, 2, 2, '2021-06-21 11:45:18');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (399, 'action.bank_flow.upload.view_attachment', '流水附件查看', '银行流水管理-流水上传-流水附件查看', 391, 2, 3, '2021-06-21 11:46:09');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (400, 'action.bank_flow.upload.edit_attachment', '流水附件编辑', '银行流水管理-流水上传-流水附件编辑', 391, 2, 4, '2021-06-21 11:47:10');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (401, 'action.bank_flow.manage.pay.list', '付款流水查询', '银行流水管理-流水管理-付款流水-列表', 393, 2, 1, '2021-06-21 11:55:01');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (402, 'action.bank_flow.manage.pay.export', '付款流水导出', '银行流水管理-流水管理-付款流水-导出', 393, 2, 2, '2021-06-21 11:48:03');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (403, 'action.bank_flow.manage.pay.view', '付款流水查看', '银行流水管理-流水管理-付款流水-付款流水查看', 393, 2, 4, '2021-06-21 11:56:25');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (404, 'action.bank_flow.manage.pay.edit_link', '付款流水关联系统单号编辑', '银行流水管理-流水管理-付款流水-关联系统单号编辑', 393, 2, 5, '2021-06-21 11:57:30');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (405, 'action.bank_flow.manage.pay.cancel', '付款流水撤回', '银行流水管理-流水管理-付款流水-撤回', 393, 2, 6, '2021-06-21 11:58:30');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (406, 'action.bank_flow.manage.pay.edit', '付款流水编辑', '银行流水管理-流水管理-付款流水-明细费用类型编辑', 393, 2, 7, '2021-06-21 11:58:58');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (407, 'action.bank_flow.manage.pay.edit_detail', '付款流水明细备注编辑上传权限', '银行流水管理-流水管理-付款流水-明细备注编辑上传权限', 393, 2, 8, '2021-06-21 11:58:58');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (408, 'action.bank_flow.manage.get.list', '收款流水查询', '银行流水管理-流水管理-收款流水-列表', 394, 2, 1, '2021-06-21 11:59:58');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (409, 'action.bank_flow.manage.get.export', '收款流水导出', '银行流水管理-流水管理-收款流水-导出', 394, 2, 2, '2021-06-21 12:11:09');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (410, 'action.bank_flow.manage.get.view', '收款流水查看', '银行流水管理-流水管理-收款流水-查看', 394, 2, 4, '2021-06-21 12:12:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (411, 'action.bank_flow.manage.get.edit', '收款流水编辑', '银行流水管理-流水管理-收款流水-编辑', 394, 2, 5, '2021-06-21 12:13:38');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (412, 'action.bank_flow.manage.get.edit_detail', '收款流水明细备注编辑上传权限', '银行流水管理-流水管理-收款流水-明细备注编辑上传权限', 394, 2, 6, '2021-06-21 12:13:38');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (413, 'menu.bank_flow.day_expense.purchase', '费用表(采购)', '银行流水管理-费用日报-费用表采购', 395, 1, 1, '2021-06-22 02:35:54');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (414, 'menu.bank_flow.day_expense.payment_store_renting', '费用表(网点租房付款)', '银行流水管理-费用日报-费用表网点租房付款', 395, 1, 2, '2021-06-22 02:38:11');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (415, 'menu.bank_flow.day_expense.ordinary_payment', '费用表(普通付款)', '银行流水管理-费用日报-普通付款', 395, 1, 3, '2021-07-05 12:22:40');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (416, 'menu.bank_flow.day_expense.reimbursement', '费用表(报销)', '银行流水管理-费用日报-报销', 395, 1, 4, '2021-07-05 12:23:45');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (417, 'menu.bank_flow.day_expense.loan', '费用表(借款)', '银行流水管理-费用日报-借款', 395, 1, 5, '2021-07-05 12:24:22');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (418, 'menu.bank_flow.day_expense.reserve', '费用表(备用金)', '银行流水管理-费用日报-备用金', 395, 1, 6, '2021-07-05 12:24:52');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (419, 'menu.bank_flow.day_money.pay_and_get', '收支表', '银行流水管理-资金日报-收支表', 396, 1, 1, '2021-07-05 12:26:20');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (420, 'menu.bank_flow.day_money.collect', '余额汇总表', '银行流水管理-资金日报-余额汇总表', 396, 1, 2, '2021-07-05 12:27:22');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (421, 'action.bank_flow.day_expense.purchase.list', '查询', '银行流水管理-费用日报-费用表采购-查询', 413, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (422, 'action.bank_flow.day_expense.purchase.export', '导出', '银行流水管理-费用日报-费用表采购-导出', 413, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (423, 'action.bank_flow.day_expense.purchase.confirm', '可抵扣确认', '银行流水管理-费用日报-费用表采购-可抵扣确认', 413, 2, 3, '2021-07-05 12:29:34');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (424, 'action.bank_flow.day_expense.payment_store_renting.list', '查询', '银行流水管理-费用日报-费用表（网点租房付款）-查询', 414, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (425, 'action.bank_flow.day_expense.payment_store_renting.export', '导出', '银行流水管理-费用日报-费用表（网点租房付款）-导出', 414, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (426, 'action.bank_flow.day_expense.payment_store_renting.confirm', '可抵扣确认', '银行流水管理-费用日报-费用表（网点租房付款）-可抵扣确认', 414, 2, 3, '2021-07-05 12:29:34');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (427, 'action.bank_flow.day_expense.ordinary_payment.list', '查询', '银行流水管理-费用日报-费用表（普通付款）-查询', 415, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (428, 'action.bank_flow.day_expense.ordinary_payment.export', '导出', '银行流水管理-费用日报-费用表（普通付款）-导出', 415, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (429, 'action.bank_flow.day_expense.ordinary_payment.confirm', '可抵扣确认', '银行流水管理-费用日报-费用表（普通付款）-可抵扣确认', 415, 2, 3, '2021-07-05 12:29:34');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (430, 'action.bank_flow.day_expense.reimbursement.list', '查询', '银行流水管理-费用日报-费用表（报销）-查询', 416, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (431, 'action.bank_flow.day_expense.reimbursement.export', '导出', '银行流水管理-费用日报-费用表（报销）-导出', 416, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (432, 'action.bank_flow.day_expense.reimbursement.confirm', '可抵扣确认', '银行流水管理-费用日报-费用表（报销）-可抵扣确认', 416, 2, 3, '2021-07-05 12:29:34');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (433, 'action.bank_flow.day_expense.loan.list', '查询', '银行流水管理-费用日报-费用表（借款）-查询', 417, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (434, 'action.bank_flow.day_expense.loan.export', '导出', '银行流水管理-费用日报-费用表（借款）-导出', 417, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (435, 'action.bank_flow.day_expense.reserve.list', '查询', '银行流水管理-费用日报-费用表（备用金）-查询', 418, 2, 1, '2021-07-05 12:28:27');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (436, 'action.bank_flow.day_expense.reserve.export', '导出', '银行流水管理-费用日报-费用表（备用金）-导出', 418, 2, 2, '2021-07-05 12:29:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (437, 'action.bank_flow.day_money.pay_and_get.list', '查询', '银行流水管理-资金日报-收支表-查询', 419, 2, 1, '2021-07-05 12:34:48');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (438, 'action.bank_flow.day_money.pay_and_get.export', '导出', '银行流水管理-资金日报-收支表-导出', 419, 2, 2, '2021-07-05 12:35:17');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (439, 'action.bank_flow.day_money.collect.list', '查询', '银行流水管理-资金日报-余额汇总表-查询', 420, 2, 1, '2021-07-05 12:35:36');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (440, 'action.bank_flow.day_money.collect.export', '导出', '银行流水管理-资金日报-余额汇总表-导出', 420, 2, 2, '2021-07-05 12:36:22');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (441, 'menu.bank_flow.day_expense.day', '费用日报', '银行流水管理-费用日报-费用日报', 395, 1, 7, '2021-07-07 08:57:48');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (442, 'menu.bank_flow.day_expense.month', '费用月报', '银行流水管理-费用日报-费用月报', 395, 1, 8, '2021-07-07 08:59:10');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (443, 'action.bank_flow.day_expense.day.list', '查询', '银行流水管理-费用日报-费用日报-查询', 441, 2, 1, '2021-07-07 09:00:25');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (444, 'action.bank_flow.day_expense.day.export', '导出', '银行流水管理-费用日报-费用日报-导出', 441, 2, 2, '2021-07-07 09:01:03');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (445, 'action.bank_flow.day_expense.month.list', '查询', '银行流水管理-费用日报-费用月报-查询', 442, 2, 1, '2021-07-07 09:01:28');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (446, 'action.bank_flow.day_expense.month.export', '导出', '银行流水管理-费用日报-费用月报-导出', 442, 2, 2, '2021-07-07 09:01:43');

-- 借款添加付款来源
ALTER TABLE `loan_pay`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `create_job_title_name`;


-- 报销添加付款来源
ALTER TABLE `reimbursement`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `type`;

ALTER TABLE `reimbursement_detail`
ADD COLUMN `is_deduct` tinyint(2) NULL DEFAULT 0 COMMENT '银行流水-是否可抵扣确认' AFTER `end_at`;


-- 采购付款申请单==不释放运算。
ALTER TABLE `purchase_payment`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `pay_id`;
ALTER TABLE `purchase_payment_receipt`
ADD COLUMN `is_deduct` tinyint(1) NULL DEFAULT 0 COMMENT '银行流水-是否可抵扣确认' AFTER `real_amount`;


-- 网点租房合同付款添加付款来源
ALTER TABLE `payment_store_renting_pay`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `pay_date`;
ALTER TABLE `payment_store_renting_detail`
ADD COLUMN `is_deduct` tinyint(1) NULL DEFAULT 0 COMMENT '银行流水-可抵扣确认' AFTER `updated_at`;


-- 普通付款添加付款来源
ALTER TABLE `ordinary_payment_extend`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `pay_bk_flow_date`;

ALTER TABLE `ordinary_payment_detail`
ADD COLUMN `is_deduct` tinyint(1) NULL DEFAULT 0 COMMENT '银行流水-是否可抵扣确认' AFTER `contract_no`;


-- 备用金付款添加付款来源
ALTER TABLE `reserve_fund_apply`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `pay_bank`;


-- 薪酬扣款添加唯一索引
ALTER TABLE `wages`
ADD UNIQUE INDEX `uk_no`(`no`) USING BTREE;

-- 薪酬扣款添加付款来源
ALTER TABLE `wages`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `payed_at`;

-- 薪资发放添加付款来源
ALTER TABLE `pay_salary_apply`
ADD COLUMN `pay_from` tinyint(2) NULL DEFAULT 1 COMMENT '付款来源，1本模块，2银行流水' AFTER `payed_at`;

-- 银行流水需求 end


-- 采购单

-- oa 数据库 所有国家
CREATE TABLE `purchase_type` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `purchase_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '采购类型 18 库存类 19 服务类 84 辅助类 1 固定资产类别',
  `name_key` varchar(64) NOT NULL DEFAULT '' comment '翻译 key',
  `operate_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配置人工号',
  `update_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后修改人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_purchase_type` (`purchase_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购类型枚举表';


CREATE TABLE `purchase_type_wrs_code` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `purchase_type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '采购类型 1 固定资产类别 2 辅助类 3 库存类 4 服务类',
  `wrs_code` varchar(20) NOT NULL DEFAULT '' COMMENT '物料编码',
  `name_key` varchar(64) NOT NULL DEFAULT '' COMMENT '翻译key',
  `operate_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '配置人工号',
  `update_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后修改人',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_delete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`wrs_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购类型枚举表';




alter table purchase_apply
add column purchase_type int unsigned not null default 0 comment '采购类型 原来在采购订单表里面 又搬到这来了 产品也不知道来回折腾啥 还新增了个子类型 物料编码'
after approve_at;

alter table purchase_apply
add column wrs_code varchar(20)  not null default '' comment '物料编码 采购类型子集'
after purchase_type;


alter table purchase_order
add column wrs_code varchar(20)  not null default '' comment '物料编码 采购类型子集'
after purchase_type;

-- 付款百分比 新增字段 在付款单发票里面
alter table purchase_payment_receipt
add column percent tinyint unsigned not null default 0 comment '付款百分比' after tax_total_price;




INSERT INTO `purchase_type` (`id`, `purchase_type`, `name_key`)
VALUES
        (1, 18, 'purchase_stock_type_key'),
        (2, 19, 'purchase_service_key'),
        (3, 84, 'purchase_cost_key'),
        (4, 1, 'purchase_capital_assets_key');


INSERT INTO `purchase_type_wrs_code` (`purchase_type`, `wrs_code`)
VALUES
   ( 1,  'A001'),
   ( 1,  'A002'),
   ( 1,  'A003'),
   ( 1,  'A004'),
   ( 1,  'A005'),
   ( 1,  'A006'),
   ( 1,  'A007'),
   ( 1,  'A008'),
   ( 1,  'A009'),
   ( 1,  'A0010'),
   ( 1,  'A0011'),
   ( 1,  'LVA'),

   ( 84,  'O001'),
    ( 84,  'O002'),
    ( 84,  'O003'),
    ( 84,  'O004'),

    ( 18,  'G010'),
    ( 18,  'G020'),
    ( 18,  'G030'),


    ( 19,  'S001'),
    ( 19,  'S002'),
    ( 19,  'S003'),
    ( 19,  'S004'),
    ( 19,  'S005'),
    ( 19,  'S006'),
    ( 19,  'S007'),
    ( 19,  'S008'),
    ( 19,  'S009');

update workflow_node set can_edit_field = '{"main":["purchase_type","wrs_code"],"meta":["ledger_account_id"]}' where id = 720;


-- 所有国家执行 这个比较重要 数据库同步扩大100倍 由原来的 4位小数点 改为 6位小数点 上线的时候要半夜执行 并且迅速上代码
update purchase_apply_product set price = price * 100;

update purchase_order_product set price = price * 100;

update purchase_payment_receipt set not_tax_price = not_tax_price * 100;

alter table purchase_apply_product modify `price` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '产品单价（乘以1000000），保留6位小数，不含税单价';

alter table purchase_order_product modify `price` bigint(20) unsigned NOT NULL COMMENT  '产品单价（乘以1000000），保留6位小数，不含税单价';

alter table purchase_payment_receipt modify `not_tax_price` bigint(20) unsigned NOT NULL COMMENT '产品单价（乘以1000000），保留6位小数，不含税单价';

-- 采购单  结束----


-- 支付模块开始 --
CREATE TABLE `payment` (
                           `id` int(11) NOT NULL AUTO_INCREMENT,
                           `oa_type` tinyint(2) DEFAULT NULL COMMENT 'oa模块对应id',
                           `no` varchar(100) DEFAULT NULL COMMENT '申请单号',
                           `apply_staff_id` int(11) DEFAULT NULL COMMENT '申请人单号',
                           `apply_staff_name` varchar(255) DEFAULT '' COMMENT '申请人姓名',
                           `cost_department_id` int(11) DEFAULT NULL COMMENT '费用所属部门id',
                           `cost_department_name` varchar(255) DEFAULT '' COMMENT '费用所属部门名字',
                           `cost_company_id` int(11) DEFAULT NULL COMMENT '费用所属公司id',
                           `cost_company_name` varchar(255) DEFAULT '' COMMENT '费用所属公司名字',
                           `apply_date` varchar(255) DEFAULT '' COMMENT '单号申请时间',
                           `pay_status` tinyint(2) DEFAULT '1' COMMENT '1待支付，2已支付，3未支付，4支付中',
                           `pay_method` tinyint(2) DEFAULT '1' COMMENT '1现金，2银行转账，3支票',
                           `pay_where` tinyint(2) DEFAULT '1' COMMENT '1境内支付，2境外支付',
                           `currency` tinyint(2) DEFAULT NULL COMMENT '1THB，2美元，3人民币（通用的货币类型id）',
                           `amount_total_no_tax` decimal(12,2) DEFAULT '0.00' COMMENT '不含税金额总计',
                           `amount_total_vat` decimal(12,2) DEFAULT '0.00' COMMENT 'vat总计',
                           `amount_total_have_tax` decimal(12,2) DEFAULT '0.00' COMMENT '含税金额总计(含wht)',
                           `amount_total_wht` decimal(12,2) DEFAULT '0.00' COMMENT 'wht总计',
                           `amount_total_have_tax_no_wht` decimal(12,2) DEFAULT '0.00' COMMENT '含税金额总计(不含wht)',
                           `amount_loan` decimal(12,2) DEFAULT '0.00' COMMENT '冲减借款金额',
                           `amount_reserve` decimal(12,2) DEFAULT '0.00' COMMENT '冲减备用金额',
                           `amount_discount` decimal(12,2) DEFAULT '0.00' COMMENT '折扣',
                           `amount_total_actually` decimal(12,0) DEFAULT '0' COMMENT '实付金额总计',
                           `amount_remark` varchar(5000) DEFAULT '' COMMENT '金额信息-备注',
                           `pay_bank_name` varchar(255) DEFAULT '' COMMENT '支付银行，pay_method=2的时候必填',
                           `pay_bank_account` varchar(255) DEFAULT '' COMMENT '银行账号，pay_method=2的时候必填',
                           `pay_bank_flow_date` date DEFAULT NULL COMMENT '银行流水日期，pay_method=2的时候必填',
                           `not_pay_reason` varchar(1000) DEFAULT '' COMMENT '未支付原因',
                           `reject_reason` varchar(1000) DEFAULT '' COMMENT '驳回原因，第二级',
                           `pay_check` text COMMENT '支票号json(支票号信息1，承兑日期1)，pay_method=3的时候必填',
                           `pay_date` date DEFAULT NULL COMMENT '付款日期，pay_method=1的时候必填',
                           `pay_remark` varchar(5000) DEFAULT '' COMMENT '支付信息-备注',
                           `is_pay` tinyint(2) DEFAULT '1' COMMENT '是否支付，1是，2否',
                           `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                           `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                           PRIMARY KEY (`id`),
                           UNIQUE KEY `uk_no` (`no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付通用模块';


CREATE TABLE `payment_pay` (
                               `id` int(11) NOT NULL AUTO_INCREMENT,
                               `payment_id` int(11) NOT NULL,
                               `amount` decimal(12,2) DEFAULT '0.00' COMMENT '付款金额',
                               `bank_name` varchar(255) DEFAULT '' COMMENT '收款人银行',
                               `bank_account` varchar(255) DEFAULT '' COMMENT '收款人账号',
                               `bank_account_name` varchar(255) DEFAULT '' COMMENT '收款人户名',
                               `swift_code` varchar(255) DEFAULT '' COMMENT 'swift_code=银行转账=境外',
                               `bank_address` varchar(255) DEFAULT '' COMMENT '收款银行地址=银行转账=境外',
                               `address` varchar(255) DEFAULT '' COMMENT '收款人地址',
                               PRIMARY KEY (`id`),
                               KEY `idx_payment_id` (`payment_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付模块关联收款人相关信息，1对多。有可能有多个人';



-- 支付模块审批
INSERT INTO `workflow` (`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (76, '支付模块审批', 76, '支付模块审批', '2021-07-22 11:59:21');


-- 支付模块node节点
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1041, 76,  '支付模块-员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1042, 76, '支付模块-一级审批人', 3, 0, NULL, NULL, NULL, NULL, 1, '119099,119140', NULL, '{\"main\":[\"is_pay\",\"pay_bank_name\",\"pay_bank_account\",\"pay_bank_flow_date\",\"pay_check\",\"pay_date\",\"pay_remark\"],\"meta\":[\"attachments\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1043, 76, '支付模块-二级审批人', 3, 0, NULL, NULL, NULL, NULL, 1, '117225,119032', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`,`name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1044, 76, '支付模块-三级审批人', 3, 0, NULL, NULL, NULL, NULL, 1, '119099,119140', NULL, '{\"main\":[\"is_pay\",\"pay_bank_flow_date\", \"pay_date\",\"pay_remark\"],\"meta\":[\"attachments\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1045, 76,'结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);

-- relate
INSERT INTO `workflow_node_relate`(`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 76, 1041, 1042, NULL, NULL, '员工提交->一级处理人', 10, '2021-07-22 12:08:54', '2021-07-22 12:08:12');
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 76, 1042, 1043, NULL, NULL, '一级处理人->二级处理人', 10, '2021-07-22 12:08:58', '2021-07-22 12:08:40');
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 76, 1043, 1044, NULL, NULL, '二级处理人->三级处理人', 10, '2021-07-22 12:09:21', '2021-07-22 12:09:10');
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 76, 1044, 1045, NULL, NULL, '三级处理人->结束', 10, '2021-07-22 12:09:43', '2021-07-22 12:09:33');




INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (452, 'menu.pay', '支付管理', '支付模块管理', 0, 1, 1, '2021-07-23 03:34:30');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (453, 'menu.pay.pay', '我的支付', '支付模块管理-我的支付', 452, 1, 1, '2021-07-23 03:34:51');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (454, 'menu.pay.data', '数据查询', '支付模块管理-数据查询', 452, 1, 2, '2021-07-23 03:35:12');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (455, 'action.pay.pay.list', '查询', '支付模块管理-我的支付-查询', 453, 2, 1, '2021-07-23 03:37:11');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (456, 'action.pay.pay.export', '导出', '支付模块管理-我的支付-导出', 453, 2, 2, '2021-07-23 03:38:02');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (457, 'action.pay.pay.view', '查看', '支付模块管理-我的支付-查看', 453, 2, 3, '2021-07-23 03:38:50');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (458, 'action.pay.pay.audit', '审核', '支付模块管理-我的支付-审核', 453, 2, 4, '2021-07-23 03:39:13');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (459, 'action.pay.data.list', '查询', '支付模块管理-数据查询-查询', 454, 2, 1, '2021-07-23 03:57:08');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (460, 'action.pay.data.export', '导出', '支付模块管理-数据查询-导出', 454, 2, 2, '2021-07-23 03:57:35');
INSERT INTO `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (461, 'action.pay.data.view', '查看', '支付模块管理-数据查询-查看', 454, 2, 3, '2021-07-23 03:58:56');
-- 支付模块结束，审批节点工号有可能需要换

-- 所有国家 db: oa
-- 增加COO组织id
INSERT INTO `setting_env`(`code`, `val`, `content`) VALUES ('coo_department_id', '222', '报销模块选择费用所属公司时判断的COO组织id');
INSERT INTO `setting_env`(`code`, `val`, `content`) VALUES ('deductible_vat_tax', '["0","9.43"]', '可抵扣vat税率');
-- 增加审批节点可修改字段
update workflow_node set can_edit_field='{"main":["extra_message","voucher_abstract"],"meta":["ledger_account_id","deductible_vat_tax"]}' where flow_id in (39,40,41,42,44,57) and can_edit_field !='';
-- 增加字段
ALTER TABLE `reimbursement`
ADD COLUMN `cost_company_id` int(11) NOT NULL DEFAULT 0 COMMENT '费用所属公司' AFTER `cost_center_code`,
ADD COLUMN `extra_message` varchar(100) NULL DEFAULT '' COMMENT '额外参考消息' AFTER `cost_company_id`,
ADD COLUMN `voucher_abstract` varchar(100) NULL COMMENT '凭证摘要' AFTER `extra_message`,
MODIFY COLUMN `amount` bigint(20) NULL DEFAULT NULL COMMENT '含税金额总计 更改为:发票金额总计（含VAT含WHT）' AFTER `local_amount`,
MODIFY COLUMN `real_amount` bigint(20) NULL DEFAULT NULL COMMENT '实付金额 更改为:实付金额总计' AFTER `other_amount`,
ADD COLUMN `payable_amount_all` bigint(20) NULL COMMENT '应付金额总计（含VAT不含WHT）' AFTER `real_amount`;


ALTER TABLE `reimbursement_detail`
MODIFY COLUMN `tax` bigint(20) NULL DEFAULT NULL COMMENT '税额 更改为:VAT税额' AFTER `info`,
MODIFY COLUMN `tax_not` bigint(20) NULL DEFAULT NULL COMMENT '不含税金额 更改为:发票金额（不含VAT含WHT）' AFTER `tax`,
MODIFY COLUMN `amount` bigint(20) NULL DEFAULT NULL COMMENT '含税金额 更改为:发票金额（含VAT含WHT）' AFTER `tax_not`,
MODIFY COLUMN `rate` bigint(255) NULL DEFAULT NULL COMMENT '税率，tax/tax_not 更改为:VAT税率' AFTER `amount`,
ADD COLUMN `wht_type` int(11) NOT NULL DEFAULT 0 COMMENT 'WHT类别' AFTER `rate`,
ADD COLUMN `wht_tax` varchar(255) NULL COMMENT 'WHT税率 存储枚举值' AFTER `wht_type`,
ADD COLUMN `wht_tax_amount` bigint(20) NULL COMMENT 'WHT税额' AFTER `wht_tax`,
ADD COLUMN `deductible_vat_tax` varchar(255) NULL COMMENT '可抵扣VAT税率 存储枚举值' AFTER `wht_tax_amount`,
ADD COLUMN `deductible_tax_amount` bigint(20) NULL COMMENT '可抵扣税额' AFTER `deductible_vat_tax`,
ADD COLUMN `payable_amount` bigint(20) NULL COMMENT '应付金额（含VAT不含WHT）' AFTER `deductible_tax_amount`;

-- 字段赋值
UPDATE `reimbursement` SET `payable_amount_all` = `amount`;

-- 所有国家 db:oa
UPDATE `workflow_node_relate` SET `valuate_formula` = 'in_array(1,$p1) || $p2==1', `valuate_code` = 'getVendorModules,getOnlyPurchaseAudit' WHERE `flow_id` = 67 and `from_node_id` = 978 and `to_node_id` = 979;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1==0', `valuate_code` = 'getOnlyPurchaseAudit' WHERE `flow_id` = 67 and `from_node_id` = 979 and `to_node_id` = 980;

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`) VALUES (67, 978, 980, '!in_array(1,$p1)', 'getVendorModules', '提交->泰国财务');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`) VALUES (67, 979, 983, '$p1==1', 'getOnlyPurchaseAudit', '采购节点->结束');
INSERT INTO `permission`( `id`,`key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES ( '471','action.contract.vendor.delete', '删除', '供应商信息-删除', 382, 2, 5, '2020-01-06 08:45:51');


ALTER TABLE `vendor`
ADD COLUMN `only_purchase_audit` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否只走采购审批  0:否 1:是' AFTER `approval_version_no`;

UPDATE `permission` SET `type` = 1 WHERE `id` = 382;