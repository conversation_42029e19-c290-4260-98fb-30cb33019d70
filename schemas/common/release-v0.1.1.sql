
ALTER TABLE `permission`
    MODIFY COLUMN `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(328,'menu.budget.object','预算科目',null,216,1,3,null);
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(329,'action.budget.object.edit','预算科目编辑',null,328,2,1,null);
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(330,'action.budget.object.add','预算科目添加',null,328,2,2,null);
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(332,'action.budget.object.product.add','预算科目明细添加',null,328,2,4,'2021-04-15 03:01:14');
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(333,'action.budget.object.product.edit','预算科目明细编辑',null,328,2,3,'2021-04-15 03:01:42');
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(334,'action.budget.object.del','预算科目删除',null,328,2,5,'2021-04-15 07:19:41');
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(335,'action.budget.object.product.del','预算科目明细删除',null,328,2,6,'2021-04-15 07:53:42');
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(336,'action.budget.object.product.view','预算科目明细查看',null,328,2,null,'2021-04-16 07:36:56');
insert into `permission`(`id`,`key`,`name`,`description`,`ancestry`,`type`,`sort`,`created_at`)
values(337,'action.budget.object.view','预算科目查看',null,328,2,null,'2021-04-16 07:37:30');
