#新增职位体系二级菜单
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('331', 'menu.organization.job_manage', '职位体系', '职位体系', '140', '1', '2', '2021-04-14 08:49:46');

insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('339', 'menu.organization.job_manage.jobgroup', '职组管理', '职位体系-职组管理', '331', '1', '2', '2021-04-19 12:33:39');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('340', 'menu.organization.job_manage.jd', 'JD管理', '职位体系-JD管理', '331', '1', '3', '2021-04-19 12:33:39');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('341', 'menu.organization.job_manage.competency', '胜任力管理', '职位体系-胜任力管理', '331', '1', '4', '2021-04-19 12:33:39');

#职位管理action:
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('342', 'action.organization.job_manage.job.list', '列表查看', '职位体系-职位管理-列表查看', '338', '2', '1', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('343', 'action.organization.job_manage.job.add', '新建职位', '职位体系-职位管理-新建职位', '338', '2', '2', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('344', 'action.organization.job_manage.job.edit', '修改职位', '职位体系-职位管理-修改职位', '338', '2', '3', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('345', 'action.organization.job_manage.job.bind', '关联/编辑职位', '职位体系-职位管理-关联/编辑职位', '338', '2', '4', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('346', 'action.organization.job_manage.job.view_detail', '查看职位详情', '职位体系-职位管理-查看职位详情', '338', '2', '5', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('347', 'action.organization.job_manage.job.export_list', '导出excel', '职位体系-职位管理-导出excel', '338', '2', '7', '2021-04-19 13:15:58');
insert into `permission` (`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) values ('348', 'action.organization.job_manage.job.export_detail', '导出职位信息', '职位体系-职位管理-导出职位信息', '338', '2', '8', '2021-04-19 13:15:58');



####@张兴武

INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (349, 'action.organization.job_manage.jobgroup.list', '列表查看', '职位体系-职组管理-列表查看', 339, 2, 1, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (350, 'action.organization.job_manage.jobgroup.add', '新增职组', '职位体系-职组管理-新增职组', 339, 2, 2, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (351, 'action.organization.job_manage.jobgroup.add_child', '新增子职组', '职位体系-职组管理-新增子职组', 339, 2, 3, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (352, 'action.organization.job_manage.jobgroup.edit', '编辑职组', '职位体系-职组管理-编辑职组', 339, 2, 4, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (353, 'action.organization.job_manage.jobgroup.view_detail', '查看职组', '职位体系-职组管理-查看职组', 339, 2, 5, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (354, 'action.organization.job_manage.jobgroup.del', '删除职组', '职位体系-职组管理-删除职组', 339, 2, 6, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (355, 'action.organization.job_manage.jd.list', '列表查看', '职位体系-JD管理-列表查看', 340, 2, 1, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (356, 'action.organization.job_manage.jd.add', '新增岗位', '职位体系-JD管理-新增岗位', 340, 2, 2, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (357, 'action.organization.job_manage.jd.edit', '编辑岗位', '职位体系-JD管理-编辑岗位', 340, 2, 3, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (358, 'action.organization.job_manage.jd.detail', '查看岗位', '职位体系-JD管理-查看岗位', 340, 2, 4, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (359, 'action.organization.job_manage.jd.del', '删除岗位', '职位体系-JD管理-删除岗位', 340, 2, 5, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (360, 'action.organization.job_manage.competency.list', '列表查看', '职位体系-胜任力管理-列表查看', 341, 2, 1, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (361, 'action.organization.job_manage.competency.add', '新增胜任力', '职位体系-胜任力管理-新增胜任力', 341, 2, 2, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (362, 'action.organization.job_manage.competency.edit', '编辑胜任力', '职位体系-胜任力管理-编辑胜任力', 341, 2, 3, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (363, 'action.organization.job_manage.competency.detail', '查看胜任力', '职位体系-胜任力管理-查看胜任力', 341, 2, 4, '2021-04-20 03:20:54');
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (364, 'action.organization.job_manage.competency.del', '删除胜任力', '职位体系-胜任力管理-删除胜任力', 341, 2, 5, '2021-04-20 03:20:55');




#### 租房合同  添加字段
alter table `contract_store_renting`
 ADD `area_service_amount_no_tax` float(10,2) DEFAULT NULL COMMENT '区域服务费金额',
  ADD  `area_vat_rate` tinyint(1) DEFAULT NULL COMMENT 'WHT税率：值为整数(0-100), 单位 %',
  ADD  `area_service_amount_vat` float(10,2) DEFAULT NULL COMMENT '区域服务费VAT税额',
  ADD  `area_wht_category` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT类别',
  ADD  `area_wht_rate` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT税率',
  ADD  `area_amount_wht` float(10,2) DEFAULT NULL COMMENT '区域服务费WHT金额',
  ADD  `contract_total_amount` varchar(50) DEFAULT NULL COMMENT '合同总金额',
  ADD `billboard_tax_payer` tinyint(1) DEFAULT NULL COMMENT '广告牌税付款人1房东2公司',
  ADD `contract_deposit` float(10,2) DEFAULT NULL COMMENT '合同定金',
  ADD  `ver` tinyint(1) DEFAULT '0' COMMENT '版本区分',
  ADD  `pdf_reject_name` varchar(2000) DEFAULT NULL COMMENT '驳回pdf';

  # 租房合同税费信息详情表
  CREATE TABLE `contract_store_renting_detail` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `contract_store_renting_id` int(11) unsigned NOT NULL COMMENT '网点租房合同主表id',
  `cost_start_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '费用发生日期',
  `cost_end_date` date NOT NULL DEFAULT '0000-00-00' COMMENT '费用结束日期',
  `amount_no_tax` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '不含税金额',
  `vat_rate` tinyint(1) NOT NULL COMMENT 'VAT税率：值为整数(0-100), 单位 %',
  `amount_vat` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT 'vat 金额 （不含税金额的7%）',
  `amount_has_tax` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '含税金额 = 不含税金额+vat金额',
  `wht_category` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT 'WHT类别：1 : PND3, 2 : PND53,  3 : /',
  `wht_rate` tinyint(1) NOT NULL COMMENT 'WHT税率：值为整数(0-100), 单位 %',
  `amount_wht` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT 'WHT金额，WHT金额=不含税金额*WHT税率',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contract_store_renting_id` (`contract_store_renting_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=510 DEFAULT CHARSET=utf8mb4 COMMENT='网点租房合同信息明细表';


# 租房付款
 alter table `payment_store_renting`
   ADD `vat_total_amount` decimal(12,2) DEFAULT NULL COMMENT 'vat 金额总计',
   ADD`tax_total_amount` decimal(12,2) DEFAULT NULL COMMENT '含税金额总计',
   ADD `ver` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0历史数据1现版本';


  alter table `payment_store_renting_detail`
  ADD   `vat_rate` tinyint(1) NOT NULL COMMENT 'vat 税率',
  ADD  `amount_has_tax` decimal(10,2) NOT NULL COMMENT '含税金额';

  ALTER TABLE `payment_store_renting`  MODIFY COLUMN total_amount decimal(12,2) unsigned DEFAULT NULL COMMENT '金额总计（改不含税金额总计）';


#####
-- 添加销售合同编号字段
alter table contract
add column sell_cno varchar(20) NOT NULL DEFAULT '' COMMENT '销售合同编号';
-- 添加是否授权范围字段
alter table contract
add column in_scope tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否授权范围内';
-- 添加结算方式字段
alter table contract
add column pay_method tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '结算方式';




