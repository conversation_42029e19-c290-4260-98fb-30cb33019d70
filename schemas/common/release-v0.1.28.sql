-- 新添加开户行
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (112, 'bank.112', '马来亚银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (113, 'bank.113', '联昌国际银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (114, 'bank.114', '马来西亚大众银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (115, 'bank.115', '马来西亚兴业银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (116, 'bank.116', '丰隆银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (117, 'bank.117', '大马银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (118, 'bank.118', '大华银行马来西亚');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (119, 'bank.119', '马来西亚人民银行');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (120, 'bank.120', '华侨银行（马来西亚）');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (121, 'bank.121', '汇丰银行马来西亚');

-- 汇率修正
UPDATE `setting_exchange_rate` SET `rate`=0.004  WHERE `base_currency`=1 AND `format_currency`=5;
UPDATE `setting_exchange_rate` SET `rate`=0.005  WHERE `base_currency`=4 AND `format_currency`=5;
UPDATE `setting_exchange_rate` SET `rate` = 0.12900 WHERE `base_currency` = 6 and `format_currency`=1;
UPDATE  `setting_exchange_rate` SET `rate` = 4.30000 WHERE `base_currency` = 6 and `format_currency`=2;
UPDATE  `setting_exchange_rate` SET `rate` = 0.66000 WHERE `base_currency` = 6 and `format_currency`=3;

-- 与SCM SAP 对接
ALTER TABLE `purchase_order`
ADD COLUMN `stock_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT 'scm入库状态 1待处理2入库完成',
ADD COLUMN `is_sap_task` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0未更新1已更新';

CREATE TABLE `purchase_storage` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `psno` varchar(20) NOT NULL DEFAULT '' COMMENT '入库单号',
  `scm_no` varchar(20) DEFAULT NULL COMMENT 'scm入库单号',
  `sap_order_no` varchar(12) NOT NULL COMMENT 'sap采购订单号',
  `po_id` int(10) unsigned NOT NULL COMMENT 'po采购单主键id',
  `po` varchar(20) NOT NULL COMMENT 'oa采购单号',
  `currency` tinyint(1) unsigned NOT NULL COMMENT '''币种 1THB 、2USD、3CNY''',
  `status` tinyint(1) NOT NULL COMMENT '1 待处理2审核通过3已入库4已收货5已撤回',
  `create_id` int(11) NOT NULL COMMENT '申请人id',
  `create_name` varchar(32) NOT NULL COMMENT '申请人姓名',
  `cost_department_name` varchar(100) NOT NULL COMMENT '费用部门名称',
  `cost_company_name` varchar(100) NOT NULL COMMENT '费用部门名称',
  `apply_date` date NOT NULL COMMENT '申请时间',
  `recall_remark` varchar(500) DEFAULT NULL,
  `create_at` datetime NOT NULL COMMENT '创建时间',
  `update_at` datetime NOT NULL COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0正常1删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_psno` (`psno`) USING BTREE,
  KEY `idx_union` (`update_at`,`status`) USING BTREE,
  KEY `idx_po_id` (`po_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `purchase_storage_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `psnoid` int(11) NOT NULL COMMENT '入库单ID',
  `product_name` varchar(255) NOT NULL DEFAULT '' COMMENT '产品名称',
  `product_option_code` varchar(100) DEFAULT NULL COMMENT '产品编码',
  `stock_id` varchar(10) DEFAULT NULL COMMENT '仓库id',
  `total` int(10) unsigned NOT NULL COMMENT '数量',
  `total_quantity_received` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '累计收货数量',
  `real_quantity_received` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实际收货数量',
  `unit` varchar(50) NOT NULL COMMENT '实际收货单位',
  `real_unit` varchar(32) DEFAULT NULL COMMENT '实际收货单位',
  `price` bigint(20) unsigned NOT NULL COMMENT '价格（不含税单价）',
  `delivery_date` date DEFAULT NULL COMMENT '计划交货日期',
  `create_at` datetime DEFAULT NULL,
  `update_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_psd` (`psnoid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入库单订单产品单';


CREATE TABLE `purchase_sap_task` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `sap_order_no` varchar(20) NOT NULL,
  `return_data` text COMMENT '返回数据',
  `create_at` datetime DEFAULT NULL,
  `update_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_sap_order` (`sap_order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (462, 'menu.my.storage', '我的申请-入库通知单', '采购入库申请单', 71, 1, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (463, 'menu.data.storage', '我的申请-入库通知单', '采购入库单', 73, 1, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (464, 'action.my.storage.search', '入库通知单-查询', '入库通知单-查询', 462, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (465, 'action.my.storage.apply', '入库通知单-新增', '入库通知单-新增', 462, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (466, 'action.my.storage.view', '入库通知单-查看', '入库通知单-查看', 462, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (467, 'action.my.storage.recall', '入库通知单-撤回', '入库通知单-撤回', 462, 2, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (468, 'action.my.storage.delete', '入库通知单-删除', '入库通知单-删除', 462, 2, 5, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (469, 'action.data.storage.search', '入库通知单-查询', '入库通知单-查询', 463, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (470, 'action.data.storage.view', '入库通知单-查看', '入库通知单-查看', 463, 2, 2, NULL);

-- 业务主表汇率字段扩展
ALTER TABLE `contract` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `contract_store_renting` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `loan` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `ordinary_payment` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `payment_store_renting` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `purchase_apply` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `purchase_order` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `purchase_payment` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `reimbursement` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

ALTER TABLE `reserve_fund_apply` MODIFY COLUMN `exchange_rate` decimal(10, 5) UNSIGNED NOT NULL DEFAULT 0.00000 COMMENT '当前币种对应默认币种的汇率';

