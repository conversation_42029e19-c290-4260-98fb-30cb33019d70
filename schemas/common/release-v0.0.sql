CREATE TABLE `reserve_fund_apply` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rfano` varchar(20) NOT NULL DEFAULT '' COMMENT '备用金申请编号',
  `apply_date` date DEFAULT NULL COMMENT '申请日期',
  `create_id` int(11) NOT NULL DEFAULT '0' COMMENT '申请人工号',
  `create_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人姓名',
  `create_department_id` varchar(10) NOT NULL DEFAULT '' COMMENT '申请人-部门ID',
  `create_department_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人-部门名字',
  `create_company_id` varchar(10) NOT NULL DEFAULT '' COMMENT '申请人-公司ID',
  `create_company_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人-公司名字',
  `create_store_id` varchar(10) NOT NULL DEFAULT '' COMMENT '申请人-所属网点ID',
  `create_store_name` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人-网点名称',
  `currency` int(1) NOT NULL COMMENT '币种 1THB 、2USD、3CNY',
  `exchange_rate` decimal(10,3) unsigned NOT NULL DEFAULT '0.000' COMMENT '当前币种对应默认币种的汇率',
  `payee_username` varchar(50) NOT NULL COMMENT '收款人-户名',
  `payee_account` varchar(16) NOT NULL COMMENT '收款人-账号',
  `payee_bank` tinyint(4) NOT NULL DEFAULT '0' COMMENT '收款人-账号',
  `apply_reason` varchar(5000) NOT NULL DEFAULT '' COMMENT '申请原因',
  `amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '金额总计',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态-1待审核，2拒绝（驳回），3同意，4撤回',
  `pay_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付状态',
  `pay_at` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_id` int(11) DEFAULT '0' COMMENT '支付人工号',
  `pay_account` varchar(20) NOT NULL DEFAULT '' COMMENT '付款账号',
  `pay_bank` varchar(50) NOT NULL DEFAULT '' COMMENT '付款银行',
  `real_pay_at` datetime DEFAULT NULL COMMENT '银行流水时间',
  `operation_remark` varchar(1000) DEFAULT '' COMMENT '操作备注（拒绝原因、撤销原因）',
  `is_cite` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被报销引用 ',
  `approve_at` datetime DEFAULT NULL COMMENT '通过时间（v8150）',
  `created_at` datetime NOT NULL COMMENT '申请时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `last_update_id` varchar(255) DEFAULT '' COMMENT '最后操作人工号',
  `last_update_name` varchar(255) DEFAULT '' COMMENT '最后操作人姓名（昵称）',
  `last_update_department` varchar(255) DEFAULT '' COMMENT '最后操作人部门',
  `last_update_job_title` varchar(255) DEFAULT '' COMMENT '最后操作人职位',
  `last_update_at` varchar(255) DEFAULT '' COMMENT '最后操作人更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rfano` (`rfano`),
  KEY `idx_create_id` (`create_id`) USING BTREE,
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备用金申请';

CREATE TABLE `reserve_fund_return` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rrno` varchar(20) NOT NULL COMMENT '归还编号',
  `apply_date` date NOT NULL COMMENT '申请日期',
  `create_id` int(11) NOT NULL COMMENT '原持有人工号',
  `create_name` varchar(50) NOT NULL DEFAULT '' COMMENT '原持有人姓名',
  `create_department_id` varchar(10) NOT NULL COMMENT '原持有人-部门ID',
  `create_department_name` varchar(50) DEFAULT NULL COMMENT '原持有人-部门名字',
  `create_company_id` varchar(10) NOT NULL COMMENT '原持有人-公司ID',
  `create_company_name` varchar(50) DEFAULT NULL COMMENT '原持有人-公司名字',
  `create_store_id` varchar(10) NOT NULL COMMENT '原持有人-所属网点ID',
  `create_store_name` varchar(50) DEFAULT NULL COMMENT '原持有人-网点名称',
  `create_total_amount` bigint(20) NOT NULL COMMENT '原持有人-备用金总金额',
  `currency` int(1) NOT NULL DEFAULT '0' COMMENT '币种 1THB 、2USD、3CNY',
  `return_reason` varchar(5000) DEFAULT '' COMMENT '归还原因',
  `amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '申请归还金额总计',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审核状态-1待审核，2拒绝（驳回），3同意，4撤回',
  `operation_remark` varchar(1000) DEFAULT NULL COMMENT '操作备注（拒绝原因、撤销原因）',
  `approve_at` datetime DEFAULT NULL COMMENT '通过时间（v8150）',
  `created_at` datetime NOT NULL COMMENT '申请时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  `last_update_id` varchar(255) DEFAULT '' COMMENT '最后操作人工号',
  `last_update_name` varchar(255) DEFAULT '' COMMENT '最后操作人姓名（昵称）',
  `last_update_department` varchar(255) DEFAULT '' COMMENT '最后操作人部门',
  `last_update_job_title` varchar(255) DEFAULT '' COMMENT '最后操作人职位',
  `last_update_at` varchar(255) DEFAULT '' COMMENT '最后操作人更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rrno` (`rrno`),
  KEY `idx_create_id` (`create_id`) USING BTREE,
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备用金归还';