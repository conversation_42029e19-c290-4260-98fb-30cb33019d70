-- 添加新币种
INSERT INTO `currency`(`id`, `country_code`, `currency_symbol`) VALUES (6, 'MY', 'MYR');

-- 添加基础币种汇率
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (1, 6, 7.98000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (4, 6, 11.98000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 6, 2263.50000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 1, 1.52600);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 2, 47.84700);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 3, 7.41100);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 4, 0.08800);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 5, 0.00040);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (6, 6, 1.00000);