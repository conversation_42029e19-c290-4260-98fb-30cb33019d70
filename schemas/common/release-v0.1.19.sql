-- sap 需求 1.0
ALTER TABLE `purchase_order`
ADD COLUMN `total_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '含税金额总计（含vat 含pnd',
ADD COLUMN `sap_supplier_no` varchar(12) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT 'SAP供应商号码',
ADD COLUMN `cost_company` varchar(30) NULL COMMENT '费用所属公司id',
ADD COLUMN `cost_company_name` varchar(50) NULL COMMENT '费用所属公司',
ADD COLUMN `cost_center_code` varchar(30) NULL COMMENT '费用所属 中心code',
ADD COLUMN `purchase_type` tinyint(1) NULL COMMENT '采购类型',
ADD COLUMN `sap_order_no` varchar(32) NULL COMMENT 'sap采购订单号',
ADD COLUMN `loan_time_id` varchar(15) NULL COMMENT '信贷期限id',
ADD COLUMN `vendor_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商id';

ALTER TABLE `purchase_order`
DROP INDEX `idx_status`,
ADD INDEX `idx_status`(`status`, `purchase_type`, `cost_company`, `sap_order_no`) USING BTREE;

ALTER TABLE purchase_order_product
ADD COLUMN `unit_code` varchar(10) NULL COMMENT '产品单位id',
ADD COLUMN `no_tax_price` bigint(20) NULL COMMENT '不含税单价(SAP)=不含税单价*不含税单价单位数量SAP',
ADD COLUMN `no_tax_num` int(0) NULL COMMENT '不含税单价单位数量（SAP）',
ADD COLUMN `delivery_date` date NULL COMMENT '计划交货日期',
ADD COLUMN `delivery_place` varchar(10) DEFAULT NULL COMMENT '收货地点';

CREATE TABLE `purchase_product_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `material_id` varchar(20) DEFAULT NULL COMMENT '产品编码',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `base_uom` varchar(255) DEFAULT NULL,
  `product_cate` varchar(15) DEFAULT NULL COMMENT '产品分类',
  `is_del` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `purchase_sap_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `noid` int(11) DEFAULT NULL,
  `uuid` varchar(38) DEFAULT NULL,
  `log` text,
  `create_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

-- sap
BEGIN;
INSERT INTO `purchase_product_list` VALUES (1, '10000000', 'Polo Flash Home Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (2, 'FEX00004', 'Size 1,000*1,200*150 mm. 黑色塑料托盘', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (3, 'FEX00008', 'PC Printer', 'Each', 'G030', 0);
INSERT INTO `purchase_product_list` VALUES (4, 'FEX00015', 'Bluetooth Printer', 'Each', 'G030', 0);
INSERT INTO `purchase_product_list` VALUES (5, 'FEX00017', 'Rain Coat', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (6, 'FEX00018', 'Portable scales', 'Each', 'G030', 0);
INSERT INTO `purchase_product_list` VALUES (7, 'FEX00022', 'Label Fragile', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (8, 'FEX00025', 'Yellow Box Size Mini', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (9, 'FEX00026', 'Yellow Box Size S', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (10, 'FEX00027', 'Yellow Box Size S+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (11, 'FEX00028', 'Yellow Box Size M', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (12, 'FEX00029', 'Yellow Box Size M+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (13, 'FEX00030', 'Yellow Box Size L', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (14, 'FEX00031', 'Clear OPP tape', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (15, 'FEX00032', 'Yellow OPP Flash Express', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (16, 'FEX00033', 'Flash Express Envolope A4', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (17, 'FEX00034', 'Flash Express Envolope A3', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (18, 'FEX00036', 'Expanded nylon side bag', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (19, 'FEX00038', 'Cable tire CN', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (20, 'FEX00040', 'COD Sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (21, 'FEX00041', 'Single protective plastic bag', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (22, 'FEX00042', 'Flash Express Paper Envolope', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (23, 'FEX00045', 'Stretch film', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (24, 'FEX00048', 'Small barcode sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (25, 'FEX00057', 'Flash toy Printing Sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (26, 'FEX00065', 'Address sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (27, 'FEX00073', 'Polo Flash Home Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (28, 'FEX00074', 'Polo Flash Home Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (29, 'FEX00075', 'Polo Flash Home Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (30, 'FEX00076', 'Polo Flash Home Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (31, 'FEX00088', 'Hight value Price Sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (32, 'FEX00092', 'Polo Flash Express Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (33, 'FEX00093', 'Polo Flash Express Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (34, 'FEX00094', 'Jacket Flash Express Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (35, 'FEX00095', 'Jacket Flash Express Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (36, 'FEX00096', 'Jacket Flash Express Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (37, 'FEX00097', 'Jacket Flash Express Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (38, 'FEX00098', 'Jacket Flash Express Size 3XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (39, 'FEX00099', 'T- Shirt Flash Express Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (40, 'FEX00100', 'T- Shirt Flash Express Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (41, 'FEX00101', 'T- Shirt Flash Express Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (42, 'FEX00102', 'T- Shirt Flash Express Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (43, 'FEX00103', 'T- Shirt Flash Express Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (44, 'FEX00104', 'Air Bubble', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (45, 'FEX00107', 'Pants Flash Express Size 28', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (46, 'FEX00108', 'Pants Flash Express Size 30', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (47, 'FEX00109', 'Pants Flash Express Size 32', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (48, 'FEX00110', 'Pants Flash Express Size 34', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (49, 'FEX00111', 'Pants Flash Express Size 36', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (50, 'FEX00112', 'Pants Flash Express Size 38', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (51, 'FEX00113', 'Pants Flash Express Size 40', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (52, 'FEX00115', 'Polo Flash Express Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (53, 'FEX00116', 'Polo Flash Express Size 3XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (54, 'FEX00117', 'Polo Flash Express Size 4XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (55, 'FEX00118', 'Polo Flash Express Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (56, 'FEX00119', 'Polo Flash Express Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (57, 'FEX00120', 'T- Shirt Flash Express Size 3XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (58, 'FEX00121', 'Jacket Flash Express Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (59, 'FEX00122', 'Pants Flash Express Size 42', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (60, 'FEX00123', 'Pants Flash Express Size 44', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (61, 'FEX00125', 'Flash Toy', 'Each', 'G030', 0);
INSERT INTO `purchase_product_list` VALUES (62, 'FEX00126', 'Cable tie without barcode', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (63, 'FEX00127', 'Small Label paper sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (64, 'FEX00138', 'Label Sticker VIP', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (65, 'FEX00140', 'Flash Express black nylon bag', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (66, 'FEX00143', 'Jacket Flash Express Size 6XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (67, 'FEX00148', 'Simple Bag (Small) 80x100 Cm.', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (68, 'FEX00149', 'Cable tire THA', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (69, 'FEX00160', 'BrownBox Size M+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (70, 'FEX00161', 'BrownBox Size L', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (71, 'FEX00162', 'BrownBox Size M', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (72, 'FEX00165', 'BrownBox Size MINI', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (73, 'FEX00166', 'BrownBox Size S', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (74, 'FEX00167', 'BrownBox Size S+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (75, 'FEX00168', 'Flash Express SPEED Sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (76, 'FEX00170', 'Black T-shirt Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (77, 'FEX00171', 'Black T-shirt Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (78, 'FEX00172', 'Black T-shirt Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (79, 'FEX00173', 'Black T-shirt Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (80, 'FEX00174', 'Black T-shirt Size 3XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (81, 'FEX00175', 'Black T-shirt Size 4XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (82, 'FEX00176', 'Black T-shirt Size 6XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (83, 'FEX00177', 'Polo Flash Express Size 6XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (84, 'FEX00178', 'Flash Home T-shirt Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (85, 'FEX00180', 'Small Label paper sticker蓝牙打印纸 2sheet', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (86, 'FEX00182', 'Big Label paper sticker(2sheet)PC 打印纸', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (87, 'FEX00186', 'POUCH', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (88, 'FEX00188', 'Simple Bag (Big) 110x120cm', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (89, 'FEX00189', 'Black T-shirt Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (90, 'FEX00225', 'Sticker cable tire', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (91, 'FEX00226', 'Cable tire', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (92, 'FEX00228', 'Simple Bag 40x60 Cm', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (93, 'FEX00230', 'Jacket Flash Express Size 4XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (94, 'FEX00238', 'A4 Bubble Envelope', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (95, 'FEX00239', 'Cable tire (Yellow)', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (96, 'FEX00267', 'Polo Flash Express Size 6XL/Polo衫6XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (97, 'FEX00289', 'PC Printer paper 100*75', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (98, 'FEX00355', 'เคเบิ้ลไทร์ เปล่า轧带 (Bagging)', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (99, 'FEX00356', 'กระดาษเลเบลเล็ก 2 ชั้น/蓝牙打印纸 两层มัด', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (100, 'FEX00359', 'Simple Bag 40x60 Cm 包裹袋子', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (101, 'FEX00360', '简易集包袋（大） 110x120cm', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (102, 'FEX00384', 'ที่พยุงหลัง 背部支撑带 Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (103, 'FEX00385', 'ที่พยุงหลัง 背部支撑带 Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (104, 'FEX00386', 'ที่พยุงหลัง 背部支撑带 Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (105, 'FEX00387', 'ที่พยุงหลัง 背部支撑带 Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (106, 'FEX00406', 'เสื้อยืด Flash Home T恤 size S(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (107, 'FEX00407', 'เสื้อยืด Flash Home T恤 size M(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (108, 'FEX00408', 'เสื้อยืด Flash Home T恤 size L(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (109, 'FEX00409', 'เสื้อยืด Flash Home T恤 size XL(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (110, 'FEX00410', 'เสื้อยืด Flash Home T恤 size 2XL(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (111, 'FEX00411', 'เสื้อยืด Flash Home T恤 size S(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (112, 'FEX00412', 'เสื้อยืด Flash Home T恤 size M(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (113, 'FEX00413', 'เสื้อยืด Flash Home T恤 size L(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (114, 'FEX00414', 'เสื้อยืด Flash Home T恤 size XL(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (115, 'FEX00415', 'เสื้อยืด Flash Home T恤 size 2XL(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (116, 'FEX00416', 'Jacket Flash Home 外套 Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (117, 'FEX00417', 'Jacket Flash Home 外套 Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (118, 'FEX00418', 'Jacket Flash Home 外套 Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (119, 'FEX00419', 'Jacket Flash Home 外套 Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (120, 'FEX00420', 'Jacket Flash Home 外套 Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (121, 'FEX00452', '蓝牙打印纸 两层', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (122, 'FEX00473', 'เสื้อยืด Flash Home T恤 size 3XL(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (123, 'FEX00474', 'เสื้อยืด Flash Home T恤 size 4XL(Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (124, 'FEX00477', 'เสื้อยืด Flash Home T恤 size 3XL(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (125, 'FEX00478', 'เสื้อยืด Flash Home T恤 size 4XL(Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (126, 'FEX00498', 'Wristband Flash Express 黑色手环 (Black)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (127, 'FEX00499', 'Wristband Flash Express 黄色手环 (Yellow)', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (128, 'FEX00518', 'flash Express 安全帽 ใบ 个 *DAMAGE*', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (129, 'FEX00578', 'กระดาษสติ๊กเกอร์ A4 不干胶', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (130, 'FEX00591', 'กล่องผลไม้ 水果箱 Size S+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (131, 'FEX00592', 'กล่องผลไม้ 水果箱 Size M', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (132, 'FEX00593', 'กล่องผลไม้ 水果箱 Size M+', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (133, 'FEX00594', 'กล่องผลไม้ 水果箱 Size L', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (134, 'FEX00651', 'NEW Flashtoy sticker', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (135, 'FEX00673', 'Flash toy (ไม่มีกาว) Flash toy纸(无胶背)', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (136, 'FEX00691', 'กล่อง Amazon 纸箱 Size S', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (137, 'FEX00748', 'กระดาษ Flashtoy 纸 57*30*8 mm.', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (138, 'FEX00756', 'Air bubble แบบแผ่น 气泡膜 20*70 cm', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (139, 'FEX00803', 'Envelope A4 for Bitkub project A4 塑料袋', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (140, 'FEX00884', 'กระดาษเลเบลม้วนขนาด 10.0X15.0 CM', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (141, 'FEX00885', 'กระดาษเลเบลม้วนขนาด 5.0X1.5 CM', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (142, 'FEX00912', 'Flash New polo Uniform (White)Size S', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (143, 'FEX00913', 'Flash New polo Uniform (White)Size M', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (144, 'FEX00914', 'Flash New polo Uniform (White)Size L', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (145, 'FEX00915', 'Flash New polo Uniform (White)Size XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (146, 'FEX00916', 'Flash New polo Uniform (White) Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (147, 'FEX00917', 'Flash New polo Uniform (White) Size 2XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (148, 'FEX00918', 'Flash New polo Uniform (White) Size 3XL', 'Each', 'G020', 0);
INSERT INTO `purchase_product_list` VALUES (149, 'FEX00919', 'Premium Box size Mini', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (150, 'FEX00920', 'Premium Box size S', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (151, 'FEX00921', 'Premium Box size S', 'Each', 'G010', 0);
INSERT INTO `purchase_product_list` VALUES (152, 'FEX00922', 'Premium Box size M', 'Each', 'G010', 0);
COMMIT;

INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_loan_time', '[{\"id\":\"0001\",\"description\":\"due net payable immediately\"},{\"id\":\"0002\",\"description\":\"7 days due net\"},{\"id\":\"0004\",\"description\":\"30 days from invoice date\"},{\"id\":\"Z001\",\"description\":\"15 days from invoice date\"},{\"id\":\"Z002\",\"description\":\"45 days from invoice date\"},{\"id\":\"Z003\",\"description\":\"60 days from invoice date\"}]', '信贷期限', '2021-06-24 07:19:29', '2021-07-08 06:23:25');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_unit', '[{\"id\":\"5B\",\"description\":\"batch\"},{\"id\":\"E49\",\"description\":\"wday\"},{\"id\":\"EA\",\"description\":\"es\"},{\"id\":\"XBG\",\"description\":\"bag\"},{\"id \":\"XBX\",\"description\":\"box\"},{\"id\":\"XCR\",\"description\":\"crate\"},{\"id\":\"XCS\",\"description\":\"case\"},{\"id\":\"XPX\",\"description\":\"pallet\"},{\"id\":\"XRO\",\"description\":\"roll\"},{\"id\":\"XST\",\"description\":\"sheet\"},{\"id\":\"XSX\",\"description\":\"set\"}]', '产品单位', '2021-06-24 07:31:03', '2021-06-24 12:03:23');

INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_type', '[{\"id\":\"18\",\"name_key\":\"purchase_stock_type_key\"},{\"id\":\"19\",\"name_key\":\"purchase_service_key\"},{\"id\":\"84\",\"name_key\":\"purchase_cost_key\"},{\"id\":\"1\",\"name_key\":\"purchase_capital_assets_key\"}]', '采购类型', '2021-06-24 09:29:29', '2021-06-24 12:46:19');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_place', '[{\"id\":\"1\",\"place_name\":\"FEX001\"}]', '采购单收货地点', '2021-06-26 06:31:27', '2021-07-06 07:24:47');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_sap_company_ids', '1', 'sap同步公司id', '2021-07-01 07:55:41', '2021-07-01 07:56:05');

-- 子公司审批流
-- 报销
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (984, 44, 'AP(Flash Pay 62265)', 3, 0, NULL, NULL, NULL, NULL, 1, '62265', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (985, 44, 'AP(Flash Money 60277)', 3, 0, NULL, NULL, NULL, NULL, 1, '60277', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (986, 44, 'AP(F-commmerce 60277)', 3, 0, NULL, NULL, NULL, NULL, 1, '60277', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (987, 44, 'AP Supervisor（Flash Pay 73599）', 3, 0, NULL, NULL, NULL, NULL, 1, '73599', NULL, '{\"main\":[],\"meta\":[\"ledger_account_id\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (988, 44, 'AP Supervisor（Flash Money 72336）', 3, 0, NULL, NULL, NULL, NULL, 1, '72336', NULL, '{\"main\":[],\"meta\":[\"ledger_account_id\"]}', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (989, 44, 'AP Supervisor（F-commmerce 73599）', 3, 0, NULL, NULL, NULL, NULL, 1, '73599', NULL, '{\"main\":[],\"meta\":[\"ledger_account_id\"]}', NULL);


-- 采购付款申请单
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (990, 37, '指定工号（AP FlashPay62265）', 3, 0, NULL, NULL, NULL, NULL, 1, '62265', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (991, 37, '指定工号（AP FlashMoney60277）', 3, 0, NULL, NULL, NULL, NULL, 1, '60277', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (992, 37, '指定工号（AP F-commerce60277）', 3, 0, NULL, NULL, NULL, NULL, 1, '60277', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (993, 37, '指定工号（APS FlashPay73599）', 3, 0, NULL, NULL, NULL, NULL, 1, '73599', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (994, 37, '指定工号（APS FlashMoney72336）', 3, 0, NULL, NULL, NULL, NULL, 1, '72336', NULL, '', NULL);
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (995, 37, '指定工号（APS F-commerce73599）', 3, 0, NULL, NULL, NULL, NULL, 1, '73599', NULL, '', NULL);


-- flashcommerce
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (996, 55, 'AP Supervisor（Fcommerce）', 3, 0, NULL, NULL, NULL, NULL, 1, '73599', NULL, '{\"amount_detail\":[\"wht_category\",\"wht_rate\",\"ledger_account_id\"]}', NULL);

-- 合同管理优化
-- 添加其他合同数据查询菜单
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (385, 'menu.contract.searchList', '数据查询', '数据查询', 178, 1, 2, '2020-01-06 08:45:51');
-- 数据查询-查看
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (386, 'action.contract.search.detail', '查看', '数据查询-查看', 385, 2, 1, NULL);
-- 合同申请-下载审批流记录
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (387, 'action.contract.apply.wfdownload', '审批记录下载', '合同申请-审批记录下载', 2, 2, 1, NULL);
-- 更新其他合同数据查询接口
UPDATE `permission` set `ancestry`=385,`name`='查询' where `id`=177;

-- 合同表添加新字段
ALTER TABLE `contract` ADD COLUMN `contract_desc` varchar(1000) DEFAULT '' COMMENT '合同说明';
ALTER TABLE `contract` ADD COLUMN `company_code` tinyint(3) DEFAULT 0 COMMENT '合同所属公司(1:Flash Express，2:Flash Fullfilment、3:Flash Money、4:FCommerce、5:Flash Laos、6:Flash Pay、7:Flash HR)';
ALTER TABLE `contract` ADD COLUMN `is_group_contract` tinyint(1) DEFAULT 0 COMMENT '是否为集团合作间合同（0：否；1：是）';
ALTER TABLE `contract` ADD COLUMN `company_relation_code` tinyint(3) DEFAULT 0 COMMENT '合同关联公司(1:Flash Express，2:Flash Fullfilment、3:Flash Money、4:FCommerce、5:Flash Laos、6:Flash Pay、7:Flash HR)';

-- 新建合同公司表
CREATE TABLE `contract_company` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `company_code` varchar(10) NOT NULL DEFAULT '' COMMENT '公司编号',
        `company_name` varchar(50) NOT NULL DEFAULT '' COMMENT '公司名称',
        `is_delete` tinyint(3) DEFAULT 0 COMMENT '是否禁用（0:未禁用, 1:禁用）',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (1, '1', 'Flash Express', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (2, '2', 'Flash Fullfilment', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (3, '3', 'Flash Money', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (4, '4', 'FCommerce', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (5, '5', 'Flash Laos', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (6, '6', 'Flash Pay', 0);
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`,`is_delete`) VALUES (7, '7', 'Flash HR', 0);

-- contract合同表sub_cno字段更新
ALTER TABLE `contract` MODIFY COLUMN `sub_cno` varchar(30) DEFAULT '' COMMENT '关联的主合同编号';
-- contract_archive合同归档表sub_cno字段更新
ALTER TABLE `contract_archive` MODIFY COLUMN `sub_cno` varchar(30) DEFAULT '' COMMENT '关联的主合同编号';

-- 增加权限
INSERT INTO `permission`(`key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES ('action.transfer.search.update_info', '转岗管理-修改角色和上级信息', NULL, 274, 2, 7, NULL);