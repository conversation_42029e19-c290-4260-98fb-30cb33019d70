-- MySQL dump 10.13  Distrib 8.0.18, for Win64 (x86_64)
--
-- Host: *************    Database: oa
-- ------------------------------------------------------
-- Server version	5.6.39-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- ----------------------------
--  Table structure for `contract`
-- ----------------------------
DROP TABLE IF EXISTS `contract`;
CREATE TABLE `contract` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同编号',
  `cname` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同名称',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '合同状态',
  `template_id` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '模版ID',
  `vendor_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码',
  `is_master` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否是主合同',
  `sub_cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联的主合同编号',
  `amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '合同金额，千分位数字',
  `payment_currency` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '付款币种，1:泰铢，2:美元，3:人民币',
  `refuse_reason` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `contract_file` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同文件',
  `attachment` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件,多个使用","分割',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '申请人姓名',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `effected_at` datetime DEFAULT NULL COMMENT '生效时间',
  `finished_at` datetime DEFAULT NULL COMMENT '完结时间',
  `approved_at` datetime DEFAULT NULL COMMENT '通过时间',
  `rejected_at` datetime DEFAULT NULL COMMENT '驳回时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `cno_unq_inx` (`cno`) USING BTREE,
  KEY `status_inx` (`status`),
  KEY `template_id_inx` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同主表';

-- ----------------------------
--  Table structure for `contract_archive`
-- ----------------------------
DROP TABLE IF EXISTS `contract_archive`;
CREATE TABLE `contract_archive` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同编号',
  `cname` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同名称',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '归档状态，1:待归档，2:已归档',
  `template_id` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '模版ID',
  `is_master` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否是主合同',
  `sub_cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联的主合同编号',
  `amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '合同金额，千分位数字',
  `payment_currency` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '付款币种，1:泰铢，2:美元，3:人民币',
  `contract_file` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同文件',
  `holder_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同原件保管人姓名',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '申请人姓名',
  `created_at` datetime DEFAULT NULL COMMENT '创建日期',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `approved_at` datetime DEFAULT NULL COMMENT '通过时间',
  `filing_at` datetime DEFAULT NULL COMMENT '归档时间',
  `filing_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '归档人姓名',
  `filing_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '归档人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `cno_unq_inx` (`cno`) USING BTREE,
  KEY `status_inx` (`status`),
  KEY `template_id_inx` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='合同归档表';

-- ----------------------------
--  Table structure for `contract_purchasing`
-- ----------------------------
DROP TABLE IF EXISTS `contract_purchasing`;
CREATE TABLE `contract_purchasing` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同编号',
  `cname` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同名称',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '合同状态',
  `template_id` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '模版ID',
  `vendor_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码',
  `is_master` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否是主合同',
  `sub_cno` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关联的主合同编号',
  `delivery_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '交货日期',
  `delivery_address` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '交货地址',
  `payment_cycle` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '付款周期',
  `payment_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '付款方式，1:银行转账；2:现金；3:支票',
  `payment_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '付款时间',
  `amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '合同金额，千分位数字',
  `payment_currency` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '付款币种，1:泰铢，2:美元，3:人民币',
  `refuse_reason` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `content` varchar(300) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '合同备注',
  `attachment` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件,多个使用","分割',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `effected_at` datetime DEFAULT NULL COMMENT '生效时间',
  `finished_at` datetime DEFAULT NULL COMMENT '完结时间',
  `approved_at` datetime DEFAULT NULL COMMENT '通过时间',
  `rejected_at` datetime DEFAULT NULL COMMENT '驳回时间',
  PRIMARY KEY (`id`),
  KEY `cno_inx` (`cno`),
  KEY `status_inx` (`status`),
  KEY `template_id_inx` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采购类型合同';

-- ----------------------------
--  Table structure for `permission`
-- ----------------------------
DROP TABLE IF EXISTS `permission`;
CREATE TABLE `permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `key` varchar(100) DEFAULT NULL COMMENT '权限key',
  `name` varchar(100) DEFAULT NULL COMMENT '权限名称',
  `description` varchar(100) DEFAULT NULL COMMENT '权限描述',
  `ancestry` int(11) DEFAULT NULL COMMENT '所属上级ID',
  `type` tinyint(4) DEFAULT NULL COMMENT '权限类型，1菜单，2操作',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COMMENT='系统权限';

-- ----------------------------
--  Table structure for `staff_permission`
-- ----------------------------
DROP TABLE IF EXISTS `staff_permission`;
CREATE TABLE `staff_permission` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '用户ID',
  `permission_ids` varchar(1000) DEFAULT NULL COMMENT '用户权限列表',
  `is_granted` tinyint(4) DEFAULT NULL,
  `last_updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_staff_id` (`staff_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='用户权限表';

-- ----------------------------
--  Table structure for `translations`
-- ----------------------------
DROP TABLE IF EXISTS `translations`;
CREATE TABLE `translations` (
  `lang` varchar(10) CHARACTER SET utf8 NOT NULL DEFAULT '',
  `t_key` varchar(100) NOT NULL DEFAULT '',
  `t_value` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `type` int(2) DEFAULT '1' COMMENT '未启用',
  PRIMARY KEY (`lang`,`t_key`),
  KEY `idx_t_key` (`t_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='语言翻译';

-- ----------------------------
--  Table structure for `vendor`
-- ----------------------------
DROP TABLE IF EXISTS `vendor`;
CREATE TABLE `vendor` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `vendor_id` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商编码',
  `vendor_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商名称',
  `company_website` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司官网',
  `ownership` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '供应商归属地，1:中国，2:泰国',
  `company_nature` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '公司性质，1：自然人；2：公司',
  `certificate_type` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '证件类型，1：社会信用代码证件；2：护照；3：身份证',
  `identification_no` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '证件号码',
  `artificial_person` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '法人代表',
  `company_phone` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '公司电话',
  `company_address` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '供应商地址',
  `contact` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人',
  `contact_phone` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人电话',
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系人邮箱',
  `bank_code` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '银行编码',
  `bank_no` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '银行卡号',
  `attachment` varchar(1000) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件,多个使用","分割',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '供应商状态',
  `create_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '申请人',
  `create_name` varchar(30) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '申请人姓名',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vendor_id_inx` (`vendor_id`),
  KEY `status_inx` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='供应商表';

-- ----------------------------
--  Table structure for `workflow`
-- ----------------------------
DROP TABLE IF EXISTS `workflow`;
CREATE TABLE `workflow` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL COMMENT '工作流名称',
  `code` varchar(45) DEFAULT NULL COMMENT '工作流代码',
  `description` varchar(45) DEFAULT NULL COMMENT '工作流描述',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='工作流表';

-- ----------------------------
--  Table structure for `workflow_audit_log`
-- ----------------------------
DROP TABLE IF EXISTS `workflow_audit_log`;
CREATE TABLE `workflow_audit_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `request_id` int(11) DEFAULT NULL COMMENT '工作流申请ID',
  `flow_id` int(11) DEFAULT NULL COMMENT '工作流ID',
  `flow_node_id` int(11) DEFAULT NULL COMMENT '工作流节点ID',
  `staff_id` int(11) DEFAULT NULL COMMENT '审批人ID',
  `staff_name` varchar(100) DEFAULT NULL COMMENT '审批人姓名',
  `staff_department` varchar(100) DEFAULT NULL COMMENT '审批人部门名称',
  `audit_action` tinyint(4) DEFAULT NULL COMMENT '审批结果，1同意，2驳回',
  `audit_info` varchar(1000) DEFAULT NULL COMMENT '审批备注',
  `audit_at` datetime DEFAULT NULL COMMENT '审批时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_id` (`request_id`)
) ENGINE=InnoDB AUTO_INCREMENT=99 DEFAULT CHARSET=utf8mb4 COMMENT='工作流审核日志';

-- ----------------------------
--  Table structure for `workflow_node`
-- ----------------------------
DROP TABLE IF EXISTS `workflow_node`;
CREATE TABLE `workflow_node` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `flow_id` int(11) DEFAULT NULL COMMENT '工作流ID',
  `name` varchar(45) DEFAULT NULL COMMENT '节点名称',
  `type` tinyint(4) DEFAULT NULL COMMENT '节点类型，1开始，2条件判断，3审批动作，4通过，5驳回',
  `expression` varchar(1000) DEFAULT NULL COMMENT '条件判断节点判断方法',
  `approve_next_node_id` int(11) DEFAULT NULL COMMENT '通过后下一节点ID',
  `reject_next_node_id` int(11) DEFAULT NULL COMMENT '驳回后下一节点ID',
  `audit_type` varchar(45) DEFAULT NULL COMMENT '审核逻辑关系，1 and，2 or',
  `auditor_type` tinyint(4) DEFAULT NULL COMMENT '审核人类型，1 员工ID,2 角色',
  `auditor_id` int(11) DEFAULT NULL COMMENT '审核人/角色ID',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_flow_id` (`flow_id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='工作流节点表	';

-- ----------------------------
--  Table structure for `workflow_request`
-- ----------------------------
DROP TABLE IF EXISTS `workflow_request`;
CREATE TABLE `workflow_request` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) DEFAULT NULL COMMENT '申请名称',
  `biz_type` tinyint(4) DEFAULT NULL COMMENT '业务数据类型,1合同审批',
  `biz_value` varchar(100) DEFAULT NULL COMMENT '业务数据ID',
  `flow_id` int(11) DEFAULT NULL COMMENT '流程ID',
  `current_flow_node_id` int(11) DEFAULT NULL COMMENT '当前流程节点ID',
  `current_node_auditor_id` varchar(1000) DEFAULT NULL COMMENT '当前审批人ID',
  `state` tinyint(4) DEFAULT NULL COMMENT '当前状态，1待审批，2通过，3驳回',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '申请提交人ID',
  `viewer_ids` varchar(2000) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `rejected_at` datetime DEFAULT NULL,
  `approved_at` datetime DEFAULT NULL,
  `rejeact_info` varchar(1000) DEFAULT NULL COMMENT '驳回原因',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_t_value` (`biz_type`,`biz_value`)
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COMMENT='工作流申请';

-- ----------------------------
--  Table structure for `workflow_request_node_auditor`
-- ----------------------------
DROP TABLE IF EXISTS `workflow_request_node_auditor`;
CREATE TABLE `workflow_request_node_auditor` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `req_id` int(11) DEFAULT NULL COMMENT '申请ID',
  `flow_id` int(11) DEFAULT NULL COMMENT '工作流ID',
  `flow_node_id` int(11) DEFAULT NULL COMMENT '工作流节点ID',
  `staff_id` int(11) DEFAULT NULL COMMENT '审批人ID',
  `staff_name` varchar(50) DEFAULT NULL COMMENT '审批人姓名',
  `staff_department` varchar(100) DEFAULT NULL COMMENT '审批人部门名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作流申请节点审核人表';
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2020-01-09 19:35:25
