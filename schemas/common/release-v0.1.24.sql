-- oa数据库 多国家执行

CREATE TABLE `contract_store_renting_area` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '网点租房合同表id',
`contract_store_renting_id` int(11) unsigned NOT NULL COMMENT '网点租房合同主表id',
`start_time` datetime DEFAULT NULL COMMENT '区间开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '区间结束时间',
  `area_service_amount_no_tax` float(10,2) DEFAULT NULL COMMENT '区域服务费金额',
  `area_vat_rate` tinyint(1) DEFAULT NULL COMMENT 'WHT税率：值为整数(0-100), 单位 %',
  `area_service_amount_vat` float(10,2) DEFAULT NULL COMMENT '区域服务费VAT税额',
  `area_wht_category` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT类别',
  `area_wht_rate` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT税率',
  `area_amount_wht` float(10,2) DEFAULT NULL COMMENT '区域服务费WHT金额',
 `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_contract_id` (`contract_store_renting_id`) USING BTREE
 ) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='网点租房合同';



alter table contract_store_renting modify `area_service_amount_no_tax` float(10,2) DEFAULT NULL COMMENT '区域服务费金额 已废弃 迁移contract_store_renting_area';

alter table contract_store_renting modify
`area_vat_rate` tinyint(1) DEFAULT NULL COMMENT 'WHT税率：值为整数(0-100), 单位 % 迁移contract_store_renting_area';

alter table contract_store_renting modify
 `area_service_amount_vat` float(10,2) DEFAULT NULL COMMENT '区域服务费VAT税额 迁移contract_store_renting_area';

alter table contract_store_renting modify
`area_wht_category` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT类别 迁移contract_store_renting_area';

alter table contract_store_renting modify
`area_wht_rate` tinyint(1) DEFAULT NULL COMMENT '区域服务费WHT税率 迁移contract_store_renting_area';

alter table contract_store_renting modify
`area_amount_wht` float(10,2) DEFAULT NULL COMMENT '区域服务费WHT金额 迁移contract_store_renting_area';



 -- 初始化数据 多国家执行

insert into contract_store_renting_area
(contract_store_renting_id,area_service_amount_no_tax,area_vat_rate,area_service_amount_vat,area_wht_category,area_wht_rate,area_amount_wht,created_at,updated_at)
select id ,area_service_amount_no_tax,area_vat_rate,area_service_amount_vat,area_wht_category,area_wht_rate,area_amount_wht,created_at,update_at from contract_store_renting;
