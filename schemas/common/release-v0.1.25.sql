-- 所有国家 OA数据库执行
-- 新添加开户行
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (85, 'bank.85', 'ACLEDA BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (86, 'bank.86', 'AGRICULTURAL PROMOTION BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (87, 'bank.87', 'ANZ BANK(LAO)');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (88, 'bank.88', 'BANGKOK BANK PLC, VIENTIANE BRANCH');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (89, 'bank.89', 'BANK OF CHINA LIMITED VIENTIANE BRANCH');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (90, 'bank.90', 'BANK OF THE LAO P.D.R.');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (91, 'bank.91', 'BANQUE FRANCO');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (92, 'bank.92', 'BANQUE POUR LE COMMERCE EXTERIEUR LAO PUBLIC');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (93, 'bank.93', 'FIRST COMMERCIAL');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (94, 'bank.94', 'INDOCHINA BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (95, 'bank.95', 'INDUSTRIAL AND COMMERCIAL BANK OF CHINA LIMITED VIENTIANE BRANCH');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (96, 'bank.96', 'INTERNATIONAL COMMERCIAL BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (97, 'bank.97', 'JOINT DEVELOPMENT BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (98, 'bank.98', 'KASIKORNTHAI BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (99, 'bank.99', 'LAO CHINA BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (100, 'bank.100', 'LAO CONSTRUCTION BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (101, 'bank.101', 'LAO DEVELOPMENT BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (102, 'bank.102', 'LAO-VIET BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (103, 'bank.103', 'MARUHAN JAPAN BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (104, 'bank.104', 'MAYBANK LAO');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (105, 'bank.105', 'PHONGSAVANH BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (106, 'bank.106', 'PUBLIC BANK, VIENTIANE BRANCH, LAO P.D.R.');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (107, 'bank.107', 'RHB BANK LAO');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (108, 'bank.108', 'SAIGON HANOI COMMERCIAL JOINT STOCK BANK LAOS BRANCH');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (109, 'bank.109', 'SIAM COMMERCIAL BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (110, 'bank.110', 'ST BANK');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (111, 'bank.111', 'VIETNAM JOINT STOCK COMMERCIAL BANK FOR INDUSTRY AND TRADE LAO P.D.P BRANCH');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`) VALUES (122, 'bank.122', 'LAND AND HOUSES BANK');

-- 添加新币种
INSERT INTO `currency`(`id`, `country_code`, `currency_symbol`) VALUES (5, 'LA', 'LAK');

-- 添加基础币种汇率
ALTER TABLE `setting_exchange_rate` MODIFY COLUMN `rate` decimal(10,5) NOT NULL COMMENT '汇率换算';
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (1, 5, 250.00000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (4, 5, 200.00000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 1, 289.48000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 2, 9517.08000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 3, 1468.07000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 4, 188.90000);
INSERT INTO `setting_exchange_rate`(`base_currency`, `format_currency`, `rate`) VALUES (5, 5, 1.00000);

-- 所有国家都要扩展
ALTER TABLE `purchase_order` MODIFY `payment_to` varchar(500) NOT NULL COMMENT '支付给';
ALTER TABLE `purchase_payment` MODIFY `payment_to` varchar(500) DEFAULT '' COMMENT '支付给';