-- 9098 李杰-----------------------
-- 增加审批流  --oa
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (71, '借款归还', 1, '借款归还_v9098', '2021-06-10 16:52:04');

INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (942, 71, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (943, 71, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (945, 71, '借款财务(58292,32739)', 3, 0, NULL, NULL, NULL, NULL, 1, '58292,32739', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (946, 71, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);


INSERT INTO `workflow_node_relate`(`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 71, 945, 946, NULL, NULL, '借款财务(58292,32739)->结束', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');
INSERT INTO `workflow_node_relate`(`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 71, 943, 945, NULL, NULL, '直接上级->借款财务(58292,32739)', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');
INSERT INTO `workflow_node_relate`(`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 71, 942, 943, NULL, NULL, '员工提交->直接上级', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');

--
ALTER TABLE `purchase_payment`
ADD COLUMN `cur_amount` bigint(20) NULL DEFAULT 0 COMMENT '本次付款金额' AFTER `receipt_amount`;

update workflow_node set can_edit_field = '{"main":["wht_amount","real_amount","is_tax","cur_amount"],"meta":["wht_type","wht_ratio","wht_amount","real_amount","category_a","category_b","finance_code"]}' where id in (370,371,372);


-- v9142
-- 二级部门负责人 审批类型=5
INSERT INTO `workflow_node` (`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (973, 42, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, NULL, NULL, '', NULL);

-- 改3行，对原来到一级部门负责人的，先到二级部门负责人=看了线上也一样
update workflow_node_relate set to_node_id='973',remark=concat(remark,"=改成到二级部门负责人") where id in (813,814,816);


INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 42, 973, 497, NULL, NULL, '二级部门负责人->一级部门负责人', 10, now(), now());