-- v9331详情表
ALTER TABLE `purchase_payment_receipt`
    MODIFY COLUMN `pop_id` int(11) NULL DEFAULT 0 COMMENT '采购订单产品ID' AFTER `ppid`,
    ADD COLUMN `pap_id` int(11) NULL DEFAULT 0 COMMENT '采购申请单产品id' AFTER `pop_id`,
    MODIFY COLUMN `ticket_amount` bigint(20) UNSIGNED NOT NULL COMMENT '发票金额（含税）' AFTER `ticket_number`,
    ADD COLUMN `ticket_amount_not_tax` bigint(20) UNSIGNED NOT NULL COMMENT '发票金额（不含税）' AFTER `ticket_amount`;


update purchase_payment_receipt set ticket_amount_not_tax= ticket_amount;


-- 主表
ALTER TABLE `purchase_payment`
    MODIFY COLUMN `po_id` int(11) NULL DEFAULT 0 COMMENT '采购订单id' AFTER `method`,
    MODIFY COLUMN `ponos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '采购订单编号 逗号分割' AFTER `po_id`,
    ADD COLUMN `is_link_pa` tinyint(2) NULL DEFAULT 0 COMMENT '是否采购框架合同（直接关联pa）' AFTER `ponos`,
    ADD COLUMN `pa_id` int(11) NULL DEFAULT 0 COMMENT '采购申请单id' AFTER `is_link_pa`,
    ADD COLUMN `pano` varchar(100) NULL DEFAULT '' COMMENT '采购申请单编号' AFTER `pa_id`,
    ADD COLUMN `contract_no` varchar(255) NULL DEFAULT '' COMMENT '合同编号' AFTER `pano`;



ALTER TABLE `purchase_payment_receipt`
    MODIFY COLUMN `vendor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '产品供应商名称' AFTER `pap_id`;


ALTER TABLE `purchase_apply`
    MODIFY COLUMN `is_cite` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否被采购订单全部引用 ' AFTER `operation_remark`,
    ADD COLUMN `is_link_po` tinyint(1) NULL DEFAULT 0 COMMENT '是否被采购订单关联着(老的数据都是关联着，不能直接关联采购付款申请单)' AFTER `is_cite`,
DROP INDEX `idx_cite`,
ADD INDEX `idx_cite_is_link_po`(`is_cite`, `is_link_po`) USING BTREE;

-- 老数据都不能直接关联采购付款申请单
update purchase_apply set is_link_po=1;




--v9364



ALTER TABLE `ordinary_payment`
ADD COLUMN `payee_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '收款人类型1供应商2个人',
ADD COLUMN `amount_total` decimal(12, 2) UNSIGNED NOT NULL DEFAULT '0.00' COMMENT '总金额';

ALTER TABLE `ordinary_payment_extend`
MODIFY COLUMN `pay_signer_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付信息-签收人';

CREATE TABLE `ordinary_payment_personal` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ordinary_payment_id` int(10) unsigned NOT NULL COMMENT '普通付款表主键',
  `staff_info_id` varchar(32) NOT NULL DEFAULT '' COMMENT '收款人工号',
  `bank_name` varchar(16) NOT NULL DEFAULT '' COMMENT '银行名称',
  `bank_no_name` varchar(32) NOT NULL DEFAULT '' COMMENT '银行账户名称',
  `bank_no` varchar(19) NOT NULL DEFAULT '' COMMENT '银行账号',
  `name` varchar(32) NOT NULL DEFAULT '' COMMENT '联系人',
  `mobile` varchar(20) NOT NULL DEFAULT '' COMMENT '联系方式',
  `amount` decimal(12,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '金额',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_opid` (`ordinary_payment_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;



INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('payee_budget_config', '[\"008\"]', '普通付款类型配置', '2021-07-13 07:29:50', '2021-07-20 10:16:01');

-- 菜单添加
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (451, 'action.access_data_sys.staff_manage', '权限配置', '取数工单系统 - 权限配置 （员工管理）', 282, 2, 1, '2021-07-17 03:50:32');

-- 员工表加创建者/更新人工号字段
ALTER TABLE `access_data_staff_info`
ADD COLUMN `created_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者工号' AFTER `update_time`,
ADD COLUMN `updated_id` int(0) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新者工号' AFTER `created_id`;

-- 更新staff_permission表permission_ids长度
ALTER TABLE `staff_permission` MODIFY COLUMN `permission_ids` text COMMENT '用户权限列表';
