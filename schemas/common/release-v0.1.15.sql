-- 9202 李杰-----------------------
-- 增加审批流  --oa

INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (977, 68, 'Payroll Manage', 3, 0, NULL, NULL, NULL, '1', 1, '32122', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (976, 25, 'Payroll Manage', 3, 0, NULL, NULL, NULL, '1', 1, '32122', NULL, '', NULL);

INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 25, 223, 976, '$p1 != 14', 'getKEY', '上级领导->Payroll Manager', 11, '2021-06-16 12:44:28', '2021-06-16 04:28:25');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 25, 976, 224, '($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)', 'getAmount,getCurrency',  'Payroll Manager->CPO', 10, '2021-06-16 12:44:28', '2021-06-16 04:28:25');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 25, 976, 225, '($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1<10000 && $p2==3)', 'getAmount,getCurrency',  'Payroll Manager->AP(泰国)', 10, '2021-06-16 12:44:28', '2021-06-16 04:28:25');

INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 68, 948, 977, '$p1 != 14', 'getKEY', '上级领导->Payroll Manager', 11, '2021-06-16 12:44:28', '2021-06-16 04:28:25');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 68, 977, 922, '($p1>=50000 && $p2==1) || ($p1>=1600 && $p2==2) || ($p1>=10000 && $p2==3)', 'getAmount,getCurrency', 'Payroll Manager->CPO', 10, '2021-06-16 12:44:28', '2021-06-16 04:28:25');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 68, 977, 924, '($p1<50000 && $p2==1) || ($p1<1600 && $p2==2) || ($p1<10000 && $p2==3)', 'getAmount,getCurrency', 'Payroll Manager->AP(泰国)', 10, '2021-06-16 12:44:28', '2021-06-16 04:28:25');


INSERT INTO `permission`( `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES ( 'action.payment.data.reply', '付款回复', '付款管理-付款回复', 207, 1, 5, '2021-06-19 07:09:41');

INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (70, '租房合同', 1, '租房合同_v0609(新版去掉财务审批节点)', '2021-06-09 16:51:57');

CREATE TABLE `crm_quotation_apply_coupon` (
                                              `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
                                              `quoted_price_list_sn` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '报价单号',
                                              `coupon_type` tinyint(1) DEFAULT NULL COMMENT '优惠卷类型',
                                              `coupon_num` int(10) unsigned DEFAULT NULL COMMENT '优惠卷数量',
                                              `coupon_expire` tinyint(1) DEFAULT NULL COMMENT '有效期时间',
                                              `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '表的状态 ：是否已经删除 0:否 1:是',
                                              `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
                                              `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_coupon_quoted_price_list_sn` (`quoted_price_list_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='折扣申请优惠券表';



