-- 更新审批流节点
UPDATE `workflow_node` SET `auditor_id`='60712,52731' where id=979;
UPDATE `workflow_node` SET `auditor_id`='' where id=980;
UPDATE `workflow_node` SET `auditor_id`='62721' where id=981;
UPDATE `workflow_node` SET `auditor_id`='' where id=982;

-- WHT类别和对应汇率
UPDATE `setting_env` SET `val`='{\"3\":{\"name\":\"\/\",\"tax\":[0]}}' where `code`='payment_wht_tax';

-- 支付人工号 (线上已执行)
UPDATE `setting_env` SET `val`='76260,65382' where `code`='purchase_payment_pay_staff_id';

-- 更新采购申请单节点审核人
update `workflow_node` set `auditor_id`='60712' where `id`=312;
update `workflow_node` set `auditor_id`='52731' where `id`=313;
update `workflow_node` set `auditor_id`='17152' where `id`=314;
update `workflow_node` set `auditor_id`='76895' where `id`=315;
update `workflow_node` set `auditor_id`='' where `id`=720;
-- 更新采购申请单节点关系
update `workflow_node_relate` set `valuate_formula`='$p1<6500000',`valuate_code`='getAmount' where `id`=455;
update `workflow_node_relate` set `valuate_formula`='$p1>=6500000',`valuate_code`='getAmount' where `id`=456;
update `workflow_node_relate` set `valuate_formula`='$p1<*********',`valuate_code`='getAmount' where `id`=457;
update `workflow_node_relate` set `valuate_formula`='$p1>=*********',`valuate_code`='getAmount' where `id`=458;
update `workflow_node_relate` set `valuate_formula`='$p1<**********',`valuate_code`='getAmount' where `id`=459;
update `workflow_node_relate` set `valuate_formula`='$p1>=**********',`valuate_code`='getAmount' where `id`=460;
update `workflow_node_relate` set `valuate_formula`='$p1<*********',`valuate_code`='getAmount' where `id`=463;
update `workflow_node_relate` set `valuate_formula`='$p1>=*********',`valuate_code`='getAmount' where `id`=464;
update `workflow_node_relate` set `valuate_formula`='$p1<**********',`valuate_code`='getAmount' where `id`=465;
update `workflow_node_relate` set `valuate_formula`='$p1>=**********',`valuate_code`='getAmount' where `id`=466;

-- 更新采购订单、采购付款单审批人
update `workflow_node` set `auditor_id`='60712' where `id`=368;
update `workflow_node` set `auditor_id`='52731' where `id`=369;
update `workflow_node` set `auditor_id`='',`name`='指定工号（AP老挝）' where `id`=370;
update `workflow_node` set `auditor_id`='',`name`='指定工号（APS老挝）' where `id`=371;
update `workflow_node` set `auditor_id`='62721' where `id`=372;
update `workflow_node` set `auditor_id`='' where `id`=373;
update `workflow_node` set `auditor_id`='54677' where `id`=374;
update `workflow_node` set `auditor_id`='17152' where `id`=375;
update `workflow_node` set `auditor_id`='76895' where `id`=376;
update `workflow_node` set `auditor_id`='17008' where `id`=377;

-- 采购订单关系
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<6500000', `valuate_code` = 'getAmount' WHERE `id` = 553;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<*********', `valuate_code` = 'getAmount' WHERE `id` = 555;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<**********', `valuate_code` = 'getAmount' WHERE `id` = 557;

-- 更新采购订单、采购付款单审批人（Flash Fullfillment、Flash Laos）
update `workflow_node` set `auditor_id`='60712' where `id`=410;
update `workflow_node` set `auditor_id`='52731' where `id`=411;
update `workflow_node` set `auditor_id`='',`name`='指定工号（AP老挝）' where `id`=412;
update `workflow_node` set `auditor_id`='',`name`='指定工号（APS老挝）' where `id`=413;
update `workflow_node` set `auditor_id`='' where `id`=414;
update `workflow_node` set `auditor_id`='74721' where `id`=415;
update `workflow_node` set `auditor_id`='35805' where `id`=416;
update `workflow_node` set `auditor_id`='17152' where `id`=417;
update `workflow_node` set `auditor_id`='76895' where `id`=418;
update `workflow_node` set `auditor_id`='17008' where `id`=419;
-- 采购订单、采购付款单关系（Flash Fullfillment、Flash Laos）
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1>=6500000', `valuate_code` = 'getAmount' WHERE `id` = 608;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1>=*********', `valuate_code` = 'getAmount' WHERE `id` = 610;
UPDATE `workflow_node_relate` SET `from_node_id`=418,`to_node_id`=420,`valuate_formula` = '$p1<**********', `valuate_code` = 'getAmount',`remark`='FD->CFO' WHERE `id` = 612;
UPDATE `workflow_node_relate` SET `from_node_id` = 417, `to_node_id` = 418 WHERE `id` = 613;

-- 供应商
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(94,'CNS-0001','杭州银弹科技有限公司001','',1,2,1,'91330108MA2B0YA74X','','','杭州市滨江区六和路307号F裙楼413室','夏鹏','***********','<EMAIL>',20,'***************','','S02009','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:48:58',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(95,'CNS-0002','广州九恒条码有限公司','',1,2,1,'914401137435971000','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S00748','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:49:35',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(96,'CNS-0003','江华九恒数码科技有限公司','',1,2,1,'***************','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S01271','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:50:15',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(97,'CNS-0004','江华恒津包装材料有限公司','',1,2,1,'***************','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S00749','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:50:46',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(98,'CNS-0005','常州宏力称重设备制造有限公司','',1,1,3,'320302197406201212','','','江苏省常州市武进高新区龙惠路21号','张孝奉','***********','<EMAIL>',19,'6230520410035755973','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:51:40',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(99,'CNS-0006','桐庐县横村镇欣和箱包厂','',1,2,1,'913301226970878000','','','浙江省杭州市桐庐县利时凌江名庭1411','潘超琦','134 2969 3687','<EMAIL>',30,'***************','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:58:52',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(100,'CNS-0007','启东荻捷工业成套设备有限公司','',1,2,1,'91320681591153462W','','','启东市滨海工业园区明珠路47号','杨挺','***********','<EMAIL>',0,'','','S00747','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29',null,0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(101,'CNS-0008','南京蜂之云信息科技有限公司','',1,2,1,'91320116MA1N4FXH12','','','南京市鼓楼区幕府东路199号A28栋','周国飞','+86 ***********','<EMAIL>',20,'***************','','S00750','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:32:26',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(102,'CNS-0009','山东安固锁业有限公司','',1,2,1,'91371623328513547J','','','山东省无棣县信阳镇闫家花园村','闫新金','***********','<EMAIL>',21,'37050183820800000508','','S00751','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:32:04',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(103,'CNS-0010','天津百丰网络科技有限公司','',1,2,1,'230103198505042411','','','天津市东丽区华明街道北于堡村北区东街4号办公楼','王秀峰','***********','<EMAIL>',22,'441320100100045502','','S00752','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:31:41',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(104,'CNS-0011','滨州鼎岳化纤绳网有限公司','',1,2,1,'91371621MA3M85PU46','','','山东省滨州市惠民县李庄镇工业园','霍光玲','***********','<EMAIL>',0,'','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29',null,0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(105,'CNS-0012','珠海市宝森科技有限公司','',1,2,1,'914404008684021G','','','珠海','Andy Xu','***********','<EMAIL>',21,'44014182900220500239','','S00744','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:30:00',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(106,'CNS-0013','中科微至智能制造科技江苏有限公司','',1,2,1,'91320214MA1MLB3M2A','','','无锡市锡山区大成路299号','柯丽','***********','<EMAIL>',0,'','','S00753','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30',null,0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(107,'CNS-0014','东莞市宝涵国际物流有限公司','',1,2,1,'914419003348121000','','','东莞市常平镇袁山贝村康泰路康宁二街2号','吴炳兴','***********','<EMAIL>',17,'2010025909200400296','','S00754','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:22:43',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(108,'CNS-0015','唐山市志同网络科技有限公司','',1,2,1,'91130283MA0CFJW3XC','','','河北省迁安市黄台湖畔阜安大路2798号集森创业孵化基地8709室','王树全（代理商）','***********','<EMAIL>',18,'************','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:22:14',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(109,'CNS-0016','义乌走召电子商务商行','',1,2,1,'92330782MA2A8PHL1K','','','义乌国际商贸城H区22044','刘坚','***********/***********','<EMAIL>',19,'6228480389397994978','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:21:30',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(110,'CNS-0017','常熟市百联自动机械有限公司','',1,2,1,'913205815691513645','','','江苏省常熟市常福街道锦州路17号','王伟','***********','<EMAIL>',28,'101267114015064168','','S00755','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:21:03',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(111,'CNS-0018','山东民安锁业有限公司','',1,2,1,'372324199412071010','','','山东省滨州市无棣县信阳镇大邵村','王凯','0086 -***********','<EMAIL>',17,'6212261613008952672','','S00746','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:16:11',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(112,'CNS-0019','个人（王珈佳）','',1,1,4,'100-********','','','北京市东城区永定门外大街128号万朋商城118号','王珈佳','***********','********＠qq．com',17,'6212250200004838403','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:13:24',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(113,'CNS-0020','杭州依纯办公用品有限公司','',1,2,1,'91330110MA27W1DT4B','','','杭州市余杭区乔司街道朝阳工业园区红普北路9号6楼','许嫣','***********','<EMAIL>',29,'3302070120100018482','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:58',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(114,'CNS-0021','上海万琛电子商务有限公司','',1,2,1,'91310118599791063M','','','上海市青浦沪清平公路3938号移动智地19号楼','徐艳玲（商务）','***********','<EMAIL>',21,'31050183440009730091','','S00756','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:38',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(115,'CNS-0022','精源粲（厦门）物流设备有限公司','',1,2,1,'91350202MA33610k46','','','厦门市同安区工业集中区思明园38号2楼','刘萍','***********','<EMAIL>',19,'6228480078018853479','','S00757','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:25',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(150,'CNS-0023','Guangzhou Jiuheng  Bar  Code Co. Ltd','',1,2,2,'91440 11374 35971 91','','','7-8# LingXing industry Park, Panyu Area, Guangzhou City, Guangdong Province, China','nicole','***********','<EMAIL>',16,'************','','S00748','WeChat Image_20210422220037.jpg@fle-asset-internal@workOrder/oca-**********-59c70914a3984b5c9f523b7c0cf4a532.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-22 22:01:06',null,0,3,'2021-04-22 22:01:06',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(163,'CNS-0024','Shandong Angu Lock Company Limited','',1,2,2,'91371623328513547J','','','Yanjia Garden, Xinyang Village, Wudi Country,  Binzhou, Shandong, China 251900','Khun Angu','***********','<EMAIL>',21,'37050183820800000508','','S00751','WeChat Photo Editor_20210424181257.jpg@fle-asset-internal@workOrder/oca-**********-d3bfa59e252242ae8aa901a5c75fd14d.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-24 18:15:02',null,0,3,'2021-04-24 18:15:02',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(164,'CNS-0025','Zhuhai Bosam Technology Company Limited','',1,2,2,'*****************','','','8 Gangle Rd., High-Tech Zone, Zhuhai, Guangdong, China 519000','Andy Xu','***********','<EMAIL>',21,'4401 4182 9002 2050 0239','','S01958','WeChat Image_20210425105039.jpg@fle-asset-internal@workOrder/oca-**********-ad472814cb3942769cb28c270ab48c01.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-25 16:36:20',null,0,3,'2021-04-25 16:36:20',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(165,'CNS-0026','BEIJING I-GOODIDEA TRADING CO.,LTD','',1,2,2,'911101023484320740','','','ROOM 189 , 1ST FLOOR , BLOCK B , NO.333 DAYANG ROAD ,  CHAOYANG DISTRICT , BEIJING , P.R CHINA','Chen Xue Ting','************','<EMAIL>',20,'***************','','S01740','WeChat Image_20210425104955.jpg@fle-asset-internal@workOrder/oca-**********-0fb87db2c5d44d498b97c42e6c5caf33.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-25 16:49:25','2021-06-02 15:21:28',0,3,'2021-04-25 16:49:25',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(169,'CNS-0027','Donnelley Financial Solutions Hong Kong Limited','',1,2,1,'0','','','20-21/F, Wheelock House 20 Pedder Street Central, Hong Kong','Mr.Don Zheng','+852 9018 7609','<EMAIL>',27,'0','','S01865','Donnelley Proposal for Flash Group.pdf@fle-asset-internal@workOrder/oca-**********-009d155b70534d4aa08d1491908078dc.pdf',3,66293,'นางสาว ชญาดา สุนทรวุฒินันท์','2021-04-27 10:08:01',null,0,3,'2021-04-27 10:08:01',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(199,'CNS-0029','广东君成印务有限公司','http://www.jcyw.ltd（中）/http://www.gdjcyw.ltd/（英）',1,1,3,'522121197305061414','刘洋','0769-********','东莞市虎门镇怀德社区荔园路27号','刘霁萱','***********','<EMAIL>',17,'2010 1224 1910 0109 969','','','君成营业执照.jpg@fle-asset-internal@workOrder/oca-**********-dcfeb907bb964a90a7576139d1d380c4.jpg,君成印刷许可.jpg@fle-asset-internal@workOrder/oca-**********-f7ae368ed5174c3d8712fc78c5df88ac.jpg',3,65714,'张琦 zhangqi','2021-05-08 13:10:32','2021-05-12 16:58:41',0,3,'2021-05-08 13:10:32',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(210,'CNS-0031','QIDONG YIDATONG AUTOMATION EQUIPMENT CO.,LTD','',1,2,2,'91320681573825042M','','','启东市汇龙镇克明村木场路','王馨','***********','<EMAIL>',33,'1030 1427 4212 7','','S01025','亦大通最新营业执照(1).pdf@fle-asset-internal@workOrder/oca-**********-5723f0e91f24440ab51a183fff7232cb.pdf',3,60712,'白亚男 Bai Yanan','2021-05-14 10:29:14',null,0,3,'2021-05-14 10:29:14',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(211,'CNS-0032','Suzhou Shuangqi (Pairs Kee) Automation Equipment CO., LTD','',1,2,2,'91320506570312188C','','','苏州市吴中区临湖镇许家港路288号','欧阳惠','***********','<EMAIL>',33,'1037 0200 2448 6','','S01270','盖章的营业执照.pdf@fle-asset-internal@workOrder/oca-**********-394b04cae0474d58bd7ce25f2690c051.pdf',3,60712,'白亚男 Bai Yanan','2021-05-14 10:37:45',null,0,3,'2021-05-14 10:37:45',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(215,'CNS-0033','Guangdong Juncheng Printing Co., Ltd','http://www.jcyw.ltd（中）/http://www.gdjcyw.ltd/（英）',1,2,1,'91441900MA4UMCJ64D','刘洋','0769-********','东莞市虎门镇怀德社区荔园路27号','刘霁萱','***********','<EMAIL>',23,'4830  0761  2141  0000  03872','','1000285','营业执照.jpg@fle-asset-internal@workOrder/oca-**********-4946fa0c13d24de693d334da1b72578e.jpg,经营许可.jpg@fle-asset-internal@workOrder/oca-**********-6e16935a419f406ab61eea6bf75802bb.jpg',3,65714,'张琦 zhangqi','2021-05-18 09:28:41',null,0,3,'2021-05-18 09:28:41',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(220,'CNS-0034','Alibaba Cloud (Singapore) Private Limited-ali saas','',1,2,1,'M90371710Y','','','8 Shenton Way, #45-01 AXA Tower, Singapore 068811','Zhong Cheng','***********','<EMAIL>',33,'ACC NO. ***********','','1000260','SIGM1-*************.pdf@fle-asset-internal@workOrder/oca-**********-ad55fe16a3e64e1db16e46bc6fbbc5f8.pdf',3,66293,'นางสาว ชญาดา สุนทรวุฒินันท์','2021-05-19 15:07:52',null,0,3,'2021-05-19 15:07:52',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(226,'CNS-0035','SHANGHAI XIETONG (GROUP) CO.,LTD','',1,2,2,'913100001322253910','','','上海市嘉定区曹安路4671号','邹小姐','***********','<EMAIL>',27,'7313811482600000642','','1000303','营业执照.jpg@fle-asset-internal@workOrder/oca-**********-4f07488cf0a747bfa2effb559bef62bb.jpg',3,60712,'白亚男 Bai Yanan','2021-05-24 19:31:51','2021-05-24 19:41:53',0,3,'2021-05-24 19:31:51',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(229,'CNS-0036','Wayz Intelligent Manufacturing Technology Co. Ltd','',1,2,2,'91320214MA1MLB3M2A','李功燕','0510-********','No.299 Dacheng Road, Xishan District, Wuxi City, China','吴洁','***********','<EMAIL>',23,'322000633141000002944','','S00753','002-中科微至-营业执照-副本-经营范围新增租赁物业管理-********.pdf@fle-asset-internal@workOrder/oca-**********-b2474808629e49c1b53c9734ac902058.pdf',3,60712,'白亚男 Bai Yanan','2021-05-26 20:01:07',null,0,3,'2021-05-26 20:01:07',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(241,'CNS-0038','杭州银弹科技有限公司','',1,2,2,'91330108MA2B0YA74X','覃健祥','0571-********','浙江省杭州市滨江区浦沿街道六和路307号1幢4层413室','孙喜儿','0571-********','<EMAIL>',20,'***************','招商银行杭州分行滨江支行','S02009','银弹营业执照.pdf@fle-asset-internal@workOrder/oca-**********-e403787aa1f2468a80c85fc30546a4ce.pdf',3,60712,'白亚男 Bai Yanan','2021-06-08 15:39:15',null,0,3,'2021-06-08 15:39:15',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(273,'CNS-**********','GENYE TECHNOLOGY COMPANY LIMITED','',1,2,2,'91440113304782005P','','','广州市番禺区南村镇汉溪大道东390号四海城商业广场3栋1301','黄文申','***********','<EMAIL>',17,'3602026409200154691','GENYE TECHNOLOGY COMPANY LIMITED','','',3,60712,'白亚男 Bai Yanan','2021-07-08 10:28:10','2021-07-13 17:32:00',54677,3,'2021-07-13 17:32:00',54677,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(280,'CNS-**********','Hangzhou Weici Technology Co.,Ltd','',1,2,2,'91330108MA2H18RD18','','','Financial Square, Tonglu County, Hangzhou City, Zhejiang Province, China','Elaine Deng','***********','<EMAIL>',19,'*****************','Hangzhou Weici Technology Co., Ltd','','',3,60712,'白亚男 Bai Yanan','2021-07-09 09:23:14','2021-07-13 13:48:01',54677,3,'2021-07-13 13:48:01',54677,1,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(317,'CNS-**********','Shandong Bozheng Ruidi export and Import Co., Ltd','',1,2,2,'91371300MA94CW6X8M','Zengyuan Su','***********','No. 05085, Unit A, Building 2, Yihe Third Road, Linyi Comprehensive Free Trade Zone','Zengyuan Su','***********','<EMAIL>',18,'161001061920068341',' Industrial and Commercial Bank of China Linyi Economic and Technological Development Zone Subbranch','','',2,65714,'张琦 zhangqi','2021-07-22 12:48:48','2021-07-22 17:32:45',54249,3,'2021-07-22 17:32:45',54249,1,0,0);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(323,'CNS-0037','SMART EDGE TECHNOLOGIES PTE. LTD.','',1,2,2,'201226209M','','',"86 Marine Parade Road, #02-20 Cote D'azur Singapore 449301",'Mr.Tao','+*************','<EMAIL>',1,'************','Smart Edge Technologies Pte. Ltd.','1000317','',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-06-02 11:49:37','2021-07-23 09:19:10',54677,3,'2021-07-23 09:19:10',54677,0,2,2);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(357,'CNS-**********','JIANGSU  LOGISTICS EQUIPMENT AUTOMATICCONTROL TECHNOLOGY CO.,LTD.','',1,2,2,'91320412MA1Q0RMP0U','','','No. 198, Datong West Road, Niutang Town, Wujin District, Changzhou City','Kuang Xiangyang','***********','<EMAIL>',18,'************','JIANGSU  LOGISTICS EQUIPMENT AUTOMATICCONTROL TECHNOLOGY CO.,LTD.','1000336','',3,60712,'白亚男 Bai Yanan','2021-07-22 10:21:43','2021-07-27 08:49:26',54677,3,'2021-07-27 08:49:26',54677,0,2,2);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(415,'CNS-**********','Shandong Bozheng Ruidi Import and Export Co., Ltd','',1,2,2,'91371300MA94CW6X8M','','','No. 05085, Unit A, Building 2, Yihe Third Road, Linyi Comprehensive Free Trade Zone','Zengyuan Su','***********','<EMAIL>',17,'161001061920068341','Shandong Bozheng Ruidi Import and Export Co., Ltd ','1000339','',3,65714,'张琦 zhangqi','2021-07-14 18:08:30','2021-07-29 09:24:23',54677,3,'2021-07-29 09:24:23',54677,1,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(422,'CNS-0028','Foshan Shunde Junye Furniture Development Company Limited','n/a',1,2,1,'11111','','***********','Huaxi Industrial Development Zone, Longjiang Town, Shunde Dist., Foshan, Guangdong , 528300 China','Khun Guangyu Feng ( 冯广裕 )','***********','<EMAIL>',33,'*********','Foshan Wuyouchuhai Technology  Company Limited','1000271','',3,19957,'นาย ตาราห์ รุนจำรัส','2021-04-28 10:29:43','2021-07-30 18:20:03',54677,3,'2021-07-30 18:20:03',54677,0,2,2);

-- 供应商模块关联
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(466,'CNS-0028',1,'2021-07-29 03:37:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(456,'CNS-**********',1,'2021-07-28 02:42:05');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(382,'CNS-**********',1,'2021-07-23 10:43:25');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(342,'CNS-0037',2,'2021-07-22 06:48:27');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(341,'CNS-0037',1,'2021-07-22 06:48:27');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(334,'CNS-**********',1,'2021-07-22 05:48:48');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(281,'CNS-**********',1,'2021-07-12 02:10:37');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(274,'CNS-**********',1,'2021-07-08 03:28:10');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(229,'CNS-0036',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(226,'CNS-0035',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(220,'CNS-0034',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(215,'CNS-0033',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(211,'CNS-0032',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(210,'CNS-0031',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(199,'CNS-0029',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(169,'CNS-0027',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(165,'CNS-0026',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(164,'CNS-0025',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(163,'CNS-0024',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(150,'CNS-0023',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(115,'CNS-0022',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(114,'CNS-0021',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(113,'CNS-0020',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(112,'CNS-0019',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(111,'CNS-0018',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(110,'CNS-0017',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(109,'CNS-0016',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(108,'CNS-0015',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(107,'CNS-0014',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(106,'CNS-0013',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(105,'CNS-0012',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(104,'CNS-0011',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(103,'CNS-0010',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(102,'CNS-0009',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(101,'CNS-0008',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(100,'CNS-0007',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(99,'CNS-0006',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(98,'CNS-0005',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(97,'CNS-0004',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(96,'CNS-0003',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(95,'CNS-0002',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(94,'CNS-0001',1,'2021-06-17 15:38:28');

-- 采购商品表
CREATE TABLE `purchase_product_list` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `material_id` varchar(20) DEFAULT NULL COMMENT '产品编码',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `base_uom` varchar(255) DEFAULT NULL,
  `product_cate` varchar(15) DEFAULT NULL COMMENT '产品分类',
  `is_del` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 新增开户行和账户
UPDATE `setting_env` SET `val`='[{\"pay_bank_id\":\"1\",\"pay_bank_name\":\"CIMB\",\"pay_bank_account\":\"**********\"},{\"pay_bank_id\":\"2\",\"pay_bank_name\":\"Public Bank\",\"pay_bank_account\":\"**********\"}]',`content`='付款银行和账号' WHERE `code`='reimbursement_bank';

INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_unit', '[{\"id\":\"5B\",\"description\":\"batch\"},{\"id\":\"E49\",\"description\":\"wday\"},{\"id\":\"EA\",\"description\":\"es\"},{\"id\":\"XBG\",\"description\":\"bag\"},{\"id\":\"XBX\",\"description\":\"box\"},{\"id\":\"XCR\",\"description\":\"crate\"},{\"id\":\"XCS\",\"description\":\"case\"},{\"id\":\"XPX\",\"description\":\"pallet\"},{\"id\":\"XRO\",\"description\":\"roll\"},{\"id\":\"XST\",\"description\":\"sheet\"},{\"id\":\"XSX\",\"description\":\"set\"}]', '产品单位', '2021-06-24 07:31:03', '2021-06-28 08:43:07');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_loan_time', '[{\"id\":\"0001\",\"description\":\"due net payable immediately\"},{\"id\":\"0002\",\"description\":\"7 days due net\"},{\"id\":\"0004\",\"description\":\"30 days from invoice date\"},{\"id\":\"Z001\",\"description\":\"15 days from invoice date\"},{\"id\":\"Z002\",\"description\":\"45 days from invoice date\"},{\"id\":\"Z003\",\"description\":\"60 days from invoice date\"}]', '信贷期限', '2021-06-24 07:19:29', '2021-07-08 06:23:25');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_type', '[{\"id\":\"18\",\"name_key\":\"purchase_stock_type_key\"},{\"id\":\"19\",\"name_key\":\"purchase_service_key\"},{\"id\":\"84\",\"name_key\":\"purchase_cost_key\"},{\"id\":\"1\",\"name_key\":\"purchase_capital_assets_key\"}]', '采购类型', '2021-06-24 09:29:29', '2021-06-24 12:46:19');
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('purchase_order_place', '[{\"id\":\"1\",\"place_name\":\"FEX001\"}]', '采购单收货地点', '2021-06-26 06:31:27', '2021-06-29 06:06:04');

-- 采购申请单
alter table `purchase_apply`
add `is_link_po` tinyint(1) DEFAULT '0' COMMENT '是否被采购订单关联着(老的数据都是关联着，不能直接关联采购付款申请单)',
add `last_update_id` varchar(255) DEFAULT '' COMMENT '最后操作人工号',
add `last_update_name` varchar(255) DEFAULT '' COMMENT '最后操作人姓名（昵称）',
add `last_update_department` varchar(255) DEFAULT '' COMMENT '最后操作人部门',
add `last_update_job_title` varchar(255) DEFAULT '' COMMENT '最后操作人职位',
add `last_update_at` varchar(255) DEFAULT '' COMMENT '最后操作人更新时间';

-- 采购申请单明细表
ALTER TABLE `purchase_apply_product` add column
`vat7_rate` bigint(20) DEFAULT NULL COMMENT 'vat 税率';

-- 采购订单
ALTER TABLE `purchase_order`
add `vendor_id` varchar(20) DEFAULT NULL COMMENT '供应商id',
add `total_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '含税金额总计（含vat 含pnd）',
add `sap_supplier_no` varchar(12) NOT NULL DEFAULT '' COMMENT 'SAP供应商号码',
add `cost_company` varchar(30) DEFAULT NULL COMMENT '费用所属公司id',
add `cost_company_name` varchar(50) DEFAULT NULL COMMENT '费用所属公司',
add `cost_center_code` varchar(30) DEFAULT NULL COMMENT '费用所属 中心id',
add `purchase_type` tinyint(1) DEFAULT NULL COMMENT '采购类型',
add `sap_order_no` varchar(32) DEFAULT NULL COMMENT 'sap采购订单号',
add `loan_time_id` varchar(15) DEFAULT NULL COMMENT '信贷期限id',
add `last_update_id` varchar(255) DEFAULT '' COMMENT '最后操作人工号',
add `last_update_name` varchar(255) DEFAULT '' COMMENT '最后操作人姓名（昵称）',
add `last_update_department` varchar(255) DEFAULT '' COMMENT '最后操作人部门',
add `last_update_job_title` varchar(255) DEFAULT '' COMMENT '最后操作人职位',
add `last_update_at` varchar(255) DEFAULT '' COMMENT '最后操作人更新时间';

-- 采购订单明细
ALTER TABLE `purchase_order_product`
add  `unit_code` varchar(10) DEFAULT NULL COMMENT '产品单位id',
add  `no_tax_price` bigint(20) DEFAULT NULL COMMENT '不含税单价(SAP)=不含税单价*不含税单价单位数量（SAP',
add  `no_tax_num` int(11) DEFAULT NULL COMMENT '不含税单价单位数量（SAP）',
add  `delivery_date` date DEFAULT NULL COMMENT '计划交货日期',
add  `delivery_place` varchar(10) DEFAULT NULL COMMENT '收货地点',
add  `vat7_rate` bigint(20) DEFAULT NULL COMMENT 'vat 税率';

-- 采购付款单
ALTER TABLE `purchase_payment`
add  `is_link_pa` tinyint(2) DEFAULT '0' COMMENT '是否采购框架合同(直接关联pa)',
add  `pa_id` int(11) DEFAULT '0' COMMENT '采购申请单id',
add  `pano` varchar(100) DEFAULT '' COMMENT '采购申请单编号',
add  `contract_no` varchar(255) DEFAULT '' COMMENT '合同编号';

-- 采购付款单票据
ALTER TABLE `purchase_payment_receipt`
add  `pap_id` int(11) DEFAULT '0' COMMENT '采购申请单产品id',
add  `order_total` int(10) DEFAULT '0' COMMENT '关联采购订单产品数量',
add  `ticket_amount_not_tax` bigint(20) unsigned DEFAULT '0' COMMENT '发票金额（不含税）',
add  `vat7_rate` bigint(20) DEFAULT NULL COMMENT 'vat 税率';