-- 更新vat税率
UPDATE `setting_env` SET `val`='5,10' where `code`='ordinary_payment_vat7_rate';

-- 预算开关
UPDATE `setting_env` SET `val`='0' where `code`='budget_status';

-- WHT类别和对应汇率
UPDATE `setting_env` SET `val`='{\"1\":{\"name\":\"\/\",\"tax\":[0]}}' where `code`='payment_wht_tax';

-- 支付人工号
UPDATE `setting_env` SET `val`='76260,65382' where `code`='ordinary_payment_pay_staff_id';

UPDATE `workflow_node` SET `name` = 'AP（马来）', `auditor_id` = NULL WHERE `id` = 427;
UPDATE `workflow_node` SET `name` = 'AP Supervisor（马来）', `auditor_id` = NULL WHERE `id` = 428;
UPDATE `workflow_node` SET `auditor_id` = '62721' WHERE `id` = 429;
UPDATE`workflow_node` SET  `auditor_id` = '' WHERE `id` = 430;
UPDATE `workflow_node` SET `auditor_id` = '76895' WHERE `id` = 433;

UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 607;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 608;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 609;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 610;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 611;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 612;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 615;
UPDATE `workflow_node` SET `name` = 'AP（马来）', `auditor_id` = NULL WHERE `id` = 617;
UPDATE `workflow_node` SET `name` = 'AP Supervisor（马来）', `auditor_id` = NULL WHERE `id` = 618;
UPDATE `workflow_node` SET `auditor_id` = '62721' WHERE `id` = 619;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 620;
UPDATE `workflow_node` SET `auditor_id` = '76895' WHERE `id` = 623;

UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 629;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 630;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 634;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 636;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 637;
UPDATE `workflow_node` SET `auditor_id` = '62721' WHERE `id` = 638;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 639;
UPDATE `workflow_node` SET `auditor_id` = '76895' WHERE `id` = 642;

UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 648;
UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 649;
UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 654;
UPDATE `workflow_node` SET `name` = 'AP（马来）', `auditor_id` = NULL WHERE `id` = 656;
UPDATE `workflow_node` SET `name` = 'AP Supervisor（马来）', `auditor_id` = NULL WHERE `id` = 657;
UPDATE `workflow_node` SET `auditor_id` = '62721' WHERE `id` = 658;
UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 659;
UPDATE `workflow_node` SET `auditor_id` = '76895' WHERE `id` = 662;

UPDATE `workflow_node` SET `name` = 'AP（马来）', `auditor_id` = NULL WHERE `id` = 681;
UPDATE `workflow_node` SET `name` = 'AP Supervisor（马来）', `auditor_id` = NULL WHERE `id` = 682;
UPDATE `workflow_node` SET `auditor_id` = NULL WHERE `id` = 683;
UPDATE `workflow_node` SET `auditor_id` = '74721' WHERE `id` = 684;
UPDATE `workflow_node` SET `auditor_id` = '17152' WHERE `id` = 686;
UPDATE `workflow_node` SET `auditor_id` = '76895' WHERE `id` = 687;

UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 1500', `remark` = '金额 < 1500，二级部门负责人->AP(马来)' WHERE `id` = 619;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 26000', `remark` = '金额 < 26000, 一级部门负责人->AP(马来)' WHERE `id` = 621;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 2600000', `remark` = '金额 < 2600000, 所属公司负责人->AP(马来)' WHERE `id` = 623;
UPDATE `workflow_node_relate` SET `remark` = 'COO/CPO -> AP(马来)' WHERE `id` = 625;
UPDATE `workflow_node_relate` SET `remark` = 'AP(马来)->AP Supervisor(马来)' WHERE `id` = 626;
UPDATE `workflow_node_relate` SET `remark` = 'AP Supervisor(马来)->AP Supervisor(北京)' WHERE `id` = 627;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 1500', `remark` = '金额 < 1500, AP Supervisor(北京) -> 结束' WHERE `id` = 628;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 26000', `remark` = '金额 < 26000, Finance Senior Manager->结束' WHERE `id` = 631;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 2600000', `remark` = '金额 < 2600000, Finance Director -> 结束' WHERE `id` = 633;

UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 2 && $p2 < 150', `remark` = '外协 且 金额 < 150: 网点主管->Supervisor_2' WHERE `id` = 1067;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 150', `remark` = '金额 < 150:网点主管->Supervisor' WHERE `id` = 1068;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 2 && $p2 < 400', `remark` = '外协 且 金额 < 400:Manager->Supervisor_2' WHERE `id` = 1070;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 400', `remark` = '金额 < 400:Manager->Supervisor' WHERE `id` = 1071;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '福利费/团建费 且 金额 < 400:Supervisor->HRBP Director' WHERE `id` = 1075;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 0 && $p2 < 400', `remark` = '其他 且 金额 < 400:Supervisor->AP（马来）' WHERE `id` = 1076;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 400', `remark` = '外协: 金额 < 400:Supervisor_2->AP（马来）' WHERE `id` = 1078;
UPDATE `workflow_node_relate` SET `valuate_formula` = 'in_array($p1, [2,0]) && $p2 < 700', `remark` = '外协/其他 且 金额 < 700:Manager->AP（马来）' WHERE `id` = 1080;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 700', `remark` = '福利费/团建费 且 金额 < 700:Manager->HRBP Director' WHERE `id` = 1081;
UPDATE `workflow_node_relate` SET `valuate_formula` = 'in_array($p1, [2,0]) && $p2 < 4000', `remark` = '外协/其他 且 金额 < 4000:/->AP（马来）' WHERE `id` = 1083;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 4000', `remark` = '福利费/团建费 且 金额 < 4000:/->HRBP Director' WHERE `id` = 1084;
UPDATE `workflow_node_relate` SET `valuate_formula` = 'in_array($p1, [2, 0]) && $p2 < 2600000', `remark` = '外协/其他 且 金额 < 2600000:所属公司负责人->AP（马来）' WHERE `id` = 1086;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '福利费/团建费 且 金额 < 2600000:所属公司负责人->HRBP Director' WHERE `id` = 1087;
UPDATE `workflow_node_relate` SET `valuate_formula` = 'in_array($p1, [2, 0])', `remark` = '外协/其他：COO->AP（马来）' WHERE `id` = 1089;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '福利费/团建费 且 金额 < 2600000:HRBP Director->AP（马来）' WHERE `id` = 1091;
UPDATE `workflow_node_relate` SET `remark` = 'CPO->AP（马来）' WHERE `id` = 1093;
UPDATE `workflow_node_relate` SET `remark` = 'AP（马来）->AP Supervisor（马来）' WHERE `id` = 1094;
UPDATE `workflow_node_relate` SET `remark` = 'AP Supervisor（马来）->AP Supervisor（北京）' WHERE `id` = 1095;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 150', `remark` = '金额 < 150:AP Supervisor（北京）->结束' WHERE `id` = 1096;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 400', `remark` = '金额 < 400:Finance Manager->结束' WHERE `id` = 1098;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 700', `remark` = '金额 < 700:Finance Senior Manager->结束' WHERE `id` = 1100;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 4000', `remark` = '金额 < 4000:Finance Director->结束' WHERE `id` = 1102;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 >= 4000' WHERE `id` = 1103;
UPDATE `workflow_node_relate` SET `valuate_formula` = '(in_array($p1, [1,2]) && $p2 < 2600000) || ($p1 == 0 && $p2 <2500000)', `remark` = '外协和福利在金额 < 2600000 或者其他在金额<2500000:CFO->结束' WHERE `id` = 1104;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1 < 700', `remark` = '外协：Manager->AP（马来）' WHERE `id` = 1107;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 >= 700', `remark` = '外协：Manager->外协：ATD' WHERE `id` = 1108;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 4000', `remark` = '外协：ATD->AP（马来）' WHERE `id` = 1109;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 >= 4000', `remark` = '外协：ATD->所属公司负责人' WHERE `id` = 1110;


UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150: 网点主管->Supervisor' WHERE `id` = 1112;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400: 网点主管->Supervisor' WHERE `id` = 1113;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150: Supervisor->AP（马来）' WHERE `id` = 1116;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400:Supervisor->HRBP Director' WHERE `id` = 1117;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 400', `remark` = '非员工福利费/团建费 且 金额 < 400:Manager->AP（马来）' WHERE `id` = 1119;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 1500', `remark` = '员工福利费/团建费 且 金额 < 1500:Manager->HRBP Director' WHERE `id` = 1120;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 1500', `remark` = '非员工福利费/团建费 且 金额 < 1500:一级部门负责人->AP（马来）' WHERE `id` = 1122;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 6500', `remark` = '员工福利费/团建费 且 金额 < 6500:一级部门负责人->HRBP Director' WHERE `id` = 1123;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 2600000', `remark` = '非员工福利费/团建费 且 金额 < 2600000:所属公司负责人->AP（马来）' WHERE `id` = 1125;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '员工福利费/团建费 且 金额 < 2600000:所属公司负责人->HRBP Director' WHERE `id` = 1126;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1', `remark` = '非员工福利费/团建费：COO->AP（马来）' WHERE `id` = 1128;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '员工福利费/团建费 且 金额 < 2600000:HRBP Director->AP（马来）' WHERE `id` = 1130;
UPDATE `workflow_node_relate` SET `remark` = 'CPO->AP（马来）' WHERE `id` = 1132;
UPDATE `workflow_node_relate` SET `remark` = 'AP（马来）->AP Supervisor（马来）' WHERE `id` = 1133;
UPDATE `workflow_node_relate` SET `remark` = 'AP Supervisor（马来）->AP Supervisor（北京）' WHERE `id` = 1134;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150: AP Supervisor（北京）->结束' WHERE `id` = 1135;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 400', `remark` = '非员工福利费/团建费 且 金额 < 400:Finance Manager->结束' WHERE `id` = 1137;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400:Finance Manager->结束' WHERE `id` = 1138;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 1500', `remark` = '非员工福利费/团建费 且 金额 < 1500:Finance Senior Manager->结束' WHERE `id` = 1140;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 1500', `remark` = '员工福利费/团建费 且 金额 < 1500: Finance Senior Manager->结束' WHERE `id` = 1141;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 2600000', `remark` = '非员工福利费/团建费 且 金额 < 2600000:CFO->结束' WHERE `id` = 1143;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 6500', `remark` = '员工福利费/团建费 且 金额 < 6500:Finance Director->结束' WHERE `id` = 1144;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 2600000', `valuate_code` = 'getTotalAmount', `remark` = '金额 < 2600000:CFO->结束' WHERE `id` = 1146;


UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150: 直线上级->Supervisor' WHERE `id` = 1150;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400:直线上级->Supervisor' WHERE `id` = 1151;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400:Supervisor->二级部门负责人' WHERE `id` = 1154;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150: Supervisor->二级部门负责人' WHERE `id` = 1155;
UPDATE `workflow_node_relate` SET  `from_node_id` = 648, `to_node_id` = 649, `valuate_formula` = '', `valuate_code` = '', `remark` = 'Supervisor->Manager' WHERE `id` = 1156;
UPDATE `workflow_node_relate` SET `from_node_id` = 651, `to_node_id` = 654, `valuate_formula` = '$p1 == 1 && $p2 < 6500', `valuate_code` = 'getOrdinaryPaymentType,getTotalAmount', `remark` = '员工福利费/团建费 且 金额 < 6500:一级部门负责人->HRBP Director' WHERE `id` = 1159;
UPDATE `workflow_node_relate` SET `from_node_id` = 651, `to_node_id` = 656, `valuate_formula` = '$p1 != 1 && $p2 < 1500', `valuate_code` = 'getOrdinaryPaymentType,getTotalAmount', `remark` = '非员工福利费/团建费 且 金额 < 1500:一级部门负责人->AP（马来）' WHERE `id` = 1160;
UPDATE `workflow_node_relate` SET `from_node_id` = 651, `to_node_id` = 652, `valuate_formula` = '', `valuate_code` = '', `remark` = '一级部门负责人->所属公司负责人' WHERE `id` = 1161;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 2600000', `remark` = '非员工福利费/团建费 且 金额 < 2600000:所属公司负责人->AP（马来）' WHERE `id` = 1162;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '员工福利费/团建费 且 金额 < 2600000:所属公司负责人->HRBP Director' WHERE `id` = 1163;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1', `remark` = '非员工福利费/团建费:COO->AP（马来）' WHERE `id` = 1165;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 2600000', `remark` = '员工福利费/团建费 且 金额 < 2600000:HRBP Director->AP（马来）' WHERE `id` = 1167;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 >= 2600000', `remark` = '员工福利/团建费 且 金额 >= 2600000:HRBP Director->CPO' WHERE `id` = 1168;
UPDATE `workflow_node_relate` SET `remark` = 'CPO->AP（马来）' WHERE `id` = 1169;
UPDATE `workflow_node_relate` SET `remark` = 'AP（马来）->AP Supervisor（马来）' WHERE `id` = 1170;
UPDATE `workflow_node_relate` SET `remark` = 'AP Supervisor（马来）->AP Supervisor（北京）' WHERE `id` = 1171;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 150', `remark` = '非员工福利费/团建费 且 金额 < 150:AP Supervisor（北京）->结束' WHERE `id` = 1172;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 400', `remark` = '非员工福利费/团建费 且 金额 < 400:Finance Manager->结束' WHERE `id` = 1174;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 400', `remark` = '员工福利费/团建费 且 金额 < 400:Finance Manager->结束' WHERE `id` = 1175;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 != 1 && $p2 < 1500', `remark` = '非员工福利费/团建费 且 金额 < 1500:Finance Senior Manager->结束' WHERE `id` = 1177;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 1500', `remark` = '员工福利费/团建费 且 金额 < 1500:Finance Senior Manager->结束' WHERE `id` = 1178;
UPDATE `workflow_node_relate` SET `from_node_id` = 661, `to_node_id` = 662, `valuate_formula` = '$p1 != 1 && $p2 >= 1500', `valuate_code` = 'getOrdinaryPaymentType,getTotalAmount', `remark` = '非员工福利费/团建费 且 金额 >=1500:Finance Director->CFO' WHERE `id` = 1180;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 == 1 && $p2 < 6500', `remark` = '员工福利费/团建费 且 金额 < 6500:Finance Director->结束' WHERE `id` = 1181;
UPDATE `workflow_node_relate` SET `from_node_id` = 662, `to_node_id` = 664, `valuate_formula` = '$p1 < 2600000', `valuate_code` = 'getTotalAmount', `remark` = '金额 < 2600000:CFO->结束' WHERE `id` = 1183;


UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 3000', `remark` = '金额 < 3000，二级部门负责人->AP(马来)' WHERE `id` = 963;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 6500', `remark` = '金额 < 6500, 一级部门负责人->AP(马来)' WHERE `id` = 965;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 2600000', `remark` = '金额 < 2600000, 所属公司负责人->AP(马来)' WHERE `id` = 967;
UPDATE `workflow_node_relate` SET `remark` = 'COO/CPO -> AP(马来)' WHERE `id` = 969;
UPDATE `workflow_node_relate` SET `remark` = 'AP(泰国)->AP Supervisor(马来)' WHERE `id` = 970;
UPDATE `workflow_node_relate` SET `remark` = 'AP Supervisor(马来)->AP Supervisor(北京)' WHERE `id` = 971;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 3000', `remark` = '金额 < 3000, AP Supervisor(北京) -> 结束' WHERE `id` = 972;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 6500', `remark` = '金额 < 6500, Finance Senior Manager->结束' WHERE `id` = 975;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1 < 2600000', `remark` = '金额 < 2600000, Finance Director -> 结束' WHERE `id` = 977;