-- LA OA数据库执行
-- 审批流
INSERT INTO `workflow`(`id`, `name`, `biz_type`, `description`, `created_at`) VALUES (67, '供应商信息审批 - v202106', 59, '供应商信息审批 - v202106', '2021-07-29 21:34:57');

-- 审批流节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (978, 67, '员工提交', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (979, 67, '采购', 3, 0, NULL, NULL, NULL, NULL, 1, '60712,52731', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (980, 67, '老挝财务', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (981, 67, '北京财务', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (982, 67, 'Finance Senior Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '76772', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (983, 67, '结束', 6, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', NULL);

  -- 审核节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 978, 979, NULL, NULL, '提交->采购节点', 10, '2021-06-03 14:04:46', '2021-06-03 14:04:46');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 979, 980, NULL, NULL, '采购节点->老挝财务', 10, '2021-07-29 11:19:39', '2021-06-03 14:05:07');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 980, 981, NULL, NULL, '老挝财务->北京财务', 10, '2021-07-29 11:19:40', '2021-06-03 14:05:22');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 981, 982, NULL, NULL, '北京财务->Finance Senior Manager', 10, '2021-06-03 14:05:42', '2021-06-03 14:05:42');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (67, 982, 983, NULL, NULL, 'Finance Senior Manager->结束', 10, '2021-06-03 14:05:58', '2021-06-03 14:05:58');

-- 添加银行新数据
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (80, 'bank.80', 'Philippine Savings Bank', 0, '2021-07-30 05:29:18');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (81, 'bank.81', 'BDO Network Bank Makati Avenue', 0, '2021-07-30 05:29:46');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (82, 'bank.82', 'United Coconut Planters Bank', 0, '2021-07-30 05:30:09');
INSERT INTO `supplier_bank`(`id`, `bank_code`, `bank_name`, `is_del`, `create_time`) VALUES (83, 'bank.84', 'Commerzbank AG', 0, '2021-07-30 05:30:30');


-- 同步预算列表
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(1,1,'001',1,'ค่าเดินทางปฏิบัติงานนอกสถานที่','Travel Expenses','差旅费',0,0,68212,'2020-12-31 17:07:23','2021-07-23 01:42:37',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(2,1,'002',1,'ยานพาหนะบริษัท-ค่าน้ำมัน','Own Fleet-Fuel','自有车队-油费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(3,1,'003',1,'ยานพาหนะบริษัท-ค่าจอดรถ','Own Fleet-Parking Fee','自有车队-停车费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(4,1,'004',1,'ยานพาหนะบริษัท-ค่าทางด่วน','Own Fleet-Toll Fee','自有车队-过路费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(5,1,'005',1,'ภาษีและค่าธรรมเนียม','Tax and Associate Charge','税金及附加',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(6,1,'006',1,'ค่าแรง-พนักงานรายวันoutsource','Labour Services-Part-time','劳务成本-外包',0,0,0,'2020-12-31 17:07:23','2021-05-19 14:31:56',0,1);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(7,1,'007',1,'ค่าแรง-พนักงานขนส่งoutsource','External Courier','劳务成本-外协',0,0,59948,'2020-12-31 17:07:23','2021-05-19 14:31:56',0,1);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(8,1,'008',1,'ค่าสวัสดิการพนักงาน','Welfare','员工福利费',0,0,68212,'2020-12-31 17:07:23','2021-06-04 08:51:48',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(9,1,'009',1,'ค่าจัดกิจกรรมของแผนก','Employee Morale','员工团建费',0,0,0,'2020-12-31 17:07:23','2021-01-12 12:47:16',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(10,1,'010',1,'ค่าสรรหาบุคลากร','Recruitment Fee','招聘费',0,0,68212,'2020-12-31 17:07:23','2021-04-29 05:02:29',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(11,1,'011',1,'ค่าประชุมประจำปีของบริษัท','Company Annual Meeting Fee','公司年会费',0,0,0,'2020-12-31 17:07:23','2021-01-12 12:48:08',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(12,1,'012',1,'ค่ากิจกรรมของบริษัท','Company Activity Fee','公司活动费',0,0,59948,'2020-12-31 17:07:23','2021-05-05 03:29:21',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(13,1,'013',1,'ค่าประชุม','Conference Fee','会议费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(14,1,'014',1,'ค่าเดินทาง','Expenses-Transportation','交通费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(15,1,'015',1,'ค่าโฆษณา-ของขวัญ','Marketing and Advertising-Gifts','广告宣传费-礼品',0,0,0,'2020-12-31 17:07:23','2021-04-08 06:41:29',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(16,1,'016',1,'ค่าน้ำ-ไฟ','Water&Electricity','水电费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(17,1,'017',1,'ค่าใช้จ่ายสำนักงาน','Office Expenses','办公费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(18,1,'018',1,'ค่ารับรองทางธุรกิจ','Business Entertainment','业务招待费',0,0,59948,'2020-12-31 17:07:23','2021-06-02 07:24:06',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(19,1,'019',1,'ค่าขนส่ง-น้ำมัน','Freight-Fuel','油费',0,0,0,'2020-12-31 17:07:23','2021-01-20 10:43:25',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(20,1,'020',1,'ค่าขนส่ง-อื่นๆ','Freight-Others','运费-其他',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(21,1,'021',1,'ค่าเช่ารถ','Rent-Vehicle','车辆租赁费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(22,1,'022',1,'อุปกรณ์รักษาความปลอดภัย','Labor Protection Supplies','劳保用品',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(23,1,'023',1,'ค่าโฆษณา-ค่าพรีเซ็นเตอร์บริษัท','Star Endorsement','广告宣传费-明星代言费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(24,1,'024',1,'ค่าโฆษณา-ค่าโฆษณา','Advertising','广告宣传费-广告费',0,0,68212,'2020-12-31 17:07:23','2021-06-30 04:40:16',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(25,1,'025',1,'ค่าโฆษณา-ค่าสถานที่','Marketing and Advertising-Venue','广告宣传费-会场费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(26,1,'026',1,'ค่าโฆษณา-ค่าพิมพ์เอกสาร','Marketing and Advertising-Printing','广告宣传费-印刷资料费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(27,1,'027',1,'ค่าโฆษณา-อื่นๆ','Marketing and Advertising-Others','广告宣传费-其他',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(28,1,'028',1,'ยานพาหนะบริษัท-ค่าประกันภัยยานพาหนะ','Own Fleet-Auto Insurance Premium','自有车队-车险',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(29,1,'029',1,'ยานพาหนะบริษัท-ค่าซ่อม/บำรุงยานพาหนะ','Own Fleet-Maintenance and Repairs','自有车队-车辆维修/保养',0,0,59948,'2020-12-31 17:07:23','2021-04-25 07:21:43',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(30,1,'030',1,'ค่าโทรศัพท์','Telephone  expense','电话费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(31,1,'032',1,'วัสดุสิ้นเปลืองมูลค่าต่ำ','Low value Consumables','低值易耗品',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(32,1,'034',1,'ค่าเช่าอุปกรณ์','Rent-Equipment','机器设备租赁费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(33,1,'036',1,'ค่าบริการSMS','Short Message','短信费',0,0,0,'2020-12-31 17:07:23','2021-01-12 12:49:06',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(34,1,'037',1,'ค่าแม่พิมพ์','Modelling','模具费',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(35,1,'038',1,'ทรัพย์สินไม่มีตัวตน','Intangible assets','无形资产',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(36,1,'039',1,'ปรับปรุงใหม่','Renovation','装修改造',0,0,0,'2020-12-31 17:07:23','2021-03-26 06:01:18',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(37,1,'040',1,'ค่าอุปกรณ์ไฟฟ้า','Electronic Equipment','电子设备',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(38,1,'041',1,'เฟอร์นิเจอร์สำนักงาน','Office Furniture','办公家具',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(39,1,'042',1,'ค่าใช้จ่ายซอฟแวร','Soft license','软件使用费',0,0,0,'2020-12-31 17:07:23','2021-04-07 06:18:24',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(40,1,'043',1,'อุปกรณ์สำนักงาน','Office Equipment','办公设备',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(41,1,'044',1,'อุปกรณ์เครื่องจักร','Machinery','机器设备',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(42,1,'045',1,'มิเตอร์และเครื่องมือ','Instruments and Tools','仪器和工具',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(43,1,'046',1,'ค่าแผ่นป้ายโฆษณา','Billboard','广告牌',0,0,0,'2020-12-31 17:07:23','2020-12-31 17:07:23',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(44,1,'047',1,'อุปกรณ์การขนส่ง','Transportation Equipment','运输设备',0,0,68212,'2020-12-31 17:07:23','2021-04-27 04:33:10',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(45,1,'048',1,'เครื่องใช้สำนักงาน','Office Supplies','办公用品',0,0,0,'2020-12-31 17:07:23','2021-01-15 05:57:07',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(46,1,'049',1,'ค่าธรรมเนียมธนาคาร','Bank Charges','银行手续费',0,0,0,'2021-01-04 10:17:19','2021-01-04 10:17:19',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(47,1,'050',1,'อื่นๆ','Other','其他',0,0,68212,'2021-01-04 10:17:23','2021-05-10 06:09:00',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(49,1,'052',1,'ค่าเช่าคลังสินค้า','Warehouse rent','仓库租金',0,0,0,'2021-01-05 14:44:48','2021-01-05 14:44:48',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(50,1,'053',1,'ค่ารักษาความปลอดภัย','Security protection fee','安全保护费',0,0,0,'2021-01-05 14:44:56','2021-01-05 14:44:56',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(51,1,'054',1,'คืนกำไร','Rebate','返佣',0,0,0,'2021-01-07 02:08:48','2021-04-13 13:51:07',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(52,1,'055',1,'ค่าธรรมเนียมจัดการวีซ่า','Visa management fee','签证管理费',0,0,59948,'2021-01-08 03:35:42','2021-05-18 02:02:00',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(53,1,'056',1,'พาณิชย์ประกันภัยของพนักงาน','Employee Commercial Insurance','员工商业保险',0,0,0,'2021-01-08 03:35:42','2021-01-08 03:35:42',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(54,1,'057',1,'ค่าเช่า','Rent','房租',0,0,59948,'2021-01-08 03:35:42','2021-04-22 10:01:16',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(55,1,'058',1,'ค่าประชาสัมพันธ์ (PR)','Public relations expenses','公关费',0,0,68212,'2021-01-08 03:35:42','2021-05-13 09:45:32',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(56,1,'059',1,'ค่าอบรม','Training fee','培训费',0,0,0,'2021-01-08 03:35:42','2021-01-08 03:35:42',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(57,1,'060',1,'ค่าทางด่วน','Tolls','过路费',0,0,0,'2021-01-08 03:35:42','2021-03-24 05:59:52',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(58,1,'061',1,'Ownfleet -ที่พัก','Own Fleet-Lodging','自有车队-住宿费',0,0,68212,'2021-01-08 06:28:23','2021-05-28 08:56:04',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(59,1,'068',1,'ยานพาหนะบริษัท-ค่าล้างรถ','Own fleet-car wash fee','自有车队-洗车费',0,0,0,'2021-01-12 12:45:47','2021-01-12 13:47:54',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(60,1,'062',1,'วัสดุบรรจุภัณฑ์','Packaging supplies','包装耗材',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(61,1,'063',1,'เสื้อผ้า','Clothing','服装',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(62,1,'064',1,'บริการเฉพาะทาง-ค่าธรรมเนียมตรวจสอบบัญชี','Professional Service-Auditing','专业服务-审计费',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(63,1,'065',1,'ค่าขนส่งtransportation','Freight-Line Haul','第三方运费-支干线',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(64,1,'066',1,'ค่าจัดเก็บ','Warehousing Expense','仓储费',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(65,1,'067',1,'ค่าจดทะเบียนสาขา','Branches Registration fee','网点注册费',0,0,0,'2021-01-12 12:45:47','2021-01-12 12:45:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(66,1,'106',1,'ค่าธรรมเนียม','Handling fee','手续费',0,0,0,'2021-01-14 12:18:51','2021-05-17 11:32:08',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(67,1,'069',1,'บริการเฉพาะทาง-ค่าที่ปรึกษา','Professional Service-Consulting Fees','专业服务-咨询费',0,0,68212,'2021-01-21 07:58:43','2021-06-29 05:40:46',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(68,1,'070',1,'ยานพาหนะบริษัท-ค่าซ่อม/บำรุงยานพาหนะ','Own Fleet-Maintenance and Repairs','自有车队-车辆维修/保养',0,0,59948,'2021-01-21 07:58:43','2021-04-25 06:41:56',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(69,1,'071',1,'ยานพาหนะบริษัท-ประกันภัยสินค้า','Own Fleet-Cargo insurance ','自有车队-货物险',0,0,0,'2021-01-21 07:58:43','2021-01-21 07:58:43',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(70,1,'072',1,'ยานพาหนะบริษัท-ค่าGPS/safetyequip','Own Fleet-GPS/CCTV/Safety equip，etc','自有车队-GPS/CCTV/Safety equip等',0,0,68212,'2021-01-21 07:58:43','2021-07-21 06:17:48',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(71,1,'073',1,'ยานพาหนะบริษัท-ค่าภาษียานพาหนะรายป','Own Fleet-Annual vehicle tax','自有车队-车辆年税',0,0,0,'2021-01-21 07:58:43','2021-01-21 07:58:43',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(72,1,'074',1,'ค่าน้ำมันพนักงานคูเรียร์','Courier gas','快递员油费',0,0,68212,'2021-03-03 14:02:34','2021-06-18 09:58:53',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(76,1,'078',1,'ค่าประกัน','insurance','保险费',0,0,0,'2021-03-03 14:02:34','2021-04-13 13:54:53',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(77,1,'079',1,'ค่าเช่าอุปกรณ์ไฟฟ้า','Machinery and equipment rental','机器设备租赁',0,0,0,'2021-03-03 14:02:34','2021-04-13 13:54:53',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(78,1,'080',1,'ซื้อรถ LH','lh buy a car','lh买车',0,0,0,'2021-03-03 14:02:34','2021-04-13 13:54:54',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(79,1,'081',1,'ค่าเช่ารถ','Car rental costs','租车费用',0,0,0,'2021-03-03 14:02:34','2021-04-13 13:54:54',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(80,1,'082',1,'ค่าบริการเฉพาะทาง-ค่าที่ปรึกษาด้านการเงิน','Professional Service-Financing Consultant Fee','专业服务-融资顾问费',0,0,0,'2021-03-03 14:02:34','2021-03-03 14:02:34',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(81,1,'083',1,'ค่าบริการเฉพาะทาง-ค่าทนายความ',"Professional Service-Attorney's fees",'专业服务-律师费',0,0,70339,'2021-03-03 14:02:34','2021-07-30 03:03:35',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(82,1,'084',1,'ค่าสมาชิก','membership fee','会员费',0,0,0,'2021-03-24 14:43:47','2021-03-24 14:43:47',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(83,1,'085',1,'ค่าธรรมเนียมสำหรับบริการทางเทคนิค','Technical Service Fee','技术服务费',0,0,0,'2021-04-07 11:55:44','2021-04-07 11:55:44',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(84,1,'086',1,'การประกันภัยความรับผิดต่อสาธารณะ','Public liability insurance','公众责任险',0,0,0,'2021-04-09 10:28:03','2021-04-09 10:28:03',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(85,1,'087',1,'ค่าติดตั้ง','Installation fee','安装费',0,0,68212,'2021-04-12 03:08:45','2021-07-15 10:20:10',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(86,1,'105',1,'ยุติดำเนินคดีฟ้องร้องบริษัท/ค่าสินไหมทดแทน','Settlement Fund','公司诉讼和解/赔偿金',0,0,0,'2021-04-12 08:12:57','2021-05-17 10:45:01',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(87,1,'088',1,'ค่าประชาสัมพันธ์การตลาด','Marketing fee','市场推广费',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(88,1,'089',1,'ค่าสมัครสมาชิกโปรแกรม','Software registration fee','软件注册费',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(89,1,'090',1,'จดทะเบียนเครื่องหมายการค้า','Trademark Registration','商标申请',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(90,1,'091',1,'ยื่นขอรับสิทธิบัตร','Patent Application','专利申请',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(91,1,'092',1,'เงินจอง','Down Payment','订金',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(92,1,'093',1,'ค่าแรงงาน- part time','Labor cost-part-time','劳务成本-兼职',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(93,1,'094',1,'ค่าใช้พื้นที่','Site fee','场地使用费',0,0,0,'2021-04-13 13:44:30','2021-04-13 13:44:30',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(94,1,'095',1,'การประกันภัยความรับผิดต่อสาธารณชน','Public liability insurance','公众责任险',0,0,0,'2021-04-13 13:44:30','2021-04-25 07:39:59',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(95,1,'096',1,'ประกันทรัพย์สิน','Property insurance','财产保险',0,0,68212,'2021-04-13 13:44:40','2021-06-07 03:02:41',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(97,1,'098',1,'เงินมัดจำ','Deposit','押金',0,0,0,'2021-04-13 13:44:40','2021-04-13 13:44:40',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(98,1,'099',1,'ประกันภัย-รถเหลือง','Small Yellow Car-Vehicle Insurance','小黄车-车辆保险',0,0,0,'2021-04-13 13:44:40','2021-04-13 13:44:40',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(99,1,'100',1,'ประกันภัยรถขนส่งสินค้า -รถเหลือง','Small Yellow Car-Cargo insurance','小黄车-货物保险',0,0,0,'2021-04-13 13:44:40','2021-04-13 13:44:40',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(100,1,'101',1,'ค่าจดทะเบียนรถ - รถเหลือง','Small Yellow Car-Yellow card registration fee','小黄车-黄牌注册费',0,0,0,'2021-04-13 13:44:40','2021-04-13 13:44:40',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(101,1,'102',1,'ค่าบริการเฉพาะทาง -รายงานการวิเคราะห','Professional Service-Analysis Report','专业服务-分析报告费',0,59948,68212,'2021-04-21 06:18:46','2021-07-07 07:34:24',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(102,1,'103',1,'ยานพาหนะบริษัท-ค่าธรรมเนียมการเปลี่ยนกรรมสิทธิ์','Own Fleet-Ownership Change Service Fee','自有车队-转让服务费',0,59948,62940,'2021-04-25 01:59:38','2021-04-27 06:37:17',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(103,1,'104',1,'ค่าเช่าพาเลทไม','Pallet Rental','托盘租赁费',0,59948,70339,'2021-05-06 06:39:35','2021-07-07 09:29:34',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(104,1,'107',1,'รถเหลืองคันเล็ก -ค่าบูรณะยานพาหนะ','Small yellow car-Vehical recovery fee','小黄车-车辆还原费',0,68212,68212,'2021-05-19 03:21:46','2021-07-22 11:22:01',1,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(105,1,'108',1,'ยานพาหนะบริษัท-เงินมัดจำค่าเช่า','Own Fleet-Rental deposit','自有车队-房租押金',0,68212,0,'2021-06-02 13:14:07','2021-06-02 13:14:07',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(106,1,'109',1,'ยานพาหนะบริษัท-ค่าธรรมเนียมเครือข่าย','Own Fleet-Network','自有车队-网络费用',0,68212,0,'2021-06-02 13:15:01','2021-06-02 13:15:01',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(107,1,'110',1,'ประกันภัย-JP','Insurance-JP','保险费-JP',0,68212,0,'2021-06-29 05:46:25','2021-06-29 05:46:25',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(108,1,'111',1,'ยานพาหนะบริษัท-ค่าลงทะเบียน','Own Fleet-Vehicle Registration Fee','自有车队-车辆注册费',0,68212,68212,'2021-07-21 07:49:58','2021-07-21 09:12:18',0,0);
insert into `budget_object`(`id`,`level`,`level_code`,`is_end`,`name_th`,`name_en`,`name_cn`,`sort`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`,`template_type`)
values(109,1,'112',1,'ประกันภัย-SMK','Insurance-SMK','保险费-SMK',0,68212,0,'2021-07-29 02:43:37','2021-07-29 02:43:37',0,0);

-- 同步采购商品表
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(561,'','017','ค่าสำนักงาน','office allowance','办公费',0,3,0,74214,0,'2021-06-29 03:20:08','2021-06-29 03:20:08',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(560,'','017','ค่าสำนักงาน','office allowance','办公费',0,1,0,74214,0,'2021-06-29 03:20:08','2021-06-29 03:20:08',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(559,'','017','ค่าสำนักงาน','office allowance','办公费',0,2,0,74214,0,'2021-06-29 03:20:08','2021-06-29 03:20:08',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(558,'','032','หมวกกันน็อค','Helmet','头盔',0,2,0,68212,0,'2021-06-02 08:31:59','2021-06-02 08:31:59',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(557,'','098','ค่าเช่ารถ','Vehical Rental','车辆租赁',0,3,0,68212,68212,'2021-05-31 08:15:46','2021-05-31 08:15:46',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(556,'','005','ภาษีป้าย','Billboard Tax','广告牌税',0,3,0,68212,68212,'2021-05-21 04:12:45','2021-05-21 04:12:45',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(555,'','032','สายห้อยบัตรพนักงาน','Work Card Lanyard','工牌挂绳',0,2,0,59948,0,'2021-05-19 03:49:47','2021-05-19 03:49:47',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(554,'','032','โยโย่ห้อยบัตร','Work Card Buckle','工牌易拉扣',0,2,0,59948,0,'2021-05-19 03:43:29','2021-05-19 03:43:29',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(553,'','050','รถเหลืองคันเล็ก -ค่าบูรณะยานพาหนะ','Small yellow car-Vehical recovery fee','小黄车-车辆还原费',0,3,0,68212,0,'2021-05-19 03:41:33','2021-05-19 03:41:33',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(552,'','050','ประกันภัยรถขนส่งสินค้า -รถเหลือง','Small Yellow Car-Cargo insurance','小黄车-货物保险',0,3,0,68212,0,'2021-05-19 03:41:01','2021-05-19 03:41:01',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(551,'','050','ค่าจดทะเบียนรถ - รถเหลือง','Small Yellow Car-Yellow card registration fee','小黄车-黄牌注册费',0,1,0,68212,0,'2021-05-19 03:37:36','2021-05-19 03:37:36',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(550,'','050','ค่าจดทะเบียนรถ - รถเหลือง','Small Yellow Car-Yellow card registration fee','小黄车-黄牌注册费',0,3,0,68212,68212,'2021-05-19 03:37:36','2021-05-19 03:37:36',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(549,'','020','ค่าขนส่งสินค้าของจัดซื้อ','Purchase product transportation fee','采购产品运输费',0,1,0,59948,59948,'2021-05-02 04:10:36','2021-05-02 04:10:36',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(548,'','040','เครื่องชั่งน้ำหนักแบบพกพ','Electronic Hook scale','电子吊钩秤',0,2,0,68212,0,'2021-04-30 10:10:27','2021-04-30 10:10:27',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(547,'','040','หน่วยประมวลผลกลางเครื่องแม่ข่าย','CPU Server','CPU 服务器',0,2,0,68212,0,'2021-04-30 06:30:14','2021-04-30 06:30:14',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(546,'','032','เสื้อกันฝน','Raincoat','快递员雨衣',0,2,0,68212,0,'2021-04-26 07:18:23','2021-04-26 07:18:23',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(545,'','042','อีเมลล์องค์กรเทนเซ็นต','Tencent Enterprise Email','腾讯企业邮箱',0,2,0,59948,0,'2021-04-21 03:59:44','2021-04-21 03:59:44',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(544,'','085','NetBay CDD','NetBay CDD','NetBay CDD',0,2,0,59948,0,'2021-04-20 12:28:25','2021-04-20 12:28:25',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(543,'','045','เครื่องวัดอุณหภูมิแนวตั้ง','Vertical thermometer','立式体温量器',0,2,0,0,0,'2021-04-13 13:59:46','2021-04-13 13:59:46',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(542,'','044','สเปรย์ฆ่าเชื้อโรคในอุโมงค์','Spray disinfection tunnel','喷雾消毒隧道',0,2,0,0,0,'2021-04-13 13:58:26','2021-04-13 13:58:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(541,'','101','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,3,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(540,'','101','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(539,'','100','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,3,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(538,'','100','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(537,'','099','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,3,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(536,'','099','ค่าประชาสัมพันธ์การตลาด','Marketing','小黄车项目',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(535,'','098','ค่าเช่าโกดัง','Warehouse Rental','仓库租赁',0,3,0,68212,68212,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(534,'','098','ค่าเช่าสาขา','Network Rental','网点租赁',0,3,0,68212,68212,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(533,'','097','ค่าประชาสัมพันธ์การตลาด','Marketing','Apple商城续费',0,3,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(532,'','097','ค่าประชาสัมพันธ์การตลาด','Marketing','人脸识别服务费',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(531,'','097','ค่าประชาสัมพันธ์การตลาด','Marketing','云服务费',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(530,'','096','ค่าประชาสัมพันธ์การตลาด','Marketing','维修险',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(529,'','096','ค่าประชาสัมพันธ์การตลาด','Marketing','火灾险',0,2,0,0,0,'2021-04-13 13:45:02','2021-04-13 13:45:02',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(528,'','032','ตะข่ายชั่งน้ำหนัก','Weighing Mesh',' 网兜',0,2,0,0,0,'2021-04-12 03:10:44','2021-04-12 03:10:44',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(527,'','085','  ค่าบริการสแกนใบหน้า',' Face Matching Service',' 人脸比对服务',0,2,0,0,0,'2021-04-07 11:57:42','2021-04-07 11:57:42',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(526,'','085','บริการคลาวด','Cloud Service','云服务',0,2,0,59948,59948,'2021-04-07 11:57:42','2021-04-07 11:57:42',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(525,'','042',' Zendesk',' Zendesk',' Zendesk',0,2,0,0,0,'2021-04-07 11:53:32','2021-04-07 11:53:32',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(524,'','042','Call Center','Call Center','呼叫中心',0,2,0,0,0,'2021-04-07 11:53:32','2021-04-07 11:53:32',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(523,'','017','ค่าซ่อมแซม','Maintenance & Repairs','维修保养费',0,3,0,59948,59948,'2021-04-06 13:03:41','2021-04-06 13:03:41',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(522,'','017','ค่าทำความสะอาด','Cleaning','保洁费',0,3,0,0,0,'2021-04-01 08:30:10','2021-04-01 08:30:10',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(521,'','005','ภาษีเงินได้นิติบุคคลครึ่งป','PND51','PND51',0,3,0,0,0,'2021-03-30 03:23:37','2021-03-30 03:23:37',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(520,'','005','ภาษีเงินได้นิติบุคคลทั้งป','PND50','PND50',0,3,0,0,0,'2021-03-30 03:23:37','2021-03-30 03:23:37',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(519,'','005','ค่าธรรมเนียมจดทะเบียนภาษีมูลค่าเพิ่มสำหรับสาขา','VAT registration fee for branch','分公司注册费VAT',0,3,0,0,0,'2021-03-30 03:23:37','2021-03-30 03:23:37',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(518,'','017','ค่าจอดรถ','Parking','车位费',0,3,0,0,0,'2021-03-30 03:01:21','2021-03-30 03:01:21',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(517,'','042','ไมโครซอฟต์','Microsoft','微软',0,2,0,0,0,'2021-03-17 11:17:06','2021-03-17 11:17:06',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(516,'','050','ค่าตู้แดง','Police protection fee','警察保护费',0,1,0,0,0,'2021-03-12 06:37:35','2021-03-12 11:39:57',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(515,'','062','สายเคเบิ้ลไทร์ (ขนาด 300*4.8mm)','Small package tie(300*4.8 mm) ','小号封包扎带(300*4.8 mm)',0,2,0,0,0,'2021-03-12 06:36:11','2021-03-12 06:36:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(514,'','058','การสื่อสารและสื่อสิ่งพิมพ์','Print & Network Media Communication','平面&网络媒体传播',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(513,'','058','ค่าเลี้ยงรับรองสื่อ','media cooperation','媒体合作',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(512,'','058','สื่อและประชาสัมพันธ์','Media PR maintenance','媒体公关维护',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(511,'','058','ค่าเลี้ยงรับรองภาครัฐ','Government cooperation','政府合作',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(510,'','055','ค่าธรรมเนียมวีซ่า','Visa fee','签证费',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(509,'','005','ภาษีการจ้างงาน','Employment tax','雇佣税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(508,'','005','ภาษีป้ายทะเบียน','Vehicle License Plate Tax','车牌税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(507,'','005','ภาษีธุรกิจ','Business Tax','营业税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(506,'','005','ภาษีโรงเรือน','property tax','房产税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(505,'','005','ภาษีที่ดิน','Land tax','土地税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(504,'','005','อากรแสตมป์','Duty Stamp','印花税',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(503,'','005','ภาษีมูลค่าเพิ่มสำหรับต่างประเทศ','PP36','PP36',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(502,'','005','ภาษีมูลค่าเพิ่ม','PP30','PP30',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(501,'','005','ภาษีหัก ณ ที่จ่ายสำหรับต่างประเทศ','PND54','PND54',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(500,'','005','ภาษีหัก ณ ที่จ่าย นิติบุคคล','PND53','PND53',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(499,'','005','ภาษี หัก ณ ที่จ่าย บุคคล','PND3','PND3',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(498,'','005','ภาษีเงินได้ส่วนบุคคล','PND1','PND1',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(497,'','019','ค่าน้ำมัน','Freight-Fuel','油费',0,3,0,0,0,'2021-03-03 14:03:07','2021-03-03 14:03:07',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(496,'','038','บริการคลาวด์ -App','Adobe Creative Cloud (All Apps)','云服务-APP',0,3,0,0,0,'2021-03-03 14:02:35','2021-03-03 14:02:35',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(495,'','017','ค่าบริการซอฟต์แวร์','Software Service（Express/E-TAX/Get invoice）','软件服务费',0,3,0,0,0,'2021-03-03 14:02:35','2021-03-03 14:02:35',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(494,'','017','ค่าอินเตอร์เน็ต','Network','网络费',0,3,0,0,0,'2021-03-03 14:02:35','2021-03-03 14:02:35',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(493,'','079','ค่าเช่าอุปกรณ์ไฟฟ้า','Machinery and equipment rental','打印机租赁',0,3,0,0,0,'2021-03-03 14:02:35','2021-04-13 13:55:59',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(492,'','017','ค่าซ่อมแซม','Maintenance & Repairs','维修保养费',0,2,0,59948,59948,'2021-02-09 04:35:37','2021-02-09 04:35:37',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(491,'','017','ค่าทำความสะอาด','Cleaning','保洁费',0,2,0,0,0,'2021-02-03 11:44:21','2021-02-03 11:44:21',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(490,'','032','ผงซักฟอก','washing powder','洗衣粉',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(489,'','032','น้ำยาทำความสะอ','detergent','清洁剂',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(488,'','032','ถุงขยะ','garbage bag','垃圾袋',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(487,'','032','น้ำยาล้างมือ','liquid soap','洗手液',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(486,'','032','ที่ตักผง','dustpan','簸箕',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(485,'','032','ไม้กวาด','broom','扫帚',0,2,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(484,'','055','ค่าธรรมเนียมวีซ่า','Visa fee','签证费',0,1,0,0,0,'2021-01-22 10:12:13','2021-01-22 10:12:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(483,'','017','ค่ารักษาความปลอดภัย','Security','保安费',0,2,0,0,0,'2021-01-21 06:50:20','2021-01-21 06:50:20',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(482,'','062','ใบปลิว Flash Home','Flash Home Flyers','Flash Home 传单',0,2,0,0,0,'2021-01-21 06:48:54','2021-01-21 06:48:54',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(481,'','055','ค่าธรรมเนียมวีซ่า','Visa fee','签证费',0,2,0,0,0,'2021-01-21 06:47:46','2021-01-21 06:47:46',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(480,'','001','ค่าน้ำมัน','Fuel Cost','油费',0,1,2,0,0,'2021-01-29 08:28:20','2021-02-18 08:05:23',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(479,'','017','ค่าบริการซอฟต์แวร์','Software Service（Express/E-TAX/Get invoice）','软件服务费',0,2,0,0,0,'2021-01-19 08:13:36','2021-01-19 08:13:36',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(478,'','040','flash toy','flash toy','flash toy',0,2,0,0,0,'2021-01-19 02:56:16','2021-01-19 02:56:16',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(477,'','062','กระดาษปริ้นท์flash toy','flash toy printing paper','flash toy打印纸',0,2,0,0,0,'2021-01-19 02:56:11','2021-01-19 02:56:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(476,'','014','ค่าน้ำมัน','Fuel Cost','油费',0,1,0,0,0,'2021-01-15 06:03:27','2021-01-15 06:03:27',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(475,'','005','ภาษีที่ดิน','Land tax','土地税',0,2,0,0,0,'2021-01-15 05:56:11','2021-01-15 05:56:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(474,'','005','ภาษีป้ายทะเบียน','Vehicle License Plate Tax','车牌税',0,2,0,0,0,'2021-01-15 05:56:11','2021-01-15 05:56:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(473,'','005','ภาษีการจ้างงาน','Employment tax','雇佣税',0,2,0,0,0,'2021-01-15 05:56:11','2021-01-15 05:56:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(472,'','005','ภาษีธุรกิจ','Business Tax','营业税',0,2,0,0,0,'2021-01-15 05:56:11','2021-01-15 05:56:11',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(471,'','055','ค่าตรวจร่างกาย','Medical examination fee','体检费',0,1,0,0,0,'2021-01-15 05:53:46','2021-01-15 05:53:46',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(470,'','002','ค่าน้ำมัน','Fuel Cost','油费',0,2,0,0,0,'2021-01-15 05:51:05','2021-01-15 05:51:05',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(469,'','019','ค่าน้ำมัน','Freight-Fuel','油费',0,2,0,0,0,'2021-01-15 05:46:03','2021-01-15 05:46:03',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(468,'','063','เสื้อยืดคอกลม L','T-shirt L','T恤 L',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(467,'','063','กางเกงขายาวFlashExpress','Pants FlashExpress','长裤FlashExpress',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(466,'','063','เสื้อกันฝน','Raincoat','雨衣',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(465,'','063','เสื้อแจ็คเก็ต FlashExpress','Jacket FlashExpress','外套FlashExpress',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(464,'','063','เข็มขัดพยุงหลัง','Back strap','背部支撑带',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(463,'','063','เสื้อยืด FlashExpress','T-thirt FlashExpress','T恤衫FlashExpress',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(462,'','063','เสื้อยืดสีดำ FlashExpress','T-thirt FlashExpress（BLACK）','FlashExpress黑色T恤衫',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(461,'','063','เสื้อโปโล FlashHome','Polo FlashHome','POLO衫FlashHome',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(460,'','063','เสื้อโปโล FlashExpress','Polo FlashExpress','Polo衫FlashExpress',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(459,'','063','เสื้อแจ็คเก็ต Flash Home','Jacket Flash Home','Jacket Flash Home 外套',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(458,'','063','เสื้อยืด Flash Home','T-shirt FlashHome','Flash HomeT恤',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(457,'','062','กระดาษเลเบลเล็ก','Blue tooth printing paper','蓝牙打印纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(456,'','062','กระดาษเลเบลใหญ่','Big Label Paper Sticker','PC打印纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(455,'','062','ซองพลาสติก A3','EnvelopeA3','A3塑料袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(454,'','062','ซองพลาสติก A4','EnvelopeA4','A4塑料袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(453,'','062','สติ๊กเกอร์ COD','COD sticker','COD贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(452,'','062','สติ๊กเกอร์ High Value','Insured small label','保价小标签',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(451,'','062','สติ๊กเกอร์ SPEED','SPEED sticker','SPEED贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(450,'','062','สติ๊กเกอร์ VIP','VIP sticker','VIP 贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(449,'','062','สติ๊กเกอร์ระวังแตก','Fragile sticker','易碎贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(448,'','062','สติ๊กเกอร์บาร์โค้ดเล็ก','Change order small label','换单小标签',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(447,'','062','เทป OPP Flash','tape','胶带',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(446,'','062','ซองกระดาษสำหรับคูเรียร์','Courier paper bag','快递文件纸袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(445,'','062','สติ๊กเกอร์ติดเคเบิ้ลไทร์','Tie sticker','扎带贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(444,'','062','สติ๊กเกอร์จ่าหน้าซอง','Collection sticker','集包贴纸',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(443,'','062','bag เอกสาร','File collection bag	Simple','文件集包袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(442,'','062','bag พัสดุ','Parcel bag Simple','包裹集包袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(441,'','062','เคเบิ้ลไทร์ล็อกกระสอบ','Set bandage','集包扎带',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(440,'','062','สายเคเบิ้ลไทร์','Car ties','封车扎带',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(439,'','062','กล่องเหลือง Size Mini','Mini carton (yellow)','Mini号纸箱（黄）',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(438,'','062','กล่องเหลือง Size S','S paper (yellow)','S号纸（黄',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(437,'','062','กล่องเหลือง Size S+','S+ paper (yellow)','S+号纸（黄）',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(436,'','062','กล่องเหลือง Size M','M carton (yellow)','M号纸箱 （黄）',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(435,'','062','กล่องเหลือง Size M+','M+ carton (yellow)','M+号（黄）',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(434,'','062','กล่องเหลือง Size L','L carton (yellow)','L号纸箱 （黄）',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(433,'','062','กล่องผลไม้ Size S+','Fruit box Size S+','水果箱 Size S+',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(432,'','062','เทป OPP แบบใส','Scotch tape','透明胶带',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(431,'','062','Air Bubble','Bubble film','气泡膜',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(430,'','062','ฟิล์มยืด','Stretch film','拉伸膜',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(429,'','062','กล่องน้ำตาล Size Mini','BrownBoxMini','棕色纸箱Mini',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(428,'','062','กล่องน้ำตาล Size L','BrownBoxL','棕色纸箱L',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(427,'','062','กล่องน้ำตาล Size M','BrownBoxM','棕色纸箱M',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(426,'','062','กล่องน้ำตาล Size S+','BrownBoxS+','棕色纸箱S+',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(425,'','062','กล่องน้ำตาล Size S','BrownBoxS','棕色纸箱S',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(424,'','062','กล่องน้ำตาล Size M+','BrownBoxM+','棕色纸箱M+',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(423,'','062','ซอง Bubble','Bubble bag','气泡袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(422,'','062','โปสเตอร์','poster','海报',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(421,'','062','กล่องผลไม้ Size M','Fruit box Size M','水果箱 Size M',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(420,'','062','กล่องผลไม้ Size M+','Fruit box Size M+','水果箱 Size M+',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(419,'','062','กล่องผลไม้ Size L','Fruit box Size L','水果箱 Size L',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(418,'','062','ที่ตัดเทป OPP','Sealing tape cutter','封箱胶带切割器',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(417,'','062','แผ่น PEA ใสแปะกล่อง','PEA side single protective plastic bag','PEA 面单保护塑料袋',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(416,'','040','เครื่องพิมพ์เลเบลขนาดพกพา','Bluetooth printer','蓝牙打印机',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(415,'','040','IDATA 95 W','IDATA 95 W','IDATA 95 W',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(414,'','040','เครื่องเสียบบัตรประชาชน','Card reader','读卡器',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(413,'','046','ธงญี่ปุ่น','Japanese billboard','日本广告牌',0,2,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(412,'','058','การสื่อสารและสื่อสิ่งพิมพ์','Print & Network Media Communication','平面&网络媒体传播',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(411,'','058','ค่าเลี้ยงรับรองสื่อ','media cooperation','媒体合作',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(410,'','058','สื่อและประชาสัมพันธ์','Media PR maintenance','媒体公关维护',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(409,'','058','ค่าเลี้ยงรับรองภาครัฐ','Government cooperation','政府合作',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(408,'','017','คัดลอกข้อมูล','Copy','抄资料',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(407,'','017','ค่าบริการซอฟต์แวร์','Software Service（Express/E-TAX/Get invoice）','软件服务费',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(406,'','017','ค่าธรรมเนียมการจัดเก็บไฟล์','Document storage fee','文件保管费',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(405,'','017','ค่าอินเตอร์เน็ต','Network','网络费',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(404,'','017','เครื่องมือเครื่องใช้ไฟฟ้า','Appliance tool','器具工具',0,1,0,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(402,'','001','ค่าตรวจสุขภาพ','inspection fees','检测费',0,1,1,0,0,'2021-01-12 13:44:13','2021-01-12 13:44:13',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(401,'','005','ภาษีหัก ณ ที่จ่ายสำหรับต่างประเทศ','PND54','PND54',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(400,'','005','ภาษีมูลค่าเพิ่มสำหรับต่างประเทศ','PP36','PP36',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(399,'','005','ภาษีมูลค่าเพิ่ม','PP30','PP30',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(398,'','005','ภาษีหัก ณ ที่จ่าย นิติบุคคล','PND53','PND53',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(397,'','005','ภาษี หัก ณ ที่จ่าย บุคคล','PND3','PND3',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(396,'','005','ภาษีเงินได้ส่วนบุคคล','PND1','PND1',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(395,'','005','ภาษีโรงเรือน','property tax','房产税',0,2,0,0,0,'2021-01-12 05:16:28','2021-01-12 05:16:28',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(394,'','017','ของใช้สำนักงาน','Office Supplies','办公用品',0,1,0,0,0,'2021-01-04 12:27:55','2021-01-04 12:27:55',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(393,'','040','แบตเตอรี่','Battery','电池',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(392,'','032','ค่าตะแกรงเหล็กมอเตอร์ไซต์','Motorcycle steel grille','摩托车钢格栅费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(391,'','032','กระเป๋า','Shoulder bag','挎包',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(390,'','032','เครื่องตัดเทป','Tape cutter','胶带切割器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(389,'','032','ถาดพลาสติก','Load tray','塑胶托盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(388,'','032','ถาดพลาสติกสีดำ','Load tray','黑塑胶托盘',0,2,0,0,59948,'2020-12-31 17:07:26','2020-12-31 17:07:26',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(387,'','032','ป้ายโฆษณา - ค่าบริการขนส่ง','Delivery On Site Service','广告牌-派送服务',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(386,'','040','เครื่องวัดระดับเสียงดิจิตอล','Digital Sound Level Meter','数码分贝仪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(385,'','032','แผงกั้นจราจร','Block Steel Footpath','路障',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(384,'','041','เก้าอี้ยาว','Bench','长凳',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(383,'','032','เครื่องดูดฝุ่น','Vacuum Cleaner','吸尘器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(382,'','032','ป้ายโฆษณา - แผนผังองค์กร','Organization Chart Board','广告牌-组织架构图版',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(381,'','032','ลำโพง','Megaphone','扩音器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(380,'','041','ตู้สำนักงาน','Office Cabinet','办公柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(379,'','048','เครื่องตัดกระดาษ','Wooden Paper Trimmer','切纸机',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:14:28',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(378,'','032','ที่รองรับยึดแน่น','Fix Shelve','固定架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(377,'','032','กระเป๋ารถมอเตอร์ไซค์','Motorcycle Bike Side Saddle Bag','摩托车挎包',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(376,'','032','ชั้นวางของขนาดเล็ก','GERMAN Mini Rack Steel Box with Shelf','迷你置物架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(375,'','040','เครื่องควบคุมไร้สาย','UniFi Cloudkey','无线控制器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(374,'','040','เครื่องมือวัด','LAN Professional Set of Tool and Tester','测试仪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(373,'','032','ปากกาไวท์บอร์ดสีแดง','Whiteboard Marker  Colour : Red','红色白板笔',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(372,'','032','ป้ายแผนก','Label for identification','标识牌',0,2,0,68212,68212,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(371,'','032','ถังดับพลิง','Fire Extinguisher Sign','灭火器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(370,'','032','ป้ายโฆษณา-บริการส่งพัสดุถึงหน้าบ้าน','Delivery On Site Service','广告牌-送货上门服务',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(369,'','039','ฉากกั้น','Mini Partition Fabric','隔断',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(368,'','032','หลอดLED','LED Tube','LED灯管',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(367,'','039','ค่าติดตั้ง-กันสาด','Awning with installation','安装费-遮阳棚',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(366,'','032','ถังขยะ','High caster trashcan with Flat lid','垃圾桶',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(365,'','032','ถาดพลาสติก','Pallet Plastic','塑料托盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(364,'','032','สายรัดข้อมืออัจฉริยะ','Xiaomi Smart Band','智能手环',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(363,'','032','ที่เขี่ยบุหรี่','Stainless Steel Ashtray Receptacl','烟灰缸',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(362,'','032','เครื่องปริ้นท์บลูทูธ','Adapter for support bluetooth Printer','蓝牙打印机配件',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(361,'','043','ตู้ไฟฟ้า','Distribution box','配电箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(360,'','032','เต้นท์กระโจม','Tent','帐篷',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(359,'','043','เครื่องทำกาแฟ','Coffee machine','咖啡机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(358,'','039','กระดานไม้ก๊อก','Cork board','软木板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(357,'','043','ตู้เซฟกรอกรหัส','Safe Deposit Box','数码保险箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(356,'','041','เครื่องกดน้ำดื่ม','Water dispenser','冷热饮水机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(355,'','039','แผ่นป้ายพื้นหลัง','Fabric Backdrop','背景板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(354,'','043','รั้วตาข่าย','Wire Mesh Fence','护栏网',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(353,'','041','ชั้นวางกล่อง','Packing Counter','装箱台',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(352,'','039','ติดตั้งจัดซื้อไฟฉุกเฉิน','emergency light','安装购买应急灯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(351,'','043','เครื่องสูบน้ำ','water pump','抽水机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(350,'','032','พัดลมไฟฟ้าอุตสาหกรรม','electric fan','工业电风扇',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(349,'','043','เครื่องจ่ายไฟ','electric generator','发电机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(348,'','039','ค่าจัดซื้อ-ติดตั้งโคมไฟสปอร์ตไลท์','Purchase + installation Embedded spotlight','购买+安装 嵌入式射灯费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(347,'','043','เครื่องปั๊มน้ำ','water pump','水泵',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(346,'','043','ตู้เซฟ','strongbox','保险箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(345,'','040','อุปกรณ์สื่อสาร -IPC','Inter Process Communication (IPC)','通讯设备-IPC',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(344,'','043','ลำโพง','Power Speaker','音箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(343,'','040','หน้าจอสัมผัส PC','PC touch pane','PC 触控屏',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(342,'','040','สวิตช์ POE','POE switchboard','POE交换机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(341,'','040','เครื่องตอกบัตร','punched-card machine','打卡机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(340,'','040','เราเตอร์สาขา','router','网点路由器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(339,'','040','คอมพิวเตอร์dell','dell computer','dell笔记本',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(338,'','043','สำนักงานใหญ่จัดซื้อเครื่องปริ้นท์','Printer','总部购打印机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(337,'','040','ชื่องกรอกรหัสความปลอดภัย','Security code box','安全代码框',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(336,'','043','เครื่องปริ้นท์EPSON','EPSON Printer','EPSON打印机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(335,'','039','CCTV+ค่าติดตั้ง','CCTV + installation fee','CCTV +安装费',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:06:49',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(334,'','040','กล้องถ่ายภาพ','Panasonic Lumix','相机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(333,'','040','ค่าเครื่องโปรเจ็กเตอร์','Projector fee(header office)','总部投影机费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(332,'','043','สาขาจัดซื้อเครื่องปริ้นท์','Printer(branch)','网点购买打印机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(331,'','040','กล้องวงจรปิด','Monitoring equipment','监控设备',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-21 06:45:21',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(330,'','048','สมุด','Notebook','本',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:14:28',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(329,'','040','กล้องวิดีโอ','Video camera','摄像机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(328,'','040','เครื่องบันทึกวิดีโอ VCR','VCR','录像机VCR',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(327,'','040','โทรทัศน์LED','LED TV','LED TV',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(326,'','040','เครื่องขยายเสียง','speaker','扬声器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(325,'','043','ลำโพงเคลื่อนที่','Mobile audio','移动音响',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(324,'','040','กล้องSony','Sony camera','Sony相机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(323,'','040','จอคอมพิวเตอร์','Lenovo computer screen','电脑显示器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(322,'','040','จอตรวจสอบ','Monitor','监视器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(321,'','040','หม้อแปลงกล้อง','Camera transformer','摄像机变压器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(320,'','040','เซิร์ฟเวอร์','Server','服务器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(319,'','040','สแกนเนอร์','Laser Barcode Scanner','扫描仪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(318,'','040','อุปกรณ์อินเทอร์เน็ต','IT Network Equipment','网络设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(317,'','040','ไมโครโฟนไร้สาย','Wireless Microphone','无线麦克风',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(316,'','043','เครื่องเคลือบ','Laminating Machine','覆膜机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(315,'','040','โทรศัพท์','mobile phone','手机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(314,'','040','MacBOOK','Mac','苹果电脑',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(313,'','040','โทรศัพท์ IP','IP Phone','IP电话',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(312,'','040','กล้องวงจรปิด CCTV','CCTV','监控摄像机CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(311,'','040','เครื่องสแกนลายนิ้วมือ','Fingerprint scanner','指纹扫描仪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(310,'','043','เครื่องพิมพ์','Card printing machine','印刷机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(309,'','043','เครื่องโทรสาร','panasonic fax','传真机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(308,'','043','เครื่องพิมพ์','Printer','打印机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(307,'','040','โปรเจคเตอร์','Projector','投影机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(306,'','043','เครื่องสำรองไฟฟ้าและปรับแรงดันไฟฟ้าอัตโนมัติ','Uninterruptible Power Supply (UPS)','不间断电源',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(305,'','032','ที่วางทีวี','Television Stand','电视支架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(304,'','040','โทรทัศน์','WALL RACK','电视',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(303,'','032','ชั้นวางทีวี','CLOSE RACK','电视机架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(302,'','040','เครื่องชั่งดิจิตอล','Digital Scales T-Scale','数字秤',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(301,'','040','แบตเตอรี่ - แคนนอน','Battery Canon','电池-佳能',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(300,'','040','กล้อง','Camera Photography Canon','相机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(299,'','040','มอนิเตอร์','Monitor Dell','显示器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(298,'','040','อุปกรณ์อิเล็กทรอนิกส์','PC Lamel','电子设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(297,'','040','สวิตช์ CCTV','Switch-CCTV','交换机-CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(296,'','043','ตู้เย็น','Double Doors Refrigerator SHARP','冰箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(295,'','040','กล่องเสียง','Potrable Speaker System with microphone','蜂鸣箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(294,'','043','ม่าน','Motorized Screen Vertex','幕布',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(293,'','032','เครื่องใช้ในโรงอาหารและห้องน้ำ','Canteen and restroom appliances July','食堂和卫生间用具',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(292,'','040','โปรเจคเตอร์','Projector EPSON','投影仪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(291,'','032','ป้ายโฆษณา-ปริ้นท์ป้ายโฆษณา','Print CMYK Outdoor','广告牌-打印条幅',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(290,'','032','เครื่องวัดอุณหภูมิหน้าผาก','Electronic Infrared Thermometer iHealth','额温枪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(289,'','040','ค่าอินเทอร์เน็ต','Internet Bandwidth','宽带费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(288,'','040','เตาอบไมโครเวฟ','Microwave Oven','微波炉',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(287,'','040','กล้อง','camera','摄像头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(286,'','040','เซิร์ฟเวอร์จัดเก็บเครือข่าย','Network cabinet','网络存储服务器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(285,'','040','เครื่องพิมพ์ PC','PC Printer','PC打印机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(284,'','043','เครื่องทำความชื้น','Humidifier','加湿器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(283,'','040','หูฟัง','Plantronics Headset','耳机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(282,'','032','หลอด LED','LED FlashLight','LED灯泡',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(281,'','032','อุปกรณ์เสริม','Accessories','配件',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(280,'','040','แล็ปท็อป','Notebook Lenovo','笔记本电脑',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(279,'','032','ตลับหมึกพิมพ์','Printer Ink Tank Epson','打印机墨盒',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(278,'','040','เครื่องชั่งอิเล็กทรอนิกส์','Electronic balance','电子秤',0,2,0,68212,68212,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(277,'','040','แบตเตอรี่ - กล้องวงจรปิด','Battery(CCTV)','电池-CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(276,'','040','ฮาร์ดดิสก์','External Harddisk','硬盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(275,'','040','กล่อง CPU','Case','机箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(274,'','040','อุปกรณ์ไร้สาย AP','Access Point Dual','无线设备-AP',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(273,'','040','สวิตซ์','switch','交换机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(272,'','040','อุปกรณ์ไร้สาย','Wireless device','无线设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(271,'','043','เครื่องส่งรับวิทยุ','Walkie Talkie','对讲机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(270,'','040','คอมพิวเตอร์','PC (Made by Order)','电脑',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(269,'','040','ฮาร์ดดิสก์กล้องวงจรปิด','Hard Disk CCTV','CCTV硬盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(268,'','040','กล้องวงจรปิด','CCTV-Camera','CCTV-摄像头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(267,'','040','คอมพิวเตอร์-ตัวเครื่อง','All In One PC','电脑一体机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(266,'','040','ชั้นวางสำหรับจัดเก็บอุปกรณ์สื่อสาร','Equipment Racks','存储通信设备的机架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(265,'','047','รถสามล้อ -3W','3 W Gasoline tricycles','三轮车-3W',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(264,'','047','รถบรรทุก -4W','4 W Isuzu trucks','卡车-4W',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(263,'','047','รถบรรทุก -6W','6 W Isuzu trucks','卡车-6W',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(262,'','047','รถสินค้า 6w','6 W wheel vehicle','货车-6W',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(261,'','047','รถยนต์','Hyundai VIP 1','小汽车-现代',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(260,'','047','รถสินค้า','truck','货车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(259,'','032','ไวท์บอร์ดสำนักงานใหญ่','whiteboard（header Office）','总部白板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(258,'','039','ประตูกระจก','Glass door','玻璃门',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(257,'','043','เครื่องทำลายเอกสาร','Shredder','碎纸机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(256,'','039','บอร์ดพาติชั่นสำนักงานคงที่','Fixed partition board of office','办公室固定分区板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(255,'','041','ลิ้นชัก','Drawer cabinet','抽屉柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(254,'','039','โต๊ะปิงปอง','Table tennis table','乒乓球桌',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(253,'','041','โต๊ะประชุม','Conference table','会议桌',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(252,'','041','โต๊ะรับแขก','Living room table','客厅桌',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(251,'','041','โซฟาหนังเทียม','Side table of faux leather sofa','人造皮沙发边几',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(250,'','041','โซฟาหนังเข้ามุม','Leather sofa corner table','造皮沙发角几',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(249,'','041','ตู้โลหะเก็บเอกสาร','Cupboard with drawers','带屉柜铁皮柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(248,'','041','ตู้ใช้โฆษณา','Counter','广宣使用柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(247,'','039','ผนังพื้นหลังใช้สำหรับประชาสัมพันธ์','Back drop','广宣使用的背景墙',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(246,'','041','เคาน์เตอร์สาขา','Counter（branch）','网点柜台',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(245,'','041','เคาน์เตอร์ลูกค้า','Counter（customer）','客户柜台',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(244,'','041','ตู้เก็บเอกสาร','Data cabinet','资料柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(243,'','041','ตู้เก็บของ','Storage tank','储柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(242,'','041','โต๊ะยาว','Long table','长桌子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(241,'','041','ตู้','Cabinet','柜子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(240,'','041','เก้าอี้','Short chair','椅子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(239,'','040','เครื่องทำน้ำอุ่น','WaterHeater-CoolerDispenser','热水器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(238,'','041','โต๊ะ','Packing Desk','桌子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(237,'','041','ชั้นเก็บของ','Steel Storage Shelf','储物架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(236,'','039','ซ่อมแซม PVC','PVC Patation','PVC修补',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(235,'','039','ฉากกั้น PVC','PVC Partition','PVC隔断',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(234,'','039','กรอบอลูมิเนียม','Aluminum glass','铝框',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(233,'','039','แผ่นยิปซัม','Ceiling Gypsum','石膏板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(232,'','040','เครื่องปรับอากาศ','Air conditioning','空调',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(231,'','032','เครื่องคิดเลข','Counter','计数器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(230,'','040','อุปกรณ์ไร้สาย- ตัวควบคุมคลาวด์','UniFi Cloudkey','无线设备-云端控制器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(229,'','040','อุปกรณ์ไร้สาย- สวิตซ์','UniFi Switch','无线设备-交换机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(228,'','041','เคาน์เตอร์','Counter','柜台',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(227,'','041','ตู้กดน้ำ','Water Heater-Cooler Dispenser','饮水机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(226,'','032','หมอน','Pillow','枕头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(225,'','041','ที่นอน','Mattress','床垫',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(224,'','041','เตียง','Steel Bunk Bed with mattress and pillow','床',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(223,'','041','โต๊ะทำงาน','Office Desk','办公桌',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(222,'','039','ลูกกลิ้งประตูบานเลื่อน','Wire Mesh Slide Gate Roller','滑门滚轮',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(221,'','041','โต๊ะพับ','Multipurpose Folding Table','折叠桌',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(220,'','041','เก้าอี้สำนักงาน','Office Chair','办公椅子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(219,'','039','หน้าต่างกระจก','Iglass Windows','玻璃窗',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(218,'','039','ประตูบานพับ PVC','PVC folding door','PVC折叠门',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(217,'','040','เครื่องชั่ง','Scales','称重器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(216,'','043','คอนเทนเนอร์','container','集装箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(215,'','043','คอนเทนเนอร์สาขา','Outlet container','网点集装箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(214,'','043','โครงเหล็กพับ','Folding iron frame','折叠式铁框',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(213,'','045','เครื่องตัดหญ้า','Brush Cutter','割草机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(212,'','046','รถลาก','Human pull cart template','人力拉车模板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(211,'','043','กรงพับเก็บได้','Roll Cage','折叠式仓储笼',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(210,'','032','รถยกแมนนวล','Manual tray','手动托盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(209,'','043','รถยก','Forklift','叉车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(208,'','043','ล้อเลื่อน','Fibertech Platform Truck','平台车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(207,'','041','ตู้วางเครื่อง','Close Rack','机柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(206,'','039','ติดตั้งอุปกรณ์','Installation Tool','安装工具',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(205,'','043','เครื่องชั่ง','Zepper Zepper scale','秤',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(204,'','043','ตะแกรงเหล็กพับได้','Foldable steel grating','可折叠钢格板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(203,'','043','เครื่องชั่งอิเล็กทรอนิกส์','Electronic scale','电子秤',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(202,'','043','ชั้นวางของ','Product Shelves','货架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(201,'','040','เครื่องสแกนเนอร์แบบพกพา','Carrying the scanner','携带扫描机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(200,'','032','พัดลมไฟฟ้าแบบไอน้ำ','Water spray electric fan','水喷雾电风扇',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(199,'','043','กรงเก็บของ','Storage cage','仓储笼',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(198,'','039','ค่าติดตั้ง PVC','cost of installation wiring PVC pipe,','安装费-PVC',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(197,'','040','คลังสินค้า PDA','Warehouse PDA','仓储PDA',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(196,'','043','ชั้นวางแบ็กกิ้ง','Bin Cabinet','集包架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(195,'','032','กล่องท้ายรถมอเตอร์ไซค์','Motorcycle trunk','摩托车后箱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(194,'','032','รถเข็นพาเลทแบบแมนนวล','Manual pallet trolley','手动托盘推车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(193,'','043','ตะแกรงเหล็กพับได้','Foldable steel grille','可折叠钢格栅',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(192,'','043','ชั้นวางสต็อก','Inventory rack','库存架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(191,'','043','ชั้นวางคลังสินค้า','Warehouse rack','仓存架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(190,'','043','รถยกแบบแมนนวล','Hand Lift','手动叉车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(189,'','032','สายรัด','strap','背带',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(188,'','032','ขาตั้งกล้องทีวี','TV stand','电视脚架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(187,'','032','รถลากพาเลท','Pallet Truck','拖板车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(186,'','032','อุปกรณ์ตะแกรงเหล็ก','Sieve equipment','筛子设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(185,'','032','ตะแกรงเหล็ก','Iron sieve','铁筛子',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(184,'','043','ชั้นเหล็ก','Boltless Steel shelves','钢货架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(183,'','041','ตู้เก็บเอกสาร','Document Cabinet','文件柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(182,'','043','บันไดเคลื่อนย้าย','chequer plate stair','移动楼梯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(181,'','043','เครื่องชั่งอัตโนมัติ','Automatic scale','自动秤',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(180,'','032','ไวท์บอร์ด','Whiteboard + Rolling Castors','白板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(179,'','032','ชั้นวางรองเท้า','Steel Shoe rack','鞋架',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(178,'','045','ขนส่ง','Transportation','运输',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(177,'','043','ทางลาดเหล็ก','Steel Ramp','钢坡道',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(176,'','032','พัดลมไฟฟ้า','Electric fan','电风扇',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(175,'','041','ล็อกเกอร์','Locker Cabinet','储物柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(174,'','041','ตู้เหล็ก','Steel Cabinet','钢柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(173,'','043','ชั้นลอย','Steel Platform Truck','钢平台车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(172,'','032','พัดลมอุตสาหกรรม','Industrial Fan Venz','工业风扇',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(171,'','043','ปืนยิงสแกน','Code Scanner','扫码枪',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(170,'','043','รถยกไฮดรอลิกแบบแมนนวล','Hand Pallet Truck','手动液压叉车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(169,'','032','ถาดตะแกรง','Mesh Pallet','网格托盘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(168,'','043','เครื่องชั่งดิจิตอล','Digital Scales T-Scale','数字秤',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(167,'','040','IDATA','I data PDA','IDATA',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(166,'','043','ตะแกรงเหล็ก','Steel grille','钢栅',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(165,'','045','รถเข็น','trolley','手推车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(164,'','045','รถเข็น','Cart','推车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(163,'','043','กรง','Cage','笼车',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(162,'','040','เครื่องชั่งดิจิตอล','Digital scale','数码称',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(161,'','044','เครื่องคัดแยก','Sorter','分拣机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(160,'','044','สายพาน','Belt conveyor','皮带机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(159,'','044','สายการผลิต','assembly line','流水线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(158,'','044','เครื่องสไลด์','Telescopic machine','伸缩机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(157,'','044','เครื่องรัดยางอัตโนมัติ','Auto Matic Strapping Machine','自动胶粒捆扎机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(156,'','032','อุปกรณ์ปั๊มน้ำ','Peplacement Comsumable Part for Diesel','水泵配件',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(155,'','045','สว่านไฟฟ้าหมุน','Rotary power drill Bosch','旋转电钻',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(154,'','044','เหล็กเส้น','Steel wire','钢丝',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(153,'','044','ต่อสายพาน','Hook to belt','钢扣',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(152,'','044','เครื่องมือต่อสายพาน','Belt-Conveyor','钢扣机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(151,'','044','อุปกรณ์โลจิสติกส์','Logistics equipment','物流设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(150,'','038','ค่าบริการซอฟต์แวร์สำนักงาน','Office software fee','办公软件费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(149,'','038','บริการคลาวด์ -App','Adobe Creative Cloud (All Apps)','云服务-APP',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(148,'','043','อุปกรณ์โรงพยาบาล','Accessories For Hospital Room','医务室设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(147,'','038','ค่าบริการระบบ CS','User Call center-CS','CS系统服务费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(146,'','039','ลานจอดรถพื้นกรวด','Filling the parking area  crushed stone','停车场碎石',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(145,'','038','บริการห้องปฏิบัติการ','Environment Laboratory Services','环境实验室服务',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(144,'','039','ใบอนุญาต - กล้อง','License-Camera','许可证-摄像机',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(143,'','039','ค่าติดตั้ง - กำลังคน','Installation Manpower','安装费-人力',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(142,'','042','เช่า Cloud Contact Center','Rental Cloud Contact Center','租赁-云联络中心',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(141,'','040','กล้องวงจรปิด','CCTV Support','CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(140,'','042','ค่าธรรมเนียมใบอนุญาตโทรคมนาคม','Telecommunications License Fee','电讯牌照费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(139,'','039','ซ่อมบำรุงห้องน้ำ','Renovation toilet fee','厕所装修',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(138,'','039','ซ่อมบำรุงแฟลชโฮม','Renovation-Franchisees Renovation','加盟商装修',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(137,'','039','ค่าติดตั้ง  - เสาเหล็ก','Installation fee-steel column','安装费-钢柱',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(136,'','039','ค่าซ่อมบำรุง  - ไฟฟ้า','Decoration fee-electricity','装修费-电力',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:06:30',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(135,'','039','ค่าซ่อมบำรุง  - LED','Decoration fee-LED','装修费-LED',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(134,'','039','สาขาซ่อมบำรุง - MDB','Branches Installation MDB','网点装修-MDB',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(133,'','039','รื้อถอนสาขา','Branches demolish','网点拆除',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(132,'','039','ค่าซ่อมบำรุง- การตรวจสอบพลังงานไฟฟ้า','Renovation Fee-Electricity Inspection','装修费-电力检查',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:03:34',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(131,'','039','สาขาซ่อมบำรุง - ห้องน้ำ','Branches decoration-bathroom','网点装修-卫生间',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(130,'','039','ค่าติดตั้ง - กล้องวงจรปิด','Installation fee-CCTV','安装费-CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:03:04',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(129,'','039','ค่าติดตั้ง - ประตู','Installation Door','安装费-门',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(128,'','039','สาขาติดตั้ง - ประตู','Branches Installation DoorPhase','网点安装-门',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(127,'','039','สาขาติดตั้ง - ระบบไฟฟ้า','Branches Installation Electricity SystemMD','网点安装-电力系统',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:02:44',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(126,'','039','สาขาซ่อมบำรุง - ท่อระบายน้ำ','Branches Construct Basement','网点装修-下水道',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(125,'','039','ค่าซ่อมบำรุง','Renovation costs','装修费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(124,'','032','เดินสายแอร์','Headquarters airconditioner wiring','空调接线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(123,'','039','สาขาติดตั้งสายไฟ','Branches Installation Electricity Cablefee','网点安装-电线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(122,'','039','ปรับปรุงระบบไฟฟ้า','Electricity System Construction','装修-电力系统',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:02:29',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(121,'','039','ค่าติดตั้ง - หม้อแปลง','Transformer Installation','安装费-变压器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(120,'','039','ปรับปรุงระบบไฟฟ้า','Branches Renovation Electricity Systemfee','电力系统装修',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:00:50',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(119,'','039','ภาษี','Tax','税',0,2,0,0,0,'2020-12-31 17:07:26','2021-02-03 11:47:27',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(118,'','039','สาขาติดตั้ง- เครื่องปรับอากาศ','Branches Installation airconditionerfee','网点安装-空调',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(117,'','039','สาขาติดตั้ง AI','Branches Installation AI','网点安装-AI',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(116,'','039','ค่าติดตั้ง - ประปา','Installation fee-water','安装费-水',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(115,'','039','ค่าติดตั้ง - เครื่องปรับอากาศ','demolishairconditioner+Installationairco','安装费-空调',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(114,'','039','ค่าซ่อมบำรุงสาขา','Branches Repairfee','网点修理费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(113,'','039','ค่าติดตั้ง - ไฟ','Installation lamp','安装费-灯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(112,'','039','ค่าซ่อมบำรุง กำแพงแตกร้าว','Renovation fee-smash the wall','装修费-粉碎墙壁',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(111,'','039','ค่าติดตั้ง - อุปกรณ์ไฟฟ้า','Installation Electricity System Equipment','安装费-电力设备',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:00:31',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(110,'','039','สาขาซ่อมบำรุง สายไฟ','Branches Installation Electricity CableSy','网点装修-布电线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(109,'','039','สาขาติดตั้งกล้องวงจรปิด','Branches Installation CCTVfee','网点安装-CCTV',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 10:00:07',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(108,'','039','สาขาซ่อมบำรุง','Branches decoration','网点装修',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(107,'','039','สาขาซ่อมบำรุงระบบไฟฟ้า','Branches Electricity Constructionfee','网点装修-电力系统',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(106,'','039','สาขาติดตั้ง','Branches Renovation','网点安装',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(105,'','039','สาขาติดตั้ง -LOGO ฯลฯ','Branches Installation El','网点安装-LOGO等',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(104,'','039','สาขาการติดตั้ง - ไฟ ฯลฯ','Branches Installation Systemfe','网点安装-照明灯等',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(103,'','039','ผนังกั้นห้อง','aluminium bulkhead wall Construction Pha','隔断墙',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(102,'','032','สายอินเตอร์เน็ต','Network SystemCable','网线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(101,'','039','ระบบไฟฟ้า','ElectricitySystem','电力系统',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-22 09:59:52',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(100,'','039','ค่าก่อสร้าง','Construction Contract Fee','施工费用',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(99,'','040','อุปกรณ์ไร้สาย AC','UniFi AC LR','无线设备-AC',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(98,'','040','เราเตอร์','Mikrotik Router and wireless','路由器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(97,'','032','ม่าน','Windows Curtain','窗帘',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(96,'','039','ซ่อมบำรุง - เปลี่ยนเครื่องปรับอากาศ','Remove Old Air Conditioner','装修-换空调',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(95,'','040','อินเวอร์เตอร์','Ceiling Suspended Type (Nom Inverter)','逆变器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(94,'','032','ปลั๊กไฟ','Power Plug','电源插头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(93,'','040','เบรกเกอร์','Circuit Breaker','断路器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(92,'','032','แผ่นเหล็กตู้ติดผนัง','Wall Mouting Cabinet steel sheet','壁挂柜钢板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(91,'','039','รื้อประตูเก่า','Remove old door','拆除旧门',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(90,'','039','โครงสร้างเหล็กรับน้ำหนัก','Steel Construction for  the weight','承重钢结构',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(89,'','039','ติดตั้งระบบประตูม้วน','Installation Roling shutter door  system','安装卷帘门系统',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(88,'','039','ค่าทำเนียม','Processing fee','手续费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(87,'','039','ค่าติดตั้ง - LED','Installation fee-LED','安装费-LED',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(86,'','032','อุปกรณ์เบ็ดเตล็ด','Miscellaneous equipment','杂项设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(85,'','032','ซีลล็อค','Cable Tie','电缆扎带',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(84,'','032','เทปพันสายไฟ','Electrical tape','电工胶带',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(83,'','032','คีมหนีบ','Clamp','钳',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(82,'','032','สลิง','Sling','吊索',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(81,'','032','รถไฟฟ้า','Light rail','轻轨',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(80,'','041','ตู้ศูนย์ควบคุม','Control cabinet Load Centyer','管控中心-柜',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(79,'','032','ไฟ','Lamp','灯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(78,'','039','ทาสีผนัง','Painted wall','刷墙',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(77,'','039','ติดตั้ง - ซ่อมไฟ','Repair Change the light bulb','安装-修理灯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(76,'','032','ท่อเหล็ก','Steel Pipe','钢管',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(75,'','040','อุปกรณ์ไร้สาย','UniFi Security Gateway PRO','无线设备-网关',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(74,'','032','เปิดปิด','UniFi Switch','开关',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(73,'','032','ปลั๊ก','Male electrical plug','插头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(72,'','032','กรรไกรตัดสายเคเบิล','Cable Cutters','电缆剪刀',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(71,'','032','กล่องแยกไฟฟ้า','Junction box','接线盒',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(70,'','032','ไขควง','Screwdriver','螺丝刀',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(69,'','032','ประแจ','Wrenches','扳手',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(68,'','032','สว่านไฟฟ้า','Electric wrenches','电动扳手',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(67,'','032','แผงเสียบปลั๊ก','Power strip','插排',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(66,'','032','ก๊อกน้ำ','Faucet','水龙头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(65,'','032','ซิงค์น้ำ','Single Bowl Sink','水槽',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(64,'','039','ค่าติดตั้ง - ปลั๊ก','Power Plug with installation','安装费-插头',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(63,'','039','ประกัน','Insurance','保险',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(62,'','032','กระเช้าเหล็ก','Steel Wireway','钢索道',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(61,'','032','สวิตช์บอร์ด','Main Destribution Board','配电板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(60,'','032','ลวดทองแดง','Copper Conductpr','铜线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(59,'','039','ค่าซ่อมบำรุง','Amortization-Decoration','装修',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(58,'','039','อุปกรณ์อื่นๆ','Other Equipment','其他设备',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(57,'','032','แผงงวงจร','Load Panel','电路板',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(56,'','039','ค่าแรงงาน','Labour Cost','劳务费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(55,'','039','ซ่อมบำรุง- ตัวตัดไฟเบรกเกอร์','Residue current Circuit Breaker Safe','装修-断路器安全',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(54,'','039','ซ่อมบำรุง- เบรกเกอร์','Renovation-Circuit Breaker','装修-断路器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(53,'','032','สายเคเบิล','cable','电缆',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(52,'','032','สายไฟ','Power Cable','电源线',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(51,'','039','ซ่อมบำรุง - ตัวควบคุม','Decoration-Controller','装修-控制器',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(50,'','032','ไฟLED','LED lights','LED灯',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(49,'','039','ค่าติดตั้ง','Installation fee','安装费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(48,'','038','ค่าบริการซอฟต์แวร์APP','APP software service fee','APP软件服务费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(47,'','038','ค่าธรรมเนียมการจดทะเบียนเครื่องหมายการค้า','Trademark registration fee','商标注册费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(46,'','039','ติดตั้ง - ค่าบริการระบบCS','Installation-CS system service fee','安装-CS系统服务费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(45,'','038','ค่าบริการระบบบุคลากร','Personnel system service fee','人事系统服务费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(44,'','038','ค่าทำเนียมซอฟต์แวร์','Software fee（PS/CC/AI)','软件费（PS/CC/AI)',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(43,'','038','ระบบซอฟต์แวร์สำนักงาน','Microsoft Office 365 SNGL License','办公软件',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:13:59',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(42,'','038','ระบบซอฟต์แวร์การเงิน','Finance Department Express Software','财务软件系统',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(41,'','038','ศูนย์บริการระบบ','Call center system','呼叫中心系统',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(40,'','038','ค่าธรรมเนียมใบอนุญาตซอฟต์แวร์','Software license fee','软件许可费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(39,'','020','ค่าธรรมเนียมระหว่างเกาะ','Cross Island Fee','跨岛费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(38,'','019','ค่าน้ำมัน','Freight-Fuel','油费',0,1,3,0,0,'2020-12-31 17:07:26','2021-01-15 06:06:43',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(37,'','017','ค่าอินเตอร์เน็ต','Network','网络费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(36,'','017','ค่าลงทะเบียนสาขา','Branches Registration fee','网点注册费',0,1,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:12:13',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(35,'','017','ค่าซ่อมแซม','Maintenance & Repairs','维修保养费',0,1,0,59948,59948,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(34,'','017','ค่าจอดรถ','Parking','车位费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(33,'','017','เครื่องดื่ม','Drinking Water','饮用水',0,1,4,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(32,'','017','ค่ารักษาความปลอดภัย','Security','保安费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(31,'','017','ค่าปริ้นท์','Printing','打印费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(30,'','017','ค่าขนส่ง','Express','快递费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(29,'','017','ค่าทำเนียมทรัพย์สิน','Property Fee','物业费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(28,'','017','ค่าทำความสะอาด','Cleaning','保洁费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(27,'','014','ค่าทางด่วน','Toll Fee','过路费',0,1,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:13:19',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(26,'','014','ค่าเดินทางภายในพื้นที่','Local transportation','本地交通费',0,1,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(25,'','005','ภาษีป้ายทะเบียน','Vehicle License Plate Tax','车牌税',0,1,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:15:10',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(24,'','005','ภาษีที่ดิน','Land tax','土地税',0,1,0,0,0,'2020-12-31 17:07:26','2021-03-04 08:29:32',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(23,'','005','ภาษีการจ้างงาน','Employment tax','雇佣税',0,1,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:15:10',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(22,'','005','ภาษีธุรกิจ','Business Tax','营业税',0,1,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:15:10',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(21,'','005','ภาษีป้าย','Billboard Tax','广告牌税',0,1,0,68212,68212,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(20,'','005','อากรแสตมป์','Duty Stamp','印花税',0,1,0,0,0,'2020-12-31 17:07:26','2021-03-04 08:29:32',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(19,'','002','ค่าน้ำมัน','Fuel Cost','油费',0,1,3,0,0,'2020-12-31 17:07:26','2021-01-15 06:07:39',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(18,'','001','ค่าต้อนรับ','Entertaining','招待费',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(17,'','001','วีซ่าทำงาน','Work Visa','工作签',0,1,1,0,0,'2020-12-31 17:07:26','2021-01-15 06:11:22',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(16,'','001','ค่าตรวจร่างกาย','Medical examination fee','体检费',0,1,1,0,0,'2020-12-31 17:07:26','2021-01-14 12:33:31',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(15,'','001','ค่าที่พัก','Lodging','住宿费',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(14,'','001','ค่ารถแท็กซี่','Taxi','出租车费',0,1,1,0,0,'2020-12-31 17:07:26','2021-01-14 12:32:22',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(12,'','001','ค่าเช่ารถ','Car Rent','租车费',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(11,'','001','ค่าทางด่วน','Toll Fee','过路费',0,1,1,0,0,'2020-12-31 17:07:26','2021-01-15 06:11:04',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(10,'','001','ตั๋วรถโดยสาร','Bus','汽车票',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(9,'','001','ตั๋วรถไฟ','Train','火车票',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(8,'','001','ตั๋วเครื่องระหว่างประเทศ','Overseas Airfare','海外机票',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(7,'','001','ตั๋วเครื่องบินภายในประเทศ','Domestic Airfare','国内机票',0,1,1,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(6,'','020','ค่าโหลดสินค้าขึ้นลงรถของจัดซื้อ','Handling fees for purchased products','采购产品装卸费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(5,'','020','ค่าขนส่งสินค้าของจัดซื้อ','Purchase product transportation fee','采购产品运输费',0,2,0,59948,59948,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(4,'','017','พัสดุสิ้นเปลือง','Low-value Consumables','低值易耗品',0,2,0,0,0,'2020-12-31 17:07:26','2021-01-15 06:11:59',1);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(3,'','017','ค่าจอดรถ','Parking','车位费',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(2,'','017','เครื่องดื่ม','Drinking Water','饮用水',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);
insert into `budget_object_product`(`id`,`pno`,`object_code`,`name_th`,`name_en`,`name_cn`,`category_id`,`type`,`template_type`,`operate_id`,`update_id`,`created_at`,`updated_at`,`is_delete`)
values(1,'','017','ของใช้สำนักงาน','Office Supplies','办公用品',0,2,0,0,0,'2020-12-31 17:07:26','2020-12-31 17:07:26',0);

-- 添加vat税率
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('ordinary_payment_vat7_rate', '0,10', 'vat7税率', '2021-04-21 07:18:10', '2021-04-21 07:20:14');

-- 预算开关
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('budget_status', '0', '预算开关', '2021-06-09 02:29:50', '2021-06-09 02:39:23');

-- WHT类别和对应汇率
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('payment_wht_tax', '{\"3\":{\"name\":\"\/\",\"tax\":[0]}}', 'WHT类别和对应汇率', '2021-05-19 14:31:57', '2021-05-19 14:31:57');

-- 支付人工号
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ( 'purchase_payment_pay_staff_id', '76260,65382', '采购付款申请单付款人', '2020-12-10 07:42:05', '2020-12-10 07:53:27');

-- 更新采购申请单节点审核人
update `workflow_node` set `auditor_id`='60712' where `id`=312;
update `workflow_node` set `auditor_id`='52731' where `id`=313;
update `workflow_node` set `auditor_id`='' where `id`=314;
update `workflow_node` set `auditor_id`='17152' where `id`=315;
update `workflow_node` set `auditor_id`='' where `id`=720;
-- 更新采购申请单节点关系
update `workflow_node_relate` set `valuate_formula`='$p1<15000000000',`valuate_code`='getAmount' where `id`=455;
update `workflow_node_relate` set `valuate_formula`='$p1>=15000000000',`valuate_code`='getAmount' where `id`=456;
update `workflow_node_relate` set `valuate_formula`='$p1<***********',`valuate_code`='getAmount' where `id`=457;
update `workflow_node_relate` set `valuate_formula`='$p1>=***********',`valuate_code`='getAmount' where `id`=458;
update `workflow_node_relate` set `valuate_formula`='$p1<***********',`valuate_code`='getAmount' where `id`=459;
update `workflow_node_relate` set `valuate_formula`='$p1>=***********',`valuate_code`='getAmount' where `id`=460;
update `workflow_node_relate` set `valuate_formula`='$p1<***********',`valuate_code`='getAmount' where `id`=463;
update `workflow_node_relate` set `valuate_formula`='$p1>=***********',`valuate_code`='getAmount' where `id`=464;
update `workflow_node_relate` set `valuate_formula`='$p1<***********',`valuate_code`='getAmount' where `id`=465;
update `workflow_node_relate` set `valuate_formula`='$p1>=***********',`valuate_code`='getAmount' where `id`=466;

-- 更新采购订单、采购付款单审批人
update `workflow_node` set `auditor_id`='60712' where `id`=368;
update `workflow_node` set `auditor_id`='52731' where `id`=369;
update `workflow_node` set `auditor_id`='',`name`='指定工号（AP老挝）' where `id`=370;
update `workflow_node` set `auditor_id`='',`name`='指定工号（APS老挝）' where `id`=371;
update `workflow_node` set `auditor_id`='' where `id`=372;
update `workflow_node` set `auditor_id`='' where `id`=373;
update `workflow_node` set `auditor_id`='' where `id`=374;
update `workflow_node` set `auditor_id`='76772' where `id`=375;
update `workflow_node` set `auditor_id`='17152' where `id`=376;
update `workflow_node` set `auditor_id`='17008' where `id`=377;
-- 采购订单关系
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<15000000000', `valuate_code` = 'getAmount' WHERE `id` = 553;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<***********', `valuate_code` = 'getAmount' WHERE `id` = 555;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1<***********', `valuate_code` = 'getAmount' WHERE `id` = 557;

-- 更新采购订单、采购付款单审批人（Flash Fullfillment、Flash Laos）
update `workflow_node` set `auditor_id`='60712' where `id`=410;
update `workflow_node` set `auditor_id`='52731' where `id`=411;
update `workflow_node` set `auditor_id`='',`name`='指定工号（AP老挝）' where `id`=412;
update `workflow_node` set `auditor_id`='',`name`='指定工号（APS老挝）' where `id`=413;
update `workflow_node` set `auditor_id`='' where `id`=414;
update `workflow_node` set `auditor_id`='' where `id`=415;
update `workflow_node` set `auditor_id`='' where `id`=416;
update `workflow_node` set `auditor_id`='76772' where `id`=417;
update `workflow_node` set `auditor_id`='17152' where `id`=418;
update `workflow_node` set `auditor_id`='17008' where `id`=419;
-- 采购订单、采购付款单关系（Flash Fullfillment、Flash Laos）
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1>=15000000000', `valuate_code` = 'getAmount' WHERE `id` = 608;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1>=***********', `valuate_code` = 'getAmount' WHERE `id` = 610;
UPDATE `workflow_node_relate` SET `valuate_formula` = '$p1>=***********', `valuate_code` = 'getAmount' WHERE `id` = 612;

-- 供应商
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(94,'CNS-0001','杭州银弹科技有限公司001','',1,2,1,'91330108MA2B0YA74X','','','杭州市滨江区六和路307号F裙楼413室','夏鹏','***********','<EMAIL>',20,'***************','','S02009','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:48:58',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(95,'CNS-0002','广州九恒条码有限公司','',1,2,1,'914401137435971000','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S00748','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:49:35',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(96,'CNS-0003','江华九恒数码科技有限公司','',1,2,1,'***************','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S01271','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:50:15',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(97,'CNS-0004','江华恒津包装材料有限公司','',1,2,1,'***************','','','广州市番禺区石楼镇灵兴工业区7-8号','张芳','***********','<EMAIL>',18,'************','','S00749','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:50:46',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(98,'CNS-0005','常州宏力称重设备制造有限公司','',1,1,3,'320302197406201212','','','江苏省常州市武进高新区龙惠路21号','张孝奉','***********','<EMAIL>',19,'6230520410035755973','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:51:40',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(99,'CNS-0006','桐庐县横村镇欣和箱包厂','',1,2,1,'913301226970878000','','','浙江省杭州市桐庐县利时凌江名庭1411','潘超琦','134 2969 3687','<EMAIL>',30,'***************','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 13:58:52',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(100,'CNS-0007','启东荻捷工业成套设备有限公司','',1,2,1,'91320681591153462W','','','启东市滨海工业园区明珠路47号','杨挺','***********','<EMAIL>',0,'','','S00747','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29',null,0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(101,'CNS-0008','南京蜂之云信息科技有限公司','',1,2,1,'91320116MA1N4FXH12','','','南京市鼓楼区幕府东路199号A28栋','周国飞','+86 ***********','<EMAIL>',20,'***************','','S00750','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:32:26',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(102,'CNS-0009','山东安固锁业有限公司','',1,2,1,'91371623328513547J','','','山东省无棣县信阳镇闫家花园村','闫新金','***********','<EMAIL>',21,'37050183820800000508','','S00751','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:32:04',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(103,'CNS-0010','天津百丰网络科技有限公司','',1,2,1,'230103198505042411','','','天津市东丽区华明街道北于堡村北区东街4号办公楼','王秀峰','***********','<EMAIL>',22,'441320100100045502','','S00752','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:31:41',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(104,'CNS-0011','滨州鼎岳化纤绳网有限公司','',1,2,1,'91371621MA3M85PU46','','','山东省滨州市惠民县李庄镇工业园','霍光玲','***********','<EMAIL>',0,'','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29',null,0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(105,'CNS-0012','珠海市宝森科技有限公司','',1,2,1,'914404008684021G','','','珠海','Andy Xu','***********','<EMAIL>',21,'44014182900220500239','','S00744','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:29','2020-01-13 14:30:00',0,3,'2020-01-13 03:06:29',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(106,'CNS-0013','中科微至智能制造科技江苏有限公司','',1,2,1,'91320214MA1MLB3M2A','','','无锡市锡山区大成路299号','柯丽','***********','<EMAIL>',0,'','','S00753','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30',null,0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(107,'CNS-0014','东莞市宝涵国际物流有限公司','',1,2,1,'914419003348121000','','','东莞市常平镇袁山贝村康泰路康宁二街2号','吴炳兴','***********','<EMAIL>',17,'2010025909200400296','','S00754','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:22:43',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(108,'CNS-0015','唐山市志同网络科技有限公司','',1,2,1,'91130283MA0CFJW3XC','','','河北省迁安市黄台湖畔阜安大路2798号集森创业孵化基地8709室','王树全（代理商）','***********','<EMAIL>',18,'************','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:22:14',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(109,'CNS-0016','义乌走召电子商务商行','',1,2,1,'92330782MA2A8PHL1K','','','义乌国际商贸城H区22044','刘坚','***********/***********','<EMAIL>',19,'6228480389397994978','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:21:30',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(110,'CNS-0017','常熟市百联自动机械有限公司','',1,2,1,'913205815691513645','','','江苏省常熟市常福街道锦州路17号','王伟','***********','<EMAIL>',28,'101267114015064168','','S00755','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:21:03',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(111,'CNS-0018','山东民安锁业有限公司','',1,2,1,'372324199412071010','','','山东省滨州市无棣县信阳镇大邵村','王凯','0086 -***********','<EMAIL>',17,'6212261613008952672','','S00746','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:16:11',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(112,'CNS-0019','个人（王珈佳）','',1,1,4,'100-********','','','北京市东城区永定门外大街128号万朋商城118号','王珈佳','***********','********＠qq．com',17,'6212250200004838403','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:13:24',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(113,'CNS-0020','杭州依纯办公用品有限公司','',1,2,1,'91330110MA27W1DT4B','','','杭州市余杭区乔司街道朝阳工业园区红普北路9号6楼','许嫣','***********','<EMAIL>',29,'3302070120100018482','','','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:58',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(114,'CNS-0021','上海万琛电子商务有限公司','',1,2,1,'91310118599791063M','','','上海市青浦沪清平公路3938号移动智地19号楼','徐艳玲（商务）','***********','<EMAIL>',21,'31050183440009730091','','S00756','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:38',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(115,'CNS-0022','精源粲（厦门）物流设备有限公司','',1,2,1,'91350202MA33610k46','','','厦门市同安区工业集中区思明园38号2楼','刘萍','***********','<EMAIL>',19,'6228480078018853479','','S00757','test.txt@fle-staging-asset-internal@workOrder/oca-**********-8ad16bb454294bb8a0d654f9384eadc8.txt',3,0,'','2020-01-13 03:06:30','2020-01-13 14:12:25',0,3,'2020-01-13 03:06:30',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(150,'CNS-0023','Guangzhou Jiuheng  Bar  Code Co. Ltd','',1,2,2,'91440 11374 35971 91','','','7-8# LingXing industry Park, Panyu Area, Guangzhou City, Guangdong Province, China','nicole','***********','<EMAIL>',16,'************','','S00748','WeChat Image_20210422220037.jpg@fle-asset-internal@workOrder/oca-**********-59c70914a3984b5c9f523b7c0cf4a532.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-22 22:01:06',null,0,3,'2021-04-22 22:01:06',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(163,'CNS-0024','Shandong Angu Lock Company Limited','',1,2,2,'91371623328513547J','','','Yanjia Garden, Xinyang Village, Wudi Country,  Binzhou, Shandong, China 251900','Khun Angu','***********','<EMAIL>',21,'37050183820800000508','','S00751','WeChat Photo Editor_20210424181257.jpg@fle-asset-internal@workOrder/oca-**********-d3bfa59e252242ae8aa901a5c75fd14d.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-24 18:15:02',null,0,3,'2021-04-24 18:15:02',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(164,'CNS-0025','Zhuhai Bosam Technology Company Limited','',1,2,2,'*****************','','','8 Gangle Rd., High-Tech Zone, Zhuhai, Guangdong, China 519000','Andy Xu','***********','<EMAIL>',21,'4401 4182 9002 2050 0239','','S01958','WeChat Image_20210425105039.jpg@fle-asset-internal@workOrder/oca-**********-ad472814cb3942769cb28c270ab48c01.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-25 16:36:20',null,0,3,'2021-04-25 16:36:20',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(165,'CNS-0026','BEIJING I-GOODIDEA TRADING CO.,LTD','',1,2,2,'911101023484320740','','','ROOM 189 , 1ST FLOOR , BLOCK B , NO.333 DAYANG ROAD ,  CHAOYANG DISTRICT , BEIJING , P.R CHINA','Chen Xue Ting','************','<EMAIL>',20,'***************','','S01740','WeChat Image_20210425104955.jpg@fle-asset-internal@workOrder/oca-**********-0fb87db2c5d44d498b97c42e6c5caf33.jpg',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-04-25 16:49:25','2021-06-02 15:21:28',0,3,'2021-04-25 16:49:25',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(169,'CNS-0027','Donnelley Financial Solutions Hong Kong Limited','',1,2,1,'0','','','20-21/F, Wheelock House 20 Pedder Street Central, Hong Kong','Mr.Don Zheng','+852 9018 7609','<EMAIL>',27,'0','','S01865','Donnelley Proposal for Flash Group.pdf@fle-asset-internal@workOrder/oca-**********-009d155b70534d4aa08d1491908078dc.pdf',3,66293,'นางสาว ชญาดา สุนทรวุฒินันท์','2021-04-27 10:08:01',null,0,3,'2021-04-27 10:08:01',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(199,'CNS-0029','广东君成印务有限公司','http://www.jcyw.ltd（中）/http://www.gdjcyw.ltd/（英）',1,1,3,'522121197305061414','刘洋','0769-********','东莞市虎门镇怀德社区荔园路27号','刘霁萱','***********','<EMAIL>',17,'2010 1224 1910 0109 969','','','君成营业执照.jpg@fle-asset-internal@workOrder/oca-**********-dcfeb907bb964a90a7576139d1d380c4.jpg,君成印刷许可.jpg@fle-asset-internal@workOrder/oca-**********-f7ae368ed5174c3d8712fc78c5df88ac.jpg',3,65714,'张琦 zhangqi','2021-05-08 13:10:32','2021-05-12 16:58:41',0,3,'2021-05-08 13:10:32',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(210,'CNS-0031','QIDONG YIDATONG AUTOMATION EQUIPMENT CO.,LTD','',1,2,2,'91320681573825042M','','','启东市汇龙镇克明村木场路','王馨','***********','<EMAIL>',33,'1030 1427 4212 7','','S01025','亦大通最新营业执照(1).pdf@fle-asset-internal@workOrder/oca-**********-5723f0e91f24440ab51a183fff7232cb.pdf',3,60712,'白亚男 Bai Yanan','2021-05-14 10:29:14',null,0,3,'2021-05-14 10:29:14',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(211,'CNS-0032','Suzhou Shuangqi (Pairs Kee) Automation Equipment CO., LTD','',1,2,2,'91320506570312188C','','','苏州市吴中区临湖镇许家港路288号','欧阳惠','***********','<EMAIL>',33,'1037 0200 2448 6','','S01270','盖章的营业执照.pdf@fle-asset-internal@workOrder/oca-**********-394b04cae0474d58bd7ce25f2690c051.pdf',3,60712,'白亚男 Bai Yanan','2021-05-14 10:37:45',null,0,3,'2021-05-14 10:37:45',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(215,'CNS-0033','Guangdong Juncheng Printing Co., Ltd','http://www.jcyw.ltd（中）/http://www.gdjcyw.ltd/（英）',1,2,1,'91441900MA4UMCJ64D','刘洋','0769-********','东莞市虎门镇怀德社区荔园路27号','刘霁萱','***********','<EMAIL>',23,'4830  0761  2141  0000  03872','','1000285','营业执照.jpg@fle-asset-internal@workOrder/oca-**********-4946fa0c13d24de693d334da1b72578e.jpg,经营许可.jpg@fle-asset-internal@workOrder/oca-**********-6e16935a419f406ab61eea6bf75802bb.jpg',3,65714,'张琦 zhangqi','2021-05-18 09:28:41',null,0,3,'2021-05-18 09:28:41',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(220,'CNS-0034','Alibaba Cloud (Singapore) Private Limited-ali saas','',1,2,1,'M90371710Y','','','8 Shenton Way, #45-01 AXA Tower, Singapore 068811','Zhong Cheng','***********','<EMAIL>',33,'ACC NO. ***********','','1000260','SIGM1-*************.pdf@fle-asset-internal@workOrder/oca-**********-ad55fe16a3e64e1db16e46bc6fbbc5f8.pdf',3,66293,'นางสาว ชญาดา สุนทรวุฒินันท์','2021-05-19 15:07:52',null,0,3,'2021-05-19 15:07:52',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(226,'CNS-0035','SHANGHAI XIETONG (GROUP) CO.,LTD','',1,2,2,'913100001322253910','','','上海市嘉定区曹安路4671号','邹小姐','***********','<EMAIL>',27,'7313811482600000642','','1000303','营业执照.jpg@fle-asset-internal@workOrder/oca-**********-4f07488cf0a747bfa2effb559bef62bb.jpg',3,60712,'白亚男 Bai Yanan','2021-05-24 19:31:51','2021-05-24 19:41:53',0,3,'2021-05-24 19:31:51',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(229,'CNS-0036','Wayz Intelligent Manufacturing Technology Co. Ltd','',1,2,2,'91320214MA1MLB3M2A','李功燕','0510-********','No.299 Dacheng Road, Xishan District, Wuxi City, China','吴洁','***********','<EMAIL>',23,'322000633141000002944','','S00753','002-中科微至-营业执照-副本-经营范围新增租赁物业管理-********.pdf@fle-asset-internal@workOrder/oca-**********-b2474808629e49c1b53c9734ac902058.pdf',3,60712,'白亚男 Bai Yanan','2021-05-26 20:01:07',null,0,3,'2021-05-26 20:01:07',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(241,'CNS-0038','杭州银弹科技有限公司','',1,2,2,'91330108MA2B0YA74X','覃健祥','0571-********','浙江省杭州市滨江区浦沿街道六和路307号1幢4层413室','孙喜儿','0571-********','<EMAIL>',20,'***************','招商银行杭州分行滨江支行','S02009','银弹营业执照.pdf@fle-asset-internal@workOrder/oca-**********-e403787aa1f2468a80c85fc30546a4ce.pdf',3,60712,'白亚男 Bai Yanan','2021-06-08 15:39:15',null,0,3,'2021-06-08 15:39:15',0,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(273,'CNS-**********','GENYE TECHNOLOGY COMPANY LIMITED','',1,2,2,'91440113304782005P','','','广州市番禺区南村镇汉溪大道东390号四海城商业广场3栋1301','黄文申','***********','<EMAIL>',17,'3602026409200154691','GENYE TECHNOLOGY COMPANY LIMITED','','',3,60712,'白亚男 Bai Yanan','2021-07-08 10:28:10','2021-07-13 17:32:00',54677,3,'2021-07-13 17:32:00',54677,0,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(280,'CNS-**********','Hangzhou Weici Technology Co.,Ltd','',1,2,2,'91330108MA2H18RD18','','','Financial Square, Tonglu County, Hangzhou City, Zhejiang Province, China','Elaine Deng','***********','<EMAIL>',19,'*****************','Hangzhou Weici Technology Co., Ltd','','',3,60712,'白亚男 Bai Yanan','2021-07-09 09:23:14','2021-07-13 13:48:01',54677,3,'2021-07-13 13:48:01',54677,1,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(317,'CNS-**********','Shandong Bozheng Ruidi export and Import Co., Ltd','',1,2,2,'91371300MA94CW6X8M','Zengyuan Su','***********','No. 05085, Unit A, Building 2, Yihe Third Road, Linyi Comprehensive Free Trade Zone','Zengyuan Su','***********','<EMAIL>',18,'161001061920068341',' Industrial and Commercial Bank of China Linyi Economic and Technological Development Zone Subbranch','','',2,65714,'张琦 zhangqi','2021-07-22 12:48:48','2021-07-22 17:32:45',54249,3,'2021-07-22 17:32:45',54249,1,0,0);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(323,'CNS-0037','SMART EDGE TECHNOLOGIES PTE. LTD.','',1,2,2,'201226209M','','',"86 Marine Parade Road, #02-20 Cote D'azur Singapore 449301",'Mr.Tao','+*************','<EMAIL>',1,'************','Smart Edge Technologies Pte. Ltd.','1000317','',3,59898,'นางสาว ณัฏฐา เงินวิวัฒน์กุล','2021-06-02 11:49:37','2021-07-23 09:19:10',54677,3,'2021-07-23 09:19:10',54677,0,2,2);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(357,'CNS-**********','JIANGSU  LOGISTICS EQUIPMENT AUTOMATICCONTROL TECHNOLOGY CO.,LTD.','',1,2,2,'91320412MA1Q0RMP0U','','','No. 198, Datong West Road, Niutang Town, Wujin District, Changzhou City','Kuang Xiangyang','***********','<EMAIL>',18,'************','JIANGSU  LOGISTICS EQUIPMENT AUTOMATICCONTROL TECHNOLOGY CO.,LTD.','1000336','',3,60712,'白亚男 Bai Yanan','2021-07-22 10:21:43','2021-07-27 08:49:26',54677,3,'2021-07-27 08:49:26',54677,0,2,2);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(415,'CNS-**********','Shandong Bozheng Ruidi Import and Export Co., Ltd','',1,2,2,'91371300MA94CW6X8M','','','No. 05085, Unit A, Building 2, Yihe Third Road, Linyi Comprehensive Free Trade Zone','Zengyuan Su','***********','<EMAIL>',17,'161001061920068341','Shandong Bozheng Ruidi Import and Export Co., Ltd ','1000339','',3,65714,'张琦 zhangqi','2021-07-14 18:08:30','2021-07-29 09:24:23',54677,3,'2021-07-29 09:24:23',54677,1,1,1);
insert into `vendor`(`id`,`vendor_id`,`vendor_name`,`company_website`,`ownership`,`company_nature`,`certificate_type`,`identification_no`,`artificial_person`,`company_phone`,`company_address`,`contact`,`contact_phone`,`contact_email`,`bank_code`,`bank_no`,`bank_account_name`,`sap_supplier_no`,`attachment`,`status`,`create_id`,`create_name`,`created_at`,`updated_at`,`updated_id`,`audit_stage`,`last_audit_time`,`last_auditor_id`,`audit_rejected_count`,`audit_approved_count`,`approval_version_no`)
values(422,'CNS-0028','Foshan Shunde Junye Furniture Development Company Limited','n/a',1,2,1,'11111','','***********','Huaxi Industrial Development Zone, Longjiang Town, Shunde Dist., Foshan, Guangdong , 528300 China','Khun Guangyu Feng ( 冯广裕 )','***********','<EMAIL>',33,'*********','Foshan Wuyouchuhai Technology  Company Limited','1000271','',3,19957,'นาย ตาราห์ รุนจำรัส','2021-04-28 10:29:43','2021-07-30 18:20:03',54677,3,'2021-07-30 18:20:03',54677,0,2,2);

-- 供应商模块关联
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(466,'CNS-0028',1,'2021-07-29 03:37:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(456,'CNS-**********',1,'2021-07-28 02:42:05');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(382,'CNS-**********',1,'2021-07-23 10:43:25');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(342,'CNS-0037',2,'2021-07-22 06:48:27');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(341,'CNS-0037',1,'2021-07-22 06:48:27');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(334,'CNS-**********',1,'2021-07-22 05:48:48');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(281,'CNS-**********',1,'2021-07-12 02:10:37');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(274,'CNS-**********',1,'2021-07-08 03:28:10');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(229,'CNS-0036',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(226,'CNS-0035',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(220,'CNS-0034',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(215,'CNS-0033',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(211,'CNS-0032',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(210,'CNS-0031',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(199,'CNS-0029',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(169,'CNS-0027',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(165,'CNS-0026',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(164,'CNS-0025',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(163,'CNS-0024',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(150,'CNS-0023',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(115,'CNS-0022',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(114,'CNS-0021',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(113,'CNS-0020',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(112,'CNS-0019',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(111,'CNS-0018',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(110,'CNS-0017',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(109,'CNS-0016',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(108,'CNS-0015',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(107,'CNS-0014',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(106,'CNS-0013',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(105,'CNS-0012',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(104,'CNS-0011',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(103,'CNS-0010',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(102,'CNS-0009',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(101,'CNS-0008',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(100,'CNS-0007',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(99,'CNS-0006',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(98,'CNS-0005',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(97,'CNS-0004',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(96,'CNS-0003',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(95,'CNS-0002',1,'2021-06-17 15:38:28');
insert into `vendor_application_module_rel`(`id`,`vendor_id`,`application_module_id`,`create_time`)
values(94,'CNS-0001',1,'2021-06-17 15:38:28');
