-- 批量分配菜单权限
INSERT INTO `setting_env`(`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('oa_batch_permission_ids', '{\"loan\":[36,37,38,39,40,41,42,43,44,45,46,47,48]}', '批量添加OA指定菜单权限', '2021-08-04 12:31:07', '2021-08-04 12:31:07');

-- 支付人配置
INSERT INTO `setting_env`(`id`, `code`, `val`, `content`, `created_at`, `updated_at`) VALUES (16, 'loan_pay_staff_id', '76260,65382', '借款业务支付人', '2021-08-06 10:07:18', '2021-08-06 10:07:18');

-- 借款申请审批流配置
-- 节点审批人人变更
update workflow_node set name = '所属组织负责人', auditor_id = '' where id = 322 and flow_id = 31;
update workflow_node set name = 'Finance Manager', auditor_id = '62721' where id = 323 and flow_id = 31;
update workflow_node set name = 'Finance Senior Manager', auditor_id = '54677' where id = 324 and flow_id = 31;
update workflow_node set name = 'Finance Director', auditor_id = '76772' where id = 325 and flow_id = 31;
update workflow_node set name = 'CFO', auditor_id = '17152' where id = 326 and flow_id = 31;

-- 如下节点不需要，暂时置空
update workflow_node set name = 'CEO', auditor_id = '' where id = 327 and flow_id = 31;

-- 节点流转关系变更
update workflow_node_relate set valuate_formula = '$p1 < 2900000000', remark = '一级部门负责人->Finance Manager' where flow_id = 31 and id = 471;
update workflow_node_relate set valuate_formula = '', remark = '一级部门负责人->所属公司负责人' where flow_id = 31 and id = 472;
update workflow_node_relate set valuate_formula = '$p1 < 15000000000', remark = '所属公司负责人->Finance Manager' where flow_id = 31 and id = 473;
update workflow_node_relate set valuate_formula = '', remark = '所属公司负责人->所属组织负责人' where flow_id = 31 and id = 474;
update workflow_node_relate set valuate_formula = '', remark = '所属组织负责人->Finance Manager' where flow_id = 31 and id = 475;
update workflow_node_relate set valuate_formula = '', remark = 'Finance Manager->Finance Senior Manager' where flow_id = 31 and id = 476;
update workflow_node_relate set valuate_formula = '$p1 < 2900000000', remark = 'Finance Senior Manager->结束' where flow_id = 31 and id = 477;
update workflow_node_relate set valuate_formula = '', remark = 'Finance Senior Manager->Finance Director' where flow_id = 31 and id = 478;
update workflow_node_relate set valuate_formula = '$p1 < 15000000000', remark = 'Finance Director->结束' where flow_id = 31 and id = 479;
update workflow_node_relate set valuate_formula = '', remark = 'Finance Director->CFO' where flow_id = 31 and id = 480;
update workflow_node_relate set valuate_formula = '', remark = 'CFO->结束' where flow_id = 31 and id = 481;


-- 借款归还审批流配置
insert into `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`)
values(71,'借款归还',1,'借款归还_v9098','2021-06-10 16:52:04');

insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`)
values(942,71,'员工提交',1,0,null,null,null,null,1,null,null,'',null);

insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`)
values(943,71,'直接上级',3,0,null,null,null,null,3,null,null,'',null);

insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`)
values(945,71,'财务',3,0,null,null,null,null,1,'76772,62721',null,'',null);

insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`)
values(946,71,'结束',6,0,null,null,null,null,1,null,null,'',null);

insert into `workflow_node_relate`(`flow_id`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`updated_at`,`created_at`)
values(71,945,946,null,null,'借款财务(58292,32739)->结束',10,'2021-06-07 09:53:09','2021-06-07 09:53:09');

insert into `workflow_node_relate`(`flow_id`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`updated_at`,`created_at`)
values(71,943,945,null,null,'直接上级->借款财务(58292,32739)',10,'2021-06-07 09:53:09','2021-06-07 09:53:09');

insert into `workflow_node_relate`(`flow_id`,`from_node_id`,`to_node_id`,`valuate_formula`,`valuate_code`,`remark`,`sort`,`updated_at`,`created_at`)
values(71,942,943,null,null,'员工提交->直接上级',10,'2021-06-07 09:53:09','2021-06-07 09:53:09');

ALTER TABLE `staff_permission`
DROP INDEX `idx_staff_id`,
ADD UNIQUE INDEX `idx_staff_id`(`staff_id`) USING HASH;