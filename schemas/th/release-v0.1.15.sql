-- 更新字段编辑权限
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 617;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 618;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 619;
-- 更新AP （泰国）和AP Supervisor（泰国）字段编辑权限
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 636;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 637;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 638;
-- 更新AP （泰国）和AP Supervisor（泰国）字段编辑权限
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 656;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 657;
UPDATE workflow_node SET can_edit_field = '{\"amount_detail\":[\"wht_category\",\"wht_rate\"]}' WHERE id = 658;


insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`) values

(960,70,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(961,70,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(962,70,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(963,70,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(964,70,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(965,70,'coo/cpo',3,0,null,null,null,null,7,'17245,56780',null,'',null),
(966,70,'中文审批',3,0,null,null,null,null,1,'57335',null,'',null),
(967,70,'英文审批',3,0,null,null,null,null,1,'56883',null,'',null),
(968,70,'泰语审批',3,0,null,null,null,null,1,'58040',null,'',null),
(969,70,'中文、英文审批',3,0,null,null,null,null,1,'57335',null,'',null),
(970,70,'泰语审批',3,0,null,null,null,null,1,'58040',null,'',null),
(971,70,'语言总审核',3,0,null,null,null,null,1,'57335',null,'',null),
(972,70,'结束',6,0,null,null,null,null,1,null,null,'',null),
(974, 70, 'AP Supervisor(泰国)', 3, 0, NULL, NULL, NULL, NULL, 1, '33306,17178', NULL, '', NULL),
(975, 70, 'AP Supervisor(北京)', 3, 0, NULL, NULL, NULL, '4', 1, '64377,57625,61682,62721,62306,66553,66979,62358,64234,55849,70080,73569', NULL, '', NULL);


INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 960, 961, NULL, NULL, '员工提交->直线上级', 10, '2020-11-06 06:12:51', '2020-11-06 06:12:37');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 961, 962, NULL, NULL, '直接上级->二级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 963, '$p1==25 || $p1== 18', 'getSubmitterDepartment', 'hub或者flashhome部门  二级部门负责人->一级部门负责人', 10, '2020-11-06 06:16:25', '2020-11-06 06:15:50');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 966, '($p1==2 &&$p2<200000) && $p3==3', 'getContractType,getTHB,getLang', '(合同总金额<200,000) && 中文 二级部门负责人->法务部门1中文', 10, '2020-11-11 07:35:43', '2020-11-06 06:18:00');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 967, '($p1==2 &&$p2<200000) && $p3==2', 'getContractType,getTHB,getLang', '(合同总金额<200,000) && 英文 二级部门负责人->法务部门1英文', 10, '2020-11-11 07:35:45', '2020-11-06 06:21:47');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 968, '($p1==2 &&$p2<200000) && $p3==1', 'getContractType,getTHB,getLang', '(合同总金额<200,000) && 泰文 二级部门负责人->法务部门1泰文', 10, '2020-11-11 07:35:47', '2020-11-06 06:22:28');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 963, NULL, NULL, '（上面都不满足）  二级部门负责人->一级部门负责人', 10, '2020-11-06 06:23:55', '2020-11-06 06:23:21');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 963, 966, '($p1==2 &&$p2<600000) && $p3==3', 'getContractType,getTHB,getLang', '(合同总金额<600,000) && 中文 一级部门负责人->法务部门1中文', 10, '2020-11-11 07:35:52', '2020-11-06 06:48:18');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 963, 967, '($p1==2 &&$p2<600000) && $p3==2', 'getContractType,getTHB,getLang', '(合同总金额<600,000) && 英文 一级部门负责人->法务部门1英文', 10, '2020-11-11 07:35:55', '2020-11-06 06:52:23');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 963, 968, '($p1==2 &&$p2<600000) && $p3==1', 'getContractType,getTHB,getLang', '(合同总金额<600,000) && 泰文 一级部门负责人->法务部门1泰文', 10, '2020-11-11 07:35:58', '2020-11-06 06:55:29');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 963, 964, '', '', '一级部门负责人->所属公司负责人', 10, '2020-11-06 06:57:47', '2020-11-06 06:56:22');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 966, '($p1==2 &&$p2<2000000) && $p3==3', 'getContractType,getTHB,getLang', '(合同总金额<2,000,000) && 中文 所属公司负责人->法务部门1中文', 10, '2020-11-11 07:36:01', '2020-11-06 06:56:48');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 967, '($p1==2 &&$p2<2000000) && $p3==2', 'getContractType,getTHB,getLang', '(合同总金额<2,000,000) && 英文 所属公司负责人->法务部门1英文', 10, '2020-11-11 07:36:03', '2020-11-06 06:59:07');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 968, '($p1==2 &&$p2<2000000) && $p3==1', 'getContractType,getTHB,getLang', '(合同总金额<2,000,000) && 泰文 所属公司负责人->法务部门1泰文', 10, '2020-11-11 07:36:05', '2020-11-06 06:59:50');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 965, NULL, NULL, '所属公司负责人->COO/CPO', 10, '2020-11-06 07:00:55', '2020-11-06 07:00:40');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 966, '$p1==3', 'getLang', 'COO/CPO->法务部门1中文', 10, '2020-11-11 07:36:10', '2020-11-06 07:01:10');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 967, '$p1==2', 'getLang', 'COO/CPO->法务部门1英文', 10, '2020-11-11 07:36:12', '2020-11-06 07:06:13');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 968, '$p1==1', 'getLang', 'COO/CPO->法务部门1泰文', 10, '2020-11-11 07:36:18', '2020-11-06 07:06:48');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 966, 969, NULL, NULL, '法务部门1中文->法务部门2 中英文', 10, '2020-11-06 07:08:15', '2020-11-06 07:07:55');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 967, 969, NULL, NULL, '法务部门1英文->法务部门2 中英文', 10, '2020-11-06 07:08:44', '2020-11-06 07:08:31');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 968, 970, NULL, NULL, '法务部门1泰文->法务部门2 泰文', 10, '2020-11-06 07:09:32', '2020-11-06 07:08:53');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 969, 971, NULL, NULL, '法务部门2 中英文->语言总审核', 10, '2020-11-06 07:10:08', '2020-11-06 07:09:49');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 970, 971, NULL, NULL, '法务部门2 泰文->语言总审核', 10, '2020-11-06 07:10:30', '2020-11-06 07:10:18');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 971, 974, NULL, NULL, '语言总审核->APS（泰国）', 10, '2020-11-06 07:19:21', '2020-11-06 07:19:14');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 974, 975, NULL, NULL, 'APS(泰国)->APS(北京)', 10, '2020-11-06 07:19:21', '2020-11-06 07:19:14');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 975, 972, NULL, NULL, 'APS(北京)->结束', 10, '2020-11-06 07:19:21', '2020-11-06 07:19:14');
