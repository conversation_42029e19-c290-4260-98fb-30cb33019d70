-- v9428网点备用金申请审批流
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (73,'网点备用金申请审批-hub部门',51,'网点备用金申请审批-hub部门','2021-07-15 16:51:57');
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (74,'网点备用金申请审批-shop部门',51,'网点备用金申请审批-shop部门','2021-07-15 16:51:57');
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (75,'网点备用金申请审批-network部门',51,'网点备用金申请审批-network部门','2021-07-15 16:51:57');

-- hub部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1011, 73, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1012, 73, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1013, 73, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1014, 73, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1015, 73, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1016, 73, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1017, 73, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1018, 73, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- shop部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1019, 74, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1020, 74, '直线上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1021, 74, 'Shop Area Manager', 3, 0, NULL, NULL, NULL, NULL, 17, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1022, 74, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1023, 74, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1024, 74, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1025, 74, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1026, 74, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1027, 74, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- network部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1029, 75, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1030, 75, '直线上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1031, 75, 'Area Manager', 3, 0, NULL, NULL, NULL, NULL, 17, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1032, 75, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1033, 75, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1034, 75, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1035, 75, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1036, 75, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1037, 75, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- 网点备用金返还审批流节点，保持和菲律宾一致
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1038, 47, 'Tax Management Supervisor', 3, 1, '20017259');
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1039, 48, 'Tax Management Supervisor', 3, 1, '20017259');
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1040, 49, 'Tax Management Supervisor', 3, 1, '20017259');


-- hub网点节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1011, 1012, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1012, 1013, NULL, NULL, '直接上级 -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1013, 1014, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1014, 1015, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1015, 1016, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1016, 1017, NULL, NULL, 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1017, 1018, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');

-- shop网点节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1019, 1020, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1020, 1021, NULL, NULL, '直接上级 -> Shop Area Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1021, 1022, NULL, NULL, 'Shop Area Manager -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1022, 1023, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1023, 1024, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1024, 1025, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1025, 1027, '$p1<15000', 'getAmount', 'Finance Manager -> 结束', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1025, 1026, '$p1>=15000', 'getAmount', 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1026, 1027, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');

-- network部门节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1029, 1030, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1030, 1031, NULL, NULL, '直接上级 -> Area Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1031, 1032, NULL, NULL, 'Area Manager -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1032, 1033, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1033, 1034, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1034, 1035, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1035, 1036, NULL, NULL, 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1036, 1037, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');