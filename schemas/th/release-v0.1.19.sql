-- sap 处理purchase_order历史数据含vat 含pnd
UPDATE purchase_order set total_amount = subtotal_amount+taxation;

-- sap 更新sap供应商编号
UPDATE `vendor` SET `sap_supplier_no` = '1000319' WHERE  `vendor_id` = 'THS-2106290217';
UPDATE `vendor` SET `sap_supplier_no` = 'S01804' WHERE  `vendor_id` = 'THS-2106290215';
UPDATE `vendor` SET `sap_supplier_no` = 'S01400' WHERE  `vendor_id` = 'THS-2106280212';
UPDATE `vendor` SET `sap_supplier_no` = 'S00932' WHERE  `vendor_id` = 'CNS-2106280040';
UPDATE `vendor` SET `sap_supplier_no` = '1000245' WHERE  `vendor_id` = 'THS-2106210211';
UPDATE `vendor` SET `sap_supplier_no` = '1000306' WHERE  `vendor_id` = 'THS-2106180210';
UPDATE `vendor` SET `sap_supplier_no` = '1000315' WHERE  `vendor_id` = 'THS-2106180212';
UPDATE `vendor` SET `sap_supplier_no` = '1000316' WHERE  `vendor_id` = 'THS-2106180211';
UPDATE `vendor` SET `sap_supplier_no` = '1000305' WHERE  `vendor_id` = 'THS-0209';
UPDATE `vendor` SET `sap_supplier_no` = '1000307' WHERE  `vendor_id` = 'THS-0207';
UPDATE `vendor` SET `sap_supplier_no` = 'S01831' WHERE  `vendor_id` = 'THS-0206';
UPDATE `vendor` SET `sap_supplier_no` = '1000308' WHERE  `vendor_id` = 'THS-0205';
UPDATE `vendor` SET `sap_supplier_no` = 'S00913' WHERE  `vendor_id` = 'THS-0204';
UPDATE `vendor` SET `sap_supplier_no` = 'S02009' WHERE  `vendor_id` = 'CNS-0038';
UPDATE `vendor` SET `sap_supplier_no` = '1000234' WHERE  `vendor_id` = 'THS-0203';
UPDATE `vendor` SET `sap_supplier_no` = '1000199' WHERE  `vendor_id` = 'THS-0202';
UPDATE `vendor` SET `sap_supplier_no` = '1000320' WHERE  `vendor_id` = 'THS-0199';
UPDATE `vendor` SET `sap_supplier_no` = 'S01840' WHERE  `vendor_id` = 'THS-0198';
UPDATE `vendor` SET `sap_supplier_no` = 'S00816' WHERE  `vendor_id` = 'THS-0197';
UPDATE `vendor` SET `sap_supplier_no` = '1000317' WHERE  `vendor_id` = 'CNS-0037';
UPDATE `vendor` SET `sap_supplier_no` = '1000302' WHERE  `vendor_id` = 'THS-0196';
UPDATE `vendor` SET `sap_supplier_no` = '1000301' WHERE  `vendor_id` = 'THS-0195';
UPDATE `vendor` SET `sap_supplier_no` = '1000309' WHERE  `vendor_id` = 'THS-0194';
UPDATE `vendor` SET `sap_supplier_no` = 'S00753' WHERE  `vendor_id` = 'CNS-0036';
UPDATE `vendor` SET `sap_supplier_no` = '1000322' WHERE  `vendor_id` = 'THS-0192';
UPDATE `vendor` SET `sap_supplier_no` = '1000303' WHERE  `vendor_id` = 'CNS-0035';
UPDATE `vendor` SET `sap_supplier_no` = '1000286' WHERE  `vendor_id` = 'THS-0191';
UPDATE `vendor` SET `sap_supplier_no` = '1000287' WHERE  `vendor_id` = 'THS-0190';
UPDATE `vendor` SET `sap_supplier_no` = 'S02037' WHERE  `vendor_id` = 'THS-0189';
UPDATE `vendor` SET `sap_supplier_no` = '1000257' WHERE  `vendor_id` = 'THS-0188';
UPDATE `vendor` SET `sap_supplier_no` = 'S01869' WHERE  `vendor_id` = 'THS-0187';
UPDATE `vendor` SET `sap_supplier_no` = '1000260' WHERE  `vendor_id` = 'CNS-0034';
UPDATE `vendor` SET `sap_supplier_no` = '1000260' WHERE  `vendor_id` = 'THS-0186';
UPDATE `vendor` SET `sap_supplier_no` = 'S01844' WHERE  `vendor_id` = 'THS-0185';
UPDATE `vendor` SET `sap_supplier_no` = 'S01124' WHERE  `vendor_id` = 'THS-0184';
UPDATE `vendor` SET `sap_supplier_no` = '1000312' WHERE  `vendor_id` = 'THS-0183';
UPDATE `vendor` SET `sap_supplier_no` = '1000285' WHERE  `vendor_id` = 'CNS-0033';
UPDATE `vendor` SET `sap_supplier_no` = '1000313' WHERE  `vendor_id` = 'THS-0182';
UPDATE `vendor` SET `sap_supplier_no` = 'S02051' WHERE  `vendor_id` = 'THS-0181';
UPDATE `vendor` SET `sap_supplier_no` = '1000276' WHERE  `vendor_id` = 'THS-0180';
UPDATE `vendor` SET `sap_supplier_no` = 'S01270' WHERE  `vendor_id` = 'CNS-0032';
UPDATE `vendor` SET `sap_supplier_no` = 'S01025' WHERE  `vendor_id` = 'CNS-0031';
UPDATE `vendor` SET `sap_supplier_no` = 'S01025' WHERE  `vendor_id` = 'CNS-0030';
UPDATE `vendor` SET `sap_supplier_no` = 'S02046' WHERE  `vendor_id` = 'THS-0179';
UPDATE `vendor` SET `sap_supplier_no` = '1000277' WHERE  `vendor_id` = 'THS-0178';
UPDATE `vendor` SET `sap_supplier_no` = '1000283' WHERE  `vendor_id` = 'THS-0176';
UPDATE `vendor` SET `sap_supplier_no` = '1000275' WHERE  `vendor_id` = 'THS-0174';
UPDATE `vendor` SET `sap_supplier_no` = '1000217' WHERE  `vendor_id` = 'THS-0173';
UPDATE `vendor` SET `sap_supplier_no` = '1000310' WHERE  `vendor_id` = 'THS-0172';
UPDATE `vendor` SET `sap_supplier_no` = 'S02028' WHERE  `vendor_id` = 'THS-0171';
UPDATE `vendor` SET `sap_supplier_no` = '1000274' WHERE  `vendor_id` = 'THS-0170';
UPDATE `vendor` SET `sap_supplier_no` = 'S01966' WHERE  `vendor_id` = 'THS-0169';
UPDATE `vendor` SET `sap_supplier_no` = 'S01630' WHERE  `vendor_id` = 'THS-0168';
UPDATE `vendor` SET `sap_supplier_no` = 'S00823' WHERE  `vendor_id` = 'THS-0167';
UPDATE `vendor` SET `sap_supplier_no` = 'S01167' WHERE  `vendor_id` = 'THS-0166';
UPDATE `vendor` SET `sap_supplier_no` = 'S01843' WHERE  `vendor_id` = 'THS-0165';
UPDATE `vendor` SET `sap_supplier_no` = 'S01999' WHERE  `vendor_id` = 'THS-0164';
UPDATE `vendor` SET `sap_supplier_no` = 'S00986' WHERE  `vendor_id` = 'THS-0163';
UPDATE `vendor` SET `sap_supplier_no` = 'S00821' WHERE  `vendor_id` = 'THS-0162';
UPDATE `vendor` SET `sap_supplier_no` = '1000229' WHERE  `vendor_id` = 'THS-0161';
UPDATE `vendor` SET `sap_supplier_no` = '1000304' WHERE  `vendor_id` = 'THS-0160';
UPDATE `vendor` SET `sap_supplier_no` = 'S02255' WHERE  `vendor_id` = 'THS-0159';
UPDATE `vendor` SET `sap_supplier_no` = '1000295' WHERE  `vendor_id` = 'THS-0158';
UPDATE `vendor` SET `sap_supplier_no` = 'S01746' WHERE  `vendor_id` = 'THS-0156';
UPDATE `vendor` SET `sap_supplier_no` = '1000300' WHERE  `vendor_id` = 'THS-0155';
UPDATE `vendor` SET `sap_supplier_no` = '1000206' WHERE  `vendor_id` = 'THS-0154';
UPDATE `vendor` SET `sap_supplier_no` = 'S01975' WHERE  `vendor_id` = 'THS-0153';
UPDATE `vendor` SET `sap_supplier_no` = '1000280' WHERE  `vendor_id` = 'THS-0152';
UPDATE `vendor` SET `sap_supplier_no` = 'S00887' WHERE  `vendor_id` = 'THS-0151';
UPDATE `vendor` SET `sap_supplier_no` = 'S01967' WHERE  `vendor_id` = 'THS-0150';
UPDATE `vendor` SET `sap_supplier_no` = 'S01633' WHERE  `vendor_id` = 'THS-0149';
UPDATE `vendor` SET `sap_supplier_no` = '1000271' WHERE  `vendor_id` = 'CNS-0028';
UPDATE `vendor` SET `sap_supplier_no` = 'S01985' WHERE  `vendor_id` = 'THS-0146';
UPDATE `vendor` SET `sap_supplier_no` = 'S02013' WHERE  `vendor_id` = 'THS-0145';
UPDATE `vendor` SET `sap_supplier_no` = '1000293' WHERE  `vendor_id` = 'THS-0144';
UPDATE `vendor` SET `sap_supplier_no` = '1000264' WHERE  `vendor_id` = 'THS-0143';
UPDATE `vendor` SET `sap_supplier_no` = 'S01865' WHERE  `vendor_id` = 'CNS-0027';
UPDATE `vendor` SET `sap_supplier_no` = '1000318' WHERE  `vendor_id` = 'THS-0142';
UPDATE `vendor` SET `sap_supplier_no` = 'S01739' WHERE  `vendor_id` = 'THS-0141';
UPDATE `vendor` SET `sap_supplier_no` = 'S02052' WHERE  `vendor_id` = 'THS-0140';
UPDATE `vendor` SET `sap_supplier_no` = 'S01740' WHERE  `vendor_id` = 'CNS-0026';
UPDATE `vendor` SET `sap_supplier_no` = 'S01958' WHERE  `vendor_id` = 'CNS-0025';
UPDATE `vendor` SET `sap_supplier_no` = 'S00751' WHERE  `vendor_id` = 'CNS-0024';
UPDATE `vendor` SET `sap_supplier_no` = '1000262' WHERE  `vendor_id` = 'THS-0139';
UPDATE `vendor` SET `sap_supplier_no` = '1000270' WHERE  `vendor_id` = 'THS-0138';
UPDATE `vendor` SET `sap_supplier_no` = 'S01849' WHERE  `vendor_id` = 'THS-0137';
UPDATE `vendor` SET `sap_supplier_no` = '1000265' WHERE  `vendor_id` = 'THS-0136';
UPDATE `vendor` SET `sap_supplier_no` = 'S01837' WHERE  `vendor_id` = 'THS-0135';
UPDATE `vendor` SET `sap_supplier_no` = 'S01265' WHERE  `vendor_id` = 'THS-0134';
UPDATE `vendor` SET `sap_supplier_no` = 'S02016' WHERE  `vendor_id` = 'THS-0132';
UPDATE `vendor` SET `sap_supplier_no` = 'S01988' WHERE  `vendor_id` = 'THS-0131';
UPDATE `vendor` SET `sap_supplier_no` = 'S01987' WHERE  `vendor_id` = 'THS-0130';
UPDATE `vendor` SET `sap_supplier_no` = 'S00800' WHERE  `vendor_id` = 'THS-0129';
UPDATE `vendor` SET `sap_supplier_no` = 'S01024' WHERE  `vendor_id` = 'THS-0128';
UPDATE `vendor` SET `sap_supplier_no` = 'S00748' WHERE  `vendor_id` = 'CNS-0023';
UPDATE `vendor` SET `sap_supplier_no` = 'S01965' WHERE  `vendor_id` = 'THS-0127';
UPDATE `vendor` SET `sap_supplier_no` = 'S00880' WHERE  `vendor_id` = 'THS-0126';
UPDATE `vendor` SET `sap_supplier_no` = 'S00837' WHERE  `vendor_id` = 'THS-0125';
UPDATE `vendor` SET `sap_supplier_no` = 'S00739' WHERE  `vendor_id` = 'THS-0124';
UPDATE `vendor` SET `sap_supplier_no` = 'S00639' WHERE  `vendor_id` = 'THS-0123';
UPDATE `vendor` SET `sap_supplier_no` = 'S01845' WHERE  `vendor_id` = 'THS-0122';
UPDATE `vendor` SET `sap_supplier_no` = '1000197' WHERE  `vendor_id` = 'THS-0121';
UPDATE `vendor` SET `sap_supplier_no` = 'S01851' WHERE  `vendor_id` = 'THS-0120';
UPDATE `vendor` SET `sap_supplier_no` = 'S01991' WHERE  `vendor_id` = 'THS-0119';
UPDATE `vendor` SET `sap_supplier_no` = 'S01043' WHERE  `vendor_id` = 'THS-0118';
UPDATE `vendor` SET `sap_supplier_no` = 'S02040' WHERE  `vendor_id` = 'THS-0116';
UPDATE `vendor` SET `sap_supplier_no` = 'S02174' WHERE  `vendor_id` = 'THS-0115';
UPDATE `vendor` SET `sap_supplier_no` = '1000218' WHERE  `vendor_id` = 'THS-0114';
UPDATE `vendor` SET `sap_supplier_no` = '1000215' WHERE  `vendor_id` = 'THS-0113';
UPDATE `vendor` SET `sap_supplier_no` = 'S02180' WHERE  `vendor_id` = 'THS-0112';
UPDATE `vendor` SET `sap_supplier_no` = '1000192' WHERE  `vendor_id` = 'THS-0111';
UPDATE `vendor` SET `sap_supplier_no` = 'S02030' WHERE  `vendor_id` = 'THS-0110';
UPDATE `vendor` SET `sap_supplier_no` = '1000205' WHERE  `vendor_id` = 'THS-0109';
UPDATE `vendor` SET `sap_supplier_no` = 'S02025' WHERE  `vendor_id` = 'THS-0108';
UPDATE `vendor` SET `sap_supplier_no` = '1000232' WHERE  `vendor_id` = 'THS-0107';
UPDATE `vendor` SET `sap_supplier_no` = 'S02014' WHERE  `vendor_id` = 'THS-0106';
UPDATE `vendor` SET `sap_supplier_no` = 'S02001' WHERE  `vendor_id` = 'THS-0105';
UPDATE `vendor` SET `sap_supplier_no` = 'S02012' WHERE  `vendor_id` = 'THS-0104';
UPDATE `vendor` SET `sap_supplier_no` = 'S01961' WHERE  `vendor_id` = 'THS-0103';
UPDATE `vendor` SET `sap_supplier_no` = 'S01858' WHERE  `vendor_id` = 'THS-0102';
UPDATE `vendor` SET `sap_supplier_no` = 'S01829' WHERE  `vendor_id` = 'THS-0101';
UPDATE `vendor` SET `sap_supplier_no` = 'S01972' WHERE  `vendor_id` = 'THS-0100';
UPDATE `vendor` SET `sap_supplier_no` = 'S01398' WHERE  `vendor_id` = 'THS-0098';
UPDATE `vendor` SET `sap_supplier_no` = 'S01817' WHERE  `vendor_id` = 'THS-0097';
UPDATE `vendor` SET `sap_supplier_no` = 'S01637' WHERE  `vendor_id` = 'THS-0094';
UPDATE `vendor` SET `sap_supplier_no` = 'S00757' WHERE  `vendor_id` = 'CNS-0022';
UPDATE `vendor` SET `sap_supplier_no` = 'S00756' WHERE  `vendor_id` = 'CNS-0021';
UPDATE `vendor` SET `sap_supplier_no` = 'S00746' WHERE  `vendor_id` = 'CNS-0018';
UPDATE `vendor` SET `sap_supplier_no` = 'S00755' WHERE  `vendor_id` = 'CNS-0017';
UPDATE `vendor` SET `sap_supplier_no` = 'S00754' WHERE  `vendor_id` = 'CNS-0014';
UPDATE `vendor` SET `sap_supplier_no` = 'S00753' WHERE  `vendor_id` = 'CNS-0013';
UPDATE `vendor` SET `sap_supplier_no` = 'S00744' WHERE  `vendor_id` = 'CNS-0012';
UPDATE `vendor` SET `sap_supplier_no` = 'S00752' WHERE  `vendor_id` = 'CNS-0010';
UPDATE `vendor` SET `sap_supplier_no` = 'S00751' WHERE  `vendor_id` = 'CNS-0009';
UPDATE `vendor` SET `sap_supplier_no` = 'S00750' WHERE  `vendor_id` = 'CNS-0008';
UPDATE `vendor` SET `sap_supplier_no` = 'S00747' WHERE  `vendor_id` = 'CNS-0007';
UPDATE `vendor` SET `sap_supplier_no` = 'S00749' WHERE  `vendor_id` = 'CNS-0004';
UPDATE `vendor` SET `sap_supplier_no` = 'S01271' WHERE  `vendor_id` = 'CNS-0003';
UPDATE `vendor` SET `sap_supplier_no` = 'S00748' WHERE  `vendor_id` = 'CNS-0002';
UPDATE `vendor` SET `sap_supplier_no` = 'S02009' WHERE  `vendor_id` = 'CNS-0001';
UPDATE `vendor` SET `sap_supplier_no` = 'S00710' WHERE  `vendor_id` = 'THS-0093';
UPDATE `vendor` SET `sap_supplier_no` = 'S00920' WHERE  `vendor_id` = 'THS-0092';
UPDATE `vendor` SET `sap_supplier_no` = 'S00919' WHERE  `vendor_id` = 'THS-0091';
UPDATE `vendor` SET `sap_supplier_no` = 'S00918' WHERE  `vendor_id` = 'THS-0090';
UPDATE `vendor` SET `sap_supplier_no` = 'S00917' WHERE  `vendor_id` = 'THS-0089';
UPDATE `vendor` SET `sap_supplier_no` = 'S00818' WHERE  `vendor_id` = 'THS-0088';
UPDATE `vendor` SET `sap_supplier_no` = 'S00916' WHERE  `vendor_id` = 'THS-0087';
UPDATE `vendor` SET `sap_supplier_no` = 'S00915' WHERE  `vendor_id` = 'THS-0086';
UPDATE `vendor` SET `sap_supplier_no` = 'S00803' WHERE  `vendor_id` = 'THS-0085';
UPDATE `vendor` SET `sap_supplier_no` = 'S00914' WHERE  `vendor_id` = 'THS-0084';
UPDATE `vendor` SET `sap_supplier_no` = 'S00913' WHERE  `vendor_id` = 'THS-0083';
UPDATE `vendor` SET `sap_supplier_no` = 'S00912' WHERE  `vendor_id` = 'THS-0082';
UPDATE `vendor` SET `sap_supplier_no` = 'S00911' WHERE  `vendor_id` = 'THS-0081';
UPDATE `vendor` SET `sap_supplier_no` = 'S00806' WHERE  `vendor_id` = 'THS-0080';
UPDATE `vendor` SET `sap_supplier_no` = 'S00910' WHERE  `vendor_id` = 'THS-0079';
UPDATE `vendor` SET `sap_supplier_no` = 'S00909' WHERE  `vendor_id` = 'THS-0078';
UPDATE `vendor` SET `sap_supplier_no` = 'S00738' WHERE  `vendor_id` = 'THS-0077';
UPDATE `vendor` SET `sap_supplier_no` = 'S00908' WHERE  `vendor_id` = 'THS-0076';
UPDATE `vendor` SET `sap_supplier_no` = 'S00907' WHERE  `vendor_id` = 'THS-0075';
UPDATE `vendor` SET `sap_supplier_no` = 'S00906' WHERE  `vendor_id` = 'THS-0074';
UPDATE `vendor` SET `sap_supplier_no` = 'S00801' WHERE  `vendor_id` = 'THS-0073';
UPDATE `vendor` SET `sap_supplier_no` = 'S00734' WHERE  `vendor_id` = 'THS-0072';
UPDATE `vendor` SET `sap_supplier_no` = 'S00905' WHERE  `vendor_id` = 'THS-0071';
UPDATE `vendor` SET `sap_supplier_no` = 'S00904' WHERE  `vendor_id` = 'THS-0070';
UPDATE `vendor` SET `sap_supplier_no` = 'S00903' WHERE  `vendor_id` = 'THS-0069';
UPDATE `vendor` SET `sap_supplier_no` = 'S00902' WHERE  `vendor_id` = 'THS-0068';
UPDATE `vendor` SET `sap_supplier_no` = 'S00814' WHERE  `vendor_id` = 'THS-0067';
UPDATE `vendor` SET `sap_supplier_no` = 'S00901' WHERE  `vendor_id` = 'THS-0066';
UPDATE `vendor` SET `sap_supplier_no` = 'S00731' WHERE  `vendor_id` = 'THS-0065';
UPDATE `vendor` SET `sap_supplier_no` = 'S00900' WHERE  `vendor_id` = 'THS-0064';
UPDATE `vendor` SET `sap_supplier_no` = 'S00899' WHERE  `vendor_id` = 'THS-0063';
UPDATE `vendor` SET `sap_supplier_no` = 'S00898' WHERE  `vendor_id` = 'THS-0062';
UPDATE `vendor` SET `sap_supplier_no` = 'S00897' WHERE  `vendor_id` = 'THS-0061';
UPDATE `vendor` SET `sap_supplier_no` = 'S00896' WHERE  `vendor_id` = 'THS-0060';
UPDATE `vendor` SET `sap_supplier_no` = 'S00895' WHERE  `vendor_id` = 'THS-0059';
UPDATE `vendor` SET `sap_supplier_no` = 'S00894' WHERE  `vendor_id` = 'THS-0058';
UPDATE `vendor` SET `sap_supplier_no` = 'S00893' WHERE  `vendor_id` = 'THS-0057';
UPDATE `vendor` SET `sap_supplier_no` = 'S00892' WHERE  `vendor_id` = 'THS-0056';
UPDATE `vendor` SET `sap_supplier_no` = 'S00891' WHERE  `vendor_id` = 'THS-0055';
UPDATE `vendor` SET `sap_supplier_no` = 'S00890' WHERE  `vendor_id` = 'THS-0054';
UPDATE `vendor` SET `sap_supplier_no` = 'S00889' WHERE  `vendor_id` = 'THS-0053';
UPDATE `vendor` SET `sap_supplier_no` = 'S00888' WHERE  `vendor_id` = 'THS-0052';
UPDATE `vendor` SET `sap_supplier_no` = 'S00653' WHERE  `vendor_id` = 'THS-0051';
UPDATE `vendor` SET `sap_supplier_no` = 'S00674' WHERE  `vendor_id` = 'THS-0050';
UPDATE `vendor` SET `sap_supplier_no` = 'S00739' WHERE  `vendor_id` = 'THS-0049';
UPDATE `vendor` SET `sap_supplier_no` = 'S00887' WHERE  `vendor_id` = 'THS-0048';
UPDATE `vendor` SET `sap_supplier_no` = 'S00612' WHERE  `vendor_id` = 'THS-0047';
UPDATE `vendor` SET `sap_supplier_no` = 'S00886' WHERE  `vendor_id` = 'THS-0046';
UPDATE `vendor` SET `sap_supplier_no` = 'S00885' WHERE  `vendor_id` = 'THS-0045';
UPDATE `vendor` SET `sap_supplier_no` = 'S00884' WHERE  `vendor_id` = 'THS-0044';
UPDATE `vendor` SET `sap_supplier_no` = 'S00883' WHERE  `vendor_id` = 'THS-0043';
UPDATE `vendor` SET `sap_supplier_no` = 'S00882' WHERE  `vendor_id` = 'THS-0042';
UPDATE `vendor` SET `sap_supplier_no` = 'S00805' WHERE  `vendor_id` = 'THS-0041';
UPDATE `vendor` SET `sap_supplier_no` = 'S00881' WHERE  `vendor_id` = 'THS-0040';
UPDATE `vendor` SET `sap_supplier_no` = 'S00880' WHERE  `vendor_id` = 'THS-0039';
UPDATE `vendor` SET `sap_supplier_no` = 'S00804' WHERE  `vendor_id` = 'THS-0038';
UPDATE `vendor` SET `sap_supplier_no` = 'S00879' WHERE  `vendor_id` = 'THS-0037';
UPDATE `vendor` SET `sap_supplier_no` = 'S00641' WHERE  `vendor_id` = 'THS-0036';
UPDATE `vendor` SET `sap_supplier_no` = 'S00711' WHERE  `vendor_id` = 'THS-0035';
UPDATE `vendor` SET `sap_supplier_no` = 'S00878' WHERE  `vendor_id` = 'THS-0034';
UPDATE `vendor` SET `sap_supplier_no` = 'S00808' WHERE  `vendor_id` = 'THS-0033';
UPDATE `vendor` SET `sap_supplier_no` = 'S00729' WHERE  `vendor_id` = 'THS-0032';
UPDATE `vendor` SET `sap_supplier_no` = 'S00877' WHERE  `vendor_id` = 'THS-0031';
UPDATE `vendor` SET `sap_supplier_no` = 'S00876' WHERE  `vendor_id` = 'THS-0030';
UPDATE `vendor` SET `sap_supplier_no` = 'S00816' WHERE  `vendor_id` = 'THS-0029';
UPDATE `vendor` SET `sap_supplier_no` = 'S00875' WHERE  `vendor_id` = 'THS-0028';
UPDATE `vendor` SET `sap_supplier_no` = 'S00874' WHERE  `vendor_id` = 'THS-0027';
UPDATE `vendor` SET `sap_supplier_no` = 'S00873' WHERE  `vendor_id` = 'THS-0026';
UPDATE `vendor` SET `sap_supplier_no` = 'S00368' WHERE  `vendor_id` = 'THS-0025';
UPDATE `vendor` SET `sap_supplier_no` = 'S00872' WHERE  `vendor_id` = 'THS-0024';
UPDATE `vendor` SET `sap_supplier_no` = 'S00647' WHERE  `vendor_id` = 'THS-0023';
UPDATE `vendor` SET `sap_supplier_no` = 'S00871' WHERE  `vendor_id` = 'THS-0022';
UPDATE `vendor` SET `sap_supplier_no` = 'S00870' WHERE  `vendor_id` = 'THS-0021';
UPDATE `vendor` SET `sap_supplier_no` = 'S00869' WHERE  `vendor_id` = 'THS-0020';
UPDATE `vendor` SET `sap_supplier_no` = 'S00868' WHERE  `vendor_id` = 'THS-0019';
UPDATE `vendor` SET `sap_supplier_no` = 'S00867' WHERE  `vendor_id` = 'THS-0018';
UPDATE `vendor` SET `sap_supplier_no` = 'S00866' WHERE  `vendor_id` = 'THS-0017';
UPDATE `vendor` SET `sap_supplier_no` = 'S00865' WHERE  `vendor_id` = 'THS-0016';
UPDATE `vendor` SET `sap_supplier_no` = 'S00643' WHERE  `vendor_id` = 'THS-0015';
UPDATE `vendor` SET `sap_supplier_no` = 'S00864' WHERE  `vendor_id` = 'THS-0014';
UPDATE `vendor` SET `sap_supplier_no` = 'S00839' WHERE  `vendor_id` = 'THS-0013';
UPDATE `vendor` SET `sap_supplier_no` = 'S00863' WHERE  `vendor_id` = 'THS-0012';
UPDATE `vendor` SET `sap_supplier_no` = 'S00862' WHERE  `vendor_id` = 'THS-0011';
UPDATE `vendor` SET `sap_supplier_no` = 'S00855' WHERE  `vendor_id` = 'THS-0010';
UPDATE `vendor` SET `sap_supplier_no` = 'S00810' WHERE  `vendor_id` = 'THS-0009';
UPDATE `vendor` SET `sap_supplier_no` = 'S00861' WHERE  `vendor_id` = 'THS-0008';
UPDATE `vendor` SET `sap_supplier_no` = 'S00860' WHERE  `vendor_id` = 'THS-0007';
UPDATE `vendor` SET `sap_supplier_no` = 'S00859' WHERE  `vendor_id` = 'THS-0006';
UPDATE `vendor` SET `sap_supplier_no` = 'S00635' WHERE  `vendor_id` = 'THS-0005';
UPDATE `vendor` SET `sap_supplier_no` = 'S00858' WHERE  `vendor_id` = 'THS-0004';
UPDATE `vendor` SET `sap_supplier_no` = 'S00845' WHERE  `vendor_id` = 'THS-0003';
UPDATE `vendor` SET `sap_supplier_no` = 'S00857' WHERE  `vendor_id` = 'THS-0002';
UPDATE `vendor` SET `sap_supplier_no` = 'S00856' WHERE  `vendor_id` = 'THS-0001';


--普通付款
-- flashpay
update workflow_node set auditor_id ='73599' where id ='713' and flow_id='56';
-- flashmoney
update workflow_node set auditor_id ='72336',name='AP Supervisor（FlashMoney)' where id ='698' and flow_id='55';

-- 报销
update workflow_node set auditor_id='60277' where flow_id='44' and id ='533';


-- 报销
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 526, 984, '$p1 != 1 && $p2 < 2000000 && $p3==60001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 2000 且是FlashPay : 二级部门负责人->AP （FlashPay）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 526, 985, '$p1 != 1 && $p2 < 2000000 && $p3==30001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 2000 且是FlashMoney : 二级部门负责人->AP （FlashMoney）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 526, 986, '$p1 != 1 && $p2 < 2000000 && $p3==50001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 2000 且是FCommerce : 二级部门负责人->AP （FCommerce）', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 527, 984, '$p1 != 1 && $p2 < 10000000 && $p3==60001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 10000 且是FlashPay:一级部门负责人->AP(FlashPay)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 527, 985, '$p1 != 1 && $p2 < 10000000 && $p3==30001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 10000 且是FlashMoney:一级部门负责人->AP(FlashMoney)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 527, 986, '$p1 != 1 && $p2 < 10000000 && $p3==50001', 'getReimbursementType,getAmount, getCompanyId', '非员工福利费/团建费 且 金额 < 10000 且是FCommerce:一级部门负责人->AP(FCommerce)', 21, now(), now());


INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 528, 984, '$p1 != 1 && $p2 < 50000000 && $p3==60001', 'getReimbursementType,getAmount, getCompanyId', '非员工福利费/团建费 且 金额 < 50000 是FlashPay :所属公司负责人->AP （FlashPay）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 528, 985, '$p1 != 1 && $p2 < 50000000 && $p3==30001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 50000 是FlashMoney :所属公司负责人->AP （FlashMoney）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 528, 986, '$p1 != 1 && $p2 < 50000000 && $p3== 50001', 'getReimbursementType,getAmount,getCompanyId', '非员工福利费/团建费 且 金额 < 50000 是FCommerce :所属公司负责人->AP （FCommerce）', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 529, 984, '$p1 != 1 && $p2 == 60001', 'getReimbursementType,getCompanyId', '非员工福利费/团建费 且是FlashPay :组织负责人->AP（FlashPay）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 529, 985, '$p1 != 1 && $p2 == 30001', 'getReimbursementType,getCompanyId', '非员工福利费/团建费 且是FlashMoney :组织负责人->AP（FlashMoney）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 529, 986, '$p1 != 1 && $p2 == 50001', 'getReimbursementType,getCompanyId', '非员工福利费/团建费 且是FCommerce :组织负责人->AP（FCommerce）', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 530, 984, '$p1 == 1 && $p2 < ********* && $p3==60001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FlashPay :HRBP DIrector-> AP (FlashPay)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 530, 985, '$p1 == 1 && $p2 < ********* && $p3== 30001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FlashMoney :HRBP DIrector-> AP (FlashMoney)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 530, 986, '$p1 == 1 && $p2 < ********* && $p3== 50001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FCommerce :HRBP DIrector-> AP (FCommerce)', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 531, 984, '$p1 == 60001', 'getCompanyId', '公司是FlashPay:CPO->AP（FlashPay）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 531, 985, '$p1 == 30001', 'getCompanyId', '公司是FlashMoney:CPO->AP（FlashMoney）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 531, 986, '$p1 == 50001', 'getCompanyId', '公司是FCommerce:CPO->AP（FCommerce）', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 813, 984, '$p1 == 1 && $p2 < ********* && $p3==60001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FlashPay:HRBP->AP(FlashPay)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 813, 985, '$p1 == 1 && $p2 < ********* && $p3== 30001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FlashMoney:HRBP->AP(FlashMoney)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 813, 986, '$p1 == 1 && $p2 < ********* && $p3== 50001', 'getReimbursementType,getAmount,getCompanyId', '员工福利费/团建费 且 金额 < 100000且是FCommerce:HRBP->AP(FCommerce)', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 984, 533, NULL, NULL, 'AP（FlashPay）->AP Supervisor（泰国）', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 985, 533, NULL, NULL, 'AP（FlashMoney）->AP Supervisor（泰国）', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 986, 533, NULL, NULL, 'AP（FCommerce）->AP Supervisor（泰国）', 10, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 533, 987, '$p1 == 60001', 'getCompanyId', 'AP Supervisor（泰国）->AP Supervisor（FlashPay）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 533, 988, '$p1 == 30001', 'getCompanyId', 'AP Supervisor（泰国）->AP Supervisor（FlashMoney）', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 533, 989, '$p1 == 50001', 'getCompanyId', 'AP Supervisor（泰国）->AP Supervisor（FCommerce）', 21, now(), now());


INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 987, 540, '$p1 != 1 && $p2 < 2000000', 'getReimbursementType,getAmount', '非员工福利费/团建费 且 金额 < 2000:AP Supervisor（FlashPay）->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 987, 540, '$p1 == 1 && $p2 < 10000000', 'getReimbursementType,getAmount', '员工福利费/团建费 且 金额 < 10000:AP Supervisor（FlashPay）-> 结束', 10,now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 987, 535, NULL, NULL, 'AP Supervisor（FlashPay）->Finance Manager',10, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 988, 540, '$p1 != 1 && $p2 < 2000000', 'getReimbursementType,getAmount', '非员工福利费/团建费 且 金额 < 2000:AP Supervisor（FlashMoney）->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 988, 540, '$p1 == 1 && $p2 < 10000000', 'getReimbursementType,getAmount', '员工福利费/团建费 且 金额 < 10000:AP Supervisor（FlashMoney）-> 结束', 10,now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 988, 535, NULL, NULL, 'AP Supervisor（FlashMoney）->Finance Manager',10, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 989, 540, '$p1 != 1 && $p2 < 2000000', 'getReimbursementType,getAmount', '非员工福利费/团建费 且 金额 < 2000:AP Supervisor（FlashMoney）->结束', 10, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 989, 540, '$p1 == 1 && $p2 < 10000000', 'getReimbursementType,getAmount', '员工福利费/团建费 且 金额 < 10000:AP Supervisor（FlashMoney）-> 结束', 10,now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 44, 989, 535, NULL, NULL, 'AP Supervisor（FlashMoney）->Finance Manager',10, now(), now());


-- 采购付款申请单
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 411, 990, '$p1==60001', 'getCompanyId', 'PSM -> AP(FlashPay)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 411, 991, '$p1==30001', 'getCompanyId', 'PSM -> AP(FlashMoney)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 411, 992, '$p1==50001', 'getCompanyId', 'PSM -> AP(FCommerce)', 21, now(), now());


INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 990, 413, NULL, NULL, 'AP(FlashPay) -> APS(泰国)', 10, now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 991, 413, NULL, NULL, 'AP(FlashMoney) -> APS(泰国)', 10, now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 992, 413, NULL, NULL, 'AP(FCommerce) -> APS(泰国)', 10, now(),now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 413, 993, '$p1==60001', 'getCompanyId', 'APS泰国 -> APS(FlashPay)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 413, 994, '$p1==30001', 'getCompanyId', 'APS泰国 -> APS(FlashMoney)', 21, now(), now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 413, 995, '$p1==50001', 'getCompanyId', 'APS泰国 -> APS(FCommerce)', 21, now(), now());

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 993, 415, NULL, NULL, 'APS(FlashPay) -> FM', 10, now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 994, 415, NULL, NULL, 'APS(FlashMoney) -> FM', 10, now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 37, 995, 415, NULL, NULL, 'APS(FCommerce) -> FM', 10, now(),now());


-- 普通付款
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 55, 697, 996, '$p1==50001', 'getCompanyId', 'AP Supervisor(泰国)->AP Supervisor(Fcommerce)', 21, now(), now());


INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 55, 996, 704, '$p1 < 20000', 'getTotalAmount', '金额 < 20000, AP Supervisor(Fcommerce) -> 结束', 10, now(),now());
INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 55, 996, 699, '', '', 'AP Supervisor（Fcommerce）-> Finance Manager', 10, now(), now());


-- 采购申请单
update workflow_node_relate set valuate_formula ='$p1==0 || $p1==1', valuate_code='getCompanyId' where flow_id=30 and from_node_id ='306' and to_node_id='720';

INSERT INTO `workflow_node_relate` (`id`, `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (null, 30, 306, 307, NULL, NULL, '员工提交->直接上级', 11, now(), now());