-- 标准主合同（销售）- 定结合同 flow_Id = 59
update workflow_node set auditor_id = '72642' where id = 823 and flow_id=59;
update workflow_node set auditor_id = '20254,76322' where id = 825 and flow_id = 59;
update workflow_node set auditor_id = '68212' where id = 827 and flow_id = 59;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1447, 59, 'lvy', 3, 1, '17152');


UPDATE workflow_node_relate set to_node_id = 1447 where id = 1254 and flow_id = 59 and from_node_id = 827;
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`) VALUES (59, 1447, 828, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');


-- 标准主合同（销售）- 现结合同 flow_id = 60
update workflow_node set auditor_id = '72642' where id = 831 and flow_id=60;
update workflow_node set auditor_id = '20254,76322' where id = 832 and flow_id = 60;


-- 非标准主合同（销售）flow_id = 61
update workflow_node set auditor_id = '72642' where id = 837 and flow_id=61;
update workflow_node set auditor_id = '20254,76322' where id = 839 and flow_id = 61;
update workflow_node set auditor_id = '68212' where id = 841 and flow_id = 61;

INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1449, 61, 'lvy', 3, 1, '17152');

UPDATE workflow_node_relate set to_node_id = 1449 where id = 1266 and flow_id = 61 and from_node_id = 841;
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`)
VALUES (61, 1449, 842, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');


-- 补充协议（授权范围内） flow_id = 62
update workflow_node set auditor_id = '72642' where id = 846 and flow_id=62;
update workflow_node set auditor_id = '20254,76322' where id = 848 and flow_id = 62;
update workflow_node set auditor_id = '68212' where id = 850 and flow_id = 62;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1450, 62, 'lvy', 3, 1, '17152');

UPDATE workflow_node_relate set to_node_id = 1450 where id = 1275 and flow_id = 62 and from_node_id = 850;
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`) VALUES (62, 1450, 851, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');


-- 补充协议（授权范围外） flow_id = 63
update workflow_node set auditor_id = '72642' where id = 854 and flow_id=63;
update workflow_node set auditor_id = '20254,76322' where id = 856 and flow_id = 63;
update workflow_node set auditor_id = '68212' where id = 858 and flow_id = 63;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1451, 63, 'lvy', 3, 1, '17152');

UPDATE workflow_node_relate set to_node_id = 1451 where id = 1282 and flow_id = 63 and from_node_id = 858;
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`) VALUES (63, 1451, 859, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');