--  v9428备用金菜单和接口权限
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (293, 'menu.reserve', '网点备用金', NULL, 0, 1, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (294, 'menu.reserve.apply', '网点备用金申请', NULL, 293, 1, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (295, 'menu.reserve.audit', '网点备用金审核', NULL, 293, 1, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (296, 'menu.reserve.pay', '网点备用金支付', NULL, 293, 1, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (297, 'menu.reserve.reply', '网点备用金回复', NULL, 293, 1, 5, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (298, 'menu.reserve.return', '网点备用金归还', NULL, 293, 1, 6, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (299, 'menu.reserve.data', '网点备用金数据查询', NULL, 293, 1, 7, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (300, 'action.reserve.apply.search', '网点备用金-我的申请-list', NULL, 294, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (301, 'action.reserve.apply.view', '网点备用金-我的申请-详情', NULL, 294, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (302, 'action.reserve.apply.apply', '网点备用金-我的申请-提交', NULL, 294, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (303, 'action.reserve.apply.cancel', '网点备用金-我的申请-撤销', NULL, 294, 2, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (304, 'menu.reserve.audit.apply', '网点备用金-审核-申请备用金', NULL, 295, 1, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (305, 'menu.reserve.audit.return', '网点备用金-审核-归还备用金', NULL, 295, 1, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (306, 'action.reserve.audit.apply.search', '网点备用金-审核-申请备用金查询', NULL, 304, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (307, 'action.reserve.audit.apply.view', '备用金申请单-审核详情', NULL, 304, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (308, 'action.reserve.audit.apply.audit', '备用金申请单-审核', NULL, 304, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (309, 'action.reserve.audit.return.search', '备用金归还申请单-审核-list', NULL, 305, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (310, 'action.reserve.audit.return.view', '备用金归还申请单-审核-详情', NULL, 305, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (311, 'action.reserve.audit.return.audit', '备用金归还申请单-审核-审核', NULL, 305, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (312, 'action.reserve.pay.search', '备用金支付-list', NULL, 296, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (313, 'action.reserve.pay.view', '备用金支付-详情', NULL, 296, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (314, 'action.reserve.pay.pay', '备用金支付-支付', NULL, 296, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (315, 'action.reserve.return.search', '备用金归还-list', NULL, 298, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (316, 'action.reserve.return.view', '备用金归还-详情', NULL, 298, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (317, 'action.reserve.return.apply', '备用金归还-申请', NULL, 298, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (318, 'action.reserve.return.cancel', '备用金归还-撤销', NULL, 298, 2, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (319, 'action.reserve.data.search', '数据查询-list', '备用金数据查询-查询', 299, 2, 4, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (320, 'action.reserve.data.view', '数据查询-view', NULL, 299, 2, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (321, 'action.reserve.data.export', '数据查询-导出excel', NULL, 299, 2, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (322, 'action.reserve.data.download', '数据查询-下载pdf', NULL, 299, 2, 4, NULL);

-- 网点备用金申请审批流
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (73,'网点备用金申请审批-hub部门',51,'网点备用金申请审批-hub部门','2021-07-15 16:51:57');
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (74,'网点备用金申请审批-shop部门',51,'网点备用金申请审批-shop部门','2021-07-15 16:51:57');
INSERT INTO `workflow`(`id`,`name`,`biz_type`,`description`,`created_at`) VALUES (75,'网点备用金申请审批-network部门',51,'网点备用金申请审批-network部门','2021-07-15 16:51:57');

-- hub部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1011, 73, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1012, 73, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1013, 73, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1014, 73, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1015, 73, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1016, 73, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1017, 73, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1018, 73, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- shop部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1019, 74, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1020, 74, '直线上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1021, 74, 'Shop Area Manager', 3, 0, NULL, NULL, NULL, NULL, 17, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1022, 74, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1023, 74, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1024, 74, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1025, 74, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1026, 74, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1027, 74, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- network部门备用金申请审批节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1029, 75, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1030, 75, '直线上级', 3, 0, NULL, NULL, NULL, NULL, 3, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1031, 75, 'Area Manager', 3, 0, NULL, NULL, NULL, NULL, 17, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1032, 75, '二级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 5, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1033, 75, '一级部门负责人', 3, 0, NULL, NULL, NULL, NULL, 6, '', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1034, 75, 'Tax Management Supervisor', 3, 0, NULL, NULL, NULL, NULL, 1, '20017259', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1035, 75, 'Finance Manager', 3, 0, NULL, NULL, NULL, NULL, 1, '64377', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1036, 75, 'Finance Director', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (1037, 75, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL);

-- hub网点节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1011, 1012, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1012, 1013, NULL, NULL, '直接上级 -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1013, 1014, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1014, 1015, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1015, 1016, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1016, 1017, NULL, NULL, 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (73, 1017, 1018, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');

-- shop网点节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1019, 1020, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1020, 1021, NULL, NULL, '直接上级 -> Shop Area Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1021, 1022, NULL, NULL, 'Shop Area Manager -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1022, 1023, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1023, 1024, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1024, 1025, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1025, 1027, '$p1<15000', 'getAmount', 'Finance Manager -> 结束', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1025, 1026, '$p1>=15000', 'getAmount', 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (74, 1026, 1027, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');

-- network部门节点关系
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1029, 1030, NULL, NULL, '员工提交 -> 直接上级', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1030, 1031, NULL, NULL, '直接上级 -> Area Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1031, 1032, NULL, NULL, 'Area Manager -> 二级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1032, 1033, NULL, NULL, '二级部门 -> 一级部门', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1033, 1034, NULL, NULL, '一级部门 -> Tax Management Supervisor', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1034, 1035, NULL, NULL, 'Tax Management Supervisor -> Finance Manager', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1035, 1036, NULL, NULL, 'Finance Manager -> Finance Director', 10, '2021-03-30 09:37:29', '2021-03-30 07:57:35');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES (75, 1036, 1037, NULL, NULL, 'Finance Director -> 结束', 10, '2021-03-30 07:57:35', '2021-03-30 07:57:35');

-- 网点备用金归还审批节点-network部门
delete from `workflow_node` where `id`=580 or `id`=581;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1038, 47, 'Tax Management Supervisor', 3, 1, '20017259');
UPDATE `workflow_node` SET `auditor_id` = '20017176' WHERE `id` = 578;
UPDATE `workflow_node` SET `auditor_id` = '64377' WHERE `id` = 579;
UPDATE `workflow_node` SET `auditor_id` = '76260,65382' WHERE `id` = 582;
-- 网点备用金归还审批节点关系-network部门
UPDATE `workflow_node_relate` SET `to_node_id` = 1038,`remark`='network support -> Tax Management Supervisor' WHERE `id` = 916;
delete from `workflow_node_relate` where `id` in(917,918,919,920,921,922,925,926);
UPDATE `workflow_node_relate` SET `from_node_id` = 1038,`remark`='Tax Management Supervisor -> FM' WHERE `id` = 923;
UPDATE `workflow_node_relate` SET `to_node_id` = 582,`remark`='Tax Management Supervisor -> FM' WHERE `id` = 924;

-- 网点备用金归还审批节点关系-hub部门
delete from `workflow_node` where `id`=588 or `id`=589;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1039, 48, 'Tax Management Supervisor', 3, 1, '20017259');
UPDATE `workflow_node` SET `auditor_id` = '30286' WHERE `id` = 586;
UPDATE `workflow_node` SET `auditor_id` = '64377' WHERE `id` = 587;
UPDATE `workflow_node` SET `auditor_id` = '76260,65382' WHERE `id` = 590;
-- 网点备用金归还审批节点关系-hub部门
UPDATE `workflow_node_relate` SET `to_node_id` = 1039,`remark`='Hub Director -> Tax Management Supervisor' WHERE `id` = 930;
delete from `workflow_node_relate` where `id` in(931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 945, 946);
UPDATE `workflow_node_relate` SET `from_node_id` = 1039,`remark`='Tax Management Supervisor -> FM' WHERE `id` = 943;
UPDATE `workflow_node_relate` SET `to_node_id` = 590,`remark`='FM -> APO(出纳)' WHERE `id` = 944;

-- 网点备用金归还审批节点关系-shop部门
delete from `workflow_node` where `id`=597;
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1040, 49, 'Tax Management Supervisor', 3, 1, '20017259');
UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 594;
UPDATE `workflow_node` SET `auditor_id` = '' WHERE `id` = 595;
UPDATE `workflow_node` SET `auditor_id` = '64377' WHERE `id` = 596;
UPDATE `workflow_node` SET `auditor_id` = '20017225' WHERE `id` = 598;
UPDATE `workflow_node` SET `auditor_id` = '76260,65382' WHERE `id` = 599;
-- 网点备用金归还审批节点关系-shop部门
UPDATE `workflow_node_relate` SET `remark` = '员工提交 -> Shop Area Manager' WHERE `id` = 948;
UPDATE `workflow_node_relate` SET `remark` = 'Shop Area Manager -> Shop operations Manager' WHERE `id` = 949;
DELETE FROM `workflow_node_relate` WHERE `id` in(950,951,954);
UPDATE `workflow_node_relate` SET `remark` = 'Shop operations Manager -> Assistant Shop MD' WHERE `id` = 952;
UPDATE `workflow_node_relate` SET `to_node_id` = 1040,`remark`='ASMD -> Tax Management Supervisor' WHERE `id` = 953;
UPDATE `workflow_node_relate` SET `from_node_id` = 1040, `to_node_id` = 596,`remark` = 'Tax Management Supervisor -> FM' WHERE `id` = 954;
UPDATE `workflow_node_relate` SET `from_node_id` = 596, `to_node_id` = 598,`remark` = 'FM -> FD' WHERE `id` = 955;

-- 支付人
update `setting_env` set `val` = '76260,65382'  where `code`='reserve_fund_payment_pay_staff_id';
