-- 增加合同所属公司枚举
INSERT INTO `contract_company`(`id`, `company_code`, `company_name`, `is_delete`) VALUES (8, '8', 'Flash Express(PH)', 0);

-- 其他合同（非销售）审批流
-- 审批流审批人变更
update workflow_node set `name` = '所属组织负责人（COO）', auditor_id = '17245' where id = 334 and flow_id = 32;

update workflow_node set `name` = '初审(中）', auditor_id = '57335' where id = 335 and flow_id = 32;
update workflow_node set `name` = '初审（英/泰）', auditor_id = '118754,118991' where id = 336 and flow_id = 32;
update workflow_node set `name` = '复审（中）', auditor_id = '57335' where id = 337 and flow_id = 32;
update workflow_node set `name` = '复审（英/泰）', auditor_id = '20017187,118991' where id = 338 and flow_id = 32;

INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1046, 32, '复审下一节点（中英泰）', 3, 1, '57335');

update workflow_node set auditor_id = '20017259' where id = 339 and flow_id = 32;
update workflow_node set auditor_id = '' where id = 340 and flow_id = 32;
update workflow_node set auditor_id = '64377,62721' where id = 341 and flow_id = 32;
update workflow_node set auditor_id = '' where id = 342 and flow_id = 32;
update workflow_node set auditor_id = '20017225' where id = 343 and flow_id = 32;

-- 审批节点流转逻辑扩展
-- 增加新币种
update workflow_node_relate set valuate_formula = '(($p1>=50000000 && $p2==1) || ($p1>=1576790 && $p2==2)||($p1>=11037530 && $p2==3)) || ($p1 >= 75000000 && $p2 == 4) || ($p3==25 || $p3==18)',remark = '（（>=50000泰铢）|| >=1,576.79 美元||>=11,037.53人民币）|| (>=75000比索)|| （提交部门是hub或者flashhome）二级部门负责人->一级部门负责人' WHERE id = 484 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['zh-CN','zh'])",valuate_code = 'getLang',remark = '其他额度, 且 中文，流转到 中文初审' WHERE id = 485 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['en', 'th'])",valuate_code = 'getLang',remark = '其他额度, 且 英泰，流转到 英泰初审',to_node_id = 336 WHERE id = 486 and flow_id = 32;

update workflow_node_relate set valuate_formula = '($p1>=200000000 && $p2==1) || ($p1>=6443300 && $p2==2) || ($p1 >= 300000000 && $p2 == 4) ||($p1>=42841680 && $p2==3)', remark = '（>=200000泰铢）|| >=6,443.30 美元||>=42,841.68人民币 || >=300000比索  一级部门负责人->所属公司负责人' WHERE id = 488 and flow_id = 32;

update workflow_node_relate set valuate_formula = "in_array($p1, ['zh-CN','zh'])",valuate_code = 'getLang',remark = '中，流转到 中初审' WHERE id = 489 and flow_id = 32;

update workflow_node_relate set valuate_formula = "in_array($p1, ['th','en'])",valuate_code = 'getLang',remark = '英泰，流转到 英泰初审',to_node_id = 336 WHERE id = 490 and flow_id = 32;


update workflow_node_relate set valuate_formula = '($p1>=1000000000 && $p2==1) || ($p1>= 31535800 && $p2==2)||($p1>= 220750600 && $p2==3) || ($p1 >= 1500000000 && $p2 == 4)',remark = '（>=1000000泰铢）|| >=31,535.80 美元||>=220,750.60人民币 || >= 1500000比索 所属公司负责人->所属组织负责人' WHERE id = 492 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['zh-CN','zh'])",valuate_code = 'getLang',remark = '中，流转到 中初审' WHERE id = 493 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['th','en'])",valuate_code = 'getLang',remark = '英泰，流转到 英泰初审',to_node_id = 336 WHERE id = 494 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['zh-CN','zh'])",valuate_code = 'getLang',remark = '中，流转到 中初审' WHERE id = 496 and flow_id = 32;


update workflow_node_relate set valuate_formula = "in_array($p1, ['th','en'])",valuate_code = 'getLang',remark = '英泰，流转到 英泰初审',to_node_id = 336 WHERE id = 497 and flow_id = 32;

DELETE from workflow_node_relate where id = 495 and flow_id = 32;

DELETE from workflow_node_relate where id = 501 and flow_id = 32;

-- 初审/复审/下一节点流转
update workflow_node_relate set valuate_formula = "in_array($p1, ['zh-CN','zh'])",valuate_code = 'getLang',remark = '中文，流转到 中复审',from_node_id = 335,to_node_id = 337 WHERE id = 487 and flow_id = 32;

update workflow_node_relate set valuate_formula = "in_array($p1, ['th','en'])",valuate_code = 'getLang',remark = '英泰，流转到 英泰复审',from_node_id = 336,to_node_id = 338 WHERE id = 491 and flow_id = 32;

update workflow_node_relate set valuate_formula = "",valuate_code = '',remark = '复审中->中英泰',from_node_id = 337, to_node_id = 1046 WHERE id = 498 and flow_id = 32;


update workflow_node_relate set valuate_formula = "",valuate_code = '',remark = '复审英泰->中英泰',from_node_id = 338, to_node_id = 1046 WHERE id = 499 and flow_id = 32;



update workflow_node_relate set valuate_formula = "",valuate_code = '',remark = '',from_node_id = 1046 WHERE id = 502 and flow_id = 32;


update workflow_node_relate set valuate_formula = '($p1<50000000 && $p2==1) || ($p1<1576790 && $p2==2) || ($p1<11037530 && $p2==3) || ($p1 < 75000000 && $p2 == 4)' WHERE id = 504 and flow_id = 32;

update workflow_node_relate set valuate_formula = '($p1<200000000 && $p2==1) || ($p1<6443300 && $p2==2) || ($p1<42841680 && $p2==3) || ($p1 < 300000000 && $p2 == 4)' WHERE id = 507 and flow_id = 32;


update workflow_node_relate set valuate_formula = '($p1<1000000000 && $p2==1) || ($p1<31535800 && $p2==2) || ($p1<220750600 && $p2==3) || ($p1 < 1500000000 && $p2 == 4)' WHERE id = 509 and flow_id = 32;


-- 其他合同（销售）审批流
-- 标准主合同（销售）-定结合同 flow_id = 59
-- 新增CFO节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1047, 59, 'CFO', 3, 1, '17152');

-- 变更审批人
update workflow_node set auditor_id = '' where id = 822 and flow_id=59;
update workflow_node set auditor_id = '' where id = 823 and flow_id=59;
update workflow_node set auditor_id = '57335,20017187' where id = 824 and flow_id=59;
update workflow_node set auditor_id = '20017259' where id = 825 and flow_id=59;
update workflow_node set auditor_id = '64377,62721' where id = 827 and flow_id=59;


-- 调整节点流转关系
UPDATE workflow_node_relate set to_node_id = 1047 where id = 1254 and flow_id = 59 and from_node_id = 827;

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`)
VALUES (59, 1047, 828, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');


-- 标准主合同（销售）-现结合同 flow_id = 60
-- 变更审批人
update workflow_node set auditor_id = '' where id = 831 and flow_id=60;
update workflow_node set auditor_id = '20017259' where id = 832 and flow_id=60;


-- 非标准主合同（销售）flow_id = 61
-- 新增CFO节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1048, 61, 'CFO', 3, 1, '17152');

-- 变更审批人
update workflow_node set auditor_id = '' where id = 836 and flow_id= 61;
update workflow_node set auditor_id = '' where id = 837 and flow_id= 61;
update workflow_node set auditor_id = '57335,20017187' where id = 838 and flow_id= 61;
update workflow_node set auditor_id = '20017259' where id = 839 and flow_id= 61;
update workflow_node set auditor_id = '64377,62721' where id = 841 and flow_id= 61;

-- 调整节点流转关系
UPDATE workflow_node_relate set to_node_id = 1048 where id = 1266 and flow_id = 61 and from_node_id = 841;

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`)
VALUES (61, 1048, 842, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');



-- 补充协议（授权范围内） flow_id = 62
-- 新增CFO节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1049, 62, 'CFO', 3, 1, '17152');

-- 变更审批人
update workflow_node set auditor_id = '' where id = 845 and flow_id= 62;
update workflow_node set auditor_id = '' where id = 846 and flow_id= 62;
update workflow_node set auditor_id = '57335,20017187' where id = 847 and flow_id= 62;
update workflow_node set auditor_id = '20017259' where id = 848 and flow_id= 62;
update workflow_node set auditor_id = '64377,62721' where id = 850 and flow_id= 62;


-- 调整节点流转关系
UPDATE workflow_node_relate set to_node_id = 1049 where id = 1275 and flow_id = 62 and from_node_id = 850;

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`)
VALUES (62, 1049, 851, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');




-- 补充协议（授权范围外） flow_id = 63
-- 新增CFO节点
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `auditor_type`, `auditor_id`) VALUES (1050, 63, 'CFO', 3, 1, '17152');

-- 变更审批人
update workflow_node set auditor_id = '' where id = 853 and flow_id= 63;
update workflow_node set auditor_id = '' where id = 854 and flow_id= 63;
update workflow_node set auditor_id = '57335,20017187' where id = 855 and flow_id= 63;
update workflow_node set auditor_id = '20017259' where id = 856 and flow_id= 63;
update workflow_node set auditor_id = '64377,62721' where id = 858 and flow_id= 63;



-- 调整节点流转关系
UPDATE workflow_node_relate set to_node_id = 1050 where id = 1282 and flow_id = 63 and from_node_id = 858;

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `sort`, `updated_at`, `created_at`)
VALUES (63, 1050, 859, 10, '2021-07-26 05:27:08', '2021-07-26 05:27:08');
