--  菲律宾reimbursement表添加source_type字段
ALTER TABLE `reimbursement` ADD COLUMN `source_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '入口来源 1 oa 2 by（油费报销）';
--  菲律宾reimbursement_expense初始化数据
insert into `reimbursement_expense`(`id`,`name`,`name_key`,`is_ho`,`is_store`,`is_hr`,`is_travel`,`is_local`,`is_china`,`created_at`,`updated_at`) values
(1,'国内机票','reimbursement_expense_0',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(2,'海外机票','reimbursement_expense_1',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(3,'火车票','reimbursement_expense_2',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(4,'汽车票','reimbursement_expense_3',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(5,'过路费','reimbursement_expense_4',1,1,0,1,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(6,'租车费','reimbursement_expense_5',1,1,0,1,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(7,'油费','reimbursement_expense_6',1,1,0,1,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(8,'住宿费','reimbursement_expense_7',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(9,'出差出租车费','reimbursement_expense_8',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(10,'业务招待费','reimbursement_expense_9',1,0,0,1,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(11,'签证费','reimbursement_expense_10',1,1,0,1,0,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(12,'当地出租车费','reimbursement_expense_11',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(13,'停车费','reimbursement_expense_12',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(14,'邮费','reimbursement_expense_13',1,0,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(15,'打印费','reimbursement_expense_14',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(16,'许可费','reimbursement_expense_15',1,0,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(17,'会议费','reimbursement_expense_16',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-10-16 06:09:05'),
(18,'办公用品费','reimbursement_expense_17',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(19,'保养&维修费','reimbursement_expense_18',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(20,'员工团建费','reimbursement_expense_19',1,0,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(21,'员工福利费','reimbursement_expense_20',1,0,1,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(22,'业务招待费（政府部门）','reimbursement_expense_21',1,0,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(23,'服务费','reimbursement_expense_22',1,0,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(24,'银行手续费','reimbursement_expense_23',0,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(25,'水电费','reimbursement_expense_24',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-10-16 06:09:04'),
(26,'饮水费','reimbursement_expense_25',1,1,0,0,1,0,'2020-07-30 09:12:13','2020-10-16 06:09:05'),
(27,'快递外包','reimbursement_expense_26',0,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(28,'人力外包','reimbursement_expense_27',0,1,0,0,1,0,'2020-07-30 09:12:13','2020-07-30 09:12:13'),
(29,'跨岛费','reimbursement_expense_28',0,1,0,0,1,0,'2020-09-15 13:41:54','2020-09-15 13:42:25'),
(30,'工作签','reimbursement_expense_29',1,0,0,0,1,0,'2020-10-15 08:51:44','2020-10-15 08:52:06'),
(31,'印花税','reimbursement_expense_30',1,0,0,0,1,0,'2020-10-15 09:43:58','2020-10-15 09:44:25'),
(32,'招聘费','reimbursement_expense_31',1,0,0,0,1,0,'2020-10-15 09:46:10','2020-10-15 09:46:23'),
(33,'宣传费','reimbursement_expense_32',1,0,0,0,1,0,'2020-10-15 09:48:00','2020-10-15 09:48:14'),
(34,'公司活动费','reimbursement_expense_33',1,0,0,0,1,0,'2020-10-15 09:49:27','2020-10-15 09:50:02'),
(35,'公司年会费','reimbursement_expense_34',1,0,0,0,1,0,'2020-10-15 09:51:09','2020-10-15 09:51:21'),
(36,'保洁费','reimbursement_expense_35',0,1,0,0,1,0,'2020-10-15 09:52:59','2020-10-15 09:53:15'),
(37,'牌税','reimbursement_expense_36',0,1,0,0,1,0,'2020-10-15 09:54:44','2020-10-15 09:54:55'),
(38,'安保费','reimbursement_expense_37',0,1,0,0,1,0,'2020-10-15 09:56:33','2020-10-15 09:58:24'),
(39,'物业费','reimbursement_expense_38',0,1,0,0,1,0,'2020-10-15 12:50:07','2020-10-15 12:50:21'),
(40,'房租费','reimbursement_expense_39',0,1,0,0,1,0,'2020-10-15 12:51:02','2020-10-15 12:51:18'),
(41,'探亲费','reimbursement_expense_40',1,1,0,0,1,1,'2020-11-03 06:55:02','2020-11-03 06:58:39');
--  添加reserve_fund_reimburse表
CREATE TABLE `reserve_fund_reimburse` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rfano` varchar(20) NOT NULL DEFAULT '' COMMENT '备用金申请编号',
  `rei_detail_id` int(10) NOT NULL DEFAULT '0' COMMENT '报销详情表id',
  `rei_id` int(11) NOT NULL DEFAULT '0' COMMENT '报销id',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_rfano` (`rfano`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
--  添加reimbursement_detail缺失字段
ALTER TABLE `reimbursement_detail` ADD COLUMN `ledger_account_id` int(11) DEFAULT '0' COMMENT '核算科目id';

UPDATE `workflow_node` SET `flow_id` = 39, `name` = '员工提交', `type` = 1, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 436;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '直线上级', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 3, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 437;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '二级部门负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 5, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 438;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '一级部门负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 6, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 439;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '所属公司负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 16, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 440;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '所属组织负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 7, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 441;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'HRBP Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '62958', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 442;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'CPO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '56780', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 443;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'AP（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = '5', `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 444;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'AP Supervisor（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 445;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'AP Supervisor（北京）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '64377', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 446;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'Finance Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 447;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'Finance Senior Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '54677', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 448;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'Finance Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 449;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'CFO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17152', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 450;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = 'CEO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17008', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 451;
UPDATE `workflow_node` SET `flow_id` = 39, `name` = '结束', `type` = 6, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 452;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = '员工提交', `type` = 1, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 542;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = '网点主管([16]branch supervisor) - 业务线', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 13, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 543;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Manager（District manager）- 业务线', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 14, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 544;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Senior Manager（Reginoal manager）- 业务线', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 15, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 545;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Supervisor - 职能线 - 网络支持部', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 546;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Supervisor_2_(外协) - 职能线 - 网络支持部', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 547;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'TSM-（福利、团结、其他）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 548;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'ATD-（福利、团建、其他）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '34017', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 549;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = '所属公司负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 16, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 550;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'COO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17245', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 551;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'HRBP Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '62958', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 552;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'CPO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '56780', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 553;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'AP（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = '5', `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 554;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'AP Supervisor（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 555;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'AP Supervisor（北京）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '64377', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 556;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Finance Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 557;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Finance Senior Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '54677', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 558;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'Finance Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 559;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'CFO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17152', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 560;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'CEO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17008', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 561;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = '结束', `type` = 6, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 562;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'TSM-（外协）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 601;
UPDATE `workflow_node` SET `flow_id` = 40, `name` = 'ATD-（外协）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '34017', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 602;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = '员工提交', `type` = 1, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 473;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = '网点主管 - MS-网点管理负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 8, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 474;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Senior Manager（area Manager）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 17, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 475;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Supervisor', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 476;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 477;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = '一级部门负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 6, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 478;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = '所属公司负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 16, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 479;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'COO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17245', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 480;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'HRBP Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '62958', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 481;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'CPO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '56780', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 482;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'AP （菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = '5', `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 483;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'AP Supervisor（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 484;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'AP Supervisor（北京）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '64377', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 485;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Finance Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 486;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Finance Senior Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '54677', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 487;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'Finance Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 488;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'CFO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17152', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 489;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = 'CEO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17008', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 490;
UPDATE `workflow_node` SET `flow_id` = 41, `name` = '结束', `type` = 6, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 491;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '员工提交', `type` = 1, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 492;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '直线上级', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 3, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 493;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '网点主管 - MS-网点管理-负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 8, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 494;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'Supervisor - 支持部门', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 495;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'Manager - 支持部门', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 496;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '一级部门负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 6, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 497;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '所属公司负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 16, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 498;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'COO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17245', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 499;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'HRBP Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '62958', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 500;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'CPO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '56780', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 501;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'AP（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = '5', `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 502;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'AP Supervisor（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 503;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'AP Supervisor（北京）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '64377', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 504;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'Finance Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 505;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'Finance Senior Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '54677', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 506;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'Finance Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 507;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'CFO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17152', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 508;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = 'CEO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17008', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 509;
UPDATE `workflow_node` SET `flow_id` = 42, `name` = '结束', `type` = 6, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 510;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = '员工提交', `type` = 1, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 721;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = '网点主管 - MS-网点管理负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 8, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 722;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Senior Manager（area Manager）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 17, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 723;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Supervisor', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 724;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 725;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = '一级部门负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 6, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 726;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = '所属公司负责人', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 16, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 727;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'COO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17245', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 728;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'HRBP Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '62958', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 729;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'CPO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '56780', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 730;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'AP （菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = '5', `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 731;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'AP Supervisor（菲律宾）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 732;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'AP Supervisor（北京）', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '64377', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 733;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Finance Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 734;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Finance Senior Manager', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '54677', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 735;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'Finance Director', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 736;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'CFO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17152', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 737;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = 'CEO', `type` = 3, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = '17008', `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 738;
UPDATE `workflow_node` SET `flow_id` = 57, `name` = '结束', `type` = 6, `node_audit_type` = 0, `expression` = NULL, `approve_next_node_id` = NULL, `reject_next_node_id` = NULL, `audit_type` = NULL, `auditor_type` = 1, `auditor_id` = NULL, `created_at` = NULL, `can_edit_field` = '', `extend_text` = NULL WHERE `id` = 739;

# 删除多余条件判断
DELETE FROM `workflow_node_relate` WHERE `id` = 659;
DELETE FROM `workflow_node_relate` WHERE `id` = 660;
DELETE FROM `workflow_node_relate` WHERE `id` = 661;
# 更新节点关系判断金额
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 436, `to_node_id` = 437, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '员工提交->直线上级', `sort` = 10, `updated_at` = '2021-02-05 09:42:36', `created_at` = '2021-02-05 09:42:36' WHERE `id` = 657;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 437, `to_node_id` = 438, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '直线上级->二级部门负责人', `sort` = 10, `updated_at` = '2021-02-05 09:43:07', `created_at` = '2021-02-05 09:43:07' WHERE `id` = 658;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 438, `to_node_id` = 439, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '其他情况：二级部门负责人->一级部门负责人', `sort` = 10, `updated_at` = '2021-02-05 09:51:54', `created_at` = '2021-02-05 09:51:29' WHERE `id` = 662;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 439, `to_node_id` = 444, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 < 10000: 一级部门负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-11 08:34:23', `created_at` = '2021-02-05 09:57:32' WHERE `id` = 663;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 439, `to_node_id` = 442, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 100000：一级部门负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-11 08:34:25', `created_at` = '2021-02-05 10:02:07' WHERE `id` = 664;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 439, `to_node_id` = 440, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '其他情况：一级部门负责人->所属公司负责人', `sort` = 8, `updated_at` = '2021-06-07 07:43:55', `created_at` = '2021-02-05 10:03:45' WHERE `id` = 665;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 440, `to_node_id` = 444, `valuate_formula` = '$p1 != 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 < 50000: 所属公司负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-11 08:34:26', `created_at` = '2021-02-05 10:07:38' WHERE `id` = 666;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 440, `to_node_id` = 442, `valuate_formula` = '$p1 == 1 && $p2 < 300000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 300000: 所属公司负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-11 08:34:28', `created_at` = '2021-02-05 10:10:50' WHERE `id` = 667;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 440, `to_node_id` = 441, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '其他情况：所属公司负责人->所属组织负责人', `sort` = 8, `updated_at` = '2021-06-07 07:44:03', `created_at` = '2021-02-05 10:12:01' WHERE `id` = 668;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 441, `to_node_id` = 444, `valuate_formula` = '$p1 != 1 && $p2 >= 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 >= 50000: 所属组织负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-11 08:34:29', `created_at` = '2021-02-05 10:14:29' WHERE `id` = 669;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 441, `to_node_id` = 442, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '所属组织负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-07 07:44:15', `created_at` = '2021-02-05 10:16:26' WHERE `id` = 670;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 442, `to_node_id` = 444, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 100000: HRBP Director->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-11 08:34:31', `created_at` = '2021-02-05 10:18:13' WHERE `id` = 671;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 442, `to_node_id` = 443, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'HRBP Director->CPO', `sort` = 9, `updated_at` = '2021-06-07 07:44:29', `created_at` = '2021-02-05 10:19:46' WHERE `id` = 672;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 443, `to_node_id` = 444, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CPO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-05 10:21:15', `created_at` = '2021-02-05 10:21:15' WHERE `id` = 673;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 444, `to_node_id` = 445, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP（菲律宾）-> AP Supervisor（菲律宾）', `sort` = 10, `updated_at` = '2021-02-05 10:22:07', `created_at` = '2021-02-05 10:22:07' WHERE `id` = 674;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 445, `to_node_id` = 446, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（菲律宾）->AP Supervisor（北京）', `sort` = 10, `updated_at` = '2021-02-05 10:22:49', `created_at` = '2021-02-05 10:22:49' WHERE `id` = 675;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 446, `to_node_id` = 452, `valuate_formula` = '$p1 != 1 && $p2 < 3000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 < 2000: AP Supervisor（北京）-> 结束', `sort` = 10, `updated_at` = '2021-06-11 08:34:36', `created_at` = '2021-02-05 10:24:08' WHERE `id` = 676;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 446, `to_node_id` = 452, `valuate_formula` = '$p1 == 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 10000: AP Supervisor（北京）-> 结束', `sort` = 9, `updated_at` = '2021-06-11 08:34:38', `created_at` = '2021-02-05 10:26:10' WHERE `id` = 677;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 446, `to_node_id` = 447, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '其他情况：AP Supervisor（北京）-> Finance Manager', `sort` = 8, `updated_at` = '2021-06-07 07:44:43', `created_at` = '2021-02-05 10:27:39' WHERE `id` = 678;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 447, `to_node_id` = 448, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Manager->Finance Senior Manager', `sort` = 10, `updated_at` = '2021-02-05 10:29:13', `created_at` = '2021-02-05 10:29:13' WHERE `id` = 679;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 448, `to_node_id` = 452, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 < 10000: Finance Senior Manager->结束', `sort` = 10, `updated_at` = '2021-06-11 08:34:39', `created_at` = '2021-02-05 10:30:30' WHERE `id` = 680;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 448, `to_node_id` = 452, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 100000: Finance Senior Manager->结束', `sort` = 9, `updated_at` = '2021-06-11 08:34:41', `created_at` = '2021-02-05 10:31:02' WHERE `id` = 681;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 448, `to_node_id` = 449, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Senior Manager->Finance Director', `sort` = 8, `updated_at` = '2021-06-07 07:44:58', `created_at` = '2021-02-05 10:32:19' WHERE `id` = 682;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 449, `to_node_id` = 452, `valuate_formula` = '$p1 != 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利/团建费 且 金额 < 50000: Finance Director->结束', `sort` = 10, `updated_at` = '2021-06-11 08:34:46', `created_at` = '2021-02-05 10:33:19' WHERE `id` = 683;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 449, `to_node_id` = 452, `valuate_formula` = '$p1 == 1 && $p2 < 300000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 < 300000: Finance Director->结束', `sort` = 9, `updated_at` = '2021-06-11 08:34:49', `created_at` = '2021-02-05 10:34:55' WHERE `id` = 684;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 449, `to_node_id` = 450, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Director->CFO', `sort` = 8, `updated_at` = '2021-06-07 07:46:23', `created_at` = '2021-02-05 10:36:02' WHERE `id` = 685;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 450, `to_node_id` = 451, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CFO->CEO', `sort` = 10, `updated_at` = '2021-02-05 10:36:28', `created_at` = '2021-02-05 10:36:28' WHERE `id` = 686;
UPDATE `workflow_node_relate` SET `flow_id` = 39, `from_node_id` = 451, `to_node_id` = 452, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CEO->结束', `sort` = 10, `updated_at` = '2021-02-05 10:36:47', `created_at` = '2021-02-05 10:36:47' WHERE `id` = 687;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 542, `to_node_id` = 543, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '员工提交->网点主管', `sort` = 10, `updated_at` = '2021-02-20 09:35:07', `created_at` = '2021-02-20 09:35:07' WHERE `id` = 844;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 543, `to_node_id` = 547, `valuate_formula` = '$p1 == 2 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '外协 且 金额 < 1000: 网点主管->Supervisor_2', `sort` = 10, `updated_at` = '2021-06-16 03:32:27', `created_at` = '2021-02-20 09:36:52' WHERE `id` = 845;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 543, `to_node_id` = 546, `valuate_formula` = '$p1 < 1500000', `valuate_code` = 'getAmount', `remark` = '金额 < 1000:网点主管->Supervisor', `sort` = 9, `updated_at` = '2021-06-16 03:32:28', `created_at` = '2021-02-20 09:38:22' WHERE `id` = 846;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 543, `to_node_id` = 544, `valuate_formula` = '', `valuate_code` = NULL, `remark` = '网点主管->Manager', `sort` = 8, `updated_at` = '2021-06-07 07:46:54', `created_at` = '2021-02-20 09:38:54' WHERE `id` = 847;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 544, `to_node_id` = 547, `valuate_formula` = '$p1 == 2 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '外协 且 金额 < 3000:Manager->Supervisor_2', `sort` = 10, `updated_at` = '2021-06-16 03:32:30', `created_at` = '2021-02-20 09:39:19' WHERE `id` = 848;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 544, `to_node_id` = 546, `valuate_formula` = '$p1 < 4500000', `valuate_code` = 'getAmount', `remark` = '金额 < 3000:Manager->Supervisor', `sort` = 9, `updated_at` = '2021-06-16 03:32:32', `created_at` = '2021-02-20 09:47:00' WHERE `id` = 849;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 544, `to_node_id` = 545, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Manager->Senior Manager', `sort` = 8, `updated_at` = '2021-06-07 07:46:58', `created_at` = '2021-02-20 09:47:21' WHERE `id` = 850;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 545, `to_node_id` = 547, `valuate_formula` = '$p1 == 2', `valuate_code` = 'getReimbursementType', `remark` = '外协：Senior Manager->Supervisor_2', `sort` = 10, `updated_at` = '2021-02-20 09:51:03', `created_at` = '2021-02-20 09:50:13' WHERE `id` = 851;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 545, `to_node_id` = 546, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Senior Manager->Supervisor', `sort` = 9, `updated_at` = '2021-06-07 07:47:04', `created_at` = '2021-02-20 09:50:43' WHERE `id` = 852;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 546, `to_node_id` = 552, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '福利费/团建费 且 金额 < 3000:Supervisor->HRBP Director', `sort` = 10, `updated_at` = '2021-06-16 03:32:33', `created_at` = '2021-02-20 09:53:19' WHERE `id` = 853;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 546, `to_node_id` = 554, `valuate_formula` = '$p1 == 0 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '其他 且 金额 < 3000:Supervisor->AP（菲律宾）', `sort` = 9, `updated_at` = '2021-06-16 03:32:35', `created_at` = '2021-02-20 09:54:10' WHERE `id` = 854;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 546, `to_node_id` = 548, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '非外协：Supervisor->Manager', `sort` = 8, `updated_at` = '2021-06-07 07:47:10', `created_at` = '2021-02-20 09:54:58' WHERE `id` = 855;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 547, `to_node_id` = 554, `valuate_formula` = '$p1 < 4500000', `valuate_code` = 'getAmount', `remark` = '外协: 金额 < 3000:Supervisor_2->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:32:36', `created_at` = '2021-02-20 09:56:43' WHERE `id` = 856;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 547, `to_node_id` = 601, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '外协：Supervisor_2->Manager', `sort` = 9, `updated_at` = '2021-06-07 07:47:13', `created_at` = '2021-02-20 09:57:18' WHERE `id` = 857;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 548, `to_node_id` = 554, `valuate_formula` = 'in_array($p1, [2,0]) && $p2 < 7500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '外协/其他 且 金额 < 5000:Manager->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:32:37', `created_at` = '2021-02-20 10:04:56' WHERE `id` = 858;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 548, `to_node_id` = 552, `valuate_formula` = '$p1 == 1 && $p2 < 7500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '福利费/团建费 且 金额 < 5000:Manager->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:32:39', `created_at` = '2021-02-20 10:06:43' WHERE `id` = 859;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 548, `to_node_id` = 549, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Manager->/', `sort` = 8, `updated_at` = '2021-06-07 07:47:16', `created_at` = '2021-02-20 10:06:53' WHERE `id` = 860;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 549, `to_node_id` = 554, `valuate_formula` = 'in_array($p1, [2,0]) && $p2 < 30000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '外协/其他 且 金额 < 300000:/->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:32:44', `created_at` = '2021-02-20 10:09:47' WHERE `id` = 861;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 549, `to_node_id` = 552, `valuate_formula` = '$p1 == 1 && $p2 < 30000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '福利费/团建费 且 金额 < 300000:/->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:32:45', `created_at` = '2021-02-20 10:11:07' WHERE `id` = 862;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 549, `to_node_id` = 550, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '/->所属公司负责人', `sort` = 8, `updated_at` = '2021-06-07 07:47:30', `created_at` = '2021-02-20 10:11:34' WHERE `id` = 863;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 550, `to_node_id` = 554, `valuate_formula` = 'in_array($p1, [2, 0]) && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '外协/其他 且 金额 < 100000:所属公司负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:32:47', `created_at` = '2021-02-20 10:14:27' WHERE `id` = 864;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 550, `to_node_id` = 552, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:32:49', `created_at` = '2021-02-20 10:15:40' WHERE `id` = 865;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 550, `to_node_id` = 551, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '所属公司负责人->COO', `sort` = 8, `updated_at` = '2021-06-07 07:47:38', `created_at` = '2021-02-20 10:16:16' WHERE `id` = 866;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 551, `to_node_id` = 554, `valuate_formula` = 'in_array($p1, [2, 0])', `valuate_code` = 'getReimbursementType', `remark` = '外协/其他：COO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-20 11:21:51', `created_at` = '2021-02-20 10:18:28' WHERE `id` = 867;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 551, `to_node_id` = 552, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'COO->HRBP Director', `sort` = 9, `updated_at` = '2021-06-07 07:47:46', `created_at` = '2021-02-20 11:21:03' WHERE `id` = 868;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 552, `to_node_id` = 554, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:32:58', `created_at` = '2021-02-20 11:23:47' WHERE `id` = 869;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 552, `to_node_id` = 553, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'HRBP Director->CPO', `sort` = 9, `updated_at` = '2021-06-07 07:47:54', `created_at` = '2021-02-20 11:24:34' WHERE `id` = 870;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 553, `to_node_id` = 554, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CPO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-20 11:25:31', `created_at` = '2021-02-20 11:25:31' WHERE `id` = 871;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 554, `to_node_id` = 555, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP（菲律宾）->AP Supervisor（菲律宾）', `sort` = 10, `updated_at` = '2021-02-20 11:26:08', `created_at` = '2021-02-20 11:26:08' WHERE `id` = 872;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 555, `to_node_id` = 556, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（菲律宾）->AP Supervisor（北京）', `sort` = 10, `updated_at` = '2021-02-20 11:26:43', `created_at` = '2021-02-20 11:26:43' WHERE `id` = 873;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 556, `to_node_id` = 562, `valuate_formula` = '$p1 < 1500000', `valuate_code` = 'getAmount', `remark` = '金额 < 1000:AP Supervisor（北京）->结束', `sort` = 10, `updated_at` = '2021-06-16 03:33:00', `created_at` = '2021-02-22 01:50:52' WHERE `id` = 874;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 556, `to_node_id` = 557, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（北京）->Finance Manager', `sort` = 9, `updated_at` = '2021-06-07 07:49:56', `created_at` = '2021-02-22 01:51:47' WHERE `id` = 875;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 557, `to_node_id` = 562, `valuate_formula` = '$p1 < 4500000', `valuate_code` = 'getAmount', `remark` = '金额 < 3000:Finance Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:33:03', `created_at` = '2021-02-22 01:52:48' WHERE `id` = 876;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 557, `to_node_id` = 558, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Manager->Finance Senior Manager', `sort` = 9, `updated_at` = '2021-06-07 07:50:00', `created_at` = '2021-02-22 01:53:14' WHERE `id` = 877;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 558, `to_node_id` = 562, `valuate_formula` = '$p1 < 7500000', `valuate_code` = 'getAmount', `remark` = '金额 < 5000:Finance Senior Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:33:05', `created_at` = '2021-02-22 01:54:13' WHERE `id` = 878;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 558, `to_node_id` = 559, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Senior Manager->Fiance Director', `sort` = 9, `updated_at` = '2021-06-07 07:50:03', `created_at` = '2021-02-22 01:54:50' WHERE `id` = 879;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 559, `to_node_id` = 562, `valuate_formula` = '$p1 < 30000000', `valuate_code` = 'getAmount', `remark` = '金额 < 300000:Finance Director->结束', `sort` = 10, `updated_at` = '2021-06-16 03:33:09', `created_at` = '2021-02-22 01:56:06' WHERE `id` = 880;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 559, `to_node_id` = 560, `valuate_formula` = '$p1 >= 30000000', `valuate_code` = 'getAmount', `remark` = 'Finance Director->CFO', `sort` = 9, `updated_at` = '2021-06-16 03:33:10', `created_at` = '2021-02-22 01:56:24' WHERE `id` = 881;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 560, `to_node_id` = 562, `valuate_formula` = '$p1 < 150000000', `valuate_code` = 'getAmount', `remark` = '金额 < 100000:CFO->结束', `sort` = 10, `updated_at` = '2021-06-16 03:33:11', `created_at` = '2021-02-22 01:57:19' WHERE `id` = 882;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 560, `to_node_id` = 561, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CFO->CEO', `sort` = 9, `updated_at` = '2021-06-07 07:50:09', `created_at` = '2021-02-22 01:57:32' WHERE `id` = 883;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 561, `to_node_id` = 562, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CEO->结束', `sort` = 10, `updated_at` = '2021-02-22 01:57:50', `created_at` = '2021-02-22 01:57:50' WHERE `id` = 884;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 601, `to_node_id` = 554, `valuate_formula` = '$p1 < 7500000', `valuate_code` = 'getAmount', `remark` = NULL, `sort` = 10, `updated_at` = '2021-06-16 03:33:12', `created_at` = '2021-04-20 11:33:43' WHERE `id` = 958;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 601, `to_node_id` = 602, `valuate_formula` = '$p1 >= 7500000', `valuate_code` = 'getAmount', `remark` = NULL, `sort` = 9, `updated_at` = '2021-06-16 03:33:13', `created_at` = '2021-04-20 11:33:43' WHERE `id` = 959;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 602, `to_node_id` = 554, `valuate_formula` = '$p1 < 30000000', `valuate_code` = 'getAmount', `remark` = NULL, `sort` = 10, `updated_at` = '2021-06-16 03:33:15', `created_at` = '2021-04-20 11:33:44' WHERE `id` = 960;
UPDATE `workflow_node_relate` SET `flow_id` = 40, `from_node_id` = 602, `to_node_id` = 550, `valuate_formula` = '$p1 >= 30000000', `valuate_code` = 'getAmount', `remark` = NULL, `sort` = 9, `updated_at` = '2021-06-16 03:33:18', `created_at` = '2021-04-20 11:33:44' WHERE `id` = 961;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 473, `to_node_id` = 474, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '员工提交->网点主管', `sort` = 10, `updated_at` = '2021-02-19 08:09:53', `created_at` = '2021-02-19 08:09:53' WHERE `id` = 770;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 474, `to_node_id` = 476, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000: 网点主管->Supervisor', `sort` = 10, `updated_at` = '2021-06-16 03:33:33', `created_at` = '2021-02-19 08:11:41' WHERE `id` = 771;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 474, `to_node_id` = 476, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000: 网点主管->Supervisor', `sort` = 9, `updated_at` = '2021-06-16 03:33:34', `created_at` = '2021-02-19 08:12:16' WHERE `id` = 772;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 474, `to_node_id` = 475, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '网点主管->Senior Manager', `sort` = 8, `updated_at` = '2021-06-07 07:52:53', `created_at` = '2021-02-19 08:12:44' WHERE `id` = 773;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 475, `to_node_id` = 476, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Senior Manager->Supervisor', `sort` = 10, `updated_at` = '2021-02-19 08:16:35', `created_at` = '2021-02-19 08:16:35' WHERE `id` = 774;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 476, `to_node_id` = 483, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000: Supervisor->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:33:35', `created_at` = '2021-02-19 08:25:49' WHERE `id` = 775;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 476, `to_node_id` = 481, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000:Supervisor->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:33:37', `created_at` = '2021-02-19 08:37:02' WHERE `id` = 776;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 476, `to_node_id` = 477, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Supervisor->Manager', `sort` = 8, `updated_at` = '2021-06-07 07:53:41', `created_at` = '2021-02-19 08:41:03' WHERE `id` = 777;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 477, `to_node_id` = 483, `valuate_formula` = '$p1 != 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 3000:Manager->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:33:39', `created_at` = '2021-02-19 08:54:43' WHERE `id` = 778;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 477, `to_node_id` = 481, `valuate_formula` = '$p1 == 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 10000:Manager->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:33:40', `created_at` = '2021-02-19 08:55:48' WHERE `id` = 779;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 477, `to_node_id` = 478, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Manager->一级部门负责人', `sort` = 8, `updated_at` = '2021-06-07 07:53:48', `created_at` = '2021-02-19 08:56:17' WHERE `id` = 780;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 478, `to_node_id` = 483, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 10000:一级部门负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:33:41', `created_at` = '2021-02-19 08:58:04' WHERE `id` = 781;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 478, `to_node_id` = 481, `valuate_formula` = '$p1 == 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 50000:一级部门负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:33:43', `created_at` = '2021-02-19 08:59:02' WHERE `id` = 782;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 478, `to_node_id` = 479, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '一级部门负责人->所属公司负责人', `sort` = 8, `updated_at` = '2021-06-07 07:54:00', `created_at` = '2021-02-19 08:59:20' WHERE `id` = 783;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 479, `to_node_id` = 483, `valuate_formula` = '$p1 != 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 50000:所属公司负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:33:44', `created_at` = '2021-02-19 09:02:06' WHERE `id` = 784;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 479, `to_node_id` = 481, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:33:46', `created_at` = '2021-02-19 09:02:56' WHERE `id` = 785;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 479, `to_node_id` = 480, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '所属公司负责人->COO', `sort` = 8, `updated_at` = '2021-06-07 07:54:06', `created_at` = '2021-02-19 09:03:15' WHERE `id` = 786;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 480, `to_node_id` = 483, `valuate_formula` = '$p1 != 1', `valuate_code` = 'getReimbursementType', `remark` = '非员工福利费/团建费：COO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-20 08:02:25', `created_at` = '2021-02-19 09:05:30' WHERE `id` = 787;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 480, `to_node_id` = 481, `valuate_formula` = '$p1 == 1', `valuate_code` = 'getReimbursementType', `remark` = '员工福利费/团建费：COO->HRBP Director', `sort` = 9, `updated_at` = '2021-06-07 07:54:12', `created_at` = '2021-02-19 09:06:01' WHERE `id` = 788;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 481, `to_node_id` = 483, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:33:58', `created_at` = '2021-02-19 09:07:45' WHERE `id` = 789;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 481, `to_node_id` = 482, `valuate_formula` = '', `valuate_code` = '', `remark` = 'HRBP Director->CPO', `sort` = 9, `updated_at` = '2021-06-07 07:54:22', `created_at` = '2021-02-19 09:08:27' WHERE `id` = 790;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 482, `to_node_id` = 483, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CPO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-19 09:11:02', `created_at` = '2021-02-19 09:11:02' WHERE `id` = 791;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 483, `to_node_id` = 484, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP（菲律宾）->AP Supervisor（菲律宾）', `sort` = 10, `updated_at` = '2021-02-19 09:11:36', `created_at` = '2021-02-19 09:11:36' WHERE `id` = 792;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 484, `to_node_id` = 485, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（菲律宾）->AP Supervisor（北京）', `sort` = 10, `updated_at` = '2021-02-19 09:12:00', `created_at` = '2021-02-19 09:12:00' WHERE `id` = 793;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 485, `to_node_id` = 491, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000: AP Supervisor（北京）->结束', `sort` = 10, `updated_at` = '2021-06-16 03:34:02', `created_at` = '2021-02-19 09:13:25' WHERE `id` = 794;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 485, `to_node_id` = 486, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（北京）->Finance Manager', `sort` = 9, `updated_at` = '2021-06-07 07:54:26', `created_at` = '2021-02-19 09:14:18' WHERE `id` = 795;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 486, `to_node_id` = 491, `valuate_formula` = '$p1 != 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 3000:Finance Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:34:03', `created_at` = '2021-02-19 09:15:35' WHERE `id` = 796;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 486, `to_node_id` = 491, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000:Finance Manager->结束', `sort` = 9, `updated_at` = '2021-06-16 03:34:09', `created_at` = '2021-02-19 09:16:30' WHERE `id` = 797;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 486, `to_node_id` = 487, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Manager->Finance Senior Manager', `sort` = 8, `updated_at` = '2021-06-07 07:54:29', `created_at` = '2021-02-19 09:17:06' WHERE `id` = 798;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 487, `to_node_id` = 491, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:34:14', `created_at` = '2021-02-19 09:19:04' WHERE `id` = 799;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 487, `to_node_id` = 491, `valuate_formula` = '$p1 == 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 10000: Finance Senior Manager->结束', `sort` = 9, `updated_at` = '2021-06-16 03:34:16', `created_at` = '2021-02-19 09:19:53' WHERE `id` = 800;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 487, `to_node_id` = 488, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Senior Manager->Finance Director', `sort` = 8, `updated_at` = '2021-06-07 07:54:54', `created_at` = '2021-02-19 09:20:21' WHERE `id` = 801;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 488, `to_node_id` = 491, `valuate_formula` = '$p1 != 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 50000:Finance Director->结束', `sort` = 10, `updated_at` = '2021-06-16 03:34:17', `created_at` = '2021-02-19 09:23:24' WHERE `id` = 802;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 488, `to_node_id` = 491, `valuate_formula` = '$p1 == 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 50000:Finance Director->结束', `sort` = 9, `updated_at` = '2021-06-16 03:34:21', `created_at` = '2021-02-19 09:24:10' WHERE `id` = 803;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 488, `to_node_id` = 489, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Director->CFO', `sort` = 8, `updated_at` = '2021-06-07 07:55:09', `created_at` = '2021-02-19 09:24:29' WHERE `id` = 804;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 489, `to_node_id` = 491, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:CFO->结束', `sort` = 10, `updated_at` = '2021-06-16 03:34:22', `created_at` = '2021-02-19 09:28:39' WHERE `id` = 805;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 489, `to_node_id` = 490, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CFO->CEO', `sort` = 9, `updated_at` = '2021-06-07 07:55:12', `created_at` = '2021-02-19 09:28:56' WHERE `id` = 806;
UPDATE `workflow_node_relate` SET `flow_id` = 41, `from_node_id` = 490, `to_node_id` = 491, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CEO->结束', `sort` = 10, `updated_at` = '2021-02-19 09:29:15', `created_at` = '2021-02-19 09:29:15' WHERE `id` = 807;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 492, `to_node_id` = 493, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '员工提交->直线上级', `sort` = 10, `updated_at` = '2021-02-19 10:22:26', `created_at` = '2021-02-19 10:22:26' WHERE `id` = 808;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 493, `to_node_id` = 495, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000: 直线上级->Supervisor', `sort` = 10, `updated_at` = '2021-06-16 03:34:50', `created_at` = '2021-02-19 10:23:42' WHERE `id` = 809;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 493, `to_node_id` = 495, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000:直线上级->Supervisor', `sort` = 9, `updated_at` = '2021-06-16 03:34:54', `created_at` = '2021-02-19 10:24:03' WHERE `id` = 810;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 493, `to_node_id` = 494, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '直线上级->网点主管', `sort` = 8, `updated_at` = '2021-06-07 07:56:30', `created_at` = '2021-02-19 10:24:12' WHERE `id` = 811;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 494, `to_node_id` = 495, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '网点主管->Supervisor', `sort` = 10, `updated_at` = '2021-02-19 10:26:10', `created_at` = '2021-02-19 10:26:10' WHERE `id` = 812;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 495, `to_node_id` = 497, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000: Supervisor->一级部门负责人', `sort` = 10, `updated_at` = '2021-06-16 03:34:55', `created_at` = '2021-02-19 10:26:51' WHERE `id` = 813;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 495, `to_node_id` = 497, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000:Supervisor->一级部门负责人', `sort` = 9, `updated_at` = '2021-06-16 03:34:56', `created_at` = '2021-02-19 10:27:22' WHERE `id` = 814;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 495, `to_node_id` = 496, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Supervisor->Manager', `sort` = 8, `updated_at` = '2021-06-07 07:56:36', `created_at` = '2021-02-19 10:27:49' WHERE `id` = 815;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 496, `to_node_id` = 497, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Manager->一级部门负责人', `sort` = 10, `updated_at` = '2021-02-19 10:28:23', `created_at` = '2021-02-19 10:28:23' WHERE `id` = 816;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 497, `to_node_id` = 502, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 10000:一级部门负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:34:57', `created_at` = '2021-02-19 10:30:07' WHERE `id` = 817;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 497, `to_node_id` = 500, `valuate_formula` = '$p1 == 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 50000:一级部门负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:34:58', `created_at` = '2021-02-19 10:31:06' WHERE `id` = 818;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 497, `to_node_id` = 498, `valuate_formula` = '(($p1 == 1 && $p2 >= 75000000)||($p1!=1 && $p2>=15000000))', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '一级部门负责人->所属公司负责人', `sort` = 8, `updated_at` = '2021-06-16 03:35:04', `created_at` = '2021-02-19 10:31:51' WHERE `id` = 819;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 498, `to_node_id` = 502, `valuate_formula` = '($p1 != 1 && $p2 < 75000000)', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 50000:所属公司负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:35:06', `created_at` = '2021-02-19 10:32:54' WHERE `id` = 820;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 498, `to_node_id` = 500, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:所属公司负责人->HRBP Director', `sort` = 9, `updated_at` = '2021-06-16 03:35:07', `created_at` = '2021-02-19 10:33:31' WHERE `id` = 821;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 498, `to_node_id` = 499, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '所属公司负责人->COO', `sort` = 8, `updated_at` = '2021-06-07 07:56:56', `created_at` = '2021-02-19 10:33:51' WHERE `id` = 822;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 499, `to_node_id` = 502, `valuate_formula` = '$p1 != 1', `valuate_code` = 'getReimbursementType', `remark` = '非员工福利费/团建费:COO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-20 07:26:00', `created_at` = '2021-02-19 10:34:48' WHERE `id` = 823;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 499, `to_node_id` = 500, `valuate_formula` = '$p1 == 1', `valuate_code` = 'getReimbursementType', `remark` = '员工福利费/团建费:COO->HRBP Director', `sort` = 9, `updated_at` = '2021-06-07 07:56:57', `created_at` = '2021-02-19 10:35:30' WHERE `id` = 824;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 500, `to_node_id` = 502, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:HRBP Director->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 03:35:10', `created_at` = '2021-02-19 10:36:19' WHERE `id` = 825;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 500, `to_node_id` = 501, `valuate_formula` = '$p1 == 1 && $p2 >= 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利/团建费 且 金额 >= 100000:HRBP Director->CPO', `sort` = 9, `updated_at` = '2021-06-16 03:35:11', `created_at` = '2021-02-19 10:36:55' WHERE `id` = 826;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 501, `to_node_id` = 502, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CPO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-02-19 10:37:19', `created_at` = '2021-02-19 10:37:19' WHERE `id` = 827;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 502, `to_node_id` = 503, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP（菲律宾）->AP Supervisor（菲律宾）', `sort` = 10, `updated_at` = '2021-02-19 10:37:43', `created_at` = '2021-02-19 10:37:43' WHERE `id` = 828;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 503, `to_node_id` = 504, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（菲律宾）->AP Supervisor（北京）', `sort` = 10, `updated_at` = '2021-02-19 10:38:16', `created_at` = '2021-02-19 10:38:16' WHERE `id` = 829;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 504, `to_node_id` = 510, `valuate_formula` = '$p1 != 1 && $p2 < 1500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 1000:AP Supervisor（北京）->结束', `sort` = 10, `updated_at` = '2021-06-16 03:35:14', `created_at` = '2021-02-19 10:39:12' WHERE `id` = 830;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 504, `to_node_id` = 505, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（北京）->Finance Manager', `sort` = 9, `updated_at` = '2021-06-07 07:57:23', `created_at` = '2021-02-19 10:39:45' WHERE `id` = 831;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 505, `to_node_id` = 510, `valuate_formula` = '$p1 != 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 3000:Finance Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:35:15', `created_at` = '2021-02-19 10:40:57' WHERE `id` = 832;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 505, `to_node_id` = 510, `valuate_formula` = '$p1 == 1 && $p2 < 4500000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 3000:Finance Manager->结束', `sort` = 9, `updated_at` = '2021-06-16 03:35:17', `created_at` = '2021-02-19 10:41:31' WHERE `id` = 833;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 505, `to_node_id` = 506, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Manager->Finance Senior Manager', `sort` = 8, `updated_at` = '2021-06-07 07:57:28', `created_at` = '2021-02-19 10:41:55' WHERE `id` = 834;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 506, `to_node_id` = 510, `valuate_formula` = '$p1 != 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 03:35:18', `created_at` = '2021-02-19 10:42:27' WHERE `id` = 835;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 506, `to_node_id` = 510, `valuate_formula` = '$p1 == 1 && $p2 < 15000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 10000:Finance Senior Manager->结束', `sort` = 9, `updated_at` = '2021-06-16 03:35:34', `created_at` = '2021-02-19 10:42:36' WHERE `id` = 836;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 506, `to_node_id` = 507, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Senior Manager->Fiance Director', `sort` = 8, `updated_at` = '2021-06-07 07:57:41', `created_at` = '2021-02-19 10:42:43' WHERE `id` = 837;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 507, `to_node_id` = 510, `valuate_formula` = '$p1 != 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '非员工福利费/团建费 且 金额 < 50000:Finance Director->结束', `sort` = 10, `updated_at` = '2021-06-16 03:35:35', `created_at` = '2021-02-19 10:44:21' WHERE `id` = 838;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 507, `to_node_id` = 510, `valuate_formula` = '$p1 == 1 && $p2 < 75000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 50000:Finance Director->结束', `sort` = 9, `updated_at` = '2021-06-16 03:35:37', `created_at` = '2021-02-19 10:44:29' WHERE `id` = 839;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 507, `to_node_id` = 508, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Director->CFO', `sort` = 8, `updated_at` = '2021-06-07 07:57:46', `created_at` = '2021-02-19 10:44:36' WHERE `id` = 840;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 508, `to_node_id` = 510, `valuate_formula` = '$p1 == 1 && $p2 < 150000000', `valuate_code` = 'getReimbursementType,getAmount', `remark` = '员工福利费/团建费 且 金额 < 100000:CFO->结束', `sort` = 10, `updated_at` = '2021-06-16 03:35:39', `created_at` = '2021-02-19 10:46:28' WHERE `id` = 841;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 508, `to_node_id` = 509, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CFO->CEO', `sort` = 9, `updated_at` = '2021-06-07 07:57:51', `created_at` = '2021-02-19 10:46:38' WHERE `id` = 842;
UPDATE `workflow_node_relate` SET `flow_id` = 42, `from_node_id` = 509, `to_node_id` = 510, `valuate_formula` = '', `valuate_code` = NULL, `remark` = 'CEO->结束', `sort` = 10, `updated_at` = '2021-06-06 14:32:45', `created_at` = '2021-02-19 10:47:32' WHERE `id` = 843;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 721, `to_node_id` = 722, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '员工提交->网点主管', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1111;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 722, `to_node_id` = 724, `valuate_formula` = '$p1 < 1500000', `valuate_code` = 'getAmount', `remark` = '金额 < 1500: 网点主管->Supervisor', `sort` = 10, `updated_at` = '2021-06-16 07:12:14', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1112;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 722, `to_node_id` = 723, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '网点主管->Senior Manager', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1113;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 723, `to_node_id` = 724, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Senior Manager->Supervisor', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1114;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 724, `to_node_id` = 731, `valuate_formula` = '$p1 < 1500000', `valuate_code` = 'getAmount', `remark` = '金额 < 1500: Supervisor->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 07:12:26', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1115;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 724, `to_node_id` = 725, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Supervisor->Manager', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1116;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 725, `to_node_id` = 731, `valuate_formula` = '$p1 < 4500000', `valuate_code` = 'getAmount', `remark` = ' 金额 < 4500:Manager->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 07:13:11', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1117;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 725, `to_node_id` = 726, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Manager->一级部门负责人', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1118;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 726, `to_node_id` = 731, `valuate_formula` = '$p1 < 15000000', `valuate_code` = 'getAmount', `remark` = '金额 < 15000:一级部门负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 07:13:42', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1119;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 726, `to_node_id` = 727, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '一级部门负责人->所属公司负责人', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1120;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 727, `to_node_id` = 731, `valuate_formula` = '$p1 < 75000000', `valuate_code` = 'getAmount', `remark` = '金额 < 75000:所属公司负责人->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-06-16 07:14:02', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1121;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 727, `to_node_id` = 728, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = '所属公司负责人->COO', `sort` = 10, `updated_at` = '2021-02-19 09:03:15', `created_at` = '2021-02-19 09:03:15' WHERE `id` = 1122;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 728, `to_node_id` = 731, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'COO->AP（菲律宾）', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1123;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 731, `to_node_id` = 732, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP（菲律宾）->AP Supervisor（菲律宾）', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1124;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 732, `to_node_id` = 733, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（菲律宾）->AP Supervisor（北京）', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1125;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 733, `to_node_id` = 739, `valuate_formula` = '$p1 < 1500000', `valuate_code` = 'getAmount', `remark` = '金额 < 1500: AP Supervisor（北京）->结束', `sort` = 10, `updated_at` = '2021-06-16 07:14:24', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1126;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 733, `to_node_id` = 734, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'AP Supervisor（北京）->Finance Manager', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1127;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 734, `to_node_id` = 739, `valuate_formula` = '$p1 < 4500000', `valuate_code` = 'getAmount', `remark` = '金额 < 4500:Finance Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 07:14:33', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1128;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 734, `to_node_id` = 735, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Manager->Finance Senior Manager', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1129;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 735, `to_node_id` = 739, `valuate_formula` = '$p1 < 15000000', `valuate_code` = 'getAmount', `remark` = '金额 < 15000:Finance Senior Manager->结束', `sort` = 10, `updated_at` = '2021-06-16 07:14:43', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1130;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 735, `to_node_id` = 736, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Senior Manager->Finance Director', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1131;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 736, `to_node_id` = 739, `valuate_formula` = '$p1 < 75000000', `valuate_code` = 'getAmount', `remark` = '金额 < 75000:Finance Director->结束', `sort` = 10, `updated_at` = '2021-06-16 07:14:56', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1132;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 736, `to_node_id` = 737, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'Finance Director->CFO', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1133;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 737, `to_node_id` = 738, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CFO->CEO', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1134;
UPDATE `workflow_node_relate` SET `flow_id` = 57, `from_node_id` = 738, `to_node_id` = 739, `valuate_formula` = NULL, `valuate_code` = NULL, `remark` = 'CEO->结束', `sort` = 10, `updated_at` = '2021-05-19 14:31:51', `created_at` = '2021-05-19 14:31:51' WHERE `id` = 1135;

--  普通付款（总部）
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 429;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 431;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 433;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 434;
--  普通付款（Network网点）
UPDATE `workflow_node` SET `auditor_id`='34017' WHERE `id` = 610;
UPDATE `workflow_node` SET `auditor_id`='34017' WHERE `id` = 612;
UPDATE `workflow_node` SET `auditor_id`='17245' WHERE `id` = 614;
UPDATE `workflow_node` SET `auditor_id`='62958' WHERE `id` = 615;
UPDATE `workflow_node` SET `auditor_id`='56780' WHERE `id` = 616;
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 619;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 621;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 623;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 624;
--  普通付款（门店shop）
UPDATE `workflow_node` SET `auditor_id`='17245' WHERE `id` = 633;
UPDATE `workflow_node` SET `auditor_id`='62958' WHERE `id` = 634;
UPDATE `workflow_node` SET `auditor_id`='56780' WHERE `id` = 635;
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 638;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 640;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 642;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 643;
--  普通付款（hub网点）
UPDATE `workflow_node` SET `auditor_id`='17245' WHERE `id` = 653;
UPDATE `workflow_node` SET `auditor_id`='62958' WHERE `id` = 654;
UPDATE `workflow_node` SET `auditor_id`='56780' WHERE `id` = 655;
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 658;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 660;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 662;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 663;
--  网点租房合同
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 360;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 362;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 364;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 365;
--  网点付款
UPDATE `workflow_node` SET `auditor_id`='64377' WHERE `id` = 387;
UPDATE `workflow_node` SET `auditor_id`='54677' WHERE `id` = 389;
UPDATE `workflow_node` SET `auditor_id`='17152' WHERE `id` = 391;
UPDATE `workflow_node` SET `auditor_id`='17008' WHERE `id` = 392;