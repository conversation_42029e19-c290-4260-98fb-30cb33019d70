-- PH OA数据库执行
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (260, 'action.wage.apply.download', '薪资发放审批-我的申请-下载', null, 240, 2, 5, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (261, 'menu.transfer', '转岗管理', null, 0, 1, 1, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (262, 'menu.transfer.apply', '转岗管理-申请', null, 261, 1, 1, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (263, 'action.transfer.apply.add', '转岗管理-批量转岗申请', null, 262, 2, 1, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (264, 'action.transfer.apply.search', '转岗管理-批量转岗查询', null, 262, 2, 2, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (265, 'action.transfer.apply.view', '转岗管理-批量转岗查看', null, 262, 2, 3, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (266, 'action.transfer.apply.flow', '转岗管理-批量转岗流程', null, 262, 2, 4, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (267, 'action.transfer.apply.export', '转岗管理-批量转岗导出', null, 262, 2, 5, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (268, 'menu.transfer.audit', '转岗管理-审核', null, 261, 1, 3, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (269, 'action.transfer.audit.audit', '转岗管理-批量转岗审核', null, 268, 2, 1, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (270, 'action.transfer.audit.search', '转岗管理-批量转岗查询', null, 268, 2, 2, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (271, 'action.transfer.audit.view', '转岗管理-批量转岗查看', null, 268, 2, 3, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (272, 'action.transfer.audit.flow', '转岗管理-批量转岗流程', null, 268, 2, 4, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (273, 'action.transfer.audit.export', '转岗管理-批量转岗导出', null, 268, 2, 5, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (274, 'menu.transfer.search', '转岗管理-查询', null, 261, 1, 5, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (275, 'action.transfer.search.search', '转岗管理-转岗数据查询', null, 274, 2, 1, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (276, 'action.transfer.search.view', '转岗管理-转岗数据查看', null, 274, 2, 2, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (277, 'action.transfer.search.flow', '转岗管理-转岗数据流程', null, 274, 2, 3, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (278, 'action.transfer.search.export', '转岗管理-转岗数据导出', null, 274, 2, 4, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (280, 'action.transfer.search.transfer', '转岗管理-转岗数据立即转岗', null, 274, 2, 6, null);
INSERT INTO permission (id, `key`, name, description, ancestry, type, sort, created_at) VALUES (281, 'action.transfer.search.update', '转岗管理-转岗数据修改日期', null, 274, 2, 7, null);

