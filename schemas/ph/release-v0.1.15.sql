insert into `workflow_node`(`id`,`flow_id`,`name`,`type`,`node_audit_type`,`expression`,`approve_next_node_id`,`reject_next_node_id`,`audit_type`,`auditor_type`,`auditor_id`,`created_at`,`can_edit_field`,`extend_text`) values
(960,70,'员工提交',1,0,null,null,null,null,1,null,null,'',null),
(961,70,'直接上级',3,0,null,null,null,null,3,null,null,'',null),
(962,70,'二级部门负责人',3,0,null,null,null,null,5,null,null,'',null),
(963,70,'一级部门负责人',3,0,null,null,null,null,6,null,null,'',null),
(964,70,'所属公司负责人',3,0,null,null,null,null,16,null,null,'',null),
(965,70,'coo/cpo',3,0,null,null,null,null,7,'',null,'',null),
(966,70,'中文审批（legal1中文）',3,0,null,null,null,null,1,'',null,'',null),
(967,70,'英文审批（legal1英文）',3,0,null,null,null,null,1,'',null,'',null),
(968,70,'菲律宾审批（legal1菲律宾）',3,0,null,null,null,null,1,'',null,'',null),
(969,70,'中文、英文审批',3,0,null,null,null,null,1,'57335',null,'',null),
(970,70,'菲律宾审批（legal2菲律宾）',3,0,null,null,null,null,1,'',null,'',null),
(971,70,'语言总审核',3,0,null,null,null,null,1,'57335',null,'',null),
(972,70,'结束',6,0,null,null,null,null,1,null,null,'',null),
(974, 70, 'AP Supervisor(菲律宾)', 3, 0, NULL, NULL, NULL, NULL, 1, '', NULL, '', NULL),
(975, 70, 'AP Supervisor(北京)', 3, 0, NULL, NULL, NULL, '4', 1, '64377', NULL, '', NULL);

INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 960, 961, NULL, NULL, '员工提交->直线上级', 10, '2020-11-06 06:12:51', '2020-11-06 06:12:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 961, 962, NULL, NULL, '直接上级->二级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 962, 963, NULL, NULL, '二级部门负责人->一级部门负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 963, 964, NULL, NULL, '一级部门负责人->所属公司负责人', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 966, '$p1<800000 && $p2==3', 'getAmount,getLang', '所属公司负责人->中文审批（legal1中文）', 10, '2021-06-08 06:15:54', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 967, '$p1<800000 && $p2==2', 'getAmount,getLang', '所属公司负责人->英文审批（legal1英文）', 9, '2021-06-08 06:15:56', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 968, '$p1<800000 && $p2==1', 'getAmount,getLang', '所属公司负责人->菲律宾语言审批（legal1菲律宾）', 8, '2021-06-08 07:35:05', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 965, '$p1<1500000', 'getAmount', '所属公司负责人->所属组织负责人', 7, '2021-06-08 06:15:59', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 964, 965, '$p1>=1500000', 'getAmount', '所属公司负责人->所属组织负责人', 6, '2021-06-08 06:16:01', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 966, '$p1==3 ', 'getLang', '所属组织负责人-> 中文审批（legal1中文）', 10, '2021-06-08 07:36:52', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 967, '$p1==2', 'getLang', '所属组织负责人-> 英文审批（legal1英文）', 10, '2021-06-08 07:36:52', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 965, 968, '$p1==1', 'getLang', '所属组织负责人-> 菲律宾语言审批（legal1菲律宾）', 10, '2021-06-08 07:36:53', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 966, 969, '$p1==3 || $p1==2', 'getLang', '中文审批（legal1中文）->中文、英文审批（legal2 中文）        3', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 966, 970, '$p1==1', 'getLang', '中文审批（legal1中文）->菲律宾语言审批 （legal2菲律宾）', 9, '2021-06-07 07:29:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 967, 969, '$p1==3 || $p1==2', 'getLang', '英文审批（legal1英文）-> 中文、英文审批（legal2 中文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 967, 970, '$p1==1', 'getLang', '英文审批（legal1英文）-> 菲律宾语言审批 （legal2菲律宾）', 9, '2021-06-07 07:30:05', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 968, 969, '$p1==3 || $p1==2', 'getLang', '菲律宾语言审批（legal1菲律宾）-> 中文、英文审批（legal2 中文）', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 968, 970, '$p1==1', 'getLang', '菲律宾语言审批（legal1菲律宾）-> 中文、英文审批（legal2 中文）', 9, '2021-06-07 07:30:10', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 969, 971, NULL, NULL, '中文、英文审批（legal2 中文）->语言总审核', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 970, 971, NULL, NULL, '菲律宾语言审批（legal1菲律宾）-> 语言总审核', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 971, 974, NULL, NULL, '语言总审核->AP Supervisor(菲律宾)', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 974, 975, NULL, NULL, 'AP Supervisor(菲律宾) ->AP Supervisor(北京)', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');
INSERT INTO `workflow_node_relate`(`flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 70, 975, 972, NULL, NULL, 'AP Supervisor(北京)->结束', 10, '2020-11-06 06:13:55', '2020-11-06 06:13:37');