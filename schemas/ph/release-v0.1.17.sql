-- 9299 菲律宾借款管理
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1<15000000',  `remark` = '<15000 一级部门负责人->FM' WHERE `id` = 471;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1>=15000000', `remark` = '>=15000 一级部门负责人->所属公司负责人' WHERE `id` = 472;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1<75000000',  `remark` = '<75000 所属公司负责人->FM' WHERE `id` = 473;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1>=75000000', `remark` = '>=75000 所属公司负责人->COO/CPO' WHERE `id` = 474;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1>=75000000', `remark` = 'COO/CPO->FM' WHERE `id` = 475;
UPDATE `workflow_node_relate` SET  `valuate_formula` = NULL, `remark` = 'FM->FSM' WHERE `id` = 476;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1<15000000', `remark` = '<15000 FSM->结束' WHERE `id` = 477;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1>=15000000', `remark` = '>=15000 FSM->FD'  WHERE `id` = 478;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1<75000000', `remark` = '<75000 FD->结束' WHERE `id` = 479;
UPDATE `workflow_node_relate` SET  `valuate_formula` = '$p1>=75000000', `remark` = '>=75000 FD->CFO' WHERE `id` = 480;


UPDATE `workflow_node` SET  `name` = '指定工号64377（Fianace Mangager）', `auditor_id` = '64377' WHERE `id` = 323;
UPDATE `workflow_node` SET  `name` = '指定工号54677（FSM）',  `auditor_id` = NULL WHERE `id` = 324;
UPDATE `workflow_node` SET  `name` = '指定工号20017225（Finace Director）', `auditor_id` = '20017225' WHERE `id` = 325;
UPDATE `workflow_node` SET  `name` = '指定工号17152（CFO）', `auditor_id` = '17152' WHERE `id` = 326;


INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (942, 71, '员工提交', 1, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (943, 71, '直接上级', 3, 0, NULL, NULL, NULL, NULL, 3, NULL, NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (945, 71, '借款财务(20017225)', 3, 0, NULL, NULL, NULL, NULL, 1, '20017225', NULL, '', NULL);
INSERT INTO `workflow_node`(`id`, `flow_id`, `name`, `type`, `node_audit_type`, `expression`, `approve_next_node_id`, `reject_next_node_id`, `audit_type`, `auditor_type`, `auditor_id`, `created_at`, `can_edit_field`, `extend_text`) VALUES (946, 71, '结束', 6, 0, NULL, NULL, NULL, NULL, 1, NULL, NULL, '', NULL);



INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 71, 942, 943, NULL, NULL, '员工提交->直接上级', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 71, 943, 945, NULL, NULL, '直接上级->借款财务(20017225)', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');
INSERT INTO `workflow_node_relate`( `flow_id`, `from_node_id`, `to_node_id`, `valuate_formula`, `valuate_code`, `remark`, `sort`, `updated_at`, `created_at`) VALUES ( 71, 945, 946, NULL, NULL, '借款财务(20017225)->结束', 10, '2021-06-07 09:53:09', '2021-06-07 09:53:09');
INSERT INTO `setting_env` (`code`, `val`, `content`, `created_at`, `updated_at`) VALUES ('loan_pay_staff_id','76260,65382', '借款付款人', '2021-07-07 07:36:16', '2021-07-07 07:53:25');

-- 9198 菲律宾采购管理需求
-- 设置wht税率
UPDATE `setting_env` SET `val` = '{\"0\":{\"name\":\"/\",\"tax\":[0]},\"1\":{\"name\":\"房租\",\"tax\":[5]},\"2\":{\"name\":\"专业服务\",\"tax\":[10]}}' WHERE `id` = 39;

-- 供应商审批人更新
-- 采购
update `workflow_node` set `auditor_id`='41081,52731' where `id`=979;
-- 菲律宾财务
update `workflow_node` set `auditor_id`='',`name`='菲律宾财务' where `id`=980;
-- 北京财务
update `workflow_node` set `auditor_id`='64377' where `id`=981;
-- Finance Senior Manager
update `workflow_node` set `auditor_id`='20017225' where `id`=982;

-- 采购申请单审批人
-- 指定工号（APS北京)
update `workflow_node` set `auditor_id`='' where `id`=720;
-- 指定工号41081(PM)
update `workflow_node` set `auditor_id`='41081' where `id`=312;
-- 指定工号52731（PSM）
update `workflow_node` set `auditor_id`='52731' where `id`=313;
-- 指定工号17152（PD）
update `workflow_node` set `auditor_id`='' where `id`=314;
-- 指定工号55356（CFO）
update `workflow_node` set `auditor_id`='17152' where `id`=315;

-- 采购订单审核人、采购付款申请单
update `workflow_node` set `auditor_id`='41081' where `id`=368;
update `workflow_node` set `auditor_id`='52731' where `id`=369;
update `workflow_node` set `auditor_id`='' where `id`=370;
update `workflow_node` set `auditor_id`='20017259' where `id`=371;
update `workflow_node` set `auditor_id`='64377' where `id`=372;
update `workflow_node` set `auditor_id`='' where `id`=373;
update `workflow_node` set `auditor_id`='' where `id`=374;
update `workflow_node` set `auditor_id`='20017225' where `id`=375;
update `workflow_node` set `auditor_id`='17152' where `id`=376;
update `workflow_node` set `auditor_id`='' where `id`=377;


-- 采购申请单关系节点条件更新
update `workflow_node_relate` set `valuate_formula`='$p1<75000000',`valuate_code`='getAmount' where id=455;
update `workflow_node_relate` set `valuate_formula`='$p1>=75000000',`valuate_code`='getAmount' where id=456;
update `workflow_node_relate` set `valuate_formula`='$p1<150000000',`valuate_code`='getAmount' where id=457;
update `workflow_node_relate` set `valuate_formula`='$p1>=150000000',`valuate_code`='getAmount' where id=458;
update `workflow_node_relate` set `valuate_formula`='$p1<750000000',`valuate_code`='getAmount' where id=459;
update `workflow_node_relate` set `valuate_formula`='$p1>=750000000',`valuate_code`='getAmount' where id=460;
update `workflow_node_relate` set `valuate_formula`='$p1<150000000',`valuate_code`='getAmount' where id=463;
update `workflow_node_relate` set `valuate_formula`='$p1>=150000000',`valuate_code`='getAmount' where id=464;
update `workflow_node_relate` set `valuate_formula`='$p1<750000000',`valuate_code`='getAmount' where id=465;
update `workflow_node_relate` set `valuate_formula`='$p1>=750000000',`valuate_code`='getAmount' where id=466;

-- 采购订单审核人、采购付款申请单条件更新
update `workflow_node_relate` set `valuate_formula`='$p1<75000000',`valuate_code`='getAmount' where id=553;
update `workflow_node_relate` set `valuate_formula`='$p1<450000000',`valuate_code`='getAmount' where id=555;
update `workflow_node_relate` set `valuate_formula`='$p1<750000000',`valuate_code`='getAmount' where id=557;


-- 采购订单需要添加VAT税率字段
ALTER TABLE `purchase_order` add COLUMN `wht_total_amount` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'wht总金额';
-- 采购订单明细表添加wht字段
ALTER TABLE `purchase_order_product` add COLUMN `wht_cate` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'WHT类别：0无，1 : 房租, 2 : 专业服务';
ALTER TABLE `purchase_order_product` add COLUMN `wht_rate` int(3) NOT NULL DEFAULT '0' COMMENT 'WHT税率：值为整数(0-100), 单位 %';
ALTER TABLE `purchase_order_product` add COLUMN `wht_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT 'WHT金额=不含税金额*WHT税率';
-- 更新采购支付人
update `setting_env` set `val`='76260,65382' where `code`='purchase_payment_pay_staff_id';