#!/bin/bash

function echo_log(){
   echo -e "\033[0;31m $1 \033[0m"
}
function echo_blue {
   echo -e "\033[47;34m $1 \033[0m"
}

#备份代码库
function backup {
  br=`git branch | grep "*"`
  local_branch=${br/* /}
  #建立备份分支名字
  branch_name=bak`date +%Y%m%d%H%M%S`
  #echo_log $branch_name
  #建立备份分支名
  git checkout -b $branch_name
  echo_blue "${local_branch} 备份成功为 ${branch_name}"
  #切换回使用分支
  git checkout $local_branch
  echo_blue "切换到${local_branch}本地完成"
}

#运行生成语言包文件
function build_translation {
    #php bin/cli translation gen
    php app/cli.php translation main
}

function count_code {
# 定义提交者姓名
author="huhelong"
# 定义统计开始时间
since="2025-01-01"
# 定义统计结束时间
until="2025-01-23"

# 获取指定时间范围内指定提交者的所有提交哈希
commits=$(git log --author="$author" --since="$since" --until="$until" --pretty=format:"%H")

# 初始化新增和删除的代码行数
total_additions=0
total_deletions=0

# 遍历每个提交
for commit in $commits; do
    # 获取当前提交与其父提交之间的差异统计
    stats=$(git diff --shortstat $commit^ $commit)
    # 提取新增的代码行数
    additions=$(echo $stats | grep -oE '([0-9]+) insertion' | grep -oE '[0-9]+')
    # 提取删除的代码行数
    deletions=$(echo $stats | grep -oE '([0-9]+) deletion' | grep -oE '[0-9]+')

    # 若有新增代码行数，则累加到总数中
    if [ -n "$additions" ]; then
        total_additions=$((total_additions + additions))
    fi
    # 若有删除代码行数，则累加到总数中
    if [ -n "$deletions" ]; then
        total_deletions=$((total_deletions + deletions))
    fi
done

# 输出结果
echo "开发者 $author 在 $since 至 $until 期间新增代码行数: $total_additions"
echo "开发者 $author 在 $since 至 $until 期间删除代码行数: $total_deletions"
echo "开发者 $author 在 $since 至 $until 期间净增代码行数: $((total_additions - total_deletions))"
}


echo_log "请选择 1 备份代码 2 升级代码 3 更新语言包 4统计代码 :"
read -p "请输入: " number

case $number in
    1)
       echo_log "你要做的操作是备份代码"
       backup
    ;;
    2)
       echo_log "更新代码中"
       git pull origin
       echo_blue "代码更新完毕"
    ;;
    3)
       echo_blue "语言包开始更新"
       build_translation
       echo_blue "语言包更新完毕"
    ;;
    4)
       echo_log "统计代码开始"
       count_code
       echo_log "统计代码结束"
    ;;
    *)
         echo_log "输入错误"
      ;;
esac