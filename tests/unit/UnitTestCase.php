<?php

namespace tests\unit;

use App\Library\BaseService;
use Phalcon\Test\UnitTestCase as PhalconTestCase;

abstract class UnitTestCase extends PhalconTestCase
{
    private $_loaded = false;

    protected $userInfo = [];
    protected $locale   = [];

    public function setUp()
    {
        BaseService::setLanguage('zh-CN');
        $this->_loaded  = true;
    }

    public function __destruct()
    {
        if (!$this->_loaded) {

        }
    }

    public function tearDown()
    {
    }
}