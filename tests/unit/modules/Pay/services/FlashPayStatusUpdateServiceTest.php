<?php

namespace tests\unit\modules\pay\services;

use App\Library\Enums\PayEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Services\FlashPayStatusUpdateService;
use App\Modules\Pay\Services\FlashPaySftpUpdateParams;
use App\Modules\Pay\Services\FlashPayApiUpdateParams;
use Exception;

/**
 * FlashPay支付状态更新服务测试类
 * @description: 测试FlashPay支付状态更新通用服务的各种场景
 * @author: AI
 * @date: 2025-08-18
 */
class FlashPayStatusUpdateServiceTest extends UnitTestCase
{
    /**
     * @var FlashPayStatusUpdateService
     */
    protected $service;

    /**
     * @var \PHPUnit\Framework\MockObject\MockObject
     */
    protected $mockPayment;

    /**
     * @var \PHPUnit\Framework\MockObject\MockObject
     */
    protected $mockLogger;

    public function setUp(): void
    {
        parent::setUp();
        $this->service = FlashPayStatusUpdateService::getInstance();
        
        // 创建Payment模型的Mock对象
        $this->mockPayment = $this->createMock(Payment::class);
        
        // 创建Logger的Mock对象
        $this->mockLogger = $this->createMock(\App\Library\Logger::class);
    }

    /**
     * 测试SFTP方式支付成功状态更新
     * @description: 测试SFTP方式处理支付成功的状态更新逻辑
     * @return void
     */
    public function testUpdateSftpPaymentStatusSuccess()
    {
        // 准备测试数据
        $this->mockPayment->oa_trade_no = 'TEST_TRADE_NO_001';
        $this->mockPayment->payment_id = 12345;
        
        // 设置Payment模型的期望行为
        $this->mockPayment->expects($this->once())
            ->method('assign')
            ->with($this->callback(function ($updateData) {
                // 验证更新数据包含必要字段
                $this->assertArrayHasKey('flashpay_transaction_batch_no', $updateData);
                $this->assertArrayHasKey('flashpay_transaction_order_no', $updateData);
                $this->assertArrayHasKey('pay_status', $updateData);
                $this->assertArrayHasKey('out_send_status', $updateData);
                $this->assertArrayHasKey('pay_user_id', $updateData);
                $this->assertArrayHasKey('pay_time', $updateData);
                
                // 验证支付成功状态
                $this->assertEquals(PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY, $updateData['pay_status']);
                $this->assertEquals(PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_SUCCESS, $updateData['out_send_status']);
                $this->assertEquals(StaffInfoEnums::SUPER_ADMIN_STAFF_ID, $updateData['pay_user_id']);
                
                return true;
            }));

        $this->mockPayment->expects($this->once())
            ->method('save')
            ->willReturn(true);

        // 设置Logger的期望行为
        $this->mockLogger->expects($this->once())
            ->method('info')
            ->with($this->stringContains('FlashPay SFTP支付状态更新成功'));

        // 执行测试
        $params = new FlashPaySftpUpdateParams(
            $this->mockPayment,
            'success',
            'BATCH_001',
            'ORDER_001',
            '',
            $this->mockLogger
        );

        $result = $this->service->updatePaymentStatus($params);

        // 验证结果
        $this->assertTrue($result['success']);
        $this->assertEmpty($result['error']);
    }

    /**
     * 测试SFTP方式支付失败状态更新
     * @description: 测试SFTP方式处理支付失败的状态更新逻辑
     * @return void
     */
    public function testUpdateSftpPaymentStatusFailed()
    {
        // 准备测试数据
        $this->mockPayment->oa_trade_no = 'TEST_TRADE_NO_002';
        $failReason = '银行账户信息错误';
        
        // 设置Payment模型的期望行为
        $this->mockPayment->expects($this->once())
            ->method('assign')
            ->with($this->callback(function ($updateData) use ($failReason) {
                // 验证更新数据包含必要字段
                $this->assertArrayHasKey('pay_status', $updateData);
                $this->assertArrayHasKey('out_send_status', $updateData);
                $this->assertArrayHasKey('flashpay_fail_reason', $updateData);
                
                // 验证支付失败状态
                $this->assertEquals(PayEnums::PAYMENT_MODULE_PAY_STATUS_FLASH_PAY_FAILED, $updateData['pay_status']);
                $this->assertEquals(PayEnums::PAYMENT_MODULE_PAY_SEND_STATUS_FAILED, $updateData['out_send_status']);
                $this->assertEquals($failReason, $updateData['flashpay_fail_reason']);
                
                return true;
            }));

        $this->mockPayment->expects($this->once())
            ->method('save')
            ->willReturn(true);

        // 执行测试
        $params = new FlashPaySftpUpdateParams(
            $this->mockPayment,
            'failed',
            'BATCH_002',
            'ORDER_002',
            $failReason,
            $this->mockLogger
        );

        $result = $this->service->updatePaymentStatus($params);

        // 验证结果
        $this->assertTrue($result['success']);
        $this->assertEmpty($result['error']);
    }

    /**
     * 测试API方式支付成功状态更新
     * @description: 测试API方式处理支付成功的状态更新逻辑
     * @return void
     */
    public function testUpdateApiPaymentStatusSuccess()
    {
        // 准备测试数据
        $this->mockPayment->oa_trade_no = 'TEST_TRADE_NO_003';
        $this->mockPayment->oa_type = 1; // 假设为报销类型
        $this->mockPayment->pay_bank_account = 'TEST_ACCOUNT';
        
        $paymentData = [
            'tradeStatus' => PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_SUCCESS,
            'completeTime' => '2025-08-18 10:30:00'
        ];

        // 设置Payment模型的期望行为
        $this->mockPayment->expects($this->once())
            ->method('toArray')
            ->willReturn([
                'oa_type' => 1,
                'payment_id' => 12345
            ]);

        $this->mockPayment->expects($this->once())
            ->method('save')
            ->willReturn(true);

        // 执行测试
        $params = new FlashPayApiUpdateParams(
            $this->mockPayment,
            PayEnums::PAYMENT_MODULE_PAY_OUT_TRADE_STATUS_SUCCESS,
            $paymentData,
            ['code' => 0],
            [],
            $this->mockLogger
        );

        $result = $this->service->updatePaymentStatus($params);

        // 验证结果
        $this->assertTrue($result['success']);
        $this->assertEmpty($result['error']);
    }

    /**
     * 测试参数验证 - 缺少必要参数
     * @description: 测试当缺少必要参数时的异常处理
     * @return void
     */
    public function testValidateParamsWithMissingPayment()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('支付记录不能为空');

        $params = new FlashPaySftpUpdateParams(
            null, // 空的payment
            'success',
            'BATCH_001',
            'ORDER_001'
        );

        $this->service->updatePaymentStatus($params);
    }

    /**
     * 测试参数验证 - 缺少交易状态
     * @description: 测试当缺少交易状态参数时的异常处理
     * @return void
     */
    public function testValidateParamsWithMissingTransactionStatus()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('交易状态不能为空');

        $params = new FlashPaySftpUpdateParams(
            $this->mockPayment,
            '', // 空的交易状态
            'BATCH_001',
            'ORDER_001'
        );

        $this->service->updatePaymentStatus($params);
    }

    /**
     * 测试SFTP方式缺少必要参数
     * @description: 测试SFTP方式缺少批次号时的异常处理
     * @return void
     */
    public function testValidateParamsForSftpWithMissingBatchNo()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('交易批次号不能为空');

        $params = new FlashPaySftpUpdateParams(
            $this->mockPayment,
            'success',
            '', // 空的批次号
            'ORDER_001'
        );

        $this->service->updatePaymentStatus($params);
    }

    /**
     * 测试API方式缺少必要参数
     * @description: 测试API方式缺少支付数据时的异常处理
     * @return void
     */
    public function testValidateParamsForApiWithMissingPaymentData()
    {
        $this->expectException(Exception::class);
        $this->expectExceptionMessage('支付数据不能为空');

        $params = new FlashPayApiUpdateParams(
            $this->mockPayment,
            'success',
            [] // 空的支付数据
        );

        $this->service->updatePaymentStatus($params);
    }

    /**
     * 测试未知交易状态处理
     * @description: 测试当遇到未知交易状态时的异常处理
     * @return void
     */
    public function testUpdateSftpPaymentStatusWithUnknownStatus()
    {
        $params = new FlashPaySftpUpdateParams(
            $this->mockPayment,
            'unknown_status',
            'BATCH_003',
            'ORDER_003',
            '',
            $this->mockLogger
        );

        $result = $this->service->updatePaymentStatus($params);

        // 验证结果 - 应该失败
        $this->assertFalse($result['success']);
        $this->assertStringContains('未知交易状态', $result['error']);
    }

    public function tearDown(): void
    {
        parent::tearDown();
        $this->service = null;
        $this->mockPayment = null;
        $this->mockLogger = null;
    }
}
