<?php

namespace App\Modules\Invoice;

use App\Modules\Invoice\Services\InvoiceDataService;
use App\Modules\Invoice\Services\OAuth\OAuthService;
use App\Modules\Invoice\Services\SyncService;
use App\Modules\Invoice\Services\VoucherAdapterFactoryService;
use Phalcon\DiInterface;
use Phalcon\Loader;
use Phalcon\Mvc\ModuleDefinitionInterface;

class Module implements ModuleDefinitionInterface
{
    /**
     * 注册自动加载器
     *
     * @param DiInterface $di
     */
    public function registerAutoloaders(DiInterface $di = null)
    {
        $loader = new Loader();

        $loader->registerNamespaces([
            'App\Modules\Invoice\Controllers'         => __DIR__ . '/controllers/',
            'App\Modules\Invoice\Validation'          => __DIR__ . '/validation/',
            'App\Modules\Invoice\Services'            => __DIR__ . '/services/',
            'App\Modules\Invoice\Services\OAuth'      => __DIR__ . '/services/OAuth/',
            'App\Modules\Invoice\Services\Validation' => __DIR__ . '/services/Validation/',
            'App\Modules\Invoice\Exceptions'          => __DIR__ . '/exceptions/',
            'App\Modules\Invoice\Transformers'        => __DIR__ . '/transformers/',
        ]);

        $loader->register();
    }

    /**
     * 注册模块特定的服务
     *
     * @param DiInterface $di
     */
    public function registerServices(DiInterface $di)
    {
        // 注册OAuth相关服务
        if (!$di->has('invoice.oauth.OAuthService')) {
            $di->setShared('invoice.oauth.OAuthService', function () use ($di) {
                $config = $di->getShared('config')->invoice->oauth;

                return new OAuthService(
                    $config->client_id,
                    $config->client_secret,
                    $config->pro_mode,
                    $di->getShared('logger')
                );
            });
        }

        // 提供便捷方法获取InvoiceClient实例
        if (!$di->has('invoice.oauth.OAuthService.getClient')) {
            $di->set('invoice.oauth.OAuthService.getClient', function ($onBehalfOf = null, $forceRefresh = false) use ($di) {
                $service = $di->getShared('invoice.oauth.OAuthService');
                return $service->getClient($onBehalfOf, $forceRefresh);
            });
        }

        // 注册同步服务
        if (!$di->has('XmlValidationService')) {
            $di->setShared('invoiceSyncService', function () use ($di) {
                $config            = $di->getShared('config');

                return new SyncService();
            });
        }

        // 注册凭证适配器服务
        if (!$di->has('invoiceDataService')) {
            $di->setShared('invoiceDataService', function () {
                return new InvoiceDataService();
            });
        }

        // 注册凭证适配器服务
        if (!$di->has('VoucherAdapterFactoryService')) {
            $di->setShared('VoucherAdapterFactoryService', function () {
                return new VoucherAdapterFactoryService();
            });
        }
    }
} 