<?php

namespace App\Modules\Invoice\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Invoice\Services\CertificateService;

class CertificateController extends BaseController
{
    /**
     * 上传证书文件并生成环境变量配置
     *
     * @param Request $request
     * @return Response|\Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws BusinessException
     * @api {post} /api/certificates/upload 上传证书
     * @apiName UploadCertificate
     * @apiGroup Certificate
     * @apiPermission admin
     *
     * @apiParam {File} certificate P12格式的证书文件
     * @apiParam {String} password 证书密码
     *
     * @apiSuccess {Boolean} success 是否成功
     * @apiSuccess {Object} data 环境变量配置数据
     *
     */
    public function uploadAction()
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException($this->t->_("bank_flow_not_found_file"));
        }

        $file = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if (!in_array($extension, ['p12', 'pfx'])) {
            throw new ValidationException($this->t->_("bank_flow_not_found_file"));
        }

        // 检查Sodium扩展
        if (!function_exists('sodium_crypto_secretbox')) {
            throw new BusinessException('Sodium扩展未安装', ErrCode::$BUSINESS_ERROR);
        }

        try {
            // 获取上传的证书文件
            $response = CertificateService::getInstance()->getEncryptCertificate($file->getTempName());

            // 返回加密后的环境变量配置
            return $this->returnJson(ErrCode::$SUCCESS, '', $response);

        } catch (BusinessException $e) {

            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage(), null);
        } catch (\Exception $e) {

            $this->logger->error('certificate upload err:' . $e->getMessage() . ' ' . $e->getTraceAsString() );
            // 记录错误日志但不暴露详情
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, '', null);
        }
    }
}