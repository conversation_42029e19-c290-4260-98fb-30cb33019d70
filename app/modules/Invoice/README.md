# 电子发票模块 (Einvoice Module)

## 概述

电子发票模块用于处理各类付款单据的电子发票同步和管理功能。该模块支持多种类型的付款单据，包括普通付款单、采购付款单和代理支付单等。

## 模块结构

### 核心组件

- **Module.php**: 模块的入口点，负责模块的定义与初始化。

### 控制器 (Controllers)

- **SyncController.php**: 处理单据同步相关的HTTP请求，提供API接口。

### 数据模型 (Models)

- **AbstractVoucher.php**: 单据的抽象基类，定义了所有单据类型的共同接口和基本功能。
- **OrdinaryPaymentVoucher.php**: 普通付款单模型，实现了AbstractVoucher接口。
- **PurchasePaymentVoucher.php**: 采购付款单模型，实现了AbstractVoucher接口。
- **ProxyPaymentVoucher.php**: 代理支付单模型，实现了AbstractVoucher接口。

### 服务层 (Services)

- **BaseService.php**: 提供基础服务功能，被其他服务类继承。
- **SyncService.php**: 处理单据同步的核心业务逻辑。

#### OAuth认证服务

- **OAuth/AuthenticationService.php**: 提供OAuth认证功能。
- **OAuth/CachedAuthenticationService.php**: 带缓存功能的认证服务，提高性能。
- **OAuth/TokenResponse.php**: 封装OAuth令牌响应数据。

#### 验证服务

- **Validation/XmlValidationService.php**: 提供XML数据验证功能。

### 异常处理 (Exceptions)

- **SyncException.php**: 处理同步过程中的异常情况。
- **XmlValidationException.php**: 处理XML验证过程中的异常。
- **OAuth/AuthenticationException.php**: 处理认证过程中的异常。

### 数据转换器 (Transformers)

- **XmlTransformer.php**: XML数据转换的基类。
- **OrdinaryPaymentTransformer.php**: 普通付款单的XML数据转换器。
- **PurchasePaymentTransformer.php**: 采购付款单的XML数据转换器。
- **ProxyPaymentTransformer.php**: 代理支付单的XML数据转换器。

### 配置文件 (Config)

- **config.php**: 模块的主要配置文件。
- **services.php**: 服务提供者的配置文件。

### 资源文件 (Resources)

- **schemas/**: 包含用于验证XML数据的XSD模式文件。
  - **ordinary_payment.xsd**: 普通付款单的XSD验证模式。
  - **purchase_payment.xsd**: 采购付款单的XSD验证模式。
  - **proxy_payment.xsd**: 代理支付单的XSD验证模式。

## 使用流程

1. 通过控制器接收单据数据
2. 使用验证服务验证数据格式
3. 通过OAuth服务进行认证
4. 使用相应的转换器将数据转换为标准格式
5. 通过同步服务处理业务逻辑
6. 返回处理结果

## 开发指南

开发新的单据类型时，需要：
1. 创建新的单据模型类，继承AbstractVoucher
2. 创建对应的XML转换器
3. 添加相应的XSD验证模式
4. 在SyncService中添加处理逻辑

## E-invoice 月度报表功能

系统提供了自动生成和发送E-invoice月度报表的功能。此功能将在每月1号自动执行，收集上个月的所有发票提交记录，并通过邮件发送给配置的收件人。

### 主要功能

1. **自动时间处理**：系统会自动识别上个月的时间范围，提取相应时间段的发票数据
2. **数据聚合**：按照OA单号+发票类型分组，获取创建时间最新的记录作为最终状态
3. **Excel报表生成**：生成包含详细发票信息的Excel报表，包括单号、类型、状态、UUID等
4. **邮件发送**：自动将报表以邮件附件形式发送给配置的收件人

### 配置方法

在系统配置中添加以下配置项：

- **配置项编码**：`einvoice_notification_emails`
- **配置项内容**：E-invoice推送结果接收邮箱，多个邮箱使用英文逗号分隔
- **示例**：`<EMAIL>,<EMAIL>,<EMAIL>`

### 手动执行

可以通过以下命令手动执行E-invoice月度报表功能：

```bash
php app/cli.php invoice sendInvoiceMonthlyReport
```

### 定时任务

系统已配置在每月1号凌晨1点自动执行此功能。

```
app/modules/Einvoice/
├── Module.php                      # 模块定义与初始化
├── controllers/                    # 控制器目录
│   └── SyncController.php          # 同步控制器
├── models/                         # 数据模型目录
│   ├── AbstractVoucher.php         # 抽象单据基类
│   ├── OrdinaryPaymentVoucher.php  # 普通付款单模型
│   ├── PurchasePaymentVoucher.php  # 采购付款单模型
│   └── ProxyPaymentVoucher.php     # 代理支付单模型
├── services/                       # 服务层目录
│   ├── BaseService.php             # 基础服务类
│   ├── SyncService.php             # 同步服务
│   ├── OAuth/                      # OAuth认证相关服务
│   │   ├── AuthenticationService.php  # 认证服务
│   │   ├── CachedAuthenticationService.php  # 带缓存的认证服务
│   │   └── TokenResponse.php       # 令牌响应对象
│   └── Validation/                 # 验证服务
│       └── XmlValidationService.php  # XML验证服务
├── exceptions/                     # 异常处理
│   ├── SyncException.php           # 同步异常
│   ├── XmlValidationException.php  # XML验证异常
│   └── OAuth/
│       └── AuthenticationException.php  # 认证异常
├── transformers/                   # 数据转换器
│   ├── XmlTransformer.php          # XML转换基类
│   ├── OrdinaryPaymentTransformer.php  # 普通付款单转换器
│   ├── PurchasePaymentTransformer.php  # 采购付款单转换器
│   └── ProxyPaymentTransformer.php  # 代理支付转换器
├── config/                         # 配置文件
│   ├── config.php                  # 模块配置
│   └── services.php                # 服务提供者配置
└── resources/
└── schemas/                    # XSD验证模式
├── ordinary_payment.xsd    # 普通付款单XSD
├── purchase_payment.xsd    # 采购付款单XSD
└── proxy_payment.xsd       # 代理支付单XSD
```

#4 中间数据结构
```
Array
(
    [e_invoice_version] => 1.1
    [e_invoice_type_code] => 11
    [e_invoice_code] => PAR202503190025
    [e_invoice_date] => 2025-04-09
    [e_invoice_time] => 21:13:22Z
    [currency_code] => MYR
    [supplier] => Array
        (
            [supplier_name] =>
            [supplier_tax_id] => EI00000000030
            [supplier_tax_number] =>
            [supplier_sst_number] => NA
            [supplier_msic_code] => 00000
            [supplier_desc] => NOT APPLICABLE
            [supplier_phone] =>
            [supplier_address] => Array
                (
                    [line0] =>
                    [city] => NA
                    [state] => 17
                    [country] =>
                )
        )
    [buyer] => Array
        (
            [buyer_name] => FLASH MALAYSIA EXPRESS SDN BHD
            [buyer_tax_id] => C24646488070
            [buyer_tax_number] => 201601028302
            [buyer_sst_number] => W10-2109-32000038
            [buyer_phone] => 0103824511
            [buyer_address] => Array
                (
                    [line0] => Suite 01-01, Level 1, Tower 5, Avenue 7, The Horizon, Bangsar South City, No. 8, Jalan Kerinchi, 59200 Kuala Lumpur
                    [city] => Kuala Lumpur
                    [state] => 14
                    [country] => MYS
                )

        )
    [items] => Array
        (
            [0] => Array
                (
                    [classification] => 035
                    [description] => 242
                    [subtotal] => 1000.00
                    [total_excluding_tax] => 1000.00
                    [quantity] => 1
                    [measurement] => XUN
                    [unit_price] => 1000.00
                    [tax_type] => 02
                    [tax_rate] => 8
                    [tax_amount] => 80.00
                )

        )
    [taxes] => Array
        (
            [total_excluding_tax] => 1000.00
            [tax_type] => 02
            [total_including_tax] => 1000.00
            [total_payable_amount] => 1000.00
            [total_tax_amount] => 0
            [total_tax_amount_per_tax_type] => 0
        )

)
```