<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace App\Modules\Invoice\Services\Document;


use App\Library\Enums\InvoiceEnums;
use App\Modules\Invoice\Services\AbstractService;
use App\Modules\Invoice\Services\InvoiceClient;

/**
 * Document submission service
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
class DocumentSubmissionService extends AbstractService
{
    /**
     * DocumentSubmissionService constructor.
     *
     * @param InvoiceClient    $client
     * @param bool              $prodMode
     */
    public function __construct(InvoiceClient $client, $prodMode = false)
    {
        parent::__construct($client,
            $this->initApiUrl(InvoiceEnums::API_DOCUMENT_SUBMISSION_PATH, $prodMode));
    }

    /**
     * This API returns information on documents submitted during a single submission by taxpayer.
     * 
     * @param string    $id         Mandatory: Unique ID of the document submission to retrieve.
     * @param int       $pageNo     Optional: number of the page to retrieve
     * @param int       $pageSize   Optional: number of the documents to retrieve per page. Page size cannot exceed system configured maximum page size for this API [100]
     * 
     * @return array
     */
    public function getSubmission($id, $pageNo = 1, $pageSize = 100)
    {
        $params = [
            'pageNo' => $pageNo,
            'pageSize' => $pageSize,
        ];
        $query = '?' . http_build_query($params);

        $url = $this->getBaseUrl() . '/' . $id . $query;

        $response = $this->getClient()->request('GET', $url);

        return $response;
    }

    /**
     * This API allows taxpayer to submit one or more signed documents to MyInvois System.
     * 
     * @param array $documents  List of document objects submitted. List should have at least one document. The document should follow the UBL 2.1 schema based on the document type version.
     * 
     * @return array
     */
    public function submitDocument(array $documents = [])
    {
        $url = $this->getBaseUrl();
        $body = [
            'json' => [
                'documents' => $documents,
            ],
        ];

        $response = $this->getClient()->request('POST', $url, $body);
        return $response;
    }
}
