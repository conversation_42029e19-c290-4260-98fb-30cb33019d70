<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace App\Modules\Invoice\Services\Document;


use App\Library\Enums\InvoiceEnums;
use App\Modules\Invoice\Services\AbstractService;
use App\Modules\Invoice\Services\InvoiceClient;

/**
 * Document type service
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
class DocumentTypeService extends AbstractService
{
    /**
     * DocumentTypeService constructor.
     *
     * @param InvoiceClient    $client
     * @param bool              $prodMode
     */
    public function __construct(InvoiceClient $client, $prodMode = false)
    {
        parent::__construct($client,
            $this->initApiUrl(InvoiceEnums::API_DOCUMENT_TYPE_PATH, $prodMode));
    }

    /**
     * This API allows taxpayer's systems to retrieve list of document types published by the MyInvois System.
     * 
     * @return array
     */
    public function getAllDocumentTypes()
    {
        $response = $this->getClient()->request('GET', $this->getBaseUrl());
        return $response;
    }

    /**
     * This API allows taxpayer's ERP system to retrieve the details of single document type that contains structure definitions of the document.
     * 
     * @param string $id    Unique ID of existing document type
     * 
     * @return array
     */
    public function getDocumentType($id)
    {
        $url = $this->getBaseUrl() . '/' . $id;
        
        $response = $this->getClient()->request('GET', $url);
        return $response;
    }

    /**
     * This API allows taxpayer's ERP system to retrieve the details of document type version that contains structure definitions of the documents.
     * 
     * @param string $id            Unique ID of existing document type
     * @param string $versionId     Unique ID of existing document type version that is published or deactivated
     * 
     * @return array
     */
    public function getDocumentTypeVersion($id, $versionId)
    {
        $url = $this->getBaseUrl() . '/' . $id . '/versions/' . $versionId;

        $response = $this->getClient()->request('GET', $url);
        return $response;
    }
}
