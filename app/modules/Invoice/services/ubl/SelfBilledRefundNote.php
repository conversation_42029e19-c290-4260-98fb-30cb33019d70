<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace app\modules\Invoice\services\ubl;

use app\modules\Invoice\services\ubl\Constant\InvoiceTypeCodes;

/**
 * self billed refund note
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
class SelfBilledRefundNote extends Invoice
{
    public $xmlTagName = 'Invoice'; //'SelfBilledRefundNote'; // MyInvois System re-use back same tag name
    protected $invoiceTypeCode = InvoiceTypeCodes::SELF_BILLED_REFUND_NOTE;
}
