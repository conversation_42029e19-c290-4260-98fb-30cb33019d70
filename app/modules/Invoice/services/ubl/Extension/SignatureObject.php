<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace app\modules\Invoice\services\ubl\Extension;

use app\modules\Invoice\services\ubl\ISerializable;
use app\modules\Invoice\services\ubl\IValidator;
use app\modules\Invoice\services\ubl\XmlSchema;
use InvalidArgumentException;
use Sabre\Xml\Writer;

/**
 * Signature object
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
class SignatureObject implements ISerializable, IValidator
{
    private $qualifyingProperties;

    /**
     * @return QualifyingProperties
     */
    public function getQualifyingProperties()
    {
        return $this->qualifyingProperties;
    }

    /**
     * @param QualifyingProperties $qualifyingProperties
     * @return SignatureObject
     */
    public function setQualifyingProperties(QualifyingProperties $qualifyingProperties)
    {
        $this->qualifyingProperties = $qualifyingProperties;
        return $this;
    }

    /**
     * validate function
     *
     * @throws InvalidArgumentException An error with information about required data that is missing
     */
    public function validate()
    {
        if($this->qualifyingProperties === null) {
            throw new InvalidArgumentException('Missing SignatureObject qualifyingProperties');
        }
    }

    /**
     * The xmlSerialize method is called during xml writing.
     *
     * @param Writer $writer
     * @return void
     */
    public function xmlSerialize(Writer $writer): void
    {
        $this->validate();

        $writer->namespaceMap = array_merge($writer->namespaceMap, [
            'http://uri.etsi.org/01903/v1.3.2#' => 'xades',
        ]);

        if ($this->qualifyingProperties !== null) {

            $attributes = $this->qualifyingProperties->getAttributes();

            $attributes = array_merge([
                'xmlns:xades' => 'http://uri.etsi.org/01903/v1.3.2#',
            ], $attributes);

            $this->qualifyingProperties->setAttributes($attributes);

            $writer->write([
                'name' => XmlSchema::XADES . 'QualifyingProperties',
                'value' => $this->qualifyingProperties,
                'attributes' => $this->qualifyingProperties->getAttributes(),
            ]);
        }
    }

    /**
     * The jsonSerialize method is called during json writing.
     *
     * @return array
     */
    public function jsonSerialize()
    {
        $this->validate();

        $arrays = [];

        if ($this->qualifyingProperties !== null) {
            $arrays['QualifyingProperties'][] = $this->qualifyingProperties;
        }

        return $arrays;
    }
}
