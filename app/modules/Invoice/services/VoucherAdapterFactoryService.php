<?php

namespace App\Modules\Invoice\Services;

use app\modules\Invoice\services\validation\AbstractVoucher;

/**
 * 凭证适配器工厂
 */
class VoucherAdapterFactoryService extends BaseService
{
    /**
     * 创建凭证适配器
     * @param string $type 凭证类型
     * @return AbstractVoucherAdapterService
     * @throws \InvalidArgumentException 当类型不支持时抛出异常
     */
    public static function createAdapter(string $type): AbstractVoucherAdapterService
    {
        // 去除可能的credit_前缀，获取基本类型
        $baseType = str_replace('credit_', '', $type);

        switch ($baseType) {
            case 'ordinary_payment':
                return new OrdinaryPaymentVoucherAdapterService();
            case 'purchase_payment':
                return new PurchasePaymentVoucherAdapterService();
            case 'agency_payment':
                return new AgencyPaymentVoucherAdapterService();
            default:
                throw new \InvalidArgumentException("不支持的凭证类型: {$type}");
        }
    }

    /**
     * 创建电子发票凭证
     * @param int $companyId 公司 ID
     * @param array $data 数据
     * @param string $datatype
     * @return AbstractVoucher
     */
    public static function createVoucher(int $companyId, array $data, string $datatype): AbstractVoucher
    {
        $adapter = self::createAdapter($datatype);
        return $adapter->createVoucher($companyId, $data, $datatype);
    }
}