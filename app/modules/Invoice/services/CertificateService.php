<?php

namespace App\Modules\Invoice\Services;

use App\Library\Exception\BusinessException;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;

/**
 * 证书服务类
 * 重新定义功能：
 * 1. 从环境变量获取加密的证书内容
 * 2. 使用Sodium解密证书内容
 * 3. 提供证书路径和密码的获取方法
 */
class CertificateService
{
    /** @var CertificateService|null 单例实例 */
    private static $instance = null;
    
    /** @var string|null 解密后的证书内容 */
    private $certificateContent = null;
    
    /** @var string|null 证书密码 */
    private $certificatePassword = null;
    
    /** @var string|null 临时证书文件路径 */
    private $certificateFilePath = null;
    
    /** @var int 最后加载时间 */
    private $lastLoadTime = 0;
    
    /** @var int 缓存过期时间（秒） */
    private const CACHE_LIFETIME = 7200; // 2小时
    
    /** @var string 环境变量名：加密的证书内容 */
    private const ENV_CERTIFICATE_CONTENT = 'certificate_p12_content';
    
    /** @var string 环境变量名：Sodium加密密钥 */
    private const ENV_ENCRYPTION_KEY = 'certificate_encryption_key';
    
    /** @var string 环境变量名：Sodium加密随机数 */
    private const ENV_ENCRYPTION_NONCE = 'certificate_encryption_nonce';
    
    /** @var string 环境变量名：证书密码 */
    private const ENV_CERTIFICATE_PASSWORD = 'certificate_p12_password';
    
    /** @var string 临时文件目录 */
    private const TEMP_DIR = '/tmp/certificates';
    
    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct()
    {
        // 确保临时目录存在
        if (!is_dir(self::TEMP_DIR)) {
            mkdir(self::TEMP_DIR, 0700, true);
        }
    }
    
    /**
     * 获取单例实例
     * @return CertificateService
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 获取证书路径
     * 方法会从环境变量中获取加密的证书内容，解密后存储到临时文件，并返回文件路径
     * @return string 证书文件路径
     * @throws BusinessException 解密失败时抛出异常
     */
    public function getCertificatePath(): string
    {
        // 如果已有有效的证书文件路径，直接返回
        if ($this->certificateFilePath !== null && file_exists($this->certificateFilePath) && !$this->isCacheExpired()) {
            return $this->certificateFilePath;
        }
        
        // 从环境变量获取加密的证书内容并解密
        $certificateContent = $this->getDecryptedCertificateContent();
        
        // 生成唯一的临时文件名
        $tempFileName = self::TEMP_DIR . '/cert_' . md5(uniqid('', true)) . '.p12';
        
        // 将解密后的证书内容写入临时文件
        $result = file_put_contents($tempFileName, $certificateContent, LOCK_EX);
        if ($result === false) {
            throw new BusinessException('无法写入临时证书文件', ErrCode::$BUSINESS_ERROR);
        }
        
        // 设置文件权限为只有当前用户可读
        chmod($tempFileName, 0400);
        
        // 如果之前有旧的临时文件，删除它
        if ($this->certificateFilePath !== null && file_exists($this->certificateFilePath)) {
            unlink($this->certificateFilePath);
        }
        
        // 更新证书文件路径和最后加载时间
        $this->certificateFilePath = $tempFileName;
        $this->lastLoadTime = time();
        
        // 注册关闭时的清理函数
        register_shutdown_function(function() use ($tempFileName) {
            if (file_exists($tempFileName)) {
                unlink($tempFileName);
            }
        });
        
        return $tempFileName;
    }
    
    /**
     * 获取证书密码
     * @return string 证书密码
     * @throws BusinessException 如果未设置密码
     */
    public function getCertificatePassword(): string
    {
        // 如果已缓存密码，直接返回
        if ($this->certificatePassword !== null) {
            return $this->certificatePassword;
        }
        
        // 从环境变量获取密码
        $password = env(self::ENV_CERTIFICATE_PASSWORD);
        if (empty($password)) {
            throw new BusinessException('未设置证书密码环境变量', ErrCode::$BUSINESS_ERROR);
        }
        
        $this->certificatePassword = base64_decode($password);
        return $this->certificatePassword;
    }
    
    /**
     * 从环境变量获取并解密证书内容
     * @return string 解密后的证书内容
     * @throws BusinessException 解密失败时抛出异常
     */
    private function getDecryptedCertificateContent(): string
    {
        // 如果已缓存解密后的内容，直接返回
        if ($this->certificateContent !== null && !$this->isCacheExpired()) {
            return $this->certificateContent;
        }
        
        // 获取加密的证书内容
        if (RUNTIME == 'dev') {
            $encryptedContent = env(self::ENV_CERTIFICATE_CONTENT);
        } else {
            $encryptedContent = EnumsService::getInstance()->getSettingEnvValue('electronic_invoice_certificate');
        }

        if (empty($encryptedContent)) {
            throw new BusinessException('未设置证书内容环境变量', ErrCode::$BUSINESS_ERROR);
        }
        
        // 获取加密密钥和随机数
        $encryptionKey = env(self::ENV_ENCRYPTION_KEY);
        $encryptionNonce = env(self::ENV_ENCRYPTION_NONCE);
        if (empty($encryptionKey) || empty($encryptionNonce)) {
            throw new BusinessException('未设置加密密钥或随机数环境变量', ErrCode::$BUSINESS_ERROR);
        }
        
        try {
            // 使用Sodium解密证书内容
            $this->certificateContent = $this->decryptWithSodium(
                base64_decode($encryptedContent),
                hex2bin($encryptionKey),
                hex2bin($encryptionNonce)
            );
            
            return $this->certificateContent;
        } catch (\Exception $e) {
            throw new BusinessException('证书内容解密失败: ' . $e->getMessage(), ErrCode::$BUSINESS_ERROR);
        }
    }
    
    /**
     * 使用Sodium解密数据
     * @param string $encryptedData 加密的数据
     * @param string $key 二进制密钥
     * @param string $nonce 二进制随机数
     * @return string 解密后的数据
     * @throws BusinessException|\SodiumException 解密失败时抛出异常
     */
    private function decryptWithSodium(string $encryptedData, string $key, string $nonce): string
    {
        // 检查Sodium扩展是否可用
        if (!function_exists('sodium_crypto_secretbox_open')) {
            throw new BusinessException('Sodium扩展未安装', ErrCode::$BUSINESS_ERROR);
        }
        
        try {
            // 使用Sodium解密
            $decrypted = sodium_crypto_secretbox_open(
                $encryptedData,
                $nonce,
                $key
            );
            
            if ($decrypted === false) {
                throw new BusinessException('Sodium解密失败，可能是密钥或随机数不正确', ErrCode::$BUSINESS_ERROR);
            }
            
            return $decrypted;
        } finally {
            // 安全清除敏感数据
            if (isset($key)) {
                sodium_memzero($key);
            }
        }
    }

    /**
     * 获取加密证书数据
     * @param $file_path
     * @return array
     * @throws BusinessException
     */
    public function getEncryptCertificate($file_path): array
    {
        // 读取证书内容
        $certificateContent = file_get_contents($file_path);
        if ($certificateContent === false) {
            throw new BusinessException('无法读取证书文件', ErrCode::$BUSINESS_ERROR);
        }

        // 验证证书和密码是否匹配
        $certs = [];
        if (!openssl_pkcs12_read($certificateContent, $certs, $this->getCertificatePassword())) {
            throw new BusinessException('证书密码不正确或证书格式无效', ErrCode::$BUSINESS_ERROR);
        }

        // 使用Sodium加密证书内容
        return $this->encryptWithSodium($certificateContent);
    }

    /**
     * 使用Sodium加密数据并生成环境变量配置
     * @param string $data 要加密的数据
     * @return array 环境变量配置
     */
    private function encryptWithSodium(string $data): array
    {
        // 生成随机密钥和随机数
        $key = sodium_crypto_secretbox_keygen();
        $nonce = random_bytes(SODIUM_CRYPTO_SECRETBOX_NONCEBYTES);

        // 加密数据
        $encrypted = sodium_crypto_secretbox($data, $nonce, $key);

        // 将加密数据转为base64
        $encryptedBase64 = base64_encode($encrypted);

        // 将密钥和随机数转为十六进制
        $keyHex = bin2hex($key);
        $nonceHex = bin2hex($nonce);

        // 安全清除敏感数据
        sodium_memzero($key);

        // 返回环境变量配置
        return [
            'certificate_encryption_key'   => $keyHex,
            'certificate_encryption_nonce' => $nonceHex,
            'certificate_p12_content'      => $encryptedBase64,
        ];
    }
    
    /**
     * 检查缓存是否过期
     * @return bool 是否过期
     */
    private function isCacheExpired(): bool
    {
        return (time() - $this->lastLoadTime) > self::CACHE_LIFETIME;
    }
    
    /**
     * 清除缓存和临时文件
     */
    public function clearCache(): void
    {
        // 删除临时证书文件
        if ($this->certificateFilePath !== null && file_exists($this->certificateFilePath)) {
            unlink($this->certificateFilePath);
        }
        
        // 清除内存中的数据
        $this->certificateContent = null;
        $this->certificatePassword = null;
        $this->certificateFilePath = null;
        $this->lastLoadTime = 0;
    }
    
    /**
     * 析构函数，清理资源
     */
    public function __destruct()
    {
        $this->clearCache();
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    private function __wakeup() {}
} 