<?php

namespace App\Modules\Invoice\Services;

use Phalcon\Di\Injectable;

/**
 * 基础服务类
 * 
 * 包含所有服务共享的基础功能
 */
class BaseService extends Injectable
{
    /**
     * 生成唯一标识
     * 
     * @param string $prefix 前缀
     * @return string
     */
    protected function generateId(string $prefix = ''): string
    {
        return $prefix . uniqid(time(), true);
    }
    
    /**
     * 记录调试日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    protected function debug(string $message, array $context = []): void
    {
        if ($this->getDI()->has('logger')) {
            $this->getDI()->get('logger')->debug($message, $context);
        }
    }
    
    /**
     * 记录信息日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    protected function info(string $message, array $context = []): void
    {
        if ($this->getDI()->has('logger')) {
            $this->getDI()->get('logger')->info($message, $context);
        }
    }
    
    /**
     * 记录警告日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    protected function warning(string $message, array $context = []): void
    {
        if ($this->getDI()->has('logger')) {
            $this->getDI()->get('logger')->warning($message, $context);
        }
    }
    
    /**
     * 记录错误日志
     * 
     * @param string $message 日志消息
     * @param array $context 上下文数据
     */
    protected function error(string $message, array $context = []): void
    {
        if ($this->getDI()->has('logger')) {
            $this->getDI()->get('logger')->error($message, $context);
        }
    }
} 