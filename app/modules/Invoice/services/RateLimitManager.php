<?php

namespace App\Modules\Invoice\Services;

use App\Library\Enums\InvoiceEnums;

/**
 * API请求限流管理器
 * 
 * 负责处理所有 API 请求的限流逻辑，包括请求计数、请求频率控制和限流处理
 */
class RateLimitManager
{
    /**
     * 限流规则配置 [API路径 => 每分钟最大请求数]
     * @var array
     */
    private $rateLimits = [
        'login'               => 12,  // 纳税人登录系统
        'loginOnBehalf'       => 12,  // 以中介身份登录系统
        'submitDocument'      => 100, // 提交文件
        'getSubmission'       => 300, // 获取提交
        'cancelDocument'      => 12,  // 取消文件
        'rejectDocument'      => 12,  // 拒绝文件
        'getDocument'         => 60,  // 获取文档
        'getDocumentDetail'   => 125, // 获取文档详细信息
        'getRecentDocuments'  => 12,  // 获取最近的文档
        'searchDocuments'     => 12,  // 搜索文档
        'searchTaxpayerTin'   => 60,  // 搜索纳税人的 TIN
        'taxpayerQrCode'      => 60,  // 纳税人二维码
        'default'             => 60,  // 默认限制
    ];
    
    /**
     * 内存中存储API请求计数
     * @var array
     */
    private static $counters = [];
    
    /**
     * 每个API类型最后请求时间的记录
     * @var array
     */
    private static $lastRequestTimes = [];
    
    /**
     * 获取API类型的请求限制
     * 
     * @param string $apiType API操作类型
     * @return int 每分钟最大请求数
     */
    public function getLimit(string $apiType): int
    {
        return $this->rateLimits[$apiType] ?? $this->rateLimits['default'];
    }
    
    /**
     * 从URL中提取API类型
     * 
     * @param string $url 请求URL
     * @return string API类型
     */
    public function getApiTypeFromUrl(string $url): string
    {
        // 从URL识别API类型
        if (strpos($url, '/document-submission') !== false) {
            if (strpos($url, 'POST') !== false) {
                return 'submitDocument';
            }
            return 'getSubmission';
        } elseif (strpos($url, '/document/') !== false) {
            if (strpos($url, '/raw') !== false) {
                return 'getDocument';
            } elseif (strpos($url, '/details') !== false) {
                return 'getDocumentDetail';
            } elseif (strpos($url, '/state') !== false) {
                if (strpos($url, 'cancelled') !== false) {
                    return 'cancelDocument';
                }
                return 'rejectDocument';
            }
        } elseif (strpos($url, '/document/recent') !== false) {
            return 'getRecentDocuments';
        } elseif (strpos($url, '/document/search') !== false) {
            return 'searchDocuments';
        } elseif (strpos($url, '/identity/login') !== false) {
            return 'login';
        } elseif (strpos($url, '/identity/loginOnBehalf') !== false) {
            return 'loginOnBehalf';
        } elseif (strpos($url, '/taxpayer/search') !== false) {
            return 'searchTaxpayerTin';
        } elseif (strpos($url, '/taxpayer/qrcode') !== false) {
            return 'taxpayerQrCode';
        }
        
        return 'default'; // 默认类型
    }
    
    /**
     * 检查是否可以发送请求，如果可以，增加计数器
     * 
     * @param string $apiType API操作类型
     * @param string $clientId 客户端ID
     * @return bool 是否允许请求
     */
    public function checkAndIncrement(string $apiType, string $clientId): bool
    {
        $limit = $this->getLimit($apiType);
        $window = floor(time() / 60); // 当前分钟的时间窗口
        $key = "rate_limit:{$apiType}:{$clientId}:{$window}";
        
        // 根据最后请求时间计算是否需要限速
        $this->applyClientSideThrottling($apiType);
        
        // 获取当前计数
        if (!isset(self::$counters[$key])) {
            self::$counters[$key] = 0;
        }
        
        if (self::$counters[$key] < $limit) {
            // 增加计数
            self::$counters[$key]++;
            return true;
        }
        
        $this->logRateLimitExceeded($apiType, $clientId, self::$counters[$key], $limit);
        return false;
    }
    
    /**
     * 记录限流信息
     * 
     * @param string $apiType API操作类型
     * @param string $clientId 客户端ID
     * @param int $count 当前计数
     * @param int $limit 限制数
     */
    private function logRateLimitExceeded(string $apiType, string $clientId, int $count, int $limit): void
    {
        $message = "Rate limit exceeded for {$apiType} (Client: {$clientId}): {$count}/{$limit}";
        error_log($message);
    }
    
    /**
     * 应用客户端侧限流，避免请求过快触发服务器限流
     * 
     * @param string $apiType API操作类型
     * @return void
     */
    private function applyClientSideThrottling(string $apiType): void
    {
        $limit = $this->getLimit($apiType);
        
        // 计算合理的请求间隔时间
        $minInterval = 60 / $limit * 0.9; // 留10%的余量
        
        // 获取上次请求时间
        $lastRequestTime = self::$lastRequestTimes[$apiType] ?? 0;
        $now = microtime(true);
        $timeSinceLast = $now - $lastRequestTime;
        
        // 如果距离上次请求时间太短，等待一段时间
        if ($lastRequestTime > 0 && $timeSinceLast < $minInterval) {
            $sleepTime = ($minInterval - $timeSinceLast) * 1000000; // 转为微秒
            error_log("客户端限流: API '{$apiType}' 延迟 " . round($sleepTime/1000) . " 毫秒");
            usleep((int)$sleepTime);
        }
        
        // 更新最后请求时间
        self::$lastRequestTimes[$apiType] = microtime(true);
    }
    
    /**
     * 处理429响应，根据Retry-After头和退避策略等待
     * 
     * @param \Exception $e 异常
     * @param string $apiType API操作类型
     * @param int $retryCount 重试次数
     * @return int 需要等待的秒数
     */
    public function handleRateLimitResponse(\Exception $e, string $apiType, int $retryCount): int
    {
        $retryAfter = 0;
        
        // 尝试从异常获取 Retry-After 头，安全地处理方法调用
        try {
            if (method_exists($e, 'getResponse')) {
                $response = $e->getResponse();
                if ($response && method_exists($response, 'getHeaderLine')) {
                    $retryAfter = (int)$response->getHeaderLine('Retry-After');
                }
            }
        } catch (\Exception $ex) {
            // 忽略获取响应头失败的异常
            error_log("Failed to get Retry-After header: " . $ex->getMessage());
        }
        
        // 如果没有Retry-After，应用指数退避
        if ($retryAfter <= 0) {
            $retryAfter = min(pow(2, $retryCount), 60); // 最多等待60秒
        }
        
        error_log("API '{$apiType}' 触发限流，等待 {$retryAfter} 秒后重试 (尝试 {$retryCount})");
        
        return $retryAfter;
    }
    
    /**
     * 重置特定API类型和客户端ID的限流计数
     * 
     * @param string $apiType API操作类型
     * @param string $clientId 客户端ID
     * @return bool 是否成功重置
     */
    public function resetCounter(string $apiType, string $clientId): bool
    {
        $window = floor(time() / 60);
        $key = "rate_limit:{$apiType}:{$clientId}:{$window}";
        
        if (isset(self::$counters[$key])) {
            unset(self::$counters[$key]);
            return true;
        }
        
        return false;
    }
} 