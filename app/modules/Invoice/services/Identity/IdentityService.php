<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace App\Modules\Invoice\Services\Identity;


use App\Library\Enums\InvoiceEnums;
use App\Modules\Invoice\Services\AbstractService;
use App\Modules\Invoice\Services\InvoiceClient;
use App\Traits\TokenTrait;
use Exception;

/**
 * Identity service
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
class IdentityService extends AbstractService
{
    use TokenTrait;

    /**
     * IdentityService constructor.
     *
     * @param InvoiceClient    $client
     * @param bool              $prodMode
     */
    public function __construct(InvoiceClient $client, $prodMode = false)
    {
        parent::__construct($client,
            $this->initApiUrl(InvoiceEnums::API_IDENTITY_PATH, $prodMode));
    }

    /**
     * Set token if you have it
     *
     * @param string $token
     *
     * @return array
     */
    public function setAccessToken($token)
    {
        $headers = $this->getClient()->getOption('headers');
        if(!$headers) {
            $headers = [];
        }
        $headers = array_merge($headers, [
            'Authorization' => 'Bearer ' . $token,
        ]);

        return $this->getClient()->setOption('headers', $headers);
    }

    /**
     * Extract auth token from client
     *
     * @return null|string
     */
    public function getAccessToken() : ?string
    {
        $authHeader = $this->getClient()->getOption('headers');

        if (!$authHeader) {
            return null;
        }

        if(!array_key_exists('Authorization', $authHeader)) {
            return null;
        }

        return substr($authHeader['Authorization'], 7);
    }

    /**
     * This should be the Tax Identification Number (TIN) of the taxpayer the intermediary is presenting
     *
     * @param string $onBehalfOf
     *
     * @return array
     */
    public function setOnbehalfof(string $onBehalfOf)
    {
        $headers = $this->getClient()->getOption('headers');
        if(!$headers) {
            $headers = [];
        }
        $headers = array_merge($headers, [
            'onbehalfof' => $onBehalfOf,
        ]);

        return $this->getClient()->setOption('headers', $headers);
    }

    /**
     * 设置过期时间戳
     * @param string $timestamp
     * @return mixed
     */
    public function setExpireAt(string $timestamp)
    {
        $headers = $this->getClient()->getOption('headers');
        if(!$headers) {
            $headers = [];
        }
        $headers = array_merge($headers, [
            'expire_at' => $timestamp,
        ]);

        return $this->getClient()->setOption('headers', $headers);
    }

    /**
     * 获取过期时间戳
     * @return mixed|null
     */
    public function getExpireAt()
    {
        $headers = $this->getClient()->getOption('headers');

        if (!$headers) {
            return null;
        }

        if(!array_key_exists('expire_at', $headers)) {
            return null;
        }

        return $headers['expire_at'];
    }

    /**
     * This API is used to authenticate the ERP system associated with a specific taxpayer calling and issue access token which allows ERP system to access those protected APIs.
     *
     * @param string|null $onBehalfOf Optional: Used by intermediary system to set (TIN) of the taxpayer the intermediary is presenting
     * @param string $grantType Optional: OAuth grant type
     * @param string $scope Optional: OAuth scope
     *
     * @return array
     * @throws Exception
     */
    public function login(string $onBehalfOf = null,  string $grantType = 'client_credentials', string $scope = 'InvoicingAPI'): array
    {
        if(!empty($onBehalfOf)) {
            $this->setOnbehalfof($onBehalfOf);
        }

        $body = [
            'form_params' => [
                'client_id'     => $this->getClient()->getClientId(),
                'client_secret' => $this->getClient()->getClientSecret(),
                'grant_type'    => $grantType,
                'scope'         => $scope,
            ],
        ];

        $response = $this->getClient()->request('POST', $this->getBaseUrl(), $body);

        if (is_array($response) && !array_key_exists('access_token', $response)) {
            throw new Exception('access_token not found!');
        }
        // 设置 token
        $this->setAccessToken($response['access_token']);

        // 设置过期时间戳
        $expireAt = time() + $response['expires_in'];
        $this->setExpireAt($expireAt);
        $response['expire_at'] = $expireAt;

        return $response;
    }
}
