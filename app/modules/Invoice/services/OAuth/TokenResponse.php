<?php

namespace App\Modules\Invoice\Services\OAuth;

class TokenResponse
{
    /** @var string */
    private $accessToken;

    /** @var string */
    private $tokenType;

    /** @var int */
    private $expiresIn;

    /** @var string */
    private $scope;

    /** @var int */
    private $createdAt;

    public function __construct(
        string $accessToken,
        string $tokenType,
        int $expiresIn,
        string $scope
    ) {
        $this->accessToken = $accessToken;
        $this->tokenType = $tokenType;
        $this->expiresIn = $expiresIn;
        $this->scope = $scope;
        $this->createdAt = time();
    }

    /**
     * 获取访问令牌
     * @return string
     */
    public function getAccessToken(): string
    {
        return $this->accessToken;
    }

    /**
     * 获取令牌类型
     * @return string
     */
    public function getTokenType(): string
    {
        return $this->tokenType;
    }

    /**
     * 获取过期时间（秒）
     * @return int
     */
    public function getExpiresIn(): int
    {
        return $this->expiresIn;
    }

    /**
     * 获取作用域
     * @return string
     */
    public function getScope(): string
    {
        return $this->scope;
    }

    /**
     * 获取认证头
     * @return string
     */
    public function getAuthorizationHeader(): string
    {
        return sprintf('%s %s', $this->tokenType, $this->accessToken);
    }
    
    /**
     * 检查令牌是否过期
     * @param int $buffer 缓冲时间（秒）
     * @return bool
     */
    public function isExpired(int $buffer = 300): bool
    {
        return (time() - $this->createdAt) > ($this->expiresIn - $buffer);
    }
} 