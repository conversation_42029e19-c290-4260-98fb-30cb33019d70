<?php

namespace App\Modules\Invoice\Services\OAuth;

use app\modules\Invoice\services\exceptions\OAuth\AuthenticationException;
use app\modules\Invoice\services\exceptions\OAuth\OAuthErrorException;
use App\Modules\Invoice\Services\InvoiceClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;

/**
 * OAuth认证服务
 * 整合了认证和缓存功能，支持不同身份的Client管理
 */
class OAuthService
{
    /** @var string */
    private $clientId;
    
    /** @var string */
    private $clientSecret;

    /** @var bool */
    private $proMode;
    
    /** @var int */
    private $cacheBuffer = 300; // 5分钟缓冲时间
    
    /** @var InvoiceClient[] */
    private static $clientCache = [];

    /**
     * @var
     */
    private $logger;

    /**
     * 构造函数
     *
     * @param string $clientId 客户端ID
     * @param string $clientSecret 客户端密钥
     * @param bool $proMode
     * @param $logger
     */
    public function __construct(
        string $clientId,
        string $clientSecret,
        bool $proMode,
        $logger
    ) {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->proMode = $proMode;
        $this->logger = $logger;
    }
    
    /**
     * 获取InvoiceClient实例
     * 
     * @param string|null $onBehalfOf 代理身份标识
     * @param bool $forceRefresh 是否强制刷新
     * @return InvoiceClient
     * @throws AuthenticationException
     * @throws OAuthErrorException
     */
    public function getClient(?string $onBehalfOf = null, bool $forceRefresh = false): InvoiceClient
    {
        try {
            // 获取 client ，首先从缓存中获取 client，如没有则 new 新的 client
            $client = $this->getClientFromCache($onBehalfOf);

            if (!$client) {
                $this->logger->info('client not exists');
                // 创建新的客户端
                $client = new InvoiceClient($this->clientId, $this->clientSecret, $this->proMode);
            }

            // 判断 client是否存在 token失效时间，如果不存在则 login
            // 检查令牌是否已过期 (生成 token 时的时间戳 + expire_in(3600s) - 缓冲时间(300s))
            $expireAt           = $client->identity->getExpireAt();
            $hasExpireTimeStamp = $expireAt - $this->cacheBuffer;

            if (!$expireAt || $hasExpireTimeStamp <= time() || $forceRefresh) {
                // 已存在 token判断是否过期，如过期则通过 client 调用 login 方法即可
                // 是否强制刷新，如是，如过期则通过 client 调用 login 方法即可
                $loginResponse = $client->identity->login(
                    $onBehalfOf
                );
                $this->logger->info(sprintf('login info: %s, %s', $onBehalfOf, json_encode($loginResponse)));
                if (!isset($loginResponse['access_token']) || !isset($loginResponse['expires_in'])) {
                    throw new OAuthErrorException('invalid_response', 'Login response missing required fields');
                }
                
                // 缓存客户端
                $cacheKey = $this->getCacheKey($onBehalfOf);
                
                // 更新内存缓存
                self::$clientCache[$cacheKey] = $client;
            }
            
            // 返回 client
            return $client;
            
        } catch (RequestException $e) {
            // 尝试解析错误响应
            if (method_exists($e, 'getResponse') && $e->getResponse()) {
                $errorData = json_decode($e->getResponse()->getBody()->getContents(), true);
                if (isset($errorData['error'])) {
                    throw new OAuthErrorException(
                        $errorData['error'],
                        $errorData['error_description'] ?? '',
                        $errorData['error_uri'] ?? null,
                        $e->getCode(),
                        $e
                    );
                }
            }
            
            throw new AuthenticationException(
                'Failed to obtain client: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        } catch (GuzzleException $e) {
            throw new AuthenticationException(
                'Failed to obtain client: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        } catch (\Exception $e) {
            throw new AuthenticationException(
                'Failed to obtain client: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * 强制刷新客户端
     * 
     * @param string|null $onBehalfOf 代理身份标识
     * @return InvoiceClient
     * @throws AuthenticationException|OAuthErrorException
     */
    public function refreshClient(?string $onBehalfOf = null): InvoiceClient
    {
        return $this->getClient($onBehalfOf, true);
    }
    
    /**
     * 清除特定身份的客户端缓存
     * 
     * @param string|null $onBehalfOf 代理身份标识
     * @return bool
     */
    public function clearClientCache(?string $onBehalfOf = null): bool
    {
        $cacheKey = $this->getCacheKey($onBehalfOf);
        
        // 清除内存缓存
        if (isset(self::$clientCache[$cacheKey])) {
            unset(self::$clientCache[$cacheKey]);
        }
        return true;
    }
    
    /**
     * 清除所有客户端缓存
     * 
     * @return bool
     */
    public function clearAllClientCache(): bool
    {
        // 清除内存缓存
        self::$clientCache = [];
        $this->logger->info('clearAllClientCache done!');

        return true;
    }

    /**
     * 设置缓冲时间
     * 
     * @param int $seconds 缓冲时间（秒）
     * @return $this
     */
    public function setCacheBuffer(int $seconds): self
    {
        $this->cacheBuffer = max(60, $seconds);
        return $this;
    }

    /**
     * 获取缓存 Key 值
     * @param string|null $onBehalfOf
     * @return string
     */
    private function getCacheKey(?string $onBehalfOf): string
    {
        return 'invoice_oauth_client_' . ($onBehalfOf ? md5($onBehalfOf) : 'default');
    }

    /**
     * 从 cache 中获取 Client
     * @param string|null $onBehalfOf
     * @return InvoiceClient|null
     */
    private function getClientFromCache(string $onBehalfOf = null): ?InvoiceClient
    {
        $cacheKey = $this->getCacheKey($onBehalfOf);

        // 从静态缓存中获取
        if (isset(self::$clientCache[$cacheKey])) {
            $this->logger->info('client exists');
            return self::$clientCache[$cacheKey];
        }
        return null;
    }
} 