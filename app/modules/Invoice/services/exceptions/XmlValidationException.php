<?php

namespace app\modules\Invoice\services\exceptions;

class XmlValidationException extends \Exception
{
    private $errors;
    
    public function __construct(string $message = "", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->errors = $message;
    }
    
    public function getValidationErrors(): string
    {
        return $this->errors;
    }
} 