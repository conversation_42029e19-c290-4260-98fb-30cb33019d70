<?php

namespace app\modules\Invoice\services\exceptions;

class SyncException extends \Exception
{
    private $voucherId;
    
    public function __construct(string $message = "", string $voucherId = '', int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
        $this->voucherId = $voucherId;
    }
    
    public function getVoucherId(): string
    {
        return $this->voucherId;
    }
} 