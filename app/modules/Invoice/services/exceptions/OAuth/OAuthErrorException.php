<?php

namespace app\modules\Invoice\services\exceptions\OAuth;

class OAuthErrorException extends \Exception
{
    /** @var string */
    private $error;

    /** @var string|null */
    private $errorUri;
    
    /**
     * 构造函数
     * 
     * @param string $error 错误代码
     * @param string $message 错误描述
     * @param string|null $errorUri 错误URI
     * @param int $code 错误码
     * @param \Throwable|null $previous 前一个异常
     */
    public function __construct(
        string $error,
        string $message = "",
        ?string $errorUri = null,
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->error = $error;
        $this->errorUri = $errorUri;
    }
    
    /**
     * 获取错误代码
     * 
     * @return string
     */
    public function getError(): string
    {
        return $this->error;
    }
    
    /**
     * 获取错误URI
     * 
     * @return string|null
     */
    public function getErrorUri(): ?string
    {
        return $this->errorUri;
    }
    
    /**
     * 获取HTTP状态码
     * 
     * @return int
     */
    public function getHttpStatusCode(): int
    {
        switch ($this->error) {
            case 'invalid_grant':
            case 'unsupported_grant_type':
            case 'invalid_scope':
            case 'invalid_request':
                return 400;
            case 'invalid_client':
                return 401;
            case 'unauthorized_client':
                return 403;
            default:
                return 500;
        }
    }
} 