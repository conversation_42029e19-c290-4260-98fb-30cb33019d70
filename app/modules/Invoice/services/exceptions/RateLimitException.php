<?php

namespace app\modules\Invoice\services\exceptions;

/**
 * 限流异常类
 * 
 * 在API调用触发限流时抛出，包含重试信息
 */
class RateLimitException extends \Exception
{
    /**
     * API类型
     * @var string
     */
    protected $apiType;
    
    /**
     * 建议的重试等待时间（秒）
     * @var int
     */
    protected $retryAfter;
    
    /**
     * 构造函数
     * 
     * @param string $message 错误消息
     * @param string $apiType API类型
     * @param int $retryAfter 重试等待时间
     * @param int $code 错误代码
     * @param \Throwable|null $previous 上一个异常
     */
    public function __construct(
        string $message,
        string $apiType,
        int $retryAfter = 0,
        int $code = 429,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        $this->apiType = $apiType;
        $this->retryAfter = $retryAfter;
    }
    
    /**
     * 获取API类型
     * 
     * @return string API类型
     */
    public function getApiType(): string
    {
        return $this->apiType;
    }
    
    /**
     * 获取建议的重试等待时间
     * 
     * @return int 重试等待时间（秒）
     */
    public function getRetryAfter(): int
    {
        return $this->retryAfter;
    }
} 