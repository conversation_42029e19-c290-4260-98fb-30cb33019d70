<?php

namespace App\Modules\Invoice\Services;

use App\Modules\Invoice\Services\Document\DocumentService;
use App\Modules\Invoice\Services\Document\DocumentSubmissionService;
use App\Modules\Invoice\Services\Document\DocumentTypeService;
use app\modules\Invoice\services\exceptions\RateLimitException;
use App\Modules\Invoice\Services\Identity\IdentityService;
use App\Modules\Invoice\Services\Notification\NotificationService;
use App\Modules\Invoice\Services\Taxpayer\TaxPayerService;
use App\Modules\Invoice\Services\Taxpayer\TaxPayersService;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\BadResponseException;
use Psr\Http\Client\ClientInterface;

class InvoiceClient extends BaseService
{
    /**
     * Client Id for MyInvois API
     *
     * @var string
     */
    private $clientId;

    /**
     * Client Secret for MyInvois API
     *
     * @var string
     */
    private $clientSecret;

    /**
     * Production mode flag
     *
     * @var boolean
     */
    private $prodMode;

    /**
     * ClientInterface object
     *
     * @var ClientInterface
     */
    private $httpClient;

    /**
     * Options options control various aspects of a request including, headers, query string parameters, timeout settings, the body of a request
     *
     * @var array
     */
    private $options = [];

    /**
     * 服务列表
     * @var AbstractService[]
     */
    private $services = [];

    /**
     * 服务对应的 class
     * @var string[]
     */
    private $serviceMap = [
        'identity'           => IdentityService::class,
        'document'           => DocumentService::class,
        'documentSubmission' => DocumentSubmissionService::class,
        'documentType'       => DocumentTypeService::class,
        'notification'       => NotificationService::class,
        'taxPayer'           => TaxPayerService::class,
        'taxPayers'          => TaxPayersService::class,
    ];

    /**
     * Client constructor.
     *
     * @param string $clientId
     * @param string $clientSecret
     * @param bool $prodMode
     * @param ClientInterface|null $httpClient
     */
    public function __construct(string $clientId, string $clientSecret, bool $prodMode = false, ClientInterface $httpClient = null)
    {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;
        $this->prodMode = $prodMode;
        $this->setHttpClient($httpClient ?: new Client());
    }

    /**
     * @param string $clientId
     *
     * @return void
     */
    public function setClientId(string $clientId)
    {
        $this->clientId = $clientId;
    }

    /**
     * @return string
     */
    public function getClientId(): string
    {
        return $this->clientId;
    }

    /**
     * @param string $clientSecret
     *
     * @return void
     */
    public function setClientSecret(string $clientSecret)
    {
        $this->clientSecret = $clientSecret;
    }

    /**
     * @return string
     */
    public function getClientSecret(): string
    {
        return $this->clientSecret;
    }

    /**
     * @param ClientInterface $client
     *
     * @return void
     */
    public function setHttpClient(ClientInterface $client)
    {
        $this->httpClient = $client;
    }

    /**
     * @return mixed
     */
    public function getHttpClient()
    {
        return $this->httpClient;
    }

    /**
     * Set options for HttpClient.
     *
     * @param array $options
     *
     * @return array
     */
    public function setOptions(array $options): array
    {
        return $this->options = $options;
    }

    /**
     * Set individual option.
     *
     * @param string $key
     * @param mixed $value
     *
     * @return mixed
     */
    public function setOption(string $key, $value)
    {
        return $this->options[$key] = $value;
    }

    /**
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @param $key
     *
     * @return bool|mixed
     * @return void
     */
    public function getOption($key)
    {
        if (isset($this->options[$key])) {
            return $this->options[$key];
        }

        return false;
    }

    /**
     * Make a request through ClientInterface.
     *
     * @param $method
     * @param $url
     * @param array $options
     *
     * @throws RateLimitException|Exception
     *
     * @return mixed
     */
    public function request($method, $url, array $options = [])
    {
        $body = '';
        $options = array_merge($this->getOptions(), $options);
        
        // 创建或获取限流管理器实例
        static $rateLimitManager = null;
        if ($rateLimitManager === null) {
            $rateLimitManager = new RateLimitManager();
        }
        
        // 从URL识别API类型
        $apiType = $rateLimitManager->getApiTypeFromUrl($method . ' ' . $url);
        
        // 最大重试次数
        $maxRetries = 3;
        $retryCount = 0;
        
        while ($retryCount <= $maxRetries) {
            try {
                // 限流检查 - 如果达到限制，等待或重试
                if (!$rateLimitManager->checkAndIncrement($apiType, $this->clientId)) {
                    $retryCount++;
                    if ($retryCount > $maxRetries) {
                        throw new RateLimitException(
                            "达到API请求限制，已尝试{$maxRetries}次重试",
                            $apiType,
                            60 // 60秒后再试
                        );
                    }
                    
                    // 等待进入下一个窗口
                    $waitTime = 60 - (time() % 60) + 1;
                    // 使用 error_log 确保总是能记录
                    error_log("客户端触发限流保护，等待 {$waitTime} 秒进入下一个窗口");
                    sleep($waitTime);
                    continue;
                }
                
                // 执行实际的HTTP请求
                $response = $this->getHttpClient()->request($method, $url, $options);
                $body = json_decode($response->getBody(), true, JSON_FORCE_OBJECT);
                return $body;
            } catch (BadResponseException $exception) {
                // 检查是否是限流响应(429)
                $statusCode = 0;
                
                try {
                    $response = $exception->getResponse();
                    if ($response) {
                        $statusCode = $response->getStatusCode();
                    }
                } catch (\Exception $e) {
                    // 无法获取响应或状态码
                    error_log("无法获取响应状态码: " . $e->getMessage());
                }
                
                if ($statusCode === 429) {
                    $retryCount++;
                    if ($retryCount > $maxRetries) {
                        $this->handleError($exception);
                    }
                    
                    // 处理限流响应，获取等待时间
                    $waitTime = $rateLimitManager->handleRateLimitResponse($exception, $apiType, $retryCount);
                    sleep($waitTime);
                    continue;
                }
                
                // 非限流错误，直接处理
                $this->handleError($exception);
            } catch (\Exception $e) {
                // 记录其他异常
                error_log("API请求异常({$apiType}): " . $e->getMessage());
                throw $e;
            }
            $retryCount++;
        }
        
        // 如果达到这里，说明所有重试都失败
        throw new RateLimitException(
            "API请求失败: 达到最大重试次数",
            $apiType,
            60, // 建议60秒后再试
            500
        );
    }

    /**
     *  Throw errors.
     *
     * @param BadResponseException $exception
     *
     * @throws PHP Exception
     *
     */
    protected function handleError(Exception $e)
    {
        $body = $e->getResponse()->getBody();
        $errorCode = $e->getResponse()->getStatusCode();

        throw new Exception($body, $errorCode);
    }

    /**
     * 魔术方法获取服务
     * @param $name
     * @return mixed|null
     */
    public function __get($name)
    {
        if (isset($this->serviceMap[$name]) && !isset($this->services[$name])) {
            $serviceClass = $this->serviceMap[$name];
            $this->services[$name] = new $serviceClass($this, $this->prodMode);
        }

        return $this->services[$name] ?? null;
    }
} 