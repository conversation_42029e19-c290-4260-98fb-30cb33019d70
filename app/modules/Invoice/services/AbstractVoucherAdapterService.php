<?php

namespace app\modules\Invoice\services;

use App\Library\Enums\InvoiceEnums;
use App\Library\Enums\PurchaseEnums;
use app\modules\Invoice\services\ubl\Constant\CountryCodes;
use app\modules\Invoice\services\validation\AbstractVoucher;
use App\Modules\Purchase\Services\BaseService;

/**
 * 发票适配器服务抽象基类
 * 负责从不同业务场景中获取数据并转换为对应的凭证对象
 */
abstract class AbstractVoucherAdapterService extends BaseService
{
    /**
     * 创建电子发票凭证
     * @param int $companyId 公司ID
     * @param array $data 数据
     * @param string $datatype
     * @return AbstractVoucher
     */
    abstract public function createVoucher(int $companyId, array $data,  string $datatype): AbstractVoucher;

    /**
     * 将数据转换为凭证数据
     * @param array $data 原始数据
     * @param string $datatype
     * @return array 凭证数据
     */
    abstract protected function transformData(array $data, string $datatype): array;

    /**
     * 构建供应商信息
     * @param array $baseData 基础数据
     * @return array 供应商数据
     */
    protected function buildSupplierData(array $baseData): array
    {
        return [
            //供应商名
            'name'        => $baseData['supplier']['vendor_name'] ?? '',
            //供应商的马来西亚标准行业分类 (MSIC) 代码（00000不适用）
            'msic_code'   => '00000',
            //party Element
            'party_details' => [
                //供应商TIN
                'TIN' => InvoiceEnums::INVOICE_SUPPLIER_TIN_ABROAD,
                //供应商的注册/身份证号码/护照号码
                'BRN' => $baseData['supplier']['identification_no'] ?? '',
                //供应商的 SST 注册号码（取供应商信息中的销售和服务税登记号码，没有值传NA）
                'SST' => $baseData['supplier']['sales_and_service_tax_number'] ?: 'NA',
            ],
            //供应商业务活动描述
            'description' => 'NOT APPLICABLE',
            //供应商联系电话
            'phone'       => $baseData['supplier']['contact_phone'] ?? '',
            //供应商地址
            'address'     => [
                //地址行0
                'line0'   => $baseData['supplier']['company_address'] ?? '',
                //城市名称
                'city'    => 'NA',
                //州/省
                'state'   => InvoiceEnums::STATE_CODE_NOT_APPLICABLE,
                //国家
                'country' => $baseData['supplier']['e_invoice_code'] ?? '',
            ],
        ];
    }

    /**
     * 构建买方信息
     * @param array $baseData 基础数据
     * @return array 买方数据
     */
    protected function buildBuyerData(array $baseData): array
    {
        $buyerData = [
            //买方名
            'name'       => $baseData['buyer']['Name'],
            //party Element
            'party_details' => [
                //买方TIN
                'TIN' => $baseData['buyer']['TIN'],
                //买方的注册/身份证号码/护照号码
                'BRN' => $baseData['buyer']['Registration Number'],
            ],
            //买方联系电话
            'phone'      => $baseData['buyer']['Contact Number'] ?? '',
            //买方地址
            'address'    => [
                //地址行0
                'line0'   => $baseData['buyer']['Address'] ?? '',
                //城市名称
                'city'    => $baseData['buyer']['City Name'] ?? '',
                //州/省
                'state'   => $baseData['buyer']['State'] ?? '',
                //国家
                'country' => CountryCodes::MALAYSIA, //固定 MYS
            ],
        ];

        //买方的 SST 注册号码
        if (!empty($baseData['buyer']['SST Registration Number'])) {
            $buyerData['party_details']['SST'] = $baseData['buyer']['SST Registration Number'];
        }
        return $buyerData;
    }

    /**
     * 构建明细项目
     * @param array $baseData 基础数据
     * @return array 明细项目数据
     */
    protected function buildItemsData(array $baseData): array
    {
        $lines = [];
        // 如果基础数据中有明细项目，直接使用
        if (isset($baseData['invoiceLineItem']) && is_array($baseData['invoiceLineItem'])) {
            foreach ($baseData['invoiceLineItem'] as $item) {
                //构建明细行
                $lines[] = $this->buildLineItemDetail($item, $baseData);
            }
        }
        return $lines;
    }

    /**
     * 构建分类
     * @description 根据"采购订单的采购类型", 划分分类
     * @param $purchase_type
     * @return string
     */
    protected function buildClassification($purchase_type): string
    {
        return $purchase_type == PurchaseEnums::PURCHASE_TYPE_SERVICE
            ? InvoiceEnums::CLASSIFICATION_CODE_IMPORTATION_SERVICES
            : InvoiceEnums::CLASSIFICATION_CODE_IMPORTATION_GOODS;
    }

    /**
     * 构建税种
     * @description Tax Amount如果为0固定传06, Tax Amount如果不为0固定传02
     * @param $tax_amount
     * @return string
     */
    protected function buildTaxType($tax_amount): string
    {
        return bccomp($tax_amount, "0", 2) === 0
            ? InvoiceEnums::TAX_TYPE_NOT_APPLICABLE
            : InvoiceEnums::TAX_TYPE_SERVICE;
    }

    /**
     * 计算补税税额
     * @description
     * 1. 针对关联的采购订单的采购类型为：服务类
     *  - 若发票税额≠0 ：则传输0
     *  - 若发票税额=0：发票金额（不含税）*8%  ，四舍五入保留2位小数
     * 2. 针对关联的采购订单的采购类型不是：服务类
     *  - 传输0
     * @param $item
     * @param $purchase_type
     * @return string
     */
    protected function calculateTaxAmount($item, $purchase_type): string
    {
        $roundedTax = '0';
        if ($purchase_type == PurchaseEnums::PURCHASE_TYPE_SERVICE && $item['ticket_tax'] == 0) {
            $actualAmount = bcdiv($item['ticket_amount_not_tax'], "1000", 4);
            $taxRate      = bcmul(InvoiceEnums::TAX_RATE_FIXED_RATE, 0.01, 2);
            $tax          = bcmul($actualAmount, $taxRate, 4);
            $taxFloat     = (float)$tax;
            $roundedTax   = number_format($taxFloat, 2, '.', '');
        }
        return $roundedTax;
    }

    /**
     * 构建明细行
     * @param $item
     * @param $baseData
     * @return array
     */
    protected function buildLineItemDetail($item, $baseData)
    {
        $purchaseType = $baseData['purchase_type'];
        //分类
        $classification = $this->buildClassification($purchaseType);

        $lineItem = [
            //分类
            'classification'      => $classification,
            //产品或服务描述
            'description'         => $item['product_desc'] ?? '',
            //数量
            'quantity'            => 1,
            //计量单位
            'measurement'         => InvoiceEnums::MEASUREMENT_TYPE_UNIT,
        ];

        if ($purchaseType == PurchaseEnums::PURCHASE_TYPE_SERVICE && $item['ticket_tax'] == 0) {
            //计算税额
            $taxAmount = $this->calculateTaxAmount($item, $purchaseType);

            $line = [
                //单价
                'unit_price'          => bcdiv($item['ticket_amount_not_tax'], 1000, 2),
                //税种
                'tax_type'            => $this->buildTaxType($taxAmount),
                //税率
                'tax_rate'            => (string)InvoiceEnums::TAX_RATE_FIXED_RATE,
                //税额
                'tax_amount'          => $taxAmount,
                //小计
                'subtotal'            => bcdiv($item['ticket_amount_not_tax'], 1000, 2),
                //总计（不含税）
                'total_excluding_tax' => bcdiv($item['ticket_amount_not_tax'], 1000, 2),
            ];
        } else {
            $line = [
                //单价
                'unit_price'          => bcdiv($item['ticket_amount'], 1000, 2),
                //税种
                'tax_type'            => $this->buildTaxType('0'),
                //税率
                'tax_rate'            => (string)InvoiceEnums::TAX_RATE_DEFAULT,
                //税额
                'tax_amount'          => '0',
                //小计
                'subtotal'            => bcdiv($item['ticket_amount'], 1000, 2),
                //总计（不含税）
                'total_excluding_tax' => bcdiv($item['ticket_amount'], 1000, 2),
            ];
        }
        return array_merge($lineItem, $line);
    }

    /**
     * 构建税额相关数据
     * @param array $baseData
     * @param $invoiceItemsData
     * @return array
     */
    protected function buildTaxData(array $baseData, $invoiceItemsData)
    {
        // 采购订单的采购类型
        $purchaseType = $baseData['purchase_type'];
        // 发票金额总计
        $invoiceTotal = bcdiv($baseData['receipt_amount'], 1000, 2);
        // 发票税额总计
        $amount = bcdiv($baseData['ticket_amount_tax'], 1000, 2);
        // 总税额
        $totalTaxAmount = $this->calculateTotalTaxAmount($invoiceItemsData);
        // 本次付款金额
        $totalPayableAmount = bcdiv($baseData['cur_amount'], 1000, 2);

        $totalTax = [
            //总计（不含税）
            'total_excluding_tax'   => $invoiceTotal,
            //税种
            'tax_type' => $this->buildTaxType($totalTaxAmount),
        ];

        // 清关编号
        if (!empty($baseData['clearance_no'])) {
            $totalTax['clearance_no'] = $baseData['clearance_no'];
        }

        if ($purchaseType == PurchaseEnums::PURCHASE_TYPE_SERVICE && bccomp($amount, "0", 2) === 0) {
            $taxInfo = [
                // 总计（含税）
                'total_including_tax'           => bcadd($invoiceTotal, $totalTaxAmount, 2),
                // 应付总额
                'total_payable_amount'          => bcadd($totalPayableAmount, $totalTaxAmount, 2),
                // 总税额
                'total_tax_amount'              => $totalTaxAmount,
                // 每种税种的总税额
                'total_tax_amount_per_tax_type' => $totalTaxAmount,
            ];
        } else {
            $taxInfo = [
                // 总计（含税）
                'total_including_tax'           => $invoiceTotal,
                // 应付总额
                'total_payable_amount'          => $totalPayableAmount,
                // 总税额
                'total_tax_amount'              => '0',
                // 每种税种的总税额
                'total_tax_amount_per_tax_type' => '0',
            ];
        }
        return array_merge($totalTax, $taxInfo);
    }

    /**
     * 计算总税额
     * @param $invoiceItemsData
     * @return string
     */
    protected function calculateTotalTaxAmount($invoiceItemsData): string
    {
        $totalTaxAmount = '0';
        foreach ($invoiceItemsData as $item) {
            $totalTaxAmount = bcadd($totalTaxAmount, $item['tax_amount'], 2);
        }
        return $totalTaxAmount;
    }
}