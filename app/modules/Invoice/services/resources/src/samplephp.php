<?php

// 文件说明 2025.04.18
// 该文件为官方提供代码样例，用于获取*.p12 文件中存储的证书、私钥文件等信息
// 提取出的文件用于签名

// Get the current script path
$currentPath = dirname(__FILE__);

// Output the current script path
echo 'Current script path: ' . $currentPath . PHP_EOL;

// Path to the .p12 file
$p12FilePath = $currentPath . DIRECTORY_SEPARATOR ."sample-co.p12"; //include your softcert here
$p12Password = "zaq12wsx"; //include your softcert pin here
echo $p12FilePath ;
echo "\n";

// Check if the file exists and is readable
if (!file_exists($p12FilePath)) {
    die('The .p12 file does not exist: ' . $p12FilePath);
}
if (!is_readable($p12FilePath)) {
    die('The .p12 file is not readable: ' . $p12FilePath);
}

// Load the .p12 file
try {
    $p12Content = file_get_contents($p12FilePath);
    if ($p12Content === false) {
        die('Failed to read the .p12 file');
    }
}

//catch exception
catch(Exception $e) {
    echo 'Message: ' .$e->getMessage();
}


// Extract the certificate and private key from the .p12 file
$certs = [];
if (!openssl_pkcs12_read($p12Content, $certs, $p12Password)) {
    // Detailed error message
    while ($error = openssl_error_string()) {
        echo "OpenSSL Error: $error\n";
    }
    die('Failed to parse the .p12 file');
}

// The private key
$privateKey = openssl_pkey_get_private($certs['pkey']);

// Data to sign
$data = 'This is the data to sign';

// Sign the data
$signature = '';
if (!openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
    die('Failed to sign the data');
}

// Free the private key from memory
openssl_free_key($privateKey);

// Output the signature in base64 format
echo 'Signature: ' . base64_encode($signature) . PHP_EOL;

// If needed, output the certificate
echo 'Certificate: ' . $certs['cert'] . PHP_EOL;
?>
