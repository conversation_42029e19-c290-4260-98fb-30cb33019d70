<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
  <UBLExtensions xmlns="urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2">
    <UBLExtension>
      <ExtensionURI>urn:oasis:names:specification:ubl:dsig:enveloped:xades</ExtensionURI>
      <ExtensionContent>
        <sig:UBLDocumentSignatures xmlns:sig="urn:oasis:names:specification:ubl:schema:xsd:CommonSignatureComponents-2"
                                   xmlns:sac="urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2"
                                   xmlns:sbc="urn:oasis:names:specification:ubl:schema:xsd:SignatureBasicComponents-2">
          <sac:SignatureInformation>
            <cbc:ID>urn:oasis:names:specification:ubl:signature:1</cbc:ID>
            <sbc:ReferencedSignatureID>urn:oasis:names:specification:ubl:signature:Invoice</sbc:ReferencedSignatureID>
            <ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#" Id="signature">
              <ds:SignedInfo>
                <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n11#" />
                <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256" />
                <ds:Reference Id="id-doc-signed-data" URI="">
                  <ds:Transforms>
                    <ds:Transform Algorithm="http://www.w3.org/TR/1999/REC-xpath-19991116">
                      <ds:XPath>not(//ancestor-or-self::ext:UBLExtensions)</ds:XPath>
                    </ds:Transform>
                    <ds:Transform Algorithm="http://www.w3.org/TR/1999/REC-xpath-19991116">
                      <ds:XPath>not(//ancestor-or-self::cac:Signature)</ds:XPath>
                    </ds:Transform>
                    <ds:Transform Algorithm="http://www.w3.org/2006/12/xml-c14n11" />
                  </ds:Transforms>
                  <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" />
                  <ds:DigestValue>QU6YSC4oEx0HPkYA3QgFf/g+20HXy9iEH+F8cH/A1xtQ/kTsIgB9I/aBi4jak5F97zCyIsXCBvWdvRWOzrLIm7T+C9/XL6nG9SJV6AZ+jjc/QSXjLH9Gvc0IsDrt/zghm761B0BkTdkmBrnKImL/UUxiGpKmCIQW0XxbBpRxixYM/dHsNCNZggX/W1Vhnssbl/5colRr/yo0cK5sU4YJkk1CNHBVf1Gx1GYePI1X2mN76NW9RBbOV4BsLuI8zVU0S81BEIxWeLqI8NxZ+ZvgucDURjUv9f532ZQ5HkpNEyy3TQ0Gn6NkRANxuBzeWRT2BR6FPFtJ3falk3bdCESFvQ==</ds:DigestValue>
                </ds:Reference>
                <ds:Reference Type="http://www.w3.org/2000/09/xmldsig#SignatureProperties" URI="#id-xades-signed-props">
                  <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" />
                  <ds:DigestValue>MTJiNjM4OTliNjZhNDI4NTNhOTQxOTkyYzkwOTQwNWM5ODA2YTk0ZDA4MTg1OTMwZmU5ZWM5YzhkZTk3ZjIwNg==</ds:DigestValue>
                </ds:Reference>
              </ds:SignedInfo>
              <ds:SignatureValue></ds:SignatureValue>
              <ds:KeyInfo>
                <ds:X509Data>
                  <ds:X509Certificate>****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************</ds:X509Certificate>
                </ds:X509Data>
              </ds:KeyInfo>
              <ds:Object>
                <xades:QualifyingProperties xmlns:xades="http://uri.etsi.org/01903/v1.3.2#" Target="signature">
                  <xades:SignedProperties Id="id-xades-signed-props">
                    <xades:SignedSignatureProperties>
                      <xades:SigningTime>2025-04-09T07:20:00Z</xades:SigningTime>
                      <xades:SigningCertificate>
                        <xades:Cert>
                          <xades:CertDigest>
                            <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256" />
                            <ds:DigestValue>MDI1ZmQyNmFkYzBmZGE2NDg3MjhiZTA2OWQ5ZGNhNGM3NjJhZmQ5NjdlNzM0NjNmNjkxMjUxYTU1NzhiMTRlMA==</ds:DigestValue>
                          </xades:CertDigest>
                          <xades:IssuerSerial>
                            <ds:X509IssuerName>C = MY, O = LHDNM, OU = Terms of use at http://www.posdigicert.com.my, CN = Trial LHDNM Sub CA V1</ds:X509IssuerName>
                            <ds:X509SerialNumber>************</ds:X509SerialNumber>
                          </xades:IssuerSerial>
                        </xades:Cert>
                      </xades:SigningCertificate>
                    </xades:SignedSignatureProperties>
                  </xades:SignedProperties>
                </xades:QualifyingProperties>
              </ds:Object>
            </ds:Signature>
          </sac:SignatureInformation>
        </sig:UBLDocumentSignatures>
      </ExtensionContent>
    </UBLExtension>
  </UBLExtensions>
  <cbc:ID>XML-SBINV12345</cbc:ID>
  <cbc:IssueDate>2025-04-09</cbc:IssueDate>
  <cbc:IssueTime>04:00:00Z</cbc:IssueTime>
  <cbc:InvoiceTypeCode listVersionID="1.1">11</cbc:InvoiceTypeCode>
  <cbc:DocumentCurrencyCode>MYR</cbc:DocumentCurrencyCode>
  <cbc:TaxCurrencyCode>MYR</cbc:TaxCurrencyCode>
  <cac:InvoicePeriod>
    <cbc:StartDate>2025-04-01</cbc:StartDate>
    <cbc:EndDate>2025-04-09</cbc:EndDate>
    <cbc:Description>Monthly</cbc:Description>
  </cac:InvoicePeriod>
  <cac:BillingReference>
    <cac:AdditionalDocumentReference>
      <cbc:ID>151891-1981</cbc:ID>
    </cac:AdditionalDocumentReference>
  </cac:BillingReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>L1</cbc:ID>
    <cbc:DocumentType>CustomsImportForm</cbc:DocumentType>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>FTA</cbc:ID>
    <cbc:DocumentType>FreeTradeAgreement</cbc:DocumentType>
    <cbc:DocumentDescription>Sample Description</cbc:DocumentDescription>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>L1</cbc:ID>
    <cbc:DocumentType>K2</cbc:DocumentType>
  </cac:AdditionalDocumentReference>
  <cac:AdditionalDocumentReference>
    <cbc:ID>L1</cbc:ID>
  </cac:AdditionalDocumentReference>
  <cac:AccountingSupplierParty>
    <cbc:AdditionalAccountID schemeAgencyName="CertEX">CPT-CCN-W-211111-KL-000002</cbc:AdditionalAccountID>
    <cac:Party>
      <cbc:IndustryClassificationCode name="Wholesale of computer hardware, software and peripherals">46510</cbc:IndustryClassificationCode>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TIN">EI00000000010</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="BRN">************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="SST">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TTX">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:CityName>Kuala Lumpur</cbc:CityName>
        <cbc:PostalZone>50480</cbc:PostalZone>
        <cbc:CountrySubentityCode>14</cbc:CountrySubentityCode>
        <cac:AddressLine>
          <cbc:Line>Lot 66</cbc:Line>
        </cac:AddressLine>
        <cac:AddressLine>
          <cbc:Line>Bangunan Merdeka</cbc:Line>
        </cac:AddressLine>
        <cac:AddressLine>
          <cbc:Line>Persiaran Jaya</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1" listAgencyID="6">MYS</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Supplier's Name</cbc:RegistrationName>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Telephone>+***********</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TIN">C24646488070</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="BRN">************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="SST">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyIdentification>
        <cbc:ID schemeID="TTX">NA</cbc:ID>
      </cac:PartyIdentification>
      <cac:PostalAddress>
        <cbc:CityName>Kuala Lumpur</cbc:CityName>
        <cbc:PostalZone>50480</cbc:PostalZone>
        <cbc:CountrySubentityCode>14</cbc:CountrySubentityCode>
        <cac:AddressLine>
          <cbc:Line>Lot 66</cbc:Line>
        </cac:AddressLine>
        <cac:AddressLine>
          <cbc:Line>Bangunan Merdeka</cbc:Line>
        </cac:AddressLine>
        <cac:AddressLine>
          <cbc:Line>Persiaran Jaya</cbc:Line>
        </cac:AddressLine>
        <cac:Country>
          <cbc:IdentificationCode listID="ISO3166-1" listAgencyID="6">MYS</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Buyer's Name</cbc:RegistrationName>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Telephone>+***********</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode>01</cbc:PaymentMeansCode>
    <cac:PayeeFinancialAccount>
      <cbc:ID>**********</cbc:ID>
    </cac:PayeeFinancialAccount>
  </cac:PaymentMeans>
  <cac:PaymentTerms>
    <cbc:Note>Payment method is cash</cbc:Note>
  </cac:PaymentTerms>
  <cac:PrepaidPayment>
    <cbc:ID>E12345678912</cbc:ID>
    <cbc:PaidAmount currencyID="MYR">1.00</cbc:PaidAmount>
    <cbc:PaidDate>2024-07-23</cbc:PaidDate>
    <cbc:PaidTime>00:30:00Z</cbc:PaidTime>
  </cac:PrepaidPayment>
  <cac:AllowanceCharge>
    <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
    <cbc:AllowanceChargeReason>Sample Description</cbc:AllowanceChargeReason>
    <cbc:Amount currencyID="MYR">100</cbc:Amount>
  </cac:AllowanceCharge>
  <cac:AllowanceCharge>
    <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
    <cbc:AllowanceChargeReason>Service charge</cbc:AllowanceChargeReason>
    <cbc:Amount currencyID="MYR">100</cbc:Amount>
  </cac:AllowanceCharge>
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="MYR">87.63</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="MYR">87.63</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="MYR">87.63</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>01</cbc:ID>
        <cac:TaxScheme>
          <cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">OTH</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="MYR">1436.50</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="MYR">1436.50</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="MYR">1436.50</cbc:TaxInclusiveAmount>
    <cbc:AllowanceTotalAmount currencyID="MYR">1436.50</cbc:AllowanceTotalAmount>
    <cbc:ChargeTotalAmount currencyID="MYR">1436.50</cbc:ChargeTotalAmount>
    <cbc:PayableRoundingAmount currencyID="MYR">0.30</cbc:PayableRoundingAmount>
    <cbc:PayableAmount currencyID="MYR">1436.50</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>
  <cac:InvoiceLine>
    <cbc:ID>1234</cbc:ID>
    <cbc:InvoicedQuantity unitCode="C62">1</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="MYR">1436.50</cbc:LineExtensionAmount>
    <cac:AllowanceCharge>
      <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReason>Sample Description</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>0.15</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="MYR">100</cbc:Amount>
    </cac:AllowanceCharge>
    <cac:AllowanceCharge>
      <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
      <cbc:AllowanceChargeReason>Sample Description</cbc:AllowanceChargeReason>
      <cbc:MultiplierFactorNumeric>0.10</cbc:MultiplierFactorNumeric>
      <cbc:Amount currencyID="MYR">100</cbc:Amount>
    </cac:AllowanceCharge>
    <cac:TaxTotal>
      <cbc:TaxAmount currencyID="MYR">0</cbc:TaxAmount>
      <cac:TaxSubtotal>
        <cbc:TaxableAmount currencyID="MYR">1460.50</cbc:TaxableAmount>
        <cbc:TaxAmount currencyID="MYR">0</cbc:TaxAmount>
        <cbc:Percent>6.00</cbc:Percent>
        <cac:TaxCategory>
          <cbc:ID>E</cbc:ID>
          <cbc:TaxExemptionReason>Exempt New Means of Transport</cbc:TaxExemptionReason>
          <cac:TaxScheme>
            <cbc:ID schemeID="UN/ECE 5153" schemeAgencyID="6">OTH</cbc:ID>
          </cac:TaxScheme>
        </cac:TaxCategory>
      </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:Item>
      <cbc:Description>Laptop Peripherals</cbc:Description>
      <cac:OriginCountry>
        <cbc:IdentificationCode>MYS</cbc:IdentificationCode>
      </cac:OriginCountry>
      <cac:CommodityClassification>
        <cbc:ItemClassificationCode listID="PTC">9800.00.0010</cbc:ItemClassificationCode>
      </cac:CommodityClassification>
      <cac:CommodityClassification>
        <cbc:ItemClassificationCode listID="CLASS">003</cbc:ItemClassificationCode>
      </cac:CommodityClassification>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="MYR">17</cbc:PriceAmount>
    </cac:Price>
    <cac:ItemPriceExtension>
      <cbc:Amount currencyID="MYR">100</cbc:Amount>
    </cac:ItemPriceExtension>
  </cac:InvoiceLine>
</Invoice>
