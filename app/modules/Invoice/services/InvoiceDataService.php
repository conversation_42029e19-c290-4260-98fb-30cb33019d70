<?php

namespace App\Modules\Invoice\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\InvoiceEnums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\HrStaffItemsModel;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Models\oa\AgencyPaymentModel;
use App\Models\oa\InvoiceSubmissionsDetailModel;
use App\Models\oa\OrdinaryPaymentModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Services\EnumsService;
use app\modules\Invoice\services\ubl\Constant\CountryCodes;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentPersonal;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Vendor\Models\SupplierOwnershipModel;
use App\Modules\Vendor\Models\VendorPaymentDetailModel;
use DateTime;

class InvoiceDataService extends \App\Library\BaseService
{
    protected $direction = 'normal'; //发票方向 (normal=正向发票, credit=贷记发票)
    /**
     * @var array 买家公司ID
     */
    protected $buyer_company_ids;

    /**
     * @var array 买家信息Map
     */
    private $buyer_company_map; //买方配置中的公司信息

    /**
     * @var array 费用类型
     */
    private $cost_types_ids;

    /**
     * @var array 费用类型配置
     */
    private $cost_types_map;

    /**
     * @var array 供应商ID(如：CNS-0001)
     */
    private $supplier_ids;

    /**
     * @var array 非马来供应商 ID
     */
    protected $non_my_supplier_ids;

    /**
     * @var string
     */
    protected $current_date;

    public function __construct()
    {
        // 设定默认语言为英语
        self::setLanguage('en');

        // 获取买家配置信息
        $this->buyer_company_map = EnumsService::getInstance()->getSettingEnvValueMap('electronic_invoice_company');
        $this->buyer_company_ids = array_keys($this->buyer_company_map);

        // 获取代理支付发票费用类型
        $cost_types_info = EnumsService::getInstance()->getSettingEnvValueMap('electronic_invoice_cost_type');
        $this->cost_types_ids = array_column($cost_types_info, 'id');
        $this->cost_types_map = array_column($cost_types_info, 'code', 'id');

        // 获取普通付款配置的供应商
        $this->supplier_ids = EnumsService::getInstance()->getSettingEnvValueIds('electronic_invoice_supplier');

        // 非马来供应商 ID
        $this->non_my_supplier_ids = $this->getNonMyVendor();
    }

    /**
     * 获取有效的费用公司
     * @return array|int[]|string[]
     */
    public function getCostCompanyId(): array
    {
        if (RUNTIME == 'dev') {
            $costTypeList = [];
            return !empty($costTypeList) ? $costTypeList: $this->buyer_company_ids;
        }
        return $this->buyer_company_ids;
    }

    /**
     * 获取买家信息
     * @param $companyId
     * @return array
     */
    public function getBuyerInfo($companyId): array
    {
        return $this->buyer_company_map[$companyId];
    }

    /**
     * 获取费用类型
     * @param $cost_type
     * @return mixed|string
     */
    private function getCostType($cost_type)
    {
        return $this->cost_types_map[$cost_type] ?? '';
    }

    /**
     * 获取配置的供应商
     * @return array
     */
    private function getConfigSupplierId(): array
    {
        return $this->supplier_ids ?? [];
    }

    /**
     * 获取非马来归属地的供应商
     * @return array
     */
    private function getNonMySupplierIds(): array
    {
        return $this->non_my_supplier_ids ?? [];
    }

    /**
     * 获取普通付款订单数据
     * @param int $company_id
     * @param int $page
     * @param int $pageSize
     * @param string $datatype
     * @return array
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    public function getOrdinaryPaymentDataByCompany(int $company_id, int $page, int $pageSize, string $datatype): array
    {
        if (empty($company_id)) {
            return [];
        }

        // 采购付款单详情
        $ordinaryPaymentList = $this->getOrdinaryPaymentList($company_id, $pageSize, ($page - 1) * $pageSize, $datatype);
        if (empty($ordinaryPaymentList)) {
            return [];
        }

        //是否存在付款对象为个人
        $individualStaffInfoIds = $individualMapList = $individualStaffInfo = [];

        // 找出收款人类型为"个人"的数据
        $individualPaymentList = array_map(function ($v) {
            return $v['payee_type'] == Enums::PAYEE_TYPE_PERSONAL ? $v['id'] : null;
        }, $ordinaryPaymentList);
        $individualPaymentList = array_values(array_filter($individualPaymentList));
        if (!empty($individualPaymentList)) {
            // 根据 ID 找出第一条收款人信息
            $staffInfoList = OrdinaryPaymentPersonal::find([
                'conditions' => 'ordinary_payment_id in({ordinary_payment_id:array})',
                'bind' => [
                    'ordinary_payment_id' => $individualPaymentList,
                ],
                'columns' => 'ordinary_payment_id,staff_info_id',
                'order' => 'id ASC',
            ])->toArray();

            foreach ($staffInfoList as $staffInfo) {
                if (!isset($individualMapList[$staffInfo['ordinary_payment_id']])) {
                    $individualMapList[$staffInfo['ordinary_payment_id']] = $staffInfo['staff_info_id'];
                }
            }
            $individualStaffInfoIds = array_values($individualMapList);
        }
        if (!empty($individualStaffInfoIds)) {
            // 收款人信息
            $individualStaffInfo = $this->getStaffBasicInfo($individualStaffInfoIds);

            // 收款人居住地址
            $residentialAddress = $this->getResidentialAddress($individualStaffInfoIds);
            foreach ($individualStaffInfo as &$staffInfoItem) {
                $staffInfoItem['address'] = $residentialAddress[$staffInfoItem['staff_info_id']] ?? '';
            }
        }

        // 供应商详情
        $vendorIds       = array_column($ordinaryPaymentList, 'supplier_id');
        $vendorIds       = array_values(array_filter($vendorIds));
        $vendorGroupList = $this->getCompanySupplierDetail($vendorIds);

        // 明细数据
        $ordinaryPaymentIds = array_column($ordinaryPaymentList, 'id');
        $ordinaryPaymentItemList = OrdinaryPaymentDetail::find([
            'conditions' => 'ordinary_payment_id in({ordinary_payment_id:array})',
            'bind' => [
                'ordinary_payment_id' => $ordinaryPaymentIds,
            ],
            'columns' => 'ordinary_payment_id,budget_id,amount_no_tax,amount_vat,amount_have_tax,wht_rate,amount_wht,vat_rate',
        ])->toArray();
        $budgetIds = array_column($ordinaryPaymentItemList, 'budget_id');
        $ordinaryPaymentItemList = array_group_by_column($ordinaryPaymentItemList, 'ordinary_payment_id');

        // 获取付款分类
        $budgetService = new BudgetService();
        $budgets = $budgetService->budgetObjectList($budgetIds);
        $budgetsMapList = array_column($budgets, 'name_en', 'id');

        foreach ($ordinaryPaymentList as &$ordinaryPayment) {
            //币种
            $ordinaryPayment['currency']      = static::$t->_(GlobalEnums::$currency_item[$ordinaryPayment['currency']]);
            $ordinaryPayment['business_date'] = date('Y-m-d', strtotime($ordinaryPayment['business_at']));

            //追加供应商信息
            if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
                $supplierInfo = $this->getOrdinaryIndividualSupplierInfo($ordinaryPayment, $individualMapList, $individualStaffInfo);
            } else {
                $supplierInfo = $this->getOrdinaryCompanySupplierInfo($ordinaryPayment, $vendorGroupList);
            }
            $ordinaryPayment['supplier'] = $supplierInfo;

            //追加买方信息
            $ordinaryPayment['buyer'] = $this->getBuyerInfo($ordinaryPayment['cost_company_id']);

            // 明细行
            $invoiceLineItem                    = $ordinaryPaymentItemList[$ordinaryPayment['id']] ?? [];
            $totalTaxAmount                     = 0;
            foreach ($invoiceLineItem as &$item) {
                // 组装明细数据
                $item['classification'] = $this->buildClassification($ordinaryPayment, $vendorGroupList);
                $item['description']    = $budgetsMapList[$item['budget_id']];
                $taxInfo                = $this->calculateTaxInfo($item, $ordinaryPayment, $vendorGroupList);
                $taxDetail              = $this->getTaxDetail($item, $ordinaryPayment, $vendorGroupList);
                $item                   = array_merge($item, $taxInfo, $taxDetail);

                // 计算总税额
                $totalTaxAmount = bcadd($totalTaxAmount, $item['tax_amount'], 2);
            }
            $ordinaryPayment['invoiceLineItem'] = $invoiceLineItem;

            // 总税额
            $ordinaryPayment['total_tax_amount'] = $this->calculateTotalTaxAmount($ordinaryPayment, $vendorGroupList, $totalTaxAmount);

            // 总计（含税）
            $ordinaryPayment['total_including_tax'] = $this->calcTotalIncludingTax($ordinaryPayment, $vendorGroupList,
                $ordinaryPayment['amount_total_have_tax'],
                $ordinaryPayment['total_tax_amount']
            );

            //总计（不含税）
            $ordinaryPayment['total_excluding_tax'] = $this->getTotalExcludingTax($ordinaryPayment, $vendorGroupList);
            
            //实付总额
            $ordinaryPayment['total_payable_amount'] = $this->calcTotalIncludingTax($ordinaryPayment, $vendorGroupList,
                $ordinaryPayment['amount_total_actually'],
                $ordinaryPayment['total_tax_amount']
            );

            //每种税种的总税额
            $ordinaryPayment['total_tax_amount_per_tax_type'] = $ordinaryPayment['total_tax_amount'];

            //税种
            $ordinaryPayment['tax_type'] = $this->buildTaxType($ordinaryPayment['total_tax_amount']);
        }
        return $ordinaryPaymentList;
    }

    /**
     * 获取采购付款订单数据
     * @param int $company_id
     * @param int $page
     * @param int $pageSize
     * @param string $datatype
     * @return array
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    public function getPurchasePaymentDataByCompany(int $company_id, int $page, int $pageSize, string $datatype): array
    {
        if (empty($company_id)) {
            return [];
        }

        // 采购付款单详情
        $purchasePaymentList = $this->getPurchasePaymentList($company_id, $pageSize, ($page - 1) * $pageSize, $datatype);
        if (empty($purchasePaymentList)) {
            return [];
        }
        $purchasePaymentIds = array_column($purchasePaymentList, 'id');
        $purchasePaymentReceipt = PurchasePaymentReceipt::find([
            'conditions' => 'ppid IN ({ids:array})',
            'bind' => [
                'ids' => $purchasePaymentIds,
            ],
        ])->toArray();
        $purchasePaymentReceiptGroupList = array_group_by_column($purchasePaymentReceipt, 'ppid');

        // 供应商详情
        $vendorIds       = array_column($purchasePaymentList, 'vendor_id');
        $vendorGroupList = $this->getCompanySupplierDetail($vendorIds);

        // 发票明细
        foreach ($purchasePaymentList as &$purchasePayment) {

            //币种/业务日期
            $purchasePayment['currency']      = static::$t->_(GlobalEnums::$currency_item[$purchasePayment['currency']]);
            $purchasePayment['business_date'] = date('Y-m-d', strtotime($purchasePayment['business_at']));

            //追加供应商信息
            if (array_key_exists($purchasePayment['vendor_id'], $vendorGroupList)) {
                //供应商详情
                $purchasePayment['supplier'] = $vendorGroupList[$purchasePayment['vendor_id']];
            }

            //追加买方信息
            $purchasePayment['buyer'] = $this->getBuyerInfo($purchasePayment['cost_company_id']);

            //追加明细
            $invoiceLineItem = $purchasePaymentReceiptGroupList[$purchasePayment['id']] ?? [];
            if (empty($invoiceLineItem)) {
                $this->logger->error("invoiceLineItem empty, purchasePayment id {$purchasePayment['id']}");
                continue;
            }
            $purchasePayment['invoiceLineItem'] = $invoiceLineItem;
        }
        return $purchasePaymentList;
    }

    /**
     * 获取代理支付数据
     * @param int $company_id
     * @param int $page
     * @param int $pageSize
     * @param string $datatype
     * @return array
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    public function getAgencyPaymentDataByCompany(int $company_id, int $page, int $pageSize, string $datatype): array
    {
        if (empty($company_id)) {
            return [];
        }

        // 获取代理支付
        $agencyPaymentList = $this->getAgentPaymentList($company_id, $pageSize, ($page - 1) * $pageSize, $datatype);
        if (empty($agencyPaymentList)) {
            return [];
        }

        // 获取收款人工号
        $staffInfo = $detailAddress = $storeInfo = [];
        $payeeStaffIds = array_column($agencyPaymentList, 'payee_staff_id');
        if (!empty($payeeStaffIds)) {
            $staffInfo = $this->getStaffBasicInfo($payeeStaffIds);
        }

        if (!empty($staffInfo)) {
            $detailAddress = $this->getResidentialAddress($payeeStaffIds);

            // 获取收款人隶属网点
            $sysStoreIds = array_column($staffInfo, 'sys_store_id');

            if (!empty($sysStoreIds)) {
                $storeInfo = SysStoreModel::find([
                    'conditions' => 'id IN ({sys_store_id:array})',
                    'bind' => [
                        'sys_store_id' => $sysStoreIds,
                    ],
                    'columns' => 'id,detail_address',
                ])->toArray();
                $storeInfo = array_column($storeInfo, 'detail_address','id');
            }
        }

        // 发票明细
        foreach ($agencyPaymentList as &$agencyPayment) {

            //币种
            $agencyPayment['currency']      = static::$t->_(GlobalEnums::$currency_item[$agencyPayment['currency']]);
            $agencyPayment['business_date'] = date('Y-m-d', strtotime($agencyPayment['business_at']));

            //追加供应商信息
            $agencyPayment['supplier'] = $this->getAgencySupplierInfo($agencyPayment, $staffInfo, $detailAddress, $storeInfo);

            //追加买方信息
            $agencyPayment['buyer'] = $this->getBuyerInfo($agencyPayment['cost_company_id']);

            // 明细行
            $invoiceLineItem = [
                'classification'   => $this->getCostType($agencyPayment['cost_type']),
                'description'      => static::$t->_(sprintf('agency_payment_cost_type.%d',
                    $agencyPayment['cost_type'])),
                'amount_no_tax'    => $agencyPayment['amount_no_tax'],
                'vat_rate'         => $agencyPayment['vat_rate'],
                'amount_total_vat' => $agencyPayment['amount_total_vat'],
            ];

            $agencyPayment['invoiceLineItem'][] = $invoiceLineItem;
        }

        return $agencyPaymentList;
    }

    /**
     * 计算数据范围开始结束时间
     * @description 当地时间每天凌晨1点推送审批通过日期在当前日期-1所属月1号到当前日期-1 day的数据(包含边界值)，
     * 例如当前日期为2025-03-01，则推送的数据为审批通过日期在2025-02-01到2025-02-28号的数据，
     * 如果当前日期为2025-03-02，则推送的数据为审批通过日期在2025-03-01到2025-03-01的数据，
     * 如果当前日期为2025-03-10，则推送的数据为审批通过日期在2025-03-01到2025-03-09的数据
     *
     * @return array
     * @throws \DateMalformedStringException
     */
    private function calcValidateDatePeriod($date = null): array
    {
        // 获取当前日期（假设当前日期为脚本运行时的日期）
        if (empty($date)) {
            $currentDate = new DateTime();
        } else {
            $currentDate = new DateTime($date);
        }

        // 计算昨天的日期
        $yesterday = clone $currentDate;
        $yesterday->modify('-1 day');

        // 判断是否是某月的第一天
        if ($currentDate->format('j') == 1) {
            // 当前是某月1号：推送上个月1号到上个月最后一天的数据
            $startDate = clone $currentDate;
            $startDate->modify('first day of last month');

            $endDate = clone $currentDate;
            $endDate->modify('last day of last month');
        } else {
            // 当前不是某月1号：推送本月1号到昨天的数据
            $startDate = clone $currentDate;
            $startDate->modify('first day of this month');

            $endDate = $yesterday;
        }
        return [
            $startDate->format('Y-m-d'),
            $endDate->format('Y-m-d'),
        ];
    }

    /**
     * 追加供应商信息
     * @param $agencyPayment
     * @param $staffInfo
     * @param $detailAddress
     * @param $storeInfo
     * @return array
     */
    private function getAgencySupplierInfo($agencyPayment, $staffInfo, $detailAddress, $storeInfo)
    {
        $supplierInfo = [
            'vendor_name'       => $agencyPayment['bank_account_name'],
            'identification_no' => $agencyPayment['payee_id_no'],
            'address'           => $agencyPayment['payee_address'],
            'contact_phone'     => $agencyPayment['payee_mobile'],
        ];
        if (empty($supplierInfo['identification_no'])) {
            $supplierInfo['identification_no'] = isset($staffInfo[$agencyPayment['payee_staff_id']], $staffInfo[$agencyPayment['payee_staff_id']]['identity'])
                ? $staffInfo[$agencyPayment['payee_staff_id']]['identity']
                : '';
        }

        if (empty($supplierInfo['address'])) {
            $supplierInfo['address'] = $detailAddress[$agencyPayment['payee_staff_id']] ?? '';

            if (empty($supplierInfo['address'])) {
                if (isset($staffInfo[$agencyPayment['payee_staff_id']], $staffInfo[$agencyPayment['payee_staff_id']]['sys_store_id'])) {
                    $storeId = $staffInfo[$agencyPayment['payee_staff_id']]['sys_store_id'];
                    $supplierInfo['address'] = $storeInfo[$storeId] ?? '';
                }
            }
        }

        if (empty($supplierInfo['contact_phone'])) {
            $supplierInfo['contact_phone'] = isset($staffInfo[$agencyPayment['payee_staff_id']], $staffInfo[$agencyPayment['payee_staff_id']]['mobile'])
                ? $staffInfo[$agencyPayment['payee_staff_id']]['mobile']
                : '';
        }
        $supplierInfo['address'] = $this->formatAddress($supplierInfo['address']);

        return $supplierInfo;
    }

    /**
     * 获取归属地非马来的供应商
     * @return array
     */
    private function getNonMyVendor(): array
    {
        $myVendor    = Vendor::find([
            'conditions' => 'ownership != :ownership:',
            'bind'       => [
                'ownership' => Enums::VENDOR_OWNERSHIP_MALAYSIA,
            ],
            'columns'    => 'vendor_id',
        ])->toArray();
        return array_column($myVendor, 'vendor_id');
    }

    /**
     * 获取供应商
     * @return array
     */
    private function getVendorIds(): array
    {
        //供应商归属地非马来
        $nonMyVendor = $this->getNonMySupplierIds();

        //供应商属于配置(加ENV)中的供应商
        $configVendorIds = $this->getConfigSupplierId();

        //供应商的公司性质为自然人
        $individualVendorIds = $this->getIndividualVendorId();

        return array_merge($nonMyVendor, $configVendorIds, $individualVendorIds);
    }

    /**
     * 获取公司性质是自然人的
     * @return array
     */
    private function getIndividualVendorId(): array
    {
        $vendor = Vendor::find([
            'conditions' => 'company_nature = :company_nature:',
            'bind'       => [
                'company_nature' => Enums\VendorEnums::COMPANY_NATURE_INDIVIDUAL,
            ],
            'columns'    => 'vendor_id',
        ])->toArray();
        return array_column($vendor, 'vendor_id');
    }

    /**
     * 获取供应商详情
     * @param $ordinaryPayment
     * @param $vendorGroupList
     * @return array
     */
    private function getOrdinaryCompanySupplierInfo($ordinaryPayment, $vendorGroupList): array
    {
        $supplierId     = $ordinaryPayment['supplier_id'];
        $supplierDetail = $vendorGroupList[$supplierId] ?? [];
        $address        = $this->formatAddress($supplierDetail['company_address']);

        return [
            'tin'            => $supplierDetail['ownership'] == Enums::VENDOR_OWNERSHIP_MALAYSIA
                ? Enums\InvoiceEnums::INVOICE_SUPPLIER_TIN_DOMESTIC
                : Enums\InvoiceEnums::INVOICE_SUPPLIER_TIN_ABROAD,
            'brn'            => $supplierDetail['company_nature'] == VendorEnums::COMPANY_NATURE_COMPANY
                ? $supplierDetail['identification_no']
                : null,
            'nric'           => $supplierDetail['company_nature'] == VendorEnums::COMPANY_NATURE_INDIVIDUAL && $supplierDetail['certificate_type'] == VendorEnums::CERTIFICATE_TYPE_ID
                ? $supplierDetail['identification_no']
                : null,
            'passport'       => $supplierDetail['company_nature'] == VendorEnums::COMPANY_NATURE_INDIVIDUAL && $supplierDetail['certificate_type'] == VendorEnums::CERTIFICATE_TYPE_PASSPORT
                ? $supplierDetail['identification_no']
                : null,
            'sst'            => !empty($supplierDetail['sales_and_service_tax_number'])
                ? $supplierDetail['sales_and_service_tax_number']
                : 'NA',
            'vendor_name'    => $supplierDetail['vendor_name'],
            'address'        => $address,
            'contact_phone'  => $supplierDetail['contact_phone'],
            'e_invoice_code' => $supplierDetail['e_invoice_code'],
        ];
    }

    /**
     * 获取供应商详情
     * @param $ordinaryPayment
     * @param $individualMapList
     * @param $staffInfo
     * @return array
     */
    private function getOrdinaryIndividualSupplierInfo($ordinaryPayment, $individualMapList, $staffInfo): array
    {
        $staffInfoId = $individualMapList[$ordinaryPayment['id']] ?? '';
        if (empty($staffInfoId)) {
            return [];
        }
        $staffInfoDetail = $staffInfo[$staffInfoId] ?? [];
        $address = $this->formatAddress($staffInfoDetail['address'] ?? '');

        return [
            'tin'            => Enums\InvoiceEnums::INVOICE_SUPPLIER_TIN_DOMESTIC,
            'brn'            => null,
            'nric'           => $staffInfoDetail['identity'],
            'sst'            => null,
            'vendor_name'    => $staffInfoDetail['name'] ?? '',
            'address'        => $address,
            'contact_phone'  => $staffInfoDetail['mobile'] ?? '',
            'e_invoice_code' => CountryCodes::MALAYSIA, //固定 MYS
        ];
    }

    /**
     * 根据 ID 获取供应商信息
     * @param array $vendorIds
     * @return mixed
     */
    private function getVendorListById(array $vendorIds)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'v.id',
            'v.vendor_id',
            'v.vendor_name',
            'v.identification_no',
            'v.sales_and_service_tax_number',   //销售和服务税登记号码
            'so.e_invoice_code',
            'v.company_address',
            'v.ownership',
            'v.company_nature',
            'v.certificate_type',
        ]);
        $builder->from(['v' => Vendor::class]);
        $builder->join(SupplierOwnershipModel::class, 'v.ownership = so.id', 'so');
        $builder->inWhere('v.vendor_id', $vendorIds);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param array $vendorMainIds
     * @return mixed
     */
    private function getVendorPaymentDetail(array $vendorMainIds)
    {
        if (empty($vendorMainIds)) {
            return [];
        }
        return VendorPaymentDetailModel::find([
            'conditions' => 'main_id IN ({main_id:array})',
            'bind'       => [
                'main_id' => $vendorMainIds,
            ],
            'columns'    => 'id,main_id,contact_phone',
            'order'      => 'id ASC',
        ])->toArray();
    }

    /**
     * 获取供应商详情
     * @param array $vendorIds
     * @return array
     */
    private function getCompanySupplierDetail(array $vendorIds): array
    {
        $vendorList      = $this->getVendorListById($vendorIds);
        $vendorGroupList = array_column($vendorList, null, 'vendor_id');

        //供应商联系方式（取供应商信息中支付信息中第一行的联系电话）
        $vendorMainIds           = array_column($vendorList, 'id');
        $vendorPaymentDetail     = $this->getVendorPaymentDetail($vendorMainIds);
        $vendorPaymentDetailList = [];
        foreach ($vendorPaymentDetail as $vendorPayment) {
            if (!isset($vendorPaymentDetailList[$vendorPayment['main_id']])) {
                $vendorPaymentDetailList[$vendorPayment['main_id']] = $vendorPayment['contact_phone'];
            }
        }
        foreach ($vendorGroupList as &$vendor) {
            $mainId = $vendor['id'];
            if (isset($vendorPaymentDetailList[$mainId])) {
                $vendor['contact_phone'] = $vendorPaymentDetailList[$mainId];
            }
            $vendor['company_address'] = $this->formatAddress($vendor['company_address']);
        }
        return $vendorGroupList;
    }

    /**
     * 获取居住地址
     * @param array $individualStaffInfoIds
     * @return array
     */
    private function getResidentialAddress(array $individualStaffInfoIds): array
    {
        $detailAddress = HrStaffItemsModel::find([
            'conditions' => 'staff_info_id IN ({staff_id:array}) and item in( \'RESIDENCE_DETAIL_ADDRESS\', \'RESIDENCE_ADDRESS\')',
            'bind'       => [
                'staff_id' => $individualStaffInfoIds,
            ],
            'columns'    => 'staff_info_id,value',
        ])->toArray();
        return array_column($detailAddress, 'value', 'staff_info_id');
    }

    /**
     * 获取员工的基本信息
     * @param array $staffIds
     * @return array
     */
    private function getStaffBasicInfo(array $staffIds): array
    {
        $staffInfo = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id IN ({staff_id:array})',
            'bind'       => [
                'staff_id' => $staffIds,
            ],
            'columns'    => 'staff_info_id,name,identity,sys_store_id,mobile',
        ])->toArray();

        return array_column($staffInfo, null, 'staff_info_id');
    }

    /**
     * 构建分类代码
     * @param $ordinaryPayment
     * @param array $vendorGroupList
     * @return string
     */
    private function buildClassification($ordinaryPayment, array $vendorGroupList): string
    {
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            return Enums\InvoiceEnums::CLASSIFICATION_CODE_OTHERS;
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];
            $configSupplier = $this->getConfigSupplierId();

            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA) {
                return Enums\InvoiceEnums::CLASSIFICATION_CODE_IMPORTATION_SERVICES;
            } else if (in_array($supplierId, $configSupplier)) {
                return Enums\InvoiceEnums::CLASSIFICATION_CODE_CONSOLIDATED_E_INVOICE;
            } else {
                return Enums\InvoiceEnums::CLASSIFICATION_CODE_OTHERS;
            }
        }
    }

    /**
     * 计算 Tax Amount、Tax Rate、Tax Type
     * @description
     * 1）Tax Amount
     * 1. 外国供应商：服务类-无清关编号
     *  - 有SST ：0
     *  - 无SST：不含税金额*8% 四舍五入保留2位小数
     * 2. 外国供应商：服务类-有清关编号
     *  - 有SST ：0
     * 3. 其他场景
     *  - 金额详情中的SST税额
     *
     * 2）Tax Rate
     * 1. 外国供应商：服务类-无清关编号
     *  - 有SST :0
     *  - 无SST：8
     * 2. 外国供应商：非服务类-有清关编号
     *  - 0
     * 3. 其他场景
     *  - 金额详情中的SST税率，10%的税率传10
     * 3)Tax Type
     * - Tax Amount如果为0固定传06
     * - Tax Amount如果不为0固定传02
     *
     * @param $item
     * @param $ordinaryPayment
     * @param $vendorGroupList
     * @return array
     */
    private function calculateTaxInfo($item, $ordinaryPayment, $vendorGroupList): array
    {
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            $taxAmount = $item['amount_vat'];
            $taxRate   = $item['vat_rate'];
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];
            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA && empty($ordinaryPayment['clearance_no'])) {
                if ($this->isExistSSTTax($item['amount_vat'])) { //无 SST
                    $taxAmount = $this->calculateTaxWithoutSST($item['amount_no_tax']);
                    $taxRate   = InvoiceEnums::TAX_RATE_FIXED_RATE;
                } else { //有 SST
                    $taxAmount = '0';
                    $taxRate   = '0';
                }
            } else {
                if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA && !empty($ordinaryPayment['clearance_no'])) {
                    $taxAmount = '0';
                    $taxRate   = '0';
                } else {
                    $taxAmount = $item['amount_vat'];
                    $taxRate   = $item['vat_rate'];
                }
            }
        }
        $taxType = $this->buildTaxType($taxAmount);

        return [
            'tax_amount' => $taxAmount,
            'tax_rate'   => $taxRate,
            'tax_type'   => $taxType,
        ];
    }

    /**
     * 计算无 SST 时的税
     * @param $amount
     * @return string
     */
    public function calculateTaxWithoutSST($amount): string
    {
        $taxRate  = bcmul(InvoiceEnums::TAX_RATE_FIXED_RATE, 0.01, 2);
        $tax      = bcmul($amount, $taxRate, 4);
        $taxFloat = (float)$tax;
        return number_format($taxFloat, 2, '.', '');
    }

    /**
     * 构建税种
     * @description Tax Amount如果为0固定传06, Tax Amount如果不为0固定传02
     * @param $tax_amount
     * @return string
     */
    protected function buildTaxType($tax_amount): string
    {
        return bccomp($tax_amount, "0", 2) === 0
            ? InvoiceEnums::TAX_TYPE_NOT_APPLICABLE
            : InvoiceEnums::TAX_TYPE_SERVICE;
    }

    /**
     * 计算unit_price、subtotal、total_excluding_tax
     * @param $item
     * @param $ordinaryPayment
     * @param array $vendorGroupList
     * @return array
     */
    private function getTaxDetail($item, $ordinaryPayment, array $vendorGroupList): array
    {
        $default = [
            'unit_price'          => $item['amount_no_tax'],
            'subtotal'            => $item['amount_no_tax'],
            'total_excluding_tax' => $item['amount_no_tax'],
        ];
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            return $default;
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];

            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA) {
                if ($this->isExistSSTTax($item['amount_vat'])) { //无 SST
                    return $default;
                } else { //有 SST
                    return [
                        'unit_price'          => $item['amount_have_tax'],
                        'subtotal'            => $item['amount_have_tax'],
                        'total_excluding_tax' => $item['amount_have_tax'],
                    ];
                }
            } else {
                return $default;
            }
        }
    }

    /**
     * 计算总税额
     * @param $ordinaryPayment
     * @param $vendorGroupList
     * @param $totalTaxAmount
     * @return string
     */
    private function calculateTotalTaxAmount($ordinaryPayment, $vendorGroupList, $totalTaxAmount): string
    {
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            $taxAmount = $ordinaryPayment['amount_total_vat'];
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];

            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA && empty($ordinaryPayment['clearance_no'])) {
                if ($this->isExistSSTTax($ordinaryPayment['amount_total_vat'])) { //无 SST
                    $taxAmount = $totalTaxAmount;
                } else { //有 SST
                    $taxAmount = '0';
                }
            } else {
                if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA && !empty($ordinaryPayment['clearance_no'])) {
                    $taxAmount = '0';
                } else {
                    $taxAmount = $ordinaryPayment['amount_total_vat'];
                }
            }
        }
        return $taxAmount;
    }

    /**
     * 总计（不含税）
     * @param $ordinaryPayment
     * @param array $vendorGroupList
     * @return mixed
     */
    private function getTotalExcludingTax($ordinaryPayment, array $vendorGroupList)
    {
        $default = $ordinaryPayment['amount_total_no_tax'];
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            return $default;
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];

            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA) {
                if ($this->isExistSSTTax($ordinaryPayment['amount_total_vat'])) { //无 SST
                    return $default;
                } else { //有 SST
                    return $ordinaryPayment['amount_total_have_tax'];
                }
            } else {
                return $default;
            }
        }
    }

    /**
     * 是否存在 SST 税
     * @param $sstTaxAmount
     * @return bool
     */
    private function isExistSSTTax($sstTaxAmount): bool
    {
        return bccomp($sstTaxAmount, "0", 2) === 0;
    }

    /**
     * 计算含税、实付金额
     * @param $ordinaryPayment
     * @param array $vendorGroupList
     * @param $defaultAmount
     * @param $totalTaxAmount
     * @return string
     */
    private function calcTotalIncludingTax($ordinaryPayment, array $vendorGroupList, $defaultAmount, $totalTaxAmount): string
    {
        if ($ordinaryPayment['payee_type'] == Enums::PAYEE_TYPE_PERSONAL) {
            return $defaultAmount;
        } else {
            $supplierId     = $ordinaryPayment['supplier_id'];
            $supplierDetail = $vendorGroupList[$supplierId] ?? [];

            if (!empty($supplierDetail) && $supplierDetail['ownership'] != Enums::VENDOR_OWNERSHIP_MALAYSIA) {
                if ($this->isExistSSTTax($ordinaryPayment['amount_total_vat'])) { //无 SST
                    return bcadd($defaultAmount, $totalTaxAmount, 2);
                } else { //有 SST
                    return $defaultAmount;
                }
            } else {
                return $defaultAmount;
            }
        }
    }

    /**
     * 根据 companyId 找 TinCode
     * @param $companyId
     * @return mixed|null
     */
    public function getTinCodeByCompanyId($companyId)
    {
        if (empty($companyId)) {
            return null;
        }
        if (!in_array($companyId, $this->buyer_company_ids)) {
            return null;
        }

        // 默认登录的是 Flash express 公司
        if ($companyId == Enums\ContractEnums::FLASH_EXPRESS_MY_ID) {
            return null;
        }

        return $this->buyer_company_map[$companyId]['TIN'];
    }

    /**
     * @param int $company_id
     * @param int $pageSize
     * @param $offset
     * @param $datatype
     * @return mixed
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    private function getPurchasePaymentList(int $company_id, int $pageSize, $offset, $datatype): array
    {
        // 计算日期区间
        [$startDate, $endDate] = $this->calcValidateDatePeriod($this->getCurrentDate());
        if (empty($startDate) || empty($endDate)) {
            $this->logger->error("无效的时间范围");
            throw new ValidationException("无效的时间范围");
        }

        // 归属地属于马来的供应商
        $nonMyVendorId = $this->getNonMySupplierIds();
        if (empty($nonMyVendorId)) {
            return [];
        }
        
        $builder = $this->modelsManager->createBuilder();
        
        // 定义基础列
        $baseColumns = [
            'pp.id',
            'pp.ppno',
            'po.purchase_type',             //采购订单的采购类型
            'pp.receipt_amount',            //发票金额总计
            'pp.currency',                  //币种
            'pp.clearance_no',              //清关单号
            'pp.vendor_id',                 //供应商编号
            'pp.cost_company_id',           //费用类型所属公司
            'pp.amount',                    //发票税额总计
            'pp.cur_amount',                //本次付款金额
            'pp.ticket_amount_tax',         //发票税额总计
        ];
        $auditStatusList = $this->getAuditStatusList();

        if ($datatype == InvoiceEnums::PURCHASE_PAYMENT_TXT) {
            // 添加特定列
            $columns = array_merge($baseColumns, [
                'pp.approve_at as business_at', //审批通过时间
                'isd.uuid',                     //uuid
            ]);
            
            $builder->columns($columns);
            $builder->from(['pp' => PurchasePayment::class]);
            $builder->join(PurchaseOrder::class, 'pp.po_id = po.id', 'po');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'pp.ppno = isd.internal_invoice_no AND isd.status in( ' . $auditStatusList . ') AND isd.internal_type = ' . Enums\InvoiceEnums::PURCHASE_PAYMENT . ' AND isd.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL,
                'isd');
            
            // 特定条件
            $builder->where('pp.status = :status:', ['status' => Enums::PURCHASE_APPLY_STATUS_APPROVAL]);
            $builder->andWhere('isd.id IS NULL');
            
            // 第一类条件（货到付款 + 有时间范围）
            $condition1 = '(pp.method = :method1: AND pp.approve_at >= :start_date: AND pp.approve_at <= :end_date:)';
            
            $params = [
                'method1'    => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_1,
                'method2'    => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_2, // 预付方式
                'start_date' => $startDate . ' 00:00:00',
                'end_date'   => $endDate . ' 23:59:59',
            ];
        } else {
            // 添加特定列
            $columns = array_merge($baseColumns, [
                'pp.pay_at as business_at',     // 业务时间
                'isd1.uuid',
            ]);
            
            $builder->columns($columns);
            $builder->from(['pp' => PurchasePayment::class]);
            $builder->join(PurchaseOrder::class, 'pp.po_id = po.id', 'po');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'pp.ppno = isd1.internal_invoice_no AND isd1.status = 2 AND isd1.internal_type = ' . Enums\InvoiceEnums::PURCHASE_PAYMENT. ' AND isd1.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL,
                'isd1');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'pp.ppno = isd2.internal_invoice_no AND isd2.status in( ' . $auditStatusList . ') AND isd2.internal_type = ' . Enums\InvoiceEnums::PURCHASE_PAYMENT. ' AND isd2.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_CREDIT,
                'isd2');
            
            // 特定条件
            $builder->where('pp.status = :status:', ['status' => Enums::PURCHASE_APPLY_STATUS_APPROVAL]);
            $builder->andWhere('pp.pay_status = :pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY]);
            $builder->andWhere('isd1.id IS NOT NULL and isd2.id IS NULL');
            $builder->andWhere('pp.pay_at >= :start_date: AND pp.pay_at <= :end_date:', [
                'start_date' => $startDate . ' 00:00:00',
                'end_date'   => $endDate . ' 23:59:59',
            ]);
            
            // 第一类条件（货到付款）
            $condition1 = '(pp.method = :method1:)';
            
            $params = [
                'method1' => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_1,
                'method2' => Enums\PurchaseEnums::PAYMENT_PAY_METHOD_2, // 预付方式
            ];
        }
        
        // 公共条件
        $builder->andWhere('pp.cost_company_id = :company_id:', ['company_id' => $company_id]);
        $builder->inWhere('pp.vendor_id', $nonMyVendorId); // 供应商归属地 ≠ 马来
        
        // 第二类条件（预付 + 清关编码不为空）- 两种情况都相同
        $condition2 = "(pp.method = :method2: AND pp.clearance_no IS NOT NULL AND pp.clearance_no != '')";
        
        // 组合条件
        $builder->andWhere(sprintf('(%s OR %s)', $condition1, $condition2), $params);
        
        // 排序和分页
        $builder->orderBy('pp.id ASC');
        //数据同步到 MyInvoice 后，数据 insert 到了 Invoice_submission_detail表，通过 sql 已经排除了
        //不再需要偏移量
        $builder->limit($pageSize);
        
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param int $company_id
     * @param int $pageSize
     * @param $offset
     * @param $datatype
     * @return mixed
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    private function getAgentPaymentList(int $company_id, int $pageSize, $offset, $datatype)
    {
        // 无有效的费用类型
        if (empty($this->cost_types_ids)) {
            return [];
        }

        // 计算日期区间
        [$startDate, $endDate] = $this->calcValidateDatePeriod($this->getCurrentDate());
        if (empty($startDate) || empty($endDate)) {
            $this->logger->error("无效的时间范围");
            throw new ValidationException("无效的时间范围");
        }

        $auditStatusList = $this->getAuditStatusList();
        $builder = $this->modelsManager->createBuilder();
        
        // 定义基础列 - 两种情况下列是相同的
        $columns = [
            'ap.id',
            'apd.no',
            'apd.cost_type',                 //费用类型
            'ap.currency',                   //币种
            'apd.bank_account_name',         //收款人姓名
            'apd.payee_id_no',               //收款人证件号码
            'apd.payee_staff_id',            //收款人工号
            'apd.payee_address',             //收款人居住地址
            'apd.payee_mobile',              //收款人联系电话
            'ap.cost_company_id',            //费用所属公司
            'apd.amount_no_tax',             //明细-不含税
            'apd.vat_rate',                  //VAT税率
            'apd.amount_total_vat',          //VAT税额
            'apd.payable_amount',            //应付总额
            'apd.amount_total_actually',     //实付总额
        ];

        $builder->from(['ap' => AgencyPaymentModel::class]);
        $builder->join(AgencyPaymentDetailModel::class, 'apd.agency_payment_id = ap.id', 'apd');

        // 公共条件
        $builder->where('ap.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
        $builder->andWhere('ap.cost_company_id = :company_id:', ['company_id' => $company_id]);
        $builder->inWhere('ap.cost_type', $this->cost_types_ids);
        
        if ($datatype == InvoiceEnums::AGENCY_PAYMENT_TXT) {

            // 添加特定列
            $columns = array_merge($columns, [
                'ap.approved_at as business_at', //审批通过时间
                'isd.uuid',                      //uuid
            ]);

            // TXT模式特定条件
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'apd.no = isd.internal_invoice_no AND isd.status in(' . $auditStatusList . ') AND isd.internal_type = ' . Enums\InvoiceEnums::AGENCY_PAYMENT . ' AND isd.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL, 'isd');
                
            // 过滤掉已经成功的数据
            $builder->andWhere('isd.id IS NULL');
            $builder->andWhere('ap.approved_at >= :start_date:', ['start_date' => $startDate . ' 00:00:00']);
            $builder->andWhere('ap.approved_at <= :end_date:', ['end_date' => $endDate . ' 23:59:59']);
        } else {
            // 添加特定列
            $columns = array_merge($columns, [
                'apd.pay_at as business_at',       //审批通过时间
                'isd1.uuid',                      //uuid
            ]);


            // 非TXT模式特定条件
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'apd.no = isd1.internal_invoice_no AND isd1.status = 2 AND isd1.internal_type = ' . Enums\InvoiceEnums::AGENCY_PAYMENT . ' AND isd1.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL,
                'isd1');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'apd.no = isd2.internal_invoice_no AND isd2.status in( ' . $auditStatusList . ') AND isd2.internal_type = ' . Enums\InvoiceEnums::AGENCY_PAYMENT . ' AND isd2.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_CREDIT,
                'isd2');
                
            $builder->andWhere('apd.pay_status = :pay_status:', ['pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY]);
            
            // 过滤掉已经成功的数据
            $builder->andWhere('isd1.id IS NOT NULL and isd2.id IS NULL');
            $builder->andWhere('apd.pay_at >= :start_date:', ['start_date' => $startDate . ' 00:00:00']);
            $builder->andWhere('apd.pay_at <= :end_date:', ['end_date' => $endDate . ' 23:59:59']);
        }
        
        // 排序和分页
        $builder->columns($columns);
        //数据同步到 MyInvoice 后，数据 insert 到了 Invoice_submission_detail表，通过 sql 已经排除了
        //不再需要偏移量
        $builder->limit($pageSize);
        $builder->orderBy('apd.id ASC');
        
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @return string
     */
    private function getAuditStatusList(): string
    {
        $auditStatus     = [
            Enums\InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING,
            Enums\InvoiceEnums::INVOICE_SUBMISSION_STATUS_VALID,
            Enums\InvoiceEnums::INVOICE_SUBMISSION_HOLD,
        ];
        return implode(',', $auditStatus);
    }

    /**
     * @param int $company_id
     * @param int $pageSize
     * @param $offset
     * @param $datatype
     * @return array
     * @throws ValidationException
     * @throws \DateMalformedStringException
     */
    private function getOrdinaryPaymentList(int $company_id, int $pageSize, $offset, $datatype): array
    {
        // 计算日期区间
        [$startDate, $endDate] = $this->calcValidateDatePeriod($this->getCurrentDate());
        if (empty($startDate) || empty($endDate)) {
            $this->logger->error("无效的时间范围");
            throw new ValidationException("无效的时间范围");
        }

        $auditStatusList = $this->getAuditStatusList();
        $builder = $this->modelsManager->createBuilder();
        
        // 定义基础列 - 两种情况下大部分列是相同的
        $baseColumns = [
            'op.id',
            'op.apply_no',              //审批编号
            'op.currency',              //币种
            'op.payee_type',            //收款人类型
            'op.amount_total_vat',      //sst金额总计
            'op.amount_total_have_tax', //含税金额总计
            'op.amount_total_no_tax',   //不含税金额总计
            'op.amount_total_no_tax',   //不含税金额总计
            'op.amount_total_actually', //实付金额总计
            'op.cost_company_id',       //费用所属公司
            'ope.clearance_no',         //清关单号
            'ope.supplier_id',          //供应商 ID
        ];
        
        // 根据数据类型添加特定列
        if ($datatype == InvoiceEnums::ORDINARY_PAYMENT_TXT) {
            $columns = array_merge($baseColumns, [
                'op.approved_at as business_at', //业务时间
                'isd.uuid',
            ]);
            
            $builder->columns($columns);
            $builder->from(['op' => OrdinaryPaymentModel::class]);
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'ope.ordinary_payment_id = op.id', 'ope');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'op.apply_no = isd.internal_invoice_no AND isd.status in( ' . $auditStatusList . ') AND isd.internal_type = ' . Enums\InvoiceEnums::ORDINARY_PAYMENT . ' AND isd.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL,
                'isd');
            
            // 特定条件
            $builder->where('op.approval_status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
            $builder->andWhere('isd.id IS NULL');
            $builder->andWhere('op.approved_at >= :start_date:', ['start_date' => $startDate . ' 00:00:00']);
            $builder->andWhere('op.approved_at <= :end_date:', ['end_date' => $endDate . ' 23:59:59']);
        } else {
            $columns = array_merge($baseColumns, [
                'ope.pay_at as business_at', //业务时间
                'isd1.uuid',
            ]);
            
            $builder->columns($columns);
            $builder->from(['op' => OrdinaryPaymentModel::class]);
            $builder->leftjoin(OrdinaryPaymentExtend::class, 'ope.ordinary_payment_id = op.id', 'ope');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'op.apply_no = isd1.internal_invoice_no AND isd1.status = 2 AND isd1.internal_type = ' . Enums\InvoiceEnums::ORDINARY_PAYMENT . ' AND isd1.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_NORMAL,
                'isd1');
            $builder->leftJoin(InvoiceSubmissionsDetailModel::class,
                'op.apply_no = isd2.internal_invoice_no AND isd2.status in( ' . $auditStatusList . ') AND isd2.internal_type = ' . Enums\InvoiceEnums::ORDINARY_PAYMENT . ' AND isd2.invoice_type = ' . InvoiceEnums::INVOICE_TYPE_CREDIT,
                'isd2');
            
            // 特定条件
            $builder->where('op.approval_status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
            $builder->andWhere('op.pay_status = :pay_status:', ['pay_status' => Enums::LOAN_PAY_STATUS_NOTPAY]);
            $builder->andWhere('isd1.id IS NOT NULL and isd2.id IS NULL');
            $builder->andWhere('ope.pay_at >= :start_date:', ['start_date' => $startDate . ' 00:00:00']);
            $builder->andWhere('ope.pay_at <= :end_date:', ['end_date' => $endDate . ' 23:59:59']);
        }
        
        // 公共条件
        $builder->andWhere('op.cost_company_id = :company_id:', ['company_id' => $company_id]);
        
        // 构建条件数组，用于动态组合OR条件 - 两种情况下相同
        $conditions = [];
        $parameters = [
            'payee_type' => Enums::PAYEE_TYPE_PERSONAL,
        ];
        
        // 供应商归属地非马来 & 供应商属于配置(加ENV)中的供应商 & 供应商的公司性质为自然人
        $vendorIds = $this->getVendorIds();
        
        // 第一类条件（供应商归属地非马来）
        $condition1 = '(ope.supplier_id in ({supplier_ids:array}))';
        if (!empty($vendorIds)) {
            $conditions[]               = $condition1;
            $parameters['supplier_ids'] = $vendorIds;
        }
        
        // 第二类条件（付款对象为个人）
        $condition2   = '(op.payee_type = :payee_type:)';
        $conditions[] = $condition2;
        
        // 如果conditions数组中只有一个条件，不需要用括号包裹
        // 如果有多个条件，则需要用OR连接并用括号包裹
        if (count($conditions) > 1) {
            $finalCondition = '(' . implode(' OR ', $conditions) . ')';
        } else {
            $finalCondition = $conditions[0];
        }
        
        if (!empty($conditions)) {
            $builder->andWhere($finalCondition, $parameters);
        }
        
        // 排序和分页
        $builder->orderBy('op.id ASC');
        //$builder->limit($pageSize, $offset);
        //数据同步到 MyInvoice 后，数据 insert 到了 Invoice_submission_detail表，通过 sql 已经排除了
        //不再需要偏移量
        $builder->limit($pageSize);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * @param $internalNo
     * @return array
     */
    public function getInvoiceUuidByInternalNo($internalNo): array
    {
        $responseData = [
            'internal_no' => $internalNo,
            'uuid'        => '',
            'status'      => 2,
        ];

        if (empty($internalNo)) {
            $message = 'The order number does not exist, please check';
            return ['code' => ErrCode::$SUCCESS, 'data' => $responseData, 'msg' => $message];
        }
        $lastRecord = InvoiceSubmissionsDetailModel::findFirst([
            'conditions' => 'internal_invoice_no = :internal_invoice_no: and status = :status:',
            'bind'       => [
                'internal_invoice_no' => $internalNo,
                'status' => Enums\InvoiceEnums::INVOICE_SUBMISSION_STATUS_VALID,
            ],
            'order'     => 'id DESC',
        ]);
        if (empty($lastRecord)) {
            $lastRecord = InvoiceSubmissionsDetailModel::findFirst([
                'conditions' => 'internal_invoice_no = :internal_invoice_no:',
                'bind'       => [
                    'internal_invoice_no' => $internalNo,
                ],
                'order'     => 'id DESC',
            ]);
            if (!empty($lastRecord)) {
                $message = 'The order number was not successfully transmitted to the tax system and there is no UUID';
            } else {
                $message = 'The order number does not exist, please check';
            }
            return ['code' => ErrCode::$SUCCESS, 'data' => $responseData, 'msg' => $message];
        }
        $responseData = [
            'internal_no' => $internalNo,
            'uuid'        => $lastRecord->uuid,
            'status'      => 1,
        ];
        return ['code' => ErrCode::$SUCCESS, 'data' => $responseData, 'msg' => ''];
    }

    /**
     * 获取E-invoice推送结果接收邮箱配置
     * 
     * @return array 邮箱地址，多个以英文逗号分隔
     */
    public function getEInvoiceEmailAddresses(): array
    {
        return EnumsService::getInstance()->getSettingEnvValueIds('electronic_invoice_notification_emails');
    }

    public function setCurrentDate($date)
    {
        $this->current_date = $date;
    }

    private function getCurrentDate()
    {
        return $this->current_date;
    }

    /**
     * 统一处理地址长度
     * @param $address
     * @return mixed|string
     */
    protected function formatAddress($address)
    {
        // 检查字符串长度（按字符计算，支持中文）
        if (mb_strlen($address, 'UTF-8') > 150) {
            // 如果超过150个字符，截取前150个
            $address = mb_substr($address, 0, 150, 'UTF-8');
        }
        return $address;
    }
}