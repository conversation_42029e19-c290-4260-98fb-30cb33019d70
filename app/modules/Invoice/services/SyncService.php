<?php

namespace App\Modules\Invoice\Services;

use App\Library\Enums\InvoiceEnums;
use app\modules\Invoice\services\exceptions\OAuth\AuthenticationException;
use app\modules\Invoice\services\exceptions\SyncException;
use app\modules\Invoice\services\validation\AbstractVoucher;
use GuzzleHttp\Exception\GuzzleException;

class SyncService extends BaseService
{
    /**
     * @var InvoiceXmlTransformer 统一的XML转换器实例
     */
    private $xmlTransformer;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 初始化统一的XML转换器
        $this->xmlTransformer = new InvoiceXmlTransformer();
    }
    
    /**
     * 同步单据到政府系统
     * 
     * @param AbstractVoucher $voucher 单据对象
     * @param string|null $onBehalfOf 代理身份ID，用于代理模式
     * @return bool 同步是否成功
     * @throws SyncException 同步异常
     * @throws AuthenticationException|GuzzleException 认证异常
     */
    public function submit(AbstractVoucher $voucher, string $onBehalfOf = null)
    {
        try {
            // 1. 转换单据为XML (使用统一的转换器)
            $xmlData = $this->xmlTransformer->transformDocument($voucher, $voucher->getInvoiceTypeCode());
            
            // 2. 获取Client服务
            $clientService = $this->di->get('invoice.oauth.OAuthService');
            
            // 3. 获取Client
            $client = $clientService->getClient($onBehalfOf);
            
            // 4. 发送数据到政府系统
            $response = $client->documentSubmission->submitDocument($xmlData);

            if (empty($response)) {
                $this->logger->error('sync response is empty: ' . json_encode($xmlData));
            }
            
        } catch (AuthenticationException $e) {
            $this->error("Authentication failed: {$e->getMessage()}");
            throw $e;
        } catch (SyncException $e) {
            $this->error("Sync failed for voucher {$e->getVoucherId()}: {$e->getMessage()}");
            throw $e;
        } catch (\Exception $e) {
            $this->error("Unexpected error during sync: {$e->getMessage()}");
            throw new SyncException(
                "Unexpected error during sync: {$e->getMessage()}",
                $voucher->getIdentifier(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * 批量同步多个单据
     *
     * @param AbstractVoucher[] $vouchers 单据数组
     * @param string|null $onBehalfOf 代理身份ID，用于代理模式
     * @return array 同步结果数组
     * @throws GuzzleException
     */
    public function batchSubmit(array $vouchers, string $onBehalfOf = null): array
    {
        $results   = [];
        $documents = [];

        // 1. 处理每个单据
        foreach ($vouchers as $voucher) {
            if (!($voucher instanceof AbstractVoucher)) {
                $results[] = [
                    'voucher_id' => 'unknown',
                    'status'     => 'error',
                    'message'    => 'Invalid voucher object',
                ];
                continue;
            }

            $voucherId = $voucher->getIdentifier();
            try {
                // 1) 转换单据为XML (使用统一的转换器)
                $xmlData = $this->xmlTransformer->transformDocument($voucher, $voucher->getInvoiceTypeCode());

                // 2) 计算文档哈希
                $documentHash = hash('sha256', $xmlData);

                // 3) 将文档添加到documents数组
                $documents[] = [
                    'format'       => 'XML',
                    'documentHash' => $documentHash,
                    'codeNumber'   => $voucherId,
                    'document'     => base64_encode($xmlData),
                ];

                // 4) 记录单个凭证的准备状态
                $results[] = [
                    'voucher_id' => $voucherId,
                    'status'     => 'prepared',
                    'message'    => 'Voucher prepared for batch submission',
                ];
            } catch (\Exception $e) {
                $this->error("Failed to transform voucher {$voucherId}: {$e->getMessage()}");
                $results[] = [
                    'voucher_id' => $voucherId,
                    'status'     => 'error',
                    'message'    => 'Failed to transform voucher: ' . $e->getMessage(),
                ];
            }
        }

        // 2. 发送批量请求
        if (!empty($documents)) {
            try {
                // 1) 获取Client服务
                $clientService = $this->di->get('invoice.oauth.OAuthService');

                // 2) 获取Client
                $client = $clientService->getClient($onBehalfOf);

                // 3) 发送数据到政府系统
                $response = $client->documentSubmission->submitDocument($documents);
                if (empty($response)) {
                    $this->logger->error('sync response is empty: ' . json_encode($documents));
                }

                // 4) 记录请求
                $this->manageResponse($documents, $response, $vouchers);

                // 更新处理结果 (需要解析 $response 来更新 $results 中每个凭证的状态)
                return [
                    'status'   => 'success',              // 或者根据 $response 判断
                    'message'  => 'Batch sync submitted', // 或者更详细的信息
                    'results'  => $results,               // 这里可以根据 $response 更新每个凭证的状态
                    'response' => $response,              // 返回原始响应
                ];
            } catch (\Exception $e) {
                // 处理其他类型的异常
                $this->logger->error("Failed to submit batch: {$e->getMessage()}");
                return [
                    'status'  => 'error',
                    'message' => 'Failed to submit batch: ' . $e->getMessage(),
                    'results' => $results, // 保持准备状态或标记为错误
                ];
            }
        }

        // 如果没有可处理的文档
        return [
            'status'  => 'success',
            'message' => 'No valid documents prepared for sync',
            'results' => $results,
        ];
    }

    /**
     * 处理应答数据
     * @param $documents
     * @param $response
     * @param $vouchers
     * @return void
     */
    private function manageResponse($documents, $response, $vouchers): void
    {
        $service           = $this->di->get('invoice.oauth.OAuthService');
        $client            = $service->getClient();
        $submissionUid     = $response['submissionUid'] ?? '';
        $acceptedDocuments = $response['acceptedDocuments'] ?? [];
        $rejectedDocuments = $response['rejectedDocuments'] ?? [];
        $originVouchers    = [];

        // 计算文档数量
        $totalDocuments      = count($documents);
        $successfulDocuments = count($acceptedDocuments);
        $failedDocuments     = count($rejectedDocuments);

        // 保存提交记录到 invoice_submissions 表
        $submissionData = [
            'submission_uid'       => $submissionUid,
            'submissions'          => json_encode($documents),
            'response'             => !empty($response) ? json_encode($response) : null,
            'token'                => $client->identity->getAccessToken(),
            'total_documents'      => $totalDocuments,
            'successful_documents' => $successfulDocuments,
            'failed_documents'     => $failedDocuments,
        ];

        // 处理响应并记录到数据库1
        if (!isset($response['submissionUid'])) {
            $this->logger->error('submissionUid not exists:' . json_encode($submissionData));
        } else {
            $this->logger->info('manageResponse-submissionData:' . json_encode($submissionData));
        }

        foreach ($vouchers as $voucher) {
            $originVouchers[$voucher->getIdentifier()] = $voucher;
        }

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $db->insertAsDict('invoice_submissions', $submissionData);
            $submissionId = $db->lastInsertId();
            if (!$submissionId) {
                throw new \Exception('Failed to insert submission data:' . json_encode($submissionData));
            }

            $relateSubmissionData = [
                'submission_uid' => $submissionUid,
                'submission_id'  => $submissionId,
            ];

            // 2. 处理详情数据
            $detailData = $this->prepareDetailsData($response, $originVouchers, $relateSubmissionData);
            if (!empty($detailData)) {
                foreach ($detailData as $detail) {
                    $db->insertAsDict('invoice_submissions_detail', $detail);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->error("Failed to insert invoice_submissions record: {$e->getMessage()}");
        }
    }

    /**
     * 准备详情数据
     * @param $response
     * @param $vouchers
     * @param $params
     * @return array
     */
    private function prepareDetailsData($response, $vouchers, $params): array
    {
        $acceptedDocuments = $response['acceptedDocuments'] ?? [];
        $rejectedDocuments = $response['rejectedDocuments'] ?? [];
        $submissionUid     = $params['submission_uid'] ?? '';
        $submissionId      = $params['submission_id'] ?? '';
        $detailData        = [];

        // 保存提交明细到 invoice_submissions_detail 表
        if (!empty($response)) {
            foreach ($acceptedDocuments as $acceptedDoc) {
                if (isset($acceptedDoc['uuid']) && isset($acceptedDoc['invoiceCodeNumber'])) {
                    $uuid              = $acceptedDoc['uuid'];
                    $invoiceCodeNumber = $acceptedDoc['invoiceCodeNumber'];

                    // 查找对应的凭证对象以获取更多信息
                    $voucherInfo = $vouchers[$invoiceCodeNumber] ?? null;
                    if (empty($voucherInfo)) {
                        continue;
                    }

                    $detailData[] = [
                        'submission_uid'      => $submissionUid,
                        'submission_id'       => $submissionId,
                        'uuid'                => $uuid,
                        'internal_invoice_no' => $voucherInfo->getOriginInvoiceNumber(),
                        'invoice_no'          => $invoiceCodeNumber,
                        'internal_type'       => $voucherInfo->getType(),
                        'invoice_type'        => $voucherInfo->getInvoiceType(),
                        'company_id'          => $voucherInfo->getCompanyId(),
                        'status'              => InvoiceEnums::INVOICE_SUBMISSION_STATUS_PENDING,
                        'business_date'       => $voucherInfo->getBusinessDate(),
                        'error_info'          => '',
                        'error_msg'           => '',
                    ];
                }
            }

            // 处理被拒绝的文档
            foreach ($rejectedDocuments as $rejectedDoc) {
                if (isset($rejectedDoc['invoiceCodeNumber'])) {
                    $invoiceCodeNumber = $rejectedDoc['invoiceCodeNumber'];

                    // 查找对应的凭证对象以获取更多信息
                    $voucherInfo = $vouchers[$invoiceCodeNumber] ?? null;
                    if (empty($voucherInfo)) {
                        continue;
                    }

                    // 无提交submissionUid,提交状态为 Hold ， 待修改完数据后，
                    // 手动置为 Invalid 后，再次同步数据
                    $status = !empty($submissionUid)
                        ? InvoiceEnums::INVOICE_SUBMISSION_STATUS_INVALID
                        : InvoiceEnums::INVOICE_SUBMISSION_HOLD;

                    if (isset($rejectedDoc['error'])) {
                        $errorInfo = $rejectedDoc['error']
                            ? json_encode($rejectedDoc['error'])
                            : '';
                        $errorMsgList = $rejectedDoc['error']['details'] ?? [];
                        $errorMsg = array_column($errorMsgList, 'message');
                        $errorMsg = !empty($errorMsg) ? join(', ', $errorMsg) : '';
                    }

                    $detailData[] = [
                        'submission_uid'      => $submissionUid,
                        'submission_id'       => $submissionId,
                        'uuid'                => $rejectedDoc['uuid'] ?? '',
                        'internal_invoice_no' => $voucherInfo->getOriginInvoiceNumber(),
                        'invoice_no'          => $invoiceCodeNumber,
                        'internal_type'       => $voucherInfo->getType(),
                        'invoice_type'        => $voucherInfo->getInvoiceType(),
                        'company_id'          => $voucherInfo->getCompanyId(),
                        'status'              => $status,
                        'business_date'       => $voucherInfo->getBusinessDate(),
                        'error_info'          => $errorInfo ?? '',
                        'error_msg'           => $errorMsg ?? '',
                    ];
                }
            }
        } else {
            foreach ($vouchers as $voucher) {
                $detailData[] = [
                    'submission_uid'      => '',
                    'submission_id'       => $submissionId,
                    'uuid'                => '',
                    'internal_invoice_no' => $voucher->getOriginInvoiceNumber(),
                    'invoice_no'          => $voucher->getInvoiceNumber(),
                    'internal_type'       => $voucher->getType(),
                    'invoice_type'        => $voucher->getInvoiceType(),
                    'company_id'          => $voucher->getCompanyId(),
                    'status'              => InvoiceEnums::INVOICE_SUBMISSION_HOLD,
                    'business_date'       => $voucher->getBusinessDate(),
                    'error_info'          => '',
                    'error_msg'           => 'no response',
                ];
            }
        }
        return $detailData;
    }

    public function test()
    {
        // 1) 获取Client服务
        $clientService = $this->di->get('invoice.oauth.OAuthService');

        // 2) 获取Client
        $client = $clientService->getClient();

        $response = $client->documentType->getAllDocumentTypes();
        $this->logger->info('test response: ' . json_encode($response));
        return $response;
    }
}