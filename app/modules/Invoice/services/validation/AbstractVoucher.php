<?php

namespace app\modules\Invoice\services\validation;

use App\Library\Enums\InvoiceEnums;
use App\Modules\Invoice\Services\BaseService;

abstract class AbstractVoucher extends BaseService
{
    /**
     * 单据数据
     * @var array
     */
    protected $data;
    
    /**
     * 单据类型
     * @var string
     */
    protected $type;

    /**
     * 发票类型
     * @var string
     */
    protected $invoiceType;

    /**
     * 公司 ID
     * @var int
     */
    protected $companyId;

    /**
     * 验证错误详情
     * @var array
     */
    protected $validationErrors = [];

    /**
     * @var string
     */
    protected $type_code;

    protected $is_credit;

    /**
     * 构造函数
     * @param array $data 单据数据
     * @param int $companyId
     * @param string $datatype
     */
    public function __construct(array $data, int $companyId, string $datatype)
    {
        $this->setData($data);
        $this->setCompanyId($companyId);
        $this->setType(InvoiceEnums::getModuleTypeMap($datatype));
        $this->setInvoiceType(InvoiceEnums::getInvoiceTypeMap($datatype));
        $this->setIsCredit(strpos($this->type, 'credit'));
        if (!$this->validate()) {
            $this->logger->error($this->validationErrors);
            throw new \InvalidArgumentException('Invalid voucher data:' . json_encode($this->validationErrors));
        }
    }
    
    /**
     * 获取单据数据
     * @return array
     */
    public function getData(): array
    {
        return $this->data;
    }

    public function setData(array $data): void
    {
        $this->data = $data;
    }

    /**
     * 获取单据类型
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @param int|string $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    public function getInvoiceType(): string
    {
        return $this->invoiceType;
    }

    public function setInvoiceType(string $invoiceType): void
    {
        $this->invoiceType = $invoiceType;
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }

    public function setCompanyId(int $companyId): void
    {
        $this->companyId = $companyId;
    }

    /**
     * 是否为贷记发票
     * @return bool
     */
    public function getIsCredit(): bool
    {
        return $this->is_credit;
    }

    /**
     * @param $isCredit
     * @return void
     */
    public function setIsCredit($isCredit): void
    {
        $this->is_credit = $isCredit;
    }

    /**
     * 验证单据数据
     * @return bool
     */
    protected function validate(): bool
    {
        // 重置变量
        $this->validationErrors = [];

        // 通用字段
        $this->validateCommonField();

        // 验证日期和时间格式
        $this->validateDateTimeFormats();

        // 验证供应商信息
        $this->validateSupplierField();

        // 验证买家信息
        $this->validateBuyerField();

        // 验证地址信息
        $this->validateAddressField();

        // 验证明细字段
        $this->validateItemField();

        // 验证税务字段
        $this->validateTaxField();

        // 子类特定验证
        $this->validateSpecific();

        return empty($this->validationErrors);
    }
    
    /**
     * 获取电子发票共通必填字段
     * @return array
     */
    protected function getCommonRequiredFields(): array
    {
        return [
            'supplier',            // 供应商（结构体）
            'buyer',               // 买方（结构体）
            'items',               // 明细 （结构体）
            'taxes',               // 税额相关数据
            'e_invoice_version',   // 电子发票版本
            'e_invoice_type_code', // 版本
            'e_invoice_number',    // 唯一编号
            'currency_code',       // 币种
        ];
    }

    /**
     * 获取供应商必填字段
     * @return array
     */
    protected function getSupplierFields(): array
    {
        return [
            'name',
            'party_details',
            'msic_code',
            'description',
            'phone',
            'address',
        ];
    }

    /**
     * 获取买家必填字段
     * @return array
     */
    protected function getBuyerFields(): array
    {
        return [
            'name',
            'party_details',
            'phone',
            'address',
        ];
    }

    /**
     * 获取发票明细必填字段
     * @return array
     */
    protected function getItemFields(): array
    {
        return [
            'classification',
            'description',
            'subtotal',
            'total_excluding_tax',
            'quantity',
            'measurement',
            'unit_price',
            'tax_type',
            'tax_rate',
            'tax_amount',
        ];
    }

    /**
     * 获取地址必填字段
     * @return array
     */
    protected function getAddressFields(): array
    {
        return [
            'line0',
            'city',
            'state',
            'country',
        ];
    }

    /**
     * 获取税务必填字段
     * @return array
     */
    protected function getTaxFields(): array
    {
        return [
            'total_excluding_tax',
            'tax_type',
            'total_including_tax',
            'total_payable_amount',
            'total_tax_amount',
            'total_tax_amount_per_tax_type',
        ];
    }

    /**
     * 获取供应商信息
     * @return array
     */
    public function getSupplier(): array
    {
        return $this->data['supplier'] ?? [];
    }
    
    /**
     * 获取买方信息
     * @return array
     */
    public function getBuyer(): array
    {
        return $this->data['buyer'] ?? [];
    }
    
    /**
     * 获取电子发票版本
     * @return string
     */
    public function getInvoiceVersion(): string
    {
        return $this->data['e_invoice_version'];
    }
    
    /**
     * 获取电子发票类型代码
     * @return string
     */
    public function getInvoiceTypeCode(): string
    {
        return $this->data['e_invoice_type_code'];
    }
    
    /**
     * 获取电子发票号码
     * @return string
     */
    public function getInvoiceNumber(): string
    {
        return $this->data['e_invoice_number'];
    }

    /**
     * 获取电子发票号码
     * @return string
     */
    public function getOriginInvoiceNumber(): string
    {
        if (strpos($this->data['e_invoice_number'], 'CN') !== false) {
            return substr($this->data['e_invoice_number'], 2);
        } else {
            return $this->data['e_invoice_number'];
        }
    }

    /**
     * 获取电子发票号码
     * @return string
     */
    public function getInvoiceUuid(): string
    {
        return $this->data['original_e_invoice_reference_number'];
    }
    
    /**
     * 获取发票货币代码
     * @return string
     */
    public function getCurrencyCode(): string
    {
        return $this->data['currency_code'];
    }
    
    /**
     * 获取发票明细项目
     * @return array
     */
    public function getItems(): array
    {
        return $this->data['items'] ?? [];
    }
    
    /**
     * 获取税种信息
     * @return array
     */
    public function getTaxes(): array
    {
        return $this->data['taxes'] ?? [];
    }

    public function getBusinessDate()
    {
        return $this->data['business_date'];
    }

    public function getClearanceNo()
    {
        return $this->data['clearance_no'] ?? '';
    }

    /**
     * 子类特定验证
     * @return bool
     */
    abstract protected function validateSpecific(): bool;
    
    /**
     * 获取单据的唯一标识
     * @return string
     */
    public function getIdentifier(): string
    {
        $prefix = $this->getIsCredit() ? 'CN' : '';
        return $prefix . $this->data['e_invoice_number'];
    }

    /**
     * 验证通用字段
     * @return bool
     */
    protected function validateCommonField(): bool
    {
        $valid = true;
        foreach ($this->getCommonRequiredFields() as $field) {
            if (empty($this->data[$field] ?? null)) {
                $this->validationErrors[] = "commonField缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 验证日期和时间格式
     * @return bool
     */
    protected function validateDateTimeFormats(): bool
    {
        return true;
    }

    /**
     * 验证供应商信息
     * @return bool
     */
    protected function validateSupplierField(): bool
    {
        $valid = true;
        foreach ($this->getSupplierFields() as $field) {
            if (empty($this->data['supplier'][$field] ?? null)) {
                $this->validationErrors[] = "supplier缺少必填字段: $field";
                $valid = false;
            }

            // 限定手机号长度
            if ($field == 'phone' && $this->checkPhoneNoLength($this->data['supplier'][$field]))  {
                $this->validationErrors[] = "supplier缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 验证买家信息
     * @return bool
     */
    protected function validateBuyerField(): bool
    {
        $valid = true;
        foreach ($this->getBuyerFields() as $field) {
            if (is_null($this->data['buyer'][$field] ?? null)) {
                $this->validationErrors[] = "buyer缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 验证地址信息
     * @return bool
     */
    protected function validateAddressField(): bool
    {
        $valid = true;
        foreach ($this->getAddressFields() as $field) {
            if (empty($this->data['supplier']['address'][$field] ?? null)) {
                $this->validationErrors[] = "supplier-address缺少必填字段: $field";
                $valid = false;
            }

            if (empty($this->data['buyer']['address'][$field] ?? null)) {
                $this->validationErrors[] = "buyer-address缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 验证明细字段
     * @return bool
     */
    protected function validateItemField(): bool
    {
        if (empty($this->data['items'] ?? null) || !is_array($this->data['items'])) {
            $this->validationErrors[] = "缺少必填字段: items";
            return false;
        }

        $valid = true;
        foreach ($this->data['items'] as $item) {
            foreach ($this->getItemFields() as $index => $field) {
                if (is_null($item[$field] ?? null)) {
                    $this->validationErrors[] = sprintf("items缺少必填字段: index {%d}, %s", $index, $field);
                    $valid = false;
                }
            }
        }
        return $valid;
    }

    /**
     * 验证税务字段
     * @return bool
     */
    protected function validateTaxField(): bool
    {
        $valid = true;
        foreach ($this->getTaxFields() as $field) {
            if (is_null($this->data['taxes'][$field] ?? null)) {
                $this->validationErrors[] = "taxes缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 校验电话号码长度
     * 有效长度为 8 ～ 20 位
     *
     * @param $phone
     * @return bool
     */
    private function checkPhoneNoLength($phone): bool
    {
        return strlen($phone) < 8 || strlen($phone) > 20;
    }
} 