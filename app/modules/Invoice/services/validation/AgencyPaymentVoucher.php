<?php

namespace app\modules\Invoice\services\validation;

class AgencyPaymentVoucher extends AbstractVoucher
{
    /**
     * 子类特定验证
     * @return bool
     */
    protected function validateSpecific(): bool
    {
        // 校验供应商party Detail
        $this->validateSupplierPartyDetails();

        // 校验买家party Detail
        $this->validateBuyerPartyDetails();

        return true;
    }

    /**
     * 校验详情
     * @return bool
     */
    protected function validateSupplierPartyDetails(): bool
    {
        $valid = true;
        foreach ($this->getSupplierPartyDetailsFields() as $field) {
            if (empty($this->data['supplier']['party_details'][$field] ?? null)) {
                $this->validationErrors[] = "supplier 缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 校验详情
     * @return bool
     */
    protected function validateBuyerPartyDetails(): bool
    {
        $valid = true;
        foreach ($this->getBuyerPartyDetailsFields() as $field) {
            if (empty($this->data['buyer']['party_details'][$field] ?? null)) {
                $this->validationErrors[] = "buyer 缺少必填字段: $field";
                $valid = false;
            }
        }
        return $valid;
    }

    /**
     * 获取详情必填字段
     * @return array
     */
    protected function getSupplierPartyDetailsFields(): array
    {
        return [
            'TIN',
            'NRIC',
        ];
    }

    /**
     * 获取详情必填字段
     * @return array
     */
    protected function getBuyerPartyDetailsFields(): array
    {
        return [
            'TIN',
            'BRN',
        ];
    }
} 