<?php

namespace app\modules\Invoice\services;

use App\Library\Enums\InvoiceEnums;
use app\modules\Invoice\services\ubl\Constant\CountryCodes;
use app\modules\Invoice\services\ubl\Constant\InvoiceTypeCodes;
use app\modules\Invoice\services\validation\AbstractVoucher;
use app\modules\Invoice\services\validation\AgencyPaymentVoucher;


/**
 * 代理支付凭证适配器
 */
class AgencyPaymentVoucherAdapterService extends AbstractVoucherAdapterService
{
    /**
     * 创建代理支付凭证
     * @param int $companyId 公司 ID
     * @param array $data 代理支付数据
     * @param string $datatype
     * @return AbstractVoucher
     */
    public function createVoucher(int $companyId, array $data,  string $datatype): AbstractVoucher
    {
        // 转换为凭证数据结构
        $voucherData = $this->transformData($data, $datatype);

        // 创建并返回凭证对象
        return new AgencyPaymentVoucher($voucherData, $companyId, $datatype);
    }

    /**
     * 转换代理支付数据为凭证数据
     * @param array $data 代理支付数据
     * @param string $datatype
     * @return array 凭证数据
     */
    protected function transformData(array $data, string $datatype): array
    {
        // 构建电子发票基本数据
        $invoiceData = [
            'e_invoice_version'   => '1.1', //固定数值
            'e_invoice_number'    => $data['no'] ?? '',
            'currency_code'       => $data['currency'],
            'business_date'       => $data['business_date'],
            'clearance_no'        => $data['clearance_no'] ?? '',
        ];

        // 如果是贷记发票，添加相关字段
        if ($datatype === InvoiceEnums::CREDIT_AGENCY_PAYMENT_TXT) {
            $invoiceData['e_invoice_number']                    = sprintf('CN%s', $data['no']);
            $invoiceData['original_e_invoice_reference_number'] = $data['uuid'] ?? '';
            $invoiceData['e_invoice_type_code']                 = InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE;
        } else {
            $invoiceData['e_invoice_type_code'] = InvoiceTypeCodes::SELF_BILLED_INVOICE;
        }

        // 添加供应商信息 - 代理支付有特殊的供应商处理
        $invoiceData['supplier'] = $this->buildAgencySupplierData($data);

        // 添加买方信息
        $invoiceData['buyer'] = $this->buildBuyerData($data);

        // 添加明细项目
        $invoiceData['items'] = $this->buildItemsData($data);

        // 添加税相关字段
        $invoiceData['taxes'] = $this->buildTaxData($data, $invoiceData['items']);

        return $invoiceData;
    }

    /**
     * 构建代理支付供应商信息
     * @param array $baseData 基础数据
     * @return array 供应商数据
     */
    protected function buildAgencySupplierData(array $baseData): array
    {
        return [
            //供应商名
            'name'        => $baseData['supplier']['vendor_name'] ?? '',
            //供应商的马来西亚标准行业分类 (MSIC) 代码（00000不适用）
            'msic_code'   => '00000',
            //party Element
            'party_details' => [
                //供应商TIN
                'TIN' => InvoiceEnums::INVOICE_SUPPLIER_TIN_DOMESTIC,
                //供应商的注册/身份证号码/护照号码
                'NRIC' => $baseData['supplier']['identification_no'] ?? '',
            ],
            //供应商业务活动描述
            'description' => 'NOT APPLICABLE',
            //供应商联系电话
            'phone'       => $baseData['supplier']['contact_phone'] ?? '',
            //供应商地址
            'address'     => [
                //地址行0
                'line0'   => $baseData['supplier']['address'] ?? '',
                //城市名称
                'city'    => 'NA',
                //州/省
                'state'   => InvoiceEnums::STATE_CODE_NOT_APPLICABLE,
                //国家
                'country' => CountryCodes::MALAYSIA,
            ],
        ];
    }

    /**
     * 构建明细行
     * @param $item
     * @param $baseData
     * @return array
     */
    protected function buildLineItemDetail($item, $baseData)
    {
        $taxAmount = $item['amount_total_vat'] ?? '';
        $taxType = $this->buildTaxType($taxAmount);

        return [
            //分类
            'classification'      => $item['classification'] ?? '',
            //产品或服务描述
            'description'         => $item['description'] ?? '',
            //小计
            'subtotal'            => $item['amount_no_tax'] ?? '',
            //总计（不含税）
            'total_excluding_tax' => $item['amount_no_tax'] ?? '',
            //数量
            'quantity'            => 1,
            //计量单位
            'measurement'         => InvoiceEnums::MEASUREMENT_TYPE_UNIT,
            //单价
            'unit_price'          => $item['amount_no_tax'] ?? '',
            //税种
            'tax_type'            => $taxType,
            //税率
            'tax_rate'            => $item['vat_rate'] ?? '',
            //税额
            'tax_amount'          => $taxAmount,
        ];
    }

    /**
     * 构建税额相关数据
     * @param array $baseData
     * @param $invoiceItemsData
     * @return array
     */
    protected function buildTaxData(array $baseData, $invoiceItemsData)
    {
        // 总税额
        $totalTaxAmount = $baseData['amount_total_vat'];

        return [
            //总计（不含税）
            'total_excluding_tax'           => $baseData['amount_no_tax'],
            // 总计（含税）
            'total_including_tax'           => $baseData['payable_amount'],
            //应付总额
            'total_payable_amount'          => $baseData['amount_total_actually'],
            // 总税额
            'total_tax_amount'              => $baseData['amount_total_vat'],
            // 每种税种的总税额
            'total_tax_amount_per_tax_type' => $baseData['amount_total_vat'],
            //税种
            'tax_type'                      => $this->buildTaxType($totalTaxAmount),
        ];
    }
}