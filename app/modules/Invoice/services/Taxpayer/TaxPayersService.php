<?php

/**
 * @copyright Copyright (c) 2025 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace App\Modules\Invoice\Services\Taxpayer;

use App\Library\Enums\InvoiceEnums;
use App\Modules\Invoice\Services\AbstractService;
use app\modules\Invoice\services\InvoiceClient;
use Exception;

/**
 * Tax payers service
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.8
 */
class TaxPayersService extends AbstractService
{
    /**
     * TaxPayersService constructor.
     *
     * @param InvoiceClient    $client
     * @param bool              $prodMode
     */
    public function __construct(InvoiceClient $client, $prodMode = false)
    {
        parent::__construct($client,
            $this->initApiUrl(InvoiceEnums::API_TAXPAYERS_PATH, $prodMode));
    }

    /**
     * This API allows taxpayer’s ERP system to retrieve the information for a specific Taxpayer based on 
     * the Base64 formatted string obtained from scanning the respective QR code.
     *
     * @param string $qrCodeText       Decoded Base64 string.
     * 
     * @return array
     */
    public function getTaxPayerFromQrcode($qrCodeText)
    {
        $url = $this->getBaseUrl() . '/qrcodeinfo/' . $qrCodeText;

        $response = $this->getClient()->request('GET', $url);
        return $response;
    }
}
