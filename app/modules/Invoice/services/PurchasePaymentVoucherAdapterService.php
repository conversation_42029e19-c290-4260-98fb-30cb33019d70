<?php

namespace app\modules\Invoice\services;

use App\Library\Enums\InvoiceEnums;
use app\modules\Invoice\services\ubl\Constant\InvoiceTypeCodes;
use app\modules\Invoice\services\validation\AbstractVoucher;
use app\modules\Invoice\services\validation\PurchasePaymentVoucher;

class PurchasePaymentVoucherAdapterService extends AbstractVoucherAdapterService
{
    /**
     * 创建代理支付凭证
     * @param int $companyId 公司 ID
     * @param array $data 代理支付数据
     * @param string $datatype
     * @return AbstractVoucher
     */
    public function createVoucher(int $companyId, array $data,  string $datatype): AbstractVoucher
    {
        // 转换为凭证数据结构
        $voucherData = $this->transformData($data, $datatype);

        // 创建并返回凭证对象
        return new PurchasePaymentVoucher($voucherData, $companyId, $datatype);
    }

    /**
     * 转换代理支付数据为凭证数据
     * @param array $data
     * @param string $datatype
     * @return array 凭证数据
     */
    protected function transformData(array $data, string $datatype): array
    {
        // 构建电子发票基本数据
        $invoiceData = [
            'e_invoice_version'   => '1.1', //固定数值
            'e_invoice_number'    => $data['ppno'] ?? '',
            'currency_code'       => $data['currency'],
            'business_date'       => $data['business_date'],
            'clearance_no'        => $data['clearance_no'] ?? '',
        ];

        // 如果是贷记发票，添加相关字段
        if ($datatype === InvoiceEnums::CREDIT_PURCHASE_PAYMENT_TXT) {
            $invoiceData['e_invoice_number']                    = sprintf('CN%s', $data['ppno']);
            $invoiceData['original_e_invoice_reference_number'] = $data['uuid'] ?? '';
            $invoiceData['e_invoice_type_code']                 = InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE;
        } else {
            $invoiceData['e_invoice_type_code'] = InvoiceTypeCodes::SELF_BILLED_INVOICE;
        }

        // 添加供应商信息 - 代理支付有特殊的供应商处理
        $invoiceData['supplier'] = $this->buildSupplierData($data);

        // 添加买方信息
        $invoiceData['buyer'] = $this->buildBuyerData($data);

        // 添加明细项目
        $invoiceData['items'] = $this->buildItemsData($data);

        // 添加税相关字段
        $invoiceData['taxes'] = $this->buildTaxData($data, $invoiceData['items']);

        return $invoiceData;
    }
}