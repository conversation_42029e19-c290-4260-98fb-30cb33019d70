<?php

namespace App\Modules\Invoice\Services; // 更新命名空间到Services

use App\Library\Exception\BusinessException;
use app\modules\Invoice\services\ubl\AccountingParty;
use app\modules\Invoice\services\ubl\AdditionalDocumentReference;
use app\modules\Invoice\services\ubl\Address;
use app\modules\Invoice\services\ubl\AddressLine;
use app\modules\Invoice\services\ubl\BillingReference;
use app\modules\Invoice\services\ubl\Builder\AbstractDocumentBuilder;
use app\modules\Invoice\services\ubl\Builder\XmlDocumentBuilder;
use app\modules\Invoice\services\ubl\CommodityClassification;
use app\modules\Invoice\services\ubl\Constant\CountryCodes;
use app\modules\Invoice\services\ubl\Constant\InvoiceTypeCodes;
use app\modules\Invoice\services\ubl\Constant\MSICCodes;
use app\modules\Invoice\services\ubl\Contact;
use app\modules\Invoice\services\ubl\Country;
use app\modules\Invoice\services\ubl\CreditNote;
use app\modules\Invoice\services\ubl\DebitNote;
use app\modules\Invoice\services\ubl\Invoice;
use app\modules\Invoice\services\ubl\InvoiceDocumentReference;
use app\modules\Invoice\services\ubl\InvoiceLine;
use app\modules\Invoice\services\ubl\Item;
use app\modules\Invoice\services\ubl\ItemPriceExtension;
use app\modules\Invoice\services\ubl\LegalEntity;
use app\modules\Invoice\services\ubl\LegalMonetaryTotal;
use app\modules\Invoice\services\ubl\Party;
use app\modules\Invoice\services\ubl\PartyIdentification;
use app\modules\Invoice\services\ubl\PrepaidPayment;
use app\modules\Invoice\services\ubl\Price;
use app\modules\Invoice\services\ubl\RefundNote;
use app\modules\Invoice\services\ubl\SelfBilledCreditNote;
use app\modules\Invoice\services\ubl\SelfBilledDebitNote;
use app\modules\Invoice\services\ubl\SelfBilledInvoice;
use app\modules\Invoice\services\ubl\SelfBilledRefundNote;
use app\modules\Invoice\services\ubl\TaxCategory;
use app\modules\Invoice\services\ubl\TaxScheme;
use app\modules\Invoice\services\ubl\TaxSubTotal;
use app\modules\Invoice\services\ubl\TaxTotal;
use app\modules\Invoice\services\validation\AbstractVoucher;


class InvoiceXmlTransformer extends BaseService
{
    /**
     * @var AbstractVoucher 参数
     */
    private $voucher;

    public function getVoucher(): AbstractVoucher
    {
        return $this->voucher;
    }

    public function setVoucher(AbstractVoucher $voucher): void
    {
        $this->voucher = $voucher;
    }

    /**
     * 创建XML文档
     * @param AbstractVoucher $voucher
     * @param string $invoiceTypeCode 发票类型代码, 默认为自开票据
     * @return string
     * @throws \DateMalformedStringException
     * @throws BusinessException
     */
    public function transformDocument(AbstractVoucher $voucher, string $invoiceTypeCode = InvoiceTypeCodes::SELF_BILLED_INVOICE): string
    {
        $this->setVoucher($voucher);
        return $this->createXmlDocument($invoiceTypeCode);
    }

    /**
     * 创建XML文档的内部方法
     * @param $invoiceTypeCode
     * @return string
     * @throws \DateMalformedStringException
     * @throws BusinessException
     */
    private function createXmlDocument(string $invoiceTypeCode): string
    {
        $builder = new XmlDocumentBuilder();
        return $this->createBuilder($builder, $invoiceTypeCode);
    }

    /**
     * 创建JSON文档（如果需要可以保留）
     * @param $invoiceTypeCode
     * @return mixed
     * @throws \DateMalformedStringException
     */
    // public function createJsonDocument($invoiceTypeCode)
    // {
    //     $builder = new JsonDocumentBuilder();
    //     return $this->createBuilder($builder, $invoiceTypeCode);
    // }

    /**
     * 创建builder
     * @param AbstractDocumentBuilder $builder
     * @param string $invoiceTypeCode
     * @return string
     * @throws BusinessException
     * @throws \DateMalformedStringException
     */
    private function createBuilder(AbstractDocumentBuilder $builder, string $invoiceTypeCode): string
    {
        // 生成submit document
        $document = $this->createDocument($invoiceTypeCode);
        $builder->setDocument($document);

        // 获取证书
        $filePath = CertificateService::getInstance()->getCertificatePath();
        $passphrase = CertificateService::getInstance()->getCertificatePassword();

        // 签名
        $builder->createSignature($filePath, null, $passphrase); // 调整签名方法参数

        // 文档序列化输出
        return $builder->build();
    }

    private function getDocumentInstance(string $invoiceTypeCode)
    {
        switch($invoiceTypeCode) {
            case InvoiceTypeCodes::CREDIT_NOTE:
                return new CreditNote();
            case InvoiceTypeCodes::DEBIT_NOTE:
                return new DebitNote();
            case InvoiceTypeCodes::REFUND_NOTE:
                return new RefundNote();
            case InvoiceTypeCodes::SELF_BILLED_INVOICE:
                return new SelfBilledInvoice();
            case InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE:
                return new SelfBilledCreditNote();
            case InvoiceTypeCodes::SELF_BILLED_DEBIT_NOTE:
                return new SelfBilledDebitNote();
            case InvoiceTypeCodes::SELF_BILLED_REFUND_NOTE:
                return new SelfBilledRefundNote();
            default:
                // 默认为普通发票，如果 SELF_BILLED_INVOICE 是最常用的，也可以设为默认
                return new Invoice();
                // 或者抛出异常： throw new \InvalidArgumentException("Unsupported Invoice Type Code: " . $invoiceTypeCode);
        }
    }

    private function createDocument(string $invoiceTypeCode)
    {
        // 获取数据
        $voucher = $this->getVoucher();
        $issueDateTime = new \DateTime('now', new \DateTimeZone('UTC'));
        //$issueDateTime->modify('-1 day'); // Yesterday

        $document = $this->getDocumentInstance($invoiceTypeCode);
        $document->setId($voucher->getIdentifier());
        $document->setIssueDateTime($issueDateTime);
        $document->setDocumentCurrencyCode($voucher->getCurrencyCode());

        $typeCode = $document->getInvoiceTypeCode(); // Get original type code
        $document->setInvoiceTypeCode($typeCode, '1.1'); // 1.1 is with digital signature verification
        $document = $this->setSupplier($document);
        $document = $this->setCustomer($document);
        $document = $this->setDocumentLine($document);
        $document = $this->setLegalMonetaryTotal($document);
        $document = $this->setTaxTotal($document);
        $document = $this->setBillingReference($document);
        $document = $this->setAdditionalDocumentReference($document);
        // $document = $this->setPrepaidPayment($document);

        return $document;
    }

    // --- 保留 setBillingReference, setPrepaidPayment, setSupplier, setCustomer, setDocumentLine, setLegalMonetaryTotal, setTaxTotal 等辅助方法 ---

    private function setBillingReference($document)
    {
        $voucher = $this->getVoucher();

        $billingReference = new BillingReference();
        $invoiceTypeCode = $voucher->getInvoiceTypeCode();

        if($invoiceTypeCode == InvoiceTypeCodes::SELF_BILLED_CREDIT_NOTE) {
            $invoiceDocumentReference = new InvoiceDocumentReference();
            $invoiceDocumentReference->setId($voucher->getOriginInvoiceNumber());
            $invoiceDocumentReference->setUuid($voucher->getInvoiceUuid());

            $billingReference->setInvoiceDocumentReference($invoiceDocumentReference);
        }
        $document->addBillingReference($billingReference);

        return $document;
    }

    private function setPrepaidPayment($document)
    {
        $prepaidPayment = new PrepaidPayment();
        $prepaidPayment->setId('E12345678912');
        $prepaidPayment->setPaidAmount(1.00);
        $prepaidPayment->setPaidDateTime(new \DateTime('2000-01-01 12:00:00Z'));

        $document->setPrepaidPayment($prepaidPayment);

        return $document;
    }

    /**
     * 供应商结构
     * @param $document
     * @return mixed
     */
    private function setSupplier($document)
    {
        $supplierData = $this->getVoucher()->getSupplier();
        $addressData = $supplierData['address'];

        $address = new Address();
        $address->setCityName($addressData['city']);
        $address->setCountrySubentityCode($addressData['state']);

        $addressLine = new AddressLine();
        $addressLine->setLine($addressData['line0']);
        $address->addAddressLine($addressLine);

        $country = new Country();
        $country->setIdentificationCode($addressData['country'] ?? CountryCodes::MALAYSIA); // 添加默认值或确保数据存在
        $address->setCountry($country);

        $legalEntity = new LegalEntity();
        $legalEntity->setRegistrationName($supplierData['name']);

        $contact = new Contact();
        $contact->setTelephone($supplierData['phone'] ?? ''); // 添加默认值

        $supplier = new Party();

        if (isset($supplierData['party_details']) && is_array($supplierData['party_details'])) {
            foreach($supplierData['party_details'] as $key => $value) {
                $partyIdentification = new PartyIdentification();
                $partyIdentification->setId($value, $key);
                $supplier->addPartyIdentification($partyIdentification);
            }
        }


        $supplier->setPostalAddress($address);
        $supplier->setLegalEntity($legalEntity);
        $supplier->setContact($contact);

        $msicCode = $supplierData['msic_code'] ?? '00000'; // 添加默认值
        $msicCodeDesc = MSICCodes::getDescription($msicCode);
        $supplier->setIndustryClassificationCode($msicCode, $msicCodeDesc);

        $accountingParty = new AccountingParty();
        $accountingParty->setParty($supplier);
        $document->setAccountingSupplierParty($accountingParty); // 使用正确的setter方法

        return $document;
    }

    /**
     * 客户结构
     * @param $document
     * @return mixed
     */
    private function setCustomer($document)
    {
        $customerData = $this->getVoucher()->getBuyer();
        $addressData = $customerData['address'];

        $address = new Address();
        $address->setCityName($addressData['city']);
        $address->setCountrySubentityCode($addressData['state']);

        $addressLine = new AddressLine();
        $addressLine->setLine($addressData['line0']);
        $address->addAddressLine($addressLine);

        $country = new Country();
        $country->setIdentificationCode($addressData['country'] ?? CountryCodes::MALAYSIA);
        $address->setCountry($country);

        $legalEntity = new LegalEntity();
        $legalEntity->setRegistrationName($customerData['name']);

        $contact = new Contact();
        $contact->setTelephone($customerData['phone'] ?? '');

        $customer = new Party();

        if (isset($customerData['party_details']) && is_array($customerData['party_details'])) {
            foreach($customerData['party_details'] as $key => $value) {
                $partyIdentification = new PartyIdentification();
                $partyIdentification->setId($value, $key);
                $customer->addPartyIdentification($partyIdentification);
            }
        }

        $customer->setPostalAddress($address);
        $customer->setLegalEntity($legalEntity);
        $customer->setContact($contact);

        // 客户通常没有MSIC Code，除非业务需要
        // $msicCode = $customerData['msic_code'] ?? '00000';
        // $msicCodeDesc = MSICCodes::getDescription($msicCode);
        // $customer->setIndustryClassificationCode($msicCode, $msicCodeDesc);

        $accountingParty = new AccountingParty();
        $accountingParty->setParty($customer);
        $document->setAccountingCustomerParty($accountingParty); // 使用正确的setter方法

        return $document;
    }

    /**
     * 单据行项目结构
     * @param $document
     * @return mixed
     */
    private function setDocumentLine($document)
    {
        $itemsData = $this->getVoucher()->getItems();
        $currencyCode = $this->getVoucher()->getCurrencyCode();
        $index = 1;

        foreach ($itemsData as $itemData) {
            $item = new Item();
            $item->setDescription($itemData['description']);

            $commodityClassification = new CommodityClassification();
            $commodityClassification->setItemClassificationCode(
                $itemData['classification'],
                'CLASS' // listID
            );
            $item->setCommodityClassification($commodityClassification);

            $price = new Price();
            $price->setPriceAmount($itemData['unit_price'], $currencyCode);

            $itemPriceExtension = new ItemPriceExtension();
            $itemPriceExtension->setAmount($itemData['subtotal'], $currencyCode); // 假设这里是税额
            //$itemPriceExtension->setAmount($itemData['tax_amount'], $currencyCode); // 假设这里是税额

            $taxTotal = new TaxTotal();
            $taxTotal->setTaxAmount($itemData['tax_amount'], $currencyCode);

            $taxSubTotal = new TaxSubTotal();
            $taxSubTotal->setTaxableAmount($itemData['total_excluding_tax'], $currencyCode);
            $taxSubTotal->setTaxAmount($itemData['tax_amount'], $currencyCode);
            $taxSubTotal->setPercent($itemData['tax_rate']);

            $taxCategory = new TaxCategory();
            $taxCategory->setId($itemData['tax_type']);

            $taxScheme = new TaxScheme();
            $taxScheme->setId('OTH', 'UN/ECE 5153', '6'); // 固定的scheme
            $taxCategory->setTaxScheme($taxScheme);
            $taxSubTotal->setTaxCategory($taxCategory);
            $taxTotal->addTaxSubTotal($taxSubTotal);

            $invoiceLine = new InvoiceLine();
            $invoiceLine->setId($index++);
            $invoiceLine->setInvoicedQuantity($itemData['quantity'], $itemData['measurement']);
            $invoiceLine->setLineExtensionAmount($itemData['total_excluding_tax'], $currencyCode);
            $invoiceLine->setItem($item);
            $invoiceLine->setPrice($price);
            $invoiceLine->setItemPriceExtension($itemPriceExtension);
            $invoiceLine->setTaxTotal($taxTotal); // 添加单个项目的税信息

            $document->addInvoiceLine($invoiceLine);
        }

        return $document;
    }

    /**
     * 法定货币总额结构
     * @param $document
     * @return mixed
     */
    private function setLegalMonetaryTotal($document)
    {
        $taxesData = $this->getVoucher()->getTaxes();
        $currencyCode = $this->getVoucher()->getCurrencyCode();

        $legalMonetaryTotal = new LegalMonetaryTotal();
        $legalMonetaryTotal->setTaxExclusiveAmount($taxesData['total_excluding_tax'], $currencyCode);
        $legalMonetaryTotal->setTaxInclusiveAmount($taxesData['total_including_tax'], $currencyCode);
        $legalMonetaryTotal->setPayableAmount($taxesData['total_payable_amount'], $currencyCode);
        $legalMonetaryTotal->setLineExtensionAmount(0.00, $currencyCode);
        $legalMonetaryTotal->setAllowanceTotalAmount(0.00, $currencyCode);

        // 根据需要添加其他金额, 例如 PrepaidAmount, AllowanceTotalAmount, ChargeTotalAmount
        // $legalMonetaryTotal->setPrepaidAmount(0.00, $currencyCode);
        // $legalMonetaryTotal->setChargeTotalAmount(0.00, $currencyCode);

        $document->setLegalMonetaryTotal($legalMonetaryTotal);

        return $document;
    }

    /**
     * 税总额结构
     * @param $document
     * @return mixed
     */
    private function setTaxTotal($document)
    {
        $taxesData = $this->getVoucher()->getTaxes();
        $currencyCode = $this->getVoucher()->getCurrencyCode();

        $taxTotal = new TaxTotal();
        $taxTotal->setTaxAmount($taxesData['total_tax_amount'], $currencyCode);

        // TaxSubTotal 部分通常包含不同税率的汇总信息
        // 在当前结构下，如果只有一个税种，可以这样简化处理
        $taxSubTotal = new TaxSubTotal();
        $taxSubTotal->setTaxableAmount(0, $currencyCode);
        $taxSubTotal->setTaxAmount($taxesData['total_tax_amount'], $currencyCode); // 税额

        $taxCategory = new TaxCategory();
        $taxCategory->setId($taxesData['tax_type']);

        $taxScheme = new TaxScheme();
        $taxScheme->setId('OTH', 'UN/ECE 5153', '6');
        $taxCategory->setTaxScheme($taxScheme);
        $taxSubTotal->setTaxCategory($taxCategory);
        $taxTotal->addTaxSubTotal($taxSubTotal);

        $document->setTaxTotal($taxTotal);

        return $document;
    }

    /**
     * 清关编号
     * @param $document
     * @return mixed
     */
    private function setAdditionalDocumentReference($document)
    {
        $clearanceNoData = $this->getVoucher()->getClearanceNo();

        if (empty($clearanceNoData)) {
            return $document;
        }

        $additionalDocumentReference = new AdditionalDocumentReference();
        $additionalDocumentReference->setId($clearanceNoData);
        $additionalDocumentReference->setDocumentType('CustomImportForm');
        $document->setAdditionalDocumentReference($additionalDocumentReference);

        return $document;
    }
} 