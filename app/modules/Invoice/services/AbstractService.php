<?php

/**
 * @copyright Copyright (c) 2024 <PERSON> (<EMAIL>)
 * @license https://github.com/klsheng/myinvois-php-sdk/blob/main/LICENSE
 */

namespace App\Modules\Invoice\Services;

use App\Library\Enums\InvoiceEnums;
use Exception;

/**
 * Abstract class for service component
 * 
 * <AUTHOR> (<EMAIL>)
 * @since 1.0.0
 */
abstract class AbstractService
{
    /**
     * Base URL
     *
     * @var string
     */
    private $baseUrl = '';

    /**
     * InvoiceClient object
     * 
     * @var InvoiceClient
     */
    private $client;

    /**
     * AbstractService constructor.
     *
     * @param InvoiceClient    $client
     * @param string $baseUrl
     */
    public function __construct(InvoiceClient $client, string $baseUrl)
    {
        $this->client = $client;
        $this->baseUrl = $baseUrl;
    }

    /**
     * @return string
     */
    protected function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    /**
     * @return mixed
     */
    protected function getClient()
    {
        return $this->client;
    }

    /**
     * 获取服务API URL
     *
     * @param string $servicePath 服务路径常量
     * @param bool $prodMode 是否为生产环境
     * @return string
     */
    protected function initApiUrl(string $servicePath, bool $prodMode = false): string
    {
        return $prodMode
            ? InvoiceEnums::getProdUrl($servicePath)
            : InvoiceEnums::getSandboxUrl($servicePath);
    }
}
