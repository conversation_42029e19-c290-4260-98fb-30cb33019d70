# API 限流管理系统

本文档描述了为发票模块 API 调用实现的全局限流管理系统。

## 功能概述

系统通过在 `InvoiceClient` 的 `request` 方法中集成限流逻辑，实现了对所有 API 调用的统一管理，无需在每个 API 调用处单独实现限流处理。

主要功能包括：

1. **按 API 类型限流**：不同 API 接口有不同的限流规则
2. **平滑请求间隔**：确保请求不会过于集中发送
3. **指数退避重试**：当触发限流时，使用指数递增的等待时间重试
4. **尊重服务器限流指示**：支持处理 429 响应和 Retry-After 头
5. **错误记录**：详细记录限流事件，便于问题排查

## 组件说明

### 1. RateLimitManager 类

位于 `app/modules/Invoice/services/RateLimitManager.php`，负责：

- 存储和管理每种 API 类型的限流规则
- 判断是否允许发送请求
- 计算请求间隔时间
- 处理限流响应
- 记录限流信息

主要方法：

- `getApiTypeFromUrl(string $url)`: 从 URL 识别 API 类型
- `checkAndIncrement(string $apiType, string $clientId)`: 检查是否可以发送请求并增加计数
- `handleRateLimitResponse(\Exception $e, string $apiType, int $retryCount)`: 处理限流响应

### 2. InvoiceClient 请求处理修改

在 `InvoiceClient` 的 `request` 方法中集成了限流逻辑：

- 使用 `RateLimitManager` 检查是否允许发送请求
- 当达到限流上限时应用延迟或等待策略
- 处理 429 响应并智能重试
- 使用指数退避算法确定重试间隔

### 3. RateLimitException 类

位于 `app/modules/Invoice/Exceptions/RateLimitException.php`，扩展自标准异常：

- 包含 API 类型和建议重试时间信息
- 便于上层应用程序针对限流异常采取特定处理

## 限流规则配置

当前配置的限流规则如下：

| API 类型 | 每分钟最大请求数 |
|---------|--------------|
| login | 12 |
| loginOnBehalf | 12 |
| submitDocument | 100 |
| getSubmission | 300 |
| cancelDocument | 12 |
| rejectDocument | 12 |
| getDocument | 60 |
| getDocumentDetail | 125 |
| getRecentDocuments | 12 |
| searchDocuments | 12 |
| searchTaxpayerTin | 60 |
| taxpayerQrCode | 60 |
| default | 60 |

可以通过修改 `RateLimitManager` 类中的 `$rateLimits` 数组来调整这些规则。

## 使用示例

系统已自动集成到所有 API 调用中，无需额外代码。但如需捕获限流异常，可以如下处理：

```php
try {
    $response = $client->documentSubmission->submitDocument($documents);
    // 处理正常响应
} catch (RateLimitException $e) {
    // 处理限流异常
    $apiType = $e->getApiType(); 
    $retryAfter = $e->getRetryAfter();
    error_log("API {$apiType} 限流，建议 {$retryAfter} 秒后重试");
    // 可以根据业务需要决定是放弃请求还是安排稍后重试
} catch (\Exception $e) {
    // 处理其他异常
}
```

## 优势与特性

1. **集中管理**：所有 API 限流逻辑在一个地方集中管理
2. **自适应**：根据不同 API 类型应用不同的限流规则
3. **智能重试**：限流时自动应用退避策略重试
4. **防止失败级联**：避免因限流导致的系统级联失败
5. **抢先限流**：客户端预先控制请求速率，减少服务器限流触发
6. **可扩展**：易于添加新的 API 类型或调整限流规则 