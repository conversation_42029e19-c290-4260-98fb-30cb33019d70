<?php
namespace App\Modules\AssetWorkOrder\Services;

use App\Library\ApiClient;
use App\Library\Enums\AssetWorkOrderEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\AssetWorkOrderModel;
use App\Models\backyard\AssetWorkOrderTypeModel;
use App\Models\backyard\SysStoreModel;


/**
 * 资产工单-服务层
 * Class OrderService
 * @package App\Modules\AssetWorkOrder\Services
 */
class OrderService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'type_id',
        'store_id',
        'status',
        'created_at_start',
        'created_at_end',
        'updated_at_start',
        'updated_at_end',
        'pageSize',
        'pageNum',
    ];

    /**
     * 列表验证
     * @var array
     */
    public static $validate_list = [
        'order_code' => 'StrLenGeLe:0,32|>>>:order_code error',
        'type_id' => 'Arr|ArrLenGe:0|>>>:type_id error',
        'type_id[*]' => 'IntGt:0',
        'staff_id_or_name' => 'StrLenGeLe:0,50|>>>:staff_id_or_name error',
        'status' => 'Arr|ArrLenGe:0|>>>: status error',
        'status[*]' => 'IntGt:0',
        'store_id' => 'Arr|ArrLenGe:0|>>>:store_id error',
        'store_id[*]' => 'StrLenGeLe:1,10',
        'created_at_start' => 'Date|>>>:created_at_start error',
        'created_at_end' => 'Date|>>>:created_at_end error',
        'updated_at_start' => 'Date|>>>:updated_at_start error',
        'updated_at_end' => 'Date|>>>:updated_at_end error',
        'pageNum' => 'IntGe:1|>>>:pageNum error',
        'pageSize' => 'IntGe:1|>>>:pageSize error'
    ];

    /**
     * 工单详情-验证
     * @var array
     */
    public static $validate_detail = [
        'id' => 'Required|IntGe:1|>>>:id error'
    ];

    /**
     * 工单回复-验证
     * @var array
     */
    public static $validate_reply = [
        'id'          => 'Required|IntGe:1|>>>:id error',
        'mark'        => 'Required|StrLenGeLe:5,500|>>>:mark error',
        'attachments' => 'Arr|ArrLenGeLe:0,5|>>>:main attachment list  error[Upload up to 5]',
    ];

    /**
     * 工单关闭-验证
     * @var array
     */
    public static $validate_close = [
        'ids' => 'Required|Arr|ArrLenGeLe:1,100|>>>:ids error',
        'ids[*]' => 'IntGt:0',
        'close_reason' => 'Required|StrLenGeLe:1,500|>>>:close reason error',
    ];

    /**
     * by rpc 路由组
     * @var array
     */
    public static $by_url = [
        'enums' => 'asset_work_order_enums',//问题类型\问题所属仓\处理状态路由
        'list' => 'asset_work_order_list',//列表
        'detail' => 'asset_work_order_detail',//详情
        'reply' => 'asset_work_order_reply',//回复
        'close' => 'asset_work_order_close',//关闭
    ];

    /**
     * 资产工单-问题类型\问题所属仓\处理状态
     * @return array
     */
    public function getDefaultData()
    {
        $ac = new ApiClient('by', '', self::$by_url['enums'], static::$language);
        $ac->setParams([[]]);
        $res = $ac->execute();
        $code = $res['result']['code'] ?? $res['code'];
        $data = [];
        if ($code == ErrCode::$SUCCESS) {
            //请求成功
            $data = $res['result']['data'] ?? [];
        }
        return $data;
    }

    /**
     * 资产工单-列表
     * @param array $condition 请求参数
     * @return array
     */
    public function getList($condition)
    {
        try {
            //提交日期起始与结束校验
            $add_start = !empty($condition['created_at_start']) ? strtotime($condition['created_at_start']) : '';
            $add_end = !empty($condition['created_at_end']) ? strtotime($condition['created_at_end']) : '';
            //最后更新日期起始与结束校验
            $update_start = !empty($condition['updated_at_start']) ? strtotime($condition['updated_at_start']) : '';
            $update_end = !empty($condition['updated_at_end']) ? strtotime($condition['updated_at_end']) : '';
            if ($add_start > $add_end || $update_start > $update_end) {
                throw new ValidationException(self::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }

            $condition['page_size'] = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
            $condition['page_num']  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
            unset($condition['pageSize'], $condition['pageNum']);
            //通过rpc获取数据源
            $ac = new ApiClient('by', '', self::$by_url['list'], static::$language);
            $ac->setParams([$condition]);
            $res = $ac->execute();
            $by_code = $res['result']['code'] ?? $res['code'];
            if ($by_code == ErrCode::$SUCCESS) {
                //请求成功
                $data = $res['result']['data'] ?? [];
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('asset-work-order-list failed:' . $real_message);
        }

        return [
            'code' => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data' => $data ?? [],
        ];
    }

    /**
     * 资产工单-导出
     * @param array $condition 请求参数组
     * @return array
     */
    public function export($condition)
    {
        try {
            //导出的数据
            $data = [];
            $builder = $this->modelsManager->createBuilder();
            $builder->from(AssetWorkOrderModel::class);
            $builder->columns('count(id) as total');
            $builder = $this->getCondition($builder, $condition);
            $count = (int)$builder->getQuery()->getSingleResult()->total;
            if ($count > 0) {
                if ($count > AssetWorkOrderEnums::ASSET_WORK_ORDER_EXPORT_LIMIT) {
                    throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max' => AssetWorkOrderEnums::ASSET_WORK_ORDER_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
                }
                $builder->columns([
                    'order_code',
                    'created_at',
                    'created_staff_name',
                    'created_staff_id',
                    'created_department_name',
                    'created_job_title_name',
                    'created_store_name',
                    'type_id',
                    'store_id',
                    'info',
                    'status',
                    'first_deal_staff_name',
                    'first_deal_staff_id',
                    'first_deal_staff_time',
                    'last_deal_staff_name',
                    'last_deal_staff_id',
                    'updated_at'
                ]);
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $data = $this->formatList($items);
            }
            $header = [
                static::$t->_('administration_ticket_order_code'),//工单ID
                static::$t->_('administration_ticket_created_at'),//提交时间
                static::$t->_('administration_ticket_created_staff_name'),//提交人
                static::$t->_('administration_ticket_created_staff_id'),//提交人工号
                static::$t->_('administration_ticket_created_department_name'),//部门
                static::$t->_('administration_ticket_job_title'),//职位
                static::$t->_('administration_ticket_created_store_name'),//所属网点
                static::$t->_('administration_ticket_question_type_name'),//问题类型
                static::$t->_('asset_work_order_store_name'),//问题所在仓
                static::$t->_('administration_ticket_info'),//问题详情
                static::$t->_('administration_ticket_status_name'),//处理状态
                static::$t->_('administration_ticket_first_deal_staff_name'),//首次回复人
                static::$t->_('administration_ticket_first_deal_staff_id'),//首次回复人工号
                static::$t->_('administration_ticket_first_deal_staff_time'),//首次回复时间
                static::$t->_('administration_ticket_last_deal_staff_name'),//最后回复人
                static::$t->_('administration_ticket_last_deal_staff_id'),//最后回复人工号
                static::$t->_('administration_ticket_updated_at')//最后更新时间
            ];
            $file_name = 'asset_work_order_export_' . date('YmdHis');
            $result = $this->exportExcel($header, $data, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('asset-work-order-export failed:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器对象
     * @param array $params 查询条件
     * @return mixed
     * @throws ValidationException
     */
    private function getCondition($builder, $params)
    {
        //提交日期起始与结束校验
        $add_start = !empty($condition['created_at_start']) ? strtotime($condition['created_at_start']) : '';
        $add_end = !empty($condition['created_at_end']) ? strtotime($condition['created_at_end']) : '';
        //最后更新日期起始与结束校验
        $update_start = !empty($condition['updated_at_start']) ? strtotime($condition['updated_at_start']) : '';
        $update_end = !empty($condition['updated_at_end']) ? strtotime($condition['updated_at_end']) : '';
        if ($add_start > $add_end || $update_start > $update_end) {
            throw new ValidationException(self::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
        }

        //按照工单编号筛选
        if (!empty($params['order_code'])) {
            $builder->andWhere('order_code like :order_code:', ['order_code' => '%' . $params['order_code'] . '%']);
        }
        //按照问题类型筛选
        if (!empty($params['type_id'])) {
            if (is_array($params['type_id'])) {
                $builder->inWhere('type_id', $params['type_id']);
            } else {
                $builder->andWhere('type_id = :type_id:', ['type_id' => $params['type_id']]);
            }
        }
        //按照问题所在仓筛选
        if (!empty($params['store_id'])) {
            if (is_array($params['store_id'])) {
                $builder->inWhere('store_id', $params['store_id']);
            } else {
                $builder->andWhere('store_id = :store_id:', ['store_id' => $params['store_id']]);
            }
        }
        //按照提交人工号或者姓名筛选
        if (!empty($params['staff_id_or_name'])) {
            $builder->andWhere('created_staff_id like :created_staff_id: or created_staff_name like :created_staff_id:', ['created_staff_id' => '%' . $params['staff_id_or_name'] . '%']);
        }
        //按照状态筛选
        if (!empty($params['status'])) {
            if (is_array($params['status'])) {
                $builder->inWhere('status', $params['status']);
            } else {
                $builder->andWhere('status = :status:', ['status' => $params['status']]);
            }
        }
        //提交时间筛选
        if (!empty($params['created_at_start']) && !empty($params['created_at_end'])) {
            $params['created_at_start'] .= ' 00:00:00';
            $params['created_at_end']   .= ' 23:59:59';
            $builder->betweenWhere('created_at', $params['created_at_start'], $params['created_at_end']);
        }
        //最后更新时间筛选
        if (!empty($params['updated_at_start']) && !empty($params['updated_at_end'])) {
            $params['updated_at_start'] .= ' 00:00:00';
            $params['updated_at_end']   .= ' 23:59:59';
            $builder->betweenWhere('updated_at', $params['updated_at_start'], $params['updated_at_end']);
        }
        return $builder;
    }


    /**
     * 格式化工单列表
     * @param array $list 工单列表组
     * @return array
     */
    private function formatList($list)
    {
        if (empty($list)) {
            return [];
        }
        //网点ID组
        $store_ids = array_values(array_unique(array_column($list, 'store_id')));

        //获取网点
        $store_list = [];
        $store_ret = SysStoreModel::find([
            'conditions' => 'id IN ({store_ids:array})',
            'bind' => [
                'store_ids' => $store_ids,
            ],
            'columns' => 'id,name',
        ])->toArray();
        if (!empty($store_ret)) {
            $store_list = array_column($store_ret, null, 'id');
        }

        //获取问题类型
        $question_type_list = [];
        $question_type_ids = array_values(array_unique(array_column($list, 'type_id')));
        $question_type_ret = AssetWorkOrderTypeModel::find([
            'conditions' => "id IN ({type_ids:array}) and is_deleted = :is_deleted:",
            'bind' => [
                'type_ids' => $question_type_ids,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
            "columns" => 'id,t_key',
        ])->toArray();
        if (!empty($question_type_ret)) {
            $question_type_list = array_column($question_type_ret, null, 'id');
        }
        //状态枚举
        $status = AssetWorkOrderEnums::$order_status;
        $row_values = [];
        foreach ($list as $item) {
            $row_values[] = [
                $item['order_code'],//工单ID
                $item['created_at'],//提交时间
                $item['created_staff_name'],//提交人
                $item['created_staff_id'],//提交人工号
                $item['created_department_name'],//部门
                $item['created_job_title_name'],//职位
                $item['created_store_name'],//所属网点
                !empty($question_type_list[$item['type_id']]) ? static::$t->_($question_type_list[$item['type_id']]['t_key']) : '',//问题类型
                !empty($store_list[$item['store_id']]) ? $store_list[$item['store_id']]['name'] : '',//问题所在仓
                $item['info'],//问题详情
                !empty($status[$item['status']]) ?  static::$t->_($status[$item['status']]) : '',//处理状态
                $item['first_deal_staff_name'],//首次回复人
                $item['first_deal_staff_id'],//首次回复人工号
                $item['first_deal_staff_time'],//首次回复人时间
                $item['last_deal_staff_name'],//最后回复人
                $item['last_deal_staff_id'],//最后回复人工号
                $item['updated_at'],//最后更新时间
            ];
        }
        return $row_values;
    }

    /**
     * 资产工单-详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getDetail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            //通过rpc获取数据源
            $ac = new ApiClient('by', '', self::$by_url['detail'], static::$language);
            $ac->setParams([['id' => $params['id']]]);
            $res = $ac->execute();
            $by_code = $res['result']['code'] ?? $res['code'];
            if ($by_code == ErrCode::$SUCCESS) {
                //请求成功
                $detail = $res['result']['data'] ?? [];
            } else {
                throw new ValidationException($res['result']['msg'] ?? static::$t->_('retry_later'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('asset-work-order-detail failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产工单-回复
     * @param array $condition 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reply($condition, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        try {
            $pics = [];
            if (!empty($condition['attachments'])) {
                foreach ($condition['attachments'] as $attachment) {
                    $pics[] = [
                        'pic_name' => $attachment['file_name'],
                        'pic_path' => $attachment['object_url']
                    ];
                }
            }

            //通过rpc获取数据源
            $ac = new ApiClient('by', '', self::$by_url['reply'], static::$language);
            $ac->setParams([[
                'order_id' => $condition['id'],//工单ID
                'created_staff_id' => $user['id'],//提交人id
                'created_staff_name' => $user['name'],//提交人姓名
                'mark' => $condition['mark'],//内容
                'pics' => !empty($pics) ? json_encode($pics, JSON_UNESCAPED_UNICODE) : '',//图片
            ]]);
            $res = $ac->execute();
            $by_code = $res['result']['code'] ?? $res['code'];
            if ($by_code != ErrCode::$SUCCESS) {
                //非请求成功
                $code = ErrCode::$VALIDATE_ERROR;
                $message = $res['result']['msg'] ?? $res['msg'];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('asset-work-order-reply failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data ?? [],
        ];
    }

    /**
     * 资产工单-关闭
     * @param array $condition 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function close($condition, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        try {
            //通过rpc获取数据源
            $ac = new ApiClient('by', '', self::$by_url['close'], static::$language);
            $ac->setParams([[
                'order_id' => $condition['ids'],//工单ID组
                'close_staff_id' => $user['id'],//工单关闭人id
                'close_reason' => $condition['close_reason'],//工单关闭原因
            ]]);
            $res = $ac->execute();
            $by_code = $res['result']['code'] ?? $res['code'];
            if ($by_code != ErrCode::$SUCCESS) {
                //非请求成功
                $code = ErrCode::$VALIDATE_ERROR;
                $message = $res['result']['msg'] ?? $res['msg'];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('asset-work-order-reply failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data ?? [],
        ];
    }
}
