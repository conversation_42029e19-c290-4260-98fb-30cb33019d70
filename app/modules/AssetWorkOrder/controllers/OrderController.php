<?php
namespace App\Modules\AssetWorkOrder\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\AssetWorkOrder\Services\BaseService;
use App\Modules\AssetWorkOrder\Services\OrderService;
use App\Util\RedisKey;

/**
 * 资产工单-控制器
 * Class OrderController
 * @package App\Modules\AssetWorkOrder\Controllers
 */
class OrderController extends BaseController
{
    /**
     * 资产工单-问题类型\问题所属仓\处理状态
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63777
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = OrderService::getInstance()->getDefaultData();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 资产工单-列表
     * @Permission(action='asset_work_order.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63932
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterfac
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_params);
        try {
            Validation::validate($params, OrderService::$validate_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = OrderService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产工单-导出
     * @Permission(action='asset_work_order.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64067
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, OrderService::$not_must_params);
        try {
            $param_rule = OrderService::$validate_list;
            unset($param_rule['pageSize'], $param_rule['pageNum']);
            Validation::validate($params, $param_rule);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::ASSET_WORK_ORDER_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return OrderService::getInstance()->export($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$SUCCESS, $res['message'] ?? '', $res['data'] ?? []);
    }

    /**
     * 资产工单-查看
     * @Permission(action='asset_work_order.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64032
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterfac
     */
    public function detailAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, OrderService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = OrderService::getInstance()->getDetail($param);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产工单-回复
     * @Permission(action='asset_work_order.reply')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64087
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterfac
     */
    public function replyAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, OrderService::$validate_reply);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = OrderService::getInstance()->reply($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产工单-关闭
     * @Permission(action='asset_work_order.close')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64092
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterfac
     */
    public function closeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, OrderService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = OrderService::getInstance()->close($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
