<?php

namespace App\Modules\Extinguisher\Services;

use App\Library\Enums;
use App\Modules\Extinguisher\Models\HrStaffInfoModel;
use App\Modules\Extinguisher\Models\HrStaffInfoPositionModel;
use App\Modules\Extinguisher\Models\SysStoreModel;
use App\Library\Enums\ExtinguisherEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\RoleEnums;

class StaffInfoService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * @param $keyword
     * 获取网点名称等列表
     * <AUTHOR>
     */
    function getStoreNameList($keyword)
    {
        $limit = Enums\GlobalEnums::DEFAULT_PAGE_SIZE;
        //模糊搜索总部
        $others = [];
        if (false !== strpos(strtolower(Enums::PAYMENT_HEADER_STORE_NAME), strtolower($keyword))) {
            $others[] =
                [
                    'id'             => '',
                    'areas'          => '',
                    'category'       => '',
                    'category_name'  => '',
                    'sorting_no'     => '',
                    'store_id'       => (string)Enums::HEAD_OFFICE_STORE_FLAG,
                    'store_name'     => Enums::PAYMENT_HEADER_STORE_NAME,
                    'administrators' => []
                ];
            $limit    = $limit - 1;
        }

        $category_ids = array_keys(ExtinguisherEnums::$extinguisher_store_category);
        $builder      = $this->modelsManager->createBuilder();
        $builder->columns('ss.id store_id, ss.name store_name,ss.category, ss.sorting_no');
        $builder->from(['ss' => SysStoreModel::class]);

        //有搜索词按搜索词模糊搜id和name，没有搜索词默认取前20条
        if (!empty($keyword)) {
            $builder->where(
                'category IN ({category_ids:array}) AND (ss.id LIKE :id: OR ss.name LIKE :name:)',
                ['category_ids' => $category_ids, 'id' => "%$keyword%", 'name' => "%$keyword%"]
            );
        } else {
            $builder->where(
                'category IN ({category_ids:array})',
                ['category_ids' => $category_ids]
            );
        }
        $builder->limit($limit);
        $list = $builder->getQuery()->execute()->toArray();
        if (!empty($list)) {
            //主管和仓管
            $staff_info_arr = [];
            $sys_store_ids  = array_values(array_unique(array_filter(array_column($list, 'store_id'))));
            if (!empty($sys_store_ids)) {
                $staff_info_arr = $this->getStoreResponsibleByNetworkManage($sys_store_ids);

            }
            foreach ($list as $key => $val) {
                $list[$key]['category_name'] = ExtinguisherEnums::$extinguisher_store_category[$val['category']];
                $list[$key]['areas'] = ExtinguisherEnums::$sorting_no_map[$val['sorting_no']];
                $list[$key]['administrators'] = !empty($staff_info_arr[$val['store_id']]) ? $staff_info_arr[$val['store_id']] : [];
            }
        }
        return array_merge($others, $list);
    }

    /**
     * 获取网点下的主管和 网管
     * @param array $sys_store_ids 网点id
     * @return  array
     **/
    public function getStoreResponsibleByNetworkManage(array $sys_store_ids)
    {
        $staff_arr = [];
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns("hsi.sys_store_id, hsi.name, hsi.staff_info_id, concat(hsi.name, '(', hsi.staff_info_id, ')') as show_name");
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(hrStaffInfoPositionModel::class, 'hsi.staff_info_id = hsip.staff_info_id', 'hsip');
        $builder->where(
            'hsi.state = :state: and hsi.wait_leave_state = :wait_leave_state:',
            ['state'            => StaffInfoEnums::STAFF_STATE_IN,
             'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]
        );
        $builder->inwhere('hsi.sys_store_id', $sys_store_ids);
        $builder->inwhere('hsip.position_category', [RoleEnums::ROLE_STORE_SUPERVISOR, RoleEnums::ROLE_STORE_WAREHOUSE_KEEPER]);
        $hr_staff_info_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($hr_staff_info_arr)) {
            foreach ($hr_staff_info_arr as $staff_info) {
                $staff_arr[$staff_info['sys_store_id']][] = $staff_info;
            }
        }
        return $staff_arr;
    }

    /**
     * @param $keyword
     * 根据关键字获取员工信息，如果关键字为空默认取20条
     */
    function getStaffList($keyword)
    {

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id, name');
        $builder->from(HrStaffInfoModel::class);
        //有搜索词按搜索词模糊搜id和name，没有搜索词默认取前20条
        if (!empty($keyword)) {
            $builder->where(
                "staff_info_id LIKE :id: ",
                ['id' => "%$keyword%"]
            );
        } else {
            $builder->limit(Enums\GlobalEnums::DEFAULT_PAGE_SIZE);
        }
        $hr_staff_info_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($hr_staff_info_arr)) {
            foreach ($hr_staff_info_arr as &$item) {
                $item['show_name'] = $item['name'] . '(' . $item['staff_info_id'] . ')';
            }
        }
        return $hr_staff_info_arr;
    }








}
