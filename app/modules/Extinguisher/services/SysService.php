<?php

namespace App\Modules\Extinguisher\Services;

use App\Modules\Extinguisher\Models\HrStaffInfoModel;
use App\Modules\Extinguisher\Models\SysStoreModel;
use App\Modules\Extinguisher\Models\SysProvinceModel;
use App\Library\Enums\ExtinguisherEnums;


class SysService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 批量获取系统信息
     */
    function sysList($list_code){
        $data = [];
        foreach($list_code as $key=>$value){
            if( method_exists($this, $value) ){
                $data[$value] = $this->$value();
            }
        }
        return $data;
    }

    /**
     * 灭火器类型
     */
    function ExtinguisherType(){
        $data = [];
        foreach(ExtinguisherEnums::$extinguisher_type as $val){
            $data[$val] = static::$t->_('extinguisher_type_'.$val);
        }

        return $data;
    }

    /**
     * 网点类型
     */
    function StoreCategory(){
        return ExtinguisherEnums::$extinguisher_store_category;
    }

    /**
     * 检查状态
     * @return array
     */
    function checkStatus()
    {
        $data = [];
        foreach(ExtinguisherEnums::$extinguisher_check_status as $key => $val){
            $data[$key] = static::$t->_($val);
        }
        return $data;
    }
    /**
     * 检查结果
     * @return array
     */
    function checkResult()
    {
        $data = [];
        foreach(ExtinguisherEnums::$extinguisher_check_result as $key => $val){
            $data[$key] = static::$t->_($val);
        }
        return $data;
    }






}