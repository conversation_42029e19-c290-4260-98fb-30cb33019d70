<?php
/**
 * Author: Bruce
 * Date  : 2021-11-20 14:10
 * Description:
 */

namespace App\Modules\Extinguisher\Services;


use App\Library\ApiClient;
use App\Modules\Common\Services\StaffLangService;

class PushService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return PushService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 推送PUSH
     * @param array $checkStaffList 筛选到的员工列表
     * @param string $title 推送显示的标题 为翻译key
     * @param string $content 推送显示的内容 为翻译key
     * @param array $data 内容如果为新的数据 通过翻译key替换
     */
    public function sendPush(array $checkStaffList, string $title = 'extinguisher_notice_push_message_title', string $content = 'extinguisher_notice_push_content', array $data = [])
    {
        $staff_ids = implode(',', $checkStaffList);
        $this->logger->info("推送PUSH-Extinguisher DATA:" . json_encode(['staff_ids' => $staff_ids, 'date' => date('Y-m-d H:i:s')]));
        try {
            $platform       = ['backyard'];
            $message_scheme = 'flashbackyard://fe/html?url=';
            foreach ($checkStaffList as $staff) {
                //拼接push接口数据
                $data = [
                    'staff_info_id'    => $staff,//员工id
                    'message_title'    => StaffLangService::getInstance()->getMsgTemplateByUserId($staff, $title),//标题
                    'message_content'  => StaffLangService::getInstance()->getMsgTemplateByUserId($staff, $content, $data),//内容
                    'message_scheme'   => $message_scheme, //地址
                    'message_priority' => 1,// push优先级: 0-普通; 1-优先
                ];
                foreach ($platform as $item) {
                    $data['src'] = $item;
                    $ret         = new ApiClient('bi_svc', '', 'push_to_staff');
                    $ret->setParams([$data]);
                    $res       = $ret->execute();
                    $bi_return = $res && isset($res['result']) && $res['result'] == true;
                    $res_msg   = $bi_return ? '成功' : '失败';
                    $res_log   = '给端为' . $item . ',员工工号为: ' . $staff . '发送灭火器检查任务push消息,发送时间:' . date('Y-m-d H:i:s') . ' ,发送结果:' . $res_msg . ';结果是数据为:' . json_encode($bi_return, JSON_UNESCAPED_UNICODE);
                    $this->logger->info($res_log);
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning('给员工发送灭火器检查任务push消息，失败[异常]' . $e->getMessage());
        }
    }

    /**
     * 发送站内信
     * @param array $checkStaffList 筛选到的员工列表
     * @param string $title 推送显示的标题 为翻译key
     * @param string $content 推送显示的内容 为翻译key
     * @param array $data 内容如果为新的数据 通过翻译key替换
     */
    public function sendMessage(array $checkStaffList, $title= '', $content = '', $data =[])
    {
        if (empty($title) && empty($content) && empty($data)) {
            // 初始化系统语言
            static::setLanguage('th');
            $message_title   =  static::$t->_('extinguisher_message_title');
            $message_content   =  static::$t->_('extinguisher_message_content');
        } else {
            //单个发送 内容都不相同
            $message_title   = StaffLangService::getInstance()->getMsgTemplateByUserId($checkStaffList[0], $title, $data);//标题
            $message_content = StaffLangService::getInstance()->getMsgTemplateByUserId($checkStaffList[0], $content, $data);//内容
        }

        $staff_ids = implode(',', $checkStaffList);
        $this->logger->info('发送站内信-Extinguisher DATA:' . json_encode(['staff_ids' => $staff_ids, 'date' => date('Y-m-d H:i:s')]));
        try {
            $kit_param = [];
            $staff_users = [];
            foreach ($checkStaffList as $staff) {
                $staff_users[] = ['id'=>$staff];
            }
            $kit_param['staff_users'] = $staff_users;
            $kit_param['message_title'] = $message_title;
            $kit_param['message_content'] = $message_content;
            $kit_param['category'] = -1;

            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res = $bi_rpc->execute();
            $bi_return = $res && isset($res['result']) && $res['result']['code'] == 1;
            $res_msg = $bi_return ? '成功' : '失败';
            $res_log = '给员工工号组:' . $staff_ids . ',发送灭火器检查任务站内信消息，发送时间:' . date('Y-m-d H:i:s') . ' ,发送结果:' . $res_msg . ';结果数据: ' . json_encode($res, JSON_UNESCAPED_UNICODE);
            $this->logger->info($res_log);
        } catch (\Exception $e) {
            $this->logger->warning('给员工发送灭火器检查任务站内信消息，失败[异常]' . $e->getMessage());
        }
    }
}
