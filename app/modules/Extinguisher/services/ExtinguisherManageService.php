<?php

namespace App\Modules\Extinguisher\Services;

use App\Library\Enums;
use App\Library\Enums\ExtinguisherEnums;
use App\Library\ErrCode;
use App\Modules\Extinguisher\Models\ExtinguisherBaseModel;
use App\Modules\Extinguisher\Models\ExtinguisherOperationLogModel;
use App\Modules\Extinguisher\Models\ExtinguisherStaffRelationModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskStaffRelationModel;
use App\Modules\Extinguisher\Models\HrStaffInfoModel;
use App\Modules\Extinguisher\Models\SysStoreModel;
use App\Library\Validation\ValidationException;
use App\Modules\Extinguisher\Models\HrStaffInfoPositionModel;


class ExtinguisherManageService extends BaseService
{
    private static $instance;

    public static $max_extinguisher_count = 200;//最大灭火器数量
    public static $min_extinguisher_count = 1;//最小灭火器数量

    public static $department_arr = [4, 13, 34];
    public static $position_arr = [2,18];

    private function __construct()
    {
    }

    /**
     * @return SysService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 新建灭火器
     * <AUTHOR>
     */
    function addExtinguisher($params, $staff_info_id){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $item_error = [];
        //组装灭火器数据
        $extinguisher_base_info = [
            'store_id' => $params['store_id'],
            'extinguisher_code' => $params['extinguisher_code'],
            'asset_code' => $params['asset_code'],
            'type' => $params['type'],
            'longitude' => $params['longitude'],
            'latitude' => $params['latitude'],
            'coordinate' => $params['coordinate'],
            'weight' => $params['weight'],
            'photo_url' => $params['photo_url'],
            'operator_id' => $staff_info_id,
        ];
        //组装灭火器与管理员关系数据，灭火器任务与管理员关联数据
        $extinguisher_staff_relation = $extinguisher_task_staff_relation = [];
        foreach($params['admin_ids'] as $key=>$val){
            $extinguisher_task_staff_relation[$key]['staff_id'] = $extinguisher_staff_relation[$key]['staff_id'] = $val;
        }
        //组装灭火器任务数据
        $extinguisher_task = [
            'store_id' => $params['store_id'],
            'extinguisher_code' => $params['extinguisher_code'],
            'type' => $params['type'],
            'extinguisher_base_json' => json_encode([
                'asset_code' => $params['asset_code'],
                'longitude' => $params['longitude'],
                'latitude' => $params['latitude'],
                'coordinate' => $params['coordinate'],
                'weight' => $params['weight'],
                'photo_url' => $params['photo_url']
            ]),
            'check_month' => date('Y-m'),    //gmdate('Y-m', time() + $add_hour  * 3600 ), //需要+时区偏移量
        ];

        try{

            //验证网点id是否存在
            $categore_ids = array_keys(ExtinguisherEnums::$extinguisher_store_category);
            if ($params['store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                $store_count = SysStoreModel::count([
                    'conditions' => 'id = :store_id: AND category IN ({category_ids:array})',
                    'bind'       => ['store_id' => $params['store_id'], 'category_ids' => $categore_ids],
                ]);
                if (!$store_count) {
                    throw new ValidationException(static::$t->_('not_found_the_store'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //校验数据是否存在（根据灭火器编码）
            $extinguisher = ExtinguisherBaseModel::count([
                'conditions' => 'extinguisher_code = :extinguisher_code:',
                'bind' => ['extinguisher_code' => $params['extinguisher_code'] ],
            ]);
            //预留入口，是否给用户返回指定信息
            if($extinguisher){
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('extinguisher_code_repeat'),   //需要替换翻译中心key
                    'data' => [
                        'item_error' => $item_error
                    ]
                ];
            }

            $db = $this->getDI()->get('db_backyard');
            $db->begin();

            //插入灭火器数据
            $result = $db->insertAsDict('extinguisher_base_info', $extinguisher_base_info);
            if($result){
                $extinguisher_info_id = $db->lastInsertId();

                //插入灭火器与管理员关系数据
                foreach($extinguisher_staff_relation as $key=>$val){
                    $extinguisher_staff_relation[$key]['extinguisher_info_id'] = $extinguisher_info_id;
                }
                $esr_model = new ExtinguisherStaffRelationModel();
                $esr_model->batch_insert_execute($extinguisher_staff_relation);

                //插入灭火器任务数据
                $extinguisher_task['extinguisher_info_id'] = $extinguisher_info_id;
                $db->insertAsDict('extinguisher_task', $extinguisher_task);
                $extinguisher_task_id = $db->lastInsertId();

                //插入灭火器任务与管理员关联数据
                foreach($extinguisher_task_staff_relation as $key=>$val){
                    $extinguisher_task_staff_relation[$key]['extinguisher_task_id'] = $extinguisher_task_id;
                }
                $etsr_model = new ExtinguisherTaskStaffRelationModel();
                $etsr_model->batch_insert_execute($extinguisher_task_staff_relation);

                //记录日志
                $log_data['extinguisher_id'] = $extinguisher_info_id;
                $log_data['content'] = $extinguisher_base_info;
                $log_data['content']['id'] = $extinguisher_info_id;
                $log_data['content']['admin_ids'] = implode(',', $params['admin_ids']);
                $log_data['content'] = json_encode($log_data['content']);
                $log_data['type'] = 1;
                $log_data['operator_id'] = $staff_info_id;
                $db->insertAsDict('extinguisher_operation_log', $log_data);
            }

            $db->commit();

            //发送站内信和push
            $this->sendMsg([$params['store_id']]);

        }catch(ValidationException $e){
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        }catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
            $message = static::$t->_('retry_later');
            $db->rollback();
        }

        //写入异常，记录log
        if (!empty($real_message)) {
            $this->logger->error('create-extinguisher-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'item_error' => $item_error
            ]
        ];
    }

    /**
     * @param $id
     * 获取单个灭火器详情
     */
    function getOneExtinguisher($id, $staff_info_id){
        $data = [];
        try{
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('eb.id, eb.store_id, eb.extinguisher_code, eb.asset_code, eb.type, eb.longitude, eb.latitude, eb.coordinate, eb.weight, eb.photo_url, ss.name store_name, ss.category, ss.sorting_no');
            $builder->from(['eb' => ExtinguisherBaseModel::class]);
            $builder->leftJoin(SysStoreModel::class, 'eb.store_id=ss.id', 'ss');
            $builder->where(
                "eb.deleted=0 AND eb.id = :id: ",
                ['id'=>$id]
            );
            $data = $builder->getQuery()->getSingleResult();
            if(!$data){
                return $data;
            }
            $data = $data->toArray();
            $data['type_name'] = isset($data['type']) && !empty($data['type']) ? static::$t->_('extinguisher_type_'.$data['type']) : '';
            $data['category_name'] = !empty($data['category']) ? ExtinguisherEnums::$extinguisher_store_category[$data['category']] : '';
            $data['areas'] = !empty($data['sorting_no']) ? ExtinguisherEnums::$sorting_no_map[$data['sorting_no']] : '';
            if ($data['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $data['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }
            //管理员信息
            if(!empty($data)){
                $builder2 = $this->modelsManager->createBuilder();
                $builder2->columns('esr.staff_id, hr.name');
                $builder2->from(['esr' => ExtinguisherStaffRelationModel::class]);
                $builder2->leftJoin(HrStaffInfoModel::class, 'esr.staff_id=hr.staff_info_id', 'hr');
                $builder2->where(
                    "esr.deleted=0 AND esr.extinguisher_info_id = :id: ",
                    [ 'id'=>$data['id'] ]
                );
                $data['admins'] = $builder2->getQuery()->execute()->toArray();
                if( !empty($data['admins']) ){
                    foreach($data['admins'] as $key=>$val ){
                        $data['admins'][$key]['name'] = $val['name'] . '(' . $val['staff_id'] . ')';
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('get-one-extinguisher-failed:' . $e->getMessage());
        }
        return $data;
    }

    /**
     * @param $id
     * 获取单个灭火器操作日志
     */
    function getOneExtinguisherLog($id){
        $list = $log_list = [];
        try{
            $list = ExtinguisherOperationLogModel::find([
                'conditions' => 'type=1 AND extinguisher_id = :extinguisher_id:',
                'bind' => ['extinguisher_id' => $id ],
                'order' => 'id DESC'
            ])->toArray();
            //处理列表
            if(!empty($list)){
                $admin_ids = [];
                foreach($list as $key=>$val){
                    $log_list[$key] = json_decode($val['content'], true);
                    $log_list[$key]['created_at'] = $val['created_at'];
                    $admin_ids = array_merge( $admin_ids, explode(',', $log_list[$key]['admin_ids']), [$log_list[$key]['operator_id']] );
                }

                //批量获取获取网点/区域信息
                $store_ids = array_values(array_unique(array_column($log_list, 'store_id')));
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('ss.id, ss.name store_name, ss.category, ss.sorting_no');
                $builder->from(['ss' => SysStoreModel::class]);
                $builder->where(
                    "ss.id IN ({store_ids:array})",
                    [ 'store_ids'=>$store_ids ]
                );
                $store_list = $builder->getQuery()->execute()->toArray();
                $store_list = array_merge($store_list, [[
                    'id' => (string) Enums::HEAD_OFFICE_STORE_FLAG,
                    'store_name' => Enums::PAYMENT_HEADER_STORE_NAME,
                    'category'   => '',
                    'sorting_no' => ''
                ]]);
                $store_list = array_column($store_list, null, 'id');
                //批量获取管理员信息
                $admin_ids = array_values(array_unique($admin_ids));
                $admin_list = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id IN ({staff_info_ids:array})',
                    'bind' => ['staff_info_ids' => $admin_ids ],
                    'columns' => ['staff_info_id, name']
                ])->toArray();
                $admin_list = array_column($admin_list, null, 'staff_info_id');
                if( !empty($admin_list) ){
                    foreach( $admin_list as $key=>$val){
                        $admin_list[$key]['name'] = $val['name'] . '(' . $val['staff_info_id'] . ')';
                    }
                }

                $add_hour = $this->config->application->add_hour;
                //组成数据
                foreach ($log_list as $l => $log) {
                    $log_list[$l]['operator_name'] = !empty($admin_list[$log['operator_id']]['name']) ? $admin_list[$log['operator_id']]['name'] : '';
                    $log_list[$l]['type_name']     = !empty($log['type']) ? static::$t->_('extinguisher_type_' . $log['type']) : '';
                    $log_list[$l]['store_name']    = !empty($store_list[$log['store_id']]['store_name']) ? $store_list[$log['store_id']]['store_name'] : '';
                    $log_list[$l]['category_name'] = !empty($store_list[$log['store_id']]['category']) ? ExtinguisherEnums::$extinguisher_store_category[$store_list[$log['store_id']]['category']] : '';
                    $log_list[$l]['areas']         = !empty($store_list[$log['store_id']]['sorting_no']) ? ExtinguisherEnums::$sorting_no_map[$store_list[$log['store_id']]['sorting_no']] : '';
                    $log_list[$l]['created_at']    = date('Y-m-d H:i:s', strtotime($log['created_at']) + $add_hour * 3600);
                    $log_admin_ids                 = [];
                    $log_admin_ids                 = explode(',', $log['admin_ids']);
                    foreach ($log_admin_ids as $ad => $aid) {
                        $log_list[$l]['admins'][] = $admin_list[$aid];
                    }
                }
            }
        }catch (\Exception $e){
            $this->logger->error('get-one-extinguisher-log-failed:' . $e->getMessage());
        }

        return $log_list;
    }

    /**
     * @param $params
     * 获取灭火器列表
     */
    function getExtinguisherList($params, $staff_info_id, $is_download = false){
        $data = [];

        $page = intval($params['page']);
        $size = intval($params['size']);
        $offset = $size * ($page - 1);

        $data['total'] = 0;
        $data['list'] = [];
        $data['current_page'] = $page;
        $data['per_page'] = $size;

        try{

            //获取当前登陆用户所在网点，并判定所能查询的网点：超管/总部员工能看所有网点数据，其他用户只能看自己所在网点数据
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => ['staff_info_id' => $staff_info_id ],
                'columns' => [ 'sys_store_id' ]
            ]);
            if(!$staff_info){
                throw new ValidationException('not found the staff');
            }
            $staff_info = $staff_info->toArray();
            if( $staff_info['sys_store_id']!=-1 && $staff_info_id!=10000 ){
                if( !empty($params['store_id']) && $params['store_id']!=$staff_info['sys_store_id'] ){
                    return $data;
                }
                $params['store_id'] = $staff_info['sys_store_id'];
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('eb.id, eb.store_id, eb.extinguisher_code, eb.asset_code, eb.type, eb.longitude, eb.latitude, eb.coordinate, eb.weight, eb.photo_url, ss.name store_name, ss.category, ss.sorting_no');
            $builder->from(['eb' => ExtinguisherBaseModel::class]);
            $builder->leftJoin(SysStoreModel::class, 'eb.store_id=ss.id', 'ss');
            if( isset($params['admin_id']) && !empty($params['admin_id']) ) {
                $builder->leftJoin(ExtinguisherStaffRelationModel::class, 'eb.id=esr.extinguisher_info_id', 'esr');
            }
            $builder->where( "eb.deleted=0 " );
            //灭火器类型
            if( isset($params['extinguisher_type']) && !empty($params['extinguisher_type']) ){
                $builder->andWhere('eb.type = :type:', [ 'type' => $params['extinguisher_type'] ]);
            }
            //灭火器管理员
            if( isset($params['admin_id']) && !empty($params['admin_id']) ){
                $builder->andWhere('esr.deleted=0 AND esr.staff_id = :staff_id:', [ 'staff_id' => $params['admin_id'] ]);
            }
            //网点id
            if( isset($params['store_id']) && !empty($params['store_id']) ){
                $builder->andWhere('eb.store_id = :store_id:', [ 'store_id' => $params['store_id'] ]);
            }
            //灭火器编码
            if( isset($params['extinguisher_code']) && !empty($params['extinguisher_code']) ){
                $builder->andWhere('eb.extinguisher_code LIKE :extinguisher_code:', [ 'extinguisher_code' => "%{$params['extinguisher_code']}%"]);
            }
            //网点类型
            if( isset($params['store_category']) && !empty($params['store_category']) ){
                $builder->andWhere('ss.category IN ({category_ids:array})', [ 'category_ids' => $params['store_category'] ]);
            }
            //获取灭火器数量
            $data['total']  = $builder->getQuery()->execute()->count();

            //获取列表数据
            if(!$is_download){
                $builder->limit($size, $offset);
            }
            $builder->orderBy('eb.id DESC');
            $data['list'] = $builder->getQuery()->execute()->toArray();

            //获取灭火器管理员信息
            if(!empty($data['list'])){
                $extinguisher_ids = array_column($data['list'], 'id');
                $builder2 = $this->modelsManager->createBuilder();
                $builder2->columns('esr.extinguisher_info_id, esr.staff_id, hr.name');
                $builder2->from(['esr' => ExtinguisherStaffRelationModel::class]);
                $builder2->leftJoin(HrStaffInfoModel::class, 'esr.staff_id=hr.staff_info_id', 'hr');
                $builder2->where(
                    " esr.deleted=0 AND esr.extinguisher_info_id IN ({extinguisher_ids:array}) ",
                    [ 'extinguisher_ids'=>$extinguisher_ids ]
                );
                $admin_list = $builder2->getQuery()->execute()->toArray();
                foreach ($data['list'] as $key => $val) {
                    if ($val['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                        $data['list'][$key]['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
                    }
                    $data['list'][$key]['type_name']     = !empty($val['type']) ? static::$t->_('extinguisher_type_' . $val['type']) : '';
                    $data['list'][$key]['category_name'] = !empty($val['category']) ? ExtinguisherEnums::$extinguisher_store_category[$val['category']] : '';
                    $data['list'][$key]['areas']         = !empty($val['sorting_no']) ? ExtinguisherEnums::$sorting_no_map[$val['sorting_no']] : '';
                    $data['list'][$key]['gps_position']  = $val['longitude'] . ';' . $val['latitude'];
                    foreach ($admin_list as $admin) {
                        if ($val['id'] == $admin['extinguisher_info_id']) {
                            $admin['name']                  = $admin['name'] . '(' . $admin['staff_id'] . ')';
                            $data['list'][$key]['admins'][] = $admin;
                        }
                    }
                }
            }
        } catch (ValidationException $e) {
            $this->logger->warning('get-extinguisher-list-failed:' . $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('get-extinguisher-list-failed:' . $e->getMessage());
        }

        return $data;
    }


    /**
     * @param $params
     * 编辑单个灭火器
     */
    function editExtinguisher($params, $staff_info_id){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $item_error = [];

        //获取灭火器详情数据
        $old_extingvisher_info = ExtinguisherBaseModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $params['id'] ]
        ]);
        if($old_extingvisher_info){
            $old_extingvisher_info = $old_extingvisher_info->toArray();
        }
        //如果没有数据，返回false
        if(empty($old_extingvisher_info)){
            throw new ValidationException('not found the Extinguisher');
        }

        //组装灭火器数据
        $new_extinguisher_info = [
            'store_id' => $params['store_id'],
            //'extinguisher_code' => $params['extinguisher_code'],  //不更改灭火器编码，一旦生成不可更改
            'asset_code' => $params['asset_code'],
            'type' => $params['type'],
            'longitude' => $params['longitude'],
            'latitude' => $params['latitude'],
            'coordinate' => $params['coordinate'],
            'weight' => $params['weight'],
            'photo_url' => $params['photo_url'],
            'operator_id' => $staff_info_id,
        ];
        //组装灭火器与管理员关系数据
        $new_extinguisher_staff_relation = [];
        foreach($params['admin_ids'] as $key=>$val){
            $new_extinguisher_staff_relation[$key]['staff_id'] = $val;
            $new_extinguisher_staff_relation[$key]['extinguisher_info_id'] = $params['id'];
        }

        //更新数据
        try{
            $db = $this->getDI()->get('db_backyard');
            $db->begin();

            //获取灭火器管理员信息
            $admin_list = ExtinguisherStaffRelationModel::find([
                'conditions' => 'deleted=0 AND extinguisher_info_id=:id:',
                'bind' => ['id' => $params['id'] ],
                'columns' => ['staff_id']
            ])->toArray();
            $admin_ids = array_column($admin_list, 'staff_id');

            //更新灭火器信息
            $db->updateAsDict('extinguisher_base_info',$new_extinguisher_info, 'id = '.$params['id']);

            //更新灭火器管理员信息
            $db->updateAsDict('extinguisher_staff_relation',['deleted'=>1], 'extinguisher_info_id = '.$params['id'].' AND deleted=0');
            $model = new ExtinguisherStaffRelationModel();
            $model->batch_insert_execute($new_extinguisher_staff_relation);

            //记录灭火器信息日志
            $extinguisher_operation_log = [
                'extinguisher_id' => $params['id'],
                'type' => 1,
                'operator_id' => $staff_info_id,
            ];
            $extinguisher_operation_log['content'] = $new_extinguisher_info;
            $extinguisher_operation_log['content']['id'] = $params['id'];
            $extinguisher_operation_log['content']['extinguisher_code'] = $old_extingvisher_info['extinguisher_code'];  //灭火器编码
            $extinguisher_operation_log['content']['admin_ids'] = implode(',', $params['admin_ids']);
            $extinguisher_operation_log['content'] = json_encode($extinguisher_operation_log['content']);
            $db->insertAsDict('extinguisher_operation_log', $extinguisher_operation_log);

            //已检查任务，不更新灭火器任务信息和管理员关系信息，backyard提交检查时更新
            //$add_hour = $this->config->application->add_hour;
            $check_month = date('Y-m');  //gmdate('Y-m', time() + $add_hour  * 3600 ); //需要+时区偏移量
            $task = ExtinguisherTaskModel::findFirst([
                'conditions' => "extinguisher_info_id = :id: AND status=1 AND check_month='$check_month'",
                'bind' => ['id' => $params['id'] ]
            ]);
            if($task){
                $task = $task->toArray();
                //组装灭火器任务数据
                $extinguisher_task = [
                    'store_id' => $params['store_id'],
                    'type' => $params['type'],
                    'extinguisher_base_json' => json_encode([
                        'asset_code' => $params['asset_code'],
                        'longitude' => $params['longitude'],
                        'latitude' => $params['latitude'],
                        'coordinate' => $params['coordinate'],
                        'weight' => $params['weight'],
                        'photo_url' => $params['photo_url']
                    ]),
                ];
                $db->updateAsDict('extinguisher_task',$extinguisher_task, 'id = '.$task['id']);
                $db->updateAsDict('extinguisher_task_staff_relation',['deleted'=>1], 'extinguisher_task_id = '.$task['id'].' AND deleted=0');
                $new_extinguisher_task_staff_relation = [];
                foreach($params['admin_ids'] as $key=>$val){
                    $new_extinguisher_task_staff_relation[$key]['staff_id'] = $val;
                    $new_extinguisher_task_staff_relation[$key]['extinguisher_task_id'] = $task['id'];
                }
                $ts_model = new ExtinguisherTaskStaffRelationModel();
                $ts_model->batch_insert_execute($new_extinguisher_task_staff_relation);
            }


            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
            $db->rollback();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
            $message = static::$t->_('retry_later');
            $db->rollback();
        }

        //写入异常，记录log
        if (!empty($real_message)) {
            $this->logger->error('create-extinguisher-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'item_error' => $item_error
            ]
        ];
    }

    /**
     * @param $ids
     * 删除灭火器
     */
    function delExtinguisher($ids){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $item_error = [];

        try{

            //获取未检查的固化任务
            $task_list = ExtinguisherTaskModel::find([
                'conditions' => 'status=1 AND deleted=0 AND extinguisher_info_id IN ({extinguisher_ids:array})',
                'bind' => ['extinguisher_ids'=>$ids ],
                'columns' => [ 'id, extinguisher_info_id' ]
            ])->toArray();
            $task_list = array_column($task_list, null, 'extinguisher_info_id');

            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            foreach($ids as $id){
                $flag = $db->updateAsDict('extinguisher_base_info',['deleted'=>1], 'id = '.$id);
                //如果本周内的对应灭火器任务没有检查，则删除任务
                if($flag && isset($task_list[$id]['id'])){
                    $db->updateAsDict('extinguisher_task',['deleted'=>1], 'id = '.$task_list[$id]['id']);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
            $message = static::$t->_('retry_later');
            $db->rollback();
        }

        //写入异常，记录log
        if (!empty($real_message)) {
            $this->logger->error('del-extinguisher-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'item_error' => $item_error
            ]
        ];
    }


    /**
     * 上传灭火器数据批量处理
     */
    function uploadExtinguisher($excel_data, $staff_info_id){
        foreach($excel_data as $key=>$val){
            if( empty($val[0]) && empty($val[1]) && empty($val[2]) && empty($val[3]) && empty($val[4]) && empty($val[5]) && empty($val[6]) && empty($val[7]) && empty($val[8]) && empty($val[9]) && empty($val[10]) ){
                unset($excel_data[$key]);
            }
        }
        //输入行数限制
        if (count($excel_data) < self::$min_extinguisher_count) {
            return ['code' => 2, 'msg' => 'Excel error', 'data' => []];
        }
        if (count($excel_data) > self::$max_extinguisher_count) {
            return ['code' => 2, 'msg' => static::$t->_('extinguisher_upload_error_8'), 'data' => []];
        }

        //获取网点id列表
        $store_category_ids = array_keys(ExtinguisherEnums::$extinguisher_store_category);
        $store_names = array_values(array_unique(array_column($excel_data, 0)));
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ss.id, ss.name, ss.category, ss.sorting_no');
        $builder->from(['ss' => SysStoreModel::class]);
        $builder->where(
            " ss.name IN ({store_names:array}) AND ss.category IN ({store_category_ids:array}) ",
            [ 'store_names'=>$store_names, 'store_category_ids'=>$store_category_ids ]
        );
        $store_list = $builder->getQuery()->execute()->toArray();
        $store_list = array_merge($store_list, [
            [
                'id'         => (string)Enums::HEAD_OFFICE_STORE_FLAG,
                'name'       => Enums::PAYMENT_HEADER_STORE_NAME,
                'category'   => '',
                'sorting_no' => ''
            ]
        ]);
        $store_ids = array_column($store_list, null,'name');

        //获取管理员id列表
        $admin_tmp_ids = array_column($excel_data, 3);
        $admin_ids = [];
        foreach($admin_tmp_ids as $key=>$val){
            $admin_ids = array_values(array_unique(array_merge($admin_ids, explode(',', $val))));
        }
        $admin_list = HrStaffInfoModel::find([
            'conditions' => ' staff_info_id IN ({admin_ids:array})',
            'bind' => ['admin_ids'=>$admin_ids ],
            'columns' => [ 'staff_info_id,name' ]
        ])->toArray();
        $admin_list = array_column($admin_list, 'staff_info_id');

        //获取灭火器编码对应的表中灭火器数据
        $extinguisher_codes = array_values(array_unique(array_column($excel_data, 4)));
        $extinguisher_list = ExtinguisherBaseModel::find([
            'conditions' => ' extinguisher_code IN ({extinguisher_codes:array})',
            'bind' => ['extinguisher_codes'=>$extinguisher_codes ],
            'columns' => [ 'id,extinguisher_code' ]
        ])->toArray();
        $extinguisher_list = array_column($extinguisher_list, 'extinguisher_code');

        $result_success = $result_error = 0;
        $insert_data = $error_data = $insert_extinguisher_code = [];

        $extinguisher_type = [];
        foreach(ExtinguisherEnums::$extinguisher_type as $t=>$tv){
            $extinguisher_type[$tv] = static::$t->_('extinguisher_type_'.$tv);
        }

        //循环验证需要创建的数据，并生成要创建的和失败的数据
        foreach ($excel_data as $key => $val) {
            //验证是否必须
            $is_head_office = strtolower($val[0]) == strtolower(Enums::PAYMENT_HEADER_STORE_NAME);
            if ($is_head_office) {
                if (empty($val[0]) || empty($val[3]) || empty($val[4]) || empty($val[5]) || empty($val[7]) || empty($val[8]) || empty($val[10])) {
                    $error_data[] = [
                        $val[4],
                        static::$t->_('extinguisher_upload_error_1'),
                    ];
                    continue;
                }
                $val[0] = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                if (empty($val[0]) || empty($val[1]) || empty($val[2]) || empty($val[3]) || empty($val[4]) || empty($val[5]) || empty($val[7]) || empty($val[8]) || empty($val[10])) {
                    $error_data[] = [
                        $val[4],
                        static::$t->_('extinguisher_upload_error_1'),
                    ];
                    continue;
                }

            }

            //验证网点名称是否存在
            if (!isset($store_ids[$val[0]])) {
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_2'),
                ];
                continue;
            }
            if (!$is_head_office) {
                //验证区域是否正确
                if (!in_array(strtoupper($val[1]), array_keys(ExtinguisherEnums::$sorting_no_map)) || strtoupper($val[1]) != $store_ids[$val[0]]['sorting_no']) {
                    $error_data[] = [
                        $val[4],
                        static::$t->_('extinguisher_upload_error_9'),
                    ];
                    continue;
                }
                //验证网点类型是否正确
                if (!in_array(strtoupper($val[2]), ExtinguisherEnums::$extinguisher_store_category) || strtoupper($val[2]) != ExtinguisherEnums::$extinguisher_store_category[$store_ids[$val[0]]['category']]) {
                    $error_data[] = [
                        $val[4],
                        static::$t->_('extinguisher_upload_error_10'),
                    ];
                    continue;
                }
            }

            //验证管理员是否存在
            $is_continue = 0;
            $admin_val = [];
            $admin_val = explode(',', $val[3]);
            foreach($admin_val as $key=>$admin) {
                if (!in_array($admin, $admin_list)) {
                    $is_continue = 1;
                    $error_data[] = [
                        $val[4],
                        static::$t->_('extinguisher_upload_error_3'),
                    ];
                    break;
                }
            }
            if($is_continue){
                continue;
            }
            //灭火器编码合格校验
            if( strlen($val[4])>20 || !preg_match("/^[A-Za-z\d\-]*$/i",$val[4]) ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_7'),
                ];
                continue;
            }
            //灭火器类型
            if( !array_search($val[5],$extinguisher_type) ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_4'),
                ];
                continue;
            }
            //灭火器编码是否重复
            if( in_array($val[4],$insert_extinguisher_code) ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_5'),
                ];
                continue;
            }
            //灭火器编码已经在库中存在
            if( in_array($val[4],$extinguisher_list) ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_6'),
                ];
                continue;
            }
            //灭火器资产编码
            if( !empty($val[6]) && strlen($val[6])>200 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_15'),
                ];
                continue;
            }
            //灭火器位置长度验证
            if( strlen($val[7])<=0 || strlen($val[7])>200 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_11'),
                ];
                continue;
            }
            //经纬度
            $local = [];
            $local = explode(';', $val[8]);
            if( (empty($local[0]) && $local[0] !== '0') || !isset($local[1]) || (empty($local[1]) && $local[1] !== '0')) {
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_16'),
                ];
                continue;
            }
            if( $local[0]<-180 || $local[0]>180 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_12'),
                ];
                continue;
            }
            if( $local[1]<-90 || $local[1]>90 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_13'),
                ];
                continue;
            }
            if( strpos($local[0],'.') && strlen(substr( $local[0],strpos($local[0],'.')+1))>8 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_12'),
                ];
                continue;
            }
            if( strpos($local[1],'.') && strlen(substr( $local[1],strpos($local[1],'.')+1))>8 ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_13'),
                ];
                continue;
            }

            //灭火器压力
            if( !empty($val[9]) && (!is_numeric($val[9]) || strlen($val[9])>20) ){
                $error_data[] = [
                    $val[4],
                    static::$t->_('extinguisher_upload_error_14'),
                ];
                continue;
            }
            //组成insert 数据
            $insert_data[] = [
                'store_id' => $store_ids[$val[0]]['id'],
                'extinguisher_code' => $val[4],
                'asset_code' => $val[6],
                'type' => array_search($val[5], $extinguisher_type),
                'longitude' => $local[0],
                'latitude' => $local[1],
                'coordinate' => $val[7],
                'weight' => $val[9],
                'photo_url' => $val[10],
                'operator_id' => $staff_info_id,
            ];
            $insert_admin_data[$val[4]] = $val[3];  //管理员信息
            $insert_extinguisher_code[] = $val[4];  //用于去重，灭火器编码
        }
        //$add_hour = $this->config->application->add_hour;
        $db = $this->getDI()->get('db_backyard');
        $esr_model = new ExtinguisherStaffRelationModel();
        $etsr_model = new ExtinguisherTaskStaffRelationModel();
        //循环插入数据（因为需要插入关联表）
        foreach($insert_data as $key=>$val){

            $admins = $extinguisher_staff_relation = $extinguisher_task_staff_relation = $extinguisher_task = $log_data = [];

            //灭火器管理员关联关系数据
            $admins = explode(',', $insert_admin_data[$val['extinguisher_code']] );
            foreach($admins as $k=>$v){
                $extinguisher_task_staff_relation[$k]['staff_id'] = $extinguisher_staff_relation[$k]['staff_id'] = $v;
            }


            //组装灭火器任务数据
            $extinguisher_task = [
                'store_id' => $val['store_id'],
                'extinguisher_code' => $val['extinguisher_code'],
                'type' => $val['type'],
                'extinguisher_base_json' => json_encode([
                    'asset_code' => $val['asset_code'],
                    'longitude' => $val['longitude'],
                    'latitude' => $val['latitude'],
                    'coordinate' => $val['coordinate'],
                    'weight' => $val['weight'],
                    'photo_url' => $val['photo_url']
                ]),
                'check_month' => date('Y-m'),    //gmdate('Y-m', time() + $add_hour  * 3600 ), //需要+时区偏移量
            ];

            try{
                $db->begin();

                //插入灭火器数据
                $result = $db->insertAsDict('extinguisher_base_info', $val);
                if($result){
                    $extinguisher_info_id = $db->lastInsertId();

                    //插入灭火器与管理员关系数据
                    foreach($extinguisher_staff_relation as $k=>$v){
                        $extinguisher_staff_relation[$k]['extinguisher_info_id'] = $extinguisher_info_id;
                    }
                    $esr_model->batch_insert_execute($extinguisher_staff_relation);

                    //记录日志
                    $log_data['extinguisher_id'] = $extinguisher_info_id;
                    $log_data['content'] = $val;
                    $log_data['content']['id'] = $extinguisher_info_id;
                    $log_data['content']['admin_ids'] = $insert_admin_data[$val['extinguisher_code']];
                    $log_data['content'] = json_encode($log_data['content']);
                    $log_data['type'] = 1;
                    $log_data['operator_id'] = $staff_info_id;
                    $db->insertAsDict('extinguisher_operation_log', $log_data);


                    //插入灭火器任务数据
                    $extinguisher_task['extinguisher_info_id'] = $extinguisher_info_id;
                    $db->insertAsDict('extinguisher_task', $extinguisher_task);
                    $extinguisher_task_id = $db->lastInsertId();

                    //插入灭火器任务与管理员关联数据
                    foreach($extinguisher_task_staff_relation as $k=>$v){
                        $extinguisher_task_staff_relation[$k]['extinguisher_task_id'] = $extinguisher_task_id;
                    }
                    $etsr_model->batch_insert_execute($extinguisher_task_staff_relation);
                }

                $db->commit();
            } catch (\Exception $e) {
                $db->rollback();
            }

        }

        //生成创建灭火器失败文件xlsx并上传
        if(!empty($error_data)){
            //$add_hour = $this->config->application->add_hour;
            $upload_key = 'th_error_extinguisher_' . date('Y-m-d H:i:s');   //gmdate('Y-m-d H:i:s', time()+$add_hour*3600);
            $excel_header = [
                static::$t->_('extinguisher_code'),
                static::$t->_('extinguisher_upload_error_msg')
            ];
            $file_result = self::exportExcel($excel_header, $error_data, $upload_key . '.xlsx');
        }

        //发送站内信和push消息
        if(!empty($insert_data)){
            $success_store_id = array_values(array_unique(array_column($insert_data, 'store_id')));
            $this->sendMsg($success_store_id);
        }

        $result_data = [
            'success_data_count' => count($insert_data), //校验成功条数
            'error_data_count' => count($error_data), //校验失败条数
            'data_count' => count($excel_data), //总条数
            'result_file' => $file_result['data'] ?? '', //结果详情文件路径
            'cache_excel_success_key' => $upload_key ?? '' //数据缓存key
        ];

        return [
            'code' => 1,
            'msg' => '',
            'data' => $result_data
        ];

    }


    /**
     * @param $params
     * @param $staff_info_id
     * @return array
     * @throws \App\Library\Exception\BusinessException
     * 下载导出excel
     */
    function dataExport($params, $staff_info_id){
        //获取条件数据
        $data = $this->getExtinguisherList($params, $staff_info_id, true);

        $header = [
            static::$t->_('extinguisher_store_name'), //网点名称
            static::$t->_('extinguisher_areas'),    //区域
            static::$t->_('extinguisher_store_type'),   //网点类型
            static::$t->_('extinguisher_admins'),   //管理员
            static::$t->_('extinguisher_code'), //灭火器编码
            static::$t->_('extinguisher_type'), //灭火器类型
            static::$t->_('extinguisher_asset_code'),   //灭火器资产编码
            static::$t->_('extinguisher_coordinate'), //灭火器位置
            static::$t->_('extinguisher_gps'), //灭火器GPS
            static::$t->_('extinguisher_weight'), //灭火器重量
            static::$t->_('extinguisher_photo_url'), //灭火器照片
        ];

        $new_data = [];
        if(!empty($data['list'])){
            foreach($data['list'] as $key=>$val){
                $admins = [];
                foreach($val['admins'] as $k=>$v){
                    $admins[] = $v['name'];
                }
                $admins = implode(',', $admins);
                $new_data[] = [
                    $val['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $val['store_name'],
                    $val['areas'],
                    $val['category_name'],
                    $admins,
                    $val['extinguisher_code'],
                    $val['type_name'],
                    $val['asset_code'],
                    $val['coordinate'],
                    $val['longitude'].','.$val['latitude'],
                    $val['weight'],
                    $val['photo_url'],
                ];
            }
        }
        $file_name = 'extinguisher_management_' . date('Y-m-d H:i:s');
        $result = $this->exportExcel($header, $new_data, $file_name);

        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }

        return $result;

    }

    /**
     * 获取 部门+角色+网点 员工id
     */
    function getStaffIdsByDepartmentRoleStore(array $department_arr, $role_arr, $store_arr){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('hsi.staff_info_id');
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->leftJoin(hrStaffInfoPositionModel::class,'hsi.staff_info_id = hsip.staff_info_id', 'hsip');
        $builder->where(
            'hsi.state=1 AND hsi.formal=1 AND hsi.is_sub_staff=0 AND hsi.sys_store_id IN ({sys_store_ids:array}) AND hsip.position_category IN ({position_categorys:array}) AND hsi.sys_department_id IN ({sys_department_ids:array})',
            [
                'sys_store_ids'      => $store_arr,
                'position_categorys' => $role_arr,
                'sys_department_ids' => $department_arr
            ]
        );
        $staff_list = $builder->getQuery()->execute()->toArray();
        $staff_list = array_column($staff_list, 'staff_info_id');
        return array_values(array_unique($staff_list));
    }

    /**
     * 调用push接口发送站内信和消息
     */
    function sendMsg($store_id){
        try{
            $push_staff_ids = $this->getStaffIdsByDepartmentRoleStore(self::$department_arr, self::$position_arr, $store_id);
            if(!empty($push_staff_ids)){
                PushService::getInstance()->sendMessage($push_staff_ids);   //站内信
                PushService::getInstance()->sendPush($push_staff_ids);  //消息
            }
        } catch (\Exception $e) {
            $this->logger->error('push-extinguisher_msg-failed:' . $e->getMessage());
        }
    }




}
