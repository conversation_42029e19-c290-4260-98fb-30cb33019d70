<?php
/**
 * Author: Bruce
 * Date  : 2021-11-21 22:29
 * Description:
 */

namespace app\modules\Extinguisher\services;

use App\Library\Enums;
use App\Library\Enums\ExtinguisherEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Extinguisher\Models\ExtinguisherCheckListModel;
use App\Modules\Extinguisher\Models\ExtinguisherOperationLogModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskFileModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskModel;
use App\Modules\Extinguisher\Models\ExtinguisherTaskStaffRelationModel;
use App\Modules\Extinguisher\Models\HrStaffInfoModel;
use App\Modules\Extinguisher\Models\SysStoreModel;

class ExtinguisherTaskService extends BaseService
{

    private static $instance;
    public $timezone;

    private function __construct()
    {
        $this->timezone = env('timeZone', '+07:00');
    }

    /**
     * @return ExtinguisherTaskService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //任务列表参数校验
    public static $task_list_search_params = [
        'store_category_ids',
        'check_status',
        'check_result',
        'extinguisher_type',
        'check_month_start',
        'check_month_end',
        'manager_name',
        'store_id',
        'page',
        'page_size',
    ];
    public static $validate_task_list_search_params = [
        'page' => 'IntGt:0',
        'page_size' => 'IntGt:0',
    ];

    public static $task_detail_params = [
        'task_id'
    ];

    public static $validate_task_detail_params = [
        'task_id' => 'IntGt:0'
    ];

    public static $task_check_result_params = [
        'task_id'
    ];
    public static $validate_task_check_result_params = [
        'task_id' => 'IntGt:0'
    ];

    public static $check_log_list_params = [
        'task_id'
    ];
    public static $validate_check_log_list_params = [
        'task_id' => 'IntGt:0'
    ];

    public static $check_delete_task_params = [
        'task_id'
    ];
    public static $validate_delete_task_params = [
        'task_id' => 'IntGt:0'
    ];

    /**
     * 获取灭火器任务列表
     * @param $params
     * @param $staff_info_id
     * @return mixed
     */
    public function getExtinguisherTaskListInfo($params, $staff_info_id)
    {
        $params['page'] = empty($params['page']) ? 1 : $params['page'];
        $params['page_size'] = empty($params['page_size']) ? 20 : $params['page_size'];
        $params['page_size'] = ($params['page_size'] > 1000) ? 1000 : $params['page_size'];

        $staff_info = $this->getStaffInfo($staff_info_id);
        if(!$staff_info) {
            return ['count' => 0, 'list' => []];
        }
        if(!empty($staff_info['sys_store_id']) && $staff_info['sys_store_id'] != -1){//网点下的员工只能看自己网点下任务信息
            if(!empty($params['store_id']) && $params['store_id'] != $staff_info['sys_store_id']){
                return ['count' => 0, 'list' => []];
            }
            $params['store_id'] = $staff_info['sys_store_id'];
        }
        $data = $this->getTaskInfoQuery($params);
        if(!empty($data['list'])){
            $extinguisher_ids = array_column($data['list'], 'id');
            $data = $this->formatList($data, $extinguisher_ids);
        }
        return $data;
    }

    /**
     * 查询当前登录人有无网点信息
     * @param $staff_info_id
     * @return mixed
     */
    public function getStaffInfo($staff_info_id)
    {
        $staff_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind' => ['staff_info_id' => $staff_info_id ],
            'columns' => [ 'sys_store_id' ]
        ]);

        return empty($staff_info) ? [] : $staff_info->toArray();
    }

    /**
     * 查询任务信息query
     * @param $params
     * @param bool $is_export
     * @return mixed
     */
    public function getTaskInfoQuery($params, $is_export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('et.id, et.store_id, et.extinguisher_code, et.extinguisher_base_json, et.type, et.check_month, et.status as check_status, et.check_time, et.check_result, et.remark,ss.name store_name, ss.category, ss.sorting_no');
        $builder->from(['et' => ExtinguisherTaskModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'et.store_id=ss.id', 'ss');
        $builder = $this->getBuilderWhere($builder, $params);
        //如果导出，去除统计和分页。
        if(!$is_export) {
            //获取灭火器任务数量
            $data['count']  = $builder->getQuery()->execute()->count();
            $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        }
        $builder->orderBy('et.id desc');
        $data['list'] = $builder->getQuery()->execute()->toArray();
        return $data;
    }

    /**
     * 查询条件
     * @param $builder
     * @param $params
     * @return mixed
     */
    public function getBuilderWhere($builder, $params)
    {
        if( isset($params['admin_id']) && !empty($params['admin_id']) ) {
            $builder->leftJoin(ExtinguisherTaskStaffRelationModel::class, 'et.id=ets.extinguisher_task_id', 'ets');
        }
        $builder->where( "et.deleted=0 " );
        //网点类型
        if(!empty($params['store_category_ids']) ){
            $builder->andWhere('ss.category IN ({category_ids:array})', [ 'category_ids' => $params['store_category_ids'] ]);
        }
        //灭火器类型
        if(!empty($params['extinguisher_type']) ){
            $builder->andWhere('et.type = :type:', [ 'type' => $params['extinguisher_type'] ]);
        }
        //灭火器管理员
        if(!empty($params['admin_id']) ){
            $builder->andWhere('ets.deleted=0 AND ets.staff_id = :staff_id:', [ 'staff_id' => $params['admin_id'] ]);
        }
        //网点id
        if(!empty($params['store_id']) ){
            $builder->andWhere('et.store_id = :store_id:', [ 'store_id' => $params['store_id'] ]);
        }
        //灭火器编码
        if(!empty($params['extinguisher_code']) ){
            $builder->andWhere('et.extinguisher_code LIKE :extinguisher_code:', [ 'extinguisher_code' => "%{$params['extinguisher_code']}%"]);
        }
        //检查状态
        if(!empty($params['check_status']) ){
            $builder->andWhere('et.status  = :check_status:', [ 'check_status' => $params['check_status'] ]);
        }
        //检查结果
        if(!empty($params['check_result']) ){
            $builder->andWhere('et.check_result  = :check_result:', [ 'check_result' => $params['check_result'] ]);
        }
        //检查周期
        if(!empty($params['check_month_start']) && !empty($params['check_month_end'])){
            $builder->betweenWhere('et.check_month', $params['check_month_start'], $params['check_month_end']);
        }
        return $builder;
    }

    /**
     * 导出数据
     * @param $params
     * @param $staff_info_id
     * @return array
     */
    public function exportExtinguisherTaskListInfo($params, $staff_info_id)
    {
        try {
            if(empty($params['check_month_start']) || empty($params['check_month_end'])){
                throw new ValidationException(self::$t->_('extinguisher_task_export_month_limit_validate'));
            }
            $staff_info = $this->getStaffInfo($staff_info_id);
            if(!empty($staff_info['sys_store_id']) && $staff_info['sys_store_id'] != -1){
                $params['store_id'] = $staff_info['sys_store_id'];
            }
            //检验，导出时间范围3个月
            if(!empty($params['check_month_start']) && !empty($params['check_month_end'])) {
                if($params['check_month_end'] < $params['check_month_start']){
                    throw new ValidationException('check_month_param_error');
                }
                $endMonth = strtotime('+2 month', strtotime($params['check_month_start']));
                if(strtotime($params['check_month_end']) > $endMonth) {
                    throw new ValidationException(self::$t->_('extinguisher_task_export_month_limit'));
                }
            }
            $data = $this->getTaskInfoQuery($params, true);

            $config = $this->getDI()->get('config');
            $img_prefix = $config->application->img_prefix ?? '';
            //获取灭火器管理员信息
            $result = [];
            if(!empty($data['list'])){
                $extinguisher_ids = array_column($data['list'], 'id');
                $data = $this->formatList($data, $extinguisher_ids);
                //查询任务检查单列表
                $checkListInfo = $this->getTaskCheckListByIds($extinguisher_ids);
                $checkFileListInfo = $this->getTaskFileInfoByIds($extinguisher_ids);
                $newCheckInfo = [];
                foreach ($checkListInfo as $oneCheckInfo) {
                    $newCheckInfo[$oneCheckInfo['extinguisher_task_id']][$oneCheckInfo['problem_key']]['content'] = $oneCheckInfo['content'];
                    $newCheckInfo[$oneCheckInfo['extinguisher_task_id']][$oneCheckInfo['problem_key']]['type'] = $oneCheckInfo['type'];
                }
                $checkPicUrl = [];
                $signUrl = [];
                foreach ($checkFileListInfo as $oneFileList) {
                    if ($oneFileList['type'] == 1) {
                        $checkPicUrl[$oneFileList['extinguisher_task_id']][] = $img_prefix . $oneFileList['object_key'];
                    } elseif ($oneFileList['type'] == 2) {
                        $signUrl[$oneFileList['extinguisher_task_id']] = $img_prefix . $oneFileList['object_key'];
                    }
                }

                //将检查照片地址按逗号隔开
                foreach ($checkPicUrl as $key => $one) {
                    $checkPicUrl[$key] = implode("\r\n", $one);
                }

                foreach ($data['list'] as $oneKey => $oneTask) {
                    if(isset($newCheckInfo[$oneTask['id']])) {
                        foreach ($newCheckInfo[$oneTask['id']] as $key => $one) {
                            $content = $newCheckInfo[$oneTask['id']][$key]['content'];
                            if($newCheckInfo[$oneTask['id']][$key]['type'] == 1) {
                                $data['list'][$oneKey][$key] = $content == 1 ? static::$t->_('extinguisher_task_whether_1') : static::$t->_('extinguisher_task_whether2');
                            }else{
                                $data['list'][$oneKey][$key] = $content;
                            }
                        }
                    }else {
                        foreach (ExtinguisherEnums::$extinguisher_check_list as $one) {
                            $data['list'][$oneKey][$one] = '';
                        }
                    }
                    $data['list'][$oneKey]['check_pic_url'] = $checkPicUrl[$oneTask['id']] ?? '';
                    $data['list'][$oneKey]['sign_url'] = $signUrl[$oneTask['id']] ?? '';
                }
            }
            $result = $this->exportFormatData($data['list'], $staff_info_id);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $logger = $this->getDI()->get('logger');
            $logger->warning('灭火器检查任务-导出数据异常:' . $message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 格式化列表数据
     * @param $data
     * @param $extinguisher_ids
     * @return mixed
     */
    public function formatList($data, $extinguisher_ids)
    {
        $newAdminsInfoEnd = $this->getExtinguisherManagerInfo($extinguisher_ids);
        foreach ($data['list'] as $key => $val) {
            if ($val['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $data['list'][$key]['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }
            $data['list'][$key]['region'] = isset($val['sorting_no']) && !empty($val['sorting_no']) ? ExtinguisherEnums::$sorting_no_map[$val['sorting_no']] : '';
            $data['list'][$key]['extinguisher_type'] = isset($val['type']) && !empty($val['type']) ? static::$t->_('extinguisher_type_'.$val['type']) : '';
            $data['list'][$key]['store_type'] = isset($val['category']) && !empty($val['category']) ? ExtinguisherEnums::$extinguisher_store_category[$val['category']] : '';
            $data['list'][$key]['manager_name'] = $newAdminsInfoEnd[$val['id']] ?? '';

            $baseJson  = json_decode($val['extinguisher_base_json'],true);
            $data['list'][$key]['extinguisher_asset_code'] = $baseJson['asset_code'];
            $data['list'][$key]['coordinate'] = $baseJson['coordinate'];
            $data['list'][$key]['gps_position'] = $baseJson['longitude'] . ';' . $baseJson['latitude'];
            $data['list'][$key]['weight'] = $baseJson['weight'];
            $data['list'][$key]['photo_url'] = $baseJson['photo_url'];
            $data['list'][$key]['check_time'] = $val['check_time'] ? date('Y-m-d', strtotime($val['check_time'])) : '-';
            $data['list'][$key]['check_status'] = static::$t->_('extinguisher_checkout_status_' . $val['check_status']);
            $data['list'][$key]['check_result'] = $val['check_result'] ? static::$t->_('extinguisher_checkout_result_' . $val['check_result']) : '-';
            unset($data['list'][$key]['extinguisher_base_json']);
        }

        return $data;
    }

    /**
     * 获取灭火器管理员信息
     * @param $extinguisher_ids
     * @return array
     */
    public function getExtinguisherManagerInfo($extinguisher_ids)
    {
        if(!$extinguisher_ids) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('etsr.extinguisher_task_id, etsr.staff_id, hr.name');
        $builder->from(['etsr' => ExtinguisherTaskStaffRelationModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 'etsr.staff_id = hr.staff_info_id', 'hr');
        $builder->where(
            " etsr.deleted=0 AND etsr.extinguisher_task_id IN ({extinguisher_ids:array}) ",
            [ 'extinguisher_ids' => $extinguisher_ids ]
        );
        $admin_list = $builder->getQuery()->execute()->toArray();
        $newAdminsInfoEnd = [];
        if($admin_list){
            $newAdminsInfo = [];
            foreach($admin_list as $admin){
                $oneAdmin = $admin['name'] . '(' . $admin['staff_id'] . ')';
                $newAdminsInfo[$admin['extinguisher_task_id']][] = $oneAdmin;
            }
            foreach ($newAdminsInfo as $oneNewAdminKey => $oneNewAdmins) {
                $newAdminsInfoEnd[$oneNewAdminKey] = implode(';', $oneNewAdmins);
            }
        }
        return $newAdminsInfoEnd;
    }

    /**
     * 格式化导出数据
     * @param $data
     * @param $staffId
     * @return array
     * @throws \App\Library\Exception\BusinessException
     */
    public function exportFormatData($data, $staffId)
    {
        $header = [
            static::$t->_('extinguisher_store_name'), //网点名称
            static::$t->_('extinguisher_areas'),    //区域
            static::$t->_('extinguisher_store_type'),   //网点类型
            static::$t->_('extinguisher_admins'),   //灭火器管理员
            static::$t->_('extinguisher_check_month'), //灭火器检查周期
            static::$t->_('extinguisher_code'), //灭火器编码
            static::$t->_('extinguisher_type'), //灭火器类型
            static::$t->_('extinguisher_asset_code'),   //灭火器资产编码
            static::$t->_('extinguisher_coordinate'), //灭火器位置
            static::$t->_('extinguisher_gps'), //灭火器GPS
            static::$t->_('extinguisher_weight'), //灭火器压力
            static::$t->_('extinguisher_photo_url'), //灭火器照片
            static::$t->_('extinguisher_check_status'), //灭火器检查状态
            static::$t->_('extinguisher_check_time'), //灭火器任务实际检查日期
            static::$t->_('extinguisher_check_result'), //灭火器任务检查结果

            static::$t->_('extinguisher_problem_1'), //是否有通知标牌?
            static::$t->_('extinguisher_problem_2'), //消防气筒是否合格?
            static::$t->_('extinguisher_problem_3'), //压力强度是否合格?
            static::$t->_('extinguisher_problem_4'), //挤压棒是否合格?
            static::$t->_('extinguisher_problem_5'), //卡榫是否合格?
            static::$t->_('extinguisher_problem_6'), //绳子是否合格?
            static::$t->_('extinguisher_problem_7'), //是否没有障碍物?
            static::$t->_('extinguisher_problem_8'), //灭火器重量（kg）是多少?
            static::$t->_('extinguisher_check_pic'), //照片
            static::$t->_('extinguisher_autograph_pic'), //检查人签名图片
            static::$t->_('extinguisher_task_remark'), //备注
        ];
        $allRow = [];
        foreach ($data as $oneData) {
            $oneRow = [
                $oneData['store_name'],
                $oneData['region'],
                $oneData['store_type'],
                $oneData['manager_name'],
                $oneData['check_month'],
                $oneData['extinguisher_code'],
                $oneData['extinguisher_type'],
                $oneData['extinguisher_asset_code'],
                $oneData['coordinate'],
                $oneData['gps_position'],
                $oneData['weight'],
                $oneData['photo_url'],
                $oneData['check_status'],
                $oneData['check_time'],
                $oneData['check_result'],

                $oneData['extinguisher_problem_1'],
                $oneData['extinguisher_problem_2'],
                $oneData['extinguisher_problem_3'],
                $oneData['extinguisher_problem_4'],
                $oneData['extinguisher_problem_5'],
                $oneData['extinguisher_problem_6'],
                $oneData['extinguisher_problem_7'],
                $oneData['extinguisher_problem_8'],
                $oneData['check_pic_url'],
                $oneData['sign_url'],
                $oneData['remark']
            ];
            $allRow[] = $oneRow;
        }
        $file_name = "extinguisher_task_" . $staffId  . '_' . date('Y-m-d H:i:s',time());
        return $this->exportExcel($header, $allRow, $file_name);
    }

    /**
     * 获取任务详情
     * @param $params
     * @return mixed
     * @throws ValidationException
     */
    public function getExtinguisherTaskDetail($params)
    {
        //任务存存档中的灭火器信息
        $detailInfo = $this->getExtinguisherTaskDetailQuery($params);
        if(!$detailInfo){
            throw new ValidationException(self::$t->_('extinguisher_task_detail_not_found'));
        }
        //灭火器管理员信息
        $managerInfo = $this->getExtinguisherManagerInfo([$detailInfo['id']]);
        $baseJson = json_decode($detailInfo['extinguisher_base_json'],true);
        $result['store_name'] = $detailInfo['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_HEADER_STORE_NAME : $detailInfo['store_name'];
        $result['region'] = !empty($detailInfo['sorting_no']) ? ExtinguisherEnums::$sorting_no_map[$detailInfo['sorting_no']] : '-';
        $result['store_type'] = !empty($detailInfo['category']) ? ExtinguisherEnums::$extinguisher_store_category[$detailInfo['category']] : '-';
        $result['extinguisher_code'] = $detailInfo['extinguisher_code'] ?? '-';
        $result['extinguisher_type'] = !empty($detailInfo['type']) ? static::$t->_('extinguisher_type_'.$detailInfo['type']) : '';
        $result['extinguisher_asset_code'] = $baseJson['asset_code'] ?? '-';
        $result['coordinate'] = $baseJson['coordinate'] ?? '-';
        $result['longitude'] = $baseJson['longitude'] ?? '-';
        $result['latitude'] = $baseJson['latitude'] ?? '-';
        $result['weight'] = $baseJson['weight'] ?? '-';
        $result['photo_url'] = $baseJson['photo_url'] ?? '-';
        $result['manager_name'] = $managerInfo[$detailInfo['id']] ?? '-';

        return $result;
    }

    /**
     * 获取任务详情信息query
     * @param $where
     * @return array
     */
    public function getExtinguisherTaskDetailQuery($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('et.id, et.store_id, et.extinguisher_code, et.extinguisher_base_json, et.type, et.check_month, et.status as check_status, et.check_time, et.check_result, ss.name store_name, ss.category, ss.sorting_no');
        $builder->from(['et' => ExtinguisherTaskModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'et.store_id=ss.id', 'ss');
        $builder->where('et.id = :task_id:', ['task_id' => $where['task_id']]);
        $result = $builder->getQuery()->getSingleResult();
        return $result ? $result->toArray() : [];
    }

    /**
     * 获取检查结果信息
     * @param $params
     * @return array
     */
    public function getCheckResultInfo($params)
    {
        $result = $this->getCheckResultQuery($params);
        $result['check_time'] = $result['check_time'] ? date('Y-m-d', strtotime($result['check_time'])) : '-';
        $result['check_result'] = $result['check_result'] ? static::$t->_('extinguisher_checkout_result_' . $result['check_result'])  : '-';
        $result['remark'] = $result['remark'] ?? '-';

        $result['sign_info']  = '';
        $taskFileInfo = $this->getTaskFileInfo($params);
        foreach ($taskFileInfo as $key => $oneFile) {
            if($oneFile['type']  == 2) {
                $result['sign_info']  = $oneFile;
                unset($taskFileInfo[$key]);
            }
        }
        $result['photo_url_list']  = $taskFileInfo;

        $taskCheckListInfo = $this->getTaskCheckList($params);
        if(!$taskCheckListInfo) {
            foreach (ExtinguisherEnums::$extinguisher_check_list as $key => $item) {
                $oneData['problem_name'] = static::$t->_($item);
                $oneData['problem_key'] = $item;
                $oneData['type'] = 1;
                if($key == 7){//第八个问题，为输入型。
                    $oneData['type'] = 2;
                }
                $oneData['content'] = '';
                $taskCheckListInfo[] = $oneData;
            }
        } else {
            foreach ($taskCheckListInfo as $oneKey => $oneProblem) {
                $taskCheckListInfo[$oneKey]['problem_name'] = static::$t->_($oneProblem['problem_key']);
            }
        }
        $result['item_list']  = $taskCheckListInfo;
        return $result;
    }

    /**
     * 检查结果query
     * @param $where
     * @return array
     */
    public function getCheckResultQuery($where)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, status as check_status, check_time, check_result, remark');
        $builder->from(ExtinguisherTaskModel::class);
        $builder->where('id = :task_id:', ['task_id' => $where['task_id']]);
        $result = $builder->getQuery()->getSingleResult();
        return $result ? $result->toArray() : [];
    }

    /**
     * 按照任务id查询检查单文件信息
     * @param $where
     * @return mixed
     */
    public function getTaskFileInfo($where)
    {
        return ExtinguisherTaskFileModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id'],
            ],
            'columns' => ['id', 'extinguisher_task_id', 'bucket_name', 'object_key', 'file_name', 'type']
        ])->toArray();
    }

    /**
     * 按照任务ids查询检查单文件信息
     * @param $taskIds
     * @return mixed
     */
    public function getTaskFileInfoByIds($taskIds)
    {
        return ExtinguisherTaskFileModel::find([
            'conditions' => 'extinguisher_task_id IN ({task_ids:array}) and deleted = 0',
            'bind' => [
                'task_ids' =>  $taskIds,
            ],
            'columns' => ['id', 'extinguisher_task_id', 'bucket_name', 'object_key', 'file_name', 'type']
        ])->toArray();
    }

    /**
     * 按照任务id查询检查单信息
     * @param $where
     * @return mixed
     */
    public function getTaskCheckList($where)
    {
        return ExtinguisherCheckListModel::find([
            'conditions' => 'extinguisher_task_id = :task_id: and deleted = 0',
            'bind' => [
                'task_id' =>  $where['task_id'],
            ],
            'columns' => ['id', 'extinguisher_task_id', 'problem_key', 'content', 'type']
        ])->toArray();
    }
    /**
     * 按照任务id查询检查单信息
     * @param $taskIds
     * @return mixed
     */
    public function getTaskCheckListByIds($taskIds)
    {
        return ExtinguisherCheckListModel::find([
            'conditions' => 'extinguisher_task_id IN ({task_ids:array}) and deleted = 0',
            'bind' => [
                'task_ids' =>  $taskIds,
            ],
            'columns' => ['id', 'extinguisher_task_id', 'problem_key', 'content', 'type']
        ])->toArray();
    }

    /**
     * 获取检查日志列表
     * @param $params
     * @return mixed
     */
    public function getCheckLogListInfo($params)
    {
        $logList = $this->getTaskCheckLogQuery($params);
        $staffInfoToId = [];
        if($logList) {
            $operatorIds = array_values(array_unique(array_column($logList, 'operator_id')));
            $staffInfo = $this->getHcStaffInfoQuery($operatorIds);
            if($staffInfo) {
                $staffInfoToId = array_column($staffInfo, 'name', 'staff_info_id');
            }
        }
        foreach ($logList as $key => $one){
            $logList[$key]['operator_name'] = $staffInfoToId[$one['operator_id']] ?? '-';
        }
        return $logList;
    }

    /**
     * 获取检查日志
     * @param $where
     * @return mixed
     */
    public function getTaskCheckLogQuery($where)
    {
        return ExtinguisherOperationLogModel::find([
            'columns' => ['id', 'extinguisher_id', 'operator_id', "DATE_FORMAT(CONVERT_TZ(created_at, '+00:00', '{$this->timezone}'), '%Y-%m-%d %H:%i:%s') AS check_time"],
            'conditions' => 'type = 2 AND extinguisher_id = :extinguisher_id:',
            'bind' => ['extinguisher_id' => $where['task_id']],
            'order' => 'id desc'
        ])->toArray();
    }

    /**
     * 根据员工id 获取信息
     * @param $staffIds
     * @return array
     */
    public function getHcStaffInfoQuery($staffIds)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id, name');
        $builder->from(HrStaffInfoModel::class);
        $builder->where("staff_info_id IN ({staff_info_ids:array}) ", ['staff_info_ids' => $staffIds]);
        $admin_list = $builder->getQuery()->execute();
        $admin_list = empty($admin_list) ? [] : $admin_list->toArray();
        if($admin_list){
            foreach($admin_list as $key => $admin){
                $admin_list[$key]['name'] = $admin['name'] . '(' . $admin['staff_info_id'] . ')';
            }
        }
        return $admin_list;
    }

    /**
     * @param $params
     * @param $staffId
     * @return array
     * @throws \Exception
     */
    public function deleteTaskInfo($params, $staffId)
    {
        $db = $this->getDI()->get('db_backyard');

        try{
            $db->begin();

            //删除任务
            $db->updateAsDict("extinguisher_task", ['deleted' => 1, 'operator_id' => $staffId], ['conditions' => "id=?", 'bind' => [$params['task_id']]]);
            //删除任务检查信息
            $db->updateAsDict("extinguisher_check_list", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$params['task_id']]]);
            //删除任务检查文件
            $db->updateAsDict("extinguisher_task_file", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$params['task_id']]]);
            //删除任务管理员信息
            $db->updateAsDict("extinguisher_task_staff_relation", ['deleted' => 1], ['conditions' => "extinguisher_task_id=?", 'bind' => [$params['task_id']]]);

            $db->commit();
            return [];
        }catch (\Exception $e) {
            $db->rollback();
            $logger = $this->getDI()->get('logger');
            $logger->error('delete-extinguisher-task-failed:' . $e->getMessage());
        }
    }
}
