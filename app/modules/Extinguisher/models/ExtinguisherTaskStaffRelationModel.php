<?php
namespace App\Modules\Extinguisher\Models;


use App\Library\BaseModel;

class ExtinguisherTaskStaffRelationModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('extinguisher_task_staff_relation');
    }

    /**
     * @param array $data
     * @return bool
     */
    public function batch_insert_execute(array $data){
        if (empty($data)) {
            return false;
        }
        $keys = array_keys(reset($data));
        $keys = array_map(function ($key) {
            return "`{$key}`";
        }, $keys);
        $keys = implode(',', $keys);
        $sql = "INSERT INTO " . $this->getSource() . " ({$keys}) VALUES ";
        foreach ($data as $v) {
            $v = array_map(function ($value) {
                if ($value === null) {
                    return 'NULL';
                } else {
                    $value = addslashes($value); //处理特殊符号，如单引号
                    return "'{$value}'";
                }
            }, $v);
            $values = implode(',', array_values($v));
            $sql .= " ({$values}), ";
        }
        $sql = rtrim(trim($sql), ',');
        //DI中注册的数据库服务名称为"db"
        $result = $this->getDI()->get('db_backyard')->execute($sql);
        if (!$result) {
            return false;
        }
        return $result;
    }
}