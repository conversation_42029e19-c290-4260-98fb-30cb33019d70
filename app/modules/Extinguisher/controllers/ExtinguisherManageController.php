<?php
/**
 * 灭火器管理
 */

namespace App\Modules\Extinguisher\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Extinguisher\Services\ExtinguisherManageService;

class ExtinguisherManageController extends BaseController{


    /**
     * 新建灭火器
     * @Permission(action='extinguisher.add')
     */
    function addExtinguisherAction(){
        $params = $this->request->get();

        //参数验证
        $valiations = [
            'store_id' => 'Required|StrLenGeLe:1,10|>>>:param error[store_id]',
            'admin_ids' => 'Required|Arr|>>>:param error[admin_ids]',
            'extinguisher_code' => 'Required|StrLenGeLe:1,20|>>>:param error[extinguisher_code]',
            'asset_code' => 'StrLenLe:200|>>>:param error[asset_code]',
            'type' => 'Required|IntIn:1,2,3|>>>:param error[type]',
            'longitude' => 'Required|FloatGeLe:-180,180|>>>:param error[longitude]',
            'latitude' => 'Required|FloatGeLe:-90,90|>>>:param error[latitude]',
            'coordinate' => 'Required|StrLenGeLe:1,200|>>>:param error[coordinate]',
            'weight' => 'StrLenGeLe:0,20|>>>:param error[weight]',
            'photo_url' => 'Required|StrLenGeLe:1,200|>>>:param error[photo_url]'
        ];
        Validation::validate($params, $valiations);

        //插入数据
        $result = ExtinguisherManageService::getInstance()->addExtinguisher($params, $this->user['id']);

        return $this->returnJson($result['code'], $result['message']);
    }


    /**
     * 获取单个灭火器信息
     * @Permission(action='extinguisher.view')
     */
    function getOneExtinguisherAction(){
        $params = $this->request->get();

        //参数验证
        $valiations = [
            'id' => 'Required|IntGt:0',
        ];
        Validation::validate($params, $valiations);

        //获取数据
        $data = ExtinguisherManageService::getInstance()->getOneExtinguisher($params['id'], $this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * 获取单个灭火器操作日志
     * @Permission(action='extinguisher.view')
     */
    function getOneExtinguisherLogAction(){
        $params = $this->request->get();

        //参数验证
        $valiations = [
            'id' => 'Required|IntGt:0',
        ];
        Validation::validate($params, $valiations);
        //权限验证

        //获取数据
        $list = ExtinguisherManageService::getInstance()->getOneExtinguisherLog($params['id']);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取灭火器列表
     * @Permission(action='extinguisher.list')
     */
    function getExtinguisherListAction(){
        $params = $this->request->get();

        $valiations = [
            'store_category' => 'Arr',
            'extinguisher_type' => 'IntIn:1,2,3',
            'admin_id' => 'IntGe:1',
            'store_id' => 'StrLenGeLe:1,10',
            'extinguisher_code' => 'StrLenGeLe:1,20',
            'page' => 'Required|IntGt:0',
            'size' => 'Required|IntGt:0'
        ];
        //参数验证
        Validation::validate($params, $valiations);

        //获取数据
        $data = ExtinguisherManageService::getInstance()->getExtinguisherList($params, $this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * 编辑灭火器
     * @Permission(action='extinguisher.edit')
     */
    function editExtinguisherAction(){
        $params = $this->request->get();

        $valiations = [
            'id' => 'Required|IntGt:0|>>>:param error[id]',
            'store_id' => 'Required|StrLenGeLe:1,10|>>>:param error[store_id]',
            'admin_ids' => 'Required|Arr|>>>:param error[admin_ids]',
            'extinguisher_code' => 'Required|StrLenGeLe:1,20|>>>:param error[extinguisher_code]',
            'asset_code' => 'StrLenLe:200|>>>:param error[asset_code]',
            'type' => 'Required|IntIn:1,2,3|>>>:param error[type]',
            'longitude' => 'Required|Float|>>>:param error[longitude]',
            'latitude' => 'Required|Float|>>>:param error[latitude]',
            'coordinate' => 'Required|StrLenGeLe:1,200|>>>:param error[coordinate]',
            'weight' => 'StrLenGeLe:0,20|>>>:param error[weight]',
            'photo_url' => 'Required|StrLenGeLe:1,200|>>>:param error[photo_url]'
        ];
        //参数验证
        Validation::validate($params, $valiations);

        //权限验证

        //更新数据
        $result = ExtinguisherManageService::getInstance()->editExtinguisher($params, $this->user['id']);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 删除灭火器
     * @Permission(action='extinguisher.del')
     */
    function delExtinguisherAction(){
        $params = $this->request->get();

        //参数验证
        $valiations = [
            'ids' => 'Required|Arr',
        ];
        Validation::validate($params, $valiations);
        //权限验证

        //执行删除操作
        $result = ExtinguisherManageService::getInstance()->delExtinguisher($params['ids']);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 批量上传
     * @Permission(action='extinguisher.batch_add')
     */
    function batchUploadExtinguisherAction(){

        $params = $this->request->get();

        //获取传入xlsx
        $excel_file = $this->request->getUploadedFiles();
        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取文件
        $excel_data = $excel->openFile($excel_file[0]->getTempName())
            ->openSheet()
            ->setSkipRows(2)
            ->getSheetData();

        //上传灭火器
        $result = ExtinguisherManageService::getInstance()->uploadExtinguisher($excel_data, $this->user['id']);

        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 批量导出灭火器数据导出
     * @Permission(action='extinguisher.exportexcel')
     */
    function exportExtinguisherExcelAction(){

        $params = $this->request->get();
        $valiations = [
            'store_category' => 'Arr',
            'extinguisher_type' => 'IntIn:1,2,3',
            'admin_id' => 'IntGe:1',
            'store_id' => 'StrLenGeLe:1,10',
            'extinguisher_code' => 'StrLenGeLe:1,20',
        ];
        //参数验证
        Validation::validate($params, $valiations);

        //获取数据
        $lock_key = md5('extinguisher_data_export_' . $this->user['id']);
        $result = $this->atomicLock(function () use ($params) {
            return ExtinguisherManageService::getInstance()->dataExport($params, $this->user['id']);
        }, $lock_key, 300);

        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }




}
