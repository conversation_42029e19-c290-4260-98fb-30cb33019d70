<?php
/**
 * 灭火器管理
 */

namespace App\Modules\Extinguisher\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Extinguisher\Services\BaseService;
use app\modules\Extinguisher\services\ExtinguisherTaskService;

class ExtinguisherTaskManageController extends BaseController
{
    /**
     * 获取灭火器任务列表
     * @Permission(action='extinguisher.check.task.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getExtinguisherTaskListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$task_list_search_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_task_list_search_params);

            $staff_info_id = $this->user['id'];
            $list = ExtinguisherTaskService::getInstance()->getExtinguisherTaskListInfo($params, $staff_info_id);

            return $this->returnJson(ErrCode::$SUCCESS, '', $list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e){
            $this->logger->warning('get extinguisher task object list :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 导出灭火器任务
     * @Permission(action='extinguisher.check.task.exportexcel')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportExtinguisherTaskListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$task_list_search_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_task_list_search_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('extinguisher_task_data_export_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return ExtinguisherTaskService::getInstance()->exportExtinguisherTaskListInfo($params, $this->user['id']);
        }, $lock_key, 300);
        if($res === false) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'please try again', []);
        }
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], ['file_url' => $res['data']]);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 查看灭火器任务中的 灭火器详情
     * @Permission(action='extinguisher.check.task.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$task_detail_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_task_detail_params);

            $result = ExtinguisherTaskService::getInstance()->getExtinguisherTaskDetail($params);
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e){
            $this->logger->warning('get extinguisher detail in extinguisher task:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 获取任务检查结果
     * @Permission(action='extinguisher.check.task.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function taskCheckResultAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$task_check_result_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_task_check_result_params);

            $result = ExtinguisherTaskService::getInstance()->getCheckResultInfo($params);
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e){
            $this->logger->warning('taskCheckResultAction_warning:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 获取检查日志
     * @Permission(action='extinguisher.check.task.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getCheckLogListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$check_log_list_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_task_detail_params);
            $result = ExtinguisherTaskService::getInstance()->getCheckLogListInfo($params);
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e){
            $this->logger->warning('getCheckLogListAction_warning :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 删除任务
     * @Permission(action='extinguisher.check.task.del')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function deleteTaskAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ExtinguisherTaskService::$check_delete_task_params);
        try {
            Validation::validate($params, ExtinguisherTaskService::$validate_delete_task_params);
            $staff_info_id = $this->user['id'];
            $result = ExtinguisherTaskService::getInstance()->deleteTaskInfo($params, $staff_info_id);
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e){
            $this->logger->warning('delete_extinguisher_task_warning:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }
}