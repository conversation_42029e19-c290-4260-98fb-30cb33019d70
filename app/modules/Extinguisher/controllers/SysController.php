<?php


namespace App\Modules\Extinguisher\Controllers;

use App\Library\ErrCode;
use App\Modules\Extinguisher\Services\SysService;
use App\Modules\Extinguisher\Services\StaffInfoService;
use App\Library\Enums\ExtinguisherEnums;



class SysController extends BaseController{

    /**
     *  根据关键词搜索网点名称（没有传递关键词，默认提取20条）
     *  附带区域和网点类型
     * <AUTHOR>
     * @Token
     */
    function getStoreNameListAction(){
        $params = $this->request->get();

        $list = StaffInfoService::getInstance()->getStoreNameList($params['keyword']);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 根据关键字获取员工信息，如果关键字为空默认取20条
     * <AUTHOR>
     * @Token
     */
    function getStaffListAction(){
        $params = $this->request->get();

        $list = StaffInfoService::getInstance()->getStaffList($params['keyword']);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 系统信息
     * @Token
     */
    function sysInfoAction(){
        $params = $this->request->get();
        $data = SysService::getInstance()->sysList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

}
