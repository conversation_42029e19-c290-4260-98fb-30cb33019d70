<?php

namespace App\Modules\PaperDocument\Controllers;

use App\Library\ErrCode;
use App\Modules\PaperDocument\Services\SysService;

class SysController extends BaseController
{
    /**
     * 获取模块下拉列表
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getModuleListAction()
    {
        $list = SysService::getInstance()->getModuleList();
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取待补全原因
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getConfirmReasonListAction()
    {
        $list = SysService::getInstance()->getConfirmReason();
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    public function getConfirmStateListAction()
    {
        $data = [
            'confirm_status'    => SysService::getInstance()->getConfirmStateList(),
            'cost_company_list' => SysService::getInstance()->getCostCompanyId(),
        ];
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }
}