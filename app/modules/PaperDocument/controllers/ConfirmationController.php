<?php

namespace App\Modules\PaperDocument\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\PaperDocument\Services\ConfirmationService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 纸质文件确认管理控制器
 * @description 纸质文件确认管理功能
 * <AUTHOR>
 * @date 2024-01-01
 * 
 * @property \Phalcon\Http\Request $request
 * @property \Phalcon\Mvc\View $view
 * @property \Phalcon\Mvc\Response $response
 */
class ConfirmationController extends BaseController
{
    /**
     * 我的申请列表
     * @description 获取当前用户提交的纸质文件确认申请列表
     * @Token
     * @Permission(action='paper_document.my_list.search')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        // 过滤参数，保留字符串"0"和整数0
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_list);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层获取列表数据
        $confirmationService = ConfirmationService::getInstance();
        $data                = $confirmationService->getMyApplyList($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 纸质审件审核列表
     * @description 获取需要审核的纸质文件确认申请列表，仅确认人员可访问
     * @Token
     * @Permission(action='paper_document.audit_list.search')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditListAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        $params = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_audit_list);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        // 调用业务层获取审核列表数据
        $confirmationService = ConfirmationService::getInstance();
        $data                = $confirmationService->getAuditList($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 查看纸质文件审件详情
     * @description 获取指定纸质文件确认申请的详细信息，包括操作日志
     * @Token
     * @Permission(action='paper_document.my_list.detail')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层获取详情数据
        $confirmationService = ConfirmationService::getInstance();
        $data                = $confirmationService->getDetail($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 查看纸质文件审件详情
     * @description 获取指定纸质文件确认申请的详细信息，包括操作日志
     * @Token
     * @Permission(action='paper_document.audit_list.detail')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditDetailAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层获取详情数据
        $confirmationService = ConfirmationService::getInstance();
        $data                = $confirmationService->getDetail($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 提交纸质文件
     * @description 提交纸质文件确认申请，创建新的确认记录
     * @Token
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function submitAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_submit);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层提交申请
        $confirmationService = ConfirmationService::getInstance();
        $recordId            = $confirmationService->submitPaperDocument($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['id' => $recordId]);
    }

    /**
     * 确认纸质文件齐全
     * @description 确认人员对纸质文件进行确认操作，更新确认状态
     * @Token
     * @Permission(action='paper_document.audit_list.confirm')
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function confirmAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_confirm);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层执行确认操作
        $confirmationService = ConfirmationService::getInstance();
        $result              = $confirmationService->confirmPaperDocument($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['result' => $result]);
    }

    /**
     * 批量确认纸质文件齐全
     * @description 确认人员对纸质文件进行确认操作，更新确认状态
     * @Token
     * @Permission(action='paper_document.audit_list.batch')
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function batchConfirmAction()
    {
        // 获取请求参数
        $params = $this->request->get();
        try {
            // 参数验证
            Validation::validate($params, ConfirmationService::$validate_batch);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        // 调用业务层执行确认操作
        $confirmationService = ConfirmationService::getInstance();
        $result              = $confirmationService->batchConfirmPaperDocument($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }
}