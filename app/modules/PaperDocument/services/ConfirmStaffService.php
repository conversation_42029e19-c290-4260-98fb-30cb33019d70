<?php

namespace App\Modules\PaperDocument\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\ValidationException;
use App\Models\oa\PaperDocumentConfirmStaffModel;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\Shop\Services\OrderSummaryService;

class ConfirmStaffService extends BaseService
{

    public static $validate_list_search = [
        'pageSize'  => 'IntGt:0',      //每页条数
        'pageNum'   => 'IntGt:0',      //页码
        'module_id' => 'StrLenGe:0',   //模块编码
    ];

    public static $validate_add = [
        'module_id'        => 'StrLenGe:0',   //模块编码
        'confirm_staff_id' => 'StrLenGe:0',   //确认人工号
    ];

    public static $validate_edit = [
        'id'               => 'StrLenGe:0',   //list ID
        'module_id'        => 'StrLenGe:0',   //模块编码
        'confirm_staff_id' => 'StrLenGe:0',   //确认人工号
    ];

    public static $validate_detail = [
        'id' => 'StrLenGe:0',//list ID
    ];

    /**
     * 实例
     * @var ConfirmStaffService
     */
    private static $instance = null;

    /**
     * 单一实例
     * @return ConfirmStaffService
     */
    public static function getInstance(): ConfirmStaffService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取纸质单据确认人列表
     * @param array $params 查询参数：包含page_num(页码)、page_size(每页条数)、module_id(模块ID)
     * @return array 返回包含code、message、data的数组结构
     */
    public function getPaperDocumentConfirmStaffList($params): array
    {
        $pageNum        = $params['pageNum'] ?? 1;
        $pageSize       = $params['pageSize'] ?? 20;
        $moduleId       = $params['module_id'] ?? 0;
        $confirmStaffId = $params['confirm_staff_id'] ?? 0;

        // 构建查询条件
        $conditions = ['deleted = 0'];
        $bind       = [];

        // 如果传入了模块ID，添加到查询条件中
        if ($moduleId > 0) {
            $conditions[]      = 'module_id = :module_id:';
            $bind['module_id'] = $moduleId;
        }
        if ($confirmStaffId > 0) {
            $conditions[]      = 'confirm_staff_id = :confirm_staff_id:';
            $bind['confirm_staff_id'] = $confirmStaffId;
        }
        $conditionStr = implode(' AND ', $conditions);

        // 查询总数
        $total = PaperDocumentConfirmStaffModel::count([
            $conditionStr,
            'bind' => $bind,
        ]);
        // 查询列表数据
        $list = [];

        if (empty($total)) {
            return [
                'items'  => $list,
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page'     => $pageSize,
                    'total_count'  => 0,
                ],
            ];
        }

        $offset = ($pageNum - 1) * $pageSize;
        $items  = PaperDocumentConfirmStaffModel::find([
            $conditionStr,
            'bind'   => $bind,
            'order'  => 'id DESC',
            'limit'  => $pageSize,
            'offset' => $offset,
        ])->toArray();

        // 确认人工号
        $staffInfoIds = array_column($items, 'confirm_staff_id');
        $staffInfo = (new HrStaffInfoService())->getStaffInfoByIds($staffInfoIds, 'staff_info_id,name,state');
        $staffInfo = array_column($staffInfo, null, 'staff_info_id');

        // 在职状态枚举
        $stateList = OrderSummaryService::staffStateEnums();

        foreach ($items as $item) {
            // 员工信息
            $itemStaffInfo = $staffInfo[$item['confirm_staff_id']];

            $list[] = [
                'id'                 => $item['id'],
                'module_id'          => $item['module_id'],
                'module_name'        => SysService::getInstance()->getModuleName($item['module_id']),
                'confirm_staff_id'   => $item['confirm_staff_id'] ?? '',
                'confirm_staff_name' => $itemStaffInfo['name'] ?? '',
                'state'              => $itemStaffInfo['state'] ?? '',
                'state_txt'          => $stateList[$itemStaffInfo['state']] ?? '',
            ];
        }

        return [
            'items'  => $list,
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => (int)$total,
            ],
        ];
    }

    /**
     * 添加纸质单据确认人
     * @param array $params 编辑参数：包含module_id(模块ID)、confirm_staff_id(确认人工号)
     * @return true
     * @throws \Exception
     */
    public function add($params)
    {
        $moduleId       = (int)$params['module_id'];
        $confirmStaffId = trim($params['confirm_staff_id'] ?? '');


        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 校验确认人信息
            $this->validateConfirmStaff($confirmStaffId);

            // 检查当前模块是否已存在配置
            $existingRecord = PaperDocumentConfirmStaffModel::findFirst([
                'conditions' => 'module_id = :module_id: AND confirm_staff_id = :confirm_staff_id: AND deleted = 0',
                'bind'       => ['module_id' => $moduleId, 'confirm_staff_id' => $confirmStaffId],
            ]);

            if ($existingRecord) { // 已存在确认人
                throw new ValidationException(static::$t->_('confirm_staff_exists'));
            }

            // 创建新记录
            $newRecord = new PaperDocumentConfirmStaffModel();
            $newRecord->module_id = $moduleId;
            $newRecord->confirm_staff_id = $confirmStaffId;
            if (!$newRecord->save()) {
                throw new \Exception('创建纸质单据确认人配置失败');
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return $newRecord->id;
    }

    /**
     * 编辑纸质单据确认人
     * @param array $params 编辑参数：包含module_id(模块ID)、confirm_staff_id(确认人工号)
     * @return true
     * @throws \Exception
     */
    public function edit($params)
    {
        $id             = (int)$params['id'];
        $moduleId       = (int)$params['module_id'];
        $confirmStaffId = trim($params['confirm_staff_id'] ?? '');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            $this->validateConfirmStaff($confirmStaffId);

            // 检查当前模块是否已存在配置
            $existingRecord = PaperDocumentConfirmStaffModel::findFirst([
                'conditions' => 'module_id = :module_id: AND confirm_staff_id = :confirm_staff_id: AND deleted = 0 and id != :id:',
                'bind'       => ['module_id' => $moduleId, 'confirm_staff_id' => $confirmStaffId, 'id' => $id],
            ]);

            if ($existingRecord) { // 已存在确认人
                throw new ValidationException(static::$t->_('confirm_staff_exists'));
            }

            $record = PaperDocumentConfirmStaffModel::findFirst($id);
            if (empty($record)) {
                throw new ValidationException(static::$t->_('bank_account_save_not_exist'));
            }
            $record->confirm_staff_id = $confirmStaffId;
            if (!$record->save()) {
                throw new \Exception('创建纸质单据确认人配置失败');
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return $id;
    }

    /**
     * 删除纸质单据确认人
     * @param $params
     * @return true
     * @throws ValidationException
     */
    public function delete($params)
    {
        $id             = (int)$params['id'];

        // 检查当前模块是否已存在配置
        $record = PaperDocumentConfirmStaffModel::findFirst([
            'conditions' => 'id = :id: AND deleted = 0',
            'bind'       => ['id' => $id],
        ]);
        if (empty($record)) {
            throw new ValidationException(static::$t->_('bank_account_delete_not_exist'));
        }
        $record->deleted = GlobalEnums::IS_DELETED;
        $record->save();

        return true;
    }

    /**
     * 获取纸质单据确认人详情
     * @param array $params 查询参数：包含module_id(模块ID)
     * @return array 返回包含code、message、data的数组结构
     */
    public function detail($params): array
    {
        $id = (int)$params['id'];

        // 查询指定模块的确认人配置
        $record = PaperDocumentConfirmStaffModel::findFirst([
            'conditions' => 'id = :id: AND deleted = 0',
            'bind'    => ['id' => $id],
            'columns' => 'module_id,confirm_staff_id',
        ]);
        if (empty($record)) {
            return [];
        }
        $staffInfo = (new HrStaffInfoService())->staffInfo($record->confirm_staff_id);

        return [
            'module_id'          => (int)$record->module_id,
            'module_name'        => SysService::getInstance()->getModuleName($record->module_id),
            'confirm_staff_id'   => $record->confirm_staff_id ?? '',
            'confirm_staff_name' => !empty($record->confirm_staff_id)
                ? $staffInfo->name
                : '',
        ];
    }

    /**
     * 获取指定模块的全部的确认
     * @param $module_id
     * @return array
     */
    public function getConfirmStaffIdsByModuleId($module_id): array
    {
        if (empty($module_id)) {
            return [];
        }
        $staffList = PaperDocumentConfirmStaffModel::find([
            'conditions' => 'module_id = :module_id: and deleted = 0',
            'bind' => [
                'module_id' => $module_id
            ],
            'columns' => 'confirm_staff_id',
        ])->toArray();
        return array_column($staffList, 'confirm_staff_id');
    }

    /**
     * 校验确认人信息
     * @param string $confirmStaffId
     * @return void
     * @throws ValidationException
     */
    private function validateConfirmStaff(string $confirmStaffId)
    {
        if (empty($confirmStaffId)) { // 工号为空
            throw new ValidationException(static::$t->_('job_transfer.err_msg.1'));
        }

        $staffInfo = (new HrStaffInfoService())->staffInfo($confirmStaffId);
        if (empty($staffInfo)) { // 工号不存在
            throw new ValidationException(static::$t->_('job_transfer.err_msg.1'));
        }

        if ($staffInfo->state != StaffInfoEnums::STAFF_STATE_IN) {
            throw new ValidationException(static::$t->_('staff_not_on_job'));
        }
    }
}