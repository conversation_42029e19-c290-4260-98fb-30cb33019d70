<?php

namespace App\Modules\PaperDocument\Services;

use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PaperDocumentEnums;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Hc\Models\SysDepartmentModel;

class SysService extends BaseService
{
    private static $valid_module_ids = [
        BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT, // 报销模块
    ];

    /**
     * 实例
     * @var SysService
     */
    private static $instance = null;

    /**
     * 单一实例
     * @return SysService
     */
    public static function getInstance(): SysService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取可用模块
     * @return array
     */
    public function getModuleList(): array
    {
        $list = [];
        foreach (self::$valid_module_ids as $item) {
            $list[] = [
                'value' => (string)$item,
                'label' => $this->getModuleName($item),
            ];
        }
        return $list;
    }

    /**
     * 获取可用模块ID
     * @return array
     */
    public function getValidModuleIds(): array
    {
        return self::$valid_module_ids;
    }

    /**
     * 获取模块名
     * @param $module_id
     * @return string
     */
    public function getModuleName($module_id): string
    {
        if (!isset(BankFlowEnums::$oa_type_id_module_key[$module_id])) {
            return '';
        }
        return static::$t->_(BankFlowEnums::$oa_type_id_module_key[$module_id]);
    }

    /**
     * 获取待补全原因
     * @return array|array[]
     */
    public function getConfirmReason(): array
    {
        $result = [];
        $countryCode = get_country_code();
        switch ($countryCode) {
            case GlobalEnums::TH_COUNTRY_CODE:
            case GlobalEnums::MY_COUNTRY_CODE:
                $result = $this->getConfirmReasonByCountry($countryCode);
                break;
            default:
                break;
        }
        return $result;
    }

    /**
     * 根据国家获取待补全原因
     * @param string $countryCode
     * @return array|array[]
     */
    private function getConfirmReasonByCountry(string $countryCode): array
    {
        $reasonList = EnumsService::getInstance()->getSettingEnvValueMap('paper_doc_reason_type');
        if (empty($reasonList)) {
            return [];
        }
        $reasonList = array_keys($reasonList);
        if (empty($reasonList)) {
            return [];
        }

        // 翻译: 通用前缀 + 国家 + 原因类型
        return array_map(function ($item) use($countryCode) {
            return [
                'value' => (string)$item,
                'label' => static::$t->_(sprintf('paper_doc_%s_%s', strtolower($countryCode), $item)),
            ];
        }, $reasonList);

    }

    /**
     * 确认状态
     * @return array|array[]
     */
    public function getConfirmStateList(): array
    {
        $stateList = [
            PaperDocumentEnums::CONFIRM_STATE_PENDING_CONFIRM,
            PaperDocumentEnums::CONFIRM_STATE_COMPLETE,
            PaperDocumentEnums::CONFIRM_STATE_PENDING_FILL,
        ];
        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            array_unshift($stateList, PaperDocumentEnums::CONFIRM_STATE_PENDING_SUBMIT);
        }

        return array_map(function ($item) {
            return [
                'value' => (string)$item,
                'label' => static::$t->_(sprintf('confirm_state_%s', $item)),
            ];
        }, $stateList);
    }

    /**
     * 获取费用所属公司
     * @param bool $isTotal 是否全部 false-取未删除的 true-取全部的
     * @return mixed
     */
    public function getCostCompanyId(bool $isTotal = false)
    {
        if ($isTotal) {
            $conditions = 'type = 1';
        } else {
            $conditions = 'deleted = 0 and type = 1';
        }

        return SysDepartmentModel::find([
            'conditions' => $conditions,
            'columns'    => 'id as value,name as label',
        ])->toArray();
    }
}