<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Modules\Material\Services\BaseService;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Payment\Services\StoreRentingAddService;


/**
 * 耗材管理-耗材领用出库
 * Class WmsOutStorageController
 * @package App\Modules\Material\Controllers
 */
class WmsOutStorageController extends BaseController
{
    /**
     * 耗材管理-耗材领用出库-查询列表
     * @Permission(action='material.wms_out_storage.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66612
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsOutStorageService::$not_must_params);
        try {
            Validation::validate($params, WmsOutStorageService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材领用出库-查看
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66627
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_share);
        $list = WmsOutStorageService::getInstance()->detail($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }


    /**
     * 耗材领用出库-作废
     * @Permission(action='material.wms_out_storage.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66632
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_share);
        $res = WmsOutStorageService::getInstance()->cancel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材领用出库-修改
     * @Permission(action='material.wms_out_storage.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67272
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_update);
        $res = WmsOutStorageService::getInstance()->edit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材领用出库-修改货主仓库
     * @Permission(action='material.wms_out_storage.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67727
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function saveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_save);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->save($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
    
    /**
     *  耗材领用出库-提交审核
     * @Permission(action='material.wms_out_storage.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66637
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_share);
        $lock_key = md5(MaterialWmsEnums::MATERIAL_WMS_OUT_AUDIT_ADD_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return WmsOutStorageService::getInstance()->audit($params, $this->user);
        }, $lock_key, 3);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     *  耗材领用出库-查看-查看操作记录
     * @Permission(action='material.wms_out_storage.detail')
     * @api  https://yapi.flashexpress.pub/project/133/interface/api/66642
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function historyApprovalLogAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_history_approval_log);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->historyApprovalLogList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *  耗材领用出库-导出
     * @Permission(action='material.wms_out_storage.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66622
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsOutStorageService::$not_must_params);
        try {
            Validation::validate($params, WmsOutStorageService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(MaterialWmsEnums::MATERIAL_WMS_OUT_STORAGE_EXPORT_LOCK . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_WMS_OUT_STORAGE, $params);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 耗材领用出库-拆分数量
     * @Permission(action='material.wms_out_storage.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66647
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function splitAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_split);
        $res = WmsOutStorageService::getInstance()->split($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }



    /**
     * 耗材领用出库-获取仓库信息
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67962
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getWarehouseListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_warehouse_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->getWarehouseList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材领用出库-获取省 市 区
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67967
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function areaverbAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_area_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res =  WmsOutStorageService::getInstance()->getAreaList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }


    /**
     *  耗材领用出库-获取成本中心
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67957
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getPcCodeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_department);
            if ($params['type'] == Enums::PAYMENT_COST_STORE_TYPE_01) {
                Validation::validate($params, WmsOutStorageService::$validate_cost_department_id);
            } else if ($params['type'] == Enums::PAYMENT_COST_STORE_TYPE_02) {
                Validation::validate($params, WmsOutStorageService::$validate_cost_store_id);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->getPcCode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材领用出库-搜索使用人
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67952
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchStaffAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsOutStorageService::$validate_search_staff);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsOutStorageService::getInstance()->searchStaff($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材管理-耗材出库-修改-选择使用人和使用网点 返回pc_code
     * @Permission(action='material.wms_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/72097
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getStoreByPcCodeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_store_by_pc_code);
        $res = WmsOutStorageService::getInstance()->storeByPcCode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *  耗材领用出库-提交审核(修改详情-保存加提交审核)
     * @Permission(action='material.wms_out_storage.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79762
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function editAndAuditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_update);
        $res = WmsOutStorageService::getInstance()->editAndAudit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材领用出库-查询scm出库状态
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/79737
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getScmStatusAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsOutStorageService::$validate_share);
        $list = WmsOutStorageService::getInstance()->getScmStatus($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }



}
