<?php

namespace App\Modules\Material\Controllers;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\WmsAllotService;
use App\Util\RedisKey;
use Phalcon\Http\ResponseInterface;
use GuzzleHttp\Exception\GuzzleException;

/**
 * 网点耗材（包材）调拨单
 */
class WmsAllotController extends BaseController
{
    /**
     * 枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91394
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = WmsAllotService::getInstance()->getOptionsDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 添加默认配置项
     * @Permission(action='material.wms_allot.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91397
     * @return Response|ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = WmsAllotService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 创建
     * @Permission(action='material.wms_allot.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91409
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_add);
        $lock_key = md5(RedisKey::MATERIAL_PACKAGE_ALLOT_ADD_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return WmsAllotService::getInstance()->add($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 网点之间直线距离
     * @Permission(action='material.wms_allot.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91403
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getLinearDistanceAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_linear_distance);
        $res = WmsAllotService::getInstance()->getLinearDistance($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 筛选ka账户
     * @Permission(action='material.wms_allot.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91430
     * @return Response|ResponseInterface
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function searchKaAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_ka);
        $res = WmsAllotService::getInstance()->searchKa($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材列表
     * @Permission(action='material.wms_allot.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91751
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSkuListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_get_sku_list);
        $res = WmsAllotService::getInstance()->getSkuList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 取消
     * @Permission(action='material.wms_allot.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91484
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_cancel);
        $lock_key = md5(RedisKey::MATERIAL_PACKAGE_ALLOT_CANCEL_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return WmsAllotService::getInstance()->cancel($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='material.wms_allot.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91487
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_id);
        $res = WmsAllotService::getInstance()->detail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看 - 调拨信息 - 导出
     * @Permission(action='material.wms_allot.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91490
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportSkuAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_id);
        $lock_key = md5(RedisKey::MATERIAL_PACKAGE_ALLOT_SKU_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return WmsAllotService::getInstance()->exportSku($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 查看 - 查看物流
     * @Permission(action='material.wms_allot.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91493
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getExpressListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_id);
        $res = WmsAllotService::getInstance()->getExpressList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看 - 查看物流 - 路由
     * @Permission(action='material.wms_allot.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91496
     * @return Response|ResponseInterface
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function getExpressRouteAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WmsAllotService::$validate_express_sn);
        $res = WmsAllotService::getInstance()->getExpressRoute($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 列表
     * @Permission(action='material.wms_allot.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91499
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, WmsAllotService::$not_must_params);
        Validation::validate($params, WmsAllotService::$validate_list);
        $res = WmsAllotService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 超时未调出调拨单数量
     * @Permission(action='material.wms_allot.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91505
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getTimeoutOutNumAction()
    {
        $res = WmsAllotService::getInstance()->getTimeoutOutNum($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='material.wms_allot.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91502
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, WmsAllotService::$not_must_params);
        Validation::validate($params, WmsAllotService::$validate_list);
        $lock_key = md5(RedisKey::MATERIAL_PACKAGE_ALLOT_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return WmsAllotService::getInstance()->export($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }
}
