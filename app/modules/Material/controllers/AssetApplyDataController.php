<?php
namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\BaseService;

/**
 * 资产领用申请-数据查询
 * Class AssetApplyDataController
 * @package App\Modules\Material\Controllers
 */
class AssetApplyDataController extends BaseController
{
    /**
     * 资产领用申请-数据查询-查询
     * @Permission(action='material.asset_apply_data.apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62717
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $res = AssetApplyService::getInstance()->getList($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY_DATA, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-数据查询-查看
     * @Permission(action='material.asset_apply_data.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62718
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_update);
        $res = AssetApplyService::getInstance()->detail($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY_DATA, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-数据查询-查看某出库单-出库路由
     * @Permission(action='material.asset_apply_data.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87425
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getOutboundTrackingInfoAction()
    {
        $params = trim_array($this->request->get());
        $res = AssetApplyService::getInstance()->getOutboundTrackingInfo($this->locale, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-数据查询-导出
     * @Permission(action='material.asset_apply_data.apply.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62719
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $lock_key = md5(MaterialAssetApplyEnums::MATERIAL_ASSET_APPLY_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return AssetApplyService::getInstance()->exportSvc($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY_DATA, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? '');
    }

    /**
     * 资产领用申请-历史审批日志-详情
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62734
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getAuthLogAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_workflow_no);
        try {
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AssetApplyService::getInstance()->getAuthLog($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
