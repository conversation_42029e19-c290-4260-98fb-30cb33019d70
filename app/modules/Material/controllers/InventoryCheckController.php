<?php
namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\InventoryCheckEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\InventoryCheckService;
use App\Util\RedisKey;

/**
 * 资产盘点-盘点单管理菜单
 */
class InventoryCheckController extends BaseController
{
    /**
     * 获取列表初始化数据
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33543
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getListDefaultAction()
    {
        $res = InventoryCheckService::getInstance()->getListDefaultActionData();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点单列表
     * @Permission(action='material.inventory_check.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33396
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        Validation::validate($params, InventoryCheckService::$validate_list_search);
        $res = InventoryCheckService::getInstance()->getList($params, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获取盘点单 - 创建页 - 基本信息默认值
     * @Permission(action='material.inventory_check.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33445
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = InventoryCheckService::getInstance()->getAddDefaultData();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 创建盘点单
     * @Permission(action='material.inventory_check.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33389
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
    **/
    public function addAction()
    {
        $params = trim_array($this->request->get());
        $validate_param = InventoryCheckService::getInstance()->getExtendValidation($params);
        Validation::validate($params, $validate_param);
        $res = InventoryCheckService::getInstance()->addInventoryCheck($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看盘点单
     * @Permission(action='material.inventory_check.info')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33403
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function infoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, InventoryCheckService::$validate_inventory_check_id);
        $res = InventoryCheckService::getInstance()->infoInventoryCheck($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除盘点单 -- 18092废弃
     * @Permission(action='material.inventory_check.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33410
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, InventoryCheckService::$validate_inventory_check_id);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = InventoryCheckService::getInstance()->delInventoryCheck($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点报表(查看任务)
     * @Permission(action='material.inventory_check.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33424
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        Validation::validate($params, InventoryCheckService::$validate_detail_search);
        $res = InventoryCheckService::getInstance()->getDetailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 下发盘点任务
     * @Permission(action='material.inventory_check.task')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78007
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function taskAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, InventoryCheckService::$validate_inventory_check_id);
        $res = InventoryCheckService::getInstance()->startInventory($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 取消限制打卡
     * @Permission(action='material.inventory_check.cancel_punch_out')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78587
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelPunchOutAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, InventoryCheckService::$validate_inventory_check_id);
        $res = InventoryCheckService::getInstance()->cancelPunchOut($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 终止
     * @Permission(action='material.inventory_check.terminal')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78027
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function terminalAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(InventoryCheckService::$validate_inventory_check_id, InventoryCheckService::$validate_terminal));
        $res = InventoryCheckService::getInstance()->terminal($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 旧资产盘点-盘点报表-导出
     * @Permission(action='material.inventory_check.detail.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33438
     */
    public function detailExportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        try {
            Validation::validate($params, InventoryCheckService::$validate_detail_export_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('material_inventory_check_staff_asset_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return InventoryCheckService::getInstance()->getDetailExport($params, $this->locale);
        }, $lock_key, 60);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 新资产盘点-查看任务（费用报表）-导出
     * @Permission(action='material.inventory_check.detail.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78037
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        Validation::validate($params, InventoryCheckService::$validate_detail_search);
        // 加锁处理
        $lock_key = md5(RedisKey::MATERIAL_INVENTORY_EXPORT_ASSETS . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $total = InventoryCheckService::getInstance()->getInventoryTaskAssetExportTotal($params);
            if ($total > InventoryCheckEnums::INVENTORY_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_INVENTORY_ASSET_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = InventoryCheckService::getInstance()->export($params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 盘点报表-取消限制打卡
     * @Permission(action='material.inventory_check.detail.cancel_punch_out')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78022
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function batchCancelPunchOutAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, InventoryCheckService::$validate_detail_ids);
        $res = InventoryCheckService::getInstance()->batchCancelPunchOut($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点报表-终止
     * @Permission(action='material.inventory_check.detail.terminal')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78042
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function batchTerminalAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(InventoryCheckService::$validate_detail_ids, InventoryCheckService::$validate_terminal));
        $res = InventoryCheckService::getInstance()->batchTerminal($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点报表-提醒
     * @Permission(action='material.inventory_check.detail.remind')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78047
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function batchRemindAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, InventoryCheckService::$validate_detail_ids);
        $res = InventoryCheckService::getInstance()->batchRemind($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点报表-盘点明细-列表
     * @Permission(action='material.inventory_check.detail.asset_list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78117
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function assetListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        Validation::validate($params, InventoryCheckService::$validate_detail_asset_list_search);
        $res = InventoryCheckService::getInstance()->getInventoryTaskAssetList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 盘点报表-盘点明细-导出
     * @Permission(action='material.inventory_check.detail.asset_list_export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/78052
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function assetListExportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, InventoryCheckService::$not_must_params);
        Validation::validate($params, InventoryCheckService::$validate_detail_asset_list_search);
        // 加锁处理
        $lock_key = md5(RedisKey::MATERIAL_INVENTORY_EXPORT_ASSETS . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $total = InventoryCheckService::getInstance()->getInventoryTaskAssetExportTotal($params);
            if ($total > InventoryCheckEnums::INVENTORY_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_INVENTORY_ASSET_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = InventoryCheckService::getInstance()->export($params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}
