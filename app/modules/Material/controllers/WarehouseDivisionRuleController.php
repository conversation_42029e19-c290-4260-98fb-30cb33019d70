<?php
namespace App\Modules\Material\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\WarehouseDivisionRuleService;
use App\Util\RedisKey;
use App\Library\Validation\ValidationException;

/**
 * 物料/资产管理-分仓规则-控制器层
 * Class WarehouseDivisionRuleController
 * @package App\Modules\Material\Controllers
 */
class WarehouseDivisionRuleController extends BaseController
{
    /**
     * 获取默认枚举配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74487
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = WarehouseDivisionRuleService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 列表
     * @Permission(action='material.warehourse_division_rule.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74562
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WarehouseDivisionRuleService::$not_must_params);
        Validation::validate($params, WarehouseDivisionRuleService::$validate_list);
        $res = WarehouseDivisionRuleService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 新建
     * @Permission(action='material.warehourse_division_rule.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74457
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(WarehouseDivisionRuleService::$validate_add, WarehouseDivisionRuleService::$validate_add_rules));

        $res = WarehouseDivisionRuleService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 覆盖导入
     * @Permission(action='material.warehourse_division_rule.import_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74637
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function coverImportAddAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        $lock_key = md5(RedisKey::MATERIAL_STORE_STORAGE_IMPORT . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return WarehouseDivisionRuleService::getInstance()->importRuleTask($excel_file, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 覆盖导入-查询最后一次导入成功的结果
     * @Permission(action='material.warehourse_division_rule.import_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74642
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAddResultAction()
    {
        $result = WarehouseDivisionRuleService::getInstance()->getImportResult();
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 导出
     * @Permission(action='material.warehourse_division_rule.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74577
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WarehouseDivisionRuleService::$not_must_params);
        Validation::validate($params, WarehouseDivisionRuleService::$validate_list);
        $lock_key = md5(RedisKey::MATERIAL_STORE_STORAGE_EXPORT . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return WarehouseDivisionRuleService::getInstance()->export($params);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='material.warehourse_division_rule.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74552
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, WarehouseDivisionRuleService::$validate_update);
        $res = WarehouseDivisionRuleService::getInstance()->detail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 编辑
     * @Permission(action='material.warehourse_division_rule.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74547
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function saveAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(WarehouseDivisionRuleService::$validate_update, WarehouseDivisionRuleService::$validate_add_rules));

        $res = WarehouseDivisionRuleService::getInstance()->save($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除
     * @Permission(action='material.warehourse_division_rule.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74567
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function delAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WarehouseDivisionRuleService::$validate_update);

        $res = WarehouseDivisionRuleService::getInstance()->del($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }
}
