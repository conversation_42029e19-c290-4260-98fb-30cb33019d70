<?php
namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\BaseService;

/**
 * 资产领用申请
 * Class AssetApplyController
 * @package App\Modules\Material\Controllers
 */

class AssetApplyController extends BaseController
{
    /**
     * 资产领用申请默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62683
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = AssetApplyService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-查询
     * @Permission(action='material.asset_apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62705
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $res = AssetApplyService::getInstance()->getList($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-查看
     * @Permission(action='material.asset_apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62706
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_update);
        $res = AssetApplyService::getInstance()->detail($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-查看某出库单-出库路由
     * @Permission(action='material.asset_apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87419
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getOutboundTrackingInfoAction()
    {
        $params = trim_array($this->request->get());
        $res = AssetApplyService::getInstance()->getOutboundTrackingInfo($this->locale, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-添加默认配置项
     * @Permission(action='material.asset_apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62709
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = AssetApplyService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用申请-添加资产-标准型号列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62710
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_search_barcode);
        $res = AssetApplyService::getInstance()->searchBarcode($params, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-创建
     * @Permission(action='material.asset_apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62707
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_add);
        $res = AssetApplyService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-撤回
     * @Permission(action='material.asset_apply.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62711
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_cancel));
        $res = AssetApplyService::getInstance()->cancel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-重新提交
     * @Permission(action='material.asset_apply.recommit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62714
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function recommitAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_add));
        $res = AssetApplyService::getInstance()->recommit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请-收货人查询列表
     * @Permission(menu='material.asset_apply')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88169
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getConsigneeListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_consignee);
        $res = AssetApplyService::getInstance()->getConsigneeList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
