<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\LeaveAssetsManagerService;
use App\Modules\Material\Services\LeaveAssetsSetService;
use App\Modules\Material\Services\StandardService;

class LeaveAssetsSetController extends BaseController
{
    /**
     * 离职资产-设置-枚举
     * @Token
     * @api
     */
    public function getDefaultAction()
    {
        $res = LeaveAssetsSetService::getInstance()->getDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级确认任务-列表
     * @Permission(action='material.setting.leave_asset_set.manager.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     */
    public function managerListAction()
    {
        $params = $this->request->get();
        $res = LeaveAssetsSetService::getInstance()->getManagerList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级确认任务-查看
     * @Permission(action='material.setting.leave_asset_set.manager.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88238
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function managerInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_remind_delete);
        $res = LeaveAssetsSetService::getInstance()->getManagerInfo($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级确认任务-编辑
     * @Permission(action='material.setting.leave_asset_set.manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67377
     */
    public function managerEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_manager_edit);
        $res = LeaveAssetsSetService::getInstance()->managerEdit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级确认任务-新增
     * @Permission(action='material.setting.leave_asset_set.manager.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67372
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function managerAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_manager_add);
        $res = LeaveAssetsSetService::getInstance()->managerAdd($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-短信邮件设置-列表
     * @Permission(action='material.setting.leave_asset_set.remind.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     */
    public function remindListAction()
    {
        $params = $this->request->get();
        $res = LeaveAssetsSetService::getInstance()->getRemindList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-短信邮件设置-查看
     * @Permission(action='material.setting.leave_asset_set.remind.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88241
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function remindInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_remind_delete);
        $res = LeaveAssetsSetService::getInstance()->getRemindInfo($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-短信邮件设置-编辑
     * @Permission(action='material.setting.leave_asset_set.remind.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68452
     */
    public function remindEditAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_remind_edit);
        $res = LeaveAssetsSetService::getInstance()->remindEdit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-短信邮件设置-新增
     * @Permission(action='material.setting.leave_asset_set.remind.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67392
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function remindAddAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_remind_add);
        $res = LeaveAssetsSetService::getInstance()->remindAdd($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-短信邮件设置-删除
     * @Permission(action='material.setting.leave_asset_set.remind.delete')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function remindDeleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsSetService::$validate_remind_delete);
        $res = LeaveAssetsSetService::getInstance()->remindDelete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
