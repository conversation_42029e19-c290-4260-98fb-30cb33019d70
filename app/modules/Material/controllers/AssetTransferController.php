<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\AssetPdfService;
use App\Modules\Material\Services\AssetTransferExportService;
use App\Modules\Material\Services\AssetTransferListService;
use App\Modules\Material\Services\AssetTransferMessageService;
use App\Modules\Material\Services\AssetTransferService;
use App\Modules\Material\Services\BaseService;
use App\Modules\User\Services\UserService;


/**
 * 资产转移管理
 * Class AssetAccountController
 * @package App\Modules\Material\Controllers
 */
class AssetTransferController extends BaseController
{
    /**
     * 我的资产-获取页面枚举
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63448
     */
    public function getOptionsDefaultAction()
    {
        $res = AssetTransferListService::getInstance()->getOptionsDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的资产-列表
     * @Permission(action='material.my_asset.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62993
     */
    public function getMyAssetListAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->getList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的资产-详情
     * @Permission(action='material.my_asset.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62994
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->getDetail($params, $this->user, $this->locale);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我的资产-导出
     * @Permission(action='material.my_asset.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63450
     */
    public function exportMyAssetListAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MY_MATERIAL_ASSET_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferListService::getInstance()->export($params, $this->locale, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 我的资产-批量转移
     * @Permission(action='material.my_asset.batch_transfer')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63019
     */
    public function batchTransferAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_TRANSFER_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetTransfer($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 我转出-列表
     * @Permission(action='material.asset_transfer.from_me.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63021
     */
    public function getMyTransferListAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->getMyTransferList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我转出-转移时查我的资产列表
     * @Permission(action='material.asset_transfer.from_me.transfer')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63461
     */
    public function myTransferGetMyAssetListAction()
    {
        $params = $this->request->get();
        $params['status'] = [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE];
        $res = AssetTransferListService::getInstance()->getList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 我转出-转移【废弃的】
     * @Permission(action='material.asset_transfer.from_me.transfer')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63462
     */
    public function transferAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_TRANSFER_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetTransfer($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 我转出-撤销
     * @Permission(action='material.asset_transfer.from_me.cancel')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63022
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_CANCEL_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetCancel($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 我转出-提醒
     * @Permission(action='material.asset_transfer.from_me.remind')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63023
     */
    public function remindAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_REMIND_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->transferRemind($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 待接收-列表
     * @Permission(action='material.asset_transfer.to_me.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63029
     */
    public function getReceiverListAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->receiverList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 待接收-拒绝
     * @Permission(action='material.asset_transfer.to_me.reject')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63031
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_REJECT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetReject($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 待接收-接收
     * @Permission(action='material.asset_transfer.to_me.reception')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63032
     */
    public function receptionAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_RECEPTION_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetReception($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 生成资产同意书/生成资产申请
     * @Permission(action='material.asset_transfer.to_me.reception')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterfacenterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63469
     */
    public function getAssetsPdfAction()
    {
        $params = $this->request->get();
        // 参数验证
        Validation::validate($params, AssetTransferExportService::$export_validate);
        $country_code = get_country_code();
        //马来 - 资产管理同意书
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            $res = AssetPdfService::getInstance()->exportAgreePdfForMy($params, $this->user);
        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && $this->user['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
            //泰国 - 个人代理 - 资产管理同意书
            $res = AssetPdfService::getInstance()->exportAgreePdfForThPersonalAgent($params, $this->user);
        } else {
            $res = AssetTransferExportService::getInstance()->exportPdf($params, $this->user);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 全部转移明细-列表
     * @Permission(action='material.asset_transfer.all.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63033
     */
    public function getAllTransferListAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->getAllTransferList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产业务数据查询-列表
     * @Permission(action='material.search_transfer.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63061
     */
    public function getTransferSearchListAction()
    {
        $params = $this->request->get();
        $res = AssetTransferListService::getInstance()->getTransferSearchList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产业务数据查询-导出
     * @Permission(action='material.search_transfer.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63062
     */
    public function exportSearchListAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_TRANSFER_SEARCH_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferListService::getInstance()->exportSearch($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产业务数据查询-撤销
     * @Permission(action='material.search_transfer.cancel')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63500
     */
    public function assetCancelAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_CANCEL_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->assetCancel($params, $this->user, true);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产业务数据查询-批量提醒
     * @Permission(action='material.search_transfer.remind')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63501
     */
    public function assetRemindAction()
    {
        $params = $this->request->get();
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_REMIND_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetTransferService::getInstance()->transferRemind($params, $this->user, true);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产业务数据查询-列表显示字段设置
     * @Permission(action='material.search_transfer.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63523
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listSetAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_set);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AssetAccountService::getInstance()->setValue($params, $this->user, MaterialEnums::ASSET_TRANSFER_SET_USE_LIST);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
