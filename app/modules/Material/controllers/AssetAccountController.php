<?php
namespace App\Modules\Material\Controllers;

use App\Library\ApiClient;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\AssetAccountService;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetPdfService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\MaterialSettingService;
use App\Modules\Material\Services\StandardService;
use App\Modules\User\Services\StaffService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 资产台账管理
 * Class AssetAccountController
 * @package App\Modules\Material\Controllers
 */
class AssetAccountController extends BaseController
{
    /**
     * 资产台账默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/55867
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = AssetAccountService::getInstance()->getOptionsDefault($this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 资产台账-搜索使用人
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56095
     * @return Response|ResponseInterface
     */
    public function searchStaffAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_search_staff);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $result = StaffService::getInstance()->searchStaff($params);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $result['message'], $result['data']);
        }
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 资产台账-搜索barcode
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56089
     * @return Response|ResponseInterface
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_search_barcode);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $result = StandardService::getInstance()->searchBarcode($this->locale,$params);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $result['message'], $result['data']);
        }
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 资产台账-列表显示字段设置
     * @Permission(action='material.asset_account.list.set')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/55891
     * @return Response|ResponseInterface
     */
    public function listSetAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_set);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $result = AssetAccountService::getInstance()->setValue($params, $this->user);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $result['message'], $result['data']);
        }
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 资产台账-查询
     * @Permission(action='material.asset_account.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56131
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetAccountService::$not_must_params);
        try {
            Validation::validate($params, AssetAccountService::$validate_list_search_material_asset);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AssetAccountService::getInstance()->getList($this->user, $params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 资产台账-导出
     * @Permission(action='material.asset_account.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56137
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetAccountService::$not_must_params);
        Validation::validate($params, AssetAccountService::$validate_list_search_material_asset);
        MaterialSettingService::getInstance()->checkStaffDataPermission($this->user['id'], MaterialSettingService::DATA_PERMISSION_ASSET);
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_ACCOUNT, $params);
        }, $lock_key, 5);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产台账-查看
     * @Permission(action='material.asset_account.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56125
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_material_asset_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = AssetAccountService::getInstance()->detail($params);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产台账-根据部门获取所属公司
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56755
     * @return Response|ResponseInterface
     */
    public function getCompanyByDepartmentAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AssetAccountService::$validate_get_company);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = AssetAccountService::getInstance()->getCompanyByDepartment($params);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产台账-创建
     * @Permission(action='material.asset_account.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56113
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetAccountService::$not_must_params_add);
        Validation::validate($params, array_merge(AssetAccountService::$validate_material_asset_add, AssetAccountService::$validate_material_asset));
        $list = AssetAccountService::getInstance()->add($params, $this->user);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产台账-修改
     * @Permission(action='material.asset_account.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56119
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetAccountService::$not_must_params_add);
        Validation::validate($params, array_merge(AssetAccountService::$validate_material_asset_update, AssetAccountService::$validate_material_asset));
        $list = AssetAccountService::getInstance()->save($params, $this->user);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产台账-转移
     * @Permission(action='material.asset_account.transfer')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56143
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function transferAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(AssetAccountService::$validate_material_asset_update, AssetAccountService::$validate_material_asset_transfer));
        $res = AssetAccountService::getInstance()->transfer($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产台账-批量导入-导入新增
     * @Permission(action='material.asset_account.batch.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56149
     * @return Response|ResponseInterface
     */
    public function batchAddAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_ADD_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return AssetAccountService::getInstance()->batchAdd($excel_file, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产台账-批量导入-导入修改
     * @Permission(action='material.asset_account.batch.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56155
     * @return Response|ResponseInterface
     */
    public function batchSaveAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_UPDATE_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return AssetAccountService::getInstance()->batchSave($excel_file, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产台账-更多操作-批量转移
     * @Permission(action='material.asset_account.more.transfer')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56161
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchTransferAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetAccountService::$validate_material_asset_batch_transfer);
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_TRANSFER_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return AssetAccountService::getInstance()->batchTransfer($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }


    /**
     * 资产台账-批量导入-批量报废
     * @Permission(action='material.asset_account.more.scrap')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56155
     * @return Response|ResponseInterface
     */
    public function batchScrapAction()
    {
        try {
            // 文件格式校验
            [$excel_data,$file]       = AssetAccountService::getInstance()->batchScrapCheck();
            $type = ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE;
            $lock_key = md5(RedisKey::MATERIAL_ASSET_SCRAP_UPLOAD_LOCK . $this->user['id']);
            $res      = $this->atomicLock(function () use ($excel_data, $file, $type) {
                return AssetAccountService::getInstance()->addBatchUploadTask($file, $excel_data, $this->user, $type);
            }, $lock_key, 20);
        } catch (ValidationException $e) {

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
            $this->logger->warning('资产台账-批量导入-批量报废: ' . $res['message']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     * 资产台账-批量导入-导入修改财务
     * @Permission(action='material.asset_account.batch.finance')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/56155
     * @return Response|ResponseInterface
     */
    public function batchFinanceAction()
    {
        try {
            [$excel_data,$file]       = AssetAccountService::getInstance()->batchFinanceCheck();
            $type = ImportCenterEnums::TYPE_ASSET_ACCOUNT_FINANCE_UPDATE;
            $lock_key = md5(RedisKey::MATERIAL_ASSET_FINANCE_UPLOAD_LOCK . $this->user['id']);
            $res      = $this->atomicLock(function () use ($excel_data, $file, $type) {
                return AssetAccountService::getInstance()->addBatchUploadTask($file, $excel_data, $this->user, $type);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data']    = [];
            $this->logger->warning('资产台账-批量导入报废: ' . $res['message']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }


    /**
     * 资产台账-批量导入-批量报废弹出框数据接口
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63464
     * @return  Response|ResponseInterface
     **/
    public function batchScrapLastDataAction()
    {
        $result = AssetAccountService::getInstance()->batchScrapLastData();
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }


    /**
     * 资产台账-批量导入-导入修改财务弹出框数据接口
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63465
     * @return  Response|ResponseInterface
     **/
    public function batchFinanceLastDataAction()
    {
        $result = AssetAccountService::getInstance()->batchFinanceLastData();
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产台账-批量导入-导入转移
     * @Permission(action='material.asset_account.batch.transfer')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63527
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function batchImportTransferAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_BATCH_IMPORT_TRANSFER_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return AssetAccountService::getInstance()->batchImportTransfer($excel_file, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产台账-更多操作-批量退库
     * @Permission(action='material.asset_account.more.stock_return')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71887
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchStockReturnAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetAccountService::$validate_material_asset_batch_stock_return);
        $lock_key = md5(MaterialEnums::MATERIAL_ASSET_STOCK_RETURN_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return AssetAccountService::getInstance()->batchStockReturn($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 资产台账-导出协议-申请同意书
     * @Permission(action='material.asset_account.export_protocol.apply')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75277
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportProtocolApplyAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetAccountService::$validate_material_export_protocol);
        $params['type'] = MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_APPLY;
        $result = AssetAccountService::getInstance()->exportProtocol($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产台账-导出协议-资产同意书
     * @Permission(action='material.asset_account.export_protocol.agree')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75282
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportProtocolAgreeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetAccountService::$validate_material_export_protocol);
        $params['type'] = MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_AGREE;
        $result = AssetAccountService::getInstance()->exportProtocol($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * OA回调接口-SCM修改sn 码
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76632
     *
     */
    public function dealScmAction()
    {
        $data = $this->request->get();
        Validation::validate($data, AssetAccountService::$validate_material_recall);
        $res = AssetAccountService::getInstance()->dealScm($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
