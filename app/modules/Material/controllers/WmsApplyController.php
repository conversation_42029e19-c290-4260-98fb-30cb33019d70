<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\BaseService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 耗材申请
 * Class WmsApplyController
 * @package App\Modules\Material\Controllers
 */
class WmsApplyController extends BaseController
{
    /**
     * 耗材申请默认配置项
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66487
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = WmsApplyService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-查询
     * @Permission(action='material.wms_apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66512
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        try {
            Validation::validate($params, WmsApplyService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->getList($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-查看
     * @Permission(action='material.wms_apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66522
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsApplyService::$validate_share);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = WmsApplyService::getInstance()->detail($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 耗材申请-添加默认配置项
     * @Permission(action='material.wms_apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66487
     * @return Response|ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = WmsApplyService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材领用申请-添加耗材-标准型号列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66502
     * @return Response|ResponseInterface
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsApplyService::getInstance()->getValidateSearchBarcode());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->searchBarcode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     * 耗材申请-创建
     * @Permission(action='material.wms_apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66507
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsApplyService::$validate_add);
        $params['source_type'] = Enums::DATA_FROM_OA;
        $res = WmsApplyService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-撤回
     * @Permission(action='material.wms_apply.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66537
     * @return Response|ResponseInterface
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_cancel));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->cancel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-重新提交
     * @Permission(action='material.wms_apply.recommit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66542
     * @return Response|ResponseInterface
     */
    public function recommitAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_add));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->recommit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-历史审批日志-详情
     *
     * @Token
     * @return Response|ResponseInterface
     * @api
     */
    public function historyApprovalLogAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsApplyService::$validate_workflow_no);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->historyApprovalLog($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材管理-耗材申请-收货人查询列表
     * @Permission(action='material.wms_apply.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/72092
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function consigneeListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        Validation::validate($params, WmsApplyService::$validate_consignee);
        $res = WmsApplyService::getInstance()->consigneeList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量新增结果查询(最近一次)
     * @Permission(action='material.wms_apply.batch_add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87701
     * @return mixed
     */
    public function batchAddResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_MATERIAL_WMS_BATCH_ADD, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量新增
     * @Permission(action='material.wms_apply.batch_add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87704
     * @return mixed
     * @throws Exception
     */
    public function batchAddAction()
    {
        $lock_key = md5('material_wms_apply_batch_add_' . $this->user['id']);
        $res     = $this->atomicLock(function () {
            return WmsApplyService::getInstance()->batchAddToImportCenter($this->user);
        }, $lock_key, 1);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }


}
