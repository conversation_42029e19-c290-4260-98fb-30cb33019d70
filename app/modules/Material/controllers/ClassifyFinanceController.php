<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Services\ClassifyService;


class ClassifyFinanceController extends BaseController
{

    /**
     * 财务分类-列表
     * @Permission(action='material.classify_finance.list')
     * @Date: 2021-10-13 21:11
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33627
     * @return:
     **@author: peak pan
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$list_search);
            $params['type'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->getClassifyArr($params);
            $data = [
                'list' => $list['data'] ?? []
            ];
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $data);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 财务分类-添加
     * @Permission(action='material.classify_finance.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33634
     * @Date: 2021-10-13 21:12
     * @return:
     **@author: peak pan
     */
    public function addAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_material_finance_category);
            $list = ClassifyService::getInstance()->financeAdd($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 财务分类-编辑
     * @Permission(action='material.classify_finance.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33648
     * @Date: 2021-10-13 21:12
     * @return:
     **@author: peak pan
     */
    public function saveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(ClassifyService::$validate_save_material_category, ClassifyService::$validate_material_finance_category));
            $list = ClassifyService::getInstance()->financeSave($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 财务分类-删除
     * @Permission(action='material.classify_finance.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33683
     * @Date: 2021-10-13 21:13
     * @return:
     **@author: peak pan
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $params['classify'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->materialDel($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }

    /**
     * 财务分类-上移下移
     * @Permission(action='material.classify_finance.move')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33690
     * @Date: 2021-10-13 21:13
     * @return:
     **@author: peak pan
     */
    public function moveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $params['classify'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->materialMove($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }

    /**
     * 财务分类-查看
     * @Permission(action='material.classify_finance.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/35797
     * @Date: 2021-11-02 16:38
     * @author: peak pan
     * @return:
     **/
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $list = ClassifyService::getInstance()->materialFinanceDetail($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }
}
