<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\WmsPlanService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 库存盘点控制器
 */
class WmsPlanController extends BaseController
{

    /**
     * 枚举
     * @Token
     * @Date: 2022-05-12 16:53
     * @return:
     * yapi https://yapi.flashexpress.pub/project/133/interface/api/53665
     **@author: peak pan
     */
    public function getPlanDefaultAction()
    {
        $res = WmsPlanService::getInstance()->getPlanDefault();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 添加计划
     * @Permission(action='material.wms_plan.plan_add')
     * @Date: 2022-05-12 14:55
     * @return:
     * @author: peak pan
     * @api https://yapi.flashexpress.pub/project/133/interface/api/53671
     **/

    public function planAddAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_check_create);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = WmsPlanService::getInstance()->planAdd($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * 计划任务列表
     * @Permission(action='material.wms_plan.plan_list')
     * @Date: 2022-05-12 16:51
     * @return:
     * @author: peak pan
     * @api https://yapi.flashexpress.pub/project/133/interface/api/53713
     */
    public function planListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsPlanService::$not_must_params);
        try {
            Validation::validate($params, WmsPlanService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = WmsPlanService::getInstance()->planList($params, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 盘点计划删除
     * @Permission(action='material.wms_plan.del')
     * @Date: 2022-05-12 17:20
     * @return:
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/53725
     **@author: peak pan
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_check_id);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsPlanService::getInstance()->delPlan($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 检测盘点人有效性接口
     * @Token
     * @Date: 2022-05-12 17:28
     * @return:
     **@author: peak pan
     * https://yapi.flashexpress.pub/project/133/interface/api/53731
     */
    public function staffInfoAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, WmsPlanService::$validate_plan_staff_info);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = WmsPlanService::getInstance()->getStaffInfo($params);

        if ($list['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
        }
        return $this->returnJson($list['code'], $list['message']);
    }


    /**
     * 添加盘点计划导出接口
     * @Permission(action='material.wms_plan.plan_export')
     * @Date: 2022-05-12 17:30
     * @return:
     * https://yapi.flashexpress.pub/project/133/interface/api/53809
     **@author: peak pan
     */
    public function planExportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsPlanService::$not_must_params);
        try {
            Validation::validate($params, WmsPlanService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('material_plan_wms_staff_asset_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return WmsPlanService::getInstance()->getDetailExport($params, $this->locale);
        }, $lock_key, 60);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], ['file_url'=>$res['data']]);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 添加盘点计划查看接口
     * @Permission(action='material.wms_plan.detail')
     * @Date: 2022-05-12 17:32
     * @return:
     * @author: peak pan
     * https://yapi.flashexpress.pub/project/133/interface/api/53827
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_check_id);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsPlanService::getInstance()->infoPlanWms($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     * 添加盘点计划撤销接口
     * @Permission(action='material.wms_plan.revoke')
     * @Date: 2022-05-12 17:32
     * @return:
     **@author: peak pan
     * https://yapi.flashexpress.pub/project/133/interface/api/53851
     */
    public function revokeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_check_id);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsPlanService::getInstance()->revokePlan($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 盘点查询枚举getAssetsList
     * @Token
     * @Date: 2022-05-12 16:53
     * @return:
     * yapi https://yapi.flashexpress.pub/project/133/interface/api/53857
     * @author: peak pan
     */
    public function getPlanSkuEnumsAction()
    {
        $res = WmsPlanService::getInstance()->getPlanSkuEnums();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 盘点报表列表、查询接口
     * @Permission(action='material.wms_plan.plan_info_list')
     *
     * @Date: 2022-05-12 17:33
     * @return Response|ResponseInterface
     * @author: peak pan
     * https://yapi.flashexpress.pub/project/133/interface/api/53863
     */
    public function planInfoListAction()
    {
        $params = $this->request->get();

        $params = BaseService::handleParams($params, WmsPlanService::$not_must_params);
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_info_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsPlanService::getInstance()->planInfoList($params, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 导出未盘点的报表接口
     * @Permission(action='material.wms_plan.plan_info_export')
     * @Date: 2022-05-12 17:33
     * @return:
     * @author: peak pan
     * https://yapi.flashexpress.pub/project/133/interface/api/53869
     */
    public function planInfoExportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsPlanService::$not_must_params);
        try {
            Validation::validate($params, WmsPlanService::$validate_plan_info_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('material_plan_info_check_export_' . $this->user['id'] . '_' . $params['type']);
        $res = $this->atomicLock(function () use ($params) {
            // 库存盘点 - 盘点点名称 - 导出盘点报表
            if ($params['type'] == 1) {
                // 添加导出任务
                return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_WMS_PLAN_INFO_INVENTORY_REPORT, $params);
            } else {
                // 库存盘点 - 未盘点Export
                return WmsPlanService::getInstance()->getPlantaskExport($params, $this->locale);
            }

        }, $lock_key, 30);

        return $this->returnJson($res['code'], $res['message'], ['file_url' => !is_string($res['data']) ? '' : $res['data']]);
    }


    /**
     * 搜索网点
     * @Token
     * @Date: 2022-05-12 16:53
     * @return:
     * yapi https://yapi.flashexpress.pub/project/133/interface/api/56959
     **@author: peak pan
     */
    public function getSearchStoreAction()
    {
        $params = $this->request->get();
        $res = WmsPlanService::getInstance()->searchStore($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 盘点包材列表
     * @Permission(action='material.wms_plan.plan_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87668
     * @return Response|ResponseInterface
     */
    public function getBarcodeAction()
    {
        $params = $this->request->get();
        $res = WmsPlanService::getInstance()->searchBarcode($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 添加计划-批量导入网点
     * @Permission(action='material.wms_plan.plan_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87665
     * @return Response|ResponseInterface
     * @throws \Exception
     */
    public function importStoreAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::MATERIAL_WMS_PLAN_IMPORT_STORE_LOCK . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return WmsPlanService::getInstance()->importStore($excel_file);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}
