<?php

namespace App\Modules\Material\Controllers;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\WmsStockService;
use App\Library\Validation\ValidationException;
use App\Util\RedisKey;
use Phalcon\Http\ResponseInterface;

/**
 * 网点耗材（包材）进销存
 */
class WmsStockController extends BaseController
{
    /**
     * 列表
     * @Permission(menu='material.wms_purchase_sale_inventory')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91571
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, WmsStockService::$not_must_params);
        Validation::validate($params, WmsStockService::getInstance()->extendValidation());
        $res = WmsStockService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(menu='material.wms_purchase_sale_inventory')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/91568
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, WmsStockService::$not_must_params);
        Validation::validate($params, WmsStockService::getInstance()->extendValidation());
        $lock_key = md5(RedisKey::MATERIAL_PACKAGE_STOCK_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return WmsStockService::getInstance()->export($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }
}
