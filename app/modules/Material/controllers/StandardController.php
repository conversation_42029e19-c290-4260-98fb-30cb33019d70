<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Services\StandardBatchService;
use App\Modules\Material\Services\StandardService;
use App\Modules\Material\Services\BaseService;

class StandardController extends BaseController
{
    /**
     * 标准型号-添加
     * @Permission(action='material.standard.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33991
     * @Date: 2021-10-13 21:24
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function addAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $params = BaseService::handleParams($params, StandardService::$not_must_add_params);
        try {
            $validate = StandardService::getInstance()->validateMaterialSau($params);
            Validation::validate($params, $validate);
            $list = StandardService::getInstance()->sauAdd($params, $this->user);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 标准型号管理列表
     * @Permission(action='material.standard.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33998
     * @Date: 2021-10-13 21:24
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StandardService::$not_must_params);
        try {
            Validation::validate($params, StandardService::$validate_list_search_material_sau);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardService::getInstance()->getList($this->locale, $params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 标准型号-导出
     * @Permission(action='material.standard.download')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34068
     * @Date: 2021-10-20 14:50
     * @return:
     **@author: peak pan
     */
    public function downloadAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, StandardService::$not_must_params);
        try {
            Validation::validate($params, StandardService::$validate_list_search_material_sau);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(MaterialClassifyEnums::MATERIAL_SAU_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return StandardService::getInstance()->exportSau($this->locale, $params, $this->user['id']);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 标准型号管理启停用
     * @Permission(action='material.standard.setup')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34873
     * @Date: 2021-10-13 21:27
     * @return:
     **@author: peak pan
     */
    public function setupAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, StandardService::$validate_setup_material_sau);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardService::getInstance()->materialChangeStatus($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 标准型号管理删除
     * @Permission(action='material.standard.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34880
     * @Date: 2021-10-13 21:28
     * @return:
     **@author: peak pan
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, StandardService::$validate_material_sau_save);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardService::getInstance()->materialSauDel($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 标准型号管理详情
     * @Permission(action='material.standard.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34019
     * @Date: 2021-10-20 11:28
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, StandardService::$validate_material_sau_save);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardService::getInstance()->materialSauDetail($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 标准型号管理编辑
     * @Permission(action='material.standard.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34103
     * @Date: 2021-10-13 21:28
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function saveAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $params = BaseService::handleParams($params, StandardService::$not_must_add_params);
        try {
            $validate = StandardService::getInstance()->validateMaterialSau($params);
            $validate_material = array_merge(StandardService::$validate_material_sau_save, $validate);
            Validation::validate($params, $validate_material);
            $list = StandardService::getInstance()->save($params, $this->user);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 查询最后一次导入成功的结果
     * @Permission(action='material.standard.import.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/9/6
     * 接口地址 https://yapi.flashexpress.pub/project/133/interface/api/62903
     */
    public function importAddResultAction()
    {
        $types = ImportCenterEnums::TYPE_BARCODE_ADD;
        $result = ImportCenterService::getInstance()->getBarcodeImportResult($types);
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 标准型号导入新增
     * @Permission(action='material.standard.import.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/7/18
     * 接口地址 https://yapi.flashexpress.pub/project/133/interface/api/62487
     */
    public function importBarcodeAddAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'));

            }
            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'));
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,// barcode
                ])
                ->getSheetData();
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }
            // 去掉表头
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            $type = ImportCenterEnums::TYPE_BARCODE_ADD;
            $lock_key = md5('standard_batch_import_add' . $this->user['id']);
            $res = $this->atomicLock(function () use ($excel_data, $file, $type) {
                return StandardBatchService::getInstance()->barcodeBatchUploadTask($file, $excel_data, $this->user, $type);
            }, $lock_key, 10);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('标准型号-批量导入新增失败: ' . $res['message']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查询最后一次导入成功的结果
     * @Permission(action='material.standard.import.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/9/6
     * 接口地址 https://yapi.flashexpress.pub/project/133/interface/api/62902
     */
    public function importUpdateResultAction()
    {
        $types = ImportCenterEnums::TYPE_BARCODE_UPDATE;
        $result = ImportCenterService::getInstance()->getBarcodeImportResult($types);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 标准型号导入编辑
     * @Permission(action='material.standard.import.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/7/18
     * https://yapi.flashexpress.pub/project/133/interface/api/62498
     */
    public function importBarcodeUpdateAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'));
            }
            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'));
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,// barcode
                ])
                ->getSheetData();
            $excel_header_column_one = array_shift($excel_data);
            $excel_header_column_two = array_shift($excel_data);
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }

            $type = ImportCenterEnums::TYPE_BARCODE_UPDATE;
            $lock_key = md5('standard_batch_import_update' . $this->user['id']);
            $res = $this->atomicLock(function () use ($excel_data, $file, $type) {
                return StandardBatchService::getInstance()->barcodeBatchUploadTask($file, $excel_data, $this->user, $type);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('标准型号-批量导入修改失败: ' . $res['message']);
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
