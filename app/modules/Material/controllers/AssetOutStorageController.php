<?php
namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\MaterialAssetOutStorageEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\AssetOutStorageService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\MaterialSettingService;
use App\Modules\Material\Services\WmsOutStorageService;
use App\Util\RedisKey;

class AssetOutStorageController extends BaseController
{
    /**
     * 资产领用-资产领用默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61426
     */
    public function getOptionsDefaultAction()
    {
        $res = AssetOutStorageService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-添加默认配置项
     * @Permission(action='material.asset_out_storage.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61516
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = AssetOutStorageService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-直接新增
     * @Permission(action='material.asset_out_storage.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61894
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $validate_param = AssetOutStorageService::getInstance()->getExtendValidation($params, AssetOutStorageService::$validate_material_add);
        Validation::validate($params, $validate_param);
        //14828需求，增加去SCM实时查询available Inventory，若库存小于本次发放数量，后端增加限制，不可提交
        AssetOutStorageService::getInstance()->checkAvailableInventory($params);
        $res = AssetOutStorageService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-添加资产-标准型号列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61723
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetOutStorageService::$not_must_params);
        Validation::validate($params, AssetOutStorageService::$validate_search_barcode);
        //出库只要同步至SCM的
        $params['update_to_scm'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
        //出库只要启用的
        $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
        $res = AssetOutStorageService::getInstance()->searchBarcode($this->locale, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-添加资产-资产台账列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61741
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchAssetAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetOutStorageService::$not_must_params);
        Validation::validate($params, AssetOutStorageService::$validate_search_asset);
        $res = AssetOutStorageService::getInstance()->searchAsset($this->locale, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-列表
     * @Permission(action='material.asset_out_storage.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61912
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $params = BaseService::handleParams($params, AssetOutStorageService::$not_must_params);
        Validation::validate($params, AssetOutStorageService::$validate_list_search);
        $res = AssetOutStorageService::getInstance()->getList($this->user, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-导出
     * @Permission(action='material.asset_out_storage.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61453
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetOutStorageService::$not_must_params);
        Validation::validate($params, AssetOutStorageService::$validate_list_search);

        MaterialSettingService::getInstance()->checkStaffDataPermission($this->user['id'], MaterialSettingService::DATA_PERMISSION_ASSET_OUT);

        $lock_key = md5(MaterialAssetOutStorageEnums::MATERIAL_ASSET_OUT_STORAGE_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_OUT_STORAGE, $params);
        }, $lock_key, 5);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 领用出库-作废
     * @Permission(action='material.asset_out_storage.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61930
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetOutStorageService::$validate_Id);
        $res = AssetOutStorageService::getInstance()->cancel($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-详情
     * @Permission(action='material.asset_out_storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61939
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetOutStorageService::$validate_Id);
        $res = AssetOutStorageService::getInstance()->detail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-名下资产
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61777
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function ownerAssetAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetOutStorageService::$not_must_params);
        Validation::validate($params, AssetOutStorageService::$validate_owner_asset);
        $res = AssetOutStorageService::getInstance()->ownerAsset($this->locale,$params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * OA回调接口-SCM出库成功
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61948
     * @throws ValidationException
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function dealScmAction()
    {
        $data = $this->request->get();
        //执行参数校验
         $wms_validate =  !empty($data['bizExt']) && $data['bizExt'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS;
        if ($wms_validate) {
            Validation::validate($data, AssetOutStorageService::$validate_recall_param_wms);
            $lock_key = md5(MaterialEnums::MATERIAL_DEAL_SCM_LOCK . '_' . $data['orderSn']);
            $res = $this->atomicLock(function () use ($data) {
                return WmsOutStorageService::getInstance()->wmsScmCallback($data);
            }, $lock_key, 30);
        } else {
            Validation::validate($data, AssetOutStorageService::$validate_recall_param);
            $lock_key = md5(MaterialEnums::MATERIAL_DEAL_SCM_LOCK . '_' . $data['orderSn']);
            $res = $this->atomicLock(function () use ($data) {
                return AssetOutStorageService::getInstance()->dealScmStatus($data);
            }, $lock_key, 30);
        }
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 领用出库-关联申请单-列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62852
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getApplyListAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $list = AssetOutStorageService::getInstance()->getApplyList($params, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 领用出库-关联申请单-详情
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62853
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getApplyInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_update);
        $res = AssetOutStorageService::getInstance()->getApplyInfo($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-关联申请单-资产明细列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62856
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getApplyProductListAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        Validation::validate($params, AssetOutStorageService::$validate_apply_param);
        $list = AssetOutStorageService::getInstance()->getApplyProductList($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 领用出库-关联申请单
     * @Permission(action='material.asset_out_storage.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62858
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function relatedApplyAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        $validate_param = array_merge(AssetOutStorageService::$validate_related_apply_param, AssetOutStorageService::$validate_material_add);
        $validate_param = AssetOutStorageService::getInstance()->getExtendValidation($params, $validate_param);
        Validation::validate($params, $validate_param);
        //14828需求，增加去SCM实时查询available Inventory，若库存小于本次发放数量，后端增加限制，不可提交
        AssetOutStorageService::getInstance()->checkAvailableInventory($params);
        $res = AssetOutStorageService::getInstance()->relatedApply($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
    * 领用发放-资产明细-查看
    * @Permission(action='material.asset_out_storage.info_detail_list')
    * @Date: 8/31/22 10:33 AM
    * @author: peak pan
    * @return:
    * @api https://yapi.flashexpress.pub/project/133/interface/api/62854
    * @throws ValidationException
    **/
    public function infoDetailListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetOutStorageService::$info_detail_list_params);
        $res = AssetOutStorageService::getInstance()->getInfoDetailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 领用出库-导入新增
     * @Permission(action='material.asset_out_storage.import_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77002
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function importAddAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        $lock_key = md5(RedisKey::MATERIAL_ASSET_OUT_STORAGE_IMPORT_ADD_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return AssetOutStorageService::getInstance()->importAddTask($excel_file, $this->user);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 导入新增-查询最后一次导入成功的结果
     * @Permission(action='material.asset_out_storage.import_add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76982
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAddResultAction()
    {
        $result = AssetOutStorageService::getInstance()->getImportResult();
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }
}
