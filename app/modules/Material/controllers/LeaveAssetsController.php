<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\LeaveAssetsExcelService;
use App\Modules\Material\Services\LeaveAssetsService;
use App\Util\RedisKey;

class LeaveAssetsController extends BaseController
{
    /**
     * 离职资产-资产部处理-枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67307
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getDefaultAction()
    {
        $res = LeaveAssetsService::getInstance()->getDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-列表
     * @Permission(action='material.leave_asset.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67292
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_list);
        Validation::validate($params, LeaveAssetsService::$validate_list);
        $res = LeaveAssetsService::getInstance()->getList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页详情
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67332
     */
    public function editDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetail($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-查看页详情
     * @Permission(action='material.leave_asset.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68267
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetail($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页详情
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetail($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页统计
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75692
     */
    public function editDetailCountAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetailCount($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-查看页统计
     * @Permission(action='material.leave_asset.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75692
     */
    public function getDetailCountAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetailCount($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页转移列表
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69522
     */
    public function getEditTransferListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_edit_transfer_list);
        $res = LeaveAssetsService::getInstance()->getEditTransferList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-查看页转移列表
     * @Permission(action='material.leave_asset.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75682
     */
    public function getViewTransferListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_edit_transfer_list);
        $res = LeaveAssetsService::getInstance()->getEditTransferList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页详情页列表
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function editAssetsListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_edit_list);
        Validation::validate($params, LeaveAssetsService::$validate_edit_list);
        $res = LeaveAssetsService::getInstance()->getEditAssetsList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-查看页详情列表
     * @Permission(action='material.leave_asset.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function getAssetsListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_edit_list);
        Validation::validate($params, LeaveAssetsService::$validate_edit_list);
        $res = LeaveAssetsService::getInstance()->getEditAssetsList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-编辑页提交(暂存)
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67342
     */
    public function saveAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        Validation::validate($params, LeaveAssetsService::$validate_edit_save);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_SAVE_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsService::getInstance()->editSave($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-资产部处理-编辑页添加
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67932
     */
    public function addAssetsAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_edit_add);
        Validation::validate($params, LeaveAssetsService::$validate_edit_add);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_ADD_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsService::getInstance()->editAdd($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-资产部处理-编辑页删除自己添加的
     * @Permission(action='material.leave_asset.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function deleteAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        Validation::validate($params, LeaveAssetsService::$validate_delete);
        //TODO 下次前端加上参数id, 然后加redis锁
        //$lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_DELETE_LOCK . '_' . $params['id']);
        $res = LeaveAssetsService::getInstance()->editDelete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-批量编辑
     * @Permission(action='material.leave_asset.upload_batch')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67347
     */
    public function batchUploadAssetsAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('leave_asset_not_found_file'));
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'));
            }

            ini_set('memory_limit', '512M');

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,// barcode
                ])
                ->getSheetData();
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }
            // 去掉表头
            array_shift($excel_data);
            $lock_key = md5(RedisKey::LEAVE_ASSET_BATCH_EDIT_LOCK . $this->user['id']);
            $res = $this->atomicLock(function () use ($excel_data, $file) {
                return LeaveAssetsExcelService::getInstance()->batchEditByFile($file, $excel_data, $this->user);
            }, $lock_key, 10);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('标准型号-批量导入新增失败: ' . $res['message']);
        }
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 查询最后一次导入成功的结果
     * @Permission(action='material.leave_asset.upload_batch')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAddResultAction()
    {
        $types = ImportCenterEnums::TYPE_LEAVE_ASSET_BATCH_EDIT;
        $result = ImportCenterService::getInstance()->getBarcodeImportResult($types);
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 离职资产-资产部处理-操作记录列表
     * @Permission(action='material.leave_asset.record')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function recordListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_record_list);
        Validation::validate($params, LeaveAssetsService::$validate_record_list);
        $res = LeaveAssetsService::getInstance()->getRecordList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-导出
     * @Permission(action='material.leave_asset.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/67322
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_list);
        Validation::validate($params, LeaveAssetsExcelService::$validate_list_export);

        if (isset($params['pageSize'])) {
            unset($params['pageSize']);
        }
        if (isset($params['pageNum'])) {
            unset($params['pageNum']);
        }
        //固定条件有效数据
        $params['is_valid'] = MaterialEnums::LEAVE_ASSET_IS_VALID_YES;
        $lock_key = md5(RedisKey::LEAVE_ASSET_EXPORT_LOCK . '_' . $this->user['id']);

        $res = $this->atomicLock(function () use ($params) {
            $count = LeaveAssetsExcelService::getInstance()->getExcelCount($params, $this->user);
            // 大于指定数量, 添加异步任务 导出
            if ($count > MaterialEnums::LEAVE_ASSETS_EXPORT_MAX) {
                $excel_type = MaterialEnums::$excel_type[$params['export_type']];
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], $excel_type, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = LeaveAssetsExcelService::getInstance()->exportSearch($params, $this->locale, $this->user, $count);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 5);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 离职资产-资产部处理-更新页详情
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75687
     */
    public function updateDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetail($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-更新-详情页列表
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function updateAssetsListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_edit_list);
        Validation::validate($params, LeaveAssetsService::$validate_edit_list);
        $res = LeaveAssetsService::getInstance()->getEditAssetsList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-更新页统计
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75692
     */
    public function updateDetailCountAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_detail);
        $res = LeaveAssetsService::getInstance()->getDetailCount($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-更新页转移列表
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75707
     */
    public function getUpdateTransferListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_edit_transfer_list);
        $res = LeaveAssetsService::getInstance()->getEditTransferList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-更新页提交(暂存)
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76722
     */
    public function updateAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        Validation::validate($params, LeaveAssetsService::$validate_update_save);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_SAVE_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsService::getInstance()->editSave($params, $this->user, true);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-资产部处理-更新页添加
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76717
     */
    public function updateAddAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        $params = BaseService::handleParams($params, LeaveAssetsService::$not_must_edit_add);
        Validation::validate($params, LeaveAssetsService::$validate_edit_add);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_ADD_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsService::getInstance()->editAdd($params, $this->user, true);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-资产部处理-更新页删除自己添加的
     * @Permission(action='material.leave_asset.update')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function updateDeleteAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        //TODO 下次前端加上参数id, 然后加redis锁
        //$lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_DELETE_LOCK . '_' . $params['id']);

        Validation::validate($params, LeaveAssetsService::$validate_delete);
        $res = LeaveAssetsService::getInstance()->editDelete($params, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-资产部处理-批量确认
     * @Permission(action='material.leave_asset.batch_confirm')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/72187
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function batchConfirmAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsService::$validate_batch_confirm);
        $lock_key = md5(RedisKey::LEAVE_ASSET_BATCH_CONFIRM_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return LeaveAssetsService::getInstance()->batchConfirm($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }
}
