<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\BaseService;

/**
 * 耗材申请-审批
 * Class WmsApplyAuditController
 * @package App\Modules\Material\Controllers
 */
class WmsApplyAuditController extends BaseController
{
    /**
     * 耗材申请审核-查询列表
     * @Permission(action='material.wms_apply_audit.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66557
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        try {
            Validation::validate($params, WmsApplyService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->getAuditList($params, $this->user);

        return $this->returnJson($res['code'], $res['message'], $res['data']);

    }

    /**
     * 耗材申请审核-查看
     * @Permission(action='material.wms_apply_audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66577
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, WmsApplyService::$validate_share);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->detail($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请审核-驳回
     * @Permission(action='material.wms_apply_audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66582
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_reject, WmsApplyService::$validate_pass));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->reject($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请审核-同意
     * @Permission(action='material.wms_apply_audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66587
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function passAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(WmsApplyService::$validate_share, WmsApplyService::$validate_pass));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = WmsApplyService::getInstance()->pass($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请审核-待处理-导出
     * @Permission(action='material.wms_apply_audit.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70722
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        Validation::validate($params, WmsApplyService::$validate_list_search);
        $lock_key = md5(MaterialWmsEnums::MATERIAL_WMS_APPLY_EXPORT_LOCK . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_WMS_APPLY, $params);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 耗材申请审核-待处理-搜索Barcode
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70727
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsApplyService::$validate_audit_search_barcode);
        $res = WmsApplyService::getInstance()->searchBarcodeList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 耗材管理-耗材审核- 批量审核-上传成功和失败数量
     * @Permission(action='material.wms_apply_audit.batch_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70732
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAuditResultAction()
    {
        $params['type']     = ImportCenterEnums::TYPE_MATERIAL_WMS_AUDIT_UPDATE;
        $params['staff_id'] = $this->user['id'];
        $result             = WmsApplyService::getInstance()->getImportAuditResult($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 耗材管理-耗材审核-批量审核-上传保存
     * @Permission(action='material.wms_apply_audit.batch_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/70737
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAuditAddAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }
            $file      = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }
            $config = ['path' => ''];
            $excel  = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    0  => \Vtiful\Kernel\Excel::TYPE_STRING,//耗材申请单
                    1  => \Vtiful\Kernel\Excel::TYPE_STRING,//barcode
                    14 => \Vtiful\Kernel\Excel::TYPE_STRING,//审批结果
                ])
                ->getSheetData();
            array_shift($excel_data);//删除第一行
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }
            //大于5000行
            if (count($excel_data) > MaterialWmsEnums::MATERIAL_WMS_AUDIT_MAX_NUMBER) {
                throw new ValidationException($this->t->_('material_wms_apply_export_max_error'), ErrCode::$VALIDATE_ERROR);
            }
            $type     = ImportCenterEnums::TYPE_MATERIAL_WMS_AUDIT_UPDATE;
            $lock_key = md5(MaterialWmsEnums::material_wms_apply_all_audit_export_lock . $this->user['id']);
            $res      = $this->atomicLock(function () use ($file, $type) {
                return WmsApplyService::getInstance()->addBatchUploadTask($file, $this->user, $type);
            }, $lock_key, 10);
        } catch (ValidationException $e) {
            $res['code']    = ErrCode::$VALIDATE_ERROR;
            $res['message'] = $e->getMessage();
        } catch (\Exception $e) {
            $res['code']    = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $this->logger->warning('耗材管理 耗材审核 批量审核 上传 失败: ' . $res['message']);
        }
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}
