<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\AssetPdfService;
use App\Modules\Material\Services\AssetReturnService;
use App\Modules\Material\Services\AssetTransferExportService;
use App\Modules\Material\Services\AssetTransferListService;
use App\Modules\Material\Services\AssetTransferMessageService;
use App\Modules\Material\Services\AssetTransferService;
use App\Modules\Material\Services\BaseService;
use App\Modules\User\Services\UserService;
use App\Util\RedisKey;


/**
 * 资产退回处理
 * Class AssetReturnController.php
 * @package App\Modules\Material\Controllers
 */
class AssetReturnController extends BaseController
{
    /**
     * 资产退回处理-枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85769
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = AssetReturnService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-列表
     * @Permission(action='material.asset_return.wait.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85823
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getWaitListAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-导出
     * @Permission(action='material.asset_return.wait.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85832
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportWaitListAction()
    {
        $params = trim_array($this->request->get());
        $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
        Validation::validate($params, AssetReturnService::$return_list_validate);

        // 加锁处理
        $lock_key = md5(RedisKey::ASSET_RETURN_WAIT_EXPORT . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $params['flag'] = AssetReturnService::LIST_TYPE_WAIT;
            $total = AssetReturnService::getInstance()->getListCount($params, $this->user);
            if ($total > MaterialEnums::RETURN_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_RETURN_WAIT_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = AssetReturnService::getInstance()->export($params, $this->user);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 资产退回处理-待处理-查看
     * @Permission(action='material.asset_return.wait.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85940
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getWaitDetailAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-批量收货
     * @Permission(action='material.asset_return.wait.receipt')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85850
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function batchReceiptAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$batch_id_validate);

        $lock_key = md5( RedisKey::ASSET_RETURN_WAIT_BATCH_RECEIPT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->batchReceipt($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-待处理-批量拒绝
     * @Permission(action='material.asset_return.wait.reject')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85868
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function batchRejectAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$batch_reject_validate);

        $lock_key = md5(RedisKey::ASSET_RETURN_WAIT_BATCH_REJECT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->batchReject($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-待处理-完成工单
     * @Permission(action='material.asset_return.wait.finish')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85877
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function batchFinishAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$batch_id_validate);

        $lock_key = md5(RedisKey::ASSET_RETURN_WAIT_BATCH_FINISH_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->batchFinish($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-待处理-处理
     * @Permission(action='material.asset_return.wait.handle')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85904
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function handleAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$asset_handle_validate);
        $res = AssetReturnService::getInstance()->assetHandle($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-转交
     * @Permission(action='material.asset_return.wait.transfer')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85901
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function transferAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$transfer_validate);
        $res = AssetReturnService::getInstance()->transfer($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-创建scm退库单-初始化信息
     * @Permission(action='material.asset_return.wait.storage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85922
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function getStorageDefaultAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AssetReturnService::$batch_id_validate);
        $res = AssetReturnService::getInstance()->getStorageDefault($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-待处理-创建scm退库单
     * @Permission(action='material.asset_return.wait.storage')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85925
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function createStorageAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AssetReturnService::getInstance()->getStorageValidate($params));

        $lock_key = md5(RedisKey::ASSET_RETURN_WAIT_CREATE_STORAGE_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->createStorage($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-已转交-列表
     * @Permission(action='material.asset_return.transfer.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85826
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getTransferListAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->getList($params, $this->user, AssetReturnService::LIST_TYPE_TRANSFER);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-已转交-导出
     * @Permission(action='material.asset_return.transfer.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85835
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportTransferListAction()
    {
        $params = trim_array($this->request->get());
        $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
        Validation::validate($params, AssetReturnService::$return_list_validate);

        // 加锁处理
        $lock_key = md5(RedisKey::ASSET_RETURN_TRANSFER_EXPORT . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $params['flag'] = AssetReturnService::LIST_TYPE_TRANSFER;
            $total = AssetReturnService::getInstance()->getListCount($params, $this->user);
            if ($total > MaterialEnums::RETURN_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_RETURN_TRANSFER_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = AssetReturnService::getInstance()->export($params, $this->user);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 资产退回处理-已转交-查看
     * @Permission(action='material.asset_return.transfer.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85943
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getTransferDetailAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-已处理-列表
     * @Permission(action='material.asset_return.done.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85829
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getDoneListAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->getList($params, $this->user, AssetReturnService::LIST_TYPE_DONE);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-已处理-导出
     * @Permission(action='material.asset_return.done.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85838
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportDoneListAction()
    {
        $params = trim_array($this->request->get());
        $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
        Validation::validate($params, AssetReturnService::$return_list_validate);

        // 加锁处理
        $lock_key = md5(RedisKey::ASSET_RETURN_DONE_EXPORT . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $params['flag'] = AssetReturnService::LIST_TYPE_DONE;
            $total = AssetReturnService::getInstance()->getListCount($params, $this->user);
            if ($total > MaterialEnums::RETURN_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_RETURN_DONE_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = AssetReturnService::getInstance()->export($params, $this->user);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 资产退回处理-已处理-查看
     * @Permission(action='material.asset_return.done.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85946
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getDoneDetailAction()
    {
        $params = $this->request->get();
        $res = AssetReturnService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-退货入库通知单-列表
     * @Permission(action='material.asset_return.storage.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85949
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function getStorageListAction()
    {
        $params = trim_array($this->request->get());
        $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
        Validation::validate($params, AssetReturnService::$return_storage_list_validate);

        $res = AssetReturnService::getInstance()->getStorageList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-退货入库通知单-导出
     * @Permission(action='material.asset_return.storage.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85952
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function exportStorageListAction()
    {
        $params = trim_array($this->request->get());
        $params = AssetReturnService::handleParams($params, AssetReturnService::$return_not_must_params);
        Validation::validate($params, AssetReturnService::$return_storage_list_validate);

        $lock_key = md5(RedisKey::ASSET_RETURN_STORAGE_EXPORT . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $total = AssetReturnService::getInstance()->getStorageListCount($params, true);
            if ($total > MaterialEnums::RETURN_ASSET_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_RETURN_STORAGE_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                $result = AssetReturnService::getInstance()->exportStorageList($params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-退货入库通知单-查看
     * @Permission(action='material.asset_return.storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85958
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getStorageDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$id_validate);

        $res = AssetReturnService::getInstance()->getStorageDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-退货入库通知单-查看-退库入库单信息
     * @Permission(action='material.asset_return.storage.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85961
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getStorageDetailProductAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetReturnService::$storage_detail_product_validate);

        $res = AssetReturnService::getInstance()->getStorageDetailProduct($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产退回处理-退货入库通知单-撤回
     * @Permission(action='material.asset_return.storage.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85934
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function cancelStorageAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AssetReturnService::$cancel_storage_validate);

        $lock_key = md5(RedisKey::ASSET_RETURN_CANCEL_STORAGE_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->cancelStorage($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 资产退回处理-退货入库通知单-删除
     * @Permission(action='material.asset_return.storage.delete')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87233
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function deleteStorageAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AssetReturnService::$id_validate);

        $lock_key = md5(RedisKey::ASSET_RETURN_DELETE_STORAGE_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AssetReturnService::getInstance()->deleteStorage($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }
}
