<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\AssetAccountService;
use App\Modules\Material\Services\BaseService;
use App\Modules\Material\Services\LeaveAssetsManagerService;
use App\Modules\Material\Services\StandardService;
use App\Util\RedisKey;

class LeaveAssetsManagerController extends BaseController
{
    /**
     * 离职资产-上级离职资产管理-枚举
     * @Token
     * @api
     */
    public function getDefaultAction()
    {
        $res = LeaveAssetsManagerService::getInstance()->getDefault();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 离职资产-搜索barcode
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchBarcodeAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetAccountService::$validate_search_barcode);
        $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
        $result = StandardService::getInstance()->searchBarcode($this->locale, $params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 离职资产-上级离职资产管理-列表
     * @Permission(action='material.leave_asset_manager.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, LeaveAssetsManagerService::$not_must_list);
        Validation::validate($params, LeaveAssetsManagerService::$validate_list);
        $res = LeaveAssetsManagerService::getInstance()->getList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页详情
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function editDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsManagerService::$validate_detail);
        $res = LeaveAssetsManagerService::getInstance()->getDetail($params, $this->locale);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-查看页详情
     * @Permission(action='material.leave_asset_manager.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsManagerService::$validate_detail);
        $res = LeaveAssetsManagerService::getInstance()->getDetail($params, $this->locale);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-查询barcode维度的统计
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     */
    public function editGetBarcodeCountAction()
    {
        $params = $this->request->get();
        $res = LeaveAssetsManagerService::getInstance()->editGetBarcodeCount($params, $this->user, $this->locale);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页列表
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function editAssetsListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsManagerService::$validate_edit_list);
        $res = LeaveAssetsManagerService::getInstance()->getEditAssetsList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页列表
     * @Permission(action='material.leave_asset_manager.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function getAssetsListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, LeaveAssetsManagerService::$validate_edit_list);
        $res = LeaveAssetsManagerService::getInstance()->getEditAssetsList($params, $this->locale, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页提交(暂存)
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function saveAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        Validation::validate($params, LeaveAssetsManagerService::$validate_edit_save_main);
        foreach ($params['data'] as $params_data) {
            $params_data = BaseService::handleParams($params_data, LeaveAssetsManagerService::$no_must_edit_save_detail);
            Validation::validate($params_data, LeaveAssetsManagerService::$validate_edit_save_detail);
        }
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_SAVE_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsManagerService::getInstance()->editSave($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页添加
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function addAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        $params = BaseService::handleParams($params, LeaveAssetsManagerService::$no_must_edit_add);
        Validation::validate($params, LeaveAssetsManagerService::$validate_edit_add);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_ADD_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsManagerService::getInstance()->editAdd($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-上级离职资产管理-编辑页删除自己添加的
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     */
    public function deleteAssetsAction()
    {
        $params = $this->request->get();
        //TODO 下次前端加上参数id, 然后加redis锁
        //$lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_DELETE_LOCK . '_' . $params['id']);
        $res = LeaveAssetsManagerService::getInstance()->editDelete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 离职资产-上级离职资产管理-批量编辑
     * @Permission(action='material.leave_asset_manager.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api
     */
    public function batchSaveAssetsAction()
    {
        $params = $this->request->get();
        //参数验证
        Validation::validate($params, LeaveAssetsManagerService::$validate_batch_save);
        $lock_key = md5(RedisKey::LEAVE_ASSET_EDIT_SAVE_LOCK . '_' . $params['id']);
        $res = $this->atomicLock(function () use ($params) {
            return LeaveAssetsManagerService::getInstance()->editBatchSave($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 离职资产-上级离职资产管理-批量确认
     * @Permission(action='material.leave_asset_manager.batch_confirm')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api
     */
    public function batchConfirmAction()
    {
        $params = $this->request->get();
        $res = LeaveAssetsManagerService::getInstance()->batchConfirm($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
