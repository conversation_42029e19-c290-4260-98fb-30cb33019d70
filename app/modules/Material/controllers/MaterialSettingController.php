<?php

namespace App\Modules\Material\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\MaterialSettingService;
use App\Modules\Material\Services\BaseService;

class MaterialSettingController extends BaseController
{
    /**
     * 通用设置 - 枚举配置
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85625
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = MaterialSettingService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 配置[出库单必填成本中心]-添加
     * 接口地址: https://yapi.flashexpress.pub/project/133/interface/api/62727
     * @Permission(action='material.setting.management.save')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/8/15
     */
    public function saveAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, MaterialSettingService::$not_must_params);
        try {
            Validation::validate($params, MaterialSettingService::$validate_save);
            $list = MaterialSettingService::getInstance()->save($params, $this->user);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 配置[出库单必填成本中心]-获取配置
     * 接口地址: https://yapi.flashexpress.pub/project/133/interface/api/62728
     * @Permission(action='material.setting.management.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @date 2022/8/15
     */
    public function getSettingAction()
    {
        $list = MaterialSettingService::getInstance()->getPcCodeSetting();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 资产盘点组
     * @Permission(action='material.setting.inventory_group.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77772
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function inventoryGroupListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ['name', 'staff_id', 'pageSize', 'pageNum']);
        Validation::validate($params, MaterialSettingService::$validate_inventory_group_list);
        $result = MaterialSettingService::getInstance()->inventoryGroupList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产盘点组 - 新增
     * @Permission(action='material.setting.inventory_group.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77777
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function inventoryGroupAddAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_inventory_group_add);
        $result = MaterialSettingService::getInstance()->inventoryGroupAdd($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产盘点组 - 编辑
     * @Permission(action='material.setting.inventory_group.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77787
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function inventoryGroupEditAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(MaterialSettingService::$validate_inventory_group_id, MaterialSettingService::$validate_inventory_group_add));
        $result = MaterialSettingService::getInstance()->inventoryGroupEdit($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产盘点组 - 删除
     * @Permission(action='material.setting.inventory_group.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77792
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function inventoryGroupDelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_inventory_group_id);
        $result = MaterialSettingService::getInstance()->inventoryGroupDel($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产盘点组 - 查看
     * @Permission(action='material.setting.inventory_group.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77807
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function inventoryGroupViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_inventory_group_id);
        $result = MaterialSettingService::getInstance()->inventoryGroupView($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组 - 列表
     * @Permission(action='material.setting.return_repair_group.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85622
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairGroupListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ['name', 'staff_id', 'pageSize', 'pageNum']);
        Validation::validate($params, MaterialSettingService::$validate_return_repair_group_list);
        $result = MaterialSettingService::getInstance()->returnRepairGroupList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组 - 新增
     * @Permission(action='material.setting.return_repair_group.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85610
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairGroupAddAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_group_add);
        $result = MaterialSettingService::getInstance()->returnRepairGroupAdd($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组 - 编辑
     * @Permission(action='material.setting.return_repair_group.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85613
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairGroupEditAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(MaterialSettingService::$validate_return_repair_id, MaterialSettingService::$validate_return_repair_group_add));
        $result = MaterialSettingService::getInstance()->returnRepairGroupEdit($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组 - 删除
     * @Permission(action='material.setting.return_repair_group.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85616
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairGroupDelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairGroupDel($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组 - 查看
     * @Permission(action='material.setting.return_repair_group.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85619
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairGroupViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairGroupView($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回类型 - 列表
     * @Permission(action='material.setting.return_repair_type.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85628
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairTypeListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ['name', 'status', 'pageSize', 'pageNum']);
        Validation::validate($params, MaterialSettingService::$validate_return_repair_group_list);
        $result = MaterialSettingService::getInstance()->returnRepairTypeList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回类型 - 新增
     * @Permission(action='material.setting.return_repair_type.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85631
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairTypeAddAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_type_add);
        $result = MaterialSettingService::getInstance()->returnRepairTypeAdd($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回类型 - 编辑
     * @Permission(action='material.setting.return_repair_type.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85634
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairTypeEditAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(MaterialSettingService::$validate_return_repair_id, MaterialSettingService::$validate_return_repair_type_add));
        $result = MaterialSettingService::getInstance()->returnRepairTypeEdit($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回类型 - 删除
     * @Permission(action='material.setting.return_repair_type.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85637
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairTypeDelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairTypeDel($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回类型 - 查看
     * @Permission(action='material.setting.return_repair_type.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85640
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairTypeViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairTypeView($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 通用设置-搜索barcode
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85655
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchBarcodeAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['name' => 'StrLenGeLe:0,100']);
        $result = MaterialSettingService::getInstance()->searchBarcode($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回组/类型 - 搜索
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85658
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairSetSearchAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params,  MaterialSettingService::$validate_return_repair_set_search);
        $result = MaterialSettingService::getInstance()->returnRepairSetSearch($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回映射 - 列表
     * @Permission(action='material.setting.return_repair_map.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85676
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairMapListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ['category_id', 'pageSize', 'pageNum']);
        Validation::validate($params, MaterialSettingService::$validate_return_repair_map_list);
        $result = MaterialSettingService::getInstance()->returnRepairMapList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回映射 - 新增
     * @Permission(action='material.setting.return_repair_map.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85664
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairMapAddAction()
    {
        $params = trim_array($this->request->get());
        $validate_param = MaterialSettingService::getInstance()->getExtendMapValidation($params);
        Validation::validate($params, $validate_param);
        $result = MaterialSettingService::getInstance()->returnRepairMapAdd($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回映射 - 编辑
     * @Permission(action='material.setting.return_repair_map.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85667
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairMapEditAction()
    {
        $params = trim_array($this->request->get());
        $validate_param = MaterialSettingService::getInstance()->getExtendMapValidation($params);
        Validation::validate($params, array_merge(MaterialSettingService::$validate_return_repair_id, $validate_param));
        $result = MaterialSettingService::getInstance()->returnRepairMapEdit($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回映射 - 删除
     * @Permission(action='material.setting.return_repair_map.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85670
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairMapDelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairMapDel($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产退回映射 - 查看
     * @Permission(action='material.setting.return_repair_map.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85673
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function returnRepairMapViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_return_repair_id);
        $result = MaterialSettingService::getInstance()->returnRepairMap($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产数据管控 - 列表
     * @Permission(action='material.setting.data_permission.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87839
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function dataPermissionListAction()
    {
        $result = MaterialSettingService::getInstance()->dataPermissionList();
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产数据管控 - 组 - 列表
     * @Permission(action='material.setting.data_permission.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87845
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function dataPermissionGroupListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, ['pageSize', 'pageNum']);
        Validation::validate($params, MaterialSettingService::$validate_data_permission_group_list);
        $result = MaterialSettingService::getInstance()->dataPermissionGroupList($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产数据管控 - 组 - 新增
     * @Permission(action='material.setting.data_permission.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87848
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function dataPermissionGroupAddAction()
    {
        $params = trim_array($this->request->get());
        $validate_param = MaterialSettingService::getInstance()->getExtendDataPermissionGroupValidation($params);
        Validation::validate($params, $validate_param);
        $result = MaterialSettingService::getInstance()->dataPermissionGroupAdd($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产数据管控 - 组 - 编辑
     * @Permission(action='material.setting.data_permission.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87851
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function dataPermissionGroupEditAction()
    {
        $params = trim_array($this->request->get());
        $validate_param = MaterialSettingService::getInstance()->getExtendDataPermissionGroupValidation($params);
        Validation::validate($params, array_merge(MaterialSettingService::$validate_data_permission_id, $validate_param));
        $result = MaterialSettingService::getInstance()->dataPermissionGroupEdit($params, $this->user);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

    /**
     * 资产数据管控 - 组 - 查看
     * @Permission(action='material.setting.data_permission.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87854
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function dataPermissionGroupViewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, MaterialSettingService::$validate_data_permission_id);
        $result = MaterialSettingService::getInstance()->dataPermissionGroup($params);
        return $this->returnJson($result['code'], $result['message'], $result['data']);
    }

}
