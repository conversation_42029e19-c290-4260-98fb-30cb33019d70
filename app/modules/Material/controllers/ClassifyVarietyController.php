<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Services\ClassifyService;

class ClassifyVarietyController extends BaseController
{
    /**
     * 分类管理 标准型号库 枚举
     * @Token
     * @Date: 2021-10-13 21:11
     * @api https://yapi.flashexpress.pub/project/133/interface/api/34033
     * @return:
     **@author: peak pan
     */
    public function getOptionsDefaultAction()
    {
        $res = ClassifyService::getInstance()->getOptionsDefault();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 物料分类-列表
     * @Permission(action='material.classify_variety.list')
     * @Date: 2021-10-13 21:11
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33550
     * @return:
     *
     **@author: peak pan
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$list_search);
            $params['type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->getClassifyArr($params);
            $data = [
                'list' => $list['data'] ?? []
            ];
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $data);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 物料分类-添加
     * @Permission(action='material.classify_variety.add')
     * @Date: 2021-10-13 21:12
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33557
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function addAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_material_category);
            $list = ClassifyService::getInstance()->categoryAdd($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 物料分类-编辑
     * @Permission(action='material.classify_variety.save')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33578
     * @Date: 2021-10-13 21:12
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     */
    public function saveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, array_merge(ClassifyService::$validate_save_material_category, ClassifyService::$validate_material_category));
            $list = ClassifyService::getInstance()->categorySave($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 物料分类-删除
     * @Permission(action='material.classify_variety.del')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33585
     * @Date: 2021-10-13 21:13
     * @return:
     **@author: peak pan
     */
    public function delAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $params['classify'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->materialDel($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }

    /**
     * 物料分类-上移下移
     * @Permission(action='material.classify_variety.move')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/33606
     * @Date: 2021-10-13 21:13
     * @return:
     **@author: peak pan
     */
    public function moveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $params['classify'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $list = ClassifyService::getInstance()->materialMove($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }

    /**
     * 物料分类详情
     * @Permission(action='material.classify_variety.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/35804
     * @Date: 2021-11-02 16:38
     * @author: peak pan
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ClassifyService::$validate_del_material_category);
            $list = ClassifyService::getInstance()->materialDetail($params);
            return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

    }
}
