<?php
namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\AssetApplyService;
use App\Modules\Material\Services\BaseService;
use App\Util\RedisKey;

/**
 * 资产领用申请-审批
 * Class AssetApplyAuditController
 * @package App\Modules\Material\Controllers
 */
class AssetApplyAuditController extends BaseController
{
    /**
     * 资产领用申请审核-查询
     * @Permission(action='material.asset_apply_audit.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62715
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $res = AssetApplyService::getInstance()->getAuditList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请审核-查看
     * @Permission(action='material.asset_apply_audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62716
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_update);
        $list = AssetApplyService::getInstance()->detail($params, MaterialAssetApplyEnums::LIST_TYPE_APPLY_AUDIT, $this->user);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产领用申请审核-查看某出库单-出库路由
     * @Permission(action='material.asset_apply_audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87422
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getOutboundTrackingInfoAction()
    {
        $params = trim_array($this->request->get());
        $res = AssetApplyService::getInstance()->getOutboundTrackingInfo($this->locale, $params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 资产领用申请审核-驳回
     * @Permission(action='material.asset_apply_audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62712
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_reject, AssetApplyService::$validate_products));
        $list = AssetApplyService::getInstance()->reject($params, $this->user);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产领用申请审核-同意
     * @Permission(action='material.asset_apply_audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/62713
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function passAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(AssetApplyService::$validate_update, AssetApplyService::$validate_products));
        $list = AssetApplyService::getInstance()->pass($params, $this->user);
        return $this->returnJson($list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
    }

    /**
     * 资产申请审核-待处理-导出
     * @Permission(action='material.asset_apply_audit.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77162
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AssetApplyService::$not_must_params);
        Validation::validate($params, AssetApplyService::$validate_list_search);
        $lock_key = md5(RedisKey::MATERIAL_ASSET_APPLY_AUDIT_EXPORT . '_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_ASSET_APPLY_AUDIT, $params);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 资产领用审核-批量审核-上传保存
     * @Permission(action='material.asset_apply_audit.batch_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77242
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \Exception
     */
    public function batchAuditAction()
    {
        $excel_file = $this->request->getUploadedFiles();
        $lock_key = md5(RedisKey::MATERIAL_ASSET_APPLY_BATCH_AUDIT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return AssetApplyService::getInstance()->importAddTask($excel_file, $this->user);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 批量审核-查询最后一次导入成功的结果
     * @Permission(action='material.asset_apply_audit.batch_audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/77252
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAuditResultAction()
    {
        $result = AssetApplyService::getInstance()->getImportAuditResult($this->user['id']);
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 资产领用申请审核-查看-网点资产数据
     * @Permission(action='material.asset_apply_audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80456
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function auditAssetListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AssetApplyService::$validate_audit_asset_list);
        $list = AssetApplyService::getInstance()->auditAssetList($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }
}
