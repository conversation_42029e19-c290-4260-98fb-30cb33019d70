<?php

namespace App\Modules\Material\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Material\Services\WmsApplyService;
use App\Modules\Material\Services\BaseService;

/**
 * 耗材申请-数据查询
 * Class WmsApplyDataController
 * @package App\Modules\Material\Controllers
 */
class WmsApplyDataController extends BaseController
{
    /**
     * 耗材申请-数据查询-查询
     * @Permission(action='material.wms_apply_data.apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66592
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        Validation::validate($params, array_merge(WmsApplyService::$validate_list_search, WmsApplyService::$validate_approve_at));
        $res = WmsApplyService::getInstance()->getList($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-数据查询-查看
     * @Permission(action='material.wms_apply_data.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66597
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WmsApplyService::$validate_share);
        $res = WmsApplyService::getInstance()->detail($params, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 耗材申请-数据查询-导出
     * @Permission(action='material.wms_apply_data.apply.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/66602
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, WmsApplyService::$not_must_params);
        Validation::validate($params, array_merge(WmsApplyService::$validate_list_search, WmsApplyService::$validate_approve_at));
        $lock_key = md5(MaterialWmsEnums::MATERIAL_WMS_APPLY_DATA_EXPORT_LOCK . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::MATERIAL_WMS_APPLY_DATA, $params);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }



}
