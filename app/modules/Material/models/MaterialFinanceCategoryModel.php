<?php

namespace App\Modules\Material\Models;

use App\Library\Enums\MaterialClassifyEnums;
use App\Models\Base;
use App\Library\ErrCode;


class MaterialFinanceCategoryModel extends Base
{

    public $id;

    public $name;

    public $code;

    public $parent_id;

    public $level;

    public $use_limit;

    public $status;

    public $update_to_scm;

    public $sort;

    public $is_deleted;

    public $created_at;

    public $updated_at;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_finance_category');
    }

    /**
     * 分页获取数据
     * @Token
     * @Date: 2021-10-19 17:33
     * @return:
     **@author: peak pan
     */
    public function getFinanceName($data)
    {
        //分页获取所有分类
        $ids_ = format_array(array_filter(explode(',', implode(',', array_column($data, 'fids')))));
        $category_ids = format_array(array_filter(explode(',', implode(',', array_column($data, 'finance_category_id')))));
        $ids = array_merge($ids_, $category_ids);
        $pic_arr = self::find([
            "conditions" => '  is_deleted =0 and id in ({ids:array}) ',
            "order" => 'level ASC',
            "bind" => [
                'ids' => $ids
            ],
            'columns' => 'id,name,code'
        ])->toArray();
        return $pic_arr ? array_column($pic_arr, null, 'id') : [];
    }

    /**
     * 获取冒个父类下的所有子类
     * @Token
     * @Date: 2021-10-20 21:03
     * @return:
     **@author: peak pan
     */
    public function getSonList($id,$groupByName='level')
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $data = $this->modelsManager->createBuilder()
                ->from(MaterialFinanceCategoryModel::class)
                ->andWhere(" find_in_set(:ids:,ids)", ['ids' =>$id])
                ->andWhere("is_deleted = :is_deleted:", ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO])
                ->groupBy($groupByName)
                ->getQuery()->execute()->toArray();
            if (!empty($data)) {
                $data = array_column($data,'id');
            } else {
                $data = [$id];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
        }

        if (count($data) > 1000) {
            $this->getDI()->get('logger')->info('检索条件范围太大，请减小范围查询: ' . $id);
            $message = static::$t->_('material_category_select_max_count');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('检索异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 批量修改层级
     * @Token
     * @Date: 2021-10-19 17:12
     * @return:
     **@author: peak pan
     */
    public function getAllSave($ids, $level, $last_ids)
    {
        $newtime = date('Y-m-d H:i:s', time());
        $sql = "update material_finance_category set level=" . $level . ", ids='" . $last_ids . "',updated_at='" . $newtime . "' where id in (" . implode(',', $ids) . ")";
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('分类批量修改失败==' . $sql, MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        return $bool;
    }

    /**
     * 指定分类下是否有启用的子类
     * @param integer $id 分类ID
     * @return bool
     */
    public function isHasChildrenUsing($id)
    {
        $count = MaterialFinanceCategoryModel::count([
            'conditions' => 'parent_id =:parent_id: and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id',
            'bind' => [
                'parent_id' =>$id,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ]);
        return $count > 0;
    }

    /**
     * 指定分类下是否有启用的子类
     * @param array $codes  分类编码集合
     * @return array 返回 id=>数据 的关联关系数组 和 所有存在子类的code
     */
    public function getHasChildrenData($codes)
    {
        if (empty($codes)){
            return [0=>[],1=>[]];
        }
        //查询code对应的id
        $data = MaterialFinanceCategoryModel::find([
            'conditions' => 'code in ({codes:array}) and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id,parent_id,code,purchase_type',
            'bind' => [
                'codes' =>$codes,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ])->toArray();
        if (empty($data)){
            return [0=>[],1=>[]];
        }
        $id_data_kv = array_column($data,null,'id');
        $id_code_kv = array_column($data,'code','id');
        $ids = array_column($data,'id');
        //查询存在子类的id
        $data = MaterialFinanceCategoryModel::find([
            'conditions' => 'parent_id in ({parent_id:array}) and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id,parent_id',
            'bind' => [
                'parent_id' =>$ids,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ])->toArray();

        //$data[]['parent_id'] = 存在子类的id, 通过此id查找对应的codes, 获取存在子类的codes
        $has_children_codes = [];
        foreach ($data as $k=>$v){
            if (key_exists($v['parent_id'],$id_code_kv)){
                $has_children_codes[] = $id_code_kv[$v['parent_id']];
            }
        }
        return [0=>$id_data_kv,1=>$has_children_codes];
    }

    /**
     * 所选择的财务分类编码是否是最末级
     * @param array $wrs_code code组
     * @return bool
     */
    public function isHasSon($wrs_code)
    {
        if (empty($wrs_code)) {
            return true;
        }
        $list = MaterialFinanceCategoryModel::find([
            'conditions' => 'code in ({codes:array}) and is_deleted = :is_deleted:',
            'columns' => 'id',
            'bind' => [
                'codes' =>$wrs_code,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
            ]
        ])->toArray();
        $category_ids = array_column($list,"id");
        if (empty($category_ids)) {
            return false;
        }
        $count = MaterialFinanceCategoryModel::count([
            'conditions' => 'parent_id in ({ids:array}) and is_deleted = :is_deleted:',
            'columns' => 'id',
            'bind' => ['ids' => $category_ids, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        return $count>0;
    }

    /**
     * 根据最末级财务分类返回这些末级财务分类的父类编码集合包括本身编码
     * @param array $wrs_code code组
     * @return array
     */
    public function getParentsCodes($wrs_code)
    {
        $last_code = [];
        if (empty($wrs_code)) {
            return $last_code;
        }
        $list = MaterialFinanceCategoryModel::find([
            'conditions' => 'code in ({codes:array}) and is_deleted = :is_deleted:',
            'columns' => 'ids as fids,code,id as finance_category_id',
            'bind' => [
                'codes' =>$wrs_code,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
            ]
        ])->toArray();
        if ($list) {
            $category_parent_ids = array_column($list,null,'finance_category_id');
            //获取所有满足条件的分类ID列表
            $all_list = $this->getFinanceName($list);
            foreach ($category_parent_ids as $item) {
                $parent_ids = explode(",", $item['fids']);
                foreach ($all_list as $key=>$value) {
                    if (in_array($key, $parent_ids) || $key == $item['finance_category_id']) {
                        $last_code[$item['code']][] = $all_list[$key]['code'];
                    }
                }
            }
        }
        return $last_code;
    }

    /**
     * 根据条件获取特定的财务分类列表
     * @param array $params['code'=>'分类编码']
     * @return mixed
     */
    public function getList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name,code,use_limit,update_to_acceptance');
        $builder->from([MaterialFinanceCategoryModel::class]);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        if (isset($params['code']) && !empty($params['code'])) {
            if (is_array($params['code'])) {
                $builder->andWhere('code in ({code:array})', ['code' => $params['code']]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取某个分类下的所有子类, 含父类自身
     *
     * @param string $code 分类编码
     * @return mixed
     */
    public function getAllSonListByCode(string $code = '')
    {
        if (empty($code)) {
            return [];
        }

        // 找分类编码的id
        $cate_model = parent::findFirst([
            'conditions' => 'code = :code:',
            'bind' => ['code' => $code],
            'columns' => ['id', 'code', 'name']
        ]);

        if (empty($cate_model)) {
            return [];
        }

        $son_list = parent::find([
            'conditions' => 'FIND_IN_SET(:parent_id:, ids)',
            'bind' => ['parent_id' => $cate_model->id],
            'columns' => ['id', 'code', 'name']
        ])->toArray();

        return array_merge([$cate_model->toArray()], $son_list);
    }
}
