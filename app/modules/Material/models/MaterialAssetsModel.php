<?php

namespace App\Modules\Material\Models;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;

/**
 * 资产清单表
 * @package App\Modules\Material\Models
 * @date 2022/2/23
 */
class MaterialAssetsModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_assets');

        //获取资产台账-附件信息
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".MaterialEnums::OSS_MATERIAL_TYPE_ASSET." and deleted=".MaterialClassifyEnums::IS_DELETED_NO
                ],
                "alias" => "PicAttachment",
            ]
        );
    }

    /**
     * 获取属性
     * @return mixed
     */
    public function getAttributesArr()
    {
        return $this->getModelsMetaData()->getAttributes($this);
    }
}
