<?php
namespace App\Modules\Material\Models;

use App\Library\BaseService;
use App\Library\Enums\MaterialClassifyEnums;
use App\Models\Base;

/**
 * 物料数据操作记录
 * @package App\Modules\Material\Models
 */
class MaterialUpdateLogModel extends Base
{
    //需要记录操作日志的字段
    const EDIT_FILED = [
        'category_id',//物料分类表ID
        'finance_category_id',//财务分类表ID
        'barcode',//标准型号的唯一标识
        'name_zh',//物料名称-中文
        'name_en',//物料名称-英文
        'name_local',//物料名称-当地语言
        'model',//规格型号
        'unit_zh',//基本单位-中文
        'unit_en',//基本单位-英文
        'remark',//备注
        'category_type',//物料类型，1资产，2耗材
        'purchase_type',//采购类型
        'price',//参考单价
        'currency',//币种(currency表ID)
        'sap_unit',//SAP单位
        'update_to_scm',//是否更新至SCM，1否，2是
        'enable_asset_code',//是否启用资产码，1否，2是
        'enable_sn',//是否启用SN，1否，2是
        'update_to_sap',//是否更新至SAP，1否，2是
        'update_to_acceptance',//是否验收，1否，2是
        'status',//启用状态：1启用，2禁用
        'transfer_forbid',//是否限制员工互转, 1是，2否
        'is_send_sap',//发送至SAP结果：0默认值，1待发送，2已发送，3发送失败
        'purchase_unit_zh',//采购管理单位-中文
        'purchase_unit_en',//采购管理单位-英文
        'purchase_val',//采购管理单位=()基本单位
        'use_unit_zh',//领用单位-中文
        'use_unit_en',//领用单位-中文
        'use_val',//领用单位=()基本单位
        'small_bag_unit_zh',//小包装单位-中文
        'small_bag_unit_en',//小包装单位-英文
        'small_bag_val',//小包装单位=()基本单位
        'big_bag_unit_zh',//大包装单位-中文
        'big_bag_unit_en',//大包装单位-英文
        'big_bag_val',//大包装单位=()基本单位

        'is_deleted',//删除
        'updated_at',//操作时间

        'job',//职位
        'purchase_staff',//采购员
    ];
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_update_log');
    }

    /**
     * 处理更新数据
     * @param integer $type 操作类型，1新增，2更新，3删除
     * @param object $item 要更新可编辑数据对象信息
     * @param array $update_data 更新的操作数据组
     * @param array $user 当前登陆操作人信息
     * @param array $edit_field 编辑的数据参数key组
     * @return bool
     */
    public function dealEditField($type, $item, $update_data, $user, $edit_field = self::EDIT_FILED)
    {
        $logData = [];//日志组
        $update_flag = false;
        if (!empty($edit_field)) {
            if ($type == MaterialClassifyEnums::OPERATE_TYPE_ADD) {
                //新增操作
                foreach ($edit_field as $key) {
                    if (!isset($update_data[$key])) {
                        continue;
                    }
                    $tmp = $update_data[$key];
                    $log['before'] = "";
                    if (in_array($key, ['job'])) {
                        $log['after'] = implode(',', $tmp);
                    } else {
                        $log['after'] = $tmp;
                    }
                    $log['field_name'] = $key;
                    $logData[] = $log;
                    $update_flag = true;
                }
            } else {
                //其他操作
                foreach ($edit_field as $key) {
                    if (!isset($update_data[$key])) {
                        continue;
                    }
                    $tmp = $update_data[$key];
                    if ($key == 'job') {
                        $log['before'] = $update_data['old_job_ids'] ?? '';
                        $log['after'] = implode(',', $tmp ?? []);
                        $log['field_name'] = $key;
                        $logData[] = $log;
                        $update_flag = true;
                    } else if ($tmp != $item->$key) {
                        //不相等，才记录
                        $log['before'] = $item->$key;
                        $log['after'] = $tmp;
                        $log['field_name'] = $key;
                        $logData[] = $log;
                        $update_flag = true;
                    }
                }
            }
        }
        //记录审批操作过程中更新日志
        if ($update_flag) {
            $log = new MaterialUpdateLogModel();
            return $log->save([
                'operate_id' => $item->id,
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }
        return true;
    }

    /**
     * 添加修改记录 add操作批量处理
     * @param array $before_data 修改前的数据,添加操作传空
     * @param array $after_data  修改后的数据
     * @param array $user 当前登陆操作人信息
     * @param string $datetime 添加数据的insert时间
     * @param array $edit_field 编辑的数据参数key组
     * @return bool
     */
    public function dealEditFieldBatch($before_data, $after_data, $user, $datetime = '', $edit_field = self::EDIT_FILED)
    {
        $time = $datetime != ''?$datetime:date('Y-m-d H:i:s');
        $base_model = new BaseService();
        if (empty($edit_field)) {
            return true;
        }
        $insert_log_data = [];
        //新增操作
        if (empty($before_data)) {
            foreach ($after_data as $add_v) {
                $log_data = [];//日志组
                foreach ($edit_field as $key) {
                    if (!isset($add_v[$key])) {
                        continue;
                    }
                    $tmp = $add_v[$key];
                    $log = [];
                    $log['before'] = "";
                    if (in_array($key, ['job'])) {
                        $log['after'] = implode(',', $tmp);
                    } else {
                        $log['after'] = $tmp;
                    }
                    $log['field_name'] = $key;
                    $log_data[] = $log;
                }
                if (!empty($log_data)) {
                    $insert_log_data[] = [
                        'operate_id' => $add_v['id'],
                        'staff_id' => $user['id'],
                        'staff_name' => $base_model->getNameAndNickName($user['name'], $user['nick_name']),
                        'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                        'type' => MaterialClassifyEnums::OPERATE_TYPE_ADD,
                        'created_at' => $time,
                        'updated_at' => $time,
                    ];
                }
            }
        } else {
            //修改操作
            $before_data = array_column($before_data, null, 'id');
            $after_data = array_column($after_data, null, 'id');
            foreach ($after_data as $update_v) {
                $log_data = [];//日志组
                foreach ($edit_field as $key) {
                    if (!isset($update_v[$key])) {
                        continue;
                    }
                    $tmp = $update_v[$key];
                    $tmp_before = $before_data[$update_v['id']][$key] ?? '';
                    if ($key == 'job') {
                        $log['before'] = $tmp_before ?? '';
                        $log['after'] = implode(',', $tmp ?? []);
                        $log['field_name'] = $key;
                        $log_data[] = $log;
                    } else if ($tmp != $tmp_before) {
                        $log = [];
                        $log['before'] = $tmp_before;
                        $log['after'] = $tmp;
                        $log['field_name'] = $key;
                        $log_data[] = $log;
                    }

                }
                if (!empty($log_data)) {
                    $insert_log_data[] = [
                        'operate_id' => $update_v['id'],
                        'staff_id' => $user['id'],
                        'staff_name' => $base_model->getNameAndNickName($user['name'], $user['nick_name']),
                        'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                        'type' => MaterialClassifyEnums::OPERATE_TYPE_UPDATE,
                        'created_at' => $time,
                        'updated_at' => $time,
                    ];
                }
            }
        }
        if (!empty($insert_log_data)) {
            $log_model = new MaterialUpdateLogModel();
            $insert_result = $log_model->batch_insert($insert_log_data);
            if (!$insert_result) {
                return false;
            }
        }
        return true;
    }
}
