<?php
namespace App\Modules\Material\Models;

use App\Library\BaseService;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;

/**
 * 资产台账数据操作记录
 * Class MaterialAssetUpdateLogModel
 * @package App\Modules\Material\Models
 */
class MaterialAssetUpdateLogModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_asset_update_log');
    }

    /**
     * 处理更新数据
     * @param integer $type 操作类型，1新增，2修改，3批量导入新增，4批量导入修改，5转移，6批量转移
     * @param object $item 要更新可审批编辑数据对象信息
     * @param array $update_data 更新的操作数据组
     * @param array $user 当前登陆操作人信息
     * @return bool
     */
    public function dealEditField($type, $item, $update_data, $user)
    {
        $edit_field = (new MaterialAssetsModel())->getAttributesArr();
        $logData = [];//日志组
        $update_flag = false;
        if (!empty($edit_field)) {
            if ($type == MaterialEnums::OPERATE_TYPE_ADD || $type == MaterialEnums::OPERATE_TYPE_BATCH_ADD) {
                //新增操作或批量导入新增操作
                $logData = $update_data;
                $update_flag = true;
            } else {
                //其他操作
                foreach ($edit_field as $key) {
                    if (!isset($update_data[$key])) {
                        continue;
                    }
                    $tmp = $update_data[$key];
                    //不相等，才记录
                    if ($tmp != $item->$key) {
                        $log['before'] = $item->$key;
                        $log['after'] = $tmp;
                        $log['field_name'] = $key;
                        $logData[] = $log;
                        $update_flag = true;
                    }
                }
            }
        }
        //记录审批操作过程中更新日志
        if ($update_flag) {
            $log = new MaterialAssetUpdateLogModel();
            return $log->save([
                'asset_code' => $item->asset_code,
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'content' => json_encode($logData, JSON_UNESCAPED_UNICODE),
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }
        return true;
    }

    /**
     * 批量记录更新日志-不同的资产修改的字段相同
     * @param integer $type 7.员工互转
     * @param array $asset_code_array 所有资产编码
     * @param array $before_data 修改前的字段值
     * @param array $after_data 修改后的字段值
     * @param array $user 登录用户信息
     * @date 2022/11/14
     * @return bool
     */
    public function dealEditFieldBatch($asset_code_array, $before_data, $after_data, $user, $type = MaterialEnums::OPERATE_TYPE_USER_TRANSFER)
    {
        $before_keys = array_keys($before_data);
        $after_keys = array_keys($after_data);
        //前后key不一致
        if (array_diff($before_keys, $after_keys) || array_diff($after_keys, $before_keys)) {
            return false;
        }
        //资产编码为空
        if (empty($asset_code_array)) {
            return false;
        }
        //log内容
        $log = $log_arr = [];
        //修改的会有多个字段
        foreach ($before_data as $before_k => $before_v) {
            if ($before_v != $after_data[$before_k]) {
                $log['before'] = $before_v;
                $log['after'] = $after_data[$before_k];
                $log['field_name'] = $before_k;
                $log_arr[] = $log;
            }
        }
        //内容为空,没有需要记录的
        if (empty($log_arr)) {
            return false;
        }
        //所有资产编码的log
        $all_log_data = [];
        foreach ($asset_code_array as $asset_code) {
            $all_log_data[] = [
                'asset_code' => $asset_code,
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'content' => json_encode($log_arr, JSON_UNESCAPED_UNICODE),
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        //批量写入
        $create_result = $this->batch_insert($all_log_data);
        if (!$create_result) {
            return false;
        }
        return true;
    }

    /**
     * 批量记录更新日志-不用的资产数据改成同样的值
     * @param $all_before_data
     * @param $after_data
     * @param $user
     * @param int $type
     * @return bool
     * @date 2023/4/24
     */
    public function dealEditDataBatch($all_before_data, $after_data, $user, $type = MaterialEnums::OPERATE_TYPE_LEAVE_ASSET)
    {
        //所有资产编码的log
        $all_log_data = [];
        foreach ($all_before_data as $before_data) {
            //1.提取出这条数据的asset_code
            if (empty($before_data['asset_code'])) {
                return false;
            }
            $this_asset_code = $before_data['asset_code'];
            unset($before_data['asset_code']);
            //2.记录变更的字段
            $before_keys = array_keys($before_data);
            $after_keys = array_keys($after_data);
            //前后key不一致
            if (array_diff($before_keys, $after_keys) || array_diff($after_keys, $before_keys)) {
                return false;
            }
            //log内容
            $log = [];
            foreach ($before_data as $before_k => $before_v) {
                if ($before_v != $after_data[$before_k]) {
                    $tmp = [];
                    $tmp['before'] = $before_v;
                    $tmp['after'] = $after_data[$before_k];
                    $tmp['field_name'] = $before_k;
                    $log[] = $tmp;
                }
            }
            //内容为空,没有需要记录的
            if (empty($log)) {
                return false;
            }
            //3.最终入库的字段
            $all_log_data[] = [
                'asset_code' => $this_asset_code,
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'content' => json_encode($log, JSON_UNESCAPED_UNICODE),
                'type' => $type,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        if (empty($all_log_data)) {
            return false;
        }
        //批量写入
        $create_result = $this->batch_insert($all_log_data);
        if (!$create_result) {
            return false;
        }
        return true;
    }


    /**
     * 批量记录更新日志-记录资产有变化的日志
     * @param array $asset_code_array 所有需要修改的资产编码
     * @param array $before_data 修改前的所有字段值集合
     * @param array $after_data 修改后的所有字段值集合
     * @param array $user 登录用户信息
     * @param integer $type 日志类型
     * @return bool
     */
    public function batchInsertEditLog($asset_code_array, $before_data, $after_data, $user, $type = MaterialEnums::OPERATE_TYPE_BATCH_CANCEL)
    {
        $time = date('Y-m-d H:i:s');
        //资产编码为空
        if (empty($asset_code_array)) {
            return false;
        }
        //log内容
        $log = $log_arr = [];
        //修改的会有多个字段
        foreach ($before_data as $before_k => $before_v) {
            foreach ($before_v as $before_key => $before_value) {
                if ($before_value != $after_data[$before_k][$before_key]) {
                    $log['before']                      = $before_value;
                    $log['after']                       = $after_data[$before_k][$before_key];
                    $log['field_name']                  = $before_key;
                    $log_arr[$before_v['asset_code']][] = $log;
                }
            }
        }
        //内容为空,没有需要记录的
        if (empty($log_arr)) {
            return true;
        }
        //所有资产编码的log
        $all_log_data = [];
        foreach ($asset_code_array as $asset_code) {
            if (empty($log_arr[$asset_code])) {
                continue;
            }
            $all_log_data[] = [
                'asset_code' => $asset_code,
                'staff_id'   => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'content'    => json_encode($log_arr[$asset_code], JSON_UNESCAPED_UNICODE),
                'type'       => $type,
                'created_at' => $time,
                'updated_at' => $time,
            ];
        }

        $child_log_data = array_chunk($all_log_data, 3000);
        //分组插入资产台账数据
        foreach ($child_log_data as $key => $log_data) {
            $bool = $this->batch_insert($log_data);
            if ($bool === false) {
                return false;
            }
        }
        return true;
    }

}
