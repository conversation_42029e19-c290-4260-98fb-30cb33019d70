<?php
namespace App\Modules\Material\Models;
use App\Library\Enums\GlobalEnums;
use App\Models\Base;

/**
 * 资产盘点单信息表
 * Class MaterialInventoryCheckModel
 * @package App\Modules\Material\Models
 */
class MaterialInventoryCheckModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_inventory_check');

        //盘点单盘点范围关系表
        $this->hasMany(
            'id',
            MaterialInventoryCheckRelModel::class,
            'inventory_check_id', [
                'params' => [
                    'conditions' => 'is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'CheckRel',
            ]
        );

        //员工盘点任务表
        $this->hasMany(
            'id',
            MaterialInventoryCheckStaffModel::class,
            'inventory_check_id', [
                'params' => [
                    'conditions' => 'is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Staffs',
            ]
        );
    }
}
