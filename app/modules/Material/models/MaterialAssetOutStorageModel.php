<?php
namespace App\Modules\Material\Models;
use App\Library\Enums\MaterialClassifyEnums;
use App\Models\Base;

/**
 * 资产领用出库单表
 * Class MaterialAssetOutStorageModel
 * @package App\Modules\Material\Models
 */
class MaterialAssetOutStorageModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_asset_out_storage');
        $this->hasMany(
            'id',
            MaterialAssetOutStorageProductModel::class,
            'aor_id', [
                'params' => [
                    'conditions' => "is_deleted=".MaterialClassifyEnums::IS_DELETED_NO
                ],
                "alias" => "Products",
            ]
        );
    }

}
