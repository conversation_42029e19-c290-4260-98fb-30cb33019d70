<?php

namespace App\Modules\Material\Models;

use App\Models\Base;
use App\Models\oa\MaterialSauPermissionModel;
use App\Models\oa\MaterialSauPurchaseStaffsModel;
use App\Models\oa\MaterialSauStorageModel;
use App\Library\Enums\GlobalEnums;

class MaterialSauModel extends Base
{

    public $id;

    public $category_id;

    public $finance_category_id;

    public $barcode;

    public $name;

    public $model;

    public $unit_id;

    public $sap_purchase_code;

    public $remark;

    public $asset_value_init;

    public $asset_value;

    public $currency;

    public $update_to_scm;

    public $asset_label;

    public $enable_sn;

    public $status;

    public $is_deleted;

    public $created_at;

    public $updated_at;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_sau');

        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = 1 and deleted=0"
                ],
                "alias" => "MaterialSauAttachments",
            ]
        );
        $this->hasOne(
            'id',
            MaterialSauSkuModel::class,
            'sau_id', [
                'params' => [
                    'conditions' => "is_deleted =0"
                ],
                "alias" => "MaterialSauSku",
            ]
        );

        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type =1 and deleted=0"
                ],
                "alias" => "PicAttachment",
            ]
        );

        $this->hasOne(
            'finance_category_id',
            MaterialFinanceCategoryModel::class,
            'id', [
                'params' => [
                    'conditions' => "is_deleted =0"
                ],
                "alias" => "MaterialFinanceCategory",
            ]
        );

        $this->hasOne(
            'category_id',
            MaterialCategoryModel::class,
            'id', [
                'params' => [
                    'conditions' => "is_deleted =0"
                ],
                "alias" => "MaterialCategory",
            ]
        );

        $this->hasMany(
            'id',
            MaterialSauStorageModel::class,
            'sau_id', [
                'params' => [
                    'conditions' => 'is_deleted=' . GlobalEnums::IS_NO_DELETED
                ],
                "alias" => "MaterialSauStorage",
            ]
        );

        $this->hasMany(
            'id',
            MaterialSauPermissionModel::class,
            'sau_id', [
                'params' => [],
                "alias" => "MaterialSauPermission",
            ]
        );

        $this->hasMany(
            'id',
            MaterialSauPurchaseStaffsModel::class,
            'sau_id', [
                'params' => [],
                'alias' => 'MaterialSauPurchaseStaffs',
            ]
        );
    }

}
