<?php
namespace App\Modules\Material\Models;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;

/**
 * 资产领用申请单表
 * Class MaterialAssetApplyModel
 * @package App\Modules\Material\Models
 */
class MaterialAssetApplyModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_asset_apply');
        $this->hasMany(
            'id',
            MaterialAssetApplyProductModel::class,
            'apply_id', [
                'params' => [
                    'conditions' => "is_deleted=".MaterialClassifyEnums::IS_DELETED_NO
                ],
                "alias" => "Products",
            ]
        );

        //获取资产申请-附件信息
        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".MaterialAssetApplyEnums::OSS_MATERIAL_TYPE_ASSET_APPLY." and deleted=".MaterialClassifyEnums::IS_DELETED_NO
                ],
                "alias" => "PicAttachment",
            ]
        );
    }

}
