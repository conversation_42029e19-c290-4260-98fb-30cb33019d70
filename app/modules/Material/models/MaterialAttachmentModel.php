<?php

namespace App\Modules\Material\Models;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Models\Base;
use App\Modules\Material\Services\StandardService;

/**
 * 资产盘点单附件信息表
 * Class MaterialAttachmentModel
 * @package App\Modules\Material\Models
 */
class MaterialAttachmentModel extends Base
{
    public $id;

    public $oss_bucket_type;

    public $sub_type;

    public $oss_bucket_key;

    public $bucket_name;

    public $object_key;

    public $file_name;

    public $deleted;

    public $created_at;

    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_attachment');
    }

    /**
     * 分页处理图片
     * @Token
     * @Date: 2021-10-19 17:12
     * @param $data
     * @param int $oss_type
     * @return array :
     **@author: peak pan
     */
    public function getColumnArr($data, $oss_type = 1)
    {
        //获取所有图片地址
        $pic_arr = self::find([
            "conditions" => ' oss_bucket_type = :oss_bucket_type: and deleted = :deleted: and oss_bucket_key in ({ids:array}) ',
            "bind" => [
                'oss_bucket_type' => $oss_type,
                'ids' => array_column($data, 'id'),
                'deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => 'id,oss_bucket_key,object_key,bucket_name'
        ])->toArray();
        $items = [];
        if ($pic_arr) {
            foreach ($pic_arr as $key => $item) {
                $items[$item['oss_bucket_key']][] = $item;
                //unset($items[$key]['oss_bucket_key']);
            }
        }
        return $items;
    }

    /**
     * 分页处理图片,比getColumnArr多返回object_url
     * @param $data
     * @param int $oss_type
     * @return array
     * @date 2023/3/19
     */
    public function getColumnArrUrl($data, $oss_type = 1)
    {
        if (empty($data)) {
            return [];
        }
        $ids = array_column($data, 'id');
        $ids = array_values(array_unique($ids));
        //获取所有图片地址
        $pic_arr = self::find([
            "conditions" => ' oss_bucket_type = :oss_bucket_type: and deleted = :deleted: and oss_bucket_key in ({ids:array}) ',
            "bind" => [
                'oss_bucket_type' => $oss_type,
                'ids' => $ids,
                'deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => 'id, oss_bucket_key, object_key, bucket_name, object_url'
        ])->toArray();
        $items = [];
        if ($pic_arr) {
            foreach ($pic_arr as $key => $item) {
                $items[$item['oss_bucket_key']][] = $item;
                //unset($items[$key]['oss_bucket_key']);
            }
        }
        return $items;
    }

    /**
     * 获取一条记录
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }


}
