<?php

namespace App\Modules\Material\Models;

use App\Library\Enums\MaterialClassifyEnums;
use App\Models\Base;
use App\Models\oa\MaterialWmsPlanBarcodeModel;
use App\Models\oa\MaterialWmsPlanStoreModel;


class MaterialWmsPlanModel extends Base
{
    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_wms_plan');

        $this->hasMany(
            'id',
            MaterialWmsPlanStoreModel::class,
            'plan_id',
            [
                'alias' => 'Store'
            ]
        );

        $this->hasMany(
            'id',
            MaterialWmsPlanBarcodeModel::class,
            'plan_id',
            [
                'alias' => 'Barcode'
            ]
        );
    }

}
