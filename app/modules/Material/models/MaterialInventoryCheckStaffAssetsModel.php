<?php
namespace App\Modules\Material\Models;
use App\Library\Enums\InventoryCheckEnums;
use App\Models\Base;
use App\Library\Enums\GlobalEnums;

/**
 * 员工盘点资产记录表
 * Class MaterialInventoryCheckStaffAssetsModel
 * @package App\Modules\Material\Models
 */
class MaterialInventoryCheckStaffAssetsModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_inventory_check_staff_assets');

        $this->hasMany(
            'id',
            MaterialAttachmentModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = '. InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK . ' and deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'Attachments',
            ]
        );
    }
}
