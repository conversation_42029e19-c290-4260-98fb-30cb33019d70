<?php

namespace App\Modules\Material\Models;

use App\Models\Base;
use App\Library\ErrCode;
use App\Modules\User\Models\TripImgModel;
use App\Library\Enums\MaterialClassifyEnums;

class MaterialCategoryModel extends Base
{

    public $id;

    public $name;

    public $code;

    public $parent_id;

    public $level;

    public $type;

    public $use_limit;

    public $status;

    public $sort;

    public $is_deleted;

    public $created_at;

    public $updated_at;


    /**
     * Initialize method for model.
     */
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('material_category');
    }

    /**
     * 分页获取数据
     * @Token
     * @Date: 2021-10-19 17:33
     * @return:
     **@author: peak pan
     */
    public function getCategoryName($data)
    {
        //分页获取所有分类
        $ids_ = format_array(array_filter(explode(',', implode(',', array_column($data, 'ids')))));
        $category_ids = format_array(array_filter(explode(',', implode(',', array_column($data, 'category_id')))));
        $ids = array_merge($ids_, $category_ids);
        $pic_arr = self::find([
            "conditions" => 'is_deleted =0 and id in ({ids:array}) ',
            "order" => 'level ASC',
            "bind" => [
                'ids' => $ids
            ],
            'columns' => 'id,name,code'
        ])->toArray();
        return $pic_arr ? array_column($pic_arr, null, 'id') : [];
    }

    /**
     * 获取冒个父类下的所有子类
     * @Token
     * @Date: 2021-10-20 21:03
     * @return:
     **@author: peak pan
     */
    public function getSonList($id,$groupByName='level')
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialCategoryModel::class);
            $builder->andWhere(" find_in_set(:ids:,ids)", ['ids' =>$id]);
            $builder->andWhere("is_deleted = :is_deleted:", ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]);
            $builder->groupBy($groupByName);
            $data = $builder->getQuery()->execute()->toArray();

            if (!empty($data)) {
                $data = array_column($data,'id');
            } else {
                $data = [$id];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
        }

        if (count($data) > 1000) {
            $this->getDI()->get('logger')->info('检索条件范围太大，请减小范围查询: ' . $id);
            $message = static::$t->_('material_category_select_max_count');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('检索异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 批量修改层级
     * @Token
     * @Date: 2021-10-19 17:12
     * @return:
     **@author: peak pan
     */
    public function getAllSave($ids, $level, $last_ids)
    {
        $newtime = date('Y-m-d H:i:s', time());
        $sql = "update material_category set level=" . $level . ", ids='" . $last_ids . "',updated_at='" . $newtime . "' where id in (" . implode(',', $ids) . ")";
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('分类批量修改失败==' . $sql, MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        return $bool;
    }


    /**
     * 批量删除
     * @Token
     * @Date: 2021-10-19 17:12
     * @return:s
     **@author: peak pan
     */
    public function getAllDel($ids)
    {

        $newtime = date('Y-m-d H:i:s', time());
        $sql = "update material_category set is_deleted=1,updated_at='" . $newtime . "'
                        where id in (" . implode(',', $ids) . ")";
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('分类批量删除失败==' . $sql, MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        return true;
    }

    /**
     * 指定分类下是否有启用的子类
     * @param integer $id 分类ID
     * @return bool
     */
    public function isHasChildrenUsing($id)
    {
        $count = MaterialCategoryModel::count([
            'conditions' => 'parent_id =:parent_id: and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id',
            'bind' => [
                'parent_id' =>$id,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ]);
        return $count > 0;
    }

    /**
     * 指定分类下是否有启用的子类
     * @param array $codes  编码集合
     * @return array 返回 id=>code的关联关系数组 和 所有存在子类的code
     */
    public function getHasChildrenData($codes)
    {
        if (empty($codes)){
            return [0=>[],1=>[]];
        }
        //查询code对应的id
        $data = MaterialCategoryModel::find([
            'conditions' => 'code in ({codes:array}) and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id,parent_id,code',
            'bind' => [
                'codes' =>$codes,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ])->toArray();
        if (empty($data)){
            return [0=>[],1=>[]];
        }
        $id_code_kv = array_column($data,'code','id');
        $ids = array_column($data,'id');
        //查询存在子类的id
        $data = MaterialCategoryModel::find([
            'conditions' => 'parent_id in ({parent_id:array}) and is_deleted = :is_deleted: and status = :status:',
            'columns' => 'id,parent_id',
            'bind' => [
                'parent_id' =>$ids,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                'status' => MaterialClassifyEnums::MATERIAL_START_USING
            ]
        ])->toArray();

        //$data[]['parent_id'] = 存在子类的id, 通过此id查找对应的codes, 获取存在子类的codes
        $has_children_codes = [];
        foreach ($data as $k=>$v){
            if (key_exists($v['parent_id'],$id_code_kv)){
                $has_children_codes[] = $id_code_kv[$v['parent_id']];
            }
        }
        return [0=>$id_code_kv,1=>$has_children_codes];
    }
}
