<?php

namespace App\Modules\Material\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysDepartmentModel;
use App\Models\oa\MaterialAssetReturnModel;
use App\Models\oa\MaterialAssetTransferLogBarcodeSummaryModel;
use App\Models\oa\MaterialSetReturnRepairTypeModel;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Purchase\Services\BaseService as PurchaseService;

class AssetTransferListService extends BaseService
{
    private static $instance;

    //转移来源
    // 1转移 = 1.资产部转移 2.员工互转 4.使用人信息自动刷新 6.离职资产转移 7.正式工转个人代理
    // 2领用 = 3.出库转移
    // 3批量退库 = 5.批量退库
    // 4退回入库 = 8退回入库
    public static $type_source_type_one = 1;
    public static $type_source_type_two = 2;
    public static $type_source_type_three = 3;
    public static $type_source_type_four = 4;

    //by我的资产-获取转移时列表 - 操作类型
    public static $by_type_transfer = 1;
    public static $by_type_cancel = 2;
    public static $by_type_return = 3;//退回提交
    public static $by_type_return_cancel = 4;//退回撤回
    //by待接收-获取接收/拒绝时列表 - 操作类型
    public static $by_type_receiver = 1;
    public static $by_type_reject = 2;

    //by端转移操作/撤销操作,全选时需要得到的前50个id的limit参数
    public static $by_get_asset_id_size = 50;
    public static $by_get_asset_id_offset = 0;
    //by端待接收列表-拒绝操作/接收操作,全选时需要得到的前50个id的limit参数
    public static $by_get_transfer_id_size = 50;
    public static $by_get_transfer_id_offset = 0;

    /**
     * 单例
     * @return AssetTransferListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //我的资产-列表-搜索-参数非必填
    public static $my_asset_list_not_must_params = [
        'name',
        'barcode',
        'asset_code',
        'sn_code',
        'status',
        'use',
    ];
    //我的资产-列表-搜索-参数验证
    public static $validate_my_asset_list_search = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'name' => 'StrLenGeLe:0,100',//资产名称
        'barcode' => 'StrLenGeLe:0,30', //barcode
        'asset_code' => 'StrLenGeLe:4,100', //资产编码-模糊
        'sn_code' => 'StrLenGeLe:3,50', //sn码
        'status' => 'Arr',//使用状态
        'status[*]' => 'IntGt:0|IntIn:' . MaterialEnums::MY_ASSET_STATUS_VALIDATE,
        'use' => 'IntIn:' . MaterialEnums::USE_VALIDATE,//使用方向
    ];
    //by端-提交转移时我的资产-列表-参数验证
    public static $validate_transfer_my_asset_list = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'ids' => 'Required|Arr|ArrLenGeLe:1,50',//资产id集合
        'ids[*]' => 'IntGt:0',//资产id
        'type' => 'Required|IntIn:1,2,3,4',//1.转移 2.撤销转移 3退回提交 4退回撤回
    ];
    //我的资产-详情-参数验证
    public static $validate_my_asset_detail = [
        'id' => 'Required|IntGt:0',
    ];

    //我转出-列表-搜索-非必填参数
    public static $my_transfer_list_not_must_params = [
        'to_staff_id',
        'status',
        'name',
        'asset_code',
        'sn_code',
        'transfer_at_start',
        'transfer_at_end',
        'finished_at_start',
        'finished_at_end',
        'express_no',
    ];
    //我转出-列表-搜索-参数验证
    public static $validate_my_transfer_list_search = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'to_staff_id' => 'IntGt:0',//接收人id
        'status' => 'ArrLenGe:1',//转移状态
        'status[*]' => 'IntGt:0|IntIn:' . MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE,
        'name' => 'StrLenGeLe:1,100',//资产名称
        'asset_code' => 'StrLenGeLe:4,100', //资产编码-模糊
        'sn_code' => 'StrLenGeLe:3,50', //sn码
        'transfer_at_start' => 'Date',//转移日期-开始  格式"yyyy-mm-dd"
        'transfer_at_end' => 'Date',//转移日期-结束  格式"yyyy-mm-dd"
        'finished_at_start' => 'Date',//完成日期-开始  格式"yyyy-mm-dd"
        'finished_at_end' => 'Date',//完成日期-结束  格式"yyyy-mm-dd"
        'express_no' => 'StrLenGe:1', //快递单号
    ];

    //待接收-列表-搜索-非必填参数
    public static $receiver_list_not_must_params = [
        'from_staff_id',
        'name',
        'asset_code',
        'sn_code',
        'transfer_at_start',
        'transfer_at_end',
        'express_no',
        'use',
    ];
    //待接收-列表-搜索-参数验证
    public static $validate_receiver_list_search = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'from_staff_id' => 'IntGt:0',//转出人id
        'from_staff_id_or_name' => 'Str',//by端-转交人
        'name' => 'StrLenGeLe:1,100',//资产名称
        'asset_code' => 'StrLenGeLe:4,100', //资产编码-模糊
        'sn_code' => 'StrLenGeLe:3,50', //sn码
        'transfer_at_start' => 'Date',//转移日期-开始  格式"yyyy-mm-dd"
        'transfer_at_end' => 'Date',//转移日期-结束  格式"yyyy-mm-dd"
        'express_no' => 'StrLenGe:1', //快递单号
        'use' => 'IntIn:' . MaterialEnums::USE_VALIDATE,//使用方向
    ];
    //待接收-列表-by端勾选接收/拒绝时详情页使用-列表-参数验证
    public static $validate_receiver_list_by_ids = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'ids' => 'Required|Arr|ArrLenGeLe:1,50',//转移id集合
        'ids[*]' => 'IntGt:0',//资产id
    ];

    //全部转移明细-列表-搜索-非必填参数
    public static $all_list_not_must_params = [
        'type',
        'to_staff_id',
        'from_staff_id',
        'status',
        'name',
        'asset_code',
        'sn_code',
        'transfer_at_start',
        'transfer_at_end',
        'finished_at_start',
        'finished_at_end',
        'express_no',
        'use',
    ];
    //全部转移明细-列表-搜索-参数验证
    public static $validate_all_list_search = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'type' => 'IntIn:' . MaterialEnums::ALL_TRANSFER_TYPE_VALIDATE,//类型 我转出,转给我
        'to_staff_id' => 'IntGt:0',//接收人id
        'from_staff_id' => 'IntGt:0',//调出人id
        'status' => 'ArrLenGe:1',//转移状态
        'status[*]' => 'IntGt:0|IntIn:' . MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE,
        'name' => 'StrLenGeLe:1,100',//资产名称
        'asset_code' => 'StrLenGeLe:4,100', //资产编码-模糊
        'sn_code' => 'StrLenGeLe:3,50', //sn码
        'transfer_at_start' => 'Date',//转移日期-开始  格式"yyyy-mm-dd"
        'transfer_at_end' => 'Date',//转移日期-结束  格式"yyyy-mm-dd"
        'finished_at_start' => 'Date',//完成日期-开始  格式"yyyy-mm-dd"
        'finished_at_end' => 'Date',//完成日期-结束  格式"yyyy-mm-dd"
        'express_no' => 'StrLenGe:1', //快递单号
        'use' => 'IntIn:' . MaterialEnums::USE_VALIDATE,//使用方向
    ];

    //全部转移明细-列表-搜索-非必填参数
    public static $search_list_not_must_params = [
        'to_staff_id',
        'to_company_id',
        'to_node_department_id',
        'to_sys_store_id',
        'from_staff_id',
        'from_company_id',
        'from_node_department_id',
        'from_sys_store_id',
        'status',
        'name',
        'asset_code',
        'sn_code',
        'asset_operation_type',
        'transfer_at_start',
        'transfer_at_end',
        'finished_at_start',
        'finished_at_end',
        'use',
    ];
    //数据查询-列表-搜索-参数验证
    public static $validate_search_list_search = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'to_staff_id' => 'IntGt:0',//接收人id
        'to_company_id' => 'IntGt:0',//接收公司id
        'to_node_department_id' => 'IntGt:0',//接收部门id
        'to_sys_store_id' => 'ArrLenGe:1',//接收网点id
        'to_sys_store_id[*]' => 'StrLenGe:1',//接收网点id
        'from_staff_id' => 'IntGt:0',//调出人id
        'from_company_id' => 'IntGt:0',//调出公司id
        'from_node_department_id' => 'IntGt:0',//调出部门id
        'from_sys_store_id' => 'ArrLenGe:1',//调出网点id
        'from_sys_store_id[*]' => 'StrLenGe:1',//调出网点id
        'status' => 'ArrLenGe:1',//转移状态
        'status[*]' => 'IntGt:0|IntIn:' . MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE,
        'name' => 'StrLenGeLe:1,100',//资产名称
        'asset_code[*]' => 'StrLenGeLe:4,100', //资产编码-模糊
        'sn_code[*]' => 'StrLenGeLe:3,50', //sn码
        'asset_operation_type' => 'IntGt:0|IntIn:1,2,3,4', //资产操作类型 1.转移(包含资产部转移和员工互转) 2.领用出库 3.批量退库 4退回入库
        'transfer_at_start' => 'Date',//转移日期-开始  格式"yyyy-mm-dd"
        'transfer_at_end' => 'Date',//转移日期-结束  格式"yyyy-mm-dd"
        'finished_at_start' => 'Date',//完成日期-开始  格式"yyyy-mm-dd"
        'finished_at_end' => 'Date',//完成日期-结束  格式"yyyy-mm-dd"
        'use' => 'IntIn:' . MaterialEnums::USE_VALIDATE,//使用方向
    ];

    //我的资产-详情-参数验证
    public static $validate_get_asset_list_by_ids = [
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,500',//资产台账id集合
        'asset_ids[*]' => 'IntGt:0',//资产台账id集合
        //'language' => 'Required|StrLenGe:1'//语言
    ];

    /**
     * 其他拦截逻辑
     * @param array $params 请求参数
     * @throws ValidationException
     */
    public function validateListOther($params)
    {
        // 转移时间/完成时间必须有一个,且时间最长12个月(不确定前端计算逻辑,宽限一点,每个月算31天)
        $three_month_times = 60 * 60 * 24 * 31 * 12;
        $transfer_at_start = $params['transfer_at_start'] ?? '';
        $transfer_at_end = $params['transfer_at_end'] ?? '';
        $finished_at_start = $params['finished_at_start'] ?? '';
        $finished_at_end = $params['finished_at_end'] ?? '';
        //不能不传
        $filter_params = $params;
        unset($filter_params['pageSize'],$filter_params['pageNum']);
        $filter_params = array_filter($filter_params);
        if (empty($filter_params) && empty($transfer_at_end) && empty($finished_at_end)) {
            throw new ValidationException(static::$t->_('transfer_search_list_date_must'), ErrCode::$VALIDATE_ERROR);
        }
        //转移时间范围不能大于12个月
        if (!empty($transfer_at_end)) {
            $date_diff = strtotime($transfer_at_end) - strtotime($transfer_at_start);
            if ($date_diff > $three_month_times) {
                throw new ValidationException(static::$t->_('transfer_at_more_than_three_months'), ErrCode::$VALIDATE_ERROR);
            }
        }
        if (!empty($finished_at_end)) {
            //完成时间范围不能大于12个月
            $date_diff = strtotime($finished_at_end) - strtotime($finished_at_start);
            if ($date_diff > $three_month_times) {
                throw new ValidationException(static::$t->_('finished_at_more_than_three_months'), ErrCode::$VALIDATE_ERROR);
            }
        }
    }

    /**
     * 我的资产-获取该菜单需要的枚举
     * @param $user
     * @param bool $is_by
     * @return array
     * @date 2022/11/13
     */
    public function getOptionsDefault($user, $is_by = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                //资产状态
                'asset_status' => [
                    MaterialEnums::ASSET_STATUS_USING => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_USING], //使用中
                    MaterialEnums::ASSET_STATUS_ALLOT => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_ALLOT], //调拨中
                    MaterialEnums::ASSET_STATUS_REPAIRING => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_REPAIRING], //维修中
                    MaterialEnums::ASSET_STATUS_REPAIRED => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_REPAIRED], //已报修
                    MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED], //待报废
                    MaterialEnums::ASSET_STATUS_LOST => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_LOST], //已丢失
                    MaterialEnums::ASSET_STATUS_RETURN_ING => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_RETURN_ING], //退回中
                    MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE => MaterialEnums::$asset_status[MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE], //闲置（在网点）
                ],
                //使用方向
                'use' => $is_by ? MaterialEnums::$use_by : MaterialEnums::$use,
                //转交方式
                'transfer_method' => MaterialEnums::$transfer_method,
                //全部转移明细-转移类型
                'all_transfer_list_type' => MaterialEnums::$all_transfer_list_type,
                //转移状态
                'transfer_status' => MaterialEnums::$transfer_log_status,
                //资产操作类型
                'asset_operation_type' => [
                    self::$type_source_type_one => 'asset_operation_type.transfer',
                    self::$type_source_type_two => 'asset_operation_type.storage',
                    self::$type_source_type_three => 'asset_operation_type.stock_return',
                    self::$type_source_type_four => 'asset_operation_type.return_storage',
                ],
                //退回方式
                'return_method' => MaterialEnums::$return_method,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
            //只有PC端需要,BY不需要
            if ($is_by == false) {
                //费用所属公司
                $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
                //获取用户设置的字段显示值
                $list_staff_set_columns_arr = [];
                $list_staff_set_columns = AssetAccountService::getInstance()->getStaffSetValue($user['id'], MaterialEnums::ASSET_TRANSFER_SET_USE_LIST);
                if ($list_staff_set_columns) {
                    $list_staff_set_columns_arr = explode(",", $list_staff_set_columns);
                }
                //用户设置的显示字段
                foreach (MaterialEnums::$asset_transfer_list_set_columns as $value) {
                    if(get_country_code() != GlobalEnums::MY_COUNTRY_CODE && (in_array($value,['from_contract_company_name','to_contract_company_name']))){
                        continue;
                    }
                    $data['list_set_columns'][] = [
                        'value' => $value,
                        'label' => static::$t->_('material_asset_transfer.' . $value),
                        'is_check' => $list_staff_set_columns_arr && in_array($value, $list_staff_set_columns_arr) ? true : false
                    ];
                }
            }
            //用户的签字图片(用来接收时显示)
            $data['sign_url'] = AssetTransferService::getInstance()->getSignUrl($user['id']);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取我的资产枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我的资产-列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @param bool $export 是否导出，true导出、false非导出
     * @param integer $count 导出记录数
     * @param bool $is_by 是否by端请求(BY端需要前50个id,做全选用 和 每个状态的数量统计)
     * @return array
     */
    public function getList($params, $locale, $user, $export = false, $count = 0, $is_by = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            // 参数验证
            $params = self::handleParams($params, self::$my_asset_list_not_must_params);
            Validation::validate($params, self::$validate_my_asset_list_search);
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //仅by需要status_count和fifty_ids
            if ($is_by) {
                $status_count = [
                    MaterialEnums::ASSET_STATUS_USING => 0,
                    MaterialEnums::ASSET_STATUS_ALLOT => 0,
                    MaterialEnums::ASSET_STATUS_REPAIRED => 0,
                    MaterialEnums::ASSET_STATUS_REPAIRING => 0,
                    MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED => 0,
                    MaterialEnums::ASSET_STATUS_LOST => 0,
                    MaterialEnums::ASSET_STATUS_RETURN_ING => 0,
                    MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE => 0,
                ];
                $data['status_count'] = $this->getListStatusCount($locale, $condition, $user, $status_count);
                $data['fifty_ids'] = [];
            }
            if ($export === false) {
                //列表查询总数，导出无需查询，在验证总导出数限制时已查询到，传递过来了
                $count = $this->getListCount($locale, $condition, $user);
            }
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                if ($export === false) {
                    $columns = 'main.id,main.bar_code,main.status,main.name_zh,main.name_en,main.name_local,main.asset_code,main.model,main.sn_code,main.use_land,main.use,main.old_asset_code,main.sys_store_id,main.store_name,main.use_land';
                } else {
                    $columns = 'main.id,main.bar_code,main.status,main.name_zh,main.name_en,main.name_local,main.asset_code,main.model,main.sn_code,main.use_land,main.use,main.old_asset_code';
                    $columns .= ',main.staff_id,main.node_department_name,main.store_name';
                }
                $builder->columns($columns);
                $builder->from(['main' => MaterialAssetsModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($locale, $items, $export, $is_by);
                //by需要返回各状态数量; 返回前50条id,用来全选
                if ($is_by) {
                    $data['fifty_ids'] = $this->getListFiftyIds($locale, $condition, $user);
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * BY端-提交转移时-我的资产-列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function getMyAssetByIds($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];

        try {
            // 参数验证
            Validation::validate($params, self::$validate_transfer_my_asset_list);
            $type = $params['type'];
            unset($params['type']);
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            $count = $this->getListCount($locale, $condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id,main.status,main.name_zh,main.name_en,main.name_local,main.asset_code,main.model,main.sn_code,main.use,main.old_asset_code,main.use_land,main.sys_store_id,main.store_name,main.staff_id,main.staff_name';
                $builder->columns($columns);
                $builder->from(['main' => MaterialAssetsModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($locale, $items, false, true);
                foreach ($items as $v) {
                    //转移时验证是否全部都是使用中
                    if ($type == self::$by_type_transfer) {
                        if (!in_array($v['status'], [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                            throw new ValidationException(static::$t->_('asset_transfer_by_status_not_using'), ErrCode::$VALIDATE_ERROR);
                        }
                    } else if ($type == self::$by_type_return) {
                        //退回时验证是否全部都是使用中、闲置（在网点）
                        if (!in_array($v['status'], [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                            throw new ValidationException(static::$t->_('material_asset_return_by_status_not_using'), ErrCode::$VALIDATE_ERROR);
                        }
                    } else if ($type ==self::$by_type_return_cancel) {
                        //退回撤销时验证是否全部都是退回中
                        if ($v['status'] != MaterialEnums::ASSET_STATUS_RETURN_ING) {
                            throw new ValidationException(static::$t->_('material_asset_return_by_status_not_returning'), ErrCode::$VALIDATE_ERROR);
                        }
                    } else {
                        //撤销时验证是否全部都是调拨中
                        if ($v['status'] != MaterialEnums::ASSET_STATUS_ALLOT) {
                            throw new ValidationException(static::$t->_('asset_transfer_by_status_not_allot'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-by-id-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @return int
     */
    public function getListCount($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetsModel::class]);
        $builder->columns('count(main.id) as count');
        //组合搜索条件
        $builder = $this->getCondition($locale, $builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 获取特定条件下的总数
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @param array $count_data 需要统计数量的status为key的数组
     * @return array
     */
    public function getListStatusCount($locale, $condition, $user, $count_data)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetsModel::class]);
        $builder->columns('count(main.id) as count,main.status');
        $builder->groupBy('main.status');
        //组合搜索条件, 不要状态搜索
        $condition['status'] = [];
        $builder = $this->getCondition($locale, $builder, $condition, $user);
        $group_count = $builder->getQuery()->execute()->toArray();
        foreach ($group_count as $status_count) {
            if (isset($count_data[$status_count['status']])) {
                $count_data[$status_count['status']] = (int)$status_count['count'];
            }
        }
        return $count_data;
    }

    /**
     * 获取前50个列表id(资产id)
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @return array
     */
    public function getListFiftyIds($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetsModel::class]);
        $builder->columns('main.id');
        //组合搜索条件
        $builder = $this->getCondition($locale, $builder, $condition, $user);
        $builder->orderby('main.id desc');
        $builder->limit(self::$by_get_asset_id_size, self::$by_get_asset_id_offset);
        $items = $builder->getQuery()->execute()->toArray();
        return array_column($items, 'id');
    }

    /**
     * 组装查询条件
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param $user
     * @return mixed
     */
    public function getCondition($locale, $builder, $condition, $user)
    {
        $staff_id = $user['id'];//员工工号
        $ids = !empty($condition['ids']) ? $condition['ids'] : [];//资产id集合
        $status = !empty($condition['status']) ? $condition['status'] : explode(',', MaterialEnums::MY_ASSET_STATUS_VALIDATE);//使用状态
        $asset_code = !empty($condition['asset_code']) ? trim($condition['asset_code']) : '';//资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? trim($condition['sn_code']) : '';//sn码
        $use = !empty($condition['use']) ? $condition['use'] : 0;//使用方向
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : [];//barcode
        //固定条件
        $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        if (!empty($ids)) {
            //资产id
            $ids = array_values($ids);
            $builder->inWhere('main.id', $ids);
        }
        if (!empty($asset_code)) {
            //资产编码
            $builder->andWhere('main.asset_code like :asset_code: or main.old_asset_code like :asset_code:', ['asset_code' => '%' . $asset_code . '%']);
        }
        if (!empty($name)) {
            //资产名称搜索
            $builder->andWhere('main.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        if (!empty($sn_code)) {
            //sn码搜索
            $builder->andWhere('main.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        if (!empty($status)) {
            //使用状态搜索
            $builder->inWhere('main.status', $status);
        }
        if (!empty($use)) {
            //使用方向
            $builder->andWhere('main.use = :use:', ['use' => $use]);
        }
        if (!empty($barcode)) {
            //barcode
            $builder->andWhere('main.bar_code = :bar_code:', ['bar_code' => $barcode]);
        }

        return $builder;
    }

    /**
     * 格式化我的资产列表
     * @param string $locale 语言
     * @param array $items 台账列表
     * @param bool $export 导出
     * @param bool $is_by 是否by端
     * @return array
     */
    private function handleListItems($locale, $items, $export, $is_by = false)
    {
        if (empty($items)) {
            return [];
        }
        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
        if ($is_by) {
            $use_text_enums = MaterialEnums::$use_by;
        } else {
            $use_text_enums = MaterialEnums::$use;
        }
        if ($export === true) {
            //导出
            $row_value = [];//组装导出需要的数据
            foreach ($items as $item) {
                $row_value[] = [
                    'asset_code' => $item['asset_code'],
                    'name' => $item[$name_key] ?? '',
                    'sn_code' => $item['sn_code'],
                    'old_asset_code' => $item['old_asset_code'],
                    'model' => $item['model'],
                    'barcode' => $item['bar_code'],
                    'status' => static::$t[MaterialEnums::$asset_status[$item['status']]],
                    'staff_id' => $item['staff_id'],
                    'node_department_name' => $item['node_department_name'],
                    'store_name' => $item['store_name'],
                    'use_land' => $item['use_land'],
                ];
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['name'] = $item[$name_key] ?? '';
                $item['status_text'] = isset(MaterialEnums::$asset_status[$item['status']]) ? static::$t[MaterialEnums::$asset_status[$item['status']]] : '';
                $item['use_text'] = isset($use_text_enums[$item['use']]) ? static::$t[$use_text_enums[$item['use']]] : '';
            }
        }
        return $items;
    }

    /**
     * 我的资产-获取详情
     * @param $params
     * @param $user
     * @param $locale
     * @return array
     * @date 2022/10/17
     */
    public function getDetail($params, $user, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_my_asset_detail);
            //查询资产台账数据
            $asset_data = AssetAccountService::getInstance()->getMaterialAssetInfoById($params['id']);
            //查询我转出的最新一条数据
            $from_me = MaterialAssetTransferLogModel::findFirst([
                'conditions' => 'asset_id = :asset_id: and from_staff_id = :from_staff_id: and is_deleted = :is_deleted:',
                'bind' => ['asset_id' => $params['id'], 'from_staff_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                'order' => 'id desc'
            ]);
            //查询转给我的最新一条数据
            $to_me = MaterialAssetTransferLogModel::findFirst([
                'conditions' => 'asset_id = :asset_id: and to_staff_id = :to_staff_id: and is_deleted = :is_deleted:',
                'bind' => ['asset_id' => $params['id'], 'to_staff_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                'order' => 'id desc'
            ]);
            $from_me_data = $from_me ? $from_me->toArray() : [];
            $to_me_data = $to_me ? $to_me->toArray() : [];
            $from_me_data = $this->handleDetailItems($from_me_data);
            $to_me_data = $this->handleDetailItems($to_me_data);
            //查询附件中的图片BMP、JPG、JPEG、PNG
            $attachment = $asset_data->getPicAttachment()->toArray();
            $attachment_img = [];
            foreach ($attachment as $v) {
                $condition_bmp = stristr($v['object_key'], '.BMP') !== false;
                $condition_jpg = stristr($v['object_key'], '.JPG') !== false;
                $condition_jpeg = stristr($v['object_key'], '.JPEG') !== false;
                $condition_png = stristr($v['object_key'], '.PNG') !== false;
                if ($condition_bmp || $condition_jpg || $condition_jpeg || $condition_png) {
                    $attachment_img[] = $v;
                }
            }
            //详情数据汇总
            $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
            $data = [
                'id' => $asset_data->id,
                'barcode' => $asset_data->bar_code,
                'name' => $asset_data->$name_key ?? '',
                'asset_code' => $asset_data->asset_code,
                'old_asset_code' => $asset_data->old_asset_code,
                'sn_code' => $asset_data->sn_code,
                'use_text' => isset(MaterialEnums::$use[$asset_data->use]) ? static::$t[MaterialEnums::$use[$asset_data->use]] : '',
                'model' => $asset_data->model,
                'brand' => $asset_data->brand,
                'staff_id' => $asset_data->staff_id,
                'staff_name' => $asset_data->staff_name,
                'node_department_id' => $asset_data->node_department_id,
                'node_department_name' => $asset_data->node_department_name,
                'sys_store_id' => $asset_data->sys_store_id,
                'store_name' => $asset_data->store_name,
                'company_id' => $asset_data->company_id,
                'company_name' => $asset_data->company_name,
                'use_land' => $asset_data->use_land,
                'attachments' => $attachment_img,
                'transfer_record_from_me' => $from_me_data ? $from_me_data : (object)[],
                'transfer_record_to_me' => $to_me_data ? $to_me_data : (object)[],
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-detail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 格式化我的资产列表
     * @param array $item 详情数据-一维数组
     * @return array
     */
    private function handleDetailItems($item)
    {
        if (empty($item)) {
            return [];
        }
        //处理
        $set_staff_name = $this->setStaffName([$item]);
        $item = current($set_staff_name);
        $item['status_text'] = isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '';
        $item['operator_remark'] = $item['remark'];
        return $item;
    }

    /**
     * 我的资产-获取详情(by端)
     * @param $params
     * @param $user
     * @param $locale
     * @return array
     * @date 2022/10/17
     */
    public function getDetailBy($params, $user, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_my_asset_detail);
            //1.查询资产台账数据
            $asset_data = AssetAccountService::getInstance()->getMaterialAssetInfoById($params['id']);
            $source_data = [];
            $receiver_data = [];
            $reject_data = [];
            $return_data = [];//资产退回详情
            //2.按条件查询[来源信息],[待接收信息],[拒绝信息]
            //调拨中查询[待接收人信息](转出人=当前用户)
            if ($asset_data->status == MaterialEnums::ASSET_STATUS_ALLOT) {
                $receiver_data = MaterialAssetTransferLogModel::findFirst([
                    'conditions' => 'asset_id = :asset_id: and from_staff_id = :from_staff_id: and is_deleted = :is_deleted: and status = :status:',
                    'bind' => ['asset_id' => $params['id'], 'from_staff_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED],
                    'order' => 'id desc'
                ]);
            } else if ($asset_data->status == MaterialEnums::ASSET_STATUS_RETURN_ING) {
                //退回中 查询 最新一条退回申请信息
                $return_info = MaterialAssetReturnModel::findFirst([
                    'conditions' => 'asset_id = :asset_id:',
                    'bind' => ['asset_id' => $params['id']],
                    'order' => 'created_at DESC'
                ]);
                if ($return_info) {
                    $type_info = MaterialSetReturnRepairTypeModel::findFirst([
                        'columns' => get_lang_field_name('name_', static::$language) . ' as name',
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $return_info->type_id]
                    ]);
                    $return_data = [
                        'type_name' => $type_info ? $type_info->name : '',//退回类型：显示退回类型，根据系统语言展示，中-中，英-英，非中英-当地语言
                        'return_method' => $return_info->return_method,//退回方式
                        'return_method_text' => isset(MaterialEnums::$transfer_method[$return_info->return_method]) ? static::$t[MaterialEnums::$transfer_method[$return_info->return_method]] : '',
                        'express_no' => $return_info->express_no,//快递单号：退回方式为“邮寄退回”，显示该字段
                        'return_reason' => $return_info->return_reason,//退回原因：支持换行展示
                    ];
                }
            } else {
                //非调拨中的查询[来源信息](接收人=当前登录人的转交接收信息)
                $source_data = MaterialAssetTransferLogModel::findFirst([
                    'conditions' => 'asset_id = :asset_id: and to_staff_id = :to_staff_id: and is_deleted = :is_deleted: and status = :status:',
                    'bind' => ['asset_id' => $params['id'], 'to_staff_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED],
                    'order' => 'id desc'
                ]);
                //使用中状态,查询转交人=当前登录人的转交[拒绝信息]
                if ($asset_data->status == MaterialEnums::ASSET_STATUS_USING) {
                    $reject_data = MaterialAssetTransferLogModel::findFirst([
                        'conditions' => 'asset_id = :asset_id: and from_staff_id = :from_staff_id: and is_deleted = :is_deleted: and status = :status:',
                        'bind' => ['asset_id' => $params['id'], 'from_staff_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_REJECTED],
                        'order' => 'id desc'
                    ]);
                }
            }
            //4.[来源信息],[待接收信息],[拒绝信息]赋值
            //来源信息
            if ($source_data) {
                $view_name = $this->getViewName($source_data->from_staff_id, true);
                $return_source_data = [
                    'staff_name' => $view_name,
                    'date' => $source_data->finished_at
                ];
            }
            //待接收信息
            if ($receiver_data) {
                $view_name = $this->getViewName($receiver_data->to_staff_id, true);
                $return_receiver_data = [
                    'status_text' => static::$t[MaterialEnums::$transfer_log_status[$receiver_data->status]],
                    'staff_name' => $view_name,
                    'date' => $receiver_data->transfer_at,
                    'remark' => $receiver_data->transfer_remark
                ];
            }
            //拒绝信息
            if ($reject_data) {
                $view_name = $this->getViewName($reject_data->operator_id, true);
                $return_reject_data = [
                    'status_text' => static::$t[MaterialEnums::$transfer_log_status[$reject_data->status]],
                    'staff_name' => $view_name,
                    'date' => $reject_data->finished_at,
                    'remark' => $reject_data->remark
                ];
            }
            //5.详情数据汇总
            $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
            $data = [
                'id' => $asset_data->id,
                'barcode' => $asset_data->bar_code,
                'name' => $asset_data->$name_key ?? '',
                'asset_code' => $asset_data->asset_code,
                'old_asset_code' => $asset_data->old_asset_code,
                'sn_code' => $asset_data->sn_code,
                'status' => $asset_data->status,
                'status_text' => isset(MaterialEnums::$asset_status[$asset_data->status]) ? static::$t[MaterialEnums::$asset_status[$asset_data->status]] : '',
                'use_text' => isset(MaterialEnums::$use_by[$asset_data->use]) ? static::$t[MaterialEnums::$use_by[$asset_data->use]] : '',
                'model' => $asset_data->model,
                'brand' => $asset_data->brand,
                'staff_id' => $asset_data->staff_id,
                'staff_name' => $this->getViewName($asset_data->staff_id, false, $asset_data->staff_name),
                'node_department_id' => $asset_data->node_department_id,
                'node_department_name' => $asset_data->node_department_name,
                'sys_store_id' => $asset_data->sys_store_id,
                'store_name' => $asset_data->store_name,
                'use_land' => $asset_data->use_land,
                'return_source_data' => $return_source_data ?? (object)[],
                'return_receiver_data' => $return_receiver_data ?? (object)[],
                'return_reject_data' => $return_reject_data ?? (object)[],
                'return_data' => $return_data ?? (object)[],
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-by-detail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我的资产-导出
     * @param $params
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function export($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $params = self::handleParams($params, self::$my_asset_list_not_must_params);
            Validation::validate($params, self::$validate_my_asset_list_search);
            $condition = $params;
            $count = $this->getListCount($locale, $params, $user);
            if ($count > MaterialEnums::MY_MATERIAL_ASSET_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max' => MaterialEnums::MY_MATERIAL_ASSET_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $page_size = 2000;
            $step = ceil($count / $page_size);
            $row_values = [];
            for ($i = 1; $i <= $step; $i++) {
                $condition['pageNum'] = $i;
                $condition['pageSize'] = $page_size;
                $list = $this->getList($condition, $locale, $user, true, $count);
                if ($list['code'] != ErrCode::$SUCCESS) {
                    throw new BusinessException('列表数据查询失败', ErrCode::$ASSET_TRANSFER_MY_ASSETS_EXPORT_ERROR);
                }
                $rows = $list['data']['items'];
                $row_values = array_merge($row_values, $rows);
            }
            $row_values = array_map('array_values', $row_values);
            $file_name = 'my_material_asset_' . date('YmdHis');
            $header = [
                static::$t->_('material_asset.asset_code'),//资产编码
                static::$t->_('material_asset.name'),//资产名称
                static::$t->_('material_asset.sn_code'),//sn码
                static::$t->_('material_asset.old_asset_code'),//旧资产编码
                static::$t->_('material_asset.model'),//规格型号
                static::$t->_('material_asset.barcode'),//barcode
                static::$t->_('material_asset.status'),//使用状态
                static::$t->_('material_asset.staff_id'),//使用人工号
                static::$t->_('material_asset.node_department_name'),//使用部门名称
                static::$t->_('material_asset.store_name'),//使用网点名称
                static::$t->_('material_asset.use_land'),//使用地信息

            ];
            $result = $this->exportExcel($header, $row_values, $file_name);
            $data = $result['data'] ?? [];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('download-my-asset-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我转出-列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function getMyTransferList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];

        try {
            // 参数验证
            $params = self::handleParams($params, self::$my_transfer_list_not_must_params);
            Validation::validate($params, self::$validate_my_transfer_list_search);
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            $count = $this->getMyTransferListCount($locale, $condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'log.id,log.status,asset.bar_code,asset.name_zh,asset.name_en,asset.name_local,
                asset.asset_code,asset.model,asset.sn_code,asset.use_land,asset.use,asset.old_asset_code,
                log.to_staff_id,log.to_node_department_name,log.to_node_department_id,log.to_sys_store_id,log.to_store_name,
                log.transfer_at,log.transfer_operator_id,log.transfer_method,log.express_no,log.transfer_remark,log.finished_at,log.remark as operator_remark';
                $builder->columns($columns);
                $builder->from(['log' => MaterialAssetTransferLogModel::class]);
                $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
                //组合搜索条件
                $builder = $this->getMyTransferCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('log.status asc,log.updated_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleMyTransferListItems($locale, $items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-transfer-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我转出列表-获取特定条件下的总数
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @return int
     */
    public function getMyTransferListCount($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('count(log.id) as count');
        //组合搜索条件
        $builder = $this->getMyTransferCondition($locale, $builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 我转出列表-组装查询条件
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     */
    public function getMyTransferCondition($locale, $builder, $condition, $user)
    {
        $from_staff_id = $user['id'];//转出员工工号
        $to_staff_id = !empty($condition['to_staff_id']) ? trim($condition['to_staff_id']) : '';//接收员工工号
        $status = !empty($condition['status']) ? $condition['status'] : explode(',', MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE);//转移状态
        $asset_code = !empty($condition['asset_code']) ? trim($condition['asset_code']) : '';//资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? trim($condition['sn_code']) : '';//sn码
        $transfer_at_start = !empty($condition['transfer_at_start']) ? $condition['transfer_at_start'] : '';//转移日期-开始
        $transfer_at_end = !empty($condition['transfer_at_end']) ? $condition['transfer_at_end'] : '';//转移日期-结束
        $finished_at_start = !empty($condition['finished_at_start']) ? $condition['finished_at_start'] : '';//完成日期-开始
        $finished_at_end = !empty($condition['finished_at_end']) ? $condition['finished_at_end'] : '';//完成日期-结束
        $express_no = !empty($condition['express_no']) ? $condition['express_no'] : '';//快递单号

        $builder->where('log.from_staff_id = :from_staff_id:', ['from_staff_id' => $from_staff_id]);
        $builder->andWhere('log.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //接收人
        if (!empty($to_staff_id)) {
            $builder->andWhere('log.to_staff_id = :to_staff_id:', ['to_staff_id' => $to_staff_id]);
        }
        //转移状态
        if (!empty($status)) {
            $builder->inWhere('log.status', $status);
        }
        //资产名称搜索
        if (!empty($name)) {
            $builder->andWhere('asset.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        //资产编码
        if (!empty($asset_code)) {
            $builder->andWhere('asset.asset_code like :asset_code: or asset.old_asset_code like :asset_code:', ['asset_code' => '%' . $asset_code . '%']);
        }
        //sn码搜索
        if (!empty($sn_code)) {
            $builder->andWhere('asset.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        //转移日期
        if (!empty($transfer_at_start)) {
            $builder->andWhere('log.transfer_at >= :transfer_at_start:', ['transfer_at_start' => $transfer_at_start . ' 00:00:00']);
        }
        if (!empty($transfer_at_end)) {
            $builder->andWhere('log.transfer_at <= :transfer_at_end:', ['transfer_at_end' => $transfer_at_end . ' 23:59:59']);
        }
        if (!empty($finished_at_start)) {
            $builder->andWhere('log.finished_at >= :finished_at_start:', ['finished_at_start' => $finished_at_start . ' 00:00:00']);
        }
        if (!empty($finished_at_end)) {
            $builder->andWhere('log.finished_at <= :finished_at_end:', ['finished_at_end' => $finished_at_end . ' 23:59:59']);
        }
        //快递单号
        if (!empty($express_no)) {
            $builder->andWhere('log.express_no = :express_no:', ['express_no' => $express_no]);
        }

        return $builder;
    }

    /**
     * 我转出列表-格式化
     * @param string $locale 语言
     * @param array $items 列表数据
     * @return array
     */
    private function handleMyTransferListItems($locale, $items)
    {
        if (empty($items)) {
            return [];
        }
        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
        //获取员工姓名
        $items = $this->setStaffName($items);
        foreach ($items as &$item) {
            $item['name'] = $item[$name_key];
            $item['status_text'] = $item['transfer_method_text'] = '';
            if (!empty($item['status'])) {
                $item['status_text'] = isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '';
            }
            if (!empty($item['transfer_method'])) {
                $item['transfer_method_text'] = isset(MaterialEnums::$transfer_method[$item['transfer_method']]) ? static::$t[MaterialEnums::$transfer_method[$item['transfer_method']]] : '';
            }
            $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
            $item['to_staff_name'] = $item['to_staff_name'] ?? '';
            $item['transfer_operator_name'] = $item['transfer_operator_name'] ?? '';
            $item['finished_at'] = $item['finished_at'] ?? '';
            $item['transfer_at'] = $item['transfer_at'] ?? '';
        }
        return $items;
    }

    /**
     * 待接收-列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @param bool $is_by 是否by端请求,BY端需要获取前50个id(做全选用)
     * @return array
     */
    public function receiverList($params, $locale, $user, $is_by = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
            'fifty_ids' => [],
            'fifty_asset_ids' => []
        ];

        try {
            // 参数验证
            $params = self::handleParams($params, self::$receiver_list_not_must_params);
            Validation::validate($params, self::$validate_receiver_list_search);
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            $count = $this->getReceiverListCount($locale, $condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'log.id,log.asset_id,log.status,asset.bar_code,asset.name_zh,asset.name_en,asset.name_local,
                asset.asset_code,asset.model,asset.sn_code,asset.use_land,asset.use,asset.old_asset_code,
                log.from_staff_id,log.from_node_department_name,log.from_node_department_id,log.from_sys_store_id,log.from_store_name,
                log.transfer_at,log.transfer_operator_id,log.transfer_method,log.express_no,log.transfer_remark,log.finished_at';
                $builder->columns($columns);
                $builder->from(['log' => MaterialAssetTransferLogModel::class]);
                $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
                //组合搜索条件
                $builder = $this->getReceiverCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('log.updated_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleReceiverListItems($locale, $items, $is_by);
                //by端来查询返回前50条id,用来全选
                if ($is_by) {
                    $fifty_data = $this->getReceiverListFiftyIds($locale, $condition, $user);
                    $data['fifty_ids'] = array_column($fifty_data, 'id');
                    $data['fifty_asset_ids'] = array_column($fifty_data, 'asset_id');
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-receiver-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 待接收-列表-by勾选转移时转移页面使用
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function receiverListByIds($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];

        try {
            // 参数验证
            Validation::validate($params, self::$validate_receiver_list_by_ids);
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            $count = $this->getReceiverListCount($locale, $condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'asset.name_zh,asset.name_en,asset.name_local,
                asset.asset_code,asset.sn_code,asset.use,asset.status as asset_status,log.from_staff_id,log.transfer_at';
                $builder->columns($columns);
                $builder->from(['log' => MaterialAssetTransferLogModel::class]);
                $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
                //组合搜索条件
                $builder = $this->getReceiverCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('log.updated_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleReceiverListItems($locale, $items, true);
                foreach ($items as $v) {
                    //拒绝时/接收时验证是否全部都是调拨中或出库中
                    if (!in_array($v['asset_status'], [MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_OUT_STORAGE])) {
                        throw new ValidationException(self::$t['asset_transfer_by_receiver_status_not_allot'], ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-receiver-list-by-ids-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 待接收-获取特定条件下的总数
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param array $user 登录人信息
     * @return int
     */
    public function getReceiverListCount($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('count(log.id) as count');
        //组合搜索条件
        $builder = $this->getReceiverCondition($locale, $builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 获取前50个列表id(转移id)
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @return array
     */
    public function getReceiverListFiftyIds($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('log.id, log.asset_id');
        //组合搜索条件
        $builder = $this->getReceiverCondition($locale, $builder, $condition, $user);
        //!注意这里很重要,一定要保持和列表排序一致
        $builder->orderby('log.updated_at desc');
        $builder->limit(self::$by_get_transfer_id_size, self::$by_get_transfer_id_offset);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 待接收-组装查询条件
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param array $user 登录人信息
     * @return mixed
     */
    public function getReceiverCondition($locale, $builder, $condition, $user)
    {
        $to_staff_id = $user['id'];//接收员工工号
        $ids = !empty($condition['ids']) ? $condition['ids'] : [];//id集合
        $from_staff_id = !empty($condition['from_staff_id']) ? trim($condition['from_staff_id']) : 0;//转出员工工号
        $from_staff_id_or_name = !empty($condition['from_staff_id_or_name']) ? trim($condition['from_staff_id_or_name']) : '';//转出员工工号或姓名模糊搜索(by端转交人)
        $asset_code = !empty($condition['asset_code']) ? trim($condition['asset_code']) : '';//资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? trim($condition['sn_code']) : '';//sn码
        $transfer_at_start = !empty($condition['transfer_at_start']) ? $condition['transfer_at_start'] : '';//转移日期-开始
        $transfer_at_end = !empty($condition['transfer_at_end']) ? $condition['transfer_at_end'] : '';//转移日期-结束
        $express_no = !empty($condition['express_no']) ? $condition['express_no'] : '';//快递单号
        $use = !empty($condition['use']) ? $condition['use'] : '';//使用方向

        if (!empty($ids)) {
            //转移id
            $ids = array_values($ids);
            $builder->inWhere('log.id', $ids);
        }
        //固定条件
        $builder->andWhere('log.to_staff_id = :to_staff_id: and log.status = :status: and log.is_deleted = :is_deleted:',
            ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'to_staff_id' => $to_staff_id, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED]);
        //转出员工
        if (!empty($from_staff_id)) {
            $builder->andWhere('log.from_staff_id = :from_staff_id:', ['from_staff_id' => $from_staff_id]);
        }
        if (!empty($from_staff_id_or_name)) {
            $builder->andWhere('log.from_staff_id like :from_staff_id_like: or log.from_staff_name like :from_staff_name_like:', ['from_staff_id_like' => '%' . $from_staff_id_or_name . '%', 'from_staff_name_like' => '%' . $from_staff_id_or_name . '%']);
        }
        //资产名称搜索
        if (!empty($name)) {
            $builder->andWhere('asset.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        //资产编码
        if (!empty($asset_code)) {
            $builder->andWhere('asset.asset_code like :asset_code: or asset.old_asset_code like :asset_code:', ['asset_code' => '%' . $asset_code . '%']);
        }
        //sn码搜索
        if (!empty($sn_code)) {
            $builder->andWhere('asset.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        //转移日期
        if (!empty($transfer_at_start)) {
            $builder->andWhere('log.transfer_at >= :transfer_at_start:', ['transfer_at_start' => $transfer_at_start . ' 00:00:00']);
        }
        if (!empty($transfer_at_end)) {
            $builder->andWhere('log.transfer_at <= :transfer_at_end:', ['transfer_at_end' => $transfer_at_end . ' 23:59:59']);
        }
        //使用方向
        if (!empty($use)) {
            $builder->andWhere('asset.use = :use:', ['use' => $use]);
        }
        //快递单号
        if (!empty($express_no)) {
            $builder->andWhere('log.express_no = :express_no:', ['express_no' => $express_no]);
        }

        return $builder;
    }

    /**
     * 待接收-格式化
     * @param string $locale 语言
     * @param array $items 列表数据
     * @param bool $is_by 是否by端请求
     * @return array
     */
    private function handleReceiverListItems($locale, $items, $is_by)
    {
        if (empty($items)) {
            return [];
        }
        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
        //获取员工姓名
        $items = $this->setStaffName($items);
        if ($is_by) {
            $use_text_enums = MaterialEnums::$use_by;
        } else {
            $use_text_enums = MaterialEnums::$use;
        }
        foreach ($items as &$item) {
            $item['name'] = $item[$name_key];
            $item['status_text'] = $item['transfer_method_text'] = '';
            if (!empty($item['status'])) {
                $item['status_text'] = isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '';
            }
            if (!empty($item['transfer_method'])) {
                $item['transfer_method_text'] = isset(MaterialEnums::$transfer_method[$item['transfer_method']]) ? static::$t[MaterialEnums::$transfer_method[$item['transfer_method']]] : '';
            }
            $item['use_text'] = isset($use_text_enums[$item['use']]) ? static::$t[$use_text_enums[$item['use']]] : '';
            $item['transfer_operator_name'] = $item['transfer_operator_name'] ?? '';
            $item['finished_at'] = $item['finished_at'] ?? '';
            $item['transfer_at'] = $item['transfer_at'] ?? '';
        }

        return $items;
    }

    /**
     * 待接收-获取详情(by端使用)
     * @param $params
     * @param $locale
     * @return array
     * @date 2022/10/17
     */
    public function getReceiverDetail($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_my_asset_detail);
            //查询转移信息
            $transfer_data = MaterialAssetTransferLogModel::findFirst([
                'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                'bind' => ['id' => $params['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            ]);
            if (empty($transfer_data)) {
                throw new BusinessException('转移记录不存在,id=' . $params['id'], ErrCode::$ASSET_TRANSFER_RECEIVER_DETAIL_ERROR);
            }
            //查询资产台账数据
            $asset_data = AssetAccountService::getInstance()->getMaterialAssetInfoById($transfer_data->asset_id);
            //详情数据汇总
            $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
            //资产详情
            $asset_info = [
                'name' => $asset_data->$name_key ?? '',
                'use_text' => isset(MaterialEnums::$use_by[$asset_data->use]) ? static::$t[MaterialEnums::$use_by[$asset_data->use]] : '',
                'asset_code' => $asset_data->asset_code,
                'old_asset_code' => $asset_data->old_asset_code,
                'sn_code' => $asset_data->sn_code,
            ];
            //转交人信息
            $staff_data = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, name, mobile_company',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $transfer_data->transfer_operator_id,
                ]
            ]);
            $staff_view_name = $staff_data ? $this->getViewName($staff_data->staff_info_id, false, $staff_data->name) : '';
            $staff_mobile = $staff_data ? $staff_data->mobile_company : '';
            //转移详情
            $transfer_info = [
                'transfer_method' => isset(MaterialEnums::$transfer_method[$transfer_data->transfer_method]) ? static::$t[MaterialEnums::$transfer_method[$transfer_data->transfer_method]] : '',
                'express_no' => $transfer_data->express_no,
                'transfer_operator_name' => $staff_view_name,
                'transfer_operator_mobile' => $staff_mobile,
                'transfer_remark' => $transfer_data->transfer_remark,
                'transfer_at' => $transfer_data->transfer_at ?? '',
            ];
            //返回数据
            $data = [
                'asset_info' => $asset_info,
                'transfer_info' => $transfer_info ?? (object)[],
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-receiver-detail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 全部转移记录-列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function getAllTransferList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            // 参数验证
            $params = self::handleParams($params, self::$all_list_not_must_params);
            Validation::validate($params, self::$validate_all_list_search);

            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            $count = $this->getAllTransferListCount($locale, $condition, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'log.id,log.status,asset.bar_code,asset.name_zh,asset.name_en,asset.name_local,
                asset.asset_code,asset.model,asset.sn_code,asset.use_land,asset.use,asset.old_asset_code,
                log.from_staff_id,log.from_node_department_name,log.from_store_name,
                log.to_staff_id,log.to_node_department_name,log.to_node_department_id,log.to_sys_store_id,log.to_store_name,
                log.transfer_at,log.transfer_operator_id,log.transfer_method,log.express_no,log.transfer_remark,log.finished_at,log.remark';
                $builder->columns($columns);
                $builder->from(['log' => MaterialAssetTransferLogModel::class]);
                $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
                //组合搜索条件
                $builder = $this->getAllTransferCondition($locale, $builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('log.updated_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleAllTransferListItems($locale, $items, $user);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-all-asset-transfer-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 全部转移记录-获取特定条件下的总数
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @param $user
     * @return int
     */
    public function getAllTransferListCount($locale, $condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('count(log.id) as count');
        //组合搜索条件
        $builder = $this->getAllTransferCondition($locale, $builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 全部转移记录-组装查询条件
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param $user
     * @return mixed
     */
    public function getAllTransferCondition($locale, $builder, $condition, $user)
    {

        $from_staff_id = !empty($condition['from_staff_id']) ? trim($condition['from_staff_id']) : '';//转出员工工号
        $to_staff_id = !empty($condition['to_staff_id']) ? trim($condition['to_staff_id']) : '';//接收员工工号
        $status = !empty($condition['status']) ? $condition['status'] : explode(',', MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE);//转移状态
        $asset_code = !empty($condition['asset_code']) ? trim($condition['asset_code']) : '';//资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? trim($condition['sn_code']) : '';//sn码
        $transfer_at_start = !empty($condition['transfer_at_start']) ? $condition['transfer_at_start'] : '';//转移日期-开始
        $transfer_at_end = !empty($condition['transfer_at_end']) ? $condition['transfer_at_end'] : '';//转移日期-结束
        $finished_at_start = !empty($condition['finished_at_start']) ? $condition['finished_at_start'] : '';//完成日期-开始
        $finished_at_end = !empty($condition['finished_at_end']) ? $condition['finished_at_end'] : '';//完成日期-结束
        $express_no = !empty($condition['express_no']) ? $condition['express_no'] : '';//快递单号
        $type = !empty($condition['type']) ? trim($condition['type']) : MaterialEnums::ALL_TRANSFER_TYPE_ALL;//类型,没填就是全部
        $use = !empty($condition['use']) ? $condition['use'] : 0;//使用方向


        $builder->where('log.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //转移类型
        if ($type == MaterialEnums::ALL_TRANSFER_TYPE_FROM_ME) {
            //我转出
            $builder->andWhere('log.from_staff_id = :from_staff_id:', ['from_staff_id' => $user['id']]);
            //我转出可以搜索接收人
            if (!empty($to_staff_id)) {
                $builder->andWhere('log.to_staff_id = :to_staff_id:', ['to_staff_id' => $to_staff_id]);
            }
        } elseif ($type == MaterialEnums::ALL_TRANSFER_TYPE_TO_ME) {
            //转给我
            $builder->andWhere('log.to_staff_id = :to_staff_id:', ['to_staff_id' => $user['id']]);
            //转给我可以搜索转出人
            if (!empty($from_staff_id)) {
                $builder->andWhere('log.from_staff_id = :from_staff_id:', ['from_staff_id' => $from_staff_id]);
            }
        } elseif ($type == MaterialEnums::ALL_TRANSFER_TYPE_ALL) {
            //全部
            $builder->andWhere('log.from_staff_id = :from_staff_id: or log.to_staff_id = :to_staff_id:', ['from_staff_id' => $user['id'], 'to_staff_id' => $user['id']]);
        }

        //转移状态
        if (!empty($status)) {
            $builder->inWhere('log.status', $status);
        }
        //资产名称搜索
        if (!empty($name)) {
            $builder->andWhere('asset.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        //资产编码
        if (!empty($asset_code)) {
            $builder->andWhere('asset.asset_code like :asset_code: or asset.old_asset_code like :asset_code:', ['asset_code' => '%' . $asset_code . '%']);
        }
        //sn码搜索
        if (!empty($sn_code)) {
            $builder->andWhere('asset.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        //转移日期
        if (!empty($transfer_at_start)) {
            $builder->andWhere('log.transfer_at >= :transfer_at_start:', ['transfer_at_start' => $transfer_at_start . ' 00:00:00']);
        }
        if (!empty($transfer_at_end)) {
            $builder->andWhere('log.transfer_at <= :transfer_at_end:', ['transfer_at_end' => $transfer_at_end . ' 23:59:59']);
        }
        if (!empty($finished_at_start)) {
            $builder->andWhere('log.finished_at >= :finished_at_start:', ['finished_at_start' => $finished_at_start . ' 00:00:00']);
        }
        if (!empty($finished_at_end)) {
            $builder->andWhere('log.finished_at <= :finished_at_end:', ['finished_at_end' => $finished_at_end . ' 23:59:59']);
        }
        //快递单号
        if (!empty($express_no)) {
            $builder->andWhere('log.express_no = :express_no:', ['express_no' => $express_no]);
        }
        //使用方向
        if (!empty($use)) {
            $builder->andWhere('asset.use = :use:', ['use' => $use]);
        }

        return $builder;
    }

    /**
     * 全部转移记录-格式化
     * @param string $locale 语言
     * @param array $items 列表数据
     * @param $user
     * @return array
     */
    private function handleAllTransferListItems($locale, $items, $user)
    {
        if (empty($items)) {
            return [];
        }
        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
        //获取员工名称
        $items = $this->setStaffName($items);
        //整合返回值
        foreach ($items as &$item) {
            if ($user['id'] == $item['from_staff_id']) {
                $item['type'] = MaterialEnums::ALL_TRANSFER_TYPE_FROM_ME;
                $item['type_text'] = static::$t[MaterialEnums::$all_transfer_list_type[$item['type']]];
            } elseif ($user['id'] == $item['to_staff_id']) {
                $item['type'] = MaterialEnums::ALL_TRANSFER_TYPE_TO_ME;
                $item['type_text'] = static::$t[MaterialEnums::$all_transfer_list_type[$item['type']]];
            } else {
                $item['type'] = 0;
                $item['type_text'] = '';
            }
            $item['name'] = $item[$name_key];
            $item['status_text'] = isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '';
            $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
            $item['transfer_at'] = $item['transfer_at'] ?? '';
            $item['finished_at'] = $item['finished_at'] ?? '';
        }
        return $items;
    }

    /**
     * 数据查询-列表
     * @param array $params 参数
     * @param $user
     * @param bool $export 是否导出
     * @param int $count 数据总量(导出传)
     * @return array
     */
    public function getTransferSearchList($params, $user, $export = false, $count = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            // 参数验证
            $params = self::handleParams($params, self::$search_list_not_must_params);
            Validation::validate($params, self::$validate_search_list_search);
            $this->validateListOther($params);
            //开始查询
            $condition = $params;
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //列表查询总数
            if ($export === false) {
                //列表查询总数，导出无需查询，在验证总导出数限制时已查询到，传递过来了
                $count = $this->getTransferSearchListCount($condition, $user);
            }
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'asset.bar_code,asset.name_zh,asset.name_en,asset.name_local,
                asset.asset_code,asset.model,asset.sn_code,asset.use_land,asset.use,asset.old_asset_code,
                log.id,log.status,log.transfer_at,log.operator_id,log.transfer_operator_id,log.transfer_method,log.express_no,log.transfer_remark,log.finished_at,log.remark as operator_remark,log.transfer_type,
                log.to_staff_id,log.to_node_department_name,log.to_node_department_id,log.to_sys_store_id,log.to_store_name,log.to_company_id,log.to_company_name,log.to_pc_code,
                log.from_contract_company_id,log.from_contract_company_name,log.to_contract_company_id,log.to_contract_company_name,
                log.from_staff_id,log.from_node_department_name,log.from_node_department_id,log.from_sys_store_id,log.from_store_name,log.from_company_id,log.from_company_name,log.from_pc_code,log.aor_no';
                $builder->columns($columns);
                $builder->from(['log' => MaterialAssetTransferLogModel::class]);
                $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
                //组合搜索条件
                $builder = $this->getTransferSearchCondition($builder, $condition, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('log.status asc,log.updated_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleTransferSearchListItems($items, $export);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-transfer-search-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 数据查询-获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @param $user
     * @return int
     * @throws ValidationException
     */
    public function getTransferSearchListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['log' => MaterialAssetTransferLogModel::class]);
        $builder->leftjoin(MaterialAssetsModel::class, 'log.asset_id = asset.id', 'asset');
        $builder->columns('count(log.id) as count');
        //组合搜索条件
        $builder = $this->getTransferSearchCondition($builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 数据查询-组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param $user
     * @throws ValidationException
     * @return mixed
     */
    public function getTransferSearchCondition($builder, $condition, $user)
    {
        //资产数据管控组-可看数据权限范围
        $data_permission = MaterialSettingService::getInstance()->getStaffDataPermissionGroup($user['id'], MaterialSettingService::DATA_PERMISSION_ASSET_TRANSFER);

        $from_staff_id = !empty($condition['from_staff_id']) ? trim($condition['from_staff_id']) : '';//转出员工工号
        $from_company_id = !empty($condition['from_company_id']) ? trim($condition['from_company_id']) : '';//转出公司id
        $from_node_department_id = !empty($condition['from_node_department_id']) ? trim($condition['from_node_department_id']) : '';//转出部门id
        $from_sys_store_id = !empty($condition['from_sys_store_id']) ? $condition['from_sys_store_id'] : [];//转出网点id
        $to_company_id = !empty($condition['to_company_id']) ? trim($condition['to_company_id']) : '';//接收公司id
        $to_node_department_id = !empty($condition['to_node_department_id']) ? trim($condition['to_node_department_id']) : '';//接收部门id
        $to_sys_store_id = !empty($condition['to_sys_store_id']) ? $condition['to_sys_store_id'] : [];//接收网点id
        $asset_operation_type = !empty($condition['asset_operation_type']) ? trim($condition['asset_operation_type']) : '';//转移来源
        $to_staff_id = !empty($condition['to_staff_id']) ? trim($condition['to_staff_id']) : '';//接收员工工号
        $status = !empty($condition['status']) ? $condition['status'] : explode(',', MaterialEnums::MY_ASSET_FROM_ME_STATUS_VALIDATE);//转移状态
        $asset_code = !empty($condition['asset_code']) ? $condition['asset_code'] : [];//资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? $condition['sn_code'] : [];//sn码
        $transfer_at_start = !empty($condition['transfer_at_start']) ? $condition['transfer_at_start'] : '';//转移日期-开始
        $transfer_at_end = !empty($condition['transfer_at_end']) ? $condition['transfer_at_end'] : '';//转移日期-结束
        $finished_at_start = !empty($condition['finished_at_start']) ? $condition['finished_at_start'] : '';//完成日期-开始
        $finished_at_end = !empty($condition['finished_at_end']) ? $condition['finished_at_end'] : '';//完成日期-结束
        $express_no = !empty($condition['express_no']) ? $condition['express_no'] : '';//快递单号
        $use = !empty($condition['use']) ? $condition['use'] : 0;//使用方向

        $locale = static::$language;
        $builder->where('log.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        //资产数据管控组-可看数据权限范围
        if (!empty($data_permission['node_department_id'])) {
            $builder->andWhere('log.from_node_department_id in ({node_department_ids:array}) OR log.to_node_department_id in ({node_department_ids:array})', ['node_department_ids' => $data_permission['node_department_id']]);
        }

        //转出人
        if (!empty($from_staff_id)) {
            $builder->andWhere('log.from_staff_id = :from_staff_id:', ['from_staff_id' => $from_staff_id]);
        }
        //转出公司
        if (!empty($from_company_id)) {
            $builder->andWhere('log.from_company_id = :from_company_id:', ['from_company_id' => $from_company_id]);
        }
        //转出部门
        if (!empty($from_node_department_id)) {
            $builder->andWhere('log.from_node_department_id = :from_node_department_id:', ['from_node_department_id' => $from_node_department_id]);
        }
        //转出网点
        if (!empty($from_sys_store_id)) {
            $from_sys_store_id = array_values($from_sys_store_id);
            $builder->inWhere('log.from_sys_store_id', $from_sys_store_id);
        }
        //转出协议公司
        if(!empty($condition['from_contract_company_id'])){
            $builder->inWhere('log.from_contract_company_id', $condition['from_contract_company_id']);
        }

        //接收协议公司
        if(!empty($condition['to_contract_company_id'])){
            $builder->inWhere('log.to_contract_company_id', $condition['to_contract_company_id']);
        }

        //接收人
        if (!empty($to_staff_id)) {
            $builder->andWhere('log.to_staff_id = :to_staff_id:', ['to_staff_id' => $to_staff_id]);
        }
        //接收公司
        if (!empty($to_company_id)) {
            $builder->andWhere('log.to_company_id = :to_company_id:', ['to_company_id' => $to_company_id]);
        }
        //接收部门
        if (!empty($to_node_department_id)) {
            $builder->andWhere('log.to_node_department_id = :to_node_department_id:', ['to_node_department_id' => $to_node_department_id]);
        }
        //接收网点
        if (!empty($to_sys_store_id)) {
            $to_sys_store_id = array_values($to_sys_store_id);
            $builder->inWhere('log.to_sys_store_id', $to_sys_store_id);
        }
        //转移来源
        // 1转移 = 1.资产部转移 2.员工互转 4.使用人信息自动刷新 6.离职资产转移 7.正式工转个人代理
        // 2领用 = 3.出库转移
        // 3批量退库 = 5.批量退库
        if (!empty($asset_operation_type)) {
            if ($asset_operation_type == self::$type_source_type_one) {
                $builder->inWhere('log.transfer_type', [MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT, MaterialEnums::TRANSFER_TYPE_USER, MaterialEnums::TRANSFER_TYPE_USER_SYNC, MaterialEnums::TRANSFER_TYPE_LEAVE_ASSET_AUTO, MaterialEnums::TRANSFER_TYPE_LEAVE_ASSET_AUTO_PERSONAL_AGENT]);
            } elseif ($asset_operation_type == self::$type_source_type_two) {
                $builder->andWhere('log.transfer_type = :transfer_type:', ['transfer_type' => MaterialEnums::TRANSFER_TYPE_OUT_STORAGE]);
            } elseif ($asset_operation_type == self::$type_source_type_three) {
                $builder->andWhere('log.transfer_type = :transfer_type:', ['transfer_type' => MaterialEnums::TRANSFER_TYPE_BATCH_STOCK_RETURN]);
            } elseif ($asset_operation_type == self::$type_source_type_four) {
                $builder->andWhere('log.transfer_type = :transfer_type:', ['transfer_type' => MaterialEnums::TRANSFER_TYPE_RETURN]);
            }
        }
        //使用方向
        if (!empty($use)) {
            $builder->andWhere('asset.use = :use:', ['use' => $use]);
        }
        //转移状态
        if (!empty($status)) {
            $builder->inWhere('log.status', $status);
        }
        //资产名称搜索
        if (!empty($name)) {
            $builder->andWhere('asset.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        //资产编码
        if (!empty($asset_code)) {
            $builder->inWhere('asset.asset_code', $asset_code);
            $builder->orWhere('asset.old_asset_code in ({old_asset_code:array})', ['old_asset_code' => $asset_code]);
        }
        //sn码搜索
        if (!empty($sn_code)) {
            $builder->inWhere('asset.sn_code', $sn_code);
        }
        //转移日期 查询范围,必须成对出现
        if (!empty($transfer_at_start) && !empty($transfer_at_end)) {
            $builder->andWhere(
                'log.transfer_at >= :transfer_at_start: and log.transfer_at <= :transfer_at_end:',
                ['transfer_at_start' => $transfer_at_start . ' 00:00:00', 'transfer_at_end' => $transfer_at_end . ' 23:59:59']
            );
        }
        //完成日期 查询范围,必须成对出现
        if (!empty($finished_at_start) && !empty($finished_at_end)) {
            $builder->andWhere(
                'log.finished_at >= :finished_at_start: and log.finished_at <= :finished_at_end:',
                ['finished_at_start' => $finished_at_start . ' 00:00:00', 'finished_at_end' => $finished_at_end . ' 23:59:59']);
        }
        //快递单号
        if (!empty($express_no)) {
            $builder->andWhere('log.express_no = :express_no:', ['express_no' => $express_no]);
        }

        return $builder;
    }

    /**
     * 数据查询-格式化
     * @param array $items 列表数据
     * @param bool $export 是否导出
     * @return array
     */
    private function handleTransferSearchListItems($items, bool $export)
    {
        if (empty($items)) {
            return [];
        }
        $locale = static::$language;
        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local');
        //绑定to_staff_name,from_staff_name,operator_name,operator_department
        $items = $this->setStaffName($items);
        if ($export == true) {
            //导出
            $row_value = [];
            foreach ($items as $item) {
                $row = [
                    isset(MaterialEnums::$transfer_type[$item['transfer_type']]) ? static::$t[MaterialEnums::$transfer_type[$item['transfer_type']]] : '',
                    $item[$name_key] ?? '',
                    $item['model'] ?? '',
                    $item['asset_code'] ?? '',
                    $item['sn_code'] ?? '',
                    $item['old_asset_code'] ?? '',
                    isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '',
                    $item['from_staff_id'] ?? '',
                    $item['from_staff_name'] ?? '',
                    $item['from_node_department_name'] ?? '',
                    $item['from_store_name'] ?? '',
                    $item['from_company_name'] ?? '',
                    $item['from_pc_code'] ?? '',
                    $item['transfer_at'] ?? '',
                    $item['to_staff_id'] ?? '',
                    $item['to_staff_name'] ?? '',
                    $item['to_node_department_name'] ?? '',
                    $item['to_store_name'] ?? '',
                    $item['to_company_name'] ?? '',
                    $item['to_pc_code'] ?? '',
                    $item['finished_at'] ?? '',
                    $item['transfer_remark'] ?? '',
                    $item['aor_no'] ?? '',
                ];
                if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
                    $row[] = $item['from_contract_company_name'] ?? '';//调出人 协议公司
                    $row[] = $item['to_contract_company_name'] ?? '';//接受人 协议公司
                }
                $row_value[] = $row;
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['name'] = $item[$name_key] ?? '';
                $item['status_text'] = isset(MaterialEnums::$transfer_log_status[$item['status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['status']]] : '';
                $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
                $item['transfer_type_text'] = isset(MaterialEnums::$transfer_type[$item['transfer_type']]) ? static::$t[MaterialEnums::$transfer_type[$item['transfer_type']]] : '';
                $item['finished_at'] = $item['finished_at'] ?? '';
                $item['transfer_at'] = $item['transfer_at'] ?? '';
            }
        }
        return $items;
    }

    /**
     * 数据查询-导出
     * @param $params
     * @param $user
     * @return array
     */
    public function exportSearch($params, $user)
    {
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            //验证参数
            $params = self::handleParams($params, self::$search_list_not_must_params);
            Validation::validate($params, self::$validate_search_list_search);
            $this->validateListOther($params);
            //开始查询
            $condition = $params;
            $condition['staff_id'] = $user['id'];
            $count = $this->getTransferSearchListCount($params, $user);
            if ($count > MaterialEnums::MATERIAL_ASSET_SEARCH_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max' => MaterialEnums::MATERIAL_ASSET_SEARCH_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $page_size = 2000;
            $step = ceil($count / $page_size);
            $row_values = [];
            for ($i = 1; $i <= $step; $i++) {
                $condition['pageNum'] = $i;
                $condition['pageSize'] = $page_size;
                $list = $this->getTransferSearchList($condition, $user, true, $count);
                $rows = $list['data']['items'] ?? [];
                $row_values = array_merge($row_values, $rows);
            }
            $row_values = array_map('array_values', $row_values);
            $file_name = 'material_asset_search_' . date('YmdHis');
            $header = [
                static::$t->_('material_asset_search_export.transfer_type'),//类型
                static::$t->_('material_asset_search_export.name'),//资产名称
                static::$t->_('material_asset_search_export.model'),//规格型号
                static::$t->_('material_asset_search_export.asset_code'),//资产编码
                static::$t->_('material_asset_search_export.sn_code'),//sn码
                static::$t->_('material_asset_search_export.old_asset_code'),//旧资产编码
                static::$t->_('material_asset_search_export.transfer_status'),//状态(转移状态)
                static::$t->_('material_asset_search_export.from_staff_id'),//转出人工号
                static::$t->_('material_asset_search_export.from_staff_name'),//转出人姓名
                static::$t->_('material_asset_search_export.from_department'),//转出部门
                static::$t->_('material_asset_search_export.from_store'),//转出网点
                static::$t->_('material_asset_search_export.from_company'),//转出公司
                static::$t->_('material_asset_search_export.from_pc_code'),//转出成本中心
                static::$t->_('material_asset_search_export.transfer_at'),//申请日期
                static::$t->_('material_asset_search_export.to_staff_id'),//转入人工号
                static::$t->_('material_asset_search_export.to_staff_name'),//转入人姓名
                static::$t->_('material_asset_search_export.to_department'),//转入部门
                static::$t->_('material_asset_search_export.to_store'),//转入网点
                static::$t->_('material_asset_search_export.to_company'),//转入公司
                static::$t->_('material_asset_search_export.to_pc_code'),//转入成本中心
                static::$t->_('material_asset_search_export.finished_at'),//完成日期
                static::$t->_('material_asset_search_export.transfer_remark'),//转移原因
                static::$t->_('material_asset_search_export.aor_no'),//单据号
            ];
            if(get_country_code() == 'MY'){
                $header[] =  static::$t->_('material_asset_search_export.from_contract_company_name');//调出人的协议签署公司
                $header[] = static::$t->_('material_asset_search_export.to_contract_company_name');//接收人的协议签署公司
            }
            $result = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-material-asset-search-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }

    /**
     * 给数组绑定转入人,转出人姓名,操作人姓名,操作人部门
     * @param array $data 二维数组,必须包含to_staff_id,from_staff_id,operator_id其中一种
     * @return array
     * @date 2022/11/7
     */
    public function setStaffName(array $data)
    {
        //从二维数组中提取所有staff_id
        //从二维数组中提取所有操作人operator_id
        $all_staff_id = [];
        $operator_ids = [];
        foreach ($data as $value) {
            if (isset($value['to_staff_id'])) {
                $all_staff_id[] = $value['to_staff_id'];
            }
            if (isset($value['from_staff_id'])) {
                $all_staff_id[] = $value['from_staff_id'];
            }
            if (isset($value['operator_id'])) {
                $operator_ids[] = $value['operator_id'];
                $all_staff_id[] = $value['operator_id'];
            }
            if (isset($value['transfer_operator_id'])) {
                $all_staff_id[] = $value['transfer_operator_id'];
            }
        }
        if (empty($all_staff_id)) {
            return $data;
        }
        //获取员工信息
        $all_staff_id = array_values(array_unique($all_staff_id));
        //查询员工信息
        $staff_data = HrStaffInfoModel::find([
            'columns' => 'staff_info_id, name, node_department_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind' => [
                'ids' => $all_staff_id,
            ]
        ])->toArray();
        $staff_data = array_column($staff_data, null, 'staff_info_id');
        //查询操作人部门
        $department_data = [];
        if (!empty($operator_ids)) {
            $operator_ids = array_values(array_unique($operator_ids));
            $operator_department_ids = [];
            foreach ($operator_ids as $one_operator) {
                if (isset($staff_data[$one_operator]['node_department_id'])) {
                    $operator_department_ids[] = $staff_data[$one_operator]['node_department_id'];
                }
            }
            if (!empty($operator_department_ids)) {
                $operator_department_ids = array_values(array_unique($operator_department_ids));
                $department_data = SysDepartmentModel::find([
                    'columns' => 'id, name',
                    'conditions' => 'id in ({ids:array})',
                    'bind' => [
                        'ids' => $operator_department_ids,
                    ]
                ])->toArray();
                $department_data = array_column($department_data, null, 'id');
            }
        }
        //拼接员工信息
        foreach ($data as &$one_data) {
            if (isset($one_data['from_staff_id'])) {
                $one_data['from_staff_name'] = $staff_data[$one_data['from_staff_id']]['name'] ?? '';
                $one_data['from_staff_name'] = $this->getViewName($one_data['from_staff_id'], false, $one_data['from_staff_name']);
            }
            if (isset($one_data['to_staff_id'])) {
                $one_data['to_staff_name'] = $staff_data[$one_data['to_staff_id']]['name'] ?? '';
                $one_data['to_staff_name'] = $this->getViewName($one_data['to_staff_id'], false, $one_data['to_staff_name']);
            }
            if (isset($one_data['transfer_operator_id'])) {
                $one_data['transfer_operator_name'] = $staff_data[$one_data['transfer_operator_id']]['name'] ?? '';
                $one_data['transfer_operator_name'] = $this->getViewName($one_data['transfer_operator_id'], false, $one_data['transfer_operator_name']);
            }
            if (isset($one_data['operator_id'])) {
                $one_data['operator_name'] = $staff_data[$one_data['operator_id']]['name'] ?? '';
                $one_data['operator_name'] = $this->getViewName($one_data['operator_id'], false, $one_data['operator_name']);

                $operator_department_id = $staff_data[$one_data['operator_id']]['node_department_id'] ?? '';
                $one_data['operator_department'] = $department_data[$operator_department_id]['name'] ?? '';
            }
        }
        return $data;
    }

    /**
     * 获取需要显示的姓名(工号)
     * @param int $staff_info_id 员工id
     * @param bool $get_by_db 是否从数据库查询员工姓名
     * @param string $staff_name 如果不从数据库查姓名,就以参数传来的员工姓名拼接
     * @return string
     * @date 2022/11/16
     */
    public function getViewName($staff_info_id, $get_by_db = false, $staff_name = '')
    {
        //参数指定从数据库查名称
        if ($get_by_db && !empty($staff_info_id)) {
            $staff_data = HrStaffInfoModel::findFirst([
                'columns' => 'staff_info_id, name',
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                ]
            ]);
            $staff_name = $staff_data->name ?? '';
        }
        //工号为空直接返回空
        if (empty($staff_info_id)) {
            $view_name = '';
        } else {
            $view_name = $staff_name . '(' . $staff_info_id . ')';
        }
        return $view_name;
    }

    /**
     * by站内信-通过资产id获取资产列表
     * @param array $params 参数
     * @param string $locale 语言
     * @param $user
     * @return array
     */
    public function getAssetsByIds($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            // 参数验证
            Validation::validate($params, self::$validate_get_asset_list_by_ids);
            // 查资产
            $ids = array_values(array_unique($params['asset_ids']));
            $asset_list = MaterialAssetsModel::find([
                'columns' => 'bar_code, name_en, name_zh, name_local, asset_code',
                'conditions' => 'id in ({ids:array})',
                'bind' => ['ids' => $ids]
            ])->toArray();
            $return_data = [];
            if (!empty($asset_list)) {
                foreach ($asset_list as $asset) {
                    $name_key = 'name_local';
                    if (isset($params['language']) && isset(MaterialClassifyEnums::$language_fields[$params['language']])) {
                        $name_key = 'name_' . (MaterialClassifyEnums::$language_fields[$params['language']]);
                    }
                    $return_data[] = [
                        'barcode' => $asset['bar_code'],
                        'name' => $asset[$name_key],
                        'asset_code' => $asset['asset_code'],
                    ];
                }

            }

            $data = $return_data;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-by-ids-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取资产转移-站内信-资产批量变更提醒[导入转移消息详情]/资产接收提醒[资产台账、资产转移]
     * @param array $params 参数组
     * @param string $lang 语种参数
     * @return array
     */
    public function getAssetTransferMsgInfo($params, $lang)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'transfer_operator_info' => [
                'staff_id' => '',
                'staff_name' => ''
            ],
            'barcode_list' => []
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.barcode, main.num, main.transfer_remark, sau.' . get_lang_field_name('name_', $lang) . ' as name, name_en');
            $builder->from(['main' => MaterialAssetTransferLogBarcodeSummaryModel::class]);
            $builder->leftJoin(MaterialSauModel::class, 'sau.barcode = main.barcode', 'sau');
            $builder->where('main.batch_id = :batch_id: and main.staff_info_id = :staff_info_id:', ['batch_id' => $params['batch_id'], 'staff_info_id' => $params['user_id']]);
            $data['barcode_list'] = $builder->getQuery()->execute()->toArray();
            foreach ($data['barcode_list'] as &$value) {
                $value['name'] = $value['name'] ? $value['name'] : $value['name_en'];
                unset($value['name_en']);
            }
            //资产接收提醒 - 员工互转或者资产部转移操作提醒 - 操作者信息
            if ($params['category'] && $params['category'] == MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER) {
                $transfer_operator_info = MaterialAssetTransferBatchModel::findFirst([
                    'conditions' => 'id = :batch_id: and is_deleted = :is_deleted:',
                    'bind' => ['batch_id' => $params['batch_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ]);
                if ($transfer_operator_info) {
                    $data['transfer_operator_info']['staff_id'] = $transfer_operator_info->staff_id;
                    $data['transfer_operator_info']['staff_name'] = $transfer_operator_info->staff_name;
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-transfer-msg-info-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }
}
