<?php

namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\HrStaffIdentityAnnexEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use Mpdf\Config\ConfigVariables;
use Mpdf\Mpdf;
use Mpdf\Config\FontVariables;

class AssetTransferExportService extends BaseService
{
    private static $instance;

    /**
     * 单例
     * @return AssetTransferExportService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 导出资产申请单/资产同意书
     * 参数验证
     * @var array
     */
    public static $export_validate = [
        'type' => 'Required|IntIn:' . MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_APPLY . ',' . MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_AGREE,//类型
        'asset_ids' => 'Arr|ArrLenGeLe:1,500',//本次接收资产id集合
        'asset_ids[*]' => 'IntGt:0',//转移记录id集合
    ];

    /**
     * 导出资产申请单/资产同意书
     * @param $params
     * @param $user
     * @return array
     * @date 2022/11/2
     */
    public function exportPdf($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $lang = self::$language;
            if (stripos($lang, 'zh') !== false) {
                $lang = 'ZH';
            } else if ($lang == 'TH' || $lang == 'th') {
                $lang = 'TH';
            } else {
                $lang = 'EN';
            }
            $type = $params['type'];
            $self_asset_ids = $params['self_asset_ids'] ?? [];//员工名下自有资产ID组
            $asset_ids = $params['asset_ids'] ?? [];//待接受的资产ID组
            // 查当前用户
            $data_tmp = [
                'date' => date('d-m-Y'),//日期(日-月-年)
                'name' => $user['name'],//姓名
                'identity' => $user['identity'],//身份证
                'job_title' => $user['job_title'],//职位
                'staff_info_id' => $user['id'],//工号
                'node_department_name' => '',//部门
                'sys_store_name' => '',//所属网点
                'img_url' => '',//签名照片
                'id_card_pic' => '',//身份证
                'items' => [],//资产清单
                'currency_symbol' => '',//币种
            ];

            $companyConfigInfo = $this->getCompanyConfigInfo($user['id']);
            $data_tmp['company_name'] = $companyConfigInfo['company_name'] ?? 'Flash Express Co., Ltd.';
            $data_tmp['company_name_local'] = $companyConfigInfo['company_name_local'] ?? 'Flash Express Co., Ltd.';
            $data_tmp['company_logo_url_base64'] = $companyConfigInfo['company_logo_url_base64'] ?? '';
            $data_tmp['company_address'] = $companyConfigInfo['company_address'] ?? '';

            // 查询当前用户名下特定状态的资产清单
            $staff_assets_list = AssetAccountService::getInstance()->getStaffAssets($user['id'], ['asset_ids' => $self_asset_ids, 'status' => explode(',', MaterialEnums::MY_ASSET_STATUS_VALIDATE), 'limit' => MaterialEnums::MATERIAL_EXPORT_PDF_MAX]);
            // 限制总数不能大于1000
            $asset_ids = !empty($asset_ids) ? array_values(array_unique($asset_ids)) : [];
            $staff_assets_count = count($staff_assets_list);
            $asset_ids_count = count($asset_ids);
            if (($staff_assets_count + $asset_ids_count) > MaterialEnums::MATERIAL_EXPORT_PDF_MAX) {
                throw new ValidationException(static::$t->_('export_pdf_exceed_maximum_limit'), ErrCode::$VALIDATE_ERROR);
            }
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 员工名下资产共:' . $staff_assets_count . '条; 传来的资产id共:' . $asset_ids_count . '条');
            // 查询本次要接收的资产
            $reception_assets_info = [];
            if (!empty($asset_ids)) {
                $reception_assets_info = MaterialAssetsModel::find([
                    'columns' => 'name_zh,name_en,name_local,asset_code,sn_code,currency,purchase_price,net_value,receipted_at',
                    'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                    'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ])->toArray();
            }
            // 全部资产
            $all_asset = array_merge($staff_assets_list, $reception_assets_info);
            foreach ($all_asset as $k => $one_asset) {
                //币种转换成当前系统默认币种
                $sys_currency_price = 0.00;
                if ($one_asset['purchase_price'] > 0) {
                    $sys_currency_price = EnumsService::getInstance()->currencyAmountConversion($one_asset['currency'], $one_asset['purchase_price'], 2);
                }
                //扣款金额 = 资产净值 && 同意书才需要
                $deduction_price = 0;
                if ($one_asset['net_value'] > 0 && $type == MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_AGREE) {
                    $deduction_price = EnumsService::getInstance()->currencyAmountConversion($one_asset['currency'], $one_asset['net_value'], 2);
                }

                $data_tmp['items'][] = [
                    'serial_number' => $k + 1,//序号
                    'name_zh' => $one_asset['name_zh'],//资产名称
                    'name_en' => $one_asset['name_en'],
                    'name_local' => $one_asset['name_local'],
                    'receipted_at' => date('Y-m-d', strtotime($one_asset['receipted_at'] ?? $user['hire_date'])),//申请日期(最新领用日期为空则获取员工入职日期)
                    'sn_code' => $one_asset['sn_code'] ?? '',//sn码
                    'asset_code' => $one_asset['asset_code'] ?? '',//资产码
                    'nums' => 1,//数量
                    'price' => $sys_currency_price,//价格
                    'deduction_price' => $deduction_price//扣款金额
                ];
            }

            // 查询用户部门
            if (!empty($user['node_department_id'])) {
                $node_department_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
                $data_tmp['node_department_name'] = $node_department_info['name'] ?? '';
            }
            // 查询用户网点
            if (!empty($user['sys_store_id'])) {
                if ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                    $data_tmp['sys_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
                } else {
                    $store_info = (new StoreRepository())->getStoreDetail($user['sys_store_id'], 0);
                    $data_tmp['sys_store_name'] = $store_info['name'] ?? '';
                }
            }

            // 查询签名照片
            $data_tmp['img_url'] = AssetTransferService::getInstance()->getSignUrl($user['id']);

            //读取员工身份证附件表，获取员工身份证正面字段以及审核状态为已通过的身份证照片
            $staff_identity_info = (new HrStaffRepository())->getStaffIdentity($user['id']);
            $data_tmp['id_card_pic'] = ($staff_identity_info && $staff_identity_info['annex_path_front']) ? $staff_identity_info['annex_path_front'] . '?x-oss-process=image/quality,q_70' : '';
            // 当前国家币种
            $sys_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            $data_tmp['currency_symbol'] = $sys_currency['symbol'];
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 查询数据完毕,准备生成pdf');
            $view = new \Phalcon\Mvc\View();
            $path = APP_PATH . '/views';
            $view->setViewsDir($path);
            $view->setVars($data_tmp);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            $staff_info_id = md5($user['id']);
            $protocol_file = ($type == MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_APPLY ? 'shenqingzichandan' : 'zichanguanlitongyishu') . '_' . $lang . '5';
            $view->render('asset', $protocol_file);
            $file_name = "staff_apply_type{$type}_{$lang}_{$staff_info_id}.pdf";

            $view->finish();
            $content = $view->getContent();
            $file_path = sys_get_temp_dir() . '/';

            //mpdf设置字体文档：https://mpdf.github.io/fonts-languages/fonts-in-mpdf-7-x.html
            $defaultConfig = (new ConfigVariables())->getDefaults();
            $fontDirs = $defaultConfig['fontDir'];

            $defaultFontConfig = (new FontVariables())->getDefaults();
            $fontData = $defaultFontConfig['fontdata'];

            if ($lang == 'ZH' || $lang == 'EN') {
                $config = [
                    'fontDir' => array_merge($fontDirs, [
                        BASE_PATH . '/public/fonts',
                    ]),
                    //设置指定要使用的字体
                    'fontdata' => $fontData,
                    'format' => 'A4',
                    'mode' => 'zh-CN',
                    'margin_left' => 10,
                    'margin_right' => 10,
                    'margin_top' => 47,
                    'margin_bottom' => 67,
                    'margin_header' => 10,
                    'margin_footer' => 10,
                ];
            } else {
                //实例化mpdf 扩展字体 由于泰文乱码
                $config = array(
                    'fontDir' => array_merge($fontDirs, [
                        BASE_PATH . '/public/fonts',
                    ]),
                    'fontdata' => $fontData + [
                            'th' => [
                                'R' => 'THSarabunNew.ttf',
                                'B' => 'THSarabunNew Bold.ttf',
                                'I' => 'THSarabunNew Italic.ttf',
                            ],

                        ],
                    'mode' => 'th',
                    'format' => 'A4',
                    'margin_left' => 10,
                    'margin_right' => 10,
                    'margin_top' => 47,
                    'margin_bottom' => 47,
                    'margin_header' => 10,
                    'margin_footer' => 10,
                );
            }

            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 准备生成Mpdf');

            $mpdf = new Mpdf($config);
            //设置字体所在目录
            //                $mpdf->AddFontDirectory(BASE_PATH.'/public/fonts/');
            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->defaultheaderline = 0;
            $mpdf->defaultfooterline = 0;
            $get_header_footer = $this->getPdfHeaderAndFooter($protocol_file, $data_tmp);
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 准备SetHTMLHeader');
            $mpdf->SetHTMLHeader($get_header_footer['header']);
            $mpdf->SetHTMLFooter($get_header_footer['footer']);
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 准备WriteHTML');
            $mpdf->WriteHTML($content);
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 准备output');
            $mpdf->Output($file_path . $file_name, "f");
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf 准备上传文件到oss');
            $upload_res = OssHelper::uploadFile($file_path . $file_name);
            $data = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';
            $this->logger->info('asset_transfer_get_asset_pdf-getAssetsPdf return_data:' . json_encode($data, JSON_UNESCAPED_UNICODE));
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-transfer-export-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];

    }

    /**
     * 获取 公司配置信息
     * @param $staffInfoId
     * @return array
     * @throws ValidationException
     */
    public function getCompanyConfigInfo($staffInfoId)
    {
        //获取管辖范围
        $rpc        = new ApiClient('hcm_rpc', '', 'get_company_config_info');
        $api_params = [
            [
                'staff_info_id' => $staffInfoId,
            ],
        ];
        $rpc->setParams($api_params);
        $data = $rpc->execute();

        if(!isset($data['result']['code']) || $data['result']['code'] != ErrCode::$SUCCESS){
            throw new ValidationException(static::$t->_('accident_report_error_9'));
        }
        return $data['result']['data'] ?? [];
    }

    /**
     * 导出拒绝接收的excel
     * @param $filename
     * @param array $export_data
     * @return array|bool
     * @throws \App\Library\Exception\BusinessException
     * @date 2022/11/5
     */
    public function exportRejectData($filename, $export_data = [])
    {
        $header_map = [
            static::$t->_('material_transfer_reject_excel.type'), //类型 出库转移或员工互转
            static::$t->_('material_transfer_reject_excel.operator'), //操作人
            static::$t->_('material_transfer_reject_excel.operation_department'), //操作部门
            static::$t->_('material_transfer_reject_excel.from_staff'), //转出人
            static::$t->_('material_transfer_reject_excel.from_department'), //转出部门
            static::$t->_('material_transfer_reject_excel.from_store'), //转出网点
            static::$t->_('material_transfer_reject_excel.barcode'), //barcode
            static::$t->_('material_transfer_reject_excel.asset_code'), //资产编码
            static::$t->_('material_transfer_reject_excel.old_asset_code'), //旧资产编码
            static::$t->_('material_transfer_reject_excel.asset_name'), //资产名称
            static::$t->_('material_transfer_reject_excel.sn_code'), //sn码
            static::$t->_('material_transfer_reject_excel.reasons'), //转移原因
            static::$t->_('material_transfer_reject_excel.transfer_at'), //转移时间
            static::$t->_('material_transfer_reject_excel.to_staff'),  //接收人
            static::$t->_('material_transfer_reject_excel.to_department'), //接收部门
            static::$t->_('material_transfer_reject_excel.to_store'), //接收网点
            static::$t->_('material_transfer_reject_excel.remark'), //拒绝原因
            static::$t->_('material_transfer_reject_excel.finished_at'), //拒绝时间
        ];
        // 查操作人名称,部门
        $export_data = AssetTransferListService::getInstance()->setStaffName($export_data);
        $filter_data = [];
        $header = array_values($header_map);
        foreach ($export_data as $key => $item) {
            $filter_data[] = [
                isset(MaterialEnums::$transfer_type[$item['transfer_type']]) ? static::$t[MaterialEnums::$transfer_type[$item['transfer_type']]] : '',
                $item['operator_name'] ?? '',
                $item['operator_department'] ?? '',
                $item['from_staff_name'] ?? '',
                $item['from_node_department_name'] ?? '',
                $item['from_store_name'] ?? '',
                $item['bar_code'] ?? '',
                $item['asset_code'] ?? '',
                $item['old_asset_code'] ?? '',
                $item['name_zh'] ?? '',
                $item['sn_code'] ?? '',
                $item['transfer_remark'] ?? '',
                $item['transfer_at'] ?? '',
                $item['to_staff_name'] ?? '',
                $item['to_node_department_name'] ?? '',
                $item['to_store_name'] ?? '',
                $item['remark'] ?? '',
                $item['finished_at'] ?? '',
            ];
        }
        $result = $this->exportExcel($header, $filter_data, $filename);
        if ($result['code'] != ErrCode::$SUCCESS) {
            return false;
        }
        return $result;
    }

    /**
     * 生成pdf的header和footer的html
     * @param string $protocol_file 协议书前缀
     * @param array $data 协议内容变量替换组
     * @return array
     * @date 2022/11/5
     */
    public function getPdfHeaderAndFooter($protocol_file, $data)
    {
        //获取头部
        $view = new \Phalcon\Mvc\View();
        $path = APP_PATH . '/views';
        $view->setViewsDir($path);
        $view->setVars($data);

        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );

        $view->render('asset', $protocol_file . '_header');
        $view->finish();
        $data['header'] = $view->getContent();

        //获取底部
        $view = new \Phalcon\Mvc\View();
        $path = APP_PATH . '/views';
        $view->setViewsDir($path);
        $view->setVars($data);

        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );

        $view->render('asset', $protocol_file . '_footer');
        $view->finish();
        $data['footer'] = $view->getContent();

        return $data;
    }
}
