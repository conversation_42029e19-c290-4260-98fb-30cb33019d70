<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/17
 * Time: 10:24
 */
namespace App\Modules\Material\Services;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\Enums\ScmEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysStoreModel;
use App\Models\bi\SysCityModel;
use App\Models\bi\SysDistrictModel;
use App\Models\bi\SysProvinceModel;
use App\Models\fle\StorePickupVirtualRelationModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialSyncScmLogModel;
use App\Modules\Purchase\Models\ScmCargoOwnerModel;
use  App\Modules\Purchase\Services\ScmService as BaseScmService;
use App\Modules\Purchase\Services\BaseService as PurchaseBaseService;
use App\Modules\User\Services\UserService;

class ScmService extends BaseService
{
    //scm连接配置
    public static $scm_config = [
        'goods_unit'              => 'open/goodsUnitList',           //获取标准型号属性
//        'add_ent' => 'goods/addEntCategory',//添加分类
//        'edit_ent' => 'goods/editEntCategory',//编辑分类
//        'search_ent' => 'goods/searchEntCategory',//查询分类是否存在
//        'list' => 'goods/entCategoryList',
//        'delete' => 'goods/deleteEntCategory',
        'add_goods'               => 'open/add_goods',               //添加标准型号
        'goods_status'            => 'open/goodsChangeStatus',       //启用和停用
        'is_goods'                => 'open/goods',                   //标准型号查询//barCode
        'edit_goods'              => 'open/edit_goods',              //编辑标准型号
        'returnWarehouseAdd'      => 'open/returnWarehouseAdd',      //出库单添加
        'cancelOutbound'          => 'open/cancelOutbound',          //取消出库
        'goodsStock'              => 'open/goodsStockWithErrBarcode',//查询所选货主的仓库下该Barcode的可售库存
        'outbound'                => 'Audit/outbound',               //出库单审核
        'getOutboundTrackingInfo' => 'open/getOutboundTrackingInfo', //出库单物流信息查询
        'getOutboundOrderStatus'  => 'open/getOutboundOrderStatus',  //出库单处理进程信息
        'getWarehouseList'        => 'open/getWarehouseList',        //可用仓库信息
        'createStorage'           => 'arrival_notice/create',        //入库通知单新增
        'cancelStorage'           => 'arrival_notice/rollback',      //入库通知单撤销
        'deleteStorage'           => 'arrival_notice/delete',        //入库通知单删除
        'inbound'                 => 'Audit/inbound',                //入库通知单审核
        'outBoundOrderDetail'     => 'return_warehouse/outBoundOrderDetail',//出库单详情(new) API
    ];

    const TYPE_SYNC_SCM = 3;//同步物料到scm
    const IS_DEFAULT = 1;//是否是默认货主0否，1是
    const STOCK_GET_WAREHOUSE_LIST = 'key_stock_get_warehouse_list';//所有仓库的缓存key
    //配送方式
    const DELIVERY_WAY_EXPRESS_NAME = 'express';
    const DELIVERY_WAY_SELF_NAME = 'self';
    public static $delivery_way_list = [
        MaterialClassifyEnums::DELIVERY_WAY_EXPRESS => self::DELIVERY_WAY_EXPRESS_NAME,
        MaterialClassifyEnums::DELIVERY_WAY_SELF => self::DELIVERY_WAY_SELF_NAME,
    ];

    /**
     * 获取资产这边默认货主
     * @return mixed
     */
    public function getDefaultScmCargoOwner()
    {
        return ScmCargoOwnerModel::findFirst([
            'conditions' => 'type = :type: and is_default=:is_default:',
            'bind' => ['type' => self::TYPE_SYNC_SCM, 'is_default' => self::IS_DEFAULT],
            'columns' => 'mach_code'
        ]);
    }

    /**
     * 获取SCM商品属性（单位）
     * @return array
     */
    public function getGoodsUnit()
    {
        $listAttribute = [];
        $scm_info = $this->getDefaultScmCargoOwner();
        if (empty($scm_info)) {
            return $listAttribute;
        }
        $scm = new BaseScmService($scm_info->mach_code, self::TYPE_SYNC_SCM);
        $post_str = $scm->buildRequestParam([]);
        $data = $scm->newPostRequest(self::$scm_config['goods_unit'], $post_str);
        if ($data['code'] == ErrCode::$SUCCESS && $data['data']) {
            $lang = strtolower(static::$language == 'zh-CN' ? 'zh' : static::$language);
            $lang = $lang == 'zh' ? 'zh' : 'en';
            $value_map = arrayColumns($data['data'], 'id,zh,en');
            foreach ($value_map as $key=>$value) {
                $value_['id'] = $key+1;
                $value_['name'] = $value[$lang];
                $value_['name_zh'] = $value['zh'];
                $value_['name_en'] = $value['en'];
                $listAttribute[] = $value_;
            }
        }
        return $listAttribute;
    }

    /**
     * 记录同步物料信息到SCM记录，用作脚本同步
     * @param integer $id 数据源ID
     * @param integer $sync_type 同步类型，0添加，1编辑，2删除，3变更状态
     * @param int $source_type 同步的数据类型，'0标准型号，1财务分类；默认同步标准型号
     * @return bool
     */
    public function saveMaterialSyncScmLog($id, $sync_type = MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD, $source_type = MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU)
    {
        if (!$id) {
            return false;
        }
        $scm_list = ScmCargoOwnerModel::find([
            'conditions' => 'type = :type:',
            'bind' => ['type' => self::TYPE_SYNC_SCM],
            'columns' => 'id'
        ])->toArray();
        if ($scm_list) {
            $log = [];
            $sync_scm_log = new MaterialSyncScmLogModel();
            $time = date('Y-m-d H:i:s', time());
            if ($sync_type == MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD) {
                //新增直接记录操作记录
                foreach ($scm_list as $item) {
                    $log[] = [
                        'sync_id' => $id,
                        'scm_cargo_id' => $item['id'],
                        'source_type' => $source_type,
                        'sync_type' => $sync_type,
                        'created_at' => $time,
                        'updated_at' => $time
                    ];
                }
            } else {
                //非新增记录，需要先看看是否还有未同步成功至SCM相关操作记录(包括新增记录)，如果存在，则继续同步即可，如果不存在则直接插入新的记录
                $has_not_sync_scm_list = MaterialSyncScmLogModel::find([
                   'columns' => 'scm_cargo_id',
                    'conditions' => 'sync_id = :sync_id: and sync_type in({sync_type:array}) and source_type=:source_type: and is_deleted = :is_deleted: and status != :status: and fail_num < :fail_num:',
                    'bind' => [
                        'sync_id' => $id,
                        'sync_type' => [MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD, $sync_type],
                        'source_type' => $source_type,
                        'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                        'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS,
                        'fail_num' => 3
                    ]
                ])->toArray();
                if ($has_not_sync_scm_list) {
                    //存在未同步到scm的记录信息
                    $scm_cargo_id = array_column($has_not_sync_scm_list, "scm_cargo_id");
                    foreach ($scm_list as $item) {
                        if(in_array($item['id'], $scm_cargo_id)) {
                            continue;
                        } else {
                            $log[] = [
                                'sync_id' => $id,
                                'scm_cargo_id' => $item['id'],
                                'source_type' => $source_type,
                                'sync_type' => $sync_type,
                                'created_at' => $time,
                                'updated_at' => $time
                            ];
                        }
                    }
                } else {
                    //不存在还未同步到SCM的数据列表，则直接插入新纪录
                    foreach ($scm_list as $item) {
                        $log[] = [
                            'sync_id' => $id,
                            'scm_cargo_id' => $item['id'],
                            'source_type' => $source_type,
                            'sync_type' => $sync_type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ];
                    }
                }
            }
            //存在需要记录的同步记录且未记录成功则返回false
            if ($log && !$sync_scm_log->batch_insert($log)) {
                return false;
            }
            return true;
        } else {
            return true;
        }
    }

    /**
     * 记录同步物料信息到SCM记录，用作脚本同步 批量方法
     * @param array $id_collect 数据源ID集合
     * @param integer $sync_type 同步类型，0添加，1编辑
     * @param string $datetime
     * @return bool
     */
    public function saveMaterialSyncScmLogBatch($id_collect, $sync_type = MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD, $datetime='')
    {
        if (empty($id_collect)) {
            return false;
        }
        $scm_list = ScmCargoOwnerModel::find([
            'conditions' => 'type = :type:',
            'bind' => ['type' => self::TYPE_SYNC_SCM],
            'columns' => 'id'
        ])->toArray();
        $sync_scm_log = new MaterialSyncScmLogModel();
        $time = $datetime != '' ? $datetime : date('Y-m-d H:i:s', time());
        if (!$scm_list){
            return true;
        }
        $log = [];
        if ($sync_type == MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD) {
            foreach ($id_collect as $id) {
                //新增直接记录操作记录
                foreach ($scm_list as $item) {
                    $log[] = [
                        'sync_id' => $id,
                        'scm_cargo_id' => $item['id'],
                        'source_type' => MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU,
                        'sync_type' => $sync_type,
                        'created_at' => $time,
                        'updated_at' => $time
                    ];
                }
            }
        }else {
            //非新增记录，需要先看看是否还有未同步成功至SCM相关操作记录(包括新增记录)，如果存在，则继续同步即可，如果不存在则直接插入新的记录
            $has_not_sync_scm_list = MaterialSyncScmLogModel::find([
                'columns' => 'scm_cargo_id,sync_id',
                'conditions' => 'sync_id in ({sync_ids:array}) and sync_type in({sync_type:array}) and source_type=:source_type: and is_deleted = :is_deleted: and status != :status:',
                'bind' => ['sync_ids'=>$id_collect, 'sync_type'=> [MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD, MaterialClassifyEnums::TO_SCM_SYNC_TYPE_UPDATE], 'source_type'=>MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'status'=>MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS]
            ])->toArray();
            //筛选需要同步的(存在待同步,同步失败(失败会重试)的id,就不用再插入了)
            //构造$cargo_id_sync_id_kv key=标准编号id, value=货主id(数组)
            $cargo_id_sync_id_kv = [];
            foreach ($has_not_sync_scm_list as $has_key=>$has_value){
                $cargo_id_sync_id_kv[$has_value['sync_id']][] = $has_value['scm_cargo_id'];
            }
            $log = [];
            foreach ($id_collect as $id){
                //存在标准编号id,去判断每个货主有没有
                if (key_exists($id,$cargo_id_sync_id_kv)){
                    foreach ($scm_list as $item){
                        if (!in_array($item['id'],$cargo_id_sync_id_kv[$id])){
                            $log[] = [
                                'sync_id' => $id,
                                'scm_cargo_id' => $item['id'],
                                'source_type' => MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU,
                                'sync_type' => $sync_type,
                                'created_at' => $time,
                                'updated_at' => $time
                            ];
                        }
                    }
                }else{
                    //不存在标准编号id,直接生成数据
                    foreach ($scm_list as $item){
                        $log[] = [
                            'sync_id' => $id,
                            'scm_cargo_id' => $item['id'],
                            'source_type' => MaterialClassifyEnums::TO_SCM_SOURCE_TYPE_SAU,
                            'sync_type' => $sync_type,
                            'created_at' => $time,
                            'updated_at' => $time
                        ];
                    }
                }
            }
        }

        //存在需要记录的同步记录且未记录成功则返回false
        if ($log && !$sync_scm_log->batch_insert($log)) {
            return false;
        }
        return true;
    }
    /**
     * 添加/编辑商品 格式化数据到scm
     * @param array $data 标准型号SAU信息组
     * @param integer $type 操作类型1添加，2编辑
     * @Date: 2021-10-22 14:03
     * @return:
     **@author: peak pan
     */
    private function sendToScmData($data, $type = MaterialClassifyEnums::OPERATE_TYPE_ADD)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $items['barCode'] = $data['barcode'];//barcode
        $items['name'] = $data['name_zh'].$data['name_en'].$data['name_local'];//传中文名称和英文名称组合。
        //13846 拼接了本地语言名称,并截取255长度
        $items['name'] = mb_substr($items['name'],0,255,'UTF-8');
        $finance_category = MaterialFinanceCategoryModel::findFirstById($data['finance_category_id']);
        /**
         * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1 需求
         * 如barcode身上的财务分类如果是2级，或者2级以下，都传输2级编码过去
         */
        if (!empty($finance_category)) {
            if ($finance_category->level == 2) {
                //如果所选财务分类就是2级分类，则传递改分类的编码
                $items['entCategoryCode'] = $finance_category->code??'';// 企业类目传barcode上所属的财务分类的2级分类
            } else if ($finance_category->level > 2) {
                //非2级分类，则需要获取到2级分类编码传递过去
                $second_level = MaterialFinanceCategoryModel::findFirst([
                    'columns' => 'code',
                    'conditions' => 'id in ({ids:array}) and level = :level:',
                    'bind' =>['ids'=>explode(",",$finance_category->ids), 'level' => 2]
                ]);
                $items['entCategoryCode'] = $second_level->code??'';// 企业类目传barcode上所属的财务分类的2级分类
            }
        }
        $items['channelSource'] = 'FlashOA';//商品来源
        $items['specification'] = $data['model'];//传规格型号
        if ($type == MaterialClassifyEnums::OPERATE_TYPE_ADD) {
            $items['encodeType'] = ($data['enable_sn'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? "1" : "0";//是否开启sn，是则传1；否则传0
            $items['isAsset'] = ($data['enable_asset_code'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? "Y" : "N";//是否启用资产码，Y是；N否
        }
        $items['remark'] = $data['remark'] ?? '';//传备注
        //系数配置
        $coefficient_config = MaterialClassifyEnums::$reduced_unit;
        $items['length'] = bcmul($data['length'], $coefficient_config[$data['length_unit']]);//传长度的值,。如果长度的单位和mm不一致，则换算值需要进行单位换算
        $items['width']  = bcmul($data['width'], $coefficient_config[$data['width_unit']]);//传宽度的值。如果宽度的单位和mm不一致，则换算值需要进行单位换算
        $items['height'] = bcmul($data['height'], $coefficient_config[$data['height_unit']]);//传高度的值。如果高度的单位和mm不一致，则换算值需要进行单位换算
        $items['weight'] = bcmul($data['weight'], $coefficient_config[$data['weight_unit']]);//传重量的值。如果重量的单位和g不一致，则换算值需要进行单位换算
        $materialAttachment = new  MaterialAttachmentModel();
        $first_attachment = $materialAttachment->getFirst([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key=:oss_bucket_key: and deleted = :deleted:',
            'columns'    => 'object_key',
            'bind'       => ['oss_bucket_type' => 1, 'oss_bucket_key'=>$data['id'], 'deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        $items['image'] = !empty($first_attachment) ? gen_file_url($first_attachment) : '';//传商品的URL，传第1张
        //$items['costPrice'] = bcmul($data['price'], 100);//传参考单价*100传输；scm还没支持到超过14位以上的金额需要迭代研发
        $items['oneUnit'] = $data['unit_en'];//传基本单位，英文名称 一级单位
        $items['twoUnit'] = $data['small_bag_unit_en'];//传小包装单位，英文名称 二级单位
        $items['twoConversion'] = $data['small_bag_val'];//传小包装单位的值
        $items['threeUnit'] = $data['big_bag_unit_en'];//传大包装单位，英文名称 三级单位
        $items['threeConversion'] = $data['big_bag_val'];//传大包装单位的值
        return $items;
    }

    /**
     * 同步新增标准型号到SCM商品
     * @param string $mach_code 货主mchid
     * @param array $log 同步至SCM log记录表信息
     * @param array $sau 标准型号SAU信息组
     * @return array
     */
    public function syncAddGoods($mach_code, $log, $sau)
    {
        $db = $this->getDI()->get('db_oa');
        $scm = new BaseScmService($mach_code, self::TYPE_SYNC_SCM);
        $post_str = $scm->buildRequestParam($this->sendToScmData($sau));
        $goods_data = $scm->newPostRequest(self::$scm_config['add_goods'], $post_str);
        if ($goods_data['code'] == ErrCode::$SUCCESS) {
            //同步成功
            $db->updateAsDict(
                (new MaterialSyncScmLogModel())->getSource(),
                [
                    'scm_code' => ErrCode::$SUCCESS,
                    'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'conditions'=>'id=?',
                    'bind' => [$log['id']]
                ]
            );
            return [true, $goods_data['message']];
        } else {
            //同步失败;1309=>商品条形码已存在错误码;12197=>不能开启ASSET
            if (isset($goods_data['code']) && in_array($goods_data['code'], [1309, 12197])) {
                //直接修改为已同步
                $status = MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS;
                $fail_reason =  $goods_data['message'].'(只不过更新scm状态为成功)';
                $result = true;
            } else {
                $status = MaterialClassifyEnums::TO_SCM_SYNC_STATUS_FAIL;
                $fail_reason = $goods_data['message'];
                $result = false;
            }
            $db->updateAsDict(
                (new MaterialSyncScmLogModel())->getSource(),
                [
                    'status' => $status,
                    'scm_code' => $goods_data['code'] ?? 0,
                    'fail_num' => $log['fail_num']+1,
                    'fail_reason' => $fail_reason,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'conditions'=>'id=?',
                    'bind' => [$log['id']]
                ]
            );
            return [$result, $fail_reason];
        }
    }

    /**
     * 同步更新标准型号到SCM商品
     * @param string $mach_code 货主mchid
     * @param array $log 同步至SCM log记录表信息
     * @param array $sau 标准型号SAU信息组
     * @return array
     */
    public function syncEditGoods($mach_code, $log, $sau)
    {
        $scm = new BaseScmService($mach_code, self::TYPE_SYNC_SCM);
        $is_goods_con = $scm->buildRequestParam(['barCode'=>$sau['barcode']]);
        $id_goods_data = $scm->newPostRequest(self::$scm_config['is_goods'], $is_goods_con);
        $db = $this->getDI()->get('db_oa');
        //针对访问频繁兼容下逻辑[1378	访问过于频繁，请稍后再试]
        if ($id_goods_data['code'] == 1378) {
            //同步失败
            $db->updateAsDict(
                (new MaterialSyncScmLogModel())->getSource(),
                [
                    'scm_code' => $id_goods_data['code'] ?? 0,
                    'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_FAIL,
                    'fail_num' => $log['fail_num']+1,
                    'fail_reason' => $id_goods_data['message'],
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'conditions'=>'id=?',
                    'bind' => [$log['id']]
                ]
            );
            return [false, $id_goods_data['message']];
        }
        if (!empty($id_goods_data['data'])) {
            //在scm存在则更新
            $post_str = $scm->buildRequestParam($this->sendToScmData($sau, MaterialClassifyEnums::OPERATE_TYPE_UPDATE));
            $goods_data = $scm->newPostRequest(self::$scm_config['edit_goods'], $post_str);
            if ($goods_data['code'] == ErrCode::$SUCCESS) {
                //同步成功
                $db->updateAsDict(
                    (new MaterialSyncScmLogModel())->getSource(),
                    [
                        'scm_code' => ErrCode::$SUCCESS,
                        'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS,
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'conditions'=>'id=?',
                        'bind' => [$log['id']]
                    ]
                );
                return [true, $goods_data['message']];
            } else {
                //同步失败
                $db->updateAsDict(
                    (new MaterialSyncScmLogModel())->getSource(),
                    [
                        'scm_code' => $goods_data['code'] ?? 0,
                        'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_FAIL,
                        'fail_num' => $log['fail_num']+1,
                        'fail_reason' => $goods_data['message'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    [
                        'conditions'=>'id=?',
                        'bind' => [$log['id']]
                    ]
                );
                return [false, $goods_data['message']];
            }
        } else {
            //在scm不存在则添加
            return $this->syncAddGoods($mach_code, $log, $sau);
        }
    }

    /**
     * 同步标准型号状态到SCM商品状态变更
     * @param string $mach_code 货主mchid
     * @param array $log 同步至SCM log记录表信息
     * @param array $sau 标准型号SAU信息组
     * @return array
     */
    public function syncGoodsChangeStatus($mach_code, $log, $sau)
    {
        $db = $this->getDI()->get('db_oa');
        $scm_data['barCode'] = $sau['barcode'];
        $scm_data['status'] = ($sau['status'] == MaterialClassifyEnums:: MATERIAL_START_USING) ? 3 : 1;//OA端1启用，2禁用；SCM端3启用，1停用
        $scm = new BaseScmService($mach_code, self::TYPE_SYNC_SCM);
        $post_str = $scm->buildRequestParam($scm_data);
        $goods_data = $scm->newPostRequest(self::$scm_config['goods_status'], $post_str);
        if ($goods_data['code'] == ErrCode::$SUCCESS) {
            //同步成功
            $db->updateAsDict(
                (new MaterialSyncScmLogModel())->getSource(),
                [
                    'scm_code' => ErrCode::$SUCCESS,
                    'status' => MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'conditions'=>'id=?',
                    'bind' => [$log['id']]
                ]
            );
            return [true, $goods_data['message']];
        } else {
            //同步失败
            $status = MaterialClassifyEnums::TO_SCM_SYNC_STATUS_FAIL;
            //100446=>商品有库存不可停用;12197=>不能开启ASSET;直接成功
            if (isset($goods_data['code']) && in_array($goods_data['code'], [100446, 12197])) {
                $status = MaterialClassifyEnums::TO_SCM_SYNC_STATUS_SUCCESS;
            }
            $db->updateAsDict(
                (new MaterialSyncScmLogModel())->getSource(),
                [
                    'status' => $status,
                    'scm_code' => $goods_data['code'] ?? 0,
                    'fail_num' => $log['fail_num']+1,
                    'fail_reason' => $goods_data['message'],
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'conditions'=>'id=?',
                    'bind' => [$log['id']],
                ]
            );
            return [false, $goods_data['message']];
        }
    }


    /**
     * 如果是虚拟网点，自动替换成真实网点信息然后传给scm
     * @param $sys_store_id
     * @param $data
     * @return array
     */
    public function autoChangeRealStoreInfo($sys_store_id,$data): array
    {
        if (get_country_code() != GlobalEnums::TH_COUNTRY_CODE || $sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
            return $data;
        }
        //获取虚拟网点对应的真实网点
        $virtualRelationInfo = StorePickupVirtualRelationModel::findFirst([
            'columns'    => 'store_id',
            'conditions' => 'pickup_virtual_store_id = :pickup_virtual_store_id: and  deleted =  0',
            'bind'       => ['pickup_virtual_store_id' => $sys_store_id],
        ]);
        if (empty($virtualRelationInfo)) {
            return $data;
        }
        $realStoreId = (string)$virtualRelationInfo->store_id;

        //获取两个网点的信息
        $storeList = SysStoreModel::find([
            'columns'    => 'id,province_code,city_code,district_code,name,detail_address',
            'conditions' => 'id in ({id:array})',
            'bind'       => ['id' => [$realStoreId, $sys_store_id]],
        ])->toArray();

        if (empty($storeList)) {
            return $data;
        }

        $this->logger->info(['virtualStoreId' => $sys_store_id, 'changeStoreInfo' => $storeList]);

        $storeList        = array_column($storeList, null, 'id');
        $realStoreInfo    = $storeList[$realStoreId];
        $virtualStoreInfo = $storeList[$sys_store_id];
        //替换
        $data['nodeSn']        = $realStoreInfo['id'];
        $data['province_code'] = $realStoreInfo['province_code'];
        $data['city_code']     = $realStoreInfo['city_code'];
        $data['district_code'] = $realStoreInfo['district_code'];
        $data['address']       = $realStoreInfo['detail_address'] . ' (' . $virtualStoreInfo['detail_address'] . ' ' . $virtualStoreInfo['name'] . ')';
        return $data;
    }

    /**
     * 出库单添加
     * @param string $mach_code 货主编码
     * @param string $warehouse_id 仓库ID
     * @param $data
     * @return mixed
     * @throws ValidationException
     */
    public function warehouseAdd($mach_code, $warehouse_id, $data)
    {
        $pc_code = $data['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG ? $data['pc_code'] : $data['sys_store_id'];
        //成本中心不可为空
        $setting_pc_code = MaterialSettingService::getPcCodeToScmRequired();
        if ($setting_pc_code && empty($pc_code)) {
            throw new ValidationException(static::$t->_('material_wms_pc_code_not_null'), ErrCode::$VALIDATE_ERROR);
        }
        // 耗材申请 使用网点如果是虚拟网点，同步scm的时候，转换成真实网点信息
        if (!empty($data['bizExt']) && $data['bizExt'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
            $data = $this->autoChangeRealStoreInfo($data['sys_store_id'], $data);
        }

        //查询员工信息
        $user = new UserService();
        $staff_info = $user->getUserByIdInRbi($data['staff_id']);
        if (empty($staff_info)) {
            throw new ValidationException(static::$t->_('access_data_staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        //查询省信息
        $sys_province = SysProvinceModel::findFirst([
            'columns' => 'name',
            "conditions" => "code=:code:",
            'bind' => ["code" => $data['province_code']]
        ]);
        if (empty($sys_province)) {
            throw new ValidationException('province information not found', ErrCode::$VALIDATE_ERROR);
        }
        //查询市信息
        $sys_city = SysCityModel::findFirst([
            'columns' => 'name',
            "conditions" => "code=:code:",
            'bind' => ["code" => $data['city_code']]
        ]);
        if (empty($sys_city)) {
            throw new ValidationException('city information not found', ErrCode::$VALIDATE_ERROR);
        }
        //查询区信息
        $sys_district = SysDistrictModel::findFirst([
            'columns' => 'name,postal_code',
            "conditions" => "code=:code:",
            'bind' => ["code" => $data['district_code']]
        ]);
        if (empty($sys_district)) {
            throw new ValidationException('district information not found', ErrCode::$VALIDATE_ERROR);
        }
        if (empty($sys_district->postal_code)) {
            //邮编如果是空的
            throw new ValidationException('postal code information is empty', ErrCode::$VALIDATE_ERROR);
        } else {
            //只需要传输一个邮编
            $postal_code_arr = explode(',', $sys_district->postal_code);
            $sys_district->postal_code = $postal_code_arr[0];
        }
        $scm_data['warehouseId'] = $warehouse_id;
        $scm_data['orderSn']          = $data['no'];
        $scm_data['channelSource']    = 'FlashOA';
        $scm_data['nodeSn']           = isset($data['nodeSn']) ? $data['nodeSn'] : $pc_code;//总部传输部门上的pc_code，网点传输网点ID

        $scm_data['consigneeName']    = $data['staff_name'].'('.$data['staff_id'].')';//收货人
        $scm_data['consigneePhone']   = !empty($staff_info->mobile_company) ? $staff_info->mobile_company : $staff_info->mobile;//联系方式

        $scm_data['province']         = $sys_province->name;//省名称
        $scm_data['city']             = $sys_city->name;//市名称
        $scm_data['district']         = $sys_district->name;//区名称
        $scm_data['postalCode']       = $sys_district->postal_code;//邮编
        $scm_data['consigneeAddress'] = $data['address'];//详细地址
        $scm_data['deliveryWay']      = $data['deliveryWay'] ?? self::DELIVERY_WAY_EXPRESS_NAME;//配送方式为默认快递
        $scm_data['goodsStatus']      = 'normal';
        //备注需要拼接部门或网点名称
        if ($data['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            $remark = 'HO-' . $data['node_department_name'] . ';' . $data['mark'];
        } else {
            $remark = $data['store_name'] . ';' . $data['mark'];
        }
        $scm_data['remark']           = $remark;//备注
        $scm_data['bizExt']           = $data['bizExt'] ?? '0';//耗材使用的是2 资产可以为空
        $goods = [];
        foreach ($data['products'] as $k => $reqItems) {
            $goods[] = [
                'i'             => $k,
                "barCode"       => $reqItems['barcode'],
                "num"     => $reqItems['this_time_num'],
                "price"         => 100,//出库商品json，产品要求写死100
            ];
        }
        $scm_data['goods']            = json_encode($goods, JSON_UNESCAPED_UNICODE);
        $this->logger->info(['scm_data'=>$scm_data,'mach_code'=>$mach_code]);
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['returnWarehouseAdd'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 出库单审核
     * @param string $mach_code 货主编码
     * @param string $scm_no SCM出库单号
     * @return bool|int
     * @throws ValidationException
     */
    public function auditOutBound($mach_code, $scm_no)
    {
        $scm_service    = new ScmService();
        $out_bound_data = $scm_service->outboundOrderStatus($mach_code, $scm_no);
        if ($out_bound_data['status'] == ScmEnums::OUT_BOUND_ORDER_STATUS_AUDIT) { //订单在scm状态为待审核
            $scm_data['orderSn'] = $scm_no;
            $scm                 = new BaseScmService($mach_code);
            $post_str            = $scm->buildRequestParam($scm_data);
            $result              = $scm->newPostRequest(self::$scm_config['outbound'], $post_str);
            if ($result['code'] == ErrCode::$SUCCESS) {
                return MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT;
            } else {
                return false;
            }

        } elseif (in_array($out_bound_data['status'], [ScmEnums::OUT_BOUND_ORDER_STATUS_FINISH, ScmEnums::OUT_BOUND_ORDER_STATUS_ALLOCATION, ScmEnums::OUT_BOUND_ORDER_STATUS_OUTBOUND_ING, ScmEnums::OUT_BOUND_ORDER_STATUS_PICKING, ScmEnums::OUT_BOUND_ORDER_STATUS_PACK])) { //如果人工已经审核了
            return MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT;
        } elseif ($out_bound_data['status'] == ScmEnums::OUT_BOUND_ORDER_STATUS_CANCEL) {
            return MaterialWmsEnums::STATUS_CANCEL;
        } else {
            $this->logger->info('出库单状态不是待审核 且也不在已审核、库存分配失败、出库中、拣货完成、已打包里面,当前返回状态为 ' . json_encode($out_bound_data, JSON_UNESCAPED_UNICODE) . ',出库单号为 ' . $scm_no);
            return false;
        }
    }

    /**
     * 出库单取消
     * @param string $mach_code 货主编码
     * @param string $scm_no SCM出库单号
     * @return bool
     * @throws ValidationException
     */
    public function cancelOutbound($mach_code, $scm_no)
    {
        $scm_data['outSn'] = $scm_no;
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['cancelOutbound'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS || $result['code'] == 1706) {
            return true;
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 获取货主列表
     * @param integer $type 1采购付款入库单，2scm 入库单，3同步物料到scm
     * @return array|mixed
     */
    public function scmCargoOwner($type = self::TYPE_SYNC_SCM)
    {
        $service = new PurchaseBaseService();
        return $service->scmCargoOwner($type);
    }

    /**
     * 查询所选货主的仓库下该Barcode的可售库存
     * @param string $mach_code 货主编码
     * @param string $warehouse_id 仓库ID
     * @param string $barcode barcode
     * @param string $locale 当前语种
     * @return array
     * @throws ValidationException
     */
    public function goodsStock($mach_code, $warehouse_id, $barcode, $locale)
    {
        $barcode_count = count(explode(",",$barcode));
        if ($barcode_count > 500) {
            //scm查询一次最多支持500个barcode
            throw new ValidationException(static::$t->_('scm_get_good_stock_max', ['max_limit' => 500]), ErrCode::$VALIDATE_ERROR);
        }
        $scm_data['barCode'] = $barcode;
        $scm_data['warehouseId'] = $warehouse_id;
        $scm = new BaseScmService($mach_code, self::TYPE_SYNC_SCM);
        $post_str = $scm->buildRequestParam($scm_data);
        return $scm->newPostRequest(self::$scm_config['goodsStock'], $post_str, $locale);
    }

    /**
     * 获取仓库信息
     * @return array
     */
    public function getStockList()
    {
        $locale = static::$language;
        $redis = $this->getDI()->get('redis');
        $warehouse_key = self::STOCK_GET_WAREHOUSE_LIST;
        $scm_warehouse_data = $redis->get($warehouse_key);
        // 不存在缓存数据
        if (empty($scm_warehouse_data)) {
            $stock_data = [];
            //则去查询每个货主下的仓库列表，然后缓存
            $cargo_owner = $this->scmCargoOwner();
            if (!empty($cargo_owner)) {
                foreach ($cargo_owner as $key => $item) {
                    $result = $this->getStockData($item['mach_code'], $locale);
                    $stock_data[$item['mach_code']] = array_column($result, null, 'id');
                    sleep(1);
                }
            }
            //查询到列表缓存
            if (!empty($stock_data)) {
                $redis->setex($warehouse_key, 86400 * 10, json_encode($stock_data, JSON_UNESCAPED_UNICODE));
            }
            return $stock_data;
        }
        return json_decode($scm_warehouse_data, true);
    }

    /**
     * 发货单物流信息查询
     * @param string $mach_code 货主编码
     * @param string $outbound_no 出库单号
     * @param string $locale 当前语种
     * @return mixed
     * @throws ValidationException
     */
    public function getOutboundTrackingInfo($mach_code, $outbound_no, $locale)
    {
        $scm_data['orderSn'] = $outbound_no;
        $scm = new BaseScmService($mach_code, self::TYPE_SYNC_SCM);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['getOutboundTrackingInfo'], $post_str, $locale);
        if ($result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }

    }


    /**
     * 出库单处理进程信息
     * @param string $mach_code 货主编码
     * @param string $scm_no  scm出库单号
     * @param string $order_sn oa侧出库单号
     * @return mixed
     * @throws ValidationException
     */
    public function outboundOrderStatus($mach_code, $scm_no, $order_sn = '')
    {
        $scm_data['outSn'] = $scm_no;
        $scm_data['orderSn'] = $order_sn;
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['getOutboundOrderStatus'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 获取仓库数据
     * @param string $mach_code 货主code
     * @param string $locale 当前语种
     * @return array
     */
    public function getStockData($mach_code, $locale)
    {
        $scm_data = $scm_data = [];

        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['getWarehouseList'], $post_str, $locale);
        if ($result['code'] == ErrCode::$SUCCESS) {
            $scm_data = $result['data'];
        }
        return $scm_data;
    }

    /**
     * 入库单添加
     * @param string $mach_code 货主编码
     * @param array $scm_data 参数
     * @return mixed
     * @throws ValidationException
     */
    public function createStorage($mach_code, $scm_data)
    {
        $scm = new BaseScmService($mach_code);
        foreach ($scm_data as $k => $v) {
            if (is_array($v)) {
                $scm_data[$k] = json_encode($v);
            }
        }
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['createStorage'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }



    /**
     * 入库单撤销
     * @param string $mach_code 货主编码
     * @param array $scm_data 参数
     * @return mixed
     * @throws ValidationException
     */
    public function cancelStorage($mach_code, $scm_data)
    {
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['cancelStorage'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 入库单审核
     * @param string $mach_code 货主编码
     * @param array $scm_data 入库单参数
     * @return bool|int
     * @throws ValidationException
     */
    public function auditInBound($mach_code, $scm_data)
    {
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['inbound'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return true;
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 入库单删除
     * @param string $mach_code 货主编码
     * @param array $scm_data 参数
     * @return mixed
     * @throws ValidationException
     */
    public function deleteStorage($mach_code, $scm_data)
    {
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['deleteStorage'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }

    /**
     * 出库单详情
     * @param string $mach_code 货主编码
     * @param array $scm_data 参数
     * @return mixed
     * @throws ValidationException
     */
    public function outBoundOrderDetail($mach_code, $scm_data)
    {
        $scm = new BaseScmService($mach_code);
        $post_str = $scm->buildRequestParam($scm_data);
        $result = $scm->newPostRequest(self::$scm_config['outBoundOrderDetail'], $post_str);
        if ($result['code'] == ErrCode::$SUCCESS) {
            return $result['data'];
        } else {
            throw new ValidationException($result['message'], $result['code']);
        }
    }
}
