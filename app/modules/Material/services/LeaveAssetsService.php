<?php

namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\MaterialLeaveAssetsDetailAmountLogModel;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsMainAmountLogModel;
use App\Models\oa\MaterialLeaveAssetsManagerSetDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Models\oa\MaterialLeaveAssetsRecordModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Services\StaffService;
use App\Repository\backyard\StaffResignRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialAssetsRepository;
use App\Util\RedisKey;


class LeaveAssetsService extends BaseService
{
    //列表-非必填
    public static $not_must_list = [
        'staff_info_id',
        'staff_state',
        'last_work_date_start',
        'last_work_date_end',
        'leave_date_start',
        'leave_date_end',
        'node_department_id',
        'sys_store_id',
        'job_title',
        'asset_department_status',
        'asset_department_progress',
        'manager_progress',
        'is_full_amount',
        'working_country',
        'company_id',
        'updated_at_start',
        'updated_at_end',
        'created_at_start',
        'created_at_end',
        'no_valid_start',
        'no_valid_end',
        'hire_type',
        'all_asset_number_is_zero'
    ];
    //列表
    public static $validate_list = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'is_valid' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_IS_VALID_VALIDATE, //处理状态
        'asset_department_status' => 'IntIn:' . MaterialEnums::ASSET_DEPARTMENT_STATUS_VALIDATE,//资产处理状态
        'asset_department_progress' => 'Arr',//单据进度
        'asset_department_progress[*]' => 'Required|IntIn:' . MaterialEnums::ASSET_DEPARTMENT_PROGRESS_VALIDATE,//单据进度
        'manager_progress' => 'Arr',//主管处理进度
        'manager_progress[*]' => 'Required|IntIn:' . MaterialEnums::MANAGER_PROGRESS_VALIDATE,//主管处理进度
        'is_full_amount' => 'IntIn:' . MaterialEnums::IS_FULL_AMOUNT_VALIDATE,//是否足额扣款
        'staff_info_id' => 'IntGt:0',//工号
        'staff_state' => 'Arr',//在职状态
        'staff_state[*]' => 'IntGt:0',//在职状态
        'last_work_date_start' => 'Date',//最后工作日-起始
        'last_work_date_end' => 'Date',//最后工作日-截止
        'leave_date_start' => 'Date',//待离职/离职日期-起始
        'leave_date_end' => 'Date',//待离职/离职日期-截止
        'node_department_id' => 'IntGt:0',//部门ID
        'sys_store_id' => 'Arr|ArrLenGeLe:1,20',//网点id
        'sys_store_id[*]' => 'Str',
        'job_title' => 'Arr|ArrLenGeLe:1,20',//职位id
        'job_title[*]' => 'Int',
        'working_country' => 'Arr',//工作所在国家
        'working_country[*]' => 'IntIn:' . StaffInfoEnums::WORKING_COUNTRY_VALIDATE,//工作所在国家
        'company_id[*]' => 'IntGt:0',//所属公司
        'updated_at_start' => 'Date',//资产操作日期-开始
        'updated_at_end' => 'Date',//资产操作日期-结束
        'created_at_start' => 'Date',//创建日期-开始
        'created_at_end' => 'Date',//创建日期-结束
        'no_valid_start' => 'Date',//作废日期-开始
        'no_valid_end' => 'Date',//作废日期-结束
        'hire_type' => 'Arr',//雇佣类型
        'hire_type[*]' => 'IntGt:0',//雇佣类型
        'all_asset_number_is_zero' => 'IntIn:' . MaterialEnums::ALL_ASSET_NUMBER_IS_ZERO_VALIDATE, //名下资产是否为0
        'asset_department_remark' => 'StrLenGeLe:0,200',//操作备注
    ];
    //编辑页-详情
    public static $validate_detail = [
        'id' => 'Required|IntGt:0',
    ];
    //编辑页-列表-非必填
    public static $not_must_edit_list = [
        'barcode',
        'asset_code',
        'sn_code',
        'asset_name',
    ];
    //编辑页-列表
    public static $validate_edit_list = [
        'id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
        'barcode' => 'StrLenGeLe:1,30',
        'asset_code' => 'StrLenGeLe:3,100',
        'sn_code' => 'StrLenGeLe:3,100',
        'asset_name' => 'StrLenGeLe:3,100',
    ];
    //编辑页-资产转移列表
    public static $validate_edit_transfer_list = [
        'id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];
    //编辑页-暂存
    public static $validate_edit_save = [
        'id' => 'Required|IntGt:0',
        'is_submit' => 'Required|IntIn:0,1',
        'asset_department_status' => 'IntIn:0,' . MaterialEnums::ASSET_DEPARTMENT_STATUS_VALIDATE,
        'asset_department_remark' => 'StrLenGeLe:0,200',
        'file' => 'Arr|ArrLenGeLe:0,20',
        'file[*].bucket_name' => 'Required',
        'file[*].object_key' => 'Required',
        'file[*].file_name' => 'Required',
        'data' => 'Arr|ArrLenGeLe:0,200',
        'data[*].detail_id' => 'Required|IntGt:0',
        'data[*].barcode' => 'StrLenGeLe:0,30', //自己添加的资产可以编辑
        'data[*].asset_code' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'data[*].sn_code' => 'StrLenGeLe:0,50',//自己添加的资产可以编辑
        'data[*].model' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'data[*].asset_status' => 'IntIn:0,' . MaterialEnums::LEAVE_ASSET_STATUS_VALIDATE,//自己添加的资产可以编辑
        'data[*].purchase_price' => 'FloatGeLe:0,999999999999.99',//采购价,自己添加的资产可以编辑
        'data[*].net_value' => 'FloatGeLe:0,999999999999.99',//净值,自己添加的资产可以编辑
        'data[*].deduct_amount' => 'Required|FloatGeLe:0,999999999999.99',//扣费金额
        'data[*].deduct_reason' => 'StrLenGeLe:0,100',//扣费原因
        'data[*].asset_handling_status' => 'IntIn:0,' . MaterialEnums::ASSET_HANDLING_STATUS_VALIDATE,//资产处理情况
    ];
    //更新页-暂存
    public static $validate_update_save = [
        'id' => 'Required|IntGt:0',
        'is_submit' => 'Required|IntIn:0,1',
        'asset_department_status' => 'IntIn:0,' . MaterialEnums::ASSET_DEPARTMENT_STATUS_VALIDATE,
        'asset_department_remark' => 'StrLenGeLe:0,200',
        'file' => 'Arr|ArrLenGeLe:0,20',
        'file[*].bucket_name' => 'Required',
        'file[*].object_key' => 'Required',
        'file[*].file_name' => 'Required',
        'data' => 'Arr|ArrLenGeLe:0,200',
        'data[*].detail_id' => 'Required|IntGt:0',
        'data[*].barcode' => 'StrLenGeLe:0,30', //自己添加的资产可以编辑
        'data[*].asset_code' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'data[*].sn_code' => 'StrLenGeLe:0,50',//自己添加的资产可以编辑
        'data[*].model' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'data[*].asset_status' => 'IntIn:0,' . MaterialEnums::LEAVE_ASSET_STATUS_VALIDATE,//自己添加的资产可以编辑
        'data[*].purchase_price' => 'FloatGeLe:0,999999999999.99',//采购价,自己添加的资产可以编辑
        'data[*].net_value' => 'FloatGeLe:0,999999999999.99',//净值,自己添加的资产可以编辑
        'data[*].deduct_amount' => 'Required|FloatGeLe:0,999999999999.99',//扣费金额
        'data[*].deduct_reason' => 'StrLenGeLe:0,100',//扣费原因
        'data[*].asset_handling_status' => 'Required|IntIn:' . MaterialEnums::ASSET_HANDLING_STATUS_VALIDATE,//资产处理情况
    ];
    //编辑页-新增-非必填
    public static $not_must_edit_add = [
        'asset_status',
        'asset_code',
        'sn_code',
        'model',
        'purchase_price',
        'net_value',
        'deduct_reason',
    ];
    //编辑页-新增
    public static $validate_edit_add = [
        'id' => 'Required|IntGt:0',
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
        'asset_handling_status' => 'Required|IntIn:' . MaterialEnums::ASSET_HANDLING_STATUS_VALIDATE,//资产处理情况
        'deduct_amount' => 'Required|FloatGeLe:0,999999999999.99', //扣费金额
        'asset_status' => 'IntIn:' . MaterialEnums::LEAVE_ASSET_STATUS_VALIDATE,//使用状态
        'barcode' => 'Required|StrLenGeLe:1,30',
        'asset_code' => 'StrLenGeLe:0,100',
        'sn_code' => 'StrLenGeLe:0,50',
        'model' => 'StrLenGeLe:0,100',
        'purchase_price' => 'FloatGeLe:0,999999999999.99', //采购价
        'net_value' => 'FloatGeLe:0,999999999999.99', //净值
        'deduct_reason' => 'StrLenGeLe:0,100', //扣费原因
    ];

    //批量编辑(barcode维度编辑)
    public static $validate_batch_save = [
        'id' => 'Required|IntGt:0',
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
        'data' => 'Required|Arr|ArrLenGeLe:1,500',
        'data[*].barcode' => 'Required|StrLenGeLe:0,30',//barcode
        'data[*].deduct_amount' => 'Required|FloatGeLe:0,999999999999.99',//扣费金额
        'data[*].deduct_reason' => 'Required|StrLenGeLe:1,100',//扣费原因
        'data[*].asset_handling_status' => 'Required|IntIn:' . MaterialEnums::ASSET_HANDLING_STATUS_VALIDATE,//资产处理情况
    ];

    //离职员工生成离职资产
    public static $validate_create_leave_asset = [
        'staff_info_id' => 'Required|IntGt:0',//工号
        'operation_staff_id' => 'Required|IntGt:0',//操作人
        'source' => 'Required|Int',//离职来源
        'last_work_date' => 'Required|Date',//最后工作日
        'leave_date' => 'Required|Date',//待离职/离职日期
        'staff_resign_id' => 'IntGe:0',//员工离职申请id
        'work_handover' => 'IntGe:0',//工作交接人
    ];

    //离职员工取消离职
    public static $validate_cancel_leave_asset = [
        'staff_info_id' => 'Required|IntGt:0',//工号
        'staff_name' => 'Required|StrLenGeLe:1,50',//员工姓名
        'update_id' => 'Required|IntGt:0',//操作人id
        'update_name' => 'Required|StrLenGeLe:1,50',//操作人姓名
        'update_remark' => 'Required|StrLenGeLe:0,1000',//操作人填写的原因或者备注
    ];

    //编辑页-删除
    public static $validate_delete = [
        'detail_id' => 'Required|IntGt:0',
    ];
    //by离职须知
    public static $validate_get_assets_by_staffId = [
        'staff_id' => 'Required|IntGt:0',//工号
        'page_size' => 'Required|IntGt:0',//每页条数
        'page_num' => 'Required|IntGt:0', //页码
    ];
    //by离职须知/转个人代理成功的资产协议-明细
    public static $validate_get_assets_detail_by_staffId = [
        'staff_id' => 'Required|IntGt:0',//工号
        'page_size' => 'IntGt:0',//每页条数
        'page_num' => 'IntGt:0', //页码
        'barcode' => 'StrLenGeLe:0,30', //barcode
        'source' => 'IntGe:0',//来源0离职须知，1资产协议
    ];
    //上级变更
    public static $validate_leave_asset_job_change = [
        'staff_info_id' => 'Required|IntGt:0',//员工工号
        'before_manager_id' => 'Required|IntGt:0',//原主管id
        'after_manager_id' => 'Required|IntGt:0',//现主管id
    ];
    //操作记录列表-非必填
    public static $not_must_record_list = [
        'type',
        'staff_info_id',
    ];
    //操作记录列表
    public static $validate_record_list = [
        'leave_assets_id' => 'Required|IntGt:0',//离职资产id
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'type' => 'Arr',//类型
        'type[*]' => 'IntIn:' . MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_VALIDATE,//类型
        'staff_info_id' => 'IntGt:0',//操作人工号
    ];
    //hcm导出时查询员工名下资产
    public static $validate_hcm_not_return_asset = [
        'staff_ids' => 'Required|ArrLenGeLe:1,500',//员工工号
    ];
    //by端离职申请 详情页资产列表
    public static $validate_by_get_audit_leave_assets = [
        'resign_id' => 'Required|IntGt:0',//离职申请id
    ];

    //短链接-查询员工名下资产
    public static $validate_leave_asset_staff_count = [
        'staff_id' => 'Required|IntGt:0',//员工工号
    ];
    //资产部处理-批量确认
    public static $validate_batch_confirm = [
        'asset_department_status' => 'Required|IntIn:' . MaterialEnums::ASSET_DEPARTMENT_STATUS_VALIDATE,
        'asset_department_remark' => 'Required|StrLenGeLe:1,200',
        'assets' => 'Required|Arr|ArrLenGeLe:1,100',
        'assets[*]' => 'Required|IntGe:1',
    ];

    //正式员工转个人代理，个人代理到岗确认，自动转移资产到代理名下
    public static $validate_transfer_leave_assets_to_personal_agent = [
        'old_staff_id' => 'Required|IntGt:0',//旧工号（离职正式员工工号）
        'new_staff_id' => 'Required|IntGt:0',//新工号（在职个人代理工号）
    ];

    public static $max_data_number = 500;
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LeaveAssetsService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     * @date 2023/3/10
     */
    public function getDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                //员工在职状态
                'staff_state' => StaffInfoEnums::$staff_state,
                //资产处理状态
                'asset_department_status_item' => MaterialEnums::$asset_department_status_list,
                //资产部处理进度(单据进度)
                'asset_department_progress_item' => MaterialEnums::$asset_department_progress_list,
                //主管处理进度
                'manager_progress_item' => MaterialEnums::$manager_progress_list,
                //是否作废
                'is_valid_item' => MaterialEnums::$leave_asset_is_valid_list,
                //工作所在国家
                'working_country_item' => StaffInfoEnums::$working_country,
                //操作记录类型
                'leave_assets_record_type_item' => MaterialEnums::$leave_assets_record_type_list,
                //是否足额扣款
                'is_full_amount_item' => MaterialEnums::$is_full_amount_list,
                //资产状况
                'leave_asset_personal_state_item' => MaterialEnums::$leave_asset_personal_state_list,
                'leave_asset_public_state_item' => MaterialEnums::$leave_asset_public_state_list,
                //资产部处理情况
                'asset_handling_status_item' => MaterialEnums::$asset_handling_status_list,
                //资产使用状态
                'asset_status_item' => array_diff_key(MaterialEnums::$asset_status, [MaterialEnums::ASSET_STATUS_IN_STORAGE => '']),//手工添加离职资产时，离职资产的使用状态不可以选择到入库中。
                //名下资产是否为0
                'all_asset_number_is_zero' => MaterialEnums::$all_asset_number_is_zero,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
            //所属公司
            $data['cost_company_item'] = (new PurchaseService())->getCooCostCompany();
            //离职资产处理-产部处理情况-低值易耗不强制归还-对应barcode组
            $data['material_leave_asset_low_value_barcodes'] = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_low_value_barcodes');
            //雇佣类型
            $data['hire_type_item'] = EnumsService::getInstance()->getHireTypeEnum(true);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('获取离职资产-资产部处理异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部处理列表
     * @param $params
     * @param $locale
     * @param $user
     * @param bool $export 是否导出
     * @param int $count 数据量
     * @return array
     * @date 2023/3/2
     */
    public function getList($params, $locale, $user, $export = false, $count = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            if (!$export) {
                $count = $this->getListCount($params, $user);
            }
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.staff_info_id, main.staff_name, main.staff_name_en, staff.state as staff_state, staff.wait_leave_state, staff.last_work_date, staff.leave_date, staff.hire_type, main.job_name, main.job_title,
                 main.node_department_name, main.node_department_id, main.sys_store_id, main.sys_store_name, main.working_country, main.company_name,
                 main.no, main.updated_at, main.asset_department_status, main.manager_progress, main.manager_updated_id, main.damage_assets_number, main.unreturned_assets_number, main.all_asset_number,
                 main.all_deduct_amount, main.deduct_amount, main.return_amount, main.loss_amount, main.asset_department_progress, main.is_full_amount, main.no_valid_staff_name, main.no_valid_at, main.staff_resign_id, main.created_at, main.asset_department_remark';
                $builder->columns($columns);
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                //组合搜索条件
                $builder = $this->getCondition($builder, $params, $user, 'main.');
                $builder->limit($page_size, $offset);
                $builder->orderby('main.created_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $export);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部处理列表-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as count');
        $builder->from(['main' => MaterialLeaveAssetsModel::class]);
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $user, 'main.');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 资产部处理列表-条件拼接
     * @param $builder
     * @param $condition
     * @param $user
     * @param string $prefix 表前缀, eg: main.
     * @return mixed
     * @throws ValidationException
     * @date 2023/3/2
     */
    public function getCondition($builder, $condition, $user, $prefix = '')
    {
        //资产数据管控组-可看数据权限范围
        $data_permission = MaterialSettingService::getInstance()->getStaffDataPermissionGroup($user['id'], MaterialSettingService::DATA_PERMISSION_LEAVE_ASSET);
        $is_valid = !empty($condition['is_valid']) ? $condition['is_valid'] : MaterialEnums::LEAVE_ASSET_IS_VALID_YES;//是否有效
        $staff_info_id = !empty($condition['staff_info_id']) ? $condition['staff_info_id'] : 0;//员工id
        $staff_state = !empty($condition['staff_state']) ? $condition['staff_state'] : [];//在职状态
        $last_work_date_start = !empty($condition['last_work_date_start']) ? $condition['last_work_date_start'] : '';//最后工作日-开始
        $last_work_date_end = !empty($condition['last_work_date_end']) ? $condition['last_work_date_end'] : '';//最后工作日-结束
        $leave_date_start = !empty($condition['leave_date_start']) ? $condition['leave_date_start'] : '';//待离职/离职日期-开始
        $leave_date_end = !empty($condition['leave_date_end']) ? $condition['leave_date_end'] : '';//待离职/离职日期-结束
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//部门id
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : [];//网点id
        $job_title = !empty($condition['job_title']) ? $condition['job_title'] : [];//职位id

        $asset_department_status = !empty($condition['asset_department_status']) ? $condition['asset_department_status'] : 0;//资产处理状态
        $asset_department_progress = !empty($condition['asset_department_progress']) ? $condition['asset_department_progress'] : [];//资产处理进度
        $manager_progress = !empty($condition['manager_progress']) ? $condition['manager_progress'] : [];//主管处理进度
        $is_full_amount = !empty($condition['is_full_amount']) ? $condition['is_full_amount'] : 0;//是否足额扣款
        $working_country = !empty($condition['working_country']) ? $condition['working_country'] : [];//工作所在国家
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司
        $updated_at_start = !empty($condition['updated_at_start']) ? $condition['updated_at_start'] : '';//更新时间(操作时间)-开始
        $updated_at_end = !empty($condition['updated_at_end']) ? $condition['updated_at_end'] : '';//更新时间(操作时间)-结束
        $no_valid_start = !empty($condition['no_valid_start']) ? $condition['no_valid_start'] : '';//作废日期-开始
        $no_valid_end = !empty($condition['no_valid_end']) ? $condition['no_valid_end'] : '';//作废日期-结束
        $created_at_start = !empty($condition['created_at_start']) ? $condition['created_at_start'] : '';//创建时间-开始
        $created_at_end = !empty($condition['created_at_end']) ? $condition['created_at_end'] : '';//创建时间-结束
        $hire_type = !empty($condition['hire_type']) ? $condition['hire_type'] : [];//雇佣类型
        $all_asset_number_is_zero = !empty($condition['all_asset_number_is_zero']) ? $condition['all_asset_number_is_zero'] : 0;//名下资产是否为0
        $asset_department_remark = !empty($condition['asset_department_remark']) ? $condition['asset_department_remark'] : '';//资产部操作备注

        //资产数据管控组-可看数据权限范围
        if (!empty($data_permission['node_department_id'])) {
            $builder->inWhere($prefix . 'node_department_id', $data_permission['node_department_id']);
        }

        //处理状态
        if (!empty($is_valid)) {
            $builder->andWhere($prefix . 'is_valid = :is_valid:', ['is_valid' => $is_valid]);
        }
        if (!empty($staff_info_id)) {
            //员工id
            $builder->andWhere($prefix . 'staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        //在职状态
        if (!empty($staff_state)) {
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $staff_state)) {
                //搜索包含待离职
                if (count($staff_state) == 1) {
                    //只搜索待离职的
                    $builder->andWhere('staff.state = :state: and staff.wait_leave_state = :wait_leave_state:', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
                } else {
                    //掺杂待离职混合搜索
                    $builder->andWhere('(staff.state = :state: and staff.wait_leave_state = :wait_leave_state:) OR (staff.state in ({states:array}) AND staff.wait_leave_state = :wait_leave_state_no:)', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES, 'states' => $staff_state, 'wait_leave_state_no' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
                }
            } else {
                $builder->inWhere('staff.state', $staff_state);
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state:', ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
            }
        }
        if (!empty($last_work_date_start)) {
            //最后工作日-开始
            $builder->andWhere('staff.last_work_date >= :last_work_date_start:', ['last_work_date_start' => $last_work_date_start]);
        }
        if (!empty($last_work_date_end)) {
            //最后工作日-结束
            $builder->andWhere('staff.last_work_date <= :last_work_date_end:', ['last_work_date_end' => $last_work_date_end]);
        }
        if (!empty($leave_date_start)) {
            //待离职/离职日期-开始
            $builder->andWhere('staff.leave_date >= :leave_date_start:', ['leave_date_start' => $leave_date_start]);
        }
        if (!empty($leave_date_end)) {
            //待离职/离职日期-结束
            $builder->andWhere('staff.leave_date <= :leave_date_end:', ['leave_date_end' => $leave_date_end]);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere($prefix . 'node_department_id', $department_ids);
        }
        if (!empty($sys_store_id)) {
            //网点
            $sys_store_id = array_values(array_unique($sys_store_id));
            $builder->inWhere($prefix . 'sys_store_id', $sys_store_id);
        }
        if (!empty($job_title)) {
            //职位
            $job_title = array_values(array_unique($job_title));
            $builder->inWhere($prefix . 'job_title', $job_title);
        }
        if (!empty($asset_department_status)) {
            //资产处理状态
            $builder->andWhere($prefix . 'asset_department_status = :asset_department_status:', ['asset_department_status' => $asset_department_status]);
        }
        if (!empty($asset_department_progress)) {
            //资产处理进度
            $asset_department_progress = array_values(array_unique($asset_department_progress));
            $builder->inWhere($prefix . 'asset_department_progress', $asset_department_progress);
        }
        if (!empty($manager_progress)) {
            //主管处理进度
            $manager_progress = array_values(array_unique($manager_progress));
            $builder->inWhere($prefix . 'manager_progress', $manager_progress);
        }
        if (!empty($is_full_amount)) {
            //是否足额扣款
            $builder->andWhere($prefix . 'is_full_amount = :is_full_amount:', ['is_full_amount' => $is_full_amount]);
        }
        if (!empty($working_country)) {
            //工作所在国家
            $builder->inWhere($prefix . 'working_country', $working_country);
        }
        if (!empty($company_id)) {
            //所属公司
            if (is_array($company_id)) {
                $builder->inWhere($prefix . 'company_id', $company_id);
            } else {
                $builder->andWhere($prefix . 'company_id = :company_id:', ['company_id' => $company_id]);
            }
        }
        if (!empty($updated_at_start)) {
            //资产操作日期-开始
            $builder->andWhere($prefix . 'updated_at >= :updated_at_start:', ['updated_at_start' => $updated_at_start . ' 00:00:00']);
        }
        if (!empty($updated_at_end)) {
            //资产操作日期-结束
            $builder->andWhere($prefix . 'updated_at <= :updated_at_end:', ['updated_at_end' => $updated_at_end . ' 23:59:59']);
        }

        if (!empty($no_valid_start)) {
            //作废日期-开始
            $builder->andWhere($prefix . 'no_valid_at >= :no_valid_start:', ['no_valid_start' => $no_valid_start . ' 00:00:00']);
        }
        if (!empty($no_valid_end)) {
            //作废日期-结束
            $builder->andWhere($prefix . 'no_valid_at <= :no_valid_end:', ['no_valid_end' => $no_valid_end . ' 23:59:59']);
        }

        if (!empty($created_at_start)) {
            //创建日期-开始
            $builder->andWhere($prefix . 'created_at >= :created_at_start:', ['created_at_start' => $created_at_start . ' 00:00:00']);
        }
        if (!empty($created_at_end)) {
            //创建日期-结束
            $builder->andWhere($prefix . 'created_at <= :created_at_end:', ['created_at_end' => $created_at_end . ' 23:59:59']);
        }
        if (!empty($hire_type)) {
            $builder->inWhere('staff.hire_type', array_values($hire_type));
        }
        //名下资产是否为0
        if ($all_asset_number_is_zero == MaterialEnums::ALL_ASSET_NUMBER_IS_ZERO_YES) {
            //是，则搜索：以下场景都满足的员工 & 资产台账里，使用人=该员工，资产数量-0 &资产转移表里，接收人=该员工，接收状态为=待接收，记录=0 & 离职资产里，资产数量=0
            $builder->andWhere($prefix . 'is_have_own_asset = :is_have_own_asset:', ['is_have_own_asset' => MaterialEnums::ALL_ASSET_NUMBER_IS_ZERO_YES]);
        } else if ($all_asset_number_is_zero == MaterialEnums::ALL_ASSET_NUMBER_IS_ZERO_NO) {
            //否，则搜索离职资产表里，该员工名下资产数量>0的员工
            $builder->andWhere($prefix . 'all_asset_number > :all_asset_number:', ['all_asset_number' => 0]);
        }
        //操作备注
        if (!empty($asset_department_remark)) {
            $builder->andWhere($prefix . 'asset_department_remark LIKE :asset_department_remark:', ['asset_department_remark' => '%' . $asset_department_remark . '%']);
        }
        return $builder;
    }

    /**
     * 资产部处理列表-列表数据处理
     * @param $items
     * @param bool $export
     * @return array
     * @date 2023/3/2
     */
    private function handleListItems($items, $export = false)
    {
        if (empty($items)) {
            return [];
        }
        //查询by离职申请的审批状态
        $staff_resign_ids = array_column($items, 'staff_resign_id');
        foreach ($staff_resign_ids as $k => $v) {
            if (empty($v)) {
                unset($staff_resign_ids[$k]);
            }
        }
        $not_approve_ids = [];
        if (!empty($staff_resign_ids)) {
            $staff_resign_ids = array_values(array_unique($staff_resign_ids));
            $not_approve_data = StaffResignRepository::getInstance()->getNotApproveIds($staff_resign_ids);
            $not_approve_ids = array_column($not_approve_data, 'resign_id');
        }
        //雇佣类型枚举
        $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
        foreach ($items as &$item) {
            //国家
            $item['working_country_text'] = isset(StaffInfoEnums::$working_country[$item['working_country']]) ? static::$t[StaffInfoEnums::$working_country[$item['working_country']]] : '';
            //主管进度
            $item['manager_progress_text'] = isset(MaterialEnums::$manager_progress_list[$item['manager_progress']]) ? static::$t[MaterialEnums::$manager_progress_list[$item['manager_progress']]] . (($item['manager_progress'] == MaterialEnums::MANAGER_PROGRESS_NO_NEED_ASSET) ? ('(' . $item['manager_updated_id'] . ')') : '') : '';
            //资产进度
            $item['asset_department_progress_text'] = isset(MaterialEnums::$asset_department_progress_list[$item['asset_department_progress']]) ? static::$t[MaterialEnums::$asset_department_progress_list[$item['asset_department_progress']]] : '';
            //资产部处理状态
            $item['asset_department_status_text'] = isset(MaterialEnums::$asset_department_status_list[$item['asset_department_status']]) ? static::$t[MaterialEnums::$asset_department_status_list[$item['asset_department_status']]] : '';
            //离职状态
            $staff_state = ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['staff_state'];
            $item['staff_state_text'] = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '';
            //是否足额扣款
            $item['is_full_amount_text'] = isset(MaterialEnums::$leave_asset_is_valid_list[$item['is_full_amount']]) ? static::$t[MaterialEnums::$leave_asset_is_valid_list[$item['is_full_amount']]] : '';
            //资产操作日期=更新日期
            $item['asset_updated_at'] = $item['updated_at'];
            //是否允许编辑 (by申请离职,且不是审批通过的不允许编辑,因为审批通过后会重置资产处理状态)
            $item['can_edit'] = !empty($item['staff_resign_id']) && !empty($not_approve_ids) && in_array($item['staff_resign_id'], $not_approve_ids) ? false : true;
            //雇佣类型
            $item['hire_type_text'] = $hire_type_enum[$item['hire_type']] ?? '';
        }
        $list = $items;
        if ($export) {
            $staff_info_ids = array_column($items, 'staff_info_id');
            $staff_info_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_info_ids);
            $export_data = [];
            foreach ($items as $value) {
                $one_hr_staff_info = $staff_info_list[$value['staff_info_id']] ?? [];
                $tmp = [
                    $value['asset_department_status_text'],
                    $value['staff_info_id'],
                    $value['staff_name'],
                    $value['job_name'],
                    $value['hire_type_text'],
                    $value['staff_state_text'],
                    $value['last_work_date'],
                    $value['leave_date'],
                    !empty($one_hr_staff_info['leave_type']) ? static::$t->_('leave_type_' . $one_hr_staff_info['leave_type']) : '',//离职类型
                    !empty($one_hr_staff_info['leave_reason']) ? static::$t->_('leave_reason_' . $one_hr_staff_info['leave_reason']) : '',//离职类型
                    $value['node_department_name'],
                    $value['sys_store_id'],
                    $value['sys_store_name'],
                    $value['working_country_text'],
                    $value['company_name'],
                    $value['asset_department_remark'],
                    $value['all_asset_number'],
                    $value['damage_assets_number'],
                    $value['unreturned_assets_number'],
                    $value['all_deduct_amount'],
                    $value['deduct_amount'],
                    $value['return_amount'],
                    $value['loss_amount'],
                    $value['created_at'],
                ];
                $export_data[] = $tmp;
            }
            $list = $export_data;
        }
        return $list;
    }

    /**
     * 资产部处理-操作记录列表
     * @param $params
     * @return array
     * @date 2023/3/2
     */
    public function getRecordList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getRecordListCount($params);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id, type, staff_info_id, staff_info_name, node_department_id, node_department_name, job_title,
                job_name, operation_content, operation_content_text, operation_at';
                $builder->columns($columns);
                $builder->from(MaterialLeaveAssetsRecordModel::class);
                //组合搜索条件
                $builder = $this->getRecordCondition($builder, $params);
                $builder->limit($page_size, $offset);
                $builder->orderby('operation_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleRecordListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leave-asset-record-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部处理-操作记录列表-总数
     * @param $condition
     * @return int
     * @date 2023/3/2
     */
    public function getRecordListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from([MaterialLeaveAssetsRecordModel::class]);
        $builder->columns('count(id) as count');
        //组合搜索条件
        $builder = $this->getRecordCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 资产部处理-操作记录列表-条件拼接
     * @param $builder
     * @param $condition
     * @return mixed
     * @date 2023/3/2
     */
    public function getRecordCondition($builder, $condition)
    {
        $type = !empty($condition['type']) ? $condition['type'] : [];//是否有效
        $staff_info_id = !empty($condition['staff_info_id']) ? $condition['staff_info_id'] : 0;//员工id
        //固定条件
        $builder->andWhere('leave_assets_id = :leave_assets_id:', ['leave_assets_id' => $condition['leave_assets_id']]);
        //操作类型
        if (!empty($type)) {
            $builder->inWhere('type', $type);
        }
        if (!empty($staff_info_id)) {
            //员工id
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }

        return $builder;
    }

    /**
     * 资产部处理列表-列表数据处理
     * @param $items
     * @return array
     * @date 2023/3/2
     */
    private function handleRecordListItems($items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            //操作类型
            $item['type_text'] = isset(MaterialEnums::$leave_assets_record_type_list[$item['type']]) ? static::$t[MaterialEnums::$leave_assets_record_type_list[$item['type']]] : '';
            //操作内容
            $item['operation_content'] = $this->dealRecordContentV1($item['type'], $item['operation_content'], $item['operation_content_text']);
        }
        return $items;
    }

    /**
     * 拼接操作日志的格式
     * 未来有别的结构的json,可以定义V2版本的拼接方式, 数据上区分V1,V2
     * @param $type
     * @param $content
     * @param $content_text
     * @return string
     * @date 2023/3/29
     */
    public function dealRecordContentV1($type, $content, $content_text)
    {
        //新增时是文本
        if (in_array($type, [MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_ADD, MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_CANCEL])) {
            $return_content = !empty($content_text) ? static::$t[$content_text] : '';
        } else {
            //非新增时是json
            $operation_content = json_decode($content, true);
            //拿到翻译字段时的前缀
            $fields_prefix = MaterialEnums::TRANSLATE_RECORD_LOG_FIELDS_PREFIX;
            //获取需要翻译的值枚举
            $record_need_view_value = MaterialEnums::recordNeedViewValue();
            //翻译内容中的字段名和枚举值
            $content = [];
            foreach ($operation_content['content'] as $key => $info) {
                //$info是每一条编辑数据,包含编辑的所有字段和值
                $line_content = [];
                foreach ($info as $field => $value) {
                    if ($field == 'record_sub_type') {
                        //用来记录操作的子类型,不用显示
                        continue;
                    }
                    //翻译字段
                    $translate_key = $fields_prefix . $field;
                    $translate_field = in_array($field, MaterialEnums::$record_need_view_fields) ? static::$t[$translate_key] : $field;
                    //翻译value
                    $translate_value = $value;
                    if (isset($record_need_view_value[$field])) {
                        //得到需要翻译的值对应的MaterialEnums中的key
                        $enum_kv = $record_need_view_value[$field];
                        $translate_value = isset($enum_kv[$value]) ? static::$t[$enum_kv[$value]] : $value;
                    }
                    //按固定格式拼接到一起,作为一行中的一个字段信息
                    $line_content[] = $translate_field . ': ' . $translate_value;
                }
                $sub_type = isset(MaterialEnums::$leave_assets_record_sub_type_list[$info['record_sub_type']]) ? static::$t[MaterialEnums::$leave_assets_record_sub_type_list[$info['record_sub_type']]] : '未知';
                //一行数据所有字段拼成一串
                $content[] = '{' . $sub_type . '} ' . implode(';', $line_content);
            }
            //多行数据用换行符分割
            $return_content = !empty($content) ? implode('<br/>', $content) : '';
        }
        return $return_content;
    }

    /**
     * 资产部处理-编辑页详情
     * @param $params
     * @param $user
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    public function getDetail($params, $user, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询离职资产
            $data_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //查询员工信息
            $staff_info_data = [];
            if (!empty($data_info->staff_info_id)) {
                $staff_info = HrStaffInfoModel::find([
                    'conditions' => 'staff_info_id in ({staff_info_id:array})',
                    'bind' => ['staff_info_id' => [$data_info->staff_info_id, $data_info->manager_staff_id]]
                ])->toArray();
                $staff_info_data = array_column($staff_info, null, 'staff_info_id');
            }
            //查主管职位
            $job_info_data = [];
            if (!empty($staff_info_data[$data_info->manager_staff_id]['job_title'])) {
                $job_info = HrJobTitleModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $staff_info_data[$data_info->manager_staff_id]['job_title']]
                ]);
                $job_info_data = $job_info ? $job_info->toArray() : [];
            }


            if ($data_info) {
                $one_staff_info = $staff_info_data[$data_info->staff_info_id];
                $staff_state = empty($one_staff_info) ? '' : ($one_staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $one_staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $one_staff_info['state']);
                //查员工离职日期/最后工作日(by员工表没有最后工作日,所以用mini表)
                $staff_info = MiniHrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $data_info->staff_info_id]
                ]);
                $company_list = EnumsService::getInstance()->getPayrollCompanyInfo();
                //雇佣类型枚举
                $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
                $data = [
                    'id'                      => $data_info->id,
                    'staff_info_id'           => $data_info->staff_info_id,
                    'staff_name'              => $data_info->staff_name,
                    'staff_name_en'           => $data_info->staff_name_en,
                    'staff_state'             => $staff_state,
                    'staff_state_text'        => !empty($staff_state) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '',
                    'last_work_date'          => $staff_info->last_work_date ?? '',
                    'leave_date'              => $staff_info->leave_date ?? '',
                    'job_name'                => $data_info->job_name,
                    'job_title'               => $data_info->job_title,
                    'node_department_name'    => $data_info->node_department_name,
                    'node_department_id'      => $data_info->node_department_id,
                    'sys_store_id'            => $data_info->sys_store_id,
                    'sys_store_name'          => $data_info->sys_store_name,
                    'working_country_text'    => isset(StaffInfoEnums::$working_country[$data_info->working_country]) ? static::$t[StaffInfoEnums::$working_country[$data_info->working_country]] : '',
                    'no'                      => $data_info->no,
                    'manager_updated_at'      => $data_info->manager_updated_at,
                    'mobile'                  => $one_staff_info['mobile'] ?? '',
                    'personal_email'          => $one_staff_info['personal_email'] ?? '',
                    'manager_staff_id'        => $data_info->manager_staff_id,
                    'manager_staff_name'      => isset($staff_info_data[$data_info->manager_staff_id]['name']) ? '(' . $data_info->manager_staff_id . ')' . $staff_info_data[$data_info->manager_staff_id]['name'] : $data_info->manager_staff_id,
                    'manager_staff_job_name'  => $job_info_data['job_name'] ?? '',
                    'manager_progress'        => $data_info->manager_progress,
                    'manager_progress_text'   => isset(MaterialEnums::$manager_progress_list[$data_info->manager_progress]) ? static::$t[MaterialEnums::$manager_progress_list[$data_info->manager_progress]] . (($data_info->manager_progress == MaterialEnums::MANAGER_PROGRESS_NO_NEED_ASSET) ? ('(' . $data_info->manager_updated_id . ')') : '') : '',
                    'asset_department_remark' => $data_info->asset_department_remark,
                    'asset_department_status' => $data_info->asset_department_status,
                    'hire_type'               => $one_staff_info['hire_type'],
                    'hire_type_text'          => $hire_type_enum[$one_staff_info['hire_type']] ?? '',
                    'contract_company_id'     => $one_staff_info['contract_company_id'],
                    'contract_company_text'   => $company_list[$one_staff_info['contract_company_id']] ?? '',
                ];
                //获取附件
                $data['file'] = $data_info->getAttach();
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $data = [];
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-department-get-detail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部处理-编辑页统计
     * @param $params
     * @return array
     * @date 2023/3/2
     */
    public function getDetailCount($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询离职资产
            $data_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            if ($data_info) {
                //查询作废人信息
                $valid_staff_info = [];
                if ($data_info->no_valid_staff_id) {
                    $valid_staff = StaffService::getInstance()->searchStaff(['staff_id' => $data_info->no_valid_staff_id, 'limit' => 1]);
                    $valid_staff_info = $valid_staff['data'][0] ?? [];
                }
                $data = [
                    'id' => $data_info->id,
                    'unreturned_assets_number' => $data_info->unreturned_assets_number,
                    'damage_assets_number' => $data_info->damage_assets_number,
                    'all_asset_amount' => $data_info->all_asset_amount,
                    'all_deduct_amount' => $data_info->all_deduct_amount,
                    'deduct_amount' => $data_info->deduct_amount,
                    'return_amount' => $data_info->return_amount,
                    'loss_amount' => $data_info->loss_amount,
                    'no_valid_id' => $data_info->no_valid_staff_id,
                    'no_valid_name' => $data_info->no_valid_staff_name,
                    'no_valid_department' => $valid_staff_info['node_department_name'] ?? '',
                    'no_valid_job' => $valid_staff_info['job_name'] ?? '',
                    'no_valid_remark' => $data_info->no_valid_remark,
                    'no_valid_at' => $data_info->no_valid_at,
                    'file' => $data_info->getAttach()->toArray()
                ];
                $this->getLeaveDetailTotalInfo($data_info->toArray(), $data);
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-asset-department-get-detail-count-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 职后名下新增0条，拒收0条，待接收0条，请核实是否这部分资产是否归属离职员工，如果是，请及时添加进离职资产里，减少资产损失！
     * @param array $leave_asset_info 离职资产信息组
     * @param $data
     */
    private function getLeaveDetailTotalInfo($leave_asset_info, &$data)
    {
        //获取员工各转移状态下的转移记录数据
        $staff_transfer_status_count_list = MaterialAssetTransferLogModel::find([
            'columns' => 'COUNT(id) as num, status',
            'conditions' => 'to_staff_id = :staff_info_id: AND is_deleted = :is_deleted: AND (status = :wait_status: OR (status in({status:array}) AND finished_at > :finished_at:))',
            'bind' => [
                'staff_info_id' => $leave_asset_info['staff_info_id'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'wait_status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                'status' => [MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED, MaterialEnums::TRANSFER_LOG_STATUS_REJECTED],
                'finished_at' => $leave_asset_info['created_at']
            ],
            'group' => 'status'
        ])->toArray();
        $staff_transfer_status_count_list = array_column($staff_transfer_status_count_list, 'num', 'status');
        $data['staff_transfer_unreceived_num'] = $staff_transfer_status_count_list[MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED] ?? 0;//待接收
        $data['staff_transfer_received_num'] = $staff_transfer_status_count_list[MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED] ?? 0;//已接收
        $data['staff_transfer_rejected_num'] = $staff_transfer_status_count_list[MaterialEnums::TRANSFER_LOG_STATUS_REJECTED] ?? 0;//已拒收
    }

    /**
     * 主管处理-编辑页-列表
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getEditAssetsList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getEditAssetsCount($params, $locale);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id, barcode, asset_name_zh, asset_name_en, asset_name_local, asset_code, sn_code, old_asset_code, model, asset_status, asset_state, manager_remark, data_tag, purchase_price, net_value, deduct_amount, deduct_reason, asset_handling_status, use';
                $builder->columns($columns);
                $builder->from(MaterialLeaveAssetsDetailModel::class);
                //组合搜索条件
                $builder = $this->getEditAssetsCondition($builder, $params, $locale);
                $builder->limit($page_size, $offset);
                $builder->orderby('created_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleEditAssetsListItems($items, $locale);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理-编辑页-资产转移记录列表
     * @param $params
     * @return array
     * @date 2023/3/2
     */
    public function getEditTransferList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        $locale = static::$language;
        try {
            //查询离职资产任务
            $leave_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            if (!$leave_info) {
                throw new ValidationException(static::$t->_('leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //构建sql
            $builder = $this->modelsManager->createBuilder();
            $columns = 'l.id, l.status as transfer_status, l.transfer_at, l.finished_at, l.transfer_type, l.aor_no, l.remark, l.transfer_remark, l.updated_at,
            asset.bar_code as barcode, asset.name_zh, asset.name_en, asset.name_local, asset.asset_code, asset.sn_code, asset.old_asset_code, asset.model, asset.use,
             asset.purchase_price, asset.net_value, asset.staff_id, asset.staff_name';
            $builder->from(['l' => MaterialAssetTransferLogModel::class]);
            $builder->leftJoin(MaterialAssetsModel::class, 'asset.id = l.asset_id', 'asset');
            $builder->andWhere('l.to_staff_id = :to_staff_id:', ['to_staff_id' => $leave_info->staff_info_id]);
            $builder->andWhere('l.status = :status: or l.updated_at >= :updated_at: ', ['status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED, 'updated_at' => $leave_info->created_at]);
            //查总数
            $builder->columns('count(l.id) as count');
            $count = (int)$builder->getQuery()->getSingleResult()->count;
            //查列表数据
            $builder->columns($columns);
            $builder->orderby('l.updated_at desc');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            //处理返回值
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'name_' . MaterialClassifyEnums::$language_fields[$locale] : 'name_local';
            foreach ($items as &$item) {
                //是否标红
                $item['is_red'] = MaterialEnums::LEAVE_ASSETS_TRANSFER_IS_RED_NO;
                if ($item['updated_at'] >= $leave_info->created_at) {
                    $item['is_red'] = MaterialEnums::LEAVE_ASSETS_TRANSFER_IS_RED_YES;
                }
                //资产名称
                $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['name_en'];
                //使用方向
                $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
                //转移状态
                $item['transfer_status_text'] = isset(MaterialEnums::$transfer_log_status[$item['transfer_status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['transfer_status']]] : '';
                $item['transfer_type_text'] = isset(MaterialEnums::$transfer_type[$item['transfer_type']]) ? static::$t[MaterialEnums::$transfer_type[$item['transfer_type']]] : '';
                //转移/拒收时间
                $item['transfer_reject_at'] = '';
                if ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    $item['transfer_reject_at'] = $item['transfer_at'];
                } elseif ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_REJECTED) {
                    $item['transfer_reject_at'] = $item['finished_at'];
                }
                //转移/拒收原因
                $item['transfer_reject_remark'] = '';
                if ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    $item['transfer_reject_remark'] = $item['transfer_remark'];
                } elseif ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_REJECTED) {
                    $item['transfer_reject_remark'] = $item['remark'];
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-edit-transfer-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部处理-编辑页-列表-总数
     * @param $condition
     * @param $locale
     * @return int
     * @date 2023/3/2
     */
    public function getEditAssetsCount($condition, $locale)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialLeaveAssetsDetailModel::class);
        $builder->columns('count(id) as count');
        //组合搜索条件
        $builder = $this->getEditAssetsCondition($builder, $condition, $locale);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 主管处理-编辑页-列表-条件拼接
     * @param $builder
     * @param $condition
     * @param $locale
     * @return mixed
     * @date 2023/3/3
     */
    public function getEditAssetsCondition($builder, $condition, $locale)
    {
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : '';//barcode
        $asset_code = !empty($condition['asset_code']) ? $condition['asset_code'] : '';//资产编码
        $sn_code = !empty($condition['sn_code']) ? $condition['sn_code'] : '';//sn码
        $asset_name = !empty($condition['asset_name']) ? $condition['asset_name'] : '';//资产名称
        //固定条件
        $builder->andWhere('leave_assets_id = :leave_assets_id:', ['leave_assets_id' => $condition['id']]);
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('use = :use:', ['use' => $condition['use']]);
        if (!empty($barcode)) {
            $builder->andWhere('barcode = :barcode:', ['barcode' => $barcode]);
        }
        if (!empty($asset_code)) {
            $builder->andWhere('asset_code like :asset_code: or old_asset_code like :asset_code:', ['asset_code' => $asset_code . '%']);
        }
        if (!empty($sn_code)) {
            $builder->andWhere('sn_code like :sn_code:', ['sn_code' => $sn_code . '%']);
        }
        if (!empty($asset_name)) {
            $builder->andWhere('asset_name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :asset_name:', ['asset_name' => '%' . $asset_name . '%']);
        }

        return $builder;
    }

    /**
     * 主管处理-编辑页-列表-处理列表数据
     * @param $items
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    private function handleEditAssetsListItems($items, $locale)
    {
        if (empty($items)) {
            return [];
        }
        //获取图片
        $attachment = (new MaterialAttachmentModel())->getColumnArrUrl($items, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL);
        $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
        $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';

        // 17682获取关联的资产台账里现使用人信息
        $asset_codes = array_values(array_unique(array_filter(array_column($items, 'asset_code'))));
        $asset_list = [];
        if (!empty($asset_codes)) {
            $asset_list = AssetAccountService::getInstance()->getMaterialAssetListByAssetCodes($asset_codes);
            $asset_list = array_column($asset_list, null, 'asset_code');
        }
        foreach ($items as &$item) {
            $item['asset_status_text'] = isset(MaterialEnums::$asset_status[$item['asset_status']]) ? static::$t[MaterialEnums::$asset_status[$item['asset_status']]] : '';
            $item['asset_state_text'] = isset($leave_asset_state_list[$item['asset_state']]) ? static::$t[$leave_asset_state_list[$item['asset_state']]] : '';
            $item['asset_handling_status_text'] = isset(MaterialEnums::$asset_handling_status_list[$item['asset_handling_status']]) ? static::$t[MaterialEnums::$asset_handling_status_list[$item['asset_handling_status']]] : '';
            $item['images'] = $attachment[$item['id']] ?? [];
            $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['asset_name_en'];
            $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';

            $item['staff_id'] = !empty($item['asset_code']) && !empty($asset_list[$item['asset_code']]) ? $asset_list[$item['asset_code']]['staff_id'] : 0;
            $item['staff_name'] = !empty($item['asset_code']) && !empty($asset_list[$item['asset_code']]) ? $asset_list[$item['asset_code']]['staff_name'] : '';
        }
        return $items;
    }

    /**
     * 离职资产上级处理-暂存资产数据
     * @param $params
     * @param $user
     * @param bool $is_update
     * @return array
     * @date 2023/3/6
     */
    public function editSave($params, $user, $is_update = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //是否提交 详情为空也允许只保存主表数据
            //待处理改为已处理
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->is_valid != MaterialEnums::LEAVE_ASSET_IS_VALID_YES) {
                throw new ValidationException(static::$t->_('leave_asset_save_not_valid_yes'), ErrCode::$VALIDATE_ERROR);
            }
            if (!$is_update) {
                //编辑时验证,更新不用验证
                if (!in_array($leave_asset->asset_department_status, [MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO, MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING])) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_department_not_todo'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //离职 或者 待离职 通过
            if (!$this->checkStaffState($leave_asset->staff_info_id)) {
                throw new ValidationException(static::$t->_('excel_result_leave_asset_staff_info_state_error'), ErrCode::$VALIDATE_ERROR);
            }

            //记录金额日志
            $main_amount_log = $detail_amount_log = [];
            $main_amount_log['operation_type'] = MaterialEnums::MATERIAL_MAIN_AMOUNT_LOG_OPERATION_SAVE;
            $main_amount_log['leave_assets_id'] = $leave_asset->id;
            $main_amount_log['before_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            //逐行编辑
            $record_log = [];
            $now_date = date('Y-m-d H:i:s');
            if (isset($params['data']) && !empty($params['data'])) {
                foreach ($params['data'] as $key => $asset_info) {
                    //资产处理情况
                    $asset_handling_status = $asset_info['asset_handling_status'] ?? 0;
                    //扣费金额
                    $deduct_amount = $asset_info['deduct_amount'] ?? 0;
                    //检测已还完好，或者在他人名下的资产，扣费金额只能是0
                    if (!$this->checkDeductAmountIsZero($asset_handling_status, $deduct_amount)) {
                        throw new ValidationException(static::$t->_('asset_deduct_amount_just_zero'), ErrCode::$VALIDATE_ERROR);
                    }
                    //准备记录日志的临时变量
                    $tmp_log = [];
                    //查询本行数据
                    $db_asset_info = MaterialLeaveAssetsDetailModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $asset_info['detail_id']]
                    ]);
                    //验证
                    if (!$db_asset_info) {
                        throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
                    }
                    $before_detail = $db_asset_info->toArray();
                    //保存
                    $db_asset_info->deduct_amount = $deduct_amount;
                    $db_asset_info->deduct_reason = $asset_info['deduct_reason'] ?? '';
                    if (!empty($asset_handling_status)) {
                        //资产处理情况
                        $db_asset_info->asset_handling_status = $asset_handling_status;
                        //检测某barcode是否可选择"低值易耗不强制归还"资产处理情况
                        if (!$this->checkBarcodeIsLowValue($db_asset_info->asset_handling_status, $db_asset_info->barcode)) {
                            throw new ValidationException(static::$t->_('asset_low_value_cannot_select'), ErrCode::$VALIDATE_ERROR);
                        }
                        //资产完成情况
                        $db_asset_info->asset_department_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
                    }
                    $db_asset_info->updated_at = $now_date;
                    //如果是资产部自己添加的,可以编辑下列字段
                    $tmp_log['id'] = $db_asset_info->id;
                    if (in_array($db_asset_info->data_tag, [MaterialEnums::LEAVE_ASSET_DATA_TAG_ASSET_DEPARTMENT, MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER])) {
                        if (isset($asset_info['asset_code'])) {
                            $db_asset_info->asset_code = $asset_info['asset_code'];
                            $tmp_log['asset_code'] = $db_asset_info->asset_code;
                        }
                        if (isset($asset_info['sn_code'])) {
                            $db_asset_info->sn_code = $asset_info['sn_code'];
                            $tmp_log['sn_code'] = $db_asset_info->sn_code;
                        }
                        if (isset($asset_info['model'])) {
                            $db_asset_info->model = $asset_info['model'];
                            $tmp_log['model'] = $db_asset_info->model;
                        }
                        if (isset($asset_info['purchase_price'])) {
                            $db_asset_info->purchase_price = $asset_info['purchase_price'];
                            $tmp_log['purchase_price'] = $db_asset_info->purchase_price;
                        }
                        if (isset($asset_info['net_value'])) {
                            $db_asset_info->net_value = $asset_info['net_value'];
                            $tmp_log['net_value'] = $db_asset_info->net_value;
                        }
                        if (isset($asset_info['asset_status'])) {
                            $db_asset_info->asset_status = $asset_info['asset_status'];
                            $tmp_log['asset_status'] = $db_asset_info->asset_status;
                        }
                    }
                    $after_asset_info = $db_asset_info->toArray();
                    if ($db_asset_info->save() === false) {
                        throw new BusinessException('离职资产-资产部管理-保存失败，data = ' . json_encode($after_asset_info, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($db_asset_info), ErrCode::$LEAVE_ASSET_DEPARTMENT_DEAL_SAVE_ERROR);
                    }
                    //损坏资产总数,未归还资产总数
                    $detail_amount_log[] = $this->updateNumBySave($leave_asset, $before_detail, $after_asset_info);
                    //记录日志
                    $tmp_log['record_sub_type'] = MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT;
                    $tmp_log['barcode'] = $db_asset_info->barcode;
                    $tmp_log['deduct_amount'] = $db_asset_info->deduct_amount;
                    $tmp_log['deduct_reason'] = $db_asset_info->deduct_reason;
                    $tmp_log['asset_handling_status'] = $db_asset_info->asset_handling_status;
                    $record_log[] = $tmp_log;
                }
            }
            //是否提交
            $record_log_main = [];
            $record_log_main['record_sub_type'] = MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT;

            if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES) {
                if (!$is_update && $params['asset_department_status'] == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    //验证所有行都处理过
                    $not_finish_exist = MaterialLeaveAssetsDetailModel::findFirst([
                        'conditions' => 'leave_assets_id = :leave_assets_id: AND is_deleted = :is_deleted: AND asset_department_finish = :asset_department_finish:',
                        'bind' => ['leave_assets_id' => $params['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'asset_department_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO]
                    ]);
                    if ($not_finish_exist) {
                        throw new ValidationException(static::$t->_('leave_asset_submit_asset_department_not_finish'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            } else {
                //非提交的时候,资产处理状态不能保存为已处理
                if (!$is_update && $params['asset_department_status'] == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    throw new ValidationException(static::$t->_('leave_asset_asset_department_status_not_done'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //保存主表数据
            //底下统计字段
            $leave_asset->deduct_amount = !empty($params['deduct_amount']) ? $params['deduct_amount'] : 0;
            $leave_asset->return_amount = !empty($params['return_amount']) ? $params['return_amount'] : 0;
            $leave_asset->loss_amount = !empty($params['loss_amount']) ? $params['loss_amount'] : 0;
            $leave_asset->asset_department_remark = $params['asset_department_remark'] ?? '';
            //保存用户传来的资产处理状态, 提交时传,离开页面暂存时传, 高级搜索编辑不传,翻页不传,
            if (!empty($params['asset_department_status']) && !$is_update) {
                $leave_asset->asset_department_status = $params['asset_department_status'];
                //同步hcm
                $this->syncToHcm($leave_asset->asset_department_status, $leave_asset->staff_info_id, $user['id']);
                //操作记录
                $record_log_main['main_fields_asset_department_status'] = $leave_asset->asset_department_status;
            }
            //是否足额扣款
            if ($leave_asset->loss_amount > 0) {
                $leave_asset->is_full_amount = MaterialEnums::IS_FULL_AMOUNT_NO;
            } else {
                $leave_asset->is_full_amount = MaterialEnums::IS_FULL_AMOUNT_YES;
            }
            $this->setAssetDepartmentProgress($leave_asset);
            //资产部在主管处理之前，在OA端进行了处理，则需要把待处理变成“已处理”，后台标注为：资产部处理，显示：资产部（工号），挪到已处理tab页
            //资产部处理状态=（处理中、已处理），可修改上级处理状态=（待处理）&& 处理进度=（待处理、处理中），修改上级处理状态=（已处理），处理进度=（资产部已处理，标记上资产部工号加姓名）
            if (in_array($leave_asset->asset_department_status, [MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING, MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE]) && $leave_asset->manager_status == MaterialEnums::MANAGER_STATUS_TODO && in_array($leave_asset->manager_progress, [MaterialEnums::MANAGER_PROGRESS_NOT, MaterialEnums::MANAGER_PROGRESS_DOING])) {
                $leave_asset->manager_status = MaterialEnums::MANAGER_STATUS_DEAL;//已处理
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_NO_NEED_ASSET;//资产部已处理
                $leave_asset->manager_updated_id = $user['id'];//主管数据更新人id
                $leave_asset->manager_updated_name = $user['name'];//主管数据更新人名称
                $leave_asset->manager_updated_at = $now_date;//主管数据更新时间
            }
            $leave_asset->updated_id = $user['id'];
            $leave_asset->updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产-资产部处理-提交失败，data = ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($leave_asset), ErrCode::$LEAVE_ASSET_DEPARTMENT_DEAL_SUBMIT_ERROR);
            }
            //记录金额日志
            $main_amount_log['after_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            $this->addAmountLog($main_amount_log, $detail_amount_log, $user['id']);
            //记录日志,往里追加,主表字段加前缀main_fields_
            $record_log_main['main_fields_deduct_amount'] = $leave_asset->deduct_amount;
            $record_log_main['main_fields_return_amount'] = $leave_asset->return_amount;
            $record_log_main['main_fields_loss_amount'] = $leave_asset->loss_amount;
            $record_log_main['main_fields_asset_department_status'] = $leave_asset->asset_department_status;
            $record_log_main['main_fields_asset_department_remark'] = $leave_asset->asset_department_remark;

            $record_log[] = $record_log_main;
            //保存附件
            LeaveAssetsManagerService::getInstance()->saveImages($params['file'] ?? [], $leave_asset->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET, 'update');

            //增加操作记录
            if (!empty($record_log)) {
                $operator = [
                    'staff_info_id' => $user['id'],
                    'staff_info_name' => $user['name'],
                    'node_department_id' => $user['department_id'] ?? 0,
                    'node_department_name' => $user['department'] ?? '',
                    'job_title' => $user['job_title_id'] ?? 0,
                    'job_name' => $user['job_title'] ?? '',
                    'operation_at' => $now_date
                ];
                $log_type = $is_update ? MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_UPDATE : MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_EDIT;
                $this->addRecordLog($leave_asset->id, $operator, $log_type, $record_log, 0);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-asset-department-edit-save-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 检测资产处理情况 = 已还完好，或者在他人名下的资产，扣费金额只能是0
     * @param integer $asset_handling_status 资产处理情况
     * @param float $deduct_amount 扣费金额
     * @return bool
     */
    public function checkDeductAmountIsZero($asset_handling_status, $deduct_amount)
    {
        if (in_array($asset_handling_status, [MaterialEnums::ASSET_HANDLING_STATUS_GOOD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_BELONG_OTHER]) && bccomp($deduct_amount, 0 , 2) !== 0) {
            return false;
        }
        return true;
    }

    /**
     * 检测员工在职状态，仅可编辑离职的员工
     * 检测员工在职状态，仅可编辑离职\待离职的员工
     * @param int $staff_info_id 员工工号
     * @return bool
     */
    public function checkStaffState(int $staff_info_id)
    {
        $staff_info = (new HrStaffRepository())->getStaffById($staff_info_id);
        return (!empty($staff_info) && ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_LEAVE || ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES))) ? true : false;
    }

    /**
     * 检测某barcode是否可选择"低值易耗不强制归还"资产处理情况
     * @param integer $asset_handling_status 资产处理情况枚举值
     * @param string $barcode barcode
     * @return bool
     */
    public function checkBarcodeIsLowValue($asset_handling_status, $barcode)
    {
        //低值易耗不强制归还 && barcode非空
        if ($asset_handling_status == MaterialEnums::ASSET_HANDLING_STATUS_LOW_VALUE_NON_RETURN && !empty($barcode)) {
            $low_value_barcodes = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_low_value_barcodes');
            if (!empty($low_value_barcodes) && !in_array($barcode, $low_value_barcodes)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 离职资产上级处理-添加资产数据
     * @param $params
     * @param $user
     * @param bool $is_update
     * @return array
     * @date 2023/3/6
     */
    public function editAdd($params, $user, $is_update = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id']
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->is_valid != MaterialEnums::LEAVE_ASSET_IS_VALID_YES) {
                throw new ValidationException(static::$t->_('leave_asset_save_not_valid_yes'), ErrCode::$VALIDATE_ERROR);
            }
            if (!$is_update) {
                //编辑时验证,更新不用验证
                if (!in_array($leave_asset->asset_department_status, [MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO, MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING])) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_department_not_todo'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //检测已还完好，或者在他人名下的资产，扣费金额只能是0
            if (!$this->checkDeductAmountIsZero($params['asset_handling_status'], $params['deduct_amount'])) {
                throw new ValidationException(static::$t->_('asset_deduct_amount_just_zero'), ErrCode::$VALIDATE_ERROR);
            }

            //离职 或者 待离职 通过
            if (!$this->checkStaffState($leave_asset->staff_info_id)) {
                throw new ValidationException(static::$t->_('excel_result_leave_asset_staff_info_state_error'), ErrCode::$VALIDATE_ERROR);
            }

            //检测某barcode是否可选择"低值易耗不强制归还"资产处理情况
            if (!$this->checkBarcodeIsLowValue($params['asset_handling_status'], $params['barcode'])) {
                throw new ValidationException(static::$t->_('asset_low_value_cannot_select'), ErrCode::$VALIDATE_ERROR);
            }

            //barcode查询资产信息
            $barcode_info = MaterialSauModel::findFirst([
                'conditions' => 'barcode = :barcode: and is_deleted = :is_deleted:',
                'bind' => [
                    'barcode' => $params['barcode'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($barcode_info)) {
                throw new ValidationException(static::$t->_('leave_asset_add_barcode_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //记录金额日志
            $main_amount_log = [];
            $main_amount_log['operation_type'] = MaterialEnums::MATERIAL_MAIN_AMOUNT_LOG_OPERATION_ADD;
            $main_amount_log['leave_assets_id'] = $leave_asset->id;
            $main_amount_log['before_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            //添加
            $db_asset_info = new MaterialLeaveAssetsDetailModel();
            $insert_data = [
                'leave_assets_id' => $params['id'],
                'use' => $params['use'],
                'barcode' => $params['barcode'] ?? '',
                'asset_code' => $params['asset_code'] ?? '',
                'sn_code' => $params['sn_code'] ?? '',
                'asset_name_zh' => $barcode_info->name_zh,
                'asset_name_en' => $barcode_info->name_en,
                'asset_name_local' => $barcode_info->name_local,
                'model' => $params['model'] ?? '',
                'asset_status' => $params['asset_status'] ?? 0,
                'asset_state' => $params['asset_state'] ?? 0,
                'purchase_price' => $params['purchase_price'] ?? 0,
                'net_value' => $params['net_value'] ?? 0,
                'asset_handling_status' => $params['asset_handling_status'],
                'deduct_amount' => $params['deduct_amount'],
                'deduct_reason' => $params['deduct_reason'] ?? '',
                'data_tag' => MaterialEnums::LEAVE_ASSET_DATA_TAG_ASSET_DEPARTMENT,
                'manager_tag' => MaterialEnums::MANAGER_TAG_NO,
                'asset_department_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES,
                'created_at' => $now_date,
            ];
            if ($db_asset_info->i_create($insert_data) === false) {
                throw new BusinessException('资产部离职资产保存失败, data: ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
            }
            //添加图片
            if (!empty($params['images'])) {
                LeaveAssetsManagerService::getInstance()->saveImages($params['images'], $db_asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'add');
            }
            //主表字段数量变更
            $detail_amount_log[] = $this->updateNumByAdd($leave_asset, $db_asset_info->toArray());
            $this->setAssetDepartmentProgress($leave_asset);

            $leave_asset->updated_id = $user['id'];
            $leave_asset->updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('资产部离职资产保存失败 - 更新资产数量, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            //记录金额日志
            $main_amount_log['after_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            $this->addAmountLog($main_amount_log, $detail_amount_log, $user['id']);

            $record_log[] = array_merge(['id' => $db_asset_info->id, 'record_sub_type' => MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_ADD], $insert_data);
            //增加操作记录
            $operator = [
                'staff_info_id' => $user['id'],
                'staff_info_name' => $user['name'],
                'node_department_id' => $user['department_id'],
                'node_department_name' => $user['department'],
                'job_title' => $user['job_title_id'],
                'job_name' => $user['job_title'],
                'operation_at' => $now_date
            ];
            $log_type = $is_update ? MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_UPDATE : MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_EDIT;
            $this->addRecordLog($leave_asset->id, $operator, $log_type, $record_log, 0);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-edit-add-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 删除资产(自己添加的可以删除)
     * @param $params
     * @param $user
     * @param bool $is_update
     * @return array
     * @date 2023/3/27
     */
    public function editDelete($params, $user, $is_update = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //查询本行数据
            $db_asset_info = MaterialLeaveAssetsDetailModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['detail_id']]
            ]);
            //验证
            if (!$db_asset_info) {
                throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($db_asset_info->data_tag != MaterialEnums::LEAVE_ASSET_DATA_TAG_ASSET_DEPARTMENT) {
                throw new ValidationException(static::$t->_('leave_asset_delete_not_allow'), ErrCode::$VALIDATE_ERROR);
            }
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $db_asset_info->leave_assets_id
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->is_valid != MaterialEnums::LEAVE_ASSET_IS_VALID_YES) {
                throw new ValidationException(static::$t->_('leave_asset_save_not_valid_yes'), ErrCode::$VALIDATE_ERROR);
            }
            if (!$is_update) {
                //编辑时验证,更新不用验证
                if (!in_array($leave_asset->asset_department_status, [MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO, MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING])) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_department_not_todo'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //记录金额日志
            $main_amount_log = [];
            $main_amount_log['operation_type'] = MaterialEnums::MATERIAL_MAIN_AMOUNT_LOG_OPERATION_DELETE;
            $main_amount_log['leave_assets_id'] = $leave_asset->id;
            $main_amount_log['before_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            //删除
            $now_date = date('Y-m-d H:i:s');
            $db_asset_info->updated_at = $now_date;
            $db_asset_info->is_deleted = GlobalEnums::IS_DELETED;
            $arr_asset_info = $db_asset_info->toArray();
            if ($db_asset_info->save() === false) {
                throw new BusinessException('资产部-离职资产管理-删除失败，data = ' . json_encode($arr_asset_info, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
            }
            $detail_amount_log[] = $this->updateNumByDelete($leave_asset, $arr_asset_info);
            $this->setAssetDepartmentProgress($leave_asset);
            $leave_asset->updated_id = $user['id'];
            $leave_asset->updated_name = $user['name'];
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产删除失败 - 更新主数据失败, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            //记录金额日志
            $main_amount_log['after_all_deduct_amount'] = $leave_asset->all_deduct_amount;
            $this->addAmountLog($main_amount_log, $detail_amount_log, $user['id']);
            //增加操作记录
            $record_log[] = [
                'id' => $db_asset_info->id,
                'record_sub_type' => MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_DELETE,
                'barcode' => $db_asset_info->barcode,
                'asset_code' => $db_asset_info->asset_code,
                'asset_status' => $db_asset_info->asset_status,
                'sn_code' => $db_asset_info->sn_code,
                'model' => $db_asset_info->model,
                'purchase_price' => $db_asset_info->purchase_price,
                'net_value' => $db_asset_info->net_value,
                'deduct_amount' => $db_asset_info->deduct_amount,
                'deduct_reason' => $db_asset_info->deduct_reason,
                'asset_handling_status' => $db_asset_info->asset_handling_status,
            ];
            $operator = [
                'staff_info_id' => $user['id'],
                'staff_info_name' => $user['name'],
                'node_department_id' => $user['department_id'],
                'node_department_name' => $user['department'],
                'job_title' => $user['job_title_id'],
                'job_name' => $user['job_title'],
                'operation_at' => $now_date
            ];
            $log_type = $is_update ? MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_UPDATE : MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_EDIT;
            $this->addRecordLog($leave_asset->id, $operator, $log_type, $record_log, 0);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-edit-delete-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产-生成离职人员资产数据
     * @param $params
     * @return array
     * @date 2023/3/6
     */
    public function createLeaveAsset($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //参数验证
            Validation::validate($params, self::$validate_create_leave_asset);
            // 查询员工职位和部门
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $params['staff_info_id']
                ]
            ]);
            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('leave_asset_staff_not_exist'), ErrCode::$LEAVE_ASSET_LEAVE_STAFF_NOT_EXIST);
            }
            //查询当前此员工名下是否存在待处理的离职资产
            $leave_assets_data = MaterialLeaveAssetsModel::find([
                'conditions' => 'manager_staff_id = :manager_staff_id: and manager_status = :manager_status: and is_valid = :is_valid:',
                'bind' => [
                    'manager_staff_id' => $params['staff_info_id'],
                    'manager_status' => MaterialEnums::MANAGER_STATUS_TODO,
                    'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES
                ]
            ]);
            //操作人信息
            $operation_name = '';
            if ($params['operation_staff_id'] == StaffInfoEnums::SUPER_ADMIN_STAFF_ID) {
                $operation_name = StaffInfoEnums::SUPER_ADMIN_STAFF_NAME;
            } else {
                $operation_info = HrStaffInfoModel::findFirst([
                    'columns' => 'name',
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => [
                        'staff_info_id' => $params['operation_staff_id']
                    ]
                ]);
                $operation_name = $operation_info ? $operation_info->name : '';
            }
            //查询当前此员工名下待处理离职资产从待处理变成已作废
            if (!empty($leave_assets_data->toArray())) {
                foreach ($leave_assets_data as $one_leave_asset) {
                    $one_leave_asset->manager_updated_id     = $params['operation_staff_id'];
                    $one_leave_asset->manager_updated_name   = $operation_name;
                    $one_leave_asset->manager_updated_at     = date('Y-m-d H:i:s');
                    $one_leave_asset->manager_status         = MaterialEnums::MANAGER_STATUS_INVALID; //已作废
                    $one_leave_asset->manager_invalid_remark = MaterialEnums::MANAGER_STATUS_INVALID_REMARK_MANAGER_LEAVE; //标识: 主管离职
                    $one_leave_asset->manager_progress       = MaterialEnums::MANAGER_PROGRESS_NO_NEED_LEAVE; //无需处理(上级离职)
                    if ($one_leave_asset->save() === false) {
                        //不抛异常, 还要继续给当前员工生成离职资产
                        $this->logger->warning('离职资产-取消离职-作废名下待处理离职资产失败, 原因可能是: ' . get_data_object_error_msg($one_leave_asset) . '; 数据: ' . json_encode($one_leave_asset->toArray(), JSON_UNESCAPED_UNICODE));
                    }
                }
            }
            //下边单独一个事务, 上边的如果执行成功再次进入不会重复执行,所以不用放到事务里
            //查员工是否符合生成离职资产的条件
            if (!(in_array($staff_info->formal, [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE])
                && $staff_info->is_sub_staff == StaffInfoEnums::IS_SUB_STAFF_NO)) {
                //不符合条件不生成离职资产
                throw new ValidationException(static::$t->_('leave_asset_staff_not_conform'), ErrCode::$LEAVE_ASSET_CREATE_LEAVE_STAFF_NOT_CONFORM);
            }
            //查询当前是否存在待处理的离职资产
            $leave_assets_main = MaterialLeaveAssetsModel::find([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => [
                    'staff_info_id' => $params['staff_info_id']
                ]
            ]);
            $leave_assets_id = 0;
            $leave_assets_array = $leave_assets_main->toArray();
            if (!empty($leave_assets_array)) {
                $this->logger->info(' 离职资产-生成离职人员资产数据 修改之前的数据: ' . json_encode($leave_assets_array, JSON_UNESCAPED_UNICODE));
                //存在待处理的离职资产
                //同步此离职资产任务的员工在职状态
                $staff_state = $staff_info->state;
                if ($staff_info->state == StaffInfoEnums::STAFF_STATE_IN) {
                    if ($staff_info->wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                        $staff_state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                    }
                }

                foreach ($leave_assets_main as $one_leave_asset) {
                    $one_leave_asset->staff_state = $staff_state;
                    if ($one_leave_asset->save() === false) {
                        $this->logger->warning('create_leave_asset_update_staff_state_failed: staff_id=' . $staff_info->id . '; 可能的原因是:' . get_data_object_error_msg($one_leave_asset));
                    }
                    //是否存在有效的
                    if ($one_leave_asset->is_valid == MaterialEnums::LEAVE_ASSET_IS_VALID_YES) {
                        $leave_assets_id = $one_leave_asset->id;
                    }

                    $this->logger->info(' 离职资产-生成离职人员资产数据 修改之后的数据: ' . json_encode($one_leave_asset->toArray(), JSON_UNESCAPED_UNICODE));
                }
            }

            $db = $this->getDI()->get('db_oa');
            $db->begin();

            if (!empty($leave_assets_id)) {
                $leave_asset = MaterialLeaveAssetsModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $leave_assets_id],
                    'for_update' => true
                ]);
                if (empty($leave_asset)) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
                }

                $diff_assets = $this->addDiffAssetsDetail($leave_asset);
                if (!empty($diff_assets['all_asset_number'])) {
                    //TODO  此处可以共用添加的方法
                    $leave_asset->asset_department_status   = MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO;
                    $leave_asset->asset_department_progress = MaterialEnums::ASSET_DEPARTMENT_PROGRESS_DOING;
                    $leave_asset->unreturned_assets_number  = $leave_asset->unreturned_assets_number + $diff_assets['unreturned_assets_number'];
                    $leave_asset->all_asset_number          = $leave_asset->all_asset_number + $diff_assets['all_asset_number'];
                    $leave_asset->all_asset_amount          = bcadd($leave_asset->all_asset_amount, $diff_assets['all_asset_amount'], 2);
                    //保存asset_department_status时 资产处理状态变更 同步给hcm
                    $this->syncToHcm($leave_asset->asset_department_status, $leave_asset->staff_info_id, $params['operation_staff_id']);
                    if ($leave_asset->save() === false) {
                        $this->logger->warning('create_leave_asset_update_staff_state_failed: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能的原因是:' . get_data_object_error_msg($leave_asset));
                    }
                } else if ($leave_asset->asset_department_status == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    //21155 若OA里存在有效的已处理的数据,且员工名下没有新增的资产需要处理，OA这边不会生成一条新的离职资产，需要将资产处理状态同步给HCM(本次新增的逻辑）
                    $this->syncToHcm($leave_asset->asset_department_status, $leave_asset->staff_info_id, $params['operation_staff_id']);
                }
            } else {

                //查询员工职位信息
                $job_info = HrJobTitleModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => [
                        'id' => $staff_info->job_title
                    ]
                ]);
                $job_info = $job_info ? $job_info->toArray() : [];
                //查询员工部门信息
                $department_info = SysDepartmentModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => [
                        'id' => $staff_info->node_department_id
                    ]
                ]);
                $department_info = $department_info ? $department_info->toArray() : [];
                //查询网点信息
                $staff_store_name = '';
                if ($staff_info->sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                    $staff_store_name = Enums::PAYMENT_HEADER_STORE_NAME;
                } elseif (!empty($staff_info->sys_store_id)) {
                    $store_info       = SysStoreModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind'       => [
                            'id' => $staff_info->sys_store_id
                        ]
                    ]);
                    $store_info       = $store_info ? $store_info->toArray() : [];
                    $staff_store_name = $store_info['name'] ?? '';
                }

                $staff_info_arr                         = $staff_info->toArray();
                $staff_info_arr['job_name']             = $job_info['job_name'] ?? '';
                $staff_info_arr['node_department_name'] = $department_info['name'] ?? '';
                $staff_info_arr['company_id']           = $department_info['company_id'] ?? 0;
                $staff_info_arr['company_name']         = $department_info['company_name'] ?? '';
                $staff_info_arr['sys_store_name']       = $staff_store_name;
                $staff_info_arr['last_work_date']       = !empty($params['last_work_date']) ? $params['last_work_date'] : '';
                $staff_info_arr['staff_resign_id']      = $params['staff_resign_id'] ?? 0;
                //以传来的参数为准
                $staff_info_arr['leave_date'] = $params['leave_date'] ?? null;
                //匹配上级处理规则,找到
                $manager_info = LeaveAssetsSetService::getInstance()->getManagerSetByStaff($staff_info->staff_info_id, $staff_info->job_title, $staff_info->node_department_id, $params['work_handover']);
                //固化资产到离职资产表
                $leave_assets_data = $this->materialAssetAddToLeaveAsset($staff_info_arr, $manager_info, $params['operation_staff_id'], $params['source']);
                // 发送消息 如果有上级且主管待处理资产大于0才发(主管处理使用中的,非使用中的不算他的数量)
                if (!empty($manager_info['manager_id'])) {
                    LeaveAssetsMessageService::getInstance()->sendLeaveAssetsMessage($manager_info['manager_id'], $leave_assets_data['leave_assets_id']);
                }
                // 给员工发短信或邮件 , 匹配到相应的规则 且有相应的 个人邮箱或个人手机 才发
                //匹配发消息规则
                $remind_type = LeaveAssetsSetService::getInstance()->getRemindSetByStaff($staff_info->job_title, $staff_info->node_department_id, $staff_info->staff_info_id);
                if (!empty($remind_type)) {
                    $this->logger->info('离职创建资产: 发送短信和邮件 remind_type = ' . json_encode($remind_type, JSON_UNESCAPED_UNICODE) . ' staff_info = ' . json_encode($staff_info_arr, JSON_UNESCAPED_UNICODE));
                    foreach ($remind_type as $one_type) {
                        if ($one_type == MaterialEnums::LEAVE_ASSET_SET_REMIND_TYPE_MESSAGE && !empty($staff_info->mobile)) {
                            LeaveAssetsMessageService::getInstance()->sendPhoneSms($staff_info->staff_info_id, $staff_info->name, $staff_info->mobile, $leave_assets_data['all_asset_amount'], $staff_info_arr);
                        } elseif ($one_type == MaterialEnums::LEAVE_ASSET_SET_REMIND_TYPE_EMAIL && !empty($staff_info->personal_email)) {
                            LeaveAssetsMessageService::getInstance()->sendEmail($staff_info->staff_info_id, $staff_info->name, $staff_info->personal_email, $leave_assets_data['all_asset_amount'], $staff_info_arr);
                        }
                    }
                }

            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('create_leave_asset-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 员工名下资产固化到离职资产表
     * @param array $staff_info 离职员工信息
     * @param array $manager_info 上级处理人信息
     * @param int $create_id 离职操作人
     * @param int $leave_source 离职来源
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/3/21
     */
    public function materialAssetAddToLeaveAsset($staff_info, $manager_info, $create_id, $leave_source)
    {
        $return_data = [
            'leave_assets_id' => 0,
            'detail_count' => 0, //主管需要处理的资产数量,用于判断是否需要给主管发by站内信
            'all_asset_amount' => 0, //员工名下资产总价值(固化到离职资产的,本位币) 用来发短信
        ];
        // 查询员工名下资产 写入离职资产
        $leave_asset_status_set = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_status');
        $staff_assets_data = MaterialAssetsModel::find([
            'conditions' => 'staff_id = :staff_info_id: and status in ({status:array}) and is_deleted = :is_deleted:',
            'bind' => [
                'staff_info_id' => $staff_info['staff_info_id'],
                'status' => $leave_asset_status_set,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ])->toArray();
        // 处理待离职
        if ($staff_info['state'] == StaffInfoEnums::STAFF_STATE_IN && $staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
            $staff_info['state'] = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
        }
        // 生成资产详情
        $now_date = date('Y-m-d H:i:s');
        //主管处理的个人资产和公共资产数量
        $personal_assets_number = $public_assets_number = 0;
        //资产总价值(资产部看,统计所有资产)
        $all_asset_amount = 0;
        //资产总数
        $all_asset_number = 0;
        $leave_assets_detail_data = [];
        //20961 离职资产-需上级处理的资产状态3使用中，15闲置（在网点）；在这个组里的资产都需上级处理
        $material_leave_manager_asset_status = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_manager_asset_status');
        foreach ($staff_assets_data as $staff_assets) {
            $tmp_detail = [];
            $tmp_detail['asset_id'] = $staff_assets['id'];
            $tmp_detail['asset_name_zh'] = $staff_assets['name_zh'];
            $tmp_detail['asset_name_en'] = $staff_assets['name_en'];
            $tmp_detail['asset_name_local'] = $staff_assets['name_local'];
            $tmp_detail['barcode'] = $staff_assets['bar_code'];
            $tmp_detail['asset_code'] = $staff_assets['asset_code'];
            $tmp_detail['old_asset_code'] = $staff_assets['old_asset_code'];
            $tmp_detail['sn_code'] = $staff_assets['sn_code'];
            $tmp_detail['model'] = $staff_assets['model'];
            $tmp_detail['use'] = $staff_assets['use'];
            $tmp_detail['asset_status'] = $staff_assets['status'];
            //只有使用中的让主管处理,20961需求新增闲置（在网点）
            if (in_array($staff_assets['status'], $material_leave_manager_asset_status)) {
                $tmp_detail['manager_tag'] = MaterialEnums::MANAGER_TAG_YES;
            } else {
                $tmp_detail['manager_tag'] = MaterialEnums::MANAGER_TAG_NO;
            }
            $tmp_detail['data_tag'] = MaterialEnums::LEAVE_ASSET_DATA_TAG_SELF;
            $tmp_detail['created_at'] = $now_date;
            //统计主管的待处理数量,分个人和公共
            if ($tmp_detail['manager_tag'] == MaterialEnums::MANAGER_TAG_YES) {
                if ($tmp_detail['use'] == MaterialEnums::USE_PERSONAL) {
                    $personal_assets_number += 1;
                } elseif ($tmp_detail['use'] == MaterialEnums::USE_PUBLIC) {
                    $public_assets_number += 1;
                }
            }
            //采购金额汇总
            $purchase_amount = $this->amountExchange($staff_assets['currency'], $staff_assets['purchase_price']);
            $net_value = $this->amountExchange($staff_assets['currency'], $staff_assets['net_value']);
            $tmp_detail['purchase_price'] = $purchase_amount;
            $tmp_detail['net_value'] = $net_value;
            //资产总价值
            $all_asset_amount = bcadd($all_asset_amount, $purchase_amount, 2);
            //资产总数量
            $all_asset_number += 1;
            $leave_assets_detail_data[] = $tmp_detail;
        }
        // 生成单号
        $no = static::genSerialNo(MaterialEnums::LEAVE_ASSET_NO_PREFIX, MaterialEnums::LEAVE_ASSET_NO_REDIS_KEY, MaterialEnums::LEAVE_ASSET_NO_LENGTH);
        // 生成离职资产待处理任务
        $obj_leave_assets = new MaterialLeaveAssetsModel();
        $leave_assets_data = [];
        $leave_assets_data['no'] = $no;
        $leave_assets_data['is_valid'] = MaterialEnums::LEAVE_ASSET_IS_VALID_YES;
        $leave_assets_data['staff_info_id'] = $staff_info['staff_info_id'];
        $leave_assets_data['staff_name'] = $staff_info['name'];
        $leave_assets_data['staff_name_en'] = $staff_info['name_en'];
        $leave_assets_data['staff_state'] = $staff_info['state'];
        $leave_assets_data['last_work_date'] = $staff_info['last_work_date'];
        $leave_assets_data['leave_date'] = $staff_info['leave_date'];
        $leave_assets_data['job_title'] = $staff_info['job_title'];
        $leave_assets_data['job_name'] = $staff_info['job_name'];
        $leave_assets_data['node_department_id'] = $staff_info['node_department_id'];
        $leave_assets_data['node_department_name'] = $staff_info['node_department_name'];
        $leave_assets_data['sys_store_id'] = $staff_info['sys_store_id'];
        $leave_assets_data['sys_store_name'] = $staff_info['sys_store_name'];
        $leave_assets_data['company_id'] = $staff_info['company_id'];
        $leave_assets_data['company_name'] = $staff_info['company_name'];
        $leave_assets_data['working_country'] = $staff_info['working_country'];
        $leave_assets_data['staff_resign_id'] = $staff_info['staff_resign_id'] ?? 0;
        $leave_assets_data['wait_leave_state'] = $staff_info['wait_leave_state'] ?? null;
        $leave_assets_data['personal_assets_number'] = $personal_assets_number;
        $leave_assets_data['public_assets_number'] = $public_assets_number;
        //当前产品逻辑: 资产使用情况=未还或空时,归类为未还数量, 当前生成离职资产时资产使用状态不维护,默认空,所以未还=总数量
        //如果以后这个逻辑有变化,这里也需要调整
        $leave_assets_data['unreturned_assets_number'] = $all_asset_number;
        $leave_assets_data['all_asset_number'] = $all_asset_number;
        $leave_assets_data['all_asset_amount'] = $all_asset_amount;
        $leave_assets_data['manager_staff_id'] = !empty($manager_info['manager_id']) ? $manager_info['manager_id'] : 0;
        $leave_assets_data['manager_set_rule'] = !empty($manager_info['rule']) ? $manager_info['rule'] : 0;
        $leave_assets_data['manager_status'] = !empty($manager_info['manager_id']) ? MaterialEnums::MANAGER_STATUS_TODO : MaterialEnums::MANAGER_STATUS_DEAL;//没有上级时-已处理
        $leave_assets_data['manager_progress'] = !empty($manager_info['manager_id']) ? MaterialEnums::MANAGER_PROGRESS_NOT : MaterialEnums::MANAGER_PROGRESS_NO_NEED;
        $leave_assets_data['asset_department_status'] = MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO;
        $leave_assets_data['asset_department_progress'] = MaterialEnums::ASSET_DEPARTMENT_PROGRESS_NOT;
        $leave_assets_data['created_id'] = $create_id;
        $leave_assets_data['created_at'] = $now_date;
        //添加离职资产
        $main_bool = $obj_leave_assets->i_create($leave_assets_data);
        if ($main_bool === false) {
            throw new BusinessException('离职资产主记录创建失败, 原因可能是: ' . get_data_object_error_msg($obj_leave_assets) . '; 数据: ' . json_encode($leave_assets_data, JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_CREATE_MAIN_ERROR);
        }
        //添加离职资产详情
        if (!empty($leave_assets_detail_data)) {
            $obj_leave_assets_detail = new MaterialLeaveAssetsDetailModel();
            foreach ($leave_assets_detail_data as &$detail_one) {
                $detail_one['leave_assets_id'] = $obj_leave_assets->id;
            }
            $detail_bool = $obj_leave_assets_detail->batch_insert($leave_assets_detail_data);
            if ($detail_bool === false) {
                throw new BusinessException('离职资产详情记录创建失败, 原因可能是: ' . get_data_object_error_msg($obj_leave_assets_detail) . '; 数据: ' . json_encode($leave_assets_detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_CREATE_DETAIL_ERROR);
            }
        }
        $create_staff_info = StaffService::getInstance()->searchStaff(['staff_id' => $create_id]);
        $create_staff_info = $create_staff_info['data'][0] ?? [];
        $operator = [
            'staff_info_id' => $create_id,
            'staff_info_name' => $create_staff_info['staff_name'] ?? '',
            'node_department_id' => !empty($create_staff_info['node_department_id']) ? $create_staff_info['node_department_id'] : 0,
            'node_department_name' => !empty($create_staff_info['node_department_name']) ? $create_staff_info['node_department_name'] : '',
            'job_title' => !empty($create_staff_info['job_id']) ? $create_staff_info['job_id'] : '',
            'job_name' => !empty($create_staff_info['job_name']) ? $create_staff_info['job_name'] : '',
            'operation_at' => $now_date
        ];

        $this->syncToHcm(MaterialEnums::ASSET_DEPARTMENT_STATUS_TODO, $staff_info['staff_info_id'], $create_id);

        $this->addRecordLog($obj_leave_assets->id, $operator, MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_ADD, [], $leave_source);
        $return_data['leave_assets_id'] = $obj_leave_assets->id;
        $return_data['detail_count'] = $personal_assets_number + $public_assets_number;
        $return_data['all_asset_amount'] = $all_asset_amount;
        return $return_data;
    }

    /**
     * 离职资产-取消离职
     * @param $params
     * @return array
     * @date 2023/3/6
     */
    public function cancelLeaveAsset($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //参数验证
            Validation::validate($params, self::$validate_cancel_leave_asset);
            // 查询当前员工的离职资产数据
            $leave_assets_main = MaterialLeaveAssetsModel::find([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $params['staff_info_id']
                ]
            ]);
            if (!$leave_assets_main->toArray()) {
                throw new ValidationException(static::$t->_('leave_asset_not_exist'), ErrCode::$LEAVE_ASSET_LEAVE_STAFF_NOT_EXIST);
            }
            //获取在职状态
            $staff_state = StaffService::getInstance()->getStaffState($params['staff_info_id']);
            //处理离职资产
            $now_date = date('Y-m-d H:i:s');
            foreach ($leave_assets_main as $one_leave_asset) {
                if ($one_leave_asset->is_valid = MaterialEnums::LEAVE_ASSET_IS_VALID_YES) {
                    //有效的改为无效
                    // 更新主管更新人
                    $one_leave_asset->staff_state = $staff_state;
                    $one_leave_asset->updated_id = $params['update_id'];
                    $one_leave_asset->updated_at = $now_date;
                    $one_leave_asset->no_valid_staff_id = $params['update_id'];
                    $one_leave_asset->no_valid_staff_name = $params['update_id'] == StaffInfoEnums::SUPER_ADMIN_STAFF_ID ? StaffInfoEnums::SUPER_ADMIN_STAFF_NAME : $params['update_name'];
                    $one_leave_asset->no_valid_at = $now_date;
                    $one_leave_asset->no_valid_remark = $params['update_remark'];
                    $one_leave_asset->is_valid = MaterialEnums::LEAVE_ASSET_IS_VALID_NO;
                    //是否从已处理/待处理变成已作废
                    if ($one_leave_asset->manager_status == MaterialEnums::MANAGER_STATUS_TODO) {
                        $one_leave_asset->manager_updated_id = $params['update_id'];
                        $one_leave_asset->manager_updated_name = $params['update_id'] == StaffInfoEnums::SUPER_ADMIN_STAFF_ID ? StaffInfoEnums::SUPER_ADMIN_STAFF_NAME : $params['update_name'];
                        $one_leave_asset->manager_updated_at = $now_date;
                        $one_leave_asset->manager_status = MaterialEnums::MANAGER_STATUS_INVALID;
                        $one_leave_asset->manager_invalid_remark = MaterialEnums::MANAGER_STATUS_INVALID_REMARK_CANCEL_LEAVE; //标识: 主管离职
                        $one_leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_NO_NEED; //无需处理
                    }
                    if ($one_leave_asset->save() === false) {
                        throw new BusinessException('离职资产-取消离职-更新失败, 原因可能是: ' . get_data_object_error_msg($one_leave_asset) . '; 数据: ' . json_encode($one_leave_asset->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_CANCEL_SAVE_ERROR);
                    }
                    //写入日志
                    $update_staff_info = StaffService::getInstance()->searchStaff(['staff_id' => $params['update_id']]);
                    $update_staff_info = $update_staff_info['data'][0] ?? [];
                    $operator = [
                        'staff_info_id' => $params['update_id'],
                        'staff_info_name' => $params['update_id'] == StaffInfoEnums::SUPER_ADMIN_STAFF_ID ? StaffInfoEnums::SUPER_ADMIN_STAFF_NAME : $params['update_name'],
                        'node_department_id' => !empty($update_staff_info['node_department_id']) ? $update_staff_info['node_department_id'] : 0,
                        'node_department_name' => !empty($update_staff_info['node_department_name']) ? $update_staff_info['node_department_name'] : '',
                        'job_title' => !empty($update_staff_info['job_id']) ? $update_staff_info['job_id'] : '',
                        'job_name' => !empty($update_staff_info['job_name']) ? $update_staff_info['job_name'] : '',
                        'operation_at' => $now_date
                    ];
                    $this->addRecordLog($one_leave_asset->id, $operator, MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_CANCEL, []);
                } else {
                    //无效的仅同步在职状态
                    $one_leave_asset->staff_state = $staff_state;
                    if ($one_leave_asset->save() === false) {
                        $this->logger->warning('cancel_leave_asset_update_staff_state_failed: leave_assets_id=' . $one_leave_asset->id . '; 可能的原因是:' . get_data_object_error_msg($one_leave_asset));

                    }
                }
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('cancel_leave_asset-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产-上级变更
     * @param $params
     * @return array
     * @date 2023/3/6
     */
    public function leaveAssetManagerChange($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //参数验证
            Validation::validate($params, self::$validate_leave_asset_job_change);
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            // 查询当前员工待处理的离职资产数据
            $leave_assets = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and manager_staff_id = :manager_staff_id:
                and is_valid = :is_valid: and manager_status = :manager_status:',
                'bind' => [
                    'staff_info_id' => $params['staff_info_id'],
                    'manager_staff_id' => $params['before_manager_id'],
                    'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES,
                    'manager_status' => MaterialEnums::MANAGER_STATUS_TODO,
                ],
                'for_update' => true
            ]);
            if (!$leave_assets) {
                //不用翻译, 这里和系统做交互的
                throw new ValidationException('leaveAssetJobChange 没有需要处理的数据', ErrCode::$LEAVE_ASSET_MANAGER_CHANGE_NOT_EXIST);
            }
            if ($leave_assets->manager_set_rule != MaterialEnums::LEAVE_ASSET_SET_RULE_SUPERIOR) {
                $this->logger->info('leaveAssetJobChange 员工更换上级,离职资产处理人非根据[直线上级分配],无需变更状态; params=' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; data=' . json_encode($leave_assets->toArray(), JSON_UNESCAPED_UNICODE));
                throw new ValidationException('leaveAssetJobChange 员工更换上级,离职资产处理人非根据[直线上级分配],无需变更状态', ErrCode::$LEAVE_ASSET_MANAGER_CHANGE_NOT_SET_RULE_SUPERIOR);
            }
            // 更新作废状态
            $leave_assets->manager_updated_id = $params['update_id'] ?? 0;
            $leave_assets->manager_updated_name = $params['update_name'] ?? '';
            $leave_assets->manager_updated_at = date('Y-m-d H:i:s');
            $leave_assets->manager_status = MaterialEnums::MANAGER_STATUS_INVALID;
            $leave_assets->manager_invalid_remark = MaterialEnums::MANAGER_STATUS_INVALID_REMARK_MANAGER_CHANGE; //标识: 主管上级变动
            $leave_assets->manager_progress = MaterialEnums::MANAGER_PROGRESS_NO_NEED_CHANGED; //无需处理(换上级)
            if ($leave_assets->save() === false) {
                throw new BusinessException('leaveAssetJobChange 员工更换上级-保存失败，params=' . json_encode($params, JSON_UNESCAPED_UNICODE)
                    . '; data=' . json_encode($leave_assets->toArray(), JSON_UNESCAPED_UNICODE)
                    . '可能的原因是:' . get_data_object_error_msg($leave_assets), ErrCode::$LEAVE_ASSET_MANAGER_CHANGE_SAVE_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-change-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取员工名下资产数据-barcode维度
     * 运算过程全部保留3位小数,结果保留2位小数
     * @date 2023/3/15
     * @param $params
     * @return array
     */
    public function getAssetsByStaffId($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => '0',
                'per_page' => '0',
                'total_count' => '0',
            ],
            'total_data' => [
                'all_num' => '0',
                'all_price' => '0'
            ],
        ];
        try {
            Validation::validate($params, self::$validate_get_assets_by_staffId);
            //查询barcode+currency维度的统计数据,币种不同的转换本国货币 , 泰国barcode维度最大数据 单人90+barcode
            $leave_asset_status_set = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_status');
            $material_data = MaterialAssetsModel::find([
                'columns' => 'count(id) as barcode_num, id, bar_code, sum(purchase_price) as barcode_price, currency, model, name_zh, name_en, name_local',
                'conditions' => 'staff_id = :staff_id: and status in ({status:array}) and is_deleted = :is_deleted:',
                'bind' => [
                    'staff_id' => $params['staff_id'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'status' => $leave_asset_status_set
                ],
                'group' => 'bar_code, currency',
                'order' => 'id ASC'
            ]);
            //计算barcode的总金额和总数量
            $barcode_count_data = []; //按barcode维度统计
            $all_count_data = []; //统计全部的总数量和总金额
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
            //名称key
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'name_' . MaterialClassifyEnums::$language_fields[$locale] : 'name_local';
            foreach ($material_data as $value) {
                if (empty($value->currency)) {
                    throw new ValidationException(static::$t->_('assets_currency_is_empty'), ErrCode::$VALIDATE_ERROR);
                }
                if ($value->currency != $default_currency['code']) {
                    //转换成本国币种
                    $amount = EnumsService::getInstance()->currencyAmountConversion((string)$value->currency, (string)$value->barcode_price, 3);
                    $amount = round($amount, 2);
                } else {
                    $amount = $value->barcode_price;
                }
                if (!isset($barcode_count_data[$value->bar_code])) {
                    $barcode_count_data[$value->bar_code]['model'] = $value->model;
                    $barcode_count_data[$value->bar_code]['name'] = !empty($value->$name_key) ? $value->$name_key : $value->name_en;
                    $barcode_count_data[$value->bar_code]['barcode'] = $value->bar_code;
                    $barcode_count_data[$value->bar_code]['symbol_simple'] = $default_currency['symbol_simple'];
                    $barcode_count_data[$value->bar_code]['barcode_price'] = $amount;
                    $barcode_count_data[$value->bar_code]['barcode_num'] = $value->barcode_num;
                } else {
                    $barcode_count_data[$value->bar_code]['barcode_price'] = bcadd($barcode_count_data[$value->bar_code]['barcode_price'], $amount, 2);
                    $barcode_count_data[$value->bar_code]['barcode_num'] = (string)($barcode_count_data[$value->bar_code]['barcode_num'] + $value->barcode_num);
                }

                $all_count_data['all_num'] = !isset($all_count_data['all_num']) ? $value->barcode_num : $all_count_data['all_num'] + $value->barcode_num;
                $all_count_data['all_price'] = !isset($all_count_data['all_price']) ? $amount : bcadd($all_count_data['all_price'], $amount, 2);
            }
            $barcode_count_data = array_values($barcode_count_data);
            //分页
            //分页总数据量
            $barcode_list_count = count($barcode_count_data);
            $page_size = $params['page_size'];
            $page_num = $params['page_num'];
            //分页参数
            $data['pagination']['current_page'] = (string)$page_num;
            $data['pagination']['per_page'] = (string)$page_size;
            $data['pagination']['total_count'] = (string)$barcode_list_count;
            //统计数据
            $data['total_data']['all_num'] = (string)($all_count_data['all_num'] ?? '0');
            $data['total_data']['all_price'] = (string)($all_count_data['all_price'] ?? '0');
            $data['total_data']['symbol_simple'] = $default_currency['symbol_simple'];

            $offset = $page_size * ($page_num - 1);
            if ($page_size < $barcode_list_count) {
                $barcode_count_data = array_slice($barcode_count_data, $offset, $page_size);
            }
            $data['items'] = $barcode_count_data ?? [];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('by-getAssetsByStaffId-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取员工名下资产数据-资产维度
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getAssetsDetailByStaffId($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => '0',
                'per_page' => '0',
                'total_count' => '0',
            ],
        ];
        try {
            Validation::validate($params, self::$validate_get_assets_detail_by_staffId);
            $leave_asset_status_set = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_status');
            $page_size = empty($params['page_size']) ? MaterialEnums::PAGE_SIZE : $params['page_size'];
            $page_num = empty($params['page_num']) ? MaterialEnums::PAGE_NUM : $params['page_num'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = (string)$page_num;
            $data['pagination']['per_page'] = (string)$page_size;
            $source = $params['source'] ?? 0;//来源0离职须知，1资产协议

            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialAssetsModel::class);
            //数据总量
            $builder->columns('count(id) as count');
            //搜索条件
            $builder->andWhere('staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
            $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->inWhere('status', $leave_asset_status_set);
            if (!empty($params['barcode'])) {
                $builder->andWhere('bar_code = :barcode:', ['barcode' => $params['barcode']]);
            }
            $count = $builder->getQuery()->getSingleResult()->count;
            $items = [];
            if ($count > 0) {
                //列表数据
                $builder->columns('id, bar_code, name_zh, name_en, name_local, asset_code, sn_code, use, purchase_price, net_value, model, currency, status');
                if ($source == 0) {
                    //离职须知需要分页
                    $builder->limit($page_size, $offset);
                }
                $builder->orderby('created_at desc, id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'name_' . MaterialClassifyEnums::$language_fields[$locale] : 'name_local';
                //当前国家币种
                $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
                $lm_service = LeaveAssetsManagerService::getInstance();
                foreach ($items as &$item) {
                    $item['status_text'] = isset(MaterialEnums::$asset_status[$item['status']]) ? $lm_service->translateTemp(MaterialEnums::$asset_status[$item['status']]) : '';
                    $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? $lm_service->translateTemp(MaterialEnums::$use[$item['use']]) : '';
                    $item['name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['name_en'];
                    if (empty($item['currency'])) {
                        throw new ValidationException(static::$t->_('assets_currency_is_empty'), ErrCode::$VALIDATE_ERROR);
                    }
                    if ($item['currency'] != $default_currency['code']) {
                        //[采购价]转换成本国币种
                        $amount = EnumsService::getInstance()->currencyAmountConversion((string)$item['currency'], (string)$item['purchase_price'], 3);
                        $item['purchase_price'] = (string)round($amount, 2);

                        //[净值]转换成本国币种
                        $net_value = EnumsService::getInstance()->currencyAmountConversion((string)$item['currency'], (string)$item['net_value'], 3);
                        $item['net_value'] = (string)round($net_value, 2);
                    }
                    $item['purchase_price'] = number_format($item['purchase_price'], 2);
                    $item['net_value'] = number_format($item['net_value'], 2);
                    $item['all_price'] = $item['purchase_price'];
                    $item['symbol_simple'] = $default_currency['symbol_simple'];
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('by-getAssetsDetailByStaffId-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取员工名下资产数据-hcm详情
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    public function getAssetsDetailHcm($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'assets_list' => [],
            'file' => [],
            'asset_department_remark' => ''
        ];
        try {
            //查询离职资产表
            $main_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and is_valid = :is_valid:',
                'bind' => [
                    'staff_info_id' => $params['staff_id'],
                    'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES
                ]
            ]);
            if (!$main_info) {
                throw new ValidationException(static::$t->_('leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //查询离职资产详情
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['leave' => MaterialLeaveAssetsDetailModel::class]);
            $builder->columns('leave.id, leave.use, leave.barcode, leave.asset_name_zh, leave.asset_name_en, leave.asset_name_local, leave.asset_code,
            leave.sn_code, leave.old_asset_code, leave.model, leave.asset_status, leave.asset_state, leave.manager_remark, leave.purchase_price,
            leave.deduct_amount, leave.deduct_reason, leave.asset_handling_status, asset.receipted_at');
            $builder->leftjoin(MaterialAssetsModel::class, 'leave.asset_id = asset.id', 'asset');
            $builder->andWhere('leave.leave_assets_id = :leave_assets_id:', ['leave_assets_id' => $main_info->id]);
            $builder->andWhere('leave.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->inWhere('leave.asset_handling_status', [MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN]);
            $leave_detail = $builder->getQuery()->execute()->toArray();
            $items = $this->handleAssetsDetailHcmItems($leave_detail, $locale);
            $data['assets_list'] = $items ?? [];
            $data['file'] = $main_info->getAttach(['columns' => 'bucket_name, object_key, file_name, object_url'])->toArray();
            $data['asset_department_remark'] = $main_info->asset_department_remark;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-assets-detail-hcm-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 批量获取各员工的资产扣费总额
     * 18447需求，用于向HCM端【Flash员工离职管理】提供资产扣费总额
     * https://flashexpress.feishu.cn/docx/YH4SdAzduo7WnDx3dyRcPYNHnFb
     * @param array $staff_ids 员工id组
     * @return array
     */
    public function batchGetStaffDeductAmount(array $staff_ids): array
    {
        if (empty($staff_ids)) {
            return [];
        }

        //查询指定工号资产扣费总额
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['asset' => MaterialLeaveAssetsModel::class]);
        $builder->columns('asset.staff_info_id,sum(detail.deduct_amount) as deduct_amount');
        $builder->leftjoin(MaterialLeaveAssetsDetailModel::class, 'detail.leave_assets_id = asset.id', 'detail');
        $builder->inWhere('asset.staff_info_id', $staff_ids);
        $builder->andWhere('asset.is_valid = :is_valid:', ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]);
        $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('detail.asset_handling_status', [
            MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN,
            MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN,
            MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN,
        ]);
        $builder->groupBy('asset.staff_info_id');
        $amount = $builder->getQuery()->execute()->toArray();
        return array_column($amount, 'deduct_amount', 'staff_info_id');
    }

    /**
     * 员工名下资产数据-hcm详情
     * @param $items
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    private function handleAssetsDetailHcmItems($items, $locale)
    {
        if (empty($items)) {
            return [];
        }
        //获取图片
        $attachment = (new MaterialAttachmentModel())->getColumnArrUrl($items, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL);
        $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
        $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
        foreach ($items as &$item) {
            $item['asset_status_text'] = isset(MaterialEnums::$asset_status[$item['asset_status']]) ? static::$t[MaterialEnums::$asset_status[$item['asset_status']]] : '';
            $item['leave_asset_state_text'] = isset($leave_asset_state_list[$item['asset_state']]) ? static::$t[$leave_asset_state_list[$item['asset_state']]] : '';
            $item['images'] = $attachment[$item['id']] ?? [];
            $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['asset_name_en'];
        }
        return $items;
    }

    /**
     * 获取员工未归还资产数据-hcm导出使用
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    public function getNotReturnAssetsList($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_hcm_not_return_asset);
            $staff_id_arr = array_values(array_unique($params['staff_ids']));
            $all_asset_list = MaterialLeaveAssetsModel::find([
                'columns' => 'id, staff_info_id, asset_department_remark',
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and is_valid = :is_valid:',
                'bind' => [
                    'staff_info_ids' => $staff_id_arr,
                    'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES
                ],
            ])->toArray();

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialLeaveAssetsModel::class]);
            $builder->columns('main.id, main.staff_info_id, main.all_deduct_amount, detail.asset_name_zh, detail.asset_name_en, detail.asset_name_local');
            $builder->leftjoin(MaterialLeaveAssetsDetailModel::class, 'detail.leave_assets_id = main.id', 'detail');
            $builder->inWhere('main.staff_info_id', $staff_id_arr);
            $builder->andWhere('main.is_valid = :is_valid:', ['is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]);
            $builder->inWhere('detail.asset_handling_status', [MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN]);
            $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $items = $builder->getQuery()->execute()->toArray();
            //计算总扣费金额
            $leave_asset_ids = array_column($all_asset_list, 'id');
            $detail_data = [];
            if (!empty($leave_asset_ids)) {
                $detail_data = MaterialLeaveAssetsDetailModel::find([
                    'columns' => 'leave_assets_id, sum(deduct_amount) as detail_deduct_amount',
                    'conditions' => 'leave_assets_id in ({leave_assets_id:array}) and is_deleted = :is_deleted:',
                    'bind' => [
                        'leave_assets_id' => $leave_asset_ids,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED
                    ],
                    'group' => 'leave_assets_id'
                ])->toArray();
                $detail_data = array_column($detail_data, null, 'leave_assets_id');
            }
            //按员工汇总资产名称, 总扣费金额
            $return_data = [];
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
            foreach ($items as &$item) {
                $asset_name = !empty($item[$name_key]) ? $item[$name_key] : $item['asset_name_en'];
                if (!isset($return_data[$item['staff_info_id']])) {
                    $return_data[$item['staff_info_id']]['asset_name'] = $asset_name;
                    //$return_data[$item['staff_info_id']]['all_deduct_amount'] = $item['all_deduct_amount'];
                    $return_data[$item['staff_info_id']]['staff_id'] = $item['staff_info_id'];
                } else {
                    $return_data[$item['staff_info_id']]['asset_name'] .= ',' . $asset_name;
                }
            }
            //所有员工的备注和未还数据结合, 把备注补进去
            foreach ($all_asset_list as $value) {
                if (!isset($return_data[$value['staff_info_id']])) {
                    $return_data[$value['staff_info_id']]['asset_name'] = '';
                    $return_data[$value['staff_info_id']]['staff_id'] = $value['staff_info_id'];
                    $return_data[$value['staff_info_id']]['asset_department_remark'] = $value['asset_department_remark'];
                } else {
                    $return_data[$value['staff_info_id']]['asset_department_remark'] = $value['asset_department_remark'];
                }
                //总扣费金额使用详情中计算的
                $return_data[$value['staff_info_id']]['all_deduct_amount'] = !empty($detail_data[$value['id']]['detail_deduct_amount']) ? $detail_data[$value['id']]['detail_deduct_amount'] : '0.00';
            }
            $data = array_values($return_data);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-hcm-not-return-assets-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产-by端离职申请-详情页资产列表
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/21
     */
    public function getAuditLeaveAssets($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'assets_process_state' => '',
            'assets' => []
        ];
        try {
            Validation::validate($params, self::$validate_by_get_audit_leave_assets);
            //查询员工资产信息(只要自有的)
            //查询离职资产表
            $main_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'staff_resign_id = :staff_resign_id:',
                'bind' => [
                    'staff_resign_id' => $params['resign_id']
                ]
            ]);
            if (!$main_info) {
                throw new ValidationException(static::$t->_('leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //查询离职资产详情
            $leave_detail = MaterialLeaveAssetsDetailModel::find([
                'columns' => 'id, use, barcode, asset_name_zh, asset_name_en, asset_name_local, asset_code, sn_code, asset_handling_status, purchase_price',
                'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted: and data_tag = :data_tag:',
                'bind' => [
                    'leave_assets_id' => $main_info->id,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'data_tag' => MaterialEnums::LEAVE_ASSET_DATA_TAG_SELF,
                ],
            ])->toArray();
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
            foreach ($leave_detail as $v) {
                $tmp = [];
                $tmp['name'] = !empty($v[$name_key]) ? $v[$name_key] : $v['asset_name_en'];
                //by老接口用[资产编码]取的assets_goods表的bar_code , 新资产取asset_code , 为了兼容, 用同样的名称返回
                $tmp['bar_code'] = $v['asset_code'];
                $tmp['sn_code'] = $v['sn_code'];
                $tmp['purchase_price'] = (string)$v['purchase_price'];
                $tmp['all_price'] = (string)$v['purchase_price'];
                $data['assets'][] = $tmp;
            }
            $data['assets_process_state'] = isset(MaterialEnums::$asset_department_status_list[$main_info->asset_department_status]) ? static::$t[MaterialEnums::$asset_department_status_list[$main_info->asset_department_status]] : '';
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-by-audit-leave-assets-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 转换采购价和净值的汇率,转成当前国家默认币种
     * @param $currency
     * @param $amount
     * @return float|mixed
     * @date 2023/3/27
     */
    public function amountExchange($currency, $amount)
    {
        //当前国家默认币种
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
        //采购金额汇总
        if ($currency != $default_currency['code']) {
            //转换成本国币种
            $amount = EnumsService::getInstance()->currencyAmountConversion((string)$currency, (string)$amount, 3);
            $amount = round($amount, 2);
        }
        return $amount;
    }

    /**
     * 添加操作记录
     * @param $leave_assets_id
     * @param array $operator
     * @param int $type 操作类型
     * @param $record
     * @param int $source 离职来源
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     * @date 2023/3/28
     */
    public function addRecordLog($leave_assets_id, $operator, $type, $record, $source = 0)
    {
        //类型
        if (!key_exists($type, MaterialEnums::$leave_assets_record_type_list)) {
            throw new ValidationException(static::$t->_('leave_asset_record_log_type_not_exist'), ErrCode::$VALIDATE_ERROR);
        }
        //拼接内容
        $operation_content = [
            'content' => [],
        ];
        $operation_content_text = '';
        switch ($type) {
            case MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_ADD:
                //新增(离职)
                if (get_country_code() == GlobalEnums::LA_COUNTRY_CODE && $source == StaffInfoEnums::LEAVE_SOURCE_NO_ATTENDANCE) {
                    $operation_content_text = StaffInfoEnums::TRANSLATE_LEAVE_SOURCE_NO_ATTENDANCE_LA;
                } else {
                    $operation_content_text = StaffInfoEnums::$leave_source[$source] ?? '';
                }
                break;
            case MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_EDIT:
            case MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_UPDATE:
                foreach ($record as $one_detail) {
                    $fields = [];
                    foreach ($one_detail as $key => $value) {
                        //record_sub_type用来标识子类型
                        if (in_array($key, MaterialEnums::$record_need_view_fields) || $key == 'record_sub_type') {
                            $fields[$key] = $value;
                        }
                    }
                    $operation_content['content'][] = $fields;
                }
                break;
            case MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_CANCEL:
                //撤销离职(员工离职状态变更)
                $operation_content_text = MaterialEnums::LEAVE_ASSETS_RECORD_CANCEL_LEAVE_KEY;
                break;
        }
        //入库
        $insert_data = [
            'leave_assets_id' => $leave_assets_id,
            'type' => $type,
            'staff_info_id' => !empty($operator['staff_info_id']) ? $operator['staff_info_id'] : 0,
            'staff_info_name' => !empty($operator['staff_info_name']) ? $operator['staff_info_name'] : '',
            'node_department_id' => !empty($operator['node_department_id']) ? $operator['node_department_id'] : 0,
            'node_department_name' => !empty($operator['node_department_name']) ? $operator['node_department_name'] : '',
            'job_title' => !empty($operator['job_title']) ? $operator['job_title'] : 0,
            'job_name' => !empty($operator['staff_info_id']) ? $operator['staff_info_id'] : '',
            'operation_at' => !empty($operator['operation_at']) ? $operator['operation_at'] : null,
            'operation_content_text' => $operation_content_text,
            'operation_content' => json_encode($operation_content),
            'created_at' => date('Y-m-d H:i:s'),
        ];
        $record_model = new MaterialLeaveAssetsRecordModel();
        $record_model->i_create($insert_data);
        if ($record_model->save() === false) {
            throw new BusinessException('离职资产-记录操作日志，data = ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($record_model), ErrCode::$LEAVE_ASSET_RECORD_LOG_SAVE_ERROR);
        }
        return true;
    }

    /**
     * 如果影响编辑速度, 可以考虑脚本定时同步
     * 同步资产状态给hcm
     * @param $asset_department_status
     * @param $staff_info_id
     * @param $operator_id
     * @date 2023/3/31
     */
    public function syncToHcm($asset_department_status, $staff_info_id, $operator_id)
    {
        $ac = new ApiClient('hcm_rpc', '', 'sync_leave_manager_assets_remand_state');
        $api_params = [
            [
                'staff_info_id' => $staff_info_id,   //离职员工id
                'state' => $asset_department_status,   //状态
                'operator_id' => $operator_id,//操作人id
            ],
        ];
        $ac->setParams($api_params);
        $result = $ac->execute();
        if (!isset($result['result']['code']) || $result['result']['code'] != ErrCode::$SUCCESS) {
            $this->logger->warning('syncToHcm 同步资产处理状态给hcm失败,参数:' . json_encode($api_params) . '返回值:' . json_encode($result, JSON_UNESCAPED_UNICODE));
        } else {
            $this->logger->info('syncToHcm 同步资产处理状态给hcm成功,参数:' . json_encode($api_params) . '返回值:' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 获取员工名下资产统计数据(固化的)
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    public function getLeaveAssetsStaffCount($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_leave_asset_staff_count);
            //查询离职资产
            $asset_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and is_valid = :is_valid:',
                'bind' => ['staff_info_id' => $params['staff_id'], 'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]
            ]);
            if ($asset_info) {
                $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_en';
                $data = MaterialLeaveAssetsDetailModel::find([
                    'columns' => 'count(id) as number, ' . $name_key . ' as asset_name, barcode, sum(purchase_price) as price',
                    'conditions' => 'leave_assets_id = :leave_assets_id: and asset_status in ({asset_status:array}) and is_deleted = :is_deleted:',
                    'bind' => [
                        'leave_assets_id' => $asset_info->id,
                        'asset_status' => [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_REPAIRED,
                            MaterialEnums::ASSET_STATUS_REPAIRING, MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_LOST],
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    ],
                    'group' => 'barcode',
                    'order' => 'id ASC'
                ])->toArray();
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leave-assets-staff-count-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产详情变更后对主表的数量字段维护
     * @date 2023/4/11
     * @param object $leave_asset_obj 离职资产主表对象信息
     * @param array $before_detail 离职资产详情修改前数据
     * @param array $after_detail 离职资产详情修改后数据
     * @param int $operation_type 操作类型
     * @return array
     */
    public function updateNumBySave(object $leave_asset_obj, array $before_detail, array $after_detail, $operation_type = MaterialEnums::MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_SAVE)
    {
        $amount_log = [];
        //资产处理情况变化受到的影响
        if (isset($after_detail['asset_handling_status'])) {
            //损坏资产数量
            if (in_array($before_detail['asset_handling_status'], [MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN])) {
                if (!in_array($after_detail['asset_handling_status'], [MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN])) {
                    //修改前损坏,修改后未损坏,损坏数量-1
                    $leave_asset_obj->damage_assets_number -= 1;
                }
            } else {
                if (in_array($after_detail['asset_handling_status'], [MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN])) {
                    //修改未损坏,修改后损坏,损坏数量+1
                    $leave_asset_obj->damage_assets_number += 1;
                }
            }
            //未归还数量
            if ($before_detail['asset_handling_status'] == MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN || empty($before_detail['asset_handling_status'])) {
                if (!($after_detail['asset_handling_status'] == MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN || empty($after_detail['asset_handling_status']))) {
                    //修改前未归还,修改后归还,未归还数量-1
                    $leave_asset_obj->unreturned_assets_number -= 1;
                }
            } else {
                if ($after_detail['asset_handling_status'] == MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN || empty($after_detail['asset_handling_status'])) {
                    //修改前归还,修改后未归还,未归还数量+1
                    $leave_asset_obj->unreturned_assets_number += 1;
                }
            }
        }
        //扣费金额变化后收到的影响
        if (isset($after_detail['deduct_amount'])) {
            $amount_log = [
                'leave_assets_detail_id' => $before_detail['id'],
                'before_deduct_amount' => $before_detail['deduct_amount'],
                'after_deduct_amount' => $after_detail['deduct_amount'],
                'operation_type' => $operation_type
            ];
            //计算修改前后差额,如有差额,总扣费金额+差额
            $diff_deduct_amount = bcsub($after_detail['deduct_amount'], $before_detail['deduct_amount'], 2);
            if ($diff_deduct_amount != '0.00') {
                $leave_asset_obj->all_deduct_amount = bcadd($leave_asset_obj->all_deduct_amount, $diff_deduct_amount, 2);
            }
        }
        return $amount_log;
    }

    /**
     * 离职资产详情添加后对主表的数量字段维护
     * @date 2023/4/11
     * @param object $leave_asset_obj 离职资产主表对象信息
     * @param array $add_detail
     * @param int $operation_type 操作类型
     * @return array
     */
    public function updateNumByAdd(object $leave_asset_obj, array $add_detail, $operation_type = MaterialEnums::MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_ADD)
    {
        //记录金额log 添加操作,before固定是0
        $amount_log = [
            'leave_assets_detail_id' => $add_detail['id'],
            'before_deduct_amount' => '0.00',
            'after_deduct_amount' => $add_detail['deduct_amount'],
            'operation_type' => $operation_type
        ];
        //资产总数量 + 1
        $leave_asset_obj->all_asset_number += 1;
        //主管数据修改个人/公共资产数量
        if ($add_detail['manager_tag'] == MaterialEnums::MANAGER_TAG_YES) {
            if ($add_detail['use'] == MaterialEnums::USE_PERSONAL) {
                $leave_asset_obj->personal_assets_number = $leave_asset_obj->personal_assets_number + 1;
            } elseif ($add_detail['use'] == MaterialEnums::USE_PUBLIC) {
                $leave_asset_obj->public_assets_number = $leave_asset_obj->public_assets_number + 1;
            }
        }
        //修改主数据的资产数量
        if (in_array($add_detail['asset_handling_status'], [MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN])) {
            //资产部确认的资产状况“损坏”的个数，即资产处理情况为"已还受损坏"和“归还缺少零件”的资产个数
            //损坏资产总数 = 损坏数量
            $leave_asset_obj->damage_assets_number = $leave_asset_obj->damage_assets_number + 1;
        } elseif ($add_detail['asset_handling_status'] == MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN || empty($add_detail['asset_handling_status'])) {
            //未还资产总数 = 未归还+为空的个数
            $leave_asset_obj->unreturned_assets_number = $leave_asset_obj->unreturned_assets_number + 1;
        }
        //资产总价值 = 采购价之和
        if (isset($add_detail['purchase_price']) && $add_detail['purchase_price'] > 0) {
            $leave_asset_obj->all_asset_amount = bcadd($leave_asset_obj->all_asset_amount, $add_detail['purchase_price'], 2);
        }
        //总扣费金额 = 个人资产+公共资产 扣费金额之和
        if (isset($add_detail['deduct_amount']) && $add_detail['deduct_amount'] > 0) {
            $leave_asset_obj->all_deduct_amount = bcadd($leave_asset_obj->all_deduct_amount, $add_detail['deduct_amount'], 2);
        }
        return $amount_log;
    }

    /**
     * 离职资产详情删除后对主表的数量字段维护
     * @date 2023/4/11
     * @param object $leave_asset_obj 离职资产主表对象信息
     * @param array $delete_detail
     * @return array
     */
    public function updateNumByDelete(object $leave_asset_obj, array $delete_detail)
    {
        //记录金额log 删除操作,after固定是0
        $amount_log = [
            'leave_assets_detail_id' => $delete_detail['id'],
            'before_deduct_amount' => $delete_detail['deduct_amount'],
            'after_deduct_amount' => '0.00',
            'operation_type' => MaterialEnums::MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_DELETE
        ];
        //资产总数
        $leave_asset_obj->all_asset_number -= 1;
        //资产总价值
        if (isset($delete_detail['purchase_price']) && $delete_detail['purchase_price'] > 0) {
            $leave_asset_obj->all_asset_amount = bcsub($leave_asset_obj->all_asset_amount, $delete_detail['purchase_price'], 2);
        }
        //总扣费金额
        if ($delete_detail['deduct_amount'] > 0) {
            $leave_asset_obj->all_deduct_amount = bcsub($leave_asset_obj->all_deduct_amount, $delete_detail['deduct_amount'], 2);
        }
        //损坏资产总数,未还资产总数
        if (in_array($delete_detail['asset_handling_status'], [MaterialEnums::ASSET_HANDLING_STATUS_BAD_RETURN, MaterialEnums::ASSET_HANDLING_STATUS_LACK_RETURN])) {
            $leave_asset_obj->damage_assets_number -= 1;
        } elseif ($delete_detail['asset_handling_status'] == MaterialEnums::ASSET_HANDLING_STATUS_NO_RETURN || empty($delete_detail['asset_handling_status'])) {
            $leave_asset_obj->unreturned_assets_number -= 1;
        }
        //主管数据修改个人/公共资产数量
        if ($delete_detail['manager_tag'] == MaterialEnums::MANAGER_TAG_YES) {
            if ($delete_detail['use'] == MaterialEnums::USE_PERSONAL) {
                $leave_asset_obj->personal_assets_number -= 1;
            } elseif ($delete_detail['use'] == MaterialEnums::USE_PUBLIC) {
                $leave_asset_obj->public_assets_number -= 1;
            }
        }
        //所有变更字段, 不能小于0
        if ($leave_asset_obj->all_asset_number < 0) {
            $leave_asset_obj->all_asset_number = 0;
        }
        if ($leave_asset_obj->all_asset_amount < '0.00') {
            $leave_asset_obj->all_asset_amount = '0.00';
        }
        if ($leave_asset_obj->all_deduct_amount < '0.00') {
            $leave_asset_obj->all_deduct_amount = '0.00';
        }
        if ($leave_asset_obj->damage_assets_number < 0) {
            $leave_asset_obj->damage_assets_number = 0;
        }
        if ($leave_asset_obj->unreturned_assets_number < 0) {
            $leave_asset_obj->unreturned_assets_number = 0;
        }
        if ($leave_asset_obj->personal_assets_number < 0) {
            $leave_asset_obj->personal_assets_number = 0;
        }
        if ($leave_asset_obj->public_assets_number < 0) {
            $leave_asset_obj->public_assets_number = 0;
        }
        return $amount_log;
    }

    /**
     * 离职资产-资产部处理-批量确认
     * @param array $params 批量确认-参数组
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function batchConfirm($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //离职员工名下没有资产 && 资产处理状态 != 已处理，才可以被批量确认
            $leave_asset_ids = $params['assets'];

            //查询到已处理的
            $done_asset_list = MaterialLeaveAssetsModel::find([
                'columns' => 'staff_info_id',
                'conditions' => 'id in ({ids:array}) and asset_department_status = :status:',
                'bind' => ['ids' => $leave_asset_ids, 'status' => MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE],
            ])->toArray();
            $staff_info_ids = array_column($done_asset_list, 'staff_info_id');
            if (count($staff_info_ids) > 0) {
                throw new ValidationException(static::$t->_('leave_asset_batch_confirm_status_error', ['staff_id' => implode(',', $staff_info_ids)]), ErrCode::$VALIDATE_ERROR);
            }

            //满足条件，开始确认资产
            foreach ($leave_asset_ids as $leave_asset_id) {
                $params_data = [
                    'id' => $leave_asset_id,
                    'is_submit' => MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES,
                    'asset_department_status' => $params['asset_department_status'],
                    'asset_department_remark' => $params['asset_department_remark'],
                    'data' => []
                ];
                $result = $this->editSave($params_data, $user);
                if(!isset($result['code']) || $result['code'] != ErrCode::$SUCCESS) {
                    throw new ValidationException($result['message'] ?? static::$t->_('retry_later'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('material-leave-assets-batch-confirm failed:' . $e->getMessage() . json_encode($params));
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }


    /**
     * 离职资产 检测离职资产详情数据和台账数据缺少的资产并增加到资产详情
     * @param object $leave_assets 离职资产对象数据
     * @return bool
     */
    public function addDiffAssetsDetail(object $leave_assets)
    {
        $return_data = [
            'unreturned_assets_number' => 0,//未归还资产数量
            'all_asset_number'         => 0,//资产总数量
            'all_asset_amount'         => 0,//资产总价值
        ];
        //离职资产详情数据
        $leave_assets_info = MaterialLeaveAssetsDetailModel::find([
            'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted:',
            'bind'       => ['leave_assets_id' => $leave_assets->id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();

        //对应的台账详情
        $material_assets_arr = MaterialAssetsModel::find([
            'conditions' => 'status = :status: and  staff_id = :staff_id: and is_deleted = :is_deleted:',
            'bind'       => [
                'status'   => MaterialEnums::ASSET_STATUS_USING,
                'staff_id' => $leave_assets->staff_info_id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ])->toArray();
        if (!empty($material_assets_arr)) {
            $asset_info_arr  = array_column($material_assets_arr, null, 'asset_code');
            $diff_asset_code = array_diff(array_column($material_assets_arr, 'asset_code'), array_column($leave_assets_info, 'asset_code'));
            if (!empty($diff_asset_code)) {
                //存在差集
                $now_date = date('Y-m-d H:i:s');
                $n        = 0;
                $purchase = 0;
                foreach ($diff_asset_code as $one_asset) {
                    $staff_assets = $asset_info_arr[$one_asset];
                    //采购金额汇总
                    $purchase_price = $this->amountExchange($staff_assets['currency'], $staff_assets['purchase_price']);
                    $net_value      = $this->amountExchange($staff_assets['currency'], $staff_assets['net_value']);
                    $assets_detail_data[] = [
                        'leave_assets_id'         => $leave_assets->id,
                        'asset_id'                => $staff_assets['id'],
                        'asset_name_zh'           => $staff_assets['name_zh'],
                        'asset_name_en'           => $staff_assets['name_en'],
                        'asset_name_local'        => $staff_assets['name_local'],
                        'barcode'                 => $staff_assets['bar_code'],
                        'asset_code'              => $staff_assets['asset_code'],
                        'old_asset_code'          => $staff_assets['old_asset_code'],
                        'sn_code'                 => $staff_assets['sn_code'],
                        'model'                   => $staff_assets['model'],
                        'use'                     => $staff_assets['use'],
                        'asset_status'            => $staff_assets['status'],
                        'currency'                => 0,
                        'purchase_price'          => $purchase_price,
                        'net_value'               => $net_value,
                        'data_tag'                => MaterialEnums::LEAVE_ASSET_DATA_TAG_SELF,
                        'manager_tag'             => MaterialEnums::MANAGER_TAG_NO,
                        'manager_finish'          => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO,
                        'asset_department_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO,
                        'created_at'              => $now_date,
                        'updated_at'              => $now_date,
                        'is_deleted'              => GlobalEnums::IS_NO_DELETED
                    ];
                    $n++;
                    $purchase = bcadd($purchase, $purchase_price, 2);
                }
                if (!empty($assets_detail_data)) {
                    //记录和资产台账差异的数据
                    $leave_assets_detail_model = new MaterialLeaveAssetsDetailModel();
                    $leave_assets_detail_bool  = $leave_assets_detail_model->batch_insert($assets_detail_data);
                    if ($leave_assets_detail_bool === false) {
                        throw new BusinessException('离职资产详情记录创建失败, 原因可能是: ' . get_data_object_error_msg($leave_assets_detail_model) . '; 数据: ' . json_encode($assets_detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_CREATE_DETAIL_ERROR);
                    }
                    $return_data = [
                        'unreturned_assets_number' => $n,//未归还资产数量
                        'all_asset_number'         => $n,//资产总数量
                        'all_asset_amount'         => $purchase,//资产总价值
                    ];
                    $this->logger->info(' 离职资产-生成离职人员资产数据-详情，添加差异数据: ' . json_encode($assets_detail_data, JSON_UNESCAPED_UNICODE));
                    $this->logger->info('离职资产-生成离职人员资产数据，增加的未归还资产数量和资产总价值数据为:' . json_encode($return_data, JSON_UNESCAPED_UNICODE));
                }
            }
        }
        return $return_data;
    }

    /**
     * 设置单据进度
     * @param $leave_asset
     * @date 2023/7/6
     */
    public function setAssetDepartmentProgress($leave_asset)
    {
        //资产处理进度(单据进度)
        if ($leave_asset->asset_department_status == MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING) {
            //资产处理状态为“处理中”，该单据为“资产处理中”
            $leave_asset->asset_department_progress = MaterialEnums::ASSET_DEPARTMENT_PROGRESS_DOING;
        } elseif ($leave_asset->asset_department_status == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
            //资产部处理状态=已处理
            //总扣费金额不为0,且已扣金额，已还金额都为0, 单据进度=待扣款
            if (bccomp($leave_asset->all_deduct_amount, '0.00', 2) !== 0
                &&
                bccomp($leave_asset->deduct_amount, '0.00', 2) === 0
                &&
                bccomp($leave_asset->return_amount, '0.00', 2) === 0
            ) {
                $leave_asset->asset_department_progress = MaterialEnums::ASSET_DEPARTMENT_PROGRESS_DEDUCT;
            }
            //总扣款金额为0, 或者总扣款金额不为0 且已扣金额或者已还金额，这2个金额任一一个大于0, 单据进度=已完成
            if (bccomp($leave_asset->all_deduct_amount, '0.00', 2) === 0
                ||
                (bccomp($leave_asset->deduct_amount, '0.00', 2) === 1 || bccomp($leave_asset->return_amount, '0.00', 2) === 1)
            ) {
                $leave_asset->asset_department_progress = MaterialEnums::ASSET_DEPARTMENT_PROGRESS_DONE;
            }
        }
    }

    /**
     * 记录金额变更日志
     * @param array $main_amount_log 主表记录[leave_assets_id=>主表id, before_all_deduct_amount=>变更前总扣费金额, after_all_deduct_amount=>变更后总扣费金额, operation_type=>操作类型]
     * @param array $detail_amount_log 明细表记录[leave_assets_detail_id=>详情表id, before_deduct_amount=>变更前扣费金额, after_deduct_amount=>变更后扣费金额, operation_type=>操作类型]
     * @param int $operator_id 操作人id
     * @param string $batch_number 批次号,不传自动生成
     * @return string $batch_number 批次号
     * @date 2023/7/13
     */
    public function addAmountLog($main_amount_log, $detail_amount_log, $operator_id, $batch_number = '')
    {
        try {
            if (empty($main_amount_log) && empty($detail_amount_log)) {
                return true;
            }
            //生成批次号
            if (empty($batch_number)) {
                $batch_number = self::genSerialNo('LAL', RedisKey::LEAVE_ASSET_AMOUNT_LOG_NO, 6);
            }
            $created_at = date('Y-m-d H:i:s');
            //记录主表日志
            $main_model = new MaterialLeaveAssetsMainAmountLogModel();
            $main_data = [
                'leave_assets_id' => $main_amount_log['leave_assets_id'],
                'batch_number' => $batch_number,
                'before_all_deduct_amount' => $main_amount_log['before_all_deduct_amount'],
                'after_all_deduct_amount' => $main_amount_log['after_all_deduct_amount'],
                'difference_amount' => bcsub($main_amount_log['after_all_deduct_amount'], $main_amount_log['before_all_deduct_amount'], 2),
                'operation_type' => $main_amount_log['operation_type'],
                'operator_id' => $operator_id,
                'created_at' => $created_at
            ];
            if ($main_model->i_create($main_data) == false) {
                throw new BusinessException('金额日志主表记录失败, 原因可能是: ' . get_data_object_error_msg($main_model) . '; 数据: ' . json_encode($main_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            //记录详情表日志
            $detail_model = new MaterialLeaveAssetsDetailAmountLogModel();
            $detail_data = [];
            foreach ($detail_amount_log as $detail_log) {
                if (empty($detail_log)) {
                    continue;
                }
                $detail_data[] = [
                    'main_log_id' => $main_model->id,
                    'leave_assets_id' => $main_amount_log['leave_assets_id'],
                    'leave_assets_detail_id' => $detail_log['leave_assets_detail_id'],
                    'batch_number' => $batch_number,
                    'before_deduct_amount' => $detail_log['before_deduct_amount'],
                    'after_deduct_amount' => $detail_log['after_deduct_amount'],
                    'difference_amount' => bcsub($detail_log['after_deduct_amount'], $detail_log['before_deduct_amount'], 2),
                    'operation_type' => $detail_log['operation_type'],
                    'operator_id' => $operator_id,
                    'created_at' => $created_at
                ];
            }
            if (!empty($detail_data)) {
                if ($detail_model->batch_insert($detail_data) == false) {
                    throw new BusinessException('金额日志明细表记录失败,  数据: ' . json_encode($detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning('message :' . $e->getMessage() . '; trace :' . $e->getTraceAsString() . '; main_amount_log=' . json_encode($main_amount_log, JSON_UNESCAPED_UNICODE) . '; detail_amount_log=' . json_encode($detail_amount_log, JSON_UNESCAPED_UNICODE));
        }
        return $batch_number;
    }

    /**
     * 正式员工转个人代理，个人代理到岗确认，自动转移资产到代理名下
     * @param array $params 参数组
     * @param string $locale 语种
     * @return array
     */
    public function transferLeaveAssetsToPersonalAgent($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_transfer_leave_assets_to_personal_agent);
            $old_staff_id = $params['old_staff_id'];//离职的正式员工工号
            $new_staff_id = $params['new_staff_id'];//在职的个人代理工号

            //正式工转个人代理的资产自动转移开关是否开启
            $auto_transfer_personal_agent_status = EnumsService::getInstance()->getSettingEnvValue('material_asset_auto_transfer_personal_agent_status', 0);
            if ($auto_transfer_personal_agent_status) {
                //开启，判断正式工转个人代理自动转移的资产（barcode）是否有值
                $auto_transfer_personal_agent_barcodes = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_auto_transfer_personal_agent_barcodes');
                if (!empty($auto_transfer_personal_agent_barcodes)) {
                    //有值，对应的正式工号的有效的资产处理情况是否为已处理
                    $leave_asset_info = MaterialLeaveAssetsModel::findFirst([
                        'conditions' => 'staff_info_id = :staff_info_id: and is_valid = :is_valid:',
                        'bind' => ['staff_info_id' => $old_staff_id, 'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES]
                    ]);
                    if (empty($leave_asset_info)) {
                        throw new ValidationException('未找到离职员工：' . $old_staff_id . '的有效离职资产数据', ErrCode::$VALIDATE_ERROR);
                    }

                    //资产处理状态非已处理
                    if ($leave_asset_info->asset_department_status != MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                        //新接收人只能是在职的不包含待离职，我们才自动处理
                        $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $new_staff_id, 'state' => StaffInfoEnums::STAFF_STATE_IN, 'limit' => 1]);
                        $to_staff_info = $staff_list['data'] ? $staff_list['data'][0] : [];
                        if (!$to_staff_info || $to_staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                            throw new ValidationException('未找到在职员工：' . $new_staff_id . '的信息或员工非在职', ErrCode::$VALIDATE_ERROR);
                        }

                        //正式工转个人代理自动转移的资产（barcode）在资产台账里不可自动转移的资产使用状态配置
                        $asset_status = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_auto_transfer_personal_agent_asset_status');

                        //操作人信息
                        $user = ['id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID, 'name' => StaffInfoEnums::SUPER_ADMIN_STAFF_NAME, 'nick_name' => ''];

                        //转移离职员工名下资产到个人代理名下
                        $this->transferAssetsToPersonalAgentOwner(['barcode' => $auto_transfer_personal_agent_barcodes, 'asset_status' => $asset_status, 'old_staff_id' => $old_staff_id, 'to_staff_info' => $to_staff_info, 'user' => $user]);

                        //处理离职资产
                        $this->handleLeaveAssets(['leave_asset_info' => $leave_asset_info, 'barcode' => $auto_transfer_personal_agent_barcodes, 'asset_status' => $asset_status, 'old_staff_id' => $old_staff_id, 'new_staff_id' => $new_staff_id, 'user' => $user]);
                    }
                }
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $this->logger->notice('transfer_leave_assets_to_personal_agent-failed:' . $e->getCode() . $e->getMessage() . $e->getTraceAsString());
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('transfer_leave_assets_to_personal_agent-failed:' . $e->getCode() . $e->getMessage() . $e->getTraceAsString());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 转移离职员工名下资产到个人代理名下
     * @param array $params[
     * 'barcode' => '正式工转个人代理自动转移的资产（barcode）组',
     * 'asset_status' => '正式工转个人代理自动转移的资产（barcode）在资产台账里的使用状态组',
     * 'old_staff_id' => '旧工号（离职的正式员工工号）',
     * 'to_staff_info' => '新工号（在职的个人代理工号）信息组',
     * 'user' => '操作人信息组'
     * ] 参数组
     * @return bool
     * @throws BusinessException
     */
    public function transferAssetsToPersonalAgentOwner($params)
    {
        $barcode = $params['barcode'];
        $asset_status = $params['asset_status'];
        $old_staff_id = $params['old_staff_id'];
        $to_staff_info = $params['to_staff_info'];
        $user = $params['user'];
        $asset_list = MaterialAssetsRepository::getInstance()->searchAsset([
            'bar_code' => $barcode,
            'staff_id' => $old_staff_id,
            'not_status' => $asset_status
        ]);
        //名下无资产不需要转移
        if (!$asset_list) {
            return true;
        }

        // 获取成本中心
        if ($to_staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_HEAD_OFFICE;
            $get_pc_id = $to_staff_info['node_department_id'];
        } else {
            $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_SYS_STORE;
            $get_pc_id = $to_staff_info['sys_store_id'];
        }
        $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($get_pc_id, $get_pc_type);
        $to_staff_info['pc_code'] = $pc_code_data['data']['pc_code'] ?? '';

        $now_time = date('Y-m-d H:i:s');
        //要修改的台账-修改前数据
        $asset_before_data = [];
        //拼接转移记录
        $transfer_data = [];
        foreach ($asset_list as $one_asset) {
            $tmp_transfer = [];
            $tmp_transfer['asset_id'] = $one_asset['id'];
            $tmp_transfer['barcode'] = $one_asset['bar_code'];
            $tmp_transfer['asset_code'] = $one_asset['asset_code'];
            $tmp_transfer['from_staff_id'] = $one_asset['staff_id'] ?? 0;
            $tmp_transfer['from_staff_name'] = $one_asset['staff_name'] ?? '';
            $tmp_transfer['from_node_department_id'] = $one_asset['node_department_id'] ?? 0;
            $tmp_transfer['from_node_department_name'] = $one_asset['node_department_name'] ?? '';
            $tmp_transfer['from_sys_store_id'] = $one_asset['sys_store_id'];
            $tmp_transfer['from_store_name'] = $one_asset['store_name'];
            $tmp_transfer['from_pc_code'] = $one_asset['pc_code'] ?? '';
            $tmp_transfer['from_use_land'] = $one_asset['use_land'] ?? '';
            $tmp_transfer['from_company_id'] = $one_asset['company_id'] ?? 0;
            $tmp_transfer['from_company_name'] = $one_asset['company_name'] ?? '';
            $tmp_transfer['to_staff_id'] = $to_staff_info['staff_id'];
            $tmp_transfer['to_staff_name'] = $to_staff_info['staff_name'];
            $tmp_transfer['to_node_department_id'] = $to_staff_info['node_department_id'] ?? 0;
            $tmp_transfer['to_node_department_name'] = $to_staff_info['node_department_name'] ?? '';
            $tmp_transfer['to_sys_store_id'] = $to_staff_info['sys_store_id'] ?? 0;
            $tmp_transfer['to_store_name'] = $to_staff_info['store_name'] ?? '';
            $tmp_transfer['to_pc_code'] = $to_staff_info['pc_code'] ?? '';
            $tmp_transfer['to_use_land'] = $to_staff_info['store_name'] ?? '';
            $tmp_transfer['to_company_id'] = $to_staff_info['company_id'] ?? 0;
            $tmp_transfer['to_company_name'] = $to_staff_info['company_name'] ?? '';
            $tmp_transfer['status'] = MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED;
            $tmp_transfer['transfer_type'] = MaterialEnums::TRANSFER_TYPE_LEAVE_ASSET_AUTO_PERSONAL_AGENT;
            $tmp_transfer['auto_recipient'] = MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_YES;
            $tmp_transfer['operator_id'] = $user['id'];
            $tmp_transfer['transfer_operator_id'] = $user['id'];
            $tmp_transfer['transfer_operator_name'] = $user['name'];
            $tmp_transfer['transfer_at'] = $now_time;
            $tmp_transfer['finished_at'] = $now_time;
            $tmp_transfer['created_at'] = $now_time;
            $tmp_transfer['updated_at'] = $now_time;
            $transfer_data[] = $tmp_transfer;
            //记录资产台账修改前后的值
            $asset_before_data[] = [
                'asset_code' => $one_asset['asset_code'],
                'staff_id' => $one_asset['staff_id'],
                'staff_name' => $one_asset['staff_name'],
                'state' => $one_asset['state'],
                'leave_date' => $one_asset['leave_date'],
                'wait_leave_state' => $one_asset['wait_leave_state'],
                'job_id' => $one_asset['job_id'],
                'job_name' => $one_asset['job_name'],
                'node_department_id' => $one_asset['node_department_id'],
                'node_department_name' => $one_asset['node_department_name'],
                'company_id' => $one_asset['company_id'],
                'company_name' => $one_asset['company_name'],
                'sys_store_id' => $one_asset['sys_store_id'],
                'store_name' => $one_asset['store_name'],
                'pc_code' => $one_asset['pc_code'],
                'use_land' => $one_asset['use_land'],
                'receipted_at' => $one_asset['receipted_at'],
                'updated_at' => $one_asset['updated_at'],
            ];
        }

        //要修改的台账-修改后数据
        $asset_ids_str = implode(',', array_column($asset_list, 'id'));
        $asset_after_data = [
            'staff_id' => $to_staff_info['staff_id'],
            'staff_name' => $to_staff_info['staff_name'],
            'state' => $to_staff_info['state'] ?? 0,
            'leave_date' => $to_staff_info['leave_date'] ?? null,
            'wait_leave_state' => $to_staff_info['wait_leave_state'] ?? 0,
            'job_id' => $to_staff_info['job_id'] ?? 0,
            'job_name' => $to_staff_info['job_name'] ?? '',
            'node_department_id' => $to_staff_info['node_department_id'] ?? 0,
            'node_department_name' => $to_staff_info['node_department_name'] ?? '',
            'company_id' => $to_staff_info['company_id'] ?? 0,
            'company_name' => $to_staff_info['company_name'] ?? '',
            'sys_store_id' => $to_staff_info['sys_store_id'] ?? 0,
            'store_name' => $to_staff_info['store_name'],
            'pc_code' => $to_staff_info['pc_code'] ?? '',
            'use_land' => $to_staff_info['store_name'] ?? '',
            'receipted_at' => $now_time,
            'updated_at' => $now_time,
        ];

        //开始数据库操作
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //批量修改台账
            $update_bool = $db->updateAsDict(
                (new MaterialAssetsModel())->getSource(),
                $asset_after_data,
                ['conditions' => "id in ({$asset_ids_str})"]
            );
            if ($update_bool === false) {
                throw new BusinessException('转移离职员工名下资产到个人代理名下-更新台账失败 ids=' . $asset_ids_str . '; update_data=' . json_encode($asset_after_data, JSON_UNESCAPED_UNICODE));
            }

            //批量记录日志
            $update_log_model = new MaterialAssetUpdateLogModel();
            $log_bool = $update_log_model->dealEditDataBatch($asset_before_data, $asset_after_data, $user, MaterialEnums::OPERATE_TYPE_LEAVE_PERSONAL_AGENT);
            if ($log_bool === false) {
                throw new BusinessException('转移离职员工名下资产到个人代理名下-操作记录 before_data = ' . json_encode($asset_before_data, JSON_UNESCAPED_UNICODE) . '; after_data = ' . json_encode($asset_after_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //生成转移记录
            $transfer_main = new MaterialAssetTransferBatchModel();
            $transfer_batch_data = [
                'staff_id' => $user['id'],
                'staff_name' => (new BaseService())->getNameAndNickName($user['name'], $user['nick_name']),
                'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                'status' => MaterialEnums::TRANSFER_BATCH_STATUS_RECEIVED,
                'mark' => '转移离职员工名下资产到个人代理名下自动接收',
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_main->i_create($transfer_batch_data);
            if ($bool === false) {
                throw new BusinessException('转移离职员工名下资产到个人代理名下-记录转移头信息失败, 数据: ' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_main), ErrCode::$BUSINESS_ERROR);
            }

            //生成转移记录详情
            foreach ($transfer_data as &$transfer) {
                $transfer['batch_id'] = $transfer_main->id;
            }
            $transfer_detail = new MaterialAssetTransferLogModel();
            $transfer_log_bool = $transfer_detail->batch_insert($transfer_data);
            if ($transfer_log_bool === false) {
                throw new BusinessException('转移离职员工名下资产到个人代理名下-记录转移行信息失败, 数据: ' . json_encode($transfer_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_detail), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

            //给个人代理发送BY消息
            LeaveAssetsMessageService::getInstance()->sendMessage([
                'staff_info_id' => $to_staff_info['staff_id'],
                'message_title' => 'ข่าวการเปลี่ยนแปลงทรัพย์สิน 资产变更消息',
                'message_content' => '',
                'category' => MaterialEnums::LEAVE_ASSET_AUTO_TRANSFER_PERSONAL_AGENT_CATEGORY
            ]);
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 处理离职员工离职资产
     * @param array $params[
     * 'leave_asset_info' => '离职资产对象',
     * 'barcode' => '正式工转个人代理自动转移的资产（barcode）组',
     * 'asset_status' => '正式工转个人代理自动转移的资产（barcode）在资产台账里的使用状态组',
     * 'old_staff_id' => '旧工号（离职的正式员工工号）',
     * 'new_staff_id' => '新工号（在职的个人代理工号）',
     * 'user' => '操作人信息组'
     * ] 参数组
     * @return bool
     * @throws ValidationException
     */
    public function handleLeaveAssets($params)
    {
        $leave_asset_info = $params['leave_asset_info'];
        $barcode = $params['barcode'];
        $asset_status = $params['asset_status'];
        $old_staff_id = $params['old_staff_id'];
        $new_staff_id = $params['new_staff_id'];
        $user = $params['user'];

        //获取离职资产明细
        $leave_asset_details = $leave_asset_info->getLeaveAssetsDetails()->toArray();
        //无资产明细，不需要处理
        if (!$leave_asset_details) {
            return true;
        }

        //离职资产处理参数组
        $leave_asset_params = [
            'id' => $leave_asset_info->id,
            'is_submit' => MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES,
            'asset_department_status' => $leave_asset_info->asset_department_status, //资产处理状态
            'asset_department_remark' => $leave_asset_info->asset_department_remark,//操作备注
            'deduct_amount' => $leave_asset_info->deduct_amount, //已扣金额
            'return_amount' => $leave_asset_info->return_amount,//已还金额
            'loss_amount' => $leave_asset_info->loss_amount,//损失金额
            'data' => [],//资产明细行
        ];

        //获取离职资产在台账里的资产清单
        $leave_asset_codes = array_values(array_filter(array_column($leave_asset_details, 'asset_code')));
        if ($leave_asset_codes) {
            $asset_list = MaterialAssetsRepository::getInstance()->searchAsset([
                'asset_code' => $leave_asset_codes,
                'bar_code' => $barcode,
                'not_status' => $asset_status,
                'staff_id' => $new_staff_id
            ]);
            $asset_list = array_column($asset_list, null, 'asset_code');
            if ($asset_list) {
                foreach ($leave_asset_details as &$leave_asset) {
                    if (!isset($asset_list[$leave_asset['asset_code']])) {
                        continue;
                    }
                    //离职资产在台账里，需要处理离职资产明细行
                    $leave_asset['asset_department_finish'] = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
                    $leave_asset['deduct_amount'] = 0.00;
                    $leave_asset_params['data'][] = [
                        'detail_id' => $leave_asset['id'],//明细id
                        'asset_handling_status' => MaterialEnums::ASSET_HANDLING_STATUS_BELONG_OTHER,//资产部处理情况
                        'asset_state' => '',//资产状况
                        'manager_remark' => $leave_asset['manager_remark'],//主管备注
                        'barcode' => $leave_asset['barcode'],//barcode
                        'asset_code' => $leave_asset['asset_code'],//资产编码
                        'sn_code' => $leave_asset['sn_code'], //sn码
                        'model' => $leave_asset['model'],//规格型号
                        'purchase_price' => $leave_asset['purchase_price'],//采购价
                        'net_value' => $leave_asset['net_value'],//净值
                        'deduct_amount' => $leave_asset['deduct_amount'],//扣费金额
                        'deduct_reason' => '',//扣费原因
                        'asset_status' => $leave_asset['asset_status'],//资产状态
                    ];
                }
            }
        }

        //再判断资产转移记录表中，是否存在接收人=正式员工，接收状态为“待接收”
        $unreceived_asset_count = MaterialAssetTransferLogModel::count([
            'conditions' => 'to_staff_id = :to_staff_id: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'to_staff_id' => $old_staff_id,
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ]
        ]);

        //不存在待接收数据 && 每一行离职资产的资产处理情况都有值，则更新“资产处理状态”为“已处理”；其他情况一律不更新资产处理状态
        if (!$unreceived_asset_count) {
            $asset_department_finish_arr = array_unique(array_column($leave_asset_details, 'asset_department_finish'));
            if (count($asset_department_finish_arr) == 1 && $asset_department_finish_arr[0] == MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES) {
                $leave_asset_params['asset_department_status'] = MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE;
            }
        }

        //离职资产处理
        $res = $this->editSave($leave_asset_params, $user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return true;
        } else if ($res['code'] == ErrCode::$VALIDATE_ERROR) {
            throw new ValidationException($res['message'], ErrCode::$VALIDATE_ERROR);
        } else {
            throw new \Exception($res['message'], $res['code']);
        }
    }

    /**
     * 员工离职名下没有资产，资产处理状态自动改成已处理
     * @param array $leave_assets_list 离职资产列表
     * @return array
     */
    public function autoProcess($leave_assets_list)
    {
        //处理情况组
        $un_process_ids = $process_success_ids = $process_fail_ids = [];
        $staff_info_ids = array_column($leave_assets_list, 'staff_info_id');
        $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_info_ids);
        $asset_department_remark = EnumsService::getInstance()->getSettingEnvValue('material_leave_asset_asset_department_remark');
        foreach ($leave_assets_list as $leave_asset_info) {
            //员工的离职状态为“已离职”；非离职跳过不处理
            $one_staff_info = $staff_list[$leave_asset_info['staff_info_id']] ?? [];
            if (!$one_staff_info || $one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_LEAVE) {
                $un_process_ids[] = '离职资产员工为：' . $leave_asset_info['staff_info_id'] . ' 的员工非离职';
               continue;
            }

            //离职资产明细表里资产数量=0
            $leave_asset_details_count = MaterialLeaveAssetsDetailModel::count([
                'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted:',
                'bind' => ['leave_assets_id' => $leave_asset_info['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if ($leave_asset_details_count) {
                $un_process_ids[] = '离职资产员工为：' . $leave_asset_info['staff_info_id'] . ' 的资产明细表里资产数量非0';
                continue;
            }

            //产转移记录表里不存在“待接收”的，接收人=离职员工
            //资产转移记录表里不存在“已接收”或者“已拒收”，接收人=离职员工，接收时间在员工写进离职表之后的数据
            $data = [];
            $this->getLeaveDetailTotalInfo($leave_asset_info, $data);
            if ($data['staff_transfer_unreceived_num'] > 0 || $data['staff_transfer_received_num'] > 0 || $data['staff_transfer_rejected_num'] > 0) {
                $un_process_ids[] = '离职资产员工为：' . $leave_asset_info['staff_info_id'] . ' 的离职员工存在待接收、已接收、已拒收资产';
                continue;
            }

            //离职资产处理参数组
            $leave_asset_params = [
                'id' => $leave_asset_info['id'],
                'is_submit' => MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES,
                'asset_department_status' => MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE, //资产处理状态
                'asset_department_remark' => $asset_department_remark,//操作备注
                'deduct_amount' => $leave_asset_info['deduct_amount'], //已扣金额
                'return_amount' => $leave_asset_info['return_amount'],//已还金额
                'loss_amount' => $leave_asset_info['loss_amount'],//损失金额
                'data' => [],//资产明细行
            ];
            //操作人信息
            $user = ['id' => StaffInfoEnums::SUPER_ADMIN_STAFF_ID, 'name' => StaffInfoEnums::SUPER_ADMIN_STAFF_NAME, 'nick_name' => ''];
            $res = $this->editSave($leave_asset_params, $user);
            if ($res['code'] == ErrCode::$SUCCESS) {
                $process_success_ids[] = '离职资产员工为：' . $leave_asset_info['staff_info_id'] . '，处理成功';
            } else {
                $process_fail_ids[] = '离职资产员工为：' . $leave_asset_info['staff_info_id'] . '，处理失败的原因为：' . $res['message'];
            }
        }
        return [
            'un_process_ids' => $un_process_ids,//未处理
            'process_success_ids' => $process_success_ids,//处理成功
            'process_fail_ids' =>  $process_fail_ids//处理失败
        ];
    }
}
