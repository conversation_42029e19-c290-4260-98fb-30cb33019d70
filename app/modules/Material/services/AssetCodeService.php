<?php
/**
 * Created by PhpStorm.
 * Date: 2021/7/17
 * Time: 10:24
 */

namespace App\Modules\Material\Services;


use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Modules\Material\Models\MaterialAssetCodeSendModel;

class AssetCodeService extends BaseService
{
    private $retry_max = 5;
    private $code_max = 99999999;
    private static $instance;

    /**
     * 单例
     * @return AssetCodeService
     * @date 2022/2/24
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 生成资产编码
     * @param $asset_type
     * @param $serial_number
     * @param $timestamp
     * @return string
     * @date 2022/2/24
     * @throws BusinessException
     */
    public function createAssetCode($asset_type,$serial_number,$timestamp){
        if (empty($asset_type) || empty($serial_number) || $serial_number>$this->code_max || empty($timestamp)){
            throw new BusinessException('create_asset_code_failed',ErrCode::$CREATE_ASSET_CODE_ERROR);
        }
        $year_month = date('Ym',$timestamp);
        $year_month = substr($year_month,-4);
        $country_code = get_country_code();
        return $asset_type.$country_code.$year_month.$serial_number;
    }

    /**
     * 获取资产编码流水号
     * @param int $num
     * @param $timestamp
     * @param int $retry
     * @return array
     * @throws BusinessException
     * @date 2022/2/23
     */
    public function createSerialNumber(int $num,$timestamp,int $retry=1){
        if ($retry>$this->retry_max){
            throw new BusinessException('create_serial_number_failed',ErrCode::$CREATE_SERIAL_CODE_ERROR);
        }
        //当前年份,本地时区
        $year = date('Y',$timestamp);
        $year_date = MaterialAssetCodeSendModel::findFirst([
            'conditions' => 'year = ?1',
            'columns' => 'id,year,asset_code_number',
            'bind' => [1 => $year]
        ]);
        $db = $this->getDI()->get('db_oa');
        $asset_code_send_model = new MaterialAssetCodeSendModel();
        //创建当前年份的流水号
        if (!$year_date){
            $insert_data = [
                'year'=>$year,
                'asset_code_number'=>0
            ];
            $success = $db->insertAsDict(
                $asset_code_send_model->getSource(), $insert_data
            );
            if (!$success){
                $this->createSerialNumber($num,$retry+1);
            }
            $id = $db->lastInsertId();
            $serial_data = $insert_data;
            $serial_data['id'] = $id;
        }else{
            $serial_data = $year_date->toArray();
        }
        //本次流水号
        $serial_number = $serial_data['asset_code_number'] + $num;
        if ($serial_number>$this->code_max){
            throw new BusinessException('create_serial_number_failed',ErrCode::$CREATE_SERIAL_CODE_EXCEED);
        }
        //占用流水号
        $update_success = $db->updateAsDict(
            $asset_code_send_model->getSource(),
            [
                'asset_code_number' => $serial_number
            ],
            [
                'conditions'=>'id=? and asset_code_number=?',
                'bind' => [$serial_data['id'],$serial_data['asset_code_number']]
            ]
        );
        if (!$update_success){
            $this->createSerialNumber($num,$retry+1);
        }
        //返回本批流水号
        $serial_number_array = [];
        for ($i=$serial_data['asset_code_number']+1;$i<=$serial_number;$i++){
            $serial_number_array[] = sprintf("%08d",$i);
        }
        return $serial_number_array;
    }
}
