<?php
namespace App\Modules\Material\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialSettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\MaterialStoreStorageModel;
use App\Models\oa\MaterialStoreStorageRuleModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Library\Enums\MaterialWmsEnums;

/**
 * 物料/资产管理-分仓规则-服务层
 * Class WarehouseDivisionRuleService
 * @package App\Modules\Material\Services
 */
class WarehouseDivisionRuleService  extends BaseService
{
    public static $result_column = 5;
    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'type',
        'region_piece_id',
        'pageSize',
        'pageNum',
    ];

    //列表-搜索-参数验证
    public static $validate_list = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'type' => 'IntIn:0,' . MaterialSettingEnums::STORE_STORAGE_TYPE_REGION . ',' . MaterialSettingEnums::STORE_STORAGE_TYPE_PIECE . ',' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE,
        'region_piece_id' => 'IntGe:0',
        'sys_store_id' => 'StrLenGeLe:0,10'
    ];

    //添加
    public static $validate_add = [
        'type' => 'Required|IntIn:' . MaterialSettingEnums::STORE_STORAGE_TYPE_REGION . ',' . MaterialSettingEnums::STORE_STORAGE_TYPE_PIECE . ',' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE,
        'region_piece_id' => 'IfIntNe:type,' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE . '|Required|IntGt:0',
        'region_piece_name' => 'IfIntNe:type,' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE . '|Required|StrLenGeLe:1,100',
        'sys_store_id' => 'IfIntEq:type,' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE . '|Required|StrLenGeLe:1,10',
        'store_name' => 'IfIntEq:type,' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE . '|Required|StrLenGeLe:1,50',
        'headquarters' => 'IfIntEq:type,' . MaterialSettingEnums::STORE_STORAGE_TYPE_STORE . '|Required|StrLenGeLe:1,10|>>>:headquarters error',
    ];

    //仓库优先级
    public static $validate_add_rules = [
        'rules' => 'Required|Arr|ArrLenGeLe:1,20',
        'rules[*].business_type' => 'Required|IntIn:' . MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET . ',' . MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS,//业务类型
        'rules[*].company_id' => 'Required|IntGt:0',//公司ID
        'rules[*].company_name' => 'Required|StrLenGeLe:1,50',//公司名称
        'rules[*].mach_code' => 'Required|StrLenGeLe:1,128',//货主code
        'rules[*].mach_name' => 'Required|StrLenGeLe:1,128',//货主名称
        'rules[*].stock_id' => 'Required|StrLenGeLe:1,10',//仓库id
        'rules[*].stock_name' => 'Required|StrLenGeLe:1,255'//仓库名称
    ];

    //详情、删除校验验证
    public static $validate_update = [
        'id' => 'Required|IntGt:0',
    ];

    private static $instance;
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return WarehouseDivisionRuleService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 网点分仓-枚举项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            //类型
            $type = MaterialSettingEnums::$material_store_storage_type;
            foreach ($type as $key => $value) {
                $data['type'][] = [
                    'value' => $key,
                    'label' => static::$t->_($value)
                ];
            }
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            //获取货主列表
            $data['cargo_owner'] = $this->getScmCargoOwner();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-warehouse_division_rule-default-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 分仓规则-列表
     * @param array $condition 参数组
     * @param bool $export 导出true，非导出false
     * @param integer $count 总记录数
     * @return array
     */
    public function getList($condition, $export = false, $count = 0)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if ($export === false) {
                $count = $this->getListCount($condition);
            }
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialStoreStorageModel::class]);
                if ($export === false) {
                    $columns = 'id, type, region_piece_name, store_name, updated_at, updated_id, updated_name';
                    $order_by = 'main.updated_at desc, main.id desc';
                } else {
                    $builder->leftJoin(MaterialStoreStorageRuleModel::class, 'rule.store_storage_id = main.id', 'rule');
                    $columns = 'main.type, main.region_piece_name, main.store_name, rule.company_name, rule.mach_name, rule.stock_name, rule.level';
                    $order_by = 'main.updated_at desc, main.id desc, rule.level asc';
                }
                $builder->columns($columns);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby($order_by);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $export);
            }
            $data['items']= $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-warehouse_division_rule-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @return int
     * @throws ValidationException
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main'=>MaterialStoreStorageModel::class]);
        $builder->columns('count(main.id) AS count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->count);
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition($builder, $condition)
    {
        $type = !empty($condition['type']) ? $condition['type'] : 0;//分仓类型
        $region_piece_id = !empty($condition['region_piece_id']) ? $condition['region_piece_id'] : 0;//大区ID或片区ID
        $sys_store_id = !empty($condition['sys_store_id']) ? trim($condition['sys_store_id']) : '';//网点ID或者总部地址ID
        if ((!empty($region_piece_id) || !empty($sys_store_id)) && empty($type)) {
            throw new ValidationException(static::$t->_('material_store_storage_type_search'), ErrCode::$VALIDATE_ERROR);
        }
        if (!empty($type)) {
            $builder->where('main.type = :type:', ['type' => $type]);
        }
        //按照网点搜索
        if (!empty($sys_store_id)) {
            $builder->andWhere('main.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        //按照大区、片区搜索
        if (!empty($region_piece_id)) {
            $builder->andWhere('main.region_piece_id = :region_piece_id:', ['region_piece_id' => $region_piece_id]);
        }
        return $builder;
    }

    /**
     * 格式化分仓设置列表
     * @param array $items 列表
     * @param bool $export 导出true，非导出false
     * @return array
     */
    private function handleListItems(array $items, bool $export = false)
    {
        if (empty($items)) {
            return [];
        }
        if ($export === false) {
            foreach ($items as &$item) {
                $item['type_text'] = static::$t[MaterialSettingEnums::$material_store_storage_type[$item['type']]];
            }
        } else {
            //导出
            $row_value = [];//组装导出需要的数据
            foreach ($items as $key => $item) {
                $row_value[] = [
                    'number' => ++$key,//序号
                    'type_text' => static::$t[MaterialSettingEnums::$material_store_storage_type[$item['type']]],//类型
                    'name' => ($item['type'] == MaterialSettingEnums::STORE_STORAGE_TYPE_STORE) ? $item['store_name'] : $item['region_piece_name'],//名称
                    'company_name' => $item['company_name'],//公司名称
                    'mach_name' => $item['mach_name'],//货主名称
                    'stock_name' => $item['stock_name'],//仓库名称
                    'level' => $item['level'],//优先级
                ];
            }
            $items = $row_value;
        }
        return $items;
    }

    /**
     * 分仓规则-新建
     * @param array $params 规则配置
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //验证参数
            $this->extendValidation($params, MaterialSettingEnums::STORE_STORAGE_OPERATE_TYPE_ADD);

            //新增保存逻辑
            $this->operateAdd($params, $user);

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-warehouse_division_rule-add failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 根据特定条件获取分仓设置信息
     * @param array $params 分仓规则参数组
     * @return mixed
     */
    public function getMaterialStoreStorageInfo($params)
    {
        $type = $params['type'];//分仓类型
        $condition = 'type = :type:';
        $bind['type'] = $params['type'];
        //新建操作独有的验证规则
        if ($type == MaterialSettingEnums::STORE_STORAGE_TYPE_STORE) {
            //分仓类型 = 网点
            $condition .= ' AND sys_store_id = :sys_store_id:';
            $bind['sys_store_id'] = $params['sys_store_id'];
        } else {
            //分仓类型 = 大区、片区
            $condition .= ' AND region_piece_id = :region_piece_id:';
            $bind['region_piece_id'] = $params['region_piece_id'];
        }
        return MaterialStoreStorageModel::findFirst([
            'conditions' => $condition,
            'bind' => $bind
        ]);
    }

    /**
     * 新建、编辑操作验证
     * @param array $params 分仓规则参数组
     * @param int $operate_type 分仓规则操作类型：1新增，2编辑
     * @throws ValidationException
     */
    private function extendValidation($params, $operate_type = MaterialSettingEnums::STORE_STORAGE_OPERATE_TYPE_SAVE)
    {
        if ($operate_type == MaterialSettingEnums::STORE_STORAGE_OPERATE_TYPE_ADD) {
            $store_storage_info = $this->getMaterialStoreStorageInfo($params);
            if (!empty($store_storage_info)) {
                //存在设置提示
                throw new ValidationException(static::$t->_('material_store_storage_type_exits', ['type' => static::$t->_('material_store_storage_type.' . $params['type'])]), ErrCode::$VALIDATE_ERROR);
            }
        }

        //新建、编辑都有的验证规则
        $company_mach_code_more = [];
        $stock_id_more = [];
        foreach ($params['rules'] as $rule) {
            // 仓库优先级中同一个公司主体是否有多个货主，如果存在，则提示“同一个公司只允许选择一个货主，请修改后提交！”
            if (!empty($company_mach_code_more[$rule['company_id']]) && !in_array( $rule['mach_code'], $company_mach_code_more[$rule['company_id']])) {
                throw new ValidationException(static::$t->_('material_store_storage_mach_code_more'), ErrCode::$VALIDATE_ERROR);
            } else {
                $company_mach_code_more[$rule['company_id']][] = $rule['mach_code'];
            }

            // 检验仓库优先级中是否存在业务类型+公司+货主+仓库同时重复，如果存在，则提示“公司、货主、仓库同时重复，请修改后提交！”
            $business_company_mach_stock_key = $rule['business_type'] . '_' . $rule['company_id'] . '_' . $rule['mach_code'];
            if ($stock_id_more && in_array($rule['stock_id'], $stock_id_more[$business_company_mach_stock_key])) {
                throw new ValidationException(static::$t->_('material_store_storage_stock_repeat'), ErrCode::$VALIDATE_ERROR);
            } else {
                $stock_id_more[$business_company_mach_stock_key][] = $rule['stock_id'];
            }
        }
    }

    /**
     * 分仓规则-新建-入库
     * @param array $params 规则配置
     * @param array $user 当前登陆者信息组
     * @throws BusinessException
     */
    private function operateAdd($params, $user)
    {
        //分仓规则入库
        $now_time = date('Y-m-d H:i:s', time());
        $material_store_storage_data = [
            'region_piece_id' => $params['region_piece_id'] ?? 0,
            'region_piece_name' => $params['region_piece_name'] ?? '',
            'sys_store_id' => $params['sys_store_id'] ?? '',
            'store_name' => $params['store_name'] ?? '',
            'headquarters' => $params['headquarters'] ?? '',
            'type' => $params['type'],
            'updated_id' => $user['id'],
            'updated_name' => $user['name'],
            'created_at' => $now_time,
            'updated_at' => $now_time
        ];
        $material_store_storage_model = new MaterialStoreStorageModel();
        $bool = $material_store_storage_model->i_create($material_store_storage_data);
        if ($bool === false) {
            throw new BusinessException('分仓规则-新建-失败 = ' . json_encode($material_store_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($material_store_storage_model), ErrCode::$BUSINESS_ERROR);
        }

        //分仓规则明细入库
        $material_store_storage_rule_model = new MaterialStoreStorageRuleModel();
        foreach ($params['rules'] as $key => &$rule) {
            $rule['store_storage_id'] = $material_store_storage_model->id;
            $rule['level'] = ++$key;
            $rule['updated_id'] = $material_store_storage_model->updated_id;
            $rule['updated_name'] = $material_store_storage_model->updated_name;
            $rule['created_at'] = $material_store_storage_model->created_at;
            $rule['updated_at'] = $material_store_storage_model->updated_at;
        }
        $bool = $material_store_storage_rule_model->batch_insert($params['rules']);
        if ($bool === false) {
            throw new BusinessException('分仓规则-新建配置明细-失败 = ' . json_encode($params['rules'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
    }

    /**
     * 分仓规则-修改
     * @param array $params 规则配置
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function save($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //验证参数
            $this->extendValidation($params);

            //检测分仓设置信息是否存在
            $material_store_storage_info = $this->getMaterialStoreStorageInfoById($params['id']);

            //修改保存逻辑
            $this->operateSave($material_store_storage_info, $params, $user);

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-warehouse_division_rule-save failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 根据ID获取分仓设置信息
     * @param integer $id 分仓设置ID
     * @return mixed
     * @throws ValidationException
     */
    private function getMaterialStoreStorageInfoById($id)
    {
        $info = MaterialStoreStorageModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($info)) {
            throw new ValidationException(static::$t->_('material_store_storage_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $info;
    }

    /**
     * 分仓规则-修改-入库
     * @param object $material_store_storage_info 分仓设置对象信息
     * @param array $params 规则配置
     * @param array $user 当前登陆者信息组
     * @throws BusinessException
     */
    private function operateSave($material_store_storage_info, $params, $user)
    {
        //更新分仓设置信息
        $now_time = date('Y-m-d H:i:s', time());
        $update_data = [
            'updated_id' => $user['id'],
            'updated_name' => $user['name'],
            'updated_at' => $now_time
        ];
        $bool = $material_store_storage_info->i_update($update_data);
        if ($bool === false) {
            throw new BusinessException('分仓规则-修改-失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        //需要将原来分仓设置明细删除
        $bool = $material_store_storage_info->getRules()->delete();
        if ($bool === false) {
            throw new BusinessException('分仓规则-修改配置明细-删除原先优先级设置-失败 = ' . json_encode(['id' => $material_store_storage_info->id], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        //分仓规则明细入库
        $rules = [];
        $material_store_storage_rule_model = new MaterialStoreStorageRuleModel();
        foreach ($params['rules'] as $key => &$rule) {
            $rules[] = [
                'store_storage_id' => $material_store_storage_info->id,
                'company_id' => $rule['company_id'],
                'company_name' => $rule['company_name'],
                'mach_code' => $rule['mach_code'],
                'mach_name' => $rule['mach_name'],
                'stock_id' => $rule['stock_id'],
                'stock_name' => $rule['stock_name'],
                'business_type' => $rule['business_type'],
                'level' => ++$key,
                'updated_id' => $material_store_storage_info->updated_id,
                'updated_name' => $material_store_storage_info->updated_name,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
        }
        $bool = $material_store_storage_rule_model->batch_insert($rules);
        if ($bool === false) {
            throw new BusinessException('分仓规则-修改配置明细-失败 = ' . json_encode($rules, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
    }

    /**
     * 分仓规则-导出
     * @param array $condition 参数组
     * @return array
     */
    public function export($condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '512M');
            $count = $this->getListCount($condition);
            $page_size = 2000;
            $step = ceil($count / $page_size);
            $row_values = [];
            for ($i = 1; $i <= $step; $i++) {
                $condition['pageNum'] = $i;
                $condition['pageSize'] = $page_size;
                $list = $this->getList($condition, true, $count);
                if ($list['code'] != ErrCode::$SUCCESS) {
                    break;
                }
                $rows = $list['data']['items'];
                $row_values = array_merge($row_values, $rows);
            }
            $row_values = array_map('array_values', $row_values);
            $file_name = 'material_store_storage_rules_' . date('YmdHis');
            $header = [
                static::$t->_('material_store_storage.number'),//序号
                static::$t->_('material_store_storage.type'),//类型
                static::$t->_('material_store_storage.name'),//名称
                static::$t->_('material_store_storage.company_name'),//公司名称
                static::$t->_('material_store_storage.mach_code'),//货主
                static::$t->_('material_store_storage.stock_name'),//仓库
                static::$t->_('material_store_storage.level'),//优先级

            ];
            $result = $this->exportExcel($header, $row_values, $file_name);
            $data = $result['data'] ?? [];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-warehouse_division_rule-export-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 分仓规则-查看
     * @param array $params 参数组
     * @return array
     */
    public function detail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $store_storage_info = $this->getMaterialStoreStorageInfoById($params['id']);
            $detail = $store_storage_info->toArray();
            $detail['rules'] = $store_storage_info->getRules()->toArray();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-warehouse_division_rule-detail failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 分仓规则-删除
     * @param array $params 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function del($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //删除设置
            $material_store_storage_info = $this->getMaterialStoreStorageInfoById($params['id']);
            $material_store_storage_data = $material_store_storage_info->toArray();
            $bool = $material_store_storage_info->delete();
            if ($bool === false) {
                throw new BusinessException('分仓规则-删除-失败 = ' . $params['id'], ErrCode::$BUSINESS_ERROR);
            }

            //删除明细
            $rule = $material_store_storage_info->getRules();
            $rule_data = $rule->toArray();
            $bool = $rule->delete();
            if ($bool === false) {
                throw new BusinessException('分仓规则-删除规则明细-失败 = ' .$params['id'], ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info('material-warehouse_division_rule-del:' . json_encode([
                    'store_storage' => $material_store_storage_data,
                    'store_storage_rule' => $rule_data,
                    'user_id' => $user['id'],
                    'user_name' => $user['name'],
                    'operate_at' => date('Y-m-d H:i:s'),
                ], JSON_UNESCAPED_UNICODE));
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-warehouse_division_rule-del failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 获取分仓规则开关状态，未设置key或者设置只为空或者为0均为未开启，只有1是开启
     * @return mixed
     */
    public function getStoreStorageOpenStatus()
    {
        return EnumsService::getInstance()->getSettingEnvValue('material_store_storage_open', 0);
    }

    /**
     * 分仓规则-覆盖导入
     * @param string $excel_file 文件
     * @param array $user 当前员工信息组
     * @return array
     */
    public function importRuleTask($excel_file, $user)
    {
        $real_message = '';
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }
            //解析文件内容
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            //读取上传文件数据
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
            //弹出excel标题一行信息
            array_shift($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }

            //验证条数
            $import_limit = MaterialSettingEnums::STORE_STORAGE_IMPORT_LIMIT;
            if (count($excel_data) > $import_limit) {
                throw new ValidationException(static::$t->_('material_store_storage_import_error', ['num' => $import_limit]), ErrCode::$VALIDATE_ERROR);
            }

            // 导入中心
            return ImportCenterService::getInstance()->addImportTask($file, $user, ImportCenterEnums::TYPE_MATERIAL_STORE_STORAGE_ADD);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('importRuleTaskAdd-分仓规则-覆盖导入-新增失败-' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 分仓规则-覆盖导入-查询最后一次导入成功的结果
     * @return array
     */
    public function getImportResult()
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_MATERIAL_STORE_STORAGE_ADD);
    }

    /**
     * 验证优先级规则导入合法性
     * @param array $excel_data excel数据组
     * @param integer $staff_info_id 操作人工号
     * @return array
     */
    public function handleRuleTask($excel_data, $staff_info_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $total_num = 0;//总记录数
        $fail_num = 0;//失败记录数
        $search_info_data = [];//根据大区/片区/网点ID获取过的信息组
        $type_company_mach_code = [];//类型+大区/片区/网点ID+公司货主唯一性校验数组
        $type_company_mach_stock = [];//类型+大区/片区/网点ID+公司货主+仓库唯一性校验数组
        $material_store_storage_rules = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //获取excel表头，并追加导入结果列
            $excel_header = array_shift($excel_data);

            //总数据记录数
            $total_num = count($excel_data);

            //BU级别公司列表
            $bu_company_list = (new PurchaseService())->getCooCostCompany();
            $cost_company_kv = array_column($bu_company_list, 'cost_company_name', 'cost_company_id');

            //货主信息
            $scm_service = new ScmService();
            $mach_code_list = $scm_service->scmCargoOwner();
            $mach_code_kv = array_column($mach_code_list, 'name', 'mach_code');

            //获取仓库信息
            $stock_list = $scm_service->getStockList();

            //开始遍历excel每一行数据并验证处理更新入库
            foreach ($excel_data as $line => &$row) {
                $type = trim($row[0]) ?? '';//类型
                $id = trim($row[1]) ?? 0;//大区/片区/网点ID
                $company_id = trim($row[2]) ?? 0;//公司ID
                $mach_code = trim($row[3]) ?? '';//货主ID
                $stock_id = trim($row[4]) ?? '';//仓库ID

                //错误信息组
                $error_msg = [];

                //类型必须是1-大区、2-片区、3-网点
                $end = stripos($type, '-');
                $type_val = ($end !== false) ? substr($type, 0, $end) : '';
                if (!in_array($type_val, array_keys(MaterialSettingEnums::$material_store_storage_type))) {
                    $error_msg[] = self::$t['material_store_storage_import_error_001'];
                }

                //大区/片区/网点ID必须有值
                $id_key = $type_val . '_' . $id;
                if (empty($id)) {
                    $error_msg[] = self::$t['material_store_storage_import_error_002'];
                } else {
                    //按照类型获取不同的信息
                    if (!isset($search_info_data[$id_key])) {
                        switch ($type_val) {
                            case MaterialSettingEnums::STORE_STORAGE_TYPE_REGION:
                                $info = RegionRepository::getInstance()->getRegionInfoById($id);
                                break;
                            case MaterialSettingEnums::STORE_STORAGE_TYPE_PIECE:
                                $info = PieceRepository::getInstance()->getPieceInfoById($id);
                                break;
                            case MaterialSettingEnums::STORE_STORAGE_TYPE_STORE:
                                if (is_numeric($id)) {
                                    //网点类型，ID是数字表示总部
                                    $info = $this->getHeadQuartersAddressInfoById($id);
                                } else {
                                    //表示网点
                                    $info = (new StoreRepository())-> getStoreDetail($id);
                                }
                                break;
                            default:
                                $info = [];
                                break;
                        }
                        $search_info_data[$id_key] = $info;
                    }

                    //大区/片区/网点ID不存在或状态错误
                    if (empty($search_info_data[$id_key])) {
                        $error_msg[] = self::$t['material_store_storage_import_error_003'];
                    }
                }

                //公司ID
                if (empty($company_id)) {
                    $error_msg[] = self::$t['material_store_storage_import_error_004'];
                } else if (!key_exists($company_id, $cost_company_kv)) {
                    //判断公司ID是否是BU级公司
                    $error_msg[] = self::$t['material_store_storage_import_error_005'];
                }

                //货主ID
                if (empty($mach_code)) {
                    $error_msg[] = self::$t['material_store_storage_import_error_006'];
                } else if (!key_exists($mach_code, $mach_code_kv)) {
                    //判断货主是否存在
                    $error_msg[] = self::$t['material_store_storage_import_error_007'];
                }

                //仓库ID
                if (empty($stock_id)) {
                    $error_msg[] = self::$t['material_store_storage_import_error_008'];
                } else if (empty($stock_list[$mach_code][$stock_id])) {
                    //仓库不存在或者不属于改货主下
                    $error_msg[] = self::$t['material_store_storage_import_error_009'];
                }

                //某类型下公司+货主唯一性
                $type_company_mach_code_unique_key = $type_val . '_' . $id . '_' . $company_id;
                if (empty($type_company_mach_code[$type_company_mach_code_unique_key])) {
                    $type_company_mach_code[$type_company_mach_code_unique_key] = $mach_code;
                }
                if ($mach_code != $type_company_mach_code[$type_company_mach_code_unique_key]) {
                    $error_msg[] = self::$t['material_store_storage_mach_code_more'];
                }

                //类型+校验同一个大区、片区或网点+公司+货主+仓库+业务类型
                $company_mach_code_stock_key = $type_val . '_' . $id . '_' . $company_id . '_' . $mach_code . '_' . MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS;
                if (isset($type_company_mach_stock[$company_mach_code_stock_key]) && in_array($stock_id, $type_company_mach_stock[$company_mach_code_stock_key])) {
                    $error_msg[] = self::$t['material_store_storage_stock_repeat'];
                } else {
                    $type_company_mach_stock[$company_mach_code_stock_key][] = $stock_id;
                }

                //存在错误信息将错误信息放入到对应的行身上
                if (!empty($error_msg)) {
                    $row[self::$result_column] = implode(";", $error_msg);
                    $fail_num++;
                } else {
                    //类型+大区、片区、网点分仓设置信息组
                    if (!isset($material_store_storage_rules[$id_key]['info'])) {
                        if ($type_val == MaterialSettingEnums::STORE_STORAGE_TYPE_STORE) {
                            //网点
                            if (is_numeric($id)) {
                                $headquarters =  Enums::HEAD_OFFICE_STORE_FLAG;
                                $search_info_data[$id_key]['name'] = $search_info_data[$id_key]['office_name'] ?? '';
                            } else {
                                $headquarters = $id;
                            }
                            $material_store_storage_rules[$id_key]['info'] = [
                                'sys_store_id' => $id,
                                'store_name' => $search_info_data[$id_key]['name'] ?? '',
                                'headquarters' => $headquarters,
                                'type' => $type_val
                            ];
                        } else {
                            //大区、片区
                            $material_store_storage_rules[$id_key]['info'] = [
                                'region_piece_id' => $id,
                                'region_piece_name' => $search_info_data[$id_key]['name'] ?? '',
                                'type' => $type_val
                            ];
                        }
                    }
                    //分仓设置规则组
                    $material_store_storage_rules[$id_key]['rules'][] = [
                        'company_id' => $company_id,
                        'company_name' => $cost_company_kv[$company_id],
                        'mach_code' => $mach_code,
                        'mach_name' => $mach_code_kv[$mach_code],
                        'stock_id' => $stock_id,
                        'stock_name' => $stock_list[$mach_code][$stock_id]['name'] ?? '',
                        'business_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS
                    ];
                }
            }
            //没有失败的，则需要做入库
            if ($fail_num === 0 && !empty($material_store_storage_rules)) {
                //获取操作对象
                $user = (new HrStaffRepository())->getStaffById($staff_info_id);
                $user['id'] = $staff_info_id;
                foreach ($material_store_storage_rules as $item) {
                    $info = $item['info'];//分仓设置信息
                    $type = $info['type'];//类型，1大区、2片区、3网点
                    $rules = $item['rules'];//分仓设置-优先级
                    if ($type == MaterialSettingEnums::STORE_STORAGE_TYPE_STORE) {
                        $store_storage_info = $this->getMaterialStoreStorageInfo(['type' => $type, 'sys_store_id' => $info['sys_store_id']]);
                    } else {
                        $store_storage_info = $this->getMaterialStoreStorageInfo(['type' => $type, 'region_piece_id' => $info['region_piece_id']]);
                    }
                    if (empty($store_storage_info)) {
                        //未设置过是新增
                        $info['rules'] = $rules;
                        $this->operateAdd($info, $user);
                    } else {
                        //设置过是更新
                        $this->operateSave($store_storage_info, ['rules' => $rules], $user);
                    }
                }
            }

            $db->commit();
            //将表头放入到data中返回
            array_unshift($excel_data, $excel_header);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('handle-rule_task_-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $excel_data,
                'success_num' => $total_num - $fail_num,
                'failed_sum' => $fail_num
            ]
        ];
    }

    /**
     * 获取某分仓规则下，某公司规则优先级列表
     * @param integer $store_storage_id 分仓规则ID
     * @param integer $company_id 公司ID
     * @param int $level 优先级查询 1 第一优先级（不是表material_store_storage_rule的level=1），0 全部
     * @return mixed
     */
    public function getStorageRuleListByParams($store_storage_id, $company_id, $level)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['mssr' => MaterialStoreStorageRuleModel::class]);
        $builder->where('mssr.store_storage_id = :store_storage_id: and mssr.company_id = :company_id:', ['store_storage_id' => $store_storage_id, 'company_id' => $company_id]);
        //同一个分仓规则和公司规则下获取优先级，level=1表示获取最小的一条数据
        if ($level > MaterialWmsEnums::MATERIAL_STORE_STORAGE_RULE_LEVEL_ALL) {
            $builder->limit($level);
        }
        $builder->orderby('mssr.level ASC');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取分仓规则设置的优先级列表
     * @param array $params 查询库存参数组
     * @param int $level 优先级查询 1 第一优先级（不是表material_store_storage_rule的level=1），0 全部
     * @return array
     */
    public function getStoreStorageRulesList($params, int $level = MaterialWmsEnums::MATERIAL_STORE_STORAGE_RULE_LEVEL_ALL)
    {
        $store_storage_rules_list = [];
        //分仓设置必须传递了公司ID、网点ID
        if (!empty($params['company_id']) && !empty($params['use_land_id'])) {
            $use_land_id = $params['use_land_id'];//网点ID或总部地址ID
            $company_id = $params['company_id'];//公司ID
            $material_store_storage_open_status = $this->getStoreStorageOpenStatus();
            if ($material_store_storage_open_status == MaterialSettingEnums::STORE_STORAGE_OPEN) {
                //开启了分仓设置,则查找使用网点是否设置了分仓优先级
                $store_storage_info = $this->getMaterialStoreStorageInfo(['type' => MaterialSettingEnums::STORE_STORAGE_TYPE_STORE, 'sys_store_id' => $use_land_id]);
                //分仓未设置 && 总部，则不需要继续找
                if (empty($store_storage_info) && is_numeric($use_land_id)) {
                    return $store_storage_rules_list;
                }
                //分仓设置了，看优先级设置的是否符合当前公司
                if (!empty($store_storage_info)) {
                    $store_storage_rules_list = $this->getStorageRuleListByParams($store_storage_info->id, $company_id, $level);
                    //优先级符合当前公司 或 优先级不符合当前公司&&总部，则不需要继续找
                    if (!empty($store_storage_rules_list) || (empty($store_storage_rules_list) && is_numeric($use_land_id))) {
                        return $store_storage_rules_list;
                    }
                }

                //未找到且是网点的需要继续找
                if (empty($store_storage_rules_list)) {
                    //获取网点的片区、大区信息
                    $store_info = (new StoreRepository())->getStoreDetail($use_land_id);
                    //网点设置了片区,看片区分仓设置否
                    if (!empty($store_info['manage_piece'])) {
                        $store_storage_info = $this->getMaterialStoreStorageInfo(['type' => MaterialSettingEnums::STORE_STORAGE_TYPE_PIECE, 'region_piece_id' => $store_info['manage_piece']]);
                        if (!empty($store_storage_info)) {
                            $store_storage_rules_list = $this->getStorageRuleListByParams($store_storage_info->id, $company_id, $level);
                        }
                    }

                    //片区未找到设置，看大区分仓设置否
                    if (empty($store_storage_rules_list) && !empty($store_info['manage_region'])) {
                        $store_storage_info = $this->getMaterialStoreStorageInfo(['type' => MaterialSettingEnums::STORE_STORAGE_TYPE_REGION, 'region_piece_id' => $store_info['manage_region']]);
                        if (!empty($store_storage_info)) {
                            $store_storage_rules_list = $this->getStorageRuleListByParams($store_storage_info->id, $company_id, $level);
                        }
                    }
                }
            }
        }
        return $store_storage_rules_list;
    }
}
