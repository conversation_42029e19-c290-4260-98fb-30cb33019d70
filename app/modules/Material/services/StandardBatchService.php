<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\oa\MaterialSauPermissionModel;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Models\MaterialSauSkuModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialUpdateLogModel;


class StandardBatchService extends BaseService
{
    public static $add_result_column = 29;
    public static $update_result_column = 18;
    public static $max_data_number = 300;
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 标准型号 新增导入任务
     * @param $file
     * @param $excel_data
     * @param $user
     * @param int $type 类型 1.导入新增 2.导入修改
     * @return array
     * @date 2022/7/18
     */
    public function barcodeBatchUploadTask($file, $excel_data, $user, $type)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //跳过空行
            $clear_excel_data = [];
            foreach ($excel_data as $key => $row) {
                if (empty($row[0])) {
                    break;
                }
                $clear_excel_data[$key] = $row;
            }
            //验证条数
            if (count($clear_excel_data) > self::$max_data_number) {
                throw new ValidationException(self::$t['data_exceeds_the_limit']);
            }
            // 文件生成OSS链接
            $file_path = sys_get_temp_dir() . '/' . $file->getName();
            $file->moveTo($file_path);
            $oss_result = OssHelper::uploadFile($file_path);
            if (!isset($oss_result['object_url']) || empty($oss_result['object_url'])) {
                throw new ValidationException(self::$t['barcode_file_upload_error']);
            }
            // 导入中心
            $bool = ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], $type);
            if (!$bool) {
                throw new ValidationException(self::$t['add_import_center_error']);
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error('barcodeBatchUploadAdd-标准型号导入新增失败-' . $real_message);
            $correct_data = [];
            $error_data = $clear_excel_data;
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 标准型号 导入新增
     * @param $excel_data
     * @param $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/7/18
     */
    public function barcodeBatchUploadAdd($excel_data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = $real_trace = '';
        $clear_excel_data = $correct_data = $error_data = [];
        //跳过空行
        foreach ($excel_data as $key => $row) {
            if ($row[0] == "") {
                break;
            }
            $clear_excel_data[$key] = $row;
        }
        $result_data = $clear_excel_data;
        $datetime = date('Y-m-d H:i:s');
        //验证条数
        if (count($clear_excel_data) > self::$max_data_number) {
            throw new ValidationException(self::$t['data_exceeds_the_limit']);
        }
        //excel数据转data
        $data = $this->excelToData($clear_excel_data);
        $barcode_arr = array_column($data, 'barcode');
        //数据校验
        $correct_and_error = $this->validationBatch($data, $barcode_arr);
        $correct_data = $correct_and_error['correct_data'];
        $error_data = $correct_and_error['error_data'];
        //$correct_data和$error_data的key必须和$result_data对齐
        if ($correct_data || $error_data) {
            foreach ($correct_data as $index => $cv) {
                $result_data[$index][self::$add_result_column] = self::$t['excel_result_success'];
            }
            foreach ($error_data as $err_index => $ev) {
                $result_data[$err_index][self::$add_result_column] = $ev['error_message'];
            }
        }
        // 图片处理
        $category_id_collect = [];
        foreach ($correct_data as $index => &$upload_data) {
            foreach ($upload_data['attachments'] as &$one_data) {
                //上传图片
                $http_header = @get_headers($one_data, true);
                $http_response_header = $http_header[0] ?? 'header null';
                if (!stripos($http_response_header, '200')) {
                    $result_data[$index][self::$add_result_column] = self::$t['success_but_attachment_error'] . ';';
                    //清空附件,后续不处理
                    $upload_data['attachments'] = [];
                    continue;
                }
                $oss_info = @OssHelper::uploadFile($one_data);
                if (!isset($oss_info['bucket_name']) || !isset($oss_info['object_key']) || !isset($oss_info['file_name'])) {
                    $result_data[$index][self::$add_result_column] = self::$t['success_but_attachment_error'] . ';';
                    //清空附件,后续不处理
                    $upload_data['attachments'] = [];
                    continue;
                }
                $one_data = $oss_info;
            }
            $category_id_collect[] = $upload_data['category_id'];
        }
        //查找物料分类设置的[是否限制员工互转],作为默认值
        $category_data_kv = [];
        if (!empty($category_id_collect)) {
            $category_id_collect = array_values(array_unique($category_id_collect));
            $category_data = MaterialCategoryModel::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted: and status = :status:',
                'columns' => 'id,transfer_forbid',
                'bind' => [
                    'ids' =>$category_id_collect,
                    'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                    'status' => MaterialClassifyEnums::MATERIAL_START_USING
                ]
            ])->toArray();
            $category_data_kv = array_column($category_data, 'transfer_forbid', 'id');
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //批量插入数据
            if (!empty($correct_data)) {
                //批量插入material_sau表
                $insert_data = [];
                foreach ($correct_data as $correct_v) {
                    $insert_data[] = [
                        'category_id' => $correct_v['category_id'],
                        'finance_category_id' => $correct_v['finance_category_id'],
                        'barcode' => trim($correct_v['barcode']),
                        'name_zh' => $correct_v['name_zh'],
                        'name_en' => $correct_v['name_en'],
                        'name_local' => $correct_v['name_local'] ?? '',
                        'model' => $correct_v['model'] ?: '',
                        'unit_zh' => $correct_v['unit_zh'],
                        'unit_en' => $correct_v['unit_en'],
                        'category_type' => $correct_v['category_type'],
                        'purchase_type' => $correct_v['purchase_type'] ?? StandardService::DEFAULT_PARAMETER_ZERO,
                        'brand' => $correct_v['brand'] ?: '',
                        'remark' => $correct_v['remark'] ?: '',
                        'price' => $correct_v['price'] ?: 0.00,
                        'sap_unit' => $correct_v['sap_unit'],
                        'currency' => $correct_v['currency'],
                        'update_to_scm' => $correct_v['update_to_scm'],
                        'update_to_sap' => $correct_v['update_to_sap'],
                        'update_to_acceptance' => $correct_v['update_to_acceptance'],
                        'enable_asset_code' => $correct_v['enable_asset_code'],
                        'enable_sn' => $correct_v['enable_sn'],
                        'transfer_forbid' => !empty($correct_v['transfer_forbid']) ? $correct_v['transfer_forbid'] : ($category_data_kv[$correct_v['category_id']]['transfer_forbid'] ?? MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO) ,
                        'status' => StandardService::DEFAULT_PARAMETER_ONE,//默认为启用
                        'is_send_sap' => StandardService::getInstance()->checkSendSap($correct_v['update_to_sap'], $correct_v['finance_category_id']),//发送至SAP结果：0默认值，1待发送，2已发送，3发送失败
                        'is_deleted' => StandardService::DEFAULT_PARAMETER_ZERO,
                        'created_at' => $datetime,
                        'updated_at' => $datetime,
                        'use_scene' => $correct_v['use_scene'],
                        'is_contain_job' => $correct_v['is_contain_job'],
                    ];
                }
                $main_model = new MaterialSauModel();
                $result_main = $main_model->batch_insert($insert_data);
                if (!$result_main) {
                    throw new BusinessException('插入数据异常: 待处理数据: '. json_encode($insert_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_ERROR);
                }
                //通过barcode反查这批id,并绑定到数组中
                $last_insert_data = $main_model::find([
                    'conditions' => 'barcode in ({barcode_arr:array}) and is_deleted=:is_deleted:',
                    'columns' => 'id,barcode',
                    'bind' => ['barcode_arr' => $barcode_arr, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                ])->toArray();
                $barcode_id_kv = array_column($last_insert_data, 'id', 'barcode');
                foreach ($correct_data as &$correct_datum) {
                    if (in_array($correct_datum['barcode'], $barcode_id_kv)) {
                        throw new BusinessException('插入数据异常', ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_ID_ERROR);
                    }
                    $correct_datum['id'] = $barcode_id_kv[$correct_datum['barcode']];
                }
                //批量插入sku
                $insert_sku = [];
                //可申请职位组
                $insert_sau_job = [];
                foreach ($correct_data as $sku_data) {
                    $insert_sku[] = [
                        'sau_id' => $sku_data['id'],
                        'length' => $sku_data['length'] ?? 0.00,
                        'length_unit' => $sku_data['length_unit'] ?? 'mm',
                        'width' => $sku_data['width'] ?? 0.00,
                        'width_unit' => $sku_data['width_unit'] ?? 'mm',
                        'height' => $sku_data['height'] ?? 0.00,
                        'height_unit' => $sku_data['height_unit'] ?? 'mm',
                        'weight' => $sku_data['weight'] ?? 0.00,
                        'weight_unit' => $sku_data['weight_unit'] ?? 'g',
                        'purchase_unit_zh' => $sku_data['purchase_unit_zh'] ?? '',
                        'purchase_unit_en' => $sku_data['purchase_unit_en'] ?? '',
                        'purchase_val' => $sku_data['purchase_val'] ?? 0,
                        'use_unit_zh' => $sku_data['use_unit_zh'] ?? '',
                        'use_unit_en' => $sku_data['use_unit_en'] ?? '',
                        'use_val' => $sku_data['use_val'] ?: 0,
                        'small_bag_unit_zh' => $sku_data['small_bag_unit_zh'] ?: '',
                        'small_bag_unit_en' => $sku_data['small_bag_unit_en'] ?: '',
                        'small_bag_val' => $sku_data['small_bag_val'] ?: 0,
                        'big_bag_unit_zh' => $sku_data['big_bag_unit_zh'] ?: '',
                        'big_bag_unit_en' => $sku_data['big_bag_unit_en'] ?: '',
                        'big_bag_val' => $sku_data['big_bag_val'] ?: 0,
                        'is_deleted' => StandardService::DEFAULT_PARAMETER_ZERO,
                        'created_at' => $datetime,
                        'updated_at' => $datetime,
                    ];

                    //16850需求针对资产且需要设置可申请职位的需要保存
                    if ($sku_data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && !empty($sku_data['job'])) {
                        foreach ($sku_data['job'] as $job_id) {
                            $job_id = trim($job_id);
                            if (empty($job_id)) {
                                continue;
                            }
                            $insert_sau_job[] = [
                                'sau_id' => $sku_data['id'],
                                'job_id' => $job_id,
                                'created_at' => $datetime,
                                'updated_at' => $datetime,
                            ];
                        }
                    }
                }
                $sku_model = new MaterialSauSkuModel();
                $insert_sku_result = $sku_model->batch_insert($insert_sku);
                if (!$insert_sku_result) {
                    throw new BusinessException('插入sku数据异常: 待处理数据: '. json_encode($insert_sku, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sku_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_ID_ERROR);
                }

                //16850需求针对资产且需要设置可申请职位的需要保存
                if (!empty($insert_sau_job)) {
                    $sau_permission_model = new MaterialSauPermissionModel();
                    $insert_job_result = $sau_permission_model->batch_insert($insert_sau_job);
                    if (!$insert_job_result) {
                        throw new BusinessException('插入sau_permission数据异常: 待处理数据: '. json_encode($insert_sau_job, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_permission_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_ID_ERROR);
                    }
                }
                //批量插入图片表
                $insert_attachment = [];
                foreach ($correct_data as $index => $attachment_data) {
                    foreach ($attachment_data['attachments'] as $oss_attachment) {
                        //截取url中的oss信息
                        $insert_attachment[] = [
                            'oss_bucket_type' => MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK,
                            'sub_type' => StandardService::DEFAULT_PARAMETER_ZERO,
                            'oss_bucket_key' => $attachment_data['id'],
                            'bucket_name' => $oss_attachment['bucket_name'],
                            'object_key' => $oss_attachment['object_key'],
                            'file_name' => $oss_attachment['file_name'],
                            'deleted' => StandardService::DEFAULT_PARAMETER_ZERO,
                            'created_at' => $datetime
                        ];
                    }
                }
                $am_model = new MaterialAttachmentModel();
                if (!empty($insert_attachment)) {
                    if (!$am_model->batch_insert($insert_attachment)) {
                        throw new BusinessException('标准型号图片添加失败: 待处理数据: '. json_encode($insert_attachment, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($am_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_IMG_ERROR);
                    }
                }
                // 批量插入同步scm的任务
                $insert_scm = [];
                foreach ($correct_data as $scm_data) {
                    if ($scm_data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                        $insert_scm[] = $scm_data['id'];
                    }
                }
                if (!empty($insert_scm)) {
                    $scm = new ScmService();
                    $bool = $scm->saveMaterialSyncScmLogBatch($insert_scm, MaterialClassifyEnums::TO_SCM_SYNC_TYPE_ADD, $datetime);
                    if ($bool === false) {
                        throw new BusinessException('标准型号添加同步至scm记录失败 = ' . json_encode($insert_scm, JSON_UNESCAPED_UNICODE), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_SYNC_SCM_ERROR);
                    }
                }
                //批量插入操作记录
                $update_log_model = new MaterialUpdateLogModel();
                $log_bool = $update_log_model->dealEditFieldBatch([], $correct_data, $user, $datetime);
                if ($log_bool === false) {
                    throw new BusinessException('标准型号添加操作记录失败 = ' . json_encode($correct_data, JSON_UNESCAPED_UNICODE), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INSERT_EDIT_LOG_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace = $e->getTraceAsString();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace = $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('barcodeBatchUploadAdd-标准型号导入新增失败-message=' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data = $clear_excel_data;
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $result_data,
                'all_num' => count($clear_excel_data),
                'success_num' => count($correct_data),
                'failed_sum' => count($error_data)
            ]
        ];
    }

    /**
     * 标准型号 导入新增
     * @param $excel_data
     * @param $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @date 2022/7/18
     */
    public function barcodeBatchUploadEdit($excel_data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = $real_trace = '';
        $clear_excel_data = $correct_data = $error_data = [];
        //跳过空行
        foreach ($excel_data as $key => $row) {
            if ($row[0] == "") {
                break;
            }
            $clear_excel_data[$key] = $row;
        }
        $result_data = $clear_excel_data;
        $datetime = date('Y-m-d H:i:s');
        //验证条数
        if (count($clear_excel_data) > self::$max_data_number) {
            throw new ValidationException(self::$t['data_exceeds_the_limit']);
        }
        //excel数据转data
        $data = $this->excelToDataEdit($clear_excel_data);
        $save_material_sau_sku = ['use_unit_zh','use_unit_en','use_val','use_unit'];
        $barcode_arr = array_column($data, 'barcode');
        //查询本次修改数据
        $sau_data = $this->isExistsBarcodeBatch(array_values(array_unique($barcode_arr)));
        $sau_data_kv = array_column($sau_data, null, 'barcode');
        $sau_data_ids = array_column($sau_data, 'id');
        //数据校验
        $correct_and_error = $this->validationBatchEdit($data, $barcode_arr, $sau_data_kv, $save_material_sau_sku);
        $correct_data = $correct_and_error['correct_data'];
        $error_data = $correct_and_error['error_data'];

        //$correct_data和$error_data的key必须和$result_data对齐
        if ($correct_data || $error_data) {
            foreach ($correct_data as $index => $cv) {
                $result_data[$index][self::$update_result_column] = self::$t['excel_result_success'];

            }
            foreach ($error_data as $err_index => $ev) {
                $result_data[$err_index][self::$update_result_column] = $ev['error_message'];
            }
        }
        // 图片处理
        foreach ($correct_data as $index => &$upload_data) {
            if (isset($upload_data['attachments'])) {
                foreach ($upload_data['attachments'] as &$one_data) {
                    //上传图片
                    $http_header = @get_headers($one_data, true);
                    $http_response_header = $http_header[0] ?? 'header null';
                    if (!stripos($http_response_header, '200')) {
                        $result_data[$index][self::$update_result_column] = self::$t['success_but_attachment_error'] . ';';
                        //清空附件,后续不处理
                        $upload_data['attachments'] = [];
                        continue;
                    }
                    $oss_info = @OssHelper::uploadFile($one_data);
                    if (!isset($oss_info['bucket_name']) || !isset($oss_info['object_key']) || !isset($oss_info['file_name'])) {
                        $result_data[$index][self::$update_result_column] = self::$t['success_but_attachment_error'] . ';';
                        //清空附件,后续不处理
                        $upload_data['attachments'] = [];
                        continue;
                    }
                    $one_data = $oss_info;
                }
            }
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //更新主表数据
            if (!empty($correct_data)) {
                $after_update_data = $before_update_data = [];
                $all_attachment_data = [];
                $all_update_sau_permission = [];
                $all_del_sau_permission_sau_ids = [];
                $sau_job_list = $this->getSauJobIds($sau_data_ids);
                //批量插入material_sau表
                foreach ($correct_data as $k => $correct_v) {
                    $update_data = $attachment_data = $update_sku_data = $before_permission_job = $after_permission_job = $update_sau_permission = $del_sau_permission_sau_ids = [];
                    $update_id = $sau_data_kv[$correct_v['barcode']]['id'];
                    //主表要更新的数据
                    foreach ($correct_v as $field => $v_v) {
                        if ($field == 'barcode') {
                            continue;
                        }

                        if ($field == 'job') {
                            $before_permission_job[$field] = $sau_job_list[$sau_data_kv[$correct_v['barcode']]['id']] ?? '';
                            $after_permission_job[$field] = $v_v;
                        } else if (isset($sau_data_kv[$correct_v['barcode']][$field])) {
                            if ($field == 'update_to_sap') {
                                //如果涉及到了是否更新至SAP的变更, 不论SAP的至是否变更，都需要判断下这个逻辑，因为其他信息被改动了
                                $update_data['is_send_sap'] = StandardService::getInstance()->checkSendSap($v_v,  $sau_data_kv[$correct_v['barcode']]['finance_category_id']);
                            }
                            if ($sau_data_kv[$correct_v['barcode']][$field] != $v_v) {
                                if (!in_array($field, $save_material_sau_sku)) {
                                    //除非SAP字段，其他值发生变更了才记录
                                    $update_data[$field] = $v_v;
                                } else {
                                    //修改领用单位和数量
                                    $update_sku_data[$field] = $v_v;
                                }
                            }
                        }
                    }
                    //图片要更新的数据
                    if (!empty($correct_v['attachments']) && isset($sau_data_kv[$correct_v['barcode']])) {
                        foreach ($correct_v['attachments'] as $oss_attachment) {
                            $attachment_data[] = [
                                'oss_bucket_type' => MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK,
                                'sub_type' => StandardService::DEFAULT_PARAMETER_ZERO,
                                'oss_bucket_key' => $sau_data_kv[$correct_v['barcode']]['id'],
                                'bucket_name' => $oss_attachment['bucket_name'],
                                'object_key' => $oss_attachment['object_key'],
                                'file_name' => $oss_attachment['file_name'],
                                'deleted' => StandardService::DEFAULT_PARAMETER_ZERO,
                                'created_at' => $datetime
                            ];
                        }
                    }
                    //资产可申请职位
                    if (!empty($correct_v['job'])) {
                        foreach ($correct_v['job'] as $job_id) {
                            $job_id = trim($job_id);
                            if (empty($job_id)) {
                                continue;
                            }
                            $update_sau_permission[] = [
                                'sau_id' => $update_id,
                                'job_id' => $job_id,
                                'created_at' => $datetime,
                                'updated_at' => $datetime
                            ];
                            $del_sau_permission_sau_ids[$update_id] = $update_id;
                        }
                    }
                    //前边有验证$sau_data_kv[$correct_v['barcode']]必有值
                    //组合修改后的数据
                    $before_update_data[$k] = array_merge($sau_data_kv[$correct_v['barcode']], $before_permission_job);
                    $after_update_data[$k] = array_merge($sau_data_kv[$correct_v['barcode']], $update_data, $after_permission_job);

                    //主表也不更新, 附属信息也不更新, 啥也没更新, 记录错误
                    if (empty($update_data) && empty($attachment_data) && empty($update_sku_data) && empty($update_sau_permission) && empty($del_sau_permission_sau_ids)) {
                        if (isset($result_data[$k][self::$update_result_column])) {
                            $result_data[$k][self::$update_result_column] .= self::$t['success_but_update_db_error'];
                        } else {
                            $result_data[$k][self::$update_result_column] = self::$t['success_but_update_db_error'];
                        }
                        unset($before_update_data[$k], $after_update_data[$k]);
                        continue;
                    }

                    //更新主表
                    if (!empty($update_data)) {
                        $update_data['updated_at'] = $datetime;
                        $update_success = $db->updateAsDict(
                            'material_sau',
                            $update_data,
                            ['conditions' => 'id=?', 'bind' => $update_id]
                        );
                        if (!$update_success) {
                            throw new BusinessException('更新数据异常,数据: id=' . $update_id . ';data=' . json_encode($update_data, true), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_ERROR);
                        }
                    }

                    //更新sku
                    if (!empty($update_sku_data)) {
                        $update_sku_data['updated_at'] = $datetime;
                        $update_success = $db->updateAsDict(
                            'material_sau_sku',
                            $update_sku_data,
                            ['conditions' => 'sau_id = ' . $update_id]
                        );
                        if (!$update_success) {
                            throw new BusinessException('更新标准型号领用单位数据异常,数据: id=' . $update_id . ';data=' . json_encode($update_sku_data, true), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_ERROR);
                        }
                    }
                    //合并图片,最后一起处理
                    $all_attachment_data = array_merge($all_attachment_data, $attachment_data);
                    //合并要删除可申请权限配置，最后一起处理
                    $all_del_sau_permission_sau_ids = array_merge($all_del_sau_permission_sau_ids, array_values($del_sau_permission_sau_ids));
                    //合并要更新可申请权限配置，最后一起处理
                    $all_update_sau_permission = array_merge($all_update_sau_permission, $update_sau_permission);
                }
                //存在要删除可申请权限配置
                if (!empty($all_del_sau_permission_sau_ids)) {
                    $del_sau_permission_result = MaterialSauPermissionModel::find([
                        'conditions' => 'sau_id in ({sau_ids:array})',
                        'bind' => ['sau_ids' => $all_del_sau_permission_sau_ids]
                    ])->delete();
                    if ($del_sau_permission_result === false) {
                        throw new BusinessException('标准型号删除可申请权限配置失败: 待处理数据: '. json_encode($all_del_sau_permission_sau_ids, JSON_UNESCAPED_UNICODE), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_IMG_ERROR);
                    }
                }
                //存在要更新可申请权限配置
                if (!empty($all_update_sau_permission)) {
                    //执行插入
                    $sau_permission_model = new MaterialSauPermissionModel();
                    if (!$sau_permission_model->batch_insert($all_update_sau_permission)) {
                        throw new BusinessException('标准型号可申请权限配置更新失败: 待处理数据: '. json_encode($all_update_sau_permission, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_permission_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_IMG_ERROR);
                    }
                }
                //批量更新图片
                if (!empty($all_attachment_data)) {
                    //先删除之前的
                    $object_key_arr = array_column($all_attachment_data, 'oss_bucket_key');
                    $object_key_arr = array_values(array_unique($object_key_arr));
                    $object_key_ids = implode(',', $object_key_arr);
                    $db->updateAsDict(
                        'material_attachment',
                        ['deleted' => MaterialClassifyEnums::IS_DELETED_YES],
                        ['conditions' => "oss_bucket_key in ({$object_key_ids}) and oss_bucket_type = " . MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK]
                    );
                    //执行插入
                    $am_model = new MaterialAttachmentModel();
                    if (!$am_model->batch_insert($all_attachment_data)) {
                        throw new BusinessException('标准型号图片更新失败: 待处理数据: '. json_encode($all_attachment_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($am_model), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_IMG_ERROR);
                    }
                }
                // 批量插入同步scm的任务
                if (!empty($after_update_data)) {
                    $insert_scm = [];
                    foreach ($after_update_data as $scm_data) {
                        if ($scm_data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                            $insert_scm[] = $scm_data['id'];
                        }
                    }
                    if (!empty($insert_scm)) {
                        $scm = new ScmService();
                        $bool = $scm->saveMaterialSyncScmLogBatch($insert_scm, MaterialClassifyEnums::TO_SCM_SYNC_TYPE_UPDATE, $datetime);
                        if ($bool === false) {
                            throw new BusinessException('标准型号添加同步至scm记录失败 = ' . json_encode($insert_scm, JSON_UNESCAPED_UNICODE), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_SYNC_SCM_ERROR);
                        }
                    }
                }
                //批量插入操作记录
                $update_log_model = new MaterialUpdateLogModel();
                $log_bool = $update_log_model->dealEditFieldBatch($before_update_data, $after_update_data, $user, $datetime);
                if ($log_bool === false) {
                    throw new BusinessException('标准型号添加操作记录失败 = ' . json_encode(array_column($after_update_data, 'id'), JSON_UNESCAPED_UNICODE), ErrCode::$STANDARD_IMPORT_EXCEL_DATA_UPDATE_EDIT_LOG_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace = $e->getTraceAsString();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace = $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('barcodeBatchUploadEdit-标准型号导入更新失败-message=' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data = $clear_excel_data;
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $result_data,
                'all_num' => count($clear_excel_data),
                'success_num' => count($correct_data),
                'failed_sum' => count($error_data)
            ]
        ];
    }

    /**
     * 获取每个标准型号设置的职位
     * @param array $sau_id 标准型号id组
     * @return array
     */
    public function getSauJobIds($sau_id)
    {
        $sau_job_list = MaterialSauPermissionModel::find([
            'columns' => 'GROUP_CONCAT(job_id) AS job_id, sau_id',
            'conditions' => 'sau_id in ({sau_ids:array})',
            'bind' => ['sau_ids' => $sau_id],
            'group' => 'sau_id'
        ])->toArray();
        return array_column($sau_job_list, 'job_id', 'sau_id');
    }

    /**
     * EXCEL数据转为关联数组(导入新增)
     * @param $excel_data
     * @return array
     * @throws ValidationException
     * @date 2022/7/18
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'barcode',
            'name_zh',//物料中文名称
            'name_en',//物料英文名称
            'name_local',//当地语言名称
            'model',//规格型号
            'category_type',//物料类型
            'category_id',//物料分类编码
            'finance_category_id',//财务分类编码
            'unit',// 基本单位
            'update_to_scm',//是否更新至scm
            'enable_asset_code',//是否启用资产码
            'enable_sn',//是否启用sn
            'update_to_sap',//是否更新至SAP，1否，2是
            'update_to_acceptance',//是否验收
            'use_scene',//可申请/购买
            'transfer_forbid', //是否限制员工互转
            'is_contain_job',//是否包含职位：1包含，2不包含
            'job',//资产可申请职位
            'brand',//品牌
            'remark',//备注
            'use_unit',//领用单位
            'use_val',//领用单位数量
            'small_bag_unit',//小包装单位
            'small_bag_val',//小包装数量
            'big_bag_unit',//大包装单位
            'big_bag_val',//大包装数量
            'attachments',//图片链接
            'price',//参考单价
            'currency',//币种
        ];
        $data = [];

        foreach ($excel_data as $k=>$v) {
            foreach ($data_key as $index => $key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'], ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        foreach ($data as $key => &$value) {
            //物料类型 eg:1-资产asset 转为 1
            $value['category_type'] = intval(explode('-', $value['category_type'])[0]);
            //是否更新至SCM
            if ($value['update_to_scm'] == 'Y') {
                $value['update_to_scm'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else if($value['update_to_scm'] == 'N') {
                $value['update_to_scm'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //是否启用资产码，1否，2是
            if ($value['enable_asset_code'] == 'Y') {
                $value['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else {
                $value['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //是否启用sn，1否，2是
            if ($value['enable_sn'] == 'Y') {
                $value['enable_sn'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else {
                $value['enable_sn'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //是否更新至SAP，1否，2是
            if ($value['update_to_sap'] == 'Y') {
                $value['update_to_sap'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else if ($value['update_to_sap'] == 'N') {
                $value['update_to_sap'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //是否验收
            if ($value['update_to_acceptance'] == 'Y') {
                $value['update_to_acceptance'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else if($value['update_to_acceptance'] == 'N') {
                $value['update_to_acceptance'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //可申请/可购买
            $value['use_scene'] = !empty($value['use_scene']) ? intval(explode('-', $value['use_scene'])[0]) : 0;
            //是否限制员工互转
            if ($value['transfer_forbid'] == 'Y') {
                $value['transfer_forbid'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_YES;
            } elseif ($value['transfer_forbid'] == 'N') {
                $value['transfer_forbid'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO;
            } else {
                $value['transfer_forbid'] = 0;
            }

            //是否包含职位
            if ($value['is_contain_job'] == 'Y') {
                $value['is_contain_job'] = MaterialClassifyEnums::IS_CONTAIN_JOB_YES;
            } elseif ($value['is_contain_job'] == 'N') {
                $value['is_contain_job'] = MaterialClassifyEnums::IS_CONTAIN_JOB_NO;
            } else {
                $value['is_contain_job'] = 0;
            }

            //资产可申请职位
            $value['job'] = $this->checkSauJob($value['job']);

            //参考单价,不填默认0
            if (empty($value['price'])) {
                $value['price'] = 0;
            }
            //币种
            if (!empty($value['currency'])) {
                $value['currency'] = explode('-', $value['currency'])[0];
            }
            //图片逗号分割
            if(!empty($value['attachments'])) {
                if (stristr($value['attachments'], ',') !== false) {
                    $value['attachments'] = explode(',', $value['attachments']);
                } else if (stristr($value['attachments'], '，') !== false) {
                    $value['attachments'] = explode('，', $value['attachments']);
                } else {
                    $value['attachments'] = [$value['attachments']];
                }
            } else {
                $value['attachments'] = [];
            }
        }
        return $data;
    }

    /**
     * EXCEL数据转为关联数组(导入修改)
     * @param $excel_data
     * @return array
     * @throws ValidationException
     * @date 2022/7/18
     */
    public function excelToDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'barcode',
            'name_zh',//物料中文名称
            'name_en',//物料英文名称
            'name_local',//当地语言名称
            'model',//规格型号
            'update_to_sap',//是否更新至SAP，1否，2是
            'update_to_acceptance',//是否验收
            'use_scene',//可申请/购买
            'transfer_forbid',//是否限制员工互转
            'is_contain_job',//是否包含职位
            'job',//资产可申请职位
            'brand',//品牌
            'remark',//备注
            'use_unit',//领用单位
            'use_val',//领用单位数量
            'attachments',//图片链接
            'price',//参考单价
            'currency',//币种
        ];
        $data = [];
        foreach ($excel_data as $k=>$v) {
            foreach ($data_key as $index=>$key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'], ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        foreach ($data as $key => &$value) {
            //是否更新至SAP
            if ($value['update_to_sap'] == 'Y') {
                $value['update_to_sap'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else if($value['update_to_sap'] == 'N') {
                $value['update_to_sap'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //是否验收
            if ($value['update_to_acceptance'] == 'Y') {
                $value['update_to_acceptance'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else if($value['update_to_acceptance'] == 'N') {
                $value['update_to_acceptance'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            //可申请/可购买
            $value['use_scene'] = !empty($value['use_scene']) ? intval(explode('-', $value['use_scene'])[0]) : 0;
            //是否限制员工互转
            if ($value['transfer_forbid'] == 'Y') {
                $value['transfer_forbid'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_YES;
            } elseif ($value['transfer_forbid'] == 'N') {
                $value['transfer_forbid'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO;
            } else {
                $value['transfer_forbid'] = 0;
            }

            //是否包含职位
            if ($value['is_contain_job'] == 'Y') {
                $value['is_contain_job'] = MaterialClassifyEnums::IS_CONTAIN_JOB_YES;
            } elseif ($value['is_contain_job'] == 'N') {
                $value['is_contain_job'] = MaterialClassifyEnums::IS_CONTAIN_JOB_NO;
            } else {
                $value['is_contain_job'] = 0;
            }

            //资产可申请职位
            $value['job'] = $this->checkSauJob($value['job']);

            //币种
            if (!empty($value['currency'])) {
                $value['currency'] = explode('-',$value['currency'])[0];
            }
            //图片逗号分割
            if(!empty($value['attachments'])) {
                if (stristr($value['attachments'],',') !== false) {
                    $value['attachments'] = explode(',',$value['attachments']);
                } else if (stristr($value['attachments'],'，') !== false) {
                    $value['attachments'] = explode('，',$value['attachments']);
                } else {
                    $value['attachments'] = [$value['attachments']];
                }
            } else {
                $value['attachments'] = [];
            }
            //空值不进行修改
            foreach ($value as $field =>$v) {
                if ($v == '') {
                    unset($value[$field]);
                }
                if ($field == 'attachments' && empty($v)) {
                    unset($value[$field]);
                }
            }
        }
        return $data;
    }

    /**
     * 检测资产可申请职位
     * @param string $job_id_str 职位id串
     * @return array
     */
    public function checkSauJob($job_id_str)
    {
        $job = [];
        //逗号分割
        if(!empty($job_id_str)) {
            $job = array_filter(explode(',', trim(trim(str_replace('，', ',', $job_id_str), ','))));
            foreach ($job as &$item) {
                $item = trim($item);
            }
        }
        return $job;
    }

    /**
     * 批量检测barcode是否存在
     * @param $barcode_arr
     * @return array
     */
    public function isExistsBarcodeBatch($barcode_arr)
    {
        if (empty($barcode_arr)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ms.id', 'ms.barcode', 'ms.name_zh', 'ms.name_en', 'ms.name_local', 'ms.model', 'ms.brand', 'ms.remark', 'ms.price', 'ms.currency', 'ms.update_to_sap', 'ms.update_to_acceptance', 'ms.update_to_scm', 'ms.finance_category_id',  'mss.use_unit_zh','mss.use_unit_en', 'mss.use_val', 'ms.transfer_forbid', 'ms.use_scene', 'ms.category_type', 'ms.is_contain_job'
        ]);
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialSauSkuModel::class, 'mss.sau_id = ms.id','mss');
        $builder->where('ms.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('ms.barcode', $barcode_arr);
        return   $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取开启的职位组
     * @param array $job_arr 职位id组
     * @return array
     */
    public function getOpenJobIds($job_arr)
    {
        if (empty($job_arr)) {
            return [];
        }
        $job_arr_ids = [];
        foreach ($job_arr as $item) {
            if (empty($item)) {
                continue;
            }
            $job_arr_ids = array_merge($job_arr_ids, $item);
        }
        $job_arr_ids = array_values(array_unique(array_filter($job_arr_ids)));
        if ($job_arr_ids) {
            $job_id_arr = HrJobTitleModel::find([
                'columns' => 'id',
                'conditions' => 'id in ({ids:array}) and status = :status:',
                'bind' => ['ids' => $job_arr_ids, 'status' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]
            ])->toArray();
            return array_column($job_id_arr, 'id');
        }
        return [];
    }

    /**
     * 导入新增
     * 验证数据, 错误信息放到error_message
     * @param array $data excel内容数据
     * @param array $barcode_arr barcode组
     * @return mixed
     * @date 2022/7/19
     */
    public function validationBatch($data, $barcode_arr)
    {
        //验证barcode在数据库中是否重复
        $is_exist_barcode = $this->isExistsBarcodeBatch(array_values(array_unique($barcode_arr)));
        $is_exist_barcode = array_column($is_exist_barcode, 'barcode', 'id');
        //查询所有物料分类编码是否是末级
        $category_id_arr = array_column($data, 'category_id');
        $category_id_arr = array_values(array_unique($category_id_arr));
        list($id_code_kv, $has_child_data) = (new MaterialCategoryModel())->getHasChildrenData($category_id_arr);
        //查询所有财务编码是否是末级
        $finance_category_id_arr = array_column($data, 'finance_category_id');
        $finance_category_id_arr = array_values(array_unique($finance_category_id_arr));
        list($id_data_kv_finance, $finance_has_child_data) = (new MaterialFinanceCategoryModel())->getHasChildrenData($finance_category_id_arr);
        $id_code_kv_finance = array_column($id_data_kv_finance, 'code', 'id');
        //查询基本单位
        $attribute = ClassifyService::getInstance()->getAttributeValueArr();
        $attribute_zh = array_column($attribute, null, 'name_zh');
        $attribute_en = array_column($attribute, null, 'name_en');
        $correct_data = $error_data = [];
        //16850需求，查询所有职位信息
        $job_open_ids = $this->getOpenJobIds(array_column($data, 'job'));
        //获取配置图片oss域名
        //$config = $this->getDI()->get('config');
        //$img_prefix = $config->application->img_prefix;
        //$unset_http = stristr($img_prefix,'http://');
        //$unset_https = stristr($img_prefix,'https://');
        //if ($unset_http !== false){
        //    $img_prefix = mb_substr($img_prefix,mb_strlen('http://'));
        //}elseif ($unset_https !== false){
        //    $img_prefix = mb_substr($img_prefix,mb_strlen('https://'));
        //}
        //获取系统币种,和国家默认币种
        GlobalEnums::init();

        foreach ($data as $k => $v) {
            $error_message = '';
            if(empty(trim($v['barcode']))) {
                $error_message .= self::$t['material_barcode_not_null'] .  ' ; ';
            }
            //必填验证
            if (empty($v['barcode']) || empty($v['name_zh']) || empty($v['name_en']) || empty($v['category_type'])
                || empty($v['category_id']) || empty($v['finance_category_id']) || empty($v['unit']) || empty($v['update_to_scm'])
                || empty($v['update_to_acceptance']) || empty($v['update_to_sap']) || empty($v['attachments'])
            ) {
                $error_message .= self::$t['error_message_required'] .  ' ; ';
            }
            //验证文件中barcode是否重复
            $barcode_numbers = count(array_keys($barcode_arr, $v['barcode']));
            if ($barcode_numbers > 1) {
                $error_message .= self::$t['error_message_barcode_inner_repeat'] .  ' ; ';
            }
            if (!empty($is_exist_barcode) && in_array($v['barcode'], $is_exist_barcode)) {
                $error_message .= self::$t['error_message_barcode_db_repeat'] .  ' ; ';
            }
            //物料名称验证
            if (mb_strlen($v['name_zh']) > 100) {
                $error_message .= self::$t['material_assetname'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            if (mb_strlen($v['name_en']) > 100) {
                $error_message .= self::$t['material_assetname_en'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            if (mb_strlen($v['name_local']) > 100) {
                $error_message .= self::$t['material_assetname_local'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            if (mb_strlen($v['model']) > 100) {
                $error_message .= self::$t['material_asset.model'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            if (mb_strlen($v['brand']) > 30) {
                $error_message .= self::$t['material_asset.brand'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            if (mb_strlen($v['remark']) > 500) {
                $error_message .= self::$t['material_asset.mark'].self::$t['error_message_beyond_maximum_length'] .  ' ; ';
            }
            //物料类型验证
            if (!in_array($v['category_type'], [MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS, MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_SERVICE])) {
                $error_message .= self::$t['error_message_category_type_error'] .  ' ; ';
            }
            //物料分类编码验证
            if (!in_array($v['category_id'], $id_code_kv) || in_array($v['category_id'], $has_child_data)) {
                //不存在物料分类编码, 或 非最子级
                $error_message .= self::$t['error_message_category_id_error'] .  ' ; ';
            }
            //财务分类验证
            if (!in_array($v['finance_category_id'], $id_code_kv_finance) || in_array($v['finance_category_id'], $finance_has_child_data)) {
                $error_message .= self::$t['error_message_finance_category_id_error'] .  ' ; ';
            }
            //基本单位验证
            if (!key_exists($v['unit'], $attribute_zh) && !key_exists($v['unit'], $attribute_en)) {
                $error_message .= self::$t['error_message_unit_error'] .  ' ; ';
            }
            //是否更新至SCM
            if (!in_array($v['update_to_scm'], [MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, MaterialClassifyEnums::MATERIAL_CATEGORY_NO])) {
                $error_message .= self::$t['error_message_update_to_scm_error'] .  ' ; ';
            }
            //是否更新至SAP，1否，2是
            if (!in_array($v['update_to_sap'], [MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, MaterialClassifyEnums::MATERIAL_CATEGORY_NO])) {
                $error_message .= self::$t['error_message_update_to_sap_error'] .  ' ; ';
            }
            //是否验收
            if (!in_array($v['update_to_acceptance'], [MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, MaterialClassifyEnums::MATERIAL_CATEGORY_NO])) {
                $error_message .= self::$t['error_message_update_to_acceptance_error'] .  ' ; ';
            }
            //可申请/可购买
            if (empty($v['use_scene'])) {
                $error_message .= self::$t['error_message_use_scene_empty'] .  ' ; ';
            } else if (!key_exists($v['use_scene'], MaterialClassifyEnums::$material_use_scene)) {
                $error_message .= self::$t['error_message_use_scene_error'] .  ' ; ';
            }
            //可申请职位
            if (!empty($v['job'])) {
                if ($v['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    //物料类型不是资产,仅有资产的SKU可以关联职位
                    $error_message .= self::$t['material_sau_job_category_type_error'] .  ' ; ';
                }
                if (!in_array($v['is_contain_job'], [MaterialClassifyEnums::IS_CONTAIN_JOB_YES, MaterialClassifyEnums::IS_CONTAIN_JOB_NO])) {
                    //配置职位的时候，包含，不包含必须选择。
                    $error_message .= self::$t['material_sau_job_contain_error'] .  ' ; ';
                }

                //最多可以选择100个职位
                if (count($v['job']) > 100) {
                    $error_message .= self::$t['material_sau_job_limit_error'] .  ' ; ';
                } else if (array_diff($v['job'], $job_open_ids)) {
                    $error_message .= self::$t['material_sau_job_error'] .  ' ; ';
                }
            }
            //小包装单位和数量 和 大包装单位和数量
            if (!empty($v['small_bag_unit'])) {
                if (!key_exists($v['small_bag_unit'], $attribute_zh) && !key_exists($v['small_bag_unit'], $attribute_en)) {
                    $error_message .= self::$t['error_message_small_bag_unit_error'] .  ' ; ';
                }
                if (empty($v['small_bag_val']) || !is_numeric($v['small_bag_val']) || $v['small_bag_val'] <= 1) {
                    $error_message .= self::$t['error_message_small_bag_val_error'] .  ' ; ';
                }
                //大包装单位和数量
                if (!empty($v['big_bag_unit'])) {
                    if (!key_exists($v['big_bag_unit'], $attribute_zh) && !key_exists($v['big_bag_unit'], $attribute_en)) {
                        $error_message .= self::$t['error_message_big_bag_unit_error'] .  ' ; ';
                    }
                    if (empty($v['big_bag_val']) || !is_numeric($v['big_bag_val']) || $v['big_bag_val'] <= 1) {
                        $error_message .= self::$t['error_message_big_bag_val_error'] .  ' ; ';
                    }
                }
                //领用单位
                if (!empty($v['use_unit'])) {
                    if (!key_exists($v['use_unit'], $attribute_zh) && !key_exists($v['use_unit'], $attribute_en)) {
                        $error_message .= self::$t['error_message_use_unit_error'] . ';';
                    }
                    if (empty($v['use_val']) || !is_numeric($v['use_val']) || $v['use_val'] < 0  || $v['use_val'] > 999999999 ) {
                        $error_message .= self::$t['error_message_use_val_error'] . ';';
                    }
                }
            } else {
                //小包装没填, 大包装也不能填 , 小包装单位没填也不能填数量
                if (!empty($v['big_bag_unit']) || !empty($v['big_bag_val']) || !empty($v['small_bag_val'])) {
                    $error_message .= self::$t['error_message_big_bag_must_small_bag_error'] .  ' ; ';
                }
            }
            //图片不能超过5个
            if (count($v['attachments']) > StandardService::MATERIAL_MAX_PIC) {
                $error_message .= self::$t['error_message_attachments_max_5'] .  ' ; ';
            }
            //图片链接验证
            foreach ($v['attachments'] as $img_url) {
                //if (stripos($img_url, $img_prefix) === false) {
                //    $error_message .= self::$t['error_message_attachments_url_error'] .  ' ; ';
                //}
                if (!preg_match('/.*?(.jpg|.png|.jpeg)$/is', $img_url)) {
                    $error_message .= self::$t['error_message_attachments_url_extension_error'] .  ' ; ';
                }
            }
            //参考单价
            if (!empty($v['price']) && !preg_match(MaterialClassifyEnums::$sau_validation['price'], $v['price'])) {
                $error_message .= self::$t['error_message_price_error'] . ';';
            }
            //币种
            if (!empty($v['currency']) && (!key_exists($v['currency'], GlobalEnums::$currency_symbol_map))) {
                $error_message .= self::$t['error_message_currency_error'] .  ' ; ';
            }
            //资产类 && 更新至SCM时，是否开启资产必须是Y
            if ($v['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && $v['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $v['enable_asset_code'] != MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $error_message .= self::$t['error_message_enable_asset_code_error'] .  ' ; ';
            }
            //拼接获取错误信息,和正确信息
            if ($error_message !== '') {
                $v['error_message'] = self::$t['error_message_title'].' : '.$error_message;
                $error_data[$k] = $v;
            } else {
                $correct_data[$k] = $v;
            }
        }
        //正确信息,处理单位和默认值
        if (!empty($correct_data)) {
            $code_id_kv_finance = array_flip($id_code_kv_finance);
            $code_id_kv = array_flip($id_code_kv);
            foreach ($correct_data as $index => &$correct_v) {
                //基本单位
                $correct_v['unit_zh'] = $correct_v['unit_en'] = '';
                if (key_exists($correct_v['unit'], $attribute_zh)) {
                    $correct_v['unit_zh'] = $attribute_zh[$correct_v['unit']]['name_zh'];
                    $correct_v['unit_en'] = $attribute_zh[$correct_v['unit']]['name_en'];
                } elseif (key_exists($correct_v['unit'], $attribute_en)) {
                    $correct_v['unit_zh'] = $attribute_en[$correct_v['unit']]['name_zh'];
                    $correct_v['unit_en'] = $attribute_en[$correct_v['unit']]['name_en'];
                }

                //领用单位
                $correct_v['use_unit_zh'] = $correct_v['use_unit_en'] = '';
                if (!empty($correct_v['use_unit'])) {
                    if (key_exists($correct_v['use_unit'], $attribute_zh)) {
                        $correct_v['use_unit_zh'] = $attribute_zh[$correct_v['use_unit']]['name_zh'];
                        $correct_v['use_unit_en'] = $attribute_zh[$correct_v['use_unit']]['name_en'];
                    } elseif (key_exists($correct_v['use_unit'], $attribute_en)) {
                        $correct_v['use_unit_zh'] = $attribute_en[$correct_v['use_unit']]['name_zh'];
                        $correct_v['use_unit_en'] = $attribute_en[$correct_v['use_unit']]['name_en'];
                    }
                }
                //小包装单位
                $correct_v['small_bag_unit_zh'] = $correct_v['small_bag_unit_en'] = '';
                if (!empty($correct_v['small_bag_unit'])) {
                    if (key_exists($correct_v['small_bag_unit'], $attribute_zh)) {
                        $correct_v['small_bag_unit_zh'] = $attribute_zh[$correct_v['small_bag_unit']]['name_zh'];
                        $correct_v['small_bag_unit_en'] = $attribute_zh[$correct_v['small_bag_unit']]['name_en'];
                    } elseif (key_exists($correct_v['small_bag_unit'], $attribute_en)) {
                        $correct_v['small_bag_unit_zh'] = $attribute_en[$correct_v['small_bag_unit']]['name_zh'];
                        $correct_v['small_bag_unit_en'] = $attribute_en[$correct_v['small_bag_unit']]['name_en'];
                    }
                }
                //大包装单位
                $correct_v['big_bag_unit_zh'] = $correct_v['big_bag_unit_en'] = '';
                if (!empty($correct_v['big_bag_unit'])) {
                    if (key_exists($correct_v['big_bag_unit'], $attribute_zh)) {
                        $correct_v['big_bag_unit_zh'] = $attribute_zh[$correct_v['big_bag_unit']]['name_zh'];
                        $correct_v['big_bag_unit_en'] = $attribute_zh[$correct_v['big_bag_unit']]['name_en'];
                    } elseif (key_exists($correct_v['big_bag_unit'], $attribute_en)) {
                        $correct_v['big_bag_unit_zh'] = $attribute_en[$correct_v['big_bag_unit']]['name_zh'];
                        $correct_v['big_bag_unit_en'] = $attribute_en[$correct_v['big_bag_unit']]['name_en'];
                    }
                }
                //sap_unit 默认 EA
                $correct_v['sap_unit'] = 'EA';
                //是否启用资产码, 如果更新至scm=是（2）&物料类型=资产（1）则enable_asset_code=2(是)；否则=1（否）
                if ($correct_v['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $correct_v['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    $correct_v['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
                } else {
                    $correct_v['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
                }
                // 是否启用sn，非资产类，sn不开启；资产类，按照业务选择为准
                if ($correct_v['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    $correct_v['enable_sn'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
                }
                //财务分类和资产分类由code转成id
                $correct_v['finance_category_id'] = $code_id_kv_finance[$correct_v['finance_category_id']];
                $correct_v['category_id'] = $code_id_kv[$correct_v['category_id']];
                //默认币种
                if (empty($correct_v['currency'])) {
                    $correct_v['currency'] = GlobalEnums::$sys_default_currency;
                }
                //采购类型通过财务分类确定
                $correct_v['purchase_type'] = $id_data_kv_finance[$correct_v['finance_category_id']]['purchase_type'];

                //基础验证
                try {
                    $params = BaseService::handleParams($correct_v, StandardService::$not_must_add_params);
                    Validation::validate($params, StandardService::$validate_material_sau);
                } catch (ValidationException $e) {
                    unset($correct_data[$index]);
                    $error_data[$index]['error_message'] = self::$t['error_message_title'].' : '.$e->getMessage();
                }
            }
        }
        return ['correct_data'=>$correct_data,'error_data'=>$error_data];
    }

    /**
     * 导入修改
     * 验证数据, 错误信息放到error_message
     * @param array $data excel中的数据
     * @param array $barcode_arr excel中的所有barcode
     * @param array $sau_data_kv 本次excel在sau表中的数据,key是barcode,value是本条数据一维数组
     * @param array $save_material_sau_sku 本次excel在sku表中数据
     * @return mixed
     * @date 2022/7/19
     */
    public function validationBatchEdit($data,$barcode_arr,$sau_data_kv, $save_material_sau_sku)
    {
        $correct_data = $error_data = [];

        $attribute = ClassifyService::getInstance()->getAttributeValueArr();
        $attribute_zh = array_column($attribute, null, 'name_zh');
        $attribute_en = array_column($attribute, null, 'name_en');

        //16850需求，查询所有职位信息
        $job_open_ids = $this->getOpenJobIds(array_column($data, 'job'));
        foreach ($data as $k => &$v) {
            $error_message = '';
            //barcode验证
            if (!isset($v['barcode']) || empty($v['barcode'])) {
                $error_message .= self::$t['error_message_edit_barcode_required'] . ' ; ';
            } else {
                //验证文件中barcode是否存在
                $barcode_numbers = count(array_keys($barcode_arr, $v['barcode']));
                if ($barcode_numbers > 1) {
                    $error_message .= self::$t['error_message_barcode_inner_repeat'] . ' ; ';
                }
                if (empty($sau_data_kv) || !key_exists($v['barcode'], $sau_data_kv)) {
                    $error_message .= self::$t['error_message_barcode_db_not_exist'] . ' ; ';
                }
            }

            //物料名称验证
            if (isset($v['name_zh']) && mb_strlen($v['name_zh']) > 100) {
                $error_message .= self::$t['material_assetname'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }
            if (isset($v['name_en']) && mb_strlen($v['name_en']) > 100) {
                $error_message .= self::$t['material_assetname_en'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }
            if (isset($v['name_local']) && mb_strlen($v['name_local']) > 100) {
                $error_message .= self::$t['material_assetname_local'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }
            if (isset($v['model']) && mb_strlen($v['model']) > 100) {
                $error_message .= self::$t['material_asset.model'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }
            if (isset($v['brand']) && mb_strlen($v['brand']) > 30) {
                $error_message .= self::$t['material_asset.brand'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }
            if (isset($v['remark']) && mb_strlen($v['remark']) > 500) {
                $error_message .= self::$t['material_asset.mark'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
            }

            //领用单位
            $v['use_unit_zh'] = $v['use_unit_en'] = '';
            if (!empty($v['use_unit'])) {
                if (key_exists($v['use_unit'], $attribute_zh)) {
                    $v['use_unit_zh'] = $attribute_zh[$v['use_unit']]['name_zh'];
                    $v['use_unit_en'] = $attribute_zh[$v['use_unit']]['name_en'];
                } elseif (key_exists($v['use_unit'], $attribute_en)) {
                    $v['use_unit_zh'] = $attribute_en[$v['use_unit']]['name_zh'];
                    $v['use_unit_en'] = $attribute_en[$v['use_unit']]['name_en'];
                } else {
                    $error_message .= self::$t['error_message_use_unit_error'] . ' ; ';
                }
            }

            if (isset($v['use_val']) && ($v['use_val'] < 0 || $v['use_val'] > 999999999)) {
                $error_message .= self::$t['error_message_use_val_error'] . ' ; ';
            }

            //是否更新至SAP
            if (isset($v['update_to_sap']) && !in_array($v['update_to_sap'], [MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, MaterialClassifyEnums::MATERIAL_CATEGORY_NO])) {
                $error_message .= self::$t['error_message_update_to_sap_error'] . ' ; ';
            }
            //是否验收
            if (isset($v['update_to_acceptance']) && !in_array($v['update_to_acceptance'], [MaterialClassifyEnums::MATERIAL_CATEGORY_OFF, MaterialClassifyEnums::MATERIAL_CATEGORY_NO])) {
                $error_message .= self::$t['error_message_update_to_acceptance_error'] . ' ; ';
            }
            //可申请/购买
            if (isset($v['use_scene']) && !empty($v['use_scene']) && !key_exists($v['use_scene'], MaterialClassifyEnums::$material_use_scene)) {
                $error_message .= self::$t['error_message_use_scene_error'] .  ' ; ';
            }
            //是否限制员工互转
            if (!empty($v['transfer_forbid']) && !in_array($v['transfer_forbid'], [MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_YES, MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO])) {
                $error_message .= self::$t['error_message_transfer_forbid_error'] . ' ; ';
            }
            //可申请职位
            if (!empty($v['job'])) {
                if ($sau_data_kv[$v['barcode']]['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                    //物料类型不是资产,仅有资产的SKU可以关联职位
                    $error_message .= self::$t['material_sau_job_category_type_error'] .  ' ; ';
                }

                if (!in_array($v['is_contain_job'], [MaterialClassifyEnums::IS_CONTAIN_JOB_YES, MaterialClassifyEnums::IS_CONTAIN_JOB_NO])) {
                    //配置职位的时候，包含，不包含必须选择。
                    $error_message .= self::$t['material_sau_job_contain_error'] .  ' ; ';
                }

                //最多可以选择100个职位
                if (count($v['job']) > 100) {
                    $error_message .= self::$t['material_sau_job_limit_error'] .  ' ; ';
                } else if (array_diff($v['job'], $job_open_ids)) {
                    $error_message .= self::$t['material_sau_job_error'] .  ' ; ';
                }
            }
            //图片不能超过5个
            if (isset($v['attachments'])) {
                if (count($v['attachments']) > StandardService::MATERIAL_MAX_PIC) {
                    $error_message .= self::$t['error_message_attachments_max_5'] . ' ; ';
                }
                //图片链接验证
                foreach ($v['attachments'] as $img_url) {
                    if (!preg_match('/.*?(.jpg|.png|.jpeg)$/is', $img_url)) {
                        $error_message .= self::$t['error_message_attachments_url_extension_error'] . ' ; ';
                    }
                }
            }
            //参考单价
            if (isset($v['price']) && !preg_match(MaterialClassifyEnums::$sau_validation['price'], $v['price'])) {
                $error_message .= self::$t['error_message_price_error'] . ' ; ';
            }
            //币种
            if (isset($v['currency']) && !key_exists($v['currency'], GlobalEnums::$currency_item)) {
                $error_message .= self::$t['error_message_currency_error'] . ' ; ';
            }
            //如果没有错误信息,判断修改范围,除了barcode和币种以外,无任何更改,则无需更新
            //如果图片存在,就不判断了, 如果填了图片肯定要修改
            if ($error_message == '') {
                $other_is_empty = true;
                foreach ($v as $field => $v_v) {
                    if ($field == 'barcode' || $field == 'currency') {
                        continue;
                    }
                    if (isset($sau_data_kv[$v['barcode']][$field]) && $sau_data_kv[$v['barcode']][$field] != $v_v || !empty($v['attachments'])|| !empty($save_material_sau_sku[$field]) || !empty($v['job'])) {
                        $other_is_empty = false;
                    }
                }
                //除了barcode和币种以外,无任何更改,则无需更新
                if ($other_is_empty) {
                    $error_message .= self::$t['error_message_not_edit_field'] . ';';
                }
            }
            //拼接获取错误信息,和正确信息
            if ($error_message !== '') {
                $v['error_message'] = self::$t['error_message_title'] . ' : ' . $error_message;
                $error_data[$k] = $v;
            } else {
                $correct_data[$k] = $v;
            }
        }

        return ['correct_data' => $correct_data, 'error_data' => $error_data];
    }
    /**
     * 从oss链接url中截取oss信息  必须是oss地址
     * @param string $url  oss文件链接
     * @return array
     * @date 2022/7/20
     */
    public function getOssInfoByUrl($url)
    {
        $oss_info = [
            'bucket_name' => '',
            'object_key' => '',
            'file_name' => '',
        ];
        //取bucket_name 把开头的http://去掉
        $have_http = stripos($url,'http://');
        $have_https = stripos($url,'https://');
        $first_point = stripos($url,'.');
        if ($have_http !== false){
            $start_index = $have_http+mb_strlen('http://');
            $end_index = $first_point-$have_http;
            $oss_info['bucket_name'] = mb_substr($url,$start_index,$end_index);
        }elseif($have_https !== false){
            $start_index = $have_https+mb_strlen('https://');
            $end_index = $first_point-$start_index;
            $oss_info['bucket_name'] = mb_substr($url,$start_index,$end_index);
        }else{
            $oss_info['bucket_name'] = mb_substr($url,0,$first_point);
        }
        //取object_key, 取第一个.com/后边的所有字符
        $bucket_name_index = stripos($url,'.com/');
        $oss_info['object_key'] = mb_substr($url,$bucket_name_index+mb_strlen('.com/'));
        //拼接file_name, 使用md5后的object_key, 再拼上原来的后缀
        $file_name_str = md5($oss_info['object_key']);
        $oss_info['file_name'] = 'import_'.$file_name_str;
        $extension = strchr($oss_info['object_key'],'.');
        $oss_info['file_name'] = $oss_info['file_name'].$extension;
        return $oss_info;
    }
}
