<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\MaterialSettingEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MaterialInventoryGroupModel;
use App\Models\oa\MaterialInventoryGroupStaffModel;
use App\Models\oa\MaterialSetDataPermissionGroupDepartmentModel;
use App\Models\oa\MaterialSetDataPermissionGroupModel;
use App\Models\oa\MaterialSetDataPermissionGroupStaffModel;
use App\Models\oa\MaterialSetReturnRepairGroupModel;
use App\Models\oa\MaterialSetReturnRepairGroupStaffModel;
use App\Models\oa\MaterialSetReturnRepairMapBarcodeModel;
use App\Models\oa\MaterialSetReturnRepairMapCategoryModel;
use App\Models\oa\MaterialSetReturnRepairMapDepartmentModel;
use App\Models\oa\MaterialSetReturnRepairMapModel;
use App\Models\oa\MaterialSetReturnRepairTypeModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialSauRepository;

class MaterialSettingService extends BaseService
{

    public static $validate_save = [
        'is_check'=>'Required|IntIn:0,1',//使用人工号,
        'department_ids' => 'Arr',
    ];

    /**
     * 资产盘点组
     * @var array
     */
    public static $validate_inventory_group_list = [
        'name' => 'StrLenGeLe:2,100',
        'staff_id' => 'IntGt:0',
        'pageSize' => 'IntGt:0',
        'pageNum' => 'IntGt:0',
    ];

    /**
     * 资产盘点组 - 新增/编辑
     * @var array
     */
    public static $validate_inventory_group_add = [
        'name' => 'Required|StrLenGeLe:1,100',
        'rule_url' => 'StrLenGeLe:0,500',
        'q_a_url' => 'StrLenGeLe:0,500',
        'staffs' => 'Arr|ArrLenGeLe:0,100',
        'staffs[*]' => 'IntGt:0'
    ];

    /**
     * 资产盘点组 - 编辑/删除/查看
     * @var array
     */
    public static $validate_inventory_group_id = [
        'id' => 'Required|IntGt:0',
    ];

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'department_ids',
    ];

    /**
     * 资产退回组
     * @var array
     */
    public static $validate_return_repair_group_list = [
        'name' => 'StrLenGeLe:1,50',
        'staff_id' => 'IntGt:0',
        'pageSize' => 'IntGt:0',
        'pageNum' => 'IntGt:0',
    ];

    /**
     * 资产退回组 - 新增/编辑
     * @var array
     */
    public static $validate_return_repair_group_add = [
        'name_zh' => 'Required|StrLenGeLe:1,50',
        'name_en' => 'Required|StrLenGeLe:1,50',
        'name_local' => 'Required|StrLenGeLe:1,50',
        'is_undertake' => 'Required|IntIn:' . MaterialEnums::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_VALIDATE,
        'email' => 'StrLenGeLe:0,50',
        'mobile' => 'StrLenGeLe:0,20',
        'address' => 'StrLenGeLe:0,200',
        'staffs' => 'Required|Arr|ArrLenGeLe:1,100',
        'staffs[*]' => 'IntGt:0'
    ];

    /**
     * 资产退回类型 - 新增/编辑
     * @var array
     */
    public static $validate_return_repair_type_add = [
        'name_zh' => 'Required|StrLenGeLe:1,50',
        'name_en' => 'Required|StrLenGeLe:1,50',
        'name_local' => 'Required|StrLenGeLe:1,50',
        'status' => 'Required|IntIn:' . MaterialEnums::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALIDATE,
    ];

    /**
     * 资产退回映射 - 新增/编辑
     * @var array
     */
    public static $validate_return_repair_map_add = [
        'group_id' => 'Required|IntGt:0',
        'type_id' => 'Required|IntGt:0',
        'address' => 'StrLenGeLe:0,200',
        'material_category_ids' => 'Arr|ArrLenGeLe:0,50',
        'barcodes' => 'Arr|ArrLenGeLe:0,500',
        'department_ids' => 'Arr|ArrLenGeLe:0,20',
    ];

    /**
     * 资产退回组/退回类型/映射 - 编辑/删除/查看
     * @var array
     */
    public static $validate_return_repair_id = [
        'id' => 'Required|IntGt:0',
    ];

    //资产退回 - 查询类型 - 1退回组、2退回类型
    const RETURN_REPAIR_SEARCH_TYPE_GROUP = 1;
    const RETURN_REPAIR_SEARCH_TYPE_TYPE = 2;
    public static $validate_return_repair_set_search = [
        'name' => 'StrLenGeLe:0,50',
        'type' => 'Required|IntIn:' . self::RETURN_REPAIR_SEARCH_TYPE_GROUP . ',' .self::RETURN_REPAIR_SEARCH_TYPE_TYPE
    ];

    /**
     * 资产退回映射
     * @var array
     */
    public static $validate_return_repair_map_list = [
        'group_name' => 'StrLenGeLe:0,50',
        'type_name' => 'StrLenGeLe:0,50',
        'barcode' => 'StrLenGeLe:0,30',
        'category_id[*]' => 'IntGt:0',
        'pageSize' => 'IntGt:0',
        'pageNum' => 'IntGt:0',
    ];

    //资产数据管控
    const DATA_PERMISSION_LEAVE_ASSET = 1;//离职资产处理
    const DATA_PERMISSION_ASSET = 2;//资产台账
    const DATA_PERMISSION_ASSET_APPLY = 3;//资产申请
    const DATA_PERMISSION_ASSET_OUT = 4;//资产出库
    const DATA_PERMISSION_ASSET_TRANSFER = 5;//资产转移
    const VALIDATE_DATA_PERMISSION_RULE = self::DATA_PERMISSION_LEAVE_ASSET . ',' . self::DATA_PERMISSION_ASSET . ',' . self::DATA_PERMISSION_ASSET_APPLY . ',' . self::DATA_PERMISSION_ASSET_OUT . ',' . self::DATA_PERMISSION_ASSET_TRANSFER;
    public static $data_permission = [
        self::DATA_PERMISSION_LEAVE_ASSET => '离职资产处理',
        self::DATA_PERMISSION_ASSET => '资产台账',
        self::DATA_PERMISSION_ASSET_APPLY => '资产申请',
        self::DATA_PERMISSION_ASSET_OUT => '资产出库',
        self::DATA_PERMISSION_ASSET_TRANSFER => '资产转移',
    ];

    /**
     * 资产数据管控 - 组 - 新增
     * @var array
     */
    public static $validate_data_permission_group_add = [
        'name' => 'Required|StrLenGeLe:1,100',
        'module_id' => 'Required|IntIn:' . self::VALIDATE_DATA_PERMISSION_RULE,
        'is_see_all' => 'Required|IntIn:' . MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_VALIDATE,
    ];

    /**
     * 资产数据管控 - 组
     * @var array
     */
    public static $validate_data_permission_group_list = [
        'name' => 'StrLenGeLe:0,100',
        'module_id' => 'Required|IntIn:' . self::VALIDATE_DATA_PERMISSION_RULE,
        'pageSize' => 'IntGt:0',
        'pageNum' => 'IntGt:0',
    ];

    /**
     * 资产数据管控 - 组 - 编辑/删除/查看
     * @var array
     */
    public static $validate_data_permission_id = [
        'id' => 'Required|IntGt:0',
    ];


    private static $instance;
    private function __construct()
    {
    }
    /**
     * @return MaterialSettingService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 通用设置 - 枚举配置
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //物料分类-树状
            $category_enums['type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $data['category_map'] = ClassifyService::getInstance()->getClassifyArr($category_enums, 'search')['data'];
            //是否兜底组、退回类型、资产数据管控-是否看全部数据、隐藏字段
            $enum = $this->getEnums();
            $data = array_merge($data,$enum);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取通用设置 - 枚举配置异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取是否兜底组、退回类型、资产数据管控-是否看全部数据、隐藏字段
     * @return array
     */
    public function getEnums()
    {
        $data = [];
        $enums_arr = [
            'is_undertake' => MaterialEnums::$material_set_return_repair_is_undertake,
            'type_status' => MaterialEnums::$material_set_return_repair_type_status,
            'is_see_all' => MaterialEnums::$material_set_data_permission_is_see_all,
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $v) {
                $data[$key][] = [
                    'value' => (string)$k,
                    'label' => static::$t->_($v)
                ];
            }
        }

        //物料/资产管理-通用设置-资产数据管控-应用模块-隐藏字段配置
        $hidden_fields = EnumsService::getInstance()->getSettingEnvValueMap('material_data_permission_hidden_fields');
        foreach ($hidden_fields as $field) {
            foreach ($field['field'] as $key => $value) {
                $data['hidden_fields'][$field['module_id']][] = [
                    'value' => $key,
                    'label' => $value
                ];
            }
        }
        return $data;
    }

    /**
     * 通用设置-管理设置-出库单必填成本中心
     * @param array $params 请求参数组
     * @param array $user 当前操作用户
     * @return array
     */
    public function save($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //查询原有设置
            $setting_code = 'material_asset_pccode_required';
            $now_date = date('Y-m-d H:i:s');
            $env_model = new EnvModel();
            $setting_env = $env_model::findFirst(
                [
                    'conditions' => 'code=:code:',
                    'bind' => ['code' => $setting_code]
                ]
            );
            //记录之前的数据
            $before = [];
            $before['is_check'] = MaterialSettingEnums::PC_CODE_IS_CHECK_NO;
            $before['department_ids'] = '';
            //设置了此code,且值不为空,才算是设置为[需要必填]
            if ($setting_env && !empty(trim($setting_env->val)) ){
                $before['is_check'] = MaterialSettingEnums::PC_CODE_IS_CHECK_YES;
                $before['department_ids'] = $setting_env->val;
            }
            //参数验证
            if (!isset($params['department_ids']) || $params['is_check'] == MaterialSettingEnums::PC_CODE_IS_CHECK_NO){
                $params['department_ids'] = [];
            }
            if ($before['is_check'] == MaterialSettingEnums::PC_CODE_IS_CHECK_NO && $params['is_check'] == MaterialSettingEnums::PC_CODE_IS_CHECK_NO ){
                throw new ValidationException(static::$t->_('setting_is_check_not_change'), ErrCode::$VALIDATE_ERROR);
            }
            if ($params['is_check'] == MaterialSettingEnums::PC_CODE_IS_CHECK_YES && empty($params['department_ids'])){
                throw new ValidationException(static::$t->_('department_ids_can_not_empty'), ErrCode::$VALIDATE_ERROR);
            }
            //记录修改后的
            $after = [
                'is_check' => $params['is_check'],
                'department_ids' => implode(',',$params['department_ids'])
            ];
            //保存新的配置
            if ($setting_env){
                //修改
                $update = [];
                $update['val'] = !empty($after['department_ids'])?$after['department_ids']:'';
                $update['updated_at'] = $now_date;
                $update['last_update_id'] = $user['id'];
                $bool = $db->updateAsDict(
                    $env_model->getSource(),
                    $update,
                    ['conditions'=>'id=?','bind' => [$setting_env->id] ]
                );
                if ($bool === false) {
                    throw new BusinessException("出库单必填成本中心配置[修改]失败". json_encode($update, JSON_UNESCAPED_UNICODE) , ErrCode::$MATERIAL_SETTING_PC_CODE_UPDATE_ERROR);
                }
            }else{
                //新增
                $insert = [];
                $insert['code'] = $setting_code;
                $insert['is_edit'] = SettingEnums::SETTING_ENUMS_IS_EDIT;
                $insert['val'] = $after['department_ids'];
                $insert['content'] = '物料/资产管理-通用设置-出库单必填[成本中心]的领用人部门';
                $insert['created_at'] = $now_date;
                $insert['last_update_id'] = $user['id'];
                $bool = $env_model->i_create($insert);
                if ($bool === false) {
                    throw new BusinessException('出库单必填成本中心配置[新增]失败: '. json_encode($insert, JSON_UNESCAPED_UNICODE) , ErrCode::$MATERIAL_SETTING_PC_CODE_ADD_ERROR);
                }
            }
            //记录日志
            $this->logger->info('出库单必填成本中心配置成功, 操作人='.$user['id'].'; 修改前数据='.json_encode($before,JSON_UNESCAPED_UNICODE).'; 修改后数据='.json_encode($after,JSON_UNESCAPED_UNICODE));
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error($real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }


    /**
     * @param int $user_id 申请人id
     * @return bool  true:必填  false:非必填
     * @date 2022/8/14
     */
    public function getPcCodeRequired($user_id)
    {
        //查询是否配置
        $setting_department = EnvModel::getEnvByCode('material_asset_pccode_required','');
        if(empty($setting_department)){
            return false;
        }
        $setting_department = explode(',',$setting_department);
        //当前用户所属部门是否必填
        $user_service = new UserService();
        $user_info = $user_service->getUserByIdInRbi($user_id);
        if (isset($user_info->node_department_id) && in_array($user_info->node_department_id,$setting_department)){
            return true;
        }
        return false;
    }

    /**
     * 查询[出库单必填成本中心]的配置
     * @return array
     * @date 2022/8/16
     */
    public function getPcCodeSetting(){
        $setting_code = 'material_asset_pccode_required';
        $setting_env = EnvModel::findFirst(
            [
                'conditions' => 'code=:code:',
                'bind' => ['code' => $setting_code]
            ]
        );
        $before = [];
        $before['is_check'] = MaterialSettingEnums::PC_CODE_IS_CHECK_NO;
        $before['department_ids'] = [];
        //设置了此code,且值不为空,才算是设置为[需要必填]
        if ($setting_env && !empty(trim($setting_env->val)) ){
            $before['is_check'] = MaterialSettingEnums::PC_CODE_IS_CHECK_YES;
            $before['department_ids'] = explode(',',$setting_env->val);
        }
        return $before;
    }

    /**
     * 同步scm成本中心必填配置查询
     * @return bool
     * @date 2023/04/17
     */
    public function getPcCodeToScmRequired()
    {
        //查询是否配置
        $setting_pc_code = EnvModel::getEnvByCode('synchronous_pc_code_required', '');
        if (empty($setting_pc_code)) {
            return false;
        }
        $setting_pc_code = json_decode($setting_pc_code, true);
        return $setting_pc_code['scm_pc_code_switcher'] == 1 ? true : false;
    }

    /**
     * 资产盘点组-列表
     * @param array $params 参数组
     * @return array
     */
    public function inventoryGroupList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialInventoryGroupModel::class]);
            $builder->leftjoin(MaterialInventoryGroupStaffModel::class, 'staff.group_id = main.id', 'staff');
            if (!empty($params['name'])) {
                $builder->andWhere('main.name like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            if (!empty($params['staff_id'])) {
                $builder->andWhere('staff.staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
            }
            $builder->columns('count(DISTINCT main.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('main.id, main.name');
                $builder->limit($page_size, $offset);
                $builder->groupBy('main.id');
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-inventory-group-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 根据组ID获取某条盘点组信息
     * @param integer $id 组ID
     * @return mixed
     * @throws ValidationException
     */
    public function getInventoryGroupInfo($id)
    {
        $group_info = MaterialInventoryGroupModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($group_info)) {
            throw new ValidationException(static::$t->_('inventory_group_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $group_info;
    }

    /**
     * 盘点组信息db操作
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param string $type 操作类型，add新增，edit编辑
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function operateGroupDb($params, $user, $type = 'add')
    {
        //组下员工不可重复
        if (!empty($params['staffs'])) {
            $unique_staff = array_unique(array_filter($params['staffs']));
            if (count($params['staffs']) != count($unique_staff)) {
                throw new ValidationException(static::$t->_('inventory_group_staff_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            //工号都是正式、实习员工。
            $staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id',
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and formal in ({formal:array}) and is_sub_staff = :is_sub_staff:',
                'bind' => ['staff_info_ids' => $params['staffs'], 'formal' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE], 'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]
            ])->toArray();
            if (count($staff_list) != count($params['staffs'])) {
                throw new ValidationException(static::$t->_('inventory_group_staff_error'), ErrCode::$VALIDATE_ERROR);
            }
        }

        $now = date('Y-m-d H:i:s');
        //新增组信息
        if ($type == 'add') {
            $group = [
                'name' => $params['name'],
                'rule_url' => $params['rule_url'],
                'q_a_url' => $params['q_a_url'],
                'create_staff_id' => $user['id'],
                'create_name' => $user['name'],
                'created_at' => $now,
                'updated_at' => $now
            ];
            $group_info = new MaterialInventoryGroupModel();
            $bool = $group_info->i_create($group);
            if ($bool === false) {
                throw new BusinessException('资产盘点组 - 新增-失败 = ' . json_encode($group, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            //更新组信息
            $group_info = $this->getInventoryGroupInfo($params['id']);
            $now = date('Y-m-d H:i:s');
            $group_info->name = $params['name'];
            $group_info->rule_url = $params['rule_url'];
            $group_info->q_a_url = $params['q_a_url'];
            $group_info->updated_at = $now;
            $bool = $group_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点组 - 编辑-失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }

            //先删除原来的组下员工
            $bool = $group_info->getStaffs()->delete();
            if ($bool === false) {
                throw new BusinessException('资产盘点组 - 编辑前删除原组下员工-失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        //插入新的组下员工
        $group_staffs = [];
        if (!empty($params['staffs'])) {
            foreach ($params['staffs'] as $staff) {
                $group_staffs[] = [
                    'group_id' => $group_info->id,
                    'staff_id' => $staff,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }
            $inventory_group_staff_model = new MaterialInventoryGroupStaffModel();
            $bool = $inventory_group_staff_model->batch_insert($group_staffs);
            if ($bool === false) {
                throw new BusinessException('资产盘点组 - 新增组下员工-失败 = ' . json_encode($group_staffs,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }
        return true;
    }

    /**
     * 资产盘点组 - 新增
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function inventoryGroupAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateGroupDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-inventory-group-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];

    }

    /**
     * 资产盘点组 - 编辑
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function inventoryGroupEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateGroupDb($params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-inventory-group-edit-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产盘点组 - 删除
     * @param array $params 参数组
     * @return array
     */
    public function inventoryGroupDel($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //删除组
            $group_info = $this->getInventoryGroupInfo($params['id']);
            $bool = $group_info->delete();
            if ($bool === false) {
                throw new BusinessException('资产盘点组-删除-失败 = ' . json_encode(['id' => $group_info->id], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }
            //删除组下员工
            $bool = $group_info->getStaffs()->delete();
            if ($bool === false) {
                throw new BusinessException('资产盘点组-删除-删除组下员工-失败', ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-inventory-group-del-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产盘点组 - 查看
     * @param array $params 参数组
     * @return array
     */
    public function inventoryGroupView($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $group_info = $this->getInventoryGroupInfo($params['id']);
            $data['name'] = $group_info->name;
            $data['rule_url'] = $group_info->rule_url;
            $data['q_a_url'] = $group_info->q_a_url;

            //获取员工
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialInventoryGroupStaffModel::class);
            $builder->where('group_id = :group_id:', ['group_id' => $group_info->id]);
            $builder->columns('staff_id');
            $builder->orderby('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            if ($items) {
                $staff_ids = array_column($items, 'staff_id');
                $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_ids, 'limit' => count($staff_ids)]);
                $staff_list = array_column($staff_list['data'] ?? [], null, 'staff_id');
                foreach ($items as &$item) {
                    $staff_info = $staff_list[$item['staff_id']] ?? [];
                    $item['staff_name'] = $staff_info['staff_name'] ?? '';
                    $item['node_department_name'] = $staff_info['node_department_name'] ?? '';
                    $item['sys_store_name'] = $staff_info['store_name'] ?? '';
                    $item['state_text'] = $staff_info['state_text'] ?? '';
                }
            }
            $data['items'] = $items ?? [];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-inventory-group-view-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 获取某个员工所在的盘点组
     * @param integer $staff_id 员工工号
     * @return array
     */
    public function getStaffInventoryGroups($staff_id)
    {
        if (empty($staff_id)) {
            return [];
        }
        $group_list = MaterialInventoryGroupStaffModel::find([
            'columns' => 'group_id',
            'conditions' => 'staff_id = :staff_id:',
            'bind' => ['staff_id' => $staff_id]
        ])->toArray();
        return array_column($group_list, 'group_id');
    }

    /**
     * 获取组集合下的所有组员（去重后的）
     * @param array $group_ids 组集合
     * @return array
     */
    public function getInventoryGroupStaffList($group_ids)
    {
        if (empty($group_ids)) {
            return [];
        }
        $group_staff_list = MaterialInventoryGroupStaffModel::find([
            'columns' => 'staff_id',
            'conditions' => 'group_id IN ({group_ids:array})',
            'bind' => ['group_ids' => $group_ids]
        ])->toArray();
        return array_values(array_unique(array_column($group_staff_list, 'staff_id')));
    }

    /**
     * 获取盘点规则url
     * @param array $group_ids 组集合
     * @return string
     */
    public function getRuleUrl($group_ids)
    {
        if (empty($group_ids)) {
            return '';
        }
        $one_group_info = MaterialInventoryGroupModel::findFirst([
            'conditions' => 'id in ({ids:array}) and rule_url != :rule_url:',
            'bind' => ['ids' => $group_ids, 'rule_url' => ''],
            'columns' => 'rule_url',
            'order' => 'id asc'
        ]);
        return $one_group_info ? $one_group_info->rule_url : '';
    }

    /**
     * 获取员工所在盘点组的Q&A url
     * @param array $staff_ids 员工ID组
     * @return array
     */
    public function getQaUrl($staff_ids)
    {
        if (empty($staff_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.q_a_url, staff.staff_id');
        $builder->from(['main' => MaterialInventoryGroupModel::class]);
        $builder->leftjoin(MaterialInventoryGroupStaffModel::class, 'staff.group_id = main.id', 'staff');
        $builder->inWhere('staff.staff_id', $staff_ids);
        $builder->andWhere('main.q_a_url != :q_a_url:', ['q_a_url' => '']);
        $builder->groupby('staff.staff_id');
        $builder->orderby('NULL');
        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list, 'q_a_url', 'staff_id');
    }

    /**
     * 资产退回组-列表
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairGroupList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialSetReturnRepairGroupModel::class]);
            $builder->leftjoin(MaterialSetReturnRepairGroupStaffModel::class, 'staff.group_id = main.id', 'staff');
            $builder->where('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $name_language = get_lang_field_name('name_', static::$language);
            if (!empty($params['name'])) {
                $builder->andWhere('main.' . $name_language .' like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            if (!empty($params['staff_id'])) {
                $builder->andWhere('staff.staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
            }
            $builder->columns('count(DISTINCT main.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('main.id, main.' . $name_language . ' as name, main.is_undertake');
                $builder->limit($page_size, $offset);
                $builder->groupBy('main.id');
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as &$item) {
                    $item['is_undertake_text'] = static::$t->_(MaterialEnums::$material_set_return_repair_is_undertake[$item['is_undertake']]);
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-return-repair-group-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回组 - 其他验证
     * @param array $params 参数组
     * @throws ValidationException
     */
    public function validateReturnRepair($params)
    {
        if (!empty($params['email']) && strpos($params['email'], '@') === false) {
            throw new ValidationException(static::$t->_('return_repair_group_email_error'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 资产退回组 - 新增
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairGroupAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairGroupDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-group-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 根据组ID获取某条资产退回组信息
     * @param integer $id 组ID
     * @return mixed
     * @throws ValidationException
     */
    public function getReturnRepairGroupInfo($id)
    {
        $group_info = MaterialSetReturnRepairGroupModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($group_info)) {
            throw new ValidationException(static::$t->_('return_repair_group_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $group_info;
    }

    /**
     * 退回组信息db操作
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param string $type 操作类型，add新增，edit编辑
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function operateReturnRepairGroupDb($params, $user, $type = 'add')
    {
        $this->validateReturnRepair($params);
        //组下员工不可重复
        if (!empty($params['staffs'])) {
            $unique_staff = array_unique(array_filter($params['staffs']));
            if (count($params['staffs']) != count($unique_staff)) {
                throw new ValidationException(static::$t->_('inventory_group_staff_repeat'), ErrCode::$VALIDATE_ERROR);
            }
        }
        //非删除态的兜底组只能有1个，如果非删除态的兜底组数量＞1，则提示：已有兜底组，请检查！
        $undertake_yes_group_info = [];
        if ($params['is_undertake'] == MaterialEnums::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES) {
            $undertake_yes_group_info = MaterialSetReturnRepairGroupModel::findFirst([
                'columns' => 'id',
                'conditions' => 'is_undertake = :is_undertake: and is_deleted = :is_deleted:',
                'bind' => ['is_undertake' => MaterialEnums::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            $undertake_yes_group_info = $undertake_yes_group_info ? $undertake_yes_group_info->toArray() : [];
        }
        $now = date('Y-m-d H:i:s');
        //新增组信息
        if ($type == 'add') {
            if ($undertake_yes_group_info) {
                throw new ValidationException(static::$t->_('return_repair_group_undertake_exists'), ErrCode::$VALIDATE_ERROR);
            }
            $group = [
                'name_zh' => $params['name_zh'],
                'name_en' => $params['name_en'],
                'name_local' => $params['name_local'],
                'is_undertake' => $params['is_undertake'],
                'email' => $params['email'],
                'mobile' => $params['mobile'],
                'address' => $params['address'],
                'create_staff_id' => $user['id'],
                'create_name' => $user['name'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'created_at' => $now,
                'updated_at' => $now
            ];
            $group_info = new MaterialSetReturnRepairGroupModel();
            $bool = $group_info->i_create($group);
            if ($bool === false) {
                throw new BusinessException('资产退回组 - 新增-失败 = ' . json_encode($group, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            //更新组信息
            $group_info = $this->getReturnRepairGroupInfo($params['id']);
            if ($undertake_yes_group_info && $undertake_yes_group_info['id'] != $group_info->id) {
                throw new ValidationException(static::$t->_('return_repair_group_undertake_exists'), ErrCode::$VALIDATE_ERROR);
            }
            $group_info->name_zh = $params['name_zh'];
            $group_info->name_en = $params['name_en'];
            $group_info->name_local = $params['name_local'];
            $group_info->is_undertake = $params['is_undertake'];
            $group_info->email = $params['email'];
            $group_info->mobile = $params['mobile'];
            $group_info->address = $params['address'];
            $group_info->update_staff_id = $user['id'];
            $group_info->update_name = $user['name'];
            $group_info->updated_at = $now;
            $bool = $group_info->save();
            if ($bool === false) {
                throw new BusinessException('资产退回组 - 编辑-失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }

            //先删除原来的组下员工
            $bool = $group_info->getStaffs()->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回组 - 编辑前删除原组下员工-失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        //插入新的组下员工
        $group_staffs = [];
        if (!empty($params['staffs'])) {
            foreach ($params['staffs'] as $staff) {
                $group_staffs[] = [
                    'group_id' => $group_info->id,
                    'staff_id' => $staff,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }
            $return_repair_group_staff_model = new MaterialSetReturnRepairGroupStaffModel();
            $bool = $return_repair_group_staff_model->batch_insert($group_staffs);
            if ($bool === false) {
                throw new BusinessException('资产退回组 - 新增组下员工-失败 = ' . json_encode($group_staffs,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }
        return true;
    }

    /**
     * 资产退回组 - 编辑
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairGroupEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairGroupDb($params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-group-edit-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回组 - 删除
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairGroupDel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            //删除组
            $group_info = $this->getReturnRepairGroupInfo($params['id']);
            //检查是否存在映射关系,存在不可删除
            $map_count = MaterialSetReturnRepairMapModel::count([
                'conditions' => 'group_id = :group_id:',
                'bind' => ['group_id' => $group_info->id]
            ]);
            if ($map_count) {
                throw new ValidationException(static::$t->_('return_repair_group_map_exists'), ErrCode::$VALIDATE_ERROR);
            }
            $group_info->is_deleted = GlobalEnums::IS_DELETED;
            $group_info->update_staff_id = $user['id'];
            $group_info->update_name = $user['name'];
            $bool = $group_info->save();
            if ($bool === false) {
                throw new BusinessException('资产退回组-删除-失败 = ' . json_encode(['id' => $group_info->id], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-group-del-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回组 - 查看
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairGroupView($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $group_info = $this->getReturnRepairGroupInfo($params['id']);
            $data = $group_info->toArray();

            //获取员工
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialSetReturnRepairGroupStaffModel::class);
            $builder->where('group_id = :group_id:', ['group_id' => $group_info->id]);
            $builder->columns('staff_id');
            $builder->orderby('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            if ($items) {
                $staff_ids = array_column($items, 'staff_id');
                $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_ids, 'limit' => count($staff_ids)]);
                $staff_list = array_column($staff_list['data'] ?? [], null, 'staff_id');
                foreach ($items as &$item) {
                    $staff_info = $staff_list[$item['staff_id']] ?? [];
                    $item['staff_name'] = $staff_info['staff_name'] ?? '';
                    $item['node_department_name'] = $staff_info['node_department_name'] ?? '';
                    $item['sys_store_name'] = $staff_info['store_name'] ?? '';
                    $item['state_text'] = $staff_info['state_text'] ?? '';
                }
            }
            $data['staffs'] = $items ?? [];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-group-view-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 资产退回类型-列表
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairTypeList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialSetReturnRepairTypeModel::class);
            $builder->where('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $name_language = get_lang_field_name('name_', static::$language);
            if (!empty($params['name'])) {
                $builder->andWhere($name_language .' like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            if (!empty($params['status'])) {
                $builder->andWhere('status = :status:', ['status' => $params['status']]);
            }
            $builder->columns('count(id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('id, name_zh, name_en, name_local, status');
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                foreach ($items as &$item) {
                    $item['id_text'] = $item['id'] < 10 ? '0' . $item['id'] : $item['id'];
                    $item['status_text'] = static::$t->_(MaterialEnums::$material_set_return_repair_type_status[$item['status']]);
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-return-repair-type-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回类型 - 新增
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairTypeAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairTypeDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-type-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 根据组ID获取某条资产退回类型信息
     * @param integer $id 组ID
     * @return mixed
     * @throws ValidationException
     */
    public function getReturnRepairTypeInfo($id)
    {
        $type_info = MaterialSetReturnRepairTypeModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($type_info)) {
            throw new ValidationException(static::$t->_('return_repair_type_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $type_info;
    }

    /**
     * 退回类型信息db操作
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param string $type 操作类型，add新增，edit编辑
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function operateReturnRepairTypeDb($params, $user, $type = 'add')
    {
        $now = date('Y-m-d H:i:s');
        //新增类型信息
        if ($type == 'add') {
            $type_data = [
                'name_zh' => $params['name_zh'],
                'name_en' => $params['name_en'],
                'name_local' => $params['name_local'],
                'status' => $params['status'],
                'create_staff_id' => $user['id'],
                'create_name' => $user['name'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'created_at' => $now,
                'updated_at' => $now
            ];
            $type_info = new MaterialSetReturnRepairTypeModel();
            $bool = $type_info->i_create($type_data);
            if ($bool === false) {
                throw new BusinessException('退回类型 - 新增-失败 = ' . json_encode($type_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($type_info), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            //更新类型信息
            $type_info = $this->getReturnRepairTypeInfo($params['id']);
            $type_info->name_zh = $params['name_zh'];
            $type_info->name_en = $params['name_en'];
            $type_info->name_local = $params['name_local'];
            $type_info->status = $params['status'];
            $type_info->update_staff_id = $user['id'];
            $type_info->update_name = $user['name'];
            $type_info->updated_at = $now;
            $bool = $type_info->save();
            if ($bool === false) {
                throw new BusinessException('退回类型 - 编辑-失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($type_info), ErrCode::$BUSINESS_ERROR);
            }
        }
        return true;
    }

    /**
     * 资产退回类型 - 编辑
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairTypeEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairTypeDb($params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-type-edit-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回类型 - 删除
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairTypeDel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            //删除组
            $type_info = $this->getReturnRepairTypeInfo($params['id']);
            //检查是否存在映射关系,存在不可删除
            $map_count = MaterialSetReturnRepairMapModel::count([
                'conditions' => 'type_id = :type_id:',
                'bind' => ['type_id' => $type_info->id]
            ]);
            if ($map_count) {
                throw new ValidationException(static::$t->_('return_repair_type_map_exists'), ErrCode::$VALIDATE_ERROR);
            }
            $type_info->is_deleted = GlobalEnums::IS_DELETED;
            $type_info->update_staff_id = $user['id'];
            $type_info->update_name = $user['name'];
            $bool = $type_info->save();
            if ($bool === false) {
                throw new BusinessException('资产退回类型-删除-失败 = ' . json_encode(['id' => $type_info->id], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($type_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-type-del-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回类型 - 查看
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairTypeView($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $type_info = $this->getReturnRepairTypeInfo($params['id']);
            $data = $type_info->toArray();
            $data['id_text'] = $data['id'] < 10 ? '0' . $data['id'] : $data['id'];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-type-view-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 搜索barcode
     * @param array $params 参数组
     * @return array
     */
    public function searchBarcode($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $params['limit'] = 500;
            $params['lang'] = static::$language;
            $data = MaterialSauRepository::getInstance()->searchBarcode($params);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-set-searchBarcode-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回组/类型 - 搜索
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairSetSearch($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $list = [];
            if ($params['type'] == self::RETURN_REPAIR_SEARCH_TYPE_GROUP) {
                $list = $this->returnRepairGroupList($params);
            } else if ($params['type'] == self::RETURN_REPAIR_SEARCH_TYPE_TYPE) {
                $params['status'] = MaterialEnums::MATERIAL_SET_RETURN_REPAIR_TYPE_STATUS_VALID;
                $list = $this->returnRepairTypeList($params);
            }
            $items = $list['data']['items'] ?? [];
            foreach ($items as $item) {
                $data[] = [
                    'id' => $item['id'],
                    'name' => isset($item['name']) ? $item['name'] : $item[get_lang_field_name('name_', static::$language)]
                ];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-return-repair-map-returnRepairSetSearch-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取资产退回映射-额外的参数组信息
     * @param array $params 请求参数
     * @return array
     */
    public function getExtendMapValidation(array $params)
    {
        $validate_param = self::$validate_return_repair_map_add;
        //资产分类
        if (!empty($params['material_category_ids'])) {
            $validate_param['material_category_ids[*]'] = 'Required|IntGt:0|>>>:param error[material_category_id]';
        }
        //barcode
        if (!empty($params['barcodes'])) {
            $validate_param['barcodes[*]'] = 'Required|StrLenGeLe:1,30|>>>:param error[barcode]';
        }
        //部门
        if (!empty($params['department_ids'])) {
            $validate_param['department_ids[*].department_id'] = 'Required|IntGt:0|>>>:param error[department_id]';//部门ID
            $validate_param['department_ids[*].is_include_sub'] = 'Required|IntIn:0,1|>>>:param error[is_include_sub]';//是否包含子部门: 0-不含；1-包含
        }
        return $validate_param;
    }

    /**
     * 资产退回映射-列表
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairMapList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $name_language = get_lang_field_name('name_', static::$language);
            $map_rel = false;

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialSetReturnRepairMapModel::class]);
            $builder->leftjoin(MaterialSetReturnRepairGroupModel::class, 'g.id = main.group_id', 'g');
            $builder->leftjoin(MaterialSetReturnRepairTypeModel::class, 't.id = main.type_id', 't');

            //退回组名搜索
            if (!empty($params['group_name'])) {
                $name_language = get_lang_field_name('name_', static::$language);
                $builder->andWhere('g.' . $name_language .' like :group_name:', ['group_name' => '%' . $params['group_name'] . '%']);
            }

            //退回类型名搜索
            if (!empty($params['type_name'])) {
                $builder->andWhere('t.' . $name_language .' like :type_name:', ['type_name' => '%' . $params['type_name'] . '%']);
            }

            //barcode
            if (!empty($params['barcode'])) {
                $map_rel = true;
                $builder->leftjoin(MaterialSetReturnRepairMapBarcodeModel::class, 'b.map_id = main.id', 'b');
                $builder->andWhere('b.barcode = :barcode:', ['barcode' => $params['barcode']]);
            }

            //物料分类搜索 含子类
            if (!empty($params['category_id'])) {
                $map_rel = true;
                $builder->leftjoin(MaterialSetReturnRepairMapCategoryModel::class, 'c.map_id = main.id', 'c');
                $category_ids = (new MaterialCategoryModel())->getSonList($params['category_id'],'id');
                $builder->inWhere('c.category_id', array_values(array_merge($category_ids['data'], $params['category_id'])));
            }

            $builder->columns($map_rel ? 'count(DISTINCT main.id) AS count' : 'count(main.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('main.id, g.' . $name_language . ' as group_name, t.' . $name_language . ' as type_name');
                $builder->limit($page_size, $offset);
                if ($map_rel) {
                    $builder->groupBy('main.id');
                }
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();

                //获取关系表barcode、以及分类数据
                if ($items) {
                    $map_ids = array_column($items, 'id');
                    $map_barcode_list = MaterialSetReturnRepairMapBarcodeModel::find([
                        'columns' => 'map_id, GROUP_CONCAT(barcode) as barcode',
                        'conditions' => 'map_id in ({map_ids:array})',
                        'bind' => ['map_ids' => $map_ids],
                        'group' => 'map_id',
                    ])->toArray();
                    $map_barcode_list = array_column($map_barcode_list, null, 'map_id');

                    $map_category_list = MaterialSetReturnRepairMapCategoryModel::find([
                        'columns' => 'map_id, GROUP_CONCAT(category_id) as category_ids',
                        'conditions' => 'map_id in ({map_ids:array})',
                        'bind' => ['map_ids' => $map_ids],
                        'group' => 'map_id',
                    ])->toArray();
                    $map_category_list = array_column($map_category_list, null, 'map_id');

                    foreach ($items as &$item) {
                        $item['barcode'] = $map_barcode_list[$item['id']]['barcode'] ?? '';
                        if (isset($map_category_list[$item['id']]['category_ids']) && !empty($map_category_list[$item['id']]['category_ids'])) {
                            $category_info = MaterialCategoryModel::findFirst([
                                'columns' => 'GROUP_CONCAT(name) as name',
                                'conditions' => 'id in ({ids:array})',
                                'bind' => ['ids' => explode(',', $map_category_list[$item['id']]['category_ids'])],
                            ]);
                            $item['category_id'] = $category_info ? $category_info->name : '';
                        } else {
                            $item['category_id'] = '';
                        }
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-return-repair-map-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回映射 - 新增
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairMapAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairMapDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-type-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 根据组ID获取某条资产退回映射信息
     * @param integer $id 组ID
     * @return mixed
     * @throws ValidationException
     */
    public function getReturnRepairMapInfo($id)
    {
        $group_info = MaterialSetReturnRepairMapModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($group_info)) {
            throw new ValidationException(static::$t->_('return_repair_map_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $group_info;
    }

    /**
     * 映射信息db操作
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param string $type 操作类型，add新增，edit编辑
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function operateReturnRepairMapDb($params, $user, $type = 'add')
    {
        //物料分类，barcode，申请人部门3个必须填写一个
        if (empty($params['material_category_ids']) && empty($params['barcodes']) && empty($params['department_ids'])) {
            throw new ValidationException(static::$t->_('return_repair_map_params_loss'), ErrCode::$VALIDATE_ERROR);
        }
        //验证退回组是否存在
        $this->getReturnRepairGroupInfo($params['group_id']);
        //验证退回类型是否存在
        $this->getReturnRepairTypeInfo($params['type_id']);
        $now = date('Y-m-d H:i:s');
        //新增映射信息
        if ($type == 'add') {
            $data = [
                'group_id' => $params['group_id'],
                'type_id' => $params['type_id'],
                'address' => $params['address'],
                'create_staff_id' => $user['id'],
                'create_name' => $user['name'],
                'created_at' => $now,
                'updated_at' => $now
            ];
            $map_info = new MaterialSetReturnRepairMapModel();
            $bool = $map_info->i_create($data);
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 新增-失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($map_info), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            //更新映射信息
            $map_info = $this->getReturnRepairMapInfo($params['id']);
            $map_info->group_id = $params['group_id'];
            $map_info->type_id = $params['type_id'];
            $map_info->address = $params['address'];
            $map_info->updated_at = $now;
            $bool = $map_info->save();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 编辑-失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($map_info), ErrCode::$BUSINESS_ERROR);
            }

            //先删除原来的物料分类、barcode、申请人部门关系数据
            $map_rel_barcode_obj = $map_info->getBarcode();
            $this->logger->info('资产退回映射 - 编辑前barcode - 关系数据为：' . json_encode($map_rel_barcode_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_barcode_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 编辑前删除原来barcode - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $map_rel_category_obj = $map_info->getCategory();
            $this->logger->info('资产退回映射 - 编辑前物料分类 - 关系数据为：' . json_encode($map_rel_category_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_category_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 编辑前删除原来的物料分类 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $map_rel_department_obj = $map_info->getDepartment();
            $this->logger->info('资产退回映射 - 编辑前申请人部门 -关系数据为：' . json_encode($map_rel_department_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_department_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 编辑前删除申请人部门 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        //物料分类
        $category_arr = [];
        if (!empty($params['material_category_ids'])) {
            foreach ($params['material_category_ids'] as $category_id) {
                $category_arr [] = [
                    'map_id' => $map_info->id,
                    'category_id' => $category_id,
                ];
            }
        } else {
            $category_arr [] = [
                'map_id' => $map_info->id,
                'category_id' => 0,
            ];
        }
        $return_repair_map_rel_model = new MaterialSetReturnRepairMapCategoryModel();
        $bool = $return_repair_map_rel_model->batch_insert($category_arr);
        if ($bool === false) {
            throw new BusinessException('资产退回组 - 新增物料分类 - 关系数据-失败 = ' . json_encode($category_arr,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        //barcode
        $barcode_data = [];
        if (!empty($params['barcodes'])) {
            foreach ($params['barcodes'] as $barcode) {
                $barcode_data [] = [
                    'map_id' => $map_info->id,
                    'barcode' => $barcode,
                ];
            }
        } else {

            $barcode_data [] = [
                'map_id' => $map_info->id,
                'barcode' => '',
            ];
        }
        $return_repair_map_rel_model = new MaterialSetReturnRepairMapBarcodeModel();
        $bool = $return_repair_map_rel_model->batch_insert($barcode_data);
        if ($bool === false) {
            throw new BusinessException('资产退回组 - barcode - 关系数据-失败 = ' . json_encode($barcode_data,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        //申请人部门
        $department_arr = [];
        if (!empty($params['department_ids'])) {
            foreach ($params['department_ids'] as $item) {
                $department_arr [] = [
                    'map_id' => $map_info->id,
                    'department_id' => $item['department_id'],
                    'is_include_sub' => $item['is_include_sub']
                ];
            }
        } else {
            $department_arr [] = [
                'map_id' => $map_info->id,
                'department_id' => 0,
                'is_include_sub' => SettingEnums::IS_INCLUDE_SUB
            ];
        }
        $return_repair_map_rel_model = new MaterialSetReturnRepairMapDepartmentModel();
        $bool = $return_repair_map_rel_model->batch_insert($department_arr);
        if ($bool === false) {
            throw new BusinessException('资产退回组 - 新增申请人部门 - 关系数据-失败 = ' . json_encode($department_arr,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }

    /**
     * 资产退回映射 - 编辑
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function returnRepairMapEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateReturnRepairMapDb($params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-map-edit-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回映射 - 删除
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairMapDel($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //删除映射
            $map_info = $this->getReturnRepairMapInfo($params['id']);
            $this->logger->info('资产退回映射 - 删除前数据为：' . json_encode($map_info->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_info->delete();
            if ($bool === false) {
                throw new BusinessException('资产盘点组-删除-失败 = ' . json_encode(['id' => $map_info->id], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($map_info), ErrCode::$BUSINESS_ERROR);
            }

            //先删除原来的物料分类、barcode、申请人部门关系数据
            $map_rel_barcode_obj = $map_info->getBarcode();
            $this->logger->info('资产退回映射 - 删除前barcode - 关系数据为：' . json_encode($map_rel_barcode_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_barcode_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 删除原来barcode - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $map_rel_category_obj = $map_info->getCategory();
            $this->logger->info('资产退回映射 - 删除前物料分类 - 关系数据为：' . json_encode($map_rel_category_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_category_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 删除原来的物料分类 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $map_rel_department_obj = $map_info->getDepartment();
            $this->logger->info('资产退回映射 - 删除前申请人部门 -关系数据为：' . json_encode($map_rel_department_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $map_rel_department_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回映射 - 删除申请人部门 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-map-del-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产退回映射 - 查看
     * @param array $params 参数组
     * @return array
     */
    public function returnRepairMap($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $map_info = $this->getReturnRepairMapInfo($params['id']);
            $group_info = $this->getReturnRepairGroupInfo($map_info->group_id);
            $type_info = $this->getReturnRepairTypeInfo($map_info->type_id);
            $group_or_type_name = get_lang_field_name('name_', static::$language);
            $data = [
                'group_id' => $map_info->group_id,
                'type_id' => $map_info->type_id,
                'address' => $map_info->address,
                'group_name' => $group_info->$group_or_type_name,
                'type_name' => $type_info->$group_or_type_name,
                'barcodes' => implode(',', array_filter(array_column($map_info->getBarcode()->toArray(), 'barcode'))),
                'material_category_ids' => implode(',', array_filter(array_column($map_info->getCategory()->toArray(), 'category_id'))),
                'department_ids' => []
            ];
            $map_department_list = $map_info->getDepartment()->toArray();
            $department_ids = array_column($map_department_list, 'department_id');
            $department_repository = new DepartmentRepository();
            $department_list = $department_repository->getDepartmentByIds($department_ids, 2);

            // 上级部门信息
            $ancestry_dept_ids = array_column($department_list, 'ancestry', 'id');
            $ancestry_dept_item = $department_repository->getDepartmentByIds(array_values($ancestry_dept_ids), 2);

            // 合并当前部门和上级部门
            $all_dept_item = array_merge($department_list, $ancestry_dept_item);
            $all_dept_item = array_column($all_dept_item, null, 'id');

            foreach ($map_department_list as $item) {
                if (empty($item['department_id'])) {
                    continue;
                }
                $dept_info = $all_dept_item[$item['department_id']] ?? [];
                // 上级部门/组织
                $ancestry = $dept_info['ancestry'] ?? '0';
                $ancestry_name = $all_dept_item[$ancestry]['name'] ?? '';
                
                $data['department_ids'][] = [
                    'department_id' => $item['department_id'],
                    'department_name' => $dept_info['name'] ?? '',
                    'is_include_sub' => $item['is_include_sub'],
                    'ancestry' => $ancestry,
                    'ancestry_name' => $ancestry_name,
                ];
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-return-repair-map-view-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data ?? []
        ];
    }

    /**
     * 获取某个员工所在的资产退回组
     * @param integer $staff_id 员工工号
     * @return array
     */
    public function getStaffReturnGroups($staff_id)
    {
        if (empty($staff_id)) {
            return [];
        }
        $group_list = MaterialSetReturnRepairGroupStaffModel::find([
            'columns' => 'group_id',
            'conditions' => 'staff_id = :staff_id:',
            'bind' => ['staff_id' => $staff_id]
        ])->toArray();
        return array_column($group_list, 'group_id');
    }

    /**
     * 资产数据管控 - 列表
     * @return array
     */
    public function dataPermissionList()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            foreach (self::$data_permission as $id => $name) {
                $data[] = [
                    'id' => $id,
                    'name' => $name
                ];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-dataPermissionList-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产数据管控 - 组 - 列表
     * @param array $params 参数组
     * @return array
     */
    public function dataPermissionGroupList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialSetDataPermissionGroupModel::class]);
            $builder->where('main.is_deleted = :is_deleted: and module_id = :module_id:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'module_id' => $params['module_id']]);
            if (!empty($params['name'])) {
                $builder->andWhere('main.name like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            $builder->columns('count(main.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('main.id, main.name');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-data-permission-group-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取资产数据管控-额外的参数组信息
     * @param array $params 请求参数
     * @return array
     */
    public function getExtendDataPermissionGroupValidation(array $params)
    {
        $validate_param = self::$validate_data_permission_group_add;

        //工号组
        $validate_param['staffs'] = 'Required|Arr|ArrLenGeLe:1,100|>>>:' . static::$t->_('data_permission_staff_error');
        $validate_param['staffs[*]'] = 'IntGt:0';

        //是否看全部数据，1否时，部门必须选择
        if (!empty($params['is_see_all']) && $params['is_see_all'] == MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO) {
            $validate_param['department_ids'] = 'Required|Arr|ArrLenGe:1|>>>:' . static::$t->_('data_permission_department_error');
            $validate_param['department_ids[*].department_id']  = 'Required|IntGt:0';
            $validate_param['department_ids[*].is_include_sub'] = 'Required|IntIn:1,0';
        }

        //资产分类
        if (!empty($params['material_category_ids'])) {
            $validate_param['material_category_ids'] = 'Arr|ArrLenGeLe:0,500';
            $validate_param['material_category_ids[*]'] = 'Required|IntGt:0|>>>:param error[material_category_id]';
        }

        //隐藏字段
        if (!empty($params['hidden_fields'])) {
            $validate_param['hidden_fields'] = 'Arr';
            $validate_param['hidden_fields[*]'] = 'Required|StrLenGe:1|>>>:param error[hidden_fields]';
        }
        return $validate_param;

    }

    /**
     * 资产数据管控 - 组 - 新增
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function dataPermissionGroupAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateDataPermissionGroupDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-data-permission-group-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }


    /**
     * 资产数据管控 - 组信息db操作
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param string $type 操作类型，add新增，edit编辑
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function operateDataPermissionGroupDb($params, $user, $type = 'add')
    {
        //组下员工不可重复
        $unique_staff = array_unique(array_filter($params['staffs']));
        if (count($params['staffs']) != count($unique_staff)) {
            throw new ValidationException(static::$t->_('data_permission_staff_repeat'), ErrCode::$VALIDATE_ERROR);
        }

        //校验工号是否都是输入正式工的，含实习生的工号
        $staff_list = (new HrStaffRepository())->onlySearchStaff(['staff_info_id' => $params['staffs'], 'formal' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE], 'limit' => count($params['staffs'])]);
        if (count($staff_list) != count($params['staffs'])) {
            throw new ValidationException(static::$t->_('data_permission_staff_invalid'), ErrCode::$VALIDATE_ERROR);
        }

        //校验工号和其他组的工号是否有重复，如果有重复，则提示：{工号1}，{工号2}只能存在1个数据组中
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialSetDataPermissionGroupModel::class]);
        $builder->leftjoin(MaterialSetDataPermissionGroupStaffModel::class, 'staff.group_id = main.id', 'staff');
        $builder->columns('staff.staff_id');
        $builder->where('main.module_id = :module_id: and main.is_deleted = :is_deleted:', ['module_id' => $params['module_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('staff.staff_id', $params['staffs']);
        if ($type == 'edit') {
            $builder->andWhere('main.id != :id:', ['id' => $params['id']]);
        }
        $items = $builder->getQuery()->execute()->toArray();
        $staff_ids = array_unique(array_column($items, 'staff_id'));
        if ($staff_ids) {
            throw new ValidationException(static::$t->_('data_permission_staff_in_group', ['staff_id' => implode(',', $staff_ids)]), ErrCode::$VALIDATE_ERROR);
        }

        $now = date('Y-m-d H:i:s');
        $material_category_ids = (!empty($params['material_category_ids']) && $params['is_see_all'] == MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO) ? implode(',', $params['material_category_ids']) : '';
        $hidden_fields = !empty($params['hidden_fields']) ? implode(',', $params['hidden_fields']) : '';
        //新增映射信息
        if ($type == 'add') {
            $data = [
                'name' => $params['name'],
                'module_id' => $params['module_id'],
                'is_see_all' => $params['is_see_all'],
                'material_category_ids' => $material_category_ids,
                'hidden_fields' => $hidden_fields,
                'create_staff_id' => $user['id'],
                'create_name' => $user['name'],
                'created_at' => $now,
                'updated_at' => $now
            ];
            $group_info = new MaterialSetDataPermissionGroupModel();
            $bool = $group_info->i_create($data);
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 新增-失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }
        } else {
            //更新映射信息
            $group_info = $this->getDataPermissionGroupInfo($params['id']);
            $group_info->name = $params['name'];
            $group_info->module_id = $params['module_id'];
            $group_info->is_see_all = $params['is_see_all'];
            $group_info->material_category_ids = $material_category_ids;
            $group_info->hidden_fields = $hidden_fields;
            $group_info->update_staff_id = $user['id'];
            $group_info->update_name = $user['name'];
            $group_info->updated_at = $now;
            $bool = $group_info->save();
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 编辑-失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($group_info), ErrCode::$BUSINESS_ERROR);
            }

            //先删除原来部门关系数据
            $rel_department_obj = $group_info->getDepartments();
            $this->logger->info('资产数据管控 - 组 - 编辑前部门 - 关系数据为：' . json_encode($rel_department_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $rel_department_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 编辑前删除原来部门 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }

            $rel_staffs_obj = $group_info->getStaffs();
            $this->logger->info('资产数据管控 - 组 - 编辑前员工 - 关系数据为：' . json_encode($rel_staffs_obj->toArray(), JSON_UNESCAPED_UNICODE));
            $bool = $rel_staffs_obj->delete();
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 编辑前删除原来的员工 - 关系数据-失败', ErrCode::$BUSINESS_ERROR);
            }
        }

        //部门
        $department_arr = [];
        if (!empty($params['department_ids']) && $params['is_see_all'] == MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO) {
            foreach ($params['department_ids'] as $item) {
                $department_arr [] = [
                    'group_id' => $group_info->id,
                    'department_id' => $item['department_id'],
                    'is_include_sub' => $item['is_include_sub']
                ];
            }
            $data_permission_group_department_model = new MaterialSetDataPermissionGroupDepartmentModel();
            $bool = $data_permission_group_department_model->batch_insert($department_arr);
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 存储部门 - 关系数据-失败 = ' . json_encode($department_arr,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        //插入新的组下员工
        $group_staffs = [];
        if (!empty($params['staffs'])) {
            foreach ($params['staffs'] as $staff) {
                $group_staffs[] = [
                    'group_id' => $group_info->id,
                    'staff_id' => $staff,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }
            $data_permission_group_staff_model = new MaterialSetDataPermissionGroupStaffModel();
            $bool = $data_permission_group_staff_model->batch_insert($group_staffs);
            if ($bool === false) {
                throw new BusinessException('资产数据管控 - 组 - 存储组下员工-失败 = ' . json_encode($group_staffs,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }
        return true;
    }

    /**
     * 资产数据管控 - 组 - 编辑
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function dataPermissionGroupEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->operateDataPermissionGroupDb($params, $user, 'edit');
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-data-permission-group-edit-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 根据组ID获取某条资产数据管控组信息
     * @param integer $id 组ID
     * @return mixed
     * @throws ValidationException
     */
    public function getDataPermissionGroupInfo($id)
    {
        $group_info = MaterialSetDataPermissionGroupModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($group_info)) {
            throw new ValidationException(static::$t->_('data_permission_group_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $group_info;
    }

    /**
     * 资产数据管控 - 组 - 查看
     * @param array $params 参数组
     * @return array
     */
    public function dataPermissionGroup($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $group_info = $this->getDataPermissionGroupInfo($params['id']);
            $data = $group_info->toArray();
            $data['hidden_fields'] = $data['hidden_fields'] ? explode(',', $data['hidden_fields']) : [];
            $data['material_category_ids'] = $data['material_category_ids'] ? explode(',', $data['material_category_ids']) : [];
            $data['department_ids'] = [];
            //是否看全部数据 - 否 - 需要获取关联的部门数据范围
            if ($data['is_see_all'] == MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO) {
                $rel_department_list = $group_info->getDepartments()->toArray();

                $department_ids = array_column($rel_department_list, 'department_id');
                $department_repository = new DepartmentRepository();
                $department_list = $department_repository->getDepartmentByIds($department_ids, 2);

                // 上级部门信息
                $ancestry_dept_ids = array_column($department_list, 'ancestry', 'id');
                $ancestry_dept_item = $department_repository->getDepartmentByIds(array_values($ancestry_dept_ids), 2);

                // 合并当前部门和上级部门
                $all_dept_item = array_merge($department_list, $ancestry_dept_item);
                $all_dept_item = array_column($all_dept_item, null, 'id');
                foreach ($rel_department_list as $item) {
                    if (empty($item['department_id'])) {
                        continue;
                    }
                    $dept_info = $all_dept_item[$item['department_id']] ?? [];
                    // 上级部门/组织
                    $ancestry = $dept_info['ancestry'] ?? '0';
                    $ancestry_name = $all_dept_item[$ancestry]['name'] ?? '';

                    $data['department_ids'][] = [
                        'department_id' => $item['department_id'],
                        'department_name' => $dept_info['name'] ?? '',
                        'is_include_sub' => $item['is_include_sub'],
                        'deleted' => $dept_info['deleted'] ?? '',
                        'ancestry' => $ancestry,
                        'ancestry_name' => $ancestry_name,
                    ];
                }
            }

            //获取员工
            $rel_staffs = $group_info->getStaffs()->toArray();
            if ($rel_staffs) {
                $staff_ids = array_column($rel_staffs, 'staff_id');
                $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_ids, 'limit' => count($staff_ids)]);
                $staff_list = array_column($staff_list['data'] ?? [], null, 'staff_id');
                foreach ($rel_staffs as &$item) {
                    $staff_info = $staff_list[$item['staff_id']] ?? [];
                    $item['staff_name'] = $staff_info['staff_name'] ?? '';
                    $item['node_department_name'] = $staff_info['node_department_name'] ?? '';
                    $item['sys_store_name'] = $staff_info['store_name'] ?? '';
                    $item['state_text'] = $staff_info['state_text'] ?? '';
                }
            }
            $data['staffs'] = $rel_staffs;
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-data-permission-group-view-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 检测员工是否有权限
     * @param integer $staff_id 员工工号
     * @param integer $module_id 应用模块 1离职资产处理、2资产台账、3资产申请、4资产出库、5资产转移
     * @return int
     * @throws ValidationException
     */
    public function checkStaffDataPermission($staff_id, $module_id)
    {
        if (!$staff_id) {
            throw new ValidationException(static::$t->_('corruption_not_authorized_operate'), ErrCode::$VALIDATE_ERROR);
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialSetDataPermissionGroupModel::class]);
        $builder->leftjoin(MaterialSetDataPermissionGroupStaffModel::class, 'staff.group_id = main.id', 'staff');
        $builder->columns('main.id');
        $builder->where('main.module_id = :module_id: and main.is_deleted = :is_deleted:', ['module_id' => $module_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('staff.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        $group_info = $builder->getQuery()->getSingleResult();
        if (empty($group_info)) {
            throw new ValidationException(static::$t->_('corruption_not_authorized_operate'), ErrCode::$VALIDATE_ERROR);
        }
        return intval($group_info->id);
    }

    /**
     * 获取某个员工所在的资产数据管控组
     * @param integer $staff_id 员工工号
     * @param integer $module_id 应用模块 1离职资产处理、2资产台账、3资产申请、4资产出库、5资产转移
     * @return array
     * @throws ValidationException
     */
    public function getStaffDataPermissionGroup($staff_id, $module_id)
    {
        $data = [
            'node_department_id' => [],
            'material_category_ids' => [],
            'hidden_fields' => []
        ];
        $group_id = $this->checkStaffDataPermission($staff_id, $module_id);
        $group_info = $this->getDataPermissionGroupInfo($group_id);
        //是否看全部数据，1否
        if ($group_info->is_see_all == MaterialEnums::MATERIAL_SET_DATA_PERMISSION_IS_SEE_ALL_NO) {
            // 获取部门的子部门
            $all_dept_ids = [];
            $rel_department_list = $group_info->getDepartments()->toArray();
            $department_repository = new DepartmentRepository();
            foreach ($rel_department_list as $dept) {
                // 含子部门
                if ($dept['is_include_sub'] == SettingEnums::IS_INCLUDE_SUB) {
                    // 返回结果带本部门+子部门
                    $dept_ids = $department_repository->getDepartmentSubListByIds($dept['department_id'], 2);
                    $dept_ids = array_column($dept_ids, 'id');
                    $all_dept_ids = array_merge($all_dept_ids, $dept_ids);
                } else {
                    $all_dept_ids[] = $dept['department_id'];
                }
            }
            $data['node_department_id'] = $all_dept_ids;
            $data['material_category_ids'] = $group_info->material_category_ids ? explode(',', $group_info->material_category_ids) : [];
        }
        $data['hidden_fields'] = $group_info->hidden_fields ? explode(',', $group_info->hidden_fields) : [];
        return $data;
    }

}
