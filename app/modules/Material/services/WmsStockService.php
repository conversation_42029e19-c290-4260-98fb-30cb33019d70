<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\MaterialPackageStockModel;
use App\Modules\Common\Services\DownloadCenterService;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class WmsStockService extends BaseService
{
    private static $instance;

    private function __construct()
    {

    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'pageSize',
        'pageNum',
    ];

    /**
     * 查询条件
     * @return array
     */
    public function extendValidation()
    {
        return [
            //默认选择当前日期的前一天，允许手工修改，可以选择当前日期之前30天以内的日期
            'statistical_date' => 'Required|Date|DateFromTo:' . date('Y-m-d', strtotime('-30 day')) . ',' . date('Y-m-d', strtotime('-1 day')),
            'store_id'         => 'StrLenGeLe:0,10',
            'barcode'          => 'StrLenGeLe:0,30',
        ];
    }

    /**
     * 列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getList(array $params, array $user)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $count = $this->getDataTotal($params, $user);
            if ($count) {
                $data['items'] = $this->getDataList($params, $user);
            }
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('进销存-列表失败:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return mixed
     * @throws GuzzleException
     */
    public function export(array $params, array $user)
    {
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDataTotal($params, $user) > MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DOWNLOAD_LIMIT) {
            $result         = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], DownloadCenterEnum::MATERIAL_WMS_PACKAGE_STOCK, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $result         = $this->getSyncExportData($params, $user);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url'      => $result['data'],
            ];
        }
        return $result;
    }

    /**
     * 获取数据的总量
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return mixed
     */
    public function getDataTotal(array $params, array $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(main.id) as count');
        $builder->from(['main' => MaterialPackageStockModel::class]);
        $builder = $this->getCondition($builder, $params, $user);
        return intval($builder->getQuery()->getSingleResult()->count);
    }

    /**
     * 获取数据列表
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @param bool $is_export 是否导出 true是，false否
     * @return array
     */
    public function getDataList(array $params, array $user, bool $is_export = false)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $columns = 'main.store_name, main.store_id, main.barcode, main.' . WmsAllotService::getInstance()->getGoodsName() . ' as goods_name, main.unit,';
        $columns.= 'main.init_number, main.store_number, main.outbound_number, main.surplus_number';
        $builder->columns($columns);
        $builder->from(['main' => MaterialPackageStockModel::class]);
        $builder = $this->getCondition($builder, $params, $user);
        $builder->limit($page_size, $offset);
        $builder->orderby('main.store_id ASC, main.barcode ASC');
        $list = $builder->getQuery()->execute()->toArray();

        //导出需要格式化
        if ($is_export) {
            $excel_data = [];
            foreach ($list as $item) {
                $excel_data[] = [
                    $item['store_name'],
                    $item['store_id'],
                    $item['barcode'],
                    $item['goods_name'],
                    $item['unit'],
                    $item['init_number'],
                    $item['store_number'],
                    $item['outbound_number'],
                    $item['surplus_number'],
                ];
            }
            $list = $excel_data;
        }
        return $list;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $params 查询条件
     * @param array $user 当前登陆者信息组
     * @return object
     */
    public function getCondition(object $builder, array $params, array $user)
    {
        //列表数据范围 - 有符合条件的
        $permission_params = WmsAllotService::getInstance()->getStaffSeePermission($user);
        if (empty($permission_params)) {
            $permission_params['store_ids'] = [$user['sys_store_id']];
        }
        $params = array_merge($params, $permission_params);

        $store_id         = $params['store_id'] ?? '';        //网点编码
        $barcode          = $params['barcode'] ?? [];         //barcode
        $statistical_date = $params['statistical_date'] ?? '';//统计日期
        $category         = $params['category'] ?? [];        //查询网点类型属于当前用户配置的网点类型的数据
        $store_ids        = $params['store_ids'] ?? [];       //查询网点属于当前用户管辖的大区或片区或本人所属网点范围内的网点的数据

        if (!empty($store_id)) {
            $builder->andWhere('main.store_id = :store_id:', ['store_id' => $store_id]);
        }
        if (!empty($barcode)) {
            $builder->andWhere('main.barcode = :barcode:', ['barcode' => $barcode]);
        }
        if (!empty($statistical_date)) {
            $builder->andWhere('main.statistical_date = :statistical_date:', ['statistical_date' => $statistical_date]);
        }

        //查询网点类型属于当前用户配置的网点类型的数据
        if (!empty($category)) {
            $builder->inWhere('main.category', $category);
        }

        //查询网点属于当前用户管辖的大区或片区或本人所属网点范围内的网点的数据
        if (!empty($store_ids)) {
            $builder->inWhere('main.store_id ', $store_ids);
        }

        return $builder;
    }

    /**
     * 同步导出excel
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws GuzzleException
     */
    public function getSyncExportData(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            // 同步下载, 最多1w条
            $params['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $params['pageSize'] = MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DOWNLOAD_LIMIT;

            // 获取数据
            $excel_data = $this->getDataList($params, $user, true);

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = 'material_wms_package_stock_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('进销存-数据同步导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取导出的Excel表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('material_package_stock.store_name'),//网点名称
            static::$t->_('material_package_stock.store_id'),//网点编码
            static::$t->_('material_package_stock.barcode'),//Barcode
            static::$t->_('material_package_stock.name'),//名称
            static::$t->_('material_package_stock.unit'),//单位
            static::$t->_('material_package_stock.init_number'),//初始库存
            static::$t->_('material_package_stock.store_number'),//今日入库
            static::$t->_('material_package_stock.outbound_number'),//今日出库
            static::$t->_('material_package_stock.surplus_number'),//结余库存
        ];
    }
}
