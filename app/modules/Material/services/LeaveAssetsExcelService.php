<?php

namespace App\Modules\Material\Services;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Repository\backyard\StaffResignRepository;
use App\Repository\HrStaffRepository;

class LeaveAssetsExcelService extends BaseService
{
    //导出
    public static $validate_list_export = [
        'export_type' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_VALIDATE, //导出类型
        'asset_department_status' => 'IntIn:' . MaterialEnums::ASSET_DEPARTMENT_STATUS_VALIDATE,//资产处理状态
        'asset_department_progress' => 'Arr',//单据进度
        'asset_department_progress[*]' => 'Required|IntIn:' . MaterialEnums::ASSET_DEPARTMENT_PROGRESS_VALIDATE,//单据进度
        'manager_progress' => 'Arr',//主管处理进度
        'manager_progress[*]' => 'Required|IntIn:' . MaterialEnums::MANAGER_PROGRESS_VALIDATE,//主管处理进度
        'is_full_amount' => 'IntIn:' . MaterialEnums::IS_FULL_AMOUNT_VALIDATE,//是否足额扣款
        'staff_info_id' => 'IntGt:0',//工号
        'staff_state' => 'Arr',//在职状态
        'staff_state[*]' => 'IntGt:0',//在职状态
        'last_work_date_start' => 'Date',//最后工作日-起始
        'last_work_date_end' => 'Date',//最后工作日-截止
        'leave_date_start' => 'Date',//待离职/离职日期-起始
        'leave_date_end' => 'Date',//待离职/离职日期-截止
        'node_department_id' => 'IntGt:0',//部门ID
        'sys_store_id' => 'Arr|ArrLenGeLe:1,20',//网点id
        'sys_store_id[*]' => 'Str',
        'job_title' => 'Arr|ArrLenGeLe:1,20',//职位id
        'job_title[*]' => 'Int',
        'working_country' => 'Arr',//工作所在国家
        'working_country[*]' => 'IntIn:' . StaffInfoEnums::WORKING_COUNTRY_VALIDATE,//工作所在国家
        'company_id[*]' => 'IntGt:0',//所属公司
        'updated_at_start' => 'Date',//资产操作日期-开始
        'updated_at_end' => 'Date',//资产操作日期-结束
        'created_at_start' => 'Date',//创建日期-开始
        'created_at_end' => 'Date',//创建日期-结束
    ];

    public static $result_column = 14;
    public static $max_data_number = 10000;
    //匹配金额最多2位小数
    public static $validate_amount = '/^[0-9]+(\.[0-9]{0,2}0{0,})?$/';
    //批量修改定位详情数据时空值的表达
    public static $empty_asset_code = 'no_asset_code';
    public static $empty_sn_code = 'no_sn_code';
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LeaveAssetsExcelService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 导出明细-离职资产-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getExportCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['detail' => MaterialLeaveAssetsDetailModel::class]);
        $builder->leftJoin(MaterialLeaveAssetsModel::class, 'main.id = detail.leave_assets_id', 'main');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
        $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->columns('count(detail.id) as count');
        //组合搜索条件
        $builder = LeaveAssetsService::getInstance()->getCondition($builder, $condition, $user, 'main.');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 导出明细-离职资产,根据列表条件查询明细
     * @param $params
     * @param $locale
     * @param $user
     * @param int $count
     * @return array
     * @date 2023/3/24
     */
    public function getExportList($params, $locale, $user, $count)
    {

        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $export_data = [];
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'detail.id, main.staff_info_id, main.staff_name, main.job_name, staff.state as staff_state, staff.wait_leave_state, staff.last_work_date, staff.leave_date, staff.hire_type, main.node_department_name,
                 main.sys_store_id, main.sys_store_name, main.working_country, main.company_name, main.all_deduct_amount, main.deduct_amount, main.return_amount, main.loss_amount, main.asset_department_remark,
                 detail.barcode, detail.asset_code, detail.sn_code, detail.asset_name_zh, detail.asset_name_en, detail.asset_name_local, detail.model,
                 detail.use, detail.purchase_price, detail.net_value, detail.asset_status, detail.asset_state, detail.asset_handling_status, deduct_reason';
                $builder->columns($columns);
                $builder->from(['detail' => MaterialLeaveAssetsDetailModel::class]);
                $builder->leftJoin(MaterialLeaveAssetsModel::class, 'main.id = detail.leave_assets_id', 'main');
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                $builder->andWhere('detail.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
                //组合搜索条件
                $builder = LeaveAssetsService::getInstance()->getCondition($builder, $params, $user, 'main.');
                $builder->limit($page_size, $offset);
                $builder->orderby('detail.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                //名称key
                $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
                //资产状况集合
                $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
                //图片
                $attachment = (new MaterialAttachmentModel())->getColumnArrUrl($items, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL);
                //雇佣类型
                $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
                foreach ($items as $item) {
                    //国家
                    $item['working_country_text'] = isset(StaffInfoEnums::$working_country[$item['working_country']]) ? static::$t[StaffInfoEnums::$working_country[$item['working_country']]] : '';
                    //离职状态
                    $staff_state = ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['staff_state'];
                    $item['staff_state_text'] = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '';
                    //使用状态
                    $item['asset_status_text'] = isset(MaterialEnums::$asset_status[$item['asset_status']]) ? static::$t[MaterialEnums::$asset_status[$item['asset_status']]] : '';
                    //主管处理状况
                    $item['asset_state_text'] = isset($leave_asset_state_list[$item['asset_state']]) ? static::$t[$leave_asset_state_list[$item['asset_state']]] : '';
                    //使用方向
                    $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
                    //资产处理情况
                    $item['asset_handling_status_text'] = isset(MaterialEnums::$asset_handling_status_list[$item['asset_handling_status']]) ? static::$t[MaterialEnums::$asset_handling_status_list[$item['asset_handling_status']]] : '';
                    $item['images'] = '';
                    if (!empty($attachment[$item['id']])) {
                        $item['images'] = implode(',', array_column($attachment[$item['id']], 'object_url'));
                    }
                    $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['asset_name_en'];
                    $item['hire_type_text'] = $hire_type_enum[$item['hire_type']] ?? '';
                    //导出数据
                    $tmp = [
                        $item['staff_info_id'],
                        $item['staff_name'],
                        $item['job_name'],
                        $item['hire_type_text'],
                        $item['staff_state_text'],
                        $item['last_work_date'],
                        $item['leave_date'],
                        $item['node_department_name'],
                        $item['sys_store_id'],
                        $item['sys_store_name'],
                        $item['working_country_text'],
                        $item['company_name'],
                        $item['all_deduct_amount'],
                        $item['deduct_amount'],
                        $item['return_amount'],
                        $item['loss_amount'],
                        $item['asset_department_remark'],
                        $item['barcode'],
                        $item['asset_code'],
                        $item['sn_code'],
                        $item['asset_name'],
                        $item['model'],
                        $item['use_text'],
                        $item['purchase_price'],
                        $item['net_value'],
                        $item['asset_status_text'],
                        $item['asset_state_text'],
                        $item['asset_handling_status_text'],
                        $item['deduct_reason'],
                        $item['images'],
                    ];
                    $export_data[] = $tmp;
                }
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-export-detail-list-failed:' . $real_message);
        }
        return $export_data;
    }

    /**
     * 导出明细-转移记录-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getExportTransferCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialLeaveAssetsModel::class]);
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
        $builder->leftJoin(MaterialAssetTransferLogModel::class, 'main.staff_info_id = l.to_staff_id', 'l');
        $builder->leftJoin(MaterialAssetsModel::class, 'asset.id = l.asset_id', 'asset');
        $builder->andWhere('l.status = :status: or l.updated_at >= main.created_at ', ['status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED]);
        $builder->columns('count(l.id) as count');
        //组合搜索条件
        $builder = LeaveAssetsService::getInstance()->getCondition($builder, $condition, $user, 'main.');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 导出明细-转移记录,根据列表条件查询明细
     * @param $params
     * @param $locale
     * @param $user
     * @param int $count
     * @return array
     * @date 2023/3/24
     */
    public function getExportTransferList($params, $locale, $user, $count)
    {
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $export_data = [];
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.staff_info_id, main.staff_name, main.job_name, staff.state as staff_state, staff.wait_leave_state, staff.last_work_date, staff.leave_date, staff.hire_type, main.node_department_name,
                  main.sys_store_id, main.sys_store_name, main.working_country, main.company_name, asset.bar_code as barcode, asset.name_zh, asset.name_en, asset.name_local,
                  asset.asset_code, asset.sn_code, asset.old_asset_code, asset.model, asset.use, asset.purchase_price, asset.net_value, asset.currency,
                  l.status as transfer_status, l.transfer_at, l.finished_at, l.transfer_type, l.aor_no, l.remark, l.transfer_remark, l.updated_at';
                $builder->columns($columns);
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                $builder->leftJoin(MaterialAssetTransferLogModel::class, 'main.staff_info_id = l.to_staff_id', 'l');
                $builder->leftJoin(MaterialAssetsModel::class, 'asset.id = l.asset_id', 'asset');
                $builder->andWhere('l.status = :status: or l.updated_at >= main.created_at ', ['status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED]);
                //组合搜索条件
                $builder = LeaveAssetsService::getInstance()->getCondition($builder, $params, $user, 'main.');
                $builder->limit($page_size, $offset);
                $builder->orderby('l.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                //名称key
                $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'name_' . MaterialClassifyEnums::$language_fields[$locale] : 'name_local';
                //当前国家币种
                $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
                //雇佣类型枚举
                $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
                foreach ($items as $item) {
                    //国家
                    $item['working_country_text'] = isset(StaffInfoEnums::$working_country[$item['working_country']]) ? static::$t[StaffInfoEnums::$working_country[$item['working_country']]] : '';
                    //离职状态
                    $staff_state = ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['staff_state'];
                    $item['staff_state_text'] = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '';
                    //资产名称
                    $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['name_en'];
                    //使用方向
                    $item['use_text'] = isset(MaterialEnums::$use[$item['use']]) ? static::$t[MaterialEnums::$use[$item['use']]] : '';
                    //转移状态
                    $item['transfer_status_text'] = isset(MaterialEnums::$transfer_log_status[$item['transfer_status']]) ? static::$t[MaterialEnums::$transfer_log_status[$item['transfer_status']]] : '';
                    $item['transfer_type_text'] = isset(MaterialEnums::$transfer_type[$item['transfer_type']]) ? static::$t[MaterialEnums::$transfer_type[$item['transfer_type']]] : '';
                    //转换汇率
                    $amount = $item['purchase_price'];
                    $net_value = $item['net_value'];
                    if ($item['currency'] != $default_currency['code']) {
                        //转换成本国币种
                        $amount = EnumsService::getInstance()->currencyAmountConversion((string)$item['currency'], (string)$item['purchase_price'], 3);
                        $amount = round($amount, 2);
                        $net_value = EnumsService::getInstance()->currencyAmountConversion((string)$item['currency'], (string)$item['net_value'], 3);
                        $net_value = round($net_value, 2);
                    }
                    //转移/拒收原因
                    $item['transfer_reject_remark'] = '';
                    //接收/转移/拒收日期
                    $item['transfer_finished_at'] = '';
                    if ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                        $item['transfer_reject_remark'] = $item['transfer_remark'];
                        $item['transfer_finished_at'] = $item['transfer_at'];
                    } elseif ($item['transfer_status'] == MaterialEnums::TRANSFER_LOG_STATUS_REJECTED) {
                        $item['transfer_reject_remark'] = $item['remark'];
                        $item['transfer_finished_at'] = $item['finished_at'];
                    }
                    //雇佣类型
                    $item['hire_type_text'] = $hire_type_enum[$item['hire_type']] ?? '';
                    //导出数据
                    $tmp = [
                        $item['staff_info_id'],
                        $item['staff_name'],
                        $item['job_name'],
                        $item['hire_type_text'],
                        $item['staff_state_text'],
                        $item['last_work_date'],
                        $item['leave_date'],
                        $item['node_department_name'],
                        $item['sys_store_id'],
                        $item['sys_store_name'],
                        $item['working_country_text'],
                        $item['company_name'],
                        $item['barcode'],
                        $item['asset_code'],
                        $item['sn_code'],
                        $item['asset_name'],
                        $item['model'],
                        $item['use_text'],
                        $amount,
                        $net_value,
                        $item['transfer_status_text'],
                        $item['transfer_finished_at'],
                        $item['transfer_type_text'],
                        $item['aor_no'],
                        $item['transfer_reject_remark'],
                    ];
                    $export_data[] = $tmp;
                }
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-export-detail-transfer-list-failed:' . $real_message);
        }
        return $export_data;
    }

    /**
     * 列表-导出按钮
     * @param $params
     * @param string $locale 语言
     * @param $user
     * @param integer $count 导出记录总数
     * @return array
     */
    public function exportSearch($params, $locale, $user, $count)
    {
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            //开始查询
            if ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_COUNT) {
                $condition = $params;
                $page_size = 2000;
                $step = ceil($count / $page_size);
                $row_values = [];
                for ($i = 1; $i <= $step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $list = LeaveAssetsService::getInstance()->getList($condition, $locale, $user, true, $count);
                    $rows = $list['data']['items'] ?? [];
                    $row_values = array_merge($row_values, $rows);
                }
                $file_name = '离职资产汇总表&leave asses summary&รายการสินทรัพย์ ที่ออกจากตำแหน่ง_' . date('YmdHis');

            } elseif ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_DETAIL) {
                $condition = $params;
                $page_size = 2000;
                $step = ceil($count / $page_size);
                $row_values = [];
                for ($i = 1; $i <= $step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $rows = $this->getExportList($condition, $locale, $user, $count);
                    $row_values = array_merge($row_values, $rows);
                }
                $file_name = '离职资产明细表&leave asses detail&รายละเอียดสินทรัพย์ ที่ออกจากงาน_' . date('YmdHis');
            } elseif ($params['export_type'] == MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER) {
                $condition = $params;
                $file_name = 'leave_asset_transfer_detail';
                $page_size = 2000;
                $step = ceil($count / $page_size);
                $row_values = [];
                for ($i = 1; $i <= $step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $rows = $this->getExportTransferList($condition, $locale, $user, $count);
                    $row_values = array_merge($row_values, $rows);
                }
            }
            //生成文件
            $header = $this->getExcelHeader($params['export_type']);
            $result = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-leave-asset-search-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }

    /**
     * 离职资产 新增导入任务
     * @param $file
     * @param $excel_data
     * @param $user
     * @return array
     * @date 2022/7/18
     */
    public function batchEditByFile($file, $excel_data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //跳过空行
            $clear_excel_data = [];
            foreach ($excel_data as $key => $row) {
                if (empty($row[0])) {
                    break;
                }
                $clear_excel_data[$key] = $row;
            }
            //验证条数
            if (count($clear_excel_data) <= 0 || count($clear_excel_data) > self::$max_data_number) {
                throw new ValidationException(self::$t['leave_asset_data_exceeds_the_limit'], ErrCode::$VALIDATE_ERROR);
            }
            // 文件生成OSS链接
            $file_path = sys_get_temp_dir() . '/' . $file->getName();
            $file->moveTo($file_path);
            $oss_result = OssHelper::uploadFile($file_path);
            if (!isset($oss_result['object_url']) || empty($oss_result['object_url'])) {
                throw new ValidationException(self::$t['leave_asset_batch_edit_upload_error'], ErrCode::$VALIDATE_ERROR);
            }
            // 导入中心
            $type = ImportCenterEnums::TYPE_LEAVE_ASSET_BATCH_EDIT;
            $bool = ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], $type);
            if (!$bool) {
                throw new ValidationException(self::$t['add_import_center_error'], ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('batchEditByFile-离职资产批量编辑失败-' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 批量上传资产
     * @param $file_data
     * @param $user
     * @return array
     * @date 2023/4/5
     */
    public function leaveAssetBatchUpload($file_data, $user)
    {
        //跳过空行
        $excel_data = [];
        foreach ($file_data as $key => $row) {
            if (empty($row[0])) {
                break;
            }
            $excel_data[$key] = $row;
        }
        $result_data = $excel_data;
        //1. 格式化数组
        $all_num = count($excel_data);
        $excel_data = $this->excelToData($excel_data);
        //2. 按每个员工维度开始执行修改
        $leave_asset_detail_model = new MaterialLeaveAssetsDetailModel();
        $now_date = date('Y-m-d H:i:s');
        //操作记录的操作人信息
        $operator = [
            'staff_info_id' => $user['id'],
            'staff_info_name' => $user['name'],
            'node_department_id' => $user['department_id'],
            'node_department_name' => $user['department'],
            'job_title' => $user['job_title_id'],
            'job_name' => $user['job_title'],
            'operation_at' => $now_date
        ];
        $success_num = $failed_num = 0;
        //按员工维度循环处理
        foreach ($excel_data as $staff_id => $one_asset_data) {
            //重置db
            $db = null;
            //重置message
            $message = $real_message = '';
            try {
                //基础校验枚举值
                $is_validate_true = true;
                //校验主表字段一致性
                $validate_main_data = [];
                //校验行是否重复
                $detail_line = [];
                foreach ($one_asset_data['detail'] as $line => $info) {
                    $base_validate = '';
                    if (empty($info['staff_info_id']) || !is_numeric($info['staff_info_id'])) {
                        $base_validate .= self::$t['excel_result_leave_asset_staff_info_id_error'] . ';';
                    }
                    if (!in_array($info['operation_type'], [MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT, MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_UPDATE])) {
                        $base_validate .= self::$t['excel_result_leave_asset_operation_type_error'] . ';';
                    }
                    if (!in_array($info['is_add'], ['Y', 'N'])) {
                        $base_validate .= self::$t['excel_result_leave_asset_is_add_error'] . ';';
                    }
                    //非必填
                    if (!empty($info['asset_code']) && mb_strlen($info['asset_code']) > 50) {
                        $base_validate .= self::$t['excel_result_leave_asset_asset_code_error'] . ';';
                    }
                    if (!empty($info['sn_code']) && mb_strlen($info['sn_code']) > 50) {
                        $base_validate .= self::$t['excel_result_leave_asset_sn_code_error'] . ';';
                    }
                    if (!empty($info['asset_handling_status'])) {
                        if (!key_exists($info['asset_handling_status'], MaterialEnums::$asset_handling_status_list)) {
                            $base_validate .= self::$t['excel_result_leave_asset_asset_handling_status_error'] . ';';
                        }
                        //检测某barcode是否可选择"低值易耗不强制归还"资产处理情况
                        if (!LeaveAssetsService::getInstance()->checkBarcodeIsLowValue($info['asset_handling_status'], $info['barcode'])) {
                            $base_validate .= self::$t['asset_low_value_cannot_select'] . ';';
                        }
                    }
                    //金额校验不能大于999999999.99,最多2位小数
                    if (!empty($info['deduct_amount'])) {
                        if (!is_numeric($info['deduct_amount']) || !preg_match(self::$validate_amount, $info['deduct_amount']) || bccomp($info['deduct_amount'], MaterialEnums::MAX_AMOUNT_VALUE, 2) == 1) {
                            $base_validate .= self::$t['excel_result_leave_asset_deduct_amount_error'] . ';';
                        }
                        //检测已还完好，或者在他人名下的资产，扣费金额只能是0
                        if (!empty($info['asset_handling_status']) && !LeaveAssetsService::getInstance()->checkDeductAmountIsZero($info['asset_handling_status'], $info['deduct_amount'])) {
                            $base_validate .= self::$t['asset_deduct_amount_just_zero'] . ';';
                        }
                    }
                    if (!empty($info['main_deduct_amount'])) {
                        if (!is_numeric($info['main_deduct_amount']) || !preg_match(self::$validate_amount, $info['main_deduct_amount']) || bccomp($info['main_deduct_amount'], MaterialEnums::MAX_AMOUNT_VALUE, 2) == 1) {
                            $base_validate .= self::$t['excel_result_leave_asset_main_deduct_amount_error'] . ';';
                        }
                    }
                    if (!empty($info['main_return_amount'])) {
                        if (!is_numeric($info['main_return_amount']) || !preg_match(self::$validate_amount, $info['main_return_amount']) || bccomp($info['main_return_amount'], MaterialEnums::MAX_AMOUNT_VALUE, 2) == 1) {
                            $base_validate .= self::$t['excel_result_leave_asset_main_return_amount_error'] . ';';
                        }
                    }
                    //操作类型+资产处理状态校验
                    if ($info['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT && empty($info['asset_department_status'])) {
                        //编辑时资产处理状态不能为空
                        $base_validate .= self::$t['excel_result_leave_asset_asset_department_status_edit_empty_error'] . ';';
                    } elseif ($info['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT && $info['asset_department_status'] == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                        //编辑操作且资产处理状态为'已处理',所有行的资产处理情况必填
                        if (empty($info['asset_handling_status'])) {
                            $base_validate .= self::$t['excel_result_leave_asset_asset_handling_status_empty_error'] . ';';
                        }
                    } elseif ($info['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_UPDATE) {
                        if (!(empty($info['asset_department_status']) || $info['asset_department_status'] == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE)) {
                            //更新操作,资产处理状态只能是空或'已处理'
                            $base_validate .= self::$t['excel_result_leave_asset_asset_department_status_update_status_error'] . ';';
                        }
                        if ($info['is_add'] == 'Y' && empty($info['asset_handling_status'])) {
                            //更新操作新增资产时,资产处理情况必填
                            $base_validate .= self::$t['excel_result_leave_asset_asset_handling_status_empty_error'] . ';';
                        }
                    }
                    //资产处理状态枚举检验
                    if (!empty($info['asset_department_status'])) {
                        if (!key_exists($info['asset_department_status'], MaterialEnums::$asset_department_status_list)) {
                            $base_validate .= self::$t['excel_result_leave_asset_asset_department_status_error'] . ';';
                        }
                    }
                    //同一个离职员工下的数据, 操作类型和主表值必须相同
                    if (empty($validate_main_data)) {
                        $validate_main_data['operation_type'] = $info['operation_type']; //操作类型
                        $validate_main_data['main_deduct_amount'] = $info['main_deduct_amount']; //已扣费金额
                        $validate_main_data['main_return_amount'] = $info['main_return_amount']; //已还金额
                        $validate_main_data['asset_department_status'] = $info['asset_department_status']; //资产部处理状态
                        $validate_main_data['main_asset_department_remark'] = $info['main_asset_department_remark']; //资产部操作备注
                    } else {
                        $this_validate_data = [];
                        $this_validate_data['operation_type'] = $info['operation_type'];
                        $this_validate_data['main_deduct_amount'] = $info['main_deduct_amount'];
                        $this_validate_data['main_return_amount'] = $info['main_return_amount'];
                        $this_validate_data['asset_department_status'] = $info['asset_department_status']; //资产部处理状态
                        $this_validate_data['main_asset_department_remark'] = $info['main_asset_department_remark'];
                        //非首次进入和上一次的对比
                        if (implode('||', $validate_main_data) != implode('||', $this_validate_data)) {
                            $base_validate .= self::$t['excel_result_leave_asset_main_data_inconsistent'] . ';';
                        }
                    }
                    //重复数据校验: 同一行数据不能填写多次
                    $tmp_asset_code_key = !empty($info['asset_code']) ? $info['asset_code'] : self::$empty_asset_code;
                    $tmp_sn_code_key = !empty($info['sn_code']) ? $info['sn_code'] : self::$empty_sn_code;
                    if (!isset($detail_line[$info['barcode']][$tmp_asset_code_key][$tmp_sn_code_key])) {
                        //首次设置行号
                        $detail_line[$info['barcode']][$tmp_asset_code_key][$tmp_sn_code_key] = $line;
                    } else {
                        //设置过了说明重复出现了
                        $base_validate .= self::$t['excel_result_leave_asset_line_repeated'] . ';';
                    }

                    if (!empty($base_validate)) {
                        $is_validate_true = false;
                        $result_data[$line][self::$result_column] = $base_validate;
                    }
                }
                if (!$is_validate_true) {
                    //有验证错误,其他行也报错
                    throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                }
                //2.1 定位离职资产主记录 理论上来说待处理的只有一条
                $this_leave_asset = MaterialLeaveAssetsModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id: and is_valid = :is_valid:',
                    'bind' => [
                        'staff_info_id' => $one_asset_data['staff_info_id'],
                        'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES,
                    ],
                    'for_update' => true
                ]);
                //没定位到, 记录错误, 进行下一个员工处理
                if (empty($this_leave_asset)) {
                    throw new ValidationException(self::$t['excel_result_not_found_leave_asset'], ErrCode::$VALIDATE_ERROR);
                }
                //如果当前数据是已完成状态, 则不允许编辑操作
                if ($validate_main_data['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT && $this_leave_asset->asset_department_status == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    throw new ValidationException(self::$t['excel_result_cannot_edit_done_status'], ErrCode::$VALIDATE_ERROR);
                }
                //如果当前数据不是已完成状态, 则不允许更新操作
                if ($validate_main_data['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_UPDATE && $this_leave_asset->asset_department_status != MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    throw new ValidationException(self::$t['excel_result_leave_asset_operation_type_illegal'], ErrCode::$VALIDATE_ERROR);
                }
                //离职 或者 待离职 通过
                if (!LeaveAssetsService::getInstance()->checkStaffState($one_asset_data['staff_info_id'])) {
                    throw new ValidationException(self::$t['excel_result_leave_asset_staff_info_state_error'], ErrCode::$VALIDATE_ERROR);
                }
                //如果是by申请离职,需要验证不是审批通过不可编辑,因为审批通过后会重置资产处理状态
                if (!empty($this_leave_asset->staff_resign_id)) {
                    $staff_resign_info = StaffResignRepository::getInstance()->getOneById($this_leave_asset->staff_resign_id);
                    if (!in_array($staff_resign_info['status'], [StaffResignRepository::$audit_status_approved, StaffResignRepository::$audit_status_timeout])) {
                        throw new ValidationException(self::$t['excel_result_staff_resign_not_approved'], ErrCode::$VALIDATE_ERROR);
                    }
                }
                //2.2 定位离职资产详情记录
                //查找数据库中的详情
                $db_asset_detail = MaterialLeaveAssetsDetailModel::find([
                    'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted:',
                    'bind' => [
                        'leave_assets_id' => $this_leave_asset->id,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED
                    ]
                ]);
                $db_asset_detail_arr = $db_asset_detail->toArray();
                //没有数据, 写上结果,继续处理下个员工
                if (empty($db_asset_detail_arr)) {
                    throw new ValidationException(self::$t['excel_result_not_found_leave_asset_detail'], ErrCode::$VALIDATE_ERROR);
                }

                //处理详情数组方便定位
                $db_asset_detail_new = [];
                foreach ($db_asset_detail_arr as $detail_k => $detail_v) {
                    if (empty($detail_v['barcode'])) {
                        continue;
                    }
                    //没有资产编码的以barcode维度存储, 有资产编码的以barcode维度+资产编码维度存储
                    $barcode_key = $detail_v['barcode'];
                    $asset_code_key = self::$empty_asset_code;
                    $sn_code_key = self::$empty_sn_code;
                    if (!empty($detail_v['asset_code'])) {
                        $asset_code_key = $detail_v['asset_code'];
                    }
                    if (!empty($detail_v['sn_code'])) {
                        $sn_code_key = $detail_v['sn_code'];
                    }
                    $db_asset_detail_new[$barcode_key][$asset_code_key][$sn_code_key][] = $detail_v;
                }
                //找barcode
                $barcode_data = [];
                $barcode_arr = array_values($one_asset_data['barcode_arr']);
                if (!empty($barcode_arr)) {
                    $barcode_info = MaterialSauModel::find([
                        'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                        'bind' => [
                            'barcode' => $barcode_arr,
                            'is_deleted' => GlobalEnums::IS_NO_DELETED
                        ]
                    ])->toArray();
                    $barcode_data = array_column($barcode_info, null, 'barcode');
                }
                //日志
                $record_log_add = [];
                $record_log_update = [];
                //2.3 定位详情, 并修改
                $db = $this->getDI()->get('db_oa');
                $db->begin();
                //记录金额日志
                $main_amount_log = $amount_excel_log = [];
                $main_amount_log['operation_type'] = MaterialEnums::MATERIAL_MAIN_AMOUNT_LOG_OPERATION_EXPORT;
                $main_amount_log['leave_assets_id'] = $this_leave_asset->id;
                $main_amount_log['before_all_deduct_amount'] = $this_leave_asset->all_deduct_amount;
                foreach ($one_asset_data['detail'] as $line => $info) {
                    //定位数据
                    if ($info['is_add'] == 'Y') {
                        //查询barcode数据
                        if (!isset($barcode_data[$info['barcode']])) {
                            $result_data[$line][self::$result_column] = self::$t['excel_result_barcode_not_exist'];
                            throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                        }
                        //新增的不用定位
                        $insert_data = [
                            'leave_assets_id' => $this_leave_asset->id,
                            'use' => MaterialEnums::USE_PERSONAL, //添加进来的固定个人使用
                            'barcode' => $info['barcode'],
                            'asset_code' => $info['asset_code'],
                            'sn_code' => $info['sn_code'],
                            'asset_name_zh' => $barcode_data[$info['barcode']]['name_zh'] ?? '',
                            'asset_name_en' => $barcode_data[$info['barcode']]['name_en'] ?? '',
                            'asset_name_local' => $barcode_data[$info['barcode']]['name_local'] ?? '',
                            'asset_status' => 0,
                            'purchase_price' => 0,
                            'net_value' => 0,
                            'asset_handling_status' => !empty($info['asset_handling_status']) ? $info['asset_handling_status'] : 0,
                            'deduct_amount' => !empty($info['deduct_amount']) ? $info['deduct_amount'] : 0,
                            'deduct_reason' => !empty($info['deduct_reason']) ? $info['deduct_reason'] : '',
                            'data_tag' => MaterialEnums::LEAVE_ASSET_DATA_TAG_ASSET_DEPARTMENT,
                            'manager_tag' => MaterialEnums::MANAGER_TAG_NO,
                            'asset_department_finish' => !empty($info['asset_handling_status']) ? MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES : MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO,
                            'created_at' => $now_date,
                        ];
                        $success = $db->insertAsDict(
                            $leave_asset_detail_model->getSource(), $insert_data
                        );
                        if (!$success) {
                            $result_data[$line][self::$result_column] = self::$t['excel_result_leave_asset_detail_insert_error'];
                            $this->logger->warning('leave-asset-batch-upload-save-detail-failed: main_id=' . $this_leave_asset->id . 'data=' . json_encode($insert_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是=' . get_data_object_error_msg($success));
                            throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                        }
                        $add_id = $db->lastInsertId();
                        $insert_data['id'] = $add_id;
                        //损坏资产总数,未归还资产总数,总扣费金额,资产总数量
                        $amount_excel_log[] = LeaveAssetsService::getInstance()->updateNumByAdd($this_leave_asset, $insert_data, MaterialEnums::MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_EXPORT_ADD);
                        $record_log_add[] = array_merge(['id' => $add_id, 'record_sub_type' => MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_ADD], $insert_data);
                    } else {
                        //定位详情数据
                        $find_asset_code = self::$empty_asset_code;
                        $find_sn_code = self::$empty_sn_code;
                        if (!empty($info['asset_code'])) {
                            $find_asset_code = $info['asset_code'];
                        }
                        if (!empty($info['sn_code'])) {
                            $find_sn_code = $info['sn_code'];
                        }
                        //如果一个barcode只有一条数据, 那不输入asset_code和sn_code也行
                        if (!isset($db_asset_detail_new[$info['barcode']][$find_asset_code][$find_sn_code])) {
                            //找不到
                            $result_data[$line][self::$result_column] = self::$t['excel_result_not_found_leave_asset_detail_by_code'];
                            throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                        } elseif (count($db_asset_detail_new[$info['barcode']][$find_asset_code][$find_sn_code]) > 1) {
                            //找到不只一条
                            $result_data[$line][self::$result_column] = self::$t['excel_result_to_much_leave_asset_detail_by_code'];
                            throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                        } else {
                            //找到唯一一条 有效
                            $this_result_data = reset($db_asset_detail_new[$info['barcode']][$find_asset_code][$find_sn_code]);
                            $this_detail_id = $this_result_data['id'];
                        }
                        //修改详情表
                        $update_data = [
                            'deduct_amount' => !empty($info['deduct_amount']) ? $info['deduct_amount'] : 0,
                            'deduct_reason' => !empty($info['deduct_reason']) ? $info['deduct_reason'] : '',
                        ];
                        if (!empty($info['asset_handling_status'])) {
                            $update_data['asset_handling_status'] = $info['asset_handling_status'];
                            $update_data['asset_department_finish'] = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;

                        }
                        $update_success = $db->updateAsDict(
                            $leave_asset_detail_model->getSource(),
                            $update_data,
                            [
                                'conditions' => 'id = ?',
                                'bind' => $this_detail_id
                            ]
                        );
                        if (!$update_success) {
                            $result_data[$line][self::$result_column] = self::$t['excel_result_leave_asset_detail_save_error'];
                            $this->logger->warning('leave-asset-batch-upload-save-detail-failed: detail_id=' . $this_detail_id . 'data=' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是=' . get_data_object_error_msg($update_success));
                            throw new ValidationException(self::$t['excel_result_one_line_error'], ErrCode::$VALIDATE_ERROR);
                        }
                        //损坏资产总数,未归还资产总数,总扣费金额
                        $amount_excel_log[] = LeaveAssetsService::getInstance()->updateNumBySave($this_leave_asset, $this_result_data, $update_data, MaterialEnums::MATERIAL_DETAIL_AMOUNT_LOG_OPERATION_EXPORT_SAVE);
                        //记录日志
                        $tmp_log['record_sub_type'] = MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT;
                        $tmp_log['id'] = $this_detail_id;
                        $tmp_log['barcode'] = $info['barcode'];
                        $tmp_log['deduct_amount'] = $update_data['deduct_amount'];
                        $tmp_log['deduct_reason'] = $update_data['deduct_reason'];
                        if (isset($update_data['asset_handling_status'])) {
                            $tmp_log['asset_handling_status'] = $update_data['asset_handling_status'];
                        }
                        $record_log_update[] = $tmp_log;
                    }
                }
                //编辑操作, 状态填写的已处理, 所有行必须是编辑完成状态, 此校验需要包含本次表格中编辑的内容, 所以放到后边修改完后校验
                if ($validate_main_data['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT && $validate_main_data['asset_department_status'] == MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE) {
                    //验证所有行都处理过
                    $not_finish_exist = MaterialLeaveAssetsDetailModel::findFirst([
                        'conditions' => 'leave_assets_id = :leave_assets_id: AND is_deleted = :is_deleted: AND asset_department_finish = :asset_department_finish:',
                        'bind' => ['leave_assets_id' => $this_leave_asset->id, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'asset_department_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO]
                    ]);
                    if ($not_finish_exist) {
                        throw new ValidationException(self::$t['excel_result_detail_not_finish'], ErrCode::$VALIDATE_ERROR);
                    }
                }

                //2.4 保存主表数据
                //更新的损坏资产总数,未归还资产总数,总扣费金额在updateNumBySave和updateNumByAdd里维护过了,
                //金额 (损失金额是页面上手填的,导入时没这项,代码不维护,同时是否足额扣款也不维护)
                $this_leave_asset->deduct_amount = !empty($one_asset_data['main_deduct_amount']) ? $one_asset_data['main_deduct_amount'] : 0;
                $this_leave_asset->return_amount = !empty($one_asset_data['main_return_amount']) ? $one_asset_data['main_return_amount'] : 0;
                $this_leave_asset->asset_department_remark = !empty($one_asset_data['main_asset_department_remark']) ? $one_asset_data['main_asset_department_remark'] : '';
                $this_leave_asset->updated_at = $now_date;
                $this_leave_asset->updated_id = $user['id'];
                if ($validate_main_data['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT && !empty($validate_main_data['asset_department_status'])) {
                    $this_leave_asset->asset_department_status = $validate_main_data['asset_department_status'];
                    LeaveAssetsService::getInstance()->syncToHcm($this_leave_asset->asset_department_status, $this_leave_asset->staff_info_id, $user['id']);
                }
                //资产处理进度(单据进度)
                LeaveAssetsService::getInstance()->setAssetDepartmentProgress($this_leave_asset);

                //资产部在主管处理之前，在OA端进行了处理，则需要把待处理变成“已处理”，后台标注为：资产部处理，显示：资产部（工号），挪到已处理tab页
                //资产部处理状态=（处理中、已处理），可修改上级处理状态=（待处理）&& 处理进度=（待处理、处理中），修改上级处理状态=（已处理），处理进度=（资产部已处理，标记上资产部工号加姓名）
                if (in_array($this_leave_asset->asset_department_status, [MaterialEnums::ASSET_DEPARTMENT_STATUS_DOING, MaterialEnums::ASSET_DEPARTMENT_STATUS_DONE]) && $this_leave_asset->manager_status == MaterialEnums::MANAGER_STATUS_TODO && in_array($this_leave_asset->manager_progress, [MaterialEnums::MANAGER_PROGRESS_NOT, MaterialEnums::MANAGER_PROGRESS_DOING])) {
                    $this_leave_asset->manager_status = MaterialEnums::MANAGER_STATUS_DEAL;//已处理
                    $this_leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_NO_NEED_ASSET;//上级处理进度-资产部处理(资产部（工号）)
                    $this_leave_asset->manager_updated_id = $user['id'];//主管数据更新人id
                    $this_leave_asset->manager_updated_name = $user['name'];//主管数据更新人名称
                    $this_leave_asset->manager_updated_at = $now_date;//主管数据更新时间
                }
                $main_amount_log['after_all_deduct_amount'] = $this_leave_asset->all_deduct_amount;
                if (!$this_leave_asset->save()) {
                    $this->logger->warning('leave-asset-batch-upload-save-main-failed: data=' . json_encode($this_leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是=' . get_data_object_error_msg($this_leave_asset));
                    throw new ValidationException(self::$t['excel_result_leave_asset_save_error'], ErrCode::$VALIDATE_ERROR);
                }
                //记录金额日志
                $batch_number = LeaveAssetsService::getInstance()->addAmountLog($main_amount_log, $amount_excel_log, $user['id'], $batch_number ?? '');
                //记录日志
                $record_log_update[] = [
                    'record_sub_type' => MaterialEnums::LEAVE_ASSETS_RECORD_SUB_TYPE_EDIT,
                    'main_fields_deduct_amount' => $this_leave_asset->deduct_amount,
                    'main_fields_return_amount' => $this_leave_asset->return_amount,
                    'main_fields_asset_department_status' => $this_leave_asset->asset_department_status,
                    'main_fields_asset_department_remark' => $this_leave_asset->asset_department_remark,
                ];
                //增加操作记录
                if ($one_asset_data['operation_type'] == MaterialEnums::BATCH_UPLOAD_OPERATION_TYPE_EDIT) {
                    $record_type = MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_EDIT;
                } else {
                    $record_type = MaterialEnums::LEAVE_ASSETS_RECORD_TYPE_UPDATE;
                }
                $all_record_log = array_merge($record_log_add, $record_log_update);
                if (!empty($all_record_log)) {
                    LeaveAssetsService::getInstance()->addRecordLog($this_leave_asset->id, $operator, $record_type, $all_record_log, 0);
                }
                $db->commit();
            } catch (ValidationException $e) {
                $message = 'validation: ' . $e->getMessage();
            } catch (BusinessException $e) {
                $message = static::$t->_('retry_later') . ' message=' . $e->getCode();
                $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            } catch (\Exception $e) {
                $message = static::$t->_('retry_later') . ': message=' . $e->getCode();
                $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
            }
            if (!empty($real_message)) {
                $this->logger->warning('leave-asset-batch-upload-failed:' . $real_message);
            }
            if (!empty($message)) {
                if (isset($db) && !empty($db)) {
                    $db->rollback();
                }
                //失败
                foreach ($one_asset_data['detail'] as $line => $info) {
                    //没设置的补充上原因, 设置了的不再覆盖(有的情况是一条错误导致其他失败,其他行的错误信息就用翻译:excel_result_one_line_error)
                    if (empty($result_data[$line][self::$result_column])) {
                        $result_data[$line][self::$result_column] = $message;
                    }
                    $failed_num += 1;
                }
            } else {
                //成功
                foreach ($one_asset_data['detail'] as $line => $info) {
                    $result_data[$line][self::$result_column] = 'success';
                    $success_num += 1;
                }
            }
        }
        return [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => [
                'excel_data' => $result_data,
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_sum' => $failed_num
            ]
        ];
    }

    /**
     * EXCEL数据转为关联数组(导入新增)
     * @param $excel_data
     * @return array
     * $new_data = [
     *      [
     *          'staff_info_id' => 1,
     *          'main_deduct_amount' => 100,
     *          ...
     *          'detail' => [
     *                  [
     *                      'barcode' => '',
     *                      'asset_code' => ''
     *                  ]
     *          ]
     *      ]
     * ]
     * @date 2022/7/18
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'staff_info_id',//工号
            'staff_name',//姓名
            'operation_type',//操作类型
            'asset_department_status',//资产处理状态
            'is_add',//是否新增资产
            'barcode',//barcode
            'asset_code',//资产编码
            'sn_code',//sn码
            'asset_handling_status',//资产处理情况
            'deduct_amount',//扣费金额
            'deduct_reason',//扣费原因
            'main_deduct_amount',//已扣费金额
            'main_return_amount',//已还金额
            'main_asset_department_remark',//资产部操作备注
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                $info[$index] = trim($info[$index]);
                if ($key == 'is_add') {
                    $info[$index] = strtoupper($info[$index]);
                }
                if (in_array($key, ['asset_department_status', 'asset_handling_status', 'operation_type'])) {
                    //例 1-未还
                    $end = stripos($info[$index], '-');
                    if ($end) {
                        $info[$index] = substr($info[$index], 0, $end);
                    } else {
                        $info[$index] = '';
                    }
                }
                //转string,不然会变成科学计数法
                if ($key == 'deduct_amount' || $key == 'main_deduct_amount' || $key == 'main_return_amount') {
                    $info[$index] = (string)$info[$index];
                }
                //扣费原因截取100
                if ($key == 'deduct_reason') {
                    $info[$index] = mb_strlen($info[$index]) > 100 ? mb_substr($info[$index], 0, 100, 'UTF-8') : $info[$index];
                }
                //备注自动截取200
                if ($key == 'main_asset_department_remark') {
                    $info[$index] = mb_strlen($info[$index]) > 200 ? mb_substr($info[$index], 0, 200, 'UTF-8') : $info[$index];
                }
                $data[$line][$key] = $info[$index];
            }
        }
        //格式转换成每个员工下数据
        $new_data = [];
        foreach ($data as $line => $value) {
            if (!isset($new_data[$value['staff_info_id']])) {
                //主表数据
                $new_data[$value['staff_info_id']]['staff_info_id'] = $value['staff_info_id'];
                $new_data[$value['staff_info_id']]['operation_type'] = $value['operation_type'];
                $new_data[$value['staff_info_id']]['main_deduct_amount'] = $value['main_deduct_amount'];
                $new_data[$value['staff_info_id']]['main_return_amount'] = $value['main_return_amount'];
                $new_data[$value['staff_info_id']]['main_asset_department_remark'] = $value['main_asset_department_remark'];
                $new_data[$value['staff_info_id']]['barcode_arr'] = [];
            }
            //详情信息
            $new_data[$value['staff_info_id']]['detail'][$line] = $value;
            //barcode
            if ($value['is_add'] == 'Y') {
                $new_data[$value['staff_info_id']]['barcode_arr'][] = $value['barcode'];
            }
        }
        return $new_data;
    }

    /**
     * 获取导出总记录数
     * @param array $params 导出请求参数组
     * @param array $user_info 登陆者
     * @return int
     */
    public function getExcelCount($params, $user_info)
    {
        $count = 0;
        switch ($params['export_type']) {
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_COUNT:
                //导出汇总表
                $count = LeaveAssetsService::getInstance()->getListCount($params, $user_info);
                break;
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_DETAIL:
                //导出明细表
                $count = LeaveAssetsExcelService::getInstance()->getExportCount($params, $user_info);
                break;
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER:
                //导出明细表(在途资产)
                $count = LeaveAssetsExcelService::getInstance()->getExportTransferCount($params, $user_info);
                break;
        }
        return $count;
    }

    /**
     * 获取导出表头
     * @param int $export_type 导出类型
     * @return array
     */
    public function getExcelHeader($export_type)
    {
        $header = [];
        switch ($export_type) {
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_COUNT: //导出汇总表表头
                $header = [
                    static::$t->_('material_leave_asset_export.asset_department_status'),//资产处理状态
                    static::$t->_('material_leave_asset_export.staff_id'),//工号
                    static::$t->_('material_leave_asset_export.staff_name'),//姓名
                    static::$t->_('material_leave_asset_export.job_name'),//职位
                    static::$t->_('material_leave_asset_export.hire_type_text'),//雇佣类型
                    static::$t->_('material_leave_asset_export.staff_state'),//在职状态
                    static::$t->_('material_leave_asset_export.last_work_date'),//最后工作日
                    static::$t->_('material_leave_asset_export.wait_leave_state'),//待离职/离职日期
                    static::$t->_('material_leave_asset_export.leave_type'),//离职类型
                    static::$t->_('material_leave_asset_export.leave_reason'),//离职原因
                    static::$t->_('material_leave_asset_export.node_department_name'),//部门
                    static::$t->_('material_leave_asset_export.sys_store_id'),//网点code
                    static::$t->_('material_leave_asset_export.sys_store_name'),//网点名称
                    static::$t->_('material_leave_asset_export.working_country'),//工作所在国家
                    static::$t->_('material_leave_asset_export.company_name'),//所属公司
                    static::$t->_('material_leave_asset_export.asset_department_remark'),//操作备注
                    static::$t->_('material_leave_asset_export.all_asset_number'),//资产总数
                    static::$t->_('material_leave_asset_export.damage_assets_number'),//损坏资产总数
                    static::$t->_('material_leave_asset_export.unreturned_assets_number'),//未还资产总数
                    static::$t->_('material_leave_asset_export.all_deduct_amount'),//总扣费金额
                    static::$t->_('material_leave_asset_export.deduct_amount'),//已扣金额
                    static::$t->_('material_leave_asset_export.return_amount'),//已还金额
                    static::$t->_('material_leave_asset_export.loss_amount'),//损失金额
                    static::$t->_('material_leave_asset_export.created_at'),//创建日期
                ];
                break;
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_DETAIL: //导出明细表表头
                $header = [
                    static::$t->_('material_leave_asset_export.staff_id'),//工号
                    static::$t->_('material_leave_asset_export.staff_name'),//姓名
                    static::$t->_('material_leave_asset_export.job_name'),//职位
                    static::$t->_('material_leave_asset_export.hire_type_text'),//雇佣类型
                    static::$t->_('material_leave_asset_export.staff_state'),//在职状态
                    static::$t->_('material_leave_asset_export.last_work_date'),//最后工作日
                    static::$t->_('material_leave_asset_export.wait_leave_state'),//待离职/离职日期
                    static::$t->_('material_leave_asset_export.node_department_name'),//部门
                    static::$t->_('material_leave_asset_export.sys_store_id'),//网点code
                    static::$t->_('material_leave_asset_export.sys_store_name'),//网点名称
                    static::$t->_('material_leave_asset_export.working_country'),//工作所在国家
                    static::$t->_('material_leave_asset_export.company_name'),//所属公司
                    static::$t->_('material_leave_asset_export.all_deduct_amount'),//总扣费金额
                    static::$t->_('material_leave_asset_export.deduct_amount'),//已扣金额
                    static::$t->_('material_leave_asset_export.return_amount'),//已还金额
                    static::$t->_('material_leave_asset_export.loss_amount'),//损失金额
                    static::$t->_('material_leave_asset_export.asset_department_remark'),//备注
                    static::$t->_('material_leave_asset_export.barcode'),//barcode
                    static::$t->_('material_leave_asset_export.asset_code'),//资产码
                    static::$t->_('material_leave_asset_export.sn_code'),//sn码
                    static::$t->_('material_leave_asset_export.asset_name'),//资产名称
                    static::$t->_('material_leave_asset_export.model'),//规格型号
                    static::$t->_('material_leave_asset_export.use'),//使用方向
                    static::$t->_('material_leave_asset_export.purchase_price'),//采购价
                    static::$t->_('material_leave_asset_export.net_value'),//净值
                    static::$t->_('material_leave_asset_export.asset_status'),//使用状态
                    static::$t->_('material_leave_asset_export.asset_state'),//主管处理状况
                    static::$t->_('material_leave_asset_export.asset_handling_status'),//资产处理情况
                    static::$t->_('material_leave_asset_export.deduct_reason'),//扣费原因
                    static::$t->_('material_leave_asset_export.images'),//图片
                ];
                break;
            case MaterialEnums::LEAVE_ASSETS_EXPORT_TYPE_TRANSFER: //导出明细表(在途资产)表头
                $header = [
                    static::$t->_('material_leave_asset_export.staff_id'),//工号
                    static::$t->_('material_leave_asset_export.staff_name'),//姓名
                    static::$t->_('material_leave_asset_export.job_name'),//职位
                    static::$t->_('material_leave_asset_export.hire_type_text'),//雇佣类型
                    static::$t->_('material_leave_asset_export.staff_state'),//在职状态
                    static::$t->_('material_leave_asset_export.last_work_date'),//最后工作日
                    static::$t->_('material_leave_asset_export.wait_leave_state'),//待离职/离职日期
                    static::$t->_('material_leave_asset_export.node_department_name'),//部门
                    static::$t->_('material_leave_asset_export.sys_store_id'),//网点code
                    static::$t->_('material_leave_asset_export.sys_store_name'),//网点名称
                    static::$t->_('material_leave_asset_export.working_country'),//工作所在国家
                    static::$t->_('material_leave_asset_export.company_name'),//所属公司
                    static::$t->_('material_leave_asset_export.barcode'),//barcode
                    static::$t->_('material_leave_asset_export.asset_code'),//资产码
                    static::$t->_('material_leave_asset_export.sn_code'),//sn码
                    static::$t->_('material_leave_asset_export.asset_name'),//资产名称
                    static::$t->_('material_leave_asset_export.model'),//规格型号
                    static::$t->_('material_leave_asset_export.use'),//使用方向
                    static::$t->_('material_leave_asset_export.purchase_price'),//采购价
                    static::$t->_('material_leave_asset_export.net_value'),//净值
                    static::$t->_('material_leave_asset_export.transfer_status'),//接收状态
                    static::$t->_('material_leave_asset_export.transfer_date'),//转移/接收/拒收日期
                    static::$t->_('material_leave_asset_export.transfer_type'),//转移类型
                    static::$t->_('material_leave_asset_export.aor_no'),//出库单号
                    static::$t->_('material_leave_asset_export.transfer_reason'),//转移拒收原因
                ];
                break;
        }
        return $header;
    }
}
