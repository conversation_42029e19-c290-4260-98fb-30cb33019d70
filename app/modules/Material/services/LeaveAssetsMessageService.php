<?php

namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\Validation;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ShortUrlService;
use App\Modules\Common\Services\SmsService;
use App\Modules\Common\Services\StaffLangService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialAssetsRepository;

class LeaveAssetsMessageService extends BaseService
{
    //列表-非必填
    public static $validate_message_content = [
        'leave_assets_id' => 'Required|IntGt:0',
    ];
    //by离职资产消息-发送给待处理人 翻译key
    public static $leave_assets_title = 'leave_assets_msg_title';
    //by离职资产消息-发送给待处理人(马来/泰国) 翻译key
    public static $leave_assets_title_malaysia = 'leave_assets_msg_title_malaysia';

    //oa创建离职资产-邮件标题-发送给离职员工
    public static $leave_assets_email_to_staff_title = 'leave_assets_email_to_staff_title';
    //oa创建离职资产-邮件标题-发送给离职员工(马来)
    public static $leave_assets_email_to_staff_title_malaysia = 'leave_assets_email_to_staff_title_malaysia';
    //oa创建离职资产-邮件标题-发送给离职员工(个人代理)
    public static $leave_assets_email_to_staff_title_personal_agent = 'leave_assets_email_to_staff_title_personal_agent';


    //oa创建离职资产-短信和邮件内容-发送给离职员工
    public static $leave_assets_sms_email_to_staff_content = 'leave_assets_sms_email_to_staff_content';
    //oa创建离职资产-邮件内容-发送给离职员工(马来)
    public static $leave_assets_sms_email_to_staff_content_malaysia = 'leave_assets_sms_email_to_staff_content_malaysia';
    //lnt 邮件内容 马来
    public static $leave_assets_sms_email_to_staff_content_malaysia_lnt = 'leave_assets_sms_email_to_staff_content_malaysia_lnt';
    //oa创建离职资产-短信内容-发送给离职员工(马来)
    public static $leave_assets_sms_to_staff_content_malaysia = 'leave_assets_sms_to_staff_content_malaysia';
    //lnt 员工 马来
    public static $leave_assets_sms_to_staff_content_malaysia_lnt = 'leave_assets_sms_to_staff_content_malaysia_lnt';
    //oa创建离职资产-短信和邮件内容-发送给离职员工(个人代理)
    public static $leave_assets_sms_email_to_staff_content_personal_agent = 'leave_assets_sms_email_to_staff_content_personal_agent';

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LeaveAssetsMessageService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 给主管发送待处理离职资产消息
     * @param $manager_id
     * @param $leave_assets_id
     * @date 2023/3/21
     * @return bool
     */
    public function sendLeaveAssetsMessage($manager_id, $leave_assets_id)
    {
        $message_info = [];
        //1.获取用户上次登录的语言环境
        $country = get_country_code();
        $default_language = $country == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';
        $staff_language = StaffLangService::getInstance()->getLatestMobileLang($manager_id, $default_language);
        //中英泰翻译
        $translation = $this->getTranslation($staff_language);
        //2.标题
        if (in_array($country, [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE])) {
            $title_key = self::$leave_assets_title_malaysia;
        } else {
            $title_key = self::$leave_assets_title;
        }
        $title_text = $translation->_($title_key);
        $message_info['message_title'] = $title_text;
        //3.分类
        $message_info['category'] = MaterialEnums::LEAVE_ASSET_MESSAGE_CATEGORY;
        //4.内容
        $by_url = env('by_url_prefix', 'https://dev-01-th-backyard-ui.fex.pub/');
        $content_url = $by_url . '#/newLeavingAssetsNews?leave_assets_id=' . $leave_assets_id;
        $content = "<meta name='viewport' content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' /><iframe src='{$content_url}' width='100%' height='85%'></iframe>";
        $message_info['message_content'] = $content;
        $message_info['staff_info_id'] = $manager_id;
        return $this->sendMessage($message_info);
    }

    /**
     * 发送by站内信
     * @param $message_info
     * @date 2022/10/28
     * @return bool
     */
    public function sendMessage($message_info)
    {
        $this->logger->info('leave_asset_send_message_input:' . json_encode(['message' => $message_info, 'date' => date('Y-m-d H:i:s')], JSON_UNESCAPED_UNICODE));
        try {
            $kit_param = [];
            $kit_param['staff_users'] = [['id' => $message_info['staff_info_id']]];
            $kit_param['message_title'] = $message_info['message_title'];
            $kit_param['message_content'] = $message_info['message_content'];
            $kit_param['category'] = $message_info['category'];

            $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
            $bi_rpc->setParams([$kit_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                return true;
            }
            $this->logger->warning('leave_asset_send_message_api_return_info' . json_encode($res, JSON_UNESCAPED_UNICODE));
        } catch (\Exception $e) {
            $this->logger->warning('leave_asset_send_message_api_error' . $e->getMessage() . PHP_EOL);
        }
        return false;
    }

    /**
     * by站内信-获取离职资产站内信内容
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/18
     */
    public function getLeaveMessageContent($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_message_content);
            //查询离职资产表
            $main_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['leave_assets_id'],
                ]
            ]);
            if (!$main_info) {
                throw new ValidationException(static::$t->_('leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //或者这个离职资产下员工自有的离职资产
            $leave_detail = MaterialLeaveAssetsDetailModel::find([
                'columns' => 'id, count(id) as all_number, use, barcode, asset_name_zh, asset_name_en, asset_name_local',
                'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted: and data_tag = :data_tag:',
                'bind' => [
                    'leave_assets_id' => $main_info->id,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'data_tag' => MaterialEnums::LEAVE_ASSET_DATA_TAG_SELF,
                ],
                'group' => 'use, barcode'
            ])->toArray();
            $data['staff_name'] = $main_info->staff_name;
            $data['staff_id'] = $main_info->staff_info_id;
            $data['job_title_name'] = $main_info->job_name;
            $data['last_work_date'] = $main_info->last_work_date;
            $data['leave_date'] = !empty($main_info->leave_date) ? date('Y-m-d', strtotime($main_info->leave_date)) : '';
            $data['assets_data'] = [
                'personal_assets' => [],
                'public_assets' => [],
            ];
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
            foreach ($leave_detail as $v) {
                $asset_name = !empty($v[$name_key]) ? $v[$name_key] : '';
                if ($v['use'] == MaterialEnums::USE_PERSONAL) {
                    $data['assets_data']['personal_assets'][] = $asset_name . ' × ' . $v['all_number'];
                } elseif ($v['use'] == MaterialEnums::USE_PUBLIC) {
                    $data['assets_data']['public_assets'][] = $asset_name . ' × ' . $v['all_number'];
                }
            }
            //返回员工的雇佣类型
            $staff_info = (new HrStaffRepository())->getStaffById($main_info->staff_info_id);
            $data['hire_type'] = $staff_info['hire_type'] ?? 0;

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leave-asset-message-content-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产发送短信
     * @param $staff_info_id
     * @param $staff_name
     * @param $mobile
     * @param $all_asset_amount
     * @param array $staff_info_arr 离职员工信息组
     * @return bool
     * @date 2023/3/30
     */
    public function sendPhoneSms($staff_info_id, $staff_name, $mobile, $all_asset_amount, $staff_info_arr)
    {
        if (empty($mobile)) {
            //没手机号不发
            return false;
        }
        //检测是否可以给离职员工发送短信或者邮件提醒
        $can_send = $this->checkUserCanSendSmsOrEmail($staff_info_arr);
        if ($can_send === false) {
            return false;
        }
        //获取用户上次登录的语言环境
        $country = get_country_code();
        $default_language = $country == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';
        $staff_language = StaffLangService::getInstance()->getLatestMobileLang($staff_info_id, $default_language);
        //当前国家默认币种
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
        $currency_symbol = $default_currency['symbol'];
        //获取翻译后的内容
        //若不是中英泰,就用英
        $staff_language = strtolower($staff_language);
        if ($staff_language == 'zh-cn') {
            $staff_language = 'zh';
        }
        if (!in_array($staff_language, ['zh', 'th', 'en'])) {
            $staff_language = 'en';
        }
        $translation = $this->getTranslation($staff_language);
        //$content = "您好" . $staff_name . " ，请于离职前归还总价值" . $all_asset_amount . "THB的资产，以免影响您的薪酬 " .  $this->getShortUrl(env("h5_url") . "zh/assets/assets_list?staff_id=" . $staff_info_id . "&lang=zh");
        //env配置h5_url 后边必须带'/'
        $url = env('h5_url') . $staff_language . '/assets/assets_list?staff_id=' . $staff_info_id . '&lang=' . $staff_language . '&is_new_asset=1&hire_type=' . $staff_info_arr['hire_type'];
        $url = ShortUrlService::getInstance()->getShortUrl($url);
        if ($country == GlobalEnums::MY_COUNTRY_CODE) {
            $content_text = $translation->_(self::$leave_assets_sms_to_staff_content_malaysia, ['staff_info_id' => $staff_info_id, 'all_asset_amount' => $all_asset_amount]);
            //如果是 lnt 公司员工 新增翻译key
            $isLnt = HrStaffRepository::isLntCompanyByInfo($staff_info_arr);
            if($isLnt){
                $content_text = $translation->_(self::$leave_assets_sms_to_staff_content_malaysia_lnt, ['staff_name' => $staff_name]);
            }
        } else {
            //个人代理单独走翻译，其他还走之前的
            $leave_assets_sms_email_to_staff_content = ($staff_info_arr['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) ? self::$leave_assets_sms_email_to_staff_content_personal_agent : self::$leave_assets_sms_email_to_staff_content;
            $content_text = $translation->_($leave_assets_sms_email_to_staff_content, ['staff_name' => $staff_name, 'all_asset_amount' => $all_asset_amount . $currency_symbol, 'url' => $url]);
        }
        //拼接参数
        $sms_params = [];
        $sms_params['nation'] = MaterialEnums::$leave_assets_country_nation_map[$country] ?? '';
        $sms_params['mobile'] = $mobile;
        $this->logger->info('leave_asset_send_sms , mobile=' . $mobile);
        // 如果是测试/tra环境，则仅给指定国家的指定手机号发一次即可，其他的默认为发送成功
        if (in_array(get_runtime_env(), ['dev', 'training'])) {
            $sms_params['mobile'] = EnvModel::getEnvByCode('leave_asset_test_phone');
            if (empty($sms_params['mobile'])) {
                return false;
            }
        }
        $sms_params['msg'] = $content_text;
        //发送 viber 信息
        if ($country == GlobalEnums::PH_COUNTRY_CODE) {
            $sms_params['type']             = 0;
            $sms_params['service_provider'] = 9;// 服务商 Viber 固定值
            $sms_params['nation']           = GlobalEnums::PH_COUNTRY_CODE;
        }
        $src = 'oa_leave_asset_remind';
        //发送短信
        $send_result = SmsService::getInstance()->send($sms_params, $src);
        $log_data = [
            'staff_info_id' => $staff_info_id,
            'staff_name' => $staff_name,
            'sms_params' => $sms_params,
            'all_asset_amount' => $all_asset_amount,
        ];
        if (!$send_result) {
            $this->logger->warning('leave_asset_send_sms_error 离职资产-发送短信提醒失败, data=' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
        }
        $this->logger->info('leave_asset_send_sms_success 离职资产-发送短信提醒成功, data=' . json_encode($log_data, JSON_UNESCAPED_UNICODE));
        return true;
    }

    /**
     * 离职资产发送邮件
     * @param $staff_info_id
     * @param $staff_name
     * @param $email
     * @param $all_asset_amount
     * @param array $staff_info_arr 离职员工信息组
     * @date 2023/3/30
     * @return bool
     */
    public function sendEmail($staff_info_id, $staff_name, $email, $all_asset_amount, $staff_info_arr)
    {
        //没邮箱地址不发
        if (empty($email)) {
            return false;
        }
        //检测是否可以给离职员工发送短信或者邮件提醒
        $can_send = $this->checkUserCanSendSmsOrEmail($staff_info_arr);
        if ($can_send === false) {
            return false;
        }
        //获取用户上次登录的语言环境
        $country = get_country_code();
        $default_language = $country == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';
        $staff_language = StaffLangService::getInstance()->getLatestMobileLang($staff_info_id, $default_language);
        //当前国家默认币种
        $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();
        $currency_symbol = $default_currency['symbol'];
        //获取翻译后的内容
        //若不是中英泰,就用英
        $staff_language = strtolower($staff_language);
        if ($staff_language == 'zh-cn') {
            $staff_language = 'zh';
        }
        if (!in_array($staff_language, ['zh', 'th', 'en'])) {
            $staff_language = 'en';
        }
        $translation = $this->getTranslation($staff_language);
        //如果是 lnt 公司员工 加个参数
        $isLnt = HrStaffRepository::isLntCompanyByInfo($staff_info_arr);
        $url = env('h5_url') . $staff_language . '/assets/assets_list?staff_id=' . $staff_info_id . '&staff_name=' . $staff_name . '&lang=' . $staff_language . '&is_new_asset=1&is_lnt=' . (int)$isLnt;
        $url = ShortUrlService::getInstance()->getShortUrl($url);
        $email_url = "<a href='{$url}' target='_blank'>{$url}</a>";

        //标题:离职资产归还提醒/内容
        if ($country == GlobalEnums::MY_COUNTRY_CODE) {
            $title = $translation->_(self::$leave_assets_email_to_staff_title_malaysia);
            $content_text = $translation->_(self::$leave_assets_sms_email_to_staff_content_malaysia, ['staff_name' => $staff_name, 'all_asset_amount' => $all_asset_amount, 'url' => $email_url]);
            //如果是 lnt 公司员工 新增翻译key
            if($isLnt){
                $content_text = $translation->_(self::$leave_assets_sms_email_to_staff_content_malaysia_lnt, ['staff_name' => $staff_name, 'url' => $email_url]);
            }
        } else {
            $leave_assets_email_to_staff_title = self::$leave_assets_email_to_staff_title;
            $leave_assets_sms_email_to_staff_content = self::$leave_assets_sms_email_to_staff_content;

            //个人代理单独走翻译，其他还走之前的
            if ($staff_info_arr['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
                $leave_assets_email_to_staff_title = self::$leave_assets_email_to_staff_title_personal_agent;
                $leave_assets_sms_email_to_staff_content = self::$leave_assets_sms_email_to_staff_content_personal_agent;
            }

            $title = $translation->_($leave_assets_email_to_staff_title);
            $content_text = $translation->_($leave_assets_sms_email_to_staff_content, ['staff_name' => $staff_name, 'all_asset_amount' => $all_asset_amount . $currency_symbol, 'url' => $email_url]);
        }

        //html内容
        $html = <<<EOF
    hi,<br/>
    <span style='margin-left: 16px'></span>{$content_text}<br/>
EOF;
        $emails = [$email];
        $this->logger->info('leave_asset_send_email , email=' . json_encode($emails, JSON_UNESCAPED_UNICODE));
        // 如果是测试/tra环境，则仅给指定国家的指定手机号发一次即可，其他的默认为发送成功
        if (in_array(get_runtime_env(), ['dev', 'training'])) {
            $emails_setting = EnvModel::getEnvByCode('leave_asset_test_email');
            if (empty($emails_setting)) {
                return false;
            }
            $emails = [$emails_setting];
        }
        // 邮件发送
        $log = ['emails' => $emails, 'title' => $title, 'html' => $html];
        if($isLnt){
            $send_res = $this->lnt_mailer->send($emails, $title, $html);
        }else{
            $send_res = $this->mailer->sendAsync($emails, $title, $html);
        }
        if ($send_res) {
            $this->logger->info('leave_asset_send_email_success 发送成功！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            return true;
        } else {
            $this->logger->warning('leave_asset_send_email_error 发送失败！' . json_encode($log, JSON_UNESCAPED_UNICODE));
            return false;
        }
    }

    /**
     * 检测是否可以给离职员工发送短信或者邮件提醒
     * @param array $staff_info_arr 离职员工信息组
     * @return bool
     */
    public function checkUserCanSendSmsOrEmail($staff_info_arr)
    {
        //先判断员工离职时，在资产台账是否有资产（即存在使用人=离职员工，使用状态为：离职资产-需固化到离职员工名下资产特定资产状态配置里），如果没有资产，则不给其发短信/邮件。
        $leave_asset_status_set = EnumsService::getInstance()->getSettingEnvValueIds('material_leave_asset_status');
        $owner_asset_list = MaterialAssetsRepository::getInstance()->searchAsset([
            'status' => $leave_asset_status_set,
            'staff_id' => $staff_info_arr['staff_info_id'],
            'is_staff_empty' => 1,//员工不为空
        ]);
        if (empty($owner_asset_list)) {
            return false;
        }

        //再判断其雇佣类型（hire_type=13）是否为个人代理，如果为个人代理，判断“给个人代理发归还资产的提醒”是否开启，如果开启，则给其发短信或者邮件。如果没有开启，则不给其发短信或者邮件。
        if ($staff_info_arr['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
            $personal_agent_status = EnumsService::getInstance()->getSettingEnvValue('material_leave_asset_personal_agent_remind_status', 0);
            return $personal_agent_status ? true : false;
        }
        return true;
    }
}
