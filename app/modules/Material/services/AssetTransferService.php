<?php

namespace App\Modules\Material\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\HrStaffContractEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffContractModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MaterialAssetTransferLogBarcodeSummaryModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialAssetsRepository;
use App\Repository\StoreRepository;

class AssetTransferService extends BaseService
{
    private static $instance;
    public static $transfer_remind_key_prefix = 'transfer_remind';

    /**
     * 单例
     * @return AssetTransferService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 我的资产-批量转移
     * 非必需的参数
     * @var array
     */
    public static $transfer_not_must_params = [
        'to_node_department_id',
        'to_node_department_name',
        'to_sys_store_id',
        'to_store_name',
        'to_company_id',
        'to_company_name',
        'to_use_land',
        'express_no',
    ];

    /**
     * BY端-我的资产-批量转移
     * 参数验证
     * @var array
     */
    public static $transfer_by_validate = [
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,50',//资产台账id集合-BY端最多50个
        'asset_ids[*]' => 'IntGt:0',//资产台账id集合
        'to_staff_id' => 'Required|IntGt:0',//接收人工号(新使用人)
        'transfer_method' => 'Required|IntIn:' . MaterialEnums::TRANSFER_METHOD_VALIDATE,//转交方式
        'transfer_remark' => 'Required|StrLenGeLe:1,200',//转移原因
        'express_no' => 'IfIntEq:transfer_method,' . MaterialEnums::TRANSFER_METHOD_POST . '|Required|StrLenGeLe:0,50', //快递单号, 如果转交方式为"邮寄",必填
        'to_sys_store_id' => 'Required|StrLenGeLe:0,10', //新使用网点id
        'to_store_name' => 'Required|StrLenGeLe:0,50', //新使用网点名称
        'to_use_land' => 'StrLenGeLe:0,100', //新使用地址
    ];
    /**
     * 我的资产-批量转移
     * 参数验证
     * @var array
     */
    public static $transfer_validate = [
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,500',//资产台账id集合
        'asset_ids[*]' => 'IntGt:0',//资产台账id集合
        'to_staff_id' => 'Required|IntGt:0',//接收人工号(新使用人)
        'transfer_method' => 'Required|IntIn:' . MaterialEnums::TRANSFER_METHOD_VALIDATE,//转交方式
        'transfer_remark' => 'Required|StrLenGeLe:1,200',//转移原因
        'to_node_department_id' => 'Required|IntGt:0', //新使用部门id
        'to_node_department_name' => 'Required|StrLenGeLe:0,50', //新使用部门名称
        'to_sys_store_id' => 'Required|StrLenGeLe:0,10', //新使用网点id
        'to_store_name' => 'Required|StrLenGeLe:0,50', //新使用网点名称
        'to_company_id' => 'IntGt:0', //新使用公司id
        'to_company_name' => 'StrLenGeLe:0,50', //新使用公司名称
        'to_use_land' => 'StrLenGeLe:0,100', //新使用地址
        'express_no' => 'IfIntEq:transfer_method,' . MaterialEnums::TRANSFER_METHOD_POST . '|Required|StrLenGeLe:0,50', //快递单号, 如果转交方式为"邮寄",必填
    ];

    /**
     * 我转出-批量撤销
     * 参数验证
     * @var array
     */
    public static $cancel_validate = [
        'transfer_ids' => 'Required|Arr|ArrLenGeLe:1,500',//转移记录id集合
        'transfer_ids[*]' => 'IntGt:0',//转移记录id集合
        'withdraw_remark' => 'Required|StrLenGeLe:5,200',//转移原因
    ];

    /**
     * by端-我转出-批量撤销
     * 参数验证
     * @var array
     */
    public static $cancel_by_validate = [
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,50',//转移记录id集合
        'transfer_ids[*]' => 'IntGt:0',//转移记录id集合
        'withdraw_remark' => 'Required|StrLenGeLe:5,200',//转移原因
    ];

    /**
     * 我转出-批量提醒
     * 参数验证
     * @var array
     */
    public static $remind_validate = [
        'transfer_ids' => 'Required|Arr|ArrLenGeLe:1,500',//转移记录id集合
        'transfer_ids[*]' => 'IntGt:0',//转移记录id集合
    ];

    /**
     * 待接收-批量拒绝
     * 参数验证
     * @var array
     */
    public static $reject_validate = [
        'transfer_ids' => 'Required|Arr|ArrLenGeLe:1,500',//转移记录id集合
        'transfer_ids[*]' => 'IntGt:0',//转移记录id集合
        'reject_remark' => 'Required|StrLenGeLe:5,200',//拒绝原因
    ];

    /**
     * 待接收-批量接收
     * 参数验证
     * @var array
     */
    public static $reception_validate = [
        'transfer_ids' => 'Required|Arr|ArrLenGeLe:1,500',//转移记录id集合
        'transfer_ids[*]' => 'IntGt:0',//转移记录id集合
    ];

    /**
     * by批量转移-把by请求转成和pc端统一参数
     * @param $params
     * @param $user
     * @date 2022/11/20
     * @return array
     */
    public function conversionByTransfer($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$transfer_by_validate);
            $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $params['to_staff_id'], 'limit' => 1]);
            if (empty($staff_list['data'])) {
                throw new ValidationException(static::$t->_('asset_transfer_by_transfer_to_staff_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $staff_info = current($staff_list['data']);
            //新使用部门
            $params['to_node_department_id'] = $staff_info['node_department_id'];
            $params['to_node_department_name'] = $staff_info['node_department_name'];
            //coo/ceo下的公司
            $company_list = (new PurchaseService())->getCooCostCompany();
            $cost_company_kv = array_column($company_list, 'cost_company_name', 'cost_company_id');
            //如果员工公司是coo/ceo下的,就能用,否则就是空
            if (key_exists($staff_info['company_id'], $cost_company_kv)) {
                $params['to_company_id'] = $staff_info['company_id'];
                $params['to_company_name'] = $staff_info['company_name'];
            }
            //调用批量转移
            $transfer_result = $this->assetTransfer($params, $user);
            $code = $transfer_result['code'];
            $message = $transfer_result['message'];
            $data = $transfer_result['data'];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('conversion-by-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 检测个人代理是否可以转移/出库
     * @param array $from_user_info 转出人信息组
     * @param array $to_user_info 接收人信息组
     * @param array $transfer_barcode 转移列表的barcode组
     * @param integer $from 转移来源1转移，2出库
     * @return bool
     * @throws ValidationException
     */
    public function checkPersonalAgentCanTransfer($from_user_info, $to_user_info, $transfer_barcode, $from = 1)
    {
        //个人代理是否开启雇佣类型只能申请固定barcode开关
        $is_open = $this->getPersonalAgentStatus();
        //开启
        if ($is_open) {
            //接收人工号
            $to_user_id = $to_user_info['staff_info_id'] ?? $to_user_info['staff_id'];
            //转出人的雇佣类型是个人代理
            if ($from == 1 && $from_user_info) {
                //若传递过来的转出人没有雇佣类型，需要查询一下雇佣类型
                if (!isset($from_user_info['hire_type'])) {
                    $from_user_info = (new HrStaffRepository())->getStaffById($from_user_info['id']);
                }
                //接收人只能是网点负责人或系统配置的接收人[资产部的导入转移原使用人工号可能是0所以再判断一次转出人信息]
                if ($from_user_info && $from_user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
                    //获取转出人的所属网点负责人（从组织架构树上取这个网点的网点负责人）
                    $store_info = (new StoreRepository())->getStoreDetail($from_user_info['sys_store_id']);
                    $sys_store_manager_id = $store_info['manager_id'] ?? 0;
                    //接收人工号非网点负责人
                    if ($to_user_id != $sys_store_manager_id) {
                        //接收人工号非系统里配置的特定工号，则不可转移
                        $receive_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_TRANSFER_STAFF_KEY);
                        if (!in_array($to_user_id, $receive_staff_ids)) {
                            throw new ValidationException(static::$t->_('material_asset_transfer_permission_error', ['staff_id' => implode(',', $receive_staff_ids)]), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                }
            }

            //接收人工号的雇佣类型是个人代理
            if ($to_user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
                //转移资产列表里的barcode非系统配置的barcode，则不可转移
                $base_barcode = EnumsService::getInstance()->getSettingEnvValueIds(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_BARCODE_KEY);
                foreach ($transfer_barcode as $barcode) {
                    if (!in_array($barcode, $base_barcode)) {
                        throw new ValidationException(static::$t->_('material_asset_transfer_barcode_error', ['barcode' => implode(',', $base_barcode)]), ErrCode::$VALIDATE_ERROR);
                    }
                }

                //资产台账里存在使用人=该个人代理、barcode=本次转移的barcode、资产状态=使用中的资产，则不可转移
                $has_asset_list = MaterialAssetsRepository::getInstance()->searchAsset(['staff_id' => $to_user_id, 'status' => MaterialEnums::ASSET_STATUS_USING, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, 'bar_code' => $transfer_barcode]);
                if ($has_asset_list) {
                    throw new ValidationException(static::$t->_('material_asset_transfer_barcode_under_staff', ['barcode' => implode(',', array_unique(array_column($has_asset_list, 'bar_code')))]), ErrCode::$VALIDATE_ERROR);
                }

                //转移资产表里存在接收人=该个人代理的、barcode=本次转移的barcode、状态为“待接收”，则不可转移
                $has_transfer_list = MaterialAssetTransferLogModel::find([
                    'columns' => 'barcode',
                    'conditions' => 'to_staff_id = :to_staff_id: and barcode in ({barcode:array}) and status = :status: and is_deleted = :is_deleted:',
                    'bind' => [
                        'to_staff_id' => $to_user_id,
                        'barcode' => $transfer_barcode,
                        'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED
                    ]
                ])->toArray();
                if ($has_transfer_list) {
                    throw new ValidationException(static::$t->_('material_asset_transfer_barcode_under_staff', ['barcode' => implode(',', array_column($has_transfer_list, 'barcode'))]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        return true;
    }

    /**
     * 我的资产-转移/批量转移
     * @param array $params 参数
     * @param $user
     * @return array
     */
    public function assetTransfer($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //如果是单个的, 转一下
            if (is_numeric($params['asset_ids'])) {
                $params['asset_ids'] = [$params['asset_ids']];
            }
            // 参数验证
            $params = self::handleParams($params, self::$transfer_not_must_params);
            Validation::validate($params, self::$transfer_validate);
            // 1. 接收人必须是在职的,非子账号,编制
            $user_service = new UserService();
            $to_staff_info = $user_service->getUserByIdInRbi($params['to_staff_id']);
            if (!$to_staff_info || $to_staff_info->state != StaffInfoEnums::STAFF_STATE_IN) {
                throw new ValidationException(static::$t->_('asset_transfer_to_staff_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if (!($to_staff_info->state == StaffInfoEnums::STAFF_STATE_IN
                && $to_staff_info->formal == StaffInfoEnums::FORMAL_IN
                && $to_staff_info->is_sub_staff == StaffInfoEnums::IS_SUB_STAFF_NO)) {
                throw new ValidationException(static::$t->_('asset_transfer_to_staff_not_illegal'), ErrCode::$VALIDATE_ERROR);
            }
            //19311 限制互转的员工雇佣类型
            $asset_transfer_staff_hire_type_limit = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_transfer_staff_hire_type');
            if (in_array($to_staff_info->hire_type, $asset_transfer_staff_hire_type_limit)) {
                throw new ValidationException(static::$t->_('material_asset_transfer_staff_hire_type_limit'), ErrCode::$VALIDATE_ERROR);
            }

            // 2. 验证资产信息
            $asset_ids = array_values(array_unique($params['asset_ids']));
            $material_assets_model = new MaterialAssetsModel();
            $asset_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($asset_list) || count($asset_list) != count($asset_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $barcode_set = [];
            $asset_ids = [];
            $asset_codes = [];
            foreach ($asset_list as $asset) {
                // 所有资产status必须等于3:使用中、15闲置（在网点）
                if (!in_array($asset['status'], [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                    throw new ValidationException(static::$t->_('asset_transfer_status_error') . ':' . $asset['asset_code'], ErrCode::$VALIDATE_ERROR);
                }
                // 所有资产使用人必须是当前登录人
                if ($asset['staff_id'] != $user['id']) {
                    throw new ValidationException(static::$t->_('asset_transfer_staff_not_self') . ':' . $asset['asset_code'], ErrCode::$VALIDATE_ERROR);
                }
                // 接收人不能是自己
                if ($asset['staff_id'] == $params['to_staff_id']) {
                    throw new ValidationException(static::$t->_('asset_receive_staff_no_self'), ErrCode::$VALIDATE_ERROR);
                }
                // 获取所有barcode
                $barcode_set[$asset['bar_code']] = $asset['bar_code'];
                $asset_ids[$asset['status']][$asset['id']] = $asset['id'];
                //获取所有资产编码
                $asset_codes[$asset['status']][$asset['asset_code']] = $asset['asset_code'];
            }
            // 查询是barcode是否限制互转
            $barcode_set = array_values($barcode_set);
            //删除的不限制转移 , 禁用的还按barcode设置的去限制
            //这里只查询未删除且限制转移的 从而实现-删除的不限制,禁用的依然根据设置限制
            $barcode_data = MaterialSauModel::find([
                'conditions' => 'barcode in ({barcode:array}) and transfer_forbid = :transfer_forbid: and is_deleted = :is_deleted:',
                'bind' => ['barcode' => $barcode_set, 'transfer_forbid' => MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if ($barcode_data) {
                $forbid_yes_barcode = array_column($barcode_data, 'barcode');
                throw new ValidationException(static::$t->_('asset_transfer_transfer_forbid_error') . ':' . implode(',', $forbid_yes_barcode), ErrCode::$VALIDATE_ERROR);
            }
            //检测个人代理是否可以转移
            $this->checkPersonalAgentCanTransfer($user, $to_staff_info->toArray(), $barcode_set);
            // 所有验证通过, 开始数据变更
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_time = date('Y-m-d H:i:s');
            foreach ($asset_ids as $asset_status => $item) {
                // 修改资产台账状态
                $asset_ids_str = implode(',', $item);
                $before_status_using = $asset_status;
                $after_asset_update = [
                    'status' => MaterialEnums::ASSET_STATUS_ALLOT,
                    'updated_at' => $now_time
                ];
                $asset_update_bool = $db->updateAsDict(
                    $material_assets_model->getSource(),
                    $after_asset_update,
                    [
                        'conditions' => " id IN ({$asset_ids_str}) AND status = {$before_status_using}",
                    ]
                );
                $asset_affected_rows = $db->affectedRows();
                if (!$asset_update_bool || $asset_affected_rows != count($item)) {
                    throw new BusinessException('批量转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_ASSETS_UPDATE_ERROR);
                }

                //记录操作日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $before_data = [
                    'status' => $asset_status
                ];
                $after_data = [
                    'status' => MaterialEnums::ASSET_STATUS_ALLOT
                ];
                $log_bool = $update_log_model->dealEditFieldBatch(array_values($asset_codes[$asset_status]), $before_data, $after_data, $user);
                if ($log_bool === false) {
                    throw new BusinessException('我的资产转移-操作记录失败 = ' . json_encode(['asset_codes' => array_values($asset_codes[$asset_status]), 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR);
                }
            }

            // 插入批量转移表
            $transfer_batch = new MaterialAssetTransferBatchModel();
            $transfer_batch_data = [
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                'mark' => $params['transfer_remark'],
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_batch->i_create($transfer_batch_data);
            if ($bool === false) {
                throw new BusinessException('我的资产-批量转移-记录转移头信息失败, 数据: ' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_batch), ErrCode::$ASSET_TRANSFER_ACCOUNT_BATCH_ADD_ERROR);

            }
            // pc_code获取和资产部互转一致,资产部互转调用接口payment/store_renting/getPcCode
            if ($params['to_sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_HEAD_OFFICE;
                $get_pc_id = $params['to_node_department_id'];
            } else {
                $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_SYS_STORE;
                $get_pc_id = $params['to_sys_store_id'];
            }
            $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($get_pc_id, $get_pc_type);
            $to_pc_code = $pc_code_data['data']['pc_code'] ?? '';
            //转移 固化协议签署公司 lnt
            $fromStaff = empty($asset_list) ? [] : array_column($asset_list, 'staff_id');
            $toStaff   = [$params['to_staff_id']];
            $allStaff  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
            //查询员工协议公司
            $staffData = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id, contract_company_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $allStaff],
            ])->toArray();
            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
            //获取公司名称
            $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

            // 插入转移记录表
            $transfer_log = new MaterialAssetTransferLogModel();
            $transfer_log_data = [];
            //资产批量变更提醒-资产汇总记录表
            $batch_transfer_log_barcode_summary_data = [];
            foreach ($asset_list as $one_asset) {
                //V19094需求，新使用人（即接收人）的所属网点=总部时，若资产上的使用网点非空，仍为原先网点，若传递了使用地则用传递，未传递若原资产使用地非空&&资产上的使用网点非空则不变化；其他情况均为新使用人所属网点名称
                if ($params['to_sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                    if ($one_asset['sys_store_id']) {
                        $params['to_sys_store_id'] = $one_asset['sys_store_id'];
                        $params['to_store_name'] = $one_asset['store_name'];
                        $params['to_use_land'] = !empty($params['to_use_land']) ? $params['to_use_land'] : ($one_asset['use_land'] ?? '');
                    }
                }
                $transfer_log_data[] = [
                    'batch_id' => $transfer_batch->id,
                    'asset_id' => $one_asset['id'],
                    'barcode' => $one_asset['bar_code'],
                    'asset_code' => $one_asset['asset_code'],
                    'from_staff_id' => $one_asset['staff_id'],
                    'from_staff_name' => $one_asset['staff_name'],
                    'from_node_department_id' => $one_asset['node_department_id'],
                    'from_node_department_name' => $one_asset['node_department_name'],
                    'from_sys_store_id' => $one_asset['sys_store_id'],
                    'from_store_name' => $one_asset['store_name'],
                    'from_company_id' => $one_asset['company_id'],
                    'from_company_name' => $one_asset['company_name'],
                    'from_contract_company_id' => $staffData[$one_asset['staff_id']] ?? 0,
                    'from_contract_company_name' => $companyList[$staffData[$one_asset['staff_id']] ?? 0] ?? '',
                    'from_pc_code' => $one_asset['pc_code'],
                    'from_use_land' => $one_asset['use_land'],
                    'to_staff_id' => $params['to_staff_id'],
                    'to_staff_name' => !empty($to_staff_info->name) ? $to_staff_info->name : '',
                    'to_node_department_id' => $params['to_node_department_id'],
                    'to_node_department_name' => $params['to_node_department_name'],
                    'to_sys_store_id' => $params['to_sys_store_id'],
                    'to_store_name' => $params['to_store_name'],
                    'to_company_id' => !empty($params['to_company_id']) ? $params['to_company_id'] : $one_asset['company_id'],
                    'to_company_name' => !empty($params['to_company_name']) ? $params['to_company_name'] : $one_asset['company_name'],
                    'to_contract_company_id' => $staffData[$params['to_staff_id']] ?? 0,
                    'to_contract_company_name' => $companyList[$staffData[$params['to_staff_id']] ?? 0] ?? '',
                    'to_pc_code' => $to_pc_code,
                    'to_use_land' => $params['to_use_land'] ?? $params['to_store_name'],
                    'transfer_remark' => $params['transfer_remark'],
                    'transfer_operator_id' => $user['id'],
                    'transfer_operator_name' => $user['name'],
                    'transfer_method' => $params['transfer_method'],
                    'express_no' => $params['express_no'] ?? '',
                    'transfer_type' => MaterialEnums::TRANSFER_TYPE_USER,
                    'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                    'from_use_status' => $one_asset['status'],
                    'transfer_at' => $now_time,
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];

                //组装资产批量变更提醒-资产汇总记录表
                if (isset($batch_transfer_log_barcode_summary_data[$one_asset['bar_code']])) {
                    $batch_transfer_log_barcode_summary_data[$one_asset['bar_code']]['num'] += 1;
                } else {
                    $batch_transfer_log_barcode_summary_data[$one_asset['bar_code']] = [
                        'batch_id' => $transfer_batch->id,
                        'barcode' => $one_asset['bar_code'],
                        'staff_info_id' => $params['to_staff_id'],
                        'num' => 1,
                        'transfer_type' => MaterialEnums::TRANSFER_TYPE_USER,
                        'type' => MaterialEnums::OPERATE_TYPE_USER_TRANSFER,
                        'transfer_remark' => $params['transfer_remark'],
                        'created_at' => $now_time
                    ];
                }
            }
            $bool = $transfer_log->batch_insert($transfer_log_data);
            if ($bool === false) {
                throw new BusinessException('资产台账批量转移-记录转移行信息失败, 数据: ' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_log), ErrCode::$ASSET_TRANSFER_ACCOUNT_LOG_ADD_ERROR);
            }
            //组装发送站内信员工组 && 记录资产汇总记录
            $batch_transfer_log_barcode_summary = new MaterialAssetTransferLogBarcodeSummaryModel();
            $bool = $batch_transfer_log_barcode_summary->batch_insert($batch_transfer_log_barcode_summary_data);
            if ($bool === false) {
                throw new BusinessException('资产转移-添加转移-记录资产批量变更提醒-资产汇总记录表失败 = ' . json_encode($batch_transfer_log_barcode_summary_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($batch_transfer_log_barcode_summary), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
            }
            $db->commit();
            //发送站内信
            AssetTransferMessageService::getInstance()->sendPendingReceiptReminder([
                'staff_info_id' => $params['to_staff_id'],
                'message_content' => $transfer_batch->id,
                'category' => MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER
            ]);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-batch-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by批量撤销-把by请求转成和pc端统一参数
     * 把传来的资产id查出关联的转移id (by端是从我的资产列表撤销传的是资产id,pc端是从待接收列表撤销传的是转移id)
     * @param $params
     * @param $user
     * @date 2022/11/20
     * @return array
     */
    public function conversionByCancel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$cancel_by_validate);
            //根据资产id查询对应的待接收转移数据的id,用这些id去撤销
            $asset_ids = array_values(array_unique($params['asset_ids']));
            $material_assets_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_assets_model::find([
                'columns' => 'id',
                'conditions' => 'asset_id in ({asset_ids:array}) and is_deleted = :is_deleted: and status = :status:',
                'bind' => ['asset_ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED]
            ])->toArray();
            if (empty($transfer_list) || count($transfer_list) != count($asset_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_cancel_asset_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $params['transfer_ids'] = array_column($transfer_list, 'id');
            unset($params['asset_ids']);
            //调用批量转移
            $transfer_result = $this->assetCancel($params, $user);
            $code = $transfer_result['code'];
            $message = $transfer_result['message'];
            $data = $transfer_result['data'];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('conversion-by-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我转出-撤销/批量撤销
     * @param array $params 参数
     * @param $user
     * @param bool $asset_operate 资产部操作,可以撤回别人的
     * @return array
     */
    public function assetCancel($params, $user, bool $asset_operate = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            // 参数验证
            Validation::validate($params, self::$cancel_validate);
            // 1. 验证资产信息
            $transfer_ids = array_values(array_unique($params['transfer_ids']));
            $material_assets_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $transfer_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($transfer_list) || count($transfer_list) != count($transfer_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_cancel_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $asset_ids = $status_asset_ids = $asset_codes = [];
            foreach ($transfer_list as $transfer_log) {
                // 所有资产转移状态status必须等于:待接收
                if ($transfer_log['status'] != MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    throw new ValidationException(static::$t->_('asset_cancel_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                // 所有资产转出人必须是当前登录人
                if ($asset_operate === false && $transfer_log['from_staff_id'] != $user['id']) {
                    throw new ValidationException(static::$t->_('cancel_transfer_staff_not_self') . ':' . $transfer_log['id'], ErrCode::$VALIDATE_ERROR);
                }
                $asset_ids[$transfer_log['asset_id']] = $transfer_log['asset_id'];
                $status_asset_ids[$transfer_log['from_use_status']][] = $transfer_log['asset_id'];
                //获取所有资产编码
                $asset_codes[$transfer_log['from_use_status']][]  = $transfer_log['asset_code'];
            }
            $asset_ids = array_values($asset_ids);
            // 2. 验证资产信息
            $material_assets_model = new MaterialAssetsModel();
            $asset_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($asset_list) || count($asset_list) != count($asset_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_cancel_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            foreach ($asset_list as $asset) {
                // 所有资产status必须等于4:调拨中
                if ($asset['status'] != MaterialEnums::ASSET_STATUS_ALLOT) {
                    throw new ValidationException(static::$t->_('asset_transfer_status_cancel_error') . ':' . $asset['asset_code'], ErrCode::$VALIDATE_ERROR);
                }
            }

            // 所有验证通过, 开始数据变更
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_time = date('Y-m-d H:i:s');

            foreach ($status_asset_ids as $from_use_status => $item) {
                //会出现同一个资产会被多条转移记录同时撤回的情况这里兼容下
                $item = array_unique($item);
                // 修改资产台账状态
                $asset_ids_str = implode(',', $item);
                $before_status_allot = MaterialEnums::ASSET_STATUS_ALLOT;
                $asset_update_bool = $db->updateAsDict(
                    $material_assets_model->getSource(),
                    [
                        'status' => $from_use_status,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => " id IN ({$asset_ids_str}) AND status = {$before_status_allot}",
                    ]
                );
                $asset_affected_rows = $db->affectedRows();
                if (!$asset_update_bool || $asset_affected_rows != count($item)) {
                    throw new BusinessException('撤回转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($item, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ASSETS_UPDATE_ERROR);
                }
                // 记录日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $before_data = [
                    'status' => MaterialEnums::ASSET_STATUS_ALLOT
                ];
                $after_data = [
                    'status' => $from_use_status
                ];
                $log_bool = $update_log_model->dealEditFieldBatch(array_values($asset_codes[$from_use_status]), $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_CANCEL_TRANSFER);
                if ($log_bool === false) {
                    throw new BusinessException('我转出-撤回-操作记录失败 = ' . json_encode(['asset_codes' => array_values($asset_codes[$from_use_status]), 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR);
                }
            }

            //批量修改转移表
            $transfer_log_model = new MaterialAssetTransferLogModel();
            $transfer_log_ids_str = implode(',', $transfer_ids);
            $status_unreceived = MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED;
            $transfer_log_update = [
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_CANCEL,
                'finished_at' => $now_time,
                'remark' => $params['withdraw_remark'],
                'operator_id' => $user['id'],
                'updated_at' => $now_time
            ];
            $transfer_log_update_bool = $db->updateAsDict(
                $transfer_log_model->getSource(),
                $transfer_log_update,
                [
                    'conditions' => " id IN ({$transfer_log_ids_str}) AND status = {$status_unreceived}",
                ]
            );
            $transfer_affected_rows = $db->affectedRows();
            if (!$transfer_log_update_bool || $transfer_affected_rows != count($transfer_ids)) {
                throw new BusinessException('撤回转移-修改转移记录失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($transfer_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ACCOUNT_LOG_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-cancel-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 我转出-批量提醒
     * @param array $params 参数
     * @param $user
     * @param bool $is_search 是否业务数据查询
     * @return array
     */
    public function transferRemind($params, $user, $is_search = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            // 参数验证
            Validation::validate($params, self::$remind_validate);
            // 1. 验证资产信息
            $transfer_ids = array_values(array_unique($params['transfer_ids']));
            $material_assets_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $transfer_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($transfer_list) || count($transfer_list) != count($transfer_ids)) {
                throw new ValidationException(static::$t->_('asset_remind_transfer_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $redis = $this->getDI()->get('redis');
            $redis_key = self::$transfer_remind_key_prefix . date('Ymd');
            //构造每个收件人的待接收转移id
            $user_number = [];
            //循环验证每一条转移数据
            foreach ($transfer_list as $transfer_log) {
                // 所有资产转移状态status必须等于:待接收
                if ($transfer_log['status'] != MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    throw new ValidationException(static::$t->_('asset_remind_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                //集合中存在此转移id,说明今天提醒过,不能继续提醒
                if ($redis->sismember($redis_key, $transfer_log['id'])) {
                    $this->logger->info('transfer-remind-repeat: id=' . $transfer_log['id']);
                    throw new ValidationException(static::$t->_('asset_remind_id_repeat_error'), ErrCode::$VALIDATE_ERROR);
                }
                //构造用户数量
                if (isset($user_number[$transfer_log['to_staff_id']])) {
                    $user_number[$transfer_log['to_staff_id']] += 1;
                } else {
                    $user_number[$transfer_log['to_staff_id']] = 1;
                }
            }
            //构造每个收件人的待接收数量, 同数量的用户放到一起 $array = ['10'=>[staff1,staff2]]
            $send_email_user_number = [];
            foreach ($user_number as $k_staff_id => $v_number) {
                $send_email_user_number[$v_number][] = $k_staff_id;
            }
            //存储本次转移id , 如果不存在需要设置过期时间
            if ($redis->exists($redis_key) == 0) {
                $is_add = $redis->sadd($redis_key, ...$transfer_ids);//10000个值毫秒级完成,内存占用10M左右
                $expire_time = 60 * 60 * 24; // 24小时过期
                $is_expire = $redis->expire($redis_key, $expire_time);
                if (!$is_expire) {
                    throw new BusinessException('asset-remind-redis-expire-error,key=' . $redis_key, ErrCode::$ASSET_TRANSFER_REMIND_REDIS_EXPIRE_ERROR);
                }
            } else {
                $is_add = $redis->sadd($redis_key, ...$transfer_ids);//10000个值毫秒级完成,内存占用10M左右
            }
            if (!$is_add) {
                throw new BusinessException('asset-remind-redis-sadd-error,key=' . $redis_key . ',transfer_ids=' . json_encode($transfer_ids), ErrCode::$ASSET_TRANSFER_REMIND_REDIS_SADD_ERROR);
            }
            $send_user = $user['id'] . '(' . $user['name'] . ')';
            //发送邮件
            AssetTransferMessageService::getInstance()->sendEmail($send_user, $send_email_user_number);
            //发送站内信
            $message_category = MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND1;
            if ($is_search) {
                $message_category = MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_REMIND2;
            }
            AssetTransferMessageService::getInstance()->sendRemindMessage($user_number, $message_category, $user);
            //发送push
            $push_users = array_keys($user_number);
            AssetTransferMessageService::getInstance()->sendPush($push_users, AssetTransferMessageService::$push_type_remind);

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('transfer-remind-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 待接收-拒绝/批量拒绝
     * @param array $params 参数
     * @param $user
     * @return array
     */
    public function assetReject($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            // 参数验证
            Validation::validate($params, self::$reject_validate);
            // 1. 验证资产信息
            $transfer_ids = array_values(array_unique($params['transfer_ids']));
            $material_assets_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $transfer_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($transfer_list) || count($transfer_list) != count($transfer_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_reject_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            //构建数据,区分需要变更为 闲置,使用中,出库拒绝的三种资产
            //员工互转被拒绝，由调度中变更为使用中，闲置（在网点）
            //出库转移被拒绝，由出库中变更为出库拒绝
            //资产转移被拒绝，由调度中变更为（闲置 使用人是0的或使用中）
            $asset_ids = []; //本次转移的所有资产id
            $asset_ids_asset_department = []; //资产部转移按照原使用状态分组资产id
            $asset_ids_user = []; //员工互转按照原使用状态分组资产id
            $asset_ids_storage = []; //出库转移的资产id
            $reject_push_staff = [];
            $reject_push_staff_asset = [];
            foreach ($transfer_list as $transfer_log) {
                // 所有资产转移状态status必须等于:待接收
                if ($transfer_log['status'] != MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    throw new ValidationException(static::$t->_('asset_reject_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                // 所有资产接收人必须是当前登录人
                if ($transfer_log['to_staff_id'] != $user['id']) {
                    throw new ValidationException(static::$t->_('reject_transfer_staff_not_self') . ':' . $transfer_log['id'], ErrCode::$VALIDATE_ERROR);
                }
                switch ($transfer_log['transfer_type']) {
                    case MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT:
                        //资产部转移
                        $asset_ids_asset_department[$transfer_log['from_use_status']][$transfer_log['asset_id']] = $transfer_log['asset_id'];
                        break;
                    case MaterialEnums::TRANSFER_TYPE_USER:
                        //员工互转
                        $asset_ids_user[$transfer_log['from_use_status']][$transfer_log['asset_id']] = $transfer_log['asset_id'];
                        break;
                    case MaterialEnums::TRANSFER_TYPE_OUT_STORAGE:
                        //出库转移
                        $asset_ids_storage[$transfer_log['asset_id']] = $transfer_log['asset_id'];
                        break;
                    default:
                        //没有类型的(历史数据全部是已接收的,新数据转移时都分配了转移类型,这种情况正常不存在)
                        throw new ValidationException(static::$t->_('asset_reject_transfer_type_error'), ErrCode::$VALIDATE_ERROR);
                        break;
                }
                //资产id集合
                $asset_ids[$transfer_log['asset_id']] = $transfer_log['asset_id'];
                //被拒绝的员工集合
                $reject_push_staff[] = $transfer_log['from_staff_id'];
                $reject_push_staff_asset[$transfer_log['from_staff_id']][] = $transfer_log['asset_id'];
            }
            $asset_ids = array_values(array_unique($asset_ids));
            // 2. 验证资产信息
            $material_assets_model = new MaterialAssetsModel();
            $asset_list = $material_assets_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($asset_list) || count($asset_list) != count($asset_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_reject_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $asset_codes_asset_department = [];//资产部转移按照原使用状态分组台账资产码
            $asset_codes_user = [];//员工互转按照原使用状态分组台账资产码
            $asset_codes_storage = [];
            foreach ($asset_list as $asset) {
                // 所有资产status必须等于调拨中 或 出库中 才可以拒绝
                if (!in_array($asset['status'], [MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_OUT_STORAGE])) {
                    throw new ValidationException(static::$t->_('asset_transfer_status_reject_error'), ErrCode::$VALIDATE_ERROR);
                }
                //获取所有资产编码
                if (!empty($asset_ids_user)) {
                    //针对资产部转移的需要将资产编码按照转移前使用状态分组
                    foreach ($asset_ids_user as $from_use_status => $asset_ids) {
                        if (in_array($asset['id'], $asset_ids)) {
                            $asset_codes_user[$from_use_status][$asset['asset_code']] = $asset['asset_code'];
                        }
                    }
                } elseif (in_array($asset['id'], $asset_ids_storage)) {
                    $asset_codes_storage[$asset['asset_code']] = $asset['asset_code'];
                } elseif (!empty($asset_ids_asset_department)) {
                    //针对资产部转移的需要将资产编码按照转移前使用状态分组
                    foreach ($asset_ids_asset_department as $from_use_status => $asset_ids) {
                        if (in_array($asset['id'], $asset_ids)) {
                            $asset_codes_asset_department[$from_use_status][$asset['asset_code']] = $asset['asset_code'];
                        }
                    }
                }
            }
            // 所有验证通过, 开始数据变更
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_time = date('Y-m-d H:i:s');
            // 修改资产台账状态
            //资产部转移,拒绝后改成16849需求，转移前的使用状态
            if (!empty($asset_ids_asset_department)) {
                $before_status = MaterialEnums::ASSET_STATUS_ALLOT;
                foreach ($asset_ids_asset_department as $after_status => $asset_ids) {
                    $asset_ids_str = implode(',', $asset_ids);
                    $asset_update_bool = $db->updateAsDict(
                        $material_assets_model->getSource(),
                        [
                            'status' => $after_status,
                            'updated_at' => $now_time
                        ],
                        [
                            'conditions' => " id IN ({$asset_ids_str}) AND status = {$before_status}",
                        ]
                    );
                    $asset_affected_rows = $db->affectedRows();
                    if (!$asset_update_bool || $asset_affected_rows != count($asset_ids)) {
                        throw new BusinessException('拒绝转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($asset_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ASSETS_UPDATE_ERROR);
                    }

                    //记录资产台账日志
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $before_data = ['status' => $before_status];
                    $after_data = ['status' => $after_status];
                    $log_bool = $update_log_model->dealEditFieldBatch($asset_codes_asset_department[$after_status], $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_REJECT_TRANSFER);
                    if ($log_bool === false) {
                        throw new BusinessException('我转出-拒绝-操作记录失败 = ' . json_encode(['asset_codes' => $asset_codes_asset_department[$after_status], 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR);
                    }
                }
            }
            //员工互转,拒绝后改成20961需求，转移前的使用状态
            if (!empty($asset_ids_user)) {
                $before_status = MaterialEnums::ASSET_STATUS_ALLOT;
                foreach ($asset_ids_user as $after_status => $asset_ids) {
                    $asset_ids_str = implode(',', $asset_ids);
                    $asset_update_bool = $db->updateAsDict(
                        $material_assets_model->getSource(),
                        [
                            'status' => $after_status,
                            'updated_at' => $now_time
                        ],
                        [
                            'conditions' => " id IN ({$asset_ids_str}) AND status = {$before_status}",
                        ]
                    );
                    $asset_affected_rows = $db->affectedRows();
                    if (!$asset_update_bool || $asset_affected_rows != count($asset_ids)) {
                        throw new BusinessException('拒绝转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($asset_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ASSETS_UPDATE_ERROR);
                    }
                    //记录资产台账日志
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $before_data = ['status' => $before_status];
                    $after_data = ['status' => $after_status];
                    $log_bool = $update_log_model->dealEditFieldBatch($asset_codes_user[$after_status], $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_REJECT_TRANSFER);
                    if ($log_bool === false) {
                        throw new BusinessException('我转出-拒绝-操作记录失败 = ' . json_encode(['asset_codes' => $asset_codes_user[$after_status], 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR);
                    }
                }
            }
            //出库转移,拒绝后改成出库拒绝
            if (!empty($asset_ids_storage)) {
                $before_status = MaterialEnums::ASSET_STATUS_OUT_STORAGE;
                $after_status = MaterialEnums::ASSET_STATUS_OUT_STORAGE_REJECT;
                $asset_ids_str = implode(',', $asset_ids_storage);
                $asset_update_bool = $db->updateAsDict(
                    $material_assets_model->getSource(),
                    [
                        'status' => $after_status,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => " id IN ({$asset_ids_str}) AND status = {$before_status}",
                    ]
                );
                $asset_affected_rows = $db->affectedRows();
                if (!$asset_update_bool || $asset_affected_rows != count($asset_ids_storage)) {
                    throw new BusinessException('拒绝转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($asset_ids_storage, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ASSETS_UPDATE_ERROR);
                }
                //记录资产台账日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $before_data = ['status' => $before_status];
                $after_data = ['status' => $after_status];
                $log_bool = $update_log_model->dealEditFieldBatch($asset_codes_storage, $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_REJECT_TRANSFER);
                if ($log_bool === false) {
                    throw new BusinessException('我转出-拒绝-操作记录失败 = ' . json_encode(['asset_codes' => $asset_codes_storage, 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_LOG_ERROR);
                }
            }
            //批量修改转移表
            $transfer_log_model = new MaterialAssetTransferLogModel();
            $transfer_log_ids_str = implode(',', $transfer_ids);
            $status_unreceived = MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED;
            $transfer_log_update = [
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_REJECTED,
                'finished_at' => $now_time,
                'remark' => $params['reject_remark'],
                'operator_id' => $user['id'],
                'updated_at' => $now_time
            ];
            $transfer_log_update_bool = $db->updateAsDict(
                $transfer_log_model->getSource(),
                $transfer_log_update,
                [
                    'conditions' => " id IN ({$transfer_log_ids_str}) AND status = {$status_unreceived}",
                ]
            );
            $transfer_affected_rows = $db->affectedRows();
            if (!$transfer_log_update_bool || $transfer_affected_rows != count($transfer_ids)) {
                throw new BusinessException('拒绝转移-修改转移记录失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($transfer_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_CANCEL_ACCOUNT_LOG_ERROR);
            }
            //发送邮件+push+by站内信
            $message_service = AssetTransferMessageService::getInstance();
            $message_service->sendPush($reject_push_staff, AssetTransferMessageService::$push_type_reject);
            $message_service->sendRejectMessage($reject_push_staff_asset, $params['reject_remark'], $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-reject-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 待接收-接收/批量接收
     * @param array $params 参数
     * @param $user
     * @param bool $is_by 是否by端请求(by端请求需要传签字)
     * @return array
     */
    public function assetReception($params, $user, $is_by = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //参数验证
            $validate_rule = self::$reception_validate;
            if ($is_by) {
                $validate_rule['sign_url'] = 'Required|StrLenGe:1';
            }
            Validation::validate($params, $validate_rule);
            //1. 验证转移信息, 验证资产信息
            $transfer_ids = array_values(array_unique($params['transfer_ids']));
            $material_transfer_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_transfer_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $transfer_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($transfer_list) || count($transfer_list) != count($transfer_ids)) {
                throw new ValidationException(static::$t->_('asset_transfer_reception_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            //原资产信息组
            $origin_assets = [];
            foreach ($transfer_list as $transfer_log) {
                // 所有资产转移状态status必须等于:待接收
                if ($transfer_log['status'] != MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED) {
                    throw new ValidationException(static::$t->_('asset_reception_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                // 所有资产接收人必须是当前登录人
                if ($transfer_log['to_staff_id'] != $user['id']) {
                    throw new ValidationException(static::$t->_('reception_transfer_staff_not_self') . ':' . $transfer_log['id'], ErrCode::$VALIDATE_ERROR);
                }
                //获取资产信息
                $asset = AssetAccountService::getInstance()->getMaterialAssetInfoById($transfer_log['asset_id']);
                //资产使用状态是调拨中,或出库中,才可以接收。
                if (!in_array($asset->status, [MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_OUT_STORAGE])) {
                    throw new ValidationException(self::$t['asset_transfer_status_reception_error'], ErrCode::$VALIDATE_ERROR);
                }
                //转移前后使用人不可一致
                if ($asset->staff_id == $transfer_log['to_staff_id']) {
                    throw new ValidationException(self::$t['material_asset_transfer_staff_error'], ErrCode::$VALIDATE_ERROR);
                }
                $origin_assets[$transfer_log['asset_id']] = $asset;
            }
            //2. pc端验证,必须有签名图片
            if ($is_by) {
                $sign_url = $params['sign_url'];
            } else {
                $sign_url = $this->getSignUrl($user['id']);
                if (empty($sign_url)) {
                    throw new ValidationException(static::$t->_('reception_lack_sign_img'), ErrCode::$VALIDATE_ERROR);
                }
            }

            // 所有验证通过, 开始数据变更
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_time = date('Y-m-d H:i:s');
            //按照转移数据,逐条修改资产台账,并记录日志 , 最大500条转移记录
            foreach ($transfer_list as $transfer_one) {
                $asset = $origin_assets[$transfer_one['asset_id']];
                //操作日志对象
                $log_asset = clone $asset;
                //修改资产台账信息
                $asset_update = [
                    'status' => MaterialEnums::ASSET_STATUS_USING,//使用状态-使用中
                    'state' => !empty($user['state']) ? $user['state'] : 0,//在职状态
                    'wait_leave_state' => !empty($user['wait_leave_state']) ? $user['wait_leave_state'] : 0,//待离职状态
                    'leave_date' => !empty($user['leave_date']) ? $user['leave_date'] : null,//离职日期
                    'job_id' => !empty($user['job_title_id']) ? $user['job_title_id'] : 0,//职位ID
                    'job_name' => !empty($user['job_title']) ? $user['job_title'] : '',//职位名称
                    'staff_id' => $transfer_one['to_staff_id'],//使用人
                    'staff_name' => $user['name'] ?? '',//使用人名称
                    'node_department_id' => $transfer_one['to_node_department_id'],//所属部门ID
                    'node_department_name' => $transfer_one['to_node_department_name'],//所属部门名称
                    'company_id' => $transfer_one['to_company_id'],//公司id
                    'company_name' => $transfer_one['to_company_name'],//公司名称
                    'sys_store_id' => $transfer_one['to_sys_store_id'],//所属网点
                    'store_name' => $transfer_one['to_store_name'],//所属网点名称
                    'pc_code' => $transfer_one['to_pc_code'],//成本中心
                    'use_land' => $transfer_one['to_use_land'],//使用地
                    'aor_no' => $transfer_one['aor_no'],//资产领用出库单号
                    'aor_scm_no' => $transfer_one['aor_scm_no'],//资产领用出库SCM单号
                    'aor_date' => $transfer_one['aor_date'],//资产领用出库时间
                    'receipted_at' => $now_time,//使用人接收时间
                    'updated_at' => $now_time
                ];
                $bool = $asset->i_update($asset_update);
                if ($bool === false) {
                    throw new BusinessException('接收转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($asset) . '; 数据: ' . json_encode(['asset_id' => $asset->id, 'update_data' => $asset_update], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_ERROR);
                }
                //记录操作日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_RECEPTION_TRANSFER, $log_asset, $asset_update, $user);
                if ($log_bool === false) {
                    throw new BusinessException('接收转移-操作记录失败 = ' . $asset->id, ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }
            }
            //批量修改转移表
            $transfer_log_model = new MaterialAssetTransferLogModel();
            $transfer_log_ids_str = implode(',', $transfer_ids);
            $before_status_unreceived = MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED;
            $transfer_log_update = [
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                'finished_at' => $now_time,
                'operator_id' => $user['id'],
                'recipient_sign_url' => $sign_url,
                'updated_at' => $now_time
            ];
            $transfer_log_update_bool = $db->updateAsDict(
                $transfer_log_model->getSource(),
                $transfer_log_update,
                [
                    'conditions' => " id IN ({$transfer_log_ids_str}) AND status = {$before_status_unreceived}",
                ]
            );
            $transfer_affected_rows = $db->affectedRows();
            if (!$transfer_log_update_bool || $transfer_affected_rows != count($transfer_ids)) {
                throw new BusinessException('接收转移-修改转移记录失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($transfer_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_LOG_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-reception-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产部转移和出库转移,自动接收
     * 跟assetReception不同的地方:
     * 1.不用验证资产接收人必须是当前登录人
     * 2.不用验证签名图片
     * 3.查询每个接收人的在职状态和职位等信息
     * 4.不用验证"待接收"状态,数据是前边定时任务查好的
     * @param array $params 参数
     * @return array
     */
    public function autoReception($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            //参数验证
            Validation::validate($params, self::$reception_validate);
            //1. 验证转移信息, 验证资产信息
            $transfer_ids = array_values(array_unique($params['transfer_ids']));
            $material_transfer_model = new MaterialAssetTransferLogModel();
            $transfer_list = $material_transfer_model::find([
                'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $transfer_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            //if (empty($transfer_list) || count($transfer_list) != count($transfer_ids)) {
            //    throw new ValidationException(static::$t->_('asset_transfer_reception_ids_error'), ErrCode::$VALIDATE_ERROR);
            //}
            //原资产信息组
            $origin_assets = [];
            $error_assets = [];
            $to_staff_id_arr = [];
            foreach ($transfer_list as $key => $transfer_log) {
                //获取资产信息
                $asset = AssetAccountService::getInstance()->getMaterialAssetInfoById($transfer_log['asset_id']);
                $transfer_log['asset_code'] = $asset->asset_code ?? '';
                //资产使用状态是调拨中,或出库中,才可以接收。
                if (!in_array($asset->status, [MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_OUT_STORAGE])) {
                    $error_assets[] = $transfer_log;
                    unset($transfer_list[$key]);
                    continue;
                }
                //转移前后使用人不可一致
                if ($asset->staff_id == $transfer_log['to_staff_id']) {
                    $error_assets[] = $transfer_log;
                    unset($transfer_list[$key]);
                    continue;
                }
                $origin_assets[$transfer_log['asset_id']] = $asset;
                $to_staff_id_arr[] = $transfer_log['to_staff_id'];
            }
            $transfer_list = array_values($transfer_list);
            $data['success'] = $transfer_list;
            $data['error'] = $error_assets;
            if (!empty($data['error'])) {
                //此警告只报资产编码, 需要详细数据从info中找
                $this->logger->warning('自动接收资产有问题的资产编码 ' . json_encode(array_column($data['error'], 'asset_code'), JSON_UNESCAPED_UNICODE) . ' , 执行时间:' . date('Y-m-d H:i:s'));
                $this->logger->info('自动接收资产有问题的数据 ' . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . ' , 执行时间:' . date('Y-m-d H:i:s'));
            }
            if (empty($transfer_list)) {
                throw new BusinessException('自动接收资产-全部数据验证失败', ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
            }
            //查所有待接收用户在职状态,职位等
            // 所有验证通过, 开始数据变更
            //资产接收人
            $to_staff_id_arr = array_values(array_unique($to_staff_id_arr)); //外层限制最多一次500个,不会太大
            $to_staff_list = [];
            if (!empty($to_staff_id_arr)){
                $search_staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $to_staff_id_arr, 'limit' => count($to_staff_id_arr)]);
                if (!empty($search_staff_list['data'])) {
                    $to_staff_list = array_column($search_staff_list['data'], null, 'staff_id');
                }
            }
            //自动转移人
            $user = [
                'id' => 10000,
                'name' => '自动任务',
                'nick_name' => '自动任务',
            ];
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_time = date('Y-m-d H:i:s');
            //按照转移数据,逐条修改资产台账,并记录日志 , 最大500条转移记录
            foreach ($transfer_list as $transfer_one) {
                $asset = $origin_assets[$transfer_one['asset_id']];
                //操作日志对象
                $log_asset = clone $asset;
                //修改资产台账信息
                $asset_update = [
                    'status' => MaterialEnums::ASSET_STATUS_USING,//使用状态-使用中
                    'state' => $to_staff_list[$transfer_one['to_staff_id']]['state'] ?? 0,//在职状态
                    'wait_leave_state' => $to_staff_list[$transfer_one['to_staff_id']]['wait_leave_state'] ?? 0,//待离职状态
                    'leave_date' => $to_staff_list[$transfer_one['to_staff_id']]['leave_date'] ?? null,//离职日期
                    'job_id' => $to_staff_list[$transfer_one['to_staff_id']]['job_id'] ?? 0,//职位ID
                    'job_name' => $to_staff_list[$transfer_one['to_staff_id']]['job_name'] ?? '',//职位名称
                    'staff_id' => $transfer_one['to_staff_id'],//使用人
                    'staff_name' => $to_staff_list[$transfer_one['to_staff_id']]['staff_name'] ?? $transfer_one['to_staff_name'],//使用人姓名
                    'node_department_id' => $transfer_one['to_node_department_id'],//所属部门ID
                    'node_department_name' => $transfer_one['to_node_department_name'],//所属部门名称
                    'company_id' => $transfer_one['to_company_id'],//公司id
                    'company_name' => $transfer_one['to_company_name'],//公司名称
                    'sys_store_id' => $transfer_one['to_sys_store_id'],//所属网点
                    'store_name' => $transfer_one['to_store_name'],//所属网点名称
                    'pc_code' => $transfer_one['to_pc_code'],//成本中心
                    'use_land' => $transfer_one['to_use_land'],//使用地
                    'aor_no' => $transfer_one['aor_no'],//资产领用出库单号
                    'aor_scm_no' => $transfer_one['aor_scm_no'],//资产领用出库SCM单号
                    'aor_date' => $transfer_one['aor_date'],//资产领用出库时间
                    'receipted_at' => $now_time,//使用人接收时间
                    'updated_at' => $now_time
                ];
                $bool = $asset->i_update($asset_update);
                if ($bool === false) {
                    throw new BusinessException('接收转移-修改资产台账状态失败, 原因可能是: ' . get_data_object_error_msg($asset) . '; 数据: ' . json_encode(['asset_id' => $asset->id, 'update_data' => $asset_update], JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_ASSET_ERROR);
                }
                //记录操作日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_RECEPTION_TRANSFER, $log_asset, $asset_update, $user);
                if ($log_bool === false) {
                    throw new BusinessException('接收转移-操作记录失败 = ' . $asset->id, ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }
            }
            //批量修改转移表
            $transfer_log_model = new MaterialAssetTransferLogModel();
            $update_transfer_ids = array_column($transfer_list, 'id');
            $transfer_log_ids_str = implode(',', $update_transfer_ids);
            $before_status_unreceived = MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED;
            $transfer_log_update = [
                'auto_recipient' => MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_YES,
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                'finished_at' => $now_time,
                'operator_id' => $user['id'],
                'updated_at' => $now_time
            ];
            $transfer_log_update_bool = $db->updateAsDict(
                $transfer_log_model->getSource(),
                $transfer_log_update,
                [
                    'conditions' => " id IN ({$transfer_log_ids_str}) AND status = {$before_status_unreceived}",
                ]
            );
            $transfer_affected_rows = $db->affectedRows();
            if (!$transfer_log_update_bool || $transfer_affected_rows != count($update_transfer_ids)) {
                throw new BusinessException('接收转移-修改转移记录失败, 原因可能是: ' . get_data_object_error_msg($db) . '; 数据: ' . json_encode($update_transfer_ids, JSON_UNESCAPED_UNICODE), ErrCode::$ASSET_TRANSFER_SAVE_LOG_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-reception-transfer-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取用户签名链接
     * @param $staff_id
     * @param $contract_status
     * @return string
     * @date 2022/10/31
     */
    public function getSignUrl($staff_id, $contract_status = [
        HrStaffContractEnums::CONTRACT_STATUS_CHECK,
        HrStaffContractEnums::CONTRACT_STATUS_ARCHIVE,
        HrStaffContractEnums::CONTRACT_STATUS_ARCHIVED,
        HrStaffContractEnums::CONTRACT_STATUS_RELEASED,
        HrStaffContractEnums::CONTRACT_STATUS_TO_BE_RENEWED,
        HrStaffContractEnums::CONTRACT_STATUS_RENEWED,
        HrStaffContractEnums::CONTRACT_STATUS_EXPIRED
    ])
    {
        //1.第一优先级,读取上次接收时签的字
        $transfer_log_info = MaterialAssetTransferLogModel::findFirst([
            'conditions' => 'to_staff_id = :to_staff_id: 
                and is_deleted = :is_deleted: 
                and status = :status:
                and recipient_sign_url != :empty_value: ',
            'bind' => [
                'to_staff_id' => $staff_id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                'empty_value' => ''
            ],
            'order' => 'finished_at desc',
            'columns' => 'recipient_sign_url',
        ]);
        $sign_url = $transfer_log_info->recipient_sign_url ?? '';
        //2.第二优先级,读取winHr中的资产合同的签名
        if (empty($sign_url)) {
            $staff_contract_info = HrStaffContractModel::findFirst([
                'conditions' => 'staff_id = :staff_id: 
                and contract_is_deleted = :is_deleted: 
                and contract_type = :contract_type:
                and contract_status in ({contract_status:array})
                and contract_signature_img != :empty_value: ',
                'bind' => [
                    'staff_id' => $staff_id,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'contract_type' => get_country_code() == GlobalEnums::MY_COUNTRY_CODE ? HrStaffContractEnums::CONTRACT_ZCXY_MY : HrStaffContractEnums::CONTRACT_ZCXY,
                    'contract_status' => $contract_status,
                    'empty_value' => ''
                ],
                'order' => 'id desc',
                'columns' => 'contract_signature_img',
            ]);
            $sign_url = $staff_contract_info->contract_signature_img ?? '';
        }
        return $sign_url;
    }

    /**
     * 获取某接收人名下各类资产某转移状态下的最新一条转移记录
     * @param array $asset_ids 资产ID组
     * @param integer $to_staff_id 接收人工号
     * @param integer $status 状态1待接收，2已接收，3已拒绝 4已取消
     * @return mixed
     */
    public function getLastTransferLogByAssetIds($asset_ids, $to_staff_id, $status = 0)
    {
        $asset_ids = $asset_ids ?? [];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('asset_id, MAX(finished_at) AS finished_at');
        $builder->from([MaterialAssetTransferLogModel::class]);
        $builder->where('is_deleted = :is_deleted: and to_staff_id = :to_staff_id: and transfer_type != :transfer_type:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'to_staff_id' => $to_staff_id, 'transfer_type' => MaterialEnums::TRANSFER_TYPE_USER_SYNC]);
        //状态
        if (!empty($status)) {
            $builder->andWhere('status', $status);
        }
        // 名下指定资产数据筛选
        if (!empty($asset_ids)) {
            $builder->inWhere('asset_id', $asset_ids);
        }
        $builder->groupBy('asset_id');
        return $builder->getQuery()->execute()->toArray();
    }
}
