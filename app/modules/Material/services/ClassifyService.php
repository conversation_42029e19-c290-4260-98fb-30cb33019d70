<?php

namespace App\Modules\Material\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Ledger\Models\LedgerAccountModel;
use App\Modules\Material\Models\MaterialAttributeModel;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Purchase\Services\FfwService;
use App\Modules\Material\Models\MaterialSauModel;

class ClassifyService extends BaseService
{
    const MATERIAL_MAX_LEVEL = 5;//分类村级限制  最大为5级
    const MATERIAL_FINANCE_MAX_LEVEL = 5;//分类村级限制  最大为5级

    const DEFAULT_PARAMETER_ZERO = 0;  //数据库默认参数

    const DEFAULT_PARAMETER_ONE = 1;   //数据库默认参数
    const DEFAULT_PARAMETER_TWO = 2;   //数据库默认参数

    //scm连接配置
    public static $scm_config = [
        'goods_unit' => 'open/goodsUnitList',//获取标准型号属性
        'add_ent' => 'goods/addEntCategory',//添加分类
        'edit_ent' => 'goods/editEntCategory',//编辑分类
        'search_ent' => 'goods/searchEntCategory',//查询分类是否存在
        'list' => 'goods/entCategoryList',
        'delete' => 'goods/deleteEntCategory',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * 搜索分类验证
     **/
    public static $list_search = [
        'name' => 'Str',
    ];

    /**
     * 添加物料分类验证
     **/
    public static $validate_material_category = [
        'top' => 'Required|IntIn:0,1|>>>:top  param error',//0 顶级过来的  父id  可以为空  1 的时候  父id 不可以为空
        'code' => 'Required|StrLenGeLe:1,30',//分类编码
        'name' => 'Required|StrLenGeLe:1,100',//分类名称
        'parent_id' => 'IfIntEq:top,1|Required|Int', //父级ID
        'type' => 'Required|IntIn:1,2,3|>>>: type param error', //物料类型，1资产，2耗材，3服务类
        'status' => 'IntIn:1,2|>>>:status  param error',//状态
        'ids' => 'StrLenGeLe:1,80',//所属上级的id集合
        'transfer_forbid'=>'IntIn:' . MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_VALIDATE,//是否限制员工互转, 1是，2否
    ];

    /**
     * 添加财务分类验证
     **/
    public static $validate_material_finance_category = [
        'top' => 'Required|IntIn:0,1|>>>:top  param error',//0 顶级过来的  父id  可以为空  1 的时候  父id 不可以为空
        'code' => 'Required|StrLenGeLe:1,30',//分类编码
        'name' => 'Required|StrLenGeLe:1,100',//分类名称
        'parent_id' => 'IfIntEq:top,1|Required|Int', //父级ID
        'use_limit' => 'Required|IntGeLe:0,999',//使用期限，单位是月
        'status' => 'IntIn:1,2|>>>:status  param error',//启用停用状态
        'update_to_scm' => 'IntIn:1,2|>>>:update_to_scm  param error',//是否更新至scm
        'update_to_sap' => 'IntIn:1,2|>>>:update_to_sap  param error',//是否更新至sap
        'update_to_acceptance' => 'IntIn:1,2|>>>:update_to_acceptance  param error',//是否验收
        'ids' => 'StrLenGeLe:1,80',//所属上级的id集合
        'purchase_type'=>'Required|IntGe:0|>>>:purchase_type  param error',//采购类型
        'ledger_account_id'=>'Required|IntGe:0|>>>:ledger_account_id  param error',//核算科目id
    ];

    /**
     * 修改
     **/
    public static $validate_save_material_category = [
        'id' => 'Required|IntGt:0',
    ];

    /**
     *  删除
     */
    public static $validate_del_material_category = [
        'id' => 'Required|IntGt:0'
    ];

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 枚举类型
     * @Token
     * @Date: 2021-10-14 11:11
     * @return:
     **@author: peak pan
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //物料分类-树状
            $category_enums['type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $data['category_map'] = $this->getClassifyArr($category_enums, 'search')['data'];
            //财务分类-树状
            $finance_category_enums['type'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $data['finance_map'] = $this->getClassifyArr($finance_category_enums, 'search')['data'];
            //计量单位
            $data['attribute_value_map'] = $this->getAttributeValueArr();
            //SAP单位
            $data['sap_attribute_value_map'] = $this->getSapAttributeValueArr();
            //币种
            foreach (GlobalEnums::$currency_item as $key=>$currency) {
                $currency_['id'] = $key;
                $currency_['name'] =  static::$t->_($currency);
                $data['currency_map'][] = $currency_;
            }
            //长宽高
            $data['measurement_map'] = MaterialClassifyEnums::MATERIAL_ATTRIBUTE_MEASUREMENT;
            //重量
            $data['weight_map'] = MaterialClassifyEnums::MATERIAL_ATTRIBUTE_WEIGHT;
            //物料类型
            foreach (MaterialClassifyEnums::$material_type_list as $category_type_) {
                $category_['id'] = $category_type_['id'];
                $category_['name'] = static::$t->_($category_type_['name']);
                $data['category_type'][] = $category_;
            }
            //是否同步等状态
            $data['category_switch'] =[['id'=>1,'name'=>static::$t->_('budget_amount_type.1')],['id'=>2,'name'=>static::$t->_('budget_amount_type.2')]];
            //启用状态
            $data['category_status'] =[['id'=>1,'name'=>static::$t->_('start_using')],['id'=>2,'name'=>static::$t->_('prohibited_use')]];
            //可申请/购买 / 是否包含职位
            $trans_arr = [
                'use_sence' => MaterialClassifyEnums::$material_use_scene,
                'is_contain_job' => MaterialClassifyEnums::$material_is_contain_job
            ];
            foreach ($trans_arr as $key => $item) {
                foreach ($item as $k => $v) {
                    $data[$key][] = [
                        'id' => $k,
                        'name' => static::$t->_($v)
                    ];
                }
            }
            //17404，分仓规则开关
            $data['material_store_storage_open_status'] = WarehouseDivisionRuleService::getInstance()->getStoreStorageOpenStatus();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('获取资产管理枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取分类
     * type  1 资产分类  2 财务分类
     * keys  搜索
     * @param $params
     * @param string $type list列表，search是搜索枚举
     * @Date: 2021-10-14 12:00
     * @return:
     **@author: peak pan
     */
    public function getClassifyArr($params, $type ='list')
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $builder = $this->modelsManager->createBuilder();
        if ($params['type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
            $builder->columns('id,name,code,parent_id,level,type,status,ids,sort,transfer_forbid');
            $builder->from(MaterialCategoryModel::class);
        } elseif ($params['type'] == MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS) {
            $builder->columns('id,name,code,parent_id,level,use_limit,status,update_to_scm,ids,sort,purchase_type,ledger_account_id,update_to_acceptance,update_to_sap,concat(code,name) as show_name');
            $builder->from(MaterialFinanceCategoryModel::class);
        }
        $builder->where("is_deleted = :is_deleted:", ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]);
        if ($type == 'search') {
            //分类搜索枚举的只需要启用的分类数据
            $builder->andWhere('status=:status:', ['status'=>MaterialClassifyEnums::MATERIAL_START_USING]);
            if (isset($params['purchase_type']) && !empty($params['purchase_type']) && $params['type'] == MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS) {
                //财务分类按照采购类型搜索
                $builder->andWhere('purchase_type=:purchase_type:', ['purchase_type'=>$params['purchase_type']]);
            }
        }
        $last_builder = clone $builder;
        $builder->orderBy(' sort desc,id asc');
        if (empty($params['name'])) {
            $list = $builder->getQuery()->execute()->toArray();
        } else {
            $builder->andwhere("name like :name: or code like :name:", ['name'=>'%'.trim($params['name']).'%']);
            $sta_arr = $builder->getQuery()->execute()->toArray();
            if (empty($sta_arr)) {
                $list = [];
            } else {
                $ids = format_array(array_filter(explode(',', implode(',', array_column($sta_arr, 'ids')))));
                //用于直接搜索的为一级
                array_push($ids, array_column($sta_arr, 'id'));
                $last_builder->andwhere("id in({ids:array})", ['ids' => $ids]);
                $last_builder->orderBy(' sort desc,id asc');
                $last_arr_list = $last_builder->getQuery()->execute()->toArray();
                $list = array_merge($last_arr_list, $sta_arr);
            }
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $list ? getTree($list, self::DEFAULT_PARAMETER_ZERO, self::DEFAULT_PARAMETER_ZERO, 'parent_id', true) : [],
        ];
    }

    /**
     * 物料属性数据（基本单位）
     * @Date: 2021-10-14 12:00
     * @return:
     **@author: peak pan
     */
    public function getAttributeValueArr()
    {
        try {
            $listAttribute = [];

            if ($this->checkLock(MaterialClassifyEnums::GET_ATTRIBUTE_VALUE_ARR_KEY)) {
                $listAttribute = json_decode($this->getCache(MaterialClassifyEnums::GET_ATTRIBUTE_VALUE_ARR_KEY), true);
            } else {
                //先从SCM那里获取单位
                $scm_service = new ScmService();
                $scm_unit_data = $scm_service->getGoodsUnit();
                if ($scm_unit_data) {
                    //能从scm读取到单位信息
                    $listAttribute = $scm_unit_data;
                } else {
                    $listAttribute = $this->getOaUnit();
                }
                $this->setLock(MaterialClassifyEnums::GET_ATTRIBUTE_VALUE_ARR_KEY, json_encode($listAttribute), MaterialClassifyEnums::GET_ATTRIBUTE_VALUE_ARR_TIME);
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->info($real_message);
        }
        return $listAttribute ? $listAttribute : [];
    }

    /**
     * 需要从OA本地获取单位枚举
     * @return mixed
     */
    public function getOaUnit()
    {
        //需要从OA本地获取单位枚举
        $lang = strtolower(static::$language == 'zh-CN' ? 'zh' : static::$language);
        $lang = $lang == 'zh' ? 'zh' : 'en';
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name_zh,name_en, name_'.$lang." as name");
        $builder->from(MaterialAttributeModel::class);
        $builder->where("is_deleted = :is_deleted: and type=:type:", ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO, 'type'=>MaterialClassifyEnums::UNIT_SCM]);
        $builder->orderBy('id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取SAP单位
     * @return array
     */
    public function getSapAttributeValueArr()
    {
        $listAttribute = [];
        try {
            //需要从OA本地获取单位枚举
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('id, name_en as name');
            $builder->from(MaterialAttributeModel::class);
            $builder->where("is_deleted = :is_deleted: and type=:type:", ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO, 'type'=>MaterialClassifyEnums::UNIT_SAP]);
            $listAttribute = $builder->getQuery()->execute()->toArray();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $logger = $this->getDI()->get('logger');
            $logger->info($e->getMessage());
        }
        return $listAttribute ? $listAttribute : [];
    }

    /**
     * 物料分类添加提交
     * @param array $data 新增参数组
     * @return array
     */
    public function categoryAdd(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if ($data['top'] == self::DEFAULT_PARAMETER_ONE && empty($data['parent_id'])) {
                throw new ValidationException(self::$t['material_category_parent_id_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }
            // 物料分类名称重复
            $exists = $this->isExistsCodeByName($data,'name');
            if ($exists === true) {
                throw new ValidationException(self::$t['material_category_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }
            // 物料分类code重复
            $exists_code = $this->isExistsCodeByName($data,'code');
            if ($exists_code === true) {
                throw new ValidationException(self::$t['material_category_code_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }
            if (!empty($data['parent_id'])) {
                $parent_id_data = MaterialCategoryModel::findFirstById($data['parent_id']);
                if ($parent_id_data->level >= self::MATERIAL_MAX_LEVEL) {
                    throw new ValidationException('物料分类最多' . self::MATERIAL_MAX_LEVEL . '级 ', MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
                }
                $level = $parent_id_data->level + self::DEFAULT_PARAMETER_ONE;
            } else {
                $level = self::DEFAULT_PARAMETER_ONE;
            }
            // 重构主表待入库数据
            $ids_ = empty($data['ids']) ? '' : $data['ids'] . ',';
            $category_data = [
                'name' => trim($data['name']),
                'code' => trim($data['code']),
                'parent_id' => $data['parent_id'],
                'level' => $level ? $level : $data['level'],
                'type' => $data['type'] ?? self::DEFAULT_PARAMETER_ONE,
                'status' => $data['status'] ?? self::DEFAULT_PARAMETER_ONE,
                'sort' => self::DEFAULT_PARAMETER_ZERO,
                'ids' => !empty($data['parent_id']) ? $ids_ . $data['parent_id'] : self::DEFAULT_PARAMETER_ZERO,
                'is_deleted' => self::DEFAULT_PARAMETER_ZERO,
                'transfer_forbid' => $data['transfer_forbid'] ?? MaterialClassifyEnums::TRANSFER_FORBID_NO,
                'created_at' => date('Y-m-d H:i:s', time()),
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
            $main_model = new MaterialCategoryModel();
            $bool = $main_model->i_create($category_data);
            if ($bool === false) {
                throw new BusinessException('物料分类添加失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error("material-classify-category-add:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 物料分类修改提交
     * @param array $data 修改参数组
     * @return array
     */
    public function categorySave(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            if ($data['top'] == self::DEFAULT_PARAMETER_ONE && empty($data['parent_id'])) {
                throw new ValidationException(self::$t['material_category_parent_id_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }

            $category = MaterialCategoryModel::findFirstById($data['id']);
            //判断分类是否存在
            if (empty($category) || $category->is_deleted == MaterialClassifyEnums::IS_DELETED_YES) {
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //修改的名称是否已存在
            if (!empty($data['name']) && trim($data['name']) != trim($category->name) && $this->isExistsCodeByName($data,'name')) {
                throw new ValidationException(self::$t['material_category_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //修改的编号是否已存在
            if (!empty($data['code']) && trim($data['code']) != trim($category->code) && $this->isExistsCodeByName($data,'code')) {
                throw new ValidationException(self::$t['material_category_code_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //修改启用状态为禁用时需要判断改分类下子分类是否有启用的分类，有不可禁用
            if ($data['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE && $this->isHasChildrenUsing($data['id'], MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS)) {
                throw new ValidationException(self::$t['material_category_prohibited'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }

            $parent_id_data = MaterialCategoryModel::findFirstById($data['parent_id']);
            if(!empty($data['parent_id']) && $data['parent_id'] != $category->parent_id) {
                $dataMaterialCategory= MaterialCategoryModel::find([
                    'conditions' => 'parent_id = :parent_id:',
                    'columns' => 'id',
                    'bind' => ['parent_id' => $parent_id_data->id]
                ])->toArray();

               if(empty($dataMaterialCategory)) {
                   throw new ValidationException(self::$t['material_category_not_last_msg'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
               }
            }
            //本级内修改
            if (empty($data['top']) && empty($parent_id_data->parent_id) ) {
                $parent_id = self::DEFAULT_PARAMETER_ZERO;
                $level = self::DEFAULT_PARAMETER_ONE;
            } else {
                $parent_id = $data['parent_id'] ? $data['parent_id'] : $category->parent_id;
                $level = $category->level;
            }

            if (!empty($data['parent_id'])) {
                if ($son_list = (new MaterialCategoryModel)->getSonList($data['id'],'level')) {
                    if(count($son_list['data'])==1 && $son_list['data'][0]==$data['id']){
                        $count_array =  self:: DEFAULT_PARAMETER_ONE;
                    }else {
                        if (empty($son_list['data'])) {
                            $count_array = self::DEFAULT_PARAMETER_ZERO;
                        } else {
                            $category_arr = MaterialCategoryModel::find([
                                'conditions' => 'id IN ({id:array}) ',
                                'bind' => ['id' => $son_list['data']]
                            ])->toArray();
                            $count_array = arrayDepth(getTree($category_arr, self::DEFAULT_PARAMETER_ZERO, self::DEFAULT_PARAMETER_ZERO, 'parent_id', true)) + 1;
                        }
                    }
                } else {
                    $count_array = self::DEFAULT_PARAMETER_ZERO;
                }
                if(in_array($data['id'], explode(',', $parent_id_data->ids))) {
                    //本级分类中不容许向下修改
                    throw new ValidationException(self::$t['material_category_not_last_msg'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }

                if (bcadd($parent_id_data->level, $count_array) > self::MATERIAL_MAX_LEVEL) {
                    throw new ValidationException(self::$t['material_category_max_four'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }
                $level = $parent_id_data->level + self::DEFAULT_PARAMETER_ONE;

                if ($level > self::MATERIAL_MAX_LEVEL) {
                    throw new ValidationException(self::$t['material_category_max_four'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }
            }
            // 重构主表待入库数据
            $ids_ = empty($data['ids']) ? '' : $data['ids'] . ',';
            $category_data = [
                'name' => trim($data['name']),
                'code' => trim($data['code']),
                'parent_id' => $parent_id,
                'level' => $level,
                'type' => $data['type'] ?? self::DEFAULT_PARAMETER_ONE,
                'status' => $data['status'] ?? self::DEFAULT_PARAMETER_ONE,
                'sort' => self::DEFAULT_PARAMETER_ZERO,
                'ids' => !empty($parent_id) ? $ids_ . $data['parent_id'] : self::DEFAULT_PARAMETER_ZERO,
                'is_deleted' => self::DEFAULT_PARAMETER_ZERO,
                'updated_at' => date('Y-m-d H:i:s', time()),
                'transfer_forbid' => $data['transfer_forbid'] ?? MaterialClassifyEnums::TRANSFER_FORBID_NO,
            ];

            $category_data['id'] = $data['id'];
            unset($category_data['sort']);
            unset($category_data['created_at']);
            $save_bool = $category->i_update($category_data);
            if ($save_bool === false) {
                throw new BusinessException('物料分类修改失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($category), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            $this->all_updata($category_data);
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('material-classify-category-save:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 批量修改上级和下级
     * @Token
     * @Date: 2021-10-21 14:38
     * @return:
     * @throws BusinessException
     **@author: peak pan
     */
    protected function all_updata($category_data)
    {
        //查询下级分类
        $material_category = new MaterialCategoryModel();
        $two_ategory = MaterialCategoryModel::find([
            'conditions' => 'parent_id = :parent_id:',
            'columns' => 'id,parent_id',
            'bind' => ['parent_id' => $category_data['id']]
        ])->toArray();
        $success_status = false;
        $ids = empty($category_data['ids']) ? '' : $category_data['ids'] . ',';
        if (!empty($two_ategory)) {
            //修改后的下一层层级
            $two_ids = array_column($two_ategory, 'id');
            $two_parent_id = array_column($two_ategory, 'parent_id');

            //二级修改
            $level = $category_data['level'] + 1;
            $last_ids = $ids . current($two_parent_id);
            if (!$material_category->getAllSave($two_ids, $level, $last_ids)) {
                $success_status = true;
            }
            $thr_ategory = MaterialCategoryModel::find([
                'conditions' => 'parent_id IN ({parent_id:array}) ',
                'columns' => 'id,parent_id',
                'bind' => ['parent_id' => $two_ids]
            ])->toArray();

            if (!empty($thr_ategory)) {
                $thr_ids = array_column($thr_ategory, 'id');
                $thr_parent_id = array_column($thr_ategory, 'parent_id');
                //三级修改
                $level = $level + 1;
                $last_ids = $last_ids . ',' . current($thr_parent_id);

                if (!$material_category->getAllSave($thr_ids, $level, $last_ids)) {
                    $success_status = true;
                }
                $four_ategory = MaterialCategoryModel::find([
                    'conditions' => 'parent_id IN ({parent_id:array}) ',
                    'columns' => 'id',
                    'bind' => ['parent_id' => $thr_ids]
                ])->toArray();

                if (!empty($four_ategory)) {
                    $four_ids = array_column($four_ategory, 'id');
                    $four_parent_id = array_column($four_ategory, 'parent_id');
                    //四级修改

                    $level = $level + self::DEFAULT_PARAMETER_ONE;
                    $last_ids = $last_ids . ',' . current($four_parent_id);
                    if (!$material_category->getAllSave($four_ids, $level, $last_ids)) {
                        $success_status = true;
                    }
                    $five_ategory = MaterialCategoryModel::find([
                        'conditions' => 'parent_id IN ({parent_id:array}) ',
                        'columns' => 'id',
                        'bind' => ['parent_id' => $four_ids]
                    ])->toArray();

                    if (!empty($five_ategory)) {
                        $five_ids = array_column($five_ategory, 'id');
                        $five_parent_id = array_column($four_ategory, 'parent_id');
                        //五级修改
                        $level = $level + self::DEFAULT_PARAMETER_ONE;
                        $last_ids = $last_ids . ',' . current($five_parent_id);
                        if (!$material_category->getAllSave($five_ids, $level, $last_ids)) {
                            $success_status = true;
                        }
                    }
                }
            }
        }else{
            return true;
        }
        if ($success_status) {
            throw new BusinessException('分类修改失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
        }
        return true;
    }

    /**
     * 批量修改上级和下级
     * @Token
     * @Date: 2021-10-21 14:38
     * @return:
     * @throws BusinessException
     **@author: peak pan
     */
    protected function all_updata_finance($category_data)
    {
        //查询下级分类
        $material_category = new MaterialFinanceCategoryModel();
        $two_ategory = MaterialFinanceCategoryModel::find([
            'conditions' => 'parent_id = :parent_id:',
            'columns' => 'id,parent_id',
            'bind' => ['parent_id' => $category_data['id']]
        ])->toArray();
        $success_status = false;
        $ids = empty($category_data['ids']) ? '' : $category_data['ids'] . ',';
        if (!empty($two_ategory)) {
            //修改后的下一层层级
            $two_ids = array_column($two_ategory, 'id');
            $two_parent_id = array_column($two_ategory, 'parent_id');

            //二级修改
            $level = $category_data['level'] + self::DEFAULT_PARAMETER_ONE;
            $last_ids = $ids . current($two_parent_id);
            if (!$material_category->getAllSave($two_ids, $level, $last_ids)) {
                $success_status = true;
            }
            $thr_ategory = MaterialFinanceCategoryModel::find([
                'conditions' => 'parent_id IN ({parent_id:array}) ',
                'columns' => 'id,parent_id',
                'bind' => ['parent_id' => $two_ids]
            ])->toArray();

            if (!empty($thr_ategory)) {
                $thr_ids = array_column($thr_ategory, 'id');
                $thr_parent_id = array_column($thr_ategory, 'parent_id');
                //三级修改
                $level = $level + self::DEFAULT_PARAMETER_ONE;
                $last_ids = $last_ids . ',' . current($thr_parent_id);
                if (!$material_category->getAllSave($thr_ids, $level, $last_ids)) {
                    $success_status = true;
                }
            }
        }else{
            return true;
        }
        if ($success_status) {
            throw new BusinessException('分类修改失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
        }
        return true;
    }

    /**
     * 财务分类添加提交
     * @param array $data
     * @param array $user
     * @return array
     * entCategoryList
     */
    public function financeAdd(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            if ($data['top'] == self::DEFAULT_PARAMETER_ONE && empty($data['parent_id'])) {
                throw new ValidationException(self::$t['material_category_parent_id_existed'], MaterialClassifyEnums::$MATERIAL_FINANCE_CATEGORY_ADD_EXISTED);
            }
            // 财务分类名称重复
            $exists = $this->isFinanceExistsCodeByName($data, 'name');
            if ($exists === true) {
                throw new ValidationException(self::$t['material_category_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }

            // 财务分类code重复
            $exists_code = $this->isFinanceExistsCodeByName($data, 'code');
            if ($exists_code === true) {
                throw new ValidationException(self::$t['material_category_code_existed'], MaterialClassifyEnums::$MATERIAL_FINANCE_CATEGORY_ADD_EXISTED);
            }

            if (!empty($data['parent_id'])) {
                $parent_id_data = MaterialFinanceCategoryModel::findFirstById($data['parent_id']);
                if ($parent_id_data->level >= self::MATERIAL_FINANCE_MAX_LEVEL) {
                    throw new ValidationException('财务分类最多' . self::MATERIAL_FINANCE_MAX_LEVEL . '级', MaterialClassifyEnums::$MATERIAL_FINANCE_CATEGORY_ADD_EXISTED);
                }
                $level = $parent_id_data->level + self::DEFAULT_PARAMETER_ONE;
            } else {
                $level = self::DEFAULT_PARAMETER_ONE;
            }
            // 重构主表待入库数据
            $ids_ = empty($data['ids']) ? '' : $data['ids'] . ',';
            $category_data = [
                'name' => trim($data['name']),
                'code' => trim($data['code']),
                'parent_id' => $data['parent_id'],
                'level' => $level,
                'use_limit' => $data['use_limit'] ?? self::DEFAULT_PARAMETER_ZERO,
                'status' => $data['status'] ?? self::DEFAULT_PARAMETER_ONE,
                'update_to_scm' => $data['update_to_scm'] ?? self::DEFAULT_PARAMETER_TWO,
                'update_to_sap' => $data['update_to_sap'] ?? self::DEFAULT_PARAMETER_ONE,
                'update_to_acceptance' => $data['update_to_acceptance'] ?? self::DEFAULT_PARAMETER_ONE,
                'purchase_type' => $data['purchase_type'],
                'ledger_account_id' => $data['ledger_account_id'],
                'ids' => !empty($data['parent_id']) ? $ids_ . $data['parent_id'] : self::DEFAULT_PARAMETER_ZERO,
                'sort' => self::DEFAULT_PARAMETER_ZERO,
                'is_deleted' => self::DEFAULT_PARAMETER_ZERO,
                'created_at' => date('Y-m-d H:i:s', time()),
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
            $main_model = new MaterialFinanceCategoryModel();
            $bool = $main_model->i_create($category_data);
            if ($bool === false) {
                throw new BusinessException('财务分类添加失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_FINANCE_CATEGORY_ADD_EXISTED);
            }
            //  todo 由于scm那边分类仅支持到2级，故而暂时财务分类同步scm功能屏蔽，后续scm开发支持多级在增加逻辑开放
            /*if ($data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $data_scm['code'] = $data['code'];
                $data_scm['name'] = $data['name'];
                $data_scm['pid'] = $data['parent_id'];
                $data_scm['status'] = $data['status'] == 2 ? self::DEFAULT_PARAMETER_ZERO : $data['status'];
                $ffw_service = new FfwService();
                $post_str = $ffw_service->buildRequestParam($data_scm, self::DEFAULT_PARAMETER_ONE);
                $data_return = $ffw_service->newPostRequest(self::$scm_config['add_ent'], $post_str);
                if ($data_return['code'] != MaterialClassifyEnums::IS_DELETED_NO) {
                    throw new ValidationException($data_return['message'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
                }
            }*/

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error($real_message . json_encode($category_data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 财务分类编辑提交
     * @param array $data
     * @param array $user
     * @return array
     */
    public function financeSave(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            if ($data['top'] == self::DEFAULT_PARAMETER_ONE && empty($data['parent_id'])) {
                throw new ValidationException(self::$t['material_category_parent_id_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            $ategory = MaterialFinanceCategoryModel::findFirstById($data['id']);
            if (empty($ategory) || $ategory->is_deleted == MaterialClassifyEnums::IS_DELETED_YES) {
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            $category_data['id'] = $data['id'];

            //检测名称是否重复
            if (!empty($data['name']) && trim($data['name']) != $ategory->name && $this->isFinanceExistsCodeByName($data,'name')) {
                throw new ValidationException(self::$t['material_category_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //检测编号是否重复
            if (!empty($data['code']) && trim($data['code']) != $ategory->code && $this->isFinanceExistsCodeByName($data,'code')) {
                throw new ValidationException(self::$t['material_category_code_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //修改启用状态为禁用时需要判断改分类下子分类是否有启用的分类，有不可禁用
            if ($data['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE && $this->isHasChildrenUsing($data['id'], MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS)) {
                throw new ValidationException(self::$t['material_category_prohibited'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }

            $parent_id_data = MaterialFinanceCategoryModel::findFirstById($data['parent_id']);
            if(!empty($data['parent_id']) && $data['parent_id']!=$ategory->parent_id){
                $dataMaterialCategory= MaterialFinanceCategoryModel::find([
                    'conditions' => 'parent_id = :parent_id:',
                    'columns' => 'id',
                    'bind' => ['parent_id' => $parent_id_data->id]
                ])->toArray();

                if(empty($dataMaterialCategory)){
                    throw new ValidationException(self::$t['material_category_not_last_msg'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }
            }

            //本级内修改
            if (empty($data['top']) && empty($ategory->parent_id) ) {
                $parent_id = self::DEFAULT_PARAMETER_ZERO;
                $level = self::DEFAULT_PARAMETER_ONE;
            } else {
                $parent_id = $data['parent_id'] ? $data['parent_id'] : $ategory->parent_id;
                $level = $ategory->level;
            }

            if (!empty($data['parent_id']) && $ategory->parent_id != $data['parent_id']) {
                if ($son_list = (new MaterialFinanceCategoryModel)->getSonList($data['id'],'level')) {
                    if(count($son_list['data'])==self:: DEFAULT_PARAMETER_ONE && $son_list['data'][0]==$data['id']){
                        $count_array =  self:: DEFAULT_PARAMETER_ONE;
                    }else {
                        if(empty($son_list['data'])){
                            $count_array = self::DEFAULT_PARAMETER_ZERO;
                        }else{
                        $category_arr = MaterialFinanceCategoryModel::find([
                            'conditions' => 'id IN ({id:array}) ',
                            'bind' => ['id' => $son_list['data']]
                        ])->toArray();
                        }
                        $count_array = arrayDepth(getTree($category_arr, self::DEFAULT_PARAMETER_ZERO, self::DEFAULT_PARAMETER_ZERO, 'parent_id', true));
                    }
                } else {
                    $count_array = self::DEFAULT_PARAMETER_ZERO;
                }

                if(in_array($data['id'],explode(',',$parent_id_data->ids))){
                    //本级分类中不容许向下修改
                    throw new ValidationException(self::$t['material_category_not_last_msg'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }

                if (bcadd($parent_id_data->level, $count_array) > self::MATERIAL_FINANCE_MAX_LEVEL) {
                    throw new ValidationException(self::$t['material_category_max_four'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }

                $level = $parent_id_data->level + self::DEFAULT_PARAMETER_ONE;
            }
            $ids_ = empty($data['ids']) ? '' : $data['ids'] . ',';

            $category_data['name'] = trim($data['name'] ? $data['name'] : $ategory->name);
            $category_data['code'] = trim($data['code'] ? $data['code'] : $ategory->code);
            $category_data['parent_id'] = $parent_id;
            $category_data['level'] = $level;
            $category_data['use_limit'] = $data['use_limit'] ? $data['use_limit'] : $ategory->use_limit;
            $category_data['status'] = $data['status'] ? $data['status'] : $ategory->status;
            $category_data['update_to_scm'] = $data['update_to_scm'] ? $data['update_to_scm'] : $ategory->update_to_scm;
            $category_data['update_to_sap'] = $data['update_to_sap'] ? $data['update_to_sap'] : $ategory->update_to_sap;
            $category_data['update_to_acceptance'] = $data['update_to_acceptance'] ? $data['update_to_acceptance'] : $ategory->update_to_acceptance;
            $category_data['purchase_type'] = $data['purchase_type'] ? $data['purchase_type'] : $ategory->purchase_type;
            $category_data['ledger_account_id'] = $data['ledger_account_id'] ? $data['ledger_account_id'] : $ategory->ledger_account_id;
            $category_data['is_deleted'] = $data['is_deleted'] ? $data['is_deleted'] : $ategory->is_deleted;
            $category_data['updated_at'] = date('Y-m-d H:i:s', time());
            $category_data['ids'] = $data['parent_id'] != $ategory->parent_id ? $ids_ . $data['parent_id'] : $ategory->ids;

            $save_bool = $ategory->i_update($category_data);
            if ($save_bool === false) {
                throw new BusinessException('财务分类修改失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            $this->all_updata_finance($category_data);
            // todo 由于scm那边分类仅支持到2级，故而暂时财务分类同步scm功能屏蔽，后续scm开发支持多级在增加逻辑开放开始修改scm
            /*if ($data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $finance_category = MaterialFinanceCategoryModel::findFirstById($data['id']);
                $is_goods_data=$this->scmIsExists($finance_category);
                $scm_data['code'] = $data['code'];
                $scm_data['name'] = $data['name'];
                $scm_data['status'] = $data['status'] == 2 ? self::DEFAULT_PARAMETER_ZERO : $data['status'];

                $ffw_service = new FfwService();
                if (!empty($is_goods_data['data'])) {
                    //存在就是修改
                    $scm_data['oldCode'] = $finance_category->code;
                    $post_str = $ffw_service->buildRequestParam($scm_data, self::DEFAULT_PARAMETER_ONE);
                    $add_goods_data = $ffw_service->postRequest(self::$scm_config['edit_ent'], $post_str);
                } else {
                    //不存在就是添加
                    $scm_data['pid'] = $data['parent_id'];
                    $post_str = $ffw_service->buildRequestParam($scm_data, self::DEFAULT_PARAMETER_ONE);
                    $add_goods_data = $ffw_service->newPostRequest(self::$scm_config['add_ent'], $post_str);
                }
                if ($add_goods_data['code'] != MaterialClassifyEnums::IS_DELETED_NO) {
                    throw new ValidationException($add_goods_data['message'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }
            }*/
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error($real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 物料（财务）分类删除
     * @param array $data
     * @return array
     */
    public function materialDel(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            if ($data['classify'] == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
                //物料分类
                $category = MaterialCategoryModel::findFirstById($data['id']);
            } else {
                //财务分类
                $category = MaterialFinanceCategoryModel::findFirstById($data['id']);
            }
            if (empty($category) || $category->is_deleted == MaterialClassifyEnums::IS_DELETED_YES) {
                //不存在分类或者分类已被删除
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            //检测分类是否满足可删除条件
            $data['status'] = $category->status;
            $is_del_data =  $this->isDelValidation($data);
            if ($is_del_data) {
                //满足条件，做分类删除
                $category_data = [
                    'is_deleted' => MaterialClassifyEnums::IS_DELETED_YES,
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ];
                $save_bool = $category->i_update($category_data);
                if ($save_bool === false) {
                    throw new BusinessException('分类修改失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                }

                // todo 由于scm那边分类仅支持到2级，故而暂时财务分类同步scm功能屏蔽，后续scm开发支持多级在增加逻辑开放
                /*if($data['classify'] == MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS && $category->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                    //财务分类 && 需要跟scm同步，则删除的时候需要在scm端删除
                    if($this->scmIsExists($category)){
                        $scm_delete=$this->scmDelete(['code'=>$category->code]);
                        if ($scm_delete['code'] != MaterialClassifyEnums::IS_DELETED_NO) {
                            throw new BusinessException($scm_delete['message'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
                        }
                    }
                }*/
                $db->commit();
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error($real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 物料（财务）分类上移下移
     * @param array $data
     * @return array
     */
    public function materialMove(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            if ($data['classify'] == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
                $ategory = MaterialCategoryModel::findFirstById($data['id']);
            } else {
                $ategory = MaterialFinanceCategoryModel::findFirstById($data['id']);
            }
            if (empty($ategory) || $ategory->is_deleted == MaterialClassifyEnums::IS_DELETED_YES ) {
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            // 重构主表待入库数据
            if ($data['move'] == "up") {
                $new_sort = $ategory->sort + self::DEFAULT_PARAMETER_ONE;
            } else {
                $new_sort = $ategory->sort - self::DEFAULT_PARAMETER_ONE;
            }
            $category_data = [
                'sort' => $new_sort,
                'updated_at' => date('Y-m-d', time()),
            ];
            $save_bool = $ategory->i_update($category_data);
            if ($save_bool === false) {
                throw new BusinessException('移动失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_CATEGORY_SAVE_FAIL);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error($real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 获取分类名称
     * @param array $data
     * @param int $type 1物料分类，2财务分类
     * @return array
     */
    public function getNameList(array $data, $type = 1)
    {
        if ($type == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
            return (new  MaterialCategoryModel())->getCategoryName($data);
        } elseif ($type == MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS) {
            return (new  MaterialFinanceCategoryModel())->getFinanceName($data);
        }
    }

    /**
     * 物料分类详情
     * @Date: 2021-11-02 16:43
     * @author: peak pan
     * @param array $data 请求参数组
     * @return array
     */
    public function materialDetail($data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $detail = [];
        try {
            $db->begin();
            $category = MaterialCategoryModel::findFirstById($data['id']);
            if (empty($category)) {
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
            }
            $category_arr = $category->toArray();
            $last_category = MaterialCategoryModel::findFirstById($category_arr['parent_id']);
            $category_arr['last_name'] = empty($last_category->id) ? '' : $last_category->name;
            $category_arr['type'] = (int)$category_arr['type'];
            $category_arr['parent_id'] = (int)$category_arr['parent_id'];
            $category_arr['level'] = (int)$category_arr['level'];
            $category_arr['status'] = (int)$category_arr['status'];
            $category_arr['transfer_forbid'] = (int)$category_arr['transfer_forbid'];
            unset($category_arr['sort']);
            unset($category_arr['created_at']);
            unset($category_arr['updated_at']);
            $detail = $category_arr;

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error("material-classify-category-detail:" . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 财务分类详情
     * @Date: 2021-11-02 16:43
     * @param array $data 数据
     * @author: peak pan
     * @return array
     **/
    public function materialFinanceDetail($data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        $detail = [];
        try {
            $category = MaterialFinanceCategoryModel::findFirstById($data['id']);
            if (empty($category)) {
                throw new ValidationException(self::$t['material_category_data_existed'], MaterialClassifyEnums::$MATERIAL_FINANCE_CATEGORY_ADD_EXISTED);
            }
            $category_arr                         = $category->toArray();
            $last_category                        = MaterialFinanceCategoryModel::findFirstById($category_arr['parent_id']);
            $category_arr['last_name']            = empty($last_category->id) ? '' : $last_category->name;
            $category_arr['parent_id']            = (int)$category_arr['parent_id'];
            $category_arr['level']                = (int)$category_arr['level'];
            $category_arr['status']               = (int)$category_arr['status'];
            $category_arr['use_limit']            = (int)$category_arr['use_limit'];
            $category_arr['update_to_scm']        = $category_arr['update_to_scm'];
            $category_arr['update_to_sap']        = $category_arr['update_to_sap'];
            $category_arr['update_to_acceptance'] = $category_arr['update_to_acceptance'];
            $category_arr['purchase_type']        = $category_arr['purchase_type'];
            $category_arr['ledger_account_id']    = $category_arr['ledger_account_id'];
            //获取核算科目对应的account用于回显
            $ledger                  = LedgerAccountModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $category_arr['ledger_account_id']]
            ]);
            $category_arr['account'] = empty($ledger) ? '' : $ledger->account;
            unset($category_arr['sort']);
            unset($category_arr['created_at']);
            unset($category_arr['updated_at']);
            $detail = $category_arr;

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error($real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $detail
        ];
    }

    /**
     * 分类删除校验
     * @param array $data['id'=>'分类ID','classify'=>'分类类型1物料，2财务', 'status'=>'启用状态：1启用，2禁用']
     * @return bool
     * @throws ValidationException
     */
    public function isDelValidation(array $data)
    {
        if ($data['status'] == MaterialClassifyEnums::MATERIAL_START_USING) {
            //若状态为启用时，不可删除
            throw new ValidationException(self::$t['material_category_can_not_del'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        if ($data['classify'] == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
            //物料分类是否有子类别
            $category_ids = (new MaterialCategoryModel)->getSonList($data['id'],'id');
            $column = 'category_id';
        } else {
            //财务分类是否有子类别
            $category_ids = (new MaterialFinanceCategoryModel)->getSonList($data['id'],'id');
            $column = 'finance_category_id';
        }
        if (!empty($category_ids['data']) && !in_array($data['id'], $category_ids['data'])) {
            //存在子类，则不允许删除
            throw new ValidationException(self::$t['material_category_is_son_existed'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        //不存在子分类，判断改类别下是否有关联标准型号
        $materialSauData = MaterialSauModel::count([
            'conditions' => $column.' = :category_id: and is_deleted = :is_deleted:',
            'columns' => 'id',
            'bind' => [
                'category_id' => $data['id'],
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
            ]
        ]);
        if ($materialSauData > 0) {
            //存在关联的标准型号，则不允许删除
            throw new ValidationException(self::$t['material_category_has_sau'], MaterialClassifyEnums::$MATERIAL_CATEGORY_ADD_EXISTED);
        }
        return true;
    }

    /**
     * 检验物料分类编码或分类名称是否存在
     * @param array $data['code'=>'编码', 'name'=>'名称']
     * @param string $type code|name
     * @return bool
     */
    public function isExistsCodeByName(array $data,$type='code')
    {
        if($type=='code'){
            $exists_code = MaterialCategoryModel::findFirst([
                'conditions' => 'code = :code: and is_deleted=:is_deleted:',
                'columns' => 'id',
                'bind' => ['code' => trim($data['code']), 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if(!empty($exists_code)){
                return true;
            }else{
                return false;
            }

        }else{
            $exists_name = MaterialCategoryModel::findFirst([
                'conditions' => 'name = :name: and is_deleted=:is_deleted:',
                'columns' => 'id',
                'bind' => ['name' => trim($data['name']), 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if(!empty($exists_name)){
                return true;
            }else{
                return false;
            }
        }
    }

    /**
     * 检验财务分类编码或分类名称是否存在
     * @param array $data['code'=>'编码', 'name'=>'名称']
     * @param string $type code|name
     * @return bool
     */
    public function isFinanceExistsCodeByName(array $data,$type='code')
    {
        if($type=='code'){
            $exists_code = MaterialFinanceCategoryModel::findFirst([
                'conditions' => 'code = :code: and is_deleted=:is_deleted:',
                'columns' => 'id',
                'bind' => ['code' => trim($data['code']), 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if(!empty($exists_code)){
                return true;
            }else{
                return false;
            }

        }else{
            $exists_name = MaterialFinanceCategoryModel::findFirst([
                'conditions' => 'name = :name: and is_deleted=:is_deleted:',
                'columns' => 'id',
                'bind' => ['name' => trim($data['name']), 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if(!empty($exists_name)){
                return true;
            }else{
                return false;
            }
        }
    }

    /**
     * 指定分类下是否有有效的子类
     * @param integer $id 分类ID
     * @param integer $classify 类别，1物料，2财务
     * @return bool
     */
    public function isHasChildrenUsing($id, $classify)
    {
        if ($classify == MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS) {
            return (new MaterialCategoryModel)->isHasChildrenUsing($id);
        } else {
            return (new MaterialFinanceCategoryModel)->isHasChildrenUsing($id);
        }
    }

    public function scmDelete(array $data)
    {
        $scm_data['code'] = $data['code'];
        $ffw_service = new FfwService();
        $post_str = $ffw_service->buildRequestParam($scm_data, self::DEFAULT_PARAMETER_ONE);
        return $ffw_service->postRequest(self::$scm_config['delete'], $post_str);
    }

    public function scmIsExists($data)
    {
        $ffw_service = new FfwService();
        $is_goods_str = $ffw_service->buildRequestParam(['code' => $data->code, 'name' => $data->name], self::DEFAULT_PARAMETER_ONE);
        return $ffw_service->postRequest(self::$scm_config['search_ent'], $is_goods_str);
    }

    /**
     * 找到指定子类所要的指定父类级别的分类信息（默认着顶级父类）
     * @param array $params['id'=>'分类ID']
     * @param int $level 父类级别
     * @return array
     */
    public function getCategoryParents($params, $level = 1)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,ids,code');
        $builder->from([MaterialCategoryModel::class]);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        if (isset($params['id']) && !empty($params['id'])) {
            if (is_array($params['id'])) {
                $builder->andWhere('id in ({ids:array})', ['ids' => $params['id']]);
            } else {
                $builder->andWhere('id = :id:', ['id' => $params['id']]);
            }
        }
        $list = $builder->getQuery()->execute()->toArray();
        $parents = [];
        $find_parents = [];
        if (!empty($list)) {
            foreach ($list as $item) {
                if (empty($item['ids'])) {
                    //没有父类直接返回本身即可
                    $parents[$item['id']] = ['code' => $item['code'], 'name' => $item['name']];
                    continue;
                }
                $parents_id_arr = explode(',',$item['ids']);
                $find_parents[$item['id']] = isset($parents_id_arr[$level-1]) ? $parents_id_arr[$level-1] : 0;
            }
        }
        //开始找父类
        if (!empty($find_parents)) {
            $parents_id_arr = array_values(array_filter($find_parents));
            if (empty($parents_id_arr)) {
                return $parents;
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->from([MaterialCategoryModel::class]);
            $builder->where('is_deleted = :is_deleted: and level=:level:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'level'=>$level]);
            if (is_array($parents_id_arr)) {
                $builder->andWhere('id in ({ids:array})', ['ids' => $parents_id_arr]);
            }
            $parent_list = $builder->getQuery()->execute()->toArray();
            if (!empty($parent_list)) {
                $parent_list = array_column($parent_list, null, 'id');
                foreach ($find_parents as $key=>$val) {
                    $parents[$key] = [
                        'code' => isset($parent_list[$val]) ? $parent_list[$val]['code'] : '',
                        'name' => isset($parent_list[$val]) ? $parent_list[$val]['name'] : '',
                    ];
                }
            }
        }
        return $parents;
    }

    /**
     * 获取所有财务分类
     *
     */
    public function getAllMaterialFinanceCategory()
    {
        return MaterialFinanceCategoryModel::find([
            'columns' => ['id', 'name', 'code', 'ledger_account_id']
        ])->toArray();
    }
}
