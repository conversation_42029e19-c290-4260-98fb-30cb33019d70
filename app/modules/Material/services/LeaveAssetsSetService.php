<?php

namespace App\Modules\Material\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\MaterialLeaveAssetsManagerSetDetailModel;
use App\Models\oa\MaterialLeaveAssetsManagerSetModel;
use App\Models\oa\MaterialLeaveAssetsRemindSetDetailModel;
use App\Models\oa\MaterialLeaveAssetsRemindSetModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;

class LeaveAssetsSetService extends BaseService
{

    //上级确认任务-新增
    public static $validate_manager_add = [
        'rule' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_RULE_VALIDATE,
        'staff_info_id' => 'IfIntEq:rule,' . MaterialEnums::LEAVE_ASSET_SET_RULE_FIXED . '|Required|IntGt:0',
        'is_enable' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_VALIDATE,
        'job_title_set' => 'Arr|ArrLenGeLe:0,150', //目前泰国1660个职位
        'department_ids' => 'Arr|ArrLenGeLe:0,2000', //目前泰国1204个部门
        'department_ids[*].department_id' => 'IntGt:0',
        'department_ids[*].is_include_sub' => 'IntIn:' . SettingEnums::IS_NO_INCLUDE_SUB . ',' . SettingEnums::IS_INCLUDE_SUB,
    ];
    //上级确认任务-编辑
    public static $validate_manager_edit = [
        'id' => 'Required|IntGt:0',
        'rule' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_RULE_VALIDATE,
        'staff_info_id' => 'IfIntEq:rule,' . MaterialEnums::LEAVE_ASSET_SET_RULE_FIXED . '|Required|IntGt:0',
        'is_enable' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_VALIDATE,
        'department_ids' => 'Arr|ArrLenGeLe:0,2000',
        'department_ids[*].department_id' => 'IntGt:0',
        'department_ids[*].is_include_sub' => 'IntIn:' . SettingEnums::IS_NO_INCLUDE_SUB . ',' . SettingEnums::IS_INCLUDE_SUB,
    ];
    //短信邮件设置-新增
    public static $validate_remind_add = [
        'remind_type' => 'Required|Arr|ArrLenGeLe:1,2',
        'remind_type[*]' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_REMIND_TYPE_VALIDATE,
        'job_title_set' => 'Arr|ArrLenGeLe:0,150', //目前泰国1660个职位,一次最多150个
        'department_ids' => 'Arr|ArrLenGeLe:0,2000', //目前泰国1204个部门
        'department_ids[*].department_id' => 'IntGt:0',
        'department_ids[*].is_include_sub' => 'IntIn:' . SettingEnums::IS_NO_INCLUDE_SUB . ',' . SettingEnums::IS_INCLUDE_SUB,
    ];
    //短信邮件设置-编辑
    public static $validate_remind_edit = [
        'id' => 'Required|IntGt:0',
        'remind_type' => 'Required|Arr',
        'remind_type[*]' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_SET_REMIND_TYPE_VALIDATE,
        'department_ids' => 'Arr|ArrLenGeLe:0,2000',
        'department_ids[*].department_id' => 'IntGt:0',
        'department_ids[*].is_include_sub' => 'IntIn:' . SettingEnums::IS_NO_INCLUDE_SUB . ',' . SettingEnums::IS_INCLUDE_SUB,
    ];
    //短信邮件设置-删除
    public static $validate_remind_delete = [
        'id' => 'Required|IntGt:0',
    ];
    /**
     * @var LeaveAssetsSetService
     */
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LeaveAssetsSetService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     * @date 2023/3/15
     */
    public function getDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                //任务规则
                'leave_assets_set_rule_item' => MaterialEnums::$leave_assets_set_rule_list,
                //启用禁用
                'leave_asset_set_is_enable_item' => MaterialEnums::$leave_asset_set_is_enable_list,
                //短信邮件设置类型
                'leave_asset_set_remind_type_item' => MaterialEnums::$leave_asset_set_remind_type_list,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('离职资产设置-枚举获取失败: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 上级确认任务-列表
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getManagerList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getManagerListCount($params, $user);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id, job_title_set, department_id_set, rule, staff_info_id, is_enable';
                $builder->columns($columns);
                $builder->from(MaterialLeaveAssetsManagerSetModel::class);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleManagerListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-set-get-manager-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 上级确认任务-列表-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getManagerListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialLeaveAssetsManagerSetModel::class);
        $builder->columns('count(id) as count');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 上级确认任务-列表数据处理
     * @param $items
     * @return array
     * @date 2023/3/2
     */
    private function handleManagerListItems($items)
    {
        if (empty($items)) {
            return [];
        }
        //取所有的部门和职位去重
        $all_job_title = $all_department_id = [];
        foreach ($items as $v) {
            $job_title = !empty($v['job_title_set']) ? explode(',', $v['job_title_set']) : [];
            $department_id = !empty($v['department_id_set']) ? explode(',', $v['department_id_set']) : [];
            $all_job_title = array_merge($all_job_title, $job_title);
            $all_department_id = array_merge($all_department_id, $department_id);
        }
        $all_job_title = array_values(array_unique($all_job_title));
        $all_department_id = array_values(array_unique($all_department_id));
        //查询职位名称
        $job_data_kv = [];
        if (!empty($all_job_title)) {
            $job_data = HrJobTitleModel::find([
                'columns' => 'id, job_name',
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $all_job_title
                ]
            ])->toArray();
            $job_data_kv = array_column($job_data, 'job_name', 'id');
        }
        //查询部门名称
        $department_data_kv = [];
        if (!empty($all_department_id)) {
            $department_data = SysDepartmentModel::find([
                'columns' => 'id, name',
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $all_department_id
                ]
            ])->toArray();
            $department_data_kv = array_column($department_data, 'name', 'id');
        }
        foreach ($items as &$item) {
            //职位
            $item['job_title_text'] = '';
            if (!empty($item['job_title_set'])) {
                $job_title_set = explode(',', $item['job_title_set']);
                foreach ($job_title_set as $job_id) {
                    if (isset($job_data_kv[$job_id])) {
                        $item['job_title_text'] .= $job_data_kv[$job_id] . ',';
                    }
                }
                $item['job_title_text'] = rtrim($item['job_title_text'], ',');
            }
            //部门
            $item['department_id_text'] = '';
            if (!empty($item['department_id_set'])) {
                $department_id_set = explode(',', $item['department_id_set']);
                foreach ($department_id_set as $department_id) {
                    if (isset($department_data_kv[$department_id])) {
                        $item['department_id_text'] .= $department_data_kv[$department_id] . ',';
                    }
                }
                $item['department_id_text'] = rtrim($item['department_id_text'], ',');
            }
            //规则
            $item['rule_text'] = isset(MaterialEnums::$leave_assets_set_rule_list[$item['rule']]) ? static::$t[MaterialEnums::$leave_assets_set_rule_list[$item['rule']]] : '';
            //是否有效
            $item['is_enable_text'] = isset(MaterialEnums::$leave_asset_set_is_enable_list[$item['is_enable']]) ? static::$t[MaterialEnums::$leave_asset_set_is_enable_list[$item['is_enable']]] : '';
        }
        return $items;
    }

    /**
     * 短信邮件通知-列表
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getRemindList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getRemindListCount($params, $user);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id, job_title_set, department_id_set, remind_type';
                $builder->columns($columns);
                $builder->from(MaterialLeaveAssetsRemindSetModel::class);
                $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleRemindListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-set-get-manager-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 短信邮件通知-列表-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getRemindListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialLeaveAssetsRemindSetModel::class);
        $builder->columns('count(id) as count');
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 短信邮件通知-列表数据处理
     * @param $items
     * @return array
     * @date 2023/3/2
     */
    private function handleRemindListItems($items)
    {
        if (empty($items)) {
            return [];
        }
        //取所有的部门和职位去重
        $all_job_title = $all_department_id = [];
        foreach ($items as $v) {
            $job_title = !empty($v['job_title_set']) ? explode(',', $v['job_title_set']) : [];
            $department_id = !empty($v['department_id_set']) ? explode(',', $v['department_id_set']) : [];
            $all_job_title = array_merge($all_job_title, $job_title);
            $all_department_id = array_merge($all_department_id, $department_id);
        }
        $all_job_title = array_values(array_unique($all_job_title));
        $all_department_id = array_values(array_unique($all_department_id));
        //查询职位名称
        $job_data_kv = [];
        if (!empty($all_job_title)) {
            $job_data = HrJobTitleModel::find([
                'columns' => 'id, job_name',
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $all_job_title
                ]
            ])->toArray();
            $job_data_kv = array_column($job_data, 'job_name', 'id');
        }
        //查询部门名称
        $department_data_kv = [];
        if (!empty($all_department_id)) {
            $department_data = SysDepartmentModel::find([
                'columns' => 'id, name',
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $all_department_id
                ]
            ])->toArray();
            $department_data_kv = array_column($department_data, 'name', 'id');
        }
        foreach ($items as &$item) {
            //职位
            $item['job_title_text'] = '';
            if (!empty($item['job_title_set'])) {
                $job_title_set = explode(',', $item['job_title_set']);
                foreach ($job_title_set as $job_id) {
                    if (isset($job_data_kv[$job_id])) {
                        $item['job_title_text'] .= $job_data_kv[$job_id] . ',';
                    }
                }
                $item['job_title_text'] = rtrim($item['job_title_text'], ',');
            }
            //部门
            $item['department_id_text'] = '';
            if (!empty($item['department_id_set'])) {
                $department_id_set = explode(',', $item['department_id_set']);
                foreach ($department_id_set as $department_id) {
                    if (isset($department_data_kv[$department_id])) {
                        $item['department_id_text'] .= $department_data_kv[$department_id] . ',';
                    }
                }
                $item['department_id_text'] = rtrim($item['department_id_text'], ',');
            }
            //提醒类型
            if (!empty($item['remind_type'])) {
                $remind_type = array_values(explode(',', $item['remind_type']));
                $item['remind_type'] = array_map(function ($v) {
                    return (int)$v;
                }, $remind_type);
            } else {
                $item['remind_type'] = [];
            }
        }
        return $items;
    }

    /**
     * 上级确认任务-添加
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function managerAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //验证当前职位加部门是否存在设置
            $validate_conditions = 'is_enable = :is_enable:';
            $validate_bind = ['is_enable' => MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES];
            $job_titles = $department_ids = [];
            if (!empty($params['job_title_set'])) {
                $job_titles = array_values(array_unique($params['job_title_set']));
                $validate_conditions .= ' and job_title in ({job_titles:array})';
                $validate_bind['job_titles'] = $job_titles;
            } else {
                $validate_conditions .= ' and job_title = 0';
            }
            if (!empty($params['department_ids'])) {
                $department_ids = array_values(array_unique(array_column($params['department_ids'], 'department_id')));
                $validate_conditions .= ' and department_id in ({department_ids:array})';
                $validate_bind['department_ids'] = $department_ids;
            } else {
                $validate_conditions .= ' and department_id = 0';
            }
            $is_exist_set = MaterialLeaveAssetsManagerSetDetailModel::findFirst([
                'columns' => 'manager_set_id',
                'conditions' => $validate_conditions,
                'bind' => $validate_bind
            ]);
            if ($is_exist_set) {
                throw new ValidationException(static::$t->_('leave_asset_manager_set_detail_exist', ['set_id' => $is_exist_set->manager_set_id]), ErrCode::$VALIDATE_ERROR);
            }
            //添加主信息
            $db_set_info = new MaterialLeaveAssetsManagerSetModel();
            if (!empty($job_titles)) {
                $db_set_info->job_title_set = implode(',', $job_titles);
            }
            if (!empty($department_ids)) {
                $db_set_info->department_id_set = implode(',', $department_ids);
            }
            $db_set_info->rule = $params['rule'];
            $db_set_info->staff_info_id = !empty($params['staff_info_id']) ? $params['staff_info_id'] : 0;
            $db_set_info->is_enable = $params['is_enable'];
            $db_set_info->created_id = $user['id'];
            $db_set_info->created_at = $now_date;
            if ($db_set_info->save() === false) {
                throw new BusinessException('离职资产-上级确认任务设置-添加失败, data: ' . json_encode($db_set_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_set_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->managerCreateDetail($job_titles, $params['department_ids'], $db_set_info);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-set-add-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 上级确认任务-详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getManagerInfo($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询本行数据
            $db_set_info = MaterialLeaveAssetsManagerSetModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //验证
            if (!$db_set_info) {
                throw new ValidationException(static::$t->_('leave_asset_manager_set_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $data = $db_set_info->toArray();
            $data['job_title_text'] = '';
            if (!empty($data['job_title_set'])) {
                $job_title_set = explode(',', $data['job_title_set']);
                $job_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_set, false);
                foreach ($job_title_set as $job_id) {
                    if (isset($job_list[$job_id])) {
                        $data['job_title_text'] .= $job_list[$job_id]['job_name'] . ',';
                    }
                }
                $data['job_title_text'] = rtrim($data['job_title_text'], ',');
            }

            $data['department_id_text'] = '';
            $data['department_ids'] = [];
            if (!empty($data['department_id_set'])) {
                $map_department_list = $db_set_info->getDetails()->toArray();
                $department_ids = array_column($map_department_list, 'department_id');
                $department_repository = new DepartmentRepository();
                $department_list = $department_repository->getDepartmentByIds($department_ids, 2);

                // 上级部门信息
                $ancestry_dept_ids = array_column($department_list, 'ancestry', 'id');
                $ancestry_dept_item = $department_repository->getDepartmentByIds(array_values($ancestry_dept_ids), 2);

                // 合并当前部门和上级部门
                $all_dept_item = array_merge($department_list, $ancestry_dept_item);
                $all_dept_item = array_column($all_dept_item, null, 'id');

                foreach ($map_department_list as $item) {
                    if (empty($item['department_id'])) {
                        continue;
                    }
                    $dept_info = $all_dept_item[$item['department_id']] ?? [];
                    // 上级部门/组织
                    $ancestry = $dept_info['ancestry'] ?? '0';
                    $ancestry_name = $all_dept_item[$ancestry]['name'] ?? '';

                    $data['department_ids'][] = [
                        'department_id' => $item['department_id'],
                        'department_name' => $dept_info['name'] ?? '',
                        'is_include_sub' => $item['is_include_sub'],
                        'ancestry' => $ancestry,
                        'ancestry_name' => $ancestry_name,
                    ];
                }
                $data['department_id_text'] = implode(',', array_column($data['department_ids'], 'department_name'));
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-set-get-manager-info-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 上级确认任务-编辑
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function managerEdit($params, $user)
    {
        ini_set('memory_limit', '512M');

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //查询本行数据
            $db_set_info = MaterialLeaveAssetsManagerSetModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //验证
            if (!$db_set_info) {
                throw new ValidationException(static::$t->_('leave_asset_manager_set_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //启用状态变了, 改变详情的启用状态
            $department_ids = !empty($params['department_ids']) ? array_values(array_unique(array_column($params['department_ids'], 'department_id'))) : [];
            if ($params['is_enable'] == MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES) {
                //验证当前职位加部门是否存在设置
                $validate_conditions = 'manager_set_id != :manager_set_id: and is_enable = :is_enable:';
                $validate_bind = [
                    'is_enable' => MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES,
                    'manager_set_id' => $params['id']
                ];
                if (!empty($db_set_info->job_title_set)) {
                    $job_titles = explode(',', $db_set_info->job_title_set);
                    $validate_conditions .= ' and job_title in ({job_titles:array})';
                    $validate_bind['job_titles'] = $job_titles;
                } else {
                    $validate_conditions .= ' and job_title = :job_title:';
                    $validate_bind['job_title'] = 0;
                }
                if (!empty($params['department_ids'])) {
                    $validate_conditions .= ' and department_id in ({department_ids:array})';
                    $validate_bind['department_ids'] = $department_ids;
                } else {
                    $validate_conditions .= ' and department_id = :department_id:';
                    $validate_bind['department_id'] = 0;
                }
                $is_exist_set = MaterialLeaveAssetsManagerSetDetailModel::findFirst([
                    'columns' => 'manager_set_id',
                    'conditions' => $validate_conditions,
                    'bind' => $validate_bind
                ]);
                if ($is_exist_set) {
                    throw new ValidationException(static::$t->_('leave_asset_manager_set_detail_exist', ['set_id' => $is_exist_set->manager_set_id]), ErrCode::$VALIDATE_ERROR);
                }
            }
            $db_set_info->department_id_set = implode(',', $department_ids);
            $db_set_info->rule = $params['rule'];
            $db_set_info->staff_info_id = !empty($params['staff_info_id']) ? $params['staff_info_id'] : 0;
            $db_set_info->is_enable = $params['is_enable'];
            $db_set_info->updated_id = $user['id'];
            $db_set_info->updated_at = $now_date;
            if ($db_set_info->save() === false) {
                throw new BusinessException('离职资产-上级确认任务设置-编辑失败, data: ' . json_encode($db_set_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_set_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->managerUpdateDetail($db_set_info, $params);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-manager-set-edit-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 生成主管配置的详情
     * @param array $job_title_array 职位id组
     * @param array $department_array 部门id，是否包含子部门
     * @param object $manager_set_info 主表对象
     * @return bool
     * @throws BusinessException
     * @date 2023/3/22
     */
    public function managerCreateDetail($job_title_array, $department_array, $manager_set_info)
    {
        //生成设置详情
        $set_detail = [];
        if (!empty($department_array) && !empty($job_title_array)) {
            foreach ($department_array as $department) {
                foreach ($job_title_array as $job_title) {
                    $tmp_info = [];
                    $tmp_info['manager_set_id'] = $manager_set_info->id;
                    $tmp_info['job_title'] = $job_title;
                    $tmp_info['department_id'] = $department['department_id'];
                    $tmp_info['is_include_sub'] = $department['is_include_sub'];
                    $tmp_info['rule'] = $manager_set_info->rule;
                    $tmp_info['staff_info_id'] = $manager_set_info->staff_info_id;
                    $tmp_info['is_enable'] = $manager_set_info->is_enable;
                    $set_detail[] = $tmp_info;
                }
            }
        } elseif (!empty($department_array)) {
            foreach ($department_array as $department) {
                $tmp_info = [];
                $tmp_info['manager_set_id'] = $manager_set_info->id;
                $tmp_info['department_id'] = $department['department_id'];
                $tmp_info['is_include_sub'] = $department['is_include_sub'];
                $tmp_info['rule'] = $manager_set_info->rule;
                $tmp_info['staff_info_id'] = $manager_set_info->staff_info_id;
                $tmp_info['is_enable'] = $manager_set_info->is_enable;
                $set_detail[] = $tmp_info;
            }
        } elseif (!empty($job_title_array)) {
            foreach ($job_title_array as $job_title) {
                $tmp_info = [];
                $tmp_info['manager_set_id'] = $manager_set_info->id;
                $tmp_info['job_title'] = $job_title;
                $tmp_info['rule'] = $manager_set_info->rule;
                $tmp_info['staff_info_id'] = $manager_set_info->staff_info_id;
                $tmp_info['is_enable'] = $manager_set_info->is_enable;
                $set_detail[] = $tmp_info;
            }
        }
        //保存
        $model = new MaterialLeaveAssetsManagerSetDetailModel();
        if ($model->batch_insert($set_detail) === false) {
            throw new BusinessException('离职资产-通用设置-主管配置详情设置-保存失败，data = ' . json_encode($set_detail, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($model), ErrCode::$LEAVE_ASSET_MANAGER_SET_DETAIL_SAVE_ERROR);
        }
        return true;
    }

    /**
     * 更新主管配置详情表
     * 目前职位一次最多150个,
     * @param $db_set_info
     * @param $params
     * @throws BusinessException
     * @date 2023/3/30
     */
    public function managerUpdateDetail($db_set_info, $params)
    {
        $details = $db_set_info->getDetails();
        $bool = $details->delete();
        if ($bool === false) {
            throw new BusinessException('离职资产-通用设置-短信通知配置详情设置-删除原有设置失败，data = ' . json_encode($details->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_MANAGER_SET_DETAIL_UPDATE_ERROR);
        }
        $job_titles = $db_set_info->job_title_set ? explode(',', $db_set_info->job_title_set) : [];
        $this->managerCreateDetail($job_titles, $params['department_ids'], $db_set_info);
    }

    /**
     * 短信邮件通知-添加
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function remindAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //验证当前职位加部门是否存在设置
            $validate_conditions = '';
            $validate_bind = [];
            $job_titles = $department_ids = [];
            if (!empty($params['job_title_set'])) {
                $job_titles = array_values(array_unique($params['job_title_set']));
                $validate_conditions = 'job_title in ({job_titles:array})';
                $validate_bind['job_titles'] = $job_titles;
            } else {
                $validate_conditions = 'job_title = 0';
            }
            if (!empty($params['department_ids'])) {
                $department_ids = array_values(array_unique(array_column($params['department_ids'], 'department_id')));
                $validate_conditions .= ' and department_id in ({department_ids:array})';
                $validate_bind['department_ids'] = $department_ids;
            } else {
                $validate_conditions .= ' and department_id = 0';
            }
            $is_exist_set = MaterialLeaveAssetsRemindSetDetailModel::findFirst([
                'columns' => 'remind_set_id',
                'conditions' => $validate_conditions,
                'bind' => $validate_bind
            ]);
            if ($is_exist_set) {
                throw new ValidationException(static::$t->_('leave_asset_manager_set_detail_exist', ['set_id' => $is_exist_set->remind_set_id]), ErrCode::$VALIDATE_ERROR);
            }
            //添加主信息
            $db_set_info = new MaterialLeaveAssetsRemindSetModel();
            if (!empty($job_titles)) {
                $db_set_info->job_title_set = implode(',', $job_titles);
            }
            if (!empty($department_ids)) {
                $db_set_info->department_id_set = implode(',', $department_ids);
            }
            //处理消息类型,多选
            $remind_type = array_values(array_unique($params['remind_type']));
            sort($remind_type);
            $remind_type = implode(',', $remind_type);
            //添加
            $db_set_info->remind_type = $remind_type;
            $db_set_info->created_id = $user['id'];
            $db_set_info->created_at = $now_date;
            if ($db_set_info->save() === false) {
                throw new BusinessException('离职资产-短信邮件通知设置-添加失败, data: ' . json_encode($db_set_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_set_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->remindCreateDetail($db_set_info->id, $job_titles, $params['department_ids'], $remind_type);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-remind-set-add-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 短信邮件设置-详情
     * @param array $params 请求参数组
     * @return array
     */
    public function getRemindInfo($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询本行数据
            $db_set_info = MaterialLeaveAssetsRemindSetModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //验证
            if (!$db_set_info) {
                throw new ValidationException(static::$t->_('leave_asset_remind_set_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $data = $db_set_info->toArray();
            $data['remind_type'] = explode(',', $data['remind_type']);
            $data['job_title_text'] = '';
            if (!empty($data['job_title_set'])) {
                $job_title_set = explode(',', $data['job_title_set']);
                $job_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_set, false);
                foreach ($job_title_set as $job_id) {
                    if (isset($job_list[$job_id])) {
                        $data['job_title_text'] .= $job_list[$job_id]['job_name'] . ',';
                    }
                }
                $data['job_title_text'] = rtrim($data['job_title_text'], ',');
            }

            $data['department_id_text'] = '';
            $data['department_ids'] = [];
            if (!empty($data['department_id_set'])) {
                $map_department_list = $db_set_info->getDetails()->toArray();
                $department_ids = array_column($map_department_list, 'department_id');
                $department_repository = new DepartmentRepository();
                $department_list = $department_repository->getDepartmentByIds($department_ids, 2);

                // 上级部门信息
                $ancestry_dept_ids = array_column($department_list, 'ancestry', 'id');
                $ancestry_dept_item = $department_repository->getDepartmentByIds(array_values($ancestry_dept_ids), 2);

                // 合并当前部门和上级部门
                $all_dept_item = array_merge($department_list, $ancestry_dept_item);
                $all_dept_item = array_column($all_dept_item, null, 'id');

                foreach ($map_department_list as $item) {
                    if (empty($item['department_id'])) {
                        continue;
                    }
                    $dept_info = $all_dept_item[$item['department_id']] ?? [];
                    // 上级部门/组织
                    $ancestry = $dept_info['ancestry'] ?? '0';
                    $ancestry_name = $all_dept_item[$ancestry]['name'] ?? '';

                    if (!isset($department_arr[$item['department_id']])) {
                        $department_arr[$item['department_id']] = [
                            'department_id' => $item['department_id'],
                            'department_name' => $dept_info['name'] ?? '',
                            'is_include_sub' => $item['is_include_sub'],
                            'ancestry' => $ancestry,
                            'ancestry_name' => $ancestry_name,
                        ];
                        $data['department_ids'][] = $department_arr[$item['department_id']];
                    }
                }
                $data['department_id_text'] = implode(',', array_column($data['department_ids'], 'department_name'));
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-set-get-remind-info-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 短信邮件通知-编辑
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function remindEdit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询本行数据
            $db_set_info = MaterialLeaveAssetsRemindSetModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //验证
            if (!$db_set_info) {
                throw new ValidationException(static::$t->_('leave_asset_remind_set_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            //验证当前职位加部门是否存在设置
            $validate_conditions = 'remind_set_id != :remind_set_id:';
            $validate_bind = ['remind_set_id' => $params['id']];
            if (!empty($db_set_info->job_title_set)) {
                $job_titles = explode(',', $db_set_info->job_title_set);
                $validate_conditions .= ' and job_title in ({job_titles:array})';
                $validate_bind['job_titles'] = $job_titles;
            } else {
                $validate_conditions .= ' and job_title = :job_title:';
                $validate_bind['job_title'] = 0;
            }
            if (!empty($params['department_ids'])) {
                $department_ids = array_values(array_unique(array_column($params['department_ids'], 'department_id')));
                $validate_conditions .= ' and department_id in ({department_ids:array})';
                $validate_bind['department_ids'] = $department_ids;
            } else {
                $validate_conditions .= ' and department_id = :department_id:';
                $validate_bind['department_id'] = 0;
            }
            $is_exist_set = MaterialLeaveAssetsRemindSetDetailModel::findFirst([
                'columns' => 'remind_set_id',
                'conditions' => $validate_conditions,
                'bind' => $validate_bind
            ]);
            if ($is_exist_set) {
                throw new ValidationException(static::$t->_('leave_asset_manager_set_detail_exist', ['set_id' => $is_exist_set->remind_set_id]), ErrCode::$VALIDATE_ERROR);
            }

            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');

            //处理消息类型,多选
            $remind_type = array_values(array_unique($params['remind_type']));
            sort($remind_type);

            if (!empty($department_ids)) {
                $db_set_info->department_id_set = implode(',', $department_ids);
            }

            //更新
            $db_set_info->remind_type = implode(',', $remind_type);
            $db_set_info->updated_id = $user['id'];
            $db_set_info->updated_at = $now_date;
            if ($db_set_info->save() === false) {
                throw new BusinessException('离职资产-短信邮件设置-编辑失败, data: ' . json_encode($db_set_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_set_info), ErrCode::$BUSINESS_ERROR);
            }

            //处理详情表
            $this->remindUpdateDetail($db_set_info, $params);
            $db->commit();

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-remind-set-edit-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 短信邮件通知-删除
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function remindDelete($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //查询本行数据
            $db_set_info = MaterialLeaveAssetsRemindSetModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //验证
            if (!$db_set_info) {
                throw new ValidationException(static::$t->_('leave_asset_remind_set_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $db_set_info->is_deleted = GlobalEnums::IS_DELETED;
            $db_set_info->updated_id = $user['id'];
            $db_set_info->updated_at = $now_date;
            if ($db_set_info->save() === false) {
                throw new BusinessException('离职资产-短信邮件设置-删除失败, data: ' . json_encode($db_set_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_set_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->remindDeleteDetail($db_set_info->id);
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-remind-set-delete-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 生成主管配置的详情
     * 目前泰国职位1660 部门1204 最大数据量1660*1204 + 1660 + 1204 = 2,001,504
     * 一次职位最多150个, 部门1204 单次最大添加量 1204*150 = 180,600
     * @param integer $remind_set_id 主表id
     * @param array $job_title_array 职位id组
     * @param array $department_array 部门id、是否包含子部门
     * @param integer $remind_type 消息类型
     * @return bool
     * @throws BusinessException
     * @date 2023/3/22
     */
    public function remindCreateDetail($remind_set_id, $job_title_array, $department_array, $remind_type)
    {
        //生成设置详情
        $set_detail = [];
        if (!empty($department_array) && !empty($job_title_array)) {
            foreach ($department_array as $department) {
                foreach ($job_title_array as $job_title) {
                    $tmp_info = [];
                    $tmp_info['remind_set_id'] = $remind_set_id;
                    $tmp_info['job_title'] = $job_title;
                    $tmp_info['department_id'] = $department['department_id'];
                    $tmp_info['is_include_sub'] = $department['is_include_sub'];
                    $tmp_info['remind_type'] = $remind_type;
                    $set_detail[] = $tmp_info;
                }
            }
        } elseif (!empty($department_array)) {
            foreach ($department_array as $department) {
                $tmp_info = [];
                $tmp_info['remind_set_id'] = $remind_set_id;
                $tmp_info['department_id'] = $department['department_id'];
                $tmp_info['is_include_sub'] = $department['is_include_sub'];
                $tmp_info['remind_type'] = $remind_type;
                $set_detail[] = $tmp_info;
            }
        } elseif (!empty($job_title_array)) {
            foreach ($job_title_array as $job_title) {
                $tmp_info = [];
                $tmp_info['remind_set_id'] = $remind_set_id;
                $tmp_info['job_title'] = $job_title;
                $tmp_info['remind_type'] = $remind_type;
                $set_detail[] = $tmp_info;
            }
        }
        //保存
        $model = new MaterialLeaveAssetsRemindSetDetailModel();
        if ($model->batch_insert($set_detail) === false) {
            $log_data = ['remind_set_id' => $remind_set_id, 'job_title' => $job_title_array, 'department_id' => $department_array, 'remind_type' => $remind_type];
            throw new BusinessException('离职资产-通用设置-短信通知配置详情设置-保存失败，data = ' . json_encode($log_data, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($model), ErrCode::$LEAVE_ASSET_REMIND_SET_DETAIL_SAVE_ERROR);
        }
        return true;
    }

    /**
     * 更新详情表
     * 目前职位一次最多150个,
     * @param $db_set_info
     * @param $params
     * @return bool
     * @throws BusinessException
     * @date 2023/3/30
     */
    public function remindUpdateDetail($db_set_info, $params)
    {
        $details = $db_set_info->getDetails();
        $bool = $details->delete();
        if ($bool === false) {
            throw new BusinessException('离职资产-通用设置-短信通知配置详情设置-删除原有设置失败，data = ' . json_encode($details->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_MANAGER_SET_DETAIL_UPDATE_ERROR);
        }
        $job_titles = $db_set_info->job_title_set ? explode(',', $db_set_info->job_title_set) : [];
        $this->remindCreateDetail($db_set_info->id, $job_titles, $params['department_ids'], $db_set_info->remind_type);
    }

    /**
     * 删除详情表
     * @param $remind_set_id
     * @return bool
     * @throws BusinessException
     * @date 2023/3/30
     */
    public function remindDeleteDetail($remind_set_id)
    {
        //没id坚决不能执行
        if (empty($remind_set_id)) {
            return false;
        }
        $db = $this->getDI()->get('db_oa');
        $success = $db->delete(
            (new MaterialLeaveAssetsRemindSetDetailModel())->getSource(),
            'remind_set_id = ?',
            [$remind_set_id]
        );
        if (!$success) {
            throw new BusinessException('离职资产-通用设置-短信通知配置详情设置-删除失败，remind_set_id = ' . $remind_set_id, ErrCode::$LEAVE_ASSET_REMIND_SET_DETAIL_DELETE_ERROR);
        }
        return true;
    }

    /**
     * 根据设置的上级规则寻找上级
     * @param int $staff_id 离职员工
     * @param int $job_title 职位
     * @param int $department_id 部门
     * @param int $work_handover 工作交接人,只有离职申请进来的才有
     * @return array
     * @date 2023/3/21
     */
    public function getManagerSetByStaff($staff_id, $job_title, $department_id, $work_handover = 0)
    {
        $manager_set = [];
        // 查询职位+部门有没有规则存在
        $manager_list_obj = MaterialLeaveAssetsManagerSetDetailModel::find([
            'conditions' => 'job_title = :job_title: and department_id != :department_id: and is_enable = :is_enable:',
            'bind' => [
                'job_title' => $job_title,
                'department_id' => 0,
                'is_enable' => MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES,
            ],
            'order' => 'id desc'
        ]);
        $department_service = new DepartmentService();
        if ($manager_list_obj->toArray()) {
            foreach ($manager_list_obj as $item) {
                $department_ids[] = $item->department_id;
                if ($item->is_include_sub == SettingEnums::IS_INCLUDE_SUB) {
                    //找部门的时候，需要注意查找基础配置里配置的部门勾选了子部门包含了员工所属部门的数据
                    $department_child_ids = $department_service->getChildrenListByDepartmentIdV2($item->department_id, true);
                    $department_ids = array_merge($department_ids, $department_child_ids);
                }
                if (in_array($department_id, $department_ids)) {
                    $manager_set = $item;
                    break;
                }
            }
        }

        // 单独的职位规则
        if (empty($manager_set)) {
            $manager_set = MaterialLeaveAssetsManagerSetDetailModel::findFirst([
                'conditions' => 'job_title = :job_title: and department_id = :department_id: and is_enable = :is_enable:',
                'bind' => [
                    'job_title' => $job_title,
                    'department_id' => 0,
                    'is_enable' => MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES,
                ],
                'order' => 'id desc'
            ]);

            //单独的部门规则
            if (empty($manager_set)) {
                $manager_list_obj = MaterialLeaveAssetsManagerSetDetailModel::find([
                    'conditions' => 'job_title = :job_title: and department_id != :department_id: and is_enable = :is_enable:',
                    'bind' => [
                        'job_title' => 0,
                        'department_id' => 0,
                        'is_enable' => MaterialEnums::LEAVE_ASSET_SET_IS_ENABLE_YES,
                    ],
                    'order' => 'id desc'
                ]);
                if ($manager_list_obj->toArray()) {
                    foreach ($manager_list_obj as $item) {
                        $department_ids[] = $item->department_id;
                        if ($item->is_include_sub == SettingEnums::IS_INCLUDE_SUB) {
                            //找部门的时候，需要注意查找基础配置里配置的部门勾选了子部门包含了员工所属部门的数据
                            $department_child_ids = $department_service->getChildrenListByDepartmentIdV2($item->department_id, true);
                            $department_ids = array_merge($department_ids, $department_child_ids);
                        }
                        if (in_array($department_id, $department_ids)) {
                            $manager_set = $item;
                            break;
                        }
                    }
                }
            }
        }

        // 命中规则
        $manager_id = 0;
        if (!empty($manager_set)) {
            //解析规则
            switch ($manager_set->rule) {
                case MaterialEnums::LEAVE_ASSET_SET_RULE_FIXED:
                    //固定工号
                    $manager_id = $manager_set->staff_info_id;
                    break;
                case MaterialEnums::LEAVE_ASSET_SET_RULE_SUPERIOR:
                    //直线上级
                    $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
                    $manager_id = $staff_info['manger'] ?? 0;
                    break;
                case MaterialEnums::LEAVE_ASSET_SET_RULE_STORE_MANAGER:
                    //网点主管
                    $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
                    if (!empty($staff_info['sys_store_id']) && $staff_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                        //查网点负责人
                        $store_info = (new StoreRepository())->getStoreDetail($staff_info['sys_store_id']);
                        $manager_id = $store_info['manager_id'] ?? 0;
                    }
                    break;
                case MaterialEnums::LEAVE_ASSET_SET_RULE_PIECE_MANAGER:
                    //片区负责人
                    $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
                    if (!empty($staff_info['sys_store_id']) && $staff_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                        //查网点信息
                        $store_info = (new StoreRepository())->getStoreDetail($staff_info['sys_store_id']);
                        if ($store_info && !empty($store_info['manage_piece'])) {
                            //查片区
                            $piece_info = SysManagePieceModel::findFirst([
                                'conditions' => 'id = :id:',
                                'bind' => ['id' => $store_info['manage_piece']]
                            ]);
                            if ($piece_info && !empty($piece_info->manager_id)) {
                                $manager_id = $piece_info->manager_id;
                            }
                        }
                    }
                    break;
                case MaterialEnums::LEAVE_ASSET_SET_RULE_REGION_MANAGER:
                    //大区负责人
                    $staff_info = (new HrStaffRepository())->getStaffById($staff_id);
                    if (!empty($staff_info['sys_store_id']) && $staff_info['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                        //查网点信息
                        $store_info = (new StoreRepository())->getStoreDetail($staff_info['sys_store_id']);
                        if ($store_info && !empty($store_info['manage_region'])) {
                            //查片区
                            $region_info = SysManageRegionModel::findFirst([
                                'conditions' => 'id = :id:',
                                'bind' => ['id' => $store_info['manage_region']]
                            ]);
                            if ($region_info && !empty($region_info->manager_id)) {
                                $manager_id = $region_info->manager_id;
                            }
                        }
                    }
                    break;
                case MaterialEnums::LEAVE_ASSET_SET_RULE_HANDOVER_STAFF:
                    //工作交接人
                    $manager_id = $work_handover;
                    break;
                default:
                    $manager_id = 0;
                    break;
            }
        }

        $log_manager_set = $manager_set ? $manager_set->toArray() : [];
        $this->logger->info('离职创建资产: 离职员工:' . $staff_id . '; 职位:' . $job_title . '; 部门:' . $department_id . '; 命中的规则是:' . json_encode($log_manager_set, JSON_UNESCAPED_UNICODE) . '; 找到的处理人是:' . $manager_id);
        //判断主管在职状态
        if (!empty($manager_id)) {
            $manager_info = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $manager_id,
                ]
            ]);
            $manager_info = $manager_info ? $manager_info->toArray() : [];
            // 不符合员工条件, 在职,非待离职,编制,非子账号,才允许
            if (!($manager_info['state'] == StaffInfoEnums::STAFF_STATE_IN
                && $manager_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO
                && $manager_info['formal'] == StaffInfoEnums::FORMAL_IN
                && $manager_info['is_sub_staff'] == StaffInfoEnums::IS_SUB_STAFF_NO)) {
                $manager_id = 0;
            }
        }
        return ['manager_id' => $manager_id, 'rule' => $manager_set->rule ?? 0];
    }

    /**
     * 根据设置的短信消息规则,找到员工消息类型
     * @param int $job_title 职位
     * @param int $department_id 部门
     * @param $staff_id
     * @return array
     * @date 2023/3/21
     */
    public function getRemindSetByStaff($job_title, $department_id, $staff_id)
    {
        $manager_set = [];
        // 查询职位+部门有没有规则存在
        $manager_list_obj = MaterialLeaveAssetsRemindSetDetailModel::find([
            'conditions' => 'job_title = :job_title: and department_id != :department_id:',
            'bind' => [
                'job_title' => $job_title,
                'department_id' => 0,
            ],
            'order' => 'id desc'
        ]);
        $department_service = new DepartmentService();
        if ($manager_list_obj->toArray()) {
            foreach ($manager_list_obj as $item) {
                $department_ids[] = $item->department_id;
                if ($item->is_include_sub == SettingEnums::IS_INCLUDE_SUB) {
                    //找部门的时候，需要注意查找基础配置里配置的部门勾选了子部门包含了员工所属部门的数据
                    $department_child_ids = $department_service->getChildrenListByDepartmentIdV2($item->department_id, true);
                    $department_ids = array_merge($department_ids, $department_child_ids);
                }
                if (in_array($department_id, $department_ids)) {
                    $manager_set = $item;
                    break;
                }
            }
        }

        // 单独的职位规则
        if (empty($manager_set)) {
            $manager_set = MaterialLeaveAssetsRemindSetDetailModel::findFirst([
                'conditions' => 'job_title = :job_title: and department_id = :department_id:',
                'bind' => [
                    'job_title' => $job_title,
                    'department_id' => 0,
                ],
                'order' => 'id desc'
            ]);
            //单独的部门规则
            if (empty($manager_set)) {
                $manager_list_obj = MaterialLeaveAssetsRemindSetDetailModel::find([
                    'conditions' => 'job_title = :job_title: and department_id != :department_id:',
                    'bind' => [
                        'job_title' => 0,
                        'department_id' => 0,
                    ],
                    'order' => 'id desc'
                ]);
                if ($manager_list_obj->toArray()) {
                    foreach ($manager_list_obj as $item) {
                        $department_ids[] = $item->department_id;
                        if ($item->is_include_sub == SettingEnums::IS_INCLUDE_SUB) {
                            //找部门的时候，需要注意查找基础配置里配置的部门勾选了子部门包含了员工所属部门的数据
                            $department_child_ids = $department_service->getChildrenListByDepartmentIdV2($item->department_id, true);
                            $department_ids = array_merge($department_ids, $department_child_ids);
                        }
                        if (in_array($department_id, $department_ids)) {
                            $manager_set = $item;
                            break;
                        }
                    }
                }
            }
        }
        $log_manager_set = !empty($manager_set) ? json_encode($manager_set->toArray(), JSON_UNESCAPED_UNICODE) : '';
        $this->logger->info('离职创建资产: 离职员工:' . $staff_id . '; 职位:' . $job_title . '; 部门:' . $department_id . '; 命中的规则是:' . $log_manager_set);
        // 命中规则
        $remind_type = [];
        if (!empty($manager_set) && !empty($manager_set->remind_type)) {
            $remind_type = explode(',', $manager_set->remind_type);
        }
        return $remind_type;
    }
}
