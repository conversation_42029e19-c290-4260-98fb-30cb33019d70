<?php
namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MaterialAssetReturnAssetHandlingStatusModel;
use App\Models\oa\MaterialAssetReturnModel;
use App\Models\oa\MaterialAssetReturnRecordModel;
use App\Models\oa\MaterialAssetReturnStorageModel;
use App\Models\oa\MaterialAssetReturnTransferLogModel;
use App\Models\oa\MaterialAssetReturnStorageProductModel;
use App\Models\oa\MaterialSetReturnRepairGroupModel;
use App\Models\oa\MaterialSetReturnRepairMapBarcodeModel;
use App\Models\oa\MaterialSetReturnRepairMapCategoryModel;
use App\Models\oa\MaterialSetReturnRepairMapDepartmentModel;
use App\Models\oa\MaterialSetReturnRepairMapModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialScmAssetUnmatchedModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\User\Services\UserService;
use App\Repository\DepartmentRepository;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialSetReturnGroupRepository;
use App\Repository\oa\MaterialSetReturnTypeRepository;
use App\Repository\StoreRepository;
use App\Library\Enums\DownloadCenterEnum;
use App\Util\RedisKey;

class AssetReturnService extends BaseService
{
    const MARK_MAX_LENGTH = 500;//资产退回各备注字段最大长度
    const LIST_TYPE_WAIT = 1;//退回资产处理-待处理-列表
    const LIST_TYPE_TRANSFER = 2;//退回资产处理-已转交-列表
    const LIST_TYPE_DONE = 3;//退回资产处理-已处理-列表
    const LIST_TYPE_STORAGE = 4;//退回资产处理-退回入库-列表
    //导出类型与列表类型映射关系
    public static $list_type = [
        DownloadCenterEnum::MATERIAL_ASSET_RETURN_WAIT_EXPORT => self::LIST_TYPE_WAIT,
        DownloadCenterEnum::MATERIAL_ASSET_RETURN_TRANSFER_EXPORT => self::LIST_TYPE_TRANSFER,
        DownloadCenterEnum::MATERIAL_ASSET_RETURN_DONE_EXPORT => self::LIST_TYPE_DONE,
    ];

    const BY_LIST_TYPE_WAIT = 1;//BY端-资产管理-资产退回申请-待接收-列表
    const BY_LIST_TYPE_ING = 2;//BY端-资产管理-资产退回申请-处理中-列表
    const BY_LIST_TYPE_DONE = 3;//BY端-资产管理-资产退回申请-已完成-列表

    private static $instance;

    /**
     * 单例
     * @return AssetReturnService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 资产退回
     * 非必需的参数
     * @var array
     */
    public static $return_not_must_params = [
        'pageSize',
        'pageNum',
        'flag',
        'apply_id',
        'company_id',
        'node_department_id',
        'asset_handling_status',
        'apply_date_start',
        'apply_date_end',
        'type_id',
        'group_id',
        'now_group_id',
        'barcode',
        'asset_code',
        'old_asset_code',
        'status'
    ];

    /**
     * BY端-我的资产-退回（批量）
     * 参数验证
     * @var array
     */
    public static $return_validate = [
        'type_id' => 'Required|IntGt:0',//退回类型
        'return_method' => 'Required|IntIn:' . MaterialEnums::RETURN_METHOD_VALIDATE, //退回方式
        'express_no' => 'IfIntEq:return_method,' . MaterialEnums::RETURN_METHOD_POST . '|Required|StrLenGeLe:1,50', //快递单号, 如果退回方式为"邮寄",必填
        'return_reason' =>'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//退回原因
        'attachments' => 'ArrLenGeLe:0,5',//附件信息
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,50',//资产台账id集合
        'asset_ids[*]' => 'IntGt:0',//资产台账id集合
    ];

    /**
     * BY端-我的资产-退回撤销（批量）
     * 参数验证
     * @var array
     */
    public static $cancel_by_validate = [
        'asset_ids' => 'Required|Arr|ArrLenGeLe:1,50',//资产台账id集合
        'asset_ids[*]' => 'IntGt:0',//资产台账id集合
        'remark' => 'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//撤销原因
    ];

    /**
     * BY端-退回资产申请-退回撤销（批量）
     * 参数验证
     * @var array
     */
    public static $cancel_validate = [
        'return_ids' => 'Required|Arr|ArrLenGeLe:1,50',//退回记录id集合
        'return_ids[*]' => 'IntGt:0',//转移记录id集合
        'remark' => 'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//撤销原因
    ];

    /**
     * BY端-退回列表 - （我的）
     * 参数验证
     * @var array
     */
    public static $by_return_list_validate = [
        'name' => 'StrLenGeLe:0,100',//资产名称
        'sn_code' => 'StrLenGeLe:0,50',//sn码
        'asset_code' => 'StrLenGeLe:0,100',//资产码
        'flag' => 'IntIn:' . self::BY_LIST_TYPE_WAIT . ',' . self::BY_LIST_TYPE_ING . ',' . self::BY_LIST_TYPE_DONE,//列表类型
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    /**
     * 退回申请单ID/退回入库单ID
     * 参数验证
     * @var array
     */
    public static $id_validate = [
        'id' => 'Required|IntGt:0'
    ];

    /**
     * 退回资产处理-列表
     * 参数验证
     * @var array
     */
    public static $return_list_validate = [
        'apply_id' => 'Arr',//申请人工号组
        'apply_id[*]' => 'IntGt:0',//申请人工号
        'company_id' => 'IntGt:0',//申请人所属公司ID
        'node_department_id' => 'IntGt:0',//申请人所属部门ID
        'asset_handling_status[*]' => 'IntGt:0',//资产处理进度
        'sys_store_id' => 'StrLenGeLe:0,10',//申请人所属网点
        'apply_date_start' => 'Date',//申请日期-起始
        'apply_date_end' => 'Date',//申请日期-截止
        'type_id' => 'IntGt:0',//退回类型
        'group_id' => 'IntGt:0',//主管部门
        'now_group_id' => 'IntGt:0',//现处理部门
        'name' => 'StrLenGeLe:0,100',//资产名称
        'sn_code' => 'StrLenGeLe:0,50',//sn码
        'asset_code' => 'Arr',//资产码
        'asset_code[*]' => 'StrLenGeLe:0,100',//资产码
        'old_asset_code' => 'Arr',//资产码
        'old_asset_code[*]' => 'StrLenGeLe:0,50',//旧资产码
        'barcode' => 'Arr',//barcode组
        'barcode[*]' => 'StrLenGeLe:0,30',//barcode
        'express_no' => 'StrLenGeLe:0,50',//快递单号
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    /**
     * 批操作ID组 - 批量接收、完成工单、创建出库单初始化信息
     * 参数验证
     * @var array
     */
    public static $batch_id_validate = [
        'ids' => 'Required|Arr|ArrLenGe:1',//退回记录id集合
        'ids[*]' => 'IntGt:0',//id
    ];

    /**
     * 批量拒收
     * 参数验证
     * @var array
     */
    public static $batch_reject_validate = [
        'ids' => 'Required|Arr|ArrLenGe:1',//退回记录id集合
        'ids[*]' => 'IntGt:0',//id
        'remark' => 'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//拒绝原因
    ];

    /**
     * 转交
     * 参数验证
     * @var array
     */
    public static $transfer_validate = [
        'id' => 'Required|IntGt:0',
        'group_id' => 'Required|IntGt:0',
        'transfer_reason' => 'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//转交原因
    ];

    /**
     * 处理
     * 参数验证
     * @var array
     */
    public static $asset_handle_validate = [
        'id' => 'Required|IntGt:0',
        'asset_handling_status' => 'Required|IntGt:0',//资产处理进度
        'asset_handling_remark' => 'StrLenGeLe:0,' . self::MARK_MAX_LENGTH,//资产处理备注
        'attachments' => 'ArrLenGeLe:0,5',//附件信息
    ];

    /**
     * 退货入库单-列表
     * 参数验证
     * @var array
     */
    public static $return_storage_list_validate = [
        'no' => 'StrLenGeLe:0,20',//申请人所属网点
        'scm_no' => 'StrLenGeLe:0,255',//SCM入库单号
        'status' => 'IntIn:' . MaterialEnums::RETURN_STORAGE_STATUS_VALIDATE,//状态
        'sn_code' => 'StrLenGeLe:0,50',//sn码
        'asset_code' => 'Arr',//资产码
        'asset_code[*]' => 'StrLenGeLe:0,100',//资产码
        'old_asset_code' => 'Arr',//资产码
        'old_asset_code[*]' => 'StrLenGeLe:0,50',//旧资产码
        'barcode' => 'Arr',//barcode组
        'barcode[*]' => 'StrLenGeLe:0,30',//barcode
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    /**
     * 创建scm退库单
     * 参数验证
     * @var array
     */
    public static $storage_validate = [
        'apply_date' => 'Required|Date',
        'no' => 'Required|StrLenGeLe:10,20',
        'apply_id' => 'Required|IntGt:0',
        'apply_name' => 'Required|StrLenGeLe:1,50',
        'company_id' => 'IntGe:0',
        'company_name' => 'StrLenGeLe:0,50',
        'node_department_id' => 'Required|IntGt:0',
        'node_department_name' => 'Required|StrLenGeLe:1,50',
        'sys_store_id' => 'Required|StrLenGeLe:1,10',
        'store_name' => 'Required|StrLenGeLe:1,50',
        'mach_code' => 'StrLenGeLe:0,128',
        'mach_name' => 'StrLenGeLe:0,128',
        'remark' => 'StrLenGeLe:0,' . self::MARK_MAX_LENGTH,//备注
        'quality_status' => 'Required|IntIn:' . MaterialEnums::QUALITY_STATUS_VALIDATE,//正品/残品
        'delivery_number' => 'StrLenGeLe:0,30',//运单号
        'return_ids' => 'Required|Arr|ArrLenGe:1',//退回记录id集合
        'return_ids[*]' => 'IntGt:0',//id
    ];

    /**
     * 退货入库通知单-查看-退库入库单信息
     * 参数验证
     * @var array
     */
    public static $storage_detail_product_validate = [
        'id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    /**
     * 撤销scm退库单
     * 参数验证
     * @var array
     */
    public static $cancel_storage_validate = [
        'id' => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,' . self::MARK_MAX_LENGTH,//撤销原因
    ];

    /**
     * 获取创建scm退库单验证规则
     * @param array $params 请求参数组
     * @return array
     */
    public function getStorageValidate($params)
    {
        $validate = self::$storage_validate;
        if (isset($params['mach_code']) && !empty($params['mach_code'])) {
            $validate['stock_id'] = 'Required|StrLenGeLe:1,10';
            $validate['stock_name'] = 'Required|StrLenGeLe:1,255';
        }
        return $validate;
    }

    /**
     * 资产退回处理-枚举
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            //处理进度
            $data['asset_handling_status'] = $this->getAssetHandlingStatus(true, ['conditions' => 'is_deleted = :is_deleted:', 'bind' => ['is_deleted' => GlobalEnums::IS_NO_DELETED]]);
            //资产处理时-处理进度
            $data['asset_handling_status_search'] = $this->getAssetHandlingStatus(true, ['conditions' => 'is_search = :is_search: and is_deleted = :is_deleted:', 'bind' => ['is_search' => MaterialEnums::RETURN_HANDLING_STATUS_SEARCH_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]]);
            //正品/残品 / 退回入库状态
            $enums = [
                'quality_status' => MaterialEnums::$quality_status,
                'return_storage_status' => MaterialEnums::$return_storage_status
            ];
            foreach ($enums as $key => $item) {
                foreach ($item as $k=>$v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v),
                    ];
                }
            }

            //获取货主列表
            $data['cargo_owner'] = $this->getScmCargoOwner();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取资产退回处理-枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取资产退回处理进度
     * @param bool $web_format 是否返回前端格式  true: [["value": "1", "label": "待接收"]]   false: ["1":"translation_code"]
     * @param array $conditions 查询条件组
     * @return array
     */
    public function getAssetHandlingStatus($web_format = true, $conditions = [])
    {
        if ($conditions) {
            $list = MaterialAssetReturnAssetHandlingStatusModel::find($conditions)->toArray();
        } else {
            $list = MaterialAssetReturnAssetHandlingStatusModel::find()->toArray();
        }
        if ($web_format) {
            $enum = [];
            foreach ($list as $value) {
                $enum[] = [
                    'value' => $value['id'],
                    'label' => static::$t->_($value['translation_code'])
                ];
            }
            return $enum;
        } else {
            return array_column($list, null, 'id');
        }
    }

    /**
     * 验证资产-批操作 退回申请、批量拒收、批量撤销、创建入库、撤销入库、完成工单
     * @param array $asset_ids 资产ID组
     * @return mixed
     * @throws ValidationException
     */
    private function validateAssets($asset_ids)
    {
        $assets = MaterialAssetsModel::find([
            'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
            'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        $asset_list = $assets->toArray();
        if (empty($asset_list) || count($asset_list) != count($asset_ids)) {
            throw new ValidationException(static::$t->_('asset_transfer_ids_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $assets;
    }

    /**
     * 验证退回申请单-批操作
     * @param array $return_ids 资产退回单ID组
     * @param integer $type 批量收货、批量拒收、批量撤销、创建SCM退库单、完成工单
     * @return mixed
     * @throws ValidationException
     */
    private function validateAssetReturn($return_ids, $type =  MaterialEnums::RETURN_ASSETS_RECORD_TYPE_RECEIPT)
    {
        //原因提示翻译组
        $error = [
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_RECEIPT => 'material_asset_return_receipt_ids_error',//批量收货
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_REJECT => 'material_asset_return_reject_ids_error',//批量拒收
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_CANCEL => 'material_asset_return_cancel_ids_error',//批量撤销
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE => 'material_asset_return_storage_create_ids_error',//创建SCM退库单
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL => 'material_asset_return_storage_cancel_ids_error',//
            MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH => 'material_asset_return_finished_ids_error',//完成工单
        ];
        //查询单据状态
        $status = in_array($type, [MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL]) ? MaterialEnums::RETURN_STATUS_RECEIVED : MaterialEnums::RETURN_STATUS_UNRECEIVED;
        $return_list = MaterialAssetReturnModel::find([
            'conditions' => 'id in ({return_ids:array}) and status = :status: and is_deleted = :is_deleted:',
            'bind' => ['return_ids' => $return_ids, 'status' => $status, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        if (empty($return_list) || count($return_list) != count($return_ids)) {
            throw new ValidationException(static::$t->_($error[$type]), ErrCode::$VALIDATE_ERROR);
        }

        return $return_list;
    }

    /**
     * 验证批量撤销、拒收资产合理性，并按照资产闲置、使用中状态讲资产分组
     * @param array $return_list 退回申请单列表
     * @return array
     * @throws ValidationException
     */
    private function validateCancelOrRejectAssets($return_list)
    {
        $asset_ids = array_values(array_column($return_list, 'asset_id'));
        $assets = $this->validateAssets($asset_ids);
        $asset_list = $assets->toArray();

        //按照原使用状态分组资产编码、资产ID
        $asset_status_codes = [];
        $return_list = array_column($return_list, null, 'asset_id');
        foreach ($asset_list as $asset) {
            // 必须是退回中
            if ($asset['status'] != MaterialEnums::ASSET_STATUS_RETURN_ING) {
                throw new ValidationException(static::$t->_('material_asset_return_by_status_not_returning') . ':' . $asset['asset_code'], ErrCode::$VALIDATE_ERROR);
            }
            //获取所有资产编码
            $asset_status_codes[$return_list[$asset['id']]['use_status']]['asset_codes'][] = $asset['asset_code'];
            $asset_status_codes[$return_list[$asset['id']]['use_status']]['asset_ids'][] = $asset['id'];
        }
        return $asset_status_codes;
    }

    /**
     * 验证退回申请单-单个
     * @param integer $return_id 退回申请单ID
     * @return mixed
     * @throws ValidationException
     */
    private function validateAssetReturnInfo($return_id)
    {
        $return_info = MaterialAssetReturnModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $return_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($return_info)) {
            throw new ValidationException(static::$t->_('material_asset_return_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $return_info;
    }

    /**
     * 处理、转交操作拦截逻辑
     * @param array $params 请求参数组
     * @param int $type 2处理、5转交
     * @return mixed
     * @throws ValidationException
     */
    private function validateAssetHandleOrTransfer($params, $type = MaterialEnums::RETURN_ASSETS_RECORD_TYPE_HANDLE)
    {
        //检测退库申请信息
        $return_info = $this->validateAssetReturnInfo($params['id']);

        //单据标识为已完成、已拒收、已撤回 不可操作
        if (in_array($return_info->status, [MaterialEnums::RETURN_STATUS_REJECTED, MaterialEnums::RETURN_STATUS_CANCEL, MaterialEnums::RETURN_STATUS_FINISHED])) {
            throw new ValidationException(static::$t->_('material_asset_return_invalid'), ErrCode::$VALIDATE_ERROR);
        }

        //处理时 & 单据标识只能是已接收
        if ($type == MaterialEnums::RETURN_ASSETS_RECORD_TYPE_HANDLE && $return_info->status != MaterialEnums::RETURN_STATUS_RECEIVED) {
            throw new ValidationException(static::$t->_('material_asset_return_must_received'), ErrCode::$VALIDATE_ERROR);
        }

        //转交时 & 转交部门不可等于原处理部门
        if ($type == MaterialEnums::RETURN_ASSETS_RECORD_TYPE_TRANSFER && $return_info->now_group_id == $params['group_id']) {
            throw new ValidationException(static::$t->_('material_asset_return_transfer_group_invalid'), ErrCode::$VALIDATE_ERROR);
        }

        //关联（待处理，已入库（scm和非scm），已审核）入库单 不可操作
        $this->validateStorage($params['id']);

        return $return_info;
    }

    /**
     * 验证退回入库单信息
     * @param integer $return_storage_id 退回入库单ID
     * @return mixed
     * @throws ValidationException
     */
    private function validateReturnStorageInfo($return_storage_id)
    {
        $return_storage = MaterialAssetReturnStorageModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $return_storage_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($return_storage)) {
            throw new ValidationException(static::$t->_('material_asset_return_storage_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $return_storage;
    }

    /**
     * 关联（待处理，已入库（scm和非scm），已审核）入库单 不可操作
     * @param integer $return_id 退回申请单ID
     * @param integer $type 操作类型 2处理、5转移、9已完成
     * @return bool
     * @throws ValidationException
     */
    private function validateStorage($return_id, $type = MaterialEnums::RETURN_ASSETS_RECORD_TYPE_HANDLE)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetReturnStorageModel::class]);
        $builder->leftJoin(MaterialAssetReturnStorageProductModel::class, 'product.return_storage_id = main.id', 'product');
        $builder->where('main.status != :status: and main.is_deleted = :is_deleted: and product.return_id = :return_id: and product.is_deleted = :is_deleted:', ['status' => MaterialEnums::RETURN_STORAGE_STATUS_CANCEL, 'return_id' => $return_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->columns('main.id');
        $storage_info = $builder->getQuery()->getSingleResult();
        if (!empty($storage_info)) {
            throw new ValidationException(static::$t->_($type == MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH ? 'material_asset_return_storage_finish_invalid' : 'material_asset_return_storage_invalid'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 退回(批量)
     * @param array $params 参数
     * @param $user
     * @return array
     */
    public function assetReturn($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            //验证选择的资产退回类型信息是否存在
            MaterialSettingService::getInstance()->getReturnRepairTypeInfo($params['type_id']);

            //验证资产信息
            $asset_ids = array_values(array_unique($params['asset_ids']));
            $assets = $this->validateAssets($asset_ids);

            $asset_list = $assets->toArray();
            foreach ($asset_list as $asset) {
                // 所有资产status必须是3:使用中、15闲置（在网点）
                if (!in_array($asset['status'], [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                    throw new ValidationException(static::$t->_('material_asset_return_by_status_not_using') . ':' . $asset['asset_code'], ErrCode::$VALIDATE_ERROR);
                }
            }

            //先找到台账里的所有部门ID，找到部门ID的上级部门
            $departmentList = (new DepartmentRepository())->getDepartmentByIds(array_column($asset_list, 'node_department_id'));

            //找主管部门
            $group_ids = [];
            foreach ($asset_list as $asset) {
                //要根据每笔资产使用部门+物料分类+barcode+退回类型找到这个资产对应的退回组
                $ancestry_v3 = $departmentList[$asset['node_department_id']]['ancestry_v3'] ?? '';
                $department_ids =  $ancestry_v3 ? explode('/', $ancestry_v3) : [];
                $group_ids[] = $this->findGroupId($department_ids, $asset['node_department_id'], $asset['category_id'], $asset['bar_code'], $params['type_id']);
            }

            //如果一批里，找到不同的退回组，则默认取兜底组为Y的退回组。
            $group_ids = array_unique($group_ids);
            $group_id = $group_ids[0];
            if (count($group_ids) > 1 || $group_id == 0) {
                $undertake_group_info = MaterialSetReturnRepairGroupModel::findFirst([
                    'conditions' => 'is_undertake = :is_undertake: and is_deleted = :is_deleted:',
                    'bind' => ['is_undertake' => MaterialEnums::MATERIAL_SET_RETURN_REPAIR_IS_UNDERTAKE_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ]);
                if (empty($undertake_group_info)) {
                    throw new ValidationException(static::$t->_('material_set_return_group_undertask_not_found'), ErrCode::$VALIDATE_ERROR);
                }
                $group_id = $undertake_group_info->id;
            }
            $params['group_id'] = $group_id;
            $params['now_time'] = date('Y-m-d H:i:s');

            // 所有验证通过, 开始数据变更
            $department_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
            $user['company_id'] = $department_info['company_id'] ?? 0;
            $user['company_name'] = $department_info['company_name'] ?? '';

            //网点信息
            if ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $user['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                $store_info = (new StoreRepository())->getStoreDetail($user['sys_store_id']);
                $user['store_name'] = $store_info['name'] ?? '';
            }

            //当前国家币种
            $default_currency = EnumsService::getInstance()->getSysCurrencyInfo();

            //一个资产一条退回申请单、绑定附件、退回操作日志、修改台账、台账操作日志
            foreach ($assets as $asset) {
                $this->saveAssetReturnDb($asset, $params, $user, $default_currency);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-assetReturn-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 根据资产身上的费用所属部门+物料分类+barcode+退回类型找到这个资产对应的退回组
     * 如果找到多个满足条件，默认找ID最小的条件找对应的组
     * @param array $node_department_parent_ids 资产所属部门的父级部门ID组
     * @param integer $node_department_id 资产所属部门ID
     * @param integer $category_id 资产物料分类表ID
     * @param string $barcode barcode
     * @param integer $type_id 退回类型ID
     * @return int|mixed
     */
    public function findGroupId($node_department_parent_ids, $node_department_id, $category_id, $barcode, $type_id)
    {
        //部门ID为0表示所有，需要追加到父级部门
        array_push($node_department_parent_ids, 0);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialSetReturnRepairMapModel::class]);
        $builder->leftJoin(MaterialSetReturnRepairMapBarcodeModel::class, 'b.map_id = main.id', 'b');
        $builder->leftJoin(MaterialSetReturnRepairMapCategoryModel::class, 'c.map_id = main.id', 'c');
        $builder->leftJoin(MaterialSetReturnRepairMapDepartmentModel::class, 'd.map_id = main.id', 'd');
        $builder->where('main.type_id = :type_id:', ['type_id' => $type_id]);
        $builder->inWhere('b.barcode', [$barcode, '']);
        $builder->inWhere('c.category_id', [$category_id, 0]);
        $builder->andWhere('(d.department_id in ({department_ids:array}) and d.is_include_sub = :is_include_sub:) OR d.department_id = :node_department_id:',
            ['department_ids' => $node_department_parent_ids, 'is_include_sub' => SettingEnums::IS_INCLUDE_SUB, 'node_department_id' => $node_department_id]);
        $builder->columns('main.id, main.group_id');
        $map_list = $builder->getQuery()->execute()->toArray();
        if ($map_list) {
            //如果找到多个满足条件，默认找ID最小的条件找对应的组 sql 会慢查询，所以采用数组排序，取第一个组
            $map_list = array_sort($map_list, 'id', SORT_DESC);
            $group_id = $map_list[0]['group_id'];
        } else {
            $group_id = 0;
        }
        return $group_id;
    }

    /**
     * 资产退回申请-相关表操作-保存
     * @param object $asset_info 资产对象
     * @param array $params 请求参数组
     * @param array $user 用户信息组
     * @param array $default_currency 币种信息
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveAssetReturnDb($asset_info, $params, $user, $default_currency)
    {
        $no = static::genSerialNo('RA', RedisKey::ASSET_RETURN_CREATE_COUNTER, 5, date('ymd'));
        // 验证单号是否已创建 或 占用
        $exist_data_by_no = MaterialAssetReturnModel::findFirst([
            'conditions' => 'no = :no:',
            'bind' => ['no' => $no],
            'columns' => ['id']
        ]);
        if (!empty($exist_data_by_no)) {
            throw new ValidationException(static::$t->_('material_asset_return_no_exist_hint', ['no' => $no]), ErrCode::$VALIDATE_ERROR);
        }

        // 记录退回申请
        if ($asset_info->currency != $default_currency['code']) {
            //转换本位币金额
            $asset_info->purchase_price = round(EnumsService::getInstance()->currencyAmountConversion((string)$asset_info->currency, (string)$asset_info->purchase_price, 3), 2);
            $asset_info->net_value = round(EnumsService::getInstance()->currencyAmountConversion((string)$asset_info->currency, (string)$asset_info->net_value, 3), 2);
        }
        $asset_return_info = [
            'no' => $no,
            'apply_date' => date('Y-m-d'),
            'apply_id' => $user['id'],
            'apply_name' => $user['name'],
            'company_id' => $user['company_id'],
            'company_name' => $user['company_name'],
            'node_department_id' => $user['node_department_id'],
            'node_department_name' => $user['department'],
            'sys_store_id' => $user['sys_store_id'],
            'store_name' => $user['store_name'],
            'job_id' => $user['job_title_id'],
            'job_name' => $user['job_title'],
            'type_id' => $params['type_id'],
            'group_id' => $params['group_id'],
            'now_group_id' => $params['group_id'],
            'return_method' => $params['return_method'],
            'express_no' => $params['express_no'] ?? '',
            'return_reason' => $params['return_reason'],
            'status' => MaterialEnums::RETURN_STATUS_UNRECEIVED,
            'is_receipt' => MaterialEnums::RETURN_STATUS_UNRECEIVED,
            'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_UNRECEIVED,
            'asset_id' => $asset_info->id,
            'asset_code' => $asset_info->asset_code,
            'barcode' => $asset_info->bar_code,
            'name_zh' => $asset_info->name_zh,
            'name_en' => $asset_info->name_en,
            'name_local' => $asset_info->name_local,
            'old_asset_code' => $asset_info->old_asset_code,
            'sn_code' => $asset_info->sn_code,
            'model' => $asset_info->model,
            'purchase_price' => $asset_info->purchase_price,
            'net_value' => $asset_info->net_value,
            'use_staff_id' => $asset_info->staff_id,
            'use_staff_name' => $asset_info->staff_name,
            'use_company_id' => $asset_info->company_id,
            'use_company_name' => $asset_info->company_name,
            'use_node_department_id' => $asset_info->node_department_id,
            'use_node_department_name' => $asset_info->node_department_name,
            'use_sys_store_id' => $asset_info->sys_store_id,
            'use_store_name' => $asset_info->store_name,
            'use' => $asset_info->use,
            'use_status' => $asset_info->status,
            'created_at' => $params['now_time'],
            'updated_at' => $params['now_time'],
        ];
        $asset_return_model = new MaterialAssetReturnModel();
        $bool = $asset_return_model->i_create($asset_return_info);
        if ($bool === false) {
            throw new BusinessException('资产退回申请-保存-失败 = ' . json_encode($asset_return_info, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_return_model), ErrCode::$BUSINESS_ERROR);
        }

        // 记录退回申请附件
        if (!empty($params['attachments'])) {
            $attachArr = [];
            foreach ($params['attachments'] as $attachment) {
                $tmp = [];
                $tmp['oss_bucket_type'] = MaterialEnums::OSS_MATERIAL_TYPE_RETURN_APPLY;
                $tmp['oss_bucket_key'] = $asset_return_model->id;
                $tmp['sub_type'] = 0;
                $tmp['bucket_name'] = $attachment['bucket_name'];
                $tmp['object_key'] = $attachment['object_key'];
                $tmp['file_name'] = $attachment['file_name'];
                $tmp['object_url'] = $attachment['object_url'];
                $tmp['created_at'] = $params['now_time'];
                $attachArr[] = $tmp;
            }
            $material_attachment = new MaterialAttachmentModel();
            $bool = $material_attachment->batch_insert($attachArr);
            if ($bool === false) {
                throw new BusinessException('资产退回申请-保存附件-失败 = ' . json_encode($attachArr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        // 记录退回操作日志
        $material_asset_return_recode = new MaterialAssetReturnRecordModel();
        $bool = $material_asset_return_recode->dealEditField(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_ADD, $asset_return_model, ['asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_UNRECEIVED, 'return_reason' => $params['return_reason']], $user);
        if ($bool === false) {
            throw new BusinessException('资产退回申请-保存退回操作记录失败 = ' . json_encode($asset_return_model->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 修改资产台账状态
        $log_asset = clone $asset_info;
        $asset_update = [
            'status' => MaterialEnums::ASSET_STATUS_RETURN_ING,
            'updated_at' => $params['now_time']
        ];
        $bool = $asset_info->i_update($asset_update);
        if ($bool === false) {
            throw new BusinessException('资产退回申请-资产台账更新-保存失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
        }

        // 记录资产台账操作日志
        $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
        $bool = $material_asset_update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_APPLY_RETURN, $log_asset, $asset_update, $user);
        if ($bool === false) {
            throw new BusinessException('资产退回申请-资产台账更新-保存操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }

    /**
     * BY端-资产管理-我的资产-退回撤销（批量）- 需把by请求按照退回申请ID
     * 把传来的资产id查出关联的退回申请id (by端是从我的资产列表撤销传的是资产id)
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @date 2022/11/20
     * @return array
     */
    public function conversionByCancel($params, $user)
    {
        $data = [];
        try {
            //根据资产id查询对应的待接收退回数据的id,用这些id去撤销
            $asset_ids = array_values(array_unique($params['asset_ids']));
            $return_list = MaterialAssetReturnModel::find([
                'conditions' => 'asset_id in({asset_ids:array}) and status = :status: and is_deleted = :is_deleted:',
                'bind' => ['asset_ids' => $params['asset_ids'], 'status' => MaterialEnums::RETURN_STATUS_UNRECEIVED, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (empty($return_list) || count($return_list) != count($asset_ids)) {
                throw new ValidationException(static::$t->_('material_asset_return_cancel_asset_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            unset($params['asset_ids']);
            //退回撤销(批量)
            $return_cancel_result = $this->assetCancel($params, $user, $return_list);
            $code = $return_cancel_result['code'];
            $message = $return_cancel_result['message'];
            $data = $return_cancel_result['data'];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('by-asset-return-conversionByCancel-failed:' . $e->getMessage() . $e->getTraceAsString());
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回撤销(批量)
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @param array $return_list 退回列表组
     * @return array
     */
    public function assetCancel($params, $user, $return_list = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //传递了退回组列表 则表示是从by端-我的资产-资产管理-撤销操作按照资产ID反查回来的
            if ($return_list) {
                $return_ids = array_column($return_list, 'id');
            } else {
                //验证信息
                $return_ids = array_values(array_unique($params['return_ids']));
                //查询退回记录 && 待接收 && 非删除
                $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_CANCEL);
            }

            //验证资产信息
            $asset_status_codes = $this->validateCancelOrRejectAssets($return_list);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            // 修改退回申请
            $return_ids_str = implode(',', $return_ids);
            $before_status_unreceived = MaterialEnums::RETURN_STATUS_UNRECEIVED;
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnModel())->getSource(),
                [
                    'status' => MaterialEnums::RETURN_STATUS_CANCEL,
                    'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_CANCEL,
                    'operator_staff_id' => $user['id'],
                    'operator_staff_name' => $user['name'],
                    'finished_at' => $now_time,
                    'remark' => $params['remark'],
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_unreceived}",
                ]
            );
            $affected_rows = $db->affectedRows();
            if (!$bool || $affected_rows != count($return_ids)) {
                throw new BusinessException('撤销退回申请 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_CANCEL, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_CANCEL, 'cancel_remark' => $params['remark']], $user);
            if ($bool === false) {
                throw new BusinessException('撤销退回申请-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 资产台账相关操作
            foreach ($asset_status_codes as $after_status => $item) {
                // 修改资产台账状态
                $asset_ids = implode(',', $item['asset_ids']);
                $before_asset_status = MaterialEnums::ASSET_STATUS_RETURN_ING;
                $bool = $db->updateAsDict(
                    (new MaterialAssetsModel())->getSource(),
                    [
                        'status' => $after_status,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => "id IN ({$asset_ids}) AND status = {$before_asset_status}",
                    ]
                );
                if ($bool === false) {
                    throw new BusinessException('撤销退回申请-修改资产台账状态失败 = ' . json_encode($item['asset_ids'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 记录资产台账操作日志
                $before_data = ['status' => $before_asset_status];
                $after_data = ['status' => $after_status];
                $bool = (new MaterialAssetUpdateLogModel())->dealEditFieldBatch($item['asset_codes'], $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_APPLY_RETURN_CANCEL);
                if ($bool === false) {
                    throw new BusinessException('撤销退回申请-资产台账-操作记录失败 = ' . json_encode(['asset_codes' => $item['asset_codes'], 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-assetCancel-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * BY端-退回列表 - （我的）
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function returnList($params, $user)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            $flag = !empty($params['flag']) ? $params['flag'] : self::BY_LIST_TYPE_WAIT;

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialAssetReturnModel::class]);
            $builder->where('main.apply_id = :apply_id: and main.is_deleted = :is_deleted:', ['apply_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            if ($flag == self::BY_LIST_TYPE_WAIT) {
                //待处理 看 待接收数据
                $builder->andWhere('main.status = :status:', ['status' => MaterialEnums::RETURN_STATUS_UNRECEIVED]);
            } elseif ($flag == self::BY_LIST_TYPE_ING) {
                //处理中 看 已接收数据
                $builder->andWhere('main.status = :status:', ['status' => MaterialEnums::RETURN_STATUS_RECEIVED]);
            } else {
                //已完成 看 已拒收、已撤回、已完成数据
                $builder->inWhere('main.status', [MaterialEnums::RETURN_STATUS_REJECTED, MaterialEnums::RETURN_STATUS_CANCEL, MaterialEnums::RETURN_STATUS_FINISHED]);
            }
            //资产名称搜索
            if (!empty($params['name'])) {
                $builder->andWhere('main.' . get_lang_field_name('name_', static::$language) . ' like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            //sn编码搜索
            if (!empty($params['sn_code'])) {
                $builder->andWhere('main.sn_code like :sn_code:', ['sn_code' => '%' . $params['sn_code'] . '%']);
            }
            //资产码或旧资产码搜索
            if (!empty($params['asset_code'])) {
                $builder->andWhere('main.asset_code like :asset_code: OR main.old_asset_code like :asset_code:', ['asset_code' => '%' . $params['asset_code'] . '%']);
            }
            $count = (int)$builder->columns('count(main.id) as total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $name_filed = get_lang_field_name('name_', static::$language);
                $builder->columns('main.id, main.asset_code, main.name_en, ' . $name_filed . ' as name, main.old_asset_code, main.sn_code, main.type_id, main.apply_date, main.return_method, main.express_no, main.group_id, main.status, main.asset_handling_status, main.now_group_id, main.remark, main.asset_id');
                $builder->limit($page_size, $offset);
                $builder->orderBy('main.id '  . ($flag == self::LIST_TYPE_DONE ? 'DESC' : 'ASC'));
                $items = $builder->getQuery()->execute()->toArray();
                if ($items) {
                    $type_ids = array_values(array_unique(array_column($items, 'type_id')));
                    $type_list = MaterialSetReturnTypeRepository::getInstance()->getTypeListByIds($type_ids);
                    $group_filed = $flag == self::BY_LIST_TYPE_WAIT ? 'group_id' : 'now_group_id';
                    $group_ids = array_values(array_unique(array_column($items, $group_filed)));
                    $group_list = MaterialSetReturnGroupRepository::getInstance()->getGroupListByIds($group_ids);

                    $asset_handling_status_list = $this->getAssetHandlingStatus(false);
                    foreach ($items as &$item) {
                        $item['name'] = $item['name'] ? $item['name'] : $item['name_en'];//资产名称当地语言为空，则取英文名称
                        $item['status_text'] = static::$t->_(MaterialEnums::$return_status[$item['status']] ?? '');
                        $item['return_method_text'] = static::$t->_(MaterialEnums::$return_method[$item['return_method']] ?? '');
                        $item['asset_handling_status_text'] = static::$t->_($asset_handling_status_list[$item['asset_handling_status']]['translation_code'] ?? '');
                        $item['type_name'] = $type_list[$item['type_id']][$name_filed];
                        $one_group_info = $group_list[$item[$group_filed]] ?? [];
                        $item['group_name'] = $one_group_info[$name_filed] ?? '';
                        $item['email'] = $one_group_info['email'] ?? '';
                        $item['mobile'] = $one_group_info['mobile'] ?? '';
                        $item['address'] = $one_group_info['address'] ?? '';
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('by-asset-return-returnList-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by端-退回详情
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function returnDetailForBy($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $return_info = MaterialAssetReturnModel::findFirst([
                'conditions' => 'id = :id: and apply_id = :apply_id: and is_deleted = :is_deleted:',
                'bind' => ['id' => $params['id'], 'apply_id' => $user['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (!empty($return_info)) {
                //资产退回处理进度
                $asset_handling_status_list = $this->getAssetHandlingStatus(false);
                $type_info = $return_info->getTypeInfo();//退回类型
                $group_info = $return_info->getGroupInfo();//现处理部门
                $name_field = get_lang_field_name('name_', static::$language);
                $data = [
                    'id' => $return_info->id,
                    'name' => $return_info->$name_field ? $return_info->$name_field : $return_info->name_en,
                    'model' => $return_info->model,
                    'asset_code' => $return_info->asset_code,
                    'sn_code' => $return_info->sn_code,
                    'use_text' => static::$t->_(MaterialEnums::$use_by[$return_info->use ?? '']),
                    'group_name' => $group_info ? $group_info->$name_field : '',
                    'asset_handling_status_text' => static::$t->_($asset_handling_status_list[$return_info->asset_handling_status]['translation_code'] ?? ''),
                    'asset_handling_remark' => $return_info->asset_handling_remark,
                    'type_name' => $type_info? $type_info->$name_field : '',
                    'return_method' => $return_info->return_method,
                    'return_method_text' => static::$t->_(MaterialEnums::$return_method[$return_info->return_method] ?? ''),
                    'return_reason' => $return_info->return_reason,
                    'use_staff_id' => $return_info->use_staff_id,
                    'use_staff_name' => $return_info->use_staff_name,
                    'express_no' => $return_info->express_no,
                    'remark' => $return_info->remark,
                    'attachments' => $return_info->getApplyAttachment()->toArray()
                ];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-returnDetailForBy-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回-添加资产-列表
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function returnAddAssetList($params, $user)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            $store_job_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_return_store_job_ids');//添加退回同网点的资产
            $node_department_job_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_return_node_department_job_ids');//添加退回同部门的资产
            $sys_department_job_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_asset_return_sys_department_job_ids');//添加退回一级部门的资产
            $sys_store_id = '';//所属网点
            $node_department_id = [];//所属部门
            $child_department_ids = [];//一级部门及其子部门
            //非总部 && 职位满足网点职位配置 && 按照使用人所属网点下的
            if (in_array($user['job_title_id'], $store_job_ids) && $user['sys_store_id'] != Enums::HEAD_OFFICE_STORE_FLAG) {
                $sys_store_id = $user['sys_store_id'];
            }
            //职位满足同部门职位配置 && 按照部门查找,需要找该部门下的
            if (in_array($user['job_title_id'], $node_department_job_ids)) {
                $node_department_id[] = $user['node_department_id'];
            }
            //职位满足一级部门职位配置 && 按照部门查找,需要找该部门以及下属子部门下的
            if (in_array($user['job_title_id'], $sys_department_job_ids)) {
                $department_service = new DepartmentService();
                $child_department_ids = $department_service->getChildrenListByDepartmentIdV2($user['sys_department_id'], true);
                array_push($child_department_ids, $user['sys_department_id']);
            }
            $department_ids = array_values(array_unique(array_merge($node_department_id,$child_department_ids)));

            //获取资产列表
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialAssetsModel::class]);
            $builder->where('main.staff_id > :staff_id_zero: and main.is_deleted = :is_deleted:', ['staff_id_zero' => 0, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->inWhere('main.status', [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE]);
            //如果职位均不符合上述设置的职位配置里，则只取自己名下的资产
            if (empty($sys_store_id) && empty($department_ids)) {
                $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $user['id']]);
            } else if ($sys_store_id && empty($department_ids)) {
                //只有网点符合，则只查网点的
                $builder->andWhere('main.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
            } else if (empty($sys_store_id) && $department_ids) {
                //只有部门符合，则只查部门的
                $builder->inWhere('main.node_department_id', $department_ids);
            } else {
                //网点、部门是或的关系
                $builder->andWhere('main.sys_store_id = :sys_store_id: OR main.node_department_id in ({node_department_ids:array})', ['sys_store_id' => $sys_store_id, 'node_department_ids' => $department_ids]);
            }
            //资产名称搜索
            if (!empty($params['name'])) {
                $builder->andWhere('main.' . get_lang_field_name('name_', static::$language) . ' like :name:', ['name' => '%' . $params['name'] . '%']);
            }
            //sn编码搜索
            if (!empty($params['sn_code'])) {
                $builder->andWhere('main.sn_code like :sn_code:', ['sn_code' => '%' . $params['sn_code'] . '%']);
            }
            //资产码或旧资产码搜索
            if (!empty($params['asset_code'])) {
                $builder->andWhere('main.asset_code like :asset_code: OR main.old_asset_code like :asset_code:', ['asset_code' => '%' . $params['asset_code'] . '%']);
            }
            //资产ID组
            if (!empty($params['ids'])) {
                $builder->inWhere('id', $params['ids']);
            }
            $count = (int)$builder->columns('count(main.id) as total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $builder->columns('main.id, main.asset_code, main.name_en, ' . get_lang_field_name('name_', static::$language) . ' as name, main.use, main.sn_code, main.model, main.old_asset_code, main.staff_id, main.staff_name, main.store_name, main.node_department_name, main.status');
                $builder->limit($page_size, $offset);
                $builder->orderBy('main.id ASC');
                $items = $builder->getQuery()->execute()->toArray();
                if ($items) {
                    foreach ($items as &$item) {
                        $item['name'] = $item['name'] ? $item['name'] : $item['name_en'];//资产名称当地语言为空，则取英文名称
                        $item['status_text'] = static::$t->_(MaterialEnums::$asset_status[$item['status']] ?? '');
                        $item['use_text'] = static::$t->_(MaterialEnums::$use_by[$item['use']] ?? '');
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('by-asset-return-returnAddAssetList-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-列表
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @param integer $flag 1待处理、2已转交、3已处理
     * @return array
     */
    public function getList($params, $user, $flag = self::LIST_TYPE_WAIT)
    {
        $params['pageSize'] = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $params['pageNum']  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $params['pageNum'],
                'per_page' => $params['pageSize'],
                'total_count' => 0,
            ],
        ];
        try {
            $params = trim_array($this->request->get());
            $params = self::handleParams($params, self::$return_not_must_params);
            Validation::validate($params, self::$return_list_validate);
            $params['flag'] = $flag;
            $count = $this->getListCount($params, $user);
            if ($count) {
                $items = $this->getReturnList($params, $user);
            }

            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-getList-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 格式化好的退回资产处理-列表
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @param bool $export 是否导出，true是，false否
     * @return array
     */
    public function getReturnList($params, $user, $export = false)
    {
        $asset_name_filed = get_lang_field_name('name_', static::$language);
        $columns = 'main.id, main.is_receipt, main.status, main.asset_code, main.old_asset_code, main.name_en, main.' . $asset_name_filed . ' as name, main.sn_code, main.asset_handling_status, main.type_id, main.group_id, main.now_group_id, main.return_reason';
        $columns.= ', main.apply_id, main.apply_name, main.store_name, main.node_department_name, main.company_name, main.express_no, main.apply_date, main.updated_at, main.finished_at, main.return_storage_no, main.return_storage_scm_no';
        if ($export) {
            $columns.= ', main.no, main.barcode, main.use_staff_id, main.use_staff_name, main.use_store_name, main.use_node_department_name, main.use_company_name, main.operator_staff_id, main.operator_staff_name, main.remark, main.updated_at';
        }
        //列表类型
        $flag = empty($params['flag']) ? self::LIST_TYPE_WAIT : $params['flag'];
        if (in_array($flag, [self::LIST_TYPE_WAIT, self::LIST_TYPE_DONE])) {
            //待处理、已完成读最新的转移信息
            $columns.= ', main.transfer_staff_id, main.transfer_staff_name, main.transfer_at, main.transfer_node_department_name';
            $sort = 'main.id ASC';
        } else {
            //已转交读每次的转移信息
            $columns.= ', transfer.id as transfer_id, transfer.staff_id as transfer_staff_id, transfer.staff_name as transfer_staff_name, transfer.created_at as transfer_at, transfer.node_department_name as transfer_node_department_name';
            $sort = 'transfer.id DESC';
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetReturnModel::class]);
        $builder->columns($columns);
        $builder = $this->getCondition($builder, $params, $user);
        if ($builder === false) {
            return [];
        }
        $offset = $params['pageSize'] * ($params['pageNum'] - 1);
        $builder->limit($params['pageSize'], $offset);
        $builder->orderBy($sort);
        $items = $builder->getQuery()->execute()->toArray();

        //有查询到数据需格式化
        if ($items) {
            $asset_handling_status_list = $this->getAssetHandlingStatus(false);

            //退回类型/退回组字段名
            $name_filed = get_lang_field_name('name_', static::$language);
            $type_ids = array_values(array_unique(array_column($items, 'type_id')));
            $type_list = MaterialSetReturnTypeRepository::getInstance()->getTypeListByIds($type_ids);
            if ($export) {
                $row_values = [];
                foreach ($items as $item) {
                    $row_values[] = [
                        static::$t->_(MaterialEnums::$return_status[$item['is_receipt']] ?? ''),//接收状态
                        static::$t->_($asset_handling_status_list[$item['asset_handling_status']]['translation_code'] ?? ''),//资产处理状态
                        $item['no'],//退回申请单号
                        $item['apply_date'],//退回发起日期
                        $item['asset_code'],//新资产码
                        $item['old_asset_code'],//旧资产码
                        $item['sn_code'],//SN码
                        $item['barcode'],//barcode
                        $item['name'] ? $item['name'] : $item['name_en'],//资产名称
                        $item['use_staff_id'],//资产使用人工号,
                        $item['use_staff_name'],//资产使用人姓名
                        $item['use_store_name'],//资产使用人所属网点
                        $item['use_node_department_name'],//资产使用人所属部门
                        $item['use_company_name'],//资产所属公司
                        $item['apply_id'],//发起人工号
                        $item['apply_name'],//发起人姓名
                        $item['store_name'],//发起人所属网点
                        $item['node_department_name'],//发起人所属部门
                        $type_list[$item['type_id']][$name_filed] ?? '',//退回类型
                        $item['express_no'],//快递单号
                        $item['return_reason'],//退回原因
                        $item['transfer_staff_id'] ? $item['transfer_staff_id'] : '',//转交人工号
                        $item['transfer_staff_name'],//转交人姓名
                        $item['transfer_node_department_name'],//转交人部门
                        $item['transfer_at'] ? date('Y-m-d', strtotime($item['transfer_at'])) : '',//转交日期
                        $item['status'] == MaterialEnums::RETURN_STATUS_FINISHED && $item['operator_staff_id'] ? $item['operator_staff_id'] : '',//完成人工号
                        $item['status'] == MaterialEnums::RETURN_STATUS_FINISHED ? $item['operator_staff_name'] : '',//完成人姓名
                        $item['status'] == MaterialEnums::RETURN_STATUS_FINISHED && $item['finished_at'] ? date('Y-m-d', strtotime($item['finished_at'])) : '',//完成日期
                        $item['status'] == MaterialEnums::RETURN_STATUS_REJECTED ? $item['remark'] : '',//拒收原因
                        $item['updated_at']//更新时间
                    ];
                }
                $items = $row_values;
            } else {
                $group_ids = array_values(array_unique(array_column($items, 'group_id')));//主管部门
                $now_group_ids = array_values(array_unique(array_column($items, 'now_group_id')));//现处理部门
                $group_list = MaterialSetReturnGroupRepository::getInstance()->getGroupListByIds(array_values(array_unique(array_merge($group_ids, $now_group_ids))));
                foreach ($items as &$item) {
                    $item['name'] = $item['name'] ? $item['name'] : $item['name_en'];//资产名称当地语言为空，则取英文名称
                    $item['type_name'] = $type_list[$item['type_id']][$name_filed] ?? '';//退回类型名称
                    $item['group_name'] = $group_list[$item['group_id']][$name_filed] ?? '';//主管部门
                    $item['now_group_name'] = $group_list[$item['now_group_id']][$name_filed] ?? '';//现处理部门
                    $item['is_receipt_text'] = static::$t->_(MaterialEnums::$return_status[$item['is_receipt']] ?? '');
                    $item['asset_handling_status_text'] = static::$t->_($asset_handling_status_list[$item['asset_handling_status']]['translation_code'] ?? '');
                    $item['transfer_staff_id'] = $item['transfer_staff_id'] ? $item['transfer_staff_id'] : '';
                    $item['updated_at'] = date('Y-m-d', strtotime($item['updated_at']));
                    $item['transfer_at'] = $item['transfer_at'] ? date('Y-m-d', strtotime($item['transfer_at'])) : '';
                    $item['finished_at'] = $item['finished_at'] ? date('Y-m-d', strtotime($item['finished_at'])) : '';
                }
            }
        }
        return $items;
    }

    /**
     * 退回资产处理-列表总数
     * @param array $condition 筛选条件组
     * @param array $user 当前登陆者信息组
     * @return int
     */
    public function getListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetReturnModel::class]);
        $builder->columns('count(main.id) AS count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $user);
        if ($builder === false) {
            return 0;
        }
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param array $user 当前登陆者信息组
     * @return mixed
     */
    public function getCondition($builder, $condition, $user)
    {
        //自己所在的退回&维修组，若在多个组则都可看到；未在组里无法看到数据
        $user_has_group_ids = MaterialSettingService::getInstance()->getStaffReturnGroups($user['id']);
        if (empty($user_has_group_ids)) {
            return false;
        }
        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //列表类型
        $flag = empty($condition['flag']) ? self::LIST_TYPE_WAIT : $condition['flag'];
        //每个列表基础查询条件
        if ($flag == self::LIST_TYPE_WAIT) {
            //待处理
            $builder->inWhere('main.now_group_id', $user_has_group_ids);
            $builder->inWhere('main.status', [MaterialEnums::RETURN_STATUS_UNRECEIVED, MaterialEnums::RETURN_STATUS_RECEIVED]);
        } else if ($flag == self::LIST_TYPE_TRANSFER) {
            //已转交
            $builder->rightJoin(MaterialAssetReturnTransferLogModel::class, 'transfer.return_id = main.id', 'transfer');
            $builder->inWhere('transfer.group_id', $user_has_group_ids);
        } else {
            //已完成
            $builder->inWhere('main.status', [MaterialEnums::RETURN_STATUS_REJECTED, MaterialEnums::RETURN_STATUS_CANCEL, MaterialEnums::RETURN_STATUS_FINISHED]);
            $builder->andWhere('main.now_group_id in ({group_ids:array}) OR main.group_id in ({group_ids:array})', ['group_ids' => $user_has_group_ids]);
        }
        $apply_id =  !empty($condition['apply_id']) ? $condition['apply_id'] : [];//申请人
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0; //申请人所属公司
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0; //申请人所属部门
        $asset_handling_status = !empty($condition['asset_handling_status']) ? $condition['asset_handling_status'] : []; //资产处理进度
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : '';//申请人所属网点
        $apply_date_start = !empty($condition['apply_date_start']) ? $condition['apply_date_start'] : '';//申请日期-起始
        $apply_date_end = !empty($condition['apply_date_end']) ? $condition['apply_date_end'] : '';//申请日期-截止
        $type_id = !empty($condition['type_id']) ? $condition['type_id'] : 0;//退回类型
        $now_group_id = !empty($condition['now_group_id']) ? $condition['now_group_id'] : 0;//现处理部门
        $group_id = !empty($condition['group_id']) ? $condition['group_id'] : 0;//主管部门
        $name = !empty($condition['name']) ? $condition['name'] : '';//资产名称
        $asset_code = !empty($condition['asset_code']) ? $condition['asset_code'] : [];//资产编码
        $old_asset_code = !empty($condition['old_asset_code']) ? $condition['old_asset_code'] : [];//旧资产编码
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : [];//barcode
        $sn_code = !empty($condition['sn_code']) ? $condition['sn_code'] : '';//SN编码
        $express_no = !empty($condition['express_no']) ? $condition['express_no'] : '';//快递单号
        if (!empty($apply_id)) {
            $builder->inWhere('main.apply_id', $apply_id);
        }
        if (!empty($company_id)) {
            $builder->andWhere('main.company_id = :company_id:', ['company_id' => $company_id]);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('main.node_department_id', $department_ids);
        }
        if (!empty($asset_handling_status)) {
            $builder->inWhere('main.asset_handling_status', $asset_handling_status);
        }
        if (!empty($sys_store_id)) {
            $builder->andWhere('main.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        if (!empty($apply_date_start)) {
            $builder->andWhere('main.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }
        if (!empty($apply_date_end)) {
            $builder->andWhere('main.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }
        if (!empty($type_id)) {
            $builder->andWhere('main.type_id = :type_id:', ['type_id' => $type_id]);
        }
        if (!empty($now_group_id)) {
            $builder->andWhere('main.now_group_id = :now_group_id:', ['now_group_id' => $now_group_id]);
        }
        if (!empty($group_id)) {
            $builder->andWhere('main.group_id = :group_id:', ['group_id' => $group_id]);
        }
        if (!empty($name)) {
            $builder->andWhere('main.' . get_lang_field_name('name_', static::$language) . ' like :name:', ['name' => '%' . $name . '%']);
        }
        if (!empty($asset_code)) {
            $builder->inWhere('main.asset_code ', $asset_code);
        }
        if (!empty($old_asset_code)) {
            $builder->inWhere('main.old_asset_code', $old_asset_code);
        }
        if (!empty($barcode)) {
            $builder->inWhere('main.barcode', $barcode);
        }
        if (!empty($sn_code)) {
            $builder->andWhere('main.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        if (!empty($express_no)) {
            $builder->andWhere('main.express_no like :express_no:', ['express_no' => '%' . $express_no . '%']);
        }
        return $builder;
    }

    /**
     * 退回资产处理 - 导出各表头
     * @param integer $flag 1待处理、2已转交、3已处理 4退回入库
     * @return array
     */
    public function getExportExcelHeaderFields($flag)
    {
        if ($flag == self::LIST_TYPE_STORAGE) {
            //退回入库
            return [
                static::$t->_('material_asset_return_storage.no'),//OA入库单号
                static::$t->_('material_asset_return_storage.scm_no'),//SCM入库单号
                static::$t->_('material_asset_return_storage.apply_id'),//申请人工号
                static::$t->_('material_asset_return_storage.apply_name'),//申请人姓名
                static::$t->_('material_asset_return_storage.apply_date'),//申请时间
                static::$t->_('material_asset_return_storage.delivery_number'),//运单号
                static::$t->_('material_asset_return_storage.remark'),//备注
                static::$t->_('material_asset_return_storage.status'),//状态
                static::$t->_('material_asset_return_storage.id'),//序号
                static::$t->_('material_asset_return.barcode'),//barcode
                static::$t->_('material_asset_return.name'),//名称
                static::$t->_('material_asset_return.asset_code'),//资产码
                static::$t->_('material_asset_return.old_asset_code'),//旧资产码
                static::$t->_('material_asset_return.sn_code'),//SN码
                static::$t->_('material_asset_return_storage.this_time_num'),//数量
                static::$t->_('material_asset_return_storage.real_quantity_received'),//实际收货数量
                static::$t->_('material_asset_return_storage.mach_name'),//SCM货主
                static::$t->_('material_asset_return_storage.stock_name'),//仓库
            ];
        } else {
            return [
                static::$t->_('material_asset_return.status'),//接收状态
                static::$t->_('material_asset_return.asset_handling_status'),//资产处理进度
                static::$t->_('material_asset_return.no'),//退回申请单号
                static::$t->_('material_asset_return.apply_date'),//退回发起日期
                static::$t->_('material_asset_return.asset_code'),//新资产码
                static::$t->_('material_asset_return.old_asset_code'),//旧资产码
                static::$t->_('material_asset_return.sn_code'),//SN码
                static::$t->_('material_asset_return.barcode'),//barcode
                static::$t->_('material_asset_return.name'),//资产名称
                static::$t->_('material_asset_return.use_staff_id'),//资产使用人工号
                static::$t->_('material_asset_return.use_staff_name'),//资产使用人姓名
                static::$t->_('material_asset_return.use_store_name'),//资产使用人所属网点
                static::$t->_('material_asset_return.use_node_department_name'),//资产使用人所属部门
                static::$t->_('material_asset_return.use_company_name'),//资产所属公司
                static::$t->_('material_asset_return.apply_id'),//发起人工号
                static::$t->_('material_asset_return.apply_name'),//发起人姓名
                static::$t->_('material_asset_return.store_name'),//发起人所属网点
                static::$t->_('material_asset_return.node_department_name'),//发起人所属部门
                static::$t->_('material_asset_return.type_name'),//退回类型
                static::$t->_('material_asset_return.express_no'),//快递单号
                static::$t->_('material_asset_return.return_reason'),//退回原因
                static::$t->_('material_asset_return.transfer_staff_id'),//转交人工号
                static::$t->_('material_asset_return.transfer_staff_name'),//转交人姓名
                static::$t->_('material_asset_return.transfer_node_department_name'),//转交人部门
                static::$t->_('material_asset_return.transfer_at'),//转交日期
                static::$t->_('material_asset_return.operator_staff_id'),//完成人工号
                static::$t->_('material_asset_return.operator_staff_name'),//完成人姓名
                static::$t->_('material_asset_return.finished_at'),//完成日期
                static::$t->_('material_asset_return.reject_remark'),//拒收原因
                static::$t->_('material_asset_return.updated_at'),//更新时间
            ];
        }
    }

    /**
     * 退回资产处理-导出
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function export($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            // 同步下载, 最多1w条
            $params['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $params['pageSize'] = MaterialEnums::RETURN_ASSET_EXPORT_MAX;

            // 获取数据
            $excel_data = $this->getReturnList($params, $user, true);

            // 获取表头
            $header = $this->getExportExcelHeaderFields($params['flag']);

            // 生成Excel
            $file_name = DownloadCenterEnum::$download_center_excel_setting_item[array_flip(self::$list_type)[$params['flag']]]['file_name'];
            $file_name = str_replace('{YmdHis}', date('YmdHis'), $file_name);
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-list-export-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回处理-退货入库通知单-列表
     * @param array $params 参数
     * @return array
     */
    public function getStorageList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            $count = $this->getStorageListCount($params);
            if ($count) {
                $items = $this->getStorageDetailList($params);
            }

            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-getStorageList-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取退回入库列表和明细
     * @param array $params 参数
     * @param bool $export 是否导出 true 是， false否
     * @return mixed
     */
    public function getStorageDetailList($params, $export = false)
    {
        $asset_name_filed = get_lang_field_name('name_', static::$language);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetReturnStorageModel::class]);
        $columns = 'main.id, main.no, main.scm_no, main.apply_id, main.apply_name, main.status, main.created_at';
        if ($export) {
            $columns .= ', main.remark, main.delivery_number, main.mach_name, main.stock_name, product.id as product_id, product.this_time_num, product.real_quantity_received, return.barcode, return.' . $asset_name_filed . ' as name, return.name_en, return.asset_code, return.old_asset_code, return.sn_code';
        }
        $builder->columns($columns);
        $builder = $this->getStorageCondition($builder, $params, $export);
        $offset = $params['pageSize'] * ($params['pageNum'] - 1);
        $builder->limit($params['pageSize'], $offset);
        $builder->orderBy('main.id DESC');
        $items = $builder->getQuery()->execute()->toArray();
        if ($export) {
            $row = [];
            foreach ($items as $item) {
                $row[] = [
                    $item['no'],
                    $item['scm_no'],
                    $item['apply_id'],
                    $item['apply_name'],
                    $item['created_at'],
                    $item['delivery_number'],
                    $item['remark'],
                    static::$t->_(MaterialEnums::$return_storage_status[$item['status']] ?? ''),
                    $item['product_id'],
                    $item['barcode'],
                    $item['name'] ? $item['name'] : $item['name_en'],//资产名称当地语言为空，则取英文名称
                    $item['asset_code'],
                    $item['old_asset_code'],
                    $item['sn_code'],
                    $item['this_time_num'],
                    $item['real_quantity_received'],
                    $item['mach_name'],
                    $item['stock_name']
                ];
            }
            $items = $row;
        } else {
            foreach ($items as &$item) {
                $item['status_text'] = static::$t->_(MaterialEnums::$return_storage_status[$item['status']] ?? '');
            }
        }
        return $items;
    }

    /**
     * 资产退回处理-退货入库通知单-列表总数
     * @param array $condition 筛选条件组
     * @param bool $export 是否导出 true 是， false否
     * @return int
     */
    public function getStorageListCount($condition, $export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialAssetReturnStorageModel::class]);
        $builder->columns('count(main.id) AS count');
        //组合搜索条件
        $builder = $this->getStorageCondition($builder, $condition, $export);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param bool $export 是否导出 true 是， false否
     * @return mixed
     */
    public function getStorageCondition($builder, $condition, $export = false)
    {
        $no = !empty($condition['no']) ? $condition['no'] : '';//OA入库单号
        $scm_no = !empty($condition['scm_no']) ? $condition['scm_no'] : '';//SCM入库单号
        $status = !empty($condition['status']) ? $condition['status'] : 0;//状态
        $asset_code = !empty($condition['asset_code']) ? $condition['asset_code'] : [];//资产编码
        $old_asset_code = !empty($condition['old_asset_code']) ? $condition['old_asset_code'] : [];//旧资产编码
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : [];//barcode
        $sn_code = !empty($condition['sn_code']) ? $condition['sn_code'] : '';//SN编码
        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($no)) {
            $builder->andWhere('main.no = :no:', ['no' => $no]);
        }
        if (!empty($scm_no)) {
            $builder->andWhere('main.scm_no = :scm_no:', ['scm_no' => $scm_no]);
        }
        if (!empty($status)) {
            $builder->andWhere('main.status = :status:', ['status' => $status]);
        }
        if (!empty($asset_code) || !empty($old_asset_code) || !empty($barcode) || !empty($sn_code) || $export) {
            if ($export) {
                $builder->rightJoin(MaterialAssetReturnStorageProductModel::class, 'product.return_storage_id = main.id', 'product');
            } else {
                $builder->leftJoin(MaterialAssetReturnStorageProductModel::class, 'product.return_storage_id = main.id', 'product');
            }
            $builder->leftJoin(MaterialAssetReturnModel::class, 'return.id = product.return_id', 'return');
            $builder->andWhere('product.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            if (!empty($asset_code)) {
                $builder->inWhere('return.asset_code', $asset_code);
            }
            if (!empty($old_asset_code)) {
                $builder->inWhere('return.old_asset_code', $old_asset_code);
            }
            if (!empty($barcode)) {
                $builder->inWhere('return.barcode', $barcode);
            }
            if (!empty($sn_code)) {
                $builder->andWhere('return.sn_code LIKE :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
            }
        }
        return $builder;
    }

    /**
     * 资产退回处理-退货入库通知单-导出
     * @param array $params 参数
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function exportStorageList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            // 同步下载, 最多1w条
            $params['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $params['pageSize'] = MaterialEnums::RETURN_ASSET_EXPORT_MAX;

            // 获取数据
            $excel_data = $this->getStorageDetailList($params, true);

            // 获取表头
            $header = $this->getExportExcelHeaderFields(self::LIST_TYPE_STORAGE);

            // 生成Excel
            $file_name = DownloadCenterEnum::$download_center_excel_setting_item[DownloadCenterEnum::MATERIAL_ASSET_RETURN_STORAGE_EXPORT]['file_name'];
            $file_name = str_replace('{YmdHis}', date('YmdHis'), $file_name);
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-exportStorageList-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回详情
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detail($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$id_validate);
            $return_info = $this->validateAssetReturnInfo($params['id']);

            //退回类型
            $type_info = $return_info->getTypeInfo();
            $name_field = get_lang_field_name('name_', static::$language);

            //资产处理进度
            $asset_handling_status_list = $this->getAssetHandlingStatus(false);

            //申请人信息
            $apply_user_info = (new HrStaffRepository())->getStaffById($return_info->apply_id);
            $data = [
                'id' => $return_info->id,
                'no' => $return_info->no,
                'apply_id' => $return_info->apply_id,
                'apply_name' => $return_info->apply_name,
                'node_department_name' => $return_info->node_department_name,
                'store_name' => $return_info->store_name,
                'job_name' => $return_info->job_name,
                'mobile' => $apply_user_info['mobile_company'] ? $apply_user_info['mobile_company'] : $apply_user_info['mobile'],
                'email' => $apply_user_info['email'] ? $apply_user_info['email'] : $apply_user_info['personal_email'],
                'return_method' => $return_info->return_method,
                'return_method_text' => static::$t->_(MaterialEnums::$return_method[$return_info->return_method] ?? ''),
                'express_no' => $return_info->express_no,
                'return_reason' => $return_info->return_reason,
                'type_name' => $type_info->$name_field,
                'asset_handling_status' => $return_info->asset_handling_status,
                'asset_handling_status_text' => static::$t->_($asset_handling_status_list[$return_info->asset_handling_status]['translation_code'] ?? ''),
                'asset_handling_remark' => $return_info->asset_handling_remark,
                'name' => $return_info->$name_field ? $return_info->$name_field : $return_info->name_en,
                'asset_code' => $return_info->asset_code,
                'old_asset_code' => $return_info->old_asset_code,
                'sn_code' => $return_info->sn_code,
                'use' => $return_info->use,
                'use_text' => static::$t->_(MaterialEnums::$use[$return_info->use] ?? ''),
                'use_staff_id' => $return_info->use_staff_id,
                'use_staff_name' => $return_info->use_staff_name,
                'use_store_name' => $return_info->use_store_name,
                'use_node_department_name' => $return_info->use_node_department_name,
                'net_value' => $return_info->net_value,
                'purchase_price' => $return_info->purchase_price,
                'attachments' => $return_info->getApplyAttachment()->toArray(),
                'asset_handling_attachment' => $return_info->getHandleAttachment()->toArray(),
                'return_record' => $return_info->getReturnRecord()->toArray()
            ];
            foreach ($data['return_record'] as &$item) {
                $item['type_text'] = static::$t->_(MaterialEnums::$return_assets_record_type_list[$item['type']] ?? '');
                $item['content'] = json_decode($item['content'], true);
                $content = '';
                foreach ($item['content'] as $value) {
                    foreach ($value as $field => $v) {
                        if ($field == 'asset_handling_attachment') {
                            $v = implode(',', $v);
                        } else if ($field == 'asset_handling_status') {
                            $v = static::$t->_($asset_handling_status_list[$v]['translation_code'] ?? '');
                        } else if ($field == 'storage_deleted') {
                            $v = static::$t->_('deleted');
                        }
                        $content .= static::$t->_(MaterialEnums::$return_record_need_view_fields[$field] ?? '') . ' : ' . $v . '; ';
                    }
                }
                $item['content'] = $content;
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-detail-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-待处理-批量收货
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchReceipt($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询退回记录 && 待接收 && 非删除
            $return_ids = $params['ids'];
            $this->validateAssetReturn($return_ids);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            $return_ids_str = implode(',', $return_ids);
            $before_status_unreceived = MaterialEnums::RETURN_STATUS_UNRECEIVED;
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnModel())->getSource(),
                [
                    'is_receipt' => MaterialEnums::RETURN_STATUS_RECEIVED,
                    'status' => MaterialEnums::RETURN_STATUS_RECEIVED,
                    'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_RECEIVED,
                    'received_at' => $now_time,
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_unreceived}",
                ]
            );
            $affected_rows = $db->affectedRows();
            if (!$bool || $affected_rows != count($return_ids)) {
                throw new BusinessException('批量收货退回申请 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_RECEIPT, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_STATUS_RECEIVED], $user);
            if ($bool === false) {
                throw new BusinessException('批量收货退回申请-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-batchReceipt-failed:' . $real_message);
        }
        if ($message) {
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-待处理-批量拒绝
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchReject($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询退回记录 && 待接收 && 非删除
            $return_ids = $params['ids'];
            $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_REJECT);

            //验证资产信息
            $asset_status_codes = $this->validateCancelOrRejectAssets($return_list);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            $return_ids_str = implode(',', $return_ids);
            $before_status_unreceived = MaterialEnums::RETURN_STATUS_UNRECEIVED;
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnModel())->getSource(),
                [
                    'is_receipt' => MaterialEnums::RETURN_STATUS_REJECTED,
                    'status' => MaterialEnums::RETURN_STATUS_REJECTED,
                    'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_REJECTED,
                    'operator_staff_id' => $user['id'],
                    'operator_staff_name' => $user['name'],
                    'finished_at' => $now_time,
                    'remark' => $params['remark'],
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_unreceived}",
                ]
            );
            $affected_rows = $db->affectedRows();
            if (!$bool || $affected_rows != count($return_ids)) {
                throw new BusinessException('批量拒绝退回申请 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_REJECT, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_STATUS_REJECTED, 'reject_remark' => $params['remark']], $user);
            if ($bool === false) {
                throw new BusinessException('批量拒绝退回申请-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 资产台账相关操作
            foreach ($asset_status_codes as $after_status => $item) {
                // 修改资产台账状态
                $asset_ids = implode(',', $item['asset_ids']);
                $before_asset_status = MaterialEnums::ASSET_STATUS_RETURN_ING;
                $bool = $db->updateAsDict(
                    (new MaterialAssetsModel())->getSource(),
                    [
                        'status' => $after_status,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => "id IN ({$asset_ids}) AND status = {$before_asset_status}",
                    ]
                );
                if ($bool === false) {
                    throw new BusinessException('批量拒绝退回申请-修改资产台账状态失败 = ' . json_encode($item['asset_ids'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 记录资产台账操作日志
                $before_data = ['status' => $before_asset_status];
                $after_data = ['status' => $after_status];
                $bool = (new MaterialAssetUpdateLogModel())->dealEditFieldBatch($item['asset_codes'], $before_data, $after_data, $user, MaterialEnums::OPERATE_TYPE_APPLY_RETURN_REJECT);
                if ($bool === false) {
                    throw new BusinessException('批量拒绝退回申请-资产台账-操作记录失败 = ' . json_encode(['asset_codes' => $item['asset_codes'], 'before_data' => $before_data, 'after_data' => $after_data], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            //给资产退回的发起人发送by消息
            $this->sendMessage(array_unique(array_column($return_list, 'apply_id')), MaterialEnums::MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_REJECT);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-batchReject-failed:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-待处理-完成工单
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchFinish($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $return_ids = $params['ids'];
            $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH);

            //关联（待处理，已入库（scm和非scm），已审核）入库单 不可操作
            foreach ($return_list as $return) {
                $this->validateStorage($return['id'], MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH);
            }

            //验证资产
            $asset_ids = array_column($return_list, 'asset_id');
            $assets = $this->validateAssets($asset_ids);

            //处理进度表里非删除的且标记为前端自行寻找的枚举值
            $asset_handle_status = $this->getAssetHandlingStatus(false, ['conditions' => 'is_search = :is_search: and is_deleted = :is_deleted:', 'bind' => ['is_search' => MaterialEnums::RETURN_HANDLING_STATUS_SEARCH_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]]);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            $return_ids_str = implode(',', $return_ids);
            $before_status_received = MaterialEnums::RETURN_STATUS_RECEIVED;
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnModel())->getSource(),
                [
                    'status' => MaterialEnums::RETURN_STATUS_FINISHED,
                    'operator_staff_id' => $user['id'],
                    'operator_staff_name' => $user['name'],
                    'finished_at' => $now_time,
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_received}",
                ]
            );
            $affected_rows = $db->affectedRows();
            if (!$bool || $affected_rows != count($return_ids)) {
                throw new BusinessException('退回申请完成工单 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }


            //每笔资产对应退回申请上的处理进度
            $return_asset_handling_status = array_column($return_list, null, 'asset_id');
            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            foreach ($assets as $asset_info) {
                // 记录退回操作日志
                $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH, [$return_asset_handling_status[$asset_info->id]['id']], ['asset_handling_status' => $return_asset_handling_status[$asset_info->id]['asset_handling_status']], $user);
                if ($bool === false) {
                    throw new BusinessException('退回申请完成工单-保存退回操作记录失败 = ' . json_encode($return_asset_handling_status[$asset_info->id]['id'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 修改资产台账状态
                $log_asset = clone $asset_info;
                $asset_update = [
                    'status' => $asset_handle_status[$return_asset_handling_status[$asset_info->id]['asset_handling_status']]['use_status'],
                    'updated_at' => $now_time
                ];
                $bool = $asset_info->i_update($asset_update);
                if ($bool === false) {
                    throw new BusinessException('退回申请完成工单-资产台账更新-保存失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
                }

                // 记录资产台账操作日志
                $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
                $bool = $material_asset_update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_APPLY_RETURN_FINISH, $log_asset, $asset_update, $user);
                if ($bool === false) {
                    throw new BusinessException('退回申请完成工单-资产台账更新-保存操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();

            //给资产退回的发起人发送by消息
            $this->sendMessage(array_unique(array_column($return_list, 'apply_id')), MaterialEnums::MATERIAL_ASSET_RETURN_MESSAGE_CATEGORY_FINISHED);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-batchFinish-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-待处理-处理
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function assetHandle($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //处理进度表里非删除的且标记为前端自行寻找的枚举值
            $asset_handle_status = $this->getAssetHandlingStatus(false, ['conditions' => 'is_search = :is_search: and is_deleted = :is_deleted:', 'bind' => ['is_search' => MaterialEnums::RETURN_HANDLING_STATUS_SEARCH_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]]);
            if (!in_array($params['asset_handling_status'], array_keys($asset_handle_status))) {
                throw new ValidationException(static::$t->_('material_asset_handling_status_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            $return_info = $this->validateAssetHandleOrTransfer($params);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            //修改退回申请
            $return_update_data = [
                'asset_handling_status' => $params['asset_handling_status'],
                'asset_handling_remark' => $params['asset_handling_remark'] ?? '',
                'updated_at' => $now_time
            ];
            $bool = $return_info->i_update($return_update_data);
            if ($bool === false) {
                throw new BusinessException('资产退回处理-保存失败 = ' . json_encode($return_update_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_info), ErrCode::$BUSINESS_ERROR);
            }

            //删除原有的处理附件
            $bool = $return_info->getHandleAttachment()->delete();
            if ($bool === false) {
                throw new BusinessException('资产退回处理-删除原来的附件-失败', ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回申请附件
            if (!empty($params['attachments'])) {
                $attachArr = [];
                foreach ($params['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialEnums::OSS_MATERIAL_TYPE_RETURN_HANDLE;
                    $tmp['oss_bucket_key'] = $return_info->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $tmp['object_url'] = $attachment['object_url'];
                    $tmp['created_at'] = $now_time;
                    $attachArr[] = $tmp;
                }
                $material_attachment = new MaterialAttachmentModel();
                $bool = $material_attachment->batch_insert($attachArr);
                if ($bool === false) {
                    throw new BusinessException('资产退回处理-保存附件-失败 = ' . json_encode($attachArr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditField(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_HANDLE, $return_info, ['asset_handling_status' => $params['asset_handling_status'], 'asset_handling_remark' => $params['asset_handling_remark'] ?? '', 'asset_handling_attachment' => array_column($params['attachments'], 'object_url')], $user);
            if ($bool === false) {
                throw new BusinessException('资产退回处理-保存退回操作记录失败 = ' . json_encode($return_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-assetHandle-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 退回资产处理-待处理-转交
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function transfer($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //转交后新的处理部门信息
            $group_info = MaterialSettingService::getInstance()->getReturnRepairGroupInfo($params['group_id']);

            $return_info = $this->validateAssetHandleOrTransfer($params, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_TRANSFER);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            $transfer_at = date('Y-m-d');
            $old_now_group_id = $return_info->now_group_id;//原现处理部门
            //记录转交记录表
            $transfer_log_model = new MaterialAssetReturnTransferLogModel();
            $transfer_log_data = [
                'apply_date' => $transfer_at,
                'return_id' => $return_info->id,
                'staff_id' => $user['id'],
                'staff_name' => $user['name'],
                'node_department_id' => $user['node_department_id'],
                'node_department_name' => $user['department'],
                'group_id' => $return_info->now_group_id,
                'now_group_id' => $params['group_id'],
                'reason' => $params['transfer_reason'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_log_model->i_create($transfer_log_data);
            if ($bool === false) {
                throw new BusinessException('资产退回转交-转交记录表-保存失败 = ' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($transfer_log_model), ErrCode::$BUSINESS_ERROR);
            }

            //修改退回申请
            $return_update_data = [
                'now_group_id' => $params['group_id'],
                'transfer_log_id' => $transfer_log_model->id,
                'transfer_staff_id' => $user['id'],
                'transfer_staff_name' => $user['name'],
                'transfer_node_department_id' => $user['node_department_id'],
                'transfer_node_department_name' => $user['department'],
                'transfer_at' => $now_time,
                'transfer_reason' => $params['transfer_reason'],
                'updated_at' => $now_time
            ];
            $bool = $return_info->i_update($return_update_data);
            if ($bool === false) {
                throw new BusinessException('资产退回转交-保存失败 = ' . json_encode($return_update_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_info), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditField(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_TRANSFER, $return_info, ['transfer_reason' => $params['transfer_reason']], $user);
            if ($bool === false) {
                throw new BusinessException('资产退回转交-保存退回操作记录失败 = ' . json_encode($params['id'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();

            //发送邮件
            if ($group_info->email) {
                try {
                    $this->sendTransferEmail($return_info, $old_now_group_id, $group_info);
                } catch (\Exception $e) {
                    $this->logger->warning('转交时给新处理部门的联系邮箱发邮件 异常：' . $e->getMessage() . $e->getTraceAsString());
                }
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-transfer-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回处理-待处理-创建scm退库单-初始化信息
     * @param array $params 参数
     * @param array $user 登陆者信息组
     * @return array
     */
    public function getStorageDefault($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            $items = [];
            $return_list = $this->validateAssetReturn($params['ids'], MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE);
            $name_filed = get_lang_field_name('name_', static::$language);
            foreach ($return_list as $return) {
                $items[] = [
                    'barcode' => $return['barcode'],
                    'name' => $return[$name_filed] ? $return[$name_filed] : $return['name_en'],
                    'model' => $return['model'],
                    'asset_code' => $return['asset_code'],
                    'old_asset_code' => $return['old_asset_code'],
                    'sn_code' => $return['sn_code'],
                ];
            }

            $data['apply_date'] = date('Y-m-d');
            $data['no'] = static::genSerialNo('ART', RedisKey::ASSET_RETURN_WAIT_CREATE_STORAGE_CREATE_COUNTER, 4, date('ymd'));
            $data['apply_id'] = $user['id'];
            $data['apply_name'] = $user['name'];
            $data['node_department_id'] = $user['node_department_id'];
            $data['node_department_name'] = $user['department'];

            //公司信息
            $department_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
            $data['company_id'] = $department_info['company_id'] ?? 0;
            $data['company_name'] = $department_info['company_name'] ?? '';

            //网点信息
            $data['sys_store_id'] = $user['sys_store_id'];
            if ($data['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $data['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            } else {
                $store_info = (new StoreRepository())->getStoreDetail($data['sys_store_id']);
                $data['store_name'] = $store_info['name'] ?? '';
            }
            //退回单列表
            $data['items'] = $items;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('reimbursement-get-default-data-failed:' . $e->getMessage() . $e->getTraceAsString());
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 退回资产处理-待处理-创建scm退库单
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function createStorage($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 验证单号是否已创建 或 占用
            $exist_data_by_no = MaterialAssetReturnStorageModel::findFirst([
                'conditions' => 'no = :no:',
                'bind' => ['no' => $params['no']],
                'columns' => ['id']
            ]);
            if (!empty($exist_data_by_no)) {
                throw new ValidationException(static::$t->_('material_asset_return_storage_no_exist_hint', ['no' => $params['no']]), ErrCode::$VALIDATE_ERROR);
            }


            //验证退回申请单
            $return_ids = $params['return_ids'];
            $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE);
            foreach ($return_list as $return) {
                $this->validateStorage($return['id']);
            }

            //验证资产
            $asset_ids = array_column($return_list, 'asset_id');
            $assets = $this->validateAssets($asset_ids);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');

            //若传递了货主，需要传输至SCM入库
            $mach_code = $params['mach_code'] ?? '';
            $scm_no = '';
            $update_to_scm = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            if (!empty($mach_code)) {
                $update_to_scm = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
                $barcode_nums = [];
                foreach ($return_list as $return_info) {
                    if (isset($barcode_nums[$return_info['barcode']])) {
                        $barcode_nums[$return_info['barcode']]['num'] += 1;
                        $barcode_nums[$return_info['barcode']]['asset'][] = $return_info['asset_code'];
                    } else {
                        $barcode_nums[$return_info['barcode']]['num'] = 1;
                        $barcode_nums[$return_info['barcode']]['asset'][] = $return_info['asset_code'];
                    }
                }
                $goods = [];
                $i = 0;
                foreach ($barcode_nums as $barcode => $one_barcode_info) {
                    $i++;
                    $goods[] = [
                        'i' => $i,
                        'barCode' => $barcode,
                        'num' => $one_barcode_info['num'],
                        'price' => 100,
                        'asset' => $one_barcode_info['asset']
                    ];
                }
                //调取SCM出库单接口
                $scm_no = (new ScmService())->createStorage($mach_code, [
                    'warehouseId' => $params['stock_id'],
                    'type' => "3",
                    'qualityStatus' => MaterialEnums::$scm_quality_status[$params['quality_status']],
                    'orderSn' => $params['no'],
                    'channelSource' => 'FlashOA',
                    'arrivalStart' => date('Y-m-d H:i'),
                    'arrivalEnd' => date('Y-m-d H:i', strtotime('+1 hour')),
                    'remark' => $params['remark'] ?? '',
                    'deliveryNumber' => $params['delivery_number'] ?? '',
                    'goods' => $goods,
                ]);
            }

            //添加SCM入库单
            $return_storage = new MaterialAssetReturnStorageModel();
            $return_storage_data = [
                'apply_date' => $params['apply_date'],
                'no' => $params['no'],
                'scm_no' => $scm_no,
                'apply_id' => $params['apply_id'],
                'apply_name' => $params['apply_name'],
                'company_id' => $params['company_id'] ?? 0,
                'company_name' => $params['company_name'] ?? '',
                'node_department_id' => $params['node_department_id'],
                'node_department_name' => $params['node_department_name'],
                'sys_store_id' => $params['sys_store_id'],
                'store_name' => $params['store_name'],
                'mach_code' => $mach_code,
                'mach_name' => $params['mach_name'] ?? '',
                'stock_id' => $params['stock_id'] ?? '',
                'stock_name' => $params['stock_name'] ?? '',
                'remark' => $params['remark'] ?? '',
                'quality_status' => $params['quality_status'],
                'update_to_scm' => $update_to_scm,
                'status' => ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? MaterialEnums::RETURN_STORAGE_STATUS_WAIT : MaterialEnums::RETURN_STORAGE_STATUS_NOT_SCM,
                'delivery_number' => $params['delivery_number'] ?? '',
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $return_storage->i_create($return_storage_data);
            if ($bool === false) {
                throw new BusinessException('资产退回创建scm退库单-保存失败 = ' . json_encode($return_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_storage), ErrCode::$BUSINESS_ERROR);
            }

            //添加SCM入库单明细
            $return_storage_product_data = [];
            foreach ($return_ids as $return_id) {
                $return_storage_product_data[] = [
                    'return_storage_id' => $return_storage->id,
                    'return_id' => $return_id,
                    'this_time_num' => 1,
                    'real_quantity_received' => ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? 0 : 1,
                    'real_arrival_date' =>  ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) ? null : $params['apply_date'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
            }
            $return_storage_product = new MaterialAssetReturnStorageProductModel();
            $bool = $return_storage_product->batch_insert($return_storage_product_data);
            if ($bool === false) {
                throw new BusinessException('资产退回创建scm退库单-保存明细失败 = ' . json_encode($return_storage_product_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_storage_product), ErrCode::$BUSINESS_ERROR);
            }

            if ($update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                //传输至SCM 修改退回申请单 将资产的处理进度变成“入库中”
                $return_ids_str = implode(',', $return_ids);
                $before_status_received = MaterialEnums::RETURN_STATUS_RECEIVED;
                $bool = $db->updateAsDict(
                    (new MaterialAssetReturnModel())->getSource(),
                    [
                        'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_IN_STORAGE,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_received}",
                    ]
                );
                $affected_rows = $db->affectedRows();
                if (!$bool || $affected_rows != count($return_ids)) {
                    throw new BusinessException('资产退回创建scm退库单 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 记录退回操作日志
                $material_asset_return_recode = new MaterialAssetReturnRecordModel();
                $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CREATE, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_IN_STORAGE, 'storage_remark' => $params['remark']], $user);
                if ($bool === false) {
                    throw new BusinessException('资产退回创建scm退库单-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                // 修改资产台账状态 资产的使用状态为“入库中”
                foreach ($assets as $asset_info) {
                    // 修改资产台账状态
                    $log_asset = clone $asset_info;
                    $asset_update = [
                        'status' => MaterialEnums::ASSET_STATUS_IN_STORAGE,
                        'updated_at' => $now_time
                    ];
                    $bool = $asset_info->i_update($asset_update);
                    if ($bool === false) {
                        throw new BusinessException('资产退回创建scm退库单-资产台账更新-保存失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
                    }

                    // 记录资产台账操作日志
                    $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
                    $bool = $material_asset_update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_APPLY_RETURN_STORAGE_CREATE, $log_asset, $asset_update, $user);
                    if ($bool === false) {
                        throw new BusinessException('资产退回创建scm退库单-资产台账更新-保存操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }
            } else {
                //非传输至SCM
                $this->doneStorage($db, $return_storage, $return_ids, $assets, $user, $now_time);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-createStorage-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 完成入库（非SCM、SCM回调）
     * @param object $db db对象
     * @param object $return_storage SCM入库单信息
     * @param array $return_ids 关联的退回ID组
     * @param object $assets 退回关联的资产对象
     * @param array $user 操作者信息组
     * @param string $now_time 操作时间
     * @param int $type 24退回入库完成（非SCM） 25退回入库完成（SCM）
     * @return bool
     * @throws BusinessException
     */
    public function doneStorage($db, $return_storage, $return_ids, $assets, $user, $now_time, $type = MaterialEnums::OPERATE_TYPE_APPLY_RETURN_STORAGE_DONE_NOT_SCM)
    {
        // 修改退回申请单 将退回申请中该资产的标识改成“已完成”，同时将资产的处理进度变成“完成入库”
        $return_ids_str = implode(',', $return_ids);
        $before_status_received = MaterialEnums::RETURN_STATUS_RECEIVED;
        $bool = $db->updateAsDict(
            (new MaterialAssetReturnModel())->getSource(),
            [
                'status' => MaterialEnums::RETURN_STATUS_FINISHED,
                'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_FINISHED,
                'return_storage_id' => $return_storage->id,
                'return_storage_no' => $return_storage->no,
                'return_storage_scm_no' => $return_storage->scm_no,
                'operator_staff_id' => $user['id'],
                'operator_staff_name' => $user['name'],
                'finished_at' => $now_time,
                'updated_at' => $now_time
            ],
            [
                'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_received}",
            ]
        );
        $affected_rows = $db->affectedRows();
        if (!$bool || $affected_rows != count($return_ids)) {
            throw new BusinessException('资产退回完成入库 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 记录退回操作日志
        $material_asset_return_recode = new MaterialAssetReturnRecordModel();
        $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_DONE, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_FINISHED, 'storage_remark' => $return_storage->remark], $user);
        if ($bool === false) {
            throw new BusinessException('资产退回完成入库-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        //在转移记录表里，新增一条已接收的转移记录
        $transfer_log_data = [];//转移记录-行信息
        $transfer_batch = new MaterialAssetTransferBatchModel(); //记录转移记录-头信息
        $batch_data = [
            'staff_id' => $user['id'],
            'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
            'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
            'status' => MaterialEnums::TRANSFER_BATCH_STATUS_RECEIVED,
            'mark' => $return_storage->no . $return_storage->remark,
            'created_at' => $now_time,
            'updated_at' => $now_time
        ];
        $bool = $transfer_batch->i_create($batch_data);
        if ($bool === false) {
            throw new BusinessException('资产退回完成入库-记录转移头信息失败 可能的原因是=' . get_data_object_error_msg($transfer_batch) . ';数据是=' . json_encode($batch_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        $fromStaff = array_column($assets->toArray(), 'staff_id');
        $staffData = [];
        if(!empty($fromStaff)){
            //查询员工协议公司
            $staffData = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, contract_company_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind' => ['ids' => $fromStaff]
            ])->toArray();
            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
        }
        //获取公司名称
        $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

        // 修改资产台账状态 资产的使用状态为“闲置”，将入库单上的正品/残品字段带到资产卡片上，清空资产台账上的使用人字段。
        foreach ($assets as $asset_info) {
            // 修改资产台账状态
            $log_asset = clone $asset_info;
            $asset_update = [
                'status' => MaterialEnums::ASSET_STATUS_UNUSED,
                'staff_id' => 0,
                'staff_name' => '',
                'quality_status' => $return_storage->quality_status,
                'updated_at' => $now_time
            ];
            $bool = $asset_info->i_update($asset_update);
            if ($bool === false) {
                throw new BusinessException('资产退回完成入库-资产台账更新-保存失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
            }

            // 记录资产台账操作日志
            $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
            $bool = $material_asset_update_log_model->dealEditField($type, $log_asset, $asset_update, $user);
            if ($bool === false) {
                throw new BusinessException('资产退回完成入库-资产台账更新-保存操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //转移记录行信息组装
            $transfer_log_data[] = [
                'batch_id' => $transfer_batch->id,
                'asset_id' => $log_asset->id,
                'barcode' => $log_asset->bar_code,
                'asset_code' => $log_asset->asset_code,
                'from_staff_id' => $log_asset->staff_id,
                'from_staff_name' => $log_asset->staff_name,
                'from_node_department_id' => $log_asset->node_department_id,
                'from_node_department_name' => $log_asset->node_department_name,
                'from_contract_company_id' => $staffData[$log_asset->staff_id] ?? 0,//调用人 协议公司 id
                'from_contract_company_name' => $companyList[$staffData[$log_asset->staff_id]] ?? '',//协议公司名称
                'from_sys_store_id' => $log_asset->sys_store_id,
                'from_store_name' => $log_asset->store_name,
                'from_pc_code' => $log_asset->pc_code,
                'from_use_land' => $log_asset->use_land,
                'from_company_id' => $log_asset->company_id,
                'from_company_name' => $log_asset->company_name,
                'to_staff_id' => 0,
                'to_staff_name' => '',
                'to_node_department_id' => $log_asset->node_department_id,
                'to_node_department_name' => $log_asset->node_department_name,
                'to_sys_store_id' => $log_asset->sys_store_id,
                'to_store_name' => $log_asset->store_name,
                'to_pc_code' => $log_asset->pc_code,
                'to_use_land' => $log_asset->use_land,
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                'to_company_id' => $log_asset->company_id,
                'to_company_name' => $log_asset->company_name,
                'finished_at' => $now_time,
                'operator_id' => $user['id'],
                'transfer_remark' => $return_storage->no . $return_storage->remark,
                'transfer_operator_id' => $user['id'],
                'transfer_method' => MaterialEnums::TRANSFER_METHOD_POST,
                'transfer_type' => MaterialEnums::TRANSFER_TYPE_RETURN,//退回入库
                'transfer_at' => $now_time,
                'auto_recipient' => MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_NO,
                'transfer_operator_name' => $user['name'],
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
        }

        //记录转移记录-行信息
        $transfer_log = new MaterialAssetTransferLogModel();
        $bool = $transfer_log->batch_insert($transfer_log_data);
        if ($bool === false) {
            throw new BusinessException('资产退回完成入库-记录转移行信息失败 可能的原因是=' . get_data_object_error_msg($transfer_log) . ';数据是=' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }

    /**
     * scm 回调处理入库单
     * @param array $params 参数组
     * @return array
     */
    public function updateStorageOrder($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询当前入库单
            $return_storage = MaterialAssetReturnStorageModel::findFirst([
                'conditions' => 'scm_no = :orderSn: and no = :no: and status = :status: and is_deleted = :is_deleted:',
                'bind' => ['orderSn' => $params['orderSn'], 'no' => $params['externalOrderSn'], 'status' => MaterialEnums::RETURN_STORAGE_STATUS_AUDIT, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (empty($return_storage)) {
                throw new ValidationException('scm单号不存在或状态不对' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }

            //获取退库单关联的退回申请单据
            $return_storage_product_list = $return_storage->getProducts()->toArray();
            $return_ids = array_column($return_storage_product_list, 'return_id');
            $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_FINISH);

            //验证资产
            $asset_ids = array_column($return_list, 'asset_id');
            $assets = $this->validateAssets($asset_ids);

            $error = $scm_asset_unmatched = $update_return_storage_product_ids = [];
            $real_arrival_date = null;
            $asset_list = array_column($return_list, null, 'asset_code');
            $return_storage_product_list = array_column($return_storage_product_list, null, 'return_id');
            $now_time = date('Y-m-d H:i:s');
            foreach ($params['goods'] as $good) {
                //scm没有回传资产码
                if (!$good['asset']) {
                    $scm_asset_unmatched[] = [
                        'scm_return_json' => json_encode($params),
                        'order_sn' => $return_storage->scm_no,
                        'psno' => $return_storage->no,
                        'bar_code' => $good['barCode'],
                        'asset_code' => '',
                        'sn_code' => '',
                        'reason' => '没有回传资产码',
                        'source_type' => 3,
                        'created_at' => $now_time,
                        'call_date' => $now_time,
                    ];
                    $error [] = $return_storage->no . '的' . $good['barCode'] . '没有回传资产码';
                    continue;
                }
                //scm有回传资产码
                foreach ($good['asset'] as $item) {
                    //scm回传的资产码跟oa的资产码进行匹配
                    if (!isset($asset_list[$item['assetCode']])) {
                        $error [] = $return_storage->no . '的回传的' . $good['barCode'] . '资产码不匹配';
                        $scm_asset_unmatched[] = [
                            'scm_return_json' => json_encode($params),
                            'order_sn' => $return_storage->scm_no,
                            'psno' => $return_storage->no,
                            'bar_code' => $good['barCode'],
                            'asset_code' => $item['assetCode'],
                            'sn_code' => $good['barCode'],
                            'reason' => '回传的资产码不匹配',
                            'source_type' => 3,
                            'created_at' => $now_time,
                            'call_date' => $now_time,
                        ];
                        continue;
                    }

                    //资产能匹配上的更新实际收货数量、退回申请
                    $update_return_storage_product_ids[] = $return_storage_product_list[$asset_list[$item['assetCode']]['id']]['id'];//SCM入库单-明细ID组
                    $real_arrival_date = $real_arrival_date ? $real_arrival_date : $item['real_arrival_date'];//实际收货日期

                    //回传的资产码上sn码不对
                    if ($item['snCode'] != $asset_list[$item['assetCode']]['sn_code']) {
                        $error [] = $return_storage->no . '的上' . $good['barCode'] . '回传的' . $item['assetCode'] . '的sn码不一致。';
                        $scm_asset_unmatched[] = [
                            'scm_return_json' => json_encode($params),
                            'order_sn' => $return_storage->scm_no,
                            'psno' => $return_storage->no,
                            'bar_code' => $good['barCode'],
                            'asset_code' => $item['assetCode'],
                            'sn_code' => $good['barCode'],
                            'reason' => '回传的资产码上sn码不对',
                            'source_type' => 3,
                            'created_at' => $now_time,
                            'call_date' => $now_time,
                        ];
                    }
                }
            }

            if ($error) {
                $this->logger->notice('asset-return-updateStorageOrder: '. implode('，', array_unique($error)));
            }

            // 所有验证通过, 开始数据变更
            // 修改scm入库单状态为已入库(SCM)
            $return_storage_data = [
                'status' => MaterialEnums::RETURN_STORAGE_STATUS_SCM,
                'updated_at' => $now_time
            ];
            $bool = $return_storage->i_update($return_storage_data);
            if ($bool === false) {
                throw new BusinessException('资产退回回调处理退库单-保存失败 = ' . json_encode($return_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_storage), ErrCode::$BUSINESS_ERROR);
            }

            //将匹配上的资产码对应的OA入库单行的实际入库数量=1
            if ($update_return_storage_product_ids) {
                $update_return_storage_product_ids_str = implode(',', $update_return_storage_product_ids);
                $bool = $db->updateAsDict(
                    (new MaterialAssetReturnStorageProductModel())->getSource(),
                    [
                        'real_quantity_received' => 1,
                        'real_arrival_date' => $real_arrival_date ? date('Y-m-d', strtotime($real_arrival_date)) : null,
                        'updated_at' => $now_time
                    ],
                    [
                        'conditions' => "id IN ({$update_return_storage_product_ids_str})",
                    ]
                );
                if ($bool === false) {
                    throw new BusinessException('资产退回回调处理退库单-保存实际入库数量失败 = ' . json_encode($update_return_storage_product_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //Scm回调匹配不到资产编码数据
            if ($scm_asset_unmatched) {
                $material_scm_asset_unmatched = new MaterialScmAssetUnmatchedModel();
                $bool = $material_scm_asset_unmatched->batch_insert($scm_asset_unmatched);
                if ($bool === false) {
                    throw new BusinessException('资产退回回调处理退库单-保存Scm回调匹配不到资产编码数据失败 = ' . json_encode($scm_asset_unmatched, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($scm_asset_unmatched), ErrCode::$BUSINESS_ERROR);
                }
            }

            //更新退回申请，更新资产台账，生成转移记录
            $user = UserService::getInstance()->getLoginUser($return_storage->apply_id);
            $this->doneStorage($db, $return_storage, $return_ids, $assets, $user, $now_time, MaterialEnums::OPERATE_TYPE_APPLY_RETURN_STORAGE_DONE_SCM);

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-updateStorageOrder-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回处理-退货入库通知单-撤回
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancelStorage($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //检测退库申请信息
            $return_storage = $this->validateReturnStorageInfo($params['id']);

            //已审核的入库单才可撤销
            if ($return_storage->status != MaterialEnums::RETURN_STORAGE_STATUS_AUDIT) {
                throw new ValidationException(static::$t->_('material_asset_return_cancel_storage_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            // 验证申请单
            $return_storage_product_list = $return_storage->getProducts()->toArray();
            $return_ids = array_column($return_storage_product_list, 'return_id');
            $return_list = $this->validateAssetReturn($return_ids, MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL);

            // 验证资产
            $asset_ids = array_column($return_list, 'asset_id');
            $assets = $this->validateAssets($asset_ids);

            //scm撤销入库
            (new ScmService())->cancelStorage($return_storage->mach_code, ['inboundSn' => $return_storage->scm_no]);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');
            // 修改scm入库单状态为撤销
            $return_storage_data = [
                'status' => MaterialEnums::RETURN_STORAGE_STATUS_CANCEL,
                'reason' => $params['reason'],
                'updated_at' => $now_time
            ];
            $bool = $return_storage->i_update($return_storage_data);
            if ($bool === false) {
                throw new BusinessException('资产退回撤销scm退库单-保存失败 = ' . json_encode($return_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_storage), ErrCode::$BUSINESS_ERROR);
            }

            // 修改退回申请
            $return_ids_str = implode(',', $return_ids);
            $before_status_received = MaterialEnums::RETURN_STATUS_RECEIVED;
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnModel())->getSource(),
                [
                    'asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_WAIT_STORAGE,
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_ids_str}) AND status = {$before_status_received}",
                ]
            );
            $affected_rows = $db->affectedRows();
            if (!$bool || $affected_rows != count($return_ids)) {
                throw new BusinessException('资产退回撤销scm退库单 - 变更状态失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_CANCEL, $return_ids, ['asset_handling_status' => MaterialEnums::RETURN_HANDLING_STATUS_WAIT_STORAGE, 'cancel_storage_remark' => $params['reason']], $user);
            if ($bool === false) {
                throw new BusinessException('资产退回撤销scm退库单-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 修改资产台账状态 资产的使用状态为“退回中”
            foreach ($assets as $asset_info) {
                // 修改资产台账状态
                $log_asset = clone $asset_info;
                $asset_update = [
                    'status' => MaterialEnums::ASSET_STATUS_RETURN_ING,
                    'updated_at' => $now_time
                ];
                $bool = $asset_info->i_update($asset_update);
                if ($bool === false) {
                    throw new BusinessException('资产退回撤销scm退库单-资产台账更新-保存失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
                }

                // 记录资产台账操作日志
                $material_asset_update_log_model = new MaterialAssetUpdateLogModel();
                $bool = $material_asset_update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_APPLY_RETURN_STORAGE_CANCEL, $log_asset, $asset_update, $user);
                if ($bool === false) {
                    throw new BusinessException('资产退回撤销scm退库单-资产台账更新-保存操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-cancelStorage-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回处理-退货入库通知单-查看
     * @param array $params 参数
     * @return array
     */
    public function getStorageDetail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $return_storage = $this->validateReturnStorageInfo($params['id']);
            $data = [
                'id' => $return_storage->id,
                'no' => $return_storage->no,
                'scm_no' => $return_storage->scm_no,
                'apply_date' => $return_storage->apply_date,
                'apply_id' => $return_storage->apply_id,
                'apply_name' => $return_storage->apply_name,
                'mach_name' => $return_storage->mach_name,
                'stock_name' => $return_storage->stock_name,
                'remark' => $return_storage->remark,
                'quality_status' => $return_storage->quality_status,
                'quality_status_text' => static::$t->_(MaterialEnums::$quality_status[$return_storage->quality_status] ?? ''),
                'delivery_number' => $return_storage->delivery_number
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-getStorageDetail-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产退回处理-退货入库通知单-查看-退库入库单信息
     * @param array $params 参数
     * @return array
     */
    public function getStorageDetailProduct($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialAssetReturnStorageProductModel::class]);
            $builder->leftJoin(MaterialAssetReturnModel::class, 'return.id = main.return_id', 'return');
            $builder->where('main.is_deleted = :is_deleted: and main.return_storage_id = :return_storage_id:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'return_storage_id' => $params['id']]);
            $count = (int)$builder->columns('count(main.id) as total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $offset = $page_size * ($page_num - 1);
                $name_filed = get_lang_field_name('name_', static::$language);
                $builder->columns('return.barcode, return.asset_code, return.name_en, return .' . $name_filed . ' as name, return.model, return.old_asset_code, return.sn_code, main.this_time_num, main.real_quantity_received');
                $builder->limit($page_size, $offset);
                $builder->orderBy('main.id ASC');
                $items = $builder->getQuery()->execute()->toArray();
                if ($items) {
                    foreach ($items as &$item) {
                        $item['name'] = $item['name'] ? $item['name'] : $item['name_en'];//资产名称当地语言为空，则取英文名称
                    }
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('asset-return-getStorageDetailProduct-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 发送站内信
     * @param array $staff_list 员工工号组
     * @param integer $category 消息类型
     * @return bool
     */
    public function sendMessage($staff_list, $category)
    {
        //无需发送
        if (empty($staff_list)) {
            return true;
        }
        $message_info = MaterialEnums::$return_send_message_title;
        $kit_param = [
            'message_title' => get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? $message_info[$category][0] : $message_info[$category][1],
            'category' => $category,
            'message_content' => ''
        ];

        $staff_users = [];
        foreach ($staff_list as $staff_id) {
            $staff_users[] = ['id' => $staff_id];
        }
        $kit_param['staff_users'] = $staff_users;

        $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
        $bi_rpc->setParams([$kit_param]);
        $res = $bi_rpc->execute();
        $bi_return = $res && isset($res['result']) && $res['result']['code'] == ErrCode::$SUCCESS;
        $res_msg = $bi_return ? '成功' : '失败';
        $res_log = '给员工工号组[' . json_encode($staff_list, JSON_UNESCAPED_UNICODE) . ']；发送[' . json_encode($kit_param, JSON_UNESCAPED_UNICODE) . ']站内信消息；发送时间[' . date('Y-m-d H:i:s') . ']；发送结果[' . $res_msg . ']';
        $this->logger->info($res_log);
        return true;
    }

    /**
     * 转交时给新处理部门的联系邮箱发邮件
     * @param object $return_info 资产退回申请
     * @param integer $old_now_group_id 原现处理部门ID
     * @param object $group_info 现处理部门
     * @return bool
     * @throws BusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function sendTransferEmail($return_info, $old_now_group_id, $group_info)
    {
        //转交前现处理部门
        $now_group_info = MaterialSetReturnGroupRepository::getInstance()->getDetail($old_now_group_id);

        //获取邮件内容信息
        $email_info = MaterialEnums::$return_transfer_email_info;
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $lang = 'th';
            $name_field = 'name_local';
        } else {
            $lang = 'en';
            $name_field = 'name_en';
        }
        $title = $email_info[$lang]['title'] . ' ' . $email_info['zh']['title'];
        $search = ['%group_name%', '%staff_id%', '%staff_name%', '%now_group_name%', '%url%'];
        $url = EnumsService::getInstance()->getSettingEnvValue(GlobalEnums::OA_DASHBOARD_PAGE_URL_CODE, GlobalEnums::OA_DASHBOARD_PAGE_URL_DEFAULT);//各环境各国家dashboard页地址
        $content = str_replace($search, [$now_group_info['name_zh'], $return_info->transfer_staff_id, $return_info->transfer_staff_name, $group_info->name_zh, $url],  $email_info['zh']['content']);
        $content.= str_replace($search, [$now_group_info[$name_field], $return_info->transfer_staff_id, $return_info->transfer_staff_name, $group_info->$name_field, $url],  $email_info[$lang]['content']);
        $header = $email_info['zh']['header'];
        $name_field = get_lang_field_name('name_', static::$language, 'en');
        $rows[] = [
            $return_info->asset_code,
            $return_info->old_asset_code,
            $return_info->sn_code,
            $return_info->$name_field,
            $return_info->transfer_staff_id,
            $return_info->transfer_staff_name,
            $return_info->transfer_node_department_name,
            date('Y-m-d', strtotime($return_info->transfer_at)),
            $return_info->transfer_reason,
            $return_info->apply_id,
            $return_info->apply_name,
            $return_info->node_department_name,
            $return_info->apply_date
        ];
        $rows = array_merge([$email_info[$lang]['header']], $rows);
        $excel_data = $this->exportExcel($header, $rows,'退回转交的附件');
        $content.= '<br/>' . sprintf("<a href='%s'>  %s  </a>", $excel_data['data'], '退回转交的附件');
        $send_res = $this->mailer->sendAsync([$group_info->email], $title, $content);
        $this->logger->info('转交时给新处理部门的联系邮箱发邮件：' . json_encode(['email' => $group_info->email, 'title' => $title, 'content' => $content], JSON_UNESCAPED_UNICODE) . '；发送【' . ($send_res ? '成功' : '失败') . '】');
        return true;
    }

    /**
     * 资产退回处理-退货入库通知单-删除
     * @param array $params 参数
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function deleteStorage($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //检测退库申请信息
            $return_storage = $this->validateReturnStorageInfo($params['id']);

            //已撤销的入库单才可删除
            if ($return_storage->status != MaterialEnums::RETURN_STORAGE_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('material_asset_return_delete_storage_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //scm撤销删除
            (new ScmService())->deleteStorage($return_storage->mach_code, ['inboundSn' => $return_storage->scm_no]);

            // 所有验证通过, 开始数据变更
            $now_time = date('Y-m-d H:i:s');

            // 修改scm入库单状态为删除
            $return_storage_data = [
                'is_deleted' => GlobalEnums::IS_DELETED,
                'updated_at' => $now_time
            ];
            $bool = $return_storage->i_update($return_storage_data);
            if ($bool === false) {
                throw new BusinessException('资产退回删除scm退库单-保存失败 = ' . json_encode($return_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($return_storage), ErrCode::$BUSINESS_ERROR);
            }

            // 修改scm入库单明细行状态为删除
            $return_storage_product_list = $return_storage->getProducts()->toArray();
            $return_storage_product_ids_str = implode(',', array_column($return_storage_product_list, 'id'));
            $bool = $db->updateAsDict(
                (new MaterialAssetReturnStorageProductModel())->getSource(),
                [
                    'is_deleted' => GlobalEnums::IS_DELETED,
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "id IN ({$return_storage_product_ids_str}) AND is_deleted = " . GlobalEnums::IS_NO_DELETED,
                ]
            );
            if ($bool === false) {
                throw new BusinessException('资产退回删除scm退库单 - 明细行 - 变更状态失败 = ' . json_encode($return_storage_product_ids_str, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 记录退回操作日志
            $return_ids = array_column($return_storage_product_list, 'return_id');
            $material_asset_return_recode = new MaterialAssetReturnRecordModel();
            $bool = $material_asset_return_recode->dealEditFieldBatch(MaterialEnums::RETURN_ASSETS_RECORD_TYPE_STORAGE_DELETE, $return_ids, ['storage_deleted' => GlobalEnums::IS_DELETED], $user);
            if ($bool === false) {
                throw new BusinessException('资产退回删除scm退库单-保存退回操作记录失败 = ' . json_encode($return_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('asset-return-deleteStorage-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }
}
