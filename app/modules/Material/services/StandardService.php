<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\MaterialSettingEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobTitleModel;
use App\Models\oa\MaterialSauPermissionModel;
use App\Models\oa\MaterialWmsApplyProductModel;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Models\MaterialSauSkuModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Models\MaterialUpdateLogModel;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchaseStorageProduct;
use App\Models\oa\MaterialSauStorageModel;
use App\Library\Enums\GlobalEnums;
use App\Repository\HrJobTitleRepository;
use App\Repository\oa\MaterialSauPermissionRepository;
use Exception;


class StandardService extends BaseService
{

    const MATERIAL_MAX_PIC = 5;//最多上传5张图片

    const DEFAULT_PARAMETER_ZERO = 0;  //数据库默认参数

    const DEFAULT_PARAMETER_ONE = 1;   //数据库默认参数

    /**
     * 非必要的添加条件
     * @var array
     */
    public static $not_must_add_params = [
        'price',
        'length',
        'width',
        'height',
        'weight',
        'purchase_val',
        'use_val',
        'small_bag_val',
        'big_bag_val',
        'transfer_forbid'
    ];
    /**
     * 添加验证
     */
    public static $validate_material_sau = [
        //基本信息栏
        'category_id' => 'Required|IntGt:0',//物料分类id
        'barcode' => 'Required|StrLenGeLe:1,30',//barcode
        'name_zh' => 'Required|StrLenGeLe:1,100',//中文名称
        'name_en' => 'Required|StrLenGeLe:1,100',//英文名称
        'name_local' => 'StrLenGeLe:0,100',//当地语言名称
        'unit_zh' => 'Required|StrLenGeLe:1,20',//基本单位-中文
        'unit_en' => 'Required|StrLenGeLe:1,20',//基本单位-英文
        'model' => 'StrLenGeLe:0,100',//规格型号
        'category_type' => 'Required|IntIn:1,2,3', //物料类型，1资产，2耗材，3服务类
        'finance_category_id' => 'Required|IntGt:0',//财务分类id
        'purchase_type'=>'Required|IntGt:0',//采购类型
        'brand' => 'StrLenGeLe:0,30',//品牌
        'remark' => 'StrLenGeLe:0,500',//备注
        'price' => 'Regexp:' . MaterialClassifyEnums::PRICE_VALIDATION . '|>>>:price param error 0.00~*********999.999999', //参考价格
        'sap_unit' => 'StrLenGeLe:0,10',//SAP单位
        'update_to_scm' => 'IntIn:1,2',//是否更新至scm，1否，2是
        'update_to_sap' => 'IntIn:1,2',//是否更新至sap，1否，2是
        'update_to_acceptance' => 'IntIn:1,2',//是否验收，1否，2是
        'enable_sn' => 'IntIn:1,2',//是否启用SN，1否，2是
        'currency' => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS,
        'use_scene' => 'Required|IntIn:' . MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY . ',' . MaterialClassifyEnums::MATERIAL_USE_SCENE_BUY . ',' . MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY_AND_BUY,//可申请/购买：0无，1可申请，2可购买，3即可申请又可购买
        'attachments' => 'Required|Arr|ArrLenGeLe:1,'.self::MATERIAL_MAX_PIC,//图片
        'transfer_forbid'=>'IntIn:' . MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_VALIDATE,//是否限制员工互转, 1是，2否

        //财务信息栏
        'length'=>'FloatGe:0',//长
        'length_unit' => 'StrLenGeLe:0,20',//长单位
        'width'=>'FloatGe:0',//宽
        'width_unit' => 'StrLenGeLe:0,20',//宽单位
        'height'=>'FloatGe:0',//高
        'height_unit' => 'StrLenGeLe:0,20',//高单位
        'weight'=>'FloatGe:0',//重量
        'weight_unit' => 'StrLenGeLe:0,20',//重量单位
        'purchase_unit_zh' => 'StrLenGeLe:0,20',//采购管理单位-中文
        'purchase_unit_en' => 'StrLenGeLe:0,20',//采购管理单位-英文
        'purchase_val'=> 'IntGeLe:0,*********',//采购管理单位=()基本单位
        'use_unit_zh'=> 'StrLenGeLe:0,20',//领用单位-中文
        'use_unit_en'=> 'StrLenGeLe:0,20',//领用单位-英文
        'use_val'=> 'IntGeLe:0,*********',//领用单位=()基本单位
        'small_bag_unit_zh'=> 'StrLenGeLe:0,20',//小包装单位-中文
        'small_bag_unit_en'=> 'StrLenGeLe:0,20',//小包装单位-英文
        'small_bag_val'=> 'IntGeLe:0,*********',//小包装单位=()基本单位
        'big_bag_unit_zh'=> 'StrLenGeLe:0,20',//大包装单位-中文
        'big_bag_unit_en'=> 'StrLenGeLe:0,20',//大包装单位-英文
        'big_bag_val'=> 'IntGeLe:0,*********',//大包装单位=()基本单位
    ];

    //校验 公司仓库货主
    public static $validate_material_storage = [
        'storage' => 'Required|Arr|ArrLenGeLe:1,10|>>>:main storage list  error[maximum  10]', //公司 货主 仓库数据
        'storage[*].company_id' => 'Required|IntGt:0',//公司id
        'storage[*].stock_id'   => 'Required|StrLenGeLe:1,10',//仓库id
        'storage[*].stock_name' => 'Required|StrLenGeLe:1,128',//仓库id
        'storage[*].mach_code'  => 'Required|StrLenGeLe:1,128',//货主code
        'storage[*].mach_name'  => 'Required|StrLenGeLe:1,128',//货主名称
    ];


    /**
     * 修改、详情、删除校验验证
     */
    public static $validate_material_sau_save = [
        'id' => 'Required|IntGt:0',
    ];

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'category_id',
        'finance_category_id',
        'status',
        'update_to_scm',
        'update_to_acceptance',
        'category_type',
        'ids'
    ];

    /**
     * 列表搜索校验
     */
    public static $validate_list_search_material_sau = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'category_id' => 'Arr', //物料分类表ID
        'finance_category_id' => 'Arr', //财务分类表ID
        'name' => 'StrLenGeLe:0,30', //物料名称
        'barcode' => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'model' => 'StrLenGeLe:0,50', //规格型号
        'status' => 'IntIn:1,2', //启用状态：1启用，2禁用
        'update_to_scm' => 'IntIn:1,2', //是否更新至SCM，1否，2是
        'update_to_acceptance' => 'IntIn:1,2', //是否验收，1否，2是
        'category_type' => 'IntIn:1,2,3',//物料类型，1资产，2耗材，3服务类
        'ids' => 'Arr|ArrLenGeLe:0,500'//按照特定工号筛选，导出勾选最多500个
    ];

    /**
     * 启用禁用校验
     */
    public static $validate_setup_material_sau = [
        'id' => 'Required|IntGt:0',                           //id
        'status' => 'Required|IntIn:1,2',                      //启用状态：1启用，2禁用
    ];

    /**
     * 资产可申请职位
     * @var array
     */
    public static $validate_sau_job = [
        'job' => 'Arr|ArrLenGeLe:0,100|>>>:main job list  error[maximum  100]', //职位id组
        'job[*]' => 'IntGt:0',//职位id
    ];

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取sau添加、编辑验证参数
     * @param array $params sau参数组
     * @return array
     */
    public function validateMaterialSau($params)
    {
        $validate = self::$validate_material_sau;
        //16712需求货主、仓库必填增加同步至scm=是&&物料类型=耗材;17404,网点分仓规则=0未开启时才是必填项
        $material_store_storage_open_status = WarehouseDivisionRuleService::getInstance()->getStoreStorageOpenStatus();
        if ($params['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $params['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $material_store_storage_open_status == MaterialSettingEnums::STORE_STORAGE_CLOSE) {
            $validate = array_merge($validate, self::$validate_material_storage);
        }

        //16850需求，资产类的才需要传递职位信息但非必填
        if ($params['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
            $validate = array_merge($validate, self::$validate_sau_job);
            //设置职位则是否包含职位参数必须有值
            if (!empty($params['job'])) {
                $validate['is_contain_job'] = 'Required|IntIn:' . MaterialClassifyEnums::IS_CONTAIN_JOB_YES . ',' . MaterialClassifyEnums::IS_CONTAIN_JOB_NO;
            }
        }
        return $validate;
    }

    /**
     * 检测barcode是否存在
     * @param string $barcode barcode
     * @return bool
     * @throws ValidationException
     */
    public function isExistsBarcode($barcode)
    {
        $exists = MaterialSauModel::findFirst([
            'conditions' => 'barcode = :barcode: and is_deleted=:is_deleted:',
            'columns' => 'id',
            'bind' => ['barcode' => trim($barcode), 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        if(empty($exists)) {
            return true;
        }else{
            throw new ValidationException(self::$t['material_sau_barcode_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }
    }

    /**
     * 其它额外参数验证
     * @param array $data 请求入参
     * @throws ValidationException
     */
    public function extendValidation($data)
    {
        $barcode = trim($data['barcode']);//大包装英文单位
        if (empty($barcode)) {
            throw new ValidationException(self::$t['material_barcode_not_null'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }
        $big_bag_unit_en = trim($data['big_bag_unit_en']);//大包装英文单位
        $small_bag_unit_en = trim($data['small_bag_unit_en']);//小包装英文单位
        $unit_en = trim($data['unit_en']);//基本单位

        if (!empty($big_bag_unit_en) && empty($small_bag_unit_en)) {
            //如果大包装英文单位传递了，但是小包装英文单位未传递
            throw new ValidationException(self::$t['material_sau_unit_required'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }

        if (!empty($small_bag_unit_en || !empty($big_bag_unit_en)) && ($small_bag_unit_en == $unit_en || $small_bag_unit_en == $big_bag_unit_en || $big_bag_unit_en == $unit_en)) {
            //基本、大、小单位均不能一致
            throw new ValidationException(self::$t['material_sau_unit_repeat'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }

        $small_bag_val = isset($data['small_bag_val']) ? $data['small_bag_val'] : 0;
        $big_bag_val = isset($data['big_bag_val']) ? $data['big_bag_val'] : 0;
        if (($small_bag_val && $small_bag_val <= 1) || ($big_bag_val && $big_bag_val <= 1)) {
            //小包装、大包装换算值均不可为1
            throw new ValidationException(self::$t['material_sau_conversion_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }

        /**
         * 为了和scm的校验保持一致，长、宽、高逻辑：如果单位选择的是mm，则不保留小数。
         * 如果单位选择的是cm，则保留1位小数。如果单位选择的是m，则保留2位小数。最大为2000mm。
         * 否则点击保存时，提示toast“长宽高超过系统最大限定2000mm，请重新输入”
         */
        $length = isset($data['length']) && !empty($data['length'])? $data['length'] : 0.00;
        $length_unit = isset($data['length_unit']) && !empty($data['length_unit'])? $data['length_unit'] : 'mm';
        $width = isset($data['width']) && !empty($data['width'])? $data['width'] : 0.00;
        $width_unit = isset($data['width_unit']) && !empty($data['width_unit'])? $data['width_unit'] : 'mm';
        $height = isset($data['height']) && !empty($data['height'])? $data['height'] : 0.00;
        $height_unit = isset($data['height_unit']) && !empty($data['height_unit'])? $data['height_unit'] : 'mm';
        $coefficient_config = MaterialClassifyEnums::$reduced_unit;
        $length = bcmul($length, $coefficient_config[$length_unit],2);//获取按照单位换算出来的长
        $width = bcmul($width, $coefficient_config[$width_unit],2);//获取按照单位换算出来的宽
        $height = bcmul($height, $coefficient_config[$height_unit],2);//获取按照单位换算出来的高
        $max_val = 2000;
        if (bccomp($max_val, $length, 2) === -1 || bccomp($max_val, $width, 2) === -1 || bccomp($max_val, $height, 2) === -1 ) {
            //长宽高超过系统最大限定2000mm，请重新输入
            throw new ValidationException(self::$t['material_sau_length_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }
        /**
         * 为了和scm的校验保持一致，重量逻辑：如果单位选择的是g，则不保留小数。
         * 如果单位选择的是kg，则保留3位小数。最大值为*********g。
         * 否则点击保存时，提示toast“重量超过系统最大限定*********g，请重新输入”
         */
        $weight = isset($data['weight']) && !empty($data['weight'])? $data['weight'] : 0.000;
        $weight_unit = isset($data['weight_unit']) && !empty($data['weight_unit'])? $data['weight_unit'] : 'g';
        $weight = bcmul($weight, $coefficient_config[$weight_unit],3);
        if (bccomp(*********, $weight, 3) === -1) {
            //重量超过系统最大限定*********g，请重新输入
            throw new ValidationException(self::$t['material_sau_weight_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
        }

        //16850需求，资产类barcode下需要记录可申请职位信息
        if ($data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && !empty($data['job'])) {
            $find_job_count = HrJobTitleModel::count([
                'conditions' => 'id in ({ids:array}) and status = :status:',
                'bind' => ['ids' => $data['job'], 'status' => StaffInfoEnums::HR_JOB_STATUS_OPEN]
            ]);
            if ($find_job_count != count($data['job'])) {
                throw new ValidationException(self::$t['material_sau_job_error'], ErrCode::$VALIDATE_ERROR);
            }
        }
    }

    /**
     * 新增标准型号
     * @param array $data 标准型号入库参数
     * @param array $user 当前登陆者信息组
     * @Date: 2021-10-18 17:17
     * @return array
     * @author: peak pan
     */
    public function sauAdd(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $sau_data = $sau_sku = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //校验基本单位、小包装单位、大包装单位、长宽高、重量传递合法性，用于适配SCM
            $this->extendValidation($data);
            if (!empty($data['storage'])) {
                $data['storage'] = $this->extendStorageValidation($data['storage']);
            }
            //检测barcode是否存在
            $this->isExistsBarcode($data['barcode']);
            //检测所选择的物料分类或财务分类是否是最末级分类
            if ($this->is_existe_son($data)) {
                throw new ValidationException(self::$t['material_sau_category_not_last'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }

            //是否启用sn,非资产类，sn不开启；资产类按照前端业务选择为准
            if ($data['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                $data['enable_sn'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            $now = date('Y-m-d H:i:s', time());
            //组装标准型号参数组
            $update_to_scm = $data['update_to_scm'] ?? MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;//更新至SCM
            $enable_asset_code = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;//是否启用sn码/是否启用资产码：默认为否
            //资产类 && 同步至SCM=是时，是否启用资产码开启；其它均不开启
            if ($data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && $update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $enable_asset_code = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            }

            $sau_data = [
                'category_id' => $data['category_id'],
                'finance_category_id' => $data['finance_category_id'],
                'barcode' => $data['barcode'],
                'name_zh' => $data['name_zh'],
                'name_en' => $data['name_en'],
                'name_local' => $data['name_local'] ?? '',
                'model' => $data['model'],
                'unit_zh' => $data['unit_zh'],
                'unit_en' => $data['unit_en'],
                'category_type' => $data['category_type'],
                'purchase_type' => $data['purchase_type'],
                'brand' => $data['brand'] ?? '',
                'remark' => $data['remark'] ?? '',
                'price' => $data['price'] ?? 0.00,
                'sap_unit' => $data['sap_unit'],
                'currency' => $data['currency'],
                'update_to_scm' => $update_to_scm,
                'update_to_sap' => $data['update_to_sap'] ?? self::DEFAULT_PARAMETER_ONE,
                'update_to_acceptance' => $data['update_to_acceptance'] ?? self::DEFAULT_PARAMETER_ONE,
                'enable_asset_code' => $enable_asset_code,//是否启用资产码
                'enable_sn' => $data['enable_sn'] ?? self::DEFAULT_PARAMETER_ONE,
                'status' => self::DEFAULT_PARAMETER_ONE,//默认为启用
                'is_send_sap' => $this->checkSendSap($data['update_to_sap'] ?? self::DEFAULT_PARAMETER_ONE, $data['finance_category_id']),//检测是否需要发送至SAP
                'is_deleted' => $data['is_deleted'] ?? self::DEFAULT_PARAMETER_ZERO,
                'created_at' => $now,
                'updated_at' => $now,
                'transfer_forbid' => $data['transfer_forbid'] ?? MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO,
                'use_scene' => $data['use_scene'] ?? 0,
                'is_contain_job' => $data['is_contain_job'] ?? 0,
            ];
            $main_model = new MaterialSauModel();
            $bool = $main_model->i_create($sau_data);
            if ($bool === false) {
                throw new BusinessException('标准型号添加失败 = ' . json_encode($sau_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            //组装标准型号sku参数组
            $sau_sku = [
                'sau_id' => $main_model->id,
                'length' => $data['length'] ?? 0.00,
                'length_unit' => $data['length_unit'] ?? 'mm',
                'width' => $data['width'] ?? 0.00,
                'width_unit' => $data['width_unit'] ?? 'mm',
                'height' => $data['height'] ?? 0.00,
                'height_unit' => $data['height_unit'] ?? 'mm',
                'weight' => $data['weight'] ?? 0.00,
                'weight_unit' => $data['weight_unit'] ?? 'g',

                'purchase_unit_zh' => $data['purchase_unit_zh'] ?? '',
                'purchase_unit_en' => $data['purchase_unit_en'] ?? '',
                'purchase_val' => $data['purchase_val'] ?? 0,

                'use_unit_zh' => $data['use_unit_zh'] ?? '',
                'use_unit_en' => $data['use_unit_en'] ?? '',
                'use_val' => $data['use_val'] ?? 0,

                'small_bag_unit_zh' => $data['small_bag_unit_zh'] ?? '',
                'small_bag_unit_en' => $data['small_bag_unit_en'] ?? '',
                'small_bag_val' => $data['small_bag_val'] ?? 0,

                'big_bag_unit_zh' => $data['big_bag_unit_zh'] ?? '',
                'big_bag_unit_en' => $data['big_bag_unit_en'] ?? '',
                'big_bag_val' => $data['big_bag_val'] ?? 0,

                'mach_code' => $data['mach_code'] ?? '',
                'mach_name' => $data['mach_name'] ?? '',
                'stock_id' => $data['stock_id'] ?? 0,
                'stock_name' => $data['stock_name'] ?? '',
                'is_deleted' => $data['is_deleted'] ?? self::DEFAULT_PARAMETER_ZERO,
                'created_at' => date('Y-m-d H:i:s', time()),
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
            $sau_model = new MaterialSauSkuModel();
            $bool = $sau_model->i_create($sau_sku);
            if ($bool === false) {
                throw new BusinessException('标准型号添加失败 = ' . json_encode($sau_sku, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            //开始存图片
            if (!empty($data['attachments'])) {
                if (count($data['attachments']) > self::MATERIAL_MAX_PIC) {
                    throw new BusinessException('attachments max 5');
                }
                $am = new MaterialAttachmentModel();
                $attachArr = [];
                foreach ($data['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK;
                    $tmp['oss_bucket_key'] = $main_model->id;
                    $tmp['sub_type'] = self::DEFAULT_PARAMETER_ZERO;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachArr[] = $tmp;
                }
                if (!$am->batch_insert($attachArr)) {
                    throw new BusinessException('标准型号图片添加失败 = ' . json_encode($data['attachments'], JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($am), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }

            //公司货主仓库数据
            if (!empty($data['storage'])) {
                $storage_model = new MaterialSauStorageModel();
                $storageArr    = [];
                foreach ($data['storage'] as $storage) {
                    $item                 = [];
                    $item['sau_id']       = $main_model->id;
                    $item['barcode']      = $data['barcode'];
                    $item['company_id']   = $storage['company_id'] ? $storage['company_id'] : 0;
                    $item['company_name'] = $storage['company_name'] ?? '';
                    $item['mach_code']    = $storage['mach_code'];
                    $item['mach_name']    = $storage['mach_name'];
                    $item['stock_id']     = $storage['stock_id'] ? $storage['stock_id'] : 0;
                    $item['stock_name']   = $storage['stock_name'];
                    $item['is_default']   = $storage['is_default'] ?? 0;
                    $item['is_deleted']   = GlobalEnums::IS_NO_DELETED;
                    $item['created_at']   = $now;
                    $storageArr[]         = $item;
                }
                if (!$storage_model->batch_insert($storageArr)) {
                    throw new BusinessException('标准型号公司货主仓库添加失败 = ' . json_encode($storageArr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($storage_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }

            //16850需求，资产类barcode下需要记录可申请职位信息
            if ($sau_data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && !empty($data['job'])) {
                $sau_permission_model = new MaterialSauPermissionModel();
                $sau_permission = [];
                foreach ($data['job'] as $job_id) {
                    $sau_permission[] = [
                        'sau_id' => $main_model->id,
                        'job_id' => $job_id,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }
                if (!$sau_permission_model->batch_insert($sau_permission)) {
                    throw new BusinessException('标准型号-可申请职位添加失败 = ' . json_encode($sau_permission, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_permission_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }

            // 记录同步物料信息到SCM记录，用作脚本同步
            if ($data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $scm = new ScmService();
                $bool = $scm->saveMaterialSyncScmLog($main_model->id);
                if ($bool === false) {
                    throw new BusinessException('标准型号添加同步至scm记录失败 = ' . json_encode($main_model->id, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }

            //记录操作日志
            $update_log_model = new MaterialUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_ADD, $main_model, $data, $user);
            if ($log_bool === false) {
                throw new BusinessException('标准型号添加操作记录失败 = ' . json_encode($main_model->id, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error($real_message . json_encode($sau_data) . '--' . json_encode($sau_sku));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 获取需要传输至SAP的二级财务分类code
     * @param integer $update_to_sap 是否更新至SAP，1否，2是
     * @param integer $finance_category_id 财务分类表ID
     * @return string
     */
    public function getFinanceSecondCategoryCode($update_to_sap, $finance_category_id)
    {
        $finance_category_code = '';//二级财务分类编码
        //泰国、马来、菲律宾三个国家 && 标准型号，是需要更新至SAP
        if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) && ($update_to_sap == MaterialClassifyEnums::MATERIAL_CATEGORY_NO)) {
            //获取当前标准型号挂栽的财务分类信息
            $finance_category_info = MaterialFinanceCategoryModel::findFirst([
                'columns' => 'code, level, ids',
                'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                'bind' => ['id' => $finance_category_id, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ]);
            if (!empty($finance_category_info)) {
                $finance_category_code = $finance_category_info->code;
                //判断当前财务分类级别是否是二级财务分类
                if ($finance_category_info->level > MaterialClassifyEnums::CATEGORY_LEVEL_TWO) {
                    //担心父级ids是空对兼容下错误数据
                    if (!empty($finance_category_info->ids)) {
                        //超过二级则需要获取该分类所有父类中的二级父类财务分类信息
                        $second_level_finance_category_info = MaterialFinanceCategoryModel::findFirst([
                            'columns' => 'code',
                            'conditions' => 'id in ({ids:array}) and level = :level: and is_deleted = :is_deleted:',
                            'bind' => ['ids' => explode(',', $finance_category_info->ids), 'level' => MaterialClassifyEnums::CATEGORY_LEVEL_TWO, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
                        ]);
                        $finance_category_code = !empty($second_level_finance_category_info) ? $second_level_finance_category_info->code : '';
                    } else {
                        $finance_category_code = '';
                    }
                }
            }
        }
        return $finance_category_code;
    }

    /**
     * 检测标准型号是否需要更新至SAP
     * @param integer $update_to_sap 是否更新至SAP，1否，2是
     * @param integer $finance_category_id 财务分类表ID
     * @return int
     */
    public function checkSendSap($update_to_sap, $finance_category_id)
    {
        $finance_category_code = $this->getFinanceSecondCategoryCode($update_to_sap, $finance_category_id);
        //判断自身或者所在的二级财务分类是否在二级财务分类G010/G020/G030名下
        if (!empty($finance_category_code) && in_array($finance_category_code, MaterialClassifyEnums::$send_to_sap_finance_category_code)) {
            return MaterialClassifyEnums::IS_SEND_SAP_WAIT;
        } else {
            return StandardService::DEFAULT_PARAMETER_ZERO;
        }
    }

    /**
     * 获取标准型号列表总记录数
     * @param string $locale 语言
     * @param array $condition 搜索条件
     * @return int
     */
    public function getCount($locale, $condition)
    {
        $count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $column_str = 'count(ms.id) as total';
            $builder->from(['ms' => MaterialSauModel::class]);
            $builder->columns($column_str);
            $builder = $this->getListCondition($locale, $builder, $condition);
            $total_info = $builder->getQuery()->getSingleResult();
            $count = intval($total_info->total);
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-sau-list-count-failed:' . $real_message . 'count :' . json_encode($condition));
        }
        return $count;
    }

    /**
     * 获取采购付款申请单列表
     *
     * @param string $locale 语言
     * @param array $condition
     * @param int $uid
     * @param int $type
     * @param bool $export
     * @return array
     */
    public function getList($locale, $condition, $export = FALSE)
    {
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $items = [];
        try {
            $count = $this->getCount($locale, $condition);
            if ($count > 0) {
                //如果是导出则要判断是否超过最大可导出数据量
                if ($export && ($count > MaterialClassifyEnums::STANDARD_MAX_DOWNLOAD)) {
                    throw new ValidationException(static::$t->_('material_standard_download_limit'));
                }
                $builder = $this->modelsManager->createBuilder();
                $unit_suffix = MaterialClassifyEnums::$language_fields[$locale] ?? 'en';
                $unit_suffix_locality = MaterialClassifyEnums::$language_fields[$locale] ?? 'local';
                if ($export) {
                    $column_str = 'ms.id, ms.barcode,ms.name_zh, ms.name_en, ms.name_local, ms.model, ms.category_type, ms.status, ms.update_to_scm, ms.update_to_acceptance, ms.unit_' . $unit_suffix . ' as unit,ms.category_id, ms.finance_category_id, mss.small_bag_unit_'.$unit_suffix.' as small_bag_unit, mss.big_bag_unit_' . $unit_suffix . ' as big_bag_unit, mc.parent_id, mc.ids, mfc.use_limit, mfc.parent_id as f_parent_id, mfc.ids as fids, mss.use_unit_' . $unit_suffix .' as use_unit, mss.use_val, ms.transfer_forbid, ms.use_scene';
                } else {
                    $column_str = 'ms.id,ms.barcode, name_' . $unit_suffix_locality . ' as name, ms.model, ms.category_type, ms.update_to_scm, ms.status, ms.unit_' . $unit_suffix . ' as unit, ms.update_to_acceptance, ms.category_id, ms.finance_category_id, mc.name as cname, mfc.name as fname';
                }
                $builder->from(['ms' => MaterialSauModel::class]);
                $builder->leftjoin(MaterialSauSkuModel::class, 'ms.id=mss.sau_id', 'mss');
                $builder->leftjoin(MaterialCategoryModel::class, 'ms.category_id=mc.id', 'mc');
                $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
                $builder->columns($column_str);
                $builder = $this->getListCondition($locale, $builder, $condition);
                $builder->orderBy('ms.id desc');
                if (!$export) {
                    $builder->limit($page_size, $offset);
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $export);
            }
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('material-sau-list-failed:' . $real_message . ' select :' . json_encode($condition));
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 组装查询条件
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     */
    public function getListCondition($locale, $builder, $condition)
    {
        $category_id = !empty($condition['category_id']) ? end($condition['category_id']) : 0; //物料分类表ID
        $finance_category_id = !empty($condition['finance_category_id']) ? end($condition['finance_category_id']) : 0;//财务分类表ID
        $name = $condition['name'] ?? '';//物料名称
        $barcode = $condition['barcode'] ?? ''; //标准型号的唯一标识
        $like_barcode = $condition['like_barcode'] ?? '';//barcode模糊搜索
        $model = $condition['model'] ?? '';//规格型号
        $status = $condition['status'] ?? 0;//启用状态：1启用，2禁用
        $update_to_scm = $condition['update_to_scm'] ?? 0;//是否更新至SCM，1否，2是
        $update_to_acceptance = $condition['update_to_acceptance'] ?? 0;//是否验收，1否，2是
        $category_type = $condition['category_type'] ?? 0;//物料类型，1资产，2耗材
        $ids = isset($condition['ids']) && !empty($condition['ids']) ? $condition['ids'] : [];//按照特定的标准型号ID检索
        $use_scene = $condition['use_scene'] ?? [];//可申请/购买
        $job_id = $condition['job_id'] ?? 0;//职位id
        $base_barcode = $condition['base_barcode'] ?? [];//设定的barcode组
        if ($base_barcode) {
            $builder->inWhere('ms.barcode', $base_barcode);
        }

        if (!empty($category_id)) {
            //物料分类筛选
            $category_ids = (new MaterialCategoryModel())->getSonList($category_id,'id');
            $builder->inWhere('ms.category_id', array_values(array_merge($category_ids['data'], [$category_id])));
        }
        if (!empty($finance_category_id)) {
            //财务分类筛选
            $finance_category_ids = (new MaterialFinanceCategoryModel())->getSonList($finance_category_id,'id');
            $builder->inWhere('ms.finance_category_id', array_values(array_merge($finance_category_ids['data'], [$finance_category_id])));
        }
        if (!empty($name)) {
            //名称搜索
            $builder->andWhere('name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' like :name:', ['name' => '%' . $name . '%']);
        }
        // by端需要走模糊搜索
        if (!empty($like_barcode) && isset($condition['source_type']) && $condition['source_type'] == MaterialWmsEnums::SOURCE_TYPE_BY) {
            $builder->andWhere('ms.barcode like :barcode:', ['barcode' => '%' . $like_barcode . '%']);
        }
        // oa/by还需要走精确搜索
        if (!empty($barcode)) {
            if (is_array($barcode)) {
                $builder->inWhere('ms.barcode', $barcode);
            } else {
                $builder->andWhere('ms.barcode = :barcode:', ['barcode' => $barcode]);
            }
        }

        if (!empty($model)) {
            //规格
            $builder->andWhere('ms.model like :model:', ['model' => '%' . $model . '%']);
        }
        if (!empty($status)) {
            //启用状态
            $builder->andWhere('ms.status = :status:', ['status' => $status]);
        }
        if (!empty($update_to_scm)) {
            //是否更新至scm
            $builder->andWhere('ms.update_to_scm = :update_to_scm:', ['update_to_scm' => $update_to_scm]);
        }
        if (!empty($update_to_acceptance)) {
            //是否验收
            $builder->andWhere('ms.update_to_acceptance = :update_to_acceptance:', ['update_to_acceptance' => $update_to_acceptance]);
        }
        if (!empty($category_type)) {
            //物料类型
            $builder->andWhere('ms.category_type=:category_type:', ['category_type' => $category_type]);
        }
        if (!empty($ids)) {
            //按照标准型号ID组检索
            $builder->andWhere('ms.id in ({ids:array})', ['ids' => $ids]);
        }
        $builder->andWhere('ms.is_deleted=:is_deleted:', ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]);
        //16712需求增加可申请/购买条件筛选
        if (!empty($use_scene)) {
            $builder->inWhere('ms.use_scene', $use_scene);
        }
        //16850需求增加职位筛选,只有资产的需要
        if (!empty($job_id) && $category_type == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
            $builder->leftjoin(MaterialSauPermissionModel::class, 'msp.sau_id = ms.id', 'msp');
            $builder->groupBy('ms.barcode');
            $builder->having("ms.is_contain_job = 0 OR (ms.is_contain_job = " . MaterialClassifyEnums::IS_CONTAIN_JOB_YES ." and FIND_IN_SET({$job_id}, GROUP_CONCAT(msp.job_id))) OR (ms.is_contain_job = " . MaterialClassifyEnums::IS_CONTAIN_JOB_NO . " and NOT FIND_IN_SET({$job_id}, GROUP_CONCAT(msp.job_id)))");
        }
        return $builder;
    }

    /**
     * 导出列表
     *
     * @param string $locale 语言
     * @param array $condition 查询条件
     * @param int $uid 用户ID
     * @return array
     * @Date: 2021-10-18 21:58
     */
    public function exportSau($locale, $condition, $uid = 0)
    {
        try{
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $data = $this->getList($locale, $condition,true);
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($data['code'] == 0 ? $data['message'] : 'exportSau getList error');
            }
            $data = $data['data']['items'];
            $row_values = $this->handleReceiptData($data);
            $file_name = "material_sau_" . date("YmdHis");
            $header = [
                static::$t->_('material_barcode'),                    //  barcode
                static::$t->_('material_assetname'),                  //  物料名称-中文
                static::$t->_('material_assetname_en'),               //  物料名称-英文
                static::$t->_('material_assetname_local'),            //  物料名称-当地语言
                static::$t->_('material_specification'),              //  规格类型
                static::$t->_('material_materialtype'),               //  物料类型
                static::$t->_('material_use_scene_text'),               //  可申请/可购买
                static::$t->_('material_sau_job'),               //  资产可申请职位

                static::$t->_('material_basicclassificationname'),    //  一级分类名称
                static::$t->_('material_basicclassificationcode'),    //  一级分类编码
                static::$t->_('material_level2classificationname'),   //  二级分类名称
                static::$t->_('material_level2classificationcode'),   //  二级分类编码
                static::$t->_('material_level3classificationname'),   //  三级分类名称
                static::$t->_('material_level3classificationcode'),   //  三级分类编码

                static::$t->_('material_level1financeclassificationname'),   //	  一级财务分类名称
                static::$t->_('material_level1financeclassificationcode'),   //   一级财务分类编码
                static::$t->_('material_level2financeclassificationname'),   //   二级财务分类名称
                static::$t->_('material_level2financeclassificationcode'),   //   二级财务类编码
                static::$t->_('material_level3financeclassificationname'),   //   三级财务分类名称
                static::$t->_('material_level3financeclassificationcode'),   //   三级财务分类编码

                static::$t->_('material_servicelife'),       //   使用期限（月）
                static::$t->_('material_status'),            //   状态
                static::$t->_('material_measuringunit'),     //	  计算单位
                static::$t->_('material_small_bag_unit'),    //   小包装单位
                static::$t->_('material_big_bag_unit'),     //	  大包装单位

                static::$t->_('material_sau_sku_use_unit'),     //	  领用单位
                static::$t->_('material_sau_sku_use_val'),     //	  领用单位数量

                static::$t->_('material_ifupdatetoscm'),     //	  是否更新至scm
                static::$t->_('material_ifupdatetoacceptance'),//是否验收
                static::$t->_('material_transfer_forbid'),//是否限制员工互转
                static::$t->_('material_pictureurl'),        //图片链接
            ];
            $row_values = array_map('array_values',$row_values);
            $result            = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-sau-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 处理标准型号数据
     * @param array $receipt_data
     * @Date: 2021-10-18 21:59
     * @return: array
     * @throws Exception
     **@author: peak pan
     */
    private function handleReceiptData($receipt_data)
    {
        try {
            if (empty($receipt_data)) return [];
            $receipt = [];
            $c_key_arr = ClassifyService::getInstance()->getNameList($receipt_data, MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS);
            $f_key_arr = ClassifyService::getInstance()->getNameList($receipt_data, MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS);
            foreach ($receipt_data as $k => $v) {
                $receipt[$k]['barcode'] = $v['barcode'];//barcode
                $receipt[$k]['name_zh'] = $v['name_zh'];//物料中文名称
                $receipt[$k]['name_en'] = $v['name_en'];//物料英文名称
                $receipt[$k]['name_local'] = $v['name_local'];//当地语言名称
                $receipt[$k]['model'] = $v['model'];//规格型号
                $receipt[$k]['category_type_text'] = $v['category_type_text'];//物料类型
                $receipt[$k]['use_scene_text'] = $v['use_scene_text']; //可申请/可购买
                $receipt[$k]['sau_job_info'] = $v['sau_job_info'];//可申请职位

                $ids_arr = array_merge(explode(',', $v['ids']), [$v['category_id']]);
                if (empty($v['parent_id'])) {
                    $ids_arr = [$v['category_id']];
                }
                $fids_arr = array_merge(explode(',', $v['fids']), [$v['finance_category_id']]);
                if (empty($v['f_parent_id'])) {
                    $fids_arr = [$v['finance_category_id']];
                }
                $receipt[$k]['classify_one_name'] = $c_key_arr[$ids_arr[0]]['name'] ?? '';//一级分类名称
                $receipt[$k]['classify_one_code'] = $c_key_arr[$ids_arr[0]]['code'] ?? '';//一级分类编码
                $receipt[$k]['classify_two_name'] = !empty($ids_arr[1]) ? $c_key_arr[$ids_arr[1]]['name'] ?? '' : '';//二级分类名称
                $receipt[$k]['classify_two_code'] = !empty($ids_arr[1]) ? $c_key_arr[$ids_arr[1]]['code'] ?? '' : '';//二级分类编码
                $receipt[$k]['classify_thr_name'] = !empty($ids_arr[2]) ? $c_key_arr[$ids_arr[2]]['name'] ?? '' : '';//三级分类名称
                $receipt[$k]['classify_thr_code'] = !empty($ids_arr[2]) ? $c_key_arr[$ids_arr[2]]['code'] ?? '' : '';//三级分类编码

                $receipt[$k]['finance_one_name'] = $f_key_arr[$fids_arr[0]]['name'] ?? '';//一级财务分类名称
                $receipt[$k]['finance_one_code'] = $f_key_arr[$fids_arr[0]]['code'] ?? '';//一级财务分类编码
                $receipt[$k]['finance_two_name'] = !empty($fids_arr[1]) ? $f_key_arr[$fids_arr[1]]['name'] ?? '' : '';//二级财务分类名称
                $receipt[$k]['finance_two_code'] = !empty($fids_arr[1]) ? $f_key_arr[$fids_arr[1]]['code'] ?? '' : '';//二级财务分类编码
                $receipt[$k]['finance_thr_name'] = !empty($fids_arr[2]) ? $f_key_arr[$fids_arr[2]]['name'] ?? '' : '';//三级财务分类名称
                $receipt[$k]['finance_thr_code'] = !empty($fids_arr[2]) ? $f_key_arr[$fids_arr[2]]['code'] ?? '' : '';//三级财务分类编码
                $receipt[$k]['use_limit'] = $v['use_limit'] ?? '--';//折旧期限
                $receipt[$k]['status_text'] = $v['status_text'];//状态
                $receipt[$k]['unit'] = $v['unit'];//基本单位
                $receipt[$k]['small_bag_unit'] = $v['small_bag_unit'];//基本单位
                $receipt[$k]['big_bag_unit'] = $v['big_bag_unit'];//大包装单位
                $receipt[$k]['use_unit'] = $v['use_unit'];//领用单位
                $receipt[$k]['use_val'] = $v['use_val'];//领用单位数量
                $receipt[$k]['update_to_scm_text'] = $v['update_to_scm_text'];//是否更新至SCM
                $receipt[$k]['update_to_acceptance_text'] = $v['update_to_acceptance_text'];//是否验收
                $receipt[$k]['transfer_forbid_text'] = $v['transfer_forbid_text'];//是否限制员工互转
                $receipt[$k]['pic'] = $v['pic'];
            }
            return $receipt;
        } catch (Exception $e) {
            $this->logger->warning('material_sau-handle-excel-data-failed:' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 处理列表数据
     * @Token
     * @Date: 2021-10-19 15:47
     * @param array $items
     * @param bool $export 是否导出，true导出
     * @return array
     **@author: peak pan
     */
    private function handleListItems($items, $export = false)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        //导出时查询可申请职位
        $sau_job_data_kv = [];
        if ($export) {
            $sau_ids = array_column($items, 'id');
            $sau_job_data = MaterialSauPermissionRepository::getInstance()->getJobIdsBySau($sau_ids);
            $job_ids = array_column($sau_job_data, 'job_id');
            //查询职位
            $job_ids = array_values(array_unique($job_ids));
            $job_data = (new HrJobTitleRepository())->getJobTitleByIds($job_ids, false);
            $job_data = array_column($job_data, 'job_name', 'id');
            foreach ($sau_job_data as $sau_job_v) {
                $sau_job_data_kv[$sau_job_v['sau_id']][] = $job_data[$sau_job_v['job_id']] ?? '';
            }
        }
        $materialAttachment = new  MaterialAttachmentModel();
        $pic_arr_key_arr = $materialAttachment->getColumnArr($items);
        foreach ($items as &$item) {
            $item['category_type_text'] = self::$t[MaterialClassifyEnums::$material_category_arr_type[$item['category_type']]];
            $item['update_to_scm_text'] = self::$t[MaterialClassifyEnums::$is_off[$item['update_to_scm']]];
            $item['update_to_acceptance_text'] = self::$t[MaterialClassifyEnums::$is_off[$item['update_to_acceptance']]];
            $item['status_text'] = static::$t->_(MaterialClassifyEnums::$status_switch[$item['status']]);
            if ($export) {
                if (isset($pic_arr_key_arr[$item['id']]) && $pic_arr_key_arr[$item['id']]) {
                    $item['pic'] = implode(',', arrayPicTool($pic_arr_key_arr[$item['id']]));
                } else {
                    $item['pic'] = '';
                }
                $item['transfer_forbid_text'] = static::$t->_(MaterialClassifyEnums::$transfer_forbid_t[$item['transfer_forbid']]);
                //可申请/可购买
                $item['use_scene_text'] = $item['use_scene'] ? static::$t->_(MaterialClassifyEnums::$material_use_scene[$item['use_scene']]) : '';
                //可申请职位
                $item['sau_job_info'] = isset($sau_job_data_kv[$item['id']]) ? implode(',', $sau_job_data_kv[$item['id']]) : '';
            } else {
                $item['pic'] = $pic_arr_key_arr[$item['id']] ?? '';
            }
        }
        return $items;
    }

    /**
     * 是否可做删除或更新操作
     * @param string $barcode barcode
     * @return bool
     */
    public function canDelOrUpdate($barcode)
    {
        $apply_product_count = PurchaseApplyProduct::count([
            'conditions' => 'product_option_code = :product_option_code:',
            'columns' => 'id',
            'bind' => [
                'product_option_code' => $barcode
            ]
        ]);
        if ($apply_product_count > 0) {
            //存在关联的采购申请，则不允许删除或更新
            return false;
        }
        $order_product_count = PurchaseOrderProduct::count([
            'conditions' => 'product_option_code = :product_option_code:',
            'columns' => 'id',
            'bind' => [
                'product_option_code' => $barcode
            ]
        ]);
        if ($order_product_count > 0) {
            //存在关联的采购订单，则不允许删除或更新
            return false;
        }
        $storage_product_count = PurchaseStorageProduct::count([
            'conditions' => 'product_option_code = :product_option_code:',
            'columns' => 'id',
            'bind' => [
                'product_option_code' => $barcode
            ]
        ]);
        if ($storage_product_count > 0) {
            //存在关联的入库单，则不允许删除或更新
            return false;
        }
        //存在资产台账则不允许删除或更新
        $material_assets = MaterialAssetsModel::count([
            'conditions' => 'bar_code = :bar_code: and is_deleted = :is_deleted:',
            'columns' => 'id',
            'bind' => [
                'bar_code' => $barcode,
                'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO
            ]
        ]);
        if ($material_assets > 0) {
            return false;
        }

        //存在耗材不允许删除或更新
        $material_wms = MaterialWmsApplyProductModel::count([
            'conditions' => 'barcode = :barcode: and is_deleted = :is_deleted:',
            'columns' => 'id',
            'bind' => [
                'barcode' => $barcode,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ]);
        if ($material_wms > 0) {
            return false;
        }

        return true;
    }

    /**
     * 标准型号删除
     * @param array $data 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function materialSauDel(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $ategory = MaterialSauModel::findFirstById($data['id']);
            //检测标准型号是否存在
            if (empty($ategory) || $ategory->is_deleted != MaterialClassifyEnums::IS_DELETED_NO) {
                throw new ValidationException(self::$t['material_sau_not_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //不同步SCM、停用状态的barcode才可删除
            if ($ategory->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO || $ategory->status == MaterialClassifyEnums::MATERIAL_START_USING) {
                throw new ValidationException(self::$t['material_sau_del_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //该barcode在采购申请、采购订单、入库单、资产台账不存在过才可删除
            $can_del = $this->canDelOrUpdate($ategory->barcode);
            if ($can_del === false) {
                throw new ValidationException(self::$t['material_sau_del_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //用于记录操作日志
            $del_ategory = clone $ategory;
            //删除sau数据
            $updated_at = date('Y-m-d H:i:s', time());
            $category_data = [
                'is_deleted' => $ategory->id,
                'updated_at' => $updated_at,
            ];
            $save_bool = $ategory->i_update($category_data);
            if ($save_bool === false) {
                throw new BusinessException('删除失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //删除sau对应的sku数据
            $db->updateAsDict(
                'material_sau_sku',
                ['is_deleted' => MaterialClassifyEnums::IS_DELETED_YES,'updated_at' => $updated_at],
                'sau_id = '. $ategory->id
            );
            //删除关联的附件信息
            $db->updateAsDict(
                'material_attachment',
                ['deleted' => MaterialClassifyEnums::IS_DELETED_YES],
                'oss_bucket_key = '. $ategory->id.' and oss_bucket_type = '.MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK
            );
            //记录操作日志
            $update_log_model = new MaterialUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_DELETE, $del_ategory, $category_data, $user, ['is_deleted', 'updated_at']);
            if ($log_bool === false) {
                $category_data['id'] = $ategory->id;
                throw new BusinessException('标准型号删除操作记录失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error($real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 标准型号启用禁用
     * @param array $data 参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function materialChangeStatus(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $ategory = MaterialSauModel::findFirstById($data['id']);
            if (empty($ategory) || $ategory->is_deleted != MaterialClassifyEnums::IS_DELETED_NO) {
                throw new ValidationException(self::$t['material_sau_not_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            if ($ategory->status == $data['status']) {
                throw new ValidationException(self::$t['material_sau_status_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //用于记录操作日志
            $update_info = clone $ategory;
            //启用或停用
            $category_data = [
                'status' => $data['status'],
                'updated_at' => date('Y-m-d H:i:s', time()),
            ];
            $save_bool = $ategory->i_update($category_data);
            if ($save_bool === false) {
                throw new BusinessException($data['status'] == MaterialClassifyEnums::MATERIAL_START_USING ? '启用' : '禁用' . '失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }

            // 记录同步物料信息到SCM记录，用作脚本同步
            if ($ategory->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && !empty($data['status'])) {
                $scm = new ScmService();
                $bool = $scm->saveMaterialSyncScmLog($ategory->id, MaterialClassifyEnums::TO_SCM_SYNC_TYPE_STATUS);
                if ($bool === false) {
                    $category_data['id'] = $ategory->id;
                    throw new BusinessException('标准型号添加同步至scm记录失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }
            //记录操作日志
            $update_log_model = new MaterialUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_UPDATE, $update_info, $category_data, $user);
            if ($log_bool === false) {
                $category_data['id'] = $ategory->id;
                throw new BusinessException('标准型号启用(停用)操作记录失败 = ' . json_encode($category_data, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error($real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 标准型号详情
     * @Date: 2021-10-20 11:31
     * @param array $data 请求参数组
     * @author: peak pan
     * @return array
     */
    public function materialSauDetail(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $sau = MaterialSauModel::findFirstById($data['id']);
            if (empty($sau) || $sau->is_deleted != MaterialClassifyEnums::IS_DELETED_NO) {
                throw new ValidationException(self::$t['material_sau_not_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            $sauSkuInfo = $sau->getMaterialSauSku()->toArray();
            if ($sauSkuInfo) {
                //由于两个表都有id字段，会在merge冲突，所以把sku的id重命名一下
                $sauSkuInfo['sku_id'] = $sauSkuInfo['id'];
                unset($sauSkuInfo['id']);
            }
            $picArr['attachments'] = $sau->getPicAttachment()->toArray();
            $picArr['storage'] = $sau->getMaterialSauStorage()->toArray();

            //16580需求，返回可申请职位信息
            $picArr['job'] = [];
            $job_list = $sau->getMaterialSauPermission()->toArray();
            if (!empty($job_list)) {
                $job_ids = array_column($job_list, 'job_id');
                $picArr['job'] = HrJobTitleModel::find([
                   'columns' => 'id,job_name as name, status',
                   'conditions' => 'id in ({ids:array})',
                   'bind' => ['ids' => $job_ids]
               ])->toArray();
                foreach ($picArr['job'] as &$item) {
                    $item['name'] .= ($item['status'] == StaffInfoEnums::HR_JOB_STATUS_OPEN) ? '' : static::$t->_('deleted');
                }
            }
            $sau_arr = $sau->toArray();
            //基本单位、小包装单位、大包装单位是否可编辑(传输至SCM，或者有业务单据关联（采购申请、采购订单、入库单），则不可以编辑)
            $sau_arr['can_edit'] = ($sau->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO || $this->canDelOrUpdate($sau->barcode) === false) ? false : true;
            $detail = array_merge($sau_arr, $sauSkuInfo, $picArr);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-sau-detail:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 标准型号  修改提交
     * @param array $data 参数组
     * @param array $user 当前登陆者信息组
     * @Date: 2021-10-20 14:58
     * @return array
     * @author: peak pan
     */
    public function save(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $sau_data = $sau_sku = [];
        try {
            $db->begin();
            //校验基本单位、小包装单位、大包装单位、长宽高、重量传递合法性，用于适配SCM
            $this->extendValidation($data);
            if (!empty($data['storage'])) {
                $data['storage'] = $this->extendStorageValidation($data['storage']);
            }
            $main_model = MaterialSauModel::findFirstById($data['id']);
            $log_model = clone $main_model;
            //判断标准型号是否存在
            if (empty($main_model->id) || $main_model->is_deleted != MaterialClassifyEnums::IS_DELETED_NO) {
                throw new ValidationException(self::$t['material_sau_data_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            // 12614【ALL-OA】资产标准型号&采购入库&资产台账-P2 停用的barcode不可编辑
            if ($main_model->status == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE) {
                throw new ValidationException(self::$t['material_sau_status_stop'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //sku信息
            $sau_sku_model = $main_model->getMaterialSauSku();
            if (empty($sau_sku_model)) {
                throw new ValidationException(self::$t['material_sau_data_existed'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //barcode：不可编辑
            if (trim($data['barcode']) != $main_model->barcode) {
                throw new ValidationException(self::$t['material_sau_update_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }

            //原来的标准型号是传输至SCM，或者有业务单据关联（采购申请、采购订单、入库单），
            if ($main_model->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO || $this->canDelOrUpdate($main_model->barcode) === false) {
                if (trim($data['unit_zh']) != trim($main_model->unit_zh) || trim($data['unit_en']) != trim($main_model->unit_en)) {
                    //基本单位
                    throw new ValidationException(self::$t['material_sau_update_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
                }
                if (trim($data['small_bag_unit_zh']) != trim($sau_sku_model->small_bag_unit_zh) || trim($data['small_bag_unit_en']) != trim($sau_sku_model->small_bag_unit_en)) {
                    //小包装单位
                    throw new ValidationException(self::$t['material_sau_update_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
                }
                if (trim($data['big_bag_unit_zh']) != trim($sau_sku_model->big_bag_unit_zh) || trim($data['big_bag_unit_en']) != trim($sau_sku_model->big_bag_unit_en)) {
                    //大包装单位
                    throw new ValidationException(self::$t['material_sau_update_error'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
                }
            }

            //检测所选择的物料分类或财务分类是否是最末级分类
            if ($this->is_existe_son($data, $main_model)) {
                throw new ValidationException(self::$t['material_sau_category_not_last'], MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }

            //是否启用资产码,如果更新至scm=是（2）&物料类型=资产（1）则enable_asset_code=2(是)；否则=1（否）
            if ($data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                $data['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
            } else {
                $data['enable_asset_code'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }
            // 是否启用sn，非资产类，sn不开启；资产类，按照前端传递为准
            if ($data['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
                $data['enable_sn'] = MaterialClassifyEnums::MATERIAL_CATEGORY_OFF;
            }

            //组装标准型号参数组
            $now_time = date('Y-m-d H:i:s', time());
            $sau_data = [
                'category_id' => $data['category_id'],
                'finance_category_id' => $data['finance_category_id'],
                'barcode' => $data['barcode'],
                'name_zh' => $data['name_zh'],
                'name_en' => $data['name_en'],
                'name_local' => $data['name_local'] ?? '',
                'model' => $data['model'],
                'unit_zh' => $data['unit_zh'],
                'unit_en' => $data['unit_en'],
                'category_type' => $data['category_type'],
                'purchase_type' => $data['purchase_type'],
                'brand' => $data['brand'] ?? '',
                'remark' => $data['remark'] ?? '',
                'price' => $data['price'] ?? 0.00,
                'sap_unit' => isset($data['sap_unit']) && !empty($data['sap_unit']) ? $data['sap_unit'] : '',
                'currency' => $data['currency'],
                'update_to_scm' => $data['update_to_scm'] ?? self::DEFAULT_PARAMETER_ONE,
                'update_to_sap' => $data['update_to_sap'] ?? self::DEFAULT_PARAMETER_ONE,
                'update_to_acceptance' => $data['update_to_acceptance'] ?? self::DEFAULT_PARAMETER_ONE,
                'enable_asset_code' => $data['enable_asset_code'],
                'enable_sn' => $data['enable_sn'] ?? self::DEFAULT_PARAMETER_ONE,
                'is_send_sap' => $this->checkSendSap($data['update_to_sap'] ?? self::DEFAULT_PARAMETER_ONE, $data['finance_category_id']),//检测是否需要发送至SAP
                'updated_at' => $now_time,
                'transfer_forbid' => $data['transfer_forbid'] ?? MaterialClassifyEnums::MATERIAL_CATEGORY_TRANSFER_FORBID_NO,
                'use_scene' => $data['use_scene'] ?? 0,
                'is_contain_job' => $data['is_contain_job'] ?? 0
            ];
            $bool = $main_model->i_update($sau_data);
            if ($bool === false) {
                throw new BusinessException('标准型号修改失败 = ' . json_encode($sau_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }

            //组装标准型号sku参数组
            $sau_sku = [
                'length' => $data['length'] ?? 0.00,
                'length_unit' => $data['length_unit'] ?? 'mm',
                'width' => $data['width'] ?? 0.00,
                'width_unit' => $data['width_unit'] ?? 'mm',
                'height' => $data['height'] ?? 0.00,
                'height_unit' => $data['height_unit'] ?? 'mm',
                'weight' => $data['weight'] ?? 0.00,
                'weight_unit' => $data['weight_unit'] ?? 'g',

                'purchase_unit_zh' => $data['purchase_unit_zh'] ?? '',
                'purchase_unit_en' => $data['purchase_unit_en'] ?? '',
                'purchase_val' => $data['purchase_val'] ?? 0,

                'use_unit_zh' => $data['use_unit_zh'] ?? '',
                'use_unit_en' => $data['use_unit_en'] ?? '',
                'use_val' => $data['use_val'] ?? 0,

                'small_bag_unit_zh' => $data['small_bag_unit_zh'] ?? '',
                'small_bag_unit_en' => $data['small_bag_unit_en'] ?? '',
                'small_bag_val' => $data['small_bag_val'] ?? 0,

                'big_bag_unit_zh' => $data['big_bag_unit_zh'] ?? '',
                'big_bag_unit_en' => $data['big_bag_unit_en'] ?? '',
                'big_bag_val' => $data['big_bag_val'] ?? 0,
                'updated_at' => $now_time,
            ];
            //需要记录操作日志的sku属性
            $log_model->purchase_unit_zh = $sau_sku_model->purchase_unit_zh;
            $log_model->purchase_unit_en = $sau_sku_model->purchase_unit_en;
            $log_model->purchase_val = $sau_sku_model->purchase_val;
            $log_model->use_unit_zh = $sau_sku_model->use_unit_zh;
            $log_model->use_unit_en = $sau_sku_model->use_unit_en;
            $log_model->use_val = $sau_sku_model->use_val;
            $log_model->small_bag_unit_zh = $sau_sku_model->small_bag_unit_zh;
            $log_model->small_bag_unit_en = $sau_sku_model->small_bag_unit_en;
            $log_model->small_bag_val = $sau_sku_model->small_bag_val;
            $log_model->big_bag_unit_zh = $sau_sku_model->big_bag_unit_zh;
            $log_model->big_bag_unit_en = $sau_sku_model->big_bag_unit_en;
            $log_model->big_bag_val = $sau_sku_model->big_bag_val;
            $bool = $sau_sku_model->i_update($sau_sku);
            if ($bool === false) {
                throw new BusinessException('标准型号修改失败 = ' . json_encode($sau_sku, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_sku_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
            }
            //编辑时先删除原来的附件信息
            $db->updateAsDict(
                'material_attachment',
                ['deleted' => MaterialClassifyEnums::IS_DELETED_YES],
                'oss_bucket_key = '. $main_model->id.' and oss_bucket_type = '.MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK
            );
            //开始存图片
            if (!empty($data['attachments'])) {
                if (count($data['attachments']) > self::MATERIAL_MAX_PIC) {
                    throw new BusinessException('attachments max ' . self::MATERIAL_MAX_PIC);
                }
                //然后在批量插入附件信息
                $am = new MaterialAttachmentModel();
                $attachArr = [];
                foreach ($data['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK;
                    $tmp['oss_bucket_key'] = $main_model->id;
                    $tmp['sub_type'] = self::DEFAULT_PARAMETER_ZERO;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachArr[] = $tmp;
                }
                if (!$am->batch_insert($attachArr)) {
                    throw new BusinessException('标准型号图片修改失败 = ' . json_encode($attachArr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($am), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
                }
            }
            $storage_model = new MaterialSauStorageModel();
            $db->updateAsDict(
                $storage_model->getSource(),
                ['is_deleted' => GlobalEnums::IS_DELETED],
                'sau_id = ' . $main_model->id
            );
            //公司货主仓库数据
            if (!empty($data['storage'])) {
                $storageArr = [];
                foreach ($data['storage'] as $storage) {
                    $item                 = [];
                    $item['sau_id']       = $main_model->id;
                    $item['barcode']      = $data['barcode'];
                    $item['company_id']   = $storage['company_id'] ? $storage['company_id'] : 0;
                    $item['company_name'] = $storage['company_name'] ?? '';
                    $item['mach_code']    = $storage['mach_code'];
                    $item['mach_name']    = $storage['mach_name'];
                    $item['stock_id']     = $storage['stock_id'] ? $storage['stock_id'] : 0;
                    $item['stock_name']   = $storage['stock_name'];
                    $item['is_default']   = $storage['is_default'] ?? 0;
                    $item['is_deleted']   = GlobalEnums::IS_NO_DELETED;
                    $item['created_at']   = date('Y-m-d H:i:s', time());
                    $storageArr[]         = $item;
                }
                if (!$storage_model->batch_insert($storageArr)) {
                    throw new BusinessException('标准型号公司货主仓库添加失败 = ' . json_encode($storageArr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($storage_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_SAVE_EXISTED);
                }
            }

            //16850需求，资产类barcode下需要记录可申请职位信息
            //先删除原来的职位信息
            $old_job = $main_model->getMaterialSauPermission();
            $old_job_list = $old_job->toArray();
            $data['old_job_ids'] = '';
            if (!empty($old_job_list)) {
                $data['old_job_ids'] = implode(',', array_column($old_job_list, 'job_id'));
                if ($old_job->delete() === false) {
                    throw new BusinessException('标准型号-可申请职位修改失败[删除原有职位] = ' . $data['old_job_ids'], ErrCode::$BUSINESS_ERROR);
                }
            }
            //插入新的职位信息
            if ($sau_data['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET && !empty($data['job'])) {
                $sau_permission_model = new MaterialSauPermissionModel();
                $sau_permission = [];
                foreach ($data['job'] as $job_id) {
                    $sau_permission[] = [
                        'sau_id' => $main_model->id,
                        'job_id' => $job_id,
                        'created_at' => $now_time,
                        'updated_at' => $now_time
                    ];
                }
                if (!$sau_permission_model->batch_insert($sau_permission)) {
                    throw new BusinessException('标准型号-可申请职位修改失败 = ' . json_encode($sau_permission, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sau_permission_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }

            // 记录同步物料信息到SCM记录，用作脚本同步
            if ($data['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                $scm = new ScmService();
                $bool = $scm->saveMaterialSyncScmLog($main_model->id, MaterialClassifyEnums::TO_SCM_SYNC_TYPE_UPDATE);
                if ($bool === false) {
                    throw new BusinessException('标准型号更新同步至scm记录失败 = ' . json_encode($main_model->id, JSON_UNESCAPED_UNICODE), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
                }
            }
            //记录操作日志
            $data['updated_at'] = $now_time;
            $data['is_send_sap'] = $sau_data['is_send_sap'];
            $update_log_model = new MaterialUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_UPDATE, $log_model, $data, $user);
            if ($log_bool === false) {
                throw new BusinessException('标准型号更新-操作记录失败 = ' . json_encode($main_model->id, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), MaterialClassifyEnums::$MATERIAL_SAU_BARCODE_ADD_EXISTED);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error($real_message . json_encode($sau_data) . '--' . json_encode($sau_sku));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 验证下面是否有子集 添加和编辑使用
     * @Token
     * @Date: 2021-10-24 13:15
     * @return:
     **@author: peak pan
     */
    private function is_existe_son($data, $main_model = '')
    {
        if (!empty($data['id'])) {
            //修改
            if ($data['category_id'] != $main_model->category_id) {
                $category_exists = (new MaterialCategoryModel)->isHasChildrenUsing($data['category_id']);
                if (!empty($category_exists)) {
                    return true;
                }
            }
            if ($data['finance_category_id'] != $main_model->finance_category_id) {
                $finance_exists = (new MaterialFinanceCategoryModel())->isHasChildrenUsing($data['finance_category_id']);
                if (!empty($finance_exists)) {
                    return true;
                }
            }
        } else {
            //添加
            $category_exists = (new MaterialCategoryModel)->isHasChildrenUsing($data['category_id']);
            if (!empty($category_exists)) {
                return true;
            }

            $finance_exists = (new MaterialFinanceCategoryModel())->isHasChildrenUsing($data['finance_category_id']);
            if (!empty($finance_exists)) {
                return true;
            }
            return false;
        }
    }

    /**
     * 按照特定条件搜索barcode
     * @param string $locale 语言
     * @param array $params['name'=>'barcode或资产名称']
     * @return mixed
     */
    public function searchBarcode($locale,$params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //根据当前语言环境决定搜索哪个key
            $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'name_' . MaterialClassifyEnums::$language_fields[$locale] : 'name_local';
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('ms.barcode, ms.name_zh, ms.name_en, ms.name_local, ms.' . $name_key . ' as name, ms.model, ms.unit_zh, ms.unit_en, ms.brand, ms.category_id, ms.price, ms.currency, mc.name as category_name,
             mc.code as category_code, ms.finance_category_id, mfc.name as finance_category_name, mfc.code as finance_category_code, mfc.use_limit, ms.category_type');
            $builder->from(['ms' => MaterialSauModel::class]);
            $builder->leftjoin(MaterialCategoryModel::class, 'ms.category_id = mc.id', 'mc');
            $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id = mfc.id', 'mfc');
            $builder->where('ms.is_deleted = :is_deleted: and mc.is_deleted = :is_deleted: and mfc.is_deleted = :is_deleted:',
                [
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]);
            $barcode_or_name = $params['name'] ?? '';
            $category_type = $params['category_type'] ?? [MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS];//物料类型，不传递默认获取资产类和耗材类
            if ($barcode_or_name) {
                //按照barcode或者名称筛选
                $builder->andWhere('barcode LIKE :barcode_or_name: OR ' . $name_key . ' LIKE :barcode_or_name:',
                    ['barcode_or_name' => '%' . trim($barcode_or_name) . '%']);
            }
            if (isset($params['barcode']) && !empty($params['barcode'])) {
                //仅按照barcode筛选
                $builder->andWhere('barcode = :barcode:', ['barcode'=>$params['barcode']]);
           }
            if (!empty($category_type)) {
                //物料类型
                if (is_array($category_type)) {
                    $builder->inWhere('ms.category_type', $category_type);
                } else {
                    $builder->andWhere('ms.category_type = :category_type:', ['category_type' => $category_type]);
                }
            }
            if (!empty($params['status'])) {
                $builder->andWhere('ms.status = :status:', ['status' => $params['status']]);
            }
            $builder->limit($params['limit'] ?? MaterialEnums::PAGE_SIZE);
            $data = $builder->getQuery()->execute()->toArray();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-search-barcode-list-failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取启用中的标准型号barcode列表
     * @param string $barcode_or_name barcode或资产名称搜索
     * @return mixed
     */
    public function getOpenBarcodeList($barcode_or_name)
    {
        $locale = static::$language;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ms.id,barcode,ms.currency,ms.price,name_en,name_zh,name_local,unit_en,sap_unit,model,ms.purchase_type,ms.update_to_acceptance,mfc.code,mfc.name as wrf_code_name,mfc.ledger_account_id');
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
        $builder->where('ms.is_deleted = :is_deleted: and mfc.is_deleted = :is_deleted: and ms.status=:status:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO, 'status'=>MaterialClassifyEnums::MATERIAL_START_USING]);
        if ($barcode_or_name) {
            $builder->andWhere('barcode LIKE :barcode_or_name: or name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' LIKE :barcode_or_name:', ['barcode_or_name' => '%' . trim($barcode_or_name) . '%']);
        }
        $builder->limit(50);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 返回满足条件的财务分类级别下的barcode列表
     * @param array $params['barcode'=>'barcode 搜索']
     * @return mixed
     */
    public function getFinanceBarcodeList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ms.barcode,ms.name_en,ms.name_zh,ms.name_local,ms.purchase_type,ms.update_to_acceptance,mfc.code,mfc.name');
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialFinanceCategoryModel::class, 'ms.finance_category_id=mfc.id', 'mfc');
        $builder->where('ms.is_deleted = :is_deleted: and mfc.is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        if (isset($params['barcode']) && !empty($params['barcode'])) {
            if (is_array($params['barcode'])) {
                $builder->andWhere('barcode in ({barcode:array})', ['barcode' => $params['barcode']]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 返回满足条件的barcode列表
     * @param array $params['barcode'=>'barcode 搜索']
     * @return mixed
     */
    public function getBarcodeList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('barcode,name_en,name_zh,name_local,model,sap_unit,category_type,update_to_scm,unit_en,category_id,finance_category_id,update_to_acceptance');
        $builder->from([MaterialSauModel::class]);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);

        if (isset($params['status']) && !empty($params['status'])) {
            $builder->andWhere('status = :status:', ['status' => $params['status']]);
        }

        if (isset($params['barcode']) && !empty($params['barcode'])) {
            if (is_array($params['barcode'])) {
                $builder->inWhere('barcode', $params['barcode']);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 返回满足条件的物料分类级别下的barcode列表
     * @param array $params['barcode'=>'barcode 搜索']
     * @return mixed
     */
    public function getCategoryBarcodeList($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ms.barcode, ms.name_zh, ms.name_en,ms.name_local,ms.model,ms.brand,ms.unit_zh,ms.unit_en, ms.category_id, mc.name as category_name, mc.code as category_code');
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialCategoryModel::class, 'ms.category_id=mc.id', 'mc');
        $builder->where('ms.is_deleted = :is_deleted: and mc.is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        if (isset($params['barcode']) && !empty($params['barcode'])) {
            if (is_array($params['barcode'])) {
                $builder->andWhere('barcode in ({barcode:array})', ['barcode' => $params['barcode']]);
            }
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 同一个公司的同一个barcode，只能放在一个货主和仓库下
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    public function extendStorageValidation(array $data)
    {
        $default_data = $company = $storage_arr = [];
        foreach ($data as $storage) {
            if ($storage['is_default'] == 1) {
                if (isset($default_data[$storage['company_id']])) {
                    throw new ValidationException(self::$t['material_sau_storage_barcode_company_id_is_default'], ErrCode::$VALIDATE_ERROR);
                } else {
                    $default_data[$storage['company_id']] = 1;
                }
            }
            if (isset($company[$storage['company_id']])) {
                throw new ValidationException(self::$t['material_sau_storage_barcode_company_id_existed'], ErrCode::$VALIDATE_ERROR);
            } else {
                $company[$storage['company_id']] = 1;
            }
            //不管是否同步scm，只要用户传入了数据，就需要存储
            if (!empty($storage['company_id']) || !empty($storage['mach_code']) || !empty($storage['stock_id'])) {
                $storage_arr[] = $storage;
            }
        }
        return $storage_arr;
    }

}
