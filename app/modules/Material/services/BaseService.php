<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\MaterialAssetApplyEnums;
use App\Models\backyard\HeadquartersAddressModel;
use App\Modules\Common\Services\EnumsService;

/**
 * 新资产基础服务类
 * Class BaseService
 * @package App\Modules\Material\Services
 */
class BaseService extends \App\Library\BaseService
{
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * @param $type
     * @param bool $is_update
     * @return array
     */
    public static function getValidateParams($type, $is_update = false)
    {
        $rules = [];
        $type = $type ? : 0;
        if (isset(self::$validate_other[$type])) {
            $rules = self::$validate_other[$type];
        }
        if ($is_update === true) {
            $rules = array_merge($rules, self::$validate_update);
        }

        return array_merge(self::$validate_currency, $rules);
    }

    /**
     * 如果网点名字为空，则赋值Header Office
     * @param $storeName
     * @return string
     */
    public function getStoreName($storeName)
    {
        if(empty($storeName)) {
            return "Header Office";
        }
        return $storeName;
    }

    /**
     * 根据id获取总部地址信息
     * @param integer $id 总部地址id
     * @return array
     */
    public function getHeadQuartersAddressInfoById($id)
    {
        $detail = [];
        if (!empty($id)) {
            $info = HeadquartersAddressModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);
            $detail = !empty($info) ? $info->toArray() : [];
        }
        return $detail;
    }


    /**
     * 检测个人代理是否开启雇佣类型只能申请固定barcode开关
     * @return bool
     */
    public function getPersonalAgentStatus()
    {
        $personal_agent_status = EnumsService::getInstance()->getSettingEnvValue(MaterialAssetApplyEnums::MATERIAL_ASSET_PERSONAL_AGENT_STATUS_KEY, 0);
        return $personal_agent_status ? true : false;
    }

    /**
     * SCM货主列表枚举
     * @return array
     */
    public function getScmCargoOwner()
    {
        $cargo_owner = (new ScmService())->scmCargoOwner();
        $cargo_owner_list = [];
        if (!empty($cargo_owner)) {
            foreach ($cargo_owner as $item) {
                $cargo_owner_list[] = [
                    'value' => $item['mach_code'],
                    'label' => $item['name']
                ];
            }
        }
        return $cargo_owner_list;
    }
}
