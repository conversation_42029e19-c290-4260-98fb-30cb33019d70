<?php
namespace App\Modules\Material\Services;

use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\ScmEnums;
use App\Library\RestClient;
use App\Models\oa\MaterialWmsOutStorageBoxModel;
use App\Models\oa\MaterialWmsOutStorageBoxProductModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Models\bi\SysCityModel;
use App\Models\bi\SysDistrictModel;
use App\Models\bi\SysProvinceModel;
use App\Modules\Purchase\Services\validSignService;
use App\Modules\Organization\Services\DepartmentService;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Shop\Services\OrderService;
use App\Repository\oa\MaterialAttachmentRepository;
use App\Util\RedisKey;
use App\Models\oa\MaterialScmWmsCallbackLogModel;
use App\Models\oa\MaterialWmsOutStorageModel;
use App\Models\oa\MaterialWmsOutStorageProductModel;
use App\Models\oa\MaterialWmsUpdateLogModel;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\User\Services\StaffService;
use App\Modules\Purchase\Services\StorageService;
use App\Library\Enums;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Payment\Services\BaseService  as BaseServiceStore;
use App\Modules\Common\Services\StoreService;
use GuzzleHttp\Exception\GuzzleException;

class WmsOutStorageService extends BaseService
{
    //修改
    public static $validate_update = [
        'id'               => 'Required|IntGt:0',
        'use_staff_id'     => 'Required|IntGt:0',//使用人工号,
        'use_staff_name'   => 'Required|StrLenGeLe:1,50',//员工姓名
        'use_company_id'   => 'Required|IntGt:0',//所属公司ID
        'use_company_name' => 'Required|StrLenGeLe:1,50',//所属公司名称
        'use_store_id'     => 'Required|StrLenGeLe:1,10',//所属网点
        'use_store_name'   => 'Required|StrLenGeLe:1,50',//所属网点名称
        'province_code'    => 'Required|StrLenGeLe:1,4',//省编码
        'city_code'        => 'Required|StrLenGeLe:1,6',//市编码
        'district_code'    => 'Required|StrLenGeLe:1,8',//区编码
        'postal_code'      => 'Required|StrLenGeLe:1,2000',//邮编
        'address'          => 'Required|StrLenGeLe:1,500',//详细地址
        'mach_code'        => 'Required|StrLenGeLe:1,128',//货主code
        'mach_name'        => 'Required|StrLenGeLe:1,128',//货主名称
        'stock_id'         => 'Required|StrLenGeLe:1,10',//仓库id
        'stock_name'       => 'Required|StrLenGeLe:1,128',//仓库id
        'remark'           => 'StrLenGeLe:0,200',//备注
        'products'         => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]'      => 'Required|Obj',
        'products[*].id'   => 'Required|IntGt:0'
    ];


    //修改货主仓库
    public static $validate_save = [
        'id'         => 'Required|IntGt:0',
        'mach_code'  => 'Required|StrLenGeLe:1,128',//货主code
        'mach_name'  => 'Required|StrLenGeLe:1,128',//货主名称
        'stock_id'   => 'Required|StrLenGeLe:1,10',//仓库id
        'stock_name' => 'Required|StrLenGeLe:1,128'//仓库id
    ];

    //筛选条件
    public static $not_must_params = [
        'staff_id',
        'status',
        'node_department_id',
        'company_id',
        'create_id',
        'create_date_begin',
        'create_date_end',
        'pageSize',
        'pageNum',
        'delivery_way'
    ];

    //审核 详情 作废
    public static $validate_share = [
        'id' => 'Required|IntGt:0'
    ];

    //列表搜索校验
    public static $validate_list_search = [
        'wms_no'             => 'StrLenGeLe:0,20',//领用单号
        'staff_id'           => 'IntGt:0',//使用人工号
        'status[*]'          => 'IntIn:' . MaterialWmsEnums::STATUS_WAIT_APPROVE . ',' . MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT . ',' . MaterialWmsEnums::STATUS_OUT . ',' . MaterialWmsEnums::STATUS_CANCEL. ',' . MaterialWmsEnums::STATUS_DRAFT,
        'sys_store_id'       => 'StrLenGeLe:0,10',//所属网点
        'node_department_id' => 'IntGt:0',//部门ID
        'company_id'         => 'IntGt:0',//所属公司
        'barcode'            => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'name'               => 'StrLenGeLe:0,100', //物料名称
        'model'              => 'StrLenGeLe:0,100',//规格型号
        'create_id'          => 'IntGt:0',//处理人工号
        'create_date_begin'  => 'Date',//创建日期-起始
        'create_date_end'    => 'Date',//创建日期-截止
        'pageSize'           => 'IntGt:0',//每页条数
        'pageNum'            => 'IntGt:0', //页码
        'apply_no'           => 'StrLenGeLe:0,50',//关联申请单号
        'use_store_id'       => 'StrLenGeLe:0,10',//使用网点
        'scm_no'             => 'StrLenGeLe:0,50',//scm出库单号
        'delivery_way'       => 'IntIn:' . MaterialClassifyEnums::DELIVERY_WAY_EXPRESS . ',' . MaterialClassifyEnums::DELIVERY_WAY_SELF . '|>>>:delivery_way error',
    ];

    //拆单
    public static $validate_split = [
        'id'                    => 'Required|IntGt:0',
        'products'              => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]'           => 'Required|Obj',
        'products[*].id'        => 'Required|IntGt:0',
        'products[*].split_num' => 'Required|IntGe:0'
    ];

    //操作记录
    public static $validate_history_approval_log = [
        'wms_no' => 'StrLenGeLe:0,50'
    ];

    //总部的成本中心需要检测部门ID合法性
    public static $validate_cost_department_id = [
        'department_id' => 'Required|IntGe:1'
    ];

    //网点类型的成本中心需要检测网点ID合法性
    public static $validate_cost_store_id = [
        'department_id' => 'Required|StrLenGeLe:1,10'
    ];

    //获取成本中心
    public static $validate_department = [
        'type' => 'Required|IntIn:1,2'
    ];

    //获取省 市 区
    public static $validate_area_params = [
        'level' => 'IntGeLe:1,3|>>>:param error[level]',
        'code'  => 'Str'//如果name为空 code就是上级code
    ];

    //搜索使用人
    public static $validate_search_staff = [
        'name' => 'Required|StrLenGeLe:1,50'
    ];

    //获取货主
    public static $validate_warehouse_list = [
        'mach_code' => 'Required|StrLenGeLe:1,128'
    ];

    //选择使用人和使用网点 返回pc_code
    public static $validate_store_by_pc_code = [
        'use_store_id' => 'Required|StrLenGeLe:1,10|>>>:use_store_id error',
        'headquarters' => 'Required|StrLenGeLe:1,10|>>>:headquarters error',
        'use_staff_id'     => 'Required|IntGt:0'
    ];

    //根据快递单号查看物料详情
    public static $validate_express_sn = [
        'express_sn' => 'Required|StrLenGe:1'
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 领用出库-列表
     * @param array $condition 设置参数
     * @return array
     */
    public function getList(array $condition)
    {
        $locale = static::$language;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getListCount($locale, $condition);
            if ($count) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.wms_no, main.apply_no, main.status, main.created_at, main.staff_id, main.staff_name, main.use_staff_id, main.use_staff_name, main.job_name, main.store_name, main.node_department_name, main.company_name, main.processor_id, main.processor_name, main.apply_date, main.mach_code, main.mach_name, main.stock_id, main.stock_name, main.scm_no, main.delivery_way, main.update_to_scm, main.is_submit_scm ';
                $builder->columns($columns);
                $builder->from(['main' => MaterialWmsOutStorageModel::class]);
                $builder->leftjoin(MaterialWmsOutStorageProductModel::class, 'main.id=product.wms_id', 'product');
                $builder = $this->getCondition($locale, $builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $builder->groupBy('main.id');
                $items_obj = $builder->getQuery()->execute();
                $items     = $items_obj ? $items_obj->toArray() : [];
                $items     = $this->handleListItems($locale, $items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material_wms_out_storage_list failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 领用出库-列表
     * @param string $locale 语言
     * @param array $condition 设置参数
     * @param integer $count 导出记录数
     * @return array
     */
    public function getExportList(string $locale, array $condition, $count)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.wms_no, main.scm_no, main.apply_no, main.status, main.staff_id, main.staff_name,main.created_at, main.use_staff_id, main.use_staff_name, main.use_company_name, main.use_department_name, main.use_store_name, main.pc_code,main.receive_name, ';
                $columns .= 'main.receive_store_id, main.province_code, main.city_code, main.district_code, main.postal_code, main.address, main.remark, main.mach_name,main.stock_name, ';
                $columns .= 'product.barcode, product.name_en, product.name_zh, product.name_local, product.model, product.unit_en, product.unit_zh, product.category_code, product.category_name, product.finance_category_code, product.finance_category_name, main.delivery_way, product.apply_num, product.this_time_num, product.real_out_num';
                $builder->columns($columns);
                $builder->from(['main' => MaterialWmsOutStorageModel::class]);
                $builder->leftjoin(MaterialWmsOutStorageProductModel::class, 'main.id=product.wms_id and product.this_time_num > 0 ', 'product');
                //组合搜索条件
                $builder = $this->getCondition($locale, $builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items_obj = $builder->getQuery()->execute();
                $items     = $items_obj ? $items_obj->toArray() : [];
                $items     = $this->handleListItems($locale, $items, true);
                $items = sort_array_by_fields($items, 'apply_no', SORT_DESC, 'barcode', SORT_ASC, 'wms_no', SORT_ASC);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get_material_wms_export_list failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 获取特定条件下的总数
     *
     * @param string $locale 语言
     * @param array $condition 筛选条件组
     * @return int
     * @throws ValidationException
     */
    public function getListCount(string $locale, array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsOutStorageModel::class]);
        $builder->leftjoin(MaterialWmsOutStorageProductModel::class, 'main.id=product.wms_id', 'product');
        //组合搜索条件
        $builder = $this->getCondition($locale, $builder, $condition);
        return (int) $builder->columns('COUNT(DISTINCT main.id) AS total')->getQuery()->getSingleResult()->total;

    }

    /**
     * 组装查询条件
     *
     * @param string $locale 语言
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition(string $locale, object $builder, array $condition)
    {
        //创建时间起始与结束校验
        $create_start = !empty($condition['create_date_begin']) ? $condition['create_date_begin'] : '';
        $create_end   = !empty($condition['create_date_end']) ? $condition['create_date_end'] : '';
        if ($create_start > $create_end) {
            throw new ValidationException(self::$t->_('material_wms_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        $wms_no             = !empty($condition['wms_no']) ? $condition['wms_no'] : ''; //领用单号
        $use_staff_id          = !empty($condition['staff_id']) ? $condition['staff_id'] : 0;//负责人工号
        $processor_id       = !empty($condition['create_id']) ? $condition['create_id'] : 0;//处理人工号
        $status             = !empty($condition['status']) ? $condition['status'] : 0;//状态
        $sys_store_id       = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : '';//使用网点
        $use_store_id       = !empty($condition['use_store_id']) ? $condition['use_store_id'] : '';//使用网点
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//使用部门
        $company_id         = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司

        $barcode  = !empty($condition['barcode']) ? $condition['barcode'] : '';//barcode
        $name     = !empty($condition['name']) ? $condition['name'] : '';//耗材名称
        $model    = !empty($condition['model']) ? $condition['model'] : '';//规格型号
        $apply_no = !empty($condition['apply_no']) ? $condition['apply_no'] : '';//关联申请单号
        $scm_no = !empty($condition['scm_no']) ? $condition['scm_no'] : '';//scm_no code
        $delivery_way = !empty($condition['delivery_way']) ? $condition['delivery_way'] : '';
        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($wms_no)) {
            //领用单号
            $builder->andWhere('main.wms_no = :wms_no:', ['wms_no' => $wms_no]);
        }

        if (!empty($use_staff_id)) {
            $builder->andWhere('(main.use_staff_id = :use_staff_id: or main.use_staff_name = :use_staff_id:)', ['use_staff_id' => $use_staff_id]);
        }

        if (!empty($processor_id)) {
            $builder->andWhere('main.processor_id = :processor_id:', ['processor_id' => $processor_id]);
        }
        if (!empty($status)) {
            if (is_array($status)) {
                $builder->inWhere('main.status ', $status);
            } else {
                $builder->andWhere('main.status = :status:', ['status' => $status]);
            }
        }
        if (!empty($sys_store_id)) {
            $builder->andWhere('main.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        if (!empty($use_store_id)) {
            $builder->andWhere('main.use_store_id = :use_store_id:', ['use_store_id' => $use_store_id]);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids     = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('main.node_department_id', $department_ids);
        }
        if (!empty($company_id)) {
            //所属公司
            $builder->andWhere('main.company_id = :company_id:', ['company_id' => $company_id]);
        }

        if (!empty($create_start) && !empty($create_end)) {
            $create_start .= ' 00:00:00';
            $create_end   .= ' 23:59:59';
            $builder->betweenWhere('main.created_at ', $create_start, $create_end);
        }

        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }

        if (!empty($scm_no)) {
            $builder->andWhere('main.scm_no = :scm_no:', ['scm_no' => $scm_no]);
        }

        if (!empty($barcode)) {
            $builder->andWhere('product.barcode = :barcode:', ['barcode' => $barcode]);
        }
        if (!empty($name)) {
            $builder->andWhere('product.name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local') . ' like :name:', ['name' => '%' . $name . '%']);
        }
        if (!empty($model)) {
            $builder->andWhere('product.model like :model:', ['model' => '%' . $model . '%']);
        }
        if (!empty($delivery_way)) {
            $builder->andWhere('main.delivery_way = :delivery_way:', ['delivery_way' => $delivery_way]);
        }

        return $builder;
    }

    /**
     * 格式化出库单列表
     * @param string $locale 语言
     * @param array $items 台账列表
     * @param bool $export 导出
     * @return array
     */
    private function handleListItems(string $locale, array $items, $export = false)
    {
        if (empty($items)) {
            return [];
        }
        if ($export === true) {
            //导出
            $row_value = [];
            //获取省信息
            $province_code = array_values(array_filter(array_unique(array_column($items, 'province_code'))));
            $province_list = SysProvinceModel::find([
                'columns'    => 'name, code, en_name',
                'conditions' => 'code in ({code:array})',
                'bind'       => ['code' => $province_code]
            ])->toArray();
            $province_list = $province_list ? array_column($province_list, null, 'code') : [];
            //获取市信息
            $city_code = array_values(array_filter(array_unique(array_column($items, 'city_code'))));
            $city_list = SysCityModel::find([
                'columns'    => 'name, code, en_name',
                'conditions' => 'code in ({code:array})',
                'bind'       => ['code' => $city_code]
            ])->toArray();
            $city_list = $city_list ? array_column($city_list, null, 'code') : [];
            //获取区信息
            $district_code = array_values(array_filter(array_unique(array_column($items, 'district_code'))));
            $district_list = SysDistrictModel::find([
                'columns'    => 'name, code, en_name',
                'conditions' => 'code in ({code:array})',
                'bind'       => ['code' => $district_code]
            ])->toArray();
            $district_list = $district_list ? array_column($district_list, null, 'code') : [];
            //组装导出需要的数据
            foreach ($items as $item) {
                $name        = $item['name_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $area        = $locale != 'th' ? 'en_name' : 'name';
                $name        = empty($name) ? $item['name_en'] : $name;
                $row_value[] = [
                    'wms_no'                => $item['wms_no'],
                    'scm_no'                => $item['scm_no'],
                    'apply_no'              => $item['apply_no'],
                    'status'                =>  MaterialWmsEnums::$wms_out_storage_status[$item['status']] ? static::$t[MaterialWmsEnums::$wms_out_storage_status[$item['status']]] : '',
                    'staff_id'              => $item['staff_id'],
                    'staff_name'            => $item['staff_name'],
                    'created_at'            => $item['created_at'],
                    'use_staff_id'          => $item['use_staff_id'],
                    'use_staff_name'        => $item['use_staff_name'],
                    'use_company_name'      => $item['use_company_name'],
                    'use_department_name'   => $item['use_department_name'],
                    'use_store_name'        => $item['use_store_name'],
                    'pc_code'               => $item['pc_code'],
                    'receive_store_id'      => $item['receive_name'],
                    'province_name'         => !empty($province_list[$item['province_code']]) ? $province_list[$item['province_code']][$area] : '',
                    'city_name'             => !empty($city_list[$item['city_code']]) ? $city_list[$item['city_code']][$area] : '',
                    'district_name'         => !empty($district_list[$item['district_code']]) ? $district_list[$item['district_code']][$area] : '',
                    'postal_code'           => $item['postal_code'],
                    'address'               => $item['address'],
                    'remark'                => $item['remark'],
                    'mach_name'             => $item['mach_name'],
                    'stock_name'            => $item['stock_name'],
                    'barcode'               => $item['barcode'],
                    'name'                  => $name,
                    'model'                 => $item['model'],
                    'unit'                  => $item['unit_' . (MaterialClassifyEnums::$language_fields[$locale] ?? 'en')],
                    'apply_num'             => $item['apply_num'],
                    'this_time_num'         => $item['this_time_num'],
                    'real_out_num'          => $item['real_out_num'],
                    'category_code'         => $item['category_code'],
                    'category_name'         => $item['category_name'],
                    'finance_category_code' => $item['finance_category_code'],
                    'finance_category_name' => $item['finance_category_name'],
                    'delivery_way_name'          =>  static::$t[MaterialClassifyEnums::$delivery_way_arr[$item['delivery_way']]],
                ];
            }
            $items = $row_value;
        } else {
            foreach ($items as &$item) {
                $item['status_text'] = static::$t[MaterialWmsEnums::$wms_out_storage_status[$item['status']]];
                $item['created_at']  = $item['created_at'] ? date('Y-m-d', strtotime($item['created_at'])) : '';
                $item['delivery_way_name'] = static::$t[MaterialClassifyEnums::$delivery_way_arr[$item['delivery_way']]];
            }
        }
        return $items;
    }

    /**
     * 耗材出库导出的表头
     */
    public function getExportHeader()
    {
        return [
            static::$t->_('material_wms_out_wms_no'),//OA耗材出库单号
            static::$t->_('material_wms_out_scm_no'),//SCM出库单号
            static::$t->_('material_wms_out_apply_no'),//OA耗材申请单号
            static::$t->_('material_wms_out_status'),//OA耗材出库单号状态
            static::$t->_('material_wms_staff_info_id'),//创建人工号
            static::$t->_('material_wms_staff_info_name'),//创建人姓名
            static::$t->_('material_wms_created_at'),//创建时间
            static::$t->_('material_wms_receive_store_id'),//收货人工号
            static::$t->_('material_wms_receive_name'),//收货人姓名
            static::$t->_('acceptance_cost_company'),//费用所属公司
            static::$t->_('acceptance_cost_department'),//费用所属部门
            static::$t->_('bank_flow_export_field_create_store_name'),//费用所属网点
            static::$t->_('sap_cost_center'),//成本中心
            static::$t->_('material_wms_out_receive_name'),//收件人信息
            static::$t->_('material_wms_out_province'),//省
            static::$t->_('material_wms_out_city'),//市
            static::$t->_('material_wms_out_district'),//区
            static::$t->_('material_wms_out_postal'),//邮编
            static::$t->_('material_wms_out_address'),//详细地址
            static::$t->_('material_wms_out_remark'),//备注
            static::$t->_('material_wms_out_mach_name'),//货主
            static::$t->_('material_wms_out_stock_id'),//仓库
            static::$t->_('material_wms_barcode'),//barcode
            static::$t->_('material_wms_name'),//耗材名称
            static::$t->_('material_wms_model'),//规格型号
            static::$t->_('material_wms_unit'),//基本单位
            static::$t->_('material_wms_apply_num'),//申请数量
            static::$t->_('material_wms_this_time_num'),//审核数量
            static::$t->_('material_wms_real_quantity_received'),//出库数量
            static::$t->_('material_wms_finance_category_code'),//财务分类编码
            static::$t->_('material_wms_finance_category_name'),//财务分类名称
            static::$t->_('material_wms_category_code'),//物料分类编码
            static::$t->_('material_wms_category_name'),//物料分类名称
            static::$t->_('delivery_way'),//配送方式
        ];
    }


    /**
     * 领用出库-撤回
     * @param array $params 请求入参
     * @return array
     */
    public function cancel(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = true;
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $wms_out_storage = $this->getMaterialWmsOutStorageInfo($params['id']);
            $out_storage_product      = $wms_out_storage->getProducts();
            $out_storage_product_data = $out_storage_product->toArray();
            if (!in_array($wms_out_storage->status, [MaterialWmsEnums::STATUS_WAIT_APPROVE, MaterialWmsEnums::STATUS_DRAFT])) {
                throw new ValidationException(self::$t['material_wms_out_cancel_error'], ErrCode::$VALIDATE_ERROR);
            }
            $result = true;
            $available_inventory = [];
            if ($wms_out_storage->status == MaterialWmsEnums::STATUS_WAIT_APPROVE && !empty($wms_out_storage->mach_code) && !empty($wms_out_storage->scm_no)) {
                $scm    = new ScmService();
                $result = $scm->cancelOutbound($wms_out_storage->mach_code, $wms_out_storage->scm_no);
                //作废的时候 去固化库存
                $wms_out_storage_arr = $wms_out_storage->toArray();
                $available_inventory = $this->getSearchAvailableInventory($wms_out_storage_arr, $out_storage_product_data);
                $this->logger->info('material_wms_out_storage_cancel front:' . '详情数据:'. json_encode($out_storage_product, JSON_UNESCAPED_UNICODE) . '库存数据:' . json_encode($available_inventory, JSON_UNESCAPED_UNICODE));
            }
            $this->logger->info('material_wms_out_storage_cancel front:' . json_encode($out_storage_product, JSON_UNESCAPED_UNICODE));
            if ($result) {
                //scm出库成功
                $now_time                    = date('Y-m-d H:i:s');
                $wms_out_storage->status     = MaterialWmsEnums::STATUS_CANCEL;
                $wms_out_storage->updated_at = $now_time;
                $bool                        = $wms_out_storage->save();
                if ($bool === false) {
                    throw new BusinessException('领用耗材出库单撤回失败: ' . json_encode($wms_out_storage->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($wms_out_storage), ErrCode::$MATERIAL_WMS_OUT_STORAGE_CANCEL_ERROR);
                }

                if (!empty($out_storage_product_data) && !empty($available_inventory)) {
                    //更新出库单明细行
                    foreach ($out_storage_product as $product) {
                        $product->available_inventory = $available_inventory[$product->barcode] ?? 0;
                        $product->updated_at          = date('Y-m-d H:i:s', time());
                        $bool                         = $product->save();
                        if ($bool === false) {
                            throw new BusinessException('耗材出库单修改明细更新可用库存失败: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_WMS_OUT_STORAGE_CANCEL_ERROR);
                        }
                    }
                }
                $db->commit();
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_cancel failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result
        ];
    }

    /**
     * 耗材出库数据
     * @param int $id 耗材出库单ID
     * @return mixed
     * @throws ValidationException
     */
    public function getMaterialWmsOutStorageInfo(int $id)
    {
        $wms_out_storage = MaterialWmsOutStorageModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind'       => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($wms_out_storage)) {
            throw new ValidationException(self::$t['material_wms_out_storage_not_found'], ErrCode::$VALIDATE_ERROR);
        }
        return $wms_out_storage;
    }

    /**
     * 领用出库-详情
     * @param array $params 请求入参
     * @return array
     */
    public function detail(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail  = [];
        try {
            $wms_out_storage            = $this->getMaterialWmsOutStorageInfo($params['id']);
            $detail                     = $wms_out_storage->toArray();
            $wms_apply                  = $wms_out_storage->getMaterialWmsApply()->toArray();
            $detail['delivery_way_name'] = static::$t[MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]];
            $detail['apply_created_at'] = date('Y-m-d', strtotime($wms_apply['created_at']));
            $detail['attachments']      = $wms_out_storage->getAttachment()->toArray();
            $detail['pc_code_required'] = MaterialSettingService::getPcCodeRequired($detail['use_staff_id']);
            $products                   = $wms_out_storage->getProducts()->toArray();
            $detail['products']         = $products ? $this->handleDetail($products, $detail) : [];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_out_storage_detail failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $detail
        ];
    }

    /**
     * 根据含有barcode的数据中找到barcode并获取barcode的图片附件信息
     * @param array $list 含有barcode的数据
     * @return array
     */
    private function handleBarcode(array $list)
    {
        $barcode_key_list = [];
        $pic_arr_key_arr  = [];
        if (!empty($list)) {
            $barcode_arr = array_values(array_filter(array_unique(array_column($list, 'barcode'))));
            if (empty($barcode_arr)) {
                return [$barcode_key_list, $pic_arr_key_arr];
            }
            $barcode_list = MaterialSauModel::find([
                'columns'    => 'id, barcode',
                'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                'bind'       => ['barcode' => $barcode_arr, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();
            if (!empty($barcode_list)) {
                $barcode_key_list = array_column($barcode_list, null, 'barcode');
                //获取barcode图片信息
                $materialAttachment = new  MaterialAttachmentModel();
                $pic_arr_key_arr    = $materialAttachment->getColumnArr($barcode_list);
            }
        }
        return [$barcode_key_list, $pic_arr_key_arr];
    }

    /**
     * 格式化详情信息
     * @param array $product_list 出库单明细列表
     * @param array $detail 详情数据
     * @return array
     */
    private function handleDetail(array $product_list, array $detail = [])
    {
        if (!empty($product_list)) {

            [$barcode_key_list, $pic_arr_key_arr] = $this->handleBarcode($product_list);
            $barcode_available_inventory = [];
            if (!empty($detail['mach_code']) && !empty($detail['stock_id']) && !in_array($detail['status'], [MaterialWmsEnums::STATUS_OUT, MaterialWmsEnums::STATUS_CANCEL]) ) {
                $scm_service     = new ScmService();
                $scm_result      = $scm_service->goodsStock($detail['mach_code'], $detail['stock_id'], implode(',', array_keys($barcode_key_list)), static::$language);
                $scm_result_data = $scm_result['data'];
                if (!empty($scm_result_data)) {
                    $barcode_available_inventory = !empty($scm_result_data['stock']) ? $scm_result_data['stock'] : [];
                }
            }

            $barcode_arr = array_values(array_unique(array_column($product_list, 'barcode')));
            $use_val_arr = WmsApplyService::getInstance()->getBarcodeList($barcode_arr);
            foreach ($product_list as &$product) {
                if (!in_array($detail['status'], [MaterialWmsEnums::STATUS_OUT, MaterialWmsEnums::STATUS_CANCEL])) {
                    $product['available_inventory'] = !empty($barcode_available_inventory[$product['barcode']]) ? $barcode_available_inventory[$product['barcode']]['availableInventory'] : static::$t->_('material_sau_not_existed');
                }
                $product['pic'] = !empty($barcode_key_list[$product['barcode']]) && !empty($pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']]) ? $pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']] : [];
                $product['use_val'] = $use_val_arr[$product['barcode']] ?? 0;
            }
        }
        return $product_list ?? [];
    }


    /**
     * 获取特定条件下的总数 主要用于导出数据
     *
     * @param string $locale 语言
     * @param array $condition 条件
     * @return int
     * @throws ValidationException
     */
    public function getExportListCount(string $locale, array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsOutStorageModel::class]);
        $builder->leftjoin(MaterialWmsOutStorageProductModel::class, 'main.id=product.wms_id and product.this_time_num > 0', 'product');
        //组合搜索条件
        $builder = $this->getCondition($locale, $builder, $condition);
        return (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
    }


    /**
     * SCM出库成功 回调OA接口
     * @param array $params scm回调请求参数
     * @return array
     */
    public function wmsScmCallback($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $this->logger->info('wms_scm_callback_log' . json_encode($params, JSON_UNESCAPED_UNICODE));
            if ((new validSignService())->validator($params)) {
                $response = $this->updateStorageOrder($params);
                $code     = $response['code'];
                $message  = $response['message'];
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('wms_scm_callback_failed:' . $real_message);
        }
        $this->logger->info('wms_scm_callback_failed_return: code=' . $code . ';message=' . $message . ';data=' . json_encode($data, JSON_UNESCAPED_UNICODE));
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }


    /**
     * OA回调接口-SCM出库成功-变更出库单信息
     * @param array $params scm回调请求次参数
     * @return array
     */
    public function updateStorageOrder(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now_time         = date('Y-m-d H:i:s');
            $out_storage_data = MaterialWmsOutStorageModel::findFirst([
                'conditions' => 'scm_no = :orderSn: and status in ({status:array}) and is_deleted = :is_deleted:',
                'bind'       => [
                    'orderSn' => $params['orderSn'],
                    'status'  => [MaterialWmsEnums::STATUS_WAIT_APPROVE, MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT], 'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($out_storage_data)) {
                throw new ValidationException('scm单号不存在或状态不对' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }
            $old_main_model = $out_storage_data->toArray();
            //查询当前出库单商品信息
            $products         = $out_storage_data->getProducts();
            $storage_products = $products->toArray();

            //比较出库单商品是否一致
            $product_barcode_arr = array_column($storage_products, 'barcode');
            $params_barcode_arr  = array_column($params['goods'], 'barCode');
            if (array_diff($product_barcode_arr, $params_barcode_arr)) {
                throw new ValidationException('scm实际出库商品不符' . $params['orderSn'], ErrCode::$VALIDATE_ERROR);
            }

            $storage_products = array_column($storage_products, null, 'barcode');
            //组装出库单明细更新信息组
            $material_wms_out_storage_product_data = [];
            foreach ($params['goods'] as $good) {
                //组装出库单行信息更新数据组
                $material_wms_out_storage_product_data[$storage_products[$good['barCode']]['id']] = [
                    'real_out_num' => $good['num']
                ];
            }
            //更新出库单信息
            $out_storage_data->status     = MaterialWmsEnums::STATUS_OUT;
            $out_storage_data->out_bound_date = !empty($params['timestamp']) ? date('Y-m-d', $params['timestamp']) : null;
            $out_storage_data->updated_at = $now_time;
            $bool                         = $out_storage_data->save();
            if ($bool === false) {
                //更新出库单信息失败
                throw new BusinessException('SCM出库成功回调更新出库单信息更新失败: ' . json_encode($out_storage_data->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($out_storage_data), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR);
            }
            //查询可用库存
            $available_inventory = $this->getSearchAvailableInventory($old_main_model, $storage_products);
            //更新出库单明细行
            foreach ($products as $product) {
                $update_product_info   = $material_wms_out_storage_product_data[$product->id];
                $product->real_out_num = $update_product_info['real_out_num'];
                $product->available_inventory = $available_inventory[$product->barcode] ?? 0;
                $product->updated_at   = $now_time;
                $bool                  = $product->save();
                if ($bool === false) {
                    //更新出库单明细行失败
                    throw new BusinessException('SCM出库成功回调 出库单行明细更新失败: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR);
                }
            }

            $scm_wms_callback_log                  = new MaterialScmWmsCallbackLogModel();
            $scm_wms_callback_log->scm_return_json = json_encode($params, JSON_UNESCAPED_UNICODE);
            $scm_wms_callback_log->scm_no          = $params['orderSn'];
            $scm_wms_callback_log->wms_no          = $out_storage_data->wms_no;
            $scm_wms_callback_log->scm_status = 1;
            $scm_wms_callback_log->reason     = '';
            $scm_wms_callback_log->created_at = $now_time;
            $log_bool                         = $scm_wms_callback_log->save();
            if ($log_bool === false) {
                throw new BusinessException('SCM出库回调记录日志失败: ' . json_encode($scm_wms_callback_log->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg(scm_wms_callback_log), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR);
            }

            $before_data = $out_storage_data->toArray();
            $user        = ['id' => $before_data['staff_id'], 'name' => $before_data['staff_name']];
            WmsOutStorageService::getInstance()->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_OUTBOUND, $old_main_model, $before_data, $user);

            //V22106 调用SCM新版查看出库单详情接口，获取出库单对应的所有箱单号、运单号、商品条码(Barcode)、商品数量，并后台进行存储，同时增加运单状态：默认未妥投，妥投日期：默认为空
            $this->saveStorageBox($out_storage_data);

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $code = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material_wms_out_storage_scm_recall failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * V22106 调用SCM新版查看出库单详情接口，获取出库单对应的所有箱单号、运单号、商品条码(Barcode)、商品数量，并后台进行存储，同时增加运单状态：默认未妥投，妥投日期：默认为空
     * @param object $out_storage_data 出库单对象信息
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    private function saveStorageBox($out_storage_data)
    {
        //传递了我们的出库单号、scm的出库单号，data数组元素只有一条
        $scm_detail = (new ScmService())->outBoundOrderDetail($out_storage_data->mach_code, ['orderSn' => $out_storage_data->wms_no, 'outSn' => $out_storage_data->scm_no]);
        if (!empty($scm_detail[0]['box'])) {
            $box = $scm_detail[0]['box'];
            foreach ($box as $item) {
                $one_box_data = [
                    'wms_id'        => $out_storage_data->id,
                    'wms_no'        => $out_storage_data->wms_no,
                    'box_sn'        => $item['boxSn'],
                    'express_sn'    => $item['expressSn'][0],//我们只取第一个
                    'delivery_date' => null,
                    'status'        => MaterialWmsEnums::MATERIAL_WMS_BOX_STATUS_NOT_DELIVERED,
                    'created_at'    => $out_storage_data->updated_at,
                    'updated_at'    => $out_storage_data->updated_at,
                ];
                $material_wms_out_storage_box = new MaterialWmsOutStorageBoxModel();
                $bool = $material_wms_out_storage_box->i_create($one_box_data);
                if ($bool === false) {
                    throw new BusinessException('SCM出库成功记录出库单对应的所有箱单号、运单号失败: ' . json_encode($one_box_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($material_wms_out_storage_box), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR);
                }
                $one_box_data_goods = [];
                foreach ($item['goods'] as $good) {
                    $one_box_data_goods[] = [
                        'box_id'      => $material_wms_out_storage_box->id,
                        'barcode'     => $good['barCode'],
                        'product_num' => $good['num'],
                        'created_at'  => $out_storage_data->updated_at,
                        'updated_at'  => $out_storage_data->updated_at,
                    ];
                }
                if (!empty($one_box_data_goods)) {
                    $material_wms_out_storage_box_product = new MaterialWmsOutStorageBoxProductModel();
                    $bool = $material_wms_out_storage_box_product->batch_insert($one_box_data_goods);
                    if ($bool === false) {
                        throw new BusinessException('SCM出库成功记录出库单对应的所有箱单号、运单号-商品明细失败: ' . json_encode($material_wms_out_storage_box_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($material_wms_out_storage_box_product), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SCM_RECALL_ERROR);
                    }
                }
            }
        }
        return true;
    }


    /**
     * 耗材领用出库-修改
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    public function edit(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->saveUpdateDb($params, $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_edit failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 耗材领用出库-修改-保存逻辑
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param boolean $can_audit 是否需要审核，false否，true是
     * @throws BusinessException
     * @throws ValidationException
     */
    private function saveUpdateDb(array $params, array $user, bool $can_audit = false)
    {
        $main_model = MaterialWmsOutStorageModel::findFirst([
            'conditions' => 'id = :id: and status = :status: and  is_submit_scm = :is_submit_scm: and is_deleted = :is_deleted:',
            'bind' => [
                'id' => $params['id'],
                'status' => MaterialWmsEnums::STATUS_DRAFT,
                'is_submit_scm' => MaterialWmsEnums::IS_SUBMIT_SCM_DENY,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
        if (empty($main_model)) {
            throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
        }

        $pc_code_required = MaterialSettingService::getPcCodeRequired($main_model->use_staff_id);
        if ($pc_code_required && empty($params['pc_code'])) {
            throw new ValidationException(static::$t->_('material_wms_pc_code_not_null'), ErrCode::$VALIDATE_ERROR);
        }

        $out_storage_product = $main_model->getProducts();
        $out_storage_product_data = $out_storage_product->toArray();
        $old_main_model = $main_model->toArray();
        $old_main_model['products'] = $out_storage_product_data;
        $out_storage_data = $this->getOutStorageData($params);
        if ($can_audit === false) {
            $bool = $main_model->i_update($out_storage_data);
            if ($bool === false) {
                throw new BusinessException('耗材出库单修改失败:' . json_encode($out_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
            }
        }
        if (!empty($out_storage_product_data)) {
            $product_ids = array_diff(array_column($out_storage_product_data, 'id'), array_column($params['products'], 'id'));
            if (!empty($product_ids)) {
                //更新出库单明细行
                foreach ($out_storage_product as $product) {
                    if (in_array($product->id, $product_ids)) {
                        $product->is_deleted = GlobalEnums::IS_DELETED;
                    }
                    $product->updated_at = date('Y-m-d H:i:s', time());
                    $bool                = $product->save();
                    if ($bool === false) {
                        throw new BusinessException('耗材出库单修改明细更新失败: ' . json_encode($product->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
                    }
                }
            }
        }
        $before_data = $main_model->toArray();
        $before_data['products'] = $main_model->getProducts()->toArray();
        $this->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_SAVE, $old_main_model, $before_data, $user);

        //提交scm审核
        if ($can_audit && empty($main_model->scm_no)) {
            $this->saveAuditDb($main_model, $out_storage_product_data, $user, $out_storage_data);
        }
    }

    /**
     * 耗材领用出库-修改货主仓库
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function save(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $main_model = MaterialWmsOutStorageModel::findFirst([
                'conditions' => 'id = :id: and status = :status: and is_deleted = :is_deleted:',
                'bind'       => ['id'         => $params['id'],
                                 'status'     => MaterialWmsEnums::STATUS_DRAFT,
                                 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $old_main_model   = $main_model->toArray();
            $out_storage_data = [
                'mach_code'  => $params['mach_code'],
                'mach_name'  => $params['mach_name'],
                'stock_id'   => $params['stock_id'],
                'stock_name' => $params['stock_name']
            ];

            $bool = $main_model->i_update($out_storage_data);
            if ($bool === false) {
                throw new BusinessException('耗材出库单修改失败:' . json_encode($out_storage_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
            }
            $before_data = $main_model->toArray();
            $this->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_APPOINT, $old_main_model, $before_data, $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_edit failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }


    /**
     * 创建审批流 格式化数据
     * @param array $params 请求参数组
     * @return  array
     **/
    private function getOutStorageData(array $params)
    {
        return [
            'use_staff_id'        => $params['use_staff_id'],
            'use_staff_name'      => $params['use_staff_name'],
            'use_company_id'      => $params['use_company_id'],
            'use_company_name'    => $params['use_company_name'],
            'use_department_id'   => $params['use_department_id'],
            'use_department_name' => $params['use_department_name'],
            'use_store_id'        => $params['use_store_id'],
            'use_store_name'      => $params['use_store_name'],
            'province_code'       => $params['province_code'],
            'city_code'           => $params['city_code'],
            'district_code'       => $params['district_code'],
            'pc_code'             => $params['pc_code'],
            'postal_code'         => $params['postal_code'],
            'address'             => $params['address'],
            'remark'              => $params['remark'] ?? '',
            'mach_code'           => $params['mach_code'],
            'mach_name'           => $params['mach_name'],
            'stock_id'            => $params['stock_id'],
            'stock_name'          => $params['stock_name'],
            'delivery_way'        => $params['delivery_way'],
            'updated_at'          => date('Y-m-d H:i:s', time())
        ];

    }

    /**
     * 耗材领用出库-提交审核
     * @param array $params 请求参数组
     * @param array $user 用户数据
     * @return array
     */
    public function audit(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $main_model = MaterialWmsOutStorageModel::findFirst([
                'conditions' => "id = :id: and status = :status: and scm_no = '' and  is_submit_scm = :is_submit_scm: and is_deleted = :is_deleted:",
                'bind' => [
                    'id' => $params['id'],
                    'status' => MaterialWmsEnums::STATUS_DRAFT,
                    'is_submit_scm' => MaterialWmsEnums::IS_SUBMIT_SCM_DENY,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                ],
            ]);
            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $out_storage_product = $main_model->getProducts();
            $out_storage_product_data = $out_storage_product->toArray();
            if (empty($out_storage_product_data)) {
                throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            $this->saveAuditDb($main_model, $out_storage_product_data, $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_audit failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }

    /**
     * @param object $main_model 出库单对象信息
     * @param array $out_storage_product_data 出库单产品明细
     * @param array $user 当前登陆者用户信息组
     * @param array $out_storage_data 组信息
     * @throws BusinessException
     * @throws ValidationException
     */
    private function saveAuditDb($main_model, $out_storage_product_data, $user, $out_storage_data = [])
    {
        $barcode_arr = array_unique(array_filter(array_column($out_storage_product_data, 'barcode')));
        if (empty($barcode_arr)) {
            throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        $material_sau_arr = MaterialSauModel::find([
            'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
            'bind' => [
                'barcode' => $barcode_arr,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ])->toArray();
        $scm = new ScmService();
        $this->validationOutStorageSubmit($main_model, $out_storage_product_data, $material_sau_arr, $scm);

        //同步scm添加出库
        $main_data = $main_model->toArray();
        $header_list = (new StoreService())->getHeaderListByCondition();
        $header_list_arr = array_merge(array_column($header_list, 'id'), [Enums::HEAD_OFFICE_STORE_FLAG]);
        $sys_store_id = in_array($main_data['use_store_id'], $header_list_arr) ? Enums::HEAD_OFFICE_STORE_FLAG : $main_data['use_store_id'];
        $main_data['staff_id'] = $main_data['use_staff_id'];
        $main_data['staff_name'] = $main_data['use_staff_name'];
        $apply_data = array_merge($main_data, ['no' => $main_data['wms_no']], ['mark' => $main_data['remark']], ['bizExt' => (string)MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS], ['sys_store_id' => $sys_store_id]);
        $apply_data['products'] = $out_storage_product_data;

        //历史逻辑处理：审核的时候传输scm的配送方式未转换为scm的枚举同拆单处理逻辑；若是审核操作获取表里的，若是保存并审核操作获取前端提交的
        $delivery_way = $out_storage_data ? $out_storage_data['delivery_way'] : $main_data['delivery_way'];
        $scm_data = $apply_data;
        $scm_data['deliveryWay'] = ScmService::$delivery_way_list[$delivery_way];
        $scm_no = $scm->warehouseAdd($main_data['mach_code'], $main_data['stock_id'], $scm_data);
        $this->logger->info('material_wms_out_storage_audit data:' . json_encode($scm_no, JSON_UNESCAPED_UNICODE) . ' mach_code data :' . json_encode($main_data, JSON_UNESCAPED_UNICODE) . ' submit data' . json_encode($apply_data, JSON_UNESCAPED_UNICODE));
        if (!empty($scm_no)) {
            $out_storage_data['status'] = MaterialWmsEnums::STATUS_WAIT_APPROVE;
            $out_storage_data['scm_no'] = $scm_no;
            $out_storage_data['is_submit_scm'] = MaterialWmsEnums::IS_SUBMIT_SCM_CORRECT;
            $out_storage_data['updated_at'] = date('Y-m-d H:i:s', time());
            $bool = $main_model->i_update($out_storage_data);
            if ($bool === false) {
                throw new BusinessException('耗材出库提交审核失败:' . json_encode($apply_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
            }
        }

        $old_main_model = ['wms_no' => $main_data['wms_no'], 'status' => $main_data['status']];
        $before_data = $main_model->toArray();
        WmsOutStorageService::getInstance()->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_EXAMINE, $old_main_model, $before_data, $user);
    }

    /**
     * 出库数据校验
     * @param object $main_model 出库数据
     * @param array $out_storage_product_data 出库单详情
     * @param array $barcode_arr barcode barcode集合
     * @param object $scm scm对象
     * @throws ValidationException
     */
    public function validationOutStorageSubmit(object $main_model, array $out_storage_product_data, array $barcode_arr, object $scm)
    {
        //不能为空的数据校验
        if (empty($main_model->province_code) || empty($main_model->city_code) || empty($main_model->district_code) || empty($main_model->postal_code) || empty($main_model->address)) {
            throw new ValidationException(static::$t->_('material_wms_area_by_postal_not_null'), ErrCode::$VALIDATE_ERROR);
        }

        if (empty($main_model->mach_code) || empty($main_model->stock_id)) {
            throw new ValidationException(static::$t->_('material_wms_mach_code_by_stock_id_not_null'), ErrCode::$VALIDATE_ERROR);
        }
        //成本中心不可为空
        $pc_code = MaterialSettingService::getPcCodeToScmRequired();
        if ($pc_code && empty($main_model->pc_code)) {
            throw new ValidationException(static::$t->_('material_wms_pc_code_not_null'), ErrCode::$VALIDATE_ERROR);
        }

        foreach ($barcode_arr as $item) {
            if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF || $item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
                throw new ValidationException(static::$t->_('material_wms_update_to_scm_not_wms', ['barcode' => $item['barcode']]), ErrCode::$VALIDATE_ERROR);
            }
            if ($item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE) {
                throw new ValidationException(static::$t->_('material_wms_barcode_enable_or_delete', ['barcode' => $item['barcode']]), ErrCode::$VALIDATE_ERROR);
            }
        }
        //提交的时候再次查询可用库存
        $scm_result = $scm->goodsStock($main_model->mach_code, $main_model->stock_id, implode(',', array_column($barcode_arr, 'barcode')), static::$language);

        $scm_result_data = $scm_result['data'];

        $this->logger->info('material_wms_out_storage_audit_goods_stock data:' . json_encode($main_model, JSON_UNESCAPED_UNICODE) . ' scm result data :' . json_encode($scm_result, JSON_UNESCAPED_UNICODE));

        if (!empty($scm_result_data)) {
            $has_stock_barcode = !empty($scm_result_data['stock']) ? $scm_result_data['stock'] : [];
        }
        if (!empty($out_storage_product_data)) {
            foreach ($out_storage_product_data as $product) {
                $barcode = $product['barcode'];
                if (!empty($has_stock_barcode) && !empty($has_stock_barcode[$barcode]) && isset($has_stock_barcode[$barcode]['availableInventory'])) {
                    //申请的数量大于可用库存
                    if ($product['this_time_num'] > $has_stock_barcode[$barcode]['availableInventory']) {
                        throw new ValidationException(static::$t->_('material_wms_apply_num_error'), ErrCode::$VALIDATE_ERROR);
                    }
                } else {
                    //未查询到对应的brcode数据
                    throw new ValidationException(static::$t->_('material_wms_apply_submit_not_barcode_null', ['barcode' => $barcode]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
    }

    /**
     * 领用出库-作废
     * @param array $params 请求入参
     * @return array
     */
    public function abolish(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result  = true;
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $wms_out_storage = $this->getMaterialWmsOutStorageInfo($params['id']);
            if (!in_array($wms_out_storage->status, [MaterialWmsEnums::STATUS_WAIT_APPROVE, MaterialWmsEnums::STATUS_DRAFT])) {
                throw new ValidationException(self::$t['material_wms_out_abolish_error'], ErrCode::$VALIDATE_ERROR);
            }
            if (!empty($wms_out_storage->mach_code) && !empty($wms_out_storage->scm_no)) {
                $scm    = new ScmService();
                $result = $scm->cancelOutbound($wms_out_storage->mach_code, $wms_out_storage->scm_no);
            }
            if ($result) {
                //scm出库成功
                $now_time                    = date('Y-m-d H:i:s');
                $wms_out_storage->status     = MaterialWmsEnums::STATUS_CANCEL;
                $wms_out_storage->updated_at = $now_time;
                $bool                        = $wms_out_storage->save();
                if ($bool === false) {
                    throw new BusinessException('领用耗材出库单作废失败: ' . json_encode($wms_out_storage->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($wms_out_storage), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ABOLISH_ERROR);
                }

                $db->commit();
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_cancel failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result
        ];
    }

    /**
     * 耗材领用出库-查看-查看操作记录
     * @Date: 8/31/22 11:03 AM
     * @param array $condition 条件参数
     * @return array
     */
    public function historyApprovalLogList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        if (!empty($condition['wms_no'])) {
            try {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialWmsUpdateLogModel::class]);
                $builder->andWhere('main.wms_no = :wms_no:', ['wms_no' => $condition['wms_no']]);
                $count = (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
                if ($count) {
                    $builder->columns('*');
                    $builder->limit($page_size, $offset);
                    $builder->orderby('main.id desc');
                    $items = $builder->getQuery()->execute()->toArray();
                    $items     = $this->handleUpdateLog($items);
                }
                $data['items']                     = $items ?? [];
                $data['pagination']['total_count'] = $count;
            } catch (\Exception $e) {
                $code         = ErrCode::$SYSTEM_ERROR;
                $message      = static::$t->_('retry_later');
                $real_message = $e->getMessage() . $e->getTraceAsString();
                $this->logger->warning('material_wms_update_log_detail_list:' . $real_message);
            }
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 添加log
     * @param int $type 1创建，2修改，3指定仓库，4拆分，5提交审核，6出库
     * @param array $before_data 提交数据库的数据
     * @param array $after_data 提交要修改的数据
     * @param array $user 提交人数据
     **/
    public function addMaterialWmsUpdateLog(int $type, array $before_data, array $after_data, array $user)
    {
        $now_time = date('Y-m-d H:i:s', time());
        $log_data = [
            'wms_no'         => $before_data['wms_no'],
            'before_status'  => $before_data['status'] ?? 0,
            'after_status'   => $after_data['status'] ?? MaterialWmsEnums::STATUS_WAIT_APPROVE,
            'type'           => $type ?? 0,
            'staff_id'       => $user['id'],
            'staff_name'     => $user['name'],
            'before_content' => json_encode($before_data, JSON_UNESCAPED_UNICODE),
            'after_content'  => json_encode($after_data, JSON_UNESCAPED_UNICODE),
            'is_deleted'     => GlobalEnums::IS_NO_DELETED,
            'created_at'     => $now_time,
            'updated_at'     => $now_time
        ];

        $log  = new MaterialWmsUpdateLogModel();
        $bool = $log->i_create($log_data);
        if ($bool === false) {
            throw new BusinessException('领用出库单操作日志添加失败 :' . json_encode($log_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($log), ErrCode::$MATERIAL_WMS_OUT_STORAGE_SAVE_ERROR);
        }
    }

    /**
     * 日志处理
     * @param array $data 数据
     * @return array
     **/

    private function handleUpdateLog(array $data)
    {
        $data_log = [];
        if (!empty($data)) {
            foreach ($data as $item_log) {
                //单个出库操作记录
                $item   = [];
                $before = json_decode($item_log['before_content'], true);
                $after  = json_decode($item_log['after_content'], true);
                if ($item_log['type'] == MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_ADD) {
                    //创建
                    $item[] = static::$t->_('material_wms_out_storage_add');
                } elseif ($item_log['type'] == MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_OUTBOUND) {
                    //出库
                    $item[] = static::$t->_('material_wms_out_storage_finish');
                } elseif ($item_log['type'] == MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_SPLIT) {
                    //拆分
                    $item[] = static::$t->_('material_wms_out_storage_split') . $before['wms_no'] . '->' . $after['wms_no'];
                } elseif (in_array($item_log['type'], [MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_SAVE, MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_APPOINT])) {
                    //编辑
                    $item[] = $this->mergeDisplay($before['use_staff_id'], $after['use_staff_id'], 'use_staff_id');
                    $item[] = $this->mergeDisplay($before['use_company_id'], $after['use_company_id'], 'use_company_id');
                    $item[] = $this->mergeDisplay($before['use_department_id'], $after['use_department_id'], 'use_department_id');
                    $item[] = $this->mergeDisplay($before['use_store_id'], $after['use_store_id'], 'use_store_id');
                    $item[] = $this->mergeDisplay($before['province_code'], $after['province_code'], 'province');
                    $item[] = $this->mergeDisplay($before['city_code'], $after['city_code'], 'city');
                    $item[] = $this->mergeDisplay($before['district_code'], $after['district_code'], 'district');
                    $item[] = $this->mergeDisplay($before['postal_code'], $after['postal_code'], 'postal');
                    $item[] = $this->mergeDisplay($before['address'], $after['address'], 'address');
                    $item[] = $this->mergeDisplay($before['pc_code'], $after['pc_code'], 'pc_code');
                    $item[] = $this->mergeDisplay($before['status'], $after['status'], 'status_name', 3);
                    $item[] = $this->mergeDisplay($before['mach_name'], $after['mach_name'], 'mach_name');
                    $item[] = $this->mergeDisplay($before['stock_name'], $after['stock_name'], 'stock_id');
                    $item[] = $this->mergeDisplay($before['products'], $after['products'], 'product', 2);
                }
                $rs['operation_record'] = array_values(array_filter($item));
                $rs['wms_no']           = $item_log['wms_no'];
                $rs['before_status']    = !empty($item_log['before_status']) ? static::$t->_('material_wms_out_storage_status.' . $item_log['before_status']) : '';
                $rs['after_status']     = static::$t->_('material_wms_out_storage_status.' . $item_log['after_status']);
                $rs['type']             = static::$t->_(MaterialWmsEnums::$wms_update_log_type[$item_log['type']]);
                $rs['staff_id']         = $item_log['staff_id'];
                $rs['staff_name']       = $item_log['staff_name'];
                $rs['created_at']       = $item_log['created_at'];
                $data_log[]             = $rs;
            }
            return $data_log;
        }
    }


    /**
     * 日志拼接
     * @param  $before  比较数据前
     * @param  $after   比较数据后
     * @param string $field 翻译key
     * @param int $type 1 比较字段 2 计算数量 3 状态比较
     * @return  string
     **/
    private function mergeDisplay($before, $after, string $field, int $type = 1)
    {
        $title = '';
        switch ($type) {
            case 1:
                $title = $before != $after ? static::$t->_('material_wms_out_' . $field) . ':' . $before . '->' . $after : '';
                break;
            case 2:
                $title = count($before) != count($after) ? static::$t->_('material_wms_out_use' . $field) . implode(',', array_diff(array_column($before, 'barcode'), array_column($after, 'barcode'))) : '';
                break;
            case 3:
                $title = $before != $after ? static::$t->_('material_wms_' . $field) . ':' . static::$t->_('material_wms_out_storage_status.' . $before) . '->' . static::$t->_('material_wms_out_storage_status.' . $after) : '';
                break;
            default:

        }
        return $title;
    }


    /**
     * 耗材领用出库-手动拆单
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function split(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            $main_model = MaterialWmsOutStorageModel::findFirst([
                'conditions' => "id = :id: and status = :status: and scm_no = '' and  is_submit_scm = :is_submit_scm: and is_deleted = :is_deleted:",
                'bind'       => ['id'            => $params['id'],
                                 'status'        => MaterialWmsEnums::STATUS_DRAFT,
                                 'is_submit_scm' => MaterialWmsEnums::IS_SUBMIT_SCM_DENY,
                                 'is_deleted'    => GlobalEnums::IS_NO_DELETED]
            ]);

            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $out_storage_product      = $main_model->getProducts();
            $out_storage_product_data = $out_storage_product->toArray();


            $this->validationSplit($params, $out_storage_product_data);
            $this->addSplitOutStorage($params, $main_model->toArray(), array_column($out_storage_product_data, null, 'id'), $user);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_split failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }


    /**
     * 手动拆单校验
     * @Date: 2/26/23 4:04 PM
     * @param array $params
     * @param array $product_data
     * @throws ValidationException
     **/

    public function validationSplit(array $params, array $product_data)
    {
        $split_num_sum = array_sum(array_column($params['products'], 'split_num'));
        if (empty($split_num_sum)) {
            throw new ValidationException(static::$t->_('material_wms_split_split_num_not_all_zero'), ErrCode::$VALIDATE_ERROR);
        }
        if ($split_num_sum == array_sum(array_column($product_data, 'this_time_num'))) {
            throw new ValidationException(static::$t->_('material_wms_split_this_time_num_error'), ErrCode::$VALIDATE_ERROR);
        }

        $params_product = array_column($params['products'], null, 'id');
        //判断传入的详情id必须在数据详情里面
        if (array_diff(array_column($params['products'], 'id'), array_column($product_data, 'id'))) {
            throw new ValidationException(static::$t->_('material_wms_split_products_id_error'), ErrCode::$VALIDATE_ERROR);
        }

        foreach ($product_data as $out_products) {
            if (!empty($params_product[$out_products['id']]) && $out_products['this_time_num'] < $params_product[$out_products['id']]['split_num']) {
                throw new ValidationException(static::$t->_('material_wms_split_split_num_gt_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
    }


    /**
     * 保存拆单
     * @param array $params 请求参数组
     * @param array $apply 耗材领用出库主数据
     * @param array $product 耗材领用出库明细数据
     * @param array $user 用户数据
     * @throws BusinessException
     */
    private function addSplitOutStorage(array $params, array $apply, array $product, array $user)
    {
        $wms_no   = BaseService::genSerialNo(MaterialWmsEnums::MATERIAL_WMS_OUT_STORAGE_NO_PREFIX, RedisKey::MATERIAL_WMS_OUT_STORAGE_COUNTER, 5);
        $now_time = date('Y-m-d H:i:s', time());
        //领用出库单信息组
        $order_data = [
            'wms_no'               => $wms_no,
            'apply_id'             => $apply['apply_id'],
            'apply_no'             => $apply['apply_no'],
            'scm_no'               => '',
            'staff_id'             => $apply['staff_id'],
            'staff_name'           => $apply['staff_name'],
            'company_id'           => $apply['company_id'],
            'company_name'         => $apply['company_name'],
            'node_department_id'   => $apply['node_department_id'],
            'node_department_name' => $apply['node_department_name'],
            'sys_store_id'         => $apply['sys_store_id'],
            'store_name'           => $apply['store_name'],
            'use_staff_id'         => $apply['use_staff_id'],
            'use_staff_name'       => $apply['use_staff_name'],
            'use_company_id'       => $apply['use_company_id'],
            'use_company_name'     => $apply['use_company_name'],
            'use_department_id'    => $apply['use_department_id'],
            'use_department_name'  => $apply['use_department_name'],
            'use_store_id'         => $apply['use_store_id'],
            'use_store_name'       => $apply['use_store_name'],
            'job_id'               => $apply['job_id'],
            'job_name'             => $apply['job_name'],
            'pc_code'              => $apply['pc_code'],
            'receive_store_id'     => $apply['receive_store_id'],
            'receive_name'         => $apply['receive_name'],
            'province_code'        => $apply['province_code'],
            'city_code'            => $apply['city_code'],
            'district_code'        => $apply['district_code'],
            'postal_code'          => $apply['postal_code'],
            'address'              => $apply['address'],
            'processor_id'         => $user['id'],
            'processor_name'       => $user['name'],
            'apply_date'           => $now_time,
            'remark'               => $apply['remark'],
            'status'               => MaterialWmsEnums::STATUS_DRAFT,
            'update_to_scm'        => $apply['update_to_scm'],
            'is_submit_scm'        => MaterialWmsEnums::IS_SUBMIT_SCM_DENY,
            'mach_code'            => $apply['mach_code'],
            'mach_name'            => $apply['mach_name'],
            'stock_id'             => $apply['stock_id'],
            'stock_name'           => $apply['stock_name'],
            'delivery_way'         => $apply['delivery_way'],
            'is_deleted'           => GlobalEnums::IS_NO_DELETED,
            'split_id'             => $apply['id'],
            'split_type'           => MaterialWmsEnums::MATERIAL_WMS_SPLIT_TYPE_HAND,
            'created_at'           => $now_time
        ];

        $wms_out_order = new MaterialWmsOutStorageModel();
        $bool          = $wms_out_order->i_create($order_data);
        if ($bool === false) {
            throw new BusinessException('耗材拆分添加失败: ' . json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($wms_out_order), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
        }

        //明细数据
        $product_data = [];
        foreach ($params['products'] as $value) {
            if (!empty($value['split_num'])) {
                $old_product    = $product[$value['id']];
                $apply_num = 0;
                if ($value['split_num'] == $old_product['this_time_num']) {
                    $apply_num = $old_product['apply_num'];
                }
                $product_data[] = [
                    'wms_id'                => $wms_out_order->id,
                    'wms_no'                => $wms_out_order->wms_no,
                    'barcode'               => $old_product['barcode'],
                    'name_zh'               => $old_product['name_zh'],
                    'name_en'               => $old_product['name_en'],
                    'name_local'            => $old_product['name_local'],
                    'unit_zh'               => $old_product['unit_zh'],
                    'unit_en'               => $old_product['unit_en'],
                    'model'                 => $old_product['model'],
                    'this_time_num'         => $value['split_num'],
                    'real_out_num'          => 0,
                    'apply_product_id'      => $old_product['id'],
                    'apply_num'             => $apply_num,
                    'use_val'               => $old_product['use_val'],
                    'category_id'           => $old_product['category_id'],
                    'category_name'         => $old_product['category_name'],
                    'category_code'         => $old_product['category_code'],
                    'finance_category_id'   => $old_product['finance_category_id'],
                    'finance_category_name' => $old_product['finance_category_name'],
                    'finance_category_code' => $old_product['finance_category_code'],
                    'is_deleted'            => GlobalEnums::IS_NO_DELETED,
                    'created_at'            => $now_time
                ];
            }
        }
        //出库单明细入库
        $now_product = new MaterialWmsOutStorageProductModel();
        if (!$now_product->batch_insert($product_data)) {
            throw new BusinessException('拆分出库单明细添加失败: ' . json_encode($product_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($now_product), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
        }
        //更新耗材被拆分的数据
        if (!empty($product)) {
            $product_ids         = array_values(array_column($product, 'id'));
            $product_update_data = MaterialWmsOutStorageProductModel::find(
                [
                    'conditions' => 'id in ({id:array}) and is_deleted = :is_deleted:',
                    'bind'       => ['id'         => $product_ids,
                                     'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ]);
            if (!empty($product_update_data)) {
                $split_products = array_column($params['products'], null, 'id');
                foreach ($product_update_data as $item) {
                    $item->this_time_num = bcsub($item->this_time_num, $split_products[$item->id]['split_num'], 0);
                    $item->updated_at    = $now_time;
                    $bool                = $item->save();
                    if ($bool === false) {
                        throw new BusinessException('耗材手动拆分 修改原拆分单失败: ' . json_encode($item->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($item), ErrCode::$MATERIAL_WMS_OUT_STORAGE_ADD_ERROR);
                    }
                }
            }
        }
        //添加日志
        $this->addMaterialWmsUpdateLog(MaterialWmsEnums::MATERIAL_WMS_UPDATE_LOG_TYPE_SPLIT, ['wms_no' => $wms_no, 'status' => MaterialWmsEnums::STATUS_DRAFT], ['wms_no' => $wms_no, 'status' => MaterialWmsEnums::STATUS_DRAFT], $user);
    }


    /**
     * 地区
     * @param array $params 请求参数组
     * @return  array
     **/
    public function getAreaList(array $params)
    {
        return OrderService::getInstance()::getAreaList($params);

    }


    /**
     * 获取成本中心
     * @param array $params 请求参数组
     * @return  array
     **/
    public function getPcCode(array $params)
    {
        return StoreRentingAddService::getInstance()->getPcCode($params['department_id'], $params['type']);

    }


    /**
     * 搜索使用人
     * @param array $params 请求参数组
     * @return  array
     **/
    public function searchStaff(array $params)
    {
        return StaffService::getInstance()->searchStaff($params);

    }

    /**
     * 获取仓库信息
     * @param array $params 请求参数组
     * @return  array
     **/
    public function getWarehouseList(array $params)
    {
        return StorageService::getInstance()->getWarehouseList($params['mach_code']);
    }


    /**
     * 根据耗材使用网点和 使用人查询 pccode
     * @param array $params 请求参数组
     * @return  array
     **/
    public function storeByPcCode(array $params)
    {
        $type  = BaseServiceStore::COST_TYPE_SYS_STORE;//成本中心网点
        $pc_id = $params['use_store_id'];
        if ($params['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG && !empty($params['use_staff_id'])) {
            $staff_info_obj = HrStaffInfoModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id:',
                'bind'       => [
                    'staff_info_id' => $params['use_staff_id'],
                ]
            ]);
            if (!empty($staff_info_obj) && !empty($staff_info_obj->node_department_id)) {
                $pc_id = $staff_info_obj->node_department_id;
            }
            $type = BaseServiceStore::COST_TYPE_HEAD_OFFICE;//成本中心总部
        }
        return StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
    }


    /**
     * 获取自提数据
     * @param object $apply 申请领用单数据
     * @return array
     */
    public function getSelfList($apply)
    {
        $data = MaterialWmsOutStorageModel::find([
            'conditions' => 'apply_id = :apply_id:',
            'bind'       => [
                'apply_id' => $apply->id
            ],
            'columns'    => 'mach_code, scm_no, status, stock_name, stock_id',
        ])->toArray();

        if (!empty($data)) {
            $stock_data = (new ScmService())->getStockList();
            foreach ($data as &$out_storage) {
                $stock_info                = $stock_data[$out_storage['mach_code']][$out_storage['stock_id']] ?? [];
                $out_storage['stock_name'] = $stock_info['name'] ?? '';
                $out_storage['address']    = $stock_info['province'] . $stock_info['city'] . $stock_info['district'] . $stock_info['address'];
                $out_storage['contact']    = $stock_info['contact'] ?? '';
                $out_storage['telephone']  = $stock_info['telephone'] ?? '';
                //出库单配送方式为自提的时候 当状态为草稿、待审核、待出库的时候，显示未出库
                $out_storage['status'] = in_array($out_storage['status'], [MaterialWmsEnums::STATUS_WAIT_APPROVE, MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT, MaterialWmsEnums::STATUS_DRAFT]) ? static::$t['material_wms_out_storage_status_null'] : static::$t[MaterialWmsEnums::$wms_out_storage_status[$out_storage['status']]];
            }
        }
        return $data;
    }

    /**
     * 按照仓库和货主查询可用库存 用于回调更新库存
     * @param array $out_storage 出库单数据
     * @param array $storage_products 出库单详情数据
     * @return array
     */
    public function getSearchAvailableInventory(array $out_storage, array $storage_products)
    {
        $scm             = new ScmService();
        $to_scm_barcode  = implode(',', array_values(array_unique(array_column($storage_products, 'barcode'))));
        $scm_result      = $scm->goodsStock($out_storage['mach_code'], $out_storage['stock_id'], $to_scm_barcode, '');
        $scm_result_data = $scm_result['data'];
        $this->logger->info('货主/仓库为 ' . $out_storage['mach_code'] . '/' . $out_storage['stock_id'] . '查询可用库存为 : ' . json_encode($scm_result_data['stock'], JSON_UNESCAPED_UNICODE));
        $available_inventory = [];
        if (!empty($scm_result_data) && !empty($scm_result_data['stock'])) {
            foreach ($scm_result_data['stock'] as $key => $inventory) {
                $available_inventory[$key] = $inventory['availableInventory'];
            }
        }
        return $available_inventory;
    }

    /**
     * 修改详情-保存加提交审核
     * @param array $params 参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function editAndAudit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $this->saveUpdateDb($params, $user, true);
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_out_storage_editAndAudit failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }

    /**
     * 领用出库-查询scm出库状态
     * @param array $params 请求入参
     * @return array
     */
    public function getScmStatus($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $wms_out_storage_info = $this->getMaterialWmsOutStorageInfo($params['id']);
            //出库状态=草稿 && 是否通过scm出库 = 是 && 是否提交过scm = 是
            if ($wms_out_storage_info->status == MaterialWmsEnums::STATUS_DRAFT && $wms_out_storage_info->update_to_scm == MaterialClassifyEnums::MATERIAL_CATEGORY_NO && $wms_out_storage_info->is_submit_scm == MaterialWmsEnums::IS_SUBMIT_SCM_CORRECT) {
                try {
                    $scm_out_storage_info = (new ScmService())->outboundOrderStatus($wms_out_storage_info->mach_code, '', $wms_out_storage_info->wms_no);
                    $wms_out_storage_info->scm_no = $scm_out_storage_info['outSn'];
                    if ($scm_out_storage_info['status'] == ScmEnums::OUT_BOUND_ORDER_STATUS_CANCEL) {
                        //已作废；作废
                        $wms_out_storage_info->status = MaterialWmsEnums::STATUS_CANCEL;
                    } elseif ($scm_out_storage_info['status'] == ScmEnums::OUT_BOUND_ORDER_STATUS_AUDIT) {
                        //待审核; 待审核
                        $wms_out_storage_info->status = MaterialWmsEnums::STATUS_WAIT_APPROVE;
                    } else if (in_array($scm_out_storage_info['status'], [
                        ScmEnums::OUT_BOUND_ORDER_STATUS_DRAFT,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_REJECT_AUDIT,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_FINISH,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_ALLOCATION,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_OUTBOUND_ING,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_PICKING,
                        ScmEnums::OUT_BOUND_ORDER_STATUS_PACK,
                    ])) {
                        //草稿、审核不过、已审核、库存分配失败、出库中、拣货完成、已打包；待出库
                        $wms_out_storage_info->status = MaterialWmsEnums::STATUS_APPROVED_WAIT_OUT;
                    } else if (in_array($scm_out_storage_info['status'],[ScmEnums::OUT_BOUND_ORDER_STATUS_DELIVERED, ScmEnums::OUT_BOUND_ORDER_STATUS_COMPLETED])) {
                        //已出库、已完成；已出库
                        $wms_out_storage_info->status = MaterialWmsEnums::STATUS_OUT;
                    }
                    $message = static::$t->_('scm_storage_out_status_sync_success');
                } catch (ValidationException $e) {
                    //产品要求scm那边查询异常的也要按照成功返回，scm的错误信息无需告知用户
                    $message = '';
                    //跟产品讨论这里需要将是否提交scm置为否，用于单据提交审核等操作
                    $wms_out_storage_info->is_submit_scm = MaterialWmsEnums::IS_SUBMIT_SCM_DENY;
                }
                $wms_out_storage_info->updated_at = date('Y-m-d H:i:s');
                $bool = $wms_out_storage_info->save();
                if ($bool === false) {
                    throw new BusinessException('耗材领用出库同步SCM出库状态、出库单号-更新失败: ' . json_encode($wms_out_storage_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($wms_out_storage_info), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if ($real_message) {
            $this->logger->error('material_wms_out_storage_getScmStatus failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $detail,
        ];
    }

    /**
     * 获取小红点 - 取数规则：获取耗材出库中状态为草稿的数据条数
     * @return mixed
     */
    public function getRedHotNum()
    {
        return MaterialWmsOutStorageModel::count([
            'conditions' => 'status = :status: and is_deleted = :is_deleted:',
            'bind' => ['status' => MaterialWmsEnums::STATUS_DRAFT, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
    }
    /**
     * 耗材申请-根据快递单号查看物料详情
     * @param string $express_sn 快递单号
     * @return array
     */
    public function getOutboundExpressProduct($express_sn)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('sau.id, sau.barcode, sau.model, sau.unit_en, product.product_num, sau.' . get_lang_field_name('name_', static::$language) . ' as name, name_en');
            $builder->from(['main' => MaterialWmsOutStorageBoxModel::class]);
            $builder->leftjoin(MaterialWmsOutStorageBoxProductModel::class, 'main.id = product.box_id', 'product');
            $builder->leftjoin(MaterialSauModel::class, 'sau.barcode = product.barcode', 'sau');
            $builder->where('main.express_sn = :express_sn:', ['express_sn' => $express_sn]);
            $data = $builder->getQuery()->execute()->toArray();
            if ($data) {
                $sau_ids = array_column($data, 'id');
                $attachment_list = MaterialAttachmentRepository::getInstance()->getAttachmentsListById($sau_ids, MaterialClassifyEnums::OSS_MATERIAL_TYPE_BAK, 0 );
                foreach ($data as &$item) {
                    $item['attachments'] = $attachment_list[$item['id']] [0] ?? [];
                }
            }

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_get_outbound_express_product_for_by failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 调取快递接口中获取运单号妥投日期
     * @param array $pno_list 运单号组
     * @return array|mixed
     * @throws GuzzleException
     */
    public function getExpressDate(array $pno_list)
    {
        $params = ['pnoList' => $pno_list];
        $api    = new RestClient('pms');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/oa/parcel/info', $params, ['Accept-Language' => static::$language]);
        if (isset($return['code']) && $return['code'] == ErrCode::$SUCCESS) {
            return $return['data'];
        }
        return [];
    }

}
