<?php
namespace App\Modules\Material\Services;
use App\Library\Enums\MaterialClassifyEnums;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Reimbursement\Models\RequestSapLog;

/**
 * sap系统 接口Service
 */
class SapsService extends BaseService
{
    private static $instance;//SapsService实例
    private static $apiPaths;//sap host
    private static $user;//sap 账户名
    private static $passWord;//sap 账户密码

    /**
     * SapsService constructor.
     */
    private function __construct()
    {
        self::$apiPaths = env('sap_interface_url', '');
        self::$user = env('sap_user_id', '_BYDHOST');
        self::$passWord = env('sap_pwd', 'Welcome1');
    }

    /**
     * @return SapsService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 发送请求
     * @param String $method 请求方法
     * @param Array $postData 请求参数
     * @return bool|string
     */
    public function httpRequestXml($method, $postData)
    {
        try {
            $curl      = curl_init();
            $header[]  = "Content-type: text/xml";
            $basic_key = self::$user . ":" . self::$passWord;
            $header[]  = "Authorization: Basic " . base64_encode($basic_key); //添加头，在name和pass处填写对应账号密码
            if (get_runtime_env() != 'dev') {
                curl_setopt($curl, CURLOPT_PROXY, env('proxy_ip')); //代理服务器地址
                curl_setopt($curl, CURLOPT_PROXYPORT, env('proxy_port')); //代理服务器端口
            }
            curl_setopt($curl, CURLOPT_URL, self::$apiPaths.$method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl,CURLOPT_CONNECTTIMEOUT, 180);
            curl_setopt($curl, CURLOPT_TIMEOUT, 300);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                $this->logger->warning('SAP-postRequest-failed:' . curl_error($curl));
            }
            curl_close($curl);// 根据头大小去获取头信息内容
            return  $responseText;
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . $message);
        }
    }

    /**
     * 将xml转为array
     * @param string $xml xml
     * @return mixed
     */
    public function xmlToArray($xml)
    {
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }

    /**
     * array转xml
     * @param array $arr
     * @return string
     */
    protected function arrayToXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";
            } else {
                $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
            }
        }
        $xml .= "</xml>";
        return $xml;
    }

    /**
     * 检测该标准型号在SAP是否存在
     * 查询物料接口:https://my602459.sapbyd.cn/sap/bc/srt/scs/?sap-vhost=my602459.sapbyd.cn
     * @param string $barcode barcode
     * @return array
     */
    public function checkIsInSap($barcode)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:MaterialByElementsQuery_sync>
        <!--Optional:-->
         <MaterialSelectionByElements>
          
            <!--根据物料编号查询:-->
            <SelectionByInternalID>
               <!--默认I:-->
               <InclusionExclusionCode>I</InclusionExclusionCode>
                 <!--默认1:-->
               <IntervalBoundaryTypeCode>1</IntervalBoundaryTypeCode>
               <!--物料编号-->
               <LowerBoundaryInternalID>'.$barcode.'</LowerBoundaryInternalID>
               <!--留空:-->
               <UpperBoundaryInternalID></UpperBoundaryInternalID>
            </SelectionByInternalID>
            
         </MaterialSelectionByElements>
      </glob:MaterialByElementsQuery_sync>      
   </soapenv:Body>
</soapenv:Envelope>';

        //发送请求
        $return_xml = $this->httpRequestXml($method = '/sap/querymaterialin', $post_xml);
        $this->logger->info('material-sync-sap-task-checkIsInSap:=====post_xml' . $post_xml . '=====return_xml======' . $return_xml);
        preg_match_all("/\<ProcessingConditions\>(.*?)\<\/ProcessingConditions\>/s", $return_xml, $res);
        if (isset($res[0][0]) && !empty($res[0][0])) {
            $res = $this->xmlToArray($res[0][0]);
            if (MaterialClassifyEnums::SAP_IS_EXISTED == $res['ReturnedQueryHitsNumberValue']) {
                //如果存在则会有主数据信息返回
                preg_match_all("/\<Material\>(.*?)\<\/Material\>/s", $return_xml, $material_return_arr);
                $return_arr = $this->xmlToArray($material_return_arr[0][0]);
                $sap_uuid = $return_arr['UUID'] ?? '';
            }
        }
        $request_sap_log = new RequestSapLog();
        $request_sap_log->save(['uuid' => $sap_uuid ?? '', 'order_code' => $barcode, 'type' => MaterialClassifyEnums::REQUEST_SAP_LOG_MATERIAL, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr ?? [];
    }

    /**
     * 获取SAP端公司ID
     * @return string
     */
    public function getSapCompanyId()
    {
        $company_id = MaterialClassifyEnums::FLASH_EXPRESS[get_country_code()];
        $company_info = SysDepartmentModel::findFirst([
            'columns' => 'sap_company_id',
            'conditions' => 'id = :id:',
            'bind' => ['id' => $company_id]
        ]);
        return !empty($company_info) ? $company_info->sap_company_id : '';
    }

    /**
     * 传输barcode到sap
     * @param string $action_code 01新增，02更新
     * @param array $material_sau barcode信息组
     * @return array|mixed
     */
    public function sendToSap($action_code, $material_sau)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:MaterialBundleMaintainRequest_sync_V1>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
          <!--物料 actionCode="01"为创建物料 、02更新物料-->
            <Material actionCode="'.$action_code.'" salesListCompleteTransmissionIndicator="true">
                <!--物料编号-->
                <InternalID>'.$material_sau['barcode'].'</InternalID>
                <!--物料描述:-->
                <Description>
                    <Description languageCode="EN">'.mb_substr(($material_sau['name_en'].$material_sau['model']), 0, 40).'</Description>
                </Description>  
                <!--产品类别:-->
                <ProductCategoryID>'.$material_sau['finance_category_code'].'</ProductCategoryID>
                <!--基本计量单位:-->
                <BaseMeasureUnitCode>EA</BaseMeasureUnitCode>
                
                <!--采购页签:-->
                <Purchasing actionCode="'.$action_code.'">
                    <!--状态 2-有效     状态3-冻结:-->
                    <LifeCycleStatusCode>2</LifeCycleStatusCode>
                    <!--采购计量单位:-->
                    <PurchasingMeasureUnitCode>EA</PurchasingMeasureUnitCode>
                </Purchasing>
                
                <!--物流页签 0或多个:-->
                <Logistics actionCode="'.$action_code.'">
                    <!--运营地点:-->
                    <SiteID>'.$material_sau['site_id'].'</SiteID>
                    <!--状态 1-准备中 2-有效:-->
                    <LifeCycleStatusCode>2</LifeCycleStatusCode>
                </Logistics>
                
                <!--计划页签:-->
                <Planning actionCode="01">
                    <!--0或多个:-->
                    <SupplyPlanning>
                        <!--计划区域:-->
                        <SupplyPlanningAreaID>'.$material_sau['site_id'].'</SupplyPlanningAreaID>
                        <!--状态 1-准备中 2-有效:-->
                        <LifeCycleStatusCode>2</LifeCycleStatusCode>
                        <!--采购类型： 2-外部采购-->
                        <ProcurementTypeCode>2</ProcurementTypeCode>
                    </SupplyPlanning>
                </Planning>
                
            </Material>
            
      </glob:MaterialBundleMaintainRequest_sync_V1>
   </soapenv:Body>
</soapenv:Envelope>';

        //发送请求
        $return_xml = $this->httpRequestXml($method = '/sap/managematerialin1', $post_xml);

        $this->logger->info('material-sau-send-sap-data:=====post_xml' . $post_xml . '=====return_xml======' . $return_xml);
        preg_match_all("/\<Material\>(.*?)\<\/Material\>/s", $return_xml, $res);

        $return_arr = [];
        if (isset($res[0][0]) && !empty($res[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($res[0][0]);
        }

        $request_sap_log = new RequestSapLog();
        $request_sap_log->save(['uuid' => $return_arr['UUID'] ?? '', 'order_code' => $material_sau['barcode'], 'type' => MaterialClassifyEnums::REQUEST_SAP_LOG_MATERIAL, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }

    /**
     * 传输barcode的评估数据到sap
     * @param string $action_code 01新增，02更新
     * @param array $material_sau barcode信息组
     * @param string $sap_uuid sap端的UUID
     * @return array|mixed
     */
    public function sendToSapValuation($action_code, $material_sau, $sap_uuid)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:MaterialValuationDataBundleMaintainRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
         <!--物料评估:-->
         <!--需要事先在管理物料接口维护该物料的相关公司和营业地点的简要信息:-->
         <MaterialValuationData actionCode="'.$action_code.'">
            <!--物料编号:-->
            <MaterialInternalID>'.$material_sau['barcode'].'</MaterialInternalID>
            <!--公司:-->
            <CompanyID>'.$material_sau['sap_company_id'].'</CompanyID>
            
            <!--编辑成本:-->
            <ValuationPrice actionCode="'.$action_code.'">
               <!--营业地点:-->
               <PermanentEstablishmentID>'.$material_sau['site_id'].'</PermanentEstablishmentID>
               <!--生效日期:-->
               <ValidityDatePeriod>
                  <!--开始日期:-->
                  <StartDate>2022-01-01</StartDate>
                  <!--结束日期:-->
                  <EndDate>9999-12-31</EndDate>
               </ValidityDatePeriod>
               <!--成本类型:-->
               <PriceTypeCode>1</PriceTypeCode>
               <!--账套:-->
               <SetOfBooksID>001</SetOfBooksID>
               <LocalCurrencyValuationPrice>
                  <!--成本:-->
                  <Amount currencyCode="'.$material_sau['currency_text'].'">0</Amount>
                  <!--成本单位:-->
                  <BaseQuantity unitCode="EA">1</BaseQuantity>
               </LocalCurrencyValuationPrice>
            </ValuationPrice>
            
            <AccountDeterminationSpecification actionCode="'.$action_code.'">
               <!--营业地点:-->
               <PermanentEstablishmentID>'.$material_sau['site_id'].'</PermanentEstablishmentID>
               <!--科目确定组:-->
               <AccountDeterminationMaterialValuationDataGroupCode>'.MaterialClassifyEnums::$finance_category_code_to_sap_code[$material_sau['finance_category_code']].'</AccountDeterminationMaterialValuationDataGroupCode>
            </AccountDeterminationSpecification>
            
            <InventoryValuationSpecification actionCode="'.$action_code.'">
               <!--营业地点:-->
               <PermanentEstablishmentID>'.$material_sau['site_id'].'</PermanentEstablishmentID>
               <!--评估等级类型 默认1:-->
               <ProductValuationLevelTypeCode>1</ProductValuationLevelTypeCode>
               <!--永续成本法: 2移动平均值-->
               <PerpetualInventoryValuationProcedureCode>2</PerpetualInventoryValuationProcedureCode>
            </InventoryValuationSpecification>
            <MaterialFinancialProcessInfo actionCode="'.$action_code.'">
               <!--营业地点:-->
               <PermanentEstablishmentID>'.$material_sau['site_id'].'</PermanentEstablishmentID>
               <!--状态 1-准备中 2-有效:-->
               <LifeCycleStatusCode>2</LifeCycleStatusCode>
            </MaterialFinancialProcessInfo>
         </MaterialValuationData>
         
      </glob:MaterialValuationDataBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        //发送请求
        $return_xml = $this->httpRequestXml($method = '/sap/managematerialvaluationdatain', $post_xml);

        $this->logger->info('material-sau-send-sap-valuation-data:=====post_xml' . $post_xml . '=====return_xml======' . $return_xml);
        preg_match_all("/\<MaterialValuationData\>(.*?)\<\/MaterialValuationData\>/s", $return_xml, $res);

        $return_arr = [];
        if (isset($res[0][0]) && !empty($res[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($res[0][0]);
        }
        $request_sap_log = new RequestSapLog();
        $request_sap_log->save(['uuid' => $sap_uuid, 'order_code' => $material_sau['barcode'], 'type' => MaterialClassifyEnums::REQUEST_SAP_LOG_MATERIAL, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }
}