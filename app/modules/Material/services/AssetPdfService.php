<?php
namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\HrStaffContractEnums;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Third\Services\FormPdfService;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;

/**
 * 资产协议-PDF-服务层
 * Class AssetPdfService
 * @package App\Modules\Material\Services
 */
class AssetPdfService extends BaseService
{
    private static $instance;
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return AssetPdfService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 马来 -《资产管理同意书》
     * @param array $params 参数组
     * @param array $user 员工信息组
     * @return array
     */
    public function exportAgreePdfForMy($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $pdf_url = '';

        try {
            $platform = $params['platform'] ?? MaterialAssetApplyEnums::SOURCE_TYPE_OA;//请求平台
            $self_asset_ids = $params['self_asset_ids'] ?? [];//员工名下自有资产ID组
            $asset_ids = $params['asset_ids'] ?? [];//待接收的资产ID组

            $staff_items_arr = (new HrStaffRepository())->getStaffItems([$user['id']], [StaffInfoEnums::HR_STAFF_ITEMS_BANK_NO_NAME]);//工资卡信息
            // 组状pdf数据组
            $pdf_data = [
                'staff_name' => $staff_items_arr[0]['value'] ?? '',//员工工资卡信息-持卡人
                'staff_info_id' => $user['id'],//工号
                'job_title' => $user['job_title'],//职位
                'node_department_name' => '',//部门
                'img_url' => '',//签名照片
                'date' => date('d/m/Y'),//日期(日-月-年)
                'items' => [],//资产清单
            ];
            if ($platform == MaterialAssetApplyEnums::SOURCE_TYPE_HCM) {
                // [HCM]要查询用户名下离职资产清单
                $resultData = LeaveAssetsService::getInstance()->getAssetsDetailHcm(['staff_id' => $user['id']], 'en');
                $staff_assets_list = $resultData['data']['assets_list'] ?? [];
            } else {
                // [OA/BY]查询用户名下特定状态的资产清单
                $staff_assets_list = AssetAccountService::getInstance()->getStaffAssets($user['id'], ['asset_ids' => $self_asset_ids, 'status' => explode(',', MaterialEnums::MY_ASSET_STATUS_VALIDATE)]);
                $asset_ids = !empty($asset_ids) ? array_values(array_unique($asset_ids)) : [];
            }
            // 限制总数不能大于1000[产品要求，【OA/HCM】不可超过设定最大值]
            if ($platform != MaterialAssetApplyEnums::SOURCE_TYPE_BY && (count($staff_assets_list) + count($asset_ids) > MaterialEnums::MATERIAL_EXPORT_PDF_MAX)) {
                throw new ValidationException(static::$t->_('export_pdf_exceed_maximum_limit'), ErrCode::$VALIDATE_ERROR);
            }
            // [OA/BY]需要查询本次要接收的资产
            $reception_assets_info = [];
            if (!empty($asset_ids) && $platform != MaterialAssetApplyEnums::SOURCE_TYPE_HCM) {
                $reception_assets_info = MaterialAssetsModel::find([
                    'columns' => 'id,name_zh,name_en,name_local,asset_code,sn_code,currency,purchase_price,net_value,receipted_at,model',
                    'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                    'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                ])->toArray();
            }
            // 全部资产
            $all_asset = array_merge($staff_assets_list, $reception_assets_info);
            if ($platform == MaterialAssetApplyEnums::SOURCE_TYPE_BY) {
                // [BY]产品要求超过最大值的只返回最大值数目资产即可
                $all_asset = array_slice($all_asset, 0, MaterialEnums::MATERIAL_EXPORT_PDF_MAX);
            }

            //取接收人为当前员工 && 状态为“已接收”的每笔资产下最新的一条转移记录完成日期
            $all_asset_ids = array_column($all_asset, 'id');
            $asset_transfer_log = AssetTransferService::getInstance()->getLastTransferLogByAssetIds($all_asset_ids, $user['id'], MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED);
            $asset_transfer_log = array_column($asset_transfer_log, null, 'asset_id');

            // [OA/HCM]；取资产转移记录里，接收人为当前员工的，最新一条的已完成的完成日期
            if ($platform != MaterialAssetApplyEnums::SOURCE_TYPE_BY) {
                $max_finished_at = empty($asset_transfer_log) ? '' : max(array_column($asset_transfer_log, 'finished_at'));
                $pdf_data['date'] = $max_finished_at ? date('d/m/Y', strtotime($max_finished_at)) : $user['hire_date'];
            }

            foreach ($all_asset as $k => $one_asset) {
                //价格
                $purchase_price = 0.00;
                //扣款金额
                $deduction_price = 0;
                //资产名称
                $asset_name = '';
                // [OA/BY]
                if (in_array($platform, [MaterialAssetApplyEnums::SOURCE_TYPE_OA, MaterialAssetApplyEnums::SOURCE_TYPE_BY])) {
                    $purchase_price = EnumsService::getInstance()->currencyAmountConversion($one_asset['currency'], $one_asset['purchase_price'], 2);
                    //净值
                    $deduction_price = EnumsService::getInstance()->currencyAmountConversion($one_asset['currency'], $one_asset['net_value'], 2);
                    $asset_name = $one_asset['name_en'];
                } else if ($platform == MaterialAssetApplyEnums::SOURCE_TYPE_HCM) {
                    // [HCM]已是本位币，无需换算
                    $purchase_price = $one_asset['purchase_price'];
                    // [HCM]取扣费金额
                    $deduction_price = $one_asset['deduct_amount'];
                    $asset_name = $one_asset['asset_name_en'];
                }

                $pdf_data['items'][] = [
                    'name' => $asset_name,//资产名称
                    'model' => $one_asset['model'],//规格型号
                    'sn_code' => $one_asset['sn_code'] ?? '',//sn码
                    'asset_code' => $one_asset['asset_code'] ?? '',//资产码
                    'nums' => 1,
                    'receipted_at' => date('d/m/Y', strtotime($asset_transfer_log[$one_asset['id']]['finished_at'] ?? $user['hire_date'])),//申请日期(最新领用日期为空则获取员工入职日期),//数量
                    'price' => 'RM ' . number_format($purchase_price, 2),//价格
                    'deduction_price' => 'RM ' . number_format($deduction_price, 2)//扣款金额
                ];
            }
            // 查询用户部门
            $node_department_info = [];
            if (!empty($user['node_department_id'])) {
                $node_department_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
                $pdf_data['node_department_name'] = $node_department_info['name'] ?? '';
            }
            // 查询签名照片
            $img_url = AssetTransferService::getInstance()->getSignUrl($user['id'], [HrStaffContractEnums::CONTRACT_STATUS_TO_BE_RENEWED, HrStaffContractEnums::CONTRACT_STATUS_IN_EFFECT, HrStaffContractEnums::CONTRACT_STATUS_EXPIRED]);
            $pdf_img_data[] = ['name' => 'staff_resign_sing', 'url' => $img_url];
            //判断是不是 lnt 新pdf 模板
            $isLnt = HrStaffRepository::isLntCompany($user['id']);
            if($isLnt){
                $pdf_url = $this->lntAssetPdf($user, $pdf_data, $pdf_img_data);
                return [
                    'code' => $code,
                    'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                    'data' => $pdf_url,
                ];
            }
            //18386按照员工所属部门所在公司的bu级公司，展示不同的模版文件头和尾
            $company_id = $node_department_info ? $node_department_info['company_id'] : 0;
            //F Commerce[15006]、Flash Fulfillment[20001]、Flash Pay[60001]、Flash Money[15050]
            $company_list = EnumsService::getInstance()->getSysDepartmentCompanyIds();
            $header_template_key = 'material_asset_agree_protocol_header_template';
            $footer_template_key = 'material_asset_agree_protocol_footer_template';
            if ($company_list && in_array($company_id, [$company_list['FCommerce'], $company_list['FlashFullfillment'], $company_list['FlashPay'], $company_list['FlashMoney']])) {
                $header_template_key .= '_' . $company_id;
                $footer_template_key .= '_' . $company_id;
            }
            $protocol_template = EnumsService::getInstance()->getMultiEnvByCode([$header_template_key, 'material_asset_agree_protocol_template_en', $footer_template_key]);
            $temp_url = $protocol_template['material_asset_agree_protocol_template_en'];
            
            $pdfOptions = [
                'headerTemplate' => $protocol_template[$header_template_key],
                'footerTemplate' => $protocol_template[$footer_template_key],
                'displayHeaderFooter' => true,
            ];
            $res = FormPdfService::getInstance()->generatePdf($temp_url, $pdf_data, $pdf_img_data, $pdfOptions);
            $pdf_url = $res['object_url'] ?? '';
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('export-agree-pdf-for-my-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $pdf_url,
        ];
    }


    public function renderPdfHeaderFooter($path, $data, $templateDir, $templateName){
        $view = new \Phalcon\Mvc\View();
        $view->setViewsDir($path);
        $view->setVars($data);

        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );

        $view->render($templateDir, $templateName);
        $view->finish();
        $content = $view->getContent();
        return $content;
    }

    public function lntAssetPdf($user, $pdf_data, $pdf_img_data)
    {
        $temp_url = EnumsService::getInstance()->getSettingEnvValue('lnt_asset_pdf_template');
        //获取 头尾 rpc hcm
        $rpc        = new ApiClient('hcm_rpc', '', 'get_company_config_info');
        $api_params = [['staff_info_id' => $user['id']]];
        $rpc->setParams($api_params);
        $configRes = $rpc->execute();
        if (empty($configRes) || $configRes['result']['code'] != ErrCode::$SUCCESS) {
            throw new ValidationException('get_company_config_info error');
        }
        $path               = BASE_PATH . '/public/';
        $assignData['logo'] = $configRes['result']['data']['company_logo_url_base64'];
        $assignData['card'] = $configRes['result']['data']['company_card_url_base64'];
        $header             = $this->renderPdfHeaderFooter($path, $assignData, 'template', 'lnt_asset_pdf_header');
        $footer             = $this->renderPdfHeaderFooter($path, $assignData, 'template', 'lnt_asset_pdf_footer');
        $pdfOptions         = [
            'headerTemplate'      => $header,
            'footerTemplate'      => $footer,
            'displayHeaderFooter' => true,
        ];
        $res                = FormPdfService::getInstance()->generatePdf($temp_url, $pdf_data, $pdf_img_data, $pdfOptions);
        return $res['object_url'] ?? '';
    }


    /**
     * 泰国 - 个人代理 -《资产管理同意书》
     * @param array $params 参数组
     * @param array $user 员工信息组
     * @return array
     */
    public function exportAgreePdfForThPersonalAgent($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $pdf_url = '';

        try {
            $asset_ids = $params['asset_ids'] ?? [];//待接收的资产ID组
            $platform = $params['platform'] ?? MaterialAssetApplyEnums::SOURCE_TYPE_OA;//请求平台
            $pdf_type = $params['type'];//协议类型
            //OA端资产台账导出，HCM导出 && 不可导出资产申请书
            if (empty($asset_ids) && $pdf_type == MaterialEnums::TRANSFER_EXPORT_PDF_TYPE_APPLY && in_array($platform, [MaterialAssetApplyEnums::SOURCE_TYPE_OA, MaterialAssetApplyEnums::SOURCE_TYPE_HCM])) {
                throw new ValidationException(static::$t->_('export_pdf_personal_agent_cannot_export_apply'), ErrCode::$VALIDATE_ERROR);
            }

            //入职日期
            $hire_date = date('d/m/Y', strtotime($user['hire_date']));
            $pdf_data = [
                'hire_date' => $hire_date,//入职日期
                'staff_name' => $user['name'],//姓名
                'identity' => $user['identity'], //身份证
                'date' => $hire_date,//导出协议日期
            ];

            // [OA/BY] - 转移操作需要查询本次待接收的资产
            if (!empty($asset_ids) && $platform != MaterialAssetApplyEnums::SOURCE_TYPE_HCM) {
                $asset_info = MaterialAssetsModel::find([
                    'columns' => 'asset_code,sn_code',
                    'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                    'bind' => ['ids' => $asset_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
                    'limit' => 1
                ])->toArray();
                $pdf_data['date'] =  date('d/m/Y');//转移的导出协议日期为当天
            } else {
                //其他界面的协议：默认只取使用中，调拨中，维修中，已报修第1个资产码
                $asset_info = AssetAccountService::getInstance()->getStaffAssets($user['id'], ['status' => [MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_ALLOT, MaterialEnums::ASSET_STATUS_REPAIRED, MaterialEnums::ASSET_STATUS_REPAIRING], 'limit' => 1]);
            }
            $pdf_data['asset_code'] = $asset_info[0]['asset_code'] ?? '';
            $pdf_data['sn_code'] = $asset_info[0]['sn_code'] ?? '';

            // 查询签名照片
            $img_url = AssetTransferService::getInstance()->getSignUrl($user['id'], [HrStaffContractEnums::CONTRACT_STATUS_TO_BE_RENEWED, HrStaffContractEnums::CONTRACT_STATUS_IN_EFFECT, HrStaffContractEnums::CONTRACT_STATUS_EXPIRED]);
            $pdf_img_data[] = ['name' => 'staff_resign_sing', 'url' => $img_url];

            $header_template_key = 'material_asset_agree_protocol_personal_agent_header_template';
            $footer_template_key = 'material_asset_agree_protocol_personal_agent_footer_template';
            $content_template_key = 'material_asset_agree_protocol_personal_agent_template_th';
            $protocol_template = EnumsService::getInstance()->getMultiEnvByCode([$header_template_key, $content_template_key, $footer_template_key]);

            $pdfOptions = [
                'headerTemplate' => $protocol_template[$header_template_key],
                'footerTemplate' => $protocol_template[$footer_template_key],
                'displayHeaderFooter' => true,
                'format' => 'A4',
            ];
            $res = FormPdfService::getInstance()->generatePdf($protocol_template[$content_template_key], $pdf_data, $pdf_img_data, $pdfOptions);
            $pdf_url = $res['object_url'] ?? '';
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('export-agree-pdf-for-th-personalAgent-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $pdf_url,
        ];
    }
}
