<?php

namespace App\Modules\Material\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\Validation;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MaterialLeaveAssetsDetailModel;
use App\Models\oa\MaterialLeaveAssetsModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\User\Services\StaffService;

class LeaveAssetsManagerService extends BaseService
{
    //列表-非必填
    public static $not_must_list = [
        'staff_info_id',
        'staff_state',
        'last_work_date_start',
        'last_work_date_end',
        'leave_date_start',
        'leave_date_end',
        'node_department_id',
        'sys_store_id',
        'job_title',
        'manager_status'
    ];
    //by端列表-非必填
    public static $not_must_list_by = [
        'search_staff_id',
        'search_leave_date',
        'search_last_work_date',
    ];
    //by端列表
    public static $validate_list_by = [
        'pagesize' => 'Required|IntGe:0', //查询数据量
        //'new_data_last_id' => 'Required|IntGe:0', //上次查询的末尾id, 没有就是0
        'oa_offset' => 'Required|IntGe:0', //上次偏移量
        'is_process' => 'Required|IntIn:0,1', //0-待处理 1-已处理
        'search_staff_id' => 'IntGe:1', //工号
        'search_leave_date' => 'Date',  //离职日期
        'search_last_work_date' => 'Date', //最后工作日
    ];
    //列表
    public static $validate_list = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'list_type' => 'Required|IntIn:' . MaterialEnums::LIST_TYPE_PENDING . ',' . MaterialEnums::LIST_TYPE_DONE, //处理状态
        'manager_status' => 'IntIn:' . MaterialEnums::MANAGER_STATUS_DEAL . ',' . MaterialEnums::MANAGER_STATUS_INVALID, //状态-只能选已处理,已作废
        'staff_info_id' => 'IntGt:0',//工号
        'staff_state' => 'Arr',//在职状态
        'staff_state[*]' => 'IntIn:' . StaffInfoEnums::STAFF_STATE_VALIDATE,//在职状态
        'last_work_date_start' => 'Date',//最后工作日-起始
        'last_work_date_end' => 'Date',//最后工作日-截止
        'leave_date_start' => 'Date',//待离职/离职日期-起始
        'leave_date_end' => 'Date',//待离职/离职日期-截止
        'node_department_id' => 'IntGt:0',//部门ID
        'sys_store_id' => 'Arr|ArrLenGeLe:1,20',//网点id
        'store_id[*]' => 'Str',
        'job_title' => 'Arr|ArrLenGeLe:1,20',//职位id
        'job_title[*]' => 'Int'
    ];
    //编辑页-详情
    public static $validate_detail = [
        'id' => 'Required|IntGt:0',
    ];
    //编辑页-列表
    public static $validate_edit_list = [
        'id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
    ];
    //编辑页-暂存-非必填
    public static $no_must_edit_save_detail = [
        'asset_code',
        'sn_code',
        'old_asset_code',
        'model'
    ];
    //编辑页-暂存
    public static $validate_edit_save_main = [
        'id' => 'Required|IntGt:0',
        'is_submit' => 'Required|IntIn:0,1',
        'data' => 'Arr|ArrLenGeLe:0,200',
    ];
    //编辑页-暂存
    public static $validate_edit_save_detail = [
        'detail_id' => 'Required|IntGt:0',
        'asset_state' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'manager_remark' => 'StrLenGeLe:1,100',
        'barcode' => 'StrLenGeLe:1,30',
        'asset_code' => 'StrLenGeLe:1,100',//自己添加的资产可以编辑
        'sn_code' => 'StrLenGeLe:1,50',//自己添加的资产可以编辑
        'model' => 'StrLenGeLe:1,100',//自己添加的资产可以编辑
        'images' => 'Arr|ArrLenGeLe:0,5',
        'images[*].bucket_name' => 'Required',
        'images[*].object_key' => 'Required',
        'images[*].file_name' => 'Required',
    ];
    //编辑页-新增-非必填
    public static $no_must_edit_add = [
        'asset_code',
        'sn_code',
        'old_asset_code',
        'model'
    ];
    //编辑页-新增
    public static $validate_edit_add = [
        'id' => 'Required|IntGt:0',
        'barcode' => 'StrLenGeLe:1,30',
        'use' => 'IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
        'asset_code' => 'StrLenGeLe:1,100',
        'sn_code' => 'StrLenGeLe:1,50',
        'model' => 'StrLenGeLe:1,100',
        'asset_state' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'manager_remark' => 'StrLenGeLe:1,100',
        'images' => 'Arr|ArrLenGeLe:0,5',
        'images[*].bucket_name' => 'Required',
        'images[*].object_key' => 'Required',
        'images[*].file_name' => 'Required',
    ];
    //编辑页-删除
    public static $validate_delete = [
        'detail_id' => 'Required|IntGt:0',
    ];

    //批量确认
    public static $validate_batch_confirm = [
        'ids' => 'Required|ArrLenGeLe:1,100',
        'ids[*]' => 'Required|IntGt:0'
    ];

    //批量编辑
    public static $validate_batch_save = [
        'id' => 'Required|IntGt:0',
        'detail_ids' => 'Required|Arr',
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
        'data' => 'Required|Arr|ArrLenGeLe:1,500',
        'data[*].asset_state' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'data[*].manager_remark' => 'Required|StrLenGeLe:1,100',
        'data[*].barcode' => 'Required|StrLenGeLe:1,30',
        'data[*].images' => 'Arr|ArrLenGeLe:0,5',
        'data[*].images[*].bucket_name' => 'Required',
        'data[*].images[*].object_key' => 'Required',
        'data[*].images[*].file_name' => 'Required',
    ];
    //批量编辑前获取barcode维度的统计
    public static $validate_batch_save_barcode_count = [
        'id' => 'Required|IntGt:0',
        'detail_ids' => 'Required|Arr',
        'use' => 'Required|IntIn:' . MaterialEnums::USE_VALIDATE, //使用方向
    ];
    //by端上级处理-详情
    public static $validate_detail_by = [
        'id' => 'Required|IntGt:0',
    ];
    //by详情页-新增-非必填
    public static $not_must_edit_add_by = [
        'images',
        'manager_remark',
    ];
    //by端编辑页-新增
    public static $validate_edit_add_by = [
        'id' => 'Required|IntGt:0',
        'barcode' => 'StrLenGeLe:1,30',
        'manager_remark' => 'StrLenGeLe:0,100',
        'images' => 'Arr|ArrLenGeLe:0,5',
        'data' => 'Required|Arr|ArrLenGeLe:1,10',
        'data[*].asset_state' => 'Required|IntIn:' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'data[*].asset_code' => 'StrLenGeLe:0,100',
        'data[*].sn_code' => 'StrLenGeLe:0,50',
        'data[*].manager_remark' => 'StrLenGeLe:0,100',
        'data[*].images' => 'Arr|ArrLenGeLe:0,10',
        'data[*].images[*].bucket_name' => 'Required',
        'data[*].images[*].object_key' => 'Required',
        'data[*].images[*].file_name' => 'Required',
    ];
    //by端编辑页-编辑-大保存
    public static $validate_edit_save_basic_by = [
        //用来确定哪个卡片 (同一个卡片里的数组)
        'id' => 'Required|IntGt:0',
        'is_submit' => 'Required|IntIn:0,1',
    ];
    //by端编辑页-编辑-小保存(暂存)
    public static $validate_edit_tmp_save_by = [
        'all_data' => 'Required|Arr|ArrLenGeLe:0,100',
        'all_data[*].manager_remark' => 'StrLenGeLe:0,100',
        'all_data[*].images' => 'Arr|ArrLenGeLe:0,10',
        'all_data[*].data' => 'Arr|ArrLenGeLe:0,100',
        'all_data[*].data[*].detail_id' => 'IntGt:0',//自己添加的资产可以编辑
        'all_data[*].data[*].asset_code' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'all_data[*].data[*].sn_code' => 'StrLenGeLe:0,50',//自己添加的资产可以编辑
        'all_data[*].data[*].asset_state' => 'IntIn:0,' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'all_data[*].data[*].manager_remark' => 'StrLenGeLe:0,100',//主管备注,损坏的必须填,没损坏的不填
        'all_data[*].data[*].images' => 'Arr|ArrLenGeLe:0,10',//主管备注,损坏的必须填,没损坏的不填
        'all_data[*].data[*].images[*].object_url' => 'Required|StrLenGeLe:0,500',//主管备注,损坏的必须填,没损坏的不填
    ];
    //by端编辑页-编辑-大保存
    public static $validate_edit_save_by = [
        'all_data' => 'Required|Arr|ArrLenGeLe:0,100',
        'all_data[*].manager_remark' => 'StrLenGeLe:0,100',
        'all_data[*].images' => 'Arr|ArrLenGeLe:0,10',
        'all_data[*].data' => 'Arr|ArrLenGeLe:0,100',
        'all_data[*].data[*].detail_id' => 'Required|IntGt:0',//自己添加的资产可以编辑
        'all_data[*].data[*].asset_code' => 'StrLenGeLe:0,100',//自己添加的资产可以编辑
        'all_data[*].data[*].sn_code' => 'StrLenGeLe:0,50',//自己添加的资产可以编辑
        'all_data[*].data[*].asset_state' => 'Required|Required|IntIn:' . MaterialEnums::LEAVE_ASSET_STATE_VALIDATE, //资产状况
        'all_data[*].data[*].manager_remark' => 'StrLenGeLe:0,100',//主管备注,损坏的必须填,没损坏的不填
        'all_data[*].data[*].images' => 'Arr|ArrLenGeLe:0,10',//主管备注,损坏的必须填,没损坏的不填
        'all_data[*].data[*].images[*].object_url' => 'Required|StrLenGeLe:1,500',//主管备注,损坏的必须填,没损坏的不填
    ];
    //by端编辑页-删除
    public static $validate_delete_by = [
        'batch_detail_ids' => 'Required|Arr|ArrLenGeLe:1,100',//by端最多操作100条
        'batch_detail_ids[*]' => 'Required|IntGt:0',
    ];
    //by端打卡限制-验证当前员工名下是否有待处理资产
    public static $validate_has_leave_assets = [
        'staff_id' => 'Required|IntGt:0',
        'clock_in_date' => 'Required|Date',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LeaveAssetsManagerService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     * @date 2023/3/15
     */
    public function getDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                //员工在职状态
                'staff_state' => StaffInfoEnums::$staff_state,
                //资产状况
                'leave_asset_personal_state_item' => MaterialEnums::$leave_asset_personal_state_list,
                'leave_asset_public_state_item' => MaterialEnums::$leave_asset_public_state_list,
                //列表状态
                'manager_status_item' => [
                    MaterialEnums::MANAGER_STATUS_DEAL => MaterialEnums::$manager_status_list[MaterialEnums::MANAGER_STATUS_DEAL],
                    MaterialEnums::MANAGER_STATUS_INVALID => MaterialEnums::$manager_status_list[MaterialEnums::MANAGER_STATUS_INVALID],
                ]
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $tmp = [];
                    $tmp['value'] = $k;
                    //这两个需要临时翻译
                    if (in_array($key, ['leave_asset_personal_state_item', 'leave_asset_public_state_item'])) {
                        $tmp['label'] = $this->translateTemp($v);
                    } else {
                        $tmp['label'] = static::$t->_($v);
                    }
                    $data[$key][] = $tmp;
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取离职资产-主管处理异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理列表-通过oa_offset查询-by端使用
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/18
     */
    public function getAssetsManagerList($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'oa_offset' => 0,
            'is_over' => MaterialEnums::LEAVE_ASSET_LIST_IS_OVER_NO
        ];
        try {
            $params = BaseService::handleParams($params, self::$not_must_list_by);
            Validation::validate($params, self::$validate_list_by);
            $page_size = $params['pagesize'];
            $offset = $params['oa_offset'];
            $params['list_type'] = MaterialEnums::LIST_TYPE_PENDING;
            if ($params['is_process'] == 1) {
                $params['list_type'] = MaterialEnums::LIST_TYPE_DONE;
            }
            //查询总数
            $count = $this->getListCount($params, $user);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.staff_info_id, main.staff_name, staff.last_work_date, staff.leave_date, main.manager_status, main.manager_updated_at, 
                main.manager_updated_id, main.personal_assets_number, main.public_assets_number, staff.wait_leave_state';
                $builder->columns($columns);
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                //组合搜索条件
                $builder = $this->getCondition($builder, $params, $user, 'main.');
                $builder->limit($page_size, $offset);
                $builder->orderby('staff.last_work_date desc,staff.leave_date desc,main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, true);
            }
            $data['items'] = $items ?? [];
            $data['oa_offset'] = $offset + $page_size;
            //全部数据的最大偏移量
            $max_offset = $count - $page_size;
            if ($offset >= $max_offset) {
                $data['is_over'] = MaterialEnums::LEAVE_ASSET_LIST_IS_OVER_YES;
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leave-asset-manager-by-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理列表
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getListCount($params, $user);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.staff_info_id, main.staff_name, main.staff_name_en, staff.state as staff_state, staff.last_work_date, staff.leave_date, staff.wait_leave_state, main.job_name, main.job_title,
                 main.node_department_name, main.node_department_id, main.sys_store_id, main.sys_store_name, main.working_country, main.personal_assets_number, 
                 main.public_assets_number, main.no, main.manager_updated_at, main.manager_updated_id, main.manager_updated_name, main.manager_status';
                $builder->columns($columns);
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                //组合搜索条件
                $builder = $this->getCondition($builder, $params, $user, 'main.');
                $builder->limit($page_size, $offset);
                if ($params['list_type'] == MaterialEnums::LIST_TYPE_PENDING) {
                    $builder->orderby('staff.last_work_date asc, staff.leave_date asc, main.id desc');
                } elseif ($params['list_type'] == MaterialEnums::LIST_TYPE_DONE) {
                    $builder->orderby('main.manager_updated_at desc, main.id desc');
                }
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-leave-asset-manager-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理列表-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getListCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialLeaveAssetsModel::class]);
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
        $builder->columns('count(main.id) as count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $user, 'main.');
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 主管处理列表-条件拼接
     * @param $builder
     * @param $condition
     * @param $user
     * @return mixed
     * @date 2023/3/2
     */
    public function getCondition($builder, $condition, $user, $prefix = '')
    {
        //oa端的搜索条件
        $list_type = !empty($condition['list_type']) ? $condition['list_type'] : MaterialEnums::LIST_TYPE_PENDING;//列表类型 待处理/已处理
        $staff_info_id = !empty($condition['staff_info_id']) ? $condition['staff_info_id'] : 0;//员工id
        $staff_state = !empty($condition['staff_state']) ? $condition['staff_state'] : [];//在职状态
        $manager_status = !empty($condition['manager_status']) ? $condition['manager_status'] : 0;//处理状态
        $last_work_date_start = !empty($condition['last_work_date_start']) ? $condition['last_work_date_start'] : '';//最后工作日-开始
        $last_work_date_end = !empty($condition['last_work_date_end']) ? $condition['last_work_date_end'] : '';//最后工作日-结束
        $leave_date_start = !empty($condition['leave_date_start']) ? $condition['leave_date_start'] : '';//待离职/离职日期-开始
        $leave_date_end = !empty($condition['leave_date_end']) ? $condition['leave_date_end'] : '';//待离职/离职日期-结束
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//部门id
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : [];//网点id
        $job_title = !empty($condition['job_title']) ? $condition['job_title'] : [];//网点id
        //by端的搜索条件
        $search_staff_id = !empty($condition['search_staff_id']) ? $condition['search_staff_id'] : 0;//by端员工id
        $search_leave_date = !empty($condition['search_leave_date']) ? $condition['search_leave_date'] : '';//离职日期
        $search_last_work_date = !empty($condition['search_last_work_date']) ? $condition['search_last_work_date'] : '';//最后工作日

        //固定条件
        $builder->andWhere($prefix . 'manager_staff_id = :manager_staff_id:', ['manager_staff_id' => $user['id']]);
        //处理状态
        switch ($list_type) {
            case MaterialEnums::LIST_TYPE_PENDING:
                $builder->andWhere($prefix . 'manager_status = :manager_status:', ['manager_status' => MaterialEnums::MANAGER_STATUS_TODO]);
                break;
            case MaterialEnums::LIST_TYPE_DONE:
                if (!empty($manager_status)) {
                    $builder->andWhere($prefix . 'manager_status = :manager_status:', ['manager_status' => $manager_status]);
                } else {
                    $builder->inWhere($prefix . 'manager_status', [MaterialEnums::MANAGER_STATUS_DEAL, MaterialEnums::MANAGER_STATUS_INVALID]);

                }
                break;
        }
        if (!empty($staff_info_id)) {
            //员工id
            $builder->andWhere($prefix . 'staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        //在职状态
        if (!empty($staff_state)) {
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $staff_state)) {
                //搜索包含待离职
                if (count($staff_state) == 1) {
                    //只搜索待离职的
                    $builder->andWhere('staff.state = :state: and staff.wait_leave_state = :wait_leave_state:', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
                } else {
                    //掺杂待离职混合搜索
                    $builder->andWhere('(staff.state = :state: and staff.wait_leave_state = :wait_leave_state:) OR (staff.state in ({states:array}) AND staff.wait_leave_state = :wait_leave_state_no:)', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES, 'states' => $staff_state, 'wait_leave_state_no' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
                }
            } else {
                $builder->inWhere('staff.state', $staff_state);
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state:', ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);
            }
        }
        if (!empty($last_work_date_start)) {
            //最后工作日-开始
            $builder->andWhere('staff.last_work_date >= :last_work_date_start:', ['last_work_date_start' => $last_work_date_start]);
        }
        if (!empty($last_work_date_end)) {
            //最后工作日-结束
            $builder->andWhere('staff.last_work_date <= :last_work_date_end:', ['last_work_date_end' => $last_work_date_end]);
        }
        if (!empty($leave_date_start)) {
            //待离职/离职日期-开始
            $builder->andWhere('staff.leave_date >= :leave_date_start:', ['leave_date_start' => $leave_date_start]);
        }
        if (!empty($leave_date_end)) {
            //待离职/离职日期-结束
            $builder->andWhere('staff.leave_date <= :leave_date_end:', ['leave_date_end' => $leave_date_end]);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere($prefix . 'node_department_id', $department_ids);
        }
        if (!empty($sys_store_id)) {
            //网点
            $sys_store_id = array_values(array_unique($sys_store_id));
            $builder->inWhere($prefix . 'sys_store_id', $sys_store_id);
        }
        if (!empty($job_title)) {
            //职位
            $job_title = array_values(array_unique($job_title));
            $builder->inWhere($prefix . 'job_title', $job_title);
        }
        //by端搜索
        if (!empty($search_staff_id)) {
            //员工id
            $builder->andWhere($prefix . 'staff_info_id = :staff_info_id:', ['staff_info_id' => $search_staff_id]);
        }
        if (!empty($search_leave_date)) {
            //待离职/离职日期
            $builder->andWhere('staff.leave_date <= :search_leave_date:', ['search_leave_date' => $search_leave_date]);
        }
        if (!empty($search_last_work_date)) {
            //待离职/离职日期
            $builder->andWhere('staff.last_work_date <= :search_last_work_date:', ['search_last_work_date' => $search_last_work_date]);
        }

        return $builder;
    }

    /**
     * 主管处理列表-列表数据处理
     * @param $items
     * @param bool $is_by
     * @return array
     * @date 2023/3/2
     */
    private function handleListItems($items, $is_by = false)
    {
        if (empty($items)) {
            return [];
        }
        if ($is_by) {
            //查询员工联系方式
            $staff_ids = array_column($items, 'staff_info_id');
            $staff_ids = array_values(array_unique($staff_ids));
            $staff_data_kv = [];
            if (!empty($staff_ids)) {
                $staff_data = HrStaffInfoModel::find([
                    'columns' => 'staff_info_id, mobile',
                    'conditions' => 'staff_info_id in ({ids:array})',
                    'bind' => [
                        'ids' => $staff_ids,
                    ]
                ])->toArray();
                $staff_data_kv = array_column($staff_data, 'mobile', 'staff_info_id');
            }
            foreach ($items as &$item) {
                //工号 由于新老数据兼容,所以保持和老数据字段名称一致
                $item['staff_id'] = $item['staff_info_id'];
                //主管处理状态
                $item['manager_status_text'] = isset(MaterialEnums::$manager_status_list[$item['manager_status']]) ? $this->translateTemp(MaterialEnums::$manager_status_list[$item['manager_status']]) : '';
                //联系方式
                $item['mobile'] = $staff_data_kv[$item['staff_info_id']] ?? '';
                //主管是否处理过
                $item['is_has_process'] = $item['manager_updated_id'] > 0 ? MaterialEnums::IS_HAS_PROCESS_YES : MaterialEnums::IS_HAS_PROCESS_NO;
                //主管处理时间
                $item['operate_date'] = !empty($item['manager_updated_at']) ? $item['manager_updated_at'] : '';
                //是否新数据,本期全是新数据v15994
                $item['is_new'] = MaterialEnums::LEAVE_ASSET_IS_NEW_YES;
                //待处理资产数量
                $item['all_assets_number'] = $item['personal_assets_number'] + $item['public_assets_number'];
                unset($item['staff_info_id']);
                unset($item['manager_updated_id']);
                unset($item['manager_updated_at']);
                unset($item['personal_assets_number']);
                unset($item['public_assets_number']);
            }
        } else {
            foreach ($items as &$item) {
                //国家
                $item['working_country_text'] = isset(StaffInfoEnums::$working_country[$item['working_country']]) ? static::$t[StaffInfoEnums::$working_country[$item['working_country']]] : '';
                //主管处理状态
                $item['manager_status_text'] = isset(MaterialEnums::$manager_status_list[$item['manager_status']]) ? static::$t[MaterialEnums::$manager_status_list[$item['manager_status']]] : '';
                //员工离职状态
                $staff_state = ($item['staff_state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['staff_state'];
                $item['staff_state'] = $staff_state;
                $item['staff_state_text'] = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '';
                //更新人名称
                $item['manager_updated_name'] = !empty($item['manager_updated_id']) ? $item['manager_updated_name'] . '(' . $item['manager_updated_id'] . ')' : $item['manager_updated_name'];
            }
        }

        return $items;
    }

    /**
     * 主管处理-编辑页详情
     * @param $params
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    public function getDetail($params, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询离职资产
            $data_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //查询员工信息
            $staff_info_data = [];
            if (!empty($data_info->staff_info_id)) {
                $staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $data_info->staff_info_id]
                ]);
                $staff_info_data = $staff_info ? $staff_info->toArray() : [];
            }

            if ($data_info) {
                //查员工离职日期/最后工作日(by员工表没有最后工作日,所以用mini表)
                $mini_staff_info = MiniHrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $data_info->staff_info_id]
                ]);
                $data = [
                    'id' => $data_info->id,
                    'staff_info_id' => $data_info->staff_info_id,
                    'staff_name' => $data_info->staff_name,
                    'staff_name_en' => $data_info->staff_name_en,
                    'staff_state' => $data_info->staff_state,
                    'staff_state_text' => isset(StaffInfoEnums::$staff_state[$data_info->staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$data_info->staff_state]] : '',
                    'last_work_date' => $mini_staff_info->last_work_date ?? '',
                    'leave_date' => $mini_staff_info->leave_date ?? '',
                    'job_name' => $data_info->job_name,
                    'job_title' => $data_info->job_title,
                    'node_department_name' => $data_info->node_department_name,
                    'node_department_id' => $data_info->node_department_id,
                    'sys_store_id' => $data_info->sys_store_id,
                    'sys_store_name' => $data_info->sys_store_name,
                    'working_country_text' => isset(StaffInfoEnums::$working_country[$data_info->working_country]) ? static::$t[StaffInfoEnums::$working_country[$data_info->working_country]] : '',
                    'personal_assets_number' => $data_info->personal_assets_number,
                    'public_assets_number' => $data_info->public_assets_number,
                    'no' => $data_info->no,
                    'manager_updated_at' => $data_info->manager_updated_at,
                    'mobile' => $staff_info_data['mobile'] ?? '',
                    'personal_email' => $staff_info_data['personal_email'] ?? '',
                ];
                //查询资产统计信息
                $data['personal_assets'] = $this->getBarcodeCount($params['id'], $locale, MaterialEnums::USE_PERSONAL, [], true);
                $data['public_assets'] = $this->getBarcodeCount($params['id'], $locale, MaterialEnums::USE_PUBLIC, [], true);
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-detail-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理-编辑页-详情处理
     * @param $items
     * @return array
     * @date 2023/3/2
     */
    private function handleBarcodeCountItems($items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['working_country_text'] = isset(StaffInfoEnums::$working_country[$item['working_country']]) ? static::$t[StaffInfoEnums::$working_country[$item['working_country']]] : '';
            $item['manager_status_text'] = isset(MaterialEnums::$manager_status_list[$item['manager_status']]) ? static::$t[MaterialEnums::$manager_status_list[$item['manager_status']]] : '';
        }
        return $items;
    }

    /**
     * 主管处理-编辑页-列表
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     * @date 2023/3/2
     */
    public function getEditAssetsList($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => 0,
                'per_page' => 0,
                'total_count' => 0,
            ],
        ];
        try {
            $page_size = empty($params['pageSize']) ? MaterialEnums::PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? MaterialEnums::PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page'] = $page_size;
            //查询总数
            $count = $this->getEditAssetsCount($params, $user);
            //查询列表
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'id, barcode, asset_name_zh, asset_name_en, asset_name_local, asset_code, sn_code, old_asset_code, model, asset_status, asset_state, manager_remark, data_tag';
                $builder->columns($columns);
                $builder->from(MaterialLeaveAssetsDetailModel::class);
                //组合搜索条件
                $builder = $this->getEditAssetsCondition($builder, $params, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleEditAssetsListItems($items, $locale);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管处理-编辑页-列表-总数
     * @param $condition
     * @param $user
     * @return int
     * @date 2023/3/2
     */
    public function getEditAssetsCount($condition, $user)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(MaterialLeaveAssetsDetailModel::class);
        $builder->columns('count(id) as count');
        //组合搜索条件
        $builder = $this->getEditAssetsCondition($builder, $condition, $user);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 主管处理-编辑页-列表-条件拼接
     * @param $builder
     * @param $condition
     * @param $user
     * @return mixed
     * @date 2023/3/3
     */
    public function getEditAssetsCondition($builder, $condition, $user)
    {
        //固定条件
        $builder->andWhere('leave_assets_id = :leave_assets_id:', ['leave_assets_id' => $condition['id']]);
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('use = :use:', ['use' => $condition['use']]);
        $builder->andWhere('manager_tag = :manager_tag:', ['manager_tag' => MaterialEnums::MANAGER_TAG_YES]);
        return $builder;
    }

    /**
     * 主管处理-编辑页-列表-处理列表数据
     * @param $items
     * @param $locale
     * @return array
     * @date 2023/3/2
     */
    private function handleEditAssetsListItems($items, $locale)
    {
        if (empty($items)) {
            return [];
        }
        //获取图片
        $attachment = (new MaterialAttachmentModel())->getColumnArrUrl($items, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL);
        $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
        $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';

        foreach ($items as &$item) {
            $item['asset_status_text'] = isset(MaterialEnums::$asset_status[$item['asset_status']]) ? static::$t[MaterialEnums::$asset_status[$item['asset_status']]] : '';
            $item['asset_state_text'] = isset($leave_asset_state_list[$item['asset_state']]) ? static::$t[$leave_asset_state_list[$item['asset_state']]] : '';
            $item['images'] = $attachment[$item['id']] ?? [];
            $item['asset_name'] = !empty($item[$name_key]) ? $item[$name_key] : $item['asset_name_en'];
        }
        return $items;
    }

    /**
     * 离职资产上级处理-暂存资产数据
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function editSave($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //是否提交
            if ($params['is_submit'] != MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES) {
                if (empty($params['data'])) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_data_empty'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //待处理改为已处理
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            $now_date = date('Y-m-d H:i:s');
            //逐行编辑
            if (isset($params['data']) && !empty($params['data'])) {
                foreach ($params['data'] as $key => $asset_info) {
                    //查询本行数据
                    $db_asset_info = MaterialLeaveAssetsDetailModel::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $asset_info['detail_id']]
                    ]);
                    //验证
                    if (!$db_asset_info) {
                        throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
                    }
                    $before_detail = $db_asset_info->toArray();
                    //保存
                    $db_asset_info->asset_state = $asset_info['asset_state'];
                    $db_asset_info->manager_remark = $asset_info['manager_remark'];
                    $db_asset_info->updated_at = $now_date;
                    //资产状况映射的资产处理情况
                    if (isset(MaterialEnums::$leave_state_asset_handling_status_relation[$db_asset_info->asset_state])) {
                        $db_asset_info->asset_handling_status = MaterialEnums::$leave_state_asset_handling_status_relation[$db_asset_info->asset_state];
                    } else {
                        $db_asset_info->asset_handling_status = 0;
                    }
                    //如果是主管自己添加的,可以编辑下列字段
                    if ($db_asset_info->data_tag == MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                        if (isset($asset_info['asset_code'])) {
                            $db_asset_info->asset_code = $asset_info['asset_code'];
                        }
                        if (isset($asset_info['sn_code'])) {
                            $db_asset_info->sn_code = $asset_info['sn_code'];
                        }
                        if (isset($asset_info['model'])) {
                            $db_asset_info->model = $asset_info['model'];
                        }
                    }
                    //设置完成状态为'已完成', oa操作参数有必填限制, 必填项都填了才能提交, 所以提交过来的都是完成的
                    $db_asset_info->manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
                    $after_asset_info = $db_asset_info->toArray();
                    if ($db_asset_info->save() === false) {
                        throw new BusinessException('离职资产上级管理-保存失败，data = ' . json_encode($after_asset_info, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
                    }
                    //损坏资产总数,未归还资产总数
                    LeaveAssetsService::getInstance()->updateNumBySave($leave_asset, $before_detail, $after_asset_info);
                    //保存图片
                    if (isset($asset_info['images']) && !empty($asset_info['images'])) {
                        $this->saveImages($asset_info['images'], $db_asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'update');
                    }
                    unset($db_asset_info);
                }
            }
            //是否提交
            if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES) {
                //只有待处理的才能提交
                if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                    throw new ValidationException(static::$t->_('leave_asset_submit_manager_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                //验证所有行都处理过
                $not_finish_exist = MaterialLeaveAssetsDetailModel::findFirst([
                    'conditions' => 'leave_assets_id = :leave_assets_id: AND is_deleted = :is_deleted: AND manager_tag = :manager_tag: AND manager_finish = :manager_finish:',
                    'bind' => ['leave_assets_id' => $params['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'manager_tag' => MaterialEnums::MANAGER_TAG_YES, 'manager_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO]
                ]);
                if ($not_finish_exist) {
                    throw new ValidationException(static::$t->_('leave_asset_submit_manager_not_finish'), ErrCode::$VALIDATE_ERROR);
                }
                //修改状态
                $leave_asset->manager_status = MaterialEnums::MANAGER_STATUS_DEAL;
                //进度
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_DONE;
                //接收资产
                $this->afterManagerDeal($leave_asset, $user);
            }
            //首次编辑 进度待处理->处理中
            if ($leave_asset->manager_progress == MaterialEnums::MANAGER_PROGRESS_NOT) {
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_DOING;
            }
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产上级管理-提交失败，data = ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-save-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 离职资产上级处理-添加资产数据
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function editAdd($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //barcode查询资产信息
            $barcode_info = MaterialSauModel::findFirst([
                'conditions' => 'barcode = :barcode: and is_deleted = :is_deleted:',
                'bind' => [
                    'barcode' => $params['barcode'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($barcode_info)) {
                throw new ValidationException(static::$t->_('leave_asset_add_barcode_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id']
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            //添加
            $db_asset_info = new MaterialLeaveAssetsDetailModel();
            $db_asset_info->leave_assets_id = $params['id'];
            $db_asset_info->use = $params['use'];
            $db_asset_info->barcode = $params['barcode'];
            $db_asset_info->asset_code = $params['asset_code'];
            $db_asset_info->sn_code = $params['sn_code'];
            $db_asset_info->asset_name_zh = $barcode_info->name_zh;
            $db_asset_info->asset_name_en = $barcode_info->name_en;
            $db_asset_info->asset_name_local = $barcode_info->name_local;
            $db_asset_info->model = $params['model'];
            $db_asset_info->asset_status = 0;
            $db_asset_info->asset_state = $params['asset_state'];
            //资产状况映射的资产处理情况
            if (isset(MaterialEnums::$leave_state_asset_handling_status_relation[$params['asset_state']])) {
                $db_asset_info->asset_handling_status = MaterialEnums::$leave_state_asset_handling_status_relation[$params['asset_state']];
            } else {
                $db_asset_info->asset_handling_status = 0;
            }
            $db_asset_info->manager_remark = $params['manager_remark'];
            $db_asset_info->data_tag = MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER;
            $db_asset_info->manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
            $db_asset_info->manager_tag = MaterialEnums::MANAGER_TAG_YES;
            $db_asset_info->created_at = $now_date;
            if ($db_asset_info->save() === false) {
                throw new BusinessException('离职资产保存失败, data: ' . json_encode($db_asset_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
            }
            $after_asset_info = $db_asset_info->toArray();
            //添加图片
            if (!empty($params['images'])) {
                $this->saveImages($params['images'], $db_asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'add');
            }
            LeaveAssetsService::getInstance()->updateNumByAdd($leave_asset, $after_asset_info);
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产保存失败 - 更新资产数量, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-save-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 上级处理-删除自己添加的
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/18
     */
    public function editDelete($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //参数验证
            Validation::validate($params, self::$validate_delete);
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //查询本行数据
            $db_asset_info = MaterialLeaveAssetsDetailModel::findFirst([
                'conditions' => 'id = :id: AND is_deleted = :is_deleted:',
                'bind' => ['id' => $params['detail_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            //验证
            if (!$db_asset_info) {
                throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($db_asset_info->data_tag != MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                throw new ValidationException(static::$t->_('leave_asset_delete_not_allow'), ErrCode::$VALIDATE_ERROR);
            }
            //删除
            $db_asset_info->is_deleted = GlobalEnums::IS_DELETED;
            $after_asset_info = $db_asset_info->toArray();
            if ($db_asset_info->save() === false) {
                throw new BusinessException('离职资产上级管理-删除失败，data = ' . json_encode($after_asset_info, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
            }
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $db_asset_info->leave_assets_id
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            LeaveAssetsService::getInstance()->updateNumByDelete($leave_asset, $after_asset_info);
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = date('Y-m-d H:i:s');
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产删除失败 - 更新资产数量, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-delete-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 批量确认
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/8
     */
    public function batchConfirm($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //参数验证
            Validation::validate($params, self::$validate_batch_confirm);
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //查询离职资产
            $ids = array_values(array_unique($params['ids']));
            $leave_asset_data = MaterialLeaveAssetsModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind' => [
                    'ids' => $ids
                ]
            ]);
            $leave_asset_array = $leave_asset_data->toArray();
            //验证主管处理状态
            foreach ($leave_asset_array as $arr_k => $arr_v) {
                if ($arr_v['manager_status'] != MaterialEnums::MANAGER_STATUS_TODO) {
                    throw new ValidationException(static::$t->_('leave_asset_batch_confirm_manager_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                if ($arr_v['manager_staff_id'] != $user['id']) {
                    throw new ValidationException(static::$t->_('leave_asset_batch_confirm_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
            //验证总数据量
            if (count($leave_asset_array) < count($ids)) {
                throw new ValidationException(static::$t->_('leave_asset_batch_confirm_count_error'), ErrCode::$VALIDATE_ERROR);
            }
            //验证详情里有没有使用中的资产
            $db_asset_info = MaterialLeaveAssetsDetailModel::find([
                'conditions' => 'leave_assets_id in ({leave_assets_ids:array}) and is_deleted = :is_deleted: and asset_status = :asset_status:',
                'bind' => [
                    'leave_assets_ids' => $ids,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'asset_status' => MaterialEnums::ASSET_STATUS_USING
                ]
            ]);
            $db_asset_array = $db_asset_info->toArray();
            if (!empty($db_asset_array)) {
                throw new ValidationException(static::$t->_('leave_asset_batch_confirm_asset_status_using'), ErrCode::$VALIDATE_ERROR);
            }
            //执行修改,同时验证主管处理状态是"待处理"
            $update_date = date('Y-m-d H:i:s');
            foreach ($leave_asset_data as $k => $v) {
                $v->manager_status = MaterialEnums::MANAGER_STATUS_DEAL;
                $v->manager_progress = MaterialEnums::MANAGER_PROGRESS_DONE;//上级批量确认-处理进度-已处理
                $v->updated_id = $user['id'];
                $v->updated_at = $update_date;
                $v->manager_updated_id = $user['id'];
                $v->manager_updated_name = $user['name'];
                $v->manager_updated_at = $update_date;
                if ($v->save() === false) {
                    throw new BusinessException('离职资产上级管理-批量确认失败，data = ' . json_encode($v->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($v), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-batch-confirm-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 批量编辑
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/8
     */
    public function editBatchSave($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id']
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            //编辑符合条件的详情
            $now_date = date('Y-m-d H:i:s');
            //预定义条件
            $condition = 'leave_assets_id = :leave_assets_id: AND use = :use: AND barcode = :barcode: AND is_deleted = :is_deleted: ';
            $bind = ['leave_assets_id' => $params['id'], 'use' => $params['use'], 'is_deleted' => GlobalEnums::IS_NO_DELETED];
            if (!empty($params['detail_ids'])) {
                $params['detail_ids'] = array_values(array_unique($params['detail_ids']));
                $condition .= ' AND id in ({ids:array})';
                $bind['ids'] = $params['detail_ids'];
            }
            //循环处理
            foreach ($params['data'] as $data_k => $data_v) {
                //是否限制详情id范围
                $bind['barcode'] = $data_v['barcode'];
                //通过barcode查询详情
                $leave_asset_detail = MaterialLeaveAssetsDetailModel::find([
                    'conditions' => $condition,
                    'bind' => $bind
                ]);
                $detail_ids = array_column($leave_asset_detail->toArray(), 'id');
                //编辑详情数据
                $update_data = [
                    'asset_state' => $data_v['asset_state'],
                    'manager_remark' => $data_v['manager_remark'],
                    'manager_tag' => MaterialEnums::MANAGER_TAG_YES,
                    'manager_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES,
                    'updated_at' => $now_date
                ];
                //资产状况映射的资产处理情况
                if (isset(MaterialEnums::$leave_state_asset_handling_status_relation[$data_v['asset_state']])) {
                    $update_data['asset_handling_status'] = MaterialEnums::$leave_state_asset_handling_status_relation[$data_v['asset_state']];
                } else {
                    $update_data['asset_handling_status'] = 0;
                }

                foreach ($leave_asset_detail as $one_detail) {
                    $before_detail = $one_detail->toArray();
                    if ($one_detail->update($update_data) === false) {
                        throw new BusinessException('离职资产上级管理-批量编辑-修改detail表失败，id = ' . $one_detail->id . '; data = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';  可能的原因是' . get_data_object_error_msg($one_detail), ErrCode::$LEAVE_ASSET_EDIT_BATCH_UPDATE_DETAIL);
                    }
                    LeaveAssetsService::getInstance()->updateNumBySave($leave_asset, $before_detail, $one_detail->toArray());
                }
                //编辑图片
                if (!empty($data_v['images'])) {
                    $this->saveBatchImages($data_v['images'], $detail_ids);
                }
            }
            //更新主表信息
            //首次编辑 进度待处理->处理中
            if ($leave_asset->manager_progress == MaterialEnums::MANAGER_PROGRESS_NOT) {
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_DOING;
            }
            //更新损坏资产总数,未归还资产总数
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产上级管理-批量编辑-修改主表失败，data = ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-batch-confirm-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 接口方式-获取按barcode维度统计的数据
     * @param $params
     * @param $user
     * @param $locale
     * @return array
     * @date 2023/3/9
     */
    public function editGetBarcodeCount($params, $user, $locale)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //参数验证
            Validation::validate($params, self::$validate_batch_save_barcode_count);
            $data = $this->getBarcodeCount($params['id'], $locale, $params['use'], $params['detail_ids'], false);
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-batch-confirm-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取按barcode维度统计的数据
     * @param $leave_asset_id
     * @param $locale
     * @param int $use 个人资产/公共资产 不传查全部
     * @param array $detail_ids
     * @param bool $is_using
     * @return array
     * @date 2023/3/9
     */
    public function getBarcodeCount($leave_asset_id, $locale, $use = 0, $detail_ids = [], $is_using = false)
    {
        $conditions = 'leave_assets_id = :leave_assets_id: AND manager_tag = :manager_tag: AND is_deleted = :is_deleted: ';
        $bind = ['leave_assets_id' => $leave_asset_id, 'manager_tag' => MaterialEnums::MANAGER_TAG_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED];
        //个人或者公共资产
        if (in_array($use, [MaterialEnums::USE_PERSONAL, MaterialEnums::USE_PUBLIC])) {
            $conditions .= ' AND use = :use: ';
            $bind['use'] = $use;
        }
        //是否只取使用中的
        if ($is_using) {
            $conditions .= ' AND asset_status = :asset_status: ';
            $bind['asset_status'] = MaterialEnums::ASSET_STATUS_USING;
        }
        //限定详情id范围
        if (!empty($detail_ids)) {
            $detail_ids = array_values(array_unique($detail_ids));
            $conditions .= ' AND id in ({detail_ids:array}) ';
            $bind['detail_ids'] = $detail_ids;
        }
        //查询资产统计信息
        $barcode_count = MaterialLeaveAssetsDetailModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => 'count(id) as use_number, asset_name_zh, asset_name_en, asset_name_local, barcode, asset_status',
            'group' => 'barcode'
        ])->toArray();
        //返回值处理
        $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
        foreach ($barcode_count as &$value) {
            $value['asset_status_text'] = isset(MaterialEnums::$asset_status[$value['asset_status']]) ? static::$t[MaterialEnums::$asset_status[$value['asset_status']]] : '';
            //产品需求, 根据系统语言展示，中文=台账中文，英文=台账英文，不是中文，英文=台账当地语言名称，台账当地语言为空，显示英文
            $value['asset_name'] = !empty($value[$name_key]) ? $value[$name_key] : $value['asset_name_en'];
        }
        return $barcode_count;
    }

    /**
     * 保存图片
     * @param $images
     * @param $detail_id
     * @param int $oss_bucket_type 业务类型
     * @param string $action update:更新操作 add:添加操作
     * @return bool
     * @throws BusinessException
     * @date 2023/3/6
     */
    public function saveImages($images, $detail_id, $oss_bucket_type, $action = 'update')
    {
        //保存图片
        $attach_arr = [];
        if (!empty($images)) {
            foreach ($images as $images_k => $image) {
                $tmp = [];
                $tmp['oss_bucket_type'] = $oss_bucket_type;
                $tmp['oss_bucket_key'] = $detail_id;
                $tmp['sub_type'] = 0;
                $tmp['bucket_name'] = $image['bucket_name'] ?? '';
                $tmp['object_key'] = $image['object_key'] ?? '';
                $tmp['file_name'] = $image['file_name'] ?? '';
                $tmp['object_url'] = $image['object_url'] ?? '';
                $tmp['created_at'] = date('Y-m-d H:i:s');
                $attach_arr[] = $tmp;
            }
        }
        $attach = new MaterialAttachmentModel();
        if ($action == 'update') {
            //删除图片
            $old_model = $attach::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:',
                'bind' => [
                    'oss_bucket_type' => $oss_bucket_type,
                    'oss_bucket_key' => $detail_id
                ]
            ]);
            $old_model_exist = $old_model->toArray();
            if (!empty($old_model_exist)) {
                if ($old_model->delete() === false) {
                    throw new BusinessException('离职资产图片删除失败, data: ' . json_encode($old_model_exist, JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($old_model), ErrCode::$BUSINESS_ERROR);
                }
            }
        }
        //新增图片
        if (!empty($attach_arr)) {
            $attach_bool = $attach->batch_insert($attach_arr);
            if ($attach_bool === false) {
                throw new BusinessException('离职资产图片添加失败，attachment data => ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 批量编辑保存图片
     * @param $images
     * @param $detail_ids
     * @return bool
     * @throws BusinessException
     * @date 2023/3/6
     */
    public function saveBatchImages($images, $detail_ids)
    {
        $detail_ids = array_values(array_unique($detail_ids));
        $date = date('Y-m-d H:i:s');
        //保存图片
        $attach_arr = [];
        if (!empty($images)) {
            foreach ($detail_ids as $detail_id_k => $detail_id_v) {
                foreach ($images as $images_k => $image) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL;
                    $tmp['oss_bucket_key'] = $detail_id_v;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $image['bucket_name'] ?? '';
                    $tmp['object_key'] = $image['object_key'] ?? '';
                    $tmp['file_name'] = $image['file_name'] ?? '';
                    $tmp['object_url'] = $image['object_url'] ?? '';
                    $tmp['created_at'] = $date;
                    $attach_arr[] = $tmp;
                }
            }

        }
        $attach = new MaterialAttachmentModel();
        //删除图片
        $old_model = $attach::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key in ({oss_bucket_key:array})',
            'bind' => [
                'oss_bucket_type' => MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL,
                'oss_bucket_key' => $detail_ids
            ]
        ]);
        $old_model_exist = $old_model->toArray();
        if (!empty($old_model_exist)) {
            if ($old_model->delete() === false) {
                throw new BusinessException('离职资产-批量编辑-图片删除失败, oss_bucket_key: ' . json_encode($detail_ids, JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($old_model), ErrCode::$BUSINESS_ERROR);
            }
        }
        //新增图片
        if (!empty($attach_arr)) {
            $attach_bool = $attach->batch_insert($attach_arr);
            if ($attach_bool === false) {
                throw new BusinessException('离职资产-批量编辑-图片添加失败，attachment data => ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 离职资产-by端上级确认-详情
     * @date 2023/3/18
     * @param $params
     * @param $locale
     * @param $user
     * @return array
     */
    public function getAssetsManagerDetail($params, $locale, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_detail_by);
            //查询离职资产
            $data_info = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']]
            ]);
            //查询员工信息
            $staff_info_data = [];
            if (!empty($data_info->staff_info_id)) {
                $staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind' => ['staff_info_id' => $data_info->staff_info_id]
                ]);
                $staff_info_data = $staff_info ? $staff_info->toArray() : [];
            }

            if ($data_info) {
                $data = [
                    'id' => $data_info->id,
                    'staff_info_id' => $data_info->staff_info_id,
                    'staff_name' => $data_info->staff_name,
                    'job_name' => $data_info->job_name,
                    'sys_store_name' => $data_info->sys_store_name,
                    'mobile' => $staff_info_data['mobile'] ?? '',
                ];
                //资产详情
                $assets_detail_data = MaterialLeaveAssetsDetailModel::find([
                    'conditions' => 'leave_assets_id = :leave_assets_id: and is_deleted = :is_deleted: and manager_tag = :manager_tag:',
                    'bind' => [
                        'leave_assets_id' => $params['id'],
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                        'manager_tag' => MaterialEnums::MANAGER_TAG_YES
                    ]
                ])->toArray();
                //该统计的统计,没个barcode下是详情
                $name_key = isset(MaterialClassifyEnums::$language_fields[$locale]) ? 'asset_name_' . MaterialClassifyEnums::$language_fields[$locale] : 'asset_name_local';
                $leave_asset_state_list = MaterialEnums::$leave_asset_personal_state_list + MaterialEnums::$leave_asset_public_state_list;
                $attachment = (new MaterialAttachmentModel())->getColumnArrUrl($assets_detail_data, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL);
                $personal_assets = $public_assets = [];
                //最终出来的结构
                //$a = [
                //    "barcode1" => [
                //        "status1" => [
                //            [
                //                "asset_name": "11",
                //                "use_number": 2,
                //                "images":[],//图片
                //                "manager_remark":"备注",
                //                "asset_status":"2",//资产状态
                //                "asset_status_text":"使用中",//资产状态文本
                //                "detail_data": [
                //                    {
                //                        "id": "1",
                //                        "asset_code": "",//资产编码
                //                        "sn_code": "",//sn码
                //                        "asset_state":"11",//资产状况值
                //                        "asset_state_text":"",//资产状况文本
                //                        "images":[], //图片
                //                        "manager_remark":""//备注
                //                    },
                //                    {
                //                        "id": "1",
                //                        "asset_code": "",//资产编码
                //                        "sn_code": "",//sn码
                //                        "asset_state":"11",//资产状况值
                //                        "asset_state_text":"",//资产状况文本
                //                        "images":[], //图片
                //                        "manager_remark":""//备注
                //                    }
                //                ]
                //            ]
                //        ]
                //    ]
                //]
                $leave_asset_state_must_arr = explode(',', MaterialEnums::LEAVE_ASSET_STATE_MUST_VALIDATE);
                //是否全部属于主管数据(全部属于主管数据可删除卡片)
                foreach ($assets_detail_data as $value) {
                    //详情里的字段
                    $tmp_detail = [
                        'id' => $value['id'],
                        'asset_code' => $value['asset_code'],
                        'sn_code' => $value['sn_code'],
                        'asset_state' => $value['asset_state'],
                        'asset_state_text' => isset($leave_asset_state_list[$value['asset_state']]) ? $this->translateTemp($leave_asset_state_list[$value['asset_state']]) : '',
                        'images' => [],
                        'manager_remark' => '',
                    ];
                    //每个barcode+使用状态维度,丢失的,都显示图片,未丢失的,只显示一个(在最底部)
                    $detail_images = $attachment[$value['id']] ?? [];
                    if (in_array($value['asset_state'], $leave_asset_state_must_arr)) {
                        $tmp_detail['images'] = $detail_images;
                        $tmp_detail['manager_remark'] = $value['manager_remark'];
                    }
                    //区分个人和公共
                    if ($value['use'] == MaterialEnums::USE_PERSONAL) {
                        //个人资产
                        if (!isset($personal_assets[$value['barcode']][$value['asset_status']])) {
                            //按照使用状态再分一层
                            $personal_assets[$value['barcode']][$value['asset_status']]['asset_name'] = !empty($value[$name_key]) ? $value[$name_key] : $value['asset_name_en'];
                            $personal_assets[$value['barcode']][$value['asset_status']]['use_number'] = 1;
                            $personal_assets[$value['barcode']][$value['asset_status']]['is_all_manager_tag'] = true; //是否全部属于主管添加的(做是否可删除判断)
                            $personal_assets[$value['barcode']][$value['asset_status']]['manager_remark'] = '';
                            $personal_assets[$value['barcode']][$value['asset_status']]['asset_status'] = $value['asset_status'];
                            $personal_assets[$value['barcode']][$value['asset_status']]['asset_status_text'] = isset(MaterialEnums::$asset_status[$value['asset_status']]) ? $this->translateTemp(MaterialEnums::$asset_status[$value['asset_status']]) : '';
                            $personal_assets[$value['barcode']][$value['asset_status']]['images'] = [];
                            $personal_assets[$value['barcode']][$value['asset_status']]['detail_data'][] = $tmp_detail;
                        } else {
                            $personal_assets[$value['barcode']][$value['asset_status']]['use_number'] += 1;
                            $personal_assets[$value['barcode']][$value['asset_status']]['detail_data'][] = $tmp_detail;
                        }
                        //如果是损坏类的,要显示自己的图片和备注,其他的都显示在底部
                        if (!in_array($value['asset_state'], $leave_asset_state_must_arr)) {
                            if (empty($personal_assets[$value['barcode']][$value['asset_status']]['images']) && !empty($detail_images)) {
                                $personal_assets[$value['barcode']][$value['asset_status']]['images'] = $detail_images;
                            }
                            if (empty($personal_assets[$value['barcode']][$value['asset_status']]['manager_remark']) && !empty($value['manager_remark'])) {
                                $personal_assets[$value['barcode']][$value['asset_status']]['manager_remark'] = $value['manager_remark'];
                            }
                        }
                        //有非主管添加的资产,
                        if ($value['data_tag'] != MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                            $personal_assets[$value['barcode']][$value['asset_status']]['is_all_manager_tag'] = false;
                        }
                    } elseif ($value['use'] == MaterialEnums::USE_PUBLIC) {
                        //公共资产
                        if (!isset($public_assets[$value['barcode']][$value['asset_status']])) {
                            //按照使用状态再分一层
                            $public_assets[$value['barcode']][$value['asset_status']]['asset_name'] = !empty($value[$name_key]) ? $value[$name_key] : $value['asset_name_en'];
                            $public_assets[$value['barcode']][$value['asset_status']]['use_number'] = 1;
                            $public_assets[$value['barcode']][$value['asset_status']]['is_all_manager_tag'] = true; //是否全部属于主管添加的(做是否可删除判断)
                            $public_assets[$value['barcode']][$value['asset_status']]['manager_remark'] = '';
                            $public_assets[$value['barcode']][$value['asset_status']]['asset_status'] = $value['asset_status'];
                            $public_assets[$value['barcode']][$value['asset_status']]['asset_status_text'] = isset(MaterialEnums::$asset_status[$value['asset_status']]) ? $this->translateTemp(MaterialEnums::$asset_status[$value['asset_status']]) : '';
                            $public_assets[$value['barcode']][$value['asset_status']]['images'] = [];
                            $public_assets[$value['barcode']][$value['asset_status']]['detail_data'][] = $tmp_detail;
                        } else {
                            $public_assets[$value['barcode']][$value['asset_status']]['use_number'] += 1;
                            $public_assets[$value['barcode']][$value['asset_status']]['detail_data'][] = $tmp_detail;
                        }
                        //如果是损坏类的,要显示自己的图片和备注,其他的都显示在底部
                        if (!in_array($value['asset_state'], $leave_asset_state_must_arr)) {
                            if (empty($public_assets[$value['barcode']][$value['asset_status']]['images']) && !empty($detail_images)) {
                                $public_assets[$value['barcode']][$value['asset_status']]['images'] = $detail_images;
                            }
                            if (empty($public_assets[$value['barcode']][$value['asset_status']]['manager_remark']) && !empty($value['manager_remark'])) {
                                $public_assets[$value['barcode']][$value['asset_status']]['manager_remark'] = $value['manager_remark'];
                            }
                        }
                        //有非主管添加的资产,
                        if ($value['data_tag'] != MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                            $public_assets[$value['barcode']][$value['asset_status']]['is_all_manager_tag'] = false;
                        }
                    }
                }
                //数据处理,把barcode的key和status的key去掉
                $personal_card_data = $public_card_data = [];
                foreach ($personal_assets as $barcode_v) {
                    foreach ($barcode_v as $status_v) {
                        $personal_card_data[] = $status_v;
                    }
                }
                foreach ($public_assets as $public_barcode_v) {
                    foreach ($public_barcode_v as $public_status_v) {
                        $public_card_data[] = $public_status_v;
                    }
                }
                $data['personal_assets'] = array_values($personal_card_data);
                $data['public_assets'] = array_values($public_card_data);
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-my-asset-detail-by-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by端上级处理-添加资产数据
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function editAddBy($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_date = date('Y-m-d H:i:s');
            //barcode查询资产信息
            $barcode_info = MaterialSauModel::findFirst([
                'conditions' => 'barcode = :barcode: and is_deleted = :is_deleted:',
                'bind' => [
                    'barcode' => $params['barcode'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($barcode_info)) {
                throw new ValidationException(static::$t->_('leave_asset_add_barcode_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $params['id']
                ]
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_add_leave_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            $leave_asset_state_must_arr = explode(',', MaterialEnums::LEAVE_ASSET_STATE_MUST_VALIDATE);
            //添加
            foreach ($params['data'] as $detail_info) {
                //归类为损坏的条件
                $bad_condition = in_array($detail_info['asset_state'], $leave_asset_state_must_arr);
                //损坏的必须传图片,填备注
                if ($bad_condition) {
                    if (empty($detail_info['images'])) {
                        throw new ValidationException(static::$t->_('by_leave_asset_add_images_is_must'), ErrCode::$VALIDATE_ERROR);
                    }
                    if (empty($detail_info['manager_remark'])) {
                        throw new ValidationException(static::$t->_('by_leave_asset_add_manager_remark_is_must'), ErrCode::$VALIDATE_ERROR);
                    }
                }
                $db_asset_info = new MaterialLeaveAssetsDetailModel();
                $db_asset_info->leave_assets_id = $params['id'];
                $db_asset_info->use = MaterialEnums::USE_PERSONAL;//by添加的都是个人
                $db_asset_info->barcode = $params['barcode'];
                $db_asset_info->asset_code = $detail_info['asset_code'];
                $db_asset_info->sn_code = $detail_info['sn_code'];
                $db_asset_info->asset_name_zh = $barcode_info->name_zh;
                $db_asset_info->asset_name_en = $barcode_info->name_en;
                $db_asset_info->asset_name_local = $barcode_info->name_local;
                $db_asset_info->asset_status = 0;
                $db_asset_info->asset_state = $detail_info['asset_state'];
                //资产状况映射的资产处理情况
                if (isset(MaterialEnums::$leave_state_asset_handling_status_relation[$detail_info['asset_state']])) {
                    $db_asset_info->asset_handling_status = MaterialEnums::$leave_state_asset_handling_status_relation[$detail_info['asset_state']];
                } else {
                    $db_asset_info->asset_handling_status = 0;
                }
                $db_asset_info->data_tag = MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER;
                $db_asset_info->manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
                $db_asset_info->manager_tag = MaterialEnums::MANAGER_TAG_YES;
                $db_asset_info->created_at = $now_date;
                //正常情况使用公共的图片和备注, 异常情况使用自己的图片和备注
                if ($bad_condition) {
                    $db_asset_info->manager_remark = $detail_info['manager_remark'] ?? '';
                } elseif (!empty($params['manager_remark'])) {
                    $db_asset_info->manager_remark = $params['manager_remark'];
                }
                //先添加详情表记录, 添加图片时要用到id
                if ($db_asset_info->save() === false) {
                    throw new BusinessException('by端离职资产保存失败, data: ' . json_encode($db_asset_info->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
                }
                $after_asset_info = $db_asset_info->toArray();
                //添加图片
                if ($bad_condition) {
                    $this->saveImages($detail_info['images'], $db_asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'add');
                } elseif (!empty($params['images'])) {
                    $this->saveImages($params['images'], $db_asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'add');
                }
                LeaveAssetsService::getInstance()->updateNumByAdd($leave_asset, $after_asset_info);
            }
            //修改主数据的资产数量
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = $now_date;
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产保存失败 - 更新资产数量, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-add-by-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by端上级处理-保存处理信息
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/6
     */
    public function editSaveBy($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //参数验证
            if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES) {
                //大保存要验证所有必填项
                Validation::validate($params, self::$validate_edit_save_by);
            } else {
                //小保存只是暂存,宽松验证,保证字段类型和长度符合数据库
                Validation::validate($params, self::$validate_edit_tmp_save_by);
            }
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['id']],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_save_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            $now_date = date('Y-m-d H:i:s');
            //每个卡片编辑
            foreach ($params['all_data'] as $key => $card_info) {
                //查询卡片中的资产
                $asset_ids = array_column($card_info['data'], 'detail_id');
                $asset_ids = array_values(array_unique($asset_ids));
                if (empty($asset_ids)) {
                    continue;
                }
                $asset_data = MaterialLeaveAssetsDetailModel::find([
                    'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted:',
                    'bind' => [
                        'ids' => $asset_ids,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    ]
                ]);
                if (empty($asset_data->toArray())) {
                    throw new ValidationException(static::$t->_('leave_asset_save_asset_detail_not_exist'), ErrCode::$VALIDATE_ERROR);
                }
                //参数构建kv结构
                $card_info_data_kv = array_column($card_info['data'], null, 'detail_id');
                $leave_asset_state_must_arr = explode(',', MaterialEnums::LEAVE_ASSET_STATE_MUST_VALIDATE);
                //一个卡片中的资产逐行编辑, 同时验证是否完成(必填项都填了)维护manager_finish字段,方便大保存时候验证所有详情完成情况
                foreach ($asset_data as $asset_info) {
                    $before_detail = $asset_info->toArray();
                    //参数中的值
                    $one_card_data_info = $card_info_data_kv[$asset_info->id];
                    //考虑到小保存(暂存),传啥存啥,所以都需要isset判断,如果用empty判断,那设置为空的就保存不了了

                    //1.图片和备注处理 损坏类型的使用内层的, 非损坏类型的使用外层的
                    //损坏类型的,存自己的图片和备注, 非损坏的,存此卡片公共的图片和备注
                    if (isset($one_card_data_info['asset_state']) && in_array($one_card_data_info['asset_state'], $leave_asset_state_must_arr)) {
                        //提交时损坏类的必须填备注和图片
                        if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES
                            && (empty($one_card_data_info['manager_remark']) || empty($one_card_data_info['images']))) {
                            throw new ValidationException(static::$t->_('leave_asset_state_remark_and_images_must_validate'), ErrCode::$VALIDATE_ERROR);
                        }
                        //非提交时传了就存
                        if (isset($one_card_data_info['manager_remark'])) {
                            $asset_info->manager_remark = $one_card_data_info['manager_remark'];
                        }
                        if (isset($one_card_data_info['images'])) {
                            $this->saveImages($one_card_data_info['images'], $asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'update');
                        }
                    } else {
                        //非损坏类型
                        //提交时,如果是主管添加的,备注可以不填,不是主管添加的数据,外层备注必填  特殊情况:如果这个卡片里只有损坏类的,那外层的备注可以不填,因为损坏类的内存备注已经验证必填了,所以进到这个else里的才验证
                        if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES
                            && $asset_info->data_tag != MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER
                            && empty($card_info['manager_remark'])) {
                            throw new ValidationException(static::$t->_('leave_asset_card_remark_must_validate'), ErrCode::$VALIDATE_ERROR);
                        }
                        if (isset($card_info['manager_remark'])) {
                            $asset_info->manager_remark = $card_info['manager_remark'];
                        }
                        if (isset($card_info['images'])) {
                            $this->saveImages($card_info['images'], $asset_info->id, MaterialEnums::OSS_MATERIAL_TYPE_LEAVE_ASSET_DETAIL, 'update');
                        }
                    }
                    if (isset($one_card_data_info['asset_state'])) {
                        $asset_info->asset_state = $one_card_data_info['asset_state'];
                        //资产状况映射的资产处理情况
                        if (isset(MaterialEnums::$leave_state_asset_handling_status_relation[$one_card_data_info['asset_state']])) {
                            $asset_info->asset_handling_status = MaterialEnums::$leave_state_asset_handling_status_relation[$one_card_data_info['asset_state']];
                        } else {
                            $asset_info->asset_handling_status = 0;
                        }
                    }
                    //2.资产编码和sn码处理 如果是主管自己添加的,可以编辑,否则不可编辑
                    if ($asset_info->data_tag == MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                        if (isset($one_card_data_info['asset_code'])) {
                            $asset_info->asset_code = $one_card_data_info['asset_code'];
                        }
                        if (isset($one_card_data_info['sn_code'])) {
                            $asset_info->sn_code = $one_card_data_info['sn_code'];
                        }
                    }
                    //3.验证完成情况 用于大提交时验证是否所有资产处理完毕
                    $manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_YES;
                    if ($asset_info->asset_state <= 0) {
                        //没填资产状况,未完成
                        $manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO;
                    } elseif (in_array($asset_info->asset_state, $leave_asset_state_must_arr)
                        && (empty($one_card_data_info['manager_remark']) || empty($one_card_data_info['images']))) {
                        //损坏类的,没填图片和备注,未完成
                        $manager_finish = MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO;
                    }
                    //4.保存必须修改的字段 无论是否改了都会报错的字段
                    $asset_info->manager_finish = $manager_finish;
                    $asset_info->updated_at = $now_date;
                    if ($asset_info->manager_tag != MaterialEnums::MANAGER_TAG_YES) {
                        $asset_info->manager_tag = MaterialEnums::MANAGER_TAG_YES;
                    }
                    //执行保存
                    $after_asset_info = $asset_info->toArray();
                    if ($asset_info->save() === false) {
                        throw new BusinessException('离职资产上级管理-by端-保存失败，data = ' . json_encode($after_asset_info, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($asset_info), ErrCode::$BUSINESS_ERROR);
                    }
                    //主表数据的 损坏资产总数,未归还资产总数
                    LeaveAssetsService::getInstance()->updateNumBySave($leave_asset, $before_detail, $after_asset_info);
                }
            }
            //是否提交
            if ($params['is_submit'] == MaterialEnums::LEAVE_ASSET_SAVE_IS_SUBMIT_YES) {
                //验证所有行都处理过
                $not_finish_exist = MaterialLeaveAssetsDetailModel::findFirst([
                    'conditions' => 'leave_assets_id = :leave_assets_id: AND is_deleted = :is_deleted: AND manager_tag = :manager_tag: AND manager_finish = :manager_finish:',
                    'bind' => ['leave_assets_id' => $params['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'manager_tag' => MaterialEnums::MANAGER_TAG_YES, 'manager_finish' => MaterialEnums::LEAVE_ASSET_MANAGER_FINISH_NO]
                ]);
                if ($not_finish_exist) {
                    throw new ValidationException(static::$t->_('leave_asset_submit_manager_not_finish'), ErrCode::$VALIDATE_ERROR);
                }
                if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                    throw new ValidationException(static::$t->_('leave_asset_submit_manager_status_error'), ErrCode::$VALIDATE_ERROR);
                }
                //修改状态
                $leave_asset->manager_status = MaterialEnums::MANAGER_STATUS_DEAL;
                //进度
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_DONE;
                //接收资产
                $this->afterManagerDeal($leave_asset, $user);
            }
            //主表 状态字段 和 数量等字段维护
            //首次编辑 进度待处理->处理中
            if ($leave_asset->manager_progress == MaterialEnums::MANAGER_PROGRESS_NOT) {
                $leave_asset->manager_progress = MaterialEnums::MANAGER_PROGRESS_DOING;
            }
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = $now_date;
            //总数量没有变化不用处理
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产上级管理-by-保存leave_asset信息失败，data = ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-save-by-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by端-上级处理-删除自己添加的
     * @param $params
     * @param $user
     * @return array
     * @date 2023/3/18
     */
    public function editDeleteBatch($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //参数验证
            Validation::validate($params, self::$validate_delete_by);
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //查询数据
            $detail_ids = array_values(array_unique($params['batch_detail_ids']));
            $db_asset_data = MaterialLeaveAssetsDetailModel::find([
                'conditions' => 'id in ({id:array}) AND is_deleted = :is_deleted:',
                'bind' => ['id' => $detail_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            //验证
            $db_asset_array = $db_asset_data->toArray();
            if (!$db_asset_array) {
                throw new ValidationException(static::$t->_('leave_asset_delete_asset_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if (count($db_asset_array) != count($detail_ids)) {
                throw new ValidationException(static::$t->_('leave_asset_delete_detail_ids_error'), ErrCode::$VALIDATE_ERROR);
            }
            $leave_assets_id = array_column($db_asset_array, 'leave_assets_id');
            $leave_assets_id = array_unique($leave_assets_id);
            if (count($leave_assets_id) != 1) {
                throw new ValidationException(static::$t->_('leave_asset_delete_assets_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            $leave_assets_id = reset($leave_assets_id);
            //查询离职资产
            $leave_asset = MaterialLeaveAssetsModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $leave_assets_id
                ],
                'for_update' => true
            ]);
            if (empty($leave_asset)) {
                throw new ValidationException(static::$t->_('leave_asset_delete_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_staff_id != $user['id']) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_manager_staff_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($leave_asset->manager_status != MaterialEnums::MANAGER_STATUS_TODO) {
                throw new ValidationException(static::$t->_('leave_asset_batch_save_status_error'), ErrCode::$VALIDATE_ERROR);
            }
            //执行删除
            foreach ($db_asset_data as $db_asset_info) {
                if ($db_asset_info->data_tag != MaterialEnums::LEAVE_ASSET_DATA_TAG_MANAGER) {
                    throw new ValidationException(static::$t->_('leave_asset_delete_not_allow'), ErrCode::$VALIDATE_ERROR);
                }
                $db_asset_arr = $db_asset_info->toArray();
                //删除
                $db_asset_info->is_deleted = GlobalEnums::IS_DELETED;
                if ($db_asset_info->save() === false) {
                    throw new BusinessException('离职资产上级管理-by-删除失败，data = ' . json_encode($db_asset_arr, JSON_UNESCAPED_UNICODE) . '可能的原因是:' . get_data_object_error_msg($db_asset_info), ErrCode::$BUSINESS_ERROR);
                }
                //修改主数据的资产数量
                LeaveAssetsService::getInstance()->updateNumByDelete($leave_asset, $db_asset_arr);
            }
            //保存主数据
            $leave_asset->manager_updated_id = $user['id'];
            $leave_asset->manager_updated_name = $user['name'];
            $leave_asset->manager_updated_at = date('Y-m-d H:i:s');
            if ($leave_asset->save() === false) {
                throw new BusinessException('离职资产删除失败- by - 更新资产数量, data: ' . json_encode($leave_asset->toArray(), JSON_UNESCAPED_UNICODE) . '可能的原因是: ' . get_data_object_error_msg($leave_asset), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-manager-edit-delete-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取-by端上级确认-数量(红点) 和oa上级处理小红点
     * @param $user
     * @return array
     * @date 2023/3/15
     */
    public function getLeaveAssetsManagerCount($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'num' => 0
        ];
        try {
            $conditions = ['list_type' => MaterialEnums::LIST_TYPE_PENDING];
            $data['num'] = $this->getListCount($conditions, $user);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取-by端上级确认-数量(红点)异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 主管已处理时需要做的操作
     * @param object $leave_asset
     * @param $user
     * @return bool
     * @throws BusinessException
     * @date 2023/3/23
     */
    public function afterManagerDeal(object $leave_asset, $user)
    {
        //离职员工或主管没有值,不做处理
        if (empty($leave_asset->staff_info_id) || empty($leave_asset->manager_staff_id)) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 员工没有主管');
            return false;
        }
        $from_staff_id = $leave_asset->staff_info_id;
        $to_staff_id = $leave_asset->manager_staff_id;
        //查接收人
        $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $to_staff_id, 'limit' => 1]);
        //接收人不符合条件,不接收
        if (!isset($staff_list['data'][0]) || empty($staff_list['data'][0])) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 接收人不符合条件-在职,编制等');
            return false;
        }
        $to_staff_info = $staff_list['data'][0];
        // pc_code获取和资产部互转一致,资产部互转调用接口payment/store_renting/getPcCode
        if ($to_staff_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_HEAD_OFFICE;
            $get_pc_id = $to_staff_info['node_department_id'];
        } else {
            $get_pc_type = StoreRentingAddService::getInstance()::COST_TYPE_SYS_STORE;
            $get_pc_id = $to_staff_info['sys_store_id'];
        }
        $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($get_pc_id, $get_pc_type);
        $to_staff_info['pc_code'] = $pc_code_data['data']['pc_code'] ?? '';
        //没有需要接收的,不做处理
        $db_asset_data = MaterialLeaveAssetsDetailModel::find([
            'conditions' => 'leave_assets_id = :leave_assets_id:',
            'bind' => ['leave_assets_id' => $leave_asset->id]
        ])->toArray();
        //一个没有不接收
        if (empty($db_asset_data)) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 没有需要接收的离职资产');
            return false;
        }
        //挑选主管数据,员工自有的,完好的,使用中的
        $leave_asset_transfer_data = [];
        //离职资产中要转移的资产id
        $transfer_asset_ids = [];
        foreach ($db_asset_data as $one_detail) {
            if ($one_detail['manager_tag'] == MaterialEnums::MANAGER_TAG_YES
                && $one_detail['data_tag'] == MaterialEnums::LEAVE_ASSET_DATA_TAG_SELF
                && in_array($one_detail['asset_state'], [MaterialEnums::LEAVE_ASSET_PUBLIC_STATE_INTACT, MaterialEnums::LEAVE_ASSET_PERSONAL_STATE_NORMAL])
                && $one_detail['asset_status'] == MaterialEnums::ASSET_STATUS_USING) {
                $leave_asset_transfer_data[$one_detail['asset_id']] = $one_detail;
                $transfer_asset_ids[] = $one_detail['asset_id'];
            }
        }
        //没一个符合条件的
        if (empty($leave_asset_transfer_data)) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 没有符合接收条件的离职资产');
            return false;
        }
        $transfer_asset_ids = array_values(array_unique($transfer_asset_ids));
        //查询员工最新资产台账
        $obj_material_assets = new MaterialAssetsModel();
        $material_asset_obj = $obj_material_assets::find([
            'conditions' => 'id in ({ids:array}) and staff_id = :staff_id: and status = :status: and is_deleted = :is_deleted:',
            'bind' => [
                'ids' => $transfer_asset_ids,
                'staff_id' => $from_staff_id,
                'status' => MaterialEnums::ASSET_STATUS_USING,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ]);
        $material_asset_data = $material_asset_obj->toArray();
        //员工没有使用中资产,不接收
        if (empty($material_asset_data)) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 离职员工资产台账没有使用中的资产');
            return false;
        }
        //员工资产台账中,使用中的资产,且离职资产里处理为完好的,接收
        $now_time = date('Y-m-d H:i:s');
        $transfer_data = [];
        //操作日志
        $update_log_model = new MaterialAssetUpdateLogModel();
        //要修改的台账-修改前数据
        $asset_before_data = [];
        //要修改的台账-修改后数据
        $asset_after_data = [
            'staff_id' => $to_staff_info['staff_id'],
            'staff_name' => $to_staff_info['staff_name'],
            'state' => $to_staff_info['state'] ?? 0,
            'leave_date' => $to_staff_info['leave_date'] ?? null,
            'wait_leave_state' => $to_staff_info['wait_leave_state'] ?? 0,
            'job_id' => $to_staff_info['job_id'] ?? 0,
            'job_name' => $to_staff_info['job_name'] ?? '',
            'node_department_id' => $to_staff_info['node_department_id'] ?? 0,
            'node_department_name' => $to_staff_info['node_department_name'] ?? '',
            'company_id' => $to_staff_info['company_id'] ?? 0,
            'company_name' => $to_staff_info['company_name'] ?? '',
            'sys_store_id' => $to_staff_info['sys_store_id'] ?? 0,
            'store_name' => $to_staff_info['store_name'],
            'pc_code' => $to_staff_info['pc_code'] ?? '',
            'use_land' => $to_staff_info['store_name'] ?? '',
            'status' => MaterialEnums::ASSET_STATUS_USING,
            'receipted_at' => $now_time,
            'updated_at' => $now_time,
        ];
        //19094需求，操作人是总部，使用网点、使用地不做变更
        if ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
            unset($asset_after_data['sys_store_id'], $asset_after_data['store_name'], $asset_after_data['use_land']);
        }

        //资产转移 记录协议公司
        $fromStaff = empty($asset_list) ? [] : array_column($asset_list, 'staff_id');
        $toStaff   = [$to_staff_info['staff_id']];
        $allStaff  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
        //查询员工协议公司
        $staffData = HrStaffInfoModel::find([
            'columns'    => 'staff_info_id, contract_company_id',
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $allStaff],
        ])->toArray();
        $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
        //获取公司名称
        $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

        //要修改的台账id
        $asset_ids = [];
        foreach ($material_asset_obj as $one_asset) {
            if (isset($leave_asset_transfer_data[$one_asset->id])) {
                $asset_ids[] = $one_asset->id;
                //拼接转移记录
                $tmp_transfer = [];
                $tmp_transfer['asset_id'] = $one_asset->id;
                $tmp_transfer['barcode'] = $one_asset->bar_code;
                $tmp_transfer['asset_code'] = $one_asset->asset_code;
                $tmp_transfer['from_staff_id'] = $one_asset->staff_id;
                $tmp_transfer['from_node_department_id'] = $one_asset->node_department_id;
                $tmp_transfer['from_node_department_name'] = $one_asset->node_department_name;
                $tmp_transfer['from_sys_store_id'] = $one_asset->sys_store_id;
                $tmp_transfer['from_store_name'] = $one_asset->store_name;
                $tmp_transfer['from_pc_code'] = $one_asset->pc_code;
                $tmp_transfer['from_use_land'] = $one_asset->use_land;
                $tmp_transfer['from_company_id'] = $one_asset->company_id;
                $tmp_transfer['from_company_name'] = $one_asset->company_name;
                $tmp_transfer['from_contract_company_id'] = $staffData[$one_asset->staff_id] ?? 0;
                $tmp_transfer['from_contract_company_name'] = $companyList[$staffData[$one_asset->staff_id] ?? 0] ?? '';
                $tmp_transfer['to_staff_id'] = $to_staff_info['staff_id'];
                $tmp_transfer['to_node_department_id'] = $to_staff_info['node_department_id'] ?? 0;
                $tmp_transfer['to_node_department_name'] = $to_staff_info['node_department_name'] ?? '';
                $tmp_transfer['to_sys_store_id'] = ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) ? $one_asset->sys_store_id : ($to_staff_info['sys_store_id'] ?? '');
                $tmp_transfer['to_store_name'] = ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) ? $one_asset->store_name : ($to_staff_info['store_name'] ?? '');
                $tmp_transfer['to_pc_code'] = $to_staff_info['pc_code'] ?? '';
                $tmp_transfer['to_use_land'] = ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) ? $one_asset->use_land : ($to_staff_info['store_name'] ?? '');
                $tmp_transfer['to_company_id'] = $to_staff_info['company_id'] ?? 0;
                $tmp_transfer['to_company_name'] = $to_staff_info['company_name'] ?? '';
                $tmp_transfer['to_contract_company_id'] = $staffData[$to_staff_info['staff_id']] ?? 0;
                $tmp_transfer['to_contract_company_name'] = $companyList[$staffData[$to_staff_info['staff_id']] ?? 0] ?? '';
                $tmp_transfer['status'] = MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED;
                $tmp_transfer['transfer_type'] = MaterialEnums::TRANSFER_TYPE_LEAVE_ASSET_AUTO;
                $tmp_transfer['transfer_remark'] = $leave_asset_transfer_data[$one_asset->id]['manager_remark'];//主管备注带到转移记录表上转移原因
                $tmp_transfer['transfer_at'] = $now_time;
                $tmp_transfer['finished_at'] = $now_time;
                $tmp_transfer['created_at'] = $now_time;
                $tmp_transfer['updated_at'] = $now_time;
                $transfer_data[] = $tmp_transfer;
                //记录资产台账修改前后的值
                $asset_before_data_info = [
                    'asset_code' => $one_asset->asset_code,
                    'staff_id' => $one_asset->staff_id,
                    'staff_name' => $one_asset->staff_name,
                    'state' => $one_asset->state,
                    'leave_date' => $one_asset->leave_date,
                    'wait_leave_state' => $one_asset->wait_leave_state,
                    'job_id' => $one_asset->job_id,
                    'job_name' => $one_asset->job_name,
                    'node_department_id' => $one_asset->node_department_id,
                    'node_department_name' => $one_asset->node_department_name,
                    'company_id' => $one_asset->company_id,
                    'company_name' => $one_asset->company_name,
                    'sys_store_id' => $one_asset->sys_store_id,
                    'store_name' => $one_asset->store_name,
                    'pc_code' => $one_asset->pc_code,
                    'use_land' => $one_asset->use_land,
                    'status' => $one_asset->status,
                    'receipted_at' => $one_asset->receipted_at,
                    'updated_at' => $one_asset->updated_at,
                ];
                if ($user['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                    unset($asset_before_data_info['sys_store_id'], $asset_before_data_info['store_name'], $asset_before_data_info['use_land']);
                }
                $asset_before_data[] = $asset_before_data_info;
            }
        }
        //没有需要接收的, 台账和离职资产没有交集数据
        if (empty($transfer_data) || empty($asset_ids)) {
            $this->logger->info('after-manager-deal-log 离职资产主管接收: 台账和离职资产没有交集数据');
            return false;
        }
        //批量修改台账
        $asset_ids = array_values(array_unique($asset_ids));
        $asset_ids_str = implode(',', $asset_ids);
        $update_bool = $this->getDI()->get('db_oa')->updateAsDict(
            $obj_material_assets->getSource(),
            $asset_after_data,
            ['conditions' => "id in ({$asset_ids_str})"]
        );
        if ($update_bool === false) {
            throw new BusinessException('离职资产-接收资产-更新台账失败 ids=' . $asset_ids_str . '; update_data=' . json_encode($asset_after_data, JSON_UNESCAPED_UNICODE));
        }
        //批量记录日志
        $log_bool = $update_log_model->dealEditDataBatch($asset_before_data, $asset_after_data, $user);
        if ($log_bool === false) {
            throw new BusinessException('离职资产-接收资产-操作记录 before_data = ' . json_encode($asset_before_data, JSON_UNESCAPED_UNICODE) . '; after_data = ' . json_encode($asset_after_data, JSON_UNESCAPED_UNICODE), ErrCode::$LEAVE_ASSET_AUTO_TRANSFER_SAVE_ASSET_LOG);
        }
        //生成转移记录
        $transfer_main = new MaterialAssetTransferBatchModel();
        $transfer_batch_data = [
            'staff_id' => $user['id'],
            'staff_name' => (new BaseService())->getNameAndNickName($user['name'], $user['nick_name']),
            'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
            'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
            'mark' => '离职资产主管处理后自动接收',
            'created_at' => $now_time,
            'updated_at' => $now_time
        ];
        $bool = $transfer_main->i_create($transfer_batch_data);
        if ($bool === false) {
            throw new BusinessException('after-manager-deal-log-error 离职资产-接收转移-记录转移头信息失败, 数据: ' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_main), ErrCode::$LEAVE_ASSET_AUTO_TRANSFER_SAVE_TRANSFER_BATCH);
        }
        //生成转移记录详情
        foreach ($transfer_data as &$transfer_v) {
            $transfer_v['batch_id'] = $transfer_main->id;
        }
        $transfer_detail = new MaterialAssetTransferLogModel();
        $transfer_log_bool = $transfer_detail->batch_insert($transfer_data);
        if ($transfer_log_bool === false) {
            throw new BusinessException('after-manager-deal-log-error 离职资产-接收转移-记录转移行信息失败, 数据: ' . json_encode($transfer_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是: ' . get_data_object_error_msg($transfer_detail), ErrCode::$LEAVE_ASSET_AUTO_TRANSFER_SAVE_TRANSFER_LOG);
        }
        return true;
    }

    /**
     * 离职资产-by打卡限制-主管名下是否有待处理的员工离职资产任务,且员工最后工作日在在今天之前(包含今天)
     * @param $params
     * @return array
     * @date 2023/3/30
     */
    public function hasLeaveAssets($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;

        try {
            //当前国家是否开启限制打卡
            $limit_clock_in = EnvModel::getEnvByCode('limit_clock_in_by_leave_asset', 0);
            if (is_numeric($limit_clock_in) && $limit_clock_in == 1) {
                //参数验证
                Validation::validate($params, self::$validate_has_leave_assets);
                // 查询当前员工待处理的离职资产数据
                //last_work_date和leave_date只有null和日期格式两种,不用考虑空字符串
                //第一种 最后工作日不为空,在打卡当日之前,限制打卡
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                $builder->where('main.manager_staff_id = :manager_staff_id: and main.manager_status = :manager_status: and main.is_valid = :is_valid: and staff.last_work_date is not null and staff.last_work_date <= :last_work_date:',
                    [
                        'manager_staff_id' => $params['staff_id'],
                        'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES,
                        'manager_status' => MaterialEnums::MANAGER_STATUS_TODO,
                        'last_work_date' => $params['clock_in_date'],
                    ]);
                $leave_assets = $builder->getQuery()->getSingleResult();
                if (empty($leave_assets)) {
                    //第二种 最后工作日为空,离职日期在今天之前的,也限制打卡
                    $builder = $this->modelsManager->createBuilder();
                    $builder->from(['main' => MaterialLeaveAssetsModel::class]);
                    $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_info_id', 'staff');
                    $builder->where('main.manager_staff_id = :manager_staff_id: and main.manager_status = :manager_status: and main.is_valid = :is_valid: and staff.last_work_date is null and staff.leave_date is not null and staff.leave_date <= :leave_date:',
                        [
                            'manager_staff_id' => $params['staff_id'],
                            'is_valid' => MaterialEnums::LEAVE_ASSET_IS_VALID_YES,
                            'manager_status' => MaterialEnums::MANAGER_STATUS_TODO,
                            'leave_date' => $params['clock_in_date'],
                        ]);
                    $leave_assets = $builder->getQuery()->getSingleResult();
                }
                if (!empty($leave_assets)) {
                    $this->logger->info('has-leave-assets-return-true params=' . json_encode($params) . 'data=' . json_encode($leave_assets->toArray(), JSON_UNESCAPED_UNICODE) . '; datetime=' . date('Y-m-d H:i:s'));
                    $data = true;
                }
            }
        } catch (ValidationException $e) {
            //参数异常data也返回false
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('leave-asset-has-leave-assets-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * by端越南和老挝临时翻译, 等开放翻译后走统一翻译
     * @param $text
     * @return mixed
     * @date 2023/5/12
     */
    public function translateTemp($text)
    {
        //越南和老挝临时翻译
        $transfer_data = [
            'vi' => [
                //资产状况-个人资产
                'leave_asset_public_state_normal' => 'Sử dụng bình thường',
                'leave_asset_public_state_no_return' => 'Tài sản chưa quy hoàn',
                'leave_asset_public_state_asset_lost' => 'Tài sản bị mất',
                'leave_asset_public_state_loss' => 'Tài sản tổn thất ko thể sử dụng',
                'leave_asset_public_state_screen_loss' => 'Màn hình bị hỏng',
                'leave_asset_public_state_camera_loss' => 'Camera bị hỏng',
                'leave_asset_public_state_charger_loss' => 'Bộ sạc chưa được trả lại',
                'leave_asset_public_state_have_password' => 'Có mật mã, ko thể dùng',
                'leave_asset_personal_state_unidentified' => 'Đã nhận nhưng ko thể xác nhận tài sản sử dụng bình thường',
                //资产状况-公共资产
                'leave_asset_personal_state_intact' => 'Vẫn bình thường',
                'leave_asset_personal_state_lost' => 'Bị mất',
                'leave_asset_personal_state_damage' => 'Bị hư hại',
                //资产使用状态
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_UNUSED => 'Chưa dùng',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_USING => 'Đang sử dụng',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_ALLOT => 'Đang chuyển',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_REPAIRED => 'Đã báo sửa',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_REPAIRING => 'Đang sửa',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED => 'Sắp hỏng',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_HANDLED => 'Đã xử lí',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_SCRAPPED => 'Đã hỏng',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_LOST => 'Đã mất',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_OUT_STORAGE => 'Đạng xuất kho',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_OUT_STORAGE_REJECT => 'Từ chối xuất kho',
                //使用方向
                'material_asset_use.' . MaterialEnums::USE_PERSONAL => 'Cá nhận sử dụng',
                'material_asset_use.' . MaterialEnums::USE_PUBLIC => 'Sử dụng công cộng',
                //转交方式
                'asset_transfer_method.face' => 'Chuyển giao tận tay',
                'asset_transfer_method.post' => 'Chuyển giao ký gửi',
            ],
            'la' => [
                //资产状况-个人资产
                'leave_asset_public_state_normal' => 'ການ​ນໍາ​ໃຊ້​ປົກ​ກະ​ຕິ​',
                'leave_asset_public_state_no_return' => 'ຊັບສິນບໍ່ໄດ້ສົ່ງຄືນ',
                'leave_asset_public_state_asset_lost' => 'ການສູນເສຍຊັບສິນ',
                'leave_asset_public_state_loss' => 'ການສູນເສຍຊັບສິນທີ່ບໍ່ສາມາດໃຊ້ໄດ້',
                'leave_asset_public_state_screen_loss' => 'ຫນ້າຈໍແຕກ',
                'leave_asset_public_state_camera_loss' => 'ກ້ອງຖ່າຍຮູບເປ່ເພ',
                'leave_asset_public_state_charger_loss' => 'ເຄື່ອງສາກຊັບສິນບໍ່ໄດ້ສົ່ງຄືນ',
                'leave_asset_public_state_have_password' => 'ບໍ່ສາມາດໃຊ້ລະຫັດຜ່ານໄດ້',
                'leave_asset_personal_state_unidentified' => 'ໄດ້ຮັບແຕ່ບໍ່ສາມາດຢືນຢັນການນໍາໃຊ້ຊັບສິນປົກກະຕິ',
                //资产状况-公共资产
                'leave_asset_personal_state_intact' => 'ສົມບູນ',
                'leave_asset_personal_state_lost' => 'ເສຍຫາຍ',
                'leave_asset_personal_state_damage' => 'ຄືນຊັບສິນທີ່ເສຍຫາຍ',
                //资产使用状态
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_UNUSED => 'ບໍ່ໄດ້ໃຊ້ງານ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_USING => 'ກຳລັງນຳໃຊ້',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_ALLOT => 'ກຳລັງໂອນຍ້າຍ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_REPAIRED => 'ສ້ອມແປງແລ້ວ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_REPAIRING => 'ກຳລັງສ້ອມແປງ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED => 'ລໍຖ້າລົບລ້າງ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_HANDLED => 'ທຳລາຍຮຽບຮ້ອຍແລ້ວ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_SCRAPPED => 'ເປັນໂມຂະແລ້ວ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_LOST => 'ສູນຫາຍ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_OUT_STORAGE => 'ກຳລັງອອກຈາກສາງ',
                'material_asset_status.' . MaterialEnums::ASSET_STATUS_OUT_STORAGE_REJECT => 'ປະຕິເສດອອກສາງ',
                //使用方向
                'material_asset_use.' . MaterialEnums::USE_PERSONAL => 'ນຳໃຊ້ສ່ວນໂຕ',
                'material_asset_use.' . MaterialEnums::USE_PUBLIC => 'ນຳໃຊ້ສ່ວນລວມ',
                //转交方式
                'asset_transfer_method.face' => 'ສົ່ງມອບຕໍ່ຫນ້າ',
                'asset_transfer_method.post' => 'ສົ່ງມອບທາງຂົນສົ່ງ',
            ]
        ];

        $language = strtolower(self::$language);
        if (in_array($language, ['vi', 'la']) && isset($transfer_data[$language][$text])) {
            return $transfer_data[$language][$text];
        }
        //普通翻译
        return static::$t->_($text);
    }
}
