<?php
namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\oa\MaterialAssetScmCallbackLogModel;
use App\Models\oa\MaterialAssetTransferLogBarcodeSummaryModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Material\Models\MaterialAssetSetModel;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Library\Enums\GlobalEnums;
use App\Modules\Material\Models\MaterialAssetTransferBatchModel;
use App\Modules\Material\Models\MaterialAssetTransferLogModel;
use App\Modules\Material\Models\MaterialAssetUpdateLogModel;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialCategoryModel;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Library\Validation\Validation;
use App\Modules\Material\Models\MaterialUpdateLogModel;
use App\Modules\Organization\Models\SysManagePieceModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Purchase\Models\Vendor;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Purchase\Services\validSignService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\StaffService;
use App\Library\Enums\MaterialAssetApplyEnums;
use App\Library\OssHelper;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialSauRepository;
use App\Modules\User\Services\UserService;

class AssetAccountService extends BaseService
{
    const MATERIAL_MAX_PIC = 5;//最多上传5张图片
    const MATERIAL_ASSET_SET_PREFIX = 'material_assets_set_';//物料属性数据缓存key
    const MATERIAL_ASSET_SET_TIME = 86400*30;//设置的列表显示字段或者导出字段数据缓存(缓存一个月)
    const MATERIAL_BATCH_SAVE_EXCEL_RESULT = 18;//批量导入-导入修改
    const MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT = 8;//批量导入-导入转移
    public static $hidden_fields = [];//隐藏字段
    private static $instance;
    /**
     * 单例
     * @return AssetAccountService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }
    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'category_id',
        'finance_category_id',
        'node_department_id',
        'state',
        'use',
        'asset_code',
        'real_arrival_date_begin',
        'real_arrival_date_end',
        'wait_leave_state_begin',
        'wait_leave_state_end',
        'company_id',
        'staff_id',
        'asset_code',
        'old_asset_code',
        'category_type',
        'receipted_at_start',
        'receipted_at_end',
        'use_land',
        'pageSize',
        'pageNum',
        'hire_type',
        'quality_status',
    ];

    public static $not_must_params_add = [
        'receipted_at',
        'quality_status'
    ];
    //资产台账-列表-搜索-参数验证
    public static $validate_list_search_material_asset = [
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
        'category_id' => 'IntGt:0', //物料分类表ID
        'finance_category_id' => 'IntGt:0', //财务分类表ID
        'asset_code' => 'Arr|ArrLenGeLe:0,100', //资产编码
        'asset_code[*]' => 'StrLenGeLe:1,100', //资产编码
        'old_asset_code[*]' =>'Arr|ArrLenGeLe:0,100',//旧资产编码
        'old_asset_code[*]' =>'StrLenGeLe:0,50',//旧资产编码
        'name' => 'StrLenGeLe:0,100',//资产名称
        'sn_code' => 'StrLenGeLe:0,50', //sn码
        'status'=>'Arr',//使用状态
        'status[*]'=>'IntGt:0|IntIn:'.MaterialEnums::ASSET_STATUS_VALIDATE,
        'node_department_id'=>'IntGt:0',//部门ID
        'sys_store_id' => 'Arr', //使用网点
        'sys_store_id[*]' =>'StrLenGeLe:1,10',
        'staff_id' =>'IntGt:0',//使用人
        'state'=>'Arr|ArrLenGe:0',//在职状态
        'state[*]'=>'IntGt:0',//在职状态
        'use' => 'IntIn:'.MaterialEnums::USE_VALIDATE,//使用方向
        'barcode' => 'Arr', //barcode
        'barcode[*]'=>'StrLenGeLe:1,30',
        'real_arrival_date_begin' => 'Date',//入库日期-起始
        'real_arrival_date_end' => 'Date',//入库日期-截止
        'wait_leave_state_begin' => 'Date',//离职日期-起始
        'wait_leave_state_end' => 'Date',//离职日期-截止
        'company_id' => 'IntGt:0',//所属公司
        'category_type' => 'IntIn:' . MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_VALIDATE,
        'receipted_at_start' => 'Date',//最新领用日期-起始
        'receipted_at_end' => 'Date',//最新领用日期-截止
        'use_land' => 'StrLenGeLe:4,100', //使用地-模糊
        'hire_type[*]' => 'IntGt:0',//雇佣类型
        'quality_status' => 'IntIn:' . MaterialEnums::QUALITY_STATUS_VALIDATE,//正品/残品
    ];

    //资产台账-列表显示字段设置
    public static $validate_set = [
        'value' => 'Required|ArrLenGe:1', //设置的字段值
    ];
    //资产台账-搜索使用人
    public static $validate_search_staff = [
        'name' =>'Required|StrLenGeLe:0,50', //工号或者名称
    ];
    //资产台账-搜索barcode
    public static $validate_search_barcode = [
        'name' => 'Required|StrLenGeLe:1,100', //barcode或者资产名称
    ];
    //资产台账-根据部门获取所属公司
    public static $validate_get_company = [
        'department_id'=>'Required|IntGt:0',//所属部门ID
    ];

    //资产台账-添加、编辑-共同参数验证
    public static $validate_material_asset = [
        'category_id' => 'Required|IntGt:0',//物料分类id
        'category_name' => 'Required|StrLenGeLe:1,100',//物料分类名称
        'category_code' => 'Required|StrLenGeLe:1,30',//物料分类编码
        'finance_category_id' => 'Required|IntGt:0',//财务分类id
        'finance_category_name' => 'Required|StrLenGeLe:1,100',//财务分类名称
        'finance_category_code' => 'Required|StrLenGeLe:1,30',//财务分类编码
        'sn_code'=>'StrLenGeLe:0,50',//sn编码
        'status' => 'Required|IntIn:'.MaterialEnums::ASSET_STATUS_VALIDATE,//使用状态
        'use' => 'Required|IntIn:'.MaterialEnums::USE_VALIDATE,//使用方向
        'quality_status' => 'IntIn:' . MaterialEnums::QUALITY_STATUS_VALIDATE,//正品/残品
        'old_asset_code'=>'StrLenGeLe:0,50',//旧资产编码

        'purchase_price' => 'Required|FloatGeLe:0.00,999999999999.99',//采购价（不含税）
        'currency' => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS,//币种
        'use_limit' => 'Required|IntGeLe:0,999',//使用期限，单位是月
        'net_value'=>'FloatGeLe:0.00,999999999999.99',//净值
        'unit_zh' => 'StrLenGeLe:0,20',//基本单位-中文
        'unit_en' => 'StrLenGeLe:0,20',//基本单位-英文
        'model' => 'StrLenGeLe:0,100',//规格型号
        'ownership' =>'Required|IntIn:'.MaterialEnums::OWNERSHIP_VALIDATE,//所有权
        'brand' => 'StrLenGeLe:0,30',//品牌
        'vendor_id'=>'StrLenGeLe:0,32',//供应商编码
        'vendor_name' => 'StrLenGeLe:0,128',//供应商名称

        'source_code'=>'Required|IntIn:'.MaterialEnums::SOURCE_CODE_VALIDATE,//来源方式
        'pc_code'=>'StrLenGeLe:0,255',//成本中心
        'use_land'=>'StrLenGeLe:0,100',//使用地

        'pono' =>'StrLenGeLe:0,20',//采购订单编号
        'psno' =>'StrLenGeLe:0,20',//入库单号
        'scm_no' =>'StrLenGeLe:0,20',//scm入库单号
        'sap_no' => 'StrLenGeLe:0,50',//SAP资产编码
        'purchase_lease_contract_no' => 'StrLenGeLe:0,50',//采购/租赁合同编号
        'maintenance_vendor'=>'StrLenGeLe:0,100',//维保供应商
        'mark'=>'StrLenGeLe:0,1000',//备注
        'company_id' => 'Required|IntGt:0',//所属公司ID
        'company_name' => 'Required|StrLenGeLe:1,50',//所属公司名称

        'attachments' => 'ArrLenGeLe:0,'.self::MATERIAL_MAX_PIC,//附件信息
        'receipted_at' => 'Date',//最新领用日期, 可编辑
    ];

    //资产台账-添加-参数验证
    public static $validate_material_asset_add = [
        'barcode' => 'Required|StrLenGeLe:1,30',//barcode
        'name_zh' => 'Required|StrLenGeLe:1,100',//中文名称
        'name_en' => 'Required|StrLenGeLe:1,100',//英文名称
        'name_local' => 'StrLenGeLe:0,100',//当地语言名称

        'staff_id'=>'IfIntIn:status,'.MaterialEnums::ASSET_STATUS_USING . ',' . MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE .'|Required|IntGt:0',//使用人工号,
        'staff_name'=>'StrLenGeLe:0,50',//员工姓名
        'state'=>'IntGe:0',//在职状态
        'wait_leave_state'=>'IntGe:0',//待离职状态
        'node_department_id'=>'IntGe:0',//所属部门ID
        'node_department_name'=>'StrLenGeLe:0,50',//所属部门名称
        'sys_store_id'=>'StrLenGeLe:0,10',//所属网点
        'store_name'=>'StrLenGeLe:0,50',//所属网点名称
        'job_id'=>'IntGe:0',//职位ID
        'job_name'=>'StrLenGeLe:0,255',//职位名称
    ];

    //资产台账-更新-参数验证
    public static $validate_material_asset_update = [
        'id'=>'Required|IntGt:0'
    ];

    //资产台账-转移-参数验证
    public static $validate_material_asset_transfer = [
        'staff_id' => 'Required|IntGt:0',//使用人工号,
        'staff_name' => 'Required|StrLenGeLe:0,50',//员工姓名
        'state' => 'Required|IntGe:0',//在职状态
        'wait_leave_state' => 'Required|IntGe:0',//待离职状态
        'node_department_id' => 'Required|IntGe:0',//所属部门ID
        'node_department_name' => 'Required|StrLenGeLe:0,50',//所属部门名称
        'sys_store_id' => 'Required|StrLenGeLe:0,10',//所属网点
        'store_name' => 'Required|StrLenGeLe:0,50',//所属网点名称
        'job_id' => 'Required|IntGe:0',//职位ID
        'job_name' => 'Required|StrLenGeLe:0,255',//职位名称
        'pc_code' => 'Required|StrLenGeLe:0,255',//成本中心
        'company_id' => 'Required|IntGt:0',//所属公司id
        'company_name' => 'Required|StrLenGeLe:0,255',//所属公司名称
        'use_land' => 'StrLenGeLe:0,255',//使用地
        'mark' => 'StrLenGeLe:0,1000',//备注
        'transfer_method' => 'Required|IntIn:' . MaterialEnums::TRANSFER_METHOD_VALIDATE,//转交方式
        'express_no' => 'IfIntEq:transfer_method,' . MaterialEnums::TRANSFER_METHOD_POST . '|Required|StrLenGeLe:0,50', //快递单号, 如果转交方式为"邮寄",必填
    ];

    //资产台账-批量转移-参数验证
    public static $validate_material_asset_batch_transfer = [
        'mark' => 'StrLenGeLe:0,1000',//备注
        'assets' => 'Required|Arr|ArrLenGeLe:1,100',
        'assets[*].asset_id' => 'Required|IntGe:1',
        'assets[*].staff_id' => 'Required|IntGt:0',//使用人工号,
        'assets[*].staff_name' => 'Required|StrLenGeLe:0,50',//员工姓名
        'assets[*].state' => 'Required|IntGe:0',//在职状态
        'assets[*].wait_leave_state' => 'Required|IntGe:0',//待离职状态
        'assets[*].node_department_id' => 'Required|IntGe:0',//所属部门ID
        'assets[*].node_department_name' => 'Required|StrLenGeLe:0,50',//所属部门名称
        'assets[*].sys_store_id' => 'Required|StrLenGeLe:0,10',//所属网点
        'assets[*].store_name' => 'Required|StrLenGeLe:0,50',//所属网点名称
        'assets[*].job_id' => 'Required|IntGe:0',//职位ID
        'assets[*].job_name' => 'Required|StrLenGeLe:0,255',//职位名称
        'assets[*].pc_code' => 'Required|StrLenGeLe:0,255',//成本中心
        'assets[*].company_id' => 'Required|IntGt:0',//所属公司id
        'assets[*].company_name' => 'Required|StrLenGeLe:0,255',//所属公司名称
        'assets[*].use_land' => 'StrLenGeLe:0,255',//使用地
        'assets[*].transfer_method' => 'Required|IntIn:' . MaterialEnums::TRANSFER_METHOD_VALIDATE,//转交方式
        'assets[*].express_no' => 'IfIntEq:transfer_method,' . MaterialEnums::TRANSFER_METHOD_POST . '|Required|StrLenGeLe:0,50', //快递单号, 如果转交方式为"邮寄",必填
    ];

    //资产台账-批量退库-参数验证
    public static $validate_material_asset_batch_stock_return = [
        'assets' => 'Required|Arr|ArrLenGeLe:1,100',
        'assets[*]' => 'Required|IntGe:1',
    ];

    //资产台账-导出协议-参数验证
    public static $validate_material_export_protocol = [
        'self_asset_ids' => 'Arr|ArrLenGeLe:0,1000',//明下资产
        'self_asset_ids[*]' => 'IntGt:0',//转移记录id集合
    ];

    //scm 修改sn -参数验证
    public static $validate_material_recall = [
        'sign'       => 'Required|StrLenGeLe:30,33',
        'scmNo'      => 'Required|StrLenGeLe:1,20',
        'snCode'     => 'Required|StrLenGeLe:1,50',//sn编码
        'timestamp'  => 'Required',
        'assetCode'  => 'Required|StrLenGeLe:1,100',//资产码
        'operateId'  => 'Required',
        'updateTime' => 'Required'
    ];

    /**
     * 资产台账默认配置项
     * @param array $user 当前登陆用户信息组
     * @return array
     */
    public function getOptionsDefault($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //物料分类-树状
            $category_enums['type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS;
            $data['category_map'] = ClassifyService::getInstance()->getClassifyArr($category_enums, 'search')['data'];
            //财务分类-树状
            $finance_category_enums['type'] = MaterialClassifyEnums::MATERIAL_FINANCE_CATEGORY_ENUMS;
            $data['finance_map'] = ClassifyService::getInstance()->getClassifyArr($finance_category_enums, 'search')['data'];
            //计量单位
            $data['attribute_value_map'] =ClassifyService::getInstance()->getOaUnit();
            //在职状态
            foreach (StaffInfoEnums::$staff_state as $key=>$value) {
                $data['staff_state'][] = ['value'=>(string)$key, 'label' => static::$t->_($value)];
            }
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            //获取用户设置的字段显示值
            $list_staff_set_columns_arr = [];
            $list_staff_set_columns = $this->getStaffSetValue($user['id']);
            if ($list_staff_set_columns) {
                $list_staff_set_columns_arr = explode(",", $list_staff_set_columns);
            }
            //查询列表显示字段枚举
            foreach (MaterialEnums::$list_set_columns as $value) {
                //只有马来展示 协议公司
                if(get_country_code() != GlobalEnums::MY_COUNTRY_CODE  && $value == 'contract_company_text'){
                    continue;
                }
                $data['list_set_columns'][] = [
                    'value'=>$value,
                    'label'=>static::$t->_('material_asset.'.$value),
                    'is_check' => $list_staff_set_columns_arr && in_array($value, $list_staff_set_columns_arr) ? true:false
                ];
            }
            //物料类型, 值集 1.资产 2.耗材
            foreach (MaterialClassifyEnums::$material_category_arr_type as $k => $v) {
                if (in_array($k, [MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS]))
                    $data['category_type'][] = [
                        'value' => (string)$k,
                        'label' => static::$t->_($v),
                    ];
            }
            //使用状态、资产来源、使用权、使用方向、正品/残品
            $enum = $this->getEnums();
            $data = array_merge($data,$enum);
            //雇佣类型
            $data['hire_type_item'] = EnumsService::getInstance()->getHireTypeEnum(true);

            //隐藏字段
            $data_permission = $this->getDataPermission($user['id']);
            $data['hidden_fields'] = $data_permission['hidden_fields'] ?? [];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取资产台账枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取使用状态、资产来源、使用权、使用方向, 转交方式, 物料类型
     * @return array
     */
    public function getEnums()
    {
        $data = [];
        $enums_arr = [
            'asset_status' => MaterialEnums::$asset_status,
            'asset_source' => MaterialEnums::$asset_source,
            'ownership' => MaterialEnums::$ownership,
            'use' => MaterialEnums::$use,
            'quality_status' => MaterialEnums::$quality_status,
            //转交方式
            'transfer_method' => MaterialEnums::$transfer_method,
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $v) {
                $data[$key][] = [
                    'value' => $k,
                    'label' => static::$t->_($v)
                ];
            }
        }
        return $data;
    }

    /**
     * 获取用户设置的列表、导出字段值
     * @param int $user_id 用户ID
     * @param int $type 1列表，2导出, 3数据转移-资产部业务数据查询
     * @return string
     */
    public function getStaffSetValue($user_id, $type= MaterialEnums::ASSET_SET_USE_LIST)
    {
        $redis = $this->getDI()->get("redis");
        $key = self::MATERIAL_ASSET_SET_PREFIX . $user_id.'_'.$type;
        $staff_set_value = $redis->get($key);
        if (empty($staff_set_value)) {
            //如果缓存过期了，或者不存在，看看数据库里有没有设置
            $list_set = $this->getStaffSetInfo($user_id, $type);
            if (!empty($list_set)) {
                $staff_set_value = $list_set->value;
                $redis->setex($key, self::MATERIAL_ASSET_SET_TIME, $staff_set_value);
            } else {
                //用户从未设置过，则返回默认的
                if ($type == MaterialEnums::ASSET_SET_USE_LIST) {
                    $staff_set_value = MaterialEnums::LIST_DEFAULT_SHOW_COLUMNS;
                } else if ($type == MaterialEnums::ASSET_SET_USE_EXPORT) {
                    $staff_set_value = MaterialEnums::EXPORT_DEFAULT_SHOW_COLUMNS;
                } else if ($type == MaterialEnums::ASSET_TRANSFER_SET_USE_LIST) {
                    $staff_set_value = MaterialEnums::ASSET_TRANSFER_LIST_DEFAULT_SHOW_COLUMNS;
                }
            }
        }
        return $staff_set_value;
    }

    /**
     * 获取用户设置的信息
     * @param int $user_id 用户ID
     * @param int $type 1列表，2导出
     * @return mixed
     */
    public function getStaffSetInfo($user_id, $type)
    {
        return MaterialAssetSetModel::findFirst([
            'conditions' => 'staff_id = :staff_id: and type=:type: and is_deleted=:is_deleted:',
            'bind' => ['staff_id'=>$user_id, 'type'=>$type, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 资产台账-列表显示字段设置
     * @param array $params 设置参数
     * @param array $user 当前登陆用户组
     * @param integer $type 1列表，2导出
     * @return array
     */
    public function setValue($params, $user, $type = MaterialEnums::ASSET_SET_USE_LIST)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        try {
            $list_set = $this->getStaffSetInfo($user['id'], $type);
            $value = implode(",", $params['value']);
            if(!empty($list_set)) {
                //当前用户存在设置，则更新
                $set = [
                    'value' => $value,
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ];
                $bool = $list_set->i_update($set);
            } else {
                //当前用户不存在设置，则添加
                $main_model = new MaterialAssetSetModel();
                $set = [
                    'staff_id'=>$user['id'],
                    'type'=>$type,
                    'value'=>$value,
                    'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO,
                    'created_at' => date('Y-m-d H:i:s', time()),
                    'updated_at' => date('Y-m-d H:i:s', time())
                ];
                $bool = $main_model->i_create($set);
            }
            if ($bool === false) {
                throw new BusinessException('资产台账列表显示字段设置失败 = ' . json_encode($set, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_SET_ERROR);
            } else {
                $redis = $this->getDI()->get("redis");
                $key = self::MATERIAL_ASSET_SET_PREFIX . $user['id'].'_'.$type;
                $redis->setex($key, self::MATERIAL_ASSET_SET_TIME, $value);
            }
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }  catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material_asset-list-set-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool
        ];
    }

    /**
     * 资产台账-列表
     * @param array $user 当前登陆者信息组
     * @param array $condition 设置参数
     * @param bool $export 是否导出，true导出、false非导出
     * @param integer $count 导出记录数
     * @return array
     */
    public function getList(array $user, array $condition, $export = false, $count = 0)
    {
        $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if ($export === false) {
                //列表查询总数，导出无需查询，在验证总导出数限制时已查询到，传递过来了
                $count = $this->getListCount($user, $condition, $export);
            }
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_id', 'staff');
                if ($export === false) {
                    //非导出
                    $columns = 'main.id,main.bar_code,main.status,main.name_zh,main.name_en,main.name_local,main.asset_code,main.model,main.category_name,main.finance_category_name,main.sn_code,main.staff_id,staff.state,staff.wait_leave_state,main.node_department_id, main.node_department_name,
                    main.sys_store_id,main.store_name,main.pc_code,main.use_land,main.company_id,main.company_name,main.job_name,
                    main.purchase_price,main.use,main.old_asset_code,main.real_arrival_date,staff.leave_date,staff.hire_type,staff.contract_company_id';
                } else {
                    //导出
                    $columns = 'main.bar_code, main.name_zh,main.name_en,main.name_local, main.asset_code, main.sn_code, main.category_id, main.category_code, main.category_name, main.finance_category_code, main.finance_category_name, main.status, main.use, main.old_asset_code, main.purchase_price, ';
                    $columns.= 'main.currency, main.use_limit, main.net_value, main.unit_zh, main.unit_en, main.model, main.source_code, main.ownership, main.brand, main.staff_id, staff.name as staff_name, main.job_name, staff.state, staff.contract_company_id, staff.wait_leave_state, main.node_department_id, main.node_department_name, main.sys_store_id, main.store_name, ';
                    $columns.= 'main.pc_code, main.use_land, main.real_arrival_date, main.pono, main.vendor_name, main.psno, main.scm_no, main.sap_no, main.mark, main.maintenance_vendor, main.maintenance_expire_date,main.company_name, main.category_type, main.receipted_at,staff.hire_type';
                }
                $builder->columns($columns);
                $builder->from(['main'=>MaterialAssetsModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($user, $builder, $condition, $export);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $export);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-material-asset-account-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     *
     * @param array $user 当前登陆者信息组
     * @param array $condition 筛选条件组
     * @param bool $export 是否导出，true导出、false非导出
     * @return int
     * @throws ValidationException
     */
    public function getListCount(array $user, array $condition, $export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main'=>MaterialAssetsModel::class]);
        $builder->columns("count('main.id') AS count");
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_info_id = main.staff_id', 'staff');
        //组合搜索条件
        $builder = $this->getCondition($user, $builder, $condition, $export);
        return $builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 资产数据管控组权限
     * @param integer $user_id 登陆者工号
     * @return array
     * @throws ValidationException
     */
    public function getDataPermission($user_id)
    {
        return MaterialSettingService::getInstance()->getStaffDataPermissionGroup($user_id, MaterialSettingService::DATA_PERMISSION_ASSET);
    }

    /**
     * 组装查询条件
     * @param array $user 当前登陆者信息组
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param bool $export 是否导出，true导出、false非导出
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition($user, $builder, $condition, $export = false)
    {
        //资产数据管控组-可看数据权限范围
        $data_permission = $this->getDataPermission($user['id']);
        self::$hidden_fields = $data_permission['hidden_fields'] ?? [];
        $category_id = !empty($condition['category_id']) ? $condition['category_id'] : 0; //物料分类表ID
        $finance_category_id = !empty($condition['finance_category_id']) ? $condition['finance_category_id'] : 0;//财务分类表ID
        $asset_code = $condition['asset_code'] ?? [];//资产编码
        $old_asset_code = $condition['old_asset_code'] ?? [];//旧资产编码
        $name = !empty($condition['name']) ? trim($condition['name']) : '';//资产名称
        $sn_code = !empty($condition['sn_code']) ? trim($condition['sn_code']) : '';//sn码
        $status = !empty($condition['status']) ? $condition['status'] : [];//使用状态
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//使用部门
        $sys_store_id = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : [];//使用网点
        $staff_id = !empty($condition['staff_id']) ? trim($condition['staff_id']) : '';//员工工号
        $state = $condition['state'] ?? [];//在职状态
        $use = !empty($condition['use']) ? $condition['use'] : 0;//使用方向
        $barcode = !empty($condition['barcode']) ? $condition['barcode'] : [];//barcode
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司
        $category_type = !empty($condition['category_type']) ? $condition['category_type'] : 0;//物料类型
        $receipted_at_start = !empty($condition['receipted_at_start']) ? $condition['receipted_at_start'] : '';//最新领用日期-开始
        $receipted_at_end = !empty($condition['receipted_at_end']) ? $condition['receipted_at_end'] : '';//最新领用日期-结束
        $use_land = !empty($condition['use_land']) ? $condition['use_land'] : '';//使用地
        $hire_type = !empty($condition['hire_type']) ? $condition['hire_type'] : [];//雇佣类型
        $quality_status = !empty($condition['quality_status']) ? $condition['quality_status'] : 0;//正品/残品
        $pono = !empty($condition['pono']) ? $condition['pono'] : '';//采购订单编号
        $psno = !empty($condition['psno']) ? $condition['psno'] : '';//入库单号
        $scm_no = !empty($condition['scm_no']) ? $condition['scm_no'] : '';//scm入库单号

        //入库时间起始与结束校验
        $arrival_start = !empty($condition['real_arrival_date_begin']) ? strtotime($condition['real_arrival_date_begin']) : '';
        $arrival_end = !empty($condition['real_arrival_date_end']) ? strtotime($condition['real_arrival_date_end']) : '';
        //离职日期起始与结束校验
        $leave_start = !empty($condition['wait_leave_state_begin']) ? strtotime($condition['wait_leave_state_begin']) : '';
        $leave_end = !empty($condition['wait_leave_state_end']) ? strtotime($condition['wait_leave_state_end']) : '';
        if ($arrival_start > $arrival_end || $leave_start > $leave_end) {
            throw new ValidationException(self::$t->_('material_asset_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }

        //入库日期一次性最多筛选一年内的数据(仅查询限制)
        if ($arrival_start && $arrival_end && $export === false) {
            $days = ($arrival_end - $arrival_start) / 86400;
            if ($days > 365) {
                throw new ValidationException(self::$t->_('material_asset_list_date_month_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        $locale = static::$language;
        $builder->where('main.is_deleted = :is_deleted: and main.status != :status:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO,'status'=>MaterialEnums::ASSET_STATUS_DRAFT]);

        //资产数据管控组-可看数据权限范围 - 部门
        if (!empty($data_permission['node_department_id'])) {
            $builder->inWhere('main.node_department_id', $data_permission['node_department_id']);
        }

        //资产数据管控组-可看数据权限范围 - 物料分类
        if (!empty($data_permission['material_category_ids'])) {
            $builder->inWhere('main.category_id', $data_permission['material_category_ids']);
        }

        if (!empty($category_id)) {
            //物料分类筛选
            $category_ids = (new MaterialCategoryModel())->getSonList($category_id,'id');
            $builder->inWhere('main.category_id', array_values(array_merge($category_ids['data'], [$category_id])));
        }
        if (!empty($finance_category_id)) {
            //财务分类筛选
            $finance_category_ids = (new MaterialFinanceCategoryModel())->getSonList($finance_category_id,'id');
            $builder->inWhere('main.finance_category_id', array_values(array_merge($finance_category_ids['data'], [$finance_category_id])));
        }
        if (!empty($asset_code)) {
            //资产编码
            $builder->inWhere('main.asset_code', $asset_code);
        }
        if (!empty($old_asset_code)) {
            //旧资产编码
            $builder->inWhere('main.old_asset_code', $old_asset_code);
        }
        if (!empty($name)) {
            //资产名称搜索
            $builder->andWhere('main.name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local').' like :name:', ['name' => '%' . $name . '%']);
        }
        if (!empty($sn_code)) {
            //sn码搜索
            $builder->andWhere('main.sn_code like :sn_code:', ['sn_code' => '%' . $sn_code . '%']);
        }
        if (!empty($status)) {
            //使用状态搜索
            $builder->inWhere('main.status', $status);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('main.node_department_id', $department_ids);
        }
        if (!empty($sys_store_id)) {
            //按照使用网点搜索
            $builder->andWhere('main.sys_store_id in ({sys_store_ids:array})', ['sys_store_ids' => $sys_store_id]);
        }
        if (!empty($staff_id)) {
            //使用人员工工号
            $builder->andWhere('main.staff_id =:staff_id:', ['staff_id'=>$staff_id]);
        }
        //在职状态
        if (!empty($state)) {
            //在职状态搜索筛选待离职
            $state_where = '';
            $state_condition = [];
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $state)) {
                //存在待离职检索
                $state_where = 'staff.wait_leave_state = :wait_leave_state: and staff.state = :state:';
                $state_condition['wait_leave_state'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES;
                $state_condition['state'] = StaffInfoEnums::STAFF_STATE_IN;
                //从在职状态组中移除
                $state = array_diff($state, [StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
            }
            if (!empty($state)) {
                //在职、离职、停职
                $state_where .= $state_where ? ' OR (staff.state in ({states:array}) and staff.wait_leave_state = :leave_state:)' : 'staff.state in ({states:array}) and staff.wait_leave_state = :leave_state:';
                $state_condition['states'] = array_values($state);
                $state_condition['leave_state'] = StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO;
            }
            $builder->andWhere($state_where, $state_condition);
        }
        if (!empty($use)) {
            //使用方向
            $builder->andWhere('main.use = :use:', ['use'=>$use]);
        }
        if (!empty($barcode)) {
            //barcode
            $builder->inWhere('main.bar_code', $barcode);
        }
        if (!empty($company_id)) {
            //所属公司
            $builder->andWhere('main.company_id = :company_id:', ['company_id'=>$company_id]);
        }
        if (!empty($arrival_start)) {
            //入库日期-起始
            $builder->andWhere('main.real_arrival_date >= :arrival_start:', ['arrival_start'=>date('Y-m-d 00:00:00',$arrival_start)]);
        }
        if (!empty($arrival_end)) {
            //入库日期-截止
            $builder->andWhere('main.real_arrival_date <= :arrival_end:', ['arrival_end'=>date('Y-m-d 23:59:59',$arrival_end)]);
        }
        if (!empty($leave_start)) {
            //离职日期-起始
            $builder->andWhere('staff.leave_date >= :leave_start:', ['leave_start'=>date('Y-m-d 00:00:00',$leave_start)]);
        }
        if (!empty($leave_end)) {
            //离职日期-截止
            $builder->andWhere('staff.leave_date <= :leave_end:', ['leave_end'=>date('Y-m-d 23:59:59',$leave_end)]);
        }

        if (!empty($condition['contract_company_id'])) {
            //协议所属公司
            $builder->inWhere('staff.contract_company_id', $condition['contract_company_id']);
        }

        if (!empty($category_type)) {
            //物料类型
            $builder->andWhere('main.category_type = :category_type:', ['category_type' => $category_type]);
        }
        if (!empty($receipted_at_start)) {
            //最新领用日期-开始
            $builder->andWhere('main.receipted_at >= :receipted_at_start:', ['receipted_at_start' => $receipted_at_start . ' 00:00:00']);
        }
        if (!empty($receipted_at_end)) {
            //最新领用日期-截止
            $builder->andWhere('main.receipted_at <= :receipted_at_end:', ['receipted_at_end' => $receipted_at_end . ' 23:59:59']);
        }
        if (!empty($use_land)) {
            $builder->andWhere('main.use_land like :use_land:', ['use_land' => $use_land . '%']);
        }
        if (!empty($hire_type)) {
            //雇佣类型搜索
            $builder->inWhere('staff.hire_type', array_values($hire_type));
        }
        if (!empty($quality_status)) {
            //正品/残品
            $builder->andWhere('main.quality_status = :quality_status:', ['quality_status' => $quality_status]);
        }
        if (!empty($pono)) {
            //采购订单编号
            $builder->andWhere('main.pono = :pono:', ['pono' => $pono]);
        }
        if (!empty($psno)) {
            //入库单号
            $builder->andWhere('main.psno = :psno:', ['psno' => $psno]);
        }
        if (!empty($scm_no)) {
            //scm入库单号
            $builder->andWhere('main.scm_no = :scm_no:', ['scm_no' => $scm_no]);
        }

        return $builder;
    }

    /**
     * 格式化资产台账列表
     *
     * @param array $items 台账列表
     * @param bool $export 导出
     * @return array
     */
    private function handleListItems(array $items, bool $export)
    {
        if (empty($items)) {
            return [];
        }
        $locale = static::$language;
        //雇佣类型枚举
        $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
        $company_list = EnumsService::getInstance()->getPayrollCompanyInfo();
        if ($export === true) {
            //导出
            $row_value = [];//组装导出需要的数据
            //网点需要找到片区、大区
            $sys_store_id_arr = array_values(array_unique(array_filter(array_column($items, 'sys_store_id'), function ($val) {
                if ($val == -1 || empty($val)) {
                    return false;
                } else {
                    return true;
                }
            })));
            [$sys_store, $region_list, $piece_list] = $this->handleSysStoreRegionAndPiece($sys_store_id_arr);
            //批量找一级物料分类
            $all_category_id = array_values(array_unique(array_column($items, 'category_id')));
            $category_code_parent = [];
            if (!empty($all_category_id)) {
                $category_code_parent = ClassifyService::getInstance()->getCategoryParents(['id' => $all_category_id]);
            }

            foreach ($items as $item) {
                foreach ($item as $field => $v) {
                    if (in_array($field, self::$hidden_fields)) {
                        $item[$field] = static::$t->_('data_permission_no');
                    }
                }
                $name = $item['name_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'local')];
                $name = empty($name) ? $item['name_en'] : $name;
                $row  = [
                    'barcode' => $item['bar_code'],
                    'name' => $name,
                    'asset_code' => $item['asset_code'],
                    'sn_code' => $item['sn_code'],
                    'category_type' => static::$t[MaterialClassifyEnums::$material_category_arr_type[$item['category_type']]],
                    'parent_category_code' => $category_code_parent[$item['category_id']]['code'] ?? '',
                    'parent_category_name' => $category_code_parent[$item['category_id']]['name'] ?? '',
                    'category_code' => $item['category_code'],
                    'category_name' => $item['category_name'],
                    'finance_category_code' => $item['finance_category_code'],
                    'finance_category_name' => $item['finance_category_name'],
                    'status' => static::$t[MaterialEnums::$asset_status[$item['status']]],
                    'use' => static::$t[MaterialEnums::$use[$item['use']]],
                    'old_asset_code' => $item['old_asset_code'],
                    'purchase_price' => $item['purchase_price'],
                    'currency' => $item['currency']? static::$t[GlobalEnums::$currency_item[$item['currency']]] : '',
                    'use_limit' => $item['use_limit'],
                    'net_value' => $item['net_value'],
                    'unit' => $item['unit_'.(MaterialClassifyEnums::$language_fields[$locale] ?? 'en')],
                    'model' => $item['model'],
                    'source_code' => is_numeric($item['source_code']) ? static::$t[MaterialEnums::$asset_source[$item['source_code']]] : $item['source_code'],
                    'ownership' => is_numeric($item['ownership']) ? static::$t[MaterialEnums::$ownership[$item['ownership']]] : $item['ownership'],
                    'brand' => $item['brand'],
                    'company_name'=>$item['company_name'],
                    'staff_id' => $item['staff_id'],
                    'staff_name' => $item['staff_name'],
                    'job_name' => $item['job_name'],
                    'state' => $item['state'] ? static::$t[StaffInfoEnums::$staff_state[($item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['state']]] :'',
                    'hire_type_text' => $hire_type_enum[$item['hire_type']] ?? '',//雇佣类型
                    'node_department_id' => $item['node_department_id'],
                    'node_department_name' => $item['node_department_name'],
                    'sys_store_id' => $item['sys_store_id'],
                    'store_name' => $item['store_name'],
                    'district' => !empty($sys_store[$item['sys_store_id']]) && !empty($piece_list[$sys_store[$item['sys_store_id']]['manage_piece']]) ? $piece_list[$sys_store[$item['sys_store_id']]['manage_piece']]['name'] : '',
                    'area' =>  !empty($sys_store[$item['sys_store_id']]) && !empty($region_list[$sys_store[$item['sys_store_id']]['manage_region']]) ? $region_list[$sys_store[$item['sys_store_id']]['manage_region']]['name'] : '',
                    'pc_code' => $item['pc_code'],
                    'use_land' => $item['use_land'],
                    'real_arrival_date' => $item['real_arrival_date'] ? substr($item['real_arrival_date'], 0, 10): '',
                    'pono' => $item['pono'],
                    'vendor_name' => $item['vendor_name'],
                    'psno' => $item['psno'],
                    'scm_no' => $item['scm_no'],
                    'sap_no' => $item['sap_no'],
                    'mark' => $item['mark'],
                    'maintenance_vendor' => $item['maintenance_vendor'],
                    'maintenance_expire_date' => $item['maintenance_expire_date'],
                    'receipted_at' => !empty($item['receipted_at']) ? date('Y-m-d', strtotime($item['receipted_at'])) : '',
                ];
                if(get_country_code() == GlobalEnums::MY_COUNTRY_CODE ){
                    $row['contract_company_text'] = $company_list[$item['contract_company_id']] ?? '';
                }
                $row_value[] = $row;
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['status_text'] = static::$t[MaterialEnums::$asset_status[$item['status']]];
                $state = ($item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $item['state'];
                $item['state_text'] = isset(StaffInfoEnums::$staff_state[$state]) ? static::$t[StaffInfoEnums::$staff_state[$state]] : '';
                $item['use_text'] = static::$t[MaterialEnums::$use[$item['use']]];
                $item['real_arrival_date'] = $item['real_arrival_date'] ? substr($item['real_arrival_date'], 0, 10): '';
                $item['leave_date'] = $item['leave_date'] ? substr($item['leave_date'], 0, 10): '';
                //雇佣类型
                $item['hire_type_text'] = $hire_type_enum[$item['hire_type']] ?? '';
                $item['contract_company_text'] = $company_list[$item['contract_company_id']] ?? '';
            }
        }
        return $items;
    }

    /**
     * 获取特定网点所在大区、片区的大区、片区信息
     * @param array $sys_store_id_arr 网点编码组
     * @return array
     */
    private function handleSysStoreRegionAndPiece($sys_store_id_arr)
    {
        $sys_store = $region_list = $piece_list = [];
        if (!empty($sys_store_id_arr)) {
            $list = SysStoreModel::find([
                'columns' => 'id,manage_region,manage_piece',
                'conditions' => 'state = :state: and id in({ids:array})',
                'bind' => ['state' => 1, 'ids'=>$sys_store_id_arr]
            ])->toArray();
            if (!empty($list)) {
                $region_id_arr = array_values(array_unique(array_filter(array_column($list, 'manage_region'))));
                if (!empty($region_id_arr)) {
                    //获取大区信息
                    $region_list = SysManageRegionModel::find([
                        'columns' => 'id,name',
                        'conditions' => 'deleted = :deleted: and id in({ids:array})',
                        'bind' => ['deleted' => 0, 'ids'=>$region_id_arr]
                    ])->toArray();
                    $region_list = array_column($region_list, null, 'id');
                }
                $piece_id_arr = array_values(array_unique(array_filter(array_column($list, 'manage_piece'))));
                if (!empty($piece_id_arr)) {
                    //获取片区信息
                    $piece_list = SysManagePieceModel::find([
                        'columns' => 'id,name',
                        'conditions' => 'deleted = :deleted: and id in({ids:array})',
                        'bind' => ['deleted' => 0, 'ids'=>$piece_id_arr]
                    ])->toArray();
                    $piece_list = array_column($piece_list, null, 'id');
                }
                $sys_store = array_column($list, null, 'id');
            }
        }
        return [$sys_store, $region_list, $piece_list];
    }

    /**
     * 资产台账-导出 -废弃
     * @param string $locale 语言
     * @param array $condition 设置参数
     * @return array
     */
    public function export($locale, $condition)
    {
        try{
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $count = $this->getListCount($locale, $condition);
            if ($count > MaterialEnums::MATERIAL_ASSET_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>MaterialEnums::MATERIAL_ASSET_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $page_size = 2000;
            $step = ceil($count/$page_size);
            $row_values = [];
            for ($i=1; $i<=$step; $i++) {
                $condition['pageNum'] = $i;
                $condition['pageSize'] = $page_size;
                $list = $this->getList($locale, $condition, true, $count);
                $rows = $list['data']['items'];
                $row_values = array_merge($row_values, $rows);
            }
            $row_values = array_map('array_values',$row_values);
            $file_name = "material_asset_" . date("YmdHis");
            $header = $this->getExportExcelHeaderFields();
            $result            = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-material-asset-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 获取台账导出的表头
     */
    public function getExportExcelHeaderFields()
    {
        $return =  [
            static::$t->_('material_asset.barcode'),//barcode
            static::$t->_('material_asset.name'),//资产名称
            static::$t->_('material_asset.asset_code'),//资产编码
            static::$t->_('material_asset.sn_code'),//sn码
            static::$t->_('material_asset.category_type'),//物料类型
            static::$t->_('material_asset.parent_category_code'),//一级物料分类编码
            static::$t->_('material_asset.parent_category_name'),//一级物料分类名称
            static::$t->_('material_asset.category_code'),//物料分类编码
            static::$t->_('material_asset.category_name'),//物料分类名称
            static::$t->_('material_asset.finance_category_code'),//财务分类编码
            static::$t->_('material_asset.finance_category_name'),//财务分类名称
            static::$t->_('material_asset.status'),//使用状态
            static::$t->_('material_asset.use'),//使用方向
            static::$t->_('material_asset.old_asset_code'),//旧资产编码
            static::$t->_('material_asset.purchase_price'),//采购价（不含税）
            static::$t->_('material_asset.currency'),//币种
            static::$t->_('material_asset.use_limit'),//折旧期限
            static::$t->_('material_asset.net_value'),//净值
            static::$t->_('material_asset.unit'),//单位
            static::$t->_('material_asset.model'),//规格型号
            static::$t->_('material_asset.source_code'),//来源方式
            static::$t->_('material_asset.ownership'),//所有权
            static::$t->_('material_asset.brand'),//品牌
            static::$t->_('material_asset.company_name'),//所属公司
            static::$t->_('material_asset.staff_id'),//使用人工号
            static::$t->_('material_asset.staff_name'),//使用人姓名
            static::$t->_('material_asset.job_name'),//职位
            static::$t->_('material_asset.state'),//在职状态
            static::$t->_('material_asset.hire_type'),//雇佣类型
            static::$t->_('material_asset.node_department_id'),//使用部门ID
            static::$t->_('material_asset.node_department_name'),//使用部门名称
            static::$t->_('material_asset.sys_store_id'),//使用网点编码
            static::$t->_('material_asset.store_name'),//使用网点名称
            static::$t->_('material_asset.district'),//所属片区
            static::$t->_('material_asset.area'),//所属大区
            static::$t->_('material_asset.pc_code'),//成本中心
            static::$t->_('material_asset.use_land'),//使用地信息
            static::$t->_('material_asset.real_arrival_date'),//入库日期
            static::$t->_('material_asset.pono'),//PO单号
            static::$t->_('material_asset.vendor_name'),//供应商
            static::$t->_('material_asset.psno'),//OA入库单号
            static::$t->_('material_asset.scm_no'),//SCM入库单号
            static::$t->_('material_asset.sap_no'),//SAP资产编码
            static::$t->_('material_asset.mark'),//备注
            static::$t->_('material_asset.maintenance_vendor'),//维保供应商
            static::$t->_('material_asset.maintenance_expire_date'),//维保到期日
            static::$t->_('material_asset.receipted_at'),//最新领用日期
        ];
        if(get_country_code() == GlobalEnums::MY_COUNTRY_CODE ){
            $return[] = static::$t->_('material_asset.contract_company_text');//使用人的协议签署公司
        }
        return $return;
    }

    /**
     * 资产台账-导出svc文件 -废弃
     * @param string $locale 语言
     * @param array $condition 设置参数
     * @param array $user 当前登陆用户信息
     * @return array
     */
    public function exportSvc($locale, $condition, $user)
    {
        try {
            $count = $this->getListCount($locale, $condition, true);
            if ($count > MaterialEnums::MATERIAL_ASSET_EXPORT_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>MaterialEnums::MATERIAL_ASSET_EXPORT_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }
            $file_name = "material_asset_" . date("YmdHis");
            //设置好告诉浏览器要下载excel文件的headers
            header('Content-Encoding:UTF-8');
            header('Content-Type: application/vnd.ms-excel;charset=UTF-8');
            header('Content-Disposition: attachment; filename="'. $file_name .'.csv"');
            //跨域设置
            header('Access-Control-Allow-Origin:*' );
            // 响应类型
            header('Access-Control-Allow-Methods:POST,GET');
            // 带 cookie 的跨域访问
            header('Access-Control-Allow-Credentials: true');
            // 响应头设置
            header('Access-Control-Allow-Headers:x-requested-with,Content-Type,Authorization,Content-Disposition');
            //打开php标准输出流
            $fp = fopen('php://output', 'a');
            //添加BOM头，以UTF8编码导出CSV文件，如果文件头未添加BOM头，打开会出现乱码。
            fwrite($fp, chr(0xEF).chr(0xBB).chr(0xBF));

            $header = $this->getExportExcelHeaderFields();

            fputcsv($fp,$header);
            if ($count > 0) {
                $page_size = 2000;
                $step = ceil($count/$page_size);
                for ($i=1; $i<=$step; $i++) {
                    $condition['pageNum'] = $i;
                    $condition['pageSize'] = $page_size;
                    $list = $this->getList($locale, $condition,true, $count);
                    $row_values = $list['data']['items'];
                    foreach ($row_values as $item) {
                        fputcsv($fp, $item);
                    }
                    if (ob_get_level() > 0) {
                        ob_flush();
                    }
                    flush();//必须同时使用 ob_flush() 和flush() 函数来刷新输出缓冲。
                }
            }
            fclose($fp);
            $this->unLock( md5(MaterialEnums::MATERIAL_ASSET_EXPORT_LOCK . '_' . $user['id']));
            exit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        }  catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('download-material-asset-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code ,
            'message' => $message,
            'data'    => '',
        ];
    }

    /**
     * 资产台账-查看
     * @param array $params 请求入参
     * @return array
     */
    public function detail($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $asset = $this->getMaterialAssetInfoById($params['id']);
            $detail = $asset->toArray();
            $detail['quality_status'] = empty($detail['quality_status']) ? '' : $detail['quality_status'];
            $detail['barcode'] = $detail['bar_code'];
            $detail['scrap_date'] = empty($detail['scrap_date']) ? '' : $detail['scrap_date'];
            unset($detail['bar_code'], $detail['is_deleted'], $detail['created_at'], $detail['updated_at']);
            //最新领用日期
            $detail['receipted_at'] = !empty($detail['receipted_at']) ? date('Y-m-d', strtotime($detail['receipted_at'])) : '';
            //物料分类翻译
            $detail['category_type_text'] = static::$t[MaterialClassifyEnums::$material_category_arr_type[$detail['category_type']]];
            $detail['attachments'] = $asset->getPicAttachment()->toArray();

            //雇佣类型枚举
            $detail['hire_type'] = '';
            $detail['hire_type_text'] = '';
            if ($detail['staff_id']) {
               $staff_info = (new HrStaffRepository())->getStaffById($detail['staff_id']);
               $hire_type_enum = EnumsService::getInstance()->getHireTypeEnum();
               $detail['hire_type'] = $staff_info['hire_type'];
               $detail['hire_type_text'] = $hire_type_enum[$staff_info['hire_type']] ?? '';
               $detail['state'] = $staff_info['state'];
               $detail['wait_leave_state'] = $staff_info['wait_leave_state'];
               //协议签署公司
               $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();
               $detail['contract_company'] = $companyList[$staff_info['contract_company_id']] ?? '';
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-detail failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail
        ];
    }

    /**
     * 资产台账-根据部门获取所属公司
     * @param array $params['department_id'=>'部门ID']
     * @return array
     */
    public function getCompanyByDepartment($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $department = DepartmentModel::findFirst([
                "conditions" => "id = :id: and deleted = 0",
                "bind" => [
                    "id" => $params['department_id'],
                ]
            ]);
            //根据费用所属部门查询对应的COO/CEO下的BU级部门
            $data['company_id'] = '';
            $data['company_name'] = '';
            $data['department_name'] = '';
            $data['department_id'] = 0;
            if (!empty($department)) {
                $company_list = (new PurchaseService())->getCooCostCompany();
                $cost_company_kv = array_column($company_list,'cost_company_name','cost_company_id');
                if(key_exists($department->company_id, $cost_company_kv)) {
                    $data['company_id'] = $department->company_id;
                    $data['company_name'] = $cost_company_kv[$department->company_id];
                }
                $data['department_name'] = $department->name;
                $data['department_id'] = $params['department_id'];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('根据部门获取所属公司异常: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 根据台账ID获取资产台账信息
     * @param int $id 资产台账ID
     * @return mixed
     * @throws ValidationException
     */
    public function getMaterialAssetInfoById($id)
    {
        $asset = MaterialAssetsModel::findFirst([
            'conditions'=>'id=:id: and is_deleted=:is_deleted:',
            'bind'=>['id'=>$id, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
        if (empty($asset)) {
            throw new ValidationException(self::$t['material_asset_not_found'], ErrCode::$VALIDATE_ERROR);
        }
        return $asset;
    }

    /**
     * 根据资产编码获取资产台账信息
     * @param string $asset_code 资产编码
     * @return mixed
     */
    public function getMaterialAssetInfoByAssetCode($asset_code)
    {
        return MaterialAssetsModel::findFirst([
            'conditions'=>'asset_code=:asset_code: and is_deleted=:is_deleted:',
            'bind'=>['asset_code'=>$asset_code, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 根据资产编码组获取资产台账信息组
     * @param array $asset_code 资产编码
     * @return mixed
     */
    public function getMaterialAssetListByAssetCodes($asset_code)
    {
        return MaterialAssetsModel::find([
            'columns' => 'asset_code, staff_id, staff_name',
            'conditions'=>'asset_code in ({asset_codes:array}) and is_deleted = :is_deleted:',
            'bind'=> ['asset_codes' => $asset_code, 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
        ])->toArray();
    }

    /**
     * 检测sn码编码是否已存在
     * @param string $sn_code sn码
     * @return mixed
     */
    public function checkSnCode($sn_code)
    {
        return MaterialAssetsModel::findFirst([
            'conditions' => 'sn_code = :sn_code: and is_deleted=:is_deleted:',
            'columns' => 'id',
            'bind' => ['sn_code' => $sn_code, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 检测旧资产编码是否已存在
     * @param string $old_asset_code 旧资产编码
     * @return mixed
     */
    public function checkOldAssetCode($old_asset_code)
    {
        return MaterialAssetsModel::findFirst([
            'conditions' => 'old_asset_code = :old_asset_code: and is_deleted=:is_deleted:',
            'columns' => 'id',
            'bind' => ['old_asset_code' => $old_asset_code, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 检测SAP资产编码是否已存在
     * @param string $sap_no SAP资产编码
     * @return mixed
     */
    public function checkSapNo($sap_no)
    {
        return MaterialAssetsModel::findFirst([
            'conditions' => 'sap_no = :sap_no: and is_deleted=:is_deleted:',
            'columns' => 'id',
            'bind' => ['sap_no' => $sap_no, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
        ]);
    }

    /**
     * 其它额外参数验证
     * @param array $data 请求入参
     * @param array $asset 资产台账信息组
     * @return mixed
     * @throws ValidationException
     */
    public function extendValidation($data, $asset = [])
    {
        if (isset($data['sn_code']) && !empty(trim($data['sn_code']))) {
            $data['sn_code'] = trim($data['sn_code']);
            //sn编码唯一性校验
            $exists_code = $this->checkSnCode($data['sn_code']);
            //添加或者编辑判断sn码
            if(!empty($exists_code) && (empty($asset) || ($asset && $data['sn_code'] != $asset['sn_code']))) {
                throw new ValidationException(static::$t->_('material_asset_sn_code_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        if (isset($data['old_asset_code']) && !empty(trim($data['old_asset_code']))) {
            $data['old_asset_code'] = trim($data['old_asset_code']);
            //旧资产编码唯一性校验
            $exists_code = $this->checkOldAssetCode($data['old_asset_code']);
            //添加或者编辑判断旧资产编码
            if(!empty($exists_code) && (empty($asset) || ($asset && $data['old_asset_code'] != $asset['old_asset_code']))) {
                throw new ValidationException(static::$t->_('material_asset_old_code_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        //所选物料分类是否是最末级分类
        $category_exists = (new MaterialCategoryModel)->isHasChildrenUsing($data['category_id']);
        if (!empty($category_exists)) {
            throw new ValidationException(static::$t->_('material_sau_category_not_last'), ErrCode::$VALIDATE_ERROR);
        }
        //所选财务分类是否是最末级分类
        $finance_exists = (new MaterialFinanceCategoryModel())->isHasChildrenUsing($data['finance_category_id']);
        if (!empty($finance_exists)) {
            throw new ValidationException(static::$t->_('material_sau_category_not_last'), ErrCode::$VALIDATE_ERROR);
        }

        //采购价（不含税）
        if (!preg_match(MaterialEnums::PRICE_RULE, $data['purchase_price'])) {
            throw new ValidationException(static::$t->_('material_asset_price_error'), ErrCode::$VALIDATE_ERROR);
        }
        //净值
        if (isset($data['net_value']) && !empty($data['net_value']) && !preg_match(MaterialEnums::PRICE_RULE, $data['net_value'])) {
            throw new ValidationException(static::$t->_('material_asset_price_error'), ErrCode::$VALIDATE_ERROR);
        } else if (bccomp($data['purchase_price'], $data['net_value'], 2) === -1) {
            throw new ValidationException(static::$t->_('material_asset_net_value_error'), ErrCode::$VALIDATE_ERROR);
        }
        //入库日期(添加或编辑采购购入方式非采购入库通知单的才做判读)
        $today =  strtotime(date('Y-m-d',time()));
        if (isset($data['real_arrival_date']) && !empty($data['real_arrival_date'])) {
            Validation::validate($data, ['real_arrival_date'=>'Date']);
            if (strtotime($data['real_arrival_date']) > $today  && (empty($asset) || ($asset && $asset['source_type'] != MaterialEnums::SOURCE_TYPE_PURCHASE && $asset['real_arrival_date'] != $data['real_arrival_date']))) {
                throw new ValidationException(static::$t->_('material_asset_arrival_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            $data['real_arrival_date'] .= ' 00:00:00';
        } else {
            $data['real_arrival_date'] = null;
        }

        //维保到期日
        if (isset($data['maintenance_expire_date']) && !empty($data['maintenance_expire_date'])) {
            Validation::validate($data, ['maintenance_expire_date'=>'Date']);
            if (strtotime($data['maintenance_expire_date']) <= $today && (empty($asset) || ($asset && $asset['maintenance_expire_date'] != $data['maintenance_expire_date']))) {
                throw new ValidationException(static::$t->_('material_asset_maintenance_expire_date_error'), ErrCode::$VALIDATE_ERROR);
            }
        } else {
            $data['maintenance_expire_date'] = null;
        }
        //离职日期处理,仅添加判断
        if (isset($data['leave_date']) && !empty($data['leave_date']) && empty($asset)) {
            Validation::validate($data, ['leave_date'=>'DateTime']);
        } else {
            $data['leave_date'] = null;
        }
        if (isset($data['sap_no']) && !empty(trim($data['sap_no']))) {
            $data['sap_no'] = trim($data['sap_no']);
            //SAP资产编码唯一性校验
            $exists_code = $this->checkSapNo($data['sap_no']);
            //添加或者编辑判断旧资产编码
            if(!empty($exists_code) && (empty($asset) || ($asset && $data['sap_no'] != $asset['sap_no']))) {
                throw new ValidationException(static::$t->_('material_asset_sap_no_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        //作废日期
        if (isset($data['scrap_date']) && !empty($data['scrap_date'])) {
            Validation::validate($data, ['scrap_date'=>'Date']);
        } else {
            $data['scrap_date'] = null;
        }
        //供应商状态验证 只有添加时验证
        //编辑历史数据时可能供应商是作废的,但只编辑了其他字段,也允许编辑成功,所以不验证;如果编辑供应商,前端搜索供应商的时候只返回符合条件的供应商
        if (empty($asset) && !empty($data['vendor_id'])) {
            $vendor_info = Vendor::findFirst([
                'columns' => 'vendor_id, grade_status',
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => [
                    'vendor_id' => $data['vendor_id']
                ]
            ]);
            if (empty($vendor_info)) {
                throw new ValidationException(static::$t->_('vendor_info_get_fail'), ErrCode::$VALIDATE_ERROR);
            }
            //供应商不能选择作废或草稿状态的
            if (in_array($vendor_info->grade_status, [VendorEnums::VENDOR_GRADE_STATUS_DRAFT, VendorEnums::VENDOR_GRADE_STATUS_INVALID])) {
                throw new ValidationException(static::$t->_('vendor_grade_status_error', ['vendor_id' => $vendor_info->vendor_id]), ErrCode::$VALIDATE_ERROR);
            }
        }
        //barcode的物料类型只能是资产类或耗材类的
        $barcode_info = MaterialSauRepository::getInstance()->getInfoByBarcode($data['barcode']);
        if (empty($barcode_info['category_type']) || !in_array($barcode_info['category_type'], [MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS])) {
            throw new ValidationException(static::$t->_('barcode_category_type_error'), ErrCode::$VALIDATE_ERROR);
        }
        //资产类型代入barcode的
        $data['category_type'] = $barcode_info['category_type'];//物料类型, 根据barcode代入

        return $data;
    }

    /**
     * 资产台账-创建
     * @param array $params 资产台账-添加-表单
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function add($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //验证参数
            $data = $this->extendValidation($params);
            $data['bar_code'] = $data['barcode'];
            //生成资产编码
            $asset_type = ClassifyService::getInstance()->getCategoryParents(['id'=>$data['category_id']]);//获取所属物料分类的顶级分类资产编码
            if (empty($asset_type)) {
                //未找到物料分类对应的一级分类编码
                throw new ValidationException(static::$t->_('material_asset_type_error'), ErrCode::$VALIDATE_ERROR);
            }
            $timestamp = time();
            $serial_number_array = AssetCodeService::getInstance()->createSerialNumber(1,$timestamp);
            $data['asset_code'] = AssetCodeService::getInstance()->createAssetCode($asset_type[$data['category_id']]['code'],array_shift($serial_number_array),$timestamp);
            $data['source_type'] = MaterialEnums::SOURCE_TYPE_ADD;
            $data['quality_status'] = $data['quality_status'] ?? 0;
            $now_time = date('Y-m-d H:i:s', time());
            $data['created_at'] = $now_time;
            $data['updated_at'] = $now_time;
            $data['maintenance_expire_date'] = empty($data['maintenance_expire_date']) ? null : $data['maintenance_expire_date'];
            //资产台账信息入库
            $asset = new MaterialAssetsModel();
            $bool = $asset->i_create($data);
            if ($bool === false) {
                throw new BusinessException('资产台账添加失败 = ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_ADD_ERROR);
            }
            //附件信息入库
            if (!empty($data['attachments'])) {
                if (count($data['attachments']) > self::MATERIAL_MAX_PIC) {
                    throw new BusinessException('attachments max 5');
                }
                $am = new MaterialAttachmentModel();
                $attachArr = [];
                foreach ($data['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialEnums::OSS_MATERIAL_TYPE_ASSET;
                    $tmp['oss_bucket_key'] = $asset->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachArr[] = $tmp;
                }
                if (!$am->batch_insert($attachArr)) {
                    throw new BusinessException('资产台账附件信息添加失败 = ' . json_encode($data['attachments'], JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_ADD_ERROR);
                }
            }
            //记录操作日志
            $update_log_model = new MaterialAssetUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_ADD, $asset, $data, $user);
            if ($log_bool === false) {
                throw new BusinessException('资产台账添加操作记录失败 = ' . json_encode($asset->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_ADD_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-add failed:' . $real_message . json_encode($data));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 资产台账-更新
     * @param array $params 资产台账-修改-表单
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function save($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //资产台账信息查询
            $asset = $this->getMaterialAssetInfoById($params['id']);

            if ($asset->status == MaterialEnums::ASSET_STATUS_SCRAPPED) {
                throw new ValidationException(self::$t['material_asset_save_error_010'], ErrCode::$VALIDATE_ERROR);
            }
            $log_asset = clone $asset;
            //验证参数
            $params = $this->extendValidation($params, $asset->toArray());
            $asset_update = [
                "category_id" => $params['category_id'],
	            "category_name" => $params['category_name'],
                "category_code" => $params['category_code'],
                "finance_category_id" => $params['finance_category_id'],
                "finance_category_name" => $params['finance_category_name'],
                "finance_category_code" => $params['finance_category_code'],
                "sn_code" => $params['sn_code'],
                "status" => $params['status'],
                "use" => $params['use'],
                "quality_status" => $params['quality_status'] ?? 0,
                "old_asset_code" => $params['old_asset_code'],
                "purchase_price" => $params['purchase_price'],
                "currency" => $params['currency'],
                "use_limit" => $params['use_limit'],
                "net_value" => $params['net_value'],
                "unit_zh" => $params['unit_zh'],
                "unit_en" => $params['unit_en'],
                "model" => $params['model'],
                "ownership" => $params['ownership'],
                "brand" => $params['brand'],
                "vendor_id" => $params['vendor_id'],
                "vendor_name" => $params['vendor_name'],
                "pc_code" => $params['pc_code'],
                "use_land" => $params['use_land'],
                "sap_no" => $params['sap_no'],
                "purchase_lease_contract_no" => $params['purchase_lease_contract_no'],
                "maintenance_vendor" => $params['maintenance_vendor'],
                "maintenance_expire_date" => $params['maintenance_expire_date'],
                "mark" => $params['mark'],
                "company_id" => $params['company_id'],
                "company_name" => $params['company_name'],
                'updated_at' => date('Y-m-d H:i:s', time()),
                'receipted_at' => $params['receipted_at'] ?? null,
            ];
            //非采购入库通知单的采购购入方式才可编辑如下信息
            if ($asset->source_type != MaterialEnums::SOURCE_TYPE_PURCHASE) {
                $asset_update['source_code'] = $params['source_code'];
                $asset_update['real_arrival_date'] = $params['real_arrival_date'];
                $asset_update['pono'] = $params['pono'];
                $asset_update['psno'] = $params['psno'];
                $asset_update['scm_no'] = $params['scm_no'];
            }
            $bool = $asset->i_update($asset_update);
            if ($bool === false) {
                throw new BusinessException('资产台账修改失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_SAVE_ERROR);
            }
            //编辑时先删除原来的附件信息
            $db->updateAsDict(
                'material_attachment',
                ['deleted' => MaterialClassifyEnums::IS_DELETED_YES],
                'oss_bucket_key = '. $asset->id.' and oss_bucket_type = '.MaterialEnums::OSS_MATERIAL_TYPE_ASSET
            );
            //开始存附件信息
            if (!empty($params['attachments'])) {
                if (count($params['attachments']) > self::MATERIAL_MAX_PIC) {
                    throw new BusinessException('attachments max ' . self::MATERIAL_MAX_PIC);
                }
                //然后在批量插入附件信息
                $am = new MaterialAttachmentModel();
                $attachArr = [];
                foreach ($params['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = MaterialEnums::OSS_MATERIAL_TYPE_ASSET;
                    $tmp['oss_bucket_key'] = $asset->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $attachArr[] = $tmp;
                }
                if (!$am->batch_insert($attachArr)) {
                    throw new BusinessException('资产台账修改附件信息失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_SAVE_ERROR);
                }
            }
            //记录操作日志
            $update_log_model = new MaterialAssetUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_UPDATE, $log_asset, $asset_update, $user);
            if ($log_bool === false) {
                throw new BusinessException('资产台账更新-操作记录失败 = ' . json_encode($asset->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_SAVE_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-save failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 资产台账-转移
     * @param array $params 资产台账-转移-表单
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function transfer($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //离职日期处理
            if (isset($params['leave_date']) && !empty($params['leave_date'])) {
                Validation::validate($params, ['leave_date' => 'DateTime']);
            } else {
                $params['leave_date'] = null;
            }
            //资产台账信息查询
            $asset = $this->getMaterialAssetInfoById($params['id']);
            if (!in_array($asset->status, [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                //资产使用状态是使用中，闲置；如果不是，则提示：只有闲置，使用中，闲置（在网点）的资产可以进行转移。
                throw new ValidationException(self::$t['material_asset_transfer_status_error'], ErrCode::$VALIDATE_ERROR);
            }
            if ($asset->staff_id == $params['staff_id']) {
                //转移前后使用人不可一致
                throw new ValidationException(self::$t['material_asset_transfer_staff_error'], ErrCode::$VALIDATE_ERROR);
            }
            $log_asset = clone $asset;
            $now_time = date('Y-m-d H:i:s', time());
            $asset_update = [
                'status' => MaterialEnums::ASSET_STATUS_ALLOT,//使用状态-调拨中
                'updated_at' => $now_time
            ];
            $bool = $asset->i_update($asset_update);
            if ($bool === false) {
                throw new BusinessException('资产台账转移失败 可能的原因是=' . get_data_object_error_msg($asset) . ';数据是=' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR);
            }
            //记录转移记录-头信息
            $transfer_batch = new MaterialAssetTransferBatchModel();
            $transfer_batch_data = [
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'type' => MaterialEnums::TRANSFER_TYPE_ONE,
                'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                'mark' => $params['mark'] ?? '',
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_batch->i_create($transfer_batch_data);
            if ($bool === false) {
                throw new BusinessException('资产台账转移-记录转移头信息失败 可能的原因是=' . get_data_object_error_msg($transfer_batch) . ';数据是=' . json_encode($transfer_batch_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR);
            }
            //查询员工协议公司
            $staffData = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, contract_company_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind' => ['ids' => [$log_asset->staff_id, $params['staff_id']]]
            ])->toArray();
            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
            //获取公司名称
            $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();
            $fromCompany = $staffData[$log_asset->staff_id] ?? 0;
            $toCompany = $staffData[$params['staff_id']] ?? 0;

            //记录转移记录-行信息 新增 协议公司
            $transfer_log = new MaterialAssetTransferLogModel();
            $transfer_log_data = [
                'batch_id' => $transfer_batch->id,
                'asset_id' => $asset->id,
                'barcode' => $asset->bar_code,
                'asset_code' => $asset->asset_code,
                'from_staff_id' => $log_asset->staff_id,
                'from_staff_name' => $log_asset->staff_name,
                'from_node_department_id' => $log_asset->node_department_id,
                'from_node_department_name' => $log_asset->node_department_name,
                'from_sys_store_id' => $log_asset->sys_store_id,
                'from_store_name' => $log_asset->store_name,
                'from_pc_code' => $log_asset->pc_code,
                'from_use_land' => $log_asset->use_land,
                'from_company_id' => $log_asset->company_id,
                'from_company_name' => $log_asset->company_name,
                'from_contract_company_id' => $fromCompany,
                'from_contract_company_name' => $companyList[$fromCompany] ?? '',
                'to_staff_id' => $params['staff_id'],
                'to_staff_name' => $params['staff_name'],
                'to_node_department_id' => $params['node_department_id'],
                'to_node_department_name' => $params['node_department_name'],
                'to_sys_store_id' => ($params['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id) ? $log_asset->sys_store_id : $params['sys_store_id'],
                'to_store_name' => ($params['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id) ? $log_asset->store_name : $params['store_name'],
                'to_pc_code' => $params['pc_code'],
                'to_use_land' => $params['use_land'] ? $params['use_land'] : (($params['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id && $log_asset->use_land) ? $log_asset->use_land : $params['store_name']),
                'to_company_id' => $params['company_id'],
                'to_company_name' => $params['company_name'],
                'to_contract_company_id' => $toCompany,
                'to_contract_company_name' => $companyList[$toCompany] ?? '',
                'transfer_method' => $params['transfer_method'],
                'express_no' => $params['express_no'],
                'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                'transfer_operator_id' => $user['id'],
                'transfer_operator_name' => $user['name'],
                'transfer_remark' => $params['mark'] ?? '',
                'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                'from_use_status' => $log_asset->status,
                'transfer_at' => $now_time,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_log->i_create($transfer_log_data);
            if ($bool === false) {
                throw new BusinessException('资产台账转移-记录转移行信息失败 可能的原因是=' . get_data_object_error_msg($transfer_log) . ';数据是=' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR);
            }
            //记录操作日志
            $update_log_model = new MaterialAssetUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_TRANSFER, $log_asset, $asset_update, $user);
            if ($log_bool === false) {
                throw new BusinessException('资产台账转移-操作记录失败 = ' . json_encode($asset->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR);
            }

            //组装发送站内信员工组 && 记录资产汇总记录
            $db_barcode_summary_data = [
                'staff_info_id' => $params['staff_id'],
                'barcode' => $asset->bar_code,
                'batch_id' => $transfer_batch->id,
                'num' => 1,
                'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                'type' => MaterialEnums::OPERATE_TYPE_TRANSFER,
                'transfer_remark' => $params['mark'] ?? '',
                'created_at' => $now_time
            ];
            $batch_transfer_log_barcode_summary = new MaterialAssetTransferLogBarcodeSummaryModel();
            $bool = $batch_transfer_log_barcode_summary->i_create($db_barcode_summary_data);
            if ($bool === false) {
                throw new BusinessException('资产台账-转移-记录资产批量变更提醒-资产汇总记录表失败 = ' . json_encode($db_barcode_summary_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($batch_transfer_log_barcode_summary), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
            }
            $db->commit();
            //发送站内信
            AssetTransferMessageService::getInstance()->sendPendingReceiptReminder([
                'staff_info_id' => $params['staff_id'],
                'message_content' => $transfer_batch->id,
                'category' => MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER
            ]);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-transfer failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 资产台账-批量导入-导入新增
     * @param array $excel_file 资产台账模版文件
     * @param array $user 当前登陆者信息
     * @return array
     * @throws BusinessException
     */
    public function batchAdd($excel_file, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //资产台账excel
        $excel_header_column = $excel_data =  $batch_assets = $new_excel_data = $log_batch = [];
        $db = $this->getDI()->get('db_oa');
        $start_time = get_curr_micro_time();
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $db->begin();
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //检测文档合理性
            [$excel_header_column, $excel_data, $batch_assets,$log_batch] = $this->checkBatchAddData($excel_file, $user);
            if (!empty($batch_assets)) {
                $child_batch_assets = array_chunk($batch_assets, 2000);
                //分组插入资产台账数据
                foreach ($child_batch_assets as $key=>$child) {
                    $asset = new MaterialAssetsModel();
                    $bool = $asset->batch_insert($child);
                    if ($bool === false) {
                        throw new BusinessException('资产台账批量导入新增失败', ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_ADD_ERROR);
                    }
                    //记录操作日志
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $log_bool = $update_log_model->batch_insert(array_slice($log_batch, $key*2000,2000));
                    if ($log_bool === false) {
                        throw new BusinessException('资产台账批量导入新增操作记录失败 ', ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_ADD_ERROR);
                    }
                }
                $db->commit();
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-asset-batch-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!in_array($code, [ErrCode::$VALIDATE_ERROR,ErrCode::$SYSTEM_ERROR])) {
            //非验证类或系统错误的提示，需要生成下载模版
            $success_num = 0;
            foreach ($excel_data as $item) {
                if ($code != ErrCode::$SUCCESS && strpos($item[22], 'success') !== false) {
                    $item[22] = "";
                } else if (strpos($item[22], 'success') !== false){
                    $success_num++;
                }
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, "导入新增（Add）-" . date("YmdHis"));
            //内容包含模版第二行表头，所以需要总数-1
            $all_num = count($new_excel_data)-1;
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => bcsub($all_num, $success_num)
            ];
            $code = ErrCode::$SUCCESS;
            $end_time = get_exec_time($start_time);
            $this->logger->info("批量导入新增[ ".count($log_batch)." ]条;耗时预估：[". $end_time."]");
        }
        return [
            'code'    => $code,
            'message' => in_array($code, [ErrCode::$VALIDATE_ERROR,ErrCode::$SYSTEM_ERROR]) ? $message : 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * 检测资产台账数据合理性
     * @param array $excel_file excel文件内容
     * @param array $user 当前登陆者信息
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    private function checkBatchAddData(array $excel_file, $user)
    {
        //barcode信息组
        $barcode_arr = [];
        //物料分类信息组
        $category_arr = [];
        //财务分类信息组
        $finance_arr = [];
        //员工信息组
        $staff_arr = [];
        //部门关联的所属公司信息
        $department_arr = [];
        //sap编码组
        $sap_no_arr = [];
        //旧资产编码组
        $old_asset_code_arr = [];
        //sn码组
        $sn_code_arr = [];
        //资产编码前缀组
        $asset_type = [];
        //需要生成的资产编码总数量
        $asset_serial_count = 0;
        //批量插入资产台账
        $batch_assets = [];
        $start_date = get_curr_micro_time();
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    14 => \Vtiful\Kernel\Excel::TYPE_STRING,
                    19 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                    20 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        //弹出excel标题第二行信息
        $second_header = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('material_asset_data_error'), ErrCode::$VALIDATE_ERROR);
        }
        //先验证每个行上数量累积和是否超过上限
        $count_num = 0;
        foreach ($excel_data as $item) {
            $quantity = isset($item[2]) ? trim($item[2]) : '';
            if (empty($quantity) || !preg_match("/^[1-9]\d*$/", $quantity)) {
                continue;
            }
            $count_num = bcadd($count_num, $quantity);
        }
        if ($count_num > MaterialEnums::MATERIAL_ASSET_BATCH_LIMIT) {
            throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>MaterialEnums::MATERIAL_ASSET_BATCH_LIMIT]), ErrCode::$VALIDATE_ERROR);
        }
        //操作者姓名
        $operate_user_name = get_name_and_nick_name($user['name'], $user['nick_name']);
        //验证每项元素是否符合标准
        foreach ($excel_data as $key => &$item) {
            $error_msg = [];
            //分类错误信息
            $category_msg = "";
            //barcode
            $barcode = isset($item[0]) ? trim($item[0]) : '';
            if (!empty($barcode) && !isset($barcode_arr[$barcode])) {
                $barcode_list = StandardService::getInstance()->searchBarcode('en', ['barcode' => $barcode, 'status'=>MaterialClassifyEnums::MATERIAL_START_USING, 'limit' => 1]);
                if (!empty($barcode_list['data'])) {
                    $barcode_arr[$barcode] = $barcode_list['data'][0];
                } else {
                    //将错误barcode数据放入数组避免重复查询错误员工信息
                    $barcode_arr[$barcode] = false;
                }
            }
            if (empty($barcode) || empty($barcode_arr) || $barcode_arr[$barcode] === false) {
                //barcode不合法
                $error_msg[] = self::$t['material_asset_add_error_001'];
            } else {
                //barcode合法判断物料分类是否是最末级有效分类
                if (!isset($category_arr[$barcode_arr[$barcode]['category_id']])) {
                    $category_exists = (new MaterialCategoryModel)->isHasChildrenUsing($barcode_arr[$barcode]['category_id']);
                    if (!empty($category_exists)) {
                        $category_msg = self::$t['material_sau_category_not_last'];
                        $category_arr[$barcode_arr[$barcode]['category_id']] = true;
                    } else {
                        $category_arr[$barcode_arr[$barcode]['category_id']] = false;
                    }
                }
                //barcode合法判断财务分类是否是最末级有效分类
                if (!isset($finance_arr[$barcode_arr[$barcode]['finance_category_id']])) {
                    $finance_exists = (new MaterialFinanceCategoryModel())->isHasChildrenUsing($barcode_arr[$barcode]['finance_category_id']);
                    if (!empty($finance_exists)) {
                        $category_msg = self::$t['material_sau_category_not_last'];
                        $finance_arr[$barcode_arr[$barcode]['finance_category_id']] = true;
                    } else {
                        $finance_arr[$barcode_arr[$barcode]['finance_category_id']] = false;
                    }
                }
                //判断是否存在错误信息或者存在不合法的数据
                if ($category_msg) {
                    $error_msg [] = $category_msg;
                } else if ($category_arr[$barcode_arr[$barcode]['category_id']] === true || $finance_arr[$barcode_arr[$barcode]['finance_category_id']] === true) {
                    $error_msg [] = self::$t['material_sau_category_not_last'];
                }
            }
            //数量
            $quantity = isset($item[2]) ? trim($item[2]) : '';//数量
            $old_asset_code = isset($item[12]) ? trim($item[12]) : '';//旧资产编码
            $sap_no = isset($item[13]) ? trim($item[13]) : '';//SAP资产编码
            $sn_code = isset($item[14]) ? trim($item[14]) : '';//SN码
            if (empty($quantity) || !preg_match(MaterialEnums::MATERIAL_ASSET_BATCH_ADD_QUANTITY, $quantity)) {
                //数量必须为>=1的整数
                $error_msg[] = self::$t['material_asset_add_error_002'];
            } else if (intval($quantity) > 1 && (!empty($old_asset_code) || !empty($sap_no) || !empty($sn_code))) {
                // 数量大于1时，则旧资产编码，SAP资产编码，SN码不可有值
                $error_msg[] = self::$t['material_asset_add_error_003'];
            }
            //单价（不含税）
            $purchase_price = isset($item[3]) ? trim($item[3]) : '';
            if (!empty($purchase_price) && !preg_match(MaterialEnums::PRICE_RULE, $purchase_price)) {
                $error_msg[] = self::$t->_('material_asset_price_error');
            }
            //币种
            $currency_text = isset($item[4]) ? trim($item[4]) : '';
            $currency = $currency_text ? explode('-',$currency_text)[0] : 0;
            if (!in_array($currency, explode(',', GlobalEnums::VALIDATE_CURRENCY_PARAMS))) {
                $error_msg[] = self::$t->_('material_asset_currency_error');
            }
            //净值
            $net_value = isset($item[5]) ? trim($item[5]) : '';
            if (!empty($net_value) && !preg_match(MaterialEnums::PRICE_RULE, $net_value)) {
                $error_msg[] = self::$t->_('material_asset_price_error');
            }
            if (!empty($purchase_price) && !empty($net_value) && bccomp($purchase_price, $net_value, 2) === -1) {
                //采购价与净值比较
                $error_msg[] = self::$t->_('material_asset_net_value_error');
            }
            //使用状态
            $status_text = isset($item[15]) ? trim($item[15]) : '';
            $status = $status_text ? explode('-',$status_text)[0] : 0;
            //使用方向
            $use_text = isset($item[16]) ? trim($item[16]) : '';
            $use = $use_text ? explode('-', $use_text)[0] : 0;
            //所有权
            $ownership_text = isset($item[17]) ? trim($item[17]) : '';
            $ownership = $ownership_text ? explode('-', $ownership_text)[0] : 0;
            //来源方式
            $source_code_text = isset($item[18]) ? trim($item[18]) : '';
            $source_code = $source_code_text ? explode('-', $source_code_text)[0] : 0;
            if (!in_array($status, [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED, MaterialEnums::ASSET_STATUS_HANDLED, MaterialEnums::ASSET_STATUS_LOST, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])
                || !in_array($use, explode(",", MaterialEnums::USE_VALIDATE))
                || !in_array($ownership, explode(',', MaterialEnums::OWNERSHIP_VALIDATE))
                || !in_array($source_code, explode(',', MaterialEnums::SOURCE_CODE_VALIDATE))) {
                //使用状态、使用方向、所有权、来源方式
                $error_msg[] = self::$t['material_asset_add_error_004'];
            }
            $staff_id = isset($item[6]) ? trim($item[6]) : '';//员工工号
            //如果使用状态不是闲置，则员工Id必填
            if ($status != MaterialEnums::ASSET_STATUS_UNUSED && empty($staff_id)) {
                $error_msg[] = self::$t['material_asset_add_error_005'];
            }
            //否则无论是闲置还是其他状态，只要填写了员工工号就要判断判断员工信息是否正确
            if ($staff_id) {
                if (!isset($staff_arr[$staff_id])) {
                    $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_id, 'limit' => 1]);
                    if (!empty($staff_list['data'])) {
                        //员工信息组不存在则查找
                        $staff_arr[$staff_id] = $staff_list['data'][0];
                        //部门ID
                        $department_id = $staff_arr[$staff_id]['node_department_id'];
                        if (!isset($department_arr[$department_id])) {
                            //部门所属公司组不存在则查找
                            $department_info = $this->getCompanyByDepartment(['department_id' => $department_id]);
                            if (!empty($department_info['data']) && $department_info['data']['department_id']) {
                                $department_arr[$department_id] = $department_info['data'];
                            } else {
                                //将错误的部门信息放入数组避免重复查询错误部门
                                $department_arr[$department_id] = false;
                            }
                        }
                    } else {
                        //将错误的员工信息放入数组，避免重复查询
                        $staff_arr[$staff_id] = false;
                    }
                }
                if ($staff_arr[$staff_id] === false) {
                    $error_msg[] = self::$t['material_asset_add_error_006'];
                }
            }

            //使用部门
            $department_id = isset($item[8]) ? trim($item[8]) : '';
            //闲置状态部门id必填
            if ($status == MaterialEnums::ASSET_STATUS_UNUSED && empty($department_id)) {
                //如果使用状态为闲置，则使用部门必填
                $error_msg[] = self::$t['material_asset_add_error_007'];
            } else if (empty($staff_id) && $status) {
                //未填写员工工号需要校验部门信息正确性
                if (is_numeric($department_id) === false) {
                    //如果部门不是有效数字
                    $error_msg [] = self::$t['material_asset_add_error_008'];
                } else {
                    if (!isset($department_arr[$department_id])) {
                        //部门所属公司组不存在则查找
                        $department_info = $this->getCompanyByDepartment(['department_id' => $department_id]);
                        if (!empty($department_info['data']) && $department_info['data']['department_id']) {
                            $department_arr[$department_id] = $department_info['data'];
                        } else {
                            //将错误的部门信息放入数组避免重复查询错误部门
                            $department_arr[$department_id] = false;
                        }
                    }
                    if ($department_arr[$department_id] === false) {
                        $error_msg[] = self::$t['material_asset_add_error_008'];
                    }
                }
            }

            //旧资产编码
            if (!empty($old_asset_code)) {
                //先判断下文档里的是否重复
                if ($old_asset_code_arr && in_array($old_asset_code, $old_asset_code_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_005'];
                } else {
                    //文档里不存在重复，先把编码放到组里，在判断与库里是否存在重复
                    $old_asset_code_arr[] = $old_asset_code;
                    if (!empty($this->checkOldAssetCode($old_asset_code))) {
                        //后判断库里的
                        $error_msg[] = self::$t['material_asset_old_code_error'];
                    }
                }
            }

            //sap资产编码
            if (!empty($sap_no)) {
                //先判断下文档里的是否重复
                if ($sap_no_arr && in_array($sap_no, $sap_no_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_006'];
                } else {
                    //文档里不存在重复，先把编码放到组里，在判断与库里是否存在重复
                    $sap_no_arr[] = $sap_no;
                    if (!empty($this->checkSapNo($sap_no))) {
                        $error_msg[] = self::$t['material_asset_sap_no_error'];
                    }
                }
            }

            //sn码
            if (!empty($sn_code) ) {
                //先判断下文档里的是否重复
                if ($sn_code_arr && in_array($sn_code, $sn_code_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_007'];
                } else {
                    //文档里不存在重复，先把编码放到组里，在判断与库里是否存在重复
                    $sn_code_arr[] = $sn_code;
                    if (!empty($this->checkSnCode($sn_code))) {
                        $error_msg[] = self::$t['material_asset_sn_code_error'];
                    }
                }
            }

            //入库日期
            $real_arrival_date = $this->handleUploadFileDate(isset($item[19]) ? trim($item[19]) : '');
            if (!preg_match(MaterialEnums::MATERIAL_ASSET_BATCH_ADD_DATE, $real_arrival_date) || strtotime($real_arrival_date) > strtotime(date('Y-m-d'))) {
                $error_msg[] = self::$t['material_asset_arrival_date_error'];
            }
            $item[19] = $real_arrival_date;
            //最新领用日期
            $receipted_at = null;
            if (isset($item[20]) && !empty($item[20])) {
                $receipted_at = $this->handleUploadFileDate(isset($item[20]) ? trim($item[20]) : '');
                if (!preg_match(MaterialEnums::MATERIAL_ASSET_BATCH_ADD_DATE, $receipted_at)) {
                    $error_msg[] = self::$t['material_asset_receipted_at_error'];
                }
            }
            //备注
            $mark = isset($item[21]) ? trim($item[21]) : '';
            if (!empty($mark) && mb_strlen($mark) > MaterialClassifyEnums::$sau_validation['mark']) {
                $error_msg[] = self::$t['material_asset_save_error_011'];
            }
            if ($error_msg) {
                //存在错误信息，则要赋予备注信息
                $item[22] = implode(";", $error_msg);
            } else {
                $category_id = $barcode_arr[$barcode]['category_id'];
                $parent_code = '';
                if (!isset($asset_type[$category_id])) {
                    //获取所属物料分类的顶级分类资产编码
                    $parent_asset_type = ClassifyService::getInstance()->getCategoryParents(['id'=>$category_id]);
                    if (empty($parent_asset_type)) {
                        $error_msg[] = self::$t['material_asset_type_error'];
                        $item[22] = implode(";", $error_msg);
                        $asset_type[$category_id] = 0;
                        continue;
                    } else {
                        $parent_code = $parent_asset_type[$category_id]['code'];
                        $asset_type[$category_id] = $parent_code;
                    }
                } else if ($asset_type[$category_id]) {
                    $parent_code = $asset_type[$category_id];
                }
                //记录资产编码总记录数
                $asset_serial_count += $quantity;
                $assets = [
                    'bar_code' => $barcode,
                    'name_zh' => $barcode_arr[$barcode]['name_zh'],
                    'name_en' => $barcode_arr[$barcode]['name_en'],
                    'name_local' => $barcode_arr[$barcode]['name_local'],
                    'category_id' => $barcode_arr[$barcode]['category_id'],
                    'category_name' => $barcode_arr[$barcode]['category_name'],
                    'category_code' => $barcode_arr[$barcode]['category_code'],
                    'finance_category_id' => $barcode_arr[$barcode]['finance_category_id'],
                    'finance_category_name' => $barcode_arr[$barcode]['finance_category_name'],
                    'finance_category_code' => $barcode_arr[$barcode]['finance_category_code'],
                    'real_arrival_date' => $real_arrival_date ? $real_arrival_date : null,
                    'status' => $status ? $status : 1,
                    'use' => $use ? $use : 1,
                    'quality_status' => MaterialEnums::QUALITY_STATUS_GOOD,
                    'source_code' => $source_code ? $source_code : 1,
                    'ownership' => $ownership ? $ownership : 1,
                    'old_asset_code' => $old_asset_code,
                    'sap_no' => $sap_no,
                    'sn_code' => $sn_code,
                    'purchase_price' => $purchase_price ? $purchase_price : 0.00,
                    'currency' => $currency,
                    'use_limit' => $barcode_arr[$barcode]['use_limit'],
                    'net_value' => $net_value ? $net_value : 0.00,
                    'unit_zh' => $barcode_arr[$barcode]['unit_zh'],
                    'unit_en' => $barcode_arr[$barcode]['unit_en'],
                    'model' => $barcode_arr[$barcode]['model'],
                    'brand' => $barcode_arr[$barcode]['brand'],
                    'source_type' => MaterialEnums::SOURCE_TYPE_ADD,
                    'mark'=>$mark,
                    'staff_id' => 0,
                    'staff_name' => '',
                    'state' => 0,
                    'wait_leave_state' => 0,
                    'leave_date' => null,
                    'job_id' => 0,
                    'job_name' => '',
                    'sys_store_id' => '',
                    'store_name' => '',
                    'node_department_id' => 0,
                    'node_department_name' => '',
                    'pc_code' => '',
                    'use_land' => '',
                    'company_id' => 0,
                    'company_name' => '',
                    'parent_code' => $parent_code,
                    'quantity' => $quantity,
                    'category_type' => $barcode_arr[$barcode]['category_type'],
                    'receipted_at' => $receipted_at,
                ];
                if ($staff_id && !empty($staff_arr[$staff_id])) {
                    $staff_info = $staff_arr[$staff_id];
                    $sys_store_id = $staff_info['sys_store_id'];
                    $node_department_id = $staff_info['node_department_id'];
                    $assets['staff_id'] = $staff_info['staff_id'];
                    $assets['staff_name'] = $staff_info['staff_name'];
                    $assets['state'] = $staff_info['state'];
                    $assets['wait_leave_state'] = $staff_info['wait_leave_state'];
                    $assets['leave_date'] = $staff_info['leave_date'];
                    $assets['job_id'] = $staff_info['job_id'];
                    $assets['job_name'] = $staff_info['job_name'];
                    $assets['sys_store_id'] = $sys_store_id;
                    $assets['store_name'] = $staff_info['store_name'];
                    $assets['node_department_id'] = $node_department_id;
                    $assets['node_department_name'] = $staff_info['node_department_name'];
                    $assets['use_land'] = $staff_info['store_name'];
                    //获取成本中心
                    if ($sys_store_id == -1) {
                        //总部
                        $pc_id = $node_department_id;
                        $type = 1;
                    } else {
                        //网点
                        $pc_id = $sys_store_id;
                        $type = 2;
                    }
                    $pc_code_data = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
                    $assets['pc_code'] = !empty($pc_code_data['data']) ? $pc_code_data['data']['pc_code'] : '';
                    if (!empty($department_arr[$node_department_id])) {
                        $company_id = $department_arr[$node_department_id]['company_id'];
                        $assets['company_id'] = $company_id ? $company_id : 0;
                        $assets['company_name'] = $department_arr[$node_department_id]['company_name'];
                    }
                } else if (!empty($department_arr) && !empty($department_arr[$department_id])) {
                    $assets['node_department_name'] = $department_arr[$department_id]['department_name'];
                    $assets['node_department_id'] = $department_id;
                    $company_id = $department_arr[$department_id]['company_id'];
                    $assets['company_id'] = $company_id ? $company_id : 0;
                    $assets['company_name'] = $department_arr[$department_id]['company_name'];
                }
                $assets['created_at'] = date('Y-m-d H:i:s');
                $assets['updated_at'] = date('Y-m-d H:i:s');
                $batch_assets[] = $assets;
                $item[22] = 'success';
            }
        }
        $this->logger->info('验证合法逻辑耗时:['.get_exec_time($start_date).']');
        $start_date = get_curr_micro_time();
        //生成资产编码
        $timestamp = time();
        $serial_number_array = AssetCodeService::getInstance()->createSerialNumber($asset_serial_count, $timestamp);
        $serial_number_array = array_reverse($serial_number_array);
        $add_batch_assets = [];
        $log_batch = [];
        foreach ($batch_assets as $asset) {
            $quantity = $asset['quantity'];
            $parent_code = $asset['parent_code'];
            unset($asset['parent_code'], $asset['quantity']);
            for ($i=0; $i<$quantity; $i++) {
                $asset['asset_code'] = AssetCodeService::getInstance()->createAssetCode($parent_code,array_pop($serial_number_array),$timestamp);
                $add_batch_assets[] = $asset;
                $log_data = [
                    'category_id'=>$asset['category_id'],
                    'finance_category_id' => $asset['finance_category_id'],
                    'staff_id'=>$asset['staff_id'],
                    'node_department_id' => $asset['node_department_id'],
                    'pc_code' => $asset['pc_code'],
                    'sys_store_id' => $asset['sys_store_id'],
                    'asset_code' => $asset['asset_code'],
                    'purchase_price' => $asset['purchase_price'],
                    'sn_code' => $asset['sn_code'],
                    'old_asset_code' => $asset['old_asset_code'],
                    'receipted_at' => $asset['receipted_at'],
                ];
                $log_batch[] = [
                    'asset_code' => $asset['asset_code'],
                    'staff_id' => $user['id'],
                    'staff_name' => $operate_user_name,
                    'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                    'type' => MaterialEnums::OPERATE_TYPE_BATCH_ADD,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
            }
        }
        $this->logger->info('处理流水号耗时：[ '.get_exec_time($start_date).']');
        return [$excel_header_column, array_merge([$second_header], $excel_data), $add_batch_assets,$log_batch];
    }

    /**
     * 上传日期格式的兼容处理
     * @param string $file_src_date 日期
     * @return false|string
     */
    private function handleUploadFileDate(string $file_src_date)
    {
        if (preg_match('/\d{10}/', $file_src_date)) {
            // 时间戳
            $date = date('Y-m-d', $file_src_date);
        } else if ((preg_match('/\d{8}/', $file_src_date)) ){
            //年月日
            $date = date('Y-m-d', strtotime($file_src_date));
        } else if (stripos($file_src_date, '/') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        } else if (stripos($file_src_date, '-') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        } else {
            $date = $file_src_date;
        }
        return $date;
    }

    /**
     * 资产台账-批量导入-导入新增
     * @param array $excel_file 资产台账模版文件
     * @param array $user 当前登陆者信息
     * @return array
     * @throws BusinessException
     */
    public function batchSave($excel_file, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //资产台账excel
        $excel_header_column = $excel_data =  $batch_assets = $new_excel_data = [];
        //操作日志组
        $update_log_data = [];
        $db = $this->getDI()->get('db_oa');
        $start_time = get_curr_micro_time();
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $db->begin();
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            //检测文档合理性
            [$excel_header_column, $excel_data, $batch_assets] = $this->checkBatchSaveData($excel_file);
            if (!empty($batch_assets)) {
                $operate_user_name = get_name_and_nick_name($user['name'], $user['nick_name']);
                foreach ($batch_assets as $item) {
                    $asset = $item['asset'];
                    $asset_update = $item['update'];
                    $log_data = $item['log'];
                    $bool = $asset->i_update($asset_update);
                    if ($bool === false) {
                        throw new BusinessException('资产台账批量导入修改失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_SAVE_ERROR);
                    }
                    //组装更新操作日志
                    $update_log_data[] = [
                        'asset_code' => $asset->asset_code,
                        'staff_id' => $user['id'],
                        'staff_name' => $operate_user_name,
                        'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                        'type' => MaterialEnums::OPERATE_TYPE_BATCH_UPDATE,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s'),
                    ];
                }
                //记录操作日志
                if ($update_log_data) {
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $log_bool = $update_log_model->batch_insert($update_log_data);
                    if ($log_bool === false) {
                        throw new BusinessException('资产台账批量导入更新操作记录失败 = ' . json_encode($update_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_SAVE_ERROR);
                    }
                }
                $db->commit();
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('material-asset-batch-save-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR])) {
            //非验证类或系统错误的提示，需要生成下载模版
            $success_num = 0;
            //内容包含模版第二行表头
            $second_header = array_shift($excel_data);
            foreach ($excel_data as $item) {
                if ($code != ErrCode::$SUCCESS && strpos($item[self::MATERIAL_BATCH_SAVE_EXCEL_RESULT], 'success') !== false) {
                    $item[self::MATERIAL_BATCH_SAVE_EXCEL_RESULT] = "";
                } else if (strpos($item[self::MATERIAL_BATCH_SAVE_EXCEL_RESULT], 'success') !== false) {
                    $success_num++;
                }
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, array_merge([$second_header], $new_excel_data), "导入修改（Modify）-" . date("YmdHis"));
            //内容包含模版第二行表头，所以需要总数-1
            $all_num = count($new_excel_data);
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => bcsub($all_num, $success_num)
            ];
            $code = ErrCode::$SUCCESS;
            $end_time = get_exec_time($start_time);
            $this->logger->info("批量导入修改[".$all_num."]条预计耗时:[ ".$end_time." ]");
        }
        return [
            'code'    => $code,
            'message' => in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR]) ? $message : 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * 检测资产台账数据合理性
     * @param array $excel_file excel文件内容
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function checkBatchSaveData(array $excel_file)
    {
        //根据资产编码筛选出来的资产台账信息组
        $asset_arr = [];
        //根据物料分类编码筛选出来的物料信息组
        $category_arr = [];
        //根据财务分类编码筛选出来的物料信息组
        $finance_arr = [];
        //sap编码组
        $sap_no_arr = [];
        //旧资产编码组
        $old_asset_code_arr = [];
        //sn码组
        $sn_code_arr = [];
        $batch_assets = [];
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        //弹出excel标题第二行信息
        $second_header = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('material_asset_data_error'), ErrCode::$VALIDATE_ERROR);
        }
        //跳过空行
        foreach ($excel_data as $line => $row) {
            if (empty(implode(',', array_filter($row)))) {
                //空行
                unset($excel_data[$line]);
            }
        }
        if (count($excel_data) > MaterialEnums::MATERIAL_ASSET_BATCH_UPDATE_LIMIT) {
            throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max'=>MaterialEnums::MATERIAL_ASSET_BATCH_UPDATE_LIMIT]), ErrCode::$VALIDATE_ERROR);
        }

        //excel数据转字段名数组
        $field_excel_data = $this->excelToBatchSaveDataEdit($excel_data);

        //用于判断所属公司
        $company_list = (new PurchaseService())->getCooCostCompany();
        //验证每项元素是否符合标准
        foreach ($field_excel_data as $key => &$item) {
            $error_msg = [];
            //资产编码
            $asset_code = isset($item['asset_code']) ? trim($item['asset_code']) : '';
            if (empty($asset_code)) {
                $error_msg[] = self::$t['material_asset_save_error_001'];
            } else {
                if (!isset($asset_arr[$asset_code])) {
                    //不存在则去查询
                    $asset_info = $this->getMaterialAssetInfoByAssetCode($asset_code);
                    if (empty($asset_info)) {
                        $error_msg[] = self::$t['material_asset_save_error_001'];
                    } else if ($asset_info->status == MaterialEnums::ASSET_STATUS_SCRAPPED) {
                        $error_msg[] = self::$t['material_asset_save_error_010'];
                    } else {
                        $asset_arr[$asset_code] = $asset_info;
                    }
                } else {
                    $error_msg[] = self::$t['material_asset_save_error_008'];
                }
            }
            //资产中文名称
            $name_zh = isset($item['name_zh']) ? trim($item['name_zh']) : '';
            $sau_validation_name_size = MaterialClassifyEnums::$sau_validation['name'];
            if (!empty($name_zh) && mb_strlen($name_zh) > $sau_validation_name_size) {
                $error_msg[] = self::$t['material_asset_save_error_009'];
            }
            //资产英文名称
            $name_en = isset($item['name_en']) ? trim($item['name_en']) : '';
            if (!empty($name_en) && mb_strlen($name_en) > $sau_validation_name_size) {
                $error_msg[] = self::$t['material_asset_save_error_009'];
            }
            //资产当地名称
            $name_local = isset($item['name_local']) ? trim($item['name_local']) : '';
            if (!empty($name_local) && mb_strlen($name_local) > $sau_validation_name_size) {
                $error_msg[] = self::$t['material_asset_save_error_009'];
            }
            //规格型号
            $model = isset($item['model']) ? trim($item['model']) : '';
            $sau_validation_model_size = MaterialClassifyEnums::$sau_validation['model'];
            if (!empty($model) && mb_strlen($model) > $sau_validation_model_size) {
                $error_msg[] = self::$t['material_asset_save_error_009'];
            }

            //物料编码
            $category_code = isset($item['category_code']) ? trim($item['category_code']) : '';
            if (!empty($category_code) && !isset($category_arr[$category_code])) {
                //传递了物料编码并且不存在则去查询
                $category_info = MaterialCategoryModel::findFirst([
                    'columns'=>'id,name,code',
                    'conditions' => 'code = :code: and status=:status: and is_deleted=:is_deleted:',
                    'bind'=>['code'=>$category_code, 'status'=>MaterialClassifyEnums::MATERIAL_START_USING, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
                ]);
                if (empty($category_info)) {
                    $error_msg[] = self::$t['material_asset_save_error_002'];
                } else {
                    if (!empty((new MaterialCategoryModel())->isHasChildrenUsing($category_info->id))) {
                        $error_msg[] = self::$t['material_asset_save_error_002'];
                        $category_arr[$category_code] = false;
                    } else {
                        $category_arr[$category_code] = $category_info;
                    }
                }
            } else if (!empty($category_code) && isset($category_arr[$category_code]) && $category_arr[$category_code] === false) {
                $error_msg[] = self::$t['material_asset_save_error_002'];
            }

            //财务分类编码
            $finance_code = isset($item['finance_category_code']) ? trim($item['finance_category_code']) : '';
            if (!empty($finance_code) && !isset($finance_arr[$finance_code])) {
                //传递了财务分类编码并且不存在则去查询
                $finance_info = MaterialFinanceCategoryModel::findFirst([
                    'columns'=>'id,name,code',
                    'conditions' => 'code = :code: and status=:status: and is_deleted=:is_deleted:',
                    'bind'=>['code'=>$finance_code, 'status'=>MaterialClassifyEnums::MATERIAL_START_USING, 'is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]
                ]);
                if (empty($finance_info)) {
                    $error_msg[] = self::$t['material_asset_save_error_003'];
                } else {
                    if (!empty((new MaterialFinanceCategoryModel())->isHasChildrenUsing($finance_info->id))) {
                        $error_msg[] = self::$t['material_asset_save_error_003'];
                        $finance_arr[$finance_code] = false;
                    } else {
                        $finance_arr[$finance_code] = $finance_info;
                    }
                }
            } else if (!empty($finance_code) && isset($finance_arr[$finance_code]) && $finance_arr[$finance_code] === false) {
                $error_msg[] = self::$t['material_asset_save_error_003'];
            }

            //旧资产编码
            $old_asset_code = isset($item['old_asset_code']) ? trim($item['old_asset_code']) : '';
            //填写了旧资产编码或者跟资产编码查询到的对应的旧资产编码不一致才去校验
            if (!empty($old_asset_code)) {
                //先判断下文档里的是否重复
                if ($old_asset_code_arr && in_array($old_asset_code, $old_asset_code_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_005'];
                } else {
                    $old_asset_code_arr[] = $old_asset_code;
                    if (isset($asset_arr[$asset_code]) && $asset_arr[$asset_code]->old_asset_code != $old_asset_code && !empty($this->checkOldAssetCode($old_asset_code))) {
                        //后判断库里的
                        $error_msg[] = self::$t['material_asset_old_code_error'];
                    }
                }
            }
            //SAP资产编码
            $sap_no = isset($item['sap_no']) ? trim($item['sap_no']) : '';
            if (!empty($sap_no)) {
                //先判断文档里的是否重复
                if ($sap_no_arr && in_array($sap_no, $sap_no_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_006'];
                } else {
                    $sap_no_arr[] = $sap_no;
                    if (isset($asset_arr[$asset_code]) && $asset_arr[$asset_code]->sap_no != $sap_no && !empty($this->checkSapNo($sap_no))) {
                        //后判断库里的
                        $error_msg[] = self::$t['material_asset_sap_no_error'];
                    }
                }
            }
            //SN码
            $sn_code = isset($item['sn_code']) ? trim($item['sn_code']) : '';
            if (!empty($sn_code)) {
                //先判断文档里是否重复
                if ($sn_code && in_array($sn_code, $sn_code_arr)) {
                    $error_msg[] = self::$t['material_asset_save_error_007'];
                } else {
                    $sn_code_arr[] = $sn_code;
                    if (isset($asset_arr[$asset_code]) && $asset_arr[$asset_code]->sn_code != $sn_code  && !empty($this->checkSnCode($sn_code))) {
                        $error_msg[] = self::$t['material_asset_sn_code_error'];
                    }
                }
            }

            //使用状态
            $status_text = isset($item['status']) ? trim($item['status']) : '';
            $status = $status_text ? explode('-',$status_text)[0] : 0;
            //使用方向
            $use_text = isset($item['use']) ? trim($item['use']) : '';
            $use = $use_text ? explode('-', $use_text)[0] : 0;
            //所有权
            $ownership_text = isset($item['ownership']) ? trim($item['ownership']) : '';
            $ownership = $ownership_text ? explode('-', $ownership_text)[0] : 0;
            //正品/残品
            $quality_status_text = isset($item['quality_status']) ? trim($item['quality_status']) : '';
            $quality_status = $quality_status_text ? explode('-', $quality_status_text)[0] : 0;
            if ($status && !in_array($status, [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_REPAIRED, MaterialEnums::ASSET_STATUS_REPAIRING, MaterialEnums::ASSET_STATUS_TO_BE_SCRAPPED, MaterialEnums::ASSET_STATUS_HANDLED, MaterialEnums::ASSET_STATUS_LOST, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])
                || $use && !in_array($use, explode(",", MaterialEnums::USE_VALIDATE))
                || $ownership && !in_array($ownership, explode(',', MaterialEnums::OWNERSHIP_VALIDATE)) || $quality_status && !in_array($quality_status, explode(',', MaterialEnums::QUALITY_STATUS_VALIDATE))) {
                //使用状态不在特定的状态类型里
                $error_msg[] = self::$t['material_asset_add_error_004'];
            }
            //成本中心
            $pc_code = isset($item['pc_code']) ? trim($item['pc_code']) : '';
            //所属公司名称
            $company_name = isset($item['company_name']) ? trim($item['company_name']) : '';
            //所属公司ID
            $company_id = 0;
            if (!empty($company_name) && !empty($company_list)) {
                $cost_company_kv = array_column($company_list,'cost_company_id','cost_company_name');
                if (key_exists($company_name, $cost_company_kv)) {
                    $company_id = $cost_company_kv[$company_name];
                } else {
                    $error_msg[] = self::$t['material_asset_save_error_004'];
                }
            }
            //使用地信息
            $use_land = isset($item['use_land']) ? trim($item['use_land']) : '';
            //备注
            $mark = isset($item['mark']) ? trim($item['mark']) : '';
            if (!empty($mark) && mb_strlen($mark) > MaterialClassifyEnums::$sau_validation['mark']) {
                $error_msg[] = self::$t['material_asset_save_error_011'];
            }
            if ($error_msg) {
                //存在错误信息，则要赋予备注信息
                $item[self::MATERIAL_BATCH_SAVE_EXCEL_RESULT] = implode(";", array_unique($error_msg));
            } else {
                $asset = $asset_arr[$asset_code];
                $update_asset = [];
                $log_data = [];
                //存在物料分类信息变更
                if (!empty($category_arr[$category_code])) {
                    $category_info = $category_arr[$category_code];
                    if ($category_info->id != $asset->category_id) {
                        $update_asset['category_id'] = $category_info->id;
                        $log_data[] = [
                            'before' => $asset->category_id,
                            'after' => $category_info->id,
                            'field_name' => 'category_id'
                        ];
                    }
                    if ($category_info->name != $asset->category_name) {
                        $update_asset['category_name'] = $category_info->name;
                        $log_data[] = [
                            'before' => $asset->category_name,
                            'after' => $category_info->name,
                            'field_name' => 'category_name'
                        ];
                    }
                    if ($category_info->code != $asset->category_code) {
                        $update_asset['category_code'] = $category_code;
                        $log_data[] = [
                            'before' => $asset->category_code,
                            'after' => $category_info->code,
                            'field_name' => 'category_code'
                        ];
                    }
                }
                //存在财务分类信息变更
                if (!empty($finance_arr[$finance_code])) {
                    $finance_info = $finance_arr[$finance_code];
                    if ($finance_info->id != $asset->finance_category_id) {
                        $update_asset['finance_category_id'] = $finance_info->id;
                        $log_data[] = [
                            'before' => $asset->finance_category_id,
                            'after' => $finance_info->id,
                            'field_name' => 'finance_category_id'
                        ];
                    }
                    if ($finance_info->name != $asset->finance_category_name) {
                        $update_asset['finance_category_name'] = $finance_info->name;
                        $log_data[] = [
                            'before' => $asset->finance_category_name,
                            'after' => $finance_info->name,
                            'field_name' => 'finance_category_name'
                        ];
                    }
                    if ($finance_info->code != $asset->finance_category_code) {
                        $update_asset['finance_category_code'] = $finance_code;
                        $log_data[] = [
                            'before' => $asset->finance_category_code,
                            'after' => $finance_info->code,
                            'field_name' => 'finance_category_code'
                        ];
                    }
                }
                //资产中文、英文、当地名称、规格型号、旧资产编码、sap编码、sn码、使用状态、使用方向、使用权、成本中心、所属公司、使用地信息变更
                $can_update_edit_field = [
                    'name_zh', 'name_en', 'name_local', 'model', 'old_asset_code', 'sap_no', 'sn_code', 'status', 'use', 'ownership', 'quality_status', 'pc_code', 'company_id', 'company_name', 'use_land', 'mark'
                ];
                foreach ($can_update_edit_field as $field) {
                    $update_filed_val = ${$field};//文档变更值
                    if (!empty($update_filed_val) && $update_filed_val != $asset->$field) {
                        $update_asset[$field] = $update_filed_val;
                        $log_data[] = [
                            'before' => $asset->$field,
                            'after' => $update_filed_val,
                            'field_name' => $field
                        ];
                    }
                }
                $update_asset['updated_at'] = date('Y-m-d H:i:s', time());
                $log_data[] = [
                    'before' => $asset->updated_at,
                    'after' => $update_asset['updated_at'],
                    'field_name' => 'updated_at'
                ];
                $batch_assets[] = [
                    'asset' => $asset,
                    'update' => $update_asset,
                    'log' => $log_data
                ];
                $item[self::MATERIAL_BATCH_SAVE_EXCEL_RESULT] = 'success';
            }
        }
        return [$excel_header_column, array_merge([$second_header], $field_excel_data), $batch_assets];
    }

    /**
     * 将批量修改对应为字段
     * @param array $excel_data 数据
     * @return array
     */
    private function excelToBatchSaveDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'asset_code',//资产编码
            'name_zh',//物料中文名称
            'name_en',//物料英文名称
            'name_local',//当地语言名称
            'model',//规格型号
            'category_code',//物料分类编码
            'finance_category_code',//财务分类编码
            'old_asset_code',//旧资产编码
            'sap_no',//SAP资产编码
            'sn_code',//sn编码
            'status',//使用状态
            'use',//使用方向
            'ownership',//所有权
            'quality_status',//正品/残品
            'pc_code',//成本中心
            'company_name',//所属公司名称
            'use_land',//使用地信息
            'mark',//备注
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                $data[$k][$key] = trim($v[$index]);
            }
        }
        return array_values($data);
    }

    /**
     * 资产台账-批量转移
     * @param array $params 资产台账-批量转移-表单
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function batchTransfer($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $db->begin();
            //原资产信息组
            $origin_assets = [];
            //先验证提交过来的信息合法性
            foreach ($params['assets'] as &$item) {
                //离职日期处理
                if (isset($item['leave_date']) && !empty($item['leave_date'])) {
                    Validation::validate(['leave_date' => $item['leave_date']], ['leave_date' => 'DateTime']);
                } else {
                    $item['leave_date'] = null;
                }
                //获取资产信息
                $asset = $this->getMaterialAssetInfoById($item['asset_id']);
                //资产使用状态是使用中，闲置；如果不是，则提示：只有闲置，使用中，闲置（在网点）的资产可以进行转移。
                if (!in_array($asset->status, [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                    throw new ValidationException(self::$t['material_asset_transfer_status_error'], ErrCode::$VALIDATE_ERROR);
                }
                //转移前后使用人不可一致
                if ($asset->staff_id == $item['staff_id']) {
                    throw new ValidationException(self::$t['material_asset_transfer_staff_error'], ErrCode::$VALIDATE_ERROR);
                }
                $origin_assets[$item['asset_id']] = $asset;
            }

            //验证都通过了，开始入库操作
            if (!empty($origin_assets)) {
                //操作时间
                $now_time = date('Y-m-d H:i:s', time());
                //转移记录-行信息
                $transfer_log_data = [];
                //记录转移记录-头信息
                $transfer_batch = new MaterialAssetTransferBatchModel();
                //资产批量变更提醒-资产汇总记录表
                $batch_transfer_log_barcode_summary_data = $db_barcode_summary_data = [];
                //需发站内信工号组
                $need_send_msg_staff = [];
                $batch_date = [
                    'staff_id' => $user['id'],
                    'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                    'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                    'status' => MaterialEnums::TRANSFER_BATCH_STATUS_UNRECEIVED,
                    'mark' => $params['mark'] ?? '',
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $bool = $transfer_batch->i_create($batch_date);
                if ($bool === false) {
                    throw new BusinessException('资产台账批量转移-记录转移头信息失败 可能的原因是=' . get_data_object_error_msg($transfer_batch) . ';数据是=' . json_encode($batch_date, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }
                $fromStaff = array_column($origin_assets, 'staff_id');
                $toStaff   = array_column($params['assets'], 'staff_id');
                $allStaff  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
                $staffData = [];
                if (!empty($allStaff)) {
                    //查询员工协议公司
                    $staffData = HrStaffInfoModel::find([
                        'columns'    => 'staff_info_id, contract_company_id',
                        'conditions' => 'staff_info_id in ({ids:array})',
                        'bind'       => ['ids' => $allStaff],
                    ])->toArray();
                    $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id',
                        'staff_info_id');
                }
                //获取公司名称
                $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

                $to_asset = array_column($params['assets'], null, 'asset_id');
                //开始处理资产转移
                foreach ($origin_assets as $asset) {
                    //操作日志对象
                    $log_asset = clone $asset;
                    //转移资产信息
                    $to_asset_info = $to_asset[$asset->id];
                    //修改资产台账信息
                    $asset_update = [
                        'status' => MaterialEnums::ASSET_STATUS_ALLOT,//使用状态-调拨中
                        'updated_at' => $now_time
                    ];
                    $bool = $asset->i_update($asset_update);
                    if ($bool === false) {
                        throw new BusinessException('资产台账批量转移失败 可能的原因是=' . get_data_object_error_msg($asset) . ';数据是=' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                    }

                    //记录操作日志
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_BATCH_TRANSFER, $log_asset, $asset_update, $user);
                    if ($log_bool === false) {
                        throw new BusinessException('资产台账批量转移-操作记录失败 = ' . json_encode($asset->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                    }
                    //转移记录行信息组装
                    $transfer_log_data[] = [
                        'batch_id' => $transfer_batch->id,
                        'asset_id' => $log_asset->id,
                        'barcode' => $log_asset->bar_code,
                        'asset_code' => $log_asset->asset_code,
                        'from_staff_id' => $log_asset->staff_id,
                        'from_staff_name' => $log_asset->staff_name,
                        'from_company_id' => $log_asset->company_id,
                        'from_company_name' => $log_asset->company_name,
                        'from_node_department_id' => $log_asset->node_department_id,
                        'from_node_department_name' => $log_asset->node_department_name,
                        'from_contract_company_id' => $staffData[$log_asset->staff_id] ?? 0,
                        'from_contract_company_name' => $companyList[$staffData[$log_asset->staff_id ?? 0]] ?? '',
                        'from_sys_store_id' => $log_asset->sys_store_id,
                        'from_store_name' => $log_asset->store_name,
                        'from_pc_code' => $log_asset->pc_code,
                        'from_use_land' => $log_asset->use_land,
                        'to_staff_id' => $to_asset_info['staff_id'],
                        'to_staff_name' => $to_asset_info['staff_name'],
                        'to_node_department_id' => $to_asset_info['node_department_id'],
                        'to_node_department_name' => $to_asset_info['node_department_name'],
                        'to_contract_company_id' => $staffData[$to_asset_info['staff_id']] ?? 0,
                        'to_contract_company_name' => $companyList[$staffData[$to_asset_info['staff_id']] ?? 0] ?? '',
                        'to_sys_store_id' => ($to_asset_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id) ? $log_asset->sys_store_id : $to_asset_info['sys_store_id'],
                        'to_store_name' => ($to_asset_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id) ? $log_asset->store_name : $to_asset_info['store_name'],
                        'to_pc_code' => $to_asset_info['pc_code'],
                        'to_use_land' => $to_asset_info['use_land'] ? $to_asset_info['use_land'] : (($to_asset_info['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG && $log_asset->sys_store_id && $log_asset->use_land) ? $log_asset->use_land : $to_asset_info['store_name']),
                        'status' => MaterialEnums::TRANSFER_LOG_STATUS_UNRECEIVED,
                        'to_company_id' => $to_asset_info['company_id'],
                        'to_company_name' => $to_asset_info['company_name'],
                        'transfer_operator_id' => $user['id'],
                        'transfer_operator_name' => $user['name'],
                        'transfer_remark' => $params['mark'] ?? '',
                        'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                        'from_use_status' => $log_asset->status,
                        'transfer_at' => $now_time,
                        'created_at' => $now_time,
                        'updated_at' => $now_time,
                        'transfer_method' => $to_asset_info['transfer_method'],
                        'express_no' => $to_asset_info['express_no']
                    ];

                    //组装资产批量变更提醒-资产汇总记录表
                    if (isset($batch_transfer_log_barcode_summary_data[$to_asset_info['staff_id']][$log_asset->bar_code])) {
                        $batch_transfer_log_barcode_summary_data[$to_asset_info['staff_id']][$log_asset->bar_code]['num'] += 1;
                    } else {
                        $batch_transfer_log_barcode_summary_data[$to_asset_info['staff_id']][$log_asset->bar_code] = [
                            'barcode' => $log_asset->bar_code,
                            'staff_info_id' => $to_asset_info['staff_id'],
                            'num' => 1,
                            'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                            'type' => MaterialEnums::OPERATE_TYPE_BATCH_TRANSFER,
                            'transfer_remark' => $params['mark'] ?? '',
                            'created_at' => $now_time
                        ];
                    }
                }
                //记录转移记录-行信息
                $transfer_log = new MaterialAssetTransferLogModel();
                $bool = $transfer_log->batch_insert($transfer_log_data);
                if ($bool === false) {
                    throw new BusinessException('资产台账批量转移-记录转移行信息失败 可能的原因是=' . get_data_object_error_msg($transfer_log) . ';数据是=' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }
                //组装发送站内信员工组 && 记录资产汇总记录
                foreach ($batch_transfer_log_barcode_summary_data as $staff_info_id => $value) {
                    $need_send_msg_staff['staff_users'][] = ['id' => $staff_info_id];
                    foreach ($value as $item) {
                        $item['batch_id'] = $transfer_batch->id;
                        $db_barcode_summary_data[] = $item;
                    }
                }
                $batch_transfer_log_barcode_summary = new MaterialAssetTransferLogBarcodeSummaryModel();
                $bool = $batch_transfer_log_barcode_summary->batch_insert($db_barcode_summary_data);
                if ($bool === false) {
                    throw new BusinessException('资产台账-批量转移-记录资产批量变更提醒-资产汇总记录表失败 = ' . json_encode($db_barcode_summary_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($batch_transfer_log_barcode_summary), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }
                $db->commit();
                //发送站内信
                AssetTransferMessageService::getInstance()->sendPendingReceiptReminder([
                    'staff_users' => $need_send_msg_staff['staff_users'],
                    'message_content' => $transfer_batch->id,
                    'category' => MaterialEnums::MATERIAL_TRANSFER_MESSAGE_CATEGORY_TRANSFER_REMINDER
                ]);
            } else {
                throw new ValidationException(self::$t['material_asset_not_found'], ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-batch-transfer failed:' . $real_message . json_encode($params));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 获取特定条件下的各个barcode下的资产数量
     * @param array $params 参数组
     * @return array
     */
    public function getBarcodeAssetCount($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(id) as count, bar_code');
        $builder->from([MaterialAssetsModel::class]);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted'=>MaterialClassifyEnums::IS_DELETED_NO]);
        //按照barcode搜素
        if (!empty($params['barcode'])) {
            if (is_array($params['barcode'])) {
                $builder->inWhere('bar_code', $params['barcode']);
            } else {
                $builder->andWhere('bar_code=:barcode:', ['barcode'=> $params['barcode']]);
            }
        }
        //按照资产状态筛选
        if (!empty($params['status'])) {
            $builder->andWhere('status=:status:', ['status'=> $params['status']]);
        }
        $builder->groupBy('bar_code');
        $items_obj = $builder->getQuery()->execute();
        $items = $items_obj ? $items_obj->toArray() : [];
        $items = $items ? array_column($items, 'count', 'bar_code') : [];
        return $items;
    }

    /**
     * 资产台账-批量导入-批量报废弹出框数据接口
     * @Date: 11/17/22 3:50 PM
     * @return  array
     **/
    public function batchScrapLastData()
    {
        return  ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE);
    }

    /**
     * 资产台账-批量导入-导入修改财务弹出框数据接口
     * @Date: 11/17/22 3:50 PM
     * @return  array
     **/
    public function batchFinanceLastData()
    {
        return  ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_ASSET_ACCOUNT_FINANCE_UPDATE);
    }

    /**
     * 资产台账-批量导入-批量报废 资产台导入修改财务 写入任务
     * @Date: 11/17/22 4:43 PM
     * @param object $file 文件
     * @param array $excel_data 导入的数据
     * @param array $user 用户数据
     * @param int $type 类型 4 资产台账批量报废   5 资产台导入修改财务
     * @return  array
     **/
    public function addBatchUploadTask(object $file, array $excel_data, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //跳过空行
            $clear_excel_data = [];
            if ($type == ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE) {
                foreach ($excel_data as $key => $row) {
                    if (empty($row[0]) && empty($row[1]) && empty($row[2])) {
                        break;
                    }
                    $clear_excel_data[$key] = $row;
                }
            } else {
                foreach ($excel_data as $key => $row) {
                    if (empty($row[0]) && empty($row[1]) && empty($row[2]) && empty($row[3]) && empty($row[4])) {
                        break;
                    }
                    $clear_excel_data[$key] = $row;
                }
            }
            //验证条数
            if ($type == ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE) {
                $max_data_number = MaterialAssetApplyEnums::MATERIAL_ASSET_UPLOAD_SCRAP_MAX_DATA_NUMBER;
            } else {
                $max_data_number = MaterialAssetApplyEnums::MATERIAL_ASSET_UPLOAD_FINANCE_MAX_DATA_NUMBER;
            }
            if (count($clear_excel_data) > $max_data_number) {
                throw new ValidationException(self::$t['material_asset_upload_max_data_number'], ErrCode::$VALIDATE_ERROR);
            }
            // 文件生成OSS链接
            $file_path = sys_get_temp_dir() . '/' . $file->getName();
            $file->moveTo($file_path);
            $oss_result = OssHelper::uploadFile($file_path);
            if (!isset($oss_result['object_url']) || empty($oss_result['object_url'])) {
                throw new ValidationException(self::$t['file_upload_error'], ErrCode::$VALIDATE_ERROR);
            }
            // 导入中心
            $bool = ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], $type);
            if (!$bool) {
                throw new ValidationException(self::$t['add_import_center_error'], ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('资产台账-批量导入-' . $type == ImportCenterEnums::TYPE_ASSET_ACCOUNT_SCRAP_UPDATE ? '批量报废' : '资产台导入修改财务' . ' 写入任务失败 原因是 ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 资产台账-批量导入-批量修改
     * @param array $excel_data
     * @param array $user
     * @param bool $type true 作废修改 false 为财务修改
     * @return array
     * @date 2022/7/18
     */
    public function batchUpdateEdit(array $excel_data, array $user, bool $type)
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = $real_trace = '';
        $clear_excel_data = $correct_data = $error_data = [];
        //跳过空行
        if ($type) {
            foreach ($excel_data as $key => $row) {
                if ($row[0] == "" && $row[1] == "" && $row[2] == "") {
                    break;
                }
                $clear_excel_data[$key] = $row;
            }
        } else {
            foreach ($excel_data as $key => $row) {
                if ($row[0] == "" && $row[1] == "" && $row[2] == "" && $row[3] == "" && $row[4] == "" && $row[5] == "") {
                    break;
                }
                $clear_excel_data[$key] = $row;
            }
        }

        $result_data = $clear_excel_data;
        $datetime    = date('Y-m-d H:i:s');

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //excel数据转data
            $data           = $this->excelToDataEdit($clear_excel_data, $type);
            $asset_code_arr = array_column($data, 'asset_code');
            //查询本次修改数据
            $asset_code_data     = $this->isExistsAssetCodeBatch(array_values(array_unique($asset_code_arr)), $type);
            $asset_code_data_key = array_column($asset_code_data, null, 'asset_code');
            //数据校验
            $correct_and_error = $this->validationBatchEdit($data, $asset_code_arr, $asset_code_data_key, $type);
            $correct_data      = $correct_and_error['correct_data'];
            $error_data        = $correct_and_error['error_data'];
            //$correct_data和$error_data的key必须和$result_data对齐
            if ($correct_data || $error_data) {
                $msg_title_key = !empty($type) ? 3 : 6;//excel里面对应的结果位置
                foreach ($correct_data as $index => $correct_item) {
                    if (isset($correct_item['scrap_date']) && !empty($correct_item['scrap_date'])) {
                        $result_data[$index][1] = $correct_item['scrap_date'];
                    }
                    $result_data[$index][$msg_title_key] = self::$t['excel_result_success'];
                }
                foreach ($error_data as $err_index => $error_item) {
                    if (isset($error_item['scrap_date']) && !empty($error_item['scrap_date'])) {
                        $result_data[$err_index][1] = $error_item['scrap_date'];
                    }
                    $result_data[$err_index][$msg_title_key] = $error_item['error_message'];
                }
            }
            //更新主表数据
            if (!empty($correct_data)) {
                $after_update_data = $before_update_data = $asset_code = [];
                $asset_code_list   = [];
                $cost_company_arr  = (new PurchaseService())->getCooCostCompany();
                $company_data      = array_column($cost_company_arr, 'cost_company_id', 'cost_company_name');
                foreach ($correct_data as $k => $correct_value) {
                    $update_data = $attachment_data = [];
                    !empty($type) ? $update_data['status'] = MaterialEnums::ASSET_STATUS_SCRAPPED : [];
                    $update_id = [];
                    //主表要更新的数据
                    foreach ($correct_value as $field => $correct_item) {
                        if ($field == 'asset_code') {
                            continue;
                        }
                        if ($asset_code_data_key[$correct_value['asset_code']][$field] != $correct_item) {
                            if ($field == 'scrap_date') {
                                $update_data[$field] = $correct_item;
                            } else if ($field == 'currency') {
                                $update_data[$field] = explode('-', $correct_item)[0];
                            } else {
                                $update_data[$field] = $correct_item;
                            }
                        }
                        $asset_code_list = $asset_code_data_key[$correct_value['asset_code']]['asset_code'];
                    }
                    //组合修改后的数据
                    $before_update_data[$k] = $asset_code_data_key[$correct_value['asset_code']];
                    $after_update_data[$k]  = array_merge($asset_code_data_key[$correct_value['asset_code']], $update_data);
                    //更新主表
                    if (!empty($update_data)) {
                        $update_data['updated_at'] = $datetime;
                        if (!empty($update_data['company_name'])) {
                            $update_data['company_id'] = $company_data[trim($update_data['company_name'])];
                        }
                        $update_success = $db->updateAsDict(
                            (new MaterialAssetsModel())->getSource(),
                            $update_data,
                            ['conditions' => 'asset_code=?', 'bind' => $asset_code_list]
                        );

                        if (!$update_success) {
                            throw new BusinessException('更新数据异常,数据: id=' . $update_id . ';data=' . json_encode($update_data, true), ErrCode::$MATERIAL_ASSET_BATCH_SCRAP_UPDATE_ERROR);
                        }
                        $asset_code[] = $asset_code_list;
                    }
                }

                //批量插入操作记录
                $asset_update_log_model = new MaterialAssetUpdateLogModel();
                $operate_type           = $type ? MaterialEnums::OPERATE_TYPE_BATCH_CANCEL : MaterialEnums::OPERATE_TYPE_IMPORT_FINANCE;
                $log_bool               = $asset_update_log_model->batchInsertEditLog($asset_code, $before_update_data, $after_update_data, $user, $operate_type);
                if ($log_bool === false) {
                    throw new BusinessException('资产台账-批量导入-批量修改-操作记录失败 = ' . json_encode(array_column($after_update_data, 'id'), JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_BATCH_SCRAP_UPDATE_LOG_ERROR);
                }
                $this->logger->info('资产台账-批量导入-批量修改-修改前数据 :' . json_encode($before_update_data, JSON_UNESCAPED_UNICODE) . ' 修改后的数据 ' . json_encode($after_update_data, JSON_UNESCAPED_UNICODE));
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('资产台账-批量导入-批量修改-更新失败 :' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data   = $clear_excel_data;
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [
                'excel_data'  => $result_data,
                'all_num'     => count($clear_excel_data),
                'success_num' => count($correct_data),
                'failed_sum'  => count($error_data)
            ]
        ];
    }

    /**
     * 资产台账-批量导入-导入转移
     * @param array $excel_file 资产台账模版文件
     * @param array $user 当前登陆者信息
     * @return array
     * @throws BusinessException
     */
    public function batchImportTransfer($excel_file, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //资产台账excel
        $excel_header_column = $excel_data =  $batch_assets = $new_excel_data = $batch_transfer_log_data = [];
        //操作日志组
        $update_log_data = [];
        $db = $this->getDI()->get('db_oa');
        $start_time = get_curr_micro_time();
        $memory_usage_start = memory_usage();
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $db->begin();
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            $now_time = date('Y-m-d H:i:s');
            //检测文档合理性
            [$excel_header_column, $excel_data, $batch_assets, $batch_transfer_log_data, $batch_transfer_log_barcode_summary_data] = $this->checkBatchImportTransferData($excel_file, $now_time, $user);
            if (!empty($batch_assets) && !empty($batch_transfer_log_data)) {
                //当前操作人名称
                $operate_user_name = get_name_and_nick_name($user['name'], $user['nick_name']);

                //第一步记录转移记录-头信息
                $transfer_batch = new MaterialAssetTransferBatchModel();
                $batch_date = [
                    'staff_id' => $user['id'],
                    'staff_name' => $operate_user_name,
                    'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                    'status' =>MaterialEnums::TRANSFER_BATCH_STATUS_RECEIVED,
                    'mark' => '批量导入转移操作对闲置或使用中的资产做转移操作',
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $bool = $transfer_batch->i_create($batch_date);
                if ($bool === false) {
                    throw new BusinessException('资产台账-批量操作-导入转移-记录转移头信息失败 = ' . json_encode($transfer_batch, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($transfer_batch), ErrCode::$MATERIAL_ASSET_ACCOUNT_TRANSFER_ERROR);
                }

                //第二步组装批次ID到转移记录明细每一行中并插入转移日志行信息
                foreach ($batch_transfer_log_data as &$log_data) {
                    $log_data['batch_id'] = $transfer_batch->id;
                }
                $transfer_log = new MaterialAssetTransferLogModel();
                $bool = $transfer_log->batch_insert($batch_transfer_log_data);
                if ($bool === false) {
                    throw new BusinessException('资产台账-批量操作-导入转移-记录转移行信息失败 = ' . json_encode($batch_transfer_log_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($transfer_log), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }

                //第三步更新每一个资产台账信息
                foreach ($batch_assets as $item) {
                    $asset = $item['asset'];
                    $asset_update = $item['update'];
                    $log_data = $item['log'];
                    $bool = $asset->i_update($asset_update);
                    if ($bool === false) {
                        throw new BusinessException('资产台账-批量操作-导入转移-更新台账信息失败 = ' . json_encode($asset_update, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($asset), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_SAVE_ERROR);
                    }
                    //组装更新操作日志
                    $update_log_data[] = [
                        'asset_code' => $asset->asset_code,
                        'staff_id' => $user['id'],
                        'staff_name' => $operate_user_name,
                        'content' => json_encode($log_data, JSON_UNESCAPED_UNICODE),
                        'type' => MaterialEnums::OPERATE_TYPE_BATCH_IMPORT_TRANSFER,
                        'created_at' => $now_time,
                        'updated_at' => $now_time,
                    ];
                }

                //第四步记录每一个资产台账信息的操作日志
                if ($update_log_data) {
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $log_bool = $update_log_model->batch_insert($update_log_data);
                    if ($log_bool === false) {
                        throw new BusinessException('资产台账-批量操作-导入转移-记录资产台账信息的操作记录失败 = ' . json_encode($update_log_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_SAVE_ERROR);
                    }
                }

                //第五步组装发送站内信员工组 && 记录资产汇总记录
                $need_send_msg_staff = [];
                $db_barcode_summary_data = [];
                if ($batch_transfer_log_barcode_summary_data) {
                    foreach ($batch_transfer_log_barcode_summary_data as $staff_info_id => $value) {
                        $need_send_msg_staff['staff_users'][] = ['id' => $staff_info_id];
                        foreach ($value as $item) {
                            $item['batch_id'] = $transfer_batch->id;
                            $db_barcode_summary_data[] = $item;
                        }
                    }
                    $batch_transfer_log_barcode_summary = new MaterialAssetTransferLogBarcodeSummaryModel();
                    $bool = $batch_transfer_log_barcode_summary->batch_insert($db_barcode_summary_data);
                    if ($bool === false) {
                        throw new BusinessException('资产台账-批量操作-导入转移-记录资产批量变更提醒-资产汇总记录表失败 = ' . json_encode($db_barcode_summary_data, JSON_UNESCAPED_UNICODE). '; 可能存在的问题: ' . get_data_object_error_msg($batch_transfer_log_barcode_summary), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                    }
                }
                $db->commit();

                //存在要发送消息的员工组则发送
                if ($need_send_msg_staff) {
                    $need_send_msg_staff['message_title'] = get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? 'แจ้งเตือนปริมาณการเปลี่ยนแปลงของสินทรัพย์ 资产批量变更提醒' : 'Asset batch change alert 资产批量变更提醒';
                    $need_send_msg_staff['message_content'] = $transfer_batch->id;
                    $need_send_msg_staff['category'] = MaterialEnums::MESSAGE_CATEGORY_TRANSFER_NOTICE;
                    $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
                    $bi_rpc->setParams([$need_send_msg_staff]);
                    $res = $bi_rpc->execute();
                    $this->logger->info('batchImportTransfer 给【' . implode(',', array_column($need_send_msg_staff['staff_users'], 'id')) . '】发送资产批量变更提醒' . (isset($res['result']['code']) && $res['result']['code'] == ErrCode::$SUCCESS ? '成功' : '失败'));
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material-asset-batch-import-transfer-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR])) {
            //非验证类或系统错误的提示，需要生成下载模版
            $success_num = 0;
            foreach ($excel_data as $item) {
                if ($code != ErrCode::$SUCCESS && strpos($item[self::MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT], 'success') !== false) {
                    $item[self::MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT] = "";
                } else if (strpos($item[self::MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT], 'success') !== false) {
                    $success_num++;
                }
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, "导入转移（Transfer）-" . date("YmdHis"));
            //内容包含模版第二行表头，所以需要总数-1
            $all_num = count($new_excel_data)-1;
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => bcsub($all_num, $success_num)
            ];
            $code = ErrCode::$SUCCESS;
            $end_time = get_exec_time($start_time);
            $this->logger->info('批量导入转移[' . $all_num . ']条，预计耗时:[ ' . $end_time . ' ]');
            $memory_usage_end = memory_usage();
            $this->logger->info('批量导入转移[' . $all_num . ']条，初始内存:[ ' . $memory_usage_start . ' ], 当前内存:[ ' . $memory_usage_end . ' ]，结果为：' . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR]) ? $message : 'success',
            'data'    => $data ?? [],
        ];
    }

    /**
     * EXCEL数据转为关联数组(导入修改)
     * @param array $excel_data
     * @param bool $type true 作废修改 false 为财务修改
     * @return array
     */
    public function excelToDataEdit(array $excel_data, bool $type = true)
    {
        if ($type) {
            $data_key = [
                'asset_code',
                'scrap_date',
                'mark'
            ];
        } else {
            $data_key = [
                'asset_code',
                'purchase_price',
                'net_value',
                'currency',
                'pc_code',
                'company_name'
            ];
        }

        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'], ErrCode::$VALIDATE_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        foreach ($data as $key => &$value) {
            //空值不进行修改
            foreach ($value as $field => $v) {
                if ($v == '') {
                    unset($value[$field]);
                }
            }
        }
        return $data;
    }

    /**
     * 批量检测asset_code是否存在
     * @param array $asset_code_arr
     * @param bool $type true 作废修改 false 为财务修改
     * @return array
     */
    public function isExistsAssetCodeBatch(array $asset_code_arr, bool $type)
    {
        if (empty($asset_code_arr)) {
            return [];
        }
        $batch_nums = 1000;//分批处理   一批1000个
        $columns    = !empty($type) ? 'id, asset_code, scrap_date, mark, status' : 'id, asset_code, purchase_price, net_value, currency, pc_code, company_name, status';

        $count = count($asset_code_arr);
        $page  = ceil($count / $batch_nums);
        $data  = [];
        for ($i = 0; $i < $page; $i++) {
            $index           = $i * $batch_nums;
            $asset_code_data = array_slice($asset_code_arr, $index, $batch_nums);
            $item            = MaterialAssetsModel::find([
                'columns'    => $columns,
                'conditions' => 'asset_code in ({asset_code:array}) and is_deleted=:is_deleted:',
                'bind'       => ['asset_code' => array_values($asset_code_data), 'is_deleted' => MaterialClassifyEnums::IS_DELETED_NO]
            ])->toArray();
            $data            = array_merge($data, $item);
        }

        return $data;
    }


    /**
     * 导入修改
     * 验证数据, 错误信息放到error_message
     * @param array $data excel中的数据
     * @param array $asset_code_arr excel中的所有asset_code
     * @param array $code_data_kv 本次excel在表中的数据
     * @param $type  true 报废校验   false  其他
     * @return mixed
     */
    public function validationBatchEdit($data, $asset_code_arr, $code_data_kv, $type = true)
    {
        $correct_data = $error_data = $cost_company_kv = [];
        if (empty($type)) {
            //校验所属公司是否是组织架构里COO、CEO底下的BU
            $company_list    = (new PurchaseService())->getCooCostCompany();
            $cost_company_kv = array_column($company_list, 'cost_company_name', 'cost_company_id');
        }
        foreach ($data as $k => &$v) {
            $error_message = '';
            //barcode验证
            if (empty($v['asset_code'])) {
                $error_message .= self::$t['error_message_edit_asset_code_required'] . ' ; ';
            } else {
                //验证文件中asset_code是否存在
                $asset_code_numbers = count(array_keys($asset_code_arr, $v['asset_code']));

                if ($asset_code_numbers > 1) {
                    $error_message .= self::$t['error_message_asset_code_inner_repeat'] . ' ; ';
                }
                if (empty($code_data_kv) || !key_exists($v['asset_code'], $code_data_kv)) {
                    $error_message .= self::$t['error_message_asset_code_db_not_exist'] . ' ; ';
                }
            }
            if (!empty($v['asset_code']) && !empty($code_data_kv[$v['asset_code']]['status']) && $code_data_kv[$v['asset_code']]['status'] == MaterialEnums::ASSET_STATUS_SCRAPPED) {
                $error_message .= self::$t['material_asset_save_error_010'] . ' ; ';
            }

            if ($type) {
                //作废校验
                if (!isset($v['scrap_date']) || empty($v['scrap_date'])) {
                    $error_message .= self::$t['error_message_edit_scrap_date_required'] . ' ; ';
                } else {
                    //验证scrap_date格式
                    $real_arrival_date = $this->handleUploadFileDate($v['scrap_date']);
                    if (!preg_match(MaterialEnums::MATERIAL_ASSET_BATCH_ADD_DATE, $real_arrival_date) || strtotime($real_arrival_date) > strtotime(date('Y-m-d'))) {
                        $error_msg[] = self::$t['material_asset_arrival_date_error'];
                    }
                    $v['scrap_date'] = $real_arrival_date;
                }


                if (isset($v['mark'])) {
                    if (mb_strlen($v['mark']) > 500) {
                        $error_message .= self::$t['material_asset.mark'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
                    }
                }

                //财务修改校验
                if (!empty($v['asset_code']) && !empty($code_data_kv[$v['asset_code']]['status']) && $code_data_kv[$v['asset_code']]['status'] != MaterialEnums::ASSET_STATUS_HANDLED) {
                    $error_message .= self::$t['error_message_status_error'] . ' ; ';
                }

            } else {
                $error_msg = '';
                //采购价
                if (isset($v['purchase_price'])) {
                    if (!is_numeric($v['purchase_price']) || (bccomp($v['purchase_price'], 0, 2) == -1) || (bccomp($v['purchase_price'], 999999999999.99, 2) == 1)) {
                        $error_message .= self::$t['error_message_all_save_purchase_price_error'] . ' ; ';
                    }

                }
                //净值
                if (isset($v['net_value'])) {
                    if (!is_numeric($v['net_value']) || (bccomp($v['net_value'], 0, 2) == -1) || (bccomp($v['net_value'], 999999999999.99, 2) == 1)) {
                        $error_message .= self::$t['error_message_all_save_net_value_error'] . ' ; ';
                    }
                }

                if (isset($v['net_value']) && isset($v['purchase_price'])) {
                    if (bccomp($v['net_value'], $v['purchase_price'], 2) == 1) {
                        $error_msg = self::$t['error_message_net_value_by_purchase_price_error'] . ' ; ';
                    }
                }

                if (!isset($v['net_value']) && isset($v['purchase_price'])) {
                        $error_msg = self::$t['error_message_net_value_and_purchase_price_error'] . ' ; ';
                }
                if (isset($v['net_value']) && !isset($v['purchase_price'])) {
                        $error_msg = self::$t['error_message_net_value_and_purchase_price_error'] . ' ; ';
                }

                $error_message .= $error_msg;
                //币种
                if (isset($v['currency'])) {
                    $currency = explode('-', $v['currency'])[0];
                    if (!key_exists($currency, GlobalEnums::$currency_item)) {
                        $error_message .= self::$t['error_message_currency_error'] . ' ; ';
                    }
                }

                //成本中心
                if (isset($v['pc_code'])) {
                    if (mb_strlen($v['pc_code']) > 255) {
                        $error_message .= self::$t['material_asset.pc_code'] . self::$t['error_message_beyond_maximum_length'] . ' ; ';
                    }
                }

                //所属公司
                if (isset($v['company_name'])) {
                    if (!in_array(trim($v['company_name']), $cost_company_kv)) {
                        $error_message .= self::$t['expense_company'] . self::$t['error_message_company_name_error'] . ' ; ';
                    }
                }

                if (!isset($v['purchase_price']) && !isset($v['net_value']) && !isset($v['currency']) && !isset($v['pc_code']) && !isset($v['company_name'])) {
                    $error_message .= self::$t['error_message_all_net_value_error'] . ' ; ';
                }
            }
            //拼接获取错误信息,和正确信息
            if ($error_message !== '') {
                $v['error_message'] = self::$t['error_message_title'] . ' : ' . $error_message;
                $error_data[$k]     = $v;
            } else {
                $correct_data[$k] = $v;
            }
        }
        return ['correct_data' => $correct_data, 'error_data' => $error_data];
    }


    /**
     * 从oss链接url中截取oss信息  必须是oss地址
     * @param string $url oss文件链接
     * @return array
     * @date 2022/11/28
     */
    public function getOssInfoByUrl($url)
    {
        $oss_info = [
            'bucket_name' => '',
            'object_key'  => '',
            'file_name'   => '',
        ];
        //取bucket_name 把开头的http://去掉
        $have_http   = stripos($url, 'http://');
        $have_https  = stripos($url, 'https://');
        $first_point = stripos($url, '.');
        if ($have_http !== false) {
            $start_index             = $have_http + mb_strlen('http://');
            $end_index               = $first_point - $have_http;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } elseif ($have_https !== false) {
            $start_index             = $have_https + mb_strlen('https://');
            $end_index               = $first_point - $start_index;
            $oss_info['bucket_name'] = mb_substr($url, $start_index, $end_index);
        } else {
            $oss_info['bucket_name'] = mb_substr($url, 0, $first_point);
        }
        //取object_key, 取第一个.com/后边的所有字符
        $bucket_name_index      = stripos($url, '.com/');
        $oss_info['object_key'] = mb_substr($url, $bucket_name_index + mb_strlen('.com/'));
        //拼接file_name, 使用md5后的object_key, 再拼上原来的后缀
        $file_name_str         = md5($oss_info['object_key']);
        $oss_info['file_name'] = 'import_' . $file_name_str;
        $extension             = strchr($oss_info['object_key'], '.');
        $oss_info['file_name'] = $oss_info['file_name'] . $extension;
        return $oss_info;
    }


    /**
     * 处理校验
     * @Date: 11/30/22 12:02 PM
     * @throws  ValidationException
     * @return  array
     **/
    public function batchFinanceCheck()
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException(self::$t['bank_flow_not_found_file'], ErrCode::$VALIDATE_ERROR);
        }
        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if ($extension != MaterialAssetApplyEnums::MATERIAL_ASSET_UPLOAD_FILE_SUFFIX) {
            throw new ValidationException(self::$t['file_format_error'], ErrCode::$VALIDATE_ERROR);
        }
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType([
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                4 => \Vtiful\Kernel\Excel::TYPE_STRING,
                5 => \Vtiful\Kernel\Excel::TYPE_STRING
            ])
            ->getSheetData();
        array_shift($excel_data); //删除表头
        array_shift($excel_data); //删除表头
        // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
        if (empty($excel_data[0][0]) && empty($excel_data[0][1]) && empty($excel_data[0][2]) && empty($excel_data[0][3]) && empty($excel_data[0][4]) && empty($excel_data[0][4])) {
            throw new ValidationException(self::$t['data_empty_or_read_data_failed'], ErrCode::$VALIDATE_ERROR);
        }

        return [$excel_data, $file];
    }


    /**
     * 处理校验
     * @Date: 11/30/22 12:02 PM
     * @return  array
     * @throws ValidationException
     */
    public function batchScrapCheck()
    {
        // 文件格式校验
        if (!$this->request->hasFiles()) {
            throw new ValidationException(self::$t['bank_flow_not_found_file'], ErrCode::$VALIDATE_ERROR);
        }
        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();

        if ($extension != MaterialAssetApplyEnums::MATERIAL_ASSET_UPLOAD_FILE_SUFFIX) {
            throw new ValidationException(self::$t['file_format_error'], ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType([
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,// assetcode
                1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,
                2 => \Vtiful\Kernel\Excel::TYPE_STRING
            ])
            ->getSheetData();
        array_shift($excel_data); //删除表头
        array_shift($excel_data); //删除表头
        // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
        if (empty($excel_data[0][0]) && empty($excel_data[0][1]) && empty($excel_data[0][2])) {
            throw new ValidationException(self::$t['data_empty_or_read_data_failed'], ErrCode::$VALIDATE_ERROR);
        }
        return [$excel_data, $file];
    }


     /**
     * 检测资产台账-批量-导入转移数据合理性
     * @param array $excel_file excel文件内容
     * @param string $now_time 操作时间
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function checkBatchImportTransferData(array $excel_file, string $now_time, $user)
    {
        //根据资产编码筛选出来的资产台账信息组
        $asset_arr = [];
        //根据新使用人筛选出来的使用人信息组
        $staff_arr = [];
        //新使用人所属部门信息组
        $department_arr = [];
        //成本中心信息组
        $pc_code_arr = [];
        //资产台账相关信息组
        $batch_assets = [];
        //转移记录行信息组装
        $batch_transfer_log_data = [];
        //转移记录行各员工各barcode需发送消息的资产汇总组
        $batch_transfer_log_barcode_summary = [];
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        //弹出excel标题第二行信息
        $second_header = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('material_asset_data_error'), ErrCode::$VALIDATE_ERROR);
        }
        if (count($excel_data) > MaterialEnums::MATERIAL_ASSET_BATCH_IMPORT_TRANSFER_LIMIT) {
            throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max' => MaterialEnums::MATERIAL_ASSET_BATCH_IMPORT_TRANSFER_LIMIT]), ErrCode::$VALIDATE_ERROR);
        }
        //用于记录重复的资产编码组
        $repeat_asset_code_arr     = [];
        $excel_staff_validate_list = [];
        $to_store_name_arr         = [];
        $excel_data                = $this->excelTransferToDataEdit($excel_data);
        $fromStaff                 = array_column($excel_data, 'from_staff_id');
        $toStaff                   = array_column($excel_data, 'to_staff_id');
        $allStaff                  = array_values(array_unique(array_merge($fromStaff, $toStaff)));
        $staffData                 = [];
        if (!empty($allStaff)) {
            //查询员工协议公司
            $staffData = HrStaffInfoModel::find([
                'columns'    => 'staff_info_id, contract_company_id',
                'conditions' => 'staff_info_id in ({ids:array})',
                'bind'       => ['ids' => $allStaff],
            ])->toArray();
            $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
        }
        //获取公司名称
        $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

        foreach ($excel_data as $key => &$item) {
            //资产编码
            $asset_code = isset($item['asset_code']) ? trim($item['asset_code']) : '';
            //新使用人工号
            $to_staff_id = isset($item['to_staff_id']) ? trim($item['to_staff_id']) : '';
            if (empty($asset_code)) {
                continue;
            }
            if (!isset($repeat_asset_code_arr[$asset_code])) {
                $repeat_asset_code_arr[$asset_code] = 1;
            } else {
                $repeat_asset_code_arr[$asset_code] += 1;
            }
            $excel_staff_validate_list[$to_staff_id]['is_send_msg'][] = $item['is_send_msg'] ?? '';
            $to_store_name_arr[] = $item['to_store_name'];
        }

        //用于判断所属公司
        $company_list = (new PurchaseService())->getCooCostCompany();
        //新使用网点列表
        $search_store_params = ['use_state' => 1, 'not_contain_category' => 6, 'store_name' => array_values(array_unique(array_filter($to_store_name_arr)))];
        //20900 菲律宾不过滤营业状态
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE) {
            unset($search_store_params['use_state']);
        }
        $to_store_list = (new StoreService())->getSysStoreListByCondition($search_store_params);
        array_unshift($to_store_list, ['id' => Enums::HEAD_OFFICE_STORE_FLAG, 'name' => Enums::PAYMENT_HEADER_STORE_NAME]);
        $to_store_list = array_column($to_store_list, null, 'name');
        //验证每项元素是否符合标准
        foreach ($excel_data as $key => &$item) {
            $error_msg = [];
            //资产编码
            $asset_code = isset($item['asset_code']) ? trim($item['asset_code']) : '';
            //新使用人工号
            $to_staff_id = isset($item['to_staff_id']) ? trim($item['to_staff_id']) : '';
            //资产编码、新使用人工号必填
            if (!empty($asset_code) && !empty($to_staff_id)) {
                //资产编码重复性以及是否存在资产台账
                if (!isset($asset_arr[$asset_code]) && $repeat_asset_code_arr[$asset_code] == 1) {
                    //不存在则去查询
                    $asset_info = $this->getMaterialAssetInfoByAssetCode($asset_code);
                    if (empty($asset_info)) {
                        $asset_arr[$asset_code] = false;
                        $error_msg[] = self::$t['material_asset_save_error_001'];
                    } else {
                        //判断使用状态必须是闲置或者使用中或者闲置（在网点）
                        if (!in_array($asset_info->status, [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_UNUSED_IN_STORE])) {
                            $asset_arr[$asset_code] = false;
                            $error_msg[] = self::$t['material_asset_transfer_status_error'];
                        } else {
                            //将正确的资产编码放进去
                            $asset_arr[$asset_code] = $asset_info;
                        }
                    }
                } else {
                    $asset_arr[$asset_code] = false;
                    $error_msg[] = self::$t['material_asset_save_error_008'];
                }

                //新使用人信息检测
                if ($asset_arr[$asset_code] !== false && $asset_arr[$asset_code]->staff_id == $to_staff_id) {
                    //转移前后使用人不可一致
                    $error_msg[] = self::$t['material_asset_transfer_staff_error'];
                } else {
                    //员工信息组不存在则查找
                    if (!isset($staff_arr[$to_staff_id])) {
                        $staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $to_staff_id, 'limit' => 1]);
                        if (!empty($staff_list['data'])) {
                            $staff_arr[$to_staff_id] = $staff_list['data'][0];
                            //部门ID
                            $department_id = $staff_arr[$to_staff_id]['node_department_id'];
                            if (!isset($department_arr[$department_id])) {
                                //部门所属公司组不存在则查找
                                $department_info = $this->getCompanyByDepartment(['department_id' => $department_id]);
                                //使用人所属部门信息存在，并且是BU级下所属公司
                                if (!empty($department_info['data']) && $department_info['data']['department_id'] && !empty($department_info['data']['company_id'])) {
                                    $department_arr[$department_id] = $department_info['data'];
                                } else {
                                    //将错误的部门信息放入数组避免重复查询错误部门
                                    $department_arr[$department_id] = false;
                                    $error_msg[100] = self::$t['material_asset_batch_import_transfer_error_002'];
                                }
                            }
                            if ($department_arr[$department_id] === false) {
                                $error_msg[100] = self::$t['material_asset_batch_import_transfer_error_002'];
                            }
                            //获取成本中心
                            $sys_store_id = $staff_arr[$to_staff_id]['sys_store_id'];
                            if ($sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG) {
                                //总部
                                $pc_id = $department_id;
                                $type = StoreRentingAddService::getInstance()::COST_TYPE_HEAD_OFFICE;
                            } else {
                                //网点
                                $pc_id = $sys_store_id;
                                $type = StoreRentingAddService::getInstance()::COST_TYPE_SYS_STORE;
                            }
                            if (!isset($pc_code_arr[$pc_id])) {
                                $pc_code_arr[$pc_id] = StoreRentingAddService::getInstance()->getPcCode($pc_id, $type);
                            }
                            $staff_arr[$to_staff_id]['pc_code'] = !empty($pc_code_arr[$pc_id]['data']) ? ($pc_code_arr[$pc_id]['data']['pc_code'] ?? '') : '';
                        } else {
                            //将错误的员工信息放入数组，避免重复查询
                            $staff_arr[$to_staff_id] = false;
                        }
                    }

                    if ($staff_arr[$to_staff_id] === false) {
                        $error_msg[] = self::$t['material_asset_add_error_006'];
                    } else {
                        //若存在新使用人信息，需要判断改新使用人所属公司是否bu级下
                        if ($department_arr[$staff_arr[$to_staff_id]['node_department_id']] === false) {
                            $error_msg[100] = self::$t['material_asset_batch_import_transfer_error_002'];
                        }
                    }
                }
            } else {
                $error_msg[] = self::$t['material_asset_batch_import_transfer_error_001'];
                if (!empty($asset_code)) {
                    //针对不满足必填校验，但是填写了资产编码，要记录用于判断文档中资产编码的重复性
                    $asset_arr[$asset_code] = false;
                }
            }

            //新使用网点名称[填写的新使用网点只能是Head Office，或者营业且激活，且网点类型不为flash home请检查！]
            $to_store_name = isset($item['to_store_name']) ? trim($item['to_store_name']) : '';
            if (!empty($to_store_name) && !isset($to_store_list[$to_store_name])) {
                $error_msg[] = self::$t['material_asset_batch_import_transfer_error_005'];
            }

            //新使用地
            $use_land = isset($item['use_land']) ? trim($item['use_land']) : '';
            if (!empty($use_land) && mb_strlen($use_land) > 100) {
                $error_msg[] = self::$t['material_asset_batch_import_transfer_error_003'];
            }

            //所属公司名称
            $company_name = isset($item['company_name']) ? trim($item['company_name']) : '';
            //所属公司ID
            $company_id = 0;
            if (!empty($company_name) && !empty($company_list)) {
                //若存在"失败，新使用人没有所属公司，请在此行补充新所属公司。"提示，需要清空掉，依据填写的所属公司为准
                unset($error_msg[100]);
                $cost_company_kv = array_column($company_list,'cost_company_id','cost_company_name');
                if (key_exists($company_name, $cost_company_kv)) {
                    $company_id = $cost_company_kv[$company_name];
                } else {
                    $error_msg[] = self::$t['material_asset_save_error_004'];
                }
            }
            $transfer_remark = isset($item['transfer_remark']) ? mb_substr(trim($item['transfer_remark']), 0, 1000) : '';

            //是否发送BY消息
            $is_send_msg = isset($item['is_send_msg']) ? trim($item['is_send_msg']) : '';
            if (count(array_unique($excel_staff_validate_list[$to_staff_id]['is_send_msg'])) > 1 || !in_array($is_send_msg, ['Y', 'N'])) {
                $error_msg[] = self::$t['material_asset_batch_import_transfer_error_004'];
            }

            if ($error_msg) {
                //存在错误信息，则要赋予备注信息
                $item[self::MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT] = implode(";", array_unique($error_msg));
            } else {
                $asset = $asset_arr[$asset_code];
                $staff = $staff_arr[$to_staff_id];
                $log_data = [];
                //导入成功后，需要更新资产台账的信息：使用人工号（文件里填写的新使用人工号），
                //使用网点（新使用人工号所属网点），使用部门（新使用人工号所属部门），
                //所属公司（文件里填写了，则取文件里正确填写的公司；没填写，取新使用人所属的公司），
                //成本中心（如果新使用人是总部的，则去新部门在组织结构里维护的成本中心，否则取所属网点在Ms里对应的成本中心，上述2个都取不到则为空），
                //使用地（文件填写了，则以文件的为准，否则带入新所属网点）
                //V19094需求，新使用人（即接收人）的所属网点=总部时，若资产上的使用网点非空，仍为原先网点，若传递了使用地则用传递，未传递若原资产使用地非空&&资产上的使用网点非空则不变化；其他情况均为新使用人所属网点名称
                //V21154如果新使用网点名称没有填写，则取新使用人的所属网点，更新到转移记录里的新使用网点字段，需要更新到资产台账上的新使用网点字段
                $update_asset = [
                    'staff_id' => $to_staff_id,
                    'staff_name' => $staff['staff_name'],
                    'sys_store_id' => $to_store_name ? $to_store_list[$to_store_name]['id'] : $staff['sys_store_id'],
                    'store_name' => $to_store_name ? $to_store_name : $staff['store_name'],
                    'node_department_id' => $staff['node_department_id'],
                    'node_department_name' => $staff['node_department_name'],
                    'use_land' => $use_land ? $use_land : ($to_store_name ? $to_store_name : $staff['store_name']),
                    'pc_code' => $staff['pc_code'],
                    'company_id' => $company_id ? $company_id : $department_arr[$staff['node_department_id']]['company_id'],
                    'company_name' => $company_name ? $company_name : $department_arr[$staff['node_department_id']]['company_name'],
                    'status' => MaterialEnums::ASSET_STATUS_USING,
                    'updated_at' => $now_time,
                    'job_id' => !empty($staff['job_id']) ? $staff['job_id'] : 0,//职位ID
                    'job_name' => !empty($staff['job_name']) ? $staff['job_name'] : '',//职位名称
                    'receipted_at' => $now_time,
                    'state' => $staff['state'],
                    'wait_leave_state' => $staff['wait_leave_state'],
                    'leave_date' => $staff['leave_date']
                ];
                //操作日志记录
                $can_update_edit_field = array_keys($update_asset);
                foreach ($can_update_edit_field as $field) {
                    $update_filed_val = $update_asset[$field];
                    if (!empty($update_filed_val) && $update_filed_val != $asset->$field) {
                        $log_data[] = [
                            'before' => $asset->$field,
                            'after' => $update_filed_val,
                            'field_name' => $field
                        ];
                    }
                }
                $batch_assets[] = [
                    'asset' => $asset,
                    'update' => $update_asset,
                    'log' => $log_data
                ];
                //转移记录行信息组装
                $batch_transfer_log_data[] = [
                    'asset_id' => $asset->id,
                    'barcode' => $asset->bar_code,
                    'asset_code' => $asset->asset_code,
                    'from_staff_id' => $asset->staff_id,
                    'from_staff_name' => $asset->staff_name,
                    'from_node_department_id' => $asset->node_department_id,
                    'from_node_department_name' => $asset->node_department_name,
                    'from_sys_store_id' => $asset->sys_store_id,
                    'from_store_name' => $asset->store_name,
                    'from_pc_code' => $asset->pc_code,
                    'from_use_land' => $asset->use_land,
                    'from_company_id' => $asset->company_id,
                    'from_company_name' => $asset->company_name,
                    'from_contract_company_id' => $staffData[$asset->staff_id] ?? 0,
                    'from_contract_company_name' => $companyList[$staffData[$asset->staff_id] ?? 0] ?? '',
                    'to_staff_id' => $update_asset['staff_id'],
                    'to_staff_name' => $update_asset['staff_name'],
                    'to_node_department_id' => $update_asset['node_department_id'],
                    'to_node_department_name' => $update_asset['node_department_name'],
                    'to_sys_store_id' => $update_asset['sys_store_id'],
                    'to_store_name' => $update_asset['store_name'],
                    'to_pc_code' => $update_asset['pc_code'],
                    'to_use_land' => $update_asset['use_land'],
                    'to_company_id' => $update_asset['company_id'],
                    'to_company_name' =>$update_asset['company_name'],
                    'to_contract_company_id' => $staffData[$update_asset['staff_id']] ?? 0,
                    'to_contract_company_name' => $companyList[$staffData[$update_asset['staff_id']] ?? 0] ?? '',
                    'finished_at' => $now_time,
                    'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                    'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                    'from_use_status' => $asset->status,
                    'transfer_at' => $now_time,
                    'operator_id' => $update_asset['staff_id'],
                    'transfer_operator_id' => $user['id'],
                    'transfer_method' => 0,
                    'auto_recipient' => MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_YES,
                    'transfer_operator_name' => $user['name'],
                    'transfer_remark' => $transfer_remark,
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
                $item[self::MATERIAL_BATCH_IMPORT_TRANSFER_EXCEL_RESULT] = 'success';
                if ($is_send_msg == 'Y') {
                    if (isset($batch_transfer_log_barcode_summary[$to_staff_id][$asset->bar_code])) {
                        $batch_transfer_log_barcode_summary[$to_staff_id][$asset->bar_code]['num'] += 1;
                    } else {
                        $batch_transfer_log_barcode_summary[$to_staff_id][$asset->bar_code] = [
                            'barcode' => $asset->bar_code,
                            'staff_info_id' => $to_staff_id,
                            'num' => 1,
                            'transfer_type' => MaterialEnums::TRANSFER_TYPE_ASSET_DEPARTMENT,
                            'type' => MaterialEnums::OPERATE_TYPE_BATCH_IMPORT_TRANSFER,
                            'transfer_remark' => $transfer_remark,
                            'created_at' => $now_time
                        ];
                    }
                }
            }
        }
        return [$excel_header_column, array_merge([$second_header], $excel_data), $batch_assets, $batch_transfer_log_data, $batch_transfer_log_barcode_summary];
    }

    /**
     * 批量导入转移处理excel数据
     * @param array $excel_data excel导入数据
     * @return array
     */
    private function excelTransferToDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'asset_code',//资产编码
            'from_staff_id',//原使用人工号
            'to_staff_id',//新使用人工号
            'to_store_name',//新使用网点名称
            'use_land',//新使用地
            'company_name',//新所属公司名称
            'transfer_remark',//备注
            'is_send_msg',//是否发送BY消息
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                $data[$k][$key] = trim($v[$index]);
            }
        }
        return $data;
    }


    /**
     * 资产台账-批量退库
     * @param array $params 资产台账-批量退库-表单
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function batchStockReturn($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $db->begin();
            $param_asset = $params['assets'];
            //根据传递的资产ID && 非删除 && 闲置，使用中，维修中，已报修
            $origin_assets = MaterialAssetsModel::find([
                'conditions'=>'id in ({ids:array}) and status in ({status:array}) and is_deleted = :is_deleted:',
                'bind' => ['ids' => $param_asset, 'status' => [MaterialEnums::ASSET_STATUS_UNUSED, MaterialEnums::ASSET_STATUS_USING, MaterialEnums::ASSET_STATUS_REPAIRED, MaterialEnums::ASSET_STATUS_REPAIRING], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            $asset_list_arr = $origin_assets->toArray();
            if (empty($asset_list_arr) || count($asset_list_arr) != count($param_asset)) {
                throw new ValidationException(self::$t['material_asset_stock_return_error'], ErrCode::$VALIDATE_ERROR);
            }

            //验证都通过了，开始入库操作
            $now_time = date('Y-m-d H:i:s', time());//操作时间
            $transfer_log_data = [];//转移记录-行信息
            $transfer_batch = new MaterialAssetTransferBatchModel(); //记录转移记录-头信息
            $batch_data = [
                'staff_id' => $user['id'],
                'staff_name' => get_name_and_nick_name($user['name'], $user['nick_name']),
                'type' => MaterialEnums::TRANSFER_TYPE_BATCH,
                'status' => MaterialEnums::TRANSFER_BATCH_STATUS_RECEIVED,
                'mark' => '',
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $transfer_batch->i_create($batch_data);
            if ($bool === false) {
                throw new BusinessException('资产台账批量退库-记录转移头信息失败 可能的原因是=' . get_data_object_error_msg($transfer_batch) . ';数据是=' . json_encode($batch_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
            }
            $to_asset = array_column($param_asset, null, 'asset_id');
            $fromStaff = array_column($asset_list_arr, 'staff_id');
            $staffData = [];
            if(!empty($fromStaff)){
                //查询员工协议公司
                $staffData = HrStaffInfoModel::find([
                    'columns' => 'staff_info_id, contract_company_id',
                    'conditions' => 'staff_info_id in ({ids:array})',
                    'bind' => ['ids' => $fromStaff]
                ])->toArray();
                $staffData = empty($staffData) ? [] : array_column($staffData, 'contract_company_id', 'staff_info_id');
            }
            //获取公司名称
            $companyList = EnumsService::getInstance()->getPayrollCompanyInfo();

            //开始处理资产转移
            foreach ($origin_assets as $asset) {
                //操作日志对象
                $log_asset = clone $asset;
                //修改资产台账信息
                $asset_update = [
                    'status' => MaterialEnums::ASSET_STATUS_UNUSED,//使用状态-闲置
                    'staff_id' => 0,//使用人工号
                    'staff_name' => '',//使用人姓名
                    'job_id' => 0,//职位id
                    'job_name' => '',//职位名称
                    'state' => 0,//在职状态
                    'wait_leave_state' => 0,//待离职状态
                    'updated_at' => $now_time
                ];
                $bool = $asset->i_update($asset_update);
                if ($bool === false) {
                    throw new BusinessException('资产台账批量退库失败 可能的原因是=' . get_data_object_error_msg($asset) . ';数据是=' . json_encode($asset_update, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }

                //记录操作日志
                $update_log_model = new MaterialAssetUpdateLogModel();
                $log_bool = $update_log_model->dealEditField(MaterialEnums::OPERATE_TYPE_BATCH_STOCK_RETURN, $log_asset, $asset_update, $user);
                if ($log_bool === false) {
                    throw new BusinessException('资产台账批量退库失败-操作记录失败 = ' . json_encode($asset->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
                }

                //转移记录行信息组装
                $transfer_log_data[] = [
                    'batch_id' => $transfer_batch->id,
                    'asset_id' => $log_asset->id,
                    'barcode' => $log_asset->bar_code,
                    'asset_code' => $log_asset->asset_code,
                    'from_staff_id' => $log_asset->staff_id,
                    'from_staff_name' => $log_asset->staff_name,
                    'from_node_department_id' => $log_asset->node_department_id,
                    'from_node_department_name' => $log_asset->node_department_name,
                    'from_sys_store_id' => $log_asset->sys_store_id,
                    'from_store_name' => $log_asset->store_name,
                    'from_pc_code' => $log_asset->pc_code,
                    'from_use_land' => $log_asset->use_land,
                    'from_company_id' => $log_asset->company_id,
                    'from_company_name' => $log_asset->company_name,
                    'from_contract_company_id' => $staffData[$log_asset->staff_id] ?? 0,
                    'from_contract_company_name' => $companyList[$staffData[$log_asset->staff_id] ?? 0] ?? '',
                    'to_staff_id' => 0,
                    'to_staff_name' => '',
                    'to_node_department_id' => $log_asset->node_department_id,
                    'to_node_department_name' => $log_asset->node_department_name,
                    'to_sys_store_id' => $log_asset->sys_store_id,
                    'to_store_name' => $log_asset->store_name,
                    'to_pc_code' => $log_asset->pc_code,
                    'to_use_land' => $log_asset->use_land,
                    'status' => MaterialEnums::TRANSFER_LOG_STATUS_RECEIVED,
                    'to_company_id' => $log_asset->company_id,
                    'to_company_name' => $log_asset->company_name,
                    'finished_at' => $now_time,
                    'operator_id' => $user['id'],
                    'transfer_operator_id' => $user['id'],
                    'transfer_method' => MaterialEnums::TRANSFER_METHOD_POST,
                    'transfer_type' => MaterialEnums::TRANSFER_TYPE_BATCH_STOCK_RETURN,//批量退库
                    'transfer_at' => $now_time,
                    'auto_recipient' => MaterialEnums::MATERIAL_TRANSFER_AUTO_RECIPIENT_NO,
                    'transfer_operator_name' => $user['name'],
                    'created_at' => $now_time,
                    'updated_at' => $now_time
                ];
            }
            //记录转移记录-行信息
            $transfer_log = new MaterialAssetTransferLogModel();
            $bool = $transfer_log->batch_insert($transfer_log_data);
            if ($bool === false) {
                throw new BusinessException('资产台账批量退库失败-记录转移行信息失败 可能的原因是=' . get_data_object_error_msg($transfer_log) . ';数据是=' . json_encode($transfer_log_data, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_BATCH_TRANSFER_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('material-asset-account-batch-stock-return failed:' . $real_message . json_encode($params, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 资产台账-导出协议-申请同意书/资产同意书
     * @param array $params 导出参数
     * @return array
     * @throws ValidationException
     */
    public function exportProtocol($params)
    {
        //使用人工号必须传递
        $staff_id = $params['staff_id'] ?? 0;
        if (empty($staff_id) || (is_numeric($staff_id) && strpos($staff_id, '.') === false) === false) {
            throw new ValidationException(static::$t->_('material_asset_account_export_protocal_staff_id_error'), ErrCode::$VALIDATE_ERROR);
        }
        //获取用户信息
        $user = (new UserService())->getUserById($staff_id);
        $user_info = (new BaseService())->format_user($user);
        $country_code = get_country_code();
        //马来 - 资产管理同意书
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            return AssetPdfService::getInstance()->exportAgreePdfForMy($params, $user_info);
        } elseif ($country_code == GlobalEnums::TH_COUNTRY_CODE && $user_info['hire_type'] == StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY) {
            //泰国 - 个人代理 - 资产管理同意书
            return AssetPdfService::getInstance()->exportAgreePdfForThPersonalAgent($params, $user_info);
        }
        return AssetTransferExportService::getInstance()->exportPdf($params, $user_info);
    }

    /**
     * 根据特定条件获取某员工名下筛选条件下的资产清单
     * @param integer $staff_id 员工工号
     * @param array $params 参数组
     * @return mixed
     */
    public function getStaffAssets($staff_id, $params)
    {
        $status = $params['status'] ?? [];
        $asset_ids = $params['asset_ids'] ?? [];
        $limit = $params['limit'] ?? 0;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name_zh,name_en,name_local,asset_code,sn_code,currency,purchase_price,net_value,receipted_at,model');
        $builder->from([MaterialAssetsModel::class]);
        $builder->where('is_deleted = :is_deleted: and staff_id = :staff_id:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'staff_id' => $staff_id]);
        if (!empty($status)) {
            $builder->inWhere('status', $status);
        }
        // 名下指定资产数据筛选
        if (!empty($asset_ids)) {
            $builder->inWhere('id', $asset_ids);
        }
        if (!empty($limit)) {
            $builder->limit($limit);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    public function dealScm($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $this->logger->info('asset_scm_callback_log' . json_encode($params, JSON_UNESCAPED_UNICODE));
            if ((new validSignService())->validator($params)) {
                $response = $this->updateAsset($params);
                $code     = $response['code'];
                $message  = $response['message'];
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = $e->getMessage();
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('wms_scm_callback_failed:' . $real_message);
        }
        $this->logger->info('wms_scm_callback_failed_return: code=' . $code . ';message=' . $message . ';data=' . json_encode($params, JSON_UNESCAPED_UNICODE));
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => []
        ];

    }

    /**
     * scm 修改sn码回写oa
     * */
    public function updateAsset($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now_time   = date('Y-m-d H:i:s');
            $asset_data = MaterialAssetsModel::findFirst([
                'conditions' => 'scm_no = :scm_no: and asset_code = :asset_code:',
                'bind'       => [
                    'scm_no'     => $params['scmNo'],
                    'asset_code' => $params['assetCode']
                ]
            ]);

            $scm_call_log = ['asset_code' => $params['assetCode'], 'staff_id' => $params['operateId'], 'staff_name' => $params['remark'], 'created_at' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s'), 'content' => json_encode(['new_sn_code' => $params['snCode'], 'sn_code' => empty($asset_data) ? '' : $asset_data->sn_code], JSON_UNESCAPED_UNICODE)];
            if (empty($asset_data)) {
                throw new ValidationException(static::$t->_('material_asset_code_scm_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            //sn码是否在oa 存在
            $asset_info = MaterialAssetsModel::findFirst([
                'conditions' => 'sn_code = :sn_code: and id != :id:',
                'bind'       => [
                    'sn_code' => $params['snCode'],
                    'id'      => $asset_data->id
                ]
            ]);

            if (empty($asset_data->sn_code)) {

                if (!empty($asset_info)) {
                    throw new ValidationException(static::$t->_('material_sn_code_is_exist'), ErrCode::$VALIDATE_ERROR);
                }
                $sn_code = $params['snCode'];


            } else {

                if ($asset_data->sn_code != $params['snCode']) {

                    if ($asset_data->asset_code == $asset_data->sn_code) {

                        if (!empty($asset_info)) {
                            throw new ValidationException(static::$t->_('material_sn_code_is_exist'), ErrCode::$VALIDATE_ERROR);
                        }
                        $sn_code = $params['snCode'];

                    } else {
                        throw new ValidationException(static::$t->_('material_asset_code_is_not_update'), ErrCode::$VALIDATE_ERROR);
                    }

                }
            }
            $before_sn_code = $asset_data->sn_code;

            if (!empty($sn_code)) {
                if ($before_sn_code == $sn_code) {//相同不做操作，存在为空不能进行不等判断

                } else {
                    $asset_data->sn_code    = $sn_code;
                    $asset_data->updated_at = $now_time;
                    $bool                   = $asset_data->save();
                    if ($bool === false) {
                        throw new BusinessException('scm修改sn码回调记录日志失败: ' . json_encode($asset_data->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($asset_data), ErrCode::$SYSTEM_ERROR);
                    }

                    $material_update_log_data = [
                        'asset_code' => $params['assetCode'],
                        'staff_id'   => '10000',
                        'staff_name' => 'SuperAdmin',
                        'content'    => json_encode(['before'     => $before_sn_code,
                                                     'after'      => $params['snCode'],
                                                     'field_name' => 'sn_code'], JSON_UNESCAPED_UNICODE),
                        'type'       => MaterialEnums::OPERATE_TYPE_SCM_CALLBACK_SN,
                        'created_at' => $now_time,
                        'updated_at' => $now_time
                    ];

                    //记录操作日志
                    $update_log_model = new MaterialAssetUpdateLogModel();
                    $log_bool         = $update_log_model->i_create($material_update_log_data);

                    if ($log_bool === false) {
                        throw new BusinessException('资产台账更新-操作记录失败 = ' . json_encode($asset_info->id, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_ASSET_ACCOUNT_SAVE_ERROR);
                    }
                }

            }


            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $code = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material_assets_scm_recall failed:' . $real_message);
        }
        //记录失败日志
        if ($code == ErrCode::$VALIDATE_ERROR) {
            $scm_call_log['fail_reason'] = $message;
            $callback_log_model          = new MaterialAssetScmCallbackLogModel();
            $callback_log_model->i_create($scm_call_log);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 导出sn失败数据
     * @param $data
     * @return string
     */
    public function exportFailSn($data)
    {
        $header    = [
            static::$t->_('re_field_sync_date'),//同步日期
            static::$t->_('re_field_asset_code'),//资产码
            static::$t->_('re_field_new_sn_code'),//新sn码
            static::$t->_('re_field_origin_sn_code'),//原sn码
            static::$t->_('re_field_fail_reason')//失败原因
        ];
        $file_name = 'assetaccount_sn_fail' . date('YmdHis');
        foreach ($data as &$item) {
            $item['content'] = json_decode($item['content'], true);
            $new_data[]      = [
                $item['created_at'],
                $item['asset_code'],
                $item['content']['new_sn_code'],
                $item['content']['sn_code'],
                $item['fail_reason']
            ];
        }

        $excel_result = $this->exportExcel($header, $new_data ?? [], $file_name);
        $this->logger->info('Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE));
        return $excel_result['data'] ?? '';

    }
}
