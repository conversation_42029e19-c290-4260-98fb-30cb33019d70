<?php
namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\HrJobTitleModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysStoreModel;
use App\Models\oa\MaterialInventoryCheckRemindModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialInventoryCheckModel;
use App\Library\Enums;
use App\Library\Enums\InventoryCheckEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Material\Models\MaterialInventoryCheckRelModel;
use App\Modules\Material\Models\MaterialInventoryCheckStaffAssetsModel;
use App\Modules\Material\Models\MaterialInventoryCheckStaffModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Services\StaffService;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialAssetsRepository;
use App\Repository\oa\MaterialSauRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;

class InventoryCheckService extends BaseService
{
    private static $instance;

    // 资产语言包字段
    private static $language_fields = [
        'zh-CN' => 'goods_name_zh',
        'en' => 'goods_name_en',
        'th' => 'goods_name_th'
    ];

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'serial_number',
        'name',
        'status',
        'check_start',
        'check_end',
        'staff_id',
        'store_id',
        'department_id',
        'forbid_punch_out',
        'unpacked_asset_status',
        'asset_code',
        'sn_code',
        'old_asset_code',
        'asset_name',
        'asset_status',
        'pageNum',
        'pageSize'
    ];

    // 资产盘点-盘点管理-盘点单列表-搜索条件
    public static $validate_list_search = [
        'serial_number' => 'StrLenLe:14|>>>:serial_number error', //盘点单编号
        'name' => 'StrLenGeLe:2,200|>>>:name error', //盘点单名称
        'status' => 'Arr', //状态(1未开始，2进行中，3已完成，4已终止)
        'status[*]' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_STATUS_RULE . '|>>>:status error',
        'check_start' => 'DateTime|>>>:check start date error',//盘点时间起始
        'check_end' => 'DateTime|>>>:check end date error',//盘点时间截止
        'forbid_punch_out' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_WHETHER_RULE . '|>>>:forbid_punch_out error', //是否限制打卡：1否，2是
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    // 资产盘点-盘点管理-盘点单-创建
    public static $validate_inventory_check_create = [
        'serial_number' => 'Required|StrLen:14|>>>:serial_number error', //盘点单编号
        'name'  => 'Required|StrLenGeLe:1,200|>>>:name error',//盘点单名称
        'start_at' => 'Required|DateTime|>>>:start date error',//盘点开始时间
        'end_at' => 'Required|DateTime|>>>:end date error',//盘点结束时间
        'remark'  => 'StrLenGeLe:0,500|>>>:remark error',//备注
        'forbid_punch_out' => 'Required|IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_WHETHER_RULE . '|>>>:forbid_punch_out error',//是否限制打卡：1否，2是
        'material_category_ids' => 'Arr|ArrLenGeLe:0,200|>>>:material_category_ids error',//盘点范围-资产分类(物料分类)
        'barcodes' => 'Arr|ArrLenGeLe:0,200|>>>:barcodes error',//盘点范围-barcode
        'asset_status' => 'Arr',//盘点范围-资产状态
        'company_ids' => 'Arr',//盘点范围-费用所属公司
        'department_ids' => 'Arr|ArrLenGeLe:0,20|>>>:department_ids error',//盘点范围-盘点部门
        'staff_ids' => 'Arr|ArrLenGeLe:0,500|>>>:staff_ids error',//盘点范围-员工工号
        'job_ids' => 'Arr|ArrLenGeLe:0,20|>>>:job_ids error',//盘点范围-职位
        'categorys' => 'Arr',//盘点范围-网点类型
        'sys_store_ids' => 'Arr|ArrLenGeLe:0,100|>>>:sys_store_ids error',//盘点范围-指定网点
        'empty_asset_status' => 'Required|IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_WHETHER_RULE . '|>>>:empty_asset_status error',//盘点策略-是否盘点名下资产为空的员工：1否，2是
        'ploy' => 'IntIn:' . InventoryCheckEnums::PLOY_ALL . ',' . InventoryCheckEnums::PLOY_STAFF. ',' . InventoryCheckEnums::PLOY_SYS_STORE_MANGER .'|>>>:ploy error', //盘点策略-0全员盘点，1指定某人盘点，2指定网点主管盘点
        'check_staff_id' => 'IfIntEq:ploy,' . InventoryCheckEnums::PLOY_STAFF . '|Required|IntGt:0', //盘点策略-指定盘点人- 工号
        'is_must_upload' => 'Required|IntIn:' . InventoryCheckEnums::INVENTORY_OPEN_NO . ',' . InventoryCheckEnums::INVENTORY_OPEN_YES,//点击盘到时必须上传图片：1不开启，2开启
        'is_only_take_pic' => 'Required|IntIn:' . InventoryCheckEnums::INVENTORY_OPEN_NO . ',' . InventoryCheckEnums::INVENTORY_OPEN_YES//盘点图片仅允许拍照上传：1不开启，2开启
    ];

    // 资产盘点-盘点管理-盘点单-查看、删除
    public static $validate_inventory_check_id = [
        'id' => 'Required|IntGe:1|>>>:id error', //盘点单ID
    ];

    // 资产盘点-盘点管理-盘点单/员工盘点任务-终止
    public static $validate_terminal = [
        'terminal_reason' => 'Required|StrLenGeLe:1,200|>>>:terminal_reason error'
    ];

    // 资产盘点-盘点管理-盘点单报表-搜索条件
    public static $validate_detail_search = [
        'inventory_check_id' => 'Required|IntGe:1|>>>:inventory_check_id error', //盘点单ID
        'staff_id' => 'IntGe:1|>>>:staff_id error', //员工工号
        'store_id' => 'Arr|ArrLenGeLe:0,20|>>>:store_id error', //所属网点，最多可选20个
        'department_id' => 'IntGe:1|>>>:department_id error', //部门
        'status' => 'Arr',
        'status[*]' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_STATUS_RULE . '|>>>:status error', //任务状态(1未开始，2进行中，3已完成，4已终止)
        'unpacked_asset_status' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_WHETHER_RULE . '|>>>:unpacked_asset_status error',//未盘资产总数是否大于0：1否，2是
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error',//每页条数
    ];

    // 资产盘点-盘点管理-盘点单报表-导出-搜索条件
    public static $validate_detail_export_search = [
        'inventory_check_id' => 'Required|IntGe:1|>>>:inventory_check_id error', //盘点单ID
        'staff_id' => 'IntGe:1|>>>:staff_id error', //员工工号
        'store_id' => 'Arr|ArrLenGeLe:0,20|>>>:store_id error', //所属网点，最多可选20个
        'department_id' => 'IntGe:1|>>>:department_id error', //部门
        'status' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_STATUS_RULE . '|>>>:status error',//任务状态(1未开始，2进行中，3已完成，4已终止)
    ];

    // 资产盘点-盘点管理-盘点报表-取消限制打卡/终止/提醒
    public static $validate_detail_ids = [
        'ids' => 'Required|Arr|ArrLenGeLe:1,100|>>>:ids error',
        'ids[*]' => 'Required|IntGe:1|>>>:id error'
    ];

    // 资产盘点-盘点管理-盘点报表-盘点明细-列表/导出
    public static $validate_detail_asset_list_search = [
        'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
        'asset_code' => 'StrLenGeLe:4,100|>>>:asset_code error',//资产编码
        'sn_code' => 'StrLenGeLe:4,100|>>>:sn_code error',//资产编码
        'old_asset_code' => 'StrLenGeLe:4,100|>>>:old_asset_code error',//旧资产编码
        'asset_name' => 'StrLenGeLe:2,100|>>>:asset_name error',//资产名称
        'asset_status' => 'Arr',
        'asset_status[*]' => 'IntIn:' . InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_RULE . '|>>>:asset_status error',//盘点结果
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error',//每页条数
    ];

    // 资产盘点-BY端-盘点通知/提醒消息详情
    public static $validate_msg_detail = [
        'inventory_check_id' => 'Required|IntGe:1|>>>:inventory_check_id error', //盘点单ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
    ];

    // 资产盘点-BY端-任务清单-待处理/已结束列表
    public static $validate_task_list = [
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
        'type' => 'IntIn:1,2|>>>:type error',//1待处理，2已结束
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error',//每页条数
    ];

    //资产盘点-BY端-资产盘点清单-待盘点、已盘点列表
    public static $validate_task_asset_list = [
        'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
        'type' => 'IntIn:0,1,2|>>>:type error',//0全部，1待处理，2已结束
        'asset_name' => 'StrLenGeLe:0,100',//资产名称
        'code' => 'StrLenGeLe:0,100',//资产码/旧资产码/sn码
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error',//每页条数
    ];

    //资产盘点-BY端-资产盘点清单-操作前置参数验证
    public static $validate_task_asset_operate = [
        'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
        'asset_id' => 'Required|IntGe:1|>>>:asset_id error', //资产清单ID
    ];

    //资产盘点-BY端-资产盘点清单-批量操作前置参数验证
    public static $validate_task_asset_batch_operate = [
        'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
        'asset_ids' => 'Required|Arr|ArrLenGe:1|>>>:asset_ids error',
        'asset_ids[*]' => 'Required|IntGe:1|>>>:asset_id error',
    ];

    //资产盘点-BY端-资产盘点清单-附件前置参数验证
    public static $validate_task_asset_attachments = [
        'attachments[*]' => 'Required|Obj',
        'attachments[*].bucket_name' => 'Required|StrLenGeLe:1,63',
        'attachments[*].object_key' => 'Required|StrLenGeLe:1,100',
        'attachments[*].file_name' => 'Required|StrLenGeLe:1,200',
        'attachments[*].object_url' => 'Required|StrLenGeLe:1,500'
    ];

    //资产盘点-BY端-资产详情参数验证
    public static $validate_task_info = [
        'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
        'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
    ];

    /**
     * 构造函数
     * InventoryCheckService constructor.
     */
    private function __construct()
    {
    }

    /**
     * 类实例
     * @return InventoryCheckService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取创建盘点单-额外的参数组信息
     * @param array $params 请求参数
     * @return array
     */
    public function getExtendValidation(array $params)
    {
        $validate_param = self::$validate_inventory_check_create;
        //盘点范围-资产分类
        if (!empty($params['material_category_ids'])) {
            $validate_param['material_category_ids[*]'] = 'Required|IntGt:0|>>>:param error[material_category_id]';
        }
        //盘点范围-barcode
        if (!empty($params['barcodes'])) {
            $validate_param['barcodes[*]'] = 'Required|StrLenGeLe:1,30|>>>:param error[barcode]';
        }
        //盘点范围-资产状态
        if (!empty($params['asset_status'])) {
            $material_inventory_assets_status = EnumsService::getInstance()->getSettingEnvValueIds('material_inventory_assets_status');
            $validate_param['asset_status'] = 'ArrLenLe:' . count($material_inventory_assets_status);
            $validate_param['asset_status[*]'] = 'Required|IntIn:' . implode(',', $material_inventory_assets_status) . '|>>>:param error[asset_status]';
        }
        //盘点范围-使用方向
        if (!empty($params['asset_use'])) {
            $validate_param['asset_use'] = 'IntIn:' . MaterialEnums::USE_PERSONAL . ',' . MaterialEnums::USE_PUBLIC;
        }
        //盘点范围-费用所属公司
        if (!empty($params['company_ids'])) {
            $cost_company = (new PurchaseService())->getCooCostCompany();
            $cost_company_ids = array_column($cost_company, 'cost_company_id');
            $validate_param['company_ids'] = 'ArrLenLe:' . count($cost_company_ids);
            $validate_param['company_ids[*]'] = 'Required|IntIn:' . implode(',', $cost_company_ids) . '|>>>:param error[company_id]';
        }
        //盘点范围-盘点部门
        if (!empty($params['department_ids'])) {
            $validate_param['department_ids[*].department_id'] = 'Required|IntGt:0|>>>:param error[department_id]';//部门ID
            $validate_param['department_ids[*].is_include_sub'] = 'Required|IntIn:0,1|>>>:param error[is_include_sub]';//是否包含子部门: 0-不含；1-包含
        }
        //盘点范围-指定工号
        if (!empty($params['staff_ids'])) {
            $validate_param['staff_ids[*]'] = 'Required|IntGt:0|>>>:param error[staff_id]';
        }
        //盘点范围-职位
        if (!empty($params['job_ids'])) {
            $validate_param['job_ids[*]'] = 'Required|IntGt:0|>>>:param error[job_id]';
        }
        //盘点范围-网点类型
        if (!empty($params['categorys'])) {
            $sys_store_type = (new StoreRepository())->getSysStoreType(6);
            $validate_param['categorys'] = 'ArrLenLe:1';
            $validate_param['categorys[*]'] = 'Required|IntIn: ' . implode(',', array_column($sys_store_type, 'id')) . '|>>>:param error[sys_store_type]';
        }
        //盘点范围-指定网点
        if (!empty($params['sys_store_ids'])) {
            $validate_param['sys_store_ids[*]'] = 'Required|StrLenGeLe:1,10|>>>:param error[sys_store_id]';
        }
        return $validate_param;
    }

    /**
     * 获取资产盘点清单-信息有误-参数验证组（BY端）
     * @param array $params 请求参数
     * @return array
     */
    public function getTaskAssetNotMatchValidation(array $params)
    {
        $validation = [
            'revise_asset_code' => 'StrLenGeLe:0,50',
            'revise_sn_code' => 'StrLenGeLe:0,50|>>>:revise_sn_code error',
            'revise_sys_store_id' => 'StrLenGeLe:0,10|>>>:revise_sys_store_id error',
            'revise_sys_store_name' => 'StrLenGeLe:0,50|>>>:revise_sys_store_name error',
            'revise_staff_id' => 'IntGe:0|>>>:revise_staff_id error',
            'revise_staff_name' => 'IfIntGt:revise_staff_id,0|Required|StrLenGeLe:1,50|>>>:revise_staff_name error',
            'reason' => 'StrLenGeLe:0,500|>>>:reason error',
            'attachments' => 'Required|Arr|ArrLenGeLe:1,10|>>>:attachments error'
        ];
        $validation = array_merge(self::$validate_task_asset_operate, $validation);
        if (!empty($params['revise_sys_store_id'])) {
            $validation['revise_sys_store_name'] = 'Required|StrLenGeLe:1,50|>>>:revise_sys_store_name error';
        }
        if (!empty($params['attachments'])) {
            $validation = array_merge($validation, self::$validate_task_asset_attachments);
        }
        return $validation;
    }

    /**
     * 获取资产盘点清单-添加资产-参数验证组（BY端）
     * @param array $params 请求参数
     * @return array
     */
    public function getTaskAssetAddValidation(array $params)
    {
        $validation = [
            'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
            'staff_id' => 'Required|IntGe:1|>>>:staff_id error',//任务员工工号
            'asset_staff_id' => 'Required|IntGe:1|>>>:asset_staff_id error',//资产使用人工号
            'asset_staff_name' => 'Required|StrLenGeLe:1,50|>>>:asset_staff_name error',//资产使用人姓名
            'bar_code' => 'Required|StrLenGeLe:1,30|>>>:bar_code error',
            'name_zh' => 'Required|StrLenGeLe:1,100|>>>:name_zh error',
            'name_en' => 'Required|StrLenGeLe:1,100|>>>:name_en error',
            'name_local' => 'Required|StrLenGeLe:0,100|>>>:name_local error',
            'asset_code' => 'StrLenGeLe:0,50|>>>:asset_code error',
            'sn_code' => 'StrLenGeLe:0,50|>>>:sn_code error',
            'reason' => 'StrLenGeLe:0,500|>>>:reason error',
            'attachments' => 'Required|Arr|ArrLenGeLe:1,10|>>>:attachments error',
        ];
        if (!empty($params['attachments'])) {
            $validation = array_merge($validation, self::$validate_task_asset_attachments);
        }
        return $validation;
    }

    /**
     * 获取资产盘点清单-修改数量-参数验证组（BY端）
     * @param array $params 请求参数
     * @return array
     */
    public function getTaskUpdateNumValidation(array $params)
    {
        $validation = [
            'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
            'staff_id' => 'Required|IntGe:1|>>>:staff_id error',//任务员工工号
            'bar_code' => 'Required|StrLenGeLe:1,30|>>>:bar_code error',
            'name_zh' => 'Required|StrLenGeLe:1,100|>>>:name_zh error',
            'name_en' => 'Required|StrLenGeLe:1,100|>>>:name_en error',
            'name_local' => 'Required|StrLenGeLe:0,100|>>>:name_local error',
            'real_asset_num' => 'Required|IntGeLe:0,9999|>>>:real_asset_num error',
            'reason' => 'StrLenGeLe:0,500|>>>:reason error',
            'type' => 'Required|IntIn:1,2',//操作来源1修改数量、2点击确认
            'attachments' => 'IfIntEq:type,1|Required|Arr|ArrLenGeLe:1,10|>>>:attachments error',
        ];
        if (!empty($params['attachments'])) {
            $validation = array_merge($validation, self::$validate_task_asset_attachments);
        }
        return $validation;
    }

    /**
     * 获取列表初始化数据
     * @return array
     */
    public function getListDefaultActionData()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_list = [
                'inventory_check_status' => InventoryCheckEnums::$inventory_check_status,
                'inventory_check_whether' => InventoryCheckEnums::$inventory_check_whether,
                'inventory_check_task_status' => InventoryCheckEnums::$inventory_check_staff_status,
                'inventory_check_task_asset_status' => InventoryCheckEnums::$inventory_check_staff_asset_status,
                'material_inventory_assets_status' => EnumsService::getInstance()->getSettingEnvValueIds('material_inventory_assets_status'),
                'asset_use' => MaterialEnums::$use
            ];
            foreach ($enums_list as $key => $item) {
                foreach ($item as $index => $t_key) {
                    if ($key == 'material_inventory_assets_status') {
                        $data[$key][] = [
                            'id' => $t_key,
                            'label' => self::$t['material_asset_status.' . $t_key]
                        ];
                    } else {
                        $data[$key][] = [
                            'id' => $index,
                            'label' => self::$t[$t_key]
                        ];
                    }
                }
            }
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            $data['sys_store_type'] = (new StoreRepository())->getSysStoreType(6);

            $data['category_map'] = ClassifyService::getInstance()->getClassifyArr(['type' => MaterialClassifyEnums::MATERIAL_CATEGORY_ENUMS], 'search')['data'];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('盘点单管理 - 列表基础数据 - 下拉选项获取异常: ' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取盘点单列表
     * @param array $condition 查询条件组
     * @param int $uid 当前员工ID
     * @return array
     */
    public function getList(array $condition, int $uid = 0)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            //盘点时间起始与结束校验
            $check_start = !empty($condition['check_start']) ? strtotime($condition['check_start']) : '';
            $check_end = !empty($condition['check_end']) ? strtotime($condition['check_end']) : '';
            if ($check_start > $check_end) {
                throw new ValidationException(static::$t->_('inventory_check_list_check_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            //最多可搜索一年的
            if ($check_start && $check_end) {
                $days = ($check_end - $check_start) / 86400;
                if ($days > 365) {
                    throw new ValidationException(static::$t->_('inventory_check_list_pass_one_year'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $condition['uid'] = $uid;
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialInventoryCheckModel::class]);
            //组合搜索条件
            $builder = $this->getCondition($builder, $condition);
            $builder->columns('count(main.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns([
                    'main.id',
                    'main.serial_number',
                    'main.name',
                    'main.create_staff_id',
                    'main.create_name',
                    'main.created_at',
                    'main.status',
                    'main.start_at',
                    'main.end_at',
                    'main.is_new',
                    'main.terminal_staff_id',
                    'main.terminal_reason',
                    'main.forbid_punch_out'
                ]);
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-list-failed:' .  $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 组装列表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition 查询条件组
     * @return mixed
     */
    private function getCondition(object $builder, array $condition)
    {
        $serial_number = $condition['serial_number'] ?? '';
        $name = $condition['name'] ?? '';
        $status = $condition['status'] ?? [];
        $check_start = $condition['check_start'] ?? '';
        $check_end = $condition['check_end'] ?? '';
        $forbid_punch_out = $condition['forbid_punch_out'] ?? 0;//是否限制打卡：0默认值，1否，2是
        //删除标志
        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        //先获取当前登陆人的盘点组集合
        $groups = MaterialSettingService::getInstance()->getStaffInventoryGroups($condition['uid']);
        if (!empty($groups)) {
            //1创建人=当前登录用户 && 2. 创建人和当前登录用户在同一个盘点组里；拿到组后获取所有组员
            $staff_ids = MaterialSettingService::getInstance()->getInventoryGroupStaffList($groups);
        } else {
            //没有组就只看1创建人=当前登录用户
            $staff_ids = [$condition['uid']];
        }
        $builder->inWhere('main.create_staff_id', $staff_ids);

        //盘点单编号
        if (!empty($serial_number)) {
            $builder->andWhere('main.serial_number = :serial_number:', ['serial_number' => $serial_number]);
        }
        //盘点单名称
        if (!empty($name)) {
            $builder->andWhere('main.name LIKE :name:', ['name' => "{$name}%"]);
        }
        //状态(1未开始，2进行中，3已完成，4已终止)
        if ($status) {
            $builder->inWhere('main.status', $status);
        }
        //盘点时间起始
        if (!empty($check_start)) {
            $builder->andWhere('main.start_at >= :check_start:', ['check_start' => $check_start]);
        }
        //盘点时间结束
        if (!empty($check_end)) {
            $builder->andWhere('main.end_at <= :check_end:', ['check_end' => $check_end]);
        }
        //是否限制打卡：0默认值，1否，2是
        if (!empty($forbid_punch_out)) {
            $builder->andWhere('main.forbid_punch_out = :forbid_punch_out:', ['forbid_punch_out' => $forbid_punch_out]);
        }
        return $builder;
    }

    /**
     * 格式化盘点单列表数据
     * @param array $items 盘点单列表
     * @return array
     */
    private function handleListItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['status_text'] = static::$t->_(InventoryCheckEnums::$inventory_check_status[$item['status']]);
            $item['forbid_punch_out_text'] = static::$t->_(InventoryCheckEnums::$inventory_check_whether[$item['forbid_punch_out']]);
        }
        return $items;
    }

    /**
     * 获取盘点单 - 创建页 - 基本信息默认值
     * @return array
     */
    public function getAddDefaultData()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $data['serial_number'] = static::genSerialNo(InventoryCheckEnums::INVENTORY_CHECK_SERIAL_NUMBER_PREFIX, RedisKey::INVENTORY_CHECK_SERIAL_NUMBER_COUNTER);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('资产盘点-盘点管理-盘点单创建-默认值获取异常:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 创建盘点单
     * @param array $data 入库数据信息
     * @param array $user 当前登陆者信息
     * @return array
     */
    public function addInventoryCheck(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //检测时间
            $start_at = strtotime($data['start_at']);
            if ($start_at < time()) {
                throw new ValidationException(static::$t->_('inventory_check_start_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($start_at > strtotime($data['end_at'])) {
                throw new ValidationException(static::$t->_('inventory_check_end_error'), ErrCode::$VALIDATE_ERROR);
            }
            //若网点类型与指定网点设置同时存在，则需要提示
            if (!empty($data['categorys']) && !empty($data['sys_store_ids'])) {
                throw new ValidationException(static::$t->_('inventory_check_store_set_error'), ErrCode::$VALIDATE_ERROR);
            }
            //若盘点策略-选择了“指定网点主管盘点”且是否盘点名下资产为空的员工选择了“是”，则提示：当选择“指定网点主管盘点”，请选择“是否盘点名下资产为空的员工”为“否”
            if ($data['ploy'] == InventoryCheckEnums::PLOY_SYS_STORE_MANGER && $data['empty_asset_status'] == InventoryCheckEnums::INVENTORY_CHECK_WHETHER_YES) {
                throw new ValidationException(static::$t->_('inventory_check_ploy_change_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            //若盘点策略-是否盘点名下资产为空的员工 = 2是;则盘点部门、盘点网点、指定工号、指定职位必填一项
            if ($data['empty_asset_status'] == InventoryCheckEnums::INVENTORY_CHECK_WHETHER_YES && empty($data['department_ids']) && empty($data['sys_store_ids']) && empty($data['staff_ids']) && empty($data['job_ids'])) {
                throw new ValidationException(static::$t->_('inventory_check_empty_asset_required'), ErrCode::$VALIDATE_ERROR);
            }
            //检查该申请编号是否已存在
            $exists = MaterialInventoryCheckModel::findFirst([
                'conditions' => 'serial_number = :serial_number:',
                'columns'    => 'id',
                'bind'       => ['serial_number' => $data['serial_number']],
            ]);
            if (!empty($exists)) {
                throw new ValidationException(static::$t->_('inventory_check_serial_num_repeat').' = ' . $data['serial_number'], ErrCode::$VALIDATE_ERROR);
            }

            //盘点单基本信息
            $now = date('Y-m-d H:i:s');
            $inventory_check_data = [
                'serial_number' => $data['serial_number'],//盘点单编号
                'name' => $data['name'],//盘点单名称
                'create_staff_id' => $user['id'],//创建人工号
                'create_name' => $user['name'],//创建人姓名
                'status' => InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED,//未开始
                'start_at' => $data['start_at'],//盘点开始时间
                'end_at' => $data['end_at'],//盘点结束时间
                'remark' => $data['remark'],//备注
                'forbid_punch_out' => $data['forbid_punch_out'],//是否限制打卡：1否，2是
                'material_category_ids' => $data['material_category_ids'] ? implode(',', $data['material_category_ids']) : '',//'盘点范围-资产分类
                'barcodes' => $data['barcodes'] ? implode(',', $data['barcodes']) : '',//盘点范围-barcode串
                'asset_status' => $data['asset_status'] ? implode(',', $data['asset_status']) : '',//盘点范围-资产状态串
                'asset_use' => $data['asset_use'] ? $data['asset_use'] : 0,//盘点范围-资产使用方向
                'company_ids' => $data['company_ids'] ? implode(',', $data['company_ids']) : '',//盘点范围-费用所属公司ID串
                'staff_ids' => $data['staff_ids'] ? implode(',', $data['staff_ids']) : '',//盘点范围-指定工号串
                'job_ids' => $data['job_ids'] ? implode(',', $data['job_ids']) : '',//盘点范围-指定职位串
                'categorys' => $data['categorys'] ? implode(',', $data['categorys']) : '',//盘点范围-网点类型
                'sys_store_ids' => $data['sys_store_ids'] ? implode(',', $data['sys_store_ids']) : '',//盘点范围-指定网点串
                'empty_asset_status' => $data['empty_asset_status'],//盘点策略-是否盘点名下资产为空的员工：0默认值，1否，2是
                'ploy' => $data['ploy'],//盘点策略-0全员盘点，1指定某人盘点
                'check_staff_id' => ($data['ploy'] == InventoryCheckEnums::PLOY_STAFF && $data['check_staff_id']) ? $data['check_staff_id'] : 0,//盘点策略-指定盘点人- 工号
                'is_new' => InventoryCheckEnums::IS_NEW_YES,//新旧资产盘点单标识：0旧资产盘点，1新资产盘点
                'is_must_upload' => $data['is_must_upload'],//点击盘到时必须上传图片
                'is_only_take_pic' => $data['is_only_take_pic'],//盘点图片仅允许拍照上传
                'created_at' => $now,//创建时间
                'updated_at' => $now//更新时间
            ];
            $inventory_check_model = new MaterialInventoryCheckModel();
            $inventory_check_add_result = $inventory_check_model->i_create($inventory_check_data);
            if ($inventory_check_add_result === false) {
                throw new BusinessException('资产盘点-盘点管理-盘点单创建失败 ' . json_encode($inventory_check_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是:' . get_data_object_error_msg($inventory_check_model), ErrCode::$BUSINESS_ERROR);
            }

            //盘点范围-盘点部门，需要录入到关系表
            if (!empty($data['department_ids'])) {
                $inventory_check_rel = [];
                foreach ($data['department_ids'] as $item) {
                    $inventory_check_rel[] = [
                        'inventory_check_id' => $inventory_check_model->id, //盘点单ID
                        'department_id' => $item['department_id'],
                        'is_include_sub' => $item['is_include_sub'],
                        'created_at' => $now,//创建时间
                        'updated_at' => $now//更新时间
                    ];
                }
                $inventory_check_rel_model = new MaterialInventoryCheckRelModel();
                $inventory_check_add_rel_result = $inventory_check_rel_model->batch_insert($inventory_check_rel);
                if ($inventory_check_add_rel_result === false) {
                    throw new BusinessException('资产盘点-盘点管理-盘点单创建附属关系失败 = ' . json_encode($inventory_check_rel, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-create-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 删除盘点单  -- 18092废弃
     * @param array $data 删除参数
     * @return array
     */
    public function delInventoryCheck(array $data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取盘点单信息
            $inventory_check_info = $this->getInventoryCheckInfoById($data['id']);
            if (empty($inventory_check_info)) {
                throw new ValidationException('未找到该盘点单信息', ErrCode::$VALIDATE_ERROR);
            }
            if (strtotime($inventory_check_info->start_at) <= time()) {
                //如果盘点开始小于当前时间，说明盘点已经开始，不可删除
                throw new ValidationException(static::$t->_('inventory_check_cannot_del'), ErrCode::$VALIDATE_ERROR);
            }
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //盘点范围关系表删除结果
            $inventory_check_rel_del_ret = true;
            $inventory_check_del_ret = $inventory_check_info->i_update([
                'is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_YES, //已删除
            ]);
            if ($inventory_check_del_ret && $inventory_check_info->type > Enums\InventoryCheckEnums::INVENTORY_CHECK_TYPE_DEFAULT) {
                //除所有盘点范围外的附属关系做删除操作
                $is_deleted = Enums\InventoryCheckEnums::IS_DELETED_YES;
                $sql = "update material_inventory_check_rel set is_deleted=:is_deleted where inventory_check_id=:inventory_check_id";
                $inventory_check_rel_del_ret = $db->execute($sql,['is_deleted'=>$is_deleted, 'inventory_check_id'=>$inventory_check_info->id]);
            }
            if ($inventory_check_del_ret === false || $inventory_check_rel_del_ret === false) {
                $db->rollback();
                $msgArr = [];
                $messages = $inventory_check_info->getMessages();
                foreach ($messages as $message){
                    $msgArr[]= $message->getMessage();
                }
                throw new BusinessException('资产盘点-盘点管理-盘点单删除失败 = ' . json_encode(['inventory_check_data'=>$inventory_check_info,'message'=>$msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_DEL_ERROR);
            } else {
                $db->commit();
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        }  catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('inventory_check-del-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS,
        ];
    }

    /**
     * 根据盘点单ID获取盘点单信息
     * @param int $id 盘点单ID
     * @return mixed
     * @throws ValidationException
     */
    public function getInventoryCheckInfoById(int $id)
    {
        $inventory_check_info =  MaterialInventoryCheckModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ]);
        if (empty($inventory_check_info)) {
            throw new ValidationException(static::$t->_('inventory_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $inventory_check_info;
    }

    /**
     * 查看盘点单信息
     * @param array $data 查询信息参数
     * @return array
     */
    public function infoInventoryCheck(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $info = [];
        try {
            //获取盘点单信息
            $inventory_check_info = $this->getInventoryCheckInfoById($data['id']);
            $info = $inventory_check_info->toArray();
            $info['department_ids'] = [];
            if (($inventory_check_info->is_new == InventoryCheckEnums::IS_NEW_NO && $inventory_check_info->type == InventoryCheckEnums::INVENTORY_CHECK_TYPE_DEPARTMENT) || $inventory_check_info->is_new == InventoryCheckEnums::IS_NEW_YES) {
                $inventory_check_rel = $inventory_check_info->getCheckRel()->toArray();
                if (!empty($inventory_check_rel)) {
                    $department_ids = array_column($inventory_check_rel, 'department_id');
                    $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids, 2);
                    foreach ($inventory_check_rel as $item) {
                        $info['department_ids'][] = [
                            'department_id' => $item['department_id'],
                            'department_name' => $department_list[$item['department_id']]['name'] ?? '',
                            'is_include_sub' => $item['is_include_sub']
                        ];
                    }
                }
            }
            $info['asset_use'] = $info['asset_use'] ? $info['asset_use'] : '';//资产使用方向
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('inventory_check-info-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $info,
        ];
    }

    /**
     * 盘点报表(查看任务)
     * @param array $condition 查询条件组
     * @param bool $export 是否导出
     * @return array
     */
    public function getDetailList(array $condition, bool $export = false)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
            $builder->leftjoin(MaterialInventoryCheckModel::class, 'task.inventory_check_id = main.id', 'main');
            //组合搜索条件
            $builder = $this->getDetailCondition($builder, $condition);
            $builder->columns('count(task.id) AS count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns([
                    'task.id',
                    'task.status',
                    'task.forbid_punch_out',
                    'task.staff_id',
                    'task.job_name',
                    'task.department_name',
                    'task.store_name',
                    'task.pack_asset_total',
                    'task.unpacked_asset_total',
                    'task.terminal_staff_id',
                    'task.terminal_reason',
                    'task.check_start_at',
                    'task.check_end_at',
                    'task.is_new',
                    'main.start_at',
                    'main.end_at'
                ]);
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDetailItems($items, $export);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-detail-list-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 组装盘点单报表搜索条件
     * @param object $builder 查询器对象
     * @param array $condition 查询条件组
     * @return mixed
     */
    private function getDetailCondition(object $builder, array $condition)
    {
        $inventory_check_id = $condition['inventory_check_id'] ?? 0;
        $staff_id = $condition['staff_id'] ?? 0;
        $store_id = $condition['store_id'] ?? [];
        $department_id = $condition['department_id'] ?? 0;
        $status = $condition['status'] ?? [];
        $unpacked_asset_status = $condition['unpacked_asset_status'] ?? 0; //未盘资产总数是否大于0：1否，2是
        $asset_code = $condition['asset_code'] ?? '';//资产编码
        $old_asset_code = $condition['old_asset_code'] ?? '';//旧资产编码
        $sn_code = $condition['sn_code'] ?? '';//SN码
        $asset_name = $condition['asset_name'] ?? '';//资产名称
        $asset_status = $condition['asset_status'] ?? [];//盘点结果
        $task_id = $condition['task_id'] ?? 0;

        //删除标志
        $builder->andWhere('task.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //盘点单ID
        if (!empty($inventory_check_id)) {
            $builder->andWhere('task.inventory_check_id = :inventory_check_id:', ['inventory_check_id' => $inventory_check_id]);
        }
        //任务ID
        if (!empty($task_id)) {
            $builder->andWhere('task.id = :id:', ['id' => $task_id]);
        }
        //员工工号
        if ($staff_id) {
            $builder->andWhere('task.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        }
        //网点筛选
        if ($store_id) {
            $builder->andWhere('task.store_id in ({store_ids:array})', ['store_ids' => $store_id]);
        }
        //部门
        if ($department_id) {
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($department_id,true);
            array_push($department_ids,$department_id);
            $builder->inWhere('task.department_id', $department_ids);
        }
        //任务状态1:待盘点，2盘点中，3已完成，4已终止
        if ($status) {
            $builder->inWhere('task.status', $status);
        }
        //未盘资产总数是否大于0：1否，2是
        if (!empty($unpacked_asset_status)) {
            $unpacked_asset_status_condition = ($unpacked_asset_status == InventoryCheckEnums::INVENTORY_CHECK_WHETHER_NO) ? '=' : '>';
            $builder->andWhere('task.unpacked_asset_total ' . $unpacked_asset_status_condition . ' :unpacked_asset_total:', ['unpacked_asset_total' => 0]);
        }
        //盘点明细-资产码
        if (!empty($asset_code)) {
            $builder->andWhere('asset.asset_code like :asset_code: ', ['asset_code' => '%' . $asset_code . '%']);
        }
        //盘点明细-SN码
        if (!empty($sn_code)) {
            $builder->andWhere('asset.sn_code like :sn_code:', ['sn_code' => $sn_code . '%']);
        }
        //盘点明细-旧资产编码
        if (!empty($old_asset_code)) {
            $builder->andWhere('asset.old_asset_code like :old_asset_code: ', ['old_asset_code' => '%' . $old_asset_code . '%']);
        }
        //盘点明细-资产名称
        if (!empty($asset_name)) {
            $builder->andWhere('asset.' . get_lang_field_name('name_', static::$language) . ' like :asset_name:', ['asset_name' => '%' . $asset_name . '%']);
        }
        //盘点明细-盘点结果
        if (!empty($asset_status)) {
            $builder->inWhere('asset.status', $asset_status);
        }
        return $builder;
    }

    /**
     * 格式化盘点单报表数据
     * @param array $items 盘点单报表列表
     * @param bool $export 是否导出
     * @return array
     */
    private function handleDetailItems(array $items, bool $export)
    {
        if (empty($items)) {
            return [];
        }
        //获取当前页员工的id组
        $staff_ids = array_column($items, 'staff_id');
        //根据员工ID组获取员工列表
        $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);
        $new_items = [];
        foreach ($items as &$item) {
            //盘点单任务状态文本
            $item['status_text'] = static::$t[InventoryCheckEnums::$inventory_check_staff_status[$item['status']]];
            //组装员工信息
            $one_staff_info = $staff_list[$item['staff_id']] ?? [];
            $item['staff_name'] = $one_staff_info['name'] ?? '';
            $staff_wait_leave_state = $one_staff_info['wait_leave_state'] ?? 0;
            $state = $one_staff_info['state'] ?? 0;
            $staff_status = ($staff_wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $state;
            $item['work_status'] = $staff_status;
            $item['work_status_text'] = $staff_status ? static::$t->_(StaffInfoEnums::$staff_state[$staff_status]) : '';

            $item['forbid_punch_out_text'] = static::$t->_(InventoryCheckEnums::$inventory_check_whether[$item['forbid_punch_out']]);
            if ($export) {
                $new_items[$item['staff_id']] = $item;
            }
        }
        return $export ? $new_items : $items;
    }

    /**
     * 下发盘点任务（启动盘点单）
     * @param array $params 请求次参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function startInventory(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取盘点单信息
            $inventory_check_info = $this->getInventoryCheckInfoById($params['id']);
            if ($inventory_check_info->status != InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED) {
                throw new ValidationException(static::$t->_('inventory_task_existed'), ErrCode::$VALIDATE_ERROR);
            }
            $this->logger->info('资产盘点-盘点管理-下发盘点任务 操作人信息：' . json_encode($user, JSON_UNESCAPED_UNICODE));

            //更改盘点状态=进行中
            $now = date('Y-m-d H:i:s');
            $inventory_check_info->status = InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING;
            $inventory_check_info->is_send_task = InventoryCheckEnums::TASK_WAIT;
            $inventory_check_info->updated_at = $now;
            $bool = $inventory_check_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点-盘点管理-下发盘点任务（启动盘点单）失败 ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是:' . get_data_object_error_msg($inventory_check_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-startInventory-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 取消限制打卡
     * @param array $params 请求次参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancelPunchOut(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            //获取盘点单信息
            $inventory_check_info = $this->getInventoryCheckInfoById($params['id']);
            if ($inventory_check_info->forbid_punch_out != InventoryCheckEnums:: INVENTORY_CHECK_WHETHER_YES || !in_array($inventory_check_info->status, [InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED, InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING])) {
                throw new ValidationException(static::$t->_('inventory_cancel_punch_out_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('资产盘点-盘点管理-取消限制打卡 操作人信息：' . json_encode($user, JSON_UNESCAPED_UNICODE));

            $now = date('Y-m-d H:i:s');
            $inventory_check_info->forbid_punch_out = InventoryCheckEnums::INVENTORY_CHECK_WHETHER_NO;
            $inventory_check_info->updated_at = $now;
            $bool = $inventory_check_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点-盘点管理-取消限制打卡失败 ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是:' . get_data_object_error_msg($inventory_check_info), ErrCode::$BUSINESS_ERROR);
            }

            //存在员工任务，则需要将员工任务的限制打卡也同时取消
            $staff_tasks = $inventory_check_info->getStaffs()->toArray();
            if ($staff_tasks) {
                $staff_task_ids = implode(',', array_column($staff_tasks, 'id'));
                $bool = $db->updateAsDict(
                    (new MaterialInventoryCheckStaffModel())->getSource(),
                    [
                        'forbid_punch_out' => InventoryCheckEnums::INVENTORY_CHECK_WHETHER_NO,
                        'updated_at' => $now
                    ],
                    ['conditions' => "id in ({$staff_task_ids}) and forbid_punch_out = " . InventoryCheckEnums::INVENTORY_CHECK_WHETHER_YES]
                );

                if ($bool === false) {
                    throw new BusinessException('资产盘点-盘点管理-取消限制打卡 - 取消关联员工盘点任务打卡限制失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-cancelPunchOut-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 终止
     * @param array $params 请求次参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function terminal(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //获取盘点单信息
            $inventory_check_info = $this->getInventoryCheckInfoById($params['id']);
            if (!in_array($inventory_check_info->status, [InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED, InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING])) {
                throw new ValidationException(static::$t->_('inventory_terminal_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //盘点任务下发中的盘点单不可终止
            if ($inventory_check_info->is_send_task == InventoryCheckEnums::TASK_ING) {
                throw new ValidationException(static::$t->_('inventory_terminal_task_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //终止盘点单
            $now = date('Y-m-d H:i:s');
            $inventory_check_info->status = InventoryCheckEnums::INVENTORY_CHECK_STATUS_TERMINAL;
            $inventory_check_info->terminal_staff_id = $user['id'];
            $inventory_check_info->terminal_reason = $params['terminal_reason'];
            $inventory_check_info->updated_at = $now;
            $inventory_check_info->terminal_at = $now;
            $bool = $inventory_check_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点-盘点管理-终止失败 ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能的原因是:' . get_data_object_error_msg($inventory_check_info), ErrCode::$BUSINESS_ERROR);
            }

            //存在员工任务，则需要将员工任务也同时终止
            $staff_tasks = $inventory_check_info->getStaffs()->toArray();
            if ($staff_tasks) {
                $staff_task_ids = implode(',', array_column($staff_tasks, 'id'));
                $bool = $db->updateAsDict(
                    (new MaterialInventoryCheckStaffModel())->getSource(),
                    [
                        'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_TERMINAL,
                        'terminal_staff_id' => $user['id'],
                        'terminal_reason' => $params['terminal_reason'],
                        'terminal_at' => $now,
                        'updated_at' => $now
                    ],
                    ['conditions' => "id in ({$staff_task_ids}) and status in (" . InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT . ',' . InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING . ')']
                );

                if ($bool === false) {
                    throw new BusinessException('资产盘点-盘点管理-终止 - 终止关联员工盘点任务失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-terminal-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 旧资产盘点-盘点单报表导出
     * @param array $condition 筛选条件
     * @param string $locale 语言种类
     * @return array
     */
    public function getDetailExport(array $condition, string $locale)
    {
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $limit_size = Enums\InventoryCheckEnums::ASSETS_DOWNLOAD_PAGE_SIZE;
            //筛选出符合导出条件的员工列表
            $condition['pageNum'] = 1;
            $condition['pageSize'] = $limit_size;
            $list = $this->getDetailList($condition,true);
            $total_count = $list['data']['pagination']['total_count'];
            $row_values = [];
            if ($total_count > 0) {
                $page = ceil($total_count/$limit_size);
                $this->getAssetsList($condition['inventory_check_id'], $list['data']['items'], $locale, $row_values);
                for ($i=2; $i<=$page; $i++) {
                    $condition['pageNum'] = $i;
                    $list = $this->getDetailList($condition,true);
                    if ($list['data']['items']) {
                        $this->getAssetsList($condition['inventory_check_id'], $list['data']['items'], $locale, $row_values);
                    }
                }
            }
            // 最大条数限制
            if (count($row_values) > Enums\InventoryCheckEnums::ASSETS_DOWNLOAD_LIMIT) {
                throw new ValidationException(static::$t->_('inventory_check_asset_download_limit'));
            }
            $header = [
                static::$t->_('asset_barcode'), //barcode
                static::$t->_('asset_goods_name'), //资产名称
                static::$t->_('asset_num'), //资产数量
                static::$t->_('inventory_check_staff_asset_actual_num'), //多项资产确认数量
                static::$t->_('asset_code'), //资产编码
                static::$t->_('asset_sn'), //SN编码
                static::$t->_('inventory_check_staff_asset_revise_sn'), //修正后SN
                static::$t->_('inventory_check_staff_asset_img'), //图片Url
                static::$t->_('inventory_check_staff_asset_type'), //盘点结果
                static::$t->_('inventory_check_staff_asset_reason'), //反馈原因
                static::$t->_('inventory_check_staff_asset_date'), //盘点时间
                static::$t->_('staff_info_id'), //员工工号
                static::$t->_('staff_name'), //员工姓名
                static::$t->_('access_data_confidential_scope_200'), //在职状态
                static::$t->_('department.manager_job_title'), //职位
                static::$t->_('global.store_name'), //网点名称
                static::$t->_('job_department'), //部门
            ];


            $file_name         = "Material_plan_wms_check_staff_" . date("YmdHis");
            $result            = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage(); //. $e->getTraceAsString();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $logger = $this->getDI()->get('logger');
            $logger->warning('download-inventory_check-staff-asset-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 旧资产盘点-格式化导出数据资产信息
     * @param integer $inventory_check_id 盘点单ID
     * @param array $staff_list 员工盘点任务列表
     * @param string $locale 语言包
     * @param array $row_values 导出数据
     * @return array
     */
    private function getAssetsList($inventory_check_id, array $staff_list, $locale, &$row_values)
    {
        if (!$staff_list) {
            return [];
        }
        $staff_ids = array_column($staff_list, 'staff_id');
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'assets.id',
            'assets.staff_id',
            'assets.bar_code',
            'assets.'.(self::$language_fields[$locale] ?? 'goods_name_zh').' as goods_name',
            'assets.sys_asset_num',
            'assets.actual_asset_num',
            'assets.asset_num',
            'assets.asset_code',
            'assets.sn_code',
            'assets.revise_sn_code',
            'assets.type',
            'assets.reason',
            'assets.created_at',
        ]);
        $builder->from(['assets' => MaterialInventoryCheckStaffAssetsModel::class]);
        $builder->andWhere('assets.staff_id in ({staff_ids:array}) and inventory_check_id = :inventory_check_id:', ['staff_ids' => $staff_ids, 'inventory_check_id'=>$inventory_check_id]);
        $builder->andWhere('assets.is_deleted = :is_deleted:', ['is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_NO]);
        $export_data_list_obj = $builder->getQuery()->execute();
        $export_data_list = $export_data_list_obj ? $export_data_list_obj->toArray() : [];
        if ($export_data_list) {
            foreach ($export_data_list as $item) {
                $img_url = '';
                if ($item['type'] == Enums\InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_GET && !empty($item['revise_sn_code'])) {
                    //如果盘到且输入了修正后的sn码需要获取图片地址
                    $file = MaterialAttachmentModel::getFirst([
                        'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key=:oss_bucket_key: and deleted = :deleted:',
                        'columns'    => 'bucket_name,object_key,file_name',
                        'bind'       => ['oss_bucket_type' => Enums\InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK, 'oss_bucket_key'=>$item['id'], 'deleted'=>Enums\InventoryCheckEnums::IS_DELETED_NO],
                    ]);
                    if (!empty($file)) {
                        //存在附件信息，则组装地址
                        $remote_file_prefix = $this->getDI()->get('config')->application->img_prefix ?? '';
                        $img_url = $remote_file_prefix.$file->object_key;
                    } else {
                        $img_url = '';
                    }
                }
                //资产的盘点的数量，1.单项资产默认1；2.多项资产确认时，为点击确认时，填写的实际数量；3.针对手工新增的，取他们填写的数量。
                $actual_asset_num = ($item['type'] == Enums\InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_TYPE_HAND_ADD) ? $item['asset_num'] : $item['actual_asset_num'];
                $row_values[] = [
                    $item['bar_code'],//barcode
                    $item['goods_name'],//资产名称
                    $item['sys_asset_num'],//资产系统数量所有资产都显示这个数量,单项资产默认为1；多项资产就是系统抓取的数量。
                    $actual_asset_num,//资产实际盘点数量
                    $item['asset_code'],//资产编码
                    $item['sn_code'],//SN编码
                    $item['revise_sn_code'],//修正后SN
                    $img_url,//图片坑
                    static::$t->_(Enums\InventoryCheckEnums::$inventory_check_staff_asset_type[$item['type']]), //盘点结果
                    $item['reason'],//反馈原因
                    $item['created_at'],//盘点时间
                    $item['staff_id'],//员工工号
                    $staff_list[$item['staff_id']]['staff_name'],//员工姓名
                    $staff_list[$item['staff_id']]['work_status_text'],//在职状态
                    $staff_list[$item['staff_id']]['job_name'],//职位
                    $staff_list[$item['staff_id']]['store_name'],//网点名称
                    $staff_list[$item['staff_id']]['department_name']//部门
                ];
            }
        }
        return $row_values;
    }

    /**
     * 新资产盘点-导出-获得下载数据的总数
     * @param array $params 参数组
     * @return mixed
     */
    public function getInventoryTaskAssetExportTotal(array $params)
    {
        $total_count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
            $builder->leftJoin(MaterialInventoryCheckStaffAssetsModel::class, 'asset.staff_rel_id = task.id', 'asset');
            $builder->columns('COUNT(task.id) AS total');
            $builder = $this->getDetailCondition($builder, $params);
            $total_count = (int) $builder->getQuery()->getSingleResult()->total;
        } catch (\Exception $e) {
            $this->logger->error('get-inventory-asset-download-data-total-failed:' . $e->getMessage());
        }
        return $total_count;
    }

    /**
     * 新资产盘点-费用报表-查看明细
     * @param array $condition 查询条件组
     * @param bool $export 是否导出
     * @return array
     */
    public function getInventoryTaskAssetList(array $condition, bool $export = false)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
            $builder->leftJoin(MaterialInventoryCheckStaffAssetsModel::class, 'asset.staff_rel_id = task.id', 'asset');
            //组合搜索条件
            $builder = $this->getDetailCondition($builder, $condition);
            if ($export === false) {
                //非导出
                $builder->columns('count(asset.id) AS count');
                $count = intval($builder->getQuery()->getSingleResult()->count);
            } else {
                //导出
                $count = InventoryCheckEnums::INVENTORY_ASSET_EXPORT_MAX;
            }
            if ($count > 0) {
                $columns = [
                    'asset.id',
                    'asset.staff_id as asset_staff_id',
                    'asset.status',
                    'asset.asset_code',
                    'asset.old_asset_code',
                    'asset.sn_code',
                    'asset.name_zh',
                    'asset.name_en',
                    'asset.name_local',
                    'asset.asset_use_status',
                    'asset.bar_code',
                    'asset.asset_num',
                    'asset.revise_asset_code',
                    'asset.revise_sn_code',
                    'asset.revise_sys_store_id',
                    'asset.revise_sys_store_name',
                    'asset.revise_staff_id',
                    'asset.revise_staff_name',
                    'asset.reason',
                ];
                if ($export === true) {
                    //导出
                    $columns = array_merge($columns, [
                        'task.status as task_status',
                        'task.forbid_punch_out',
                        'task.staff_id as task_staff_id',
                        'task.job_name',
                        'task.store_name',
                        'task.department_name',
                        'task.terminal_staff_id',
                        'task.terminal_reason',
                        'task.pack_asset_total',
                        'task.unpacked_asset_total',
                    ]);
                }
                $builder->columns($columns);
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleDetailAssetItems($items, $export);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-detail-asset-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 格式化盘点资产清单
     * @param array $items 资产清单
     * @param bool $export 是否导出
     * @return array
     */
    private function handleDetailAssetItems(array $items, bool $export)
    {
        if (empty($items)) {
            return [];
        }

        //获取附件信息
        $asset_ids = array_column($items, 'id');
        $asset_attachment_list = MaterialAttachmentModel::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key in ({oss_bucket_key:array}) and deleted = :deleted:',
            'columns' => 'oss_bucket_key, GROUP_CONCAT(object_url) as object_url',
            'bind' => ['oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK, 'oss_bucket_key' => $asset_ids, 'deleted' => GlobalEnums::IS_NO_DELETED],
            'group' => 'oss_bucket_key'
        ])->toArray();
        $asset_attachment_list = array_column($asset_attachment_list, 'object_url', 'oss_bucket_key');

        if ($export === false) {
            //非导出
            foreach ($items as &$item) {
                //盘点结果
                $item['status_text'] = static::$t[InventoryCheckEnums::$inventory_check_staff_asset_status[$item['status']]];
                //资产使用状态
                $item['asset_use_status_text'] = $item['asset_use_status'] ? static::$t[MaterialEnums::$asset_status[$item['asset_use_status']]] : '';

                //有附件的填充附件
                $item['attachment'] = $asset_attachment_list[$item['id']] ?? '';
            }
        } else {
            $row_values = [];
            //导出
            $asset_name_field = get_lang_field_name('name_', static::$language);
            //获取盘点人信息
            $staff_ids = array_values(array_unique(array_column($items, 'task_staff_id')));
            $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);

            //获取资产台账信息
            $asset_codes = array_column(array_filter($items), 'asset_code');
            $asset_list = MaterialAssetsRepository::getInstance()->searchAsset(['asset_code' => $asset_codes, 'is_staff_empty' => 1]);
            $asset_list = array_column($asset_list, null, 'asset_code');

            //当一个盘点任务有多个明细行时，应盘资产总数、未盘资产总数只需要出现在该盘点任务的第一行，其他行为空
            $task_staff_asset = [];
            foreach ($items as $item) {
                //盘点人信息
                $one_staff_info = $staff_list[$item['task_staff_id']] ?? [];
                $staff_name = $one_staff_info['name'] ?? '';
                $staff_wait_leave_state = $one_staff_info['wait_leave_state'] ?? 0;
                $state = ($staff_wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $one_staff_info['state'] ?? 0;
                $state_text = $state ? static::$t[StaffInfoEnums::$staff_state[$state]] : '';
                //资产台账信息
                $one_asset_account_info = $item['asset_code'] ? ($asset_list[$item['asset_code']] ?? []) : [];
                //盘点任务是否有多个明细行
                $task_staff_has_more_asset = in_array($item['task_staff_id'], $task_staff_asset) ? true : false;
                $row_values[] = [
                    static::$t[InventoryCheckEnums::$inventory_check_staff_status[$item['task_status']]],//任务状态
                    static::$t[InventoryCheckEnums::$inventory_check_whether[$item['forbid_punch_out']]],//是否限制打卡
                    $item['task_staff_id'],//盘点人工号
                    $staff_name,//盘点人姓名
                    $item['job_name'],//盘点人职位
                    $item['store_name'],//所属网点
                    $item['department_name'],//所属部门
                    $state_text,//盘点人在职状态
                    !empty($item['terminal_staff_id']) ? $item['terminal_staff_id'] : '',//终止人工号
                    $item['terminal_reason'],//终止原因
                    $task_staff_has_more_asset ? '' : $item['pack_asset_total'],//应盘资产总数
                    $task_staff_has_more_asset ? '' : $item['unpacked_asset_total'],//未盘资产总数
                    !in_array($item['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT]) ? $item['bar_code'] : '',//barcode
                    $item['asset_code'],//资产编码
                    $item['old_asset_code'],//旧资码
                    $item['sn_code'],//SN编码
                    $item[$asset_name_field],//资产名称
                    !in_array($item['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT]) ? $item['asset_num'] : '0',//资产数量
                    $item['asset_staff_id'],//盘点时资产使用人工号
                    $item['asset_use_status'] ? static::$t[MaterialEnums::$asset_status[$item['asset_use_status']]] : '',//盘点时资产状态
                    $one_asset_account_info['staff_id'] ?? '',//台账现使用人工号
                    ($one_asset_account_info && isset(MaterialEnums::$asset_status[$one_asset_account_info['status']]))? static::$t[MaterialEnums::$asset_status[$one_asset_account_info['status']]] : '',//台账现使用状态
                    static::$t[InventoryCheckEnums::$inventory_check_staff_asset_status[$item['status']]] ?? '',//盘点结果
                    in_array($item['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT]) ? $item['bar_code'] : '',//盘盈barcode
                    in_array($item['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT]) ? $item[$asset_name_field] : '',//盘盈资产名称
                    in_array($item['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT]) ? $item['asset_num'] : '0',//盘盈数量
                    $item['revise_asset_code'],//修正新资产码
                    $item['revise_sn_code'],//修正新SN码
                    $item['revise_staff_id'] ? $item['revise_staff_id'] . '(' . $item['revise_staff_name'] . ')' : '',//修正使用人
                    $item['revise_sys_store_name'],//修正使用地址
                    $item['reason'],//反馈备注
                    $asset_attachment_list[$item['id']] ?? '',//图片url
                ];
                $task_staff_asset[] = $item['task_staff_id'];
            }
            $items = $row_values;
        }

        return $items;
    }

    /**
     * 盘点报表-导出/盘点报表-盘点明细-导出
     * @param array $condition 请求参数组
     * @return array
     */
    public function export(array $condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $data = '';
        try {
            //筛选出符合导出条件的员工列表
            $condition['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = InventoryCheckEnums::INVENTORY_ASSET_EXPORT_MAX;
            $list = $this->getInventoryTaskAssetList($condition,true);
            $row_values = $list['data']['items'];

            $file_name = 'inventory_asset _detail_export-' . date('YmdHis');
            $result = $this->exportExcel($this->getExportExcelHeaderFields(), $row_values, $file_name);
            $data = $result['data'] ?? '';
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('download-inventory_check-staff-asset-failed:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 查看任务（费用报表）-导出表头
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('inventory_asset_detail_export.task_status'),//任务状态
            static::$t->_('inventory_asset_detail_export.forbid_punch_out'),//是否限制打卡
            static::$t->_('inventory_asset_detail_export.task_staff_id'),//盘点人工号
            static::$t->_('inventory_asset_detail_export.task_staff_name'),//盘点人姓名
            static::$t->_('inventory_asset_detail_export.task_staff_job_name'),//盘点人职位
            static::$t->_('excel_title_store_name'),//所属网点
            static::$t->_('view_department'),//所属部门
            static::$t->_('inventory_asset_detail_export.task_staff_state'),//盘点人在职状态
            static::$t->_('inventory_asset_detail_export.terminal_staff_id'),//终止人工号
            static::$t->_('inventory_asset_detail_export.terminal_reason'),//终止原因
            static::$t->_('inventory_asset_detail_export.pack_asset_total'),//应盘资产总数
            static::$t->_('inventory_asset_detail_export.unpacked_asset_total'),//未盘资产总数
            static::$t->_('material_asset.barcode'),//barcode
            static::$t->_('asset_code'), //资产编码
            static::$t->_('material_asset.old_asset_code'),//旧资码
            static::$t->_('asset_sn'), //SN编码
            static::$t->_('asset_goods_name'),//资产名称
            static::$t->_('inventory_asset_detail_export.material_asset_num'),//资产数量
            static::$t->_('inventory_asset_detail_export.asset_staff_id'),//盘点时资产使用人工号
            static::$t->_('inventory_asset_detail_export.asset_use_status'),//盘点时资产状态
            static::$t->_('inventory_asset_detail_export.staff_id'),//台账现使用人工号
            static::$t->_('inventory_asset_detail_export.status'),//台账现使用状态
            static::$t->_('inventory_asset_detail_export.asset_status'),//盘点结果
            static::$t->_('inventory_asset_detail_export.asset_bar_code'),//盘盈barcode
            static::$t->_('inventory_asset_detail_export.asset_name'),//盘盈资产名称
            static::$t->_('inventory_asset_detail_export.asset_num'),//盘盈数量
            static::$t->_('inventory_asset_detail_export.revise_asset_code'),//修正新资产码
            static::$t->_('inventory_asset_detail_export.revise_sn_code'),//修正新SN码
            static::$t->_('inventory_asset_detail_export.revise_staff_id'),//修正使用人
            static::$t->_('inventory_asset_detail_export.revise_sys_store_name'),//修正使用地址
            static::$t->_('inventory_asset_detail_export.asset_reason'),//反馈备注
            static::$t->_('inventory_check_staff_asset_img'),//图片url
        ];
    }

    /**
     * 验证盘点报表-批量取消限制打卡、终止、提醒操作合法性
     * @param array $params 请求参数组
     * @return mixed
     * @throws ValidationException
     */
    public function validateBatchOperate(array $params)
    {
        $staff_task_list = MaterialInventoryCheckStaffModel::find([
            'columns' => 'id, forbid_punch_out, staff_id, inventory_check_id',
            'conditions' => 'id in ({ids:array}) and is_deleted = :is_deleted: and status in ({status:array})',
            'bind' => ['ids' => $params['ids'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => [InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING]]
        ])->toArray();
        $staff_task_ids = array_column($staff_task_list, 'id');
        if (count($params['ids']) != count($staff_task_ids)) {
            throw new ValidationException(static::$t->_('inventory_staff_task_batch_operate_invalid'), ErrCode::$VALIDATE_ERROR);
        }
        return $staff_task_list;
    }

    /**
     * 盘点报表-取消限制打卡
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchCancelPunchOut(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $staff_task_list = $this->validateBatchOperate($params);
            $this->logger->info('资产盘点-盘点管理-盘点报表-取消限制打卡 操作人信息：' . json_encode($user, JSON_UNESCAPED_UNICODE));
            $forbid_punch_out = array_unique(array_column($staff_task_list, 'forbid_punch_out'));
            if (in_array(InventoryCheckEnums::INVENTORY_CHECK_WHETHER_NO, $forbid_punch_out)) {
                throw new ValidationException(static::$t->_('inventory_staff_task_cancel_punch_out_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //盘点报表-取消限制打卡
            $staff_task_ids = implode(',', array_column($staff_task_list, 'id'));
            $now = date('Y-m-d H:i:s');
            $db = $this->getDI()->get('db_oa');
            $bool = $db->updateAsDict(
                (new MaterialInventoryCheckStaffModel())->getSource(),
                [
                    'forbid_punch_out' => InventoryCheckEnums::INVENTORY_CHECK_WHETHER_NO,
                    'updated_at' => $now
                ],
                ['conditions' => "id in ({$staff_task_ids})"]
            );

            if ($bool === false) {
                throw new BusinessException('资产盘点-盘点管理-盘点报表-取消限制打卡失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-batchCancelPunchOut-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 盘点报表-终止
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchTerminal(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $staff_task_list = $this->validateBatchOperate($params);
            $staff_task_ids = implode(',', array_column($staff_task_list, 'id'));
            $now = date('Y-m-d H:i:s');
            $db = $this->getDI()->get('db_oa');
            $bool = $db->updateAsDict(
                (new MaterialInventoryCheckStaffModel())->getSource(),
                [
                    'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_TERMINAL,
                    'terminal_staff_id' => $user['id'],
                    'terminal_reason' => $params['terminal_reason'],
                    'terminal_at' => $now,
                    'updated_at' => $now
                ],
                ['conditions' => "id in ({$staff_task_ids})"]
            );

            if ($bool === false) {
                throw new BusinessException('资产盘点-盘点管理-盘点报表-终止失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-batchTerminal-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 盘点报表-提醒
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function batchRemind(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //只有待盘点、盘点中的才可被提醒
            $staff_task_list = $this->validateBatchOperate($params);
            $staff_task_ids = array_column($staff_task_list, 'id');

            //一天只能被提醒一次
            $today = date('Y-m-d');
            $remind_list = MaterialInventoryCheckRemindModel::find([
                'columns' => 'staff_rel_id',
                'conditions' => 'staff_rel_id in ({staff_rel_id:array}) and remind_date = :remind_date:',
                'bind' => ['staff_rel_id' => $staff_task_ids, 'remind_date' => $today]
            ])->toArray();
            $remind_task_ids = array_column($remind_list, 'staff_rel_id');
            $need_remind_task_ids = array_diff($staff_task_ids, $remind_task_ids);

            //存在需要提醒的任务则去提醒
            if ($need_remind_task_ids) {
                $inventory_check_id = $staff_task_list[0]['inventory_check_id'];//盘点单ID
                $country_code = get_country_code();
                $staff_task_list = array_column($staff_task_list, null, 'id');
                $need_remind_data = $message_info = $message_staff_users = [];
                foreach ($need_remind_task_ids as $id) {
                    $one_staff_task_info = $staff_task_list[$id];
                    $need_remind_data[] = [
                        'inventory_check_id' => $one_staff_task_info['inventory_check_id'],
                        'staff_rel_id' => $one_staff_task_info['id'],
                        'staff_id' => $one_staff_task_info['staff_id'],
                        'create_staff_id' => $user['id'],
                        'remind_date' => $today,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    $message_staff_users[] = $one_staff_task_info['staff_id'];
                }
                $remind_model = new MaterialInventoryCheckRemindModel();
                $bool = $remind_model->batch_insert($need_remind_data);
                if ($bool === false) {
                    throw new BusinessException('资产盘点-盘点管理-盘点报表-提醒失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                //发送站内信
                $message_info = [
                    'message_title' => InventoryCheckEnums::$inventory_remind_title[$country_code],
                    'category' => InventoryCheckEnums::MESSAGE_CATEGORY_INVENTORY_CHECK_REMIND,
                    'message_content' => $inventory_check_id
                ];
                $this->sendMessage($message_staff_users, $message_info);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('inventory_check-batchRemind-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 获取进行中、待发送盘点任务的盘点单清单
     * @return mixed
     */
    public function getSendTaskInventoryList()
    {
        return MaterialInventoryCheckModel::find([
            'conditions' => 'status = :status: and is_send_task = :task_status: and is_deleted = :is_deleted: and is_new = :is_new: and end_at > :check_end:',
            'bind' => ['status' => InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING, 'task_status' => InventoryCheckEnums::TASK_WAIT, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'is_new' => InventoryCheckEnums::IS_NEW_YES, 'check_end' => date('Y-m-d H:i:s')]
        ]);
    }

    /**
     * 系统定时-下发盘单任务 - 获取未开始的盘点总数（盘点单的开始时间到达后，自动开始下发）
     * @return mixed
     */
    public function getUnStartInventoryCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) AS count');
        $builder->from(['main' => MaterialInventoryCheckModel::class]);
        $builder = $this->getUnStartInventoryCondition($builder);
        return intval($builder->getQuery()->getSingleResult()->count);
    }

    /**
     * 系统定时-下发盘单任务 - 获取未开始的盘点清单（盘点单的开始时间到达后，自动开始下发）列表
     * @param array $condition 筛选条件组
     * @return mixed
     */
    public function getUnStartInventoryList(array $condition = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialInventoryCheckModel::class]);
        $builder = $this->getUnStartInventoryCondition($builder);
        $builder->limit($condition['pageSize'], $condition['offset']);
        return $builder->getQuery()->execute();
    }

    /**
     * 组装系统定时-下发盘单任务搜索条件
     * @param object $builder 查询器对象
     * @return mixed
     */
    public function getUnStartInventoryCondition(object $builder)
    {
        $builder->andWhere('main.is_deleted = :is_deleted: and main.is_new = :is_new:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'is_new' => InventoryCheckEnums::IS_NEW_YES]);
        $builder->andWhere('main.status = :status:', ['status' => InventoryCheckEnums::INVENTORY_CHECK_STATUS_NOT_STARTED]);
        $builder->andWhere('main.is_send_task = :task_wait:', ['task_wait' => InventoryCheckEnums::TASK_WAIT]);
        $builder->andWhere('main.start_at <= :check_start: and main.end_at > :check_start:', ['check_start' => date('Y-m-d H:i:s')]);
        return $builder;
    }

    /**
     * 生成员工盘点任务
     * @param object $inventory 盘点单对象
     * @return array
     */
    public function createStaffTask(object $inventory)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $now = date('Y-m-d H:i:s');
        try {
            if (!empty($inventory) && $inventory->status == InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING && $inventory->is_send_task == InventoryCheckEnums::TASK_WAIT) {
                //第一步先将盘点单的任务发送状态：发送中
                $inventory->is_send_task = InventoryCheckEnums::TASK_ING;
                $inventory->updated_at = $now;
                $bool = $inventory->save();
                if ($bool === false) {
                    throw new BusinessException('盘点单的任务发送状态改为发送中失败；可能的原因是：' . get_data_object_error_msg($inventory), ErrCode::$BUSINESS_ERROR);
                }
                //第二步获取盘点范围的员工信息及其资产信息
                $asset_list = $this->getInventoryStaffAssetsList($inventory);

                //第三步组装员工任务，员工资产
                $staff_task_asset_list = $this->formatStaffTaskAndAssets($inventory, $asset_list, $now);
                $has_asset_staff_ids = $staff_task_asset_list['has_asset_staff_ids'];//拥有资产的员工工号组
                $staff_asset_list = $staff_task_asset_list['staff_asset_list'];//资产持有人或指定盘点人与资产清单关系（任务组）

                //第四步若盘点资产空的员工信息，还需要组装资产空的员工任务
                $empty_staff_task_list = $this->getEmptyAssetStaff($inventory, $has_asset_staff_ids, $now);
                $empty_asset_staff_ids = $empty_staff_task_list['empty_asset_staff_ids'];//无资产员工工号组
                $empty_staff_asset_list = $empty_staff_task_list['empty_staff_asset_list'];//无资产员工任务组

                //第五步插入数据
                $task_list = array_merge($staff_asset_list, $empty_staff_asset_list);
                $this->saveStaffTask($inventory, $task_list);

                //发送盘点通知
                $task_staff_ids = array_keys($staff_asset_list);
                $staff_ids = array_merge($task_staff_ids, $empty_asset_staff_ids);
                $message_info = [
                    'message_title' => InventoryCheckEnums::$inventory_notice_title[get_country_code()] . '(' . date('md', strtotime($inventory->start_at)) . '-' . date('md', strtotime($inventory->end_at)) . ')',
                    'category' => InventoryCheckEnums::MESSAGE_CATEGORY_INVENTORY_CHECK,
                    'message_content' => $inventory->id,
                    'top' => 1
                ];
                $this->sendMessage($staff_ids, $message_info);
            }
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
            $real_message = $e->getMessage();
        }

        //第六步任何一步失败了，盘点单的任务发送状态回退至：0待发送
        if (!empty($real_message)) {
            $inventory->is_send_task = InventoryCheckEnums::TASK_WAIT;
            $inventory->updated_at = $now;
            $bool = $inventory->save();
            if ($bool === false) {
                $this->logger->warning('盘点单的任务发送状状态回退至待发送失败;状态值：' . $inventory->is_send_task . '；可能的原因是：' . get_data_object_error_msg($inventory));
            }
            $this->logger->warning('inventory_check-createStaffTask-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 获取盘点范围的员工信息及其资产信息
     * @param object $inventory 盘点单对象
     * @return mixed
     */
    private function getInventoryStaffAssetsList(object $inventory)
    {
        //盘点范围-指定网点串
        $sys_store_ids = $inventory->sys_store_ids ? explode(',', $inventory->sys_store_ids) : [];

        //盘点范围-网点类型串
        if (!empty($inventory->categorys)) {
            $sys_store_list = (new StoreRepository())->searchSysStore(['category' => explode(',', $inventory->categorys)]);
            $sys_store_ids = array_column($sys_store_list, 'id');
            //若根据网点类型未找到符合条件的网点直接跳过，不查找资产台账
            if (empty($sys_store_ids)) {
                return [];
            }
        }

        //盘点范围 - 部门
        $department_ids = $this->getInventoryDepartmentIds($inventory);

        return MaterialAssetsRepository::getInstance()->searchAsset([
            'category_id' => $inventory->material_category_ids ? explode(',', $inventory->material_category_ids) : [],
            'bar_code' => $inventory->barcodes ? explode(',', $inventory->barcodes) : [],
            'status' => $inventory->asset_status ? explode(',', $inventory->asset_status) : EnumsService::getInstance()->getSettingEnvValueIds('material_inventory_assets_status'),
            'company_id' => $inventory->company_ids ? explode(',', $inventory->company_ids) : [],
            'staff_id' => $inventory->staff_ids ? explode(',', $inventory->staff_ids) : [],
            'is_staff_empty' => 1,//员工不为空
            'job_id' => $inventory->job_ids ? explode(',', $inventory->job_ids) : [],
            'sys_store_id' => $sys_store_ids,
            'node_department_id' => $department_ids,
            'use' => $inventory->asset_use,//资产使用方向
        ]);
    }

    /**
     * 获取盘点单部门组
     * @param object $inventory 盘点单对象
     * @return array
     */
    public function getInventoryDepartmentIds(object $inventory)
    {
        $department_ids = [];
        $inventory_check_rel = $inventory->getCheckRel()->toArray();
        if (!empty($inventory_check_rel)) {
            $department_service = new DepartmentService();
            foreach ($inventory_check_rel as $item) {
                $department_ids[] = $item['department_id'];
                if ($item['is_include_sub'] == SettingEnums::IS_INCLUDE_SUB) {
                    //包含子部门
                    $department_child_ids = $department_service->getChildrenListByDepartmentIdV2($item['department_id'], true);
                    $department_ids = array_merge($department_ids, $department_child_ids);
                }
            }
        }
        return $department_ids;
    }

    /**
     * 按照资产持有人或指定盘点人维度汇总任务人与资产清单关系
     * @param object $inventory 盘点单对象
     * @param array $asset_list 资产台账里的资产清单
     * @param string $now 操作时间
     * @return array
     */
    private function formatStaffTaskAndAssets(object $inventory, array $asset_list, string $now)
    {
        $data = ['staff_asset_list' => [], 'has_asset_staff_ids' => []];

        //获取网点所属片区、大区
        $sys_store_ids = array_values(array_unique(array_filter(array_column($asset_list, 'sys_store_id'))));
        $sys_store_list = (new StoreRepository())->getStoreListByIds($sys_store_ids);

        //组装资产持有人或指定盘点人维度汇总任务人与资产清单关系
        $staff_asset_list = [];
        if ($inventory->ploy == InventoryCheckEnums::PLOY_ALL) {
            //全员按照资产持有人分组资产清单
            foreach ($asset_list as $asset) {
                if (!empty($staff_asset_list[$asset['staff_id']])) {
                    $staff_asset_list[$asset['staff_id']]['task']['pack_asset_total'] += 1;
                    $staff_asset_list[$asset['staff_id']]['task']['unpacked_asset_total'] += 1;
                } else {
                    $staff_asset_list[$asset['staff_id']]['task'] = [
                        'staff_id' => $asset['staff_id'],
                        'inventory_check_id' => $inventory->id,
                        'forbid_punch_out' => $inventory->forbid_punch_out,
                        'pack_asset_total' => 1,
                        'unpacked_asset_total' => 1,
                        'is_new' => InventoryCheckEnums::IS_NEW_YES,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }

                $one_sys_store_info = $sys_store_list[$asset['sys_store_id']] ?? [];
                $staff_asset_list[$asset['staff_id']]['assets'][] = [
                    'inventory_check_id' => $inventory->id,
                    'staff_id' => $asset['staff_id'],
                    'staff_name' => $asset['staff_name'],
                    'node_department_id' => $asset['node_department_id'],
                    'node_department_name' => $asset['node_department_name'],
                    'sys_store_id' => $asset['sys_store_id'],
                    'store_name' => $asset['store_name'],
                    'region_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_region']) ? $one_sys_store_info['manage_region'] : 0,
                    'piece_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_piece']) ? $one_sys_store_info['manage_piece'] : 0,
                    'asset_use_status' => $asset['status'],
                    'category_type' => $asset['category_type'],
                    'bar_code' => $asset['bar_code'],
                    'name_zh' => $asset['name_zh'],
                    'name_en' => $asset['name_en'],
                    'name_local' => $asset['name_local'],
                    'asset_code' => $asset['asset_code'],
                    'old_asset_code' => $asset['old_asset_code'],
                    'sn_code' => $asset['sn_code'],
                    'asset_num' => 1,
                    'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT,
                    'is_new' => InventoryCheckEnums::IS_NEW_YES,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }

            //拥有资产的员工工号组(含有离职) - 只有全员盘点策略盘点员工名下资产为空为是，才会用到这个数据
            $data['has_asset_staff_ids'] = array_keys($staff_asset_list);

            //只给员工在职的（含待离职），正式，实习的非子账号，给这些员工发有资产数量的资产任务
            $staff_asset_list = $this->formatStaffTask($staff_asset_list);
        } else if ($inventory->ploy == InventoryCheckEnums::PLOY_STAFF) {
            //指定盘点人则所有资产都归改人员盘点
            $staff_id = $inventory->check_staff_id;
            $user_info = StaffService::getInstance()->searchStaff(['staff_id' => $staff_id, 'limit' => 1])['data'][0] ?? [];
            if (!empty($user_info)) {
                $asset_count = count($asset_list);//资产总数
                $staff_asset_list[$staff_id]['task'] = [
                    'inventory_check_id' => $inventory->id,
                    'staff_id' => $staff_id,
                    'store_id' => $user_info['sys_store_id'],
                    'store_name'=> $user_info['store_name'],
                    'department_id' => $user_info['node_department_id'],
                    'department_name' => $user_info['node_department_name'],
                    'job_id' => $user_info['job_id'],
                    'job_name' => $user_info['job_name'],
                    'forbid_punch_out' => $inventory->forbid_punch_out,
                    'pack_asset_total' => $asset_count,
                    'unpacked_asset_total' => $asset_count,
                    'is_new' => InventoryCheckEnums::IS_NEW_YES,
                    'created_at' => $now,
                    'updated_at' => $now
                ];

                foreach ($asset_list as $asset) {
                    $one_sys_store_info = $sys_store_list[$asset['sys_store_id']] ?? [];
                    $staff_asset_list[$staff_id]['assets'][] = [
                        'inventory_check_id' => $inventory->id,
                        'staff_id' => $asset['staff_id'],
                        'staff_name' => $asset['staff_name'],
                        'node_department_id' => $asset['node_department_id'],
                        'node_department_name' => $asset['node_department_name'],
                        'sys_store_id' => $asset['sys_store_id'],
                        'store_name' => $asset['store_name'],
                        'region_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_region']) ? $one_sys_store_info['manage_region'] : 0,
                        'piece_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_piece']) ? $one_sys_store_info['manage_piece'] : 0,
                        'asset_use_status' => $asset['status'],
                        'category_type' => $asset['category_type'],
                        'bar_code' => $asset['bar_code'],
                        'name_zh' => $asset['name_zh'],
                        'name_en' => $asset['name_en'],
                        'name_local' => $asset['name_local'],
                        'asset_code' => $asset['asset_code'],
                        'old_asset_code' => $asset['old_asset_code'],
                        'sn_code' => $asset['sn_code'],
                        'asset_num' => 1,
                        'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT,
                        'is_new' => InventoryCheckEnums::IS_NEW_YES,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }
            }
        } else {
            //指定网点主管盘点；按照网点维度进行汇总
            //根据网点去组织架构树中，找这个网点，在“OA-组织架构-部门架构”中维护的负责人（不区分是正式还是代理）。只给在职（含待离职）的，非子账号的进行发送任务。
            //1. 如果捞出来的资产，按照资产卡片上的使用网点维度汇总，有head office，则无需给head office这个网点创建盘点任务，
            //2. 如果下发时，网点主管不是在职（含待离职）的，非子账号，则不给其创建盘点任务
            //3. 如果下发时，对应的网点负责人为空，则不给这个网点创建盘点任务
            foreach ($asset_list as $asset) {
                $one_sys_store_info = $sys_store_list[$asset['sys_store_id']] ?? [];
                if (empty($asset['sys_store_id']) || $asset['sys_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG || empty($one_sys_store_info) || empty($one_sys_store_info['manager_id'])) {
                    continue;
                }

                $sys_store_manager_id = $one_sys_store_info['manager_id'];
                if (!empty($staff_asset_list[$sys_store_manager_id])) {
                    $staff_asset_list[$sys_store_manager_id]['task']['pack_asset_total'] += 1;
                    $staff_asset_list[$sys_store_manager_id]['task']['unpacked_asset_total'] += 1;
                } else {
                    $staff_asset_list[$sys_store_manager_id]['task'] = [
                        'staff_id' => $sys_store_manager_id,
                        'inventory_check_id' => $inventory->id,
                        'forbid_punch_out' => $inventory->forbid_punch_out,
                        'pack_asset_total' => 1,
                        'unpacked_asset_total' => 1,
                        'is_new' => InventoryCheckEnums::IS_NEW_YES,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }

                //按照网点维度进行汇总
                $staff_asset_list[$sys_store_manager_id]['assets'][] = [
                    'inventory_check_id' => $inventory->id,
                    'staff_id' => $asset['staff_id'],
                    'staff_name' => $asset['staff_name'],
                    'node_department_id' => $asset['node_department_id'],
                    'node_department_name' => $asset['node_department_name'],
                    'sys_store_id' => $asset['sys_store_id'],
                    'store_name' => $asset['store_name'],
                    'region_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_region']) ? $one_sys_store_info['manage_region'] : 0,
                    'piece_id' => $one_sys_store_info && !empty($one_sys_store_info['manage_piece']) ? $one_sys_store_info['manage_piece'] : 0,
                    'asset_use_status' => $asset['status'],
                    'category_type' => $asset['category_type'],
                    'bar_code' => $asset['bar_code'],
                    'name_zh' => $asset['name_zh'],
                    'name_en' => $asset['name_en'],
                    'name_local' => $asset['name_local'],
                    'asset_code' => $asset['asset_code'],
                    'old_asset_code' => $asset['old_asset_code'],
                    'sn_code' => $asset['sn_code'],
                    'asset_num' => 1,
                    'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT,
                    'is_new' => InventoryCheckEnums::IS_NEW_YES,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
            }
            //只给员工在职的（含待离职），正式，实习的非子账号，给这些员工发有资产数量的资产任务
            $staff_asset_list = $this->formatStaffTask($staff_asset_list);
        }
        $data['staff_asset_list'] = $staff_asset_list;
        return $data;
    }

    /**
     * 只给员工在职的（含待离职），正式，实习的非子账号，给这些员工发有资产数量的资产任务
     * @param array $staff_asset_list 按照资产持有人或指定盘点人维度汇总任务人与资产清单关系组
     * @return mixed
     */
    private function formatStaffTask($staff_asset_list)
    {
        $has_asset_staff_ids = array_keys($staff_asset_list);//拥有资产的员工工号组
        $staff_id_arr_all = array_chunk($has_asset_staff_ids, 2000);
        $staff_list = [];
        foreach ($staff_id_arr_all as $staff_id_chunk) {
            $tmp_staff_list = StaffService::getInstance()->searchStaff(['staff_id' => $staff_id_chunk, 'state' => StaffInfoEnums::STAFF_STATE_IN, 'limit' => count($staff_id_chunk)]);
            $tmp_staff_list = $tmp_staff_list['data'];
            $staff_list = array_merge($staff_list, $tmp_staff_list);
        }
        $staff_list = array_column($staff_list, null, 'staff_id');
        foreach ($staff_asset_list as $staff_id => &$task) {
            $one_staff_info = $staff_list[$staff_id] ?? [];
            if (empty($one_staff_info)) {
                unset($staff_asset_list[$staff_id]);
            }
            $task['task']['store_id'] = $one_staff_info['sys_store_id'];
            $task['task']['store_name'] = $one_staff_info['store_name'];
            $task['task']['department_id'] = $one_staff_info['node_department_id'];
            $task['task']['department_name'] = $one_staff_info['node_department_name'];
            $task['task']['job_id'] = $one_staff_info['job_id'];
            $task['task']['job_name'] = $one_staff_info['job_name'];
        }
        return $staff_asset_list;
    }

    /**
     * 获取盘点资产为空的员工-组装下发盘点任务
     * @param object $inventory 盘点单对象
     * @param array $has_asset_staff_ids 拥有资产的员工工号组
     * @param string $now 操作时间
     * @return array
     */
    private function getEmptyAssetStaff(object $inventory, array $has_asset_staff_ids, string $now)
    {
        $data = ['empty_staff_asset_list' => [], 'empty_asset_staff_ids' => []];
        $list = $staff_asset_list = [];
        //全员盘点才需要找名下资产为0的员工，下发盘点任务；指定盘点人无需找有资产和无资产都是一条任务；指定网点主管盘点，是否盘点名下资产为空的员工，只可选择否
        if ($inventory->ploy == InventoryCheckEnums::PLOY_ALL && $inventory->empty_asset_status == InventoryCheckEnums::INVENTORY_CHECK_WHETHER_YES) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('si.staff_info_id as staff_id, si.sys_store_id as store_id, s.name as store_name, si.node_department_id as department_id, d.name as department_name, sjt.id as job_id, sjt.job_name')
                ->from(['si' => HrStaffInfoModel::class])
                ->leftJoin(HrJobTitleModel::class, 'cast(si.job_title as UNSIGNED) = sjt.id', 'sjt')
                ->leftJoin(SysDepartmentModel::class, 'si.node_department_id = d.id ', 'd')
                ->leftJoin(SysStoreModel::class, 'si.sys_store_id = s.id ', 's')
                ->where('si.state = :state: and si.is_sub_staff = :is_sub_staff: and si.formal in ({formals:array})', ['state' => StaffInfoEnums::STAFF_STATE_IN, 'is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO, 'formals' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]]);
            //盘点范围-指定网点串
            $sys_store_ids = $inventory->sys_store_ids ? explode(',', $inventory->sys_store_ids) : [];
            if (!empty($sys_store_ids)) {
                $builder->inWhere('si.sys_store_id', $sys_store_ids);
            }
            //盘点范围-指定部门
            $department_ids = $this->getInventoryDepartmentIds($inventory);
            if (!empty($department_ids)) {
                $builder->inWhere('si.node_department_id', $department_ids);
            }
            //盘点范围-指定员工串
            $staff_ids = $inventory->staff_ids ? explode(',', $inventory->staff_ids) : [];
            if (!empty($staff_ids)) {
                $builder->inWhere('si.staff_info_id', $staff_ids);
            }
            //盘点范围-指定职位串
            $job_ids = $inventory->job_ids ? explode(',', $inventory->job_ids) : [];
            if (!empty($job_ids)) {
                $builder->inWhere('si.job_title', $job_ids);
            }
            $staff_asset_list = $builder->getQuery()->execute()->toArray();

            //组装盘点任务 && 资产
            foreach ($staff_asset_list as $item) {
                //查询到的员工工号需要过滤有资产的员工【拥有资产的员工很多，直接查询会慢查询所以用php处理】
                if (!empty($has_asset_staff_ids) && in_array($item['staff_id'], $has_asset_staff_ids)) {
                    continue;
                }
                $item['store_name'] = ($item['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) ? Enums::PAYMENT_HEADER_STORE_NAME : $item['store_name'];
                $item['inventory_check_id'] = $inventory->id;
                $item['forbid_punch_out'] = $inventory->forbid_punch_out;
                $item['pack_asset_total'] = 0;
                $item['unpacked_asset_total'] = 0;
                $item['is_new'] = InventoryCheckEnums::IS_NEW_YES;
                $item['created_at'] = $now;
                $item['updated_at'] = $now;
                $list[$item['staff_id']] = [
                    'task' => $item,
                    'assets' => []
                ];
            }
            $data['empty_staff_asset_list'] = $list;
            $data['empty_asset_staff_ids'] = array_keys($list);
        }
        return $data;
    }

    /**
     * 保存员工盘点任务
     * @param object $inventory 盘点单对象
     * @param array $task_list 盘点任务+盘点资产清单
     * @throws BusinessException
     * @return bool
     */
    public function saveStaffTask(object $inventory, array $task_list)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now = date('Y-m-d H:i:s');

            //盘点单的任务发送状态：已发送
            $inventory->is_send_task = InventoryCheckEnums::TASK_DONE;
            $inventory->updated_at = $now;
            $bool = $inventory->save();
            if ($bool === false) {
                throw new BusinessException('盘点单的任务发送状态改为已发送失败,状态值：'.$inventory->is_send_task . '；可能的原因是：' . get_data_object_error_msg($inventory), ErrCode::$BUSINESS_ERROR);
            }

            //生成员工盘点任务 && 盘点资产清单
            $asset_model = new MaterialInventoryCheckStaffAssetsModel();
            foreach ($task_list as $item) {
                $task = new MaterialInventoryCheckStaffModel();
                $bool = $task->i_create($item['task']);
                if ($bool === false) {
                    throw new BusinessException('盘点单-员工任务存储失败;数据为：' . json_encode($item['task'], JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($task), ErrCode::$BUSINESS_ERROR);
                }

                //名下无资产的，无需存储资产清单
                if (empty($item['assets'])) {
                    continue;
                }

                //名下有资产的，需要存储资产清单
                foreach ($item['assets'] as &$asset) {
                    $asset['staff_rel_id'] = $task->id;
                }
                $bool = $asset_model->batch_insert($item['assets']);
                if ($bool === false) {
                    throw new BusinessException('盘点单-员工任务-资产清单存储失败，盘单单ID：' . $inventory->id, ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 发送站内信
     * @param array $staff_list 员工工号组
     * @param array $kit_param 消息参数组
     * @return bool
     */
    private function sendMessage(array $staff_list, array $kit_param)
    {
        //盘点任务id，发送范围，需发送员工数，发送结果，发送时间
        try {
            if (!empty($staff_list)) {
                $staff_users = [];
                foreach ($staff_list as $staff_id) {
                    $staff_users[] = ['id' => $staff_id];
                }
                $kit_param['staff_users'] = $staff_users;

                $bi_rpc = (new ApiClient('bi_svc', '', 'add_kit_message'));
                $bi_rpc->setParams([$kit_param]);
                $res = $bi_rpc->execute();
                $bi_return = $res && isset($res['result']) && $res['result']['code'] == ErrCode::$SUCCESS;
                $res_msg = $bi_return ? '成功' : '失败';
                $res_log = '给员工工号组[' . json_encode($staff_list, JSON_UNESCAPED_UNICODE) . ']；发送盘点单ID[' . $kit_param['message_content'] . ']站内信消息了；发送时间[' . date('Y-m-d H:i:s') . ']；发送结果[' . $res_msg . ']';
                $this->logger->info($res_log);
            }
        } catch (\Exception $e) {
            $this->logger->warning("给员工发送盘点站内信消息，失败[异常]" . $e->getMessage() . PHP_EOL);
        }
        return true;
    }

    /**
     * 更新员工盘点任务
     * @param object $inventory 盘点单对象
     * @throws BusinessException
     * @return bool
     */
    public function updateStaffTask(object $inventory)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now = date('Y-m-d H:i:s');

            //盘点单的状态状态：已完成
            $inventory->status = InventoryCheckEnums::INVENTORY_CHECK_STATUS_DONE;
            $inventory->updated_at = $now;
            $bool = $inventory->save();
            if ($bool === false) {
                throw new BusinessException('盘点单的状态改为已完成失败;可能的原因是：' . get_data_object_error_msg($inventory), ErrCode::$BUSINESS_ERROR);
            }
            //盘点单下待盘点、盘点中的任务：已完成
            $inventory_task_list = $this->getInventoryTaskList($inventory->id);
            if (!empty($inventory_task_list)) {
                $staff_task_ids = implode(',', array_column($inventory_task_list, 'id'));
                $bool = $db->updateAsDict(
                    (new MaterialInventoryCheckStaffModel())->getSource(),
                    [
                        'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE,
                        'updated_at' => $now
                    ],
                    ['conditions' => "id in ({$staff_task_ids})"]
                );

                if ($bool === false) {
                    throw new BusinessException('盘点单的任务状态改为已完成失败 ' . json_encode($staff_task_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 获取某个盘点单下特定状态下的任务清单
     * @param integer $inventory_check_id 盘点单ID
     * @param array $status
     * @return mixed
     */
    public function getInventoryTaskList(int $inventory_check_id, array $status = [InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING])
    {
        return MaterialInventoryCheckStaffModel::find([
            'conditions' => 'inventory_check_id = :inventory_check_id: and status in ({status:array}) and is_deleted = :is_deleted:',
            'bind' => ['inventory_check_id' => $inventory_check_id, 'status' => $status, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
    }

    /**
     * 获取某个盘点单/盘点计划下下某员工的盘点任务信息
     * @param integer $inventory_check_id 盘点单ID
     * @param integer $staff_id 员工工号
     * @return mixed
     * @throws ValidationException
     */
    public function getStaffTaskInfo(int $inventory_check_id, int $staff_id)
    {
        $inventory_check_staff_info =  MaterialInventoryCheckStaffModel::findFirst([
            'conditions' => 'inventory_check_id = :inventory_check_id: and staff_id = :staff_id: and is_deleted = :is_deleted:',
            'bind' => ['inventory_check_id' => $inventory_check_id, 'staff_id' => $staff_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ]);
        if (empty($inventory_check_staff_info)) {
            throw new ValidationException(static::$t->_('inventory_staff_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $inventory_check_staff_info;
    }

    /**
     * 获取盘点消息详情信息
     * @param array $params 请求参数组
     * @return array
     */
    public function getInventoryMsgDetail(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            Validation::validate($params, InventoryCheckService::$validate_msg_detail);
            $type = $params['type'] ?? InventoryCheckEnums::MESSAGE_CATEGORY_INVENTORY_CHECK;//消息类型不传递默认44
            $inventory_check_info = $this->getInventoryCheckInfoById($params['inventory_check_id']);//盘点单信息
            $inventory_check_staff_info = $this->getStaffTaskInfo($params['inventory_check_id'], $params['staff_id']);//员工盘点任务

            //员工未点击完成盘点，但是盘点周期结束了也要该状态为已完成
            if (in_array($inventory_check_staff_info->status, [InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING]) && strtotime($inventory_check_info->end_at) <= time()) {
                $inventory_check_staff_info->status = InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE;
            }
            $data = [
                'inventory_check_id' => $inventory_check_info->id,//盘点单ID
                'start_at' => $inventory_check_info->start_at,//盘点开始时间
                'end_at' => $inventory_check_info->end_at,//盘点结束时间
                'task_id' => $inventory_check_staff_info->id,//任务ID
                'status' => $inventory_check_staff_info->status,//任务状态
                'count' => 0,
                'rule_url' => ''
             ];

            //资产盘点-通知
            if ($type == InventoryCheckEnums::MESSAGE_CATEGORY_INVENTORY_CHECK) {
                //获取员工盘点任务名下待盘资产数量
                $data['count'] = $inventory_check_staff_info->unpacked_asset_total;

                //盘点规则
                $group_ids = MaterialSettingService::getInstance()->getStaffInventoryGroups($inventory_check_info->create_staff_id);
                if (!empty($group_ids)) {
                    //根据组ID获取盘点规则url不为空的盘点组设置的盘点规则（盘点组ID升序）
                    $data['rule_url'] = MaterialSettingService::getInstance()->getRuleUrl($group_ids);
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-inventory-getInventoryMsgDetail-failed:'.$e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 资产盘点-开始盘点/去盘点
     * @param array $params 请求参数组
     * @return array
     */
    public function taskStart(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];
        try {
            Validation::validate($params, InventoryCheckService::$validate_msg_detail);
            $inventory_check_info = $this->getInventoryCheckInfoById($params['inventory_check_id']);//盘点单信息
            $inventory_check_staff_info = $this->getStaffTaskInfo($params['inventory_check_id'], $params['staff_id']);//员工盘点任务
            if ($inventory_check_staff_info->status == InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT && strtotime($inventory_check_info->end_at) > time()) {
                $inventory_check_staff_info->status = InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING;
                $inventory_check_staff_info->updated_at = date('Y-m-d H:i:s');
                $bool = $inventory_check_staff_info->save();
                if ($bool === false) {
                    throw new BusinessException('盘点资产-修改任务状态或未盘资产总数失败：' . json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material-inventory-taskStart-failed:'.$e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束记录数
     * @param array $params['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 查询条件
     * @return mixed
     */
    public function getTaskCount(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(task.id) as total');
        $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
        $builder->leftjoin(MaterialInventoryCheckModel::class, 'task.inventory_check_id = main.id', 'main');
        $builder = $this->getTaskCondition($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 资产盘点-审批-盘点单-待处理,已结束列表
     * @param array $params['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 查询条件
     * @param array $user_info 当前登陆者信息组
     * @return array
     */
    public function getTaskList(array $params, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            Validation::validate($params, InventoryCheckService::$validate_task_list);

            //获得数量
            $count = $this->getTaskCount($params);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'main.name',
                    'main.end_at',
                    'main.remark',
                    'main.create_staff_id',
                    'main.status inventory_check_status',
                    'task.id',
                    'task.inventory_check_id',
                    'task.created_at',
                    'task.status',
                    'task.pack_asset_total',
                    'task.unpacked_asset_total',
                    'task.terminal_reason',
                    'task.is_new'
                ]);
                $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
                $builder->leftjoin(MaterialInventoryCheckModel::class, 'task.inventory_check_id = main.id', 'main');
                $builder = $this->getTaskCondition($builder, $params);
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $type = $params['type'] ?? InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING;
                $builder->orderBy($type == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING ? 'task.status DESC, main.end_at,main.id ASC' : 'task.id DESC');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatTaskList($items, $user_info, $params['type']);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-task-list-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 组装盘点任务查询条件
     * @param object $builder 查询器对象
     * @param array $condition['staff_id'=>'员工工号','type'=>'1待处理，2已处理'] 筛选条件组
     * @return mixed
     */
    private function getTaskCondition(object $builder, array $condition)
    {
        $builder->where('task.staff_id = :staff_id: and task.is_deleted = :is_deleted: and main.is_deleted = :is_deleted:', ['staff_id' => $condition['staff_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $condition['type'] = $condition['type'] ?? InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING;
        if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING) {
            //待处理(待盘点/盘点中/未到盘点结束时间)
            $builder->andWhere('task.status < :status: and main.end_at > :now:', ['status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, 'now' => date('Y-m-d H:i:s')]);
        } else if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PROCESSED) {
            //已完成(已完成/已终止)
            $builder->andWhere('task.status in ({status:array})', ['status' => [InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE, InventoryCheckEnums::INVENTORY_CHECK_STATUS_TERMINAL]]);
        }
        return $builder;
    }

    /**
     * 格式化任务列表
     * @param array $list 任务列表
     * @param array $user_info 当前用户信息
     * @param integer $type 1待处理，2已完成
     * @return array
     */
    private function formatTaskList(array $list, array $user_info, int $type)
    {
        if (empty($list)) {
            return [];
        }
        $q_a_urls = $task_asset_list = [];
        if ($type == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING) {
            //待处理(待盘点/盘点中/未到盘点结束时间)
            $create_staff_ids = array_values(array_unique(array_column($list, 'create_staff_id')));
            $q_a_urls = MaterialSettingService::getInstance()->getQaUrl($create_staff_ids);
        } else if ($type == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PROCESSED) {
            //已完成(已完成/已终止)
            $task_ids = array_column(array_filter($list, function ($var) { return $var['is_new'] == InventoryCheckEnums::IS_NEW_YES; }), 'id');
            if (!empty($task_ids)) {
                $task_asset_list = MaterialInventoryCheckStaffAssetsModel::find([
                    'columns' => 'SUM(asset_num) as asset_num, staff_rel_id',
                    'conditions' => 'staff_rel_id in ({staff_rel_id:array}) and is_deleted = :is_deleted: and status != :status: and is_new = :is_new:',
                    'bind' => ['staff_rel_id' => $task_ids, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT, 'is_new' => InventoryCheckEnums::IS_NEW_YES],
                    'group' => 'staff_rel_id'
                ])->toArray();
                $task_asset_list = array_column($task_asset_list, 'asset_num', 'staff_rel_id');
            }
        }

        foreach ($list as &$values) {
            $values['real_asset_total'] = $task_asset_list[$values['id']] ?? 0;//实盘资产数量
            $values['q_a_url'] = $q_a_urls[$values['create_staff_id']] ?? '';
            $values['staff_name'] = $user_info['name'] . '(' . $user_info['staff_id'] . ')';
            $values['status_text'] = static::$t->_(InventoryCheckEnums::$inventory_check_staff_status[$values['status']]);
        }
        return $list;
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点总数
     * @param array $params['task_id'=>'任务ID', 'staff_id'=>'任务人工号','type'=>'1待盘点，2已盘点'] 筛选条件组
     * @return int
     */
    public function getTaskAssetCount(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('SUM(asset.asset_num) as total');
        $builder->from(['asset' => MaterialInventoryCheckStaffAssetsModel::class]);
        $builder->leftJoin(MaterialInventoryCheckStaffModel::class, 'task.id = asset.staff_rel_id', 'task');
        $builder = $this->getTaskAssetCondition($builder, $params);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点列表
     * @param array $params['task_id'=>'任务ID', 'staff_id'=>'任务人工号', 'type'=>'1待处理，2已处理', 'name'=>'资产名称', 'code'=>'资产码/旧资产码/sn码'] 查询条件
     * @return array
     */
    public function getTaskAssetList(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            Validation::validate($params, InventoryCheckService::$validate_task_asset_list);
            //获得数量
            $count = $this->getTaskAssetListCount($params);
            if ($count > 0) {
                $category_type = MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS;
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'asset.id',
                    'asset.bar_code',
                    'asset.name_zh',
                    'asset.name_en',
                    'asset.name_local',
                    'asset.asset_code',
                    'asset.old_asset_code',
                    'asset.sn_code',
                    'asset.staff_id',
                    'asset.staff_name',
                    'asset.status',
                    'asset.category_type',
                    'asset.reason',
                    'asset.asset_num',
                    'task.status as task_status',
                    'main.status inventory_check_status',
                    'main.is_must_upload',
                    'main.is_only_take_pic',
                ]);
                $builder->from(['asset' => MaterialInventoryCheckStaffAssetsModel::class]);
                $builder->leftJoin(MaterialInventoryCheckStaffModel::class, 'task.id = asset.staff_rel_id', 'task');
                $builder->leftJoin(MaterialInventoryCheckModel::class, 'main.id = task.inventory_check_id', 'main');
                $builder = $this->getTaskAssetCondition($builder, $params);
                $builder->groupBy('CASE asset.category_type WHEN ' . $category_type . ' THEN asset.bar_code ELSE asset.id END');
                $builder->orderBy('NULL');
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatTaskAssetList($items, $params['task_id']);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-task-asset-list-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-待盘点、已盘点列表总数
     * @param array $params['task_id'=>'任务ID','type'=>'1待处理，2已处理', 'name'=>'资产名称', 'code'=>'资产码/旧资产码/sn码'] 查询条件
     * @return int
     */
    public function getTaskAssetListCount(array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('asset.id');
        $builder->from(['asset' => MaterialInventoryCheckStaffAssetsModel::class]);
        $builder->leftJoin(MaterialInventoryCheckStaffModel::class, 'task.id = asset.staff_rel_id', 'task');
        $builder = $this->getTaskAssetCondition($builder, $params);
        $builder->groupBy('CASE asset.category_type WHEN ' . MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS . ' THEN asset.bar_code ELSE asset.id END');
        $builder->orderBy('NULL');
        return (int) $builder->getQuery()->execute()->count();
    }

    /**
     * 组装盘点资产查询条件
     * @param object $builder 查询器对象
     * @param array $condition['task_id'=>'任务ID','type'=>'1待盘点，2已盘点'] 筛选条件组
     * @return mixed
     */
    private function getTaskAssetCondition(object $builder, array $condition)
    {
        $builder->where('task.id = :task_id: and task.staff_id = :staff_id: and asset.is_deleted = :is_deleted: and asset.is_new = :is_new:', ['task_id' => $condition['task_id'], 'staff_id' => $condition['staff_id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'is_new' => InventoryCheckEnums::IS_NEW_YES]);
        $condition['type'] = $condition['type'] ?? InventoryCheckEnums::INVENTORY_TASK_LIST_ALL;
        if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING) {
            //待盘点
            $builder->andWhere('asset.status = :status:', ['status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT]);
        } else if ($condition['type'] == InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PROCESSED) {
            //已盘点
            $builder->andWhere('asset.status > :status:', ['status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT]);
        }
        //资产名称
        $asset_name = $condition['asset_name'] ?? '';
        if (!empty($asset_name)) {
            $builder->andWhere('asset.' . get_lang_field_name('name_', static::$language) . ' like :asset_name:', ['asset_name' => '%' . $asset_name . '%']);
        }
        //编码
        $code = $condition['code'] ?? '';
        if (!empty($code)) {
            $builder->andWhere('asset.category_type = :category_type:', ['category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET]);
            $builder->andWhere('asset.asset_code like :code: OR asset.old_asset_code like :code: OR asset.sn_code like :code:', ['code' => '%' . $code . '%']);
        }
        return $builder;
    }

    /**
     * 格式化资产盘点列表
     * @param array $item 资产盘点列表
     * @param integer $task_id 任务ID
     * @return array
     */
    private function formatTaskAssetList(array $item, int $task_id)
    {
        if (empty($item)) {
            return [];
        }

        //针对耗材类的barcode，需要按照barcode分组汇总
        $barcode_nums = [];
        $barcode = array_values(array_unique(array_column($item, 'bar_code')));
        if (!empty($barcode)) {
            $barcode_list = MaterialInventoryCheckStaffAssetsModel::find([
                'columns' => 'asset_num, bar_code, status',
                'conditions' => 'staff_rel_id = :task_id: and bar_code in ({barcode:array}) and is_deleted = :is_deleted: and category_type = :category_type:',
                'bind'=> ['task_id' => $task_id, 'barcode' => $barcode, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS],
            ])->toArray();
            foreach ($barcode_list as $v) {
                //系统数量（1待盘点、6低值盘到、7低值盘亏数量和）
                if (in_array($v['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_LOSE])) {
                    if (isset($barcode_nums[$v['bar_code']]['asset_num'])) {
                        $barcode_nums[$v['bar_code']]['asset_num'] += $v['asset_num'];
                    } else {
                        $barcode_nums[$v['bar_code']]['asset_num'] = $v['asset_num'];
                    }
                }
                //实际数量 = 6低值盘到、8低值盘盈数量和
                if (in_array($v['status'], [InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT ])) {
                    if (isset($barcode_nums[$v['bar_code']]['real_asset_num'])) {
                        $barcode_nums[$v['bar_code']]['real_asset_num'] += $v['asset_num'];
                    } else {
                        $barcode_nums[$v['bar_code']]['real_asset_num'] = $v['asset_num'];
                    }
                }
            }
        }
        foreach ($item as &$value) {
            if ($value['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS) {
                $value['asset_num'] = $barcode_nums[$value['bar_code']]['asset_num'] ?? 0;
                $value['real_asset_num'] = $barcode_nums[$value['bar_code']]['real_asset_num'] ?? 0;
            } else {
                $value['real_asset_num'] = $value['asset_num'];
            }
            $value['status_text'] = ($value['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) ? (static::$t[InventoryCheckEnums::$inventory_check_staff_asset_status_by[$value['status']]] ?? '') : '';
        }
        return $item;
    }

    /**
     * 资产盘点-资产详情
     * @param int $task_id 任务ID
     * @param int $staff_id 员工ID
     * @return mixed
     * @throws ValidationException
     */
    public function getTaskInfo(int $task_id, int $staff_id)
    {
        $inventory_check_staff_info =  MaterialInventoryCheckStaffModel::findFirst([
            'conditions' => 'id = :id: and staff_id = :staff_id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $task_id, 'staff_id' => $staff_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ]);
        if (empty($inventory_check_staff_info)) {
            throw new ValidationException(static::$t->_('inventory_staff_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $inventory_check_staff_info;
    }

    /**
     * 资产盘点-资产盘点清单-任务-操作前置验证
     * @param int $task_id 任务ID
     * @param int $staff_id 员工ID
     * @return mixed
     * @throws ValidationException
     */
    private function validateTask(int $task_id, int $staff_id)
    {
        $inventory_check_staff_info =  $this->getTaskInfo($task_id, $staff_id);

        //仅待盘点、盘点中任务才可操作
        if (!in_array($inventory_check_staff_info->status, [InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING])) {
            throw new ValidationException(static::$t->_('inventory_staff_asset_cannot_operate'), ErrCode::$VALIDATE_ERROR);
        }
        return $inventory_check_staff_info;
    }

    /**
     * 资产盘点-资产盘点清单-资产-操作前置验证
     * @param int $asset_id 资产ID
     * @param int $task_id 任务ID
     * @return mixed
     * @throws ValidationException
     */
    private function validateAsset(int $asset_id, int $task_id)
    {
        $inventory_check_staff_asset_info = MaterialInventoryCheckStaffAssetsModel::findFirst([
            'conditions' => 'id = :id: and staff_rel_id = :staff_rel_id: and is_deleted = :is_deleted:',
            'bind' => ['id' => $asset_id, 'staff_rel_id' => $task_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        if (empty($inventory_check_staff_asset_info)) {
            throw new ValidationException(static::$t->_('inventory_staff_asset_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $inventory_check_staff_asset_info;
    }

    /**
     * 单个资产清单操作db
     * @param array $params 请求参数组
     * @param int $asset_status 盘点结果枚举：2账实相符（盘到）、3账实不符（信息有误）、4盘亏（未盘到）
     * @return bool
     * @throws ValidationException
     * @throws BusinessException
     */
    private function oneAssetOperateDb(array $params, int $asset_status)
    {
        $task_id = $params['task_id'];
        $staff_id = $params['staff_id'];
        $asset_id = $params['asset_id'];

        $inventory_check_staff_info = $this->validateTask($task_id, $staff_id);
        //资产信息是否存在
        $inventory_check_staff_asset_info = $this->validateAsset($asset_id, $task_id);
        //资产类才可做的操作
        if ($inventory_check_staff_asset_info->category_type != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET) {
            throw new ValidationException(static::$t->_('inventory_staff_asset_type_cannot_operate'), ErrCode::$VALIDATE_ERROR);
        }
        //盘到则需要判读 盘点单- 点击盘到时必须上传图片 - 开启，附件必须有;扫码盘到不走以下拦截逻辑
        $is_scan = $params['is_scan'] ?? '';
        if ($is_scan != InventoryCheckEnums::IS_SCAN_GET && $asset_status == InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH && empty($attachment)) {
            $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_staff_info->inventory_check_id);
            if ($inventory_check_info->is_must_upload == InventoryCheckEnums::INVENTORY_OPEN_YES) {
                $validate = self::$validate_task_asset_attachments;
                $validate['attachments'] = 'Required|Arr|ArrLenGeLe:1,10|>>>:' . static::$t->_('inventory_is_must_upload_must');
                Validation::validate($params, $validate);
            }
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now = date('Y-m-d H:i:s');
            //更新资产盘点结果
            $inventory_check_staff_asset_info->status = $asset_status;//盘点结果
            $inventory_check_staff_asset_info->reason = $params['reason'] ?? '';//盘点备注
            $inventory_check_staff_asset_info->revise_asset_code = $params['revise_asset_code'] ?? '';//修改资产码
            $inventory_check_staff_asset_info->revise_sn_code = $params['revise_sn_code'] ?? '';//修正后的sn编码
            $inventory_check_staff_asset_info->revise_sys_store_id = $params['revise_sys_store_id'] ?? '';//修正使用地址-网点ID
            $inventory_check_staff_asset_info->revise_sys_store_name = $params['revise_sys_store_name'] ?? '';//修正使用地址-网点名称
            $inventory_check_staff_asset_info->revise_staff_id = $params['revise_staff_id'] ?? 0;//修正使用人工号
            $inventory_check_staff_asset_info->revise_staff_name = $params['revise_staff_name'] ?? '';//修正使用人姓名
            $inventory_check_staff_asset_info->is_scan = $params['is_scan'] ?? InventoryCheckEnums::IS_SCAN_DEFAULT;//扫码埋点：默认值0非扫码，1:扫码盘到、2扫码新增
            $inventory_check_staff_asset_info->updated_at = $now;
            $bool = $inventory_check_staff_asset_info->save();
            if ($bool === false) {
                throw new BusinessException('盘点资产-修改资产盘点结果失败：' . json_encode($inventory_check_staff_info, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_asset_info), ErrCode::$BUSINESS_ERROR);
            }

            //删除原来的附件信息
            $inventory_check_staff_asset_info->getAttachments()->delete();

            //若有附件，则处理附件信息
            if (!empty($params['attachments'])) {
                $attachment_arr = [];
                foreach ($params['attachments'] as $attachment) {
                    $attachment_arr[] = [
                        'oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK,
                        'oss_bucket_key' => $inventory_check_staff_asset_info->id,
                        'bucket_name' => $attachment['bucket_name'],
                        'object_key' => $attachment['object_key'],
                        'file_name' => $attachment['file_name'],
                        'object_url' => $attachment['object_url'],
                        'created_at' => $now
                    ];
                }

                $material_attachment_model = new MaterialAttachmentModel();
                if (!$material_attachment_model->batch_insert($attachment_arr)) {
                    throw new BusinessException('盘点资产-修改资产盘点结果-添加附件信息失败 = ' . json_encode($attachment_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //更新未盘资产数量
            $inventory_check_staff_info->unpacked_asset_total = $this->getTaskAssetCount(['task_id' => $task_id, 'staff_id' => $staff_id, 'type' => InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING]);
            $inventory_check_staff_info->updated_at = $now;
            $bool = $inventory_check_staff_info->save();
            if ($bool === false) {
                throw new BusinessException('盘点资产-修改任务状态或未盘资产总数失败：' . json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 多个资产清单操作db
     * @param array $params 请求参数组
     * @param int $asset_status 盘点结果枚举：2账实相符（盘到）、4盘亏（未盘到）
     * @return bool
     * @throws ValidationException
     * @throws BusinessException
     */
    private function batchAssetOperateDb(array $params, int $asset_status)
    {
        $task_id = $params['task_id'];
        $staff_id = $params['staff_id'];
        $asset_ids = $params['asset_ids'];

        $inventory_check_staff_info = $this->validateTask($task_id, $staff_id);
        //资产信息是否存在
        $inventory_check_staff_asset = MaterialInventoryCheckStaffAssetsModel::find([
            'conditions' => 'id in ({ids:array}) and staff_rel_id = :staff_rel_id: and category_type = :category_type: and is_deleted = :is_deleted:',
            'bind' => ['ids' => $asset_ids, 'staff_rel_id' => $task_id, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ]);
        $inventory_check_staff_asset_list = $inventory_check_staff_asset->toArray();
        //资产类才可做的操作
        if (empty($inventory_check_staff_asset_list) || count($asset_ids) != count(array_column($inventory_check_staff_asset_list, 'id'))) {
            throw new ValidationException(static::$t->_('inventory_staff_asset_type_cannot_operate'), ErrCode::$VALIDATE_ERROR);
        }
        //批量盘到则需要判读 盘点单- 点击盘到时必须上传图片 - 开启，附件必须有
        if ($asset_status == InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH && empty($attachment)) {
            $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_staff_info->inventory_check_id);
            if ($inventory_check_info->is_must_upload == InventoryCheckEnums::INVENTORY_OPEN_YES) {
                $validate = self::$validate_task_asset_attachments;
                $validate['attachments'] = 'Required|Arr|ArrLenGeLe:1,10|>>>:' . static::$t->_('inventory_is_must_upload_must');
                Validation::validate($params, $validate);
            }
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $now = date('Y-m-d H:i:s');
            //更新资产盘点结果
            $staff_asset_ids = implode(',', $asset_ids);
            $bool = $db->updateAsDict(
                (new MaterialInventoryCheckStaffAssetsModel())->getSource(),
                [
                    'status' => $asset_status,//盘点结果
                    'reason' => $params['reason'] ?? '',//盘点备注
                    'updated_at' => $now
                ],
                ['conditions' => "id in ({$staff_asset_ids})"]
            );

            if ($bool === false) {
                throw new BusinessException('盘点资产-批量修改资产盘点结果失败' . json_encode($staff_asset_ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //删除原来的附件信息
            MaterialAttachmentModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key in ({oss_bucket_keys:array}) and deleted = :deleted:',
                'bind' => ['oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK, 'oss_bucket_keys' => $asset_ids, 'deleted' => GlobalEnums::IS_NO_DELETED]
            ])->delete();

            //更新未盘资产数量
            $inventory_check_staff_info->unpacked_asset_total = $this->getTaskAssetCount(['task_id' => $task_id, 'staff_id' => $staff_id, 'type' => InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING]);
            $inventory_check_staff_info->updated_at = $now;
            $bool = $inventory_check_staff_info->save();
            if ($bool === false) {
                throw new BusinessException('盘点资产-修改任务状态或未盘资产总数失败：' . json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
            }

            //若有附件，则处理附件信息; 每一个资产的附件是一样的
            if (!empty($params['attachments'])) {
                $attachment_arr = [];
                foreach ($asset_ids as $inventory_check_staff_asset_info_id) {
                    foreach ($params['attachments'] as $attachment) {
                        $attachment_arr[] = [
                            'oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK,
                            'oss_bucket_key' => $inventory_check_staff_asset_info_id,
                            'bucket_name' => $attachment['bucket_name'],
                            'object_key' => $attachment['object_key'],
                            'file_name' => $attachment['file_name'],
                            'object_url' => $attachment['object_url'],
                            'created_at' => $now
                        ];
                    }
                }

                $material_attachment_model = new MaterialAttachmentModel();
                if (!$material_attachment_model->batch_insert($attachment_arr)) {
                    throw new BusinessException('盘点资产-修改任务状态或未盘资产-添加附件信息失败 = ' . json_encode($attachment_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (BusinessException $e) {
            $db->rollback();
            throw $e;
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }
        return true;
    }

    /**
     * 资产盘点-资产盘点清单-未盘到(盘亏)
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetLose(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        try {
            self::$validate_task_asset_operate['reason'] = 'Required|StrLenGeLe:3,500';
            Validation::validate($params, self::$validate_task_asset_operate);
            $data = $this->oneAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-lose-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-批量未盘到(盘亏)
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetBatchLose(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        try {
            self::$validate_task_asset_batch_operate['reason'] = 'Required|StrLenGeLe:3,500';
            Validation::validate($params, self::$validate_task_asset_batch_operate);
            $data = $this->batchAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOSE);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-batchLose-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-盘到(账实相符)
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetMatch(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        try {
            Validation::validate($params, self::$validate_task_asset_operate);
            $data = $this->oneAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-match-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-批量盘到(账实相符)
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetBatchMatch(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        try {
            Validation::validate($params, self::$validate_task_asset_batch_operate);
            $data = $this->batchAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-batchMatch-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-信息有误(账实不符)
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetNotMatch(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        try {
            $data = $this->oneAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_NOT_MATCH);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-NotMatch-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-查看信息有误
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetViewNotMatch(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, self::$validate_task_asset_operate);
            $one_asset_info = $this->validateAsset($params['asset_id'], $params['task_id']);
            $data = [
                'id' => $one_asset_info->id,
                'name_zh' => $one_asset_info->name_zh,
                'name_en' => $one_asset_info->name_en,
                'name_local' => $one_asset_info->name_local,
                'asset_code' => $one_asset_info->asset_code,
                'old_asset_code' => $one_asset_info->old_asset_code,
                'sn_code' => $one_asset_info->sn_code,
                'revise_asset_code' => $one_asset_info->revise_asset_code,
                'revise_sn_code' => $one_asset_info->revise_sn_code,
                'reason' => $one_asset_info->reason,
                'revise_sys_store_id' => $one_asset_info->revise_sys_store_id,
                'revise_sys_store_name' => $one_asset_info->revise_sys_store_name,
                'revise_staff_id' => $one_asset_info->revise_staff_id,
                'revise_staff_name' => $one_asset_info->revise_staff_name
            ];
            $data['attachments'] = $one_asset_info->getAttachments()->toArray();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-task-asset-ViewNotMatch-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-修改数量
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetUpdateNum(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $task_id = $params['task_id'];
            $staff_id = $params['staff_id'];
            $barcode = $params['bar_code'];
            $now = date('Y-m-d H:i:s');
            //任务合法性
            $inventory_check_staff_info = $this->validateTask($task_id, $staff_id);

            //操作来源1修改数量、2点击确认,则需要判读 盘点单- 点击盘到时必须上传图片 - 开启，附件必须有
            if ($params['type'] == 2 && empty($params['attachments'])) {
               $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_staff_info->inventory_check_id);
               if ($inventory_check_info->is_must_upload == InventoryCheckEnums::INVENTORY_OPEN_YES) {
                   throw new ValidationException(static::$t->_('inventory_is_must_upload_must'), ErrCode::$VALIDATE_ERROR);
               }
            }

            //获取盘点的资产清单
            $inventory_asset_obj = MaterialInventoryCheckStaffAssetsModel::find([
                'conditions' => 'inventory_check_id = :inventory_check_id: and staff_rel_id = :task_id: and bar_code = :bar_code: and category_type = :category_type: and is_deleted = :is_deleted:',
                'bind' => ['inventory_check_id' => $inventory_check_staff_info->inventory_check_id, 'task_id' => $task_id, 'bar_code' => $barcode, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            $inventory_asset_data = $inventory_asset_obj->toArray();
            if (empty($inventory_asset_data)) {
                throw new ValidationException(static::$t->_('inventory_staff_asset_wms_type_cannot_operate'), ErrCode::$VALIDATE_ERROR);
            }

            $one_profit_asset = [];//低值盘盈资产信息
            $all_asset_ids = [];//所有的资产ID组
            $other_asset_ids = [];//1待盘点、6低值盘到、7低值盘亏资产ID组
            foreach ($inventory_asset_obj as $asset) {
                if ($asset->status == InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT) {
                    //获取8低值盘盈的资产信息
                    $one_profit_asset = $asset;
                } else {
                    //汇总（1待盘点、6低值盘到、7低值盘亏）的资产清单ID组
                    $other_asset_ids[] = $asset->id;
                }
                $all_asset_ids[] = $asset->id;
            }

            //汇总系统数量（1待盘点、6低值盘到、7低值盘亏数量和）
            $asset_num = count($other_asset_ids);
            //输入的数量
            $real_asset_num = $params['real_asset_num'];
            $reason = $params['reason'] ?? '';//盘点备注

            $last_asset_num = 0;//剩余数量
            $low_lose_asset_ids = [];//低值盘亏资产ID组
            if ($asset_num <= $real_asset_num) {
                //系统数量<=输入的数量时，则查找到的资产清单 = 6低值盘到
                $low_get_asset_ids = $other_asset_ids;

                //系统数量<输入的数量时，剩余数量插入资产清单（耗材类，8低值盘盈，剩余数量）
                $last_asset_num = $real_asset_num - $asset_num;
            } else {
                //系统数量 > 输入的数量时, 截取输入数量的资产清单 = 6低值盘到
                $low_get_asset_ids = array_slice($other_asset_ids, 0, $real_asset_num);
                //剩下的资产清单 = 7低值盘亏
                $low_lose_asset_ids = array_slice($other_asset_ids, $real_asset_num);
            }

            //原来的低值盘盈信息需要删掉
            if (!empty($one_profit_asset)) {
                $one_profit_asset->delete();
            }

            //删除原来的附件信息
            MaterialAttachmentModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key in ({oss_bucket_keys:array}) and deleted = :deleted:',
                'bind' => ['oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK, 'oss_bucket_keys' => $all_asset_ids, 'deleted' => GlobalEnums::IS_NO_DELETED]
            ])->delete();

            //6低值盘到
            if (!empty($low_get_asset_ids)) {
                $low_get_asset_ids_str = implode(',', $low_get_asset_ids);
                $bool = $db->updateAsDict(
                    (new MaterialInventoryCheckStaffAssetsModel())->getSource(),
                    [
                        'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_GET,
                        'reason' => $reason,
                        'updated_at' => $now
                    ],
                    ['conditions' => "id in ({$low_get_asset_ids_str})"]
                );

                if ($bool === false) {
                    throw new BusinessException('资产盘点-资产盘点清单-修改数量-修改资产状态为低值盘到失败 ' . json_encode($low_get_asset_ids_str, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //7低值盘亏
            if (!empty($low_lose_asset_ids)) {
                $low_lose_asset_ids_str = implode(',', $low_lose_asset_ids);
                $bool = $db->updateAsDict(
                    (new MaterialInventoryCheckStaffAssetsModel())->getSource(),
                    [
                        'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_LOSE,
                        'reason' => $reason,
                        'updated_at' => $now
                    ],
                    ['conditions' => "id in ({$low_lose_asset_ids_str})"]
                );

                if ($bool === false) {
                    throw new BusinessException('资产盘点-资产盘点清单-修改数量-修改资产状态为低值盘亏失败 ' . json_encode($low_lose_asset_ids_str, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //8低值盘盈
            $low_profit_asset_ids = [];
            if ($last_asset_num > 0) {
                $asset_data = [
                    'inventory_check_id' => $inventory_check_staff_info->inventory_check_id,
                    'staff_rel_id' => $inventory_check_staff_info->id,
                    'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS,
                    'bar_code' => $barcode,
                    'name_zh' => $params['name_zh'],
                    'name_en' => $params['name_en'],
                    'name_local' => $params['name_local'],
                    'reason' => $reason,
                    'asset_num' => $last_asset_num,
                    'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_LOW_PROFIT,
                    'is_new' => InventoryCheckEnums::IS_NEW_YES,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
                $inventory_check_staff_asset_model = new MaterialInventoryCheckStaffAssetsModel();
                $bool = $inventory_check_staff_asset_model->i_create($asset_data);
                if ($bool === false) {
                    throw new BusinessException('资产盘点-资产盘点清单-修改数量-修改资产状态为低值盘盈失败 ' . json_encode($asset_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_asset_model), ErrCode::$BUSINESS_ERROR);
                }
                $low_profit_asset_ids[] = $inventory_check_staff_asset_model->id;
            }

            //若有附件，则处理附件信息
            $low_asset_ids = array_merge($low_get_asset_ids, $low_lose_asset_ids, $low_profit_asset_ids);
            if (!empty($params['attachments']) && $low_asset_ids) {
                $attachment_arr = [];
                foreach ($params['attachments'] as $attachment) {
                    foreach ($low_asset_ids as $asset_id) {
                        $attachment_arr[] = [
                            'oss_bucket_type' => InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK,
                            'oss_bucket_key' => $asset_id,
                            'bucket_name' => $attachment['bucket_name'],
                            'object_key' => $attachment['object_key'],
                            'file_name' => $attachment['file_name'],
                            'object_url' => $attachment['object_url'],
                            'created_at' => $now
                        ];
                    }
                }

                $material_attachment_model = new MaterialAttachmentModel();
                if (!$material_attachment_model->batch_insert($attachment_arr)) {
                    throw new BusinessException('资产盘点-资产盘点清单-修改数量-添加附件信息失败 = ' . json_encode($attachment_arr, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            //更新未盘资产数量
            $inventory_check_staff_info->unpacked_asset_total = $this->getTaskAssetCount(['task_id' => $task_id, 'staff_id' => $staff_id, 'type' => InventoryCheckEnums::INVENTORY_TASK_LIST_TYPE_PENDING]);
            $inventory_check_staff_info->updated_at = $now;
            $bool = $inventory_check_staff_info->save();
            if ($bool === false) {
                throw new BusinessException('盘点资产-修改数量操作-修改任务状态失败：' . json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-UpdateNum-failed:' .$real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-搜索资产
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetBarcodeSearch(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, ['name' => 'StrLenGeLe:0,100']);
            $params['category_type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET;
            $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
            $params['lang'] = static::$language;
            $data = MaterialSauRepository::getInstance()->searchBarcode($params);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-task-asset-taskAssetBarcodeSearch-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-添加资产（盘盈）
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetAdd(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = false;
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $inventory_check_staff_info = $this->validateTask($params['task_id'], $params['staff_id']);
            if (!empty($params['asset_code'])) {
                $this->validateAssetIsExists($inventory_check_staff_info->id, $params['asset_code']);
            }
            if (!empty($params['sn_code'])) {
                $this->validateAssetIsExists($inventory_check_staff_info->id, $params['sn_code']);
            }

            //添加资产
            $now = date('Y-m-d H:i:s');
            $asset_data = [
                'inventory_check_id' => $inventory_check_staff_info->inventory_check_id,
                'staff_id' => $params['asset_staff_id'],
                'staff_name' => $params['asset_staff_name'],
                'staff_rel_id' => $inventory_check_staff_info->id,
                'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET,
                'bar_code' => $params['bar_code'],
                'name_zh' => $params['name_zh'],
                'name_en' => $params['name_en'],
                'name_local' => $params['name_local'],
                'asset_code' => $params['asset_code'] ?? '',
                'sn_code' => $params['sn_code'] ?? '',
                'reason' => $params['reason'] ?? '',
                'asset_num' => 1,
                'status' => InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT,
                'is_scan' => $params['is_scan'] ?? InventoryCheckEnums::IS_SCAN_DEFAULT,
                'is_new' => InventoryCheckEnums::IS_NEW_YES,
                'created_at' => $now,
                'updated_at' => $now
            ];
            $inventory_check_staff_asset_model = new MaterialInventoryCheckStaffAssetsModel();
            $bool = $inventory_check_staff_asset_model->i_create($asset_data);
            if ($bool === false) {
                throw new BusinessException('盘点资产-添加资产失败：' . json_encode($asset_data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($inventory_check_staff_asset_model), ErrCode::$BUSINESS_ERROR);
            }

            //若有附件，则处理附件信息
            if (!empty($params['attachments'])) {
                $am = new MaterialAttachmentModel();
                $attachArr = [];
                foreach ($params['attachments'] as $attachment) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = InventoryCheckEnums::OSS_BUCKET_TYPE_INVENTORY_CHECK;
                    $tmp['oss_bucket_key'] = $inventory_check_staff_asset_model->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $attachment['bucket_name'];
                    $tmp['object_key'] = $attachment['object_key'];
                    $tmp['file_name'] = $attachment['file_name'];
                    $tmp['object_url'] = $attachment['object_url'];
                    $tmp['created_at'] = $now;
                    $attachArr[] = $tmp;
                }
                if (!$am->batch_insert($attachArr)) {
                    throw new BusinessException('盘点资产-添加资产-添加附件信息失败 = ' . json_encode($params['attachments'], JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $data = true;
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-add-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 检测某个任务下某个资产是否已经存在
     * @param int $task_id 任务ID
     * @param string $code 编码
     * @return bool
     * @throws ValidationException
     */
    private function validateAssetIsExists(int $task_id, string $code)
    {
        $one_asset_info = MaterialInventoryCheckStaffAssetsModel::findFirst([
            'conditions' => 'staff_rel_id = :task_id: and category_type = :category_type: and is_deleted = :is_deleted: and (asset_code = :code: or sn_code = :code: or old_asset_code = :code:)',
            'bind' => ['task_id' => $task_id, 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'code' => $code]
        ]);
        if ($one_asset_info) {
            throw new ValidationException(static::$t->_('inventory_staff_asset_code_exist'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 资产盘点-资产盘点清单-删除资产
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetDel(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            Validation::validate($params, self::$validate_task_asset_operate);
            $this->validateTask($params['task_id'], $params['staff_id']);
            $inventory_check_staff_asset_info = $this->validateAsset($params['asset_id'], $params['task_id']);
            if ($inventory_check_staff_asset_info->status != InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_PROFIT) {
                throw new ValidationException(static::$t->_('inventory_staff_asset_cannot_del'), ErrCode::$VALIDATE_ERROR);
            }

            //删除资产
            $bool = $inventory_check_staff_asset_info->delete();
            if ($bool === false) {
                throw new BusinessException('盘点资产-添加资产-删除失败；可能原因是：' . get_data_object_error_msg($inventory_check_staff_asset_info), ErrCode::$BUSINESS_ERROR);
            }

            //删除原来的附件信息
            $bool = $inventory_check_staff_asset_info->getAttachments()->delete();
            if ($bool === false) {
                throw new BusinessException('盘点资产-添加资产-删除资产关联附件失败', ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-taskAssetDel-failed:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-扫码盘点
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetScanCode(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $validation = [
                'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
                'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
                'asset_code' => 'Required|StrLenGeLe:1,50|>>>:asset_code error',
            ];
            Validation::validate($params, $validation);
            $this->validateTask($params['task_id'], $params['staff_id']);

            $staff_asset_obj = MaterialInventoryCheckStaffAssetsModel::find([
                'conditions' => 'staff_rel_id = :task_id: and category_type = :category_type: and is_deleted = :is_deleted: and (asset_code = :code: or sn_code = :code: or old_asset_code = :code:)',
                'bind' => ['task_id' => $params['task_id'], 'category_type' => MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'code' => $params['asset_code']]
            ]);
            $staff_asset_data = $staff_asset_obj->toArray();
            if ($staff_asset_data) {
                //在
                foreach ($staff_asset_obj as $asset) {
                    //看是否有待盘点的
                    if ($asset->status == InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_WAIT) {
                        $params['asset_id'] = $asset->id;
                        break;
                    }
                }
                //存在待盘点的会放入待盘点的资产ID，以供盘点
                if (!empty($params['asset_id'])) {
                    $params['is_scan'] = InventoryCheckEnums::IS_SCAN_GET;
                    $result = $this->oneAssetOperateDb($params, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ASSET_STATUS_MATCH);
                    if ($result === true) {
                        $data = ['type' => 1];
                    }
                } else {
                    //不存在待盘点，则意味着该编码全部盘完了
                    $message = static::$t->_('inventory_staff_asset_over');
                    $data = ['type' => 3];
                }
            } else {
                //不在，跳转到新增页码
                $data = ['type' => 2];
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-taskAssetScanCode-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-确认无资产
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetDone($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $validation = [
                'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
                'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
            ];
            Validation::validate($params, $validation);
            $inventory_check_staff_info = $this->validateTask($params['task_id'], $params['staff_id']);
            //名下无资产或名下有资产但提前盘点完毕了，可以点击确认无资产
            if ($inventory_check_staff_info->pack_asset_total > 0 && $inventory_check_staff_info->unpacked_asset_total > 0) {
                throw new ValidationException(static::$t->_('inventory_staff_asset_done_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            //盘点单状态非进行中
            $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_staff_info->inventory_check_id);
            if ($inventory_check_info->status != InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING) {
                throw new ValidationException(static::$t->_('inventory_check_status_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $inventory_check_staff_info->status = InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE;
            $inventory_check_staff_info->updated_at = date('Y-m-d H:i:s');
            $bool = $inventory_check_staff_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点清单-确认无资产-修改状态=已完成失败：'. json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是:' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-taskAssetDone-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-更新盘点
     * @param array $params 请求参数组
     * @return array
     */
    public function taskAssetUpdate($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $validation = [
                'task_id' => 'Required|IntGe:1|>>>:task_id error', //任务ID
                'staff_id' => 'Required|IntGe:1|>>>:staff_id error', //员工工号
            ];
            Validation::validate($params, $validation);
            $inventory_check_staff_info = $this->getTaskInfo($params['task_id'], $params['staff_id']);
            //盘点任务状态非已完成
            if ($inventory_check_staff_info->status != InventoryCheckEnums::INVENTORY_CHECK_STAFF_DONE) {
                throw new ValidationException(static::$t->_('inventory_staff_asset_update_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $inventory_check_info = $this->getInventoryCheckInfoById($inventory_check_staff_info->inventory_check_id);
            //盘点单状态非进行中
            if ($inventory_check_info->status != InventoryCheckEnums::INVENTORY_CHECK_STATUS_ING) {
                throw new ValidationException(static::$t->_('inventory_check_status_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $inventory_check_staff_info->status = InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING;
            $inventory_check_staff_info->updated_at = date('Y-m-d H:i:s');
            $bool = $inventory_check_staff_info->save();
            if ($bool === false) {
                throw new BusinessException('资产盘点清单-更新盘点-修改状态=进行中失败：'. json_encode($inventory_check_staff_info->toArray(), JSON_UNESCAPED_UNICODE) . ';可能的原因是:' . get_data_object_error_msg($inventory_check_staff_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-inventory_check-task-asset-taskAssetUpdate-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 资产盘点-资产盘点清单-限制打卡
     * @param array $params 请求参数组
     * @return array
     */
    public function taskCheckPunchOut(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            Validation::validate($params, ['staff_id' => 'Required|IntGe:1|>>>:staff_id error']);

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('COUNT(task.id) as total');
            $builder->from(['task' => MaterialInventoryCheckStaffModel::class]);
            $builder->leftjoin(MaterialInventoryCheckModel::class, 'task.inventory_check_id = main.id', 'main');
            $builder->where('task.staff_id = :staff_id: and task.forbid_punch_out = :forbid_punch_out: and task.unpacked_asset_total > 0 and task.is_deleted = :is_deleted: and main.is_deleted = :is_deleted:', ['staff_id' => $params['staff_id'], 'forbid_punch_out' => InventoryCheckEnums::INVENTORY_CHECK_WHETHER_YES, 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->inWhere('task.status', [InventoryCheckEnums::INVENTORY_CHECK_STAFF_STATUS_WAIT, InventoryCheckEnums::INVENTORY_CHECK_STAFF_ING]);
            $builder->andWhere('DATE_FORMAT(main.end_at, "%Y-%m-%d") = :end_at:', ['end_at' => date('Y-m-d')]);
            $total_count = (int) $builder->getQuery()->getSingleResult()->total;
            if ($total_count > 0) {
                $data['business_type'] = 'un_asset_inventory';
                $data['asset_inventory_detail'] = [
                    'dialog_msg' => '',
                    'dialog_btn_msg' => '',
                    'dialog_status' => 1,//0不弹，1弹
                    'dialog_must_status' => 1,// 0 能跳过 1不能跳过
                    'dialog_jump_url' => '',
                    'is_ces_tra' => 0
                ];
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('get-inventory_check-task-asset-taskCheckPunchOut-failed:' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }
}
