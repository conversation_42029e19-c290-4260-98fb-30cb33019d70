<?php

namespace App\Modules\Material\Services;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Models\backyard\StoreMaterialStatisticsModel;
use App\Models\oa\MaterialPackageAllotModel;
use App\Models\oa\MaterialPackageAllotSkuModel;
use App\Models\oa\MaterialPackageStockArchiveModel;
use App\Models\oa\MaterialPackageStockModel;
use App\Models\oa\MaterialWmsOutStorageBoxModel;
use App\Models\oa\MaterialWmsOutStorageBoxProductModel;
use App\Models\oa\MaterialWmsOutStorageModel;
use App\Models\oa\MaterialWmsOutStorageProductModel;
use App\Models\oa\MaterialWmsPlanBarcodeModel;
use App\Models\oa\MaterialWmsPlanStoreModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\StoreModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\StoreService;
use App\Library\Enums\DownloadCenterEnum;
use App\Modules\Material\Models\MaterialWmsPlanModel;
use App\Modules\Material\Models\MaterialWmsPlantaskModel;
use App\Modules\Material\Models\MaterialWmsPlantaskInfoModel;
use App\Modules\Material\Models\MaterialPackageSkuModel;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\AccidentReport\Models\HrStaffInfo;
use App\Modules\User\Services\StaffService;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use App\Library\Enums\InventoryCheckEnums;
use App\Modules\User\Models\StaffInfoModel;
use App\Modules\Material\Models\MaterialPackageCategoryModel;
use App\Library\ApiClient;
use App\Library\Enums\MaterialWmsEnums;
use App\Models\oa\MaterialPackageOrderModel;
use App\Models\oa\MaterialPackageOrderSkuModel;
use App\Models\oa\MaterialPackageReturnOrderModel;
use App\Models\oa\MaterialPackageReturnOrderSkuModel;


class WmsPlanService extends BaseService
{
    private static $instance;


    public static $not_must_params = [
        'staff_info_id',
        'plan_noun',
        'store_name',
        'department_name',
        'package_sku_id',
        'category',
        'plan_status',
        'id',
    ];
    // 库存盘点-盘点管理-盘点单列表-搜索条件
    public static $validate_list_search = [
        'staff_info_id' => 'StrLenGeLe:0,8|>>>:staff_info_id error',
        'plan_noun' => 'IntIn:1,2|>>>:plan_noun error',
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];


    public static $validate_plan_info_list_search = [
        'plan_id' => 'Required|IntGe:1|>>>:plan_id error', //盘点单ID
        'staff_info_id' => 'StrLenGeLe:0,8|>>>:staff_info_id error',
        'store_name' => 'Str|>>>:store_name error',
        'department_name' => 'Str|>>>:department_name error',
        'package_sku_id' => 'Arr|>>>:package_sku_id error',
        'category' => 'Arr|>>>:category error',
        'plan_status' => 'IntGe:1',//任务状态
        'pageNum' => 'IntGe:1|>>>:page error', //当前页码
        'pageSize' => 'IntGe:1|>>>:page_size error', //每页条数
    ];


    // 库存盘点-盘点管理-盘点单-创建
    public static $validate_plan_check_create = [
        'plan_name' => 'Required|StrLenLe:200|>>>:plan_name error',//盘点单名称
        'plan_noun' => 'Required|IntIn:1,2|>>>:plan_noun error',//盘点频次
        'plan_nub' => 'Required|IntGeLe:1,24|>>>:serial_number error', //次数
        'sta_time' => 'Required|DateTime|>>>:sta_time date error',//盘点开始时间
        'end_time' => 'Required|DateTime|>>>:end end_time error',//盘点结束时间
        'plan_scope' => 'Required|IntIn:1,2,3|>>>:plan_scope error',//盘点范围
        'plan_manager_id' => 'StrLenGeLe:0,8|>>>:plan_manager_id error',//盘点负责人
        'is_photo' => 'Required|IntIn:0,1|>>>:is_photo error',//必须拍照  1 盘点事必须拍照 0 不用拍照
        'is_photo_camera' => 'Required|IntIn:0,1|>>>:is_photo_camera error',//1 照片必须是使用相机拍照 0 不限制
        'is_relay' => 'Required|IntIn:0,1|>>>:is_relay error',//允许转派 1 允许转派 0 不允许
        'plan_remark' => 'StrLenLe:500|>>>:plan_remark error',//备注
        'stores' => 'IfIntEq:plan_scope,' . InventoryCheckEnums::PLAN_SCOPE_SITE . '|Required|Arr|ArrLenGeLe:1,500',//指定网点-盘点网点列表
        'stores[*]' => 'IfIntEq:plan_scope,' . InventoryCheckEnums::PLAN_SCOPE_SITE . '|Required|Obj',
        'stores[*].sys_store_id' => 'Required|StrLenGeLe:1,10',//网点编码
        'stores[*].sys_store_name' => 'Required|StrLenGeLe:1,50',//网点名称
        'stores[*].manager_id' => 'Required|IntGt:0',//网点负责人工号
        'stores[*].manager_name' => 'Required|StrLenGeLe:1,50',//盘点负责人姓名
        'barcode' =>'Required|Arr|ArrLenGe:1',
        'barcode[*]' => 'Required|StrLenGeLe:1,20',
    ];

    // 库存盘点-盘点管理-盘点单-查看、删除
    public static $validate_plan_check_id = [
        'id' => 'Required|IntGe:1|>>>:id error', //盘点单ID
    ];


    // 库存盘点-盘点管理-盘点单-查看、删除
    public static $validate_plan_staff_info = [
        'staff_info_id' => 'Required|StrLenGeLe:5,8|>>>:staff_info_id error',
    ];


    /**
     * 构造函数
     * WmsPlanService constructor.
     */
    private function __construct()
    {
    }

    /**
     * 类实例
     * @return WmsPlanService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取列表初始化数据
     * @return array
     * @author: peak pan
     */
    public function getPlanDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = StaffService::getInstance()->departmentList();
        $sourceType = $this->request->get('source_type');
        if ($sourceType == BudgetService::ORDER_TYPE_1) {
            $departmentId = $this->request->get('department_id');
            foreach ($data as $datum) {
                if ($this->isHasNodeDepartment([$datum], $departmentId)) {
                    $data = [$datum];
                    break;
                }
            }
        }
        $list['departmentList'] = $data ?? [];

        $plan_noun_enums_arr = InventoryCheckEnums::$plan_noun_enums;
        foreach ($plan_noun_enums_arr as &$itme) {
            $itme['name'] = static::$t[$itme['name']];
        }

        $plan_scope_arr = InventoryCheckEnums::$plan_scope_enums;
        foreach ($plan_scope_arr as &$itme_) {
            $itme_['name'] = static::$t[$itme_['name']];
        }
        $list['plan_noun'] = $plan_noun_enums_arr;
        $list['plan_scope'] = $plan_scope_arr;
        $list['store_type_map'] = json_decode(EnvModel::getEnvByCode('store_type_map', ''), true);

        $list['status_job_name'] = [
            ['id' => InventoryCheckEnums::STAFF_STATE_IN, 'name' => static::$t[InventoryCheckEnums::$staff_state[InventoryCheckEnums::STAFF_STATE_IN]]],
            ['id' => InventoryCheckEnums::STAFF_STATE_LEAVE, 'name' => static::$t[InventoryCheckEnums::$staff_state[InventoryCheckEnums::STAFF_STATE_LEAVE]]],
            ['id' => InventoryCheckEnums::STAFF_STATE_STOP, 'name' => static::$t[InventoryCheckEnums::$staff_state[InventoryCheckEnums::STAFF_STATE_STOP]]],
        ];
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $list
        ];
    }


    /**
     * 创建库存盘点计划任务
     * @return array
     * @author: peak pan
     */
    public function planAdd(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            //检测时间
            $start_at = strtotime($data['sta_time']);
            if ($start_at < time()) {
                throw new ValidationException(static::$t->_('plan_check_start_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($start_at > strtotime($data['end_time'])) {
                throw new ValidationException(static::$t->_('plan_check_end_error'), ErrCode::$VALIDATE_ERROR);
            }

            //盘点范围-按照网点类型
            if ($data['plan_scope'] == InventoryCheckEnums::PLAN_STORE_TYPE && empty($data['store_type'])) {
                throw new ValidationException(static::$t->_('plan_manager_store_type_not_null'), ErrCode::$VALIDATE_ERROR);
            }

            //盘点范围-指定部门
            if ($data['plan_scope'] == InventoryCheckEnums::PLAN_SCOPE_DEPARTMENT) {
                if (empty($data['plan_manager_id'])) {
                    throw new ValidationException(static::$t->_('plan_manager_id_not_null'), ErrCode::$VALIDATE_ERROR);
                }
                if (empty($data['department_ids'])) {
                    throw new ValidationException(static::$t->_('plan_department_ids_not_null'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //盘点范围-指定网点
            if ($data['plan_scope'] == InventoryCheckEnums::PLAN_SCOPE_SITE && count($data['stores']) != count(array_unique(array_column($data['stores'], 'sys_store_id')))) {
                throw new ValidationException(static::$t->_('plan_store_ids_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            $planTime = [];
            if ($data['plan_noun'] == 1) {
                //单次
                $planTime = [['sta_time' => $data['sta_time'], 'end_time' => $data['end_time']]];
            } else {
                if (empty($data['sta_month']) || empty($data['end_month'])) {
                    throw new ValidationException(static::$t->_('plan_check_sta_month_or_end_month'), ErrCode::$VALIDATE_ERROR);
                }

                if($data['sta_month'] > $data['end_month']){
                    throw new ValidationException(static::$t->_('plan_check_end_error'), ErrCode::$VALIDATE_ERROR);
                }
                //多次
                $sta_time_date = date('Y-m', strtotime($data['sta_month'] . '-01 00:00:00')) . date('-d H:i:s', strtotime($data['sta_time']));

                $end_time_date = $data['end_month'] . '-28 23:59:59';


                $plan_noun_end_time = strtotime('+' . ($data['plan_nub'] - 1) . ' month', strtotime($sta_time_date));

                if ($plan_noun_end_time > strtotime($end_time_date)) {
                    throw new ValidationException(static::$t->_('plan_check_sta_time_or_end_time'), ErrCode::$VALIDATE_ERROR);
                }

                for ($i = 0; $i < $data['plan_nub']; $i++) {
                    $sta_time = strtotime('+' . $i . ' month', strtotime($sta_time_date));
                    //设置的开始时间小于当前时间 跳过本次
                    if ($sta_time < time()) {
                        continue;
                    }
                    $end_time = strtotime(date('Y-m', $sta_time) . date('-d H:i:s', strtotime($data['end_time'])));
                    //结束时间大于语句次数 直接提示失败
                    if ($end_time > strtotime($end_time_date)) {
                        return [
                            'code' => ErrCode::$SYSTEM_ERROR,
                            'message' => static::$t->_('plan_check_end_error'),
                            'data' => [],
                        ];
                    }
                    if($sta_time>=$end_time){
                        return [
                            'code' => ErrCode::$SYSTEM_ERROR,
                            'message' => static::$t->_('plan_check_end_error'),
                            'data' => [],
                        ];

                    }
                    $datetime['sta_time'] = date('Y-m-d H:i:s', $sta_time);
                    $datetime['end_time'] = date('Y-m-d H:i:s', $end_time);

                    $planTime[] = $datetime;
                }
            }
            //盘点单盘点范围关系表组信息
            //创建盘点单
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            //盘点任务时间
            $serialNo = $this->serialNo();
            $now = date('Y-m-d H:i:s');
            foreach ($planTime as $item_time) {
                $wmsPlan = new  MaterialWmsPlanModel();
                $plan = [
                    'plan_code' => $serialNo['data']['serial_number'],
                    'plan_name' => $data['plan_name'],
                    'plan_noun' => $data['plan_noun'],
                    'plan_nub' => $data['plan_nub'] ?? 1,
                    'sta_month' => $data['sta_month'] ? $data['sta_month'] . '-01 00:00:00' : date('Y-m-d'),
                    'end_month' => $data['end_month'] ? $data['end_month'] . '-28 23:59:59' : date('Y-m-d'),
                    'sta_time' => $item_time['sta_time'],
                    'end_time' => $item_time['end_time'],
                    'plan_scope' => $data['plan_scope'],
                    'store_type' => !empty($data['store_type']) ? json_encode($data['store_type']) : '',
                    'store_ids' => (!empty($data['store_ids']) && $data['plan_scope'] == InventoryCheckEnums::PLAN_STORE_TYPE) ? json_encode($data['store_ids']) : '',
                    'department_ids' => $data['department_ids'],
                    'plan_manager_id' => !empty($data['plan_manager_id']) ? $data['plan_manager_id'] : 0,//盘点负责人
                    'is_photo' => $data['is_photo'],
                    'is_photo_camera' => $data['is_photo_camera'],
                    'is_relay' => $data['is_relay'],
                    'plan_remark' => $data['plan_remark'],
                    'status' => InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED,
                    'is_deleted' => InventoryCheckEnums::DEFAULT_VALUES,
                    'store_nub' => InventoryCheckEnums::DEFAULT_VALUES,
                    'reality_store_nub' => InventoryCheckEnums::DEFAULT_VALUES,

                    'staff_info_id' => $user['id'],
                    'staff_info_name' => $user['name'],
                    'up_staff_info_id' => InventoryCheckEnums::DEFAULT_VALUES,
                    'is_task' => InventoryCheckEnums::DEFAULT_VALUES,
                    'created_at' => $now,
                    'updated_at' => $now
                ];
                $bool = $wmsPlan->i_create($plan);
                if ($bool === false) {
                    $db->rollback();
                    throw new BusinessException('盘点计划添加失败' . json_encode(['plan_data' => $plan], JSON_UNESCAPED_UNICODE) . '; 可能的原因是：' . get_data_object_error_msg($wmsPlan), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
                }

                //库存盘点计划表-盘点网点
                if ($data['plan_scope'] == InventoryCheckEnums::PLAN_SCOPE_SITE && !empty($data['stores'])) {
                    foreach ($data['stores'] as &$item) {
                        $item['plan_id'] = $wmsPlan->id;
                        $item['created_at'] = $now;
                        $item['updated_at'] = $now;
                    }
                    $material_wms_plan_store = new MaterialWmsPlanStoreModel();
                    $bool = $material_wms_plan_store->batch_insert($data['stores']);
                    if ($bool === false) {
                        $db->rollback();
                        throw new BusinessException('盘点计划添加盘点网点关系失败' . json_encode(['stores' => $data['stores']], JSON_UNESCAPED_UNICODE). '; 可能的原因是：' . get_data_object_error_msg($material_wms_plan_store), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
                    }
                }

                //库存盘点计划表-包材
                $data_barcode = [];
                foreach ($data['barcode'] as $barcode) {
                    $data_barcode[] = [
                        'plan_id' => $wmsPlan->id,
                        'barcode' => $barcode,
                        'created_at' => $now,
                        'updated_at' => $now
                    ];
                }
                $material_wms_plan_barcode = new MaterialWmsPlanBarcodeModel();
                $bool = $material_wms_plan_barcode->batch_insert($data_barcode);
                if ($bool === false) {
                    $db->rollback();
                    throw new BusinessException('盘点计划添加盘点包材关系失败' . json_encode(['barcode' => $data_barcode], JSON_UNESCAPED_UNICODE). '; 可能的原因是：' . get_data_object_error_msg($material_wms_plan_barcode), ErrCode::$INVENTORY_CHECK_CREATE_ERROR);
                }
            }
            $db->commit();


        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->info('inventory_check-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }


    /**
     * 获取盘点单列表
     * @return array
     * @author: peak pan
     */
    public function planList(array $condition, int $uid = 0)
    {
        $page_size = empty($condition['pageSize']) ? Enums\InventoryCheckEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? Enums\InventoryCheckEnums::PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'pageNum' => $page_num,
                'pageSize' => $page_size,
                'total_count' => 0,
            ],
        ];

        try {
            //盘点时间起始与结束校验
            $check_start = !empty($condition['sta_time']) ? strtotime($condition['sta_time']) : '';
            $check_end = !empty($condition['end_time']) ? strtotime($condition['end_time']) : '';
            if ($check_start > $check_end) {
                throw new ValidationException('盘点时间起始时间不得大于结束时间', ErrCode::$VALIDATE_ERROR);
            }
            //$condition['uid'] = $uid;
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_code',
                'main.plan_name',
                'main.staff_info_id',
                'main.staff_info_name',
                'main.created_at',
                'main.plan_noun',
                'main.plan_scope',
                'main.department_ids',
                'main.store_ids',
                'main.is_photo',
                'main.is_photo_camera',
                'main.is_relay',
                'main.sta_time',
                'main.end_time',
                'main.status',
                'main.store_nub',
                'main.reality_store_nub',
                'main.is_task'
            ]);
            $builder->from(['main' => MaterialWmsPlanModel::class]);
            //组合搜索条件
            $builder = $this->getCondition($builder, $condition);
            $count = $builder->getQuery()->execute()->count();
            if ($count > 0) {
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id desc');
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $items = $this->handleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('material.wms_plan.plan_list:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 组装列表搜索条件
     * @return mixed
     * @author: peak pan
     */
    private function getCondition($builder, $condition)
    {
        $plan_name = $condition['plan_name'] ?? '';
        $staff_info_id = $condition['staff_info_id'] ?? '';
        $staff_info_name = $condition['staff_info_name'] ?? '';
        $plan_noun = $condition['plan_noun'] ?? '';
        $sta_time = $condition['sta_time'] ?? '';
        $end_time = $condition['end_time'] ?? '';

        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_NO]);

        if (!empty($staff_info_id)) {
            $builder->andWhere('main.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        if (!empty($plan_noun)) {
            $builder->andWhere('main.plan_noun = :plan_noun:', ['plan_noun' => $plan_noun]);
        }
        if (isset($condition['is_photo']) && $condition['is_photo'] != '') {
            $builder->andWhere('main.is_photo = :is_photo:', ['is_photo' => $condition['is_photo']]);
        }
        if (isset($condition['is_photo_camera']) && $condition['is_photo_camera'] != '') {
            $builder->andWhere('main.is_photo_camera = :is_photo_camera:', ['is_photo_camera' => $condition['is_photo_camera']]);
        }
        if (isset($condition['is_relay']) && $condition['is_relay'] != '') {
            $builder->andWhere('main.is_relay = :is_relay:', ['is_relay' => $condition['is_relay']]);
        }
        if (!empty($staff_info_name)) {
            $builder->andWhere('main.staff_info_name LIKE :staff_info_name:', ['staff_info_name' => "%{$staff_info_name}%"]);
        }
        if (!empty($plan_name)) {
            $builder->andWhere('main.plan_name LIKE :plan_name:', ['plan_name' => "%{$plan_name}%"]);
        }
        //盘点时间起始
        if (!empty($sta_time)) {
            $builder->andWhere('main.created_at >= :sta_time:', ['sta_time' => $sta_time]);
        }
        //盘点时间结束
        if (!empty($end_time)) {
            $builder->andWhere('main.created_at <= :end_time:', ['end_time' => $end_time]);
        }
        return $builder;
    }

    /**
     * 组装列表搜索条件
     * @return mixed
     * @author: peak pan
     */
    private function getConditionInfo($builder, $condition)
    {
        $plan_manager_id = $condition['plan_manager_id'] ?? '';
        $store_id        = $condition['store_id'] ?? '';
        $department_id   = $condition['department_id'] ?? '';
        $category        = $condition['category'] ?? '';
        $package_sku_id  = $condition['package_sku_id'] ?? [];
        $plan_id         = $condition['plan_id'] ?? '';
        $plan_status = $condition['plan_status'] ?? '';

        $builder->andWhere('main.is_deleted = :is_deleted:', ['is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_NO]);

        if (!empty($plan_id)) {
            $builder->andWhere('main.plan_id = :plan_id:', ['plan_id' => $plan_id]);
        }
        if (!empty($plan_manager_id)) {
            $builder->andWhere('main.plan_manager_id = :plan_manager_id:', ['plan_manager_id' => $plan_manager_id]);
        }
        if (!empty($store_id)) {
            $builder->andWhere('main.store_id IN ({store_id:array}) ', ['store_id' => $store_id]);
        }
        if ($department_id) {
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($department_id,true);
            array_push($department_ids,$department_id);
            $builder->andWhere('main.department_id in ({department_ids:array})', ['department_ids' => $department_ids]);
        }
        if (!empty($category) && !empty($package_sku_id)) {
            $sku_id =  array_values(array_intersect($category,$package_sku_id));
            if(empty($sku_id)){ $sku_id = [0];}
            $builder->andWhere('main.package_sku_id IN ({package_sku_id:array}) ', ['package_sku_id' => $sku_id]);
        }

        if (empty($category) && !empty($package_sku_id)) {
            $builder->andWhere('main.package_sku_id IN ({package_sku_id:array}) ', ['package_sku_id' => $package_sku_id]);
        }
        if (!empty($category) && empty($package_sku_id)) {
            $builder->andWhere('main.package_sku_id IN ({package_sku_id:array}) ', ['package_sku_id' => $category]);
        }

        //任务状态
        if (!empty($plan_status)) {
            if ($plan_status == InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_NOT_STARTED) {
                //未开始
                $builder->andWhere('main.plan_sta_time > :plan_sta_time:', ['plan_sta_time' => date('Y-m-d H:i:s')]);
            } elseif ($plan_status == InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_ING) {
                //待盘点
                $builder->andWhere('main.plan_sta_time <= :plan_sta_time: and main.plan_status != :plan_status: ', ['plan_sta_time' => date('Y-m-d H:i:s'), 'plan_status' => InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_END]);
            } else if ($plan_status == InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_END) {
                //已结束
                $builder->andWhere('main.plan_status = :plan_status:', ['plan_status' => $plan_status]);
            }
        }

        return $builder;
    }


    /**
     * 组装列表搜索条件
     * @return mixed
     * @author: peak pan
     */
    private function getConditionTask($builder, $condition)
    {
        $plan_id = $condition['plan_id'] ?? '';
        $builder->andWhere('main.plan_id = :plan_id: and main.is_deleted = :is_deleted: and (status_job !=:status_job: or  main.plan_manager_id = :plan_manager_id:)',
            ['plan_id' => $plan_id,'is_deleted' => InventoryCheckEnums::IS_DELETED_NO, 'status_job' => InventoryCheckEnums::STAFF_STATE_IN,'plan_manager_id' => InventoryCheckEnums::IS_DELETED_NO]
        );
        return $builder;
    }

    /**
     * 格式化盘点单列表数据
     * @param array $items 数据
     * @param bool $is_export 是否为导出，true是，false否
     * @return array
     * @author: peak pan
     */
    private function handleListItems(array $items, bool $is_export = false)
    {
        if (empty($items)) {
            return [];
        }

        foreach ($items as &$item) {
            if ($item['is_task'] == 1) {
                if (in_array($item['status'], [3, 4])) {
                    $item['status'] = $item['status'];
                } else {
                    if (strtotime($item['sta_time']) > time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED;
                    } elseif (strtotime($item['end_time']) < time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_END;
                    } elseif (strtotime($item['sta_time']) < time() && strtotime($item['end_time']) > time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_ING;
                    }
                }
            } else {
                $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED;
            }

            $sysStore_name = '';
            $department_ids_name = '';
            $barcode = '';
            if ($is_export) {
                //盘点范围-指定网点类型-盘点网点
                $store_ids = json_decode($item['store_ids'],true);
                if (!empty($store_ids) && $item['plan_scope'] == InventoryCheckEnums::PLAN_STORE_TYPE) {
                    $store_list = (new StoreRepository())->getStoreListByIds($store_ids, Enums::STORE_STATE_ALL);
                    $sysStore_name = implode(',' , array_column($store_list, 'name'));
                }

                //盘点范围-指定网点-盘点网点
                if ($item['plan_scope'] == InventoryCheckEnums::PLAN_SCOPE_SITE) {
                    $store_list = MaterialWmsPlanStoreModel::find([
                        'columns' => 'sys_store_name',
                        'conditions' => 'plan_id = :plan_id:',
                        'bind' => ['plan_id' => $item['id']]
                    ])->toArray();
                    $sysStore_name = implode(',' , array_column($store_list, 'sys_store_name'));
                }

                //盘点范围-指定部门
                if($item['department_ids']) {
                    $sysStoreArr = (new DepartmentModel())->findFirst([
                        "conditions" => 'id = :id:',
                        "bind" => [
                            'id' =>$item['department_ids'],
                        ],
                        'columns' => 'id,name',
                    ]);
                    if(!empty($sysStoreArr->id)){
                        $department_ids_name =  $sysStoreArr->name;
                    }
                }

                //盘点包材
                $barcode_list = MaterialWmsPlanBarcodeModel::find([
                    'columns' => 'barcode',
                    'conditions' => 'plan_id = :plan_id:',
                    'bind' => ['plan_id' => $item['id']]
                ])->toArray();
                $barcode = implode(',' , array_column($barcode_list, 'barcode'));
            }

            $item['store_ids'] = $sysStore_name;
            $item['department_ids'] = $department_ids_name;
            $item['plan_scope'] = empty($item['plan_scope']) ? '-' : static::$t[InventoryCheckEnums::$plan_scope_enums_key[$item['plan_scope']]];
            $item['plan_noun'] = static::$t[InventoryCheckEnums::$plan_noun[$item['plan_noun']]];
            $item['is_photo'] = InventoryCheckEnums::$is_clock[$item['is_photo']];
            $item['is_photo_camera'] = InventoryCheckEnums::$is_clock[$item['is_photo_camera']];
            $item['is_relay'] = InventoryCheckEnums::$is_clock[$item['is_relay']];
            $item['store_nub'] = empty($item['store_nub']) ? '-' : $item['store_nub'];
            $item['reality_store_nub'] = empty($item['reality_store_nub']) ? '-' : $item['reality_store_nub'];
            $item['status_name'] = static::$t[InventoryCheckEnums::$wms_plan_status[$item['status']]];
            $item['barcode'] = $barcode;
        }
        return $items;
    }


    /**
     * 格式化盘点列表数据
     *
     * @param array $items 盘点单列表
     * @param bool $export
     * @return array
     * @author: peak pan
     */
    private function planwmsHandleListItems(array $items, bool $export = false)
    {
        if (empty($items)) {
            return [];
        }
        $store_type_map = array_column(json_decode(EnvModel::getEnvByCode('store_type_map', ''), true), 'name', 'id');

        $lan = static::getLanguage(InventoryCheckEnums::DEFAULT_LANG);
        $title = 'goods_name_' . $lan;
        $packageCategoryArr = (new MaterialPackageCategoryModel())->find([
            'conditions' => ' status = :status: and is_deleted=:is_deleted:',
            'bind' => [
                'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
                'is_deleted' => InventoryCheckEnums::IS_DELETED_NO,
            ],
            'columns' => 'id,' . $title,
        ])->toArray();
        $packageCategoryArrList = array_column($packageCategoryArr, $title, 'id');

        $wms_plan_task = [];

        $wms_plan = (new MaterialWmsPlanModel())->findFirst([
            "conditions" => 'id=:id:',
            "bind" => [
                'id' => $items[0]['plan_id'],
            ],
            'columns' => 'id,staff_info_id,staff_info_name,store_type,plan_code',
        ])->toArray();

        if ($export) {
            $wms_plan_task_arr = (new MaterialWmsPlantaskModel())->find([
                "conditions" => 'id in ({id:array})',
                "bind" => [
                    'id' => array_values(array_unique(array_column($items, 'plan_task_id'))),
                ],
                'columns' => 'id,task_code,created_at,plan_code',
            ])->toArray();
            $wms_plan_task = array_column($wms_plan_task_arr, null, 'id');
        }

        foreach ($items as &$item) {
            if (!in_array($item['plan_status'],[3,4])) {
                if (strtotime($item['plan_sta_time']) > time()) {
                    $item['plan_status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED;
                } else {
                    $item['plan_status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_ING;
                }
            }
            $item['id'] = $wms_plan['plan_code'].$wms_plan['id'];
            $item['status_job'] = static::$t[InventoryCheckEnums::$staff_state[$item['status_job']]];
            $item['category'] = $packageCategoryArrList[$item['category']] ?? '';
            $item['status_name'] = static::$t[InventoryCheckEnums::$wms_plan_info_status[$item['plan_status']]];
            $item['goods_name'] = $item[$title] ?? $item['goods_name_en'];
            $item['reference_stock_number'] = is_null($item['reference_stock_number']) ? '-' : $item['reference_stock_number'];
            if ($export) {
                $item['plan_code_id'] = $wms_plan['plan_code'].$wms_plan['id'] ?? '';
                $item['plan_task_code'] = $wms_plan_task[$item['plan_task_id']]['task_code'] ?? '';
                $item['staff_info_id'] = $wms_plan['staff_info_id'];
                $item['staff_info_name'] = $wms_plan['staff_info_name'];
                $item['task_created_at'] = $wms_plan_task[$item['plan_task_id']]['created_at'] ?? '';
                $store_type_ = json_decode($wms_plan['store_type'],true);
                $item['store_type'] = $store_type_map[$store_type_[0]] ?? '';
            }
        }
        return $items;
    }


    /**
     * 格式化盘点列表数据
     * @param array $items 盘点单列表
     * @return array
     * @author: peak pan
     */
    private function planTaskHandleListItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
                if(in_array($item['status'],[3,4])){
                    $item['status'] = $item['status'];
                }else{
                    if (strtotime($item['sta_time']) > time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED;
                    }elseif(strtotime($item['end_time']) < time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_END;
                    }elseif(strtotime($item['sta_time']) < time() && strtotime($item['end_time']) >time()) {
                        $item['status'] = InventoryCheckEnums::PLAN_NOUN_STATUS_ING;
                    }
                }
            $item['status_job'] = static::$t[InventoryCheckEnums::$staff_state[$item['status_job']]];
            $item['status_name'] = static::$t[InventoryCheckEnums::$wms_plan_status[$item['status']]];
        }
        return $items;
    }


    /**
     * 删除盘点单
     * @param array $data 删除参数
     * @return array
     * @author: peak pan
     */
    public function delPlan(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取盘点单信息
            $plan_wms = $this->getPlanInfoById($data['id']);
            if (empty($plan_wms)) {
                throw new ValidationException('未找到该盘点单信息', ErrCode::$VALIDATE_ERROR);
            }
            if (strtotime($plan_wms->sta_time) <= time() || $plan_wms->is_task == 1) {
                throw new ValidationException(static::$t->_('plan_wms_cannot_del'), ErrCode::$VALIDATE_ERROR);
            }
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //盘点范围关系表删除结果
            $plan_wms_del_ret = $plan_wms->i_update([
                'is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_YES, //已删除
                'updated_at' => date('Y-m-d H:i:s', time()),
            ]);

            if ($plan_wms_del_ret === false) {
                $db->rollback();
                $msgArr = [];
                $messages = $plan_wms_del_ret->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                throw new BusinessException('库存盘点-盘点管理-计划任务删除失败 = ' . json_encode(['data' => $plan_wms, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_DEL_ERROR);
            } else {
                $db->commit();
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('plan_wms_del_failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS,
        ];
    }


    /**
     * 删除盘点单
     * @param array $data 撤销参数
     * @return array
     * @author: peak pan
     */
    public function revokePlan(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //获取盘点单信息
            $plan_wms = $this->getPlanInfoByOne($data['id']);
            if (empty($plan_wms)) {
                throw new ValidationException('未找到该盘点单信息', ErrCode::$VALIDATE_ERROR);
            }

            if (strtotime($plan_wms->end_time) <= time()) {
                throw new ValidationException(static::$t->_('plan_wms_cannot_not_revoke'), ErrCode::$VALIDATE_ERROR);
            }

            if (empty($plan_wms->is_task)) {
                throw new ValidationException(static::$t->_('plan_wms_not_task_sta'), ErrCode::$VALIDATE_ERROR);
            }

            if (in_array($plan_wms->status, [3, 4])) {
                throw new ValidationException(static::$t->_('plan_wms_cannot_ing_not_revoke'), ErrCode::$VALIDATE_ERROR);
            }

            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //盘点范围关系表删除结果
            $plan_wms_del_ret = $plan_wms->i_update([
                'status' => InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE, //已撤销
                'updated_at' => date('Y-m-d H:i:s', time()),
            ]);

            if ($plan_wms_del_ret === false) {
                $db->rollback();
                $msgArr = [];
                $messages = $plan_wms_del_ret->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                throw new BusinessException('库存盘点-盘点管理-计划任务撤销失败 = ' . json_encode(['data' => $plan_wms, 'message' => $msgArr], JSON_UNESCAPED_UNICODE), ErrCode::$INVENTORY_CHECK_DEL_ERROR);
            } else {
                //批量修改拍点任务
                $update_success = $db->updateAsDict(
                    (new MaterialWmsPlantaskModel)->getSource(),
                    [
                        'status' => InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE,
                        'updated_at' => date('Y-m-d')
                    ],
                    [
                        'conditions' => 'plan_id=?',
                        'bind' => [$data['id']]
                    ]
                );
                if (!$update_success) {
                    $db->rollback();
                    throw new BusinessException('更新物料任务据失败', ErrCode::$UPDATE_ASSET_DATA_ERROR);
                }

                $update_success = $db->updateAsDict(
                    (new MaterialWmsPlantaskInfoModel())->getSource(),
                    [
                        'plan_status' => InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_END,
                        'updated_at' => date('Y-m-d')
                    ],
                    [
                        'conditions' => 'plan_id=?',
                        'bind' => [$data['id']]
                    ]
                );
                if (!$update_success) {
                    $db->rollback();
                    throw new BusinessException('更物料盘点数据数据失败', ErrCode::$UPDATE_ASSET_DATA_ERROR);
                }

                $db->commit();

                $wmsPlantaskArr = (new MaterialWmsPlantaskModel())->find(
                    ["conditions" => 'plan_id=:plan_id: and  msg_id  > :msg_id: and status=:status: and plan_manager_id >:plan_manager_id:',
                        "bind" => [
                            'plan_id' => $data['id'],
                            'msg_id' => InventoryCheckEnums::DEFAULT_VALUES,
                            'status' => InventoryCheckEnums::PLAN_NOUN_STATUS_REVOKE,
                            'plan_manager_id' => InventoryCheckEnums::DEFAULT_VALUES

                        ],
                        'columns' => 'id,msg_id,plan_manager_id',
                    ]);
                if (!empty($wmsPlantaskArr)) {
                    foreach ($wmsPlantaskArr->toArray() as $item_msg) {
                        $msg_arr['msg_id'] = $item_msg['msg_id'];
                        $msg_arr['staff_id'] = $item_msg['plan_manager_id'];
                        $ac = new ApiClient('by', '', 'has_read_operation_by_staffid', static::$language);
                        $ac->setParams([
                            $msg_arr
                        ]);
                        $res = $ac->execute();
                        $code = $res['result']['code'] ?? '';
                    }
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }
        if (!empty($real_message)) {
            $db->rollback();
            $logger = $this->getDI()->get('logger');
            $logger->warning('plan_wms_del_failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS,
        ];
    }


    /**
     * 查找管理人是否有效
     * @Date: 2022-05-13 17:15
     * @return:
     **@author: peak pan
     */
    public function getStaffInfo(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $staff_info = (new StaffInfoModel())->user_info($data['staff_info_id']);
        if (!empty($staff_info)) {
            if ($staff_info['state'] == 1) {
                return [
                    'code' => $code,
                    'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                    'data' => ['status' => true]
                ];
            } else {
                return [
                    'code' => 0,
                    'message' => static::$t->_('plan_manager_id_jobquit'),
                    'data' => ['status' => false]
                ];
            }
        }else{
            return [
                'code' => 0,
                'message' => static::$t->_('plan_manager_id_not_user'),
                'data' => ['status' => false]
            ];
        }
    }

    /**
     * 获取盘点单 - 创建页 - 基本信息默认值
     * @return array
     * @author: peak pan
     */
    public function serialNo()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $data['serial_number'] = static::genSerialNo(InventoryCheckEnums::PLAN_CHECK_SERIAL_NUMBER_PREFIX, RedisKey::PLAN_CHECK_SERIAL_NUMBER_COUNTER);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('物料盘点-获取code:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 根据盘点单ID获取盘点单信息
     * @return mixed
     * @author: peak pan
     */
    private function getPlanInfoById(int $id)
    {
        return MaterialWmsPlanModel::findFirst([
            'conditions' => 'id = :id: and status = :status: and is_deleted = :is_deleted: ',
            'bind' => ['id' => $id, 'is_deleted' => Enums\InventoryCheckEnums::IS_DELETED_NO, 'status' => Enums\InventoryCheckEnums::PLAN_NOUN_STATUS_NOT_STARTED]

        ]);
    }

    /**
     * 查看数据
     * @Date: 2022-05-14 18:11
     * @return:
     * @author: peak pan
     */
    public function getPlanInfoByOne(int $id)
    {
        return MaterialWmsPlanModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
    }

    /**
     * 查看盘点单信息
     * @param array $data 查询信息参数
     * @return array
     * @author: peak pan
     */
    public function infoPlanWms(array $data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $info = [];
        try {
            //获取盘点单信息
            $planInfoByOne = $this->getPlanInfoByOne($data['id']);
            if (empty($planInfoByOne)) {
                throw new ValidationException('未找到该盘点单信息', ErrCode::$VALIDATE_ERROR);
            }
            $item = $planInfoByOne->toArray();
            $item['id'] = (int)$item['id'];
            $item['sta_month'] = date('Y-m', strtotime($item['sta_month']));
            $item['end_month'] = date('Y-m', strtotime($item['end_month']));
            $item['plan_noun'] = (int)$item['plan_noun'];
            $item['plan_nub'] = (int)$item['plan_nub'];
            $item['plan_scope'] = (int)$item['plan_scope'];
            $item['store_type'] = json_decode($item['store_type'], true);
            $item['store_ids'] = json_decode($item['store_ids'], true);
            $item['plan_manager_id'] = (int)$item['plan_manager_id'];
            $item['store_nub'] = (int)$item['store_nub'];
            $item['status'] = (int)$item['status'];
            $item['reality_store_nub'] = (int)$item['reality_store_nub'];
            $item['staff_info_id'] = (int)$item['staff_info_id'];
            $item['up_staff_info_id'] = (int)$item['up_staff_info_id'];
            $item['is_task'] = (int)$item['is_task'];

            //盘点范围-指定网点类型
            $item['store_names'] = '';
            if ($item['plan_scope'] == InventoryCheckEnums::PLAN_STORE_TYPE) {
                $store_list = (new StoreRepository())->getStoreListByIds($item['store_ids'], Enums::STORE_STATE_ALL);
                $item['store_names'] = implode(',', array_column($store_list, 'name'));
            }

            //盘点范围-指定网点
            $item['stores'] = [];
            if ($item['plan_scope'] == InventoryCheckEnums::PLAN_SCOPE_SITE) {
                $item['stores'] = $planInfoByOne->getStore()->toArray();
            }

            //盘点范围-包材列表
            $item['barcode'] = $this->getPlanBarcodeList($planInfoByOne);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('plan_detailAction:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $item,
        ];
    }

    /**
     * 获取盘点计划-包材列表
     * @param object $plan_info 盘点计划对象
     * @return mixed
     */
    public function getPlanBarcodeList($plan_info)
    {
        $barcode_list = $plan_info->getBarcode()->toArray();
        $barcode = array_column($barcode_list, 'barcode');
        $list = [];
        if ($barcode) {
            $list = MaterialPackageSkuModel::find([
                'columns' => 'id, barcode, goods_name_zh, goods_name_th, goods_name_en, specs_model, unit, image_path, category',
                'conditions' => 'barcode in({barcode:array})',
                'bind' => ['barcode' => $barcode]
            ])->toArray();
        }
        return $list;
    }

    /**
     * 查询盘点单报表
     * @param array $condition 查询条件组
     * @return array
     * @author: peak pan
     */
    public function getDetailList(array $condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //检验员工工号
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_code',
                'main.plan_name',
                'main.staff_info_id',
                'main.staff_info_name',
                'main.created_at',
                'main.plan_noun',
                'main.plan_scope',
                'main.department_ids',
                'main.store_ids',
                'main.is_photo',
                'main.is_photo_camera',
                'main.is_relay',
                'main.sta_time',
                'main.end_time',
                'main.status',
                'main.store_nub',
                'main.reality_store_nub',
                'main.plan_remark',
                'main.is_task'
            ]);
            $builder->from(['main' => MaterialWmsPlanModel::class]);
            //组合搜索条件
            $builder = $this->getCondition($builder, $condition);
            $count = $builder->getQuery()->execute()->count();
            if ($count > 0) {
                $page_size = empty($condition['pageSize']) ? Enums\InventoryCheckEnums::PAGE_SIZE : $condition['pageSize'];
                $page_num = empty($condition['pageNum']) ? Enums\InventoryCheckEnums::PAGE_NUM : $condition['pageNum'];
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $items = $this->handleListItems($items, true);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('getDetailList:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 查询盘点单报表
     * @param array $condition 查询条件组
     * @return mixed
     * @author: peak pan
     */
    public function getPlaninfoDetailListTotal(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

        // 组合搜索
        $builder = $this->getConditionInfo($builder, $condition);
        return $builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
    }

    /**
     * 查询盘点单报表
     * @param array $condition 查询条件组
     * @param bool $export 是否导出
     * @return array
     * @author: peak pan
     */
    public function getPlaninfoDetailList(array $condition, $export = false)
    {
        $list = [];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);

            //组合搜索条件
            $builder = $this->getConditionInfo($builder, $condition);

            $builder->columns([
                'main.id',
                'main.plan_id',
                'main.plan_name',
                'main.store_id',
                'main.store_name',
                'main.department_id',
                'main.department_name',
                'main.plan_sta_time',
                'main.plan_end_time',
                'main.reality_sta_time',
                'main.reality_end_time',
                'main.plan_manager_id',
                'main.status_job',
                'main.barcode',
                'main.goods_name_zh',
                'main.goods_name_th',
                'main.goods_name_en',
                'main.specs_model',
                'main.unit',
                'main.category',
                'main.plan_nub',
                'main.reference_stock_number',
                'main.plan_image_path',
                'main.plan_sku_end_time',
                'main.plan_status',
                'main.plan_task_id'
            ]);

            $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
            $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $builder->limit($page_size, $offset);
            $list = $builder->getQuery()->execute()->toArray();
            $list = $this->planwmsHandleListItems($list, $export);

        } catch (\Exception $e) {
            $this->logger->warning('getPlaninfoDetailList:' . $e->getMessage() . $e->getTraceAsString());
        }

        return $list;
    }


    /**
     * 查询盘点任务报表
     * @param array $condition 查询条件组
     * @param bool $export 是否导出
     * @return array
     * @author: peak pan
     */
    public function getPlanTaskList(array $condition, $export = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //检验员工工号
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'main.id',
                'main.plan_name',
                'main.plan_id',
                'main.plan_code',
                'main.task_code',
                'main.staff_info_id',
                'main.staff_info_name',
                'main.created_at',
                'main.store_id',
                'main.store_name',
                'main.sta_time',
                'main.end_time',
                'main.plan_manager_id',
                'main.plan_manager_name',
                'main.status_job',
                'main.status'
            ]);
            $builder->from(['main' => MaterialWmsPlantaskModel::class]);
            //组合搜索条件
            $builder = $this->getConditionTask($builder, $condition);
            $count = $builder->getQuery()->execute()->count();
            if ($count > 0) {
                $page_size = empty($condition['pageSize']) ? Enums\InventoryCheckEnums::PAGE_SIZE : $condition['pageSize'];
                $page_num = empty($condition['pageNum']) ? Enums\InventoryCheckEnums::PAGE_NUM : $condition['pageNum'];
                $offset = $page_size * ($page_num - 1);
                $builder->limit($page_size, $offset);
                $items_obj = $builder->getQuery()->execute();
                $items = $items_obj ? $items_obj->toArray() : [];
                $items = $this->planTaskHandleListItems($items);
            }

            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('getPlaninfoDetailList:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 盘点计划单报表导出
     * @param array $condition 筛选条件
     * @return array
     * @author: peak pan
     */
    public function getDetailExport(array $condition)
    {

        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $limit_size = 30000;
            //筛选出符合导出条件的员工列表
            $condition['pageNum'] = 1;
            $condition['pageSize'] = $limit_size;
            $list = $this->getDetailList($condition, true);
            $total_count = $list['data']['pagination']['total_count'];
            $row_values = [[]];
            if ($total_count > 0) {
                $row_values = $list['data']['items'];
            }

            // 最大条数限制
            if (count($row_values) > $limit_size) {
                throw new ValidationException(static::$t->_('inventory_check_asset_download_limit_max'));
            }
            foreach ($row_values as $value) {
                unset($value['status']);
                $value_[] = [
                    $value['plan_code'].$value['id'],
                    $value['plan_code'],
                    $value['plan_name'],
                    $value['staff_info_id'],
                    $value['staff_info_name'],
                    $value['created_at'],
                    $value['plan_noun'],
                    $value['sta_time'],
                    $value['end_time'],
                    $value['barcode'],
                    $value['plan_scope'],
                    $value['store_ids'],
                    $value['department_ids'],
                    $value['is_photo'],
                    $value['is_photo_camera'],
                    $value['is_relay'],
                    $value['status_name'],
                    $value['plan_remark'],
                ];
            }
            $header = [
                static::$t->_('material_wms_plan_code_id'), //子计划ID
                static::$t->_('material_wms_plan_code'), //计划批号
                static::$t->_('material_wms_plan_name'), //计划名称
                static::$t->_('material_wms_staff_info_id'), //创建人工号
                static::$t->_('material_wms_staff_info_name'), //创建人姓名
                static::$t->_('material_wms_created_at'), //创建计划时间
                static::$t->_('material_wms_plan_noun'), //盘点频率
                static::$t->_('material_wms_sta_time'), //盘点开始时间
                static::$t->_('material_wms_end_time'), //盘点结束时间
                static::$t->_('material_wms_plan_barcode'),//盘点包材范围
                static::$t->_('material_wms_plan_scope'), //盘点范围
                static::$t->_('material_wms_store_ids'), //盘点网点
                static::$t->_('material_wms_department_ids'), //盘点部门
                static::$t->_('material_wms_is_photo'), //是否必须拍照
                static::$t->_('material_wms_is_photo_camera'), //是否仅允许拍照上传
                static::$t->_('material_wms_is_relay'), //盘点前是否允许转派盘点任务
                static::$t->_('material_wms_status_name'), //状态
                static::$t->_('material_wms_plan_remark'), //备注

            ];
            $file_name = "Consumable_Inventory_" . date("YmdHis");
            $result = $this->exportExcel($header, $value_, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $logger = $this->getDI()->get('logger');
            $logger->warning('download-inventory_check-staff-asset-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }


    /**
     * 盘点任务详情单报表导出
     * @param array $condition 取数参数
     * @param string $file_name 文件名

     * @return mixed
     */
    public function getPlanInfoExport(array $condition, $file_name = '')
    {
        $return = [
            'total' => 0,
            'file_url' => ''
        ];

        try {
            $this->logger->info('当前内存: ' . memory_usage());

            // 1.设置语言
            self::setLanguage($condition['language']);

            // 2.分批取数
            $return['total'] = $this->getPlaninfoDetailListTotal($condition);

            // 2.1获取当前数据量, 确定取数批次
            $total_page_num = ceil($return['total'] / DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE);

            // 2.2分批取
            $excel_data = [];
            $page_num = DownloadCenterEnum::INIT_PAGE_NUM;
            for ($page_num; $page_num <= $total_page_num; $page_num++) {
                $this->logger->info('----- 取数批次: ' . $page_num . '-----');

                $condition['pageNum'] = $page_num;
                $condition['pageSize'] = DownloadCenterEnum::DEFAUL_PER_PAGE_SIZE;
                $list = $this->getPlaninfoDetailList($condition, true);
                $this->logger->info('本批数量: ' . count($list));

                // 2.3合并数据
                foreach ($list as $value) {
                    $excel_data[] = [
                        $value['plan_code_id'] ?? '-',
                        $value['plan_name'],
                        $value['plan_task_code'],
                        $value['staff_info_id'],
                        $value['staff_info_name'],
                        $value['task_created_at'],
                        $value['store_id'],
                        $value['store_name'],
                        $value['store_type'],
                        $value['category'],
                        $value['department_id'],
                        $value['department_name'],
                        $value['plan_sta_time'],
                        $value['plan_end_time'],
                        $value['reality_sta_time'],
                        $value['reality_end_time'],
                        $value['plan_manager_id'] ?? 0,
                        $value['status_job'],
                        $value['barcode'],
                        $value['goods_name_zh'],
                        $value['goods_name_th'],
                        $value['goods_name_en'],
                        $value['specs_model'],
                        $value['unit'],
                        $value['plan_nub'],
                        is_numeric($value['reference_stock_number']) ? $value['reference_stock_number'] : '',
                        $value['plan_sku_end_time'],
                        $value['plan_image_path'],
                        $value['status_name'],
                    ];
                }

                unset($list);

                $this->logger->info('当前内存: ' . memory_usage());
            }

            $this->logger->info('实际总量: ' . count($excel_data) . '; 当前内存: ' . memory_usage());

            $header = [
                static::$t->_('plantask_info_id'), //盘点ID
                static::$t->_('plantask_info_plan_name'), //盘点名称
                static::$t->_('plantask_info_plan_id'), //任务批次号
                static::$t->_('material_wms_staff_info_id'), //计划创建人id
                static::$t->_('material_wms_staff_info_name'), //计划创建人姓名
                static::$t->_('material_wms_task_created_at'), //计划创建时间
                static::$t->_('plantask_info_store_id'), //网点编号
                static::$t->_('plantask_info_store_name'), //盘点网点
                static::$t->_('plan_wms_stroe_type'), //网点类型
                static::$t->_('plantask_info_category'), //分类
                static::$t->_('plantask_info_department_id'), //部门编号
                static::$t->_('plantask_info_department_name'), //盘点部门
                static::$t->_('plantask_info_plan_sta_time'), //任务开始时间
                static::$t->_('plantask_info_plan_end_time'), // 任务截止时间
                static::$t->_('plantask_info_reality_sta_time'), //实际盘点开始时间
                static::$t->_('plantask_info_reality_end_time'), //实际盘点结束时间
                static::$t->_('plantask_info_plan_manager_id'), //盘点执行人
                static::$t->_('plantask_info_status_job'), // 在职状态
                static::$t->_('plantask_info_barcode'), //   Barcode
                static::$t->_('plantask_info_goods_name_zh'), // 包材名称中文
                static::$t->_('plantask_info_goods_name_th'), // 包材名称中文
                static::$t->_('plantask_info_goods_name_en'), // 包材名称中文
                static::$t->_('plantask_info_specs_model'), // 规格型号
                static::$t->_('plantask_info_unit'), //  单位
                static::$t->_('plantask_info_plan_nub'), //本次盘点数量
                static::$t->_('plantask_info_reference_stock_number'), //参考库存数量
                static::$t->_('plantask_info_plan_sku_end_time'), //盘点时间
                static::$t->_('plantask_info_plan_image_path'), //图片
                static::$t->_('plantask_info_status_name'), //盘点任务状态
            ];

            // 3.生成Excel
            $excel_result = $this->exportExcel($header, $excel_data, $file_name);
            $this->logger->info('Excel结果: ' . json_encode($excel_result, JSON_UNESCAPED_UNICODE));

            if (isset($excel_result['code']) && $excel_result['code'] == ErrCode::$SUCCESS && !empty($excel_result['data'])) {
                $return['file_url'] = $excel_result['data'];
            }

        } catch (\Exception $e) {
            $this->logger->warning('download-inventory_check-staff-asset-failed:' . $e->getMessage() . $e->getTraceAsString());
        }

        return $return;
    }


    /**
     * 盘点任务详情单报表导出
     * @param array $condition 筛选条件
     * @return array
     * @author: peak pan
     */
    public function getPlantaskExport(array $condition)
    {
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            $limit_size = InventoryCheckEnums::ASSETS_DOWNLOAD_LIMIT;
            //筛选出符合导出条件的员工列表
            $condition['pageNum'] = 1;
            $condition['pageSize'] = $limit_size;
            $list = $this->getPlanTaskList($condition, $export = true);

            $total_count = $list['data']['pagination']['total_count'];
            $row_values = [[]];
            if ($total_count > 0) {
                $row_values = $list['data']['items'];
            }
            // 最大条数限制
            if (count($row_values) >= 30000) {
                throw new ValidationException(static::$t->_('inventory_check_asset_download_limit_max'));
            }
            $sysStore_name ='';
            foreach ($row_values as $value) {
                if(!empty($value['store_id'])){
                    $sysStoreArr = (new StoreModel())->findFirst([
                        "conditions" => 'id =:id:',
                        "bind" => [
                            'id' => $value['store_id'],
                        ],
                        'columns' => 'id,name,created_at',
                    ]);
                    if(!empty($sysStoreArr)){
                        $sysStore_name =  $sysStoreArr->name;
                    }
                }
                $value_[] = [
                    $value['plan_code'].($value['plan_id'] ?? ''),
                    $value['plan_name'] ?? '',
                    $value['plan_code'] ?? '',
                    $value['staff_info_id'] ?? '',
                    $value['staff_info_name'] ?? '',
                    $value['created_at'] ?? '',
                    $value['store_id'] ?? '',
                    $sysStore_name,
                    $value['sta_time'] ?? '',
                    $value['end_time'] ?? '',
                    $value['plan_manager_id']==0?'':$value['plan_manager_id'],
                    $value['plan_manager_id']==0?'':$value['status_job'],
                    $value['status_name'] ?? '',
                ];
            }
            $header = [
                static::$t->_('plantask_info_id'), //盘点ID
                static::$t->_('plantask_info_plan_name'), //盘点名称
                static::$t->_('plantask_info_plan_id'), //任务批次号
                static::$t->_('material_wms_staff_info_id'), //计划创建人id
                static::$t->_('material_wms_staff_info_name'), //计划创建人姓名
                static::$t->_('material_wms_task_created_at'), //计划创建时间
                static::$t->_('plantask_info_store_id'), //网点编号
                static::$t->_('plantask_info_store_name'), //盘点网点
                static::$t->_('plantask_info_plan_sta_time'), //任务开始时间
                static::$t->_('plantask_info_plan_end_time'), // 任务截止时间
                static::$t->_('plantask_info_plan_manager_id'), //盘点执行人
                static::$t->_('plantask_info_status_job'), // 在职状态
                static::$t->_('plantask_info_status_name'), //盘点任务状态
            ];
            $file_name = "Uncounted_Data_Reports_" . date("YmdHis");
            $result = $this->exportExcel($header, $value_, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (ValidationException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage(); //. $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $logger = $this->getDI()->get('logger');
            $logger->warning('download-inventory_check-staff-asset-failed:' . $message . $e->getTraceAsString());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [],
        ];
    }


    /**
     * 获取特定条件下的员工数
     * @param array $condition 筛选条件组
     * @return mixed
     * @author: peak pan
     */
    public function getStaffListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => HrStaffInfo::class]);
        $builder->columns(['main.id']);
        //组合搜索条件
        $builder = $this->getStaffCondition($builder, $condition);
        return $builder->getQuery()->execute()->count();
    }


    /**
     * 组装搜索条件
     * @param object $builder 查询器对象
     * @param array $condition 查询条件组
     * @return mixed
     **@author: peak pan
     */
    private function getStaffCondition($builder, $condition)
    {
        //在职、停职
        $state_arr = [
            Enums\StaffInfoEnums::STAFF_STATE_IN,
            Enums\StaffInfoEnums::STAFF_STATE_STOP,
        ];
        $builder->andWhere('main.state in ({states:array})', ['states' => $state_arr]);
        $builder->andWhere('main.formal in ({formals:array})', ['formals' => [Enums\StaffInfoEnums::FORMAL_IN, Enums\StaffInfoEnums::FORMAL_TRAINEE]]);
        $builder->andWhere('main.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => Enums\StaffInfoEnums::IS_SUB_STAFF_NO]);
        if ($condition['department_id']) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_id = $condition['department_id'];
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($department_id, true);
            array_push($department_ids, $department_id);
            $builder->andWhere('main.node_department_id in ({department_ids:array})', ['department_ids' => $department_ids]);
        }
        if ($condition['staff_ids']) {
            //按照特定工号查找
            $builder->andWhere('main.staff_info_id in ({staff_ids:array})', ['staff_ids' => $condition['staff_ids']]);
        }
        if ($condition['job_ids']) {
            //按照特定工号查找
            $builder->andWhere('main.job_title in ({job_ids:array})', ['job_ids' => $condition['job_ids']]);
        }
        //测试过程中发现工号存在0的，查线上库也存在工号为0的情况，故而要排除掉
        $builder->andWhere('main.staff_info_id > :staff_info_id:', ['staff_info_id' => 0]);
        return $builder;
    }

    /**
     * 获取sku分类数据 提供前端查询使用
     * @Token
     * @Date: 2022-05-14 20:54
     * @return:
     * @author: peak pan
     */
    public function getPlanSkuEnums()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $list = (new MaterialPackageSkuModel())->find([
            'conditions' => ' status = :status: and is_deleted=:is_deleted:',
            'bind' => [
                'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
                'is_deleted' => InventoryCheckEnums::DEFAULT_VALUES
            ]
        ]);
        $lan = static::getLanguage(InventoryCheckEnums::DEFAULT_LANG);
        $title = 'goods_name_' . $lan;
        $packageCategoryArr = (new MaterialPackageCategoryModel())->find([
            'conditions' => ' status = :status: and is_deleted=:is_deleted:',
            'bind' => [
                'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
                'is_deleted' => InventoryCheckEnums::IS_DELETED_NO,
            ],
            'columns' => 'id,' . $title,
        ])->toArray();
        if (!empty($list)) {
            $sku_arr = $list->toArray();
            $barcode_arr = [];
            $goods_name_zh = [];
            $goods_name_th = [];
            $goods_name_en = [];
            $specs_model_arr = [];
            $groups = [];
            foreach ($sku_arr as $value) {
                $barcode['id'] = $value['id'];
                $barcode['label'] = $value['barcode'];
                $barcode_arr[] = $barcode;
                $name_zh['id'] = $value['id'];
                $name_zh['label'] = $value['goods_name_zh'];
                $goods_name_zh[] = $name_zh;
                $name_th['id'] = $value['id'];
                $name_th['label'] = $value['goods_name_th'];
                $goods_name_th[] = $name_th;
                $name_en['id'] = $value['id'];
                $name_en['label'] = $value['goods_name_en'];
                $goods_name_en[] = $name_en;
                $model_arr['id'] = $value['id'];
                $model_arr['label'] = $value['specs_model'];
                $specs_model_arr[] = $model_arr;


                $key = $value['category'];
                if (!isset($groups[$key])) {
                    $groups[$key] = [
                        'ids' => [$value['id']],
                    ];
                } else {
                    $groups[$key]['ids'][] = $value['id'];
                }
            }
            foreach ($packageCategoryArr as $packageCategory) {
                $rs['id'] = implode(',', $groups[$packageCategory['id']]['ids']);
                $rs['label'] = $packageCategory[$title];
                $categoryArr[] = $rs;
            }
            $data['barcode_arr'] = $barcode_arr;
            $data['goods_name_arr'] = [
                'goods_name_zh' => $goods_name_zh,
                'goods_name_th' => $goods_name_th,
                'goods_name_en' => $goods_name_en
            ];
            $data['specs_model_arr'] = $specs_model_arr;
            $data['spackage_ategory_arr'] = $categoryArr;
            $wms_plan_info_status = InventoryCheckEnums::$wms_plan_info_status;
            //由于库里不会存储3所以无需搜索
            unset($wms_plan_info_status[InventoryCheckEnums::PLAN_NOUN_INFO_STATUS_REVOKE]);
            foreach ($wms_plan_info_status as $key => $value) {
                $data['wms_plan_info_status'][] = [
                    'id' => $key,
                    'label' => static::$t->_($value)
                ];
            }
        } else {
            $data = [];
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 获取盘点单列表
     * @param array $condition 查询条件组
     * @param int $uid 当前员工ID
     * @return array
     * @author: peak pan
     */
    public function planInfoList(array $condition, int $uid = 0)
    {
        ini_set('memory_limit', '512M');

        $page_size = empty($condition['pageSize']) ? InventoryCheckEnums::PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? InventoryCheckEnums::PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'pageNum' => $page_num,
                'pageSize' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //盘点时间起始与结束校验
            //$condition['uid'] = $uid;
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialWmsPlantaskInfoModel::class]);
            //组合搜索条件
            $builder = $this->getConditionInfo($builder, $condition);
            $count = (int) $builder->columns('COUNT(main.id) AS t_count')->getQuery()->getSingleResult()->t_count;
            if ($count > 0) {
                $builder->columns([
                    'main.id',
                    'main.plan_id',
                    'main.plan_name',
                    'main.store_id',
                    'main.store_name',
                    'main.department_id',
                    'main.department_name',
                    'main.plan_sta_time',
                    'main.plan_end_time',
                    'main.reality_sta_time',
                    'main.reality_end_time',
                    'main.plan_manager_id',
                    'main.status_job',
                    'main.barcode',
                    'main.goods_name_zh',
                    'main.goods_name_th',
                    'main.goods_name_en',
                    'main.specs_model',
                    'main.unit',
                    'main.category',
                    'main.plan_nub',
                    'main.reference_stock_number',
                    'main.plan_image_path',
                    'main.plan_sku_end_time',
                    'main.plan_status'
                ]);

                $builder->limit($page_size, $offset);
                $builder->orderby('main.plan_sta_time desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->planwmsHandleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('material.wms_plan_info.plan_list:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
    * 查询获取数据
    * @Date: 10/16/22 12:08 PM
    * @param array $param 条件
    * @param bool $status 状态
    * @return  array
    **/
    public function getStoreData($param, $status = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = (new StoreService())->getAllStoreList($status, $param, 1);
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }

    /**
     * 20848-盘点计划优化-指定网点-列表返回逻辑
     * @param array $param 请求参数组
     * @param bool $need_header 是否需要总部，true是，false否
     * @param bool $return_all 是否返回全部，true是，false否
     * @return array
     */
    public function searchStore($param, $need_header = true, $return_all = false)
    {
        //1. 网点信息表中激活状态为激活，state=1-原有过滤条件
        $param['state'] = Enums::STORE_STATE_ACTIVE;

        //2. 并且网点类型不是FH、SHOP、USHOP时，网点营业状态为营业，use_state=1
        $param['not_in_category'] = [Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY, Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY, Enums::STORE_CATEGORY_FH, Enums::STORE_CATEGORY_USHOP];

        //3. 并且网点编码不属于不下发包材盘点任务网点-本次新增配置
        $material_wms_plan_task_store_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_wms_plan_task_store_ids');

        //4. 并且网点编码不属于虚拟网点-如果网点编码属于网点配置中的客户揽件虚拟网点，则该网点为虚拟网点
        $store_repository = new StoreRepository();
        $virtual_store_ids = $store_repository->getVirtualStoreIds();
        $param['not_in_store_ids'] = array_unique(array_merge($material_wms_plan_task_store_ids, $virtual_store_ids));
        if ($return_all === false) {
            $param['page_num'] = $param['pageSize'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;
        }
        $data = $store_repository->searchSysStore($param, 'id, name, manager_id, category');
        //返回总部
        if ($need_header) {
            array_unshift($data, ['id' => '-1', 'name' => Enums::PAYMENT_HEADER_STORE_NAME, 'manager_id' => '']);
        }
        return [
            'code' => ErrCode::$SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 盘点包材列表
     * @param array $param 参数组
     * @return array
     */
    public function searchBarcode($param)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('barcode, goods_name_zh, goods_name_th, goods_name_en, specs_model, unit, image_path');
        $builder->from(MaterialPackageSkuModel::class);
        $builder->where('is_deleted = :is_deleted: and status = :status:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS]);
        if (!empty($param['barcode'])) {
            $builder->andWhere('barcode = :barcode:', ['barcode' => $param['barcode']]);
        }
        $builder->orderBy('barcode');
        $data = $builder->getQuery()->execute()->toArray();

        return [
            'code' => ErrCode::$SUCCESS,
            'message' => '',
            'data' => $data,
        ];
    }

    /**
     * 添加计划-批量导入网点
     * @param array $excel_file 导入excel
     * @return array
     * @throws ValidationException
     */
    public function importStore($excel_file)
    {
        $file = $excel_file[0];
        //支持选择xlsx格式的单个文件
        $extension = $file->getExtension();
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType([
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,//网点编码
                1 => \Vtiful\Kernel\Excel::TYPE_STRING,//盘点负责人工号
            ])
            ->getSheetData();
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('material_wms_plan_import_store_001'), ErrCode::$VALIDATE_ERROR);
        }
        //跳过空行
        foreach ($excel_data as $line => $row) {
            if (empty(implode(',', array_filter($row)))) {
                //空行
                unset($excel_data[$line]);
            }
        }
        //单次操作付款明细条数限制
        $excel_data_count = count($excel_data);
        if ($excel_data_count > InventoryCheckEnums::PLAN_IMPORT_STORE_MAX) {
            throw new ValidationException(static::$t->_('material_wms_plan_import_store_002', ['max' => InventoryCheckEnums::PLAN_IMPORT_STORE_MAX]), ErrCode::$VALIDATE_ERROR);
        }
        $result_data = $excel_data;
        $excel_data = $this->excelToData($excel_data);

        //网点列表
        $store_repository = new StoreRepository();
        $store_ids = array_filter(array_column($excel_data, 'sys_store_id'));
        $store_list = $store_repository->getStoreListByIds(array_unique($store_ids), Enums::STORE_STATE_ALL);

        //虚拟网点id
        $virtual_store_ids = $store_repository->getVirtualStoreIds();
        //不下发包材盘点任务网点
        $material_wms_plan_task_store_ids = EnumsService::getInstance()->getSettingEnvValueIds('material_wms_plan_task_store_ids');

        //汇总每个网点编号出现的次数
        $check_repeat = array_count_values($store_ids);

        //负责人列表
        $manager_user_list = (new HrStaffRepository())->getStaffListByStaffIds(array_values(array_unique(array_filter(array_column($excel_data, 'manager_id')))));
        $error_num = 0;
        $data = [];
        foreach ($excel_data as $key => $item) {
            $one_store_info = $one_manager_info = $error_msg = [];
            //网点编码-必填
            if (empty($item['sys_store_id'])) {
                $error_msg[] = static::$t->_('material_wms_plan_import_store_003');
            } else {
                $one_store_info = $store_list[$item['sys_store_id']] ?? [];
                //网点编码在系统中不存在！
                if (empty($one_store_info)) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_004');
                }

                //网点编码属于不下发包材盘点任务网点！
                if (in_array($item['sys_store_id'], $material_wms_plan_task_store_ids)) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_005');
                }

                //网点未激活！
                if ($one_store_info && $one_store_info['state'] != Enums::STORE_STATE_ACTIVE) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_006');
                }

                //网点为虚拟网点！
                if (in_array($item['sys_store_id'], $virtual_store_ids)) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_007');
                }

                //判断网点类型是否不属于FH、SHOP、USHOP，如果不属于则校验网点营业状态是否为停业，如果是，则提示“网点已停业！”
                if ($one_store_info && !in_array($one_store_info['category'], [Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY, Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY, Enums::STORE_CATEGORY_FH, Enums::STORE_CATEGORY_USHOP]) && $one_store_info['use_state'] != 1) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_008');
                }

                //网点编码重复！
                if ($check_repeat[$item['sys_store_id']] >= 2) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_009');
                }
            }

            //盘点负责人工号必填！
            if (empty($item['manager_id'])) {
                $error_msg[] = static::$t->_('material_wms_plan_import_store_010');
            } else {
                //工号在系统中不存在！
                $one_manager_info = $manager_user_list[$item['manager_id']] ?? [];
                if (empty($one_manager_info)) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_011');
                }

                //工号不应该为子账号！
                if ($one_manager_info && $one_manager_info['is_sub_staff'] != StaffInfoEnums::IS_SUB_STAFF_NO) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_012');
                }

                //判断员工是否为正式员工，不包含个人代理，如果不是，则在错误信息中增加“员工非正式员工！”
                if ($one_manager_info && ($one_manager_info['formal'] != StaffInfoEnums::FORMAL_IN || in_array($one_manager_info['hire_type'], [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT]))) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_013');
                }

                //员工非在职！
                if ($one_manager_info && $one_manager_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                    $error_msg[] = static::$t->_('material_wms_plan_import_store_014');
                }
            }

            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                $error_num ++;
                $result_data[$key][2] = static::$t->_('error_message_title') . '，' . implode('', $error_msg);
            } else {
                $data[] = [
                    'sys_store_id' => $item['sys_store_id'],
                    'sys_store_name' => $one_store_info['name'],
                    'manager_id' => $item['manager_id'],
                    'manager_name' => $one_manager_info['name']
                ];
            }
        }
        $count = count($excel_data);
        $res = [
            'all_num' => $count,
            'success_num' => $count - $error_num,
            'failed_num' => $error_num,
            'tasking_exist' => false,
            'url' => '',
            'data' => []
        ];

        if ($error_num) {
            $res['url'] = $this->exportExcel($excel_header_column, $result_data)['data'];
        } else {
            $res['data'] = $data;
        }
        return ['code' => ErrCode::$SUCCESS, 'message' => '', 'data' => $res];
    }


    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'sys_store_id',//网点编码
            'manager_id',//盘点负责人工号
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                $data[$line][$key] = trim($info[$index]);
            }
        }
        return $data;
    }

    /**
     * 网点包材数据统计
     * @param string $date 指定日期，若未指定则为前一天日期(脚本0点01分执行)
     * @param integer $batch_number 分批次入库数量
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    public function packageStock($date, $batch_number)
    {
        $log = '统计日期为：' . $date . PHP_EOL;
        try {
            //第一步：指定网点类型配置
            $category = EnumsService::getInstance()->getSettingEnvValueIds('material_package_stock_store_categorys');
            if (empty($category)) {
                throw new ValidationException('指定网点类型配置缺失，请联系产品配置', ErrCode::$VALIDATE_ERROR);
            }

            //第二步：判断统计日期是否已被统计过，勿重复操作
            $statistic_info = MaterialPackageStockModel::findFirst([
                'columns'    => 'statistical_date',
                'conditions' => 'statistical_date = :statistical_date:',
                'bind'       => ['statistical_date' => $date],
            ]);
            if (!empty($statistic_info)) {
                throw new ValidationException('统计日期 ' . $date . '，已被汇总，请勿重复汇总', ErrCode::$VALIDATE_ERROR);
            }

            //第三步：获取符合条件的网点(指定网点类型、激活、非虚拟网点、网点类型非FH、SHOP、USHOP时，网点营业状态为营业)
            $store_repository  = new StoreRepository();
            $virtual_store_ids = $store_repository->getVirtualStoreIds();//虚拟网点组
            $store_list        = $store_repository->searchSysStore([
                'category'         => $category,
                'state'            => Enums::STORE_STATE_ACTIVE,
                'not_in_store_ids' => $virtual_store_ids,
                'not_in_category'  => [
                    Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY,
                    Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY,
                    Enums::STORE_CATEGORY_FH,
                    Enums::STORE_CATEGORY_USHOP,
                ],
            ], 'id, name, category, manage_region, manage_piece');
            $log .= '捞取到符合条件网点总量是：' . count($store_list) . PHP_EOL;

            //第四步：获取符合条件的包材(非删除、启用的)
            $package_sku_list = MaterialPackageSkuModel::find([
                'conditions' => 'status = :status: and is_deleted = :is_deleted:',
                'bind'       => ['status'     => InventoryCheckEnums::PLAN_MATERIAL_PACKAGE_SKU_STATUS,
                                 'is_deleted' => GlobalEnums::IS_NO_DELETED,
                ],
            ])->toArray();
            $log              .= '捞取到符合条件包材总量是：' . count($package_sku_list) . PHP_EOL;

            //第五步：获取网点、barcode分组后的每一项初始库存（结余库存）
            $yesterday_stock_list       = MaterialPackageStockModel::find([
                'columns'    => 'store_id, barcode, surplus_number',
                'conditions' => 'statistical_date = :statistical_date:',
                'bind'       => ['statistical_date' => date('Y-m-d', strtotime('-1 day', strtotime($date)))],
            ])->toArray();
            $yesterday_group_stock_list = [];
            foreach ($yesterday_stock_list as $item) {
                $group_key                              = $item['store_id'] . '_' . $item['barcode'];
                $yesterday_group_stock_list[$group_key] = $item['surplus_number'];
            }
            $log .= '捞取到有初始库存的网点、barcode数据为：' . json_encode($yesterday_group_stock_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //第六步：获取网点、barcode分组后的今日入库数量 = 获取耗材出库单关联出库详情中（本次新增的根据物流单号查询的明细）的中妥投日期等于当前日期-1、耗材使用网点等于对应网点，对应Barcode的商品数量的和 + 包材退回单中管理员确认时间等于传递日期、已确认确认数量、退还网点 + 耗材调拨单中调入日期等于当前日期-1、调入网点等于对应网点、对应Barcode的调拨数量的和
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.use_store_id, product.barcode, product.product_num');
            $builder->from(['main' => MaterialWmsOutStorageModel::class]);
            $builder->leftJoin(MaterialWmsOutStorageBoxModel::class, 'box.wms_id = main.id', 'box');
            $builder->leftJoin(MaterialWmsOutStorageBoxProductModel::class, 'product.box_id = box.id', 'product');
            $builder->where('box.delivery_date = :delivery_date: and main.use_store_id != :use_store_id: and main.status = :status: ',
                [
                    'delivery_date' => $date,
                    'use_store_id'  => Enums::HEAD_OFFICE_STORE_FLAG,
                    'status'        => MaterialWmsEnums::STATUS_OUT,
                ]);
            $wms_out_storage_list   = $builder->getQuery()->execute()->toArray();
            $group_out_storage_list = [];
            foreach ($wms_out_storage_list as $item) {
                $group_key = $item['use_store_id'] . '_' . $item['barcode'];
                if (isset($group_out_storage_list[$group_key])) {
                    $group_out_storage_list[$group_key] += $item['product_num'];
                } else {
                    $group_out_storage_list[$group_key] = $item['product_num'];
                }
            }
            $log .= '捞取到有耗材实际出库数量的网点、barcode数据为：' . json_encode($group_out_storage_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //包材退回单中管理员确认时间等于传递日期、已确认确认数量、退还网点
            $confirm_at_end = date('Y-m-d 00:00:00', strtotime('+1 day', strtotime($date)));
            $builder        = $this->modelsManager->createBuilder();
            $builder->columns('main.return_store_id, sku.barcode, sku.confirm_num');
            $builder->from(['main' => MaterialPackageReturnOrderModel::class]);
            $builder->leftJoin(MaterialPackageReturnOrderSkuModel::class, 'sku.order_id = main.id', 'sku');
            $builder->where('main.return_store_id != :return_store_id: and main.status = :status: and main.confirm_at >= :confirm_at_start: and main.confirm_at < :confirm_at_end: ',
                [
                    'return_store_id'  => Enums::HEAD_OFFICE_STORE_FLAG,
                    'status'           => 2,
                    'confirm_at_start' => $date . ' 00:00:00',
                    'confirm_at_end'   => $confirm_at_end,
                ]);
            $package_return_list       = $builder->getQuery()->execute()->toArray();
            $package_group_return_list = [];
            foreach ($package_return_list as $item) {
                $group_key = $item['return_store_id'] . '_' . $item['barcode'];
                if (isset($package_group_return_list[$group_key])) {
                    $package_group_return_list[$group_key] += $item['confirm_num'];
                } else {
                    $package_group_return_list[$group_key] = $item['confirm_num'];
                }
            }
            $log .= '捞取到有包材退回确认数量的网点、barcode数据为：' . json_encode($package_group_return_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //耗材调拨单中调入日期等于当前日期-1、调入网点等于对应网点、对应Barcode的调拨数量的和
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.in_sys_store_id, sku.barcode, sku.out_number');
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder->leftJoin(MaterialPackageAllotSkuModel::class, 'sku.allot_id = main.id', 'sku');
            $builder->where('main.in_date = :in_date: and main.status = :status: ',
                ['in_date' => $date, 'status' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE]);
            $package_allot_in_list       = $builder->getQuery()->execute()->toArray();
            $group_package_allot_in_list = [];
            foreach ($package_allot_in_list as $item) {
                $group_key = $item['in_sys_store_id'] . '_' . $item['barcode'];
                if (isset($group_package_allot_in_list[$group_key])) {
                    $group_package_allot_in_list[$group_key] += $item['out_number'];
                } else {
                    $group_package_allot_in_list[$group_key] = $item['out_number'];
                }
            }
            $log .= '捞取到耗材调拨单入库的网点、barcode数据为：' . json_encode($group_package_allot_in_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;


            //第七步：获取网点、barcode分组后的今日出库数量 = 包材领用单中管理员确认时间等于传递日期、已确认确认数量 + bs系统中售出日期等于当前日期-1、售出网点等于对应网点、对应barcode的售出数量的和 + 调拨单中实际调出日期等于当前日期-1、调出网点等于当前网点对应Barcode的调拨数量的和
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.store_id, sku.barcode, sku.confirm_num');
            $builder->from(['main' => MaterialPackageOrderModel::class]);
            $builder->leftJoin(MaterialPackageOrderSkuModel::class, 'sku.order_id = main.id', 'sku');
            $builder->where('main.store_id != :store_id: and main.status = :status: and main.confirm_at >= :confirm_at_start: and main.confirm_at < :confirm_at_end: ',
                [
                    'store_id'         => Enums::HEAD_OFFICE_STORE_FLAG,
                    'status'           => 2,
                    'confirm_at_start' => $date . ' 00:00:00',
                    'confirm_at_end'   => $confirm_at_end,
                ]);
            $package_apply_list       = $builder->getQuery()->execute()->toArray();
            $package_group_apply_list = [];
            foreach ($package_apply_list as $item) {
                $group_key = $item['store_id'] . '_' . $item['barcode'];
                if (isset($package_group_apply_list[$group_key])) {
                    $package_group_apply_list[$group_key] += $item['confirm_num'];
                } else {
                    $package_group_apply_list[$group_key] = $item['confirm_num'];
                }
            }
            $log .= '捞取到有包材领用确认数量的网点、barcode数据为：' . json_encode($package_group_apply_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //bs系统中售出日期等于当前日期-1、售出网点等于对应网点、对应barcode的售出数量的和
            $group_bs_sale_list = $this->getBsSaleList($date);
            $log .= '捞取到bs系统中售出网点、barcode数据为：' . json_encode($group_bs_sale_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;


            //调拨单中实际调出日期等于当前日期-1、调出网点等于当前网点对应Barcode的调拨数量的和
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('main.out_sys_store_id, sku.barcode, sku.out_number');
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder->leftJoin(MaterialPackageAllotSkuModel::class, 'sku.allot_id = main.id', 'sku');
            $builder->where('main.real_out_date = :real_out_date: and main.status in({status:array})', [
                'real_out_date' => $date,
                'status'        => [
                    MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN,
                    MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE,
                ],
            ]);
            $package_allot_out_list = $builder->getQuery()->execute()->toArray();
            $group_package_allot_out_list = [];
            foreach ($package_allot_out_list as $item) {
                $group_key = $item['out_sys_store_id'] . '_' . $item['barcode'];
                if (isset($group_package_allot_out_list[$group_key])) {
                    $group_package_allot_out_list[$group_key] += $item['out_number'];
                } else {
                    $group_package_allot_out_list[$group_key] = $item['out_number'];
                }
            }
            $log .= '捞取到耗材调拨单出库的网点、barcode数据为：' . json_encode($group_package_allot_out_list, JSON_UNESCAPED_UNICODE) . PHP_EOL;

            //第八步：按照网点、barcode分组组装网点包材库存数据统计-入库、出库及库存情况数据组
            $material_package_stock_data = [];
            $now = date('Y-m-d H:i:s');
            foreach ($store_list as $store) {
                foreach ($package_sku_list as $barcode) {
                    $group_key = $store['id'] . '_' . $barcode['barcode'];
                    //初始库存 = 统计日期前一天的汇总数据的结余库存数量
                    $init_number = isset($yesterday_group_stock_list[$group_key]) ? $yesterday_group_stock_list[$group_key] : 0;
                    //今日入库数量 = 耗材出库单实际出库数量 + 包材退回确认数量 + 耗材调拨调入数量
                    $today_store_number = (isset($group_out_storage_list[$group_key]) ? $group_out_storage_list[$group_key] : 0) + (isset($package_group_return_list[$group_key]) ? $package_group_return_list[$group_key] : 0) + (isset($group_package_allot_in_list[$group_key]) ? $group_package_allot_in_list[$group_key] : 0);
                    //今日出库数量 = 包材领用确认数量 + bs系统中售出数量 + 耗材调拨单出库数量
                    $today_outbound_number = (isset($package_group_apply_list[$group_key]) ? $package_group_apply_list[$group_key] : 0) + (isset($group_bs_sale_list[$group_key]) ? $group_bs_sale_list[$group_key] : 0) + (isset($group_package_allot_out_list[$group_key]) ? $group_package_allot_out_list[$group_key] : 0);
                    //结余库存数量 = 初始库存+今日入库-今日出库，如果数据为0则存储0，出现负数就存负数
                    $surplus_number = $init_number + $today_store_number - $today_outbound_number;

                    $material_package_stock_data[$group_key] = [
                        'statistical_date' => $date,
                        'store_id'         => $store['id'],
                        'store_name'       => $store['name'],
                        'category'         => $store['category'],
                        'manage_region'    => $store['manage_region'],
                        'manage_piece'     => $store['manage_piece'],
                        'barcode'          => $barcode['barcode'],
                        'goods_name_zh'    => $barcode['goods_name_zh'],
                        'goods_name_th'    => $barcode['goods_name_th'],
                        'goods_name_en'    => $barcode['goods_name_en'],
                        'unit'             => $barcode['unit'],
                        'init_number'      => $init_number,
                        'store_number'     => $today_store_number,    //今日入库数量
                        'outbound_number'  => $today_outbound_number, //今日出库数量
                        'surplus_number'   => $surplus_number,        //结余库存数量
                        'created_at'       => $now,
                        'updated_at'       => $now,
                    ];
                }
            }
            $log .= '统计汇总数据总量是：' . count($material_package_stock_data) . '；每' . $batch_number . '/批；开始分批次入库' . PHP_EOL;

            //第九步：分5000/批次入库，一整个事务
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $material_package_stock_chunk_list = array_chunk($material_package_stock_data, $batch_number);
            foreach ($material_package_stock_chunk_list as $page => $child) {
                $log .= '第' . ($page + 1) . '批；开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;

                $material_package_stock_model = new MaterialPackageStockModel();
                $bool                         = $material_package_stock_model->batch_insert($child);
                if ($bool === false) {
                    throw new BusinessException($log . '入库失败，数据为：' . json_encode($child, JSON_UNESCAPED_UNICODE) . ' ; 可能的原因是：' . get_data_object_error_msg($material_package_stock_model), ErrCode::$BUSINESS_ERROR);
                }

                $log .= '入库成功；结束时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
            }
            $db->commit();

            $log .= '网点包材数据统计任务处理完毕' . PHP_EOL;
        } catch (ValidationException $e) {
            throw $e;
        } catch (BusinessException $e) {
            if (isset($db)) {
                $db->rollback();
            }
            throw $e;
        } catch (\Exception $e) {
            if (isset($db)) {
                $db->rollback();
            }
            throw $e;
        }
        return $log;
    }

    /**
     * 网点包材数据统计-归档
     * @param integer $day 前多少天
     * @param integer $batch_number 分批次入库数量
     * @return string
     * @throws BusinessException
     * @throws ValidationException
     */
    public function packageStockArchive($day, $batch_number)
    {
        $end_statistical_date = date('Y-m-d', strtotime('-' . $day . ' day'));
        $log = '归档前 ' . $day . ' 天的数据，截止日期为：' . $end_statistical_date . PHP_EOL;
        try {
            //第一步：获取截止日期之前的数据
            $package_stock_list = MaterialPackageStockModel::find([
                'conditions' => 'statistical_date < :statistical_date:',
                'bind'       => ['statistical_date' => $end_statistical_date],
            ])->toArray();
            $log .= '获取到需要归档的数据总量是：' . count($package_stock_list). '；每' . $batch_number . '/批；开始分批次归档' . PHP_EOL;

            //第二步：分批次进行归档
            $now = date('Y-m-d H:i:s');
            foreach ($package_stock_list as &$item) {
                $item['archived_at'] = $now;
            }
            $material_package_stock_chunk_list =  array_chunk($package_stock_list, $batch_number);
            foreach ($material_package_stock_chunk_list as $page => $child) {
                $log .= '第' . ($page+1) . '批；归档入库 开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
                $material_package_stock_archive_model = new MaterialPackageStockArchiveModel();
                $bool = $material_package_stock_archive_model->batch_insert($child, 'db_arch_oa');
                if ($bool === false) {
                    throw new BusinessException($log . '归档失败，数据为：' . json_encode($child, JSON_UNESCAPED_UNICODE) . ' ; 可能的原因是：' . get_data_object_error_msg($material_package_stock_archive_model), ErrCode::$BUSINESS_ERROR);
                }
                $log .= '归档成功；归档入库结束时间: '. date('Y-m-d H:i:s') . PHP_EOL;

                //删除原表
                $log .= '第' . ($page+1) . '批；删除原统计数据 开始时间: ' . date('Y-m-d H:i:s') . PHP_EOL;
                $ids = array_column($child, 'id');
                $sql = 'DELETE FROM material_package_stock WHERE id in (' . implode(',', $ids) . ')';
                $bool = $this->getDI()->get('db_oa')->execute($sql);
                if ($bool === false) {
                    throw new BusinessException($log . '删除失败，数据为：' . json_encode($ids, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
                $log .= '删除成功；结束时间: '. date('Y-m-d H:i:s') . PHP_EOL;
            }
            $log .= '网点包材数据统计-归档任务处理完毕' . PHP_EOL;
        } catch (BusinessException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw $e;
        }
        return $log;
    }

    /**
     * bs系统中售出日期等于当前日期-1、售出网点等于对应网点、对应barcode的售出数量的和
     * @param string $statistical_date 统计日期
     * @return array
     */
    public function getBsSaleList($statistical_date)
    {
        $group_bs_sale_list = [];

        if (empty($statistical_date)) {
            return $group_bs_sale_list;
        }
        //获取BS系统物料类型与包材映射关系
        $material_wms_package_bs_map = EnumsService::getInstance()->getSettingEnvValueMap('material_wms_package_bs_map');
        if (empty($material_wms_package_bs_map)) {
            return $group_bs_sale_list;
        }

        //BS系统-网点包材统计
        $list = StoreMaterialStatisticsModel::find([
            'columns'    => 'total, store_id, material_category ',
            'conditions' => 'business_date = :business_date:',
            'bind'       => ['business_date' => $statistical_date],
        ])->toArray();
        if(empty($list)) {
            return $group_bs_sale_list;
        }

        foreach ($list as $item) {
            //未配置物料类型映射的无需统计
            if (!isset($material_wms_package_bs_map[$item['material_category']])) {
                continue;
            }
            $barcode = $material_wms_package_bs_map[$item['material_category']];
            $group_key = $item['store_id'] . '_' . $barcode;
            if (isset($group_bs_sale_list[$group_key])) {
                $group_bs_sale_list[$group_key] += $item['total'];
            } else {
                $group_bs_sale_list[$group_key] = $item['total'];
            }
        }

        return $group_bs_sale_list;
    }
}
