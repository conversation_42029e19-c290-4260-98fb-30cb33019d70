<?php
namespace App\Modules\Material\Services;

use App\Library\ApiClient;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RestClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\MaterialPackageAllotExpressModel;
use App\Models\oa\MaterialPackageAllotModel;
use App\Models\oa\MaterialPackageAllotSkuModel;
use App\Models\oa\MaterialPackageStockModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StaffLangService;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialPackageSkuModel;
use App\Repository\backyard\PieceRepository;
use App\Repository\backyard\RegionRepository;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class WmsAllotService extends BaseService
{
    //耗材调拨单-待处理/已处理-列表
    const LIST_FOR_BY_WAIT_HANDLE = 1;//待处理
    const LIST_FOR_BY_HANDLED = 2;//已处理
    private static $instance;

    private function __construct()
    {

    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 创建
     * @var array
     */
    public static $validate_add = [
        'no'                   => 'Required|StrLenGeLe:1,20', //调拨编号
        'apply_date'           => 'Required|Date', //创建日期
        'apply_id'             => 'Required|IntGt:0', //创建人工号,
        'apply_name'           => 'Required|StrLenGeLe:1,255', //创建人姓名
        'node_department_id'   => 'Required|IntGt:0', //创建人所属部门ID
        'node_department_name' => 'Required|StrLenGeLe:1,50', //创建人所属部门名称
        'sys_department_id'    => 'Required|IntGt:0', //创建人一级部门ID
        'sys_department_name'  => 'Required|StrLenGeLe:1,50', //创建人一级部门名称
        'out_sys_store_id'     => 'Required|StrLenGeLe:1,10', //调出网点编码
        'out_store_name'       => 'Required|StrLenGeLe:1,50', //调出网点名称
        'out_store_category'   => 'Required|IntGt:0', //调出网点类型
        'in_sys_store_id'      => 'Required|StrLenGeLe:1,10', //调入网点编码
        'in_store_name'        => 'Required|StrLenGeLe:1,50', //调入网点名称
        'in_store_category'    => 'Required|IntGt:0', //调入网点类型
        'linear_distance'      => 'Required',//网点之间直线距离
        'delivery_way'         => 'Required|IntIn:' . MaterialWmsEnums::VALIDATE_MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY, //配送方式: 1快递配送、2 自行配送
        'plan_out_date'        => 'Required|Date', //计划调出日期
        'reason_type'          => 'Required|IntIn:' . MaterialWmsEnums::VALIDATE_MATERIAL_PACKAGE_ALLOT_REASON_TYPE, //调拨理由：1自用、2送客户
        'ka_account'           => 'IfIntEq:reason_type,' . MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER . '|Required|StrLenGeLe:1,50', //ka账户
        'ka_name'              => 'IfIntEq:reason_type,' . MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER . '|Required|StrLenGeLe:1,500', //ka账户名称
        'remark'               => 'Required|StrLenGeLe:1,500', //调拨说明

        //附件信息
        'attachments'                => 'Arr|ArrLenGeLe:0,20',
        'attachments[*]'             => 'Obj',
        'attachments[*].bucket_name' => 'StrLenGeLe:1,63',
        'attachments[*].object_key'  => 'StrLenGeLe:1,100',
        'attachments[*].file_name'   => 'StrLenGeLe:1,200',

        //调拨信息
        'sku'                   => 'Required|Arr|ArrLenGe:1',
        'sku[*]'                => 'Required|Obj',
        'sku[*].sku_id'         => 'Required|IntGt:0', //包材sku表ID
        'sku[*].barcode'        => 'Required|StrLenGeLe:1,30', //barcode
        'sku[*].category'       => 'Required|IntGt:0', //分类ID
        'sku[*].goods_name_zh'  => 'Required|StrLenGeLe:1,100', //包材中文名称
        'sku[*].goods_name_th'  => 'Required|StrLenGeLe:1,100', //包材泰文名称
        'sku[*].goods_name_en'  => 'Required|StrLenGeLe:1,100', //包材英文名称
        'sku[*].unit'           => 'Required|StrLenGeLe:0,50', //单位
        'sku[*].specs_model'    => 'Required|StrLenGeLe:0,255', //规格型号
        'sku[*].image_path'     => 'Required|StrLenGeLe:0,255', //图片
        'sku[*].surplus_number' => 'Required|IntGe:1', //库存数量
        'sku[*].out_number'     => 'Required|IntGe:1',//调出数量
    ];

    /**
     * 网点之间直线距离
     * @var array
     */
    public static $validate_linear_distance = [
        'out_sys_store_id'     => 'Required|StrLenGeLe:1,10', //调出网点编码
        'in_sys_store_id'      => 'Required|StrLenGeLe:1,10', //调入网点编码
    ];

    /**
     * 耗材列表
     * @var array
     */
    public static $validate_get_sku_list = [
        'out_sys_store_id' => 'Required|StrLenGeLe:1,10', //调出网点编码
        'barcode'          => 'StrLenGeLe:0,30',          //barcode
        'goods_name'       => 'StrLenGeLe:0,100',         //名称
    ];

    /**
     * ID
     * @var array
     */
    public static $validate_id = [
        'id' => 'Required|IntGt:0',
    ];

    /**
     * 用户
     * @var array
     */
    public static $validate_user = [
        'user_id'   => 'Required|IntGt:0',
        'user_name' => 'Required|StrLenGeLe:1,255',
    ];

    /**
     * 运单号
     * @var array
     */
    public static $validate_express_sn = [
        'express_sn' => 'Required|StrLenGeLe:1,50',
    ];

    /**
     * ka账户
     * @var array
     */
    public static $validate_ka = [
        'ka_account'        => 'Required|StrLenGeLe:1,50',
        'sys_department_id' => 'Required|IntGt:0',
    ];

    /**
     * 取消
     * @var array
     */
    public static $validate_cancel = [
        'id'            => 'Required|IntGt:0',
        'cancel_reason' => 'Required|StrLenGeLe:1,500',//取消原因
    ];

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'status',
        'plan_out_date_start',
        'plan_out_date_end',
        'real_out_date_start',
        'real_out_date_end',
        'in_date_start',
        'in_date_end',
        'created_at_start',
        'created_at_end',
        'reason_type',
        'timeout_out',
        'flag',
        'pageSize',
        'pageNum',
    ];

    /**
     * 列表
     * @var array
     */
    public static $validate_list = [
        'no'                  => 'StrLenGeLe:0,20', //调拨编号
        'status'              => 'Arr',             //调拨单状态
        'status[*]'           => 'IntGt:0',
        'out_sys_store_id'    => 'StrLenGeLe:0,10', //调出网点编码
        'in_sys_store_id'     => 'StrLenGeLe:0,10', //调入网点编码
        'plan_out_date_start' => 'Date',            //计划调出日期-起始
        'plan_out_date_end'   => 'Date',            //计划调出日期-截止
        'real_out_date_start' => 'Date',            //实际调出日期-起始
        'real_out_date_end'   => 'Date',            //实际调出日期-截止
        'in_date_start'       => 'Date',            //调入日期-起始
        'in_date_end'         => 'Date',            //调入日期-截止
        'created_at_start'    => 'Date',            //创建时间-起始
        'created_at_end'      => 'Date',            //创建时间-截止
        'reason_type'         => 'IntGt:0',         //调拨理由
        'timeout_out'         => 'IntGt:0',         //超时未调出
        'pageSize'            => 'IntGt:0',         //每页条数
        'pageNum'             => 'IntGt:0',         //当前页码
    ];

    /**
     * 耗材调拨单-耗材调拨待处理数 - 小红点数 - For - By
     * @var array
     */
    public static $validate_by_wait_handle_hot = [
        'staff_id'     => 'Required|IntGt:0',
        'sys_store_id' => 'Required|StrLenGeLe:1,10',
    ];

    /**
     *  耗材调拨单-待处理/已处理 - 列表 - For - By
     * @var array
     */
    public static $validate_by_list = [
        'staff_id'            => 'Required|IntGt:0',
        'flag'                => 'IntIn:' . self::LIST_FOR_BY_WAIT_HANDLE . ',' . self::LIST_FOR_BY_HANDLED,//列表类型 1待处理、2已处理，不传递默认待处理
        'status'              => 'Arr',             //调拨单状态
        'status[*]'           => 'IntGt:0',
        'real_out_date_start' => 'Date',            //实际调出日期-起始
        'real_out_date_end'   => 'Date',            //实际调出日期-截止
        'in_date_start'       => 'Date',            //调入日期-起始
        'in_date_end'         => 'Date',            //调入日期-截止
    ];

    /**
     * 当前用户配置的网点类型权限
     * @param array $user 当前登陆者信息组
     * @return array|mixed
     */
    public function getStaffCategoryPermission(array $user)
    {
        $material_wms_allot_staff_category = EnumsService::getInstance()->getSettingEnvValueMap('material_wms_allot_staff_category');
        if (!empty($material_wms_allot_staff_category[$user['id']])) {
           return explode(',', $material_wms_allot_staff_category[$user['id']]);
        }
        return [];
    }

    /**
     * 获取管辖的网点
     * @param int $staff_info_id 员工工号
     * @return array
     */
    public function getStaffManagerStoreIds(int $staff_info_id)
    {
        $manage_store_list = (new StoreRepository())->getListByManagerId($staff_info_id);
        return array_filter(array_column($manage_store_list, 'store_id'));
    }

    /**
     * 枚举
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getOptionsDefault(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            //调拨状态、调出理由、配送方式、超时未调出
            $enums_arr = [
                'status'       => MaterialWmsEnums::$package_allot_status,
                'reason_type'  => MaterialWmsEnums::$package_allot_reason_type,
                'delivery_way' => MaterialWmsEnums::$package_allot_delivery_way,
                'timeout_out'  => MaterialWmsEnums::$package_allot_timeout_out
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v),
                    ];
                }
            }
            //当前用户配置的网点类型权限
            $data['material_wms_allot_staff_category'] = $this->getStaffCategoryPermission($user);
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单-枚举:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 添加默认配置项
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $data['no']                   = static::genSerialNo(MaterialWmsEnums::MATERIAL_PACKAGE_NO_PREFIX, RedisKey::MATERIAL_PACKAGE_ALLOT_COUNTER, 4);
            $data['apply_date']           = date('Y-m-d');
            $data['apply_id']             = $user['id'];
            $data['apply_name']           = $user['name'];
            $data['node_department_id']   = $user['node_department_id'];
            $data['node_department_name'] = $user['department'];
            $data['sys_department_id']    = $user['sys_department_id'];
            $sys_department_info          = (new DepartmentRepository())->getDepartmentDetail($data['sys_department_id'], 2);
            $data['sys_department_name']  = $sys_department_info['name'] ?? '';
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单- 添加默认配置项:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 验证数据提交合法性
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws ValidationException
     */
    private function validationSubmit(array $params, array $user)
    {
        //调出、调入网点不可一致
        if ($params['out_sys_store_id'] == $params['in_sys_store_id']) {
            throw new ValidationException(static::$t->_('material_package_allot_store_repeat'), ErrCode::$VALIDATE_ERROR);
        }

        //计划调出日期选择范围为大于等于当天的日期
        if ($params['plan_out_date'] < date('Y-m-d')) {
            throw new ValidationException(static::$t->_('material_package_allot_plan_out_date_error'), ErrCode::$VALIDATE_ERROR);
        }

        //调拨耗材的barcode不允许重复
        $repeat_count = count(array_unique(array_column($params['sku'], 'barcode')));
        if ($repeat_count != count($params['sku'])) {
            throw new ValidationException(static::$t->_('material_package_allot_sku_repeat'), ErrCode::$VALIDATE_ERROR);
        }
        foreach ($params['sku'] as $item) {
            //调出数量不能大于库存数量
            if ($item['out_number'] > $item['surplus_number']) {
                throw new ValidationException(static::$t->_('material_package_allot_out_number_error'), ErrCode::$VALIDATE_ERROR);
            }
        }

        //当前用户没有配置网点类型权限,不可提交
        $category = $this->getStaffCategoryPermission($user);
        if (empty($category)) {
            throw new ValidationException(static::$t->_('material_package_allot_user_category_empty'), ErrCode::$VALIDATE_ERROR);
        }

        //非配置网点类型权限，不可提交
        if (!in_array($params['out_store_category'], $category) || !in_array($params['in_store_category'], $category)) {
            throw new ValidationException(static::$t->_('material_package_allot_category_error'), ErrCode::$VALIDATE_ERROR);
        }

        return $params;
    }

    /**
     * 创建
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //验证参数
            $params = $this->validationSubmit($params, $user);

            //调拨编号是否已存在
            $exits = MaterialPackageAllotModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
            ]);
            if (!empty($exits)) {
                throw new ValidationException(static::$t->_('material_package_allot_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');

            //网点耗材调拨单
            $material_package_allot = new MaterialPackageAllotModel();
            $add_data = [
                'no'                   => $params['no'],
                'apply_date'           => $params['apply_date'],
                'apply_id'             => $params['apply_id'],
                'apply_name'           => $params['apply_name'],
                'node_department_id'   => $params['node_department_id'],
                'node_department_name' => $params['node_department_name'],
                'sys_department_id'    => $params['sys_department_id'],
                'sys_department_name'  => $params['sys_department_name'],
                'out_sys_store_id'     => $params['out_sys_store_id'],
                'out_store_name'       => $params['out_store_name'],
                'out_store_category'   => $params['out_store_category'],
                'in_sys_store_id'      => $params['in_sys_store_id'],
                'in_store_name'        => $params['in_store_name'],
                'in_store_category'    => $params['in_store_category'],
                'linear_distance'      => $params['linear_distance'] === '' ? null : $params['linear_distance'],
                'delivery_way'         => $params['delivery_way'],
                'plan_out_date'        => $params['plan_out_date'],
                'real_out_date'        => null,
                'reason_type'          => $params['reason_type'],
                'ka_account'           => $params['reason_type'] == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER ? $params['ka_account'] : '',
                'ka_name'              => $params['reason_type'] == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_REASON_TYPE_CUSTOMER ? $params['ka_name'] : '',
                'remark'               => $params['remark'],
                'status'               => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT,
                'updated_id'           => $user['id'],
                'updated_name'         => $user['name'],
                'created_at'           => $now,
                'updated_at'           => $now,
            ];
            $bool = $material_package_allot->i_create($add_data);
            if ($bool === false) {
                throw new BusinessException('调拨单-创建失败，数据为：' . json_encode($add_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot), ErrCode::$BUSINESS_ERROR);
            }

            //网点耗材调拨详情
            $material_package_allot_sku = new MaterialPackageAllotSkuModel();
            $detail_data = [];
            foreach ($params['sku'] as $item) {
                $detail_data[] = [
                    'allot_id'       => $material_package_allot->id,
                    'sku_id'         => $item['sku_id'],
                    'barcode'        => $item['barcode'],
                    'category'       => $item['category'],
                    'goods_name_zh'  => $item['goods_name_zh'],
                    'goods_name_th'  => $item['goods_name_th'],
                    'goods_name_en'  => $item['goods_name_en'],
                    'unit'           => $item['unit'],
                    'specs_model'    => $item['specs_model'],
                    'image_path'     => $item['image_path'],
                    'surplus_number' => $item['surplus_number'],
                    'out_number'     => $item['out_number'],
                    'created_at'     => $now,
                ];
            }
            $bool = $material_package_allot_sku->batch_insert($detail_data);
            if ($bool === false) {
                throw new BusinessException('调拨单-创建-保存调拨详情失败，数据为：' . json_encode($detail_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot_sku), ErrCode::$BUSINESS_ERROR);
            }

            //附件
            if (!empty($params['attachments'])) {
                $attachment_arr = [];
                foreach ($params['attachments'] as $item) {
                    $attachment_arr[] = [
                        'oss_bucket_type' => MaterialEnums::OSS_MATERIAL_TYPE_PACKAGE_ALLOT,
                        'sub_type'        => 0,
                        'oss_bucket_key'  => $material_package_allot->id,
                        'bucket_name'     => $item['bucket_name'],
                        'object_key'      => $item['object_key'],
                        'file_name'       => $item['file_name'],
                        'deleted'         => GlobalEnums::IS_NO_DELETED,
                        'created_at'      => $now,
                        'object_url'      => $item['object_url'] ?? '',
                    ];
                }
                $material_attachment = new MaterialAttachmentModel();
                $bool = $material_attachment->batch_insert($attachment_arr);
                if ($bool === false) {
                    throw new BusinessException('调拨单-创建-保存附件失败，数据为：' . json_encode($attachment_arr, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_attachment), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();

            //同时给调出网点和调入网点的网点负责人(网点表中的负责人)发送BY消息-系统消息
            $this->sendMessage($material_package_allot);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-创建:' . $real_message . json_encode($data));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 发送站内信
     * @param object $material_package_allot 调拨单对象
     * @param string $type 发送类型 add 创建， cancel 取消
     */
    private function sendMessage(object $material_package_allot, string $type = 'add')
    {
        $store_list = (new StoreRepository())->getStoreListByIds([$material_package_allot->out_sys_store_id, $material_package_allot->in_sys_store_id], null);

        $out_store_info = $store_list[$material_package_allot->out_sys_store_id] ?? [];
        $in_store_info  = $store_list[$material_package_allot->in_sys_store_id] ?? [];

        //根据发送对象的上次登录语言发送固定的标题，如果获取不到上次登录语言，则泰国固定为泰语，其他国家为英文
        $country_code = get_country_code();
        $default_lang = $country_code == GlobalEnums::TH_COUNTRY_CODE ? 'th' : 'en';

        //调出网点负责人-发送耗材调出提醒、耗材调拨取消提醒
        if (!empty($out_store_info['manager_id'])) {
            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($out_store_info['manager_id'], $default_lang);
            $translation = $this->getTranslation($staff_language);
            $by_msg_param = [
                'staff_users'     => [['id' => $out_store_info['manager_id']]],
                'message_title'   => $translation->_($type == 'add' ? 'material_package_allot_out_notice' : 'material_package_allot_cancel_notice'),
                'message_content' => $material_package_allot->id,
                'category'        => $type == 'add' ? MaterialEnums::MATERIAL_PACKAGE_ALLOT_OUT_NOTICE : MaterialEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_NOTICE,
            ];
            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$by_msg_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                $this->logger->info('sendMessage 站内信 发送成功！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->notice('sendMessage 站内信 发送失败！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            }
        }

        //调入网点的负责人-发送耗材调入提醒、耗材调拨取消提醒
        if (!empty($in_store_info['manager_id'])) {
            $staff_language = StaffLangService::getInstance()->getLatestMobileLang($in_store_info['manager_id'], $default_lang);
            $translation = $this->getTranslation($staff_language);
            $by_msg_param = [
                'staff_users'     => [['id' => $in_store_info['manager_id']]],
                'message_title'   => $translation->_($type == 'add' ? 'material_package_allot_in_notice' : 'material_package_allot_cancel_notice'),
                'message_content' => $material_package_allot->id,
                'category'        => $type == 'add' ? MaterialEnums::MATERIAL_PACKAGE_ALLOT_IN_NOTICE : MaterialEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_NOTICE,
            ];
            $bi_rpc = (new ApiClient('hcm_rpc', '', 'add_kit_message'));
            $bi_rpc->setParams([$by_msg_param]);
            $res = $bi_rpc->execute();
            if (isset($res['result']['code']) && ($res['result']['code'] == ErrCode::$SUCCESS)) {
                $this->logger->info('sendMessage 站内信 发送成功！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            } else {
                $this->logger->notice('sendMessage 站内信 发送失败！' . json_encode($by_msg_param, JSON_UNESCAPED_UNICODE));
            }
        }
    }

    /**
     * 网点之间直线距离
     * @param array $params 请求参数组
     * @return array
     */
    public function getLinearDistance(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';
        try {
            //调出、调入网点不可一致
            if ($params['out_sys_store_id'] == $params['in_sys_store_id']) {
                throw new ValidationException(static::$t->_('material_package_allot_store_repeat'), ErrCode::$VALIDATE_ERROR);
            }

            $store_list     = (new StoreRepository())->getStoreListByIds([
                $params['out_sys_store_id'],
                $params['in_sys_store_id'],
            ]);
            $out_store_info = $store_list[$params['out_sys_store_id']] ?? [];
            $in_store_info  = $store_list[$params['in_sys_store_id']] ?? [];
            if (empty($out_store_info) || empty($in_store_info)) {
                throw new ValidationException(static::$t->_('material_package_allot_store_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($out_store_info['lat'] && $out_store_info['lng'] && $in_store_info['lat'] && $in_store_info['lng']) {
                $data = get_distance($out_store_info['lat'], $out_store_info['lng'], $in_store_info['lat'], $in_store_info['lng']);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单-网点之间直线距离:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 筛选ka账户
     * @param array $params 请求参数组
     * @return array
     * @throws GuzzleException
     */
    public function searchKa(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $params = [
                'kaId'         => $params['ka_account'],
                'departmentId' => $params['sys_department_id'],
            ];
            $api    = new RestClient('oms');
            $return = $api->execute(RestClient::METHOD_POST, '/svc/ka_profile/oa/query', $params, ['Accept-Language' => static::$language]);
            if (isset($return['code']) && $return['code'] == ErrCode::$SUCCESS && !empty($return['data'])) {
                $data = [
                    'ka_account' => $return['data']['kaId'],
                    'ka_name'    => $return['data']['kaName']
                ];
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-筛选ka账户失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data ? [$data] : [],
        ];
    }

    /**
     * 获取耗材名称
     * @return string
     */
    public function getGoodsName()
    {
        return get_lang_field_name('goods_name_', static::$language, 'en', 'zh');
    }

    /**
     * 耗材列表
     * @param array $params 请求参数组
     * @return array
     */
    public function getSkuList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';
        try {
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('sku.id as sku_id,sku.barcode,sku.category,sku.goods_name_zh,sku.goods_name_th,sku.goods_name_en,sku.specs_model,sku.unit,sku.image_path,stock.surplus_number');
            $builder->from(['sku' => MaterialPackageSkuModel::class]);
            $builder->leftjoin(MaterialPackageStockModel::class, 'stock.barcode = sku.barcode AND stock.store_id = "' . $params['out_sys_store_id'] . '" AND stock.statistical_date = "' . $yesterday . '"', 'stock');
            $builder->where('sku.is_deleted = :is_deleted: and sku.status = :status:', ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'status' => MaterialClassifyEnums::MATERIAL_START_USING]);
            if (!empty($params['barcode'])) {
                $builder->andWhere('sku.barcode = :barcode:', ['barcode' => $params['barcode']]);
            }
            if (!empty($params['goods_name'])) {
                $builder->andWhere('sku.'. $this->getGoodsName() .' like :name:', ['name' => '%' . $params['goods_name'] . '%']);
            }
            $builder->orderBy('sku.barcode ASC');
            $data = $builder->getQuery()->execute()->toArray();
            foreach ($data as &$item) {
                $item['surplus_number'] = is_null($item['surplus_number']) ? 0 : $item['surplus_number'];
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单-耗材列表:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取调拨单信息
     * @param int $id 调拨单ID
     * @return mixed
     * @throws ValidationException
     */
    public function getPackageAllotInfo(int $id)
    {
        $material_package_allot = MaterialPackageAllotModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $id],
        ]);
        if (empty($material_package_allot)) {
            throw new ValidationException(static::$t->_('material_package_allot_no_found'), ErrCode::$VALIDATE_ERROR);
        }
        return $material_package_allot;
    }

    /**
     * 耗材调拨单-站内信详情
     * @param int $id 调拨单ID
     * @return array
     */
    public function getPackageAllotMsgInfo(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $material_package_allot = $this->getPackageAllotInfo($id);
            $data                   = [
                'id'            => $material_package_allot->id,
                'no'            => $material_package_allot->no,
                'plan_out_date' => $material_package_allot->plan_out_date,
            ];
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('调拨单-站内信详情:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 取消/BY-无法调出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $material_package_allot = $this->getPackageAllotInfo($params['id']);
            if ($material_package_allot->status != MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT) {
                throw new ValidationException(static::$t->_('material_package_allot_out_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $cancel_source = $params['cancel_source'] ?? MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_OA;
            $update_data = [
                'cancel_reason' => $params['cancel_reason'],
                'cancel_source' => $cancel_source,
                'status'        => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL,
                'updated_id'    => $user['id'],
                'updated_name'  => $user['name'],
                'updated_at'    => date('Y-m-d H:i:s')
            ];

            //BY-无法调出
            if ($cancel_source == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_BY) {
                //将调拨状态更新为已取消，当前操作人记录为调出确认人，取消原因为填写的无法调出说明
                $update_data['out_confirm_staff_id']   = $user['id'];
                $update_data['out_confirm_staff_name'] = $user['name'];
            }
            $bool = $material_package_allot->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('调拨单-取消失败，数据为：' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot), ErrCode::$BUSINESS_ERROR);
            }

            //OA-取消-发送耗材调拨取消提醒
            if ($cancel_source == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_CANCEL_REASON_OA) {
                $this->sendMessage($material_package_allot, 'cancel');
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-取消失败:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 查看
     * @param array $params 请求参数组
     * @return array
     */
    public function detail(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $material_package_allot       = $this->getPackageAllotInfo($params['id']);
            $data                         = $material_package_allot->toArray();
            $data['linear_distance']      = is_null($data['linear_distance']) ? '' : $data['linear_distance'];
            $data['delivery_way_text']    = static::$t->_(MaterialWmsEnums::$package_allot_delivery_way[$data['delivery_way']]);
            $data['status_text']          = static::$t->_(MaterialWmsEnums::$package_allot_status[$data['status']]);
            $data['real_out_date']        = $data['real_out_date'] ? $data['real_out_date'] : '';
            $data['in_date']              = $data['in_date'] ? $data['in_date'] : '';
            $data['out_confirm_staff_id'] = $data['out_confirm_staff_id'] ? $data['out_confirm_staff_id'] : '';
            $data['in_confirm_staff_id']  = $data['in_confirm_staff_id'] ? $data['in_confirm_staff_id'] : '';
            $data['reason_type_text']     = static::$t->_(MaterialWmsEnums::$package_allot_reason_type[$data['reason_type']]);

            $data['attachments'] = $material_package_allot->getAttachments()->toArray();

            $data['sku'] = $material_package_allot->getSku()->toArray();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-查看失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 查看 - 调拨信息 - 导出
     * @param array $params 请求参数组
     * @return array
     * @throws GuzzleException
     */
    public function exportSku(array  $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            // 获取数据-Barcode、耗材名称、规格型号、基本单位、调出网点库存数量、调出数量
            $items = MaterialPackageAllotSkuModel::find([
                'columns'    => 'barcode,' . $this->getGoodsName() . ' as goods_name, specs_model,unit,surplus_number,out_number',
                'conditions' => 'allot_id = :allot_id:',
                'bind'       => ['allot_id' => $params['id']],
                'order'      => 'barcode ASC',
            ])->toArray();
            $excel_data = [];
            foreach ($items as $item) {
                $excel_data[] = [
                    $item['barcode'],
                    $item['goods_name'],
                    $item['specs_model'],
                    $item['unit'],
                    $item['surplus_number'],
                    $item['out_number'],
                ];
            }

            // 获取表头
            $header = [
                static::$t->_('material_package_allot.barcode'),
                static::$t->_('material_package_allot.goods_name'),
                static::$t->_('material_package_allot.specs_model'),
                static::$t->_('material_package_allot.unit'),
                static::$t->_('material_package_allot.surplus_number'),
                static::$t->_('material_package_allot.out_number'),
            ];

            // 生成Excel
            $file_name = 'material_wms_package_allot_sku_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('调拨单-调拨信息-导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 查看 - 查看物流
     * @param array $params 请求参数组
     * @return array
     */
    public function getExpressList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $material_package_allot = $this->getPackageAllotInfo($params['id']);
            //状态为待调入或已完成并且配送方式为快递配送时显示
            if (in_array($material_package_allot->status, [MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN, MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE]) && $material_package_allot->delivery_way == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS) {
                $data = $material_package_allot->getExpress()->toArray();
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-查看物流失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 查看 - 查看路由
     * @param array $params 请求参数组
     * @return array
     * @throws GuzzleException
     */
    public function getExpressRoute(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $params = [
                'pno'           => $params['express_sn'],
                'local'         => static::$language,
                'multipleRoute' => true,
                'thirdRoute'    => true,
            ];
            $api = new RestClient('nws');
            $return = $api->execute(RestClient::METHOD_POST, '/svc/parcel/route/internal_all', $params, ['Accept-Language' => static::$language]);
            if (isset($return['code']) && $return['code'] == ErrCode::$SUCCESS) {
                $data = $return['data'];
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-查看路由失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getList(array $params, array $user)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('COUNT(main.id) as count');
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder = $this->getCondition($builder, $params, $user);
            $count   = intval($builder->getQuery()->getSingleResult()->count);

            if ($count) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id,main.no,main.status,main.out_sys_store_id,main.out_store_name,main.in_sys_store_id,main.in_store_name,main.plan_out_date,main.real_out_date,';
                $columns .= 'main.in_date,main.apply_id,main.apply_name,main.created_at,main.updated_id,main.updated_name,main.updated_at';
                $builder->columns($columns);
                $builder->from(['main' => MaterialPackageAllotModel::class]);
                $builder = $this->getCondition($builder, $params, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.created_at desc');
                $items         = $builder->getQuery()->execute()->toArray();
                $items         = $this->handleListItems($items);
                $data['items'] = $items ?? [];
            }
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            //无可查看的数据范围时无需提示，直接空列表返回，查看不到任何数据即可
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('调拨单-列表失败:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $params 查询条件
     * @param array $user 当前登陆者信息组
     * @return object
     * @throws ValidationException
     */
    public function getCondition(object $builder, array $params, array $user)
    {
        //列表数据范围 - 有符合条件的
        $permission_params = $this->getStaffSeePermission($user);
        if (empty($permission_params)) {
            throw new ValidationException('无可查看的数据范围', ErrCode::$VALIDATE_ERROR);
        }
        $params  = array_merge($params, $permission_params);

        $no                  = $params['no'] ?? '';                 //调拨编号
        $status              = $params['status'] ?? [];             //调拨单状态
        $out_sys_store_id    = $params['out_sys_store_id'] ?? '';   //调出网点编码
        $in_sys_store_id     = $params['in_sys_store_id'] ?? '';    //调入网点编码
        $plan_out_date_start = $params['plan_out_date_start'] ?? '';//计划调出日期-起始
        $plan_out_date_end   = $params['plan_out_date_end'] ?? '';  //计划调出日期-截止
        $real_out_date_start = $params['real_out_date_start'] ?? '';//实际调出日期-起始
        $real_out_date_end   = $params['real_out_date_end'] ?? '';  //实际调出日期-截止
        $in_date_start       = $params['in_date_start'] ?? '';      //调入日期-起始
        $in_date_end         = $params['in_date_end'] ?? '';        //调入日期-截止
        $created_at_start    = $params['created_at_start'] ?? '';   //创建时间-起始
        $created_at_end      = $params['created_at_end'] ?? '';     //创建时间-截止
        $reason_type         = $params['reason_type'] ?? 0;         //调拨理由
        $timeout_out         = $params['timeout_out'] ?? 0;         //超时未调出
        $category            = $params['category'] ?? [];           //查询调出网点类型或调入网点类型属于当前用户配置的网点类型的数据
        $store_ids           = $params['store_ids'] ?? [];          //查询调出网点或者调入网点属于当前用户管辖的大区或片区范围内的网点的数据
        if (!empty($no)) {
            $builder->andWhere('main.no = :no:', ['no' => $no]);
        }
        if (!empty($status)) {
            $builder->inWhere('main.status', $status);
        }
        if (!empty($out_sys_store_id)) {
            $builder->andWhere('main.out_sys_store_id = :out_sys_store_id:', ['out_sys_store_id' => $out_sys_store_id]);
        }
        if (!empty($in_sys_store_id)) {
            $builder->andWhere('main.in_sys_store_id = :in_sys_store_id:', ['in_sys_store_id' => $in_sys_store_id]);
        }
        if (!empty($plan_out_date_start)) {
            $builder->andWhere('main.plan_out_date >= :plan_out_date_start:', ['plan_out_date_start' => $plan_out_date_start]);
        }
        if (!empty($plan_out_date_end)) {
            $builder->andWhere('main.plan_out_date <= :plan_out_date_end:', ['plan_out_date_end' => $plan_out_date_end]);
        }
        if (!empty($real_out_date_start)) {
            $builder->andWhere('main.real_out_date >= :real_out_date_start:', ['real_out_date_start' => $real_out_date_start]);
        }
        if (!empty($real_out_date_end)) {
            $builder->andWhere('main.real_out_date <= :real_out_date_end:', ['real_out_date_end' => $real_out_date_end]);
        }
        if (!empty($in_date_start)) {
            $builder->andWhere('main.in_date >= :in_date_start:', ['in_date_start' => $in_date_start]);
        }
        if (!empty($in_date_end)) {
            $builder->andWhere('main.in_date <= :in_date_end:', ['in_date_end' => $in_date_end]);
        }
        if (!empty($created_at_start)) {
            $builder->andWhere('main.created_at >= :created_at_start:', ['created_at_start' => $created_at_start . ' 00:00:00']);
        }
        if (!empty($created_at_end)) {
            $builder->andWhere('main.created_at <= :created_at_end:', ['created_at_end' => $created_at_end . ' 23:59:59']);
        }
        if (!empty($reason_type)) {
            $builder->andWhere('main.reason_type = :reason_type:', ['reason_type' => $reason_type]);
        }

        //如果选择是则查询条件增加计划调出日期小于当前日期并且调拨状态等于待调出，如果选择否，则该条件不生效
        if ($timeout_out == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_TIMEOUT_YES) {
            $builder->andWhere('main.status = :status: and plan_out_date <= :plan_out_date:', ['status' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT, 'plan_out_date' => date('Y-m-d')]);
        }

        //查询调出网点类型或调入网点类型属于配置的网点类型的数据
        if (!empty($category)) {
            $builder->andWhere('main.out_store_category in ({category:array}) OR main.in_store_category in ({category:array})', ['category' => $category]);
        }

        //查询调出网点或者调入网点属于当前用户管辖的大区或片区范围内的网点的数据
        if (!empty($store_ids)) {
            $builder->andWhere('main.out_sys_store_id in ({store_ids:array}) OR main.in_sys_store_id in ({store_ids:array})', ['store_ids' => $store_ids]);
        }

        return $builder;
    }

    /**
     * 格式化列表
     * @param array $items 列表
     * @param bool $export 导出
     * @return array
     */
    private function handleListItems(array $items, bool $export = false)
    {
        if (empty($items)) {
            return [];
        }

        if ($export) {
            $rows = [];
            foreach ($items as $item) {
                $rows[] = [
                    $item['no'],
                    $item['apply_date'],
                    $item['apply_name'],
                    $item['apply_id'],
                    $item['node_department_name'],
                    $item['out_store_name'],
                    $item['out_sys_store_id'],
                    $item['in_store_name'],
                    $item['in_sys_store_id'],
                    is_null($item['linear_distance']) ? '' : $item['linear_distance'],
                    static::$t->_(MaterialWmsEnums::$package_allot_delivery_way[$item['delivery_way']]),
                    static::$t->_(MaterialWmsEnums::$package_allot_reason_type[$item['reason_type']]),
                    $item['ka_account'],
                    $item['remark'],
                    $item['plan_out_date'],
                    static::$t->_(MaterialWmsEnums::$package_allot_status[$item['status']]),
                    $item['real_out_date'] ? $item['real_out_date'] : '',
                    $item['in_date'] ? $item['in_date'] : '',
                    $item['out_confirm_staff_name'],
                    $item['out_confirm_staff_id'] ? $item['out_confirm_staff_id'] : '',
                    $item['in_confirm_staff_name'],
                    $item['in_confirm_staff_id'] ? $item['in_confirm_staff_id'] : '',
                    $item['cancel_reason'],
                    $item['barcode'],
                    $item['goods_name'],
                    $item['specs_model'],
                    $item['unit'],
                    $item['surplus_number'],
                    $item['out_number']
                ];
            }
            $items = $rows;
        } else {
            foreach ($items as &$item) {
                $item['status_text'] = static::$t->_(MaterialWmsEnums::$package_allot_status[$item['status']]);
                $item['real_out_date'] = $item['real_out_date'] ? $item['real_out_date'] : '';
                $item['in_date'] = $item['in_date'] ? $item['in_date'] : '';
            }
        }
        return $items;
    }

    /**
     * 获取当前登录用户可看数据范围
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getStaffSeePermission(array $user)
    {
        //数据范围逻辑如下(按照顺序执行，不需要取并集)
        //a.如果当前用户属于配置的工号，则可以查询调出网点类型或调入网点类型属于配置的网点类型的数据
        $category = $this->getStaffCategoryPermission($user);
        if ($category) {
            return ['category' => $category];
        }

        //b.如果不属于a，则判断当前负责人是否属于AM(取大区表的负责人)，如果属于AM则可查看调出网点或者调入网点属于管辖大区(可能有多个)的数据
        $store_ids = RegionRepository::getInstance()->getStoreListByManagerId($user['id']);
        if ($store_ids) {
            return ['store_ids' => array_column($store_ids, 'store_id')];
        }

        //c.如果不属于b，则判断当前负责人是否属于DM(取片区表的负责人)，如果属于DM则可查看调出网点或者调入网点属于管辖片区(可能有多个)的数据
        $store_ids = PieceRepository::getInstance()->getStoreListByManagerId($user['id']);
        if ($store_ids) {
            return ['store_ids' => array_column($store_ids, 'store_id')];
        }
        return [];
    }

    /**
     * 超时未调出调拨单数量
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getTimeoutOutNum(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        $data    = 0;

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('COUNT(main.id) as count');
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            //当存在计划调出日期小于当前日期并且调拨状态等于待调出的数据时显示，如果没有满足条件的数据则不显示
            $builder->where('main.status = :status: and main.plan_out_date < :plan_out_date:', ['status' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT, 'plan_out_date' => date('Y-m-d')]);
            $builder = $this->getCondition($builder, [], $user);
            $data    = intval($builder->getQuery()->getSingleResult()->count);
        } catch (ValidationException $e) {
            //无可查看的数据范围时无需提示，直接返回0，查看不到任何数据即可
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单-超时未调出调拨单数量:' . $e->getMessage() . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return mixed
     * @throws GuzzleException
     */
    public function export(array $params, array $user)
    {
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDataExportTotal($params, $user) > MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DOWNLOAD_LIMIT) {
            $result         = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], DownloadCenterEnum::MATERIAL_WMS_PACKAGE_ALLOT, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $result         = $this->getSyncExportData($params, $user);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url'      => $result['data'],
            ];
        }
        return $result;
    }

    /**
     * 获取导出数据的总量
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return mixed
     */
    public function getDataExportTotal(array $params, array $user)
    {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder->leftJoin(MaterialPackageAllotSkuModel::class, 'sku.allot_id = main.id', 'sku');
            $builder->columns('count(main.id) AS count');
            $builder = $this->getCondition($builder, $params, $user);
            return (int)$builder->getQuery()->getSingleResult()->count;
        } catch (ValidationException $e) {
            //无可查看的数据范围时，直接返回0
            return 0;
        } catch (Exception $e) {
            $this->logger->warning('调拨单-获取导出数据的总量失败：' . $e->getMessage());
        }
    }

    /**
     * 同步导出excel
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws GuzzleException
     */
    public function getSyncExportData(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            // 同步下载, 最多1w条
            $params['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $params['pageSize'] = MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DOWNLOAD_LIMIT;

            // 获取数据
            $excel_data = $this->getExportData($params, $user);

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = 'material_wms_package_allot_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('调拨单-数据同步导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出数据
     * @param array $params 参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function getExportData(array $params, array $user)
    {
        try {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);

            //获取导出列表
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder->leftJoin(MaterialPackageAllotSkuModel::class, 'sku.allot_id = main.id', 'sku');
            $columns = 'main.no,main.apply_date,main.apply_name,main.apply_id,main.node_department_name,main.out_store_name,main.out_sys_store_id,main.in_store_name,main.in_sys_store_id,main.linear_distance,';
            $columns.= 'main.delivery_way,main.reason_type,main.ka_account,main.remark,main.plan_out_date,main.status,main.real_out_date,main.in_date,main.out_confirm_staff_name,main.out_confirm_staff_id,main.in_confirm_staff_name,';
            $columns.= 'main.in_confirm_staff_id,main.cancel_reason,sku.barcode,sku.' . $this->getGoodsName() . ' as goods_name,sku.specs_model,sku.unit,sku.surplus_number,sku.out_number';
            $builder->columns($columns);
            $builder = $this->getCondition($builder, $params, $user);
            $builder->limit($page_size, $offset);
            $builder->orderby('main.created_at desc');
            $items = $builder->getQuery()->execute()->toArray();
            return $this->handleListItems($items, true);
        } catch (ValidationException $e) {
            //无可查看的数据范围时，直接返回空
            return [];
        } catch (Exception $e) {
            $this->logger->warning('调拨单-获取导出数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取导出的Excel表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('material_package_allot.no'),//网点耗材调拨单号
            static::$t->_('material_package_allot.apply_date'),//创建日期
            static::$t->_('material_package_allot.apply_name'),//创建人姓名
            static::$t->_('material_package_allot.apply_id'),//创建人工号
            static::$t->_('material_package_allot.node_department_name'),//创建部门
            static::$t->_('material_package_allot.out_store_name'),//调出网点名称
            static::$t->_('material_package_allot.out_sys_store_id'),//调出网点编码
            static::$t->_('material_package_allot.in_store_name'),//调入网点名称
            static::$t->_('material_package_allot.in_sys_store_id'),//调入网点编码
            static::$t->_('material_package_allot.linear_distance'),//网点之间直线距离(KM)
            static::$t->_('material_package_allot.delivery_way'),//配送方式
            static::$t->_('material_package_allot.reason_type'),//调拨理由
            static::$t->_('material_package_allot.ka_name'),//客户
            static::$t->_('material_package_allot.remark'),//调拨说明
            static::$t->_('material_package_allot.plan_out_date'),//计划调出日期
            static::$t->_('material_package_allot.status'),//调拨状态
            static::$t->_('material_package_allot.real_out_date'),//实际调出日期
            static::$t->_('material_package_allot.in_date'),//调入日期
            static::$t->_('material_package_allot.out_confirm_staff_name'),//调出确认人姓名
            static::$t->_('material_package_allot.out_confirm_staff_id'),//调出确认人工号
            static::$t->_('material_package_allot.in_confirm_staff_name'),//调入确认人姓名
            static::$t->_('material_package_allot.in_confirm_staff_id'),//调入确认人工号
            static::$t->_('material_package_allot.cancel_reason'),//取消说明
            static::$t->_('material_package_allot.barcode'),//barcode
            static::$t->_('material_package_allot.goods_name'),//耗材名称
            static::$t->_('material_package_allot.specs_model'),//规格型号
            static::$t->_('material_package_allot.unit'),//基本单位
            static::$t->_('material_package_allot.surplus_number'),//调出网点库存数量
            static::$t->_('material_package_allot.out_number'),//调出数量
        ];
    }

    /**
     * 耗材调拨单-耗材调拨待处理数 - For - By
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotWaitHandleNumForBy(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = 0;
        try {
            //当前用户负责网点
            $manage_store_ids = $this->getStaffManagerStoreIds($params['staff_id']);

            //当前用户所属网点
            $manage_store_ids = array_merge($manage_store_ids, [$params['sys_store_id']]);
            if ($manage_store_ids) {
                //1. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调出网点并且调拨单状态为待调出的所有调拨单
                //3. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调入网点并且调拨单状态为待调入的所有调拨单
                $builder = $this->modelsManager->createBuilder();
                $builder->from(['main' => MaterialPackageAllotModel::class]);
                $builder->where('main.out_sys_store_id in ({store_ids:array}) and main.status = :status_out:', ['store_ids' => $manage_store_ids, 'status_out' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT]);
                $builder->orWhere('main.in_sys_store_id in ({store_ids:array}) and main.status = :status_in:', ['store_ids' => $manage_store_ids, 'status_in' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN]);
                $builder->columns('count(DISTINCT main.id) AS count');
                $data = (int)$builder->getQuery()->getSingleResult()->count;
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('调拨单-耗材调拨待处理数-For-By:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 耗材调拨单-待处理/已处理-列表 - For - By
     * @param array $params 参数组
     * @return array
     */
    public function getPackageAllotListForBy(array $params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            //获取当前用户所属网点或者当前用户负责网点的网点组
            $manage_store_ids = $this->getManageStoreIds($params['staff_id']);
            $params['manage_store_ids'] = $manage_store_ids;

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('COUNT(DISTINCT main.id) as count');
            $builder->from(['main' => MaterialPackageAllotModel::class]);
            $builder = $this->getConditionForBy($builder, $params);
            $count   = $builder->getQuery()->getSingleResult()->count;

            if ($count) {
                $flag    = $params['flag'] ?? self::LIST_FOR_BY_WAIT_HANDLE;
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.id,main.no,main.out_sys_store_id,main.out_store_name,main.in_sys_store_id,main.in_store_name,main.plan_out_date,main.status,main.real_out_date,main.delivery_way');
                $builder->from(['main' => MaterialPackageAllotModel::class]);
                $builder = $this->getConditionForBy($builder, $params);
                $builder->limit($page_size, $offset);
                $builder->groupBy('main.id');
                $builder->orderby('main.id ' . ($flag == self::LIST_FOR_BY_WAIT_HANDLE ? 'ASC' : 'DESC'));
                $items         = $builder->getQuery()->execute()->toArray();
                foreach ($items as &$item) {
                    $item['real_out_date']     = $item['real_out_date'] ? $item['real_out_date'] : '';
                    $item['status_text']       = static::$t->_(MaterialWmsEnums::$package_allot_status[$item['status']]);
                    $item['delivery_way_text'] = static::$t->_(MaterialWmsEnums::$package_allot_delivery_way[$item['delivery_way']]);
                    $item['is_show_out']       = $flag == self::LIST_FOR_BY_WAIT_HANDLE && in_array($item['out_sys_store_id'], $manage_store_ids) && $item['status'] == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT ? true : false ;//当前用户的所属网点等于调出网点或者当前用户是调出网点负责人时并且调拨状态为待调出时显示
                    $item['is_show_in']        = $flag == self::LIST_FOR_BY_WAIT_HANDLE && in_array($item['in_sys_store_id'], $manage_store_ids) && $item['status'] == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN ? true : false ;//当前用户的所属网点等于调入网点或者当前用户是调入网点负责人时并且调拨状态为待调入时显示
                }
                $data['items'] = $items ?? [];
            }
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('调拨单-待处理/已处理-列表 - For - By-失败:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 耗材调拨单-获取枚举 - For - By
     * @return array
     */
    public function getPackageAllotOptionsDefaultForBy()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            //调拨状态
            $enums_arr = [
                'status'       => MaterialWmsEnums::$package_allot_status,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v),
                    ];
                }
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('调拨单-耗材调拨单-获取枚举 - For - By:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取当前用户所属网点或者当前用户负责网点的网点组
     * @param int $staff_info_id 员工工号
     * @return array
     */
    private function getManageStoreIds(int $staff_info_id)
    {
        //当前用户负责网点
        $manage_store_ids = $this->getStaffManagerStoreIds($staff_info_id);
        $staff_info = (new HrStaffRepository)->getStaffById($staff_info_id);

        //当前用户所属网点
        if ($staff_info) {
            $manage_store_ids = array_merge($manage_store_ids, [$staff_info['sys_store_id']]);
        }
        return $manage_store_ids;
    }

    /**
     * 耗材调拨单-待处理/已处理-列表 - 查询条件 - For - By
     * @param object $builder 查询器
     * @param array $params 参数组
     * @return object
     */
    public function getConditionForBy(object $builder, array $params)
    {
        $manage_store_ids    = $params['manage_store_ids'];
        $flag                = $params['flag'] ?? self::LIST_FOR_BY_WAIT_HANDLE;//列表类型 1待处理、2已处理，不传递默认待处理
        $status              = $params['status'] ?? [];             //调拨单状态
        $real_out_date_start = $params['real_out_date_start'] ?? '';//实际调出日期-起始
        $real_out_date_end   = $params['real_out_date_end'] ?? '';  //实际调出日期-截止
        $in_date_start       = $params['in_date_start'] ?? '';      //调入日期-起始
        $in_date_end         = $params['in_date_end'] ?? '';        //调入日期-截止

        if ($flag == self::LIST_FOR_BY_WAIT_HANDLE) {
            /**
             * 待处理
             * 1. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调出网点并且调拨单状态为待调出的所有调拨单
             * 2. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调入网点并且调拨单状态为待调出的所有调拨单
             * 3. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调入网点并且调拨单状态为待调入的所有调拨单
             * 4. 以上3个条数的数据均展示在待处理列表中，其中1优先级高于2
             */
            $builder->where('main.out_sys_store_id in({store_ids:array}) and status = :status_out:', ['store_ids' => $manage_store_ids, 'status_out' => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT]);
            $builder->orWhere('main.in_sys_store_id in({store_ids:array}) and status in({status_in:array})', ['store_ids' => $manage_store_ids, 'status_in' => [MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT, MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN]]);
        } elseif ($flag == self::LIST_FOR_BY_HANDLED) {
            /**
             * 已处理
             * 1. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调出网点并且调拨单状态为待调入或者已完成或者已取消的调拨单
             * 2. 当前用户所属网点或者当前用户负责网点(取网点表中负责人等于当前用户)等于调入网点并且调拨单状态为已完成或者已取消的调拨单
             * 3. 以上2个条数的数据均展示在已处理列表中，去重复
             */
            $builder->where('main.out_sys_store_id in({store_ids:array}) and status in({status_out:array})', ['store_ids' => $manage_store_ids, 'status_out' => [MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN, MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE, MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL]]);
            $builder->orWhere('main.in_sys_store_id in({store_ids:array}) and status in({status_in:array})', ['store_ids' => $manage_store_ids, 'status_in' => [MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE, MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_CANCEL]]);
        }

        if (!empty($status)) {
            $builder->inWhere('main.status', $status);
        }
        if (!empty($real_out_date_start)) {
            $builder->andWhere('main.real_out_date >= :real_out_date_start:', ['real_out_date_start' => $real_out_date_start]);
        }
        if (!empty($real_out_date_end)) {
            $builder->andWhere('main.real_out_date <= :real_out_date_end:', ['real_out_date_end' => $real_out_date_end]);
        }
        if (!empty($in_date_start)) {
            $builder->andWhere('main.in_date >= :in_date_start:', ['in_date_start' => $in_date_start]);
        }
        if (!empty($in_date_end)) {
            $builder->andWhere('main.in_date <= :in_date_end:', ['in_date_end' => $in_date_end]);
        }
        return $builder;
    }

    /**
     * BY-确认调出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function confirmOut(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $material_package_allot = $this->getPackageAllotInfo($params['id']);
            if ($material_package_allot->status != MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_OUT) {
                throw new ValidationException(static::$t->_('material_package_allot_out_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //1. 默认展示一行物流单号，允许填写多行，最多10行，最少1行，本次填写的单号不允许重复
            if ($material_package_allot->delivery_way == MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_DELIVERY_WAY_EXPRESS) {
                Validation::validate($params, ['express_sn' => 'Required|Arr|ArrLenGeLe:1,10', 'express_sn[*]' => 'Required|StrLenGeLe:1,50|>>>:'.static::$t->_('material_package_allot_confirm_express_sn_required')]);

                //本次填写的单号不允许重复
                if (count(array_unique($params['express_sn'])) != count($params['express_sn'])) {
                    throw new ValidationException(static::$t->_('material_package_allot_confirm_express_sn_repeat'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $update_data = [
                'status'                 => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN,
                'real_out_date'          => date('Y-m-d'),
                'out_confirm_staff_id'   => $user['id'],
                'out_confirm_staff_name' => $user['name'],
                'updated_id'             => $user['id'],
                'updated_name'           => $user['name'],
                'updated_at'             => date('Y-m-d H:i:s'),
            ];
            $bool        = $material_package_allot->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('调拨单-确认调出失败，数据为：' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot), ErrCode::$BUSINESS_ERROR);
            }

            //保存快递信息
            if (!empty($params['express_sn'])) {
                $express_data = [];
                foreach ($params['express_sn'] as $item) {
                    $express_data[] = [
                        'allot_id'   => $material_package_allot->id,
                        'express_sn' => $item,
                        'created_at' => $material_package_allot->updated_at,
                    ];
                }
                $material_package_allot_express = new MaterialPackageAllotExpressModel();
                $bool = $material_package_allot_express->batch_insert($express_data);
                if ($bool === false) {
                    throw new BusinessException('调拨单-确认调出-保存物流信息失败，数据为：' . json_encode($express_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot_express), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-确认调出-For-By失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * BY-确认调入
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function confirmIn(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $material_package_allot = $this->getPackageAllotInfo($params['id']);
            if ($material_package_allot->status != MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_IN) {
                throw new ValidationException(static::$t->_('material_package_allot_in_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            $update_data = [
                'status'                => MaterialWmsEnums::MATERIAL_PACKAGE_ALLOT_STATUS_DONE,
                'in_date'               => date('Y-m-d'),
                'in_confirm_staff_id'   => $user['id'],
                'in_confirm_staff_name' => $user['name'],
                'updated_id'            => $user['id'],
                'updated_name'          => $user['name'],
                'updated_at'            => date('Y-m-d H:i:s'),
            ];
            $bool        = $material_package_allot->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('调拨单-确认调入失败，数据为：' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($material_package_allot), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('调拨单-确认调入-For-By失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }
}
