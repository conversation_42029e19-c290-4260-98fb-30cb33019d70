<?php

namespace App\Modules\Material\Services;

use app\enums\HrStaffInfoStateEnums;
use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\MaterialWmsEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\MaterialSettingEnums;
use App\Library\Enums\ScmEnums;
use App\Library\ErrCode;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Models\oa\ByWorkflowBusinessRelModel;
use App\Models\oa\MaterialWmsApplyModel;
use App\Models\oa\MaterialWmsApplyProductModel;
use App\Models\oa\MaterialWmsOutStorageModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Material\Models\MaterialAttachmentModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Library\Validation\ValidationException;
use App\Library\Exception\BusinessException;
use App\Modules\Third\Services\ByWorkflowService;
use App\Modules\User\Models\DepartmentModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\User\Services\StaffService;
use App\Util\RedisKey;
use App\Modules\Material\Models\MaterialSauSkuModel;
use App\Library\Enums\StaffInfoEnums;
use App\Repository\StoreRepository;
use App\Repository\HrStaffRepository;
use App\Library\OssHelper;
use GuzzleHttp\Exception\GuzzleException;

class WmsApplyService extends BaseService
{
    //非必需的筛选条件
    public static $not_must_params = [
        'staff_id',
        'status',
        'node_department_id',
        'company_id',
        'apply_date_start',
        'apply_date_end',
        'approve_at_start',
        'approve_at_end',
        'type',
        'pageSize',
        'pageNum'
    ];

    //列表搜索
    public static $validate_list_search = [
        'staff_id'           => 'IntGt:0',//申请人工号,
        'apply_no'           => 'StrLenGeLe:0,50',//耗材单号-精确
        'status[*]'          => 'IntIn:' . Enums::WF_STATE_PENDING . ',' . Enums::WF_STATE_REJECTED . ',' . Enums::WF_STATE_APPROVED . ',' . Enums::WF_STATE_CANCEL,
        'sys_store_id'       => 'StrLenGeLe:0,10',//申请人所属网点
        'node_department_id' => 'IntGt:0',//申请人所属部门ID
        'company_id'         => 'IntGt:0',//申请人所属公司
        'use_land_id[*]'     => 'StrLenGeLe:0,10', //使用地
        'apply_date_start'   => 'Date',//申请日期-起始
        'apply_date_end'     => 'Date',//申请日期-截止
        'type'               => 'IntIn:' . MaterialWmsEnums::AUDIT_TYPE_WAIT . ',' . MaterialWmsEnums::AUDIT_TYPE_PROCESSED ,//审批列表（0待审核，1已处理）
        'barcode[*]'         => 'StrLenGeLe:0,30',//barcode
        'name'               => 'StrLenGeLe:0,100',//耗材名称
        'pageSize'           => 'IntGt:0',//每页条数
        'pageNum'            => 'IntGt:0', //页码
    ];

    //搜索标准型号
    public static $validate_search_barcode = [
        'pageSize'   => 'IntGt:0',//每页条数
        'pageNum'    => 'IntGt:0', //页码
        'name'       => 'StrLenGeLe:0,30', //物料名称
        'like_barcode' => 'StrLenGeLe:0,30',//barcode模糊搜索
        'barcode[*]'    => 'StrLenGeLe:0,30', //标准型号的唯一标识
        'model'      => 'StrLenGeLe:0,100', //规格型号
        'company_id' => 'Required|IntGt:0',//申请人所属公司
    ];

    //新增或者重新提交
    public static $validate_add = [
        'apply_no'                        => 'Required|StrLenGeLe:15,50|>>>:apply_no error',
        'apply_date'                      => 'Required|Date|>>>:apply_date error',
        'staff_id'                        => 'Required|IntGt:0|>>>:staff_id error',
        'staff_name'                      => 'Required|StrLenGeLe:1,50|>>>:staff_name error',
        'company_id'                      => 'Required|IntGt:0|>>>:company_id error',
        'company_name'                    => 'Required|StrLenGeLe:1,50|>>>:company_name error',
        'node_department_id'              => 'Required|IntGt:0|>>>:node_department_id error',//所属部门ID
        'node_department_name'            => 'Required|StrLenGeLe:1,50|>>>:node_department_name error',//所属部门名称
        'sys_store_id'                    => 'Required|StrLenGeLe:1,10|>>>:sys_store_id error',//所属网点
        'store_name'                      => 'Required|StrLenGeLe:1,50|>>>:store_name error',//所属网点名称
        'use_land_id'                     => 'Required|StrLenGeLe:1,10|>>>:use_land_id error',//使用地ID
        'use_land_name'                   => 'Required|StrLenGeLe:1,100|>>>:use_land_name error',//使用地名称
        'consignee_id'                     => 'Required|IntGt:0|>>>:consignee_id error',//收货人id
        'consignee_name'                   => 'Required|StrLenGeLe:1,50|>>>:consignee_name error',//收货人name
        'delivery_way'                     => 'Required|IntIn:1,2|>>>:delivery_way error',//配送方式
        'headquarters'                     => 'Required|StrLenGeLe:1,10|>>>:headquarters error',//耗材使用网点如果总部为-1
        'reason'                          => 'Required|StrLenGeLe:1,200|>>>:reason error',//申请理由
        'attachments'                     => 'Arr|ArrLenGeLe:0,10|>>>:attachments error',//附件信息
        'products'                        => 'Required|Arr|ArrLenGeLe:1,50|>>>:products error',
        'products[*]'                     => 'Required|Obj',
        'products[*].id'                  => 'Required|IntGt:0|>>>:id error',//id
        'products[*].barcode'             => 'Required|StrLenGeLe:1,30|>>>:barcode error',//barcode
        'products[*].name_zh'             => 'Required|StrLenGeLe:1,100|>>>:name_zh error',//中文名称
        'products[*].name_en'             => 'Required|StrLenGeLe:1,100|>>>:name_en error',//英文名称
        'products[*].name_local'          => 'StrLenGeLe:0,100|>>>:name_local error',//当地语言名称
        'products[*].unit_zh'             => 'StrLenGeLe:0,20|>>>:unit_zh error',//基本单位-中文
        'products[*].unit_en'             => 'StrLenGeLe:0,20|>>>:unit_en error',//基本单位-英文
        'products[*].model'               => 'StrLenGeLe:0,100|>>>:model error',//规格型号
        'products[*].this_time_num'       => 'Required|IntGeLe:1,100000|>>>:this_time_num error',//申请数量
        'products[*].available_inventory' => 'Required|IntGe:0|>>>:available_inventory error',//可用库存
    ];

    //查看 、修改 、撤回 、驳回 、审核
    public static $validate_share = [
        'id' => 'Required|IntGt:0',
    ];

    // by审批流水号
    public static $validate_workflow_no = [
        'workflow_no' => 'Required|StrLenGeLe:1,50',
    ];

    //撤回
    public static $validate_cancel = [
        'reason' => 'Required|StrLenGeLe:1,100'
    ];

    //驳回
    public static $validate_reject = [
        'reason' => 'Required|StrLenGeLe:1,1000'
    ];

    //产品明细
    public static $validate_products = [
        'products'                        => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]'                     => 'Required|Obj',
        'products[*].id'                  => 'Required|IntGt:0',//耗材明细表ID
        'products[*].available_inventory' => 'Required|IntGe:0',//可用库存
    ];

    //审核
    public static $validate_pass = [
        'products'                 => 'Required|Arr|ArrLenGeLe:1,50',
        'products[*]'              => 'Required|Obj',
        'products[*].id'           => 'Required|IntGt:0',//明细表ID
        'products[*].now_time_num' => 'Required|IntGe:0',//审核数量
        'products[*].barcode'      => 'Required|StrLenGeLe:1,30|>>>:barcode error',//barcode
    ];

    //查询收货人
    public static $validate_consignee = [
        'use_land_id'  => 'Required|StrLenGeLe:1,10|>>>:use_land_id error',
        'headquarters' => 'Required|StrLenGeLe:1,10|>>>:headquarters error',
        'sys_store_id' => 'Required|StrLenGeLe:1,10|>>>:sys_store_id error',
        'name'         => 'StrLenGeLe:0,200|>>>:name error',
    ];

    //数据查询列表 审核时间
    public static $validate_approve_at = [
        'approve_at_start'     => 'Date',//审批日期-起始
        'approve_at_end'   => 'Date',//审批日期-截止
    ];

    //审核模糊搜索barcode
    public static $validate_audit_search_barcode = [
        'pageSize'   => 'IntGt:0',//每页条数
        'pageNum'    => 'IntGt:0', //页码
        'barcode'    => 'Required|StrLenGeLe:1,30', //标准型号的唯一标识
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return WmsApplyService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 搜索标准型号
     * @return array
     */
    public function getValidateSearchBarcode()
    {
        $validate_search_barcode = self::$validate_search_barcode;
        //17404需求，分仓规则开启时，网点参数必须传递
        $material_store_storage_open_status = WarehouseDivisionRuleService::getInstance()->getStoreStorageOpenStatus();
        if ($material_store_storage_open_status == MaterialSettingEnums::STORE_STORAGE_OPEN) {
            $validate_search_barcode['use_land_id'] = 'Required|StrLenGeLe:1,10';
        }
        return $validate_search_barcode;
    }

    /**
     * 耗材默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $data = $this->getStatus();
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            //货主列表
            $scm              = new ScmService();
            $cargo_owner      = $scm->scmCargoOwner();
            $cargo_owner_list = [];
            if (!empty($cargo_owner)) {
                foreach ($cargo_owner as $item) {
                    $cargo_owner_list[] = [
                        'value' => $item['mach_code'],
                        'label' => $item['name']
                    ];
                }
            }
            $data['cargo_owner'] = $cargo_owner_list;
            $data['delivery_way_list'] = $this->getDeliveryWay();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('获取耗材枚举信息失败: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取使用状态
     * @return array
     */
    public function getStatus()
    {
        $data      = [];
        $enums_arr = [
            'status'     => MaterialWmsEnums::$wms_apply_status,
            'out_status' => MaterialWmsEnums::$wms_out_storage_status,
        ];
        foreach ($enums_arr as $key => $value) {
            foreach ($value as $k => $item) {
                $data[$key][] = [
                    'value' => $k,
                    'label' => static::$t->_($item)
                ];
            }
        }
        return $data;
    }

    /**
     * 获取耗材申请单列表
     * @param array $condition 查询条件组
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getList(array $condition, int $type, array $user)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getListCount($condition, $type, $user);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.apply_no, status, staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark, approve_at';

                $builder->columns($columns);
                $builder->from(['main' => MaterialWmsApplyModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition, $type, $user);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id ' . ($condition['sort'] ?? 'DESC'));
                $items = $builder->getQuery()->execute()->toArray();
                $items     = $this->handleListItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get_material_wms_apply_list_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 获取耗材申请单下载列表
     * @param array $condition 查询条件组
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param int $count 总记录数
     * @return array
     */
    public function getExportList($condition, $type, $count)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.apply_no, main.apply_date, main.status, main.staff_id, main.staff_name, main.company_name, main.node_department_name, main.store_name, main.reason, main.use_land_name, product.barcode, product.this_time_num, product.name_zh, product.name_en, product.name_local, product.model, product.last_time_num, product.use_val, main.approve_at, main.operation_remark, product.unit_zh, product.unit_en';
                $builder->columns($columns);
                $builder->from(['main' => MaterialWmsApplyModel::class]);
                $builder->leftjoin(MaterialWmsApplyProductModel::class, 'main.id=product.apply_id', 'product');
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition, $type);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id ' . ($condition['sort'] ?? 'DESC'));
                $items_obj = $builder->getQuery()->execute();
                $items     = $items_obj ? $items_obj->toArray() : [];
                $items     = $this->handleListItems($items, true, $type);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get_material_wms_apply_export_list failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @param bool $export 是否导出，true导出、false非导出
     * @return int
     * @throws ValidationException
     */
    public function getListCount($condition, $type, $user = [], $export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => MaterialWmsApplyModel::class]);
        if ($export === true) {
            $builder->leftjoin(MaterialWmsApplyProductModel::class, 'main.id = product.apply_id', 'product');
        }
        //组合搜索条件
        $builder    = $this->getCondition($builder, $condition, $type, $user);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param integer $type 1:申请单列表，2审批列表，3数据查询列表
     * @param array $user 当前登陆者信息组
     * @return mixed
     * @throws ValidationException
     */
    public function getCondition($builder, $condition, $type, $user = [])
    {
        //申请时间起始与结束校验
        $apply_date_start = !empty($condition['apply_date_start']) ? $condition['apply_date_start'] : '';
        $apply_date_end   = !empty($condition['apply_date_end']) ? $condition['apply_date_end'] : '';
        if ($apply_date_start > $apply_date_end) {
            throw new ValidationException(self::$t->_('material_wms_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        //审批时间起始与结束校验
        $approve_at_start = !empty($condition['approve_at_start']) ? $condition['approve_at_start'] : '';
        $approve_at_end   = !empty($condition['approve_at_end']) ? $condition['approve_at_end'] : '';
        if ($approve_at_start > $approve_at_end) {
            throw new ValidationException(self::$t->_('material_wms_list_date_error'), ErrCode::$VALIDATE_ERROR);
        }
        $apply_no           = !empty($condition['apply_no']) ? $condition['apply_no'] : ''; //申请单号-精确搜索
        $staff_id           = !empty($condition['staff_id']) ? trim($condition['staff_id']) : 0;//申请人工号
        $status             = !empty($condition['status']) ? $condition['status'] : [];//状态组
        $sys_store_id       = !empty($condition['sys_store_id']) ? $condition['sys_store_id'] : '';//所属网点
        $node_department_id = !empty($condition['node_department_id']) ? $condition['node_department_id'] : 0;//所属部门
        $company_id         = !empty($condition['company_id']) ? $condition['company_id'] : 0;//所属公司
        $use_land_id        = !empty($condition['use_land_id']) ? $condition['use_land_id'] : [];//使用地ID组
        $audit_type         = $condition['type'] ?? 0;//审批列表类型，0待审核，1已处理
        $workflow_no        = $condition['workflow_no'] ?? [];//按照by的审批流水号搜索
        $barcode            = $condition['barcode'] ?? [];

        $builder->where('main.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY) {
            //申请列表
            $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $user['id']]);
        } else if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT) {
            if ($audit_type == 0) {
                //待审核
                $builder->andWhere('main.status = :status:', ['status' => Enums::WF_STATE_PENDING]);
                if (!empty($barcode) && is_array($barcode) && empty($workflow_no)) {
                    $builder->inWhere('product.barcode', $barcode);
                }
            } else {
                //已处理（驳回、通过）
                $builder->andWhere('audit.approval_id = :approval_id:', ['approval_id' => $user['id']]);
                $builder->andWhere('audit.biz_type = :biz_type:', ['biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS]);
                $builder->inWhere('audit.status', [Enums::WF_STATE_REJECTED, Enums::WF_STATE_APPROVED]);
            }
        }
        if (!empty($apply_no)) {
            //申请单号
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }
        if (!empty($staff_id)) {
            //申请人工号
            $builder->andWhere('main.staff_id = :staff_id:', ['staff_id' => $staff_id]);
        }
        if (!empty($status)) {
            //申请状态
            if (is_array($status)) {
                $builder->inWhere('main.status ', $status);
            } else {
                $builder->andWhere('main.status = :status:', ['status' => $status]);
            }
        }
        if (!empty($sys_store_id)) {
            //按照所属网点搜索
            $builder->andWhere('main.sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
        }
        if (!empty($node_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids     = $department_service->getChildrenListByDepartmentIdV2($node_department_id, true);
            array_push($department_ids, $node_department_id);
            $builder->inWhere('main.node_department_id', $department_ids);
        }
        if (!empty($workflow_no)) {
            //按照by审批流水号搜索
            $builder->inWhere('main.workflow_no', $workflow_no);
        }
        if (!empty($company_id)) {
            //所属公司
            $builder->andWhere('main.company_id = :company_id:', ['company_id' => $company_id]);
        }
        if (!empty($use_land_id)) {
            //网点地址
            $builder->inWhere('main.use_land_id ', $use_land_id);
        }
        if (!empty($apply_date_start) && !empty($apply_date_end)) {
            $apply_date_start .= ' 00:00:00';
            $apply_date_end   .= ' 23:59:59';
            $builder->betweenWhere('main.created_at ', $apply_date_start, $apply_date_end);
        }
        if (!empty($approve_at_start) && !empty($approve_at_end)) {
            $approve_at_start .= ' 00:00:00';
            $approve_at_end   .= ' 23:59:59';
            $builder->betweenWhere('main.approve_at ', $approve_at_start, $approve_at_end);
        }

        return $builder;
    }

    /**
     * 格式化申请单列表
     * @param array $items 申请单列表
     * @param bool $export 导出
     * @param int $type   1 申请 2 审核   3 数据查询
     * @return array
     */
    private function handleListItems($items, $export = false, int $type = 0)
    {
        if (empty($items)) {
            return [];
        }
        if ($export === true) {
            //导出
            $lang         = MaterialClassifyEnums::$language_fields[static::$language] ?? 'local';
            $name         = 'name_' . $lang;
            $barcode_list = MaterialSauModel::find([
                'columns'    => 'id, barcode, update_to_scm, category_type',
                'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                'bind'       => [
                    'barcode'    => array_values(array_unique(array_column($items, 'barcode'))),
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ])->toArray();

            //获取最新的领用单位
            $use_val_arr = $this->getMaterialSauSkuByUseVal($barcode_list);
            $row_value = [];
            //组装导出需要的数据
            if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA) {
                //数据查询导出
                foreach ($items as $item) {
                    $row_value[] = [
                        'apply_no'             => $item['apply_no'],
                        'status'               => static::$t[MaterialWmsEnums::$wms_apply_status[$item['status']]],
                        'apply_date'           => $item['apply_date'],
                        'approve_at'           => $item['approve_at'] ? date('Y-m-d', strtotime($item['approve_at'])) : '',
                        'staff_id'             => $item['staff_id'],
                        'staff_name'           => $item['staff_name'],
                        'use_land_name'        => $item['use_land_name'],
                        'company_name'         => $item['company_name'],
                        'node_department_name' => $item['node_department_name'],
                        'store_name'           => $item['store_name'],
                        'reason'               => $item['reason'],
                        'operation_remark'     => $item['operation_remark'],
                        'barcode'              => $item['barcode'],
                        'this_time_num'        => $item['this_time_num'],
                        'last_time_num'        => $item['last_time_num'],
                        'name_zh'              => $item['name_zh'],
                        'name_en'              => $item['name_en'],
                        'name_local'           => $item['name_local'],
                        'model'                => $item['model'],
                        'unit_zh'              => $item['unit_zh'],
                        'unit_en'              => $item['unit_en'],
                        'use_val'              => $use_val_arr[$item['barcode']] ?? 0,
                    ];
                }
            } else {
                //审核数据导出
                foreach ($items as $item) {
                    $row_value[] = [
                        'apply_no'             => $item['apply_no'],
                        'barcode'              => $item['barcode'],
                        'apply_date'           => $item['apply_date'],
                        'staff_id'             => $item['staff_id'],
                        'staff_name'           => $item['staff_name'],
                        'name_local_model'     => $item[$name] . '/' . $item['model'],
                        'this_time_num'        => $item['this_time_num'],
                        'last_time_num'        => $item['last_time_num'],
                        'use_val'              => $use_val_arr[$item['barcode']] ?? 0,
                        'company_name'         => $item['company_name'],
                        'node_department_name' => $item['node_department_name'],
                        'store_name'           => $item['store_name'],
                        'use_land_name'        => $item['use_land_name'],
                        'reason'               => $item['reason'],
                    ];
                }
            }
            $items = $row_value;
        } else {
            //非导出
            foreach ($items as &$item) {
                $item['approve_at']  = $item['approve_at'] ? date('Y-m-d', strtotime($item['approve_at'])) : '';
                $item['status_text'] = static::$t[MaterialWmsEnums::$wms_apply_status[$item['status']]];
            }
        }
        return $items;
    }

    /**
     * 耗材申请审批列表
     * @param array $condition 条件
     * @param array $user 当前用户数据
     * @return array
     */
    public function getAuditList($condition, $user)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $audit_type = $condition['type'] ?? 0;//0待审核，1已处理
            if ($audit_type == MaterialWmsEnums::AUDIT_TYPE_WAIT) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.workflow_no');
                $builder->from(['main' => MaterialWmsApplyModel::class]);
                if (!empty($condition['barcode'])) {
                    $builder->leftjoin(MaterialWmsApplyProductModel::class, 'main.id=product.apply_id', 'product');
                }
                $builder     = $this->getCondition($builder, $condition, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT, $user);
                $search_list = $builder->getQuery()->execute()->toArray();
                if (!empty($search_list)) {
                    $workflow_no                       = array_unique(array_column($search_list, 'workflow_no'));
                    $by_list_params                    = [
                        'serial_no'   => $workflow_no,
                        'biz_type'    => [ByWorkflowEnums::BY_BIZ_TYPE_WMS],
                        'approval_id' => $user['id'],
                        'state'       => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                        'page_num'    => $page_num,
                        'page_size'   => $page_size
                    ];
                    $result                            = (new ByWorkflowService())->getList($by_list_params);
                    $data['pagination']['total_count'] = !empty($result['total_count']) ? $result['total_count'] : 0;
                    if (!empty($result['list'])) {
                        $condition['workflow_no'] = array_values(array_column($result['list'], 'serial_no'));
                        if ($condition['workflow_no']) {
                            $builder = $this->modelsManager->createBuilder();
                            $columns = 'main.id, apply_no, status, staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark, approve_at';
                            $builder->columns($columns);
                            $builder->from(['main' => MaterialWmsApplyModel::class]);
                            $builder = $this->getCondition($builder, $condition, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT, $user);
                            $builder->orderby('main.id asc');
                            $items = $builder->getQuery()->execute()->toArray();

                            $items = $this->handleListItems($items);
                        }
                    }
                }
            } else if ($audit_type == MaterialWmsEnums::AUDIT_TYPE_PROCESSED) {
                //已处理列表
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('count(distinct audit.biz_value) as total');
                $builder->from(['audit' => ByWorkflowAuditLogModel::class]);
                $builder->leftjoin(MaterialWmsApplyModel::class, 'main.id = audit.biz_value', 'main');
                $builder    = $this->getCondition($builder, $condition, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT, $user);
                $total_info = $builder->getQuery()->getSingleResult();
                $count      = intval($total_info->total);
                if ($count > 0) {
                    $columns = 'main.id, apply_no, main.status, main.staff_id, staff_name,company_name, store_name, node_department_name, apply_date, use_land_name, reason, operation_remark,approve_at';
                    $builder->columns($columns);
                    $builder->limit($page_size, $offset);
                    $builder->groupBy('main.id');
                    $builder->orderby('main.id desc');
                    $items = $builder->getQuery()->execute()->toArray();
                    $items = $this->handleListItems($items);
                }
                $data['pagination']['total_count'] = $count;
            }
            $data['items'] = $items ?? [];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('material_wms_apply_audit_list failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 耗材数据查询导出表头
     * @Date: 2/17/23 11:26 AM
     * @return array
     * @author: peak pan
     **/
    public function getExportHeader()
    {
        return [
            static::$t->_('material_wms_apply_no'),//耗材申请单号
            static::$t->_('material_wms_apply_barcode'),//barcode
            static::$t->_('material_wms_apply_created_at'),//申请时间
            static::$t->_('material_wms_apply_apply_id'),//申请人工号
            static::$t->_('material_wms_apply_apply_name'),//申请人姓名
            static::$t->_('material_wms_name_local_model'),//耗材名称—规格型号
            static::$t->_('material_wms_apply_this_time_num'),//申请数量
            static::$t->_('material_wms_apply_last_time_num'),//上级审核人审核数量
            static::$t->_('material_wms_apply_use_val'),//最小申请倍数
            static::$t->_('material_wms_apply_company_name'),//申请人所属公司
            static::$t->_('material_wms_apply_department_name'),//申请人所属部门
            static::$t->_('material_wms_apply_store_name'),//申请人所属网点
            static::$t->_('material_wms_apply_export_use_land_name'),//耗材使用网点
            static::$t->_('material_wms_apply_reason'),//申请理由
        ];
    }


    /**
     * 耗材审核导出表头
     * @Date: 5/29/23 11:26 AM
     * @return array
     **/
    public function getAuditExportHeader()
    {
        return [
            static::$t->_('material_wms_apply_no'),//耗材申请单号
            static::$t->_('material_wms_apply_status'),//单据状态
            static::$t->_('material_wms_apply_created_at'),//申请时间
            static::$t->_('material_wms_apply_approve_at'),//审批通过时间
            static::$t->_('material_wms_apply_apply_id'),//申请人工号
            static::$t->_('material_wms_apply_apply_name'),//申请人姓名
            static::$t->_('material_wms_apply_export_use_land_name'),//耗材使用网点
            static::$t->_('material_wms_apply_company_name'),//申请人所属公司
            static::$t->_('material_wms_apply_department_name'),//申请人所属部门
            static::$t->_('material_wms_apply_store_name'),//申请人所属网点
            static::$t->_('material_wms_apply_reason'),//申请理由
            static::$t->_('material_wms_apply_operation_remark'),//驳回原因
            static::$t->_('material_wms_apply_barcode'),//barcode
            static::$t->_('material_wms_apply_this_time_num'),//申请数量
            static::$t->_('material_wms_apply_last_time_num'),//上级审核人审核数量
            static::$t->_('material_wms_apply_name_zh'),//耗材中文名称
            static::$t->_('material_wms_apply_name_en'),//耗材英文名称
            static::$t->_('material_wms_apply_name_local'),//耗材当地语言名称
            static::$t->_('material_wms_model'),//规格型号
            static::$t->_('material_wms_apply_unit_zh'), //基本单位-中文
            static::$t->_('material_wms_apply_unit_en'),// 基本单位-英文
            static::$t->_('material_wms_apply_use_val'),//最小申请倍数
        ];
    }

    /**
     * 生成wms申请单号
     *
     * @param int $number_length
     * @return string
     */
    protected function generateMaterialWmsApplyNo(int $number_length = 5)
    {
        return static::genSerialNo(MaterialWmsEnums::MATERIAL_WMS_APPLY_NO_PREFIX, RedisKey::MATERIAL_WMS_APPLY_COUNTER, $number_length);
    }

    /**
     * 领用申请-添加默认配置项
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $now_date                     = date("Ymd");
            $data['apply_no']             = $this->generateMaterialWmsApplyNo();
            $data['apply_date']           = date("Y-m-d", strtotime($now_date));
            $data['staff_id']             = $user['id'];
            $data['staff_name']           = $user['name'];
            $staff_list                   = StaffService::getInstance()->searchStaff(['staff_id' => $user['id'], 'limit' => 1]);
            $user_other_info              = !empty($staff_list['data']) ? $staff_list['data'][0] : [];
            $data['node_department_id']   = $user_other_info['node_department_id'] ?? 0;
            $data['node_department_name'] = $user_other_info['node_department_name'] ?? '';
            $data['sys_store_id']         = $user_other_info['sys_store_id'] ?? '';
            $data['store_name']           = $user_other_info['store_name'] ?? '';
            $data['delivery_way_list'] = $this->getDeliveryWay();
            $department = DepartmentModel::findFirst([
                'conditions' => 'id = :id: and deleted = :deleted:',
                'bind'       => [
                    'id' => $user_other_info['node_department_id'],
                    'deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            //根据费用所属部门查询对应的COO/CEO下的BU级部门
            $data['company_id']   = '';
            $data['company_name'] = '';
            if (!empty($department)) {
                $company_list    = (new PurchaseService())->getCooCostCompany();
                $cost_company_kv = array_column($company_list, 'cost_company_name', 'cost_company_id');
                if (key_exists($department->company_id, $cost_company_kv)) {
                    $data['company_id']   = $department->company_id;
                    $data['company_name'] = $cost_company_kv[$department->company_id];
                }
            }
            //17404需求，返回分仓规则配置项
            $data['store_storage_open_status'] = WarehouseDivisionRuleService::getInstance()->getStoreStorageOpenStatus();
            //21074需求，返回禁止申请耗材网点类型
            $data['material_wms_apply_forbid_categorys'] = EnumsService::getInstance()->getSettingEnvValueIds('material_wms_apply_forbid_categorys');
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('material_wms_apply_add_default_failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }

    /**
     * 耗材搜索标准型号
     * @param array $params 搜索条件
     * @return array
     */
    public function searchBarcode($params)
    {
        $scm_service             = new ScmService();
        $default_scm             = $scm_service->getDefaultScmCargoOwner();
        $params['mach_code']     = !empty($default_scm) ? $default_scm['mach_code'] : '';
        $params['status']        = MaterialClassifyEnums::MATERIAL_START_USING;
        $params['update_to_scm'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
        $params['category_type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS;
        //16712需求增加包含可申请条件的筛选
        $params['use_scene'] = [MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY, MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY_AND_BUY];
        return AssetOutStorageService::getInstance()->searchBarcode(static::$language, $params);
    }

    /**
     * 领用申请-添加
     * @param array $params 参数
     * @param array $user 用户数据
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //非法操作
            if ($params['staff_id'] != $user['id']) {
                throw new ValidationException(static::$t->_('save_data_user_error'), ErrCode::$VALIDATE_ERROR);
            }
            //判断申请单号是否已存在
            $apply_no  = $params['apply_no'];
            $wms_apply = MaterialWmsApplyModel::findFirst([
                'conditions' => 'apply_no = :apply_no: and is_deleted = :is_deleted:',
                'bind'       => ['apply_no' => $apply_no, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (!empty($wms_apply)) {
                throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }
            //验证参数
            [$product_list, $sau_sku] = $this->validationSubmit($params);

            $params['current_time'] = date('Y-m-d H:i:s', time());
            $order_data             = $this->getApplyData($params);
            $main_model             = new MaterialWmsApplyModel();
            $bool                   = $main_model->i_create($order_data);
            if ($bool === false) {
                throw new BusinessException('耗材申请单添加失败: ' . json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }
            $sau_sku_arr = array_column($sau_sku, null, 'sau_id');
            $this->addApplyAttached($params, $main_model, $sau_sku_arr, $product_list);

            $db->commit();

            //18383需求抄送收货人，抄送成功与否不影响我们单据的提交也不将异常信息返回给用户
            $this->sendCCToConsignee($main_model);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_apply_add_failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 验证数据提交合法性以及组装数据
     * @param array $params 请求参数组
     * @return array
     * @throws ValidationException
     */
    private function validationSubmit(array $params)
    {
        //验证明细行barcode是否重复
        $barcode_arr = array_values(array_unique(array_filter(array_column($params['products'], 'barcode'))));
        if (count($barcode_arr) < count($params['products'])) {
            throw new ValidationException(static::$t->_('material_wms_barcode_unique_error'), ErrCode::$VALIDATE_ERROR);
        }

        //明细行数量不能全部是0
        $all_products_time_num_sum = array_sum(array_filter(array_column($params['products'], 'this_time_num')));
        if (empty($all_products_time_num_sum)) {
            throw new ValidationException(static::$t->_('material_wms_apply_all_num_not_zero'), ErrCode::$VALIDATE_ERROR);
        }

        //验证提交过来的barcode
        $barcode_list = MaterialSauModel::find([
            'conditions' => 'is_deleted = :is_deleted: and barcode in ({barcode:array})',
            'bind'       => ['is_deleted' => MaterialClassifyEnums::IS_DELETED_NO, 'barcode' => $barcode_arr]
        ])->toArray();

        if (empty($barcode_list)) {
            throw new ValidationException(static::$t->_('material_wms_barcode_existed'), ErrCode::$VALIDATE_ERROR);
        }

        $sau_arr = array_values(array_column($barcode_list, 'id'));

        $select_barcode = array_column($barcode_list, 'barcode');
        //验证是否存在已删除的barcode
        $diff_barcode = array_diff($barcode_arr, $select_barcode);
        if (!empty($diff_barcode)) {
            throw new ValidationException(static::$t->_('material_wms_barcode_enable_or_delete', ['barcode' => implode(";", $diff_barcode)]), ErrCode::$VALIDATE_ERROR);
        }

        //未启用barcode
        $enable_barcode = [];
        //非耗材类的barcode
        $not_wms_barcode  = $product_list = [];
        $products_barcode = array_column($params['products'], null, 'barcode');
        foreach ($barcode_list as $item) {
            if ($item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE) {
                $enable_barcode[] = $item['barcode'];
            } elseif ($item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $products_barcode[$item['barcode']]['this_time_num'] > 0) {
                $not_wms_barcode[] = $item['barcode'];
            } elseif ($item['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $products_barcode[$item['barcode']]['this_time_num'] > 0 && $item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO &&  $item['status'] == MaterialClassifyEnums::MATERIAL_START_USING ){
                $product_list[] = $products_barcode[$item['barcode']];
            } elseif ($item['category_type'] == MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $products_barcode[$item['barcode']]['this_time_num'] > 0 && $item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_OFF &&  $item['status'] == MaterialClassifyEnums::MATERIAL_START_USING) {
                //耗材的 并且不更新scm的 数量大于0 状态启用
                $product_list[] = $products_barcode[$item['barcode']];
            }
        }
        //验证是否存在非启用的barcode
        if (!empty($enable_barcode)) {
            throw new ValidationException(static::$t->_('material_wms_barcode_enable_or_delete', ['barcode' => implode(";", $enable_barcode)]), ErrCode::$VALIDATE_ERROR);
        }
        //验证是否存在非耗材类的barcode且申请数量大于0的
        if (!empty($not_wms_barcode)) {
            throw new ValidationException(static::$t->_('material_not_wms_barcode_max_num_zero', ['barcode' => implode(";", $not_wms_barcode)]), ErrCode::$VALIDATE_ERROR);
        }

        $sau_sku     = MaterialSauSkuModel::find([
            'conditions' => 'is_deleted = :is_deleted: and sau_id in ({sau_id:array})',
            'bind'       => ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'sau_id' => $sau_arr]
        ])->toArray();

        if (empty($sau_sku)) {
            throw new ValidationException(static::$t->_('material_wms_barcode_existed'), ErrCode::$VALIDATE_ERROR);
        }
        $products_barcode_arr = array_column($params['products'], null, 'id');

        foreach ($sau_sku as $value) {
            if (!empty($value['use_val'])) {
                if (!empty($products_barcode_arr[$value['sau_id']]) && !empty($products_barcode_arr[$value['sau_id']]['this_time_num'] % $value['use_val'])) {
                    //不是倍数
                    throw new ValidationException(static::$t->_('material_wms_use_val_not_times'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }
        return [$product_list, $sau_sku];
    }


    /**
     * 创建审批流 格式化数据
     * @param array $params 请求参数组
     * @param string $type add 添加  recommit 重新提交
     * @return  array
     **/
    private function getApplyData($params, $type = 'add')
    {
        //当前操作时间
        $current_time = $params['current_time'];
        //调取by创建审批
        $by_workflow   = new ByWorkflowService();
        $by_add_result = $by_workflow->add([
            'submitter_id' => $params['staff_id'],
            'summary_data' => [['key' => 'wms_use_land_name', 'value' => $params['use_land_name']]],
            'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
            'audit_params' => [
                'company_id' => $params['company_id']
            ]
        ]);
        $apply_data = [
            'apply_no'             => $params['apply_no'],
            'workflow_no'          => $by_add_result['serial_no'],
            'apply_date'           => $params['apply_date'],
            'staff_id'             => $params['staff_id'],
            'staff_name'           => $params['staff_name'],
            'company_id'           => $params['company_id'],
            'company_name'         => $params['company_name'],
            'node_department_id'   => $params['node_department_id'],
            'node_department_name' => $params['node_department_name'],
            'sys_store_id'         => $params['sys_store_id'],
            'store_name'           => $params['store_name'],
            'use_land_id'          => $params['use_land_id'],
            'use_land_name'        => $params['use_land_name'],
            'reason'               => $params['reason'],
            'status'               => Enums::WF_STATE_PENDING,
            'source_type'          => $params['source_type'] ?? 0,
            'updated_at'           => $current_time,
            'consignee_id'         => $params['consignee_id'],
            'consignee_name'       => $params['consignee_name'],
            'delivery_way'         => $params['delivery_way'],
            'headquarters'         => $params['headquarters'],
            'is_instead_apply' => ($params['staff_id'] == $params['consignee_id']) ? MaterialWmsEnums::IS_INSTEAD_APPLY_NO : MaterialWmsEnums::IS_INSTEAD_APPLY_YES
        ];
        if ($type == 'add') {
            $apply_data ['created_at'] = $current_time;
        } elseif ($type == 'recommit') {
            $apply_data ['operation_remark'] = '';
        }
        return $apply_data;
    }

    /**
     * 添加或重新提交申请单附属信息
     * @param array $params 参数
     * @param object $apply_model 申请单对象信息
     * @param array $sau_sku_arr sku详情数据
     * @param array $product_list 处理之后的数据
     * @throws BusinessException
     */
    private function addApplyAttached(array $params, object $apply_model, array $sau_sku_arr, array $product_list)
    {
        //当前操作时间
        $current_time = $params['current_time'];
        //申请明细信息入库
        $products = [];
        foreach ($product_list as $product) {
                $products[] = [
                    'apply_id'            => $apply_model->id,
                    'barcode'             => $product['barcode'],
                    'name_zh'             => $product['name_zh'],
                    'name_en'             => $product['name_en'],
                    'name_local'          => $product['name_local'],
                    'unit_zh'             => $product['unit_zh'],
                    'unit_en'             => $product['unit_en'],
                    'use_val'             => !empty($sau_sku_arr[$product['id']]) ? $sau_sku_arr[$product['id']]['use_val'] : 1,
                    'model'               => $product['model'],
                    'last_time_num'       => $product['this_time_num'],
                    'this_time_num'       => $product['this_time_num'],
                    'available_inventory' => isset($product['available_inventory']) ? $product['available_inventory'] : 0,
                    'is_deleted'          => GlobalEnums::IS_NO_DELETED,
                    'created_at'          => $current_time,
                    'updated_at'          => $current_time
                ];
        }
        $apply_product = new MaterialWmsApplyProductModel();
        if (!empty($products) && !$apply_product->batch_insert($products)) {
            throw new BusinessException('耗材申请单详情添加失败: ' . json_encode($products, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_product), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
        }
        //申请单附件信息入库
        if (!empty($params['attachments'])) {
            $material_attachment = new MaterialAttachmentModel();
            $attach_arr           = [];
            foreach ($params['attachments'] as $attachment) {
                $tmp                    = [];
                $tmp['oss_bucket_type'] = Enums::OSS_MATERIAL_TYPE_WMS_APPLY;
                $tmp['oss_bucket_key']  = $apply_model->id;
                $tmp['sub_type']        = 0;
                $tmp['bucket_name']     = $attachment['bucket_name'];
                $tmp['object_key']      = $attachment['object_key'];
                $tmp['file_name']       = $attachment['file_name'];
                $attach_arr[]            = $tmp;
            }
            if (!$material_attachment->batch_insert($attach_arr)) {
                throw new BusinessException('耗材申请附件添加失败: ' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($material_attachment), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }
        }
    }

    /**
     * 领用申请-重新提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function recommit(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $main_model = MaterialWmsApplyModel::findFirst([
                'conditions' => 'id = :id: and staff_id = :staff_id: and status in({status:array}) and is_deleted = :is_deleted:',
                'bind'       => ['id' => $params['id'], 'staff_id' => $user['id'], 'status' => [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_apply_not_submit'), ErrCode::$VALIDATE_ERROR);
            }

            [$product_list, $sau_sku]   = $this->validationSubmit($params);
            $apply_product      = $main_model->getProducts();
            $apply_product_data = $apply_product->toArray();

            $current_time           = date('Y-m-d H:i:s', time());
            $params['current_time'] = $current_time;
            $old_workflow_no        = $main_model->workflow_no;
            $order_data             = $this->getApplyData($params, 'recommit');
            $bool                   = $main_model->i_update($order_data);
            if ($bool === false) {
                throw new BusinessException('耗材申请单重新提交失败:' . json_encode($order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }
            //审批流归档到历史审批日志表中
            $by_workflow_model = new ByWorkflowBusinessRelModel();
            $by_workflow_data  = [
                'biz_value'   => $main_model->id,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                'workflow_no' => $old_workflow_no,
                'created_at'  => $current_time,
                'updated_at'  => $current_time
            ];
            $bool              = $by_workflow_model->i_create($by_workflow_data);
            if ($bool === false) {
                throw new BusinessException('耗材申请单重新提交添加by审批日志失败:' . json_encode($by_workflow_data,
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_model),
                    ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }

            //删除掉原来的耗材明细行
            $delete_apply_product = $apply_product->delete();
            if ($delete_apply_product === false) {
                throw new BusinessException('耗材申请单重新提交删除耗材明细行失败: ' . json_encode($apply_product_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($apply_product), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }

            //重新提交时先删除原来的附件信息
            $delete_attachment = $db->updateAsDict(
                'material_attachment',
                ['deleted' => MaterialClassifyEnums::IS_DELETED_YES], 'oss_bucket_key = ' . $main_model->id . ' and oss_bucket_type = ' . Enums::OSS_MATERIAL_TYPE_WMS_APPLY
            );
            if (!$delete_attachment) {
                throw new BusinessException('耗材申请单重新提交删除原附件失败: ' . json_encode(['oss_bucket_key' => $main_model->id, 'oss_bucket_type' => Enums::OSS_MATERIAL_TYPE_WMS_APPLY], JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_APPLY_ADD_ERROR);
            }
            $sau_sku_arr = array_column($sau_sku, null, 'sau_id');
            $this->addApplyAttached($params, $main_model, $sau_sku_arr, $product_list);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_apply_recommit failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 代替申请耗材需抄送收货人
     * @param object $apply_info 耗材申请单对象信息
     * @return bool
     */
    private function sendCCToConsignee($apply_info)
    {
        if (!empty($apply_info) && $apply_info->is_instead_apply == MaterialWmsEnums::IS_INSTEAD_APPLY_YES) {
            try {
                (new ByWorkflowService())->sendCC([
                    'staff_info_id' => $apply_info->consignee_id,
                    'remind_title' => static::$t->_('send_cc_to_consignee_msg_title.' . strtolower(get_country_code())),
                    'remind_content' => $apply_info->id,
                    'audit_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                    'serial_no' => $apply_info->workflow_no,
                    'message_category' => 112//耗材申请-收货提醒
                ]);
            } catch (\Exception $e) {
                $this->logger->notice('耗材申请-调取by抄送接口-请求失败，申请单号：[ ' . $apply_info->apply_no . ' ]，原因是：' . $e->getMessage());
                return false;
            }
        }
        return true;
    }

    /**
     * 领用申请-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //检测该申请单是否是待审核状态
            $main_model = MaterialWmsApplyModel::findFirst([
                'conditions' => 'id = :id: and staff_id = :staff_id: and status = :status: and is_deleted = :is_deleted:',
                'bind'       => ['id'         => $params['id'],
                                 'staff_id'   => $user['id'],
                                 'status'     => Enums::WF_STATE_PENDING,
                                 'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            $by_workflow = new ByWorkflowService();
            $by_workflow->audit(['serial_no' => $main_model->workflow_no, 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS, 'reason' => $params['reason'], 'status' => ByWorkflowEnums::BY_OPERATE_CANCEL, 'operator_id' => $user['id']]);

            //单据撤回
            $current_time      = date('Y-m-d H:i:s', time());
            $cancel_order_data = [
                'operation_remark' => $params['reason'],
                'status'           => Enums::WF_STATE_CANCEL,
                'updated_at'       => $current_time
            ];
            $bool              = $main_model->i_update($cancel_order_data);
            if ($bool === false) {
                throw new BusinessException('耗材申请单撤回失败 处理数据:' . json_encode($cancel_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_APPLY_CANCEL_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_apply_cancel failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 领用申请-驳回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            [$main_model, $products] = $this->validationAudit($params);
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit(
                ['serial_no'   => $main_model->workflow_no,
                 'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                 'reason'      => $params['reason'],
                 'status'      => ByWorkflowEnums::BY_OPERATE_REJECT,
                 'operator_id' => $user['id']
                ]);
            $current_time      = date('Y-m-d H:i:s', time());
            $reject_order_data = [
                'operation_remark' => $params['reason'],
                'status'           => Enums::WF_STATE_REJECTED,
                'updated_at'       => $current_time
            ];
            $bool              = $main_model->i_update($reject_order_data);
            if ($bool === false) {
                throw new BusinessException('耗材审核驳回失败: ' . json_encode($reject_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_APPLY_REJECT_ERROR);
            }

            //更新上次申请数量
            $params_products = array_column($params['products'], null, 'id');
            foreach ($products as $product) {
                $update_product = [
                    'last_time_num' => $params_products[$product->id]['now_time_num'],
                    'updated_at'    => $current_time
                ];
                $bool           = $product->i_update($update_product);
                if ($bool === false) {
                    throw new BusinessException('耗材申请单驳回失败]:' . json_encode($update_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_WMS_APPLY_REJECT_ERROR);
                }
            }

            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $reject_log            = [
                'biz_type'      => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                'biz_value'     => $main_model->id,
                'staff_id'      => $main_model->staff_id,
                'approval_id'   => $user['id'],
                'status'        => Enums::WF_STATE_REJECTED,
                'approval_time' => $current_time,
                'audit_method'  => $params['is_batch'] ?? 0,
                'created_at'    => $current_time,
                'updated_at'    => $current_time
            ];
            $bool                  = $by_workflow_audit_log->i_create($reject_log);
            if ($bool === false) {
                throw new BusinessException('耗材审核驳回失败: ' . json_encode($reject_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$MATERIAL_WMS_APPLY_REJECT_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_apply_reject failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 可审批规则验证
     * @param array $params 请求参数组
     * @return array
     * @throws ValidationException
     */
    private function validationAudit($params)
    {
        $main_model = MaterialWmsApplyModel::findFirst([
            'conditions' => 'id = :id: and status = :status: and is_deleted = :is_deleted:',
            'bind'       => [
                'id'         => $params['id'],
                'status'     => Enums::WF_STATE_PENDING,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ]);
        if (empty($main_model)) {
            throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
        }
        if ($main_model->is_batch_lock == MaterialWmsEnums::MATERIAL_WMS_IS_BATCH_LOCK && empty($params['is_batch'])) {
            //该单据正在审批，请勿重复审批
            throw new ValidationException(static::$t->_('material_wms_apply_ing_audit'), ErrCode::$VALIDATE_ERROR);
        }
        $products            = $main_model->getProducts();
        $db_products_ids     = array_column($products->toArray(), 'id');
        $params_products_ids = array_column($params['products'], 'id');
        if (!empty(array_diff($db_products_ids, $params_products_ids)) || !empty(array_diff($params_products_ids, $db_products_ids))) {
            throw new ValidationException(static::$t->_('material_wms_apply_product_audit_count_error'), ErrCode::$VALIDATE_ERROR);
        }
        return [$main_model, $products];
    }

    /**
     * 领用申请-通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            [$main_model, $products] = $this->validationAudit($params);

            $this->validationSaveUseVal($products, $params);
            $by_workflow = new ByWorkflowService();
            $result      = $by_workflow->audit([
                'serial_no'   => $main_model->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                'reason'      => $params['reason'] ?? '',
                'status'      => ByWorkflowEnums::BY_OPERATE_PASS,
                'operator_id' => $user['id']
            ]);

            $current_time = date('Y-m-d H:i:s', time());
            if (!empty($result) && !empty($result['is_final']) && $result['is_final'] == 1) {
                $pass_order_data = [
                    'status'     => Enums::WF_STATE_APPROVED,
                    'approve_at' => $current_time,
                    'updated_at' => $current_time
                ];
                $bool            = $main_model->i_update($pass_order_data);
                if ($bool === false) {
                    throw new BusinessException('耗材终审核通过失败: ' . json_encode($pass_order_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$MATERIAL_WMS_APPLY_PASS_ERROR);
                }
            }

            //更新上次申请数量
            $params_products = array_column($params['products'], null, 'id');
            foreach ($products as $product) {
                $update_product = [
                    'last_time_num' => $params_products[$product->id]['now_time_num'],
                    'updated_at'    => $current_time
                ];
                $bool           = $product->i_update($update_product);
                if ($bool === false) {
                    throw new BusinessException('耗材申请单审核通过失败]:' . json_encode($update_product, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($product), ErrCode::$MATERIAL_WMS_APPLY_PASS_ERROR);
                }
            }
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $pass_log              = [
                'biz_type'      => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                'biz_value'     => $main_model->id,
                'staff_id'      => $main_model->staff_id,
                'approval_id'   => $user['id'],
                'status'        => Enums::WF_STATE_APPROVED,
                'approval_time' => $current_time,
                'audit_method'  => $params['is_batch'] ?? 0,
                'created_at'    => $current_time,
                'updated_at'    => $current_time
            ];
            $bool                  = $by_workflow_audit_log->i_create($pass_log);
            if ($bool === false) {
                throw new BusinessException('耗材审核通过失败: ' . json_encode($pass_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$MATERIAL_WMS_APPLY_PASS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('material_wms_apply_pass failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 耗材申请-自动通过(BY回调)
     * @param array $params 请求参数组
     * @return array
     */
    public function autoPass(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $result = true;

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $main_model = MaterialWmsApplyModel::findFirst([
                'conditions' => 'workflow_no = :workflow_no: AND is_deleted = :is_deleted:',
                'bind' => [
                    'workflow_no' => $params['workflow_no'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_PENDING) {
                $approve_at = show_time_zone($params['approval_time']);

                $pass_data = [
                    'status' => Enums::WF_STATE_APPROVED,
                    'approve_at' => $approve_at,
                    'updated_at' => date('Y-m-d H:i:s'),
                ];

                if ($main_model->i_update($pass_data) === false) {
                    throw new BusinessException('耗材自动通过异常-主表更新失败, 原因可能是: ' . get_data_object_error_msg($main_model), ErrCode::$BUSINESS_ERROR);
                }

                $by_workflow_audit_log = new ByWorkflowAuditLogModel();
                $pass_log = [
                    'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
                    'biz_value' => $main_model->id,
                    'staff_id' => $main_model->staff_id,
                    'approval_id' => $params['approval_id'],
                    'status' => Enums::WF_STATE_APPROVED,
                    'approval_time' => $approve_at,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($by_workflow_audit_log->i_create($pass_log) === false) {
                    throw new BusinessException('耗材自动通过异常-BY审批日志创建失败: data=' . json_encode($pass_log, JSON_UNESCAPED_UNICODE) . '; 原因可能是' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $result = false;
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('material_wms_apply_auto_pass failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result
        ];
    }

    /**
     * 网点领用记录
     * @param object $apply 耗材领用记录
     * @return  array
     **/
    public function applyRecord(object $apply)
    {
        $month_start = date('Y-m-01', strtotime($apply->created_at)) . ' 00:00:00';
        $columns     = 'mwap.barcode, mwap.name_zh, mwap.name_en, mwap.name_local, mwap.unit_zh, mwap.unit_en, mwap.model, sum(mwap.last_time_num) as apply_num, mwap.created_at as apply_time';
        $builder     = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['mwap' => MaterialWmsApplyProductModel::class]);
        $builder->leftjoin(MaterialWmsApplyModel::class, 'mwap.apply_id = mwa.id', 'mwa');
        $builder->betweenWhere('mwap.created_at', $month_start, $apply->created_at);
        $builder->andWhere('mwa.status = :status:', ['status' => Enums::WF_STATE_APPROVED]);
        $builder->andWhere('mwa.use_land_id = :use_land_id:', ['use_land_id' => $apply->use_land_id]);
        $builder->groupBy('mwap.barcode');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 查看申请-详情
     * @param array $params 参数
     * @param int $type 1 申请详情 2审批详情 3数据查询详情
     * @param array $user 用户数据
     * @return array
     */
    public function detail(array $params, int $type, array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail  = [];
        try {
            $apply = MaterialWmsApplyModel::findFirst([
                'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                'bind'       => [
                    'id'         => $params['id'],
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($apply)) {
                throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY) {
                if ($apply->staff_id != $user['id']) {
                    throw new ValidationException(static::$t->_('material_wms_apply_no_auth'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $detail = $apply->toArray();
            //审核增加网点领用记录
            if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT) {
                $detail['apply_record'] = $this->applyRecord($apply);
            }
            $detail['delivery_way_name'] = static::$t->_(MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]);

            $detail['attachments'] = $apply->getAttachment()->toArray();
            //耗材明细
            $detail['products'] = $apply->getProducts()->toArray();
            $detail['products'] = $this->handleDetail($detail, $detail['products'], $type);
            //审批日志
            $detail['auth_logs'] = (new ByWorkflowService())->log(['serial_no' => $detail['workflow_no'], 'operator_id' => $user['id'], 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS]);
            //历史审批记录
            $detail['history_auth_logs'] = $this->getApplyHistoryAuthLog($apply->id);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_apply_detail_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $detail
        ];
    }

    /**
     * 查看申请-详情-针对by端
     * @param array $params 参数组
     * @param int $type 1:申请单详情，2审批详情，3数据查询详情
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detailForBy(array $params, int $type, array $user = [])
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail  = [];
        try {
            $workflow_no = $params['workflow_no'];
            $apply = MaterialWmsApplyModel::findFirst([
                'conditions' => 'workflow_no = :workflow_no: and is_deleted = :is_deleted:',
                'bind'       => ['workflow_no' => $workflow_no, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (empty($apply)) {
                $rel = ByWorkflowBusinessRelModel::findFirst([
                    'columns'    => 'biz_value',
                    'conditions' => 'workflow_no = :workflow_no: and biz_type = :biz_type:',
                    'bind'       => ['workflow_no' => $workflow_no, 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS]
                ]);
                if (!empty($rel)) {
                    $apply = MaterialWmsApplyModel::findFirst([
                        'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                        'bind'       => ['id' => $rel->biz_value, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
                    ]);
                    if (empty($apply)) {
                        throw new ValidationException(static::$t->_('material_wms_apply_no_existed'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            if ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY) {
                if ($apply->staff_id != $user['id']) {
                    throw new ValidationException(static::$t->_('material_wms_apply_no_auth'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $detail             = $apply->toArray();
            $detail             = $apply->toArray();
            $detail['delivery_way_name'] = static::$t[MaterialClassifyEnums::$delivery_way_arr[$detail['delivery_way']]];
            $detail['products'] = $apply->getProducts()->toArray();
            $detail['products'] = $this->handleDetail($detail, $detail['products'], $type);
            //附件明细
            $detail['attachments'] = $apply->getAttachment()->toArray();
            foreach ($detail['products'] as &$product) {
                if (!empty($product['pic'])) {
                    $barcode_pic           = [];
                    foreach ($product['pic'] as $pic) {
                        $barcode_pic[] = gen_file_url($pic);
                    }
                    $product['pic'] = $barcode_pic;
                }
            }

            $attachment_pic = [];
            if (!empty($detail['attachments'])) {
                foreach ($detail['attachments'] as $attachment) {
                    $attachment_pic[] = gen_file_url($attachment);
                }
                $detail['attachments'] = $attachment_pic;
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_apply_detail_for_by failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $detail
        ];
    }

    /**
     * 获取申请单的历史审批日志
     * @param int $id 申请单ID
     * @return array
     */
    public function getApplyHistoryAuthLog(int $id)
    {
        $history = ByWorkflowBusinessRelModel::find([
            'conditions' => 'biz_value = :biz_value: and biz_type = :biz_type:',
            'bind'       => ['biz_value' => $id, 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_WMS],
            'columns'    => 'workflow_no',
            'order'      => 'id desc',
            'limit'      => GlobalEnums::DEFAULT_PAGE_SIZE
        ])->toArray();
        if (!empty($history)) {
            $info = static::$t->_('material_wms_apply_view_details');
            foreach ($history as &$item) {
                $item['workflow_no_text'] = $item['workflow_no'] . $info;
            }
        }
        return $history;
    }

    /**
     * 格式化详情信息
     * @param array $apply_info 申请单信息组
     * @param array $product_list 申请单明细列表
     * @param integer $type 1:申请单详情，2审批详情，3数据查询详情
     * @return array
     * @throws ValidationException
     */
    private function handleDetail(array $apply_info, array $product_list, int $type)
    {
        if (!empty($product_list)) {
            $barcode_arr = array_values(array_filter(array_unique(array_column($product_list, 'barcode'))));
            $material_sau_barcode = [];
            if (!empty($barcode_arr)) {
                $barcode_list = MaterialSauModel::find([
                    'columns'    => 'id, barcode, update_to_scm, category_type',
                    'conditions' => 'barcode in ({barcode:array}) and is_deleted = :is_deleted:',
                    'bind'       => [
                        'barcode'    => $barcode_arr,
                        'is_deleted' => GlobalEnums::IS_NO_DELETED
                    ]
                ])->toArray();
                if (!empty($barcode_list)) {
                    $material_sau_barcode  = array_column($barcode_list, null, 'barcode');
                    //更新至scm的barcode组
                    $update_to_scm_barcode       = [];
                    $barcode_available_inventory = [];
                    if (
                        ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY &&  $apply_info['status'] != Enums::WF_STATE_APPROVED)
                        ||
                        ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT && $apply_info['status'] == Enums::WF_STATE_PENDING)
                        ||
                        ($type == MaterialWmsEnums::WMS_LIST_TYPE_APPLY_DATA && $apply_info['status'] == Enums::WF_STATE_PENDING)
                    ) {
                        foreach ($barcode_list as $item) {
                            if ($item['update_to_scm'] == MaterialClassifyEnums::MATERIAL_CATEGORY_NO) {
                                $update_to_scm_barcode[] = $item['barcode'];
                            }
                        }
                        if ($update_to_scm_barcode) {
                            //17404【ALL|OA/BY 耗材管理】耗材出库拆分以及库存查询逻辑优化
                            $store_storage_rules_list = WarehouseDivisionRuleService::getInstance()->getStoreStorageRulesList(['company_id' => $apply_info['company_id'], 'use_land_id' => $apply_info['use_land_id']]);
                            if (!empty($store_storage_rules_list)) {
                                //一个设置下同一个公司的货主是唯一的
                                $mach_code = $store_storage_rules_list[0]['mach_code'];
                                $warehouse_id = implode(',', array_column($store_storage_rules_list, 'stock_id'));
                                //开始处理更新至scm的各个barcode的可用库存
                                $barcode_available_inventory = AssetOutStorageService::getInstance()->handleUpdateToScmBarcode($update_to_scm_barcode, $mach_code, $warehouse_id);
                            } else {
                                $scm_service             = new ScmService();
                                $default_scm_cargo_owner = $scm_service->getDefaultScmCargoOwner();
                                $mach_code               = !empty($default_scm_cargo_owner) ? $default_scm_cargo_owner->mach_code : '';

                                $barcode_available_inventory = AssetOutStorageService::getInstance()->handleGroupMerge($update_to_scm_barcode, $mach_code, '', $apply_info['company_id'], static::$language);
                            }
                        }
                    }
                    $barcode_key_list   = array_column($barcode_list, null, 'barcode');
                    $materialAttachment = new  MaterialAttachmentModel();
                    $pic_arr_key_arr    = $materialAttachment->getColumnArr($barcode_list);

                    $use_val_arr = $this->getMaterialSauSkuByUseVal($barcode_list);
                    foreach ($product_list as &$product) {
                        $product['now_time_num'] = $product['last_time_num'];
                        $product['update_to_scm'] = $material_sau_barcode[$product['barcode']]['update_to_scm'] ?? '';
                        $product['category_type'] = $material_sau_barcode[$product['barcode']]['category_type'] ?? '';
                        if (!empty($barcode_key_list[$product['barcode']])) {
                            $product['available_inventory'] = !empty($barcode_available_inventory) ? $barcode_available_inventory[$product['barcode']] : $product['available_inventory'];
                            $product['pic']                 = $pic_arr_key_arr[$barcode_key_list[$product['barcode']]['id']] ?? [];
                        } else {
                            $product['pic'] = [];
                        }
                        $product['use_val'] = $use_val_arr[$product['barcode']] ?? 0;
                    }
                }
            }
        }
        return $product_list ?? [];
    }

    /**
     * 耗材申请-查看出库信息
     * @param array $params 请求参数组
     * @return array
     */
    public function getWmsOutStorageList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $apply = MaterialWmsApplyModel::findFirst([
                'conditions' => 'workflow_no = :workflow_no: and is_deleted = :is_deleted:',
                'bind'       => [
                    'workflow_no' => $params['workflow_no'],
                    'is_deleted'  => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (empty($apply)) {
                throw new ValidationException(static::$t->_('material_wms_out_storage_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            $data['delivery_way_type'] = $apply->delivery_way;
            if ($apply->delivery_way ==  MaterialClassifyEnums::DELIVERY_WAY_EXPRESS) {
                $storage_arr = [];
                if ($apply->status == Enums::WF_STATE_APPROVED) {
                    //只有申请人名下审核已通过的申请单才需要去查询关联的出库单信息
                    $storage_arr = MaterialWmsOutStorageModel::find([
                        'columns'    => 'wms_no as no, mach_code, scm_no, is_new',
                        'conditions' => 'apply_id = :apply_id: and status = :status: and is_deleted = :is_deleted:',
                        'bind'       => [
                            'apply_id'      => $apply->id,
                            'status'        => MaterialWmsEnums::STATUS_OUT,
                            'is_deleted'    => GlobalEnums::IS_NO_DELETED
                        ],
                        'order'      => 'id DESC'
                    ])->toArray();
                }
                //出库信息需要返回scm那边的出库状态
                if (!empty($storage_arr)) {
                    $scm = new ScmService();
                    foreach ($storage_arr as &$item) {
                        try {
                            $res = $scm->outboundOrderStatus($item['mach_code'], $item['scm_no']);
                            $scm_out_status = $res['status'];
                            $scm_out_status_text = $scm_out_status ? static::$t->_(ScmEnums::$scm_out_status[$scm_out_status]) : '';
                        } catch (\Exception $e) {
                            $scm_out_status = 0;
                            $scm_out_status_text = '';
                        }
                        $item['scm_out_status'] = $scm_out_status;
                        $item['scm_out_status_text'] = $scm_out_status_text;
                    }
                }
                $data['storage_list'] = $storage_arr;
            } else {
                $data['self_list'] = WmsOutStorageService::getInstance()->getSelfList($apply);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_apply_get_wms_out_storage_list failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }

    /**
     * 耗材申请-查看出库的数据
     * @param string $locale 当前语种
     * @param array $params 请求参数组
     * @return array
     */
    public function getOutboundTrackingInfo(string $locale, array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $scm  = new ScmService();
            $data = $scm->getOutboundTrackingInfo($params['mach_code'], $params['no'], $locale);
            if (!empty($data['trackingInfo'])) {
                foreach ($data['trackingInfo'] as &$item) {
                    if (!empty($item['routes'])) {
                        foreach ($item['routes'] as &$route) {
                            $route['routeTime'] = date('Y-m-d H:i:s', $route['routeTime']);
                        }
                    }
                }
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_apply_get_out_bound_tracking_info failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }

    /**
     * 耗材领用申请-审批日志
     * @param array $params 条件
     * @param array $user 用户数据
     * @return array
     */
    public function historyApprovalLog(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $by_workflow = new ByWorkflowService();
            $data = $by_workflow->log([
                'serial_no'   => $params['workflow_no'],
                'operator_id' => $user['id'],
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_WMS
            ]);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_history_approval_log failed:' . $real_message . json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }


    /**
     * 根据barcode 返回最小倍数
     * @param array $barcode barcode
     * @return mixed
     */
    private function getMaterialSauList(array $barcode)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ms.id', 'ms.barcode', 'mss.use_val, ms.category_type, ms.status, ms.is_deleted'
        ]);
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialSauSkuModel::class, 'mss.sau_id = ms.id', 'mss');
        $builder->inWhere('ms.barcode', $barcode);
        $storage = $builder->getQuery()->execute()->toArray();
        return $storage ? array_column($storage, null, 'barcode') : [];
    }


    /**
     * 驳回和审核通过修改申请数量和倍数校验
     * @param object $products barcode下的查询数据
     * @param array $params 提交的数据
     * @return mixed
     */
    private function validationSaveUseVal(object $products, array $params)
    {
        $products_arr = $products->toArray();
        if (empty($products_arr)) {
            throw new ValidationException(static::$t->_('material_wms_apply_product_not_null'), ErrCode::$VALIDATE_ERROR);
        }
        $sau_sku = $this->getMaterialSauList(array_values(array_column($products_arr, 'barcode')));

        if (empty($sau_sku)) {
            throw new ValidationException(static::$t->_('material_wms_barcode_existed'), ErrCode::$VALIDATE_ERROR);
        }

        //已经删除的sku是否增加校验
        if (in_array(GlobalEnums::IS_DELETED, array_unique(array_column($sau_sku, 'is_deleted')))) {
            throw new ValidationException(static::$t->_('material_wms_barcode_del'), ErrCode::$VALIDATE_ERROR);
        }
        //非耗材类的barcode
        $not_wms_barcode  = $wms_status_barcode = [];
        $products_barcode = array_column($params['products'], null, 'barcode');
        foreach ($sau_sku as $item) {
            if (!empty($products_barcode[$item['barcode']])) {
                if ($item['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $products_barcode[$item['barcode']]['now_time_num'] > 0) {
                    $not_wms_barcode[] = $item['barcode'];
                } elseif (!empty($item['use_val']) && !empty($products_barcode[$item['barcode']]['now_time_num'] % $item['use_val'])) {
                    //不是倍数
                    throw new ValidationException(static::$t->_('material_wms_use_val_not_times'), ErrCode::$VALIDATE_ERROR);
                } elseif ($item['status'] == MaterialClassifyEnums::MATERIAL_PROHIBITED_USE && $products_barcode[$item['barcode']]['now_time_num'] > 0) {
                    $wms_status_barcode[] = $item['barcode'];
                }
            }
        }
        if (!empty($wms_status_barcode)) {
            //XXX、XXX(获取所有数量不为0并且状态禁用的barcode)已禁用，请将数量调整为0
            throw new ValidationException(static::$t->_('material_not_wms_barcode_status_num_zero', ['barcode' => implode(',', $wms_status_barcode)]), ErrCode::$VALIDATE_ERROR);
        }
        //验证是否存在非耗材类的barcode且申请数量大于0的数据
        if (!empty($not_wms_barcode)) {
            throw new ValidationException(static::$t->_('material_not_wms_barcode_max_num_zero', ['barcode' => implode(";", $not_wms_barcode)]), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }


    /**
     * 获取Sku表里面的领用单位
     * @param array $barcode_list 数据
     * @return array
     */
    public function getMaterialSauSkuByUseVal(array $barcode_list)
    {
        $sau_ids            = array_values(array_column($barcode_list, 'id'));
        $sau_barcode_id     = array_unique(array_column($barcode_list, 'barcode', 'id'));
        $material_sau_sku   = MaterialSauSkuModel::find([
            'columns'    => 'id, sau_id, use_val',
            'conditions' => 'sau_id in ({sau_id:array}) and is_deleted = :is_deleted:',
            'bind'       => [
                'sau_id'     => $sau_ids,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ])->toArray();
        $sau_sku_by_use_val = $barcode_use_val = [];
        if (!empty($material_sau_sku)) {
            $sau_sku_by_use_val = array_column($material_sau_sku, 'use_val', 'sau_id');
        }
        //返回 barcode=>use_val 的数据
        foreach ($sau_barcode_id as $key => $value) {
            $barcode_use_val[$value] = $sau_sku_by_use_val[$key];
        }
        return $barcode_use_val;
    }


    /**
     * 搜索barcode
     * @param array $params 搜索条件
     * @return array
     */
    public function searchBarcodeList($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $items   = [];
        try {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
            $builder   = $this->modelsManager->createBuilder();
            $builder->from(['ms' => MaterialSauModel::class]);
            if (isset($params['barcode'])) {
                $builder->andWhere('ms.barcode like :barcode:', ['barcode' => '%' . $params['barcode'] . '%']);
            }
            $builder->columns('count(ms.id) as total');
            $total_info = $builder->getQuery()->getSingleResult();
            $count      = intval($total_info->total);
            if ($count > 0) {
                $builder->columns('ms.barcode');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data = [
                'items'      => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page'     => $page_size,
                    'total_count'  => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('material_wms_apply_audit_search_barcode failed:' . $real_message . ' select :' . json_encode($params));
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];

    }


    /**
     * 耗材批量审核 上传结果查询
     * @param array $params 查询条件
     * @return array
     */
    public function getImportAuditResult($params)
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult($params['type'], $params['staff_id']);
    }


    /**
     * 耗材管理 耗材审核 批量审核 上传 写入任务
     * @param object $file 文件
     * @param array $user 用户数据
     * @param int $type 类型 8 耗材审核 批量审核 上传
     * @return  array
     **/
    public function addBatchUploadTask(object $file, array $user, int $type)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $file_path = sys_get_temp_dir() . '/' . $file->getName();
            $file->moveTo($file_path);
            $oss_result = OssHelper::uploadFile($file_path);
            if (!isset($oss_result['object_url']) || empty($oss_result['object_url'])) {
                throw new ValidationException(self::$t['file_upload_error'], ErrCode::$VALIDATE_ERROR);
            }
            // 导入中心
            $bool = ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], $type);
            if (!$bool) {
                throw new ValidationException(self::$t['add_import_center_error'], ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('耗材管理_耗材审核_批量审核_上传 写入任务失败 原因是' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $code == ErrCode::$SUCCESS
        ];
    }


    /**
     * 获取待审核下的条数总数
     * @param array $condition 筛选条件组
     * @return array
     * @throws ValidationException
     */
    public function getAuditListCount($condition)
    {
        $page_size = empty($condition['pageSize']) ? MaterialWmsEnums::MATERIAL_WMS_AUDIT_MAX_EXPORT : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $builder   = $this->modelsManager->createBuilder();
        $builder->columns('main.workflow_no');
        $builder->from(['main' => MaterialWmsApplyModel::class]);
        if (!empty($condition['barcode'])) {
            $builder->leftjoin(MaterialWmsApplyProductModel::class, 'main.id=product.apply_id', 'product');
        }
        $builder     = $this->getCondition($builder, $condition, MaterialWmsEnums::WMS_LIST_TYPE_APPLY_AUDIT);
        $search_list = $builder->getQuery()->execute()->toArray();
        $data        = [];
        if (!empty($search_list)) {
            $workflow_no         = array_column($search_list, 'workflow_no');
            $by_list_params      = [
                'serial_no'   => $workflow_no,
                'biz_type'    => [ByWorkflowEnums::BY_BIZ_TYPE_WMS],
                'approval_id' => $condition['user_id'],
                'state'       => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                'page_num'    => $page_num,
                'page_size'   => $page_size
            ];
            $result              = (new ByWorkflowService())->getList($by_list_params);
            $data['total_count'] = !empty($result['total_count']) ? $result['total_count'] : 0;
            $data['serial_no']   = array_values(array_column($result['list'], 'serial_no'));
        }
        return $data;
    }


    /**
     * 批量审核数据处理
     * @param array $excel_data 批量数据
     * @param int $user_id 批量操作人id
     * @param int $update_result_column 备注写入列
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function batchAuditEdit(array $excel_data, int $user_id, int $update_result_column)
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = $real_trace = '';
        $clear_excel_data = $correct_data = $error_data = [];

        $this->logger->info('耗材批量审核 开始处理数据 上传的数据为' . json_encode($excel_data, JSON_UNESCAPED_UNICODE));
        foreach ($excel_data as $key => $row) {
            //只有申请单号和barcode同时为空视为空跳过
            if (empty($row[0]) && empty($row[1])) {
                continue;
            }
            $clear_excel_data[$key] = $row;
        }
        $db = $this->getDI()->get('db_oa');
        try {
            $result_data = $clear_excel_data;
            //为excel数据增加key
            $data = $this->excelToDataEdit($clear_excel_data);
            //查询数据
            $apply_all = $this->getWmsApplyList(array_values(array_filter(array_unique(array_column($data, 'apply_no')))));
            $this->logger->info('耗材批量审核按照申请单 查询表数据为' . json_encode($apply_all, JSON_UNESCAPED_UNICODE));

            //增加锁数据 $apply_no 被锁的数据 $apply_no_lock本次未锁的数据
            [$apply_no, $apply_no_lock] = $this->addWmsApplyLock($apply_all, $db);
            $this->logger->info('耗材批量审核批量加锁数据' . json_encode($apply_no, JSON_UNESCAPED_UNICODE) . '本次被其他增加过锁的数据是 ' . json_encode($apply_no_lock, JSON_UNESCAPED_UNICODE));

            //申请单数据格式化
            $apply_handle_data = $this->wmsApplyHandleData($apply_all);
            //得到最新的最小倍数数据
            $apply_use_val_data = array_column($apply_all, 'use_val', 'barcode');
            //上传的excel数据格式化用作校验比对使用
            $apply_excel_data = $this->wmsApplyHandleData($data);

            //得到审批数据
            $workflow_data = $this->getPending($user_id, array_values(array_unique(array_column($apply_all, 'workflow_no'))));
            $this->logger->info('耗材批量审核  查询by数据为' . json_encode($workflow_data, JSON_UNESCAPED_UNICODE));
            //数据校验开始

            $correct_and_error = $this->validationBatchEdit($data, $apply_handle_data, $apply_excel_data, $apply_use_val_data, $workflow_data);

            $correct_data = $correct_and_error['correct_data'];
            $error_data   = $correct_and_error['error_data'];

            if ($correct_data || $error_data) {
                foreach ($correct_data as $index => $cv) {
                    $result_data[$index][$update_result_column] = self::$t['excel_result_validation_pass'];
                }
                foreach ($error_data as $err_index => $ev) {
                    $result_data[$err_index][$update_result_column] = $ev['error_message'];
                }
            }
            $this->logger->info('耗材批量审核  校验之后的数据为' . json_encode($result_data, JSON_UNESCAPED_UNICODE));
            //开始批量审核
            if (!empty($correct_data)) {
                $correct_data_arr = $this->rejectHandleData($correct_data, $error_data);
                if (!empty($correct_data_arr)) {
                    $user['id'] = $user_id;
                    foreach ($correct_data_arr as $k => $correct_item) {
                        $correct_item['is_batch'] = 1;
                        if ($correct_item['approval_result'] == MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_AGREE) {
                            //审核
                            $pass_info = $this->pass($correct_item, $user);
                            $this->logger->info('耗材批量审核 审核数据' . json_encode($correct_item, JSON_UNESCAPED_UNICODE) . '审核返回数据' . json_encode($pass_info, JSON_UNESCAPED_UNICODE));
                        } elseif ($correct_item['approval_result'] == MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_REJECT) {
                            //驳回
                            $reject_info = $this->reject($correct_item, $user);
                            $this->logger->info('耗材批量审核 驳回数据' . json_encode($correct_item, JSON_UNESCAPED_UNICODE) . '驳回返回数据' . json_encode($reject_info, JSON_UNESCAPED_UNICODE));
                        }
                    }
                }
            }
            //批量解锁
            $this->logger->info('耗材批量审核 开始解锁  数据为' . json_encode($apply_no, JSON_UNESCAPED_UNICODE));
            $this->unWmsApplyLock($apply_no, $db);

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->error('耗材批量审核更新失败 ' . $real_message . '; trace=' . $real_trace);
            $correct_data = [];
            $error_data   = $clear_excel_data;
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [
                'excel_data'  => $result_data,
                'all_num'     => count($clear_excel_data),
                'success_num' => count($correct_data),
                'failed_sum'  => count($error_data)
            ]
        ];
    }


     /**
     * 处理数据
     * @param $excel_data
     * @return array
     */
    public function excelToDataEdit($excel_data)
    {
        //excel转字段
        $data_key = [
            'apply_no',
            'barcode',//barcode
            'created_at',//申请时间
            'staff_id',//申请人工号
            'staff_name',//申请人姓名
            'name_local_model',//耗材名称和规格型号
            'this_time_num',//申请数量
            'last_time_num',//上一次审核数据
            'use_val',//最小申请倍数
            'company_name',//申请人公司
            'node_department_name',//申请人部门
            'store_name',//申请人网点
            'use_land_name',//耗材使用网点
            'reason',//申请理由
            'approval_result',//审批结果
            'now_time_num',//审核数量
            'operation_remark',//驳回原因/通过原因
        ];
        $data     = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                $data[$k][$key] = trim($v[$index]);
            }
        }
        return $data;
    }


    /**
     * 获取本次上传的最新数据
     * @param $apply_no_arr
     * @return array
     */
    public function getWmsApplyList($apply_no_arr)
    {
        if (empty($apply_no_arr)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'mwa.apply_no', 'mwa.id', 'mwap.barcode', 'mwa.workflow_no', 'mwa.is_batch_lock', 'mwa.is_deleted', 'mwa.status', 'mwap.this_time_num as approval_result', 'mwa.reason as operation_remark', 'mwap.use_val', 'mwap.id as product_id'
        ]);
        $builder->from(['mwa' => MaterialWmsApplyModel::class]);
        $builder->leftjoin(MaterialWmsApplyProductModel::class, 'mwap.apply_id = mwa.id', 'mwap');
        $builder->inWhere('mwa.apply_no', $apply_no_arr);
        $wms_apply_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($wms_apply_arr)) {
            $barcode_by_use_val = $this->getBarcodeList(array_values(array_filter(array_unique(array_column($wms_apply_arr, 'barcode')))));
            foreach ($wms_apply_arr as &$wms_apply) {
                $wms_apply['use_val'] = $barcode_by_use_val[$wms_apply['barcode']] ?? 0;
            }
        }
        return $wms_apply_arr;
    }

    /**
     * 获取本次批量上传的barcode最新数据
     * @param $barcode_arr
     * @return array
     */
    public function getBarcodeList($barcode_arr)
    {
        if (empty($barcode_arr)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'ms.barcode', 'mss.use_val'
        ]);
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialSauSkuModel::class, 'mss.sau_id = ms.id', 'mss');
        $builder->inWhere('ms.barcode', $barcode_arr);
        $sau_sku_arr = $builder->getQuery()->execute()->toArray();
        return array_column($sau_sku_arr, 'use_val', 'barcode');
    }


    /**
     * 批量锁数据
     * @param $wms_apply_arr
     * @param $db
     * @return array
     */
    public function addWmsApplyLock($wms_apply_arr, $db)
    {
        if (empty($wms_apply_arr)) {
            return [];
        }
        $apply_no = $apply_no_lock = [];
        foreach ($wms_apply_arr as $wms_apply) {
            if (empty($wms_apply['is_batch_lock'])) {
                $apply_no[] = $wms_apply['apply_no'];
            } else {
                $apply_no_lock[] = $wms_apply['apply_no'];
            }
        }
        $apply_no_string = "'" . implode("','", array_values(array_unique($apply_no))) . "'";
        if (!empty($apply_no_string)) {
            $update_success = $db->updateAsDict(
                (new MaterialWmsApplyModel())->getSource(),
                [
                    'is_batch_lock' => MaterialWmsEnums::MATERIAL_WMS_IS_BATCH_LOCK,
                    'updated_at'    => date('Y-m-d H:i:s', time())
                ],
                [
                    'conditions' => " apply_no IN ($apply_no_string)",
                ]
            );
            if (!$update_success) {
                throw new BusinessException('耗材批量审核修改锁数据失败  数据是' . $apply_no_string, ErrCode::$MATERIAL_WMS_APPLY_BATCH_AUDIT_EDIT_ERROR);
            }
        }
        return [$apply_no, $apply_no_lock];
    }

    /**
     * 导入修改
     * 验证数据, 错误信息放到error_message
     * @param array $data excel中的数据
     * @param array $apply_handle_data 按照excel中的数据到数据库查询的数据
     * @param array $apply_excel_data excel中的数据格式化后的数据
     * @param array $apply_use_val_data 从数据库查询最小申请的倍数集合
     * @param array $workflow_data 本次审批数据
     * @return mixed
     */
    public function validationBatchEdit(array $data, array $apply_handle_data, array $apply_excel_data, array $apply_use_val_data, array $workflow_data)
    {
        $correct_data = $error_data = [];

        $sau_sku = $this->getMaterialSauList(array_values(array_unique(array_column($data, 'barcode'))));
        foreach ($data as $k => &$v) {
            $error_message = '';
            //申请单号校验
            //1检测申请单不能为空   2 检测申请单有效 3 检测申请单状态   4 检测锁状态 5 检测申请单是不是此人审核
            if (empty($v['apply_no'])) {
                //1检测申请单不能为空
                $error_message .= self::$t['material_wms_apply_apply_no_not_null'] . ';';
            } else {
                //检测申请单有效
                if (!in_array($v['apply_no'], array_keys($apply_handle_data))) {
                    $error_message .= self::$t['material_wms_apply_export_apply_no_error'] . ';';
                } else {
                    //检测申请单状态
                    if ($apply_handle_data[$v['apply_no']]['status'] != Enums::WF_STATE_PENDING) {
                        $error_message .= self::$t['material_wms_apply_apply_status_error'] . ';';
                    }
                    //文档中包含正在审批的单据，不可重复审批，请删除后重新导入
                    if ($apply_handle_data[$v['apply_no']]['is_batch_lock'] == MaterialWmsEnums::MATERIAL_WMS_IS_BATCH_LOCK) {
                        $error_message .= self::$t['material_wms_apply_export_ing_audit_not_repeat_error'] . ';';
                    }
                    //检测申请单是不是此人审核
                    if (!in_array($apply_handle_data[$v['apply_no']]['workflow_no'], $workflow_data['pending'])) {
                        $error_message .= self::$t['material_wms_apply_export_user_permission_error'] . ';';
                    }
                }
            }
            //barcode校验
            // 1 检测barcode不能为空  2 检测barcode是不是有效  3 barcode和原数据是否相等
            if (empty($v['barcode'])) {
                //检测barcode不能为空
                $error_message .= self::$t['material_wms_apply_barcode_not_null'] . ';';
            } else {
                //检测barcode是不是有效
                if (!in_array($v['barcode'], $apply_handle_data[$v['apply_no']]['barcode'])) {
                    $error_message .= self::$t['material_wms_apply_barcode_not_null'] . ';';
                }
                //barcode和原数据是否相等
                if (!empty(array_diff($apply_excel_data[$v['apply_no']]['barcode'], $apply_handle_data[$v['apply_no']]['barcode'])) || !empty(array_diff($apply_handle_data[$v['apply_no']]['barcode'], $apply_excel_data[$v['apply_no']]['barcode']))) {
                    $error_message .= self::$t['material_wms_apply_barcode_error'] . ';';

                }
                if ($sau_sku[$v['barcode']]['category_type'] != MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS && $v['now_time_num'] > 0) {
                    $error_message .= $v['barcode'] . self::$t['material_not_wms_barcode_max_num_zero_batch'] . ';';
                }
            }
            $v['id']         = $apply_handle_data[$v['apply_no']]['id']; //记录id
            $v['product_id'] = $apply_handle_data[$v['apply_no']]['product'][$v['barcode']];//记录详情id
            //审批结果校验
            // 1 审批意见必须是 Agree、Reject  2 相同的申请单意见必须相同
            if (empty($v['approval_result'])) {
                //审批结果不能为空
                $error_message .= self::$t['material_wms_apply_approval_result_not_null'] . ';';
            } else {
                if (!in_array($v['approval_result'], [MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_AGREE, MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_REJECT])) {
                    $error_message .= self::$t['material_wms_apply_approval_result_error'] . ';';
                }
                if (count(array_unique($apply_excel_data[$v['apply_no']]['approval_result'])) > 1) {
                    $error_message .= self::$t['material_wms_apply_result_not_consistent'] . ';';
                } else if ($apply_excel_data[$v['apply_no']]['approval_result'][0] == MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_AGREE && !array_sum($apply_excel_data[$v['apply_no']]['now_time_num'])) {
                    //同一个审批单,通过时 单据里所有barcode的审核为0，只能审核拒绝
                    $error_message .= self::$t['material_wms_apply_product_audit_num_zero'];
                }
            }
            //审核数量校验
            //1 审核数量必须大于等于0  2 审核数量必须是最小申请的倍数
            if ($v['now_time_num'] == '') {
                //审核数量不能为空
                $error_message .= self::$t['material_wms_apply_new_time_num_not_null'] . ';';
            } else {
                //审核数量必须大于等于0 小于100000的整数
                if (!preg_match(MaterialWmsEnums::MATERIAL_WMS_AUDIT_NUM_RULE, $v['now_time_num'])) {
                    $error_message .= self::$t['material_wms_apply_new_time_num_not_null'] . ';';
                }
                // 审核数量必须是最小申请的倍数
                if (!empty($apply_use_val_data[$v['barcode']]) && (int)$v['now_time_num'] % $apply_use_val_data[$v['barcode']] > 0) {
                    $error_message .= self::$t['material_wms_apply_min_multiple'] . ';';
                }
            }
            //驳回原因校验
            //  1 当审批驳回时，驳回原因必填   2. 驳回原因限制100字符 3 同一个审批单 驳回原因必须相同
            if (empty($v['operation_remark']) && $v['approval_result'] == MaterialWmsEnums::MATERIAL_WMS_AUDIT_APPROVAL_RESULT_REJECT) {
                $error_message .= self::$t['material_wms_apply_operation_remark_not_null'] . ';';
            } else {
                //同一个审批单 驳回原因/通过原因必须相同
                if (count(array_unique($apply_excel_data[$v['apply_no']]['operation_remark'])) > 1) {
                    $error_message .= self::$t['material_wms_apply_operation_remark_not_consistent'] . ';';
                }

                if (mb_strlen($v['operation_remark']) > 1000) {
                    $error_message .= self::$t['material_wms_apply_operation_remark_maximum_length'] . ';';
                }
            }
            if ($error_message !== '') {
                $v['error_message'] = self::$t['error_message_title'] . ' : ' . $error_message;
                $error_data[$k]     = $v;
            } else {
                $correct_data[$k] = $v;
            }
        }
        return ['correct_data' => $correct_data, 'error_data' => $error_data];
    }


    /**
     * 对上传的excel数据格式化
     * @param array $data excel中的数据
     * @return array
     */
    public function wmsApplyHandleData($data)
    {
        $rs = $value = [];
        foreach ($data as $item) {
            if (in_array($item['apply_no'], $value)) {
                $rs[$item['apply_no']]['barcode'][]                 = $item['barcode'];
                $rs[$item['apply_no']]['approval_result'][]         = $item['approval_result'] ?? '';
                $rs[$item['apply_no']]['use_val'][]                 = $item['use_val'] ?? '';
                $rs[$item['apply_no']]['operation_remark'][]        = $item['operation_remark'] ?? '';
                $rs[$item['apply_no']]['now_time_num'][]            = $item['now_time_num'] ?? '';
                $rs[$item['apply_no']]['status']                    = $item['status'] ?? '';
                $rs[$item['apply_no']]['is_batch_lock']             = $item['is_batch_lock'] ?? '';
                $rs[$item['apply_no']]['workflow_no']               = $item['workflow_no'] ?? '';
                $rs[$item['apply_no']]['id']                        = $item['id'] ?? '';
                $rs[$item['apply_no']]['product'][$item['barcode']] = $item['product_id'] ?? '';
            } else {
                $value[]                                            = $item['apply_no'];
                $rs[$item['apply_no']]['barcode'][]                 = $item['barcode'];
                $rs[$item['apply_no']]['approval_result'][]         = $item['approval_result'] ?? '';
                $rs[$item['apply_no']]['use_val'][]                 = $item['use_val'] ?? '';
                $rs[$item['apply_no']]['operation_remark'][]        = $item['operation_remark'] ?? '';
                $rs[$item['apply_no']]['now_time_num'][]            = $item['now_time_num'] ?? '';
                $rs[$item['apply_no']]['status']                    = $item['status'] ?? '';
                $rs[$item['apply_no']]['is_batch_lock']             = $item['is_batch_lock'] ?? '';
                $rs[$item['apply_no']]['workflow_no']               = $item['workflow_no'] ?? '';
                $rs[$item['apply_no']]['id']                        = $item['id'] ?? '';
                $rs[$item['apply_no']]['product'][$item['barcode']] = $item['product_id'] ?? '';
            }
        }
        return $rs;
    }


    /**
     * 从by批量获取审批数据
     * @param int $approval_id 当前审批人账号
     * @param array $serial_no 待审批的workflow_no集合
     * @return array
     */
    public function getPending(int $approval_id, array $serial_no)
    {
        $by_pending_params = [
            'approval_id' => $approval_id,
            'audit_type'  => ByWorkflowEnums::BY_BIZ_TYPE_WMS,
            'serial_no'   => $serial_no,
        ];
        return (new ByWorkflowService())->pending($by_pending_params);

    }

    /**
     * 处理审核通过的数据，把多个领用单详情 按照apply_no，合并为一个准备审核或驳回使用
     * @param array $correct_data 校验通过的数据
     * @param array $error_data 校验不通过的数据
     * @return array
     */
    public function rejectHandleData(array $correct_data, array $error_data)
    {
        $error_apply_no_data = array_unique(array_column($error_data, 'apply_no'));
        $result              = $rs = $value = [];
        foreach ($correct_data as $item) {
            if (in_array($item['apply_no'], $error_apply_no_data)) {
                continue;
            }
            if (in_array($item['apply_no'], $value)) {
                $result[$item['apply_no']]['approval_result'] = $item['approval_result'] ?? '';
                $result[$item['apply_no']]['id']              = $item['id'] ?? '';
                $result[$item['apply_no']]['reason']          = $item['operation_remark'] ?? '';
                $rs['id']                                     = $item['product_id'];
                $rs['now_time_num']                           = $item['now_time_num'] ?? '';
                $rs['barcode']                                = $item['barcode'];
                $result[$item['apply_no']]['products'][]      = $rs;
            } else {
                $value[]                                      = $item['apply_no'];
                $result[$item['apply_no']]['approval_result'] = $item['approval_result'] ?? '';
                $result[$item['apply_no']]['id']              = $item['id'] ?? '';
                $result[$item['apply_no']]['reason']          = $item['operation_remark'] ?? '';
                $rs['id']                                     = $item['product_id'];
                $rs['now_time_num']                           = $item['now_time_num'] ?? '';
                $rs['barcode']                                = $item['barcode'];
                $result[$item['apply_no']]['products'][]      = $rs;
            }
        }
        return $result;
    }

    /**
     * 批量解除锁定
     * @param $un_wms_apply_arr
     * @param $db
     * @return bool
     */
    public function unWmsApplyLock($un_wms_apply_arr, $db)
    {
        if (empty($un_wms_apply_arr)) {
            return [];
        }
        $apply_no_string = "'" . implode("','", array_values(array_unique($un_wms_apply_arr))) . "'";
        $update_success  = $db->updateAsDict(
            (new MaterialWmsApplyModel())->getSource(),
            [
                'is_batch_lock' => MaterialWmsEnums::MATERIAL_WMS_IS_BATCH_UN_LOCK,
                'updated_at'    => date('Y-m-d H:i:s', time())
            ],
            [
                'conditions' => " apply_no IN ($apply_no_string)",
            ]
        );
        if (!$update_success) {
            throw new BusinessException('耗材批量审核更新解锁失败 解锁的数据是 ' . json_encode($un_wms_apply_arr, JSON_UNESCAPED_UNICODE), ErrCode::$MATERIAL_WMS_APPLY_BATCH_AUDIT_EDIT_ERROR);
        }
        return $update_success;
    }


    /**
     * 耗材管理-耗材申请/资产管理-资产申请-收货人查询
     * @param array $params 条件
     * @param array $user 用户数据
     * @return array
     */
    public function consigneeList(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $limit   = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        try {
            if (isset($params['name']) && $params['name'] != '') {
                $hr_staff_repository = new HrStaffRepository();
                if ($params['headquarters'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                    //耗材使用网点 == 总部；收货人选择范围为所有在职(包含待离职)&正式员工
                    $data = $hr_staff_repository->getStaffLikeOnLineList($params['name'], $limit);
                } elseif ($user['sys_store_id'] != $params['use_land_id']) {
                    //申请人网点 != 耗材使用地点；先判断耗材使用地点下是否有员工在职(包含待离职)&正式员工
                    $staff_count = $hr_staff_repository->getStaffCount(['sys_store_id' => $params['use_land_id']]);
                    if ($staff_count) {
                        //有；收货人选择范围为耗材使用网点下所有在职(包含待离职)&正式员工+网点负责人+申请人
                        $store_staff_list = (new HrStaffRepository)->getStaffOfficially($params['use_land_id'], $params['name'], $limit);
                        //申请人
                        $apply_user['consignee_id'] = $user['id'];
                        $apply_user['consignee_name'] = $user['name'];
                        //网点负责人
                        $sys_store_manager_data =  $this->getSysStoreManagerInfo($params['use_land_id']);
                        $search_data = array_filter([$apply_user, $sys_store_manager_data]);
                        $other_staff = [];
                        //网点负责人+申请人 & 索索关键字
                        if (!empty($search_data)) {
                            foreach ($search_data as $search) {
                                if (strpos($search['consignee_id'], $params['name']) !== false || strpos($search['consignee_name'], $params['name']) !== false) {
                                    $other_staff[] = $search;
                                }
                            }
                        }

                        //搜索范围为耗材使用地点下在职(包含待离职)&正式员工
                        $data = array_merge($store_staff_list, $other_staff);
                    } else {
                        //无；收货人选择范围为所有在职(包含待离职)&正式员工
                        $data = $hr_staff_repository->getStaffLikeOnLineList($params['name'], $limit);
                    }
                } else {
                    //申请人网点==耗材使用地点；收货人选择范围为耗材使用网点下所有在职(包含待离职)&正式员工
                    $data = (new HrStaffRepository)->getStaffOfficially($params['use_land_id'], $params['name'], $limit);
                }
            } else {
                //默认收货人 = 申请人
                $data['consignee_id'] = $user['id'];
                $data['consignee_name'] = $user['name'];

                //申请网点 != 使用网点；若能找到网点负责人则默认为网点负责人
                if ($user['sys_store_id'] != $params['use_land_id']) {
                    $sys_store_manager_data = $this->getSysStoreManagerInfo($params['use_land_id']);
                    $data = $sys_store_manager_data ? $sys_store_manager_data : $data;
                }
                $data = [$data];
            }
            //去除重复的收货人
            $data = array_values(array_column($data, null, 'consignee_id'));
            //按照工号升序
            array_multisort(array_column($data, 'consignee_id'), SORT_ASC, $data);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms/asset_apply_consignee_list failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }


    /**
     * 获取配送方式
     * @return array
     */
    private function getDeliveryWay()
    {
        $delivery_way      = MaterialClassifyEnums::$delivery_way_arr;
        $delivery_way_data = [];
        foreach ($delivery_way as $key => $delivery_way) {
            $delivery_way_data[] = [
                'value' => $key,
                'label' => static::$t->_($delivery_way)
            ];
        }
        return $delivery_way_data;
    }


    /**
     * 查询职状态下网点负责人
     * @param string $store_id 网点ID
     * @return array
     */
    private function getSysStoreManagerInfo(string $store_id)
    {
        $manager_data = [];
        $store_info = (new StoreRepository())->getStoreDetail($store_id, 0);
        if (!empty($store_info['manager_id'])) {
            //存在网点主管设置，判断网点主管是否在职
            $manager_data = (new HrStaffRepository())->getStaffInfo($store_info['manager_id']);
            if (!empty($manager_data)) {
                return [
                    'consignee_id' => $manager_data['id'],
                    'consignee_name' => $manager_data['name']
                ];
            }
        }
        return $manager_data;
    }

    /**
     * 获取抄送收货人站内信消息详情
     * @param integer $apply_id 申请单ID
     * @return array
     */
    public function getWmsApplyInfo($apply_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = ['id' => $apply_id, 'apply_no' => '', 'staff_name' => '', 'workflow_no' => ''];
        try {
            $wms_apply_info = MaterialWmsApplyModel::findFirst([
                'columns' => 'id, apply_no, staff_name, workflow_no',
                'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                'bind' => ['id' => $apply_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            ]);
            if (!empty($wms_apply_info)) {
                $detail = $wms_apply_info->toArray();
            }
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('material_wms_apply_detail_for_by failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail,
        ];
    }

    /**
     * 批量新增
     *
     * @param array $user
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     * @throws GuzzleException
     */
    public function batchAddToImportCenter(array $user)
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
        }

        $file = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if ($extension != 'xlsx') {
            throw new ValidationException(static::$t->_('bank_flow_upload_file_type_error'), ErrCode::$VALIDATE_ERROR);
        }

        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);

        // 读取上传文件数据
        $excel_data = $excel->openFile($file->getTempName())
            ->openSheet()
            ->getSheetData();

        $this->logger->info(['excel_file_data' => $excel_data]);

        // Excel空数据校验: 不含表头
        $excel_content_data = array_slice($excel_data, 1);
        if (empty($excel_content_data)) {
            throw new ValidationException(static::$t->_('bank_flow_data_empty'), ErrCode::$VALIDATE_ERROR);
        }

        // 单次导入最多行数校验
        if (count($excel_content_data) > MaterialWmsEnums::MATERIAL_WMS_APPLY_MAX_LIMIT) {
            throw new ValidationException(static::$t->_('import_center_single_max_limit', ['max_limit' => MaterialWmsEnums::MATERIAL_WMS_APPLY_MAX_LIMIT]), ErrCode::$VALIDATE_ERROR);
        }

        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . mt_rand(10, 10000) . '_' . $file->getName();
        $file->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info(['excel_file_data_oss_result' => $oss_result]);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(static::$t->_('file_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        ImportCenterService::getInstance()->addImportCenter($user, $oss_result['object_url'], ImportCenterEnums::TYPE_MATERIAL_WMS_BATCH_ADD);

        return [
            'code' => ErrCode::$SUCCESS,
            'message' => static::$t->_('success'),
        ];
    }


    /**
     * 耗材申请 - 批量新增
     *
     * @param array $excel_data
     * @param array $user
     * @return array
     * @throws GuzzleException
     */
    public function batchHandleImportWmsApply(array $excel_data, array $user)
    {
        // 返回结果
        $code = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data = [];

        try {
            // 表头
            $excel_header = array_shift($excel_data);

            // Excel业务数据行处理
            // 1. 构建 申请单 与 行数据 结构
            $check_main_item = []; // 公司ID + 使用网点ID + 收货人工号 分组
            foreach ($excel_data as $k => $v) {
                $v = trim_array(array_slice($v, 0, 7));
                $excel_data[$k] = $v;

                // 申请单分组标识
                $apply_group_id = $v[0] . '_' . strtolower($v[1]) . '_' . $v[2];

                // 组内的行统计
                if (isset($check_main_item[$apply_group_id])) {
                    $check_main_item[$apply_group_id]['barcode_count'] += 1;
                } else {
                    $check_main_item[$apply_group_id]['barcode_count'] = 1;
                }

                // 配送方式
                $check_main_item[$apply_group_id]['delivery_way'][$v[3]] = 1;

                // 申请理由
                $check_main_item[$apply_group_id]['reason'][strtolower($v[4])] = 1;

                // barcode => 申请数量
                $check_main_item[$apply_group_id]['barcode_item'][strtolower($v[5])] = $v[6];
            }

            // 指定Cleve下的BU
            $company_list = (new PurchaseService())->getCooCostCompany();
            $company_list = array_column($company_list, 'cost_company_name', 'cost_company_id');

            // 使用网点信息 激活态
            $use_land_ids = array_values(array_unique(array_column($excel_data, 1)));
            $use_land_ids = (new StoreRepository())->getStoreListByIds($use_land_ids, Enums::STORE_STATE_ACTIVE);
            $use_land_ids = array_column($use_land_ids, null, 'id');

            //禁止申请耗材网点类型
            $material_wms_apply_forbid_categorys = EnumsService::getInstance()->getSettingEnvValueIds('material_wms_apply_forbid_categorys');

            // 收货人信息 正式的 在职/待离职 非子账号 非个人代理
            $consignee_ids = array_values(array_unique(array_column($excel_data, 2)));
            if (!empty($consignee_ids)) {
                $columns = [
                    'staff.staff_info_id as staff_id',
                    'staff.name as staff_name',
                ];

                $consignee_ids = $this->modelsManager->createBuilder()
                    ->columns($columns)
                    ->from(['staff' => HrStaffInfoModel::class])
                    ->inWhere('staff.staff_info_id', $consignee_ids)
                    ->andWhere('staff.formal = :formal:', ['formal' => StaffInfoEnums::FORMAL_IN])
                    ->andWhere('staff.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN])
                    ->andWhere('staff.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO])
                    ->notInWhere('staff.hire_type', [StaffInfoEnums::HIRE_TYPE_PERSONAL_AGENCY, StaffInfoEnums::HIRE_TYPE_PART_TIME_AGENT])
                    ->getQuery()->execute()->toArray();
                $consignee_ids = array_column($consignee_ids, 'staff_name', 'staff_id');
            }

            // 申请人信息
            $apply_info = StaffService::getInstance()->searchStaff(['staff_id' => $user['id'], 'limit' => 1]);
            $apply_info = !empty($apply_info['data']) ? $apply_info['data'][0] : [];
            $user['node_department_id'] = $apply_info['node_department_id'] ?? $user['node_department_id'];
            $user['node_department_name'] = $apply_info['node_department_name'] ?? '';
            $user['sys_store_id'] = $apply_info['sys_store_id'] ?? $user['sys_store_id'];
            $user['store_name'] = $apply_info['store_name'] ?? '';

            // 配送方式 1 / 2
            $delivery_way_item = [MaterialClassifyEnums::DELIVERY_WAY_EXPRESS, MaterialClassifyEnums::DELIVERY_WAY_SELF];

            // barcode 标准型号表 已启用 的 可申请/可申请-可购买 的 耗材
            $barcode_item = array_values(array_unique(array_column($excel_data, 5)));
            $barcode_item = $this->getBarcodeByBatchImport($barcode_item);
            $barcode_item = array_column($barcode_item, null, 'barcode');

            // 2. 文件逐行校验
            $error_list = [];
            $error_apply_group_ids = [];
            $correct_main_list = [];
            $excel_num_total = count($excel_data);
            foreach ($excel_data as $row_k => $row_v) {
                $error_remark = [];

                $company_id = $row_v[0]; // BU ID
                $use_land_id = $row_v[1]; // 耗材使用网点ID
                $consignee_id = $row_v[2]; // 收货人工号
                $delivery_way = $row_v[3]; // 配送方式
                $reason = $row_v[4]; // 申请理由
                $barcode = $row_v[5]; // barcode
                $this_time_num = $row_v[6]; // 申请数量

                if (mb_strlen($company_id) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_001');
                } else if (empty($company_list[$company_id])) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_002');
                }

                if (mb_strlen($use_land_id) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_003');
                } else if (!isset($use_land_ids[$use_land_id]) || in_array($use_land_ids[$use_land_id]['category'], $material_wms_apply_forbid_categorys) || (!in_array($use_land_ids[$use_land_id]['category'], [Enums::STORE_CATEGORY_SHOP_PICKUP_ONLY, Enums::STORE_CATEGORY_SHOP_PICKUP_DELIVERY, Enums::STORE_CATEGORY_FH, Enums::STORE_CATEGORY_USHOP]) && $use_land_ids[$use_land_id]['use_state'] != 1)) {
                    //非激活 || 禁止申请耗材网点类型 || 网点类型不是FH、SHOP、USHOP时，网点营业状态非营业
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_004');
                }

                if (mb_strlen($consignee_id) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_005');
                } else if (empty($consignee_ids[$consignee_id])) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_006');
                }

                if (mb_strlen($delivery_way) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_007');
                } else if (!in_array($delivery_way, $delivery_way_item)) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_008');
                }

                $reason_strlen = mb_strlen($reason);
                if ($reason_strlen < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_009');
                } else if ($reason_strlen > 200) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_010');
                }

                $barcode_info = $barcode_item[$barcode] ?? [];
                if (mb_strlen($barcode) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_011');
                } else if (empty($barcode_info)) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_012');
                }

                if (mb_strlen($this_time_num) < 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_013');
                } else if (!preg_match('/^[1-9]+\d*$/', $this_time_num)) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_014');
                } else if (!empty($barcode_info['use_val']) && ($this_time_num % $barcode_info['use_val']) != 0) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_015');
                }

                // 单据的行校验
                $apply_group_id = $company_id . '_' . strtolower($use_land_id) . '_' . $consignee_id;
                $check_main_info = $check_main_item[$apply_group_id] ?? [];

                // 配送方式是否唯一
                if (count($check_main_info['delivery_way']) > 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_016');
                }

                // 申请理由是否唯一
                if (count($check_main_info['reason']) > 1) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_017');
                }

                // barcode 是否唯一
                if ($check_main_info['barcode_count'] != count($check_main_info['barcode_item'])) {
                    $error_remark[] = static::$t->_('material_wms_batch_import_error_018');
                }

                // 当前行有误
                if (!empty($error_remark)) {
                    $error_apply_group_ids[] = $apply_group_id;

                    // 取出所在组前一个校验无误的行, 放到错误文件中 (整组不处理)
                    if (isset($correct_main_list[$apply_group_id])) {
                        $exist_correct_main_info = $correct_main_list[$apply_group_id];
                        $error_list[] = [
                            $exist_correct_main_info['company_id'],
                            $exist_correct_main_info['use_land_id'],
                            $exist_correct_main_info['consignee_id'],
                            $exist_correct_main_info['delivery_way'],
                            $exist_correct_main_info['reason'],
                            $exist_correct_main_info['products'][0]['barcode'],
                            $exist_correct_main_info['products'][0]['this_time_num'],
                            ''
                        ];

                        unset($correct_main_list[$apply_group_id]);
                    }

                    // 当前行
                    $row_v[7] = implode('; ', $error_remark);
                    $error_list[] = $row_v;
                    continue;
                }

                // 当前行无误: 当前行所在组已存在校验有误的行 (整组不处理)
                if (in_array($apply_group_id, $error_apply_group_ids)) {
                    $row_v[7] = '';
                    $error_list[] = $row_v;

                    if (isset($correct_main_list[$apply_group_id])) {
                        unset($correct_main_list[$apply_group_id]);
                    }

                    continue;
                }

                // 行校验通过, 归并到同一个申请单中(组)
                $correct_main_info = $correct_main_list[$apply_group_id] ?? [];
                if (empty($correct_main_info)) {
                    $correct_main_info = [
                        'staff_id' => $user['id'],
                        'staff_name' => $user['name'],
                        'company_id' => $company_id,
                        'company_name' => $company_list[$company_id] ?? '',
                        'node_department_id' => $user['node_department_id'],
                        'node_department_name' => $user['node_department_name'],
                        'sys_store_id' => $user['sys_store_id'],
                        'store_name' => $user['store_name'],
                        'use_land_id' => $use_land_id,
                        'use_land_name' => $use_land_ids[$use_land_id]['name'] ?? '',
                        'consignee_id' => $consignee_id,
                        'consignee_name' => $consignee_ids[$consignee_id] ?? '',
                        'delivery_way' => $delivery_way,
                        'headquarters' => $use_land_id,
                        'reason' => $reason,
                        'source_type' => MaterialWmsEnums::SOURCE_TYPE_OA,
                        'apply_date' => date('Y-m-d'),
                    ];
                }

                $correct_main_info['products'][] = [
                    'id' => $barcode_info['id'],
                    'barcode' => $barcode,
                    'name_zh' => $barcode_info['name_zh'],
                    'name_en' => $barcode_info['name_en'],
                    'name_local' => $barcode_info['name_local'],
                    'unit_zh' => $barcode_info['unit_zh'],
                    'unit_en' => $barcode_info['unit_en'],
                    'model' => $barcode_info['model'],
                    'use_val' => $barcode_info['use_val'],
                    'this_time_num' => $this_time_num,
                    'available_inventory' => 0,
                ];

                $correct_main_list[$apply_group_id] = $correct_main_info;
            }

            // 校验通过的依次创建申请单
            $create_failed_list = [];
            $index = 0;
            foreach ($correct_main_list as $main_info) {
                $index++;

                // 50单 歇5s
                if ($index % 50 == 0) {
                    sleep(5);
                }

                // 实时查barcode的库存
                $barcode_params = [
                    'barcode' => array_column($main_info['products'], 'barcode'),
                    'use_land_id' => $main_info['use_land_id'],
                    'company_id' => $main_info['company_id'],
                    'pageSize' => count($main_info['products'])
                ];
                $search_res = $this->searchBarcode($barcode_params);
                if (!isset($search_res['data']['items']) || $search_res['code'] != ErrCode::$SUCCESS) {
                    $this->logger->info(['wms_batch_import_search_failed_apply_data' => $main_info]);

                    foreach ($main_info['products'] as $products) {
                        $create_failed_list[] = [
                            $main_info['company_id'],
                            $main_info['use_land_id'],
                            $main_info['consignee_id'],
                            $main_info['delivery_way'],
                            $main_info['reason'],
                            $products['barcode'],
                            $products['this_time_num'],
                            $search_res['message'] ?? static::$t->_('material_wms_batch_import_error_020')
                        ];
                    }

                    continue;
                }

                // 申请数量 和 可用库存对比, 若有库存不足的, 则整单不导入, 在对应barcode行上提示「库存不足」
                $barcode_stock_list = array_column($search_res['data']['items'] ?? [], 'available_inventory', 'barcode');

                $barcode_error_msg = [];
                foreach ($main_info['products'] as $k => $products) {
                    $barcode_available_inventory = $barcode_stock_list[$products['barcode']] ?? null;
                    if (is_numeric($barcode_available_inventory)) {
                        if ($products['this_time_num'] > $barcode_available_inventory) {
                            $barcode_error_msg[$products['barcode']] = static::$t->_('material_wms_batch_import_error_019');
                        }

                        $main_info['products'][$k]['available_inventory'] = $barcode_available_inventory;
                    } else {
                        $barcode_error_msg[$products['barcode']] = $barcode_available_inventory;
                    }
                }

                // 有库存异常的
                if (!empty($barcode_error_msg)) {
                    $this->logger->info(['wms_batch_import_stock_abnormal_apply_data' => $main_info]);

                    foreach ($main_info['products'] as $products) {
                        $create_failed_list[] = [
                            $main_info['company_id'],
                            $main_info['use_land_id'],
                            $main_info['consignee_id'],
                            $main_info['delivery_way'],
                            $main_info['reason'],
                            $products['barcode'],
                            $products['this_time_num'],
                            $barcode_error_msg[$products['barcode']] ?? ''
                        ];
                    }

                    continue;
                }

                // 校验通过, 创建申请单
                $main_info['apply_no'] = $this->generateMaterialWmsApplyNo();
                $add_result = $this->add($main_info, $user);

                // 创建失败, 失败的数据追加到失败文件中
                if ($add_result['code'] != ErrCode::$SUCCESS) {
                    $this->logger->info(['wms_batch_import_create_failed_apply_data' => $main_info]);

                    foreach ($main_info['products'] as $products) {
                        $create_failed_list[] = [
                            $main_info['company_id'],
                            $main_info['use_land_id'],
                            $main_info['consignee_id'],
                            $main_info['delivery_way'],
                            $main_info['reason'],
                            $products['barcode'],
                            $products['this_time_num'],
                            $add_result['message']
                        ];
                    }
                }
            }

            // 校验有误的行数据
            $this->logger->info(['wms_batch_import_validation_error_list' => $error_list]);

            // 申请单创建失败的行数据
            $this->logger->info(['wms_batch_import_create_failed_list' => $create_failed_list]);

            // 所有未成功处理的行
            $error_list = array_merge($error_list, $create_failed_list);
            $error_num_count = count($error_list);
            $success_num = $excel_num_total - $error_num_count;

            $data['error_num'] = $error_num_count;
            $data['success_num'] = $success_num >= 0 ? $success_num : 0;
            $data['result_file_url'] = '';

            // 将有误的写入错误文件
            $file_name = 'Consumables_Batch_Import_Result_' . date('YmdHis') . '.xlsx';
            $excel_header[7] = static::$t->_('check_result');

            $export_res = $this->exportExcel($excel_header, $error_list, $file_name);
            if (empty($export_res['data'])) {
                throw new BusinessException('生成结果文件失败, 请检查', ErrCode::$BUSINESS_ERROR);
            }

            $data['result_file_url'] = $export_res['data'];

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        }

        if ($code != ErrCode::$SUCCESS) {
            $this->logger->error('耗材/资产管理-耗材申请-批量导入异常, 原因可能是=' . $message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 批量导入耗材时，可选范围的barcode
     *
     * @param array $barcode_item
     * @return array
     */
    protected function getBarcodeByBatchImport(array $barcode_item)
    {
        if (empty($barcode_item)) {
            return [];
        }

        $params['status'] = MaterialClassifyEnums::MATERIAL_START_USING;
        $params['update_to_scm'] = MaterialClassifyEnums::MATERIAL_CATEGORY_NO;
        $params['category_type'] = MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_WMS;
        $params['use_scene'] = [MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY, MaterialClassifyEnums::MATERIAL_USE_SCENE_APPLY_AND_BUY];
        $params['barcode'] = $barcode_item;

        $columns = [
            'ms.id',
            'ms.barcode',
            'ms.name_zh',
            'ms.name_en',
            'ms.name_local',
            'ms.unit_zh',
            'ms.unit_en',
            'ms.model',
            'ms.update_to_scm',
            'mss.use_val'
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftjoin(MaterialSauSkuModel::class, 'ms.id = mss.sau_id  and mss.is_deleted = ' . MaterialClassifyEnums::IS_DELETED_NO, 'mss');
        $builder = StandardService::getInstance()->getListCondition('', $builder, $params);
        $builder->columns($columns);
        return $builder->getQuery()->execute()->toArray();
    }


}
