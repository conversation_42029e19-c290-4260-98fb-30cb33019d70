<?php
/**
 *SysStoreModel.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/9 0009 21:27
 */

namespace App\Modules\Contract\Models;


use App\Models\Base;


class ContractArchiveEditLogModel  extends Base
{
    public $id;
    public $store_name_old;
    public $store_name;
    public $store_cate_old;
    public $store_cate;
    public $provinces;
    public $lon_lat;
    public $store_addr;
    public $contract_name;
    public $contract_id;
    public $contract_begin;
    public $contract_begin_old;
    public $contract_end;
    public $contract_end_old;
    public $contract_effect_date_old;
    public $contract_effect_date;
    public $bank_collection_old;
    public $bank_collection;
    public $hourse_owner_addr_old;
    public $hourse_owner_addr;
    public $operator_id;
    public $operator_name;
    public $is_invalid;
    public $created_at;
    public $updated_at;
    public $is_terminal;
    public $rent_free_time;
    public $rent_free_time_old;
    public $modify_desc;
    public $need_workflow;
    public $land_type;
    public $land_type_old;
    public $leaser_type;
    public $leaser_type_old;
    public $rent_due_date;
    public $rent_due_date_old;
    public $land_type_content;
    public $land_type_content_old;
    public $leaser_type_content;
    public $leaser_type_content_old;
    public $warehouse_id;
    public $warehouse_id_old;

    public function initialize()
    {
        $this->setWriteConnectionService('db_oa');
        $this->setReadConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_archive_edit_log');
    }
}