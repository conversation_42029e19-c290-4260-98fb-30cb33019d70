<?php

namespace App\Modules\Contract\Models;

use App\Library\Enums;
use App\Models\Base;
use App\Models\oa\ContractQuotationModel;
use App\Modules\User\Models\AttachModel;

class Contract extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_CONTRACT_FILE." and deleted=0"
                ],
                "alias" => "File",
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT." and deleted=0"
                ],
                "alias" => "Attach",
            ]
        );

        $this->hasMany(
            'id',
            ContractQuotationModel::class,
            'contract_id',
            [
                "alias" => "ContractQuotation",
            ]
        );

        $this->hasOne(
            'cno',
            ContractArchive::class,
            'cno',
            [
                'alias' => 'ContractArchive',
            ]
        );
    }

}
