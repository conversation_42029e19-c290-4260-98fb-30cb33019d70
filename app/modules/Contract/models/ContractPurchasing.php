<?php

namespace App\Modules\Contract\Models;

use App\Models\Base;

class ContractPurchasing extends Base
{
    public $id;

    public $cno;

    public $cname;

    public $status;

    public $template_id;

    public $vendor_id;

    public $is_master;

    public $sub_cno;

    public $delivery_date;

    public $delivery_address;

    public $payment_cycle;

    public $payment_type;

    public $payment_date;

    public $amount;

    public $payment_currency;

    public $refuse_reason;

    public $content;

    public $attachment;

    public $create_id;

    public $created_at;

    public $updated_at;

    public $effected_at;

    public $finished_at;

    public $approved_at;

    public $rejected_at;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_purchasing');
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
