<?php

namespace App\Modules\Contract\Models;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class ContractArchive extends Base
{
    public $id;

    public $cno;

    public $cname;

    public $status;

    public $template_id;

    public $is_master;

    public $sub_cno;

    public $amount;

    public $payment_currency;

    public $contract_file;

    public $holder_name;

    public $create_id;

    public $create_name;

    public $created_at;

    public $updated_at;

    public $approved_at;

    public $filing_at;

    public $filing_name;

    public $filing_id;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_archive');

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE." and deleted=0"
                ],
                "alias" => "File",
            ]
        );

        // 关联的签字合同
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted = :deleted:',
                    'bind' => ['oss_bucket_type' => ContractEnums::OSS_BUCKET_TYPE_CONTRACT_SIGNATURE_FILE, 'deleted' => Enums\GlobalEnums::IS_NO_DELETED],
                    'columns' => ['oss_bucket_type', 'file_name', 'bucket_name', 'object_key','id']
                ],
                'alias' => 'SignatureFiles',
            ]
        );
    }

    public function getAllContractArchive(){
        $contracts = self::find([
            "columns"    => "id,filing_at,cno,cname,template_id,approved_at,amount,payment_currency,status,create_name,filing_name,holder_name,contract_file"
        ]);
        return  $contracts ? $contracts->toArray() : null;
    }
}
