<?php
/**
 *SysStoreModel.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/9 0009 21:27
 */

namespace App\Modules\Contract\Models;


use App\Models\Base;


class SysDepartmentStoreTypeModel  extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('sys_department_store_type');
    }

    /**
     * 根据部门id找对应网点名称列表
     * @param $department_id
     * @return array
     */
    public static function getStoreListByDepartmentId($department_id)
    {
        if (empty($department_id)) {
            $where = ['conditions' => 'is_del = 0'];
        } else {
            $where =  [
                'conditions' => 'is_del = 0 and department_id = :id:',
                'bind' => ['id' => $department_id]
            ];
        }
        $item = self::find($where);

        return !empty($item) ? $item->toArray() : [];
    }
}