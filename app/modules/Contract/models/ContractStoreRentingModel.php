<?php
/**
 *StoreRentingContractModel.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/9 0009 21:01
 */

namespace App\Modules\Contract\Models;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ContractEnums;
use App\Modules\User\Models\AttachModel;
use Phalcon\Mvc\Model;
use App\Models\Base;

class ContractStoreRentingModel extends Base
{
    public function initialize()
    {
        $this->setWriteConnectionService('db_oa');
        $this->setReadConnectionService('db_oa');
//        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('contract_store_renting');

        $this->hasMany(
            'id',
            ContractStoreRentingArea::class,
            'contract_store_renting_id', [
                "alias" => "RentingArea",
            ]
        );

        $this->hasMany(
            'id',
            ContractStoreRentingDetailModel::class,
            'contract_store_renting_id',
            [
                "alias" => "RentingDetail"
            ]
        );

        $this->hasMany(
            'id',
            ContractStoreRentingMoneyModel::class,
            'store_renting_contract_id',
            [
                "alias" => "RentingMoney"
            ]
        );

        // 获取租房合同自身的附件(仅指合同提交场景的附件, 不含作废/终止等场景的)
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . ContractEnums::RENT_CONTRACT_FILE_OSS_BUCKET_TYPE . ' AND deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'SelfAttachments',
            ]
        );
    }




    /**
     * 自定义的update事件
     * @param array $data
     * @return array
     */
    protected function before_update(array $data)
    {
        /*$data['create_by'] = $this -> _user['uid'];
        $data['create_at'] = date('Y-m-d H:i:s');*/
        return $data;
    }

    /**
     * 更新记录 更新字段中必须包含主键
     * @param array $data 要更新的数据数组，其中键是字段名，值是待更新的字段数据
     * @param $PrimaryValue 主键值
     * @param $PrimaryColumn 对应的主键字段名
     * @return int
     * @throws \Exception
     */
    public function update_record(array $data, $PrimaryValue = '', $PrimaryColumn = 'id')
    {
        $data = $this->before_update($data);
        $this->$PrimaryColumn = $PrimaryValue;
        $result = $this->iupdate($data);
        if (!$result) {
            return false;
            //throw new \Exception('更新失败');
        }
        //$affectedRows = $this -> db -> affectedRows();
        return true;
    }

    /**
     * 封装phalcon model的update方法，实现仅更新数据变更字段，而非所有字段更新
     * @param array|null $data
     * @param null $whiteList
     * @return bool
     */
    public function iupdate(array $data=null, $whiteList=null){
        if(count($data) > 0){
            $attributes = $this -> getModelsMetaData() -> getAttributes($this);//echo 'getModelsMetaData->getAttributes'. json_encode($attributes).'<br>';
            $this -> skipAttributesOnUpdate(array_diff($attributes, array_keys($data)));//echo 'array_diff'.json_encode(array_diff($attributes, array_keys($data))).'<br>';
        }//echo  json_encode($data);echo $whiteList;
        return parent::update($data, $whiteList);
    }



    /**
     * 合同新增
     */
    public function addContract($param)
    {
        try {
            $contract_model = new self;
            $result_insert  = $contract_model->insert_record($param);
            if (!$result_insert) {
                return 0;
            } else {
                return 1;
            }
        } catch (\Exception $e) {
            return [$e->getMessage(), '-1', []];
        }
    }

    public function insert_record(array $data)
    {
        if (!is_array($data) || count($data) == 0) {
            return false;
        }
        $result = $this->create($data);
        if (!$result) {
            throw new \Exception(implode(',', $this->getMessages()));
        }
        return $result;
    }

}
