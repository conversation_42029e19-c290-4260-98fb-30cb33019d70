<?php
/**
 *HrStaffInfoModel.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/9/10 0010 19:51
 */

namespace App\Modules\Contract\Models;

use App\Models\Base;

class HrStaffInfoModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_info');
    }

    public static function getUserInfo($staffInfoId, $columns = '*')
    {
        if(empty($staffInfoId)){
            return null;
        }
       $userInfo = self::findFirst([
           "conditions" => "staff_info_id = :staff_info_id:",
           "bind"       => ['staff_info_id' => $staffInfoId],
           "columns"    => $columns
       ]);
       return  $userInfo ? $userInfo->toArray() : null;
    }

}