<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Services\InitFlowService;
use App\Modules\Contract\Services\BaseService;
use App\Modules\Contract\Services\ListService;
use App\Modules\Contract\Services\StandardContractService;
use App\Modules\CrmQuotation\Services\ApplyService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StandardContractController extends BaseController
{
    /**
     * 初始化枚举列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getInitAction()
    {
        $instance = StandardContractService::getInstance();
        // 合同类型
        $contract_types  = $instance->getContractTypes();
        // 合同主从属性
        $is_main_list  = $instance->getIsMainList();
        // 获取有效期
        $deadline_list  = $instance->getDeadlineList();
        // 结算方式
        $pay_method = $instance->getPayMethod();

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', [
            'contract_types'        => $contract_types,
            'is_main_list'     => $is_main_list,
            'deadline_list'     => $deadline_list,
            'pay_method'        => $pay_method,
        ]);
    }

    /**
     * 合同申请-合同添加
     * @Permission(action='contract.standard.apply.create')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        try {
            $validate = StandardContractService::$validate_contract;
            if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
                $validate = array_merge($validate, StandardContractService::$validate_contract_th);
            }
            Validation::validate($data, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardContractService::getInstance()->add($data, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同申请-合同列表
     * @Permission(action='contract.standard.apply.query')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            $params = BaseService::handleParams($params, StandardContractService::$not_must_params);
            Validation::validate($params, StandardContractService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = StandardContractService::getInstance()->getList($params, $this->user['id'],ListService::LIST_TYPE_APPLY);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同申请-合同编辑
     * @Permission(action='contract.standard.apply.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateAction()
    {
        $data = $this->request->get();
        try {
            $validate = array_merge(StandardContractService::$validate_contract, StandardContractService::$validate_contract_edit);
            if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
                $validate = array_merge($validate, StandardContractService::$validate_contract_th);
            }
            Validation::validate($data, $validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardContractService::getInstance()->update($data, $this->user);

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同申请-合同详情
     * @Permission(action='contract.standard.apply.detail')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = StandardContractService::getInstance()->getAuditDetail($id, $this->user);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 合同申请-合同下载
     * @Permission(action='contract.standard.apply.download')
     * @return Response|ResponseInterface
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);

            // 加锁
            $lock_key = md5('standard_contract_download_' . $id . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($id){
                return StandardContractService::getInstance()->download($id);
            }, $lock_key, 30);
            if ($res['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($res['message'], ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
    }

    /**
     * 数据查询-查询列表
     * @Permission(action='contract.standard.query.query')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchListAction()
    {
        $params = $this->request->get();

        try {
            $params = BaseService::handleParams($params, array_merge(StandardContractService::$not_must_params,
                StandardContractService::$search_not_must_params));
            Validation::validate($params, array_merge(StandardContractService::$validate_list_search,
                StandardContractService::$search_validate_list_search));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = StandardContractService::getInstance()->getList($params, $this->user['id'],
            ListService::LIST_TYPE_SEARCH);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 数据查询-合同详情
     * @Permission(action='contract.standard.query.detail')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchDetailAction()
    {
        $data = $this->request->get();
        $contract_id = $this->request->get('id', 'int');
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = StandardContractService::getInstance()->getAuditDetail($contract_id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-合同下载
     * @Permission(action='contract.standard.query.download')
     * @return Response|ResponseInterface
     */
    public function queryDownloadAction(){
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);

            // 加锁
            $lock_key = md5('standard_contract_query_download_' . $id . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($id){
                return StandardContractService::getInstance()->download($id);
            }, $lock_key, 30);
            if ($res['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($res['message'], ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
    }

    /**
     * 合同申请-新建标准合同-银行列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63832
     */
    public function getBankAcctListAction()
    {
        try {
            $data = InitFlowService::getInstance()->getBankList();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (\Exception $e) {
            $this->logger->error('新建标准合同 - 获取银行列表异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }
}
