<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\ContractNotRemindService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractNotRemindController extends BaseController
{

    /**
     * 不提醒网点列表
     * @Permission(action='contract.contract_remind.not_remind_store.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73727
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = params_filter($params, ContractNotRemindService::$not_must_params_list);
        Validation::validate($params, ContractNotRemindService::$validate_list_search);
        $res = ContractNotRemindService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 不提醒网点添加
     * @Permission(action='contract.contract_remind.not_remind_store.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73747
     */
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ContractNotRemindService::$validate_add);
        $res = ContractNotRemindService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 不提醒网点删除
     * @Permission(action='contract.contract_remind.not_remind_store.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/73762
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ContractNotRemindService::$validate_delete);
        $res = ContractNotRemindService::getInstance()->delete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
