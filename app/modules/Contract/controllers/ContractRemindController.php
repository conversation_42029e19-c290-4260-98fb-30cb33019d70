<?php

namespace App\Modules\Contract\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\BaseService;
use App\Modules\Contract\Services\ContractRemindListService;

class ContractRemindController extends BaseController
{
    /**
     * 提醒列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63678
     */
    public function getOptionsDefaultAction()
    {
        $res = ContractRemindListService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 提醒列表
     * @Permission(action='contract.contractRemind.storeRentingContract.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63676
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ContractRemindListService::$not_must_params);
        try {
            Validation::validate($params, ContractRemindListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ContractRemindListService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
