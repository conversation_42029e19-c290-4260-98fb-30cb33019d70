<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\ContractTemplateBaseService;
use App\Modules\Contract\Services\ContractTemplateVersionService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractTemplateVersionController extends BaseController
{
    /**
     * 搜索列表
     * @Permission(action='contract.template.manage.contract_template_version')
     *
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params      = $this->request->get();
        $staff_lists = ContractTemplateVersionService::getInstance()->authorityStaffs();
        if (!in_array($this->user['id'], $staff_lists)) {
            $params['department_id'] = [$this->user['sys_department_id']];
        }
        $res = ContractTemplateVersionService::getInstance()->getList($params, 'tpl_list');
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 使用列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74812
     * @return Response|ResponseInterface
     */
    public function useListAction()
    {
        $params                  = $this->request->get();
        $params['state']         = ContractEnums::CONTRACT_TEMPLATE_STATE_2;
        $params['department_id'] = $this->user['node_department_id'];
        $res = ContractTemplateVersionService::getInstance()->useList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 模版编辑
     * @Permission(action='contract.template.manage.contract_template_version')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $data = $this->request->get();
        $this->logger->info('合同版本管理编辑参数' . json_encode($data, JSON_UNESCAPED_UNICODE));

        Validation::validate($data, ContractTemplateVersionService::$validate_edit);

        $lock_key = md5('contract_template_version_edit_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return ContractTemplateVersionService::getInstance()->edit($data, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 模版新增
     * @Permission(action='contract.template.manage.contract_template_version')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        $this->logger->info('合同版本管理新增参数' . json_encode($data, JSON_UNESCAPED_UNICODE));
        Validation::validate($data, ContractTemplateVersionService::$validate_add);

        $lock_key = md5('contract_template_version_add_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($data) {
            return ContractTemplateVersionService::getInstance()->add($data, $this->user);
        }, $lock_key, 30);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='contract.template.manage.contract_template_version')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractTemplateVersionService::getInstance()->getDetail($data, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 下载预览模版
     * @Permission(action='contract.template.manage.contract_template_version')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractTemplateVersionService::getInstance()->download($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Token
     * 新增模版初始化数据
     * 基础模版列表
     */
    public function defaultAction()
    {
        $data = $this->request->get();
        $res = ContractTemplateBaseService::getInstance()->templateList($data);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Token
     * 模版信息
     *
     */
    public function templateBaseInfoAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1', 'lang' => 'Required|StrLenGeLe:1,10']);
        $res = ContractTemplateVersionService::getInstance()->templateBaseInfo($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 电子合同制作列表的筛选项(可用版本相关的)
     * @Token
     *
     */
    public function listEnumsAction()
    {
        $res = ContractTemplateVersionService::getInstance()->getListEnums($this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

}
