<?php

namespace App\Modules\Contract\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\ContractSubFileService;
use App\Modules\Contract\Services\ContractTemplateBaseService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractTemplateBaseController extends BaseController
{
    /**
     * 搜索列表
     * @Permission(action='contract.template.manage.contract_template')
     *
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res    = ContractTemplateBaseService::getInstance()->getList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 模版编辑
     * @Permission(action='contract.template.manage.contract_template')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractTemplateBaseService::$validate_edit);
        $res = ContractTemplateBaseService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 模版新增
     * @Permission(action='contract.template.manage.contract_template')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ContractTemplateBaseService::validateAdd());
        $res = ContractTemplateBaseService::getInstance()->add($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 查看
     * @Permission(action='contract.template.manage.contract_template')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, ['id' => 'Required|IntGe:1']);
        $res = ContractTemplateBaseService::getInstance()->getDetail($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @Token
     * 新增模版初始化数据
     * 子文件
     */
    public function defaultAction()
    {
        $data = $this->request->get();

        $contract_enums = ContractSubFileService::getInstance()->getElectronicContractEnums();
        $contract_type = implode(',', $contract_enums['contract_type']);
        Validation::validate($data, ['contract_type' => 'IntIn:' . $contract_type, 'department_id' => 'Required|StrLenGeLe:1,20']);
        $res = ContractSubFileService::getInstance()->fileList($data);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

}
