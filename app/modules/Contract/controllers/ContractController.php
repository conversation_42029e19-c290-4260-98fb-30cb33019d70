<?php

namespace App\Modules\Contract\Controllers;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Services\AddService;
use App\Modules\Contract\Services\ArchiveAddService;
use App\Modules\Contract\Services\ArchiveDetailService;
use App\Modules\Contract\Services\ArchiveListService;
use App\Modules\Contract\Services\ArchiveUpdateService;
use App\Modules\Contract\Services\BaseService;
use App\Modules\Contract\Services\ContractConsumerService;
use App\Modules\Contract\Services\ContractFlowService;
use App\Modules\Contract\Services\DetailService;
use App\Modules\Contract\Services\ListService;
use App\Modules\Contract\Services\UpdateService;
use App\Modules\CrmQuotation\Services\ApplyService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ContractController extends BaseController
{
    /**
     * 合同添加
     * @Permission(action='contract.apply.apply')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        $data = BaseService::handleParams($data, AddService::$not_must_params);
        Validation::validate($data, (new BaseService())->getValidateParams($data, false));
        $res = AddService::getInstance()->one($data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 电子合同制作-发起申请 / 其他合同-发起申请
     * @Token
     */
    public function getCnoAction()
    {
        return $this->returnJson(ErrCode::$SUCCESS, '', AddService::getInstance()->getDefault($this->user));
    }

    /**
     * 合同列表
     * @Permission(action='contract.apply.search')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        Validation::validate($params, ListService::$validate_list_search);

        $list = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_APPLY);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同审核列表
     * @Permission(action='contract.audit.search')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        Validation::validate($params, ListService::$validate_list_search);

        $list = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_AUDIT);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 合同详情
     * @Permission(action='contract.apply.view')
     * @return mixed
     */
    public function detailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getAuditDetail($data, $this->user['id'], ContractEnums::MODULE_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 快速复制回显
     * @Permission(action='contract.apply.view')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function editDetailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail);

        $res = DetailService::getInstance()->editDetail($data['id'], $this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS, $res['message'] ?? '', $res['data']);
    }

    /**
     * 合同详情
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        $cno = $this->request->get('cno');
        try {
            Validation::validate($data, []);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        if ($id) {
            $res = DetailService::getInstance()->getAuditDetail($data, NULL);
        }elseif ($cno) {
            $res = DetailService::getInstance()->getContractId($cno, NULL);
        }else{
            try {
                Validation::validate($data, BaseService::$validate_detail);
            } catch (ValidationException $e) {
                return $this->returnJson($e->getCode(), $e->getMessage());
            }
        }

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同审核详情
     * @Permission(action='contract.audit.view')
     *
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, array_merge(BaseService::$validate_detail, BaseService::$validate_fyr));
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getAuditDetail($data, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 查看报价单-权限跟随审核详情
     * @Permission(action='contract.audit.view')
     * @return Response|ResponseInterface
     */
    public function quotationDetailAction()
    {
        try {
            $quotation_no = $this->request->get('quotation_no', 'string');
            Validation::validate(['quotation_no' => $quotation_no],['quotation_no' => 'Required|StrLenGe:1']);
            $data = (new ApplyService())->detail(0, $quotation_no, $this->user,2);
        } catch (ValidationException $validationException) {
            $this->logger->info('crm_apply_detail ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (Exception $exception) {
            $this->logger->error('crm_apply_detail ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }
    /**
     * 查看报价单-权限跟随【报价管理】-【详情操作】
     * @Permission(action='crm.apply.view')
     *
     * @return Response|ResponseInterface
     */
    public function quotationViewAction()
    {
        try {
            $quotation_no = $this->request->get('quotation_no', 'string');
            Validation::validate(['quotation_no' => $quotation_no],['quotation_no' => 'Required|StrLenGe:1']);
            $data = (new ApplyService())->detail(0, $quotation_no, $this->user,3);
        } catch (ValidationException $validationException) {
            $this->logger->info('crm_apply_detail ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (Exception $exception) {
            $this->logger->error('crm_apply_detail ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }

    /**
     * 合同编辑
     * @Permission(action='contract.apply.edit')
     * @return mixed
     * @throws ValidationException
     */
    public function updateAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        $data = BaseService::handleParams($data, AddService::$not_must_params);
        Validation::validate($data, (new BaseService())->getValidateParams($data, true));
        $res = UpdateService::getInstance()->one($id, $data, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同下载
     * @Permission(action='contract.apply.download')
     * @return mixed
     */
    public function downloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);
            ArchiveAddService::getInstance()->one($id, $this->user);
            // 加锁
            $lock_key = md5('contract_download_' . $id . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($id){
                 return DetailService::getInstance()->download($id, $this->user['id']);
            }, $lock_key, 30);

            if (empty($res['url'])) {
                throw new ValidationException('File download failed, please try again', ErrCode::$VALIDATE_ERROR);
            }

        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function outPutAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        $url = $data['url'] ?? '';
        $name = $data['name'] ?? '';
        try {
            if (!empty($id)) {
                Validation::validate($data, BaseService::$validate_detail);
            }

            $oss_url = DetailService::getInstance()->outPut($id,$url,$name);
            if (!empty($oss_url)) {
                $this->response->redirect($oss_url, true, 301);
            } else {
                throw new ValidationException('File download failed, please try again', ErrCode::$VALIDATE_ERROR);
            }
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 合同审核通过
     * @Permission(action='contract.audit.audit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function approveAction()
    {
        $contract_id = $this->request->get('id', 'int', 0);
        $note = $this->request->get('note', 'trim', '');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractFlowService::$validate_approve);

        $lock_key = md5('other_contract_approve_' . $contract_id);
        $res = $this->atomicLock(function () use ($contract_id, $note) {
            return (new ContractFlowService())->approve($contract_id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 合同审核拒绝
     * @Permission(action='contract.audit.audit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $contract_id = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractFlowService::$validate_reject);

        $lock_key = md5('other_contract_reject_' . $contract_id);
        $res = $this->atomicLock(function () use ($contract_id, $note) {
            return (new ContractFlowService())->reject($contract_id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 合同撤销
     * @Permission(action='contract.apply.cancel')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $contract_id = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');
        Validation::validate(['id' => $contract_id, 'note' => $note], ContractFlowService::$validate_cancel);

        $lock_key = md5('other_contract_cancel_' . $contract_id);
        $res = $this->atomicLock(function () use ($contract_id, $note) {
            return (new ContractFlowService())->cancel($contract_id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * @return Response|ResponseInterface
     */
    public function archiveAddAction()
    {
        $data = $this->request->get();
        $contract_id = $this->request->get('id', 'int');
        try {
            Validation::validate($data, []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        ArchiveAddService::getInstance()->one($contract_id, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok');
    }

    /**
     * 合同归档回显
     * @Permission(action='contract.archive.view')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function archiveDetailAction()
    {
        $data = $this->request->get();
        $archive_id = $this->request->get('id', 'int');
        try {
            Validation::validate($data, ArchiveDetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = ArchiveDetailService::getInstance()->getDetail($archive_id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 数据查询详情
     * @Permission(action='contract.electronic_audit_query.audit_view')
     *
     * @return Response|ResponseInterface
     */
    public function searchDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = DetailService::getInstance()->getAuditDetail($data,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同归档操作
     * @Permission(action='contract.archive.perform')
     *
     * @return Response|ResponseInterface
     */
    public function archivePerformAction()
    {
        $data = $this->request->get();
        $archive_id = $this->request->get('id', 'int');
        try {
            Validation::validate($data, ArchiveUpdateService::$validate_archive);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ArchiveUpdateService::getInstance()->one($archive_id, $data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message']);
        } else {
            return $this->returnJson($res['code'], $res['message']);
        }
    }

    /**
     * 合同配置客户id
     * @Permission(action='contract.archive.consumer')
     * @return Response|ResponseInterface
     */
    public function configure_consumerAction()
    {
        try {
            $data = $this->request->get();

            Validation::validate($data, ContractConsumerService::$validate_consumer_id);
            $res = ContractConsumerService::getInstance()->addConsumerId($data['cno'], $data['quotation']);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['message']);
            } else {
                return $this->returnJson($res['code'], $res['message']);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (BusinessException $e) {
            $this->getDI()->get('logger')->warning(
                'function =>get_consumer_id' .
                'message =>'. $e->getMessage() .
                'request=>' . $e->getTraceAsString() .
                'params=>' . json_encode($data , JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson($e->getCode(), $message = $this->t->_('retry_later'));
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(
                'function =>get_consumer_id' .
                'message =>'. $e->getMessage() .
                'request=>' . $e->getTraceAsString() .
                'params=>' . json_encode($data , JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson($e->getCode(), $message = $this->t->_('retry_later'));
        }
    }

    /**
     * 查询MS客户id
     * @Permission(action='contract.archive.consumer')
     *
     * @return Response|ResponseInterface
     */
    public function get_consumer_idAction(){
        try {
            $data = $this->request->get();
            Validation::validate($data, ContractConsumerService::$validate_check_consumer_id);
            $res = ContractConsumerService::getInstance()->checkConsumerId($data['consumer_id']);
            return $this->returnJson(ErrCode::$SUCCESS,'ok', $res);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(
                'function =>get_consumer_id' .
                'message =>'. $e->getMessage() .
                'request=>' . $e->getTraceAsString() .
                'params=>' . json_encode($data , JSON_UNESCAPED_UNICODE)
            );
            return $this->returnJson($e->getCode(), $message = $this->t->_('retry_later'));
        }
    }

    /**
     * 报价单号查询
     * @Permission(action='contract.apply.apply')
     */
    public function searchQuotationAction() {
        $quotation_no = $this->request->get('quotation_no', 'trim');
        if(is_null($quotation_no)) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }

        $res = ContractConsumerService::getInstance()->searchQuotation($quotation_no);
        if($res['code'] == ErrCode::$SUCCESS){
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同归档列表
     * @Permission(action='contract.archive.search')
     *
     * @return Response|ResponseInterface
     */
    public function archiveListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ArchiveListService::$not_must_params);
        try {
            Validation::validate($params, ArchiveListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = ArchiveListService::getInstance()->getList($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 数据查询
     * @Permission(action='contract.electronic.audit_query')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        Validation::validate($params, ListService::$validate_list_search);
        $list = ListService::getInstance()->getList($params, $this->user, ListService::LIST_TYPE_SEARCH);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 数据查询-导出excel
     * @Permission(action='contract.electronic_audit_query.audit_export')
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        try {
            $params = BaseService::handleParams($params, ListService::$not_must_params);
            Validation::validate($params, ListService::$validate_export_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5('other_contract_lock_key_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            $total = ListService::getInstance()->getTotal($params, $this->user);
            if ($total > ContractEnums::CONTRACT_ELECTRONIC_EXPORT_MAX) {
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], Enums\DownloadCenterEnum::CONTRACT_OTHER_EXPORT, $params);
                $result['data'] = [
                    'export_method' => Enums\DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            }else{
                $result = ListService::getInstance()->getDownloadList($params, $this->user);
                $result['data'] = [
                    'export_method' => Enums\DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']) ? $result['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * @Token
     * @return Response|ResponseInterface
     */
    public function templatesAction()
    {
        $company_code = $this->request->get('company_code', 'int', '0');
        $data = EnumsService::getInstance()->getContractCategoryTreeList($company_code);
        return $this->returnJson(ErrCode::$SUCCESS,'success', $data);
    }

    /**
     * 合同管理 - 合同申请 - 审批流记录下载
     * @Permission(action='contract.apply.wfdownload')
     *
     * @return mixed
     * @throws Exception
     */
    public function wfDownloadAction()
    {
        $param = $this->request->get();
        $id    = $this->request->get('id', 'int');

        Validation::validate($param, BaseService::$validate_detail);

        // 加锁处理
        $lock_key = md5('contract_wf_download_' . $id . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($id) {
            return DetailService::getInstance()->wfDownload($id, $this->user['id']);
        }, $lock_key, 20);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 合同归档 - 下载
     * @Permission(action='contract.archive.download')
     * @return mixed
     */
    public function archiveDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, BaseService::$validate_detail);

            // 加锁
            $lock_key = md5('contract_archive_download_' . $id . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($id){
                return DetailService::getInstance()->genArchiveDownload($id, $this->user['id']);
            }, $lock_key, 30);

            if (empty($res['url'])) {
                throw new ValidationException('File download failed, please try again', ErrCode::$VALIDATE_ERROR);
            }

        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }
}
