<?php

namespace App\Modules\Contract\Controllers;

use App\Library\BaseController as Controller;
use app\models\backyard\ToolStaffInfoModel;
use App\Modules\User\Services\UserService;

/**
 * @name BaseController
 * @desc 所有web模块控制器都继承自该控制器
 */
abstract class BaseController extends Controller
{
    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);
    }

    /**
     * 用户信息初始化- 兼容工具号
     */
    protected function getLoginUser()
    {
        $uid = $this->request->getUid();
        if ($uid) {
            $us   = new UserService();
            $user = $us->getUserById($uid);

            if (
                isCountry('MY')
                &&
                !$user->node_department_id
                &&
                !$user->sys_department_id
            ) {
                //查询工具号
                $toolUser = (new ToolStaffInfoModel())->getToolStaffInfo($uid);

                if ($toolUser) {
                    $user->node_department_id = $toolUser['node_department_id'] ?? null;
                    $user->sys_department_id  = $toolUser['sys_department_id'] ?? null;
                }
            }

            return $this->format_user($user);
        } else {
            return [];
        }
    }
}
