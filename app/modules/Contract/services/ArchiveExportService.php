<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;

class ArchiveExportService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param string $filename
     * @param array $exportData
     * @return bool | array
     */
    public function exportContractArchive($filename,$exportData = [])
    {
        $headerMap = [
            static::$t->_('global.no'),
            static::$t->_('contract.apply_date'),
            static::$t->_('contract.cname'),
            static::$t->_('contract.cno'),
            static::$t->_('contract.sales_contract_no'),
            static::$t->_('contract.template'),
            static::$t->_('contract.approved_date'),
            static::$t->_('contract.amount'),
            static::$t->_('contract.status'),
            static::$t->_('borrow.apply_name')
        ];

        $filterData = [];
        $header = array_values($headerMap);

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();
        foreach ($exportData as $key => $item) {
            $filterData[$key][] = strval($key + 1);
            $filterData[$key][] = $item['filing_at'] ?: '';
            $filterData[$key][] = $item['cname'] ?: '';
            $filterData[$key][] = $item['cno'] ?: '';
            $filterData[$key][] = ''; // 销售合同编号，后期待填充字段

            // 直属分类信息
            $contract_category_info = $contract_category_map[$item['template_id']] ?? [];

            // 若直属分类是二级分类, 则需拼接一级分类
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $filterData[$key][] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $contract_category_info['label'];
            } else {
                $filterData[$key][] = $contract_category_info['label'] ?? '';
            }

            $filterData[$key][] = $item['approved_at'] ?: '';
            $filterData[$key][] = bcdiv($item['amount'], 1000, 2);
            $status = ContractEnums::$contract_archive_status[$item['status']] ?? '';
            $filterData[$key][] = static::$t->_($status);
            $filterData[$key][] = $item['create_name'] ?: '';
        }
        $result = $this->exportExcel($header,$filterData,$filename);
        if ($result['code'] != ErrCode::$SUCCESS) {
            return false;
        }

        return $result;
    }
}
