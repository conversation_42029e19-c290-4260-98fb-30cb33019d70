<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\User\Models\AttachModel;

class ArchiveUpdateService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * @param $archive_id
     * @param $data
     * @param $user
     * @return array
     */
    public function one($archive_id, $data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $archive = ContractArchive::findFirst([
            'conditions' => 'id = ?1 and status = ?2',
            'bind' => [1 => $archive_id, 2 => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]
        ]);
        if (empty($archive)) {
            return [
                'code' => ErrCode::$CONTRACT_ARCHIVE_GET_INFO_ERROR,
                'message' => static::$t->_('contract_archive_done'),
            ];
        }
        $data = $this->handleData($data, $user);
        try {
            $contract_file = $data['contract_file'];
            $data['contract_file'] = "";
            $bool = $archive->i_update($data);
            if ($bool === false) {
                throw new BusinessException('更新归档信息失败', ErrCode::$CONTRACT_ARCHIVE_UPDATE_INFO_ERROR);
            }

            //只改一次，所以没有删除
            if(!empty($contract_file)){
                $attachArr = [];
                foreach ($contract_file as $k=>$v){
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE;
                    $tmp['oss_bucket_key'] = $archive_id;
                    $tmp['bucket_name'] = $v['bucket_name'];
                    $tmp['object_key'] = $v['object_key'];
                    $tmp['file_name'] = $v['file_name'];
                    $attachArr[] = $tmp;
                }
                $attach = new AttachModel();
                $attach_bool = $attach->batchInsert($attachArr);
                if($attach_bool === false){
                    throw new BusinessException('归档合同附件创建失败='.json_encode($contract_file,JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('contract-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        //$data['contract_file'] = $this->handle_oss_file($data['contract_file']);
        $data['status'] = ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL;
        $data['updated_at'] = date('Y-m-d H:i:s');
        $data['filing_at'] = date('Y-m-d H:i:s');
        $data['filing_id'] = $user['id'] ?? 0;
        $data['filing_name'] = $user['name'] ?? '';
        $data['holder_name'] = $data['holder_name'] ?? '';
        return $data;
    }

    /**
     * 电子合同签约
     * 自动归档
     *
     * @param $data
     * @param array $other_attachment 其他附件
     * @return bool
     * @throws BusinessException
     */
    public function autoArchive($data, $other_attachment = [])
    {
        $contract = contract::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $data['relate_id']]
        ]);

        if (!empty($contract)) {
            $archive = ContractArchive::findFirst([
                'conditions' => 'cno = :cno: and status = :status:',
                'bind'       => ['cno' => $contract->cno, 'status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]
            ]);

            if (empty($archive)) {
                return true;
            }

            $attach = new AttachModel();
            if (strchr($data['contract_name'], '.') === false) {
                $extension = strchr($data['file_url'],'.');
                $data['contract_name'] = $data['contract_name'] . $extension;
            }

            // 附件
            $attach_arr = [];

            // 其他附件
            foreach ($other_attachment as $att) {
                $attach_arr[] = [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT,
                    'sub_type' => 0,
                    'oss_bucket_key' => $contract->id,
                    'bucket_name' => $att['bucket_name'],
                    'object_key' => $att['object_key'],
                    'file_name' => $att['file_name']
                ];
            }

            // 盖章合同
            $attach_arr[] = [
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE,
                'sub_type' => 0,
                'oss_bucket_key'  => $archive->id,
                'bucket_name'     => $data['bucket_name'],
                'object_key'      => $data['file_url'],
                'file_name'       => $data['contract_name']
            ];

            $this->logger->info('电子合同-签字完成-合同自动归档附件=' . json_encode($attach_arr, JSON_UNESCAPED_UNICODE));
            if ($attach->batch_insert($attach_arr) === false) {
                throw new BusinessException('电子合同-签字完成-合同自动归档附件写入失败', ErrCode::$BUSINESS_ERROR);
            }

            $archive_data = [
                'filing_at'   => date('Y-m-d H:i:s'),
                'updated_at'  => date('Y-m-d H:i:s'),
                'filing_id'   => $data['created_id'],
                'filing_name' => $data['created_name'],
                'holder_name' => $data['holder_name'] ?? $data['created_name'],
                'status'      => ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,
            ];

            return $archive->save($archive_data);
        }
        return true;
    }
}
