<?php
namespace App\Modules\Contract\Services\My;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractElectronicModel;
use App\Models\oa\ContractElectronicStaffSignConfigModel;
use App\Models\oa\SettingEnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Services\ContractElectronicService as BaseService;
use App\Modules\User\Models\StaffInfoModel;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;

class ContractElectronicService extends BaseService
{
    /**
     * 处理合同表单数据
     *
     * @param $data
     * @param $lang
     * @return array
     */
    public function contractParams($data, $lang)
    {
        $field_data = [];

        // 一维结构上的合同编号
        $field_data['contract_no']             = $data['contract_no'] ?? '';
        $field_data['contract_name']           = $data['contract_name'] ?? '';
        $field_data['department_id']           = $data['department_id'] ?? '';
        $field_data['business_approve_status'] = $data['business_approve_status'] ?? '';
        $field_data['created_id']              = $data['created_id'] ?? 0;

        //获取特殊字段 翻译配置
        foreach ($data['form_rule'] as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $field_data[$v1['field']] = $v1['value'];
            }
        }

        // 获取枚举数据
        $enums_data = $this->formSelectEnums($data['department_id']);

        //处理枚举选项
        foreach ($enums_data as $k => $v) {
            if ($k == 'customer_segment_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (isset($field_data['customer_segment']) && ($field_data['customer_segment'] == $v2['id'])) {
                        $enums_data['customer_segment_list'][$k2]['is_customer_segment'] = true;
                    }
                }
            }

            if ($k == 'service_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (is_array($field_data['service']) && in_array($v2['id'], $field_data['service'])) {
                        $enums_data['service_list'][$k2]['is_service'] = true;
                    }
                }
            }

            if ($k == 'cod_free_type_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (isset($field_data['cod_free_type']) && ($field_data['cod_free_type'] == $v2['id'])) {
                        $enums_data['cod_free_type_list'][$k2]['is_cod_free_type'] = true;
                    }
                }
            }

            if ($k == 'billing_cycle_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (isset($field_data['billing_cycle']) && ($field_data['billing_cycle'] == $v2['id'])) {
                        $enums_data['billing_cycle_list'][$k2]['is_billing_cycle'] = true;
                    }
                }
            }

            if ($k == 'credit_terms_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (isset($field_data['credit_terms']) && ($field_data['credit_terms'] == $v2['id'])) {
                        $enums_data['credit_terms_list'][$k2]['is_credit_terms'] = true;
                    }
                }
            }

            if ($k == 'company_business_entity_type_list') {
                foreach ($v as $k2 => $v2) {
                    //处理选择
                    if (is_array($field_data['company_business_entity_type']) && in_array($v2['id'],$field_data['company_business_entity_type'])) {
                        $enums_data['company_business_entity_type_list'][$k2]['is_company_business_entity_type'] = true;
                    }
                }
            }
        }

        //合同审核完成标识
        if (isset($field_data['business_approve_status'] ) && ($field_data['business_approve_status'] == Enums::CONTRACT_STATUS_APPROVAL)) {
            $field_data['is_contract_approval'] = true;
        }

        // 合成签章数据
        $sing_data = $this->getElectronicContractSignData($field_data);

        return array_merge($field_data ,$sing_data, $enums_data);
    }

    public function getElectronicContractSignData(array $field_data, int $sign_step = 0)
    {
        // 签字变量
        $sign_field_data = [
            'contract_no'          => '', // 合同编号
            'authorized_name'      => '', // 授权人
            'authorized_job_title' => '', // 授权人职位
            'current_date'         => '', // 当前日期
            'flash_bd_name'        => '', // 乙方BD
            'flash_bd_job_title'   => '', // 乙方BD职位
        ];

        // 合成签名数据
        if (!empty($field_data['is_contract_approval'])) {
            //Agreement No
            $sign_field_data['contract_no'] = $this->generateAgreementNo();

            //生成授权人签字
            $authorizedInfo                          = $this->getElectronicContractAuthorizedPersonInfo($field_data['department_id'],
                0);
            $sign_field_data['authorized_sign_img']  = $authorizedInfo['sign_img'];
            $sign_field_data['authorized_name']      = $authorizedInfo['name'];
            $sign_field_data['authorized_job_title'] = $authorizedInfo['job_title'];

            // 乙方BD页脚小签
            $bdInfo                                = $this->getReviewerSignImg(['id' => $field_data['created_id'] ?? 0]);
            $sign_field_data['flash_bd_sign_img']  = $bdInfo['sign_img'] ?? '';
            $sign_field_data['flash_bd_name']      = $bdInfo['name'] ?? '';
            $sign_field_data['flash_bd_job_title'] = $bdInfo['job_title'] ?? '';

            $sign_field_data['current_date'] = date('Y-m-d');
        }

        return $sign_field_data;
    }

    /**
     * 获取乙方授权人的电子签章 和 职位
     *
     * @param int $department_id 电子合同所属部门
     * @param int $staff_id
     * @return array
     */
    public function getElectronicContractAuthorizedPersonInfo(int $department_id, int $staff_id)
    {
        $staff_info = [];
        // 授权人配置
        $info = EnumsService::getInstance()->getSettingEnvValueMap('retail_contract_electronic_staff_list');

        if (empty($info)) {
            return $staff_info;
        }

        $staff_info['staff_id']  = $info['id'] ?? 0;
        $staff_info['name']      = $info['name'] ?? '';
        $staff_info['sign_img']  = $info['sign_img'] ?? '';
        $staff_info['job_title'] = $info['job_title'] ?? '';

        // 授权人职位
        return $staff_info;
    }

    /**
     * 生成合同编号
     * 格式：MYRETAIL + YYMM + 序列号(3位)
     * 例如：MYRETAIL2507001 (25年07月第001份合同)
     * @return string
     */
    public function generateAgreementNo(): string
    {
        // 获取系统配置中的初始序列号
        $initial_sequence = SettingEnvModel::getValByCode('contract_initial_sequence_number', 0);
        
        // 生成日期部分 (YYMM格式)
        $date_part = date('ym');
        
        // 查询当月已生成的合同数量
        $month_start = date('Y-m-01 00:00:00');
        $month_end = date('Y-m-t 23:59:59');
        
        $month_count = ContractElectronicModel::count([
            'conditions' => 'created_at >= :start_time: AND created_at <= :end_time: AND contract_no LIKE :prefix:',
            'bind' => [
                'start_time' => $month_start,
                'end_time' => $month_end,
                'prefix' => 'MYRETAIL' . $date_part . '%'
            ]
        ]);
        
        // 计算当前序列号：初始序列号 + 当月已生成数量 + 1 (确保比配置大1)
        $current_sequence = intval($initial_sequence) + $month_count + 1;
        
        // 生成完整的合同编号，序列号格式化为3位数字
        return 'MYRETAIL' . $date_part . sprintf('%03d', $current_sequence);
    }

    /**
     * 获取BD签名
     * @param array $user
     * @return array
     * @throws ValidationException
     */
    public function getReviewerSignImg(array $user = [])
    {
        // 获取BD小签配置
        $signModel = ContractElectronicStaffSignConfigModel::findFirst([
            'conditions' => 'staff_id = :staff_id:',
            'bind'       => ['staff_id' => $user['id']],
            'columns'    => ['sign_img'],
        ]);

        //获取BD签名和职位
        $staffInfo = StaffInfoModel::findFirst(
            [
                'conditions' => 'id = :uid:',
                'bind' => [
                    'uid' => $user['id'],
                ],
            ]
        );

        //职位
        $staffJobInfo = (new HrJobTitleRepository())->getJobTitleInfo($staffInfo->job_title ?? 0, false);

        return [
            'sign_img'  => $signModel->sign_img ?? '',
            'name'      => $staffInfo->name ?? '',
            'job_title' => $staffJobInfo->job_name ?? '',
        ];
    }

    /**
     * 表单枚举
     *
     * @param int $department_id 电子合同所属一级部门
     * @return array
     */
    public function formSelectEnums(int $department_id = 0)
    {
        $data               = [];
        $enums_data         = EnumsService::getInstance()->getSettingEnvValueMap('retail_form_rule_select_enums');


        $t = self::getTranslation('en');

        foreach ($enums_data['customer_segment_list'] as $key => $value) {
            $data['customer_segment_list'][] = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];
        }

        foreach ($enums_data['service_list'] as $key => $value) {
            $data['service_list'][] = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];
        }

        foreach ($enums_data['cod_free_type_list'] as $key => $value) {
            $data['cod_free_type_list'][] = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];
        }

        foreach ($enums_data['billing_cycle_list'] as $key => $value) {
            $data['billing_cycle_list'][] = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];
        }

        foreach ($enums_data['credit_terms_list'] as $key => $value) {
            $data['credit_terms_list'][] = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];
        }

        foreach ($enums_data['company_business_entity_type_list'] as $key => $value) {
            $info = [
                'id'   => $value['id'],
                'name' => $t->_($value['name']),
            ];

            if ($value['id'] == 5) {
                $info['is_other'] = true;
            }

            $data['company_business_entity_type_list'][] = $info;
        }

        return $data;
    }
}