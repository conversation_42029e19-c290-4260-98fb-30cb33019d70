<?php
namespace App\Modules\Contract\Services\My;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\oa\ContractSubFileModel;
use App\Modules\Contract\Services\ContractTemplateVersionService as BaseService;
use GuzzleHttp\Exception\GuzzleException;

class ContractTemplateVersionService extends BaseService
{
    /**
     * 模版表单字段和文件内容 - 复制泰国逻辑 去除了部分字段
     * @param $data
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function handleTemplateData($data)
    {
        //查询子文件信息
        $unique_no = array_column($data['subfile_info'], 'unique_no');

        $sub_file_info = ContractSubFileModel::find([
            'conditions' => 'unique_no in ({unique_no:array})',
            'bind'       => ['unique_no' => $unique_no]
        ])->toArray();
        if (empty($sub_file_info)) {
            throw new BusinessException('模版合同版本创建子文件信息不存在 ' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
        }

        $sub_file_info = array_column($sub_file_info, null, 'unique_no');
        $sub_file_infos = [];
        foreach ($unique_no as $k => $v) {
            $sub_file_infos[] = $sub_file_info[$v];
        }

        $field_arr         = [];
        $base_key          = [];
        $template_form_arr = [];

        // 拼接html模版
        $html_config = $this->getElectronicContractHtmlConfig($data['department_id']); //子文件更新
        $template_html = $html_config['header_html'];

        //子文件数量 最后一个子文件不需要分页
        $file_count = count($sub_file_infos);

        foreach ($sub_file_infos as $key => $item) {
            //拼接html模版
            if ($file_count - 1 == $key) {//最后一个子文件不需要分页
                $template_html .= $item['contract_content'];
            } else {
                $template_html .= $item['contract_content'] . '<div style="page-break-before: always"></div>';
            }

            // 相关子文件基础信息和其下的表单字段名提炼
            if (!empty($item['file_form_rule'])) {

                foreach (json_decode($item['file_form_rule'], true) as $k => $v) {
                    //每个子文件表单规则 提取 基础信息key
                    $base_key[] = $k;
                    foreach ($v as $v_2) {
                        //每个基础key 下字段放到一起
                        $field_arr[] = $v_2;
                    }
                }
            }
        }

        $template_html .= $html_config['footer_html'];

        //$field_arr是所有变量字段 $template_html是模版头+子文件+尾部 $base_key是按照模块的key
        $file_name = 'template_version_' . $data['ver'] . '.ftl';

        //模版上传到oss
        $data['file_url'] = $this->createFtlFile($template_html, $file_name);

        //字段 和基础key 分别去重
        $base_key = array_unique($base_key);

        // 查询基础的表单验证规则
        $res             = $html_config['base_form'];


        // 组装子文件拼成的表单[表单数据单元]
        foreach ($base_key as $value) {
            $template_form_arr[$value] = $res[$value];
        }

        foreach ($template_form_arr as $k1 => &$value) {
            foreach ($value as $k => &$v) {
                // 提取合同模板表单表字段在全局表单字段配置中的规则(仅处理模板中的表单字段与全局表单的交集字段)
                if (!in_array($v['field'], $field_arr)) {
                    unset($value[$k]);
                }
            }

            $template_form_arr[$k1] = array_values($value);
        }

        // 合同模板变量对应的表单项
        $form_data = $this->contractParams(array_merge($template_form_arr,['department_id' => $data['department_id']]));

        // 合同模板当前版本的pdf样例文件
        $pdf_info = $this->generateElectronicContractPdfFile($data['department_id'], $data['file_url'], $form_data);

        return [
            'form_rule' => json_encode($template_form_arr, JSON_UNESCAPED_UNICODE),
            'file_url'  => $data['file_url'],
            'pdf_url'   => $pdf_info['object_key']
        ];
    }

    /**
     * 处理合同表单数据
     * @param $form_rule
     * @return array
     */
    public function contractParams($form_rule)
    {
        $field_data = [];

        //格式化数据取值
        foreach ($form_rule as $k => $v) {
            foreach ($v as $k1 => $v1) {
                $field_data[$v1['field']] = $v1['value'];

                // 不要默认值
                if (in_array($v1['field'],['billing_country','pickup_country'])) {
                    $field_data[$v1['field']] = '';
                }
            }
        }

        // 获取枚举数据
        $enums_data = (new ContractElectronicService())->formSelectEnums($form_rule['department_id']);

        // 获取枚举展示项
        return $this->getElectronicContractSignData(array_merge($field_data, $enums_data), 0);
    }

    /**
     * 生成电子合同pdf签字信息
     * @param array $field_data
     * @param int $sign_step
     * @return array|string[]
     */
    public function getElectronicContractSignData(array $field_data, int $sign_step = 0)
    {
        // 签字变量
        $sign_field_data = [
            'authorized_name'      => '', // 授权人姓名
            'authorized_job_title' => '', // 授权人职位
            'current_date'         => '', // 授权日期
            'flash_bd_name'        => '', // BD 姓名
            'flash_bd_job_title'   => '', // BD 职位
        ];

        return array_merge($sign_field_data , $field_data);
    }
}