<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingArea;
use App\Modules\Contract\Models\ContractStoreRentingDetailModel;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Modules\Contract\Models\HrStaffInfoModel;
use App\Modules\Contract\Models\PaymentExportLogModel;
use App\Modules\Contract\Models\SysStoreModel;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\User\Services\StaffService;

class ContractStoreRentingExportService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 导出租房付款数据
     *
     * @param array $login_user
     * @return mixed
     * @throws BusinessException
     */
    public function exportRentingPayment($login_user = [])
    {
        ini_set('memory_limit','512M');

        // 当前用户是否是指定工号
        $access_staff_ids = $this->getExportAccessStaffIds();
        $store_cate = [];
        if (!in_array($login_user['id'], $access_staff_ids)) {
            $store_cate = $this->getStoreCateByUid($login_user['id']);
        }

        //【房租金额】取合同及金额详情[待归档 且 付款方式为月付的合同编号、和待上传盖章合同
        //且 付款方式为月付的合同编号、和已归档 且 付款方式为月付的合同编号
        $renting_pending_items = $this->getPendingContractDetail($store_cate);

        // 【房租金额】付款单号为待审核或已通过待支付、或已通过已支付的合同
        $payment_items = $this->getPaymentContractList();
        $contract_ids = array_unique(array_filter(array_column($payment_items,'contract_no')));
        $renting_reject_items = [];
        if (!empty($contract_ids)) {
            // 【房租金额】已作废和已终止的仅导出月付 且 有过付款记录
            $renting_reject_items = $this->getRejectContractDetail($contract_ids,$store_cate);
        }

        $renting_all_items = array_merge($renting_pending_items,$renting_reject_items);
        $renting_contract_ids = !empty($renting_all_items) ? array_unique(array_filter(array_column(
            $renting_all_items,'contract_id'))) : [];

        //【区域服务费】取合同及区域服务费信息
        $area_pending_items = $this->getAreaPendingContractDetail($store_cate);
        $area_reject_items = [];
        if (!empty($contract_ids)) {
            //【区域服务费】已作废和已终止的仅导出月付 且 有过付款记录
            $area_reject_items = $this->getAreaRejectContractDetail($contract_ids,$store_cate);
        }
        unset($store_cate,$renting_pending_items,$renting_reject_items);

        $area_all_items = array_merge($area_pending_items,$area_reject_items);

        unset($area_pending_items,$area_reject_items);
        $area_contract_ids = !empty($area_all_items) ? array_unique(array_filter(array_column(
            $area_all_items,'contract_id'))) : [];

        // 取付款关联合同信息
        $renting_contract_ids = array_chunk($renting_contract_ids,ContractEnums::SQL_QUERY_PAGE_SIZE);
        $payment_relations = [];
        foreach ($renting_contract_ids  as $contract_id_items) {
            $the_payment_items = $this->getPaymentRelationList($contract_id_items,ContractEnums::COST_TYPE_RENTING);
            $payment_relations = array_merge($payment_relations, $the_payment_items);
        }
        $area_contract_ids = array_chunk($area_contract_ids,ContractEnums::SQL_QUERY_PAGE_SIZE);
        foreach ($area_contract_ids  as $contract_id_items) {
            $the_payment_items = $this->getPaymentRelationList($contract_id_items,ContractEnums::COST_TYPE_AREA);
            $payment_relations = array_merge($payment_relations, $the_payment_items);
        }
        unset($renting_contract_ids,$area_contract_ids);

        // 付款关联信息按每月拆分付款关联合同的费用日期
        $all_payment_month_split_date = $payment_contract_transfer_date = [];
        foreach ($payment_relations as $payment_item) {
            $payment_contract_transfer_date[$payment_item['rkey']][] = [
                'start_date' => $payment_item['cost_start_date'],
                'end_date' => $payment_item['cost_end_date'],
                'apply_no' => $payment_item['apply_no'],
                'pay_status' => $payment_item['pay_status'],
                'cost_type' => $payment_item['cost_type']
            ];
        }
        unset($payment_relations);


        // 归并合同导出数据
        $all_items = array_merge($renting_all_items,$area_all_items);
        unset($renting_all_items,$area_all_items);
        // 取房租金额和区域服务费的合同生效和结束日期
        $all_handle_items = $this->handleItems($all_items,$payment_contract_transfer_date);
        unset($all_items,$all_payment_month_split_date);

        // 上传文件并导出
        $filename = "payment_renting_" . date("YmdHis").'.xlsx';
        return $this->returnRentingPaymentData($filename,$all_handle_items);
    }

    /**
     * 取房租和区域服务费归档状态为待归档/待盖章/已归档的房租合同金额详情
     *
     * @param array $store_cate
     * @return mixed
     */
    public function getPendingContractDetail(&$store_cate = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,concat(c.contract_id,'_1') rkey,1 cost_type,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,
        c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status,GROUP_CONCAT(concat_ws('_',d.id,d.cost_start_date)) cost_start_date,GROUP_CONCAT(concat_ws('_',d.id,d.cost_end_date)) cost_end_date,
        GROUP_CONCAT(concat_ws('_',d.id,d.amount_no_tax)) amount_no_tax,GROUP_CONCAT(concat_ws('_',d.id,d.vat_rate)) vat_rate,
        GROUP_CONCAT(concat_ws('_',d.id,d.wht_category)) wht_category,GROUP_CONCAT(concat_ws('_',d.id,d.wht_rate)) wht_rate");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id and a.status in(1,2,4)', 'a');
        $builder->leftJoin(ContractStoreRentingDetailModel::class, 'd.contract_store_renting_id=c.id', 'd');
        $builder->andWhere('c.contract_lease_type = :contract_lease_type:',['contract_lease_type' => Enums::RENT_BY_MONTH]);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        $builder->inWhere('a.status', [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }

        $builder->groupBy('c.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取应付年月是当月、上一个月、下一个月的数据
     *
     * @return mixed
     */
    public function getvalidRentDueDate(){
        return [
            date('Y-m',strtotime('-1 month')),
            date('Y-m'),
            date('Y-m',strtotime('+1 month'))
        ];
    }

    /**
     * 取其它服务归档状态为待归档/待盖章/已归档的房租合同金额详情
     *
     * @param array $store_cate
     * @return mixed
     */
    public function getPendingOtherDetail($store_cate = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,
        c.deposit_amount,c.contract_deposit,c.total_amount_monthly,c.total_amount,c.signboard_tax,c.land_tax_amount,
        c.fire_insurance_amount,c.antimoth_amount,c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id', 'a');
        $builder->leftJoin(ContractStoreRentingDetailModel::class, 'd.contract_store_renting_id=c.id', 'd');
        $builder->andWhere('c.contract_lease_type = :contract_lease_type:',['contract_lease_type' => Enums::RENT_BY_MONTH]);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        $builder->inWhere('a.status', [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }
        $builder->groupBy('c.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     *  取有过关联付款记录的房租合同金额详情
     *
     * @return mixed
     */
    public function getPaymentContractList()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('distinct pd.contract_no');
        $builder->from(['p' => PaymentStoreRenting::class]);
        $builder->leftJoin(PaymentStoreRentingDetail::class, 'pd.store_renting_id=p.id', 'pd');
        $builder->leftJoin(ContractArchive::class, 'a.cno=pd.contract_no', 'a');
        $builder->inWhere('p.approval_status', [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]);
        $builder->inWhere('p.pay_status', [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY]);
        $builder->inWhere('a.status', [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID, ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]);

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取有关付款关联的已终止/已作废的房租金额详情
     * @param array $contract_ids
     * @param array $store_cate
     * @return mixed
     */
    public function getRejectContractDetail(&$contract_ids = [],&$store_cate = []){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,concat(c.contract_id,'_1') rkey,1 cost_type,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,
        c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status,GROUP_CONCAT(concat_ws('_',d.id,d.cost_start_date)) cost_start_date,GROUP_CONCAT(concat_ws('_',d.id,d.cost_end_date)) cost_end_date,
        GROUP_CONCAT(concat_ws('_',d.id,d.amount_no_tax)) amount_no_tax,GROUP_CONCAT(concat_ws('_',d.id,d.vat_rate)) vat_rate,
        GROUP_CONCAT(concat_ws('_',d.id,d.wht_category)) wht_category,GROUP_CONCAT(concat_ws('_',d.id,d.wht_rate)) wht_rate");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id', 'a');
        $builder->leftJoin(ContractStoreRentingDetailModel::class, 'd.contract_store_renting_id=c.id', 'd');
        if (!empty($contract_ids)) {
            $builder->inWhere('c.contract_id', $contract_ids);
        }
        $builder->andWhere('c.contract_lease_type = :contract_lease_type:', ['contract_lease_type' => Enums::RENT_BY_MONTH]);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        $builder->inWhere('a.status',[ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }
        $builder->groupBy('c.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取有关付款关联的已终止/已作废的其它服务金额
     * @param array $contract_ids
     * @param array $store_cate
     * @return mixed
     */
    public function getRejectOtherDetail($contract_ids = [],$store_cate = []){

        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,c.deposit_amount,
        c.contract_deposit,c.total_amount_monthly,c.total_amount,c.signboard_tax,c.land_tax_amount,c.fire_insurance_amount,c.antimoth_amount,
        c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id', 'a');
        $builder->leftJoin(ContractStoreRentingDetailModel::class, 'd.contract_store_renting_id=c.id', 'd');
        $builder->inWhere('c.contract_id',$contract_ids);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }
        $builder->groupBy('c.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取区域服务费合同及金额详情待归档/已归档列表
     *
     * @param array $store_cate
     * @return mixed
     */
    public function getAreaPendingContractDetail(&$store_cate = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,concat(c.contract_id,'_3') rkey,3 cost_type,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,
        c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status,GROUP_CONCAT(concat_ws('_',ca.id,ca.start_time)) cost_start_date,GROUP_CONCAT(concat_ws('_',ca.id,ca.end_time)) cost_end_date,
        GROUP_CONCAT(concat_ws('_',ca.id,ca.area_service_amount_no_tax)) amount_no_tax,GROUP_CONCAT(concat_ws('_',ca.id,ca.area_vat_rate)) vat_rate,
        GROUP_CONCAT(concat_ws('_',ca.id,ca.area_wht_category)) wht_category,GROUP_CONCAT(concat_ws('_',ca.id,ca.area_wht_rate)) wht_rate");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id', 'a');
        $builder->leftJoin(ContractStoreRentingArea::class, 'ca.contract_store_renting_id=c.id', 'ca');
        $builder->andWhere('c.contract_lease_type = :contract_lease_type:',['contract_lease_type' => Enums::RENT_BY_MONTH]);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        $builder->inWhere('a.status', [ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING,
            ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL,ContractEnums::CONTRACT_ARCHIVE_STATUS_NO_UPLOAD]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }
        $builder->groupBy('c.id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取费用类型为区域服务费，且有过关联付款记录的房租合同金额详情
     * @return mixed
     */
    public function getAreaPaymentContractList(){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('pd.contract_no,p.pay_status,pd.cost_start_date,pd.cost_end_date');
        $builder->from(['p' => PaymentStoreRenting::class]);
        $builder->leftJoin(PaymentStoreRentingDetail::class, 'pd.store_renting_id=p.id', 'pd');
        $builder->leftJoin(ContractStoreRentingModel::class, 'c.contract_id=pd.contract_no', 'c');
        $builder->andWhere('c.contract_lease_type = :contract_lease_type:',['contract_lease_type' => Enums::RENT_BY_MONTH]);
        $builder->inWhere('p.approval_status', [Enums::CONTRACT_STATUS_PENDING, Enums::CONTRACT_STATUS_APPROVAL]);
        $builder->inWhere('p.pay_status', [Enums::LOAN_PAY_STATUS_PENDING,Enums::LOAN_PAY_STATUS_PAY]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取费用类型为区域服务费，且有关付款关联的已终止/已作废的房租金额详情
     * @param array $contract_ids
     * @param array $store_cate
     * @return mixed
     */
    public function getAreaRejectContractDetail(&$contract_ids = [],&$store_cate = []){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("c.id,concat(c.contract_id,'_3') rkey,3 cost_type,c.contract_id,c.store_id,c.store_cate,c.contract_effect_date,c.contract_end,c.rent_due_date,
        c.bank_collection,c.contract_tax_no,c.store_cate,a.status archive_status,GROUP_CONCAT(concat_ws('_',ca.id,ca.start_time)) cost_start_date,GROUP_CONCAT(concat_ws('_',ca.id,ca.end_time)) cost_end_date,
        GROUP_CONCAT(concat_ws('_',ca.id,ca.area_service_amount_no_tax)) amount_no_tax,GROUP_CONCAT(concat_ws('_',ca.id,ca.area_vat_rate)) vat_rate,
        GROUP_CONCAT(concat_ws('_',ca.id,ca.area_wht_category)) wht_category,GROUP_CONCAT(concat_ws('_',ca.id,ca.area_wht_rate)) wht_rate");
        $builder->from(['c' => ContractStoreRentingModel::class]);
        $builder->leftJoin(ContractArchive::class, 'a.cno=c.contract_id', 'a');
        $builder->leftJoin(ContractStoreRentingArea::class, 'ca.contract_store_renting_id=c.id', 'ca');
        $builder->inWhere('c.contract_id', $contract_ids);
        $builder->inWhere('a.status', [ContractEnums::CONTRACT_ARCHIVE_STATUS_INVALID,ContractEnums::CONTRACT_ARCHIVE_STATUS_TERMINAL]);
        $builder->andWhere('c.contract_end >= :cur_date:',['cur_date' => date('Y-m-d')]);
        // 根据网点类型筛选
        if (!empty($store_cate)) {
            $builder->inWhere('c.store_cate',$store_cate);
        }
        $builder->groupBy('c.contract_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取合同归档状态为待归档//待盖章/已归档的付款关联合同信息
     * @param array $contract_id_items
     * @param integer $type
     * @return array
     */
    public function getPaymentRelationList(&$contract_id_items = [], $type = ContractEnums::COST_TYPE_RENTING){
        $builder = $this->modelsManager->createBuilder();
        $builder->columns("p.apply_no,pd.contract_no,concat(pd.contract_no,'_{$type}') rkey,p.pay_status,pd.cost_start_date,
        pd.cost_end_date,pd.cost_type");
        $builder->from(['p' => PaymentStoreRenting::class]);
        $builder->leftJoin(PaymentStoreRentingDetail::class, 'pd.store_renting_id=p.id', 'pd');
        $builder->inWhere('pd.contract_no', $contract_id_items);
        $builder->inWhere('p.approval_status', [Enums::CONTRACT_STATUS_APPROVAL,Enums::CONTRACT_STATUS_PENDING]);
        $builder->inWhere('p.pay_status', [Enums::PAYMENT_PAY_STATUS_PAY,Enums::PAYMENT_PAY_STATUS_PENDING]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 取配置的指定工号
     * @return array
     */
    public function getExportAccessStaffIds(){
        $access_staff_ids = EnvModel::getEnvByCode('export_payment_access_staff_ids','');
        $access_staff_ids = explode(',',$access_staff_ids);

        return $access_staff_ids ?: [];
    }

    /**
     * 取当前用户可取的网点类型
     * @param integer $uid
     * @return array
     */
    public function getStoreCateByUid($uid){
        $userDept      = HrStaffInfoModel::getUserInfo($uid, 'sys_department_id');
        $storeCateList = ContractStoreRentingService::getInstance()->getStoreCateList($userDept, []);

        return !empty($storeCateList) ? array_values(array_keys($storeCateList)) : [];
    }

    /**
     * 将合同金额和区域服务费生效和结束日期分段
     * @param array $contract_date
     * @param bool $is_split 默认需要拆分
     * @return mixed
     */
    public function transferToMonthSection($contract_date = [], $is_split = true){
        if (empty($contract_date)) {
            return [];
        }

        $split_data = [];
        $contract_date_yield = $this->getYieldItemList($contract_date,true);
        foreach ($contract_date_yield as $cdate) {
            $kcdate = $cdate[0] ?? 0;
            $vcdate = $cdate[1] ?? [];
            if (empty($vcdate['contract_effect_date']) || empty($vcdate['contract_end'])) {
                continue;
            }

            yield [$kcdate,$this->splitContractMonth($vcdate['contract_effect_date'],$vcdate['contract_end'],$is_split)];
        }
    }

    /**
     * 将付款信息费用开始和结束日期分段
     * @param array $payment_date
     * @return mixed
     */
    public function transferToPaymentMonthSection(&$payment_date = []){
        if (empty($payment_date)) {
            return [];
        }

        $split_data = [];
        foreach ($payment_date as $rkey => $payment_item) {
            foreach ($payment_item as $k => $cdate) {
                if (empty($cdate['start_date']) || empty($cdate['end_date'])) {
                    continue;
                }
                $cost_split_items = $this->splitContractMonth($cdate['start_date'],$cdate['end_date']);
                foreach ($cost_split_items as $item) {
                    $split_data[$rkey][] = [
                        'start_date' => $item['start_date'] ?? '',
                        'end_date' => $item['end_date'] ?? '',
                        'apply_no' => $cdate['apply_no'],
                        'pay_status' => $cdate['pay_status'],
                        'cost_type' => $cdate['cost_type']
                    ];
                }
            }
        }

        return $split_data;
    }

    /**
     * 将一个时间段拆成独立月
     * @param string $start_date
     * @param string $end_date
     * @param bool $is_split
     * @return array
     */
    public function splitContractMonth($start_date, $end_date, $is_split = true){
        $result = $result_month = [];

        if (!$is_split) {
            return [0 => $this->formatDate($start_date,$end_date,false,false)];
        } else {
            $s_year = date('Y',strtotime($start_date));
            $s_month = date('m',strtotime($start_date));
            $s_year_month = $s_year.'-'.$s_month;
            $e_year = date('Y',strtotime($end_date));
            $e_month = date('m',strtotime($end_date));
            $e_year_month = $e_year.'-'.$e_month;

            // 同一年
            if ($s_year == $e_year) {
                // 同一个月
                if ($s_month == $e_month) {
                    $result = [$s_year_month => $this->formatDate($start_date,$end_date,true)];
                } else {
                    // 首个月
                    $result = $this->getCurMonthStartEndDate($start_date,ContractEnums::MONTH_SPLIT_START);
                    // 满月的月份
                    if ((int)$e_month - (int)$s_month > 1) {
                        $nums = range((int)$s_month + 1,(int)$e_month - 1);
                        foreach ($nums as $num) {
                            if ($num < 10) {
                                $num = '0' . $num;
                            }
                            $result = $result + $this->getCurMonthStartEndDate($s_year.'-'.$num.'-01',ContractEnums::MONTH_SPLIT_FULL);
                        }
                    }
                    // 最后一个月
                    $result = $result + $this->getCurMonthStartEndDate($end_date,ContractEnums::MONTH_SPLIT_END);
                }
            } else { // 不同年
                // 首年年份
                $result_month = $result_month + $this->getCurYearStartEndDate($s_year_month, ContractEnums::MONTH_SPLIT_START);

                // 满年年份
                if ((int)$e_year - (int)$s_year > 1) {
                    $years = range((int)$s_year + 1,(int)$e_year - 1);
                    foreach ($years as $year) {
                        $result_month = $result_month + $this->getCurYearStartEndDate($year.'-01', ContractEnums::MONTH_SPLIT_FULL);
                    }
                }
                // 尾年年份
                $result_month = $result_month + $this->getCurYearStartEndDate($e_year_month, ContractEnums::MONTH_SPLIT_END);
                foreach ($result_month as $year => $date) {
                    $r_a_month = date('m',strtotime($date['start_date']));
                    $r_e_month = date('m',strtotime($date['end_date']));
                    $r_monthes = range((int)$r_a_month,(int)$r_e_month);
                    foreach ($r_monthes as $r_month) {
                        if ($year == $s_year && $r_month == (int)$s_month) {
                            $result = $result + $this->getCurMonthStartEndDate($start_date,ContractEnums::MONTH_SPLIT_START);
                            continue;
                        }
                        if ($year == $e_year && $r_month == (int)$e_month) {
                            $result = $result + $this->getCurMonthStartEndDate($end_date,ContractEnums::MONTH_SPLIT_END);
                            continue;
                        }
                        if ($r_month < 10) {
                            $r_month = '0'.$r_month;
                        }
                        $result = $result + $this->getCurMonthStartEndDate($year.'-'.$r_month.'-01', ContractEnums::MONTH_SPLIT_FULL);
                    }
                }
            }

            // 去掉生效日期是当月最后一天，以及结束日期是第一天
            foreach ($result as $k => $item) {
                if (!isset($item['start_date']) || empty($item['start_date']) ||
                    !isset($item['end_date']) || empty($item['end_date'])) {
                    unset($result[$k]);
                    continue;
                }
                $cur_date = $this->getCurMonthStartEndDate($item['start_date']);
                $cur_date = array_values($cur_date);
                $cur_date = $cur_date[0] ?? [];
                // 首天是否为当月最后一天,或者尾月是否为当月第一天
                if (isset($cur_date['start_date']) && isset($cur_date['end_date']) &&
                    ($start_date == $cur_date['end_date'] || $end_date == $cur_date['start_date'])){
                    unset($result[$k]);
                    continue;
                }
            }

            return $result;
        }
    }

    /**
     * 取当年第一个月和最后一个月
     * @param string $date
     * @param integer $type (0:满月,1:起始日期,2:结束日期)
     * @return array
     */
    public function getCurYearStartEndDate($date = '',$type = ContractEnums::MONTH_SPLIT_FULL)
    {
        if (empty($date)) {
            return [];
        }

        $cur_year = date('Y',strtotime($date));
        $cur_year_start = date('Y-01',strtotime($date));
        $cur_year_end = date('Y-12',strtotime($date));

        if (ContractEnums::MONTH_SPLIT_START == $type) { // 起始日期
            $cur_year_start = $date;
        } elseif (ContractEnums::MONTH_SPLIT_END == $type){ // 结束日期
            $cur_year_end = $date;
        }

        return [
            $cur_year => $this->formatDate($cur_year_start,$cur_year_end)
        ];
    }

    /**
     * 取当月第一天和最后一天
     * @param string $date
     * @param integer $type (0:满月,1:起始日期,2:结束日期)
     * @return array
     */
    public function getCurMonthStartEndDate($date = '',$type = ContractEnums::MONTH_SPLIT_FULL){
        if (empty($date)) {
            return [];
        }

        $cur_month = date('Y-m',strtotime($date));
        $cur_month_start = date('Y-m-01',strtotime($date));
        $cur_month_end = date('Y-m-d',strtotime(str_replace('$first',$cur_month_start,'$first +1 month -1 day')));

        if (ContractEnums::MONTH_SPLIT_START == $type) { // 起始日期
            $cur_month_start = $date;
        } elseif (ContractEnums::MONTH_SPLIT_END == $type){ // 结束日期
            $cur_month_end = $date;
        }

        return [
            $cur_month => $this->formatDate($cur_month_start,$cur_month_end)
        ];
    }

    /**
     * 按照数组格式输出日期
     * @param string $start_date
     * @param string $end_date
     * @param bool $is_same_month
     * @param bool $is_split
     * @return array
     */
    public function formatDate($start_date = '',$end_date = '',$is_same_month = false, $is_split = true){
        return [
            'start_date' => $start_date,
            'end_date' => $end_date,
            'is_same_month' => $is_same_month,
            'is_split' => $is_split
        ];
    }

    /**
     * 返回数组中指定多列
     * @param  array  $input       需要取出数组列的多维数组
     * @param  string $column_keys 要取出的列名，逗号分隔，如不传则返回所有列
     * @param  string $index_key   作为返回数组的索引的列
     * @return array
     * */
    function arrayColumns($input, $column_keys = '', $index_key = '')
    {
        $result = array();
        $keys = isset($column_keys) ? explode(',', $column_keys) : array();
        if ($input) {
            foreach ($input as $k => $v) {
                // 指定返回列
                if ($keys) {
                    $tmp = array();
                    foreach ($keys as $key) {
                        $tmp[$key] = $v[$key];
                    }
                } else {
                    $tmp = $v;
                }
                // 指定索引列
                if (isset($index_key)) {
                    $result[$v[$index_key]] = $tmp;
                } else {
                    $result[] = $tmp;
                }

            }
        }
        return $result;
    }

    /**
     * 按照数组格式输出日期
     * @param array $all_items
     * @param array $all_payment_month_split_date
     * @param bool $is_split
     * @throws BusinessException
     * @return array
     */
    public function handleItems($all_items,$all_payment_month_split_date,$is_split = true)
    {
        //  取网点名称映射数据
        $storeIds  = array_unique(array_filter(array_column($all_items, 'store_id')));
        $storeNameList = [];
        if ($storeIds) {
            $where = 'id in ({storeIds:array})';
            $storeObj  = SysStoreModel::find([
                'conditions' => $where,
                'bind'       => [
                    'storeIds' => array_values($storeIds)
                ],
                'columns'    => 'id,name'
            ]);
            $storeNameList = $storeObj->toArray();
            $storeNameList = array_column($storeNameList, 'name', 'id');
        }

        // 网点类型
        $storeTypeList = (new StoreService())->getDepartStoreType();
        $storeTypeList = array_column($storeTypeList,'name','type');
        $storeTypeList['-1'] = Enums::PAYMENT_HEADER_STORE_NAME;

        // 生成合同付款子项yield列表
        $all_chunk_items = array_chunk($all_items,1000);
        $export_item_list = [];
        $rent_due_date_valid = $this->getvalidRentDueDate();

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        foreach ($all_chunk_items as $key => &$all_item) {
            // 按照合同日期独立项合并导出数据项
            foreach ($all_item as $k => &$item) {
                // 生效日期为空跳过
                if (empty($item['contract_effect_date']) || empty($item['contract_end']) ||
                '0000-00-00' == $item['contract_effect_date'] || '0000-00-00' == $item['contract_end']) {
                    continue;
                }
                // 网点合同
                $item['contract_id'] = $item['contract_id'] ?: 0;

                // 网点名称
                $item['store_name'] = $storeNameList[$item['store_id']] ?? '';

                // 费用类型
                $item['cost_type'] = ContractEnums::$cost_type_items[$item['cost_type']] ?? '';

                // 获取对应的金额详情税率
                $contract_start_date_list = $is_split ? $this->splitGroupItems($item['cost_start_date']) : [];
                $contract_end_date_list = $is_split ? $this->splitGroupItems($item['cost_end_date']) : [];

                // 默认第一个详情
                $default_start_date_list = array_reverse(array_keys($contract_start_date_list));
                $default_detail_id = array_pop($default_start_date_list);

                // VAT税率：房租、区域服务费：取合同对应行的VAT税率
                $amount_no_tax_split_list = $is_split ? $this->splitGroupItems($item['amount_no_tax']) : [];
                $vat_split_list = $is_split ? $this->splitGroupItems($item['vat_rate']) : [];
                $wht_cate_split_list = $is_split ? $this->splitGroupItems($item['wht_category']) : [];
                $wht_rate_split_list = $is_split ? $this->splitGroupItems($item['wht_rate']) : [];

                // 银行名称、银行账户名称、银行账户号、联系人电话、联系人邮箱、SAP供应商号码【取合同的第一条付款信息】
                $bank_collections = (isset($item['bank_collection']) && !empty($item['bank_collection'])) ?
                    json_decode($item['bank_collection'], true) : [];
                if (isset($bank_collections[0])) {
                    $item['bank_name'] = isset($bank_collections[0]['bank_name']) ? $bank_collections[0]['bank_name'] :
                        ($bank_collections[0]['bank_name_1'] ?? '');
                    $item['bank_account_name'] = isset($bank_collections[0]['bank_account_name']) ? $bank_collections[0]['bank_account_name'] :
                        ($bank_collections[0]['bank_account_title_1'] ?? '');
                    $item['bank_account'] = isset($bank_collections[0]['bank_account']) ? $bank_collections[0]['bank_account'] :
                        ($bank_collections[0]['bank_book_no_1'] ?? '');
                    $item['contact_mobile'] = isset($bank_collections[0]['contact_mobile']) ? $bank_collections[0]['contact_mobile'] :
                        ($bank_collections[0]['contact_mobile_1'] ?? '');
                    $item['contact_email'] = isset($bank_collections[0]['contact_emial']) ? $bank_collections[0]['contact_emial'] :
                        ($bank_collections[0]['contact_emial_1'] ?? '');
                    $item['sap_supplier_no'] = isset($bank_collections[0]['sap_supplier_no']) ? $bank_collections[0]['sap_supplier_no'] :
                        ($bank_collections[0]['sap_supplier_no_1'] ?? '');
                } else {
                    $item['bank_name'] = '';
                    $item['bank_account_name'] = '';
                    $item['bank_account'] = '';
                    $item['contact_mobile'] = '';
                    $item['contact_email'] = '';
                    $item['sap_supplier_no'] = '';
                }

                // 房东税务号：取合同的房东税号TIN，没有就空着
                $item['contract_tax_no'] = (isset($item['contract_tax_no']) && !empty($item['contract_tax_no'])) ? $item['contract_tax_no'] : '';

                // 合同生效日期-合同结束日期
                $item['contract_effect_date'] = (isset($item['contract_effect_date']) && !empty($item['contract_effect_date'])) ?
                    $item['contract_effect_date'] : '';
                $item['contract_end'] = (isset($item['contract_end']) && !empty($item['contract_end'])) ? $item['contract_end'] : '';

                // 网点类型
                $item['store_cate'] = $storeTypeList[$item['store_cate']] ?? '';

                // 合同归档状态：取合同的归档状态
                $item['archive_status_text'] = ContractEnums::$contract_archive_status[$item['archive_status']] ?? '';

                // 拆分费用开始和结束日期
                $tmp_month_split_date = $this->splitContractMonth($item['contract_effect_date'],$item['contract_end'],$is_split);
                foreach ($tmp_month_split_date as $k => $split_item) {
                    if ($split_item['start_date'] > $rent_due_date_valid[2].'-01' ||
                        $split_item['end_date'] < $rent_due_date_valid[0].'-01') {
                        unset($tmp_month_split_date[$k]);
                    }
                }
                $cur_split_date = (isset($item['rkey']) && isset($all_payment_month_split_date[$item['rkey']])) ? array_values($all_payment_month_split_date[$item['rkey']]) : [];
                foreach ($tmp_month_split_date as $j => &$date_item) {
                    // 对应的金额详情id
                    $amount_detail_id = $is_split ? $this->getAmountDetailId($date_item['start_date'],$contract_start_date_list,$contract_end_date_list) : 0;

                    // 合同金额详情费用开始或者结束日期为空
                    if (empty($amount_detail_id) && !empty($default_detail_id)) {
                        $amount_detail_id = $default_detail_id;
                    }

                    // 应付日期
                    $rent_due_date = $this->getPaymentLimitDay($item['rent_due_date'],$date_item['start_date'],
                        $date_item['end_date']);
                    // 应付年月
                    $rent_due_year_month = !empty($rent_due_date) ? date('Y-m',strtotime($rent_due_date)) : '';
                    if ($is_split && !in_array($rent_due_year_month,$rent_due_date_valid)) {
                        continue;
                    }

                    // 独立月期间付款状态情况
                    $pay_status_text = $this->getPaymentStatusList($cur_split_date,
                        $date_item['start_date'],$date_item['end_date'],$item['rkey'],$is_split);
                    // 只用取第一个拆分日期中的is_same_month
                    $is_same_month = (isset($cur_split_date[0]) && isset($cur_split_date[0]['is_same_month'])) ?? false;

                    // 费用开始结束日期
                    $cost_start_date = $date_item['start_date'] ?? '';
                    $cost_end_date = $date_item['end_date'] ?? '';

                    // 不含漱金额
                    $amount_no_tax = $is_split ? $this->getAmountNoTax($amount_no_tax_split_list[$amount_detail_id] ?? 0,$cost_start_date,$cost_end_date,$is_same_month) : $item['amount_no_tax'];
                    // 房租和区域服务费不含税金额为0需要过滤掉
                    if ($is_split && $amount_no_tax == 0) {
                        continue;
                    }

                    // vat税率
                    $item['vat_rate'] = isset($vat_split_list[$amount_detail_id]) && is_numeric($vat_split_list[$amount_detail_id]) ? $vat_split_list[$amount_detail_id] : 0;

                    // VAT税额：不含税金额 * VAT税率
                    $vat_amount = !empty($item['vat_rate']) ? round($amount_no_tax * round($item['vat_rate']/100,3),2) : 0;

                    // 含税金额：不含税金额 + VAT税额
                    $amount_with_tax = (is_numeric($amount_no_tax) && is_numeric($amount_no_tax)) ? round($amount_no_tax + $vat_amount,2) : 0;

                    // WHT金额：不含税金额 * WHT税率
                    // WHT类型：房租、区域服务费：取合同对应行的WHT类别；
                    $item['wht_category'] = isset($wht_cate_split_list[$amount_detail_id]) && isset($wht_cat_map[$wht_cate_split_list[$amount_detail_id]]) ?
                        $wht_cat_map[$wht_cate_split_list[$amount_detail_id]] : '/';

                    // WHT税率：房租、区域服务费取合同对应行的WHT税率
                    $item['wht_rate'] = !empty($wht_rate_split_list[$amount_detail_id]) && is_numeric($wht_rate_split_list[$amount_detail_id]) ? $wht_rate_split_list[$amount_detail_id] : 0;
                    $wht_amount = !empty($item['wht_rate']) ? round($amount_no_tax * round($item['wht_rate']/100,3),2) : 0;

                    // 实付金额：含税金额 - WHT金额
                    $actual_amount = round($amount_with_tax - $wht_amount,2);

                    // 按顺序合并导出数据项
                    $filterData = [];
                    $filterData[] = $item['contract_id'] ?: 0;
                    $filterData[] = $item['store_name'] ?: '';
                    $filterData[] = !empty($item['cost_type']) ? static::$t->_($item['cost_type']) : '';
                    $filterData[] = $rent_due_date ?: '';
                    $filterData[] = $cost_start_date ?: '';
                    $filterData[] = $cost_end_date ?: '';
                    $filterData[] = $amount_no_tax ?: 0;
                    $filterData[] = $item['vat_rate'] . '%';
                    $filterData[] = $vat_amount ?: 0;
                    $filterData[] = $amount_with_tax ?: 0;
                    $filterData[] = $item['wht_category'];
                    $filterData[] = $item['wht_rate'] . '%';
                    $filterData[] = $wht_amount ?: 0;
                    $filterData[] = $actual_amount ?: 0;
                    $filterData[] = $item['bank_name'] ?: '';
                    $filterData[] = $item['bank_account_name'] ?: '';
                    $filterData[] = $item['bank_account'] ?: '';
                    $filterData[] = $item['contact_mobile'] ?: '';
                    $filterData[] = $item['contact_email'] ?: '';
                    $filterData[] = $item['sap_supplier_no'] ?: '';
                    $filterData[] = $item['contract_tax_no'] ?: '';
                    $filterData[] = (!empty($item['contract_effect_date']) && !empty($item['contract_end'])) ?
                        $item['contract_effect_date'].' - '. $item['contract_end'] : '';
                    $filterData[] = $item['store_cate'] ?: '';

                    $filterData[] = $rent_due_year_month ?: '';
                    $filterData[] = static::$t->_($item['archive_status_text']);
                    $filterData[] = static::$t->_($pay_status_text);

                    $export_item_list[] = $filterData;
                }
            }
        }

        return $export_item_list;
    }

    /**
     * 按照yeild格式返回单个项
     * @param array $all_items
     * @param array $with_key
     * @return array
     */
    public function getYieldItemList($all_items = [],$with_key = false){
        foreach ($all_items as $k => $item) {
            if ($with_key) {
                yield [$k,$item];
            } else {
                yield $item;
            }
        }
    }

    /**
     * 按照合同拆分后的独立月继续拆分付款费用日期
     * @param array $cur_split_date
     * @param string $start_date
     * @param string $end_date
     * @return string
     */
    public function getPaymentStatusList($cur_split_date = [], $start_date = '', $end_date = '', $rkey = '', $is_split = true) {
        $pay_list = $result = [];

        // 房租和区域服务费
        if ($is_split) {
            $start_num = date('d',strtotime($start_date));
            $end_num = date('d',strtotime($end_date));
            $occupy_date = range((int)$start_num,(int)$end_num);

            foreach ($cur_split_date as $it) {
                if ($it['start_date'] > $end_date || $it['end_date'] < $start_date ||
                    empty($it['start_date']) || empty($rkey)) {
                    continue;
                }

                $cost_type = explode('_',$rkey);
                if ($it['cost_type'] == $cost_type[1] && $it['start_date'] <= $end_date && $it['end_date'] >= $start_date) {
                    $it['start_date'] = ($it['start_date'] <= $start_date) ? $start_date : $it['start_date'];
                    $it['end_date'] = ($it['end_date'] >= $end_date) ? $end_date : $it['end_date'];
                    $i_start_num = date('d',strtotime($it['start_date']));
                    $i_end_num = date('d',strtotime($it['end_date']));
                    $occupy_date = array_diff($occupy_date,range((int)$i_start_num,(int)$i_end_num));
                    $pay_list[$it['pay_status']][] = $it['apply_no'];
                }
            }
        } else {
            foreach ($cur_split_date as $it) {
                if ($it['start_date'] > $end_date || $it['end_date'] < $start_date ||
                    empty($it['start_date']) || empty($rkey)) {
                    continue;
                }

                $cost_type = explode('_',$rkey);
                if ($it['cost_type'] == $cost_type[1] && $it['start_date'] <= $end_date && $it['end_date'] >= $start_date) {
                    $pay_list[$it['pay_status']][] = $it['apply_no'];
                }
            }
        }

        // 统计最终付款状态
        $pay_key_list = array_values(array_keys($pay_list));
        $pay_list_count = count($pay_key_list);
        // 暂未支付
        if (empty($pay_key_list) || ($pay_list_count == 1 && Enums::LOAN_PAY_STATUS_NOTPAY == $pay_key_list[0])) {
            $result['status'] = [ContractEnums::PAYMENT_TYPE_NOT_PAY];
            $result['ids'] = [];
        // 全部已支付
        } elseif ($pay_list_count == 1 && Enums::LOAN_PAY_STATUS_PAY == $pay_key_list[0]) {
            if (empty($occupy_date)) {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PAY_DONE];
            } else {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PART_PAY_DONE];
            }
            $result['ids'] = [$pay_list[Enums::LOAN_PAY_STATUS_PAY]];
        // 全部待支付
        } elseif ($pay_list_count == 1 && Enums::LOAN_PAY_STATUS_PENDING == $pay_key_list[0]) {
            if (empty($occupy_date)) {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PENDING_PAY];
            } else {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PART_PENDING_PAY];
            }
            $result['ids'] = [$pay_list[Enums::LOAN_PAY_STATUS_PENDING]];
        // 部分已支付
        } elseif ($pay_list_count > 1) {
            if (in_array(Enums::LOAN_PAY_STATUS_PAY,$pay_key_list) &&
                in_array(Enums::LOAN_PAY_STATUS_PENDING,$pay_key_list)) {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PART_PAY_DONE,ContractEnums::PAYMENT_TYPE_PART_PENDING_PAY];
                $result['ids'] = [$pay_list[Enums::LOAN_PAY_STATUS_PAY], $pay_list[Enums::LOAN_PAY_STATUS_PENDING]];
            } elseif (in_array(Enums::LOAN_PAY_STATUS_PAY,$pay_key_list)) {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PART_PAY_DONE];
                $result['ids'] = [$pay_list[Enums::LOAN_PAY_STATUS_PAY]];
            } else {
                $result['status'] = [ContractEnums::PAYMENT_TYPE_PART_PENDING_PAY];
                $result['ids'] = [$pay_list[Enums::LOAN_PAY_STATUS_PENDING]];
            }
        }

        $status_str = '';
        $len_max = count($result['status']);
        foreach ($result['status'] as $k => $status) {
            $status_str .= static::$t->_(ContractEnums::$payment_status_item[$status]);
            if (!empty($result['ids'][$k])) {
                $status_str .= implode(',',array_unique($result['ids'][$k]));
            }
            if ($k < $len_max - 1) {
                $status_str .= ';';
            }
        }

       return $status_str;
    }

    /**
     * 按照拆分的费用日期取房租应付日
     * @param integer $rent_due_date
     * @param string $cost_start_date
     * @param string $cost_end_date
     * @return string
     */
    public function getPaymentLimitDay($rent_due_date,$cost_start_date = '',$cost_end_date = '') {
        if (empty($rent_due_date) || empty($cost_start_date) || empty($cost_end_date)) {
            return '';
        }

        if ($rent_due_date < 10) {
            $rent_due_date = '0'.$rent_due_date;
        }
        // 房租应付日整形转为日期格式
        $rent_due_date = date('Y-m',strtotime($cost_start_date)).'-'.$rent_due_date;
        if ($rent_due_date < $cost_start_date || $rent_due_date > $cost_end_date) {
            $rent_due_date = $cost_start_date;
        }

        return $rent_due_date;
    }

    /**
     * 根据费用起始和结束取不含税金额
     * @param float $amount_no_tax
     * @param integer $rent_due_date
     * @param string $cost_start_date
     * @param string $cost_end_date
     * @return integer
     */
    public function getAmountNoTax($amount_no_tax,$cost_start_date,$cost_end_date,$is_same_month = false){
        if (empty($amount_no_tax)) {
            return 0;
        }
        // 费用起止满一个月
        $month_days = $this->getMonthDays($cost_start_date,$cost_end_date);

        // 整月或者起止同一个月
        if ($is_same_month || (isset($month_days['total_days']) && $month_days['total_days'] == $month_days['sub_days'])) {
            $res_amount_no_tax = $amount_no_tax;
        } else {
            $res_amount_no_tax = (isset($month_days['total_days']) && isset($month_days['sub_days']) &&
                !empty($month_days['total_days'])) ? round(round($month_days['sub_days']/$month_days['total_days'],6) * $amount_no_tax,2) : 0;
        }

        return $res_amount_no_tax;
    }

    /**
     * 根据费用起始和结束取不含税金额
     * @param string $cost_start_date
     * @param string $cost_end_date
     * @return array
     */
    public function getMonthDays($cost_start_date,$cost_end_date){
        if (empty($cost_start_date) || empty($cost_end_date)) {
            return [];
        }
        // 当月天数
        $total_days = date('t',strtotime($cost_start_date));
        // 起始日期天数
        $start_days = date('d',strtotime($cost_start_date));
        $end_days = date('d',strtotime($cost_end_date));

        return [
            'total_days' => $total_days,
            'sub_days' => (int)$end_days - (int)$start_days + 1
        ];
    }

    /**
     * 导出其他费用类型
     *
     * @param array $login_user
     * @return mixed
     * @throws BusinessException
     */
    public function exportOtherFee($login_user = [])
    {
        ini_set('memory_limit','512M');

        // 当前用户是否是指定工号
        $access_staff_ids = $this->getExportAccessStaffIds();
        $store_cate = [];
        if (!in_array($login_user['id'], $access_staff_ids)) {
            $store_cate = $this->getStoreCateByUid($login_user['id']);
        }

        // 【其它服务】取合同及金额详情[待归档 且 付款方式为月付的合同编号、和待上传盖章合同
        // 且 付款方式为月付的合同编号、和已归档 且 付款方式为月付的合同编号
        $other_pending_items = $this->getPendingOtherDetail($store_cate);

        // 【其它服务】付款单号为待审核或已通过待支付、或已通过已支付的合同
        $payment_items = $this->getPaymentContractList();
        $contract_ids = array_unique(array_filter(array_column($payment_items,'contract_no')));

        $other_reject_items = [];
        if (!empty($contract_ids)) {
            // 【房租金额】已作废和已终止的仅导出月付 且 有过付款记录
            $other_reject_items = $this->getRejectOtherDetail($contract_ids,$store_cate);
        }
        $other_all_items = array_merge($other_pending_items,$other_reject_items);
        $other_contract_ids = !empty($other_all_items) ? array_unique(array_filter(array_column(
            $other_all_items,'contract_id'))) : [];

        // 分配key和cost_type
        $other_all_items = $this->addKeyAndCostType($other_all_items);

        // 取付款关联合同信息
        $other_contract_ids = array_chunk($other_contract_ids,ContractEnums::SQL_QUERY_PAGE_SIZE);
        $payment_relations = [];
        // 取服务费类型列表
        $other_types = $this->getOtherType(ContractEnums::EXPORT_TYPE_OTHER_SERVICE);
        foreach ($other_contract_ids  as $contract_id_items) {
            $the_payment_items = $this->getPaymentRelationList($contract_id_items,ContractEnums::COST_TYPE_DEPOSIT_AMOUNT);
            foreach ($the_payment_items as $pitem) {
                foreach ($other_types as $type) {
                    $pitem['rkey'] = $pitem['contract_no'].'_'.$type;
                    $payment_relations = array_merge($payment_relations, [$pitem]);
                }
            }
        }

        // 付款关联信息按每月拆分付款关联合同的费用日期
        $all_payment_month_split_date = $payment_contract_transfer_date = [];
        foreach ($payment_relations as $payment_item) {
            if (!isset($payment_item['rkey'])) {
                continue;
            }
            $payment_contract_transfer_date[$payment_item['rkey']][] = [
                'start_date' => $payment_item['cost_start_date'],
                'end_date' => $payment_item['cost_end_date'],
                'apply_no' => $payment_item['apply_no'],
                'pay_status' => $payment_item['pay_status'],
                'cost_type' => $payment_item['cost_type']
            ];
        }

        // 按月拆分付款关联的费用开始和结束日期
        if (!empty($payment_contract_transfer_date)) {
            $all_payment_month_split_date = $this->transferToPaymentMonthSection($payment_contract_transfer_date);
        }

        // 整理结果集数据
        $all_handle_items = $this->handleItems($other_all_items,$all_payment_month_split_date,false);
        unset($other_all_items,$all_payment_month_split_date);

        // 上传文件并导出
        $filename = "payment_others_" . date("YmdHis") . '.xlsx';
        return $this->returnRentingPaymentData($filename,$all_handle_items);
    }

    /**
     * 按顺序组装成excel导出的数据并上传文件并返回
     * @param array $other_all_items
     * @return array
     */
    public function addKeyAndCostType($other_all_items = []){
        $export_all_items = [];
        // 取服务费类型列表
        $other_types = $this->getOtherType(ContractEnums::EXPORT_TYPE_OTHER_SERVICE);

        // 生成合同付款子项yield列表
        $yield_all_items = $this->getYieldItemList($other_all_items);

        foreach ($yield_all_items as $item) {
            // 默认值
            $item['vat_rate'] = 0;
            $item['wht_category'] = 0;
            $item['wht_rate'] = 0;

            foreach ($other_types as $type) {
                $tmp_items = $item;
                // 费用类型
                $tmp_items['cost_type'] = $type;
                // 添加rkey
                $tmp_items['rkey'] = $tmp_items['contract_id'].'_'.$type;
                // 不含税金额
                $tmp_items['amount_no_tax'] = $this->getOtherAmountNoTax($item,$type);

                $export_all_items[] = $tmp_items;
            }
        }

        return $export_all_items;
    }

    /**
     * 获取类型列表
     * @param int $type
     * @return mixed
     */
    public function getOtherType($type = ContractEnums::EXPORT_TYPE_ALL){
        $cost_types = ContractEnums::$cost_type_items;
        if ($type = ContractEnums::EXPORT_TYPE_OTHER_SERVICE) {
            // 只保留其它类型
            unset($cost_types[ContractEnums::COST_TYPE_RENTING],$cost_types[ContractEnums::COST_TYPE_AREA]);
            $cost_types = array_keys($cost_types);
        } elseif ($type = ContractEnums::EXPORT_TYPE_RENTING_AREA) {
            $cost_types = [ContractEnums::COST_TYPE_RENTING,ContractEnums::COST_TYPE_AREA];
        }

        return $cost_types;
    }

    /**
     * 按顺序组装成excel导出的数据并上传文件并返回
     * @param array $item
     * @param int $type
     * @return mixed
     */
    public function getOtherAmountNoTax($item, $type){
        if ($type == ContractEnums::COST_TYPE_DEPOSIT_AMOUNT) {
            $amount = $item['deposit_amount'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_CONTRACT_DEPOSIT) {
            $amount = $item['contract_deposit'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_AMOUNT_MONTHLY) {
            $amount = $item['total_amount_monthly'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_TOTAL_AMOUNT) {
            $amount = $item['total_amount'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_SIGNBOARD_TAX) {
            $amount = $item['signboard_tax'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_LAND_TAX) {
            $amount = $item['land_tax_amount'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_INSURANCE_AMOUNT) {
            $amount = $item['fire_insurance_amount'] ?? 0;
        } elseif ($type == ContractEnums::COST_TYPE_ANTIMOTH_AMOUNT) {
            $amount = $item['antimoth_amount'] ?? 0;
        } else {
            $amount = 0;
        }

        return $amount;
    }

    /**
     * 按顺序组装成excel导出的数据并上传文件并返回
     * @param string $filename
     * @param array $exportData
     * @return mixed
     */
    public function returnRentingPaymentData($filename,&$export_data)
    {
        $real_message = '';
        $result = [
            'code' => ErrCode::$SUCCESS,
            'message' => 'ok',
            'data' => ''
        ];

        $header_map = [
            static::$t->_('payment_store_renting_contract_no'),       // 相关合同
            static::$t->_('payment_store_renting_store_name'),        // 网点名称
            static::$t->_('payment_store_renting_cost_type'),         // 费用类型
            static::$t->_('payment_store_renting_due_date'),          //应付日期
            static::$t->_('payment_store_renting_cost_start_date'), // 费用开始日期
            static::$t->_('payment_store_renting_cost_end_date'),  // 费用结束日期
            static::$t->_('csr_field_no_tax'),  // 不含税金额
            static::$t->_('payment_store_renting_vat_rate'), //vat 税率
            static::$t->_('payment_store_renting_vat_amount'),//vat税额
            static::$t->_('payment_store_renting_amount_has_tax'),//含税金额
            static::$t->_('payment_store_renting_wht_category'),  // WHT类别
            static::$t->_('payment_store_renting_wht_category_tax'),  // WHT税率
            static::$t->_('payment_store_renting_wht_amount'),  // WHT金额
            static::$t->_('payment_store_renting_actually_amount'),  // 实付金额
            static::$t->_('payment_store_renting_bank_name'),  // 银行名称
            static::$t->_('payment_store_renting_bank_acct_name'),  // 银行账户名称
            static::$t->_('payment_store_renting_bank_acct_no'),  // 银行账户号
            static::$t->_('payment_store_renting_contact_phone'),  // 联系人电话
            static::$t->_('payment_store_renting_contact_email'),  // 联系人邮箱
            static::$t->_('import_field_payment_sap_supplier_no'),  // sap供应商编号
            static::$t->_('payment_store_renting_landlord_tax_no'),  // 房东税务号
            static::$t->_('contract_enums_contract_effect_end_date'),  // 合同生效日期-合同结束日期
            static::$t->_('contract_enums_store_cate'), // 网点类型
            static::$t->_('contract_enums_rent_due_date'), // 应付年月
            static::$t->_('contract_enums_archive_status'), // 合同归档状态
            static::$t->_('contract_enums_pay_status'), // 付款情况
        ];

        $header = array_values($header_map);

        try {
            $config = [
                'path' => sys_get_temp_dir()
            ];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 此处会自动创建一个工作表
            $export_data_chunk = array_chunk($export_data,50000);
            $fileObject = $excel->fileName($filename,'sheet_0');
            foreach ($export_data_chunk as $k => $data_chunk) {
                if ($k > 0) {
                    $fileObject = $fileObject->addSheet('sheet_'.$k);
                }
                $fileObject = $fileObject->header($header)->data($data_chunk);
            }
            $filePath = $fileObject->output();
            $file = OssHelper::uploadFile($filePath);
            $result['data'] = $file['object_url'];
        }  catch (\Exception $e) {
            $result['code'] = $e->getCode();
            $result['message'] = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if ($real_message) {
            $this->logger->warning('文件上传错误，real_message => '.$real_message);
        }

        return $result;
    }

    /**
     * 拆分分组字符串为数组格式
     * @param string $vals
     * @return array
     */
    public function splitGroupItems($vals = ''){
        if (empty($vals)){
            return [];
        }

        $arr_vals = explode(',',$vals);
        $result = [];
        foreach ($arr_vals as $val) {
            $sval = explode('_',$val);
            $sval[0] = $sval[0] ?? 0;
            $sval[1] = $sval[1] ?? 0;
            $result[$sval[0]] = $sval[1];
        }

        return $result;
    }

    /**
     * 通过合同金额详情费用开始时间和结束时间取对应的id
     * @param string $start_date
     * @param array $contract_start_date_list
     * @param array $contract_end_date_list
     * @return mixed
     */
    public function getAmountDetailId($start_date,$contract_start_date_list = [],$contract_end_date_list = []){
        foreach ($contract_start_date_list as $id => $contract_start_date) {
            $contract_start_date = date('Y-m-d',strtotime($contract_start_date));
            if (isset($contract_end_date_list[$id]) && $start_date >= $contract_start_date
                && $start_date <= date('Y-m-d',strtotime($contract_end_date_list[$id]))) {
                return $id;
            }
        }

        return 0;
    }

}
