<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractStoreRentingRemindModel;


class ContractRemindListService extends BaseService
{
    public static $not_must_params = [
        'store_id',
        'task_type',
        'task_status',
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',//每页条数
        'pageNum' => 'Required|IntGt:0',//页码
        'store_id' => 'StrLenGe:1',//网点id
        'task_type' => 'IntIn:' . ContractEnums::VALIDATE_TASK_TYPE, //提醒类型
        'task_status' => 'IntIn:' . ContractEnums::VALIDATE_TASK_STATUS, //任务状态
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ContractRemindListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     * @return array
     * @date 2022/12/6
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                'task_type' => ContractEnums::$task_type,
                'task_status' => ContractEnums::$task_status,
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $data[$key][] = [
                        'value' => $k,
                        'label' => static::$t->_($v)
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取网点合同等进度管理-枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取列表
     * @param $condition
     * @return array
     */
    public function getList($condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //列表查询总数
            $count = $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('id, store_id, store_name, store_created_at, contract_begin, contract_end, task_type, task_status, created_at, finished_at');
                $builder->from(ContractStoreRentingRemindModel::class);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('task_status asc, created_at asc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-contract-remind-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 根据条件获取总数量
     * @param array $condition
     * @return int
     * @date 2022/12/6
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from([ContractStoreRentingRemindModel::class]);
        $builder->columns("count('id') AS count");
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * @param $builder
     * @param $condition
     * @return mixed
     */
    private function getCondition($builder, $condition)
    {
        $store_id = $condition['store_id'] ?? '';
        $task_type = $condition['task_type'] ?? 0;
        $task_status = $condition['task_status'] ?? 0;
        //网点id
        if (!empty($store_id)) {
            $builder->andWhere('store_id = :store_id:', ['store_id' => $store_id]);
        }
        //任务类型
        if (!empty($task_type)) {
            $builder->andWhere('task_type = :task_type:', ['task_type' => $task_type]);
        }
        //任务状态
        if (!empty($task_status)) {
            $builder->andWhere('task_status = :task_status:', ['task_status' => $task_status]);
        }
        return $builder;
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items)
    {
        foreach ($items as &$item) {
            $item['task_type_text'] = static::$t[ContractEnums::$task_type[$item['task_type']]];
            $item['task_status_text'] = static::$t[ContractEnums::$task_status[$item['task_status']]];
            $item['finished_at'] = $item['finished_at'] ?? '';
            $item['store_created_at'] = $item['store_created_at'] ?? '';
            $item['contract_begin'] = $item['contract_begin'] ?? '';
            $item['contract_end'] = $item['contract_end'] ?? '';
            $item['created_at'] = $item['created_at'] ?? '';
        }
        return $items;
    }
}
