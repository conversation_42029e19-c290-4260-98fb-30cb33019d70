<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Repository\oa\VendorRepository;

class ArchiveDetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1'                                  //合同ID
    ];

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    public function getDetail($archive_id,$cno='')
    {
        //增加合同编号查询,优先使用id
        $find_field = 'id';
        $find_value = $archive_id;
        if (empty($archive_id) && !empty($cno)){
            $find_field = 'cno';
            $find_value = $cno;
        }
        $archive = ContractArchive::findFirst([
            'conditions' => $find_field.'= ?1',
            'bind' => [1 => $find_value]
        ]);
        $archiveArr = empty($archive) ? [] : $archive->toArray();
        if (!empty($archiveArr)) {
            $contract = Contract::findFirst([
                'conditions' => 'cno = ?1',
                'bind' => [1 => $archiveArr['cno']]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $find_field.'-'.$find_value]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $archiveArr['contract_desc'] = $contract->contract_desc;
            $archiveArr['company_code'] = $contract->company_code;
            $archiveArr['is_group_contract'] = $contract->is_group_contract;
            $archiveArr['company_relation_code'] = $contract->company_relation_code;
            $archiveArr['sell_cno'] = $contract->sell_cno;
            $archiveArr['in_scope'] = $contract->in_scope;
            $archiveArr['pay_method'] = $contract->pay_method;
            $archiveArr['is_quotation'] = $contract->is_quotation;
            //v10239需求增加了归档信息查看(显示了合同归档时的附件),之前合同归档时的附件是在合同正文处显示的
            //合同正文显示的附件改成合同申请时的附件
            $archiveArr['apply_contract_file'] = [];
            $file = $contract->getFile(["columns"=>"id,bucket_name,object_key,file_name"]);
            if(!empty($file)){
                $archiveArr['apply_contract_file'] = $file->toArray();
            }
            $archiveArr['effective_date'] = $contract->effective_date??'';
            $archiveArr['expiry_date'] = $contract->expiry_date??'';
            //显示关联的报价单
            $contract_quotation = $contract->getContractQuotation(['columns' => 'quotation_no, configure_consumer_id'])->toArray();
            $archiveArr['contract_quotation'] = $contract_quotation;
            //是否为供应商
            $archiveArr['is_vendor'] = $contract->is_vendor;
            $archiveArr['vendor_id'] = $contract->vendor_id;
            $archiveArr['customer_company_name'] = $contract->customer_company_name;

            // 合同的其他附件
            $archiveArr['attachment'] = $contract->getAttach(['columns' => 'id, bucket_name, object_key, file_name']);
        }
        $archiveArr = $this->handleData($archiveArr);

        if(!empty($archive)){
            $archiveArr['contract_file'] = [];
            $file = $archive->getFile(["columns"=>"id, bucket_name,object_key,file_name"]);
            if(!empty($file)){
                $archiveArr['contract_file'] = $file->toArray();
            }
        }
        return $archiveArr;
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $status = ContractEnums::$contract_archive_status[$data['status']] ?? '';
        $data['status_title'] = static::$t->_($status);

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

        // 直属分类信息
        $contract_category_info = $contract_category_map[$data['template_id']] ?? [];
        $data['template_title'] = $contract_category_info['label'] ?? '';

        // 若直属分类是二级分类, 则需拼接一级分类
        if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
            $data['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $data['template_title'];
        }

        $data['real_amount'] = bcdiv($data['amount'], 1000, 2);
        $is_master = Enums::$contract_is_master[$data['is_master']] ?? '';
        $data['is_master_title'] = static::$t->_($is_master);
        $payment_currency = GlobalEnums::$currency_item[$data['payment_currency']] ?? '';
        $data['payment_currency_title'] = static::$t->_($payment_currency);
        $data['lang'] = static::$t->_($data['lang']);
        // v17222 供应商回显对应的最新名称即可
        $data['vendor_name'] = '';
        if (!empty($data['vendor_id'])) {
            $vendor_info = VendorRepository::getInstance()->getVendorByVendorId($data['vendor_id']);
            $data['vendor_name'] = $vendor_info['vendor_name'] ?? '';
        }

        return $data;
    }
}
