<?php
/**
 *CsrfTokenServer.php
 * Created by: Lqz.
 * Description:
 * User: Administrator
 * CreateTime: 2020/8/12 0012 11:31
 */

namespace App\Modules\Contract\Services;

class CsrfTokenServer extends BaseService
{
    private $csrf_cache_key_timeout;
    private $csrf_cache_key_prefix;

    public function __construct(string $csrfCacheKeyPrefix = 'csrf_token', int $timeout = 3600)
    {
        $this->csrf_cache_key_timeout = $timeout;
        $this->csrf_cache_key_prefix  = $csrfCacheKeyPrefix;
    }

    /**
     * 获取表单唯一标识
     * Created by: Lqz.
     * @return string
     * CreateTime: 2020/8/12 0012 11:43
     */
    public function getCsrfToken()
    {
        $redis        = $this->getDI()->get('redis');
        $csrfToken    = uniqid();
        $csrfCacheKey = $this->csrf_cache_key_prefix . $csrfToken;
        $res = $redis->set($csrfCacheKey, 0, $this->csrf_cache_key_timeout);
        if(!$res){
            throw new \LogicException('Set CsrfToken fialed!');
        }
        return $csrfToken;
    }

    /**
     * 检测数据是否重复提交
     * Created by: Lqz.
     * @param string $csrfToken
     * @return bool|string[]
     * CreateTime: 2020/8/12 0012 11:43
     */
    public function checkCsrfToken(string $csrfToken)
    {
        $redis        = $this->getDI()->get('redis');
        $csrfCacheKey = $this->csrf_cache_key_prefix . $csrfToken;
        if (!$redis->exists($csrfCacheKey)) {
            return [
                'msg' => self::$t->_('csrf_token_tip_key')
            ];
        }
        $value = $redis->incr($csrfCacheKey);
        if ($value !== 1) {
            return [
                'msg' => self::$t->_('csrf_data_is_submit')
            ];
        }
        return true;
    }

}