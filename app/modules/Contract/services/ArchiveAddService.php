<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;

class ArchiveAddService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * @param $contract_id
     * @param $user
     * @return bool|mixed
     */
    public function one($contract_id, $user)
    {
        $contract = Contract::findFirst([
            'conditions' => 'id = ?1 and status = ?2',
            'bind' => [1 => $contract_id, 2 => Enums::CONTRACT_STATUS_APPROVAL]
        ]);
        if (empty($contract)) {
            return true;
        }
        $archive = ContractArchive::findFirst([
            'conditions' => 'cno = ?1',
            'columns' => 'id',
            'bind' => [1 => $contract->cno]
        ]);
        if (!empty($archive)) {
            return true;
        }
        $contract = $contract->toArray();
        $contract = $this->handleData($contract, $user);
        return (new ContractArchive())->i_create($contract);
    }

    /**
     * @param $contract
     * @param $user
     * @return array
     */
    private function handleData($contract, $user)
    {
        if (empty($contract) || !is_array($contract)) {
            return [];
        }
        $new_contract['cno'] = $contract['cno'];
        $new_contract['quotation_no'] = $contract['quotation_no'];
        $new_contract['cname'] = $contract['cname'];
        $new_contract['status'] = 1;
        $new_contract['template_id'] = $contract['template_id'];
        $new_contract['is_master'] = $contract['is_master'];
        $new_contract['sub_cno'] = $contract['sub_cno'];
        $new_contract['amount'] = $contract['amount'];
        $new_contract['payment_currency'] = $contract['payment_currency'];
        $new_contract['create_id'] = $contract['create_id'] ?? 0;
        $new_contract['create_department_id'] = $contract['create_department_id'] ?? 0;
        $new_contract['create_name'] = $contract['create_name'] ?? '';
        $new_contract['created_at'] = date('Y-m-d H:i:s');
        $new_contract['approved_at'] = $contract['approved_at'];
        $new_contract['lang'] = $contract['lang'];
        return $new_contract;
    }
}
