<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Organization\Services\DepartmentService;
use App\Repository\DepartmentRepository;
use App\Repository\oa\ContractCategoryRepository;

class ArchiveListService extends BaseService
{
    const AMOUNT_THB_ONE_HUNDRED_THOUSAND = 100000000;          //十万
    const AMOUNT_THB_FIVE_HUNDRED_THOUSAND = 500000000;         //五十万
    const AMOUNT_THB_ONE_MILLION = 1000000000;                  //一百万
    const AMOUNT_THB_FIVE_MILLION = 5000000000;                 //五百万

    const AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED = 3500000;                    //三千五
    const AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED = 16600000;                  //一万六千六
    const AMOUNT_USD_THIRTY_THREE_THOUSAND = 33000000;                         //三万三千
    const AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND = 166000000;           //十六万六千

    const AMOUNT_CNY_TWENTY_FIVE_THOUSAND = 25000000;                                      //两万五千
    const AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND = 117000000;                       //十一万七千
    const AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND = *********;                     //二十三万四千
    const AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND = 1168000000;        //一百一十六万八千

    public static $not_must_params = [
        'cno',
        'quotation_no',
        'cname',
        'template_id',
        'filing_at_start',
        'filing_at_end',
        'amount_type',
        'approved_at_start',
        'approved_at_end',
        'status',
        'customer_company_name',
        'company_code',
        'create_department_id'
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',                  //每页条数
        'pageNum' => 'Required|IntGt:0',                   //页码
        'cname' => 'StrLenGeLe:1,50',                      //合同名称
        'filing_at_start' => 'DateTime',                  //合同申请开始时间
        'filing_at_end' => 'DateTime',                    //合同申请结束时间
        'amount_type' => 'IntGeLe:1,6',                    //合同金额
        'approved_at_start' => 'DateTime',                 //合同生效开始时间
        'approved_at_end' => 'DateTime',                   //合同生效结束时间
        'status' => 'IntIn:1,2,3,4',                       //合同状态
        'customer_company_name' => 'StrLenGeLe:0,50', //客户公司名称
        'company_code' => 'IntGt:0', //合同所属公司
        'create_department_id' => 'IntGt:0', //负责人部门id
    ];

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * @param $condition
     * @param array $user
     * @return array
     */
    public function getList($condition, $user = [])
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder->leftJoin(Contract::class, 'ca.cno = c.cno', 'c');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = ms.staff_info_id', 'ms');
        $builder = $this->getCondition($builder, $condition, $user);
        $count = (int) $builder->columns('COUNT(ca.id) AS total')->getQuery()->getSingleResult()->total;
        $items = [];
        if ($count) {
            $columns = [
                'ca.id',
                'ca.filing_at',
                'ca.cno',
                'ca.cname',
                'ca.template_id',
                'ca.approved_at',
                'ca.amount',
                'ca.payment_currency',
                'ca.status',
                'ca.create_name',
                'ca.filing_name',
                'ca.holder_name',
                'ca.contract_file',
                'c.id as contract_id',
                'c.is_group_contract',
                'c.customer_company_name',
                'c.company_code',
                'c.effective_date',
                'c.expiry_date',
                'c.sell_cno',
                'c.contract_storage_type',
                'c.sign_type',
                'ms.node_department_id'
            ];
            $builder->columns($columns)->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleItems($items);
        }

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param array $user
     * @return mixed
     */
    private function getCondition($builder, $condition, $user = [])
    {
        $cno = $condition['cno'] ?? '';
        $quotation_no = $condition['quotation_no'] ?? '';
        $c_name = $condition['cname'] ?? '';
        $template_id = $condition['template_id'] ?? 0;
        $filing_at_start = $condition['filing_at_start'] ?? '';
        $filing_at_end = $condition['filing_at_end'] ?? '';
        $amount_type = $condition['amount_type'] ?? 0;
        $approved_at_start = $condition['approved_at_start'] ?? '';
        $approved_at_end = $condition['approved_at_end'] ?? '';
        $status = $condition['status'] ?? 0;
        //是否集团公司间合同
        $is_group_contract = isset($condition['is_group_contract']) && key_exists($condition['is_group_contract'], ContractEnums::$is_group_contract_items) ? $condition['is_group_contract'] : '';
        //客户公司名称
        $customer_company_name = $condition['customer_company_name'] ?? '';
        //合同所属公司
        $company_code = $condition['company_code'] ?? 0;
        //合同负责人所在部门
        $create_department_id = $condition['create_department_id'] ?? 0;

        $effective_date_start = $condition['effective_date_start'] ?? '';
        $effective_date_end   = $condition['effective_date_end'] ?? '';
        $expiry_date_start    = $condition['expiry_date_start'] ?? '';
        $expiry_date_end      = $condition['expiry_date_end'] ?? '';
        $sign_type            = $condition['sign_type'] ?? '';

        $builder->andWhere('ca.contract_type = :contract_type:', ['contract_type' => ContractEnums::CONTRACT_TYPE_OTHER]);

        if (!empty($cno)) {
            $builder->andWhere('ca.cno = :cno:', ['cno' => $cno]);
        }
        if (!empty($quotation_no)) {
            $builder->leftJoin(ContractQuotationModel::class, 'cq.cno = ca.cno', 'cq');
            $builder->andWhere('cq.quotation_no = :quotation_no:', ['quotation_no' => $quotation_no]);
            $builder->groupBy('ca.id');
        }
        if (!empty($template_id)) {
            $builder->andWhere('ca.template_id = :template_id:', ['template_id' => $template_id]);
        }
        if (!empty($filing_at_start)) {
            $builder->andWhere('ca.filing_at >= :filing_at_start:', ['filing_at_start' => $filing_at_start]);
        }
        if (!empty($filing_at_end)) {
            $builder->andWhere('ca.filing_at < :filing_at_end:', ['filing_at_end' => $filing_at_end]);
        }
        if (!empty($approved_at_start)) {
            $builder->andWhere('ca.approved_at >= :approved_at_start:', ['approved_at_start' => $approved_at_start]);
        }
        if (!empty($approved_at_end)) {
            $builder->andWhere('ca.approved_at < :approved_at_end:', ['approved_at_end' => $approved_at_end]);
        }
        if (!empty($status)) {
            $builder->andWhere('ca.status = :status:', ['status' => $status]);
        }
        if (!empty($amount_type)) {
            switch ($amount_type) {
                case 1:
                    $builder->andWhere('(ca.payment_currency = :1: and ca.amount < :2:) OR (ca.payment_currency = :3: and ca.amount < :4:) OR (ca.payment_currency = :5: and ca.amount < :6:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_HUNDRED_THOUSAND,
                            '3' => GlobalEnums::CURRENCY_USD,
                            '4' => self::AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED,
                            '5' => GlobalEnums::CURRENCY_CNY,
                            '6' => self::AMOUNT_CNY_TWENTY_FIVE_THOUSAND
                        ]);
                    break;
                case 2:
                    $builder->andWhere('(ca.payment_currency = :1: and ca.amount >= :2: and ca.amount < :3:) OR (ca.payment_currency = :4: and ca.amount >= :5: and ca.amount < :6:) OR (ca.payment_currency = :7: and ca.amount >= :8: and ca.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_HUNDRED_THOUSAND,
                            '3' => self::AMOUNT_THB_FIVE_HUNDRED_THOUSAND,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED,
                            '6' => self::AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_TWENTY_FIVE_THOUSAND,
                            '9' => self::AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND
                        ]);
                    break;
                case 3:
                    $builder->andWhere('(ca.payment_currency = :1: and ca.amount >= :2: and ca.amount < :3:) OR (ca.payment_currency = :4: and ca.amount >= :5: and ca.amount < :6:) OR (ca.payment_currency = :7: and ca.amount >= :8: and ca.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_FIVE_HUNDRED_THOUSAND,
                            '3' => self::AMOUNT_THB_ONE_MILLION,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED,
                            '6' => self::AMOUNT_USD_THIRTY_THREE_THOUSAND,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND,
                            '9' => self::AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND
                        ]);
                    break;
                case 4:
                    $builder->andWhere('(ca.payment_currency = :1: and ca.amount >= :2: and ca.amount < :3:) OR (ca.payment_currency = :4: and ca.amount >= :5: and ca.amount < :6:) OR (ca.payment_currency = :7: and ca.amount >= :8: and ca.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_MILLION,
                            '3' => self::AMOUNT_THB_FIVE_MILLION,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_THIRTY_THREE_THOUSAND,
                            '6' => self::AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND,
                            '9' => self::AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND
                        ]);
                    break;
                case 5:
                    $builder->andWhere('(ca.payment_currency = :1: and ca.amount >= :2:) OR (ca.payment_currency = :3: and ca.amount >= :4:) OR (ca.payment_currency = :5: and ca.amount >= :6:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_FIVE_MILLION,
                            '3' => GlobalEnums::CURRENCY_USD,
                            '4' => self::AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND,
                            '5' => GlobalEnums::CURRENCY_CNY,
                            '6' => self::AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND
                        ]);
                    break;
                default:
                    break;
            }
        }
        if (!empty($c_name)) {
            $builder->andWhere('ca.cname LIKE :cname:', ['cname' => $c_name . '%']);
        }

        // 对接通用数据权限
        // 业务表参数
        $table_params = [
            'table_alias_name' => 'c',
            'create_id_field' => 'create_id',
            'create_node_department_id_filed' => 'create_department_id',
        ];
        $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_OTHER_CONTRACT, $table_params);
        if ($is_group_contract !== '') {
            $builder->andWhere('c.is_group_contract = :is_group_contract:', ['is_group_contract' => $is_group_contract]);
        }
        if (!empty($customer_company_name)) {
            $builder->andWhere('c.customer_company_name like :customer_company_name:', ['customer_company_name' => '%' . $customer_company_name . '%']);
        }
        if (!empty($company_code)) {
            $builder->andWhere('c.company_code = :company_code:', ['company_code' => $company_code]);
        }

        if (!empty($create_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_service = new DepartmentService();
            $department_ids = $department_service->getChildrenListByDepartmentIdV2($create_department_id, true);
            array_push($department_ids, $create_department_id);
            $builder->inWhere('ms.node_department_id', $department_ids);
        }

        if (!empty($effective_date_start)) {
            $builder->andWhere('c.effective_date >= :effective_date_start:', ['effective_date_start' => $effective_date_start]);

        }

        if (!empty($effective_date_end)) {
            $builder->andWhere('c.effective_date <= :effective_date_end:', ['effective_date_end' => $effective_date_end]);
        }

        if (!empty($expiry_date_start)) {
            $builder->andWhere('c.expiry_date <= :expiry_date_start:', ['expiry_date_start' => $effective_date_start]);
        }

        if (!empty($expiry_date_end)) {
            $builder->orWhere('c.expiry_date <= :expiry_date_end: and expiry_date is null', ['expiry_date_end' => $effective_date_end]);
        }

        if (!empty($sign_type)) {
            $builder->andWhere('c.sign_type = :sign_type:', ['sign_type' => $sign_type]);
        }

        return $builder;
    }

    private function handleItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

        //查询关联报价单没配置客户id的信息
        $contract_quotation = [];
        $contract_ids = array_column($items, 'contract_id');
        $contract_ids = array_values($contract_ids);
        if (!empty($contract_ids)) {
            $contract_quotation = ContractQuotationModel::find([
                'conditions' => 'contract_id IN ({contract_ids:array}) and configure_consumer_id = :empty_string:',
                'bind' => ['contract_ids' => $contract_ids, 'empty_string' => ''],
            ])->toArray();
            $contract_quotation = array_column($contract_quotation, null, 'contract_id');
        }
        //合同所属公司
        $contract_company = EnumsService::getInstance()->getContractCompanyItem();
        //负责人部门id
        $node_department_ids = array_column($items, 'node_department_id');
        $node_department_ids = array_values(array_unique($node_department_ids));
        $department_data = (new DepartmentRepository())->getDepartmentByIds($node_department_ids, 2);
        $contract_storage_type = ContractEnums::$contract_storage_type;
        $sign_type = ContractEnums::$contract_sign_type;
        foreach ($items as &$item) {
            $status = ContractEnums::$contract_archive_status[$item['status']] ?? '';
            $item['status_title'] = static::$t->_($status);

            // 直属分类信息
            $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
            $item['template_title'] = $contract_category_info['label'] ?? '';

            // 若直属分类是二级分类, 则需拼接一级分类
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $item['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $item['template_title'];
            }

            $payment_currency = GlobalEnums::$currency_item[$item['payment_currency']] ?? '';
            $item['payment_currency_title'] = static::$t->_($payment_currency);
            $item['real_amount'] = bcdiv($item['amount'], 1000, 2);
            // 是否可下载
            $item['can_download'] = 1; // 数据查询默认可下载

            // 获取合同ID
            $item['cid'] = $item['contract_id'];

            //是否可配置账户
            $item['can_configure_consumer'] = Enums\CrmQuotationEnums::CAN_CONFIGURE_CONSUMER_NO;
            if (isset($contract_quotation[$item['cid']]) && get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
                $item['can_configure_consumer'] = Enums\CrmQuotationEnums::CAN_CONFIGURE_CONSUMER_YES;
            }

            //是否为集团公司间合同
            $item['is_group_contract_text'] = isset(ContractEnums::$is_group_contract_items[$item['is_group_contract']]) ? static::$t[ContractEnums::$is_group_contract_items[$item['is_group_contract']]] : '';
            //合同所属公司
            $item['company_code_text'] = $contract_company[$item['company_code']] ?? '';
            //合同负责人所在部门
            $item['create_department_text'] = $department_data[$item['node_department_id']]['name'] ?? '';
            $item['effective_date'] = is_null($item['effective_date']) ? '' : $item['effective_date'];
            $item['expiry_date'] = is_null($item['expiry_date']) ? '' : $item['expiry_date'];
            $item['contract_storage_type'] = static::$t->_($contract_storage_type[$item['contract_storage_type']]);
            $item['sign_type_text'] = static::$t->_($sign_type[$item['sign_type']]);
            $item['can_perform'] = ($item['sign_type'] == ContractEnums::CONTRACT_SIGN_TYPE_1 && $item['status'] == ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING) ? 1 : 0;

        }
        return $items;
    }
}
