<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\CommonService;
use App\Modules\Contract\Models\ContractStandardModel;
use App\Models\oa\ContractCategoryModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\User\Models\DepartmentModel;
use App\Models\oa\SupplierBankModel;

class StandardContractService extends BaseService
{
    private static $instance;
    private static $apiPath;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function __construct()
    {
        self::$apiPath    = env('pdf_rpc_endpoint', '');
    }

    public static $validate_contract = [
        'contract_no'                 => 'Required|StrLenGeLe:0,50',  // 合同编号
        'create_date'                 => 'Required|date', // 新建时间
        'effect_date'                 => 'Required|date', // 生效时间
        'end_date'                    => 'Required',      // 结束时间
        'contract_deadline'           => 'Required|IntIn:1,2,3|>>>:contract deadline type err',   // 有效期
        'deadline_days'               => 'IfIntEq:contract_deadline,3|Required|StrLenGeLe:0,50',  // 有效期自定义天数
        'contract_category'           => 'Required|Int',  // 合同分类
        'is_master'                   => 'Required|IntIn:0,1', // 是否主合同
        'company_name_en'             => 'Required|StrLenGeLe:0,100',    // 公司名称（英文）
        'company_name_local'          => 'Required|StrLenGeLe:0,100',    // 公司名称（本地语种）
        'company_legal_en'            => 'Required|StrLenGeLe:0,70',
        'company_legal_local'         => 'Required|StrLenGeLe:0,70',
        'contract_name_en'            => 'Required|StrLenGeLe:0,70',
        'contract_name_local'         => 'Required|StrLenGeLe:0,70',
        'passport_no'                 => 'Required|StrLenGeLe:0,30',
        'email'                       => 'Required|Email',
        'address'                     => 'Required|StrLenGeLe:0,200',
        'pay_method'                  => 'Required|IntIn:1,2,3,4,5|>>>:pay method deadline type err',   // 结算方式
        'credit_cycle'                => 'Required|StrLenGeLe:0,10', // 信用周期
        'bank_id'                     => 'Required|Int', // 开户行
        'bank_code'                   => 'Required|StrLenGeLe:0,50',
        'bank_branch'                 => 'Required|StrLenGeLe:0,100',
        'bank_branch_code'            => 'StrLenLe:50',
        'account_name'                => 'Required|StrLenGeLe:0,100',
        'bank_account'                => 'Required|StrLenGeLe:0,50', // 银行账号
        'swift_code'                  => 'StrLenLe:50',
        'auth_person'                 => 'Required|StrLenGeLe:0,70',
        'apply_name_en'               => 'Required|StrLenGeLe:0,70',
        'apply_name_local'            => 'Required|StrLenGeLe:0,70',
        'apply_email'                 => 'Required|StrLenGeLe:0,50',
        'rebate_rate'                 => 'Required|FloatGeLe:0,100'
    ];
    public static $validate_contract_edit = [
        'id' => 'Required|Int',//合同id
    ];
    //需求15321新增的字段
    public static $validate_contract_th = [
        'apply_phone' => 'Required|StrLenGeLe:1,15|Numbers',//申请人联系电话
        'contract_bank_name_id' => 'Required|Int',//Flash 银行名称id
        'contract_bank_name' => 'Required|StrLenGeLe:1,100',//Flash 银行名称
        'contract_bank_address' => 'Required|StrLenGeLe:1,1000',//Flash 银行地址
        'contract_bank_account_name' => 'Required|StrLenGeLe:1,100',//Flash 银行账户名称
        'contract_bank_account_no' => 'Required|StrLenGeLe:1,30',//Flash 银行账户
        'contract_bank_swift_code' => 'StrLenGeLe:0,50',//Flash SWIFT Code

    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',                  //每页条数
        'pageNum' => 'Required|IntGt:0',                   //页码
        'contract_no' => 'StrLenLe:20',                      //合同编号
        'contract_type' => 'Int',                        //合同分类
        'create_date_start' => 'Date',                 //创建日期开始时间
        'create_date_end' => 'Date',                   //创建日期结束时间
        'effect_date_start' => 'Date',                 //有效日期开始时间
        'effect_date_end' => 'Date',                   //有效日期结束时间
        'end_date_start' => 'Date',                    //结束日期开始时间
        'end_date_end' => 'Date',                      //结束日期结束时间
    ];

    public static $search_validate_list_search = [
        'department_id' => 'Int'
    ];

    public static $not_must_params = [
        'contract_no',
        'contract_type',
        'create_date_start',
        'create_date_end',
        'effect_date_start',
        'effect_date_end',
        'end_date_start',
        'end_date_end'
    ];

    public static $search_not_must_params = [
        'apply_id',
        'department_id'
    ];

    /**
     * 添加合同
     * @param $data
     * @param $user
     * @return array
     */
    public function add($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $data = $this->handleData($data, $user);
            // 入库前合同数据
            $this->logger->info(
                '标准合同新增入库数据 => data:' . json_encode($data , JSON_UNESCAPED_UNICODE) .
                'uid=>' . $user['id']
            );

            $exists = ContractStandardModel::findFirst([
                'conditions' => 'contract_no = :contract_no:',
                'columns' => 'id',
                'bind' => ['contract_no' => $data['contract_no']]
            ]);
            // 合同编号已存在
            if (isset($exists->id)) {
                throw new ValidationException(static::$t->_('contract_number_has_been_exist'), ErrCode::$VALIDATE_ERROR);
            }
            // 注册公司编号错误
            if (isset($data['register_no']) && !preg_match('/^\d{13}$/', $data['register_no'])) {
                throw new ValidationException(static::$t->_('company_register_no_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 电话号码格式错误
            if (isset($data['phone_no']) && !preg_match('/^\d{10}$/', $data['phone_no'])) {
                throw new ValidationException(static::$t->_('phone_number_format_err'), ErrCode::$VALIDATE_ERROR);
            }
            $model = new ContractStandardModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_create_failed'), ErrCode::$CONTRACT_CREATE_ERROR);
            }
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data'  => []
        ];
    }

    /**
     * 编辑合同
     * @param $data
     * @param $user
     * @return array
     */
    public function update($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $contract_model = ContractStandardModel::findFirst($data['id']);
            // 合同不存在
            if (empty($contract_model)) {
                throw new BusinessException('standard contract not exist', ErrCode::$BUSINESS_ERROR);
            }
            // 注册公司编号错误
            if (isset($data['register_no']) && !preg_match('/^\d{13}$/', $data['register_no'])) {
                throw new ValidationException(static::$t->_('company_register_no_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 电话号码格式错误
            if (isset($data['phone_no']) && !preg_match('/^\d{10}$/', $data['phone_no'])) {
                throw new ValidationException(static::$t->_('phone_number_format_err'), ErrCode::$VALIDATE_ERROR);
            }
            if (trim($data['contract_no']) != $contract_model->contract_no) {
                $exists = ContractStandardModel::findFirst([
                    'conditions' => 'contract_no = :contract_no:',
                    'bind' => ['contract_no' => $data['contract_no']]
                ]);
                // 合同编号已存在
                if (isset($exists->id)) {
                    throw new ValidationException(static::$t->_('contract_number_has_been_exist'), ErrCode::$VALIDATE_ERROR);
                }
            }
            $data = $this->handleData($data, $user);
            $this->logger->info(
                '标准合同编辑入库数据 => contract info:' . json_encode($contract_model->toArray() , JSON_UNESCAPED_UNICODE) .
                'uid=>' . $user['id'] .
                'data=>' . json_encode($data , JSON_UNESCAPED_UNICODE)
            );
            unset($data['id']);
            $bool = $contract_model->i_update($data);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_create_failed'), ErrCode::$CONTRACT_CREATE_ERROR);
            }
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data'  => []
        ];
    }

    /**
     * 合同处理函数
     * @param $data
     * @param $user
     * @return array
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        // 银行名称
        $bank_info = SupplierBankModel::findFirst($data['bank_id']);
        $data['bank_name'] = static::$t->_($bank_info->bank_code ?? '');
        if (!isset($data['id'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['create_id'] = $user['id'] ?? 0;
            $data['create_name'] = $user['name'] ?? '';
            $data['create_department_id'] = $user['department_id'] ?? '';
        }

        return $data;
    }

    /**
     * 获取合同类型
     * @return array
     */
    public function getContractTypes(){
        $list = ContractCategoryModel::find([
            'conditions' => 'is_enable = :is_enable: AND is_deleted = :is_deleted:',
            'columns' => 'id,ancestry_id parent_id,translation_key as label',
            'bind' => ['is_enable' => ContractEnums::IS_ENABLED, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        $item_name = array_column($list,'label','id');
        // 翻译文案
        foreach ($list as &$item) {
            $item['label'] = static::$t->_($item_name[$item['parent_id']]).'/'.static::$t->_($item['label']);
        }

        return array_values(list_to_tree($list,'id','parent_id','children',2));
    }

    /**
     * 获取主从属性
     * @return array
     */
    public function getIsMainList(){
        $is_master_list = [
            Enums::CONTRACT_IS_MASTER_YES => self::$t->_('contract_is_master.1'),
            Enums::CONTRACT_IS_MASTER_NO  => self::$t->_('contract_is_master.2'),
        ];

        $_tmp_data = [];
        foreach ($is_master_list as $code => $t_key) {
            $_tmp_data[] = [
                'code' => $code,
                'label' => self::$t[$t_key],
            ];
        }

        return $_tmp_data;
    }

    /**
     * 获取有效期
     * @return array
     */
    public function getDeadlineList(){
        $deadline_type = [
            ContractEnums::CONTRACT_DEADLINE_HALF_YRAE => self::$t->_('contract_deadline_type.1'),
            ContractEnums::CONTRACT_DEADLINE_YRAE  => self::$t->_('contract_deadline_type.2'),
            ContractEnums::CONTRACT_DEADLINE_OTHER  => self::$t->_('contract_deadline_type.3')
        ];

        $_tmp_data = [];
        foreach ($deadline_type as $code => $t_key) {
            $_tmp_data[] = [
                'code' => $code,
                'label' => self::$t[$t_key],
            ];
        }

        return $_tmp_data;
    }

    /**
     * 获取结算方式
     * @return array
     */
    public function getPayMethod(){
        $pay_method = [
            ContractEnums::PAY_METHOD_LEFT_AMOUNT   => self::$t->_('pay_method_type.1'),
            ContractEnums::PAY_METHOD_LIVE_WAY      => self::$t->_('pay_method_type.2'),
            ContractEnums::PAY_METHOD_ONE_WEEK      => self::$t->_('pay_method_type.3'),
            ContractEnums::PAY_METHOD_HALF_MONTH    => self::$t->_('pay_method_type.4'),
            ContractEnums::PAY_METHOD_HOULE_MONTH   => self::$t->_('pay_method_type.5')
        ];

        $_tmp_data = [];
        foreach ($pay_method as $code => $t_key) {
            $_tmp_data[] = [
                'code' => $code,
                'label' => self::$t[$t_key],
            ];
        }

        return $_tmp_data;
    }

    /**
     * @param array $condition
     * @param int $uid
     * @param int $type
     * @return array
     */
    public function getList($condition, $uid, $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num-1);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('distinct c.id,c.contract_no,c.contract_category,c.create_date,c.effect_date,c.end_date,
        c.create_id apply_id,c.create_name apply_name,c.create_department_id department_id');
        $builder->from(['c' => ContractStandardModel::class]);
        $builder = $this->getCondition($builder, $condition, $type);
        $builder->andWhere('c.is_del = 0');
        $builder->orderBy('c.id desc');

        $count = $builder->getQuery()->execute()->count();
        $builder->limit($page_size, $offset);
        $items = $builder->getQuery()->execute()->toArray();
        $items = $this->handleItems($items);
        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * 条件查询函数
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type)
    {
        $contract_no = $condition['contract_no'] ?? '';
        $contract_type = $condition['contract_category'] ?? 0;
        $create_date_start = $condition['create_date_start'] ?? '';
        $create_date_end = $condition['create_date_end'] ?? '';
        $effect_date_start = $condition['effect_date_start'] ?? '';
        $effect_date_end = $condition['effect_date_end'] ?? '';
        $end_date_start = $condition['end_date_start'] ?? '';
        $end_date_end = $condition['end_date_end'] ?? '';
        $create_department_id = $condition['department_id'] ?? '';
        $create_id = $condition['apply_id'] ?? '';

        if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('c.create_id = :uid:', ['uid' => $condition['uid']]);
        }
        if ($type == self::LIST_TYPE_SEARCH) {
            if (!empty($create_id)) {
                if (is_numeric($create_id)) {
                    $builder->andWhere('c.create_id = :uid:', ['uid' => $create_id]);
                } else {
                    $builder->andWhere('c.create_name = :create_name:', ['create_name' => $create_id]);
                }
            }
            if (!empty($create_department_id)) {
                // 部门以及子部门
                $deps = (new DepartmentService())->getChildrenListByDepartmentIdV2($create_department_id,true);
                $create_department_id = !empty($deps) ? array_values(array_merge([$create_department_id], $deps)) : [$create_department_id];
                $builder->andWhere('c.create_department_id in({ids:array})', ['ids' => $create_department_id]);
            }
        }
        if (!empty($contract_no)) {
            $builder->andWhere('c.contract_no = :cno:', ['cno' => $contract_no]);
        }
        if (!empty($contract_type)) {
            $builder->andWhere('c.contract_category = :contract_type:', ['contract_type' => $contract_type]);
        }
        if (!empty($create_date_start) && !empty($create_date_end)) {
            $builder->andWhere('c.create_date >= :create_date_start: and c.create_date <= :create_date_end:',
                ['create_date_start' => $create_date_start, 'create_date_end' => $create_date_end]);
        }
        if (!empty($effect_date_start) && !empty($effect_date_end)) {
            $builder->andWhere('c.effect_date >= :effect_date_start: and c.effect_date <= :effect_date_end:',
                ['effect_date_start' => $effect_date_start, 'effect_date_end' => $effect_date_end]);
        }
        if (!empty($end_date_start) && !empty($end_date_end)) {
            $builder->andWhere('c.end_date >= :end_date_start: and c.end_date <= :end_date_end:',
                ['end_date_start' => $end_date_start, 'end_date_end' => $end_date_end]);
        }

        return $builder;
    }

    /**
     * 数据处理函数
     * @param $items
     * @return array
     */
    private function handleItems($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        // 分类名称
        $list = ContractCategoryModel::find([
            'conditions' => 'is_enable = :is_enable: AND is_deleted = :is_deleted:',
            'columns' => 'id,ancestry_id,translation_key as label',
            'bind' => ['is_enable' => ContractEnums::IS_ENABLED, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        $list_items = array_column($list,null,'id');
        // 申请人部门
        $department_ids = array_values(array_filter(array_column($items,'department_id')));
        $all_dep = [];
        if (!empty($department_ids)) {
            $all_dep = DepartmentModel::find([
                "conditions" => "id in ({dep_ids:array})",
                "bind"=>["dep_ids"=>$department_ids],
                "columns" => "id,LOWER(name) as name"
            ])->toArray();
            $all_dep = array_column($all_dep,'name','id');
        }

        foreach ($items as $k => &$item) {
            $parent_label = '';
            $this_label = '';

            if (isset($list_items[$item['contract_category']]) && !empty($list_items[$item['contract_category']]['ancestry_id'])) {
                $tmp_ancestry = $list_items[$item['contract_category']]['ancestry_id'];
                $parent_label = static::$t->_($list_items[$tmp_ancestry]['label'] ?? '').'/';
                $this_label = static::$t->_($list_items[$item['contract_category']]['label'] ?? '');
            }

            $item['contract_category'] = $parent_label . $this_label;
            $item['index'] = $k + 1;

            // 部门名称
            $item['department_name'] = !empty($item['department_id']) ? ($all_dep[$item['department_id']] ?? '') : '';
        }

        return $items;
    }

    /**
     * @param integer $id
     * @param array $user
     * @return array
     */
    public function getAuditDetail($id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $contract = [];
        try {
            $contract = $this->getDetail($id, $user);
            if (!isset($contract['id'])) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $contract
        ];
    }

    /**
     * 获取标准合同详情
     * @param integer $id
     * @param array $user
     * @return array
     */
    public function getDetail($id, $user)
    {
        $contract = ContractStandardModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        $contractArr = empty($contract) ? [] : $contract->toArray();
        $contractArr = $this->handleData($contractArr, $user);

        return $contractArr;
    }

    /**
     * 合同下载
     * @param integer $id
     * @return array
     */
    public function download($id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $contract = ContractStandardModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            // 获取模版数据
            $oss_html_path = EnvModel::getEnvByCode('generate_pdf_template_ftl','');
            if (empty($oss_html_path)) {
                throw new BusinessException('ftl模版文件路径未配置', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            // 页眉设置
            $template_header = EnvModel::getEnvByCode('generate_pdf_template_ftl_header','');
            $template_header = str_replace('a2_company_name_en',$contract->company_name_en,$template_header);
            // 页脚设置
            $template_footer = EnvModel::getEnvByCode('generate_pdf_template_ftl_footer','');

            // 整理变量数据
            $form_data = $this->getParamsFromContract($contract->toArray());
            $post_data = [
                'pdfName'=> time(),
                'templateUrl'=>$oss_html_path,
                'data'=>$form_data,
                'downLoadData'=> [],
                "pdfOptions" => [
                    "displayHeaderFooter" => true,
                    "footerTemplate" => $template_footer,
                    "headerTemplate" => $template_header,
                ]
            ];

            // 生成pdf文件
            $data = CommonService::getInstance()->newPostRequest(self::$apiPath . '/api/pdf/createPdfByJvppeteer', json_encode($post_data));
            $this->logger->info("调用pdf生成接口返回数据 =>".json_encode($data,JSON_UNESCAPED_UNICODE));
            if ($data['code'] != ErrCode::$SUCCESS) {
                throw new BusinessException('调用pdf生成接口失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $data = $data['data'] ?? [];
            if(!isset($data['bucket_name']) || !isset($data['content_type'])){
                throw new BusinessException('生成pdf文件格式错误', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data['object_url'] ?? ''
        ];
    }

    /**
     * 从合同信息中取模版变量
     * @param array $contract
     * @return array
     */
    private function getParamsFromContract($contract = []){
        // 结算方式
        $cur_lang = self::$language;
        $contract['pay_method'] = ContractEnums::$pay_method_type_items[$contract['pay_method']] ?? '';
        $contract['contract_deadline'] = ContractEnums::$contract_deadline_type_items[$contract['contract_deadline']] ?? '';
        // 取结算方式和有效期英文翻译
        $contract = $this->getPayMethodAndDeadline($contract,'en');
        // 取结算方式和有效期泰文翻译
        $contract = $this->getPayMethodAndDeadline($contract);


        self::setLanguage($cur_lang);

        return [
            'a1_register_no' => $contract['register_no'],
            'a2_company_name_en' => $contract['company_name_en'],
            'a3_company_name_local' => $contract['company_name_local'],
            'a4_company_legal_en' => $contract['company_legal_en'],
            'a5_company_legal_local' => $contract['company_legal_local'],
            'a6_contract_name_en' => $contract['contract_name_en'],
            'a7_contract_name_local' => $contract['contract_name_local'],
            'a8_passport_no' => $contract['passport_no'],
            'a9_phone_no' => $contract['phone_no'],
            'a10_email' => $contract['email'],
            'a11_address' => $contract['address'],
            'a12_pay_method_en' => $contract['pay_method_en'],
            'a12_pay_method_local' => $contract['pay_method_local'],
            'a13_credit_cycle' => $contract['credit_cycle'],
            'a14_bank_name' => $contract['bank_name'],
            'a15_bank_code' => $contract['bank_code'],
            'a16_bank_branch' => $contract['bank_branch'],
            'a17_bank_branch_code' => $contract['bank_branch_code'],
            'a18_account_name' => $contract['account_name'],
            'a19_bank_account' => $contract['bank_account'],
            'a20_swift_code' => $contract['swift_code'],
            'a20_rebate_rate' => $contract['rebate_rate'],
            'b1_auth_person' => $contract['auth_person'],
            'b2_apply_name_en' => $contract['apply_name_en'],
            'b3_apply_name_local' => $contract['apply_name_local'],
            'b4_apply_email' => $contract['apply_email'],
            'b5_apply_phone' => $contract['apply_phone'],
            'c1_contract_no' => $contract['contract_no'],
            'c2_create_date' => $contract['create_date'],
            'c3_effect_date' => $contract['effect_date'],
            'c4_end_date'    => $contract['end_date'],
            'c5_contract_deadline_en' => $contract['contract_deadline_en'],
            'c5_contract_deadline_local' => $contract['contract_deadline_local'],
            'c6_rebate_rate_en'          => $contract['rebate_rate_en'],
            'c6_rebate_rate_local'       => $contract['rebate_rate_local'],
            'c7_contract_bank_name' => $contract['contract_bank_name'],
            'c8_contract_bank_address' => $contract['contract_bank_address'],
            'c9_contract_bank_account_name' => $contract['contract_bank_account_name'],
            'c10_contract_bank_account_no' => $contract['contract_bank_account_no'],
            'c11_contract_bank_swift_code' => $contract['contract_bank_swift_code'],

        ];
    }

    /**
     * 获取结算方式英文翻译
     * @param array $contract
     * @param string $lang
     * @return array
     */
    private function getPayMethodAndDeadline($contract, $lang = 'local'){
        self::setLanguage($lang == 'local' ? 'th' : $lang);
        $contract['pay_method_' . $lang] = static::$t->_($contract['pay_method']);
        // 有效期
        if (ContractEnums::CONTRACT_DEADLINE_OTHER == $contract['contract_deadline']) {
            $contract['contract_deadline_' . $lang] = $contract['deadline_days'];
        } else {
            $contract['contract_deadline_' . $lang] = static::$t->_($contract['contract_deadline']);
        }
        $rebate_rate_left = $lang == 'en' ? '' : static::$t->_('rebate_rate_left');
        if (0 == $contract['rebate_rate']) {//数值0为0特殊处理
            $contract['rebate_rate_' . $lang] = static::$t->_('rebate_rate_zero');
        } else {
            $contract['rebate_rate_' . $lang] = $rebate_rate_left . ' ' . $contract['rebate_rate'] . ' ' . static::$t->_('rebate_rate_right');
        }

        return $contract;
    }

}
