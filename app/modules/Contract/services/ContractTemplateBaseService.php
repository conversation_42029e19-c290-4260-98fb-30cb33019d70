<?php
/**
 * Created by PhpStorm.
 * Date: 2023/7/13
 * Time: 16:27
 */

namespace App\Modules\Contract\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractSubFileModel;
use App\Models\oa\ContractTemplateBaseModel;
use App\Util\RedisKey;

class ContractTemplateBaseService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static $validate_add = [
        'department_id'             => 'Required|StrLenGeLe:1,200',
        'template_name_zh'          => 'Required|StrLenGeLe:1,100',
        'template_name_en'          => 'Required|StrLenGeLe:1,100',
        'template_name_th'          => 'Required|StrLenGeLe:1,100',
        'business_review'           => 'Required|IntIn:0,1',
        'is_combination_supplemental_agreement' => 'Required|IntIn:0,1,2',
        'subfile_info'              => 'Required|Arr|ArrLenGeLe:1,10',
        'subfile_info[*]'           => 'Required|Obj',
        'subfile_info[*].unique_no' => 'Required|StrLenGeLe:1,32',
        'subfile_info[*].file_name' => 'Required|StrLenGeLe:1,200',
    ];

    public static $validate_edit = [
        'id'                        => 'Required|IntGe:1',
        'department_id'             => 'Required|StrLenGeLe:1,200',
        'business_review'           => 'Required|IntIn:0,1',
        'is_combination_supplemental_agreement' => 'Required|IntIn:0,1,2',
        'subfile_info'              => 'Required|Arr|ArrLenGeLe:1,10',
        'subfile_info[*]'           => 'Required|Obj',
        'subfile_info[*].unique_no' => 'Required|StrLenGeLe:1,32',
        'subfile_info[*].file_name' => 'Required|StrLenGeLe:1,200',
    ];

    public static function validateAdd()
    {
        $validate_add = static::$validate_add;
        $contract_enums = ContractSubFileService::getInstance()->getElectronicContractEnums();
        $contract_type = implode(',', $contract_enums['contract_type']);
        $validate_add['contract_type'] = 'IntIn:' . $contract_type;
        return $validate_add;
    }

    public function add($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $data = $this->checkSubFile($data);

            $insert_data = [
                'no'               => BaseService::genSerialNo('TB', RedisKey::CONTRACT_TEMPLATE_BASE_COUNTER),
                'template_name_zh' => $data['template_name_zh'],
                'template_name_en' => $data['template_name_en'],
                'template_name_th' => $data['template_name_th'],
                'contract_type'    => $data['contract_type'],
                'department_id'    => $data['department_id'],
                'business_review'  => $data['business_review'],
                'is_combination_supplemental_agreement' => $data['is_combination_supplemental_agreement'] ?? 0,
                'subfile_info'     => json_encode($data['subfile_info'], JSON_UNESCAPED_UNICODE),
                'created_at'       => date('Y-m-d H:i:s'),
                'created_id'       => $user['id'],
                'updated_at'       => date('Y-m-d H:i:s'),
                'updated_id'       => $user['id'],
                'is_show_return_person_signature' => $data['is_show_return_person_signature'],
            ];

            $model = new ContractTemplateBaseModel();
            if ($model->i_create($insert_data) === false) {
                throw new BusinessException('合同基础模版创建失败, 原因可能是: ' . get_data_object_error_msg($model) . '; 数据: ' . json_encode($insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-template-base-add-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];
    }


    public function edit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $exists = ContractTemplateBaseModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $data['id']],
            ]);

            // 合同模版不存在
            if (empty($exists)) {
                throw new ValidationException(static::$t->_('contract_template_base_get_fail'), ErrCode::$VALIDATE_ERROR);
            }

            $data                    = $this->checkSubFile($data);
            $exists->contract_type   = $data['contract_type'];
            $exists->business_review = $data['business_review'];
            $exists->department_id   = $data['department_id'];
            $exists->is_combination_supplemental_agreement = $data['is_combination_supplemental_agreement'] ?? 0;
            $exists->subfile_info    = json_encode($data['subfile_info'], JSON_UNESCAPED_UNICODE);
            $exists->updated_id      = $user['id'];
            $exists->updated_at      = date('Y-m-d H:i:s');
            $exists->is_show_return_person_signature = $data['is_show_return_person_signature'];
            if ($exists->save() === false) {
                throw new BusinessException('pmd修改失败, 原因可能是: ' . get_data_object_error_msg($exists) . '; 数据: ' . json_encode($exists->toArray(), JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-template-base-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message
        ];

    }

    public function checkSubFile($data)
    {
        //子文件验证
        $file_infos = array_column($data['subfile_info'], 'file_type');
        $nos        = array_column($data['subfile_info'], 'unique_no');
        if (count($file_infos) != count(array_unique($file_infos))) {
            throw new ValidationException(static::$t->_('template_file_type_repeat'), ErrCode::$VALIDATE_ERROR);
        }

        //判断子文件
        $file_list = ContractSubFileModel::Find([
            'conditions' => 'unique_no in ({nos:array})',
            'bind'       => ['nos' => $nos],
            'columns'    => ['id', 'unique_no', 'file_name', 'file_type', 'is_main', 'department_id', 'is_show_return_person_signature'],
        ])->toArray();


        $department_ids = [];
        $is_main_count  = 0;
        $is_show_return_person_signature = 0;
        foreach ($file_list as $item) {
            $department_ids = array_merge(explode(',', $item['department_id']), $department_ids);
            if ($item['is_main'] == 1) {//主文件
                $is_main_count++;
            }

            if ($item['is_show_return_person_signature'] == 1) {
                $is_show_return_person_signature = 1;
            }
        }

        if (!in_array($data['department_id'], array_unique($department_ids))) {
            throw new ValidationException(static::$t->_('contract_template_department_is_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        if ($is_main_count > 1) {
            throw new ValidationException(static::$t->_('contract_template_main_is_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        $data['is_show_return_person_signature'] = $is_show_return_person_signature;
        return $data;
    }


    /**
     * @param $condition
     * @return array
     */
    public function getList($condition)
    {

        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $builder   = $this->modelsManager->createBuilder();
        $builder->from(['c' => ContractTemplateBaseModel::class]);

        $count = (int)$builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;
        if ($count > 0) {
            $builder->columns('*');
            $builder->orderBy('c.id desc');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handelItems($items);
        }

        return [
            'items'      => $items ?? [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count,
            ]
        ];
    }


    public function getDetail($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $id      = $params['id'];

        try {
            $contract = ContractTemplateBaseModel::findFirst([
                'conditions' => 'id =:id:',
                'bind'       => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist'), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $contract = $contract->toArray();

            $contract_enums      = $this->getEnums();
            $contract_type_enums = array_column($contract_enums['contract_type'], 'name', 'id');

            $department_info          = array_column($contract_enums['use_department'], 'name', 'id');
            $contract['subfile_info'] = json_decode($contract['subfile_info'], true);

            $contract['contract_type_text'] = $contract_type_enums[$contract['contract_type']] ?? '';
            $contract['department_name']    = $department_info[$contract['department_id']];

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract ?? []
        ];
    }


    public function handelItems($items)
    {
        $contract_enums      = $this->getEnums();
        $contract_type_enums = array_column($contract_enums['contract_type'], 'name', 'id');

        $department_info          = array_column($contract_enums['use_department'], 'name', 'id');
        $contract_business_review = ContractEnums::$contract_business_review;

        foreach ($items as &$item) {
            $item['subfile_info']         = json_decode($item['subfile_info'], true);
            $item['contract_type_text']   = $contract_type_enums[$item['contract_type']] ?? '';
            $item['subfile_num']          = count($item['subfile_info']);
            $item['business_review_text'] = static::$t->_($contract_business_review[$item['business_review']]);
            $item['department_name']      = $department_info[$item['department_id']];
        }
        return $items;
    }

    /**
     * 获取所有基础模版
     */
    public function templateList()
    {
        return ContractTemplateBaseModel::find([
            'columns' => ['id', 'no as template_no', 'template_name_zh', 'template_name_en', 'template_name_th', 'contract_type', 'department_id', 'business_review'],
        ])->toArray();
    }


}