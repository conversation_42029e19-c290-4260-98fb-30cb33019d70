<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Models\Contract;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\oa\ContractElectronicRepository;
use App\Repository\oa\VendorRepository;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;
use Mpdf\Mpdf;
use App\Library\OssHelper;
use Mpdf\MpdfException;

class DetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param Contract $contract
     * @return array|mixed
     */
    public function getDetail($contract)
    {
        $contract_arr = [];
        if (empty($contract)) {
            return $contract_arr;
        }
        $contract_arr = $contract->toArray();
        $contract_arr = $this->handleData($contract_arr);
        if(!empty($contract_arr)){
            $contract_arr['contract_file'] = [];
            $file = $contract->getFile(["columns"=>"id,bucket_name,object_key,file_name"]);
            if(!empty($file)){
                $contract_arr['contract_file'] = $file->toArray();
            }
            $attach = $contract->getAttach(["columns"=>"id,bucket_name,object_key,file_name"]);
            if(!empty($attach)){
                $contract_arr['attachment'] = $attach->toArray();
            }
            //关联报价单数据
            //历史版本的不用了
            unset($contract_arr['quotation_no']);
            unset($contract_arr['configure_consumer_id']);
            unset($contract_arr['configure_consumer_time']);
            $contract_quotation = $contract->getContractQuotation(['columns' => 'quotation_no, configure_consumer_id'])->toArray();
            $contract_arr['contract_quotation'] = $contract_quotation;

            //V20608 获取合同归档信息-上传的盖章合同附件
            $contract_archive = $contract->getContractArchive();
            $contract_arr['contract_archive_attachment'] = [];
            if ($contract_archive) {
                $contract_arr['contract_archive_attachment'] = $contract_archive->getFile(['columns' => 'id,bucket_name,object_key,file_name'])->toArray();
            }
        }

        return $contract_arr;
    }

    /**
     *快速复制回显
     * @param $id
     * @param $uid
     * @return array
     */

    public function editDetail($id, $uid)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = '';
        $contract = [];
        try {
            $contract = Contract::findFirst([
                'conditions' => 'id =:id: and create_id =:create_id:',
                'bind'       => ['id' => $id, 'create_id' => $uid]
            ]);
            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            if ($contract->status != Enums::WF_STATE_REJECTED) {
                throw new ValidationException(static::$t->_('contract_status_not_reject', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $contract        = $this->getDetail($contract);
            $contract['cno'] = self::genSerialNo('FEX', RedisKey::CONTRACT_CREATE_COUNTER);
            unset($contract['id']);

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $contract = [];
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $contract
        ];
    }

    /**
     * 根据合同编号获取ID，然后获取合同详情
     *
     * @param String $cno
     * @param int $uid
     * @return array
     */
    public function getContractId($cno,$uid = NULL){
        try {
            $contract_model = Contract::findFirst([
                'cno = :cno:',
                'bind' => ['cno' => $cno]
            ]);
            if (empty($contract_model)) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $contract = $this->getDetail($contract_model);
            if (empty($contract['id'])) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $contract_req = (new ContractFlowService())->getRequest($contract['id']);
            if (empty($contract_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            //待回复征询ID
            $ask = (new FYRService())->getRequestToByReplyAsk($contract_req,$uid);
            $contract['ask_id'] = $ask ? $ask->id:'';

            $contract['auth_logs'] = $this->getContractAuditLogs($contract_model, $contract_req);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('contract-get-public-detail-failed:' . $real_message);
        }

        return [
            'code' => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data' => $contract ?? []
        ];
    }

    /**
     * @param $params
     * @param int $uid
     * @param int $module_type
     * @return array
     */
    public function getAuditDetail($params, $uid = 0, $module_type = 0)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $contract = [];
        try {
            $id = $params['id'];
            $fyr_id = $params['fyr_id'] ?? 0;

            $conditions = "id = :id:";
            $bind = ['id' => $id];
            if ($module_type == ContractEnums::MODULE_TYPE_APPLY) {
                $conditions .= " AND create_id = :create_id:";
                $bind['create_id'] = $uid;
            }
            $contract_model = Contract::findFirst([
                'conditions' => $conditions,
                'bind' => $bind
            ]);
            if (empty($contract_model)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id . '-' . $uid]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $contract = $this->getDetail($contract_model);
            $contract_req = (new ContractFlowService())->getRequest($contract['id']);
            if (empty($contract_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }
            // 意见征询处理
            // 被征询ID与征询内容
            $contract['ask_id'] = '0';
            $contract['fyr_info'] = (object)[];
            // 新逻辑
            if ($fyr_id) {
                $fyr_info = FYRService::getInstance()->getFyrInfoById($contract_req->id, $fyr_id, $uid);
                if (!empty($fyr_info)) {
                    $contract['fyr_info'] = $fyr_info;
                    $contract['ask_id'] = $fyr_info['ask_id'];
                }

            } else {
                //待回复征询ID
                $ask = FYRService::getInstance()->getRequestToByReplyAsk($contract_req, $uid);
                $contract['ask_id'] = $ask ? $ask->id : '';
            }

            $contract['auth_logs'] = $this->getContractAuditLogs($contract_model, $contract_req);

            //归档信息
            $archive_info = ArchiveDetailService::getInstance()->getDetail(0,$contract['cno']);
            $contract['archive_detail'] = [];
            $contract['archive_detail']['holder_name'] = $archive_info['holder_name']??'';
            $contract['archive_detail']['contract_file'] = $archive_info['contract_file']??[];
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('contract-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $contract
        ];
    }

    /**
     * 获取合同的审批流日志
     *
     * @param object $contract_model
     * @param object $contract_req
     * @return array
     * @throws BusinessException
     */
    protected function getContractAuditLogs(object $contract_model, object $contract_req)
    {
        // 电子合同, 取电子合同内容审核日志
        $electronic_contract_audit_log = [];
        if ($contract_model->contract_storage_type == ContractEnums::CONTRACT_STORAGE_TYPE_2) {
            $electronic_contract = ContractElectronicRepository::getInstance()->getOneByRelateId($contract_model->id);
            if (!empty($electronic_contract) && $electronic_contract['business_approve_status'] != 0) {
                $electronic_contract_audit_log = ContractElectronicService::getInstance()->getContentAuditLog($electronic_contract['id']);
            }
        }

        // 其他合同审核日志
        $contract_logs = (new WorkflowServiceV2())->getAuditLogs($contract_req, true);
        if (!empty($electronic_contract_audit_log)) {
            // 去掉合同日志的申请人节点(数组最后一个元素)
            array_pop($contract_logs);
        }

        return array_merge($contract_logs, $electronic_contract_audit_log);
    }

    /**
     * @param $id
     * @param $uid
     * @return array
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function download($id, $uid)
    {
        $exist = WorkflowRequestModel::findFirst([
            'conditions' => "biz_type in(1,20,21,22,23,24) and biz_value = :id: and FIND_IN_SET(:uid:,viewer_ids)",
            'bind' => ['id' => $id, 'uid' => $uid]
        ]);

        if (empty($exist)) {
            throw new ValidationException(static::$t->_('no_permission_to_download'), ErrCode::$CONTRACT_GET_INFO_NO_AUTH_ERROR);
        }

        $contract = Contract::findFirst([
            'id = :id: and status = ' . Enums::CONTRACT_STATUS_APPROVAL,
            'bind' => ['id' => $id]
        ]);

        $file = $contract->getFile(["columns"=>"bucket_name,object_key,file_name"]);
        $file_info = $file->toArray() ?? [];
        if (empty($file_info)) {
            throw new ValidationException(static::$t->_('contract_file_not_exist'), ErrCode::$CONTRACT_GET_NO_CONTRACT_FILE_ERROR);
        }

        // 下载
        $file_url = $this->getShowPath($file_info,true);
        $path = !empty($file_url['object_key']) ? OssHelper::downloadFileHcm($file_url['object_key']) : '';
        $path = $path['file_url'] ?? '';

        // 文件原名
        $file_name = !empty($file_info[0]['file_name']) ? $file_info[0]['file_name'] : 'contract_' . date('ymdHis') . '.pdf';

        // pdf 加水印
        $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
        if (!empty($water_pdf_info['object_key'])) {
            $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
            $download_url = $result['file_url'];
        } else {
            $download_url = $path;
        }

        return [
            'url' => $download_url,
        ];
    }

    /**
     * @param int $id
     * @param string $url
     * @param string $name
     * @return mixed|string
     * @throws BusinessException
     */
    public function outPut($id = 0, $url = '', $name = '')
    {
        $download_url = '';
        $contract = Contract::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (!empty($contract)) {
            $contract = $this->getDetail($contract);
            $path = $this->getShowPath($contract['contract_file']);
            $local_file = sys_get_temp_dir(). '/' . $contract['contract_file'][0]['file_name'] ?? 'contract_' . date('ymdHis').'.pdf';

            // pdf 加水印
            $water_res = WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($path, $local_file);
            if ($water_res === false) {
                $download_url = $path;
            } else {
                // 生成成功, 上传OSS
                $upload_res = OssHelper::uploadFile($local_file);
                $download_url = !empty($upload_res['object_url']) ? $upload_res['object_url'] : '';
            }

        } else if(!empty($url) && !empty($name)) {
            $download_url = $url;
        }

        return $download_url;
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $status = Enums::$contract_status[$data['status']] ?? '';
        $data['status_title'] = static::$t->_($status);

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();

        // 直属分类信息
        $contract_category_info = $contract_category_map[$data['template_id']] ?? [];
        $data['template_title'] = $contract_category_info['label'] ?? '';

        // 若直属分类是二级分类, 则需拼接一级分类
        if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
            $data['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $data['template_title'];
        }

        $data['real_amount'] = bcdiv($data['amount'], 1000, 2);
        $is_master = Enums::$contract_is_master[$data['is_master']] ?? '';
        $data['is_master_title'] = static::$t->_($is_master);
        $payment_currency = GlobalEnums::$currency_item[$data['payment_currency']] ?? '';
        $data['payment_currency_title'] = static::$t->_($payment_currency);
        $data['lang_label'] = isset(Enums::$contract_lang[$data['lang']])?static::$t->_(Enums::$contract_lang[$data['lang']]):'';
        $data['amount'] = bcdiv($data['amount'], 1000, 2);
        // v17222 供应商回显对应的最新名称即可
        $data['vendor_name'] = '';
        if (!empty($data['vendor_id'])) {
            $vendor_info = VendorRepository::getInstance()->getVendorByVendorId($data['vendor_id']);
            $data['vendor_name'] = $vendor_info['vendor_name'] ?? '';
        }

        $franchisee_type_enums = ContractEnums::$franchisee_type_enums;
        $data['franchisee_type_text'] = static::$t->_($franchisee_type_enums[$data['franchisee_type']] ?? '');
        $data['apply_staff_department'] = ($this->isFlashHomeOperation($data['create_department_id'])) ? ContractEnums::FLASH_HOME_OPERATION : 0;
        $data['franchisee_type'] = empty($data['franchisee_type']) ? '' : (int)$data['franchisee_type'];
        return $data;
    }

    /**
     * 生成审批流pdf文件
     *
     * @param int $id
     * @param int $uid
     * @return array
     * @throws GuzzleException
     */
    public function wfDownload(int $id, int $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang = $this->getLang();

        $return_data = [];

        try {
            $contract = Contract::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($contract)) {
                throw new BusinessException('获取合同信息失败', ErrCode::$CONTRACT_GET_INFO_ERROR);
            }
            $data = $this->getDetail($contract);
            if (!$this->isCanDownload($data,$uid)) {
                throw new ValidationException('该合同状态不允许下载文件', ErrCode::$CONTRACT_WORKFLOW_LOG_DOWNLOAD_ERROR);
            }
            $contract_req = (new ContractFlowService())->getRequest($data['id']);

            $data['auth_logs'] = $this->getContractAuditLogs($contract, $contract_req);

            $this->logger->info('待参与pdf的数据=' . json_encode($data, JSON_UNESCAPED_UNICODE));

            // 转义合同描述里的特殊字符
            $data['contract_desc'] = htmlentities($data['contract_desc']);

            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir    = $sys_tmp_dir . '/';
            $file_name   = 'workflow_audit_log_' . md5($id) . "_{$lang}.pdf";
            $file_path   = $file_dir . $file_name;

            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序
            $view->render('contract', 'contract_workflow_audit_log_' . $lang);
            $view->finish();
            $content = $view->getContent();

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'   => 'zh-CN',
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path, "f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path);
            if (!empty($upload_res['object_url'])) {
                $return_data['file_name'] = 'contract_workflow_audit_log.pdf';
                $return_data['file_url']  = $upload_res['object_url'];
                if (!empty($upload_res['object_key'])){
                    $result = OssHelper::downloadFileHcm($upload_res['object_key']);
                    $return_data['file_url'] = $result['file_url'];
                }
            } else {
                throw new BusinessException('合同审批记录下载失败，请重试', ErrCode::$CONTRACT_WORKFLOW_LOG_DOWNLOAD_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (MpdfException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-workflow-audit-log-download-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $return_data,
        ];
    }

    /**
     * 归档 - 下载
     *
     * @param $id
     * @return array
     * @throws GuzzleException
     * @throws ValidationException
     */
    public function genArchiveDownload($id)
    {
        if (empty($id)) {
            return ['url' => ''];
        }

        $contract = Contract::findFirst([
            'conditions' => 'id = :id: AND status = :status:',
            'bind' => ['id' => $id, 'status' => Enums::CONTRACT_STATUS_APPROVAL]
        ]);

        if (empty($contract)) {
            throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $id]), ErrCode::$CONTRACT_GET_INFO_ERROR);
        }

        $file = $contract->getFile(["columns"=>"bucket_name,object_key,file_name"]);
        $file_info = $file->toArray() ?? [];
        if (empty($file_info)) {
            throw new ValidationException(static::$t->_('contract_file_not_exist'), ErrCode::$CONTRACT_GET_NO_CONTRACT_FILE_ERROR);
        }

        // 下载
        $path = $this->getShowPath($file_info,true);
        $file_name = !empty($file_info[0]['file_name']) ? $file_info[0]['file_name'] : 'contract_' . date('ymdHis') . '.pdf';

        $path = OssHelper::downloadFileHcm($path['object_key'], 600);
        $path = $path['file_url'] ?? '';
        // pdf 加水印
        $water_pdf_info = WaterMarkerService::getInstance()->addWaterMarkerToPdfFileV2($path, $file_name, true);
        if (!empty($water_pdf_info['object_key'])) {
            $result = OssHelper::downloadFileHcm($water_pdf_info['object_key']);
            $download_url = $result['file_url'];
        } else {
            $download_url = $path;
        }

        return [
            'url' => $download_url,
        ];
    }

}
