<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\oa\ContractPlatFormModel;
use App\Library\Validation\ValidationException;
use App\Modules\User\Services\UserService;

use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Phalcon\Mvc\Model;

class ContractPlatFormFlowService extends AbstractFlowService
{

    /**
     * @param $contract_id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($contract_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $request = $this->getRequest($contract_id);
        $db      = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }
            $contract = ContractPlatFormModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $contract_id]
            ]);
            $result   = (new WorkflowServiceV2())->doApprove($request, $user, $this->getWorkflowParams($contract, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流通过失败', ErrCode::$BUSINESS_ERROR);
            }

            if (!empty($result->approved_at)) {
                $bool = $contract->i_update([
                    'status'      => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_at'  => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at,
                ]);
                if ($bool === false) {
                    throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
                }
                //终审归档
                ContractPlatFormService::getInstance()->saveArchive($contract_id, $user);

            }

            $db->commit();
        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-platform-approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contract_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($contract_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $request = $this->getRequest($contract_id);
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }
            $contract = ContractPlatFormModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $contract_id]
            ]);
            $result   = (new WorkflowServiceV2())->doReject($request, $user, $this->getWorkflowParams($contract, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$BUSINESS_ERROR);
            }

            $bool = $contract->i_update([
                'status'      => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at'  => date('Y-m-d H:i:s'),
                'rejected_at' => $result->rejected_at,
            ]);
            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
            }
        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-platform-reject-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contract_id
     * @param $note
     * @param $user
     * @return mixed|void
     */
    public function cancel($contract_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $contract = ContractPlatFormModel::findFirst([
                'conditions' => 'id = :id: AND create_id = :create_id: AND status=:status:',
                'bind'       => ['id' => $contract_id, 'create_id' => $user['id'], 'status' => Enums::CONTRACT_STATUS_PENDING]
            ]);

            if (empty($contract)) {
                throw new ValidationException(static::$t->_('contract_data_not_exist', ['id' => $contract_id . '-' . $user['id']]), ErrCode::$CONTRACT_GET_INFO_ERROR);
            }

            $request = $this->getRequest($contract_id);
            if (empty($request->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$BUSINESS_ERROR);
            }
            if ($request->state != Enums::WF_STATE_PENDING) {
                throw new BusinessException('审批状态错误，不允许进行该操作', ErrCode::$BUSINESS_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($request, $user, $this->getWorkflowParams($contract, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流撤回失败', ErrCode::$BUSINESS_ERROR);
            }
            $bool = $contract->i_update([
                'status'        => Enums::CONTRACT_STATUS_CANCEL,
                'updated_at'    => date('Y-m-d H:i:s'),
                'cancel_reason' => $note,
            ]);

            if ($bool === false) {
                throw new BusinessException('同步合同信息失败', ErrCode::$BUSINESS_ERROR);
            }

        } catch (ValidationException $e) {
            //校验错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {                 //业务错误可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('contract-withdrawal-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $contract_id
     * @return Model
     */
    public function getRequest($contract_id)
    {
        $biz_type = Enums::WF_CONTRACT_GPMD_BIZ_TYPE;
        return $this->getRequestByBiz($contract_id, $biz_type);
    }

    /**
     * 获取当前业务审批流信息
     * @param $id
     * @param $bizType
     * @return \Phalcon\Mvc\Model
     */
    public function getRequestByBiz($id, $bizType)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id:',
                'bind'  => ['type' => $bizType, 'id' => $id],
                'order' => 'id desc'
            ]
        );
    }

    /**
     * @param $contract_id
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($contract_id, $user)
    {
        $workflowServiceV2 = new WorkflowServiceV2();
        $contract          = ContractPlatFormModel::findFirst($contract_id);
        $data['id']        = $contract->id;
        $data['name']      = $contract->cno . '审批申请';
        $data['biz_type']  = Enums::WF_CONTRACT_GPMD_BIZ_TYPE;
        $data['flow_id']   = $this->getFlowId($contract);

        return $workflowServiceV2->createRequest($data, $user, $this->getWorkflowParams($contract, $user));
    }

    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit($item, $user)
    {
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException('没有找到req=' . $item->id);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->save();

        return $this->createRequest($item->id, $user);
    }

    /**
     * 根据业务类型获取审批流ID
     * @param Model $model
     * @return int
     */
    public function getFlowId($model = null)
    {
        return Enums::CONTRACT_GPMD_WF_ID;
    }


    /**
     * @param $item
     * @param $user
     * @return array
     */
    private function getWorkflowParams($item, $user)
    {
        $staff_info = (new UserService())->getUserByIdInRbi($item->create_id);

        return [
            'submitter_id' => $staff_info->staff_info_id,
        ];
    }

    /***
     * @param $builder
     * @param $biz_type
     * @param $biz_table_alias
     * @return
     */
    public function getBizWorkflowOrderList($builder, $biz_type, $biz_table_alias)
    {
        $builder->leftjoin(WorkflowRequestModel::class, "request.biz_value = {$biz_table_alias}.id", 'request');
        $builder->leftjoin(WorkflowAuditLogModel::class, "request.id = log.request_id", 'log');
        $builder->andWhere('request.biz_type = :biz_type: AND request.is_abandon = :is_abandon:', [
            'biz_type'   => $biz_type,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);
        return $builder;
    }

}
