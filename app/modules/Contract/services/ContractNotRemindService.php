<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ContractStoreRentingNotRemindModel;
use App\Models\oa\ContractStoreRentingRemindModel;
use App\Repository\oa\ContractStoreRentingNotRemindRepository;
use App\Repository\StoreRepository;


class ContractNotRemindService extends BaseService
{
    public static $not_must_params_list = [
        'store_id',
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',//每页条数
        'pageNum' => 'Required|IntGt:0',//页码
        'store_id' => 'StrLenGe:1',//网点id
    ];

    //参数验证规则-新增
    public static $validate_add = [
        'store_id' => 'Required|StrLenGeLe:1,10', //网点id
        'store_name' => 'Required|StrLenGeLe:1,50', //使用方向
        'reason' => 'StrLenGeLe:0,300', //原因
    ];

    //参数验证规则-新增
    public static $validate_delete = [
        'id' => 'Required|IntGe:1', //id
    ];


    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ContractNotRemindService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 不提醒网点添加
     * @param $params
     * @param $user
     * @return array
     * @date 2023/7/17
     */
    public function add($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //验证是不是正经网点
            $store_info = (new StoreRepository())->getStoreDetail($params['store_id']);
            if (empty($store_info)) {
                throw new ValidationException(static::$t->_('contract_not_remind_store_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //查询当前网点是否存在
            $data_exist = ContractStoreRentingNotRemindRepository::getInstance()->getInfoByStoreId($params['store_id']);
            if (!empty($data_exist)) {
                throw new ValidationException(static::$t->_('contract_not_remind_already_existed'), ErrCode::$VALIDATE_ERROR);
            }
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $now_data = date('Y-m-d H:i:s');
            //执行添加
            $insert = [
                'store_id' => $params['store_id'],
                'store_name' => $params['store_name'],
                'reason' => $params['reason'] ?? '',
                'created_id' => $user['id'],
                'created_at' => $now_data
            ];
            $not_remind_model = new ContractStoreRentingNotRemindModel();
            if ($not_remind_model->i_create($insert) == false) {
                throw new BusinessException('不提醒网点添加失败, 参数: ' . json_encode($insert, JSON_UNESCAPED_UNICODE) . ' ; 可能的原因是:' . get_data_object_error_msg($not_remind_model), ErrCode::$BUSINESS_ERROR);
            }
            //查询是否存在待处理提醒
            $remind_data = ContractStoreRentingRemindModel::find([
                'conditions' => 'store_id = :store_id: and task_status = :task_status:',
                'bind' => [
                    'store_id' => $params['store_id'],
                    'task_status' => ContractEnums::TASK_STATUS_TODO
                ]
            ]);
            foreach ($remind_data as $one_remind) {
                $one_remind->task_status = ContractEnums::TASK_STATUS_DONE;
                $one_remind->updated_at = $now_data;
                $one_remind->finished_at = $now_data;
                if ($one_remind->save() == false) {
                    throw new BusinessException('不提醒网点-设置已提醒失败, data: ' . json_encode($one_remind->toArray(), JSON_UNESCAPED_UNICODE) . ' ; 可能的原因是:' . get_data_object_error_msg($one_remind), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
            //待处理提醒改成已处理
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-not-remind-add-failed:' . $real_message);
        }
        if (!empty($message) && isset($db)) {
            $db->rollback();
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取列表
     * @param $condition
     * @return array
     */
    public function getList($condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //列表查询总数
            $count = $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('id, store_id, store_name, reason, created_id, created_at');
                $builder->from(ContractStoreRentingNotRemindModel::class);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-contract-not-remind-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 根据条件获取总数量
     * @param array $condition
     * @return int
     */
    public function getListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from([ContractStoreRentingNotRemindModel::class]);
        $builder->columns('count(id) AS count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * @param $builder
     * @param $condition
     * @return mixed
     */
    private function getCondition($builder, $condition)
    {
        $store_id = $condition['store_id'] ?? '';
        //网点id
        if (!empty($store_id)) {
            $builder->andWhere('store_id = :store_id:', ['store_id' => $store_id]);
        }
        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        return $builder;
    }

    /**
     * @param $items
     * @return array
     */
    private function handleItems($items)
    {
        foreach ($items as &$item) {
            $item['created_at'] = $item['created_at'] ?? '';
        }
        return $items;
    }

    /**
     * 不提醒网点删除
     * @param $params
     * @param $user
     * @return array
     */
    public function delete($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询当前数据是否存在
            $info = ContractStoreRentingNotRemindRepository::getInstance()->getInfoById($params['id']);
            if (!$info) {
                throw new ValidationException(static::$t->_('contract_not_remind_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $info->is_deleted = GlobalEnums::IS_DELETED;
            if ($info->save() == false) {
                throw new BusinessException('不提醒网点删除失败, data: ' . json_encode($info->toArray(), JSON_UNESCAPED_UNICODE) . ' ; 可能的原因是:' . get_data_object_error_msg($info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('contract-not-remind-delete-failed:' . $real_message);
        }
        $this->logger->info('contract-not-remind-delete-success: operator_id=' . $user['id'] . '; id=' . $params['id']);
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }
}
