<?php

namespace App\Modules\Contract\Services;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Common\Services\EnumsService;
use App\Models\oa\ContractQuotationModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\CrmQuotation\Models\CrmQuotationApplyModel;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeFYR;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Common\Models\ContractCompanyModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\SysDepartmentModel;
use App\Repository\DepartmentRepository;
use GuzzleHttp\Exception\GuzzleException;

class ListService extends BaseService
{
    const AMOUNT_THB_ONE_HUNDRED_THOUSAND = *********;          //十万
    const AMOUNT_THB_FIVE_HUNDRED_THOUSAND = *********;         //五十万
    const AMOUNT_THB_ONE_MILLION = *********0;                  //一百万
    const AMOUNT_THB_FIVE_MILLION = *********0;                 //五百万
    const AMOUNT_THB_TEN__MILLION = *********00;                //一千万

    const AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED = 3500000;                    //三千五
    const AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED = 16600000;                  //一万六千六
    const AMOUNT_USD_THIRTY_THREE_THOUSAND = 33000000;                         //三万三千
    const AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND = *********;           //十六万六千

    const AMOUNT_CNY_TWENTY_FIVE_THOUSAND = 25000000;                                      //两万五千
    const AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND = 117000000;                       //十一万七千
    const AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND = *********;                     //二十三万四千
    const AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND = 1168000000;        //一百一十六万八千


    public static $not_must_params = [
        'cno',
        'quotation_no',
        'cname',
        'template_id',
        'created_at_start',
        'created_at_end',
        'amount_type',
        'approved_at_start',
        'approved_at_end',
        'status',
        'create_name',
        'customer_company_name',
        'company_code',
        'create_department_id'
    ];

    public static $validate_list_search = [
        'pageSize' => 'Required|IntGt:0',                  //每页条数
        'pageNum' => 'Required|IntGt:0',                   //页码
        //'cno' => 'StrLen:12',                            //合同编号
        //'cname' => 'StrLenGeLe:1,50',                      //合同名称
        //'template_id' => 'IntIn:11,12,13,14,15',           //合同模版
        'created_at_start' => 'DateTime',                  //合同申请开始时间
        'created_at_end' => 'DateTime',                    //合同申请结束时间
        'amount_type' => 'IntGeLe:1,6',                    //合同金额
        'approved_at_start' => 'DateTime',                 //合同生效开始时间
        'approved_at_end' => 'DateTime',                   //合同生效结束时间
        'status' => 'IntIn:1,2,3,4',                       //合同状态
        'create_name' => 'StrLenGeLe:2,50',
        'is_reply' => 'IntIn:0,1',
        'customer_company_name' => 'StrLenGeLe:0,50', //客户公司名称
        'company_code' => 'IntGt:0', //合同所属公司
        'create_department_id' => 'IntGt:0', //负责人部门id
    ];

    public static $validate_export_search = [
        'created_at_start' => 'DateTime',                  //合同申请开始时间
        'created_at_end' => 'DateTime',                    //合同申请结束时间
        'amount_type' => 'IntGeLe:1,6',                    //合同金额
        'approved_at_start' => 'DateTime',                 //合同生效开始时间
        'approved_at_end' => 'DateTime',                   //合同生效结束时间
        'status' => 'IntIn:1,2,3,4',                       //合同状态
        'create_name' => 'StrLenGeLe:2,50',
        'is_reply' => 'IntIn:0,1',
        'customer_company_name' => 'StrLenGeLe:0,50', //客户公司名称
        'company_code' => 'IntGt:0', //合同所属公司
        'create_department_ids' => 'Arr', //负责人部门id
        'create_department_ids[*]' => 'IntGt:0',
    ];

    public static $validate_check_list_search = [
        'status' => 'IntIn:2,3,4',                       //合同状态
        'create_id' => 'IntGt:0',                        //业务专员
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $condition
     * @param $user
     * @param int $type
     * @return array
     */
    public function getList($condition, $user, $type = 0)
    {
        $condition['uid'] = $user['id'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num-1);

        $items = [];
        $count = 0;
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['c' => Contract::class]);
            //只有数据查询有报价单搜索, 如果我的申请需要开启搜索,要注意关联contract_quotation会影响log_num数量,进而影响是否可编辑是否可撤回
            if ($type == self::LIST_TYPE_SEARCH) {
                $builder->leftJoin(ContractQuotationModel::class, 'cq.contract_id = c.id', 'cq');
                $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = ms.staff_info_id', 'ms');
            }
            $builder = $this->getCondition($builder, $condition, $type, $user);
            $builder->andWhere('c.contract_type != :contract_type: ', ['contract_type' => 'store_renting_contract']);
            $count = (int) $builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $columns = 'c.id,c.cno,c.cname,c.status,c.template_id,c.payment_currency,c.amount,c.refuse_reason,c.cancel_reason,c.create_id,c.create_name,c.create_department,c.created_at,c.approved_at,c.contract_file,c.sell_cno,c.effective_date,c.expiry_date,c.contract_storage_type,c.sign_type';
                // 审核模块的已处理列表, 展示处理时间
                if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    $columns .= ',log.audit_at';
                } else if ($type == self::LIST_TYPE_APPLY) {
                    $columns .= ',request.state,count(1) as log_num';
                } else if ($type == self::LIST_TYPE_SEARCH) {
                    $columns .= ',c.is_group_contract, c.customer_company_name, c.company_code, ms.node_department_id';
                } else if ($type == self::LIST_TYPE_FYR) {
                    // 借款回复列表:返回征询ID，回复详情接口用到
                    $columns .= ',reply.fyr_id';
                }
                $builder->columns($columns);
                //合同回复不group, 有几次征询就显示几条数据
                if ($type != self::LIST_TYPE_FYR) {
                    $builder->groupBy('c.id');
                }

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('c.id desc');
                }

                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items,$type);
            }
        } catch (\Exception $e) {
            $this->logger->warning('其他合同列表获取失败, 原因可能是' . $e->getMessage());
        }

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ]
        ];
    }

    /**
     * @param $condition
     * @param $user
     * @return array
     * @throws BusinessException
     */
    public function getDownloadList($condition, $user)
    {
        ini_set('memory_limit', '1024M');

        $condition['uid'] = $user['id'];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            distinct c.id,
            c.cno,
            c.cname,
            c.company_code,
            c.template_id,
            c.sell_cno,
            cq.quotation_no,
            c.pay_method,
            c.balance_days,
            c.valuation_type,
            c.discount,
            cq.configure_consumer_id,
            c.lang,
            c.payment_currency,
            c.amount,
            c.is_master,
            c.in_scope,
            c.sub_cno,
            c.is_group_contract,
            c.company_relation_code,
            c.contract_desc,
            c.create_department,
            c.create_id,
            c.create_name,
            c.created_at,
            c.status,
            ca.id archive_id,
            ca.status archive_status,
            ca.filing_id a_create_id,
            ca.filing_name a_create_name,
            ca.filing_at a_filing_at,
            ca.holder_name,
            c.company_code,
            c.customer_company_name,
            ms.node_department_id,
            c.expiry_date,
            c.effective_date,
            c.contract_storage_type,
            ms.node_department_id
        ');
        $builder->from(['c' => Contract::class]);
        $builder->leftJoin(ContractArchive::class, 'ca.cno=c.cno', 'ca');
        $builder->leftJoin(ContractQuotationModel::class, 'cq.contract_id = c.id', 'cq');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = ms.staff_info_id', 'ms');
        $builder = $this->getCondition($builder, $condition, ListService::LIST_TYPE_SEARCH, $user);
        $builder->andWhere('c.contract_type != :contract_type: ', ['contract_type' => 'store_renting_contract']);
        $builder->orderBy('c.id desc');
        $items = $builder->getQuery()->execute()->toArray();
        $items = $this->handleItemExport($items);

        return $this->_exportContract($items);
    }

    /**
     * 获取导出的总记录数
     * @param array $condition 请求参数组
     * @param array $user 当前登录着信息组
     * @param int $type 列表类型
     * @return int
     */
    public function getTotal($condition, $user)
    {
        $condition['uid'] = $user['id'];
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => Contract::class]);
        $builder->leftJoin(ContractArchive::class, 'ca.cno=c.cno', 'ca');
        $builder->leftJoin(ContractQuotationModel::class, 'cq.contract_id = c.id', 'cq');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = ms.staff_info_id', 'ms');
        $builder = $this->getCondition($builder, $condition, ListService::LIST_TYPE_SEARCH, $user);
        $builder->andWhere('c.contract_type != :contract_type: ', ['contract_type' => 'store_renting_contract']);
        $builder->orderBy('c.id desc');
        return (int)$builder->columns('COUNT(DISTINCT c.id) AS total')->getQuery()->getSingleResult()->total;
    }

    /**
     * @param $builder
     * @param $condition
     * @param int $type
     * @param array $user
     * @return mixed
     * @throws BusinessException
     */
    private function getCondition($builder, $condition, $type = 0, $user = [])
    {
        $cno = $condition['cno'] ?? '';
        $quotation_no = $condition['quotation_no'] ?? '';
        $c_name = $condition['cname'] ?? '';
        $template_id = $condition['template_id'] ?? 0;
        $created_at_start = $condition['created_at_start'] ?? '';
        $created_at_end = $condition['created_at_end'] ?? '';
        $amount_type = $condition['amount_type'] ?? 0;
        $approved_at_start = $condition['approved_at_start'] ?? '';
        $approved_at_end = $condition['approved_at_end'] ?? '';
        $status = $condition['status'] ?? 0;
        //是否集团公司间合同
        $is_group_contract = isset($condition['is_group_contract']) && key_exists($condition['is_group_contract'], ContractEnums::$is_group_contract_items) ? $condition['is_group_contract'] : '';
        //客户公司名称
        $customer_company_name = $condition['customer_company_name'] ?? '';
        //合同所属公司
        $company_code = $condition['company_code'] ?? 0;
        //合同负责人所在部门
        $create_department_id = $condition['create_department_id'] ?? 0;

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $create_name = $condition['create_name'] ?? '';
        $effective_date_start = $condition['effective_date_start'] ?? '';
        $effective_date_end = $condition['effective_date_end'] ?? '';
        $expiry_date_start = $condition['expiry_date_start'] ?? '';
        $expiry_date_end = $condition['expiry_date_end'] ?? '';

        if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('c.create_id = :uid:', ['uid' => $condition['uid']]);
            if (!empty($status)) {
                $builder->andWhere('c.status = :status:', ['status' => $status]);
            }
            $biz_type = [
                Enums::WF_CONTRACT_TYPE1,
                Enums::WF_CONTRACT_TYPE20,
                Enums::WF_CONTRACT_TYPE21,
                Enums::WF_CONTRACT_TYPE22,
                Enums::WF_CONTRACT_TYPE23,
                Enums::WF_CONTRACT_TYPE24
            ];
            $builder = (new ContractFlowService())->getBizWorkflowOrderList($builder,$biz_type,'c');
        } else if ($type == self::LIST_TYPE_AUDIT) {
            $biz_type = [
                Enums::WF_CONTRACT_TYPE1,
                Enums::WF_CONTRACT_TYPE20,
                Enums::WF_CONTRACT_TYPE21,
                Enums::WF_CONTRACT_TYPE22,
                Enums::WF_CONTRACT_TYPE23,
                Enums::WF_CONTRACT_TYPE24
            ];
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, $biz_type, $condition['uid'], 'c');

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_type = [
                Enums::WF_CONTRACT_TYPE1,
                Enums::WF_CONTRACT_TYPE20,
                Enums::WF_CONTRACT_TYPE21,
                Enums::WF_CONTRACT_TYPE22,
                Enums::WF_CONTRACT_TYPE23,
                Enums::WF_CONTRACT_TYPE24
            ];
            $biz_table_info = ['table_alias' => 'c'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, $biz_type, $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_SEARCH) {
            if (!empty($status)) {
                $builder->andWhere('c.status = :status:', ['status' => $status]);
            }

            // 对接通用数据权限
            // 业务表参数
            $table_params = [
                'table_alias_name' => 'c',
                'create_id_field' => 'create_id',
                'create_node_department_id_filed' => 'create_department_id',
            ];
            $builder = CommonDataPermissionService::getInstance()->getCommonDataPermission($user, $builder, Enums\SysConfigEnums::SYS_MODULE_OTHER_CONTRACT, $table_params);

            if ($is_group_contract !== '') {
                $builder->andWhere('c.is_group_contract = :is_group_contract:', ['is_group_contract' => $is_group_contract]);
            }
            if (!empty($customer_company_name)) {
                $builder->andWhere('c.customer_company_name like :customer_company_name:', ['customer_company_name' => '%' . $customer_company_name . '%']);
            }
            if (!empty($company_code)) {
                $builder->andWhere('c.company_code = :company_code:', ['company_code' => $company_code]);
            }
            if (!empty($create_department_id)) {
                //按照部门查找,需要找该部门以及下属子部门的所有信息
                $department_service = new DepartmentService();
                $department_ids = $department_service->getChildrenListByDepartmentIdV2($create_department_id, true);
                array_push($department_ids, $create_department_id);
                $builder->inWhere('ms.node_department_id', $department_ids);
            }
        }

        if (!empty($cno)) {
            $builder->andWhere('c.cno = :cno:', ['cno' => $cno]);
        }

        if (!empty($quotation_no) && $type == self::LIST_TYPE_SEARCH) {
            $builder->andWhere('cq.quotation_no = :quotation_no:', ['quotation_no' => $quotation_no]);
        }

        if (!empty($template_id)) {
            $builder->andWhere('c.template_id = :template_id:', ['template_id' => $template_id]);
        }

        if (!empty($amount_type)) {
            switch ($amount_type) {
                case 1:
                    $builder->andWhere('(c.payment_currency = :1: and c.amount < :2:) OR (c.payment_currency = :3: and c.amount < :4:) OR (c.payment_currency = :5: and c.amount < :6:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_HUNDRED_THOUSAND,
                            '3' => GlobalEnums::CURRENCY_USD,
                            '4' => self::AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED,
                            '5' => GlobalEnums::CURRENCY_CNY,
                            '6' => self::AMOUNT_CNY_TWENTY_FIVE_THOUSAND
                        ]);
                    break;
                case 2:
                    $builder->andWhere('(c.payment_currency = :1: and c.amount >= :2: and c.amount < :3:) OR (c.payment_currency = :4: and c.amount >= :5: and c.amount < :6:) OR (c.payment_currency = :7: and c.amount >= :8: and c.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_HUNDRED_THOUSAND,
                            '3' => self::AMOUNT_THB_FIVE_HUNDRED_THOUSAND,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_THREE_THOUSAND_FIVE_HUNDRED,
                            '6' => self::AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_TWENTY_FIVE_THOUSAND,
                            '9' => self::AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND
                        ]);
                    break;
                case 3:
                    $builder->andWhere('(c.payment_currency = :1: and c.amount >= :2: and c.amount < :3:) OR (c.payment_currency = :4: and c.amount >= :5: and c.amount < :6:) OR (c.payment_currency = :7: and c.amount >= :8: and c.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_FIVE_HUNDRED_THOUSAND,
                            '3' => self::AMOUNT_THB_ONE_MILLION,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_SIXTEEN_THOUSAND_SIX_HUNDRED,
                            '6' => self::AMOUNT_USD_THIRTY_THREE_THOUSAND,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_ONE_HUNDRED_AND_SEVENTEEN_THOUSAND,
                            '9' => self::AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND
                        ]);
                    break;
                case 4:
                    $builder->andWhere('(c.payment_currency = :1: and c.amount >= :2: and c.amount < :3:) OR (c.payment_currency = :4: and c.amount >= :5: and c.amount < :6:) OR (c.payment_currency = :7: and c.amount >= :8: and c.amount < :9:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_ONE_MILLION,
                            '3' => self::AMOUNT_THB_FIVE_MILLION,
                            '4' => GlobalEnums::CURRENCY_USD,
                            '5' => self::AMOUNT_USD_THIRTY_THREE_THOUSAND,
                            '6' => self::AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND,
                            '7' => GlobalEnums::CURRENCY_CNY,
                            '8' => self::AMOUNT_CNY_TWO_HUNDRED_AND_THIRTY_FOUR_THOUSAND,
                            '9' => self::AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND
                        ]);
                    break;
                case 5:
                    $builder->andWhere('(c.payment_currency = :1: and c.amount >= :2:) OR (c.payment_currency = :3: and c.amount >= :4:) OR (c.payment_currency = :5: and c.amount >= :6:)',
                        [
                            '1' => GlobalEnums::CURRENCY_THB,
                            '2' => self::AMOUNT_THB_FIVE_MILLION,
                            '3' => GlobalEnums::CURRENCY_USD,
                            '4' => self::AMOUNT_USD_ONE_HUNDRED_AND_SIXTY_SIX_THOUSAND,
                            '5' => GlobalEnums::CURRENCY_CNY,
                            '6' => self::AMOUNT_CNY_ONE_MILLION_ONE_HUNDRED_AND_SIXTY_EIGHT_THOUSAND
                        ]);
                    break;
                default:
                    break;
            }
        }

        if (!empty($created_at_start)) {
            $builder->andWhere('c.created_at >= :created_at_start:', ['created_at_start' => $created_at_start]);
        }

        if (!empty($created_at_end)) {
            $builder->andWhere('c.created_at < :created_at_end:', ['created_at_end' => $created_at_end]);
        }

        if (!empty($approved_at_start)) {
            $builder->andWhere('c.approved_at >= :approved_at_start:', ['approved_at_start' => $approved_at_start]);
        }

        if (!empty($approved_at_end)) {
            $builder->andWhere('c.approved_at < :approved_at_end:', ['approved_at_end' => $approved_at_end]);
        }

        if (!empty($create_name)) {
            $builder->andWhere('c.create_name = :create_name:', ['create_name' => $create_name]);
        }

        if (!empty($c_name)) {
            $builder->andWhere('c.cname LIKE :cname:', ['cname' => $c_name . '%']);
        }

        if (!empty($effective_date_start)) {
            $builder->andWhere('c.effective_date >= :effective_date_start:', ['effective_date_start' => $effective_date_start]);
        }

        if (!empty($effective_date_end)) {
            $builder->andWhere('c.effective_date <= :effective_date_end:', ['effective_date_end' => $effective_date_end]);
        }

        if (!empty($expiry_date_start)) {
            $builder->andWhere('c.expiry_date >= :expiry_date_start:', ['expiry_date_start' => mb_substr($expiry_date_start, 0, 10)]);
        }

        if (!empty($expiry_date_end)) {
            $builder->andWhere('c.expiry_date <= :expiry_date_end:', ['expiry_date_end' => mb_substr($expiry_date_end, 0, 10)]);
        }

        return $builder;
    }

    /**
     * @param $items
     * @param $type
     * @return array
     */
    private function handleItems($items, $type)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        $contract_storage_type = ContractEnums::$contract_storage_type;
        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();
        //合同所属公司
        $contract_company = EnumsService::getInstance()->getContractCompanyItem();
        //查部门名称
        $node_department_ids = array_column($items, 'node_department_id');
        $node_department_ids = array_values(array_unique($node_department_ids));
        $department_data = (new DepartmentRepository())->getDepartmentByIds($node_department_ids, 2);
        $sign_type = ContractEnums::$contract_sign_type;
        foreach ($items as &$item) {
            $status = Enums::$contract_status[$item['status']] ?? '';
            $item['status_title'] = static::$t->_($status);

            // 直属分类信息
            $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
            $item['template_title'] = $contract_category_info['label'] ?? '';

            // 若直属分类是二级分类, 则需拼接一级分类
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $item['template_title'] = $contract_category_map[$contract_category_info['ancestry_id']]['label'] . '/' . $item['template_title'];
            }

            $payment_currency = GlobalEnums::$currency_item[$item['payment_currency']] ?? '';
            $item['payment_currency_title'] = static::$t->_($payment_currency);
            $item['real_amount'] = bcdiv($item['amount'], 1000, 2);
            if ($type == ListService::LIST_TYPE_APPLY) {
                $item['can_cancel'] = $item['state'] == Enums::WF_STATE_PENDING && $item['log_num'] <= 2 ? 1:2;
                $item['can_edit'] = $item['state'] == Enums::WF_STATE_PENDING && $item['log_num'] == 1 ? 1:0;
                $item['can_copy'] = ($item['status'] == Enums::WF_STATE_REJECTED && $item['contract_storage_type'] == ContractEnums::CONTRACT_STORAGE_TYPE_1 && $item['created_at'] > '2023-09-01 00:00:00') ? 1 : 2;
            }
            $item['can_download'] = Enums::CONTRACT_STATUS_APPROVAL == $item['status'] ? 1 : 0;
            if ($type == ListService::LIST_TYPE_SEARCH) {
                //是否为集团公司间合同
                $item['is_group_contract_text'] = isset(ContractEnums::$is_group_contract_items[$item['is_group_contract']]) ? static::$t[ContractEnums::$is_group_contract_items[$item['is_group_contract']]] : '';
                //合同所属公司
                $item['company_code_text'] = $contract_company[$item['company_code']] ?? '';
                //合同负责人所在部门
                $item['create_department_text'] = $department_data[$item['node_department_id']]['name'] ?? '';
            }

            $item['effective_date'] = is_null($item['effective_date']) ? '' : $item['effective_date'];
            $item['expiry_date']    = is_null($item['expiry_date']) ? '' : $item['expiry_date'];

            $item['contract_storage_type'] = static::$t->_($contract_storage_type[$item['contract_storage_type']]);
            $item['sign_type_text'] = !empty($item['sign_type']) ? static::$t->_($sign_type[$item['sign_type']]) : '';
        }

        return $items;
    }

    /**
     * 按照excel导出格式处理数据
     * @param $items
     * @return array
     */
    private function handleItemExport($items)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $export_file_url_time_limit_second = (int)EnumsService::getInstance()->getSettingEnvValue('export_file_url_time_limit_second', 600);
        // 获取合同、附件
        $contract_ids = array_column($items,'id');
        $detail_attachment = $archive_attachment = [];
        if (!empty($contract_ids)) {
            $detail_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type in({oss_bucket_type:array}) AND oss_bucket_key IN ({keys:array}) AND deleted = :deleted:',
                'bind' => [
                    'oss_bucket_type' => [Enums::OSS_BUCKET_TYPE_CONTRACT_FILE,Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT],
                    'keys' => $contract_ids,
                    'deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ])->toArray();
            $detail_attachment = $this->get_contract_attachments($detail_attachment);
        }
        // 待盖章合同
        $archive_ids = array_column($items,'archive_id');
        if (!empty($archive_ids)) {
            $archive_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted = :deleted:',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE,
                    'keys' => $archive_ids,
                    'deleted' => GlobalEnums::IS_NO_DELETED
                ]
            ])->toArray();
        }

        // 关联公司配置
        $contract_company = ContractCompanyModel::find()->toArray();
        $contract_company = array_column($contract_company, 'company_name', 'company_code');

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();
        //查部门名称
        $node_department_ids = array_column($items, 'node_department_id');
        $node_department_ids = array_values(array_unique($node_department_ids));
        $department_data = (new DepartmentRepository())->getDepartmentByIds($node_department_ids, 2);
        $contract_storage_type = ContractEnums::$contract_storage_type;

        foreach ($items as &$item) {
            $status = Enums::$contract_status[$item['status']] ?? '';
            $item['status_title'] = static::$t->_($status);

            // 直属分类信息
            $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
            $item['template_title'] = $contract_category_info['label'] ?? '';

            // 若直属分类是二级分类, 则需拼接一级分类
            $parent_template_id = $item['template_id'];
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $parent_template_id = $contract_category_info['ancestry_id'];
                $item['template_title'] = $contract_category_map[$parent_template_id]['label'] . '/' . $item['template_title'];
            }

            // 是否是销售合同
            $is_sell_contract = $parent_template_id == Enums::CONTRACT_TEMPLATE_SALES;

            $is_supply_chain = $is_sell_contract && $item['company_code'] == ContractEnums::CONTRACT_COMPANY_FLASH_SUPPLY_CHAIN_MANAGEMENT;
            $is_archive_done = $item['archive_status'] == ContractEnums::CONTRACT_ARCHIVE_STATUS_APPROVAL;
            $is_master = $item['is_master'];

            $payment_currency = GlobalEnums::$currency_item[$item['payment_currency']] ?? '';
            $item['payment_currency_title'] = static::$t->_($payment_currency);
            $item['real_amount'] = bcdiv($item['amount'], 1000, 2);
            $item['company_name'] = $contract_company[$item['company_code']] ?? '';
            $item['balance_days'] = $is_supply_chain ? $item['balance_days'] : ''; // 结算天数
            $item['pay_method'] = static::$t->_('contract_pay_method.'.$item['pay_method']); //结算方式
            $item['valuation_type'] = $is_supply_chain ? static::$t->_(Enums::$contract_valuation_type[$item['valuation_type']] ?? '') : '';// 计价方式
            $item['lang'] = static::$t->_(Enums::$contract_lang[$item['lang']] ?? '');
            $item['payment_currency'] = static::$t->_(GlobalEnums::$currency_item[$item['payment_currency']] ?? '');
            $item['is_master'] = static::$t->_(Enums::$contract_is_master[$is_master] ?? '');
            $item['discount'] = $is_supply_chain ? (!empty($item['discount']) ? $item['discount'] : '0.00') .'%' : ''; //折扣比例
            $item['company_relation_code'] = $contract_company[$item['company_relation_code']] ?? '';
            $item['archive_status_text'] = static::$t->_(ContractEnums::$contract_archive_status[$item['archive_status']] ?? '');
            // 	客户ID 是否授权范围内
            $item['sell_cno'] = $is_sell_contract ? $item['sell_cno'] : '';
            $item['quotation_no'] = $is_sell_contract ? $item['quotation_no'] : '';
            $item['balance_days'] = $is_sell_contract ? $item['balance_days'] : '';
            $item['configure_consumer_id'] = $is_sell_contract ? $item['configure_consumer_id'] : '';
            $item['in_scope'] = ($is_sell_contract && Enums::CONTRACT_IS_MASTER_NO == $is_master) ? ($item['in_scope'] ? static::$t->_('view_yes') : static::$t->_('view_no')) : '';
            $item['is_group_contract'] = $item['is_group_contract'] ? static::$t->_('view_yes') : static::$t->_('view_no');
            // 归档内容
            $item['a_create_id'] = $is_archive_done ? $item['a_create_id'] : '';
            $item['a_create_name'] = $is_archive_done ? $item['a_create_name'] : '';
            $item['a_filing_at'] = $is_archive_done ? $item['a_filing_at'] : '';
            // 合同内容
            $contract_file = isset($detail_attachment[$item['id']][Enums::OSS_BUCKET_TYPE_CONTRACT_FILE]) ?
                merge_attachments($detail_attachment[$item['id']][Enums::OSS_BUCKET_TYPE_CONTRACT_FILE],false,true) : [];
            $item['contract_file'] = $contract_file[$item['id']] ?? '';
            // 合同附件
            $contract_attachment = isset($detail_attachment[$item['id']][Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT]) ?
                merge_attachments($detail_attachment[$item['id']][Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT],false,true) : [];
            $item['contract_attachment'] = $contract_attachment[$item['id']] ?? '';
            // 待盖章合同
            $archive_file = !empty($archive_attachment) ? merge_attachments($archive_attachment,false,true) : [];
            $_archive_file_url = '';
            if (!empty($archive_file[$item['archive_id']])){
                $path = OssHelper::downloadFileHcm($archive_file[$item['archive_id']], $export_file_url_time_limit_second);
                $_archive_file_url = $path['file_url'] ?? '';
            }
            $item['archive_file'] = $_archive_file_url;
            // 合同负责人所在部门
            $item['create_department_text'] = $department_data[$item['node_department_id']]['name'] ?? '';
            $item['effective_date'] = is_null($item['effective_date']) ? '' : $item['effective_date'];
            $item['expiry_date'] = is_null($item['expiry_date']) ? '' : $item['expiry_date'];
            $item['contract_storage_type'] = static::$t->_($contract_storage_type[$item['contract_storage_type']]);

        }
        return $items;
    }

    /**
     * 任务：导出自定义条件的其他合同
     * 说明：含相关附件、审批日志
     *
     * @param $condition
     * @param int $uid
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function getListByTask($condition, $uid = 0)
    {
        parent::setLanguage($condition['lang'] ?? 'zh-CN');

        // 找指定部门下的员工
        $create_ids = $staff_item = [];
        if (!empty($condition['ancestry'])) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                's.staff_info_id',
                's.name AS staff_name',
                'd.id AS department_id',
                'd.name AS department_name',
            ]);
            $builder->from(['s' => HrStaffInfoModel::class]);
            $builder->leftJoin(SysDepartmentModel::class, 's.node_department_id = d.id', 'd');
            $builder->where('d.ancestry_v3 LIKE :ancestry: ', ['ancestry' => "{$condition['ancestry']}%"]);
            $staff_item = $builder->getQuery()->execute()->toArray();
            $staff_item = array_column($staff_item, null, 'staff_info_id');
            $create_ids = array_keys($staff_item);
        }

        $condition['uid'] = $uid;
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'distinct c.id',
            'c.cno',
            'c.cname',
            'c.status',
            'c.template_id',
            'c.payment_currency',
            'c.amount',
            'c.refuse_reason',
            'c.cancel_reason',
            'c.create_id',
            'c.create_name',
            'c.create_department',
            'c.created_at',
            'c.approved_at',
            'c.contract_file',
            'c.sell_cno',
            'c.company_code',
            'c.sell_cno',
            //'c.quotation_no',
            'c.pay_method',
            'c.balance_days',
            'c.valuation_type',
            'c.discount',
            'c.configure_consumer_id',
            'c.lang',
            'c.payment_currency',
            'c.amount',
            'c.is_master',
            'c.in_scope',
            'c.sub_cno',
            'c.is_group_contract',
            'c.company_relation_code',
            'c.contract_desc',
            'request.id AS wid',
        ]);
        $builder->from(['c' => Contract::class]);
        $builder->leftJoin(WorkflowRequestModel::class, 'request.biz_type in (1,20,21,22,23,24) AND request.biz_value=c.id', 'request');
        $builder = $this->getCondition($builder, $condition);
        $builder->andWhere('c.contract_type != :contract_type: ', ['contract_type' => 'store_renting_contract']);
        if (!empty($create_ids)) {
            $builder->inWhere('c.create_id', array_values($create_ids));
        }

        // 合同类型: 支持多个
        if (!empty($condition['template_id_item'])) {
            $builder->inWhere('c.template_id', $condition['template_id_item']);
        }

        $builder->orderBy('c.id desc');

        $items = $builder->getQuery()->execute()->toArray();

        if (empty($items)) {
            return [
                'code' => 0,
                'message' => '无符合条件的数据'
            ];
        }

        // 关联公司配置
        $contract_company = ContractCompanyModel::find()->toArray();
        $contract_company = array_column($contract_company, 'company_name', 'company_code');

        // 合同的归档信息
        $contract_nos = array_column($items, 'cno');
        $archive_item = [];
        if (!empty($contract_nos)) {
            $archive_item = ContractArchive::find([
                'conditions' => 'cno IN ({nos:array})',
                'bind' => ['nos' => $contract_nos],
                'columns' => ['id', 'cno', 'status', 'filing_id', 'filing_name', 'filing_at', 'holder_name']
            ])->toArray();
            $archive_item = array_column($archive_item, null, 'cno');
        }

        // 合同相关附件
        $contract_ids = array_column($items, 'id');

        // 正文合同
        $text_contract = $contract_attachment = [];
        if (!empty($contract_ids)) {
            $text_contract = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_CONTRACT_FILE,
                    'keys' => $contract_ids
                ]
            ])->toArray();
            $text_contract = merge_attachments($text_contract);


            // 合同附件
            $contract_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_CONTRACT_ATTACHMENT,
                    'keys' => $contract_ids
                ]
            ])->toArray();
            $contract_attachment = merge_attachments($contract_attachment);
        }


        // 盖章合同
        $archive_ids = array_column($archive_item, 'id');
        $stamp_attachment = [];
        if (!empty($archive_ids)) {
            $stamp_attachment = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_ARCHIVE_CONTRACT_FILE,
                    'keys' => $archive_ids
                ]
            ])->toArray();
            $stamp_attachment = merge_attachments($stamp_attachment);
        }

        // 意见征询/回复附件
        $request_ids = array_values(array_filter(array_unique(array_column($items, 'wid'))));
        $ask_reply_attachment = [];
        if (!empty($request_ids)) {
            $fyr_ids = WorkflowRequestNodeFYR::find([
                'conditions' => 'request_id IN ({request_ids:array})',
                'bind' => ['request_ids' => $request_ids],
                'columns' => ['id', 'request_id', 'action_type'],
                'order' => 'id ASC'
            ])->toArray();
            $fyr_ids = array_column($fyr_ids, 'request_id', 'id');

            if (!empty($fyr_ids)) {
                $consult_attachment = AttachModel::find([
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key IN ({keys:array}) AND deleted=0',
                    'bind' => [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_FRY_ATTACH,
                        'keys' => array_keys($fyr_ids)
                    ]
                ])->toArray();
                $consult_attachment = merge_attachments($consult_attachment);
                // 归并同一个工单的意见征询/回复附件
                foreach ($fyr_ids as $fyr_id => $request_id) {
                    if (empty($consult_attachment[$fyr_id])) {
                        continue;
                    }

                    if (isset($ask_reply_attachment[$request_id])) {
                        $ask_reply_attachment[$request_id] .= " ;\r\n" . $consult_attachment[$fyr_id];
                    } else {
                        $ask_reply_attachment[$request_id] = $consult_attachment[$fyr_id];
                    }
                }

                $consult_attachment = null;
            }
        }

        // 合同分类map
        $contract_category_map = EnumsService::getInstance()->getContractCategorysMap();


        // 找合同关联的报价单的KA ID
        // 关联的报价单
        $contract_customer_map = [];
        if (!empty($contract_nos)) {
            // 找关联的报价单
            $contract_quotation_list = ContractQuotationModel::find([
                'conditions' => 'cno IN ({cno:array})',
                'bind' => ['cno' => $contract_nos],
                'columns' => ['cno', 'group_concat(quotation_no) AS quotation_no'],
                'group' => 'cno'
            ])->toArray();

            $quoted_price_list_sn = explode(',', implode(',', array_column($contract_quotation_list, 'quotation_no')));

            $quotation_customer_list = [];
            if (!empty($quoted_price_list_sn)) {
                $quotation_customer_list = CrmQuotationApplyModel::find([
                    'conditions' => 'quoted_price_list_sn IN ({quoted_price_list_sn:array}) AND customer_type_category = 2',
                    'bind' => ['quoted_price_list_sn' => $quoted_price_list_sn],
                    'columns' => ['quoted_price_list_sn', 'customer_id'],
                ])->toArray();

                $quotation_customer_list = array_column($quotation_customer_list, 'customer_id', 'quoted_price_list_sn');
            }

            // 提炼合同关联的报价单的KAID
            foreach ($contract_quotation_list as $value) {
                $_quotation_no_item = explode(',', $value['quotation_no']);
                foreach ($_quotation_no_item as $_quotation_no) {
                    if (isset($quotation_customer_list[$_quotation_no])) {
                        $contract_customer_map[$value['cno']][] = $quotation_customer_list[$_quotation_no];
                    }
                }
            }
        }

        // 生成Excel 数据
        $excel_data = [];
        $workflow_service_v2_entity = new WorkflowServiceV2();
        foreach ($items as $item) {
            $status = Enums::$contract_status[$item['status']] ?? '';

            // 直属分类信息
            $contract_category_info = $contract_category_map[$item['template_id']] ?? [];
            $item['template_title'] = $contract_category_info['label'] ?? '';

            // 若直属分类是二级分类, 则需拼接一级分类
            $parent_template_id = $item['template_id'];
            if (isset($contract_category_info['level']) && $contract_category_info['level'] > 1) {
                $parent_template_id = $contract_category_info['ancestry_id'];
                $item['template_title'] = $contract_category_map[$parent_template_id]['label'] . '/' . $item['template_title'];
            }

            $payment_currency = GlobalEnums::$currency_item[$item['payment_currency']] ?? '';

            $is_master = Enums::$contract_is_master[$item['is_master']] ?? '';

            $archive_info = $archive_item[$item['cno']] ?? [];

            // 合同审批日志
            $request_model = WorkflowRequestModel::findFirst($item['wid']);
            $flow_logs = $workflow_service_v2_entity->getAuditLogs($request_model, true);

            // 格式: 操作行为 - 操作人 - 姓名 - 操作时间(审批时间或等待时长待审批时有)
            // 暂不处理子节点会签审批 与 审批人会签审批的展示情况
            $_wk_log = '';
            foreach ($flow_logs as $log) {
                $_staff_info = '';
                $_curr_log = $log['action_name'] . ' - ' . 'AUDIT_STAFF_INFO' . ' - ' . $log['audit_at'];

                // 待审批人为多个的情况
                if (!empty($log['list'])) {
                    foreach ($log['list'] as $sub_log) {
                        $_staff_info .= $sub_log['staff_id'].'['.$sub_log['staff_name'].']/';
                    }

                    $_staff_info = trim($_staff_info, ' /');

                } else {
                    // 审批人为一
                    $_staff_info = $log['staff_id'].'['.$log['staff_name'].']';

                }

                // TODO 意见征询日志
                if (!empty($log['fyr_list'])) {

                }

                $_wk_log .= str_replace('AUDIT_STAFF_INFO', $_staff_info, $_curr_log) . "\r\n";
            }

            // 仅当销售合同时, 如下字段取值
            // 销售合同编号/相关报价/结算方式/结算天数/计价方式/折扣比例/客户ID/是否授权范围内
            if ($parent_template_id != Enums::CONTRACT_TEMPLATE_SALES) {
                $item['sell_cno'] = '';
                $item['quotation_no'] = '';
                $item['balance_days'] = '';
                $item['configure_consumer_id'] = '';
                $pay_method = '';
                $valuation_type = '';
                $discount = '';
                $in_scope = '';
            } else {
                $pay_method = static::$t->_('contract_pay_method.'.$item['pay_method']); //结算方式
                $valuation_type = static::$t->_(Enums::$contract_valuation_type[$item['valuation_type']] ?? '');// 计价方式
                $discount = !empty($item['discount']) ? $item['discount'] .'%' : ''; //折扣比例
                $in_scope = $item['in_scope'] ? '是' : '否'; //是否授权范围内
            }

            $customer_id_list = $contract_customer_map[$item['cno']] ?? [];

            $excel_data[] = [
                $item['cname'], //合同名称
                $item['cno'], //合同编号
                $contract_company[$item['company_code']] ?? '', //合同所属公司
                $item['template_title'], //合同分类
                $item['sell_cno'], //销售合同编号
                //$item['quotation_no'], //相关报价单
                implode(',', $customer_id_list),//KA ID
                $pay_method, //结算方式
                $item['balance_days'], //结算天数
                $valuation_type, //计价方式
                $discount, //折扣比例
                $item['configure_consumer_id'], //客户ID
                $item['lang'], //合同语言
                static::$t->_($payment_currency), //付款币种
                bcdiv($item['amount'], 1000, 2), //合同金额
                static::$t->_($is_master), //合同主从属性
                $in_scope, //是否授权范围内
                $item['sub_cno'], //关联主合同编号
                $item['is_group_contract'] ? '是' : '否', //是否为集团公司间合同
                $contract_company[$item['company_relation_code']] ?? '', //合同关联公司
                $item['contract_desc'], //合同说明
                $item['create_department'], //申请人部门
                $item['create_id'], //申请人工号
                $item['create_name'], //申请人姓名
                $item['created_at'], //申请日期
                static::$t->_($status), //申请状态
                static::$t->_(ContractEnums::$contract_archive_status[$archive_info['status'] ?? ''] ?? ''), //归档状态
                $archive_info['filing_id'] ?? '', //归档人工号
                $archive_info['filing_name'] ?? '', //归档人姓名
                $archive_info['filing_at'] ?? '', //归档日期
                $archive_info['holder_name'] ?? '', //合同原件保管人姓名
                $text_contract[$item['id']] ?? '', //合同正文
                $contract_attachment[$item['id']] ?? '', //合同附件
                $stamp_attachment[$archive_info['id'] ?? ''] ?? '', //盖章合同
                $ask_reply_attachment[$item['wid']] ?? '', //意见征询/回复附件
                $_wk_log, //审批日志
            ];
        }

        // 上传Excel文件
        $header = [
            '合同名称',
            '合同编号',
            '合同所属公司',
            '合同分类',
            '销售合同编号',
            //'相关报价单',
            'KA ID',
            '结算方式',
            '结算天数',
            '计价方式',
            '折扣比例',
            '客户ID',
            '合同语言',
            '付款币种',
            '合同金额',
            '合同主从属性',
            '是否授权范围内',
            '关联主合同编号',
            '是否为集团公司间合同',
            '合同关联公司',
            '合同说明',
            '申请人部门',
            '申请人工号',
            '申请人姓名',
            '申请日期',
            '申请状态',
            '归档状态',
            '归档人工号',
            '归档人姓名',
            '归档日期',
            '合同原件保管人姓名',
            '合同正文',
            '合同附件',
            '盖章合同',
            '意见征询/回复附件',
            '审批日志明细',
        ];
        $file_name = "other_contract_file_" . date("YmdHis");
        return $this->exportExcel($header, $excel_data, $file_name);
    }

    /**
     * 导出excel数据
     * @param $items
     * @return array
     */
    private function _exportContract($items)
    {

        $new_data = [];
        foreach ($items as $key => $val) {

            $new_data[] = [
                $val['cname'],
                $val['cno'],
                $val['company_name'],
                $val['template_title'],
                is_null($val['effective_date']) ? '' : $val['effective_date'],
                is_null($val['expiry_date']) ? '' : $val['expiry_date'],
                $val['contract_storage_type'],
                $val['sell_cno'],
                $val['quotation_no'],
                $val['pay_method'],
                $val['balance_days'],
                $val['valuation_type'],
                $val['discount'],
                $val['configure_consumer_id'] ?? '',
                $val['lang'],
                $val['payment_currency'],
                $val['real_amount'],
                $val['is_master'],
                $val['in_scope'],
                $val['sub_cno'],
                $val['is_group_contract'],
                $val['company_relation_code'],
                $val['contract_desc'],
                $val['create_department'],
                $val['create_id'],
                $val['create_name'],
                $val['created_at'],
                $val['status_title'],
                $val['archive_status_text'],
                $val['a_create_id'],
                $val['a_create_name'],
                $val['a_filing_at'],
                $val['holder_name'],
                $val['contract_file'],
                $val['contract_attachment'],
                $val['archive_file'],
                $val['customer_company_name'],
                $val['create_department_text'],
            ];
        }

        $file_name = "contract_" . date("YmdHis");
        $header    = [
            static::$t->_('csr_field_contract_name'),//'合同名称',
            static::$t->_('csr_field_contract_id'),//'合同编号',
            static::$t->_('contract_export_field_company'),//'合同所属公司',
            static::$t->_('contract.template'), // 合同分类
            static::$t->_('contract_export_field_effected_date'), // 合同生效日期
            static::$t->_('contract_export_field_finished_date'), // 合同合同结束日期
            static::$t->_('contract.contract_storage_type'), //合同存储类型
            static::$t->_('contract.sales_contract_no'), // 销售合同编号
            static::$t->_('contract_export_field_quotation'), // 相关报价单
            static::$t->_('contract_export_field_pay_method'),
            static::$t->_('contract_export_field_pay_days'),
            static::$t->_('contract_export_field_valuation_type'),
            static::$t->_('contract_export_field_discount'),
            static::$t->_('contract_export_field_consumer_id'),
            static::$t->_('csr_field_contract_lang'),
            static::$t->_('csr_field_money_type'),
            static::$t->_('contract.amount'),
            static::$t->_('contract_export_field_is_master'),
            static::$t->_('contract_export_field_in_scope'),
            static::$t->_('contract_export_field_sub_cno'),
            static::$t->_('contract_export_field_is_group_contract'),
            static::$t->_('contract_export_field_company_relation_code'),
            static::$t->_('contract_export_field_contract_desc'),
            static::$t->_('csr_field_dept_name'),  //'申请人部门',
            static::$t->_('csr_field_manage_id'),//'申请人工号',
            static::$t->_('csr_field_staff_name'), //'申请人姓名',
            static::$t->_('csr_field_apply_at'),
            static::$t->_('csr_field_contract_status'),
            static::$t->_('csr_filed_archive_status'),
            static::$t->_('contract_export_field_archive_staff_id'),
            static::$t->_('contract_export_field_archive_staff_name'),
            static::$t->_('contract_export_field_archive_create_at'),
            static::$t->_('contract_export_field_holder_name'),
            static::$t->_('contract_export_field_contract_file'),
            static::$t->_('contract_export_field_contract_attachment'),
            static::$t->_('contract_export_field_archive_file'),
            static::$t->_('contract_export_field_customer_company_name'),
            static::$t->_('contract_export_field_create_department_text'),
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 按照合同纬度组装合同附件数据
     * @param $attachments
     * @return array
     */
    private function get_contract_attachments($attachments = []){

        $export_file_url_time_limit_second = (int)EnumsService::getInstance()->getSettingEnvValue('export_file_url_time_limit_second', 600);

        $result = [];
        foreach ($attachments as $item) {
            if (empty($item['oss_bucket_key']) || empty($item['oss_bucket_type'])) {
                continue;
            }
            $path = !empty($item['object_key']) ? OssHelper::downloadFileHcm($item['object_key'], $export_file_url_time_limit_second) : [];
            $item['object_key'] = $path['file_url'] ?? '';
            $result[$item['oss_bucket_key']][$item['oss_bucket_type']][] = $item;
        }

        return $result;
    }
}
