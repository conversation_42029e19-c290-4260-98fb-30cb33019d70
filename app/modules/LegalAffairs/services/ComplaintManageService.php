<?php

namespace App\Modules\LegalAffairs\Services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\ContractCompanyModel;

class ComplaintManageService extends BaseService
{
    private static $instance;

    /**
     * 构造函数
     * ActivityService constructor.
     */

    private function __construct()
    {
    }

    private function __clone()
    {

    }

    /**
     * 类实例
     * @return ComplaintManageService
     */
    public static function getInstance(): ComplaintManageService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取公司信息
     * @param int $is_all
     * @return mixed
     */
    public function getCompanyList($is_all = true)
    {
        $delete = [GlobalEnums::IS_NO_DELETED];
        if ($is_all) {
            $delete = [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED];
        }
        return ContractCompanyModel::find([
            'conditions' => 'is_delete in ({delete:array}) ',
            'bind'       => ['delete' => $delete],
            'columns'    => ['id', 'company_name', 'is_delete'],
        ])->toArray();
    }

    /**
     * 统一的业务调用入口
     * @throws ValidationException
     */
    public function fire($action, $params)
    {
        $ret = new ApiClient('ard_api_svc', '', $action, static::$language);
        $ret->setParams([$params]);
        $res = $ret->execute();
        if (isset($res['error'])) {
            throw new ValidationException($res['error']);
        }
        return $res['result'];
    }


}