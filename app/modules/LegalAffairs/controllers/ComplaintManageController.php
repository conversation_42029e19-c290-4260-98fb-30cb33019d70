<?php

namespace App\Modules\LegalAffairs\Controllers;

use App\Library\Validation\ValidationException;
use App\Modules\LegalAffairs\Services\ComplaintManageService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 诉讼纠纷管理
 */
class ComplaintManageController extends BaseController
{

    public function initialize()
    {
        parent::initialize();
    }

    /**
     * @Permission(action='complaint-manage')
     * 接口统一入口
     * @param $name
     * @param $args
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function __call($name, $args)
    {
        $params                            = trim_array($this->request->get());
        $params['operate_staff_info_id']   = $this->user['id'];
        $params['operate_staff_info_name'] = $this->user['name'];

        $svc_method = sprintf('complaintmanage.%s', convert_camel_to_underscore(substr($name, 0, -6)));
        $res        = ComplaintManageService::getInstance()->fire($svc_method, $params);
        return $this->returnJson($res['code'], $res['msg'], $res['data']);
    }

}