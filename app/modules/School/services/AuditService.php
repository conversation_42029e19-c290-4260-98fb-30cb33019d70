<?php

namespace App\Modules\School\Services;

use App\Library\ApiClient;
use App\Library\BaseService;

class AuditService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取培训系统-学习计划-待审批数量
     * @param $user
     * @return int
     * @date 2022/4/7
     */
    public function getAuditCount($user){
        $ac = new ApiClient('school', '', 'learning_plan_audit_number',static::$language);
        $api_params = [[],$user];
        $ac->setParams($api_params);
        $result = $ac->execute();
        if (isset($result['result']['data']) && is_numeric($result['result']['data'])){
            return (int)$result['result']['data'];
        }
        return 0;
    }

}