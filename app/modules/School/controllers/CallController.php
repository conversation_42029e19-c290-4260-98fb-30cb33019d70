<?php
namespace App\Modules\School\Controllers;


use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\BaseController as Controller;


class CallController extends Controller
{
// @Permission(action='school.')
    /**
     * 培训系统接口调用
     *
     * @param $school_controller
     * @param $school_action
     * @return mixed
     * @Token
     */
    public function entranceAction($school_controller,$school_action)
    {
        $params = $this->request->get();
        $ac = new ApiClient('school', '', $school_controller.'_'.$school_action,$this->locale);
        $api_params = [$params,$this->user];
        $ac->setParams($api_params);
        $result = $ac->execute();
        if (isset($result['result'])){
            return $this->returnJson($result['result']['code'],$result['result']['message'],$result['result']['data'] );
        }elseif (isset($result['error'])){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR,'error',null );
        }
    }

}