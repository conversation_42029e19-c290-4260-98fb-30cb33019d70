<?php

namespace App\Modules\FundsManagement\Services;

use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Vendor\Services\DetailService as VendorDetailService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\BankAccountRepository;
use App\Repository\oa\BankCompanyRepository;
use App\Repository\oa\BankFlowRepository;
use App\Repository\oa\BankListRepository;
use App\Repository\oa\ChequeAccountRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class BankAccountService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static $operation_type_add    = 1;
    public static $operation_type_save   = 2;
    public static $operation_type_delete = 3;
    //列表-非必填
    public static $not_must_list = [
        'bank_id',
        'company_id',
        'account',
    ];

    //列表
    public static $validate_list = [
        'pageSize'   => 'IntGt:0',   //每页条数
        'pageNum'    => 'IntGt:0',   //页码
        'bank_id'    => 'IntGt:0',   //银行id
        'company_id' => 'IntGt:0',   //公司id
        'account'    => 'StrLenGe:4',//账号
    ];

    // 添加 和 更新的公共校验参数
    public static $validate_save_common = [
        'company_id'                  => 'Required|IntGt:0',                    //公司id
        'currency'                    => 'Required|IntGt:0',                    //公司id
        'left_amount'                 => 'Required|FloatGeLe:0,************.99',//余额
        'left_amount_date'            => 'Required|Date',                       //余额日期,
        'account_manager_id'          => 'Required|StrLenGeLe:0,10',
        'business_line'               => 'Required|StrLenGeLe:0,128',
        'company_registered_region'   => 'Required|IntGe:0',
        'company_registered_address'  => 'Required|StrLenGeLe:0,500',
        'bank_full_name'              => 'Required|StrLenGeLe:0,500',
        'current_interest_rate'       => 'Required|StrLenGeLe:0,10',
        'account_purpose'             => 'Required|StrLenGeLe:0,100',
        'bank_region'                 => 'Required|IntGe:0',
        'bank_address'                => 'Required|StrLenGeLe:0,500',
        'branch_bank_name'            => 'Required|StrLenGeLe:0,500',
        'bank_international_code'     => 'Required|StrLenGeLe:0,50',
        'account_open_date'           => 'Required|Date',// 开户日期 必填
        'authorized_person_signature' => 'Required|StrLenGeLe:0,200',
        'account_status'              => 'Required|IntIn:1,2',// 账户状态 必填
        'account_attributes'          => 'Required|IntGe:0',
        'client_manager'              => 'Required|StrLenGeLe:0,500',
        'contact_number'              => 'Required|StrLenGeLe:0,100',
        'contact_email'               => 'Required|StrLenGeLe:0,500',
        'remark'                      => 'Required|StrLenGeLe:0,500',
    ];

    // 添加
    public static $validate_add = [
        'bank_id' => 'Required|IntGt:0',        //银行id
        'account' => 'Required|StrLenGeLe:1,50',//账号
    ];

    //编辑
    public static $validate_save = [
        'id' => 'Required|IntGt:0', //id
    ];

    //删除
    public static $validate_delete = [
        'id' => 'Required|IntGt:0', //id
    ];

    /**
     * @return BankAccountService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取枚举
     *
     * @return array
     */
    public function getDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [
            'bank_items'     => [],
            'company_items'  => [],
            'currency_items' => [],
        ];
        try {
            $enums_arr = [];
            // 银行列表
            $bank_list               = BankListRepository::getInstance()->getBankList();
            $enums_arr['bank_items'] = array_column($bank_list, null, 'id');

            // 公司列表
            $company_list               = BankCompanyRepository::getInstance()->getBankCompanyList();
            $enums_arr['company_items'] = array_column($company_list, null, 'id');

            // 币种
            GlobalEnums::init();
            $enums_arr['currency_items'] = GlobalEnums::$currency_symbol_map;

            // 业务线
            $enums_arr['business_line'] = EnumsService::getInstance()->getSettingEnvValueIds('bank_account_business_line');

            // 国家/地区
            $enums_arr['country_region_items'] = VendorDetailService::getInstance()->getCountryReginItem(-1);

            // 账户状态
            $enums_arr['account_status_items'] = BankFlowEnums::$account_status_item;

            // 账户属性
            $runtime_env                           = get_runtime_env();
            $enums_arr['account_attributes_items'] = EnumsService::getInstance()->getSettingEnvValueMap('bank_account_attributes_enums');
            $account_attributes_prefix             = $runtime_env . '_fund_bank_account_attributes_';

            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k => $v) {
                    $tmp = [];

                    $tmp['value'] = (string)$k;
                    switch ($key) {
                        case 'bank_items':
                            $tmp['label']      = $v['bank_name'];
                            $tmp['is_deleted'] = $v['is_deleted'];
                            break;
                        case 'company_items':
                            $tmp['label']      = $v['company_name'];
                            $tmp['code']       = $v['company_code'];
                            $tmp['is_deleted'] = $v['is_deleted'];
                            break;
                        case 'business_line':
                            $tmp['value'] = $v;
                            $tmp['label'] = $v;
                            break;
                        case 'country_region_items':
                            $tmp['label']      = static::$t->_($v['translation_code']);
                            $tmp['is_deleted'] = $v['is_del'];
                            break;
                        case 'account_status_items':
                            $tmp['label'] = static::$t->_($v);
                            break;
                        case 'account_attributes_items':
                            $tmp['label'] = static::$t->_($account_attributes_prefix . $k);
                            break;
                        default:
                            $tmp['label'] = $v;
                            break;
                    }

                    $data[$key][] = $tmp;
                }
            }
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('银行账号配置-获取枚举失败: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 银行账号配置-列表
     * @param $params
     * @param bool $export 是否导出
     * @return array
     * @date 2023/3/2
     */
    public function getList($params, $export = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => 0,
                'per_page'     => 0,
                'total_count'  => 0,
            ],
        ];
        try {
            $page_size                          = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num                           = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset                             = $page_size * ($page_num - 1);
            $data['pagination']['current_page'] = $page_num;
            $data['pagination']['per_page']     = $page_size;
            //查询总数
            $count = $this->getListCount($params);
            //查询列表
            if ($count > 0) {
                $columns = $export ? '*' : 'id, bank_id, company_id, company_name, account, currency, left_amount, left_amount_date';

                $builder = $this->modelsManager->createBuilder();
                $builder->columns($columns);
                $builder->from(BankAccountModel::class);

                //组合搜索条件
                $builder = $this->getCondition($builder, $params);
                if (!$export) {
                    $builder->limit($page_size, $offset);
                }
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $export);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-bank-account-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 银行账号列表-总数
     * @param $condition
     * @return int
     */
    public function getListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(BankAccountModel::class);
        $builder->columns('count(id) as count');
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 银行账号列表-条件拼接
     * @param $builder
     * @param $condition
     * @return mixed
     */
    public function getCondition($builder, $condition)
    {
        $bank_id    = !empty($condition['bank_id']) ? $condition['bank_id'] : 0;      //银行id
        $company_id = !empty($condition['company_id']) ? $condition['company_id'] : 0;//公司id
        $account    = !empty($condition['account']) ? $condition['account'] : '';     //账号

        $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //银行id
        if (!empty($bank_id)) {
            $builder->andWhere('bank_id = :bank_id:', ['bank_id' => $bank_id]);
        }
        //公司id
        if (!empty($company_id)) {
            $builder->andWhere('company_id = :company_id:', ['company_id' => $company_id]);
        }
        //账号
        if (!empty($account)) {
            $builder->andWhere('account like :account:', ['account' => '%' . $account . '%']);
        }

        return $builder;
    }

    /**
     * 银行账号列表-列表数据处理
     * @param $items
     * @param bool $export
     * @return array
     * @date 2023/3/2
     */
    private function handleListItems($items, $export = false)
    {
        if (empty($items)) {
            return [];
        }

        //银行id
        $bank_ids = array_values(array_unique(array_column($items, 'bank_id')));

        //公司id
        $company_ids = array_values(array_unique(array_column($items, 'company_id')));

        //查银行
        $bank_list = BankListRepository::getInstance()->getBankList($bank_ids);
        $bank_list = array_column($bank_list, 'bank_name', 'id');

        //查公司
        $company_list = BankCompanyRepository::getInstance()->getBankCompanyList($company_ids);
        $company_list = array_column($company_list, null, 'id');

        //币种
        GlobalEnums::init();
        if ($export) {
            // 账户负责人姓名
            $account_manager_ids = array_unique(array_filter(array_column($items, 'account_manager_id')));
            $account_manager_ids = (new HrStaffRepository())->getStaffListByStaffIds(array_values($account_manager_ids));
            $account_manager_ids = array_column($account_manager_ids, 'name', 'staff_info_id');

            // 枚举配置
            $enums = $this->getDefault();

            // 国家/地区
            $country_region_items = $enums['data']['country_region_items'];
            $country_region_items = array_column($country_region_items, 'label', 'value');

            // 账户状态
            $account_status_items = $enums['data']['account_status_items'];
            $account_status_items = array_column($account_status_items, 'label', 'value');

            // 账户属性
            $account_attributes_items = $enums['data']['account_attributes_items'];
            $account_attributes_items = array_column($account_attributes_items, 'label', 'value');

            $data = [];
            foreach ($items as $item) {
                $company_info = $company_list[$item['company_id']] ?? [];

                $data[] = [
                    $item['bank_full_name'],
                    $bank_list[$item['bank_id']] ?? '',
                    $item['account'],
                    $company_info['company_name'] ?? '',
                    GlobalEnums::$currency_symbol_map[$item['currency']] ?? '',
                    $item['left_amount_date'],
                    $item['left_amount'],

                    get_name_and_nick_name($account_manager_ids[$item['account_manager_id']] ?? '',
                        $item['account_manager_id']),
                    $company_info['company_code'] ?? '',
                    $item['business_line'],
                    $country_region_items[$item['company_registered_region']] ?? '',// 公司注册地
                    $item['company_registered_address'],
                    $item['current_interest_rate'],
                    $item['account_purpose'],
                    $country_region_items[$item['bank_region']] ?? '',// 开户银行地区
                    $item['bank_address'],
                    $item['branch_bank_name'],
                    $item['bank_international_code'],
                    $item['account_open_date'] ?? '',
                    $item['authorized_person_signature'],
                    $account_status_items[$item['account_status']] ?? '',
                    $account_attributes_items[$item['account_attributes']] ?? '',
                    $item['client_manager'],
                    $item['contact_number'],
                    $item['contact_email'],
                    $item['remark'],
                ];
            }

            return $data;
        } else {
            foreach ($items as &$item) {
                //币种
                $item['currency_text'] = GlobalEnums::$currency_symbol_map[$item['currency']] ?? '';
                //银行名称
                $item['bank_name'] = $bank_list[$item['bank_id']] ?? '';
                //公司名称
                $item['company_name'] = $company_list[$item['company_id']]['company_name'] ?? '';
            }

            return $items;
        }
    }


    /**
     * 添加
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function add($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $this->logger->info(['fund_bank_account_add_params' => ['params' => $data, 'user' => $user]]);

            //查询公司名称
            $bank_company = BankCompanyRepository::getInstance()->getBankCompanyById($data['company_id']);
            if (empty($bank_company) || $bank_company['is_deleted'] == GlobalEnums::IS_DELETED) {
                throw new ValidationException(self::$t->_('bank_account_company_id_not_exist'),
                    ErrCode::$VALIDATE_ERROR);
            }

            //查询银行是否被删除
            $bank_info = BankListRepository::getInstance()->getBankById($data['bank_id']);
            if (empty($bank_info) || $bank_info['is_deleted'] == GlobalEnums::IS_DELETED) {
                throw new ValidationException(self::$t->_('bank_account_bank_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $data['company_name'] = $bank_company['company_name'] ?? '';

            //查询银行账号是否存在
            $model = BankAccountRepository::getInstance()->getBankAccount(['account' => $data['account']]);

            //如果存在
            if (!empty($model)) {
                //未删除, 报异常
                if ($model->is_deleted == GlobalEnums::IS_NO_DELETED) {
                    throw new ValidationException(self::$t->_('bank_account_add_exist'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                // 如果不存在,直接添加
                $model = new BankAccountModel();
            }

            $model = $this->setModel($model, $data, $user, self::$operation_type_add);
            if ($model->save() === false) {
                throw new BusinessException('银行账号添加失败, 可能的原因是:' . get_data_object_error_msg($model),
                    ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info(['fund_bank_account_add_result' => $model->toArray()]);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = ErrCode::$BUSINESS_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('bank_account_add_error:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 编辑
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function save($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $this->logger->info(['fund_bank_account_edit_params' => ['params' => $data, 'user' => $user]]);

            //查询银行账号是否存在
            $bank_account = BankAccountRepository::getInstance()->getBankAccount([
                'id'         => $data['id'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ]);
            if (!$bank_account) {
                throw new ValidationException(self::$t->_('bank_account_save_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            //查询公司名称
            $bank_company = BankCompanyRepository::getInstance()->getBankCompanyById($data['company_id']);
            if (empty($bank_company) || $bank_company['is_deleted'] == GlobalEnums::IS_DELETED) {
                throw new ValidationException(self::$t->_('bank_account_company_id_not_exist'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info(['fund_bank_account_before_data' => $bank_account->toArray()]);

            $data['company_name'] = $bank_company['company_name'] ?? '';
            $bank_account         = $this->setModel($bank_account, $data, $user, self::$operation_type_save);
            if ($bank_account->save() === false) {
                throw new BusinessException('银行账号编辑失败 可能的原因是:' . get_data_object_error_msg($bank_account),
                    ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info(['fund_bank_account_after_data' => $bank_account->toArray()]);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = ErrCode::$BUSINESS_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('bank_account_save_error:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 获取详情
     *
     * @param string $account
     * @return array
     */
    public function getDetail(string $account)
    {
        $code    = ErrCode::$SUCCESS;
        $message = static::$t->_('success');
        $data    = [];

        try {
            //查询银行账号是否存在
            $bank_account = BankAccountRepository::getInstance()->getBankAccount([
                'account'    => $account,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ]);
            if (empty($bank_account)) {
                throw new ValidationException(self::$t->_('bank_account_id_find_error'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $bank_account->toArray();

            // 账户负责人姓名
            $data['account_manager_name'] = (new HrStaffRepository())->getStaffById($data['account_manager_id'])['name'] ?? '';

            // 查询公司代码
            $bank_company         = BankCompanyRepository::getInstance()->getBankCompanyById($bank_account->company_id);
            $data['company_code'] = $bank_company['company_code'] ?? '';

            // 默认值为0的 置为 ''
            $data['company_registered_region'] = $data['company_registered_region'] ? $data['company_registered_region'] : '';
            $data['bank_region']               = $data['bank_region'] ? $data['bank_region'] : '';
            $data['account_status']            = $data['account_status'] ? $data['account_status'] : '';
            $data['account_attributes']        = $data['account_attributes'] ? $data['account_attributes'] : '';

            // 剃掉用不到的字段
            unset($data['is_deleted'], $data['created_at'], $data['updated_at'], $data['created_id'], $data['updated_id']);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('bank_account_get_detail_error:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 删除
     *
     * @param $data
     * @param $user
     * @return array
     */
    public function delete($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            //查询银行账号是否存在
            $bank_account = BankAccountRepository::getInstance()->getBankAccount([
                'id'         => $data['id'],
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ]);
            if (!$bank_account) {
                throw new ValidationException(self::$t->_('bank_account_delete_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //账号是否关联过流水
            $exist_flow = BankFlowRepository::getInstance()->getBankFlowByAccountId($data['id']);
            if ($exist_flow) {
                throw new ValidationException(self::$t->_('bank_account_delete_flow_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //账号是否关联过支票
            $exist_cheque = ChequeAccountRepository::getInstance()->getChequeByAccount($bank_account->account);
            if ($exist_cheque) {
                throw new ValidationException(self::$t->_('bank_account_delete_cheque_exist'),
                    ErrCode::$VALIDATE_ERROR);
            }
            $this->setModel($bank_account, $data, $user, self::$operation_type_delete);
            if ($bank_account->save() == false) {
                throw new BusinessException('银行账号删除失败, 可能的原因是:' . get_data_object_error_msg($bank_account),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = ErrCode::$BUSINESS_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('bank_account_delete_error:' . $real_message);
        }
        $this->logger->info('bank_account_delete_info: 员工id=' . $user['id'] . ' 删除银行账号, 参数是=' . json_encode($data,
                JSON_UNESCAPED_UNICODE));
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 银行账号赋值
     *
     * @param object $model
     * @param $data
     * @param $user
     * @param $operation_type
     * @date 2023/8/30
     * @return object
     */
    public function setModel(object $model, $data, $user, $operation_type)
    {
        $now = date('Y-m-d H:i:s');
        if ($operation_type == self::$operation_type_add) {
            $model->bank_id    = $data['bank_id'];
            $model->account    = $data['account'];
            $model->created_at = $now;
            $model->created_id = $user['id'];
        } elseif ($operation_type == self::$operation_type_save) {
        } elseif ($operation_type == self::$operation_type_delete) {
            $model->is_deleted = GlobalEnums::IS_DELETED;
        }

        $model->updated_at = $now;
        $model->updated_id = $user['id'];

        // 添加 和 更新的公共字段
        if (in_array($operation_type, [self::$operation_type_add, self::$operation_type_save])) {
            $model->company_id       = $data['company_id'];
            $model->company_name     = $data['company_name'];
            $model->currency         = $data['currency'];
            $model->left_amount_date = $data['left_amount_date'];
            $model->left_amount      = $data['left_amount'];
            $model->is_deleted       = GlobalEnums::IS_NO_DELETED;

            $model->account_manager_id          = $data['account_manager_id'];
            $model->business_line               = $data['business_line'];
            $model->company_registered_region   = $data['company_registered_region'];
            $model->company_registered_address  = $data['company_registered_address'];
            $model->bank_full_name              = $data['bank_full_name'];
            $model->current_interest_rate       = $data['current_interest_rate'];
            $model->account_purpose             = $data['account_purpose'];
            $model->bank_region                 = $data['bank_region'];
            $model->bank_address                = $data['bank_address'];
            $model->branch_bank_name            = $data['branch_bank_name'];
            $model->bank_international_code     = $data['bank_international_code'];
            $model->account_open_date           = $data['account_open_date'];
            $model->authorized_person_signature = $data['authorized_person_signature'];
            $model->account_status              = $data['account_status'];
            $model->account_attributes          = $data['account_attributes'];
            $model->client_manager              = $data['client_manager'];
            $model->contact_number              = $data['contact_number'];
            $model->contact_email               = $data['contact_email'];
            $model->remark                      = $data['remark'];
        }

        return $model;
    }

    /**
     * 银行账号配置-导出
     * @param array $condition 设置参数
     * @return array
     * @throws GuzzleException
     */
    public function export(array $condition)
    {
        try {
            ini_set('memory_limit', '512M');

            $list       = $this->getList($condition, true);
            $row_values = $list['data']['items'];
            $row_values = array_map('array_values', $row_values);
            $file_name  = 'bank_account_' . date('YmdHis') . '.xlsx';
            $header     = [
                static::$t->_('bank_account_export_bank_full_name'),  //银行全称
                static::$t->_('bank_account_export.bank'),            //所属银行
                static::$t->_('bank_account_export.account'),         //银行账号
                static::$t->_('bank_account_export.company'),         //所属公司
                static::$t->_('bank_account_export.currency'),        //币种
                static::$t->_('bank_account_export.left_amount_date'),//余额日期
                static::$t->_('bank_account_export.left_amount'),     //账户余额

                static::$t->_('bank_account_export_account_manager_id'),         //账户负责人
                static::$t->_('bank_account_export_company_code'),               //公司代码
                static::$t->_('bank_account_export_business_line'),              //业务线
                static::$t->_('bank_account_export_company_registered_region'),  //公司注册地
                static::$t->_('bank_account_export_company_registered_address'), //注册地址
                static::$t->_('bank_account_export_current_interest_rate'),      //活期利率
                static::$t->_('bank_account_export_account_purpose'),            //账户用途
                static::$t->_('bank_account_export_bank_region'),                //开户银行地区
                static::$t->_('bank_account_export_bank_address'),               //开户银行地址
                static::$t->_('bank_account_export_branch_bank_name'),           //支行名称
                static::$t->_('bank_account_export_bank_international_code'),    //银行国际代码
                static::$t->_('bank_account_export_account_open_date'),          //开户时间
                static::$t->_('bank_account_export_authorized_person_signature'),//授权签字人
                static::$t->_('bank_account_export_account_status'),             //账户状态
                static::$t->_('bank_account_export_account_attributes'),         //账户属性
                static::$t->_('bank_account_export_client_manager'),             //客户经理
                static::$t->_('bank_account_export_contact_number'),             //联系电话
                static::$t->_('bank_account_export_contact_email'),              //联系邮件
                static::$t->_('bank_account_export_remark'),                     //备注
            ];

            return $this->exportExcel($header, $row_values, $file_name);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('bank_account_export_error:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }
}
