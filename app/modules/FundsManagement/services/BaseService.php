<?php

namespace App\Modules\FundsManagement\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;

class BaseService extends \App\Library\BaseService
{

    /**
     * 数据权限验证
     *
     * @param array $user
     * @return bool
     * @throws ValidationException
     */
    public function checkStaffDataPermissions(array $user)
    {
        $country_code = get_country_code();
        if (!in_array($country_code,
            [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            return true;
        }

        $staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('funds_management_data_permissions_staffs');
        if (!in_array($user['id'], $staff_ids)) {
            throw new ValidationException(static::$t->_('no_data_permission_error'), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

}
