<?php

namespace App\Modules\FundsManagement\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\FundsManagement\Services\BankAccountService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class BankAccountController extends BaseController
{
    /**
     * 银行账号配置-枚举
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75667
     */
    public function getDefaultAction()
    {
        $res = BankAccountService::getInstance()->getDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-列表
     * @Permission(action='funds_management.bank_account.list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75647
     */
    public function listAction()
    {
        // 数据权限校验
        BankAccountService::getInstance()->checkStaffDataPermissions($this->user);

        $params = $this->request->get();
        $params = params_filter($params, BankAccountService::$not_must_list);
        Validation::validate($params, BankAccountService::$validate_list);

        $res = BankAccountService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-添加
     * @Permission(action='funds_management.bank_account.add')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75637
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());

        $params['company_registered_region'] = $params['company_registered_region'] ?: 0;
        $params['bank_region']               = $params['bank_region'] ?: 0;
        $params['account_attributes']        = $params['account_attributes'] ?: 0;

        $validation = array_merge(BankAccountService::$validate_add, BankAccountService::$validate_save_common);
        Validation::validate($params, $validation);
        $res = BankAccountService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-编辑
     * @Permission(action='funds_management.bank_account.save')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75642
     */
    public function saveAction()
    {
        $params = trim_array($this->request->get());

        $params['company_registered_region'] = $params['company_registered_region'] ?: 0;
        $params['bank_region']               = $params['bank_region'] ?: 0;
        $params['account_attributes']        = $params['account_attributes'] ?: 0;

        $validation = array_merge(BankAccountService::$validate_save, BankAccountService::$validate_save_common);
        Validation::validate($params, $validation);
        $res = BankAccountService::getInstance()->save($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-删除
     * @Permission(action='funds_management.bank_account.delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75652
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BankAccountService::$validate_delete);
        $res = BankAccountService::getInstance()->delete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-详情
     * @Permission(menu='funds_management.bank_account')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87767
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['account' => 'Required|StrLenGeLe:1,50']);
        $res = BankAccountService::getInstance()->getDetail($params['account']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行账号配置-导出
     * @Permission(action='funds_management.bank_account.export')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75662
     */
    public function exportAction()
    {
        // 数据权限校验
        BankAccountService::getInstance()->checkStaffDataPermissions($this->user);

        $params = $this->request->get();
        $params = params_filter($params, BankAccountService::$not_must_list);
        Validation::validate($params, BankAccountService::$validate_list);

        $lock_key = md5(RedisKey::FUNDS_BANK_ACCOUNT_EXPORT . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BankAccountService::getInstance()->export($params);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }
}
