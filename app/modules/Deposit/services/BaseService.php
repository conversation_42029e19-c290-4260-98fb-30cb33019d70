<?php

namespace App\Modules\Deposit\Services;

use App\Library\Enums\DepositEnums;
use App\Modules\Store\Models\SysStore;


class BaseService extends \App\Library\BaseService
{
    //归还列表
    public static $validate_deposit_list = [
        'type'              => 'Required|IntIn:1,2,3,4|>>>: type  error',
        'apply_no'          => 'StrLenGeLe:5,30|>>>:apply no error',           //编号
        'create_name'       => 'StrLenGe:1|>>>:create_name error',             //押金负责人
        'create_company_id' => 'StrLenGeLe:0,10|>>>: create_company_id  error',//费用所属网点类型：1：总部，2：网点
        'contract_no'       => 'StrLenGeLe:1,30|>>>:contract_no no error',     //合同编号
        'sta_date'          => 'Date|>>>:sta_date error',                      //申请日期
        'end_date'          => 'Date|>>>:end end_date error',                  //申请日期
        'sta_return_date'   => 'Date|>>>:sta_return_date error',               //归还日期
        'end_return_date'   => 'Date|>>>:end_return_date error',               //归还日期
        'return_status'     => 'IntIn:1,2,3,4,5|>>>: return_status_id  error', //归还状态
        'pageNum'           => 'IntGe:1|>>>:page error',                       //当前页码
        'pageSize'          => 'IntGe:1|>>>:page_size error',                  //每页条数
    ];

    //归还查看
    public static $validate_deposit_view = [
        'type' => 'Required|IntIn:1,2,3,4|>>>: type  error',
        'id'   => 'Required|IntGe:1|>>>:id error',
    ];

    //归还添加
    public static $validate_return_add = [
        'id'                                   => 'Required|IntGe:1|>>>:id error',
        'apply_id'                             => 'Required|IntGt:1|>>>:apply_id error',
        'return_money'                         => 'Required|FloatGeLe:0.00,**********.00|>>>:return_money error',
        'apply_no'                             => 'StrLenGeLe:12,20|>>>:apply no error',
        //编号
        'bank_flow_date'                       => 'Required|Date|>>>:bank_flow_date error',
        //银行流水日期
        'loss_money'                           => 'FloatGeLe:0,**********|>>>:loss_money id error',
        'return_info'                          => 'Required|StrLenLe:1000|>>>:plan_remark error',
        //归还说明
        'other_return_money'                   => 'Required|FloatGeLe:0,********.99|>>>:other_return_money error',
        // 其他退回金额
        'other_return_info'                    => 'IfStrGt:other_return_money,0.00|Required|StrLenGeLe:1,1000|>>>:other_return_info error',
        // 其他退款金额说明
        'attachment'                           => 'Arr|ArrLenGeLe:0,10|>>>:main attachment list  error[Upload up to 10]',
        //附件列表，必填
        'deposit_loss[*]'                      => 'Obj',
        'deposit_loss[*].loss_money'           => 'IfIntGt:loss_money,0|Required|FloatGeLe:0.00,**********|>>>:loss_money error',
        //损失金额
        'deposit_loss[*].loss_bear_id'         => 'IfIntGt:loss_money,0|Required|IntGe:1|>>>:loss_bear_id  error',
        //损失承担方id
        'deposit_loss[*].loss_department_id'   => 'IfIntGt:loss_money,0|Required|IntGe:1|>>>:loss_department_id error',
        //损失部门id
        'deposit_loss[*].loss_organization_id' => 'IfIntGt:loss_money,0|Required|IntGe:1|>>>:loss_organization_id error',
        //损失机构id
        'deposit_loss[*].loss_budget_id'       => 'IfIntGt:loss_money,0|Required|IntGe:1|>>>:loss_budget_id error',
        //损失类型id
    ];

    //编辑
    public static $validate_deposit_edit = [
        'id'               => 'Required|IntGe:1|>>>:id error',
        'type'             => 'Required|IntIn:1,2,3,4|>>>: type  error',
        'edit_type'        => 'Required|IntIn:1,2|>>>: edit_type  error',
        'contract_no'      => 'IfIntEq:edit_type,1|Required|StrLenGeLe:1,30|>>>:contract_no no error',
        'return_status_id' => 'IfIntEq:edit_type,2|Required|IntIn:1,2,3,4,5|>>>: return_status_id  error',    //归还状态
        'attachment'       => 'Required|Arr|ArrLenGeLe:0,10|>>>:main attachment list  error[Upload up to 10]',//附件列表
    ];

    //转交
    public static $validate_deposit_forward = [
        'id'           => 'Required|IntGe:1|>>>:id error',
        'type'         => 'Required|IntIn:1,2,3,4|>>>: type  error',
        'edit_type'    => 'Required|IntIn:1,2,3|>>>: edit_type  error',
        'new_apply_id' => 'Required|IntGt:1|>>>:new_apply_id error',
        'apply_name'   => 'StrLenGeLe:0,255|>>>:apply_name  error',//新责任人姓名当edit_type=3  必填 调后端接口获取
    ];

    //根据填写的用户id返name
    public static $validate_get_apply_id_by_name = [
        'id'   => 'Required|StrLenGeLe:5,8|>>>:id error',
        'type' => 'IntIn:1,2',
    ];

    //导出
    public static $validate_deposit_export = [
        'type'              => 'IntIn:0,1,2,3,4|>>>: type  error',
        'apply_no'          => 'StrLenGeLe:12,20|>>>:apply no error',
        //编号
        'create_id'         => 'IntGt:0|>>>:create_id error',
        //押金负责人
        'create_company_id' => 'StrLenGeLe:0,10|>>>: create_company_id  error',
        'contract_no'       => 'StrLenGeLe:1,30|>>>:contract_no no error',
        //合同编号
        'sta_date'          => 'Date|>>>:sta_date error',
        //申请日期
        'end_date'          => 'Date|>>>:end end_date error',
        //申请日期
        'sta_return_date'   => 'Date|>>>:sta_return_date error',
        //归还日期
        'end_return_date'   => 'Date|>>>:end_return_date error',
        //归还日期
        'return_status_id'  => 'IntIn:' . DepositEnums::DEPOSIT_VALIDATE_RETURN_STATUS_STR . '|>>>: return_status_id  error',
        //归还状态
    ];


    //归还审核列表
    public static $validate_audit_list = [
        'apply_no'          => 'StrLenGeLe:12,20|>>>:apply no error',
        //编号
        'create_id'         => 'IntGt:0|>>>:create_id error',
        //押金负责人
        'create_company_id' => 'StrLenGeLe:0,10|>>>: create_company_id  error',
        'contract_no'       => 'StrLenGeLe:1,30|>>>:contract_no no error',
        //合同编号
        'sta_return_date'   => 'Date|>>>:sta_return_date error',
        //归还日期
        'end_return_date'   => 'Date|>>>:end_return_date error',
        //归还日期
        'return_status_id'  => 'IntIn:' . DepositEnums::DEPOSIT_VALIDATE_RETURN_STATUS_STR . '|>>>: return_status_id  error',
        //归还状态
    ];

    //数据查询列表
    public static $validate_data_list = [
        'type'              => 'Required|IntIn:1,2,3,4|>>>: type  error',
        'apply_no'          => 'StrLenGeLe:12,20|>>>:apply no error',
        //编号
        'create_id'         => 'IntGt:0|>>>:create_id error',
        //押金负责人
        'create_company_id' => 'StrLenGeLe:0,10|>>>: create_company_id  error',
        'contract_no'       => 'StrLenGeLe:1,30|>>>:contract_no no error',
        //合同编号
        'sta_date'          => 'Date|>>>:sta_date error',
        //申请日期
        'end_date'          => 'Date|>>>:end end_date error',
        //申请日期
        'return_status_id'  => 'IntIn:' . DepositEnums::DEPOSIT_VALIDATE_RETURN_STATUS_STR . '|>>>: return_status_id  error',
        //归还状态
    ];

    //归还审核列表-审核
    public static $validate_approve = [
        'id'   => 'Required|IntGe:1',
        'note' => 'StrLenGeLe:0,1000',
    ];

    //审核 通过字段 数据校验
    public static $validate_approve_amount_detail_param = [
        'depositLoss[*]'              => 'Required|Obj',
        'depositLoss[*].loss_bear_id' => 'Required|IntGe:1|>>>:loss_bear_id  error', //损失承担方id
    ];


    //归还审核列表-驳回
    public static $validate_reject = [
        'id'   => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000',
    ];

    //待回复征询列表
    public static $validate_reply_list = [
        'is_reply' => 'Required|IntIn:0,1|>>>:is_reply is_reply error',
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];


    // url校验
    public static $validate_url_param = [
        'name' => 'Required|StrLenGeLe:1,256|>>>:error',
        'url'  => 'Required|StrLenGeLe:64,1024|>>>:error',
    ];

    // 合同编号搜索验证规则
    public static $validate_contract_search_param = [
        'cno' => 'Required|StrLenGeLe:1,20|>>>:cno error',
    ];

    // 网点搜索验证规则
    public static $validate_store_search_param = [
        'store_name' => 'Required|StrLenGeLe:1,255|>>>:store_name error',
    ];

    //查询所属部门
    public static $validate_budget = [
        'cost_department_id' => 'Required|IntGe:1',
    ];

    //批量转交
    public static $validate_deposit_batch_forward = [
        'deposit_type'    => 'Required|Arr|ArrLenGe:1',//模块
        'deposit_type[*]' => 'Required|IntIn:' . DepositEnums::DEPOSIT_TYPE_RULE,
        'attachment'      => 'Required|Arr|ArrLenGeLe:0,20',//附件列表
        'apply_id'        => 'Required|IntGt:1',            //原责任人
        'new_apply_id'    => 'Required|IntGt:1',            //新责任人
        'new_apply_name'  => 'StrLenGeLe:0,255'             //新责任人姓名
    ];

    /**
     * 过滤空值 和 非必要参数
     *
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must = [])
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

}
