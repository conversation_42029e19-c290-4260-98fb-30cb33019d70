<?php

namespace App\Modules\Deposit\Controllers;

use App\Library\Enums\DepositEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Deposit\Services\BaseService;

class ConsultationController extends BaseController
{

    /**
     * 待回复征询列表
     *
     * @Token
     * @Date: 8/24/22 11:05 AM
     * @author: peak pan
     * yapi  https://yapi.flashexpress.pub/project/133/interface/api/62610
     */
    public function listAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BaseService::$validate_reply_list);

        $list = DepositService::getInstance()->auditDepositList($params, $this->user['id'],
            DepositEnums::LIST_TYPE_CONSULTED_REPLY);

        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 回复详情
     *
     * @Token
     * @Date: 8/24/22 11:05 AM
     * @author: peak pan
     * yapi  https://yapi.flashexpress.pub/project/133/interface/api/62611
     */
    public function detailAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_detail_param);

        $res = DepositService::getInstance()->getDetail($data, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 回复
     *
     * @Token
     * @Date: 8/24/22 11:05 AM
     * @author: peak pan
     * yapi  https://yapi.flashexpress.pub/project/133/interface/api/62613
     */
    public function replyAction()
    {
        $ask_id      = $this->request->get('ask_id', 'int');
        $note        = $this->request->get('note', 'trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $ask_id,
            'note'        => $note,
            'attachments' => $attachments,
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 征询提交
     *
     * @Token
     * @Date: 8/24/22 11:05 AM
     * @author: peak pan
     * yapi https://yapi.flashexpress.pub/project/133/interface/api/62614
     */
    public function askAction()
    {
        $biz_id      = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        $to_staffs   = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $biz_id,
            'note'        => $note,
            'to_staff'    => $to_staffs,
            'attachments' => $attachments,
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request   = DepositService::getInstance()->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result    = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }
}
