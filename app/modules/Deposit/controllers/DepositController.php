<?php

namespace App\Modules\Deposit\Controllers;

use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Deposit\Services\BaseService;
use App\Library\Enums\DepositEnums;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class DepositController extends BaseController
{


    /**
     * 押金管理默认配合
     *
     * @Token
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62556
     * @author: peak pan
     */
    public function getOptionsDefaultAction()
    {
        $res = DepositService::getInstance()->getCreatePageBaseInfoDefaultData();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 搜索网点
     *
     * @Token
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62559
     **@author: peak pan
     */
    public function getSearchStoreAction()
    {
        $params = $this->request->get();
        $res    = DepositService::getInstance()->getStoreData($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 我的押金列表
     * @Permission(action='deposit.deposit.list')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62569
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        Validation::validate($params, BaseService::$validate_deposit_list);

        $res = DepositService::getInstance()->getList($params, $this->user, DepositEnums::LIST_TYPE_APPLY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 查看
     * @Permission(action='deposit.deposit.view')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62589
     */
    public function viewAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_deposit_view);

        $res = DepositService::getInstance()->getDetail($param, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 编辑
     * @Permission(action='deposit.deposit.edit')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62590
     */
    public function editAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_deposit_edit);
        $res = DepositService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 转交
     * @Permission(action='deposit.deposit.forward')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62592
     */
    public function forwardAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_deposit_forward);
        $res = DepositService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归还添加
     * @Permission(action='deposit.deposit.return_add')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62595
     */
    public function returnAddAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_return_add);

        $res = DepositService::getInstance()->addApply($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 根据填写的用户id返name
     *
     * @Token
     * @Date: 8/1/22 4:35 PM
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62596
     **/
    public function getApplyIdByNameAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_get_apply_id_by_name);

        $res = DepositService::getInstance()->applyIdByName($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 导出
     * @Permission(action='deposit.deposit.export')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws Exception
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62597
     */
    public function exportAction()
    {
        $param = $this->request->get();
        $param = BaseService::handleParams($param, []);
        Validation::validate($param, BaseService::$validate_deposit_export);

        $param['export_type'] = DepositEnums::EXPORT_TYPE_ONE; //表示导出对应的uid数据

        // 加锁处理
        $lock_key = md5('deposit_deposit_export_download_' . $param['type'] . '_' . $this->user['id'] . '_' . $param['export_type']);
        $res      = $this->atomicLock(function () use ($param) {
            return DepositService::getInstance()->download($param, $this->user, DepositEnums::DEPOSIT_DOWNLOAD_LIMIT);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }


    /**
     * 归还审核列表
     * @Permission(action='deposit.deposit.audit_list')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62599
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, BaseService::$validate_audit_list);

        $res = DepositService::getInstance()->auditDepositList($params, $this->user['id'],
            DepositEnums::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归还审核列表-查看
     * @Permission(action='deposit.deposit.audit_detail')
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62600
     */
    public function auditDetailAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_deposit_view);

        $res = DepositService::getInstance()->getDetail($param, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 归还审核列表-审核
     * @Permission(action='deposit.deposit.approve')
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62720
     */
    public function approveAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, BaseService::$validate_approve);

        $amount_detail = $params['depositLoss'] ?? [];

        //如果有修改金额详情的字段信息，则验证参数正确性
        if ($amount_detail) {
            Validation::validate($params, BaseService::$validate_approve_amount_detail_param);
        }

        //todo 审核通过逻辑处理
        $res = DepositService::getInstance()->approve($params['id'], $params['note'] ?? '',
            $this->user, $params['depositLoss']);

        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 归还审核列表-驳回
     * @Permission(action='deposit.deposit.approve')
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62722
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, BaseService::$validate_reject);

        $res = DepositService::getInstance()->reject($params['id'], $params['note'], $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询列表
     * @Permission(action='deposit.deposit.data_list')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62602
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        Validation::validate($params, BaseService::$validate_data_list);
        $params['export_type']                      = DepositEnums::EXPORT_TYPE_ZERO;
        $params['is_access_common_data_permission'] = true;
        $res                                        = DepositService::getInstance()->getList($params, $this->user,
            DepositEnums::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询 - 详情
     * @Permission(action='deposit.deposit.data_detail')
     *
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62605
     */
    public function dataDetailAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_deposit_view);

        $res = DepositService::getInstance()->getDetail($param, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 数据查询-导出
     * @Permission(action='deposit.deposit.data_export')
     * @Date: 7/26/22 2:49 PM
     * @return Response|ResponseInterface
     * @throws Exception
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62603
     */
    public function dataExportAction()
    {
        $param = $this->request->get();
        $param = BaseService::handleParams($param, []);

        Validation::validate($param, BaseService::$validate_deposit_export);

        $param['export_type']                      = DepositEnums::EXPORT_TYPE_ZERO; //表示导出数据不按照工号
        $param['is_access_common_data_permission'] = true;

        // 加锁处理
        $lock_key = md5('deposit_deposit_export_download_' . $param['type'] . '_' . $this->user['id'] . '_' . $param['export_type']);
        $res      = $this->atomicLock(function () use ($param) {
            return DepositService::getInstance()->download($param, $this->user, DepositEnums::DEPOSIT_DOWNLOAD_LIMIT);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 损失类型
     *
     * @Token
     * @Date: 8/3/22 8:12 PM
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62634
     **/
    public function getBudgetListAction()
    {
        $params = $this->request->get();
        $res    = DepositService::getInstance()->getBudgetList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 合同搜索
     *
     * @Token
     * @Date: 8/24/22 6:54 PM
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @author: peak pan
     */
    public function getSearchContractListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);

        Validation::validate($params, BaseService::$validate_contract_search_param);

        $res = DepositService::getInstance()->getContractList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量转交
     * @Permission(action='deposit.deposit.data_batch_forward')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85442
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchForwardAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_deposit_batch_forward);
        $res = DepositService::getInstance()->batchForward($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 批量转交 结果查询
     * @Permission(action='deposit.deposit.data_batch_forward')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/85427
     * @return Response|ResponseInterface
     * @return array
     */
    public function getResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_DEPOSIT_BATCH_FORWARD);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-转交提交
     * @Permission(action='deposit.deposit_select.transfer')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/87929
     */
    public function forwardFromDataAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_deposit_forward);
        $res = DepositService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-编辑提交
     * @Permission(action='deposit.deposit_select.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/87932
     */
    public function editFromDataAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_deposit_edit);
        $res = DepositService::getInstance()->edit($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询-归还提交
     * @Permission(action='deposit.deposit.return_add')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/87935
     */
    public function returnAddFromDataAction()
    {
        $data = $this->request->get();
        Validation::validate($data, BaseService::$validate_return_add);
        $res = DepositService::getInstance()->addApply($data, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
