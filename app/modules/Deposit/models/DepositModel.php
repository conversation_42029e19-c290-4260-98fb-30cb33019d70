<?php

namespace App\Modules\Deposit\Models;

use App\Models\Base;
use App\Modules\User\Models\AttachModel;
use App\Library\Enums;

class DepositModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('deposit');
        $this->hasOne(
            'id',
            DepositReturnModel::class,
            'deposit_id',
            [
                "alias"  => "DepositReturn",
                'params' => [
                    'columns' => 'id,deposit_type,deposit_id,create_id,return_date,status,loss_money,return_money,deposit_money,bank_flow_date,return_info,other_return_money,other_return_info',
                ],
            ]
        );
        $this->hasMany(
            'id',
            DepositEditLogModel::class,
            'deposit_id',
            [
                'alias'  => 'DepositEditLog',
                'params' => [
                    'columns' => 'id,deposit_id,type,before_data,after_data,created_id,created_name,created_at',
                ],
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_EDIT_LOG_ADD . ' and deleted = 0',
                ],
                'alias'  => 'FileLog',
            ]
        );
    }

}
