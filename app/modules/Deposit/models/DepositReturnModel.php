<?php

namespace App\Modules\Deposit\Models;

use App\Models\Base;
use App\Modules\User\Models\AttachModel;
use App\Library\Enums;

class DepositReturnModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('deposit_return');

        $this->hasMany(
            'id',
            DepositLossModel::class,
            'deposit_return_id',
            [
                'alias'  => 'DepositLoss',
                'params' => [
                    'columns' => 'id,loss_money,loss_bear_id,loss_bear_name,loss_department_id,loss_department_name,loss_organization_id,loss_organization_name,loss_budget_id,loss_budget_name',
                ],
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = ' . Enums::OSS_ORDINARY_PAYMENT_TYPE_DEPOSIT_ADD . ' and deleted = 0 ',
                ],
                'alias'  => 'File',
            ]
        );
    }

}
