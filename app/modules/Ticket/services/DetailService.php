<?php

namespace App\Modules\Ticket\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;


class DetailService extends BaseService
{
    public static $validate_detail = [
        'id' => 'Required|IntGe:1',                                  //ID
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $params
     * @param $user
     * @return array
     */
    public function getAuditDetail($params, $user)
    {

        $id = $params['id'] ?? 0;

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $ac = new ApiClient('by', '', 'ticket_detail', static::$language);
        $ac->setParams(
            [
                [
                    "id" => $id,
                    'user_id'=>$user['id'],
                    'user_name'=>$user['name'],
                    'organization_id'=>$user['organization_id'],
                    'organization_type'=>$user['organization_type']
                ]
            ]
        );
        $res = $ac->execute();

        if(empty($res)){
            return ["code"=>ErrCode::$SYSTEM_ERROR,"data"=>[],"message"=>""];
        }

        if($res['result']['code']!=ErrCode::$SUCCESS){
            return $res['result'];
        }

        $data =  $res['result']['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }
}
