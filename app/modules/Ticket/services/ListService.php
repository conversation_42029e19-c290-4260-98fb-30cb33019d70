<?php

namespace App\Modules\Ticket\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\Ticket\Models\ItemType;

class ListService extends BaseService
{

    public static $not_must_params = [
        'pageSize',
        'pageNum',
        'item_type',
        'created_store_id',
        'created_id',
        'status',
        'created_at_start',
        'created_at_end',
        'updated_at_start',
        'update_at_end',
    ];

    public static $validate_list_search = [
        'pageSize' => 'IntGt:0',
        'pageNum' => 'IntGt:0',
        'item_type' => "Arr",//设备类型
        'created_store_id' => 'StrLenGeLe:1,50',
        'created_id' => 'StrLenGeLe:1,50',
        'status'=>'IntIn:1,2,3',
        'created_at_start' => 'DateTime',
        'created_at_end' => 'DateTime',
        'updated_at_start' => 'DateTime',
        'update_at_end' => 'DateTime',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 工单审核列表
     * @param $condition
     * @param $user
     * @return array
     */
    public function getAuditList($condition, $user)
    {
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_size = ($page_size > 1000) ? 1000 : $page_size;


        $ac = new ApiClient('by', '', 'ticket_list', static::$language);
        $ac->setParams(
            [
                [
                    'page_num' => $page_num,
                    'page_size' => $page_size,
                    'ticket_id' => $condition['ticket_id'] ?? '',
                    'status'=>$condition['status']??'',
                    'created_id'=>$condition['created_id']??'',
                    'item_type'=>$condition['item_type']??'',
                    'store_category'=>$condition['store_category']??'',
                    'created_store_id'=>$condition['created_store_id'] ?? '',
                    'created_at_start' => $condition['created_at_start'] ?? '',
                    'created_at_end' => $condition['created_at_end'] ?? '',
                    'updated_at_start' => $condition['updated_at_start'] ?? '',
                    'updated_at_end' => $condition['updated_at_end'] ?? '',
                    'device_store_id' => $condition['device_store_id'] ?? '',   // 设备所在网点

                    'user_id'=>$user['id'],
                    'user_name'=>$user['name'],
                    'organization_id'=>$user['organization_id'],
                    'organization_type'=>$user['organization_type']

                ]
            ]
        );
        $res = $ac->execute();

        if(empty($res)){
            return ["code"=>ErrCode::$SYSTEM_ERROR,"data"=>[],"message"=>""];
        }


        if($res['result']['code']!=ErrCode::$SUCCESS){
            return $res['result'];
        }

        $items = empty($res['result']['data']['dataList']) ? [] : $res['result']['data']['dataList'];

        return [
            'code' =>ErrCode::$SUCCESS,
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => intval($res['result']['data']['pagination']['count'] ?? 0),
            ]
        ];
    }

    /**
     * 工单审核列表下载
     * @param $condition
     * @param $user
     * @return array
     */
    public function download($condition, $user)
    {

        $params['status'] = $condition['status'] ?? '';
        $params['created_id'] =$condition['created_id']??'';
        $params['item_type'] = $condition['item_type']??'';
        $params['created_store_id']=$condition['created_store_id'] ?? '';
        $params['store_category']=$condition['store_category'] ?? '';

        $params['created_at_start'] = $condition['created_at_start'] ?? '';
        $params['created_at_end'] = $condition['created_at_end'] ?? '';
        $params['updated_at_start'] = $condition['updated_at_start'] ?? '';
        $params['updated_at_end'] = $condition['updated_at_end'] ?? '';
        $params['ticket_id'] = $condition['ticket_id'] ?? '';


        $params['pageNum'] = 1;
        $params['pageSize'] = 10000;
        $res = $this->getAuditList($params, $user);
        $data = $res['items'] ?? [];
        $new_data = [];
        $i = 0;
        foreach ($data as $key => $val) {
            ++$i;

            $new_data[$key][] = $i;
            $new_data[$key][] = $val['ticket_id'];
            $new_data[$key][] = $val['created_at'];
            $new_data[$key][] = $val['created_name'];
            $new_data[$key][] = $val['created_id'];
            $new_data[$key][] = $val['created_department_name']; //部门
            $new_data[$key][] = $val['created_job_title_name']; //职位
            $new_data[$key][] = $val['created_store_name'];
            $new_data[$key][] = $val['category_name'];//网点类型 名称
            $new_data[$key][] = $val['item_type_text'];
            $new_data[$key][] = $val['device_store_name']; // 设备所在网点
            $new_data[$key][] = $val['item_code'];
            $new_data[$key][] = $val['info'];
            $new_data[$key][] = $val['status_text'];
            $new_data[$key][] = $val['first_deal_name'];
            $new_data[$key][] = $this->getFormatId($val['first_deal_id']);
            $new_data[$key][] = $val['first_deal_at']; //首次回复时间
            $new_data[$key][] = $val['deal_name'];
            $new_data[$key][] = $this->getFormatId($val['deal_id']);
            $new_data[$key][] = $val['updated_at'];
        }
        //$file_name = static::$t->_('asset_collection_approval_form') . date('YmdHis');
        $file_name = "ticket_".date("YmdHis");
        $header = [
            static::$t->_('global.no'),
            static::$t->_('ticket.ticket_id'), // 工单 ID
            static::$t->_('ticket.created_at'),  //提交时间
            static::$t->_('ticket.created_name'),   //提交人
            static::$t->_('ticket.created_id'),     //提交人工号
            static::$t->_('ticket.created_department_name'),    //部门
            static::$t->_('ticket.created_job_title_name'),     //职位
            static::$t->_('ticket.created_store_name'),//网点
            static::$t->_('ticket.store_category_name'),//网点类型名称
            static::$t->_('ticket.item_type_text'),//设备类型
            static::$t->_('ticket.device_store_name'),//设备所在网点
            static::$t->_('ticket.item_code'),  //设备编码
            static::$t->_('ticket.info'),  //问题详情
            static::$t->_('ticket.status_text'), //处理状态
            static::$t->_('ticket.first_deal_name'),  //首次处理人
            static::$t->_('ticket.first_deal_id'),  //首次回复人工号
            static::$t->_('ticket.first_deal_at'),  //首次回复时间
            static::$t->_('ticket.deal_name'),  //最后处理人
            static::$t->_('ticket.deal_id'),    //最后回复人工号
            static::$t->_('ticket.updated_at')  //更新时间
        ];
        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 获得ItemType的类型。
     * @return array
     */
    public function getItemType($user){
        /*try {
            $data = [];
            $types = ItemType::find(['order'=>'sort'])->toArray();
            $lang = static::$language;
            foreach ($types as $k=>$v){
                $tmp = [];
                $tmp['id'] = $v['id'];
                $tmp['name']= $v[$lang];
                $data[] = $tmp;
            }
            return ['code' => 1,'data' => $data];

        } catch (\Exception $e) {
            return ['code' => 0,'data' => 'Please Try Again'.$e->getMessage()];
        }*/
        //上一版本查询数据表，当前版本为兼容其他国家通过rpc调用
        $ac = new ApiClient('by', '', 'ticket_item_type', static::$language);
        $ac->setParams(
            [
                [
                    'user_id'=>$user['id'],
                    'user_name'=>$user['name'],
                    'organization_id'=>$user['organization_id'],
                    'organization_type'=>$user['organization_type']

                ]
            ]
        );
        $res = $ac->execute();
        if(empty($res)){
            return ["code"=>ErrCode::$SYSTEM_ERROR,"data"=>[],"message"=>""];
        }

        if($res['result']['code']!=ErrCode::$SUCCESS){
            return $res['result'];
        }

        $data = empty($res['result']['data']) ? [] : $res['result']['data'];

        return [
            'code' =>ErrCode::$SUCCESS,
            'data' => $data,
        ];
    }

    public function getFormatId($id){
        if(empty($id)){
            return '';
        }
        return $id;
    }



}
