<?php

namespace App\Modules\Ticket\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\ErrCode;

class AuditService extends BaseService
{
    public static $not_must_params = [
    ];

    public static $validate_reply = [
        'id' => 'Required|IntGe:1',                               //ID
        'mark' => 'Required|StrLenGeLe:10,500',                   //mark
        'is_pic' => 'IntIn:0,1'
    ];

    public static $validate_close = [
        'ids' => 'Required',                               //IDs
        'mark' => 'Required|StrLenGeLe:10,500',            //mark
    ];


    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AuditService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function reply($params, $user)
    {
        $ac = new ApiClient('by', '', 'ticket_reply', static::$language);
        $ac->setParams(
            [
                [
                    'id' =>$params['id'],
                    'mark'=>$params['mark'],
                    'is_pic'=>$params['is_pic']??0,
                    'user_id'=>$user['id'],
                    'user_name'=>$user['name'],
                    'organization_id'=>$user['organization_id'],
                    'organization_type'=>$user['organization_type']
                ]
            ]
        );
        $res = $ac->execute();

        if(empty($res)){
            return ["code"=>ErrCode::$SYSTEM_ERROR,"data"=>[],"message"=>""];
        }

        $code = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data = $res['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    public function close($params, $user)
    {
        $ac = new ApiClient('by', '', 'ticket_batch_close', static::$language);
        $ac->setParams(
            [
                [
                    'ids' =>$params['ids'],
                    'mark'=>$params['mark'],
                    'user_id'=>$user['id'],
                    'user_name'=>$user['name'],
                    'organization_id'=>$user['organization_id'],
                    'organization_type'=>$user['organization_type']
                ]
            ]
        );
        $res = $ac->execute();

        if(empty($res)){
            return ["code"=>ErrCode::$SYSTEM_ERROR,"data"=>[],"message"=>""];
        }

        $code = $res['result']['code'] ?? $res['code'];
        $message = $res['result']['msg'] ?? $res['msg'];
        $data = $res['data'] ?? [];
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }
}