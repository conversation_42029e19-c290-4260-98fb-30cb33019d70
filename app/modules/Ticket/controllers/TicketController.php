<?php

namespace App\Modules\Ticket\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SettingEnvModel;
use App\Modules\Ticket\Services\AuditService;
use App\Modules\Ticket\Services\BaseService;
use App\Modules\Ticket\Services\DetailService;
use App\Modules\Ticket\Services\ListService;

class TicketController extends BaseController
{
    //it工单审核用户信息
    public $it_audit_user = [];

    public function initialize(){
        parent::initialize();
        //有菜单权限就能访问
        //$this->user['organization_id'] = 21;
        //$this->user['organization_type'] = 2;
        //上一版it工单只有泰国，初始化有菜单权限用户的部门和部门类型，但是也有问题，不能为此功能重新构建员工信息
        //此版本兼容其他国家，从setting_env取权限数据
        $this->initItAuditUserInfo();
    }

    /**
     * 工单审核列表
     * @Permission(action='ticket.audit.search')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        $res = ListService::getInstance()->getAuditList($params,$this->it_audit_user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            unset($res['code']);
            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * 审核详情
     * @Permission(action='ticket.audit.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();

        try {
            Validation::validate($data, DetailService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = DetailService::getInstance()->getAuditDetail($data, $this->it_audit_user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 审核
     * @Permission(action='ticket.audit.reply')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function replyAction()
    {
        $data = $this->request->get();
        $data = filter_param($data);
        try {
            Validation::validate($data, AuditService::$validate_reply);
            if ($data['is_pic'] == 1){
                Validation::validate($data, ['mark' => 'Required|Url']);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        //增加参数过滤
        $res = AuditService::getInstance()->reply($data, $this->it_audit_user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 关闭工单
     * @Permission(action='ticket.audit.close')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function closeAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, AuditService::$validate_close);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AuditService::getInstance()->close($data, $this->it_audit_user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 下载
     * @Permission(action='ticket.audit.download')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function downloadAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
       $res =  ListService::getInstance()->download($params, $this->it_audit_user);
       if ($res['code'] == ErrCode::$SUCCESS) {
           return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
       }
       return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 工单类型列表
     * @Permission(action='ticket.audit.search')
     *
     */
    public function getItemTypeAction(){
        $res =  ListService::getInstance()->getItemType($this->it_audit_user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    //工单相关 枚举 目前只有 网点类型
    public function ticket_enumAction(){
        try{
            $store_category = Enums::$store_category;
            $data['store_category'] = array();
            foreach ($store_category as $k => $v){
                $row['id'] = $k;
                $row['name'] = $v;

                $data['store_category'][] = $row;
            }
            return $this->returnJson(ErrCode::$SUCCESS,'',$data);
        }catch (\Exception $e){
            $this->logger->error('ticket enum '. $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error');
        }
    }

    /**
     * 初始化审核员工信息-只是在审核菜单使用
     * @Token
     * */
    function initItAuditUserInfo(){
        $audit_power = SettingEnvModel::findFirst([
            'conditions' => 'code = :code:',
            'bind' => [ 'code'=>enums::IT_TICKET_AUDIT_POWER ],
            'columns' => [ 'set_val' ],
        ])->toArray();
        $audit_power = json_decode($audit_power['set_val'], true);
        if( isset($audit_power['department_id'])  && !empty($audit_power['department_id']) ){
            $this->it_audit_user = $this->user;
            $this->it_audit_user['organization_id'] = $audit_power['department_id'];
            $this->it_audit_user['organization_type'] = 2;
        }
    }
}
