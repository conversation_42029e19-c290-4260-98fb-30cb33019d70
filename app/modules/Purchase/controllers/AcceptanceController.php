<?php
/**
 * Created by PhpStorm.
 * Date: 2022/2/24
 * Time: 17:38
 */

namespace App\Modules\Purchase\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Purchase\Services\AcceptanceService;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\PayFlowService;
use App\Modules\User\Services\StaffService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AcceptanceController extends BaseController
{
    /**
     * @Permission(action='my.acceptance.apply')
     * @return Response|ResponseInterface
     * 创建验收单
     * @throws Exception
     */
    public function addAction()
    {
        if ($this->request->isPost()) {
            $data = $this->request->get();

            //执行参数校验
            Validation::validate($data, AcceptanceService::$validate_add_param);

            $lock_key = RedisKey::PURCHASE_SAVE_LOCK . '_' . $data['no'];
            $res      = $this->atomicLock(function () use ($data) {
                return AcceptanceService::getInstance()->createOrder($this->user, $data);
            }, $lock_key, 10);

            return $this->returnJson($res['code'] ?? ErrCode::$SUCCESS, $res['message'] ?? '');
        } else {
            $res = AcceptanceService::getInstance()->defaultData($this->user);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        }
    }

    /**
     * @Permission(action='my.acceptance.delete')
     * @return Response|ResponseInterface
     * 编辑验收单
     * @throws ValidationException
     */
    public function updateAction()
    {
        $data = $this->request->get();
        Validation::validate($data, AcceptanceService::$validate_update_param);

        $res = AcceptanceService::getInstance()->updateAcceptance($this->user,$data);
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='my.acceptance.search')
     * @return Response|ResponseInterface
     * 查询列表
     *
     * */
    public function myListAction()
    {
        $params = $this->request->get();
        $res = AcceptanceService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Permission(action='my.acceptance.view')
     * @return Response|ResponseInterface
     * 采购申请-查看
     * */
    public function myDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='my.acceptance.recall')
     * @return Response|ResponseInterface
     * 采购申请-撤回
     *
     * */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE, $data['data'] ?? []);

        $res = $service->cancel($id,$note,$this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='auidt.acceptance.search')
     * @return Response|ResponseInterface
     * 采购审核-列表
     *
     * */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res = AcceptanceService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * @Permission(action='audit.acceptance.view')
     * @return Response|ResponseInterface
     * 采购审核-查看
     *
     * */

    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->getDetail($id, $this->user['id'], BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='audit.acceptance.approved')
     * @return Response|ResponseInterface
     * 采购审核-审核
     * */
    public function auditApprovedAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE, $data ?? []);

        $res = $service->approve($id, $note, $this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='audit.acceptance.approved')
     * 采购审核-驳回和审核共用权限
     * */
    public function rejectAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');

        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_ACCEPTANCE, $data['data'] ?? []);


        $res = $service->reject($id,$note,$this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }



    /**
     * @Permission(action='data.acceptance.search')
     * @return Response|ResponseInterface
     * 数据查询-列表
     * */
    public function queryListAction()
    {
        $params = $this->request->get();
        $res = AcceptanceService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Token
     * 回复公共-列表
     * */
    public function publicListAction()
    {
        $params = $this->request->get();
        $res = AcceptanceService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Permission(action='data.acceptance.view')
     * @return Response|ResponseInterface
     * 数据查询-查看
     * */
    public function queryDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * @Token
     * 数据查询-查看
     * */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Token
     * 枚举参数
     * */
    public function getEnumsParamsAction(){
        $res = AcceptanceService::getInstance()->getEnumsParams();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购单入库详情
     *
     *  @Token
     *  @return Response|ResponseInterface
     * */
    public function  poStockDetailAction(){
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->purchaseOrderDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Token
     * @API https://yapi.flashexpress.pub/project/133/interface/api/46597
     * 获取启用中的标准型号barcode列表
     * @return Response|ResponseInterface
     */
    public function purchaseProductListAction()
    {
        $name_key = $this->request->get('name_key');

        $res = AcceptanceService::getInstance()->purchaseProductList($name_key);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }
    /**
     * @Token
     * wrs_code枚举
     *
     * */
    public function wrsCodeListAction()
    {
        $name_key = $this->request->get('name_key');

        $res = AcceptanceService::getInstance()->wrsCodeList($name_key);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Token
     * 产品描述枚举
     *
     * */
    public function productDescAction()
    {
        $name_key = $this->request->get('name_key');

        $res = AcceptanceService::getInstance()->productDesc($name_key);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * @Permission(action='data.acceptance.export')
     * @return Response|ResponseInterface
     * 导出列表
     * @throws Exception
     */
    public function exportListAction()
    {
        $params   = $this->request->get();
        $lock_key = md5('purchase_acceptance_product_export_lock' . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AcceptanceService::getInstance()->exportList($params, $this->user,BaseService::LIST_TYPE_DATA);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'],  $res['message']);
    }

    /**
     * @Token
     * 数据查询-查看
     * */
    public function publicNoDetailAction()
    {
        $data = $this->request->get();
        $no = $this->request->get('no');
        try {
            Validation::validate($data, ['no'=>'Required|Str']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->getDetailByNo($no,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 采购单入库详情
     *
     *  @Token
     *  @return Response|ResponseInterface
     * */
    public function  puStockDetailAction(){
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = AcceptanceService::getInstance()->purchaseApplyDetail($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 我的申请-验收-下载
     * @Permission(action='my.acceptance.down')
     *
     * @return Response|ResponseInterface
     */
    public function myDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = AcceptanceService::getInstance()->download($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-验收-下载
     *
     * @Permission(action='data.acceptance.down')
     * @return Response|ResponseInterface
     */
    public function queryDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = AcceptanceService::getInstance()->download($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 查询相关采购订单
     * @Token
     *
     * */
    public function getRelateOrderAction()
    {
        $data = $this->request->get();

        Validation::validate($data, ['no' => 'Required|StrLenGeLe:0,20', 'type' => 'Required|IntIn:1,2']);
        $res = AcceptanceService::getInstance()->getRelateOrder($data);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);

    }
    /**
     * 员工列表
     * @Token
     *
     * */
    public function searchStaffAction()
    {
        $staff_info_id = $this->request->get('staff_info_id');
        $department_id = $this->request->get('department_id', 'int');
        list($page, $page_size) = $this->getPaginationParams();

        $list = StaffService::getInstance()->getStaffsByDepOrStore('', $department_id, '', $page, $page_size, ['formal' => true, 'staff_info_id' => $staff_info_id]);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

}
