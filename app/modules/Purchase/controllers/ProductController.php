<?php

namespace App\Modules\Purchase\Controllers;

use App\Library\ErrCode;
use App\Modules\Purchase\Services\ProductService;

class ProductController extends BaseController{

    /**
     * Undocumented function
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function uploadAction(){
        if(!$this->request->hasFiles()){
        return $this->returnJson(-1, 'no file found');
        }
        $ret = ProductService::getInstance()->upload('product_file');
        if ($ret == FALSE) {
            return $this->returnJson(-1, 'upload error');
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success',$ret);
    }
}
