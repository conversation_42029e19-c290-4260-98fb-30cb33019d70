<?php
/**
 * 采购员设置
 */
namespace App\Modules\Purchase\Controllers;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\SetService;
use App\Util\RedisKey;
use Phalcon\Http\ResponseInterface;
use App\Library\Validation\ValidationException;

class SetController extends BaseController
{
    /**
     * 默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87509
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = SetService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查询
     * @Permission(action='purchase.set.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87497
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, SetService::$not_must_params);
        Validation::validate($params, SetService::$validate_list);
        $res = SetService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入编辑
     * @Permission(action='purchase.set.import')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87521
     * @return Response|ResponseInterface
     * @throws \Exception
     */

    public function importAction()
    {
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::PURCHASE_SET_IMPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function() use ($excel_file) {
            return SetService::getInstance()->import($excel_file, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 编辑
     * @Permission(action='purchase.set.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87512
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, SetService::$validate_edit);
        $res = SetService::getInstance()->edit($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='purchase.set.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87506
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, SetService::$not_must_params);
        Validation::validate($params, SetService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::PURCHASE_SET_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return SetService::getInstance()->export($params);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}
