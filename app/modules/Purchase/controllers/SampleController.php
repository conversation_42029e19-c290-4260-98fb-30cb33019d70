<?php
/**
 * Created by PhpStorm.
 * Date: 2022/7/12
 * Time: 16:11
 */

namespace App\Modules\Purchase\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Purchase\Services\SampleService;
use App\Modules\Purchase\Services\BaseService;
use App\Modules\Purchase\Services\PayFlowService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class SampleController extends BaseController
{
    /**
     * @Permission(action='my.sample.apply')
     * @return Response|ResponseInterface
     * 创建验收单
     * */
    public function addAction()
    {
        if ($this->request->isPost()) {
            $data = $this->request->get();
            //执行参数校验
            try {
                Validation::validate($data, SampleService::$validate_add_param);

            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }
            $res = SampleService::getInstance()->createOrder($this->user, $data);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data'] ?? []);
            }
            return $this->returnJson($res['code'], $res['message']);

        } else {
            $res = SampleService::getInstance()->defaultData($this->user);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);

        }
    }



    /**
     * @Permission(action='my.sample.delete')
     * @return Response|ResponseInterface
     * 重新提交验收单
     * */
    public function updateAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, SampleService::$validate_add_param);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = SampleService::getInstance()->recommitSample($this->user,$data);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='my.sample.search')
     * @return Response|ResponseInterface
     * 我的列表
     *
     * */
    public function myListAction()
    {
        $params = $this->request->get();
        $res = SampleService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Permission(action='my.sample.view')
     * @return Response|ResponseInterface
     * 采购申请-查看
     * */
    public function myDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = SampleService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='my.sample.recall')
     * @return Response|ResponseInterface
     * 采购申请-撤回
     *
     * */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_SAMPLE, $data['data'] ?? []);

        $res = $service->cancel($id,$note,$this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='auidt.sample.search')
     * @return Response|ResponseInterface
     * 采购审核-列表
     *
     * */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res = SampleService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }

    /**
     * @Permission(action='audit.sample.view')
     * @return Response|ResponseInterface
     * 采购审核-查看
     *
     * */

    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = SampleService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='audit.sample.approved')
     * @return Response|ResponseInterface
     * 采购审核-审核
     * */
    public function auditApprovedAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');
        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_SAMPLE, $data['data'] ?? []);


        $res = $service->approve($id, $note, $this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * @Permission(action='audit.sample.approved')
     * @return Response|ResponseInterface
     * 采购审核-驳回和审核共用权限
     * */
    public function rejectAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id','int');

        $note = $this->request->get('note', 'trim');

        try {
            Validation::validate($data, [
                'id' => 'Required|IntGe:1'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $service = new PayFlowService(Enums::WF_PURCHASE_SAMPLE, $data['data'] ?? []);


        $res = $service->reject($id,$note,$this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
        }
        return $this->returnJson($res['code'], $res['message']);

    }



    /**
     * @Permission(action='data.sample.search')
     * @return Response|ResponseInterface
     * 数据查询-列表
     * */
    public function queryListAction()
    {
        $params = $this->request->get();
        $res = SampleService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_DATA);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Token
     * 回复公共-列表
     * */
    public function publicListAction()
    {
        $params = $this->request->get();
        $res = SampleService::getInstance()->getList($params, $this->user,BaseService::LIST_TYPE_FYR);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);

    }

    /**
     * @Permission(action='audit.sample.view')
     * @return Response|ResponseInterface
     * 数据查询-查看
     * */
    public function queryDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = SampleService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * @Token
     * @return Response|ResponseInterface
     * */
    public function publicDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = SampleService::getInstance()->getDetail($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    /**
     * @Token
     * 枚举参数
     * */
    public function getEnumsParamsAction(){
        $res = SampleService::getInstance()->getEnumsParams();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @Permission(action='data.sample.export')
     * @return Response|ResponseInterface
     * 导出列表
     * @throws Exception
     */
    public function exportListAction()
    {
        $params   = $this->request->get();
        $lock_key = md5('purchase_sample_product_export_lock' . '_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return SampleService::getInstance()->exportList($params, $this->user,BaseService::LIST_TYPE_DATA);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'],  $res['message']);
    }

    /**
     * 我的申请-样品-下载
     * @Permission(action='my.sample.down')
     *
     * @return Response|ResponseInterface
     */
    public function myDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = SampleService::getInstance()->download($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 数据查询-样品-下载
     * @Permission(action='data.sample.down')
     *
     * @return Response|ResponseInterface
     */
    public function queryDownloadAction()
    {
        $data = $this->request->get();
        $id = $data['id'] ?? 0;
        try {
            Validation::validate($data, ['id'=>'Required|IntGe:1']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = SampleService::getInstance()->download($id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'],$res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

}
