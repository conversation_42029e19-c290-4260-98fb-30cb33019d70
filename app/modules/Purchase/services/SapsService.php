<?php
/**
 * Created by PhpStorm.
 * Date: 2021/6/28
 * Time: 19:18
 */
namespace App\Modules\Purchase\Services;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;

/**
 * sap系统 接口Service
 */
class SapsService extends BaseService
{
    const SAP_NO_ISSET = 1;//sap 单号存在

    private static $instance;
    private static $apiPaths;
    private static $user;
    private static $passWord;



    private function __construct(){
        self::$apiPaths = env('sap_interface_url','');
        self::$user = env('sap_user_id','_BYDHOST');
        self::$passWord = env('sap_pwd','Welcome1');
    }

    /**
     * @return  SapsService
     */
    public static function getInstance(){
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 发送请
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function httpRequestXml($method, $postData)
    {
        try {
            $curl      = curl_init();
            $header[]  = "Content-type: text/xml";
            $basic_key = self::$user . ":" . self::$passWord;
            $header[]  = "Authorization: Basic " . base64_encode($basic_key); //添加头，在name和pass处填写对应账号密码
            if (get_runtime_env() != 'dev') {
                curl_setopt($curl, CURLOPT_PROXY, env('proxy_ip')); //代理服务器地址
                curl_setopt($curl, CURLOPT_PROXYPORT, env('proxy_port')); //代理服务器端口
            }
            curl_setopt($curl, CURLOPT_URL, self::$apiPaths.$method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl,CURLOPT_CONNECTTIMEOUT, 180);
            curl_setopt($curl, CURLOPT_TIMEOUT, 300);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . curl_error($curl));

            }
            curl_close($curl);// 根据头大小去获取头信息内容

         return  $responseText;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $data    = [];
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . $message);
        }


    }

    /**
     * 作用：将xml转为array
     */
    public function xmlToArray($xml)
    {
        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }

    /**
     * 作用：array转xml
     */
    protected function arrayToXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";

            } else
                $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
        }
        $xml .= "</xml>";
        return $xml;
    }


    /**
     * 更新sap 任务号
     */
    public function updateSapTask($data)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SiteLogisticsTaskByElementsQuery_sync>
         <!--Optional:-->
         <SiteLogisticsTaskSelectionByElements>
          
            <!--相关单据编号:-->
            <SelectionByReferenceDocumentID>
               <!--默认I:-->
               <InclusionExclusionCode>I</InclusionExclusionCode>
               <!--默认1:-->
               <IntervalBoundaryTypeCode>1</IntervalBoundaryTypeCode>
               <!--相关单据编号，默认是收货通知编号:-->
               <LowerBoundaryReferenceDocumentID>'.$data['scm_no'].'</LowerBoundaryReferenceDocumentID>
               <!--Optional:-->
               <UpperBoundaryReferenceDocumentID></UpperBoundaryReferenceDocumentID>
            </SelectionByReferenceDocumentID>
            
           <!--相关单据编号类型:-->
            <SelectionByReferenceDocumentTypeCode>
             <!--默认I:-->
               <InclusionExclusionCode>I</InclusionExclusionCode>
              <!--默认1:-->
               <IntervalBoundaryTypeCode>1</IntervalBoundaryTypeCode>
              <!--相关单据类型，默认是58,收货通知的类型:-->
               <LowerBoundaryReferenceDocumentTypeCode>58</LowerBoundaryReferenceDocumentTypeCode>
               <!--Optional:-->
               <UpperBoundaryReferenceDocumentTypeCode></UpperBoundaryReferenceDocumentTypeCode>
            </SelectionByReferenceDocumentTypeCode>
             
         </SiteLogisticsTaskSelectionByElements>

      </glob:SiteLogisticsTaskByElementsQuery_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        $this->logger->info('update-sap-post-data:'.$data['scm_no'].'=========' . $post_xml);
        $return_xml = SapsService::getInstance()->httpRequestXml($method = '/sap/querysitelogisticstaskin', $post_xml);
        $this->logger->info('update-sap-return-data:'.$data['scm_no'].'=========' . $return_xml);

        preg_match_all("/\<SiteLogisticsTask\>(.*?)\<\/SiteLogisticsTask\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];

        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $site_logistics_task = $SiteLogisticsTask[0][0];
            $site_logistics_task = preg_replace('/xmlns[^=]*="[^"]*"/i', '', $site_logistics_task);
            $site_logistics_task = preg_replace('/[a-zA-Z0-9]+:/', '', $site_logistics_task);
            $return_arr = trim_array($this->xmlToArray($site_logistics_task));
        }
        return $return_arr;
    }

    public function confirmSapTaskIn($data)
    {
        $item_xml = '';
        foreach ($data['MaterialOutput'] as $k => $v) {
            $item_xml .= '<MaterialOutput>
                     <!--物料UUID，从任务查询得到:-->
                     <MaterialOutputUUID>' . $v['SiteLogisticsLotMaterialOutputUUID'] . '</MaterialOutputUUID>
                     <!--物料编码，从任务查询得到:-->
                     <ProductID>' . $v['ProductID'] . '</ProductID>
                     <!--库位号:-->
                     <TargetLogisticsAreaID>INV01</TargetLogisticsAreaID>
                     <!--收货数量:-->
                     <ActualQuantity unitCode="' . $v['unit'] . '">' . $v['num'] . '</ActualQuantity>
                  </MaterialOutput>';
        }


        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SiteLogisticsTaskBundleMaintainRequest_sync_V1>
         <BasicMessageHeader>
         </BasicMessageHeader>
         <SiteLogisticsTask>
            <!--任务ID:-->
            <SiteLogisticTaskID>' . $data['SiteLogisticsTaskID'] . '</SiteLogisticTaskID>
            <!--任务UUID，从任务查询得到:-->
            <SiteLogisticTaskUUID>' . $data['SiteLogisticTaskUUID'] . '</SiteLogisticTaskUUID>
            <!--创建日期:-->
            <ActualExecutionOn>' . $data['date'] . 'T00:00:00Z</ActualExecutionOn>
            <!--Optional:-->
            <ActualDeliveryDate>
               
               <StartDateTime timeZoneCode="UTC">' . $data['date'] . 'T00:00:00Z</StartDateTime>
               <EndDateTime timeZoneCode="UTC">' . $data['date'] . 'T00:00:00Z</EndDateTime>
            </ActualDeliveryDate>
            <ReferenceObject>
               <!--ReferenceUUID，从任务查询得到:-->
               <ReferenceObjectUUID>' . $data['ReferencedObjectUUID'] . '</ReferenceObjectUUID>
               <OperationActivity>
                  <!--OperationActivityUUID，从任务查询得到:-->
                  <OperationActivityUUID>' . $data['SiteLogisticsLotOperationActivityUUID'] . '</OperationActivityUUID>' . $item_xml .
            '</OperationActivity>
            </ReferenceObject>
         </SiteLogisticsTask>
      </glob:SiteLogisticsTaskBundleMaintainRequest_sync_V1>
   </soapenv:Body>
</soapenv:Envelope>';

        $this->logger->info('confirm-sap-post-data:=====post_xml' .$post_xml);
        $return_xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managesitelogisticstaskin', $post_xml);
        $this->logger->info('confirm-sap-return-data:=====return_xml======'. $return_xml);

        preg_match_all("/\<SiteLogisticsTaskResponse\>(.*?)\<\/SiteLogisticsTaskResponse\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];
        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = $this->xmlToArray($SiteLogisticsTask[0][0]);
        }

        return $return_arr;

    }

    /**
     * 根据oa po号
     * 查询sap任务号
     * */
    public function getSapNo($order_code)
    {

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:PurchaseOrderSimpleByElementsQuery_sync>
         <PurchaseOrderSimpleSelectionByElements>
          

          <!--根据Flash PO No查询:-->
            <a3ok:FlashContractNo_BHQ7O8J0U6CKCS20MCR06HO3R>
               <!--Zero or more repetitions:-->
               <SelectionByText>
                  <!--固定值:-->
                  <InclusionExclusionCode>I</InclusionExclusionCode>
                  <IntervalBoundaryTypeCode>1</IntervalBoundaryTypeCode>
                  <!--Flash PO No-->
                  <LowerBoundaryName>' . $order_code . '</LowerBoundaryName>
                  <!--Optional:-->
                  <UpperBoundaryName></UpperBoundaryName>
               </SelectionByText>
            </a3ok:FlashContractNo_BHQ7O8J0U6CKCS20MCR06HO3R>
         </PurchaseOrderSimpleSelectionByElements>
      <ProcessingConditions>
            <!--Optional:-->
            <QueryHitsMaximumNumberValue>1</QueryHitsMaximumNumberValue>
            <QueryHitsUnlimitedIndicator>false</QueryHitsUnlimitedIndicator>
            <!--Optional:-->
            <LastReturnedObjectID></LastReturnedObjectID>
         </ProcessingConditions>
      </glob:PurchaseOrderSimpleByElementsQuery_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        $return_xml = SapsService::getInstance()->httpRequestXml($method = '/sap/querypurchaseorderqueryin', $post_xml);

        $logger = $this->getDI()->get('logger');

        $logger->info('get-sap-no-data:=====post_xml' . $post_xml . '=====return_xml======' . $return_xml);

        preg_match_all("/\<ResponseProcessingConditions\>(.*?)\<\/ResponseProcessingConditions\>/s", $return_xml, $res);

        $code         = 0;
        $sap_order_no = '';
        if (isset($res[0][0]) && !empty($res[0][0])) {
            $res = $this->xmlToArray($res[0][0]);

            if (self::SAP_NO_ISSET == $res['ReturnedQueryHitsNumberValue']) {

                preg_match_all("/\<PurchaseOrder\>(.*?)\<\/PurchaseOrder\>/s", $return_xml, $purchase_order);

                if (isset($purchase_order[0][0])) {
                    $purchase_order = $this->xmlToArray($purchase_order[0][0]);
                    $sap_order_no   = $purchase_order['PurchaseOrderID'];
                    $code           = 1;
                }

            }
        }

        return ['code' => $code, 'sap_order_no' => $sap_order_no];
    }


    /**
     * 入库单发送sap
     * */

    public function storageToSap($storage_data)
    {
        $item_xml = '';
        foreach ($storage_data['item'] as $key => $value) {
            $item_xml .= ' <!--行项目10:-->
            <Item actionCode="01">
              <!--行项目编号，按10的倍数增加:-->
               <LineItemID>' . $value['item_id'] . '</LineItemID>
                 <!--行项目类型 固定值14:-->
               <TypeCode>14</TypeCode>
                <!--单据类型 固定值INST:-->
               <ProcessingTypeCode>INST</ProcessingTypeCode>
                  <!--数量:-->
               <DeliveryQuantity>' . $value['this_time_num'] . '</DeliveryQuantity>
                 <!--发货方:-->
               <SellerPartyID>' . $storage_data['sap_supplier_no'] . '</SellerPartyID>
                <!--收货方:-->
               <BuyerPartyID>' . $storage_data['cost_company'] . '</BuyerPartyID>
                  <!--产品:-->
               <ItemProduct actionCode="01">
                 <!--产品编码:-->
                 <ProductID>' . $value['product_option_code'] . '</ProductID>
               </ItemProduct>
                 <!--关联订单:-->
               <ItemBusinessTransactionDocumentReference actionCode="01">
                   <!--关联采购订单:-->
                  <PurchaseOrder>
                    <!--关联采购订单编号:-->
                     <ID>' . $storage_data['sap_order_no'] . '</ID>
                        <!--关联采购订单行项目编号:-->
                     <ItemID>' .  $value['item_id'] . '</ItemID>
                  </PurchaseOrder>
               </ItemBusinessTransactionDocumentReference>
            </Item>';

        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:StandardInboundDeliveryNotificationBundleCreateRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
           <!--创建收货通知:01创建、releaseDocumentIndicator下达标识符 默认true-->
          <StandardInboundDeliveryNotification actionCode="01"  releaseDocumentIndicator="true">
            <!--收货通知编码:-->
            <DeliveryNotificationID>' . $storage_data['scm_no'] . '</DeliveryNotificationID>
             <!--单据类型 固定值SD:-->
            <ProcessingTypeCode>SD</ProcessingTypeCode>
              <!--送货地点编号:-->
            <ShipToLocationID>' . $storage_data['delivery_place'] . '</ShipToLocationID>
               <!--收货方编号:-->
            <ProductRecipientID>' . $storage_data['cost_company'] . '</ProductRecipientID>
              <!--发货方编号:-->
            <VendorID>' . $storage_data['sap_supplier_no'] . '</VendorID>' . $item_xml .
            '</StandardInboundDeliveryNotification>  
      </glob:StandardInboundDeliveryNotificationBundleCreateRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';


        $this->logger->info('send-sap-storage-data:=====post_xml' . $post_xml);
        $return_xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managestandardinbounddeliveryn', $post_xml);
        $this->logger->info('send-sap-storage-data:=====return_xml======' . $return_xml);

        $res = [];
        preg_match_all("/\<StandardInboundDeliveryNotificationConfirmationBody\>(.*?)\<\/StandardInboundDeliveryNotificationConfirmationBody\>/s", $return_xml, $res);
        
        if (isset($res[0][0])) {
            $res = $this->xmlToArray($res[0][0]);
        }

        return $res;
    }






}