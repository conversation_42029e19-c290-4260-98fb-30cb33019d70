<?php

namespace App\Modules\Purchase\Services;

use App\Library\Exception\BusinessException;
use App\Modules\Purchase\Models\PurchaseProductCategory;
use App\Modules\Purchase\Models\PurchaseProducts;
use App\Modules\Purchase\Models\Translations;

class ProductService extends BaseService
{

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function upload($key, $path = APP_PATH . '/runtime/upload')
    {
        $file = $this->getUploadFile($key, $path);
        if ($file === false) {
            return $file;
        }
        $data = $this->getExcelContents($file, 1, 2, [
            1 => 'z_class_a',
            2 => 'e_class_a',
            3 => 't_class_a',
            4 => 'class_a_only',
            5 => 'z_class_b',
            6 => 'e_class_b',
            7 => 't_class_b',
            8 => 'class_b_only',
            9 => 'z_name',
            10 => 'e_name',
            11 => 't_name',
            12 => 'option',
            13 => 'f_code',
            14 => 'p_code',
            15 => 'z_unit',
            16 => 'e_unit',
            17 => 't_unit']);

        try {
            foreach ($data as $key => $v) {
                if (empty($v['z_class_a']) || empty($v['z_class_b'])) {
                    continue;
                }
                $category_one_id = PurchaseProductCategory::getFirst([
                    'conditions' => 'name = ?0',
                    'columns' => 'id',
                    'bind' => [$v['z_class_a']]
                ])->id;
                if (!$category_one_id) {
                    $category_one_id = $this->saveCategory([
                        'name' => $v['z_class_a'],
                        'e_name' => $v['e_class_a'],
                        't_name' => $v['t_class_a'],
                        'is_only' => $v['class_a_only'] ?: 0
                    ]);
                }

                $category_two_id = PurchaseProductCategory::getFirst([
                    'conditions' => 'name = ?0 AND pid = ?1',
                    'columns' => 'id',
                    'bind' => [$v['z_class_b'], $category_one_id]
                ])->id;
                if (!$category_two_id) {
                    $category_two_id = $this->saveCategory([
                        'name' => $v['z_class_b'],
                        'e_name' => $v['e_class_b'],
                        't_name' => $v['t_class_b'],
                        'is_only' => $v['class_b_only'] ?: 0,
                        'pid' => $category_one_id,
                        'finance_code' => $v['f_code']
                    ]);
                }

                if (empty($v['z_name'])) {
                    continue;
                }
                $v['cid'] = $category_two_id;
                $productId = $this->saveProduct($v);
                if ($productId) {
                    $productIds[] = $productId;
                } else {
                    $errors[] = [$v['z_name'], $v['option'], $v['p_code']];
                }
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('upload Error!' . $e->getMessage());
        }

        return [
            'success' => count($productIds),
            'errors' => $errors
        ];
    }

    /**
     * 读取Excel内容
     *
     * @param [string] $file_name
     * @param integer $headRow 表头所在行
     * @param integer $dataRow 数据起始行
     * @param array $fields 所需数据列
     * @return array
     * <AUTHOR>
     */
    public function getExcelContents($file_name, $headRow = 1, $dataRow = 2, $fields = [])
    {
        ini_set("error_reporting", "E_ALL & ~E_NOTICE & ~E_WARNING");
        try {
            $objPHPExcel = \PhpOffice\PhpSpreadsheet\IOFactory::load($file_name);
            $data_all = [];
            $objWorksheet = $objPHPExcel->getSheet(0);
            $highestRow = $objWorksheet->getHighestRow();//获取总行数
            //在导入时，启动行号为1，第1行为标题，第2行开始为数据
            if ($highestRow > 1) {
                $data_res = [];
                $highestColumn = $objWorksheet->getHighestColumn();//获取总列数
                $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);//总列数
                //注意下面以$row=2开始，在导入时，启动行号为1，第1行为标题，第2行开始为数据
                for ($row = $dataRow; $row <= $highestRow; $row++) {
                    $strs = [];
                    //注意highestColumnIndex的列数索引从1开始
                    for ($col = 1; $col <= $highestColumnIndex; $col++) {
                        if (trim($objWorksheet->getCellByColumnAndRow(1, $row)->getFormattedValue(\PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING)) == '') {
                            break 2;
                        }
                        if (count($fields) && empty($fields[$col])) {
                            continue;
                        }
                        if ($fields[$col]) {
                            $key = $fields[$col];
                        } else {
                            $key = trim($objWorksheet->getCellByColumnAndRow($col, $headRow)->getFormattedValue(\PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING));
                        }
                        $strs[$key]
                            = trim($objWorksheet->getCellByColumnAndRow($col, $row)->getFormattedValue(\PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_STRING));
                    }
                    array_push($data_res, $strs);
                }
                $data_all = array_merge($data_all, $data_res);
            } else {
                return false;
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('readExcel Error!' . $e->getMessage());
            return false;
        }
        return $data_all;
    }

    public function saveCategory($data)
    {
        $model = new PurchaseProductCategory();
        try {
            $data['created_at'] = date('Y-m-d H:i:s');
            $name_key = $this->getTkey($data['name']);
            if ($name_key) {
                $data['name_key'] = $name_key;
            }
            if ($model->i_create($data)) {
                $insertId = $model->getWriteConnection()->lastInsertId($model->getSource());
            } else {
                $insertId = false;
                throw new BusinessException('产品分类添加失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), -1);
            }
            if (!$name_key) {
                $name_key = 'purchase_product_category_name_' . $insertId;
                $z_key = $this->saveTranslation($name_key, $data['name']);
                $e_key = $this->saveTranslation($name_key, $data['e_name'] ?: ($data['t_name'] ?: $data['name']), 'en');
                $t_key = $this->saveTranslation($name_key, $data['t_name'] ?: ($data['e_name'] ?: $data['name']), 'th');
                if (!($z_key && $e_key && $t_key)) {
                    throw new BusinessException('产品分类翻译添加失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), -1);
                } else {
                    $update = ['name_key' => $name_key];
                    $bool = PurchaseProductCategory::getFirst([
                        'id = ?0',
                        'bind' => [$insertId]
                    ])->i_update($update);
                    if ($bool === false) {
                        throw new BusinessException('产品分类翻译更新失败=' . json_encode($update, JSON_UNESCAPED_UNICODE), -1);
                    }
                }
            }
        } catch (BusinessException $e) {
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('productCategory-create-failed:' . $real_message);
        }
        return $insertId;
    }

    public function saveProduct($data)
    {
        $model = new PurchaseProducts();
        try {
            $name_key = $this->getTkey($data['z_name']);
            $unit_key = $this->getTkey($data['z_unit']);
            $insertData = array_filter([
                'name' => $data['z_name'],
                'option' => $data['option'],
                'option_code' => $data['p_code'],
                'unit' => $unit_key,
                'name_key' => $name_key,
                'cid' => $data['cid'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
            if ($model->i_create($insertData)) {
                $insertId = $model->getWriteConnection()->lastInsertId($model->getSource());
            } else {
                $insertId = false;
                throw new BusinessException('产品添加失败=' . json_encode($insertData, JSON_UNESCAPED_UNICODE), -1);
            }

            if (!$name_key) {
                $name_key = 'purchase_product_name_' . $insertId;
                $z_key = $this->saveTranslation($name_key, $data['z_name']);
                $e_key = $this->saveTranslation($name_key, $data['e_name'] ?: ($data['t_name'] ?: $data['z_name']), 'en');
                $t_key = $this->saveTranslation($name_key, $data['t_name'] ?: ($data['e_name'] ?: $data['z_name']), 'th');
                if (!($z_key && $e_key && $t_key)) {
                    throw new BusinessException('产品名称翻译添加失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), -1);
                } else {
                    $update = ['name_key' => $name_key];
                    $bool = PurchaseProducts::getFirst([
                        'id = ?0',
                        'bind' => [$insertId]
                    ])->i_update($update);
                    if ($bool === false) {
                        throw new BusinessException('产品名称翻译更新失败=' . json_encode($update, JSON_UNESCAPED_UNICODE), -1);
                    }
                }
            }
            if (!$unit_key) {
                $unit_key = 'purchase_product_unit_' . $insertId;
                $z_key = $this->saveTranslation($unit_key, $data['z_unit']);
                $e_key = $this->saveTranslation($unit_key, $data['e_unit'] ?: ($data['t_unit'] ?: $data['z_unit']), 'en');
                $t_key = $this->saveTranslation($unit_key, $data['t_unit'] ?: ($data['e_unit'] ?: $data['z_unit']), 'th');
                if (!($z_key && $e_key && $t_key)) {
                    throw new BusinessException('产品单位翻译添加失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), -1);
                } else {
                    $update = ['unit' => $unit_key];
                    $bool = PurchaseProducts::getFirst([
                        'id = ?0',
                        'bind' => [$insertId]
                    ])->i_update($update);
                    if ($bool === false) {
                        throw new BusinessException('产品单位翻译更新失败=' . json_encode($update, JSON_UNESCAPED_UNICODE), -1);
                    }
                }
            }
        } catch (BusinessException $e) {
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('product-create-failed:' . $real_message);
        }

        return $insertId;
    }

    public function getTkey($value, $lang = 'zh-CN')
    {
        $key = Translations::getFirst([
            'conditions' => 't_value = ?0 AND lang = ?1',
            'columns' => 't_key',
            'bind' => [$value, $lang]
        ]);

        return $key->t_key ?? '';
    }

    public function saveTranslation($t_key, $t_value, $lang = 'zh-CN')
    {
        $model = new Translations();
        try {
            $data = [
                'lang' => $lang,
                't_key' => $t_key,
                't_value' => $t_value,
                'created_at' => date('Y-m-d H:i:s')
            ];
            if ($model->i_create($data) == false) {
                throw new BusinessException('翻译添加失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), -1);
            }
        } catch (BusinessException $e) {
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('translations-create-failed:' . $real_message);
        }
        return $t_key;
    }
}
