<?php

namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Material\Models\MaterialFinanceCategoryModel;
use App\Modules\Material\Services\StandardService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Purchase\Models\PurchaseAcceptanceModel;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Purchase\Models\PurchaseApplyProduct;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchaseOrderProduct;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Purchase\Models\PurchaseSampleModel;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Models\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowUpdateLogModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\HrStaffRepository;
use Google\Protobuf\Enum;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class PayFlowService extends AbstractFlowService
{

    private $model = null;
    private $blz_type = null;


    //no字段
    public $field_no = 'lno';

    //关联表id
    public $link_field_no = 'paid';

    //note字段
    public $field_note ='operation_remark';
    //department_id字段
    public $field_department_id ='create_department';

    public $update_data = [];



    public function __construct($blz_type,$update_data =[])
    {
        $this->blz_type = $blz_type;

        switch ($this->blz_type){
            case Enums::WF_PURCHASE_APPLY:
                $this->model = new PurchaseApply();
                $this->field_no='pano';
                break;
            case Enums::WF_PURCHASE_ORDER:
                $this->model = new PurchaseOrder();
                $this->field_no='pono';
                $this->link_field_no = 'poid';
                break;
            case Enums::WF_PURCHASE_PAYMENT:
                $this->model = new PurchasePayment();
                $this->field_no='ppno';
                $this->link_field_no = 'ppid';
                break;
            case Enums::WF_PURCHASE_ACCEPTANCE:
                $this->model = new PurchaseAcceptanceModel();
                $this->field_no='no';
                $this->link_field_no = 'pa_id';
                break;
            case Enums::WF_PURCHASE_SAMPLE:
                $this->model = new PurchaseSampleModel();
                $this->field_no='no';
                $this->link_field_no = 'sample_id';
                break;
        }

        $this->update_data = $update_data;
    }


    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            /**
             * @var $work_req  WorkflowRequestModel
             */
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }

            $item = $this->model::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_biz_main_data_null', ['biz_type' => $this->blz_type, 'main_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待审批状态, 不可审批通过
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取当前节点的业务标签: 新采购订单审批流的逻辑
            $node_tag = $this->getPendingNodeTag($work_req, $user['id']);

            // 产品行数据新旧版本的key值
            $product_key = isset($this->update_data['product_v1']) ? 'product_v1' : 'product';

            // 各国快递公司/子公司ID配置
            $company_list = EnumsService::getInstance()->getSysDepartmentCompanyIds();

            // AP(BJ)审批节点的特殊校验
            if ($node_tag && $node_tag == Enums::WF_NODE_TAG_AP_BEIJING) {
                // 代码整合
                $country_code = get_country_code();

                // 原有流程块
                if (in_array($work_req->flow_id, [Enums::WF_PURCHASE_ORDER_DEFAULT_FLOW, Enums::WF_PURCHASE_ORDER_LA_EXPRESS]) || ($work_req->flow_id == Enums::WF_PURCHASE_ORDER_OTHER_FLOW && in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]))) {
                    // AP北京 当费用公司是快递公司时,物料编码必填[16911的p2部分，旧117泰国菲律宾，234老挝；增加新299泰国菲律宾]
                    if ($this->update_data['cost_company'] == $company_list['FlashExpress']) {
                        Validation::validate($this->update_data, [
                            "{$product_key}[*].wrs_code"       => 'Required|StrLenGeLe:1,64|>>>:' . static::$t->_('setting_excel_import_product_cate_empty')
                        ]);
                    }

                    // 采购类型 等于 库存类时, 采购类型不可修改
                    if ($item->purchase_type == PurchaseEnums::PURCHASE_TYPE_STOCK && $item->purchase_type != $this->update_data['purchase_type']) {
                        throw new ValidationException(static::$t->_('purchase_order_audit_error_01'), ErrCode::$VALIDATE_ERROR);
                    }

                    /**
                     * 12614【ALL-OA】资产标准型号&采购入库&资产台账-P1
                     * 需要验证所选择的财务分类编码是否是最末级
                     */
                    $wrs_code_arr = array_values(array_unique(array_filter(array_column($this->update_data[$product_key], 'wrs_code'))));
                    if (!empty($wrs_code_arr)) {
                        $is_has_son = (new MaterialFinanceCategoryModel)->isHasSon($wrs_code_arr);
                        if ($is_has_son) {
                            throw new ValidationException(static::$t->_('purchase_order_audit_error_02'), ErrCode::$VALIDATE_ERROR);
                        }
                    }
                }

                // v18429 采购订单 在 AP(BJ) 节点的审批同意操作时特有校验: 费用所属公司是需传输给sap公司的, 且 为 泰国/马来/菲律宾的, 财务编码必填
                // 必填国家
                $required_countrys = [
                    GlobalEnums::TH_COUNTRY_CODE,
                    GlobalEnums::PH_COUNTRY_CODE,
                    GlobalEnums::MY_COUNTRY_CODE
                ];

                // 需同步给sap的公司配置
                $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
                if ($this->blz_type == Enums::WF_PURCHASE_ORDER && in_array($item->cost_company, $sap_company_ids) && in_array($country_code, $required_countrys)) {
                    Validation::validate($this->update_data, [
                        "{$product_key}[*].wrs_code" => 'Required|StrLenGeLe:1,64|>>>:' . static::$t->_('setting_excel_import_product_cate_empty')
                    ]);
                }
            }

            $can_edit_field = $this->getCanEditFieldByReq($work_req, $user['id']);

            if ($work_req->flow_id == Enums::PURCHASE_ACCEPTANCE_FLOW_ID_V2 && !empty($can_edit_field) && isset($can_edit_field['main']) && in_array('it_attach_files', $can_edit_field['main'])) {
                Validation::validate($this->update_data, [
                    'it_attach_files' => 'Required|Arr|ArrLenGeLe:1,20|>>>:' . static::$t->_('purchase_acceptance_it_attach_empty')
                ]);
            }

            //如果更新数据不为空
            if (!empty($this->update_data)) {
                if (!empty($can_edit_field)) {
                    // 指定国家指定公司可修改指定字段: 自定义逻辑
                    $can_edit_field = $this->workflowNodeEditFieldsCustomizeLogic($work_req, $item, $can_edit_field);
                    $this->dealEditField($item,$can_edit_field,$this->update_data,$work_req,$user);
                }
            }
            $ws = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if (!empty($result->approved_at)) {
                $bool = $item->i_update([
                    'status' => Enums::WF_STATE_APPROVED,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approve_at' => date("Y-m-d H:i:s")
                ]);
                if ($bool === false) {
                    throw new BusinessException('工作流拒绝更新宿主失败,宿主id='.$item->id."==宿主type==".$this->blz_type . '; 原因可能是: ' . get_data_object_error_msg($item), ErrCode::$CONTRACT_UPDATE_ERROR);
                } else {
                    if ($this->blz_type == Enums::WF_PURCHASE_PAYMENT) {
                        $pay_staff_id = (new BaseService())->getPurchasePaymentPayStaffIds();
                        $this->sendEmailToAuditors($work_req, $pay_staff_id, 1);
                        $this->delUnReadNumsKeyByStaffIds($pay_staff_id);

                        if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_PURCHASE, $item->cost_company_id)) {
                            //这肯定是PurchasePayment
                            PayService::getInstance()->saveOne($item);
                        }
                    } else if ($this->blz_type == Enums::WF_PURCHASE_ACCEPTANCE) {
                        //V20609 当验收单审核通过的时候，关联了采购订单，需更新采购订单各行的验收数量、采购订单的执行状态
                        OrderService::getInstance()->updateAcceptanceTotal($item, $user);

                        $email = EnvModel::getEnvByCode('acceptance_email');
                        $email = explode(',', $email);
                        $title = "Reminder of Receiving Report";
                        $html  = $this->mailContent($item->no);
                        if (!empty($email)) {
                            $this->mailer->sendAsync($email, $title, $html);
                        }
                    }
                }
            }

            if ($this->blz_type == Enums::WF_PURCHASE_PAYMENT) {
                if ($item->is_after_ap_th != 1 && $ws->isAfterApTH($result)) {
                    $item->is_after_ap_th = 1;
                    if ($item->save() === false) {
                        throw new BusinessException('宿主[is_after_ap_th]更新失败,宿主id='.$item->id."==宿主type==".$this->blz_type . '; 原因可能是: ' . get_data_object_error_msg($item), ErrCode::$CONTRACT_UPDATE_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('PayFlowService===approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $work_req = $this->getRequest($id);
            if (empty($work_req->id)) {
                throw new BusinessException("获取工作流批次失败, main_id={$id}, biz_type=" . $this->blz_type, ErrCode::$CONTRACT_GET_WORK_REQUEST_ERROR);
            }

            $item = $this->model::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_biz_main_data_null', ['biz_type' => $this->blz_type, 'main_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待审批状态, 不可驳回
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 如果是PO单驳回, 需回退PO单关联的PUR单相关数据
            if ($this->blz_type == Enums::WF_PURCHASE_ORDER) {
                OrderService::getInstance()->cancelRelation($item, Enums::WF_ACTION_REJECT);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status'          => Enums::WF_STATE_REJECTED,
                'updated_at'      => date('Y-m-d H:i:s'),
                $this->field_note => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException('工作流拒绝更新宿主失败,宿主id=' . $item->id . "==宿主type==" . $this->blz_type, ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            // 采购申请单驳回 释放占用的预算
            if ($this->blz_type == Enums::WF_PURCHASE_APPLY) {
                ApplyService::getInstance()->freedBudget($id, $user);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('PayFlowService===reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //只能撤回自己的
            $item = $this->model::findFirst([
                'conditions' => 'id = :id: AND create_id = :uid:',
                'bind' => ['id' => $id, 'uid' => $user['id']]
            ]);
            if (empty($item)) {
                throw new ValidationException(static::$t->_('purchase_biz_main_data_null', ['biz_type' => $this->blz_type, 'main_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待审批状态, 不可撤回
            if ($item->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('workflow_action_status_check_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 如果是PO单撤销, 需回退PO单关联的PUR单相关数据
            if ($this->blz_type == Enums::WF_PURCHASE_ORDER) {
                OrderService::getInstance()->cancelRelation($item, Enums::WF_ACTION_CANCEL);
            }

            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::WF_STATE_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                $this->field_note => $note,
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }

            // 采购申请单撤销, 释放/返还预算
            if ($this->blz_type == Enums::WF_PURCHASE_APPLY) {
                ApplyService::getInstance()->freedBudget($id, $user);
            }

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $db->rollback();

            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $db->rollback();

            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $db->rollback();

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('PayFlowService===cancel-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * @param int $id  业务数据ID
     * @param
     * @return Model|void
     */
    public function getRequest($id)
    {
        return $this->getRequestByBiz($id,$this->blz_type);
    }

    /**
     * 获取当前业务审批流信息
     *
     * @param $id
     * @param $bizType
     * @return Model
     */
    public function getRequestByBiz($id, $bizType)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id:',
                'bind' => ['type'=> $bizType ,'id' => $id],
                'order'=>'id desc'

            ]
        );
    }

    /**
     * @param $id
     * @param $user
     * @return Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($id, $user)
    {
        $item = $this->model::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        $data['id'] = $item->id;

        $field_no = $this->field_no;
        $data['name'] = $item->$field_no . '审批申请';

        $data['biz_type'] = $this->blz_type;

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($item, $user));
    }

    /**
     * 审批流需要数据
     *
     * @param $item
     * @param $user
     * @return array
     * @throws BusinessException
     */
    public function getWorkflowParams($item, $user)
    {
        //未含有资产类型barcode
        $has_asset_barcode = 0;
        $in_group_asset_management = $in_it_department = false;
        //v10533, 采购申请单保持原逻辑,取费用所属部门对应的公司
        //采购订单取费用所属公司
        //采购付款申请单,如果关联了采购订单就用采购订单的费用所属公司,如果关联了采购申请单就用采购申请单的费用所属公司
        if ($this->blz_type == Enums::WF_PURCHASE_APPLY) {
            //采购申请单，用含税金额总计
            $amount = $item->amount;
            //15424采购申请单, 费用公司使用cost_company_id, 之前是用cost_department查所属公司
            $companyId = $item->cost_company_id ?? 0;
            /**
             * 13663【OA-ALL】采购申请单优化
             *   1. 如果pr单的明细行里，填写的barcode，有任意一个是“资产”的barcode，则“资产经理”节点，必须审批。
             *   2. 如果pr单的明细行里，没有填写barcode，或者填写的barcode，没有任意一个是“资产”的barcode，则无需“资产经理”节点审批，自动跳过。
             *   3. “资产经理”审批节点为或签，如果配置了多个，则其中一个人审批通过之后，则该节点审批通过。
             * 14086 需求: 如果pr单的明细行里，任意一行填写了barcode, 不再区分是否"资产"的barcode, 都走"资产经理"节点
             */
            // 采购申请单-产品明细
            $products = $item->getProducts()->toArray();
            $product_barcode_arr = array_values(array_filter(array_column($products, "product_option_code")));
            if ($product_barcode_arr) {
                ////存在barcode，则获取barcode信息
                //$product_barcode_list = StandardService::getInstance()->getBarcodeList(['barcode'=>$product_barcode_arr]);
                //if ($product_barcode_list) {
                //    //找出各个barcode身上的所属物料类型，1资产，2耗材
                //    $category_type_arr = array_values(array_column($product_barcode_list, 'category_type'));
                //    if (in_array(Enums\MaterialClassifyEnums::MATERIAL_CATEGORY_TYPE_ASSET, $category_type_arr)) {
                //        //含有资产类型barcode
                //        $has_asset_barcode = 1;
                //    }
                //}
                $has_asset_barcode = 1;
            }

            /**
             * 16911【ALL|OA】采购申请优化
             * Group Asset Management部门及其子部门\IT部门及其子部门
             */
            $in_group_asset_management = $this->checkInDepartment($item->node_department_id, 'purchase_apply_asset_department_id');
            $in_it_department = $this->checkInDepartment($item->node_department_id, 'purchase_apply_it_department_id');
        } else if ($this->blz_type == Enums::WF_PURCHASE_ORDER) {
            //采购订单，用含税金额总计
            $amount = $item->total_amount;
            $companyId = $item->cost_company;
            //21846 - 财务BP分组 - 按照费用部门ID-审批
            $item->node_department_id = in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) ? $item->cost_department : $item->node_department_id;
        } else if ($this->blz_type == Enums::WF_PURCHASE_PAYMENT) {
            //付款申请单，用发票总计
            $amount = $item->receipt_amount;
            //v10533需求,如果关联了采购订单就用采购订单的费用所属公司,如果关联了采购申请单就用采购申请单的费用所属公司
            if (!empty($item->pa_id)) {
                $pa_info = PurchaseApply::findFirst([
                    'conditions' => 'id = ?1',
                    'bind' => [
                        1 => $item->pa_id,
                    ]
                ]);
                $companyId = isset($pa_info->cost_company_id) ? $pa_info->cost_company_id : 0;
            } else {
                $po_info = PurchaseOrder::findFirst([
                    'conditions' => 'id = ?1',
                    'bind' => [
                        1 => $item->po_id,
                    ]
                ]);
                $companyId = isset($po_info->cost_company) ? $po_info->cost_company : 0;
            }
        } else if ($this->blz_type == Enums::WF_PURCHASE_ACCEPTANCE) {
            return [
                'acceptance_cate'    => $item->acceptance_cate,
                'submitter_id'       => $item->create_id,
                'node_department_id' => $item->cost_department_id,// 改为费用所属部门: 审批流中取费用所属部门的一级/二级部门负责人
                'auditor_ids' => [
                    'use_staff_id' => $item->acceptance_officer,
                ],
            ];

        } else if ($this->blz_type == Enums::WF_PURCHASE_SAMPLE) {
            return [
                'submitter_id' => $item->create_id,
                'auditor_ids'  => [
                    'use_staff_id'     => $item->use_staff_id,
                    'confirm_staff_id' => $item->confirm_staff_id
                ],
            ];

        } else {
            throw new BusinessException('not found biz_type', ErrCode::$BUSINESS_ERROR);
        }

        // 金额根据币种汇率转换为系统默认币种的额度
        $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($amount, $item->exchange_rate, 0);

        GlobalEnums::init();

        return [
            'amount'                               => $default_currency_amount,
            'currency'                             => GlobalEnums::$sys_default_currency,
            'submitter_id'                         => $item->create_id,
            'department_id'                        => $item->sys_department_id,
            'node_department_id'                   => $item->node_department_id,
            'company_id'                           => $companyId,
            'cost_store'                           => $item->cost_store,
            'apply_type'                           => $item->apply_type,
            'has_asset_barcode'                    => $has_asset_barcode,
            'in_group_asset_management_department' => $in_group_asset_management,
            'in_it_department'                     => $in_it_department,
            'attachment'                           => $this->update_data['workflow_attachment'] ?? [],
            'is_cmo'                               => get_country_code() == GlobalEnums::TH_COUNTRY_CODE ? (new HrStaffRepository())->isCMO($item->create_id) : 0,//V21791-当申请人直线上级等于配置的CMO工号时该节点需要审批
        ];
    }

    /**
     * 检测申请人所属部门是否在配置项配置的部门下以及子部门下
     * @param integer $node_department_id 申请人所属部门
     * @param string $code 配置项code组
     * @return bool
     */
    public function checkInDepartment($node_department_id, $code)
    {
        $set_department_id = EnumsService::getInstance()->getSettingEnvValue($code) ?? 0;
        if (!empty($set_department_id)) {
            $department_ids = (new DepartmentService())->getChildrenListByDepartmentIdV2($set_department_id, true);
            array_push($department_ids, $set_department_id);
            return in_array($node_department_id, $department_ids);
        }
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model=null)
    {
        // TODO: Implement getFlowId() method.
    }

    /**
     * 处理更新数据
     *
     * @param $item
     * @param $edit_field
     * @param $update_data
     * @param $work_req
     * @param $user
     * @throws BusinessException
     * @throws ValidationException
     */
    public function dealEditField($item, $edit_field, $update_data, $work_req, $user)
    {
        $mainData = [];
        $logData = [];
        $logData['main'] = [];
        $logData['meta'] = [];

        $update_flag = false;

        $amount_field = [
            'wht_amount',
            'real_amount',
            'cur_amount',
            'deductible_vat_amount',
            'vat7',
            'all_total',
            'all_total_no_wht',
            'taxation',
            'amount',
            'total_amount',
            'wht_total_amount',
            'receipt_amount',
            'subtotal_amount',
            'ticket_amount',
            'ticket_amount_tax',
            'ticket_tax',
        ];
        $rate_field = [
            'wht_cate',
            'wht_rate'
        ];

        //值需要翻译的字段
        $tran_field = ['is_tax','wht_type'];

        //需要展示修改的字段
        //v12836增加了字段 'purchase_type','cno','wrs_code','vat7_rate','vat7','wht_cate','wht_rate','wht_amount','deductible_vat_tax','ticket_amount'
        $display_field = ['wht_type','wht_ratio','is_tax','ledger_account_id',
            'purchase_type','cno','wrs_code','vat7_rate','vat7','wht_cate','wht_rate','wht_amount','deductible_vat_tax','ticket_amount',
            'all_total','all_total_no_wht','taxation','amount','total_amount','wht_total_amount',
            'cur_amount','deductible_vat_amount','receipt_amount','ticket_amount_tax','ticket_tax','supplement_invoice'];

        //展示字段的翻译
        $lang_field  = ['purchase_product_field_wht_type','purchase_product_field_wht_ratio','purchase_order_field_is_tax','purchase_apply_ledger_account',
            'purchase_order_field_purchase_type','purchase_order_field_cno','purchase_order_field_wrs_code',
            'purchase_order_field_vat7_rate','purchase_order_field_vat7','purchase_order_field_wht_cate',
            'purchase_order_field_wht_rate','purchase_order_field_wht_amount',
            'purchase_payment_field_deductible_vat_tax','purchase_payment_field_ticket_amount',
            'purchase_order_edit_log_all_total','purchase_order_edit_log_all_total_no_wht',
            'purchase_order_edit_log_taxation','purchase_order_edit_log_amount','purchase_order_edit_log_total_amount',
            'purchase_order_edit_log_wht_total_amount',
            'purchase_payment_field_cur_amount','purchase_payment_field_deductible_vat_amount',
            'purchase_payment_field_receipt_amount','purchase_payment_field_ticket_amount_tax',
            'purchase_payment_field_ticket_tax','purchase_payment_field_supplement_invoice'];

        $langArr = array_combine($display_field,$lang_field);

        $whtKeyArr = EnumsService::getInstance()->getWhtRateCategoryMap();

        $model = null;
        $update_data_meta = [];
        switch ($this->blz_type){
            case Enums::WF_PURCHASE_PAYMENT:
                // 补充产品明细税率相关字段配置
                if (!empty($edit_field['meta']) && is_array($edit_field['meta'])) {
                    $rate_relate_fields = PurchaseEnums::$purchase_payment_product_rate_field_item;
                    $rate_relate_main_fields = PurchaseEnums::$purchase_payment_product_rate_field_main;
                    foreach ($edit_field['meta'] as $meta) {

                        if (isset($rate_relate_fields[$meta])) {
                            $edit_field['meta'] = array_merge($edit_field['meta'], $rate_relate_fields[$meta]);
                        }

                        //总计等金额受行数影响相关字段且主数据未配置编辑
                        if(isset($rate_relate_main_fields[$meta])&&$rate_relate_main_fields[$meta]){
                            $edit_field['main'] = array_merge($edit_field['main']??[], $rate_relate_main_fields[$meta]);

                        }
                    }

                    $edit_field['meta'] = array_unique($edit_field['meta']);
                    $edit_field['main'] = !empty($edit_field['main']) ? array_values(array_unique($edit_field['main'])) : [];
                }

                // 补充金额总计相关字段
                if (!empty($edit_field['main']) && is_array($edit_field['main'])) {
                    $base_amount_relate_fields = PurchaseEnums::$purchase_payment_base_total_field_item;
                    foreach ($edit_field['main'] as $base_amount) {

                        if (isset($base_amount_relate_fields[$base_amount])) {
                            $edit_field['main'] = array_merge($edit_field['main'], $base_amount_relate_fields[$base_amount]);
                        }
                    }

                    $edit_field['main'] = array_values(array_unique($edit_field['main']));
                }

                //前端传过来的数据，出来下，不需要传1000
                if (!isset($update_data['receipt']) && isset($update_data['receipt_v1'])) {
                    $update_data['receipt'] = $update_data['receipt_v1'];
                    unset($update_data['receipt_v1']);
                }

                PaymentService::getInstance()->handleProductData($update_data,false, false);

                $update_data_meta = $update_data['receipt'];
                $model = new PurchasePaymentReceipt();
                break;

            //以下暂时都不能修改
            case Enums::WF_PURCHASE_ORDER:
                // 补充产品明细税率相关字段配置
                if (isset($edit_field['meta']) && is_array($edit_field['meta'])) {
                    $rate_relate_fields = PurchaseEnums::$purchase_order_product_rate_field_item;
                    $rate_relate_main_fields = PurchaseEnums::$purchase_order_product_rate_field_main;
                    foreach ($edit_field['meta'] as $meta) {
                        if (isset($rate_relate_fields[$meta])) {
                            $edit_field['meta'] = array_merge($edit_field['meta'], $rate_relate_fields[$meta]);
                        }

                        //总计等金额受行数影响相关字段且主数据未配置编辑
                        if (isset($rate_relate_main_fields[$meta]) && $rate_relate_main_fields[$meta]) {
                            $edit_field['main'] = array_merge($edit_field['main'] ?? [], $rate_relate_main_fields[$meta]);
                        }
                    }

                    $edit_field['meta'] = array_values(array_unique($edit_field['meta']));
                    $edit_field['main'] = !empty($edit_field['main']) ? array_values(array_unique($edit_field['main'])) : [];
                }

                // 补充金额总计相关字段
                if (isset($edit_field['main']) && is_array($edit_field['main'])) {
                    $base_amount_relate_fields = PurchaseEnums::$purchase_order_base_total_field_item;
                    foreach ($edit_field['main'] as $base_amount) {
                        if (isset($base_amount_relate_fields[$base_amount])) {
                            $edit_field['main'] = array_merge($edit_field['main'], $base_amount_relate_fields[$base_amount]);
                        }
                    }

                    $edit_field['main'] = array_values(array_unique($edit_field['main']));
                }

                if (!isset($update_data['product']) && isset($update_data['product_v1'])) {
                    $update_data['product'] = $update_data['product_v1'];
                    unset($update_data['product_v1']);
                }

                OrderService::getInstance()->handleProductsData($update_data,false,false, false);
                $update_data_meta = $update_data['product'];
                $model = new PurchaseOrderProduct();

                break;
            case Enums::WF_PURCHASE_APPLY:
                if (!isset($update_data['products']) && isset($update_data['products_v1'])) {
                    $update_data['products'] = $update_data['products_v1'];
                    unset($update_data['products_v1']);
                }

                ApplyService::getInstance()->handleProductsData($update_data,false,false);
                $update_data_meta = $update_data['products'];
                $model = new PurchaseApplyProduct();
                break;

            case Enums::WF_PURCHASE_ACCEPTANCE:
                if (!empty($edit_field) && isset($edit_field['main']) && in_array('it_attach_files', $edit_field['main']) && !empty($update_data['it_attach_files'])) {
                    //先删除已有的
                    $db          = $this->getDI()->get('db_oa');
                    $update_bool = $db->updateAsDict(
                        (new AttachModel())->getSource(),
                        [
                            'deleted' => GlobalEnums::IS_DELETED,
                        ],
                        [
                            'conditions' => " oss_bucket_key = {$this->update_data['id']} AND oss_bucket_type =" . Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE_SU . " AND deleted =" . GlobalEnums::IS_NO_DELETED,
                        ]
                    );
                    if ($update_bool === false) {
                        throw new BusinessException('验收模块-IT附件删除失败,data=' . json_encode($this->update_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }

                    $attachments = [];
                    foreach ($update_data['it_attach_files'] as $attachment) {
                        $tmp                    = [];
                        $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_PURCHASE_ACCEPTANCE_SU;
                        $tmp['oss_bucket_key']  = $this->update_data['id'];
                        $tmp['bucket_name']     = $attachment['bucket_name'];
                        $tmp['object_key']      = $attachment['object_key'];
                        $tmp['file_name']       = $attachment['file_name'];
                        $attachments[]          = $tmp;
                    }

                    if (!empty($attachments) && (new AttachModel())->batch_insert($attachments) === false) {
                        throw new BusinessException('验收模块-IT附件创建失败,data=' . json_encode($attachments, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }
                }

        }

        $this->logger->info('purchase_audit_handled_data: ' . json_encode($update_data, JSON_UNESCAPED_UNICODE));
        if(!empty($edit_field['main'])){
            foreach ($edit_field['main'] as $key){
                //如果没有定义该参数，则不修改
                if(!isset($update_data[$key])){
                    continue;
                }

                $tmp = $update_data[$key];

                $is_amount = 0;
                if(in_array($key,$amount_field)){
                    $tmp = (string) $tmp == "" ? 0 : $tmp;
                    $tmp = bcmul(str_replace(',', '', $tmp),1000);

                    $is_amount = 1;
                }

                //不相等，才记录
                if($tmp!=$item->$key){
                    $log = [];
                    $log['need_translate'] = in_array($key,$tran_field)?1:0;
                    $log['is_display'] = in_array($key,$display_field)?1:0;
                    if($log['need_translate']){
                        if($key == 'is_tax'){
                            $log['before'] = 'purchase_order_is_tax_'.$item->$key;
                            $log['after'] = 'purchase_order_is_tax_'.$tmp;
                        }
                    }else{
                        if($is_amount){
                            $log['before'] = bcdiv($item->$key,1000,2);
                            $log['after'] = bcdiv($tmp,1000,2);
                        }else{
                            $log['before'] = $item->$key;
                            $log['after'] = $tmp;
                        }

                    }
                    $log['field_name'] = $langArr[$key]??$key;
                    $logData['main'][] = $log;

                    $update_flag = true;
                    $mainData[$key] = $tmp;
                }
            }
        }
        if(!empty($edit_field['meta']) && !empty($update_data_meta)){
            foreach ($update_data_meta as $k=>$metaArr){
                $metaItem = $model::findFirst([
                    'conditions'=>'id = ?0 and '.$this->link_field_no." = ?1",
                    'bind'=>[$metaArr['id'],$item->id]
                ]);
                if(empty($metaItem)){
                    continue;
                }

                $metaData = [];

                foreach ($edit_field['meta'] as $key){
                    //如果没定义
                    if(!isset($metaArr[$key])){
                        continue;
                    }
                    //采购员
                    if ($key == 'purchase_staff' && $this->blz_type == Enums::WF_PURCHASE_APPLY) {
                        $metaArr = ApplyService::getInstance()->saveAuditPurchaseStaff($metaItem, $metaArr);
                    }

                    $tmp = $metaArr[$key];
                    $is_amount = 0;
                    if(in_array($key,$amount_field)){
                        $tmp = (string) $tmp == "" ? 0 : $tmp;

                        $tmp = bcmul(str_replace(',', '', $tmp),1000);
                        $is_amount = 1;
                    }
                    //处理无wht 场景
                    if(in_array($key, $rate_field)){
                        $tmp = (string) $tmp == "" ? 0 : $tmp;
                    }

                    //不相等，才记录
                    if($tmp != $metaItem->$key){
                        $log = [];
                        $log['id'] = $metaItem->id;
                        $log['no'] = $k+1;
                        $log['need_translate'] = in_array($key,$tran_field)?1:0;
                        $log['is_display'] = in_array($key,$display_field)?1:0;
                        $log['field_name'] = $langArr[$key]??$key;

                        //如果需要翻译，category_a,category_b，product_id为空才能修改，否则过滤
                        if($log['need_translate']){
                            /*if(!empty($metaItem->product_id)){
                                continue;
                            }
                            if($key == 'category_a' || $key=='category_b'){
                                $log['before'] = 'purchase_product_category_name_'.$metaItem->$key;
                                $log['after'] = 'purchase_product_category_name_'.$tmp;
                            }*/
                            if($key == 'wht_type'){
                                $log['before'] = $whtKeyArr[$metaItem->$key]??"";
                                $log['after'] = $whtKeyArr[$tmp]??"";
                            }
                        }else{
                            if($is_amount){
                                $log['before'] = bcdiv($metaItem->$key,1000,2);
                                $log['after'] = bcdiv($tmp,1000,2);
                            }else{
                                $log['before'] = $metaItem->$key;
                                $log['after'] = $tmp;
                            }
                        }

                        $logData['meta'][] = $log;
                        $metaData[$key] = $tmp;
                    }
                }

                $this->logger->info('purchase_audit_update_meta_data: ' . json_encode($metaData, JSON_UNESCAPED_UNICODE));

                if(!empty($metaData)){
                    $update_flag = true;
                    if (isset($metaData['purchase_staff'])) {
                        unset($metaData['purchase_staff']);
                    }
                    if ($metaData) {
                        $metaItem->update($metaData);
                    }
                }
            }
        }

        $this->logger->info('purchase_audit_update_main_data: ' . json_encode($mainData, JSON_UNESCAPED_UNICODE));

        if(!empty($mainData)){
            $item->update($mainData);
        }

        if($update_flag){
            $log = new WorkflowUpdateLogModel();
            $save_data = [
                'request_id' => $work_req->id,
                'flow_id' => $work_req->flow_id,
                'flow_node_id' => $work_req->current_flow_node_id,
                'staff_id' => $user['id'],
                'staff_name' => $this->getNameAndNickName($user['name'],$user['nick_name']),
                'staff_department' => $user['department'],
                'staff_job_title' => $user['job_title'],
                'content' => json_encode($logData,JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s'),
            ];
            $log->save($save_data);
        }

        $this->logger->info('purchase_audit_update_log_data: ' . json_encode($save_data ?? [], JSON_UNESCAPED_UNICODE));

    }

    /**
     * 获得修改的日志
     * @param $req
     * @return array
     */
    public function getEditLog($req){
        $list =WorkflowUpdateLogModel::find(
            [
                'conditions'=>'request_id = :id:',
                'bind'=>['id'=>$req->id],
                'order' => 'id DESC'
            ]
        )->toArray();


        foreach ($list as $k => $item) {
            $logArr  = [];
            $content = json_decode($item['content'], 1);
            //检测修改数据中是否有值需要转义的
            $have_purchase_type = $have_wht_type = 0;
            foreach ($content['main'] as $check_k => $check_v) {
                //采购类型
                if ($check_v['field_name'] == 'purchase_order_field_purchase_type') {
                    $have_purchase_type = 1;
                }
                //wht类型
                if ($check_v['field_name'] == 'purchase_product_field_wht_type' || $check_v['field_name'] == 'purchase_order_field_wht_cate') {
                    $have_wht_type = 1;
                }
            }
            foreach ($content['meta'] as $meta_v) {
                //wht类型
                if ($meta_v['field_name'] == 'purchase_product_field_wht_type' || $meta_v['field_name'] == 'purchase_order_field_wht_cate') {
                    $have_wht_type = 1;
                }
            }
            //存在需要转义的,查询相应枚举
            $kv_purchase_type = $kv_wht_type = $supplement_status = [];
            if ($have_purchase_type === 1) {
                $kv_purchase_type = array_column(OrderService::getInstance()->getPurchaseType('purchase_order_type'), 'name_key', 'id');
            }

            if ($have_wht_type === 1) {
                $kv_wht_type = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            }

            $supplement_status = PurchaseEnums::$supplement_invoice_status;

            foreach ($content['main'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }
                $log               = [];
                $log['field_name'] = static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after']  = static::$t->_($vv['after']);
                } else {
                    $log['before'] = $vv['before'];
                    $log['after']  = $vv['after'];
                }
                //采购类型
                if ($vv['field_name'] == 'purchase_order_field_purchase_type') {
                    $log['before'] = $kv_purchase_type[$vv['before']] ?? $vv['before'];
                    $log['after']  = $kv_purchase_type[$vv['after']] ?? $vv['after'];
                }
                //wht类型
                if ($vv['field_name'] == 'purchase_product_field_wht_type' || $vv['field_name'] == 'purchase_order_field_wht_cate') {
                    $log['before'] = $kv_wht_type[$vv['before']] ?? $vv['before'];
                    $log['after']  = $kv_wht_type[$vv['after']] ?? $vv['after'];
                }
                if ($vv['field_name'] == 'supplement_invoice') {
                    $log['before'] = $supplement_status[$vv['before']] ? static::$t->_($supplement_status[$vv['before']]) : $vv['before'];
                    $log['after']  = $supplement_status[$vv['after']] ? static::$t->_($supplement_status[$vv['after']]) : $vv['after'];
                }
                $logArr[] = $log;
            }
            //附属表是否存在核算科目
            $ledger_account_ids = [];
            foreach ($content['meta'] as $kk => $vv) {
                if ($vv['field_name'] == 'purchase_apply_ledger_account') {
                    if (!empty($vv['before']) && !in_array($vv['before'], $ledger_account_ids)) {
                        $ledger_account_ids[] = $vv['before'];
                    }
                    if (!empty($vv['after']) && !in_array($vv['after'], $ledger_account_ids)) {
                        $ledger_account_ids[] = $vv['after'];
                    }
                }
            }

            $ledgerIdToName = [];
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'], 'name', 'id');
                }
            }
            //附属表字段翻译
            foreach ($content['meta'] as $kk => $vv) {
                if ($vv['is_display'] == 0) {
                    continue;
                }
                $log               = [];
                $log['field_name'] = $vv['no'] . "-" . static::$t->_($vv['field_name']);
                if ($vv['need_translate']) {
                    $log['before'] = static::$t->_($vv['before']);
                    $log['after']  = static::$t->_($vv['after']);
                } else {
                    //税率和可抵扣税率
                    if ($vv['field_name'] == 'purchase_product_field_wht_ratio' || $vv['field_name'] == 'purchase_payment_field_deductible_vat_tax'
                        || $vv['field_name'] == 'purchase_order_field_vat7_rate' || $vv['field_name'] == 'purchase_order_field_wht_rate') {
                        $log['before'] = $vv['before'] . "%";
                        $log['after']  = $vv['after'] . "%";
                    } //核算科目
                    elseif ($vv['field_name'] == 'purchase_apply_ledger_account') {
                        $log['before'] = $ledgerIdToName[$vv['before']] ?? '';
                        $log['after']  = $ledgerIdToName[$vv['after']] ?? '';
                    } else {
                        $log['before'] = $vv['before'];
                        $log['after']  = $vv['after'];
                    }
                    //wht类型
                    if ($vv['field_name'] == 'purchase_product_field_wht_type' || $vv['field_name'] == 'purchase_order_field_wht_cate') {
                        $log['before'] = $kv_wht_type[$vv['before']] ?? $vv['before'];
                        $log['after']  = $kv_wht_type[$vv['after']] ?? $vv['after'];
                    }
                }
                $logArr[] = $log;
            }
            $list[$k]['log'] = $logArr;
            unset($list[$k]['content']);
        }
        return $list;
    }

    /**
     * 自定义处理审批节点可修改指定字段
     * 说明: 在指定节点可修改指定字段逻辑的定制化处理, 仅依赖workflow_node的can_edit_field字段配置已不能满足
     * @param $workflow_request
     * @param $biz_info
     * @param $can_edit_field
     *
     * @return mixed
     */
    protected function workflowNodeEditFieldsCustomizeLogic($workflow_request, $biz_info, $can_edit_field)
    {
        // 采购付款申请单
        // 相关采购申请单或相关采购订单的费用所属公司为快递公司时，审批流到达AP Supervisor（北京）时，允许修改核算科目
        if (isset($workflow_request->flow_id) && $workflow_request->flow_id == Enums::WF_PURCHASE_PAYMENT_FLOW) {
            // 是否是当前国家的快递公司
            $sys_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds(true);
            $express_company_id = $sys_company_ids['FlashExpress'] ?? '';

            // 获取付款单的费用所属公司: (依据其采购申请单 或 采购订单)
            $cost_company_id = '';
            if (!empty($biz_info->po_id)) {
                $cost_company_id = PurchaseOrder::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $biz_info->po_id],
                    'columns' => ['cost_company']
                ])->cost_company ?? '';

            } else if (!empty($biz_info->pa_id)) {
                $cost_company_id = PurchaseApply::findFirst([
                        'conditions' => 'id = :id:',
                        'bind' => ['id' => $biz_info->pa_id],
                        'columns' => ['cost_company_id']
                ])->cost_company_id ?? '';
            }

            // 当前国家快递公司为空 或 费用所用公司为空 或 公司不一致, 均不能修改核算科目字段
            if (empty($express_company_id) || empty($cost_company_id) || $express_company_id != $cost_company_id) {
                $ledger_account_id_key = array_search('ledger_account_id', $can_edit_field['meta'] ?? []);
                if ($ledger_account_id_key !== false) {
                    unset($can_edit_field['meta'][$ledger_account_id_key]);
                }
            }
        }

        return $can_edit_field;
    }


    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function recommit($item,$user){
        $req = $this->getRequest($item);
        if(empty($req)){
            throw new BusinessException("没有找到req=".$item);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->save();

        return $this->createRequest($item,$user);
    }

    public function mailContent($acceptance_no){
        $html  = "<p>Dear all,<?p></br></br>";
        $html .= "<p>Please kindly notice that receiving report Number:".$acceptance_no. "has been approved in OA system.</p></br></br>";
        $html .= "<p>  Thanks!</p>";

        return $html;
    }


}
