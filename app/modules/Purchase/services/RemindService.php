<?php
namespace App\Modules\Purchase\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\WorkflowPendingEnums;
use App\Models\oa\WorkflowRequestModel;
use App\Models\oa\WorkflowNodeModel;
use App\Models\oa\WorkflowRequestNodeAuditorModel;
use App\Models\oa\WorkflowRequestNodeFYR;
use App\Models\oa\WorkflowRequestNodeAt;
use App\Models\oa\WorkflowSubNodeModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\SmsService;
use App\Modules\Common\Services\WorkflowPendingService;
use App\Modules\User\Models\StaffInfoModel;
use App\Repository\HrStaffRepository;

/**
 * 采购审批-提醒服务层
 * Class RemindService
 * @package App\Modules\Purchase\Services
 */
class RemindService extends BaseService
{
    const SEND_MSG_TYPE_PENDING = 1;//给采购经理/集团采购总监/资产经理节点审批人发送短信
    const SEND_MSG_TYPE_FINANCE_PENDING = 2;//给财务各节点审批人发送短信
    const SEND_MSG_TYPE_PURCHASE_DEPARTMENT = 3;//采购领导
    const SEND_MSG_TYPE_ASSET_DEPARTMENT = 4;//资产部领导
    const SEND_MSG_TYPE_FINANCE_DEPARTMENT = 5;//财务部领导

    // 各部门审批截止时间点
    public static $type_datetime = [
        self::SEND_MSG_TYPE_PURCHASE_DEPARTMENT => '15:00:00',
        self::SEND_MSG_TYPE_ASSET_DEPARTMENT => '18:00:00',
    ];

    // 各部门获取内容模板翻译key
    public static $type_email_content_key = [
        //采购部
        self::SEND_MSG_TYPE_PURCHASE_DEPARTMENT => [
            'title' => '采购部PUR&PO审批超时数据 Purchase Dept PUR&PO approval timeout data',
            'content' => [
                'zh' => '上周{country_code}采购部有{apply_num}笔PUR，{order_num}笔PO审批时效超时，请查看邮箱看详细清单。',
                'en' => 'Last week, {country_code} procurement dept had approval timeout for {apply_num} PURs and {order_num} POs. Please check your mailbox for the detailed list.'
            ]
        ],
        //资产部
        self::SEND_MSG_TYPE_ASSET_DEPARTMENT => [
            'title' => '资产部PUR审批超时数据 Asset Dept PUR approval timeout data',
            'content' => [
                'zh' => '上周{country_code}资产部有{apply_num}笔PUR审批时效超时，请查看邮箱看详细清单。',
                'en' => 'Last week, {country_code} asset dept had approval timeout for {apply_num} PURs. Please check your mailbox for the detailed list.'
            ]
        ],
        //财务部
        self::SEND_MSG_TYPE_FINANCE_DEPARTMENT => [
            'title' => '财务部PO审批超时数据 Financial Dept PO approval timeout data',
            'content' => [
                'zh' => '上周{country_code}财务部有{apply_num}笔PO审批时效超时，请查看邮箱看详细清单。',
                'en' => 'Last week, {country_code} Financial dept had approval timeout for {apply_num} POs. Please check your mailbox for the detailed list.'
            ]
        ]
    ];
    
    // 手机号黑名单
    public static $mobile_blacklist = [];

    // 黑名单配置key
    public static $mobile_blacklist_setting_key = 'workflow_pending_sms_blacklist';

    // 公共假期日期组
    public static $public_holiday_date_list = [];

    private static $instance;
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return RemindService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 资产经理/采购经理/集团采购总监[给审批人发送待审批采购申请单短信提醒]-未征询
     * @param int $biz_type 业务类型9采购申请单，10申请订单
     * @param int $node_tag 节点类型3=采购经理、10=资产经理、11=集团采购总监
     * @param string $updated_at_start 流转到该节点-起始日期
     * @param string $updated_at_end 流转到该节点-结束日期
     * @return mixed
     */
    public function getRemindPurchaseData($biz_type, $node_tag, $updated_at_start, $updated_at_end)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.current_node_auditor_id'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'node.id = request.current_flow_node_id and node.flow_id = request.flow_id', 'node');
        $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and auditor.flow_node_id = request.current_flow_node_id', 'auditor');
        $builder->leftjoin(WorkflowRequestNodeFYR::class, 'fyr.flow_node_id = request.current_flow_node_id and fyr.request_id = request.id AND fyr.flow_id = request.flow_id', 'fyr');
        $builder->where('request.biz_type = :biz_type: and request.is_abandon = :is_abandon: and request.state = :state: and auditor.audit_status = :state:', ['biz_type' => $biz_type, 'is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO, 'state' => Enums::WF_STATE_PENDING]);
        $builder->andWhere('node.node_tag = :node_tag:', ['node_tag' => $node_tag]);
        $builder->andWhere('request.updated_at > :updated_at_start: and request.updated_at <= :updated_at_end:', ['updated_at_start' => $updated_at_start, 'updated_at_end' => $updated_at_end]);
        $builder->andWhere('fyr.staff_id IS NULL');
        return $builder->getQuery()->getSingleResult()->current_node_auditor_id ?? '';
    }

    /**
     * 资产经理/采购经理/集团采购总监[给审批人发送待审批采购申请单短信提醒]-已征询
     * @param int $biz_type 业务类型9采购申请单，10申请订单
     * @param int $node_tag 节点类型3=采购经理、10=资产经理、11=集团采购总监
     * @param string $reply_at_start 征询回复-起始日期
     * @param string $reply_at_end 征询回复-结束日期
     * @return mixed
     */
    public function getRemindPurchaseFyrData($biz_type, $node_tag, $reply_at_start, $reply_at_end)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.current_node_auditor_id',
            'MAX( reply.reply_at ) AS reply_at',
            'COUNT(reply.id) as ask_num',
            'SUM(IF(reply.is_reply = 1,1,0)) as reply_num',
            'request.id'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'node.id = request.current_flow_node_id and node.flow_id = request.flow_id', 'node');
        $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and auditor.flow_node_id = request.current_flow_node_id', 'auditor');
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'reply.request_id = request.id and reply.flow_id = request.flow_id and reply.flow_node_id = request.current_flow_node_id', 'reply');
        $builder->where('request.biz_type = :biz_type: and request.is_abandon = :is_abandon: and request.state = :state: and auditor.audit_status = :state:', ['biz_type' => $biz_type, 'is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO, 'state' => Enums::WF_STATE_PENDING]);
        $builder->andWhere('node.node_tag = :node_tag: and reply.request_id IS NOT NULL', ['node_tag' => $node_tag]);
        $builder->groupBy('request.id');
        $builder->having('reply_at > :reply_at_start: and reply_at <= :reply_at_end: AND ask_num = reply_num', ['reply_at_start' => $reply_at_start, 'reply_at_end' => $reply_at_end]);
        $builder->orderBy('NULL');
        return $builder->getQuery()->getSingleResult()->current_node_auditor_id ?? '';
    }

    /**
     * 获取指定工号的手机号/工作所在国家
     * @param array $staff_ids
     * @return array
     */
    public function getStaffBaseInfo(array $staff_ids)
    {
        if (empty($staff_ids)) {
            return [];
        }

        // 获取员工基本信息
        $hr_staff_repository = new HrStaffRepository();
        $staff_list = $hr_staff_repository->getStaffListByStaffIds($staff_ids);
        // 获取员工工作所在国家
        $staff_working_country_list = $hr_staff_repository->getStaffItems($staff_ids, [StaffInfoEnums::HR_STAFF_ITEMS_WORKING_COUNTRY]);
        foreach ($staff_working_country_list as $item) {
            $staff_base_info = $staff_list[$item['staff_info_id']] ?? [];
            if (!empty($staff_base_info)) {
                $staff_list[$item['staff_info_id']]['working_country_id'] = $item['value'];
            }
        }

        return $staff_list;
    }

    /**
     * 给审批人发送短信提醒
     * @param string $current_node_auditor_id 审批人ID串
     * @param int $type 1采购经理/集团采购总监/资产经理审批人，2财务部审批人，3采购部领导，4资产部领导，5财务部领导
     * @param array $content_params 短信模版内容替换变量组
     * @return string
     */
    public function sendRemindPendingMsg($current_node_auditor_id, $type, $content_params = [])
    {
        $log = '';
        $staff_ids = explode(',', $current_node_auditor_id);
        $sys_country_code = get_country_code();

        //1.获取白名单人员
        $whitelist_staff_list = WorkflowPendingService::getInstance()->getWhitelist();
        $whitelist_staff_list = array_column($whitelist_staff_list, null, 'staff_id');

        //获取黑名单
        self::$mobile_blacklist = EnumsService::getInstance()->getSettingEnvValueIds(self::$mobile_blacklist_setting_key);

        //2.获取待办人的HRIS个人信息: 手机号/工作所在国家/在职状态
        $hris_staff_list = $this->getStaffBaseInfo($staff_ids);

        //3.判断每个待办人员情况
        foreach ($staff_ids as $staff_id) {
            //4.是否在HRIS系统中
            $staff_base_info = $hris_staff_list[$staff_id] ?? [];

            //5.不存在
            if (empty($staff_base_info)) {
                $log .= $staff_id . ' HRIS员工信息不存在;' . PHP_EOL;
                continue;
            }

            //6.在职状态不正确
            if (in_array($staff_base_info['state'], [StaffInfoEnums::STAFF_STATE_LEAVE, StaffInfoEnums::STAFF_STATE_STOP])) {
                $log .= $staff_id . ' 离职或停职状态' . PHP_EOL;
                continue;
            }

            //7.存在HRIS系统中
            $tmp_val['staff_name'] = $staff_base_info['name'];
            $tmp_val['mobile'] = trim($staff_base_info['mobile']);
            $tmp_val['country_id'] = !empty($staff_base_info['working_country_id']) ? $staff_base_info['working_country_id'] : 0;
            $tmp_val['sms_nation'] = WorkflowPendingEnums::$country_id_and_sms_nation_map[(int)$tmp_val['country_id']] ?? '';

            //8.是否在白名单
            $staff_whitelist_info = $whitelist_staff_list[$staff_id] ?? [];

            //9.存在白名单
            if (!empty($staff_whitelist_info)) {
                $tmp_val['mobile'] = trim($staff_whitelist_info['mobile']);
                $tmp_val['country_id'] = $staff_whitelist_info['country_id'];
                $tmp_val['sms_nation'] = $staff_whitelist_info['sms_nation'];
                $tmp_val['is_whitelist'] = true;
            } else if ($tmp_val['sms_nation'] != $sys_country_code) {
                //10.非白名单；工作所在国家与系统所在国家不一致: 不发送
                $log .= $staff_id . ' 工作所在国家与系统国家不一致' . PHP_EOL;
                continue;
            }

            //11.验证手机号
            if (empty($tmp_val['mobile'])) {
                $log .= $staff_id . ' 手机号为空' . PHP_EOL;
                continue;
            }

            //12.手机号不符规则(手机号为同一字符构成)
            if (mb_substr_count($tmp_val['mobile'], mb_substr($tmp_val['mobile'], 0, 1)) == mb_strlen($tmp_val['mobile'])) {
                $log .= $staff_id . ' 手机号不符规则' . PHP_EOL;
                continue;
            }

            //13.在黑名单；手机号黑名单, 在黑名单中的, 无需发送短信
            if (in_array($tmp_val['mobile'], self::$mobile_blacklist, true)) {
                $log .= $staff_id . ' 手机号在黑名单' . PHP_EOL;
                continue;
            }

            //14.验证发送日期是否是该员工的公休日，若是则不发送
            $today = date('Y-m-d');
            $is_holiday = (new HrStaffRepository())->checkIsStaffHoliday($staff_base_info, $today);
            if ($is_holiday) {
                $log .= $staff_id . ' 的'. $today . '是休息日，无需发送提醒'. PHP_EOL;
                continue;
            }

            //15.都验证过了，可以发短信了
            $send_result = $this->toSendMsg($tmp_val, $type, $content_params);

            $log .= $staff_id . ' 提醒短信发送' . ($send_result ? '成功' : '失败') . PHP_EOL;
        }
        return $log;
    }

    /**
     * 获取短信模板并发送短信
     * @param array $tmp_val 短信发送者信息组
     * @param int $type 1采购经理/集团采购总监/资产经理审批人，2财务部审批人，3采购部领导，4资产部领导，5财务部领导
     * @param array $content_params 短信模版内容替换变量组
     * @return mixed
     */
    public function toSendMsg($tmp_val, $type, $content_params)
    {
        $sms_api_param = [
            'mobile' => $tmp_val['mobile'],
            'nation' => $tmp_val['sms_nation'],
        ];

        //给采购经理/集团采购总监/资产经理审批人发送短信提醒
        if ($type == self::SEND_MSG_TYPE_PENDING) {
            //设置语种
            BaseService::setLanguage('en');

            //采购业务模块名称翻译key组
            $biz_type_arr = [
                Enums::WF_PURCHASE_APPLY => 'purchase_apply_name',
                Enums::WF_PURCHASE_ORDER => 'purchase_order_name'
            ];// 中国区短信 阿里云通道
            if ($sms_api_param['nation'] == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
                $template_code = WorkflowPendingEnums::PURCHASE_ALI_SMS_TMP_CODE_CN;
                $content_values = [
                    'biz_type' => static::$t->_($biz_type_arr[$content_params['biz_type']]),
                    'remind_time' => static::$t->_($content_params['remind_time']),
                    'country_code' => '{' . $content_params['country_code'] . '}',
                ];
                $sms_api_param['msg'] = json_encode($content_values);
                $sms_api_param['template'] = $template_code;
                $sms_api_param['service_provider'] = WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER;
                $sms_api_param['sendname'] = WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME;
            } else {
                // 其他短信通道
                $template_content = WorkflowPendingEnums::PURCHASE_SMS_CONTENT_TMP_EN;
                $content_keys = [
                    '${biz_type}',
                    '${remind_time}',
                    '${country_code}'
                ];
                $content_values = [
                    static::$t->_($biz_type_arr[$content_params['biz_type']]),
                    static::$t->_($content_params['remind_time']),
                    '{' . $content_params['country_code'] . '}'
                ];
                $sms_api_param['msg'] = str_replace($content_keys, $content_values, $template_content);
            }
        } else if ($type == self::SEND_MSG_TYPE_FINANCE_PENDING) {
            //给财务各节点审批人发送短信
            if ($sms_api_param['nation'] == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
                // 中国区短信 阿里云通道
                $template_code = WorkflowPendingEnums::PURCHASE_FINANCE_SMS_TMP_CODE_CN;
                $content_values = [
                    'country_code' => '{' . $content_params['country_code'] . '}',
                ];
                $sms_api_param['msg'] = json_encode($content_values);
                $sms_api_param['template'] = $template_code;
                $sms_api_param['service_provider'] = WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER;
                $sms_api_param['sendname'] = WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME;
            } else {
                // 其他短信通道
                $template_content = WorkflowPendingEnums::PURCHASE_FINANCE_SMS_CONTENT_TMP_EN;
                $content_keys = [
                    '${country_code}'
                ];
                $content_values = [
                    '{' . $content_params['country_code'] . '}'
                ];
                $sms_api_param['msg'] = str_replace($content_keys, $content_values, $template_content);
            }
        } else if ($type == self::SEND_MSG_TYPE_PURCHASE_DEPARTMENT) {
            //给采购部领导发送短信
            if ($sms_api_param['nation'] == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
                // 中国区短信 阿里云通道
                $template_code = WorkflowPendingEnums::PURCHASE_LEADER_ALI_SMS_TMP_CODE_CN;
                $content_values = [
                    'country_code' => '{' . $content_params['country_code'] . '}',
                    'apply_num' => '{' . $content_params['apply_num'] . '}',
                    'order_num' => '{' . $content_params['order_num'] . '}'
                ];
                $sms_api_param['msg'] = json_encode($content_values);
                $sms_api_param['template'] = $template_code;
                $sms_api_param['service_provider'] = WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER;
                $sms_api_param['sendname'] = WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME;
            } else {
                // 其他短信通道
                $template_content = WorkflowPendingEnums::PURCHASE_LEADER_SMS_CONTENT_TMP_EN;
                $content_keys = [
                    '${country_code}',
                    '${apply_num}',
                    '${order_num}'
                ];
                $content_values = [
                    '{' . $content_params['country_code'] . '}',
                    '{' . $content_params['apply_num'] . '}',
                    '{' . $content_params['order_num'] . '}'
                ];
                $sms_api_param['msg'] = str_replace($content_keys, $content_values, $template_content);
            }
        } else if (in_array($type, [self::SEND_MSG_TYPE_ASSET_DEPARTMENT, self::SEND_MSG_TYPE_FINANCE_DEPARTMENT])) {
            //给资产部/财务部领导发送短信
            if ($sms_api_param['nation'] == WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
                // 中国区短信 阿里云通道[根据部门不同找不同的阿里云模板]
                $template_code = ($type == self::SEND_MSG_TYPE_ASSET_DEPARTMENT) ? WorkflowPendingEnums::PURCHASE_ASSET_ALI_SMS_TMP_CODE_CN : WorkflowPendingEnums::PURCHASE_FINANCE_LEADER_ALI_SMS_TMP_CODE_CN;
                $content_values = [
                    'country_code' => '{' . $content_params['country_code'] . '}',
                    'apply_num' => '{' . $content_params['apply_num'] . '}'
                ];
                $sms_api_param['msg'] = json_encode($content_values);
                $sms_api_param['template'] = $template_code;
                $sms_api_param['service_provider'] = WorkflowPendingEnums::ALI_SMS_CN_SERVICE_PROVIDER;
                $sms_api_param['sendname'] = WorkflowPendingEnums::ALI_SMS_CN_SEND_NAME;
            } else {
                // 其他短信通道[根据部门不同找不同的模板]
                $template_content = ($type == self::SEND_MSG_TYPE_ASSET_DEPARTMENT) ? WorkflowPendingEnums::PURCHASE_ASSET_SMS_CONTENT_TMP_EN : WorkflowPendingEnums::PURCHASE_FINANCE_LEADER_SMS_CONTENT_TMP_EN;
                $content_keys = [
                    '${country_code}',
                    '${apply_num}'
                ];
                $content_values = [
                    '{' . $content_params['country_code'] . '}',
                    '{' . $content_params['apply_num'] . '}'
                ];
                $sms_api_param['msg'] = str_replace($content_keys, $content_values, $template_content);
            }
        }
        //符合条件 viber 发送
        if (get_country_code() == GlobalEnums::PH_COUNTRY_CODE
            && !isset($tmp_val['is_whitelist'])
            && $sms_api_param['nation'] != WorkflowPendingEnums::ALI_SMS_CN_CHANNEL_CODE) {
            $sms_api_param['type']             = 0;
            $sms_api_param['service_provider'] = 9;// 服务商 Viber 固定值
            $sms_api_param['nation']           = GlobalEnums::PH_COUNTRY_CODE;
        }
        return SmsService::getInstance()->send($sms_api_param, 'oa_purchase_remind_pending_task');
    }

    /**
     * 获取需要提醒采购订单列表【采购部领导、资产部领导】
     * @param array $biz_type 业务类型9采购申请单，10申请订单
     * @param array $node_tag 节点类型3=采购经理、10=资产经理、11=集团采购总监
     * @param int $type 1采购经理/集团采购总监/资产经理审批人，2财务部审批人，3采购部领导，4资产部领导，5财务部领导
     * @return array
     */
    public function getRemindPurchaseList($biz_type, $node_tag, $type)
    {
        $purchase_list = [];

        //获取上周一到周日的时间戳组
        $last_week_days = $this->getLastWeekDays();
        foreach ($last_week_days as $key => $date_timestamp) {
            //到达审批时间格式[采购部是15点，资产部是18点]
            $arrival_approve_time_format = self::$type_datetime[$type];
            $audit_approve_at = strtotime('+1 day', $date_timestamp);//审核完成时间

            //当天00~下午15点/18点之前到达的审批且审核完成时间不在当天晚上24点之前，若当天是公休日，则应该是公休日的次个工作日上午12点之后
            $arrival_approve_at_before_start = date('Y-m-d 00:00:00', $date_timestamp);//到达审批-起始日期
            $arrival_approve_at_before_end = date('Y-m-d ' . $arrival_approve_time_format, $date_timestamp);//到达审批-截止日期[当天的15点/18点]
            $audit_approve_time_before_format = '00:00:00';//审批完成时间格式

            //当天15点/18点-24点到达的审批且审核完成时间在次日上午12点之后，若遇上公休日，则应该是公休日的次个工作日上午12点之后
            $arrival_approve_at_after_start = date('Y-m-d ' . $arrival_approve_time_format, $date_timestamp);//到达审批-起始日期
            $arrival_approve_at_after_end = date('Y-m-d 00:00:00', $audit_approve_at);//到达审批-截止日期[第二天的00点]
            $audit_approve_time_after_format = '12:00:00';//审批完成时间格式

            //获取审批时间：检测后一天的工作是否是公休日；若一直是公休日则一直找到非公休日的工作日为止
            $is_holiday = (new HrStaffRepository())->checkIsHoliday(date('Y-m-d', $audit_approve_at));
            while ($is_holiday) {
                $audit_approve_at = strtotime('+1 day',  $audit_approve_at);
                $audit_approve_time_before_format = '12:00:00';
                $date = date('Y-m-d', $audit_approve_at);
                //将假期日期放入假期日期组里
                array_push(self::$public_holiday_date_list, $date);
                $is_holiday = (new HrStaffRepository())->checkIsHoliday($date);
            }

            //审批通过时间
            $audit_before_at = date('Y-m-d ' . $audit_approve_time_before_format, $audit_approve_at);
            $audit_after_at = date('Y-m-d ' . $audit_approve_time_after_format, $audit_approve_at);

            //没有发起过征询[当天00~下午15/18点之前]
            $before_list = $this->getRemindPurchaseLeaderData($biz_type, $node_tag, $arrival_approve_at_before_start, $arrival_approve_at_before_end, $audit_before_at);
            //没有发起过征询[当天15/18点-24点]
            $after_list = $this->getRemindPurchaseLeaderData($biz_type, $node_tag, $arrival_approve_at_after_start, $arrival_approve_at_after_end, $audit_after_at);

            //发起过征询[当天00~下午15/18点之前]
            $ask_before_list = $this->getRemindPurchaseLeaderFyrData($biz_type, $node_tag, $arrival_approve_at_before_start, $arrival_approve_at_before_end, $audit_before_at);
            //发起过征询[当天15/18点-24点]
            $ask_after_list = $this->getRemindPurchaseLeaderFyrData($biz_type, $node_tag, $arrival_approve_at_after_start, $arrival_approve_at_after_end, $audit_after_at);

            //把上周一到周日的每一天找到的采购清单放到最终的采购清单组里
            $one_day_list = array_merge($before_list, $after_list, $ask_before_list, $ask_after_list);
            $one_day_list ? array_push($purchase_list, $one_day_list): $purchase_list;
        }

        //需要将上周一到周日的每一天的数据放到一个组里，不按照天数分开
        $final_purchase_list = [];
        $apply_num = 0;
        if ($purchase_list) {
            foreach ($purchase_list as $day_list) {
                foreach ($day_list as $item) {
                    $final_purchase_list[] = $item;
                }
            }
            //因为单个申请单的审批人会有多个，短信统计多少笔时不可重复
            $apply_num = count(array_unique(array_filter(array_column($final_purchase_list, 'no'))));
        }

        return ['apply_num' => $apply_num, 'purchase_list' => $final_purchase_list];
    }
    
    /**
     * 获取上一周周一到周日的所有日期时间戳
     * @return array
     */
    public function getLastWeekDays()
    {
        // 获取当前日期本周周一的时间戳
        $current_monday_timestamp = strtotime('this week Monday');

        // 获取上一周的周一的时间戳
        $last_monday_timestamp = strtotime('-1 week', $current_monday_timestamp);

        // 获取上一周的周日的时间戳
        $last_sunday_timestamp = strtotime('-1 days', $current_monday_timestamp);

        // 循环获取上周周一到周日的所有日期
        $current_date = $last_monday_timestamp;
        $days = [];
        while ($current_date <= $last_sunday_timestamp) {
            array_push($days, $current_date);
            $current_date = strtotime('+1 day', $current_date);
        }
        return $days;
    }

    /**
     * 资产经理/采购经理/集团采购总监-未征询-清单列表
     * @param array $biz_type 业务类型9采购申请单，10申请订单
     * @param array $node_tag 节点类型3=采购经理、10=资产经理、11=集团采购总监
     * @param string $created_at_start 流转到该节点-起始时间
     * @param string $created_at_end 流转到该节点-截止时间
     * @param string $audit_approve_at 该节点审批通过时间
     * @return mixed
     */
    public function getRemindPurchaseLeaderData($biz_type, $node_tag, $created_at_start, $created_at_end, $audit_approve_at)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.name, auditor.auditor_id, auditor.created_at, auditor.audit_at'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'node.flow_id = request.flow_id', 'node');
        $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and auditor.flow_node_id = node.id', 'auditor');
        $builder->leftjoin(WorkflowRequestNodeFYR::class, 'fyr.request_id = request.id AND fyr.flow_id = request.flow_id and fyr.flow_node_id = node.id', 'fyr');
        $builder->where('request.is_abandon = :is_abandon: and auditor.audit_status = :state:', ['is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO, 'state' => Enums::WF_STATE_APPROVED]);
        $builder->inWhere('request.biz_type', $biz_type);
        $builder->inWhere('node.node_tag', $node_tag);
        $builder->andWhere('auditor.created_at > :created_at_start: and auditor.created_at <= :created_at_end:', ['created_at_start' => $created_at_start, 'created_at_end' => $created_at_end]);
        $builder->andWhere('auditor.audit_at > :audit_at:', ['audit_at' => $audit_approve_at]);
        $builder->andWhere('fyr.staff_id IS NULL');
        $list = $builder->getQuery()->execute()->toArray();
        return $this->formatRemindPurchaseLeaderData($list);
    }

    /**
     * 资产经理/采购经理/集团采购总监-已征询-清单列表
     * @param array $biz_type 业务类型9采购申请单，10申请订单
     * @param array $node_tag 节点类型3=采购经理、10=资产经理、11=集团采购总监
     * @param string $reply_at_start 征询回复完成-起始时间
     * @param string $reply_at_end 征询回复完成-截止时间
     * @param string $audit_approve_at 该节点审批通过时间
     * @return mixed
     */
    public function getRemindPurchaseLeaderFyrData($biz_type, $node_tag, $reply_at_start, $reply_at_end, $audit_approve_at)
    {
        //第一步需要先获取在这个时间段内征询完毕的所有采购清单
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.id, request.name, MAX( reply.reply_at ) AS reply_at, COUNT(reply.id) as ask_num, SUM(IF(is_reply = 1,1,0)) as reply_num'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'node.flow_id = request.flow_id', 'node');
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'reply.request_id = request.id and reply.flow_id = request.flow_id and reply.flow_node_id = node.id', 'reply');
        $builder->where('request.is_abandon = :is_abandon:', ['is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO]);
        $builder->inWhere('request.biz_type', $biz_type);
        $builder->inWhere('node.node_tag', $node_tag);
        $builder->andWhere('reply.request_id IS NOT NULL');
        $builder->groupBy('request.id');
        $builder->having('ask_num = reply_num AND reply_at > :reply_at_start: and reply_at <= :reply_at_end:', ['reply_at_start' => $reply_at_start, 'reply_at_end' => $reply_at_end]);
        $ask_list = $builder->getQuery()->execute()->toArray();

        $list = [];
        if (!empty($ask_list)) {
            //第二步根据上述找到采购清单request_id组且在这些节点审批通过且审批完成时间在之后的
            $request_ids = array_column($ask_list, 'id');
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'auditor.request_id, auditor.auditor_id, auditor.created_at, auditor.audit_at '
            ]);
            $builder->from(['auditor' => WorkflowRequestNodeAuditorModel::class]);
            $builder->leftjoin(WorkflowNodeModel::class, 'node.id = auditor.flow_node_id', 'node');
            $builder->where('auditor.audit_status = :audit_status: and auditor.audit_at > :audit_at:', ['audit_status' => Enums::WF_STATE_APPROVED, 'audit_at' => $audit_approve_at]);
            $builder->inWhere('auditor.request_id', $request_ids);
            $builder->inWhere('node.node_tag', $node_tag);
            $list = $builder->getQuery()->execute()->toArray();
            if (!empty($list)) {
                $request_name_list = array_column($ask_list, 'name', 'id');
                foreach ($list as &$item) {
                    $item['name'] = $request_name_list[$item['request_id']] ?? '';
                }
            }
        }
        return $this->formatRemindPurchaseLeaderData($list);
    }

    /**
     * 格式化采购【申请单、订单】列表
     * @param array $list 采购单列表
     * @return array
     */
    private function formatRemindPurchaseLeaderData($list)
    {
        $remind_purchase_list = [];
        if (!empty($list)) {
            //获取所有审批人的信息列表
            $auditor_ids = array_values(array_unique(array_filter(array_column($list, 'auditor_id'))));
            $auditor_list = (new HrStaffRepository())->getStaffListByStaffIds($auditor_ids);

            foreach ($list as $item) {
                //审批单号
                preg_match('/[a-zA-Z\d]+/', $item['name'], $match);
                $remind_purchase_list[] = [
                    'no' => $match[0] ?? '',//单据号
                    'auditor_id' => $item['auditor_id'],//审批人工号
                    'auditor_name' => $auditor_list[$item['auditor_id']]['name'] ?? '', //审批人姓名
                    'created_at' => $item['created_at'],//开始审批时间
                    'audit_at' => $item['audit_at']//完成审批时间
                ];
            }
        }
        return $remind_purchase_list;
    }

    /**
     * 发送邮件至采购部/资产部/财务部
     * @param array $staff_ids 员工工号组
     * @param array $purchase_list 提醒采购订单列表
     * @param int $type 1采购经理/集团采购总监/资产经理审批人，2财务部审批人，3采购部领导，4资产部领导，5财务部领导
     * @param array $content_params 短信模版内容替换变量组
     * @return string
     * @throws \App\Library\Exception\BusinessException
     */
    public function sendEmailToLeader($staff_ids, $purchase_list, $type, $content_params)
    {
        $log = '';
        $emails = (new StaffInfoModel())->getEmails($staff_ids);
        if (!empty($emails)) {
            $header = [
                '单据号 Document NO.',
                '审批人工号 Employee ID',
                '姓名 Name',
                '开始审批时间 Start approval time',
                '完成审批时间 Approval completion time'
            ];
            $rows_value = [];
            foreach ($purchase_list as $item) {
                $rows_value[] = [
                    $item['no'],
                    $item['auditor_id'],
                    $item['auditor_name'],
                    $item['created_at'],
                    $item['audit_at']
                ];
            }

            $file_name = get_country_code() . '-' . 'Limitation of approval' . '-' . date('Ymd');
            $oss_upload_res = RemindService::getInstance()->exportExcel($header, $rows_value, $file_name);
            if (!empty($oss_upload_res['code']) && $oss_upload_res['code'] == ErrCode::$SUCCESS) {
                $oss_file_url = $oss_upload_res['data'];
                //获取邮件内容
                $email_data = self::$type_email_content_key[$type];
                //获取邮件标题
                $title = $email_data['title'];

                $content_keys = array_keys($content_params);
                $first_content = str_replace($content_keys, $content_params, $email_data['content']['zh']);
                $second_content = str_replace($content_keys, $content_params, $email_data['content']['en']);

                $html = <<<EOF
    {$first_content}<a href="{$oss_file_url}" target='_blank'>download</a><br/>
    {$second_content}<a href="{$oss_file_url}" target='_blank'>download</a>

EOF;
                //发送邮件
                $send_res = $this->mailer->sendAsync($emails, $title, $html);
                $log .= '邮件发送【' . ($send_res ? '成功' : '失败') . '】! ' . json_encode(['emails' => $emails, 'title' => $title, 'html' => $html], JSON_UNESCAPED_UNICODE) . PHP_EOL;
            }
        }
        return $log;
    }

    /**
     * 获取各财务节点待审批人
     * @return string
     */
    public function getRemainFinancePurchaseData()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.id, request.current_node_auditor_id, request.current_flow_node_id, auditor.created_at, sub.node_tag'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and auditor.flow_node_id = request.current_flow_node_id', 'auditor');
        $builder->leftjoin(WorkflowSubNodeModel::class, 'sub.id = auditor.sub_node_id', 'sub');
        $builder->where('request.is_abandon = :is_abandon: and request.state = :state: and request.biz_type = :biz_type:', ['is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO, 'state' => Enums::WF_STATE_PENDING, 'biz_type' => Enums::WF_PURCHASE_ORDER]);
        $builder->inWhere('request.flow_id', Enums::$WF_PURCHASE_ORDER_FLOW_IDS);
        $builder->inWhere('sub.node_tag', Enums::$FINANCE_WF_NODE_TAGS);
        $builder->andWhere('sub.auditor_id is not NULL');
        $builder->andWhere('auditor.audit_status = :audit_status: and auditor.sub_node_id > 0', ['audit_status' => Enums::WF_STATE_PENDING]);
        $builder->andWhere('auditor.created_at <= :created_at_end:', ['created_at_end' => date('Y-m-d H:00:00')]);
        $list = $builder->getQuery()->execute()->toArray();

        // 同个审批请求，财务各子节点会签的审批人在current_node_auditor_id值是一样的，
        // 通过mysql按照request.id分组会引起临时表使用担心出现慢查询
        // 所以这里采取获取所有待审批会签子节点，然后通过程序按照审批请求分组
        $final_list = [];
        if (!empty($list)) {
            foreach ($list as $item) {
                if (isset($final_list[$item['id']])) {
                    continue;
                } else {
                    $final_list[$item['id']] = $item;
                }
            }

            //过滤掉财务节点待审批的单据中待回复的审批单据[待回复的单据无需提醒]
            $final_list = $this->getWaitReplyFyr($final_list);
        }
        return $this->formatRemainFinancePurchaseData($final_list);
    }

    /**
     * 获取需要短信提醒的审批人
     * @param array $list
     * @return string
     */
    private function formatRemainFinancePurchaseData($list)
    {
        $current_node_auditor_id = '';
        if (empty($list)) {
            return $current_node_auditor_id;
        }
        foreach ($list as $item) {
            if (in_array($item['node_tag'], [Enums::WF_NODE_TAG_AP_LOCAL, Enums::WF_NODE_TAG_AP_BEIJING, Enums::WF_NODE_TAG_TAX_LOCAL, Enums::WF_NODE_TAG_TAX_BEIJING])) {
                //AP 节点 待审批；node_tag in (5,6,7,8)表示AP节点
                $self_hours = $this->getSelfApproveHours($item['created_at'], $item['id'], $item['current_flow_node_id']);//自身审批时效

                //AP节点只有自身审批时效
                $approval_hours = $self_hours;
            } else if (in_array($item['node_tag'], [Enums::WF_NODE_TAG_APS_BEIJING, Enums::WF_NODE_TAG_APS_LOCAL])) {
                // APS（财务经理）节点待审批；node_tag (4,9)表示APS（财务经理）节点
                $self_hours = $this->getSelfApproveHours($item['created_at'], $item['id'], $item['current_flow_node_id']);//自身审批时效

                //获取AP财务节点审批时效
                $request_prev_node_approval_data = $this->getPrevNodeApprovalData([$item['id']], [Enums::WF_NODE_TAG_AP_LOCAL, Enums::WF_NODE_TAG_AP_BEIJING, Enums::WF_NODE_TAG_TAX_LOCAL, Enums::WF_NODE_TAG_TAX_BEIJING]);

                //财务经理 = AP审批时效 + 自身审批时效
                $approval_hours = bcadd($self_hours, ($request_prev_node_approval_data['hours'][$item['id']] ?? 0), 5);
            } else if (in_array($item['node_tag'], [Enums::WF_NODE_TAG_APD_BEIJING, Enums::WF_NODE_TAG_APD_LOCAL])) {
                // APD（财务总监）节点待审批；node_tag in (12,13)表示APD（财务总监）节点
                $self_hours = $this->getSelfApproveHours($item['created_at'], $item['id'], $item['current_flow_node_id']);//自身审批时效

                //获取AP财务+财务经理节点审批时效
                $request_prev_node_approval_data = $this->getPrevNodeApprovalData([$item['id']], [Enums::WF_NODE_TAG_AP_LOCAL, Enums::WF_NODE_TAG_AP_BEIJING, Enums::WF_NODE_TAG_TAX_LOCAL, Enums::WF_NODE_TAG_TAX_BEIJING, Enums::WF_NODE_TAG_APS_BEIJING, Enums::WF_NODE_TAG_APS_LOCAL]);

                //财务总监 = AP+财务经理总审批时效 + 自身审批时效
                $approval_hours = bcadd($self_hours, ($request_prev_node_approval_data['hours'][$item['id']] ?? 0), 5);
            } else {
                //不合法的节点数据，略过
                continue;
            }
            $this->logger->info('remind_finance 获取财务各节点待审批超时时效：' . $approval_hours . ' 小时');
            //获取到每笔审批请求最终的审批小时数，判断是否符合23H < 审批时效 < 24H
            if (bccomp($approval_hours, 23, 5) === 1 && bccomp($approval_hours, 24, 5) === -1) {
                $current_node_auditor_id = $item['current_node_auditor_id'];
                break;
            } else {
                continue;
            }
        }
        return $current_node_auditor_id;
    }

    /**
     * 自身审批时效 = 审批小时数(发短信时间 - 流转到节点时间(created_at) - 节假日) - C(意见征询)
     * @param string $approval_arrival_at 流转到节点时间
     * @param integer $request_id 审批请求id
     * @param intger $flow_node_id 节点id
     * @return false|float|int
     */
    private function getSelfApproveHours($approval_arrival_at, $request_id, $flow_node_id)
    {
        $hours = $this->getApproveHours($approval_arrival_at, date('Y-m-d H:i:s'));
        $request_fyr_hours = $this->getFyrHours([$request_id], [$flow_node_id]);
        $hours = bcsub($hours, $request_fyr_hours[$request_id] ?? 0, 5);
        return $hours;
    }

    /**
     * 获取每个审批请求已审批小时数组、涉及到的财务审批节点组（主）
     * @param array $request_ids 审批请求id组
     * @param array $node_tag 节点枚举组
     * @return array
     */
    private function getPrevNodeApprovalData($request_ids, $node_tag)
    {
        $request_hours_and_node_ids = [
            'hours' => [],
            'flow_node_ids' => [],
        ];
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request_id, auditor.flow_node_id, MIN(auditor.created_at) as created_at, MAX(auditor.audit_at) as audit_at'
        ]);
        $builder->from(['auditor' => WorkflowRequestNodeAuditorModel::class]);
        $builder->leftjoin(WorkflowSubNodeModel::class, 'sub.id = auditor.sub_node_id', 'sub');
        $builder->inWhere('request_id', $request_ids);
        $builder->inWhere('auditor.audit_status', [Enums::WF_STATE_APPROVED, Enums::WF_STATE_AUTO_APPROVED]);
        $builder->andWhere('auditor.sub_node_id > 0 ');
        $builder->inWhere('sub.node_tag', $node_tag);
        $builder->andWhere('sub.auditor_id is not NULL');
        $builder->groupBy('request_id, flow_node_id');
        $list = $builder->getQuery()->execute()->toArray();
        if (!empty($list)) {
            foreach ($list as $item) {
                //需要将审批的每个财务节点已审批的时效累计和（AP、财务经理、财务总监）【刨除节假日】
                $request_hours_and_node_ids['hours'][$item['request_id']] = $request_hours_and_node_ids['hours'][$item['request_id']] ?? 0;
                $request_hours_and_node_ids['hours'][$item['request_id']] = bcadd($request_hours_and_node_ids['hours'][$item['request_id']], $this->getApproveHours($item['created_at'], $item['audit_at']), 5);
            }
            //将所有审批节点汇总
            $request_hours_and_node_ids['flow_node_ids'] = array_values(array_unique(array_filter(array_column($list, 'flow_node_id'))));
        }
        $this->logger->info('remind_finance_department 提醒财务部领导，获取财务各审批节点审批时效结果：' . json_encode($request_hours_and_node_ids, JSON_UNESCAPED_UNICODE));
        return $request_hours_and_node_ids;
    }

    /**
     * 获取审批小时数 = 最终审批通过时间-流转到该节点的会签时间 - 节假日
     * @param string $approval_arrival_at  流转到节点时间
     * @param string $audit_at 审批时间
     * @return false|float|int
     */
    private function getApproveHours($approval_arrival_at, $audit_at)
    {
        $hours = $this->diffDateHours($approval_arrival_at, $audit_at);
        //存在审批小时差，才需要去看看这段时间内有没有公休日
        if ($hours > 0) {
            // 循环获取日期
            $start_timestamp = strtotime($approval_arrival_at);
            $end_timestamp = strtotime($audit_at);
            $hr_staff_repository = new HrStaffRepository();
            $current_timestamp = $start_timestamp;
            while ($current_timestamp <= $end_timestamp) {
                $current_date = date('Y-m-d', $current_timestamp);
                //每遇到一个公休日，审批时效-1天（24小时）
                if ($hr_staff_repository->checkIsHoliday($current_date)) {
                    $hours = bcsub($hours, 24, 5);
                }
                // 将当前日期增加1天
                $current_timestamp = strtotime('+1 day', $current_timestamp);
            }
        }
        return $hours;
    }

    /**
     * 获取两个时间之间的小时差(保留5位小数，不四舍五入)
     * @param string $start_time 起止时间
     * @param string $end_time 截止时间
     * @return string|null
     */
    private function diffDateHours($start_time, $end_time)
    {
        $start_timestamp = strtotime($start_time);
        $end_timestamp = strtotime($end_time);
        return bcdiv(($end_timestamp - $start_timestamp) , 3600, 5);
    }

    /**
     * 过滤掉财务节点待审批的单据中待回复的审批单据
     * @param array $final_list 财务节点待审批的单据
     * @return mixed
     */
    private function getWaitReplyFyr($final_list)
    {
        $request_ids = array_keys($final_list);
        $flow_node_ids = array_column($final_list, 'current_flow_node_id');
        $wait_ask_final_list = WorkflowRequestNodeAt::find([
            'columns' => 'request_id, flow_node_id',
            'conditions' => 'is_reply = :reply: and request_id in ({request_ids:array}) and flow_node_id in ({flow_node_id:array})',
            'bind' => ['reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING, 'request_ids' => $request_ids, 'flow_node_id' => $flow_node_ids],
        ])->toArray();
        if (!empty($wait_ask_final_list)) {
            foreach ($wait_ask_final_list as $item) {
                if (isset($final_list[$item['request_id']])) {
                    unset($final_list[$item['request_id']]);
                }
            }
        }
        return $final_list;
    }


    /**
     * 获取每个审批请求已征询小时数组
     * 某节点在某申请审批里意见征询时效换算为小时数 C = 自身这个主节点的征询时间 = 计算最晚的回复时间-最早发起的时间
     * @param array $request_ids 审批请求id
     * @param array $flow_node_ids 节点id
     * @return array
     */
    private function getFyrHours($request_ids, $flow_node_ids)
    {
        $request_hours = [];
        $fyr_list = WorkflowRequestNodeAt::find([
            'columns' => 'request_id, flow_node_id, MIN(created_at) as created_at, MAX(reply_at) as reply_at',
            'conditions' => 'is_reply = :reply: and reply_at IS NOT NULL and request_id in ({request_ids:array}) and flow_node_id in ({flow_node_id:array})',
            'bind' => ['reply' => GlobalEnums::CONSULTED_REPLY_STATE_PROCESSED, 'request_ids' => $request_ids, 'flow_node_id' => $flow_node_ids],
            'group' => 'request_id, flow_node_id'
        ])->toArray();
        if (!empty($fyr_list)) {
            foreach ($fyr_list as $item) {
                //需要将审批的每个财务节点已征询的时效累计和（AP、财务经理、财务总监）
                $request_hours[$item['request_id']] = $request_hours[$item['request_id']] ?? 0;
                $request_hours[$item['request_id']] = bcadd($request_hours[$item['request_id']], $this->diffDateHours($item['created_at'], $item['reply_at']), 5);
            }
        }
        return $request_hours;
    }

    /**
     * 每周一上午当地时间10点给财务部领导发短信，发邮件
     * 财务总监节点的审批通过：AP（TH）子节点会签的审批时效+财务经理会签的审批时效+财务总监会签的审批时效>24H，这种数据需要发。
     * 时间范围:上周一  00:00:00 到上周日 23:59:59
     * @return array
     */
    public function getFinancePurchaseLeaderList()
    {
        $remind_leader_purchase_data = [
            'apply_num' => 0,
            'purchase_list' => []
        ];
        // 获取当前日期本周周一的时间戳
        $current_monday_timestamp = strtotime('this week Monday');
        // 获取上一周的周一的时间戳
        $last_monday_timestamp = strtotime('-1 week', $current_monday_timestamp);

        //第一步先获取财务总监已审批通过的PO单
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'request.id'
        ]);
        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and request.current_flow_node_id > auditor.flow_node_id', 'auditor');
        $builder->leftjoin(WorkflowSubNodeModel::class, 'sub.id = auditor.sub_node_id', 'sub');
        $builder->where('request.is_abandon = :is_abandon: and request.biz_type = :biz_type:', ['is_abandon' =>  GlobalEnums::WORKFLOW_ABANDON_STATE_NO, 'biz_type' => Enums::WF_PURCHASE_ORDER]);
        $builder->inWhere('request.flow_id', Enums::$WF_PURCHASE_ORDER_FLOW_IDS);
        $builder->inWhere('sub.node_tag', [Enums::WF_NODE_TAG_APS_BEIJING, Enums::WF_NODE_TAG_APS_LOCAL]);
        $builder->andWhere('sub.auditor_id is not NULL');
        $builder->inWhere('auditor.audit_status', [Enums::WF_STATE_APPROVED, Enums::WF_STATE_AUTO_APPROVED]);
        $builder->andWhere('auditor.sub_node_id > 0');
        $builder->andWhere('auditor.audit_at >= :audit_at_start: and auditor.audit_at < :audit_at_end:', ['audit_at_start' => date('Y-m-d 00:00:00', $last_monday_timestamp), 'audit_at_end' => date('Y-m-d 00:00:00', $current_monday_timestamp)]);
        $finance_approval_list = $builder->getQuery()->execute()->toArray();
        if (!empty($finance_approval_list)) {
            //第二步根据第一步查询到的PO单的审批记录，找到每笔审批申请单：AP + 财务经理 + 财务总监各节点的审批时效小时数和【刨除节假日】
            $request_ids = array_values(array_unique(array_filter(array_column($finance_approval_list, 'id'))));
            $request_prev_node_approval_data = $this->getPrevNodeApprovalData($request_ids, Enums::$FINANCE_WF_NODE_TAGS);

            //第三步根据第一步查询到的PO单的审批记录、第二步的node节点查询个子节点征询时长 = 子节点会签的最后回复时间-最早的发起征询时间
            $request_fyr_hours_data = $this->getFyrHours($request_ids, $request_prev_node_approval_data['flow_node_ids']);

            //第四步循环第一步查询到的PO单审批记录，计算每一笔单据审批时效 = 第二步 - 第三步 > 24H；得到最终满足审批时效超时24小时的审批记录
            $final_approval_request_ids = [];
            foreach ($request_ids as $request_id) {
                $request_approval_hours = $request_prev_node_approval_data['hours'][$request_id] ?? 0;
                $request_fyr_hours = $request_fyr_hours_data[$request_id] ?? 0;
                $last_hours = bcsub($request_approval_hours, $request_fyr_hours, 5);
                //超过24小时
                if (bccomp($last_hours, 24, 5) === 1) {
                    $final_approval_request_ids[] = $request_id;
                }
            }

            //第五步存在满足审批时效超时24小时的审批记录，则需要获取审批中各财务节点下各审批人的审批时效记录列表
            if (!empty($final_approval_request_ids)) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns([
                    'request.name, auditor.auditor_id, auditor.created_at, auditor.audit_at'
                ]);
                $builder->from(['request' => WorkflowRequestModel::class]);
                $builder->leftjoin(WorkflowRequestNodeAuditorModel::class, 'auditor.request_id = request.id and request.current_flow_node_id > auditor.flow_node_id', 'auditor');
                $builder->leftjoin(WorkflowSubNodeModel::class, 'sub.id = auditor.sub_node_id', 'sub');
                $builder->inWhere('auditor.request_id', $final_approval_request_ids);
                $builder->inWhere('auditor.audit_status', [Enums::WF_STATE_APPROVED, Enums::WF_STATE_AUTO_APPROVED]);
                $builder->andWhere('auditor.sub_node_id > 0');
                $builder->inWhere('sub.node_tag', Enums::$FINANCE_WF_NODE_TAGS);
                $builder->andWhere('sub.auditor_id is not NULL');
                $list = $builder->getQuery()->execute()->toArray();
                $remind_leader_purchase_data['purchase_list'] = $this->formatRemindPurchaseLeaderData($list);
                $remind_leader_purchase_data['apply_num'] = count($final_approval_request_ids);
            }
        }
        return $remind_leader_purchase_data;
    }
}
