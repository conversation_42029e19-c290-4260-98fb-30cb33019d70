<?php
namespace App\Modules\Purchase\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\MaterialClassifyEnums;
use App\Library\Enums\PurchaseEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\MaterialSauPurchaseStaffsModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Modules\Material\Models\MaterialUpdateLogModel;
use App\Repository\HrStaffRepository;
use App\Repository\oa\MaterialSauRepository;

class SetService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //列表非必需
    public static $not_must_params = [
        'status',
        'purchase_staffs',
        'pageSize',
        'pageNum'
    ];

    //列表验证器
    public static $validate_list = [
        'status' => 'IntIn:' . MaterialClassifyEnums::MATERIAL_START_USING . ',' . MaterialClassifyEnums::MATERIAL_PROHIBITED_USE, //启用状态：1启用，2禁用
        'purchase_staff' => 'Arr',//采购员
        'purchase_staff[*]' => 'IntGt:0',//采购员工号
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];


    //列表验证器
    public static $validate_edit = [
        'barcode' => 'Required|StrLenGeLe:1,30',
        'purchase_staff' => 'Required|Arr|ArrLenGeLe:0,5',//采购员
        'purchase_staff[*]' => 'IntGt:0',//采购员工号
    ];

    /**
     * 默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $status = MaterialClassifyEnums::$status_switch;
            foreach ($status as $k => $v) {
                $data['status'][] = [
                    'value' => $k,
                    'label' => static::$t->_($v)
                ];
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取默认配置项异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 列表
     * @param array $condition 参数组
     * @param boolean $is_export true导出，false非导出
     * @return array
     */
    public function getList($condition, $is_export = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //获取列表
            $count = $is_export ? $page_size : $this->getListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('ms.id, ms.name_en, ms.name_zh, ms.name_local, ms.barcode, ms.status');
                $builder->from(['ms' => MaterialSauModel::class]);
                $builder->leftJoin(MaterialSauPurchaseStaffsModel::class, 'staff.sau_id = ms.id', 'staff');
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition);
                $builder->limit($page_size, $offset);
                $builder->orderby('ms.id desc');
                $builder->groupBy('ms.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items, $is_export);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-agency-payment-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }


    /**
     * 获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @return int
     */
    public function getListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftJoin(MaterialSauPurchaseStaffsModel::class, 'staff.sau_id = ms.id', 'staff');
        $builder->columns('count(DISTINCT ms.id) AS count');
        $builder = $this->getCondition($builder, $condition);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return mixed
     */
    public function getCondition($builder, $condition)
    {
        $name = $condition['name'] ?? '';//物料名称
        $barcode = $condition['barcode'] ?? ''; //标准型号的唯一标识
        $status = $condition['status'] ?? 0;//启用状态：1启用，2禁用
        $purchase_staff = $condition['purchase_staff'] ?? [];//采购员
        $builder->where('ms.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //名称搜索
        if (!empty($name)) {
            $builder->andWhere('ms.' . get_lang_field_name('name_', static::$language) . ' like :name:', ['name' => '%' . $name . '%']);
        }

        //精确搜索
        if (!empty($barcode)) {
            $builder->andWhere('ms.barcode = :barcode:', ['barcode' => $barcode]);
        }

        //启用状态
        if (!empty($status)) {
            $builder->andWhere('ms.status = :status:', ['status' => $status]);
        }
        //采购员
        if (!empty($purchase_staff)) {
            $builder->inWhere('staff.staff_id', $purchase_staff);
        }
        return $builder;
    }


    /**
     * 处理列表数据
     * @param array $items 列表数据
     * @param bool $is_export 是否导出，true导出
     * @return array
     */
    private function handleListItems($items, $is_export = false)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        $sau_ids = array_column($items, 'id');
        $sau_staff_list = MaterialSauRepository::getInstance()->getBarcodePurchaseStaffs($sau_ids);
        if ($is_export) {
            $rows = [];
            foreach ($items as $item) {
                $one_sau_staff_list = $sau_staff_list[$item['id']] ?? [];
                $rows[] = [
                    $item['barcode'],
                    $item['name_zh'],
                    $item['name_en'],
                    $item['name_local'],
                    static::$t->_(MaterialClassifyEnums::$status_switch[$item['status']]),
                    implode(',', $one_sau_staff_list['staffs'] ?? [])
                ];
            }
            $items = $rows;
        } else {
            foreach ($items as &$item) {
                $item['status_text'] = static::$t->_(MaterialClassifyEnums::$status_switch[$item['status']]);
                $one_sau_staff_list = $sau_staff_list[$item['id']] ?? [];
                $item['staffs'] = implode(',', $one_sau_staff_list['staffs'] ?? []);
                $item['purchase_staff_ids'] = $one_sau_staff_list['purchase_staff_ids'] ?? [];
                $item['purchase_staff'] = $one_sau_staff_list['purchase_staff'] ?? [];
            }
        }
        return $items;
    }

    /**
     * 导出
     * @param array $condition 参数组
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function export($condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = '';

        try {
            // 同步下载, 最多1w条
            $condition['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = $this->getListCount($condition);

            // 获取数据
            $res = $this->getList($condition, true);
            $excel_data = $res['data']['items'] ?? [];

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = 'purchase_set_export_' . date('YmdHis') . '.xlsx';
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('采购员设置-导出异常:' . $message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data,
        ];
    }

    /**
     * 获取导出的Excel表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('material_asset.barcode'),//barcode
            static::$t->_('material_assetname_zh'),//物料中文名称
            static::$t->_('material_assetname_en'),//物料英文名称
            static::$t->_('material_assetname_local'),//物料当地语言名称
            static::$t->_('material_status'),//状态
            static::$t->_('purchase_set.staff'),//采购员
        ];
    }

    /**
     * 编辑
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function edit($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $material_sau = MaterialSauModel::findFirst([
                'conditions' => 'barcode = :barcode: and is_deleted = :is_deleted:',
                'bind' => ['barcode' => $params['barcode'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ]);
            if (empty($material_sau)) {
                throw new ValidationException(static::$t->_('material_sau_not_existed'), ErrCode::$VALIDATE_ERROR);
            }

            $purchase_staff = $params['purchase_staff'] ?? [];
            $purchase_staff_list = (new HrStaffRepository())->getStaffListByStaffIds($purchase_staff);
            $now = date('Y-m-d H:i:s');
            $material_sau_purchase_staff_arr = [];
            foreach ($purchase_staff as $staff_id) {
                $one_staff_info = $purchase_staff_list[$staff_id] ?? [];
                if (empty($one_staff_info) || $one_staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN || $one_staff_info['formal'] != StaffInfoEnums::FORMAL_IN || $one_staff_info['is_sub_staff'] != StaffInfoEnums::IS_SUB_STAFF_NO) {
                    throw new ValidationException(static::$t->_('purchase_set.staff_invalid'), ErrCode::$VALIDATE_ERROR);
                }
                $material_sau_purchase_staff_arr[] = [
                    'sau_id' => $material_sau->id,
                    'staff_id' => $staff_id,
                    'created_at' =>$now
                ];
            }

            //删除原有采购员设置
            $material_sau_purchase_staff_obj = $material_sau->getMaterialSauPurchaseStaffs();
            $bool = $material_sau_purchase_staff_obj->delete();
            if ($bool === false) {
                throw new BusinessException('采购员设置-编辑-标准型号删除原有采购员设置失败', ErrCode::$BUSINESS_ERROR);
            }

            //标准型号保存采购员
            if ($material_sau_purchase_staff_arr) {
                $material_sau_purchase_staff_model = new MaterialSauPurchaseStaffsModel();
                $bool = $material_sau_purchase_staff_model->batch_insert($material_sau_purchase_staff_arr);
                if ($bool === false) {
                    throw new BusinessException('采购员设置-编辑-标准型号保存采购员失败，参数组：' . json_encode($params, JSON_UNESCAPED_UNICODE) . '；可能的原因是：' . get_data_object_error_msg($material_sau_purchase_staff_model), ErrCode::$BUSINESS_ERROR);
                }
            }

            //记录操作日志
            $material_sau->purchase_staff = implode(',', array_column($material_sau_purchase_staff_obj->toArray(), 'staff_id'));
            $params['purchase_staff'] = implode(',', $purchase_staff);
            $update_log_model = new MaterialUpdateLogModel();
            $log_bool = $update_log_model->dealEditField(MaterialClassifyEnums::OPERATE_TYPE_PURCHASE_STAFF, $material_sau, $params, $user);
            if ($log_bool === false) {
                throw new BusinessException('采购员设置-编辑-标准型号添加操作记录失败 = ' . json_encode($material_sau->id, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($update_log_model), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('purchase-set-edit-failed:' . $real_message . json_encode($params));
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 导入编辑
     * @param array $excel_file 模版文件
     * @param array $user 当前登陆者信息
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function import($excel_file, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }

            //读取上传文件数据
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
            //弹出excel标题第一行信息
            $excel_header_column = array_shift($excel_data);

            //验证条数
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('read_file_content_error'), ErrCode::$VALIDATE_ERROR);
            }
            $total_num = count($excel_data);
            $success_num = 0;
            if ($total_num > PurchaseEnums::PURCHASE_SET_LIMIT) {
                throw new ValidationException(static::$t->_('material_asset_data_limit_error', ['max' => PurchaseEnums::PURCHASE_SET_LIMIT]), ErrCode::$VALIDATE_ERROR);
            }

            //验证模板-头：barcode、采购员工号
            if (trim($excel_header_column[0] ?? '') != static::$t->_('material_asset.barcode') || trim($excel_header_column[1] ?? '') != static::$t->_('purchase_set.staff_ids')) {
                throw new ValidationException(static::$t->_('purchase_set_template_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //处理每行
            $row_values = [];
            foreach ($excel_data as $item) {
                $barcode = trim($item[0] ?? '');
                if (empty($barcode)) {
                    $item[2] = static::$t->_('material_asset_add_error_001');
                    $row_values[] = $item;
                    continue;
                }
                $purchase_staff_str =  str_replace( '，', ',', trim($item[1]));
                $purchase_staff = $purchase_staff_str ? explode(',', $purchase_staff_str) : [];
                if (count($purchase_staff) > 5) {
                    $item[2] = static::$t->_('purchase_set.staff_max');
                    $row_values[] = $item;
                    continue;
                }

                $res = $this->edit([
                    'barcode' => $barcode,
                    'purchase_staff' => $purchase_staff
                ], $user);
                if (isset($res['code']) && $res['code'] == ErrCode::$SUCCESS) {
                    $success_num ++;
                    $item[2] = static::$t->_('excel_result_success');
                } else {
                    $item[2] = static::$t->_('error_message_title') . '，' . $res['message'];
                }
                $row_values[] = $item;
            }

            $result = $this->exportExcel($excel_header_column, $row_values, '采购员导入编辑-' . date('YmdHis'));
            $data = [
                'url' => $result['data'],
                'all_num' => $total_num,
                'success_num' => $success_num,
                'failed_num' => bcsub($total_num, $success_num)
            ];
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('importAdd-网点租房付款 - 我的申请 - 创建 - 批量导入数据失败-' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data ?? []
        ];
    }
}
