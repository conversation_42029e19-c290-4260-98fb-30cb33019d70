<?php

namespace App\Modules\Purchase\Models;

use App\Library\Enums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class PurchaseUpdateTotalLog extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_update_total_log');
    }

}
