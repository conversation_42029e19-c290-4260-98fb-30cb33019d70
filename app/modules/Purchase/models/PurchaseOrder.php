<?php

namespace App\Modules\Purchase\Models;

use App\Library\Enums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class PurchaseOrder extends Base
{
    
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        parent::initialize();

        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_order');
        
        $this->hasMany(
            'id',
            PurchaseOrderProduct::class,
            'poid', [
                "alias" => "Products",
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = ".Enums::OSS_BUCKET_TYPE_PURCHASE_ORDER." and deleted=0"
                ],
                "alias" => "Attachments",
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

}
