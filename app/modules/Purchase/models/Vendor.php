<?php

namespace App\Modules\Purchase\Models;

use App\Models\Base;

class Vendor extends Base
{
    // 官方建议在模型中预先定义好所有的列，这样可以减少模型内存的开销以及内存分配。

    public $id;

    public $vendor_id;

    public $vendor_name;

    public $company_website;

    public $ownership;

    public $company_nature;

    public $certificate_type;

    public $identification_no;

    public $artificial_person;

    public $company_phone;

    public $company_address;

    public $contact;

    public $contact_phone;

    public $contact_email;

    public $bank_code;

    public $bank_no;

    public $attachment;

    public $status;

    public $create_id;

    public $create_name;

    public $created_at;

    public $updated_at;

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('vendor');
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
