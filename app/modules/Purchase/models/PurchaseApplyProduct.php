<?php

namespace App\Modules\Purchase\Models;

use App\Models\Base;
use App\Models\oa\PurchaseApplyProductStaffsModel;

class PurchaseApplyProduct extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        parent::initialize();

        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_apply_product');

        $this->hasMany(
            'id',
            PurchaseApplyProductStaffsModel::class,
            'apply_product_id', [
                'alias' => 'ProductStaffs',
            ]
        );
    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

}
