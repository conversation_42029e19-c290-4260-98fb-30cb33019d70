<?php

namespace App\Modules\Purchase\Models;

use App\Models\Base;

class PurchasePaymentReceipt extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_payment_receipt');

        $this->hasMany(
            'id',
            PurchaseOrderProduct::class,
            'poid', [
                "alias" => "Products",
            ]
        );
        $this->hasOne(
            'ppid',
            PurchasePayment::class,
            'id',
            [
                "alias" => "PurchasePayment",
            ]
        );

    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
