<?php

namespace App\Modules\Purchase\Models;

use App\Library\Enums;
use App\Models\Base;
use App\Modules\User\Models\AttachModel;

class PurchaseApply extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        parent::initialize();

        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('purchase_apply');

        $this->hasMany(
            'id',
            PurchaseApplyProduct::class,
            'paid', [
                "alias" => "Products",
            ]
        );


        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => "oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_PURCHASE_APPLY . " and deleted=0"
                ],
                "alias" => "File",
            ]
        );

    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

}
