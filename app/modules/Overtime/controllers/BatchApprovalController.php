<?php

namespace App\Modules\Overtime\Controllers;


use App\Library\ApiClient;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Overtime\Services\ApprovalOvertimeService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class BatchApprovalController extends BaseController
{

    /**
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * 加班列表页
     * @Permission(action='ot_approval.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $apiClient            = new ApiClient('by', '', 'listApprovalOvertime', $this->locale);
        $apiClient->setParams([$params]);
        $return = $apiClient->execute();

        if (isset($return['result'])) {
            return $this->returnJson($return['result']['code'], $return['result']['msg'], $return['result']['data']);
        }

        return $this->returnJson(ErrCode::$BUSINESS_ERROR, 'server error', null);
    }


    /**
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * 获取加班审批详情
     * @Permission(action='ot_approval.view')
     * @throws \App\Library\Exception\BusinessException
     */
    public function detailAction()
    {
        $params               = $this->request->get();
        $rpcParam['locale']   = $this->locale;
        $rpcParam['union_id'] = "id_" . $params['overtime_id'];
        $rpcParam['type']     = ByWorkflowEnums::WF_APPROVAL_OVERTIME;
        $rpcParam['staff_id'] = $this->user['id'];

        $server     = new WorkflowServiceV2();
        $detailData = $server->getAuditDetailFromBy($rpcParam, $this->user['id']);
        return $this->returnJson($detailData['code'], '', $detailData['data']);
    }

    /**
     * 单个审批
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * @Permission(action='ot_approval.approval')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function approvalAction(){
        $params         = $this->request->get();
        $params['lang'] = $this->locale;
        $validate = [
            'reject_reason' => 'StrLenGeLe:0,500', //驳回原因 非必填
        ];
        Validation::validate($params, $validate);
        $return         = ApprovalOvertimeService::getInstance()->approvalToBy($params, $this->user);
        return $this->returnJson($return['code'], '', $return['data']);
    }

    /**
     * 批量审批
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * @Permission(action='ot_approval.batch_approval')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function batchApprovalAction()
    {
        $params         = $this->request->get();
        $params['lang'] = $this->locale;
        $validate = [
            'reject_reason' => 'StrLenGeLe:0,500', //驳回原因 非必填
        ];
        Validation::validate($params, $validate);
        $return         = ApprovalOvertimeService::getInstance()->approvalToBy($params, $this->user);
        return $this->returnJson($return['code'], '', $return['data']);
    }
    /**
     * 单个审批
     * @api https://yapi.flashexpress.pub/project/133/interface/api/cat_13369
     * @Permission(action='ot_approval.approval')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function cancelAction(){
        $params               = $this->request->get();
        $params['operate_id'] = $this->user['id'];
        $apiClient            = new ApiClient('by', '', 'cancelApprovalOvertime', $this->locale);
        $apiClient->setParams([$params]);
        $return = $apiClient->execute();

        if (isset($return['result'])) {
            return $this->returnJson($return['result']['code'], $return['result']['message'], $return['result']['data']);
        }

        return $this->returnJson(ErrCode::$BUSINESS_ERROR, 'server error', null);
    }

}