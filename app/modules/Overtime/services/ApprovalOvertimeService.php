<?php

namespace App\Modules\Overtime\Services;


use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\RocketMQ;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysManageRegionModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Modules\Overtime\Models\HrOvertime;
use App\Modules\Transfer\Models\AuditApprovalModel;
use App\Modules\User\Services\StaffService;

class ApprovalOvertimeService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    public function approvalToBy($param, $user)
    {
        if (empty($param['overtime_id'])) {
            return ['code' => ErrCode::$SUCCESS, 'msg' => '', 'data' => true];
        }
        if (!is_array($param['overtime_id'])) {
            $param['overtime_id'] = [$param['overtime_id']];
        }
        //先 修改字段  in_approval = 1
        $ids = array_values(array_unique($param['overtime_id']));

        //查询状态 排除非带审批
        $realData = HrOvertime::find([
            'columns'    => 'overtime_id',
            'conditions' => 'overtime_id in ({ids:array}) and state = 1',
            'bind'       => ['ids' => $ids],
        ])->toArray();

        if (empty($realData)) {
            throw new ValidationException('data state error, please refresh page');
        }

        //带审批的id
        $ids     = array_column($realData, 'overtime_id');
        $ids_str = implode(',', $ids);
        $this->getDI()->get('db_backyard')->updateAsDict(
            (new HrOvertime())->getSource(),
            ['in_approval' => 1, 'updated_at' => new \Phalcon\Db\RawValue('updated_at')],
            ['conditions' => "overtime_id in ({$ids_str})"]
        );

        //写队列
        $rmq = new RocketMQ('overtime-batch-approval');
        foreach ($ids as $id) {
            $mqParam = [
                'lang'          => $param['lang'],
                'audit_id'      => $id,
                'staff_id'      => $user['id'],
                'status'        => $param['status'],
                'reject_reason' => $param['reject_reason'] ?? '',
            ];
            $rmq->sendToMsg($mqParam,3);
        }
        //返回
        return ['code' => ErrCode::$SUCCESS, 'msg' => '', 'data' => true];
    }

    //获取 带审批小红点
    public function getWfCount($user)
    {
        $count = AuditApprovalModel::count([
            'conditions' => 'biz_type = :biz_type: and approval_id = :staff_id: and state = 1',
            'bind'       => ['biz_type' => ByWorkflowEnums::WF_APPROVAL_OVERTIME, 'staff_id' => $user],
        ]);

        return $count;
    }


}
