<?php

namespace App\Modules\Overtime\Services;


use App\Library\Enums;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Repository\DepartmentRepository;
use Exception;

class SysService extends BaseService
{

    //根据网点名称模糊查询
    public function searchStore($params)
    {
        $conditions        = 'state = 1';
        $bind = [];

        $query['limit'] = 20;//默认返回限制20个网点
        if (!empty($params['search_name'])) {
            $conditions   .= ' and name LIKE :name: ';
            $bind['name'] = '%' . $params['search_name'] . '%';
            unset($query['limit']);//搜索不限制
        }
        $query['columns']    = 'id, name';
        $query['conditions'] = $conditions;
        $query['bind']       = $bind;

        $search_name = strtolower($params['search_name']);
        $list        = SysStoreModel::find($query)->toArray();
        if (strstr("head office", $search_name)) {
            $row[0]['id']   = '-1';
            $row[0]['name'] = 'Header Office';
            $list           = array_merge($row, $list);
        }
        return $list;
    }


    //列表也搜索 下拉
    public function selectEnum(){
        //大区
        $region = SysManageRegionModel::find([
            'columns' => 'id, name',
            'conditions' => 'deleted = 0',
        ])->toArray();

        //片区
        $piece = SysManagePieceModel::find([
            'columns' => 'id, name',
            'conditions' => 'deleted = 0',
        ])->toArray();

        //审批状态 排除带审批 后来又加上待审批了
        $stateList = [
            ['code' => Enums::WF_STATE_PENDING, 'value' => static::$t->_('wms_audit_status.1')],
            ['code' => Enums::WF_STATE_REJECTED, 'value' => static::$t->_('wms_audit_status.2')],
            ['code' => Enums::WF_STATE_APPROVED, 'value' => static::$t->_('wms_audit_status.3')],
            ['code' => Enums::WF_STATE_CANCEL, 'value' => static::$t->_('wms_audit_status.4')],
            ['code' => Enums::WF_STATE_AUTO_APPROVED, 'value' => static::$t->_('wms_audit_status.5')],
        ];
        return ['region' => $region, 'piece' => $piece, 'state' => $stateList];
    }

}
