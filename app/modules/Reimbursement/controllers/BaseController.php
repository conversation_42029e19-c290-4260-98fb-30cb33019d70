<?php

namespace App\Modules\Reimbursement\Controllers;

use App\Library\BaseController as Controller;

/**
 * @name BaseController
 * @desc 所有web模块控制器都继承自该控制器
 */
abstract class BaseController extends Controller
{
    //模块自己控制
    public function onConstruct()
    {
    }

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);
    }

}
