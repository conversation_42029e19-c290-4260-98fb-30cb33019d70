<?php

namespace App\Modules\Reimbursement\Controllers;

use App\Library\ErrCode;
use App\Modules\Reimbursement\Services\BaseService;
use App\Modules\Reimbursement\Services\DetailService;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\Reimbursement\Services\ReimbursementFlowService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;

class ConsultationController extends BaseController
{
    /**
     * 待回复征询列表
     *
     * @Token
     */
    public function listAction()
    {
        $params = $this->request->get();
        //新增需求 by申请的数据  报销列表不展示
        //新增需求 by申请的报销 也要展示在oa报销列表里
//        $params['source_type'] = 1;//1  oa  2 by
        $list = ListService::getInstance()->getList($params, $this->user, BaseService::LIST_TYPE_FYR);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
    }

    /**
     *
     * @Token
     */
    public function detailAction()
    {
        $id = $this->request->get('id');

        $res = DetailService::getInstance()->getCommonDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * @Token
     */
    public function askAction()
    {
        $biz_id      = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        $to_staffs   = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $biz_id,
            'note'        => $note,
            'to_staff'    => $to_staffs,
            'attachments' => $attachments,
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request   = (new ReimbursementFlowService())->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result    = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id      = $this->request->get('ask_id', 'int');
        $note        = $this->request->get('note', 'trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id'          => $ask_id,
            'note'        => $note,
            'attachments' => $attachments,
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }
}