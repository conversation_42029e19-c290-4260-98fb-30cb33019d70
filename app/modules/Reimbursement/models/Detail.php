<?php

namespace App\Modules\Reimbursement\Models;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Models\Base;
use App\Models\oa\ReimbursementDetailSupportRelModel;
use App\Models\oa\ReimbursementDetailTravelRoommateRelModel;
use App\Modules\User\Models\AttachModel;

class Detail extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('reimbursement_detail');

        // 附件
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted= :deleted:',
                    'bind'       => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_REIMBURSEMENT, 'deleted' => GlobalEnums::IS_NO_DELETED],
                ],
                'alias'  => 'File',
            ]
        );

        $this->hasOne(
            're_id',
            Reimbursement::class,
            'id',
            [
                'alias' => 'ReimbursementDetail',
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailTicketModel::class,
            'detail_id', [
                'alias' => 'Tickets',
            ]
        );

        // 获取支付凭证
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted=0',
                    'bind'       => ['oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_REIMBURSEMENT_PAYMENT_VOUCHER],
                ],
                'alias'  => 'PaymentVoucherFile',
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailSupportRelModel::class,
            'detail_id',
            [
                'alias' => 'SupportSerialNo',
            ]
        );

        $this->hasMany(
            'id',
            ReimbursementDetailTravelRoommateRelModel::class,
            'detail_id',
            [
                'alias' => 'TravelRoommateRelList',
                'params' => [
                    'columns' => ['serial_no', 'apply_staff_id', 'apply_staff_name', 'confirm_status', 'confirm_at'],
                ],
            ]
        );

        // 报销超出标准金额附件
        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted=0',
                    'bind'       => ['oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_EXCEEDS_STANDARD_AMOUNT_EMAIL_FILE],
                    'columns'    => ['bucket_name', 'object_key', 'file_name'],
                ],
                'alias'  => 'ExceedsStandardAmountEmailFile',
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted=0',
                    'bind'       => ['oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_START_MILEAGE_FILE],
                    'columns'    => ['bucket_name', 'object_key', 'file_name'],
                ],
                'alias'  => 'FuelStartMileageFile',
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'oss_bucket_type = :oss_bucket_type: AND deleted=0',
                    'bind'       => ['oss_bucket_type' => ReimbursementEnums::OSS_BUCKET_TYPE_FUEL_END_MILEAGE_FILE],
                    'columns'    => ['bucket_name', 'object_key', 'file_name'],
                ],
                'alias'  => 'FuelEndMileageFile',
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
