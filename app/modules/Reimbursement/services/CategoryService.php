<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\Reimbursement\Models\Expense;
use Exception;

class CategoryService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return CategoryService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }


    /**
     * sys_store_id网点id,
     *
     * @param array $user
     * @return array
     */

    public function getList($user)
    {

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];


        $condition = "";
        if ($user['sys_store_id'] == -1) {
            $condition = "is_ho=1 ";
        } else {
            $condition = "is_store=1 ";
        }

        //是否是Human Resources
        $is_hr = $user['sys_department_id'] == 7 ? 1 : 0;
        if (!$is_hr) {
            //如果不是，需要只能拿=0的，是的话不做判断
            $condition .= " and is_hr=0 ";
        }

        //是否是中国国籍
        $is_china = $user['is_china'];
        if (!$is_china) {
            $condition .= " and is_china=0 ";
        }


        try {
            $list = Expense::find(
                [
                    "conditions" => $condition
                ]
            )->toArray();

            if (empty($list)) {
                throw new Exception("no expense");
            }


            $category_a = Enums::$reimbursement_expense_type;

            $data = [];
            foreach ($category_a as $k => $v) {
                $data[$k]['id'] = '' . $k;
                $data[$k]['name'] = static::$t->_($v);
                $data[$k]['list'] = [];
            }

            foreach ($list as $k => $v) {
                $temp = [];
                $temp['id'] = $v['id'];
                $temp['name'] = static::$t->_($v['name_key']);

                //是差旅
                if ($v['is_travel']) {
                    $data[Enums::REIMBURSEMENT_EXPENSE_TRAVEL]["list"][] = $temp;
                }

                //是本地
                if ($v['is_local']) {
                    $data[Enums::REIMBURSEMENT_EXPENSE_LOCAL]["list"][] = $temp;
                }
            }
        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('expense-category-get-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => array_values($data)
        ];
    }

    public function getListByIds($ids)
    {

        $items = Expense::find(
            [
                "conditions" => "id in ({ids:array})",
                "bind" => ["ids" => $ids],
            ]
        )->toArray();


        if (empty($items)) {
            return [];
        } else {
            return array_column($items, "name_key", "id");
        }

    }

}
