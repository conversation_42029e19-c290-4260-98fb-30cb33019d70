<?php

namespace App\Modules\Reimbursement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\ReimbursementDraftModel;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\Reimbursement\Models\ReimbursementRelLoan;
use App\Modules\User\Models\StaffExpenseLogModel;
use App\Modules\User\Models\StaffExpenseModel;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;

class UpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return UpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $id
     * @param $data
     * @param $user
     * @param int $is_from 1本模块 ，2付款模块
     * @return array
     */

    public function pay($id, $data, $user, $is_from = 1)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $reimbursement_pay_staff_id = $this->getReimbursementPayStaffIds();
            if (!in_array($user['id'], $reimbursement_pay_staff_id) && $is_from == PayEnums::IS_FROM_SELF) {
                throw new ValidationException(static::$t->_('user_no_pay_permission_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 已支付状态的, 银行流水日期为必填
            if ($data['pay_status'] == Enums::LOAN_PAY_STATUS_PAY && empty($data['pay_at'])) {
                throw new ValidationException('Paid, bank journal date is required', ErrCode::$VALIDATE_ERROR);
            }

            $item = Reimbursement::getFirst([
                'conditions' => 'id = :id: AND status = :status:',
                'bind' => ['id' => $id, 'status' => Enums::WF_STATE_APPROVED]
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('biz_order_data_is_null', ['biz_id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 非待支付状态, 不可重复支付
            if ($item->pay_status != Enums::PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('repeated_payment_error_hint', ['serial_no' => $item->no]), ErrCode::$VALIDATE_ERROR);
            }

            if ($is_from == PayEnums::IS_FROM_SELF && $item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }

            // 纸质单据的确认状态不是已齐全无法支付
            if (in_array(get_country_code(), [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
                if ($item->confirm_status != Enums\PaperDocumentEnums::CONFIRM_STATE_COMPLETE) {
                    throw new ValidationException(static::$t->_('22111_a8e872ad'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $data = $this->handleData($data, $user);

            $bool = $item->i_update($data);
            if ($bool === false) {
                throw new BusinessException('更新报销失败, 原因可能是: ' . get_data_object_error_msg($item) . '; 数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . "; id = {$item->id}", ErrCode::$BUSINESS_ERROR);
            }

            // 释放报销关联备用金
            if (Enums::LOAN_PAY_STATUS_NOTPAY == $data['pay_status']) {
                (new ReimbursementFlowService())->freeReserves($item);
            }

            $this->delUnReadNumsKeyByStaffIds($reimbursement_pay_staff_id);

            //处理关联借款
            $loanRels = $item->getLoans();

            $this->logger->info("pay_reimbursement_free_loans 报销关联的借款单re_id={$item->id}: " . json_encode($loanRels->toArray(), JSON_UNESCAPED_UNICODE));

            foreach ($loanRels as $loanRel) {
                $loanItem = Loan::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $loanRel->loan_id]
                ]);

                // 借款数据不存在, 数据不完整, 需要报异常进行关注
                if (empty($loanItem)) {
                    throw new BusinessException("pay_reimbursement_free_loans 借款数据不存在: loan_id = {$loanRel->loan_id}", ErrCode::$BUSINESS_ERROR);
                }

                $this->logger->info('pay_reimbursement_free_loans 借款数据(before): ' . json_encode($loanItem->toArray(), JSON_UNESCAPED_UNICODE));

                //报销支付未支付
                if ($data['pay_status'] == Enums::PAYMENT_PAY_STATUS_NOTPAY) {
                    //软删除；同时也需要把报销单与借款单的关联关系解绑掉
                    $loanRel->is_deleted = GlobalEnums::IS_DELETED;
                    $loanRel->updated_at = date('Y-m-d H:i:s');

                    $this->logger->info("pay_reimbursement_free_loans 报销与借款关系表更新后数据(re_id={$item->id}): " . json_encode($loanRel->toArray(), JSON_UNESCAPED_UNICODE));

                    if ($loanRel->save() === false) {
                        throw new BusinessException('pay_reimbursement_free_loans 报销与借款关系表更新失败, 原因可能是: ' . get_data_object_error_msg($loanRel), ErrCode::$BUSINESS_ERROR);
                    }

                    //借款单中的报销抵扣金额需要➖本单报销单借款冲减金额
                    $loanItem->re_amount = $loanItem->re_amount - $item->loan_amount;
                }

                //已归还金额 = 报销抵扣-已通过已支付（re_amount）+ 归还金额（back_amount）
                $re_amount = $this->getTotalLoanAmounts($loanItem->id);
                $paid_return_amount = $re_amount + $loanItem->back_amount;
                //15873需求需要变更借款单状态
                if ($paid_return_amount == 0) {
                    //未开始归还：已归还金额=0
                    $loanItem->loan_status = Enums\LoanEnums::LOAN_STATUS_NOT_START_RETURN;
                } elseif ($paid_return_amount < $loanItem->amount) {
                    //部分归还：已归还金额<借款金额
                    $loanItem->loan_status = Enums\LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
                } elseif ($paid_return_amount == $loanItem->amount) {
                    //已还清：已归还总金额=借款金额
                    $loanItem->loan_status = Enums\LoanEnums::LOAN_STATUS_PAID_OFF;
                } elseif ($paid_return_amount > $loanItem->amount) {
                    //超额归还：已归还总金额＞借款金额
                    $loanItem->loan_status = Enums\LoanEnums::LOAN_STATUS_OVER_RETURN;
                }

                // 借款数据更新
                $loanItem->updated_at = date('Y-m-d H:i:s');

                $this->logger->info('pay_reimbursement_free_loans 借款数据(after): ' . json_encode($loanItem->toArray(), JSON_UNESCAPED_UNICODE));

                //更新关联的借款单信息
                if ($loanItem->save() === false) {
                    throw new BusinessException('pay_reimbursement_free_loans, 更新loan表失败, 原因可能是: ' . get_data_object_error_msg($loanItem), ErrCode::$BUSINESS_ERROR);
                }
            }

            /**
             * v12855
             * 如果开启了支付模块,支付模块请求的支付否(撤回操作),需要释放预算, 本模块支付不再释放
             * 为了兼容开启支付模块后任然处于本模块支付流程中的数据, 使用is_pay_module判断,如果=1 限制只有支付模块撤回可以释放,如果=0则本模块可以释放
             *
             * 如果没有开启支付模块, 本模块依然释放预算
             */
            $pay_module_status = EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_REIMBURSEMENT, $item->cost_company_id);
            if ($data['pay_status'] == Enums::PAYMENT_PAY_STATUS_NOTPAY) {
                //开启支付模块后的支付模块来源请求,且已经进入支付模块的数据(为了兼容),可以释放
                if ($pay_module_status == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES && $is_from == PayEnums::IS_FROM_PAY && $item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                    AddService::getInstance()->freeBudget($id, $user);
                }
                //开启支付模块后的本模块来源请求,且未进入支付模块的数据(为了兼容),可以释放
                if ($pay_module_status == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES && $is_from == PayEnums::IS_FROM_SELF && $item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_NO) {
                    AddService::getInstance()->freeBudget($id, $user);
                }
                //未开启支付模块(没开启支付模块只有本模块一个入口,无需判断来源), 可以释放
                if ($pay_module_status != PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                    AddService::getInstance()->freeBudget($id, $user);
                }
            }
            $db->commit();

            // 未支付: BY端提交的油费报销关系, 需置为软删除
            if (Enums::LOAN_PAY_STATUS_NOTPAY == $data['pay_status'] && $item->source_type == Enums\ReimbursementEnums::SOURCE_TYPE_BY) {
                //报销单 如果是 by申请的 油费报销 驳回之后 需要 删除用车记录关联
                $ac = new ApiClient('by', '', 'reject_fuel');
                $api_params = [
                    [
                        'staff_id' => $item->created_id,
                        'no' => $item->no,
                    ],
                ];
                $ac->setParams($api_params);
                $ac->execute();
            }

        } catch (ValidationException $e) {
            $db->rollback();

            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (TxFailed $e) {
            $db->rollback();

            //数据库错误，不可对外抛出
            $code = ErrCode::$MYSQL_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();

            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('reimbursement-pay-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            throw new ValidationException('data is null', ErrCode::$VALIDATE_ERROR);
        }

        $new = [];

        // 仅已付款, 需保存付款日期(银行流水日期)
        $new['pay_at'] = $data['pay_status'] == Enums::LOAN_PAY_STATUS_PAY ? $data['pay_at'] : null;
        $new['updated_at'] = date('Y-m-d H:i:s');
        $new['pay_operate_date'] = date('Y-m-d H:i:s');
        $new['pay_status'] = $data['pay_status'];
        $new['remark'] = $data['remark'] ?? '';
        $new['payer_id'] = $user['id'];

        $new['pay_bank_id'] = $data['pay_bank_id'] ?? 1;
        $new['pay_bank_name'] = $data['pay_bank_name'] ?? 'Thai Military Bank';
        $new['pay_bank_account'] = $data['pay_bank_account'] ?? '**********';

        return $new;
    }

    /**
     * 获取某个借款下关联的所有已通过已支付的报销单的借款冲减金额和
     *
     * @param integer $loan_id 借款单id
     * @return mixed
     */
    private function getTotalLoanAmounts($loan_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['r' => Reimbursement::class]);
        $builder->leftjoin(ReimbursementRelLoan::class, 'r.id = rel.re_id', 'rel');
        $builder->where('rel.loan_id = :loan_id: AND r.status = :status: AND r.pay_status = :pay_status: AND rel.is_deleted = :is_deleted:', [
            'loan_id' => $loan_id,
            'status' => Enums::WF_STATE_APPROVED,
            'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY,
            'is_deleted' => GlobalEnums::IS_NO_DELETED
        ]);
        $builder->columns('SUM(r.loan_amount) as re_amount');
        return (int)($builder->getQuery()->getSingleResult()->re_amount ?? 0);
    }

    /**
     * 删除用户草稿
     *
     * @param $user
     * @return bool
     * @throws ValidationException
     */
    public function delUserDraft($user)
    {
        $draft_model = ReimbursementDraftModel::findFirst([
            'conditions' => 'created_id = :created_id:',
            'bind'       => ['created_id' => $user['id']],
        ]);
        if (empty($draft_model)) {
            throw new ValidationException(static::$t->_('reimbursement_draft_not_exist'), ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info(['reimbursement_draft_del_data' => $draft_model->toArray()]);

        if ($draft_model->delete() === false) {
            throw new ValidationException(static::$t->_('reimbursement_save_error_018'), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }


}
