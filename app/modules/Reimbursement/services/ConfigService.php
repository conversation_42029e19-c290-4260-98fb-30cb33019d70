<?php
/**
 * 报销配置管理
 */

namespace App\Modules\Reimbursement\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SysCityModel;
use App\Models\backyard\SysProvinceModel;
use App\Models\oa\ReimbursementCarRentalQuotaModel;
use App\Models\oa\ReimbursementDomesticAccommodationAreaModel;
use App\Models\oa\ReimbursementDomesticAccommodationQuotaModel;
use App\Models\oa\ReimbursementDomesticAirTicketQuotaModel;
use App\Models\oa\ReimbursementFuelQuotaModel;
use App\Models\oa\ReimbursementOverseasAccommodationAreaModel;
use App\Models\oa\ReimbursementOverseasAccommodationQuotaModel;
use App\Models\oa\ReimbursementTripTimeLimitModel;
use App\Models\oa\SysExchangeRateModel;
use App\Modules\Common\Models\CurrencyModel;
use App\Modules\Common\Services\EnumsService;
use App\Repository\HrStaffRepository;
use App\Repository\oa\ReimbursementCarRentalQuotaRepository;
use App\Repository\oa\ReimbursementDomesticAccommodationAreaRepository;
use App\Repository\oa\ReimbursementDomesticAccommodationQuotaRepository;
use App\Repository\oa\ReimbursementDomesticAirTicketQuotaRepository;
use App\Repository\oa\ReimbursementFuelQuotaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationAreaRepository;
use App\Repository\oa\ReimbursementOverseasAccommodationQuotaRepository;
use App\Repository\oa\ReimbursementTripTimeLimitRepository;
use App\Repository\oa\SysExchangeRateRepository;
use Exception;

class ConfigService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取出差报销时间限制
     */
    public function getReimbursementTripTimeLimit()
    {
        $result = [
            'id'                           => 1,
            'allowed_travel_end_date_days' => '',
            'is_limit'                     => '1',
            'next_month_specified_date'    => '',
        ];

        $model = ReimbursementTripTimeLimitRepository::getInstance()->getOneById($result['id']);
        if (!empty($model)) {
            $result['id']                           = $model->id;
            $result['allowed_travel_end_date_days'] = $model->allowed_travel_end_date_days;
            $result['is_limit']                     = $model->is_limit;
            $result['next_month_specified_date']    = $model->next_month_specified_date;
        }

        return $result;
    }


    /**
     * 保存出差报销时间限制
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     */
    public function saveReimbursementTripTimeLimit($params, $user)
    {
        $model = ReimbursementTripTimeLimitRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            $model             = new ReimbursementTripTimeLimitModel();
            $model->created_at = date('Y-m-d H:i:s');
            $model->created_id = $user['id'];
        }

        $this->logger->info(['reimbursement_trip_time_limit_before_data' => $model->toArray()]);

        $model->id                           = $params['id'];
        $model->allowed_travel_end_date_days = $params['allowed_travel_end_date_days'];
        $model->is_limit                     = $params['is_limit'];
        $model->next_month_specified_date    = $params['next_month_specified_date'];
        $model->updated_at                   = date('Y-m-d H:i:s');
        $model->updated_id                   = $user['id'];

        $this->logger->info(['reimbursement_trip_time_limit_after_data' => $model->toArray()]);
        if ($model->save() === false) {
            throw new BusinessException('保存出差报销时间限制配置失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取系统汇率列表
     * @param $params
     * @return mixed
     */
    public function getSysExchangeRateList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(SysExchangeRateModel::class);

        if (!empty($params['currency_symbol'])) {
            $builder->andWhere('currency_symbol = :currency_symbol:',
                ['currency_symbol' => $params['currency_symbol']]);
        }

        if (!empty($params['start_date'])) {
            $builder->andWhere('selling_date >= :start_date:', ['start_date' => $params['start_date']]);
        }

        if (!empty($params['end_date'])) {
            $builder->andWhere('selling_date <= :end_date:', ['end_date' => $params['end_date']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'selling_date',
                'currency_symbol',
                'exchange_rate',
            ]);

            $builder->orderBy('selling_date DESC, currency_symbol ASC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

    /**
     * 更新系统汇率: 获取指定日期的汇率
     * @param $period_date
     * @param $user
     * @return array
     */
    public function updateSysExchangeRateList($period_date, $user = [])
    {
        $code     = ErrCode::$SUCCESS;
        $message  = static::$t->_('success');
        $is_retry = false;

        try {
            // bot api
            $api = ReimbursementEnums::EXCHANGE_RATE_BOT_API . "?start_period={$period_date}&end_period={$period_date}&currency=";

            // bot api id
            $client_id = ReimbursementEnums::EXCHANGE_RATE_BOT_API_CLIENT_ID;

            // 查询所有外币汇率
            $header = [
                "accept: application/json",
                "X-IBM-Client-Id: {$client_id}",
            ];

            $this->logger->info(['daily_avg_exg_rate_request' => $api]);
            $curl_res = curl_request($api, null, 'get', $header);
            $this->logger->info(['daily_avg_exg_rate_response' => $curl_res]);

            $curl_res = !empty($curl_res) ? json_decode($curl_res, true) : $curl_res;

            if (!isset($curl_res['result']['data'])) {
                $is_retry = true;
                throw new ValidationException(static::$t->_('business_setting_save_error_010'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $data_detail = $curl_res['result']['data']['data_detail'] ?? [];
            if (empty($data_detail)) {
                $is_retry = true;
                throw new ValidationException(static::$t->_('business_setting_save_error_013'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $hr_staff_repository = new HrStaffRepository();
            $selling             = array_filter(array_unique(array_column($data_detail, 'selling')));
            $period              = array_filter(array_unique(array_column($data_detail, 'period')));
            if (empty($selling) || empty($period)) {
                if ($hr_staff_repository->checkIsHoliday($period_date)) {
                    throw new ValidationException(static::$t->_('business_setting_save_error_012',
                        ['period_date' => $period_date]), ErrCode::$VALIDATE_ERROR);
                } else {
                    throw new BusinessException(static::$t->_('business_setting_save_error_011',
                        ['period_date' => $period_date]), ErrCode::$BUSINESS_ERROR);
                }
            }

            // 取当前系统所有币种
            $currency_list = CurrencyModel::getAllCurrency();

            // 当前日期已同步过的币种
            $exist_currency_list = SysExchangeRateRepository::getInstance()->getCurrencyListBySellingDate($period_date);

            // 判断系统中的外币是否在接口的返回清单中
            $data_detail = array_column($data_detail, null, 'currency_id');

            $country_code = get_country_code();

            // 遍历待同步汇率的币种
            $batch_rate_list = [];
            foreach ($currency_list as $currency_info) {
                // 本国币种跳过
                if ($currency_info['country_code'] == $country_code) {
                    continue;
                }

                // 该日期该币种的汇率已同步过
                if (in_array($currency_info['currency_symbol'], $exist_currency_list)) {
                    continue;
                }

                $rate_info = $data_detail[$currency_info['currency_symbol']] ?? [];
                if (empty($rate_info)) {
                    $this->logger->notice("币种-{$currency_info['currency_symbol']}, 在BOT接口中未找到, 请同步产品");
                    continue;
                }

                if ($rate_info['period'] == '' || $rate_info['selling'] == '') {
                    if (!$hr_staff_repository->checkIsHoliday($period_date)) {
                        $this->logger->notice(static::$t->_('business_setting_save_error_011',
                            ['period_date' => $period_date]));
                    }

                    continue;
                }

                $batch_rate_list[] = [
                    'currency'        => $currency_info['id'],
                    'currency_symbol' => $currency_info['currency_symbol'],
                    'exchange_rate'   => $rate_info['selling'],
                    'selling_date'    => $rate_info['period'],
                    'created_at'      => date('Y-m-d H:i:s'),
                    'created_id'      => $user['id'] ?? '10000',
                ];
            }

            $this->logger->info(['sys_exchange_rate_batch_add_data' => $batch_rate_list]);

            // 写入数据
            $sys_exchange_rate_model = new SysExchangeRateModel();
            if (!empty($batch_rate_list) && $sys_exchange_rate_model->batch_insert($batch_rate_list) === false) {
                throw new BusinessException('系统汇率批量写入失败, 请核查', ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->logger->notice(['daily_avg_exg_rate_error_msg' => $message]);
        } catch (Exception $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning(['daily_avg_exg_rate_error_msg' => $e->getMessage()]);
        }

        return [
            'code'     => $code,
            'message'  => $message,
            'is_retry' => $is_retry,
        ];
    }

    /**
     * 获取境内机票额度
     */
    public function getReimbursementDomesticAirTicketQuota()
    {
        $result = [
            'id'                          => 1,
            'head_office_amount'          => '0.00',
            'frontline_functional_amount' => '0.00',
            'frontline_operation_amount'  => '0.00',
        ];

        $model = ReimbursementDomesticAirTicketQuotaRepository::getInstance()->getOneById($result['id']);
        if (!empty($model)) {
            $result['id']                          = $model->id;
            $result['head_office_amount']          = $model->head_office_amount;
            $result['frontline_functional_amount'] = $model->frontline_functional_amount;
            $result['frontline_operation_amount']  = $model->frontline_operation_amount;
        }

        return $result;
    }

    /**
     * 保存境内机票额度
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     */
    public function saveReimbursementDomesticAirTicketQuota($params, $user)
    {
        $model = ReimbursementDomesticAirTicketQuotaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            $model             = new ReimbursementDomesticAirTicketQuotaModel();
            $model->created_at = date('Y-m-d H:i:s');
            $model->created_id = $user['id'];
        }

        $this->logger->info(['reimbursement_domestic_air_ticket_quota_before_data' => $model->toArray()]);

        $model->id                          = $params['id'];
        $model->head_office_amount          = $params['head_office_amount'];
        $model->frontline_functional_amount = $params['frontline_functional_amount'];
        $model->frontline_operation_amount  = $params['frontline_operation_amount'];
        $model->updated_at                  = date('Y-m-d H:i:s');
        $model->updated_id                  = $user['id'];

        $this->logger->info(['reimbursement_domestic_air_ticket_quota_after_data' => $model->toArray()]);
        if ($model->save() === false) {
            throw new BusinessException('保存境内机票额度配置失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 油费额度列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementFuelQuota($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementFuelQuotaModel::class);

        if (!empty($params['position_type'])) {
            $builder->andWhere('position_type = :position_type:', ['position_type' => $params['position_type']]);
        }

        if (!empty($params['expenses_type'])) {
            $builder->andWhere('expenses_type = :expenses_type:', ['expenses_type' => $params['expenses_type']]);
        }

        if (!empty($params['oil_type'])) {
            $builder->andWhere('oil_type = :oil_type:', ['oil_type' => $params['oil_type']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'position_type',
                'expenses_type',
                'oil_type',
                'rates',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $enums_item         = EnumsService::getInstance()->getReimbursementFuelQuotaEnums();
            $expenses_type_item = array_column($enums_item['expenses_type_item'], 'label', 'value');
            $position_type_item = array_column($enums_item['position_type_item'], 'label', 'value');
            $oil_type_item      = array_column($enums_item['oil_type_item'], 'label', 'value');

            foreach ($items as &$val) {
                $val['position_type'] = $position_type_item[$val['position_type']] ?? '';
                $val['expenses_type'] = $expenses_type_item[$val['expenses_type']] ?? '';
                $val['oil_type']      = $oil_type_item[$val['oil_type']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }


    /**
     * 油费额度添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementFuelQuota($params, $user)
    {
        $exist_model = ReimbursementFuelQuotaRepository::getInstance()->getOneByType($params['position_type'],
            $params['expenses_type'], $params['oil_type']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_003',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'position_type' => $params['position_type'],
            'expenses_type' => $params['expenses_type'],
            'oil_type'      => $params['oil_type'],
            'rates'         => $params['rates'],
            'created_at'    => date('Y-m-d H:i:s'),
            'created_id'    => $user['id'],
            'updated_at'    => date('Y-m-d H:i:s'),
            'updated_id'    => $user['id'],
        ];

        $this->logger->info(['reimbursement_fuel_quota_add_data' => $add_data]);

        $model = new ReimbursementFuelQuotaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('报销油费额度配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 油费额度删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementFuelQuota($params, $user)
    {
        $model = ReimbursementFuelQuotaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info(['reimbursement_fuel_quota_del_data' => $model->toArray(), 'user_info' => $user]);
        if ($model->delete() === false) {
            throw new BusinessException('报销油费额度配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销-境内住宿区域配置列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementDomesticAccommodationArea($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementDomesticAccommodationAreaModel::class);

        if (!empty($params['area_type'])) {
            $builder->andWhere('area_type = :area_type:', ['area_type' => $params['area_type']]);
        }

        if (!empty($params['province_code'])) {
            $builder->andWhere('province_code = :province_code:', ['province_code' => $params['province_code']]);
        }

        if (!empty($params['city_code'])) {
            $builder->andWhere('city_code = :city_code:', ['city_code' => $params['city_code']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'area_type',
                'province_code',
                'city_code',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->getAreaItem($items);

            $area_type_item = EnumsService::getInstance()->getReimbursementDomesticAccommodationAreaEnums()['area_type_item'] ?? [];
            $area_type_item = array_column($area_type_item, 'label', 'value');
            foreach ($items as &$val) {
                $val['area_type'] = $area_type_item[$val['area_type']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

    /**
     * 获取省/市名称
     *
     * @param array $list
     * @return array
     */
    public function getAreaItem($list = [])
    {
        $province_codes = array_filter(array_unique(array_column($list, 'province_code')));
        $city_codes     = array_filter(array_unique(array_column($list, 'city_code')));

        $name_field = static::$language == 'th' ? 'name' : 'en_name';

        // 省
        $province_map = [];
        if (!empty($province_codes)) {
            $province_map = SysProvinceModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => array_values($province_codes)],
                'columns'    => ['code', "$name_field AS name"],
            ])->toArray();
            $province_map = array_column($province_map, 'name', 'code');
        }

        // 市
        $city_map = [];
        if (!empty($city_codes)) {
            $city_map = SysCityModel::find([
                'conditions' => 'code IN ({codes:array})',
                'bind'       => ['codes' => array_values($city_codes)],
                'columns'    => ['code', "$name_field AS name"],
            ])->toArray();
            $city_map = array_column($city_map, 'name', 'code');
        }

        foreach ($list as &$info) {
            $info['province_name'] = $province_map[$info['province_code']] ?? '';
            $info['city_name']     = $city_map[$info['city_code']] ?? '';
        }

        return $list;
    }

    /**
     * 境内住宿区域配置添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementDomesticAccommodationArea($params, $user)
    {
        $exist_model = ReimbursementDomesticAccommodationAreaRepository::getInstance()->getOneByAreaCode($params['province_code'], $params['city_code']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_004',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'area_type'     => $params['area_type'],
            'province_code' => $params['province_code'],
            'city_code'     => $params['city_code'],
            'created_at'    => date('Y-m-d H:i:s'),
            'created_id'    => $user['id'],
            'updated_at'    => date('Y-m-d H:i:s'),
            'updated_id'    => $user['id'],
        ];

        $this->logger->info(['reimbursement_domestic_accommodation_area_add_data' => $add_data]);

        $model = new ReimbursementDomesticAccommodationAreaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('境内住宿区域配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 境内住宿区域配置删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementDomesticAccommodationArea($params, $user)
    {
        $model = ReimbursementDomesticAccommodationAreaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info([
            'reimbursement_domestic_accommodation_area_del_data' => $model->toArray(),
            'user_info'                                          => $user,
        ]);
        if ($model->delete() === false) {
            throw new BusinessException('境内住宿区域配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销-境内住宿额度配置列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementDomesticAccommodationQuota($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementDomesticAccommodationQuotaModel::class);

        if (!empty($params['area_type'])) {
            $builder->andWhere('area_type = :area_type:', ['area_type' => $params['area_type']]);
        }

        if (!empty($params['position_type'])) {
            $builder->andWhere('position_type = :position_type:', ['position_type' => $params['position_type']]);
        }

        if (mb_strlen($params['job_level']) > 0) {
            $builder->andWhere('job_level = :job_level:', ['job_level' => $params['job_level']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'position_type',
                'area_type',
                'job_level',
                'standards_amount',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $enums_item         = EnumsService::getInstance()->getReimbursementDomesticAccommodationQuotaEnums();
            $area_type_item     = array_column($enums_item['area_type_item'], 'label', 'value');
            $position_type_item = array_column($enums_item['position_type_item'], 'label', 'value');
            $job_level_item     = array_column($enums_item['job_level_item'], 'label', 'value');
            foreach ($items as &$val) {
                $val['area_type']     = $area_type_item[$val['area_type']] ?? '';
                $val['position_type'] = $position_type_item[$val['position_type']] ?? '';
                $val['job_level']     = $job_level_item[$val['job_level']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

    /**
     * 境内住宿额度配置添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementDomesticAccommodationQuota($params, $user)
    {
        $exist_model = ReimbursementDomesticAccommodationQuotaRepository::getInstance()->getOneByType($params['position_type'],
            $params['area_type'], $params['job_level']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_005',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'position_type'    => $params['position_type'],
            'area_type'        => $params['area_type'],
            'job_level'        => $params['job_level'],
            'standards_amount' => $params['standards_amount'],
            'created_at'       => date('Y-m-d H:i:s'),
            'created_id'       => $user['id'],
            'updated_at'       => date('Y-m-d H:i:s'),
            'updated_id'       => $user['id'],
        ];

        $this->logger->info(['reimbursement_domestic_accommodation_quota_add_data' => $add_data]);

        $model = new ReimbursementDomesticAccommodationQuotaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('境内住宿额度配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 境内住宿额度配置删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementDomesticAccommodationQuota($params, $user)
    {
        $model = ReimbursementDomesticAccommodationQuotaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info([
            'reimbursement_domestic_accommodation_quota_del_data' => $model->toArray(),
            'user_info'                                           => $user,
        ]);
        if ($model->delete() === false) {
            throw new BusinessException('境内住宿额度配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销-境外住宿区域配置列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementOverseasAccommodationArea($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementOverseasAccommodationAreaModel::class);

        if (!empty($params['accommodation_type'])) {
            $builder->andWhere('accommodation_type = :accommodation_type:',
                ['accommodation_type' => $params['accommodation_type']]);
        }

        if (!empty($params['country_id'])) {
            $builder->andWhere('country_id = :country_id:', ['country_id' => $params['country_id']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'accommodation_type',
                'country_id',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $enums_item              = EnumsService::getInstance()->getReimbursementOverseasAccommodationAreaEnums();
            $accommodation_type_item = array_column($enums_item['accommodation_type_item'], 'label', 'value');
            $country_item            = array_column($enums_item['country_item'], 'label', 'value');
            foreach ($items as &$val) {
                $val['accommodation_type'] = $accommodation_type_item[$val['accommodation_type']] ?? '';
                $val['country_name']       = $country_item[$val['country_id']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

    /**
     * 境外住宿区域配置添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementOverseasAccommodationArea($params, $user)
    {
        $exist_model = ReimbursementOverseasAccommodationAreaRepository::getInstance()->getOneByCountryId($params['country_id']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_006',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'accommodation_type' => $params['accommodation_type'],
            'country_id'         => $params['country_id'],
            'created_at'         => date('Y-m-d H:i:s'),
            'created_id'         => $user['id'],
            'updated_at'         => date('Y-m-d H:i:s'),
            'updated_id'         => $user['id'],
        ];

        $this->logger->info(['reimbursement_overseas_accommodation_area_add_data' => $add_data]);

        $model = new ReimbursementOverseasAccommodationAreaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('境外住宿区域配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 境外住宿区域配置删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementOverseasAccommodationArea($params, $user)
    {
        $model = ReimbursementOverseasAccommodationAreaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info([
            'reimbursement_overseas_accommodation_area_del_data' => $model->toArray(),
            'user_info'                                          => $user,
        ]);
        if ($model->delete() === false) {
            throw new BusinessException('境外住宿区域配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销-境外住宿额度配置列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementOverseasAccommodationQuota($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementOverseasAccommodationQuotaModel::class);

        if (!empty($params['accommodation_type'])) {
            $builder->andWhere('accommodation_type = :accommodation_type:',
                ['accommodation_type' => $params['accommodation_type']]);
        }

        if (!empty($params['position_type'])) {
            $builder->andWhere('position_type = :position_type:', ['position_type' => $params['position_type']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'accommodation_type',
                'position_type',
                'standards_amount',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $enums_item              = EnumsService::getInstance()->getReimbursementOverseasAccommodationQuotaEnums();
            $accommodation_type_item = array_column($enums_item['accommodation_type_item'], 'label', 'value');
            $position_type_item      = array_column($enums_item['position_type_item'], 'label', 'value');
            foreach ($items as &$val) {
                $val['accommodation_type'] = $accommodation_type_item[$val['accommodation_type']] ?? '';
                $val['position_type']      = $position_type_item[$val['position_type']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

    /**
     * 境外住宿额度配置添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementOverseasAccommodationQuota($params, $user)
    {
        $exist_model = ReimbursementOverseasAccommodationQuotaRepository::getInstance()->getOneByType($params['position_type'],
            $params['accommodation_type']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_007',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'position_type'      => $params['position_type'],
            'accommodation_type' => $params['accommodation_type'],
            'standards_amount'   => $params['standards_amount'],
            'created_at'         => date('Y-m-d H:i:s'),
            'created_id'         => $user['id'],
            'updated_at'         => date('Y-m-d H:i:s'),
            'updated_id'         => $user['id'],
        ];

        $this->logger->info(['reimbursement_overseas_accommodation_quota_add_data' => $add_data]);

        $model = new ReimbursementOverseasAccommodationQuotaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('境外住宿额度配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 境外住宿额度配置删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementOverseasAccommodationQuota($params, $user)
    {
        $model = ReimbursementOverseasAccommodationQuotaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info([
            'reimbursement_overseas_accommodation_quota_del_data' => $model->toArray(),
            'user_info'                                           => $user,
        ]);
        if ($model->delete() === false) {
            throw new BusinessException('境外住宿额度配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 租车费额度配置添加
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addReimbursementCarRentalQuota($params, $user)
    {
        $exist_model = ReimbursementCarRentalQuotaRepository::getInstance()->getOneByType($params['position_type'],
            $params['job_level']);
        if (!empty($exist_model)) {
            throw new ValidationException(static::$t->_('business_setting_save_error_008',
                ['config_id' => $exist_model->id]), ErrCode::$VALIDATE_ERROR);
        }

        $add_data = [
            'position_type'              => $params['position_type'],
            'job_level'                  => $params['job_level'],
            'is_required_email_approval' => $params['is_required_email_approval'],
            'standards_amount'           => $params['standards_amount'],
            'created_at'                 => date('Y-m-d H:i:s'),
            'created_id'                 => $user['id'],
            'updated_at'                 => date('Y-m-d H:i:s'),
            'updated_id'                 => $user['id'],
        ];

        $this->logger->info(['reimbursement_car_rental_quota_add_data' => $add_data]);

        $model = new ReimbursementCarRentalQuotaModel();
        if ($model->i_create($add_data) === false) {
            throw new BusinessException('租车费额度配置添加失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 租车费额度配置删除
     * @param $params
     * @param $user
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function delReimbursementCarRentalQuota($params, $user)
    {
        $model = ReimbursementCarRentalQuotaRepository::getInstance()->getOneById($params['id']);
        if (empty($model)) {
            throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info([
            'reimbursement_car_rental_quota_del_data' => $model->toArray(),
            'user_info'                               => $user,
        ]);
        if ($model->delete() === false) {
            throw new BusinessException('租车费额度配置删除失败, 原因可能是=' . $model->getErrorMessagesString(),
                ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }

    /**
     * 获取报销-租车费额度配置列表
     * @param $params
     * @return mixed
     */
    public function getReimbursementCarRentalQuota($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(ReimbursementCarRentalQuotaModel::class);

        if (!empty($params['position_type'])) {
            $builder->andWhere('position_type = :position_type:', ['position_type' => $params['position_type']]);
        }

        if (mb_strlen($params['job_level']) > 0) {
            $builder->andWhere('job_level = :job_level:', ['job_level' => $params['job_level']]);
        }

        $builder->columns('count(id) as count');
        $total_count = (int)$builder->getQuery()->getSingleResult()->count;
        $items       = [];
        if ($total_count) {
            $builder->columns([
                'id',
                'position_type',
                'job_level',
                'is_required_email_approval',
                'standards_amount',
            ]);

            $builder->orderBy('id DESC');
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();

            $enums_item                      = EnumsService::getInstance()->getReimbursementCarRentalQuotaEnums();
            $job_level_item                  = array_column($enums_item['job_level_item'], 'label', 'value');
            $position_type_item              = array_column($enums_item['position_type_item'], 'label', 'value');
            $is_required_email_approval_item = array_column($enums_item['is_required_email_approval_item'], 'label',
                'value');
            foreach ($items as &$val) {
                $val['job_level']                  = $job_level_item[$val['job_level']] ?? '';
                $val['position_type']              = $position_type_item[$val['position_type']] ?? '';
                $val['is_required_email_approval'] = $is_required_email_approval_item[$val['is_required_email_approval']] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => $total_count,
            ],
        ];
    }

}
