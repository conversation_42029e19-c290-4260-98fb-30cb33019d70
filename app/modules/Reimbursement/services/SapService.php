<?php
/**
 * Created by PhpStorm.
 * Date: 2021/8/12
 * Time: 14:17
 */

namespace App\Modules\Reimbursement\Services;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\SapsService;
use App\Modules\Reimbursement\Models\RequestSapLog;
use App\Library\Enums\GlobalEnums;
use \Exception;


class SapService extends BaseService
{
    const LEDGER_ACCOUNT_ID_1 = 1;//核算科目开头为1
    const LEDGER_ACCOUNT_ID_2 = 2;//核算科目开头为2
    const LEDGER_ACCOUNT_ID_6 = 6;//核算科目开头为6
    const SAP_LOG_TYPE = 1;//报销sap log

    private static $instance;


    private function __construct()
    {

    }

    /**
     * @return  SapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function ReimbursementToSap($data, $ledger_info)
    {
        try {
            if (empty($data)) {
                return [];
            }

            $wht_type_arr = EnumsService::getInstance()->getWhtRateCategoryMap(0);

            foreach ($data['items'] as $k => $v) {
                $v['tax_not'] = bcdiv($v['tax_not'], 1000, 2);//发票金额不含税
                $v['wht_tax_amount'] = bcdiv($v['wht_tax_amount'], 1000, 2);
                $v['tax'] = bcdiv(($v['tax']), 1000, 2);

                $v['ledger_account'] = $ledger_info[$v['ledger_account_id']] ?? '';
                $v['wht_type_name'] = $wht_type_arr[$v['wht_type']] ?? '';
                //不同国家vat 计算逻辑
                $v['vat_code'] = $this->vat_code(bcdiv($v['rate'], 10), $v['deductible_vat_tax']);

                $v['voucher_description'] = str_replace('&', '', $v['voucher_description']);

                if (substr($v['ledger_account'], 0, 1) == self::LEDGER_ACCOUNT_ID_1 || substr($v['ledger_account'], 0, 1) == self::LEDGER_ACCOUNT_ID_2) {//科目编号1开头的或2开头的，默认FEX01
                    $v['profit_center'] = $data['cost_company_id'];
                }

                if (substr($v['ledger_account'], 0, 1) != self::LEDGER_ACCOUNT_ID_6) {//首字母不含6pc code 为空
                    $v['cost_center_code'] = '';
                } else {
                    $v['profit_center'] = '';
                }

                $data['items'][$k] = $v;
            }

            $data['vat_code'] = $this->de_vat_code();

            return $this->newCreateSap($data);

        } catch (Exception $e) {
            $this->logger->warning('reimbursement_sap_service_exception: ' . $e->getMessage());
            return [];
        }
    }


    /**
     * 菲律宾
     * vat 税率转换
     * */
    protected function vat_code_ph($vat_rate)
    {
        switch ($vat_rate) {
            case 0:
                $vate_code = '012';
                break;
            case 12:
                $vate_code = '011';

                break;
            default:
                $vate_code = '';
        }
        return $vate_code;

    }

    /**
     * 新版报销
     * 不在区分场景
     * 需求https://l8bx01gcjr.feishu.cn/docs/doccnXPxMGjok540lyhbszt7ytG#RrWfLB
     * 根据金额是否需要传输该行
     * */
    public function reimbursementCreateSap($data)
    {
        $item = '';
        $tax_item = '';
        $i = 0;
        //税代码
        $tax_code_arr = [];
        foreach ($data['items'] as $k => $v) {

            $tax_code = $data['vat_code'];
            if ($v['deductible_tax_amount'] == 0) {
                $tax_code = $v['vat_code'];
            }

            $tax_code_arr[$tax_code] = bcadd($tax_code_arr[$tax_code] ?? 0, $v['deductible_tax_amount'], 2);
            $tax = '<Tax>				
		    <TaxCountryCode>' . get_country_code() . '</TaxCountryCode>			
		    <ProductTaxationCharacteristicsCode listID="' . get_country_code() . '">' . $tax_code . '</ProductTaxationCharacteristicsCode>			
		    <TaxJurisdictionCode></TaxJurisdictionCode>			
		   </Tax>';
            if ($v['tax_not'] > 0) {
                $i++;
                $item .= '<Item>
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['tax_not'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_code'] . '</CostCentreID>					
			</AccountingCodingBlock>
                <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>'
                    . $tax .
                    '</Item>';
            }


            if ($v['wht_tax_amount'] > 0) {
                $i++;

                $item .= ' <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['wht_tax_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>					
            </Item>';
            }


        }
        $item .= '<Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 1) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$data['real_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
     
			    <!--文本描述-->					
               <Note>' . $data['order_no'] . '</Note>					
            </Item>';

        if ($data['loan_amount'] != 0) {
            $i = $i + 2;
            $data['loan_note'] = $this->loanNote($data['created_id'], $data['lno']);

            $item .= '<Item>                                        
            <OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>                                        
               <!--总账科目-->                                        
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>
               <!--贷方金额（负数）-->                                                
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$data['loan_amount'] . '</BusinessTransactionCurrencyAmount>                                        
               <!--文本描述-->                                        
               <Note>' . $data['loan_note'] . '</Note>                                        
            </Item>';
        }


        foreach ($tax_code_arr as $key => $value) {
            $i++;
            $tax_item .= '<TaxItem>	
   <ObjectNodeSenderTechnicalID>1</ObjectNodeSenderTechnicalID>	
   <OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>	
   <TaxCountryCode>' . get_country_code() . '</TaxCountryCode>	
   <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
   <ProductTaxationCharacteristicsCode listID="' . get_country_code() . '">' . $key . '</ProductTaxationCharacteristicsCode>	
   <TaxJurisdictionCode></TaxJurisdictionCode>	
   <BusinessTransactionCurrencyNonDeductibleAmount currencyCode="' . $data['currency'] . '">0</BusinessTransactionCurrencyNonDeductibleAmount>
   <BusinessTransactionCurrencyAmount currencyCode="' . $data['currency'] . '">' . $value . '</BusinessTransactionCurrencyAmount>
   <Note>' . $data['order_no'] . '</Note>
  </TaxItem>';

        }


        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>					
   <soapenv:Body>					
      <glob:JournalEntryBundleCreateRequest>					
         <BasicMessageHeader>					
         </BasicMessageHeader>					
         <!-- 维护日记账分录 -->					
         <JournalEntry>					
            <!-- 公司代码 泰国公司固定FEX01 -->					
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>					
            <!-- 任意值，原始单据编号  传OA系统原始单据号，供后续查询分析使用 -->					
            <OriginalEntryDocumentReferenceID>' . $data['order_no'] . '</OriginalEntryDocumentReferenceID>					
            <!-- 固定值，通信系统 -->					
            <OriginalEntryDocumentReferenceBusinessSystemID>BYDHOST</OriginalEntryDocumentReferenceBusinessSystemID>					
            <!-- 过账日期 -->					
            <AccountingBusinessTransactionDate>' . $data['apply_date'] . '</AccountingBusinessTransactionDate>					
            <!-- 固定值，原始单据类型 -->					
            <AccountingBusinessTransactionTypeCode>701</AccountingBusinessTransactionTypeCode>					
             <!--  输入额外参考信息 -->					
            <OriginalEntryDocumentExternalID>' . $data['extra_message'] . '</OriginalEntryDocumentExternalID>					
            <!-- 固定值，日记账分录类型 -->					
            <AccountingDocumentTypeCode>00107</AccountingDocumentTypeCode>					
             <!-- 输入凭证摘要 -->					
		 <Note>' . $data['order_no'] . '</Note>' . $item . $tax_item .
            '</JournalEntry>					
      </glob:JournalEntryBundleCreateRequest>					
   </soapenv:Body>					
</soapenv:Envelope>';

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managejournalentryin', $post_xml);

        preg_match_all("/\<JournalEntryCreateConfirmationBundleJournalEntry\>(.*?)\<\/JournalEntryCreateConfirmationBundleJournalEntry\>/s", $xml, $re_data);

        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();


        $logModel->save(['uuid' => $return_data['UUID'] ?? '', 'order_code' => $data['order_no'], 'type' => 1, 'request_data' => $post_xml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);


        return $return_data;

    }

    /**
     * 泰国
     * vat 税率转换
     * */
    public function vat_code_th($vat_rate, $deductible_vat_tax)
    {
        $vate_code = '';
        switch ($vat_rate) {
            case 0:
                $vate_code = '001';
                break;
            case 7:
                if (0 == $deductible_vat_tax) {
                    $vate_code = '012';

                } elseif ('8.82' == $deductible_vat_tax) {
                    $vate_code = 'Z20';
                }

                break;
            default:
                $vate_code = '';
        }
        return $vate_code;

    }


    /**
     * 马来
     * vat 税率转换
     * */
    public function vat_code_my($vat_rate)
    {
        switch ($vat_rate) {
            case 0:
                $vate_code = '630';
                break;
            case 5:
                $vate_code = '611';
                break;
            case 6:
                $vate_code = '810';
                break;
            case 10:
                $vate_code = '610';
                break;
            case 8:
                $vate_code = 'Z01';
                break;
            default:
                $vate_code = '';
        }
        return $vate_code;

    }

    public function vat_code($vat_rate, $deductible_tax_amount)
    {
        $vat_code = '';
        switch (get_country_code()) {
            case 'TH':
                $vat_code = $this->vat_code_th($vat_rate, $deductible_tax_amount);

                break;
            case 'PH':
                $vat_code = $this->vat_code_ph($vat_rate);

                break;
            case 'MY':
                $vat_code = $this->vat_code_my($vat_rate);
                break;
            default;

        }
        return $vat_code;
    }

    //可抵扣taxitem 税码
    public function de_vat_code()
    {
        $vat_code = '';
        switch (get_country_code()) {
            case 'TH':
                $vat_code = 'Z20';

                break;
            case 'PH':
                $vat_code = '010';

                break;
            case 'MY':
                $vat_code = '';
                break;
            default;

        }
        return $vat_code;
    }

    public function loanNote($create_id, $loan_no)
    {
        $note = '';
        switch (get_country_code()) {
            case  GlobalEnums::TH_COUNTRY_CODE:
                $note = $create_id . '#loan no.' . $loan_no;

                break;
            case  GlobalEnums::PH_COUNTRY_CODE:
                $note = 'E' . $create_id;

                break;
            case GlobalEnums::MY_COUNTRY_CODE:
                $note = $create_id;
                break;
            default;

        }
        return $note;
    }

    /**
     * 21568【TH MY|OA|报销SAP】报销对接SAP贷方科目调整
     * 1. CR实付金额总计对应的会计科目调整
            泰国从********调整为********
            马来从********调整为********
     *      菲律宾不变
     * @return string
     */
    public function getRealAmountGeneralLedgerAccountAliasCode()
    {
        $code = '';
        switch (get_country_code()) {
            case  GlobalEnums::TH_COUNTRY_CODE:
                $code = '********';
                break;
            case GlobalEnums::MY_COUNTRY_CODE:
                $code = '********';
                break;
            case  GlobalEnums::PH_COUNTRY_CODE:
                $code = '********';
                break;
            default;

        }
        return $code;
    }

    /**
     * 新版传输规则
     * 不在计算金额场景
     * 需求https://flashexpress.feishu.cn/docx/doxcnO3dnwK069Npv9pfpTAPj0f
     * 发票金额、 wht税额（没有不传）、实付金额（没有不传）、借款
     * */
    public function newCreateSap($data)
    {
        $item_xml = '';
        $i = 0;
        foreach ($data['items'] as $k => $v) {
            $i++;
            $vat_code = $data['vat_code'];
            if ($v['deductible_tax_amount'] == 0) {//可抵扣税额为0传可抵扣代码
                $vat_code = $v['vat_code'];
            }
            $item_xml .= '<!--第一行:-->
            <AccountAssignment actionCode="01">
                  <!--编号，任意输入不允许重复:-->
               <ObjectNodeSenderTechnicalID>' . $i . '</ObjectNodeSenderTechnicalID>
               <!--成本中心:-->
               <CostCentreID>' . $v['cost_center_code'] . '</CostCentreID>
            <!--Optional:-->
               <ProfitCentreID>' . $v['profit_center'] . '</ProfitCentreID>
               <!--行项目描述:-->
               <Note>' . $v['voucher_description'] . '</Note>
               <!--总账科目:-->
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>
               <!--税务代码:-->
               <ProductTaxationCharacteristicsCode listID="' . get_country_code() . '">' . $vat_code . '</ProductTaxationCharacteristicsCode>
               <!--借方:-->
               <TransactionCurrencyDebitNetAmount ' . ' currencyCode="' . $data['currency'] . '">' . $v['tax_not'] . '</TransactionCurrencyDebitNetAmount>
                              <!--税额:-->
               <TransactionCurrencyTaxAmount ' . ' currencyCode="' . $data['currency'] . '">' . $v['tax'] . '</TransactionCurrencyTaxAmount>

          
            </AccountAssignment>';

            if ($v['wht_tax_amount'] > 0) {//有税额传输
                $i++;
                $item_xml .= '<!--第二行:-->
            <AccountAssignment actionCode="01">
                  <!--编号，任意输入不允许重复:-->
               <ObjectNodeSenderTechnicalID>' . $i . '</ObjectNodeSenderTechnicalID>
                  <!--成本中心:-->
               <CostCentreID></CostCentreID>
                <!--Optional:-->
               <ProfitCentreID>' . $data['cost_company_id'] . '</ProfitCentreID>
                <!--行项目描述:-->
               <Note>' . trim(mb_substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>
               <!--总账科目:-->
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>        
               <!--贷方:-->
               <TransactionCurrencyCreditNetAmount ' . ' currencyCode="' . $data['currency'] . '">' . -$v['wht_tax_amount'] . '</TransactionCurrencyCreditNetAmount>         
            </AccountAssignment>';
            }
        }

        if ($data['real_amount'] > 0) {//实付总计
            $i = $i + 1;
            $item_xml .= ' <!--第二行:-->
            <AccountAssignment actionCode="01">
                  <!--编号，任意输入不允许重复:-->
               <ObjectNodeSenderTechnicalID>' . $i . '</ObjectNodeSenderTechnicalID>
                <!--行项目描述:-->
               <Note>' . $data['order_no'] . '</Note>
               <!--总账科目:-->
               <GeneralLedgerAccountAliasCode>' . $this->getRealAmountGeneralLedgerAccountAliasCode() . '</GeneralLedgerAccountAliasCode>
               <!--贷方:-->
               <TransactionCurrencyCreditNetAmount ' . ' currencyCode="' . $data['currency'] . '">' . -$data['real_amount'] . '</TransactionCurrencyCreditNetAmount>         
            </AccountAssignment>';
        }

        if ($data['loan_amount'] != 0) {
            $i = $i + 1;
            $data['loan_note'] = $this->loanNote($data['apply_id'], $data['lno']);
            $item_xml .= '<AccountAssignment actionCode="01">
                  <!--编号，任意输入不允许重复:-->
               <ObjectNodeSenderTechnicalID>' . $i . '</ObjectNodeSenderTechnicalID>
                <!--行项目描述:-->
               <Note>' . $data['loan_note'] . '</Note>
               <!--总账科目:-->
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>        
               <!--贷方:-->
               <TransactionCurrencyCreditNetAmount ' . ' currencyCode="' . $data['currency'] . '">' . -$data['loan_amount'] . '</TransactionCurrencyCreditNetAmount>         
            </AccountAssignment>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:ManualTaxEntryBundleMaintainRequest>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
       <!--1 or more repetitions:-->
         <ManualTaxEntry actionCode="01">
            <!--编号，任意输入不允许重复:-->
            <ObjectNodeSenderTechnicalID>' . $data['order_no'] . '</ObjectNodeSenderTechnicalID>
       
            <!--公司编号:-->
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>
            <!--单据日期:-->
            <BusinessTransactionDocumentDate>' . $data['apply_date'] . '</BusinessTransactionDocumentDate>
      
            <!--国家代码:-->
            <CountryCode>' . get_country_code() . '</CountryCode>
            <!--过账日期:-->
            <AccountingTransactionDate>' . $data['posting_date'] . '</AccountingTransactionDate>
            <!--货币:-->
            <TransactionCurrencyCode>' . $data['currency'] . '</TransactionCurrencyCode>
            <!--单据描述:-->
            <Note>' . $data['order_no'] . '</Note>
            <!--外部参考号:-->
            <ExternalReferenceID>' . $data['order_no'] . '</ExternalReferenceID>'
            . $item_xml .
            '</ManualTaxEntry>

      </glob:ManualTaxEntryBundleMaintainRequest>
   </soapenv:Body>
</soapenv:Envelope>';

        $this->logger->info('reimbursement_sap_' . $data['order_no'] . ':' . '====request====' . $post_xml);
        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managetaxreceivablespayablesen', $post_xml);
        $this->logger->info('reimbursement_sap_' . $data['order_no'] . ':' . '====response====' . $xml);

        preg_match_all("/\<ManualTaxEntry\>(.*?)\<\/ManualTaxEntry\>/s", $xml, $re_data);

        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);

        $return_data['log'] = [];
        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();
        $sap_log = [
            'uuid' => $return_data['UUID'] ?? '',
            'order_code' => $data['order_no'],
            'type' => self::SAP_LOG_TYPE,
            'request_data' => $post_xml,
            'response_data' => $xml ?? '',
            'create_at' => date('Y-m-d H:i:s')
        ];
        if ($logModel->save($sap_log) === false) {
            $this->logger->warning(['sap_log_save_error' => get_data_object_error_msg($logModel), 'sap_log_data' => $sap_log]);
        }

        return $return_data;
    }


}