<?php

namespace App\Modules\TalentReview\Services;

use App\Library\ApiClient;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Common\Models\ExcelTaskModel;
use App\Models\backyard\HrProbationModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;
use App\Repository\StoreRepository;
use Exception;

class StaffService extends BaseService
{
    public static $staff_profile_validation = [
        'staff_info_id' => 'Required|Int|>>>:staff_info_id params error',
    ];

    public static $staff_contrast_validation = [
        'staff_info_ids' => 'Required|Arr|ArrLenGe:1|>>>:staff_info_ids params error',
        'staff_info_ids[*]' => 'Required|Int|>>>:staff_info_ids params error',
    ];

    public static $search_staff_validation = [
        'search_name' => 'Required|StrLenGeLe:1,50|>>>:Search name length 1-50 characters',
        'search_type' => 'Required|IntIn:1,2|>>>:search_type params error'
    ];

    /**
     * 员工列表搜索条件验证信息
     * @return array
     */
    public function getStaffListParamsValidation()
    {
        return [
            'staff_info_id' => 'Int|>>>:staff_info_id params error',
            'job_title_grade' => 'Int|>>>:job_title_grade params error',
            'department_id' => 'Int|>>>:department_id params error',
            'job_title_id' => 'Int|>>>:job_title_id params error',
            'sex' => 'IntIn:' . implode(',', array_keys(StaffInfoEnums::$staff_sex)) . '|>>>:sex error',
            'average_tenure' => 'IntIn:' . implode(',', array_keys(StaffInfoEnums::$average_tenure)) . '|>>>:average_tenure params error',
            'hire_type' => 'IntIn:' . implode(',', array_keys(StaffInfoEnums::$hire_type)) . '|>>>:hire_type params error',
            'state' => 'Arr|ArrLenGe:1|>>>:state params error',
            'state[*]' => 'IntIn:'.implode(',', array_keys(StaffInfoEnums::$staff_state)).'|>>>:state* params error',
            'education' => 'IntIn:' . implode(',', array_keys(StaffInfoEnums::$education)) . '|>>>:education params error',
            'probation' => 'IntIn:' . implode(',', array_keys(StaffInfoEnums::$probation_status)) . '|>>>:probation params error',
            'hire_date_begin' => 'Date|>>>:hire_date_begin params error',
            'hire_date_end' => 'Date|>>>:hire_date_end params error',
            'store_id' => 'Arr|ArrLenGeLe:1,5|>>>:store params error',
            'store_id[*]' => 'StrLenGeLe:1,50|>>>:Search name length 1-50 characters',
        ];
    }

    /**
     * 员工列表 我的部门/我的管辖/公司全员
     * @param $params
     * @return array
     */
    public function getStaffList($params)
    {
        $staff_scope = $params['staff_scope'] ?? 1;//员工范围：1我的部门；2我的管辖；3公司全员
        $page_size = $params['pageSize'] ?? 20;
        $page_num = $params['pageNum'] ?? 1;
        $total_count = 0;
        $staff_items = [];
        try {
            $offset = $page_size * ($page_num - 1);
            $builder_list = $this->getStaffListBuilder($params);
            $builder_count = $this->getStaffListBuilder($params);
            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();

            $builder_list->limit($page_size, $offset);
            $list = $builder_list->orderBy('hr_staff.hire_date asc')->getQuery()->execute()->toArray();

            $staff_items = $this->combinationStaffList($list, $staff_scope, $params['user']);
            $total_count = !empty($totalCount) ? $totalCount->count : 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'items' => $staff_items,
            'pagination' => [
                'page_num' => $page_num,
                'page_size' => $page_size,
                'total_count' => $total_count,
            ]
        ];
    }

    /**
     * 条件
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    private function getStaffListBuilder($params)
    {
        $staff_info_id = $params['staff_info_id'] ?? 0; //工号
        $job_title_grade = $params['job_title_grade'] ?? '-1'; //职级
        $department_id = $params['department_id'] ?? 0; //部门
        $job_title_id = $params['job_title_id'] ?? 0; //职位
        $sex = $params['sex'] ?? '-1'; //性别
        $average_tenure = $params['average_tenure'] ?? 0; //司龄
        $hire_type = $params['hire_type'] ?? 0; //雇佣类型
        $state = $params['state'] ?? []; //在职状态
        $education = $params['education'] ?? 0; //学历
        $superior_id = $params['superior_id'] ?? 0; //上级id
        $probation = $params['probation'] ?? 0; //试用期状态
        $hire_date_begin = $params['hire_date_begin'] ?? '';
        $hire_date_end = $params['hire_date_end'] ?? '';
        $store_id = $params['store_id'] ?? ''; //网点id

        $staff_scope = $params['staff_scope'];
        $user = $params['user'];

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'hr_staff.staff_info_id',
            'hr_staff.name',
            'hr_staff.sex',
            'hr_staff.name_en',
            'hr_staff.job_title',
            'hr_staff.sys_store_id',
            'hr_staff.sys_department_id',
            'hr_staff.node_department_id',
            'hr_staff.education',
            'hr_staff.hire_type',
            'hr_staff.state',
            'hr_staff.hire_date',
            'hr_staff.manger',
            'hr_staff.job_title_grade_v2',
            'hr_staff.education',
            'hr_staff.average_tenure',
            'hr_staff.wait_leave_state',
            'hr_staff.working_country'
        ]);
        $builder->from(['hr_staff' => HrStaffInfoModel::class]);
        $builder->where('hr_staff.is_sub_staff = 0');
        $builder->inWhere('hr_staff.formal', [1, 4]);//员工属性1正式 4实习生
        $builder->andWhere('hr_staff.working_country  = :working_country:', ['working_country' => $this->getWorkingCountryID()]);

        //我的部门
        if ($staff_scope == self::$staff_scope_department) {
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
            $department_ids = array_map('intval', array_column($department_list, 'id'));
            $builder->andWhere('hr_staff.node_department_id IN ({department_ids:array})',
                ['department_ids' => $department_ids]);
        }

        //我的管辖
        if ($staff_scope == self::$staff_scope_manage) {
            $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
            if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                if (in_array('-2', $areas_range['stores_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $builder->andWhere("(hr_staff.node_department_id IN ({department_ids:array}) or hr_staff.sys_store_id != '-1')", ['department_ids' => $department_ids]);
                } else {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $store_ids = $areas_range['stores_ids'];
                    $builder->andWhere("(hr_staff.node_department_id IN ({department_ids:array}) or hr_staff.sys_store_id IN ({store_ids:array}))", ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                }
            } else {
                if (!empty($areas_range['department_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $builder->andWhere('hr_staff.node_department_id IN ({department_ids:array})', ['department_ids' => $department_ids]);
                }
                if (!empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $builder->andWhere("hr_staff.sys_store_id != '-1'");
                    } else {
                        $store_ids = $areas_range['stores_ids'];
                        $builder->andWhere("hr_staff.sys_store_id IN ({store_ids:array})", ['store_ids' => $store_ids]);
                    }
                }
            }
        }

        if (!empty($staff_info_id)) {
            $builder->andWhere('hr_staff.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        $job_title_grade_ids = array_keys(StaffInfoEnums::$config_job_title_grade_v2);
        if (in_array($job_title_grade, $job_title_grade_ids)) {
            $builder->andWhere('hr_staff.job_title_grade_v2 = :job_title_grade:', ['job_title_grade' => $job_title_grade]);
        }

        if (!empty($job_title_id)) {
            $builder->andWhere('hr_staff.job_title = :job_title_id:', ['job_title_id' => $job_title_id]);
        }
        $sex_ids = array_keys(StaffInfoEnums::$staff_sex);
        if (in_array($sex, $sex_ids)) {
            $builder->andWhere('hr_staff.sex = :sex:', ['sex' => $sex]);
        }

        if (!empty($hire_type)) {
            $builder->andWhere('hr_staff.hire_type = :hire_type:', ['hire_type' => $hire_type]);
        }

        if (!empty($state)) {
            if(in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $state)) {
                if(count($state) == 1) {
                    $builder->andWhere('hr_staff.state = 1 and wait_leave_state = 1');
                } else {
                    $builder->andWhere('(hr_staff.state IN ({state:array}) or hr_staff.wait_leave_state = 1)', ['state' => $state]);
                }
            } else {
                $builder->andWhere('hr_staff.state IN({state:array}) and hr_staff.wait_leave_state = 0', ['state' => $state]);
            }
        } else {
            $builder->inWhere('hr_staff.state', [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]);//1在职 3停职
        }

        if (!empty($education)) {
            $builder->andWhere('hr_staff.education = :education:', ['education' => $education]);
        }

        if (!empty($superior_id)) {
            $builder->andWhere('hr_staff.manger = :superior_id:', ['superior_id' => $superior_id]);
        }

        if (!empty($department_id)) {
            //查找当前部门以及子部门数据
            $node_department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
            $node_department_ids = array_map('intval', array_column($node_department_list, 'id'));
            $builder->andWhere('hr_staff.node_department_id in ({node_department_ids:array})', ['node_department_ids' => $node_department_ids]);
        }

        if (!empty($average_tenure)) {
            //司龄1 => '<0.5',2 => '[0.5,1]',3 => '[1,2]',4 => '[2,3]',5 => '>= 3',
            switch ($average_tenure) {
                case 1:
                    $builder->andWhere('hr_staff.average_tenure < 0.5');
                    break;
                case 2:
                    $builder->andWhere('hr_staff.average_tenure >= 0.5 and hr_staff.average_tenure < 1');
                    break;
                case 3:
                    $builder->andWhere('hr_staff.average_tenure >= 1 and hr_staff.average_tenure < 2');
                    break;
                case 4:
                    $builder->andWhere('hr_staff.average_tenure >= 2 and hr_staff.average_tenure < 3');
                    break;
                case 5:
                    $builder->andWhere('hr_staff.average_tenure >= 3');
                    break;
            }
        }

        if (!empty($probation)) {
            //试用期状态
            $builder->leftJoin(HrProbationModel::class, 'hr_staff.staff_info_id = p.staff_info_id', 'p');

            switch (get_country_code()) {
                case GlobalEnums::TH_COUNTRY_CODE:
                case GlobalEnums::LA_COUNTRY_CODE:
                case GlobalEnums::VN_COUNTRY_CODE:
                    $builder->andWhere("hr_staff.hire_date >= '2020-06-13' and ( hr_staff.hire_type = 1 or ( hr_staff.hire_type in (3,4) and hr_staff.hire_times >= 365 ) or (hr_staff.hire_type in (2) and hr_staff.hire_times >= 12) )");
                    break;
                case GlobalEnums::PH_COUNTRY_CODE:
                    $builder->andWhere("hr_staff.hire_type in (1,2) or (hr_staff.hire_type in (3,4) and hr_staff.hire_times >= 365)");
                    break;
                case GlobalEnums::MY_COUNTRY_CODE:
                    $builder->andWhere("hr_staff.hire_type = 1");
                    break;
                default:
                    break;
            }
            if ($probation == 1) {
                $builder->andWhere('p.status = :status: or p.status is null', ['status' => $probation]);
            } else {
                $builder->andWhere('p.status = :status:', ['status' => $probation]);
            }
        }

        if (!empty($hire_date_begin)) {
            $hire_date_begin = date('Y-m-d 00:00:00', strtotime($hire_date_begin));
            $builder->andWhere('hr_staff.hire_date >= :hire_date_begin:', ['hire_date_begin' => $hire_date_begin]);
        }

        if (!empty($hire_date_end)) {
            $hire_date_end = date('Y-m-d 23:59:59', strtotime($hire_date_end));
            $builder->andWhere('hr_staff.hire_date <= :hire_date_end:', ['hire_date_end' => $hire_date_end]);
        }

        if(!empty($store_id)) {
            $builder->andWhere('hr_staff.sys_store_id IN({sys_store_ids:array})', ['sys_store_ids' => $store_id]);
        }

        return $builder;
    }

    /**
     * 组合列表数据
     * @param $staff_list
     * @param $staff_scope
     * @param $user
     * @return array
     */
    private function combinationStaffList($staff_list, $staff_scope, $user)
    {
        $return_data = [];
        try {
            $department_ids = array_column($staff_list, 'node_department_id');
            $job_title_ids = array_column($staff_list, 'job_title');
            $staff_superior_ids = array_column($staff_list, 'manger');
            $store_ids = array_column($staff_list, 'sys_store_id');

            $staff_info_ids = array_column($staff_list, 'staff_info_id');
            $staff_items = $this->getStaffItems($staff_info_ids);

            $department_ids = array_values(array_unique($department_ids));
            $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);

            $job_title_ids = array_values(array_unique($job_title_ids));
            $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

            $superior = (new HrStaffRepository())->getStaffListByStaffIds($staff_superior_ids);

            $store_ids = array_values(array_unique($store_ids));
            $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

            $is_show_job_title_grade = false;
            switch ($staff_scope) {
                case self::$staff_scope_department:
                case self::$staff_scope_manage:
                    $is_show_job_title_grade = true;
                    break;
                case self::$staff_scope_company:
                    $is_show_job_title_grade = $this->isLookJobTitleGrade($user['id']);
                    break;
            }

            foreach ($staff_list as $key => $value) {
                $job_title_grade = '';
                $job_title_grade_text = '';
                if ($is_show_job_title_grade) {
                    $job_title_grade = $value['job_title_grade_v2'];
                    $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                }

                $head_portrait = $staff_items[$value['staff_info_id']]['PROFILE_OBJECT_KEY'] ?? '';
                $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';

                $staff_state = $value['state'];
                $staff_state_text = isset(StaffInfoEnums::$staff_state[$value['state']]) ? static::$t->_(StaffInfoEnums::$staff_state[$value['state']]) : '';
                $wait_leave_state = $value['wait_leave_state'];
                if($staff_state == 1 && $wait_leave_state == 1) {
                    $staff_state_text = static::$t->_(StaffInfoEnums::$staff_state[StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
                }

                if($value['sys_store_id'] == '-1') {
                    $store_name = 'Header Office';
                } else {
                    $store_name = $store_list[$value['sys_store_id']]['name'] ?? '';
                }

                $return_data[] = [
                    'staff_info_id' => $value['staff_info_id'],
                    'name' => $value['name'],
                    'sex' => $value['sex'],
                    'sex_text' => isset(StaffInfoEnums::$staff_sex[$value['sex']]) ? static::$t->_(StaffInfoEnums::$staff_sex[$value['sex']]) : '',
                    'head_portrait' => $head_portrait_url,
                    'job_title_grade' => $job_title_grade,
                    'job_title_grade_text' => $job_title_grade_text,
                    'average_tenure' => $value['average_tenure'] >= 0 ? $value['average_tenure'] : '-',
                    'node_department_id' => $value['node_department_id'],
                    'department_name' => $department_list[$value['node_department_id']]['name'] ?? '',
                    'job_title' => $value['job_title'],
                    'job_title_name' => $job_title_list[$value['job_title']]['job_name'] ?? '',
                    'education' => $value['education'],
                    'education_text' => isset(StaffInfoEnums::$education[$value['education']]) ? static::$t->_(StaffInfoEnums::$education[$value['education']]) : '',
                    'hire_type' => $value['hire_type'],
                    'hire_type_text' => isset(StaffInfoEnums::$hire_type[$value['hire_type']]) ? static::$t->_(StaffInfoEnums::$hire_type[$value['hire_type']]) : '',
                    'state' => $value['state'],
                    'wait_leave_state' => $wait_leave_state,
                    'state_text' => $staff_state_text,
                    'hire_date' => date('Y-m-d', strtotime($value['hire_date'])),
                    'superior_id' => !empty($value['manger']) ? $value['manger'] : '',
                    'superior_name' => $superior[$value['manger']]['name'] ?? '',
                    'sys_store_id' => $value['sys_store_id'],
                    'store_name' => $store_name
                ];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'staff_scope' => $staff_scope,
                'user' => $user,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $return_data;
    }

    /**
     * 人员详情
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffProfile($params)
    {
        $staff_profile = [];
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $staff_scope = $params['staff_scope'] ?? 0;
        $user = $params['user'];
        //验证是否在数据查看范围
        $permission_service = new PermissionsService();
        if($permission_service->validationStaffDataRange([$staff_info_id], $staff_scope, $user) == 0) {
            throw new ValidationException(static::$t->_('talent_review_error_8'));//没有权限查看该员工信息
        }
        try {

            $staff_profile = $this->getStaffInfo($staff_info_id);
            if (!empty($staff_profile)) {
                if($staff_profile['working_country'] != $this->getWorkingCountryID()) {
                    throw new ValidationException(static::$t->_('talent_review_error_9'));//不支持查看非本国人员详情
                }

                //判断是否有职级查看权限 如果来源是公司全员 验证是否有配置职级查看权限
                if($staff_scope == self::$staff_scope_company) {
                    if(!$this->isLookJobTitleGrade($user['id'])) {
                        $staff_job_grade_data_ranges = $permission_service->getJobGradeDataRangeStaffs([$staff_info_id], $user['id']);//是否部门查看范围
                        if(!isset($staff_job_grade_data_ranges[$staff_info_id])) {
                            $staffs = $permission_service->validateStaffJobGradeShow([$staff_info_id], $user['id']); //职级查看范围
                            if(!isset($staffs[$staff_info_id])) {
                                $staff_profile['job_title_grade'] = '';
                                $staff_profile['job_title_grade_text'] = '';
                            }
                        }
                    }
                }

                //员工员工生命周期轨迹
                $staff_profile['work_track'] = $this->getStaffWorkTrackList($staff_info_id, $staff_profile['hire_date']);
                //教育经历
                $staff_profile['education_list'] = $this->getStaffEducationList($staff_info_id);
                //过往工作经历
                $staff_profile['work_experience'] = $this->getStaffWorkExperienceList($staff_info_id);
                //转岗记录
                $staff_profile['job_transfer'] = $this->getStaffJobTransferList($staff_info_id);
                //电子警告记录
                $staff_profile['message_warning'] = $this->getStaffMessageWarningList($staff_info_id);
                //犯罪记录
                $staff_profile['criminal_list'] = $this->getStaffCriminalList($staff_info_id);
                //培训记录
                $staff_profile['learning_plan_list'] = $this->getStaffLearningPlanLis($staff_info_id, $user);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $staff_profile;
    }

    /**
     * 员工基本信息
     * @param $staff_info_id
     * @return array
     */
    public function getStaffInfo($staff_info_id)
    {
        //员工详情
        $staff = [];
        try {
            $hr_staff_repository = new HrStaffRepository();
            $staff_info = $hr_staff_repository->getStaffDetail($staff_info_id);

            if (!empty($staff_info)) {
                $staff_items = $this->getStaffItems([$staff_info_id], ['PROFILE_OBJECT_KEY', 'WORKING_COUNTRY']);
                //头像
                $head_portrait = $staff_items[$staff_info_id]['PROFILE_OBJECT_KEY'] ?? '';
                $working_country = $staff_items[$staff_info_id]['WORKING_COUNTRY'] ?? '';
                //试用期
                $staff_probation = $hr_staff_repository->getStaffProbationList([$staff_info_id]);
                $probation_status = $staff_probation[$staff_info_id]['status'] ?? '';
                $probation_status_text = isset(StaffInfoEnums::$probation_status[$probation_status]) ? static::$t->_(StaffInfoEnums::$probation_status[$probation_status]) : '';

                $department_list = (new DepartmentRepository())->getDepartmentByIds([$staff_info['node_department_id']]);
                $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds([$staff_info['job_title']]);
                $store_list = (new StoreRepository())->getStoreListByIds([$staff_info['sys_store_id']]);
                $store_name = $store_list[$staff_info['sys_store_id']]['name'] ?? '';
                if ($staff_info['sys_store_id'] == '-1') {
                    $store_name = 'Header Office';
                }

                $superior = [];
                if (!empty($staff_info['manger'])) {
                    $superior = $hr_staff_repository->getStaffListByStaffIds([$staff_info['manger']]);
                }

                $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';
                $hire_date = date('Y-m-d', strtotime($staff_info['hire_date']));
                $staff = [
                    'staff_info_id' => $staff_info['staff_info_id'],
                    'name' => $staff_info['name'],
                    'sex' => $staff_info['sex'],
                    'sex_text' => isset(StaffInfoEnums::$staff_sex[$staff_info['sex']]) ? static::$t->_(StaffInfoEnums::$staff_sex[$staff_info['sex']]) : '',
                    'head_portrait' => $head_portrait_url,
                    'job_title_grade' => $staff_info['job_title_grade_v2'],
                    'job_title_grade_text' => StaffInfoEnums::$config_job_title_grade_v2[$staff_info['job_title_grade_v2']] ?? '',
                    'average_tenure' => $staff_info['average_tenure'] >= 0 ? $staff_info['average_tenure'] : '-',
                    'node_department_id' => $staff_info['node_department_id'],
                    'department_name' => $department_list[$staff_info['node_department_id']]['name'] ?? '',
                    'job_title' => $staff_info['job_title'],
                    'job_title_name' => $job_title_list[$staff_info['job_title']]['job_name'] ?? '',
                    'education' => $staff_info['education'],
                    'education_text' => isset(StaffInfoEnums::$education[$staff_info['education']]) ? static::$t->_(StaffInfoEnums::$education[$staff_info['education']]) : '',
                    'hire_type' => $staff_info['hire_type'],
                    'hire_type_text' => isset(StaffInfoEnums::$hire_type[$staff_info['hire_type']]) ? static::$t->_(StaffInfoEnums::$hire_type[$staff_info['hire_type']]) : '',
                    'state' => $staff_info['state'],
                    'state_text' => isset(StaffInfoEnums::$staff_state[$staff_info['state']]) ? static::$t->_(StaffInfoEnums::$staff_state[$staff_info['state']]) : '',
                    'hire_date' => $hire_date,
                    'superior_id' => $staff_info['manger'],
                    'superior_name' => $superior[$staff_info['manger']]['name'] ?? '',
                    'probation_status' => $probation_status,
                    'probation_status_text' => $probation_status_text,
                    'working_country' => $working_country,
                    'working_country_text' =>isset(StaffInfoEnums::$working_country[$working_country]) ? static::$t->_(StaffInfoEnums::$working_country[$working_country]) : '',
                    'store_id' => $staff_info['sys_store_id'],
                    'store_name' => $store_name,
                ];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $staff;
    }

    /**
     * 人员对比
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function getStaffContrastList($params)
    {
        $staff_info_ids = $params['staff_info_ids'];
        //$staff_info_ids = [25699, 25699];
        $staff_scope = $params['staff_scope'] ?? 0;
        $user = $params['user'];
        $contrast_list = [];
        //验证工号是否在权限查看范围内
        $permission_service = new PermissionsService();
        $staff_count = $permission_service->validationStaffDataRange($staff_info_ids, $staff_scope, $user);
        if(count($staff_info_ids) != $staff_count) {
            throw new ValidationException(static::$t->_('talent_review_error_8'));//没有权限查看该员工信息
        }

        //验证职级是否有查看权限
        $is_look_job_title_grade = $this->isLookJobTitleGrade($user['id']);

        $staff_job_grade_show = [];
        $staff_job_grade_data_ranges = [];
        if(!$is_look_job_title_grade) {
            $staff_job_grade_show = $permission_service->validateStaffJobGradeShow($staff_info_ids, $user['id']);
            $staff_job_grade_data_ranges = $permission_service->getJobGradeDataRangeStaffs($staff_info_ids, $user['id']);
        }

        try {
            foreach ($staff_info_ids as $staff_info_id) {
                $staff_info = $this->getStaffInfo($staff_info_id);
                if(!empty($staff_info)) {
                    //判断是否有职级查看权限 如果来源是公司全员 验证是否有配置职级查看权限
                    if(!$is_look_job_title_grade) { //如果没有公司全员职级查看权限
                        if(!isset($staff_job_grade_data_ranges[$staff_info_id])) { //如果不是部门管辖范围内
                            if(!isset($staff_job_grade_show[$staff_info_id])) { //如果页不是配置的职级查看权限
                                $staff_info['job_title_grade'] = '';
                                $staff_info['job_title_grade_text'] = '';
                            }
                        }
                    }

                    $staff_info['education_list'] = $this->getStaffEducationList($staff_info_id);//教育经历
                    //过往工作经历
                    $staff_info['work_experience'] = $this->getStaffWorkExperienceList($staff_info_id);
                    //转岗记录
                    $staff_info['job_transfer'] = $this->getStaffJobTransferList($staff_info_id);
                    //电子警告记录
                    $staff_info['message_warning'] = $this->getStaffMessageWarningList($staff_info_id);
                    //犯罪记录
                    $staff_info['criminal_list'] = $this->getStaffCriminalList($staff_info_id);
                    //培训记录
                    $staff_info['learning_plan_list'] = $this->getStaffLearningPlanLis($staff_info_id, $user);

                    $contrast_list[] = $staff_info;
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $contrast_list;
    }

    /**
     * 员工搜索
     * @param $params
     * @return mixed
     */
    public function searchStaffList($params)
    {
        $search_name = $params['search_name'] ?? '';
        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        $search_type = $params['search_type']; //search_type 1列表搜索2对比搜索
        $list = [];
        try {
            if (empty($search_name)) {
                return $list;
            }
            $conditions = "formal IN({formal:array}) and state IN({state:array}) and is_sub_staff = :is_sub_staff: and working_country = :working_country:";
            $bind = [
                'formal' => [1, 4],
                'state' => [1, 3],
                'is_sub_staff' => 0,
                'working_country' => $this->getWorkingCountryID(),
            ];

            if ($search_type == 1) {
                //列表搜索
                $where = $this->getStaffDataRange($user['id'], $staff_scope);
                if (empty($where)) {
                    throw new ValidationException('error');
                }
                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);
            } else {
                if ($staff_scope != BaseService::$staff_scope_company) {
                    //获取员工数据范围 我的部门/我的管辖
                    //对比搜索 取合集
                    //判断是否有配置公司全员数据查看权限
                    $permission_service = new PermissionsService();
                    if (!$permission_service->isEnvAdminAuthorize($user['id'])) {
                        //判断是否部门负责人
                        $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                        $department_ids = array_column($department_list, 'id');

                        $stores_ids = [];
                        //判断是否有管辖区域
                        if ($permission_service->isRolePermission($user['id'], BaseService::$HRBP_ROLE_ID)) {
                            $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                            $department_ids = !empty($areas_range['department_ids']) ? array_merge($department_ids, $areas_range['department_ids']) : $department_ids;
                            $stores_ids = $areas_range['stores_ids'];
                        }

                        if (!empty($department_ids) && !empty($stores_ids)) {
                            if (in_array('-2', $stores_ids)) {
                                $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id != '-1')";
                                $bind['department_ids'] = $department_ids;
                            } else {
                                $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id IN ({store_ids:array}))";
                                $bind['department_ids'] = $department_ids;
                                $bind['store_ids'] = $stores_ids;
                            }
                        } else {
                            if (!empty($department_ids)) {
                                $conditions .= " and node_department_id IN ({department_ids:array})";
                                $bind['department_ids'] = $department_ids;
                            }
                            if (!empty($stores_ids)) {
                                if (in_array('-2', $stores_ids)) {
                                    $conditions .= " and sys_store_id != '-1')";
                                } else {
                                    $conditions .= " and sys_store_id IN ({store_ids:array})";
                                    $bind['store_ids'] = $stores_ids;
                                }
                            }
                        }
                    }
                }
            }

            $conditions .= " and (name like :search_name: or staff_info_id like :search_name:)";
            $bind = array_merge($bind, ['search_name' => '%' . $search_name . '%']);
            $list = HrStaffInfoModel::find([
                'column' => 'staff_info_id, name',
                'conditions' => $conditions,
                'bind' => $bind,
                'limit' => 10
            ])->toArray();
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $list;
    }

    /**
     * 获取员工item表信息
     * @param $staff_info_ids
     * @param array $items
     * @return array
     */
    public function getStaffItems($staff_info_ids, $items = ['PROFILE_OBJECT_KEY'])
    {
        $staff_items_list = [];
        try {
            $list = (new HrStaffRepository())->getStaffItems($staff_info_ids, $items);
            if (!empty($list)) {
                foreach ($list as $key => $value) {
                    $staff_items_list[$value['staff_info_id']][$value['item']] = $value['value'];
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_ids' => $staff_info_ids, 'items' => $items],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $staff_items_list;
    }

    /**
     * 员工教育经历
     * @param $staff_info_id
     * @return mixed
     */
    public function getStaffEducationList($staff_info_id)
    {
        $education_list = [];
        try {
            $education_list = (new HrStaffRepository())->getStaffEducationList($staff_info_id);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $education_list;
    }

    /**
     * 员工过往工作经历
     * @param $staff_info_id
     * @return mixed
     */
    public function getStaffWorkExperienceList($staff_info_id)
    {
        $experience_list = [];
        try {
            $experience_list = (new HrStaffRepository())->getStaffWorkExperienceList($staff_info_id);
            if (!empty($experience_list)) {
                foreach ($experience_list as $key => $value) {

                    if ($this->checkDate($value['work_time_start']) !== false && $this->checkDate($value['work_time_end']) !== false) {
                        $diff_day = (strtotime($value['work_time_end']) - strtotime($value['work_time_start'])) / 86400;
                        $average_tenure = bcdiv($diff_day, 365, 1);
                        $experience_list[$key]['average_tenure'] = $average_tenure >= 0 ? $average_tenure : '';
                    } else {
                        $experience_list[$key]['average_tenure'] = '';
                        $experience_list[$key]['work_time_start'] = $this->checkDate($value['work_time_start']) !== false ? $value['work_time_start'] : '';
                        $experience_list[$key]['work_time_end'] = $this->checkDate($value['work_time_end']) !== false ? $value['work_time_end'] : '';
                    }
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $experience_list;
    }

    /**
     * 员工轨迹
     * @param $staff_info_id
     * @param $hire_date
     * @return array
     */
    public function getStaffWorkTrackList($staff_info_id, $hire_date)
    {
        $tack_list = [];
        try {
            $tack_list[] = [
                'track_date' => $hire_date,
                'track_type' => 1,
                'track_type_text' => static::$t->_('talent_review_work_track_type_1'),
                'track_content' => static::$t->_('talent_review_work_track_type_1')
            ];
            $job_transfer_list = (new HrStaffRepository())->getStaffJobTransferList($staff_info_id, 'after_date asc');
            if (!empty($job_transfer_list)) {
                $current_job_title_ids = array_column($job_transfer_list, 'current_position_id');
                $after_job_title_ids = array_column($job_transfer_list, 'after_position_id');
                $job_title_ids = array_values(array_unique(array_merge($current_job_title_ids, $after_job_title_ids)));
                $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

                foreach ($job_transfer_list as $key => $value) {
                    $current_job_title_name = $job_title_list[$value['current_position_id']]['job_name'] ?? '';
                    $after_job_title_name = $job_title_list[$value['after_position_id']]['job_name'] ?? '';
                    $tack_list[] = [
                        'track_date' => $value['after_date'],
                        'track_type' => 2,
                        'track_type_text' => static::$t->_('talent_review_work_track_type_2'),
                        'track_content' => $current_job_title_name . '->' . $after_job_title_name
                    ];
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id, 'hire_date' => $hire_date],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $tack_list;
    }

    /**
     * 员工转岗记录
     * @param $staff_info_id
     * @return array
     */
    public function getStaffJobTransferList($staff_info_id)
    {
        $job_transfer_list = [];
        try {
            $job_transfer_list = (new HrStaffRepository())->getStaffJobTransferList($staff_info_id);
            if (!empty($job_transfer_list)) {
                $current_department_ids = array_column($job_transfer_list, 'current_department_id');
                $current_store_ids = array_column($job_transfer_list, 'current_store_id');
                $current_job_title_ids = array_column($job_transfer_list, 'current_position_id');

                $after_department_ids = array_column($job_transfer_list, 'after_department_id');
                $after_store_ids = array_column($job_transfer_list, 'after_store_id');
                $after_job_title_ids = array_column($job_transfer_list, 'after_position_id');

                $department_ids = array_values(array_unique(array_merge($current_department_ids,
                    $after_department_ids)));
                $store_ids = array_values(array_unique(array_merge($current_store_ids, $after_store_ids)));
                $job_title_ids = array_values(array_unique(array_merge($current_job_title_ids, $after_job_title_ids)));

                $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
                $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);
                $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

                $current_manager_ids = array_column($job_transfer_list, 'current_manager_id');
                $after_manager_ids = array_column($job_transfer_list, 'after_manager_id');
                $manager_ids = array_values(array_unique(array_merge($current_manager_ids, $after_manager_ids)));
                $manager_list = (new HrStaffRepository())->getStaffListByStaffIds($manager_ids);

                foreach ($job_transfer_list as $key => $value) {
                    $job_transfer_list[$key]['current_department_name'] = $department_list[$value['current_department_id']]['name'] ?? '';
                    $job_transfer_list[$key]['current_store_name'] = $store_list[$value['current_store_id']]['name'] ?? '';
                    $job_transfer_list[$key]['current_position_name'] = $job_title_list[$value['current_position_id']]['job_name'] ?? '';
                    $job_transfer_list[$key]['after_department_name'] = $department_list[$value['after_department_id']]['name'] ?? '';
                    $job_transfer_list[$key]['after_store_name'] = $store_list[$value['after_store_id']]['name'] ?? '';
                    $job_transfer_list[$key]['after_position_name'] = $job_title_list[$value['after_position_id']]['job_name'] ?? '';
                    $job_transfer_list[$key]['current_manager_name'] = $manager_list[$value['current_manager_id']]['name'] ?? '';
                    $job_transfer_list[$key]['after_manager_name'] = $manager_list[$value['after_manager_id']]['name'] ?? '';
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $job_transfer_list;
    }

    /**
     * 员工犯罪记录
     * @param $staff_info_id
     * @return array
     */
    public function getStaffCriminalList($staff_info_id)
    {
        $criminal_list = [];
        try {
            $criminal_list = (new HrStaffRepository())->getStaffCriminalList($staff_info_id);
            foreach ($criminal_list as $key => $value) {
                $record_case_text = static::$t->_('criminal_case_' . $value['record_case']);
                if($value['record_type'] == 7) {
                    $record_case_text = $value['record_text'];
                }
                $criminal_list[$key]['created_time'] = date('Y-m-d', strtotime($value['created_time']));
                $criminal_list[$key]['record_type_text'] = static::$t->_('criminal_record_' . $value['record_type']);
                $criminal_list[$key]['record_case_text'] = $record_case_text;
                $criminal_list[$key]['record_status_text'] = static::$t->_('record_status_' . $value['record_status']);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $criminal_list;
    }

    /**
     * 员工警告书列表
     * @param $staff_info_id
     * @return array
     */
    public function getStaffMessageWarningList($staff_info_id)
    {
        $warning_list = [];
        try {
            $warning_list = (new HrStaffRepository())->getStaffMessageWarningList($staff_info_id);
            foreach ($warning_list as $key => $value) {
                $warning_list[$key]['type_code_text'] = static::$t->_($value['type_code']);
                $t_warning_type = 'warning_type_' . $value['warning_type'];
                if('PH' == get_country_code() && $value['warning_type'] != 1) {
                    $t_warning_type = 'ph_warning_type_' . $value['warning_type'];
                }
                $warning_list[$key]['warning_type_text'] = static::$t->_($t_warning_type);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $warning_list;
    }

    /**
     * 员工学习计划
     * @param $staff_info_id
     * @param array $user
     * @return array
     */
    public function getStaffLearningPlanLis($staff_info_id, $user = [])
    {
        $learning_plan_list = [];
        try {
            $ac = new ApiClient('school', '', 'external_staff_learning_plan_list', static::$language);
            $api_params = [
                [
                    "staff_info_id" => $staff_info_id,
                    "pageSize" => 1000,
                    "pageNum" => 1
                ],
                $user
            ];
            $ac->setParams($api_params);
            $result = $ac->execute();
            $learning_plan_list = $result['result']['data']['items'] ?? [];
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id, 'user' => $user],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $learning_plan_list;
    }

    /**
     * 添加下载任务
     *
     * @param $params
     * @return bool
     * @throws ValidationException
     */
    public function addDownloadCenter($params)
    {
        try {
            $staff_info_id = $params['user']['id'];
            $params['language'] = static::$language;
            $excel_type = DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_1;
            $file_name = date('YmdHis').'_talent_review_staff_list.xlsx';
            switch ($params['staff_scope']) {
                case BaseService::$staff_scope_department:
                    $excel_type = DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_1;
                    $file_name = date('YmdHis').'_talent_review_staff_list_department.xlsx';
                    break;
                case BaseService::$staff_scope_manage:
                    $excel_type = DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_2;
                    $file_name = date('YmdHis').'_talent_review_staff_list_manage.xlsx';
                    break;
                case BaseService::$staff_scope_company:
                    $excel_type = DownloadCenterEnum::TYPE_TALENT_REVIEW_STAFF_SCOPE_3;
                    $file_name = date('YmdHis').'_talent_review_staff_list_company.xlsx';
                    break;
            }

            $detail = ExcelTaskModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND status = :status: AND is_deleted = :is_deleted: AND type = :type:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                    'status' => DownloadCenterEnum::TASK_STATUS_PENDING,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'type' => $excel_type
                ]
            ]);

            if ($detail) {
                throw new ValidationException(static::$t->_('download_center_error_task_exist'), ErrCode::$VALIDATE_ERROR);//下载任务正在执行，请稍等
            }

            $excel_task_model = new ExcelTaskModel();
            $excel_task_model->executable_path = $this->config->application->appDir . 'cli.php';
            $excel_task_model->action_name = 'talent_review'.DownloadCenterEnum::ACTION_NAME_SPACE_SEPARATOR.'export_staff';
            $excel_task_model->file_name = $file_name;
            $excel_task_model->type = $excel_type;
            $excel_task_model->staff_info_id = $staff_info_id;
            $excel_task_model->args_json = base64_encode(json_encode($params));
            $excel_task_model->created_at = date('Y-m-d H:i:s');
            $excel_task_model->updated_at = date('Y-m-d H:i:s');

            if($excel_task_model->save() === false) {
                $msg = '下载任务生成失败：' . get_data_object_error_msg($excel_task_model);
                $this->logger->error(['params' => $params, 'message' => $msg]);
                throw new ValidationException(static::$t->_('download_center_error_save_error'), ErrCode::$VALIDATE_ERROR);//下载任务生成失败，请稍后重试
            }
            return true;
        } catch (ValidationException $e) {
            throw new ValidationException($e->getMessage(), $e->getCode());
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return false;
    }

    //导出员工数据生成excel文件
    public function exportStaffExcel($params, $excel_task_id, $file_name)
    {
        try {
            $page_size = 1000;//分页导出
            $staff_scope = $params['staff_scope'] ?? 1;
            $user = $params['user'] ?? [];
            $language = $params['language'] ?? 'en';
            self::setLanguage($language);

            $builder_list = $this->getStaffListBuilder($params);
            $builder_count = $this->getStaffListBuilder($params);
            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
            $total_count = !empty($totalCount) ? $totalCount->count : 0;
            $page_count = ceil($total_count / $page_size);//总页数
            $this->getDI()->get('logger')->info('任务id' . $excel_task_id . ':开始执行,总页数：' . $page_count);
            $excel_data = [];
            for ($i = 1; $i <= $page_count; $i++) {
                $offset = $page_size * ($i - 1);
                $builder_list->limit($page_size, $offset);
                $staff_list = $builder_list->orderBy('hr_staff.hire_date asc')->getQuery()->execute()->toArray();
                $this->getDI()->get('logger')->info('任务id' . $excel_task_id . ':页码：' . $i . '----offset:' . $offset . '-----list_count:' . count($staff_list));


                $department_ids = array_column($staff_list, 'node_department_id');
                $job_title_ids = array_column($staff_list, 'job_title');
                $staff_superior_ids = array_column($staff_list, 'manger');
                $store_ids = array_column($staff_list, 'sys_store_id');

                $staff_info_ids = array_column($staff_list, 'staff_info_id');

                $hr_staff_repository = new HrStaffRepository();

                $department_ids = array_values(array_unique($department_ids));
                $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);

                $job_title_ids = array_values(array_unique($job_title_ids));
                $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

                $superior = $hr_staff_repository->getStaffListByStaffIds($staff_superior_ids);

                $store_ids = array_values(array_unique($store_ids));
                $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

                $is_show_job_title_grade = false;
                switch ($staff_scope) {
                    case self::$staff_scope_department:
                    case self::$staff_scope_manage:
                        $is_show_job_title_grade = true;
                        break;
                    case self::$staff_scope_company:
                        $is_show_job_title_grade = $this->isLookJobTitleGrade($user['id']);
                        break;
                }

                //试用期
                $staff_probation = $hr_staff_repository->getStaffProbationList($staff_info_ids);

                foreach ($staff_list as $key => $value) {
                    $job_title_grade_text = '';
                    if ($is_show_job_title_grade) {
                        $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                    }

                    $staff_state = $value['state'];
                    $staff_state_text = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t->_(StaffInfoEnums::$staff_state[$staff_state]) : '';
                    if ($staff_state == 1 && $value['wait_leave_state'] == 1) {
                        $staff_state_text = static::$t->_(StaffInfoEnums::$staff_state[StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
                    }

                    if ($value['sys_store_id'] == '-1') {
                        $store_name = 'Header Office';
                    } else {
                        $store_name = $store_list[$value['sys_store_id']]['name'] ?? '';
                    }
                    $sex_text = isset(StaffInfoEnums::$staff_sex[$value['sex']]) ? static::$t->_(StaffInfoEnums::$staff_sex[$value['sex']]) : '';
                    $education_text = isset(StaffInfoEnums::$education[$value['education']]) ? static::$t->_(StaffInfoEnums::$education[$value['education']]) : '';
                    $job_title_name = $job_title_list[$value['job_title']]['job_name'] ?? '';
                    $department_name = $department_list[$value['node_department_id']]['name'] ?? '';
                    $working_country_text = isset(StaffInfoEnums::$working_country[$value['working_country']]) ? static::$t->_(StaffInfoEnums::$working_country[$value['working_country']]) : '';
                    $probation_status = $staff_probation[$value['staff_info_id']]['status'] ?? '';
                    $hire_type_text = isset(StaffInfoEnums::$hire_type[$value['hire_type']]) ? static::$t->_(StaffInfoEnums::$hire_type[$value['hire_type']]) : '';
                    $hr_probation_status_text = isset(StaffInfoEnums::$probation_status[$probation_status]) ? static::$t->_(StaffInfoEnums::$probation_status[$probation_status]) : '';

                    $superior_id = '';
                    $superior_name = '';
                    if(isset($superior[$value['manger']])) {
                        $superior_id = $value['manger'];
                        $superior_name = $superior[$value['manger']]['name'] ?? '';
                    }
                    $average_tenure = $value['average_tenure'] >=0 ? $value['average_tenure'] : '-';
                    $excel_data[] = [
                        $value['staff_info_id'],
                        $value['name'],
                        $sex_text,
                        $job_title_grade_text,
                        $average_tenure,
                        $education_text,
                        $job_title_name,
                        $department_name,
                        $store_name,
                        $working_country_text,
                        $hire_type_text,
                        date('Y-m-d', strtotime($value['hire_date'])),
                        $staff_state_text,
                        $hr_probation_status_text,
                        $superior_id,
                        $superior_name,
                    ];
                }
            }
            $excel_header = [
                static::$t->_('talent_review_staff_info_id'),
                static::$t->_('talent_review_staff_name'),
                static::$t->_('talent_review_staff_sex'),
                static::$t->_('talent_review_staff_grade'),
                static::$t->_('talent_review_staff_average_tenure'),
                static::$t->_('talent_review_staff_education'),
                static::$t->_('talent_review_staff_job_title'),
                static::$t->_('talent_review_department'),
                static::$t->_('talent_review_staff_store'),
                static::$t->_('talent_review_working_country'),
                static::$t->_('talent_review_staff_hire_type'),
                static::$t->_('talent_review_staff_hire_date'),
                static::$t->_('talent_review_staff_state'),
                static::$t->_('talent_review_staff_probation_status'),
                static::$t->_('talent_review_staff_superior_id'),
                static::$t->_('talent_review_staff_superior_name'),
            ];
            $res = $this->exportExcel($excel_header, $excel_data, $file_name);
            $file_path = $res['data'];
            $this->getDI()->get('logger')->info('任务id' . $excel_task_id . ':执行完成,文件地址：' . $file_path);

            $excel_task_model = ExcelTaskModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => [
                    'id' => $excel_task_id
                ],
            ]);
            $excel_task_model->file_download_path = $file_path;
            $excel_task_model->status = 1;
            $excel_task_model->finish_at = date('Y-m-d H:i:s');
            $excel_task_model->updated_at = date('Y-m-d H:i:s');

            if($excel_task_model->save() === false) {
                $msg = '下载任务生成失败：' . get_data_object_error_msg($excel_task_model);
                $this->logger->error([
                    'params' => $params,
                    'message' => $msg
                ]);
            }
            $this->logger->info('任务id' . $excel_task_id . ':执行完成,修改状态成功，task_id:' . $excel_task_id);
            return true;
        } catch (BusinessException $e) {
            $this->logger->notice([
                'BusinessException' => $e->getMessage(),
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
            return false;
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }
}