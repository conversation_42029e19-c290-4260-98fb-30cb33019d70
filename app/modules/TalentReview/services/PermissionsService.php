<?php

namespace App\Modules\TalentReview\Services;

use App\Models\backyard\SysDepartmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\User\Models\StaffPermissionModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;
use Exception;

class PermissionsService extends BaseService
{
    /**
     * 判断工号是否有指定角色
     * @param $user_id
     * @param $role_id
     * @return bool
     */
    public function isRolePermission($user_id, $role_id)
    {
        $role_list = (new HrStaffRepository())->getStaffRoleList($user_id);
        $role_ids = !empty($role_list) ? array_column($role_list, 'position_category') : [];
        return in_array($role_id, $role_ids);
    }

    /**
     * 验证是否env配置成管理员
     * @param $user_id
     * @return bool
     */
    public function isEnvAdminAuthorize($user_id)
    {
        $staff_info_ids = [];
        $ids = EnvModel::getEnvByCode('talent_review_admin_staff_id');
        if (!empty($ids)) {
            $staff_info_ids = array_map("intval", explode(',', $ids));
        }
        return in_array($user_id, $staff_info_ids);
    }

    /**
     * 判断工号是否是部门负责人
     * @param $staff_info_id
     * @return mixed
     */
    public function isDepartmentManager($staff_info_id)
    {
        return (new DepartmentRepository())->departmentManagerExists($staff_info_id);
    }

    /**
     * 管辖部门及子部门列表
     * @param $staff_info_id
     * @return array
     */
    public function getMyDepartmentList($staff_info_id)
    {
        $department_list = [];
        if (!empty($staff_info_id)) {
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($staff_info_id);
        }
        return $department_list;
    }

    /**
     * 获取汇报地图 公司全员 boss id
     * @return array
     */
    public function getReportMapBossId()
    {
        $boss_ids = [];
        try {
            $ids = EnvModel::getEnvByCode('talent_review_report_map_boss_id');
            if (!empty($ids)) {
                $boss_ids = array_map("intval", explode(',', $ids));
            }
        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $boss_ids;
    }

    /**
     * 判断工号是否是部门负责人管辖范围内的员工
     * @param $params
     * @return bool
     */
    public function validationDepartmentStaff($params)
    {
        $manager_id = $params['manager_id'];//部门负责人工号
        $staff_info_id = $params['staff_info_id'];//员工工号

        $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($manager_id);
        if (empty($department_list)) {
            return false;
        }

        $department_ids = array_column($department_list, 'id');

        $count = HrStaffInfoModel::count([
            "state in ({state:array}) and formal IN (1,4) AND is_sub_staff = 0 and node_department_id in ({department_ids:array}) and staff_info_id = :staff_info_id:",
            'bind' => [
                'state' => [1, 3],
                'department_ids' => $department_ids,
                'staff_info_id' => $staff_info_id,
            ]
        ]);
        return $count > 0 ? true : false;
    }

    /**
     * 验证是否配置部门大区片区网点数据范围
     * @param $staff_info_id
     * @return bool
     */
    public function isConfigDataRange($staff_info_id)
    {
        $relations = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);
        if (
            empty($relations['departments'])
            && empty($relations['stores'])
            && empty($relations['regions'])
            && empty($relations['pieces'])
            && empty($relations['store_categories'])
        ) {
            return false;
        }
        return true;
    }

    /**
     * 部门负责人 新增人才盘点菜单
     * @param $manager_id
     * @param $department_id
     * @return bool
     */
    public function addDepartmentPermission($manager_id, $department_id)
    {
        try {
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" => "staff_id = :staff_info_id:",
                "bind" => [
                    'staff_info_id' => $manager_id,
                ]
            ]);

            $ids[] = self::$talent_review_permission_id; //人才盘点
            $ids[] = self::$talent_review_department_staff; //人才盘点 部门成员
            $ids[] = self::$talent_review_department_staff_scope_1; //人才盘点 - 部门成员 - 我的部门
            $ids[] = self::$talent_review_team_analysis; //人才盘点 - 部门分析
            $ids[] = self::$talent_review_team_analysis_staff_scope_1; //人才盘点 - 部门分析 - 我的部门
            $ids[] = self::$talent_review_report_map; //人才盘点 - 汇报地图
            $ids[] = self::$talent_review_report_map_staff_scope_1; //人才盘点 - 汇报地图 - 我的部门

            $ids = array_merge(
                $ids,
                self::$talent_review_department_staff_scope_1_action,
                self::$talent_review_team_analysis_staff_scope_1_action,
                self::$talent_review_report_map_staff_scope_1_action
            );

            if (!empty($staff_permission)) {
                $before_permission_ids_str = $staff_permission->permission_ids;
                $staff_permission_ids = explode(',', $staff_permission->permission_ids);

                $after_permission_ids = array_unique(array_merge($staff_permission_ids, $ids));
                $after_permission_ids_str = implode(',', $after_permission_ids);

                $staff_permission->permission_ids = $after_permission_ids_str;
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                $operate = 'update';
            } else {
                $staff_permission = new StaffPermissionModel();
                $staff_permission->staff_id = $manager_id;
                $staff_permission->permission_ids = implode(',', $ids);
                $staff_permission->is_granted = 1;
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                $before_permission_ids_str = '';
                $after_permission_ids_str = $staff_permission->permission_ids;
                $operate = 'insert';
            }
            $r = $staff_permission->save();

            if (!$r) {
                $msgArr = [];
                $messages = $staff_permission->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                $this->getDI()->get('logger')->error([
                    'manager_id' => $manager_id,
                    'department_id' => $department_id,
                    'before_permission_ids_str' => $before_permission_ids_str,
                    'after_permission_ids_str' => $after_permission_ids_str,
                    'operate' => $operate,
                    'result' => $r,
                    'error_message' => $msgArr
                ]);
                return false;
            }
            $this->getDI()->get('logger')->info([
                'manager_id' => $manager_id,
                'department_id' => $department_id,
                'before_permission_ids_str' => $before_permission_ids_str,
                'after_permission_ids_str' => $after_permission_ids_str,
                'operate' => $operate
            ]);
            return true;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['manager_id' => $manager_id, 'department_id' => $department_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * 部门负责人 删除人才盘点菜单操作
     * @param $manager_id
     * @param $department_id
     * @return bool
     */
    public function delDepartmentPermission($manager_id, $department_id)
    {
        try {
            //如果是多个部门负责人 不进行删除菜单操作
            $department_list = SysDepartmentModel::find([
                "manager_id = :manager_id: and deleted = 0 and id != :department_id:",
                "bind" => [
                    "manager_id" => $manager_id,
                    "department_id" => $department_id
                ]
            ])->toArray();

            if (count($department_list) == 0) {
                $staff_permission = StaffPermissionModel::findFirst([
                    "conditions" => "staff_id = :staff_info_id:",
                    "bind" => [
                        'staff_info_id' => $manager_id,
                    ]
                ]);

                if (!empty($staff_permission)) {
                    $del_ids = [
                        self::$talent_review_department_staff_scope_1,
                        self::$talent_review_team_analysis_staff_scope_1,
                        self::$talent_review_report_map_staff_scope_1,
                    ];
                    $del_ids = array_merge(
                        $del_ids,
                        self::$talent_review_department_staff_scope_1_action,
                        self::$talent_review_team_analysis_staff_scope_1_action,
                        self::$talent_review_report_map_staff_scope_1_action
                    );

                    $permission_ids = explode(',', $staff_permission->permission_ids);

                    //判断是否有 我的管辖和公司全员 如果有的话 不删除上级菜单
                    if (!in_array(self::$talent_review_department_staff_scope_2, $permission_ids)
                        && !in_array(self::$talent_review_department_staff_scope_3, $permission_ids)) {
                        $del_ids[] = self::$talent_review_department_staff;
                    }

                    if (!in_array(self::$talent_review_team_analysis_staff_scope_2, $permission_ids)
                        && !in_array(self::$talent_review_team_analysis_staff_scope_3, $permission_ids)) {
                        $del_ids[] = self::$talent_review_team_analysis;
                    }

                    if (!in_array(self::$talent_review_report_map_staff_scope_3, $permission_ids)) {
                        $del_ids[] = self::$talent_review_report_map;
                    }

                    if (in_array(self::$talent_review_department_staff, $del_ids)
                        && in_array(self::$talent_review_team_analysis, $del_ids)
                        && in_array(self::$talent_review_report_map, $del_ids)) {
                        $del_ids[] = self::$talent_review_permission_id;
                    }

                    $permission_ids = array_diff($permission_ids, $del_ids);
                    $staff_permission->permission_ids = implode(',', $permission_ids);
                    $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                    $result = $staff_permission->save();
                    if (!$result) {
                        $msgArr = [];
                        $messages = $staff_permission->getMessages();
                        foreach ($messages as $message) {
                            $msgArr[] = $message->getMessage();
                        }
                        $this->getDI()->get('logger')->error([
                            'manager_id' => $manager_id,
                            'department_id' => $department_id,
                            'result' => $result,
                            'error_message' => $msgArr
                        ]);
                        return false;
                    }
                } else {
                    $this->getDI()->get('logger')->info([
                        'manager_id' => $manager_id,
                        'department_id' => $department_id,
                        'message' => '负责人没有找到菜单',
                    ]);
                }
            } else {
                $this->getDI()->get('logger')->info([
                    'manager_id' => $manager_id,
                    'department_id' => $department_id,
                    'message' => '多个部门负责人不移除菜单',
                    'department_list' => array_column($department_list, 'id')
                ]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['manager_id' => $manager_id, 'department_id' => $department_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return true;
    }

    /**
     * 给hrbp 增加菜单 人才盘点菜单和组织架构菜单
     * @param $manager_id
     * @return bool
     */
    public function addHrbpPermission($manager_id): bool
    {
        $add_ids[] = self::$talent_review_permission_id;
        $add_ids[] = self::$talent_review_department_staff;
        $add_ids[] = self::$talent_review_department_staff_scope_2;
        $add_ids[] = self::$talent_review_team_analysis;
        $add_ids[] = self::$talent_review_team_analysis_staff_scope_2;

//        $add_ids[] = DepartmentService::$organization_menu_id; //组织架构
//        $add_ids[] = DepartmentService::$organization_department_menu_id; //部门架构
//        $add_ids[] = DepartmentService::$organization_job_manage_menu_id; //职位体系
//        $add_ids[] = DepartmentService::$organization_job_manage_job_menu_id;//职位管理
//        $add_ids[] = DepartmentService::$organization_job_manage_job_log_menu_id; //变更记录

        $add_ids = array_merge(
            $add_ids,
            self::$talent_review_department_staff_scope_2_action,
            self::$talent_review_team_analysis_staff_scope_2_action
//            DepartmentService::$organization_department_menu_action_id,
//            DepartmentService::$organization_job_manage_job_menu_action_id,
//            DepartmentService::$organization_job_manage_job_log_action_id
        );

        try {
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" =>"staff_id = :staff_info_id:",
                "bind"=>[
                    'staff_info_id' => $manager_id,
                ]
            ]);

            if(!empty($staff_permission)) {
                $before_permission_ids_str = $staff_permission->permission_ids;
                $permission_ids = explode(',', $staff_permission->permission_ids);
                $permission_ids = array_unique(array_merge($permission_ids, $add_ids));
                $staff_permission->permission_ids = implode(',', $permission_ids);
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                $after_permission_ids_str = $staff_permission->permission_ids;
            } else {
                $staff_permission = new StaffPermissionModel();
                $staff_permission->staff_id = $manager_id;
                $staff_permission->permission_ids = implode(',', $add_ids);
                $staff_permission->is_granted = 1;
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');

            }
            $result = $staff_permission->save();

            if (!$result) {
                $msgArr = [];
                $messages = $staff_permission->getMessages();
                foreach ($messages as $message) {
                    $msgArr[] = $message->getMessage();
                }
                $this->getDI()->get('logger')->error([
                    'manager_id' => $manager_id,
                    'result' => $result,
                    'error_message' => $msgArr
                ]);
                return false;
            }
            $this->logger->info([
                'manager_id' => $manager_id,
                'result' => $result,
                'message' => 'hrbp_add_talent_review_permission_id',
                'before_permission_id' => $before_permission_ids_str ?? '',
                'after_permission_id' => $after_permission_ids_str ?? ''
            ]);
            return true;
        } catch (Exception $e) {
            $this->logger->error([
                'params' => ['manager_id' => $manager_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
    }

    /**
     * 删除hrbp菜单 人才盘点和组织架构菜单
     * @param $manager_id
     * @return bool
     */
    public function delHrbpPermission($manager_id): bool
    {
        $del_ids[] = self::$talent_review_department_staff_scope_2;
        $del_ids[] = self::$talent_review_team_analysis_staff_scope_2;

//        $del_ids[] = DepartmentService::$organization_department_menu_id;//组织架构 menu
//        $del_ids[] = DepartmentService::$organization_job_manage_job_menu_id;//职位管理 menu
//        $del_ids[] = DepartmentService::$organization_job_manage_job_log_menu_id;//变更记录 menu
        $del_ids = array_merge(
            $del_ids,
            self::$talent_review_department_staff_scope_2_action,
            self::$talent_review_team_analysis_staff_scope_2_action
//            DepartmentService::$organization_department_menu_action_id, //组织架构 action
//            DepartmentService::$organization_job_manage_job_menu_action_id,//职位管理 action
//            DepartmentService::$organization_job_manage_job_log_action_id //变更记录列表 action
        );
        try {
            $staff_permission = StaffPermissionModel::findFirst([
                "conditions" =>"staff_id = :staff_info_id:",
                "bind"=>[
                    'staff_info_id' => $manager_id,
                ]
            ]);

            if(!empty($staff_permission)) {
                $before_permission_ids_str = $staff_permission->permission_ids;
                $permission_ids = explode(',', $staff_permission->permission_ids);

                if(!in_array(self::$talent_review_department_staff_scope_1, $permission_ids)
                    && !in_array(self::$talent_review_department_staff_scope_3, $permission_ids)) {
                    $del_ids[] = self::$talent_review_department_staff;
                }

                if(!in_array(self::$talent_review_team_analysis_staff_scope_1, $permission_ids)
                    && !in_array(self::$talent_review_team_analysis_staff_scope_3, $permission_ids)) {
                    $del_ids[] = self::$talent_review_team_analysis;
                }

                if(!in_array(self::$talent_review_report_map_staff_scope_1, $permission_ids) &&
                    !in_array(self::$talent_review_report_map_staff_scope_3, $permission_ids)) {
                    $del_ids[] = self::$talent_review_report_map;
                }

                if(in_array(self::$talent_review_department_staff, $del_ids)
                    && in_array(self::$talent_review_team_analysis, $del_ids)
                    && in_array(self::$talent_review_report_map, $del_ids)) {
                    $del_ids[] = self::$talent_review_permission_id;
                }

//                //组织架构部分
//                //339 340 341
//                if (!in_array(DepartmentService::$organization_job_manage_job_group_menu_id,
//                        $permission_ids) && !in_array(DepartmentService::$organization_job_manage_jd_menu_id,
//                        $permission_ids) && !in_array(DepartmentService::$organization_job_manage_competency_menu_id,
//                        $permission_ids)) {
//                    $del_ids[] = DepartmentService::$organization_job_manage_menu_id;//职级体系
//                }
//                if (in_array(DepartmentService::$organization_job_manage_menu_id, $del_ids)) {
//                    $del_ids[] = DepartmentService::$organization_menu_id;
//                }

                $permission_ids = array_unique(array_diff($permission_ids, $del_ids));
                $staff_permission->permission_ids = implode(',', $permission_ids);
                $staff_permission->last_updated_at = date('Y-m-d H:i:s');
                $after_permission_ids_str = $staff_permission->permission_ids;
                $result = $staff_permission->save();
                if (!$result) {
                    $msgArr = [];
                    $messages = $staff_permission->getMessages();
                    foreach ($messages as $message) {
                        $msgArr[] = $message->getMessage();
                    }
                    $this->getDI()->get('logger')->error([
                        'manager_id' => $manager_id,
                        'result' => $result,
                        'error_message' => $msgArr
                    ]);
                    return false;
                }
                $this->getDI()->get('logger')->info([
                    'manager_id' => $manager_id,
                    'result' => $result,
                    'message' => 'hrbp_del_talent_review_permission_id',
                    'before_permission_id' => $before_permission_ids_str,
                    'after_permission_id' => $after_permission_ids_str
                ]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['manager_id' => $manager_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return false;
        }
        return true;
    }

    /**
     * @param $staff_info_ids
     * @param $staff_scope
     * @param $user
     * @return int|mixed
     */
    public function validationStaffDataRange($staff_info_ids, $staff_scope, $user)
    {
        $count = 0;
        try {
            $conditions = " staff_info_id IN ({staff_info_ids:array}) and state IN (1,3) and formal IN (1,4) and is_sub_staff = 0 and working_country = :working_country:";
            $bind = [
                'staff_info_ids' => $staff_info_ids,
                'working_country' => $this->getWorkingCountryID()
            ];

            if ($staff_scope != BaseService::$staff_scope_company) {
                //获取员工数据范围 我的部门/我的管辖
                //对比搜索 取合集
                //判断是否有配置公司全员数据查看权限
                //$permission_service = new PermissionsService();
                if (!$this->isEnvAdminAuthorize($user['id'])) {
                    //判断是否部门负责人
                    $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                    $department_ids = array_column($department_list, 'id');

                    $stores_ids = [];
                    //判断是否有管辖区域
                    if ($this->isRolePermission($user['id'], BaseService::$HRBP_ROLE_ID)) {
                        $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                        $department_ids = !empty($areas_range['department_ids']) ? array_merge($department_ids,
                            $areas_range['department_ids']) : $department_ids;
                        $stores_ids = $areas_range['stores_ids'];
                    }

                    if (!empty($department_ids) && !empty($stores_ids)) {
                        if (in_array('-2', $stores_ids)) {
                            $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id != '-1')";
                            $bind['department_ids'] = $department_ids;
                        } else {
                            $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id IN ({store_ids:array}))";
                            $bind['department_ids'] = $department_ids;
                            $bind['store_ids'] = $stores_ids;
                        }
                    } else {
                        if (!empty($department_ids)) {
                            $conditions .= " and node_department_id IN ({department_ids:array})";
                            $bind['department_ids'] = $department_ids;
                        }
                        if (!empty($stores_ids)) {
                            if (in_array('-2', $stores_ids)) {
                                $conditions .= " and sys_store_id != '-1')";
                            } else {
                                $conditions .= " and sys_store_id IN ({store_ids:array})";
                                $bind['store_ids'] = $stores_ids;
                            }
                        }
                    }
                }
            }

            $count = HrStaffInfoModel::count([
                $conditions,
                'bind' => $bind
            ]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_ids' => $staff_info_ids, 'user' => $user, 'staff_scope' => $staff_scope],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $count;
    }

    /**
     * 验证是否正确的权限范围
     * @param $params
     * @return array
     */
    public function validatePermission($params)
    {
        $staff_scope = $params['staff_scope'];
        $user = $params['user'];

        $result = ['permission' => 0, 'permission_msg' => 'error'];
        try {
            if ($staff_scope == BaseService::$staff_scope_department) {
                if (!$this->isDepartmentManager($user['id'])) {
                    $result['permission_msg'] = self::$t->_('talent_review_error_1');//非部门负责人不能查看我的部门信息
                } else {
                    $result['permission'] = 1;
                    $result['permission_msg'] = 'ok';
                }
            }

            if ($staff_scope == BaseService::$staff_scope_manage) {
                if (!$this->isRolePermission($user['id'], BaseService::$HRBP_ROLE_ID)) {
                    $result['permission_msg'] = self::$t->_('talent_review_error_2');//非HRBP角色不能查看我的管辖信息
                } else {
                    if (!$this->isConfigDataRange($user['id'])) {
                        $result['permission_msg'] = self::$t->_('talent_review_error_3');//该HRBP还没有配置管辖范围
                    } else {
                        $result['permission'] = 1;
                        $result['permission_msg'] = 'ok';
                    }
                }
            }

            if ($staff_scope == BaseService::$staff_scope_company) {
                if (!$this->isEnvAdminAuthorize($user['id'])) {
                    $result['permission_msg'] = self::$t->_('talent_review_error_4');//非管理员不能查看公司全员信息
                } else {
                    $result['permission'] = 1;
                    $result['permission_msg'] = 'ok';
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $result;
    }

    /**
     * 验证是否能查看指定工号的职级
     * @param $staff_info_ids
     * @param $user_id
     * @return array
     */
    public function validateStaffJobGradeShow($staff_info_ids, $user_id)
    {
        $where = $this->getStaffJobGradeDataRange($user_id);
        $list = [];
        if (empty($where)) {
            return $list;
        }
        try {
            $conditions = "staff_info_id in({staff_info_id:array}) and state IN (1,3) and formal IN (1,4) and is_sub_staff = 0 ";
            $bind = [
                'staff_info_id' => $staff_info_ids
            ];

            $conditions .= $where['conditions'];
            $bind = array_merge($bind, $where['bind']);

            $list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,sex,job_title_grade_v2,node_department_id,job_title,hire_type,state,hire_date,education,manger,average_tenure,sys_store_id',
                'conditions' => $conditions,
                'bind' => $bind
            ])->toArray();

            $list = !empty($list) ? array_column($list, null, 'staff_info_id') : [];
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $list;
    }

    /**
     * 获取职级查看范围
     * @param $staff_info_id
     * @return array
     */
    public function getStaffJobGradeDataRange($staff_info_id)
    {
        $where = [];
        try {
            $manage_data = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($staff_info_id,StaffDepartmentAreasStoreRepository::$TYPE_2);

            if (empty($manage_data)) {
                return $where;
            }
            $conditions = '';
            $bind = [];

            if (!empty($manage_data['department_ids']) && !empty($manage_data['stores_ids'])) {
                if (in_array('-2', $manage_data['stores_ids'])) {
                    $conditions = " and (node_department_id IN ({department_ids:array}) or sys_store_id != '-1')";
                    $bind['department_ids'] = $manage_data['department_ids'];
                } else {
                    $conditions = " and (node_department_id IN ({department_ids:array}) or sys_store_id IN ({store_ids:array}))";
                    $bind['department_ids'] = $manage_data['department_ids'];
                    $bind['store_ids'] = $manage_data['stores_ids'];
                }
            } else {
                if (!empty($manage_data['department_ids'])) {
                    $conditions = " and node_department_id IN ({department_ids:array})";
                    $bind['department_ids'] = $manage_data['department_ids'];
                }
                if (!empty($manage_data['stores_ids'])) {
                    if (in_array('-2', $manage_data['stores_ids'])) {
                        $conditions = " and sys_store_id != '-1')";
                    } else {
                        $conditions = " and sys_store_id IN ({store_ids:array})";
                        $bind['store_ids'] = $manage_data['stores_ids'];
                    }
                }
            }
            $where = ['conditions' => $conditions, 'bind' => $bind];
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $where;
    }

    /**
     * 查询是否在管辖范围内
     * @param $staff_info_ids
     * @param $user_id
     * @return array
     */
    public function getJobGradeDataRangeStaffs($staff_info_ids, $user_id) {
        $list = [];
        try {
            //判断是否部门负责人
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user_id);
            $role_permission = $this->isRolePermission($user_id, BaseService::$HRBP_ROLE_ID);
            if(empty($department_list) && !$role_permission) {
                return $list;
            }

            $conditions = " staff_info_id IN ({staff_info_ids:array}) and state IN (1,3) and formal IN (1,4) and is_sub_staff = 0 ";
            $bind = [
                'staff_info_ids' => $staff_info_ids,
            ];

            $department_ids = array_column($department_list, 'id');
            $stores_ids = [];

            //判断是否有管辖区域
            if ($role_permission) {
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user_id);
                $department_ids = !empty($areas_range['department_ids']) ? array_merge($department_ids,
                    $areas_range['department_ids']) : $department_ids;
                $stores_ids = $areas_range['stores_ids'];
            }

            if (!empty($department_ids) && !empty($stores_ids)) {
                if (in_array('-2', $stores_ids)) {
                    $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id != '-1')";
                    $bind['department_ids'] = $department_ids;
                } else {
                    $conditions .= " and (node_department_id IN ({department_ids:array}) or sys_store_id IN ({store_ids:array}))";
                    $bind['department_ids'] = $department_ids;
                    $bind['store_ids'] = $stores_ids;
                }
            } else {
                if (!empty($department_ids)) {
                    $conditions .= " and node_department_id IN ({department_ids:array})";
                    $bind['department_ids'] = $department_ids;
                }
                if (!empty($stores_ids)) {
                    if (in_array('-2', $stores_ids)) {
                        $conditions .= " and sys_store_id != '-1')";
                    } else {
                        $conditions .= " and sys_store_id IN ({store_ids:array})";
                        $bind['store_ids'] = $stores_ids;
                    }
                }
            }

            $list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id,name,sex,job_title_grade_v2,node_department_id,job_title,hire_type,state,hire_date,education,manger,average_tenure,sys_store_id',
                'conditions' => $conditions,
                'bind' => $bind
            ])->toArray();
            $list = !empty($list) ? array_column($list, null, 'staff_info_id') : [];
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $list;
    }
}