<?php

namespace App\Modules\TalentReview\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrProbationModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Models\backyard\JobTransferModel;
use App\Models\backyard\MessageWarningModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;
use App\Repository\StoreRepository;
use Exception;

/**
 * 团队分析
 * Class TeamAnalysisService
 * @package App\Modules\TalentReview\Services
 */
class TeamAnalysisService extends BaseService
{
    public static $department_id_validation = [
        'department_id' => 'IntGe:0|>>>:department_id error'
    ];

    public static $begin_end_date_validation = [
        'begin_date' => 'Required|Date|>>>:begin_date error',
        'end_date' => 'Required|Date|>>>:end_date error',
    ];

    /**
     * 员工基本统计 在职人数、待离职人数、停职人数、平均司龄、试用期人数
     * @param $params
     * @return array
     */
    public function getStaffInfoStatistics($params)
    {
        $department_id = $params['department_id'] ?? 0;
        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        $on_job_count = 0;
        $wait_leave_count = 0;
        $staff_suspend_count = 0;
        $average_tenure = 0;
        $probation_count = 0;

        try {
            $where = $this->getStaffDataRange($user['id'], $staff_scope, $department_id);

            $on_job_count = $this->getStaffOnJobCount($where); //在职人数
            $wait_leave_count = $this->getStaffWaitLeaveCount($where); //待离职人数
            $staff_suspend_count = $this->getStaffSuspendCount($where); //停职人数
            $average_tenure = $this->getStaffAverageTenure($where); //平均司龄
            $probation_count = $this->getStaffProbationCount($params); //试用期人数
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'on_job_count' => $on_job_count,
            'wait_leave_count' => $wait_leave_count,
            'suspend_count' => $staff_suspend_count,
            'average_tenure' => $average_tenure,
            'probation_count' => $probation_count
        ];
    }

    /**
     * 性别占比、雇佣类型、学历、司龄、职级
     * @param $params
     * @return array
     */
    public function getStaffChartList($params)
    {
        $department_id = $params['department_id'] ?? 0;
        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        $sex_list = []; //性别占比
        $hire_type_list = []; //雇佣类型占比
        $education_list = []; //学历占比
        $average_tenure_list = []; //司龄占比
        $job_title_grade_list = []; //职级
        try {
            $where = $this->getStaffDataRange($user['id'], $staff_scope, $department_id);

            $sex_list = $this->getSexCount($where);
            $hire_type_list = $this->getHireTypeCount($where);
            $education_list = $this->getEducationCount($where);
            $average_tenure_list = $this->getAverageTenureCount($params);
            $job_title_grade_list = $this->getStaffJobTitleGradeCount($where);
            if ($staff_scope == self::$staff_scope_company && !$this->isLookJobTitleGrade($user['id'])) {
                $job_title_grade_list = [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'sex_list' => $sex_list,
            'hire_type_list' => $hire_type_list,
            'education_list' => $education_list,
            'average_tenure_list' => $average_tenure_list,
            'job_title_grade_list' => $job_title_grade_list
        ];
    }

    /**
     * 指定时间区间内入职人数
     * @param $params
     * @return array
     */
    public function getEntryStaffCount($params)
    {
        $department_id = $params['department_id'] ?? 0;
        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        $count = 0;
        try {
            $where = $this->getStaffDataRange($user['id'], $staff_scope, $department_id);
            if (!empty($where)) {
                $conditions = "state IN({state:array}) and formal IN (1,4) AND is_sub_staff = 0 and working_country = :working_country:";
                $bind = ['state' => [1, 3], 'working_country' => $this->getWorkingCountryID()];
                $begin_date = $params['begin_date'] ?? '';
                $end_date = $params['end_date'] ?? '';
                if (!empty($begin_date) && !empty($end_date)) {
                    $conditions .= " and hire_date >= :begin_hire_date: and hire_date <= :end_hire_date:";
                    $begin_date = date('Y-m-d 00:00:00', strtotime($begin_date));
                    $end_date = date('Y-m-d 23:59:59', strtotime($end_date));
                    $bind = array_merge($bind, ['begin_hire_date' => $begin_date, 'end_hire_date' => $end_date]);
                }

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $count = HrStaffInfoModel::count([$conditions, 'bind' => $bind]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return ['entry_count' => $count];
    }

    /**
     * 指定时间区间内电子警告总数
     * @param $params
     * @return array
     */
    public function getStaffWarningCount($params)
    {
        $warning_count = 0;
        //$user = $params['user'];
        //$staff_scope = $params['staff_scope'];
        try {
            /*
            $begin_date = $params['begin_date'] ?? '';
            $end_date = $params['end_date'] ?? '';

            $builder = $this->modelsManager->createBuilder();
            $builder->columns('count(DISTINCT(w.staff_info_id)) as count');
            $builder->from(['w' => MessageWarningModel::class]);
            $builder->leftjoin(BiHrStaffInfoModel::class, 'w.staff_info_id = s.staff_info_id', 's');
            $builder->where('w.is_delete = 0 and s.state IN (1,3) and s.formal IN (1,4) and s.is_sub_staff = 0');

            if (!empty($begin_date) && !empty($end_date)) {
                $builder->andWhere('w.date_at >= :begin_date:', ['begin_date' => $begin_date]);
                $builder->andWhere('w.date_at <= :end_date:', ['end_date' => $end_date]);
            }

            //我的部门
            if ($staff_scope == self::$staff_scope_department) {
                $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                $department_ids = array_map('intval', array_column($department_list, 'id'));
                $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                    ['department_ids' => $department_ids]);
            }

            //我的管辖
            if ($staff_scope == self::$staff_scope_manage) {
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id != '-1')",
                            ['department_ids' => $department_ids]);
                    } else {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $store_ids = $areas_range['stores_ids'];
                        $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id IN ({store_ids:array}))",
                            ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                    }
                } else {
                    if (!empty($areas_range['department_ids'])) {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                            ['department_ids' => $department_ids]);
                    }
                    if (!empty($areas_range['stores_ids'])) {
                        if (in_array('-2', $areas_range['stores_ids'])) {
                            $builder->andWhere("s.sys_store_id != '-1'");
                        } else {
                            $store_ids = $areas_range['stores_ids'];
                            $builder->andWhere("s.sys_store_id IN ({store_ids:array})", ['store_ids' => $store_ids]);
                        }
                    }
                }
            }

            //公司全员
            if ($staff_scope == self::$staff_scope_company) {
                $department_id = $params['department_id'] ?? 0;
                if (!empty($department_id)) {
                    $department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
                    $department_ids = array_map('intval', array_column($department_list, 'id'));
                    $builder->andWhere("s.node_department_id IN ({department_ids:array})",
                        ['department_ids' => $department_ids]);
                }
            }

            */

            $builder = $this->getStaffWarningBuilder($params);
            $builder->columns('count(DISTINCT(w.staff_info_id)) as count');
            $warning = $builder->getQuery()->execute()->getFirst()->toArray();
            $warning_count = $warning['count'] ?? 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return ['warning_count' => $warning_count];
    }

    /**
     * 指定时间区间内电子警告总数列表
     * @param $params
     * @return array
     */
    public function getStaffWarningList($params)
    {
        $page_size = $params['pageSize'] ?? 20;
        $page_num = $params['pageNum'] ?? 1;
        $total_count = 0;
        $staff_items = [];

        try {
            $offset = $page_size * ($page_num - 1);

            $builder_list = $this->getStaffWarningBuilder($params);
            $builder_list->columns('DISTINCT(w.staff_info_id) as staff_info_id,s.name as staff_name,s.job_title,s.sys_store_id,s.node_department_id');

            $builder_count = $this->getStaffWarningBuilder($params);
            $totalCount = $builder_count->columns('count(DISTINCT(w.staff_info_id)) as count')->getQuery()->execute()->getFirst();

            $builder_list->limit($page_size, $offset);
            $list = $builder_list->getQuery()->execute()->toArray();

            $staff_items = $this->combinationStaffList($list);
            $total_count = !empty($totalCount) ? $totalCount->count : 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'items' => $staff_items,
            'pagination' => [
                'page_num' => $page_num,
                'page_size' => $page_size,
                'total_count' => $total_count,
            ]
        ];
    }

    /**
     * 警告人数 Builder
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getStaffWarningBuilder($params)
    {

        $user = $params['user'];
        $staff_scope = $params['staff_scope'];

        $begin_date = $params['begin_date'] ?? '';
        $end_date = $params['end_date'] ?? '';


        $builder = $this->modelsManager->createBuilder();
        //$builder->columns('count(DISTINCT(w.staff_info_id)) as count');
        $builder->from(['w' => MessageWarningModel::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'w.staff_info_id = s.staff_info_id', 's');
        $builder->where('w.is_delete = 0 and s.state IN (1,3) and s.formal IN (1,4) and s.is_sub_staff = 0');
        $builder->andWhere('s.working_country = :working_country:', ['working_country' => $this->getWorkingCountryID()]);
        if (!empty($begin_date) && !empty($end_date)) {
            $builder->andWhere('w.date_at >= :begin_date:', ['begin_date' => $begin_date]);
            $builder->andWhere('w.date_at <= :end_date:', ['end_date' => $end_date]);
        }

        //我的部门
        if ($staff_scope == self::$staff_scope_department) {
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
            $department_ids = array_map('intval', array_column($department_list, 'id'));
            $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                ['department_ids' => $department_ids]);
        }

        //我的管辖
        if ($staff_scope == self::$staff_scope_manage) {
            $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
            if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                if (in_array('-2', $areas_range['stores_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id != '-1')",
                        ['department_ids' => $department_ids]);
                } else {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $store_ids = $areas_range['stores_ids'];
                    $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id IN ({store_ids:array}))",
                        ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                }
            } else {
                if (!empty($areas_range['department_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                        ['department_ids' => $department_ids]);
                }
                if (!empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $builder->andWhere("s.sys_store_id != '-1'");
                    } else {
                        $store_ids = $areas_range['stores_ids'];
                        $builder->andWhere("s.sys_store_id IN ({store_ids:array})", ['store_ids' => $store_ids]);
                    }
                }
            }
        }

        //公司全员
        if ($staff_scope == self::$staff_scope_company) {
            $department_id = $params['department_id'] ?? 0;
            if (!empty($department_id)) {
                $department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
                $department_ids = array_map('intval', array_column($department_list, 'id'));
                $builder->andWhere("s.node_department_id IN ({department_ids:array})",
                    ['department_ids' => $department_ids]);
            }
        }

        return $builder;
    }

    /**
     * 指定时间区间内转岗人数
     * @param $params
     * @return array
     */
    public function getTransferStaffCount($params)
    {
        //$user = $params['user'];
        //$staff_scope = $params['staff_scope'];
        $count = 0;
        try {
            /*
            $begin_date = $params['begin_date'] ?? '';
            $end_date = $params['end_date'] ?? '';
            if ($staff_scope != self::$staff_scope_company) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('count(DISTINCT(j.staff_id)) as count');
                $builder->from(['j' => JobTransferModel::class]);
                $builder->where('j.state = 3');

                if (!empty($begin_date) && !empty($end_date)) {
                    $builder->andWhere('j.after_date >= :begin_date:', ['begin_date' => $begin_date]);
                    $builder->andWhere('j.after_date <= :end_date:', ['end_date' => $end_date]);
                }

                //转岗人数只有我的部门 我的管辖
                //我的部门
                if ($staff_scope == self::$staff_scope_department) {
                    $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                    $department_ids = array_map('intval', array_column($department_list, 'id'));
                    $builder->andWhere('j.after_department_id IN ({department_ids:array})',
                        ['department_ids' => $department_ids]);
                }

                //我的管辖
                if ($staff_scope == self::$staff_scope_manage) {
                    $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                    if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                        if (in_array('-2', $areas_range['stores_ids'])) {
                            $department_ids = array_map('intval', $areas_range['department_ids']);
                            $builder->andWhere("(j.after_department_id IN ({department_ids:array}) or j.after_store_id != '-1')",
                                ['department_ids' => $department_ids]);
                        } else {
                            $department_ids = array_map('intval', $areas_range['department_ids']);
                            $store_ids = $areas_range['stores_ids'];
                            $builder->andWhere("(j.after_department_id IN ({department_ids:array}) or j.after_store_id IN ({store_ids:array}))",
                                ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                        }
                    } else {
                        if (!empty($areas_range['department_ids'])) {
                            $department_ids = array_map('intval', $areas_range['department_ids']);
                            $builder->andWhere('j.after_department_id IN ({department_ids:array})',
                                ['department_ids' => $department_ids]);
                        }
                        if (!empty($areas_range['stores_ids'])) {
                            if (in_array('-2', $areas_range['stores_ids'])) {
                                $builder->andWhere("j.after_store_id != '-1'");
                            } else {
                                $store_ids = $areas_range['stores_ids'];
                                $builder->andWhere("j.after_store_id IN ({store_ids:array})",
                                    ['store_ids' => $store_ids]);
                            }
                        }
                    }
                }

                $transfer = $builder->getQuery()->execute()->getFirst()->toArray();
                $count = $transfer['count'] ?? 0;
            }
            */
            $builder = $this->getTransferStaffBuilder($params);
            $builder->columns('count(DISTINCT(j.staff_id)) as count');
            $transfer = $builder->getQuery()->execute()->getFirst()->toArray();
            $count = $transfer['count'] ?? 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return ['transfer_count' => $count];
    }

    /**
     * 指定区间转岗人列表
     * @param $params
     * @return array
     */
    public function getTransferStaffList($params)
    {
        $page_size = $params['pageSize'] ?? 20;
        $page_num = $params['pageNum'] ?? 1;
        $total_count = 0;
        $staff_items = [];
        try {
            $offset = $page_size * ($page_num - 1);

            $builder_list = $this->getTransferStaffBuilder($params);
            $builder_list->columns('DISTINCT(j.staff_id) as staff_info_id,s.name as staff_name,s.job_title,s.sys_store_id,s.node_department_id');

            $builder_count = $this->getTransferStaffBuilder($params);
            $totalCount = $builder_count->columns('count(DISTINCT(j.staff_id)) as count')->getQuery()->execute()->getFirst();

            $builder_list->limit($page_size, $offset);
            $list = $builder_list->getQuery()->execute()->toArray();

            $staff_items = $this->combinationStaffList($list);
            $total_count = !empty($totalCount) ? $totalCount->count : 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'items' => $staff_items,
            'pagination' => [
                'page_num' => $page_num,
                'page_size' => $page_size,
                'total_count' => $total_count,
            ]
        ];
    }

    /**
     * 转岗 Builder
     * @param $params
     * @return \Phalcon\Mvc\Model\Query\BuilderInterface
     */
    public function getTransferStaffBuilder($params)
    {

        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        $begin_date = $params['begin_date'] ?? '';
        $end_date = $params['end_date'] ?? '';

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => JobTransferModel::class]);
        $builder->leftJoin(HrStaffInfoModel::class, 's.staff_info_id = j.staff_id', 's');
        $builder->where('j.state = 3');
        $builder->andWhere('s.working_country = :working_country:', ['working_country' => $this->getWorkingCountryID()]);
        if (!empty($begin_date) && !empty($end_date)) {
            $builder->andWhere('j.after_date >= :begin_date:', ['begin_date' => $begin_date]);
            $builder->andWhere('j.after_date <= :end_date:', ['end_date' => $end_date]);
        }

        //转岗人数只有我的部门 我的管辖
        //我的部门
        if ($staff_scope == self::$staff_scope_department) {
            $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
            $department_ids = array_map('intval', array_column($department_list, 'id'));

            $builder->andWhere('j.after_department_id IN ({after_department_ids:array})',
                ['after_department_ids' => $department_ids]);

            $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                ['department_ids' => $department_ids]);
        }

        //我的管辖
        if ($staff_scope == self::$staff_scope_manage) {
            $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
            if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                if (in_array('-2', $areas_range['stores_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);

                    $builder->andWhere("(j.after_department_id IN ({after_department_ids:array}) or j.after_store_id != '-1')",
                        ['after_department_ids' => $department_ids]);

                    $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id != '-1')",
                        ['department_ids' => $department_ids]);
                } else {
                    $department_ids = array_map('intval', $areas_range['department_ids']);
                    $store_ids = $areas_range['stores_ids'];

                    $builder->andWhere("(j.after_department_id IN ({after_department_ids:array}) or j.after_store_id IN ({after_store_ids:array}))",
                        ['after_department_ids' => $department_ids, 'after_store_ids' => $store_ids]);

                    $builder->andWhere("(s.node_department_id IN ({department_ids:array}) or s.sys_store_id IN ({store_ids:array}))",
                        ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                }
            } else {
                if (!empty($areas_range['department_ids'])) {
                    $department_ids = array_map('intval', $areas_range['department_ids']);

                    $builder->andWhere('j.after_department_id IN ({after_department_ids:array})',
                        ['after_department_ids' => $department_ids]);

                    $builder->andWhere('s.node_department_id IN ({department_ids:array})',
                        ['department_ids' => $department_ids]);
                }
                if (!empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $builder->andWhere("j.after_store_id != '-1'");

                        $builder->andWhere("s.sys_store_id != '-1'");
                    } else {
                        $store_ids = $areas_range['stores_ids'];
                        $builder->andWhere("j.after_store_id IN ({after_store_ids:array})", ['after_store_ids' => $store_ids]);

                        $builder->andWhere("s.sys_store_id IN ({store_ids:array})", ['store_ids' => $store_ids]);
                    }
                }
            }
        }
        return $builder;
    }

    /**
     * 组合员工数据
     * @param $list
     * @return array
     */
    public function combinationStaffList($list)
    {
        $staff_info_List = [];
        try {
            $department_ids = array_column($list, 'node_department_id');
            $job_title_ids = array_column($list, 'job_title');
            $store_ids = array_column($list, 'sys_store_id');

            $department_ids = array_values(array_unique($department_ids));
            $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);

            $job_title_ids = array_values(array_unique($job_title_ids));
            $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

            $store_ids = array_values(array_unique($store_ids));
            $store_list = (new StoreRepository())->getStoreListByIds($store_ids);

            $staff_info_ids = array_column($list, 'staff_info_id');
            $staff_items = (new StaffService())->getStaffItems($staff_info_ids);

            foreach ($list as $key => $value) {
                $head_portrait = $staff_items[$value['staff_info_id']]['PROFILE_OBJECT_KEY'] ?? '';
                $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';

                if($value['sys_store_id'] == '-1') {
                    $store_name = 'Header Office';
                } else {
                    $store_name = $store_list[$value['sys_store_id']]['name'] ?? '';
                }
                $staff_info_List[] = [
                    'staff_info_id' => $value['staff_info_id'],
                    'name' => $value['staff_name'],
                    'head_portrait' => $head_portrait_url,
                    'node_department_id' => $value['node_department_id'],
                    'department_name' => $department_list[$value['node_department_id']]['name'] ?? '',
                    'job_title' => $value['job_title'],
                    'job_title_name' => $job_title_list[$value['job_title']]['job_name'] ?? '',
                    'sys_store_id' => $value['sys_store_id'],
                    'store_name' => $store_name
                ];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $staff_info_List;
    }

    /**
     * 在职人数
     * @param $where
     * @return mixed
     */
    public function getStaffOnJobCount($where = [])
    {
        $count = 0;
        try {
            if (!empty($where)) {
                $conditions = "state IN ({state:array}) and formal IN ({formal:array}) and is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $count = HrStaffInfoModel::count([$conditions, 'bind' => $bind]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }
        return $count;
    }

    /**
     * 待离职人数
     * @param $where
     * @return mixed
     */
    public function getStaffWaitLeaveCount($where = [])
    {
        $count = 0;
        try {
            if (!empty($where)) {
                $conditions = " state = :state: and wait_leave_state = :wait_leave_state: and formal IN ({formal:array}) and is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => 1,
                    'wait_leave_state' => 1,
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $count = HrStaffInfoModel::count([$conditions, 'bind' => $bind]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $count;
    }

    /**
     * 停职人数
     * @param $where
     * @return mixed
     */
    public function getStaffSuspendCount($where = [])
    {
        $count = 0;
        try {
            if (!empty($where)) {
                $conditions = " state = :state: and formal IN ({formal:array}) and is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => 3,
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $count = HrStaffInfoModel::count([$conditions, 'bind' => $bind]);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $count;
    }

    /**
     * 平均司龄
     * @param array $where
     * @return float
     */
    public function getStaffAverageTenure($where = [])
    {
        $average = 0;
        try {
            if (!empty($where)) {
                $conditions = "state IN ({state:array}) and formal IN ({formal:array}) and is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $average = HrStaffInfoModel::average([
                    'column' => 'average_tenure',
                    'conditions' => $conditions,
                    'bind' => $bind,
                ]);
                $average = (float)sprintf("%.1f", substr(sprintf("%.3f", $average), 0, -2));
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $average;
    }

    /**
     * 试用期人数
     * @param array $params
     * @return int
     */
    public function getStaffProbationCount($params)
    {
        $probation_count = 0;
        $user = $params['user'];
        $staff_scope = $params['staff_scope'];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('count(*) as count');
            $builder->from(['s' => HrStaffInfoModel::class]);
            $builder->leftJoin(HrProbationModel::class, 's.staff_info_id = p.staff_info_id', 'p');
            $builder->where('s.formal IN (1,4)');
            $builder->andWhere('s.state in (1, 3)');
            $builder->andWhere('s.is_sub_staff = 0');

            switch (get_country_code()) {
                case GlobalEnums::TH_COUNTRY_CODE:
                case GlobalEnums::LA_COUNTRY_CODE:
                case GlobalEnums::VN_COUNTRY_CODE:
                    $builder->andWhere("s.hire_date>='2020-06-13' and  ( s.hire_type = 1 or ( s.hire_type in (3,4) and s.hire_times >= 365 ) or (s.hire_type in (2) and s.hire_times >= 12) )");
                    break;
                case GlobalEnums::PH_COUNTRY_CODE:
                    $builder->andWhere("s.hire_type  in (1,2) or (s.hire_type in (3,4) and s.hire_times >= 365)");
                    break;
                case GlobalEnums::MY_COUNTRY_CODE:
                    $builder->andWhere("s.hire_type = 1");
                    break;
                default:
                    break;
            }
            $builder->andWhere('p.status = 1 or p.status is null');
            $builder->andWhere('s.working_country  = :working_country:', ['working_country' => $this->getWorkingCountryID()]);
            if ($staff_scope == self::$staff_scope_department) {
                $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                $department_ids = array_map('intval', array_column($department_list, 'id'));
                $builder->andWhere('s.node_department_id IN({department_ids:array})',
                    ['department_ids' => $department_ids]);
            }

            if ($staff_scope == self::$staff_scope_manage) {
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $builder->andWhere("(s.node_department_id IN({department_ids:array}) or s.sys_store_id != '-1')",
                            ['department_ids' => $department_ids]);
                    } else {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $store_ids = $areas_range['stores_ids'];
                        $builder->andWhere("(s.node_department_id IN({department_ids:array}) or s.sys_store_id in({store_ids:array}))",
                            ['department_ids' => $department_ids, 'store_ids' => $store_ids]);
                    }
                } else {
                    if (!empty($areas_range['department_ids'])) {
                        $department_ids = array_map('intval', $areas_range['department_ids']);
                        $builder->andWhere('s.node_department_id IN({department_ids:array})',
                            ['department_ids' => $department_ids]);
                    }
                    if (!empty($areas_range['stores_ids'])) {
                        if (in_array('-2', $areas_range['stores_ids'])) {
                            $builder->andWhere("s.sys_store_id != '-1'");
                        } else {
                            $store_ids = $areas_range['stores_ids'];
                            $builder->andWhere("s.sys_store_id in({store_ids:array})",
                                ['store_ids' => $store_ids]);
                        }
                    }
                }
            }

            if ($staff_scope == self::$staff_scope_company) {
                $department_id = $params['department_id'] ?? 0;
                if (!empty($department_id)) {
                    $department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
                    $department_ids = array_map('intval', array_column($department_list, 'id'));
                    $builder->andWhere("s.node_department_id IN({department_ids:array})",
                        ['department_ids' => $department_ids]);
                }
            }

            $probation = $builder->getQuery()->execute()->getFirst()->toArray();
            $probation_count = (int)$probation['count'] ?? 0;
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $probation_count;
    }

    /**
     * 性别分布
     * @param $where
     * @return array
     */
    public function getSexCount($where = [])
    {
        $sex_data = [];
        $list = [];
        try {
            if (!empty($where)) {
                $conditions = " state IN ({state:array}) AND formal IN ({formal:array}) AND is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $list = HrStaffInfoModel::find([
                    'columns' => 'sex,count(*) as count',
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'group' => 'sex',
                    'order' => 'sex ASC'
                ])->toArray();
                $list = !empty($list) ? array_column($list, 'count', 'sex') : [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        foreach (StaffInfoEnums::$staff_sex as $key => $value) {
            $sex_data[] = [
                'sex' => $key,
                'sex_text' => static::$t->_($value),
                'count' => intval($list[$key] ?? 0)
            ];
        }

        return $sex_data;
    }

    /**
     * 雇佣类型
     * @param $where
     * @return array
     */
    public function getHireTypeCount($where = [])
    {
        $hire_type_data = [];
        $list = [];
        try {
            if (!empty($where)) {
                $conditions = " state IN ({state:array}) AND formal IN ({formal:array}) AND is_sub_staff = :is_sub_staff: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $list = HrStaffInfoModel::find([
                    'columns' => 'hire_type,count(*) as count',
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'group' => 'hire_type',
                    'order' => 'hire_type ASC'
                ])->toArray();
                $list = !empty($list) ? array_column($list, 'count', 'hire_type') : [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        foreach (StaffInfoEnums::$hire_type as $key => $value) {
            $hire_type_data[] = [
                'hire_type' => $key,
                'hire_type_text' => static::$t->_($value),
                'count' => intval($list[$key] ?? 0)
            ];
        }

        return $hire_type_data;
    }

    /**
     * 学历分布
     * @param $where
     * @return array
     */
    public function getEducationCount($where = [])
    {
        $education_date = [];
        $list = [];
        try {
            if (!empty($where)) {
                $conditions = " state IN ({state:array}) AND formal IN ({formal:array}) AND is_sub_staff = :is_sub_staff: AND education >= :education: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'education' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);

                $list = HrStaffInfoModel::find([
                    'columns' => 'education,count(*) as count',
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'group' => 'education',
                    'order' => 'education ASC'
                ])->toArray();
                $list = !empty($list) ? array_column($list, 'count', 'education') : [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        foreach (StaffInfoEnums::$education as $key => $value) {
            $education_date[] = [
                'education' => $key,
                'education_text' => static::$t->_($value),
                'count' => intval($list[$key] ?? 0)
            ];
        }

        return $education_date;
    }

    /**
     * 司龄分布
     * @param $params
     * @return array
     */
    public function getAverageTenureCount($params)
    {
        $average_tenure_data = [];
        $staff_scope = $params['staff_scope'];
        $user = $params['user'];

        $db = $this->getDI()->get("db_backyard");

        $data_range_where = [];
        $conditions = '';
        $bind = [];
        try {
            //我的部门
            if ($staff_scope == self::$staff_scope_department) {
                $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($user['id']);
                $department_ids = array_column($department_list, 'id');

                $department_ids_str = implode(',', array_map('intval', $department_ids));
                $conditions = " and node_department_id IN ({$department_ids_str}) ";

                $data_range_where = ['conditions' => $conditions, 'bind' => $bind];
            }

            //我的管辖
            if ($staff_scope == self::$staff_scope_manage) {
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($user['id']);
                if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $department_ids_str = implode(',', array_map('intval', $areas_range['department_ids']));
                        $conditions = " and (node_department_id IN ({$department_ids_str}) or sys_store_id != '-1')";
                    } else {
                        $department_ids_str = implode(',', array_map('intval', $areas_range['department_ids']));
                        $store_ids_str = "'" . implode("','", $areas_range['stores_ids']) . "'";
                        $conditions = " and (node_department_id IN ({$department_ids_str}) or sys_store_id IN ({$store_ids_str}))";
                    }
                } else {
                    if (!empty($areas_range['department_ids'])) {
                        $department_ids_str = implode(',', $areas_range['department_ids']);
                        $conditions = " and node_department_id IN ({$department_ids_str})";
                    }
                    if (!empty($areas_range['stores_ids'])) {
                        if (in_array('-2', $areas_range['stores_ids'])) {
                            $conditions = " and sys_store_id != '-1'";
                        } else {
                            $store_ids_str = "'" . implode("','", $areas_range['stores_ids']) . "'";
                            $conditions = " and sys_store_id IN ({$store_ids_str})";
                        }
                    }
                }
                $data_range_where = ['conditions' => $conditions, 'bind' => $bind];
            }

            //公司全员
            if ($staff_scope == self::$staff_scope_company) {
                $department_id = $params['department_id'] ?? '';
                if (!empty($department_id)) {
                    $department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
                    $department_ids = array_column($department_list, 'id');
                    $department_ids_str = implode(',', array_map('intval', $department_ids));
                    $conditions = " and node_department_id IN ({$department_ids_str}) ";
                }
                $data_range_where = ['conditions' => $conditions, 'bind' => $bind];
            }

            if (empty($data_range_where)) {
                return $average_tenure_data;
            }

            //1(<0.5);2(0.5,1);3(1,2);4(2,3);5(>=3)
            $where_str = " state in (1,3) and formal IN (1,4) and is_sub_staff = :is_sub_staff and working_country = :working_country";
            $sql_bind = ['is_sub_staff' => 0, 'working_country' => $this->getWorkingCountryID()];

            $where_str .= $data_range_where['conditions'];
            $sql_bind = array_merge($sql_bind, $data_range_where['bind']);
            $sql = "SELECT 
                    (CASE 
                        WHEN average_tenure < 0.5 THEN '1'
                        WHEN average_tenure >= 0.5 and average_tenure < 1 THEN '2'
                        WHEN average_tenure >= 1 and average_tenure < 2  THEN '3'
                        WHEN average_tenure >= 2 and average_tenure < 3 THEN '4'
                        WHEN average_tenure >= 3 THEN '5'
                    END) as average_tenure_new,count(1) as count
                FROM hr_staff_info WHERE {$where_str}
                GROUP BY average_tenure_new 
                ORDER BY average_tenure_new ASC;";
            $list = $db->fetchAll($sql, \Phalcon\Db::FETCH_ASSOC, $sql_bind);

            $list = !empty($list) ? array_column($list, 'count', 'average_tenure_new') : [];
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        foreach (StaffInfoEnums::$average_tenure as $key => $value) {
            $average_tenure_data[] = [
                'average_tenure' => $key,
                'average_tenure_text' => $value,
                'count' => intval($list[$key] ?? 0)
            ];
        }

        return $average_tenure_data;
    }

    /**
     * 职级分布
     * @param $where
     * @return array
     */
    public function getStaffJobTitleGradeCount($where = [])
    {
        $grade_data = [];
        $list = [];
        try {
            if (!empty($where)) {
                $conditions = " state IN ({state:array}) AND formal IN ({formal:array}) AND is_sub_staff = :is_sub_staff: AND job_title_grade_v2 >= :job_title_grade_v2: and working_country = :working_country:";
                $bind = [
                    'state' => [1, 3],
                    'formal' => [1, 4],
                    'is_sub_staff' => 0,
                    'job_title_grade_v2' => 0,
                    'working_country' => $this->getWorkingCountryID()
                ];

                $conditions .= $where['conditions'];
                $bind = array_merge($bind, $where['bind']);
                $list = HrStaffInfoModel::find([
                    'columns' => 'job_title_grade_v2,count(*) as count',
                    'conditions' => $conditions,
                    'bind' => $bind,
                    'group' => 'job_title_grade_v2',
                    'order' => 'job_title_grade_v2 ASC'
                ])->toArray();
                $list = !empty($list) ? array_column($list, 'count', 'job_title_grade_v2') : [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $where,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        foreach (StaffInfoEnums::$config_job_title_grade_v2 as $key => $value) {
            $grade_data[] = [
                'job_title_grade' => $key,
                'job_title_grade_text' => $value,
                'count' => intval($list[$key] ?? 0)
            ];
        }

        return $grade_data;
    }

}