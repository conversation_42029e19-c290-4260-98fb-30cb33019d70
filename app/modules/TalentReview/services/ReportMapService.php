<?php

namespace App\Modules\TalentReview\Services;

use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\ValidationException;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\HrStaffRepository;
use Exception;

class ReportMapService extends BaseService
{
    public static $default_subordinate_validation = [
        'department_id' => 'IntGe:0|>>>:department params error'
    ];

    public static $staff_info_id_validation = [
        'staff_info_id' => 'Required|IntGe:0|>>>:staff_info_id params error'
    ];

    /**
     * 默认下级列表
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function defaultSubordinateList($params)
    {
        $list = [];
        $staff_info_ids = [];
        $staff_scope = $params['staff_scope'] ?? 0;
        $department_id = $params['department_id'] ?? 0;
        $user = $params['user'];
        $department_repository = new DepartmentRepository();
        if ($staff_scope == BaseService::$staff_scope_company) {
            if (!empty($department_id)) {
                $department_detail = $department_repository->getDepartmentDetail($department_id);
                if (empty($department_detail)) {
                    throw new ValidationException(static::$t->_('talent_review_error_5'));//搜索部门不正确
                }
                if (empty($department_detail['manager_id'])) {
                    throw new ValidationException($department_detail['name'] . static::$t->_('talent_review_error_6'));//部门还没有配置部门负责人
                }
                $staff_info_ids[] = $department_detail['manager_id'];
            } else {
                $staff_info_ids = (new PermissionsService())->getReportMapBossId();
                if (empty($staff_info_ids)) {
                    throw new ValidationException(static::$t->_('talent_review_error_7'));//还没有配置Boss ID,请联系产品进行配置
                }
            }
        } else {
            $staff_info_ids[] = $user['id'];
        }

        if (!empty($staff_info_ids)) {
            try {
                $hr_staff_repository = new HrStaffRepository();
                $staff_service = new StaffService();
                $staff_list = $hr_staff_repository->getStaffListByStaffIds($staff_info_ids);

                $staff_items = $staff_service->getStaffItems($staff_info_ids);

                $job_title_ids = array_column($staff_list, 'job_title');
                $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

                $department_ids = array_column($staff_list, 'node_department_id');
                $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);

                $is_look_job_title_grade = $this->isLookJobTitleGrade($user['id']);

                $staff_job_grade_show = [];
                $permission_service = new PermissionsService();
                if(!$is_look_job_title_grade) {
                    $staff_job_grade_show = $permission_service->validateStaffJobGradeShow($staff_info_ids, $user['id']);
                }

                $staff_job_grade_data_ranges = $permission_service->getJobGradeDataRangeStaffs($staff_info_ids, $user['id']);

                foreach ($staff_list as $key => $value) {
                    $job_title_grade = '';
                    $job_title_grade_text = '';
                    /*
                    if ($staff_scope == self::$staff_scope_department) {
                        //判断是否为负责部门内人员
                        $job_title_grade = $value['job_title_grade_v2'];
                        $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                    }

                    if($staff_scope == self::$staff_scope_company) {
                        if($is_look_job_title_grade == true) {
                            $job_title_grade = $value['job_title_grade_v2'];
                            $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                        } else {
                            if(!isset($staff_job_grade_data_ranges[$value['staff_info_id']])) { //如果不是部门管辖范围内
                                if(isset($staff_job_grade_show[$value['staff_info_id']])) {
                                    $job_title_grade = $value['job_title_grade_v2'];
                                    $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                                }
                            } else {
                                $job_title_grade = $value['job_title_grade_v2'];
                                $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                            }
                        }
                    }
                    */

                    if($is_look_job_title_grade == true) {
                        $job_title_grade = $value['job_title_grade_v2'];
                        $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                    } else {
                        if(!isset($staff_job_grade_data_ranges[$value['staff_info_id']])) { //如果不是部门管辖范围内
                            if(isset($staff_job_grade_show[$value['staff_info_id']])) {
                                $job_title_grade = $value['job_title_grade_v2'];
                                $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                            }
                        } else {
                            $job_title_grade = $value['job_title_grade_v2'];
                            $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                        }
                    }

                    $subordinate_list = $this->getSubordinateStaffList([
                        'staff_info_id' => $value['staff_info_id'],
                        'user' => $user,
                        'staff_scope' => $staff_scope
                    ]);

                    $head_portrait = $staff_items[$value['staff_info_id']]['PROFILE_OBJECT_KEY'] ?? '';
                    $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';

                    $list[] = [
                        'staff_info_id' => $value['staff_info_id'],
                        'name' => $value['name'],
                        'head_portrait' => $head_portrait_url,
                        'job_title_id' => $value['job_title'],
                        'job_title_name' => $job_title_list[$value['job_title']]['job_name'] ?? '',
                        'job_title_grade' => $job_title_grade,
                        'job_title_grade_text' => $job_title_grade_text,
                        'node_department_id' => $value['node_department_id'],
                        'department_name' => $department_list[$value['node_department_id']]['name'] ?? '',
                        'superior_id' => $value['manger'],
                        'subordinate_count' => count($subordinate_list),
                        'state' => $value['state'],
                        'child_node' => $subordinate_list,
                    ];
                }
            } catch (Exception $e) {
                $this->getDI()->get('logger')->error([
                    'params' => $params,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }
        return $list;
    }

    /**
     * 汇报地图 下级列表
     * @param $params
     * @return array
     */
    public function getSubordinateStaffList($params)
    {
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $staff_scope = $params['staff_scope'];
        $user = $params['user'];
        $list = [];
        if (!empty($staff_info_id)) {
            try {
                $hr_staff_repository = new HrStaffRepository();
                $subordinate_staff_list = $hr_staff_repository->getSubordinateStaffList([$staff_info_id]);
                if (!empty($subordinate_staff_list)) {
                    $subordinate_staff_ids = array_column($subordinate_staff_list, 'staff_info_id');

                    $staff_items = (new StaffService())->getStaffItems($subordinate_staff_ids);
                    $subordinate_count = $this->getStaffSubordinateCount($subordinate_staff_ids);//下级总数

                    $job_title_ids = array_column($subordinate_staff_list, 'job_title');
                    $job_title_list = (new HrJobTitleRepository())->getJobTitleByIds($job_title_ids);

                    $department_ids = array_column($subordinate_staff_list, 'node_department_id');
                    $department_list = (new DepartmentRepository())->getDepartmentByIds($department_ids);
                    $is_look_job_title_grade = $this->isLookJobTitleGrade($user['id']);

                    $permission_service = new PermissionsService();
                    $staff_job_grade_show = [];
                    $staff_job_grade_data_ranges = [];
                    if(!$is_look_job_title_grade) {
                        $staff_job_grade_show = $permission_service->validateStaffJobGradeShow($subordinate_staff_ids, $user['id']); //职级查看范围
                        $staff_job_grade_data_ranges = $permission_service->getJobGradeDataRangeStaffs($subordinate_staff_ids, $user['id']); //部门管辖范围
                    }

                    foreach ($subordinate_staff_list as $key => $value) {
                        if($staff_info_id == $value['staff_info_id']) {
                            continue;
                        }
                        $job_title_grade = '';
                        $job_title_grade_text = '';
                        /*
                        if ($staff_scope == self::$staff_scope_department) {
                            //判断是否为负责部门内人员
                            $job_title_grade = $value['job_title_grade_v2'];
                            $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                        }


                        if($staff_scope == self::$staff_scope_company) {
                            if($is_look_job_title_grade) {
                                $job_title_grade = $value['job_title_grade_v2'];
                                $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                            } else {
                                if(!isset($staff_job_grade_data_ranges[$value['staff_info_id']])) { //如果不是部门管辖范围内
                                    if(isset($staff_job_grade_show[$value['staff_info_id']])) { //是是否配置的职级查看范围
                                        $job_title_grade = $value['job_title_grade_v2'];
                                        $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                                    } else {
                                        $job_title_grade = '';
                                        $job_title_grade_text = '';
                                    }
                                } else {
                                    $job_title_grade = $value['job_title_grade_v2'];
                                    $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                                }
                            }
                        }
                        */

                        if($is_look_job_title_grade) {
                            $job_title_grade = $value['job_title_grade_v2'];
                            $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                        } else {
                            if(!isset($staff_job_grade_data_ranges[$value['staff_info_id']])) { //如果不是部门管辖范围内
                                if(isset($staff_job_grade_show[$value['staff_info_id']])) { //是是否配置的职级查看范围
                                    $job_title_grade = $value['job_title_grade_v2'];
                                    $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                                } else {
                                    $job_title_grade = '';
                                    $job_title_grade_text = '';
                                }
                            } else {
                                $job_title_grade = $value['job_title_grade_v2'];
                                $job_title_grade_text = StaffInfoEnums::$config_job_title_grade_v2[$value['job_title_grade_v2']] ?? '';
                            }
                        }

                        $head_portrait = $staff_items[$value['staff_info_id']]['PROFILE_OBJECT_KEY'] ?? '';
                        $head_portrait_url = !empty($head_portrait) ? gen_file_url(['object_key' => $head_portrait]) : '';

                        $list[] = [
                            'staff_info_id' => $value['staff_info_id'],
                            'name' => $value['name'],
                            'head_portrait' => $head_portrait_url,
                            'job_title_id' => $value['job_title'],
                            'job_title_name' => $job_title_list[$value['job_title']]['job_name'] ?? '',
                            'job_title_grade' => $job_title_grade,
                            'job_title_grade_text' => $job_title_grade_text,
                            'node_department_id' => $value['node_department_id'],
                            'department_name' => $department_list[$value['node_department_id']]['name'] ?? '',
                            'superior_id' => $value['manger'],
                            'subordinate_count' => $subordinate_count[$value['staff_info_id']] ?? '0',
                            'state' => $value['state'],
                            'child_node' => [],
                        ];
                    }
                }
            } catch (Exception $e) {
                $this->getDI()->get('logger')->error([
                    'params' => $params,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            }
        }

        return $list;
    }

    /**
     * 汇报地图 人员详情
     * @param $params
     * @return array
     */
    public function getStaffInfo($params)
    {
        $staff_scope = $params['staff_scope'] ?? 0;//来源
        $staff_info_id = $params['staff_info_id'] ?? 0;//工号
        $user = $params['user'];//登陆人

        $result_data = ['is_permission' => '1', 'message' => 'success', 'staff_info' => []];
        try {
            //判断工号是否在我的部门数据查看范围
            $staff_count = (new PermissionsService())->validationStaffDataRange([$staff_info_id], $staff_scope, $user);
            if($staff_count == 0) {
                $result_data['is_permission'] = '0';
                $result_data['message'] = static::$t->_('talent_review_error_8'); //没有权限查看该员工信息
            }

            if ($result_data['is_permission'] == '1') {
                $staff_service = new StaffService();
                $staff_info = $staff_service->getStaffInfo($staff_info_id);
                if (!empty($staff_info)) {
                    if($staff_info['working_country'] != $this->getWorkingCountryID()) {
                        $result_data['is_permission'] = '0';
                        $result_data['message'] = static::$t->_('talent_review_error_9'); //没有权限查看该员工信息
                    }

                    $is_look_job_title_grade = $this->isLookJobTitleGrade($user['id']);
                    //判断是否有职级查看权限 如果来源是公司全员 验证是否有配置职级查看权限
                    if($staff_scope == self::$staff_scope_company) {
                        if(!$is_look_job_title_grade) {
                            $permission_service = new PermissionsService();
                            $staff_job_grade_data_ranges = $permission_service->getJobGradeDataRangeStaffs([$staff_info_id], $user['id']); //部门查看范围
                            if(!isset($staff_job_grade_data_ranges[$staff_info_id])) {
                                $staff_job_grade_show = $permission_service->validateStaffJobGradeShow([$staff_info_id], $user['id']); //是否职级查看范围
                                if(!isset($staff_job_grade_show[$staff_info_id])) {
                                   $staff_info['job_title_grade'] = '';
                                   $staff_info['job_title_grade_text'] = '';
                               }
                            }
                        }
                    }
                    //教育经历
                    $staff_info['education_list'] = $staff_service->getStaffEducationList($staff_info_id);
                    //过往工作经历
                    $staff_info['work_experience'] = $staff_service->getStaffWorkExperienceList($staff_info_id);
                }
                $result_data['staff_info'] = $staff_info;
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $result_data;
    }

    /**
     * 下级数量列表
     * @param $staff_info_ids
     * @return array
     */
    private function getStaffSubordinateCount($staff_info_ids)
    {
        $subordinate_count = [];
        try {
            $subordinate_count = (new HrStaffRepository())->getSubordinateCount($staff_info_ids);
            $subordinate_count = array_column($subordinate_count, 'count', 'superior_id');
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_ids' => $staff_info_ids],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $subordinate_count;
    }
}