<?php

namespace App\Modules\TalentReview\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Common\Models\EnvModel;
use App\Repository\DepartmentRepository;
use App\Repository\StaffDepartmentAreasStoreRepository;
use Exception;

class BaseService extends \App\Library\BaseService
{
    public static $staff_scope_department = 1;//我的部门
    public static $staff_scope_manage = 2;//我的管辖HRBP
    public static $staff_scope_company = 3;//公司全员

    public static $HRBP_ROLE_ID = 68;//HRBP角色id

    /*********************************************************/
    //人才盘点 755
    //人才盘点 - 部门成员 756
    //人才盘点 - 部门成员 - 我的部门 769
    //人才盘点 - 部门成员 - 我的部门 - action 772,773,774,785,786
    /*--------------------------------------------------------*/
    //人才盘点 - 部门成员 - 我的管辖 770
    //人才盘点 - 部门成员 - 我的管辖 - action 775,776,777,783,784
    /*--------------------------------------------------------*/
    //人才盘点 - 汇报地图 - 公司全员 771
    //人才盘点 - 汇报地图 - 公司全员 - action 778,779,780,781,782
    /*********************************************************/
    //人才盘点 - 部门分析 757
    //人才盘点 - 部门分析 - 我的部门 763
    //人才盘点 - 部门分析 - 我的部门 - action 766
    /*--------------------------------------------------------*/
    //人才盘点 - 部门分析 - 我的管辖 764
    //人才盘点 - 部门分析 - 我的管辖 - action 767
    /*--------------------------------------------------------*/
    //人才盘点 - 汇报地图 - 公司全员 765
    //人才盘点 - 汇报地图 - 公司全员 - action 768
    /*********************************************************/
    //人才盘点 - 汇报地图 758
    //人才盘点 - 汇报地图 - 我的部门 759
    //人才盘点 - 汇报地图 - 我的部门 - action 761
    /*--------------------------------------------------------*/
    //人才盘点 - 汇报地图 - 公司全员 760
    //人才盘点 - 汇报地图 - 公司全员 - action 762
    /*********************************************************/

    //人才盘点 755
    public static $talent_review_permission_id = 755;
    //人才盘点 - 部门成员 756
    public static $talent_review_department_staff = 756;
    //人才盘点 - 部门成员 - 我的部门 769
    public static $talent_review_department_staff_scope_1 = 769;
    //人才盘点 - 部门成员 - 我的部门 - action 772,773,774,785,786
    public static $talent_review_department_staff_scope_1_action = [772, 773, 774, 785, 786];
    //人才盘点 - 部门成员 - 我的管辖 770
    public static $talent_review_department_staff_scope_2 = 770;
    //人才盘点 - 部门成员 - 我的管辖 - action 775,776,777,783,784
    public static $talent_review_department_staff_scope_2_action = [775, 776, 777, 783, 784];
    //人才盘点 - 汇报地图 - 公司全员 771
    public static $talent_review_department_staff_scope_3 = 771;
    //人才盘点 - 汇报地图 - 公司全员 - action 778,779,780,781,782
    public static $talent_review_department_staff_scope_3_action = [778, 779, 780, 781, 782];
    //人才盘点 - 部门分析 757
    public static $talent_review_team_analysis = 757;
    //人才盘点 - 部门分析 - 我的部门 763
    public static $talent_review_team_analysis_staff_scope_1 = 763;
    //人才盘点 - 部门分析 - 我的部门 - action 766
    public static $talent_review_team_analysis_staff_scope_1_action = [766];
    //人才盘点 - 部门分析 - 我的管辖 764
    public static $talent_review_team_analysis_staff_scope_2 = 764;
    //人才盘点 - 部门分析 - 我的管辖 - action 767
    public static $talent_review_team_analysis_staff_scope_2_action = [767];
    //人才盘点 - 汇报地图 - 公司全员 765
    public static $talent_review_team_analysis_staff_scope_3 = 765;
    //人才盘点 - 汇报地图 - 公司全员 - action 768
    public static $talent_review_team_analysis_staff_scope_3_action = [768];

    //人才盘点 - 汇报地图 758
    public static $talent_review_report_map = 758;
    //人才盘点 - 汇报地图 - 我的部门 759
    public static $talent_review_report_map_staff_scope_1 = 759;
    //人才盘点 - 汇报地图 - 我的部门 - action 761
    public static $talent_review_report_map_staff_scope_1_action = [761];
    //人才盘点 - 汇报地图 - 公司全员 760
    public static $talent_review_report_map_staff_scope_3 = 760;
    //人才盘点 - 汇报地图 - 公司全员 - action 762
    public static $talent_review_report_map_staff_scope_3_action = [762];

    /**
     * 部门tree
     * @param $data
     * @param int $parentId
     * @param int $node_level
     * @return array
     */
    public function _treeNode($data, $parentId = 0, $node_level = 0)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $node_level++;
        $tree_node = [];
        if($node_level > 100) {
            return $tree_node;
        }
        foreach ($data as $key => $value) {
            if ($parentId == $value['ancestry']) {
                $item = [
                    'id' => intval($value['id']),
                    'label' => $value['name'],
                    'ancestry' => intval($value['ancestry']),
                    'type' => intval($value['type']),
                    'level' => intval($value['level']),
                    'children' => $this->_treeNode($data, $value['id'], $node_level),
                ];
                $tree_node[] = $item;
            }
        }
        return $tree_node;
    }

    /**
     * 转换为key value 格式
     * @param $arr
     * @param bool $is_translate
     * @return array
     */
    public function kvWrap($arr, $is_translate = false)
    {
        foreach ($arr as $key => $val) {
            $data[] = [
                'key' => (string)$key,
                'value' => $is_translate === true ? self::$t->_($val) : $val,
            ];
        }
        return $data ?? [];
    }

    /**
     * 校验是否为日期格式
     * @param $date
     * @return bool|false|int|string
     */
    public function checkDate($date)
    {
        if (empty($date)) {
            return false;
        }

        $tmp = strtotime($date);
        if ($tmp === false) {
            return $tmp;
        }
        return date("Y-m-d", $tmp);
    }

    /**
     * 获取数据范围权限
     * @param $staff_info_id
     * @param $staff_scope
     * @param $department_id
     * @return array
     */
    public function getStaffDataRange($staff_info_id, $staff_scope, $department_id = 0)
    {
        $conditions = '';
        $bind = [];
        $where = [];
        try {
            //我的部门
            if ($staff_scope == self::$staff_scope_department) {
                $department_list = (new DepartmentRepository())->getDepartmentAllListByManagerId($staff_info_id);
                $department_ids = array_column($department_list, 'id');

                $conditions = " and node_department_id IN ({department_ids:array}) ";
                $bind['department_ids'] = $department_ids;

                $where = ['conditions' => $conditions, 'bind' => $bind];
            }

            //我的管辖
            if ($staff_scope == self::$staff_scope_manage) {
                $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($staff_info_id);
                if (!empty($areas_range['department_ids']) && !empty($areas_range['stores_ids'])) {
                    if (in_array('-2', $areas_range['stores_ids'])) {
                        $conditions = " and (node_department_id IN ({department_ids:array}) or sys_store_id != '-1')";
                        $bind['department_ids'] = $areas_range['department_ids'];
                    } else {
                        $conditions = " and (node_department_id IN ({department_ids:array}) or sys_store_id IN ({store_ids:array}))";
                        $bind['department_ids'] = $areas_range['department_ids'];
                        $bind['store_ids'] = $areas_range['stores_ids'];
                    }
                } else {
                    if (!empty($areas_range['department_ids'])) {
                        $conditions = " and node_department_id IN ({department_ids:array})";
                        $bind['department_ids'] = $areas_range['department_ids'];
                    }
                    if (!empty($areas_range['stores_ids'])) {
                        if (in_array('-2', $areas_range['stores_ids'])) {
                            $conditions = " and sys_store_id != '-1'";
                        } else {
                            $conditions = " and sys_store_id IN ({store_ids:array})";
                            $bind['store_ids'] = $areas_range['stores_ids'];
                        }
                    }
                }
                $where = ['conditions' => $conditions, 'bind' => $bind];
            }

            //公司全员
            if ($staff_scope == self::$staff_scope_company) {
                if (!empty($department_id)) {
                    $department_list = (new DepartmentRepository())->getDepartmentSubListByIds($department_id);
                    $department_ids = array_column($department_list, 'id');
                    $conditions = " and node_department_id IN ({department_ids:array}) ";
                    $bind['department_ids'] = $department_ids;
                }
                $where = ['conditions' => $conditions, 'bind' => $bind];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id, 'staff_scope' => $staff_scope, 'department_id' => $department_id],
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return $where;
    }

    /**
     * 验证工号是否有职级查看权限 公司全员
     * @param $staff_info_id
     * @return bool
     */
    public function isLookJobTitleGrade($staff_info_id)
    {
        $staff_info_ids = [];
        try {
            $ids = EnvModel::getEnvByCode('talent_review_report_look_job_title_grade');
            if (!empty($ids)) {
                $staff_info_ids = array_map("intval", explode(',', $ids));
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $staff_info_id, 'message' => $e->getMessage(), 'file' => $e->getLine(), 'line' => $e->getLine()]);
        }
        return in_array($staff_info_id, $staff_info_ids);
    }

    /**
     * 获取国家码key
     * @return int
     */
    public function getWorkingCountryID() {
        $country_code = get_country_code();
        $country_id = 1;
        switch ($country_code) {
            case GlobalEnums::TH_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_TH;
                break;
            case GlobalEnums::PH_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_PH;
                break;
            case GlobalEnums::MY_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_MY;
                break;
            case GlobalEnums::LA_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_LA;
                break;
            case GlobalEnums::VN_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_VN;
                break;
            case GlobalEnums::ID_COUNTRY_CODE:
                $country_id = StaffInfoEnums::WORKING_COUNTRY_ID;
                break;
        }
        return $country_id;
    }
}
