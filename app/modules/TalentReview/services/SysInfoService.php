<?php

namespace App\Modules\TalentReview\Services;

use App\Library\Enums\StaffInfoEnums;
use App\Repository\DepartmentRepository;
use App\Repository\HrJobTitleRepository;
use App\Repository\StoreRepository;
use Exception;

class SysInfoService extends BaseService
{
    /**
     * 静态资源
     * @param $params
     * @return array
     */
    public function getStaticInfo($params)
    {
        $result = [
            'sex' => [],
            'average_tenure' => [],
            'job_title_grade' => [],
            'education' => [],
            'hire_type' => [],
            'staff_state' => [],
            'probation_status' => [],
        ];
        try {
            $sex = $this->kvWrap(StaffInfoEnums::$staff_sex, true);//性别
            $average_tenure = $this->kvWrap(StaffInfoEnums::$average_tenure);//司龄
            $job_title_grade = $this->kvWrap(StaffInfoEnums::$config_job_title_grade_v2, true);//职级
            $education = $this->kvWrap(StaffInfoEnums::$education, true);//学历
            $hire_type = $this->kvWrap(StaffInfoEnums::$hire_type, true);//雇佣类型

            $state = StaffInfoEnums::$staff_state;
            unset($state[StaffInfoEnums::STAFF_STATE_LEAVE]);
            $staff_state = $this->kvWrap($state, true);//在职状态
            $probation_status = $this->kvWrap(StaffInfoEnums::$search_probation_status, true);//试用期状态

            $result = [
                'sex' => $sex,
                'average_tenure' => $average_tenure,
                'job_title_grade' => $job_title_grade,
                'education' => $education,
                'hire_type' => $hire_type,
                'staff_state' => $staff_state,
                'probation_status' => $probation_status,
            ];

        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'message' => $e->getMessage(), 'file' => $e->getLine(), 'line' => $e->getLine()]);
        }
        return $result;
    }

    /**
     * 职位搜索
     * @param $params
     * @return array
     */
    public function searchJobTitle($params) {
        $list = [];
        try {
            $search_name = $params['search_name'] ?? '';
            if(!empty($search_name)) {
                $list = (new HrJobTitleRepository())->searchJobTitle($search_name);
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'message' => $e->getMessage(), 'file' => $e->getLine(), 'line' => $e->getLine()]);
        }
        return $list;
    }

    /**
     * 部门列表-tree
     * @return array
     */
    public function getDepartmentTreeList() {
        $tree_list = [];
        try {
            $department_list = (new DepartmentRepository())->getDepartmentList();
            $tree_list = $this->_treeNode($department_list, 0, 0);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['message' => $e->getMessage(), 'file' => $e->getLine(), 'line' => $e->getLine()]);
        }
        return $tree_list;
    }

    /**
     * 网点搜索
     * @param $params
     * @return array
     */
    public function searchStore($params) {
        $list = [];
        try {
            $search_name = $params['search_name'] ?? '';
            if(!empty($search_name)) {
                $list = (new StoreRepository())->searchStoreList($search_name);
                if (strpos('header office',strtolower($search_name)) !== false) {
                    array_unshift($list, ['id'=>'-1','name'=>'Header Office']);
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'message' => $e->getMessage(), 'file' => $e->getLine(), 'line' => $e->getLine()]);
        }
        return $list;
    }
}