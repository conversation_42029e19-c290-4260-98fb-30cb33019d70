<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\TalentReview\Services\ReportMapService;
use Exception;

/**
 * 汇报地图
 * Class ReportMapController
 * @package App\Modules\TalentReview\Controllers
 */
class ReportMapController extends BaseController
{
    /**
     * 默认列表
     * @Permission(action='talent_review.report_map.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function defaultSubordinateListAction() {
        try {
            $params = $this->request_params;
            $staff_scope_validation = [
                'staff_scope' => 'Required|IntIn:1,3|>>>:staff_scope params error'
            ];
            Validation::validate($params, array_merge(ReportMapService::$default_subordinate_validation,$staff_scope_validation));

            $result = (new ReportMapService())->defaultSubordinateList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 下级列表
     * @Permission(action='talent_review.report_map.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function subordinateListAction() {
        try {
            $params = $this->request_params;
            $staff_scope_validation = [
                'staff_scope' => 'Required|IntIn:1,3|>>>:staff_scope params error'
            ];
            Validation::validate($params, array_merge(ReportMapService::$staff_info_id_validation,$staff_scope_validation));

            $report_map_service = new ReportMapService();
            $result = $report_map_service->getSubordinateStaffList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 人员信息
     * @Permission(action='talent_review.report_map.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffInfoAction() {
        try {
            $params = $this->request_params;
            $staff_scope_validation = [
                'staff_scope' => 'Required|IntIn:1,3|>>>:staff_scope params error'
            ];
            Validation::validate($params, array_merge(ReportMapService::$staff_info_id_validation,$staff_scope_validation));

            $result = (new ReportMapService())->getStaffInfo($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }
}