<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\BaseController as Controller;
use App\Library\Validation\ValidationException;
use App\Modules\TalentReview\Services\BaseService;
use App\Modules\TalentReview\Services\PermissionsService;
use Phalcon\Mvc\Dispatcher;

/**
 * @name BaseController
 * @desc 所有web模块控制器都继承自该控制器
 */
abstract class BaseController extends Controller
{
    public $request_params;

    //模块自己控制
    public function onConstruct()
    {
    }

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);

        $params = $this->request->get();
        $params = array_filter($params, function ($v) {
            if(is_array($v)) {
                if(count($v) == 0) {
                    return false;
                }
            } else {
                $v = trim($v);
                if ($v == '' || $v == null) {
                    return false;
                }
            }

            return true;
        });
        $this->request_params = $params;
        $this->request_params['user'] = $this->user;
        if (!in_array($this->dispatcher->getControllerName(), ['sys_info', 'permissions'])) {
            $staff_scope = $this->request_params['staff_scope'];
            if (!in_array($staff_scope, [BaseService::$staff_scope_department, BaseService::$staff_scope_manage, BaseService::$staff_scope_company])) {
                throw new ValidationException('staff_scope Params error');
            }

            $permission_service = new PermissionsService();
            //判断是否为部门负责人
            if ($staff_scope == BaseService::$staff_scope_department) {
                $is_department_manager = $permission_service->isDepartmentManager($this->user['id']);
                if (!$is_department_manager) {
                    throw new ValidationException($this->t->_('talent_review_error_1'));//非部门负责人不能查看我的部门信息
                }
            }

            //判断是否为HRBP
            if ($staff_scope == BaseService::$staff_scope_manage) {
                $is_role_permission = $permission_service->isRolePermission($this->user['id'],
                    BaseService::$HRBP_ROLE_ID);
                if (!$is_role_permission) {
                    throw new ValidationException($this->t->_('talent_review_error_2'));//非HRBP角色不能查看我的管辖信息
                }
                //判断是否有管辖数据，如果没有管辖数据 提示还未设置管辖数据，请先设置管辖数据
                $is_config_data_range = $permission_service->isConfigDataRange($this->user['id']);
                if (!$is_config_data_range) {
                    throw new ValidationException($this->t->_('talent_review_error_3'));//该HRBP还没有配置管辖范围
                }
            }

            //判断是否为配置管理员
            if ($staff_scope == BaseService::$staff_scope_company) {
                $is_admin = (new PermissionsService())->isEnvAdminAuthorize($this->user['id']);
                if (!$is_admin) {
                    throw new ValidationException($this->t->_('talent_review_error_4'));//非管理员不能查看公司全员信息
                }
            }
        }

    }
}
