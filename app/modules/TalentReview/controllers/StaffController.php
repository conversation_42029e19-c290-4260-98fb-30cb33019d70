<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\TalentReview\Services\StaffService;
use Exception;

/**
 * 团队成员
 * Class TeamController
 * @package App\Modules\TalentReview\Controllers
 */
class StaffController extends BaseController
{
    /**
     * 员工列表
     * @Permission(action='talent_review.department_staff.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffListAction()
    {
        $params = $this->request_params;
        try {
            $staff_service = new StaffService();
            $params_validation = $staff_service->getStaffListParamsValidation();
            $page_validation = [
                'pageNum' => 'Required|IntGe:1|>>>:pageNum params error', //当前页码
                'pageSize' => 'Required|IntGe:1|>>>:pageSize params error', //每页条数
            ];
            Validation::validate($params, array_merge($page_validation, $params_validation));

            $result = (new StaffService())->getStaffList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 人员profile
     * @Permission(action='talent_review.department_staff.profile')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffProfileAction()
    {
        $params = $this->request_params;
        try {
            Validation::validate($params, StaffService::$staff_profile_validation);

            $result = (new StaffService())->getStaffProfile($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 人员对比
     * @Permission(action='talent_review.department_staff.contrast')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffContrastAction()
    {
        $params = $this->request_params;
        try {
            Validation::validate($params, StaffService::$staff_contrast_validation);

            $result = (new StaffService())->getStaffContrastList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 员工搜索 按照工号或者姓名搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchStaffAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            //search_name
            //staff_scope
            //search_type 1列表搜索2对比搜索
            Validation::validate($params, StaffService::$search_staff_validation);

            $result = (new StaffService())->searchStaffList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 员工导出
     * @Permission(action='talent_review.department_staff.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportStaffAction()
    {
        $params = $this->request_params;
        try {
            $staff_service = new StaffService();
            $params_validation = $staff_service->getStaffListParamsValidation();
            Validation::validate($params, $params_validation);

            $result = (new StaffService())->addDownloadCenter($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }
}