<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\TalentReview\Services\ReportMapService;
use App\Modules\TalentReview\Services\SysInfoService;
use Exception;

/**
 * 人才盘点 - 公共资源部分
 * Class SysInfoController
 * @package App\Modules\TalentReview\Controllers
 */
class SysInfoController extends BaseController
{
    /**
     * 静态资源部分
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staticInfoAction()
    {
        $result = [];
        $params = $this->request_params;
        try {
            $result = (new SysInfoService())->getStaticInfo($params);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'error' => $e->getMessage(),'file' => $e->getFile(), 'line' => $e->getLine()]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 职位搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchJobTitleAction()
    {
        $params = $this->request_params;
        try {
            Validation::validate($params, ['search_name' => 'Required|StrLenGeLe:1,100|>>>:Job Title Name Error']);

            $result = (new SysInfoService())->searchJobTitle($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'error' => $e->getMessage(),'file' => $e->getFile(), 'line' => $e->getLine()]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 部门列表-tree
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function departmentListAction() {
        $result = [];
        $params = $this->request_params;
        try {
            $result = (new SysInfoService())->getDepartmentTreeList();
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'error' => $e->getMessage(),'file' => $e->getFile(), 'line' => $e->getLine()]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 网点搜索
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function searchStoreAction() {
        $params = $this->request_params;
        try {
            Validation::validate($params, ['search_name' => 'Required|StrLenGeLe:1,100|>>>:Job Title Name Error']);

            $result = (new SysInfoService())->searchStore($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error(['params' => $params, 'error' => $e->getMessage(),'file' => $e->getFile(), 'line' => $e->getLine()]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }
}