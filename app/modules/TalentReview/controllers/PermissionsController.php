<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\ErrCode;
use App\Modules\TalentReview\Services\PermissionsService;
use Exception;

/**
 * 权限部分
 * Class PermissionsController
 * @package App\Modules\TalentReview\Controllers
 */
class PermissionsController extends BaseController
{
    /**
     * 是否有职级查看权限
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function isLookJobTitleGradeAction()
    {
        $result['is_look_job_title_grade'] = false;
        try {
            $params = $this->request_params;
            $user_id = $params['user']['id'];
            $result['is_look_job_title_grade'] = (new PermissionsService())->isLookJobTitleGrade($user_id);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 验证用户是否有查看权限 我的公司 我的管辖 公司全员
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function validationPermissionsAction()
    {
        try {
            $params = $this->request_params;
            $result = (new PermissionsService())->validatePermission($params);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }
}