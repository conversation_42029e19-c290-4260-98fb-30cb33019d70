<?php

namespace App\Modules\TalentReview\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\TalentReview\Services\TeamAnalysisService;
use Exception;

/**
 * 团队分析
 * Class TeamAnalysisController
 * @package App\Modules\TalentReview\Controllers
 */
class TeamAnalysisController extends BaseController
{
    /**
     * 入职总数
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function entryStaffCountAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            Validation::validate($params, array_merge(TeamAnalysisService::$department_id_validation,
                TeamAnalysisService::$begin_end_date_validation));

            $result = (new TeamAnalysisService())->getEntryStaffCount($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 警告人数
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function warningStaffCountAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            Validation::validate($params, array_merge(TeamAnalysisService::$department_id_validation,
                TeamAnalysisService::$begin_end_date_validation));
            $result = (new TeamAnalysisService())->getStaffWarningCount($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 警告人数 列表
     * @Token
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function warningStaffListAction()
    {
        $params = $this->request_params;
        try {
            $params_validation = TeamAnalysisService::$begin_end_date_validation;
            $page_validation = [
                'pageNum' => 'Required|IntGe:1|>>>:pageNum params error', //当前页码
                'pageSize' => 'Required|IntGe:1|>>>:pageSize params error', //每页条数
            ];

            Validation::validate($params,
                array_merge(TeamAnalysisService::$department_id_validation, $params_validation, $page_validation));
            $result = (new TeamAnalysisService())->getStaffWarningList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }

    /**
     * 转岗人数
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function transferStaffCountAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            Validation::validate($params, TeamAnalysisService::$begin_end_date_validation);
            $result = (new TeamAnalysisService())->getTransferStaffCount($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 转岗列表
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function transferStaffListAction()
    {
        $params = $this->request_params;
        try {
            $params_validation = TeamAnalysisService::$begin_end_date_validation;
            $page_validation = [
                'pageNum' => 'Required|IntGe:1|>>>:pageNum params error', //当前页码
                'pageSize' => 'Required|IntGe:1|>>>:pageSize params error', //每页条数
            ];
            Validation::validate($params, array_merge($page_validation, $params_validation));
            $result = (new TeamAnalysisService())->getTransferStaffList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 员工基本统计 在职人数、待离职人数、停职人数、平均司龄、试用期人数
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffStatisticsAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            Validation::validate($params, TeamAnalysisService::$department_id_validation);

            $result = (new TeamAnalysisService())->getStaffInfoStatistics($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 性别占比、雇佣类型、学历、司龄、职级 chart
     * @Permission(action='talent_review.team_analysis.staff_info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function staffChartAction()
    {
        $params = $this->request_params;
        $result = [];
        try {
            Validation::validate($params, TeamAnalysisService::$department_id_validation);

            $result = (new TeamAnalysisService())->getStaffChartList($params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

}