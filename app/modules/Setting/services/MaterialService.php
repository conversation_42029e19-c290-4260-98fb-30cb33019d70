<?php

namespace App\Modules\Setting\Services;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetObjectLedger;
use App\Modules\Budget\Models\BudgetObjectProductLedger;
use App\Modules\Ledger\Models\LedgerAccountModel;
use App\Modules\Purchase\Models\PurchaseProductList;

class MaterialService extends BaseService
{
    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                           //每页条数
        'page' => 'IntGt:0',                            //页码
        'material_id' => 'StrLenGeLe:0,50',                  //产品编号
        'description' => 'StrLenGeLe:0,50',                  //产品编号描述
    ];

    public static $validate_params = [
        'material_id' => 'Required|StrLenGeLe:0,25',  // 产品编号
        'description' => 'Required|StrLenGeLe:0,100',  // 描述
        'base_uom' => 'Required|StrLenGeLe:0,10',  // 计量单位
        'product_cate' => 'Required|StrLenGeLe:0,10',  // 产品分类
    ];

    public static $edit_validate_params = [
        'id' => 'Required|IntGt:0', // 产品编号ID
        'description' => 'Required|StrLenGeLe:0,100',  // 描述
        'base_uom' => 'Required|StrLenGeLe:0,10',  // 计量单位
        'product_cate' => 'Required|StrLenGeLe:0,10',  // 产品分类
    ];

    public static $single_validate_params = [
        'id' => 'IntGt:0',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return MaterialService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 产品编号查询列表
     * @param $params array
     * @return array
     */
    public function getMaterialList($params = [])
    {
        $materialId = $params['material_id'];
        $description = $params['description'];
        $limit = $params['page_size'] ?? 20; // 默认每页20条
        $offset = (isset($params['page_size']) && isset($params['page'])) ?
            ($params['page_size'] * ($params['page'] - 1)) : 0; // 默认偏移量为0

        $builder = $this->modelsManager->createBuilder();
        $column_str = "id,material_id,description,base_uom,product_cate";
        $builder->columns($column_str);
        $builder->from(['c' => PurchaseProductList::class]);

        if (!empty($materialId)) {
            $builder->andWhere('c.material_id like :material_id:', ['material_id' => "%{$materialId}%"]);
        }
        if (!empty($description)) {
            $builder->andWhere('c.description like :description:', ['description' => "%{$description}%"]);
        }

        $count = $builder->getQuery()->execute()->count();
        $builder->limit($limit, $offset);
        $builder->orderBy('id desc');
        $items = $builder->getQuery()->execute()->toArray();

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $params['page'],
                'page_limit'   => $params['page_size'],
                'total_count'  => $count,
            ]
        ];
    }

    /**
     * 保存单个产品编号
     * @param $data
     * @param $user
     * @return mixed
     */
    public function saveOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $productNo = PurchaseProductList::findFirst([
                'conditions' => 'material_id = :material_id:',
                'bind' => ['material_id' => $data['material_id']]
            ]);
            // 编号已存在
            if (!empty($productNo)) {
                throw new ValidationException(static::$t->_('setting_product_no_is_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            // 参数处理
            $data = $this->handleItems($data,$user);

            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => addMaterialAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );

            $model = new PurchaseProductList();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('产品编号创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('product-no-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 批量导入产品编号
     * @param $param
     * @param $excel_file
     * @param $user
     * @return mixed
     */
    public function batchImport($param, $excel_file, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $this->logger->info('batchImport: parameter => ' . json_encode($param, JSON_UNESCAPED_UNICODE)
                . ', user =>' . json_encode($user, JSON_UNESCAPED_UNICODE));

            if (!empty($excel_file)) {
                $config = ['path' => ''];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile($excel_file[0]->getTempName())
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();
            } else {
                $config = ['path' => BASE_PATH . '/public'];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile('obj_dep.xlsx')
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();
            }
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('setting_product_no_excel_import_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            // 去空行
            foreach ($excel_data as $k => $line) {
                if (empty($line[0]) && empty($line[1]) && empty($line[2]) && empty($line[3])) {
                    unset($excel_data[$k]);
                }
            }

            // 空校验
            $error_tip = $this->isImportDataFieldEmpty($excel_data);
            if (!empty($error_tip)) {
                throw new ValidationException($error_tip, ErrCode::$VALIDATE_ERROR);
            }
            // 预算重复性校验
            $is_repeat = $this->isMaterialIdRepeat($excel_data);
            if ($is_repeat) {
                throw new ValidationException(static::$t->_('setting_product_no_is_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            // 加锁导入数据
            $lock_key = md5('setting_material_source_data_import_' . $user['id']);
            $import_res = $this->atomicLock(function () use ($excel_data, $user) {
                return $this->sourceDataCheck($excel_data, $user);
            }, $lock_key, 60);

            if (ErrCode::$SUCCESS != $import_res['code']) {
                throw new ValidationException($import_res['message'], $import_res['code']);
            }
            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('batch-import-material-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * 编辑单个产品编号
     * @param $data
     * @param $user
     * @return mixed
     */
    public function editOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $product = PurchaseProductList::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']]
            ]);
            // 核算科目不存在
            if (empty($product)) {
                throw new BusinessException('产品编号不存在', ErrCode::$VALIDATE_ERROR);
            }
            // 参数处理
            $data = $this->handleItems($data,$user);
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => editMaterialAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            $bool = $product->save($data);
            if ($bool === false) {
                throw new BusinessException('产品编号编辑失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('product-no-edit-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 处理列表数据
     * @param $items
     * @param $user
     * @return array
     */
    private function handleItems($items,$user)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $items['last_update_id'] = $user['id'];
        $items['last_update_at'] = date('Y-m-d H:i:s');

        return $items;
    }

    /**
     * 是否存在重复产品编号
     * @param $excel_data
     * @return bool
     */
    private function isMaterialIdRepeat($excel_data = []){

        if (!empty($excel_data)) {
            $materialId = array_values(array_column($excel_data,0));
            $materialIdCount = count($materialId);
            // 文件内编号重复
            if (count(array_unique($materialId)) != $materialIdCount) {
                return true;
            }
            // 已经存在产品编号
            $isExist = PurchaseProductList::findFirst([
                'conditions' => 'material_id in({material_id:array})',
                'bind' => ['material_id' => $materialId]
            ]);
            if (!empty($isExist)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 是否存在必填的空数据
     * @param $excel_data
     * @return string
     */
    private function isImportDataFieldEmpty($excelData = []){
        if (empty($excelData)) {
            return '';
        }

        $errorTip = '';
        foreach ($excelData as $item) {
            // 产品编号为空
            if ('' == $item[0]) {
                $errorTip = static::$t->_('setting_excel_import_material_id_empty');
                break;
            }
            // 描述为空
            if ('' == $item[1]) {
                $errorTip = static::$t->_('setting_excel_import_description_empty');
                break;
            }
            // 计量单位为空
            if ('' == $item[2]) {
                $errorTip = static::$t->_('setting_excel_import_base_uom_empty');
                break;
            }
            // 物料编码为空
            if ('' == $item[3]) {
                $errorTip = static::$t->_('setting_excel_import_product_cate_empty');
                break;
            }
        }

        return $errorTip;
    }

    /**
     * 文件数据入库逻辑
     * @param $excel_data
     * @return array
     */
    private function sourceDataCheck($excel_data, $user){
        $importRes = [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => []
        ];
        if (empty($excel_data)) {
            return $importRes;
        }

        $data = [];
        foreach ($excel_data as $item) {
            $data[] = [
                'material_id'   => trim($item[0]),
                'description'   => trim($item[1]),
                'base_uom'      => trim($item[2]),
                'product_cate'  => trim($item[3]),
                'is_del'        => 0,
                'last_update_id'=> $user['id'],
                'last_update_at'=> date('Y-m-d H:i:s')
            ];
        }

        // 批量入库
        $purchaseProductList = new PurchaseProductList();
        $bool = $purchaseProductList->batch_insert($data);
        if (!$bool) {
            $importRes['code'] = ErrCode::$SYSTEM_ERROR;
            $importRes['message'] = 'Batch insert error';
        }

        return $importRes;
    }

    /**
     * 生成导入模板
     */
    public function importTemplate()
    {
        $default_file = 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1645532738-113d74fad9eb4d0492e70c7a785be87c.xlsx';
        $header_info = get_headers($default_file, 1);
        if (!empty($header_info[0]) && strrpos($header_info[0],'200') !== false && !empty($header_info['Content-Length'])) {
            $file_url = $default_file;
        } else {
            $column = array(
                '产品编号',
                '产品描述',
                '单位',
                '物料编码'
            );

            $file_name = "product_no_" . date("YmdHis");
            $new_data = array_map('array_values',[]);
            $r = $this->exportExcel($column, $new_data, $file_name);

            $file_url = $r['data'] ?? '';
        }

        $return = [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'error',
            'data' => ''
        ];
        if (!empty($file_url)) {
            $return['code'] = ErrCode::$SUCCESS;
            $return['message'] = 'success';
            $return['data'] = $file_url ?? '';
        }
        return $return;
    }

    /**
     * 批量删除产品编号
     * @param $ids
     * @param $user
     * @return mixed
     */
    public function batchDelete($ids, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 需要删除的产品编号
            $productModel = PurchaseProductList::find([
                'conditions' => 'id in({ids:array})',
                'bind' => ['ids' => $ids],
                'for_update' => true
            ]);
            $ledgers = $productModel->toArray();
            $ledgersIds = array_column($ledgers,'id');

            foreach ($ids as $id) {
                // 产品编号是否存在
                if (!in_array($id,$ledgersIds)) {
                    throw new BusinessException('产品编号不存在', ErrCode::$VALIDATE_ERROR);
                }
            }

            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => deleteLedgerAction before & params=>" . json_encode($ids, JSON_UNESCAPED_UNICODE)
            );
            // 删除
            $bool = $productModel->delete();
            if ($bool === false) {
                throw new BusinessException('产品编号删除失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('product-model-delete-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }
}