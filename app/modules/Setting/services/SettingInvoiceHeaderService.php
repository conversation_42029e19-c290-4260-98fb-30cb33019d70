<?php
/**
 * 配置发票抬头
 */

namespace App\Modules\Setting\Services;

use App\Library\Enums\SettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\SettingInvoiceHeaderModel;
use App\Modules\Common\Services\EnumsService;
use App\Repository\oa\SettingInvoiceHeaderRepository;
use \Exception;

class SettingInvoiceHeaderService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 发票抬头新增和编辑的校验
     *
     * @param array $params
     * @param bool $is_edit
     * @throws ValidationException
     */
    public function save_validate_params(array $params, $is_edit = false)
    {
        $save_validate_params = [
            'related_company_ids' => 'Required|ArrLenGe:1|>>>:' . static::$t->_('invoice_header_associated_company_error'),
        ];

        if ($is_edit) {
            $save_validate_params['id'] = 'Required|IntGt:0|>>>:params error [id]';
        } else {
            $save_validate_params['header_name'] = 'Required|StrLenGeLe:1,200|>>>:' . static::$t->_('invoice_header_name_validate_error');
        }

        // 通用静态校验
        Validation::validate($params, $save_validate_params);

        // "不限" 选项 与 具体的BU选项 不能同时选择
        if (count($params['related_company_ids']) > 1 && in_array(SettingEnums::INVOICE_HEADER_RELATED_COMPANY_TYPE_ALL,
                $params['related_company_ids'])) {
            throw new ValidationException(static::$t->_('invoice_header_associated_company_error_1'),
                ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 发票抬头列表
     *
     * @return array
     */
    public function getList()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items' => [],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SettingInvoiceHeaderModel::class);
            $builder->columns(['id', 'header_name', 'related_company_ids']);
            $builder->orderby('id DESC');
            $items = $builder->getQuery()->execute()->toArray();
            if (!empty($items)) {
                // 全部BU
                $company_list = EnumsService::getInstance()->getCompanyList();
                $company_list = array_column($company_list, 'label', 'value');

                foreach ($items as $k => $v) {
                    $related_company_ids   = explode(',', $v['related_company_ids']);
                    $related_company_names = '';

                    // 原关联的公司被删除/类型变更均无需展示
                    $_new_related_company_ids = [];
                    foreach ($related_company_ids as $company_id) {
                        $company_name = $company_list[$company_id] ?? '';
                        if (!empty($company_name)) {
                            $related_company_names      .= $company_name . ', ';
                            $_new_related_company_ids[] = $company_id;
                        }
                    }

                    $items[$k]['related_company_ids']   = $_new_related_company_ids;
                    $items[$k]['related_company_names'] = trim($related_company_names, ' ,');
                }
            }

            $data['items'] = $items;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('业务配置-发票抬头列表获取失败, ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 发票抬头添加
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 名称是否重复
            $exist = SettingInvoiceHeaderRepository::getInstance()->getInfoByHeaderName($params['header_name']);
            if (!empty($exist)) {
                throw new ValidationException(static::$t->_('invoice_header_name_exist', ['id' => $exist->id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $data  = [
                'header_name'         => $params['header_name'],
                'related_company_ids' => implode(',', array_unique($params['related_company_ids'])),
                'created_id'          => $user['id'],
                'created_at'          => date('Y-m-d H:i:s'),
                'updated_id'          => $user['id'],
                'updated_at'          => date('Y-m-d H:i:s'),
            ];
            $model = new SettingInvoiceHeaderModel();
            if ($model->i_create($data) === false) {
                throw new BusinessException('发票抬头添加失败, 原因可能是:' . get_data_object_error_msg($model) . '数据:' . json_encode($data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('business_setting_invoice_header_add: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('business_setting_invoice_header_add: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 发票抬头更新
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function update(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $exist = SettingInvoiceHeaderRepository::getInstance()->getInfoById($params['id']);
            if (empty($exist)) {
                throw new ValidationException(static::$t->_('invoice_header_name_exist_error', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('业务设置-发票抬头编辑-编辑前数据: ' . json_encode($exist->toArray(), JSON_UNESCAPED_UNICODE));

            $exist->related_company_ids = implode(',', array_unique($params['related_company_ids']));
            $exist->updated_id          = $user['id'];
            $exist->updated_at          = date('Y-m-d H:i:s');

            $this->logger->info('业务设置-发票抬头编辑-编辑后数据: ' . json_encode($exist->toArray(), JSON_UNESCAPED_UNICODE));
            if ($exist->save() === false) {
                throw new BusinessException('发票抬头添加失败, 原因可能是:' . get_data_object_error_msg($exist),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('business_setting_invoice_header_update: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('business_setting_invoice_header_update: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }
}
