<?php

namespace App\Modules\Setting\Services;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\SupplierBankModel;

class SupplierBankService extends BaseService
{
    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                           //每页条数
        'page' => 'IntGt:0',                            //页码
        'bank_name' => 'StrLenGeLe:0,50',                  //开户行描述
    ];

    public static $validate_params = [
        'bank_code' => 'Required|StrLenGeLe:1,30', // 开户行编号
        'bank_name' => 'Required|StrLenGeLe:1,50'  // 开户行描述
    ];

    public static $edit_validate_params = [
        'id' => 'Required', // 开户行id
        'bank_name' => 'Required|StrLenGeLe:1,50'  // 开户行描述
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return SupplierBankService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 开户行列表
     * @param $params array
     * @return array
     */
    public function getSupplierBankList($params = [])
    {
        $bankName = $params['bank_name'];
        $limit = $params['page_size'] ?? 20; // 默认每页20条
        $offset = (isset($params['page_size']) && isset($params['page'])) ?
            ($params['page_size'] * ($params['page'] - 1)) : 0; // 默认偏移量为0

        $builder = $this->modelsManager->createBuilder();
        $column_str = "id,bank_code,bank_name";
        $builder->columns($column_str);
        $builder->from(['c' => SupplierBankModel::class]);
        $builder->where('c.is_del = 0');
        if (!empty($bankName)) {
            $builder->andWhere('c.bank_name like :bank_name:', ['bank_name' => "%{$bankName}%"]);
        }

        $count = $builder->getQuery()->execute()->count();
        $builder->limit($limit, $offset);
        $builder->orderBy('id desc');
        $items = $builder->getQuery()->execute()->toArray();

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $params['page'],
                'page_limit'   => $params['page_size'],
                'total_count'  => $count,
            ]
        ];
    }

    /*
     * 生成开户行编号
     * @return string
     * */
    public function generateSerialNo()
    {
        // 国家
        $country = get_country_code();
        // 生成编号
        $supplier = SupplierBankModel::findFirst([
            'order' => 'id desc',
            'for_update' => true,
        ]);
        $no = isset($supplier->id) ? $supplier->id : '0';

       return  strtolower($country) . '.' . 'bank' . '.' . $no;
    }

    /**
     * 保存开户行
     * @param $data
     * @param $user
     * @return mixed
     */
    public function saveOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $supplier = SupplierBankModel::findFirst([
                'conditions' => 'bank_code = :bank_code:',
                'bind' => ['bank_code' => $data['bank_code']]
            ]);
            // 开户行已存在
            if (!empty($supplier)) {
                throw new ValidationException(static::$t->_('supplier_bank_no_has_been_exist'), ErrCode::$VALIDATE_ERROR);
            }
            $supplier = SupplierBankModel::findFirst([
                'conditions' => 'bank_name = :bank_name:',
                'bind' => ['bank_name' => $data['bank_name']]
            ]);
            // 开户行描述重复
            if (!empty($supplier)) {
                throw new ValidationException(static::$t->_('supplier_bank_name_is_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            // 参数处理
            $data = $this->handleItems($data,$user);

            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => addSupplierBankAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );

            $model = new SupplierBankModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('supplier_bank_create_failed'), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('supplier-bank-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 编辑开户行
     * @param $data
     * @param $user
     * @return mixed
     */
    public function editOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $supplier = SupplierBankModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']]
            ]);
            // 开户行不存在
            if (empty($supplier)) {
                throw new BusinessException('开户行不存在', ErrCode::$VALIDATE_ERROR);
            }
            // 参数处理
            $data = $this->handleItems($data,$user);
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => addSupplierBankAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            $bool = $supplier->save($data);
            if ($bool === false) {
                throw new BusinessException('supplier_bank_edit_failed', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('supplier-bank-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * @param $items
     * @param $user
     * @return array
     */
    private function handleItems($items,$user)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $items['is_del'] = 0;
        $items['last_update_id'] = $user['id'];
        $items['last_update_at'] = date('Y-m-d H:i:s');

        return $items;
    }
}