<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Setting\Models\CostCategoryReimbursementDetailModel;

class CostCategoryReimbursementDetailService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance(): CostCategoryReimbursementDetailService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    const SUPPORT_BIZ_TYPE_REIMBURSEMENT = 1;   //支持模块-报销管理
    const SUPPORT_BIZ_TYPE_ORDINARY_PAYMENT = 3;//支持模块-普通付款

    public static $validate_list_search = [
        'pageSize'       => 'IntGt:0',                            // 每页条数
        'pageNum'        => 'IntGt:0',                            // 页码
        'budget_name_cn' => 'StrLenGeLe:0,100',                   // 预算科目中文
        'biz_type'       => 'IntGt:0',                            // 业务类型
    ];

    public static $edit_validate_params = [
        'id'               => 'Required|IntGt:0|>>>:params error[id]',
        'biz_type'         => 'Required|IntGt:0|>>>:params error[biz_type]',                          // 业务类型
        'budget_object_id' => 'Required|IntGt:0|>>>:params error[budget_object_id]',                  // 预算科目id
        'translation_key'  => 'Required|StrLenGeLe:1,100|>>>:params error[translation_key]',          // 翻译key
    ];

    public static $add_validate_params = [
        'biz_type'         => 'Required|IntGt:0|>>>:params error[biz_type]',                          // 业务类型
        'budget_object_id' => 'Required|IntGt:0|>>>:params error[budget_object_id]',                  // 预算科目id
        'translation_key'  => 'Required|StrLenGeLe:1,100|>>>:params error[translation_key]',          // 翻译key
    ];


    public static $delete_validate_params = [
        'id' => 'Required|IntGt:0|>>>:params error[id]',
    ];

    /**
     * 涉及的业务模块
     * @return array[]
     */
    protected function getBizTypeList(): array
    {
        return [
            [
                'label' => self::$t->_('sys_module_reimbursement'),
                'value' => self::SUPPORT_BIZ_TYPE_REIMBURSEMENT,
            ],
            [
                'label' => self::$t->_('sys_module_ordinary_payment'),
                'value' => self::SUPPORT_BIZ_TYPE_ORDINARY_PAYMENT,
            ],
        ];
    }


    public function getOptionsDefault(): array
    {
        return [
            'biz_type_list' => $this->getBizTypeList(),
        ];
    }


    /**
     * 验证
     * @param array $data
     * @return bool
     * @throws BusinessException
     */
    protected function validation(array $data): bool
    {
        $translation_key_conditions = "translation_key = :translation_key: and is_deleted = :is_deleted:";
        $translation_key_bind       = [
            'translation_key' => $data['translation_key'],
            'is_deleted'      => GlobalEnums::IS_NO_DELETED,
        ];

        $biz_type_conditions = "biz_type = :biz_type: and budget_object_id = :budget_object_id: and is_deleted = :is_deleted:";
        $biz_type_bind       = [
            'biz_type'         => $data['biz_type'],
            'budget_object_id' => $data['budget_object_id'],
            'is_deleted'       => GlobalEnums::IS_NO_DELETED,
        ];

        if (!empty($data['id'])) {
            $translation_key_conditions .= " and id != :id:";
            $translation_key_bind['id'] = $data['id'];
            $biz_type_conditions        .= " and id != :id:";
            $biz_type_bind['id']        = $data['id'];
        }

        $exist = CostCategoryReimbursementDetailModel::findFirst([
            'conditions' => $translation_key_conditions,
            'bind'       => $translation_key_bind,
        ]);
        if (!empty($exist)) {
            throw new BusinessException(self::$t->_('translation_key_already_exist'));
        }

        $exist = CostCategoryReimbursementDetailModel::findFirst([
            'conditions' => $biz_type_conditions,
            'bind'       => $biz_type_bind,
        ]);
        if (!empty($exist)) {
            throw new BusinessException(self::$t->_('biz_type_budget_already_exist'));
        }
        return true;
    }


    /**
     * 获取预算科目中文名
     * @param $budget_object_id
     * @return mixed
     * @throws BusinessException
     */
    protected function getBudgetNameCn($budget_object_id)
    {
        $model = BudgetObject::findFirst($budget_object_id);
        if (empty($model)) {
            throw new BusinessException(self::$t->_('budget_object_not_found'));
        }
        return $model->name_cn;
    }


    /**
     * 新增
     * @param $data
     * @param $user
     * @return true
     * @throws BusinessException
     */
    public function add($data, $user): bool
    {
        //公共验证
        $this->validation($data);
        $model                   = new CostCategoryReimbursementDetailModel();
        $model->biz_type         = $data['biz_type'];
        $model->translation_key  = $data['translation_key'];
        $model->budget_object_id = $data['budget_object_id'];
        $model->budget_name_cn   = $this->getBudgetNameCn($data['budget_object_id']);
        $model->created_id       = $user['id'];
        $model->created_at       = date('Y-m-d H:i:s');
        $model->updated_id       = $user['id'];
        $model->updated_at       = date('Y-m-d H:i:s');
        return $model->save();
    }


    /**
     * 编辑
     * @param $data
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function edit($data, $user)
    {
        //公共验证
        $this->validation($data);
        $model = CostCategoryReimbursementDetailModel::findFirst($data['id']);
        if (empty($model)) {
            throw new BusinessException(self::$t->_('empty_data'));
        }
        $model->biz_type         = $data['biz_type'];
        $model->translation_key  = $data['translation_key'];
        $model->budget_object_id = $data['budget_object_id'];
        $model->budget_name_cn   = $this->getBudgetNameCn($data['budget_object_id']);
        $model->updated_id       = $user['id'];
        $model->updated_at       = date('Y-m-d H:i:s');
        return $model->save();
    }

    /**
     * 编辑
     * @param $data
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function delete($data, $user)
    {
        $model = CostCategoryReimbursementDetailModel::findFirst($data['id']);
        if (empty($model)) {
            throw new BusinessException(self::$t->_('empty_data'));
        }
        if($model->is_deleted == GlobalEnums::IS_DELETED){
            throw new BusinessException(self::$t->_('submit_audit_error.4'));
        }
        $model->is_deleted = GlobalEnums::IS_DELETED;
        $model->updated_id = $user['id'];
        $model->updated_at = date('Y-m-d H:i:s');
        return $model->save();
    }

    /**
     * 系统配置项列表
     *
     * @param $data
     * @return array
     */
    public function list($data): array
    {
        $limit  = $data['pageSize'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;                                                       // 默认每页20条
        $offset = isset($data['pageNum']) ? ($limit * ($data['pageNum'] - 1)) : 0; // 默认偏移量为0
       
        $builder = $this->modelsManager->createBuilder();
        $builder->from(CostCategoryReimbursementDetailModel::class);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($data['budget_name_cn'])) {
            $builder->andWhere('budget_name_cn like :budget_name_cn:', ['code' => "%{$data['budget_name_cn']}%"]);
        }
        if (!empty($data['biz_type'])) {
            $builder->andWhere('biz_type = :biz_type:', ['biz_type' => $data['biz_type']]);
        }
        $count = (int)$builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;

        $items = [];
        if ($count) {
            $builder->columns('id, biz_type, translation_key, budget_name_cn,budget_object_id');
            $builder->limit($limit, $offset);
            $items              = $builder->getQuery()->execute()->toArray();
            $biz_type_name_list = array_column($this->getBizTypeList(), 'label', 'value');
            foreach ($items as &$value) {
                $value['biz_type_text'] = $biz_type_name_list[$value['biz_type']] ?? '';
            }
        }
        return ['list' => $items, 'total' => $count];
    }


    /**
     * 获取配置的翻译信息map
     * @param $biy_type
     * @return array
     */
    public function getMapByBizType($biy_type): array
    {
        $result = [];
        $data = CostCategoryReimbursementDetailModel::find([
            'columns'    => 'translation_key,budget_object_id',
            'conditions' => "biz_type = :biz_type:  and is_deleted = :is_deleted:",
            'bind'       => ['biz_type' => $biy_type, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
        ])->toArray();
        if(empty($data)){
            return [];
        }
        foreach ($data as $item) {
            $result[$item['budget_object_id']] = self::$t->_($item['translation_key']);
        }
        return $result;
    }




}
