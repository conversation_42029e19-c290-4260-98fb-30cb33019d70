<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanStaffGradeAmountModel;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanStaffAmount;
use App\Modules\Loan\Services\AddService;
use App\Modules\User\Services\UserService;
use App\Repository\HrStaffRepository;

class LoanStaffService extends BaseService
{
    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                           //每页条数
        'page' => 'IntGt:0',                            //页码
        'staff_info_id' => 'IntGt:0',                  //员工工号
    ];

    public static $add_validate_params = [
        'staff_info_id' => 'Required|IntGt:0',  // 员工工号
        'amount' => 'Required|IntGt:0',  // 金额
    ];

    public static $edit_validate_params = [
        'id' => 'Required|IntGt:0', // 产品编号ID
        'amount' => 'Required|IntGt:0',  // 金额
    ];

    public static $delete_validate_params = [
        'id' => 'Required|IntGt:0',
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LoanStaffService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 借款员工配置查询列表
     * @param $params array
     * @return array
     */
    public function getLoanStaffList($params = [])
    {
        $staff_info_id = $params['staff_info_id'] ?? 0;
        $page_size = empty($params['page_size']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['page_size'];
        $page_num = empty($params['page']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['page'];
        $offset = $page_size * ($page_num-1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(LoanStaffAmount::class);
        if (!empty($staff_info_id)) {
            $builder->andWhere('staff_info_id = :staff_info_id:',['staff_info_id'=>$staff_info_id]);
        }
        //总条数
        $count_info = $builder->columns('COUNT(id) AS t_count')->getQuery()->getSingleResult();
        $count = $count_info->t_count ?? 0;
        $items = [];
        if ($count) {
            //分页数据
            $builder->columns("id,staff_info_id,amount");
            $builder->limit($page_size, $offset);
            $builder->orderBy('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleItems($items);
        }

        return [
            'items' => $items,
            'pagination' => [
                'current_page' => $page_num,
                'page_limit' => $page_size,
                'total_count' => $count,
            ],
        ];
    }

    /**
     * 保存借款员工配置
     * @param $data
     * @param $user
     * @return mixed
     */
    public function saveOne($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => addLoanStaff before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            $data = array_only($data,array_keys(self::$add_validate_params));
            // 参数验证-工号 1.是否存在, 2.是否离职 3.是否编制员工
            $user_info = (new UserService())->getUserByIdInRbi($data['staff_info_id']);
            if (empty($user_info)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'),ErrCode::$VALIDATE_ERROR);
            }
            if ($user_info->state == 2) {
                throw new ValidationException(static::$t->_('re_staff_info_id_left'),ErrCode::$VALIDATE_ERROR);
            }
            if ($user_info->formal != 1) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_formal'),ErrCode::$VALIDATE_ERROR);
            }
            //工号是否已配置
            $exist = LoanStaffAmount::findFirst([
                'conditions' => ' staff_info_id = :staff_info_id:',
                'bind' => [
                    'staff_info_id' => $data['staff_info_id']
                ]
            ]);
            if ($exist){
                throw new ValidationException(static::$t->_('loan_staff_non_repeatable_addition'),ErrCode::$VALIDATE_ERROR);
            }
            $data['create_id'] = $user['id'];
            $data['created_at'] = date('Y-m-d H:i:s');
            $model = new LoanStaffAmount();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('借款员工配置创建失败', ErrCode::$LOAN_STAFF_ADD_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan_staff_amount-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }


    /**
     * 编辑借款员工配置
     * @param $data
     * @param $user
     * @return mixed
     */
    public function editOne($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => editLoanStaff before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );

            $model = LoanStaffAmount::findFirst([
                'conditions' => ' id = :id:',
                'bind' => [
                    'id' => $data['id']
                ]
            ]);
            if (!$model){
                throw new ValidationException(static::$t->_('loan_staff_data_not_exist'),ErrCode::$VALIDATE_ERROR);
            }
            // 验证员工当前可用额度
            $staff_loan_all_amount = AddService::getInstance()->getStaffLoanOutstandingAmountReal($model->staff_info_id);
            if ($staff_loan_all_amount>$data['amount']){
                throw new ValidationException(static::$t->_('limit_amount_less_than_loan'), ErrCode::$VALIDATE_ERROR);
            }
            $update = [
                'amount' => $data['amount'],
                'update_id' => $user['id'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            $bool = $model->save($update);
            if ($bool === false) {
                throw new BusinessException('借款员工配置编辑失败', ErrCode::$LOAN_STAFF_EDIT_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan_staff_amount-edit-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 删除员工配置
     * @param $ids
     * @param $user
     * @return mixed
     */
    public function deleteLoanStaff($id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => deleteLoanStaff before & id=>" . json_encode($id, JSON_UNESCAPED_UNICODE)
            );

            //是否存在
            $model = LoanStaffAmount::findFirst([
                'conditions' => ' id = :id:',
                'bind' => [
                    'id' => $id
                ]
            ]);
            if (!$model){
                throw new ValidationException(static::$t->_('loan_staff_data_not_exist'),ErrCode::$VALIDATE_ERROR);
            }
            //验证此工号是否存在未归还借款(待审批,审批通过) 且 (待支付,已支付) 且 (未归还,归还中)
            $loan = Loan::getFirst([
                'create_id = :create_id: and status in (1,3) and pay_status in (1,2) and back_status in (1,2)',
                'bind' => ['create_id' => $model->staff_info_id]
            ]);
            if ($loan){
                throw new ValidationException(static::$t->_('staff_have_loan_amount'),ErrCode::$VALIDATE_ERROR);
            }
            // 传参原数据保存
            $this->getDI()->get('logger')->info("function => deleteLoanStaff info & data=>" . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE)." uid=".$user['id']);

            $bool = $model->delete();
            if ($bool === false) {
                throw new BusinessException('借款员工配置删除失败', ErrCode::$LOAN_STAFF_EDIT_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan_staff_amount-delete-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 多次借款工号格式化
     * @param array $data 列表
     * @return mixed
     */
    public function handleItems($data)
    {
        if (empty($data)) {
            return $data;
        }
        $staff_info_ids = array_column($data, 'staff_info_id');
        $staff_list = (new HrStaffRepository())->getStaffListByStaffIds($staff_info_ids);
        foreach ($data as $key => &$value) {
            $value['amount'] = number_format($value['amount']);

            //获取员工信息
            $one_staff_info = $staff_list[$value['staff_info_id']] ?? [];
            $value['staff_name'] = $one_staff_info['name'] ?? '';
            $state = $one_staff_info['state'] ?? 0;
            $wait_leave_state = $one_staff_info['wait_leave_state'] ?? 0;
            $value['state'] = $state;
            $value['wait_leave_state'] = $wait_leave_state;
            $staff_state = ($state == StaffInfoEnums::STAFF_STATE_IN && $wait_leave_state == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : $state;
            $value['state_text'] = isset(StaffInfoEnums::$staff_state[$staff_state]) ? static::$t[StaffInfoEnums::$staff_state[$staff_state]] : '';
        }
        return $data;
    }

    /**
     * 借款单次借款金额限制列表
     * @return array
     */
    public function getLoanStaffGradeList()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [
            'items' => []
        ];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(LoanStaffGradeAmountModel::class);
            //分页数据
            $builder->columns('id,job_title_grade,amount');
            $builder->orderBy('job_title_grade desc');
            $items = $builder->getQuery()->execute()->toArray();
            foreach ($items as &$item) {
                $item['job_title_grade_text'] = StaffInfoEnums::$config_job_title_grade_v2[$item['job_title_grade']];
                $item['amount'] = number_format($item['amount']);
            }
            $items = array_column($items, null, 'job_title_grade');

            //获取职级列表，未配置的需要初始化列表显示
            $job_title_grade = StaffInfoEnums::$config_job_title_grade_v2;
            $grade_un_set = false;//职级列表未全部配置
            foreach ($job_title_grade as $grade_key => $grade) {
                if (isset($items[$grade_key])) {
                    continue;
                }
                $items[$grade_key] = [
                    'id' => '0',
                    'job_title_grade' => (string)$grade_key,
                    'job_title_grade_text' => $grade,
                    'amount' => '0',
                ];
                $grade_un_set = true;
            }
            if ($grade_un_set) {
                $data['items'] = sort_array_by_fields($items, 'job_title_grade', SORT_DESC);
            } else {
                $data['items'] = array_values($items);
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('业务配置-单次借款金额限制列表获取失败, ' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 单次借款金额限制编辑
     * @param array $data 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function editLoanStaffGrade($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $validate_params = [
                'job_title_grade' => 'Required|IntIn:'.implode(',', array_keys(StaffInfoEnums::$config_job_title_grade_v2)),  //职级
                'amount' => 'Required|IntGt:0',  // 金额
            ];
            Validation::validate($data, $validate_params);
            $model = LoanStaffGradeAmountModel::findFirst([
                'conditions' => 'job_title_grade = :job_title_grade:',
                'bind' => ['job_title_grade' => $data['job_title_grade']]
            ]);
            $now = date('Y-m-d H:i:s');
            if ($model) {
                //存在更新
                $model->amount = $data['amount'];
                $model->update_id = $user['id'];
                $model->updated_at = $now;
                $bool = $model->save();
            } else {
                //不存在新增
                $model = new LoanStaffGradeAmountModel();
                $data['create_id'] = $user['id'];
                $data['created_at'] = $now;
                $data['updated_at'] = $now;
                $bool = $model->i_create($data);
            }
            if ($bool === false) {
                throw new BusinessException('单次借款金额限制编辑失败, 变更信息组：' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能的原因是' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('loan_staff_grade_amount-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }
}