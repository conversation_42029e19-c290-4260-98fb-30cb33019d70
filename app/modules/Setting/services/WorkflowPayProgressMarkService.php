<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\WorkflowPayProgressMarkModel;
use App\Repository\oa\WorkflowPayProgressMarkRepository;

class WorkflowPayProgressMarkService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 进度标记列表
     *
     * @param string $keywords
     * @return array
     */
    public function getList(string $keywords)
    {
        return WorkflowPayProgressMarkRepository::getInstance()->getList($keywords);
    }

    /**
     * 生成翻译key
     * @return string
     */
    public function generateTranslationKey()
    {
        // 生成编号
        $max_id = WorkflowPayProgressMarkRepository::getInstance()->getMaxId();
        $max_id++;
        return strtolower(get_country_code()) . '_' . strtolower(get_runtime_env()) . '_pay_progress_mark_' . $max_id;
    }

    /**
     * 保存进度标记
     * @param $params
     * @param $user
     * @return mixed
     */
    public function saveOne(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        try {
            $model = WorkflowPayProgressMarkRepository::getInstance()->getOneByTranslationkey($params['translation_key']);
            if (empty($model)) {
                $model             = new WorkflowPayProgressMarkModel();
                $model->created_id = $user['id'];
                $model->created_at = date('Y-m-d H:i:s');
            }

            $this->logger->info('进度标记修改前: ' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE));

            $model->translation_key = $params['translation_key'];
            $model->remark          = $params['remark'];
            $model->is_deleted      = GlobalEnums::IS_NO_DELETED;
            $model->updated_id      = $user['id'];
            $model->updated_at      = date('Y-m-d H:i:s');

            $this->logger->info('进度标记修改后: ' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE));
            if ($model->save() === false) {
                throw new BusinessException('系统配置-进度标记保存失败, 原因可能是:' . get_data_object_error_msg($model),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('pay_progress_mark_save_error:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 进度标记枚举列表
     *
     * @param int $is_deleted
     * @return array
     */
    public function getEnumsList($is_deleted = GlobalEnums::IS_NO_DELETED)
    {
        $list = WorkflowPayProgressMarkRepository::getInstance()->getList('', $is_deleted);

        $items = [];
        foreach ($list as $value) {
            $items[] = [
                'value' => $value['id'],
                'label' => static::$t->_($value['translation_key']),
            ];
        }

        return $items;
    }

}