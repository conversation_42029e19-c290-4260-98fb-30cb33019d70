<?php

namespace App\Modules\Setting\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Asset\Models\AssetGoods;
use App\Modules\Setting\Models\SettingBusinessModuleModel;

class BusinessService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return BusinessService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 业务模块列表
     * @param array $params
     * @return array
     */
    public function getBusinessModulesList($params = [])
    {
        $pageSize = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $pageNum  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $start    = ($pageNum - 1) * $pageSize;

        $conditions[] = '1=:1:';
        $bind         = ['1' => 1];

        if (!empty($params['type_name'])) {
            $conditions[]      = 'type_name LIKE :type_name:';
            $bind['type_name'] = "%{$params['type_name']}%";
        }

        if (!empty($params['module_name'])) {
            $conditions[]        = 'module_name LIKE :module_name:';
            $bind['module_name'] = "%{$params['module_name']}%";
        }

        $conditions = implode(' AND ', $conditions);

        $total_count = SettingBusinessModuleModel::count([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        $settingReault = [];
        if ($total_count) {
            $settings = SettingBusinessModuleModel::find([
                'conditions' => $conditions,
                'bind'       => $bind,
                'offset'     => $start,
                'limit'      => $pageSize,
            ])->toArray();

            foreach ($settings as $setting) {
                $settingReault[] = [
                    'type_key'    => $setting['type_key'],
                    'type_name'   => static::$t->_($setting['type_key']),
                    'module_name' => static::$t->_($setting['module_key']),
                    'action_url'  => $setting['action_url'],
                ];
            }
        }

        return [
            'items'      => $settingReault,
            'pagination' => [
                'current_page' => (int)$pageNum,
                'per_page'     => (int)$pageSize,
                'total_count'  => $total_count,
            ],
        ];
    }
}
