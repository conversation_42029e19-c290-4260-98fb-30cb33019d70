<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\StaffManageGroupModel;
use App\Models\oa\StaffManageListModel;
use App\Models\oa\StaffManageListRelateModel;
use App\Models\oa\ManageGroupDepartmentRelateModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Hc\Models\HrStaffInfoModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\HrJobTitleRepository;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Organization\Models\SysDepartmentModel as wSysDepartmentModel;


class FinancialService extends BaseService
{
    //redis list key
    public static $redis_list_name   = 'financial_change_list_send';
    public static $redis_list_type_1 = 1;//部门新增
    public static $redis_list_type_2 = 2;//员工变更

    public static $validate_list_search = [
        'pageSize' => 'IntGt:0',                            // 每页条数
        'pageNum'  => 'IntGt:0',                            // 页码
    ];
    //财务分组
    public static $add_validate_params = [
        'name'           => 'Required|StrLenGeLe:1,50|>>>:param error[name]',
        'department_ids' => 'Required|StrLenGeLe:1,2000|>>>:param error[department_ids]',
        'remark'         => 'Required|StrLenGeLe:0,500|>>>:param error[content]',                // 描述
    ];
    //财务分组修改
    public static $edit_validate_params = [
        'id'             => 'Required|StrLenGeLe:1,11|>>>:param error[id]',
        'name'           => 'Required|StrLenGeLe:1,50|>>>:param error[name]',
        'department_ids' => 'Required|StrLenGeLe:1,2000|>>>:param error[department_ids]',
        'remark'         => 'Required|StrLenGeLe:0,500|>>>:param error[content]',                // 描述
    ];
    //财务人员列表
    public static $search_validate_params = [
        'pageSize'      => 'IntGt:0',                            // 每页条数
        'pageNum'       => 'IntGt:0',                            // 页码
        'staff_info_id' => 'StrLenGeLe:0,11|>>>:param error[staff_info_id]',
        'group_id'      => 'StrLenGeLe:0,50|>>>:param error[group_id]',
    ];

    public static $detail_validate_params = [
        'id' => 'Required|StrLenGeLe:1,11|>>>:param error[id]',
    ];
    // 管辖区修改验证
    public static $edit_financial_validate_params = [
        'id'                     => 'StrLenGeLe:1,11|>>>:param error[id]',
        'group_id'               => 'Required|StrLenGeLe:1,50|>>>:param error[name]',
        'data'                   => 'Required|ArrLenGeLe:0,100',
        'data[*].department_id'  => "StrLenGeLe:1,32",
        'data[*].is_include_sub' => "IntIn:1,0",

    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return FinancialService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 财务分组列表
     * @param $params array
     * @return array
     */
    public function getGroupList($params = [])
    {
        $code      = ErrCode::$SUCCESS;
        $message   = 'success';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);


        try {
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['s' => StaffManageGroupModel::class]);
            $builder->andWhere('s.is_deleted = :is_deleted: and type = :type:', [
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'type'       => SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_FINANCE,
            ]);
            $count = (int)$builder->columns('COUNT(DISTINCT(s.id)) AS total')->getQuery()->getSingleResult()->total;

            if ($count > 0) {
                $column_str = 'id,name,department_ids,remark';
                $builder->columns($column_str);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();

                $items = $this->handelGroupItems($items);
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('financial-group-list-failed:' . $e->getMessage());
        }


        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'items'      => $items ?? [],
                'pagination' => [
                    'current_page' => $params['pageNum'] ?? 1,
                    'per_page'     => $page_size,
                    'total_count'  => $count ?? 0,
                ],
            ],
        ];
    }

    /***
     * 新增分组
     * @param $params
     * @param $uid
     * @return array
     */
    public function addGroup($params, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $group_model = new StaffManageGroupModel();
            $add_data    = [
                'name'           => $params['name'],
                'department_ids' => $params['department_ids'],
                'type'           => SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_FINANCE,
                'remark'         => $params['remark'],
                'created_at'     => date('Y-m-d H:i:s'),
                'updated_at'     => date('Y-m-d H:i:s'),
                'updated_id'     => $uid,
            ];
            if ($group_model->save($add_data) == false) {
                throw new \Exception('财务审批分组创建失败，sql错误信息： ' . get_data_object_error_msg($group_model) . 'add_group_data' . json_encode($add_data,
                        JSON_UNESCAPED_UNICODE));
            }

            //关联关系表
            $group_department_relate = [];
            $department_ids          = explode(',', $params['department_ids']);
            foreach ($department_ids as $department_id) {
                $group_department_relate[] = [
                    'group_id'      => $group_model->id,
                    'department_id' => $department_id,
                ];
            }

            $relate_model = new ManageGroupDepartmentRelateModel();

            if ($relate_model->batch_insert($group_department_relate) == false) {
                throw new \Exception('财务审批分组关联关系创建失败，sql错误信息： ' . get_data_object_error_msg($relate_model) . 'add_group_relate' . json_encode($group_department_relate,
                        JSON_UNESCAPED_UNICODE));
            }

            $db->commit();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $db->rollback();
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('财务审批分工管理-addGroup-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /***
     * 编辑分组
     * @param $params
     * @param $uid
     * @return array
     */
    public function editGroup($params, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $group_model = StaffManageGroupModel::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                ]);
            if (empty($group_model)) {
                throw new ValidationException(static::$t->_('group_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('edit_group_old_data' . json_encode($group_model->toArray(), JSON_UNESCAPED_UNICODE));

            //删除 已删除的原有分组部门管辖人员
            $org_department_ids      = explode(',', $group_model->department_ids);
            $department_ids          = explode(',', $params['department_ids']);
            $diff_department_ids     = array_diff($org_department_ids, $department_ids);
            $new_diff_department_ids = array_diff($department_ids, $org_department_ids);
            if (!empty($diff_department_ids) || !empty($new_diff_department_ids)) {
                $group_relate_model = ManageGroupDepartmentRelateModel::find([
                    'conditions' => "group_id = :group_id:",
                    'bind'       => ['group_id' => $params['id']],
                ]);

                $this->logger->info('edit_group_relate_old_data' . json_encode($group_relate_model->toArray(),
                        JSON_UNESCAPED_UNICODE));
                $group_relate_bool = $group_relate_model->delete();
                if ($group_relate_bool == false) {
                    throw new \Exception('财务审批分组manage_group_department_relate删除失败');
                }

                //关联关系表
                $group_department_relate = [];
                foreach ($department_ids as $department_id) {
                    $group_department_relate[] = [
                        'group_id'      => $group_model->id,
                        'department_id' => $department_id,
                    ];
                }
                $relate_model = new ManageGroupDepartmentRelateModel();

                if ($relate_model->batch_insert($group_department_relate) == false) {
                    throw new \Exception('财务审批分组关联关系创建失败，sql错误信息： ' . get_data_object_error_msg($relate_model) . 'edit_group_department_relate' . json_encode($group_department_relate,
                            JSON_UNESCAPED_UNICODE));
                }
            }

            if (!empty($diff_department_ids)) {
                $staff_ids = (new HrStaffRepository())->getStaffByDepartment(array_values($diff_department_ids));
                $staff_ids = implode(',', array_column($staff_ids, 'staff_info_id'));
                if ($staff_ids) {
                    $manage_list_bool = $db->updateAsDict(
                        (new StaffManageListModel())->getSource(),
                        ['is_deleted' => GlobalEnums::IS_DELETED],
                        ['conditions' => "staff_info_id in ({$staff_ids}) and group_id =" . $params['id']]
                    );
                    if ($manage_list_bool == false) {
                        throw new \Exception('财务审批分组manage_list更新失败，sql错误信息： ' . get_data_object_error_msg($db));
                    }
                    $manage_relate_bool = $db->updateAsDict(
                        (new StaffManageListRelateModel())->getSource(),
                        ['is_deleted' => GlobalEnums::IS_DELETED, 'updated_at' => date('Y-m-d H:i:s')],
                        ['conditions' => "staff_info_id in ({$staff_ids}) and group_id =" . $params['id']]
                    );

                    if ($manage_relate_bool == false) {
                        throw new \Exception('财务审批分组manage_relate_list更新失败，sql错误信息： ' . get_data_object_error_msg($db));
                    }
                }
            }
            $update_data = [
                'name'           => $params['name'],
                'department_ids' => $params['department_ids'],
                'remark'         => $params['remark'],
                'updated_at'     => date('Y-m-d H:i:s'),
                'updated_id'     => $uid,
            ];

            if ($group_model->save($update_data) == false) {
                throw new \Exception('财务审批分组更新失败，sql错误信息： ' . get_data_object_error_msg($group_model) . 'edit_group_new_data' . json_encode($update_data,
                        JSON_UNESCAPED_UNICODE));
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $db->rollback();
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('财务审批分工管理-editGroup-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     *处理分组数据
     * @param $items
     * @return array
     * */
    public function handelGroupItems($items)
    {
        $all_department = (new DepartmentRepository())->getAllDepartmentList();
        $all_department = array_column($all_department, 'name', 'id');

        foreach ($items as &$item) {
            $department_tmp = [];
            $department_ids = explode(',', $item['department_ids']);
            foreach ($department_ids as $department_id) {
                if (isset($all_department[$department_id])) {
                    $department_tmp[] = $all_department[$department_id];
                }
            }
            $item['department_names'] = implode(',', $department_tmp);
        }
        return $items;
    }

    /**
     *处理财务人员数据
     * @param $items
     * @return array
     * */
    public function handelStaffItems($items)
    {
        $all_department  = (new DepartmentRepository())->getAllDepartmentList();
        $group_info      = $this->getAllGroup();
        $job_title       = (new HrJobTitleRepository())->getJobTitleByIds(array_column($items, 'job_title'));
        $group_info      = array_column($group_info, 'name', 'id');
        $all_department  = array_column($all_department, 'name', 'id');
        $job_title       = array_column($job_title, 'job_name', 'id');
        $staff_state     = StaffInfoEnums::$staff_state;
        $configure_state = SettingEnums::$configure_state;
        foreach ($items as &$item) {
            $department_tmp = [];
            $department_ids = explode(',', $item['manage_department_ids']);
            foreach ($department_ids as $department_id) {
                if (isset($all_department[$department_id])) {
                    $department_tmp[] = $all_department[$department_id];
                }
            }
            $item['department_name'] = $all_department[$item['node_department_id']] ?? '';

            $item['manage_department_names'] = implode(',', $department_tmp);
            $item['group_name']              = $group_info[$item['group_id']] ?? '';
            $item['job_title']               = $job_title[$item['job_title']] ?? '';
            $item['configure_state_text']    = static::$t->_($configure_state[(is_null($item['configure_state']) ? 1 : $item['configure_state'])]);
            if ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                $item['state_text'] = static::$t->_($staff_state[StaffInfoEnums::STAFF_STATE_IN]);
            } else {
                if ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    $item['state_text'] = static::$t->_($staff_state[StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
                } else {
                    $item['state_text'] = static::$t->_($staff_state[$item['state']]);
                }
            }
        }
        return $items;
    }

    /**
     * 获取管辖部门
     * @param $id
     * @return array
     */
    public function getStaffManageDepartments($id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $manage_list_info = StaffManageListModel::findFirst([
                'conditions' => 'id = :id: and is_deleted = :is_deleted:',
                'bind'       => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            ]);
            if (empty($manage_list_info)) {
                throw new ValidationException(static::$t->_('manage_list_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $department_ids = explode(',', $manage_list_info->manage_department_ids);
            $list           = StaffManageListRelateModel::find([
                'conditions' => "department_id in ({department_ids:array}) and pid = :pid: and source_type = :source_type: and is_deleted = :is_deleted:",
                'bind'       => [
                    'department_ids' => $department_ids,
                    'pid'            => $id,
                    'source_type'    => SettingEnums::SOURCE_TYPE_1,
                    'is_deleted'     => GlobalEnums::IS_NO_DELETED,
                ],
            ])->toArray();
            $department_ids = array_values(array_unique(array_column($list, 'department_id')));

            //获取管辖部门及其上级部门
            $dept_ids             = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => [
                    'ids' => $department_ids,
                ],
                'columns'    => 'id,ancestry',
            ])->toArray();
            $ancestry             = array_column($dept_ids, 'ancestry', 'id');
            $ancestry_ids         = array_column($dept_ids, 'ancestry');
            $total_department_ids = array_values(array_unique(array_merge($department_ids, $ancestry_ids)));

            $department_info = SysDepartmentModel::find([
                'conditions' => 'id in ({ids:array})',
                'bind'       => [
                    'ids' => $total_department_ids,
                ],
                'columns'    => 'id,name',
            ])->toArray();

            $total_info = array_column($department_info, 'name', 'id');
            foreach ($list as &$v) {
                $v['name']          = $total_info[$v['department_id']] ?? '';
                $v['ancestry_name'] = $total_info[$v['department_id']] ?? '';
                $v['ancestry']      = $ancestry[$v['department_id']] ?? '';
                $v['ancestry_name'] = $total_info[$v['ancestry']] ?? '';
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('financial-manage-detail-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'items' => $list ?? [],
            ],
        ];
    }

    /**
     * 设置管辖部门
     * @param $params
     * @param $uid
     * @return array
     */
    public function editStaffManageRegion($params, $uid)
    {
        $code           = ErrCode::$SUCCESS;
        $message        = 'ok';
        $department_ids = array_column($params['data'], 'department_id');
        $department_ids = implode(',', $department_ids);

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //传id 的话是更新管辖部门
            if (!empty($params['id'])) {
                $manage_list_info = StaffManageListModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $params['id']],
                ]);

                if (empty($manage_list_info)) {
                    throw new ValidationException(static::$t->_('manage_list_id_not_exist'), ErrCode::$VALIDATE_ERROR);
                }

                $this->logger->info('edit_old_staff_manage_list' . json_encode($manage_list_info->toArray(),
                        JSON_UNESCAPED_UNICODE));

                $manage_relate_bool = $db->updateAsDict(
                    (new StaffManageListRelateModel())->getSource(),
                    ['is_deleted' => GlobalEnums::IS_DELETED, 'updated_at' => date('Y-m-d H:i:s')],
                    ['conditions' => "pid =" . $params['id']]
                );

                if ($manage_relate_bool == false) {
                    throw new \Exception('编辑管辖部门manage_list_relate删除失败，sql错误信息： ' . get_data_object_error_msg($db));
                }

                //申请管辖的部门id
                if (empty($params['data'])) {
                    $manage_list_info->is_deleted = GlobalEnums::IS_DELETED;
                }

                $manage_list_info->manage_department_ids = $department_ids;
                $manage_list_info->configure_state       = SettingEnums::CONFIGURE_STATE_2;
                $manage_list_info->updated_id            = $uid;
                $manage_list_info->updated_at            = date('Y-m-d H:i:s');
                $manage_lis_bool                         = $manage_list_info->save();
                if ($manage_lis_bool == false) {
                    throw new \Exception('编辑管辖部门manage_list 更新失败，sql错误信息： ' . get_data_object_error_msg($manage_list_info) . json_encode($manage_list_info->toArray(),
                            JSON_UNESCAPED_UNICODE));
                }
            } else {
                if (!empty($params['data'])) {
                    $manage_list_info = new StaffManageListModel();
                    $manage_list_data = [
                        'staff_info_id'         => $params['staff_info_id'],
                        'group_id'              => $params['group_id'],
                        'manage_department_ids' => $department_ids,
                        'created_at'            => date('Y-m-d H:i:s'),
                        'updated_at'            => date('Y-m-d H:i:s'),
                        'configure_state'       => SettingEnums::CONFIGURE_STATE_2,
                        'updated_id'            => $uid,

                    ];

                    $manage_lis_bool = $manage_list_info->i_create($manage_list_data);
                    if ($manage_lis_bool == false) {
                        throw new \Exception('编辑管辖部门manage_list 新增失败，sql错误信息： ' . get_data_object_error_msg($manage_list_info) . json_encode($manage_list_info->toArray(),
                                JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            $chain_list = [];
            foreach ($params['data'] as $v) {
                if ($v['is_include_sub'] == SettingEnums::IS_INCLUDE_SUB) {
                    //获取部门链
                    $dept_info = SysDepartmentModel::findFirst([
                        'conditions' => "id = :id: and deleted = :is_deleted:",
                        'bind'       => [
                            'id'         => $v['department_id'],
                            'is_deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ]);
                    if (empty($dept_info)) {
                        continue;
                    }
                    $chain      = SysDepartmentModel::find([
                        'conditions' => "ancestry_v3 like :chain: and deleted = :is_deleted:",
                        'bind'       => [
                            'chain'      => $dept_info->ancestry_v3 . '/%',
                            'is_deleted' => GlobalEnums::IS_NO_DELETED,

                        ],
                        'columns'    => 'id',
                    ])->toArray();
                    $chain_list = array_merge($chain_list, array_column($chain, 'id'));

                    if (!empty($chain_list)) {
                        foreach ($chain_list as $item) {
                            $model                 = new StaffManageListRelateModel();
                            $model->staff_info_id  = $manage_list_info->staff_info_id;
                            $model->group_id       = $manage_list_info->group_id;
                            $model->pid            = $manage_list_info->id;
                            $model->department_id  = $item;
                            $model->is_include_sub = SettingEnums::IS_NO_INCLUDE_SUB;
                            $model->source_type    = SettingEnums::SOURCE_TYPE_2;
                            $model->created_at     = date('Y-m-d H:i:s');
                            $model->updated_at     = date('Y-m-d H:i:s');
                            $model_bool            = $model->save();
                            if ($model_bool == false) {
                                throw new \Exception('编辑管辖部门manage_list_relate 新增失败，sql错误信息： ' . get_data_object_error_msg($model) . json_encode($model->toArray(),
                                        JSON_UNESCAPED_UNICODE));
                            }
                        }
                    }
                }

                $model                 = new StaffManageListRelateModel();
                $model->staff_info_id  = $manage_list_info->staff_info_id;
                $model->group_id       = $manage_list_info->group_id;
                $model->pid            = $manage_list_info->id;
                $model->department_id  = $v['department_id'];
                $model->is_include_sub = $v['is_include_sub'];
                $model->created_at     = date('Y-m-d H:i:s');
                $model->updated_at     = date('Y-m-d H:i:s');
                $model_bool            = $model->save();

                if ($model_bool == false) {
                    throw new \Exception('编辑管辖部门manage_list 新增失败，sql错误信息： ' . get_data_object_error_msg($model) . json_encode($model->toArray(),
                            JSON_UNESCAPED_UNICODE));
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $db->rollback();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $db->rollback();
            $this->logger->warning('financial-manage-edit-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 财务人员列表
     * @param $params
     * @return array
     */
    public function financialManageList($params)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = 'ok';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);


        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['s' => MiniHrStaffInfoModel::class]);
            $builder->leftJoin(ManageGroupDepartmentRelateModel::class, 's.node_department_id = gr.department_id',
                'gr');
            $builder->leftJoin(StaffManageListModel::class,
                's.staff_info_id = m.staff_info_id and gr.group_id = m.group_id and m.is_deleted =' . GlobalEnums::IS_NO_DELETED,
                'm');
            $builder = $this->getCondition($builder, $params);

            $count = (int)$builder->columns('COUNT(s.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count > 0) {
                $column_str = 's.staff_info_id,s.name,s.node_department_id,s.job_title,s.state,s.wait_leave_state,m.id,gr.group_id,m.manage_department_ids,m.configure_state';
                $builder->columns($column_str);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handelStaffItems($items);
            }
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('financial-staff-list-failed:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'items'      => $items ?? [],
                'pagination' => [
                    'current_page' => $params['pageNum'] ?? 1,
                    'per_page'     => $page_size,
                    'total_count'  => $count ?? 0,
                ],
            ],
        ];
    }

    public function getCondition($builder, $condition)
    {
        $staff_info_id   = empty($condition['staff_info_id']) ? '' : $condition['staff_info_id'];
        $group_id        = empty($condition['group_id']) ? '' : $condition['group_id'];
        $state           = empty($condition['state']) ? '' : $condition['state'];
        $configure_state = empty($condition['configure_state']) ? '' : $condition['configure_state'];


        if (!empty($staff_info_id)) {
            $builder->andWhere('s.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        if (!empty($state)) {
            switch ($state) {
                case StaffInfoEnums::STAFF_STATE_IN:

                    $builder->andWhere('s.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
                    $builder->andWhere('s.wait_leave_state  = :wait_leave_state:',
                        ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);

                    break;
                case StaffInfoEnums::STAFF_STATE_LEAVE:

                    $builder->andWhere('s.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_LEAVE]);

                    break;
                case StaffInfoEnums::STAFF_STATE_STOP:

                    $builder->andWhere('s.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_STOP]);

                    break;
                case StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE:

                    $builder->andWhere('s.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
                    $builder->andWhere('s.wait_leave_state  = :wait_leave_state:',
                        ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
                    break;
                default:
                    break;
            }
        } else {
            $builder->inWhere('s.state', [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]);
        }

        if ($configure_state == SettingEnums::CONFIGURE_STATE_2) {
            $builder->andWhere('m.configure_state = :configure_state:', ['configure_state' => $configure_state]);
        } else {
            if ($configure_state == SettingEnums::CONFIGURE_STATE_1) {
                $builder->andWhere('m.configure_state is null');
            }
        }

        if (!empty($group_id)) {
            $builder->andWhere('gr.group_id = :group_id:', ['group_id' => $group_id]);
        }

        $builder->andWhere('gr.department_id is not null');

        return $builder;
    }

    /**
     * 按照类型查询所有分组
     * @param int $type 1 财务部门分组  2 特殊组
     * @return array
     */
    public function getAllGroup(int $type = SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_FINANCE)
    {
        return StaffManageGroupModel::find([
            'columns'    => 'id,name,department_ids',
            'conditions' => 'is_deleted = :is_deleted: and type = :type:',
            'bind'       => ['is_deleted' => GlobalEnums::IS_NO_DELETED, 'type' => $type],
        ])->toArray();
    }

    /**
     * 财务人员搜索枚举
     * @param int $type 1 财务部门分组  2特殊组
     * @return array
     */
    public function getEnums($type = SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_FINANCE)
    {
        $configure_state = [];
        foreach (SettingEnums::$configure_state as $k => $v) {
            $configure_state[] = [
                'id'   => $k,
                'name' => static::$t->_($v),
            ];
        }

        return [
            'financial_group' => $this->getAllGroup($type),
            'configure_state' => $configure_state,
            'state'           => $this->getStaffStateEnum(),
        ];
    }

    /**
     * 员工状态
     * */
    function getStaffStateEnum()
    {
        foreach (StaffInfoEnums::$staff_state as $key => $value) {
            if ($key == StaffInfoEnums::STAFF_STATE_LEAVE) {
                continue;
            }
            $data[] = ['id' => (string)$key, 'name' => static::$t->_($value)];
        }
        return $data ?? [];
    }


    /**
     * @description:redis 队列 入  用于新增编辑部门和 工号变更
     * @param null
     * @return array
     */
    public function redisListAdd(array $params = []): array
    {
        $result = ['code' => ErrCode::$SUCCESS, 'message' => 'success!', 'data' => []];
        try {
            //类型
            $redis_data['type']          = $params['type'] ?? self::$redis_list_type_1;
            $redis_data['operator_id']   = $params['operator_id'] ?? '';   //操作人 id
            $redis_data['staff_info_id'] = $params['staff_info_id'] ?? ''; //被操作员 id
            $redis_data['dep_id']        = $params['dep_id'] ?? '';        //被操作部门 id
            $redis_data                  = json_encode($redis_data, JSON_UNESCAPED_UNICODE);
            //入队列
            $redis = $this->getDI()->get("redis");
            $redis->lpush(self::$redis_list_name, $redis_data);
            $this->logger->info("redis_list_add_lpush:" . $redis_data);
        } catch (\Exception $e) {
            $this->logger->warning(' exception:' . $e->getMessage() . $e->getTraceAsString());
            $result['code']    = ErrCode::$SYSTEM_ERROR;
            $result['message'] = $e->getMessage();
        }
        return $result;
    }

    /**
     * @description:消费任务
     * @param array
     * @return array
     */
    public function consumptionRedisList(array $params = [])
    {
        $result = ['code' => ErrCode::$SUCCESS, 'message' => 'success!', 'data' => []];
        try {
            //类型
            if (empty($params['type'])) {
                throw new \Exception('no  type !');
            }
            $operator_id = $params['operator_id'] ?? '10000'; //操作人 id
            //部门新增
            if ($params['type'] == self::$redis_list_type_1) {
                //获取部门
                $dep_id = $params['dep_id'] ?? '';
                if (empty($dep_id)) {
                    throw new \Exception('no  dep_id !');
                }
                //查询部门 主从延迟强制读主库
                $dept_info = wSysDepartmentModel::findFirst([
                    'conditions' => "id = :id:",
                    'bind'       => [
                        'id' => $dep_id,
                    ],
                ]);
                if (empty($dept_info)) {
                    throw new \Exception('no  dept_info !');
                }
                //找出父级的 id
                $ancestry_v3    = $dept_info->ancestry_v3;
                $ancestry_ids   = explode('/', $ancestry_v3);
                $ancestry_ids[] = $dep_id;
                $ancestry_ids   = array_values(array_unique($ancestry_ids));
                //下面准备刷部门
                //第一步先把原来来包含该部门的 人 和 即将要包含的人找出来  只需要找 计算出来的
                $refresh_staff_info_ids = StaffManageListRelateModel::find(
                    [
                        'conditions' => 'is_deleted = :is_deleted: and department_id in ({dep_ids:array})  and source_type = :source_type:',
                        'bind'       => [
                            'is_deleted'  => GlobalEnums::IS_NO_DELETED,
                            'dep_ids'     => $ancestry_ids,
                            'source_type' => SettingEnums::SOURCE_TYPE_1,
                        ],
                    ]
                )->toArray();
                //第二步 把这些人的部门刷新
                if (!empty($refresh_staff_info_ids)) {
                    $refresh_staff_info_ids = array_column($refresh_staff_info_ids, 'staff_info_id');
                    $refresh_staff_info_ids = array_values(array_unique($refresh_staff_info_ids));
                    $this->logger->info('重新计算子部门的员工 :' . json_encode($refresh_staff_info_ids) . ' 操作人=>' . $operator_id);
                    foreach ($refresh_staff_info_ids as $v) {
                        $this->refreshStaffDepartment($v);
                    }
                }
                $this->logger->info(' 重新计算子部门:' . json_encode($params));
            } else {
                if ($params['type'] == self::$redis_list_type_2) {
                    //查询特殊分组id，过滤特殊分组数据处理
                    $special_group_arr = $this->getAllGroup(SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_SPECIAL);
                    $special_group_arr = !empty($special_group_arr) ? array_column($special_group_arr, 'id') : [0];
                    //角色变更
                    //获取用户
                    $staff_info_id = $params['staff_info_id'] ?? '';
                    if (empty($staff_info_id)) {
                        throw new \Exception('no  staff_info_id !');
                    }
                    //查询用户是否存在管辖范围
                    $info = StaffManageListRelateModel::find([
                        'conditions' => 'staff_info_id = :staff_info_id: and  is_deleted = :is_deleted: and group_id not in ({group_id:array})',
                        'bind'       => [
                            'staff_info_id' => $staff_info_id,
                            'is_deleted'    => GlobalEnums::IS_NO_DELETED,
                            'group_id'      => $special_group_arr,
                        ],
                    ])->toArray();
                    if ($info) {
                        //存在的话 看该员工当前部门是否在管辖部门里
                        $staff_info = HrStaffInfoModel::findFirst([
                            'conditions' => "staff_info_id  = :staff_info_id:",
                            'bind'       => [
                                'staff_info_id' => $staff_info_id,
                            ],
                        ]);


                        $department_ids = array_column($info, 'department_id');

                        if (in_array($staff_info->node_departent_id, $department_ids)) {
                            $this->logger->info(' 员工变更信息:' . json_encode($params) . '用户当前部门在管辖范围不需变更管辖关系');
                        } else {
                            //当前部门不在管辖列表 清空 改员工管辖范围
                            $this->delManageDepartment($staff_info_id, $special_group_arr);
                        }
                    } else {
                        $this->logger->info('员工变更信息:' . json_encode($params) . ' 用户没有管辖范围');
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->warning(' exception:' . $e->getMessage() . $e->getTraceAsString());
            $result['code']    = ErrCode::$SYSTEM_ERROR;
            $result['message'] = $e->getMessage();
        }
        return $result;
    }

    /**
     * 刷新staff 子部门
     * @param $staff_info_id   用户id
     * @return array
     */
    public function refreshStaffDepartment($staff_info_id)
    {
        //开启事务
        $result = ['code' => ErrCode::$SUCCESS, 'message' => 'success!', 'data' => []];
        $db     = $this->getDI()->get('db_oa');

        $db->begin();
        try {
            //查询出选择的部门 并且勾选了包含子部门
            $v_dep_ids = StaffManageListRelateModel::find(
                [
                    'conditions' => "staff_info_id = :staff_info_id: and is_deleted = :is_deleted: and source_type = :source_type: and is_include_sub = :is_include_sub:",
                    'bind'       => [
                        'staff_info_id'  => $staff_info_id,
                        'is_deleted'     => GlobalEnums::IS_NO_DELETED,
                        'source_type'    => SettingEnums::SOURCE_TYPE_1,
                        'is_include_sub' => SettingEnums::IS_INCLUDE_SUB,
                    ],
                ]
            )->toArray();
            if (empty($v_dep_ids)) {
                //没有选择勾选的子部门的
                $db->rollback();
                return $result;
            }
            //删除计算的子部门
            $manage_list_bool = $db->updateAsDict(
                (new StaffManageListRelateModel())->getSource(),
                [
                    'is_deleted' => GlobalEnums::IS_DELETED,
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                [
                    'conditions' => 'is_deleted = 0 and  source_type = 1 and staff_info_id =' . $staff_info_id,
                ]
            );
            if ($manage_list_bool == false) {
                throw new \Exception('刷新子部门manage_list_relate 删除失败，sql错误信息： ' . get_data_object_error_msg($db));
            }

            $this->logger->info(' 重新计算子部门的员工和部门 :' . json_encode($staff_info_id) . ' ' . json_encode($v_dep_ids));
            //查询计算的子部门
            foreach ($v_dep_ids as $dep_id_val) {
                //获取部门链
                $dept_info = wSysDepartmentModel::findFirst([
                    'conditions' => "id = :id: and deleted = :deleted:",
                    'bind'       => [
                        'id'      => $dep_id_val['department_id'],
                        'deleted' => GlobalEnums::IS_NO_DELETED,
                    ],
                ]);
                if (empty($dept_info)) {
                    continue;
                }
                //查询子部门
                $chain       = wSysDepartmentModel::find([
                    'conditions' => "ancestry_v3 like :chain: and deleted = :is_deleted:",
                    'bind'       => [
                        'chain'      => $dept_info->ancestry_v3 . '/%',
                        'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'id',
                ])->toArray();
                $chain_list  = array_column($chain, 'id');
                $insert_list = [];
                if (!empty($chain_list)) {
                    foreach ($chain_list as $item) {
                        $insert_list[] = [
                            'staff_info_id'  => $dep_id_val['staff_info_id'],
                            'pid'            => $dep_id_val['pid'],
                            'group_id'       => $dep_id_val['group_id'],
                            'department_id'  => $item,
                            'is_include_sub' => SettingEnums::IS_NO_INCLUDE_SUB,
                            'source_type'    => SettingEnums::SOURCE_TYPE_2,
                            'created_at'     => date('Y-m-d H:i:s'),
                            'updated_at'     => date('Y-m-d H:i:s'),
                        ];
                    }
                }

                if (!empty($insert_list)) {
                    $model      = new StaffManageListRelateModel();
                    $model_bool = $model->batch_insert($insert_list);
                    if ($model_bool == false) {
                        throw new \Exception('刷新子部门manage_list_relate 入库失败，数据： ' . json_encode($insert_list,
                                JSON_UNESCAPED_UNICODE));
                    }
                }
            }
            $db->commit(); //提交事务
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->warning('job刷新子部门exception:' . $e->getMessage() . $e->getTraceAsString());
            $result['code']    = ErrCode::$SYSTEM_ERROR;
            $result['message'] = $e->getMessage();
        }
        return $result;
    }

    /**
     * 清空staff_id所管辖部门
     * @param int $staff_id 员工工号
     * @param array $special_group_arr 财务特殊分组数据
     * @return array
     */
    public function delManageDepartment(int $staff_id, array $special_group_arr)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $special_group_string = implode(',', $special_group_arr);
            $manage_list_bool     = $db->updateAsDict(
                (new StaffManageListModel())->getSource(),
                [
                    'is_deleted' => GlobalEnums::IS_DELETED,
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ],
                ['conditions' => "staff_info_id = {$staff_id} and group_id not in ({$special_group_string})"]
            );
            if ($manage_list_bool == false) {
                throw new \Exception($staff_id . 'staff_manage_list失败，sql错误信息： ' . get_data_object_error_msg($db));
            }
            $manage_relate_bool = $db->updateAsDict(
                (new StaffManageListRelateModel())->getSource(),
                [
                    'is_deleted' => GlobalEnums::IS_DELETED,
                    'updated_at' => date('Y-m-d H:i:s'),
                ],
                ['conditions' => "staff_info_id = {$staff_id} and group_id not in ({$special_group_string})"]
            );

            if ($manage_relate_bool == false) {
                throw new \Exception($staff_id . 'staff_manage_relate_list失败，sql错误信息：' . get_data_object_error_msg($db));
            }
            $this->logger->info('处理成功工号' . $staff_id);


            $db->commit();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $db->rollback();
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('工号' . $staff_id . '员工信息变更-清空管辖部门-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


}
