<?php

namespace App\Modules\Setting\Services;

use App\Modules\Common\Models\EnvModel;

class BaseService extends \App\Library\BaseService
{
    public static function getSettingAuthStaffId($code = '')
    {
        $authIds = EnvModel::getEnvByCode(!empty($code) ? $code : 'store_access_staff_id', '');

        return !empty($authIds) ? explode(',', $authIds) : [];
    }

    public static function getSettingFinancialAuthStaffId($code = '')
    {
        $auth_ids = EnvModel::getEnvByCode(!empty($code) ? $code : 'access_approval_groups_id', '');

        return !empty($auth_ids) ? explode(',', $auth_ids) : [];
    }

    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }
}
