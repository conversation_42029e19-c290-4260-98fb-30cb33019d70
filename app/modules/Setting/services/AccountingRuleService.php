<?php
/**
 * 子公司 - 会计规则配置
 */

namespace App\Modules\Setting\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\AccountingSubjectsModel;
use App\Models\oa\AccountingRuleModel;
use App\Models\oa\AccountingRuleDepartmentListModel;
use App\Models\oa\BudgetObjectModel;
use App\Models\oa\BudgetObjectProductModel;
use App\Repository\oa\AccountingRuleDepartmentListRepository;
use App\Repository\oa\AccountingRuleRepository;
use App\Repository\oa\AccountingSubjectsRepository;
use App\Repository\oa\BudgetObjectProductRepository;
use App\Repository\oa\BudgetObjectRepository;
use App\Repository\DepartmentRepository;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

class AccountingRuleService extends BaseService
{
    // 批量新增最多限制条数
    const BATCH_ADD_MAX_TOTAL = 100;

    // 参与校验的基础数据
    protected $budget_object_list       = null; // 预算科目列表 [科目名称 => [id => 1, level_code => 001, object_name => xxx]]
    protected $product_list             = null; // 预算科目明细列表 [科目编号 => [明细名称 => 明细编号]]
    protected $accounting_subjects_list = null; // 会计科目列表 [科目名称 => 科目id]
    protected $dept_list                = null; // 系统部门列表 [部门id => 部门链]
    protected $organization_type_map    = null; // 费用所属网点/总部 [枚举名称 => 枚举值]
    protected $organization_type_enums  = null; // 费用所属网点/总部 所有枚举值 [枚举值]

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 规则 新增/编辑提交参数的公共校验
     *
     * @param array $params
     * @param bool $is_update
     * @throws ValidationException
     */
    public static function saveRuleCommonParamsValidation(array $params, bool $is_update = false)
    {
        $params['organization_type'] = $params['organization_type'] == '' ? (int)$params['organization_type'] : $params['organization_type'];
        $validation                  = [
            'budget_id'                              => 'Required|IntGt:0|>>>:param error[budget_id]',
            'product_no'                             => 'Required|StrLenGeLe:0,10|>>>:param error[product_no]',
            'organization_type'                      => 'Required|IntIn:0,1,2|>>>:param error[organization_type]',
            'cost_department_list'                   => 'Required|Arr|>>>:param error[cost_department_list]',
            'cost_department_list[*].department_id'  => 'Required|IntGt:0|>>>:param error[department_id]',
            'cost_department_list[*].is_include_sub' => 'Required|IntIn:0,1|>>>:param error[is_include_sub]',
            'subjects_id'                            => 'Required|IntGt:0|>>>:param error[subjects_id]',
        ];

        if ($is_update) {
            $validation['id'] = 'Required|IntGt:0|>>>:param error[id]';
        }

        Validation::validate($params, $validation);
    }

    /**
     * 获取该模块静态枚举配置
     *
     * @return array
     */
    public function getStaticEnums()
    {
        $return['organization_type_map'] = [];

        // 1. 所属总部/网点
        $organization_type_map = Enums::$organization_type_map;
        foreach ($organization_type_map as $key => $value) {
            $return['organization_type_map'][] = [
                'value' => (string)$key,
                'label' => $value,
            ];
        }
        return $return;
    }

    /**
     * 会计科目搜素列表
     *
     * @param string $keyword
     * @return array
     */
    public function getAccountingSubjectsList(string $keyword)
    {
        return AccountingSubjectsRepository::getInstance(static::$language)->getListByKeyword($keyword,
            GlobalEnums::DEFAULT_PAGE_SIZE);
    }

    /**
     * 规则配置列表
     *
     * @param array $params
     * @return array
     */
    public function getRuleList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => (int)$page_num,
                'per_page'     => (int)$page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['rule' => AccountingRuleModel::class]);
            $builder->leftjoin(BudgetObjectModel::class, 'rule.budget_object_id = budget.id', 'budget');
            $builder->leftjoin(BudgetObjectProductModel::class, 'rule.budget_object_product_no = budget_product.pno',
                'budget_product');
            $builder->leftjoin(AccountingSubjectsModel::class, 'rule.accounting_subjects_id = subjects.id', 'subjects');
            $builder->leftjoin(AccountingRuleDepartmentListModel::class, 'rule.id = rule_dept_rel.accounting_rule_id',
                'rule_dept_rel');

            // 组合条件搜索
            $budget_name_field = get_lang_field_name('name_', static::$language, 'th', 'cn');
            if (!empty($params['budget_name'])) {
                $builder->andWhere("budget.{$budget_name_field} LIKE :budget_name:",
                    ['budget_name' => "%{$params['budget_name']}%"]);
            }

            if (!empty($params['product_name'])) {
                $builder->andWhere("budget_product.{$budget_name_field} LIKE :budget_product_name:",
                    ['budget_product_name' => "%{$params['product_name']}%"]);
            }

            if (!empty($params['subjects_id'])) {
                $builder->andWhere('subjects.id = :subjects_id:', ['subjects_id' => $params['subjects_id']]);
            }

            if (!empty($params['department_id'])) {
                // 当前部门 + 所有组织(空配置) + 上级组织 且 含子部门
                $dept_sql = 'rule_dept_rel.department_id = :department_id: OR rule_dept_rel.id IS NULL';
                $bind     = ['department_id' => $params['department_id']];

                // 获取所选部门的上级部门
                $dept_info   = (new DepartmentRepository())->getDepartmentDetail($params['department_id']);
                $ancestry_v3 = str_replace('/' . $params['department_id'], '', $dept_info['ancestry_v3']);
                if (!empty($ancestry_v3)) {
                    $ancestry_v3            = str_replace('/', ',', $ancestry_v3);
                    $dept_sql               .= ' OR (rule_dept_rel.department_id IN (:ancestry_ids:) AND rule_dept_rel.is_include_sub = :is_include_sub:)';
                    $bind['ancestry_ids']   = $ancestry_v3;
                    $bind['is_include_sub'] = SettingEnums::IS_INCLUDE_SUB;
                }

                $builder->andWhere($dept_sql, $bind);
            }

            // 总记录数
            $count = (int)$builder->columns('COUNT(DISTINCT(rule.id)) AS total')->getQuery()->getSingleResult()->total;
            $items = [];
            if ($count) {
                $subjects_name_field = get_lang_field_name('subjects_name_', static::$language, 'local', 'zh');
                $columns             = [
                    'rule.id',
                    'rule.organization_type',
                    'rule.cost_department_ids',
                    "budget.{$budget_name_field} AS budget_name",
                    "budget.is_delete AS budget_is_deleted",
                    "budget_product.{$budget_name_field} AS product_name",
                    "budget_product.is_delete AS product_is_deleted",
                    'subjects.subjects_code AS subjects_code',
                    "subjects.{$subjects_name_field} AS subjects_name",
                    'subjects.is_deleted AS subjects_is_deleted',
                ];

                $builder->columns($columns);
                $builder->groupBy('rule.id');
                $builder->orderBy('rule.id DESC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();

                // 获取费用部门信息
                $all_cost_dept_ids = explode(',', implode(',', array_column($items, 'cost_department_ids')));
                $all_cost_dept_ids = (new DepartmentRepository())->getDepartmentByIds($all_cost_dept_ids, 2);

                $organization_type_map = Enums::$organization_type_map;
                foreach ($items as &$val) {
                    // 明细空处理
                    $val['product_name']       = $val['product_name'] ?? '';
                    $val['product_is_deleted'] = $val['product_is_deleted'] ?? (string)GlobalEnums::IS_NO_DELETED;

                    // 费用所属总部/网点处理
                    if (!empty($val['organization_type'])) {
                        $val['organization_type_label'] = $organization_type_map[$val['organization_type']];
                    } else {
                        $val['organization_type']       = '';
                        $val['organization_type_label'] = '';
                    }

                    // 获取费用部门名称 和 删除状态
                    $dept_ids  = array_filter(explode(',', $val['cost_department_ids']));
                    $dept_item = [];
                    foreach ($dept_ids as $id) {
                        $dept_info = $all_cost_dept_ids[$id] ?? [];
                        if (empty($dept_info)) {
                            continue;
                        }

                        $dept_item[] = [
                            'department_id' => $id,
                            'name'          => $dept_info['name'],
                            'is_deleted'    => $dept_info['deleted'],
                        ];
                    }

                    $val['cost_department_list'] = $dept_item;
                    unset($val['cost_department_ids']);
                }
            }

            $data['items']                     = $items;
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则列表异常:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 提取Excel数据
     *
     * @param object $excel_file
     * @return array
     * @throws ValidationException
     */
    protected function getExcelData(object $excel_file)
    {
        $config = ['path' => ''];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 读取文件
        $excel_data = $excel->openFile($excel_file->getTempName())
            ->openSheet()
            ->setType([
                0 => \Vtiful\Kernel\Excel::TYPE_STRING,
                1 => \Vtiful\Kernel\Excel::TYPE_STRING,
                2 => \Vtiful\Kernel\Excel::TYPE_STRING,
                3 => \Vtiful\Kernel\Excel::TYPE_STRING,
                4 => \Vtiful\Kernel\Excel::TYPE_STRING,
            ])
            ->getSheetData();

        // 提取Excel Header: 固定文案
        $excel_header_columns = [
            0 => array_shift($excel_data),// 首行中文
            1 => array_shift($excel_data) // 次行英文
        ];

        // 过滤Excel中空白行
        foreach ($excel_data as $k => &$v) {
            $v = trim_array(array_slice($v, 0, 5));
            if (empty(array_filter($v))) {
                unset($excel_data[$k]);
                continue;
            }

            // 第5列导出结果初始化为空
            $v[5] = '';
        }

        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('excel_file_empty'), ErrCode::$VALIDATE_ERROR);
        }

        if (count($excel_data) > static::BATCH_ADD_MAX_TOTAL) {
            throw new ValidationException(static::$t->_('accounting_rule_batch_upload_max_limit',
                ['max_total' => static::BATCH_ADD_MAX_TOTAL]), ErrCode::$VALIDATE_ERROR);
        }

        return [
            'excel_header_columns' => $excel_header_columns,
            'excel_data'           => array_values($excel_data),
        ];
    }

    /**
     * 返回Excel文件
     *
     * @param array $excel_header_columns Excel 头部 首行中文, 次行英文
     * @param array $excel_data Excel校验后的数据
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    protected function getExcelFileInfo(array $excel_header_columns, array $excel_data)
    {
        $file_name   = static::$t->_('accounting_rule_batch_upload_file_name') . '_' . date('YmdHis') . '.xlsx';
        $file_header = array_shift($excel_header_columns);
        $file_data   = array_merge($excel_header_columns, $excel_data);
        $return      = $this->exportExcel($file_header, $file_data, $file_name);

        return [
            'file_name' => $file_name,
            'file_url'  => $return['data'],
        ];
    }

    /**
     * 初始化参与校验的基础数据
     */
    protected function initBaseValidationData()
    {
        // 预算科目
        if (is_null($this->budget_object_list)) {
            $budget_object_list       = BudgetObjectRepository::getInstance(static::$language)->getListByName('',
                'id, level_code');
            $this->budget_object_list = array_column($budget_object_list, null, 'object_name');
            unset($budget_object_list);
        }

        // 预算科目下的明细
        if (is_null($this->product_list)) {
            $product_list  = BudgetObjectProductRepository::getInstance(static::$language)->getAllValidList();
            $_product_list = [];
            foreach ($product_list as $p) {
                $_product_list[$p['object_code']][$p['product_name']] = $p['product_no'];
            }
            $this->product_list = $_product_list;
            unset($_product_list, $product_list);
        }

        // 会计科目
        if (is_null($this->accounting_subjects_list)) {
            $accounting_subjects_list       = AccountingSubjectsRepository::getInstance(static::$language)->getListByKeyword();
            $this->accounting_subjects_list = array_column($accounting_subjects_list, 'id', 'subjects_name');
            unset($accounting_subjects_list);
        }

        // 所有部门
        if (is_null($this->dept_list)) {
            $dept_list       = (new DepartmentRepository())->getDepartmentList();
            $this->dept_list = array_column($dept_list, 'ancestry_v3', 'id');
            unset($dept_list);
        }

        // 所属网点/总部
        if (is_null($this->organization_type_map)) {
            $organization_type_map         = array_map(function ($v) {
                return strtolower($v);
            }, Enums::$organization_type_map);
            $this->organization_type_map   = array_flip($organization_type_map);
            $this->organization_type_enums = array_values($this->organization_type_map);
            unset($organization_type_map);
        }
    }

    /**
     * 构造N种场景下, 用作匹配key的库
     *
     * @param array $keys_lib 构建中的keys集合
     * @param array $options 待参与构建keys集合的选项值
     */
    protected function generateMatchedKeysLib(array &$keys_lib, array $options = [])
    {
        $result = [];
        foreach ($keys_lib as $lib_v) {
            foreach ($options as $v) {
                $result[] = $lib_v . '_' . $v;
            }
        }

        $keys_lib = $result;
    }


    /**
     * 给key追加会计科目值
     *
     * @param array $keys_lib 构建中的keys集合
     * @param string $subject_id
     */
    protected function appendSubjectsIdToKeysLib(array &$keys_lib, string $subject_id)
    {
        $result = [];
        foreach ($keys_lib as $lib_k) {
            $result[$lib_k] = $subject_id;
        }

        $keys_lib = $result;
    }

    /**
     * 获取指定预算科目的会计规则及规则关联的部门
     *
     * @param array $budget_ids
     * @return array
     */
    protected function getExistRuleListByBudgetIds(array $budget_ids)
    {
        // 1. 查找预算科目和会计科目已有的规则
        $exist_rules = AccountingRuleRepository::getInstance()->getRuleListByBudgetIds($budget_ids);
        if (empty($exist_rules)) {
            return [];
        }
        $exist_rules = array_column($exist_rules, null, 'id');

        // 找规则关联的部门明细
        $rule_dept_item = AccountingRuleDepartmentListRepository::getInstance()->getListByRuleIds(array_column($exist_rules,
            'id'));
        foreach ($rule_dept_item as $item) {
            $exist_rules[$item['accounting_rule_id']]['cost_department_list'][] = [
                'department_id'  => $item['department_id'],
                'is_include_sub' => $item['is_include_sub'],
            ];
        }

        return $exist_rules;
    }

    /**
     * 将规则各项按已有规则生成N种场景 对应的key库, 与待校验规则详做匹配
     *
     * @param array $rule_list
     * @param array $ignore_rule_ids 忽略的规则ID, 即要生成key的规则ID, 在该参数中时, 则该规则的各项无需生成key
     * @param bool $is_append_subjects_id 是否 将会计科目赋值给唯一的规则key
     * @return array
     */
    protected function generateCommonRuleKeysLib(
        array $rule_list,
        array $ignore_rule_ids = [],
        bool $is_append_subjects_id = false
    ) {
        // 会计规则所有场景的key
        $all_keys_lib = [];

        if (empty($rule_list)) {
            return $all_keys_lib;
        }

        // 初始化基本数据
        $this->initBaseValidationData();

        static $budget_object_id_code_map = null;
        if (is_null($budget_object_id_code_map)) {
            $budget_object_id_code_map = array_column($this->budget_object_list, 'level_code', 'id');
        }

        // 根据N场景, 生成待匹配的keys库
        // 每个key构成：科目 + 明细 + 部门ID + 费用所属网点/类型 => 会计科目ID
        foreach ($rule_list as $rule) {
            // 跳过无需生成key的规则配置
            if (in_array($rule['id'], $ignore_rule_ids)) {
                continue;
            }

            // 初始化当前规则key的参与项
            $per_rule_lib = [$rule['budget_id']];

            // 预算明细
            $lib_product_nos = [$rule['product_no']];
            if (empty($rule['product_no'])) {
                // 取科目下所有明细
                $lib_product_nos = $this->product_list[$budget_object_id_code_map[$rule['budget_id']]] ?? [];
                $lib_product_nos = !empty($lib_product_nos) ? array_merge(array_values($lib_product_nos), ['']) : [''];
            }

            $this->generateMatchedKeysLib($per_rule_lib, $lib_product_nos);

            // 部门
            $cost_dept_list = $rule['cost_department_list'] ?? [];
            if (!empty($cost_dept_list)) {
                $lib_dept_ids = [];
                foreach ($cost_dept_list as $dept) {
                    $lib_dept_ids = array_merge($lib_dept_ids, [$dept['department_id']]);

                    // 含子部门: 取该部门下级部门
                    if ($dept['is_include_sub']) {
                        $ancestry_v3  = $this->dept_list[$dept['department_id']];
                        $sub_dept_ids = get_sub_department_ids($ancestry_v3, $this->dept_list);
                        $lib_dept_ids = array_merge($lib_dept_ids, $sub_dept_ids);
                    }
                }
            } else {
                // 所有部门
                $lib_dept_ids = array_merge(array_keys($this->dept_list), ['']);
            }

            $this->generateMatchedKeysLib($per_rule_lib, $lib_dept_ids);

            // 费用所属网点/类型
            $lib_org_types = $rule['organization_type'] == 0 ? array_merge($this->organization_type_enums,
                [$rule['organization_type']]) : [$rule['organization_type']];
            $this->generateMatchedKeysLib($per_rule_lib, $lib_org_types);

            // 给key补充val: 会计科目ID
            if ($is_append_subjects_id) {
                $this->appendSubjectsIdToKeysLib($per_rule_lib, $rule['subjects_id']);
            }

            $all_keys_lib[$rule['id'] ?? 0] = $per_rule_lib;
        }

        return $all_keys_lib;
    }

    /**
     * Excel数据校验
     *
     * @param array $excel_data
     * @return array
     */
    protected function excelDataValidation(array $excel_data)
    {
        // 校验结果的错误统计
        $validation_error_count = 0;

        // 参与校验的基础数据
        $this->initBaseValidationData();

        // Excel 数据自身校验
        // 1. 各项是否存在的初步校验
        // 收集通过初步校验的行(数组索引与行索引对应)
        $db_data = [];
        foreach ($excel_data as $index => &$row) {
            // 必填项: 预算科目
            if (empty($row[0])) {
                $validation_error_count++;
                $row[5] = static::$t->_('accounting_rule_upload_error_1');
                continue;
            } else {
                $budget_info = $this->budget_object_list[$row[0]] ?? [];
                if (empty($budget_info)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_1');
                    continue;
                }
            }

            // 必填项: 会计科目
            if (empty($row[4])) {
                $validation_error_count++;
                $row[5] = static::$t->_('accounting_rule_upload_error_6');
                continue;
            } else {
                $subjects_id = $this->accounting_subjects_list[$row[4]] ?? '';
                if (empty($subjects_id)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_6');
                    continue;
                }
            }

            // 非必填: 科目明细验证
            if (!empty($row[1])) {
                // 明细是否存在
                $budget_product_list = $this->product_list[$budget_info['level_code']] ?? [];
                $product_no          = $budget_product_list[$row[1]] ?? '';
                if (empty($product_no)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_2');
                    continue;
                }
            } else {
                // 取科目下所有明细
                $product_no = '';
            }

            // 非必填: 费用部门是否存在
            $dept_id = $row[2];
            if (!empty($dept_id)) {
                // 存在非数字, 格式不合法
                if (preg_match('/[^0-9]+/', $dept_id)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_7');
                    continue;
                }

                // 父链
                $ancestry_v3 = $this->dept_list[$dept_id] ?? '';
                if (empty($ancestry_v3)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_3');
                    continue;
                }

                $cost_department_list = [
                    'department_id'  => $dept_id,
                    'is_include_sub' => SettingEnums::IS_INCLUDE_SUB,
                ];
            } else {
                $cost_department_list = [];
            }

            // 非必填: 费用所属网点/总部校验
            if (!empty($row[3])) {
                $organization_type = $this->organization_type_map[strtolower($row[3])] ?? '';
                if (empty($organization_type)) {
                    $validation_error_count++;
                    $row[5] = static::$t->_('accounting_rule_upload_error_5');
                    continue;
                }
            } else {
                $organization_type = 0;
            }

            // 第5列导入结果初始化为成功
            $row[5] = static::$t->_('excel_result_success');

            // 构造可入库的规则数据(但仍需继续校验规则是否有重复的情况)
            $db_data[$index] = [
                'id'                   => $index,            // 虚拟ID, 定位Excel行号用
                'budget_id'            => $budget_info['id'],// 预算科目ID
                'product_no'           => $product_no,       // 预算明细编号
                'cost_department_id'   => $dept_id,          // 费用部门ID
                'cost_department_list' => $cost_department_list ? [$cost_department_list] : [],
                'organization_type'    => $organization_type,// 所属网点/总部枚举
                'subjects_id'          => $subjects_id,      // 会计科目ID
            ];
        }

        // 将Excel中暂无初步问题的项生成下一步待匹配校验的keys
        $excel_keys_lib = $this->generateCommonRuleKeysLib($db_data, [], false);


        // 2. Excel 各行之间的规则重复性校验
        $excel_pass_keys_lib   = [];
        $excel_pass_budget_ids = [];
        foreach ($excel_keys_lib as $row_index => $row_v) {
            $curr_row_is_error = false;
            foreach ($excel_keys_lib as $lib_index => $lib_v) {
                // 同一行, 无需比较, 直接跳过
                if ($row_index == $lib_index) {
                    continue;
                }

                // 与其他行中匹配对比, 已有配置, 跳出当前行
                // 求不同行之间规则的交集, 有交集, 则说明规则有重复
                $repeated_rules = array_intersect($row_v, $lib_v);
                if (!empty($repeated_rules)) {
                    $curr_row_is_error = true;
                    $validation_error_count++;
                    // error_row + 3, 是与Excel左侧边栏序号对齐, Sheet前两行为中英文标题
                    $excel_data[$row_index][5] = static::$t->_('accounting_rule_upload_error_8',
                        ['error_row' => $lib_index + 3]);
                    break;
                }
            }

            // 该行Excel校验通过, 继续与库中规则校验
            if ($curr_row_is_error === false) {
                $excel_pass_keys_lib[$row_index] = $row_v;
                $excel_pass_budget_ids[]         = $db_data[$row_index]['budget_id'];
            }
        }

        // 3. Excel 自检通过的行数据, 继续与库中已有的规则作校验
        if (!empty($excel_pass_budget_ids)) {
            $exist_rule_list = $this->getExistRuleListByBudgetIds($excel_pass_budget_ids);

            $exist_rule_keys_lib = $this->generateCommonRuleKeysLib($exist_rule_list, [], false);

            foreach ($excel_pass_keys_lib as $row_index => $row_v) {
                foreach ($exist_rule_keys_lib as $lib_id => $lib_v) {
                    // Excel 行 与库中已有规则匹配, 发现已配置, 则跳出当前行
                    $repeated_rules = array_intersect($row_v, $lib_v);
                    if (!empty($repeated_rules)) {
                        $validation_error_count++;
                        $excel_data[$row_index][5] = static::$t->_('accounting_rule_upload_error_9',
                            ['rule_id' => $lib_id]);
                        break;
                    }
                }
            }
        }

        return [
            'error_count' => $validation_error_count,
            'excel_data'  => $excel_data,
            'db_data'     => $db_data,
        ];
    }

    /**
     * Excel 批量添加
     *
     * @param object $excel_file
     * @param array $user
     * @return array
     * @throws GuzzleException
     */
    public function batchAdd(object $excel_file, array $user)
    {
        ini_set('memory_limit', '512M');

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'file_name'     => '',// 返回的文件名称
            'file_url'      => '',// 返回的文件地址
            'total_count'   => 0, // 数据总条数
            'success_count' => 0, // 成功数
            'failure_count' => 0  // 失败数
        ];

        try {
            // 获取Excel数据
            $excel_content       = $this->getExcelData($excel_file);
            $data['total_count'] = count($excel_content['excel_data']);

            // Excel 数据校验
            $validation_result     = $this->excelDataValidation($excel_content['excel_data']);
            $data['failure_count'] = $validation_result['error_count'];
            $data['success_count'] = $data['total_count'] - $validation_result['error_count'];

            // 校验通过, 批量入库
            if ($validation_result['error_count'] == 0) {
                $db = $this->getDI()->get('db_oa');
                $db->begin();

                // 规则关联的部门明细Model
                $rule_dept_model = new AccountingRuleDepartmentListModel();

                // 规则主数据
                foreach ($validation_result['db_data'] as $val) {
                    // 写入规则表
                    $rule_model          = new AccountingRuleModel();
                    $cost_department_ids = !empty($val['cost_department_list']) ? implode(',',
                        array_column($val['cost_department_list'], 'department_id')) : '';

                    $add_data = [
                        'budget_object_id'         => $val['budget_id'],
                        'budget_object_product_no' => $val['product_no'],
                        'cost_department_ids'      => $cost_department_ids,
                        'organization_type'        => $val['organization_type'],
                        'accounting_subjects_id'   => $val['subjects_id'],
                        'created_id'               => $user['id'],
                        'created_at'               => date('Y-m-d H:i:s'),
                        'updated_id'               => $user['id'],
                        'updated_at'               => date('Y-m-d H:i:s'),
                    ];
                    if ($rule_model->i_create($add_data) === false) {
                        throw new BusinessException('批量导入-规则主表添加失败, 原因可能是:' . get_data_object_error_msg($rule_model) . '; 数据:' . json_encode($add_data,
                                JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                    }

                    // 写入规则关联的部门明细表
                    if (!empty($cost_department_ids)) {
                        $batch_rule_dept_data = [];
                        foreach ($val['cost_department_list'] as $dept) {
                            $batch_rule_dept_data[] = [
                                'accounting_rule_id' => $rule_model->id,
                                'department_id'      => $dept['department_id'],
                                'is_include_sub'     => $dept['is_include_sub'],
                                'created_id'         => $user['id'],
                                'created_at'         => date('Y-m-d H:i:s'),
                            ];
                        }

                        if ($rule_dept_model->batch_insert($batch_rule_dept_data) === false) {
                            throw new BusinessException('批量导入-规则关联部门明细添加失败; 数据:' . json_encode($batch_rule_dept_data,
                                    JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                        }
                    }
                }

                $db->commit();
            }

            // 返回结果文件
            $file_info         = $this->getExcelFileInfo($excel_content['excel_header_columns'],
                $validation_result['excel_data']);
            $data['file_name'] = $file_info['file_name'];
            $data['file_url']  = $file_info['file_url'];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('会计规则配置-批量上传异常: ' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则配置-批量上传异常: ' . $e->getMessage());
        }

        // 启用了事务 且 处理失败, 则回滚数据操作
        if (isset($db) && $code != ErrCode::$SUCCESS) {
            $db->rollback();
        }

        $this->logger->info('会计规则批量上传-响应结果' . json_encode($data, JSON_UNESCAPED_UNICODE));

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 规则 新增提交 和 编辑提交的公共逻辑校验
     *
     * @param array $params
     * @param bool $is_update
     * @return bool
     * @throws ValidationException
     */
    protected function saveRuleCommonLogicValidation(array $params, bool $is_update = false)
    {
        // 必填项: 预算科目, 非软删态
        $budget_model = BudgetObjectRepository::getInstance()->getOneById($params['budget_id']);
        if (empty($budget_model)) {
            throw new ValidationException(static::$t->_('accounting_rule_save_error_1',
                ['budget_id' => $params['budget_id']]), ErrCode::$VALIDATE_ERROR);
        }

        // 必填项: 会计科目, 非软删态
        $subjects_model = AccountingSubjectsRepository::getInstance(static::$language)->getOneById($params['subjects_id']);
        if (empty($subjects_model)) {
            throw new ValidationException(static::$t->_('accounting_rule_save_error_4',
                ['subjects_id' => $params['subjects_id']]), ErrCode::$VALIDATE_ERROR);
        }

        // 明细有值时, 非软删态 且 已启用
        if (!empty($params['product_no'])) {
            $budget_product_model = BudgetObjectProductRepository::getInstance()->getOneByObjectCodeAndPno($budget_model->level_code,
                $params['product_no']);
            if (empty($budget_product_model)) {
                throw new ValidationException(static::$t->_('accounting_rule_save_error_2',
                    ['p_no' => $params['product_no']]), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 费用部门校验
        $this->costDepartmentListValidation($params['cost_department_list']);

        // 构建待生成key库的数据结构
        if ($is_update) {
            $ignore_rule_ids = [$params['id']];
        } else {
            $params['id']    = 0;
            $ignore_rule_ids = [];
        }

        // 配置规则验证
        // 待提交规则的N种场景key
        $waiting_submit_rule_keys = $this->generateCommonRuleKeysLib([$params], [], false);

        // 库里已有的key
        $exist_rule_list = $this->getExistRuleListByBudgetIds([$params['budget_id']]);
        $exist_keys_lib  = $this->generateCommonRuleKeysLib($exist_rule_list, $ignore_rule_ids, false);
        foreach ($waiting_submit_rule_keys as $submit_k) {
            foreach ($exist_keys_lib as $rule_id => $lib_k) {
                // 求提交的规则k与库里的规则k的交集, 有交集, 则规则重复; 否则, 不重复, 校验通过
                $repeated_rules = array_intersect($submit_k, $lib_k);
                if (!empty($repeated_rules)) {
                    throw new ValidationException(static::$t->_('accounting_rule_save_error_5',
                        ['rule_id' => $rule_id]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        return true;
    }

    /**
     * 费用部门校验
     *
     * @param array $department_list
     * @return bool
     * @throws ValidationException
     */
    protected function costDepartmentListValidation(array $department_list = [])
    {
        if (empty($department_list)) {
            return true;
        }

        // 勾选的部门id
        $dept_ids = array_column($department_list, 'department_id');

        // 1. 是否有同一部门重复提交的
        if (count(array_unique($dept_ids)) < count($dept_ids)) {
            throw new ValidationException(static::$t->_('accounting_rule_save_error_6'), ErrCode::$VALIDATE_ERROR);
        }

        // 2. 部门是否有被删的
        $exist_dept_item = (new DepartmentRepository())->getDepartmentByIds($dept_ids, GlobalEnums::IS_NO_DELETED);
        $exist_dept_item = array_column($exist_dept_item, 'ancestry_v3', 'id');
        $diff_dept_ids   = array_diff($dept_ids, array_keys($exist_dept_item));
        if (!empty($diff_dept_ids)) {
            throw new ValidationException(static::$t->_('accounting_rule_save_error_3',
                ['cost_dept_ids' => implode(',', $diff_dept_ids)]), ErrCode::$VALIDATE_ERROR);
        }

        // 3. 没有包含子部门的情况: 若均不包含子部门, 则无需继续校验部门是否嵌套
        $is_include_sub_item = array_unique(array_column($department_list, 'is_include_sub'));
        if (!in_array(SettingEnums::IS_INCLUDE_SUB, $is_include_sub_item)) {
            return true;
        }

        // 4. 有包含子部门的情况: 验证勾选的部门是否有嵌套(比如: 勾选了某一级部门且含子部门, 但同时也勾选了该一级部门下的其他层级部门)
        // 找勾选部门的父部门
        foreach ($department_list as &$dept) {
            $dept['ancestry_v3_item'] = explode('/', $exist_dept_item[$dept['department_id']]);
        }

        // 对比部门之间, 是否 有在一个部门链的情况
        foreach ($department_list as $curr_dept) {
            foreach ($department_list as $other_dept) {
                // 同一个部门, 无需比对, 略过
                if ($curr_dept['department_id'] == $other_dept['department_id']) {
                    continue;
                }

                // 若当前部门含子部门, 则确认当前部门是否是其他部门的父部门, 即确定其他部门是否是当前部门的子部门
                if ($curr_dept['is_include_sub'] && in_array($curr_dept['department_id'],
                        $other_dept['ancestry_v3_item'])) {
                    $include_dept_ids = $curr_dept['department_id'] . '->' . $other_dept['department_id'];
                    throw new ValidationException(static::$t->_('accounting_rule_save_error_7',
                        ['dept_id' => $include_dept_ids]), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        return true;
    }

    /**
     * 规则添加
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function addRule(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 入库前校验
            $this->saveRuleCommonLogicValidation($params, false);

            // 费用部门列表
            $cost_department_list = $params['cost_department_list'];

            // 写入规则表
            $cost_department_ids = !empty($cost_department_list) ? implode(',',
                array_column($cost_department_list, 'department_id')) : '';
            $rule_model          = new AccountingRuleModel();
            $add_data            = [
                'budget_object_id'         => $params['budget_id'],
                'budget_object_product_no' => $params['product_no'],
                'cost_department_ids'      => $cost_department_ids,
                'organization_type'        => $params['organization_type'],
                'accounting_subjects_id'   => $params['subjects_id'],
                'created_id'               => $user['id'],
                'created_at'               => date('Y-m-d H:i:s'),
                'updated_id'               => $user['id'],
                'updated_at'               => date('Y-m-d H:i:s'),
            ];
            if ($rule_model->i_create($add_data) === false) {
                throw new BusinessException('规则新增-主表添加失败, 原因可能是:' . get_data_object_error_msg($rule_model) . '; 数据:' . json_encode($add_data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            // 写入规则关联的部门明细表
            $batch_rule_dept_data = [];
            foreach ($cost_department_list as $dept) {
                $batch_rule_dept_data[] = [
                    'accounting_rule_id' => $rule_model->id,
                    'department_id'      => $dept['department_id'],
                    'is_include_sub'     => $dept['is_include_sub'],
                    'created_id'         => $user['id'],
                    'created_at'         => date('Y-m-d H:i:s'),
                ];
            }
            if (!empty($batch_rule_dept_data)) {
                $rule_dept_model = new AccountingRuleDepartmentListModel();
                if ($rule_dept_model->batch_insert($batch_rule_dept_data) === false) {
                    throw new BusinessException('规则新增-部门明细添加失败; 数据:' . json_encode($batch_rule_dept_data,
                            JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('会计规则添加异常:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则添加异常:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 规则更新
     *
     * @param array $params
     * @param array $user
     * @return array
     */
    public function updateRule(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 规则主数据校验
            $rule_model = AccountingRuleRepository::getInstance()->getOneById($params['id']);
            if (empty($rule_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($rule_model->budget_object_id != $params['budget_id']) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id',
                    ['id' => $params['id'] . '-' . $params['budget_id']]), ErrCode::$VALIDATE_ERROR);
            }

            // 入库前校验
            $this->saveRuleCommonLogicValidation($params, true);

            $this->logger->info('规则编辑-主数据(更新前):' . json_encode($rule_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 规则关联的原部门明细删除
            $rule_dept_models = $rule_model->getRuleDepartmentList();
            $this->logger->info('规则编辑-部门明细(更新前):' . json_encode($rule_dept_models->toArray(), JSON_UNESCAPED_UNICODE));
            if ($rule_dept_models->delete() === false) {
                throw new BusinessException('规则编辑-部门明细删除失败, 原因可能是:' . get_data_object_error_msg($rule_dept_models),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 费用部门列表
            $cost_department_list = $params['cost_department_list'];

            // 规则主数据更新
            $cost_department_ids = !empty($cost_department_list) ? implode(',',
                array_column($cost_department_list, 'department_id')) : '';
            $update_data         = [
                'budget_object_product_no' => $params['product_no'],
                'cost_department_ids'      => $cost_department_ids,
                'organization_type'        => $params['organization_type'],
                'accounting_subjects_id'   => $params['subjects_id'],
                'updated_id'               => $user['id'],
                'updated_at'               => date('Y-m-d H:i:s'),
            ];
            if ($rule_model->i_update($update_data) === false) {
                throw new BusinessException('规则编辑-主表更新失败, 原因可能是:' . get_data_object_error_msg($rule_model) . '; 数据:' . json_encode($update_data,
                        JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('规则编辑-主数据(更新后):' . json_encode($rule_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 规则关联部门明细重建
            $batch_rule_dept_data = [];
            foreach ($cost_department_list as $dept) {
                $batch_rule_dept_data[] = [
                    'accounting_rule_id' => $rule_model->id,
                    'department_id'      => $dept['department_id'],
                    'is_include_sub'     => $dept['is_include_sub'],
                    'created_id'         => $user['id'],
                    'created_at'         => date('Y-m-d H:i:s'),
                ];
            }

            $this->logger->info('规则编辑-部门明细(更新后):' . json_encode($batch_rule_dept_data, JSON_UNESCAPED_UNICODE));
            if (!empty($batch_rule_dept_data)) {
                $rule_dept_model = new AccountingRuleDepartmentListModel();
                if ($rule_dept_model->batch_insert($batch_rule_dept_data) === false) {
                    throw new BusinessException('规则编辑-部门明细添加失败', ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('会计规则编辑异常:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则编辑异常:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 规则删除
     *
     * @param int $id
     * @param array $user
     * @return array
     */
    public function delRule(int $id, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 规则主数据校验
            $rule_model = AccountingRuleRepository::getInstance()->getOneById($id);
            if (empty($rule_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('规则删除-操作用户:' . json_encode($user, JSON_UNESCAPED_UNICODE));

            // 规则主数据删除
            $this->logger->info('规则删除-主数据:' . json_encode($rule_model->toArray(), JSON_UNESCAPED_UNICODE));
            if ($rule_model->delete() === false) {
                throw new BusinessException('规则主数据删除失败, 原因可能是:' . get_data_object_error_msg($rule_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 规则关联的部门明细删除
            $rule_dept_models = $rule_model->getRuleDepartmentList();
            $this->logger->info('规则删除-部门明细:' . json_encode($rule_dept_models->toArray(), JSON_UNESCAPED_UNICODE));
            if ($rule_dept_models->delete() === false) {
                throw new BusinessException('部门明细删除失败, 原因可能是:' . get_data_object_error_msg($rule_dept_models),
                    ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('会计规则删除异常:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则删除异常:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 规则配置详情
     *
     * @param int $id
     * @return array
     */
    public function getRuleDetail(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['rule' => AccountingRuleModel::class]);
            $builder->leftjoin(BudgetObjectModel::class, 'rule.budget_object_id = budget.id', 'budget');
            $builder->leftjoin(BudgetObjectProductModel::class, 'rule.budget_object_product_no = budget_product.pno',
                'budget_product');
            $builder->leftjoin(AccountingSubjectsModel::class, 'rule.accounting_subjects_id = subjects.id', 'subjects');
            $builder->where('rule.id = :id:', ['id' => $id]);

            // 预算侧和会计侧系统语言对应的名称字段
            $budget_name_field   = get_lang_field_name('name_', static::$language, 'th', 'cn');
            $subjects_name_field = get_lang_field_name('subjects_name_', static::$language, 'local', 'zh');
            $columns             = [
                'rule.id',
                'rule.organization_type',
                'rule.budget_object_id AS budget_id',
                'rule.budget_object_product_no AS product_no',
                'rule.accounting_subjects_id AS subjects_id',
                "budget.{$budget_name_field} AS budget_name",
                "budget.is_delete AS budget_is_deleted",
                "budget_product.{$budget_name_field} AS product_name",
                "budget_product.is_delete AS product_is_deleted",
                'subjects.subjects_code AS subjects_code',
                "subjects.{$subjects_name_field} AS subjects_name",
                'subjects.is_deleted AS subjects_is_deleted',
            ];

            $builder->columns($columns);
            $data = $builder->getQuery()->getSingleResult();
            if (empty($data)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }
            $data = $data->toArray();

            // 费用所属总部/网点处理
            if (!empty($data['organization_type'])) {
                $data['organization_type_label'] = Enums::$organization_type_map[$data['organization_type']];
            } else {
                $data['organization_type']       = '';
                $data['organization_type_label'] = '';
            }

            // 获取费用部门信息
            $rule_dept_item = AccountingRuleDepartmentListRepository::getInstance()->getListByRuleIds([$data['id']]);
            $dept_item      = [];
            if (!empty($rule_dept_item)) {
                $department_repository = new DepartmentRepository();
                // 费用部门信息
                $cost_dept_item = $department_repository->getDepartmentByIds(array_column($rule_dept_item,
                    'department_id'), 2);

                // 上级部门信息
                $ancestry_dept_ids  = array_column($cost_dept_item, 'ancestry', 'id');
                $ancestry_dept_item = $department_repository->getDepartmentByIds(array_values($ancestry_dept_ids), 2);

                // 合并当前部门和上级部门
                $all_dept_item = array_merge($cost_dept_item, $ancestry_dept_item);
                $all_dept_item = array_column($all_dept_item, null, 'id');

                // 获取费用部门名称 和 删除状态
                foreach ($rule_dept_item as $dept) {
                    $dept_info = $all_dept_item[$dept['department_id']] ?? [];

                    // 上级部门/组织
                    $ancestry      = $dept_info['ancestry'] ?? '0';
                    $ancestry_name = $all_dept_item[$ancestry]['name'] ?? '';

                    // 构造已选部门/组织
                    $dept_item[] = [
                        'department_id'  => $dept['department_id'],
                        'name'           => $dept_info['name'] ?? '',
                        'is_deleted'     => $dept_info['deleted'],
                        'is_include_sub' => $dept['is_include_sub'],
                        'ancestry'       => $ancestry,
                        'ancestry_name'  => $ancestry_name,
                    ];
                }
            }

            $data['cost_department_list'] = $dept_item;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('会计规则详情异常:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取会计科目信息
     *
     * @param array $params
     * @return array
     */
    public function getAccountingSubjectsInfo(array $params)
    {
        // 费用所属网点/总部的枚举值 是否 符合通用的 1-总部; 2-网点; 个别业务模块是1-网点; 2-总部 相反定义的
        // 为了保证在会计规则侧的枚举统一, 需要 将非通用定义的枚举处理下
        // 目前按 1-网点；2-总部 定义的业务模块有: 报销
        if (!$params['organization_type_is_common'] && $params['organization_type']) {
            $params['organization_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 ? Enums::PAYMENT_COST_STORE_TYPE_02 : Enums::PAYMENT_COST_STORE_TYPE_01;
        }

        // 取该预算科目下的规则配置
        $budget_rule_list = $this->getExistRuleListByBudgetIds([$params['budget_id']]);
        if (empty($budget_rule_list)) {
            return [];
        }

        // 预算相关规则的key库
        $keys_lib = $this->generateCommonRuleKeysLib($budget_rule_list, [], true);

        // 待取数的规则项
        $params['cost_department_list'][] = [
            'department_id'  => $params['cost_department_id'],
            'is_include_sub' => SettingEnums::IS_NO_INCLUDE_SUB,
        ];

        $request_keys           = $this->generateCommonRuleKeysLib([$params], [], false)[0] ?? [];
        $accounting_subjects_id = $this->getRuleMatchedSubjectsId($request_keys, $keys_lib);

        $subjects_info = [];
        if (!empty($accounting_subjects_id)) {
            $subjects_info = AccountingSubjectsRepository::getInstance(static::$language)->getOneById($accounting_subjects_id);
            $subjects_info = $subjects_info ? $subjects_info->toArray() : [];
        }

        return $subjects_info;
    }

    /**
     * 根据会计规则项获取会计科目ID
     *
     * @param array $request_keys 业务侧规则项生成的相关key
     * @param array $keys_lib 库里已有规则的key库
     * @return mixed
     */
    protected function getRuleMatchedSubjectsId(array $request_keys, array $keys_lib)
    {
        $accounting_subjects_id = '';
        foreach ($request_keys as $request_k) {
            foreach ($keys_lib as $lib_k) {
                $accounting_subjects_id = $lib_k[$request_k] ?? '';
                if (!empty($accounting_subjects_id)) {
                    break 2;
                }
            }
        }

        return $accounting_subjects_id;
    }

}
