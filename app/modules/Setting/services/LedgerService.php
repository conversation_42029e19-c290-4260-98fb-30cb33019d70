<?php

namespace App\Modules\Setting\Services;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetObjectLedger;
use App\Modules\Budget\Models\BudgetObjectProductLedger;
use App\Modules\Ledger\Models\LedgerAccountModel;

class LedgerService extends BaseService
{
    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                           //每页条数
        'page' => 'IntGt:0',                            //页码
        'name_en' => 'StrLenGeLe:0,50',                  //开户行描述
    ];

    public static $validate_params = [
        'account' => 'Required|Regexp:/^[0-9]{0,15}$/',  // 科目号
        'name_en' => 'Required',  // 英文名称
    ];

    public static $single_validate_params = [
        'ledger_id' => 'IntGt:0',
    ];
    public static $delete_validate_params = [
        'ledger_id' => 'ArrLenGeLe:1,100',
    ];

    public static $edit_validate_params = [
        'id' => 'Required', // id
        'name_en' => 'Required',  // 英文名称
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LedgerService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 核算科目列表
     * @param $params array
     * @return array
     */
    public function getSupplierBankList($params = [])
    {
        $nameEn = $params['name_en'];
        $limit = $params['page_size'] ?? 20; // 默认每页20条
        $offset = (isset($params['page_size']) && isset($params['page'])) ?
            ($params['page_size'] * ($params['page'] - 1)) : 0; // 默认偏移量为0

        $builder = $this->modelsManager->createBuilder();
        $column_str = "id,account,name_en,name_cn,name_th";
        $builder->columns($column_str);
        $builder->from(['c' => LedgerAccountModel::class]);
        if (!empty($nameEn)) {
            $builder->where('c.name_en like :name_en:', ['name_en' => "%{$nameEn}%"]);
        }

        $count = $builder->getQuery()->execute()->count();
        $builder->limit($limit, $offset);
        $builder->orderBy('id desc');
        $items = $builder->getQuery()->execute()->toArray();

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $params['page'],
                'page_limit'   => $params['page_size'],
                'total_count'  => $count,
            ]
        ];
    }

    /**
     * 保存核算科目
     * @param $data
     * @param $user
     * @return mixed
     */
    public function saveOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $supplier = LedgerAccountModel::findFirst([
                'conditions' => 'account = :account: or name_en = :name_en:',
                'bind' => ['account' => $data['account'],'name_en' => $data['name_en']]
            ]);
            // 科目号或名称已存在
            if (!empty($supplier)) {
                if ($supplier->account == $data['account']) {
                    throw new ValidationException(static::$t->_('ledger_account_is_repeat'), ErrCode::$VALIDATE_ERROR);
                } else {
                    throw new ValidationException(static::$t->_('ledger_name_en_is_repeat'), ErrCode::$VALIDATE_ERROR);
                }
            }
            // 参数处理
            $data = $this->handleItems($data,$user);

            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => addLedgerAccountAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );

            $model = new LedgerAccountModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('核算科目创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ledger-account-create-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 批量删除核算科目
     * @param $ids
     * @param $user
     * @return mixed
     */
    public function batchDelete($ids, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 需要删除的科目
            $ledgersModel = LedgerAccountModel::find([
                'conditions' => 'id in({ids:array})',
                'bind' => ['ids' => $ids],
                'for_update' => true
            ]);
            $ledgers = $ledgersModel->toArray();
            $ledgersIds = array_column($ledgers,'id');
            // 已经关联过的核算科目
            $budgetObjectLedger = BudgetObjectLedger::find([
                'conditions' => 'ledger_id in({ids:array})',
                'bind' => ['ids' => $ids]
            ])->toArray();
            $budgetObjectLedgerIds = array_column($budgetObjectLedger,'ledger_id');
            // 已经关联过的产品科目
            $budgetObjectProductLedger = BudgetObjectProductLedger::find([
                'conditions' => 'ledger_id in({ids:array})',
                'bind' => ['ids' => $ids]
            ])->toArray();
            $budgetObjectProductLedgerIds = array_column($budgetObjectProductLedger,'ledger_id');

            foreach ($ids as $id) {
                // 科目是否存在
                if (!in_array($id,$ledgersIds)) {
                    throw new ValidationException(static::$t->_('ledger_account_is_not_exist'), ErrCode::$VALIDATE_ERROR);
                }
                // 是否关联过的核算科目
                if (in_array($id,$budgetObjectLedgerIds)) {
                    throw new ValidationException(static::$t->_('budget_object_is_related_to_ledger'), ErrCode::$VALIDATE_ERROR);
                }
                // 是否关联过的产品科目
                if (in_array($id,$budgetObjectProductLedgerIds)) {
                    throw new ValidationException(static::$t->_('budget_object_product_is_related_to_ledger'), ErrCode::$VALIDATE_ERROR);
                }
            }

            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => batchDeleteLedgerAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            // 删除
            $bool = $ledgersModel->delete();
            if ($bool === false) {
                throw new BusinessException('核算科目批量删除失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('ledger-account-batch-delete-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * 编辑核算科目
     * @param $data
     * @param $user
     * @return mixed
     */
    public function editOne($data, $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $ledger = LedgerAccountModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $data['id']]
            ]);
            // 核算科目不存在
            if (empty($ledger)) {
                throw new BusinessException('核算科目不存在', ErrCode::$VALIDATE_ERROR);
            }
            $ledgerName = LedgerAccountModel::findFirst([
                'conditions' => 'name_en = :name_en: or name_cn = :name_cn:',
                'bind' => ['name_en' => $data['name_en'],'name_cn' => $data['name_cn']]
            ]);
            // 名称已存在
            if (!empty($ledgerName)) {
                if ($ledgerName->name_en == $data['name_en']) {
                    throw new ValidationException(static::$t->_('ledger_name_en_is_repeat'), ErrCode::$VALIDATE_ERROR);
                } else {
                    throw new ValidationException(static::$t->_('ledger_name_cn_is_repeat'), ErrCode::$VALIDATE_ERROR);
                }
            }
            // 参数处理
            $data = $this->handleItems($data,$user);
            // 传参原数据保存
            $this->getDI()->get('logger')->info(
                "function => editLedgerAccountAction before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            $bool = $ledger->save($data);
            if ($bool === false) {
                throw new BusinessException('核算科目编辑失败', ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('supplier-bank-edit-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    /**
     * @param $items
     * @param $user
     * @return array
     */
    private function handleItems($items,$user)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $items['name_th'] = ''; // 泰国名称默认为空
        $items['last_update_id'] = $user['id'];
        $items['last_update_at'] = date('Y-m-d H:i:s');

        return $items;
    }
}