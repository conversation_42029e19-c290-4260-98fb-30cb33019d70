<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Purchase\Models\PurchaseOrderPaymentMode;

class PaymentModeService extends BaseService
{
    public static $validate_list_search = [
        'page_size'         => 'IntGt:0',                             //每页条数
        'page'              => 'IntGt:0',                             //页码
        'payment_mode_name' => 'StrLenGeLe:0,200',                    //名称描述
    ];

    public static $add_validate_params  = [
        'payment_mode_code' => 'Required|StrLenGeLe:1,50',  // 编号
        'payment_mode_name' => 'Required|StrLenGeLe:1,200'  // 名称
    ];
    public static $edit_validate_params = [
        'id'                => 'Required',                  // id
        'payment_mode_name' => 'Required|StrLenGeLe:1,200'  // 名称
    ];

    const PAYMENT_MODE_KEY = 'purchase_order_payment_mode'; //付款模式翻译Key

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return LoanStaffService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 配置查询列表
     * @param $params array
     * @return array
     */
    public function getPaymentModeList($params = [])
    {
        $payment_mode_name = $params['payment_mode_name'] ?? '';
        $page_size         = empty($params['page_size']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['page_size'];
        $page_num          = empty($params['page']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['page'];
        $offset            = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $builder->from(PurchaseOrderPaymentMode::class);
        $builder->where('is_del = ' . GlobalEnums::IS_NO_DELETED);

        if (!empty($payment_mode_name)) {
            $builder->andWhere('payment_mode_name like :payment_mode_name:',
                ['payment_mode_name' => "%{$payment_mode_name}%"]);
        }

        //总条数
        $count_info = $builder->columns('COUNT(id) AS t_count')->getQuery()->getSingleResult();
        $count      = $count_info->t_count ?? 0;
        //分页数据
        $builder->columns("id,payment_mode_code,payment_mode_name");
        $builder->limit($page_size, $offset);
        $builder->orderBy('id desc');
        $items = $builder->getQuery()->execute()->toArray();

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $page_num,
                'page_limit'   => $page_size,
                'total_count'  => $count,
            ],
        ];
    }

    /*
    * 生成编号
    * @return string
    * */
    public function generateSerialNo()
    {
        // 国家
        $country = get_country_code();
        // 生成编号
        $row = PurchaseOrderPaymentMode::findFirst([
            'order'      => 'id desc',
            'for_update' => true,
        ]);
        $no  = isset($row->id) ? $row->id : 0;
        return strtolower($country) . '.' . self::PAYMENT_MODE_KEY . '.' . ($no + 1);
    }

    /**
     * 保存配置
     * @param $data
     * @param $user
     * @return mixed
     */
    public function add($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 传参原数据保存
            $this->logger->info(
                "purchase_payment_mode add before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );
            $data               = array_only($data, array_keys(self::$add_validate_params));
            $data['create_id']  = $user['id'];
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['updated_at'] = date('Y-m-d H:i:s');
            $model              = new PurchaseOrderPaymentMode();
            $bool               = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException("付款模式配置创建失败:[" . implode(",", $model->getMessages()) . "]",
                    ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('purchase_payment_mode-create-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }


    /**
     * 编辑借款员工配置
     * @param $data
     * @param $user
     * @return mixed
     */
    public function edit($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            // 传参原数据保存
            $this->logger->info(
                "purchase_payment_mode edit before & params=>" . json_encode($data, JSON_UNESCAPED_UNICODE)
            );

            $model = $this->getOne($data['id']);
            if (!$model) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'),
                    ErrCode::$VALIDATE_ERROR);
            }
            $update = [
                'payment_mode_name' => $data['payment_mode_name'],
                'update_id'         => $user['id'],
                'updated_at'        => date('Y-m-d H:i:s'),
            ];
            $bool   = $model->save($update);
            if ($bool === false) {
                throw new BusinessException('付款模式配置编辑失败:[' . implode(",", $model->getMessages()) . ']',
                    ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {        //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('purchase_payment_mode-edit-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    public function getOne($id = 0)
    {
        return PurchaseOrderPaymentMode::findFirst([
            'conditions' => 'id = :id: and is_del = :is_del:',
            'bind'       => [
                'id'     => $id,
                'is_del' => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
    }

}