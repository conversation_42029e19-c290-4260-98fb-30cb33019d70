<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\StaffManageListRelateModel;
use App\Models\oa\StaffSpecialGroupRelateModel;
use App\Models\oa\StaffManageListModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\HrJobTitleRepository;

class SpecialGroupRelateService extends BaseService
{

    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'group_id',
        'staff_info_id',
        'configure_state',
        'state',
        'pageSize',
        'pageNum'
    ];

    //特殊组配置-新增员工
    public static $add_validate_params = [
        'staff_info_id' => 'Required|StrLenGeLe:1,10|>>>:param error[staff_info_id]',
        'group_id'      => 'Required|StrLenGeLe:1,10|>>>:param error[group_id]'
    ];

    //删除 详情
    public static $validate_params = [
        'id' => 'Required|StrLenGeLe:1,10|>>>:param error[id]'
    ];

    //模糊搜索用户的工号和姓名
    public static $search_staff_validate_params = [
        'name'         => 'StrLenGeLe:1,200|>>>:name error',
    ];

    //特殊组管辖配置列表
    public static $search_validate_params = [
        'pageSize'        => 'IntGt:0',
        'pageNum'         => 'IntGt:0',
        'state'           => 'IntIn:' . StaffInfoEnums::STAFF_STATE_IN . ',' . StaffInfoEnums::STAFF_STATE_LEAVE . ',' . StaffInfoEnums::STAFF_STATE_STOP . ',' . StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE,
        'configure_state' => 'IntIn:' . SettingEnums::CONFIGURE_STATE_1 . ',' . SettingEnums::CONFIGURE_STATE_2,
        'staff_info_id'   => 'StrLenGeLe:0,11|>>>:param error[staff_info_id]',
        'group_id'        => 'StrLenGeLe:0,50|>>>:param error[group_id]',
    ];

    //特殊组管辖配置-配置修改
    public static $edit_validate_params = [
        'id'                     => 'StrLenGeLe:1,11|>>>:param error[id]',
        'group_id'               => 'Required|StrLenGeLe:1,50|>>>:param error[group_id]',
        'staff_info_id'          => 'StrLenGeLe:0,11|>>>:param error[staff_info_id]',
        'data'                   => 'Required|ArrLenGeLe:0,100',
        'data[*].department_id'  => 'StrLenGeLe:1,32',
        'data[*].is_include_sub' => 'IntIn:1,0',
    ];

    private static $instance;

    private function __clone()
    {
    }

    private function __construct()
    {
    }


    /**
     * @return SpecialGroupRelateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /***
     * 特殊组配置-新增员工
     * @param array $params  输入的数据
     * @param int $uid   当前登录用户
     * @return array
     */
    public function addSpecialGroupStaff(array $params, int $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $staff_info = (new HrStaffRepository())->getStaffById($params['staff_info_id']);
            if (!empty($staff_info)) {
                if ($staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN) {
                    throw new ValidationException(static::$t->_('staff_state_not_on_job'), ErrCode::$VALIDATE_ERROR);
                }

                if ($staff_info['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    throw new ValidationException(static::$t->_('staff_state_wait_leave_state'), ErrCode::$VALIDATE_ERROR);
                }

                if ($staff_info['formal'] != StaffInfoEnums::FORMAL_IN) {
                    throw new ValidationException(static::$t->_('staff_identity_must_formal'), ErrCode::$VALIDATE_ERROR);
                }
            } else {
                throw new ValidationException(static::$t->_('staff_info_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            $staff_special = StaffSpecialGroupRelateModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and  group_id = :group_id: and is_deleted = :is_deleted:',
                'bind'       => [
                    'staff_info_id' => $params['staff_info_id'],
                    'group_id'      => $params['group_id'],
                    'is_deleted'    => GlobalEnums::IS_NO_DELETED,

                ],
            ]);
            if (!empty($staff_special)) {
                throw new ValidationException(static::$t->_('staff_special_group_repeat_add'), ErrCode::$VALIDATE_ERROR);
            }
            $special_group_relate_model = new StaffSpecialGroupRelateModel();
            $current_time        = date('Y-m-d H:i:s');
            $add_data            = [
                'staff_info_id'      => $params['staff_info_id'],
                'node_department_id' => $staff_info['node_department_id'],
                'is_deleted'         => GlobalEnums::IS_NO_DELETED,
                'group_id'           => $params['group_id'],
                'updated_id'         => $uid,
                'created_id'         => $uid,
                'created_at'         => $current_time,
                'updated_at'         => $current_time
            ];
            if ($special_group_relate_model->save($add_data) == false) {
                throw new BusinessException('特殊组配置-新增员工-失败: ' . '; 可能存在的问题: ' . get_data_object_error_msg($special_group_relate_model) . ' 添加的数据是:' . json_encode($add_data, JSON_UNESCAPED_UNICODE), ErrCode::$STAFF_SPECIAL_GROUP_ADD_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('特殊组配置-新增员工-异常信息: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 特殊组配置-删除添加的员工数据
     * @param array $params 请求入参数
     * @param int $uid 当前登录用户
     * @return array
     */
    public function delSpecialGroupStaff(array $params, int $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $staff_special_group_relate = StaffSpecialGroupRelateModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $params['id']
                ],
            ]);
            if (empty($staff_special_group_relate)) {
                throw new ValidationException(static::$t->_('staff_special_group_not_null'), ErrCode::$VALIDATE_ERROR);
            }
            $staff_special_group_relate->is_deleted = GlobalEnums::IS_DELETED;
            $staff_special_group_relate->updated_id = $uid;
            $staff_special_group_relate->updated_at = date('Y-m-d H:i:s');
            if ($staff_special_group_relate->save() === false) {
                throw new BusinessException('特殊组配置-删除添加的员工数据失败: ' . '; 可能存在的问题: ' . get_data_object_error_msg($staff_special_group_relate), ErrCode::$STAFF_SPECIAL_GROUP_DEL_ERROR);
            }

            $staff_manage_list = StaffManageListModel::find([
                'conditions' => 'staff_info_id = :staff_info_id: and group_id = :group_id:',
                'bind'       => [
                    'staff_info_id' => $staff_special_group_relate->staff_info_id,
                    'group_id'      => $staff_special_group_relate->group_id,
                ],
            ]);

            if (!empty($staff_manage_list->toArray())) {
                foreach ($staff_manage_list as $staff_manage_item) {
                    $staff_manage_item->is_deleted = GlobalEnums::IS_DELETED;
                    $staff_manage_item->updated_at = date('Y-m-d H:i:s');
                    if ($staff_manage_item->save() === false) {
                        throw new BusinessException('特殊组配置-删除添加的员工数据-同步删除 manage_list删除失败,可能存在的问题: ' . get_data_object_error_msg($staff_manage_item->toArray()), ErrCode::$STAFF_SPECIAL_GROUP_DEL_ERROR);
                    }
                    $manage_relate_bool = $db->updateAsDict(
                        (new StaffManageListRelateModel())->getSource(),
                        ['is_deleted' => GlobalEnums::IS_DELETED, 'updated_at' => date('Y-m-d H:i:s')],
                        ['conditions' => 'pid =' . $staff_manage_item->id]
                    );
                    if ($manage_relate_bool == false) {
                        throw new BusinessException('特殊组配置-编辑管辖部门manage_list_relate删除失败,被修改的pid是: ' . $staff_manage_item->id, ErrCode::$STAFF_SPECIAL_GROUP_DEL_ERROR);
                    }
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if(!empty($message)){
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('del_special_group_staff failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message
        ];
    }

    /**
     * 格式化特殊组管辖配置列表数据
     * @param array $items 需要格式化的数据
     * @return array
     */
    public function handelStaffItems(array $items)
    {

        $all_department  = (new DepartmentRepository())->getAllDepartmentList();
        $group_info      = FinancialService::getInstance()->getAllGroup(SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_SPECIAL);
        $job_title       = (new HrJobTitleRepository())->getJobTitleByIds(array_column($items, 'job_title'));
        $group_info      = array_column($group_info, 'name', 'id');
        $all_department  = array_column($all_department, 'name', 'id');
        $job_title       = array_column($job_title, 'job_name', 'id');
        $staff_state     = StaffInfoEnums::$staff_state;
        $configure_state = SettingEnums::$configure_state;
        foreach ($items as &$item) {
            $department_tmp = [];
            $department_ids = explode(',', $item['manage_department_ids']);
            foreach ($department_ids as $department_id) {
                if (isset($all_department[$department_id])) {
                    $department_tmp[] = $all_department[$department_id];
                }
            }
            $item['department_name'] = $all_department[$item['node_department_id']] ?? '';

            $item['manage_department_names'] = implode(',', $department_tmp);
            $item['group_name']              = $group_info[$item['group_id']] ?? '';
            $item['job_title']               = $job_title[$item['job_title']] ?? '';
            $item['configure_state_text']    = static::$t->_($configure_state[(is_null($item['configure_state']) ? 1 : $item['configure_state'])]);
            if ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                $item['state_text'] = static::$t->_($staff_state[StaffInfoEnums::STAFF_STATE_IN]);
            } else if ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                $item['state_text'] = static::$t->_($staff_state[StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE]);
            } else {
                $item['state_text'] = static::$t->_($staff_state[$item['state']]);
            }

        }
        return $items;
    }


    /**
     * 特殊组管辖配置列表
     * @param array $params 条件
     * @return array
     */
    public function specialGroupList(array $params)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['ssgr' => StaffSpecialGroupRelateModel::class]);
            $builder->leftJoin(MiniHrStaffInfoModel::class, 'ssgr.staff_info_id = mhsi.staff_info_id', 'mhsi');
            $builder->leftJoin(StaffManageListModel::class, 'ssgr.staff_info_id = sml.staff_info_id and ssgr.group_id = sml.group_id and sml.is_deleted =' . GlobalEnums::IS_NO_DELETED, 'sml');
            $builder->where('ssgr.is_deleted = :is_deleted:', ['is_deleted' =>  GlobalEnums::IS_NO_DELETED]);
            $builder = $this->getCondition($builder, $params);
            $count   = (int)$builder->columns('COUNT(ssgr.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count > 0) {
                $column_str = 'ssgr.staff_info_id, mhsi.name, ssgr.node_department_id, mhsi.job_title, ssgr.group_id, mhsi.state, mhsi.wait_leave_state, sml.configure_state, ssgr.id as del_id, sml.id, sml.manage_department_ids';
                $builder->columns($column_str);
                $builder->limit($page_size, $offset);
                $builder->orderBy('ssgr.created_at desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handelStaffItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('special_group_list_relate failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @return object
     */
    public function getCondition(object $builder, array $condition)
    {
        $staff_info_id   = empty($condition['staff_info_id']) ? '' : $condition['staff_info_id'];
        $group_id        = empty($condition['group_id']) ? '' : $condition['group_id'];
        $state           = empty($condition['state']) ? '' : $condition['state'];
        $configure_state = empty($condition['configure_state']) ? '' : $condition['configure_state'];
        if (!empty($staff_info_id)) {
            $builder->andWhere('ssgr.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }

        if (!empty($state)) {
            switch ($state) {
                case StaffInfoEnums::STAFF_STATE_IN:

                    $builder->andWhere('mhsi.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
                    $builder->andWhere('mhsi.wait_leave_state  = :wait_leave_state:', ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO]);

                    break;
                case StaffInfoEnums::STAFF_STATE_LEAVE:

                    $builder->andWhere('mhsi.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_LEAVE]);

                    break;
                case StaffInfoEnums::STAFF_STATE_STOP:

                    $builder->andWhere('mhsi.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_STOP]);

                    break;
                case StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE:

                    $builder->andWhere('mhsi.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
                    $builder->andWhere('mhsi.wait_leave_state  = :wait_leave_state:', ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES]);
                    break;
                default:
                    break;
            }
        }

        if ($configure_state == SettingEnums::CONFIGURE_STATE_2) {
            $builder->andWhere('sml.configure_state = :configure_state:', ['configure_state' => $configure_state]);
        } else if ($configure_state == SettingEnums::CONFIGURE_STATE_1) {
            $builder->andWhere('sml.configure_state is null');
        }

        if (!empty($group_id)) {
            $builder->andWhere('ssgr.group_id = :group_id:', ['group_id' => $group_id]);
        }
        return $builder;
    }

    /**
     * 特殊组配置-枚举
     * @return array
     * */
    public function specialEnums()
    {
        return FinancialService::getInstance()->getEnums(SettingEnums::WORKFLOW_STAFF_MANAGE_GROUP_TYPE_SPECIAL);
    }


    /**
     * 特殊组管辖部门配置
     * @param array $params 查询条件
     * @param int $uid  用户id
     * @return array
     **/
    public function specialEditStaffManageRegion(array $params, int $uid)
    {
        return FinancialService::getInstance()->editStaffManageRegion($params, $uid);
    }


    /**
     * 特殊组管辖部门详情
     * @param array $params 查询条件
     * @return array
     **/
    public function staffManageDepartments(array $params)
    {
        return FinancialService::getInstance()->getStaffManageDepartments($params['id']);
    }


    /**
     * 模糊搜索在职员工
     * @param array $params 查询条件
     * @return array
     **/
    public function searchStaffData(array $params)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $data = (new HrStaffRepository)->likeSearchStaffOnLineData($params['name']);
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->error('setting_special_group_search_staff failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }


}
