<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RedisClient;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Util\RedisKey;

class SystemService extends BaseService
{
    public static $validate_list_search = [
        'page_size' => 'IntGt:0',                            // 每页条数
        'page'      => 'IntGt:0',                            // 页码
        'code'      => 'StrLenGeLe:0,100',                   // 设置key
        'content'   => 'StrLenGeLe:0,100',                   // 描述
    ];

    public static $edit_validate_params = [
        'code'    => 'Required|StrLenGeLe:1,60|>>>:params error[code]',
        'val'     => 'Required|Str|>>>:params error[val]',                  // 配置值
        'content' => 'Required|StrLenGeLe:0,255|>>>:params error[content]', // 配置描述
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return SystemService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 系统配置项列表
     *
     * @param $params array
     * @return array
     */
    public function getSystemSettingList($params = [])
    {
        $code    = $params['code'];
        $content = $params['content'];
        $limit   = $params['page_size'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;                                                       // 默认每页20条
        $offset  = (isset($params['page_size']) && isset($params['page'])) ? ($params['page_size'] * ($params['page'] - 1)) : 0; // 默认偏移量为0

        $builder = $this->modelsManager->createBuilder();
        $builder->from(EnvModel::class);
        $builder->where('is_edit = :is_edit:', ['is_edit' => SettingEnums::SETTING_ENUMS_IS_EDIT]);

        if (!empty($code)) {
            $builder->andWhere('code like :code:', ['code' => "%$code%"]);
        }

        if (!empty($content)) {
            $builder->andWhere('content like :content:', ['content' => "%$content%"]);
        }

        $count = (int)$builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;

        $items = [];
        if ($count) {
            $builder->columns('id, code, val, content');
            $builder->limit($limit, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            foreach ($items as &$value) {
                $value['content'] = $value['content'] ?? '';
            }
        }

        return [
            'items'      => $items,
            'pagination' => [
                'current_page' => $params['page'] ?? 1,
                'page_limit'   => $limit,
                'total_count'  => $count,
            ],
        ];
    }

    /**
     * 编辑系统配置项
     *
     * @param $data
     * @param $user
     * @return mixed
     */
    public function editOne($data, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 可配置项
            $env = EnvModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $data['code']],
            ]);

            // 可编辑系统配置项不存在
            if (empty($env)) {
                throw new ValidationException('可编辑系统配置项:' . $data['code'] . '不存在', ErrCode::$VALIDATE_ERROR);
            }

            if ($env->is_edit != SettingEnums::SETTING_ENUMS_IS_EDIT) {
                throw new ValidationException('无权编辑,请联系管理员', ErrCode::$VALIDATE_ERROR);
            }

            // 更新前原数据保存
            $this->logger->info('sys_setting_env_edit: before data => ' . json_encode($env->toArray(),
                    JSON_UNESCAPED_UNICODE));

            // 参数处理
            $env->val            = trim($data['val']);
            $env->content        = trim($data['content']);
            $env->updated_at     = date('Y-m-d H:i:s');
            $env->last_update_id = $user['id'];

            // 更新后数据
            $this->logger->info('sys_setting_env_edit: after data => ' . json_encode($env->toArray(),
                    JSON_UNESCAPED_UNICODE));
            if ($env->save() === false) {
                throw new BusinessException('setting_env 编辑更新失败, 原因可能是:' . get_data_object_error_msg($env),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('system-setting-edit-failed: ' . $e->getMessage());
        } catch (\Exception $e) {
            // 系统错误
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('system-setting-edit-failed: ' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 同步本机翻译
     *
     * @param string $host_type
     * @return bool
     * @throws BusinessException
     */
    public function pullCurrentHostLanguage(string $host_type = 'oa-api')
    {
        $redis = RedisClient::getInstance()->getClient();

        // 1. 获取当前共享中心版本号
        $center_language_version_key = RedisKey::SYS_SETTING_SYNC_LANGUAGE_CENTER_VERSION;
        $center_language_version     = (int)$redis->get($center_language_version_key);

        // 2. 获取当前主机的版本号
        $current_host_name = gethostname();

        $hosts_language_version_key    = RedisKey::SYS_SETTING_SYNC_LANGUAGE_HOSTS_VERSION;
        $hosts_language_version_list   = $redis->get($hosts_language_version_key);
        $hosts_language_version_list   = !empty($hosts_language_version_list) ? json_decode($hosts_language_version_list,
            true) : [];
        $current_host_language_version = $hosts_language_version_list[$current_host_name] ?? 0;

        // 3. 本机版本号 低于 共享版本号, 则拉取本机翻译 并 更新本机版本号
        if ($current_host_language_version < $center_language_version) {
            if ($this->pullLanguage()) {
                $hosts_language_version_list[$current_host_name] = $center_language_version;
                $redis->setex($hosts_language_version_key, SettingEnums::I18N_TRANSLATION_CATCH_PERIOD,
                    json_encode($hosts_language_version_list, JSON_UNESCAPED_UNICODE));
            } else {
                throw new BusinessException("该主机翻译拉取失败, 主机类型={$host_type}, 主机名=$current_host_name",
                    ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 语言同步初始化
     *
     * @return mixed
     */
    public function languageSyncInit()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $redis = RedisClient::getInstance()->getClient();

            // 1. 获取当前共享版本号
            $center_language_version_key = RedisKey::SYS_SETTING_SYNC_LANGUAGE_CENTER_VERSION;
            $center_language_version     = (int)$redis->get($center_language_version_key);

            // 2. 更新当前共享版本号
            $redis->setex($center_language_version_key, SettingEnums::I18N_TRANSLATION_CATCH_PERIOD,
                ++$center_language_version);

            // 3. 拉取本机翻译
            $this->pullCurrentHostLanguage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('oa_setting_sync_language:' . $e->getMessage());
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->error('oa_setting_sync_language:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 拉取远端词库翻译到本机语言包目录
     *
     * @return bool
     */
    public function pullLanguage()
    {
        $result = true;

        $file_path = APP_PATH . '/language';
        if (!file_exists($file_path)) {
            mkdir($file_path, 0777, true);
        }

        $langs = SettingEnums::I18N_TRANSLATION_SUPPORT_LANGUAGES;
        foreach ($langs as $lang) {
            $url       = sprintf(SettingEnums::I18N_TRANSLATION_SYNC_API . '/%s.json', substr($lang, 0, 2));
            $lang_data = json_decode(file_get_contents($url), true);
            if (is_array($lang_data)) {
                $language_file_path = "{$file_path}/{$lang}.php";

                $file_content = '<?php return ' . var_export($lang_data, true) . ';';
                if (file_put_contents($language_file_path, $file_content) == false) {
                    $result = false;
                    break;
                }

                $reset_opcache_res = safe_reset_opcache($language_file_path);
                $this->logger->info(['pull_language_reset_opcache_res' => $reset_opcache_res]);
            }
        }

        return $result;
    }

}
