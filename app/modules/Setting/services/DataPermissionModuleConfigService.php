<?php

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\DataPermissionModuleConfigModel;
use App\Models\oa\DataPermissionModuleConfigPowerModel;
use App\Models\oa\DataPermissionManagePowerListModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Repository\DepartmentRepository;
use App\Repository\oa\DataPermissionModuleConfigRepository;
use App\Repository\oa\SysModuleRepository;

class DataPermissionModuleConfigService extends BaseService
{
    // 管辖人管辖范围的缓存配置
    // 缓存前缀
    const DATA_PERMISSION_APPLICATION_CACHE_PREFIX = 'data_config_permission_app_';

    // 缓存时长(秒数)
    const DATA_PERMISSION_APPLICATION_CACHE_DURATION = 600;

    //非必填
    public static $not_must_params = [
        'staff_info_id',
        'pageSize',
        'pageNum',
    ];
    //校验
    public static $validate_share = [
        'id' => 'Required|IntGt:0',
    ];

    //配置权限列表校验
    public static $validate_power = [
        'staff_info_id' => 'IntGt:0',//申请人工号,
        'pageSize'      => 'IntGt:0',//每页条数
        'pageNum'       => 'IntGt:0', //页码
    ];

    //配置模块校验
    public static $validate_config_list = [
        'pageSize' => 'IntGt:0',
        'pageNum'  => 'IntGt:0',
    ];

    //添加
    public static $validate_add = [
        'staff_info_id' => 'Required|IntGt:0',//工号,
    ];

    //保存
    public static $validate_save = [
        'id'                     => 'Required|IntGt:0|>>>:param error[id]',
        'data'                   => 'Required|ArrLenGeLe:0,1000',
        'data[*].department_id'  => 'StrLenGeLe:1,32',
        'data[*].is_include_sub' => 'IntIn:1,0',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }


    /**
     * @return DataPermissionModuleConfigService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 应用模块列表查询
     * @param array $condition 查询条件
     * @return array
     */
    public function getConfigList(array $condition): array
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getConfigListCount();
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.module_name, main.menu_name';
                $builder->columns($columns);
                $builder->from(['main' => DataPermissionModuleConfigModel::class]);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id DESC');
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('get_config_list_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 应用模块统计数据
     * @return array
     */
    public function getConfigListCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => DataPermissionModuleConfigModel::class]);
        return intval($builder->getQuery()->getSingleResult()->total);
    }


    /**
     * 配置权限数据列表
     * @param array $condition 查询条件组
     * @return array
     */
    public function getPowerList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            $count = $this->getPowerListCount($condition);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $columns = 'main.id, main.staff_info_id, main.staff_department_id, main.manage_department_ids';
                $builder->columns($columns);
                $builder->from(['main' => DataPermissionModuleConfigPowerModel::class]);
                $builder->limit($page_size, $offset);
                $builder->orderby('main.id ' . ($condition['sort'] ?? 'asc'));
                //组合搜索条件
                $builder = $this->getPowerCondition($builder, $condition);
                $items   = $builder->getQuery()->execute()->toArray();
                $items   = $this->handleListItems($items);

            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('get_power_list_failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 配置权限列表统计数据
     * @param array $condition 查询条件组
     * @return int
     */
    public function getPowerListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(main.id) as total');
        $builder->from(['main' => DataPermissionModuleConfigPowerModel::class]);
        //组合搜索条件
        $builder = $this->getPowerCondition($builder, $condition);
        return intval($builder->getQuery()->getSingleResult()->total);
    }

    /**
     * 配置权限列表组合搜索条件
     * @param object $builder 查询条件对象
     * @param array $condition 查询条件组
     * @return object
     */
    public function getPowerCondition(object $builder, array $condition)
    {
        $staff_info_id = !empty($condition['staff_info_id']) ? $condition['staff_info_id'] : 0;
        $id            = !empty($condition['id']) ? $condition['id'] : '';

        if (!empty($id)) {
            $builder->andWhere('main.cid = :cid:', ['cid' => $id]);
        }
        if (!empty($staff_info_id)) {
            $builder->andWhere('main.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_info_id]);
        }
        return $builder;
    }

    /**
     * 数据处理
     * @param array $items 数据
     * @return array
     */
    private function handleListItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        $staff_ids = array_values(array_column($items, 'staff_info_id'));
        $staff_arr = HrStaffInfoModel::find(
            ['conditions' => 'staff_info_id in ({ids:array})',
             'bind'       => ['ids' => $staff_ids],
             'columns'    => ['staff_info_id', 'name']
            ])->toArray();
        if (!empty($staff_arr)) {
            $staff_arr = array_column($staff_arr, 'name', 'staff_info_id');
        }
        $all_department_ids = explode(',', implode(',', array_column($items, 'manage_department_ids')));
        $department_ids     = array_unique(array_filter(array_merge(array_column($items, 'staff_department_id'), $all_department_ids)));
        $department_arr     = $this->getDepartmentInfo($department_ids);

        foreach ($items as &$item) {
            $item['staff_info_name']       = $staff_arr[$item['staff_info_id']];
            $item['staff_department_name'] = $item['staff_department_id'] ? $department_arr[$item['staff_department_id']] : '';
            $item['department_arr']        = !empty($item['manage_department_ids']) ? $this->getCombinationDepartmentArr($item['manage_department_ids'], $department_arr) : [];
        }
        return $items;
    }

    /**
     * 按照部门ids  返回对应的name
     * @param string $department_ids 部门ids
     * @param array $department_arr 部门arr
     * @return array
     */
    public function getCombinationDepartmentArr(string $department_ids, array $department_arr)
    {
        if (empty($department_ids)) {
            return [];
        }
        $department = [];
        $department_id_arr = explode(',', $department_ids);
        foreach ($department_id_arr as $department_id) {
            $department[] = $department_arr[$department_id];
        }
        return $department;
    }


    /**
     * 批量分组查询部门
     * @param array $department_ids 部门id
     * @return array
     */
    public function getDepartmentInfo(array $department_ids)
    {
        if (empty($department_ids)) {
            return [];
        }

        $department_data    = array_chunk($department_ids, 1000);
        $department_all_arr = [];
        foreach ($department_data as $department_data_ids) {
            $department_arr = SysDepartmentModel::find(
                ['conditions' => 'id in ({ids:array})',
                 'bind'       => ['ids' => $department_data_ids],
                 'columns'    => ['id', 'name']
                ])->toArray();
            if (!empty($department_arr)) {
                $department_all_arr = array_merge($department_all_arr, $department_arr);
            }
        }
        return array_column($department_all_arr, 'name', 'id');

    }

    /**
     * 配置权限添加用户
     * @param array $params 请求入参
     * @param array $user 当前登录用户
     * @return array
     */
    public function add(array $params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $config_power = DataPermissionModuleConfigPowerModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: and cid = :cid:',
                'bind'       => [
                    'staff_info_id' => $params['staff_info_id'],
                    'cid'           => $params['id']
                ]
            ]);

            if (!empty($config_power)) {
                throw new ValidationException(self::$t['data_permission_module_config_power_exist'], ErrCode::$VALIDATE_ERROR);
            }
            $staff_info = HrStaffInfoModel::findFirst([
                'conditions' => 'state = :state: and wait_leave_state = :wait_leave_state: and staff_info_id = :staff_info_id:',
                'bind'       => [
                    'state'            => StaffInfoEnums::STAFF_STATE_IN,
                    'wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO,
                    'staff_info_id'    => $params['staff_info_id']
                ]
            ]);
            if (empty($staff_info)) {
                throw new ValidationException(self::$t['data_permission_module_config_power_staff_info_null'], ErrCode::$VALIDATE_ERROR);
            }
            $data       = [
                'cid'                   => $params['id'],
                'staff_info_id'         => $params['staff_info_id'],
                'staff_department_id'   => $staff_info->node_department_id,
                'manage_department_ids' => '',
                'operator_id'           => $user['id'],
                'created_at'            => date('Y-m-d H:i:s', time())
            ];
            $main_model = new DataPermissionModuleConfigPowerModel();
            $bool       = $main_model->i_create($data);
            if ($bool === false) {
                throw new BusinessException('配置权限添加用户失败: ' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($main_model), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_ADD_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('data_permission_module_config_power_add failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => true
        ];
    }

    /**
     * 删除
     * @param array $params 请求入参数
     * @param array $user 当前登录用户
     * @return array
     */
    public function del(array $params, array $user): array
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $power_data = $this->checkPowerExist($params['id']);
            //记录上传前数据

            $this->logger->info('data_permission_module_config_power_del_data : ' . json_encode($power_data->toArray(), JSON_UNESCAPED_UNICODE) . '删除人:' . $user['id']);
            $bool = $power_data->delete();
            if ($bool === false) {
                throw new BusinessException('删除配置权限数据失败: ' . '; 可能存在的问题: ' . get_data_object_error_msg($power_data), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_DEL_ERROR);
            }
            $manage_data     = DataPermissionManagePowerListModel::find([
                'conditions' => 'pid = :pid:',
                'bind'       => [
                    'pid' => $power_data->id
                ]
            ]);
            $manage_data_arr = $manage_data->toArray();
            $this->logger->info('data_permission_module_config_manage_power_del_data :' . json_encode($manage_data_arr, JSON_UNESCAPED_UNICODE) . '删除人:' . $user['id']);

            if (!empty($manage_data_arr)) {
                $manage_bool = $manage_data->delete();
                if ($manage_bool === false) {
                    throw new BusinessException('管辖部门权限数据失败: ' . '; 可能存在的问题: ' . get_data_object_error_msg($manage_data), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_DEL_ERROR);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('data_permission_module_config_power_del failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }


    /**
     * 保存
     * @param array $params 请求入参
     * @param array $user 当前登录用户
     * @return array
     */
    public function save(array $params, array $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';
        $result       = true;
        $current_time = date('Y-m-d H:i:s', time());
        $db           = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $power_data      = $this->checkPowerExist($params['id']);
            $manage_data     = DataPermissionManagePowerListModel::find([
                'conditions' => 'pid = :pid:',
                'bind'       => [
                    'pid' => $params['id']
                ]
            ]);
            $manage_data_arr = $manage_data->toArray();
            if (!empty($manage_data_arr)) {
                $this->logger->info('编辑保存数据记录,管辖部门权限数据 :' . json_encode($manage_data_arr, JSON_UNESCAPED_UNICODE) . '删除人:' . $user['id']);
                $manage_bool = $manage_data->delete();
                if ($manage_bool === false) {
                    throw new BusinessException('管辖部门权限数据失败: ' . json_encode($manage_data_arr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($manage_data), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_SAVE_ERROR);
                }
            }
            $department_ids = implode(',', array_column($params['data'], 'department_id'));

            $this->logger->info('配置权限数据: ' . json_encode($power_data->toArray(), JSON_UNESCAPED_UNICODE) . '删除人:' . $user['id']);
            $power_arr      = [
                'manage_department_ids' => $department_ids,
                'updated_id'            => $user['id'],
                'updated_at'            => $current_time

            ];
            $bool           = $power_data->i_update($power_arr);
            if ($bool === false) {
                throw new BusinessException('配置权限数据修改失败:' . '; 可能存在的问题: ' . get_data_object_error_msg($power_data), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_SAVE_ERROR);
            }

            if (!empty($params['data'])) {
                foreach ($params['data'] as $item) {
                    $manage[] = [
                        'pid'            => $params['id'],
                        'staff_info_id'  => $power_data->staff_info_id,
                        'department_id'  => $item['department_id'],
                        'is_include_sub' => $item['is_include_sub'],
                        'operator_id'    => $user['id'],
                        'created_at'     => $current_time
                    ];
                }
                $manage_obj = new DataPermissionManagePowerListModel();
                if (!empty($manage) && !$manage_obj->batch_insert($manage)) {
                    throw new BusinessException('管辖部门权限数据添加失败: ' . json_encode($manage, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($manage_obj), ErrCode::$DATA_PERMISSION_MODULE_CONFIG_POWER_SAVE_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('data_permission_manage_power_list_save failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result
        ];
    }

    /**
     * 编辑选中的数据
     * @param array $params 请求入参
     * @return array
     */
    public function detail(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $power_data     = $this->checkPowerExist($params['id']);
            $department_ids = array_values(array_filter(explode(',', $power_data->manage_department_ids)));
            if (!empty($department_ids)) {

                //过滤删除的部门
                $department_arr = $this->getDepartment($department_ids);
                $department_ids = array_values(array_unique(array_column($department_arr, 'id')));
                if (!empty($department_ids)) {
                    //获取正常部门的管辖部门及其上级部门
                    $list = DataPermissionManagePowerListModel::find([
                        'conditions' => ' pid = :pid: and department_id in ({department_ids:array}) ',
                        'bind'       => ['pid' => $params['id'], 'department_ids' => $department_ids]
                    ])->toArray();

                    $dept_ids             = $this->getDepartment($department_ids);
                    $ancestry             = array_column($dept_ids, 'ancestry', 'id');
                    $ancestry_ids         = array_column($dept_ids, 'ancestry');
                    $total_department_ids = array_values(array_unique(array_merge($department_ids, $ancestry_ids)));
                    $department_info      = $this->getDepartment($total_department_ids);
                    $department_arr       = array_column($department_info, 'name', 'id');
                    foreach ($list as &$v) {
                        $v['name']          = $department_arr[$v['department_id']] ?? '';
                        $v['ancestry_name'] = $department_arr[$v['department_id']] ?? '';
                        $v['ancestry']      = $ancestry[$v['department_id']] ?? '';
                        $v['ancestry_name'] = $department_arr[$v['ancestry']] ?? '';
                    }
                }
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('financial-manage-detail-failed:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [
                'items' => $list ?? [],
            ]
        ];
    }

    /**
     * 获取部门数据
     * @param array $department_ids 部门id集合
     * @return array
     */
    public function getDepartment(array $department_ids)
    {
        return SysDepartmentModel::find([
            'conditions' => 'id in ({ids:array}) and deleted = :deleted: ',
            'bind'       => [
                'ids'     => $department_ids,
                'deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns'    => 'id, name, ancestry'
        ])->toArray();
    }

    /**
     * 检验配置权限数据是否存在
     * @param int $id 部门id集合
     * @return object
     */
    public function checkPowerExist(int $id)
    {
        $config_power = DataPermissionModuleConfigPowerModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => [
                'id' => $id
            ]
        ]);
        if (empty($config_power)) {
            throw new ValidationException(self::$t['data_permission_module_config_power_id_error'], ErrCode::$VALIDATE_ERROR);
        }
        return $config_power;
    }


    /**
     * 校验输入的部门是否有权限查询
     *
     * @param int $department_id 选择的部门
     * @param array $user 当前登录人数据
     * @param string $module_key 模块标识
     * @return bool
     * @throws ValidationException
     */
    public function queryAuthority(int $department_id, array $user, string $module_key)
    {
        // 找该模块的数据配置开关
        $module_info = SysModuleRepository::getInstance()->getOneByKey($module_key);
        if (empty($module_info) || $module_info->data_permission_status != SysConfigEnums::DATA_PERMISSION_STATUS_YES) {
            $department_arr = [];
        } else {
            // 获取业务模块管辖人的管辖部门
            $department_arr = $this->getDepartmentIdsByModuleAndStaff($module_info->id, $user['id'], SysConfigEnums::DATA_PERMISSION_APPLICATION_SCENES_ENABLED);
        }

        //当没有配置的时候 或管辖部门没有配置数据的时候走当前用户所属部门的子部门
        if (empty($department_arr)) {
            $department_arr = $this->getDepartmentChild($user['node_department_id']);
        }

        if (!in_array($department_id, $department_arr)) {
            throw new ValidationException(self::$t['setting_no_operation_permissions'], ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 获取用户下的子部门
     * @param int $department_id 部门
     * @return array
     */
    public function getDepartmentChild(int $department_id)
    {
        $department_service = new DepartmentService();
        $department_ids     = $department_service->getChildrenListByDepartmentIdV2($department_id, true);
        array_push($department_ids, $department_id);
        return $department_ids;
    }


    /**
     * 获取新增工号的部门变更的数据 脚本使用
     * @return array
     */
    public function getDepartmentChangeData()
    {
        $data            = [];
        $config_power = DataPermissionModuleConfigPowerModel::find([
            'conditions' => 'staff_department_id > :staff_department_id:',
            'bind'       => [
                'staff_department_id' => 0
            ],
            'columns'    => 'staff_info_id, staff_department_id, cid'
        ])->toArray();

        if (empty($config_power)) {
            return $data;
        }
        $staff_info_data = array_chunk($config_power, 500);

        // 获取数据配置的应用模块
        $all_module_config = DataPermissionModuleConfigRepository::getInstance()->getAllModuleConfig();
        $all_module_config = array_column($all_module_config, 'module_name', 'id');

        //分配处理
        $department_repository = new DepartmentRepository();
        foreach ($staff_info_data as $staff_info_ids) {
            //分批查询
            $staff_data          = HrStaffInfoModel::find(
                ['conditions' => 'staff_info_id in ({ids:array})',
                 'bind'       => ['ids' => array_values(array_unique(array_column($staff_info_ids, 'staff_info_id')))],
                 'columns'    => ['staff_info_id', 'node_department_id', 'name']
                ])->toArray();
            $staff_arr           = array_column($staff_data, 'node_department_id', 'staff_info_id');
            $department_ids      = array_values(array_unique(array_merge($staff_arr, array_column($staff_info_ids, 'staff_department_id'))));
            $department_name_arr = array_column($department_repository->getDepartmentByIds($department_ids, 2), 'name', 'id');
            foreach ($staff_info_ids as $staff_info) {
                $staff_dept_id = $staff_arr[$staff_info['staff_info_id']] ?? '';
                if (empty($staff_dept_id) || $staff_info['staff_department_id'] != $staff_dept_id) {
                    $staff_info['module_type']               = $all_module_config[$staff_info['cid']] ?? '';
                    $staff_info['staff_department_name']     = $department_name_arr[$staff_info['staff_department_id']] . '(' . $staff_info['staff_department_id'] . ')';
                    $staff_info['new_staff_department_name'] = $department_name_arr[$staff_dept_id] . '(' . $staff_dept_id . ')';
                    $data[]                                  = $staff_info;
                }
            }
        }
        return $data;
    }

    /**
     * 获取指定模块指定管辖人的管辖范围
     *
     * @param string $module_key 业务模块标识
     * @param int $staff_id 管辖人工号
     * @return array
     */
    public function getDataConfigPermission(string $module_key, int $staff_id)
    {
        if (empty($module_key) || empty($staff_id)) {
            return [];
        }

        // 找该模块的数据配置开关
        $module_info = SysModuleRepository::getInstance()->getOneByKey($module_key);
        if (empty($module_info) || $module_info->data_permission_status != SysConfigEnums::DATA_PERMISSION_STATUS_YES) {
            return [];
        }

        // 获取业务模块管辖人的管辖部门
        return $this->getDepartmentIdsByModuleAndStaff($module_info->id, $staff_id, SysConfigEnums::DATA_PERMISSION_APPLICATION_SCENES_ALL);
    }

    /**
     * 获取指定业务模块指定管辖人的管辖范围
     *
     * @param int $module_id 业务模块ID
     * @param int $staff_id 管辖人工号
     * @param int $data_permission_application_scense 管辖权限应用场景: 1-所有管辖范围, 部门含已删除; 2-有效管辖范围, 部门是未删除的
     * @return array
     */
    protected function getDepartmentIdsByModuleAndStaff(int $module_id, int $staff_id, int $data_permission_application_scense = 1)
    {
        // 优先取缓存
        $cache_key = md5(self::DATA_PERMISSION_APPLICATION_CACHE_PREFIX . $module_id . '_' . $staff_id . '_' . $data_permission_application_scense);
        $cache_data = $this->getCache($cache_key);
        if ($cache_data !== false) {
           return json_decode($cache_data);
        }

        // 获取管辖人管辖范围
        $dept_list = DataPermissionModuleConfigRepository::getInstance()->getDepartmentListByStaffIds($module_id, [$staff_id]);
        $dept_ids = $this->getSubDepartmentsByManageDepartment($dept_list[$staff_id] ?? [], $data_permission_application_scense);

        // 加缓存
        $this->setCache($cache_key, json_encode($dept_ids), self::DATA_PERMISSION_APPLICATION_CACHE_DURATION);
        return $dept_ids;
    }

    /**
     * 找管辖部门下的子部门
     *
     * @param array $dept_list
     * @param int $data_permission_application_scense 管辖权限应用场景: 1-所有管辖范围, 含已删除部门; 2-有效管辖范围, 未删除的部门
     * @return array
     */
    protected function getSubDepartmentsByManageDepartment(array $dept_list = [], int $data_permission_application_scense = 2)
    {
        if (empty($dept_list)) {
            return [];
        }

        // 获取所有部门
        static $all_dept_id_deleted_map = null;
        static $all_dept_id_v3_map = null;
        if (is_null($all_dept_id_deleted_map) || is_null($all_dept_id_v3_map)) {
            $all_dept_list = (new DepartmentRepository())->getAllDepartmentList('id, ancestry_v3, deleted');
            $all_dept_id_deleted_map = array_column($all_dept_list, 'deleted', 'id');
            $all_dept_id_v3_map = array_column($all_dept_list, 'ancestry_v3', 'id');
            unset($all_dept_list);
        }

        $dept_ids = [];
        foreach ($dept_list as $dept) {
            $dept_ids[] = (int)$dept['department_id'];

            // 含子部门
            if ($dept['is_include_sub'] == SettingEnums::IS_INCLUDE_SUB) {
                $sub_dept_ids = get_sub_department_ids($all_dept_id_v3_map[$dept['department_id']], $all_dept_id_v3_map);
                if (empty($sub_dept_ids)) {
                    continue;
                }

                $dept_ids = array_merge($dept_ids, $sub_dept_ids);
            }
        }

        $dept_ids = array_unique(array_filter($dept_ids));

        // 只要有效的管辖部门, 剔除删除态的部门
        if ($data_permission_application_scense == SysConfigEnums::DATA_PERMISSION_APPLICATION_SCENES_ENABLED) {
            foreach ($dept_ids as $k => $id) {
                if ($all_dept_id_deleted_map[$id] == GlobalEnums::IS_DELETED) {
                    unset($dept_ids[$k]);
                }
            }
        }

        return array_values($dept_ids);
    }

    /**
     * 过滤指定组织部门在某管辖范围内得所属管辖人
     *
     * @param int $department_id 指定的组织部门
     * @param array $staff_ids 指定的管辖人
     * @param string $module_key 业务模块标识
     * @return array
     */
    public function filterStaffIdsByDepartmentId(int $department_id, array $staff_ids, string $module_key)
    {
        if (empty($department_id) || empty($staff_ids) || empty($module_key)) {
            return [];
        }

        // 找该模块的数据配置开关
        $module_info = SysModuleRepository::getInstance()->getOneByKey($module_key);
        if (empty($module_info) || $module_info->data_permission_status != SysConfigEnums::DATA_PERMISSION_STATUS_YES) {
            return [];
        }

        // 获取管辖人管辖范围
        $staff_dept_list = DataPermissionModuleConfigRepository::getInstance()->getDepartmentListByStaffIds($module_info->id, $staff_ids);

        // 过滤后的管辖人
        $result_staff_ids = [];
        foreach ($staff_dept_list as $staff_id => $dept_list) {
            $dept_ids = $this->getSubDepartmentsByManageDepartment($dept_list);
            if (in_array($department_id, $dept_ids)) {
                $result_staff_ids[] = $staff_id;
            }
        }

        return $result_staff_ids;
    }


}
