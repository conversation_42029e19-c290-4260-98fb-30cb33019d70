<?php
/**
 * 数据配置 - 通用数据配置
 */

namespace App\Modules\Setting\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SettingEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\oa\DataPermissionStaffManageDepartmentListModel;
use App\Models\oa\DataPermissionStaffModuleRelModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Repository\oa\DataPermissionCategoryRepository;
use App\Repository\oa\DataPermissionStaffManageDepartmentListRepository;
use App\Repository\oa\DataPermissionStaffModuleRelRepository;
use App\Repository\oa\SysModuleRepository;
use App\Repository\oa\DataPermissionJobModuleRelRepository;
use App\Repository\HrJobTitleRepository;
use App\Models\oa\DataPermissionJobModuleRelModel;
use Exception;

class CommonDataPermissionService extends BaseService
{
    // 参数校验规则: 职位/管辖人配置列表
    public static $validate_config_list = [
        'permission_category_id' => 'Required|IntGt:0|>>>:param error[permission_category_id]',
    ];

    // 参数校验规则: 职位/管辖人配置详情
    public static $validate_config_detail = [
        'id'                     => 'Required|IntGt:0|>>>:param error[id]',
        'permission_category_id' => 'Required|IntGt:0|>>>:param error[permission_category_id]',
    ];

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 添加职位配置的校验规则
     *
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    static function validateAddJobConfigParams(array $params = [])
    {
        // 接入了通用数据权限的业务模块数量
        $module_count = SysModuleRepository::getInstance()->getModuleTotalByCommonDataPermissionStatus(SysConfigEnums::DATA_PERMISSION_STATUS_YES);

        // 根据职位配置的权限分类
        $category_ids_by_job = DataPermissionCategoryRepository::getInstance()->getListByConfigureReference(SettingEnums::DATA_PERMISSION_CATEGORY_CONFIGURE_REFERENCE_JOB);

        $validate_rules = [
            'permission_category_id' => 'Required|IntIn:' . implode(',',
                    $category_ids_by_job) . '|>>>:param error[permission_category_id]',
            'job_ids'                => 'Required|ArrLenGeLe:1,50|>>>:param error[job_ids]',
            'job_ids[*]'             => 'Required|IntGt:0|>>>:param error[job_ids]',
            'module_ids'             => 'Required|ArrLenGeLe:1,' . $module_count . '|>>>:param error[module_ids]',
            'module_ids[*]'          => 'Required|IntGt:0|>>>:param error[module_ids]',
        ];

        Validation::validate($params, $validate_rules);
    }

    /**
     * 更新职位配置的校验规则
     *
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    static function validateUpdateJobConfigParams(array $params = [])
    {
        // 接入了通用数据权限的业务模块数量
        $module_count = SysModuleRepository::getInstance()->getModuleTotalByCommonDataPermissionStatus(SysConfigEnums::DATA_PERMISSION_STATUS_YES);

        $validate_rules = [
            'id'                     => 'Required|IntGt:0|>>>:param error[id]',
            'permission_category_id' => 'Required|IntGt:0|>>>:param error[permission_category_id]',
            'job_id'                 => 'Required|IntGt:0|>>>:param error[job_id]',
            'module_ids'             => 'Required|ArrLenGeLe:1,' . $module_count . '|>>>:param error[module_ids]',
            'module_ids[*]'          => 'Required|IntGt:0|>>>:param error[module_ids]',
        ];

        Validation::validate($params, $validate_rules);
    }

    /**
     * 添加管辖人的校验规则
     *
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    static function validateAddStaffParams(array $params = [])
    {
        // 根据管辖人配置的权限分类
        $category_ids_by_staff = DataPermissionCategoryRepository::getInstance()->getListByConfigureReference(SettingEnums::DATA_PERMISSION_CATEGORY_CONFIGURE_REFERENCE_STAFF);

        $validate_rules = [
            'staff_info_id'          => 'Required|IntGt:0|>>>:param error[staff_info_id]',
            'permission_category_id' => 'Required|IntIn:' . implode(',',
                    $category_ids_by_staff) . '|>>>:param error[permission_category_id]',
        ];

        Validation::validate($params, $validate_rules);
    }

    /**
     * 保存管辖人-管辖范围配置的校验规则
     *
     * @param array $params
     * @return void
     * @throws ValidationException
     */
    static function validateSaveStaffConfigParams(array $params = [])
    {
        // 接入了通用数据权限的业务模块数量
        $module_count = SysModuleRepository::getInstance()->getModuleTotalByCommonDataPermissionStatus(SysConfigEnums::DATA_PERMISSION_STATUS_YES);

        // 根据管辖人配置的权限分类
        $category_ids_by_staff = DataPermissionCategoryRepository::getInstance()->getListByConfigureReference(SettingEnums::DATA_PERMISSION_CATEGORY_CONFIGURE_REFERENCE_STAFF);

        $validate_rules = [
            'id'                     => 'Required|IntGt:0|>>>:param error[id]',
            'staff_info_id'          => 'Required|IntGt:0|>>>:param error[staff_info_id]',
            'permission_category_id' => 'Required|IntIn:' . implode(',',
                    $category_ids_by_staff) . '|>>>:param error[permission_category_id]',
            'manage_department_list' => 'Required|Arr|>>>:param error[manage_department_list]',
            'module_ids'             => 'Required|ArrLenGeLe:1,' . $module_count . '|>>>:param error[module_ids]',
            'module_ids[*]'          => 'Required|IntGt:0|>>>:param error[module_ids]',
        ];

        if (!empty($params['manage_department_list'])) {
            $validate_rules['manage_department_list[*].department_id']  = 'Required|IntGt:0|>>>:param error[department_id]';
            $validate_rules['manage_department_list[*].is_include_sub'] = 'Required|IntIn:0,1|>>>:param error[is_include_sub]';
        }

        Validation::validate($params, $validate_rules);
    }

    /**
     * 职位配置列表
     *
     * @param array $params
     * @return array
     */
    public function getJobConfigList(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'category_info' => [],
            'items'         => [],
        ];

        try {
            // 获取权限分类信息
            $category_info = DataPermissionCategoryRepository::getInstance()->getInfo($params['permission_category_id']);
            if (!empty($category_info)) {
                $data['category_info'] = [
                    'id'            => $category_info['id'],
                    'category_name' => $category_info['category_name'],
                ];

                // 获取该权限分类下的职位模块配置列表
                $item = DataPermissionJobModuleRelRepository::getInstance()->getListBySearch($params['permission_category_id'],
                    $params['job_name']);

                foreach ($item as &$value) {
                    // 获取启用状态的业务模块
                    $value['module_item'] = $this->handleApplicationModuleItem($value['module_ids']);
                    unset($value['module_ids']);
                }

                $data['items'] = $item;
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-职位模块配置列表-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 职位配置详情
     *
     * @param int $id 职位模块配置ID
     * @param int $permission_category_id 权限分类ID
     * @return array
     */
    public function getJobConfigDetail(int $id, int $permission_category_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            // 获取职位配置详情
            $data = DataPermissionJobModuleRelRepository::getInstance()->getDetail($id, $permission_category_id);
            if (empty($data)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 解析启用状态的业务模块名称
            $data['module_item'] = $this->handleApplicationModuleItem($data['module_ids']);
            unset($data['module_ids']);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-职位模块配置详情-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 更新职位与各业务模块的权限
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function updateJobModulePermission($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $exist_rel_model = DataPermissionJobModuleRelRepository::getInstance()->getOne($params['id']);
            if (empty($exist_rel_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($exist_rel_model->permission_category_id != $params['permission_category_id'] || $exist_rel_model->job_id != $params['job_id']) {
                throw new ValidationException(static::$t->_('main_data_and_cate_job_not_match_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 获取启用中的职位名称
            $job_info = (new HrJobTitleRepository())->getJobTitleInfo($params['job_id']);
            if (empty($job_info)) {
                throw new ValidationException(static::$t->_('exist_not_enabled_job_error_hint'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('职位数据权限-更新前:' . json_encode($exist_rel_model->toArray(), JSON_UNESCAPED_UNICODE));

            $exist_rel_model->job_name   = $job_info->job_name;
            $exist_rel_model->module_ids = implode(',', array_unique($params['module_ids']));
            $exist_rel_model->updated_id = $user['id'];
            $exist_rel_model->updated_at = date('Y-m-d H:i:s');

            $this->logger->info('职位数据权限-更新后:' . json_encode($exist_rel_model->toArray(), JSON_UNESCAPED_UNICODE));
            if ($exist_rel_model->save() === false) {
                throw new BusinessException('职位与模块关系更新失败,原因可能是:' . get_data_object_error_msg($exist_rel_model),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-更新职位模块权限-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-更新职位模块权限-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 删除职位与各业务模块的权限
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function removeJobModulePermission($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $exist_rel_model = DataPermissionJobModuleRelRepository::getInstance()->getOne($params['id']);
            if (empty($exist_rel_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($exist_rel_model->permission_category_id != $params['permission_category_id']) {
                throw new ValidationException(static::$t->_('main_data_and_cate_job_not_match_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info("职位数据权限-操作人:{$user['id']}-删除前数据:" . json_encode($exist_rel_model->toArray(),
                    JSON_UNESCAPED_UNICODE));

            if ($exist_rel_model->delete() === false) {
                throw new BusinessException('职位与模块关系删除失败,原因可能是:' . get_data_object_error_msg($exist_rel_model),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-删除职位模块权限-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-删除职位模块权限-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 添加职位与各业务模块的权限
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function addJobModulePermission($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $permission_category_id = $params['permission_category_id'];
            $job_ids                = array_unique($params['job_ids']);
            $module_ids             = implode(',', array_unique($params['module_ids']));

            // 获取启用中的职位名称
            $job_list = (new HrJobTitleRepository())->getJobTitleByIds($job_ids);
            if (count($job_list) != count($job_ids)) {
                throw new ValidationException(static::$t->_('exist_not_enabled_job_error_hint'),
                    ErrCode::$VALIDATE_ERROR);
            }
            $job_list = array_column($job_list, 'job_name', 'id');

            // 查询已配置过的职位
            $exist_rel_models = DataPermissionJobModuleRelRepository::getInstance()->getListByCategoryJobIds($permission_category_id,
                $job_ids);
            $exist_rel_array  = $exist_rel_models->toArray();

            // 更新已配置的职位模块权限
            if (!empty($exist_rel_array)) {
                // 重置待新增的职位
                $job_ids = array_diff($job_ids, array_column($exist_rel_array, 'job_id'));

                $this->logger->info('职位数据权限-更新前:' . json_encode($exist_rel_array, JSON_UNESCAPED_UNICODE));
                foreach ($exist_rel_models as $model) {
                    $model->job_name   = $job_list[$model->job_id];
                    $model->module_ids = $module_ids;
                    $model->updated_id = $user['id'];
                    $model->updated_at = date('Y-m-d H:i:s');

                    $this->logger->info('职位数据权限-更新后:' . json_encode($model->toArray(), JSON_UNESCAPED_UNICODE));
                    if ($model->save() === false) {
                        throw new BusinessException('职位与模块关系更新失败,原因可能是:' . get_data_object_error_msg($model),
                            ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            // 未配置过的写入
            if (!empty($job_ids)) {
                $batch_add_data = [];
                foreach ($job_ids as $job_id) {
                    $batch_add_data[] = [
                        'permission_category_id' => $permission_category_id,
                        'job_id'                 => $job_id,
                        'job_name'               => $job_list[$job_id],
                        'module_ids'             => $module_ids,
                        'created_id'             => $user['id'],
                        'created_at'             => date('Y-m-d H:i:s'),
                        'updated_id'             => $user['id'],
                        'updated_at'             => date('Y-m-d H:i:s'),
                    ];
                }

                $this->logger->info('职位数据权限-批量添加:' . json_encode($batch_add_data, JSON_UNESCAPED_UNICODE));

                $batch_add_res = (new DataPermissionJobModuleRelModel())->batch_insert($batch_add_data);
                if ($batch_add_res === false) {
                    throw new BusinessException('职位与模块批量添加失败', ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-添加职位模块权限-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-添加职位模块权限-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }


    /**
     * 获取权限分类列表
     */
    public function getCategoryList()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            $data = DataPermissionCategoryRepository::getInstance()->getAllList();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-权限分类列表-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取通用数据配置模块的静态枚举配置
     */
    public function getStaticEnums()
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            // 1. 获取对接了通用数据权限的业务模块列表
            $data['module_item'] = SysModuleRepository::getInstance()->getModuleByCommonDataPermissionStatus(SysConfigEnums::DATA_PERMISSION_STATUS_YES);

            // 2. 配置状态
            foreach (SettingEnums::$configure_state as $k => $v) {
                $data['configure_status'][] = [
                    'value' => (string)$k,
                    'label' => static::$t->_($v),
                ];
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-获取静态枚举配置-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 添加分类权限下的管辖人
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function addStaffByPermissionCategory($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 获取管辖人基本信息
            $staff_info = (new HrStaffRepository())->getStaffById($params['staff_info_id']);
            if (empty($staff_info)) {
                throw new ValidationException(static::$t->_('re_staff_info_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 仅可添加在职且非待离职的员工
            if ($staff_info['state'] != StaffInfoEnums::STAFF_STATE_IN || $staff_info['wait_leave_state'] != StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO) {
                throw new ValidationException(static::$t->_('staff_state_and_wait_leave_state_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $model = new DataPermissionStaffModuleRelModel();

            $add_data = [
                'permission_category_id' => $params['permission_category_id'],
                'staff_info_id'          => $params['staff_info_id'],
                'node_department_id'     => $staff_info['node_department_id'],
                'configure_status'       => SettingEnums::CONFIGURE_STATE_1,
                'created_id'             => $user['id'],
                'created_at'             => date('Y-m-d H:i:s'),
                'updated_id'             => $user['id'],
                'updated_at'             => date('Y-m-d H:i:s'),
            ];

            $this->logger->info('添加管辖人:' . json_encode($add_data, JSON_UNESCAPED_UNICODE));
            if ($model->save($add_data) === false) {
                throw new BusinessException('添加管辖人,原因可能是:' . get_data_object_error_msg($model),
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-添加管辖人-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-添加管辖人-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 管辖人配置列表
     *
     * @param array $params
     * @return array
     */
    public function getStaffConfigList(array $params)
    {
        // 分页参数
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : (int)$params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : (int)$params['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        // 返回的数据结构
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'category_info' => [],
            'items'         => [],
            'pagination'    => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            // 获取权限分类信息
            $category_info = DataPermissionCategoryRepository::getInstance()->getInfo($params['permission_category_id']);
            if (!empty($category_info)) {
                $data['category_info'] = [
                    'id'            => $category_info['id'],
                    'category_name' => $category_info['category_name'],
                ];

                // 该权限分类下的管辖人数量
                $count = DataPermissionStaffModuleRelRepository::getInstance()->getTotalBySearch($params);

                $items = [];
                if ($count) {
                    $builder = $this->modelsManager->createBuilder();
                    $builder->columns([
                        'id',
                        'permission_category_id',
                        'staff_info_id',
                        'node_department_id',
                        'module_ids',
                        'manage_department_ids',
                        'configure_status',
                    ]);

                    $builder->from(DataPermissionStaffModuleRelModel::class);
                    $builder->where('permission_category_id = :permission_category_id:',
                        ['permission_category_id' => $params['permission_category_id']]);

                    if (!empty($params['staff_info_id'])) {
                        $builder->andWhere('staff_info_id = :staff_info_id:',
                            ['staff_info_id' => $params['staff_info_id']]);
                    }

                    if (!empty($params['node_department_id'])) {
                        $builder->andWhere('node_department_id = :node_department_id:',
                            ['node_department_id' => $params['node_department_id']]);
                    }

                    if (!empty($params['configure_status'])) {
                        $builder->andWhere('configure_status = :configure_status:',
                            ['configure_status' => $params['configure_status']]);
                    }

                    $builder->limit($page_size, $offset);
                    $items = $builder->getQuery()->execute()->toArray();

                    // 格式化列表数据
                    $items = $this->handleStaffConfigList($items);
                }

                $data['items']                     = $items;
                $data['pagination']['total_count'] = $count;
            }
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-管辖人管辖范围列表-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 格式化管辖人列表数据
     *
     * @param array $items
     * @return array
     */
    public function handleStaffConfigList(array $items = [])
    {
        if (empty($items)) {
            return [];
        }

        // 解析员工信息
        $staff_ids   = array_values(array_unique(array_column($items, 'staff_info_id')));
        $staff_items = (new HrStaffRepository())->getStaffListByStaffIds($staff_ids);

        // 解析部门名称
        $all_department_ids   = array_merge(array_column($items, 'node_department_id'),
            array_column($items, 'manage_department_ids'));
        $all_department_ids   = explode(',', implode(',', $all_department_ids));
        $all_department_items = (new DepartmentRepository())->getDepartmentByIds($all_department_ids, 2);
        $all_department_items = array_column($all_department_items, 'name', 'id');

        // 列表处理
        foreach ($items as &$item) {
            // 员工信息
            $item['staff_name']           = $staff_items[$item['staff_info_id']]['name'] ?? '';
            $item['staff_nick_name']      = $staff_items[$item['staff_info_id']]['nick_name'] ?? '';
            $item['node_department_name'] = $all_department_items[$item['node_department_id']] ?? '';

            // 管辖部门
            $manage_department_ids  = explode(',', $item['manage_department_ids']);
            $manage_department_item = [];
            foreach ($manage_department_ids as $id) {
                $department_name = $all_department_items[$id] ?? null;
                if (!is_null($department_name)) {
                    $manage_department_item[] = [
                        'id'   => $id,
                        'name' => $department_name,
                    ];
                }
            }
            $item['manage_department_item'] = $manage_department_item;

            // 解析启用状态的业务模块名称
            $item['module_item'] = $this->handleApplicationModuleItem($item['module_ids']);

            // 配置状态
            $item['configure_status_text'] = static::$t->_(SettingEnums::$configure_state[$item['configure_status']]);

            // 剔除无用字段
            unset($item['module_ids'], $item['manage_department_ids']);
        }

        return $items;
    }

    /**
     * 解析权限配置已启用的应用模块
     *
     * @param string $permission_module_ids 权限关联的应用模块 id 英文逗号间隔
     * @return array
     */
    private function handleApplicationModuleItem(string $permission_module_ids = '')
    {
        $module_item = [];

        if (empty($permission_module_ids)) {
            return $module_item;
        }

        // 系统已启用通用数据权限的模块
        static $sys_module_items = null;
        if (is_null($sys_module_items)) {
            $sys_module_items = SysModuleRepository::getInstance()->getModuleByCommonDataPermissionStatus(SysConfigEnums::DATA_PERMISSION_STATUS_YES);
            $sys_module_items = array_column($sys_module_items, 'name', 'id');
        }

        $module_ids = explode(',', $permission_module_ids);
        foreach ($module_ids as $id) {
            $module_name = $sys_module_items[$id] ?? null;
            if (!is_null($module_name)) {
                $module_item[] = [
                    'id'   => $id,
                    'name' => $module_name,
                ];
            }
        }

        return $module_item;
    }

    /**
     * 保存管辖人配置权限(管辖范围)
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function saveStaffConfigPermission($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $id                     = $params['id'];
            $module_ids             = array_values(array_unique($params['module_ids']));
            $permission_category_id = $params['permission_category_id'];
            $staff_info_id          = $params['staff_info_id'];
            $manage_department_list = $params['manage_department_list'];

            // 管辖人基本配置
            $exist_base_model = DataPermissionStaffModuleRelRepository::getInstance()->getOne($id);
            if (empty($exist_base_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($exist_base_model->permission_category_id != $permission_category_id || $exist_base_model->staff_info_id != $staff_info_id) {
                throw new ValidationException(static::$t->_('main_data_and_cate_staff_not_match_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 验证员工关联的应用模块是否与该员工其他配置的应用模块存在重叠或交叉
            // 同一个权限分类下的同一个员工的关联部门不可重复或重叠
            // 1. 该员工在同一个应用分类下已配置的应用模块
            $staff_exist_rel_list = DataPermissionStaffModuleRelRepository::getInstance()->getListByPermissionCategoryIdAndStafffId($permission_category_id,
                $staff_info_id);
            foreach ($staff_exist_rel_list as $value) {
                // 2. 当前提交的记录 或 其他记录的应用模块未配置, 则略过
                if ($value['id'] == $id || empty($value['module_ids'])) {
                    continue;
                }

                // 3. 验证提交的应用模块 与 已有模块 是否 存在重叠(求交集)
                $exist_module_ids = explode(',', $value['module_ids']);
                $intersect_ids    = array_intersect($module_ids, $exist_module_ids);
                if (!empty($intersect_ids)) {
                    throw new ValidationException(static::$t->_('save_staff_manage_dept_module_repeat_error',
                        ['repeat_ids' => $value['id'] . ':' . implode(',', $intersect_ids)]), ErrCode::$VALIDATE_ERROR);
                }
            }

            // 管辖人已配置的管辖部门明细
            $manage_department_models = $exist_base_model->getStaffManageDepartmentList();

            $this->logger->info('管辖人管辖范围-保存前[staff_module_rel]:' . json_encode($exist_base_model->toArray(),
                    JSON_UNESCAPED_UNICODE));
            $this->logger->info('管辖人管辖范围-保存前[manage_department_list]:' . json_encode($manage_department_models->toArray(),
                    JSON_UNESCAPED_UNICODE));

            // 清空原管辖部门数据
            if ($manage_department_models->delete() === false) {
                throw new BusinessException('管辖人管辖范围保存失败,原因可能是:' . get_data_object_error_msg($manage_department_models),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 更新管辖人与应用模块关系数据
            $manage_department_ids                   = array_unique(array_column($manage_department_list,
                'department_id'));
            $exist_base_model->module_ids            = implode(',', $module_ids);
            $exist_base_model->manage_department_ids = implode(',', $manage_department_ids);
            $exist_base_model->configure_status      = SettingEnums::CONFIGURE_STATE_2;
            $exist_base_model->updated_id            = $user['id'];
            $exist_base_model->updated_at            = date('Y-m-d H:i:s');

            $this->logger->info('管辖人管辖范围-保存后[staff_module_rel]:' . json_encode($exist_base_model->toArray(),
                    JSON_UNESCAPED_UNICODE));
            if ($exist_base_model->save() === false) {
                throw new BusinessException('管辖人管辖范围保存失败[staff_module_rel],原因可能是:' . get_data_object_error_msg($exist_base_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            // 重新生成管辖人与管辖部门明细
            if (!empty($manage_department_list)) {
                $batch_manage_department_item = [];
                foreach ($manage_department_list as $department) {
                    $batch_manage_department_item[] = [
                        'staff_module_rel_id' => $exist_base_model->id,
                        'staff_info_id'       => $exist_base_model->staff_info_id,
                        'department_id'       => $department['department_id'],
                        'is_include_sub'      => $department['is_include_sub'],
                        'created_id'          => $user['id'],
                        'created_at'          => date('Y-m-d H:i:s'),
                    ];
                }

                $this->logger->info('管辖人管辖范围-保存后[manage_department_list]:' . json_encode($batch_manage_department_item,
                        JSON_UNESCAPED_UNICODE));

                $manage_department_list_model = new DataPermissionStaffManageDepartmentListModel();
                if ($manage_department_list_model->batch_insert($batch_manage_department_item) === false) {
                    throw new BusinessException('管辖人管辖范围保存失败[manage_department_list]', ErrCode::$BUSINESS_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-管辖人管辖范围保存失败-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-管辖人管辖范围保存失败-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 管辖人配置详情
     *
     * @param int $id 职位模块配置ID
     * @param int $permission_category_id 权限分类ID
     * @return array
     */
    public function getStaffConfigDetail(int $id, int $permission_category_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];

        try {
            // 获取管辖人配置详情
            $staff_module_rel_model = DataPermissionStaffModuleRelRepository::getInstance()->getDetail($id,
                $permission_category_id);
            if (empty($staff_module_rel_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]),
                    ErrCode::$VALIDATE_ERROR);
            }
            $data = [
                'id'                        => $staff_module_rel_model->id,
                'permission_category_id'    => $staff_module_rel_model->permission_category_id,
                'staff_info_id'             => $staff_module_rel_model->staff_info_id,
                'module_ids'                => $staff_module_rel_model->module_ids,
                'other_selected_module_ids' => [],// 该员工在其他记录配已配置的应用模块列表
            ];

            // 获取该员工在其他记录配已配置的应用模块列表
            $staff_all_rel_list = DataPermissionStaffModuleRelRepository::getInstance()->getListByPermissionCategoryIdAndStafffId($permission_category_id,
                $staff_module_rel_model->staff_info_id);
            $staff_all_rel_list = array_column($staff_all_rel_list, 'module_ids', 'id');
            unset($staff_all_rel_list[$staff_module_rel_model->id]);// 剔除当前记录关联的应用模块
            if (!empty($staff_all_rel_list)) {
                $data['other_selected_module_ids'] = array_values(array_unique(explode(',',
                    implode(',', $staff_all_rel_list))));
            }

            // 获取启用状态的业务模块
            $data['module_item'] = $this->handleApplicationModuleItem($data['module_ids']);
            unset($data['module_ids']);

            // 获取管辖的部门明细(过滤删除态部门)
            $data['manage_department_list'] = [];
            $manage_department_item         = $staff_module_rel_model->getStaffManageDepartmentList()->toArray();
            if (!empty($manage_department_item)) {
                $manage_department_ids = array_column($manage_department_item, 'department_id');

                // 1. 找当前部门的基本信息
                $department_repository    = new DepartmentRepository();
                $manage_department_detail = $department_repository->getDepartmentByIds($manage_department_ids,
                    GlobalEnums::IS_NO_DELETED);

                // 获取上级部门信息
                $ancestry_department_ids    = array_column($manage_department_detail, 'ancestry', 'id');
                $ancestry_department_detail = $department_repository->getDepartmentByIds(array_values($ancestry_department_ids),
                    GlobalEnums::IS_NO_DELETED);

                // 合并当前部门和上级部门
                $all_manage_department_detail = array_merge($manage_department_detail, $ancestry_department_detail);
                $all_manage_department_detail = array_column($all_manage_department_detail, 'name', 'id');

                // 2. 构造已选部门/组织
                $manage_department_list = [];
                foreach ($manage_department_item as $department) {
                    // 上级部门/组织
                    $ancestry      = $ancestry_department_ids[$department['department_id']] ?? 0;
                    $ancestry_name = $all_manage_department_detail[$ancestry] ?? '';

                    $manage_department_list[] = [
                        'department_id'  => $department['department_id'],
                        'name'           => $all_manage_department_detail[$department['department_id']] ?? '',
                        'is_include_sub' => $department['is_include_sub'],
                        'ancestry'       => $ancestry,
                        'ancestry_name'  => $ancestry_name,
                    ];
                }

                $data['manage_department_list'] = $manage_department_list;
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-管辖人管辖范围详情-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 删除管辖人配置
     *
     * @param $params
     * @param $user
     * @return array
     */
    public function removeStaffConfig($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 管辖人与应用模块关系
            $exist_rel_model = DataPermissionStaffModuleRelRepository::getInstance()->getOne($params['id']);
            if (empty($exist_rel_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $params['id']]),
                    ErrCode::$VALIDATE_ERROR);
            }

            if ($exist_rel_model->permission_category_id != $params['permission_category_id']) {
                throw new ValidationException(static::$t->_('main_data_and_cate_staff_not_match_error'),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 管辖人管辖部门明细
            $manage_department_models = $exist_rel_model->getStaffManageDepartmentList();

            $this->logger->info("管辖人配置[staff_module_rel]-操作人:{$user['id']}-删除前数据:" . json_encode($exist_rel_model->toArray(),
                    JSON_UNESCAPED_UNICODE));
            $this->logger->info("管辖人配置[manage_department_list]-操作人:{$user['id']}-删除前数据:" . json_encode($manage_department_models->toArray(),
                    JSON_UNESCAPED_UNICODE));

            if ($exist_rel_model->delete() === false) {
                throw new BusinessException('删除管辖人配置[staff_module_rel],原因可能是:' . get_data_object_error_msg($exist_rel_model),
                    ErrCode::$BUSINESS_ERROR);
            }

            if ($manage_department_models->delete() === false) {
                throw new BusinessException('删除管辖人配置[manage_department_list],原因可能是:' . get_data_object_error_msg($manage_department_models),
                    ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $db->rollback();

            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('通用数据配置-删除管辖人配置-失败, 原因可能是:' . $e->getMessage());
        } catch (Exception $e) {
            $db->rollback();

            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('通用数据配置-删除管辖人配置-失败, 原因可能是:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
        ];
    }

    /**
     * 业务模块 应用 通用数据权限的公共方法
     *
     * @param array $user 登录用户
     * @param object $builder 业务侧查询构造器
     * @param string $module_key 业务模块唯一标识
     * @param array $table_params 查询表字段 相关配置参数(默认)
     * [
     *     'table_alias_name' => 'main', // 创建人工号字段 和 创建人直属部门字段所在表的别名
     *     'create_id_field' => 'create_id',// 创建人工号字段
     *     'create_node_department_id_filed' => 'create_node_department_id'// 创建人直属部门字段
     * ]
     *
     * @return object
     * @throws BusinessException
     */
    public function getCommonDataPermission(array $user, object $builder, string $module_key, array $table_params)
    {
        // 1. 必备参数校验
        if (empty($user['id']) || empty($user['node_department_id']) || empty($user['job_title_id']) || !is_object($builder) || empty($module_key)) {
            throw new BusinessException('获取通用数据权限-参数异常[user/builder/module_key], 请检查', ErrCode::$BUSINESS_ERROR);
        }

        // v2 多实体的查询条件验证
        if (isset($table_params['type']) && $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_MULTI_ENTIEY) {
            foreach ($table_params['entity_item'] as $entity) {
                if (empty($entity['table_alias_name']) || empty($entity['create_id_field']) || empty($entity['create_node_department_id_filed'])) {
                    throw new BusinessException('获取通用数据权限-参数异常[table_params], 请检查', ErrCode::$BUSINESS_ERROR);
                }
            }
        } else {
            if (empty($table_params['table_alias_name']) || empty($table_params['create_id_field']) || empty($table_params['create_node_department_id_filed'])) {
                // v1 单实体的查询条件验证
                throw new BusinessException('获取通用数据权限-参数异常[table_params], 请检查', ErrCode::$BUSINESS_ERROR);
            }
        }

        // 2. 业务模块通用数据权限 开关 校验
        $sys_module = SysModuleRepository::getInstance()->getOneByKey($module_key);
        if (empty($sys_module)) {
            throw new BusinessException("获取通用数据权限-业务模块未配置[module_key-{$module_key}], 请检查", ErrCode::$BUSINESS_ERROR);
        }

        // 未启用 通用数据权限
        $sys_module = $sys_module->toArray();
        if ($sys_module['common_data_permission_status'] != SysConfigEnums::DATA_PERMISSION_STATUS_YES) {
            return $builder;
        }

        // 3. 通用数据权限 各优先级 校验
        // 权限分类列表: 优先级 高->低
        $permission_category_list = DataPermissionCategoryRepository::getInstance()->getAllList('DESC');

        // 获取符合某优先级的权限分类查询条件
        $handled_builder = null;
        foreach ($permission_category_list as $category) {
            $category_flag    = str_replace(' ', '', ucwords(str_replace('_', ' ', $category['category_flag'])));
            $_method_function = 'handlePermissionCategoryLogicBy' . $category_flag;
            if (!method_exists($this, $_method_function)) {
                throw new BusinessException("获取通用数据权限-该权限分类的处理逻辑未定义[{$category['category_flag']}-{$_method_function}], 请检查",
                    ErrCode::$BUSINESS_ERROR);
            }

            // 按优先级依次获取各自的处理逻辑
            $table_params['module_id']              = $sys_module['id'];
            $table_params['permission_category_id'] = $category['id'];
            $handled_builder                        = $this->$_method_function($user, $builder, $table_params);
            if (is_object($handled_builder)) {
                break;
            }
        }

        return $handled_builder;
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 可见管辖部门下的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object|null
     */
    private function handlePermissionCategoryLogicByDesignatedDepartment(
        array $user,
        object $builder,
        array $table_params
    ) {
        // 获取当前用户管辖部门及子部门
        $manage_dept_list = DataPermissionStaffManageDepartmentListRepository::getInstance()->getListByCategoryStaffModuleId($table_params['permission_category_id'],
            $user['id'], $table_params['module_id']);

        if (empty($manage_dept_list)) {
            return null;
        }

        // 获取部门的子部门
        $all_dept_ids           = [];
        $department_reprository = new DepartmentRepository();
        foreach ($manage_dept_list as $dept) {
            // 含子部门
            if ($dept['is_include_sub'] == SettingEnums::IS_INCLUDE_SUB) {
                // 返回结果带本部门+子部门
                $dept_ids     = $department_reprository->getDepartmentSubListByIds($dept['department_id'],2);
                $dept_ids     = array_column($dept_ids, 'id');
                $all_dept_ids = array_merge($all_dept_ids, $dept_ids);
            } else {
                $all_dept_ids[] = $dept['department_id'];
            }
        }

        // 过滤已删除的
        $all_dept_ids = $department_reprository->getDepartmentByIds($all_dept_ids, 2);
        if (empty($all_dept_ids)) {
            return null;
        }

        $all_dept_ids = array_keys($all_dept_ids);

        // v1 单实体条件
        if (!isset($table_params['type']) || $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_DEFAULT) {
            $builder->inWhere($table_params['table_alias_name'] . '.' . $table_params['create_node_department_id_filed'],
                $all_dept_ids);
        } else {
            // v2 多实体条件, 实体之间 OR
            $where_item = [];
            foreach ($table_params['entity_item'] as $entity) {
                $where_item[] = "{$entity['table_alias_name']}.{$entity['create_node_department_id_filed']} IN ({all_dept_ids:array})";
            }

            $where = implode(' OR ', $where_item);
            $builder->andWhere($where, ['all_dept_ids' => $all_dept_ids]);
        }

        return $builder;
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 可见全公司的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object|null
     */
    private function handlePermissionCategoryLogicByAllCompany(array $user, object $builder, array $table_params)
    {
        // 查看用户职位是否启用状态
        $job_title_info = (new HrJobTitleRepository())->getJobTitleInfo($user['job_title_id'], true);
        if (empty($job_title_info)) {
            return null;
        }

        // 查看用户职位的权限配置
        $job_config_list = DataPermissionJobModuleRelRepository::getInstance()->getListByCategoryJobModuleId($table_params['permission_category_id'],
            $user['job_title_id'], $table_params['module_id']);
        if (empty($job_config_list)) {
            return null;
        }

        return $builder;
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 可见所管辖部门及子部门的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object|null
     */
    private function handlePermissionCategoryLogicByManageDepartment(array $user, object $builder, array $table_params)
    {
        // 1. 当前用户是哪些部门负责人
        $department_reprository = new DepartmentRepository();
        $manage_dept_list       = $department_reprository->getDepartmentListByManagerId($user['id'],2);
        if (empty($manage_dept_list)) {
            return null;
        }

        // 2. 查找负责人所在部门 及 子部门
        $all_dept_ids = [];
        foreach ($manage_dept_list as $dept) {
            // 返回结果带本部门+子部门
            $dept_ids     = $department_reprository->getDepartmentSubListByIds($dept['id'],2);
            $dept_ids     = array_column($dept_ids, 'id');
            $all_dept_ids = array_merge($all_dept_ids, $dept_ids);
        }

        if (empty($all_dept_ids)) {
            return null;
        }

        // v1 单实体条件
        if (!isset($table_params['type']) || $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_DEFAULT) {
            $builder->inWhere($table_params['table_alias_name'] . '.' . $table_params['create_node_department_id_filed'],
                $all_dept_ids);
        } else {
            // v2 多实体条件, 实体之间 OR
            $where_item = [];
            foreach ($table_params['entity_item'] as $entity) {
                $where_item[] = "{$entity['table_alias_name']}.{$entity['create_node_department_id_filed']} IN ({all_dept_ids:array})";
            }

            $where = implode(' OR ', $where_item);
            $builder->andWhere($where, ['all_dept_ids' => $all_dept_ids]);
        }

        return $builder;
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 可见自己所在部门的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object|null
     */
    private function handlePermissionCategoryLogicBySelfNodeDepartment(
        array $user,
        object $builder,
        array $table_params
    ) {
        // 查看用户职位是否启用状态
        $job_title_info = (new HrJobTitleRepository())->getJobTitleInfo($user['job_title_id'], true);
        if (empty($job_title_info)) {
            return null;
        }

        // 查看用户职位的权限配置
        $job_config_list = DataPermissionJobModuleRelRepository::getInstance()->getListByCategoryJobModuleId($table_params['permission_category_id'],
            $user['job_title_id'], $table_params['module_id']);
        if (empty($job_config_list)) {
            return null;
        }

        // 自己所在部门是否有效(未删除)
        $dept_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
        if (empty($dept_info)) {
            return null;
        }

        // v1 单实体条件
        if (!isset($table_params['type']) || $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_DEFAULT) {
            $where = "{$table_params['table_alias_name']}.{$table_params['create_node_department_id_filed']} = :cdp_create_node_department_id_filed:";
        } else {
            // v2 多实体条件, 实体之间 OR
            $where_item = [];
            foreach ($table_params['entity_item'] as $entity) {
                $where_item[] = "{$entity['table_alias_name']}.{$entity['create_node_department_id_filed']} = :cdp_create_node_department_id_filed:";
            }

            $where = implode(' OR ', $where_item);
        }

        return $builder->andWhere($where, ['cdp_create_node_department_id_filed' => $user['node_department_id']]);
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 只可见自己创建的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object
     */
    private function handlePermissionCategoryLogicBySelfCreated(array $user, object $builder, array $table_params)
    {
        // v1 单实体条件
        if (!isset($table_params['type']) || $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_DEFAULT) {
            $where = "{$table_params['table_alias_name']}.{$table_params['create_id_field']} = :cdp_create_id_field:";
        } else {
            // v2 多实体条件, 实体之间 OR
            $where_item = [];
            foreach ($table_params['entity_item'] as $entity) {
                $where_item[] = "{$entity['table_alias_name']}.{$entity['create_id_field']} = :cdp_create_id_field:";
            }

            $where = implode(' OR ', $where_item);
        }

        return $builder->andWhere($where, ['cdp_create_id_field' => $user['id']]);
    }

    /**
     * 自定义 处理不同权限分类的取数逻辑: 可见所在一级部门以下的数据
     *
     * @param array $user 登录用户
     * @param object $builder 查询构造器
     * @param array $table_params 业务表参数
     * @return object
     */
    private function handlePermissionCategoryLogicBySelfSysDepartmentAndAllSub(
        array $user,
        object $builder,
        array $table_params
    ) {
        // 查看用户职位是否启用状态
        $job_title_info = (new HrJobTitleRepository())->getJobTitleInfo($user['job_title_id'], true);
        if (empty($job_title_info)) {
            return null;
        }

        // 查看用户职位的权限配置
        $job_config_list = DataPermissionJobModuleRelRepository::getInstance()->getListByCategoryJobModuleId($table_params['permission_category_id'],
            $user['job_title_id'], $table_params['module_id']);
        if (empty($job_config_list)) {
            return null;
        }

        // 自己所在级部门(未删除)
        $dept_repository = new DepartmentRepository();
        $dept_info       = $dept_repository->getDepartmentDetail($user['node_department_id'],2);
        if (empty($dept_info)) {
            return null;
        }

        // 自己所在部门的一级部门(未删除)
        $first_dept_info = $dept_repository->getFirstLevelDepartmentByAncestryV3($dept_info['ancestry_v3'],2);
        if (empty($first_dept_info)) {
            return null;
        }

        // 找一级部门及其下的所有子部门(未删除)
        $all_dept_ids = $dept_repository->getDepartmentSubListByIds($first_dept_info['id'],2);
        $all_dept_ids = array_column($all_dept_ids, 'id');

        // v1 单实体条件
        if (!isset($table_params['type']) || $table_params['type'] == SettingEnums::DATA_PERMISSION_TYPE_DEFAULT) {
            $builder->inWhere("{$table_params['table_alias_name']}.{$table_params['create_node_department_id_filed']}",
                $all_dept_ids);
        } else {
            // v2 多实体条件, 实体之间 OR
            $where_item = [];
            foreach ($table_params['entity_item'] as $entity) {
                $where_item[] = "{$entity['table_alias_name']}.{$entity['create_id_field']} IN ({all_dept_ids:array})";
            }

            $where = implode(' OR ', $where_item);
            $builder->andWhere($where, ['all_dept_ids' => $all_dept_ids]);
        }

        return $builder;
    }

}
