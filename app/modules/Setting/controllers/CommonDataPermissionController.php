<?php
/**
 * 数据配置 - 通用数据配置
 */

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\CommonDataPermissionService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class CommonDataPermissionController extends BaseController
{
    /**
     * 权限分类列表
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74297
     * @return Response|ResponseInterface
     */
    public function categoryListAction()
    {
        $res = CommonDataPermissionService::getInstance()->getCategoryList();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 静态枚举配置(应用模块 + 配置状态)
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74312
     * @return Response|ResponseInterface
     */
    public function enumsAction()
    {
        $res = CommonDataPermissionService::getInstance()->getStaticEnums();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 职位配置列表
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74352
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function jobConfigListAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        Validation::validate($params, CommonDataPermissionService::$validate_config_list);

        $res = CommonDataPermissionService::getInstance()->getJobConfigList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 职位配置详情
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74357
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function jobConfigDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, CommonDataPermissionService::$validate_config_detail);

        $res = CommonDataPermissionService::getInstance()->getJobConfigDetail($params['id'],
            $params['permission_category_id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 添加职位配置
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74337
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addJobConfigAction()
    {
        $params = $this->request->get();

        CommonDataPermissionService::getInstance()::validateAddJobConfigParams($params);

        $res = CommonDataPermissionService::getInstance()->addJobModulePermission($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 更新职位配置
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74362
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateJobConfigAction()
    {
        $params = $this->request->get();

        CommonDataPermissionService::getInstance()::validateUpdateJobConfigParams($params);

        $res = CommonDataPermissionService::getInstance()->updateJobModulePermission($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 删除职位配置
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74367
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delJobConfigAction()
    {
        $params = $this->request->get();

        Validation::validate($params, CommonDataPermissionService::$validate_config_detail);
        $res = CommonDataPermissionService::getInstance()->removeJobModulePermission($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 添加管辖人
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74382
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addStaffAction()
    {
        $params = $this->request->get();

        Validation::validate($params, CommonDataPermissionService::getInstance()::validateAddStaffParams($params));
        $res = CommonDataPermissionService::getInstance()->addStaffByPermissionCategory($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 管辖人配置列表
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74402
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function staffConfigListAction()
    {
        $params = $this->request->get();

        Validation::validate($params, CommonDataPermissionService::$validate_config_list);

        $res = CommonDataPermissionService::getInstance()->getStaffConfigList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 保存管辖人配置
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74467
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveStaffConfigAction()
    {
        $params = $this->request->get();

        Validation::validate($params,
            CommonDataPermissionService::getInstance()::validateSaveStaffConfigParams($params));

        // 给管辖人加锁, 避免多个人同时对同一个管辖人设置关系范围, 造成数据错乱
        $lock_key = md5(RedisKey::COMMON_DATA_PERMISSION_SAVE_STAFF_CONFIG_LOCK_PREFIX . $params['permission_category_id'] . '_' . $params['staff_info_id']);
        $res      = $this->atomicLock(function () use ($params) {
            return CommonDataPermissionService::getInstance()->saveStaffConfigPermission($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 管辖人配置详情
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74542
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function staffConfigDetaiAction()
    {
        $params = $this->request->get();

        Validation::validate($params, CommonDataPermissionService::$validate_config_detail);

        $res = CommonDataPermissionService::getInstance()->getStaffConfigDetail($params['id'],
            $params['permission_category_id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除管辖人配置
     * @Permission(action='setting.data_permission.common_data_config')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74557
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delStaffConfigAction()
    {
        $params = $this->request->get();

        Validation::validate($params, CommonDataPermissionService::$validate_config_detail);
        $res = CommonDataPermissionService::getInstance()->removeStaffConfig($params, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }

}
