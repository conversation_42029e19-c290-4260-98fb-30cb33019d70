<?php
/**
 * 系统配置 - 子公司 - 会计规则配置
 */

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\AccountingRuleService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AccountingRuleController extends BaseController
{
    /**
     * 静态枚举配置
     * @Permission(menu='setting.accounting_rule')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75612
     * @return Response|ResponseInterface
     */
    public function enumsAction()
    {
        $data = AccountingRuleService::getInstance()->getStaticEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 会计规则-列表
     * @Permission(menu='setting.accounting_rule')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75812
     * @return Response|ResponseInterface
     */
    public function getListAction()
    {
        $params = trim_array($this->request->get());
        $data   = AccountingRuleService::getInstance()->getRuleList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 会计规则-添加
     * @Permission(action='setting.accounting_rule.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75817
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        AccountingRuleService::saveRuleCommonParamsValidation($params);

        $lock_key = md5(RedisKey::SYS_SETTING_ACCOUNTING_RULE_SINGLE_ADD_LOCK_PREFIX . $params['budget_id'] . '_' . $params['subjects_id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AccountingRuleService::getInstance()->addRule($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 会计规则-批量上传
     * @Permission(action='setting.accounting_rule.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75837
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function batchUploadAction()
    {
        if (!$this->request->hasFiles()) {
            throw new ValidationException($this->t['excel_file_empty'], ErrCode::$VALIDATE_ERROR);
        }

        $excel_file = $this->request->getUploadedFiles()[0];
        $extension  = $excel_file->getExtension();
        if (!in_array($extension, ['xlsx'])) {
            throw new ValidationException($this->t['file_format_error'], ErrCode::$VALIDATE_ERROR);
        }

        $lock_key = md5(RedisKey::SYS_SETTING_ACCOUNTING_RULE_BATCH_ADD_LOCK_PREFIX);
        $res      = $this->atomicLock(function () use ($excel_file) {
            return AccountingRuleService::getInstance()->batchAdd($excel_file, $this->user);
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 会计规则-更新
     * @Permission(action='setting.accounting_rule.edit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75832
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateAction()
    {
        $params = trim_array($this->request->get());
        AccountingRuleService::saveRuleCommonParamsValidation($params, true);

        $lock_key = md5(RedisKey::SYS_SETTING_ACCOUNTING_RULE_SINGLE_UPDATE_LOCK_PREFIX . $params['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return AccountingRuleService::getInstance()->updateRule($params, $this->user);
        }, $lock_key, 10);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

    /**
     * 会计规则-详情
     * @Permission(menu='setting.accounting_rule')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75822
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required|IntGt:0|>>>:param error[id]']);
        $data = AccountingRuleService::getInstance()->getRuleDetail($params['id']);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 会计规则-删除
     * @Permission(action='setting.accounting_rule.del')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75827
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required|IntGt:0|>>>:param error[id]']);
        $data = AccountingRuleService::getInstance()->delRule($params['id'], $this->user);
        return $this->returnJson($data['code'], $data['message']);
    }

}
