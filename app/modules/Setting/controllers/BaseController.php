<?php

namespace App\Modules\Setting\Controllers;

use App\Library\BaseController as Controller;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\BaseService;

/**
 * @name BaseController
 * @desc 所有web模块控制器都继承自该控制器
 */
abstract class BaseController extends Controller
{
    //模块自己控制
    public function onConstruct()
    {
    }

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);
    }

    /**
     * 员工权限校验，员工工号来自setting_env中store_access_staff_id，
     * 只允许该配置下的员工，对业务配置的编辑和系统配置的编辑做操作
     */
    public function getSettingAuthorize()
    {
        $authIds = BaseService::getSettingAuthStaffId();
        if (!in_array($this->user['id'], $authIds)) {
            throw new ValidationException($this->t->_('staff_permission_is_forbidden'), ErrCode::$VALIDATE_ERROR);
        }
    }

    /**
     * 员工权限校验，员工工号来自setting_env中access_approval_groups_id，
     * 只允许该配置下的员工，对业务配置的编辑和系统配置的编辑做操作
     */
    public function getSettingFinancialAuthorize()
    {
        $authIds = BaseService::getSettingFinancialAuthStaffId();

        return in_array($this->user['id'], $authIds) ? true : false;
    }
}