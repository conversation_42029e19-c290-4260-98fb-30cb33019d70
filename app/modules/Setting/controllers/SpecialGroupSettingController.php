<?php

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\BaseService;
use App\Modules\Setting\Services\SpecialGroupRelateService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class SpecialGroupSettingController extends BaseController
{

    /**
     * 特殊组配置-新增员工
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        Validation::validate($data, SpecialGroupRelateService::$add_validate_params);
        $data = filter_param($data);
        $res  = SpecialGroupRelateService::getInstance()->addSpecialGroupStaff($data, $this->user['id']);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 特殊组配置-删除添加的员工数据
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delAction()
    {
        $data = $this->request->get();
        Validation::validate($data, SpecialGroupRelateService::$validate_params);

        $res = SpecialGroupRelateService::getInstance()->delSpecialGroupStaff($data, $this->user['id']);
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 特殊组管辖配置列表
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $data = $this->request->get();
        $data = BaseService::handleParams($data, SpecialGroupRelateService::$not_must_params);
        Validation::validate($data, SpecialGroupRelateService::$search_validate_params);
        $data = filter_param($data);
        $res = SpecialGroupRelateService::getInstance()->specialGroupList($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 特殊组管辖部门详情
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffManageDepartmentsAction()
    {
        $data = $this->request->get();
        Validation::validate($data,  SpecialGroupRelateService::$validate_params);
        $res = SpecialGroupRelateService::getInstance()->staffManageDepartments($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 特殊组管辖部门配置
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editStaffManageDepartmentsAction()
    {
        $data = $this->request->get();
        Validation::validate($data, SpecialGroupRelateService::$edit_validate_params);
        $data = filter_param($data);
        $res = SpecialGroupRelateService::getInstance()->specialEditStaffManageRegion($data, $this->user['id']);
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 特殊组管辖部门配置-枚举
     * @Token
     * @return Response|ResponseInterface
     */
    public function specialEnumsAction()
    {
        $res = SpecialGroupRelateService::getInstance()->specialEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 模糊搜索在职员工
     * @Permission(action='setting.special.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchStaffListAction()
    {
        $data = $this->request->get();
        Validation::validate($data, SpecialGroupRelateService::$search_staff_validate_params);
        $data = filter_param($data);
        $res = SpecialGroupRelateService::getInstance()->searchStaffData($data);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
