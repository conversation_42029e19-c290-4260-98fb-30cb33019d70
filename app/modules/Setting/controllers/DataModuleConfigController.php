<?php

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\BaseService;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


class DataModuleConfigController extends BaseController
{
    /**
     * 数据配置列表
     * @Permission(action='setting.data_module_config.config_list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69807
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function configListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, DataPermissionModuleConfigService::$not_must_params);
        Validation::validate($params, DataPermissionModuleConfigService::$validate_config_list);
        $res = DataPermissionModuleConfigService::getInstance()->getConfigList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 配置权限列表（详情）
     * @Permission(action='setting.data_module_config.power_list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69812
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function powerListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, DataPermissionModuleConfigService::$not_must_params);
        Validation::validate($params, array_merge(DataPermissionModuleConfigService::$validate_share,
            DataPermissionModuleConfigService::$validate_power));
        $res = DataPermissionModuleConfigService::getInstance()->getPowerList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 配置权限添加用户
     * @Permission(action='setting.data_module_config.add')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69817
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = $this->request->get();
        Validation::validate($params, array_merge(DataPermissionModuleConfigService::$validate_share,
            DataPermissionModuleConfigService::$validate_add));
        $res = DataPermissionModuleConfigService::getInstance()->add($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除
     * @Permission(action='setting.data_module_config.del')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69822
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function delAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DataPermissionModuleConfigService::$validate_share);
        $res = DataPermissionModuleConfigService::getInstance()->del($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 保存
     * @Permission(action='setting.data_module_config.save')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69827
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function saveAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DataPermissionModuleConfigService::$validate_save);
        $res = DataPermissionModuleConfigService::getInstance()->save($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 编辑详情数据
     * @Permission(action='setting.data_module_config.save')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69832
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DataPermissionModuleConfigService::$validate_share);
        $res = DataPermissionModuleConfigService::getInstance()->detail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
