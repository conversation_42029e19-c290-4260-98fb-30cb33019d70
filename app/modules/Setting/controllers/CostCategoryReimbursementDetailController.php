<?php

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetEditService;
use App\Modules\Setting\Services\CostCategoryReimbursementDetailService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class CostCategoryReimbursementDetailController extends BaseController
{
    /**
     * 配置信息
     * @Permission(action='setting.system.get')
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = CostCategoryReimbursementDetailService::getInstance()->getOptionsDefault();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 系统配置项列表
     * @Permission(action='setting.system.get')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = (array)$this->request->get();
        Validation::validate($params, CostCategoryReimbursementDetailService::$validate_list_search);
        $list = CostCategoryReimbursementDetailService::getInstance()->list($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 系统配置项编辑
     * @Permission(action='setting.system.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException|BusinessException
     */
    public function editAction()
    {
        $data = (array)$this->request->get();
        Validation::validate($data, CostCategoryReimbursementDetailService::$edit_validate_params);
        $data = filter_param($data);
        $res = CostCategoryReimbursementDetailService::getInstance()->edit($data, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok',$res);
    }

    /**
     * 系统配置项编辑
     * @Permission(action='setting.system.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException|BusinessException
     */
    public function addAction()
    {
        $data = (array)$this->request->get();
        Validation::validate($data, CostCategoryReimbursementDetailService::$add_validate_params);
        $data = filter_param($data);
        $res  = CostCategoryReimbursementDetailService::getInstance()->add($data, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok',$res);
    }

    /**
     * 系统配置项移除
     * @Permission(action='setting.system.edit')
     * @return Response|ResponseInterface
     * @throws ValidationException|BusinessException
     */
    public function deleteAction()
    {
        $data = (array)$this->request->get();
        Validation::validate($data, CostCategoryReimbursementDetailService::$delete_validate_params);
        $res = CostCategoryReimbursementDetailService::getInstance()->delete($data, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok',$res);
    }


    /**
     * 预算科目列表
     * @Permission(action='setting.system.get')
     * @return Response|ResponseInterface
     */
    public function budgetListAction()
    {
        $params      = (array)$this->request->get();
        $params['order'] = 'b.id asc';
        $list = BudgetEditService::getInstance()->list($params, $this->user['id']);
        $data = [
            'list'  => $list['data'] ?? [],
            'total' => $list['total'] ?? 0,
        ];
        return $this->returnJson($list['code'], $list['message'], $data);
    }


}
