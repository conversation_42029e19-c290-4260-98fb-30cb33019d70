<?php

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Setting\Services\FinancialService;
use App\Repository\DepartmentRepository;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class FinancialApprovalSettingController extends BaseController
{
    /**
     * 财务分组管理列表
     * @Permission(action='setting.financial.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function FinancialGroupListAction()
    {
        $params = $this->request->get();
        // 员工权限校验
        if (!$this->getSettingFinancialAuthorize()) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('staff_permission_is_forbidden'));
        }

        Validation::validate($params, FinancialService::$validate_list_search);

        $list = FinancialService::getInstance()->getGroupList($params);
        return $this->returnJson(ErrCode::$SUCCESS, $list['message'], $list['data']);
    }

    /**
     * 财务分组新增
     * @Permission(action='setting.financial.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addFinancialGroupAction()
    {
        $data = $this->request->get();

        $this->logger->info('财务审批分组管理新增, 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $this->logger->info('财务审批分组管理新增, 当前用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        // 员工权限校验
        if (!$this->getSettingFinancialAuthorize()) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('staff_permission_is_forbidden'));
        }

        Validation::validate($data, FinancialService::$add_validate_params);
        $data = filter_param($data);

        $res = FinancialService::getInstance()->addGroup($data, $this->user['id']);

        $this->logger->info('财务审批分组管理新增, 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 财务分组编辑
     * @Permission(action='setting.financial.group')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editFinancialGroupAction()
    {
        $data = $this->request->get();

        $this->logger->info('财务审批分组管理编辑, 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $this->logger->info('财务审批分组管理编辑, 当前用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        // 员工权限校验
        if (!$this->getSettingFinancialAuthorize()) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('staff_permission_is_forbidden'));
        }

        Validation::validate($data, FinancialService::$edit_validate_params);
        $data = filter_param($data);

        $res = FinancialService::getInstance()->editGroup($data, $this->user['id']);

        $this->logger->info('财务审批分组管理编辑, 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 财务分组管辖部门详情
     * @Permission(action='setting.financial.staff_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffManageDepartmentsAction()
    {
        $data = $this->request->get();

        Validation::validate($data, FinancialService::$detail_validate_params);

        $res = FinancialService::getInstance()->getStaffManageDepartments($data['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 财务分组管辖部门配置
     * @Permission(action='setting.financial.staff_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editStaffManageDepartmentsAction()
    {
        $data = $this->request->get();

        $this->logger->info('财务分组管辖部门配置, 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $this->logger->info('财务分组管辖部门配置, 当前用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        Validation::validate($data, FinancialService::$edit_financial_validate_params);
        $data = filter_param($data);

        $res = FinancialService::getInstance()->editStaffManageRegion($data, $this->user['id']);

        $this->logger->info('财务分组管辖部门配置, 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 财务分组财务人员列表
     * @Permission(action='setting.financial.staff_list')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function FinancialStaffListAction()
    {
        $data = $this->request->get();

        Validation::validate($data, FinancialService::$search_validate_params);
        $data = filter_param($data);

        $res = FinancialService::getInstance()->financialManageList($data, $this->user);

        $this->logger->info('财务审批分组管理编辑, 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 枚举
     * @Token
     * @return Response|ResponseInterface
     */
    public function getEnumsAction()
    {
        $res = FinancialService::getInstance()->getEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res);
    }

    /**
     * 组织架构树根据部门id
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getSubDepartmentByIdsAction()
    {
        $params = $this->request->get();
        $res    = (new DepartmentRepository())->getSubDepartmentById($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res ?? []);
    }

    /**
     * 组织架构根据名称
     * @Token
     * @return Response|ResponseInterface
     */
    public function getDepartmentByKeyAction()
    {
        $params = $this->request->get();
        $data   = (new DepartmentService())->search($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


}
