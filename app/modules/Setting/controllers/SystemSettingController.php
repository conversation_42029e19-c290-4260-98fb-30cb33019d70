<?php

namespace App\Modules\Setting\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\SystemService;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class SystemSettingController extends BaseController
{
    /**
     * 系统配置项列表
     * @Permission(action='setting.system.get')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSystemSettingListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, SystemService::$validate_list_search);

        $list = SystemService::getInstance()->getSystemSettingList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 系统配置项编辑
     * @Permission(action='setting.system.edit')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function editSystemSettingAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $data = $this->request->get();

        $this->logger->info('系统设置编辑, 请求参数: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
        $this->logger->info('系统设置编辑, 当前用户: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

        $data['content'] = $data['content'] ?? '';
        Validation::validate($data, SystemService::$edit_validate_params);
        $data = filter_param($data);

        $res = SystemService::getInstance()->editOne($data, $this->user);

        $this->logger->info('系统设置编辑, 响应结果: ' . json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 更新翻译
     * @Permission(action='setting.system.lang')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function syncLanguageAction()
    {
        // 员工权限校验
        $this->getSettingAuthorize();

        $lock_key = md5(RedisKey::SYS_SETTING_SYNC_LANGUAGE_LOCK_PREFIX . $this->user['id']);
        $res      = $this->atomicLock(function () {
            return SystemService::getInstance()->languageSyncInit();
        }, $lock_key, 20);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
        }

        return $this->returnJson($code, $message);
    }

}
