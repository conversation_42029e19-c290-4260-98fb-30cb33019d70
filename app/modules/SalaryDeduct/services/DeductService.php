<?php
namespace App\Modules\SalaryDeduct\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\Enums\LoanEnums;
use App\Library\Enums\SalaryDeductEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\LoanReturnModel;
use App\Models\oa\ReserveFundApplyReturnRelModel;
use App\Models\oa\SalaryDeductBatchModel;
use App\Models\oa\SalaryDeductBatchUserModel;
use App\Models\oa\SalaryDeductBatchUserOrderModel;
use App\Models\oa\SalaryDeductTaskModel;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Loan\Models\Loan;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\ReserveFund\Models\ReserveFundReturn;

/**
 * 薪资抵扣服务层
 * Class DeductService
 * @package App\Modules\SalaryDeduct\Services
 */
class DeductService extends BaseService
{
    /**
     * 非必需的筛选条件
     * @var array
     */
    public static $not_must_params = [
        'staff_id',
        'deduct_date',
        'status',
        'type',
        'pageSize',
        'pageNum'
    ];

    //薪资抵扣总数据-列表搜索
    public static $validate_list_search = [
        'deduct_date' => 'Date',//应扣日期
        'status' => 'IntIn:' . SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS,//实扣状态：1未上传，2处理中，3待payroll终审,4成功扣款
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //薪资抵扣总数据-详情
    public static $validate_batch_detail = [
        'batch_id' => 'Required|IntGt:0',//批次id
        'staff_id' => 'IntGt:0',//工号
        'status' => 'IntIn:' . SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS,//实扣状态：1成功，2未开始处理，3失败'
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //下载(导出)应扣明细/下载(导出)实扣明细
    public static $validate_batch_export = [
        'type' => 'IntIn:' . SalaryDeductEnums::SALARY_DEDUCT_EXPORT_TYPE_RULE,//导出类型：1应扣，2实扣，不传递默认1
    ];

    //本批次数据概述、下载(导出)应扣明细/下载(导出)实扣明细、上传实扣金额、确认扣款正确
    public static $validate_batch_update = [
        'batch_id' => 'Required|IntGt:0',
    ];

    //薪资抵扣总数据-详情页-编辑
    public static $validate_update_batch_user = [
        'id' => 'Required|IntGt:0',
        'actual_deduct_amount' => 'Required|Float',//实扣金额
        'company_name' => 'Required|StrLenGeLe:1,50',//抵扣所属公司
    ];

    //薪资抵扣借款/薪资抵扣备用金-查询
    public static $validate_batch_user_order_list_search = [
        'deduct_date' => 'Date',//应扣日期
        'status' => 'IntIn:' . SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_STATUS,//实扣状态
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //薪资抵扣借款/备用金-下载应扣明细/下载实扣明细
    public static $validate_update_batch_user_order = [
        'serial_no' => 'Required|StrLenGeLe:1,20',
        'type' => 'IntIn:' . SalaryDeductEnums::SALARY_DEDUCT_EXPORT_TYPE_RULE,//导出类型：1应扣，2实扣，不传递默认1
    ];

    private static $instance;
    /**
     * 单例
     * @return DeductService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 薪资抵扣-默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $enums_arr = [
                'salary_deduct_batch_status' => SalaryDeductEnums::$salary_deduct_batch_status,
                'salary_deduct_batch_user_status' => SalaryDeductEnums::$salary_deduct_batch_user_status,
                'salary_deduct_batch_user_order_status' => SalaryDeductEnums::$salary_deduct_batch_user_order_status
            ];
            foreach ($enums_arr as $key => $value) {
                foreach ($value as $k=>$v) {
                    $data[$key][] = [
                        'value'=>$k,
                        'label'=>static::$t->_($v)
                    ];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('获取资产台账枚举异常信息: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 薪资抵扣总数据-查询
     * @param array $params 筛选条件参数组
     * @return array
     */
    public function getList($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['batch' => SalaryDeductBatchModel::class]);
            $builder->leftJoin(SalaryDeductBatchUserModel::class, 'batch_user.batch_id = batch.id', 'batch_user');
            //组合搜索条件
            if (!empty($params['deduct_date'])) {
                //应扣日期筛选
                $builder->andWhere('batch.deduct_date = :deduct_date:', ['deduct_date' => $params['deduct_date']]);
            }
            if (!empty($params['status'])) {
                //实扣状态筛选
                $builder->andWhere('batch.status = :status:', ['status' => $params['status']]);
            }
            $builder->columns('count(DISTINCT batch.id) as count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('batch.id, no, batch.deduct_date, count(batch_user.id) as data_count, batch.status, batch.created_at, batch.actual_deduct_at');
                $builder->limit($page_size, $offset);
                $builder->orderby('batch.id DESC');
                $builder->groupBy('batch.id');
                $items = $builder->getQuery()->execute()->toArray();
                if (!empty($items)) {
                    foreach ($items as &$item) {
                        $item['actual_deduct_at'] = $item['actual_deduct_at'] ?? '';
                        $item['status_text'] = static::$t[SalaryDeductEnums::$salary_deduct_batch_status[$item['status']]];
                    }
                }
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-salary-deduct-batch-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 详情查询条件组
     * @param object $builder 查询器
     * @param array $params 筛选条件参数组
     * @return mixed
     */
    private function getDetailCondition($builder, $params)
    {
        $builder->where('batch_id = :batch_id:', ['batch_id' => $params['batch_id']]);
        //组合搜索条件
        if (!empty($params['staff_id'])) {
            //员工工号
            $builder->andWhere('staff_id = :staff_id:', ['staff_id' => $params['staff_id']]);
        }
        if (!empty($params['status'])) {
            //实扣状态筛选
            $builder->andWhere('status = :status:', ['status' => $params['status']]);
        }
        return $builder;
    }

    /**
     * 薪资抵扣总数据/详情页-导出/下载应扣明细/实扣明细
     * @param array $params 筛选条件参数组
     * @return array
     */
    public function export($params)
    {
        try {
            $row_values = [];
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SalaryDeductBatchUserModel::class);
            $builder = $this->getDetailCondition($builder, $params);
            if (!empty($params['type']) && $params['type'] == SalaryDeductEnums::EXPORT_TYPE_ACTUAL_DEDUCT) {
                //若是下载2实扣明细，要查询实扣数据生成时间不为NULL的
                $builder->andWhere('actual_deduct_at is not null');
            }
            $builder->columns('serial_number, staff_id, staff_name, state, wait_leave_state, deduct_type, deduct_amount, actual_deduct_amount, company_name, status');
            $builder->orderby('status,id DESC');
            $items = $builder->getQuery()->execute()->toArray();
            if (!empty($items)) {
                foreach ($items as $item) {
                    //待离职
                    $state = ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : StaffInfoEnums::STAFF_STATE_IN;
                    $row_values[] = [
                        $item['serial_number'],//UID编号
                        $item['staff_id'],//员工工号
                        $item['staff_name'],//员工姓名
                        static::$t->_(StaffInfoEnums::$staff_state[$state]),//在职状态
                        static::$t->_(SalaryDeductEnums::$salary_deduct_type[$item['deduct_type']]),//抵扣类型
                        $item['company_name'],//扣款所属公司
                        bcdiv($item['deduct_amount'], 1000, 2),//本期应扣金额
                        ($item['status'] !=  SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_UN_START) ? bcdiv($item['actual_deduct_amount'], 1000, 2) : ''//本期实扣金额
                    ];
                }
            }
            
            $header = [
                static::$t->_('salary_deduct_user_serial_number'), //UID编号
                static::$t->_('salary_deduct_user_staff_id'), //员工工号
                static::$t->_('salary_deduct_user_staff_name'), //员工姓名
                static::$t->_('salary_deduct_user_state'), //申请人在职状态
                static::$t->_('salary_deduct_deduct_type'), //全额/限额
                static::$t->_('salary_deduct_company_name'), //扣款所属公司
                static::$t->_('salary_deduct_deduct_amount'), //本期应扣金额
                static::$t->_('salary_deduct_actual_deduct_amount'), //本期实扣金额
            ];
            $file_name = static::$t->_('salary_deduct').'-' . date("Ymd");
            $result = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('export-salary-deduct-batch-user-list:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 薪资抵扣总数据-详情
     * @param array $params 筛选条件参数组
     * @return array
     */
    public function detail($params)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SalaryDeductBatchUserModel::class);
            $builder = $this->getDetailCondition($builder, $params);
            $builder->columns('count(id) as count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('id, batch_id, serial_number, staff_id, deduct_amount, actual_deduct_amount, status, company_name');
                $builder->limit($page_size, $offset);
                $builder->orderby('status DESC, id ASC');
                $items = $builder->getQuery()->execute()->toArray();
                if (!empty($items)) {
                    foreach ($items as &$item) {
                        $item['deduct_amount'] = bcdiv($item['deduct_amount'], 1000, 2);
                        $item['actual_deduct_amount'] = $item['actual_deduct_amount'] ? bcdiv($item['actual_deduct_amount'], 1000, 2) : '0.00';
                        $item['status_text'] = static::$t[SalaryDeductEnums::$salary_deduct_batch_user_status[$item['status']]];
                    }
                }
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-salary-deduct-batch-detail-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取批次信息
     * @param integer $batch_id 批次id
     * @return mixed
     */
    public function getSalaryDeductBatchInfo($batch_id)
    {
        return SalaryDeductBatchModel::findFirst([
            'conditions' => 'id = :batch_id:',
            'bind' => ['batch_id' => $batch_id]
        ]);
    }

    /**
     * 获取批次下-员工明细-各状态数据
     * @param integer $batch_id 批次id
     * @return array
     */
    public function getSalaryDeductBatchUserStatusCount($batch_id)
    {
        $data = [];
        $salary_deduct_batch_user_list = SalaryDeductBatchUserModel::find([
            'conditions' => 'batch_id = :batch_id:',
            'bind' => ['batch_id' => $batch_id],
            'columns' => 'count(id) as num, status',
            'group' => 'status'
        ])->toArray();
        $data['total'] = array_sum(array_column($salary_deduct_batch_user_list, 'num'));
        $salary_deduct_batch_user_list = array_column($salary_deduct_batch_user_list, 'num', 'status');
        $data['success'] = $salary_deduct_batch_user_list[SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_SUCCESS] ?? 0;
        $data['fail'] = $salary_deduct_batch_user_list[SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_FAIL] ?? 0;
        $data['un_start'] = $salary_deduct_batch_user_list[SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_UN_START] ?? 0;
        return $data;
    }

    /**
     * 薪资抵扣总数据-详情页-本批次数据概述
     * @param array $params 筛选条件参数组
     * @return array
     */
    public function summary($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'total' => 0,
            'success' => 0,
            'fail' => 0,
            'un_start' => 0,
            'batch_status' => 0
        ];
        try {
            $batch_id = $params['batch_id'];
            $salary_deduct_batch_info = $this->getSalaryDeductBatchInfo($batch_id);
            if (!empty($salary_deduct_batch_info)) {
                //获取批次下-员工明细-各状态数据值
                $data = $this->getSalaryDeductBatchUserStatusCount($batch_id);
                $data['batch_status'] = $salary_deduct_batch_info->status;
            }
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-salary-deduct-batch-summary-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 检测批次信息是否可操作
     * @param integer $batch_id 批次id
     * @return mixed
     * @throws ValidationException
     */
    private function checkSalaryDeductBatchInfo($batch_id)
    {
        $salary_deduct_batch_info = $this->getSalaryDeductBatchInfo($batch_id);
        if (empty($salary_deduct_batch_info)) {
            throw new ValidationException(static::$t->_('salary_deduct_batch_info_not_found'), ErrCode::$VALIDATE_ERROR);
        }
        if ($salary_deduct_batch_info->status == SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_DONE) {
            throw new ValidationException(static::$t->_('salary_deduct_batch_status_done'), ErrCode::$VALIDATE_ERROR);
        }
        return $salary_deduct_batch_info;
    }

    /**
     * 薪资抵扣总数据-详情页-上传实扣金额-异步上传-任务
     * @param array $params 请求参数组
     * @param string $excel_file 文件
     * @param array $user 当前员工信息组
     * @return array
     */
    public function importDeductTask($params, $excel_file, $user)
    {
        $real_message = '';
        try {
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('salary_deduct_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            $file = $excel_file[0];
            //仅支持.xlsx格式的文件
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException(static::$t->_('file_format_error'), ErrCode::$VALIDATE_ERROR);
            }

            //检测本批次任务是否已完成扣款
            $batch_id = $params['batch_id'];
            $this->checkSalaryDeductBatchInfo($batch_id);

            //解析文件内容
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            //读取上传文件数据
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    \Vtiful\Kernel\Excel::TYPE_STRING,
                ])
                ->getSheetData();
            //弹出excel标题一行信息
            array_shift($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('data_empty_or_read_data_failed'), ErrCode::$VALIDATE_ERROR);
            }

            //跳过空行
            $clear_excel_data = [];
            foreach ($excel_data as $key => $row) {
                if (empty($row[0])) {
                    break;
                }
                $clear_excel_data[$key] = $row;
            }
            //检测上传的实扣明细总数应于本批次应扣明细总数一致
            $count = SalaryDeductBatchUserModel::count([
                'conditions' => 'batch_id = :batch_id:',
                'bind' => ['batch_id' => $batch_id]
            ]);
            if (count($clear_excel_data) != $count) {
                throw new ValidationException(static::$t->_('salary_deduct_import_num_error'), ErrCode::$VALIDATE_ERROR);
            }
            $this->logger->info('import-salary_deduct-操作上传实扣金额:' . json_encode(['staff_id' => $user['id'], 'data' => $clear_excel_data], JSON_UNESCAPED_UNICODE));
            // 导入中心
            return ImportCenterService::getInstance()->addImportTask($file, $user, ImportCenterEnums::TYPE_SALARY_DEDUCT);
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('importDeductTaskAdd-薪资抵扣-上传实扣金额-导入新增失败-' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS ? true : false
        ];
    }

    /**
     * 薪资抵扣总数据-详情页-上传实扣金额-查询最后一次导入成功的结果
     * @return array
     */
    public function getImportResult()
    {
        return ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_SALARY_DEDUCT);
    }

    /**
     * 薪资抵扣总数据-详情页-上传实扣金额-异步任务-处理
     * @param array $data 抵扣数据
     * @return array
     */
    public function handleDeductTask($data)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $total_num = 0;//总记录数
        $success_num = 0;//成功数
        $updated_at =  date('Y-m-d H:i:s');
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //批次id
            $batch_id = 0;

            //获取excel表头，并追加导入结果列
            $excel_header = array_shift($data);
            array_push($excel_header, static::$t->_('salary_deduct_import_result'));

            //总数据记录数
            $total_num = count($data);

            //开始遍历excel每一行数据并验证处理更新入库
            foreach ($data as $k => $row) {
                $serial_number = trim($row[0]) ?? '';//UID编号
                $staff_id = trim($row[1]) ?? 0;//员工工号
                $company_name = trim($row[5]) ?? '';//扣款所属公司
                $deduct_amount = trim($row[6]) ?? 0;//应扣金额
                $actual_deduct_amount = trim($row[7]) ?? 0;//实扣金额
                //扣款所属公司长度
                $company_name_len = mb_strlen($company_name);
                //处理扣款状态【默认：失败】
                $status = SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_FAIL;
                if (empty($serial_number) || empty($staff_id)) {
                    //UID编号、员工工号必填
                    $data[$k][8] = static::$t->_('salary_deduct_staff_info_required');
                } else if (empty($company_name) || $company_name_len < 1 || $company_name_len > 50) {
                    //扣款所属公司必填，长度1-50个字符
                    $data[$k][8] = static::$t->_('salary_deduct_company_name_required');
                } else if (!is_numeric($actual_deduct_amount) || !preg_match(SalaryDeductEnums::SALARY_DEDUCT_AMOUNT_RULE, $actual_deduct_amount)) {
                    //实扣金额仅限数字，两位小数
                    $data[$k][8] = static::$t->_('salary_deduct_amount_non_numeric');
                } else if (($deduct_amount < 0 && $actual_deduct_amount > 0) || ($deduct_amount > 0 && $actual_deduct_amount < 0)) {
                    //检测：实扣金额应于抵扣金额正负号一致
                    $data[$k][8] = static::$t->_('salary_deduct_amount_sign_different');
                } else if (abs($actual_deduct_amount) > abs($deduct_amount)) {
                    //检测：实扣金额 <= 应扣金额（因为会出现负数，所以都取绝对值校验）
                    $data[$k][8] = static::$t->_('salary_deduct_amount_wrongful');
                } else {
                    //各种限制验证通过，则是成功
                    $status = SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_SUCCESS;
                    $success_num++;
                    $data[$k][8] = 'success';
                }

                //【验证通过与否都需要更新数据】获取要操作的员工明细信息
                $salary_deduct_batch_user_info = SalaryDeductBatchUserModel::findFirst([
                    'conditions' => 'serial_number = :serial_number: and staff_id = :staff_id:',
                    'bind' => ['serial_number' => $serial_number, 'staff_id' => $staff_id]
                ]);
                if (empty($salary_deduct_batch_user_info)) {
                    //要更新的批次信息未找到
                    $success_num--;
                    $data[$k][8] = static::$t->_('salary_deduct_batch_user_info_not_found');
                } else {
                    //为了防止excel中手动改动应扣金额跟库里的应扣金额值作对比，必须一致，不一致给予提示
                    if (bcmul($deduct_amount, 1000) != $salary_deduct_batch_user_info->deduct_amount) {
                        $success_num--;
                        //文件中应扣金额与数据表里应扣金额不一致
                        $data[$k][8] = static::$t->_('salary_deduct_amount_error');
                        $status = SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_FAIL;
                    }

                    //都满足了开始更新
                    $update_data = [
                        'status' => $status,//扣款状态：1成功,3失败
                        'actual_deduct_amount' => bcmul($actual_deduct_amount, 1000),//实扣金额
                        'company_name' => $company_name,//扣款所属公司
                        'updated_at' => $updated_at//更新时间
                    ];
                    $bool = $salary_deduct_batch_user_info->i_update($update_data);
                    if ($bool === false) {
                        $success_num--;
                        $data[$k][8] = '';
                        throw new BusinessException('薪资抵扣-上传实扣金额-异步任务-处理【实扣金额、扣款所属公司】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_user_info), ErrCode::$BUSINESS_ERROR);
                    }
                    //这个excel中的批次都是统一的
                    $batch_id = ($batch_id == 0) ? $salary_deduct_batch_user_info->batch_id : $batch_id;
                }
            }

            //未找到批次信息
            if (empty($batch_id)) {
                //未找到批次信息
                throw new ValidationException(static::$t->_('salary_deduct_batch_info_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //检测批次信息是否可操作
            $salary_deduct_batch_info = $this->checkSalaryDeductBatchInfo($batch_id);

            //变更批次表的实扣状态【批次下所有员工明细-实扣状态-全部成功；则标记为3待payroll终审；否则标记为2处理中】
            $count_data = $this->getSalaryDeductBatchUserStatusCount($batch_id);
            //全部成功；则标记为3待payroll终审
            if ($count_data['total'] == $count_data['success']) {
                $batch_status = SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_PAYROLL;
            } else {
                $batch_status = SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_ING;
            }
            $update_data = [
                'status' => $batch_status,
                'updated_at' => $updated_at//更新时间
            ];
            $bool = $salary_deduct_batch_info->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('薪资抵扣-上传实扣金额-异步任务-处理【变更批次实扣状态】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_info), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();

            //将表头放入到data中返回
            array_unshift($data, $excel_header);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('handle-salary_deduct_-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'excel_data' => $data,
                'success_num' => $success_num,
                'failed_sum' => $total_num - $success_num
            ]
        ];
    }

    /**
     * 薪资抵扣总数据-详情页-确认扣款正确
     * @param array $params 请求参数组
     * @param array $user 当前员工信息组
     * @return array
     */
    public function confirm($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $bool = false;
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //批次id
            $batch_id = $params['batch_id'];

            //检测批次信息是否可操作【已完成确认或批次信息不存在】
            $salary_deduct_batch_info = $this->checkSalaryDeductBatchInfo($batch_id);

            //仅待payroll终审的薪资抵扣才允许确认
            if ($salary_deduct_batch_info->status != SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_PAYROLL) {
                throw new ValidationException(static::$t->_('salary_deduct_confirm_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //获取本批次下员工明细数据
            $salary_deduct_batch_user = SalaryDeductBatchUserModel::find([
                'conditions' => 'batch_id = :batch_id:',
                'bind' => ['batch_id' => $batch_id]
            ]);
            $salary_deduct_batch_user_list = $salary_deduct_batch_user->toArray();
            if (empty($salary_deduct_batch_user_list)) {
                throw new ValidationException(static::$t->_('salary_deduct_batch_user_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            $updated_at =  date('Y-m-d H:i:s');
            //第一步更新本批次抵扣总数据信息
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 操作批次前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_info->toArray()], JSON_UNESCAPED_UNICODE));
            $update_data = [
                'status' => SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_DONE,//扣款状态：1成功
                'actual_deduct_at' => $updated_at,//扣款反馈时间
                'updated_at' => $updated_at//更新时间
            ];
            $bool = $salary_deduct_batch_info->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('薪资抵扣-确认扣款正确-更新批次表【实扣状态、扣款反馈时间】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_info), ErrCode::$BUSINESS_ERROR);
            }
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 操作批次后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_info->toArray()], JSON_UNESCAPED_UNICODE));

            //第二步更新本批次下-员工明细数据信息
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工明细表前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_user_list], JSON_UNESCAPED_UNICODE));
            $update_data = [
                'actual_deduct_at' => $updated_at,//扣款反馈时间
                'updated_at' => $updated_at//更新时间
            ];
            foreach ($salary_deduct_batch_user as $item) {
                $bool = $item->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工明细表【扣款反馈时间】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($item), ErrCode::$BUSINESS_ERROR);
                }
                //第三步开始处理该员工名下-应扣明细
                $this->handleDeductUserOrder($item, $updated_at, $user);
            }
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】-员工明细表后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_user->toArray()], JSON_UNESCAPED_UNICODE));
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('confirm-salary_deduct-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $bool? true : false,
        ];
    }

    /**
     * 处理某员工名下-应扣明细【扣款所属公司、实扣数据生成时间、实扣金额、状态】
     * @param object $salary_deduct_user_info 薪资抵扣总数据-批次员工明细-信息
     * @param string $updated_at 更细时间
     * @param array $user 当前登录者信息组
     * @throws BusinessException
     * @throws ValidationException
     */
    public function handleDeductUserOrder($salary_deduct_user_info, $updated_at, $user)
    {
        //优先抵扣借款时长较长实扣数据;多笔借款从回传的实扣金额中-需要抵扣的金额。若实扣金额为0，则其他借款金额实扣金额= 0;借款抵扣完事儿了有剩余抵扣金额，抵扣备用金 = 剩余可抵扣金额，否则实际抵扣金额=0
        $user_order = SalaryDeductBatchUserOrderModel::find([
            'conditions' => 'batch_id = :batch_id: and staff_id = :staff_id:',
            'bind' => ['batch_id' => $salary_deduct_user_info->batch_id, 'staff_id' => $salary_deduct_user_info->staff_id],
            'order' => 'source_type ASC, deduct_amount ASC'
        ]);
        $user_order_list = $user_order->toArray();
        if (!empty($user_order_list)) {
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $user_order_list], JSON_UNESCAPED_UNICODE));
            //员工此次实际扣款金额
            $user_actual_deduct_amount = $salary_deduct_user_info->actual_deduct_amount;

            //开始处理应扣明细
            foreach ($user_order as $order) {
                //员工此次应扣金额 = 员工此次实际扣款金额
                if ($salary_deduct_user_info->deduct_amount == $user_actual_deduct_amount) {
                    $actual_deduct_amount = $order->deduct_amount;
                } else {
                    //已扣完了，后续单子的实际抵扣金额 = 0
                    if ($user_actual_deduct_amount == 0) {
                        $actual_deduct_amount = 0;
                    } else {
                        if ($user_actual_deduct_amount < 0 && $order->deduct_amount < 0) {
                            $actual_deduct_amount = ($user_actual_deduct_amount >= $order->deduct_amount) ? $user_actual_deduct_amount : $order->deduct_amount;
                        } else {
                            $actual_deduct_amount = ($user_actual_deduct_amount >= $order->deduct_amount) ? $order->deduct_amount : $user_actual_deduct_amount;
                        }
                        //当前剩余余额
                        $user_actual_deduct_amount -= $actual_deduct_amount;
                    }
                }

                //开始更新应扣明细表信息
                $update_data = [
                    'actual_deduct_amount' => $actual_deduct_amount,
                    'company_name' => $salary_deduct_user_info->company_name,
                    'status' => SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_STATUS_DONE,
                    'actual_deduct_at' => $updated_at,
                    'updated_at' => $updated_at
                ];
                $bool = $order->i_update($update_data);
                if ($bool === false) {
                    throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工应扣明细【扣款状态、实际扣款、金额扣款反馈时间】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($order), ErrCode::$BUSINESS_ERROR);
                }

                //开始操作应扣明细中关联业务侧归还单
                if ($order->source_type == SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_LOAN) {
                    $this->handleDeductUserOrderLoan($order, $actual_deduct_amount, $user, $updated_at);
                } else if ($order->source_type == SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND) {
                    $this->handleDeductUserOrderReserveFund($order, $actual_deduct_amount, $user, $updated_at);
                }
            }
            $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $user_order->toArray()], JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 操作员工-薪资抵扣-借款应扣明细
     * @param object $order 薪资抵扣 - 应扣明细信息
     * @param integer $actual_deduct_amount 本期应扣金额
     * @param array $user 当前登陆者信息组
     * @param string $updated_at 更新时间
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleDeductUserOrderLoan($order, $actual_deduct_amount, $user, $updated_at)
    {
        //借款单实际抵扣金额等于0，对应的借款单不生成薪资抵扣归还数据
        if ($actual_deduct_amount == 0) {
            return true;
        }
        //更新借款单归还金 && 借款单状态
        $loan = Loan::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $order->order_id]
        ]);
        if (empty($loan)) {
            //未找到借款申请单信息
            throw new ValidationException(static::$t->_('salary_deduct_user_order_loan_not_found', ['loan_no' => $order->order_no]), ErrCode::$VALIDATE_ERROR);
        }
        //借款业务生成一条该笔借款的薪资抵扣归还单 && 更改借款表归还金额、借款单状态
        $loan_return_data = [
            'back_no' => static::genSerialNo(LoanEnums::LOAN_RETURN_BACK_APPLY_NO_PREFIX, 'loan_return_back_apply_counter', 5),//归还单申请编号
            'back_apply_date' => substr($updated_at, 0 , 10),//归还申请日期
            'loan_id' => $order->order_id,//借款单id
            'back_amount' => $order->deduct_amount,//归还金额
            'back_actual_amount' => $actual_deduct_amount,//实际归还金额
            'back_status' => LoanEnums::LOAN_BACK_STATUS_BACK,//还款状态-已完成
            'back_mark' => '',//备注
            'back_audit_status' => Enums::WF_STATE_APPROVED,//归还审核状态-通过
            'back_approved_at' => $updated_at,//归还通过时间
            'back_date' => $order->deduct_date,//归还时银行流水日期
            'back_type' => LoanEnums::LOAN_BACK_TYPE_SALARY,//归还方式-薪资抵扣
            'back_progress' => LoanEnums::LOAN_RETURN_BACK_PROGRESS_DONE,//归还进度-已完成
            'created_at' => $updated_at,
            'updated_at' => $updated_at
        ];
        $loan_return = new LoanReturnModel();
        $bool = $loan_return->i_create($loan_return_data);
        if ($bool === false) {
            throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工应扣明细【创建借款归还单】- 失败 = ' . json_encode($loan_return_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($loan_return), ErrCode::$BUSINESS_ERROR);
        }
        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 创建借款归还单数据:' . json_encode(['staff_id' => $user['id'], 'data' => $loan_return_data], JSON_UNESCAPED_UNICODE));

        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 关联借款单前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $loan->toArray()], JSON_UNESCAPED_UNICODE));

        //更新借款表相关字段
        $loan->back_amount += $actual_deduct_amount;//归还金额累计本期实际抵扣金额
        //已归还金额
        $paid_return_amount = $loan->re_amount + $loan->back_amount;
        if ($paid_return_amount < $loan->amount) {
            //部分归还：已归还金额<借款金额
            $loan->loan_status = LoanEnums::LOAN_STATUS_PARTIAL_RETURN;
        } else if ($paid_return_amount == $loan->amount) {
            //已还清：已归还总金额=借款金额
            $loan->loan_status = LoanEnums::LOAN_STATUS_PAID_OFF;
        } else if ($paid_return_amount > $loan->amount) {
            //超额归还：已归还总金额＞借款金额
            $loan->loan_status = LoanEnums::LOAN_STATUS_OVER_RETURN;
        }
        $loan->updated_at = $updated_at;
        $bool = $loan->save();
        $after_loan = $loan->toArray();
        if ($bool === false) {
            throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工应扣明细【更新借款单借款状态、归还金额】- 失败 = ' . json_encode($after_loan, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($loan), ErrCode::$BUSINESS_ERROR);
        }
        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 关联借款单后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $after_loan], JSON_UNESCAPED_UNICODE));
        return true;
    }

    /**
     * 操作员工-薪资抵扣-备用金应扣明细
     * @param object $order 薪资抵扣 - 应扣明细信息
     * @param integer $actual_deduct_amount 本期应扣金额
     * @param array $user 当前登陆者信息组
     * @param string $updated_at 更新时间
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    private function handleDeductUserOrderReserveFund($order, $actual_deduct_amount, $user, $updated_at)
    {
        $reserve = ReserveFundApply::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $order->order_id]
        ]);
        //未找到备用金申请单信息
        if (empty($reserve)) {
            throw new ValidationException(static::$t->_('salary_deduct_user_order_reserve_not_found', ['reserve_no' => $order->order_no]), ErrCode::$VALIDATE_ERROR);
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['r' => ReserveFundReturn::class]);
        $builder->leftJoin(ReserveFundApplyReturnRelModel::class, 'rl.return_id = r.id', 'rl');
        $builder->leftJoin(ReserveFundApply::class, 'a.id = rl.apply_id', 'a');
        $builder->columns('rl.return_id');
        $builder->where('rl.apply_id = :apply_id: and rl.is_deleted = :is_deleted: and r.type = :type: and r.status = :status:', ['apply_id' => $order->order_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'type' => Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE, 'status' => Enums::WF_STATE_PENDING]);
        $reserve_return_rel = $builder->getQuery()->getSingleResult();
        //未找到备用金关联的待审核薪资抵扣归还单信息
        if (empty($reserve_return_rel)) {
            throw new ValidationException(static::$t->_('salary_deduct_user_order_reserve_return_error', ['reserve_no' => $order->order_no]), ErrCode::$VALIDATE_ERROR);
        }

        $reserve_return = ReserveFundReturn::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $reserve_return_rel->return_id]
        ]);
        //未找到备用金申请单关联的归还单信息
        if (empty($reserve_return)) {
            throw new ValidationException(static::$t->_('salary_deduct_user_order_reserve_return_error', ['reserve_no' => $order->order_no]), ErrCode::$VALIDATE_ERROR);
        }

        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 更新备用金归还单前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $reserve_return->toArray()], JSON_UNESCAPED_UNICODE));
        //更新备用金归还单信息
        $update_data = [
            'amount' => $actual_deduct_amount,
            'status' => Enums::WF_STATE_APPROVED,
            'approve_at' => $updated_at,
            'updated_at' => $updated_at
        ];
        $bool = $reserve_return->i_update($update_data);
        if ($bool === false) {
            throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工应扣明细【更新备用金归还单申请归还金额总计、审核状态】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($reserve_return), ErrCode::$BUSINESS_ERROR);
        }
        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 更新备用金归还单后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $reserve_return->toArray()], JSON_UNESCAPED_UNICODE));

        //未归还金额 = 本条备用金单中的备用金金额-本条备用金已归还的金额小计（备用金归还单状态=审批同意 && 归还类型是归还、薪资抵扣）
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['r' => ReserveFundReturn::class]);
        $builder->leftJoin(ReserveFundApplyReturnRelModel::class, 'rl.return_id = r.id', 'rl');
        $builder->leftJoin(ReserveFundApply::class, 'a.id = rl.apply_id', 'a');
        $builder->columns('SUM(r.amount) as back_amount');
        $builder->where('rl.apply_id = :apply_id: and rl.is_deleted = :is_deleted: and r.type in ({type:array}) and r.status = :status:', ['apply_id' => $order->order_id, 'is_deleted' => GlobalEnums::IS_NO_DELETED, 'type' => [Enums\ReserveFundReturnEnums::RETURN_TYPE, Enums\ReserveFundReturnEnums::RETURN_SALARY_DEDUCT_TYPE], 'status' => Enums::WF_STATE_APPROVED]);
        $reserve_return_amount = $builder->getQuery()->getSingleResult();
        if (empty($reserve_return_amount)) {
            throw new ValidationException(static::$t->_('salary_deduct_user_order_reserve_return_error', ['reserve_no' => $order->order_no]), ErrCode::$VALIDATE_ERROR);
        }
        $last_back_amount = $reserve->amount - $reserve_return_amount->back_amount;

        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 更新备用金申请单前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $reserve->toArray()], JSON_UNESCAPED_UNICODE));
        //更新备用金申请单信息
        if ($last_back_amount > 0) {
            $reserve->return_status = Enums\ReserveFundReturnEnums::BACK_STATUS_NOT;//未归还金额＞0，状态未归还
        } else if ($last_back_amount == 0) {
            $reserve->return_status = Enums\ReserveFundReturnEnums::BACK_STATUS_BACK;//未归还金额=0，状态 已归还
        } else {
            throw new ValidationException(static::$t->_('salary_deduct_user_order_reserve_unreturned_amount_error', ['reserve_no' => $order->order_no, 'return_no' => $reserve_return->rrno]), ErrCode::$VALIDATE_ERROR);//未归还金额＜0，不存在系统有误；整个操作回滚，需要定位到原因后重新操作
        }
        $reserve->update_at = $updated_at;
        $bool = $reserve->save();
        $after_reserve = $reserve->toArray();
        if ($bool === false) {
            throw new BusinessException('薪资抵扣-确认扣款正确-更新批次-员工应扣明细【更新备用金归申请单归还状态】- 失败 = ' . json_encode($after_reserve, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($reserve), ErrCode::$BUSINESS_ERROR);
        }
        $this->logger->info('confirm-salary_deduct-操作【确认抵扣正确】- 员工应扣明细 - 更新备用金申请单后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $after_reserve], JSON_UNESCAPED_UNICODE));
        return true;
    }

    /**
     * 薪资抵扣总数据-详情页-编辑
     * @param array $params 请求参数组
     * @param array $user 当前登录者信息组
     * @return array
     */
    public function edit($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        //编辑信息的操作结果
        $bool = false;
        $db = $this->getDI()->get('db_oa');
        try {
            //检测下实扣金额
            $actual_deduct_amount = $params['actual_deduct_amount'];
            if (!is_numeric($actual_deduct_amount) || !preg_match(SalaryDeductEnums::SALARY_DEDUCT_AMOUNT_RULE, $actual_deduct_amount)) {
                //仅限数字，两位小数
                throw new ValidationException(static::$t->_('salary_deduct_amount_non_numeric'), ErrCode::$VALIDATE_ERROR);
            }

            //批次id
            $batch_id = $params['batch_id'];
            //检测批次信息是否可操作
            $salary_deduct_batch_info = $this->checkSalaryDeductBatchInfo($batch_id);
            //获取要操作的员工明细信息
            $salary_deduct_batch_user_info = SalaryDeductBatchUserModel::findFirst([
                'conditions' => 'id = :id: and batch_id = :batch_id:',
                'bind' => ['id' => $params['id'], 'batch_id' => $batch_id]
            ]);
            if (empty($salary_deduct_batch_user_info)) {
                throw new ValidationException(static::$t->_('salary_deduct_batch_user_info_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            //检测：实扣金额应于抵扣金额正负号一致
            if (($salary_deduct_batch_user_info->deduct_amount < 0 && $actual_deduct_amount > 0) || ($salary_deduct_batch_user_info->deduct_amount > 0 && $actual_deduct_amount < 0)) {
                throw new ValidationException(static::$t->_('salary_deduct_amount_sign_different'), ErrCode::$VALIDATE_ERROR);
            }

            //检测：实扣金额 <= 应扣金额（因为会出现负数，所以都取绝对值校验）
            $actual_deduct_amount = bcmul($actual_deduct_amount, 1000);
            if (abs($actual_deduct_amount) > abs($salary_deduct_batch_user_info->deduct_amount)) {
                throw new ValidationException(static::$t->_('salary_deduct_amount_wrongful'), ErrCode::$VALIDATE_ERROR);
            }

            $this->logger->info('edit-salary_deduct_batch_user_info-操作编辑变更前数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_user_info->toArray()], JSON_UNESCAPED_UNICODE));

            $db->begin();
            $updated_at =  date('Y-m-d H:i:s');
            //都满足了开始更新
            $update_data = [
                'status' => SalaryDeductEnums::SALARY_DEDUCT_USER_STATUS_SUCCESS,//扣款状态：1成功
                'actual_deduct_amount' => $actual_deduct_amount,//实扣金额
                'company_name' => $params['company_name'],//扣款所属公司
                'updated_at' => $updated_at//更新时间
            ];
            $bool = $salary_deduct_batch_user_info->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('薪资抵扣-编辑员工【实扣金额、扣款所属公司】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_user_info), ErrCode::$BUSINESS_ERROR);
            }

            //变更批次表的实扣状态【批次下所有员工明细-实扣状态-全部成功；则标记为3待payroll终审；否则标记为2处理中】
            $count_data = $this->getSalaryDeductBatchUserStatusCount($batch_id);
            //全部成功；则标记为3待payroll终审
            if ($count_data['total'] == $count_data['success']) {
                $batch_status = SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_PAYROLL;
            } else {
                $batch_status = SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_ING;
            }
            $update_data = [
                'status' => $batch_status,
                'updated_at' => $updated_at//更新时间
            ];
            $bool = $salary_deduct_batch_info->i_update($update_data);
            if ($bool === false) {
                throw new BusinessException('薪资抵扣-编辑员工【变更批次实扣状态】- 失败 = ' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . ';可能的原因是：' . get_data_object_error_msg($salary_deduct_batch_info), ErrCode::$BUSINESS_ERROR);
            }

            $this->logger->info('edit-salary_deduct_batch_user_info-操作编辑变更后数据:' . json_encode(['staff_id' => $user['id'], 'data' => $salary_deduct_batch_user_info->toArray()], JSON_UNESCAPED_UNICODE));

            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $db->rollback();
            $this->logger->warning('edit-salary_deduct_batch_user_info-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $bool? true : false,
        ];
    }

    /**
     * 薪资抵扣借款/薪资抵扣备用金-查询
     * @param array $params 请求参数
     * @param integer $source_type 单据类型：1借款，2备用金
     * @return array
     */
    public function getUserOrderList($params, $source_type)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SalaryDeductBatchUserOrderModel::class);
            $builder->where('source_type = :source_type:', ['source_type' => $source_type]);
            //组合搜索条件
            if (!empty($params['deduct_date'])) {
                //应扣日期筛选
                $builder->andWhere('deduct_date = :deduct_date:', ['deduct_date' => $params['deduct_date']]);
            }
            if (!empty($params['status'])) {
                //实扣状态筛选
                $builder->andWhere('status = :status:', ['status' => $params['status']]);
            }
            $builder->columns('count(DISTINCT batch_id, serial_no) as count');
            $count = intval($builder->getQuery()->getSingleResult()->count);
            if ($count > 0) {
                $builder->columns('batch_id, serial_no, deduct_date, count(id) as data_count, status, created_at, actual_deduct_at');
                $builder->limit($page_size, $offset);
                $builder->orderby('batch_id DESC');
                $builder->groupBy('batch_id, serial_no');
                $items = $builder->getQuery()->execute()->toArray();
                if (!empty($items)) {
                    foreach ($items as &$item) {
                        $item['actual_deduct_at'] = $item['actual_deduct_at'] ?? '';
                        $item['status_text'] = static::$t[SalaryDeductEnums::$salary_deduct_batch_user_order_status[$item['status']]];
                    }
                }
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-salary-deduct-user-order-list-failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 薪资抵扣借款/薪资抵扣备用金-下载应扣明细/下载实扣明细
     * @param array $params 请求参数
     * @param integer $source_type 单据类型：1借款，2备用金
     * @return array
     */
    public function exportUserOrder($params, $source_type)
    {
        try {
            //不传递默认1应扣明细
            $type = empty($params['type']) ? SalaryDeductEnums::EXPORT_TYPE_DEDUCT : $params['type'];
            //excel内容
            $row_values = [];
            //excel表头
            $header = [];
            //文件标题
            $file_title = '';
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SalaryDeductBatchUserOrderModel::class);
            $builder->where('batch_id = :batch_id: and serial_no = :serial_no: and source_type = :source_type:', ['batch_id' => $params['batch_id'], 'serial_no' => $params['serial_no'], 'source_type' => $source_type]);
            if ($type == SalaryDeductEnums::EXPORT_TYPE_ACTUAL_DEDUCT) {
                //若是下载2实扣明细，要查询实扣数据生成时间不为NULL的
                $builder->andWhere('actual_deduct_at is not null');
            }
            $builder->columns('serial_number, staff_id, staff_name, state, wait_leave_state, order_no, amount, node_department_name, apply_date, bank_pay_date, loan_days, deduct_type, deduct_date, deduct_amount, actual_deduct_amount, company_name, status');
            $items = $builder->getQuery()->execute()->toArray();
            if ($source_type == SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_LOAN) {
                //借款
                $header = [
                    static::$t->_('salary_deduct_order_loan_serial_number'), //借款应扣流水号
                    static::$t->_('salary_deduct_order_staff_id'), //借款员工工号
                    static::$t->_('salary_deduct_order_staff_name'), //借款员工姓名
                    static::$t->_('salary_deduct_user_state'), //申请人在职状态
                    static::$t->_('salary_deduct_order_loan_order_no'), //借款单号
                    static::$t->_('salary_deduct_order_amount'), //借款金额
                    static::$t->_('salary_deduct_order_department'),//借款员工所在部门
                    static::$t->_('salary_deduct_order_loan_apply_date'),//借款单申请日期
                    static::$t->_('salary_deduct_order_loan_bank_pay_date'),//借款单银行流水日期
                    static::$t->_('salary_deduct_order_loan_days'),//员工借款时长
                    static::$t->_('salary_deduct_deduct_type'), //抵扣类型
                    static::$t->_('salary_deduct_company_name'), //扣款所属公司
                    static::$t->_('salary_deduct_deduct_amount'), //本期应扣金额
                    static::$t->_('salary_deduct_actual_deduct_amount'), //本期实扣金额
                ];
                $file_title = 'salary_deduct_loan';
                if (!empty($items)) {
                    foreach ($items as $item) {
                        //待离职
                        $state = ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : StaffInfoEnums::STAFF_STATE_IN;
                        $row_values[] = [
                            $item['serial_number'],//借款应扣流水号
                            $item['staff_id'],//借款员工工号
                            $item['staff_name'],//借款员工姓名
                            static::$t->_(StaffInfoEnums::$staff_state[$state]),//申请人在职状态
                            $item['order_no'],//借款单号
                            bcdiv($item['amount'], 1000, 2),//借款金额
                            $item['node_department_name'],//借款员工所在部门
                            $item['apply_date'],//借款单申请日期
                            $item['bank_pay_date'],//借款单银行流水日期
                            $item['loan_days'],//员工借款时长
                            static::$t->_(SalaryDeductEnums::$salary_deduct_type[$item['deduct_type']]),//抵扣类型
                            $item['company_name'],//扣款所属公司
                            bcdiv($item['deduct_amount'], 1000, 2),//本期应扣金额
                            ($item['status'] == SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_STATUS_DONE) ? bcdiv($item['actual_deduct_amount'], 1000, 2) : ''//本期实扣金额
                        ];
                    }
                }
            } else if ($source_type == SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND) {
                //备用金
                $header = [
                    static::$t->_('salary_deduct_order_reserve_serial_number'), //备用金应扣流水号
                    static::$t->_('salary_deduct_order_staff_id'), //借款员工工号
                    static::$t->_('salary_deduct_order_staff_name'), //借款员工姓名
                    static::$t->_('salary_deduct_user_state'), //申请人在职状态
                    static::$t->_('salary_deduct_order_reserve_order_no'), //备用金单号
                    static::$t->_('salary_deduct_order_amount'), //金额
                    static::$t->_('salary_deduct_order_department'),//员工所在部门
                    static::$t->_('salary_deduct_deduct_type'), //抵扣类型
                    static::$t->_('salary_deduct_company_name'), //扣款所属公司
                    static::$t->_('salary_deduct_deduct_amount'), //本期应扣金额
                    static::$t->_('salary_deduct_actual_deduct_amount'), //本期实扣金额
                ];
                $file_title = 'salary_deduct_reserve_fund';
                if (!empty($items)) {
                    foreach ($items as $item) {
                        //待离职
                        $state = ($item['state'] == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) ? StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE : StaffInfoEnums::STAFF_STATE_IN;
                        $row_values[] = [
                            $item['serial_number'],//备用金应扣流水号
                            $item['staff_id'],//借款员工工号
                            $item['staff_name'],//借款员工姓名
                            static::$t->_(StaffInfoEnums::$staff_state[$state]),//申请人在职状态
                            $item['order_no'],//备用金单号
                            bcdiv($item['amount'], 1000, 2),//借款金额
                            $item['node_department_name'],//借款员工所在部门
                            static::$t->_(SalaryDeductEnums::$salary_deduct_type[$item['deduct_type']]),//抵扣类型
                            $item['company_name'],//扣款所属公司
                            bcdiv($item['deduct_amount'], 1000, 2),//本期应扣金额
                            $item['actual_deduct_amount'] ? bcdiv($item['actual_deduct_amount'], 1000, 2) : ''//本期实扣金额
                        ];
                    }
                }
            }

            $file_name = static::$t->_($file_title).'-' . date("Ymd");
            $result = $this->exportExcel($header, $row_values, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];
            return $result;
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('export-salary-deduct-user-order-list:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];

    }

    /**
     * 生成本期
     * 抵扣数据状态
     * */
    public function taskDetail()
    {

        $salary_deduct = SalaryDeductBatchModel::findFirst([
            'conditions' => 'status != :status:',
            'bind'       => ['status' => SalaryDeductEnums::SALARY_DEDUCT_BATCH_STATUS_DONE]
        ]);


        $current_date = date('Y-m-d H:i:s');


        //判断当前时间
        if (!(($current_date >= date('Y-m-06 02:00:00') && $current_date <= date('Y-m-09 23:00:00')) || ($current_date >= date('Y-m-21 02:00:00') && $current_date <= date('Y-m-24 23:00:00')))) {

            $status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_5;
            if (!empty($salary_deduct)) {
                $status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_4;
            }
        } else {
            $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_1;

            if ($current_date >= date('Y-m-06 02:00:00') && $current_date <= date('Y-m-09 23:00:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_1;
            }

            if ($current_date >= date('Y-m-21 02:00:00') && $current_date <= date('Y-m-24 23:00:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_2;
            }

            $task_detail = SalaryDeductTaskModel::findFirst([
                'conditions' => 'month = :month: and type = :type:',
                'bind'       => ['month' => date('Y-m'), 'type' => $type],
            ]);


            if (empty($task_detail)) {
                $status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_1;
                if (!empty($salary_deduct)) {
                    $status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_4;
                }
            } else {
                $status = $task_detail->status;
                if (!empty($salary_deduct) && $task_detail->status == SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_3) {
                    $status = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_3;
                }
            }
        }


        return ['status' => $status];
    }

    public function createTask($uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {

            $status = $this->taskDetail();
            if (!empty($status) && $status['status'] != SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_1) {
                throw new BusinessException('生成本期抵扣数据task失败', ErrCode::$BUSINESS_ERROR);
            }

            $current_date = date('Y-m-d H:i:s');

            if ($current_date >= date('Y-m-06 02:00:00') && $current_date <= date('Y-m-09 23:000:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_1;
            }

            if ($current_date >= date('Y-m-21 02:00:00') && $current_date <= date('Y-m-24 23:00:00')) {
                $type = SalaryDeductEnums::SALARY_DEDUCT_TASK_TYPE_2;
            }


            $obj             = new SalaryDeductTaskModel();
            $obj->created_id = $uid;
            $obj->status     = SalaryDeductEnums::SALARY_DEDUCT_TASK_STATUS_2;
            $obj->type       = $type;
            $obj->month      = date('Y-m');
            $obj->created_at = date('Y-m-d H:i:s');
            $obj->updated_at = date('Y-m-d H:i:s');

            if ($obj->save() === false) {
                throw new BusinessException('生成本期抵扣数据task失败' . get_data_object_error_msg($obj), ErrCode::$BUSINESS_ERROR);
            }


        } catch (BusinessException $e) {
            $code    = $e->getCode();
            $message = static::$t->_('retry_later');
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('create-salary-deduct-task:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];

    }
}