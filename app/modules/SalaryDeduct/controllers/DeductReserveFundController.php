<?php
namespace App\Modules\SalaryDeduct\Controllers;
use App\Library\Enums\SalaryDeductEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\SalaryDeduct\Services\BaseService;
use App\Modules\SalaryDeduct\Services\DeductService;
use App\Util\RedisKey;

/**
 * 薪资抵扣备用金
 * Class DeductReserveFundController
 * @package App\Modules\SalaryDeduct\Controllers
 */
class DeductReserveFundController extends BaseController
{
    /**
     * 薪资抵扣备用金-查询
     * @Permission(action='salary.deduct.reserve_fund')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68592
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, DeductService::$not_must_params);
        try {
            Validation::validate($params, DeductService::$validate_batch_user_order_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = DeductService::getInstance()->getUserOrderList($params, SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 薪资抵扣备用金-下载应扣明细/下载实扣明细
     * @Permission(action='salary.deduct.reserve_fund')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68622
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, DeductService::$not_must_params);
        try {
            Validation::validate($params, array_merge(DeductService::$validate_batch_update, DeductService::$validate_update_batch_user_order));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::SALARY_DEDUCT_DETAILED_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return DeductService::getInstance()->exportUserOrder($params, SalaryDeductEnums::SALARY_DEDUCT_USER_ORDER_SOURCE_TYPE_RESERVE_FUND);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? '');
    }
}