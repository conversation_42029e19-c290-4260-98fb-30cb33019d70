<?php
namespace App\Modules\SalaryDeduct\Controllers;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\SalaryDeduct\Services\BaseService;
use App\Modules\SalaryDeduct\Services\DeductService;
use App\Util\RedisKey;

/**
 * 薪资抵扣总数据
 * Class DeductController
 * @package App\Modules\SalaryDeduct\Controllers
 */
class DeductController extends BaseController
{
    /**
     * 薪资抵扣-默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68577
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = DeductService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 薪资抵扣总数据-查询
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68582
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, DeductService::$not_must_params);
        try {
            Validation::validate($params, DeductService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = DeductService::getInstance()->getList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 薪资抵扣总数据/详情页-导出/下载应扣明细/实扣明细
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68612
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, array_merge(DeductService::$validate_batch_export, DeductService::$validate_batch_update));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::SALARY_DEDUCT_DETAILED_EXPORT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return DeductService::getInstance()->export($params);
        }, $lock_key, 15);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? '');
    }

    /**
     * 薪资抵扣总数据-详情页
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68597
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, DeductService::$not_must_params);
        try {
            Validation::validate($params, DeductService::$validate_batch_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = DeductService::getInstance()->detail($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 薪资抵扣总数据-详情页-本批次数据概述
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68602
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function summaryAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, DeductService::$validate_batch_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = DeductService::getInstance()->summary($params);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 薪资抵扣总数据-详情页-上传实扣金额-异步上传-任务
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68642
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importDeductAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, DeductService::$validate_batch_update);
            $excel_file = $this->request->getUploadedFiles();
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::SALARY_DEDUCT_IMPORT_DEDUCT_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params, $excel_file) {
            return DeductService::getInstance()->importDeductTask($params, $excel_file, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 薪资抵扣总数据-详情页-上传实扣金额-查询最后一次导入成功的结果
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69422
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function importAddResultAction()
    {
        $result = DeductService::getInstance()->getImportResult();
        return $this->returnJson($result['code'], $result['message'], $result['data']);

    }

    /**
     * 薪资抵扣总数据-详情页-确认扣款正确
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68632
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function confirmAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, DeductService::$validate_batch_update);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::SALARY_DEDUCT_CONFIRM_LOCK . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return DeductService::getInstance()->confirm($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * 薪资抵扣总数据-详情页-编辑
     * @Permission(action='salary.deduct')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/68627
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function editAction()
    {
        $params = trim_array($this->request->get());
        try {
            Validation::validate($params, array_merge(DeductService::$validate_batch_update, DeductService::$validate_update_batch_user));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = DeductService::getInstance()->edit($params, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 生成本期抵扣数据按钮状态
     * @Token
     * */

    public function taskDetailAction()
    {

        $res = DeductService::getInstance()->taskDetail();
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * 生成本期抵扣数据task
     * @Token
     * */

    public function createTaskAction()
    {
        $lock_key = md5(RedisKey::SALARY_DEDUCT_CONFIRM_LOCK);
        $uid      = $this->user['id'];
        $res      = $this->atomicLock(function () use ($uid) {
            return DeductService::getInstance()->createTask($uid);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? '', $res['data'] ?? []);
    }
}