<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2020/12/3
 * Time: 5:27 PM
 */


namespace App\Modules\Budget\Controllers;

use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Services\BaseService;
use App\Modules\Budget\Services\BudgetExtendService;
use App\Modules\Budget\Services\BudgetFlowService;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Budget\Services\BudgetSourceDataService;
use App\Modules\Budget\Services\BudgetDataInfoExtendService;
use App\Library\ErrCode;

class BudgetController extends \App\Library\BaseController {

    /**
     * @Token
     */
    public function excel_departmentAction()
    {
        set_time_limit(0);

        $param = $this->request->get();
        $year = $param['year_at'];
        $source_type = $param['source_type'] ?? '';
        $attachments = [
            'bucket_name' => $param['bucket_name'] ?? '',
            'object_key'  => $param['object_key'] ?? '',
            'file_name'   => $param['file_name'] ?? '',
        ];

        Validation::validate($param, [
            'reason' => 'Required|StrLenGeLe:0,1000',//备注
            'attachments' => 'ArrLenGeLe:0,5',//附件信息
            'attachments[*]' => 'Obj',
            'attachments[*].bucket_name' => 'StrLenGeLe:1,63',
            'attachments[*].object_key' => 'StrLenGeLe:1,100',
            'attachments[*].file_name' => 'StrLenGeLe:1,200',
        ]);
        $is_check = $param['check']; //1 第一次导入验证 非1 导入操作
        if (!in_array($is_check, [1, ''])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'is_check param error ', []);
        }

        if (!preg_match('/^\d{4}$/', $year)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'year_at param error ', []);
        }

        if (!in_array($source_type, [BaseService::BUDGET_SOURCE_TYPE_1, BaseService::BUDGET_SOURCE_TYPE_2])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'source_type param error ', []);
        }

        try {
            $this->logger->info('budget_import_user: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

            $excel_file = $this->request->getUploadedFiles();
            if (!empty($excel_file)) {
                $config = ['path' => ''];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile($excel_file[0]->getTempName())
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();

            } else {
                $config = ['path' => BASE_PATH . '/public'];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile('obj_dep.xlsx')
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();

            }

            if (empty($excel_data)) {
                return $this->returnJson(-11, 'excel 无有效数据', null);
            }
            BudgetSourceDataService::$excel_department_params = $param;
            // 加锁处理
            if ($source_type == BaseService::BUDGET_SOURCE_TYPE_1) {//预算追加
                if ($is_check == 1) {
                    $import_res = BudgetSourceDataService::getInstance()->sourceDataCheck($year, $excel_data, true, $this->user, 1);
                } else {
                    $lock_key   = md5('budget_source_data_import_' . $this->user['id']);
                    $import_res = $this->atomicLock(function () use ($year, $excel_data, $attachments) {
                        return BudgetSourceDataService::getInstance()->sourceDataCheck($year, $excel_data, false, $this->user, 1, $attachments);
                    }, $lock_key, 60);
                }
            } else {//预算覆盖
                if ($is_check == 1) {
                    $import_res = BudgetSourceDataService::getInstance()->sourceImportDataCheck($year, $excel_data, true, $this->user, 1);
                } else {
                    $lock_key   = md5('budget_source_data_import_' . $this->user['id']);
                    $import_res = $this->atomicLock(function () use ($year, $excel_data, $attachments) {
                        return BudgetSourceDataService::getInstance()->sourceImportDataCheck($year, $excel_data, false, $this->user, 1, $attachments);
                    }, $lock_key, 60);
                }
            }


            $this->logger->info('source_type-' . $source_type . '===budget_import_result: ' . json_encode($import_res, JSON_UNESCAPED_UNICODE));

            return $this->returnJson($import_res['code'], $import_res['message'], $import_res['data']);

        } catch (\Exception $e) {
            $this->logger->warning('budget_import_result:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', []);
        }
    }

    protected function get_level_code($level,$p_code = ''){
        $num = 3;//code 固定位数 每层级3位
        $con['conditions'] = "level = :level:";
        $con['bind']['level'] = $level;
        $con['columns'] = 'max(level_code)';

        //父目录  不为空
        if(!empty($p_code)){
            $con['conditions'] .= " and level_code like :p_code: ";
            $con['bind']['p_code'] = "{$p_code}%";
        }
        $t = BudgetObject::findFirst($con);
        $code = empty($t) ? '' : $t->toArray()[0];
        if(empty($code) && !empty($p_code))
            $code = $p_code . '001';
        else
            $code = intval($code) + 1;
        return str_pad($code,$level * $num,"0",STR_PAD_LEFT);
    }

    /**
     * 获得所有预算科目
     * @Token
     * @return mixed
     */
    public function getBudgetObjectListAction(){
        try {
            $data = (new BudgetService())->getAllObjectList();
            return $this->returnJson(ErrCode::$SUCCESS,'',$data);
        }catch (\Exception $e){
            $this->logger->warning('get budget object list :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 实时查询-查询列表
     * @Permission(action='budget.budget.get_budget_amount')
     * @return mixed
     */
    public function getBudgetAmountAction()
    {
        $validateParam = [
            'department_id'   => 'Required',
            'cost_store_type' => 'Required|IntIn:1,2',
            'deadline_at'     => 'Required|Date'
        ];
        $data          = $this->request->get();
        try {
            Validation::validate($data, $validateParam);
            $res = (new BudgetService())->getAmount($data, $this->user);
            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->warning('get budget object list :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 实时查询-导出预算列表
     * @Permission(action='budget.budget.export_budget_amount')
     * @return mixed
     */
    public function exportBudgetAmountAction()
    {
        $validateParam = [
            'department_id'   => 'Required',
            'cost_store_type' => 'Required|IntIn:1,2',
            'deadline_at'     => 'Required|Date'
        ];
        $data          = $this->request->get();
        try {
            Validation::validate($data, $validateParam);
            $res = (new BudgetService())->exportAmount($data, $this->user);
            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->warning('get budget object list :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 获得预算数量
     * @Permission(action='budget.search')
     * @return mixed
     */
    public function get_objectAction(){

    }


    /**
     * 月度查询-查询获得预算数量
     * @Permission(action='budget.budget.budget_amount_search')
     * @return mixed
     */
    public function budgetAmountSearchAction()
    {
        $param         = $this->request->get();
        $validateParam = [
            'department_id'   => 'Required',
            'cost_store_type' => 'Required|IntIn:1,2',
            'year_at'         => 'Required'
        ];
        try {
            Validation::validate($param, $validateParam);
            $res = (new BudgetService())->budget_search($param, false, $this->user);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }




    /**
     * 导入列表/查询
     * @Token
     */
    public function importListAction()
    {
        $param = $this->request->get();
        $param = array_filter($param);

        $this->logger->info('budget_import_download: user - ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));
        $this->logger->info('budget_import_list: params - ' . json_encode($param, JSON_UNESCAPED_UNICODE));

        try {
            $validateParam = [
                'creator' => 'StrLenGeLe:1,128|>>>:参数错误[导入人-creator]',// 导入人 工号 / 姓名
                'start_date' => 'Date|>>>:参数错误[导入开始日期-start_date]',//导入开始日期
                'end_date' => 'Date|>>>:参数错误[导入结束日期-end_date]',//导入结束日期
                'object_code' => 'StrLenGeLe:3,10|>>>:参数错误[预算科目-object_code]',//科目code
                'department_id' => 'IntGt:0|>>>:参数错误[公司/部门ID-department_id]',//部门id
                'organization_type' => 'IntIn:1,2|>>>:参数错误[组织机构-organization_type]',//组织机构
                'budget_year' => 'IntGeLe:1000,9999|>>>:参数错误[预算年份-budget_year]',//预算年份
                'approved_status' => 'IntIn:1,2,3,4|>>>:参数错误[申请状态-approved_status]',//申请状态
                'status' => 'IntIn:1,2|>>>:参数错误[生效状态-status]',//生效状态

            ];

            Validation::validate($param, $validateParam);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = BudgetSourceDataService::getInstance()->getList($param, false);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预算导入的下载
     * @Token
     *
     */
    public function downloadAction()
    {
        $param = $this->request->get();
        $param = array_filter($param);
        $param['budget_year'] = $param['budget_year'] ?? date('Y');

        $this->logger->info('budget_import_download: user - ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));
        $this->logger->info('budget_import_download: params - ' . json_encode($param, JSON_UNESCAPED_UNICODE));

        try {
            $validateParam = [
                'creator' => 'StrLenGeLe:3,128|>>>:参数错误[导入人-creator]',// 导入人 工号 / 姓名
                'start_date' => 'Date|>>>:参数错误[导入开始日期-start_date]',//导入开始日期
                'end_date' => 'Date|>>>:参数错误[导入结束日期-end_date]',//导入结束日期
                'object_code' => 'StrLenGeLe:3,10|>>>:参数错误[预算科目-object_code]',//科目code
                'department_id' => 'IntGt:0|>>>:参数错误[公司/部门ID-department_id]',//部门id
                'organization_type' => 'IntIn:1,2|>>>:参数错误[组织机构-organization_type]',//组织机构
                'budget_year' => 'IntGeLe:1000,9999|>>>:参数错误[预算年份-budget_year]',//预算年份
            ];

            Validation::validate($param, $validateParam);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('budget_source_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($param) {
            return BudgetSourceDataService::getInstance()->dataExport($param, $this->user);
        }, $lock_key, 60);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入记录详情
     * @Token
     */
    public function recordDetailAction()
    {
        $param = $this->request->get();
        $validateParam = [
            'id' => 'Required|IntGt:0|>>>:参数错误[id]',
        ];

        try {
            Validation::validate($param, $validateParam);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = BudgetSourceDataService::getInstance()->getDetail($param['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入记录编辑提交
     * @Token
     */
    public function submitAction()
    {
        $param = $this->request->get();

        $this->logger->info('budget_import_resubmit: user - ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));
        $this->logger->info('budget_import_resubmit: params - ' . json_encode($param, JSON_UNESCAPED_UNICODE));

        try {
            $validateParam = [
                'id' => 'Required|IntGt:0|>>>:参数错误[id]',
                'jan_amount' => 'Required|Float|>>>:参数错误[jan_amount]',
                'feb_amount' => 'Required|Float|>>>:参数错误[feb_amount]',
                'mar_amount' => 'Required|Float|>>>:参数错误[mar_amount]',
                'apr_amount' => 'Required|Float|>>>:参数错误[apr_amount]',
                'may_amount' => 'Required|Float|>>>:参数错误[may_amount]',
                'jun_amount' => 'Required|Float|>>>:参数错误[jun_amount]',
                'jul_amount' => 'Required|Float|>>>:参数错误[jul_amount]',
                'aug_amount' => 'Required|Float|>>>:参数错误[aug_amount]',
                'sep_amount' => 'Required|Float|>>>:参数错误[sep_amount]',
                'oct_amount' => 'Required|Float|>>>:参数错误[oct_amount]',
                'nov_amount' => 'Required|Float|>>>:参数错误[nov_amount]',
                'dec_amount' => 'Required|Float|>>>:参数错误[dec_amount]',
            ];

            Validation::validate($param, $validateParam);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = BudgetSourceDataService::getInstance()->addBudget($param, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入模板下载
     * @Token
     *
     */
    public function downloadTemplateAction()
    {
        // 加锁处理
        $lock_key = md5('budget_import_template_download_' . $this->user['id']);
        $res = $this->atomicLock(function() {
            return BudgetSourceDataService::getInstance()->importTemplate();
        }, $lock_key, 10);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 获得所有核算科目
     * @Token
     */
    public function getAllLedgerAccountsAction(){
        $params = $this->request->get();
        $res = LedgerAccountService::getInstance()->getList([],$params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '',$res['data']);
        }
        return $this->returnJson($res['code'],$res['message']);
    }
    /**
     * 根据预算科目id获得核算科目名称和id
     * @Token
     */
    public function getLedgerAccountAction(){
        $budget_id = $this->request->get('budget_id', 'int');
        $product_id = $this->request->get('product_id', 'int');
        $res = LedgerAccountService::getInstance()->getLedgerAccountByBudgetIdOrProdcutId($budget_id, $product_id);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 执行表-查询(年度)
     * @Permission(action='budget.budget.year_budget_list')
     * @return mixed
     */
    public function year_budget_listAction()
    {
        $param['end_date']          = $this->request->get('date_at', 'string');
        $param['department_id']     = $this->request->get('department_id', 'int');
        $param['organization_type'] = $this->request->get('organization_type', 'int');

        if (empty($param['end_date']) || empty($param['department_id']) || empty($param['organization_type']))
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('access_data_request_param_error'));

        try {
            $service = new BudgetExtendService();
            $list    = $service->year_list($param, $this->user);
            if (!empty($list) && is_string($list))
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $list, null);
            return $this->returnJson(ErrCode::$SUCCESS, '', $list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('year_budget_list ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

    }

    /**
     * 点击年度查询 弹窗 订单列表
     * @Token
     */
    public function order_budget_listAction(){
        $param['end_date'] = $this->request->get('end_date', 'string');
        $param['department_id'] = $this->request->get('department_id', 'int');
        $param['organization_type'] = $this->request->get('organization_type', 'int');
        $param['object_code'] = $this->request->get('object_code', 'string');
        $param['between'] = $this->request->get('between', 'int');

        try{
            $service = new BudgetExtendService();
            $list = $service->year_detail_list($param);
            if(!empty($list) && is_string($list))
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $list, null);

            return $this->returnJson(ErrCode::$SUCCESS, '', $list);
        }catch (\Exception $e){
            $this->logger->error('year_budget_list ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 执行表-导出(年度)
     * @Permission(action='budget.budget.year_budget_list')
     * @return mixed
     */
    public function year_budget_exportAction()
    {
        $param['end_date']          = $this->request->get('date_at', 'string');
        $param['department_id']     = $this->request->get('department_id', 'int');
        $param['organization_type'] = $this->request->get('organization_type', 'int');
        $param['is_export']         = 1;
        if (empty($param['end_date']) || empty($param['department_id']) || empty($param['organization_type']))
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, static::$t->_('access_data_request_param_error'));
        try {
            $service = new BudgetExtendService();
            $list    = $service->year_list($param, $this->user);
            if (!empty($list) && is_string($list))
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $list, null);

            return $this->returnJson($list['code'], '', $list['data']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error('year_budget_export ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }

    /**
     * 订单预算详情导出
     * @Token
     */
    public function order_budget_exportAction(){
        $param['end_date'] = $this->request->get('end_date', 'string');
        $param['department_id'] = $this->request->get('department_id', 'int');
        $param['organization_type'] = $this->request->get('organization_type', 'int');
        $param['object_code'] = $this->request->get('object_code', 'string');
        $param['between'] = $this->request->get('between', 'int');
        $param['is_export'] = 1;

        try{
            $service = new BudgetExtendService();
            $list = $service->year_detail_list($param);
            if(!empty($list) && is_string($list))
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $list, null);

            return $this->returnJson($list['code'], '', $list['data']);
        }catch (\Exception $e){
            $this->logger->error('year_budget_list ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
    }


    /**
     * 预算科目明细导出
     * @Date: 2021-09-02 12:13
     * @author: peak pan
     * @Token
     **/
    public function budgetExportAction()
    {
        $param = $this->request->get();
        $param = array_filter($param);

        $this->logger->info('budget_export: user - ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));
        $this->logger->info('budget_export: params - ' . json_encode($param, JSON_UNESCAPED_UNICODE));
        try {
            $validateParam = [
                'object_detail' => 'StrLenGeLe:1,200|>>>:参数错误[科目明细-object_detail]',// 科目明细
                'object_name' => 'StrLenGeLe:1,100|>>>:参数错误[预算科目-object_name]',//预算科目
                'object_type' => 'IntIn:1,2,3|>>>:参数错误[订单类型-object_type]',//订单类型
            ];

            Validation::validate($param, $validateParam);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 加锁处理
        $lock_key = md5('budget_source_data_export_out_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($param) {
            return BudgetDataInfoExtendService::getInstance()->dataExport($param);
        }, $lock_key, 60);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预算审核通过
     * @Token
     * */
    public function budgetApproveAction()
    {
        ini_set('memory_limit', '1024M');

        $id     = $this->request->get('id', 'int');
        $remark = $this->request->get('remark', 'trim', '');

        // 请求参数
        $this->logger->info('财务预算 - 预算审核 - 审批通过请求参数: ' . json_encode($this->request->get(), JSON_UNESCAPED_UNICODE));

        Validation::validate(['id' => $id, 'note' => $remark], BudgetFlowService::$validate_approve);

        $lock_key = md5('lock_key_budget_apply_approve_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $remark) {
            return (new BudgetFlowService())->approve($id, $remark, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 预算审核列表
     * @Token
     * */
    public function budgetListAction()
    {
        $params = $this->request->get();
        $res = BudgetSourceDataService::getInstance()->budgetList($params, $this->user['id'],BudgetSourceDataService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预算审核查看
     * @Token
     * */
    public function budgetApprovedDetailAction()
    {
        $param = $this->request->get();
        $id    = $this->request->get('id');
        try {
            Validation::validate($param, BudgetSourceDataService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = BudgetSourceDataService::getInstance()->detailSource($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 预算审核查看
     * @Token
     * */
    public function getAuditDetailAction()
    {
        $param = $this->request->get();
        $id    = $this->request->get('id');
        try {
            Validation::validate($param, BudgetSourceDataService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = BudgetSourceDataService::getInstance()->getAuditDetail($id, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 预算审核驳回
     * @Token
     * */
    public function budgetRejectAction()
    {
        $id = $this->request->get('id','int');
        $note = $this->request->get('remark', 'trim');
        Validation::validate(['id' => $id, 'note' => $note], BudgetFlowService::$validate_reject);

        $lock_key = md5('lock_key_budget_apply_reject_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $note) {
            return (new BudgetFlowService())->reject($id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 预算审核撤回
     * @Token
     * */
    public function budgetCancelAction()
    {
        $id     = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim', '');

        Validation::validate(['id' => $id, 'note' => $note], BudgetFlowService::$validate_approve);

        $lock_key = md5('lock_key_budget_apply_cancel_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $note) {
            return (new BudgetFlowService())->cancel($id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 预算 - 列表页 - 默认选项配置
     * @Token
     * @return mixed
     */
    public function getSearchPageDefaultOptionsAction()
    {
        try {
            $data = BudgetSourceDataService::getInstance()->getSearchDefault();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);

        } catch (\Exception $e) {
            $this->logger->warning('财务预算 - 预算导入 - 枚举状态列表异常: ' . $e->getMessage());

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 月度查询-获得预算导出
     * @Permission(action='budget.budget.budget_amount_export')
     * @return mixed
     */
    public function budgetAmountExportAction()
    {
        $param         = $this->request->get();
        $validateParam = [
            'department_id'   => 'Required',
            'cost_store_type' => 'Required|IntIn:1,2',
            'year_at'         => 'Required'
        ];

        try {
            Validation::validate($param, $validateParam);
            $lock_key = md5('budget_source_amount_import_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($param) {
                return (new BudgetService())->budgetExport($param, $this->user);
            }, $lock_key, 60);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 月度查询-导出本年度全年的预算
     * @Permission(action='budget.budget.export_all_budget')
     * @return mixed
     */
    public function exportAllBudgetAction()
    {
        $param = $this->request->get();
        try {
            $lock_key = md5('budget_source_all_amount_import_' . $this->user['id']);
            $res      = $this->atomicLock(function () use ($param) {
                return (new BudgetService())->exportAllBudget($param);
            }, $lock_key, 60);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
            }
            return $this->returnJson($res['code'], $res['message']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
    /**
     * @Token
     * 更新子部门预算
     * */
    public function updateBudgetAction(){
        $id    = $this->request->get('id');
        $res = BudgetSourceDataService::getInstance()->updateBudget($id,$this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], '');
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 月度预算-更新子部门预算
     * @Permission(action='budget.budget.update_department_budget')
     * @return mixed
     */
    public function updateDepartmentBudgetAction()
    {
        $param         = $this->request->get();
        try {
            Validation::validate($param, [
                'department_id'   => 'Required|StrLenGeLe:1,20',
                'cost_store_type' => 'Required|IntIn:1,2',
                'level_code'=>'Required|StrLenGeLe:1,20'
            ]);
            $res = BudgetSourceDataService::getInstance()->updateDepartmentBudget($param, $this->user);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['message'], '');
            }
            return $this->returnJson($res['code'], $res['message']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}
