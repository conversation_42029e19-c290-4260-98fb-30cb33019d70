<?php

namespace App\Modules\Budget\Controllers;

use App\Library\Enums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Budget\Services\BaseService;
use App\Modules\Budget\Services\BudgetAdjustService;
use App\Modules\Budget\Services\BudgetAdjustSourceDataService;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class BudgetAdjustController extends \App\Library\BaseController {

    /**
     * 预算调整新增
     * @Permission(action='budget.adjust.apply.add')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function addAction(){
        $params = $this->request->get();
        $instance = BudgetAdjustService::getInstance();

        try {
            Validation::validate($params, BudgetAdjustService::$validate_param);

            // 调整明细公共校验
            $lock_key = md5('lock_key_budget_adjust_add_check_' . $this->user['id']);
            $this->atomicLock(function() use ($instance, $params) {
                return $instance->checkAdjustDetail($params, $this->user, false);
            }, $lock_key, 20);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('lock_key_budget_adjust_add_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($instance, $params) {
            return $instance->saveOne($params, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 科目详情编辑
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function getDefaultAction(){
        try {
            $list = BudgetAdjustService::getInstance()->getDefaultInfo($this->user);
            return $this->returnJson($list['code'], $list['message'], $list['data']);
        } catch (Exception $e) {
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage());
        }
    }

    /**
     * 获得所有预算科目
     * @Token
     * @return mixed
     */
    public function getBudgetObjectListAction(){
        try {
            $data = (new BudgetService())->getAllObjectList($is_budget=1);
            return $this->returnJson(ErrCode::$SUCCESS,'',$data);
        }catch (Exception $e){
            $this->logger->warning('get budget object list :' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', null);
        }
    }

    /**
     * 预算调整申请单列表
     * @Permission(action='budget.adjust.apply.list')
     *
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = $this->request->get();
        $res = BudgetAdjustService::getInstance()->getList($params, $this->user['id'],BudgetAdjustService::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 申请单详情
     * @Permission(action='budget.adjust.apply.detail')
     *
     * @return Response|ResponseInterface
     */
    public function detailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, BudgetAdjustService::$validate_param_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }

        $res = BudgetAdjustService::getInstance()->getAuditDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审核tab详情
     * @Permission(action='budget.adjust.audit.detail')
     *
     * @return Response|ResponseInterface
     */
    public function auditDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, BudgetAdjustService::$validate_param_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = BudgetAdjustService::getInstance()->getAuditDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查询tab详情
     * @Permission(action='budget.adjust.query.detail')
     *
     * @return Response|ResponseInterface
     */
    public function queryDetailAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id');

        try {
            Validation::validate($data, BudgetAdjustService::$validate_param_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = BudgetAdjustService::getInstance()->getAuditDetail($id, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 撤销
     * @Permission(action='budget.adjust.apply.cancel')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function cancelAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');
        $note = $this->request->get('note', 'trim');

        Validation::validate($data, BudgetAdjustService::$validate_param_cancel);

        $lock_key = md5('lock_key_budget_adjust_cancel_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $note) {
            return (new BudgetAdjustService())->cancel($id, $note, $this->user);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 审核通过
     * @Permission(action='budget.adjust.audit.audit')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function approveAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');
        $remark = $this->request->get('remark', 'trim');

        Validation::validate($data, BudgetAdjustService::$validate_param_detail);

        //通过
        $lock_key = md5('lock_key_budget_adjust_approve_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $remark, $data) {
            return (new BudgetAdjustService())->approve($id, $remark, $this->user, $data['data'] ?? [], $data['adjust_detail'] ?? []);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 审核驳回
     * @Permission(action='budget.adjust.audit.audit')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function rejectAction()
    {
        $data = $this->request->get();
        $id = $this->request->get('id', 'int');
        $remark = $this->request->get('remark', 'trim');

        Validation::validate($data, BudgetAdjustService::$validate_param_reject);

        // 驳回
        $lock_key = md5('lock_key_budget_adjust_reject_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($id, $remark, $data) {
            return (new BudgetAdjustService())->reject($id, $remark, $this->user, $data['attachments'] ?? []);
        }, $lock_key, 20);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing']);
    }

    /**
     * 预算调整审核列表
     * @Permission(action='budget.adjust.audit.auditList')
     *
     * @return Response|ResponseInterface
     */
    public function auditListAction()
    {
        $params = $this->request->get();
        $res = BudgetAdjustService::getInstance()->getList($params, $this->user['id'],BudgetAdjustService::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 预算调整回复列表
     * @Token
     */
    public function replyListAction()
    {
        $params = $this->request->get();
        $res = BudgetAdjustService::getInstance()->getList($params, $this->user['id'], BaseService::LIST_TYPE_FYR);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导入模板下载
     * @Permission(action='budget.adjust.audit.excelDepartment')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function downloadTemplateAction()
    {
        $type = $this->request->get('type');
        // 加锁处理
        $lock_key = md5('budget_adjust_import_template_download_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($type) {
            return BudgetAdjustSourceDataService::getInstance()->importAdjustTemplate($type);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 预算调整意见征询
     * @Permission(action='budget.adjust.audit.ask')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function askAction()
    {
        $biz_id = $this->request->get('id', 'int');
        $note = $this->request->get('note','trim');
        $to_staffs = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request = (new BudgetAdjustService())->_getWkReq($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复意见征询
     * @Token
     */
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id','int');
        $note = $this->request->get('note','trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复模块的详情页
     * @Token
     */
    public function replyDetailAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BudgetAdjustService::$validate_param_detail);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        
        $res = BudgetAdjustService::getInstance()->getAuditDetail($data['id'], $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预算调整-查询-查询列表
     * @Permission(action='budget.adjust.query.queryList')
     *
     * @return Response|ResponseInterface
     */
    public function queryListAction()
    {
        $params = $this->request->get();
        $res = BudgetAdjustService::getInstance()->getList($params, $this->user['id'], BaseService::LIST_TYPE_QUERY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 预算调整-查询-数据导出
     * @Permission(action='budget.adjust.query.export')
     *
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();

        // 加锁处理
        $lock_key = md5('budget_adjust_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return BudgetAdjustService::getInstance()->export($params, $this->user['id']);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 预算调整-审核-导入校验
     * @Permission(action='budget.adjust.audit.excelDepartment')
     *
     * @return Response|ResponseInterface
     */
    public function excelDepartmentAction()
    {
        ini_set('memory_limit', '1024M');

        $param = $this->request->get();
        $year = $param['year'];
        $type = $param['type'];
        $is_check = $param['check']; //1 第一次导入验证 非1 导入操作
        if (!in_array($is_check, [1, ''])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'is_check param error ', []);
        }

        if (!in_array($type,[BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INPUT,
            BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT,BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT])) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'budget import type param error ', []);
        }

        try {
            Validation::validate($param, BudgetAdjustService::$validate_param_excel);
            $this->logger->info('budget_import_user: ' . json_encode($this->user, JSON_UNESCAPED_UNICODE));

            $excel_file = $this->request->getUploadedFiles();
            if (!empty($excel_file)) {
                $config = ['path' => ''];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile($excel_file[0]->getTempName())
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();
            } else {
                $config = ['path' => BASE_PATH . '/public'];
                $excel = new \Vtiful\Kernel\Excel($config);
                // 读取文件
                $excel_data = $excel->openFile('obj_dep.xlsx')
                    ->openSheet()
                    ->setSkipRows(1)
                    ->getSheetData();
            }
            if (empty($excel_data)) {
                return $this->returnJson(-11, $this->t->_('budget_adjust_excel_import_empty_data'), null);
            }
            // 校验模板与类型是否一致
            if (count($excel_data[0]) > 25) {
                return $this->returnJson(-13, $this->t->_('budget_adjust_excel_import_type_template_error'), null);
            }

            // 加锁处理
            $lock_key = md5('budget_adjust_source_data_import_' . $this->user['id']);
            if ($is_check == 1) {
                // 预算重复性校验
                $is_repeat = BudgetAdjustSourceDataService::getInstance()->budgetObjectRepeat($excel_data);
                if ($is_repeat) {
                    return $this->returnJson(-22, $this->t->_('budget_adjust_excel_import_budget_code_repeat'), null);
                }
                // 预算有效性校验
                $is_invalid = BudgetAdjustSourceDataService::getInstance()->budgetObjectValidation($param['id'], $excel_data);
                if ($is_invalid) {
                    return $this->returnJson(-23, $this->t->_('budget_adjust_excel_import_over_apply_budget'), null);
                }
                // 预算调整
                $import_res = $this->atomicLock(function () use ($type, $excel_data, $year) {
                    return BudgetAdjustSourceDataService::getInstance()->sourceDataCheck($type, $excel_data, true, $this->user, 1, 0, 0, $year);
                }, $lock_key, 30);

            } else {
                $import_res = $this->atomicLock(function () use ($type, $excel_data, $year) {
                    return BudgetAdjustSourceDataService::getInstance()->sourceDataCheck($type, $excel_data, false, $this->user, 1, 0, 0, $year);
                }, $lock_key, 30);

                // 上传excel文件
                if ($import_res['code'] == ErrCode::$SUCCESS && !empty($param['bucket_name']) && !empty($param['object_key'])) {
                    BudgetAdjustSourceDataService::getInstance()->uploadAdjustFile($param, $param['file_name']);
                }
            }

            return $this->returnJson($import_res['code'], $import_res['message'], $import_res['data']);
        } catch (Exception $e) {
            $this->logger->warning('budget_import_result:' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server error', []);
        }
    }

    /**
     * 预算调整-获取Bp联系人列表
     * @Token
     * @return Response|ResponseInterface
     */
    public function getBpContractListAction(){
        $res = BudgetAdjustService::getInstance()->getBpList();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 预算调整申请 - 创建页 - 基本信息 默认值
     * @Token
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = BudgetAdjustService::getInstance()->getCreatePageBaseInfoDefaultData($this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 预算调整审核 - 编辑页 - 作废/取消
     * @Permission(action='budget.adjust.audit.audit')
     *
     * @return Response|ResponseInterface
     */
    public function switchDetailStatusAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();

            $validate_params = [
                'id'         => 'Required|Int|>>>:param error[id]',
                'status'     => 'Required|IntIn:0,1|>>>:param error[status]'
            ];
            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $data = BudgetAdjustService::getInstance()->switchDetailStatus($params,$this->user);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 预算调整审核 - 编辑页 - 确认调整
     * @Permission(action='budget.adjust.audit.audit')
     * @return Response|ResponseInterface
     */
    public function checkAdjustAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();

            // 调整明细统一校验
            $res = BudgetAdjustService::getInstance()->checkAdjustDetail($params, $this->user, true);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
