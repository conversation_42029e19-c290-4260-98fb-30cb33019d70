<?php

namespace App\Modules\Budget\Controllers;

use App\Library\Enums\BudgetObjectEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BaseService;
use App\Modules\Budget\Services\BudgetEditService;
use App\Library\ErrCode;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class BudgetEditController extends \App\Library\BaseController {

    /**
     * 科目添加
     * @Permission(action='budget.object.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24682
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ObjectAddAction()
    {
        $validate_budget_object = [
            'b_name_en' => 'Required|StrLenGeLe:1,300',
            'b_name_cn' => 'Required|StrLenGeLe:1,300',
            'object_type' => 'Required|Arr|ArrLenGeLe:1,100',
            'is_purchase' => 'Required|IntIn:' . BudgetObjectEnums::IS_PURCHASE_RULE,
        ];
        $params = $this->request->get();
        Validation::validate($params, $validate_budget_object);
        $list = BudgetEditService::getInstance()->BudgetObjectAdd($params, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 科目详情添加
     * @Permission(action='budget.object.product.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/31093
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ObjectProductAddAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        Validation::validate($params, [
            'level_code'  => 'Required|StrLenGeLe:1,300',
            'bop_name_th' => 'Required|StrLenGeLe:1,300',
            'bop_name_en' => 'Required|StrLenGeLe:1,300',
            'bop_name_cn' => 'Required|StrLenGeLe:1,300',
            'object_type' => 'Required|Arr|ArrLenGeLe:1,100',
            'status'      => 'Required|IntIn:1,2|>>>: status  error',
            'is_purchase' => 'Required|IntIn:' . BudgetObjectEnums::IS_PURCHASE_RULE,
        ]);

        $list = BudgetEditService::getInstance()->BudgetObjectProductAdd($params, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 科目详情编辑
     * @Permission(action='budget.object.product.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24778
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ObjectProductEditAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);

        Validation::validate($params, [
            'level_code'  => 'Required|StrLenGeLe:1,300',
            'bop_name_th' => 'Required|StrLenGeLe:1,300',
            'bop_name_en' => 'Required|StrLenGeLe:1,300',
            'bop_name_cn' => 'Required|StrLenGeLe:1,300',
            'object_type' => 'Required|Arr|ArrLenGeLe:1,100',
            'product_id'  => 'Required|StrLenGeLe:1,300',
            'status'      => 'Required|IntIn:1,2|>>>: status  error',
            'is_purchase' => 'Required|IntIn:' . BudgetObjectEnums::IS_PURCHASE_RULE,
        ]);

        $list = BudgetEditService::getInstance()->BudgetObjectProductEdit($params, $this->user);
        return $this->returnJson( $list['code'], $list['message'], $list['data']);
    }

    /**
     * 科目编辑
     * @Permission(action='budget.object.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24738
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ObjectEditAction()
    {
        $validate_budget_object = [
            'level_code' => 'Required|StrLenGeLe:1,300',
            'b_name_en' => 'Required|StrLenGeLe:1,300',
            'b_name_cn' => 'Required|StrLenGeLe:1,300',
            'object_type' => 'Required|Arr|ArrLenGeLe:1,100',
            'is_purchase' => 'Required|IntIn:' . BudgetObjectEnums::IS_PURCHASE_RULE,
        ];
        $params = $this->request->get();
        Validation::validate($params, $validate_budget_object);
        $list = BudgetEditService::getInstance()->BudgetObjectEdit($params, $this->user);
        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 科目删除
     * @Permission(action='budget.object.del')
     */
    public function ObjectDeleteAction(){
        $validate_budget_object = [
            'level_code' => 'Required|StrLenGeLe:1,300',
        ];
        $params = $this->request->get();
        try {
            Validation::validate($params, $validate_budget_object);
            $list = BudgetEditService::getInstance()->BudgetObjecDel($params, $this->user);
            return $this->returnJson( $list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 科目删除
     * @Permission(action='budget.object.product.del')
     */
    public function ObjectProductDelAction(){
        $validate_budget_object = [
            'level_code' => 'Required|StrLenGeLe:1,300',
            'product_id' => 'Required|StrLenGeLe:1,300',
        ];
        $params = $this->request->get();
        try {
            Validation::validate($params, $validate_budget_object);
            $list = BudgetEditService::getInstance()->BudgetObjectProductDel($params, $this->user);
            return $this->returnJson( $list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 科目删除
     * @Permission(action='budget.object.product.del')
     */
    public function ObjectProductDelAllAction(){
        $validate_budget_object = [
            'level_code' => 'Required|StrLenGeLe:1,300',
            'product_ids' => 'Required|Arr|ArrLenGeLe:1,100',
        ];
        $params = $this->request->get();
        try {
            //Validation::validate($params, $validate_budget_object);
            $list = BudgetEditService::getInstance()->BudgetObjectProductDelAll($params, $this->user);
            return $this->returnJson( $list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $list['data'] ?? []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

    /**
     * 科目列表
     * @Permission(action='budget.object.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24702
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ListAction()
    {
        $params = $this->request->get();
        $list_search = [];
        Validation::validate($params, $list_search);
        $list = BudgetEditService::getInstance()->list($params);
        $data = [
            'list' => $list['data'] ?? [],
            'total' => $list['total'] ?? 0,
        ];
        return $this->returnJson( $list['code'], $list['message'], $data);
    }

    /**
     * 科目明细列表
     * @Permission(action='budget.object.product.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24794
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function ObjectProductListAction()
    {
        $params = $this->request->get();
        $list_search = [
            'level_code' => 'Required|StrLenGeLe:1,300',
        ];
        Validation::validate($params, $list_search);
        $list = BudgetEditService::getInstance()->ObjectProductList($params);
        $data = [
            'list' => $list['data'] ?? [],
            'total' => $list['total'] ?? 0,
        ];
        return $this->returnJson($list['code'], $list['message'], $data);
    }

    /**
     * 静态资源类型
     * @Token
     */
    public function ObjectTypeAction(){
        $data = [];
        foreach (BudgetObjectEnums::$budget_object_order_type_text as $k => $v) {
            $t = [
                'code' => $k,
                'text' => $this->t->_($v),
            ];
            $data[] = $t;
        }
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

}
