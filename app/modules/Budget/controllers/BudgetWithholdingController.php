<?php
namespace App\Modules\Budget\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BaseService;
use App\Modules\Budget\Services\BudgetWithholdingService;
use App\Library\BaseController;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;


/**
 * 预算管理-费用预提、预提审核
 * Class BudgetWithholdingController
 * @package App\Modules\Budget\Controllers
 */
class BudgetWithholdingController extends BaseController
{
    /**
     * 费用预提-默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90311
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = BudgetWithholdingService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-创建-默认配置项
     * @Permission(action='budget.withholding.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90314
     * @return Response|ResponseInterface
     */
    public function getAddDefaultAction()
    {
        $res = BudgetWithholdingService::getInstance()->getAddDefault($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-创建/重新提交-导入费用明细
     * @Permission(menu='budget.withholding')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90515
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function importDetailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_detail_add);
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_IMPORT_DETAIL_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params, $excel_file) {
            return BudgetWithholdingService::getInstance()->importDetail($params, $excel_file);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 费用预提-创建
     * @Permission(action='budget.withholding.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90377
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::validateCreate($params));
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_ADD_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->add($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 费用预提-重新提交
     * @Permission(action='budget.withholding.recommit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90419
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function recommitAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, array_merge(BudgetWithholdingService::$validate_id, BudgetWithholdingService::validateCreate($params)));
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_RECOMMIT_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->recommit($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 费用预提-查询
     * @Permission(action='budget.withholding.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90386
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, BudgetWithholdingService::$not_must_params);
        Validation::validate($params, BudgetWithholdingService::$validate_list);
        $res = BudgetWithholdingService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-导出
     * @Permission(action='budget.withholding.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90839
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, BudgetWithholdingService::$not_must_params);
        Validation::validate($params, BudgetWithholdingService::$validate_list);
        // 加锁处理
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->listExport($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 费用预提-撤回
     * @Permission(action='budget.withholding.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90380
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_cancel);
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_CANCEL_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->cancel($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 费用预提-关闭
     * @Permission(action='budget.withholding.close')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90572
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function closeAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_close);
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_CLOSE_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->close($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 费用预提-查看
     * @Permission(action='budget.withholding.view')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90518
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-查看-费用详情-列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90530
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewDetailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->viewDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-查看-费用详情-导出
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90539
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewExportAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        // 加锁处理
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_DETAIL_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->viewDetailExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 费用预提-查看-使用详情-列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90683
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewUseDetailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->viewUseDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 费用预提-查看-使用详情-导出
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90686
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function viewUseExportAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        // 加锁处理
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_DETAIL_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->viewUseDetailExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 预提审核-列表
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90545
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditListAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, BudgetWithholdingService::$not_must_params);
        Validation::validate($params, BudgetWithholdingService::$validate_list);
        $res = BudgetWithholdingService::getInstance()->getList($params, $this->user, BudgetWithholdingService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预提审核-通过
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90563
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function passAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_pass);
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_PASS_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->pass($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 预提审核-驳回
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90560
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_reject);
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_REJECT_LOCK . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->reject($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t->_('sys_processing'), $res['data'] ?? []);
    }

    /**
     * 预提审核-查看
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90521
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditViewAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预提审核-查看-费用详情-列表
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90533
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditViewDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->viewDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预提审核-查看-费用详情-导出
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90542
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditViewDetailExportAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        // 加锁处理
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_DETAIL_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->viewDetailExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 预提审核-查看-使用详情-列表
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90689
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditViewUseDetailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        $res = BudgetWithholdingService::getInstance()->viewUseDetail($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 预提审核-查看-使用详情-导出
     * @Permission(action='budget.withholding.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90692
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function auditViewUseExportAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, BudgetWithholdingService::$validate_id);
        // 加锁处理
        $lock_key = md5(RedisKey::BUDGET_WITHHOLDING_DETAIL_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return BudgetWithholdingService::getInstance()->viewUseDetailExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}
