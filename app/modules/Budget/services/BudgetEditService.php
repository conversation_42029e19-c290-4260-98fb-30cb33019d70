<?php

namespace App\Modules\Budget\Services;


use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetObjectLedger;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Models\BudgetObjectProductLedger;
use App\Modules\Budget\Models\LedgerAccount;
use App\Library\Enums\BudgetObjectEnums;
use App\Util\RedisKey;
use Phalcon\Mvc\Model;
use App\Library\Enums\GlobalEnums;

class BudgetEditService extends BaseService{


    private static $instance;
    /**
     * @return BudgetEditService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 科目列表
     * @param array $params
     * @return mixed
     */
    public function list(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {

            $page_size = !empty($params['page_size']) ? $params['page_size'] : 20;
            $page_num = !empty($params['page']) ? $params['page'] : 1 ;
            $offset = $page_size * ($page_num - 1);

            //整理语言环境
            $name = $this->get_lang_column(static::$language);
            $columns = 'b.level_code,
                        b.name_th as b_name_th,
                        b.name_en as b_name_en,
                        b.name_cn as b_name_cn,
                        b.bar_code,
                        b.is_budget,
                        bop.name_th as bop_name_th,
                        bop.name_en as bop_name_en,
                        bop.name_cn as bop_name_cn,
                        group_concat(DISTINCT(bo.type)) as object_type,
                        b.budget_account,
                        b.budget_description,
                        b.effective_year,
                        b.expiration_year,
                        b.is_purchase,
                        b.id as budget_object_id
                    ';
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['b' => BudgetObject::class]);
            $builder->leftjoin(BudgetObjectOrder::class, 'b.level_code = bo.level_code and bo.is_delete = 0','bo');
            $builder->leftjoin(BudgetObjectProduct::class, 'b.level_code = bop.object_code and bop.is_delete = 0','bop');

            $builder->where('b.is_delete = 0');

            //查询科目详情
            if(!empty($params['level_code'])){
                $builder->andwhere('b.level_code = :level_code: ', ['level_code' => $params['level_code']]);
                $columns .= ",group_concat(DISTINCT(bop.type)) product_type ,group_concat(DISTINCT(bop.id)) product_id";
            }

            //查询科目名字 都根据现在用户所在的语言环境
            if(!empty($params['object_name'])){
                $builder->andwhere("b.{$name} like :object_name:", ['object_name' => "%{$params['object_name']}%"]);
            }

            //查询科目明细名字 都根据现在用户所在的语言环境
            if(!empty($params['object_detail'])){
                $builder->andwhere("bop.{$name} like :object_detail:", ['object_detail' => "%{$params['object_detail']}%"]);
            }

            //查询科目明细名字 都根据现在用户所在的语言环境
            if(!empty($params['object_type'])){
                $builder->andwhere("bo.type = :object_type:", ['object_type' => $params['object_type']]);
            }

            $builder->columns($columns);
            $builder->groupBy("b.level_code");
            if (!empty($params['order'])) {
                $builder->orderBy($params['order']);
            } else {
                $builder->orderBy('b.id desc');
            }

            $total = $builder->getQuery()->execute()->count();
            $builder->limit($page_size, $offset);
            $data = $builder->getQuery()->execute()->toArray();

            if($data){
              foreach ($data as &$data_value){
                  $data_value['budget_description'] = ['account' => $data_value['budget_account'], 'name_en' => $data_value['budget_description']];
              }
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('检索异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'total' => $total,
            'data' => $data
        ];
    }

    /**
     * 科目列表
     * @param array $params
     * @return mixed
     */
    public function ObjectProductList(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {

            $page_size = !empty($params['page_size']) ? $params['page_size'] : 10000;
            $page_num = !empty($params['page']) ? $params['page'] : 1 ;
            $offset = $page_size * ($page_num - 1);
            if(empty($params['level_code'])){
                throw new ValidationException(self::$t['BudgetObjectCodeEmpty'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }
            //整理语言环境
            $name = $this->get_lang_column(static::$language);
            $columns = 'b.level_code,
                        b.name_th as b_name_th,
                        b.name_en as b_name_en,
                        b.name_cn as b_name_cn,
                        b.bar_code as b_bar_code,
                        bop.name_th as bop_name_th,
                        bop.name_en as bop_name_en,
                        bop.name_cn as bop_name_cn,
                        bop.budget_product_account,
                        bop.budget_product_description,
                        group_concat(DISTINCT(bop.type)) as object_type,
                        group_concat(DISTINCT(bop.type)) product_type ,group_concat(bop.id) product_id,
                        bop.status,
                        bop.is_purchase,
                        bop.bar_code
                    ';
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['bop' => BudgetObjectProduct::class]);
            $builder->leftjoin(BudgetObject::class, 'b.level_code = bop.object_code and bop.is_delete = 0','b');
            //查询科目详情
            $builder->andwhere('b.level_code = :level_code: ',[ 'level_code' => $params['level_code']]);
            $builder->columns($columns);
            $builder->andwhere('b.is_delete = :is_delete: and bop.is_delete = :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);
            $builder->groupBy('bop.object_code, bop.name_cn');
            $builder->orderBy(' bop.id desc');
            $total = $builder->getQuery()->execute()->count();
            $builder->limit($page_size, $offset);
            $data = $builder->getQuery()->execute()->toArray();
            if ($data) {
                $budget_object_product_arr = BudgetObjectEnums::$budget_object_product_text;
                foreach ($data as &$data_value) {
                    $data_value['budget_product_description'] = ['account' => $data_value['budget_product_account'], 'name_en' => $data_value['budget_product_description']];
                    $data_value['status_text']                = static::$t->_($budget_object_product_arr[$data_value['status']]);
                }
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('检索异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'total' => $total,
            'data' => $data
        ];
    }

    /**
     * 科目添加提交
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjectAdd(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            if (!empty($data['expiration_year']) && !empty($data['effective_year']) && $data['expiration_year'] < $data['effective_year']) {
                throw new ValidationException(self::$t['expiration_invalid_year'], ErrCode::$VALIDATE_ERROR);
            }
            // 科目名称重复
            $exists = BudgetObject::findFirst([
                'conditions' => '(name_th = :name_th: or name_en = :name_en: or name_cn = :name_cn: ) and is_delete=:is_delete:',
                'columns'    => 'id',
                'bind'       => ['name_th' => trim($data['b_name_th']), 'name_en' => trim($data['b_name_en']), 'name_cn' => trim($data['b_name_cn']), 'is_delete' => GlobalEnums::IS_NO_DELETED]
            ]);

            if (!empty($exists)) {
                throw new ValidationException(self::$t['BudgetObject_existed'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }

            // 重构主表待入库数据
            $apply_data = [
                'budget_account' => $data['budget_description']['account'],
                'budget_description' => $data['budget_description']['name_en'],
                'name_cn' => $data['b_name_cn'],
                'name_en' => $data['b_name_en'],
                'name_th' => $data['b_name_th'],
                'level_code' => $this->get_level_code(1),
                'operate_id' => $user['id'],
                'is_end' => 1,
                'bar_code' => $data['bar_code'] ?? 0,
                'is_budget'=> $data['is_budget'],
                'effective_year'  => $data['effective_year'],
                'expiration_year' => $data['expiration_year'],
                'is_purchase' => $data['is_purchase']
            ];
            $main_model = new BudgetObject();
            $bool = $main_model->i_create($apply_data);

            if ($bool === false) {
                throw new BusinessException('科目创建失败 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
            }
            if (!empty($data['budget_description']['account'])) {
                $ledgerObj = LedgerAccount::findFirst([
                    'conditions' => 'account = ?1',
                    'bind' => [ 1 => $data['budget_description']['account']]
                ])->toArray();
                if (empty($ledgerObj)) {
                    throw new BusinessException('核算科目号不能为空 = ' . json_encode($data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                }
                // 更新预算科目和核算科目关系
                $model = new BudgetObjectLedger();
                $bool = $model->i_create([
                    'budget_id' => $main_model->id,
                    'ledger_id' => $ledgerObj['id']
                ]);
                if (empty($bool)) {
                    throw new BusinessException('创建预算科目和核算科目关系失败 = ' . json_encode($ledgerObj,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                }
            }

            //科目科目
            $form_item = $data['object_type'];
            if(empty($form_item) || !is_array($form_item)){
                throw new BusinessException('科目类型缺少 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ORDER_EMPTY_ERROR);
            }
            // [3.2] 表单项
            foreach ($form_item as $item) {
                $_tmp_form = [
                    'level_code' => $main_model->level_code,
                    'type' => $item,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                    'is_delete' => 0
                ];

                $bool = (new BudgetObjectOrder())->i_create($_tmp_form);
                if ($bool === false) {
                    throw new BusinessException('添加详情失败 = ' . json_encode($item,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ORDER_ADD_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('科目添加提交: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 科目详情添加提交
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjectProductAdd(array $data, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 科目名称重复
            $exists = BudgetObjectProduct::findFirst([
                'conditions' => 'object_code = :object_code: AND (name_th = :name_th: OR name_en = :name_en: OR name_cn = :name_cn:) AND is_delete = :is_delete:',
                'columns'    => 'id',
                'bind'       => [
                    'name_th'     => $data['bop_name_th'],
                    'name_en'     => $data['bop_name_en'],
                    'name_cn'     => $data['bop_name_cn'],
                    'object_code' => $data['level_code'],
                    'is_delete'   => GlobalEnums::IS_NO_DELETED
                ]
            ]);
            if (!empty($exists)) {
                throw new ValidationException(self::$t['BudgetObjectProduct_existed'], ErrCode::$VALIDATE_ERROR);
            }

            // 明细关联的业务模块
            $pno = static::genSerialNo('', RedisKey::BUDGET_OBJECT_PRODUCT_CREATE_COUNTER, 4, date('ymd'));
            foreach ($data['object_type'] as $k => $v) {
                // 重构主表待入库数据
                $apply_data = [
                    'pno' => $pno,
                    'name_cn' => $data['bop_name_cn'],
                    'name_en' => $data['bop_name_en'],
                    'name_th' => $data['bop_name_th'],
                    'budget_product_account'     => $data['budget_product_description']['account'] ?? '',
                    'budget_product_description' => $data['budget_product_description']['name_en'] ?? '',
                    'object_code' => $data['level_code'],
                    'operate_id' => $user['id'],
                    'type' => $v,
                    'is_delete' => GlobalEnums::IS_NO_DELETED,
                    'status' => $data['status'],
                    'is_purchase' => $data['is_purchase'],
                    'bar_code' => $data['bar_code'] ?? 0
                ];

                // 差旅费科目下的明细 template_type 置为1
                if ($data['level_code'] == GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE) {
                    $apply_data['template_type'] = 1;
                }

                $main_model = new BudgetObjectProduct();
                if ($main_model->i_create($apply_data) === false) {
                    throw new BusinessException('科目明细创建失败, 原因可能是:' . get_data_object_error_msg($main_model) . '; 数据:' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                if (!empty($data['budget_product_description']['account'])) {
                    $ledger_obj = LedgerAccount::findFirst([
                        'conditions' => 'account = :account:',
                        'bind' => [ 'account' => $data['budget_product_description']['account']]
                    ]);
                    if (empty($ledger_obj)) {
                        throw new BusinessException('核算科目号不能为空 = ' . json_encode($data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                    }

                    $model = new BudgetObjectProductLedger();
                    $ledger_data = [
                        'product_id' => $main_model->id,
                        'ledger_id' => $ledger_obj->id
                    ];
                    if ($model->i_create($ledger_data) === false) {
                        throw new BusinessException('创建预算科目和核算科目关系失败, 原因可能是:' . get_data_object_error_msg($model) . '; 数据:' . json_encode($ledger_data, JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('科目明细添加失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 科目编辑
     *
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjectEdit(array $data, array $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            if (!empty($data['expiration_year']) && !empty($data['effective_year']) && $data['expiration_year'] < $data['effective_year']) {
                throw new ValidationException(self::$t['expiration_invalid_year'], ErrCode::$VALIDATE_ERROR);
            }
            // 科目名称重复
            $main_model = BudgetObject::findFirst([
                'conditions' => 'level_code = :level_code: ',
                'bind' => ['level_code' => trim($data['level_code'])]
            ]);

            if (empty($main_model)) {
                throw new ValidationException(self::$t['BudgetObject_empty'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }
            //名称重复检验
            $exists = BudgetObject::find([
                'conditions' => '(name_th = :name_th: or name_en = :name_en: or name_cn = :name_cn:) and is_delete = :is_delete:',
                'columns' => 'id,level_code',
                'bind' => ['name_th' => trim($data['b_name_th']) ,'name_en' => trim($data['b_name_en']) ,'name_cn' => trim($data['b_name_cn']),'is_delete' => GlobalEnums::IS_NO_DELETED ]
            ]);
            $exists=$exists->toArray();
            if (!empty($exists)) {
                if(!(1==count($exists)&&$exists[0]['level_code']==$data['level_code'])){
                    throw new ValidationException(self::$t['BudgetObject_existed'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
                }
            }
            //校验该预算科目是否有预算额度未使用完
            $year = date('Y');
            $month_start = $year.'-01';
            $month_end = $year.'-12';
            $all_amount = BudgetObjectDepartmentAmount::find([
                'limit' => 1,
                'columns' => 'id',
                "conditions" => "object_code =:object_code: and month >= :month_start: and month <= :month_end: and is_delete=0 and amount_left>0 ",
                "bind"=>["month_start" => $month_start,"month_end" => $month_end,'object_code'=>trim($data['level_code'])],
            ])->toArray();

            if(!empty($all_amount)&&self::IS_NO_BUDGET==$data['is_budget']){//预算科目剩余预算不为0，不允许撤销预算管控
                throw new ValidationException(self::$t['object_level_code'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }


            // 重构主表待入库数据
            $apply_data = [
                'name_cn'            => $data['b_name_cn'],
                'name_en'            => $data['b_name_en'],
                'name_th'            => $data['b_name_th'],
                'level'              => 1,
                'update_id'          => $user['id'],
                'budget_account'     => $data['budget_description']['account'] ?? '',
                'budget_description' => $data['budget_description']['name_en'] ?? '',
                'bar_code'           => $data['bar_code'] ?? 0,
                'is_budget'          => $data['is_budget'],
                'effective_year'     => $data['effective_year'] ?? '',
                'expiration_year'    => $data['expiration_year'] ?? '',
                'is_purchase' => $data['is_purchase']
            ];
            $bool = $main_model->i_update($apply_data);

            if ($bool === false) {
                throw new BusinessException('科目编辑失败 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
            }
            // 删除历史关联
            BudgetObjectLedger::find([
                'conditions' => 'budget_id = :budget_id: ',
                'bind' => [ 'budget_id' => $main_model->id]
            ])->delete();
            if (!empty($data['budget_description']['account'])) {
                $ledgerObj = LedgerAccount::findFirst([
                    'conditions' => 'account = :account:',
                    'bind' => [ 'account' => $data['budget_description']['account']]
                ]);
                if (empty($ledgerObj)) {
                    throw new BusinessException('核算科目号不能为空 = ' . json_encode($data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                }
                $ledgerObj = $ledgerObj->toArray();
            }
            if (isset($ledgerObj)) {

                // 更新预算科目和核算科目关系
                $model = new BudgetObjectLedger();
                $bool = $model->i_create([
                    'budget_id' => $main_model->id,
                    'ledger_id' => $ledgerObj['id']
                ]);
                if (empty($bool)) {
                    throw new BusinessException('创建预算科目和核算科目关系失败 = ' . json_encode($ledgerObj,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
                }
            }
            //科目类型
            $form_item = $data['object_type'];
            if(empty($form_item) || !is_array($form_item)){
                throw new BusinessException('科目类型缺少 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ORDER_EMPTY_ERROR);
            }

            //查询历史的的type
            $_b_m = BudgetObjectOrder::find([
                'conditions' => 'level_code = ?1 ',
                'bind' => [ 1 => $main_model->level_code]
            ]);

            $old_type_map = [];
            if($_b_m){
                $old_type_map = array_column($_b_m->toArray(),'id','type');
                $_b_m->update([
                    'is_delete' => 1,
                    'updated_at' => gmdate('Y-m-d H:i:s')
                ]);
            }

            $old_type_key = array_keys($old_type_map);
            $need_install = array_diff($old_type_key,$form_item);
            $need_del = array_diff($old_type_key,$form_item);
            //需要删除的
            if($need_del){
                $need_del = array_shift($need_del);
                $m_ObjPro = BudgetObjectProduct::find([
                    'conditions' => 'object_code = :code: and type = :type: and is_delete = 0',
                    'bind' => ['code' => $main_model->level_code ,'type' => $need_del ]
                ]);
                if (!empty($m_ObjPro->toArray())) {
                    throw new ValidationException('科目明细有包含删除的类型', BudgetObjectEnums::$BUDGET_OBJECT_ORDER_ADD_ERROR);
                }
            }

            // [3.2] 表单项
            foreach ($form_item as $item) {
                $_tmp_form = [
                    'level_code' => $main_model->level_code,
                    'type' => $item,
                    'is_delete' => 0
                ];
                //查找如果有就更新没有就
                $_tmp = BudgetObjectOrder::findFirst([
                    'conditions' => 'level_code = ?1 and type = ?2',
                    'bind' => [ 1 => $main_model->level_code,2 => $item]
                ]);
                if($_tmp){
                    $bool = $_tmp->i_update($_tmp_form);
                }else{
                    $bool = (new BudgetObjectOrder())->i_create($_tmp_form);
                }
                if ($bool === false) {
                    throw new BusinessException('更新详情失败 = ' . json_encode($item,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ORDER_ADD_ERROR);
                }

            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
             $this->getDI()->get('logger')->warning('科目编辑异常: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 科目明细删除
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjectProductDel(array $data, array $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $ids = $data['product_id'];
            //用id删除
            $ids = explode(',',$ids);
            $main_model = BudgetObjectProduct::find([
                'conditions' => 'id in ({ids:array}) ',
                'bind' => ['ids' => $ids]
            ]);

            if (empty($main_model)) {
                throw new ValidationException(self::$t['BudgetObjectProduct_empty'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }
            // 重构主表待入库数据
            $apply_data = [
                'update_id' => $user['id'],
                'is_delete' => 1,
                'updated_at' => gmdate('Y-m-d H:i:s')
            ];
            $bool = $main_model->update($apply_data);

            if ($bool === false) {
                throw new BusinessException('科目编辑明细失败 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger')->warning('科目明细删除: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 批量删除
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjectProductDelAll(array $data, array $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        foreach ($data  as $k => $v) {
            $ids = $v['product_id'];
            $this->BudgetObjectProductDel(['product_id' => $ids],$user);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 科目删除
     * @param array $data
     * @param array $user
     * @return array
     */
    public function BudgetObjecDel(array $data, array $user){
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            // 科目名称重复
            $main_model = BudgetObject::findFirst([
                'conditions' => 'level_code = ?1 and is_delete = 0 ',
                'bind' => [1 => trim($data['level_code'])]
            ]);

            if (empty($main_model)) {
                throw new ValidationException(self::$t['BudgetObject_empty'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }

            //科目明细是有清空
            $order_model = BudgetObjectProduct::findFirst([
                'conditions' => 'object_code = ?1 and is_delete = 0',
                'bind' => [1 => trim($data['level_code'])]
            ]);

            if (!empty($order_model)) {
                throw new ValidationException(self::$t['BudgetObjectProduct_existed'], BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }

            // 重构主表待入库数据
            $apply_data = [
                'update_id' => $user['id'],
                'is_delete' => 1
            ];
            $bool = $main_model->i_update($apply_data);
            $this->getDI()->get('logger')->info('科目删除: ' . json_encode($main_model->toArray(),JSON_UNESCAPED_UNICODE));

            if ($bool === false) {
                throw new BusinessException('科目编辑失败 = ' . json_encode($apply_data,JSON_UNESCAPED_UNICODE), BudgetObjectEnums::$BUDGET_OBJECT_ADD_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger')->warning('科目删除: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }
    public function get_level_code($level,$p_code = ''){
        $num = 3;//code 固定位数 每层级3位
        $con['conditions'] = "level = :level:";
        $con['bind']['level'] = $level;
        $con['columns'] = 'max(level_code)';

        //父目录  不为空
        if(!empty($p_code)){
            $con['conditions'] .= " and level_code like :p_code: ";
            $con['bind']['p_code'] = "{$p_code}%";
        }
        $t = BudgetObject::findFirst($con);
        $code = empty($t) ? '' : $t->toArray()[0];
        if(empty($code) && !empty($p_code))
            $code = $p_code . '001';
        else
            $code = intval($code) + 1;
        return str_pad($code,$level * $num,"0",STR_PAD_LEFT);
    }


    /**
     * 科目详情 编辑
     * @param array $param
     * @param array $user
     * @return array
     */
    public function BudgetObjectProductEdit(array $param, array $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $obj_code = $param['level_code'];
            $product_type = $param['object_type'];// 订单类型, 即明细关联的业务模块
            $ids = $param['product_id'];// 1,2,3 明细关联了多少个type, 编辑会提交多少个关系id(所谓的明细ID)
            $ids = explode(',', $ids);

            // 明细是否存在
            $exist_products = BudgetObjectProduct::find([
                'conditions' => 'id in ({ids:array}) ',
                'bind' => ['ids' => $ids]
            ]);
            // 明细为空, 禁止编辑提交
            $exist_products_array = $exist_products->toArray();
            if (empty($exist_products_array)) {
                throw new ValidationException(static::$t->_('BudgetObjectProduct_empty'), ErrCode::$VALIDATE_ERROR);
            }

            // 明细名称是否重复
            $repeated_product = BudgetObjectProduct::findFirst([
                'conditions' => 'object_code = :object_code: AND id NOT IN ({ids:array}) AND (name_th = :name_th: OR name_en = :name_en: OR name_cn = :name_cn:) AND is_delete = :is_delete:',
                'columns' => 'id',
                'bind' => [
                    'name_th' => $param['bop_name_th'],
                    'name_en' => $param['bop_name_en'],
                    'name_cn' => $param['bop_name_cn'],
                    'object_code' => $obj_code,
                    'is_delete' => GlobalEnums::IS_NO_DELETED,
                    'ids' => $ids
                ]
            ]);
            if (!empty($repeated_product)) {
                throw new ValidationException(static::$t->_('BudgetObjectProduct_existed'), BudgetObjectEnums::$BUDGET_OBJECT_ADD_EXISTED);
            }

            // 重置明细状态
            $del_update = [
                'is_delete' => GlobalEnums::IS_DELETED,
                'updated_at' => gmdate('Y-m-d H:i:s')
            ];
            if ($exist_products->update($del_update) === false) {
                throw new BusinessException('明细软删重置失败, 原因可能是:' . get_data_object_error_msg($exist_products), ErrCode::$BUSINESS_ERROR);
            }

            // 明细的pno
            $source_pno = $exist_products_array[0]['pno'];

            $old_type_map = array_column($exist_products_array, 'id', 'type');
            $old_type_key = array_keys($old_type_map);
            $need_install = array_diff($product_type, $old_type_key);
            //交叉包含的需要更新的
            $need_up = array_intersect($product_type, $old_type_key);
            if (!empty($param['budget_product_description']['account'])) {
                $ledger_info = LedgerAccount::findFirst([
                    'conditions' => 'account = ?1',
                    'bind' => [1 => $param['budget_product_description']['account']]
                ]);
                if (empty($ledger_info)) {
                    throw new BusinessException('核算科目号不能为空 = ' . json_encode($param, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }

                $ledger_info = $ledger_info->toArray();
            }

            // 更新原关联的明细关系
            foreach ($need_up as $k => $v) {
                $main_model = BudgetObjectProduct::findFirst([
                    'conditions' => 'id = :id: ',
                    'bind' => ['id' => $old_type_map[$v]]
                ]);
                $up_data = [
                    'name_cn' => $param['bop_name_cn'],
                    'name_en' => $param['bop_name_en'],
                    'name_th' => $param['bop_name_th'],
                    'budget_product_account' => $param['budget_product_description']['account'] ?? '',
                    'budget_product_description' => $param['budget_product_description']['name_en'] ?? '',
                    'object_code' => $obj_code,
                    'operate_id' => $user['id'] ?? 10000,
                    'update_id' => $user['id'] ?? 10000,
                    'is_delete' => GlobalEnums::IS_NO_DELETED,
                    'type' => $v,
                    'status' => $param['status'],
                    'is_purchase' => $param['is_purchase'],
                    'bar_code' => $param['bar_code'] ?? 0,
                ];
                if ($main_model->i_update($up_data) === false) {
                    throw new BusinessException('科目明细更新失败:' . json_encode($up_data,JSON_UNESCAPED_UNICODE) . '; 原因可能是:' . get_data_object_error_msg($main_model), ErrCode::$BUSINESS_ERROR);
                }

                // 删除历史关联
                BudgetObjectProductLedger::find([
                    'conditions' => 'product_id = ?1 ',
                    'bind' => [ 1 => $main_model->id]
                ])->delete();
                if (!empty($ledger_info)) {
                    // 更新预算科目和核算科目关系
                    $model = new BudgetObjectProductLedger();
                    $create_product_ledger_data = [
                        'product_id' => $main_model->id,
                        'ledger_id' => $ledger_info['id']
                    ];
                    if ($model->i_create($create_product_ledger_data) === false) {
                        throw new BusinessException('创建预算科目和核算科目关系失败, 数据:' . json_encode($create_product_ledger_data,JSON_UNESCAPED_UNICODE) . '; 原因可能是:' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            // 创建新明细关系
            foreach ($need_install as $k => $v) {
                $add_data = [
                    'pno' => $source_pno,
                    'object_code' => $obj_code,
                    'name_cn' => $param['bop_name_cn'],
                    'name_en' => $param['bop_name_en'],
                    'name_th' => $param['bop_name_th'],
                    'operate_id' => $user['id'] ?? 10000,
                    'update_id' => $user['id'] ?? 10000,
                    'is_delete' => GlobalEnums::IS_NO_DELETED,
                    'type' => $v,
                    'status' => $param['status'],
                    'is_purchase' => $param['is_purchase']
                ];

                // 差旅费科目下的明细 template_type 置为1
                if ($obj_code == GlobalEnums::BUDGET_TRAVEL_LEVEL_CODE) {
                    $add_data['template_type'] = 1;
                }

                $main_model = new BudgetObjectProduct();
                if ($main_model->i_create($add_data) === false) {
                    throw new BusinessException('科目明细创建失败,数据:' . json_encode($add_data,JSON_UNESCAPED_UNICODE) . ';原因可能是:' . get_data_object_error_msg($main_model), ErrCode::$BUSINESS_ERROR);
                }

                if (!empty($ledger_info)) {
                    $model = new BudgetObjectProductLedger();
                    $create_product_ledger_data = [
                        'product_id' => $main_model->id,
                        'ledger_id' => $ledger_info['id']
                    ];
                    if ($model->i_create($create_product_ledger_data) === false) {
                        throw new BusinessException('创建预算科目和核算科目关系失败, 数据:' . json_encode($create_product_ledger_data,JSON_UNESCAPED_UNICODE) . '; 原因可能是:' . get_data_object_error_msg($model), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('科目明细编辑保存失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

}
