<?php

namespace App\Modules\Budget\Services;


use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectLedger;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Models\BudgetObjectProductLedger;
use App\Modules\Budget\Models\LedgerAccount;
use App\Util\RedisKey;
use App\Library\Enums\BudgetObjectEnums;
use Phalcon\Mvc\Model;

class LedgerAccountService extends BaseService{


    private static $instance;
    /**
     * @return LedgerAccountService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取核算科目
     * @param array $ids 指定筛选的科目id组
     * @param array $params['is_all'=>'0非全部，1全部，不传递默认为1全部', 'account_or_name'=>'科目号或名称模糊搜索'] 其他筛选条件组
     * @return array
     */
    public function getList($ids = [], $params = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            if (!empty($ids)) {
                $data = LedgerAccount::find(
                    [
                        'columns'=>'id,account,name_en,name_cn,name_th,name_en as name',
                        'conditions' => 'id in ({ids:array})',
                        'bind' => ['ids' => $ids]
                    ]
                )->toArray();
            } else {
                //是要所有科目还是部分，默认是所有，为了兼容之前的逻辑
                $is_all = !isset($params['is_all']) ? 1 : $params['is_all'];
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('id,account,name_en,name_cn,name_th, name_en as name');
                $builder->from([LedgerAccount::class]);
                if ($is_all != 1) {
                    $builder->limit(100);
                }
                //按照科目号或者名称搜索
                $account_or_name = isset($params['account_or_name']) && !empty(trim($params['account_or_name'])) ? trim($params['account_or_name']) : '';
                if ($account_or_name) {
                    if (is_numeric($account_or_name)) {
                        //如果是数字则按照科目号搜索
                        $builder->where('account like :account_or_name:', ['account_or_name' => '%'.$account_or_name.'%']);
                    } else {
                        //如果是字符串则按照名称搜索由于之前都是只展示英文，前端也是按照英文筛选，所以保持原有逻辑
                        $builder->where('name_en like :account_or_name:', ['account_or_name' => '%'.$account_or_name.'%']);
                    }
                }
                $data = $builder->getQuery()->execute()->toArray();
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("Ledger-account-getList-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * 根据预算科目id获得核算科目id==不是强关联，aps（北京）节点可以修改
     * @param $budget_id
     * @param $product_id
     * @return array
     */
    public function getLedgerAccountByBudgetIdOrProdcutId($budget_id,$product_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $data['ledger_account_id'] = '0';
        $data['ledger_account_name'] = '';
        $ledger_id = 0;

        try {
            //如果产品id为空
            if(empty($product_id)){
                $object = BudgetObjectLedger::findFirst(
                    [
                        'conditions' => 'budget_id = :budget_id:',
                        'bind' => ['budget_id' => $budget_id]
                    ]
                );
                if (!empty($object)) {
                    $ledger_id = $object->ledger_id;
                }
            }else{
                $product = BudgetObjectProductLedger::findFirst(
                    [
                        'conditions'=>'product_id = :product_id:',
                        'bind'=>['product_id'=>$product_id]
                    ]
                );
                if(!empty($product)){
                    $ledger_id = $product->ledger_id;
                }
            }

            if(!empty($ledger_id)){
                $item = LedgerAccount::findFirst(
                    [
                        'conditions' => 'id = :ledger_account_id:',
                        'bind' => ['ledger_account_id' => $ledger_id]
                    ]
                );
                if (!empty($item)) {
                    $item = $item->toArray();
                    $item['name'] = $item['name_en'];
                    //产品说只要英文
                    /*$name_key = $this->get_lang_column(self::$language);
                    if (!empty($item[$name_key])) {
                        $item['name'] = $item[$name_key];
                    }*/

                    $data['ledger_account_id'] = $item['id'];
                    $data['ledger_account_name'] = $item['name'];
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("Ledger-account-getItem-failed:" . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 根据科目号组获取核算科目列表
     * @param array $accountArr 科目号组
     * @return array
     */
    public function getLedgerListByAccounts($accountArr)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            if (count($accountArr) > 0) {
                $data = LedgerAccount::find(
                    [
                        'columns'=>'id,account,name_en,name_cn,name_th',
                        'conditions' => 'account in ({account:array})',
                        'bind' => ['account' => $accountArr]
                    ]
                )->toArray();
            } else {
                $data = LedgerAccount::find(['columns'=>'id,account,name_en,name_cn,name_th'])->toArray();
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("Ledger-account-getList-failed:" . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 根据核算科目名称模糊搜索
     * 产品要求只支持英文
     * @param string $name 科目号组
     * @return array
     * */
    public function getLedgerListByName($name)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {

            $data = LedgerAccount::find(
                [
                    'columns'    => 'id,account,name_en,name_cn,name_th',
                    'conditions' => 'name_en like :name:',
                    'bind'       => ['name' => '%' . $name . '%']
                ]
            )->toArray();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning("Ledger-account-getList-failed:" . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];

    }
}