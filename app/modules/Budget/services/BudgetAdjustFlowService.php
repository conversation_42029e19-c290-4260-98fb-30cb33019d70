<?php

namespace App\Modules\Budget\Services;

use App\Library\Enums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Budget\Models\BudgetAdjustDetailModel;
use App\Modules\Budget\Models\BudgetAdjustSourceDataModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmountLogModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use FlashExpress\bi\App\Models\SysDepartment;
use Phalcon\Mvc\Model\Resultset;
use Phalcon\Mvc\Phalcon\Mvc\Model;

class BudgetAdjustFlowService extends AbstractFlowService
{


    /**
     * @param $id
     * @param $note
     * @param $user
     * @param $update_data array 更新的数据
     * @param $adjust_detail array 预算表单的数据
     * @return array
     */
    public function approve($id,$note, $user,$update_data=[],$adjust_detail=[])
    {
        ini_set('memory_limit', '1024M');

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException(static::$t->_('workflow_is_empty'), ErrCode::$BUSINESS_ERROR);
            }
            $item = BudgetAdjustModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if ($item->status == BudgetAdjustEnums::BUDGET_ADJUST_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('budget_adjust_status_is_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 表单预算金额调整
            $first_dep = [];
            $department_model = new DepartmentModel();
            if (!empty($adjust_detail)) {
                // 调整金额单位
                $adjust_detail = $this->transferToDbData($adjust_detail);
                $budget_adjust_id = array_column($adjust_detail, 'budget_adjust_id');
                $object_codes = array_column($adjust_detail, 'object_code');
                $department_ids = array_column($adjust_detail, 'budget_department_id');
                $adjust_detail = array_column($adjust_detail,null,'id');
                $adjust_type = array_column($adjust_detail,'budget_adjust_type');
                $year = array_column($adjust_detail,'budget_year');
                $deduction_amount = [];
                $column = [
                    'jan_amount' => $year[0] . '-01',
                    'feb_amount' => $year[0] . '-02',
                    'mar_amount' => $year[0] . '-03',
                    'apr_amount' => $year[0] . '-04',
                    'may_amount' => $year[0] . '-05',
                    'jun_amount' => $year[0] . '-06',
                    'jul_amount' => $year[0] . '-07',
                    'aug_amount' => $year[0] . '-08',
                    'sep_amount' => $year[0] . '-09',
                    'oct_amount' => $year[0] . '-10',
                    'nov_amount' => $year[0] . '-11',
                    'dec_amount' => $year[0] . '-12',
                ];
                $deduction_amount_log = BudgetObjectDepartmentAmountLogModel::find([
                    "conditions" => "budget_adjust_id =:budget_adjust_id: and type= 1 and is_deleted=0",
                    "bind"       => ['budget_adjust_id' => $budget_adjust_id[0]],
                ])->toArray();


                foreach ($adjust_detail as $a_item) {
                    // 明细是否作废
                    if ($a_item['status'] == BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_INVALID) {
                        continue;
                    }
                    //bp 调整扣减金额   对比是否有变动

                    $obj_code    = $a_item['object_code'];
                    $dep_id      = $a_item['budget_department_id'];
                    $org         = $a_item['organization_type'];
                    $amount_type = $a_item['amount_type'];
                    $dk          = "{$dep_id}_{$obj_code}_{$org}_{$amount_type}";

                    foreach ($column as $month => $month_num) {
                        if (isset($a_item[$month]) && $a_item[$month] < 0) {
                            $deduction_amount[$dk][$month_num] = $a_item[$month];
                        }
                    }

                    $budgetAdjustDetailModel = new BudgetAdjustDetailModel();
                    $budgetAdjustDetailModel->save($a_item);
                    // 获取预算部门一级部门
                    $first_dep[] = $department_model->getFirstDepartmentIdByDepartmentId($a_item['budget_department_id']);
                }



                // 同步更新主表
                if ($item->budget_adjust_type != $adjust_type[0] || $item->budget_year != $year[0]) {
                    $item->budget_adjust_type = $adjust_type[0] ?? 0;
                    $item->budget_year = $year[0] ?? '';
                    $item->save();
                }

                if (!empty($deduction_amount_log)) {
                    foreach ($deduction_amount_log as $k_1 => $de_item) {

                        $sk = "{$de_item['department_id']}_{$de_item['object_code']}_{$de_item['organization_type']}_{$de_item['amount_type']}";
                        if (isset($deduction_amount[$sk])) {//同为负向抵消扣减
                            foreach ($deduction_amount[$sk] as $m => $v) {
                                if ($m == $de_item['month']) {

                                    $deduction_amount[$sk][$m] = $v - $de_item['amount'];

                                    if ($deduction_amount[$sk][$m] > 0) {
                                        $deduction_amount_log[$k_1]['amount'] = 0 - $deduction_amount[$sk][$m];
                                        unset($deduction_amount[$sk][$m]);
                                    } else if ($deduction_amount[$sk][$m] == 0) {
                                        unset($deduction_amount[$sk][$m]);
                                        unset($deduction_amount_log[$k_1]);
                                    } else {
                                        unset($deduction_amount_log[$k_1]);
                                    }
                                }
                            }
                        }
                    }

                }
                //预算数据进行返还和扣减
                $year  = $year[0];
                $start = $year . '-01';
                $end   = $year . '-12';


                $department_budget_list = BudgetObjectDepartmentAmount::find([
                    'conditions' => 'month >= :start: AND month <= :end: AND is_delete = 0 AND department_id in({department_id:array}) AND object_code in({object_code:array})',
                    'bind'       => ['start' => $start, 'end' => $end, 'department_id' => $department_ids, 'object_code' => $object_codes],
                    'columns'    => ['id', 'department_id', 'object_code', 'organization_type', 'amount_type', 'month', 'amount', 'amount_left']
                ])->toArray();
                $department_budget_temp = [];
                foreach ($department_budget_list as $budget_amount) {
                    $ba_k = "{$budget_amount['department_id']}_{$budget_amount['object_code']}_{$budget_amount['organization_type']}_{$budget_amount['amount_type']}";

                    foreach ($deduction_amount_log as $k_2 => $v_2) {//返还
                        $adk = "{$v_2['department_id']}_{$v_2['object_code']}_{$v_2['organization_type']}_{$v_2['amount_type']}";

                        if ($ba_k == $adk && $budget_amount['month'] == $v_2['month']) {

                            $department_amount_obj      = BudgetObjectDepartmentAmount::findFirst(
                                [
                                    'conditions' => 'id=?0',
                                    'bind'       => [$budget_amount['id']]
                                ]
                            );
                            $temp                       = [];
                            $temp['department_id']      = $department_amount_obj->department_id;
                            $temp['object_code']        = $department_amount_obj->object_code;
                            $temp['organization_type']  = $department_amount_obj->organization_type;
                            $temp['amount_type']        = $department_amount_obj->amount_type;
                            $temp['month']              = $department_amount_obj->month;
                            $temp['season']             = $department_amount_obj->season;
                            $temp['budget_adjust_id']   = $budget_adjust_id[0];
                            $temp['type']               = 2;
                            $temp['before_amount']      = $department_amount_obj->amount;
                            $temp['before_amount_left'] = $department_amount_obj->amount_left;
                            $temp['amount']             = (0 - $v_2['amount']);
                            $temp['operate_id']         = $user['id'];
                            $temp['created_at']         = date('Y-m-d H:i:s');
                            $temp['updated_at']         = date('Y-m-d H:i:s');
                            $department_budget_temp[]   = $temp;

                            $re_bool = $department_amount_obj->save(['amount' => ($department_amount_obj->amount - $v_2['amount']), 'amount_left' => ($department_amount_obj->amount_left - $v_2['amount'])]);
                            if ($re_bool === false) {
                                $messages = $department_amount_obj->getMessages();
                                throw new BusinessException('预算调整审批-返还失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                            }

                        }
                    }
                    foreach ($deduction_amount as $k_3 => $v_3) {//扣减


                        if ($ba_k == $k_3 && isset($v_3[$budget_amount['month']])) {
                            if ($v_3[$budget_amount['month']] == 0) {
                                continue;
                            }
                            $department_amount_obj = BudgetObjectDepartmentAmount::findFirst(
                                [
                                    'conditions' => 'id=?0',
                                    'bind'       => [$budget_amount['id']]
                                ]
                            );

                            $temp                       = [];
                            $temp['department_id']      = $department_amount_obj->department_id;
                            $temp['object_code']        = $department_amount_obj->object_code;
                            $temp['organization_type']  = $department_amount_obj->organization_type;
                            $temp['amount_type']        = $department_amount_obj->amount_type;
                            $temp['month']              = $department_amount_obj->month;
                            $temp['season']             = $department_amount_obj->season;
                            $temp['budget_adjust_id']   = $budget_adjust_id[0];
                            $temp['type']               = 1;
                            $temp['before_amount']      = $department_amount_obj->amount;
                            $temp['before_amount_left'] = $department_amount_obj->amount_left;
                            $temp['amount']             = $v_3[$budget_amount['month']];
                            $temp['operate_id']         = $user['id'];
                            $temp['created_at']         = date('Y-m-d H:i:s');
                            $temp['updated_at']         = date('Y-m-d H:i:s');
                            $department_budget_temp[]   = $temp;

                            $de_bool = $department_amount_obj->save(['amount' => ($department_amount_obj->amount + $v_3[$budget_amount['month']]), 'amount_left' => ($department_amount_obj->amount_left + $v_3[$budget_amount['month']])]);

                            if ($de_bool === false) {
                                $messages = $department_amount_obj->getMessages();
                                throw new BusinessException('预算调整审批-扣减失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                            }

                        }

                    }
                }


                if (!empty($department_budget_temp)) {
                    $log_bool = (new BudgetObjectDepartmentAmountLogModel())->batch_insert($department_budget_temp);
                    $this->logger->info('budget-adjust-approved-deduction' . json_encode($deduction_amount));
                    if ($log_bool === false) {
                        throw new BusinessException(static::$t->_('预算调整bp审批-预扣减返还记录失败'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
                    }
                }

            // 更新预算数据
            } elseif (!empty($update_data)) {
                $detailItems = BudgetAdjustDetailModel::find([
                    'budget_adjust_id = :budget_adjust_id:',
                    'bind' => ['budget_adjust_id' => $id]
                ])->toArray();
                $detailItems = array_column($detailItems,null,'object_code');
                // 预算导入入库操作
                foreach ($update_data as $k => $data_item) {
                    $update_data[$k]['budget_adjust_id'] = $id;
                    $update_data[$k]['budget_adjust_detail_id'] = $detailItems[$data_item['budget_object_code']]['id'];
                    // 获取预算部门一级部门
                    $first_dep[] = $department_model->getFirstDepartmentIdByDepartmentId($data_item['budget_department_id']);
                }

                $write_res = BudgetAdjustSourceDataService::getInstance()->writeBudgetSourceData($update_data);
                if (!$write_res) {
                    throw new BusinessException('Budget adjust source data inserted error!', ErrCode::$BUSINESS_ERROR);
                }
            } elseif(!empty($work_req->node_tag)) {
                throw new ValidationException(static::$t->_('budget_adjust_data_empty'), ErrCode::$VALIDATE_ERROR);
            } else {
                $detailItems = BudgetAdjustDetailModel::find([
                    'budget_adjust_id = :budget_adjust_id:',
                    'bind' => ['budget_adjust_id' => $id]
                ])->toArray();
                // 预算导入入库操作
                foreach ($detailItems as $k => $data_item) {
                    $first_dep[] = $department_model->getFirstDepartmentIdByDepartmentId($data_item['budget_department_id']);
                }
            }
            $params['first_department'] = array_values(array_unique(array_filter($first_dep)));

            $ws = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($item, $user, $params),$note);
            if (!empty($result->approved_at)) {
                // 调整预算
                $update_result = $this->updateBudgetData($id,$user,$adjust_detail);
                // 更新预算调整主表状态
                $bool = $item->i_update([
                    'status' => BudgetAdjustEnums::BUDGET_ADJUST_STATUS_APPROVAL,
                    'remark' => $note,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'approved_at' => $result->approved_at
                ]);
                if ($bool === false) {
                    throw new BusinessException(static::$t->_('budget_adjust_update_failed'), ErrCode::$BUSINESS_ERROR);
                }
                // 更新预算调整明细表导入字段
                $details = BudgetAdjustDetailModel::find([
                    'budget_adjust_id = :budget_adjust_id:',
                    'bind' => ['budget_adjust_id' => $id]
                ]);
                foreach ($details as $detail) {
                    if ($detail->status == BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_INVALID) {
                        continue;
                    }
                    $budget_adjust_detail_id = $detail->object_code;
                    $amount_object = $update_result['amount_objects'][$budget_adjust_detail_id] ?? [];
                    $bool = $detail->i_update([
                        'before_amount' => $amount_object['before_amount'] ?? 0,
                        'after_amount' => $amount_object['after_amount'] ?? 0,
                        'import_method' => $update_result['import_method'] ?? 0
                    ]);
                    if ($bool === false) {
                        throw new BusinessException(static::$t->_('budget_adjust_update_failed'), ErrCode::$BUSINESS_ERROR);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException(static::$t->_('workflow_is_empty'), ErrCode::$BUSINESS_ERROR);
            }
            $item = BudgetAdjustModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            //撤回需要返还扣减
            $budget_adjust_detail = $item->getDetails()->toArray();

            if ($item->status == BudgetAdjustEnums::BUDGET_ADJUST_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('budget_adjust_status_is_error'), ErrCode::$VALIDATE_ERROR);
            }
            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException(static::$t->_('workflow_process_do_failed'), ErrCode::$BUSINESS_ERROR);
            }
            $bool = $item->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note
            ]);
            if ($bool === false) {
                throw new BusinessException('更新Reimbursement表失败', ErrCode::$CONTRACT_UPDATE_ERROR);
            }
            $return_budget_bool = BudgetAdjustService::getInstance()->returnBudget($budget_adjust_detail, $user['id'], $id);
            if ($return_budget_bool === false) {
                throw new BusinessException(static::$t->_('budget_adjust_return_fail'), ErrCode::$BUSINESS_ERROR);

            }
            $db->commit();

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {

            $db->begin();

            $item = BudgetAdjustModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            $budget_adjust_detail = $item->getDetails()->toArray();

            if (empty($item)) {
                throw new BusinessException(static::$t->_('budget_adjust_not_exist'), ErrCode::$BUSINESS_ERROR);
            }
            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($item, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$CONTRACT_WORK_FLOW_REJECT_ERROR);
            }
            $bool = $item->i_update([
                'status' => BudgetAdjustEnums::BUDGET_ADJUST_STATUS_CANCEL,
                'updated_at' => date('Y-m-d H:i:s'),
                'remark' => $note
            ]);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('budget_adjust_update_failed'), ErrCode::$BUSINESS_ERROR);
            }
            $return_budget_bool = BudgetAdjustService::getInstance()->returnBudget($budget_adjust_detail, $user['id'], $id);
            if ($return_budget_bool === false) {
                throw new BusinessException(static::$t->_('budget_adjust_return_fail'), ErrCode::$BUSINESS_ERROR);

            }

            $db->commit();
        } catch (ValidationException $e) {               //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-cancel-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
        ];
    }

    /**
     * 找最新的request
     * @param $id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($id)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :lid:',
                'bind' => ['type'=> Enums::WF_BUDGET_ADJUST_TYPE ,'lid' => $id],
                'order'=>'id desc'
            ]
        );
    }

    /**
     * @param $item
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($item, $user)
    {
        $data['id'] = $item->id;
        $data['name'] = $item->adjust_no . '审批申请';
        $data['biz_type'] = Enums::WF_BUDGET_ADJUST_TYPE;

        //报销用的是申请人id，申请人名字
        $user['id'] = $item->apply_id;
        $user['name'] = $item->apply_name;

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($item, $user));
    }

    /**
     * 获取审批流需要数据
     * @param $item object
     * @param $user array
     * @param $params array
     * @return array
     */
    public function getWorkflowParams($item, $user, $params = [])
    {
        // 金额根据币种汇率转换为系统默认币种的额度
        $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($item->total_amount, $item->exchange_rate, 0);

        // 申请人一级部门
        $apply_first_dep_id = !empty($item->apply_department_id) ?
            (new DepartmentModel())->getFirstDepartmentIdByDepartmentId($item->apply_department_id) : 0;
        // 预算部门负责人
        $manager_id = 0;
        $first_department = [];
        if (!empty($item->budget_department_id)) {
            $dep_model = DepartmentModel::getFirst($item->budget_department_id);
            $manager_id = $dep_model->manager_id;
        }
        if (isset($params['first_department']) && !empty($params['first_department'])) {
            $params['first_department'] = array_values(array_diff($params['first_department'],[$apply_first_dep_id]));
            if (!empty($params['first_department'])) {
                $dep_info = DepartmentModel::find([
                    'id in({ids:array})',
                    'bind' => ['ids'=> $params['first_department']]
                ])->toArray();
                $first_department = !empty($dep_info) ? array_values(array_unique(array_column($dep_info,'manager_id'))): [];
            }
        }

        return [
            'amount'   => $default_currency_amount,
            'store_id' => $item->cost_store_id,
            'company_id' => $item->cost_company_id,
            'node_department_id' => $item->apply_department_id,
            'submitter_id' => $item->apply_id,
            'type' => $item->budget_adjust_type,
            'sys_department_id' => $apply_first_dep_id,
            'budget_department_id' => $item->budget_department_id,
            'manager_id' => $manager_id,
            'first_department' => $params['first_department'] ?? [],
            'first_department_manager' => $first_department
        ];
    }

    /**
     * 根据业务类型获取审批流ID
     *
     * @param null $model
     * @return void
     */
    public function getFlowId($model=null){
    }


    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function recommit($item,$user){
        $req = $this->getRequest($item->id);
        if(empty($req)){
            throw new BusinessException("没有找到req=".$item->id);
        }

        //老的改成被遗弃
        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->save();

        return $this->createRequest($item,$user);
    }

    /**
     * 更新预算调整数据到预算表
     *
     * @param $id int 调整单ID
     * @param $user array 用户信息
     * @param $budget_adjust_detail array 预算详情
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateBudgetData(int $id, array $user, $budget_adjust_detail = [])
    {
        if (empty($budget_adjust_detail)) {
            // 预算调整单
            $budget_adjust = BudgetAdjustModel::getFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $id],
                ]
            )->toArray();
            if (empty($budget_adjust)) {
                throw new BusinessException('Budget adjust is not exist, budget_adjust_id = ' . $id, ErrCode::$BUSINESS_ERROR);
            }
        }

        $import_res['code'] = ErrCode::$SUCCESS;
        $import_res['message'] = '';
        // 预算调整源数据
        $sourceData = BudgetAdjustSourceDataModel::find(
            [
                'conditions' => 'budget_adjust_id = :id:',
                'bind' => ['id' => $id],
            ]
        )->toArray();
        if (empty($sourceData) || !empty($budget_adjust_detail)) {
            if (empty($budget_adjust_detail)) {
                // 预算调整明细
                $budget_adjust_detail = BudgetAdjustDetailModel::find(
                    [
                        'conditions' => 'budget_adjust_id = :id:',
                        'bind' => ['id' => $id],
                    ]
                )->toArray();
                if (empty($budget_adjust_detail)) {
                    throw new BusinessException('Budget adjust detail is not exist', ErrCode::$BUSINESS_ERROR);
                }
            } else {
                $budget_adjust_detail = array_values($budget_adjust_detail);
            }
            $type = $budget_adjust_detail[0]['budget_adjust_type'] ?? '';
            $year = $budget_adjust_detail[0]['budget_year'] ?? '';

            // 将表单明细转为excel格式数据
            $sourceData = $this->transferToSourceData($budget_adjust_detail);
            // 是否明细都作废
            if (empty($sourceData)) {
                return [];
            }
        } else {
            $type = $sourceData[0]['import_budget_type'];
            $year = $sourceData[0]['budget_year'];
        }
        // 将源数据转为excel格式数据
        $excel_data = $this->transferToExelData($sourceData);
        // 取源数据科目对应调整前后总金额
        $total_amount = $this->getLevelCodeTotalAmount($sourceData);

        // 数据校验
        $import_res = BudgetAdjustSourceDataService::getInstance()->sourceDataCheck($type, $excel_data, false, $user, 1, 0, 1, $year, true, $id);
        if (ErrCode::$SUCCESS != $import_res['code'] || $import_res['data']['error_num'] > 0) {
            if ($import_res['data']['error_num'] > 0) {
                $import_res['message'] = explode("｜",$import_res['data']['data'][0]['error'])[0];
            }
            throw new ValidationException($import_res['message'], ErrCode::$BUSINESS_ERROR);
        }

        // 校验结果
        $excel_data = $import_res['data']['excel_data'];
        $need_delete = $excel_data['need_delete'];
        $budget_adjust_data = $excel_data['budget_object_department'];
        $budget_adjust_detail_data = $excel_data['budget_object_department_amount'];

        $obj_amount_model = new BudgetObjectDepartmentAmount();
        $obj_model = new BudgetObjectDepartment();

        // 存在需要删除的数据
        if (!empty($need_delete)) {
            $db = $this->getDI()->get('db_oa');
            $month_start = $year.'-01';
            $month_end = $year.'-12';
            foreach ($need_delete as $del) {
                $deps_str = "'" . implode("','", $del['department_id']) . "'";//多个部门id
                $object_code = $del['object_code'];
                $organization_type = $del['organization_type'];

                // 科目部门金额关系表状态更新条件
                $obj_dept_amount_update_conditions = "department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0";
                $obj_dept_amount_update_res = $db->updateAsDict(
                    $obj_amount_model->getSource(),
                    ['is_delete' => GlobalEnums::IS_DELETED],
                    ['conditions' => $obj_dept_amount_update_conditions]
                );
                if ($obj_dept_amount_update_res === false) {
                    throw new BusinessException("budget_object_department_amount 软删失败, where={$obj_dept_amount_update_conditions}", ErrCode::$BUSINESS_ERROR);
                }

                // 科目部门表软删条件
                $obj_dept_update_conditions = "department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0";
                $obj_dept_update_res = $db->updateAsDict(
                    $obj_model->getSource(),
                    ['is_delete' => GlobalEnums::IS_DELETED],
                    ['conditions' => $obj_dept_update_conditions]
                );

                if ($obj_dept_update_res === false) {
                    throw new BusinessException("budget_object_department 软删失败, where={$obj_dept_update_conditions}", ErrCode::$BUSINESS_ERROR);
                }
            }
        }

        // 科目部门新数据写入
        if ($obj_model->batch_insert($budget_adjust_data) === false) {
            throw new BusinessException('budget_object_department 批量写入失败, 数据=' . json_encode($budget_adjust_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 科目部门金额新数据写入
        if ($obj_amount_model->batch_insert($budget_adjust_detail_data) === false) {
            throw new BusinessException('budget_object_department_amount 批量写入失败, 数据=' . json_encode($budget_adjust_detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
        }

        // 返回的公共数据
        return [
            'amount_objects' => $total_amount,
            'import_method' => $type,
            'excel_data' => $excel_data // task 调用需要
        ];
    }

    private function getLevelCodeTotalAmount($sourceData) {
        $month = BaseService::getCurrentYearMonth();
        $amount_objects = [];

        foreach ($sourceData as $item) {
            $object_code = $item['budget_object_code'];
            // 取该科目历史预算总额
            $old_sum = BudgetObjectDepartmentAmount::sum(
                [
                    'column' => 'amount',
                    'conditions'=>'department_id = :department_id: and object_code = :object_code: and month < :month: and organization_type = :organization_type: and amount_type = :amount_type:',
                    'bind'=>[
                        'department_id' => $item['budget_department_id'],
                        'object_code'   => $item['budget_object_code'],
                        'month' => $month,
                        'organization_type' => $item['organization_type'],
                        'amount_type' => $item['amount_type'],
                    ]
                ]
            );
            $old_sum = !empty($old_sum) ? $old_sum : 0;
            // 取该科目预算总额
            $sum = BudgetObjectDepartmentAmount::sum(
                [
                    'column' => 'amount',
                    'conditions'=>'department_id = :department_id: and object_code = :object_code: and organization_type = :organization_type: and amount_type = :amount_type:',
                    'bind'=>[
                        'department_id' => $item['budget_department_id'],
                        'object_code'   => $item['budget_object_code'],
                        'organization_type' => $item['organization_type'],
                        'amount_type' => $item['amount_type'],
                    ]
                ]
            );
            $sum = !empty($sum) ? $sum : 0;
            $amount_objects[$object_code]['before_amount'] = $sum;
            // 导入的总金额
            $after_amount = $this->getAfterImportAmount($item);
            $amount_objects[$object_code]['after_amount'] = $old_sum + $after_amount;
        }

        return $amount_objects;
    }

    private function getAfterImportAmount($item) {
        $year = BaseService::getCurrentYear();
        $month = BaseService::getCurrentYearMonth();
        $total = 0;
        $month_maps = [
            'jan_amount' => $year.'-01',
            'feb_amount' => $year.'-02',
            'mar_amount' => $year.'-03',
            'apr_amount' => $year.'-04',
            'may_amount' => $year.'-05',
            'jun_amount' => $year.'-06',
            'jul_amount' => $year.'-07',
            'aug_amount' => $year.'-08',
            'sep_amount' => $year.'-09',
            'oct_amount' => $year.'-10',
            'nov_amount' => $year.'-11',
            'dec_amount' => $year.'-12'
        ];
        foreach ($item as $field => $value) {
            if (isset($month_maps[$field]) && $month_maps[$field] >= $month) {
                $total += $value;
            }
        }

        return $total;
    }

    /**
     * 转化为excel导出格式
     * @param $sourceData array
     * @param $is_div bool
     * @return mixed
     */
    public function transferToExelData($sourceData, $is_div = true){
        $all_data = [];

        $budgetCodes = array_column($sourceData, 'object_code');
        $budgetList = BudgetObject::find([
            'conditions' => 'level_code in ("'.implode('","',$budgetCodes).'")'
        ])->toArray();
        $budgetCodeList = array_column($budgetList,'name_cn','level_code');
        // 取部门名称
        $dep_ids = array_values(array_unique(array_filter(array_column($sourceData, 'budget_department_id'))));
        $sysDepList = \App\Modules\Hc\Models\SysDepartmentModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind' => ['ids' => $dep_ids]
        ])->toArray();
        $sysDepList = array_column($sysDepList,'name','id');

        foreach ($sourceData as $source) {
            $inout_data = [];
            $inout_data[] = $source['budget_object_name'] ?? ($budgetCodeList[$source['object_code']] ?? '');
            $inout_data[] = isset($sysDepList[$source['budget_department_id']]) ? $sysDepList[$source['budget_department_id']] : $source['budget_department_name'];
            $inout_data[] = 1 == $source['organization_type'] ? static::$t->_('payment_cost_store_type_2') : static::$t->_('payment_cost_store_type_1');
            $inout_data[] = 1 == $source['amount_type'] ? static::$t->_('budget_amount_type.1') : static::$t->_('budget_amount_type.2');
            $inout_data[] = $is_div ? $source['jan_amount'] / 1000 : $source['jan_amount'];
            $inout_data[] = $is_div ? $source['feb_amount'] / 1000 : $source['feb_amount'];
            $inout_data[] = $is_div ? $source['mar_amount'] / 1000 : $source['mar_amount'];
            $inout_data[] = $is_div ? $source['apr_amount'] / 1000 : $source['apr_amount'];
            $inout_data[] = $is_div ? $source['may_amount'] / 1000 : $source['may_amount'];
            $inout_data[] = $is_div ? $source['jun_amount'] / 1000 : $source['jun_amount'];
            $inout_data[] = $is_div ? $source['jul_amount'] / 1000 : $source['jul_amount'];
            $inout_data[] = $is_div ? $source['aug_amount'] / 1000 : $source['aug_amount'];
            $inout_data[] = $is_div ? $source['sep_amount'] / 1000 : $source['sep_amount'];
            $inout_data[] = $is_div ? $source['oct_amount'] / 1000 : $source['oct_amount'];
            $inout_data[] = $is_div ? $source['nov_amount'] / 1000 : $source['nov_amount'];
            $inout_data[] = $is_div ? $source['dec_amount'] / 1000 : $source['dec_amount'];
            $all_data[] = $inout_data;
        }

        return $all_data;
    }

    /**
     * 转化为源数据格式
     * @param $budgetAdjust array
     * @return mixed
     */
    private function transferToSourceData($budgetAdjust = []){
        $res = [];

        foreach ($budgetAdjust as $budgetItem) {
            if ($budgetItem['status'] == BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_INVALID){
                continue;
            }
            $res[] = [
                'budget_object_code' => $budgetItem['object_code'],
                'budget_object_name' => $budgetItem['object_name'],
                'budget_department_id' => $budgetItem['budget_department_id'],
                'budget_department_name' => $budgetItem['budget_department_name'],
                'organization_type' => $budgetItem['organization_type'],
                'amount_type' => $budgetItem['amount_type'],
                'jan_amount' => $budgetItem['jan_amount'],
                'feb_amount' => $budgetItem['feb_amount'],
                'mar_amount' => $budgetItem['mar_amount'],
                'apr_amount' => $budgetItem['apr_amount'],
                'may_amount' => $budgetItem['may_amount'],
                'jun_amount' => $budgetItem['jun_amount'],
                'jul_amount' => $budgetItem['jul_amount'],
                'aug_amount' => $budgetItem['aug_amount'],
                'sep_amount' => $budgetItem['sep_amount'],
                'oct_amount' => $budgetItem['oct_amount'],
                'nov_amount' => $budgetItem['nov_amount'],
                'dec_amount' => $budgetItem['dec_amount']
            ];
        }

        return $res;
    }

    /**
     * 转化数据金额格式
     * @param $adjust_detail array
     * @return mixed
     */
    private function transferToDbData($adjust_detail = []){
        foreach ($adjust_detail as &$data) {
            $data['jan_amount'] = bcmul($data['jan_amount'],1000,2);
            $data['feb_amount'] = bcmul($data['feb_amount'],1000,2);
            $data['mar_amount'] = bcmul($data['mar_amount'],1000,2);
            $data['apr_amount'] = bcmul($data['apr_amount'],1000,2);
            $data['may_amount'] = bcmul($data['may_amount'],1000,2);
            $data['jun_amount'] = bcmul($data['jun_amount'],1000,2);
            $data['jul_amount'] = bcmul($data['jul_amount'],1000,2);
            $data['aug_amount'] = bcmul($data['aug_amount'],1000,2);
            $data['sep_amount'] = bcmul($data['sep_amount'],1000,2);
            $data['oct_amount'] = bcmul($data['oct_amount'],1000,2);
            $data['nov_amount'] = bcmul($data['nov_amount'],1000,2);
            $data['dec_amount'] = bcmul($data['dec_amount'],1000,2);
            $data['total_amount'] = bcmul($data['total_amount'],1000,2);
        }

        return $adjust_detail;
    }
}
