<?php

namespace App\Modules\Budget\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Library\ErrCode;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Budget\Models\BudgetAdjustDetailModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmountLogModel;
use App\Modules\Budget\Models\BudgetSourceMainModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\StoreService;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Phalcon\Mvc\Model;

class BudgetAdjustService extends BaseService{


    private static $instance;
    /**
     * @return BudgetAdjustService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 默认申请用户数据
     * @param $user
     * @return mixed
     */
    public function getDefaultInfo($user){
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $data = [];

        try {
            if (empty($user)) {
                throw new ValidationException("The employee information is null [{$user['id']}]", ErrCode::$VALIDATE_ERROR);
            }

            $arr = $this->getUserMetaFromBi($user['id']);
            if (empty($arr)) {
                throw new ValidationException(static::$t->_("re_staff_info_id_not_exist"), ErrCode::$VALIDATE_ERROR);
            }

            // 申请编号
            GlobalEnums::init();
            $now_date = date("Ymd");
            $data['adjust_no'] = 'YSTZ0' . GlobalEnums::$sys_default_currency . static::getNo($now_date);
            $data['apply_date'] = date("Y-m-d");
            // 币种
            $data['currency'] = GlobalEnums::$sys_default_currency;
            $data['currency_text'] = GlobalEnums::$sys_default_currency_symbol;

            $budget_department_id         = (new DepartmentModel())->getFirstDepartmentIdByDepartmentId($arr['apply_department']);
            $data['budget_department_id'] = !empty($budget_department_id) ? $budget_department_id : $arr['apply_department'];
            $data = array_merge($data, $arr);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $data = [];
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-get-default-data-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 新增预算调整单
     * @param $data
     * @return array
     */
    public function saveOne($data,$user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            //校验当前 待审批 预算导入单子
            $main_model = BudgetSourceMainModel::findFirst([
                'apply_status = :status: and year_at = :year_at: and source_type = :source_type:',
                'bind' => ['status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_PENDING, 'year_at' => $data['adjust_detail'][0]['budget_year'], 'source_type' => self::BUDGET_SOURCE_TYPE_2],
            ]);

            if (!empty($main_model)) {
                throw new ValidationException(static::$t->_('budget_import_order_is_pending'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
            }
            $data = $this->handleData($data);
            $year = $data['adjust_detail'][0]['budget_year'];
            $start = $year . '-01';
            $end = $year . '-12';

            $object_codes   = array_column($data['adjust_detail'], 'object_code');
            $department_ids = array_column($data['adjust_detail'], 'budget_department_id');

            $department_budget_list = BudgetObjectDepartmentAmount::find([
                'conditions' => 'month >= :start: AND month <= :end: AND is_delete = 0 AND department_id in({department_id:array}) AND object_code in({object_code:array})',
                'bind'       => ['start' => $start, 'end' => $end, 'department_id' => $department_ids, 'object_code' => $object_codes],
                'columns'    => ['id', 'department_id', 'object_code', 'organization_type', 'amount_type', 'month', 'amount', 'amount_left']
            ])->toArray();

            foreach ($department_budget_list as $amount_detail) {
                $k                                              = "{$amount_detail['department_id']}_{$amount_detail['object_code']}_{$amount_detail['organization_type']}_{$amount_detail['amount_type']}";
                $format_all_amount[$k][$amount_detail['month']] = $amount_detail;

            }
            unset($department_budget_list);
            $column = [
                'jan_amount' => $year . '-01',
                'feb_amount' => $year . '-02',
                'mar_amount' => $year . '-03',
                'apr_amount' => $year . '-04',
                'may_amount' => $year . '-05',
                'jun_amount' => $year . '-06',
                'jul_amount' => $year . '-07',
                'aug_amount' => $year . '-08',
                'sep_amount' => $year . '-09',
                'oct_amount' => $year . '-10',
                'nov_amount' => $year . '-11',
                'dec_amount' => $year . '-12',
            ];

            $model = new BudgetAdjustModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('预算调整申请单创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            if (!empty($data['attachments'])) {
                $attachments_m = [];
                foreach ($data['attachments'] as $attachment) {
                    $tmp_m                    = [];
                    $tmp_m['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_ADD_MAIN;
                    $tmp_m['oss_bucket_key']  = $model->id;
                    $tmp_m['bucket_name']     = $attachment['bucket_name'];
                    $tmp_m['object_key']      = $attachment['object_key'];
                    $tmp_m['file_name']       = $attachment['file_name'];
                    $attachments_m[]          = $tmp_m;
                }
                $attach_bool = (new AttachModel())->batch_insert($attachments_m);
                if ($attach_bool === false) {
                    throw new BusinessException('预算调整申请单-主数据附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                }
            }
            $deduction_amount      = [];
            $department_amount_log = [];
            foreach ($data['adjust_detail'] as $detail) {
                $t_model = new BudgetAdjustDetailModel();
                $detail['budget_adjust_id'] = $model->id;
                $detail_bool = $t_model->i_create($detail);

                if ($detail_bool === false) {
                    $messages = $t_model->getMessages();
                    throw new BusinessException('预算调整申请单-产品创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                }
                if (!empty($detail['attachments'])) {
                    $attachments = [];
                    foreach ($detail['attachments'] as $attachment) {
                        $tmp = [];
                        $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_ADD;
                        $tmp['oss_bucket_key'] = $t_model->id;
                        $tmp['bucket_name'] = $attachment['bucket_name'];
                        $tmp['object_key'] = $attachment['object_key'];
                        $tmp['file_name'] = $attachment['file_name'];
                        $attachments[] = $tmp;
                    }
                    $attach_bool = (new AttachModel())->batch_insert($attachments);
                    if ($attach_bool === false) {
                        throw new BusinessException('预算调整申请单-附件创建失败', ErrCode::$CONTRACT_CREATE_ERROR);
                    }
                }
                //提交申请单负向金额提前从预算表扣减
                $obj_code    = $detail['object_code'];
                $dep_id      = $detail['budget_department_id'];
                $org         = $detail['organization_type'];
                $amount_type = $detail['amount_type'];
                $dk          = "{$dep_id}_{$obj_code}_{$org}_{$amount_type}";
                foreach ($column as $month => $month_num) {
                    if (isset($detail[$month]) && $detail[$month] < 0) {

                        if (isset($format_all_amount[$dk])) {

                            $department_amount_obj = BudgetObjectDepartmentAmount::findFirst(
                                [
                                    'conditions' => 'id=?0',
                                    'bind'       => [$format_all_amount[$dk][$month_num]['id']]
                                ]
                            );

                            $temp                       = [];
                            $temp['department_id']      = $department_amount_obj->department_id;
                            $temp['object_code']        = $department_amount_obj->object_code;
                            $temp['organization_type']  = $department_amount_obj->organization_type;
                            $temp['amount_type']        = $department_amount_obj->amount_type;
                            $temp['month']              = $department_amount_obj->month;
                            $temp['season']             = $department_amount_obj->season;
                            $temp['budget_adjust_id']   = $model->id;
                            $temp['type']               = self::IS_DEDUCTION;
                            $temp['before_amount']      = $department_amount_obj->amount;
                            $temp['before_amount_left'] = $department_amount_obj->amount_left;
                            $temp['amount']             = $detail[$month];
                            $temp['operate_id']         = $user['id'];
                            $temp['created_at']         = date('Y-m-d H:i:s');
                            $temp['updated_at']         = date('Y-m-d H:i:s');
                            $department_amount_log[]    = $temp;

                            $bool = $department_amount_obj->save(['amount' => ($department_amount_obj->amount + $detail[$month]), 'amount_left' => ($department_amount_obj->amount_left + $detail[$month])]);

                            if ($bool === false) {
                                $messages = $department_amount_obj->getMessages();
                                throw new BusinessException('预算调整申请单-预扣减失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                            }


                        }
                        $deduction_amount[$dk][$month_num] = $detail[$month];
                    }
                }
            }
            if (!empty($department_amount_log)) {
                $log_bool = (new BudgetObjectDepartmentAmountLogModel())->batch_insert($department_amount_log);
                $this->logger->info('budget-adjust-create-deduction' . json_encode($deduction_amount));
                if ($log_bool === false) {
                    throw new BusinessException(static::$t->_('预算调整申请单-预扣减记录失败'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
                }
            }
                $flow_bool = (new BudgetAdjustFlowService)->createRequest($model, $user);
                if ($flow_bool === false) {
                    throw new BusinessException(static::$t->_('loan_create_work_flow_failed'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
                }


            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            if (in_array($e->getCode(), [ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY, ErrCode::$BUDGET_OVERAMOUNT_MONTH])) {
                $code = ErrCode::$SUCCESS;
                $result = [
                    'message' => $e->getMessage(),
                    'can_apply' => $e->getCode() == ErrCode::$BUDGET_OVERAMOUNT_QUARTERLY ? 0 : 1
                ];
            } else {
                $code = $e->getCode();
                $message = $e->getMessage();
            }
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
     * @param $id
     * @param $user
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     */
    public function createRequest($item, $user)
    {
        $data['id'] = $item->id;
        $data['name'] = $item->no . '审批申请';
        $data['biz_type'] = Enums::WF_REIMBURSEMENT_TYPE;

        //报销用的是申请人id，申请人名字
        $user['id'] = $item->apply_id;
        $user['name'] = $item->apply_name;


        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($item, $user));
    }

    /**
     * @param $data
     * @return array|string
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            throw new BusinessException('data empty', ErrCode::$VALIDATE_ERROR);
        }
        if (!isset($data['id'])) {
            $data['cost_store_id'] = $data['apply_store_id'];
            $data['status'] = BudgetAdjustEnums::BUDGET_ADJUST_STATUS_PENDING;
            $data['apply_date'] = date('Y-m-d');
            $data['updated_at'] = $data['created_at'] = date('Y-m-d H:i:s');
        }
        if (-1 == $data['cost_store_id']) {
            $data['cost_store_name'] = 'Header Office';
        } else {
            $storeInfo = (new StoreService())->getSysStoreInfo($data['cost_store_id']);
            $data['cost_store_name'] = isset($storeInfo[0]['name']) ? $storeInfo[0]['name'] : '';
        }
        $data['cost_store_type'] = -1 == $data['cost_store_id'] ? 1 : 2;

        // 取科目名称
        $budgetCodes = array_column($data['adjust_detail'], 'object_code');
        $budgetList = BudgetObject::find([
            'conditions' => 'level_code in ("'.implode('","',$budgetCodes).'")'
        ])->toArray();
        $budgetCodeList = array_column($budgetList,'name_cn','level_code');

        // 取部门名称
        $dep_ids = array_values(array_unique(array_filter(array_column($data['adjust_detail'], 'budget_department_id'))));
        $dep_ids = array_merge($dep_ids,[$data['cost_company_id'],$data['apply_department_id']]);
        $sysDepList = SysDepartmentModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind' => ['ids' => $dep_ids]
        ])->toArray();
        $sysDepList = array_column($sysDepList,'name','id');
        $data['apply_department_name'] = !empty($data['apply_department_id']) && isset($sysDepList[$data['apply_department_id']]) ? $sysDepList[$data['apply_department_id']] : '';
        $data['cost_company_name'] = !empty($data['cost_company_id']) && isset($sysDepList[$data['cost_company_id']]) ? $sysDepList[$data['cost_company_id']] : '';

        $data['currency'] = $data['adjust_detail'][0]['currency'] ?? 0;
        $data['currency_text'] = !empty($data['currency']) ? static::$t->_(GlobalEnums::$currency_item[$data['currency']]) : '';

        // 获取币种与系统默认币种的汇率
        $exchange_rate = EnumsService::getInstance()->getCurrencyExchangeRate((int)$data['currency']);
        $data['exchange_rate'] = $exchange_rate ? $exchange_rate : 1;

        $adjust_types = $adjust_deps = [];
        $data['total_amount'] = 0;
        foreach ($data['adjust_detail'] as $k => &$v) {
            // 金额按照*1000存储
            if (!isset($data['id'])) {
                $v['jan_amount'] = bcmul(floatval($v['jan_amount']), 1000);
                $v['feb_amount'] = bcmul(floatval($v['feb_amount']), 1000);
                $v['mar_amount'] = bcmul(floatval($v['mar_amount']), 1000);
                $v['apr_amount'] = bcmul(floatval($v['apr_amount']), 1000);
                $v['may_amount'] = bcmul(floatval($v['may_amount']), 1000);
                $v['jun_amount'] = bcmul(floatval($v['jun_amount']), 1000);
                $v['jul_amount'] = bcmul(floatval($v['jul_amount']), 1000);
                $v['aug_amount'] = bcmul(floatval($v['aug_amount']), 1000);
                $v['sep_amount'] = bcmul(floatval($v['sep_amount']), 1000);
                $v['oct_amount'] = bcmul(floatval($v['oct_amount']), 1000);
                $v['nov_amount'] = bcmul(floatval($v['nov_amount']), 1000);
                $v['dec_amount'] = bcmul(floatval($v['dec_amount']), 1000);
                $v['total_amount'] = bcmul((string)($v['total_amount']), 1000);
                // 默认为有效
                $v['status'] = BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_VALID;
            } else {
                $v['jan_amount'] = bcdiv(floatval($v['jan_amount']), 1000, 2);
                $v['feb_amount'] = bcdiv(floatval($v['feb_amount']), 1000, 2);
                $v['mar_amount'] = bcdiv(floatval($v['mar_amount']), 1000, 2);
                $v['apr_amount'] = bcdiv(floatval($v['apr_amount']), 1000, 2);
                $v['may_amount'] = bcdiv(floatval($v['may_amount']), 1000, 2);
                $v['jun_amount'] = bcdiv(floatval($v['jun_amount']), 1000, 2);
                $v['jul_amount'] = bcdiv(floatval($v['jul_amount']), 1000, 2);
                $v['aug_amount'] = bcdiv(floatval($v['aug_amount']), 1000, 2);
                $v['sep_amount'] = bcdiv(floatval($v['sep_amount']), 1000, 2);
                $v['oct_amount'] = bcdiv(floatval($v['oct_amount']), 1000, 2);
                $v['nov_amount'] = bcdiv(floatval($v['nov_amount']), 1000, 2);
                $v['dec_amount'] = bcdiv(floatval($v['dec_amount']), 1000, 2);
                $v['total_amount'] = bcdiv((string)($v['total_amount']), 1000, 2);
            }
            // level_code
            $v['object_name'] = $budgetCodeList[$v['object_code']];
            $v['updated_at'] = $v['created_at'] = date('Y-m-d H:i:s');
            // organization_type名称
            $organization_type = $v['organization_type'] == 1 ? 2 : 1;
            $v['organization_type_name'] = static::$t->_(Enums::$payment_cost_store_type[$organization_type]);
            // amount_type名称
            $v['amount_type_name'] = !empty(BudgetAdjustEnums::$adjust_is_share_key[$v['amount_type']]) ? static::$t->_(BudgetAdjustEnums::$adjust_is_share_key[$v['amount_type']]) : '';
            // budget_adjust_type名称
            $v['budget_adjust_type_name'] = static::$t->_(BudgetAdjustEnums::$budget_adjust_import_method[$v['budget_adjust_type']]);

            $v['exchange_rate'] = $data['exchange_rate'];
            $data['total_amount'] += $v['total_amount'];
            unset($v['amount']);

            $adjust_types[] = $v['budget_adjust_type'];
            $year[] = $v['budget_year'];
            if (!isset($v['status']) || empty($v['status'])) {
                $adjust_deps[] = $v['budget_department_id'];
            }

            $v['budget_department_name'] = !empty($v['budget_department_id']) && isset($sysDepList[$v['budget_department_id']]) ? $sysDepList[$v['budget_department_id']] : '';
        }
        $data['budget_adjust_type'] = $adjust_types[0] ?? 0;
        $data['budget_year'] = $year[0] ?? 0;
        $data['budget_department_id'] = 0;
        // 预算一级部门
        $budget_department_id = array_filter($adjust_deps)[0] ?? 0;
        if (!empty($budget_department_id)) {
            $data['budget_department_id'] = (new DepartmentModel())->getFirstDepartmentIdByDepartmentId($budget_department_id);
        }

        return $data;
    }

    /**
     * 是否存在非末级的科目ID
     *
     * @param $data
     * @param $budgetIds
     *
     */
    protected function isHasNotEndBudgetIds($budgetIds)
    {
        $budgetTrees = $this->productList();
        $budgetService = new BudgetService();
        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);
        return array_diff($budgetIds, $endBudgetIds) ? 1 : 0;
    }

    /**
     * 预算调整申请单 根据人的部门返回
     * 科目与商品树状关联图
     *
     * @return array
     */
    public function productList()
    {
        $budgetService = new BudgetService();
        $name = $this->get_lang_column(static::$language);
        $budgetTrees = BudgetObject::find([
            'condition' => 'is_delete=0',
            'columns' => "id,level_code,{$name} as name,template_type"
        ])->toArray();
        $endBudgetIds = $budgetService->endBudgetIds($budgetTrees);
        if ($endBudgetIds) {
            $budgets = BudgetObject::find([
                'conditions' => 'is_delete=0 and id in ({ids:array}) ',
                'bind' => ['ids' => $endBudgetIds]
            ])->toArray();
            $products = $budgetService->purchaseProducts(array_column($budgets, 'level_code'));
            $budgetTrees = $this->bindProduct($budgetTrees, $products);
        }

        return $budgetTrees;
    }

    private function bindProduct($budgetTrees, $products)
    {
        foreach ($budgetTrees as $k => $budgetTree) {
            if (isset($budgetTree['list'])) {
                $budgetTrees[$k]['list'] = $this->bindProduct($budgetTree['list'], $products);
            } else {
                if (isset($products[$budgetTree['level_code']])) {

                    $budgetTrees[$k]['products'] = isset($products[$budgetTree['level_code']]) ? $products[$budgetTree['level_code']] : [];
                }
            }
        }
        return $budgetTrees;
    }

    /**
     * @param $condition
     * @param $uid
     * @param int $type
     * @param bool $if_download
     * @return array
     */
    public function getList($condition, $uid = null, $type = 0, $if_download = false)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['per_page']) ? 20 : $condition['per_page'];
        $page_num = empty($condition['current_page']) ? 1 : $condition['current_page'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['b' => BudgetAdjustModel::class]);
            $builder->leftjoin(BudgetAdjustDetailModel::class, 'bd.budget_adjust_id=b.id', 'bd');
            $builder = $this->getCondition($builder, $condition, $type);

            $count = (int) $builder->columns('COUNT(DISTINCT b.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $column_str = 'b.id,b.adjust_no,b.apply_id,b.apply_name,b.apply_department_id,b.apply_department_name,b.apply_date,b.cost_store_type,b.status,group_concat(distinct bd.object_code) object_code_str';

                if ($type == self::LIST_TYPE_QUERY) {
                    $column_str = 'b.id,b.adjust_no,b.apply_id,b.apply_name,b.apply_department_id,b.apply_department_name,b.apply_date,b.cost_store_type,b.status,bd.object_code object_code_str,bd.reason reason_str,bd.import_method,bd.before_amount before_adjust_amount,bd.after_amount after_adjust_amount,bd.budget_amount,bd.currency';
                } else if ($type == self::LIST_TYPE_FYR) {
                    $column_str .= ',reply.id ask_id';

                } else if ($type == self::LIST_TYPE_AUDIT && isset($condition['process_status']) && $condition['process_status'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                    // 审核模块的已处理列表, 展示处理时间
                    $column_str .= ',log.audit_at';
                }

                $builder->columns($column_str);

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('b.id desc');
                }

                if ($type != self::LIST_TYPE_QUERY) {
                    $builder->groupBy('b.id');
                }

                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items, $uid);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('budget-adjust-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type = 0)
    {
        $no = $condition['apply_no'] ?? '';
        $status = $condition['status'] ?? 0;

        // 部门搜索
        $apply_department_id = $condition['apply_department_id'] ?? 0;
        $import_method = $condition['import_method'] ?? 0;
        $apply_date_start = $condition['apply_start_date'] ?? '';
        $apply_date_end = $condition['apply_end_date'] ?? '';

        $apply_id_name = $condition['apply_id_name'] ?? '';
        $object_code = $condition['object_code'] ?? 0;//预算科目
        $cost_store_type = $condition['cost_store_type'] ?? 0; //费用所属网点 1总部2网点

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['process_status'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        //审核列表
        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_BUDGET_ADJUST_TYPE], $condition['uid'], 'b');

        } else if ($type == self::LIST_TYPE_APPLY) {
            // 如果uid不为NULL，数据或者下载
            if (!empty($condition['uid'])) {
                $builder->andWhere('b.apply_id = :uid:', ['uid' => $condition['uid']]);
            }

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表
            $biz_table_info = ['table_alias' => 'b'];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_BUDGET_ADJUST_TYPE], $condition['uid'], $biz_table_info);

        }

        if (!empty($no)) {
            $builder->andWhere('b.adjust_no = :no:', ['no' => $no]);
        }

        //工号或者姓名
        if (!empty($apply_id_name)) {
            $builder->andWhere('b.apply_id = :apply_id: or b.apply_name=:apply_id:', ['apply_id' => $apply_id_name]);
        }

        if (!empty($status)) {
            $builder->andWhere('b.status = :status:', ['status' => $status]);
        }

        if (!empty($apply_department_id)) {
            $builder->andWhere('b.apply_department_id = :apply_department_id:', ['apply_department_id' => $apply_department_id]);
        }

        if (!empty($import_method)) {
            $builder->andWhere('bd.import_method = :import_method:', ['import_method' => $import_method]);
        }

        if (!empty($apply_date_start)) {
            $builder->andWhere('b.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }

        if (!empty($apply_date_end)) {
            $builder->andWhere('b.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }

        if (!empty($object_code)) {
            $list = BudgetAdjustDetailModel::find([
                'columns' => 'budget_adjust_id',
                'conditions' => 'object_code = ?1',
                'bind'       => [
                    1 => $object_code,
                ]
            ])->toArray();

            if(!empty($list)) {
                $budget_adjust_ids = array_unique(array_column($list,'budget_adjust_id'));
                $builder->inWhere('b.id',$budget_adjust_ids);
            } else {
                $builder->inWhere('b.id',[-1]);
            }
        }

        if (!empty($cost_store_type)) {
            $builder->andWhere('b.cost_store_type = :cost_store_type:', ['cost_store_type' => $cost_store_type]);
        }

        return $builder;
    }

    /**
     * @param $items
     * @param $uid
     * @return array
     */
    private function handleItems($items,$uid=null)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        // 根据科目ID找科目名称
        $object_code_arr = array_column($items,'object_code_str');

        $code_ids = [];
        foreach ($object_code_arr as $code_item) {
            $code_ids = array_merge($code_ids,explode(',',$code_item));
        }
        $code_ids = array_values(array_unique($code_ids));
        $budget_object_list = BudgetObject::find([
            'columns' => 'level_code,name_th,name_en,name_cn',
            'conditions' => 'level_code in({level_code:array})',
            'bind'       => [
                'level_code' => $code_ids,
            ]
        ])->toArray();
        $budget_object_list = array_column($budget_object_list,null,'level_code');
        foreach ($items as &$item) {
            // 币种
            $item['currency_text'] = isset($item['currency']) ? static::$t->_(GlobalEnums::$currency_item[$item['currency']]) : '';
            // 组织机构类型
            $item['cost_store_type_text'] = isset($item['cost_store_type']) ? static::$t->_(Enums::$payment_cost_store_type[$item['cost_store_type']]) : '';
            $item['budget_amount'] = isset($item['budget_amount']) ? bcdiv($item['budget_amount'], 1000, 2) : 0;
            $item['before_adjust_amount'] = isset($item['before_adjust_amount']) ? bcdiv($item['before_adjust_amount'], 1000, 2) : 0;
            $item['after_adjust_amount'] = isset($item['after_adjust_amount']) ? bcdiv($item['after_adjust_amount'], 1000, 2) : 0;
            $item['status_text'] = isset($item['status']) ? static::$t->_(BudgetAdjustEnums::$budget_adjust_status[$item['status']]) : '';
            $item['import_method_text'] = isset($item['import_method']) ? static::$t->_(BudgetAdjustEnums::$budget_adjust_import_method[$item['import_method']] ?? '') ?? '' : '';
            // 科目预算名称
            if (!empty($item['object_code_str'])) {
                $tmp = [];
                $object_code_strs = explode(',',$item['object_code_str']);
                foreach ($object_code_strs as $str) {
                    $tmp[] = $budget_object_list[$str]['name_' . strtolower(substr(static::$language, -2))] ?? '';
                }
                $item['object_code_str'] = implode(',',$tmp);
            } else {
                $item['object_code_str'] = '';
            }

        }

        return $items;
    }

    /**
     * @param $id
     * @param $uid
     * @return array
     */
    public function getAuditDetail($id, $uid)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $real_message = '';

        $item = [];
        try {
            $item = $this->getDetail($id, $uid,false);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('budget-adjust-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $item
        ];
    }

    /**
     * @param $id
     * @param null $uid
     * @param boolean $if_download
     * @return array|mixed
     * @throws ValidationException
     * @throws BusinessException
     */
    public function getDetail($id, $uid = null, $if_download = false)
    {
        $item = BudgetAdjustModel::getFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        if (empty($item)) {
            throw new BusinessException('data empty');
        }
        $req = (new BudgetAdjustFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('获取工作流批次失败');
        }

        $detailArr = [];
        $details = $item->getDetails();
        foreach ($details as $detail) {
            $files = $detail->getFile(["columns" => "bucket_name,object_key,file_name"]);
            $temp = $detail->toArray();
            $temp['attachments'] = $files;
            $detailArr[] = $temp;
        }
        $data = $item->toArray();
        //待回复征询ID
        $ask = (new FYRService())->getRequestToByReplyAsk($req,$uid);
        $data['ask_id'] = $ask ? $ask->id:'';
        $data['auth_logs'] = $this->getAuditLogs($req, $item, $if_download);
        $data['adjust_detail'] = $detailArr;

        //判断是否能更改
        $data['can_edit'] = 0;
        //是否为BP审核详情
        $data['can_import'] = $this->getImportAuth($req,$uid);
        $main_attachment = AttachModel::find(
            [
                "columns" => "bucket_name,object_key,file_name",
                'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key= :oss_bucket_key:',
                'bind' => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_ADD_MAIN, 'oss_bucket_key' => $id],
                'order' => 'id desc'
            ]
        );
        // excel导入的文件
        $attachment = AttachModel::findFirst(
            [
                "columns" => "bucket_name,object_key,file_name",
                'conditions' => 'oss_bucket_type = :oss_bucket_type: and oss_bucket_key= :oss_bucket_key:',
                'bind' => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_EXCEL, 'oss_bucket_key' => $id],
                'order' => 'id desc'
            ]
        );
        $data['excel_attachment'] = isset($attachment->bucket_name) ? $attachment : null;
        $data['attachments'] =$main_attachment;
        $data = $this->handleData($data);

        return $data;
    }

    private function getImportAuth($req,$uid){
        $field = (new BudgetAdjustFlowService())->getCanEditFieldByReq($req,$uid);
        if (empty($field)) {
            return 0;
        }
        return $field['budget_adjust_import'] == 'on' ? 1 : 0;
    }

    private function getAuditLogs($req, $item, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }
        //下载要正序
        if($if_download){
            $auth_logs = array_reverse($auth_logs);
        }
        return $auth_logs;
    }

    public function cancel($id, $note, $user){
        return (new BudgetAdjustFlowService())->cancel($id, $note, $user);
    }

    public function approve($id,$note, $user,$update_data=[],$adjust_detail = []){
        return (new BudgetAdjustFlowService())->approve($id,$note, $user,$update_data,$adjust_detail);
    }

    public function reject($id,$note, $user,$attachments=[]){
        $result = (new BudgetAdjustFlowService())->reject($id,$note, $user);
        if (ErrCode::$SUCCESS != $result['code']) {
            return $result;
        }

        if (!empty($attachments)) {
            $attachmentData = [];
            $item = BudgetAdjustModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            foreach ($attachments as $attachment) {
                $tmp = [];
                $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_REJECT;
                $tmp['oss_bucket_key'] = isset($item->id) ? $item->id : '';
                $tmp['bucket_name'] = $attachment['bucket_name'];
                $tmp['object_key'] = $attachment['object_key'];
                $tmp['file_name'] = $attachment['file_name'];
                $attachmentData[] = $tmp;
            }
            $attach_bool = (new AttachModel())->batch_insert($attachmentData);
            if ($attach_bool === false) {
                return ['code' => ErrCode::$BUSINESS_ERROR,'message' => static::$t->_('file_attachment_update_failed')];
            }
        }

        return $result;
    }

    public function _getWkReq($id)
    {
        static $workRequest = [];
        if(empty($workRequest[$id])){
            $workRequest[$id] = WorkflowRequestModel::findFirst(
                [
                    'conditions' => 'biz_type = :type: and biz_value= :lid:',
                    'bind' => ['type' => Enums::WF_BUDGET_ADJUST_TYPE, 'lid' => $id]
                ]
            );
        }
        return $workRequest[$id];
    }

    /**
     * 导出列表
     *
     * @param $condition
     * @param $uid
     * @return array
     * @throws BusinessException
     */
    public function export($condition,$uid)
    {
        ini_set('memory_limit', '1024M');

        $res = BudgetAdjustService::getInstance()->getList($condition, $uid, BaseService::LIST_TYPE_QUERY,true);
        if ($res['code'] != ErrCode::$SUCCESS) {
            return $res;
        }
        $itemdata = $res['data']['items'];
        $new_data = [];
        foreach ($itemdata as $key => $val) {
            $new_data[$key][] = $val['adjust_no'];
            $new_data[$key][] = $val['apply_id'];
            $new_data[$key][] = $val['apply_date'];
            $new_data[$key][] = $val['apply_department_name'];
            $new_data[$key][] = $val['cost_store_type_text'];
            $new_data[$key][] = $val['status_text'];
            $new_data[$key][] = $val['import_method_text'];
            $new_data[$key][] = $val['object_code_str'];
            $new_data[$key][] = $val['budget_amount'];
            $new_data[$key][] = $val['currency_text'];
            $new_data[$key][] = $val['reason_str'];
            $new_data[$key][] = $val['before_adjust_amount'];
            $new_data[$key][] = $val['after_adjust_amount'];
        }
        $file_name = "budget_adjust_" . date("YmdHis");
        $header = [
            static::$t->_('budget_adjust_no'),//申请单号
            static::$t->_('budget_adjust_date'),//申请人工号
            static::$t->_('budget_adjust_apply_date'),//申请时间
            static::$t->_('budget_adjust_department'),//预算部门
            static::$t->_('budget_adjust_store_type'), //组织机构
            static::$t->_('budget_adjust_status'),//申请状态
            static::$t->_('budget_adjust_import_method'),//方式
            static::$t->_('budget_adjust_level_code'),//预算科目
            static::$t->_('budget_adjust_apply_amount'),//申请金额
            static::$t->_('budget_adjust_currency'),//币种
            static::$t->_('budget_adjust_reason'),//事由
            static::$t->_('budget_adjust_before_amount'),//调整前年度金额
            static::$t->_('budget_adjust_after_amount'),//调整后年度金额
        ];

        return $this->exportExcel($header, $new_data, $file_name);
    }

    /**
     * 获得下载的列表，主要是详细
     * @param $condition
     * @return array
     */
    public function getDownloadList($condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $builder = $this->modelsManager->createBuilder();

            $column_str = 'b.id,b.adjust_no,b.apply_id,b.apply_name,b.apply_department_id,b.apply_department_name,b.apply_date,b.cost_store_type,b.status,bd.object_name object_code_str,bd.reason reason_str,bd.import_method,bd.before_amount before_adjust_amount,bd.after_amount after_adjust_amount,bd.budget_amount';
            $builder->columns($column_str);
            $builder->from(['b' => BudgetAdjustModel::class]);
            $builder->leftjoin(BudgetAdjustDetailModel::class, 'bd.budget_adjust_id=b.id', 'bd');
            $builder = $this->getCondition($builder, $condition);
            $data = $builder->getQuery()->execute()->toArray();
            $data = $this->handleItems($data);
        } catch (ValidationException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-get-download-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * @return array
     */
    public function getBpList()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $bpContractJson = EnvModel::getEnvByCode('bp_contract_infomation','');
            $data = json_decode($bpContractJson,true);
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-adjust-list-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取申请创建页 - 基本信息 - 默认数据
     * @param array $user
     * @param int $data_type
     * @return array
     */
    public function getCreatePageBaseInfoDefaultData(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [];

        try {
            // 网点类型
            $data['cost_store_type']    = $this->getEnumsItems($this->getOrganizationTypeItems());

            //网点类型选择总部时使用的总部网点ID和网点名称
            $data['header_office']      = ['id' => Enums::PAYMENT_HEADER_STORE_ID, 'name' => Enums::PAYMENT_HEADER_STORE_NAME];

            //COO下的公司名称和id
            $data['cost_company']       =  $this->getCooCostCompany();

            //公用类型
            $data['amount_type']          = $this->getEnumsItems(BudgetAdjustEnums::$adjust_is_share_key);

            //调整类型
            $data['budget_adjust_type']    = $this->getEnumsItems(BudgetAdjustEnums::$budget_adjust_import_method);

        } catch (ValidationException $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('普通付款申请创建页 - 基本信息 - 默认值获取异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 更新调整明细状态
     * @param array $params
     * @param array $user
     * @param int $data_type
     * @return array
     */
    public function switchDetailStatus($params, $user){
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 日志导出存在性校验
            $detail = BudgetAdjustDetailModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (!isset($detail->id)) {
                $this->logger->warning('调整明细不存在，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('detail not found.');
            }
            // 已处理过
            if ($detail->status == $params['status']) {
                $this->logger->warning('调整明细状态已被处理，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('switch status has been done.');
            }
            $detail->status = $params['status'];
            $flag = $detail->save();
            if ($flag === false) {
                $this->logger->warning('调整明细状态更新失败，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('switch status failed.');
            }
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 校验审核信息
     * @param array $params
     * @param array $user
     * @return array
     */
    public function checkAuditInfo($params, $user){
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            // 日志导出存在性校验
            $detail = BudgetAdjustDetailModel::getFirst([
                'id = :id:',
                'bind' => ['id' => $params['id']],
            ]);
            if (!isset($detail->id)) {
                $this->logger->warning('调整明细不存在，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('detail not found.');
            }
            // 已处理过
            if ($detail->status == $params['status']) {
                $this->logger->warning('调整明细状态已被处理，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('switch status has been done.');
            }
            $detail->status = $params['status'];
            $flag = $detail->save();
            if ($flag === false) {
                $this->logger->warning('调整明细状态更新失败，传参信息：' . json_encode($params, JSON_UNESCAPED_UNICODE));
                throw new BusinessException('switch status failed.');
            }
        } catch (BusinessException $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 校验审核明细信息
     * @param array $params
     * @throws ValidationException
     * @return array
     */
    public function checkAdjustDetail($params, $user, $is_deduction = false)
    {
        ini_set('memory_limit', '1024M');

        // 字段规范性校验
        Validation::validate($params, BudgetAdjustService::$audit_validate_param);
        if (isset($params['adjust_detail'][0]['budget_year']) && $params['adjust_detail'][0]['budget_year'] != BaseService::getCurrentYear()) {
            throw new ValidationException(static::$t->_('budget_adjust_year_not_current_year'), ErrCode::$VALIDATE_ERROR);
        }

        // 重复性校验
        $exist_object_code = $adjust_types = $adjust_deps = $adjust_inout = $first_dep = $year = [];

        $total_amount = 0;
        // 传参日志
        $this->logger->info('check detail params => 确认调整传参数据：' . json_encode($params['adjust_detail'], JSON_UNESCAPED_UNICODE));
        // 提取预算科目名称
        $object_item = (new BudgetService())->getAllObjectList();
        $object_item = $object_item ? array_column($object_item, 'name', 'level_code') : [];
        foreach ($params['adjust_detail'] as $k => &$v) {

            if (isset($v['status']) && $v['status'] == BudgetAdjustEnums::BUDGET_ADJUST_DETAIL_STATUS_INVALID) {
                unset($params['adjust_detail'][$k]);
                continue;
            }
            $pre_only_key = $v['object_code'] . '_' . $v['budget_department_id'] . '_' . $v['organization_type'];
            $only_key = $v['budget_adjust_type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT ? $pre_only_key . '_' . $v['amount_type'] : $pre_only_key;
            $tmp_total_amount = bcadd($v['total_amount'],0,2);
            if (in_array($only_key, $exist_object_code)) {
                throw new ValidationException(static::$t->_('budget_adjust_line_repeat'), ErrCode::$VALIDATE_ERROR);
            }
            // 是否公用格式不能为空【历史数据问题】
            if (empty($v['amount_type'])) {
                throw new ValidationException( $object_item[$v['object_code']].static::$t->_('budget_adjust_amount_type_format_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 部门格式校验
            if (empty($v['budget_department_id'])) {
                throw new ValidationException(static::$t->_('budget_adjust_budget_department_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            if (empty($v['apply_department_id'])) {
                throw new ValidationException(static::$t->_('budget_adjust_apply_department_id_error'), ErrCode::$VALIDATE_ERROR);
            }
            // 预算追加时，每行的预算调整金额汇总不能小于0
            if ($v['budget_adjust_type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INPUT && bccomp($tmp_total_amount,0,2) < 0) {
                throw new ValidationException(static::$t->_('budget_adjust_amount_total_less_than_zero'), ErrCode::$VALIDATE_ERROR);
                // 预算释放时，每行的预算调整金额汇总只能等于=0；
            } elseif ($v['budget_adjust_type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT && bccomp($tmp_total_amount,0,2)) {
                throw new ValidationException(static::$t->_('budget_adjust_amount_total_not_zero'), ErrCode::$VALIDATE_ERROR);
            }
            $total_amount += $tmp_total_amount;
            $exist_object_code[] = $only_key;
            $adjust_types[] = $v['budget_adjust_type'];
            $adjust_inout[] = $tmp_total_amount;
            $year[] = $v['budget_year'];

          }
        // 所有明细都作废，不用校验
        if (empty($exist_object_code)) {
            return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => []];
        }
        // 校验后的日志
        $this->logger->info('check detail params => total_amount：'.$total_amount . ', exist_object_code' .
            json_encode($exist_object_code, JSON_UNESCAPED_UNICODE) . ',adjust_types:'. json_encode($exist_object_code, JSON_UNESCAPED_UNICODE) . ',year:' .
        json_encode($year, JSON_UNESCAPED_UNICODE));
        // 调整类型校验
        $adjust_types = array_values(array_unique(array_filter($adjust_types)));
        if (count($adjust_types) > 1) {
            throw new ValidationException(static::$t->_('budget_adjust_only_one_type'), ErrCode::$VALIDATE_ERROR);
        }
        // 预算转移至少2行
        if ($adjust_types[0] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT && count($params['adjust_detail']) < 2) {
            throw new ValidationException(static::$t->_('budget_adjust_less_than_two_error'), ErrCode::$VALIDATE_ERROR);
        }
        // 预算转移时，每行的预算调整金额汇总无限制，所有行的预算调整金额汇总合计值=0
        if ($adjust_types[0] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT && bccomp($total_amount,0,2)) {
            throw new ValidationException(static::$t->_('budget_adjust_amount_all_total_not_zero'), ErrCode::$VALIDATE_ERROR);
        }
        // 调整金额一致
        if (count(array_unique(array_filter($year))) > 1) {
            throw new ValidationException(static::$t->_('budget_adjust_year_not_match'), ErrCode::$VALIDATE_ERROR);
        }

        // 调整后的金额小于0
        // 部门已有的预算额度
        $all_amount = array();
        $year = $year[0];
        $month_start = $year.'-01';
        $month_end = $year.'-12';
        $departments = array_column($params['adjust_detail'],'budget_department_id');
        $departments = array_values(array_unique(array_filter($departments)));

        $sys_department = SysDepartmentModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind'       => ['ids' => $departments]
        ])->toArray();
        $sys_department = array_column($sys_department, 'name', 'id');
        if (!empty($departments)) {
            $all_amount = BudgetObjectDepartmentAmount::find([
                "conditions" => "department_id in ({departments:array}) and month >= :month_start: and month <= :month_end: and is_delete=0",
                "bind"=>["departments"=>$departments,"month_start" => $month_start,"month_end" => $month_end],
            ])->toArray();
            //是否是bp 校验 需要添加回预扣减金额
            if ($is_deduction) {
                //扣减数据
                $deduction_amount = BudgetObjectDepartmentAmountLogModel::find([
                    "conditions" => "budget_adjust_id =:budget_adjust_id: and type= 1 and is_deleted=0",
                    "bind"       => ['budget_adjust_id' => $params['adjust_detail'][0]['budget_adjust_id']],
                ])->toArray();
                if (!empty($deduction_amount) && !empty($all_amount)) {
                    foreach ($all_amount as &$budget_amount) {
                        foreach ($deduction_amount as $deduction) {
                            $b_k = $budget_amount['department_id'] . '_' . $budget_amount['object_code'] . '_' . $budget_amount['organization_type'] . '_' . $budget_amount['amount_type'] . '_' . $budget_amount['month'];
                            $d_k = $deduction['department_id'] . '_' . $deduction['object_code'] . '_' . $deduction['organization_type'] . '_' . $deduction['amount_type'] . '_' . $deduction['month'];
                            if ($b_k == $d_k) {
                                $budget_amount['amount']      = $budget_amount['amount'] - $deduction['amount'];
                                $budget_amount['amount_left'] = $budget_amount['amount_left'] - $deduction['amount'];

                            }
                        }
                    }
                }

            }

        }

        $unique_keys = $format_all_amount = [];
        $subject =  static::$t->_('budget_adjust_subject');//科目


            foreach ($all_amount as $al) {
                $k = "{$al['department_id']}_{$al['object_code']}_{$al['organization_type']}_{$al['amount_type']}";

                $format_all_amount[$k]['amount_type'] = $al['amount_type'] ?? 0;
                // 修复用户导出预算时给三位小数，展示或判断时给四舍五入保留两位的情况
                $format_all_amount[$k][$al['month']]['occupy_amount'] = 1000 * sprintf('%.3f', ($al['amount'] - $al['amount_left']) / 1000);
                $format_all_amount[$k][$al['month']]['total_amount'] = 1000 * sprintf('%.3f', $al['amount'] / 1000);
                if (in_array($k, $unique_keys)) {
                    continue;
                }
                $unique_keys[] = $k;
            }
            unset($all_amount);

            $this->logger->info('check detail params => format_all_amount:' . json_encode($format_all_amount, JSON_UNESCAPED_UNICODE));
            $column = array(
                'jan_amount' => $year.'-01',
                'feb_amount' => $year.'-02',
                'mar_amount' => $year.'-03',
                'apr_amount' => $year.'-04',
                'may_amount' => $year.'-05',
                'jun_amount' => $year.'-06',
                'jul_amount' => $year.'-07',
                'aug_amount' => $year.'-08',
                'sep_amount' => $year.'-09',
                'oct_amount' => $year.'-10',
                'nov_amount' => $year.'-11',
                'dec_amount' => $year.'-12',
            );
            // 遍历要导入的数据: 基础校验已通过
        foreach ($params['adjust_detail'] as $da) {
            $obj_code               = $da['object_code'];
            $dep_id                 = $da['budget_department_id'];
            $org                    = $da['organization_type'];
            $amount_type            = $da['amount_type'];
            $dk                     = "{$dep_id}_{$obj_code}_{$org}_{$amount_type}";
            $budget_department_name = !empty($dep_id) && isset($sys_department[$dep_id]) ? $sys_department[$dep_id] : '';

            foreach ($column as $k => $item) {
                $tip = $subject . $object_item[$obj_code] . ',' . $item;
                if ($da['budget_adjust_type'] == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INPUT && $da[$k] < 0) {
                    throw new ValidationException($tip . $budget_department_name . static::$t->_('budget_adjust_amount_month_less_than_zero'), ErrCode::$VALIDATE_ERROR);

                }
                if (isset($format_all_amount[$dk]) && isset($format_all_amount[$dk][$item])) {
                    // 总金额小于0
                    if ((string)((string)($da[$k] * 1000) + (string)$format_all_amount[$dk][$item]['total_amount']) < 0) {
                        throw new ValidationException($tip . static::$t->_('budget_adjust_amount_not_enough'), ErrCode::$VALIDATE_ERROR);
                    }
                    // 小于占用金额
                    if ((string)((string)($da[$k] * 1000) + (string)$format_all_amount[$dk][$item]['total_amount']) < (string)$format_all_amount[$dk][$item]['occupy_amount']) {

                        throw new ValidationException($tip . static::$t->_('budget_adjust_amount_is_over_occupy'), ErrCode::$VALIDATE_ERROR);
                    }
                } else if ((string)$da[$k] < 0) {
                    throw new ValidationException($tip . $budget_department_name . static::$t->_('budget_adjust_amount_not_budget'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

            // 转成校验格式
            $excel_data = (new BudgetAdjustFlowService())->transferToExelData($params['adjust_detail']);

            $this->logger->info('check detail params => excel_data:' . json_encode($excel_data, JSON_UNESCAPED_UNICODE));

            // 预算调整
        $import_res = BudgetAdjustSourceDataService::getInstance()->sourceDataCheck($adjust_types[0], $excel_data, true, $user, 1, 0, 0, $year, $is_deduction, $params['adjust_detail'][0]['budget_adjust_id'] ?? 0);

            $this->logger->info('check detail params => $import_res:' . json_encode($import_res, JSON_UNESCAPED_UNICODE));

            if ($import_res['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($import_res['message'], ErrCode::$VALIDATE_ERROR);
            }
            if (isset($import_res['data']['error_num']) && $import_res['data']['error_num'] > 0) {
                $error_str = explode('｜',$import_res['data']['error'])[0];
                throw new ValidationException($error_str, ErrCode::$VALIDATE_ERROR);
            }

        return ['code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => []];
    }

    /**
     * 获取费用所属网点类型列表
     * @params array $cost_store_type_item
     * @return array
     */
    public function getEnumsItems($cost_store_type_item = [])
    {
        $cost_store_type_item_tmp = [];
        foreach ($cost_store_type_item as $index => $t_key) {
            $cost_store_type_item_tmp[] = [
                'id' => (string)$index,
                'label' => self::$t[$t_key]
            ];
        }

        return $cost_store_type_item_tmp;
    }
    /**
     * 用于返还扣减金额计算和逻辑处理
     * 根据调整单原数据进行返还
     * */
    public function returnBudget($budget_adjust_detail, $uid, $budget_adjust_id)
    {
        $real_message = '';
        $deduction_amount      = [];
        $department_amount_log = $format_all_amount = [];
        $year                  = $budget_adjust_detail[0]['budget_year'];
        $start                 = $year . '-01';
        $end                   = $year . '-12';

        $object_codes   = array_column($budget_adjust_detail, 'object_code');
        $department_ids = array_column($budget_adjust_detail, 'budget_department_id');
        try {
            $department_budget_list = BudgetObjectDepartmentAmount::find([
                'conditions' => 'month >= :start: AND month <= :end: AND is_delete = 0 AND department_id in({department_id:array}) AND object_code in({object_code:array})',
                'bind'       => ['start' => $start, 'end' => $end, 'department_id' => $department_ids, 'object_code' => $object_codes],
                'columns'    => ['id', 'department_id', 'object_code', 'organization_type', 'amount_type', 'month', 'amount', 'amount_left']
            ])->toArray();

            foreach ($department_budget_list as $amount_detail) {
                $k                                              = "{$amount_detail['department_id']}_{$amount_detail['object_code']}_{$amount_detail['organization_type']}_{$amount_detail['amount_type']}";
                $format_all_amount[$k][$amount_detail['month']] = $amount_detail;

            }
            unset($department_budget_list);
            $column = [
                'jan_amount' => $year . '-01',
                'feb_amount' => $year . '-02',
                'mar_amount' => $year . '-03',
                'apr_amount' => $year . '-04',
                'may_amount' => $year . '-05',
                'jun_amount' => $year . '-06',
                'jul_amount' => $year . '-07',
                'aug_amount' => $year . '-08',
                'sep_amount' => $year . '-09',
                'oct_amount' => $year . '-10',
                'nov_amount' => $year . '-11',
                'dec_amount' => $year . '-12',
            ];

            //提交申请单详情表里负向金额返还到预算表
            foreach ($budget_adjust_detail as $detail) {
                $obj_code    = $detail['object_code'];
                $dep_id      = $detail['budget_department_id'];
                $org         = $detail['organization_type'];
                $amount_type = $detail['amount_type'];
                $dk          = "{$dep_id}_{$obj_code}_{$org}_{$amount_type}";
                foreach ($column as $month => $month_num) {
                    if (isset($detail[$month]) && $detail[$month] < 0) {

                        if (isset($format_all_amount[$dk])) {

                            $department_amount_obj = BudgetObjectDepartmentAmount::findFirst(
                                [
                                    'conditions' => 'id=?0',
                                    'bind'       => [$format_all_amount[$dk][$month_num]['id']]
                                ]
                            );

                            $temp                       = [];
                            $temp['department_id']      = $department_amount_obj->department_id;
                            $temp['object_code']        = $department_amount_obj->object_code;
                            $temp['organization_type']  = $department_amount_obj->organization_type;
                            $temp['amount_type']        = $department_amount_obj->amount_type;
                            $temp['month']              = $department_amount_obj->month;
                            $temp['season']             = $department_amount_obj->season;
                            $temp['budget_adjust_id']   = $budget_adjust_id;
                            $temp['type']               = self::IS_RE_BACK;
                            $temp['before_amount']      = $department_amount_obj->amount;
                            $temp['before_amount_left'] = $department_amount_obj->amount_left;
                            $temp['amount']             = (0 - $detail[$month]);
                            $temp['operate_id']         = $uid;
                            $temp['created_at']         = date('Y-m-d H:i:s');
                            $temp['updated_at']         = date('Y-m-d H:i:s');
                            $department_amount_log[]    = $temp;

                            $bool = $department_amount_obj->save(['amount' => ($department_amount_obj->amount - $detail[$month]), 'amount_left' => ($department_amount_obj->amount_left - $detail[$month])]);

                            if ($bool === false) {
                                $messages = $department_amount_obj->getMessages();
                                throw new BusinessException('预算调整撤回或者驳回-返还失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
                            }


                        }
                        $deduction_amount[$dk][$month_num] = $detail[$month];
                    }
                }
            }
            if (!empty($department_amount_log)) {
                $log_bool = (new BudgetObjectDepartmentAmountLogModel())->batch_insert($department_amount_log);
                $this->logger->info('budget-adjust-return-budget' . json_encode($deduction_amount));
                if ($log_bool === false) {
                    throw new BusinessException(static::$t->_('预算调整申请单-预扣减记录失败'), ErrCode::$LOAN_CREATE_WORK_FLOW_ERROR);
                }

            }
        } catch (BusinessException $e) {
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if ($real_message) {
            $this->logger->info('budget-adjust-return-fail-budget:' . $real_message);
            return false;
        }
        return true;

    }
}
