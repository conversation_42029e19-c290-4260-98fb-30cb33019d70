<?php
namespace App\Modules\Budget\Services;

use App\Library\Enums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BudgetWithholdingEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\BudgetWithholdingDetailModel;
use App\Models\oa\BudgetWithholdingModel;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\OrdinaryPayment\Services\SapService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\Third\Services\ByWorkflowService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Repository\oa\BudgetObjectRepository;
use App\Repository\oa\LedgerAccountRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;
use Exception;

/**
 * 预算管理-费用预提、预提审核
 * Class BudgetWithholdingService
 * @package App\Modules\Budget\Services
 */
class BudgetWithholdingService extends BaseService
{
    const DOWNLOAD_LIMIT = 1000;     //导出限制条数
    const IMPORT_DETAIL_LIMIT = 2000;//费用明细限制条数
    const IMPORT_DETAIL_PROVISION_AMOUNT_RULE = '/^([1-9]\d{0,9})(\.\d{1,2})?$|^0?\.\d{1,2}$/';
    const LIST_TYPE_WAIT_HANDLE = 1; //待处理
    const LIST_TYPE_HAD_HANDLE = 2;  //已处理
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //列表非必需
    public static $not_must_params = [
        'status',
        'use_status',
        'cost_department_id',
        'cost_store_type',
        'budget_id',
        'apply_id',
        'cost_company_id',
        'pageSize',
        'pageNum'
    ];

    /**
     * 费用预提 - 查询
     * @var array
     */
    public static $validate_list = [
        'no'                     => 'StrLenGeLe:0,20',
        'status'                 => 'Arr',
        'status[*]'              => 'IntGt:0',
        'use_status'             => 'IntIn:' . BudgetWithholdingEnums::USE_STATUS_VALIDATE,
        'cost_department_id'     => 'IntGt:0',
        'cost_store_type'        => 'IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02,
        'ownership_period_start' => 'StrLenGeLe:0,7',
        'ownership_period_end'   => 'StrLenGeLe:0,7',
        'budget_id'              => 'IntGt:0',
        'apply_id'               => 'IntGt:0',
        'cost_company_id'        => 'Arr',
        'cost_company_id[*]'     => 'IntGt:0',
        'pageSize'               => 'IntGt:0',
        'pageNum'                => 'IntGt:0',
    ];

    /**
     * 费用预提-明细行
     * @var array
     */
    public static $validate_detail_add = [
        'cost_company_id' => 'Required|IntGt:0',
    ];

    /**
     * 费用预提 - 新增
     * @var array
     */
    public static $validate_add = [
        'no'                         => 'Required|StrLenGeLe:1,20',
        'apply_id'                   => 'Required|IntGt:0',
        'apply_name'                 => 'Required|StrLenGeLe:1,255',
        'apply_date'                 => 'Required|Date',
        'cost_company_id'            => 'Required|IntGt:0',
        'cost_company_name'          => 'Required|StrLenGeLe:1,50',
        'cost_department_id'         => 'Required|IntGt:0',
        'cost_department_name'       => 'Required|StrLenGeLe:1,50',
        'cost_store_type'            => 'Required|IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02,
        'is_split'                   => 'IfIntEq:cost_store_type,' . Enums::PAYMENT_COST_STORE_TYPE_02 . '|Required|IntIn:' . BudgetWithholdingEnums::IS_SPLIT_VALIDATE,
        'ownership_period'           => 'Required|StrLen:7',
        'budget_id'                  => 'Required|IntGt:0',
        'remark'                     => 'Required|StrLenGeLe:1,1000',
        'provision_amount'           => 'Required|FloatGt:0',
        'attachments'                => 'ArrLenGeLe:0,20',
        'attachments[*]'             => 'Obj',
        'attachments[*].bucket_name' => 'StrLenGeLe:1,63',
        'attachments[*].object_key'  => 'StrLenGeLe:1,100',
        'attachments[*].file_name'   => 'StrLenGeLe:1,200',
    ];


    /**
     * ID
     * @var array
     */
    public static $validate_id = [
        'id' => 'Required|IntGt:0'
    ];

    /**
     * 费用预提 - 撤回
     * @var array
     */
    public static $validate_cancel = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,500',
    ];

    /**
     * 费用预提 - 关闭
     * @var array
     */
    public static $validate_close = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,500',
    ];

    /**
     * 预提审核 - 通过
     * @var array
     */
    public static $validate_pass = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:0,1000',
    ];

    /**
     * 预提审核 - 驳回
     * @var array
     */
    public static $validate_reject = [
        'id'     => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,1000',
    ];

    /**
     * 费用预提-默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //预提状态、使用状态、费用是否拆分到网点、费用总部/网点
            $enums = [
                'status'          => BudgetWithholdingEnums::$status,
                'use_status'      => BudgetWithholdingEnums::$use_status,
                'is_split'        => BudgetWithholdingEnums::$is_split,
                'cost_store_type' => Enums::$payment_cost_store_type,
            ];
            foreach ($enums as $key => $item) {
                foreach ($item as $k => $v) {
                    $data[$key][] = [
                        'value' => (string)$k,
                        'label' => static::$t->_($v),
                    ];
                }
            }
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
            //SAP的费用所属公司
            $data['sap_company_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        } catch (Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取费用预提默认配置项异常信息: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 费用预提-创建-默认配置项
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function getAddDefault(array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $data['no']         = static::genSerialNo(BudgetWithholdingEnums::BUDGET_WITHHOLDING_NO_PREFIX, RedisKey::BUDGET_WITHHOLDING_ADD_COUNTER, 4);
            $data['apply_id']   = $user['id'];
            $data['apply_name'] = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $data['apply_date'] = date('Y-m-d');
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取费用预提-创建-默认配置项异常信息: ' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 费用预提-创建/重新提交-验证
     * @param array $params 请求参数组
     * @return array
     * @throws ValidationException
     */
    public static function validateCreate(array $params)
    {
        $validate_add = self::$validate_add;
        //当费用所属网点/总部为总部或者(费用所属网点/总部为网点并且费用是否拆分到网点为无法拆分)
        if (!empty($params['cost_store_type']) && ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 || ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02 && !empty($params['is_split']) && $params['is_split'] == BudgetWithholdingEnums::IS_SPLIT_NO))) {
            $validate_add['provision_amount'] = 'Required|FloatGtLe:0,9999999999.99';
        }

        //当费用所属网点/总部为网点并且费用是否拆分到网点为拆分到网点时显示，显示后必填
        if (!empty($params['cost_store_type']) && $params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02 && !empty($params['is_split']) && $params['is_split'] == BudgetWithholdingEnums::IS_SPLIT_YES)  {
            $validate_add['detail']                     = 'Required|ArrLenGeLe:1,2000|>>>:' . static::$t->_('budget_withholding_import_detail_004');
            $validate_add['detail[*].cost_store_id']    = 'Required|StrLenGeLe:1,10';
            $validate_add['detail[*].cost_store_name']  = 'Required|StrLenGeLe:1,50';
            $validate_add['detail[*].provision_amount'] = 'Required|FloatGtLe:0,9999999999.99';

            //当选择的费用所属公司为属于同步SAP的公司时必填
            $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            if (!empty($params['cost_company_id']) && in_array($params['cost_company_id'], $sap_company_ids)) {
                $validate_add['detail[*].cost_center_code'] = 'Required|StrLenGeLe:1,20|>>>:' . static::$t->_('budget_withholding_import_detail_008');
            }
        }
        return $validate_add;
    }

    /**
     * 费用预提-创建/重新提交-验证
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     * @throws ValidationException
     */
    public function validationOther(array $params, array $user)
    {
        //判断费用明细-网点不可重复
        if (!empty($params['detail'])) {
            $cost_store_ids = array_count_values(array_column($params['detail'], 'cost_store_id'));
            foreach ($cost_store_ids as $value) {
                if ($value > 1) {
                    throw new ValidationException(static::$t->_('budget_withholding_cost_store_id_error'), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        //判断费用所属公司是否属于同步SAP的费用所属公司(purchase_sap_company_ids)，如果属于则判断选择的费用部门的SAP成本中心是否有值，如果无值则提示“费用所属部门未维护成本中心，请联系核算组维护成本中心！”
        $cost_center_code = '';
        $sap_company_ids  = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
        if (isset($params['cost_company_id']) && in_array($params['cost_company_id'], $sap_company_ids)) {
            $pc_code_data     = StoreRentingAddService::getInstance()->getPcCode($params['cost_department_id'], 1);
            $cost_center_code = !empty($pc_code_data['data']) ? $pc_code_data['data']['pc_code'] : '';
            if (empty($cost_center_code)) {
                throw new ValidationException(static::$t->_('budget_withholding_cost_center_required_error'), ErrCode::$VALIDATE_ERROR);
            }
        }
        //判断选择的费用部门+费用所属网点/总部+预算归属期间+预算科目+预提金额是否超出预算归属期间预算，如果超出当前季度预算则提示“预算科目预算所属期间预算不足，请先进行预算调整！”
        $check_budgets_data = [
            'cost_store_type' => $params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 ? 2 : 1,//费用所属网点/总部；预算占用组织架构类型：1-网点；2-总部相反需要特殊处理
            'cost_department' => $params['cost_department_id'],//费用部门
            'current_month'   => $params['ownership_period']//预算归属期间
        ];

        //当费用所属网点/总部为网点并且费用是否拆分到网点为拆分到网点时等于费用详情中的所有预提金额的合计
        if ($params['detail'] && $params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02 && $params['is_split'] == BudgetWithholdingEnums::IS_SPLIT_YES) {
            $params['provision_amount'] = array_sum(array_column($params['detail'], 'provision_amount'));
        }

        //预算侧都是*1000倍，故而我们入库也是1000倍
        $params['provision_amount'] = bcmul($params['provision_amount'], 1000);
        $amount_info[] = ['budget_id' => $params['budget_id'], 'amount' => $params['provision_amount']];

        //按照费用部门+费用所属网点/总部+预算归属期间+预算科目+预提金额占用预算（判断预算和占用预算在一起）
        $budgetServer = new BudgetService();
        $budgetServer->checkBudgets($params['no'], $amount_info, BudgetService::ORDER_TYPE_5, $check_budgets_data, 1, $user['id']);

        $params['now_time']         = date('Y-m-d H:i:s', time());
        $params['cost_center_code'] = $cost_center_code;
        return $params;
    }

    /**
     * 费用预提-创建/重新提交-导入费用明细
     * @param array $params 请求参数组
     * @param array $excel_file excel文件内容
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function importDetail(array $params, array $excel_file)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $error_num = 0;
        $res       = $data = [];
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('budget_withholding_import_detail_001'), ErrCode::$VALIDATE_ERROR);
            }

            //仅支持xlsx格式文件
            $extension = $excel_file[0]->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('budget_withholding_import_detail_002'), ErrCode::$VALIDATE_ERROR);
            }

            //解析文件内容
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,//费用所属网点名称
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,//预提金额
                ])
                ->getSheetData();

            //弹出excel标题第一行信息
            $excel_header_column = array_shift($excel_data);
            if (empty($excel_data)) {
                throw new ValidationException(static::$t->_('budget_withholding_import_detail_003'), ErrCode::$VALIDATE_ERROR);
            }

            //费用详情不允许超过2000行
            $count = count($excel_data);
            if ($count > self::IMPORT_DETAIL_LIMIT) {
                throw new ValidationException(static::$t->_('budget_withholding_import_detail_004'), ErrCode::$VALIDATE_ERROR);
            }

            $excel_data_columns = $this->excelToData($excel_data);
            //根据网点名称在网点表中存在并且状态为激活
            $cost_store_names = array_column($excel_data_columns, 'cost_store_name');
            $store_list = (new StoreRepository())->getStoreByNames($cost_store_names);
            //统计每个网点名称出现次数
            $cost_store_name_nums = array_count_values($cost_store_names);
            //SAP的费用所属公司
            $sap_company_ids = EnumsService::getInstance()->getSettingEnvValueIds('purchase_sap_company_ids');
            //费用所属公司ID
            $cost_company_id = $params['cost_company_id'];
            foreach ($excel_data_columns as $line => $item) {
                $error_msg = [];
                //费用所属网点必填
                if (empty($item['cost_store_name'])) {
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_005');
                } elseif ($cost_store_name_nums[$item['cost_store_name']] >= 2) {
                    //网点名称重复！
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_006');
                }

                //费用所属网点在系统中不存在！
                $one_store_info = $store_list[$item['cost_store_name']] ?? [];
                if ($item['cost_store_name'] && empty($one_store_info)) {
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_007');
                }

                //费用成本中心不能为空
                if ($one_store_info && empty($one_store_info['sap_pc_code']) && in_array($cost_company_id, $sap_company_ids)) {
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_008');
                }

                //预提金额必填
                if (empty($item['provision_amount'])) {
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_009');
                } elseif (!preg_match(self::IMPORT_DETAIL_PROVISION_AMOUNT_RULE, $item['provision_amount'])) {
                    //预提金额不正确，请输入>0，小于等于9999999999.99的2位小数以内的正数
                    $error_msg[] = static::$t->_('budget_withholding_import_detail_010');
                }

                if ($error_msg) {
                    $excel_data[$line][2] = implode(' ', $error_msg);
                    $error_num ++;
                } else {
                    $data[] = [
                        'cost_store_id'    => $one_store_info['id'],
                        'cost_store_name'  => $item['cost_store_name'],
                        'cost_center_code' => $one_store_info['sap_pc_code'],
                        'provision_amount' => $item['provision_amount'],
                    ];
                }
            }

            $res = [
                'all_num'     => $count,
                'success_num' => $count - $error_num,
                'failed_num'  => $error_num,
                'url'         => '',
                'data'        => [],
            ];
            if ($error_num) {
                $excel_header_column[2] = static::$t->_('check_result');
                $res['url']  = $this->exportExcel($excel_header_column, $excel_data)['data'];
            } else {
                $res['data'] = $data;
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget_withholding_import_detail failed:' . $real_message);
        }
        return ['code' => $code, 'message' => $message, 'data' => $res];
    }

    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'cost_store_name',//费用所属网点名称
            'provision_amount',//预提金额
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                //转string,不然会变成科学计数法
                if ($key == 'provision_amount') {
                    $info[$index] = (string)$info[$index];
                }
                $data[$line][$key] = $info[$index];
            }
        }
        return $data;
    }


    /**
     * 费用预提-创建
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function add(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //判断预提单号是否已存在
            $budget_withholding_exists = BudgetWithholdingModel::findFirst([
                'conditions' => 'no = :no:',
                'bind'       => ['no' => $params['no']],
            ]);
            if (!empty($budget_withholding_exists)) {
                throw new ValidationException(static::$t->_('budget_withholding_no_existed'), ErrCode::$VALIDATE_ERROR);
            }

            //提交前的验证
            $params = $this->validationOther($params, $user);

            //预提单信息入库
            $budget_withholding_info = new BudgetWithholdingModel();
            $data = $this->getApplyMainData($params, $user);
            $bool = $budget_withholding_info->i_create($data);
            if ($bool === false) {
                throw new BusinessException('费用预提-保存失败: 待处理数据: '. json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_info), ErrCode::$BUSINESS_ERROR);
            }

            //添加 - 预提单附属信息（费用预提-费用详情、附件信息）
            $this->saveApplyAttachedToDb($params, $budget_withholding_info);
            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget_withholding_add failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 获取预提信息
     * @param integer $id ID
     * @return mixed
     * @throws ValidationException
     */
    public function getBudgetWithholdingInfoById($id)
    {
        $budget_withholding_info = BudgetWithholdingModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($budget_withholding_info)) {
            throw new ValidationException(static::$t->_('budget_withholding_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $budget_withholding_info;
    }

    /**
     * 费用预提-重新提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function recommit(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
            if (!in_array($budget_withholding_info->status, [Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL])) {
                throw new ValidationException(static::$t->_('budget_withholding_recommit_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //提交前的验证
            $params = $this->validationOther($params, $user);

            //删掉原先的费用明细
            $budget_withholding_detail = $budget_withholding_info->getDetails();
            $bool = $budget_withholding_detail->delete();
            if ($bool === false) {
                throw new BusinessException('费用预提-重新提交-删除原费用明细失败: 待处理数据: '. json_encode($budget_withholding_detail->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_detail), ErrCode::$BUSINESS_ERROR);
            }

            //删除原来的附件信息
            $budget_withholding_attachment = $budget_withholding_info->getAttachments();
            $bool = $budget_withholding_attachment->delete();
            if ($bool === false) {
                throw new BusinessException('费用预提-重新提交-删除原附件失败: 待处理数据: '. json_encode($budget_withholding_attachment->toArray(), JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_attachment), ErrCode::$BUSINESS_ERROR);
            }

            //预提单信息入库
            $params['workflow_no'] = $budget_withholding_info->workflow_no;
            $data = $this->getApplyMainData($params, $user, 'recommit');
            $bool = $budget_withholding_info->i_update($data);
            if ($bool === false) {
                throw new BusinessException('费用预提-重新提交-保存失败: 待处理数据: '. json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_info), ErrCode::$BUSINESS_ERROR);
            }

            //重新提交 - 预提单附属信息（费用预提-费用详情、附件信息）
            $this->saveApplyAttachedToDb($params, $budget_withholding_info);

            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget_withholding_recommit failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 组装预提单主数据
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @param string $type add，recommit
     * @return array
     * @throws ValidationException
     */
    private function getApplyMainData(array $params, array $user, string $type = 'add')
    {
        $data = [
            'cost_company_id'      => $params['cost_company_id'],
            'cost_company_name'    => $params['cost_company_name'],
            'cost_department_id'   => $params['cost_department_id'],
            'cost_department_name' => $params['cost_department_name'],
            'cost_center_code'     => $params['cost_center_code'],
            'cost_store_type'      => $params['cost_store_type'],
            'is_split'             => $params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_01 ? 0 : $params['is_split'],
            'ownership_period'     => $params['ownership_period'],
            'budget_id'            => $params['budget_id'],
            'provision_amount'     => $params['provision_amount'],
            'remark'               => $params['remark'],
            'status'               => Enums::WF_STATE_PENDING,
            'use_status'           => BudgetWithholdingEnums::IS_SPLIT_NO,
            'updated_id'           => $user['id'],
            'updated_name'         => $this->getNameAndNickName($user['name'], $user['nick_name']),
            'updated_at'           => $params['now_time'],
        ];

        //调取by创建审批
        $by_workflow = new ByWorkflowService();
        $by_audit_data = [
            'submitter_id' => $user['id'],
            'summary_data' => [],
            'biz_type'     => ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
            'audit_params' => [
                'company_id'       => $params['cost_company_id'],
                'department_id'    => $params['cost_department_id'],
                'budget_id'        => $params['budget_id'],
                'provision_amount' => $params['provision_amount'],
            ],
        ];
        if ($type == 'add') {
            //默认币种
            $default_currency = (new EnumsService())->getSysCurrencyInfo();
            $default_currency_code = $default_currency['code'] ?? 0;
            $data = array_merge($data, [
                'no'                   => $params['no'],
                'apply_id'             => $params['apply_id'],
                'apply_name'           => $params['apply_name'],
                'apply_date'           => $params['apply_date'],
                'currency'             => $default_currency_code,
                'created_at'           => $params['now_time'],
            ]);
            $by_add_result = $by_workflow->add($by_audit_data);
        } else {
            $by_audit_data['serial_no'] = $params['workflow_no'];
            $by_add_result = $by_workflow->edit($by_audit_data);
        }
        $data['workflow_no'] = $by_add_result['serial_no'];
        return $data;
    }

    /**
     * 添加/重新提交 - 预提单附属信息（费用预提-费用详情、附件信息）
     * @param array $params 请求参数组
     * @param object $budget_withholding 预提单
     * @throws BusinessException
     */
    private function saveApplyAttachedToDb(array $params, object $budget_withholding)
    {
        //费用明细 - 当费用所属网点/总部为网点并且费用是否拆分到网点为拆分到网点时显示，显示后必填
        if ($params['cost_store_type'] == Enums::PAYMENT_COST_STORE_TYPE_02 && $params['is_split'] == BudgetWithholdingEnums::IS_SPLIT_YES) {
            $details = [];
            foreach ($params['detail'] as $item) {
                $details[] = [
                    'budget_withholding_id' => $budget_withholding->id,
                    'cost_store_id'         => $item['cost_store_id'],
                    'cost_store_name'       => $item['cost_store_name'],
                    'cost_center_code'      => $item['cost_center_code'],
                    'provision_amount'      => bcmul($item['provision_amount'], 1000),
                    'created_at'            => $params['now_time'],
                    'updated_at'            => $params['now_time']
                ];
            }
            $budget_withholding_detail = new BudgetWithholdingDetailModel();
            if (!empty($details) && !$budget_withholding_detail->batch_insert($details)) {
                throw new BusinessException('费用预提-费用详情-保存失败: 待处理数据: ' . json_encode($details, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_detail), ErrCode::$BUSINESS_ERROR);
            }
        }
        //附件
        if (!empty($params['attachments'])) {
            $sys_attachment = new SysAttachmentModel();
            $attachArr      = [];
            foreach ($params['attachments'] as $attachment) {
                $tmp                    = [];
                $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_WITHHOLDING_ADD;
                $tmp['oss_bucket_key']  = $budget_withholding->id;
                $tmp['sub_type']        = 0;
                $tmp['bucket_name']     = $attachment['bucket_name'];
                $tmp['object_key']      = $attachment['object_key'];
                $tmp['file_name']       = $attachment['file_name'];
                $attachArr[]            = $tmp;
            }
            if (!$sys_attachment->batch_insert($attachArr)) {
                throw new BusinessException('费用预提-附件添加失败: 待处理数据: ' . json_encode($attachArr, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($sys_attachment), ErrCode::$BUSINESS_ERROR);
            }
        }
    }

    /**
     * 费用预提-查询
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @param int $type 列表来源
     * @return array
     */
    public function getList(array $params, array $user, $type = self::LIST_TYPE_APPLY)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $count     = 0;
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            //预提审核-待处理
            $list_type = $params['type'] ?? self::LIST_TYPE_WAIT_HANDLE;
            $other_params = [];
            if ($type == self::LIST_TYPE_AUDIT && $list_type == self::LIST_TYPE_WAIT_HANDLE) {
                $other_params = $this->getWorkflowNo($params, $user);
                if ($other_params['total_count'] && $other_params['list']) {
                    $params['workflow_no'] = array_values(array_column($other_params['list'], 'serial_no'));
                    $count = $other_params['total_count'];
                } else {
                    return [
                        'code' => $code,
                        'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                        'data' => $data,
                    ];
                }
            }

            //获取列表
            $count = $count ? $count : $this->getListCount($params, $user, $type);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.*');
                $builder->from(['main' => BudgetWithholdingModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($builder, $params, $user, $type);
                if (!($type == self::LIST_TYPE_AUDIT && $list_type == self::LIST_TYPE_WAIT_HANDLE)) {
                    $builder->limit($page_size, $offset);
                }
                if ($type == self::LIST_TYPE_APPLY) {
                    $builder->orderby('main.created_at desc');
                } elseif ($type == self::LIST_TYPE_AUDIT && $list_type == self::LIST_TYPE_HAD_HANDLE) {
                    $builder->orderby('main.updated_at desc');
                }
                $builder->groupBy('main.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
                if ($type == self::LIST_TYPE_AUDIT && $list_type == self::LIST_TYPE_WAIT_HANDLE) {
                    $items = (new ByWorkflowService())->pendingDataSort($other_params['list'], $items);
                }
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget_withholding_list failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取审核人名下待审核记录总数以及by审批流-流水号组
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function getWorkflowNo(array $params, array $user)
    {
        //第一步在oa库查特定条件下的workflow_no号列
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.workflow_no');
        $builder->from(['main' => BudgetWithholdingModel::class]);
        $builder     = $this->getCondition($builder, $params, $user, self::LIST_TYPE_AUDIT);
        $search_list = $builder->getQuery()->execute()->toArray();

        // 第二步调取by接口获取当前登陆人要审批的审批流水号组
        $total_count = 0;
        $list = [];
        if (!empty($search_list)) {
            $page_size      = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num       = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $workflow_no    = array_column($search_list, 'workflow_no');
            $by_list_params = [
                'serial_no'   => $workflow_no,
                'biz_type'    => [ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING],
                'approval_id' => $user['id'],
                'state'       => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                'page_num'    => $page_num,
                'page_size'   => $page_size,
                'sort'        => 4,//审批人接收时间升序
            ];
            $result         = (new ByWorkflowService())->getList($by_list_params);
            $total_count    = !empty($result['total_count']) ? $result['total_count'] : 0;
            if (!empty($result['list'])) {
                //第三步查询到该审批人明细存在要审批的数据，关联上业务单据然后展示
                $list = $result['list'];
            }
        }
        return [
            'total_count' => $total_count,
            'list' => $list,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @param int $type 列表来源
     * @param bool $is_export 是否是导出
     * @return int
     */
    public function getListCount(array $params, array $user, int $type = self::LIST_TYPE_APPLY, $is_export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => BudgetWithholdingModel::class]);
        $builder->columns('count(DISTINCT main.id) AS count');
        if ($is_export) {
            $builder->leftJoin(OrdinaryPayment::class, 'payment.budget_withholding_id = main.id and payment.approval_status in ('.Enums::WF_STATE_PENDING . ',' . Enums::WF_STATE_APPROVED . ') AND payment.pay_status in (' . Enums::PAYMENT_PAY_STATUS_PENDING . ',' . Enums::PAYMENT_PAY_STATUS_PAY . ')', 'payment');
        }
        $builder = $this->getCondition($builder, $params, $user, $type);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @param int $type 列表来源
     * @return mixed
     */
    public function getCondition(object $builder, array $params, array $user, int $type = self::LIST_TYPE_APPLY)
    {
        $no                     = $params['no'] ?? '';                    //预提编号
        $status                 = $params['status'] ?? [];                //预提状态
        $use_status             = $params['use_status'] ?? 0;             //使用状态
        $cost_department_id     = $params['cost_department_id'] ?? 0;     //费用所属部门
        $cost_store_type        = $params['cost_store_type'] ?? 0;        //费用总部/网点
        $ownership_period_start = $params['ownership_period_start'] ?? '';//预算归属期间-开始
        $ownership_period_end   = $params['ownership_period_end'] ?? '';  //预算归属期间-结束
        $budget_id              = $params['budget_id'] ?? 0;              //预算科目
        $apply_id               = $params['apply_id'] ?? 0;               //提交人工号
        $cost_company_id        = $params['cost_company_id'] ?? [];       //费用所属公司
        $list_type              = $params['type'] ?? self::LIST_TYPE_WAIT_HANDLE;//审核列表-待处理/已处理
        $workflow_no            = $params['workflow_no'] ?? [];//by审批流-流水号
        //区分不同入口过来的列表基础查询条件
        if ($type == self::LIST_TYPE_AUDIT) {
            //我的审核-需要按照审批人查询
            if ($list_type == self::LIST_TYPE_WAIT_HANDLE) {
                //待处理 - 待审核
                $builder->andWhere('main.status = :status:', ['status' => Enums::WF_STATE_PENDING]);
                if (!empty($workflow_no)) {
                    $builder->inWhere('main.workflow_no', $workflow_no);
                }
            } elseif ($list_type == self::LIST_TYPE_HAD_HANDLE) {
                //已处理 - 驳回、通过
                $builder->leftJoin(ByWorkflowAuditLogModel::class, 'main.id = audit.biz_value', 'audit');
                $builder->andWhere('audit.approval_id = :approval_id:', ['approval_id' => $user['id']]);
                $builder->andWhere('audit.biz_type = :biz_type:', ['biz_type' => Enums::WF_BUDGET_WITHHOLDING_BIZ_TYPE]);
                $builder->inWhere('audit.status', [Enums::WF_STATE_REJECTED, Enums::WF_STATE_APPROVED]);
            }
        }

        //预提编号
        if (!empty($no)) {
            $builder->andWhere('main.no = :no:', ['no' => $no]);
        }
        //预提状态
        if (!empty($status)) {
            $builder->inWhere('main.status', $status);
        }
        //使用状态
        if (!empty($use_status)) {
            $builder->andWhere('main.use_status = :use_status:', ['use_status' => $use_status]);
        }
        //费用所属部门
        if (!empty($cost_department_id)) {
            //按照部门查找,需要找该部门以及下属子部门的所有信息
            $department_ids = (new DepartmentService())->getChildrenListByDepartmentIdV2($cost_department_id,true);
            array_push($department_ids, $cost_department_id);
            $builder->inWhere('main.cost_department_id', $department_ids);
        }
        //费用总部/网点
        if (!empty($cost_store_type)) {
            $builder->andWhere('main.cost_store_type = :cost_store_type:', ['cost_store_type' => $cost_store_type]);
        }
        //预算归属期间-起始
        if (!empty($ownership_period_start)) {
            $builder->andWhere('main.ownership_period >= :ownership_period_start:', ['ownership_period_start' => $ownership_period_start]);
        }
        //预算归属期间-截止
        if (!empty($ownership_period_end)) {
            $builder->andWhere('main.ownership_period <= :ownership_period_end:', ['ownership_period_end' => $ownership_period_end]);
        }
        //预算科目
        if (!empty($budget_id)) {
            $builder->andWhere('main.budget_id = :budget_id:', ['budget_id' => $budget_id]);
        }
        //提交人工号
        if (!empty($apply_id)) {
            $builder->andWhere('main.apply_id = :apply_id:', ['apply_id' => $apply_id]);
        }
        //费用所属公司
        if (!empty($cost_company_id)) {
            $builder->inWhere('main.cost_company_id', $cost_company_id);
        }
        return $builder;
    }

    /**
     * 获取导出数据的总量
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return mixed
     */
    public function getDataExportTotal(array $params, array $user)
    {
        $total_count = 0;
        try {
            $total_count = $this->getListCount($params, $user, self::LIST_TYPE_APPLY, true);
        } catch (Exception $e) {
            $this->logger->error('代理支付-获取导出数据的总量异常:' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 费用预提-导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array|mixed
     * @throws GuzzleException
     */
    public function listExport(array $params, array $user)
    {
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDataExportTotal($params, $user) > self::DOWNLOAD_LIMIT) {
            $result         = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], DownloadCenterEnum::BUDGET_WITHHOLDING_EXPORT, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url'      => '',
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $result         = $this->getSyncExportData($params, $user);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url'      => $result['data'],
            ];
        }
        return $result;
    }

    /**
     * 数据同步导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     * @throws GuzzleException
     */
    public function getSyncExportData(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            // 同步下载, 最多1w条
            $params['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $params['pageSize'] = self::DOWNLOAD_LIMIT;

            // 获取数据
            $excel_data = $this->getExportData($params, $user);

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = 'budget_withholding_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('预算管理-费用预提-导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取列表导出数据
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @param int $type 列表来源
     * @return array
     */
    public function getExportData(array $params, array $user, int $type = self::LIST_TYPE_APPLY)
    {
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        $columns = 'main.no, main.apply_name, main.apply_id, main.cost_company_name, main.cost_department_name, main.cost_store_type, main.is_split, main.ownership_period, main.budget_id, main.currency, ';
        $columns.= 'main.provision_amount, main.use_amount, main.status, main.use_status, main.reason, payment.apply_no, payment.created_at, payment.amount_total_have_tax, payment.currency as payment_currency, payment.approval_status, payment.pay_status';
        $builder->columns($columns);
        $builder->from(['main' => BudgetWithholdingModel::class]);
        $builder->leftJoin(OrdinaryPayment::class, 'payment.budget_withholding_id = main.id and payment.approval_status in ('.Enums::WF_STATE_PENDING . ',' . Enums::WF_STATE_APPROVED . ') AND payment.pay_status in (' . Enums::PAYMENT_PAY_STATUS_PENDING . ',' . Enums::PAYMENT_PAY_STATUS_PAY . ')', 'payment');
        $builder = $this->getCondition($builder, $params, $user, $type, true);
        $builder->limit($page_size, $offset);
        $builder->orderby('main.id desc');
        $items = $builder->getQuery()->execute()->toArray();
        return $this->handleListItems($items, true);
    }

    /**
     * 格式化列表
     * @param array $items 列表
     * @param bool $is_export 是否是导出
     * @return array
     */
    private function handleListItems($items, $is_export = false)
    {
        if (empty($items)) {
            return $items;
        }
        if ($is_export) {
            $row_values = [];
            $budget_ids  = array_values(array_unique(array_filter(array_column($items, 'budget_id'))));
            $budget_list = (new BudgetService())->getBudgetByIds($budget_ids);
            foreach ($items as $item) {
                $provision_amount = bcdiv($item['provision_amount'], 1000, 2);
                $use_amount       = bcdiv($item['use_amount'], 1000, 2);
                $currency_text    = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                $row_values[] = [
                    $item['no'],
                    $item['apply_name'],
                    $item['apply_id'],
                    $item['cost_company_name'],
                    $item['cost_department_name'],
                    static::$t[Enums::$payment_cost_store_type[$item['cost_store_type']]],
                    static::$t[BudgetWithholdingEnums::$is_split[$item['is_split']]],
                    $item['ownership_period'],
                    $budget_list[$item['budget_id']] ?? '',
                    $provision_amount . ' ' . $currency_text,
                    static::$t->_(BudgetWithholdingEnums::$status[$item['status']]),
                    $item['status'] == BudgetWithholdingEnums::STATUS_CLOSED ? $item['reason'] : '',
                    static::$t->_(BudgetWithholdingEnums::$use_status[$item['use_status']] ?? ''),
                    $use_amount . ' ' . $currency_text,
                    bcsub($provision_amount, $use_amount, 2) . ' ' . $currency_text,
                    $item['apply_no'] ?? '',
                    !empty($item['created_at']) ? date('Y-m-d', strtotime($item['created_at'])) : '',
                    isset($item['amount_total_have_tax']) ? ($item['amount_total_have_tax'] . ' ' . static::$t->_(GlobalEnums::$currency_item[$item['payment_currency']])) : '',
                    !empty($item['approval_status']) ? static::$t->_(Enums::$contract_status[$item['approval_status']]) : '',
                    !empty($item['pay_status']) ? static::$t->_(Enums::$payment_pay_status[$item['pay_status']]) : '',
                ];
            }
            $items = $row_values;
        } else {
            $budget_ids  = array_values(array_unique(array_filter(array_column($items, 'budget_id'))));
            $budget_list = (new BudgetService())->getBudgetByIds($budget_ids);
            foreach ($items as &$item) {
                $item['provision_amount'] = bcdiv($item['provision_amount'], 1000, 2);                  //预提金额
                $item['use_amount']       = bcdiv($item['use_amount'], 1000, 2);                        //使用金额
                $item['budget_name']      = $budget_list[$item['budget_id']] ?? '';                                 //预算科目名称
                $item['remain_amount']    = bcsub($item['provision_amount'], $item['use_amount'], 2);          //剩余金额
                $item['currency_text']    = static::$t->_(GlobalEnums::$currency_item[$item['currency']]);          //币种
                $item['use_status_text']  = static::$t->_(BudgetWithholdingEnums::$use_status[$item['use_status']]);//使用状态
                $item['status_text']      = static::$t->_(BudgetWithholdingEnums::$status[$item['status']]);        //使用状态
            }
        }
        return $items;
    }

    /**
     * 导出的Excel表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('budget_withholding.no'),//预提编号
            static::$t->_('budget_withholding.apply_name'),//提交人姓名
            static::$t->_('budget_withholding.apply_id'),//提交人工号
            static::$t->_('re_field_cost_company_name'),//费用所属公司
            static::$t->_('re_filed_apply_cost_department'),//费用所属部门
            static::$t->_('re_filed_apply_cost_store'),//费用所属网点/总部
            static::$t->_('budget_withholding.is_split'),//费用是否拆分到网点
            static::$t->_('budget_withholding.ownership_period'),//预算归属期间
            static::$t->_('budget_object_name'),//预算科目
            static::$t->_('budget_withholding.provision_amount'),//预提金额
            static::$t->_('budget_withholding.status'),//预提状态
            static::$t->_('budget_withholding.close_reason'),//关闭说明
            static::$t->_('budget_withholding.use_status'),//使用状态
            static::$t->_('budget_withholding.use_amount'),//使用金额
            static::$t->_('budget_withholding.remain_amount'),//剩余金额
            static::$t->_('ordinary_payment.apply_no'),//付款单号
            static::$t->_('global.apply.date'),//申请日期
            static::$t->_('bank_flow_export_field_amount'),//含税金额总计
            static::$t->_('global.apply.status.text'),//申请状态
            static::$t->_('global_pay_status'),//支付状态
        ];
    }

    /**
     * 费用预提-撤回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function cancel(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
            if ($budget_withholding_info->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('budget_withholding_cancel_invalid'),
                    ErrCode::$VALIDATE_ERROR);
            }

            //调取by接口，by撤销成功，记录撤回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit([
                'serial_no'   => $budget_withholding_info->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
                'reason'      => $params['reason'],
                'status'      => ByWorkflowEnums::BY_OPERATE_CANCEL,
                'operator_id' => $user['id'],
                'is_force'    => 1,//是否跳过权限验证强制审批1是
            ]);

            //单据撤回
            $date                                 = date('Y-m-d H:i:s');
            $budget_withholding_info->status      = Enums::WF_STATE_CANCEL;
            $budget_withholding_info->reason      = $params['reason'];
            $budget_withholding_info->canceled_at = $date;
            $budget_withholding_info->updated_at  = $date;
            $bool                                 = $budget_withholding_info->save();
            if ($bool === false) {
                throw new BusinessException('费用预提-撤回失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($budget_withholding_info), ErrCode::$BUSINESS_ERROR);
            }

            //同时释放当前预提单占用的预算
            $this->freedBudget($budget_withholding_info, $user);

            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget_withholding_cancel failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 费用预提-关闭
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function close(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
            if ($budget_withholding_info->status != Enums::WF_STATE_APPROVED) {
                throw new ValidationException(static::$t->_('budget_withholding_close_invalid'), ErrCode::$VALIDATE_ERROR);
            }

            //校验普通付款中预提单号等于当前预提单并且申请状态等于待审核或已通过的数据是否状态都为已支付或未支付，只要有任意一条数据不是，则提示“存在未完成支付的单据，不允许关闭预提单”，如果没有关联的普通付款单，则无效校验普通付款状态
            OrdinaryPaymentListService::getInstance()->checkBudgetWithholding($budget_withholding_info->id);

            //单据关闭
            $date                                = date('Y-m-d H:i:s');
            $budget_withholding_info->status     = BudgetWithholdingEnums::STATUS_CLOSED;
            $budget_withholding_info->reason     = $params['reason'];
            $budget_withholding_info->closed_at  = $date;
            $budget_withholding_info->updated_at = $date;

            //推送SAP红冲暂估凭证
            $is_send_red_voucher = BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_NO;
            $detail = $budget_withholding_info->getDetails()->toArray();
            if ($detail) {
                //有明细 - 按照明细行 - 当存在任意一行明细行的使用金额小于预提金额 - 推送SAP红冲暂估凭证
                foreach ($detail as $item) {
                    if ($item['use_amount'] < $item['provision_amount']) {
                        $is_send_red_voucher = BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_YES;
                        break;
                    }
                }
            } else {
                //无明细行 - 按照头 - 实际支付金额[使用金额]小于预提金额 - 推送SAP红冲暂估凭证
                $is_send_red_voucher = $budget_withholding_info->use_amount < $budget_withholding_info->provision_amount ? BudgetWithholdingEnums::IS_SEND_RED_VOUCHER_YES : $is_send_red_voucher;
            }
            $budget_withholding_info->is_send_red_voucher = $is_send_red_voucher;
            $bool = $budget_withholding_info->save();
            if ($bool === false) {
                throw new BusinessException('费用预提-关闭失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($budget_withholding_info), ErrCode::$BUSINESS_ERROR);
            }

            //按照头 - 实际支付金额[使用金额]小于预提金额 - 释放预算
            if ($budget_withholding_info->use_amount < $budget_withholding_info->provision_amount) {
                //释放预算逻辑：将预提单的预算占用金额从预提金额调整为使用金额--采购申请修改数量释放预算逻辑
                $this->freedBudget($budget_withholding_info, $user, $budget_withholding_info->use_amount);
            }

            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget_withholding_close failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 查看
     * @param array $params 请求参数组
     * @param array $user 当前登陆者用户信息组
     * @return array
     */
    public function detail(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail  = [];
        try {
            $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
            $detail                  = $budget_withholding_info->toArray();

            $budget_list                = (new BudgetService())->getBudgetByIds([$detail['budget_id']]);
            $detail['budget_name']      = $budget_list[$detail['budget_id']] ?? '';
            $detail['currency_text']    = static::$t->_(GlobalEnums::$currency_item[$detail['currency']]);
            $detail['status_text']      = static::$t->_(BudgetWithholdingEnums::$status[$detail['status']]);
            $detail['use_status_text']  = static::$t->_(BudgetWithholdingEnums::$use_status[$detail['use_status']]);
            $detail['provision_amount'] = bcdiv($detail['provision_amount'], 1000, 2);
            $detail['use_amount']       = bcdiv($detail['use_amount'], 1000, 2);
            $detail['remain_amount']    = bcsub($detail['provision_amount'], $detail['use_amount'], 2);
            $detail['attachments']      = $budget_withholding_info->getAttachments()->toArray();
            //审批日志
            $detail['auth_logs'] = (new ByWorkflowService())->log(['serial_no'   => $detail['workflow_no'], 'operator_id' => $user['id'], 'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,]);
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $detail,
        ];
    }

    /**
     * 查看-费用明细
     * @param array $params 请求参数组
     * @return array
     */
    public function viewDetail(array $params)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $data      = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(BudgetWithholdingDetailModel::class);
            $builder->where('budget_withholding_id = :budget_withholding_id:', ['budget_withholding_id' => $params['id']]);
            $builder->columns('*');
            $builder->orderby('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleDetailListItems($items);
            $data  = $items ?? [];
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget_withholding_view_detail failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 格式化-费用明细列表
     * @param array $items 列表
     * @param boolean $is_export 是否为导出 true是，false否
     * @return array
     */
    private function handleDetailListItems(array $items, bool $is_export = false)
    {
        if (empty($items)) {
            return $items;
        }
        if ($is_export) {
            $excel_data = [];
            foreach ($items as $key => $item) {
                $provision_amount = bcdiv($item['provision_amount'], 1000, 2);
                $use_amount       = bcdiv($item['use_amount'], 1000, 2);
                $excel_data[]     = [
                    $key + 1,
                    $item['cost_store_name'],
                    $item['cost_store_id'],
                    $item['cost_center_code'],
                    $provision_amount,
                    $use_amount,
                    bcsub($provision_amount, $use_amount, 2),
                ];
            }
            $items = $excel_data;
        } else {
            foreach ($items as &$item) {
                $item['provision_amount'] = bcdiv($item['provision_amount'], 1000, 2);
                $item['use_amount']       = bcdiv($item['use_amount'], 1000, 2);
                $item['remain_amount']    = bcsub($item['provision_amount'], $item['use_amount'], 2);
            }
        }
        return $items;
    }

    /**
     * 查看-费用明细-导出
     * @param array $params 请求参数组
     * @return array
     * @throws GuzzleException
     */
    public function viewDetailExport(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
            // 获取数据
            $builder = $this->modelsManager->createBuilder();
            $builder->from(BudgetWithholdingDetailModel::class);
            $builder->where('budget_withholding_id = :budget_withholding_id:', ['budget_withholding_id' => $params['id']]);
            $builder->columns('*');
            $builder->orderby('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $excel_data = $this->handleDetailListItems($items, true);

            // 获取表头
            $currency_text = '(' . static::$t->_(GlobalEnums::$currency_item[$budget_withholding_info->currency]) . ')';
            $header = [
                static::$t->_('global.no'),//序号
                static::$t->_('csr_field_create_store_name'),//费用所属网点
                static::$t->_('budget_withholding_detail.cost_store_id'),//网点编码
                static::$t->_('sap_cost_center'),//成本中心
                static::$t->_('budget_withholding.provision_amount') . $currency_text,//预提金额
                static::$t->_('budget_withholding.use_amount'). $currency_text,//使用金额
                static::$t->_('budget_withholding.remain_amount'). $currency_text,//剩余金额
            ];

            // 生成Excel
            $file_name = 'budget_withholding_detail_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('预算管理-费用预提-费用明细-导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 查看-使用明细
     * @param array $params 请求参数组
     * @return array
     */
    public function viewUseDetail(array $params)
    {
        $code      = ErrCode::$SUCCESS;
        $message   = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num  = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            //获取普通付款中预提单号等于当前预提单并且申请状态等于待审核或已通过并且支付状态等于待支付或已支付的数据
            $count =  $this->getUseDetailCount($params);

            if ($count > 0) {
                $builder = $this->getUseDetailConditions($params);
                $builder->columns([
                    'id',
                    'apply_no',
                    'apply_id',
                    'apply_name',
                    'apply_node_department_name',
                    'created_at',
                    'amount_total_have_tax',
                    'amount_total_actually',
                    'approval_status',
                    'pay_status',
                    'currency',
                ]);
                $builder->limit($page_size, $offset);
                $builder->orderby('id desc');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleUseDetailListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('budget_withholding_view_use_detail failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取使用明细-查询对象
     * @param array $params 请求参数组
     * @return mixed
     */
    private function getUseDetailConditions($params)
    {
        //获取普通付款中预提单号等于当前预提单并且申请状态等于待审核或已通过并且支付状态等于待支付或已支付的数据
        $builder = $this->modelsManager->createBuilder();
        $builder->from(OrdinaryPayment::class);
        $builder->where('budget_withholding_id = :budget_withholding_id:', ['budget_withholding_id' => $params['id']]);
        $builder->inWhere('approval_status', [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]);
        $builder->inWhere('pay_status', [Enums::PAYMENT_PAY_STATUS_PENDING, Enums::PAYMENT_PAY_STATUS_PAY]);
        return $builder;
    }

    /**
     * 获取使用明细总数
     * @param array $params 请求参数组
     * @return int
     */
    private function getUseDetailCount($params)
    {
        $builder = $this->getUseDetailConditions($params);
        return (int)$builder->columns('count(id) AS count')->getQuery()->getSingleResult()->count;
    }

    /**
     * 格式化使用详情数据
     * @param array $items 使用详情数据
     * @param boolean $is_export 是否为导出 true是，false否
     * @return array
     */
    private function handleUseDetailListItems(array $items, bool $is_export = false)
    {
        if (empty($items)) {
            return [];
        }

        if ($is_export) {
            $excel_data = [];
            foreach ($items as $key => $item) {
                $excel_data[] = [
                    $item['apply_no'],
                    $item['apply_name'],
                    $item['apply_id'],
                    $item['apply_node_department_name'],
                    gmdate_customize_by_datetime($item['created_at'], 'Y-m-d'),
                    $item['amount_total_have_tax'] . ' ' . static::$t[GlobalEnums::$currency_item[$item['currency']]],
                    static::$t[Enums::$payment_apply_status[$item['approval_status']]],
                    static::$t[Enums::$payment_pay_status[$item['pay_status']]]
                ];
            }
            $items = $excel_data;
        } else {
            foreach ($items as &$item) {
                $item['currency_text']        = static::$t[GlobalEnums::$currency_item[$item['currency']]];
                $item['approval_status_text'] = static::$t[Enums::$payment_apply_status[$item['approval_status']]];
                $item['pay_status_text']      = static::$t[Enums::$payment_pay_status[$item['pay_status']]];
                $item['created_at']           = gmdate_customize_by_datetime($item['created_at'], 'Y-m-d');
            }
        }

        return $items;
    }

    /**
     * 查看-使用明细-导出
     * @param array $params 请求参数组
     * @return array
     * @throws GuzzleException
     */
    public function viewUseDetailExport(array $params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = '';

        try {
            // 获取数据
            $builder = $this->getUseDetailConditions($params);
            $builder->columns([
                'id',
                'apply_no',
                'apply_id',
                'apply_name',
                'apply_node_department_name',
                'created_at',
                'amount_total_have_tax',
                'amount_total_actually',
                'approval_status',
                'pay_status',
                'currency',
            ]);
            $builder->orderby('id desc');
            $items = $builder->getQuery()->execute()->toArray();
            $excel_data = $this->handleUseDetailListItems($items, true);

            // 获取表头
            $header = [
                static::$t->_('global.number'),//编号
                static::$t->_('re_field_apply_name'),//申请人姓名
                static::$t->_('csr_field_create_id'),//申请人工号
                static::$t->_('csr_field_dept_name'),//申请人部门
                static::$t->_('global.apply.date'),//申请日期
                static::$t->_('ordinary_payment.amount_total_have_tax'),//含税金额合计
                static::$t->_('global.apply.status.text'),//申请状态
                static::$t->_('global_pay_status'),//支付状态
            ];

            // 生成Excel
            $file_name = 'budget_withholding_use_detail_' . date('YmdHis') . '.xlsx';
            $result    = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('预算管理-费用预提-使用明细-导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 检测费用预提单据是否可审批
     * @param array $params 请求参数组
     * @return mixed
     * @throws ValidationException
     */
    public function validationAudit(array $params)
    {
        $budget_withholding_info = $this->getBudgetWithholdingInfoById($params['id']);
        if ($budget_withholding_info->status != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('budget_withholding_audit_status_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $budget_withholding_info;
    }

    /**
     * 通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = $this->validationAudit($params);
            //调取by审批接口，by审核通过成功，记录通过信息
            $reason      = $params['reason'] ?? '';
            $by_workflow = new ByWorkflowService();
            $result      = $by_workflow->audit([
                'serial_no'   => $budget_withholding_info->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
                'reason'      => $reason,
                'status'      => ByWorkflowEnums::BY_OPERATE_PASS,
                'operator_id' => $user['id'],
            ]);
            //by审批通过且最终节点审批才需要做下面的逻辑
            $now_time = date('Y-m-d H:i:s');
            if (!empty($result) && !empty($result['is_final']) && $result['is_final'] == 1) {
                $this->savePass($budget_withholding_info, $reason, $now_time);
            }
            //记录审批操作记录
            $this->savePassLog($budget_withholding_info, $user, $now_time);
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget-withholding-audit-pass failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $bool ?? false,
        ];
    }

    /**
     * 保持审批信息
     * @param object $budget_withholding_info 费用预提单据对象
     * @param string $reason 审批原因
     * @param string $now_time 审批时间
     * @throws BusinessException
     */
    private function savePass($budget_withholding_info, $reason, $now_time)
    {
        $pass_params = [
            'status'      => Enums::WF_STATE_APPROVED,
            'reason'      => $reason,
            'approved_at' => $now_time,
            'updated_at'  => $now_time,
        ];
        $bool        = $budget_withholding_info->i_update($pass_params);
        if ($bool === false) {
            throw new BusinessException('预算管理-预提审核-通过: 待处理数据: ' . json_encode($pass_params,
                    JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_info),
                ErrCode::$BUSINESS_ERROR);
        }
    }

    /**
     * 保持审批通过审批日志
     * @param object $budget_withholding_info 费用预提单据对象
     * @param array $user 审批人工号
     * @param string $now_time 时间
     * @return bool
     * @throws BusinessException
     */
    private function savePassLog($budget_withholding_info, $user, $now_time)
    {
        $by_workflow_audit_log = new ByWorkflowAuditLogModel();
        $pass_log              = [
            'biz_type'      => Enums::WF_BUDGET_WITHHOLDING_BIZ_TYPE,
            'biz_value'     => $budget_withholding_info->id,
            'staff_id'      => $budget_withholding_info->apply_id,
            'approval_id'   => $user['id'],
            'status'        => Enums::WF_STATE_APPROVED,
            'approval_time' => $now_time,
            'created_at'    => $now_time,
            'updated_at'    => $now_time,
        ];
        $bool                  = $by_workflow_audit_log->i_create($pass_log);
        if ($bool === false) {
            throw new BusinessException('预算管理-预提审核-通过-记录by审批日志失败: 待处理数据: ' . json_encode($pass_log,
                    JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log),
                ErrCode::$BUSINESS_ERROR);
        }
        return true;
    }

    /**
     * by自动通过审批回调
     * @param array $params 请求参数
     * @return array
     */
    public function autoPass($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = BudgetWithholdingModel::findFirst([
                'conditions' => 'workflow_no = :workflow_no:',
                'bind'       => ['workflow_no' => $params['workflow_no']],
            ]);
            if (empty($budget_withholding_info)) {
                throw new ValidationException(static::$t->_('budget_withholding_not_exists'), ErrCode::$VALIDATE_ERROR);
            }
            //由于by哪里异步通知无法区分是系统自动审批还是人为审批，一律回调，非待审核默认为异步同步成功
            if ($budget_withholding_info->status == Enums::WF_STATE_PENDING) {
                //审核通过
                $now_time = date('Y-m-d H:i:s');
                $this->savePass($budget_withholding_info, $params['reason'] ?? '', $now_time);

                //记录审批操作记录
                $this->savePassLog($budget_withholding_info, ['id' => $params['approval_id']], $now_time);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('budget-withholding-audit-auto-pass failed:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $bool ?? true,
        ];
    }

    /**
     * 驳回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $budget_withholding_info = $this->validationAudit($params);
            //调取by驳回接口，by驳回成功，记录驳回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit([
                'serial_no'   => $budget_withholding_info->workflow_no,
                'biz_type'    => ByWorkflowEnums::BY_BIZ_TYPE_BUDGET_WITHHOLDING,
                'reason'      => $params['reason'],
                'status'      => ByWorkflowEnums::BY_OPERATE_REJECT,
                'operator_id' => $user['id'],
            ]);

            //by审批成功，驳回单据
            $now_time      = date('Y-m-d H:i:s');
            $reject_params = [
                'reason'      => $params['reason'],
                'status'      => Enums::WF_STATE_REJECTED,
                'rejected_at' => $now_time,
                'updated_at'  => $now_time,
            ];
            $bool          = $budget_withholding_info->i_update($reject_params);
            if ($bool === false) {
                throw new BusinessException('预算管理-预提审核-驳回失败: 待处理数据: ' . json_encode($reject_params,
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_info),
                    ErrCode::$BUSINESS_ERROR);
            }

            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $reject_log            = [
                'biz_type'      => Enums::WF_BUDGET_WITHHOLDING_BIZ_TYPE,
                'biz_value'     => $budget_withholding_info->id,
                'staff_id'      => $budget_withholding_info->apply_id,
                'approval_id'   => $user['id'],
                'status'        => Enums::WF_STATE_REJECTED,
                'approval_time' => $now_time,
                'created_at'    => $now_time,
                'updated_at'    => $now_time,
            ];
            $bool = $by_workflow_audit_log->i_create($reject_log);
            if ($bool === false) {
                throw new BusinessException('预算管理-预提审核-驳回-记录by审批日志失败: 待处理数据: ' . json_encode($reject_log,
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log),
                    ErrCode::$BUSINESS_ERROR);
            }

            //同时释放当前预提单占用的预算
            $this->freedBudget($budget_withholding_info, $user);

            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('budget-withholding-audit-reject failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $bool ?? false,
        ];
    }

    /**
     * 释放预算
     * @param object $budget_withholding_info 费用预提单信息
     * @param array $user 当前登陆者信息
     * @param int $free_amount 预算释放金额值
     * @return bool
     * @throws ValidationException
     */
    public function freedBudget(object $budget_withholding_info, array $user, int $free_amount = 0)
    {
        // 验证默认国家是否开启预算(0:关闭 1:打开)
        if (!(new EnumsService())->getBudgetStatus()) {
            return true;
        }

        $budget_info = BudgetObjectRepository::getInstance()->getOneById($budget_withholding_info->budget_id, 2);
        $freedAmount[$budget_info->level_code] = $free_amount;

        $budgetService = new BudgetService();
        $result = $budgetService->re_back_budget($budget_withholding_info->no, $user, BudgetService::ORDER_TYPE_5, $freedAmount);
        $this->logger->info('freed_budget  释放预算判断 params ' . json_encode([
                'no'           => $budget_withholding_info->no,
                'user'         => $user,
                'order_type'   => BudgetService::ORDER_TYPE_5,
                'freed_amount' => $freedAmount,
        ]) . ' results ' . json_encode([$result], JSON_UNESCAPED_UNICODE));
        if ($result['code'] != ErrCode::$SUCCESS) {
            throw new ValidationException($result['message'], ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 发送预提单超时未关单提醒 - 邮件
     * @param array $emails 邮件组
     * @param array $budget_set 预算科目预提超时天数配置
     * @return string
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function sendRemind(array $emails, array $budget_set)
    {
        //获取状态为已通过
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('no, ownership_period, cost_department_name, budget_id, provision_amount, use_amount, approved_at');
        $builder->from(BudgetWithholdingModel::class);
        $builder->where('status = :status:', ['status' =>  Enums::WF_STATE_APPROVED]);
        $items = $builder->getQuery()->execute()->toArray();

        //并且当前日期-审核通过日期>配置的科目超时未关单天数
        $excel_data = [];
        $budget_list = BudgetObjectRepository::getInstance()->getDataByIds(array_values(array_unique(array_column($items, 'budget_id'))));
        $budget_list = array_column($budget_list, 'name_cn', 'id');
        foreach ($items as $item) {
            $diff_day = floor((time() - strtotime($item['approved_at'])) / 86400);
            if (isset($budget_set[$item['budget_id']]) && $diff_day > $budget_set[$item['budget_id']]) {
                $excel_data[] = [
                    $item['no'],                                             //预提单号
                    $item['ownership_period'],                               //预算期间
                    $item['cost_department_name'],                           //费用部门
                    $budget_list[$item['budget_id']] ?? '',                  //预提科目
                    bcdiv($item['provision_amount'], 1000, 2),   //预提金额
                    bcdiv($item['use_amount'], 1000, 2),         //使用金额
                    date('Y-m-d', strtotime($item['approved_at'])),   //审批通过日期
                ];
            }
        }

        if (empty($excel_data)) {
            return '暂无需要提醒的预提单';
        }

        //有需要提醒的预提单-发送邮件
        $header = ['预提单号', '预算期间', '费用部门', '预提科目', '预提金额', '使用金额', '审批通过日期'];
        $file_name = get_country_code() . '-' . '超时预提单' . '-' . date('Ymd');
        $oss_upload_res = $this->exportExcel($header, $excel_data, $file_name);
        $oss_file_url = $oss_upload_res['data'];
        //邮件标题
        $title = '预提单超时未关单提醒';
        //邮件内容
        $content = '附件中的预提单超时未关闭，请核对是否已完成支付并执行关单操作！';
        $html = <<<EOF
    {$content}<a href="{$oss_file_url}" target='_blank'>超时预提单.xlsx</a><br/>
EOF;
        //发送邮件
        $send_res = $this->mailer->sendAsync($emails, $title, $html);
        return '邮件发送【' . ($send_res ? '成功' : '失败') . '】! ' . json_encode(['emails' => $emails, 'title' => $title, 'html' => $html], JSON_UNESCAPED_UNICODE);
    }

    /**
     * 普通付款 - 关联预提单 - 变更预提单使用金额、使用状态、预提单-费用明细-按照成本中心变更使用金额
     * @param object $ordinary_payment 普通付款单据
     * @param object $budget_withholding_info 关联预提单对象
     * @param string $type 金额增加 incr、减少 decr
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateAmount(object $ordinary_payment, object $budget_withholding_info, string $type = 'incr')
    {
        $now = date('Y-m-d H:i:s');

        //当使用详情发生变化时更新使用状态的值，等于使用详情中的含税金额总计的合计-需要统一转化为本位币，汇率使用预算占用的汇率
        $use_amount = EnumsService::getInstance()->amountExchangeRateCalculation($ordinary_payment->amount_total_have_tax, $ordinary_payment->exchange_rate, 2);
        $use_amount = bcmul($use_amount, 1000);

        //当使用详情发生变化时更新使用状态的值，如果使用详情中数据量为0则为未使用，如果使用详情中数量大于0则为已使用
        $use_count = $this->getUseDetailCount(['id' => $ordinary_payment->budget_withholding_id]);

        //变更预提单据信息
        $budget_withholding_info->use_amount = $type == 'incr' ? $budget_withholding_info->use_amount + $use_amount : $budget_withholding_info->use_amount - $use_amount;
        $budget_withholding_info->use_status = $use_count > 0 ? BudgetWithholdingEnums::USE_STATUS_USED : BudgetWithholdingEnums::USE_STATUS_UNUSED;
        $budget_withholding_info->updated_at = $now;
        $bool = $budget_withholding_info->save();
        if ($bool === false) {
            throw new BusinessException('预算管理-费用预提单据-变更使用金额、状态失败: 待处理数据: ' . json_encode(['use_amount' => $budget_withholding_info->use_amount, 'use_status' => $budget_withholding_info->use_status],
                    JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($budget_withholding_info),
                ErrCode::$BUSINESS_ERROR);
        }

        //当使用详情发生变化时更新使用状态的值，等于对应的普通付款明细行中的成本中心等于预提明细中的成本中心的普通付款明细行中含税金额的合计，需要统一转化为本位币，汇率使用预算占用的汇率
        $budget_withholding_detail = $budget_withholding_info->getDetails();

        //无明细行-直接返回
        if (!$budget_withholding_detail->toArray()) {
            return true;
        }

        //有明细行-按照成本中心比对
        $cost_center_code_group  = [];
        $ordinary_payment_detail = $ordinary_payment->getDetails()->toArray();
        foreach ($ordinary_payment_detail as $detail) {
            //成本中心空，跳过不处理
            if (empty($detail['cost_center_name'])) {
                continue;
            }
            $amount_have_tax = EnumsService::getInstance()->amountExchangeRateCalculation($detail['amount_have_tax'], $ordinary_payment->exchange_rate, 2);
            $amount_have_tax = bcmul($amount_have_tax, 1000);
            if (isset($cost_center_code_group[$detail['cost_center_name']])) {
                continue;
            }
            $cost_center_code_group[$detail['cost_center_name']] = $amount_have_tax;
        }

        //普通付款明细行-成本中心都是空的，不需要处理预提单费用明细
        if (!$cost_center_code_group) {
            return true;
        }

        foreach ($budget_withholding_detail as $item) {
            if (!isset($cost_center_code_group[$item->cost_center_code])) {
                continue;
            }
            $item->use_amount = $type == 'incr' ? $item->use_amount + $cost_center_code_group[$item->cost_center_code] : $item->use_amount - $cost_center_code_group[$item->cost_center_code];
            $item->updated_at = $now;
            $bool             = $item->save();
            if ($bool === false) {
                throw new BusinessException('预算管理-费用预提单据-变更费用明细-使用金额、状态失败: 待处理数据: ' . json_encode(['id' => $item->id, 'use_amount' => $item->use_amount,],
                        JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($item),
                    ErrCode::$BUSINESS_ERROR);
            }
        }

        return true;
    }

    /**
     * 获取预算科目对应的核算科目
     * @param array $list 列表
     * @return array
     */
    public function getBorrowerLedgerAccount(array $list)
    {
        return LedgerAccountRepository::getInstance()->getBudgetLedgerAccount(array_column($list, 'budget_id'));
    }

    /**
     * 借方成本中心
     * @param object $budget_withholding_info 预提单信息
     * @param array $budget_withholding_department_virtual_cost_center 每个费用部门配置一个虚拟成本中心
     * @return mixed|string
     */
    public function getBorrowerCostCenterCode(object $budget_withholding_info, array $budget_withholding_department_virtual_cost_center)
    {
        $borrower_cost_center_code = '';
        if ($budget_withholding_info->cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_01) {
            //当费用所属网点/总部为总部时取费用部门中的成本中心
            $borrower_cost_center_code = StoreRentingAddService::getInstance()->getPcCodeByDepartmentId($budget_withholding_info->cost_department_id);
        } else if ($budget_withholding_info->cost_store_type == Enums::PAYMENT_COST_STORE_TYPE_02) {
            //当费用所属网点/总部为网点时根据费用部门获取配置中的虚拟成本中心 - 每个部门配置一个虚拟成本中心
            $borrower_cost_center_code = $budget_withholding_department_virtual_cost_center[$budget_withholding_info->cost_department_id] ?? '';
        }
        return $borrower_cost_center_code;
    }

    /**
     * 获取预提单传输sap的头信息
     * @param integer $offset 分页偏移量
     * @param array $params 参数组
     * @return mixed
     */
    public function getBudgetWithholdingSapList(int $offset, array $params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(BudgetWithholdingModel::class);
        //0未同步1已同步2同步失败3同步重试
        if (!empty($params['sync_sap'])) {
            $builder->inWhere('sync_sap', $params['sync_sap']);
        }

        //关闭时红冲凭证：0未同步1已同步2同步失败3同步重试
        if (!empty($params['red_voucher_sync_sap'])) {
            $builder->inWhere('red_voucher_sync_sap', $params['red_voucher_sync_sap']);
        }

        //预提状态：1-待审核、2-已驳回、3-已通过、 4-已撤回、5-已关闭
        if (!empty($params['status'])) {
            $builder->andWhere('status = :status:', ['status' => $params['status']]);
        }

        //关闭时是否抛红冲凭证：0否，1是
        if (!empty($params['is_send_red_voucher'])) {
            $builder->andWhere('is_send_red_voucher = :is_send_red_voucher:', ['is_send_red_voucher' => $params['is_send_red_voucher']]);
        }

        //费用所属公司
        if (!empty($params['cost_company_id'])) {
            $builder->inWhere('cost_company_id', $params['cost_company_id']);
        }

        $builder->limit(100, $offset);
        return $builder->getQuery()->execute();
    }

    /**
     * 预提单-对接SAP
     * @param array $base_data
     * @return mixed
     */
    public function sendToSap(array $base_data)
    {
        $main_model                                        = $base_data['budget_withholding_info'];                          //预提单信息
        $posting_date                                      = $base_data['posting_date'];                                     //关账日期
        $current_date                                      = date('Y-m-d');                                                  //当前日期
        $budget_withholding_ledger_account                 = $base_data['budget_withholding_ledger_account'];                //系统配置中增加暂估科目
        $budget_withholding_department_virtual_cost_center = $base_data['budget_withholding_department_virtual_cost_center'];//每个费用部门配置一个虚拟成本中心
        $department_list                                   = $base_data['department_list'];                                  //获取费用公司信息
        $ledger_account_list                               = $base_data['ledger_account_list'];                              //借方预算科目对应的核算科目

        //预提金额
        $provision_amount = bcdiv($main_model->provision_amount, 1000, 2);

        //借方成本中心
        $borrower_cost_center_code = BudgetWithholdingService::getInstance()->getBorrowerCostCenterCode($main_model, $budget_withholding_department_virtual_cost_center);

        //请求参数
        $request_data = [
            'cost_company_id' => !empty($department_list[$main_model->cost_company_id]) ? $department_list[$main_model->cost_company_id]['sap_company_id'] : 'FEX01',
            'apply_no'        => $main_model->no,
            'approved_at'     => $current_date < $posting_date ? date('Y-m-t', strtotime("{$main_model->ownership_period}-01")) : date('Y-m-d', strtotime($main_model->approved_at)),
            'currency'        => self::$t->_(GlobalEnums::$currency_item[$main_model->currency]),
            'borrower'        => [],//借方
            'lender'          => [//贷方
                'ledger_account_name' => $budget_withholding_ledger_account,
                'voucher_description' => $main_model->no,
                'provision_amount'    => $provision_amount,
                'cost_center_code'    => '',
            ],
        ];

        $details = $main_model->getDetails()->toArray();
        //有明细行
        if ($details) {
            //防止因明细行分批次发送失败，重试
            $amount_detail = [];
            foreach ($details as $item) {
                if ($item['accounting_entry_id']) {
                    continue;
                }
                $amount_detail[] = $item;
            }

            //明细行都抛完毕了，主表状态变更即可
            if (empty($amount_detail)) {
                //0未同步1已同步2同步失败3同步重试
                $main_model->sync_sap = 1;
            } else {
                //按照每450行金额详情分组进行抛数
                $amount_detail_child       = array_chunk($amount_detail, 450);
                $amount_detail_child_count = count($amount_detail_child);
                for ($i = 0; $i < $amount_detail_child_count; $i++) {
                    $borrower                        = [];
                    $borrower_provision_amount_total = 0;
                    foreach ($amount_detail_child[$i] as $detail) {
                        $provision_amount = bcdiv($detail['provision_amount'], 1000, 2);
                        $borrower[]                      = [
                            'ledger_account_name' => $ledger_account_list[$main_model->budget_id] ?? '',
                            'voucher_description' => $main_model->no,
                            'provision_amount'    => $provision_amount,
                            'cost_center_code'    => $detail['cost_center_code'],
                        ];
                        $borrower_provision_amount_total = bcadd($borrower_provision_amount_total, $provision_amount, 2);
                    }

                    $request_data['borrower']                   = $borrower;
                    $request_data['lender']['provision_amount'] = $borrower_provision_amount_total;

                    $send_result = $this->sendGeneralLedgerVoucherToSap($request_data, $amount_detail_child[$i]);
                    if ($i == $amount_detail_child_count - 1) {
                        //0未同步1已同步2同步失败3同步重试
                        $main_model->sync_sap = $send_result ? 1 : 2;
                    }
                }
            }
        } else {
            //无费用明细
            $request_data['borrower'] = [
                [
                    'ledger_account_name' => $ledger_account_list[$main_model->budget_id] ?? '',
                    'voucher_description' => $main_model->no,
                    'provision_amount'    => $provision_amount,
                    'cost_center_code'    => $borrower_cost_center_code,
                ],
            ];
            $send_result              = $this->sendGeneralLedgerVoucherToSap($request_data);
            //0未同步1已同步2同步失败3同步重试
            $main_model->sync_sap     = $send_result ? 1 : 2;
        }
        return $main_model;
    }

    /**
     * 抛预提单-sap总账凭证/红冲总账凭证
     * @param array $request_data sap 请求参数组
     * @param array $detail 费用明细组
     * @param int $request_sap_type 14预提单总账凭证、15预提单红冲凭证
     * @return bool
     */
    private function sendGeneralLedgerVoucherToSap(array $request_data, array $detail = [], int $request_sap_type = 14)
    {
        $detail_z = 0;
        while (empty($return_data) && $detail_z < 1) {
            $detail_z++;
            $return_data = SapService::getInstance()->budget_withholding_general_ledger_voucher_to_sap($request_data, $request_sap_type);
            if (isset($return_data['ID']) && !empty($return_data['ID'])) {
                if ($detail) {
                    $detail_ids = implode(',', array_column($detail, 'id'));
                    $field      = $request_sap_type == 14 ? 'accounting_entry_id' : 'red_voucher_accounting_entry_id';
                    $db         = $this->getDI()->get('db_oa');
                    $db->updateAsDict(
                        (new BudgetWithholdingDetailModel())->getSource(),
                        [
                            $field       => $return_data['ID'],
                            'updated_at' => date('Y-m-d H:i:s'),
                        ],
                        ['conditions' => "id IN ($detail_ids)"]
                    );
                }
                return true;
            } else {
                return false;
            }
            sleep(2);
        }
    }

    /**
     * 关闭预提单 - 红冲凭证 -对接SAP
     * @param array $base_data
     * @return mixed
     */
    public function sendRedVoucherToSap(array $base_data)
    {
        $main_model                                        = $base_data['budget_withholding_info'];                          //预提单信息
        $budget_withholding_ledger_account                 = $base_data['budget_withholding_ledger_account'];                //系统配置中增加暂估科目
        $budget_withholding_department_virtual_cost_center = $base_data['budget_withholding_department_virtual_cost_center'];//每个费用部门配置一个虚拟成本中心
        $department_list                                   = $base_data['department_list'];                                  //获取费用公司信息
        $ledger_account_list                               = $base_data['ledger_account_list'];                              //借方预算科目对应的核算科目

        //预提金额-使用金额的相反数
        $remain_amount = bcdiv(($main_model->provision_amount - $main_model->use_amount), 1000, 2);
        $remain_amount = bcsub(0, $remain_amount, 2);

        //借方成本中心
        $borrower_cost_center_code = BudgetWithholdingService::getInstance()->getBorrowerCostCenterCode($main_model, $budget_withholding_department_virtual_cost_center);

        //请求参数
        $request_data = [
            'cost_company_id' => !empty($department_list[$main_model->cost_company_id]) ? $department_list[$main_model->cost_company_id]['sap_company_id'] : 'FEX01',
            'apply_no'        => $main_model->no,
            'approved_at'     => date('Y-m-d', strtotime($main_model->closed_at)),
            'currency'        => self::$t->_(GlobalEnums::$currency_item[$main_model->currency]),
            'borrower'        => [],//借方
            'lender'          => [//贷方
                'ledger_account_name' => $budget_withholding_ledger_account,
                'voucher_description' => $main_model->no,
                'provision_amount'    => $remain_amount,
                'cost_center_code'    => '',
            ],
        ];

        $details = $main_model->getDetails()->toArray();
        //有明细行
        if ($details) {
            //防止因明细行分批次发送失败，重试
            $amount_detail = [];
            foreach ($details as $item) {
                //需要抛的
                if ($item['use_amount'] < $item['provision_amount'] && empty($item['red_voucher_accounting_entry_id'])) {
                    $amount_detail[] = $item;
                }
            }

            //明细行都抛完毕了，主表状态变更即可
            if (empty($amount_detail)) {
                //0未同步1已同步2同步失败3同步重试
                $main_model->red_voucher_sync_sap = 1;
            } else {
                //按照每450行金额详情分组进行抛数
                $amount_detail_child       = array_chunk($amount_detail, 450);
                $amount_detail_child_count = count($amount_detail_child);
                for ($i = 0; $i < $amount_detail_child_count; $i++) {
                    $borrower                        = [];
                    $borrower_remain_amount_total = 0;
                    foreach ($amount_detail_child[$i] as $detail) {
                        //预提金额-使用金额的相反数
                        $remain_amount = bcdiv(($detail['provision_amount'] - $detail['use_amount']), 1000, 2);
                        $remain_amount = bcsub(0, $remain_amount, 2);

                        $borrower[]                      = [
                            'ledger_account_name' => $ledger_account_list[$main_model->budget_id] ?? '',
                            'voucher_description' => $main_model->no,
                            'provision_amount'    => $remain_amount,
                            'cost_center_code'    => $detail['cost_center_code'],
                        ];
                        $borrower_remain_amount_total = bcadd($borrower_remain_amount_total, $remain_amount, 2);
                    }

                    $request_data['borrower']                   = $borrower;
                    $request_data['lender']['provision_amount'] = $borrower_remain_amount_total;

                    $send_result = $this->sendGeneralLedgerVoucherToSap($request_data, $amount_detail_child[$i], 15);
                    if ($i == $amount_detail_child_count - 1) {
                        //0未同步1已同步2同步失败3同步重试
                        $main_model->red_voucher_sync_sap = $send_result ? 1 : 2;
                    }
                }
            }
        } else {
            //无费用明细
            $request_data['borrower'] = [
                [
                    'ledger_account_name' => $ledger_account_list[$main_model->budget_id] ?? '',
                    'voucher_description' => $main_model->no,
                    'provision_amount'    => $remain_amount,
                    'cost_center_code'    => $borrower_cost_center_code,
                ],
            ];
            $send_result              = $this->sendGeneralLedgerVoucherToSap($request_data, [], 15);
            //0未同步1已同步2同步失败3同步重试
            $main_model->red_voucher_sync_sap     = $send_result ? 1 : 2;
        }
        return $main_model;
    }
}


