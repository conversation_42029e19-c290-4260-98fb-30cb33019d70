<?php

namespace App\Modules\Budget\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Budget\Models\BudgetDepartmentRelateModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetSourceDataModel;
use App\Modules\Budget\Models\BudgetSourceMainModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Workflow\Models\WorkflowAuditLogModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Util\RedisKey;

class BudgetSourceDataService extends BaseService
{
    const IS_NO_EFFECTIVE = 1;//未生效
    const IS_EFFECTIVE = 2;//已生效

    protected static $is_effective_arr =[
        self::IS_NO_EFFECTIVE => 'budget_is_no_effective',
        self::IS_EFFECTIVE => 'budget_is_effective',
    ];

    protected $base_source_data_id = 0;

    private static $instance;

    public static $excel_department_params = [];

    /**
     * @return BudgetEditService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取导入的记录列表
     * @param array $params
     * @param bool $is_download
     * @return mixed
     */
    public function getList(array $params, bool $is_download = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $total = 0;

        try {
            $page_size = $params['page_size'] ?? 20;
            $page_num = $params['page'] ?? 1;
            $offset = $page_size * ($page_num - 1);

            $creator = $params['creator'] ?? '';
            $start_date = $params['start_date'] ?? '';
            $end_date = $params['end_date'] ?? '';
            $object_code = $params['object_code'] ?? '';
            $department_id = $params['department_id'] ?? '';
            $organization_type = $params['organization_type'] ?? '';
            $budget_year = $params['budget_year'] ?? '';
            $apply_status = $params['approved_status']??'';
            $effective_status = $params['status']??'';

            // 导入人
            $creator_ids = [];
            if (!empty($creator)) {
                $creator_ids = HrStaffInfoModel::find([
                    'conditions' => '(staff_info_id LIKE :creator: OR name LIKE :creator:) AND is_sub_staff = 0',
                    'bind' => ['creator' => "%$creator%"],
                    'columns' => ['staff_info_id']
                ])->toArray();
                $creator_ids = !empty($creator_ids) ? array_column($creator_ids, 'staff_info_id') : [];
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(BudgetSourceDataModel::class);

            if(!empty($creator_ids)){
                $builder->inWhere('creator_id', $creator_ids);
            }

            if(!empty($start_date)){
                $builder->andWhere('create_date >= :start_create_date:', ['start_create_date' => $start_date]);
            }

            if(!empty($end_date)){
                $builder->andWhere('create_date <= :end_create_date:', ['end_create_date' => $end_date]);
            }

            if(!empty($object_code)){
                $builder->andWhere('budget_object_code = :budget_object_code:', ['budget_object_code' => $object_code]);
            }

            if(!empty($department_id)){
                $department_ids[] = $department_id;

                // 如果是二级 或 以下子部门, 则需向上查询其一级/公司的共用预算
                $dept_info = SysDepartmentModel::findFirst($department_id);
                if (in_array($dept_info->type, [2,3]) && $dept_info->level != 1 && !empty($dept_info->ancestry_v3)) {
                    $parent_dept_ids = DepartmentModel::getParentIdsByDepartmentId($dept_info->ancestry_v3);
                    $parent_dept_ids = $parent_dept_ids ? array_column($parent_dept_ids, 'id') : [];
                    $department_ids = array_merge($department_ids, $parent_dept_ids);

                    $builder->andWhere('amount_type = :amount_type:', ['amount_type' => 2]);
                }

                $builder->inWhere('budget_department_id', array_values(array_unique($department_ids)));
            }

            if(!empty($budget_year)){
                $builder->andWhere('budget_year = :budget_year:', ['budget_year' => $budget_year]);
            }

            if(!empty($organization_type)){
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $organization_type]);
            }

            if(!empty($apply_status)){
                $builder->andWhere('approved_status = :approved_status:', ['approved_status' => $apply_status]);

            }

            if(!empty($effective_status)){
                $builder->andWhere('status = :effective_status:', ['effective_status' => $effective_status]);

            }

            $builder->columns([
                'id',
                'creator_id',
                'create_date',
                'budget_object_code',
                'budget_department_id',
                'organization_type',
                'amount_type',
                'budget_year',
                'jan_amount',
                'feb_amount',
                'mar_amount',
                'apr_amount',
                'may_amount',
                'jun_amount',
                'jul_amount',
                'aug_amount',
                'sep_amount',
                'oct_amount',
                'nov_amount',
                'dec_amount',
                'main_id',
                'approved_status',
                'status'
            ]);

            $total = $builder->getQuery()->execute()->count();
            $builder->orderBy('create_time DESC');

            if (!$is_download) {
                $builder->limit($page_size, $offset);
            }
            if ($total) {
                $data = $builder->getQuery()->execute()->toArray();
                $data = $this->handleData($data, $creator_ids);
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('预算导入 - 获取记录列表异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'total' => $total,
                'list' => $data
            ]
        ];


    }

    /**
     * 处理导入数据格式
     * @param array $handle_data
     * @param array $creator_ids
     * @return mixed
     */
    protected function handleData(array $handle_data = [], array $creator_ids = [])
    {
        if (empty($handle_data)) {
            return [];
        }

        $is_multiple_data = true;
        if (count($handle_data) == count($handle_data,1)) {
            $data[] = $handle_data;

            $is_multiple_data = false;
        } else {
            $data = $handle_data;
        }

        // 提取员工姓名
        if (empty($creator_ids)) {
            $creator_ids = array_unique(array_column($data, 'creator_id'));
        }

        $staff_item = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id IN ({ids:array}) AND is_sub_staff = 0',
            'bind' => ['ids' => array_values($creator_ids)],
            'columns' => ['staff_info_id', 'name']
        ])->toArray();
        $staff_item = $staff_item ? array_column($staff_item, 'name', 'staff_info_id') : [];

        // 提取员工部门/公司名称
        $sys_department = SysDepartmentModel::find(['columns' => ['id', 'name']])->toArray();
        $sys_department = array_column($sys_department, 'name', 'id');

        // 提取预算科目名称
        $object_item = (new BudgetService())->getAllObjectList();
        $object_item = $object_item ? array_column($object_item, 'name', 'level_code') : [];

        $t = self::$t;
        foreach ($data as $key => $val) {
            $val['status_text'] = static::$t->_( self::$is_effective_arr[$val['status']??'2']);
            $val['approved_status_text'] = static::$t->_(Enums::$payment_apply_status[$val['approved_status']]);

            $val['budget_department_name'] = $sys_department[$val['budget_department_id']] ?? '';
            $val['creator_name'] = $staff_item[$val['creator_id']] ?? '';
            $val['budget_object_name'] = $object_item[$val['budget_object_code']] ?? '';

            switch ($val['amount_type']) {
                case 1:
                    $val['amount_type'] = trim($t['budget_amount_type.1']);// '否'
                    break;
                case 2:
                    $val['amount_type'] = trim($t['budget_amount_type.2']);// '是'
                    break;
                default:
                    $val['amount_type'] = '';
            }

            switch ($val['organization_type']) {
                case 1:
                    $val['organization_type'] = trim($t['budget_organization_type.1']);// '网点'
                    break;
                case 2:
                    $val['organization_type'] = trim($t['budget_organization_type.2']);// '总部'
                    break;
                default:
                    $val['organization_type'] = '';
            }

            $val['jan_amount'] = bcdiv($val['jan_amount'],1000,3);
            $val['feb_amount'] = bcdiv($val['feb_amount'],1000,3);
            $val['mar_amount'] = bcdiv($val['mar_amount'],1000,3);
            $val['apr_amount'] = bcdiv($val['apr_amount'],1000,3);
            $val['may_amount'] = bcdiv($val['may_amount'],1000,3);
            $val['jun_amount'] = bcdiv($val['jun_amount'],1000,3);
            $val['jul_amount'] = bcdiv($val['jul_amount'],1000,3);
            $val['aug_amount'] = bcdiv($val['aug_amount'],1000,3);
            $val['sep_amount'] = bcdiv($val['sep_amount'],1000,3);
            $val['oct_amount'] = bcdiv($val['oct_amount'],1000,3);
            $val['nov_amount'] = bcdiv($val['nov_amount'],1000,3);
            $val['dec_amount'] = bcdiv($val['dec_amount'],1000,3);

            $data[$key] = $val;
        }

        if (!$is_multiple_data) {
            return $data[0];
        }

        return $data;
    }

    /**
     * 获取单个导入记录详情
     * @param int $id
     * @return mixed
     */
    public function getDetail(int $id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $info = BudgetSourceDataModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
                'columns' => [
                    'id',
                    'creator_id',
                    'budget_object_code',
                    'budget_department_id',
                    'organization_type',
                    'amount_type',
                    'budget_year',
                    'jan_amount',
                    'feb_amount',
                    'mar_amount',
                    'apr_amount',
                    'may_amount',
                    'jun_amount',
                    'jul_amount',
                    'aug_amount',
                    'sep_amount',
                    'oct_amount',
                    'nov_amount',
                    'dec_amount',
                ]
            ]);
            if (!empty($info)) {
                $data = $this->handleData($info->toArray());
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('预算导入记录详情异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 新增预算导入
     * @param array $params
     * @param array $user_info
     * @return mixed
     */
    public function addBudget(array $params, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $exist_info = BudgetSourceDataModel::findFirst($params['id']);
            if (empty($exist_info)) {
                throw new ValidationException("导入的原始预算数据不存在[id={$params['id']}]", ErrCode::$BUSINESS_ERROR);
            }

            // 预算导入, 使用中文匹配进行校验
            self::setLanguage('zh-CN');

            $exist_info = $this->handleData($exist_info->toArray());

            // 补全数据 - 同导入的结构
            $form_data[] = [
                $exist_info['budget_object_name'],
                $exist_info['budget_department_name'],
                $exist_info['organization_type'],
                $exist_info['amount_type'],
                $params['jan_amount'],
                $params['feb_amount'],
                $params['mar_amount'],
                $params['apr_amount'],
                $params['may_amount'],
                $params['jun_amount'],
                $params['jul_amount'],
                $params['aug_amount'],
                $params['sep_amount'],
                $params['oct_amount'],
                $params['nov_amount'],
                $params['dec_amount'],
            ];

            $budget_year = $exist_info['budget_year'];
            $this->base_source_data_id = $exist_info['id'];
            $check_res = $this->sourceDataCheck($budget_year, $form_data, false, $user_info, 2);
            if ($check_res['code'] != ErrCode::$SUCCESS) {
                throw new ValidationException($check_res['message'], $check_res['code']);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('预算导入 - 复制新增异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $check_res['data'] ?? []
        ];

    }

    /**
     * 下载导入记录
     * @param array $params
     * @param array $user_info
     * @return mixed
     */
    public function dataExport(array $params, array $user_info = [])
    {
        // 预算导出，导出中文即可
        self::setLanguage('zh-CN');

        $data = $this->getList($params, true);

        $data = $data['data']['list'] ?? [];
        $new_data = [];
        if (!empty($data)) {
            foreach ($data as $val ) {
                $new_data[] = [
                    $val['creator_id'],
                    $val['creator_name'],
                    $val['create_date'],
                    $val['budget_object_name'],
                    $val['budget_department_name'],
                    $val['organization_type'],
                    $val['amount_type'],
                    $val['budget_year'],
                    $val['jan_amount'],
                    $val['feb_amount'],
                    $val['mar_amount'],
                    $val['apr_amount'],
                    $val['may_amount'],
                    $val['jun_amount'],
                    $val['jul_amount'],
                    $val['aug_amount'],
                    $val['sep_amount'],
                    $val['oct_amount'],
                    $val['nov_amount'],
                    $val['dec_amount'],
                ];
            }
        }

        $header = [
            '导入人工号',
            '导入人姓名',
            '导入日期',
            '预算科目',
            '公司／部门',
            '组织机构 ',
            '是否共用 ',
            '预算年份',
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
        ];

        $file_name = "BudgetImportData_Download_" . date("YmdHis");

        $result = $this->exportExcel($header, $new_data, $file_name);
        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
            $result['data'] = $result['data'];
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }

        return $result;
    }

    /**
     * 预算导入数据检测
     * 说明: 不导入 只验证数据  返回 原装excel 增加一列 错误表示
     * @param array $data
     * @param string $year
     * @param bool $is_insert
     *
     * @return mixed
     */
    public function check(array $data, string $year, bool $is_insert = false)
    {

        $error_num = $data['error_num'];//错误数据
        $obj_data = $data['obj_data'];
        $all_dep = $data['all_dep'];
        $data = $data['data'];

        // 验证额度变化  是否比 占用额度小

        // 导入预算关联的部门id
        $departments = array_values($all_dep);

        // 关联部门已有的预算额度
        $all_amount = array();
        if (!empty($departments)) {
            $month_start = $year.'-01';
            $month_end = $year.'-12';
            $all_amount = BudgetObjectDepartmentAmount::find([
                "conditions" => "department_id in ({departments:array}) and month >= :month_start: and month <= :month_end: and is_delete=0",
                "bind"=>["departments"=>$departments,"month_start" => $month_start,"month_end" => $month_end],
            ])->toArray();
        }
        // 第一次或者 新增部门 表里没数据 不需要验证
        if (empty($all_amount) && $error_num == 0) {
            //校验金额不能为0
            foreach ($data as &$da) {
                if (!empty($da['error'])) {
                    continue;
                }
                $is_error = false;

                for ($i = 1; $i < 13; $i++) {
                    $month = $year . '-' . sprintf("%02d", $i);

                    if ($da[$month] < 0) {
                        $da['error'] .= "{$month} 预算金额小于0无法导入，请检查！｜";
                        if (!$is_error) {
                            $error_num++;
                            $is_error = true;
                        }
                    }
                }
            }

            if ($is_insert) {
                if ($error_num > 0) {
                    $res['error_num'] = $error_num;
                    $res['data']      = $data;
                    return array('code' => -1, 'message' => '数据验证存在问题', 'data' => $res);
                }
                $return['delete']    = $return['update'] = array();
                $return['error_num'] = $error_num;
                return array('code' => 1, 'message' => 'success', 'data' => $return);
            }
            // 不需要入库 返回数据
            $res['error_num'] = $error_num;
            $res['url']       = '';
            // 验证有错误
            if ($error_num > 0) {
                $res['url'] = $this->genImportItemExcel($data);
            }

            return array('code' => 1, 'message' => 'success', 'data' => $res);
        }

        $format_all_amount = array();

        // $need_delete_sub 需删除的当前部门和子部门
        // $need_delete 只删除当前部门
        // $need_update 记录 已经占用额度 用于计算 更新剩余额度
        $need_delete_sub = $need_delete = $need_update = $source_data = array();
        if (!empty($all_amount)) {
            foreach ($all_amount as $al) {
                $k = "{$al['department_id']}_{$al['object_code']}_{$al['organization_type']}";
                $format_all_amount[$k]['amount_type'] = $al['amount_type'];
                $format_all_amount[$k][$al['month']]['use_amount'] = $al['amount'] - $al['amount_left'];
                $format_all_amount[$k][$al['month']]['amount'] = $al['amount'];
                $format_all_amount[$k][$al['month']]['amount_left'] = $al['amount_left'];

            }

            unset($all_amount);

            // 遍历要导入的数据: 基础校验已通过
            foreach ($data as &$da) {
                if (!empty($da['error'])) {
                    continue;
                }

                $is_error = false;
                if ($is_insert) {
                    $obj_code = $da['object_code'];
                    $dep_id = $da['department_id'];
                    $org = $da['organization_type'];
                } else {
                    $obj_code = empty($obj_data[$da['object_code']]) ? '' : $obj_data[$da['object_code']];//转科目code
                    $dep_id = empty($all_dep[$da['department_id']]) ? 0 : $all_dep[$da['department_id']];
                    $org = 0;

                    if ($da['organization_type'] == '总部') {
                        $org = 2;
                    } else if ($da['organization_type'] == '网点') {
                        $org = 1;
                    }
                }

                $dk = "{$dep_id}_{$obj_code}_$org";

                // 库里有数据的情况
                if (!empty($format_all_amount[$dk])) {
                    // 如果 共用类型不一样 占用规则
                    // 1 单独自己部门额度
                    // 2 与子部门共用一级部门/公司预算
                    $amount_type = $da['amount_type'];
                    if ($da['amount_type'] == '是') {
                        $amount_type = 2;
                    } else if ($da['amount_type'] == '否') {
                        $amount_type = 1;
                    }

                    if ($amount_type != $format_all_amount[$dk]['amount_type']) {
                        // 插入操作 需要删除
                        // 验证逻辑 不用
                        if ($is_insert) {
                            // 第一次库里保存 共用: 2
                            // 二次不共用  删除子部门
                            if ($format_all_amount[$dk]['amount_type'] == 2) {
                                $need_delete_sub[$dk] = $dep_id;
                            }
                        }

                        // 第一次库里保存 不共用: 1
                        // 二次 共用 提示错误不能修改 因为我无法知道 该部门子部门 谁已经产生订单
                        if ($format_all_amount[$dk]['amount_type'] == 1) {
                            $da['error'] .= "数据库中存在该部门占用规则为【与子部门共用】无法修改｜";
                            if (!$is_error) {
                                $error_num++;
                                $is_error = true;
                            }
                        }

                    } else {
                        // 共用额度相同 并且 都为 2 与子部门共用顶级部门预算 需要删除子部门 重新入库
                        if ($amount_type == 2 && $format_all_amount[$dk]['amount_type'] == 2) {
                            $need_delete_sub[$dk] = $dep_id;
                        }

                        if ($amount_type == 1 && $format_all_amount[$dk]['amount_type'] == 1) {
                            $need_delete[$dk] = $dep_id;
                        }
                    }

                    // 验证修改后的额度 是否 小于 已经占用的 额度 新逻辑改为金额追加 计算逻辑调整

                    $err = array();
                    $change_err =[];
                    foreach ($format_all_amount[$dk] as $month => $format) {
                        if ($month == 'amount_type') {
                            continue;
                        }


                        // 数据库 budget_object_department_amount 存的记录 计算差值获取已经占用的额度 泰铢*1000
                        $use_amount = floatval($format['use_amount']);
                        $change_amount = floatval(floatval($da[$month]) * 1000 +$format['amount']);
                        //预算金额+文件中的预算金额（每月维度）结果为负数
                        if ($change_amount < 0) {
                            $change_err[] = $month;
                        }

                        // 已经占用的额度 大于 要修改的金额 提示错误
                        if ($use_amount > $change_amount) {
                            $err[] = $month;
                        }

                        // 不为0 说明已经有占用额度 需要记录
                        // 如果占用为0  不需要记录 删了新增
                      //  if ($use_amount != 0) {
                            $up_k = "{$dk}_{$month}";
                            $need_update[$up_k]['use_amount'] = $use_amount;
                            $need_update[$up_k]['amount'] = $change_amount;
                       // }
                    }
                    if(!empty($change_err)){
                        $month = implode(',',$err);
                        $da['error'] .= "{$month} 预算金额不够无法导入，请检查！｜";
                        if (!$is_error) {
                            $error_num++;
                            $is_error = true;

                        }
                    }

                    if (!empty($err)) {
                        $month = implode(',',$err);
                        $da['error'] .= "{$month} 月份额度 已占用 超过修改后金额｜";
                        if (!$is_error) {
                            $error_num++;
                        }
                    }


                    if ($is_insert) {
                        $da['amount_type'] = $amount_type;
                    }
                }
            }
        }

        // 返回需要删除的 重新插入 数据
        if ($is_insert) {
            if ($error_num > 0) {
                $res['error_num'] = $error_num;
                $res['data'] = $data;
                return array('code' => -1,'message' => '数据验证存在问题' ,'data' => $res);
            }

            // 获取 需要删除 共用的 子部门

            // 整合 需要删除子部门的 和 不需要删除子部门的 数据
            $del = array();
            if (!empty($need_delete_sub)) {
                $delete_dep = array_values($need_delete_sub);
                $delete_dep = DepartmentModel::get_sub_department($delete_dep);

                foreach ($need_delete_sub as $k => $v){
                    $row = [];

                    //"{$da['department_id']}_{$da['object_code']}_{$da['organization_type']}";
                    $info = explode('_',$k);
                    $row['department_id'] = array($info[0]);

                    if (!empty($delete_dep[$info[0]])) {
                        $row['department_id'] = array_merge($row['department_id'],$delete_dep[$info[0]]);
                    }

                    $row['object_code'] = $info[1];
                    $row['organization_type'] = $info[2];
                    $del[] = $row;
                }
            }

            if (!empty($need_delete)) {
                foreach ($need_delete as $k => $v) {
                    $info = explode('_',$k);
                    $del[] = [
                        'department_id' => array($info[0]),
                        'object_code' => $info[1],
                        'organization_type' => $info[2],
                    ];
                }
            }

            $return['delete'] = $del;
            $return['update'] = $need_update;
            $return['error_num'] = $error_num;
            return array('code' => 1,'message' => 'success' ,'data' => $return);
        }

        // 不需要入库 返回数据
        $result['error_num'] = $error_num;
        $result['url'] = '';

        // 验证有错误
        if ($error_num > 0) {
            $result['url'] = $this->genImportItemExcel($data);
        }

        return array('code' => 1,'message' => 'success' ,'data' => $result);
    }

    /**
     * 生成预算导入返回的Excel
     * 说明：有错误时会返回
     * @param array $source_item
     * @return mixed
     */
    protected function genImportItemExcel(array $source_item)
    {
        $server = new BudgetService();
        $column = array(
            '预算科目-中文',
            '公司/部门',
            '组织机构',
            '是否共用',
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月',
            '错误信息',
        );
        $r = $server->export_check($source_item ?? [], $column);

        return  $r['data'] ?? '';// url
    }

    /**
     * 导入的预算数据入库
     * @param array $need_delete 待删除的旧数据
     * @param array $format_data 待写入的预算与部门关系数据
     * @param array $amount_insert 待写入部门预算明细数据
     * @param $is_effective
     * @param string $budget_year 导入的预算所属年份
     *
     * @return mixed
     */
    public function writeBudgetData(array $need_delete, array $format_data, array $amount_insert, $is_effective, string $budget_year)
    {
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 入库
            $obj_amount_model = new BudgetObjectDepartmentAmount();
            $obj_model = new BudgetObjectDepartment();

            // 存在需要删除的数据
            if (!empty($need_delete)) {
                // 只删除当前年度 改为预算所属年度 同部门同科目同机构类型的预算
                $year = $budget_year;
                $month_start = $year.'-01';
                $month_end = $year.'-12';
                foreach ($need_delete as $del) {
                    $deps_str = "'".implode("','",$del['department_id'])."'";//多个部门id
                    $object_code = $del['object_code'];
                    $organization_type = $del['organization_type'];
                    $db->updateAsDict(
                        $obj_amount_model->getSource(),
                        ['is_delete'=>1],
                        ['conditions'=>"department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{$month_end}'  and is_delete=0"]

                    );
                    $db->updateAsDict(
                        $obj_model->getSource(),
                        ['is_delete'=>1],
                        ['conditions'=>"department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{$month_end}'  and is_delete=0"]

                    );
                }
            }

            $flag = $obj_model->batch_insert($format_data);
            if (!$flag) {
                $db->rollback();
                return false;
            }

            $flag = $obj_amount_model->batch_insert($amount_insert);
            if (!$flag) {
                $db->rollback();
                return false;
            }
            $db->updateAsDict(
                (new BudgetSourceDataModel)->getSource(),
                [
                    'status'          => 2,
                    'approved_status' => 3
                ],
                ['conditions' => 'main_id=?', 'bind' => $is_effective]
            );

            $db->updateAsDict(
                (new BudgetSourceMainModel)->getSource(),
                [
                    'is_effective'          => 2,
                ],
                ['conditions' => 'id=?', 'bind' => $is_effective]
            );
            $db->commit();

            return true;
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->warning('预算导入/复制新增 - 数据写入异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 预算导入/新增数据校验
     * @param string $year 预算年份
     * @param array $excel_data 预算原数据: Excel / 表单提交
     * @param bool $is_only_check 是否仅做数据检测
     * @param array $user_info 当前操作用户
     * @param int $data_source 数据来源 1-Excel；2-表单提交
     * @param array $attachments 附件
     * @param $is_effective
     * @return mixed
     */
    public function sourceDataCheck(string $year, array $excel_data, bool $is_only_check = true, array $user_info = [], int $data_source = 0, $attachments = [], $is_effective = false)
    {
        ini_set('memory_limit', '1024M');

        try {
            $this->logger->info('budget_source_data_check, data = ' . json_encode($excel_data, JSON_UNESCAPED_UNICODE));
            $this->logger->info('budget_source_data_check_memory_1:' . memory_usage());
            //校验当前 待审批 预算导入
            $main_model = BudgetSourceMainModel::findFirst([
                'apply_status = :status: and year_at = :year_at: and source_type = :source_type:',
                'bind' => ['status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_PENDING, 'year_at' => $year, 'source_type' => self::BUDGET_SOURCE_TYPE_2],
            ]);
            if (!empty($main_model)) {
                return [
                    'code'    => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('budget_import_order_is_pending'),
                    'data'    => []
                ];
            }


            // 提取 excel 科目列 及 科目编码
            $all_min_obj = array_column($excel_data,0);
            $obj_name = array_map('trim',$all_min_obj);
            $obj_name = array_unique($obj_name);
            $obj_name = array_diff($obj_name,array(''));
            $obj_name = array_values($obj_name);
            if (!empty($obj_name)) {
                $budget_obj_data = BudgetObject::find([
                    "conditions" => "name_cn in ({obj_name:array}) and is_end = :is_end: and is_delete = 0",
                    "bind"=>["obj_name"=>$obj_name,"is_end" => 1],
                ])->toArray();
            }

            if (empty($budget_obj_data)) {
                return [
                    'code' => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('confirm_budget_object_name'),
                    'data' => []
                ];
            }
            foreach ($budget_obj_data as $budget_item){
                if(self::IS_NO_BUDGET==$budget_item['is_budget']){
                    return [
                        'code' => ErrCode::$VALIDATE_ERROR,
                        'message' => $budget_item['name_en'].static::$t->_('object_name_not_budget'),
                        'data' => []
                    ];
                }
            }

            $obj_data = array_column($budget_obj_data,'level_code','name_cn');
            $obj_data_code_name = array_column($budget_obj_data,'name_cn','level_code');
            unset($budget_obj_data);

            // 提取 excel 公司/部门列
            $need_dep = array_column($excel_data,1);
            $need_dep = array_map('strtolower',$need_dep);
            $need_dep = array_map('trim',$need_dep);
            $need_dep = array_unique($need_dep);
            $need_dep = array_diff($need_dep,array(''));
            $need_dep = array_values($need_dep);
            $all_dep = $top_dep = $all_dep_source = array();
            //根据excel 部门name获取部门信息
            if (!empty($need_dep)) {
                // fle 库取部门
                $all_dep = DepartmentModel::find([
                    "conditions" => "name in ({need_dep:array}) and deleted = :deleted: ",
                    "bind"=>["need_dep"=>$need_dep,"deleted" => 0],
                    "columns" => "id,LOWER(name) as name,type,level"
                ])->toArray();
                $top_dep = array_column($all_dep,null,'name');
                //数据库所有部门
                $all_dep = array_column($all_dep,'id','name');
            }

            // excel 字段转换 name -> id excel字段处理便于程序处理

            $column = array(
                0 => 'object_code',//最小级code
                1 => 'department_id',//部门id
                2 => 'organization_type',//组织机构
                3 => 'amount_type',//是否共用
                4 => $year.'-01',
                5 => $year.'-02',
                6 => $year.'-03',
                7 => $year.'-04',
                8 => $year.'-05',
                9 => $year.'-06',
                10 => $year.'-07',
                11 => $year.'-08',
                12 => $year.'-09',
                13 => $year.'-10',
                14 => $year.'-11',
                15 => $year.'-12',
            );

            // excel 横向记录 变成列
            $start = $year.'-01';
            $month_arr = array();
            $this->logger->info('budget_source_data_check_memory_2: ' . memory_usage());
            //1-12月 1-4 四个季度
            while ($start <= $year.'-12') {
                $month_int = intval(date('m',strtotime($start)));
                $month_arr[] = [
                    'month' => $start,
                    'season' => intval((($month_int -1) / 3) + 1),//取月份对应季度
                ];

                $start = date('Y-m',strtotime("{$start} +1 month"));
            }

            // 系统所有的部门
            $sys_department = DepartmentModel::find([
                "conditions" => "deleted = 0",
                "columns" => "id,name,type,level,ancestry,ancestry_v3"
            ])->toArray();
            $all_dep_source = array_column($sys_department, null, 'id');

            // 系统已有的预算
            $sys_exist_department_budget_item = $this->getExistDepartmentBudgetList($year, $all_dep_source);

            $need_parent = $need_parent_ids = array();
            $format_data = array();// 入库 budget_object_department数据
            $new_excel_data = array();//excel 整理成 key val
            $error_num = 0;//错误行数
            $all_num = 0;//总记录数
            $is_unique = true;//是否有重复记录
            $check_unique = array();
            $excel_check_item = [];

            // 原数据校验
            // 1. Excel原数据格式初始化
            $organization_type_item = [
                '网点' => 1,
                '总部' => 2
            ];
            $amount_type_item = [
                '否' => 1,
                '是' => 2,
            ];
            $this->logger->info('budget_source_data_check_memory_3: ' . memory_usage());
            foreach ($excel_data as $k => &$v) {
                $v = array_map('trim', $v);
                if (empty($v[0]) && empty($v[1]) && empty($v[2]) && empty($v[3])) {
                    unset($excel_data[$k]);
                    continue;
                }

                $v[1] = strtolower($v[1]);//部门转小写

                $all_num++;

                // 验证是否有重复记录, 重复的跳出
                $unique_k = "$v[0]_$v[1]_$v[2]_$v[3]";
                if (in_array($unique_k, $check_unique)) {
                    $is_unique = false;
                    break;
                }
                $check_unique[] = $unique_k;

                // 异构Excel预算原数据结构, 用于下列校验
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if ($_curr_obj_code && $_curr_dept_info) {
                    $_organization_type = $organization_type_item[$v[2]] ?? '';
                    $excel_check_item[$_curr_obj_code.'_'.$_curr_dept_info['id'].'_'.$_organization_type] = [
                        'type' => $_curr_dept_info['type'],
                        'level' => $_curr_dept_info['level'],
                        'organization_type' => $_organization_type,
                        'amount_type' => $amount_type_item[$v[3]] ?? '',
                    ];
                }
            }
            $this->logger->info('budget_source_data_check_memory_4: ' . memory_usage());
            // Excel行数据重复性校验
            if (!$is_unique) {
                return [
                    'code' => -22,
                    'message' => static::$t->_('excel_data_budget_repeat'),
                    'data' => []
                ];
            }
            unset($check_unique);

            // 2. Excel数据基本校验、Excel数据自身校验、Excel数据 和 表里已有数据校验
            $excel_data = array_values($excel_data);
            // 遍历Start
            foreach ($excel_data as &$v) {
                //单行多个错误 是否需要自增错误行数 标记
                $is_error = false;
                $error_str = '';

                // 没科目 不管
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                if (empty($_curr_obj_code)) {
                    $error_str .= '科目匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 没部门 不管
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if (empty($_curr_dept_info)) {
                    $error_str .= '部门匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
                // c-level  只可以导入不共用预算  公司bu 可以导入共用和不共用预算 共用可改为独占

                // 不能给非公司、非一级部门、非二级部门导入预算
                if ((!isset($_curr_dept_info['level']) || (!in_array($_curr_dept_info['level'], [0, 1, 2]))&&(isset($_curr_dept_info['type']) && !in_array($_curr_dept_info['type'], [4, 5])))) {
                    $error_str .= '非一/二级部门/非公司/非c-level, 不可导入预算|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 共用规则:
                // 共用规则 验证
                if (!in_array($v[3],array('是','否'))) {
                    $error_str .= '共用规则匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 组织机构 非总部 网点
                if (!in_array($v[2], array('总部','网点'))) {
                    $error_str .= '组织机构匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 部门各层级逻辑校验
                $_curr_item = [
                    'obj_code' => $_curr_obj_code,
                    'organization_type' => $organization_type_item[$v[2]] ?? '',
                    'amount_type' => $amount_type_item[$v[3]] ?? '',
                ];


                // 找当前部门的父级部门 和 子部门
                $_curr_parent_and_sub_departments = $this->getParentAndSubDepartmentList($_curr_dept_info, $all_dep_source);

                // 1. Excel数据项自查
                $_curr_item = array_merge($_curr_item, $_curr_dept_info);
                //c-level 不允许导入共用预算
                if ($_curr_item['type'] == 4 && $_curr_item['amount_type'] == 2) {
                    $error_str .= $_curr_item['name'].'c-level 不能导入共用预算| ';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
                $excel_item_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $excel_check_item);

                if (!empty($excel_item_check_res)) {
                    $error_str .= 'Excel 中'. $excel_item_check_res . '也有该科目预算, 请自查|';
                    $error_num++;
                    $is_error = true;
                }

                // 2. 表中已有数据检查
                $exist_budget_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $sys_exist_department_budget_item);
                if (!empty($exist_budget_check_res)) {
                    $error_str .= '已给' . $exist_budget_check_res . '在本年份导入过此科目的预算|';
                    $error_num++;
                    $is_error = true;
                }
                // 非导入 需要验证 需要转换部门
                if (!$is_only_check) {
                    // 提取科目code
                    $v[0] = empty($obj_data[$v[0]]) ? '' : $obj_data[$v[0]];

                    // 提取部门id
                    $v[1] = empty($all_dep[$v[1]]) ? 0 : $all_dep[$v[1]];

                    // 组织机构
                    if ($v[2] == '总部') {
                        $v[2] = 2;
                    } else if($v[2] == '网点') {
                        $v[2] = 1;
                    }

                    // 需要找子部门 占用规则:
                    // 1. 单独自己部门额度
                    // 2. 与子部门共用顶级部门预算 ()
                    if ($v[3] == '是') {
                        $v[3] = 2;
                        $k = "{$v[1]}_{$v[0]}_{$v[2]}";
                        $need_parent[$k] = $v;
                        $need_parent_ids[] = $v[1];
                    } else {
                        $v[3] = 1;
                    }

                    //整理入库数据
                    $row = array();
                    foreach ($month_arr as $m){
                        $row['department_id'] = $v[1];
                        $row['object_level'] = 1;
                        $row['object_code'] = $v[0];
                        $row['organization_type'] = $v[2];
                        $row['month'] = $m['month'];
                        $row['season'] = $m['season'];
                        $format_data[] = $row;
                    }
                }

                $new = array();
                foreach ($v as $index => $f) {
                    if (empty($column[$index])) {
                        continue;
                    }

                    $new[$column[$index]] = $f;
                }
                $new['error'] = empty($error_str) ? '' : $error_str;
                $new_excel_data[] = $new;
            }
            unset($excel_data);
            $this->logger->info('budget_source_data_check_memory_5: ' . memory_usage());
            // Excel数据基本校验: 遍历End

            $check_data['data'] = $new_excel_data;
            $check_data['all_dep'] = $all_dep;
            $check_data['obj_data'] = $obj_data;
            $check_data['error_num'] = $error_num;

            // 只是验证数据 不需要入库
            $check_res = $this->check($check_data, $year,$is_only_check ? false : true);
            $res = $check_res['data'];
            $res['all_num'] = $all_num;
            $res['success_num'] = $all_num - $res['error_num'];
            $res['success_num'] = $res['success_num'] < 0 ? 0 : $res['success_num'];
            if ($check_res['code'] != 1 || $res['error_num'] > 0 || $is_only_check) {
                $_error_str = array_filter(array_unique(array_column($res['data'] ?? [], 'error')));
                $_error_str = $_error_str ? ' : ' . implode(' ', $_error_str) : '';

                return [
                    'code' => $check_res['code'],
                    'message' => $is_only_check ? $check_res['message'] : $check_res['message'] . $_error_str,
                    'data' => $res
                ];
            }

            // 重复导入 可以删除的数据
            $need_delete = empty($check_res['data']['delete']) ? array() : $check_res['data']['delete'];

            // 已经存在占用额度 需要保留 已占用的预算额度
            $need_update = empty($check_res['data']['update']) ? array() : $check_res['data']['update'];

            // 处理 共用部门
            // 如果已经导入过数据 并且 类型没有变化 也需要 删除掉 重新导入
            if (!empty($need_parent)) {
                $need_parent_ids = array_unique($need_parent_ids);
                $department_connect = DepartmentModel::get_sub_department($need_parent_ids);
                foreach ($need_parent as $k => $v) {
                    $exp = explode('_',$k);
                    $par_dep_id = $exp[0];
                    $sub_ids = empty($department_connect[$par_dep_id]) ? array() : $department_connect[$par_dep_id];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    foreach ($sub_ids as $sub) {
                        foreach ($month_arr as $m) {
                            $row = array();
                            $row['department_id'] = $sub;
                            $row['object_level'] = 1;
                            $row['object_code'] = $v[0];
                            $row['organization_type'] = $v[2];
                            $row['month'] = $m['month'];
                            $row['season'] = $m['season'];
                            $format_data[] = $row;
                        }
                    }
                }
            }

            // 处理 最小级code 对应 上级 所有科目
            foreach ($format_data as $f) {
                $codes = $this->split_code($f['object_code']);
                $codes = array_diff($codes,array($f['object_code']));
                if (!empty($codes)) {
                    foreach ($codes as $c) {
                        $f['object_code'] = $c;
                        $f['object_level'] = 1;
                        $format_data[] = $f;
                    }
                }
            }

            $dep_amount = array();
            $log_source_data = [];
            $curr_date = date('Y-m-d');
            foreach ($new_excel_data as $new) {
                // 预算导入的原始数据
                $_dept_info = $all_dep_source[$new['department_id']] ?? [];
                $log_source_data[] = [
                    'creator_id' => $user_info['id'] ?? 0,
                    'creator_name' => $user_info['name'] ?? '',
                    'creator_department_id' => $user_info['department_id'] ?? 0,
                    'creator_department_name' => $user_info['department'] ?? '',
                    'create_date' => $curr_date,
                    'budget_object_name' => $obj_data_code_name[$new['object_code']] ?? '',
                    'budget_object_code' => $new['object_code'],
                    'budget_department_name' => $_dept_info['name'] ?? '',
                    'budget_department_id' => $new['department_id'],
                    'budget_department_type' => $_dept_info['type'] ?? 0,
                    'budget_department_level' => $_dept_info['level'] ?? 0,
                    'organization_type' => $new['organization_type'],
                    'amount_type' => $new['amount_type'],
                    'budget_year' => $year,
                    'jan_amount' => is_numeric($new[$year.'-01']) ? $new[$year.'-01'] * 1000 : 0,
                    'feb_amount' => is_numeric($new[$year.'-02']) ? $new[$year.'-02'] * 1000 : 0,
                    'mar_amount' => is_numeric($new[$year.'-03']) ? $new[$year.'-03'] * 1000 : 0,
                    'apr_amount' => is_numeric($new[$year.'-04']) ? $new[$year.'-04'] * 1000 : 0,
                    'may_amount' => is_numeric($new[$year.'-05']) ? $new[$year.'-05'] * 1000 : 0,
                    'jun_amount' => is_numeric($new[$year.'-06']) ? $new[$year.'-06'] * 1000 : 0,
                    'jul_amount' => is_numeric($new[$year.'-07']) ? $new[$year.'-07'] * 1000 : 0,
                    'aug_amount' => is_numeric($new[$year.'-08']) ? $new[$year.'-08'] * 1000 : 0,
                    'sep_amount' => is_numeric($new[$year.'-09']) ? $new[$year.'-09'] * 1000 : 0,
                    'oct_amount' => is_numeric($new[$year.'-10']) ? $new[$year.'-10'] * 1000 : 0,
                    'nov_amount' => is_numeric($new[$year.'-11']) ? $new[$year.'-11'] * 1000 : 0,
                    'dec_amount' => is_numeric($new[$year.'-12']) ? $new[$year.'-12'] * 1000 : 0,
                    'data_source' => $data_source,
                    'base_source_data_id' => $this->base_source_data_id
                ];


                $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}";

                // 处理子部门
                if (!empty($need_parent[$k])) {
//                    $new['amount_type'] = 1;
                    $sub_ids = empty($department_connect[$new['department_id']]) ? array() : $department_connect[$new['department_id']];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    foreach ($sub_ids as $sub) {
                        $row = array();
                        $row['department_id'] = $sub;
                        $row['object_level'] = 1;
                        $row['object_code'] = $new['object_code'];
                        $row['organization_type'] = $new['organization_type'];
                        $row['amount_type'] = $new['amount_type'];
                        $dep_amount[] = $row;
                    }
                }
            }

            $new_excel_data = array_merge($new_excel_data,$dep_amount);
            $amount_insert = array();
            foreach ($new_excel_data as $new) {
                foreach ($month_arr as $m){
                    //"department_id_object_code_{organization_type}_month";
                    $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}_{$m['month']}";
                    $row = array();
                    $row['department_id'] = $new['department_id'];
                    $row['object_code'] = $new['object_code'];
                    $row['organization_type'] = $new['organization_type'];
                    $row['amount_type'] = $new['amount_type'];
                    $row['month'] = $m['month'];
                    $row['season'] = $m['season'];
                    if (empty($new[$m['month']])) {
                        $new[$m['month']] = 0;
                    }
                   $row['amount'] =  (isset($need_update[$k])) ? $need_update[$k]['amount'] : ($new[$m['month']] * 1000);
                   $row['amount_left'] = isset($need_update[$k]) ? ($row['amount'] - $need_update[$k]['use_amount']) : $row['amount'];
                   $amount_insert[] = $row;
                }
            }

            // 入库源数据且创建审批流
            $this->logger->info('budget_source_data_check_memory_6: ' . memory_usage());
            if ($is_effective) {
                $effective_flag = $this->writeBudgetData($need_delete, $format_data, $amount_insert, $is_effective, $year);
                $this->logger->info('budget_source_data_check_memory_7: ' . memory_usage());
                if ($effective_flag) {
                    return [
                        'code'    => ErrCode::$SUCCESS,
                        'message' => 'success',
                        'data'    => []
                    ];
                } else {
                    return [
                        'code'    => -1,
                        'message' => 'success',
                        'data'    => []
                    ];
                }

            } else {
                $db = $this->getDI()->get('db_oa');
                $db->begin();
                $db_flag = true;

                $params = self::$excel_department_params;
                //审批主表写入
                $main_data         = [
                    'create_id'    => $user_info['id'] ?? 0,
                    'apply_name'   => $user_info['name'] ?? '',
                    'no'           => static::genSerialNo('BSM', RedisKey::BUDGET_MAIN_APPLY_COUNTER),
                    'apply_status' => 1,
                    'year_at'      => $year,
                    'source_type'  => self::BUDGET_SOURCE_TYPE_1,
                    'reason' => $params['reason'] ?? '',
                    'created_at'   => gmdate('Y-m-d H:i:s'),
                    'updated_at'   => gmdate('Y-m-d H:i:s'),
                    'apply_date'   => date('Y-m-d')
                ];
                $source_main_model = new BudgetSourceMainModel();
                $main_flag         = $source_main_model->create($main_data);

                if ($main_flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算审批主表入库失败===, 原因可能是: ' . get_data_object_error_msg($source_main_model), ErrCode::$SYSTEM_ERROR);
                }

                if (!empty($log_source_data)) {
                    foreach ($log_source_data as &$source_item) {
                        $source_item['main_id']         = $source_main_model->id;
                        $source_item['approved_status'] = 1;
                        $source_item['status']          = 1;
                    }
                }

                $source_data_model = new BudgetSourceDataModel();
                $flag              = $source_data_model->batch_insert($log_source_data);
                if ($flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算源数据入库失败===, 原因可能是: ' . get_data_object_error_msg($source_data_model), ErrCode::$SYSTEM_ERROR);
                }

                $flow_flag = (new BudgetFlowService())->createRequest($source_main_model->id, $user_info);
                if ($flow_flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算审批流创建失败===', ErrCode::$SYSTEM_ERROR);
                }
                $attach = new AttachModel();

                $tmpList = [];
                //附件写入
                if (!empty($attachments)) {
                        $tmp                    = [];
                        $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_SOURCE_DATA;
                        $tmp['oss_bucket_key']  = $source_main_model->id;
                        $tmp['sub_type']        = 0;
                        $tmp['bucket_name']     = $attachments['bucket_name'];
                        $tmp['object_key']      = $attachments['object_key'];
                        $tmp['file_name']       = $attachments['file_name'];
                        $tmpList[]              = $tmp;

                }
                //导入附件-非excel文件
                foreach ($params['attachments'] as $file) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_IMPORT_OSS;
                    $tmp['oss_bucket_key']  = $source_main_model->id;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $file['bucket_name'];
                    $tmp['object_key']      = $file['object_key'];
                    $tmp['file_name']       = $file['file_name'];
                    $tmpList[]              = $tmp;
                }
                if ($tmpList) {
                    $attach_bool = $attach->batchInsert($tmpList);
                    if ($attach_bool === false) {
                        $db_flag = false;

                        throw new \Exception('财务预算导入-附件创建失败： ' . json_encode($tmpList, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($attach), ErrCode::$SYSTEM_ERROR);
                    }
                }

                $db->commit();

                if ($db_flag) {
                    return [
                        'code'    => ErrCode::$SUCCESS,
                        'message' => 'success',
                        'data'    => []
                    ];
                }
            }


        } catch (\Exception $e) {
            if (isset($db_flag) && $db_flag == false) {
                $db->rollback();
            }
            $this->logger->error('budget_source_data_check, result -  ' . $e->getMessage());
        }

        return [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'failed',
            'data' => []
        ];
    }

    protected function split_code($code){
        // 因level_code的长度, 无实意, 故无需根据长度派生多级code; 直接返回原level_code即可
        return $code;

        $length = strlen($code);
        $return = [];
        if(empty($code) || $length < 3)
            return $return;

        for ($m = 3; $m <= $length; $m += 3){
            $return[] = substr($code,0,$m);
        }
        return $return;
    }

    /**
     * 获取指定年份现有部门预算
     * @param string $budget_year
     * @param array $sys_department_list
     * @return mixed
     */
    protected function getExistDepartmentBudgetList(string $budget_year, array $sys_department_list)
    {
        if (empty($budget_year)) {
            return [];
        }

        $department_budget_list = BudgetObjectDepartmentAmount::find([
            'conditions' => 'month LIKE :budget_year: AND amount > 0 AND is_delete = 0',
            'bind' => ['budget_year' => "$budget_year%"],
            'columns' => ['department_id', 'object_code', 'organization_type', 'amount_type', 'amount', 'amount_left'],
            'group' => 'department_id,object_code,organization_type'
        ])->toArray();

        $list = [];
        foreach ($department_budget_list as $value) {
            $value['used_amount'] = $value['amount'] - $value['amount_left'];
            $value['type'] = $sys_department_list[$value['department_id']]['type'] ?? '';
            $value['level'] = $sys_department_list[$value['department_id']]['level'] ?? '';
            $list[$value['object_code'].'_'.$value['department_id'].'_'.$value['organization_type']] = $value;
        }

        return $list;
    }

    /**
     * 找指定部门的父级部门 和 子部门
     * @param array $department_info
     * @param array $all_department
     * @return mixed
     */
    protected function getParentAndSubDepartmentList(array $department_info, array $all_department)
    {
        $result = [];
        if (empty($department_info) || empty($all_department)) {
            return $result;
        }

        // 找父级
        if (in_array($department_info['type'], [2, 3]) && in_array($department_info['level'], [1, 2])) {
            $ancestry_v3 = $all_department[$department_info['id']]['ancestry_v3'] ?? '';
            $ancestry_v3 = $ancestry_v3 ? explode('/', $ancestry_v3) : [];
            foreach ($ancestry_v3 as $ancestry_val) {
                $_ancestry_info = $all_department[$ancestry_val] ?? [];
                if (!in_array($_ancestry_info['level'], [0, 1]) || $_ancestry_info['id'] == $department_info['id']) {
                    continue;
                }

                $result[] = $_ancestry_info;
            }
        }

        // 找子级
        if ($department_info['type'] == 1 || (in_array($department_info['type'], [2, 3]) && $department_info['level'] == 1)) {
            foreach ($all_department as $dept) {
                if ($dept['ancestry'] != $department_info['id']) {
                    continue;
                }

                $result[] = $dept;

                if ($dept['level'] == 1) {
                    // 找二级
                    foreach ($all_department as $_sub_dept) {
                        if ($_sub_dept['ancestry'] == $dept['id']) {
                            $result[] = $_sub_dept;
                        }
                    }
                }
            }
        }


        return $result;
    }

    /**
     * 待导入的数据项同科目不同部门层级的逻辑校验
     * @param array $curr_item 当前待校验项基本信息(含科目code/是否共用/组织机构/部门id/部门类型/部门层级
     * @param array $curr_parent_and_sub_departments 当前校验项的父级部门/子部门列表
     * @param array $waiting_match_item 待匹配的预算项(Excel 项 / 表中已有部门预算项)
     *
     * @return mixed
     */
    protected function checkSourceDataItem(array $curr_item, array $curr_parent_and_sub_departments, array $waiting_match_item)
    {
        $error_str = '';

        if (empty($curr_item) || empty($curr_parent_and_sub_departments) || empty($waiting_match_item)) {
            return $error_str;
        }

        foreach ($curr_parent_and_sub_departments as $_item) {
            $match_obj_dept_data = $waiting_match_item[$curr_item['obj_code'].'_'.$_item['id'].'_'.$curr_item['organization_type']] ?? [];
            if (empty($match_obj_dept_data)) {
                continue;
            }
            // 当前项是公司:共用 e 一二级部门不可导入
            if ($curr_item['type'] == 1) {//当前是公司 共用下 子部门都只能共用 不可导入
                if ($curr_item['amount_type'] == 2) {
                    if (!($match_obj_dept_data['amount_type'] == 2 && $match_obj_dept_data['type'] == 1)) {
                        $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                    }

                }


            } else if (in_array($curr_item['type'], [2, 3]) && $curr_item['level'] == 1) {
                // 当前项是一级部门
                if ($match_obj_dept_data['type'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . '公司 / ';
                }

                if ($curr_item['amount_type'] == 2) {
                    // 共用: 公司 和 二级部门不可导入
                    if ( $match_obj_dept_data['level'] == 2 ) {
                        $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                    }
                } else {
                    // 不共用: 略

                }

            } else if (in_array($curr_item['type'], [2, 3]) && $curr_item['level'] == 2) {
                // 当前项是二级部门: 只能不共用
                if ($match_obj_dept_data['type'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . '公司 / ';
                }
                //父级部门共用子级不可导入
                if (in_array($match_obj_dept_data['type'], [2, 3]) && $match_obj_dept_data['level'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                }
                if ($curr_item['amount_type'] == 2) {
                    // 共用: 公司 和 二级部门不可导入
                    if ($match_obj_dept_data['amount_type'] == 2 && $match_obj_dept_data['level'] != 2 ) {
                        $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                    }
                } else {
                    // 不共用: //除一级不共用的情况下 都不可导入

                    if (!($match_obj_dept_data['amount_type'] == 1 && in_array($match_obj_dept_data['level'], [0, 1, 2]))) {
                        $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                    }

                    if ($match_obj_dept_data['amount_type'] == 2 && $match_obj_dept_data['level'] == 1) {
                        $error_str .= $_item['name'] . '部门(' . $match_obj_dept_data['level'] . '级) / ';
                    }
                }
            }
        }

        return $error_str ? rtrim(trim($error_str), '/') : '';
    }

    /**
     * 生成导入模板
     */
    public function importTemplate()
    {
        $default_file = 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1622613274-f43362a180a44311b29c877c6aca0726.xlsx';
        $header_info = get_headers($default_file, 1);
        if (!empty($header_info[0]) && strrpos($header_info[0],'200') !== false && !empty($header_info['Content-Length'])) {
            $file_url = $default_file;
        } else {
            $server = new BudgetService();
            $column = array(
                '预算科目',
                '公司／部门',
                '组织机构',
                '是否共用',
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec'
            );

            $r = $server->export_check([], $column);
            $file_url = $r['data'] ?? '';
        }

        $return = [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'error',
            'data' => ''
        ];
        if (!empty($file_url)) {
            $return['code'] = ErrCode::$SUCCESS;
            $return['message'] = 'success';
            $return['data'] = $file_url ?? '';
        }
        return $return;
    }

    /**
     * 审批后数据生效
     *
     * @param $budgetId
     * @param $user
     * @param $year
     * @param bool $lang
     * @return false
     */
    public function budgetEffective($budgetId, $user, $year, $lang = false)
    {
        if ($lang) {
            parent::setLanguage($condition['lang'] ?? 'zh-CN');
        }
        $start_memory_1 = memory_get_usage()/ 1024 / 1024;

        $db = $this->getDI()->get('db_oa');
        try {
            // 获取附件信息
            $file = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type:',
                'bind'       => ['oss_bucket_key' => $budgetId, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_BUDGET_SOURCE_DATA],
                'columns'    => ['bucket_name', 'object_key', 'file_name']
            ])->toArray();
            // 读取文件
            $file_name  = 'budget_' . $budgetId . '.xlsx';
            $file_url   = $this->getShowPath($file[0]['object_key']);
            $excel_file = file_get_contents($file_url);
            $start_memory_2 = memory_get_usage()/ 1024 / 1024;

            $file_path  = sys_get_temp_dir() . '/' . $file_name;

            file_put_contents($file_path, $excel_file);
            $start_memory_3 = memory_get_usage()/ 1024 / 1024;

            $config = ['path' => sys_get_temp_dir()];
            $excel  = new \Vtiful\Kernel\Excel($config);

            $excel_data = $excel->openFile($file_name)
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();
            if(is_file($file_path)){
                unlink($file_path);
            }
            $start_memory_4 = memory_get_usage()/ 1024 / 1024;

            //查询当前单子的导入类型
            $budget = BudgetSourceMainModel::findFirst([
                'id = :id:',
                'bind' => ['id' =>  $budgetId],
            ]);
            if (empty($budget)) {
                $this->logger->notice('budget-effective-failed_err:' . $budgetId . '单子不存在');
                return false;
            }
            if ($budget->source_type == self::BUDGET_SOURCE_TYPE_1) {
                $res            = $this->sourceDataCheck($year, $excel_data, false, $user, 1, [], $budgetId);
                $start_memory_5 = memory_get_usage() / 1024 / 1024;
                $this->logger->info('budget-effective-memory_type_1' . $budgetId . 'm_1:' . $start_memory_1 . '-m_2:' . $start_memory_2 . '-m_3:' . $start_memory_3 . '-m_4:' . $start_memory_4 . '-m_5:' . $start_memory_5);

            } else {
                $res            = $this->sourceImportDataCheck($year, $excel_data, false, $user, 1, [], $budgetId);
                $start_memory_5 = memory_get_usage() / 1024 / 1024;
                $this->logger->info('budget-effective-memory_type_2' . $budgetId . 'm_1:' . $start_memory_1 . '-m_2:' . $start_memory_2 . '-m_3:' . $start_memory_3 . '-m_4:' . $start_memory_4 . '-m_5:' . $start_memory_5);
            }

            if ($res['code'] != 1) {

                if (isset($res['data']['data'])) {
                    $fail_url = $this->genImportItemExcel($res['data']['data']);
                }
                $db->updateAsDict(
                    (new BudgetSourceMainModel)->getSource(),
                    [
                        'fail_url' => $fail_url ?? '',
                    ],
                    ['conditions' => 'id=?', 'bind' => $budgetId]
                );


                $this->logger->notice('budget-effective-failed_reason:' . $budgetId . json_encode($res, JSON_UNESCAPED_UNICODE));

                return false;
            }
        } catch (\Exception $e) {
            $this->logger->warning('budget-effective-failed:' . $budgetId . $e->getMessage());
            return false;
        }

        return true;
    }

    /**
     * 查看
     *
     * @param int $id
     * @param int $uid
     * @return array
     */
    public function detailSource(int $id, int $uid = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            $data = $this->getMainDetail($id, $uid);
            if (empty($data['id'])) {
                throw new BusinessException('获取网点租房付款信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];
    }

    /**
     * 审核列表
     *
     * @param array $condition
     * @param int $uid
     * @param int $type
     * @return array
     */
    public function budgetList(array $condition, int $uid = 0, int $type = 0)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => BudgetSourceMainModel::class]);
            $builder = $this->getCondition($builder, $condition, $type);
            $count = (int) $builder->columns('COUNT(DISTINCT main.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns([
                    'main.id',
                    'main.no',
                    'main.create_id',
                    'main.apply_name',
                    'main.apply_status',
                    'main.created_at',
                    'main.apply_date'

                ]);

                // 待处理
                if ($condition['flag'] == GlobalEnums::AUDIT_TAB_PENDING) {
                    $builder->orderBy('main.id asc');
                } else {
                    $builder->orderBy('main.id desc');
                }

                $builder->limit($page_size, $offset);
                $builder->groupBy('main.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }

            $data['items'] = $items;
            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('财务预算审核列表异常信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 列表复合搜索条件
     *
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type)
    {
        $apply_id_name = $condition['apply_id_name'] ?? '';
        $apply_date    = $condition['apply_date'] ?? '';

        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        //申请列表
        if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('main.create_id = :uid:', ['uid' => $condition['uid']]);
        } else if ($type == self::LIST_TYPE_AUDIT) {
            // 审批列表
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::BUDGET_OB_TYPE], $condition['uid'], 'main');
        }

        if (!empty($apply_date)) {
            $builder->andWhere('main.apply_date = :apply_date:', ['apply_date' => $condition['apply_date']]);
        }

        //申请人ID或姓名
        if (!empty($apply_id_name)) {
            $builder->andWhere('(main.create_id LIKE :apply_id: OR main.apply_name LIKE :apply_name:)', ['apply_id' => "$apply_id_name", 'apply_name' => "$apply_id_name%"]);
        }

        return $builder;
    }

    public function getShowPath($path)
    {
        $config     = $this->getDI()->get('config');
        $img_prefix = $config->application->img_prefix ?? '';
        return $img_prefix . $path;
    }

    /**
     * 列表数据格式处理
     * @param array $items
     * @return array
     */
    private function handleItems(array $items) :array
    {
        if (empty($items)) {
            return [];
        }
        $translate =static::$t;
        foreach ($items as &$item) {
            $item['approval_status_text'] = $translate[BudgetAdjustEnums::$budget_import_status[$item['apply_status']]];
        }

        return $items;
    }

    /**
     * 获取详情
     * */
    public function getMainDetail(int $id, int $uid = 0)
    {
        //获取审核信息
        $main_model = BudgetSourceMainModel::findFirst([
            'id = :id:',
            'bind' => ['id' => $id],
        ]);
        if (empty($main_model)) {
            return [];
        }

        $data = $main_model->toArray();

        $file = AttachModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key:  AND oss_bucket_type = :oss_bucket_type:',
            'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_BUDGET_SOURCE_DATA],
            'columns'    => ['bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
        ]);

        $attachments         = $file ? $file->toArray() : [];
        $data['attachments'] = $attachments;

        $data['attachments_list'] = AttachModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key:  AND oss_bucket_type = :oss_bucket_type:',
            'bind'       => ['oss_bucket_key' => $id, 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_BUDGET_IMPORT_OSS],
            'columns'    => ['bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
        ])->toArray();

        $data['data_info'] = $this->dealBudgetExcel($id,$uid, $attachments);



        // 审批日志
        //审批流信息
        $req = (new BudgetFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('财务预算获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
        }

        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);
        $audit_log_staff = array_column($auth_logs, 'action', 'staff_id');

        foreach ($auth_logs as $k => $v) {
            $tmp_sub_list = [];
            $sub_list     = $v['list'] ?? [];
            foreach ($sub_list as $sub_k => $sub_v) {
                if (isset($audit_log_staff[$sub_v['staff_id']]) && $audit_log_staff[$sub_v['staff_id']] == 1) {
                    continue;
                }

                $tmp_sub_list[] = $sub_v;
            }
            if (!empty($tmp_sub_list)) {
                $auth_logs[$k]['list'] = $tmp_sub_list;
            }

        }

        $data['auth_logs'] = $auth_logs;


        return $data;


    }


    /**
     * @param $id
     * @param $uid
     * @param $is_audit
     * @return array
     */
    public function getAuditDetail($id, $uid)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $data = $this->getMainDetail($id, $uid);
            if (empty($data['id'])) {
                throw new BusinessException('获取财务预算信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('budget-get-audit-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取列表页搜索条件的生效状态 和 申请状态列表
     *
     * @return array
     */
    public function getSearchDefault()
    {
        $apply_status_item = BudgetAdjustEnums::$budget_import_status;

        $effective_status_item_tmp = [];
        foreach (self::$is_effective_arr as $index => $t_key) {
            $effective_status_item_tmp [] = [
                'id'    => $index,
                'label' => self::$t[$t_key]
            ];
        }

        $apply_status_item_tmp = [];
        foreach ($apply_status_item as $index => $t_key) {
            $apply_status_item_tmp[] = [
                'id'    => $index,
                'label' => self::$t[$t_key]
            ];
        }

        $budget_source_item_tmp = [];
        foreach (self::$budget_import_type as $index => $t_key) {
            $budget_source_item_tmp[] = [
                'id'    => $index,
                'label' => self::$t[$t_key]
            ];
        }

        return [
            'apply_status_item'     => $apply_status_item_tmp,
            'effective_status_item' => $effective_status_item_tmp,
            'budget_source_item'    => $budget_source_item_tmp
        ];
    }

    /**
     * 处理导入excel 汇总信息
     * */
    public function dealBudgetExcel($budget_id,$uid, $file)
    {
        GlobalEnums::init();
        if (empty($file)) {
            return [];
        }
        // 读取文件
        $file_name  = 'budget_detail' .$uid. $budget_id . '.xlsx';
        $file_url   = $this->getShowPath($file[0]['object_key']);
        $excel_file = file_get_contents($file_url);

        $file_path = sys_get_temp_dir() . '/' . $file_name;

        file_put_contents($file_path, $excel_file);

        $config = ['path' => sys_get_temp_dir()];
        $excel  = new \Vtiful\Kernel\Excel($config);

        $excel_data = $excel->openFile($file_name)
            ->openSheet()
            ->setSkipRows(1)
            ->getSheetData();

        if (is_file($file_path)) {
            unlink($file_path);
        }
        // 查询组织和部门映射
        $department_relate = BudgetDepartmentRelateModel::Find()->toArray();
        $department_relate = array_column($department_relate, 'organization', 'department_id');
        // 提取 excel 公司/部门列
        $need_dep = array_column($excel_data, 1);
        $need_dep = array_map('strtolower', $need_dep);
        $need_dep = array_map('trim', $need_dep);
        $need_dep = array_unique($need_dep);
        $need_dep = array_diff($need_dep, array(''));
        $need_dep = array_values($need_dep);
        $all_dep  = $top_dep = $all_dep_source = array();
        if (!empty($need_dep)) {
            // fle 库取部门
            $all_dep = DepartmentModel::find([
                "conditions" => "name in ({need_dep:array}) and deleted = :deleted: ",
                "bind"       => ["need_dep" => $need_dep, "deleted" => 0],
                "columns"    => "id,LOWER(name) as name,type,level"
            ])->toArray();
            $top_dep = array_column($all_dep, null, 'name');
            //数据库所有部门
            $all_dep = array_column($all_dep, 'id', 'name');
        }

        $new_arr = [];
        $arr     = [];
        //季度求和
        $season_arr = [];
        foreach ($excel_data as $k => $v) {
            $season_arr[] = [
                'organization'    => $department_relate[$all_dep[trim(strtolower($v[1]))]],
                'department_name' => $v[1],
                'department_id'   => $all_dep[trim(strtolower($v[1]))],
                'q1'              => (is_numeric($v[4]) ? $v[4] * 1000 : 0) + (is_numeric($v[5]) ? $v[5] * 1000 : 0) + (is_numeric($v[6]) ? $v[6] * 1000 : 0),
                'q2'              => (is_numeric($v[7]) ? $v[7] * 1000 : 0) + (is_numeric($v[8]) ? $v[8] * 1000 : 0) + (is_numeric($v[9]) ? $v[9] * 1000 : 0),
                'q3'              => (is_numeric($v[10]) ? $v[10] * 1000 : 0) + (is_numeric($v[11]) ? $v[11] * 1000 : 0) + (is_numeric($v[12]) ? $v[12] * 1000 : 0),
                'q4'              => (is_numeric($v[13]) ? $v[13] * 1000 : 0) + (is_numeric($v[14]) ? $v[14] * 1000 : 0) + (is_numeric($v[15]) ? $v[15] * 1000 : 0),
                'year'            => (is_numeric($v[4]) ? $v[4] * 1000 : 0) + (is_numeric($v[5]) ? $v[5] * 1000 : 0) + (is_numeric($v[6]) ? $v[6] * 1000 : 0)
                    + (is_numeric($v[7]) ? $v[7] * 1000 : 0) + (is_numeric($v[8]) ? $v[8] * 1000 : 0) + (is_numeric($v[9]) ? $v[9] * 1000 : 0)
                    + (is_numeric($v[10]) ? $v[10] * 1000 : 0) + (is_numeric($v[11]) ? $v[11] * 1000 : 0) + (is_numeric($v[12]) ? $v[12] * 1000 : 0)
                    + (is_numeric($v[13]) ? $v[13] * 1000 : 0) + (is_numeric($v[14]) ? $v[14] * 1000 : 0) + (is_numeric($v[15]) ? $v[15] * 1000 : 0),

            ];
        }
        //部门求和
        $organization_arr = [];
        $temp_arr         = [];

        foreach ($season_arr as $k1 => $v1) {
            $v1['q1']   = (string)$v1['q1'];
            $v1['q2']   = (string)$v1['q2'];
            $v1['q3']   = (string)$v1['q3'];
            $v1['q4']   = (string)$v1['q4'];
            $v1['year'] = (string)$v1['year'];

            if (in_array($v1['department_id'], $arr)) {
                $new_arr[$v1['department_id']]['q1']   += $v1['q1'];
                $new_arr[$v1['department_id']]['q2']   += $v1['q2'];
                $new_arr[$v1['department_id']]['q3']   += $v1['q3'];
                $new_arr[$v1['department_id']]['q4']   += $v1['q4'];
                $new_arr[$v1['department_id']]['year'] += $v1['year'];


            } else {
                $arr[]                                            = $v1['department_id'];
                $new_arr[$v1['department_id']]['organization']    = $v1['organization'];
                $new_arr[$v1['department_id']]['department_name'] = $v1['department_name'];
                $new_arr[$v1['department_id']]['department_id']   = $v1['department_id'];
                $new_arr[$v1['department_id']]['is_total']        = 0;

                $new_arr[$v1['department_id']]['q1']           = $v1['q1'];
                $new_arr[$v1['department_id']]['q2']           = $v1['q2'];
                $new_arr[$v1['department_id']]['q3']           = $v1['q3'];
                $new_arr[$v1['department_id']]['q4']           = $v1['q4'];
                $new_arr[$v1['department_id']]['year']         = $v1['year'];
                $new_arr[$v1['department_id']]['currency_txt'] = GlobalEnums::$sys_default_currency_symbol;
            }

            if (in_array($v1['organization'], $temp_arr)) {
                $organization_arr[$v1['organization']]['q1']   += $v1['q1'];
                $organization_arr[$v1['organization']]['q2']   += $v1['q2'];
                $organization_arr[$v1['organization']]['q3']   += $v1['q3'];
                $organization_arr[$v1['organization']]['q4']   += $v1['q4'];
                $organization_arr[$v1['organization']]['year'] += $v1['year'];


            } else {
                $temp_arr[]                                               = $v1['organization'];
                $organization_arr[$v1['organization']]['organization']    = $v1['organization'];
                $organization_arr[$v1['organization']]['department_name'] = '汇总';
                $organization_arr[$v1['organization']]['is_total']        = 1;

                $organization_arr[$v1['organization']]['department_id']   = $v1['department_id'];
                $organization_arr[$v1['organization']]['q1']              = $v1['q1'];
                $organization_arr[$v1['organization']]['q2']              = $v1['q2'];
                $organization_arr[$v1['organization']]['q3']              = $v1['q3'];
                $organization_arr[$v1['organization']]['q4']              = $v1['q4'];
                $organization_arr[$v1['organization']]['year']            = $v1['year'];
                $organization_arr[$v1['organization']]['currency_txt']    = GlobalEnums::$sys_default_currency_symbol;
            }

        }

        $organization_data        = array_merge($new_arr, $organization_arr);
        $budget_organization_data = [];
        foreach ($organization_data as $k2 => $v2) {
            $budget_organization_data[$v2['organization']][] = $v2;
        }
        $info_data = [];
        foreach ($budget_organization_data as $k3 => $v3) {
            $info_data = array_merge($info_data, $v3);
        }

        //千分位处理
        foreach ($info_data as &$item) {
            $item['q1']   = number_format(bcdiv($item['q1'], 1000, 2), 2, '.', ',');//1,234.57
            $item['q2']   = number_format(bcdiv($item['q2'], 1000, 2), 2, '.', ',');//1,234.57
            $item['q3']   = number_format(bcdiv($item['q3'], 1000, 2), 2, '.', ',');//1,234.57
            $item['q4']   = number_format(bcdiv($item['q4'], 1000, 2), 2, '.', ',');//1,234.57
            $item['year'] = number_format(bcdiv($item['year'], 1000, 2), 2, '.', ',');//1,234.57
        }

        return $info_data;
    }

    public function updateBudget($budget_detail_id,$uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            //审批通过已生效
            $budget_source_data = BudgetSourceDataModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $budget_detail_id],
            ]);

            if (empty($budget_source_data)) {
                throw new ValidationException('数据异常', ErrCode::$VALIDATE_ERROR);
            }
            $budget_source_data = $budget_source_data->toArray();
            if (self::IS_EFFECTIVE == $budget_source_data['status']) {//共用已生效
                //验证部门是否被删除
                $department_info    = DepartmentModel:: getFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $budget_source_data['budget_department_id']],
                ]);

                if(empty($department_info)){
                    throw new ValidationException('部门不存在', ErrCode::$VALIDATE_ERROR);
                }
                //记录操作人和部门
                $this->getDI()->get('logger')->info('更新子部门预算操作信息: ' . json_encode(['uid'=>$uid,'department_id'=>$budget_source_data['budget_department_id']]));
                $bool = $this->dealBudget($budget_source_data);
                if ($bool == false) {
                    throw new ValidationException('update fail', ErrCode::$VALIDATE_ERROR);
                }

            }


        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->info('更新子部门预算' . $message);


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('更新子部门预算: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => ''
        ];

    }

    public function dealBudget($budget_source_data){
        //验证是否有子部门
        $sub_dep = DepartmentModel::get_sub_department($budget_source_data['budget_department_id']);
        if(empty($sub_dep)){
            throw new ValidationException('当前预算所属部门下，无子部门', ErrCode::$VALIDATE_ERROR);
        }
        $sub_dep=$sub_dep[$budget_source_data['budget_department_id']];
        //删除原有子部门 更新子部门预算
        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $month_start       = $budget_source_data['budget_year'] . '-01';
            $month_end         = $budget_source_data['budget_year'] . '-12';
            $deps_str          = "'" . implode("','", $sub_dep) . "'";//多个部门id
            $object_code       = $budget_source_data['budget_object_code'];
            $organization_type = $budget_source_data['organization_type'];
            $budget_department = BudgetObjectDepartment::find([
                'conditions' => "department_id =:department_id: and object_code =:object_code: and organization_type = :organization_type: and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0",
                'bind'       => ['department_id' => $budget_source_data['budget_department_id'], 'object_code' => $object_code, 'organization_type' => $organization_type]

            ])->toArray();

            $budget_amount = BudgetObjectDepartmentAmount::find([
                'conditions' => "department_id =:department_id: and object_code =:object_code: and organization_type = :organization_type: and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0",
                'bind'       => ['department_id' => $budget_source_data['budget_department_id'], 'object_code' => $object_code, 'organization_type' => $organization_type]

            ])->toArray();

            if (!empty($budget_amount)) {
                if ($budget_amount[0]['amount_type'] == self::IS_COM_USE) {
                    $obj_amount_model = new BudgetObjectDepartmentAmount();
                    $obj_model        = new BudgetObjectDepartment();
                    $db->updateAsDict(
                        $obj_amount_model->getSource(),
                        ['is_delete' => 1],
                        ['conditions' => "department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0"]

                    );
                    $db->updateAsDict(
                        $obj_model->getSource(),
                        ['is_delete' => 1],
                        ['conditions' => "department_id in ({$deps_str}) and object_code = '{$object_code}' and organization_type = $organization_type and  month >='{$month_start}' and month <='{ $month_end}'  and is_delete=0"]

                    );


                    $amount_insert            = [];
                    $amount_department_insert = [];
                    foreach ($sub_dep as $key => $value) {
                        foreach ($budget_amount as $k => $v) {
                            $row                      = [];
                            $row['department_id']     = $value;
                            $row['object_code']       = $v['object_code'];
                            $row['organization_type'] = $v['organization_type'];
                            $row['amount_type']       = $v['amount_type'];
                            $row['month']             = $v['month'];
                            $row['season']            = $v['season'];
                            $row['amount']      = 0;
                            $row['amount_left'] = 0;
                            $amount_insert[] = $row;
                        }

                        foreach ($budget_department as $k1 => $v1) {
                            $tmp                      = [];
                            $tmp['department_id']     = $value;
                            $tmp['object_level']      = $v1['object_level'];
                            $tmp['object_code']       = $v1['object_code'];
                            $tmp['organization_type'] = $v1['organization_type'];
                            $tmp['month']             = $v1['month'];
                            $tmp['season']            = $v1['season'];

                            $amount_department_insert[] = $tmp;
                        }
                    }

                    $flag = $obj_model->batch_insert($amount_department_insert);
                    if (!$flag) {
                        $this->getDI()->get('logger')->info('更新子部门预算department' . json_encode($amount_department_insert));

                        $db->rollback();
                        return false;
                    }

                    $flag = $obj_amount_model->batch_insert($amount_insert);
                    if (!$flag) {
                        $this->getDI()->get('logger')->info('更新子部门预算amount' . json_encode($amount_insert));

                        $db->rollback();
                        return false;
                    }

                }

            }

            $db->commit();

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->info('更新子部门预算' . $e->getMessage());
            return false;
        }

        return true;
    }

    /**
     * 月度预算报表下更新预算按钮
     * @param array $params 请求入参
     * @param array $user 当前登录用户
     * @return array
     */
    public function updateDepartmentBudget(array $params, array $user)
    {
        ini_set('memory_limit', '1024M');
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            DataPermissionModuleConfigService::getInstance()->queryAuthority($params['department_id'], $user, SysConfigEnums::SYS_MODULE_BUDGET);
            //记录操作人和部门
            $this->logger->info('月度报表-更新子部门预算操作信息: ' . json_encode(['uid' => $user, 'department_id' => $params['department_id']]));
            $budget_data = [
                'budget_year'          => BaseService::getCurrentYear(),
                'budget_department_id' => $params['department_id'],
                'budget_object_code'   => $params['level_code'],
                'organization_type'    => $params['cost_store_type'],
            ];
            $bool        = $this->dealBudget($budget_data);
            if ($bool == false) {
                throw new ValidationException('update fail', ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->info('月度报表-更新子部门预算' . $message);

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('月度报表-更新子部门预算: ' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => ''
        ];
    }


    /**
     * 预算导入覆盖新增数据校验
     * @param string $year 预算年份
     * @param array $excel_data 预算原数据: Excel / 表单提交
     * @param bool $is_only_check 是否仅做数据检测
     * @param array $user_info 当前操作用户
     * @param int $data_source 数据来源 1-Excel；2-表单提交
     * @param array $attachments 附件
     * @param $is_effective
     * @return mixed
     */
    public function sourceImportDataCheck(string $year, array $excel_data, bool $is_only_check = true, array $user_info = [], int $data_source = 0, $attachments = [], $is_effective = false)
    {
        ini_set('memory_limit', '1024M');

        try {
            $this->logger->info('budget_source_data_check_type_2, data = ' . json_encode($excel_data, JSON_UNESCAPED_UNICODE));
            $this->logger->info('budget_source_data_check_type_2_memory_1:' . memory_usage());
            //校验当前 待审批 预算导入和预算调整单子
            $main_model = BudgetSourceMainModel::findFirst([
                'apply_status = :status: and year_at = :year_at:',
                'bind' => ['status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_PENDING, 'year_at' => $year],
            ]);
            if (!empty($main_model)) {
                return [
                    'code'    => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('budget_import_order_is_pending'),
                    'data'    => []
                ];
            }

            $budget_adjust_model = BudgetAdjustModel::findFirst([
                'status = :status: and budget_year = :budget_year: ',
                'bind' => ['status' => BudgetAdjustEnums::BUDGET_IMPORT_STATUS_PENDING, 'budget_year' => $year],
            ]);

            if (!empty($budget_adjust_model)) {
                return [
                    'code'    => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('budget_adjust_order_is_pending'),
                    'data'    => []
                ];
            }

            // 提取 excel 科目列 及 科目编码
            $all_min_obj = array_column($excel_data,0);
            $obj_name = array_map('trim',$all_min_obj);
            $obj_name = array_unique($obj_name);
            $obj_name = array_diff($obj_name,array(''));
            $obj_name = array_values($obj_name);
            if (!empty($obj_name)) {
                $budget_obj_data = BudgetObject::find([
                    "conditions" => "name_cn in ({obj_name:array}) and is_end = :is_end: and is_delete = 0",
                    "bind"=>["obj_name"=>$obj_name,"is_end" => 1],
                ])->toArray();
            }

            if (empty($budget_obj_data)) {
                return [
                    'code'    => ErrCode::$VALIDATE_ERROR,
                    'message' => static::$t->_('confirm_budget_object_name'),
                    'data'    => []
                ];
            }
            foreach ($budget_obj_data as $budget_item) {
                if (self::IS_NO_BUDGET == $budget_item['is_budget']) {
                    return [
                        'code'    => ErrCode::$VALIDATE_ERROR,
                        'message' => $budget_item['name_en'] . static::$t->_('object_name_not_budget'),
                        'data'    => []
                    ];
                }
            }

            $obj_data = array_column($budget_obj_data,'level_code','name_cn');
            $obj_data_code_name = array_column($budget_obj_data,'name_cn','level_code');
            unset($budget_obj_data);

            // 提取 excel 公司/部门列
            $need_dep = array_column($excel_data,1);
            $need_dep = array_map('strtolower',$need_dep);
            $need_dep = array_map('trim',$need_dep);
            $need_dep = array_unique($need_dep);
            $need_dep = array_diff($need_dep,array(''));
            $need_dep = array_values($need_dep);
            $all_dep = $top_dep = $all_dep_source = array();
            //根据excel 部门name获取部门信息
            if (!empty($need_dep)) {
                // fle 库取部门
                $all_dep = DepartmentModel::find([
                    "conditions" => "name in ({need_dep:array}) and deleted = :deleted: ",
                    "bind"=>["need_dep"=>$need_dep,"deleted" => 0],
                    "columns" => "id,LOWER(name) as name,type,level"
                ])->toArray();
                $top_dep = array_column($all_dep,null,'name');
                //数据库所有部门
                $all_dep = array_column($all_dep,'id','name');
            }

            // excel 字段转换 name -> id excel字段处理便于程序处理

            $column = array(
                0 => 'object_code',//最小级code
                1 => 'department_id',//部门id
                2 => 'organization_type',//组织机构
                3 => 'amount_type',//是否共用
                4 => $year.'-01',
                5 => $year.'-02',
                6 => $year.'-03',
                7 => $year.'-04',
                8 => $year.'-05',
                9 => $year.'-06',
                10 => $year.'-07',
                11 => $year.'-08',
                12 => $year.'-09',
                13 => $year.'-10',
                14 => $year.'-11',
                15 => $year.'-12',
            );

            // excel 横向记录 变成列
            $start = $year.'-01';
            $month_arr = array();
            $this->logger->info('budget_source_data_check_type_2_memory_2: ' . memory_usage());
            //1-12月 1-4 四个季度
            while ($start <= $year.'-12') {
                $month_int = intval(date('m',strtotime($start)));
                $month_arr[] = [
                    'month' => $start,
                    'season' => intval((($month_int -1) / 3) + 1),//取月份对应季度
                ];

                $start = date('Y-m',strtotime("{$start} +1 month"));
            }

            // 系统所有的部门
            $sys_department = DepartmentModel::find([
                "conditions" => "deleted = 0",
                "columns" => "id,name,type,level,ancestry,ancestry_v3"
            ])->toArray();
            $all_dep_source = array_column($sys_department, null, 'id');

            // 系统已有的预算
            $sys_exist_department_budget_item = $this->getExistDepartmentBudgetList($year, $all_dep_source);

            $need_parent = $need_parent_ids = array();
            $format_data = array();// 入库 budget_object_department数据
            $new_excel_data = array();//excel 整理成 key val
            $error_num = 0;//错误行数
            $all_num = 0;//总记录数
            $is_unique = true;//是否有重复记录
            $check_unique = array();
            $excel_check_item = [];

            // 原数据校验
            // 1. Excel原数据格式初始化
            $organization_type_item = [
                '网点' => 1,
                '总部' => 2
            ];
            $amount_type_item = [
                '否' => 1,
                '是' => 2,
            ];
            $this->logger->info('budget_source_data_check_type_2_memory_3: ' . memory_usage());
            foreach ($excel_data as $k => &$v) {
                $v = array_map('trim', $v);
                if (empty($v[0]) && empty($v[1]) && empty($v[2]) && empty($v[3])) {
                    unset($excel_data[$k]);
                    continue;
                }

                $v[1] = strtolower($v[1]);//部门转小写

                $all_num++;

                // 验证是否有重复记录, 重复的跳出
                $unique_k = "$v[0]_$v[1]_$v[2]";
                if (in_array($unique_k, $check_unique)) {
                    $is_unique = false;
                    break;
                }
                $check_unique[] = $unique_k;

                // 异构Excel预算原数据结构, 用于下列校验
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if ($_curr_obj_code && $_curr_dept_info) {
                    $_organization_type = $organization_type_item[$v[2]] ?? '';
                    $excel_check_item[$_curr_obj_code.'_'.$_curr_dept_info['id'].'_'.$_organization_type] = [
                        'type' => $_curr_dept_info['type'],
                        'level' => $_curr_dept_info['level'],
                        'organization_type' => $_organization_type,
                        'amount_type' => $amount_type_item[$v[3]] ?? '',
                    ];
                }
            }
            $this->logger->info('budget_source_data_check_type_2_memory_4: ' . memory_usage());
            // Excel行数据重复性校验
            if (!$is_unique) {
                return [
                    'code' => -22,
                    'message' => static::$t->_('excel_data_import_budget_repeat'),
                    'data' => []
                ];
            }
            unset($check_unique);

            // 2. Excel数据基本校验、Excel数据自身校验、Excel数据 和 表里已有数据校验
            $excel_data = array_values($excel_data);
            // 遍历Start
            foreach ($excel_data as &$v) {
                //单行多个错误 是否需要自增错误行数 标记
                $is_error = false;
                $error_str = '';

                // 没科目 不管
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                if (empty($_curr_obj_code)) {
                    $error_str .= '科目匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 没部门 不管
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if (empty($_curr_dept_info)) {
                    $error_str .= '部门匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
                // c-level  只可以导入不共用预算  公司bu 可以导入共用和不共用预算 共用可改为独占

                // 不能给非公司、非一级部门、非二级部门导入预算
                if ((!isset($_curr_dept_info['level']) || (!in_array($_curr_dept_info['level'], [0, 1, 2]))&&(isset($_curr_dept_info['type']) && !in_array($_curr_dept_info['type'], [4, 5])))) {
                    $error_str .= '非一/二级部门/非公司/非c-level, 不可导入预算|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 共用规则:
                // 共用规则 验证
                if (!in_array($v[3],array('是','否'))) {
                    $error_str .= '共用规则匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 组织机构 非总部 网点
                if (!in_array($v[2], array('总部','网点'))) {
                    $error_str .= '组织机构匹配失败|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 部门各层级逻辑校验
                $_curr_item = [
                    'obj_code' => $_curr_obj_code,
                    'organization_type' => $organization_type_item[$v[2]] ?? '',
                    'amount_type' => $amount_type_item[$v[3]] ?? '',
                ];


                // 找当前部门的父级部门 和 子部门
                $_curr_parent_and_sub_departments = $this->getParentAndSubDepartmentList($_curr_dept_info, $all_dep_source);

                // 1. Excel数据项自查
                $_curr_item = array_merge($_curr_item, $_curr_dept_info);
                //c-level 不允许导入共用预算
                if ($_curr_item['type'] == 4 && $_curr_item['amount_type'] == 2) {
                    $error_str .= $_curr_item['name'].'c-level 不能导入共用预算| ';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
                $excel_item_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $excel_check_item);

                if (!empty($excel_item_check_res)) {
                    $error_str .= 'Excel 中'. $excel_item_check_res . '也有该科目预算, 请自查|';
                    $error_num++;
                    $is_error = true;
                }

                // 2. 表中已有数据检查
                $exist_budget_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $sys_exist_department_budget_item);
                if (!empty($exist_budget_check_res)) {
                    $error_str .= '已给' . $exist_budget_check_res . '在本年份导入过此科目的预算|';
                    $error_num++;
                    $is_error = true;
                }
                // 非导入 需要验证 需要转换部门
                if (!$is_only_check) {
                    // 提取科目code
                    $v[0] = empty($obj_data[$v[0]]) ? '' : $obj_data[$v[0]];

                    // 提取部门id
                    $v[1] = empty($all_dep[$v[1]]) ? 0 : $all_dep[$v[1]];

                    // 组织机构
                    if ($v[2] == '总部') {
                        $v[2] = 2;
                    } else if($v[2] == '网点') {
                        $v[2] = 1;
                    }

                    // 需要找子部门 占用规则:
                    // 1. 单独自己部门额度
                    // 2. 与子部门共用顶级部门预算 ()
                    if ($v[3] == '是') {
                        $v[3] = 2;
                        $k = "{$v[1]}_{$v[0]}_{$v[2]}";
                        $need_parent[$k] = $v;
                        $need_parent_ids[] = $v[1];
                    } else {
                        $v[3] = 1;
                    }

                    //整理入库数据
                    $row = array();
                    foreach ($month_arr as $m){
                        $row['department_id'] = $v[1];
                        $row['object_level'] = 1;
                        $row['object_code'] = $v[0];
                        $row['organization_type'] = $v[2];
                        $row['month'] = $m['month'];
                        $row['season'] = $m['season'];
                        $format_data[] = $row;
                    }
                }

                $new = array();
                foreach ($v as $index => $f) {
                    if (empty($column[$index])) {
                        continue;
                    }

                    $new[$column[$index]] = $f;
                }
                $new['error'] = empty($error_str) ? '' : $error_str;
                $new_excel_data[] = $new;
            }
            unset($excel_data);
            $this->logger->info('budget_source_data_check_type_2_memory_5: ' . memory_usage());
            // Excel数据基本校验: 遍历End

            $check_data['data'] = $new_excel_data;
            $check_data['all_dep'] = $all_dep;
            $check_data['obj_data'] = $obj_data;
            $check_data['error_num'] = $error_num;

            // 只是验证数据 不需要入库
            $check_res = $this->checkImportData($check_data, $year,$is_only_check ? false : true);
            $res = $check_res['data'];
            $res['all_num'] = $all_num;
            $res['success_num'] = $all_num - $res['error_num'];
            $res['success_num'] = $res['success_num'] < 0 ? 0 : $res['success_num'];
            if ($check_res['code'] != 1 || $res['error_num'] > 0 || $is_only_check) {
                $_error_str = array_filter(array_unique(array_column($res['data'] ?? [], 'error')));
                $_error_str = $_error_str ? ' : ' . implode(' ', $_error_str) : '';

                return [
                    'code' => $check_res['code'],
                    'message' => $is_only_check ? $check_res['message'] : $check_res['message'] . $_error_str,
                    'data' => $res
                ];
            }

            // 重复导入 可以删除的数据
            $need_delete = empty($check_res['data']['delete']) ? array() : $check_res['data']['delete'];

            // 已经存在占用额度 需要保留 已占用的预算额度
            $need_update = empty($check_res['data']['update']) ? array() : $check_res['data']['update'];

            // 处理 共用部门
            // 如果已经导入过数据 并且 类型没有变化 也需要 删除掉 重新导入
            if (!empty($need_parent)) {
                $need_parent_ids = array_unique($need_parent_ids);
                $department_connect = DepartmentModel::get_sub_department($need_parent_ids);
                foreach ($need_parent as $k => $v) {
                    $exp = explode('_',$k);
                    $par_dep_id = $exp[0];
                    $sub_ids = empty($department_connect[$par_dep_id]) ? array() : $department_connect[$par_dep_id];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    foreach ($sub_ids as $sub) {
                        foreach ($month_arr as $m) {
                            $row = array();
                            $row['department_id'] = $sub;
                            $row['object_level'] = 1;
                            $row['object_code'] = $v[0];
                            $row['organization_type'] = $v[2];
                            $row['month'] = $m['month'];
                            $row['season'] = $m['season'];
                            $format_data[] = $row;
                        }
                    }
                }
            }

            // 处理 最小级code 对应 上级 所有科目
            foreach ($format_data as $f) {
                $codes = $this->split_code($f['object_code']);
                $codes = array_diff($codes,array($f['object_code']));
                if (!empty($codes)) {
                    foreach ($codes as $c) {
                        $f['object_code'] = $c;
                        $f['object_level'] = 1;
                        $format_data[] = $f;
                    }
                }
            }

            $dep_amount = array();
            $log_source_data = [];
            $curr_date = date('Y-m-d');
            foreach ($new_excel_data as $new) {
                // 预算导入的原始数据
                $_dept_info = $all_dep_source[$new['department_id']] ?? [];
                $log_source_data[] = [
                    'creator_id' => $user_info['id'] ?? 0,
                    'creator_name' => $user_info['name'] ?? '',
                    'creator_department_id' => $user_info['department_id'] ?? 0,
                    'creator_department_name' => $user_info['department'] ?? '',
                    'create_date' => $curr_date,
                    'budget_object_name' => $obj_data_code_name[$new['object_code']] ?? '',
                    'budget_object_code' => $new['object_code'],
                    'budget_department_name' => $_dept_info['name'] ?? '',
                    'budget_department_id' => $new['department_id'],
                    'budget_department_type' => $_dept_info['type'] ?? 0,
                    'budget_department_level' => $_dept_info['level'] ?? 0,
                    'organization_type' => $new['organization_type'],
                    'amount_type' => $new['amount_type'],
                    'budget_year' => $year,
                    'jan_amount' => is_numeric($new[$year.'-01']) ? $new[$year.'-01'] * 1000 : 0,
                    'feb_amount' => is_numeric($new[$year.'-02']) ? $new[$year.'-02'] * 1000 : 0,
                    'mar_amount' => is_numeric($new[$year.'-03']) ? $new[$year.'-03'] * 1000 : 0,
                    'apr_amount' => is_numeric($new[$year.'-04']) ? $new[$year.'-04'] * 1000 : 0,
                    'may_amount' => is_numeric($new[$year.'-05']) ? $new[$year.'-05'] * 1000 : 0,
                    'jun_amount' => is_numeric($new[$year.'-06']) ? $new[$year.'-06'] * 1000 : 0,
                    'jul_amount' => is_numeric($new[$year.'-07']) ? $new[$year.'-07'] * 1000 : 0,
                    'aug_amount' => is_numeric($new[$year.'-08']) ? $new[$year.'-08'] * 1000 : 0,
                    'sep_amount' => is_numeric($new[$year.'-09']) ? $new[$year.'-09'] * 1000 : 0,
                    'oct_amount' => is_numeric($new[$year.'-10']) ? $new[$year.'-10'] * 1000 : 0,
                    'nov_amount' => is_numeric($new[$year.'-11']) ? $new[$year.'-11'] * 1000 : 0,
                    'dec_amount' => is_numeric($new[$year.'-12']) ? $new[$year.'-12'] * 1000 : 0,
                    'data_source' => $data_source,
                    'base_source_data_id' => $this->base_source_data_id
                ];


                $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}";

                // 处理子部门
                if (!empty($need_parent[$k])) {
//                    $new['amount_type'] = 1;
                    $sub_ids = empty($department_connect[$new['department_id']]) ? array() : $department_connect[$new['department_id']];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    foreach ($sub_ids as $sub) {
                        $row = array();
                        $row['department_id'] = $sub;
                        $row['object_level'] = 1;
                        $row['object_code'] = $new['object_code'];
                        $row['organization_type'] = $new['organization_type'];
                        $row['amount_type'] = $new['amount_type'];
                        $dep_amount[] = $row;
                    }
                }
            }

            $new_excel_data = array_merge($new_excel_data,$dep_amount);
            $amount_insert = array();
            foreach ($new_excel_data as $new) {
                foreach ($month_arr as $m){
                    //"department_id_object_code_{organization_type}_month";
                    $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}_{$m['month']}";
                    $row = array();
                    $row['department_id'] = $new['department_id'];
                    $row['object_code'] = $new['object_code'];
                    $row['organization_type'] = $new['organization_type'];
                    $row['amount_type'] = $new['amount_type'];
                    $row['month'] = $m['month'];
                    $row['season'] = $m['season'];

                    $row['amount'] = empty($new[$m['month']]) ? 0 : ($new[$m['month']] * 1000);
                    $row['amount_left'] = empty($need_update[$k]) ? $row['amount'] : ($row['amount'] - $need_update[$k]);
                    $amount_insert[] = $row;
                }
            }

            // 入库源数据且创建审批流
            $this->logger->info('budget_source_data_check_type_2_memory_6: ' . memory_usage());
            if ($is_effective) {
                $effective_flag = $this->writeBudgetData($need_delete, $format_data, $amount_insert, $is_effective, $year);
                $this->logger->info('budget_source_data_check_type_2_memory_7: ' . memory_usage());
                if ($effective_flag) {
                    return [
                        'code'    => ErrCode::$SUCCESS,
                        'message' => 'success',
                        'data'    => []
                    ];
                } else {
                    return [
                        'code'    => -1,
                        'message' => 'success',
                        'data'    => []
                    ];
                }

            } else {
                $db = $this->getDI()->get('db_oa');
                $db->begin();
                $db_flag = true;
                $params = self::$excel_department_params;
                //审批主表写入
                $main_data         = [
                    'create_id'    => $user_info['id'] ?? 0,
                    'apply_name'   => $user_info['name'] ?? '',
                    'no'           => static::genSerialNo('BSM', RedisKey::BUDGET_MAIN_APPLY_COUNTER),
                    'apply_status' => 1,
                    'year_at'      => $year,
                    'source_type'  => self::BUDGET_SOURCE_TYPE_2,
                    'reason' => $params['reason'] ?? '',
                    'created_at'   => gmdate('Y-m-d H:i:s'),
                    'updated_at'   => gmdate('Y-m-d H:i:s'),
                    'apply_date'   => date('Y-m-d')
                ];
                $source_main_model = new BudgetSourceMainModel();
                $main_flag         = $source_main_model->create($main_data);

                if ($main_flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算审批主表入库失败===, 原因可能是: ' . get_data_object_error_msg($source_main_model), ErrCode::$SYSTEM_ERROR);
                }

                if (!empty($log_source_data)) {
                    foreach ($log_source_data as &$source_item) {
                        $source_item['main_id']         = $source_main_model->id;
                        $source_item['approved_status'] = 1;
                        $source_item['status']          = 1;
                    }
                }

                $source_data_model = new BudgetSourceDataModel();
                $flag              = $source_data_model->batch_insert($log_source_data);
                if ($flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算源数据_type_2入库失败===, 原因可能是: ' . get_data_object_error_msg($source_data_model), ErrCode::$SYSTEM_ERROR);
                }

                $flow_flag = (new BudgetFlowService())->createRequest($source_main_model->id, $user_info);
                if ($flow_flag === false) {
                    $db_flag = false;
                    throw new \Exception('财务预算审批流_type_2创建失败===', ErrCode::$SYSTEM_ERROR);
                }
                $attach = new AttachModel();

                $tmpList = [];
                //附件写入
                if (!empty($attachments)) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_SOURCE_DATA;
                    $tmp['oss_bucket_key']  = $source_main_model->id;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $attachments['bucket_name'];
                    $tmp['object_key']      = $attachments['object_key'];
                    $tmp['file_name']       = $attachments['file_name'];
                    $tmpList[]              = $tmp;

                }
                //导入附件-非excel文件
                foreach ($params['attachments'] as $file) {
                    $tmp                    = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_BUDGET_IMPORT_OSS;
                    $tmp['oss_bucket_key']  = $source_main_model->id;
                    $tmp['sub_type']        = 0;
                    $tmp['bucket_name']     = $file['bucket_name'];
                    $tmp['object_key']      = $file['object_key'];
                    $tmp['file_name']       = $file['file_name'];
                    $tmpList[]              = $tmp;
                }
                if ($tmpList) {
                    $attach_bool = $attach->batchInsert($tmpList);
                    if ($attach_bool === false) {
                        $db_flag = false;

                        throw new \Exception('财务预算导入_type_2-附件创建失败： ' . json_encode($tmpList, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($attach), ErrCode::$SYSTEM_ERROR);
                    }
                }

                $db->commit();

                if ($db_flag) {
                    return [
                        'code'    => ErrCode::$SUCCESS,
                        'message' => 'success',
                        'data'    => []
                    ];
                }
            }


        } catch (\Exception $e) {
            if (isset($db_flag) && $db_flag == false) {
                $db->rollback();
            }
            $this->logger->error('budget_source_data_check_type_2, result -  ' . $e->getMessage());
        }

        return [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'failed',
            'data' => []
        ];
    }


    /**
     * 预算导入数据检测
     * 说明: 不导入 只验证数据  返回 原装excel 增加一列 错误表示
     * @param array $data
     * @param string $year
     * @param bool $is_insert
     *
     * @return mixed
     */
    public function checkImportData(array $data, string $year, bool $is_insert = false)
    {

        $error_num = $data['error_num'];//错误数据
        $obj_data = $data['obj_data'];
        $all_dep = $data['all_dep'];
        $data = $data['data'];

        // 验证额度变化  是否比 占用额度小

        // 导入预算关联的部门id
        $departments = array_values($all_dep);

        // 关联部门已有的预算额度
        $all_amount = array();
        if (!empty($departments)) {
            $month_start = $year.'-01';
            $month_end = $year.'-12';
            $all_amount = BudgetObjectDepartmentAmount::find([
                "conditions" => "department_id in ({departments:array}) and month >= :month_start: and month <= :month_end: and is_delete=0",
                "bind"=>["departments"=>$departments,"month_start" => $month_start,"month_end" => $month_end],
            ])->toArray();
        }

        //校验金额不能为0
        foreach ($data as &$da) {
            if (!empty($da['error'])) {
                continue;
            }
            $is_error = false;

            for ($i = 1; $i < 13; $i++) {
                $month = $year . '-' . sprintf("%02d", $i);

                if ($da[$month] < 0) {
                    $da['error'] .= "{$month} 覆盖导入的金额必须≥0｜";
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
            }
        }
        // 第一次或者 新增部门 表里没数据 不需要验证
        if (empty($all_amount) && $error_num == 0) {

            if ($is_insert) {

                $return['delete']    = $return['update'] = array();

                $return['error_num'] = $error_num;
                return array('code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $return);
            }
            // 不需要入库 返回数据
            $res['error_num'] = $error_num;
            $res['url']       = '';
            // 验证有错误
            if ($error_num > 0) {
                $res['url'] = $this->genImportItemExcel($data);
            }

            return array('code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $res);
        }

        $format_all_amount = array();

        // $need_delete_sub 需删除的当前部门和子部门
        // $need_delete 只删除当前部门
        // $need_update 记录 已经占用额度 用于计算 更新剩余额度
        $need_delete_sub = $need_delete = $need_update = array();
        if (!empty($all_amount)) {
            foreach ($all_amount as $al) {
                $k = "{$al['department_id']}_{$al['object_code']}_{$al['organization_type']}";
                $format_all_amount[$k]['amount_type'] = $al['amount_type'];
                $format_all_amount[$k][$al['month']] = $al['amount'] - $al['amount_left'];

            }

            unset($all_amount);

            // 遍历要导入的数据: 基础校验已通过
            foreach ($data as &$da) {
                if (!empty($da['error'])) {
                    continue;
                }

                $is_error = false;
                if ($is_insert) {
                    $obj_code = $da['object_code'];
                    $dep_id = $da['department_id'];
                    $org = $da['organization_type'];
                } else {
                    $obj_code = empty($obj_data[$da['object_code']]) ? '' : $obj_data[$da['object_code']];//转科目code
                    $dep_id = empty($all_dep[$da['department_id']]) ? 0 : $all_dep[$da['department_id']];
                    $org = 0;

                    if ($da['organization_type'] == '总部') {
                        $org = 2;
                    } else if ($da['organization_type'] == '网点') {
                        $org = 1;
                    }
                }

                $dk = "{$dep_id}_{$obj_code}_$org";

                // 库里有数据的情况
                if (!empty($format_all_amount[$dk])) {
                    // 如果 共用类型不一样 占用规则
                    // 1 单独自己部门额度
                    // 2 与子部门共用一级部门/公司预算
                    $amount_type = $da['amount_type'];
                    if ($da['amount_type'] == '是') {
                        $amount_type = 2;
                    } else if ($da['amount_type'] == '否') {
                        $amount_type = 1;
                    }

                    if ($amount_type != $format_all_amount[$dk]['amount_type']) {
                        // 插入操作 需要删除
                        // 验证逻辑 不用
                        if ($is_insert) {
                            // 第一次库里保存 共用: 2
                            // 二次不共用  删除子部门
                            if ($format_all_amount[$dk]['amount_type'] == 2) {
                                $need_delete_sub[$dk] = $dep_id;
                            }
                        }

                        // 第一次库里保存 不共用: 1
                        // 二次 共用 提示错误不能修改 因为我无法知道 该部门子部门 谁已经产生订单
                        if ($format_all_amount[$dk]['amount_type'] == 1) {
                            $da['error'] .= "数据库中存在该部门占用规则为【与子部门共用】无法修改｜";
                            if (!$is_error) {
                                $error_num++;
                                $is_error = true;
                            }
                        }

                    } else {
                        // 共用额度相同 并且 都为 2 与子部门共用顶级部门预算 需要删除子部门 重新入库
                        if ($amount_type == 2 && $format_all_amount[$dk]['amount_type'] == 2) {
                            $need_delete_sub[$dk] = $dep_id;
                        }

                        if ($amount_type == 1 && $format_all_amount[$dk]['amount_type'] == 1) {
                            $need_delete[$dk] = $dep_id;
                        }
                    }

                    // 验证修改后的额度 是否 小于 已经占用的 额度

                    $err = array();
                    foreach ($format_all_amount[$dk] as $month => $format) {
                        if ($month == 'amount_type') {
                            continue;
                        }


                        // 数据库 budget_object_department_amount 存的记录 计算差值获取已经占用的额度 泰铢*1000
                        $db_amount     = $format;
                        $change_amount = floatval((empty($da[$month]) ? 0 : $da[$month]) * 1000);


                        // 已经占用的额度 大于 要修改的金额 提示错误
                        if ($db_amount > $change_amount) {
                            $err[] = $month;
                        }

                        // 不为0 说明已经有占用额度 需要记录
                        // 如果占用为0  不需要记录 删了新增
                        if ($db_amount != 0) {
                            $up_k               = "{$dk}_{$month}";
                            $need_update[$up_k] = $db_amount;

                        }
                    }


                    if (!empty($err)) {
                        $month = implode(',',$err);
                        $da['error'] .= "{$month} 月份额度 已占用 超过修改后金额｜";
                        if (!$is_error) {
                            $error_num++;
                        }
                    }


                    if ($is_insert) {
                        $da['amount_type'] = $amount_type;
                    }
                }
            }
        }

        // 返回需要删除的 重新插入 数据
        if ($is_insert) {
            if ($error_num > 0) {
                $res['error_num'] = $error_num;
                $res['data'] = $data;
                return array('code' => -1,'message' => '数据验证存在问题' ,'data' => $res);
            }

            // 获取 需要删除 共用的 子部门

            // 整合 需要删除子部门的 和 不需要删除子部门的 数据
            $del = array();
            if (!empty($need_delete_sub)) {
                $delete_dep = array_values($need_delete_sub);
                $delete_dep = DepartmentModel::get_sub_department($delete_dep);

                foreach ($need_delete_sub as $k => $v){
                    $row = [];

                    //"{$da['department_id']}_{$da['object_code']}_{$da['organization_type']}";
                    $info = explode('_',$k);
                    $row['department_id'] = array($info[0]);

                    if (!empty($delete_dep[$info[0]])) {
                        $row['department_id'] = array_merge($row['department_id'],$delete_dep[$info[0]]);
                    }

                    $row['object_code'] = $info[1];
                    $row['organization_type'] = $info[2];
                    $del[] = $row;
                }
            }

            if (!empty($need_delete)) {
                foreach ($need_delete as $k => $v) {
                    $info = explode('_',$k);
                    $del[] = [
                        'department_id' => array($info[0]),
                        'object_code' => $info[1],
                        'organization_type' => $info[2],
                    ];
                }
            }

            $return['delete'] = $del;
            $return['update'] = $need_update;
            $return['error_num'] = $error_num;
            return array('code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $return);
        }

        // 不需要入库 返回数据
        $result['error_num'] = $error_num;
        $result['url'] = '';

        // 验证有错误
        if ($error_num > 0) {
            $result['url'] = $this->genImportItemExcel($data);
        }

        return array('code' => ErrCode::$SUCCESS, 'message' => 'success', 'data' => $result);
    }

}
