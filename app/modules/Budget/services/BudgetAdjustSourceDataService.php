<?php

namespace App\Modules\Budget\Services;

use App\Library\Enums;
use App\Library\Enums\BudgetAdjustEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Models\BudgetAdjustModel;
use App\Modules\Budget\Models\BudgetAdjustDetailModel;
use App\Modules\Budget\Models\BudgetAdjustSourceDataModel;
use App\Modules\Budget\Models\BudgetDetailAmount;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmountLogModel;
use App\Modules\Budget\Models\BudgetSourceDataModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;

class BudgetAdjustSourceDataService extends BaseService
{
    protected $base_source_data_id = 0;

    private static $instance;

    /**
     * @return BudgetAdjustSourceDataService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取导入的记录列表
     * @param array $params
     * @param bool $is_download
     * @return mixed
     */
    public function getList(array $params, bool $is_download = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $total = 0;

        try {
            $page_size = $params['page_size'] ?? 20;
            $page_num = $params['page'] ?? 1;
            $offset = $page_size * ($page_num - 1);

            $creator = $params['creator'] ?? '';
            $start_date = $params['start_date'] ?? '';
            $end_date = $params['end_date'] ?? '';
            $object_code = $params['object_code'] ?? '';
            $department_id = $params['department_id'] ?? '';
            $organization_type = $params['organization_type'] ?? '';
            $budget_year = $params['budget_year'] ?? '';

            // 导入人
            $creator_ids = [];
            if (!empty($creator)) {
                $creator_ids = HrStaffInfoModel::find([
                    'conditions' => '(staff_info_id LIKE :creator: OR name LIKE :creator:) AND is_sub_staff = 0',
                    'bind' => ['creator' => "%$creator%"],
                    'columns' => ['staff_info_id']
                ])->toArray();
                $creator_ids = !empty($creator_ids) ? array_column($creator_ids, 'staff_info_id') : [];
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(BudgetSourceDataModel::class);

            if(!empty($creator_ids)){
                $builder->inWhere('creator_id', $creator_ids);
            }

            if(!empty($start_date)){
                $builder->andWhere('create_date >= :start_create_date:', ['start_create_date' => $start_date]);
            }

            if(!empty($end_date)){
                $builder->andWhere('create_date <= :end_create_date:', ['end_create_date' => $end_date]);
            }

            if(!empty($object_code)){
                $builder->andWhere('budget_object_code = :budget_object_code:', ['budget_object_code' => $object_code]);
            }

            if(!empty($department_id)){
                $department_ids[] = $department_id;

                // 如果是二级 或 以下子部门, 则需向上查询其一级/公司的共用预算
                $dept_info = SysDepartmentModel::findFirst($department_id);
                if (in_array($dept_info->type, [2,3]) && $dept_info->level != 1 && !empty($dept_info->ancestry_v3)) {
                    $parent_dept_ids = DepartmentModel::getParentIdsByDepartmentId($dept_info->ancestry_v3);
                    $parent_dept_ids = $parent_dept_ids ? array_column($parent_dept_ids, 'id') : [];
                    $department_ids = array_merge($department_ids, $parent_dept_ids);

                    $builder->andWhere('amount_type = :amount_type:', ['amount_type' => 2]);
                }

                $builder->inWhere('budget_department_id', array_values(array_unique($department_ids)));
            }

            if(!empty($budget_year)){
                $builder->andWhere('budget_year = :budget_year:', ['budget_year' => $budget_year]);
            }

            if(!empty($organization_type)){
                $builder->andWhere('organization_type = :organization_type:', ['organization_type' => $organization_type]);
            }

            $builder->columns([
                'id',
                'creator_id',
                'create_date',
                'budget_object_code',
                'budget_department_id',
                'organization_type',
                'amount_type',
                'budget_year',
                'jan_amount',
                'feb_amount',
                'mar_amount',
                'apr_amount',
                'may_amount',
                'jun_amount',
                'jul_amount',
                'aug_amount',
                'sep_amount',
                'oct_amount',
                'nov_amount',
                'dec_amount',
            ]);

            $total = $builder->getQuery()->execute()->count();
            $builder->orderBy('create_time DESC');

            if (!$is_download) {
                $builder->limit($page_size, $offset);
            }
            if ($total) {
                $data = $builder->getQuery()->execute()->toArray();
                $data = $this->handleData($data, $creator_ids);
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('预算调整导入 - 获取记录列表异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'total' => $total,
                'list' => $data
            ]
        ];


    }

    /**
     * 处理导入数据格式
     * @param array $handle_data
     * @param array $creator_ids
     * @return mixed
     */
    protected function handleData(array $handle_data = [], array $creator_ids = [])
    {
        if (empty($handle_data)) {
            return [];
        }

        $is_multiple_data = true;
        if (count($handle_data) == count($handle_data,1)) {
            $data[] = $handle_data;

            $is_multiple_data = false;
        } else {
            $data = $handle_data;
        }

        // 提取员工姓名
        if (empty($creator_ids)) {
            $creator_ids = array_unique(array_column($data, 'creator_id'));
        }

        $staff_item = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id IN ({ids:array}) AND is_sub_staff = 0',
            'bind' => ['ids' => array_values($creator_ids)],
            'columns' => ['staff_info_id', 'name']
        ])->toArray();
        $staff_item = $staff_item ? array_column($staff_item, 'name', 'staff_info_id') : [];

        // 提取员工部门/公司名称
        $sys_department = SysDepartmentModel::find(['columns' => ['id', 'name']])->toArray();
        $sys_department = array_column($sys_department, 'name', 'id');

        // 提取预算科目名称
        $object_item = (new BudgetService())->getAllObjectList();
        $object_item = $object_item ? array_column($object_item, 'name', 'level_code') : [];

        $t = self::$t;
        foreach ($data as $key => $val) {
            $val['budget_department_name'] = $sys_department[$val['budget_department_id']] ?? '';
            $val['creator_name'] = $staff_item[$val['creator_id']] ?? '';
            $val['budget_object_name'] = $object_item[$val['budget_object_code']] ?? '';

            switch ($val['amount_type']) {
                case 1:
                    $val['amount_type'] = trim($t['budget_amount_type.1']);// '否'
                    break;
                case 2:
                    $val['amount_type'] = trim($t['budget_amount_type.2']);// '是'
                    break;
                default:
                    $val['amount_type'] = '';
            }

            switch ($val['organization_type']) {
                case 1:
                    $val['organization_type'] = trim($t['budget_organization_type.1']);// '网点'
                    break;
                case 2:
                    $val['organization_type'] = trim($t['budget_organization_type.2']);// '总部'
                    break;
                default:
                    $val['organization_type'] = '';
            }

            $val['jan_amount'] = bcdiv($val['jan_amount'],1000,3);
            $val['feb_amount'] = bcdiv($val['feb_amount'],1000,3);
            $val['mar_amount'] = bcdiv($val['mar_amount'],1000,3);
            $val['apr_amount'] = bcdiv($val['apr_amount'],1000,3);
            $val['may_amount'] = bcdiv($val['may_amount'],1000,3);
            $val['jun_amount'] = bcdiv($val['jun_amount'],1000,3);
            $val['jul_amount'] = bcdiv($val['jul_amount'],1000,3);
            $val['aug_amount'] = bcdiv($val['aug_amount'],1000,3);
            $val['sep_amount'] = bcdiv($val['sep_amount'],1000,3);
            $val['oct_amount'] = bcdiv($val['oct_amount'],1000,3);
            $val['nov_amount'] = bcdiv($val['nov_amount'],1000,3);
            $val['dec_amount'] = bcdiv($val['dec_amount'],1000,3);

            $data[$key] = $val;
        }

        if (!$is_multiple_data) {
            return $data[0];
        }

        return $data;
    }

    /**
     * 获取单个导入记录详情
     * @param int $id
     * @return mixed
     */
    public function getDetail(int $id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $info = BudgetSourceDataModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
                'columns' => [
                    'id',
                    'creator_id',
                    'budget_object_code',
                    'budget_department_id',
                    'organization_type',
                    'amount_type',
                    'budget_year',
                    'jan_amount',
                    'feb_amount',
                    'mar_amount',
                    'apr_amount',
                    'may_amount',
                    'jun_amount',
                    'jul_amount',
                    'aug_amount',
                    'sep_amount',
                    'oct_amount',
                    'nov_amount',
                    'dec_amount',
                ]
            ]);
            if (!empty($info)) {
                $data = $this->handleData($info->toArray());
            }

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('预算调整导入记录详情异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 下载导入记录
     * @param array $params
     * @param array $user_info
     * @return mixed
     */
    public function dataExport(array $params, array $user_info = [])
    {
        // 预算导出，导出中文即可
        self::setLanguage('zh-CN');

        $data = $this->getList($params, true);

        $data = $data['data']['list'] ?? [];
        $new_data = [];
        if (!empty($data)) {
            foreach ($data as $val ) {
                $new_data[] = [
                    $val['creator_id'],
                    $val['creator_name'],
                    $val['create_date'],
                    $val['budget_object_name'],
                    $val['budget_department_name'],
                    $val['organization_type'],
                    $val['amount_type'],
                    $val['budget_year'],
                    $val['jan_amount'],
                    $val['feb_amount'],
                    $val['mar_amount'],
                    $val['apr_amount'],
                    $val['may_amount'],
                    $val['jun_amount'],
                    $val['jul_amount'],
                    $val['aug_amount'],
                    $val['sep_amount'],
                    $val['oct_amount'],
                    $val['nov_amount'],
                    $val['dec_amount'],
                ];
            }
        }

        $header = [
            '导入人工号',
            '导入人姓名',
            '导入日期',
            '预算科目',
            '公司／部门',
            '组织机构 ',
            '是否共用 ',
            '预算年份',
            'Jan',
            'Feb',
            'Mar',
            'Apr',
            'May',
            'Jun',
            'Jul',
            'Aug',
            'Sep',
            'Oct',
            'Nov',
            'Dec'
        ];

        $file_name = "BudgetAdjustImportData_Download_" . date("YmdHis");

        $result = $this->exportExcel($header, $new_data, $file_name);
        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }

        return $result;
    }

    /**
     * 预算导入数据检测(追加检测)
     * 说明: 不导入 只验证数据  返回 原装excel 增加一列 错误表示
     * @param array $data
     * @param string $year
     * @param bool $is_insert
     * @param integer $import_method
     *
     * @return mixed
     */
    public function check($data, $year, $is_insert = false, $import_method = 0,$is_deduction=false,$budget_adjust_id=0)
    {
        $error_num = $data['error_num'];//错误数据
        $obj_data = $data['obj_data'];
        $all_dep = $data['all_dep'];
        $data = $data['data'];

        // 验证额度变化  是否比 占用额度小

        // 导入预算关联的部门id
        $departments = array_values($all_dep);

        // 关联部门已有的预算额度
        $all_amount = array();
        $month_start = $year.'-01';
        $month_end = $year.'-12';
        if (!empty($departments)) {
            $all_amount = BudgetObjectDepartmentAmount::find([
                "conditions" => "department_id in ({departments:array}) and month >= :month_start: and month <= :month_end:",
                "bind"=>["departments"=>$departments,"month_start" => $month_start,"month_end" => $month_end],
            ])->toArray();
            //是否需要验证扣减
            if ($is_deduction && $budget_adjust_id) {
                $deduction_amount = [];
                //扣减数据
                $budget_adjust_detail  = BudgetAdjustDetailModel::find([
                    "conditions" =>"budget_adjust_id =:budget_adjust_id:  and status=0" ,
                    "bind"=>['budget_adjust_id'=>$budget_adjust_id],
                ])->toArray();

                $column = [
                    'jan_amount' => $year . '-01',
                    'feb_amount' => $year . '-02',
                    'mar_amount' => $year . '-03',
                    'apr_amount' => $year . '-04',
                    'may_amount' => $year . '-05',
                    'jun_amount' => $year . '-06',
                    'jul_amount' => $year . '-07',
                    'aug_amount' => $year . '-08',
                    'sep_amount' => $year . '-09',
                    'oct_amount' => $year . '-10',
                    'nov_amount' => $year . '-11',
                    'dec_amount' => $year . '-12',
                ];

                //提交申请单详情表里负向金额返还到预算表
                foreach ($budget_adjust_detail as $detail) {
                    $obj_codes   = $detail['object_code'];
                    $dep_id      = $detail['budget_department_id'];
                    $org         = $detail['organization_type'];
                    $amount_type = $detail['amount_type'];
                    $dk          = "{$dep_id}_{$obj_codes}_{$org}_{$amount_type}";
                    foreach ($column as $month => $month_num) {
                        if (isset($detail[$month]) && $detail[$month] < 0) {
                            $deduction_amount[$dk . '_' . $month_num] = $detail[$month];
                        }
                    }
                }


                if (!empty($deduction_amount) && !empty($all_amount)) {
                    foreach ($all_amount as &$budget_amount) {
                        $b_k = $budget_amount['department_id'] . '_' . $budget_amount['object_code'] . '_' . $budget_amount['organization_type'] . '_' . $budget_amount['amount_type'] . '_' . $budget_amount['month'];
                        if (isset($deduction_amount[$b_k]) && $deduction_amount[$b_k] < 0) {
                            $budget_amount['amount']      = $budget_amount['amount'] - $deduction_amount[$b_k];
                            $budget_amount['amount_left'] = $budget_amount['amount_left'] - $deduction_amount[$b_k];

                        }

                    }
                }
            }
        }
        // 第一次或者 新增部门 表里没数据 不需要验证
        if (empty($all_amount) && $error_num == 0) {
            if ($is_insert) {
                $return['delete'] = $return['update'] = array();
                $return['error_num'] = $error_num;
                return array('code' => 1,'message' => 'success' ,'data' => $return);
            }

            $res['error_num'] = $error_num;
            $res['data'] = $data;
            return array('code' => 1,'message' => 'success' ,'data' => $res);
        }

        $format_all_amount = array();

        $organization_type_item = self::$organization_type_item;

        // $need_delete_sub 需删除的当前部门和子部门
        // $need_delete 只删除当前部门
        // $need_update 记录 已经占用额度 用于计算 更新剩余额度
        $need_delete_sub = $need_delete = $need_update = array();
        if (!empty($all_amount)) {
            $unique_keys = [];
            foreach ($all_amount as $al) {
                $k = "{$al['department_id']}_{$al['object_code']}_{$al['organization_type']}";

                $format_all_amount[$k]['amount_type'] = $al['amount_type'] ?? 0;
                // 修复用户导出预算时给三位小数，展示或判断时给四舍五入保留两位的情况
                $format_all_amount[$k][$al['month']]['occupy_amount'] = bcadd(1000 * sprintf('%.3f',bcsub($al['amount'],$al['amount_left'],2)/1000), 0 ,2);
                $format_all_amount[$k][$al['month']]['total_amount'] = bcadd($al['amount'],0,2);
                if (in_array($k,$unique_keys)) {
                    continue;
                }
                $unique_keys[] = $k;
            }

            $unique_keys = array_values(array_unique($unique_keys));
            unset($all_amount);

            // 遍历要导入的数据: 基础校验已通过
            foreach ($data as &$da) {
                if (!empty($da['error']) ) {
                    continue;
                }

                $is_error = false;
                $org = isset($organization_type_item[strtolower($da['organization_type'])]) ? $organization_type_item[strtolower($da['organization_type'])] : strtolower($da['organization_type']);
                if ($is_insert) {
                    $obj_code = $da['object_code'];
                    $dep_id = $da['department_id'];
                } else {
                    $obj_code = empty($obj_data[$da['object_code']]) ? '' : $obj_data[$da['object_code']];//转科目code
                    $dep_id = empty($all_dep[$da['department_id']]) ? 0 : $all_dep[$da['department_id']];
                }

                $dk = "{$dep_id}_{$obj_code}_$org";
                // 新增科目不允许释放
                if ($import_method == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT && !in_array($dk,$unique_keys)) {
                    $da['error'] .= static::$t->_('budget_new_object_not_free');
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 库里有数据的情况
                if (isset($format_all_amount[$dk]) && !empty($format_all_amount[$dk])) {
                    // 如果 共用类型不一样 占用规则
                    // 1 单独自己部门额度
                    // 2 与子部门共用一级部门/公司预算
                    $amount_type = $da['amount_type'];
                    if ($da['amount_type'] == self::IS_COM_USE_ZH || strtolower($da['amount_type']) == self::IS_COM_USE_EN || $da['amount_type'] == self::IS_COM_USE_TH) {
                        $amount_type = self::IS_COM_USE;
                    } else if ($da['amount_type'] == self::IS_NO_COM_USE_ZH || strtolower($da['amount_type']) == self::IS_NO_COM_USE_EN || $da['amount_type'] == self::IS_NO_COM_USE_TH) {
                        $amount_type = self::IS_NO_COM_USE;
                    }

                    if ($amount_type != $format_all_amount[$dk]['amount_type']) {
                        // 插入操作 需要删除
                        // 验证逻辑 不用
                        if ($is_insert) {
                            // 第一次库里保存 共用: 2
                            // 二次不共用  删除子部门
                            if ($format_all_amount[$dk]['amount_type'] == 2) {
                                $need_delete_sub[$dk] = $dep_id;
                            }
                        }
                        // 第一次库里保存 不共用: 1
                        // 二次 共用 提示错误不能修改 因为我无法知道 该部门子部门 谁已经产生订单
                        if ($format_all_amount[$dk]['amount_type'] == 1) {
                            $da['error'] .= static::$t->_('budget_department_rule_not_alter') . '｜';
                            if (!$is_error) {
                                $error_num++;
                                $is_error = true;
                            }
                        }

                    } else {
                        // 共用额度相同 并且 都为 2 与子部门共用顶级部门预算 需要删除子部门 重新入库
                        if ($amount_type == 2 && $format_all_amount[$dk]['amount_type'] == 2) {
                            $need_delete_sub[$dk] = $dep_id;
                        }

                        if ($amount_type == 1 && $format_all_amount[$dk]['amount_type'] == 1) {
                            $need_delete[$dk] = $dep_id;
                        }
                    }

                    // 验证修改后的额度 是否 小于 已经占用的 额度
                    $err = array();
                     $data_total_amount = 0;
                    foreach ($format_all_amount[$dk] as $month => $format) {
                        if ($month == 'amount_type') {
                            continue;
                        }

                        // 数据库 budget_object_department_amount 存的记录 计算差值获取已经占用的额度 泰铢*1000
                        $db_amount = $format['occupy_amount'];
                        $data_total_amount = bcadd($data_total_amount,floatval($da[$month]) * 1000,2);
                        $change_amount = bcadd($format['total_amount'], floatval($da[$month]) * 1000, 2);
                        // 已经占用的额度 大于 要修改的金额 提示错误
                        if (bccomp($db_amount, $change_amount,0) > 0) {
                            $err[] = $month;
                        }

                        // 不为0 说明已经有占用额度 需要记录
                        // 如果占用为0  不需要记录 删了新增
                        if ($db_amount != 0) {
                            $up_k = "{$dk}_{$month}";
                            $need_update[$up_k] = $db_amount;
                        }
                    }

                    if (!empty($err)) {
                        $month = implode(',',$err);
                        $da['error'] .= $month . static::$t->_('budget_quota_amount_modified') . '｜';
                        if (!$is_error) {
                            $error_num++;
                            $is_error = true;
                        }
                    }

                    if ($is_insert) {
                        $da['amount_type'] = $amount_type;
                    }
                }
            }
        }

        // 返回需要删除的 重新插入 数据
        if ($is_insert) {
            if ($error_num > 0) {
                $res['error_num'] = $error_num;
                $res['data'] = $data;
                return array('code' => -1,'message' => '数据验证存在问题' ,'data' => $res);
            }

            // 获取 需要删除 共用的 子部门

            // 整合 需要删除子部门的 和 不需要删除子部门的 数据
            $del = array();
            if (!empty($need_delete_sub)) {
                $delete_dep = array_values($need_delete_sub);
                $delete_dep = DepartmentModel::get_sub_department($delete_dep);

                foreach ($need_delete_sub as $k => $v){
                    $row = [];

                    //"{$da['department_id']}_{$da['object_code']}_{$da['organization_type']}";
                    $info = explode('_',$k);
                    $row['department_id'] = array($info[0]);

                    if (!empty($delete_dep[$info[0]])) {
                        $row['department_id'] = array_merge($row['department_id'],$delete_dep[$info[0]]);
                    }

                    $row['object_code'] = $info[1];
                    $row['organization_type'] = $info[2];
                    $del[] = $row;
                }
            }

            if (!empty($need_delete)) {
                foreach ($need_delete as $k => $v) {
                    $info = explode('_',$k);
                    $del[] = [
                        'department_id' => array($info[0]),
                        'object_code' => $info[1],
                        'organization_type' => $info[2],
                    ];
                }
            }

            $return['delete'] = $del;
            $return['update'] = $need_update;
            $return['error_num'] = $error_num;

            return array('code' => 1,'message' => 'success' ,'data' => $return);
        }

        // 不需要入库 返回数据
        $result['error_num'] = $error_num;
        $result['url'] = '';

        // 验证有错误
        $result['error'] = array_filter(array_column($data,'error'));
        if ($error_num > 0) {
            $result['url'] = $this->genImportItemExcel($data);
        }

        return array('code' => 1,'message' => 'success' ,'data' => $result);
    }

    /**
     * 生成预算导入返回的Excel
     * 说明：有错误时会返回
     * @param array $source_item
     * @return mixed
     */
    protected function genImportItemExcel(array $source_item)
    {
        $server = new BudgetService();
        $column = array(
            '预算科目-中文',
            '公司/部门',
            '组织机构',
            '是否共用',
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月',
            '错误信息',
        );
        $r = $server->export_check($source_item ?? [], $column);

        return  $r['data'] ?? '';// url
    }

    /**
     * 导入的预算数据入库
     * @param array $log_source_data 导入预算的原始数据
     *
     * @return mixed
     */
    public function writeBudgetSourceData(array $log_source_data)
    {
        try {
            // 入库
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            $source_data_model = new BudgetAdjustSourceDataModel();

            $flag = $source_data_model->batch_insert($log_source_data);
            if (!$flag) {
                $db->rollback();
                return false;
            }

            $db->commit();

            return true;
        }  catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('预算调整导入/复制新增 - 数据写入异常: ' . $e->getMessage());

            return false;
        }
    }


    /**
     * 预算导入/新增数据校验
     *
     * @param integer $type 导入类型
     * @param array $excel_data 预算原数据: Excel / 表单提交
     * @param bool $is_only_check 是否仅做数据检测
     * @param array $user_info 当前操作用户
     * @param int $data_source 数据来源 1-Excel；2-表单提交
     * @param int $transfer_method 转入转出类型 1、转入；2、转出
     * @param int $is_pass 是否审核通过 0、待审核  1、已通过
     * @param string $year 调整年份
     * @param bool $is_deduction
     * @param int $budget_adjust_id
     * @return mixed
     */
    public function sourceDataCheck(int $type, array $excel_data, bool $is_only_check = true, array $user_info = [], int $data_source = 0, $transfer_method = 0, $is_pass = 0, $year = '', $is_deduction = false, $budget_adjust_id = 0)
    {
        try {
            $this->logger->info("budget_adjust_source_data_check, data = " . json_encode($excel_data, JSON_UNESCAPED_UNICODE));
            // 提取 excel 科目列 及 科目编码
            $all_min_obj = array_column($excel_data,0);
            $obj_name = array_map('trim',$all_min_obj);
            $obj_name = array_unique($obj_name);
            $obj_name = array_diff($obj_name,array(''));
            $obj_name = array_values($obj_name);
            if (!empty($obj_name)) {
                $budget_obj_data = BudgetObject::find([
                    "conditions" => "(name_cn in ({name_cn:array}) or name_en in ({name_en:array}) or name_th in ({name_th:array})) and is_end = :is_end: and is_delete = 0",
                    "bind"=>[
                        "name_cn"=>$obj_name,
                        "name_en"=>$obj_name,
                        "name_th"=>$obj_name,
                        "is_end" => 1
                    ]
                ])->toArray();
            }

            if (empty($budget_obj_data)) {
                return [
                    'code' => -12,
                    'message' => static::$t->_('budget_adjust_excel_import_budget_name_error'),
                    'data' => []
                ];
            }

            foreach ($budget_obj_data as $budget_item) {
                if (self::IS_NO_BUDGET == $budget_item['is_budget']) {
                    return [
                        'code'    => -12,
                        'message' => $budget_item['name_en'] . static::$t->_('object_name_not_budget'),
                        'data'    => []
                    ];
                }
            }

            $obj_data_cn = array_column($budget_obj_data, 'level_code', 'name_cn');
            $obj_data_en = array_column($budget_obj_data, 'level_code', 'name_en');
            $obj_data_th = array_column($budget_obj_data, 'level_code', 'name_th');
            $obj_data = array_merge($obj_data_cn, $obj_data_en, $obj_data_th);

            $this->logger->info("budget_adjust_source_data_check, obj_data = " . json_encode($obj_data, JSON_UNESCAPED_UNICODE));

            $obj_data_code_name = array_column($budget_obj_data,'name_cn','level_code');
            unset($budget_obj_data);

            // 提取 excel 公司/部门列
            $need_dep = array_column($excel_data,1);
            $need_dep = array_map('strtolower',$need_dep);
            $need_dep = array_map('trim',$need_dep);
            $need_dep = array_unique($need_dep);
            $need_dep = array_diff($need_dep,array(''));
            $need_dep = array_values($need_dep);
            $all_dep = $top_dep = $all_dep_source = array();
            if (!empty($need_dep)) {
                // fle 库取部门
                $all_dep = DepartmentModel::find([
                    "conditions" => "name in ({need_dep:array}) and deleted = :deleted: ",
                    "bind" => ["need_dep" => $need_dep, "deleted" => 0],
                    "columns" => "id,LOWER(name) as name,type,level"
                ])->toArray();
                $top_dep = array_column($all_dep, null, 'name');
                //数据库所有部门
                $all_dep = array_column($all_dep, 'id', 'name');
            }

            $this->logger->info("budget_adjust_source_data_check, need_dep = " . json_encode($need_dep, JSON_UNESCAPED_UNICODE));

            // excel 字段转换 name -> id
            $column = array(
                0 => 'object_code',//最小级code
                1 => 'department_id',//部门id
                2 => 'organization_type',//组织机构
                3 => 'amount_type',//是否共用
                4 => $year.'-01',
                5 => $year.'-02',
                6 => $year.'-03',
                7 => $year.'-04',
                8 => $year.'-05',
                9 => $year.'-06',
                10 => $year.'-07',
                11 => $year.'-08',
                12 => $year.'-09',
                13 => $year.'-10',
                14 => $year.'-11',
                15 => $year.'-12',
            );

            // excel 横向记录 变成列
            $start = $year . '-01';
            $month_arr = array();
            while ($start <= $year.'-12') {
                $month_int = intval(date('m',strtotime($start)));
                $month_arr[] = [
                    'month' => $start,
                    'season' => intval((($month_int -1) / 3) + 1),//取月份对应季度
                ];

                $start = date('Y-m',strtotime("{$start} +1 month"));
            }

            // 系统所有的部门
            $sys_department = DepartmentModel::find([
                "conditions" => "deleted = 0",
                "columns" => "id,name,type,level,ancestry,ancestry_v3"
            ])->toArray();
            $all_dep_source = array_column($sys_department, null, 'id');

            // 科目+部门+组织类型 是否存在待审核
            $month_start = $year . '-01-01';
            $month_end = $year . '-12-31';
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['ba' => BudgetAdjustModel::class]);
            $builder->leftjoin(BudgetAdjustDetailModel::class, "d.budget_adjust_id=ba.id", "d");
            $builder->andWhere('ba.status = :status:', ['status' => BudgetAdjustEnums::BUDGET_ADJUST_STATUS_PENDING]);
            $builder->andWhere("ba.apply_date >= '" . $month_start . "' and ba.apply_date <= '" . $month_end . "'");
            $builder->columns('d.object_code,d.budget_department_id,d.organization_type,d.amount_type,ba.id');
            $all_pending_order = $builder->getQuery()->execute()->toArray();

            $pending_order = [];
            foreach ($all_pending_order as $order) {
                if ($budget_adjust_id && $budget_adjust_id == $order['id']) {//剔除自身单号
                    continue;
                }
                $key = $order['budget_department_id'] . '_' . $order['object_code'] . '_' . $order['organization_type'];
                $pending_order[$key] = $order;
            }

            $this->logger->info("budget_adjust_source_data_check, pending_order = " . json_encode(array_keys($pending_order), JSON_UNESCAPED_UNICODE));

            // 系统已有的预算
            $uq_budget_code = array_values(array_unique(array_keys($obj_data_code_name)));

            $sys_exist_department_budget_item = $this->getExistDepartmentBudgetList($year, $all_dep_source, $uq_budget_code);

            $this->logger->info("budget_adjust_source_data_check, sys_exist_department_budget_item = " . json_encode(array_keys($sys_exist_department_budget_item), JSON_UNESCAPED_UNICODE));

            // 历史预算占用
           // $history_exist_department_budget_occupy_item = $this->getHistoryDepartmentOccpyBudgetList($year);

            $need_parent = $need_parent_ids = array();
            $format_data = array();// 入库 budget_object_department数据
            $new_excel_data = $new_excel_check_data = array();//excel 整理成 key val
            $error_num = 0;//错误行数
            $all_num = 0;//总记录数
            $is_unique = true;//是否有重复记录
            $check_unique = array();
            $excel_check_item = [];

            // 原数据校验
            // 1. Excel原数据格式初始化
            $organization_type_item = self::$organization_type_item;
            $amount_type_item       = self::$amount_type_item;
            $all_total_amount = 0;
            foreach ($excel_data as $k => &$v) {
                $v = array_map('trim', $v);
                if (empty($v[0]) && empty($v[1]) && empty($v[2]) && empty($v[3])) {
                    unset($excel_data[$k]);
                    continue;
                }

                $v[1] = strtolower($v[1]);//部门转小写

                $all_num++;

                // 验证是否有重复记录, 重复的跳出
                $unique_k = "$v[0]_$v[1]_$v[2]_$v[3]";
                if (in_array($unique_k, $check_unique)) {
                    $is_unique = false;
                    break;
                }
                $check_unique[] = $unique_k;

                // 异构Excel预算原数据结构, 用于下列校验
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if ($_curr_obj_code && $_curr_dept_info) {
                    $_organization_type = $organization_type_item[strtolower(trim($v[2]))] ?? '';
                    $excel_check_item[$_curr_obj_code.'_'.$_curr_dept_info['id'].'_'.$_organization_type] = [
                        'type' => $_curr_dept_info['type'],
                        'level' => $_curr_dept_info['level'],
                        'organization_type' => $_organization_type,
                        'amount_type' => $amount_type_item[strtolower(trim($v[3]))] ?? '',
                    ];
                }
                $this->logger->info('check detail params => excel_check_item:' . json_encode($excel_check_item, JSON_UNESCAPED_UNICODE));
                $total_amount = 0;
                foreach ($column as $k => $val) {
                    if ($k < 4){
                        continue;
                    }
                    $total_amount += sprintf("%.3f",$v[$k]);
                }
                // 预算追加时，每行的预算调整金额汇总不能小于0
                if ($type == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INPUT && bccomp($total_amount,0,2) < 0) {
                    return ['code' => ErrCode::$VALIDATE_ERROR,'message' => static::$t->_('budget_adjust_amount_total_less_than_zero'), 'data' => []];
                    // 预算释放时，每行的预算调整金额汇总只能等于=0；
                } elseif ($type == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_OUTPUT && bccomp($total_amount,0,2)) {
                    return ['code' => ErrCode::$VALIDATE_ERROR,'message' => static::$t->_('budget_adjust_amount_total_not_zero'), 'data' => []];
                }
                $all_total_amount += sprintf("%.3f",$total_amount);
            }

            $all_total_amount = sprintf("%.2f",$all_total_amount);

            $this->logger->info('check detail params => all_total_amount:'.$all_total_amount);

            // 预算转移时，每行的预算调整金额汇总无限制，所有行的预算调整金额汇总合计值=0
            if ($type == BudgetAdjustEnums::BUDGET_ADJUST_IMPORT_TYPE_INOUT && bccomp($all_total_amount,0,2)) {
                return ['code' => ErrCode::$VALIDATE_ERROR,'message' => static::$t->_('budget_adjust_amount_all_total_not_zero'), 'data' => []];
            }

            // Excel行数据重复性校验
            if (!$is_pass && !$is_unique) {
                return [
                    'code' => -22,
                    'message' => '数据有重复（预算科目ID·公司/部门·组织机构·是否共用相同）请删除重复行重新操作',
                    'data' => []
                ];
            }
            unset($check_unique);

            // 2. Excel数据基本校验、Excel数据自身校验、Excel数据 和 表里已有数据校验
            $excel_data = array_values($excel_data);
            // 当前月份
            // 遍历Start
            foreach ($excel_data as &$v) {
                //单行多个错误 是否需要自增错误行数 标记
                $is_error = false;
                $error_str = '';

                // 没科目 不管
                $_curr_obj_code = $obj_data[$v[0]] ?? '';
                if (empty($_curr_obj_code)) {
                    $error_str .= static::$t->_('budget_object_match_fail') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 没部门 不管
                $_curr_dept_info = $top_dep[$v[1]] ?? [];
                if (empty($_curr_dept_info)) {
                    $error_str .= static::$t->_('department_match_fail') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }


                // 不能给非公司、非一级部门、非二级部门导入预算
                if (((!in_array($_curr_dept_info['level'], [0, 1, 2])) && (isset($_curr_dept_info['type']) && !in_array($_curr_dept_info['type'], [4, 5])))) {
                    $error_str .= static::$t->_('organization_not_import') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                // 共用规则:
                // 共用规则 验证
                if (!in_array(strtolower(trim($v[3])),array_keys($amount_type_item))) {
                    $error_str .= static::$t->_('common_rule_match_fail') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }


                // 组织机构 非总部 网点
                if (!in_array(strtolower(trim($v[2])), array_keys($organization_type_item))) {
                    $error_str .= static::$t->_('organization_match_fail') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }
                // 部门各层级逻辑校验
                $_curr_item = [
                    'obj_code' => $_curr_obj_code,
                    'organization_type' => $organization_type_item[strtolower(trim($v[2]))] ?? '',
                    'amount_type' => $amount_type_item[strtolower(trim($v[3]))] ?? '',
                ];

                // 找当前部门的父级部门 和 子部门
                $_curr_parent_and_sub_departments = $this->getParentAndSubDepartmentList($_curr_dept_info, $all_dep_source);

                $this->logger->info('check detail params => $_curr_parent_and_sub_departments:' . json_encode($_curr_parent_and_sub_departments, JSON_UNESCAPED_UNICODE));

                // 1. Excel数据项自查
                $_curr_item = array_merge($_curr_item, $_curr_dept_info);
                //c-level 不允许导入共用预算
                if ($_curr_item['type'] == 4 && $_curr_item['amount_type'] == 2) {
                    $error_str .= $_curr_item['name'] . static::$t->_('c_level_not_import') . '|';
                    if (!$is_error) {
                        $error_num++;
                        $is_error = true;
                    }
                }

                $excel_item_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $excel_check_item);
                if (!empty($excel_item_check_res)) {
                    $error_str .= static::$t->_('budget_in_excel') . $excel_item_check_res . static::$t->_('budget_has_the_object') . '|';

                    $error_num++;
                    $is_error = true;
                }

                $this->logger->info('check detail params => $excel_item_check_res:' . json_encode($excel_item_check_res, JSON_UNESCAPED_UNICODE));

                // 2. 表中已有数据检查
                $exist_budget_check_res = $this->checkSourceDataItem($_curr_item, $_curr_parent_and_sub_departments, $sys_exist_department_budget_item);
                if (!empty($exist_budget_check_res)) {
                    $error_str .= static::$t->_('budget_given') . $exist_budget_check_res . static::$t->_('budget_current_year_imported') . '|';
                    $error_num++;
                    $is_error = true;
                }

                $this->logger->info('check detail params => $exist_budget_check_res:' . json_encode($exist_budget_check_res, JSON_UNESCAPED_UNICODE));

                // 组织机构
                if ($v[2] == self::ORGANIZATION_TYPE_ZH_2 || strtolower($v[2]) == self::ORGANIZATION_TYPE_EN_2 || $v[2] == self::ORGANIZATION_TYPE_TH_2) {
                    $store_type = self::HEAD_OFFICE;
                } else if ($v[2] == self::ORGANIZATION_TYPE_ZH_1 || strtolower(trim($v[2])) == self::ORGANIZATION_TYPE_EN_1 || $v[2] == self::ORGANIZATION_TYPE_TH_1) {
                    $store_type = self::COST_STORE;
                }

                // 公用类型
                $share_amount_type = $amount_type_item[strtolower(trim($v[3]))] ?? '';

                // 3.待审核的 相同部门+科目+组织类型 不能继续调整
                if (!$is_pass && isset($pending_order["{$all_dep[$v[1]]}_{$obj_data[$v[0]]}_{$store_type}"])) {
                    $error_str .= static::$t->_('budget_adjust_order_not_completed');

                    $error_num++;
                    $is_error = true;
                }

                // 非导入 需要验证 需要转换部门
                if (!$is_only_check) {
                    // 提取科目code
                    $v[0] = empty($obj_data[$v[0]]) ? '' : $obj_data[$v[0]];

                    // 提取部门id
                    $v[1] = empty($all_dep[$v[1]]) ? 0 : $all_dep[$v[1]];


                    // 需要找子部门 占用规则:
                    // 1. 单独自己部门额度
                    // 2. 与子部门共用顶级部门预算 ()
                    if ($v[3] == self::IS_COM_USE_ZH || strtolower($v[3]) == self::IS_COM_USE_EN || $v[3] == self::IS_COM_USE_TH) {
                        $v[3] = 2;
                        $k = "{$v[1]}_{$v[0]}_{$store_type}";
                        $need_parent[$k] = $v;
                        $need_parent_ids[] = $v[1];
                    } else {
                        $v[3] = 1;
                    }

                    //整理入库数据
                    $row = array();
                    foreach ($month_arr as $m){
                        $row['department_id'] = $v[1];
                        $row['object_level'] = 1;
                        $row['object_code'] = $v[0];
                        $row['amount_type'] = $share_amount_type;
                        $row['organization_type'] = $store_type;
                        $row['month'] = $m['month'];
                        $row['season'] = $m['season'];
                        $format_data[] = $row;
                    }
                }

                $new = array();

                foreach ($v as $index => $f) {
                    if (empty($column[$index])) {
                        continue;
                    }

                    $new[$column[$index]] = $f;
                }
                $new['error'] = empty($error_str) ? '' : $error_str;
                $new_excel_check_data[] = $new;
            }
            unset($excel_data);
            // Excel数据基本校验: 遍历End
            $check_data['data'] = $new_excel_check_data;
            $check_data['all_dep'] = $all_dep;
            $check_data['obj_data'] = $obj_data;
            $check_data['error_num'] = $error_num;

            $this->logger->info('check detail params => $format_data:' . json_encode($format_data, JSON_UNESCAPED_UNICODE).
                ',$new_excel_check_data:'.json_encode($new_excel_check_data, JSON_UNESCAPED_UNICODE).
                ',$check_data:'.json_encode($check_data, JSON_UNESCAPED_UNICODE));

            // 只是验证数据 不需要入库
            $is_insert = $is_only_check ? false : true;
            $error_str = $check_data['data'][0]['error'] ?? '';
            $check_res = $this->check($check_data, $year, $is_insert, $type, $is_deduction, $budget_adjust_id);
            $this->logger->info('check detail params => $check_res:' . json_encode($check_res, JSON_UNESCAPED_UNICODE));

            if (!empty($check_res['data']['error'])) {
                $error_str .= implode('｜',$check_res['data']['error']);
            }
            $res = $check_res['data'];
            $res['all_num'] = $all_num;
            $res['success_num'] = $all_num - $res['error_num'];
            $res['success_num'] = $res['success_num'] < 0 ? 0 : $res['success_num'];
            $res['error'] = $error_str;
            if (!$is_pass && ($check_res['code'] != 1 || $res['error_num'] > 0 || $is_only_check)) {
                $_error_str = array_filter(array_unique(array_column($res['data'] ?? [], 'error')));
                $_error_str = $_error_str ? ' : ' . implode(' ', $_error_str) : '';
                return [
                    'code' => $check_res['code'],
                    'message' => $is_only_check ? $check_res['message'] : $check_res['message'] . $_error_str,
                    'data' => $res
                ];
            }
            // 重复导入 可以删除的数据
            $need_delete = empty($check_res['data']['delete']) ? array() : $check_res['data']['delete'];
            // 已经存在占用额度 需要保留 已占用的预算额度
            $need_update = empty($check_res['data']['update']) ? array() : $check_res['data']['update'];

            // 处理 共用部门
            // 如果已经导入过数据 并且 类型没有变化 也需要 删除掉 重新导入
            if (!empty($need_parent)) {
                $need_parent_ids = array_unique($need_parent_ids);
                $department_connect = DepartmentModel::get_sub_department($need_parent_ids);
                foreach ($need_parent as $k => $v) {
                    $exp = explode('_',$k);
                    $par_dep_id = $exp[0];
                    $sub_ids = empty($department_connect[$par_dep_id]) ? array() : $department_connect[$par_dep_id];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    $sub_store_type = isset($organization_type_item[strtolower(trim($v[2]))]) ? $organization_type_item[strtolower(trim($v[2]))] : $v[2];
                    $sub_amount_type = isset($amount_type_item[strtolower(trim($v[3]))]) ? $amount_type_item[strtolower(trim($v[3]))] : $v[3];
                    foreach ($sub_ids as $sub) {
                        foreach ($month_arr as $m) {
                            $row = array();
                            $row['department_id'] = $sub;
                            $row['object_level'] = 1;
                            $row['object_code'] = $v[0];
                            $row['organization_type'] = $sub_store_type;
                            $row['amount_type'] = $sub_amount_type;
                            $row['month'] = $m['month'];
                            $row['season'] = $m['season'];
                            $format_data[] = $row;
                        }
                    }
                }
            }

            // 历史是否有预算
            $department_ids = array_values(array_unique(array_column($format_data,'department_id')));
            $object_codes = array_values(array_unique(array_column($format_data,'object_code')));
            $history_exist_department_budget_item = $this->getHistoryDepartmentBudgetList($year, $department_ids, $object_codes);
            $this->logger->info("budget_adjust_source_data_check, history_exist_department_budget_item = " . json_encode(array_keys($history_exist_department_budget_item), JSON_UNESCAPED_UNICODE));


            // 处理 最小级code 对应 上级 所有科目
            foreach ($format_data as &$f) {
                unset($f['amount_type']);
                // 历史已存在的预算数据不更新
                if (isset($history_exist_department_budget_item["{$f['department_id']}_{$f['object_code']}_{$store_type}_{$f['month']}"])) {
                    continue;
                }
                $codes = $this->split_code($f['object_code']);
                $codes = array_diff($codes,array($f['object_code']));
                if (!empty($codes)) {
                    foreach ($codes as $c) {
                        $f['object_code'] = $c;
                        $f['object_level'] = 1;
                        $format_data[] = $f;
                    }
                }
            }

            $dep_amount = array();
            $log_source_data = [];
            $curr_date = date('Y-m-d');
            foreach ($new_excel_check_data as $new) {
                $new['organization_type'] = isset($organization_type_item[strtolower($new['organization_type'])]) ? $organization_type_item[strtolower($new['organization_type'])] : strtolower($new['organization_type']);
                // 预算导入的原始数据
                $_dept_info = $all_dep_source[$new['department_id']] ?? [];
                $log_source_data[] = [
                    'creator_id' => $user_info['id'] ?? 0,
                    'creator_name' => $user_info['name'] ?? '',
                    'creator_department_id' => $user_info['department_id'] ?? 0,
                    'creator_department_name' => $user_info['department'] ?? '',
                    'create_date' => $curr_date,
                    'budget_object_name' => $obj_data_code_name[$new['object_code']] ?? '',
                    'budget_object_code' => $new['object_code'],
                    'budget_department_name' => $_dept_info['name'] ?? '',
                    'budget_department_id' => $new['department_id'],
                    'budget_department_type' => $_dept_info['type'] ?? 0,
                    'budget_department_level' => $_dept_info['level'] ?? 0,
                    'organization_type' => $new['organization_type'],
                    'amount_type' => $new['amount_type'],
                    'budget_year' => $year,
                    'jan_amount' => is_numeric($new[$year.'-01']) ? $new[$year.'-01'] * 1000 : 0,
                    'feb_amount' => is_numeric($new[$year.'-02']) ? $new[$year.'-02'] * 1000 : 0,
                    'mar_amount' => is_numeric($new[$year.'-03']) ? $new[$year.'-03'] * 1000 : 0,
                    'apr_amount' => is_numeric($new[$year.'-04']) ? $new[$year.'-04'] * 1000 : 0,
                    'may_amount' => is_numeric($new[$year.'-05']) ? $new[$year.'-05'] * 1000 : 0,
                    'jun_amount' => is_numeric($new[$year.'-06']) ? $new[$year.'-06'] * 1000 : 0,
                    'jul_amount' => is_numeric($new[$year.'-07']) ? $new[$year.'-07'] * 1000 : 0,
                    'aug_amount' => is_numeric($new[$year.'-08']) ? $new[$year.'-08'] * 1000 : 0,
                    'sep_amount' => is_numeric($new[$year.'-09']) ? $new[$year.'-09'] * 1000 : 0,
                    'oct_amount' => is_numeric($new[$year.'-10']) ? $new[$year.'-10'] * 1000 : 0,
                    'nov_amount' => is_numeric($new[$year.'-11']) ? $new[$year.'-11'] * 1000 : 0,
                    'dec_amount' => is_numeric($new[$year.'-12']) ? $new[$year.'-12'] * 1000 : 0,
                    'data_source' => $data_source,
                    'base_source_data_id' => $this->base_source_data_id,
                    'import_budget_type' => $type, // 导入预算类型
                    'input_output_type' => $transfer_method, // 转入转出方式
                    'budget_adjust_id' => 0 // 审核同意时会赋值
                ];

                $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}";

                // 处理子部门
                if (!empty($need_parent[$k])) {
                    $sub_ids = empty($department_connect[$new['department_id']]) ? array() : $department_connect[$new['department_id']];
                    if (empty($sub_ids)) {
                        continue;
                    }

                    foreach ($sub_ids as $sub) {
                        $row = array();
                        $row['department_id'] = $sub;
                        $row['object_level'] = 1;
                        $row['object_code'] = $new['object_code'];
                        $row['organization_type'] = $new['organization_type'];
                        $row['amount_type'] = $new['amount_type'];
                        $dep_amount[] = $row;
                    }
                }
            }

            $new_excel_data = array_merge($new_excel_check_data,$dep_amount);

            $amount_insert = array();
            foreach ($new_excel_data as $new) {
                $new['organization_type'] = isset($organization_type_item[strtolower($new['organization_type'])]) ? $organization_type_item[strtolower($new['organization_type'])] : strtolower($new['organization_type']);
                foreach ($month_arr as $m) {
                    $row = array();
                    $k = "{$new['department_id']}_{$new['object_code']}_{$new['organization_type']}_{$m['month']}";
                    $row['department_id'] = $new['department_id'];
                    $row['object_code'] = $new['object_code'];
                    $row['organization_type'] = $new['organization_type'];
                    $row['amount_type'] = $new['amount_type'];
                    $row['month'] = $m['month'];
                    $row['season'] = $m['season'];
                    if (isset($new[$m['month']]) && $new[$m['month']] < 0) {
                        $new[$m['month']] = 0;
                    }
                    // 历史已存在的预算数据不更新
                    $history_items = $history_exist_department_budget_item[$k] ?? [];
                    if (empty($history_items)) {
                        $row['amount'] = isset($new[$m['month']]) ? $new[$m['month']] * 1000 : 0;
                        $row['amount_left'] = $row['amount'];
                        $amount_insert[] = $row;
                    } else {
                        $row['amount'] = isset($history_items[0]) && isset($new[$m['month']]) ? ($new[$m['month']] * 1000 + $history_items[0]) : 0;
                        $row['amount_left'] = isset($history_items[1]) && isset($new[$m['month']]) ? ($new[$m['month']] * 1000 + $history_items[1]) : 0;
                        $amount_insert[] = $row;
                    }
                }
            }
            if ($is_pass) {
                $result = [
                    'need_delete' => $need_delete,
                    'budget_object_department' => $format_data,
                    'budget_object_department_amount' => $amount_insert
                ];
            } else {
                $result = $log_source_data;
            }
            $res['excel_data'] = $result;

            $check_return = [
                'code' => ErrCode::$SUCCESS,
                'message' => 'success',
                'data' => $res
            ];
        } catch (\Exception $e) {
            $this->logger->error('budget_adjust_source_data_check, result -  ' . $e->getMessage());
            $check_return = [
                'code' => ErrCode::$SYSTEM_ERROR,
                'message' => 'failed',
                'data' => []
            ];
        }

        $this->logger->info('budget_adjust_source_data_check, return -  ' . json_encode($check_return, JSON_UNESCAPED_UNICODE));

        return $check_return;
    }

    protected function split_code($code){
        $length = strlen($code);
        $return = [];
        if(empty($code) || $length < 3)
            return $return;

        for ($m = 3; $m <= $length; $m += 3){
            $return[] = substr($code,0,$m);
        }
        return $return;
    }

    /**
     * 获取指定年份现有部门预算
     * @param string $budget_year
     * @param array $sys_department_list
     * @param array $uq_budget_code
     * @return mixed
     */
    protected function getExistDepartmentBudgetList(string $budget_year, array $sys_department_list, array $uq_budget_code)
    {
        if (empty($budget_year) || empty($uq_budget_code)) {
            return [];
        }

        // 涉及到同一个科目下，所属部门和子部门查询
        $department_budget_list = BudgetObjectDepartmentAmount::find([
            'conditions' => 'object_code in({object_code:array}) and month LIKE :budget_year: AND amount > 0 AND is_delete = 0',
            'bind' => ['object_code' => $uq_budget_code, 'budget_year' => "$budget_year%"],
            'columns' => ['department_id', 'object_code', 'organization_type', 'amount_type', 'amount', 'amount_left'],
        ])->toArray();

        $list = [];
        foreach ($department_budget_list as $value) {
            $value['used_amount'] = $value['amount'] - $value['amount_left'];
            $value['type'] = $sys_department_list[$value['department_id']]['type'] ?? '';
            $value['level'] = $sys_department_list[$value['department_id']]['level'] ?? '';
            $list[$value['object_code'].'_'.$value['department_id'].'_'.$value['organization_type']] = $value;
        }

        return $list;
    }

    /**
     * 获取历史现有部门预算
     * @param string $budget_year
     * @param array $department_ids
     * @param array $object_codes
     * @return mixed
     */
    protected function getHistoryDepartmentBudgetList(string $budget_year,$department_ids, $object_codes)
    {
        if (empty($budget_year) || empty($department_ids) || empty($object_codes)) {
            return [];
        }

        $start = $budget_year.'-01';
        $end = $budget_year.'-12';
        $department_budget_list = BudgetObjectDepartmentAmount::find([
            'conditions' => 'month >= :start: AND month <= :end: AND is_delete = 0 AND department_id in({department_id:array}) AND object_code in({object_code:array})',
            'bind' => ['start' => $start,'end' => $end, 'department_id' => $department_ids, 'object_code' => $object_codes],
            'columns' => ['department_id', 'object_code', 'organization_type', 'month', 'amount', 'amount_left']
        ])->toArray();
        $list = [];
        foreach ($department_budget_list as $value) {
            $list[$value['department_id'].'_'.$value['object_code'].'_'.$value['organization_type'].'_'.$value['month']] = [$value['amount'],$value['amount_left']];
        }

        return $list;
    }

    /**
     * 已占用的部门预算
     * @param string $budget_year
     * @return mixed
     */
    protected function getHistoryDepartmentOccpyBudgetList(string $budget_year)
    {
        if (empty($budget_year)) {
            return [];
        }

        $department_budget_list = BudgetDetailAmount::find([
            'conditions' => "month like :month:",
            'bind' => ['month' => "{$budget_year}%"],
            'columns' => ['department_id', 'object_code', 'organization_type', 'month', 'act_amount'],
        ])->toArray();

        $list = [];
        foreach ($department_budget_list as $value) {
            $list[$value['department_id'].'_'.$value['object_code'].'_'.$value['organization_type'].'_'.$value['month']] = $value['act_amount'];
        }

        return $list;
    }

    /**
     * 找指定部门的父级部门 和 子部门
     * @param array $department_info
     * @param array $all_department
     * @return mixed
     */
    protected function getParentAndSubDepartmentList(array $department_info, array $all_department)
    {
        $result = [];
        if (empty($department_info) || empty($all_department)) {
            return $result;
        }

        // 找父级
        if (in_array($department_info['type'], [2, 3]) && in_array($department_info['level'], [1, 2])) {
            $ancestry_v3 = $all_department[$department_info['id']]['ancestry_v3'] ?? '';
            $ancestry_v3 = $ancestry_v3 ? explode('/', $ancestry_v3) : [];
            foreach ($ancestry_v3 as $ancestry_val) {
                $_ancestry_info = $all_department[$ancestry_val] ?? [];
                if (!in_array($_ancestry_info['level'], [0, 1]) || $_ancestry_info['id'] == $department_info['id']) {
                    continue;
                }

                $result[] = $_ancestry_info;
            }
        }

        // 找子级
        if ($department_info['type'] == 1 || (in_array($department_info['type'], [2, 3]) && $department_info['level'] == 1)) {
            foreach ($all_department as $dept) {
                if ($dept['ancestry'] != $department_info['id']) {
                    continue;
                }

                $result[] = $dept;

                if ($dept['level'] == 1) {
                    // 找二级
                    foreach ($all_department as $_sub_dept) {
                        if ($_sub_dept['ancestry'] == $dept['id']) {
                            $result[] = $_sub_dept;
                        }
                    }
                }
            }
        }


        return $result;
    }

    /**
     * 待导入的数据项同科目不同部门层级的逻辑校验
     * @param array $curr_item 当前待校验项基本信息(含科目code/是否共用/组织机构/部门id/部门类型/部门层级
     * @param array $curr_parent_and_sub_departments 当前校验项的父级部门/子部门列表
     * @param array $waiting_match_item 待匹配的预算项(Excel 项 / 表中已有部门预算项)
     *
     * @return mixed
     */
    protected function checkSourceDataItem(array $curr_item, array $curr_parent_and_sub_departments, array $waiting_match_item)
    {
        $error_str = '';

        if (empty($curr_item) || empty($curr_parent_and_sub_departments) || empty($waiting_match_item)) {
            return $error_str;
        }

        foreach ($curr_parent_and_sub_departments as $_item) {
            $match_obj_dept_data = $waiting_match_item[$curr_item['obj_code'] . '_' . $_item['id'] . '_' . $curr_item['organization_type']] ?? [];
            if (empty($match_obj_dept_data)) {
                continue;
            }
            // 当前项是公司:共用 e 一二级部门不可导入
            if ($curr_item['type'] == 1) {//当前是公司 共用下 子部门都只能共用 不可导入
                if ($curr_item['amount_type'] == 2) {
                    if (!($match_obj_dept_data['amount_type'] == 2 && $match_obj_dept_data['type'] == 1)) {
                        $error_str .= $_item['name'] . static::$t->_('budget_tip_department') . '(' . $match_obj_dept_data['level'] . static::$t->_('budget_tip_level') . ') / ';
                    }

                }


            } else if (in_array($curr_item['type'], [2, 3]) && $curr_item['level'] == 1) {
                // 当前项是一级部门
                if ($match_obj_dept_data['type'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . static::$t->_('budget_tip_company') . '/ ';
                }

                if ($curr_item['amount_type'] == 2) {
                    // 共用: 公司 和 二级部门不可导入
                    if ($match_obj_dept_data['level'] == 2) {
                        $error_str .= $_item['name'] . static::$t->_('budget_tip_department') . '(' . $match_obj_dept_data['level'] . static::$t->_('budget_tip_level') . ') / ';
                    }
                } else {
                    // 不共用: 略

                }

            } else if (in_array($curr_item['type'], [2, 3]) && $curr_item['level'] == 2) {
                // 当前项是二级部门: 只能不共用
                if ($match_obj_dept_data['type'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . static::$t->_('budget_tip_company') . ' / ';
                }
                //父级部门共用子级不可导入
                if (in_array($match_obj_dept_data['type'], [2, 3]) && $match_obj_dept_data['level'] == 1 && $match_obj_dept_data['amount_type'] == 2) {
                    $error_str .= $_item['name'] . static::$t->_('budget_tip_department') . '(' . $match_obj_dept_data['level'] . static::$t->_('budget_tip_level') . ') / ';
                }

                if ($curr_item['amount_type'] == 2) {
                    // 共用: 公司 和 二级部门不可导入
                    if ($match_obj_dept_data['amount_type'] == 2 && $match_obj_dept_data['level'] != 2) {
                        $error_str .= $_item['name'] . static::$t->_('budget_tip_department') . '(' . $match_obj_dept_data['level'] . static::$t->_('budget_tip_level') . ') / ';
                    }
                } else {
                    // 不共用: //除一级不共用的情况下 都不可导入

                    if (!($match_obj_dept_data['amount_type'] == 1 && in_array($match_obj_dept_data['level'], [1, 2]))) {
                        $error_str .= $_item['name'] . static::$t->_('budget_tip_department') . '(' . $match_obj_dept_data['level'] . static::$t->_('budget_tip_level') . ') / ';
                    }
                }


            }
        }
        return $error_str ? rtrim(trim($error_str), '/') : '';
    }

    /**
     * 生成导入模板
     */
    public function importTemplate()
    {
        $default_file = 'https://fle-asset-internal.oss-ap-southeast-1.aliyuncs.com/workOrder/oca-1622613274-f43362a180a44311b29c877c6aca0726.xlsx';
        $header_info = get_headers($default_file, 1);
        if (!empty($header_info[0]) && strrpos($header_info[0],'200') !== false && !empty($header_info['Content-Length'])) {
            $file_url = $default_file;
        } else {
            $server = new BudgetService();
            $column = array(
                '预算科目',
                '公司／部门',
                '组织机构',
                '是否共用',
                'Jan',
                'Feb',
                'Mar',
                'Apr',
                'May',
                'Jun',
                'Jul',
                'Aug',
                'Sep',
                'Oct',
                'Nov',
                'Dec'
            );

            $r = $server->export_check([], $column);
            $file_url = $r['data'] ?? '';
        }

        $return = [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'error',
            'data' => ''
        ];
        if (!empty($file_url)) {
            $return['code'] = ErrCode::$SUCCESS;
            $return['message'] = 'success';
            $return['data'] = $file_url ?? '';
        }
        return $return;
    }

    /**
     * 生成导入调整模板
     * @params $type integer 导入模板类型 1：追加；2：撤回；3：调整
     * @return array
     */
    public function importAdjustTemplate($type = 0)
    {
        if (empty($type)) {
            return [
                'code' => ErrCode::$SYSTEM_ERROR,
                'message' => static::$t->_('budget_adjust_type_is_empty'),
                'data' => ''
            ];
        }
        $template_key = Enums::BUDGET_ADJUST_UPLOAD_TEMPLATE_KEY . '1_' . self::$language;
        $template_url = EnvModel::getEnvByCode($template_key, 'val');
        $header_info = get_headers($template_url, 1);
        if (!empty($header_info[0]) && strrpos($header_info[0],'200') !== false && !empty($header_info['Content-Length'])) {
            $file_url = $template_url;
        } else {
            $server = new BudgetService();
            $column = array(
                '预算科目','公司／部门','组织机构','是否共用', 'Jan', 'Feb', 'Mar',
                'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
            );

            $r = $server->export_check([], $column);
            $file_url = $r['data'] ?? '';
        }

        if (!empty($file_url)) {
            return [
                'code' => ErrCode::$SUCCESS,
                'message' => 'success',
                'data' => $file_url ?? ''
            ];
        }

        return [
            'code' => ErrCode::$SYSTEM_ERROR,
            'message' => 'error',
            'data' => ''
        ];
    }

    /**
     * 转换excel数据为多组形式
     * @params $excelData array excel数据
     * @return array
     */
    public function transferExcelData($excelData = []){
        if (empty($excelData)) {
            return [];
        }

        $allInputData = [];
        foreach ($excelData as $k => $excelDataItem) {
            $inputData = [];
            foreach ($excelDataItem as $i => $item) {
                if ($i > 15) {
                    $inputData[] = $item;
                    unset($excelData[$k][$i]);
                }
            }
            $allInputData[] = $inputData;
        }

        return [
            'output' => $excelData,
            'input' => $allInputData
        ];
    }

    /**
     * 校验预算科目重复问题
     * @params array $excel_data excel数据
     * @return bool
     */
    public function budgetObjectRepeat($excel_data){
        // 预算调整操作
        $budget_counts = array_count_values(array_column($excel_data,0));
        foreach ($budget_counts as $count) {
            if ($count > 1) {
                return true;
            }
        }

        return false;
    }

    /**
     * 校验预算科目有效性
     * @params integer $id
     * @params array $excel_data excel数据
     * @return bool
     */
    public function budgetObjectValidation($id, $excel_data){
        // 预算调整单中的预算科目
        $list = BudgetAdjustDetailModel::find([
            'columns' => 'object_code',
            'conditions' => 'budget_adjust_id = :budget_adjust_id:',
            'bind'       => [
                'budget_adjust_id' => $id,
            ]
        ])->toArray();
        $object_codes = !empty($list) ? array_column($list,'object_code') : [];
        // 预算调整操作
        $budget_objects = array_column($excel_data,0);
        $exit_budget_object = BudgetObject::find([
            'columns' => 'level_code',
            'conditions' => 'name_cn in({name_cn:array}) or name_en in({name_en:array}) or name_th in({name_th:array})',
            'bind'       => [
                'name_cn' => $budget_objects,
                'name_en' => $budget_objects,
                'name_th' => $budget_objects
            ]
        ])->toArray();
        if (count($exit_budget_object) != count($budget_objects)) {
            return true;
        }
        foreach ($exit_budget_object as $budget_object) {
            if (!in_array(trim($budget_object['level_code']),$object_codes)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 上传文件
     * @params array $param
     * @params string $file_name
     * @return bool
     */
    public function uploadAdjustFile($param,$file_name){
        // 删除就附件
        AttachModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key: and oss_bucket_type = :oss_bucket_type:',
            'bind'       => [
                'oss_bucket_key' => $param['id'],
                "oss_bucket_type" => Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_EXCEL,
            ]
        ])->delete();
        // 新增附件
        $attach = new AttachModel();
        $attachment = [
            "oss_bucket_key" => $param['id'],
            "oss_bucket_type" => Enums::OSS_BUCKET_TYPE_BUDGET_ADJUST_EXCEL,
            "sub_type" => 0,
            "bucket_name" => $param['bucket_name'],
            "object_key" => $param['object_key'],
            "file_name" => $file_name
        ];
        $attach_bool = $attach->save($attachment);
        if ($attach_bool === false) {
            throw new \Exception('预算调整附件关联失败='.json_encode($attachment,JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
        }

        return true;
    }
}
