<?php
/**
 * Created by PhpStorm.
 * User: nick
 * Date: 2021/6/30
 * Time: 4:07 PM
 */


namespace App\Modules\Budget\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Modules\Budget\Models\BudgetDetail;
use App\Modules\Budget\Models\BudgetDetailAmount;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectDepartment;
use App\Modules\Budget\Models\BudgetObjectDepartmentAmount;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Setting\Services\DataPermissionModuleConfigService;

class BudgetExtendService extends BudgetService{

    const MONTH_SEARCH = 1;
    const SEASON_SEARCH = 2;
    const YEAR_SEARCH = 3;


    /**
     *  预算执行情况统计 查询导出
     * @param array $param 条件
     * @param array $user 当前登录用户
     * @return array
     */
    public function year_list($param, $user)
    {
        ini_set('memory_limit', '1024M');
        $department_id   = intval($param['department_id']);
        $department_info = SysDepartmentModel::findFirst($department_id);
        if (empty($department_info)) {
            throw new ValidationException(self::$t['setting_no_operation_permissions_department_error'], ErrCode::$VALIDATE_ERROR);
        }
        DataPermissionModuleConfigService::getInstance()->queryAuthority($param['department_id'], $user, SysConfigEnums::SYS_MODULE_BUDGET);
        $organization_type = intval($param['organization_type']);
        $end_date_tmp      = strtotime($param['end_date']);
        //获取 年初 1月1号到 查询日期 截止 对应的数据
        $year_start   = date('Y-01-01', $end_date_tmp);
        $year         = date('Y', $end_date_tmp);
        $month_int    = intval(date('m', $end_date_tmp));
        $month_first  = $this->get_season_start($month_int);
        $season_start = date("Y-{$month_first}-01", $end_date_tmp);
        $month_start  = date('Y-m-01', $end_date_tmp);

        $add_hour    = $this->config->application->add_hour;
        $real_end    = date('Y-m-d 16:59:59', $end_date_tmp);
        $real_year   = date('Y-m-d H:i:s', strtotime($year_start) - $add_hour * 3600);
        $real_season = date('Y-m-d H:i:s', strtotime($season_start) - $add_hour * 3600);
        $real_month  = date('Y-m-d H:i:s', strtotime($month_start) - $add_hour * 3600);

        //获取所有科目
        $budget_list = BudgetObject::find([
            'columns' => 'level_code,name_th,name_en,name_cn'
        ])->toArray();

        //科目 列表 第一列
        $all_object = array();
        foreach ($budget_list as $budget) {
            $all_object[$budget['level_code']] = $budget[$this->get_lang_column(static::$language)];
        }

        //执行金额 2 3 4 列
        $use_amount = BudgetDetailAmount::find([
            'columns'    => 'department_id,object_code,organization_type,act_amount,month,created_at',
            'conditions' => "department_id = {$department_id} and organization_type = {$organization_type} and created_at between '{$real_year}' and '{$real_end}'",
        ])->toArray();

        $amount = array('month' => array(), 'season' => array(), 'year' => array());//月 季度 年
        if (!empty($use_amount)) {
            foreach ($use_amount as $u) {
                //月
                if ($u['created_at'] >= $real_month && $u['created_at'] <= $real_end) {
                    if (empty($amount['month'][$u['object_code']])) {
                        $amount['month'][$u['object_code']] = $u['act_amount'];
                    } else {
                        $amount['month'][$u['object_code']] += $u['act_amount'];
                    }
                }
                if ($u['created_at'] >= $real_season && $u['created_at'] <= $real_end) {
                    if (empty($amount['season'][$u['object_code']])) {
                        $amount['season'][$u['object_code']] = $u['act_amount'];
                    } else {
                        $amount['season'][$u['object_code']] += $u['act_amount'];
                    }
                }
                //年
                if (empty($amount['year'][$u['object_code']])) {
                    $amount['year'][$u['object_code']] = $u['act_amount'];
                } else {
                    $amount['year'][$u['object_code']] += $u['act_amount'];
                }
            }
        }
        //预算导入 最新总数 5 6 7列
        $start = (string)($year . '-01');
        $end   = (string)($year . '-12');

        $export_amount = BudgetObjectDepartmentAmount::find([
            'columns'    => 'object_code,month,season,amount,amount_left',
            'conditions' => 'month >= :start: AND month <= :end: AND is_delete = :is_delete: AND department_id = :department_id: AND organization_type = :organization_type:',
            'bind'       => ['start' => $start, 'end' => $end, 'is_delete' => GlobalEnums::IS_NO_DELETED, 'department_id' => (string)$department_id, 'organization_type' => (string)$organization_type],
        ])->toArray();



        $export     = array('month' => array(), 'season' => array(), 'year' => array());
        $left       = array('month' => array(), 'season' => array(), 'year' => array());
        $season_arr = $this->get_season_arr($year, $month_first);

        if (!empty($export_amount)) {
            foreach ($export_amount as $ex) {
                //月
                if ($ex['month'] == date('Y-m', strtotime("{$year}-{$month_int}"))) {
                    $export['month'][$ex['object_code']] = $ex['amount'];
                    $left['month'][$ex['object_code']]   = $ex['amount_left'];
                }

                //季度
                if (in_array($ex['month'], $season_arr)) {
                    if (empty($export['season'][$ex['object_code']])) {
                        $export['season'][$ex['object_code']] = $ex['amount'];
                    } else {
                        $export['season'][$ex['object_code']] += $ex['amount'];
                    }
                    if (empty($left['season'][$ex['object_code']])) {
                        $left['season'][$ex['object_code']] = $ex['amount_left'];
                    } else {
                        $left['season'][$ex['object_code']] += $ex['amount_left'];
                    }
                }
                //年
                if (empty($export['year'][$ex['object_code']])) {
                    $export['year'][$ex['object_code']] = $ex['amount'];
                } else {
                    $export['year'][$ex['object_code']] += $ex['amount'];
                }

                if (empty($left['year'][$ex['object_code']])) {
                    $left['year'][$ex['object_code']] = $ex['amount_left'];
                } else {
                    $left['year'][$ex['object_code']] += $ex['amount_left'];
                }
            }
        }
        //整理格式
        $return = array();
        $c3     = $c4 = $c5 = $c6 = $c7 = $c8 = $c9 = $c10 = $c11 = 0;
        foreach ($all_object as $code => $name) {
            $row['object_name'] = $name;
            if (empty($param['is_export'])) {
                $row['object_code'] = $code;
            }
            $row['month_amount']  = empty($amount['month'][$code]) ? 0 : round($amount['month'][$code] / 1000, 2);
            $row['season_amount'] = empty($amount['season'][$code]) ? 0 : round($amount['season'][$code] / 1000, 2);
            $row['year_amount']   = empty($amount['year'][$code]) ? 0 : round($amount['year'][$code] / 1000, 2);
            $row['month_import']  = empty($export['month'][$code]) ? 0 : round($export['month'][$code] / 1000, 2);
            $row['season_import'] = empty($export['season'][$code]) ? 0 : round($export['season'][$code] / 1000, 2);
            $row['year_import']   = empty($export['year'][$code]) ? 0 : round($export['year'][$code] / 1000, 2);
            $row['month_left']    = empty($left['month'][$code]) ? 0 : round($left['month'][$code] / 1000, 2);
            $row['season_left']   = empty($left['season'][$code]) ? 0 : round($left['season'][$code] / 1000, 2);
            $row['year_left']     = empty($left['year'][$code]) ? 0 : round($left['year'][$code] / 1000, 2);
            //合计
            $c3       += $row['month_amount'];
            $c4       += $row['season_amount'];
            $c5       += $row['year_amount'];
            $c6       += $row['month_import'];
            $c7       += $row['season_import'];
            $c8       += $row['year_import'];
            $c9       += $row['month_left'];
            $c10      += $row['season_left'];
            $c11      += $row['year_left'];
            $return[] = $row;
        }

        //合计
        $row['object_name'] = '合计';
        if (empty($param['is_export']))
            $row['object_code'] = '';
        $row['month_amount']  = round($c3, 2);
        $row['season_amount'] = round($c4, 2);
        $row['year_amount']   = round($c5, 2);
        $row['month_import']  = round($c6, 2);
        $row['season_import'] = round($c7, 2);
        $row['year_import']   = round($c8, 2);
        $row['month_left']    = round($c9, 2);
        $row['season_left']   = round($c10, 2);
        $row['year_left']     = round($c11, 2);
        $return[]             = $row;
        if (empty($param['is_export'])) {
            //拼接 部门公司信息
            $res['company_name']      = $department_info->company_name;
            $res['department_name']   = $department_info->name;
            $res['organization_name'] = $param['organization_type'] == 1 ? '网点' : '总部';
            $res['list']              = $return;
            return $res;
        }

        //变态插件 不能有key
        $return = array_map('array_values', $return);
        //导出
        $header    = array(
            //一级预算项目
            static::$t->_('year_budget_export_first_project'),
            //本月已执行金额
            static::$t->_('year_budget_export_current_month_amount'),
            //本季度已执行金额
            static::$t->_('year_budget_export_current_season_amount'),
            //全年累计执行金额
            static::$t->_('year_budget_export_current_year_amount'),
            //本月已导入预算
            static::$t->_('year_budget_export_current_month_export'),
            //本季度已导入预算
            static::$t->_('year_budget_export_current_season_export'),
            //本年累计已导入预算
            static::$t->_('year_budget_export_current_year_export'),
            //本月剩余
            static::$t->_('year_budget_export_current_month_surplus'),
            //季度剩余
            static::$t->_('year_budget_export_current_season_surplus'),
            //年度剩余
            static::$t->_('year_budget_export_current_year_surplus'),
        );
        $file_name = 'budget_export_' . date('Y-m-d') . '.xlsx';
        return $this->exportExcel($header, $return, $file_name);
    }



    public function year_detail_list($param){
        $department_id = intval($param['department_id']);
        $organization_type = intval($param['organization_type']);
        $object_code = addslashes($param['object_code']);

        if(empty($object_code))
            return 'need object code input(需要输入科目编码)';

        $object_info = BudgetObject::findFirst("level_code = '{$object_code}'");
        if(empty($object_info))
            return 'wrong object code(错误科目编码)';

        $obj_name  = $object_info->name_cn;
        $object_info = null;

        $end_date_tmp = strtotime($param['end_date']);
        $between = intval($param['between']);
        $start = '';
        if($between == self::MONTH_SEARCH)
            $start = date('Y-m-01',$end_date_tmp);

        if($between == self::SEASON_SEARCH){
            $month_int = intval(date('m',$end_date_tmp));
            $month_first = $this->get_season_start($month_int);
            $start = date("Y-{$month_first}-01",$end_date_tmp);
        }

        if($between == self::YEAR_SEARCH)
            $start = date('Y-01-01',$end_date_tmp);

        if(empty($start))
            return 'need between input';

        $add_hour = $this->config->application->add_hour;
        $real_end = date('Y-m-d 16:59:59',$end_date_tmp);
        $real_start = date('Y-m-d H:i:s',strtotime($start) - $add_hour * 3600);


        $builder = $this->modelsManager->createBuilder();
        $column = 'da.department_id,da.object_code,da.organization_type,sum(da.act_amount) act_amount,d.type,d.order_no,d.created_at,d.staff_info_id';
        $builder->columns($column);
        $builder->from(['da' => BudgetDetailAmount::class]);
        $builder->join(BudgetDetail::class, 'da.detail_id = d.id', 'd');
        $builder->andWhere('da.department_id = :department_id: and da.organization_type = :organization_type: and da.object_code = :object_code:',
            ['department_id' => $department_id,'organization_type' => $organization_type,'object_code' => $object_code]);
        $builder->betweenWhere('da.created_at',$real_start,$real_end);

        $builder->groupby('da.department_id,da.object_code,da.organization_type,d.type,d.order_no,d.created_at');

        $list = $builder->getQuery()->execute()->toArray();

        $return = array();
        if(!empty($list)){
            foreach($list as $k => $li){
                $row['num'] = $k+1;
                $row['object_name'] = $obj_name;
                $row['create_time'] = date('Y-m-d',strtotime($li['created_at']));
                $row['amount'] = empty($li['act_amount']) ? 0 : round($li['act_amount'] / 1000,2);
                $row['staff_info_id'] = $li['staff_info_id'];
                $type_text = '';
                if($li['type'] == self::ORDER_TYPE_1)
                    $type_text = '报销';
                if($li['type'] == self::ORDER_TYPE_2)
                    $type_text = '采购';
                if($li['type'] == self::ORDER_TYPE_3)
                    $type_text = '付款';
                $row['type'] = $type_text;
                $row['order_no'] = $li['order_no'];
                $return[] = $row;
            }
        }

        if(empty($param['is_export']))
            return $return;


        //变态插件 不能有key
        $return = array_map('array_values',$return);

        //导出
        $header = array(
            //序号
            static::$t->_('order_budget_export_number'),
            //一级预算项目
            static::$t->_('order_budget_export_first_project'),
            //预算执行日期
            static::$t->_('order_budget_export_execute_date'),
            //预算执行金额（本位币）
            static::$t->_('order_budget_export_execute_amount'),
            //预算执行提交人
            static::$t->_('order_budget_export_execute_submitter'),
            //订单类型
            static::$t->_('order_budget_export_order_type'),
            //订单编号
            static::$t->_('order_budget_export_order_no'),
        );
        $file_name = 'budget_export_'.date('Y-m-d').'.xlsx';
        return $this->exportExcel($header, $return, $file_name);
    }




    //根据当前月 所在季度的 位置 获取优先级 m 优先 n 次级优先
    protected function get_season_start($month_int){
        //季度首  后月 =》 后后月
        if($month_int % 3 == 1)
            return $month_int;
        //季度中 前月 =》 后月
        if($month_int % 3 == 2)
            return $month_int - 1;
        //季度 末 前月 =》 前前月
        if($month_int % 3 == 0)
            return $month_int - 2;
    }

    public function get_season_arr($year,$month){
        $arr = array(date('Y-m',strtotime("{$year}-{$month}")));
        for($i = 1; $i < 3; $i++){
            $act_month = $month + $i;
            $arr[] = date("Y-m",strtotime("{$year}-{$act_month}"));
        }

        return $arr;
    }




}
