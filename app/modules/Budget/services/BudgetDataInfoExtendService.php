<?php
/**
 * 预算科目导出类
 * @Date: 2021-09-02 17:24
 * @return:
 **@author: peak pan
 */

namespace App\Modules\Budget\Services;

use App\Library\ErrCode;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Library\Enums\BudgetObjectEnums;


class BudgetDataInfoExtendService extends BudgetService
{


    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 订单类型
     * @Date: 2021-09-02 17:25
     * @return:
     **@author: peak pan
     */
    public static function orderTypeArr(){

        return [
        BudgetService::ORDER_TYPE_1 => static::$t->_('object_type_001'),
        BudgetService::ORDER_TYPE_2 => static::$t->_('object_type_002'),
        BudgetService::ORDER_TYPE_3 => static::$t->_('object_type_003'),
        ];
    }


    /**
     * 导出数据标头
     * @Date: 2021-09-02 17:27
     * @return:
     **@author: peak pan
     */
    public static function headerExportData()
    {
        return [
            static::$t->_('budget_object_name'), // '预算科目'
            static::$t->_('budget_object_is_budget'), // '是否管控'
            static::$t->_('budget_object_account'),//   '预算科目-核算科目编号',
            static::$t->_('budget_object_description_en'),//    '预算科目-核算科目描述(英文)',
            static::$t->_('budget_object_order_type'),//'订单类型(预算科目)',
            static::$t->_('budget_object_is_purchase'),//'是否采购使用',
            static::$t->_('budget_object_effective_year'),//生效年份
            static::$t->_('budget_object_expiration_year'),//失效年份
            static::$t->_('budget_object_product_info_cn'),//'科目明细(中文)',
            static::$t->_('budget_object_product_info_en'),//'科目明细(英文)',
            static::$t->_('budget_object_product_info_th'),//'科目明细(泰文)',
            static::$t->_('budget_object_product_account'),//'核算科目编号',
            static::$t->_('budget_object_product_description_en'),//'核算科目描述(英文)',
            static::$t->_('budget_object_product_type'),//'订单类型(科目明细)'
            static::$t->_('budget_object_is_purchase'),//'是否采购使用',
            static::$t->_('budget_object_product_status'),//状态
        ];
    }


    /**
     * 预算科目导出封装数据
     * @Date: 2021-09-02 17:25
     * @return:
     **@author: peak pan
     */
    public function dataExport(array $params)
    {
        //获取条件数据
        $data = $this->getList($params, true);
        $data = $data['data']['list'] ?? [];

        $new_data = [];
        if (!empty($data)) {
            $budget_object_product_arr = BudgetObjectEnums::$budget_object_product_text;
            foreach ($data as $val) {
                $new_data[] = [
                    $val['name_cn'],
                    $val['is_budget_text'],
                    $val['budget_account'],
                    $val['budget_description'],
                    $val['type_arr'],
                    $val['is_purchase_text'],
                    $val['effective_year'],
                    $val['expiration_year'],
                    $val['name_cn_info'],
                    $val['name_en'],
                    $val['name_th'],
                    $val['budget_product_account'],
                    $val['budget_product_description'],
                    $val['budget_product_info_type'],
                    $val['product_is_purchase_text'],
                    !empty($val['status']) ? static::$t->_($budget_object_product_arr[$val['status']]) : ''
                ];
            }
        }
        //标题
        $header = self::headerExportData();
        $file_name = "BudgetExportDataInfo_Download_" . date("YmdHis");
        $result = $this->exportExcel($header, $new_data, $file_name);
        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
            $result['data'] = $result['data'];
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }

        return $result;
    }

    /**
    * 获取导入的记录列表
    * @Date: 2021-09-03 17:24
    * @author: peak pan
    * @return:
    **/
    public function getList(array $params, bool $is_download = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        $total = 0;

        $page_size = $params['page_size'] ?? 20;
        $page_num = $params['page'] ?? 1;
        $offset = $page_size * ($page_num - 1);
        //整理语言环境
        $name = $this->get_lang_column(static::$language);
        try {

            $columns = "b.{$name} as name_cn,
                        b.budget_account ,
                        b.budget_description,
                        b.is_budget,
                        group_concat(DISTINCT(bo.type)) as type_arr,
                        bop.name_cn as name_cn_info,
                        bop.name_en,
                        bop.name_th,
                        bop.budget_product_account,
                        bop.budget_product_description,
                        group_concat(DISTINCT(bop.type)) as budget_product_info_type,
                        b.effective_year,
                        b.expiration_year,
                        b.is_purchase,
                        bop.status,
                        bop.is_purchase as product_is_purchase	
                       ";
            $builder = $this->modelsManager->createBuilder();

            $builder->from(['b' => BudgetObject::class]);
            $builder->leftjoin(BudgetObjectProduct::class, 'b.level_code = bop.object_code and bop.is_delete = 0', 'bop');
            $builder->leftjoin(BudgetObjectOrder::class, 'b.level_code = bo.level_code and bo.is_delete = 0', 'bo');

            $builder->where('b.is_delete = 0');

            //查询科目名字 都根据现在用户所在的语言环境
            if (!empty($params['object_name'])) {
                $builder->andwhere("b.{$name} like :object_name:", ['object_name' => "%{$params['object_name']}%"]);
            }

            //查询科目明细名字 都根据现在用户所在的语言环境
            if (!empty($params['object_detail'])) {
                $builder->andwhere("bop.{$name} like :object_detail:", ['object_detail' => "%{$params['object_detail']}%"]);
            }

            //查询科目明细名字 都根据现在用户所在的语言环境
            if (!empty($params['object_type'])) {
                $builder->andwhere("bo.type = :object_type:", ["object_type" => $params['object_type']]);
            }

            $builder->columns($columns);
            $builder->groupBy("bop.object_code,bop.name_cn,b.id");
            $builder->orderBy(' b.id desc');
            $total = $builder->getQuery()->execute()->count();
            if (!$is_download) {
                $builder->limit($page_size, $offset);
            }
            if ($total) {
                $data = $builder->getQuery()->execute()->toArray();
                $data = $this->handleDataArr($data);
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->error('科目详情导出 - 获取记录列表数据异常: ' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'total' => $total,
                'list' => $data
            ]
        ];

    }


    /**
     * 格式化预算科目数据
     * @Date: 2021-09-02 15:32
     * @param array $handle_data
     * @return array
     **@author: peak pan
     */
    protected function handleDataArr(array $handle_data)
    {
        $budget_status= self::$budget_status;
        $is_purchase = BudgetObjectEnums::$is_purchase;
        foreach ($handle_data as &$handle_value) {
            $handle_value['type_arr'] = $handle_value['type_arr'] ? $this->toolTypeName(explode(',', $handle_value['type_arr'])) : '';
            $handle_value['budget_product_info_type'] = $handle_value['budget_product_info_type'] ? $this->toolTypeName(explode(',', $handle_value['budget_product_info_type'])) : '';
            $handle_value['is_budget_text'] = static::$t->_($budget_status[$handle_value['is_budget']]);
            $handle_value['is_purchase_text'] = static::$t->_($is_purchase[$handle_value['is_purchase']]);
            $handle_value['product_is_purchase_text'] = $handle_value['product_is_purchase'] ? static::$t->_($is_purchase[$handle_value['product_is_purchase']]) : '';
        }
        return $handle_data;
    }

    /**
     * 通过类型返回值  并用逗号分隔
     * @Date: 2021-09-02 17:13
     * @return:
     **@author: peak pan
     */
    protected function toolTypeName(array $type_arr)
    {
        $type_name_arr = [];
        foreach ($type_arr as $value) {
            $type_name_arr[] = self::orderTypeArr()[$value];
        }
        return implode(',', array_filter($type_name_arr));
    }


}
