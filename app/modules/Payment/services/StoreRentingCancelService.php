<?php

namespace App\Modules\Payment\Services;

class StoreRentingCancelService extends BaseService
{
    public static $validate_cancel = [
        'id' => 'Required|IntGe:1|>>>:id param error',
        'remark' => 'Required|StrLenGeLe:'.parent::REQUIRED_LONG_TEXT_LEN.'|>>>:remark param error'
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingCancelService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
