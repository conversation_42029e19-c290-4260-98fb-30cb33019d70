<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\Warehouse\Services\ThreadService;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Library\Enums\GlobalEnums;

class StoreRentingUpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingUpdateService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 网点租房付款 - 支付
     * @param int $id
     * @param array $data
     * @param array $user
     * @param int $is_from  1 租房付款-付款支付   2 三级支付人 -银行支付
     * @return array
     */
    public function pay(int $id, array $data, array $user,$is_from = 1)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = $this->handleData($data, $user);

        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();

            $pay_staff_id = $this->getPayAuthStaffIdItem();
            if (!in_array($user['id'], $pay_staff_id) && $is_from==1) {
                throw new ValidationException("该工号无网点租房付款支付权限", ErrCode::$STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR);
            }

            $main_model = PaymentStoreRenting::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
            ]);

            if (empty($main_model)) {
                throw new BusinessException("付款支付 - 获取网点租房付款信息失败", ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

            if ($main_model->approval_status != Enums::PAYMENT_APPLY_STATUS_APPROVAL) {
                throw new ValidationException("付款支付 - 网点租房付款审批未通过, 不可支付",
                    ErrCode::$STORE_RENTING_PAYMENT_PAY_APPROVAL_STATUS_ERROR);
            }
            if ($is_from == 1 && $main_model->is_pay_module == 1) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'),
                    ErrCode::$VALIDATE_ERROR);
            }

            $pay_model = new PaymentStoreRentingPay();
            $bool = $pay_model->i_create($data);
            if ($bool === false) {
                throw new BusinessException("付款支付 - 网点租房付款支付失败", ErrCode::$STORE_RENTING_PAYMENT_PAY_CREATE_FAIL_ERROR);
            }

            $main_data = [
                'pay_status'             => $data['is_pay'],
                'updated_at'             => $data['created_at'],
                'last_update_id'         => $data['create_id'],
                'last_update_name'       => $data['create_name'],
                'last_update_department' => $data['create_department_name'],
                'last_update_job_title'  => $data['create_job_title_name'],
                'last_update_at'         => $data['created_at'],
            ];

            $bool = $main_model->i_update($main_data);
            if ($bool === false) {
                throw new BusinessException("付款支付 - 网点租房付款支付状态同步失败", ErrCode::$STORE_RENTING_PAYMENT_PAY_UPDATE_FAIL_ERROR);
            }

            //同步详情的支付状态
            $main_detail_model = PaymentStoreRentingDetail::find([
                'conditions' => 'store_renting_id = :store_renting_id:',
                'bind'       => ['store_renting_id' => $main_model->id],
            ]);
            $main_detail_data = $main_detail_model->toArray();
            
            $country = get_country_code();
            if (
                ($country == GlobalEnums::MY_COUNTRY_CODE && $is_from == 2)
                ||
                ($main_model->payment_method != GlobalEnums::PAYMENT_METHOD_CHECK && $country == GlobalEnums::PH_COUNTRY_CODE && $is_from == 2)
                ||
                $is_from == 1
            ) {
                if (!empty($main_detail_data)) {
                    foreach ($main_detail_model as $main_detail) {
                        $main_detail->pay_status = $data['is_pay'];
                        $main_detail->updated_at = date('Y-m-d H:i:s', time());
                        if ($main_detail->save() === false) {
                            throw new BusinessException("付款支付 - 网点租房付款详情支付状态同步失败", ErrCode::$STORE_RENTING_PAYMENT_PAY_UPDATE_FAIL_ERROR);
                        }
                    }
                }
            }

            //V21475 网点租房合同付款已支付时 - 如果付款明细中关联了租房合同，并且租房合同关联了仓库并且仓库关联了线索，并且线索状态为待付款时将线索状态更新为待入驻，将线索关联的仓库需求状态更新为待入驻
            if (in_array($country, [GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::TH_COUNTRY_CODE]) && $data['is_pay'] == Enums::LOAN_PAY_STATUS_PAY) {
                $contract_ids = array_values(array_unique(array_filter(array_column($main_detail_data, 'contract_no'))));
                switch ($country) {
                    case GlobalEnums::TH_COUNTRY_CODE:
                        ThreadService::getInstance()->syncRelatedTheadPayTH($contract_ids, $user);
                        break;
                    default:
                        ThreadService::getInstance()->syncRelatedTheadPay($contract_ids, $user);
                        break;
                }
            }

            $db->commit();

            $this->delUnReadNumsKeyByStaffIds($this->getPayAuthStaffIdItem());


        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (TxFailed $e) {
            //数据库错误，不可对外抛出
            $code         = ErrCode::$MYSQL_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('store_renting_payment_pay-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 网点租房付款 - 支付提交数据处理
     * @param array $data
     * @param array $user
     * @return array
     */
    private function handleData(array $data, array $user)
    {
        if (empty($data)) {
            return [];
        }

        $data['is_pay'] = intval($data['is_pay']);
        $data['store_renting_id'] = $data['id'];
        $data['create_id'] = $user['id'] ?? 0;
        $data['create_name'] = $this->getNameAndNickName($user['name'] ?? '',$user['nick_name'] ?? '');
        $data['create_department_name'] = '';
        $data['create_job_title_name'] = '';
        unset($data['id']);

        if (!empty($data['create_id'])) {
            $temp = $this->getUserMetaFromBi($data['create_id']);

            if (!empty($temp)) {
                $data['create_department_name'] = $temp['create_display_department_name'];
                $data['create_job_title_name'] = $temp['create_job_title_name'];
            }
        }

        // 未付款
        if ($data['is_pay']== Enums::PAYMENT_PAY_STATUS_NOTPAY) {
            if (isset($data['pay_bank_name'])) {
                unset($data['pay_bank_name']);
            }
            if (isset($data['pay_bank_account'])) {
                unset($data['pay_bank_account']);
            }
            if (isset($data['pay_date'])) {
                unset($data['pay_date']);
            }
        }

        $data['created_at'] = date('Y-m-d H:i:s');

        return $data;
    }
}
