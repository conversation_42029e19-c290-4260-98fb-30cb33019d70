<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Services\BaseService AS PaymentBaseService;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Repository\DepartmentRepository;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class StoreRentingPaymentFlowService extends AbstractFlowService
{

    /**
     * 网点租房付款审批 - 通过操作
     * @param $id
     * @param $note
     * @param $user
     * @param $update_data
     * @return array
     */
    public function approve($id, $note, $user, $update_data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');

        $logger = $this->getDI()->get('logger');
        try {
            $db->begin();

            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$STORE_RENTING_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = PaymentStoreRenting::getFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if ($main_model->approval_status == Enums::PAYMENT_APPLY_STATUS_WITHDRAW) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->approval_status == Enums::PAYMENT_APPLY_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->approval_status == Enums::PAYMENT_APPLY_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            $logger->info('付款管理 - 网点租房付款 - 审批通过 - 主表更新前数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));

            // 业务主表待更新数据
            $update_main_data = [];

            // 是否可编辑指定字段
            // [1] 权限校验
            $can_edit = (new StoreRentingPaymentFlowService())->getCanEditFieldByReq($work_req, $user['id']);

            // [2] 编辑更新
            if (!empty($can_edit) && !empty($update_data)) {
                $ids = array_column($update_data, 'detail_id');
                $amount_detail = PaymentStoreRentingDetail::find([
                    'conditions' => 'store_renting_id = :store_renting_id: AND id IN ({ids:array})',
                    'bind' => ['store_renting_id' => $main_model->id, 'ids' => $ids],
                ]);

                if (empty($amount_detail)) {
                    throw new BusinessException('金额详情数据为空, 不可继续编辑', ErrCode::$STORE_RENTING_PAYMENT_APPROVAL_WHT_UPDATE_ERROR);
                }

                $amount_detail_array = $amount_detail->toArray();
                $logger->info('付款管理 - 网点租房付款 - 审批通过 - 金额详情更新前数据: ' . json_encode($amount_detail_array, JSON_UNESCAPED_UNICODE));
                $amount_detail_array = array_column($amount_detail_array, null, 'id');

                $wht_tax_config = EnumsService::getInstance()->getWhtRateMap();

                $wht_total_amount = 0;
                $actually_total_amount = 0;
                foreach ($update_data as $_key => $_value) {
                    if (!isset($amount_detail_array[$_value['detail_id']])) {
                        throw new ValidationException('detail_id param error', ErrCode::$VALIDATE_ERROR);
                    }

                    // 提取WHT类别
                    $wht_category_id = $_value['wht_category_id'];
                    if (empty($wht_tax_config[$wht_category_id])) {
                        throw new ValidationException(self::$t['payment_upload_error_007'], ErrCode::$VALIDATE_ERROR);
                    }

                    // 提取WHT税率
                    if (empty($wht_tax_config[$wht_category_id]['rate_list'][$_value['wht_tax_rate_id']])) {
                        throw new ValidationException(self::$t['payment_upload_error_008'], ErrCode::$VALIDATE_ERROR);
                    }

                    $_value['wht_category'] = $wht_tax_config[$wht_category_id]['label'];
                    $_value['wht_tax_rate'] = $_value['wht_tax_rate_id'];

                    $amount_has_tax = $amount_detail_array[$_value['detail_id']]['amount_has_tax'] ?? 0;
                    $_value['wht_amount'] = round($_value['wht_amount'], 2);
                    $_value['actually_amount'] = round($amount_has_tax - $_value['wht_amount'], 2);

                    // 凭证描述和费用所属中心更新
                    if (isset($_value['sap_supplier_no'])) {
                        $_value['sap_supplier_no'] = trim($_value['sap_supplier_no']);
                    }
                    if (isset($_value['certificate_desc'])) {
                        $_value['certificate_desc'] = trim($_value['certificate_desc']);
                    }
                    if (isset($_value['cost_center_code'])) {
                        $_value['cost_center_code'] = trim($_value['cost_center_code']);
                    }

                    // 重新计算WHT总额 和 实付总额
                    $wht_total_amount += $_value['wht_amount'];
                    $actually_total_amount += $_value['actually_amount'];

                    $update_data[$_key] = $_value;
                }

                $update_data = array_column($update_data, null, 'detail_id');

                // 更新detail表
                foreach ($amount_detail as $detail_model) {
                    $_tmp_detail = $update_data[$detail_model->id];
                    $_tmp_update_detail = [
                        'wht_category' => $_tmp_detail['wht_category'],
                        'wht_tax_rate' => $_tmp_detail['wht_tax_rate'],
                        'wht_amount' => $_tmp_detail['wht_amount'],
                        'actually_amount' => $_tmp_detail['actually_amount'],
                        'ledger_account_id' => $_tmp_detail['ledger_account_id']
                    ];
                    if (isset($_tmp_detail['sap_supplier_no'])) {
                        $_tmp_update_detail['sap_supplier_no'] = $_tmp_detail['sap_supplier_no'];
                    }
                    if (isset($_tmp_detail['certificate_desc'])) {
                        $_tmp_update_detail['certificate_desc'] = $_tmp_detail['certificate_desc'];
                    }
                    if (isset($_tmp_detail['cost_center_code'])) {
                        $_tmp_update_detail['cost_center_code'] = $_tmp_detail['cost_center_code'];
                    }

                    if ($detail_model->i_update($_tmp_update_detail) === false) {
                        throw new BusinessException('网点租房付款金额详情表更新失败', ErrCode::$STORE_RENTING_PAYMENT_AMOUNT_DETAIL_UPDATE_ERROR);
                    }

                    $logger->info('付款管理 - 网点租房付款 - 审批通过 - 金额详情更新后数据: ' . json_encode($detail_model->toArray(), JSON_UNESCAPED_UNICODE));
                }

                // 业务主表 wht 相关待更新数据
                $update_main_data['wht_total_amount'] = $wht_total_amount;
                $update_main_data['actually_total_amount'] = $actually_total_amount;
            }

            // 审批
            $ws = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getStoreRentingPaymentWorkflowParams($main_model, $user), $note);
            //是否是ap泰国节点之后
            if ($main_model->is_after_ap_th != 1 && $ws->isAfterApTH($result)) {
                $update_main_data['is_after_ap_th'] = 1;
            }

            // 全部审批通过
            if (!empty($result->approved_at)) {
                $update_main_data['approval_status'] = Enums::PAYMENT_APPLY_STATUS_APPROVAL;
                $update_main_data['approved_at'] = $result->approved_at;
                //终审行数据过账日期取值审批日期
                $amount_detail = PaymentStoreRentingDetail::find([
                    'conditions' => 'store_renting_id = :store_renting_id:',
                    'bind' => ['store_renting_id' => $id],
                ]);
                $amount_detail=  $amount_detail->toArray();


                $detail_ids = array_column($amount_detail,'id');
                $detail_ids = implode(',',$detail_ids);
                $db->updateAsDict(
                    (new PaymentStoreRentingDetail())->getSource(),
                    [
                        'transaction_date' => date('Y-m-d')
                    ],
                    [
                        'conditions' => " id IN ($detail_ids)",
                    ]
                );
            }

            // 补充业务主表数据
            $main_bool = null;
            if (!empty($update_main_data)) {
                $user_base_info = (new PaymentBaseService())->getUserMetaFromBi($user['id']);
                $update_main_data['updated_at'] = date('Y-m-d H:i:s');
                $update_main_data['last_update_id'] = $user['id'] ?? 0;
                $update_main_data['last_update_name'] = $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? '');
                $update_main_data['last_update_department'] = $user_base_info['create_display_department_name'] ?? '';
                $update_main_data['last_update_job_title'] = $user_base_info['create_job_title_name'] ?? '';
                $update_main_data['last_update_at'] = date('Y-m-d H:i:s');

                // 更新业务主表数据
                $main_bool = $main_model->i_update($update_main_data);
                if ($main_bool === false) {
                    throw new BusinessException('网点租房付款主表更新失败', ErrCode::$STORE_RENTING_PAYMENT_UPDATE_ERROR);
                }

                $logger->info('付款管理 - 网点租房付款 - 审批通过 - 主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));
            }

            // 全部审核通过 且 数据更新成功
            if (!empty($result->approved_at) && $main_bool) {
                //给支付人员发送待支付信息邮件
                $pay_staff_id = (new PaymentBaseService())->getPayAuthStaffIdItem();
                $this->sendEmailToAuditors($work_req, $pay_staff_id, 1);
                $this->delUnReadNumsKeyByStaffIds($pay_staff_id);
                //同步数据到支付模块
                if (EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_PAYMENT, $main_model->cost_company_id)) {
                    PayService::getInstance()->saveOne($main_model);
                }
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('store_renting_payment-approve-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 网点租房付款申请 - 驳回
     * @param $main_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($main_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $work_req = $this->getRequest($main_id);
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$STORE_RENTING_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = PaymentStoreRenting::getFirst([
                'id = :id:',
                'bind' => ['id' => $main_id]
            ]);

            if ($main_model->approval_status == Enums::CONTRACT_STATUS_CANCEL) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->approval_status == Enums::PAYMENT_APPLY_STATUS_APPROVAL) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->approval_status == Enums::PAYMENT_APPLY_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('payment_store_renting_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getStoreRentingPaymentWorkflowParams($main_model, $user), $note);

            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$STORE_RENTING_PAYMENT_WORK_FLOW_REJECT_ERROR);
            }

            $user_base_info = (new PaymentBaseService())->getUserMetaFromBi($user['id']);

            $bool = $main_model->i_update([
                'approval_status' => Enums::PAYMENT_APPLY_STATUS_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'rejected_at' => $result->rejected_at,
                'refuse_reason' => $note,
                'pay_status'=> Enums::PAYMENT_PAY_STATUS_NOTPAY,
                'last_update_id' => $user['id'],
                'last_update_name' => $this->getNameAndNickName($user['name'] ?? '', $user['nick_name']),
                'last_update_department' => $user_base_info['create_display_department_name'],
                'last_update_job_title' => $user_base_info['create_job_title_name'],
                'last_update_at' => date('Y-m-d H:i:s'),
            ]);

            if ($bool === false) {
                throw new BusinessException('网点租房付款主表更新失败', ErrCode::$STORE_RENTING_PAYMENT_UPDATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('store_renting_payment-reject-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 网点租房付款申请 - 撤回
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $work_req = $this->getRequest($id);
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            $store_renting_model = PaymentStoreRenting::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);
            if (empty($store_renting_model)) {
                throw new BusinessException(static::$t->_('payment_store_renting_get_info_failed_when_update'), ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

            // 只有待审核的，方可撤回
            if ($store_renting_model->approval_status != 1) {
                throw new ValidationException(static::$t->_('payment_store_renting_approval_status_error_when_update'), ErrCode::$STORE_RENTING_PAYMENT_NOT_PENDING_CANCEL_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getStoreRentingPaymentWorkflowParams($store_renting_model, $user), $note);
            if ($result === false) {
                throw new BusinessException('审批流拒绝失败', ErrCode::$STORE_RENTING_PAYMENT_WORK_FLOW_REJECT_ERROR);
            }

            $user_base_info = (new PaymentBaseService())->getUserMetaFromBi($user['id']);
            $bool = $store_renting_model->i_update([
                'approval_status' => Enums::PAYMENT_APPLY_STATUS_WITHDRAW,
                'updated_at' => date('Y-m-d H:i:s'),
                'cancel_at' => date('Y-m-d H:i:s'),
                'cancel_reason' => $note,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY,
                'last_update_id' => $user['id'],
                'last_update_name' => $this->getNameAndNickName($user['name'] ?? '', $user['nick_name']),
                'last_update_department' => $user_base_info['create_display_department_name'],
                'last_update_job_title' => $user_base_info['create_job_title_name'],
                'last_update_at' => date('Y-m-d H:i:s'),

            ]);

            if ($bool === false) {
                throw new BusinessException(static::$t->_('contract_withdrawal_failed'), ErrCode::$CONTRACT_CANCEL_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('loan-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 获取审批流信息
     * @param $store_renting_id
     * @return Model
     */
    public function getRequest($store_renting_id)
    {
        return $this->getRequestByBiz($store_renting_id, Enums::WF_PAYMENT_STORE_RENTING_TYPE);
    }

    /**
     * 网点租房付款申请 - 注入审批流
     *
     * @param int $biz_id
     * @param array $user
     * @param array $info 提交的数据
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($biz_id, $user, $info = [])
    {
        $model = PaymentStoreRenting::findFirst([
            'id = :id:',
            'bind' => ['id' => $biz_id]
        ]);

        $data['id'] = $model->id;
        $data['name'] = $model->apply_no . '审批申请';
        $data['biz_type'] = Enums::WF_PAYMENT_STORE_RENTING_TYPE;
        $data['flow_id'] = $this->getFlowId($user, $model, $info);

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getStoreRentingPaymentWorkflowParams($model, $user));
    }

    /**
     * 获取网点租房付款审批流需要数据
     * @param $main_model
     * @param $user
     * @return array
     */
    public function getStoreRentingPaymentWorkflowParams($main_model, $user)
    {
        // 金额根据币种汇率转换为系统默认币种的额度
        if($main_model->ver==1){//区分版本
            $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($main_model->tax_total_amount, $main_model->exchange_rate, 2);
        }else{
            $default_currency_amount = (new EnumsService())->amountExchangeRateCalculation($main_model->total_amount, $main_model->exchange_rate, 2);

        }
        // 是否需要部门审核人审批
        if ($main_model->id) {
            $paymentStoreRentingDetails = PaymentStoreRentingDetail::find(
                [
                    'conditions'=>'store_renting_id = :id: and is_contract=0',
                    'bind'=>['id'=>$main_model->id]
                ]
            );
            $isManagerApprove = count($paymentStoreRentingDetails) > 0 ? 1 : 0;
        }

        // 菲律宾的快递公司 Network Management
        $sys_department_ids = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_ids');
        $store_category = (($sys_department_ids['network_management'] ?? '') == $main_model->create_department_id) ? 1 : 0;


        return [
            'total_amount'   => $default_currency_amount,
            'currency' => $main_model->currency,
            'staff_id' => env('default_auditor', 20254),
            'submitter_id' => $main_model->create_id, // 申请人用，审批流创建人
            'job_title_level' => $main_model->create_job_title_level,
            'department_id' => $main_model->create_department_id,
            'node_department_id' => $main_model->create_node_department_id,
            'need_manager_approve' => isset($isManagerApprove) && !empty($isManagerApprove) ? 1 : 0,
            'store_category' => $store_category,
            'create_company_id'  => $main_model->cost_company_id,   //为了取财务分组审批人,需要定义create_company_id=费用所属公司

        ];
    }

    /**
    * 获取审批流
    * @param array $user
    * @param object $model
    * @param array  $info
    * @throws ValidationException
    * @return  int
    **/
    public function getFlowId($user = [], $model = null, $info = [])
    {
        $cost_company_id = $model->cost_company_id;
        $data            = $info['data'];
        $is_contract     = true;
        $contract_arr    = array_unique(array_column($data['amount_detail'], 'is_contract'));
        if (in_array(0, $contract_arr)) {
            //无关联合同
            $is_contract = false;
        }

        //费用所属公司是快递公司
        $company_ids        = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_company_ids');
        $department_arr     = EnumsService::getInstance()->getSettingEnvValueMap('payment_store_renting_department_ids');
        $is_express_company = false;
        if ($data['cost_company_id'] == $company_ids['FlashExpress']) {
            $is_express_company = true;
        }
        //申请人是否属于快递公司

        $is_apply_company  = false;
        $department        = (new DepartmentRepository());
        $flash_express_sub = $department->getDepartmentSubListByIds($company_ids['FlashExpress']);
        $flash_express_sub = array_column($flash_express_sub, 'id');
        if (in_array($user['node_department_id'], $flash_express_sub)) {
            $is_apply_company = true;
        }

        $cpo_department_id  = $department_arr['cpo'];//cpo及以下
        $cpo_department_ids = $department->getDepartmentSubListByIds($cpo_department_id);
        $cpo_department_ids = array_column($cpo_department_ids, 'id');
        $is_cpo_department  = false;
        if (in_array($user['node_department_id'], $cpo_department_ids)) {
            $is_cpo_department = true;
        }

        $flow_id = '';
        $country = get_country_code();
        if ($country == GlobalEnums::TH_COUNTRY_CODE) {
            $wpm_department_id = $department_arr['wpm'];//wpm部门及子部门
            $wpm_department_id = $department->getDepartmentSubListByIds($wpm_department_id);
            $wpm_department_id = array_column($wpm_department_id, 'id');
            $is_wpm_department = false;
            if (in_array($user['node_department_id'], $wpm_department_id)) {
                $is_wpm_department = true;
            }
            $fex_department_id  = $department_arr['Flash Express'];//Flash Express 及以下
            $fex_department_ids = $department->getDepartmentSubListByIds($fex_department_id);
            $fex_department_id  = array_column($fex_department_ids, 'id');
            //申请人是否属于fex部门
            $is_fex_company = false;
            if (in_array($user['node_department_id'], $fex_department_id)) {
                $is_fex_company = true;
            }
            $many_department_ids = explode(',', $department_arr['many_department_ids']);
            $many_department_ids = $department->getManyDepartmentSonList($many_department_ids);
            //配置部门
            $is_config_many_department = false;
            if (in_array($user['node_department_id'], $many_department_ids)) {
                $is_config_many_department = true;
            }
            $this->logger->info('th_store_renting_payment_flow_service ;费用所属公司是否快递公司' . $is_express_company . ' / 是否有关联合同' . $is_contract . ' /  申请人属于CPO' . $is_cpo_department . ' /  申请人属于WPM部门及子部门' . $is_wpm_department . ' /  申请人不属于快递' . $is_apply_company . ' / 申请人是否属于fex部门' . $is_fex_company . ' / 配置部门' . $is_config_many_department);
            //费用所属公司不是快递公司
            if (!$is_express_company) {
                //无关联合同
                if (!$is_contract) {
                    $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_NOT_CONTRACT_FLOW_ID;
                } else {
                    //有关联合同
                    $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_CONTRACT_FLOW_ID;
                }
            } else {
                //费用所属公司是快递公司
                if ($is_cpo_department || $is_config_many_department) {//申请人属于CPO及下属组织 或 配置部门 （Management（组织ID=25）（含子部门））
                    if (!$is_contract) {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_NOT_CONTRACT_FLOW_ID;
                    } else {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_CONTRACT_FLOW_ID;
                    }
                } elseif ($is_wpm_department) {//申请人属于WPM部门及子部门
                    if (!$is_contract && $is_fex_company) {
                        //无关联合同
                        $flow_id = Enums::PAYMENT_STORE_RENTING_AT_WPM_NOT_CONTRACT_FLOW_ID;
                    } else {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_AT_WPM_CONTRACT_FLOW_ID;
                    }
                } elseif (!$is_apply_company && !$is_cpo_department) {
                    if ($is_contract) {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_AT_WPM_CONTRACT_FLOW_ID;
                    } else {
                        throw new ValidationException(static::$t->_('unassociated_payment_store_renting_not_applicable'), ErrCode::$VALIDATE_ERROR);
                    }
                } else {
                    //其他场景）不属于以上任何条件时
                    if (!$is_contract) {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_NOT_CONTRACT_FLOW_ID;
                    } else {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_DELIVERY_CONTRACT_FLOW_ID;
                    }
                }
            }

        } else if ($country == GlobalEnums::MY_COUNTRY_CODE) {

            $cost_company   = Enums\ContractEnums::FLASH_EXPRESS_MY_ID; //Flash Malaysia Express;
            $is_fme_company = false;
            if ($data['cost_company_id'] == $cost_company) {
                $is_fme_company = true;
            }

            $this->logger->info('my_store_renting_payment_flow_service ; 是否有关联合同' . $is_contract . ' /  ' . '申请人属于CPO' . $is_cpo_department . ' /  ' . '申请人不属于快递' . $is_apply_company . ' /  ' . '费用公司是否Flash Malaysia Express' . $is_fme_company . ' /  ');

            //费用所属公司=Flash Malaysia Express
            if ($is_fme_company) {
                if (!$is_apply_company && !$is_cpo_department) {
                    if ($is_contract) {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_NOT_CPO_FLOW_ID;
                    } else {
                        throw new ValidationException(static::$t->_('unassociated_payment_store_renting_not_applicable'), ErrCode::$VALIDATE_ERROR);
                    }
                } else if ($is_apply_company || $is_cpo_department) {//且无关联合同的时候
                    if ($is_contract) {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_FME_CONTRACT_FLOW_ID;
                    } else {
                        $flow_id = Enums::PAYMENT_STORE_RENTING_FME_NOT_CONTRACT_FLOW_ID;
                    }
                }

            } else {
                if ($is_contract) {
                    $flow_id = Enums::PAYMENT_STORE_RENTING_FME_CONTRACT_FLOW_ID;
                } else {
                    $flow_id = Enums::PAYMENT_STORE_RENTING_FME_NOT_CONTRACT_FLOW_ID;
                }
            }

        } else if ($country == GlobalEnums::PH_COUNTRY_CODE) {
            $node_department_id = $model->create_node_department_id;
            //跟据直属部门查找部门链是否属于cpo
            $department_info = DepartmentModel::getFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $node_department_id]]
            );
            if (empty($department_info)) {
                throw new ValidationException(static::$t->_('not_found_sales_flow_id'), ErrCode::$VALIDATE_ERROR);
            }
            $sys_department_ids = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_ids');

            $ancestry_v3 = explode('/', $department_info->ancestry_v3);
            if ($cost_company_id != $company_ids['FlashExpress']) { //费用公司不为flash express
                return Enums::PAYMENT_STORE_RENTING_FFM_FLOW_ID;
            } else {
                if (in_array($sys_department_ids['warehouse_procurement_department'], $ancestry_v3) || ($model->create_company_id != $company_ids['FlashExpress'] && !in_array($sys_department_ids['cpo_department'], $ancestry_v3))) {
                    //费用公司flash express 且申请人 (wpd 或申请公司不为flash express)
                    return Enums::PAYMENT_STORE_RENTING_NW_FLOW_ID;
                } else {
                    return Enums::PAYMENT_STORE_RENTING_HUB_OTHER_FLOW_ID;

                }
            }
        } else {
            $is_ffm = (new DepartmentService())->isSonCompanyId($cost_company_id);
            if ($is_ffm) {
                $flow_id = Enums::PAYMENT_STORE_RENTING_FFM_FLOW_ID;
            } else {
                $flow_id = Enums::PAYMENT_STORE_RENTING_WF_ID;
            }
        }

        if ($flow_id == '') {
            throw new ValidationException(static::$t->_('not_found_sales_flow_id'), ErrCode::$VALIDATE_ERROR);
        }
        return $flow_id;
    }
}
