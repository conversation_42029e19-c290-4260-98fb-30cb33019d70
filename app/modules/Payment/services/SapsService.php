<?php
/**
 * Created by PhpStorm.
 * Date: 2022/4/29
 * Time: 14:06
 */

namespace App\Modules\Payment\Services;
use App\Library\Validation\ValidationException;
use App\Modules\Purchase\Services\SapsService as SapService;
use App\Modules\Reimbursement\Models\RequestSapLog;


class SapsService extends BaseService
{
    const SAP_NO_ISSET = 1;//sap 单号存在

    private static $instance;
    private static $apiPaths;
    private static $user;
    private static $passWord;



    private function __construct(){
        self::$apiPaths = env('sap_interface_url','');
        self::$user = env('sap_user_id_3','_BYDHOST');
        self::$passWord = env('sap_pwd_3','Welcome1');
    }

    /**
     * @return  SapsService
     */
    public static function getInstance(){
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }
    /**
     * 发送请
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function httpRequestXml($method, $postData)
    {
        try {
            $curl      = curl_init();
            $header[]  = "Content-type: text/xml";
            $basic_key = self::$user . ":" . self::$passWord;
            $header[]  = "Authorization: Basic " . base64_encode($basic_key); //添加头，在name和pass处填写对应账号密码
            if (get_runtime_env() != 'dev') {
                curl_setopt($curl, CURLOPT_PROXY, env('proxy_ip')); //代理服务器地址
                curl_setopt($curl, CURLOPT_PROXYPORT, env('proxy_port')); //代理服务器端口
            }
            curl_setopt($curl, CURLOPT_URL, self::$apiPaths.$method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl,CURLOPT_CONNECTTIMEOUT, 180);
            curl_setopt($curl, CURLOPT_TIMEOUT, 300);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . curl_error($curl));

            }
            curl_close($curl);// 根据头大小去获取头信息内容

            return  $responseText;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $data    = [];
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . $message);
        }


    }

    /**
     *     作用：将xml转为array
     */
    public function xmlToArray($xml)
    {

        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }

    public function PaymentRentSap($data){

        $item_xml = '';
        $wht_tax_xml='';
        if (get_country_code() == 'PH' && !empty($data['wht_tax_code'])) {
            $wht_tax_xml= '<WithholdingTaxationCharacteristicsCode listID="'.get_country_code().'" listAgencyID="http://0050105282-one-off.sap.com/YCB5ESA8Y_">'.$data['wht_tax_code'].'</WithholdingTaxationCharacteristicsCode>';
        }

        if($data['vat_rate']!=0&&$data['wht_tax_rate']!=0){
            $item_xml='<Item actionCode="01">
               <BusinessTransactionDocumentItemTypeCode>80</BusinessTransactionDocumentItemTypeCode>	      
               <!--描述:-->
               <SHORT_Description languageCode="ZH">VAT</SHORT_Description>		
			 <!--净金额:-->
                  <NetAmount currencyCode="' . $data['currency'] . '">'.$data['vat_amount'].'</NetAmount>
               <!--净价:-->
               <NetUnitPrice>
                  <Amount currencyCode="' . $data['currency'] . '">0</Amount>                  
               </NetUnitPrice>         
              <AccountingCodingBlockDistribution ActionCode="01" AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true">	
                  <AccountingCodingBlockAssignment ActionCode="01">	
                     <!--账户分配类型:ACC - 分配科目-->	
                     <AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>	 
                      <!--成本中心-->	
                     <CostCentreID>'.$data['cost_center_code'].' </CostCentreID>	
                  </AccountingCodingBlockAssignment>	
               </AccountingCodingBlockDistribution>	         
               <!--产品税:-->
               <ProductTax actionCode="01">
                  <!--税务明细-税务代码:-->
                  <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">'.$data['vat_code'].'</ProductTaxationCharacteristicsCode>
                  <CountryCode>'.get_country_code().'</CountryCode>
               </ProductTax>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SupplierInvoiceBundleMaintainRequest_sync>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
     <SupplierInvoice actionCode="01" itemListCompleteTransmissionIndicator="true">
         	 <!--817预付·定金申请:-->
            <BusinessTransactionDocumentTypeCode>817</BusinessTransactionDocumentTypeCode>
            <!--发票描述:-->
            <MEDIUM_Name></MEDIUM_Name>
            <!--发票日期:-->
            <Date>'.$data['create_date'].'</Date>
            <!--接收日期:-->
            <ReceiptDate>'.$data['create_date'].'</ReceiptDate>
            <!--过账日期:-->
            <TransactionDate>'.$data['transaction_date'].'</TransactionDate>
            <!--Optional:-->
            <DocumentItemGrossAmountIndicator>false</DocumentItemGrossAmountIndicator>
            <!--Optional:-->
            <ManualEntryERSIndicator>false</ManualEntryERSIndicator>
            <!--总金额:-->
            <GrossAmount currencyCode="' . $data['currency'] . '">'.$data['total_amount'].'</GrossAmount>
            <!--总税额:-->
            <Status>
               <!--状态:-->
               <DataEntryProcessingStatusCode>3</DataEntryProcessingStatusCode>
            </Status>
                <!--外部单据编号:-->
            <CustomerInvoiceReference actionCode="01">
               <BusinessTransactionDocumentReference>
                  <ID>'.$data['apply_index_no'].'</ID>
               </BusinessTransactionDocumentReference>
            </CustomerInvoiceReference>
          
            <!--买方公司:-->
            <BuyerParty actionCode="01">
               <!--Optional:-->
               <PartyKey>
                  <!--固定值:-->
                  <PartyTypeCode>147</PartyTypeCode>
                  <!--Optional:-->
                  <PartyID>'.$data['cost_company'].'</PartyID>
               </PartyKey>
            </BuyerParty>
            <!--供应商:-->
            <SellerParty actionCode="01">
               <PartyKey>
                  <!--固定值:-->
                  <PartyTypeCode>147</PartyTypeCode>
                  <!--Optional:-->
                  <PartyID>'.$data['sap_supplier_no'].'</PartyID>
               </PartyKey>
            </SellerParty>
            <!--统驭科目-->
            <ReconciliationAccount>'.$data['account'].'</ReconciliationAccount>

		 <PaymentControl ActionCode="01">         
               <!--负责员工l:-->
               <ResponsibleEmployeeID>U002</ResponsibleEmployeeID>
 			</PaymentControl>
 		
	 	 <Item actionCode="01">
               <BusinessTransactionDocumentItemTypeCode>80</BusinessTransactionDocumentItemTypeCode>	      
               <!--描述:-->
               <SHORT_Description languageCode="ZH">Net price</SHORT_Description>		
			 <!--净金额:-->
                  <NetAmount currencyCode="' . $data['currency'] . '">'.$data['amount'].'</NetAmount>
               <!--净价:-->
               <NetUnitPrice>
                  <Amount currencyCode="' . $data['currency'] . '">0</Amount>                  
               </NetUnitPrice>         
              <AccountingCodingBlockDistribution ActionCode="01" AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true">	
                  <AccountingCodingBlockAssignment ActionCode="01">	
                     <!--账户分配类型:ACC - 分配科目-->	
                     <AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>	 
                      <!--成本中心-->	
                     <CostCentreID>'.$data['cost_center_code'].'</CostCentreID>	
                  </AccountingCodingBlockAssignment>	
               </AccountingCodingBlockDistribution>	         
               <!--产品税:-->
               <ProductTax actionCode="01">
                  <!--税务明细-税务代码:-->
                  <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">'.$data['vat_code'].'</ProductTaxationCharacteristicsCode>
                  <!--代扣代缴税说明-代扣代缴税代码:-->'
                    .$wht_tax_xml.
                  '<!--Optional:-->
                  <CountryCode>'.get_country_code().'</CountryCode>
               </ProductTax>
            </Item>'

            .$item_xml.

         '</SupplierInvoice>
      </glob:SupplierInvoiceBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';



        $xml = $this->httpRequestXml($method = '/sap/managesupplierinvoicein', $post_xml);
        preg_match_all("/\<SupplierInvoice\>(.*?)\<\/SupplierInvoice\>/s", $xml, $re_data);
        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapService::getInstance()->xmlToArray($re_data[0][0]);
        }

        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = $this->xmlToArray($log[0][0]);
        }
        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_data['UUID'] ?? '','order_code'=>$data['apply_index_no'], 'type' => 5, 'request_data' => $post_xml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

        return $return_data;
    }






}
