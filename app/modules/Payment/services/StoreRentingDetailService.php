<?php

namespace App\Modules\Payment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\BankFlowOaRelModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\WaterMarkerService;
use App\Modules\Contract\Services\ContractStoreRentingService;
use App\Modules\Pay\Services\PayFlowService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Payment\Models\PaymentStoreRentingPay;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Models\AttachModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Util\RedisKey;
use Mpdf\Mpdf;
use App\Library\OssHelper;

class StoreRentingDetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return StoreRentingDetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取网点租房付款详情
     * @param int $id
     * @param int $uid
     * @param boolean $if_download
     * @param bool $is_audit
     * @return array|mixed
     * @throws BusinessException
     */
    public function getDetail(int $id, int $uid = 0, bool $is_audit = false, bool $if_download = false)
    {
        $main_model = PaymentStoreRenting::findFirst([
            'id = :id:',
            'bind' => ['id' => $id]
        ]);

        if(empty($main_model)){
            return [];
        }

        $req = (new StoreRentingPaymentFlowService())->getRequest($id);
        if (empty($req->id)) {
            throw new BusinessException('网点租房付款获取工作流批次失败', ErrCode::$STORE_RENTING_PAYMENT_GET_WORK_REQUEST_ERROR);
        }

        $data = $main_model->toArray();

        //待回复征询ID
        $ask = (new FYRService())->getRequestToByReplyAsk($req,$uid);
        $data['ask_id'] = $ask ? $ask->id:'';

        // 获取支付信息
        $pay_conditions = [
            'conditions' => 'store_renting_id = :store_renting_id:',
            'bind' => ['store_renting_id' => $data['id']],
            'columns' => ['create_id', 'is_pay', 'pay_bank_name', 'pay_bank_account', 'pay_date', 'remark', 'created_at']
        ];

        $pay = PaymentStoreRentingPay::getFirst($pay_conditions);

        // 获取附件信息
        $file = AttachModel::find([
            'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type: and sub_type = 0',
            'bind' => ['oss_bucket_key' => $data['id'], 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT],
            'columns' => ['bucket_name', 'object_key', 'file_name','id']
        ]);

        // 金额详情
        $amount_detail = PaymentStoreRentingDetail::find([
            'conditions' => 'store_renting_id = :store_renting_id:',
            'bind' => ['store_renting_id' => $data['id']],
        ]);

        $data['event_file'] = $file ? $file->toArray() : [];

        $data['pay_info'] = $pay ? $pay->toArray() : null; // 应前端要求，[] 改为 null


        $data['amount_detail_item'] = $amount_detail ? $amount_detail->toArray() : [];

        if (get_country_code() == GlobalEnums::MY_COUNTRY_CODE && $data['payment_method'] == GlobalEnums::PAYMENT_METHOD_CHECK) {
            $detail_ids = array_values(array_column($data['amount_detail_item'], 'id'));
            $oa_rel     = BankFlowOaRelModel::find([
                'conditions' => 'detail_id in ({detail_id:array})',
                'bind'       => ['detail_id' => $detail_ids],
                'order'      => 'id asc',
                'limit'      => 1
            ])->toArray();
            if (!empty($oa_rel)) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns("2 as is_pay,bfo.created_id, bfo.created_at,  bf.bank_account as pay_bank_account ,bf.bank_name as pay_bank_name, DATE_FORMAT(bfo.created_at, '%Y-%m-%d') as pay_date");
                $builder->from(['bfo' => BankFlowOaModel::class]);
                $builder->leftjoin(BankFlowModel::class, 'bfo.bank_flow_id = bf.id', 'bf');
                $builder->where('bfo.id = :id:', ['id' => $oa_rel[0]['bank_flow_oa_id']]);
                $data['pay_info'] = $builder->getQuery()->execute()->getFirst()->toArray();
            }
        }
        // 审批日志
        $data['auth_logs'] = $this->getAuditLogs($req, $data, $if_download);

        // 审批节点: 可编辑的字段: AP, APS 泰, APS 北 可编辑WHT类别/WHT税率
        $data['can_edit'] = false;
        if ($is_audit) {
            $data['can_edit'] = (new StoreRentingPaymentFlowService())->getCanEditFieldByReq($req, $uid);
        }

        // 费用所属部门
        if (!empty($data['cost_department_id'])) {
            $cost_department = DepartmentModel::getFirst([
                'conditions' => 'id = :id:',
                'columns' => 'name',
                'bind' => ['id' => $data['cost_department_id']]
            ]);
            $data['cost_department_name'] = $cost_department->name ?? '';
        } else {
            $data['cost_department_name'] = $data['cost_center_department_name'] ?? '';
        }


        $attach_list = AttachModel::find([
            'columns'    => ['id', 'bucket_name', 'object_key', 'file_name', 'oss_bucket_key'],
            'conditions' => 'oss_bucket_key = :oss_bucket_key: and oss_bucket_type = :oss_bucket_type:',
            'bind'       => [
                'oss_bucket_key'  => $id,
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT_SUPPLEMENT
            ],
        ])->toArray();


        $attach_supplement_list = [];
        foreach ($attach_list as $item) {
            $attach_supplement_list[] = $item;
        }

        $data['required_supplement_file'] = $attach_supplement_list;
        $data['attachment_list'] = $this->getConfigAttachmentList($main_model);

        return $this->handleData($data);
    }

    /**
     * 获取网点租房付款-配置附件清单
     * @param object $payment_store_renting_info 网点租房付款对象组
     * @return array
     */
    private function getConfigAttachmentList($payment_store_renting_info)
    {
        $attachment_list = [];
        $attachment_config_list = $this->getAttachmentAllConfig();
        if (!empty($attachment_config_list)) {
            $upload_attachment_list = $payment_store_renting_info->getSelfAttachments()->toArray();
            $exist_file_list_by_sub_type_map = [];
            foreach ($upload_attachment_list as $file) {
                $exist_file_list_by_sub_type_map[$file['sub_type']][] = [
                    'id' => $file['id'],
                    'bucket_name' => $file['bucket_name'],
                    'object_key' => $file['object_key'],
                    'file_name' => $file['file_name'],
                ];
            }
            unset($upload_attachment_list);
            if ($exist_file_list_by_sub_type_map) {
                foreach ($attachment_config_list['attachment_list'] as $file_field => $attr) {
                    $attachment_list[$file_field] = $exist_file_list_by_sub_type_map[$attr['file_sub_type']] ?? [];
                }
            }
        }
        return $attachment_list ? $attachment_list : (object)$attachment_list;
    }

    /**
     * 快速复制网点租房付款详情
     *
     * @param int $id
     * @param int $uid
     * @return array|mixed
     */
    public function editDetail(int $id, int $uid = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            $main_model = PaymentStoreRenting::findFirst([
                'id = :id:',
                'bind' => ['id' => $id]
            ]);

            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('data_not_exist_by_id', ['id' => $id]), ErrCode::$VALIDATE_ERROR);
            }

            // 仅可复制自己创建的单据
            if ($main_model->create_id != $uid) {
                throw new ValidationException(static::$t->_('payment_order_copy_edit_create_id_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 非驳回态, 不可复制新增
            if ($main_model->approval_status != Enums::PAYMENT_APPLY_STATUS_REJECTED) {
                throw new ValidationException(static::$t->_('payment_order_status_not_reject'), ErrCode::$STORE_RENTING_PAYMENT_PAY_APPROVAL_STATUS_ERROR);
            }

            $data = $main_model->toArray();


            // 获取附件信息
            $file = AttachModel::find([
                'conditions' => 'oss_bucket_key = :oss_bucket_key: AND oss_bucket_type = :oss_bucket_type:',
                'bind'       => ['oss_bucket_key' => $data['id'], 'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT],
                'columns'    => ['bucket_name', 'object_key', 'file_name','id']
            ]);

            // 金额详情
            $amount_detail = PaymentStoreRentingDetail::find([
                'conditions' => 'store_renting_id = :store_renting_id:',
                'bind'       => ['store_renting_id' => $data['id']],
            ]);

            $data['event_file']         = $file ? $file->toArray() : [];
            $data['amount_detail_item'] = $amount_detail ? $amount_detail->toArray() : [];
            $user_base_info = $this->getUserMetaFromBi($uid);
            $data['create_email'] = $user_base_info['create_email'];
            $data['create_department_name'] = $user_base_info['create_display_department_name'];

            // 费用所属部门
            if (!empty($data['cost_department_id'])) {
                $cost_department              = DepartmentModel::getFirst([
                    'conditions' => 'id = :id:',
                    'columns'    => 'name',
                    'bind'       => ['id' => $data['cost_department_id']]
                ]);
                $data['cost_department_name'] = $cost_department->name ?? '';
            } else {
                $data['cost_department_name'] = $data['cost_center_department_name'] ?? '';
            }
            $data['apply_no'] = static::genSerialNo(Enums::PAYMENT_STORE_RENTING_APPLY_NO_PREFIX, RedisKey::PAYMENT_STORE_RENTING_APPLY_COUNTER);

            $data['attachment_list'] = $this->getConfigAttachmentList($main_model);
            $data = $this->handleEditData($data);

            if (empty($data['id'])) {
                throw new BusinessException('获取网点租房付款信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }
            unset($data['id']);

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $this->logger->warning('payment-data-copy-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data
        ];

    }

    /**
     * @param $id
     * @param $uid
     * @param $is_audit
     * @return array
     */
    public function getAuditDetail($id, $uid, $is_audit = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $data = $this->getDetail($id, $uid, $is_audit);
            if (empty($data['id'])) {
                throw new BusinessException('获取网点租房付款信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $this->logger->warning('get-detail-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 生成网点租房付款pdf文件
     * @param int $id
     * @param int $uid
     * @param bool $is_audit
     * @param bool $batch_download 是否批量下载 false否，true是
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function download(int $id, int $uid,$is_audit=false, $batch_download = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $lang = $this->getLang();

        $return_data = [];

        try {
            $data = $this->getDetail($id, $uid, false, true);

            if (empty($data['id'])) {
                throw new BusinessException('获取网点租房付款信息失败', ErrCode::$STORE_RENTING_PAYMENT_GET_INFO_ERROR);
            }

            if(!$is_audit && !$this->isCanDownload($data, $uid)){
                throw new ValidationException('unpaid status cannot be downloaded1', ErrCode::$ORDINARY_PAYMENT_PAY_STATUS_DOWNLOAD_ERROR);
            }

            // 文件临时目录
            $sys_tmp_dir = sys_get_temp_dir();
            $file_dir = $file_dir = $sys_tmp_dir . '/';
            $file_name = $batch_download ? ($data['apply_no'] . "_{$lang}.pdf") : (self::$t['payment_store_renting_pdf_file_name'] .'_' . md5($id) . "_{$lang}.pdf");
            $file_path = $file_dir . $file_name;
            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($data);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序

            if (1 == $data['ver']) {
                $view->render("payment", "store_renting_v1_" . $lang);

            } else {
                $view->render("payment", "store_renting_" . $lang);

            }
            $view->finish();
            $content = $view->getContent();
            //echo $content;

            $mpdf = new Mpdf([
                'format' => 'A4',
                'mode'=>'zh-CN'
            ]);

            $mpdf->useAdobeCJK = true;
            $mpdf->SetDisplayMode('fullpage');
            $mpdf->SetHTMLHeader("");
            $mpdf->SetHTMLFooter("");
            $mpdf->WriteHTML($content);
            $mpdf->Output($file_path,"f");

            // pdf 加水印
            WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

            // 生成成功, 上传OSS
            $upload_res = OssHelper::uploadFile($file_path);
            if (!empty($upload_res['object_url'])) {
                $return_data['file_name'] = $file_name;
                $return_data['file_url'] = $upload_res['object_url'];
            } else {
                throw new ValidationException('Download failed, please try again', ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Mpdf\MpdfException $e){
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('store_renting_payment-detail-download-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $return_data
        ];
    }

    /**
     * 处理详情数据格式
     * @param $data
     * @return array
     */
    protected function handleData($data)
    {
        if (empty($data)) {
            return [];
        }

        if (!empty($data['amount_detail_item'])) {
            // WHT类别
            $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(0, true);
            $rent_due_arr = ContractStoreRentingService::getInstance()->getRentDueDate();

            // 网点租房付款费用类型枚举
            $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

            // 9888【OA|网点租房付款】功能优化 新增查看合同操作，目前没有合同ID只有合同编号，需要根据合同编号反查询出合同ID
            // 根据合同编号获取所有的合同ID
            $contract_list = $this->getStoreRentingContractList(array_column($data['amount_detail_item'], 'contract_no'));
            $contract_id_arr = [];//合同ID组
            if (!empty($contract_list)) {
                $contract_id_arr = array_column($contract_list, null, 'contract_no');
            }

            foreach ($data['amount_detail_item'] as $key => $value) {
                // WHT类别 和 类别税率转换
                $value['detail_id'] = $value['id'];
//                $value['cost_type_id'] = $value['cost_type'];
                $value['wht_category_id'] = $wht_category_item[$value['wht_category']] ?? '0';
                $value['wht_tax_rate_id'] = $value['wht_tax_rate'];

                $value['cost_type_text'] = self::$t[$store_rent_payment_cost_enums[$value['cost_type']]];

                $value['is_contract_text'] = self::$t[Enums::$payment_contract_status[$value['is_contract']]];

                $value['wht_tax_rate'] .= '%';
                $value['vat_rate'] .= '%';

                $value['vat_amount'] = round($value['amount_has_tax'] - $value['amount'], 2);

                $value['index'] = $value['index_no'] ?: 0;
                $value['pay_status'] = isset(Enums::$payment_pay_status[$value['pay_status']]) ? self::$t[Enums::$payment_pay_status[$value['pay_status']]] : '';

                unset($value['id']);
                unset($value['cost_type']);
                unset($value['store_renting_id']);
                unset($value['last_edit_id']);
                unset($value['created_at']);
                unset($value['updated_at']);
                if (!empty($contract_id_arr) && !empty($value['contract_no']) && isset($contract_id_arr[$value['contract_no']])) {
                    $value['contract_id'] = $contract_id_arr[$value['contract_no']]['contract_id'];
                    $value['contract_effect_date'] = $contract_id_arr[$value['contract_no']]['contract_effect_date'];
                    $value['contract_end_date'] = $contract_id_arr[$value['contract_no']]['contract_end_date'];
                    $value['rent_due_date'] = $rent_due_arr[$contract_id_arr[$value['contract_no']]['rent_due_date']] ?? '';
                    $value['is_main'] = $contract_id_arr[$value['contract_no']]['is_main'];
                    $value['house_owner_type'] = $contract_id_arr[$value['contract_no']]['house_owner_type'];
                } else {
                    $value['contract_id'] = 0;
                    $value['contract_effect_date'] = '';
                    $value['contract_end_date'] = '';
                    $value['rent_due_date'] = '';
                    $value['is_main'] = 0;
                    $value['house_owner_type'] = 0;
                }

                $data['amount_detail_item'][$key] = $value;
            }
        }

        // 费用所属公司获取
        $coo_cost_company_list = (new PurchaseService())->getCooCostCompany();
        $coo_cost_company_list = array_column($coo_cost_company_list, 'cost_company_name', 'cost_company_id');

        return [
            'id' => $data['id'],
            'apply_no' => $data['apply_no'],
            'create_id' => $data['create_id'],
            'create_name' => $this->getNameAndNickName($data['create_name'], $data['create_nickname']),
            'create_department_name' => $data['create_node_department_name'] ? $data['create_node_department_name'] : $data['create_department_name'],
            'create_company_name' => $data['create_company_name'],
            'cost_center_department_name' => $data['cost_center_department_name'],
            'cost_center_name' => $data['cost_center_name'],
            'cost_store_type' => (int) $data['cost_store_type'],
            'cost_store_type_text' => self::$t[Enums::$payment_cost_store_type[$data['cost_store_type']]],
            'payment_method' => (int) $data['payment_method'],
            'payment_method_text' => self::$t[Enums::$payment_method[$data['payment_method']]],
            'currency' => (int) $data['currency'],
            'currency_text' => self::$t[GlobalEnums::$currency_item[$data['currency']]],
            'approval_status' => $data['approval_status'],
            'pay_status' => $data['pay_status'],
            'cancel_reason' => $data['cancel_reason'],
            'refuse_reason' => $data['refuse_reason'],
            'cancel_at' => $data['cancel_at'],
            'rejected_at' => $data['rejected_at'],
            'approved_at' => $data['approved_at'],
            'remark' => $data['remark'],
            'updated_at' => $data['updated_at'],
            'total_amount' => $data['total_amount'],
            'wht_total_amount' => $data['wht_total_amount'],
            'vat_total_amount'=>$data['vat_total_amount'],
            'tax_total_amount'=>$data['tax_total_amount'],
            'actually_total_amount' => $data['actually_total_amount'],
            'event_file' => $data['event_file'],
            'pay_info' => $data['pay_info'],
            'amount_detail_item' => $data['amount_detail_item'],
            'auth_logs' => $data['auth_logs'],
            'can_edit' => $data['can_edit'],
            'is_after_ap_th' => $data['is_after_ap_th'],
            'ver'            =>$data['ver'],
            'ask_id'         => $data['ask_id']??'',
            'cost_department_id' => $data['cost_department_id'],
            'cost_department_name' => $data['cost_department_name'],
            'cost_company_id' => $data['cost_company_id'],
            'cost_company_name' => $coo_cost_company_list[$data['cost_company_id']] ?? '',
            'border_payment_type' => $data['border_payment_type'],
            'create_email' => $data['create_email'],
            'required_supplement_file' => $data['required_supplement_file'],
            'attachment_list' => $data['attachment_list'],
            'create_date' => $data['create_date']
        ];
    }


    /**
     * 处理详情数据格式
     * @param $data
     * @return array
     */
    protected function handleEditData($data)
    {
        if (empty($data)) {
            return [];
        }

        if (!empty($data['amount_detail_item'])) {
            // WHT类别
            $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(0, true);

            // 网点租房付款费用类型枚举
            $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

            // 9888【OA|网点租房付款】功能优化 新增查看合同操作，目前没有合同ID只有合同编号，需要根据合同编号反查询出合同ID
            // 根据合同编号获取所有的合同ID
            $contract_list = $this->getStoreRentingContractList(array_column($data['amount_detail_item'], 'contract_no'));
            $contract_id_arr = [];//合同ID组
            if (!empty($contract_list)) {
                $contract_id_arr = array_column($contract_list, null, 'contract_no');
            }

            foreach ($data['amount_detail_item'] as $key => $value) {
                // WHT类别 和 类别税率转换
                $value['detail_id'] = $value['id'];
                $value['cost_type_id'] = (int)$value['cost_type'];
                $value['wht_category_id'] = (string)$wht_category_item[$value['wht_category']] ?? '0';
                $value['wht_tax_rate_id'] = $value['wht_tax_rate'];

                $value['cost_type_text'] = self::$t[$store_rent_payment_cost_enums[$value['cost_type']]];

                $value['is_contract_text'] = self::$t[Enums::$payment_contract_status[$value['is_contract']]];



                $value['vat_amount'] = round($value['amount_has_tax'] - $value['amount'], 2);

                $value['index'] = $value['index_no'] ?: 0;
                $value['pay_status'] = isset(Enums::$payment_pay_status[$data['pay_status']]) ? self::$t[Enums::$payment_pay_status[$data['pay_status']]] : '';

                unset($value['id']);
                unset($value['cost_type']);
                unset($value['store_renting_id']);
                unset($value['last_edit_id']);
                unset($value['created_at']);
                unset($value['updated_at']);
                if (!empty($contract_id_arr) && !empty($value['contract_no']) && isset($contract_id_arr[$value['contract_no']])) {
                    $value['contract_id'] = $contract_id_arr[$value['contract_no']]['contract_id'];
                    $value['contract_effect_date'] = $contract_id_arr[$value['contract_no']]['contract_effect_date'];
                    $value['contract_end_date'] = $contract_id_arr[$value['contract_no']]['contract_end_date'];
                    $value['is_main'] = $contract_id_arr[$value['contract_no']]['is_main'];
                    $value['house_owner_type'] = $contract_id_arr[$value['contract_no']]['house_owner_type'];
                } else {
                    $value['contract_id'] = 0;
                    $value['contract_effect_date'] = '';
                    $value['contract_end_date'] = '';
                    $value['is_main'] = 0;
                    $value['house_owner_type'] = 0;
                }
                $value['is_contract'] = (int)$value['is_contract'];

                $data['amount_detail_item'][$key] = $value;
            }
        }

        // 费用所属公司获取
        $coo_cost_company_list = (new PurchaseService())->getCooCostCompany();
        $coo_cost_company_list = array_column($coo_cost_company_list, 'cost_company_name', 'cost_company_id');

        return [
            'id' => $data['id'],
            'apply_no' => $data['apply_no'],
            'create_id' => $data['create_id'],
            'create_name' => $this->getNameAndNickName($data['create_name'], $data['create_nickname']),
            'create_department_name' => $data['create_department_name'],
            'create_company_name' => $data['create_company_name'],
            'cost_center_department_name' => $data['cost_center_department_name'],
            'cost_center_name' => $data['cost_center_name'],
            'cost_store_type' => (int) $data['cost_store_type'],
            'cost_store_type_text' => self::$t[Enums::$payment_cost_store_type[$data['cost_store_type']]],
            'payment_method' => (int) $data['payment_method'],
            'payment_method_text' => self::$t[Enums::$payment_method[$data['payment_method']]],
            'currency' => (int) $data['currency'],
            'currency_text' => self::$t[GlobalEnums::$currency_item[$data['currency']]],
            'approval_status' => $data['approval_status'],
            'pay_status' => $data['pay_status'],
            'cancel_reason' => $data['cancel_reason'],
            'refuse_reason' => $data['refuse_reason'],
            'cancel_at' => $data['cancel_at'],
            'rejected_at' => $data['rejected_at'],
            'approved_at' => $data['approved_at'],
            'remark' => $data['remark'],
            'updated_at' => $data['updated_at'],
            'total_amount' => $data['total_amount'],
            'wht_total_amount' => $data['wht_total_amount'],
            'vat_total_amount'=>$data['vat_total_amount'],
            'tax_total_amount'=>$data['tax_total_amount'],
            'actually_total_amount' => $data['actually_total_amount'],
            'event_file' => $data['event_file'],
            'amount_detail_item' => $data['amount_detail_item'],
            'can_edit' => $data['can_edit'] ?? false,
            'is_after_ap_th' => $data['is_after_ap_th'],
            'ver' => $data['ver'],
            'ask_id' => $data['ask_id'] ?? '',
            'cost_department_id' => $data['cost_department_id'],
            'cost_department_name' => $data['cost_department_name'],
            'cost_company_id' => $data['cost_company_id'],
            'cost_company_name' => $coo_cost_company_list[$data['cost_company_id']] ?? '',
            'border_payment_type' => $data['border_payment_type'],
            'create_email' => $data['create_email'],
            'attachment_list' => $data['attachment_list'],
            'create_date' => $data['create_date']
        ];
    }

    /**
     * 获取审批日志
     * @param $req
     * @param $detail_data
     * @param bool $if_download
     * @return array
     */
    private function getAuditLogs($req, $detail_data, $if_download=false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k=>$v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        //查询支付模块的审批流
        if ($detail_data['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
            $payment_data = PayService::getInstance()->getPaymentByBusinessNo(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING, $detail_data['apply_no']);
            if (!empty($payment_data)) {
                $pay_flow_service = new PayFlowService();
                $payment_audit_logs = $pay_flow_service->getAuditLogs($payment_data, true);
                //上下文必须保证两个数组是索引数组,且$payment_audit_logs排在$auth_logs之前
                $auth_logs = array_merge($payment_audit_logs, $auth_logs);
                //查到支付模块数据直接返回, 没查到的继续走下边的拼接支付人逻辑(兼容开启支付模块后历史数据审批通过未支付完成的)
                return $auth_logs;
            }
        }
        $us = new UserService();
        // 审核通过 -> 待付款: 如果是下载, 待付款的不在审批流程中展示
        if (!$if_download && ($detail_data['approval_status'] == Enums::PAYMENT_APPLY_STATUS_APPROVAL) && ($detail_data['pay_status'] == Enums::PAYMENT_PAY_STATUS_PENDING)) {
            $payPendingLogs = [
                'action_name' => self::$t[(Enums::$payment_pay_status[$detail_data['pay_status']])],
                'audit_at' => $detail_data['approved_at'],
                'audit_at_datetime' => $detail_data['approved_at'],
                'action' => 5,
                "info" => ''
            ];

            $pay_staff_id = $this->getPayAuthStaffIdItem();
            foreach ($pay_staff_id as $staff_id) {
                $current = $us->getUserById($staff_id);
                if (!empty($current) && !is_string($current)) {
                    // 待支付
                    $payPendingLogs['list'][] = [
                        'staff_id' => $staff_id,
                        'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name ?? ''),
                        'staff_department' => $current->getDepartment()->name ?? '',
                        'job_title' => $current->getJobTitle()->name ?? '',
                    ];
                }
            }

            array_unshift($auth_logs, $payPendingLogs);
        }
        // 审核通过 -> 已付款 / 未付款
        if (($detail_data['approval_status'] == Enums::PAYMENT_APPLY_STATUS_APPROVAL) && in_array($detail_data['pay_status'], [Enums::PAYMENT_PAY_STATUS_PAY, Enums::PAYMENT_PAY_STATUS_NOTPAY])) {
            // 支付操作后，不要再支付
            $current = $us->getUserById($detail_data['pay_info']['create_id']);

            // 放入付款
            if ($current) {
                $payLogs = [
                    'staff_id' => $detail_data['pay_info']['create_id'],
                    'staff_name' => $this->getNameAndNickName($current->name,$current->nick_name ?? ''),
                    'staff_department' => $current->getDepartment()->name ?? '',
                    'job_title' => $current->getJobTitle()->name ?? '',
                    'action_name' => self::$t[Enums::$payment_pay_status[$detail_data['pay_status']]],
                    'audit_at' => $detail_data['pay_info']['created_at'],
                    'audit_at_datetime' => $detail_data['pay_info']['created_at'],
                    'action' => 5,
                    "info" => ''
                ];

                array_unshift($auth_logs, $payLogs);
            }
        }

        return $auth_logs;
    }

    private function getLang(){
        $lang = self::$language;
        if(empty($lang) || !in_array($lang,["th","en","zh-CN"],1)){
            $lang ="th";
        }
        return $lang;
    }
}
