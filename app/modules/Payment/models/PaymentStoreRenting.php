<?php

namespace App\Modules\Payment\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\Payment\Services\StoreRentingAddService;
use App\Modules\User\Models\AttachModel;

class PaymentStoreRenting extends Base implements BankFlowModelInterface,PayModelInterface
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('payment_store_renting');


        $this->hasOne(
            'id',
            PaymentStoreRentingPay::class,
            'store_renting_id',
            [
                "alias" => "Pay"
            ]
        );


        $this->hasMany(
            'id',
            PaymentStoreRentingDetail::class,
            'store_renting_id',
            [
                "alias" => "Details"
            ]
        );

        $this->hasMany(
            'id',
            AttachModel::class,
            'oss_bucket_key', [
                'params' => [
                    'conditions' => 'sub_type > 0 and oss_bucket_type = ' . Enums::OSS_BUCKET_TYPE_STORE_RENTING_PAYMENT . ' AND deleted = ' . GlobalEnums::IS_NO_DELETED
                ],
                'alias' => 'SelfAttachments',
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'apply_no = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }


    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'apply_no in ({nos:array}) and approval_status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'apply_no in ({nos:array}) and approval_status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }



    public function getFormatData()
    {
        // TODO: Implement getFormatData() method.
        return [
            'oa_value' => $this->id,
            'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING,
            'no' => $this->apply_no,
            'amount' => $this->actually_total_amount,   //实付金额总计-数据库里就是保留2位小数的
            'currency' => $this->currency,
            'status' => $this->approval_status,
            'pay_status' => $this->pay_status
        ];
    }

    public function link(array $data)
    {
        // TODO: Implement link() method.

        //判断现有的状态
        if (empty($this) || $this->approval_status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found payment_store_renting or payment_store_renting pay_status is error');
        }


        $item = [];
        $item['is_pay'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_bank_name'] = $data['bank_name'];
        $item['pay_bank_account'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];
        $item['create_id'] = $data['create_id'];
        $item['create_name'] = $data['create_name'];
        $item['create_department_name'] = $data['create_department_name'];
        $item['create_job_title_name'] = $data['create_job_title_name'];
        $item['created_at'] = date("Y-m-d H:i:s");
        $item['remark'] = $data['ticket_no'];
        $item['pay_from'] = 2;


        $item['store_renting_id'] = $this->id;
        $pay = new PaymentStoreRentingPay();
        $bool = $pay->i_create($item);
        if ($bool === false) {
            throw new BusinessException("网点租房付款-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }

        $main_data = [
            'pay_status' => $item['is_pay'],
            'updated_at' => $item['created_at'],
            'last_update_id' => $item['create_id'],
            'last_update_name' => $item['create_name'],
            'last_update_department' => $item['create_department_name'],
            'last_update_job_title' => $item['create_job_title_name'],
            'last_update_at' => $item['created_at'],
        ];


        $bool = $this->i_update($main_data);
        if ($bool === false) {
            throw new BusinessException("网点租房付款-更新主表失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }


        $main_detail_model = PaymentStoreRentingDetail::find([
            'conditions' => 'store_renting_id = :store_renting_id:',
            'bind'       => ['store_renting_id' => $this->id]
        ]);

        if (!empty($main_detail_model->toArray())) {
            foreach ($main_detail_model as $main_detail) {
                $main_detail->pay_status = $item['is_pay'];
                $main_detail->updated_at = date('Y-m-d H:i:s', time());
                if ($main_detail->save() === false) {
                    throw new BusinessException('付款支付 - 网点租房付款详情支付状态同步失败', ErrCode::$STORE_RENTING_PAYMENT_PAY_UPDATE_FAIL_ERROR);
                }
            }
        }

        return true;
    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $batchData = [];

        $item = [];
        $item['is_pay'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_bank_name'] = $data['bank_name'];
        $item['pay_bank_account'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];
        $item['create_id'] = $data['create_id'];
        $item['create_name'] = $data['create_name'];
        $item['create_department_name'] = $data['create_department_name'];
        $item['create_job_title_name'] = $data['create_job_title_name'];
        $item['created_at'] = date("Y-m-d H:i:s");
        $item['remark'] = $data['ticket_no'];
        $item['pay_from'] = 2;

        foreach ($ids as $id) {
            $item['store_renting_id'] = $id;
            $batchData[] = $item;
        }

        $pay = new PaymentStoreRentingPay();
        $bool = $pay->batch_insert($batchData);
        if ($bool === false) {
            throw new BusinessException("网点租房付款-批量创建失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }

        $db           = $this->getDI()->get('db_oa');
        $update_ids= implode(',', $ids);

        $sql = 'update payment_store_renting set 
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         updated_at="' . $item['created_at'] . '",
                         last_update_id="' . $item['create_id'] . '",
                         last_update_name="' . $item['create_name'] . '",
                         last_update_department="' . $item['create_department_name'] . '",
                         last_update_job_title="' . $item['create_job_title_name'] . '",
                         last_update_at="' . $item['created_at'] . '"
                         
                        where id in (' . $update_ids .')';


        $bool = $db->execute($sql);
        if ($bool === false) {
            throw new BusinessException('网点租房付款-批量更新失败==' . $sql);
        }

        //批量修改
        $all_update_success = $db->updateAsDict(
            (new PaymentStoreRentingDetail())->getSource(),
            [
                'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                'updated_at' => date('Y-m-d H:i:s', time())
            ],
            [
                'conditions' => "store_renting_id in ($update_ids)",
            ]
        );
        if ($all_update_success === false) {
            throw new BusinessException('网点租房付款-批量更新详情失败数据是：'. json_encode(['ids' => $update_ids], JSON_UNESCAPED_UNICODE), ErrCode::$PAYMENT_STORE_RENTING_DETAIL_SAVE_ERROR);
        }

        return true;
    }






    public function cancel($user)
    {
        //判断现有的状态
        if (empty($this) || $this->approval_status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found payment_store_renting or payment_store_renting pay_status is error');
        }

        $this->getPay()->delete();

        $main_data = [
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING,
            'updated_at' => date("Y-m-d H:i:s"),
            'last_update_id' => $user['id'],
            'last_update_name' => $user['name'],
            'last_update_department' => $user['department'],
            'last_update_job_title' => $user['job_title'],
            'last_update_at' => date("Y-m-d H:i:s"),
        ];

        $bool = $this->i_update($main_data);
        if ($bool === false) {
            throw new BusinessException("网点租房付款-撤销-更新主表失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }

        //批量修改
        $db           = $this->getDI()->get('db_oa');
        $all_update_success = $db->updateAsDict(
            (new PaymentStoreRentingDetail())->getSource(),
            [
                'pay_status' => Enums::LOAN_PAY_STATUS_PENDING,
                'updated_at' => date('Y-m-d H:i:s', time())
            ],
            [
                'conditions' => "store_renting_id = {$this->id}",
            ]
        );
        if ($all_update_success === false) {
            throw new BusinessException('网点租房付款-撤回更新详情失败数据是：'. $this->id, ErrCode::$PAYMENT_STORE_RENTING_DETAIL_SAVE_ERROR);
        }

        return true;
    }

    public function batch_confirm($ids, $data)
    {
        $sql = 'update payment_store_renting_detail set 
                        is_deduct = '.intval($data['is_deduct']).'        
                        where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('租房合同付款-批量确认失败==' . $sql);
        }
        return true;
    }

    public function getPayData()
    {
        $details = $this->getDetails();
        //最小应付日期
        $minDueDate = null;
        if ($details) {
                $minDueDate = min(array_column($details->toArray(), 'due_date'));
        }
        $arr = [
            'oa_type' => BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING,
            'no' => $this->apply_no,
            'apply_staff_id' => $this->create_id,
            'apply_staff_name' => $this->create_name,
            'apply_date' => $this->create_date,
            'pay_method' => $this->payment_method,
            'pay_where' => $this->border_payment_type,//境内/境外支付
            'currency' => $this->currency,
            'amount_total_no_tax' => $this->total_amount,                           //不含税金额
            'amount_total_vat' => $this->vat_total_amount,                          //税额
            'amount_total_have_tax' => $this->tax_total_amount,                     //含税金额（含VAT含WHT）
            'amount_total_wht' => $this->wht_total_amount,                          //wht总计
            'amount_total_have_tax_no_wht' => $this->actually_total_amount,         //含税金额总计（含VAT不含WHT）
            'amount_loan' => 0,             //冲减借款金额,
            'amount_reserve' => 0,
            'amount_discount' => 0,                           //折扣
            'amount_total_actually' =>$this->actually_total_amount,                     //实付金额
            'amount_remark' => $this->remark,                                            //备注
            'cost_department_id' => $this->cost_department_id,                           //费用所属部门
            'cost_company_id' => $this->cost_company_id,                           //费用所属公司
            'default_planned_pay_date' => empty($minDueDate) ? date('Y-m-d') : $minDueDate,//默认计划支付日期
            'planned_pay_date' => empty($minDueDate) ? date('Y-m-d') : $minDueDate,//计划支付日期
        ];

        //费用部门名称
        $arr['cost_department_name'] = '';
        if (!empty($arr['cost_department_id'])){
            $department_service = new DepartmentService();
            $department_detail = $department_service->getDepartmentDetail($arr['cost_department_id']);
            $arr['cost_department_name'] = $department_detail['name']??'';
        }
        //费用公司名字
        $arr['cost_company_name'] = '';
        if(!empty($arr['cost_company_id'])){
            $arr['cost_company_name'] = SysDepartmentModel::getCompanyNameByCompanyId($arr['cost_company_id']);
        }
        // pays 使用在了batch_insert方法,所以二维数组中字段必须一致,没值的也要有这个key
        $arr['pays'] = [];
        if(!empty($details)){
            foreach ($details as $detail){
                $tmp = [];
                $tmp['bank_name'] = $detail->bank_name;
                $tmp['bank_account_name'] = $detail->bank_account_name;
                $tmp['bank_account'] = $detail->bank_account_no;
                $tmp['amount'] = $detail->actually_amount;
                if ($detail->is_contract == 1){
                    $tmp['contract_no'] = $detail->contract_no;
                }else{
                    $tmp['contract_no'] = '';
                }
                $tmp['swift_code'] = $detail->swift_code??'';
                $tmp['bank_address'] = $detail->bank_address??'';
                $arr['pays'][] = $tmp;
            }
        }
        return $arr;
    }

    public function getPayCallBackData($data)
    {
        /*$validate_pay_param = [
            'id' => 'Required|IntGe:1|>>>:id param error',
            'is_pay' => 'Required|IntIn:2,3|>>>:whether paid param error',
            'pay_bank_name' =>'IfIntEq:is_pay,2|Required|StrIn:Thai Military Bank,Siam Commercial Bank|>>>:payment bank param error',
            'pay_date' =>'IfIntEq:is_pay,2|Required|Date|>>>:payment date param error',
            'remark' =>'IfIntEq:is_pay,3|Required|StrLenGeLe:1,5000|>>>:remark param error',
        ];*/
        $new = [];
        $pay_status = $data['pay_status'];
        $new['is_pay'] = $pay_status;
        if ($pay_status == Enums::LOAN_PAY_STATUS_PAY) {
            //这支付方式可能是支票，没有这些值
            $new['pay_bank_name'] = $data['pay_bank_name'] ?? 'Thai Military Bank';
            $new['pay_bank_account'] = $data['pay_bank_account'] ?? '';
            $new['pay_date'] = $data['pay_bank_flow_date'] ?? date("Y-m-d");
            $new['remark'] = $data['pay_remark'] ?? '';
        } else {
            $new['remark'] = $data['pay_remark'] ?? '';
            $new['pay_date'] = null;
        }

        //pay_bank_account 这个是根据银行名字判断，写死的
        return $new;
    }

    /**
     * 从添加时的来源获取银行账号, 用于支付模块更新银行账号
     * @param string $no
     * @param $pay_id
     * @return array
     * @date 2022/3/9
     */
    public function getBankInfo($no,$pay_id)
    {
        $myself = self::findFirst(
            [
                'conditions' => 'apply_no = :apply_no: and approval_status = :approval_status: and pay_status = :pay_status:',
                'bind' => [
                    'apply_no' => $no,
                    'approval_status' => Enums::ORDINARY_PAYMENT_APPROVAL_STATUS_PASSED,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING
                ]
            ]
        );
        if (empty($myself)){
            return [];
        }
        //查询支付详情,得到合同编号
        $payment_pay = PaymentPay::findFirst([
            'conditions' => 'id = :pay_id:',
            'bind' => ['pay_id' => $pay_id]
        ]);
        if (empty($payment_pay)){
            return [];
        }
        if (!isset($payment_pay->contract_no) || empty($payment_pay->contract_no)){
            return [];
        }
        $params = ['contract_id'=>$payment_pay->contract_no];
        $renting_bank = StoreRentingAddService::getInstance()->rentingBank($params);
        if (!isset($renting_bank['bank_collection'])){
            return [];
        }
        //contact_mobile(联系人电话),
        //contact_emial(联系人邮箱),
        //contact_type(联系人类型),
        //contact_code(联系人身份证/税号)
        //二维数据, 让前端选择
        $bank_info = [];
        foreach ($renting_bank['bank_collection'] as $bank){
            $tmp = [];
            $tmp['bank_name'] = $bank['bank_name']??'';
            $tmp['bank_account'] = $bank['bank_account']??'';
            $tmp['bank_account_name'] = $bank['bank_account_name']??'';
            $tmp['contact_mobile'] = $bank['contact_mobile']??'';
            $tmp['contact_emial'] = $bank['contact_emial']??'';
            $tmp['contact_type'] = $bank['contact_type']??'';
            $tmp['contact_code'] = $bank['contact_code']??'';
            $bank_info[] = $tmp;
        }
        return [
            'type' => 2,
            'items' => $bank_info
        ];
    }

    //打上支付模块标记
    public function updatePayTag():bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module'=>1]) === false){
            return false;
        }
        return true;
    }


    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     * @throws BusinessException
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        //判断现有的状态
        $main_model = self::findFirst([
            'conditions' => 'apply_no = :no: AND approval_status = :status: AND pay_status = :pay_status: AND is_pay_module = :is_pay_module:',
            'bind' => [
                'no' => $data['payment_no'],
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING,
                'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_YES,
            ],
        ]);

        // 主数据为空, 不可变更收款人信息
        if (empty($main_model)) {
            return true;
        }

        // 明细行收款人信息
        $detail_models = $main_model->getDetails();
        if (empty($detail_models->toArray())) {
            return true;
        }

        // 要变更的数据
        $pay_item = array_column($data['pay'], null, 'contract_no');

        // 回更明细行合同对应的收款人信息
        foreach ($detail_models as $detail) {
            // 未关联合同, 不回更
            if (empty($detail->contract_no)) {
                continue;
            }

            $pay_info = $pay_item[$detail->contract_no] ?? [];

            // 未找到合同对应的收款人信息, 不回更
            if (empty($pay_info)) {
                continue;
            }

            // 变更前数据
            $this->getLogger()->info('sync_update_pyeeinfo_before_data=' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE));

            $sync_data = [
                'bank_account_name' => $pay_info['bank_account_name'],
                'bank_name' => $pay_info['bank_name'],
                'bank_account_no' => $pay_info['bank_account'],
                'updated_at' => date('Y-m-d H:i:s'),
            ];
            if ($detail->i_update($sync_data) === false) {
                throw new BusinessException('租房付款支付-回更明细行收款人信息失败, 原因可能是:' . get_data_object_error_msg($detail), ErrCode::$BUSINESS_ERROR);
            }

            // 变更后数据
            $this->getLogger()->info('sync_update_pyeeinfo_after_data=' . json_encode($detail->toArray(), JSON_UNESCAPED_UNICODE));
        }

        return true;
    }
}
