<?php
namespace App\Modules\Payment\Models;
use App\Models\Base;

/**
 * 网点租房付款费用类型表
 * Class PaymentCostType
 * @package App\Modules\Payment\Models
 */
class PaymentCostType extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');

        // setSource()方法指定数据库表
        $this->setSource('payment_cost_type');

        //关联网点租房付款核算科目表
        $this->hasMany(
            'id',
            PaymentCostLedgeRelLedger::class,
            'cost_type_id',
            [
                "alias" => "Ledger"
            ]
        );
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}
