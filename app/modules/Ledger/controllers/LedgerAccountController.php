<?php

namespace App\Modules\Ledger\Controllers;

use App\Library\Validation\Validation;
use App\Modules\Ledger\Services\LedgerAccountService;
use App\Library\ErrCode;

class LedgerAccountController extends BaseController {

    /**
    * 核算科目查询列表
    * @Date: 2021-09-02 12:08
    * @author: peak pan
    * @return:
    **/
    public function ledgerSelectListAction()
    {
        $params = $this->request->get();
        try {
            $list_search = [
                'name_en' => 'Required|StrLenGeLe:3,100',
            ];
            Validation::validate($params, $list_search);
            $list = LedgerAccountService::getInstance()->getList($params);
            $data = [
                'list' => $list['data'] ?? []
            ];
            return $this->returnJson( $list['code'] ?? ErrCode::$SUCCESS, $list['message'] ?? '', $data);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

}
