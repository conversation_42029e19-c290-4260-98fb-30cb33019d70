<?php

namespace App\Modules\Ledger\Services;


class BaseService extends \App\Library\BaseService
{


    /**
     * 根据 语言环境 获取对应字段名 涉及表 share_center_dir  share_center_file 字段名(name_th,name_en,name_cn)
     * @param $lang
     * @return string
     */
    protected function get_lang_column($lang = 'th'){
        $lang_arr = array(
            'en' => 'name_en',
            'th' => 'name_th',
            'zh-CN' => 'name_cn',
            'zh' => 'name_cn'
        );
        return empty($lang_arr[$lang]) ? '' : $lang_arr[$lang];
    }
}
