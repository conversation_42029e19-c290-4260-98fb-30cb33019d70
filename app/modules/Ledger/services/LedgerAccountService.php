<?php

namespace App\Modules\Ledger\Services;


use App\Library\ErrCode;
use App\Modules\Ledger\Models\LedgerAccountModel;
use Phalcon\Mvc\Model;

class LedgerAccountService extends BaseService
{


    private static $instance;

    /**
     * @return LedgerAccountService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 按照name_en 模糊查询
     * @Date: 2021-09-02 12:06
     * @return: array
     **@author: peak pan
     */

    public function getList(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $data = $this->modelsManager->createBuilder()
                ->columns('account, name_en')
                ->from(LedgerAccountModel::class)
                ->andWhere("name_en like :name_en:", ['name_en' => '%' . $params['name_en'] . '%'])
                ->limit(0, 100)
                ->getQuery()->execute()->toArray();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('检索异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

}
