<?php


namespace App\Modules\Salary\Controllers;


use App\Library\ErrCode;
use App\Modules\Salary\Services\ApplyService;

class PdfController extends BaseController
{

    /**
     * @Token
     */
    public function outputAction()
    {
        $hash = $this->request->get('hash');
        $cached = $this->cache->get('DL:'.$hash);
        if ($cached){
            $res = ApplyService::getInstance()->download($cached['id'],$cached['uid'],$cached['lang']);

            if ($res['code'] == ErrCode::$SUCCESS) {
                $this->response->redirect($res['data'], true, 301);
            } else {
                return $this->returnJson(ErrCode::$BUSINESS_ERROR, $res['msg'], []);
            }

        }else{
            return $this->returnJson(0,'error');
        }

    }
}