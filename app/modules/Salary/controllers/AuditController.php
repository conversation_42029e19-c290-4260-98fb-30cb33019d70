<?php

namespace App\Modules\Salary\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Salary\Services\ApplyService;
use App\Modules\Salary\Services\BaseService;
use App\Modules\Salary\Services\SalaryFlowService;

/**
 * 薪资发放审批
 *
 * Class ApplyController
 * @package App\Modules\Salary\Controllers
 */
class AuditController extends BaseController
{
    /**
     * 我的审核列表
     *
     * @Permission(action='wage.audit.search')
     */
    public function listAction()
    {
        $params = $this->request->get();
        $data = ApplyService::getInstance()->getList($params, $this->user['id'], BaseService::LIST_TYPE_AUDIT);
        return $this->returnJson($data['code'],$data['message'],$data['data']);
    }


    /**
     * 我的审核-查看
     *
     * @Permission(action='wage.audit.view')
     */
    public function detailAction()
    {

        $id = $this->request->get('id', 'int');
        Validation::validate(
            [
                'id' => $id,
            ],
            [
                'id' => 'Required|Int',
            ]
        );

        $data = ApplyService::getInstance()->detail($id);
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }

    /**
     * 薪资发放审批~我的审核~审核
     * @Permission(action='wage.audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22566
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function auditAction()
    {
        try {
            $id = $this->request->get('id', 'int');
            $note = $this->request->get('note', 'trim');
            $flag = $this->request->get('flag', 'int');

            $service = new SalaryFlowService();
            if (empty($flag)) {
                //通过
                $res = $service->approve($id, $note, $this->user);
            } else {
                //拒绝
                $res = $service->reject($id, $note, $this->user);
            }
        } catch (ValidationException $validationException) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('salary_audit_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $exception->getMessage());
        }
        return $this->returnJson(ErrCode::$SUCCESS, '');
    }


    /**
     * @Permission(action='wage.audit.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('salary_audit_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return ApplyService::getInstance()->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }


}