<?php


namespace App\Modules\Salary\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Salary\Services\ApplyService;
use App\Modules\Salary\Services\BaseService;
use App\Modules\Salary\Services\UpdateService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class PaymentController extends BaseController
{
    /**
     *薪资发放审批-我的支付-列表
     * @Permission(action='wage.payment.search')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22570
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(), []);
        $result = ApplyService::getInstance()->getList($data, $this->user['id'], BaseService::LIST_TYPE_PAY);
        return $this->returnJson($result['code'],$result['message'],$result['data']);
    }

    /**
     * @Permission(action='wage.payment.view')
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result =  ApplyService::getInstance()->detail($data['id']);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * 我的支付-支付
     * @Permission(action='wage.payment.pay')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/22582
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function payAction()
    {
        $data = Validation::validate($this->request->get(), UpdateService::$validate_pay, false, true);
        $result = (new UpdateService())->updatePayment($data['id'], $data, $this->user);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * @Permission(action='wage.payment.download')
     * @throws ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('salary_payment_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return ApplyService::getInstance()->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}