<?php
namespace App\Modules\Salary\Controllers;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Salary\Services\ApplyService;
use App\Modules\Salary\Services\BaseService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 薪资发放审批
 *
 * Class ApplyController
 * @package App\Modules\Salary\Controllers
 */
class ApplyController extends BaseController
{

    /**
     * 获得Wht类别
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getWhtAction(){
        $data = EnumsService::getInstance()->getFormatWhtRateConfig();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * @Token
     * 计算金额
     * @return Response|ResponseInterface
     */
    public function calAmountAction()
    {
        $data = $this->request->get();
        try {
            $validationApplyParams = [
                'not_tax_amount'   => 'Required|Float',          //不含税金额
                'wht_category'     => 'Required|StrLenGeLe:1,32',//WHT类型
                'wht_tax_rate'     => 'Required|Float',          //WHT税率
                'input_wht_amount' => 'Float',          //WHT金额
            ];

            Validation::validate($data, $validationApplyParams);
            $data = ApplyService::getInstance()->calWHTAmountAndRealPayAmount($data['not_tax_amount'],
                $data['wht_category'], $data['wht_tax_rate'] , $data['input_wht_amount'] ?? false);
        } catch (ValidationException $validationException) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * 薪资审批添加
     * @Permission(action='wage.apply.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     */
    public function addAction()
    {
        $data = $this->request->get();
        try {
            //获取薪资company id
            $companyList = ApplyService::getInstance()->getSalaryCompanyList();

            $validationApplyParams = [
                'xzno'                   => 'Required|StrLenGeLe:10,20',
                'apply_date'             => 'Required|Date',
                'create_id'              => 'Required|Int',
                'create_name'            => 'Required|Str',
                'create_company_id'      => 'Required|Int',
                'create_company_name'    => 'Required',
                'create_department'      => 'Required|Int',
                'create_node_department' => 'Required|Int',
                'create_department_name' => 'Required|Str',
                'create_store'           => 'Required|Str',
                'create_store_name'      => 'Required|Str',
                'create_center_code'     => 'StrLenGeLe:0,100',
                'pay_type'               => 'Required|IntIn:1,2',
                'currency'               => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS,
                'salary_company_id'      => 'Required|IntIn:'.implode(",", array_keys($companyList)),
                'salary_type'            => 'Required|IntIn:1,2',
                'salary_start_date'      => 'Required|Date',
                'salary_end_date'        => 'Required|Date',
                'salary_month'           => 'Required',
                'salary_remark'          => 'StrLenGeLe:0,500',
                'not_tax_amount'         => 'Required|Float',//不含税金额
                'wht_category'           => 'Required|StrLenGeLe:1,32',//WHT类型
                'wht_tax_rate'           => 'Required|Float',//WHT税率
                'wht_amount'             => 'Required|Float',//WHT金额
            ];

            if (isset($data['pay_start_date']) && $data['pay_start_date']) {
                $validationApplyParams['pay_start_date'] = 'Required|Date';
                $validationApplyParams['pay_end_date'] = 'Required|Date';
            }

            Validation::validate($data, $validationApplyParams);
            ApplyService::getInstance()->saveOne($data, $this->user);

        } catch (ValidationException $validationException) {

            $this->logger->info('salary_add_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }catch (\Exception $exception) {

            $this->logger->error('salary_add_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '');
    }


    /**
     * 获取默认值
     *
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     */
    public function getDefaultAction()
    {

        try {

            $data = ApplyService::getInstance()->defaultData($this->user);

        } catch (ValidationException $validationException) {

            $this->logger->info('salary_default_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());

        } catch (\Exception $exception) {

            $this->logger->error('salary_default_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);

    }


    /**
     * 获取用户信息
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     */
    public function getUserAction()
    {
        try {
            $id = $this->request->get('id', 'int');
            Validation::validate(
                [
                    'id' => $id,
                ],
                [
                    'id' => 'Required|IntGe:1',
                ]
            );
            $data = ApplyService::getInstance()->getUserMetaFromBi($id);
        } catch (ValidationException $validationException) {
            $this->logger->info(
                'salary_getUser_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error(
                'salary_getUser_notice ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        //用户不存在，或者相关报错
        if(is_string($data)){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $data);
        }
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }


    /**
     * 我的申请列表
     *
     * @Permission(action='wage.apply.search')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, [
                'pageNum' => 'Required|Int',
                'pageSize' => 'Required|Int',
            ]);

            $data = ApplyService::getInstance()->getList($params, $this->user['id'], BaseService::LIST_TYPE_APPLY);

        } catch (ValidationException $validationException) {

            $this->logger->info('salary_list_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());

        } catch (\Exception $exception) {

            $this->logger->error('salary_list_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson($data['code'],$data['message'],$data['data']);
    }


    /**
     *
     * 撤销审批
     * *
     * @Permission(action='wage.apply.cancel')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function cancelAction()
    {
        try {
            $id = $this->request->get('id', 'int');
            $note = $this->request->get('note', 'trim');
            Validation::validate([
                'id' => $id,
                'note' => $note
            ], [
                'id' => 'Required|Int',
                'note' => 'Required|StrLenGe:1',
            ]);

            ApplyService::getInstance()->cancel($id, $note, $this->user);

        } catch (ValidationException $validationException) {

            $this->logger->info('salary_cancel_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());

        } catch (\Exception $exception) {

            $this->logger->error('salary_cancel_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '');
    }


    /**
     *
     * 查看
     * @Permission(action='wage.apply.view')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function detailAction(){
        try {
            $id = $this->request->get('id', 'int');
            Validation::validate([
                    'id' => $id], [
                    'id' => 'Required|Int']);

            $data = ApplyService::getInstance()->detail($id);
        } catch (ValidationException $validationException) {

            $this->logger->info(
                'salary_cancel_notice ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());

        } catch (\Exception $exception) {

            $this->logger->error(
                'salary_cancel_error ' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
        return $this->returnJson(ErrCode::$SUCCESS, '',$data);
    }


    /**
     * @Permission(action='wage.apply.download')
     * @throws \App\Library\Validation\ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('salary_apply_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return ApplyService::getInstance()->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }

}