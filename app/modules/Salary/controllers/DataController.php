<?php


namespace App\Modules\Salary\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\Salary\Services\ApplyService;
use App\Modules\Salary\Services\BaseService;

class DataController extends BaseController
{

    /**
     * @Permission(action='wage.data.search')
     */
    public function listAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = ApplyService::getInstance()->getList($data, $this->user['id'], BaseService::LIST_TYPE_DATA);

        return $this->returnJson($result['code'],$result['message'],$result['data']);
    }

    /**
     * @Permission(action='wage.data.view')
     */
    public function detailAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result = ApplyService::getInstance()->detail($data['id']);

        return $this->returnJson(ErrCode::$SUCCESS,$this->t['success'],$result);
    }

    /**
     * @Permission(action='wage.data.export')
     */
    public function exportAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        $result =  ApplyService::getInstance()->exportList($data);

        return $this->returnJson($result['code'],'',$result['data']);
    }

    /**
     * @Permission(action='wage.data.download')
     * @throws \App\Library\Validation\ValidationException
     */
    public function downloadAction()
    {
        $data = Validation::validate($this->request->get(),[]);

        try {
            // 加锁处理
            $lock_key = md5('salary_data_download_' . $data['id'] . '_' . $this->user['id']);
            $res = $this->atomicLock(function() use ($data){
                return ApplyService::getInstance()->download($data['id'], $this->user['id'], $this->locale);
            }, $lock_key, 30);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $res['msg'], ['url' => $res['data']]);
            }

            return $this->returnJson($res['code'], $res['msg']);

        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
    }
}