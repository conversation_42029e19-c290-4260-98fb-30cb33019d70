<?php

namespace App\Modules\Salary\Models;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\PayModelInterface;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;
use App\Modules\Salary\Services\ApplyService;

class PaySalaryApply extends Base implements BankFlowModelInterface,PayModelInterface
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('pay_salary_apply');

    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'xzno = :no:',
                'bind' => ['no' => $no]
            ]
        );
    }

    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        //默认条件
        $conditions = 'xzno in ({nos:array}) and status = :status: and pay_status = :pay_status:';
        $bind = [
            'nos' => $no,
            'status' => Enums::CONTRACT_STATUS_APPROVAL,
            'pay_status' => Enums::LOAN_PAY_STATUS_PENDING
        ];
        //是否需要包含已支付数据
        if ($has_pay == true) {
            $conditions = 'xzno in ({nos:array}) and status = :status: and pay_status in ({pay_status:array})';
            $bind['pay_status'] = [Enums::LOAN_PAY_STATUS_PENDING, Enums::LOAN_PAY_STATUS_PAY];
        }
        return self::find(
            [
                'conditions' => $conditions,
                'bind' => $bind
            ]
        );
    }

    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_SALARY,
            'no' => $this->xzno,
            'amount' => bcdiv($this->salary_amount,1000,2),
            'currency' => $this->currency,
            'status'   => $this->status,
            'pay_status' => $this->pay_status
        ];
    }

    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found salary or salary status is error');
        }

        $item = [];
        $item['payer_bank'] = $data['bank_name'];
        $item['payer_no'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];
        $item['payed_at'] = date("Y-m-d H:i:s");
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_remark'] = $data['ticket_no'];
        $item['payer_id'] = $data['create_id'];
        $item['pay_from'] = 2;
        $item['updated_time'] = date("Y-m-d H:i:s");

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("薪资发放-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;


    }


    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data)
    {
        $sql = 'update pay_salary_apply set 
                         payer_bank="' . $data['bank_name'] . '",
                         payer_no="' . $data['bank_account'] . '",
                         pay_date="' . $data['date'] . '",
                         payed_at="' . date("Y-m-d H:i:s") . '",
                         updated_time="' . date("Y-m-d H:i:s") . '",
                         pay_status=' . Enums::LOAN_PAY_STATUS_PAY . ',
                         pay_remark="' . $data['ticket_no'] . '",
                         payer_id="' . $data['create_id'] . '",
                         pay_from=2 where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('薪资发放付款-批量更新失败==' . $sql);
        }
        return true;
    }

    public function cancel(array $user)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found salary or salary pay_status is error');
        }

        $item = [];
        $item['payer_bank'] = '';
        $item['payer_no'] = '';
        $item['pay_date'] = null;
        $item['payed_at'] = null;
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PENDING;    //是否已付款
        $item['pay_remark'] = null;
        $item['payer_id'] = 0;
        $item['pay_from'] = 1;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("薪资发放-撤销失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        return true;
        // TODO: Implement batch_confirm() method.
    }

    /**
     * 获得支付模块需要数据
     * @return array
     */
    public function getPayData()
    {
        $amount_total_actually = bcdiv($this->salary_amount, 1000, 2);

        //薪资发放审批公司枚举配置
        $salary_company_list = ApplyService::getInstance()->getSalaryCompanyList();

        //由于薪资发放审批的公司id是枚举配置而非真正的公司id，所以需要做下转换
        $salary_company_to_payment = ApplyService::getInstance()->getSalaryCompanyToPaymentDepartmentMap();

        $arr = [
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_SALARY,//薪资发放
            'no' => $this->xzno,//申请单号
            'apply_staff_id' => $this->create_id,//申请人单号
            'apply_staff_name' => $this->create_name,//申请人姓名
            'cost_department_id' => '',//费用所属部门id
            'cost_department_name' => '',//费用所属部门名字
            'cost_company_id' => $salary_company_to_payment[$this->salary_company_id] ?? 0,//薪资信息-公司id
            'cost_company_name' => $salary_company_list[$this->salary_company_id] ?? '',//薪资信息-公司名字
            'apply_date' => $this->apply_date,//单号申请时间
            'pay_method' => ($this->pay_type == Enums::SALARY_PAY_TYPE_TRANSFER) ? Enums::PAYMENT_METHOD_BANK_TRANSFER : Enums::PAYMENT_METHOD_CASH,//支付方式
            'pay_where' => Enums\PayEnums::PAY_WHERE_IN,//境内/境外支付-境内
            'currency' => $this->currency,//币种
            'amount_total_no_tax' => bcdiv($this->not_tax_amount, 1000, 2),//不含税金额总计
            'amount_total_vat' => 0,//vat总计
            'amount_total_have_tax' => bcdiv($this->not_tax_amount, 1000, 2),//含税金额总计(含wht)
            'amount_total_wht' => bcdiv($this->wht_amount, 1000, 2),//wht总计
            'amount_total_have_tax_no_wht' => $amount_total_actually,  //含税金额总计（含VAT不含WHT）= 不含税金额总计+VAT总计-WHT总计
            'amount_loan' => 0, //冲减借款金额,
            'amount_reserve' => 0,//冲减备用金额
            'amount_discount' => 0,//折扣
            'amount_total_actually' => $amount_total_actually,//实付金额
            'amount_remark' => $this->salary_remark//备注
        ];

        //组装收款人信息
        $arr['pays'][] = [
            'bank_name' => '',//收款人银行
            'bank_account' => '',//收款人账号
            'bank_account_name' => ($this->salary_type == Enums::SALARY_TYPE_MONEY) ? 'เงินเดือน salary' : 'กำลังทำ commission',//收款人户名,发放类型
            'amount' => $amount_total_actually//付款金额
        ];
        return $arr;
    }

    /**
     * 支付或者未支付
     * @param array $data
     * @return array
     */
    public function getPayCallBackData($data)
    {
        $new = [];
        $pay_status = $data['pay_status'];
        if ($pay_status == Enums\PayEnums::PAYMENT_MODULE_PAY_STATUS_PAY) {
            //三级审批人-支付；已支付
            $new['pay_date'] = $data['pay_bank_flow_date'] ?? date('Y-m-d');
            $new['pay_status'] = $pay_status;
            $new['payer_bank'] = $data['pay_bank_name'];
            $new['payer_no'] = $data['pay_bank_account'];
            $new['pay_remark'] = $data['pay_remark'] ?? '';
        } else if ($pay_status == Enums\PayEnums::PAYMENT_MODULE_PAY_STATUS_NOTPAY) {
            //申请人-撤回；未支付
            $new['pay_status'] = $pay_status;
            $new['pay_remark'] = $data['cancel_reason'] ?? '';
        }
        return $new;
    }

    /**
     * 更新支付信息-收款明细-刷新
     * @param string $no
     * @param int $pay_id
     * @return array
     */
    public function getBankInfo(string $no, int $pay_id)
    {
        return [];
    }

    /**
     * 打上支付模块标记
     * @return bool
     */
    public function updatePayTag():bool
    {
        //修改是否进入支付模块标记
        if ($this->i_update(['is_pay_module' => Enums\PayEnums::BIZ_DATA_IS_PAY_MODULE_YES]) === false) {
            return false;
        }
        return true;
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        // TODO 暂无需处理
        return true;
    }
}
