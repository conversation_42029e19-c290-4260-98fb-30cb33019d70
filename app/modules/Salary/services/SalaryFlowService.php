<?php

namespace App\Modules\Salary\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Pay\Services\PayService;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class SalaryFlowService extends AbstractFlowService
{

    private $biz_type = Enums::WF_SALARY_APPLY;


    public function __construct()
    {
    }

    /**
     * 薪资发放审批~我的审核~审核
     * @param $id
     * @param $note
     * @param $user
     * @return bool|mixed
     * @throws \Exception
     */
    public function approve($id, $note, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $item = PaySalaryApply::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [ 'id' => $id],
                'for_update' => true
            ]);
            if (empty($item)) {
                throw new \Exception('不存在此申请单');
            }

            if ($item->status != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            $request = $this->getRequest($id);
            $result = (new WorkflowServiceV2())->doApprove($request, $user, $this->getWorkflowParams($item, $user), $note);
            if (!empty($result->approved_at)) {
                $bool = $item->i_update([
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'updated_time' => date('Y-m-d H:i:s')
                ]);

                if ($bool === false) {
                    throw new \Exception('申请单审批通过失败');
                } else {
                    //给支付人员发送待支付信息邮件
                    $pay_staff_id = (new BaseService())->getSalaryPayStaffIds();
                    $this->sendEmailToAuditors($request,$pay_staff_id,1);
                    $this->delUnReadNumsKeyByStaffIds($pay_staff_id);

                    //16325需求，泰国需要将薪资发放审批通过的申请同步数据到支付模块
                    if(EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_WAGE, $item->salary_company_id)) {
                        PayService::getInstance()->saveOne($item);
                    }
                }
            }
            $db->commit();
        } catch (\Exception $exception) {
            $db->rollback();
            throw $exception;
        }

        return true;
    }

    public function reject($id, $note, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $item = PaySalaryApply::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => [ 'id' => $id],
                'for_update' => true
            ]);

            if (empty($item)) {
                throw new \Exception('不存在此申请单');
            }
            if ($item->status != Enums::CONTRACT_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('contract_has_been_withdrawal'));
            }

            $bool = (new WorkflowServiceV2())->doReject($this->getRequest($id), $user, $this->getWorkflowParams($item, $user), $note);
            if ($bool === false) {

                throw new \Exception('申请单审批通过失败');
            }
            $bool = $item->i_update([
                'status' => Enums::CONTRACT_STATUS_REJECTED,
                'pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY,
                'updated_time' => date('Y-m-d H:i:s')
            ]);
            if ($bool === false) {
                throw new \Exception('撤销失败');
            }

            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            throw $e;
        }

        return true;
    }

    public function cancel($id, $note, $user)
    {

        // 只能自己撤销自己
        $item = PaySalaryApply::findFirst([
            'conditions' => ' id = :id: and created_id = :uid: ',
            'bind' => [ 'id' => $id, 'uid' => $user['id']],
            'for_update' => true
        ]);
        if (empty($item)) {
            throw new ValidationException('非法撤销!');
        }

        $bool = (new WorkflowServiceV2())->doCancel($this->getRequest($id), $user, $this->getWorkflowParams($item, $user), $note);
        if ($bool === false) {
            throw new \Exception('审批流撤销失败');
        }

        $bool = $item->i_update([
            'status' => Enums::CONTRACT_STATUS_CANCEL,
            'pay_status' => Enums::PAYMENT_PAY_STATUS_NOTPAY,
            'updated_time' => date('Y-m-d H:i:s')
        ]);
        if ($bool === false) {
            throw new \Exception('撤销失败');
        }

    }


    public function getRequest($id)
    {
        return $this->getRequestByBiz($id,$this->biz_type);
    }


    /**
     *
     * @param $id
     * @param $user
     * @return mixed|\Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws \App\Library\Exception\BusinessException
     */
    public function createRequest($id, $user)
    {
        $item = PaySalaryApply::findFirst([
            'conditions' => ' id = :id: ',
            'bind' => ['id' => $id]
        ]);

        $data['id'] = $item->id;
        $data['name'] = $item->xzno . '审批申请';
        $data['biz_type'] = $this->biz_type;
        $data['flow_id'] = $this->getFlowId($item);


        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($item, $user));
    }


    public function getWorkflowParams($item, $user)
    {

        return [
            'amount'       => $item->salary_amount,
            'currency'     => $item->currency,
            'company_id'   => $item->salary_company_id,
            'submitter_id' => $item->create_id,
        ];

    }

    /**
     * @description 获取审批流ID
     * @param $model
     * @return int
     */
    public function getFlowId($model = null)
    {
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            if (empty($model) || $model->salary_company_id == 1) {
                //FlashExpress
                return Enums::WF_FLOW_ID_SALARY_APPLY_FLASH_EXPRESS;
            } else if ($model->salary_company_id == 6) {
                //Flash Home Operation
                return Enums::WF_FLOW_ID_SALARY_APPLY_FLASH_HOME_OPERATION;
            } else {
                //其他公司
                return Enums::WF_FLOW_ID_SALARY_APPLY_OTHERS;
            }
        } else {
            if (empty($model) || $model->salary_company_id == 1) {
                //FlashExpress
                return Enums::WF_FLOW_ID_SALARY_APPLY_FLASH_EXPRESS;
            } else {
                //其他公司
                return Enums::WF_FLOW_ID_SALARY_APPLY_OTHERS;
            }
        }
    }

}