<?php

namespace App\Modules\Salary\Services;

use App\Library\RedisClient;
use App\Modules\Common\Models\EnvModel;
use App\Util\RedisExpire;
use App\Util\RedisKey;

class BaseService extends \App\Library\BaseService
{
    const LIST_TYPE_DATA = 0;
    const LIST_TYPE_APPLY = 1;
    const LIST_TYPE_AUDIT = 2;
    const LIST_TYPE_FYR = 3;
    const LIST_TYPE_PAY = 4;

    /**
     * 获取编号
     * @return string
     */
    public static function getNo($date, $key = NULL)
    {
        $key = self::getCounterKey($date, $key);
        if (self::getCounter($key)) {           //有计数器
            $lno = self::incrCounter($key);
        } else {
            $lno = self::setCounter($key);
        }
        return $date . sprintf('%04s', $lno);
    }


    /**
     * 判断计数器是否存在
     * @param string $key
     * @return bool|int
     */
    private static function getCounter($key)
    {
        return RedisClient::getInstance()->getClient()->exists($key);
    }

    /**
     * 计数器不存在的情况下
     * @param string $key
     * @return bool|int
     */
    private static function setCounter($key)
    {
        $lno = 1;
        RedisClient::getInstance()->getClient()->setex($key, RedisExpire::ONE_DAY, $lno);
        return $lno;
    }

    /**
     * 计数器存在的情况下
     * @param string $key
     * @return int
     */
    private static function incrCounter($key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($key, 1);
    }


    private static function getCounterKey($date, $key = NULL)
    {
        $key = $key ?? RedisKey::WAGE_APPLY_COUNTER;
        return $key . "_" . $date;
    }



    public function getSalaryPayStaffIds(){
        $pay_staff_id = EnvModel::getEnvByCode("salary_pay_staff_id");
        $pay_staff_id = explode(',', $pay_staff_id);
        return $pay_staff_id ?? [];
    }

}