<?php

namespace App\Modules\Salary\Services;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Salary\Models\PaySalaryApply;

class UpdateService extends BaseService
{
    public static $validate_pay = [
        'id' => 'Required|Int',
        'pay_status' => 'Required|StrIn:2,3',
        'payer_bank' => [
            'IfStrEq:pay_status,2|Required|Str',
        ],
        'payer_no' => [
            'IfStrEq:pay_status,2|Required|Str',
        ],
        'pay_date' => [
            'IfStrEq:pay_status,2|Required|Date',
        ],
        'pay_remark' => [
            'IfStrEq:pay_status,2|Str',
            'IfStrEq:pay_status,3|Required|Str',
        ]
    ];

    /**
     *
     * @param $id
     * @param $data
     * @param $user
     * @param int $is_from 1本模块，2是付款模块
     * @return array
     */
    public function updatePayment($id, $data, $user, $is_from = Enums\PayEnums::IS_FROM_SELF)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $item = PaySalaryApply::findFirst(
                [
                    'id = :id:',
                    'bind' => ['id' => $id,]
                ]
            );
            if (empty($item)) {
                throw new BusinessException("获取薪酬发放审批失败", ErrCode::$BUSINESS_ERROR);
            }
            if ($item->status != Enums::WF_STATE_APPROVED) {
                throw new BusinessException("申请未通过审批", ErrCode::$BUSINESS_ERROR);
            }

            // 16325需求，增加非待支付状态, 不可重复支付；原先这个逻辑缺失会有问题
            if ($item->pay_status != Enums::PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('repeated_payment_error_hint', ['serial_no' => $item->xzno]), ErrCode::$VALIDATE_ERROR);
            }
            //16325需求，对于自己业务侧支付，但是单据已对接到支付模块，在这里不可支付
            if ($is_from == PayEnums::IS_FROM_SELF && $item->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->handleData($data, $item, $user);
            $bool = $item->i_update($data);
            if ($bool === false) {
                throw new BusinessException("更新薪酬审批失败", ErrCode::$CONTRACT_UPDATE_ERROR);
            }

            $db->commit();

            $this->delUnReadNumsKeyByStaffIds($this->getSalaryPayStaffIds());

        } catch (ValidationException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {                 //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {                       //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning(__METHOD__ . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message
        ];
    }

    public function handleData($data, $item, $user)
    {
        $new = [];
        $new['payer_bank'] = $data['payer_bank'] ?? '';
        $new['payer_no'] = $data['payer_no'] ?? '';
        $new['pay_date'] = empty($data['pay_date']) ? null : $data['pay_date'];
        $new['payed_at'] = date("Y-m-d H:i:s");
        $new['pay_status'] = $data['pay_status'];
        $new['pay_remark'] = $data['pay_remark'] ?? "";
        $new['payer_id'] = $user['id'];
        return $new;
    }
}