<?php

namespace App\Modules\Salary\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Reimbursement\Models\Pccode;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\User\Services\UserService;
use App\Modules\User\Models\DepartmentModel;
use App\Library\Enums;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use Mpdf\Mpdf;
use Mpdf\Output\Destination;
use App\Modules\Common\Services\WaterMarkerService;

class ApplyService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 验证wht税率
     * @throws ValidationException
     */
    protected function checkWHTRate($wht_category, $wht_tax_rate): bool
    {
        $wht_category_arr = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'rate_list', 'value');

        $rate_list = $wht_category_arr[$wht_category] ?? [];
        if (empty($rate_list)) {
            throw new ValidationException(self::$t->_('wht_category_invalid'));
        }
        $is_error = true;
        foreach ($rate_list as $item) {
            if ($item['value'] == $wht_tax_rate && $item['is_enabled']) {
                $is_error = false;
            }
        }
        if ($is_error) {
            throw new ValidationException(self::$t->_('wht_tax_rate_invalid'));
        }
        return true;
    }



    /**
     * 计算wht金额和实付金额
     * @param $not_tax_amount
     * @param $wht_category
     * @param $wht_tax_rate
     * @param $input_wht_amount
     * @return string[]
     * @throws ValidationException
     */
    public function calWHTAmountAndRealPayAmount(
        $not_tax_amount,
        $wht_category,
        $wht_tax_rate,
        $input_wht_amount
    ): array {
        $this->checkWHTRate($wht_category, $wht_tax_rate);

        $data = [
            'wht_amount'   => '0.00',
            'real_amount'  => '0.00',
            'is_out_scope' => false,
        ];

        if (bccomp($not_tax_amount, '0.00', 2) <= 0) {
            return $data;
        }

        $data['wht_amount'] = round(bcmul($not_tax_amount, $wht_tax_rate / 100, 3), 2);

        //输入的wht金额与计算值比较，不能超过1
        if ($input_wht_amount !== false) {
            if (bccomp(abs(bcsub($data['wht_amount'], $input_wht_amount, 2)), 1, 2) == 1) {
                $data['is_out_scope'] = true;
            } else {
                $data['wht_amount'] = $input_wht_amount;
            }
        }
        $data['real_amount'] = round(bcsub($not_tax_amount, $data['wht_amount'], 3), 2);
        return $data;
    }



    /**
     * 添加薪资发放审批
     *
     * @param $data
     * @param $user
     * @return bool
     * @throws \Exception
     */
    public function saveOne($data, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $data = $this->handleData($data, $user);
            $model = new PaySalaryApply();
            $data['status'] = Enums::CONTRACT_STATUS_PENDING;
            $bool = $model->i_create($data);
            if (!$bool) {
                $messages = $model->getMessages();
                throw new \Exception('薪资发放申请单 创建失败=' . implode(",", $messages), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $bool = (new SalaryFlowService())->createRequest($model->id, $user);
            if (!$bool) {
                throw new \Exception('薪资发放申请单 创建失败=' . json_encode($data, JSON_UNESCAPED_UNICODE), ErrCode::$CONTRACT_CREATE_ERROR);
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollback();
            throw $exception;
        }

        return true;
    }

    /**
     * @param $id
     * @return array|void
     */
    public function detail($id,$isDownload=false){
        $item = PaySalaryApply::findFirst(
            [
                'conditions'=>'id = :id:',
                'bind'=>['id'=>$id]
            ]
        );
        if(empty($item)){
            throw new ValidationException('不存在的薪资发放申请');
        }

        $request = (new SalaryFlowService())->getRequest($id);
        if(empty($request)){
            throw new ValidationException('不存在的薪资发放审批');
        }
        $companyList = $this->getSalaryCompanyList();
        $wht_category_arr = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');

        $data = $item->toArray();
        $data['auth_logs'] =  $this->getAuditLogs($request,$item,$isDownload);

        $data['pay_start_date'] = $data['pay_start_date'] != '1970-01-01' ? $data['pay_start_date'] : '';
        $data['pay_end_date'] = $data['pay_end_date'] != '1970-01-01' ? $data['pay_end_date'] : '';

        $data['status_text'] = static::$t->_(Enums::$loan_status[$data['status']] ?? '');
        $data['pay_status_text'] = static::$t->_(Enums::$loan_pay_status[$data['pay_status']] ?? '');
        $data['salary_type_text'] = static::$t->_(Enums::$salary_type[$data['salary_type']] ?? '');
        $data['pay_type_text'] = static::$t->_(Enums::$salary_pay_type[$data['pay_type']] ?? '');
        $data['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['currency']])??'';
        $data['salary_amount'] = bcdiv($data['salary_amount'], 1000,2);
        $data['salary_company_name'] = $companyList[$data['salary_company_id']] ?? '';
        $data['not_tax_amount']      = bcdiv($data['not_tax_amount'], 1000,
            2); //不含税金额
        $data['wht_amount']          = bcdiv($data['wht_amount'], 1000,
            2); //wht金额
        $data['wht_category']        = !empty($data['wht_category']) && !empty($wht_category_arr[$data['wht_category']]) ? $wht_category_arr[$data['wht_category']] : '/';  //wht类别
        $data['wht_tax_rate']        = $data['wht_tax_rate'] . '%';

        return $data;
    }


    /**
     * 撤销薪资发放审批
     *
     * @param $id
     * @param $note
     * @param $userId
     *
     */
    public function cancel($id, $note, $user)
    {
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            (new SalaryFlowService())->cancel($id, $note, $user);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollback();
            throw $exception;
        }

        return true;

    }


    /**
     *
     * 获得审核列表
     * @param $condition
     * @param $uid
     * @param int $type
     * @param bool $if_download
     * @return array
     */
    public function getList($condition, $uid = null, $type = 0, $if_download = false)
    {
        $condition['uid'] = $uid;
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $columns = [
                'p.id',
                'p.xzno',
                'p.apply_date',
                'p.created_id',
                'p.create_id',
                'p.create_name',
                'p.create_department',
                'p.create_node_department',
                'p.create_company_id',
                'p.create_company_name',
                'p.create_department_name',
                'p.create_store',
                'p.create_store_name',
                'p.create_center_code',
                'p.pay_start_date',
                'p.pay_end_date',
                'p.pay_type',
                'p.currency',
                'p.salary_company_id',
                'p.salary_type',
                'p.salary_start_date',
                'p.salary_end_date',
                'p.salary_month',
                'p.salary_remark',
                'p.salary_amount',
                'p.status',
                'p.pay_status',
                'p.payer_id',
                'p.payer_bank',
                'p.payer_no',
                'p.pay_date',
                'p.pay_remark',
                'p.payed_at',
                'p.pay_from',
                'p.created_time',
                'p.updated_time',
                'p.not_tax_amount',
                'p.wht_category',
                'p.wht_tax_rate',
                'p.wht_amount',
            ];

            // 审核模块的已处理列表, 展示处理时间
            if ($type == self::LIST_TYPE_AUDIT && isset($condition['flag']) && $condition['flag'] == GlobalEnums::AUDIT_TAB_PROCESSED) {
                $columns[] = 'log.audit_at';
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->from(['p' => PaySalaryApply::class]);
            $builder = $this->getCondition($builder, $condition, $type);

            $count = (int) $builder->columns('COUNT(DISTINCT p.id) AS total')->getQuery()->getSingleResult()->total;

            $items = [];
            if ($count) {
                $builder->columns($columns);
                $builder->groupBy('p.id');

                if (!in_array($type, [self::LIST_TYPE_AUDIT, self::LIST_TYPE_FYR])) {
                    $builder->orderBy('p.id desc');
                }

                if (!$if_download) {
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->formatList($items);
            }

            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('salary-list-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * @param $builder
     * @param $condition
     * @param $type
     * @return mixed
     */
    private function getCondition($builder, $condition, $type = 0)
    {
        $status = $condition['status'] ?? 0;
        $pay_status = $condition['pay_status'] ?? 0;

        // 征询回复进度
        $is_reply = $condition['is_reply'] ?? GlobalEnums::CONSULTED_REPLY_STATE_PENDING;
        $is_reply = in_array($is_reply, GlobalEnums::CONSULTED_REPLY_STATE_ITEM) ? $is_reply : GlobalEnums::CONSULTED_REPLY_STATE_PENDING;

        // 审批处理进度
        $flag = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;
        $flag = in_array($flag, GlobalEnums::AUDIT_TAB_STATE_ITEM) ? $flag : GlobalEnums::AUDIT_TAB_PENDING;

        $xzno = $condition['xzno'] ?? '';           //薪资发放编号
        $start_date = $condition['start_date'] ?? '';  //创建时间，开始时间
        $end_date = $condition['end_date'] ?? '';   //创建时间，结束时间

        if (!empty($xzno)) {
            $builder->andWhere('p.xzno = :xzno:', ['xzno' => $xzno]);
        }

        if (!empty($status)) {
            $builder->andWhere('p.status = :status:', ['status' => $status]);
        }
        if (!empty($pay_status)) {
            $builder->andWhere('p.pay_status = :pay_status:', ['pay_status' => $pay_status]);
        }

        if (!empty($start_date)) {
            $builder->andWhere('p.apply_date >= :apply_date_start:', ['apply_date_start' => $start_date]);
        }

        if (!empty($end_date)) {
            $builder->andWhere('p.apply_date <= :apply_date_end:', ['apply_date_end' => $end_date]);
        }

        if (!empty($condition['pay_date_start']) && !empty($condition['pay_date_end'])) {
            $builder->andWhere('p.pay_date >= :pay_date_start: and p.pay_date <= :pay_date_end:', ['pay_date_start' => $condition['pay_date_start'], 'pay_date_end' => $condition['pay_date_end']]);
        }

        //费用所属公司
        if (!empty($condition['salary_company_id'])) {
            if (is_array($condition['salary_company_id'])) {
                $builder->andWhere('p.salary_company_id IN ({salary_company_id:array})', ['salary_company_id' => array_values($condition['salary_company_id'])]);
            } else {
                $builder->andWhere('p.salary_company_id = :salary_company_id:', ['salary_company_id' => $condition['salary_company_id']]);
            }
        }
        //发放类型
        if (!empty($condition['salary_type'])) {
            $builder->andWhere('p.salary_type = :salary_type:', ['salary_type' => $condition['salary_type']]);
        }

        if ($type == self::LIST_TYPE_AUDIT) {
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_SALARY_APPLY], $condition['uid'], 'p');

        } else if ($type == self::LIST_TYPE_APPLY) {
            $builder->andWhere('p.created_id = :uid:', ['uid' => $condition['uid']]);

        } else if ($type == self::LIST_TYPE_FYR) {
            // 意见征询回复列表 'pay_status_field_name'
            $biz_table_info = ['table_alias' => 'p', 'pay_status_field_name' => ''];
            $builder = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $is_reply, [Enums::WF_SALARY_APPLY], $condition['uid'], $biz_table_info);

        } else if ($type == self::LIST_TYPE_PAY) {
            $builder->andWhere('p.status = :status: and p.is_pay_module = :is_pay_module:', ['status' => Enums::WF_STATE_APPROVED, 'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
            if ($pay_status == Enums::LOAN_PAY_STATUS_PAY || $pay_status == Enums::LOAN_PAY_STATUS_NOTPAY){
                $builder->andWhere('p.payer_id = :payer_id:',['payer_id'=>$condition['uid']]);
            }
        }

        return $builder;
    }

    public function exportList($condition)
    {
        $condition['pageSize'] = 100000;
        $condition['pageNum'] = 1;

        $data = $this->getList($condition);
        $data = $data['data'];
        if ($data['pagination']['total_count'] > 0) {
            $header = [
                static::$t->t('re_field_id'),
                static::$t->t('re_field_apply_name'),
                static::$t->t('re_field_apply_id'),
                static::$t->t('re_field_apply_department_name'),
                static::$t->t('global.apply.date'),
                static::$t->t('wage_field_salary_type'),
                static::$t->t('contract_contract_type_company'),
                static::$t->t('distribution_cycle'),
                static::$t->t('distribution_month'),
                static::$t->t('salary_apply_not_tax_amount'),
                static::$t->t('deposit_wht_category'),
                static::$t->t('deposit_wht_rate'),
                static::$t->t('salary_apply_wht_amount'),
                static::$t->t('deposit_sum_money'),
                static::$t->t('global.apply.status.text'),
                static::$t->t('global_pay_status'),
                static::$t->t('pay_date'),
            ];
            $body = [];
            foreach ($data['items'] as $k=>$v){
                $body[] = [
                    $v['xzno'],
                    $v['create_name'],
                    $v['create_id'],
                    $v['create_department'],
                    $v['apply_date'],
                    $v['salary_type_text'],
                    $v['salary_company_name'],
                    $v['salary_start_date'] . ' - ' . $v['salary_end_date'],
                    $v['salary_month'],
                    $v['not_tax_amount'] . ' ' . $v['currency_text'],
                    $v['wht_category'],
                    $v['wht_tax_rate'],
                    $v['wht_amount'] . ' ' . $v['currency_text'],
                    $v['amount'] . ' ' . $v['currency_text'],
                    $v['status_text'],
                    $v['pay_status_text'],
                    empty($v['pay_date']) ? '' : $v['pay_date'], // 付款日期
                ];
            }
            return $this->exportExcel($header,$body,'salary_'.date("YmdHis"));
        }

        return ['code'=>0,'data'=>'no data'];
    }


    private function formatList($list)
    {
        $result = [];
        $companyList = $this->getSalaryCompanyList();
        $wht_category_arr = array_column(EnumsService::getInstance()->getFormatWhtRateConfig(), 'label', 'value');

        foreach ($list as $item) {
            $result[] = [
                'id' =>$item['id'],
                'xzno' => $item['xzno'],
                'create_id' => $item['create_id'],
                'create_name' => $item['create_name'],
                'create_department' => $item['create_department_name'],
                'create_company_name'=> $item['create_company_name'],
                'apply_date' => $item['apply_date'],
                'amount' => bcdiv($item['salary_amount'], 1000, 2),
                'currency' => $item['currency'],
                'status' => $item['status'],
                'pay_status' => $item['pay_status'],
                'salary_month' => $item['salary_month'],
                'salary_start_date' => $item['salary_start_date'],
                'salary_end_date' => $item['salary_end_date'],
                'salary_type'=>$item['salary_type'],
                'salary_company_id'=>$item['salary_company_id'],
                'status_text'=> static::$t->_(Enums::$loan_status[$item['status']] ?? ''),
                'pay_status_text'=>static::$t->_(Enums::$loan_pay_status[$item['pay_status']] ?? ''),
                'salary_type_text'=>static::$t->_(Enums::$salary_type[$item['salary_type']] ?? ''),
                'currency_text'=> static::$t->_(GlobalEnums::$currency_item[$item['currency']])??'',
                'salary_company_name'=> $companyList[$item['salary_company_id']],
                'audit_at' => $item['audit_at'] ?? '',
                'not_tax_amount'      => bcdiv($item['not_tax_amount'], 1000, 2),//不含税金额
                'wht_amount'          => bcdiv($item['wht_amount'], 1000, 2),//wht金额
                'wht_category'        => !empty($item['wht_category']) && !empty($wht_category_arr[$item['wht_category']]) ? $wht_category_arr[$item['wht_category']] : '/',  //wht类别
                'wht_tax_rate'        => $item['wht_tax_rate'] . '%',//wht税率
                'pay_date'            => empty($item['pay_date']) ? '' : $item['pay_date'],//付款日期
            ];
        }
        return $result;
    }


    /**
     * @param $data
     * @param $user
     * @return array
     * @throws ValidationException
     */
    private function handleData($data, $user)
    {
        $result = [];
        $result['xzno'] = $data['xzno'];
        $result['apply_date'] = $data['apply_date'];
        $result['created_id'] = $user['id'];    //创建人，用来查询列表

        $result['create_id'] = $data['create_id'];
        $result['create_name'] = $data['create_name'];
        $result['create_company_id'] = $data['create_company_id'];
        $result['create_company_name'] = (string) $data['create_company_name'];
        $result['create_department'] = $data['create_department'];
        $result['create_department_name'] = (string) $data['create_department_name'];
        $result['create_node_department'] = $data['create_node_department'];
        $result['create_store'] = $data['create_store'];
        $result['create_store_name'] = $data['create_store_name'];
        $result['create_center_code'] = $data['create_center_code'];

        $result['pay_start_date'] = $data['pay_start_date'] ? $data['pay_start_date'] : '1970-01-01';
        $result['pay_end_date'] = $data['pay_end_date'] ? $data['pay_end_date'] : '1970-01-01';

        $result['pay_type'] = $data['pay_type'];
        $result['currency'] = $data['currency'];
        $result['salary_type'] = $data['salary_type'];
        $result['salary_start_date'] = $data['salary_start_date'];
        $result['salary_end_date'] = $data['salary_end_date'];
        $result['salary_month'] = $data['salary_month'];
        $result['salary_company_id'] = $data['salary_company_id'];
        if ($data['salary_remark']) {
            $result['salary_remark'] = $data['salary_remark'];
        }
        $result['wht_category'] = $data['wht_category'];
        $result['wht_tax_rate'] = $data['wht_tax_rate'];
        //计算金额
        $calResult = $this->calWHTAmountAndRealPayAmount($data['not_tax_amount'], $data['wht_category'],
            $data['wht_tax_rate'], $data['wht_amount']);
        if ($calResult['is_out_scope']) {
            throw new ValidationException(self::$t->_('salary_apply_wht_amount_out_scope'));
        }
        $result['not_tax_amount'] = bcmul($data['not_tax_amount'], 1000);
        $result['wht_amount']     = bcmul($calResult['wht_amount'], 1000);
        $result['salary_amount']  = bcmul($calResult['real_amount'], 1000);
        return $result;
    }

    /**
     * 获取申请人信息
     *
     * @param $user
     * @return array
     * @throws \Exception
     *
     */
    public function defaultData($user)
    {
        if (empty($user)) {
            throw new ValidationException("not found user");
        }

        $nowDate = date('Ymd');
        $data['xzno'] = 'XZ' . static::getNo($nowDate);
        $data['apply_date'] = date("Y-m-d", strtotime($nowDate));
        $data['company_list'] =  $this->getCompanyList();
        $data['wht_category'] = EnumsService::getInstance()->getFormatWhtRateConfig();
        $data['salary_types'] = $this->getSalaryTypes();
        return $data;
    }

    protected function getSalaryTypes(): array
    {
        foreach (Enums::$salary_type as $type => $key) {
            $itemTypes[] = ['label' => self::$t->t($key), 'value' => intval($type)];
        }
        return $itemTypes ?? [];
    }


    /**
     * 去bi里面取相关数据
     * @param $userId
     * @return array|string
     */
    public function getUserMetaFromBi($userId)
    {
        $model = (new UserService())->getUserByIdInRbi($userId);
        if (empty($model)) {
            return static::$t->_("re_staff_info_id_not_exist");
        }

        if ($model->state == 2) {
            return static::$t->_("re_staff_info_id_left");
        }

        if ($model->formal != 1) {
            return static::$t->_("re_staff_info_id_not_formal");
        }


        $data = [];
        $id = $model->staff_info_id ?? "";
        $name = $model->name ?? "";
        $nick_name = $model->nick_name ?? "";
        $department_id = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $department_id = empty($node_department_id) ? $department_id : $node_department_id;
        $department_name = "";
        $company_name = "";
        $company_id = 0;

        $t = DepartmentModel::findFirst(
            [
                "conditions" => "id = :id:",
                "bind" => [
                    "id" => $department_id,
                ]
            ]
        );

        if (!empty($t)) {
            $department_name = $t->name ?? '';
            $company_name = $t->company_name ?? '';
            $company_id = $t->company_id ?? 0;
        }

        $data['create_id'] = $id;
        $data['create_name'] = $this->getNameAndNickName($name, $nick_name);

        $data['create_company_id'] = $company_id;
        $data['create_company_name'] = $company_name;

        $data['create_department'] = $department_id;
        $data['create_node_department'] = $node_department_id;
        $data['create_department_name'] = $department_name;

        $data['create_store'] = $model->sys_store_id;

        if ($model->sys_store_id == -1) {
            $data['create_store_name'] = 'Header Office';
            $item = Pccode::findFirst(["conditions" => "department_id = :id:", "bind" => ["id" => $department_id]]);
            if (!empty($item)) {
                $data['create_center_code'] = $item->pc_code;
            }
        } else {
            $tmp = SysStoreModel::findFirst("id = '" . $model->sys_store_id . "'");
            $data['create_store_name'] = $tmp ? $tmp->name : '';
            $data['create_center_code'] = $this->getPcCodeByStoreId($model->sys_store_id);
        }
        return $data;
    }

    /**
     * @param $req
     * @param $item
     * @param bool $isDownload
     * @return array
     */
    private function getAuditLogs($req, $item, $isDownload = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        //下载的时候不要申请
        if ($isDownload) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                //如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }
                $temp[] = $v;
            }
            $auth_logs = $temp;
        }

        //下载不要支付的
        if (!$isDownload && $item->status == Enums::CONTRACT_STATUS_APPROVAL && ($item->pay_status == Enums::LOAN_PAY_STATUS_PAY || $item->pay_status == Enums::LOAN_PAY_STATUS_NOTPAY)) {
            $us = new UserService();
            $current = $us->getUserById($item->payer_id);
            //放入付款
            if ($current && !is_string($current)) {
                $payLogs = [
                    'staff_id' => $item->payer_id,
                    'staff_name' => $current->name,
                    'staff_department' => $current->getDepartment()->name ?? '',
                    'job_title' => $current->getJobTitle()->name ??'',
                    'action_name' => self::$t->_(Enums::$loan_pay_status[$item->pay_status]),
                    'audit_at' => $item->payed_at,
                    'audit_at_datetime' => $item->payed_at,
                    'action' => 5,
                    "info" => $item->pay_remark
                ];
                array_unshift($auth_logs, $payLogs);
            }
        }
        //下载要正序
        if($isDownload){
            $auth_logs = array_reverse($auth_logs);
        }
        return $auth_logs;
    }


    public function download($id,$uid,$lang){
        if(!in_array($lang,['en','th','zh-CN'])){
            $lang = 'th';
        }
        self::setLanguage($lang);

        $data = $this->detail($id,true);

        $file_path = sys_get_temp_dir()."/salary-" . md5($id) .'_' . date('ymdHis') . ".pdf";

        $view = new \Phalcon\Mvc\View();
        $path = APP_PATH . '/views';
        $view->setViewsDir($path);
        $view->setVars($data);

        $view->start();
        $view->disableLevel(
            [
                \Phalcon\Mvc\View::LEVEL_LAYOUT => false,
                \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
            ]
        );

        $view->render("salary", "salary_".$lang);
        $view->finish();
        $content = $view->getContent();
        $mpdf = new Mpdf(
            [
                'format' => 'A4',
                'mode' => 'zh-CN'
            ]
        );
        $mpdf->useAdobeCJK = true;
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        $mpdf->SetDisplayMode('fullpage');
        $mpdf->SetHTMLHeader("");
        $mpdf->SetHTMLFooter("");
        $mpdf->WriteHTML($content);
        $mpdf->Output($file_path, Destination::FILE);

        // pdf 加水印
        WaterMarkerService::getInstance()->addWaterMarkerToPdfFile($file_path, $file_path);

        // 生成成功, 上传OSS
        $upload_res = OssHelper::uploadFile($file_path);
        $return = [
            'code' => ErrCode::$SUCCESS,
            'msg' => '',
            'data' => ''
        ];

        if (!empty($upload_res['object_url'])) {
            $return['data'] = $upload_res['object_url'];
            $return['msg'] = 'success';
        } else {
            $return['code'] = ErrCode::$BUSINESS_ERROR;
            $return['msg'] = 'pdf file download failed';
        }

        return $return;
    }

    /**
     * 获得薪资信息下拉列表
     * @return array
     */
    public function getCompanyList(): array
    {
        $list = [];
        foreach ($this->getSalaryCompanyList() as $k => $v) {
            $list[] = ['id' => $k, 'value' => $v];
        }
        return $list;
    }

    public function getPcCodeByStoreId($store_id)
    {
        $item = SysStoreModel::findFirst(["conditions" => "id = :id:", "bind" => ["id" => $store_id]]);
        if (!empty($item)) {
            return $item->sap_pc_code;
        }
        return "";
    }


    public function getWaitingPayCount($user_id){
        if(empty($user_id)){
            return 0;
        }

        $pay_staff_id = $this->getSalaryPayStaffIds();
        if(empty($pay_staff_id) || !in_array($user_id,$pay_staff_id)){
            return 0;
        }

        return PaySalaryApply::count(
            [
                'conditions' => 'is_pay_module = :is_pay_module: AND status = :status: AND pay_status = :pay_status:',
                'bind' => [
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO,
                    'status' => Enums::PAYMENT_APPLY_STATUS_APPROVAL,
                    'pay_status' => Enums::PAYMENT_PAY_STATUS_PENDING
                ],
            ]
        );
    }

    /**
     * @description 获取薪资公司list
     * @return array
     */
    public function getSalaryCompanyList(): array
    {
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $companyList = Enums::$salary_company_list_th;
        } else {
            $companyList = Enums::$salary_company_list;
        }
        return $companyList;
    }

    /**
     * @description 获取薪资发放申请公司id对应部门映射 (For 支付)
     * @return array
     */
    public function getSalaryCompanyToPaymentDepartmentMap(): array
    {
        return Enums::$salary_company_map;
    }
}