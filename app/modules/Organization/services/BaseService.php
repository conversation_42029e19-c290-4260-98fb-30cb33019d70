<?php

namespace App\Modules\Organization\Services;

use App\Library\OssHelper;

class BaseService extends \App\Library\BaseService
{
    const STORE_TYPE_DEPARTMENT_ID = [32, 51, 57, 58, 59, 106, 95, 65];//网点类型部门ID
    const SHOW_REGION_PIECE = [32, 51, 483, 1183];
    //const SHOW_STORE = [58, 59, 106, 95, 501, 502, 503];
    const SHOW_STORE = [];//hub部门不在显示网点直接显示部门

    const STORE_TYPE_DEPARTMENT_ID_PH = [123, 18, 5110];//const STORE_TYPE_DEPARTMENT_ID_PH = [123, 18, 126];
    const SHOW_REGION_PIECE_PH = [123, 18];
    //const SHOW_STORE_PH = [5110];
    const SHOW_STORE_PH = [];//hub部门不在显示网点直接显示部门

    const STORE_TYPE_DEPARTMENT_ID_MY = [320, 350, 328];
    const SHOW_REGION_PIECE_MY = [320, 350, 15084];
    const SHOW_STORE_MY = [328];

    const STORE_TYPE_DEPARTMENT_ID_VN = [];
    const SHOW_REGION_PIECE_VN = [];
    const SHOW_STORE_VN = [];

    const STORE_TYPE_DEPARTMENT_ID_LA = [];
    const SHOW_REGION_PIECE_LA = [];
    const SHOW_STORE_LA = [];

    const STORE_TYPE_DEPARTMENT_ID_ID = [];
    const SHOW_REGION_PIECE_ID = [];
    const SHOW_STORE_ID = [];

    //是否显示负责人是正职和代理
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE = [1104,1110];//Network Bulky Operations , Network Operations
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH = [18, 5492, 5564, 5565, 5566];
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY = [320, 15084];
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE_VN = [];
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE_LA = [];
    const SHOW_DEPARTMENT_MANAGER_POSITION_STATE_ID = [];

    //展示正职，代理的部门id
    public $show_manager_position_department_ids = [];

    /**
     * 返回 部门树状结构数据
     * @param $data 部门列表
     * @param $hr_staffs 员工表数据（负责人信息匹配）
     * @param array $department_worker_nums 员工人数
     * @param int $parentId
     * @param int $node_level 当前部门节点级别
     * @return array
     */
    public function _treeNode($data, $hr_staffs, $department_worker_nums, $parentId = 0, $node_level = 0,$deparment_list='',$hc_summary_list=[], $children_department = [])
    {
        if(empty($data) || !is_array($data)) return [];
        $node_level++;
        $tree_node = [];
        $countryCode = strtoupper(env('country_code', 'TH'));
        foreach ($data as $key => $value) {
            if ($parentId == $value['ancestry']) {
                $item = [
                    'department_id'            => intval($value['id']),//前端不好判断，支持department_id
                    'id'                       => intval($value['id']),
                    'label'                    => $value['name'],
                    'ancestry'                 => intval($value['ancestry']),
                    'type'                     => intval($value['type']),
                    'level'                    => intval($value['level']),
                    'node_level'               => $node_level,//节点等级
                    'company_id'               => intval($value['company_id']),
                    'group_boss_id'            => intval($value['group_boss_id']),
                    'is_group'                 => 0,
                    'ancestry_name'            => $value['company_name'],
                    'staff_info_id'            => intval($value['manager_id']),//负责人工号
                    'staff_info_name'          => $hr_staffs[$value['manager_id']]['name'] ?? '',//负责人姓名
                    'job_title'                => $hr_staffs[$value['manager_id']]['job_title_name'] ?? '',//职位
                    'hr_staff_state'           => $hr_staffs[$value['manager_id']]['state'] ?? 1,//负责人在职状态
                    'work_count'               => $department_worker_nums[$value['id']]['count'] ?? 0,//该部门直属员工人数
                    'current_type'             => 1,//获取网点数据时候使用（标识当前ID是部门ID）
                    'children'                 => $this->_treeNode($data, $hr_staffs, $department_worker_nums, $value['id'], $node_level,$deparment_list,$hc_summary_list, $children_department),
                    'next_type'                => 0,//值不同，请求的接口不同（获取 大区或网店接口的标识）
                    'is_department'            => 1,//部门数据返回该字段
                    'hasChildren'              => false,
                    'show_list_create_btn'     => self::is_show_list_create_btn($value['id'],$value['level'],$node_level),//列表页是否显示"新建"按钮
                    'show_list_structures_btn' => $node_level == 1,//列表页是否显示"查看架构图"按钮有权限的前提下的是否显示逻辑）
                    'sap_company_id'                => $value['sap_company_id'],
                    'sap_reporting_currency'        => $value['sap_reporting_currency'],
                    'sap_describe'                  => $value['sap_describe'],
                    'sap_cost_center'               => $value['sap_cost_center'],
                    'sap_company_address'           => $value['sap_company_address'],
                    'sap_tax_id'                    => $value['sap_tax_id'],
                    'country_code'                  => $value['country_code'] ?? '',
                    'last_country_code'             => $deparment_list[$value['ancestry']] ?? '',
                    'manager_position_state'        => $value['manager_position_state'],
                    'budget_subordinates_sum'       => $hc_summary_list[$value['id']]['budget_real_sum'] ?? 0, //部门及下级部门 hc 预算数量
                    'budget_sum'                    => $hc_summary_list[$value['id']]['budget_real_sum'] ?? 0, //本部门 hc 预算数量
                    'relevance_store_id'            => $value['relevance_store_id'], //部门关联的网点id
                    'relevance_store_name'          => $value['relevance_store_name'], //部门关联网点名称
                    'is_show_relevance_store'       => $value['is_show_relevance_store'], //是否显示部门关联网点
                    'is_edit_organization_info'     => $value['is_edit_organization_info'],
                    'is_edit_organization_sap_info' => $value['is_edit_organization_sap_info'],
                    'kingdee_cost_center'           => $value['kingdee_cost_center'],
                ];
                $item['is_show_manager_position_state'] = (in_array($value['id'], $this->show_manager_position_department_ids)) ? 1 : 0;

                if (in_array($value['id'], $children_department)) {
                    $item['hasChildren'] = true;
                }

                //最终数据合并
                $tree_node[] = $item;

            }
        }
        return $tree_node;
    }



    /**
     * 查看部门结构图处理
     * @param $data
     * @param $hr_staffs
     * @param $department_worker_nums
     * @param int $parentId
     * @param int $level
     * @return array
     */
    public function _treeNodeViewStructures($data, $parentId = 0, $node_level = 0)
    {
        $tree_node = [];
        $node_level++;
        foreach ($data as $key => $value) {
            if ($parentId == $value['ancestry']) {
                $tree_node[] = [
                    'id'              => $value['id'], //部门ID
                    'label'           => $value['name'], // 部门名称
                    'children'        => $this->_treeNodeViewStructures($data, $value['id'], $node_level),

                ];

            }
        }
        return $tree_node;
    }

    /**
     * 根据部门树状关系统计每个部门包含子部门的人数的一个树状部门列表
     * @param $department_tree_list
     * @return array
     */
    public function count_department_total_worker_nums(&$department_tree_list)
    {
	    $sum['budget_subordinates_sum'] = $sum['work_count'] = 0;
        foreach ($department_tree_list as &$item) {
            $item['is_have_children'] = false;
            if ($item['children']) {
	            $item_data = $this->count_department_total_worker_nums($item['children']);
                $item['work_count'] += $item_data['work_count'];
	            $item['budget_subordinates_sum'] += $item_data['budget_subordinates_sum'];
                $item['is_have_children'] = true;
            }
            $sum['work_count'] += $item['work_count'];
	        $sum['budget_subordinates_sum'] += $item['budget_subordinates_sum'];
        }

        return $sum;

    }

    /**
     * 部门ID作为key值,员工数作为value 的树状关系数组
     * @param $data_list 包含下级部门人数的 部门tree 结构数据
     * @return array
     * 返回示例：
     * $id_map = [
     *   1=> [
     *    'work_count'=>17,'child'=>[
              11=>['work_count'=>8,'child'=>[xx]],
              12=>['work_count'=>8,'child'=>[xx]],
           ],
     *   ],
     *   2=>[xxx],
     *
     *  ]
     *
     */
    function _id_worker_num_tree_map($data_list)
    {
        $id_map = [];
        foreach ($data_list as $item) {
            $id_map[$item['id']]['work_count'] = $item['work_count'];
            $id_map[$item['id']]['child'] = [];
            if ($item['child']) {
                $id_map[$item['id']]['child'] = $this->_id_worker_num_tree_map($item['child']);
            }

        }
        return $id_map;
    }

    /**
     * 获取 部门ID和员工数的映射关系数组
     * key:部门ID，value:部门下员工数
     * @param $data_list
     * @param $id_map
     */
    function _id_worker_num_map($data_list, &$id_map)
    {
        foreach ($data_list as $item) {
            $id_map[$item['id']] = $item['work_count'];
            if ($item['child']) {
                $this->_id_worker_num_map($item['child'], $id_map);
            }

        }
    }

    /**
     * 是否显示创建按钮
     * 显示条件：一级部门、非网点二级部门、三级部门（有权限的前提下的是否显示逻辑）
     * @param $department_id
     * @param $depatment_level
     * @return bool
     */
    public static function is_show_list_create_btn($department_id,$level,$node_level){
        $countryCode = strtoupper(env('country_code', 'TH'));
        switch ($countryCode) {
            case 'PH':
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID_PH;
                break;
            case 'MY':
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID_MY;
                break;
            case 'VN':
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID_VN;
                break;
            case 'LA':
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID_LA;
                break;
            case 'ID':
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID_ID;
                break;
            default:
                $store_type_department_id = self::STORE_TYPE_DEPARTMENT_ID;
        }
        if (in_array($level, [1, 2, 3]) && in_array($node_level,[1,2,3]) && !in_array($department_id, $store_type_department_id)) {
            return true;
        }
        return false;
    }

    /**
     * 下载excel
     * @param $head
     * @param array $data
     * @param string $fileName
     * @return string
     */
    public static function excelToFile($head, $data = [], $fileName = 'excel.xlsx')
    {
        $config = [
            'path' => sys_get_temp_dir() . '/'
        ];
        $excel = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        $fileObject = $excel->fileName($fileName);
        $fileObject->header($head)->data($data);
        // 最后的最后，输出文件
        $filePath = $fileObject->output();
        //判断文件是否生成成功了
        if (!is_file($filePath)) {
            return "";
        }
        return OssHelper::uploadFile($filePath);
    }

    /**
     * 根据部门id 获取ancestry_v3部门链名称
     * @param $department_id
     * @param $department_list
     * @return array
     */
    public function getDepartmentLevel($department_id, $department_list) {
        $department_info = $department_list[$department_id] ?? [];

        $department_level = [
            0 => '-', //group ceo
            1 => '-', //c-level
            2 => '-', //bu
            3 => '-', //一级部门
            4 => '-', //二级部门
            5 => '-', //三级部门
            6 => '-', //四级部门
        ];

        if(!empty($department_info)) {
            $ancestry_arr = explode('/',$department_info['ancestry_v3'] ?? '');
            $ancestry_arr_0 = $ancestry_arr[0] ?? -1;
            $ancestry_arr_1 = $ancestry_arr[1] ?? -1;
            $ancestry_arr_2 = $ancestry_arr[2] ?? -1;
            $ancestry_arr_3 = $ancestry_arr[3] ?? -1;
            $ancestry_arr_4 = $ancestry_arr[4] ?? -1;
            $ancestry_arr_5 = $ancestry_arr[5] ?? -1;
            $ancestry_arr_6 = $ancestry_arr[6] ?? -1;
            switch ($department_info['type']) {
                case 1: //公司
                    $department_level = [
                        0 => $department_list[$ancestry_arr_0]['name'] ?? '-',
                        1 => $department_list[$ancestry_arr_1]['name'] ?? '-',
                        2 => $department_list[$ancestry_arr_2]['name'] ?? '-',
                    ];
                    break;
                case 2: //部门
                    $department_level = [
                        0 => $department_list[$ancestry_arr_0]['name'] ?? '-',
                        1 => $department_list[$ancestry_arr_1]['name'] ?? '-',
                        2 => $department_list[$ancestry_arr_2]['name'] ?? '-',
                    ];
                    if($department_list[$ancestry_arr_3]['level'] == 4) {
                        $department_level[6] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                    } else if($department_list[$ancestry_arr_3]['level'] == 3) {
                        $department_level[5] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                        $department_level[6] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                    } else if($department_list[$ancestry_arr_3]['level'] == 2) {
                        $department_level[4] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                        $department_level[5] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                        $department_level[6] = $department_list[$ancestry_arr_5]['name'] ?? '-';
                    }  else if($department_list[$ancestry_arr_3]['level'] == 1) {
                        $department_level[3] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                        $department_level[4] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                        $department_level[5] = $department_list[$ancestry_arr_5]['name'] ?? '-';
                        $department_level[6] = $department_list[$ancestry_arr_6]['name'] ?? '-';
                    }
                    break;
                case 3: //组织下部门
                    $department_level = [
                        0 => $department_list[$ancestry_arr_0]['name'] ?? '-',
                    ];
                    $department_ancestry_type = $department_list[$ancestry_arr_1]['type'] ?? 3;
                    if($department_info['ancestry'] == 999 || $department_ancestry_type == 3) {
                        if($department_list[$ancestry_arr_1]['level'] == 4) {
                            $department_level[6] = $department_list[$ancestry_arr_1]['name'] ?? '-';
                        } else if($department_list[$ancestry_arr_1]['level'] == 3) {
                            $department_level[5] = $department_list[$ancestry_arr_1]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                        } else if($department_list[$ancestry_arr_1]['level'] == 2) {
                            $department_level[4] = $department_list[$ancestry_arr_1]['name'] ?? '-';
                            $department_level[5] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                        }  else if($department_list[$ancestry_arr_1]['level'] == 1) {
                            $department_level[3] = $department_list[$ancestry_arr_1]['name'] ?? '-';
                            $department_level[4] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                            $department_level[5] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                        }
                    } else {
                        $department_level[1] = $department_list[$ancestry_arr_1]['name'] ?? '-';
                        if($department_list[$ancestry_arr_2]['level'] == 4) {
                            $department_level[6] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                        } else if($department_list[$ancestry_arr_2]['level'] == 3) {
                            $department_level[5] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                        } else if($department_list[$ancestry_arr_2]['level'] == 2) {
                            $department_level[4] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                            $department_level[5] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                        }  else if($department_list[$ancestry_arr_2]['level'] == 1) {
                            $department_level[3] = $department_list[$ancestry_arr_2]['name'] ?? '-';
                            $department_level[4] = $department_list[$ancestry_arr_3]['name'] ?? '-';
                            $department_level[5] = $department_list[$ancestry_arr_4]['name'] ?? '-';
                            $department_level[6] = $department_list[$ancestry_arr_5]['name'] ?? '-';
                        }
                    }

                    break;
                case 4: //组织
                    $department_level = [
                        0 => $department_list[$ancestry_arr_0]['name'] ?? '-',
                        1 => $department_list[$ancestry_arr_1]['name'] ?? '-',
                    ];
                    break;
                case 5:
                    $department_level = [
                        0 => $department_list[$department_id]['name'] ?? '-',
                    ];
                    break;
            }
        }

        return $department_level;
    }


}
