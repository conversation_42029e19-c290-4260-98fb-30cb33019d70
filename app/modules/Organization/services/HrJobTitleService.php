<?php

namespace App\Modules\Organization\Services;

use App\Modules\Organization\Models\HrJobTitleModel;

class HrJobTitleService extends BaseService
{
    public function getHrJobTitleList() {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,status,job_name');
        $builder->from(HrJobTitleModel::class);
        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list,null,'id');
    }

    /**
     * 根据部门ID组批量获取部门信息
     * @param array $jobIds 部门ID组
     * @return array
     */
    public function getJobInfoByIds(array $jobIds)
    {
        if (!$jobIds) {
            return [];
        }
        $list = HrJobTitleModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids'=>$jobIds]
        ]);
        return !empty($list) ? $list->toArray() : [];
    }
}