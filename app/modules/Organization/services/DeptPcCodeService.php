<?php

namespace App\Modules\Organization\Services;

use App\Library\Enums\GlobalEnums;
use App\Models\backyard\FranchiseeManagePieceModel;
use App\Models\backyard\FranchiseeManageRegionModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Modules\Organization\Models\DeptPcCodeModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use app\modules\Organization\models\SysDeptOperateLogsModel;
use Exception;

class DeptPcCodeService extends BaseService
{
    /**
     * 保存pc code
     * @param $param
     * @return bool
     */
    public function savePcCode($param): bool
    {
        try {
            $detail = DeptPcCodeModel::findFirst([
                'conditions' => "department_id = :department_id:",
                'bind'       => [
                    'department_id' => $param['id'],
                ],
            ]);

            if (!$detail) {
                $dept_pc_code = new DeptPcCodeModel();
                $create_param = [
                    'department_id'       => $param['id'],
                    'department_name'     => $param['name'],
                    'pc_code'             => $param['sap_cost_center'],
                    'kingdee_cost_center' => $param['kingdee_cost_center'],
                ];
                $result       = $dept_pc_code->i_create($create_param);
                if ($result) {
                    $this->logger->info('创建PcCode成功：' . json_encode($param, JSON_UNESCAPED_UNICODE));
                    return true;
                } else {
                    $this->logger->info('创建PcCode失败：' . json_encode($param, JSON_UNESCAPED_UNICODE));
                    return false;
                }
            } else {
                $update_param = [
                    'department_name'     => $param['name'],
                    'pc_code'             => $param['sap_cost_center'],
                    'kingdee_cost_center' => $param['kingdee_cost_center'],
                ];
                $result       = $detail->i_update($update_param);

                if ($result) {
                    $this->logger->info('更新PcCode成功：' . json_encode($param, JSON_UNESCAPED_UNICODE));
                    return true;
                } else {
                    $this->logger->info('更新PcCode失败：' . json_encode($param, JSON_UNESCAPED_UNICODE));
                    return false;
                }
            }
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 保存操作记录
     * @param $operater
     * @param $operater_name
     * @param $before
     * @param $after
     * @param $dept_id
     * @return bool|mixed
     */
    public function saveDeptOperateLog($operater, $operater_name, $before, $after, $business_id, $type= SysDeptOperateLogsModel::TYPE_DEPT)
    {
        try {
            $log                = new SysDeptOperateLogsModel();
            $log->operater      = (int)$operater;
            $log->operater_name = $operater_name;
            $log->type          = $type;
            $log->before        = json_encode($before);
            $log->after         = json_encode($after);
            $log->business_id   = $business_id;
            $result             = $log->save();

            $p = [
                'operater'      => $operater,
                'operater_name' => $operater_name,
                'before'        => $before,
                'after'         => $after,
                'dept_id'       => $business_id,
            ];
            if ($result === false) {
                $this->logger->error([
                    'function'   => 'saveDeptOperateLog',
                    'params'     => $p,
                    'result'     => $result,
                    'save_error' => $log->getMessages(),
                ]);
            } else {
                $this->logger->info([
                    'message' => 'Dept操作记录',
                    'params'  => $p
                ]);
            }
            return $result;
        } catch (\Exception $e) {
            $this->logger->error([
                'function' => 'saveDeptOperateLog',
                'message'  => $e->getMessage(),
                'line'     => $e->getLine(),
                'file'     => $e->getFile(),
                'trace'    => $e->getTraceAsString(),
            ]);
            return false;
        }
    }

    /**
     * 操作记录列表
     * @param array $where
     * @return array
     */
    public function getOperateLogList(array $where = []): array
    {
        $business_id = $where['id'] ?? '';
        $start_time  = $where['start_time'] ?? '';
        $end_time    = $where['end_time'] ?? '';
        $type        = $where['type'] ?? 'dept';

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(SysDeptOperateLogsModel::class);
        $builder->where('type = :type:', ['type' => $type]);
        if (!empty($business_id)) {
            $builder->andWhere('business_id = :business_id:', ['business_id' => $business_id]);
        }

        if (!empty($start_time)) {
            $builder->andWhere('created_at >= :start_time:', ['start_time' => $start_time]);
        }

        if (!empty($end_time)) {
            $builder->andWhere('created_at <= :end_time:', ['end_time' => $end_time]);
        }
        $builder->orderby('created_at desc');
        $list = $builder->getQuery()->execute()->toArray();

        $business_ids = array_column($list, 'business_id');

        $business_list  = [];
        if(!empty($business_ids)) {
            switch ($type) {
                case 'dept':
                    $business_list = SysDepartmentModel::find([
                        "conditions" => "deleted = :deleted: and id in({ids:array})",
                        'bind' => [
                            "ids"     => $business_ids,
                            'deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ])->toArray();
                    $business_list = array_column($business_list, null, 'id');
                    break;
                case 'region':
                    $business_list = SysManageRegionModel::find([
                        "conditions" => "deleted = :deleted: and id in({ids:array})",
                        'bind' => [
                            "ids"     => $business_ids,
                            'deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ])->toArray();
                    $business_list = array_column($business_list, null, 'id');
                    break;
                case 'piece':
                    $business_list = SysManagePieceModel::find([
                        "conditions" => "deleted = :deleted: and id in({ids:array})",
                        'bind' => [
                            "ids"     => $business_ids,
                            'deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ])->toArray();
                    $business_list = array_column($business_list, null, 'id');
                    break;
                case 'store':
                    $business_list = SysStoreModel::find([
                        "conditions" => "id in({ids:array}) and state = :state:",
                        'bind'       => [
                            "ids"   => $business_ids,
                            'state' => SysStoreModel::STATE_ACTIVATION_YES,
                        ],
                    ])->toArray();
                    $business_list = array_column($business_list, null, 'id');
                    break;
                case 'franchisee_region':
                    $business_list = FranchiseeManageRegionModel::find([
                        "conditions" => "id in({ids:array}) and deleted = :deleted:",
                        'bind'       => [
                            "ids"   => $business_ids,
                            'deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ])->toArray();
                    break;
                case 'franchisee_piece':
                    $business_list = FranchiseeManagePieceModel::find([
                        "conditions" => "id in({ids:array}) and deleted = :deleted:",
                        'bind'       => [
                            "ids"   => $business_ids,
                            'deleted' => GlobalEnums::IS_NO_DELETED,
                        ],
                    ])->toArray();
                    break;
            }
        }

        $dept_translations = [
            'name'                     => static::$t->_('department.name'),
            'ancestry'                 => static::$t->_('department.parent_name'),
            'level'                    => static::$t->_('department.level'),
            'manager_id'               => static::$t->_('department.manager_id'),
            'manager_name'             => static::$t->_('department.manager'),
            'assistant_id'             => 'Assitant ID',
            'assistant_name'           => 'Assitant Name',
            'sap_company_id'           => static::$t->_('department.sap_company_id'),
            'sap_reporting_currency'   => static::$t->_('department.sap_reporting_currency'),
            'sap_describe'             => static::$t->_('department.sap_describe'),
            'sap_cost_center'          => static::$t->_('department.sap_cost_center'),
            'sap_company_address'      => static::$t->_('department.sap_company_address'),
            'sap_tax_id'               => static::$t->_('department.sap_tax_id'),
            'change'                   => static::$t->_('department.change'),
            'creation'                 => static::$t->_('department.creation'),
            'relation_department_name' => static::$t->_('relation_department_name'),
            'relation_region_name'     => static::$t->_('relation_region_name'),
            'relation_piece_name'      => static::$t->_('relation_piece_name'),
            'manager_position_state'   => static::$t->_('manager_position_state'),
            'kingdee_cost_center'      => static::$t->_('kingdee_cost_center'),
            'assistant_manager_id'     => static::$t->_('assistant_manager_id'),
            'assistant_manager_name'   => static::$t->_('assistant_manager_name'),
        ];

        $changes = [];
        foreach ($list as $key => $value) {
            $before     = json_decode($value['before'], true);
            $after      = json_decode($value['after'], true);
            $created_at = show_time_zone($value['created_at'], 'Y-m-d H:i:s');
            $operate   = $value['operater_name'] . '(' . $value['operater'] . ')';
            $dept_id   = $value['business_id'];
            $business_name = $business_list[$value['business_id']]['name'] ?? '';

            array_filter($after, function ($afval, $afkey) use (
                $before,
                &$changes,
                $created_at,
                $operate,
                $dept_id,
                $business_name,
                $dept_translations
            ) {
                if (in_array($afkey, [
                    'id',
                    'ancestry_v2',
                    'ancestry_v3',
                    'type',
                    'phone',
                    'manager_phone',
                    'country_code',
                    'province_code',
                    'city_code',
                    'group_boss_id',
                    'district_code',
                    'detail_address',
                    'position',
                    'company_id',
                    'company_name',
                    'created_at',
                    'updated_at',
                    'relation_department_id',
                    'relation_region_id',
                    'relation_piece_id',
                    'relation_state'
                ])) {
                    return false;
                }

                if (empty($before[$afkey]) && empty($afval)) {
                    return false;
                }

                if ($afkey == 'deleted' && $afval == 1) {
                    $before[$afkey] = ' ';
                    $afval          = '';
                }

                if (!isset($before[$afkey])) {
                    $changes[] = [
                        'dept_id'        => $dept_id,
                        'dept_name'      => $business_name,
                        'operate_type'   => $dept_translations['creation'] ?? 'creation',
                        'change_content' => $dept_translations[$afkey] ?? $afkey,
                        'before'         => '',
                        'after'          => $afval,
                        'created_at'     => $created_at,
                        'operate'        => $operate,
                    ];
                    return false;
                }

                if ($before[$afkey] != $afval) {
                    $changes[] = [
                        'dept_id'        => $dept_id,
                        'dept_name'      => $business_name,
                        'operate_type'   => $dept_translations['change'] ?? 'change',
                        'change_content' => $dept_translations[$afkey] ?? $afkey,
                        'before'         => $before[$afkey],
                        'after'          => $afval,
                        'created_at'     => $created_at,
                        'operate'        => $operate,
                    ];
                }
            }, ARRAY_FILTER_USE_BOTH);
        }

        return $changes;
    }
}