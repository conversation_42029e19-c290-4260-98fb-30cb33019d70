<?php

namespace App\Modules\Organization\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RestClient;
use App\Modules\Organization\Models\HrOrganizationDepartmentStoreRelationModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use app\modules\Organization\models\SysDeptOperateLogsModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Repository\HrStaffRepository;
use App\Models\backyard\SettingEnvModel;
use App\Models\fle\SysStoreModel as FleSysStoreModel;

class StoreService extends BaseService
{
    public $department_store_category_th = [
        1 => 32,
        2 => 32, //???
        4 => 51,
        5 => 51,
        6 => 18,
        7 => 51,
        8 => 25,
        9 => 25,
        10 => 483,
        11 => 243,
        12 => 65,
        13 => 483,
    ];

    public $department_store_category_my = [
        1 => 320,
        2 => 320,
        4 => 350,
        5 => 350,
        7 => 350,
        8 => 328,
        9 => 328,
        10 => 320,
    ];

    public $department_store_category_ph = [
        1 => 18,
        2 => 18,
        4 => 5052,
        5 => 5052,
        7 => 5052,
        8 => 5110,
        9 => 5110,
        10 => 18,
    ];

    //19609【组织架构/员工管理】NW员工上级根据组织自动更新 负责人 参与 正职代理的 网点类型
    public static $store_category_manager_list = [Enums::STORE_CATEGORY_SP, Enums::STORE_CATEGORY_DC, Enums::STORE_CATEGORY_BDC, Enums::STORE_CATEGORY_PDC, Enums::STORE_CATEGORY_CDC];

    //hub 部门 category = 8
    //network operations 根据片区id
    //shop project 根据大区id
    public function getStoreList($params, $staff_info_id)
    {
        $department_id = $params['department_id'] ?? '';
        $region_id     = $params['region_id'] ?? '';
        $piece_id      = $params['piece_id'] ?? '';

        $builder = $this->modelsManager->createBuilder();

        $builder->columns('
            store_relation.department_id,
            store_relation.region_id,
            store_relation.piece_id,
            store_relation.store_id,
            store.id,
            store.name as label,
            store.manage_region,
            store.category,
            store.manage_piece,
            store.manager_id,
            store.manager_position_state
        ');

        $builder->from(['store_relation' => HrOrganizationDepartmentStoreRelationModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'store_relation.store_id = store.id', 'store');
        $builder->where('store_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('store_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('store_relation.level_state = :level_state:', ['level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL]);
        $builder->andWhere('store_relation.region_id = :region_id:', ['region_id' => $region_id]);
        $builder->andWhere('store_relation.piece_id = :piece_id:', ['piece_id' => $piece_id]);
        $builder->andWhere('store_relation.department_id = :department_id:', ['department_id' => $department_id]);
        $scope_store_ids = [];
        $department_service = new DepartmentService();
        $result = $department_service->validateDepartmentManager($department_id, $staff_info_id);
        if(!$result) {
            //查看我管辖的网点
            $scope_store_ids = DepartmentRelationService::getInstance()->getStaffStoreScopeList($staff_info_id);

            if ($scope_store_ids == OrganizationDepartmentEnums::NO_AUTH) {
                return [];
            }

            if ($scope_store_ids != OrganizationDepartmentEnums::ALL_AUTH){
                $builder->inWhere('store_relation.store_id', $scope_store_ids);
            }
        }
        $areas_range_list = DepartmentRelationService::getInstance()->getDepartmentJurisdiction($staff_info_id);

        if (!empty($areas_range_list['store_categories'])) {
            $category_rel_ids = DepartmentRelationService::getInstance()->getInfoIdsByCategory($areas_range_list['store_categories'], 'store_id');
            $areas_range_list['stores'] = array_merge($areas_range_list['stores'], $category_rel_ids);
        }

        $list = $builder->getQuery()->execute()->toArray();

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);


        $store_ids        = array_column($list, 'id');
        $store_work_count = $this->getStroeWorkCount($store_ids);
        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($list, 'manager_id');
        $staffs         = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        $country_code = get_country_code();

        foreach ($list as $key => $value) {
            $list[$key]['staff_info_id']                  = $value['manager_id'] ?? '';                                 //负责人工号
            $list[$key]['staff_info_name']                = $staffs[$value['manager_id']]['name'] ?? '';                //负责人姓名
            $list[$key]['job_title']                      = $staffs[$value['manager_id']]['job_title_name'] ?? '';      //职位
            $list[$key]['work_count']                     = $store_work_count[$value['id']]['count'] ?? 0;
            $list[$key]['next_type']                      = 0;
            $list[$key]['hasChildren']                    = false;
            $list[$key]['current_type']                   = 5;
            $list[$key]['is_show_manager_position_state'] = OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;

            if (in_array($country_code, DepartmentService::$nw_manager_country_list)) {
                $list[$key]['is_show_manager_position_state'] = in_array($value['category'], self::$store_category_manager_list) ? OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_YES : OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;
            }

            $list[$key]['budget_sum']                     = '';
            $list[$key]['budget_subordinates_sum']        = '';

            if(!empty(array_intersect($organization_edit_info_roles_id_arr, $staff_position))) {
                if(!in_array(OrganizationDepartmentEnums::ALL_STORE, $areas_range_list['stores'])) {
                    $is_edit = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_NO;
                    //配置管辖大区：那么员工可编辑管辖范围里的大区及其下片区、网点
                    //配置管辖片区，那么员工可编辑管辖范围里的片区及其下网点
                    //配置管辖网点：那么员工可编辑管辖范围里的网点
                    //配置管辖网点类型：那么员工可编辑管辖范围里该类型的所有网点
                    if (in_array($value['region_id'], $areas_range_list['regions']) ||
                        in_array($value['piece_id'], $areas_range_list['pieces']) ||
                        in_array($value['store_id'], $areas_range_list['stores'])) {
                        $is_edit = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
                    }
                    $list[$key]['is_edit_organization_info'] = $is_edit;
                } else {
                    $list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
                }
            } else {
                $list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
            }
        }

        return $list;
    }

    public function getStoreList_PH($department_id,$manage_region_id,$manage_piece_id) {
        //SysStore
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name as label,manager_id,category,manage_region,manage_piece,manager_position_state');
        $builder->from(SysStoreModel::class);
        $builder->where('state = 1');

        if(in_array($department_id, self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH)) { //Network Operations (NW-PH) 部门（需要展示大区片区）：SP[1]、DC[2]、BDC[10]
            $builder->andWhere('category in (1, 2, 10)');
        }

        if($department_id == 123) {
            $builder->andWhere('category in (4, 5, 7)');
        }

        if($department_id == 5110) { //Flash Philippines Hub部门126：Hub[8] 、OS[9]
            $builder->andWhere('category in (8, 9)');
        }

        if($department_id == 26) { //Philippine Fulfillment部门26：Fulfillment[11]
            $builder->andWhere('category = 11');
        }

        if($department_id == 124) { //Flash Home Philippines部门124：FH[6]
            $builder->andWhere('category = 6');
        }

        if($manage_region_id > 0) {
            $builder->andWhere('manage_region = :manage_region:',['manage_region' => $manage_region_id]);
        }

        if($manage_piece_id > 0) {
            $builder->andWhere('manage_piece = :manage_piece:',['manage_piece' => $manage_piece_id]);
        }

        $list = $builder->getQuery()->execute()->toArray();

        $store_ids = array_column($list,'id');
        $store_work_count = $this->getStroeWorkCount($store_ids);
        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($list,'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        foreach ($list as $key => $value) {
            $list[$key]['staff_info_id'] = $value['manager_id'] ?? '';//负责人工号
            $list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';//负责人姓名
            $list[$key]['job_title'] = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $list[$key]['work_count'] = $store_work_count[$value['id']]['count'] ?? 0;
            $list[$key]['next_type'] = 0;
            $list[$key]['hasChildren'] = false;
            $list[$key]['current_type'] = 5;
            $list[$key]['is_show_manager_position_state'] = in_array($value['category'],[1, 2, 10]) ? 1 : 0;
	        $list[$key]['budget_sum'] = '';
	        $list[$key]['budget_subordinates_sum'] = '';
        }
        return $list;
    }

    public function getStoreList_MY($department_id,$manage_region_id,$manage_piece_id) {
        //SysStore
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name as label,manager_id,category,manage_region,manage_piece,manager_position_state');
        $builder->from(SysStoreModel::class);
        $builder->where('state = 1');

        if(in_array($department_id, self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY)) { //Network Operations (NW-PH) 部门（需要展示大区片区）：SP[1]、DC[2]、BDC[10]
            $builder->andWhere('category in (1, 2, 10)');
        }

        if($department_id == 350) {
            $builder->andWhere('category in (4, 5, 7)');
        }

        if($department_id == 328) { //Flash Philippines Hub部门126：Hub[8] 、OS[9]
            $builder->andWhere('category in (8, 9)');
        }

        if($manage_region_id > 0) {
            $builder->andWhere('manage_region = :manage_region:',['manage_region' => $manage_region_id]);
        }

        if($manage_piece_id > 0) {
            $builder->andWhere('manage_piece = :manage_piece:',['manage_piece' => $manage_piece_id]);
        }

        $list = $builder->getQuery()->execute()->toArray();

        $store_ids = array_column($list,'id');
        $store_work_count = $this->getStroeWorkCount($store_ids);
        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($list,'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        foreach ($list as $key => $value) {
            $list[$key]['staff_info_id'] = $value['manager_id'] ?? '';//负责人工号
            $list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';//负责人姓名
            $list[$key]['job_title'] = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $list[$key]['work_count'] = $store_work_count[$value['id']]['count'] ?? 0;
            $list[$key]['next_type'] = 0;
            $list[$key]['hasChildren'] = false;
            $list[$key]['current_type'] = 5;
            $list[$key]['is_show_manager_position_state'] = in_array($value['category'],[1, 2, 10]) ? 1 : 0;
	        $list[$key]['budget_sum'] = '';
	        $list[$key]['budget_subordinates_sum'] = '';
        }
        return $list;
    }

    //获取网点在职人数
    public function getStroeWorkCount($store_ids) {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('sys_store_id,count(1) as count');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('formal in (1,4) and state=1');
        $builder->andWhere('working_country = :country:', ['country' => (new \App\Models\backyard\HrStaffInfoModel)->getWorkingCountryCode()]);
        $builder->inWhere('sys_store_id',$store_ids);
        $builder->groupby('sys_store_id');
        $list = $builder->getQuery()->execute()->toArray();

        return array_column($list,null,'sys_store_id');
    }

    public function getStoreDetail($store_id) {
        $detail = SysStoreModel::findFirst([
            'conditions' => 'id = :store_id: and state = 1',
            'bind' => [
                'store_id' => $store_id,
            ]
        ]);

        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 网点详情 fle
     * @param $store_id
     * @return array
     */
    public function getStoreDetailByFle($store_id)
    {
        $detail = FleSysStoreModel::findFirst([
            'conditions' => 'id = :store_id: and state = 1',
            'bind'       => [
                'store_id' => $store_id,
            ],
        ]);

        return !empty($detail) ? $detail->toArray() : [];
    }

    public function sync_store_manager_ms($param)
    {
        $store_id               = $param['id'];
        $manager_id             = $param['manager_id'];
        $manager_position_state = $param['manager_position_state'] ?? null;
        $operator_id            = $param['operator_id'];
        if ($operator_id == -1) {
            $operator_id = 10001;
        }
        $params = [
            'id'                     => (string)$store_id,
            'manager_id'             => $manager_id,
            'operator_id'            => $operator_id,
            'manager_position_state' => $manager_position_state,
        ];

        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/region/staff/update/store_manager',
            $params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] = true;
            return $return;
        }
        $this->logger->error('sync_store_manager_ms,操作内容' . json_encode($params) . ',接口返回：' . json_encode($return));
        $return['error'] = $return['message'] ?? static::$t->_('retry_later');
        return $return;
    }

    public function updateStorePositionState($param) {
        $store_id = $param['store_id'] ?? ''; //网点id
        $manager_id = $param['manager_id'] ?? ''; //负责人id
        $user = $param['user'];//登录用户信息
        $conditions = 'category in ({category_ids:array}) and state = 1 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'manager_id'=> $manager_id,
            'category_ids' => self::$store_category_manager_list
        ];
        if(!empty($store_id)) {
            $conditions = 'category in ({category_ids:array}) and state = 1 and manager_position_state = 1 and manager_id = :manager_id: and id != :store_id:';
            $bind = [
                'manager_id' => $manager_id,
                'store_id' => $store_id,
                'category_ids' => self::$store_category_manager_list
            ];
        }

        $store_list = SysStoreModel::find([
            'conditions' => $conditions,
            'bind' => $bind
        ]);
        $store_list = !empty($store_list) ? $store_list->toArray() : [];
        foreach ($store_list as $key => $value) {
            $sync_param = [
                'id' => $value['id'],
                'manager_id' => $manager_id,
                'operator_id' => $user['id'],
                'manager_position_state' => 2
            ];
            $result = $this->sync_store_manager_ms($sync_param);
            $this->getDI()->get('logger')->info('updateStorePositionState，操作人工号:' . $user['id'] . ',操作内容' . json_encode($sync_param) . ',接口返回：' . json_encode($result));
        }
        return true;
    }

    /**
     * 模糊搜索网点列表
     * @param $params
     * @return array
     */
    public function search_store_list($params , $staff_info_id = 0) {
        $search_name = $params['search_name'] ?? '';
        $page_size   = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num    = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset      = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();

        $builder->columns('COUNT(store_relation.id) AS count');
        $builder->from(['store_relation' => HrOrganizationDepartmentStoreRelationModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'store_relation.store_id = store.id', 'store');
        $builder->where('store.state = :store_state:', ['store_state' => OrganizationDepartmentEnums::STATE_NORMAL]);

        if(!empty($search_name)) {
            $builder->andWhere('store.name LIKE :search_name:',['search_name' => '%' . $search_name .'%']);
        }

        $store_ids = DepartmentRelationService::getInstance()->getStaffStoreScopeList($staff_info_id);

        $scope_list = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);

        if (!empty($scope_list['store_categories'])) {
            $category_rel_ids = DepartmentRelationService::getInstance()->getInfoIdsByCategory($scope_list['store_categories'], 'store_id');
            $scope_list['stores'] = array_merge($scope_list['stores'], $category_rel_ids);
        }

        if ($store_ids == OrganizationDepartmentEnums::NO_AUTH) {
            $builder->andWhere('store_relation.store_id = :store_ids:', ['store_ids' => OrganizationDepartmentEnums::NO_STORE]);
        } elseif (!empty($store_ids) && $store_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $builder->inWhere('store_relation.store_id', $store_ids);
        }

        $builder->andWhere('store_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('store_relation.level_state = :level_state:', ['level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL]);
        $builder->andWhere('store_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $count = $builder->getQuery()->getSingleResult();
        $count = intval($count['count'] ?? 0);

        $builder->columns('
            store.id,
            store.name,
            store.manager_id,
            store.manager_name,
            store.category,
            store.manage_region,
            store.manage_piece,
            store.manager_position_state,
            store_relation.department_id,
            store_relation.region_id,
            store_relation.piece_id
        ');

        $builder->orderBy('store.name ASC');
        $builder->limit($page_size, $offset);

        $store_list = $builder->getQuery()->execute()->toArray();

        $store_ids = array_column($store_list, 'id');
        $store_work_count = $this->getStroeWorkCount($store_ids);
        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($store_list, 'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        $region_piece_service = new SysManageRegionPieceService();
        $region_list = $region_piece_service->getRegionAllList();
        $region_list = array_column($region_list, null, 'id');
        $piece_list = $region_piece_service->getPieceAllList();
        $piece_list = array_column($piece_list, null, 'id');
        $department_service = new DepartmentService();
        $department_ids = array_column($store_list, 'department_id');
        $department_list = $department_service->getDepartmentInfoByIds($department_ids);
        $department_list = array_column($department_list, null, 'id');

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);

        $country_code = get_country_code();
        foreach ($store_list as $key => $value) {
            $store_list[$key]['staff_info_id'] = $value['manager_id'] ?? '';//负责人工号
            $store_list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';//负责人姓名
            $store_list[$key]['job_title'] = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位
            $store_list[$key]['work_count'] = $store_work_count[$value['id']]['count'] ?? 0;

            if(!empty($value['department_id'])) {
                $department = $department_list[$value['department_id']] ?? [];
                $store_list[$key]['department'] = !empty($department) ? $department['name'] : '';//所属部门
            } else {
                $store_list[$key]['department'] = '';

                $store_list[$key]['manage_piece_name'] = '';
            }

            if(!empty($value['region_id'])) {
                $manage_region = $region_list[$value['region_id']] ?? [];
                $store_list[$key]['manage_region_name'] = !empty($manage_region) ? $manage_region['name'] : '';
            } else {
                $store_list[$key]['manage_region_name'] = '';
            }

            if(!empty($value['piece_id'])) {
                $manage_piece = $piece_list[$value['piece_id']] ?? [];
                $store_list[$key]['manage_piece_name'] = !empty($manage_piece) ? $manage_piece['name'] : '';
            } else {
                $store_list[$key]['manage_piece_name'] = '';
            }

            $store_list[$key]['next_type'] = 0;
            $store_list[$key]['hasChildren'] = false;
            $store_list[$key]['current_type'] = 5;
            $store_list[$key]['is_show_manager_position_state'] = OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;
            if (in_array($country_code, DepartmentService::$nw_manager_country_list)) {
                $store_list[$key]['is_show_manager_position_state'] = in_array($value['category'], self::$store_category_manager_list) ? OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_YES : OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;
            }
            $store_list[$key]['budget_sum'] = '';
            $store_list[$key]['budget_subordinates_sum'] = '';
            if(!empty(array_intersect($organization_edit_info_roles_id_arr, $staff_position))) {
                if(!empty($scope_list)) {
                    if(!in_array(OrganizationDepartmentEnums::ALL_STORE, $store_ids['stores'])) {
                        $store_list[$key]['is_edit_organization_info'] = in_array($value['id'], $scope_list['stores']);
                    } else {
                        $store_list[$key]['is_edit_organization_info'] = true;
                    }
                } else {
                    $store_list[$key]['is_edit_organization_info'] = false;
                }
            } else {
                $store_list[$key]['is_edit_organization_info'] = true;
            }
        }

        return [
            'items' => $store_list,
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => $count
            ],
        ];
    }

    /**
     * 导出网点数据
     * @return array
     * @throws \App\Library\Exception\BusinessException
     */
    public function exportStore($staff_info_id = 0) {
        $builder = $this->modelsManager->createBuilder();

        $builder->columns('
            store.id,
            store.name,
            store.manager_id,
            store.manager_name,
            store.category,
            store.manage_region,
            store.manage_piece,
            store.manager_position_state,
            store_relation.department_id,
            store_relation.region_id,
            store_relation.piece_id
        ');

        $builder->from(['store_relation' => HrOrganizationDepartmentStoreRelationModel::class]);
        $builder->leftJoin(SysStoreModel::class, 'store_relation.store_id = store.id', 'store');
        $builder->where('store.state = :store_state:', ['store_state' => OrganizationDepartmentEnums::STATE_NORMAL]);

        $store_ids = DepartmentRelationService::getInstance()->getStaffStoreScopeList($staff_info_id);

        if ($store_ids == OrganizationDepartmentEnums::NO_AUTH) {
            $builder->andWhere('store_relation.store_id = :store_ids:', ['store_ids' => OrganizationDepartmentEnums::NO_STORE]);
        } elseif (!empty($store_ids) && $store_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $builder->inWhere('store_relation.store_id', $store_ids);
        }

        $builder->andWhere('store_relation.level_state = :level_state:', ['level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL]);
        $builder->andWhere('store_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('store_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $store_list = $builder->getQuery()->execute()->toArray();

        $header = [
            static::$t->_('export_store_id'),
            static::$t->_('export_store_name'),
            static::$t->_('export_store_manager_id'),
            static::$t->_('export_store_manager_name'),
            static::$t->_('person_in_charge_attribute'),
            static::$t->_('export_store_department_id'),
            static::$t->_('export_store_manage_region'),
            static::$t->_('export_store_manage_piece'),
        ];

        $country = get_country_code();
        if (!in_array($country, DepartmentService::$nw_manager_country_list)) {
            $header = [
                static::$t->_('export_store_id'),
                static::$t->_('export_store_name'),
                static::$t->_('export_store_manager_id'),
                static::$t->_('export_store_manager_name'),
                static::$t->_('export_store_department_id'),
                static::$t->_('export_store_manage_region'),
                static::$t->_('export_store_manage_piece'),
            ];
        }

        $region_piece_service = new SysManageRegionPieceService();
        $region_list = $region_piece_service->getRegionAllList();
        $region_list = array_column($region_list, null, 'id');
        $piece_list = $region_piece_service->getPieceAllList();
        $piece_list = array_column($piece_list, null, 'id');

        $department_ids = array_column($store_list, 'department_id');
        $department_list = (new DepartmentService())->getDepartmentInfoByIds($department_ids);
        $department_list = array_column($department_list, null, 'id');

        $file_name = 'export_store_list'.date('YmdHis');
        $data = [];
        foreach ($store_list as $key => $value) {
            $department_name        = isset($department_list[$value['department_id']]) ? $department_list[$value['department_id']]['name'] : '';
            $manage_region_name     = isset($region_list[$value['region_id']]) ? $region_list[$value['region_id']]['name'] : '';
            $manage_piece_name      = isset($piece_list[$value['piece_id']]) ? $piece_list[$value['piece_id']]['name'] : '';

            if (!in_array($country, DepartmentService::$nw_manager_country_list)) {
                $data[] = [
                    $value['id'],
                    $value['name'],
                    $value['manager_id'],
                    $value['manager_name'],
                    $department_name,
                    $manage_region_name,
                    $manage_piece_name,
                ];
            } else {
                $manager_position_state = '';
                if (in_array($value['manager_position_state'], [
                    OrganizationDepartmentEnums::MANAGER_POSITION_STATE_YES,
                    OrganizationDepartmentEnums::MANAGER_POSITION_STATE_NO,
                ])) {
                    $manager_position_state = static::$t->_('manager_position_state_' . $value['manager_position_state']);
                }

                $data[] = [
                    $value['id'],
                    $value['name'],
                    $value['manager_id'],
                    $value['manager_name'],
                    $manager_position_state,
                    $department_name,
                    $manage_region_name,
                    $manage_piece_name,
                ];
            }
        }

        return $this->exportExcel($header, $data, $file_name);
    }

    /**
     * 模糊搜索网点，网点类型
     * @param $params
     * @return array
     */
    public function search_store_category_list($params)
    {
        $search_name = $params['search_name'] ?? '';
        //先暂时注释
//        $department_id = $params['id'] ?? '';

//        //hub及其子部门
//        $departmentServer = new DepartmentService();
//        $hub_department_list = $departmentServer->getHubDepartmentList();
//        $hub_department_ids = array_column($hub_department_list, 'id');
//
//        //fulfillment及其子部门
//        $fulfillment_department_list = $departmentServer->getFulfillmentDepartmentList();
//        $fulfillment_department_ids = array_column($fulfillment_department_list, 'id');
//
//        if (in_array($department_id, $hub_department_ids)) {
//            $category = [8, 12];
//        } else if (in_array($department_id, $fulfillment_department_ids)) {
//            $category = [11];
//        } else {
//            return ['items' => []];
//        }

        $store_list = [];
        if (!empty($search_name)) {
            $store_list = SysStoreModel::find([
                'columns'    => 'id,name,manager_id,category,manage_region,manage_piece,manager_position_state',
                'conditions' => 'state = :state: and name like :store_name:',
                'bind'       => [
                    'store_name' => '%' . $search_name . "%",
                    'state'      => Enums::STORE_STATE_ACTIVE,
                ],
                'limit'      => 20,
                'order'      => 'name asc',

            ])->toArray();
        }
        return ['items' => $store_list];
    }

    /**
     * 更新网点负责人
     * @param $params
     * @return array|true
     * @throws BusinessException
     */
    public function updateStoreManager($params)
    {
        $user        = $params['user'];
        $id          = $params['id'];
        $manager_id  = $params['manager_id'];
        $confirm     = $params['confirm'] ?? OrganizationDepartmentEnums::CONFIRM_NO;
        $operator_id = $user['id']; //登陆人id

        $department_service = new DepartmentService();
        //验证工号是否正确
        $manager_id_error = $department_service->validateStaffInfoId($manager_id);
        if ($manager_id_error) {
            throw new BusinessException(static::$t->_($manager_id_error), ErrCode::$BUSINESS_ERROR);
        }

        $detail = $this->getStoreDetail($id);
        if (empty($detail)) {
            throw new BusinessException('store ID error', ErrCode::$BUSINESS_ERROR);
        }

        $manager_position_state = $params['manager_position_state'] ?? null;
        $before = [
            'manager_id'             => $detail['manager_id'] ?? 0,
            'manager_name'           => $detail['manager_name'] ?? '',
            'manager_position_state' => $detail['manager_position_state'] ?? null,
        ];

        $countryCode            = strtoupper(env('country_code', GlobalEnums::TH_COUNTRY_CODE));

        if (!empty($manager_id)
            && in_array($countryCode, DepartmentService::$nw_manager_country_list)
            && in_array($detail['category'], self::$store_category_manager_list)
            && $manager_position_state == OrganizationDepartmentEnums::MANAGER_POSITION_STATE_YES
            && $confirm != OrganizationDepartmentEnums::CONFIRM_YES)
        {
            //验证正职负责人是否是有多个
            $validate_result = (new DepartmentService())->validate_manager_position_state([
                'store_id'   => $id,
                'manager_id' => $manager_id,
            ]);
            if ($validate_result['code'] == 0) {
                $msg = static::$t->_('department_manager_position_msg') . "<br/>" . $validate_result['msg'];
                return ['code' => ErrCode::$SUCCESS, 'msg' => $msg, 'data' => ['confirm' => OrganizationDepartmentEnums::CONFIRM_YES]];
            }
        }

        $api_params = [
            'id'                     => (string)$id,
            'manager_id'             => $manager_id,
            'operator_id'            => $operator_id,
            'manager_position_state' => $manager_position_state,
        ];
        $return     = $this->sync_store_manager_ms($api_params);

        if (isset($return['error'])) {
            $this->logger->notice(['params' => $params, 'response' => $return, 'function' => 'updateStoreManager']);
            throw new BusinessException($return['error'], ErrCode::$BUSINESS_ERROR);
        }

        //同步直线上级变更 网点类型：1:SP;2:DC;10:BDC;14:PDC
        if (in_array($countryCode, DepartmentService::$nw_manager_country_list)
            && in_array($detail['category'], self::$store_category_manager_list)
        )
        {
            //变更
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'store_id'    => $id,
                    'manager_id'  => $manager_id,
                    'operator_id' => $operator_id,
                    'update_type' => OrganizationDepartmentEnums::SYNC_MS_UPDATE_TYPE_STORE,
                ],
            ]);
            $hris_ac->execute();
        }

        //如果设置当前组织为正职，则其他负责人都变更成代理
        if (in_array($countryCode, DepartmentService::$nw_manager_country_list)
            && in_array($detail['category'], self::$store_category_manager_list)
            && $manager_position_state == OrganizationDepartmentEnums::MANAGER_POSITION_STATE_YES)
        {
            $update_department_param = [
                'manager_id'    => $manager_id,
                'user'          => $user,
                'department_id' => '',
            ];
            (new DepartmentService())->updateOtherOrganizationManagerPositionState($update_department_param);
            $region_param = [
                'region_id'  => '',
                'manager_id' => $manager_id,
                'user'       => $user,
            ];
            (new SysManageRegionPieceService())->updateManagerRegionPositionState($region_param);
            $piece_param = [
                'piece_id'   => '',
                'manager_id' => $manager_id,
                'user'       => $user,
            ];
            (new SysManageRegionPieceService())->updateManagerPiecePositionState($piece_param);
            $store_param = [
                'store_id'   => $id,
                'manager_id' => $manager_id,
                'user'       => $user,
            ];
            $this->updateStorePositionState($store_param);
        }

        $stores = $this->getStoreDetailByFle($id);
        $after  = [
            'manager_id'             => $stores['manager_id'] ?? 0,
            'manager_name'           => $stores['manager_name'] ?? '',
            'manager_position_state' => $stores['manager_position_state'] ?? null,
        ];
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $before, $after, $id,
            SysDeptOperateLogsModel::TYPE_STORE);

        return true;
    }
}