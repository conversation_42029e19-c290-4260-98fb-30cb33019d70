<?php
/**
 * 职位体系-jd模块
 */

namespace App\Modules\Organization\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJdJobTitleRelationModel;
use App\Modules\Organization\Models\HrJdModel;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobGroupModel;
use App\Modules\Organization\Models\HrJobTitleByModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Library\Enums\StaffInfoEnums;
use App\Modules\Organization\Models\SysDepartmentByModel;
use App\Repository\HrJobTitleRepository;

class JobJdService extends BaseService
{
    public static $validate_jd_add_params_v1 = [                                //子职组
        'name'               => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'job_group_id'       => 'Required|IntGt:0|>>>:job_group_id error',      //父职组id
        'job_title_id'       => 'Required|Arr|ArrLenGe:1|>>>:job_title_id error',
    ];
    public static $validate_jd_edit_params_v1 = [                               //jd
        'id'                 => 'Required|IntGt:0|>>>:id error',                //jd ID
        'name'               => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'job_group_id'       => 'Required|IntGt:0|>>>:job_group_id error',      //父职组id
        'job_title_id'       => 'Required|Arr|ArrLenGe:1|>>>:job_title_id error',
    ];
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取JD选项数据
     * @return mixed
     */
    public function getJobJdOption()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('job_id as id,job_name as name,job_description as description');
        $builder->from(HrJdModel::class);
        $builder->andWhere('state=:state:', ['state' => 1]);
        $list = $builder->getQuery()->execute()->toArray();

        return $list ? $list : [];
    }

    /**
     * 模糊搜索jd
     * @param $params
     * @return array
     */
    public function searchJobJodList($params): array
    {
        $search_name = $params['search_name'] ?? '';
        $job_group_id = $params['job_group_id'] ?? 0;
        $page_size   = $params['page_size'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('job_id as id, job_name as name, job_description as description');
        $builder->from(HrJdModel::class);
        $builder->andWhere('state = :state:', ['state' => HrJdModel::STATE_DELETED_NO]);

        if (!empty($search_name)) {
            $builder->andWhere('job_name like :search_name: or job_id like :search_name:', [
                'search_name' => '%' . $search_name . '%',
            ]);
        }

        if(!empty($job_group_id)) {
            $builder->andWhere('job_group_id = :job_group_id:', ['job_group_id' => $job_group_id]);
        }

        $builder->orderBy('job_name ASC');
        $builder->limit($page_size);

        $list = $builder->getQuery()->execute()->toArray();

        $data['job_jd_list'] = $list;

        return $data;
    }

    /**
     * 搜索关联的 JD
     * @param $params
     * @return array
     */
    public function searchRelateJd($params): array
    {
        $jobTitleId = $params['job_title_id'];
        if (empty($jobTitleId)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('job_id');
        $builder->from(HrJdJobTitleRelationModel::class);
        $builder->andWhere('deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('position_id = :position_id:', [
            'position_id' => $jobTitleId,
        ]);
        $list = $builder->getQuery()->execute()->toArray();

        if (empty($list)) {
            return [];
        }

        $jdList = array_column($list, 'job_id');
        return HrJdModel::find([
            'conditions' => 'job_id in ({ids:array}) and state = 1',
            'bind' => [
                'ids' => $jdList,
            ],
            'columns' => 'job_id as value, job_name as label, job_description as description',
        ])->toArray();
    }

    /**
     * jd 列表分页
     * 可搜索
     * */
    public function getJobJdListOption(array $condition)
    {

        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['main' => HrJdModel::class]);
            //组合搜索条件
            if (!empty($condition['job_group_id'])) {
                $builder->andWhere('job_group_id = :job_group_id:', ['job_group_id' => $condition['job_group_id']]);
            }

            $builder->andWhere('state=:state:', ['state' => 1]);
            if (!empty($condition['name'])) {
                $builder->andWhere('(job_name LIKE :job_name:)', ['job_name' => "{$condition['name']}%"]);
            }

            if (!empty($condition['job_title_id'])) {
                $jobIdList = $this->getRelateJdByJobTitle($condition['job_title_id']);
                $jobIdList = array_column($jobIdList, 'job_id');

                if (!empty($jobIdList)) {
                    $builder->inWhere('job_id', $jobIdList);
                } else {
                    $builder->inWhere('job_id', [0]);
                }
            }

            $builder->columns('count(1) as cou');
            $result = $builder->getQuery()->getSingleResult();
            $count = $result->cou;

            //职组数量
            $jobGroupDetail = JobGroupService::getInstance()->getJobGroups();


            $jobGroupDetail = array_column($jobGroupDetail, 'name', 'id');

            if ($count <= 0) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'success',
                    'data'    => [
                        'items' => [],
                        'pagination' => [
                            'total_count' => $count,
                        ],
                    ],
                ];
            }

            if(!isset($condition['is_download']) || !$condition['is_download']) {
                $builder->limit($page_size, $offset);
            }
            $builder->columns([
                'job_id as id',
                'job_name as name',
                'job_description',
                'job_group_id',
                'job_group_child_id',
                'created_at',
                'updated_at',
                'created_uid',
                'updated_uid',
            ]);
            $items = $builder->getQuery()->execute()->toArray();

            if (empty($items)) {
                return [
                    'code'    => ErrCode::$SUCCESS,
                    'message' => 'success',
                    'data'    => [
                        'items' => [],
                        'pagination' => [
                            'total_count' => $count,
                        ],
                    ],
                ];
            }

            //jd_id
            $jd_id = array_column($items, 'id');

            $jd_count = $this->getJobNum($jd_id);

            //根据jd 查询职位id

            $job_id_res = $this->getJobByJd($jd_id);

            //根据职位id 查询在职
            if (isset($job_id_res['job_ids']) && $job_id_res['job_ids']) {
                $staff_info = $this->getStaffNums($job_id_res['job_ids']); //职位的在职人数-JD列表

                if (!empty($staff_info)) {
                    $staff_info = array_column($staff_info, 'num', 'job_title');
                }

                //jd对应职位下员工数
                $jd_job_num = [];
                foreach ($job_id_res['jd_ids'] as $k1 => $v1) {
                    foreach ($v1 as $k2 => $v2) {
                        $v1[$k2] = $staff_info[$v2] ?? '';
                    }

                    $jd_job_num[$k1] = array_sum($v1);
                }
            }

            //jd - job_title 关联关系
            $jdJobTitleRelationList = $this->getJobTitleRelateByJd($jd_id);
            $totalJobTitleId        = array_column($jdJobTitleRelationList, 'position_id');
            $jdJobTitleRelationList = array_group_by_column($jdJobTitleRelationList, 'job_id');
            $totalJobTitleList      = [];
            if (!empty($totalJobTitleId)) {
                $totalJobTitleList = (new HrJobTitleRepository())->getJobTitleByIds($totalJobTitleId);
            }

            foreach ($items as $key => &$value) {

                $relation = $jdJobTitleRelationList[$value['id']] ?? [];
                $relationJobTitleIds = !empty($relation) ? array_column($relation, 'position_id') : [];

                // 职组名称 职位数量 员工数量
                $value['job_group_id']         = isset($jobGroupDetail[$value['job_group_id']]) ? $value['job_group_id'] : null;//职组id
                $value['job_group_name']       = $jobGroupDetail[$value['job_group_id']] ?? '';//职组名称
                $value['job_group_child_id']   = isset($jobGroupDetail[$value['job_group_child_id']]) ? $value['job_group_child_id'] : null;//子职组id
                $value['job_group_child_name'] = $jobGroupDetail[$value['job_group_child_id']] ?? '';//子职组名称
                $value['job_count']            = $jd_count[$value['id']] ?? 0;
                $value['job_person_num']       = $jd_job_num[$value['id']] ?? 0;//员工数量
                $value['job_title_id']         = $relationJobTitleIds;
                $value['job_title_label']      = $this->formatJobTitle($relationJobTitleIds, $totalJobTitleList, $condition['is_download']);
                $value['updated_at'] =date('Y-m-d H:i:s', strtotime($value['updated_at']) + get_sys_time_offset() * 3600);
                $value['created_at'] =date('Y-m-d H:i:s', strtotime($value['created_at']) + get_sys_time_offset() * 3600);
            }

            $data['items'] = $items;

            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('jd列表异常信息:' . $real_message);
        }
        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR) {
            $code    = ErrCode::$SUCCESS;
            $message = $real_message;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     *新建jd
     *
     * */

    public function addJobJdOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $name_is_repeat = $this->checkNameIsRepeat($param);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('jd_name_exists'));
            }

            if ($this->checkIsJobTitleValid($param)) {
                throw new ValidationException(self::$t->_('exists_invalid_jd_title'));
            }

            $hrJdModel = new HrJdModel();
            $bool = $hrJdModel->save(['job_name'           => trim($param['name']),
                                      'job_description'    => $param['job_description'],
                                      'job_group_id'       => $param['job_group_id'],
                                      'job_group_child_id' => $param['job_group_child_id'] ?? 0,
                                      'created_uid'         => $param['uid'],
                                      'created_uname'      => $param['uname'],
                                      'updated_uid'        => $param['uid'],
                                      'updated_uname'      => $param['uname'],


            ]);
            if ($bool === false) {
                $messages = $hrJdModel->getMessages();
                throw new \Exception('jd创建失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }

            // 保存关联关系
            foreach ($param['job_title_id'] as $item) {
                $relationModel = new HrJdJobTitleRelationModel();
                $relationModel->job_id = $hrJdModel->job_id;
                $relationModel->position_id = $item;
                $relationModel->save();
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('jd创建异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 检查职组名称是否重复
     * @param $job_name
     * @return bool
     */
    private function checkNameIsRepeat($param, $id = 0)
    {

        if ($id) { //编辑操作
            $res = HrJdModel::findFirst([
                'job_name = :job_name: and job_id <> :job_id: and state = :state:',
                'bind' => ['job_name' => $param['name'], 'job_id' => $param['id'],'state'=>1],
            ]);

        } else { //新增操作
            $res = HrJdModel::findFirst([
                'job_name = :job_name: and state = :state:',
                'bind' => ['job_name' => $param['name'],'state'=>1],
            ]);
        }
        if ($res) {
            return true;
        }
        return false;
    }

    /**
     *编辑jd
     *
     *
     * */

    public function updateJobJdOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检验是否存在
            $hrJdModel = HrJdModel::findFirst([
                'job_id = :job_id:',
                'bind' => ['job_id' => $param['id']],
            ]);
            if (empty($hrJdModel)) {
                throw new \Exception('jd 不存在');
            }

            //检查名称重复
            $name_is_repeat = $this->checkNameIsRepeat($param, 1);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('jd_name_exists'));
            }

            if ($this->checkIsJobTitleValid($param)) {
                throw new ValidationException(self::$t->_('exists_invalid_jd_title'));
            }

            $bool = $hrJdModel->save(['job_name'           => trim($param['name']),
                                      'job_description'    => $param['job_description'],
                                      'job_group_id'       => $param['job_group_id'],
                                      'job_group_child_id' => (!empty($param['job_group_child_id'])) ? $param['job_group_child_id'] : 0,
                                      'updated_uid'        => $param['uid'],
                                      'updated_uname'      => $param['uname'],
                                      'updated_at'         => gmdate('Y-m-d H:i:s'),
            ]);
            if ($bool === false) {
                $messages = $hrJdModel->getMessages();
                throw new \Exception('jd 更新失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }

            $relations = HrJdJobTitleRelationModel::find([
                'conditions' => 'job_id = :job_id: and deleted = :deleted:',
                'bind' => ['job_id' => $hrJdModel->job_id, 'deleted' => GlobalEnums::IS_NO_DELETED],
            ]);
            $relations->update(['deleted' => GlobalEnums::IS_DELETED]);

            // 保存关联关系
            foreach ($param['job_title_id'] as $item) {
                $relationModel = new HrJdJobTitleRelationModel();
                $relationModel->job_id = $hrJdModel->job_id;
                $relationModel->position_id = $item;
                $relationModel->save();
            }

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('jd更新异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     *删除
     * */

    public function delJobJdOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //检验该jd是否存在
            $hrJdModel = HrJdModel::findFirst([
                'job_id = :job_id:',
                'bind' => ['job_id' => $param['id']],
            ]);
            if (empty($hrJdModel)) {
                throw new \Exception('jd 不存在');
            }
            $isHasJob = HrJobDepartmentRelationModel::findFirst([
                'jd_id = :jd_id:',
                'bind' => ['jd_id' => $param['id']],
            ]);

            if (!empty($isHasJob)) {
                throw new ValidationException(self::$t->_('not_del_jd'));
            }


            // 更新职位表
            $bool = $hrJdModel->save(['state' => 2]);
            if ($bool === false) {
                $messages = $hrJdModel->getMessages();
                throw new \Exception('jd 删除失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('jd删除异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];

    }

    /**
     * 根据jd id 查询职位数
     * */
    private function getJobNum($jd_id)
    {
        $res = $this->modelsManager->createBuilder()
            ->columns(['jd_id', 'count(*) as num'])
            ->from(['hjd' => HrJobDepartmentRelationModel::class])
            ->join(SysDepartmentByModel::class, 'sd.id = hjd.department_id', 'sd')
            ->inWhere('jd_id', $jd_id)
            ->andWhere('sd.deleted = 0')
            ->groupBy(['jd_id'])
            ->getQuery()->execute()->toArray();
        if (empty($res)) {
            return [];
        }
        $res = array_column($res, 'num', 'jd_id');

        return $res ?? [];
    }

    /**
     * 根据jd id 查询职位id
     * */
    private function getJobByJd($jd_id)
    {
        $job_res = $this->modelsManager->createBuilder()
            ->columns(['jd_id', 'job_id'])
            ->from(HrJobDepartmentRelationModel::class)
            ->inWhere('jd_id', $jd_id)
            ->getQuery()->execute()->toArray();
        if (empty($job_res)) {
            return [];
        }

        $res['job_ids'] = array_column($job_res, 'job_id');

        $i = 0;
        foreach ($job_res as $key => $value) {
            if ($i == 0) {
                $res['jd_ids'][$value['jd_id']][] = $value['job_id'];
            } else {
                if (!in_array($value['job_id'], $res['jd_ids'][$value['jd_id']] ?? [])) {
                    $res['jd_ids'][$value['jd_id']][] = $value['job_id'];
                }
            }
            $i++;
        }

        return $res ?? [];
    }

    /**
     * 获取指定职位的在职人数
     * @param $department_id
     * @param $job_id
     * @return mixed
     */
    public function getStaffNums(array $job_id)
    {
        //当前职位在职人数

        $res = $this->modelsManager->createBuilder()
            ->columns(['job_title', 'count(*) as num'])
            ->from(HrStaffInfoModel::class)//
            ->Where('formal IN ({formal:array})', ['formal' => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE ]])
            ->andWhere('state IN ({state:array})', ['state' => [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP]])
            ->andWhere('is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO])
            ->inWhere('job_title', $job_id)
            ->andWhere('working_country = :country:', ['country' => (new \App\Models\backyard\HrStaffInfoModel)->getWorkingCountryCode()])
            ->groupBy(['job_title'])
            ->getQuery()->execute()->toArray();

        if (empty($res)) {
            return [];
        }
        return $res;
    }


    /**
     * 导出JD列表
     * @param $params
     * @return array
     */
    function exportJobjdlist($params, $locale){
        try {
            $data = $this->getJobJdListOption($params);

            //越南语表头
            if($locale==Enums::VN_LOCALE) {
                $header = [
                    Enums::VI_EXPORT_JD_ID,                     //JDID
                    Enums::VI_EXPORT_JD_NAME,                   //JD名称
                    Enums::VI_EXPORT_BELONG_JOB_GEROUP,         //所属职组
                    Enums::VI_EXPORT_BELONG_CHILD_JOB_GEROUP,   //所属子职组
                    Enums::VI_EXPORT_RELATE_JOB_TITLE,          //关联职位
                    Enums::VI_EXPORT_JOB_TITLE_MANAGEMENT_COUNT,//职位管理数量
                    Enums::VI_EXPORT_STAFF_COUNT,               //员工数量
                    Enums::VI_EXPORT_JD_DESCRIPTION,            //JD描述
                ];
            }else {
                $header = [
                    static::$t->_('jd_id'),                        //JDID
                    static::$t->_('jd_name'),                      //JD名称
                    static::$t->_('belong_job_group'),             //所属职组
                    static::$t->_('belong_child_job_group'),       //所属子职组
                    static::$t->_('relate_job_title'),             //关联职位
                    static::$t->_('job_title_management_count'),   //职位管理数量
                    static::$t->_('staff_count'),                  //员工数量
                    static::$t->_('jd_description'),               //JD描述
                ];
            }

            $new_data = [];
            if (!empty($data['data']['items'])) {
                foreach ($data['data']['items'] as $key => $val) {
                    $new_data[] = [
                        $val['id'],
                        $val['name'],
                        $val['job_group_name'],
                        $val['job_group_child_name'],
                        $val['job_title_label'],
                        $val['job_count'],
                        $val['job_person_num'],
                        strip_tags($val['job_description']),
                    ];
                }
            }
            $file_name = "job_jd_" . date('Y-m-d H:i:s');
            $result = $this->exportExcel($header, $new_data, $file_name);
        }catch (\Exception $e){
            $message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('exportJobjdlist-failed:' . $message);
        }

        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }
        return $result;
    }

    private function getJobTitleRelateByJd(array $jd_id)
    {
        return HrJdJobTitleRelationModel::find([
            'conditions' => 'job_id in ({job_ids:array}) and deleted = 0',
            'bind' => [
                'job_ids' => $jd_id,
            ],
            'columns' => 'job_id,position_id',
        ])->toArray();
    }

    /**
     * 根据职位 ID 获取关联的 Jd
     * @param mixed $job_title
     * @return array
     */
    private function getRelateJdByJobTitle($job_title): array
    {
        if (empty($job_title)) {
            return [];
        }
        if (is_array($job_title)) {
            $jobTitleList = $job_title;
        } else {
            $jobTitleList = [$job_title];
        }
        return HrJdJobTitleRelationModel::find([
            'conditions' => 'position_id in ({position_ids:array}) and deleted = 0',
            'bind' => [
                'position_ids' => $jobTitleList,
            ],
            'columns' => 'job_id,position_id',
        ])->toArray();
    }

    /**
     * @param array $relationJobTitleIds
     * @param array $totalJobTitleList
     * @param $isDownload
     * @return string
     */
    private function formatJobTitle(array $relationJobTitleIds, array $totalJobTitleList, $isDownload): string
    {
        $list = array_map(function ($jobTitleId) use ($totalJobTitleList, $isDownload) {
            return isset($totalJobTitleList[$jobTitleId])
                ? $this->formatJobTitleLabel($jobTitleId, $totalJobTitleList[$jobTitleId]['job_name'], $isDownload)
                : '';
        } , $relationJobTitleIds);
        $list = array_values(array_unique($list));
        return join(',', $list);
    }

    private function formatJobTitleLabel($jobTitleId, $jobTitleName, $isDownload)
    {
        if ($isDownload) {
            return sprintf('(%s)%s ', $jobTitleId,  $jobTitleName);
        } else {
            return $jobTitleName;
        }
    }

    /**
     * 校验是否存在无效的职位
     * @param $param
     * @return bool
     */
    private function checkIsJobTitleValid($param): bool
    {
        if (empty($param['job_title_id'])) {
            return false;
        }
        $invalidJotTitle = HrJobTitleByModel::find([
            'conditions' => 'id in({ids:array}) and status = :status:',
            'bind' => [
                'ids' => $param['job_title_id'],
                'status' => HrJobTitleByModel::STATUS_NOT_VALID,
            ],
        ])->toArray();
        return !empty($invalidJotTitle);
    }

    /**
     * 获取 Jd 详情
     * @param $params
     * @return array
     */
    public function getJdDetail($params): array
    {
        if (empty($params['job_id'])) {
            return [];
        }

        $jobInfo = HrJdModel::findFirstByJobId($params['job_id']);
        if (empty($jobInfo)) {
            return [];
        }
        $jobInfo  = $jobInfo->toArray();
        $jobGroup = [$jobInfo['job_group_id'], $jobInfo['job_group_child_id']];

        $jobGroupList = HrJobGroupModel::find([
            'conditions' => 'id in ({job_group_ids:array})',
            'bind'       => [
                'job_group_ids' => $jobGroup,
            ],
            'columns'    => 'id,name',
        ])->toArray();
        $jobGroupList = array_column($jobGroupList, 'name', 'id');

        $jobInfo['job_group_name']       = $jobGroupList[$jobInfo['job_group_id']] ?? '';
        $jobInfo['job_group_child_name'] = $jobGroupList[$jobInfo['job_group_child_id']] ?? '';

        return $jobInfo;
    }
}