<?php

namespace app\modules\Organization\services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\Exception\BusinessException;
use App\Library\RestClient;
use App\Library\Validation\ValidationException;
use app\modules\Organization\models\StaffInfoJobTitleModel;
use app\modules\Organization\models\SysDepartmentFleModel;
use app\modules\Organization\models\SysStoreFleModel;
use App\Modules\Organization\Models\SysStoreModel;
use Exception;

class ThirdApiService extends BaseService
{
    /**
     * 创建组织
     * @param $params
     * @return array|bool|mixed|null
     * @throws ValidationException
     */
    public static function addSysGroupBoss($params)
    {
        if (env('break_away_from_ms')) {
            return self::addSysGroupBossSelf($params);
        } else {
            return self::addSysGroupBossMS($params);
        }
    }

    /**
     * @throws ValidationException
     */
    protected static function addSysGroupBossMS($params)
    {
        $api_params = [
            'name'                   => $params['name'] ?? '',
            //组织名称
            'manager_id'             => $params['manager_id'],
            //负责人ID
            'type'                   => OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE,
            //1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
            'level'                  => OrganizationDepartmentEnums::GROUP_BOSS_LEVEL,
            //层级关系level BU=0 公司级别(名称变化id没变) ，新增 Clevel=99 boss级别
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
            'relevance_store_id'     => strval($params['relevance_store_id'] ?? ''),
            'operator_id'            => intval($params['operator_id'] ?? 0),
        ];
        if ($params['assistant_id']) {
            $api_params['assistant_id'] = $params['assistant_id'];
        }

        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/department/add/group_boss',
            $api_params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] = $return['data'];
            return $return;
        }
        throw new ValidationException($return['message'] ?? self::$t->_('retry_later'), ErrCode::$BUSINESS_ERROR);

    }

    /**
     * 生成部门ID
     * @return void
     */
    private static function getSysDepartmentIdV2()
    {
        $departmentList = SysDepartmentFleModel::find(['columns' => 'id'])->toArray();
        $department_id  = max(max(array_map('intval', array_column($departmentList, 'id'))), 1);
        return $department_id + 1;
    }

    protected static function addSysGroupBossSelf($params): array
    {
        $sysDepartmentModel                         = new SysDepartmentFleModel();
        $staffService                               = new HrStaffInfoService();
        $managerInfo                                = $staffService->staffInfo($params['manager_id']);
        $sysDepartmentModel->id                     = self::getSysDepartmentIdV2();
        $sysDepartmentModel->name                   = $params['name'];
        $sysDepartmentModel->type                   = OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE;
        $sysDepartmentModel->level                  = OrganizationDepartmentEnums::GROUP_BOSS_LEVEL;
        $sysDepartmentModel->manager_id             = $params['manager_id'];
        $sysDepartmentModel->manager_name           = $managerInfo->name;
        $sysDepartmentModel->manager_phone          = $managerInfo->mobile;
        $sysDepartmentModel->sap_company_id         = $params['sap_company_id'];
        $sysDepartmentModel->sap_reporting_currency = $params['sap_reporting_currency'];
        $sysDepartmentModel->sap_describe           = $params['sap_describe'];
        $sysDepartmentModel->sap_company_address    = $params['sap_company_address'];
        $sysDepartmentModel->sap_cost_center        = $params['sap_cost_center'];
        $sysDepartmentModel->sap_tax_id             = $params['sap_tax_id'];
        $sysDepartmentModel->kingdee_cost_center    = $params['kingdee_cost_center'];
        $sysDepartmentModel->country_code           = $params['country_code'];
        $sysDepartmentModel->relevance_store_id     = $params['relevance_store_id'];
        $sysDepartmentModel->ancestry               = GlobalEnums::TOP_DEPARTMENT_ID;
        $sysDepartmentModel->ancestry_v3            = GlobalEnums::TOP_DEPARTMENT_ID . '/' . $sysDepartmentModel->id;
        $sysDepartmentModel->save();
        $result['result'] = $sysDepartmentModel->id;
        return $result;
    }


    /**
     * 编辑组织
     * @param $params
     * @return array|mixed
     * @throws ValidationException
     */
    public static function updateSysGroupBoss($params)
    {
        if (env('break_away_from_ms')) {
            return self::updateSysGroupBossSelf($params);
        } else {
            return self::updateSysGroupBossMS($params);
        }
    }

    /**
     * 编辑组织
     * @param $params
     * @return array
     * @throws ValidationException
     */
    protected static function updateSysGroupBossSelf($params): array
    {
        $sysDepartmentModel = SysDepartmentFleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['id']],
        ]);
        if (empty($sysDepartmentModel)) {
            throw new ValidationException(self::$t->_('department_not_exist'));
        }

        if ($params['edit_type'] == DepartmentService::$organization_edit_info) {
            $staffService = new HrStaffInfoService();
            $managerInfo  = $staffService->staffInfo($params['manager_id']);
            if (!empty($params['assistant_id'])) {
                $assistantInfo                      = $staffService->staffInfo($params['assistant_id']);
                $sysDepartmentModel->assistant_id   = $assistantInfo->staff_info_id;
                $sysDepartmentModel->assistant_name = $assistantInfo->name;
            }
            $sysDepartmentModel->manager_id    = $params['manager_id'];
            $sysDepartmentModel->manager_name  = $managerInfo->name;
            $sysDepartmentModel->manager_phone = $managerInfo->mobile;
        }

        $sysDepartmentModel->name                   = $params['name'];
        $sysDepartmentModel->sap_company_id         = $params['sap_company_id'];
        $sysDepartmentModel->sap_reporting_currency = $params['sap_reporting_currency'];
        $sysDepartmentModel->sap_describe           = $params['sap_describe'];
        $sysDepartmentModel->sap_company_address    = $params['sap_company_address'];
        $sysDepartmentModel->sap_cost_center        = $params['sap_cost_center'];
        $sysDepartmentModel->sap_tax_id             = $params['sap_tax_id'];
        $sysDepartmentModel->relevance_store_id     = $params['relevance_store_id'];
        $sysDepartmentModel->kingdee_cost_center    = $params['kingdee_cost_center'];
        $sysDepartmentModel->country_code           = $params['country_code'];
        $sysDepartmentModel->updated_at             = gmdate('Y-m-d H:i:s');
        $sysDepartmentModel->save();
        $result['result'] = true;
        return $result;
    }

    /**
     * 编辑组织
     * @throws ValidationException
     */
    protected static function updateSysGroupBossMS($params)
    {

        $api_params = [
            'id'                     => $params['id'],            //组织ID
            'name'                   => $params['name'] ?? '',    //组织名称
            'manager_id'             => $params['manager_id'],    //负责人ID
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
            'operator_id'            => intval($params['operator_id'] ?? 0),
        ];
        if ($params['assistant_id']) {
            $api_params['assistant_id'] = $params['assistant_id'];
        }
        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/department/update/group_boss',
            $api_params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] = true;
            return $return;
        }
        throw new ValidationException($return['message'] ?? self::$t->_('retry_later'), ErrCode::$BUSINESS_ERROR);
    }


    /**
     * @param $params
     * @return array|bool|mixed|null
     * @throws ValidationException
     */
    public static function addSysDepartmentV2($params)
    {
        if (env('break_away_from_ms')) {
            return self::addSysDepartmentV2Self($params);
        } else {
            return self::addSysDepartmentV2MS($params);
        }
    }

    protected static function addSysDepartmentV2Self($params): array
    {
        $sysDepartmentModel = new SysDepartmentFleModel();
        $staffService       = new HrStaffInfoService();
        $managerInfo        = $staffService->staffInfo($params['manager_id']);
        if (!empty($params['assistant_id'])) {
            $assistantInfo                      = $staffService->staffInfo($params['assistant_id']);
            $sysDepartmentModel->assistant_id   = $assistantInfo->staff_info_id;
            $sysDepartmentModel->assistant_name = $assistantInfo->name;
        }
        $sysDepartmentModel->id                     = self::getSysDepartmentIdV2();
        $sysDepartmentModel->name                   = $params['name'];
        $sysDepartmentModel->country_code           = $params['country_code'];
        $sysDepartmentModel->ancestry               = $params['ancestry'];
        $sysDepartmentModel->type                   = $params['type'];
        $sysDepartmentModel->level                  = $params['level'];
        $sysDepartmentModel->manager_id             = $params['manager_id'];
        $sysDepartmentModel->manager_name           = $managerInfo->name;
        $sysDepartmentModel->manager_phone          = $managerInfo->mobile;
        $sysDepartmentModel->group_boss_id          = $params['group_boss_id'];
        $sysDepartmentModel->sap_company_id         = $params['sap_company_id'];
        $sysDepartmentModel->sap_reporting_currency = $params['sap_reporting_currency'];
        $sysDepartmentModel->sap_describe           = $params['sap_describe'];
        $sysDepartmentModel->sap_company_address    = $params['sap_company_address'];
        $sysDepartmentModel->sap_cost_center        = $params['sap_cost_center'];
        $sysDepartmentModel->sap_tax_id             = $params['sap_tax_id'];
        $sysDepartmentModel->kingdee_cost_center    = $params['kingdee_cost_center'];
        $sysDepartmentModel->relevance_store_id     = $params['relevance_store_id'];

        if (!empty($params['ancestry'])) {
            $parentDepartment = SysDepartmentFleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['ancestry']],
            ]);

            $sysDepartmentModel->ancestry_v3 = $parentDepartment->ancestry_v3 . '/' . $sysDepartmentModel->id;
            if ($parentDepartment->type == 1) {
                $sysDepartmentModel->company_id   = $parentDepartment->id;
                $sysDepartmentModel->company_name = $parentDepartment->name;
            } else {
                $sysDepartmentModel->company_id   = $parentDepartment->company_id;
                $sysDepartmentModel->company_name = $parentDepartment->company_name;
            }
        } else {
            $sysDepartmentModel->ancestry_v3 = $sysDepartmentModel->id;
            $sysDepartmentModel->company_id  = $params['company_id'] ?? null;
        }
        //如果添加的是公司 则公司ID和公司名称就是自己的ID和自己的名称
        if ($params['type'] == 1) {
            $sysDepartmentModel->company_id   = $sysDepartmentModel->id;
            $sysDepartmentModel->company_name = $sysDepartmentModel->name;
        }
        $sysDepartmentModel->save();
        $result['result'] = $sysDepartmentModel->id;
        return $result;
    }

    /**
     * @throws ValidationException
     */
    protected static function addSysDepartmentV2MS($params)
    {
        $api_params = [
            'name'                   => (string)$params['department_name'],                   //部门名称
            'level'                  => (int)$params['level'],                                //部门级别
            'ancestry'               => (string)($params['ancestry']),                        //上级组织id
            'manager_id'             => (int)$params['manager_id'],                           //负责人工号
            'type'                   => (int)$params['type'],                                 //部门
            'group_boss_id'          => (string)$params['group_boss_id'],                     //所属组织ID
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'relevance_store_id'     => $params['relevance_store_id'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
            'operator_id'            => intval($params['operator_id'] ?? 0),


        ];
        if (!empty($params['assistant_id'])) {
            $api_params['assistant_id'] = $params['assistant_id'];
        }
        if (!empty($params['company_id'])) {
            $api_params['company_id'] = $params['company_id'];
        }
        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/department/add',
            $api_params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] =  $return['data'];
            return $return;
        }
        throw new ValidationException($return['message'] ?? self::$t->_('retry_later'), ErrCode::$BUSINESS_ERROR);
    }


    /**
     * @param $params
     * @return array|bool|null
     * @throws BusinessException|ValidationException
     */
    public static function updateSysDepartmentV2($params)
    {
        if (env('break_away_from_ms')) {
            return self::updateSysDepartmentV2Self($params);
        } else {
            return self::updateSysDepartmentV2MS($params);
        }
    }

    protected static function updateSysDepartmentV2Self($params): array
    {
        $sysDepartmentModel = SysDepartmentFleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $params['department_id']],
        ]);

        if (empty($sysDepartmentModel)) {
            throw new BusinessException('部门不存在');
        }

        if (!empty($params['ancestry'])) {
            $parentDepartment = SysDepartmentFleModel::findFirst([
                'conditions' => 'id = :id: and deleted = :deleted:',
                'bind'       => ['id' => $params['ancestry'], 'deleted' => 0],
            ]);
            if (empty($parentDepartment)) {
                throw new BusinessException('上级部门不存在');
            }
        }

        if ($params['type'] != $sysDepartmentModel->type || $params['group_boss_id'] != $sysDepartmentModel->group_boss_id) {
            $sysDepartmentModel->group_boss_id = $params['group_boss_id'];
        }
        $staffService = new HrStaffInfoService();
        $managerInfo  = $staffService->staffInfo($params['manager_id']);

        if ($params['manager_id'] != $sysDepartmentModel->manager_id) {
            $sysDepartmentModel->manager_id    = $params['manager_id'];
            $sysDepartmentModel->manager_name  = $managerInfo->name;
            $sysDepartmentModel->manager_phone = $managerInfo->mobile;
        }


        if (!empty($params['assistant_id']) && $params['assistant_id'] != $sysDepartmentModel->assistant_id) {
            $assistantInfo                      = $staffService->staffInfo($params['assistant_id']);
            $sysDepartmentModel->assistant_id   = $assistantInfo->staff_info_id;
            $sysDepartmentModel->assistant_name = $assistantInfo->name;
        }
        $sysDepartmentModel->name                   = $params['department_name'];
        $sysDepartmentModel->country_code           = $params['country_code'];
        $sysDepartmentModel->type                   = $params['type'];
        $sysDepartmentModel->level                  = $params['level'];
        $sysDepartmentModel->sap_company_address    = $params['sap_company_address'];
        $sysDepartmentModel->sap_company_id         = $params['sap_company_id'];
        $sysDepartmentModel->sap_cost_center        = $params['sap_cost_center'];
        $sysDepartmentModel->sap_describe           = $params['sap_describe'];
        $sysDepartmentModel->sap_reporting_currency = $params['sap_reporting_currency'];
        $sysDepartmentModel->sap_tax_id             = $params['sap_tax_id'];
        $sysDepartmentModel->kingdee_cost_center    = $params['kingdee_cost_center'];
        $sysDepartmentModel->manager_position_state = $params['manager_position_state'];
        $sysDepartmentModel->relevance_store_id     = $params['relevance_store_id'];

        if ($params['ancestry'] != $sysDepartmentModel->ancestry) {
            $sysDepartmentModel->ancestry    = $params['ancestry'];
            $parentDepartment                = SysDepartmentFleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['ancestry']],
            ]);
            $sysDepartmentModel->ancestry_v3 = $parentDepartment->ancestry_v3 . '/' . $sysDepartmentModel->id;
            if ($parentDepartment->type == 2) {
                $sysDepartmentModel->company_id   = $parentDepartment->company_id;
                $sysDepartmentModel->company_name = $parentDepartment->company_name;
            } else {
                $sysDepartmentModel->company_id   = 0;
                $sysDepartmentModel->company_name = '';
            }
            if ($parentDepartment->type == 1) {
                $sysDepartmentModel->company_id   = $parentDepartment->id;
                $sysDepartmentModel->company_name = $parentDepartment->name;
            }
        }
        if (!empty($params['relevance_store_id']) && !empty($managerInfo)) {
            $storeInfo = SysStoreFleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['relevance_store_id']],
            ]);
            if ($storeInfo) {
                $storeInfo->manager_id    = $params['manager_id'];
                $storeInfo->manager_name  = $managerInfo->name;
                $storeInfo->manager_phone = $managerInfo->mobile;
                $storeInfo->save();
            }
        }
        $result['result'] = $sysDepartmentModel->save();
        return $result;
    }

    /**
     * @throws ValidationException
     */
    protected static function updateSysDepartmentV2MS($params)
    {
        $api_params = [
            'id'                     => (string)$params['department_id'],   //部门ID
            'name'                   => (string)$params['department_name'], //部门名称
            'manager_id'             => (int)$params['manager_id'],         //负责人ID
            'ancestry'               => (string)$params['ancestry'],
            'type'                   => (int)$params['type'],  //类型
            'level'                  => (int)$params['level'], //层级
            'group_boss_id'          => (string)$params['group_boss_id'],
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'manager_position_state' => $params['manager_position_state'] ?? null,
            'relevance_store_id'     => $params['relevance_store_id'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
            'operator_id'            => intval($params['operator_id'] ?? 0),

        ];
        if (!empty($params['assistant_id'])) {
            $api_params['assistant_id'] = $params['assistant_id'];
        }
        if (!empty($params['company_id'])) {
            $api_params['company_id'] = $params['company_id'];
        }
        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/department/update',
            $api_params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            return true;
        }
        throw new ValidationException($return['message'] ?? self::$t->_('retry_later'), ErrCode::$VALIDATE_ERROR);
    }


    /**
     * @param $department_id
     * @return bool
     * @throws ValidationException
     */
    public static function deleteSysDepartment($department_id,$operator_id)
    {
        if (env('break_away_from_ms')) {
            return self::deleteSysDepartmentSelf($department_id);
        } else {
            return self::deleteSysDepartmentMS($department_id,$operator_id);
        }
    }

    protected static function deleteSysDepartmentSelf($department_id)
    {
        $sysDepartmentModel          = SysDepartmentFleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $department_id],
        ]);
        $sysDepartmentModel->deleted = 1;
        return $sysDepartmentModel->save();
    }

    /**
     * @throws ValidationException
     */
    protected static function deleteSysDepartmentMS($department_id,$operator_id)
    {
        //调用接口执行删除操作
        $params['department_id'] = $department_id;
        $params['operator_id']   = intval($operator_id ?? 0);

        $api                     = new RestClient('nws');
        $return                  = $api->execute(RestClient::METHOD_POST, '/svc/department/delete',
            $params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            return true;
        }
        throw new ValidationException($return['message']??self::$t->_('retry_later'), ErrCode::$BUSINESS_ERROR);
    }


    /**
     * @param $job_title_id
     * @param $job_name
     * @return true
     * @throws \Exception
     */
    public static function addJobTitle($job_title_id, $job_name): bool
    {
        if (env('break_away_from_ms')) {
            return self::addJobTitleSelf($job_title_id, $job_name);
        } else {
            return self::addJobTitleMs($job_title_id, $job_name);
        }
    }

    /**
     * @param $job_title_id
     * @param $job_name
     * @return true
     * @throws ValidationException
     */
    protected static function addJobTitleSelf($job_title_id, $job_name): bool
    {
        $jobTitleModel = StaffInfoJobTitleModel::findFirst([
            'conditions' => 'name = :name: and state = :state: and deleted = 0',
            'bind'       => ['name' => $job_name, 'state' => STATUS_VALID],
        ]);

        if (!empty($jobTitleModel)) {
            throw new ValidationException(self::$t->_('job_name_exists'));
        }

        $jobTitleModel        = new StaffInfoJobTitleModel();
        $jobTitleModel->id    = $job_title_id;
        $jobTitleModel->name  = $job_name;
        $jobTitleModel->state = StaffInfoJobTitleModel::STATUS_VALID;
        $jobTitleModel->save();
        return true;
    }


    /**
     * @param $job_title_id
     * @param $job_name
     * @return true
     * @throws \Exception
     */
    protected static function addJobTitleMs($job_title_id, $job_name): bool
    {
        //同步ms
        $ms_params = ['job_title_id' => $job_title_id, 'job_name' => $job_name];
        $fle_rpc   = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffInfoSvc', 'addJobTitle', self::$language);
        $fle_rpc->setParams($ms_params);
        $fle_return = $fle_rpc->execute();
        if (isset($fle_return['error'])) {
            throw new ValidationException($fle_return['error']);
        }
        return true;
    }

    /**
     * @param $job_id
     * @param $job_name_new
     * @return bool
     * @throws ValidationException
     * @throws Exception
     */
    public static function updateJobTitle($job_id, $job_name_new): bool
    {
        if (env('break_away_from_ms')) {
            return self::updateJobTitleSelf($job_id, $job_name_new);
        } else {
            return self::updateJobTitleMs($job_id, $job_name_new);
        }
    }


    /**
     * @param $job_id
     * @param $job_name_new
     * @return true
     * @throws ValidationException
     * @throws Exception
     */
    public static function updateJobTitleSelf($job_id, $job_name_new): bool
    {
        $jobTitleModelExists = StaffInfoJobTitleModel::findFirst([
            'conditions' => 'name = :name: and state = :state: and deleted = 0 and id != :id:',
            'bind'       => ['name' => $job_name_new, 'state' => STATUS_VALID, 'id' => $job_id],
        ]);
        if (!empty($jobTitleModelExists)) {
            throw new ValidationException(self::$t->_('job_name_exists'));
        }

        $jobTitleModel = StaffInfoJobTitleModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $job_id],
        ]);

        if (empty($jobTitleModel)) {
            throw new Exception('not exists');
        }
        $jobTitleModel->name = $job_name_new;
        $jobTitleModel->save();
        return true;
    }


    /**
     * @param $job_id
     * @param $job_name_new
     * @return true
     * @throws Exception
     */
    protected static function updateJobTitleMs($job_id, $job_name_new): bool
    {
        //同步ms
        $ms_params = ['job_title_id' => $job_id, 'job_name' => $job_name_new];
        $fle_rpc   = new ApiClient('fle', 'com.flashexpress.fle.svc.api.StaffInfoSvc', 'updateJobTitle',
            self::$language);
        $fle_rpc->setParams($ms_params);
        $fle_return = $fle_rpc->execute();
        if (isset($fle_return['error'])) {
            throw new ValidationException($fle_return['error']);
        }
        return true;
    }


}
