<?php

namespace App\Modules\Organization\Services;

use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Organization\Models\DepartmentBatchCreateLogModel;
use App\Modules\Organization\Models\DeptPcCodeModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\Common\Models\EnvModel;
use App\Library\MnsClient;
use App\Library\Enums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Organization\Models\SysManagePieceModel;
use App\Modules\Organization\Models\SysManageRegionModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Hc\Services\BudgetSummaryService;
use App\Modules\TalentReview\Services\PermissionsService as TalentReviewPermissionsService;
use App\Repository\DepartmentRepository;
use App\Models\fle\SysDepartmentModel as SysDepartmentModelFle;
use App\Modules\Setting\Services\FinancialService;
use App\Repository\HrStaffRepository;
use App\Models\backyard\SettingEnvModel;

class DepartmentService extends BaseService
{
    const TABLE_DEPARTMENT = 'sys_department';//部门表
    const TABLE_GROUP_BOSS = 'sys_group_boss';//group组织表
    const DEPARTMENT_SORT = ' order by id asc ';//部门表排序条件
    private $db_fle; //fle数据库链接

    public static $validate_company_edit_params_v2 = [
        'id'                  => 'Required|IntGt:0',    //组织ID
        'name'                => 'Required|StrLenGe:1', //组织名称
        'manager_id'          => 'Required|IntGt:0',    //负责人ID
        'kingdee_cost_center' => 'StrLenGeLe:0,15',     //金蝶成本中心
    ];
    public static $validate_company_add_params_v2 = [
        'name'                => 'Required|StrLenGe:1', //组织名称
        'manager_id'          => 'Required|IntGt:0',    //负责人ID
        'type'                => 'IntIn:1,2,3,4,5',     //部门类型 2,公司部门，3：group组织部门(ceo，cfo...)
        'level'               => 'IntIn:0,1,2,3,4,99',
        'kingdee_cost_center' => 'StrLenGeLe:0,15',     //金蝶成本中心
    ];

    public static $validate_department_list_params_v2 = [
        'id'   => 'Required|IntGt:0', //组织ID
        'type' => 'IntIn:1,4,5', //部门类型 2,公司部门，3：group组织部门(ceo，cfo...)

    ];
    //创建部门接口参数验证规则
    public static $validate_department_add_params = [
        'department_name'        => 'Required|StrLenGe:1', //组织名称
        'group_boss_id'          => 'Required|IntGt:0',    //组织ID
        'manager_id'             => 'Required|IntGt:0',    //部门负责人工号
        'ancestry'               => 'Required|IntGe:0',    //上级组织（部门）ID
        'level'                  => 'IntIn:0,1,2,3,4',     //部门等级
        'type'                   => 'IntIn:1,2,3',         //部门类型 2,公司下的部门，3：group组织部门(ceo，cfo等下的部门)
        'sap_company_id'         => 'StrLenGeLe:0,20',
        'sap_reporting_currency' => 'StrLenGeLe:0,10',
        'sap_describe'           => 'StrLenGeLe:0,50',
        'sap_cost_center'        => 'StrLenGeLe:0,20',
        'sap_company_address'    => 'StrLenGeLe:0,500',
        'sap_tax_id'             => 'StrLenGeLe:0,50',
        'kingdee_cost_center'    => 'StrLenGeLe:0,15', // 金蝶成本中心
    ];
    //修改部门接口参数验证规则
    public static $validate_department_edit_params = [
        'department_id'          => 'Required|IntGt:0',    //组织名称
        'department_name'        => 'Required|StrLenGe:1', //组织名称
        'group_boss_id'          => 'Required|IntGt:0',    //组织ID（公司ID）
        'manager_id'             => 'Required|IntGt:0',    //部门负责人工号
        'ancestry'               => 'Required|IntGt:0',    //上级组织（部门）ID
        'level'                  => 'IntIn:0,1,2,3,4',     //部门等级
        'type'                   => 'IntIn:1,2,3',         //部门类型 2,公司下的部门，3：group组织部门(ceo，cfo等下的部门)
        'sap_company_id'         => 'StrLenGeLe:0,20',
        'sap_reporting_currency' => 'StrLenGeLe:0,10',
        'sap_describe'           => 'StrLenGeLe:0,50',
        'sap_cost_center'        => 'StrLenGeLe:0,20',
        'sap_company_address'    => 'StrLenGeLe:0,500',
        'sap_tax_id'             => 'StrLenGeLe:0,50',
        'manager_position_state' => 'IntIn:1,2',       //1正职 2代理
        'kingdee_cost_center'    => 'StrLenGeLe:0,15', //金蝶成本中心
    ];

    //删除组织参数校验
    public static $validate_department_del_params = [
        'department_id' => 'Required|Int|>>>:Department ID is error',
    ];

    public static $batch_create_dept_max_excel_count = 100;
    public static $batch_create_dept_min_excel_count = 1;

    public static $organization_menu_id = 140; //组织架构id
    public static $organization_department_menu_id = 59; //部门架构
    public static $organization_department_menu_action_id = [60, 61, 62, 63, 141, 142, 201, 202, 665];

    public static $organization_job_manage_menu_id = 331; //职位体系
    public static $organization_job_manage_job_menu_id = 338; //职位管理
    public static $organization_job_manage_job_menu_action_id = [342, 345, 346, 347,348, 720];
    public static $organization_job_manage_job_log_menu_id = 380; //列表查看
    public static $organization_job_manage_job_log_action_id = [381];

    public static $organization_job_manage_job_group_menu_id = 339;//职组管理
    public static $organization_job_manage_jd_menu_id = 340;//JD管理
    public static $organization_job_manage_competency_menu_id = 341;//胜任力管理

    public static $organization_edit_info = 'edit_info';
    public static $organization_edit_sap = 'edit_sap';

    public static $department_change_ancestry_redis_key = 'department_change_ancestry';

    //19609 NW员工上级根据组织自动更新
    public static $nw_manager_country_list = [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE];

    public function __construct()
    {
        $this->db_fle = $this->getDI()->get('db_fle');
    }

    //公司列表
    public function getCompanyList($format = 0)
    {
        $company_list = SysDepartmentModel::find([
            'conditions' => "deleted=0 and type = 1",
        ])->toArray();
        if ($format == 1) {
            $company_list = $this->_format_ret_column($company_list, 0);
        }

        return $company_list;
    }

    //部门详情
    public function getDepartmentDetail($department_id, $is_del = 0)
    {
        $where = '';
        if(!$is_del) {
            $where = ' and deleted=0';
        }
        $detail = SysDepartmentModelFle::findFirst(
            [
                'conditions' => 'id =:id: '.$where,
                'bind'=> [
                    'id' => $department_id,
                ],
            ]
        );
        return !empty($detail) ? $detail->toArray() : [];
    }

    public function getDetail($id) {

        $detail = $this->getDepartmentDetail($id);

        if(empty($detail))  return [];

        $last_country_detail['country_code']='';
        if(!empty($detail['ancestry'])){
            $last_country_detail = $this->getDepartmentDetail($detail['ancestry']);
        }
        //当前部门在职人数
        $current_count = HrStaffInfoModel::count([
            'formal =1 and state in(1,3) and is_sub_staff = 0 and node_department_id = ?0',
            'bind' => [
                0 => $id,
            ],
        ]);

        //子部门id
        $ancestry_v3 = empty($detail['ancestry_v3']) ? $detail['id'] : $detail['ancestry_v3'];
        $child_list = SysDepartmentModel::find([
                            'conditions' => "deleted=0 and ancestry_v3 like :ancestry_v3: or id = :id:",
                            'bind' => [
                                'ancestry_v3' => $ancestry_v3."/%",
                                'id' => $id,
                            ],
                        ])->toArray();

        $child_id_list = array_column($child_list, 'id');
        //该部门及自部门在职人数
        $total_count = HrStaffInfoModel::count([
                'formal IN (1, 4) and state = 1 and is_sub_staff = 0 and node_department_id in ({ids:array})',
                'bind' => [
                    'ids' => $child_id_list,
                ],
            ]);

        $group_boss_id = $detail['group_boss_id'];
        if($detail['type'] == 4) {
            $group_boss_id = $detail['ancestry'];
        }
        if($detail['type'] == 5) {
            $group_boss_id = $id;
        }

        $dept_pc_code = DeptPcCodeModel::findFirst([
            'conditions' => "department_id = :department_id:",
            'bind' => [
                'department_id' => $id,
            ],
        ]);
        $sap_cost_center = $detail['sap_cost_center'];
        $kingdee_cost_center = $detail['kingdee_cost_center'];
        if (!empty($dept_pc_code)) {
            $sap_cost_center     = $dept_pc_code->pc_code;
            $kingdee_cost_center = $dept_pc_code->kingdee_cost_center;
        }

        $relevance_store_name = '';
        if(!empty($detail['relevance_store_id'])) {
            $store_detail = (new StoreService())->getStoreDetail($detail['relevance_store_id']);
            if(!empty($store_detail)) {
                $relevance_store_name = $store_detail['name'];
            }
        }
        $is_show_relevance_store = 0;

        $show_relevance_store_department_list = $this->getShowRelevanceStoreDepartment();
        $show_relevance_store_department_ids = array_column($show_relevance_store_department_list, 'id');
        if(in_array($id, $show_relevance_store_department_ids)) {
            $is_show_relevance_store = 1;
        }
        return [
            'id'                      => $detail['id'],
            'name'                    => $detail['name'],
            'ancestry'                => $detail['ancestry'],
            'ancestry_v3'             => $detail['ancestry_v3'],
            'manager_id'              => $detail['manager_id'],
            'manager_name'            => $detail['manager_name'],
            'assistant_id'            => $detail['assistant_id'],
            'assistant_name'          => $detail['assistant_name'],
            'group_boss_id'           => $group_boss_id,
            'type'                    => $detail['type'],
            'level'                   => $detail['level'],
            'current_count'           => $current_count,
            'total_count'             => $total_count,
            'sap_company_id'          => $detail['sap_company_id'],
            'sap_reporting_currency'  => $detail['sap_reporting_currency'],
            'sap_describe'            => $detail['sap_describe'],
            'sap_cost_center'         => $sap_cost_center,
            'sap_company_address'     => $detail['sap_company_address'],
            'sap_tax_id'              => $detail['sap_tax_id'],
            'country_code'            => $detail['country_code'] ?? '',
            'last_country_code'       => $last_country_detail['country_code'] ?? '',
            'relevance_store_id'      => $detail['relevance_store_id'],
            'relevance_store_name'    => $relevance_store_name,
            'is_show_relevance_store' => $is_show_relevance_store,
            'kingdee_cost_center'     => $kingdee_cost_center,
        ];
    }

    /**
     * 获取组织范围
     * 2023-03-23更新权限范围
     * @param int $staff_info_id
     * @return mixed
     */
    public function getLeftTreeV2($staff_info_id = 0) {
        $group_ceo_id = GlobalEnums::TOP_DEPARTMENT_ID;
        $left_list = SysDepartmentModel::find([
                            'columns' => 'id, name, ancestry, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                            'conditions' => 'type in (1, 4, 5) and deleted = 0',
                        ])->toArray();
        $left_list = array_column($left_list, null, 'id');

        //权限范围控制
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id);

        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            return [];
        }

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position,
        ] = $this->getOrganizationEditPermission($staff_info_id);

        $res         = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);
        $departments = $res['departments'] ?? [];

        //如果配置角色id 则按照管辖范围可编辑，如果没有在配置角色id内则看到的都能编辑
        foreach ($left_list as $key => $value) {
            if (array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                $left_list[$key]['is_edit_organization_info'] = in_array($value['id'], $departments);
            } else {
                $left_list[$key]['is_edit_organization_info'] = true;
            }

            if (array_intersect($organization_edit_sap_info_roles_id_arr, $staff_position)) {
                $left_list[$key]['is_edit_organization_sap_info'] = in_array($value['id'], $departments);
            } else {
                $left_list[$key]['is_edit_organization_sap_info'] = true;
            }
        }

        //进行过滤
        if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $left_list = $departmentRelation->formatDepartmentList($left_list,$dominion_department_ids);
        }


        $left_tree = $left_list[$group_ceo_id];//ceo 组织
        unset($left_list[$group_ceo_id]);

        $list = $this->_left_tree_list($left_list, $group_ceo_id);
        unset($left_tree['ancestry']);
        $left_tree['child'] = $list;
        return $left_tree;
    }

    private function _left_tree_list($data_list, $parentId = '')
    {
        $ret_data = [];
        if (empty($data_list)) {
            return [];
        }
        foreach ($data_list as $item) {
            if($parentId == $item['ancestry']) {
                $ret_data[] = [
                    'id'             => intval($item['id']),
                    'name'           => $item['name'],
                    'manager_id'     => $item['manager_id'] ? intval($item['manager_id']) : '',
                    'manager_name'   => $item['manager_name'] ?? '',
                    'assistant_id'   => $item['assistant_id'] ? intval($item['assistant_id']) : '',
                    'assistant_name' => $item['assistant_name'] ?? '',
                    'company_id'     => $item['company_id'],
                    'type'           => $item['type'],//1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别
                    'level'          => $item['level'] ?? 0,
                    'child'          => $this->_left_tree_list($data_list, $item['id']),
                    'group_boss_id'  => $item['group_boss_id'],
                    'is_edit_organization_info'     => $item['is_edit_organization_info'],
                    'is_edit_organization_sap_info' => $item['is_edit_organization_sap_info'],
                ];
            }
        }
        return $ret_data;
    }
    
    public function getDepartmentFHOList($params)
    {
        $company_config = EnumsService::getInstance()->getSettingEnvValueMap('sys_department_company_ids');
        $id = $company_config['FlashHomeOperation'] ?? '';
        $newArray = [];
        if (!empty($id)){
            $list = $this->getDeparmentByCompany($id);
           foreach ($list as $k=>$v){
               if ($v['level'] == 2){
                   $newArray[$k]['id'] = $v['id'] ?? '';
                   $newArray[$k]['name'] = $v['name'] ?? '';
               }
           }
        }
        return [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => array_values($newArray)
        ];
    }

    /**
     * 部门列表数据
     * @param $company_id
     * @param bool $export_action
     * @param $parent
     * @return array
     */
    public function getDepartmentListV2($id, $type , $staff_info_id)
    {
        //type = 5 or type = 4 查询组织下的部门 查询type = 3 的部门
        //type = 1 查询公司下的部门 type = 2 的部门
        //获取指定组织下所有部门数据
        if (in_array($type,[Enums::TYPE_GROUP_CEO, Enums::TYPE_GROUP_BOSS])) {
            $list = $this->getDeparmentByGroupBoss($id);
        } elseif ($type == Enums::TYPE_COMPANY) {
            $list = $this->getDeparmentByCompany($id);
        } else {
            $list = [];
        }

        //获取所有部门负责人信息
        $staff_info_ids = array_filter(array_unique(array_column($list, 'manager_id')));
        $hr_staffs      = [];
        if ($staff_info_ids) {
            $hr_staff_service = new HrStaffInfoService();
            $hr_staffs        = $hr_staff_service->getHrStaffInfo($staff_info_ids);
        }

        $dept_pc_code_list = DeptPcCodeModel::find()->toArray();
        $dept_pc_code_list = array_column($dept_pc_code_list, null, 'department_id');

        $store_list = SysStoreModel::find([
            'columns' =>'id,name,manager_id,manager_name,category,manage_region,manage_piece,manager_position_state',
            'conditions' => 'state = 1',
        ])->toArray();
        $store_list = array_column($store_list, null, 'id');

        $show_relevance_store_department_list = $this->getShowRelevanceStoreDepartment();
        $show_relevance_store_department_ids = array_column($show_relevance_store_department_list, 'id');
        foreach ($list as $key => $value) {
            $dept_pc_code = $dept_pc_code_list[$value['id']]['pc_code'] ?? '';
            $list[$key]['sap_cost_center'] = empty($value['sap_cost_center']) ? $dept_pc_code : $value['sap_cost_center'];
            if(!empty($value['relevance_store_id'])) {
                $list[$key]['relevance_store_name'] = $store_list[$value['relevance_store_id']]['name'] ?? '';
            } else {
                $list[$key]['relevance_store_name'] = '';
            }

            if(in_array($value['id'], $show_relevance_store_department_ids)) {
                $list[$key]['is_show_relevance_store'] = 1;
            } else {
                $list[$key]['is_show_relevance_store'] = 0;
            }
        }

        //权限范围控制
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id);

        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            return [];
        }

        //进行过滤
        if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $list = $departmentRelation->formatDepartmentList($list,$dominion_department_ids);
        }

        //获取所有部门下员工人数
        $department_worker_nums = $this->getDepartmentWorkCount();
        if ($list) {
            //对每页的数据做批量处理查找上级
            $deparment_list  = $this->getDeparmentInIdByData(array_filter(format_array(array_column($list,'id'))));
        }

		//获取所有部门下的 hc 预算
	    $budget_summary_service = new BudgetSummaryService();
        $hc_summary_list = $budget_summary_service->getDepartmentSummary();

        //获取子部门是否有下级挂网点
        $children_department = $departmentRelation->getDepartmenthasChildren(array_column($list,'id'));

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position,
        ] = $this->getOrganizationEditPermission($staff_info_id);


        $res         = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);
        $departments = $res['departments'] ?? [];

        foreach ($list as $key => $value) {
            if (array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                $list[$key]['is_edit_organization_info'] = in_array($value['id'], $departments);
            } else {
                $list[$key]['is_edit_organization_info'] = true;
            }

            if (array_intersect($organization_edit_sap_info_roles_id_arr, $staff_position)) {
                $list[$key]['is_edit_organization_sap_info'] = in_array($value['id'], $departments);
            } else {
                $list[$key]['is_edit_organization_sap_info'] = true;
            }
        }

        switch (get_country_code()) {
            case 'PH':
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH;
                break;

            case 'MY':
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY;
                break;

            case 'VN':
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_VN;
                break;

            case 'LA':
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_LA;
                break;

            case 'ID':
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_ID;
                break;

            default:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE;
        }

        $childDepartmentIds        = (new DepartmentRepository())->getManyDepartmentSonList($show_manager_position_department_ids);
        //获取需要展示 正职 代理的部门id
        $this->show_manager_position_department_ids = DepartmentRelationService::getInstance()->getRegionRelationByDeptIds($childDepartmentIds);


        //整理部门从属关系结构数据
        $department_tree_list = $this->_treeNode($list, $hr_staffs, $department_worker_nums, $id,'', $deparment_list ?? [], $hc_summary_list , $children_department);
        // 根据每个部门的直属人数，重新生成一个包含下属部门人数最为最终人数的一个部门树状列表
        $this->count_department_total_worker_nums($department_tree_list);
        return $department_tree_list;
    }

    /**
     * 获取hub下子部门列表
     * @return array
     */
    public function getHubDepartmentList() {
        $hub_department_list = [];
        if(in_array(get_country_code(),['TH','PH'])) {
            $hub_id = [25, 464];
            if(get_country_code() == 'PH') {
                $hub_id = [126];
            }

            $hub_list = SysDepartmentModel::find([
                'columns' => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                'conditions' => "id in({department_ids:array})",
                'bind' => [
                    'department_ids' => $hub_id,
                ],
            ])->toArray();

            if(!empty($hub_list)) {
                foreach ($hub_list as $key => $value) {
                    $list = SysDepartmentModel::find([
                        'columns' => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                        'conditions' => "ancestry_v3 like :ancestry_v3: and deleted = 0",
                        'bind' => [
                            'ancestry_v3' => $value['ancestry_v3'] . "/%",
                        ],
                    ])->toArray();
                    $hub_department_list = array_merge($hub_department_list, $list);
                }
            }
        }
        return $hub_department_list;
    }

    /*
     * 获取fulfillment下子部门列表
     */
    public function getFulfillmentDepartmentList()
    {
        $department_list = [];
        if(get_country_code() != Enums\GlobalEnums::TH_COUNTRY_CODE) {
            return $department_list;
        }

        $fulfillment_id = [20002];
        $fulfillment_list = SysDepartmentModel::find([
            'columns' => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
            'conditions' => "id in({department_ids:array})",
            'bind' => [
                'department_ids' => $fulfillment_id,
            ],
        ])->toArray();

        if(empty($fulfillment_list)) {
            return $department_list;
        }

        foreach ($fulfillment_list as $key => $value) {
            $list = SysDepartmentModel::find([
                'columns' => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                'conditions' => "ancestry_v3 like :ancestry_v3: and deleted = 0",
                'bind' => [
                    'ancestry_v3' => $value['ancestry_v3'] . "/%",
                ],
            ])->toArray();
            $department_list = array_merge($department_list, $list);
            }

        return $department_list;
    }

    /**
     * 获取显示关联网点的部门数据
     * @return array
     */
    public function getShowRelevanceStoreDepartment(): array
    {
        $department_list = [];
        $val = (new SettingEnvModel())->getValByCode('show_relevance_store_department_ids');
        if (!empty($val)) {
            $relevance_store_department_ids  = explode(',', $val);
            $relevance_store_department_list = SysDepartmentModel::find([
                'columns'    => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name',
                'conditions' => "id in({department_ids:array})",
                'bind'       => [
                    'department_ids' => $relevance_store_department_ids,
                ],
            ])->toArray();

            if (!empty($relevance_store_department_list)) {
                foreach ($relevance_store_department_list as $key => $value) {
                    $list            = SysDepartmentModel::find([
                        'columns'    => 'id, name, ancestry, ancestry_v3, manager_id, manager_name, assistant_id, assistant_name',
                        'conditions' => "ancestry_v3 like :ancestry_v3: and deleted = 0",
                        'bind'       => [
                            'ancestry_v3' => $value['ancestry_v3'] . "/%",
                        ],
                    ])->toArray();
                    $department_list = array_merge($department_list, $list);
                }
            }
        }
        return $department_list;
    }

    public function exportDepartmentListV2($cond ,$staff_info_id = 0)
    {
        $department_id = $cond['department_id'] ?? 0;
        $id            = $cond['id'] ?? 0;//公司或组织ID
        $type          = $cond['type'] ?? 0;

        if($department_id == 0) {
            $department_id = $id;
        }

        $department  = $this->getDepartmentDetail($department_id);

        $data = [];
        $header = [
            static::$t->_('department.id'),
            static::$t->_('department.name'),
            static::$t->_('department.group_ceo'),
            static::$t->_('department.c_level'),
            static::$t->_('department.bu'),
            static::$t->_('department.level_1'),
            static::$t->_('department.level_2'),
            static::$t->_('department.level_3'),
            static::$t->_('department.level_4'),
            static::$t->_('department.manager'),
            static::$t->_('department.manager_job_title'),
        ];
        $file_name = "department-info-" . date('YmdHis');
        if(!empty($department)) {
            $children_department_list = $this->getChildrenListByDepartmentIdV2($department_id);
            $department_list = array_merge([$department],$children_department_list);

            if(empty($department_list)){
                return ['code' => 0, 'data' => 'data empty'];
            }

            //获取所有部门负责人信息
            $staff_info_ids   = array_filter(array_unique(array_column($department_list, 'manager_id')));
            $hr_staff_service = new HrStaffInfoService();
            $hr_staffs        = $hr_staff_service->getHrStaffInfo($staff_info_ids);

            //部门列表
            $department_all_list = SysDepartmentModel::find([
                'columns' => "id,name,ancestry,ancestry_v3,type,level,company_id,company_name",
            ])->toArray();
            if (!empty($department_all_list)) {
                $department_all_list = array_column($department_all_list, null, 'id');
            }

            // 下载处理
            if ($department_id) {
                $file_name = "department-info-d{$department_id}-" . date('YmdHis');
            } else {
                $file_name = "department-info-g{$id}-t{$type}-" . date('YmdHis');
            }

            //获取我管辖的部门
            $departmentRelation = DepartmentRelationService::getInstance();
            $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id, false,false);

            //无权限直接返回
            if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
                $department_list = [];
            } elseif ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
                $department_list = $departmentRelation->formatDepartmentList($department_list,$dominion_department_ids);
            }

            //直接排除
            $data = [];
            foreach ($department_list as $keys => $depart) {
                $manager_info  = $depart['manager_id'] . ($depart['manager_name'] ?? '');//负责人：工号+姓名
                $department_level = $this->getDepartmentLevel($depart['id'], $department_all_list);

                $data[] = [
                    $depart['id'] ?? 0,//组织ID
                    $depart['name'] ?? '',//部门名称
                    $department_level[0] ?? '-',
                    $department_level[1] ?? '-',
                    $department_level[2] ?? '-',
                    $department_level[3] ?? '-',
                    $department_level[4] ?? '-',
                    $department_level[5] ?? '-',
                    $department_level[6] ?? '-',
                    $manager_info,//负责人
                    $hr_staffs[$depart['manager_id']]['job_title_name'] ?? '',//职位
                ];
            }
        }

        return $this->exportExcel($header, $data, $file_name);
    }

    public function getDepartmentStructersDataV2($cond , $staff_info_id = 0) {
        $department_id = $cond['department_id'] ?? 0;
        $id            = $cond['id'] ?? 0;//公司或组织ID
        $type          = $cond['type'] ?? 0;

        if($department_id == 0) {
            $department_id = $id;
        }

        return $this->get_department_structures_dataV2($department_id, $staff_info_id);
    }

    private function get_department_structures_dataV2($department_id , $staff_info_id){

        $department  = $this->getDepartmentDetail($department_id);

        $department_structures_data = [
            'id'       => $department['id'],
            'label'    => $department['name'],
            'children' => [],
        ];

        //获取部门下所有子部门
        $children_department_list   = $this->getChildrenListByDepartmentIdV2($department_id);
        if(empty($children_department_list)) return $department_structures_data;

        //权限范围控制
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id);

        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            return $department_structures_data;
        }

        //进行过滤
        if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $children_department_list = $departmentRelation->formatDepartmentList($children_department_list,$dominion_department_ids);
        }

        //返回部门架构图 数据
        $department_structures_data['children'] = $this->_treeNodeViewStructures($children_department_list, $department['id']);
        return $department_structures_data;
    }

    /**
     * 根据公司ID获取公司下面所有部门列表
     * @param $company_id
     * @return mixed
     */
    public function getDeparmentByCompany($company_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(SysDepartmentModel::class);
        $builder->where('deleted = 0 and id != 6 and type=2');
        $builder->andWhere('company_id = :company_id:', ['company_id' => $company_id]);
        $builder->orderby('name ASC');
        $list = $builder->getQuery()->execute()->toArray();
        return $list ? $list : [];
    }

    /**
     * 根据指定group组织id 下所有直属部门
     * @param $company_id
     * @return mixed
     */
    public function getDeparmentByGroupBoss(int $group_boss_id)
    {
        //type=3 标识组织下直属部门
        $list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and type = 3 and group_boss_id = :group_boss_id:',
            'bind' => [
                'group_boss_id' => $group_boss_id,
            ],
            'order' => 'id asc',
        ])->toArray();

        return $list ? $list : [];
    }

    /**
    * 处理类
    * @Token
    * @Date: 2021-09-15 17:17
    * @author: peak pan
    * @return:
    **/
    public function getDeparmentInIdByData(array $ids)
    {
        $list = SysDepartmentModel::find([
            'conditions' => ' deleted = 0 and ancestry > 0 and id IN ({ids:array})',
            'bind' => [
                'ids' => $ids,
            ],
            'columns' =>'country_code,ancestry',
            'order' => 'id asc',
        ])->toArray();
        return $list ? array_column($list,'country_code','ancestry') : [];
    }

    public function getAvailableParentDepartmentV2($department_id)
    {
        $department_detail = $this->getDepartmentDetail($department_id);
        //获取当前部门节点级别，以及当前部门的上上级部门ID类型
        $current_department_level = $this->get_department_node_levelV2($department_detail, $grandpa_department_id);
        //$grandpa_department_type  = $department_detail['type'];//1：公司；2：公司下的部门；3：是组织下的部门，新增扩展 4：boss级别（coo/cto/c..o）；5:gropu ceo 级别

        //todo 获取 向下可移动部门
        //（同一父级下兄弟级部门&原本上级部门数据）
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(SysDepartmentModel::class);
        $builder->where('deleted = 0');
        if ($current_department_level < 3) { //获取同一上级部门的的兄弟部门& 原本的上级部门
            $builder->andWhere('ancestry = :ancestry:', ['ancestry' => $department_detail['ancestry']]);
            $builder->andWhere('id != :id:', ['id' => $department_id]);
            $builder->orWhere('id = :ancestry:', ['ancestry' => $department_detail['ancestry']]);
        } else { //只获取上级部门
            $builder->andWhere('id = :ancestry:', ['ancestry' => $department_detail['ancestry']]);
        }

        $brother_department_list = $builder->getQuery()->execute()->toArray();
        $brother_department_list = $this->_format_ret_columnV2($brother_department_list, 0);

        $grandpa_department_list = [];
        //todo 获取向上可移动部门
        // 如果是一级部门 ，则他的爷爷部门和他的上级部门都是公司
        if ($current_department_level == 0) {

            $group_company_list = SysDepartmentModel::find([
                                        'columns' => 'id, name, ancestry, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                                        'conditions' => 'type in (1, 4, 5) and deleted = 0',
                                    ])->toArray();

            $grandpa_department_list = $this->_format_ret_columnV2($group_company_list);
        } else {//非一级部门，上上级部门获取
            $list = $this->getDepartmentDetail($grandpa_department_id);
            $grandpa_department_list = $this->_format_ret_columnV2([$list], 1);
        }

        //合并 二者部门作为最终可供移动的上级部门选项
        $ret_data = array_merge($grandpa_department_list, $brother_department_list);
        $ret_data = array_values(array_column($ret_data, null, 'id'));
        return $ret_data;
    }

    public function get_department_node_levelV2($departent_detail, &$grandpa_department_id = 0)
    {
        $department_ancestry_v3 = explode('/', $departent_detail['ancestry_v3']);// 数组键值对关系：部门级别（索引位置）=>部门ID
        $group_company_list = SysDepartmentModelFle::find([
                                        'columns' => 'id, name, ancestry, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                                        'conditions' => 'type in (1, 4, 5) and deleted = 0',
                                    ])->toArray();
        $group_company_ids = array_column($group_company_list, 'id');
        $department_level_id_map = array_values(array_diff($department_ancestry_v3, $group_company_ids));//去除组织和公司

        $department_id_level_map = array_flip($department_level_id_map); // 反转数组，数组键值对关系：部门ID => 部门级别（索引位置）

        $current_department_level = $department_id_level_map[$departent_detail['id']];//当前部门节点级别（根据ancestry_v3 字段部门ID所在的索引位置获取）

        if($current_department_level == 0) { //一级部门
            $grandpa_department_id = 0;
        } else { //非一级部门
            $department_ancestry_v3_flip = array_flip($department_ancestry_v3);
            $c_level = $department_ancestry_v3_flip[$departent_detail['id']];
            $grandpa_department_id = $department_ancestry_v3[$c_level - 2];
        }

        return $current_department_level;
    }


    public function hasChildDepartment($department_id)
    {
        return SysDepartmentModel::count([
            'conditions' => 'deleted = 0 and ancestry= :department_id:',
            'bind' => [
                'department_id' => $department_id,
            ],
        ]);

    }


    /**
     * 获取员工直属部门和员工人数对应关系
     * 条件：正式员工&主账号&在职或停职的
     * @return array
     */
    public function getDepartmentWorkCount()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('node_department_id as department_id,count(1) as count');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('formal =1 and state in(1,3) and is_sub_staff = 0 and node_department_id != 0');
        $builder->andWhere('working_country = :country:', ['country' => (new \App\Models\backyard\HrStaffInfoModel)->getWorkingCountryCode()]);
        $builder->groupby('node_department_id');
        $list                 = $builder->getQuery()->execute()->toArray();
        return array_column($list, null, 'department_id');
    }

    /**
     * 获取员工直属部门和员工人数对应关系
     * 条件：正式员工&主账号&在职或停职的
     * @return array
     */
    public function getDepartmentWorkCountById($department_id)
    {
        return \App\Models\backyard\HrStaffInfoModel::count([
            'conditions' => 'formal =1 and state in(1,3) and is_sub_staff = 0 and node_department_id = :department_id:',
            'bind' => [
                'department_id' => $department_id,
            ],
        ]);
    }


    /**
     * 验证部门名称是否重复
     * @param $department_name
     * @param $type 部门类型 2：公司部门，3:group部门
     * @param $ancestry 上级组织ID
     * @param int $department_id
     * @return array
     */
    public function checkDepartmentName($department_name, $department_id = 0)
    {
        $conditions = 'deleted = 0 and name = :name:';
        $bind = [
            'name' => $department_name,
        ];
        if ($department_id != 0) {
            $conditions = 'deleted = 0 and name = :name:  and id != :department_id:';
            $bind = [
                'name' => $department_name,
                'department_id' => $department_id,
            ];
        }

        $detail = SysDepartmentModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 部门修改-移动部门级别的动作写入消息队列
     * 只有 一级部门变二级部门，二级部门变一级部门 需要调用此方法
     * @param $params_arr
     */
    public function department_level_move_add_to_mns($sys_department_id, $node_department_id)
    {
        try {
            $msg_data   = [
                'sys_department_id'  => $sys_department_id,
                'node_department_id' => $node_department_id,
            ];
            $accessId   = EnvModel::getEnvByCode('mns_access_id');
            $accessKey  = EnvModel::getEnvByCode('mns_access_key');
            $endPoint   = EnvModel::getEnvByCode('mns_end_point');
            $queue_name = EnvModel::getEnvByCode('mns_queue_name_staff_department');
            //发送部门变动关系ID至消息到队列  hris消费
            $mns     = new MnsClient($accessId, $accessKey, $endPoint);
            return $mns->sendToMNS($queue_name, $msg_data);

        } catch (\Exception $e) {
            $this->logger->error('oa:department_level_move 异常，原因：' . $e->getMessage() . $e->getTraceAsString());
            return false;
        }
    }

    public function getChildrenListByDepartmentIdV2($department_id, $only_id = false)
    {
        $department  = $this->getDepartmentDetail($department_id);
        if($department_id == 999) {
            $ancestry_v3 = 999;
        } else {
            $ancestry_v3 = $department['ancestry_v3'] ?? '';
        }

        if(empty($department) || empty($ancestry_v3)) return [];

        $child_list = SysDepartmentModel::find([
            'conditions' => 'deleted = 0 and ancestry_v3 like :ancestry_v3:',
            'bind' => [
                'ancestry_v3' => $ancestry_v3 . '/%',
            ],
            'order' => 'id asc',
        ])->toArray();

        if ($only_id) {
            return array_column($child_list, 'id');
        } else {
            return $child_list;

        }

    }

    /**
     * 格式话左侧菜单（或编辑页面上级组织选项） 结构字段
     * @param $data_list
     * @param int $is_group_boss_data 上级组织是否是group组织（只有sys_group_boss表数据该值为 1）
     */
    private function _format_ret_column($data_list, $is_group_boss_data = 0)
    {
        $ret_data = [];
        if (empty($data_list)) {
            return [];
        }
        foreach ($data_list as $item) {
            $ret_data[] = [
                'id'             => intval($item['id']),
                'name'           => $item['name'],
                'manager_id'     => $item['manager_id'] ? intval($item['manager_id']) : '',
                'manager_name'   => $item['manager_name'] ?? '',
                'assistant_id'   => $item['assistant_id'] ? intval($item['assistant_id']) : '',
                'assistant_name' => $item['assistant_name'] ?? '',
                'group_boss_id'  => $is_group_boss_data ? intval($item['id']) : intval($item['group_boss_id']),//sys_group_boss表中的ID对应department表中是group_boss_id
                'company_id'     => $is_group_boss_data ? 0 : intval($item['company_id']),
                'type'           => $is_group_boss_data ? 3 : ($item['type'] == 1 ? 2 : intval($item['type'])),//部门类型 2：公司部门，3 group组织部门（ $item['type']== 1代表是 子公司调用该方法）
                'level'          => $item['level'] ?? 0,
                'is_group'       => $is_group_boss_data ? 1 : 0,
            ];
        }

        return $ret_data;
    }
    /**
     * 格式话左侧菜单（或编辑页面上级组织选项） 结构字段
     * @param $data_list
     * @param int $is_group_boss_data 上级组织是否是group组织（只有sys_group_boss表数据该值为 1）
     */
    private function _format_ret_columnV2($data_list, $is_group_boss_data = 0)
    {
        $ret_data = [];
        if (empty($data_list)) {
            return [];
        }
        foreach ($data_list as $item) {
            $group_boss_id = intval($item['group_boss_id'] ?? 0);
            $company_id = intval($item['company_id'] ?? 0);
            if(in_array($item['type'], [4, 5])) {
                $group_boss_id = intval($item['id']);
            }
            if($item['type'] == 1) {
                $company_id = intval($item['id']);
            }
            $ret_data[] = [
                'id'             => intval($item['id']),
                'name'           => $item['name'],
                'manager_id'     => $item['manager_id'] ? intval($item['manager_id']) : '',
                'manager_name'   => $item['manager_name'] ?? '',
                'assistant_id'   => $item['assistant_id'] ? intval($item['assistant_id']) : '',
                'assistant_name' => $item['assistant_name'] ?? '',
                'group_boss_id'  => $group_boss_id,
                'company_id'     => $company_id,
                'type'           => intval($item['type'] ?? 0),
                'level'          => $item['level'] ?? 0,
            ];
        }

        return $ret_data;
    }

    /**
     * 判断部门所属公司是否在以下子公司范围内
     * @param $department_id
     * @return bool
     * @date 2022/4/28
     */
    public function isSonByDepartmentId($department_id){
        $department_id = (string)$department_id;
        //查询特定公司id
        $item = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id =:id:',
                'bind'=> ['id'=>$department_id],
            ]
        );
        if (!$item){
            return false;
        }
        return $this->isSonCompanyId((string)$item->company_id);
    }

    /**
     * 判断传来的公司id是不是特定的几个子公司
     * @param $company_id
     * @return bool
     * @date 2022/4/29
     */
    public function isSonCompanyId($company_id){
        $setting_company_ids = EnumsService::getInstance()->getSysDepartmentCompanyIds();
        $company_array = [
            $setting_company_ids['FlashFullfillment'],
            $setting_company_ids['FlashMoney'],
            $setting_company_ids['FlashPay'],
            $setting_company_ids['FCommerce'],
        ];
        if(in_array((string)$company_id,$company_array)){
            return true;
        }
        return false;
    }

    /**
     * 判断是不是FFM公司
     * @param $department_id
     * @return bool
     */
    public function isFFMByDepartmentId($department_id){
        $item = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id =:id:',
                'bind'=> ['id'=>''.$department_id],
            ]
        );
        if(!empty($item) && $item->company_id ==  Enums::$company_types['FlashFullfillment']){
            return true;
        }
        return false;
    }

    /**
     * 判断是不是FlashLaos公司
     * @param $department_id
     * @return bool
     */
    public function isFlashLaosByDepartmentId($department_id){
        $item = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id =:id:',
                'bind'=> ['id'=>''.$department_id],
            ]
        );
        if(!empty($item) && $item->company_id ==  Enums::$company_types['FlashLaos']){
            return true;
        }
        return false;
    }

    /**
     * 查询费用所属公司信息
     * @param $department_id
     * @return []
     */
    public function getCostCompanyByDepartmentId($department_id)
    {
        $item = SysDepartmentModel::findFirst(
            [
                'conditions' => 'id =:id: and deleted = 0 and type =1 and level =0',
                'bind'       => ['id' => '' . $department_id],
            ]
        );
        if ($item) {
            return $item->toArray();
        }

        return [];
    }

    /**
     * 根据部门ID组批量获取部门信息
     * @param array $departmentIds 部门ID组
     * @return array
     */
    public function getDepartmentInfoByIds(array $departmentIds)
    {
        if (!$departmentIds) {
            return [];
        }
        $list = SysDepartmentModel::find([
            'conditions' => 'id in ({ids:array})',
            'bind' => ['ids'=>$departmentIds],
        ]);
        return !empty($list) ? $list->toArray() : [];
    }

    /**
     * 获取所有部门列表
     * @return array
     */
    public function getAllDepartment()
    {
        try {
            // 提取员工部门/公司名称
            $sys_department = SysDepartmentModel::find(['columns' => ['id', 'name']])->toArray();
        } catch (\Exception $e){
            $this->logger->error(json_encode(['msg'=>$e->getMessage()]));
        }
        return $sys_department ?: [];
    }


    public function validate_manager_position_state($param) {

        $department_list = $this->validate_manager_position_state_department($param);

        $region_list = $this->validate_manager_position_state_region($param);

        $piece_list = $this->validate_manager_position_state_piece($param);

        $store_list = $this->validate_manager_position_state_store($param);

        $name_arr = array_merge(array_column($department_list, 'name'), array_column($region_list, 'name'), array_column($piece_list, 'name'), array_column($store_list, 'name'));

        if(empty($name_arr)) {
            return ['code' => 1, 'msg' => 'success'];
        }
        $name_s = implode(',', $name_arr);
        return ['code' => 0, 'msg' => $name_s];
    }

    public function validate_manager_position_state_department($param) {
        $department_id = $param['department_id'] ?? ''; //负责人id
        $manager_id = $param['manager_id'] ?? ''; //负责人id
        $countryCode = strtoupper(env('country_code', 'TH'));
        switch ($countryCode) {
            case 'PH':
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH;
                break;
            case 'MY':
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY;
                break;
            case 'VN':
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_VN;
                break;
            case 'LA':
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_LA;
                break;
            case 'ID':
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_ID;
                break;
            default:
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE;
        }

        $childDepartmentIds        = (new DepartmentRepository())->getManyDepartmentSonList($ids);

        $ids = DepartmentRelationService::getInstance()->getRegionRelationByDeptIds($childDepartmentIds);

        $conditions = 'id in({ids:array}) and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'ids'=> $ids,
            'manager_id' => $manager_id,
        ];
        if(!empty($department_id)) {
            $conditions = 'id in({ids:array})  and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and id != :department_id:';
            $bind = [
                'ids'=> $ids,
                'manager_id' => $manager_id,
                'department_id' => $department_id,
            ];
        }
        $department_list = SysDepartmentModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        return !empty($department_list) ? $department_list->toArray() : [];
    }

    public function validate_manager_position_state_region($param) {
        $manage_region_id = $param['region_id'] ?? ''; //大区id
        $manager_id = $param['manager_id'] ?? ''; //负责人id

        $conditions = 'type = 1 and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'manager_id'=> $manager_id,
        ];
        if(!empty($manage_region_id)) {
            $conditions = 'type = 1 and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and id != :region_id:';
            $bind = [
                'manager_id'=> $manager_id,
                'region_id' => $manage_region_id,
            ];
        }

        $region_list = SysManageRegionModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        return !empty($region_list) ? $region_list->toArray() : [];
    }

    public function validate_manager_position_state_piece($param) {
        $manage_piece_id = $param['piece_id'] ?? ''; //片区id
        $manager_id = $param['manager_id'] ?? ''; //负责人id
        $region_list = SysManageRegionModel::find([
            'conditions' => 'type = 1 and deleted = 0',
        ]);
        $region_list = !empty($region_list) ? $region_list->toArray() : [];
        $region_ids = array_column($region_list, 'id');
        $conditions = 'deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and manage_region_id in ({region_ids:array})';
        $bind = [
            'manager_id' => $manager_id,
            'region_ids' => $region_ids,
        ];

        if(!empty($manage_piece_id)) {
            $conditions = 'deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and manage_region_id in ({region_ids:array}) and id != :piece_id:';
            $bind = [
                'manager_id' => $manager_id,
                'region_ids' => $region_ids,
                'piece_id' => $manage_piece_id,
            ];
        }

        $piece_list = SysManagePieceModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);

        return !empty($piece_list) ? $piece_list->toArray() : [];
    }

    public function validate_manager_position_state_store($param) {
        $store_id = $param['store_id'] ?? ''; //网点id
        $manager_id = $param['manager_id'] ?? ''; //负责人id

        $conditions = 'category in ({category_ids:array}) and state = 1 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'manager_id' => $manager_id,
            'category_ids' => StoreService::$store_category_manager_list,
        ];
        if(!empty($store_id)) {
            $conditions = 'category in ({category_ids:array}) and state = 1 and manager_position_state = 1 and manager_id = :manager_id: and id != :store_id:';
            $bind = [
                'manager_id'   => $manager_id,
                'store_id'     => $store_id,
                'category_ids' => StoreService::$store_category_manager_list,

            ];
        }

        $store_list = SysStoreModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);

        return !empty($store_list) ? $store_list->toArray() : [];
    }

    //更新其他组织负责人正职
    public function updateOtherOrganizationManagerPositionState($param) {
        $manager_id = $param['manager_id']; //负责人id
        $department_id = $param['department_id'] ?? 0;//组织id
        $user = $param['user'];//登录用户信息
        $countryCode = strtoupper(env('country_code', 'TH'));
        if(!in_array($countryCode, self::$nw_manager_country_list)) {
            return false;
        }
        switch ($countryCode) {
            case GlobalEnums::TH_COUNTRY_CODE:
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE;
                break;
            case GlobalEnums::PH_COUNTRY_CODE:
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH;
                break;
            case GlobalEnums::MY_COUNTRY_CODE:
                $ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY;
                break;
        }

        $childDepartmentIds        = (new DepartmentRepository())->getManyDepartmentSonList($ids);

        $ids = DepartmentRelationService::getInstance()->getRegionRelationByDeptIds($childDepartmentIds);

        $conditions = 'id in({ids:array}) and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'ids'=> $ids,
            'manager_id' => $manager_id,
        ];
        if(!empty($department_id)) {
            $conditions = 'id in({ids:array})  and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and id != :department_id:';
            $bind = [
                'ids'=> $ids,
                'manager_id' => $manager_id,
                'department_id' => $department_id,
            ];
        }
        $department_list = SysDepartmentModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
        $department_list = !empty($department_list) ? $department_list->toArray() : [];
        if(!empty($department_list)) {
            foreach ($department_list as $key => $value) {
                $api_params = [
                    'department_id'          => (string)$value['id'],     //部门ID
                    'department_name'        => (string)$value['name'],   //部门名称
                    'manager_id'             => (int)$value['manager_id'],//负责人ID
                    'ancestry'               => (string)$value['ancestry'],
                    'type'                   => (int)$value['type'],  //类型
                    'level'                  => (int)$value['level'], //层级
                    'group_boss_id'          => (string)$value['group_boss_id'],
                    'sap_company_id'         => (string)$value['sap_company_id'],
                    'sap_reporting_currency' => (string)$value['sap_reporting_currency'],
                    'sap_describe'           => (string)$value['sap_describe'],
                    'sap_cost_center'        => (string)$value['sap_cost_center'],
                    'sap_company_address'    => (string)$value['sap_company_address'],
                    'sap_tax_id'             => (string)$value['sap_tax_id'],
                    'country_code'           => (string)$value['country_code'] ?? '',
                    'manager_position_state' => 2,
                    'kingdee_cost_center'    => (string)$value['kingdee_cost_center'] ?? '',
                    'operator_id'            => $user['id'],
                ];
                ThirdApiService::updateSysDepartmentV2($api_params);
                $this->logger->info('updateOtherOrganizationManagerPositionState，操作人工号:' . $user['id'] . ',操作内容' . json_encode($api_params) . ',接口返回：' . json_encode($return));
                $after = $this->getDepartmentDetail($value['id']);
                (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $value, $after, $value['id']);
            }
        }
        return true;
    }

    public function sync_manage_department_manager_ms($param) {
        $department_id = $param['department_id'];
        $manager_id = $param['manager_id'];
        $operator_id = $param['operator_id'];
        $manager_position_state = $param['manager_position_state'];

        $department_detail = $this->getDepartmentDetail($department_id);
        $api_params = [
            'department_id'          => (string)$department_id,             //部门ID
            'department_name'        => (string)$department_detail['name'], //部门名称
            'manager_id'             => (int)$manager_id,                   //负责人ID
            'ancestry'               => (string)$department_detail['ancestry'],
            'type'                   => (int)$department_detail['type'],  //类型
            'level'                  => (int)$department_detail['level'], //层级
            'group_boss_id'          => (string)$department_detail['group_boss_id'],
            'sap_company_id'         => (string)$department_detail['sap_company_id'],
            'sap_reporting_currency' => (string)$department_detail['sap_reporting_currency'],
            'sap_describe'           => (string)$department_detail['sap_describe'],
            'sap_cost_center'        => (string)$department_detail['sap_cost_center'],
            'sap_company_address'    => (string)$department_detail['sap_company_address'],
            'sap_tax_id'             => (string)$department_detail['sap_tax_id'],
            'country_code'           => (string)$department_detail['country_code'] ?? '',
            'manager_position_state' => $manager_position_state,
            'kingdee_cost_center'    => (string)$department_detail['kingdee_cost_center'] ?? '',
            'operator_id'            => $operator_id,
        ];
        ThirdApiService::updateSysDepartmentV2($api_params);
        $after = $this->getDepartmentDetail($department_id);

        $this->logger->info('sync_manage_department_manager_ms,操作内容' . json_encode($api_params, JSON_UNESCAPED_UNICODE) . ',接口返回：' . json_encode($return, JSON_UNESCAPED_UNICODE));
        $staff_info = HrStaffInfoModel::findFirst([
            'staff_info_id = :staff_info_id: and formal IN (1, 4) and state = 1 and is_sub_staff = 0',
            'bind' => [
                'staff_info_id' => $operator_id,
            ],
        ]);
        $staff_info = !empty($staff_info) ? $staff_info->toArray() : [];
        $staff_name = $staff_info['name'] ?? '';
        (new DeptPcCodeService())->saveDeptOperateLog($operator_id, $staff_name, $department_detail, $after, $department_id);
        $this->logger->info('sync_manage_department_manager_ms,操作内容' . json_encode($api_params, JSON_UNESCAPED_UNICODE) . ',接口返回：' . json_encode($return, JSON_UNESCAPED_UNICODE));
        return $return;
    }

    /**
     * 模糊搜索部门list
     * @param $params
     * @return array
     */
    public function search_department_list($params ,$staff_info_id = 0) {
        $search_name = $params['search_name'] ?? '';
        $page_size   = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num    = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset      = $page_size * ($page_num - 1);

        $builder = $this->modelsManager->createBuilder();
        //$builder->columns('id, name, ancestry, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id')
        $builder->from(SysDepartmentModel::class)
            ->where('deleted = 0 AND type in (2, 3)');

        if(!empty($search_name)) {
            $builder->andWhere('name LIKE :search_name:',['search_name' => '%' . $search_name .'%']);
        }

        //获取我管辖的部门
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id, false,false);

        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            $builder->andWhere('id = :department_id:',['department_id' => OrganizationDepartmentEnums::NO_DEPARTMENT]);
        } else if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            $builder->andWhere('id IN ({department_ids:array})',['department_ids' => $dominion_department_ids]);
        }

        $count = $builder->getQuery()->execute()->count();

        $department_list = $builder->orderBy('name')
                            ->limit($page_size, $offset)
                            ->getQuery()
                            ->execute()
                            ->toArray();

        $department_work_count = $this->getDepartmentWorkCount();
        $department_all = $this->getAllDepartment();
        $department_all = array_column($department_all, null, 'id');

        //获取所有部门负责人信息
        $staff_info_ids = array_filter(array_unique(array_column($department_list, 'manager_id')));
        $hr_staffs = [];
        if ($staff_info_ids) {
            $hr_staff_service = new HrStaffInfoService();
            $hr_staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);
        }

        $store_list = SysStoreModel::find([
            'columns' =>'id,name,manager_id,manager_name,category,manage_region,manage_piece,manager_position_state',
            'conditions' => 'state = 1',
        ])->toArray();
        $store_list = array_column($store_list, null, 'id');

        $show_relevance_store_department_list = $this->getShowRelevanceStoreDepartment();
        $show_relevance_store_department_ids = array_column($show_relevance_store_department_list, 'id');

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position,
        ] = $this->getOrganizationEditPermission($staff_info_id);


        $res         = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);
        $departments = $res['departments'] ?? [];

        foreach ($department_list as $key => $value) {
            $work_count = 0;//当前部门及子部门
            $ancestry_v3_arr = explode('/', $value['ancestry_v3']);
            //当前部门及子部门总人数
            $ancestry_v3 = $value['ancestry_v3'];
            $child_list = SysDepartmentModel::find([
                'conditions' => "deleted=0 and ancestry_v3 like :ancestry_v3: or id = :id:",
                'bind' => [
                    'ancestry_v3' => $ancestry_v3."/%",
                    'id' => $value['id'],
                ],
            ])->toArray();
            $dept_ids = array_column($child_list, 'id');
            for ($i = 0; $i < count($dept_ids); $i++) {
                $work_count += $department_work_count[$dept_ids[$i]]['count'] ?? 0;
            }

            $group_company_list = SysDepartmentModel::find([
                'columns' => 'id, name, ancestry, manager_id, manager_name, assistant_id, assistant_name, company_id, type, level, group_boss_id',
                'conditions' => 'type in (1, 4, 5) and deleted = 0',
            ])->toArray();
            $group_company_ids = array_column($group_company_list, 'id');
            $department_id_map = array_values(array_diff($ancestry_v3_arr, $group_company_ids));//去除组织和公司
            $node_level = count($department_id_map);

            $department_list[$key]['work_count'] = $work_count;//部门在职人数
            $department_list[$key]['ancestry_name'] = $department_all[$value['ancestry']]['name'] ?? ''; //上级部门名称
            $department_list[$key]['manager_job_title'] = $hr_staffs[$value['manager_id']]['job_title_name'] ?? ''; //负责人职位

            $department_list[$key]['show_list_create_btn'] = BaseService::is_show_list_create_btn($value['id'], $value['level'], $node_level);//列表页是否显示"新建"按钮

            if(!empty($value['relevance_store_id'])) {
                $department_list[$key]['relevance_store_name'] = $store_list[$value['relevance_store_id']]['name'] ?? '';
            }

            if(in_array($value['id'], $show_relevance_store_department_ids)) {
                $department_list[$key]['is_show_relevance_store'] = 1;
            } else {
                $department_list[$key]['is_show_relevance_store'] = 0;
            }

            if(!empty(array_intersect($organization_edit_info_roles_id_arr, $staff_position))) {
                $department_list[$key]['is_edit_organization_info'] = in_array($value['id'], $departments);
            } else {
                $department_list[$key]['is_edit_organization_info'] = true;
            }

            if(!empty(array_intersect($organization_edit_sap_info_roles_id_arr, $staff_position))) {
                $department_list[$key]['is_edit_organization_sap_info'] = in_array($value['id'], $departments);
            } else {
                $department_list[$key]['is_edit_organization_sap_info'] = true;
            }

            unset($department_list[$key]['created_at']);
            unset($department_list[$key]['updated_at']);
        }

        return [
            'items' => $department_list,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ],
        ];
    }

    /**
     * 显示开关
     * @param int $department_id 员工所在部门
     * @param array $hub_department_ids hub及其子部门
     * @param array $fulfillment_department_ids fulfillment及其子部门
     * @return bool
     */
    public function showRelevanceStoreSwitch($department_id, $hub_department_ids = [], $fulfillment_department_ids = []): bool
    {
        if(in_array($department_id, $hub_department_ids) || in_array($department_id, $fulfillment_department_ids)) {
            return true;
        } else {
            return false;
        }
    }

    //批量创建部门
    public function batch_create_department($params) {
        try {
            $excel_data = $params['excel_data'];
            if (count($excel_data) < self::$batch_create_dept_min_excel_count) {
                return ['code' => ErrCode::$VALIDATE_ERROR, 'msg' => 'Excel error', 'data' => []];
            }

            if (count($excel_data) > self::$batch_create_dept_max_excel_count) {
                return ['code' => ErrCode::$VALIDATE_ERROR, 'msg' => static::$t->_('batch_create_department_error_1'), 'data' => []];//excel数据量不能超过100条，请重新上传
            }

            $department_all = SysDepartmentModel::find([
                'columns' => 'id,name,ancestry,ancestry_v3,type,level,group_boss_id,country_code,manager_id,manager_name,deleted',
                'conditions' => 'deleted = 0',
            ])->toArray();
            $all_department_name_arr = array_column($department_all,'name');

            $excel_department_name_arr = array_map('trim', array_column($excel_data, 0));
            $excel_department_name_count = array_count_values($excel_department_name_arr);
            $ancestry_name_arr = array_map('trim', array_column($excel_data, 1));
            $staff_manager_ids = array_map('trim', array_column($excel_data, 3));

            $staff_list = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, name',
                'conditions' => 'staff_info_id in ({staff_info_ids:array}) and formal IN (1, 4) and state = 1 and is_sub_staff = 0',
                'bind' => [
                    'staff_info_ids' => $staff_manager_ids,
                ],
            ])->toArray();
            $staff_list = array_column($staff_list, null, 'staff_info_id');

            $ancestry_list = SysDepartmentModel::find([
                'columns' => 'id,name,ancestry,ancestry_v3,type,level,group_boss_id,country_code,manager_id,manager_name,deleted',
                'conditions' => 'name in ({name:array}) and deleted = 0',
                'bind' => [
                    'name' => $ancestry_name_arr,
                ],
            ])->toArray();
            $ancestry_list = array_column($ancestry_list, null, 'name');
            $success_count = 0;
            $error_count = 0;
            $dept_pc_code_service = new DeptPcCodeService();

            //获取国家简称
            $data = EnumsService::getInstance()->getCountryListV2();
            $countryCode = array_keys($data['country_list']);

            foreach ($excel_data as $key => $value) {
                $msg = [];
                $department_name = trim($value[0]);//部门名称
                $ancestry_name = trim($value[1]);//上级名称
                $level = trim($value[2]);//级别
                $manager_id = trim($value[3]);//负责人id
                $country_code = trim($value[4]);//国家编码
                $ancestry_info = $ancestry_list[$ancestry_name];

                if ((empty($department_name) && empty($ancestry_name) && empty($level) && empty($manager_id)) && empty($country_code)) {
                    continue;
                }

                if(empty($department_name)) {
                    $msg[] = static::$t->_('batch_create_department_error_2');//'组织名称不能为空';
                } else {
                    if($excel_department_name_count['$department_name'] > 1) {
                        $msg[] = static::$t->_('batch_create_department_error_3');//'组织名称和excel表中的有重复';
                    }
                    if(in_array($department_name, $all_department_name_arr)) {
                        $msg[] = static::$t->_('batch_create_department_error_4');//'组织名称和现有组织架构名称有重复';
                    }
                }

                if(empty($ancestry_name)) {
                    $msg[] = static::$t->_('batch_create_department_error_5');//'上级组织名称不能为空';
                } else {
                    if(!array_key_exists($ancestry_name, $ancestry_list)) {
                        $msg[] = static::$t->_('batch_create_department_error_6');//'上级组织不存在';
                    }
                }

                if(empty($level)) {
                    $msg[] = static::$t->_('batch_create_department_error_7');//'组织级别不能为空';
                } else {
                    if(!in_array($level, [1, 2, 3, 4])) {
                        $msg[] = static::$t->_('batch_create_department_error_8');//'组织级别只能为：1，2，3，4';
                    } else {
                        if(!in_array($ancestry_info['type'],[1, 4, 5]) && $level <= $ancestry_info['level']) {
                            $msg[] = static::$t->_('batch_create_department_error_14');//'组织级别只能为：1，2，3，4';
                        }
                    }
                }

                if(empty($manager_id)) {
                    $msg[] = static::$t->_('batch_create_department_error_9');//'负责人不能为空';
                } else {
                    if(!array_key_exists($manager_id, $staff_list)) {
                        $msg[] = static::$t->_('batch_create_department_error_10');//'负责人工号不正确';
                    }
                }

                if(!empty($country_code)) {
                    if(!in_array($country_code, $countryCode)) {
                        $msg[] = static::$t->_('batch_create_department_error_15', ['country_list' => implode(',', $countryCode)]);//原逻辑：国家只能为：CN,TH,PH,LA,VN,MY,ID;现在通过接口获取
                    }
                }

                if(empty($msg)) {
                    $ancestry_id = $ancestry_info['id'];
                    if($ancestry_info['type'] == 5) {
                        $group_boss_id = $ancestry_info['id'];
                        $department_type = 3;
                    } else if($ancestry_info['type'] == 4) {
                        $group_boss_id = $ancestry_id;
                        $department_type = 3;
                    } else if($ancestry_info['type'] == 3) {
                        $group_boss_id = $ancestry_info['group_boss_id'];
                        $department_type = 3;
                    } else {
                        $group_boss_id = $ancestry_info['group_boss_id'];
                        $department_type = 2;
                    }

                    $api_params = [
                        'department_name' => $department_name,
                        'level'           => $level,
                        'ancestry_id'     => $ancestry_id,
                        'manager_id'      => $manager_id,
                        'type'            => $department_type,
                        'group_boss_id'   => $group_boss_id,
                        'country_code'    => $country_code,
                        'operator_id'     => $params['operator_id'],
                    ];
                    $add_department_result = $this->add_sys_department_v2($api_params);
                    $log_params = [
                        'name' => $department_name,
                        'level'           => $level,
                        'manager_id'      => $manager_id,
                        'type'            => $department_type,
                        'group_boss_id'   => $group_boss_id,
                        'country_code'    => $country_code,
                        'ancestry'        => $ancestry_info['name'] ?? '',
                    ];

                    if($add_department_result['code'] == ErrCode::$SUCCESS) {
                        $success_count++;
                        $msg[] = 'success';
                        $new_dept_id = $add_department_result['department_id'];
                        $this->noticeHcmDepartmentChange($new_dept_id, $params['operator_id'], true);
                        $this->noticeOaDepartmentChange($new_dept_id, $params['operator_id']);
                        $dept_pc_code_service->saveDeptOperateLog($params['operator_id'], $params['operator_name'], [], $log_params, $new_dept_id);
                    } else {
                        $msg[] = $add_department_result['msg'];
                        $error_count++;
                    }
                } else {
                    $error_count++;
                }

                $excel_result[] = [
                    $value[0],
                    $value[1],
                    $value[2],
                    $value[3],
                    $value[4],
                    (empty($msg) ? '' : implode(',', $msg)),
                ];
            }

            $upload_key = 'batch_create_department_result_'.time();
            //导出xsls
            $excel_header = [
                static::$t->_('department.name'),
                static::$t->_('department.parent_name'),
                static::$t->_('department.level'),
                static::$t->_('department.manager_id'),
                static::$t->_('department_country'),
            ];

            $path = self::excelToFile($excel_header, $excel_result, $upload_key . '.xlsx');

            //返回结果
            $result_data = [
                'success_data_count' => $success_count, //校验成功条数
                'error_data_count' => $error_count, //校验失败条数
                'result_file' => $path, //结果详情文件路径
            ];

            //记录批量创建日志
            $batch_create_log_model = new DepartmentBatchCreateLogModel();
            $batch_create_log_model->file_name = $params['file_name'];
            $batch_create_log_model->success_count = $success_count;
            $batch_create_log_model->error_count = $error_count;
            $batch_create_log_model->operator = $params['operator_id'];
            $batch_create_log_model->file_parth = $path['object_url'] ?? '';
            $batch_create_log_model->upload_oss_object = json_encode($path);
            $log_result = $batch_create_log_model->save();

            return [
                'code' => ErrCode::$SUCCESS,
                'msg' => 'success',
                'data' => $result_data,
            ];
        } catch (\Exception $e) {
            $this->logger->error('DepartmentService:addSysDepartmentV2 异常，原因：' . $e->getMessage() . $e->getTraceAsString());
            return [
                'code' => ErrCode::$SYSTEM_ERROR,
                'msg' => 'error',
                'data' => [],
            ];
        }
    }

    //调用ms接口创建部门
    public function add_sys_department_v2($params) {
        try {
            $params['ancestry'] = strval($params['ancestry_id']);
            $params['name']     = $params['department_name'];
            $result             = ThirdApiService::addSysDepartmentV2($params);
            $new_dept_id        = $result['result'];

            $params['id'] = $new_dept_id;
            (new DeptPcCodeService())->savePcCode($params);
            return [
                'code' => ErrCode::$SUCCESS,
                'msg' => 'SUCCESS',
                'department_id' => $new_dept_id,
            ];
        } catch (\Exception $e) {
            $this->logger->error('DepartmentService:addSysDepartmentV2 异常，原因：' . $e->getMessage() . $e->getTraceAsString());
            return [
                'code' => ErrCode::$SYSTEM_ERROR,
                'msg' => $e->getMessage(),
            ];
        }
    }

    //批量创建部门log
    public function get_batch_create_department_log_list($params) {
        $page_size = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset = $page_size * ($page_num-1);

        $builder = $this->modelsManager->createBuilder();
        $list = $builder->columns('*')
                    ->from(DepartmentBatchCreateLogModel::class)
                    ->orderBy('id desc')
                    ->limit($page_size, $offset)
                    ->getQuery()
                    ->execute()
                    ->toArray();
        $count = DepartmentBatchCreateLogModel::count();
        $add_hour = $this->config->application->add_hour;
        foreach ($list as $key => $value) {
            $list[$key]['created_at'] = date('Y-m-d H:i:s',strtotime($value['created_at']) + $add_hour * 3600);
        }

        return [
            'items' => $list,
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => $count,
            ],
        ];
    }

    /**
     * 判断网点是否被其他部门关联
     * @param $department_id
     * @param $relevance_store_id
     * @return array
     */
    public function isRelevanceStore($department_id, $store_id){
        $detail = SysDepartmentModel::findFirst([
            'columns' => 'id,name,ancestry,ancestry_v3,type,level,group_boss_id,country_code,manager_id,manager_name,relevance_store_id,deleted',
            'conditions' => 'id != :id: and relevance_store_id = :relevance_store_id:',
            'bind'=> [
                'id' => $department_id,
                'relevance_store_id' => $store_id,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }


    /**
     * @description:hcm 管辖范围管理通知 -- 部门新增和编辑
     * @param $department_id  部门 id
     * @param $operater  操作人
     * @return     :
     * <AUTHOR> L.J
     * @time       : 2022/6/24 14:41
     */

    public function  noticeHcmDepartmentChange($department_id = 0,$operater = 0, $is_add = false){
        if(empty($department_id) && empty($operater)){
            return false;
        }
        $ac         = new ApiClient('hcm_rpc', '', 'approval_jurisdiction_changes');
        $api_params = [
            [
                'operator_id' => $operater,   //操作者
                'dep_id'      => $department_id,   //部门 id
                'type'        => 1,//操作部门
                'is_add'      => $is_add,
            ],
        ];

        $ac->setParams($api_params);
        return $ac->execute();
    }

    /**
     * @description:  oa 财务审批管辖范围管理通知 -- 部门新增和编辑
     * @param $department_id  部门 id
     * @param $operator  操作人
     * @return bool
     */

    public function noticeOaDepartmentChange($department_id = 0, $operator = 0)
    {
        if (empty($department_id) && empty($operater)) {
            return false;
        }
        $params = [

            'operator_id' => $operator,   //操作者
            'dep_id'      => $department_id,   //部门 id
            'type'        => 1,//操作部门
        ];
        FinancialService::getInstance()->redisListAdd($params);
    }

    /**
     * Notes: 部门选择组件-获取部门（使用by库的部门）
     * @param array $paramIn
     * @return array|array[]
     */
    public function getSubDepartmentById(array $paramIn = []): array
    {
        $id      = $paramIn['id'] ?? 0;
        $depRepo = new DepartmentRepository();
        if (empty($id)) {
            $topDeptInfo = $depRepo->getTopDepartment();
            if (!$topDeptInfo) {
                return [];
            }
            return [
                [
                    'id'            => $topDeptInfo->id,
                    'name'          => $topDeptInfo->name,
                    'isHasChildren' => true,
                ],
            ];
        } else {
            //获取当前id对应的部门信息
            $currentDeptInfo = $depRepo->getDepartmentDetail($id);
            if (empty($currentDeptInfo)) {
                return [];
            }

            //指定id对应的子部门
            $childIdInfo = $depRepo->getAncestryDepartmentsById($id, 'id,name')->toArray();
            $childIds    = array_column($childIdInfo, 'id');
            if (empty($childIds)) {
                return [];
            }
            $childDetail = $depRepo->getAncestryDepartmentsByAncestryId($childIds)->toArray();
            $childDetail = array_column($childDetail, 'ids', 'ancestry');
            foreach ($childIdInfo as &$v) {
                $v['ancestry_name'] = $currentDeptInfo->name ?? '';
                $v['ancestry']      = $id;
                $v['isHasChildren'] = isset($childDetail[$v['id']]);
            }
            return $childIdInfo;
        }
    }

    /**
     * 部门选择组件-搜索部门接口（使用by库的部门）
     * @param array $paramIn
     * @return array
     */
    public function search(array $paramIn = []): array
    {
        $condition = $paramIn['name'] ?? "";
        $is_deleted = isset($paramIn['is_deleted']) ? $paramIn['is_deleted'] : GlobalEnums::IS_NO_DELETED;
        if (empty($condition)) {
            return [];
        }

        $depRepo = new DepartmentRepository();
        if (is_numeric($condition)) {
            $list = $depRepo->getListLikeId($condition, $is_deleted, 'id, name, ancestry,deleted')->toArray();
        } else {
            $list = $depRepo->getListLikeName($condition, $is_deleted, 'id, name, ancestry,deleted')->toArray();
        }
        $ancestry = array_values(array_unique(array_column($list, 'ancestry')));

        if (!empty($ancestry)) {
            $ancestryDetail = $depRepo->getDepByIds($ancestry, 'id,name', $is_deleted)->toArray();
            $ancestryDetail = array_column($ancestryDetail, 'name', 'id');
        }

        foreach ($list as &$v) {
            $v['ancestry_name'] = $ancestryDetail[$v['ancestry']] ?? '';
            $v['isHasChildren'] = false;
        }

        return $list;
    }

    /**
     * 创建组织
     * @param $params
     * @return true
     * @throws BusinessException
     * @throws ValidationException
     */
    public function addGroup($params) {
        $user = $params['user'];
        $name_is_exists = $this->checkDepartmentName($params['name'], $params['id']??0);
        if ($name_is_exists) {
            throw new ValidationException(static::$t->_('department.name_exsits'), ErrCode::$VALIDATE_ERROR);
        }
        //验证负责人工号是否正确
        $manager_id_error = $this->validateStaffInfoId($params['manager_id']);//验证负责人工号
        if ($manager_id_error) {
            throw new ValidationException(static::$t->_($manager_id_error), ErrCode::$VALIDATE_ERROR);
        }
        //验证助理工号是否正确
        if ($params['assistant_id']) {
            $assistant_id_error = $this->validateStaffInfoId($params['assistant_id']);//验证助理工号
            if ($assistant_id_error) {
                throw new ValidationException(static::$t->_($assistant_id_error), ErrCode::$VALIDATE_ERROR);
            }
        }
        $params['operator_id'] = $user['id'];
        $sync_result = ThirdApiService::addSysGroupBoss($params);

        $new_id = $sync_result['result'];
        //同步pc code 信息
        (new DeptPcCodeService())->savePcCode([
            'id'                     => $new_id,
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
        ]);
        $after = $this->getDepartmentDetailByFle($new_id);
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], [], $after, $new_id);
        return true;
    }

    /**
     * 编辑组织
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function editGroup($params): bool
    {
        $user = $params['user'];
        $name_is_exists     = $this->checkDepartmentName($params['name'], $params['id']);
        if ($name_is_exists) {
            throw new ValidationException(static::$t->_('department.name_exsits'), ErrCode::$VALIDATE_ERROR);
        }
        if($params['edit_type'] == self::$organization_edit_info) {
            //验证负责人工号是否正确
            $manager_id_error = $this->validateStaffInfoId($params['manager_id']);//验证负责人工号
            if ($manager_id_error) {
                throw new ValidationException(static::$t->_($manager_id_error), ErrCode::$VALIDATE_ERROR);
            }
            //验证助理工号是否正确
            if ($params['assistant_id']) {
                $assistant_id_error = $this->validateStaffInfoId($params['assistant_id']);//验证助理工号
                if ($assistant_id_error) {
                    throw new ValidationException(static::$t->_($assistant_id_error), ErrCode::$VALIDATE_ERROR);
                }
            }
        }

        $before = $this->getDepartmentDetail($params['id']);
        //编辑组织
        $params['operator_id'] = $user['id'];
        ThirdApiService::updateSysGroupBoss($params);

        (new DeptPcCodeService())->savePcCode([
            'id'                     => $params['id'],            //组织ID
            'name'                   => $params['name'] ?? '',    //组织名称
            'manager_id'             => $params['manager_id'],    //负责人ID
            'sap_company_id'         => (string)$params['sap_company_id'],
            'sap_reporting_currency' => (string)$params['sap_reporting_currency'],
            'sap_describe'           => (string)$params['sap_describe'],
            'sap_cost_center'        => (string)$params['sap_cost_center'],
            'sap_company_address'    => (string)$params['sap_company_address'],
            'sap_tax_id'             => (string)$params['sap_tax_id'],
            'country_code'           => (string)$params['country_code'] ?? '',
            'kingdee_cost_center'    => (string)$params['kingdee_cost_center'] ?? '',
        ]);
        $after = $this->getDepartmentDetailByFle($params['id']);
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $before, $after, $params['id']);
        return true;
    }

    /**
     * 创建组织(c_level/bu/department)
     * @param $params
     * @return true
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createOrganization($params) {
        $user = $params['user'];
        $ancestry = trim($params['ancestry']);
        if ($params['type'] == 2 && (!isset($params['company_id']) || empty($params['company_id']))) {
            throw new ValidationException('company id error', ErrCode::$VALIDATE_ERROR);
        }

        //验证部门是否有重复
        if (!empty($this->checkDepartmentName($params['department_name'], 0))) {
            throw new ValidationException(static::$t->_('department.name_exsits'), ErrCode::$VALIDATE_ERROR);
        }

        $ancestry_detail = $this->getDepartmentDetail($ancestry);
        if (in_array($ancestry_detail['type'], [
            OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE,
            OrganizationDepartmentEnums::ORGANIZATION_GROUP_CEO_TYPE,
        ])) {
            $params['group_boss_id'] = $ancestry;
        }

        $params['name']        = (string)$params['department_name'];   //部门名称
        $params['operator_id'] = $user['id'];
        $return                = ThirdApiService::addSysDepartmentV2($params);
        $manager_id            = (int)$params['manager_id'];
        $new_id                = $return['result'];
        $params['id']          = $new_id;
        (new DeptPcCodeService())->savePcCode($params);
        $after = $this->getDepartmentDetail($new_id);
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], [], $after, $new_id);

        //给部门负责人增加人才盘点菜单
        (new TalentReviewPermissionsService())->addDepartmentPermission($manager_id, $new_id);
        //调用 hcm rpc  告知部门变更
        $this->noticeHcmDepartmentChange($new_id, $user['id'], true);
        $this->noticeOaDepartmentChange($new_id, $user['id']);
        return true;
    }

    /**
     * 编辑组织(c_level/bu/department)
     * @param $params
     * @return array|true
     * @throws ValidationException|BusinessException
     */
    public function updateOrganization($params)
    {
        $user               = $params['user'];
        $department_id      = $params['department_id'];
        $ancestry           = (string)$params['ancestry'];
        $department_name    = $params['department_name'];
        $department_type    = $params['type'];
        $manager_id         = $params['manager_id'];
        $confirm            = $params['confirm'] ?? 0;
        $relevance_store_id = $params['relevance_store_id'] ?? '';

        $department_detail = $this->getDepartmentDetail($department_id);
        $department_ancestry_is_change = false;//部门上级组织是否变更，默认为false
        if($params['edit_type'] == self::$organization_edit_info) {
            //验证部门是否有重复
            if (!empty($this->checkDepartmentName($department_name, $department_id))) {
                throw new ValidationException(static::$t->_('department.name_exsits'), ErrCode::$VALIDATE_ERROR);
            }

            if (empty($department_detail)) {
                throw new ValidationException('Department Id error', ErrCode::$VALIDATE_ERROR);
            }

            //如果变动上级组织，则判断当前部门是否可以移动部门（有子部门的部门不可以选择上级部门）
            if (($department_type == $department_detail['type'] && $ancestry != $department_detail['ancestry']) || ($department_type != $department_detail['type'])) {
                $department_ancestry_is_change = true;
            }
            if ($department_ancestry_is_change && $this->hasChildDepartment($department_id)) {
                throw new ValidationException("Current Department Can't remove", ErrCode::$VALIDATE_ERROR);
            }
            if ($ancestry > 0 && in_array($department_type, [OrganizationDepartmentEnums::ORGANIZATION_BU_DEPARTMENT_TYPE,OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_DEPARTMENT_TYPE])) { //下级部门组织等级必须大于上级组织等级
                $ancestry_detail = $this->getDepartmentDetail($ancestry);
                $ancestry_level  = $ancestry_detail['level'];
                if (in_array($ancestry_detail['type'],[OrganizationDepartmentEnums::ORGANIZATION_BU_DEPARTMENT_TYPE,OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_DEPARTMENT_TYPE]) &&  $params['level'] <= $ancestry_level) {
                    throw new ValidationException("department level not allowed!", ErrCode::$VALIDATE_ERROR);
                }
            }
            //验证工号是否正确
            $manager_id_error = $this->validateStaffInfoId($params['manager_id']);
            if ($manager_id_error) {
                throw new ValidationException(static::$t->_($manager_id_error), ErrCode::$VALIDATE_ERROR);
            }
        }

        $show_manager_position_department_list = $this->getShowRelevanceStoreDepartment();
        $show_manager_position_department_ids = array_column($show_manager_position_department_list, 'id');

        //如果是hub或者fulfillment部门的话 同步网点负责人变更
        if(in_array($department_id, $show_manager_position_department_ids) && !empty($relevance_store_id)) {
            //判断该网点是否被其他部门关联
            $relevance_result = $this->isRelevanceStore($department_id, $relevance_store_id);
            if(!empty($relevance_result)) {
                throw new ValidationException(static::$t->_('batch_create_department_error_13'), ErrCode::$VALIDATE_ERROR);
            }
        }

        $countryCode = strtoupper(env('country_code', GlobalEnums::TH_COUNTRY_CODE));
        switch ($countryCode) {
            case GlobalEnums::PH_COUNTRY_CODE:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_PH;
                break;
            case GlobalEnums::MY_COUNTRY_CODE:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_MY;
                break;
            case GlobalEnums::VN_COUNTRY_CODE:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_VN;
                break;
            case GlobalEnums::LA_COUNTRY_CODE:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_LA;
                break;
            case GlobalEnums::ID_COUNTRY_CODE:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE_ID;
                break;
            default:
                $show_manager_position_department_ids = self::SHOW_DEPARTMENT_MANAGER_POSITION_STATE;
        }
        $manager_position_state = $params['manager_position_state'] ?? null;

        $childDepartmentIds        = (new DepartmentRepository())->getManyDepartmentSonList($show_manager_position_department_ids);

        $show_manager_position_department_ids = DepartmentRelationService::getInstance()->getRegionRelationByDeptIds($childDepartmentIds);

        if(!empty($manager_id) && in_array($countryCode, self::$nw_manager_country_list) && in_array($department_id, $show_manager_position_department_ids) && $manager_position_state == 1 && $confirm != 1) {
            //验证正职负责人是否是有多个
            $validate_result = $this->validate_manager_position_state(['department_id' => $department_id, 'manager_id' => $manager_id]);
            if($validate_result['code'] == 0) {
                $msg = static::$t->_('department_manager_position_msg') . "<br/>" . $validate_result['msg'];
                return ['code' => ErrCode::$SUCCESS, 'msg' => $msg, 'data' => ['confirm' => 1]];
            }
        }
        $before_manager_id = $department_detail['manager_id'];

        if (in_array($department_detail['type'], [
                OrganizationDepartmentEnums::ORGANIZATION_BU_DEPARTMENT_TYPE,
                OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_DEPARTMENT_TYPE,
            ]) && $ancestry != $department_detail['ancestry']) {
            $params['kingdee_cost_center'] = '';
        }

        $params['operator_id'] = $user['id'];
        ThirdApiService::updateSysDepartmentV2($params);

        $after          = $this->getDepartmentDetailByFle($department_id);
        $params['id']   = (string)$params['department_id'];   //部门ID
        $params['name'] = (string)$params['department_name']; //部门名称
        (new DeptPcCodeService())->savePcCode($params);
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $department_detail, $after,
            $department_id);

        //如果上级组织有变动，且 是二级->一级或 一级->二级，则同步hris接口做员工部门变更逻辑
        if ($department_ancestry_is_change) {
            $msg_res = $this->department_level_move_add_to_mns($ancestry, $department_id);
        }

        //同步直线上级变更
        if(in_array($countryCode, self::$nw_manager_country_list) && in_array($department_id, $show_manager_position_department_ids) && $params['manager_id'] != $department_detail['manager_id']) {
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'department_id' => $department_id,
                    'manager_id'  => $manager_id,
                    'operator_id' => $user['id'],
                    'update_type' => 1,
                ],
            ]);
            $res = $hris_ac->execute();
        }
        //如果设置当前组织为正职，则其他负责人都变更成代理
        if(in_array($countryCode, self::$nw_manager_country_list) && in_array($department_id, $show_manager_position_department_ids) && $manager_position_state == 1) {
            $update_department_param = [
                'manager_id' => $manager_id,
                'user' => $user,
                'department_id' => $department_id,
            ];
            $this->updateOtherOrganizationManagerPositionState($update_department_param);
            $region_param = [
                'region_id' => '',
                'manager_id' => $manager_id,
                'user' => $user,
            ];
            (new SysManageRegionPieceService())->updateManagerRegionPositionState($region_param);
            $piece_param = [
                'piece_id' => '',
                'manager_id' => $manager_id,
                'user' => $user,
            ];
            (new SysManageRegionPieceService())->updateManagerPiecePositionState($piece_param);
            $store_param = [
                'store_id' => '',
                'manager_id' => $manager_id,
                'user' => $user,
            ];
            (new StoreService())->updateStorePositionState($store_param);
        }

        if($before_manager_id != $manager_id) {
            //给部门负责人增加人才盘点菜单
            $talent_review_permissions_service = new TalentReviewPermissionsService();
            $talent_review_permissions_service->addDepartmentPermission($manager_id, $department_id);
            $talent_review_permissions_service->delDepartmentPermission($before_manager_id, $department_id);
        }
        //调用 hcm rpc  告知部门变更
        $this->noticeHcmDepartmentChange($department_id, $user['id']);
        //oa 告知部门变更
        $this->noticeOaDepartmentChange($department_id, $user['id']);

        return true;
    }

    /**
     * 清空下级金蝶成本中心字段
     * @param $params
     * @return bool
     */
    public function departmentKingdeeCostCenterClear($params): bool
    {
        $department_id = $params['department_id'];
        $department    = $this->getDepartmentDetailByFle($department_id);
        $user          = $params['user'];

        if ($department_id == GlobalEnums::TOP_DEPARTMENT_ID) {
            $ancestry_v3 = GlobalEnums::TOP_DEPARTMENT_ID;
        } else {
            $ancestry_v3 = $department['ancestry_v3'] ?? '';
        }

        if (empty($department) || empty($ancestry_v3)) {
            return false;
        }

        $child_list = SysDepartmentModelFle::find([
            'conditions' => 'deleted = 0 and ancestry_v3 like :ancestry_v3:',
            'bind'       => [
                'ancestry_v3' => $ancestry_v3 . '/%',
            ],
        ])->toArray();

        $pc_code_service = new DeptPcCodeService();
        foreach ($child_list as $key => $value) {
            try {
                $api_params = [
                    'department_id'          => (string)$value['id'],     //部门ID
                    'department_name'        => (string)$value['name'],   //部门名称
                    'manager_id'             => (int)$value['manager_id'],//负责人ID
                    'ancestry'               => (string)$value['ancestry'],
                    'type'                   => (int)$value['type'],  //类型
                    'level'                  => (int)$value['level'], //层级
                    'group_boss_id'          => (string)$value['group_boss_id'],
                    'sap_company_id'         => (string)$value['sap_company_id'],
                    'sap_reporting_currency' => (string)$value['sap_reporting_currency'],
                    'sap_describe'           => (string)$value['sap_describe'],
                    'sap_cost_center'        => (string)$value['sap_cost_center'],
                    'sap_company_address'    => (string)$value['sap_company_address'],
                    'sap_tax_id'             => (string)$value['sap_tax_id'],
                    'country_code'           => (string)$value['country_code'] ?? '',
                    'manager_position_state' => $value['manager_position_state'],
                    'kingdee_cost_center'    => '',
                    'operator_id'            => $user['id'],
                ];
                ThirdApiService::updateSysDepartmentV2($api_params);
                if (isset($return['error'])) {
                    $this->logger->error(['function' => 'departmentKingdeeCostCenterClear', 'request_params' => $api_params, 'result' => $return, 'params' => $params]);
                } else {
                    $after = $this->getDepartmentDetailByFle($value['id']);
                    $save_pc_code_result     = $pc_code_service->savePcCode($api_params[0]);
                    $save_operate_log_result = $pc_code_service->saveDeptOperateLog($user['id'], $user['name'], $value, $after, $value['id']);
                    $this->logger->info([
                        'function'                => 'departmentKingdeeCostCenterClear',
                        'params'                  => $params,
                        'request_params'          => $api_params,
                        'result'                  => $return,
                        'save_pc_code_result'     => $save_pc_code_result,
                        'save_operate_log_result' => $save_operate_log_result,
                    ]);
                }
            } catch (\Exception $e) {
                $this->logger->error([
                    'function' => 'departmentKingdeeCostCenterClear',
                    'params'   => $params,
                    'value'    => $value,
                    'message'  => $e->getMessage(),
                    'file'     => $e->getFile(),
                    'line'     => $e->getLine(),
                ]);
            }
        }
        return true;
    }

    /**
     * 部门删除
     * @param $params
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function DepartmentDel($params): bool
    {
        $department_id = $params['department_id'];
        $type = $params['type'];
        $user = $params['user'];
        $department_detail = SysDepartmentModel::findFirst([
            'conditions' => 'id = :id: and type in({type:array}) and deleted = :deleted:',
            'bind' => [
                'id' => $department_id,
                'type' => $type,
                'deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ]);

        if (empty($department_detail)) {
            //未找到指定部门信息
            throw new ValidationException("Department ID Error", ErrCode::$VALIDATE_ERROR);
        }

        $has_children = $this->hasChildDepartment($department_id);
        if ($has_children) {
            //有子部门 不能删除
            throw new ValidationException("Current Department have children  Can't delete", ErrCode::$VALIDATE_ERROR);
        }

        //部门下有大区片区网点关联也不能删除
        $departmentRelation = DepartmentRelationService::getInstance();
        $children_department = $departmentRelation->getDepartmenthasChildren([$department_id]);
        if(!empty($children_department)) {
            //有子部门 不能删除
            throw new ValidationException("There are subordinate organizations Can't delete", ErrCode::$VALIDATE_ERROR);
        }

        $work_count = $this->getDepartmentWorkCountById($department_id);
        if($work_count > 0) {
            //有在职人数 不能删除
            throw new ValidationException(static::$t->_('err_msg_can_not_del_department'), ErrCode::$VALIDATE_ERROR);
        }

        //招聘中人数 、待入职人数 、 已提交的HC人数
        $hcSurplusCount      = BudgetService::getInstance()->getSurplusCount($department_id);
        $hcPendingEntryCount = BudgetService::getInstance()->getPendingEntryCount($department_id);
        $hcBudgetAdoptCount  = BudgetService::getInstance()->getBudgetAdoptCount($department_id);

        if (BudgetService::getInstance()->isConfiguredBudget()) {
            $transferEntry       = BudgetService::getInstance()->getJobTransferEntryCount($department_id);
            if ($hcSurplusCount > 0 || $hcPendingEntryCount > 0 || $hcBudgetAdoptCount > 0 || $transferEntry > 0) {
                //有招聘流程中的HC，请联系TA处理后再操作
                throw new ValidationException(static::$t->_('err_msg.origanization_exist_hc'), ErrCode::$VALIDATE_ERROR);
            }
        } else {
            //不走新版预算计算方式
            if ($hcSurplusCount > 0 || $hcPendingEntryCount > 0 || $hcBudgetAdoptCount > 0) {
                //有招聘流程中的HC，请联系TA处理后再操作
                throw new ValidationException(static::$t->_('err_msg.origanization_exist_hc'), ErrCode::$VALIDATE_ERROR);
            }
        }

        ThirdApiService::deleteSysDepartment($department_id,$user['id']);

        //删除同步hc
        //需求18329 在删除部门前校验是否存在HC，不再在删除部门时强制删除HC
        //https://flashexpress.feishu.cn/docx/HqlSdfGeLo1eJ0xPMQ7cTQBJnkd
        if (!BudgetService::getInstance()->isConfiguredBudget()) {
            $ac = new ApiClient('hr_rpc', '', 'deleteDepartment');
            $ac->setParams([$department_id]);
            $sync_hc_result = $ac->execute();
            if (isset($sync_hc_result['error'])) {
                throw new BusinessException($sync_hc_result['error'], ErrCode::$BUSINESS_ERROR);
            }
        }

        //记录log
        $after = $this->getDepartmentDetailByFle($department_id);
        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $department_detail, $after, $department_id);

        return true;
    }

    /**
     * 验证是否可以删除
     * @param $params
     * @return bool
     */
    public function isDelDepartment($params): bool
    {
        $department_id = $params['department_id'];//部门id

        //有在职人数 不能删除
        $work_count = $this->getDepartmentWorkCountById($department_id);
        if($work_count > 0) {
            return false;
        }

        //有子部门 不能删除
        $has_children = $this->hasChildDepartment($department_id);
        if ($has_children) {
            return false;
        }

        //部门下有大区片区网点关联也不能删除
        $departmentRelation = DepartmentRelationService::getInstance();
        $children_department = $departmentRelation->getDepartmenthasChildren([$department_id]);
        if(!empty($children_department)) {
            return false;
        }

        return true;
    }

    /**
     * 获取部门信息 读fle库
     * @param $department_id
     * @return array
     */
    public function getDepartmentDetailByFle($department_id): array
    {
        $detail = SysDepartmentModelFle::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $department_id,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 验证工号是否正确，错误直接返回错误提示
     * @param $staff_id
     * @param $hr_staff_info
     * @return string
     */
    public function validateStaffInfoId($staff_id, &$hr_staff_info = [])
    {
        //验证工号是否正确
        $hr_service    = new HrStaffInfoService();
        $hr_staff_info = $hr_service->getHrStaffInfo([$staff_id]);
        if (empty($hr_staff_info)) {
            return 'department.staff_id_error1';
        }

        if ($hr_staff_info[$staff_id]['state'] != 1) {
            return 'department.staff_id_error2';
        }

        if ($hr_staff_info[$staff_id]['formal'] != 1) {
            return 'department.staff_id_error3';
        }

        return '';
    }

    /**
     * 验证工号是否是指定部门的负责人或者助理
     * @param $department_id
     * @param $staff_info_id
     * @return bool
     */
    public function validateDepartmentManager($department_id, $staff_info_id): bool
    {
        //查找工号负责的部门及子部门，如果当前部门在 负责的部门子部门的话 返回true
        $dep_list_arr = SysDepartmentModel::find([
            'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and deleted = :deleted:',
            'columns'    => ['id', 'ancestry_v3'],
            'bind'       => [
                'deleted'      => GlobalEnums::IS_NO_DELETED,
                'manager_id'   => $staff_info_id,
                'assistant_id' => $staff_info_id,
            ],
        ])->toArray();

        $staff_info_department_ids = [];
        if(!empty($dep_list_arr)) {
            foreach ($dep_list_arr as $k => $v) {
                $department_List = SysDepartmentModel::find([
                    'conditions' => '(ancestry_v3 like :chain: or ancestry_v3 = :chain_id:) and deleted = :deleted:',
                    'bind'       => [
                        'chain'    => $v['ancestry_v3'].'/%',
                        'chain_id' => $v['ancestry_v3'],
                        'deleted'  => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'id',
                ])->toArray();

                $staff_info_department_ids = array_merge($staff_info_department_ids, array_column($department_List, 'id'));
            }
            $staff_info_department_ids = array_merge(array_column($dep_list_arr, 'id'), $staff_info_department_ids);
        }

        return in_array($department_id, $staff_info_department_ids);
    }

    /**
     * 获取编辑权限配置
     * @param $staff_info_id
     * @return array
     */
    public function getOrganizationEditPermission($staff_info_id): array
    {
        $setting_env_model = new SettingEnvModel();
        $organization_edit_info_roles_id         = $setting_env_model->getValByCode('organization_edit_info_roles_id');
        $organization_edit_info_roles_id_arr     = !empty($organization_edit_info_roles_id) ? explode(',', $organization_edit_info_roles_id) : [];
        $organization_edit_sap_info_roles_id     = $setting_env_model->getValByCode('organization_edit_sap_info_roles_id');
        $organization_edit_sap_info_roles_id_arr = !empty($organization_edit_sap_info_roles_id) ? explode(',', $organization_edit_sap_info_roles_id) : [];

        $positions      = (new HrStaffRepository())->getStaffRoleList($staff_info_id);
        $staff_position = array_column($positions, 'position_category');

        return [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position,
        ];
    }

    /**
     * 根据部门ID获取关联网点
     * @param array $department_ids
     * @return array
     */
    public function getRelevanceStoreByDepartmentId(array $department_ids)
    {
        if (empty($department_ids) || !is_array($department_ids)) {
            return [];
        }
        $manageStoreIds = SysDepartmentModel::find([
            'conditions' => 'relevance_store_id != \'\' and id in({ids:array})',
            'bind' => [
                'ids' => $department_ids
            ],
        ])->toArray();
        return array_column($manageStoreIds, 'relevance_store_id');
    }
}
