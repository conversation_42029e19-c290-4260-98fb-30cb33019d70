<?php
/**
 * 职位体系-职位列表
 */

namespace app\modules\Organization\services;

use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobTitleByModel;
use App\Modules\Organization\Models\SysDepartmentByModel;
use App\Modules\Organization\Services\JobJdService as JobJdService;
use GuzzleHttp\Exception\GuzzleException;

class JobTitleService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 职位列表
     * @param $params
     * @return array
     */
    public function getList($params): array
    {
        $pageSize = empty($params['pageSize']) ? 20 : $params['pageSize'];
        $pageNum  = empty($params['pageNum']) ? 1 : $params['pageNum'];
        $offset   = $pageSize * ($pageNum - 1);

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => (int) $pageNum,
                'per_page'     => (int) $pageSize,
                'total_count'  => 0,
            ],
        ];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => HrJobTitleByModel::class]);
        $builder->columns('count(id) as total');

        //组合搜索条件
        if (!empty($params['job_id'])) {
            $builder->andWhere('id = :id:', ['id' => $params['job_id']]);
        }

        if (!empty($params['status'])) {
            $builder->andWhere('status = :status:', ['status' => $params['status']]);
        }

        $count = (int) $builder->getQuery()->getSingleResult()->total;

        if (!$count) {
            return $data;
        }

        $builder->columns([
            'id as job_id',
            'job_name as job_name',
            'status',
            'create_at',
            'updated_at',
        ]);

        if (empty($params['is_export'])) {
            $builder->limit($pageSize, $offset);
        }

        $builder->orderBy('id DESC');
        $items = $builder->getQuery()->execute()->toArray();

        $data['items']                     = $this->formatData($items);
        $data['pagination']['total_count'] = $count;

        return $data;
    }

    /**
     * 导出职位列表数据
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function exportList($params, $userInfo): array
    {
        $data = $this->getList(array_merge($params, ['is_export' => true]));

        $data = $data['items'] ?? [];

        $header = [
            static::$t->_('job_id'),                               //职位id
            static::$t->_('job_name'),                             //职位名称
            static::$t->_('organization_department_nums'),         //关联部门数量
            static::$t->_('organization_staff_nums'),              //员工数量
            static::$t->_('job_status'),                           //职位状态
            static::$t->_('create_time'),                          //创建时间
            static::$t->_('update_time'),                          //最后更新时间
        ];


        $newData = [];

        foreach ($data as $val) {
            $newData[] = [
                $val['job_id'],
                $val['job_name'],
                $val['department_nums'],
                $val['staff_nums'],
                $val['status_name'],
                $val['create_at'],
                $val['updated_at'],
            ];
        }

        return $this->exportExcel($header, $newData, "job-title-list-" . $userInfo['id'] . '-' . date('YmdHis'));
    }


    /**
     * 格式化数据
     * @param $data
     * @return mixed
     */
    public function formatData($data)
    {
        $jobIds = array_column($data, 'job_id');

        $staffNums = $departmentNums = [];
        if (!empty($jobIds)) {
            $staffNums      = JobJdService::getInstance()->getStaffNums($jobIds);
            $staffNums      = array_column($staffNums, 'num', 'job_title');
            $departmentNums = $this->getDepartmentNums($jobIds);
        }

        foreach ($data as &$v) {
            $v['updated_at']      = show_time_zone($v['updated_at']);
            $v['create_at']       = show_time_zone($v['create_at']);
            $v['staff_nums']      = intval($staffNums[$v['job_id']] ?? 0);
            $v['department_nums'] = intval($departmentNums[$v['job_id']] ?? 0);
            $v['status_name']     = self::$t->_('job_title_status_name_' . $v['status']);
        }

        return $data;
    }


    /**
     * 获取指定职位的在职人数
     * @param array $jobIds
     * @return array
     */
    public function getDepartmentNums(array $jobIds)
    {
        if (empty($jobIds)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('count(relation.id) as num,job_id');
        $builder->from(['relation' => HrJobDepartmentRelationModel::class]);
        $builder->leftJoin(SysDepartmentByModel::class, 'department.id = relation.department_id', 'department');
        $builder->andWhere('department.deleted = :department_deleted:',
            ['department_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('relation.job_id IN ({job_ids:array})', ['job_ids' => $jobIds]);
        $builder->groupBy('relation.job_id');

        $data = $builder->getQuery()->execute()->toArray();

        return $data ? array_column($data, 'num','job_id') : [];
    }
}