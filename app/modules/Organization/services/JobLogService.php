<?php
/**
 * 职位体系-变更记录
 */

namespace App\Modules\Organization\Services;


use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobLogModel;


class JobLogService extends BaseService
{
    private static $instance;

    //记录日志字段
    public static $log_field_data = ['job_name', 'department_name'];
    //类型 翻译
    public static $log_type_data = [
        '1' => 'job_log_data_type_1',
        '2' => 'job_log_data_type_2',
        '3' => 'job_log_data_type_3',
        '4' => 'job_log_data_type_4',
        '5' => 'job_log_data_type_5',
        '6' => 'job_log_data_type_6',
    ];

    //列表
    public static $validate_bind_log_list = [
        'job_id'   => 'IntGt:0|>>>:job_id error',  //职位ID
        'staff_id' => 'IntGt:0',//工号
    ];

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @description 记录日志
     */
    public function saveLog ($params)
    {
        $log_data       = $params['log_data'] ?? [];
        $save_data      = $params['save_data'] ?? [];
        $type           = $params['type'] ?? 0;
        $hr_log_id      = $params['hr_log_id'] ?? 0;
        $user           = $params['user'] ?? [];
        $hr_job_id      = $params['hr_job_id'] ?? 0;
        $log_field_data = $params['log_field_data'] ?? null;
        $department_id  = $params['department_id'] ?? 0;
        try {
            //判断是否记录日志
            if (empty($log_field_data)) {
                $log_field_data_list = self::$log_field_data;
            } else {
                $log_field_data_list = $log_field_data;
            }
            $log_data_log   = [];
            $save_data_log  = [];
            foreach ($log_field_data_list as $item) {
	            $log_data[$item] = $log_data[$item] ?? null;
	            $save_data[$item] = $save_data[$item] ?? null;
                if ($log_data[$item] !== $save_data[$item]) {
                    $log_data_log[$item]  = !is_null($log_data[$item]) ? $log_data[$item] : '';
                    $save_data_log[$item] = !is_null($save_data[$item]) ? $save_data[$item] : '';
                }
            }


            if ($log_data_log) {
                $log_data = [
                    'hr_job_d_r_id'   => $hr_log_id,
                    'type'            => $type,
                    'log_data'        => json_encode($log_data_log),
                    'save_data'       => json_encode($save_data_log, JSON_UNESCAPED_UNICODE),
                    'created_at'      => date('Y-m-d H:i:s'),
                    'hr_job_id'       => $hr_job_id,
                    'created_uid'     => $user['id'],
                    'staff_info_name' => $user['name'],
                    'department_id'   => $department_id ?? 0,
                ];

                $jobModel = new HrJobLogModel();
                if ($jobModel->save($log_data) == false) {
                    $messages = $jobModel->getMessages();
                    $save_err = '';
                    foreach ($messages as $message) {
                        $save_err .= $message . PHP_EOL;
                    }
                    throw new \Exception('变更记录失败，sql错误信息： ' . $save_err);
                }
            }

        } catch (\Exception $e) {
            $real_message = $e->getMessage();
            $logger       = $this->getDI()->get('logger');
            $logger->warning('职位体系-变更记录-saveLog-异常信息: ' . $real_message);

            return false;
        }

        return true;
    }


    /**
     * 变更记录列表
     * @param array $params
     * @return array
     */
    public function bindJobLogList(array $condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $user = $condition['user'];
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];
        try {
            //todo 获取变更记录列表
            $builder = $this->modelsManager->createBuilder();
            $builder->from(HrJobLogModel::class);
            //组合搜索条件
            if (isset($condition['job_id']) && $condition['job_id']) {
                $builder->andWhere('hr_job_id = :hr_job_id:', ['hr_job_id' => $condition['job_id']]);

            }
            //组合搜索条件
            if (isset($condition['staff_id']) && $condition['staff_id']) {
                $builder->andWhere('created_uid = :created_uid:', ['created_uid' => $condition['staff_id']]);
            }

            //组合搜索条件
            $search_department_ids = [];
            if (isset($condition['department_id']) && $condition['department_id']) {
                $search_department_ids[] = $condition['department_id'];
            }

            //获取我管辖的部门
            $departmentRelation = DepartmentRelationService::getInstance();
            $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($user['id'], false, false, true);
            //无权限直接返回
            if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
                $builder->andWhere('department_id = :department_id:', ['department_id' => OrganizationDepartmentEnums::NO_DEPARTMENT]);
            } else if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
                //合并，取交集
                if (!empty($search_department_ids)) {
                    $department_ids = array_intersect($dominion_department_ids, $search_department_ids);
                } else {
                    $department_ids = $dominion_department_ids;
                }

                if (empty($department_ids)) {
                    $builder->andWhere('department_id = :department_id:', ['department_id' => 0]);
                } else {
                    $builder->inWhere('department_id', array_values($department_ids));
                }
            } else {
                if (!empty($condition['department_id'])) {
                    $builder->inWhere('department_id', $search_department_ids);
                }
            }

            $count = $builder->getQuery()->execute()->count();

            if ($count > 0) {
                $builder->orderBy('created_at desc');
                $builder->limit($page_size, $offset);

                $items = $builder->getQuery()->execute()->toArray();
                //todo 构建列表返回字段
                $items = $this->buildJobBindList($items);
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '-line:' . $e->getLine();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('职位体系-bindJobLogList-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 处理 列表字段
     * @param $job_log_bind_list
     */
    private function buildJobBindList($job_log_bind_list)
    {
        $log_type_data = self::$log_type_data;
        $return_data   = [];

        $job_ids = array_column($job_log_bind_list, 'hr_job_id');
        $department_ids = array_column($job_log_bind_list, 'department_id');

        //获取部门列表
        $department_list = (new DepartmentService())->getDepartmentInfoByIds($department_ids);
        $department_list = array_column($department_list, 'name', 'id');

        //职位列表
        $job_list = (new HrJobTitleService())->getJobInfoByIds($job_ids);
        $job_list = array_column($job_list, 'job_name', 'id');

        foreach ($job_log_bind_list as $item) {
            $log_data      = json_decode($item['log_data'], true);
            $save_data     = json_decode($item['save_data'], true);
            $remarks_text = '';
            $type_text = $item['type'] == 1 ? static::$t->_('job_log_type_add') : static::$t->_('job_log_type_save');
            if(in_array($item['type'], [HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC, HrJobLogModel::JOB_LOG_TYPE_NEW_RELATION, HrJobLogModel::JOB_LOG_TYPE_EDIT_RELATION])) {
                $department = static::$t->_('job.department_name') . ':' . $department_list[$item['department_id']] ?? '';
                $job = static::$t->_('hc_job_title') . ':' . $job_list[$item['hr_job_id']] ?? '';
                $remarks_text .= $department . '  ' . $job . '  ' . $type_text  . ':' . $this->splicingStr($log_data, $save_data);
            } else {
                $remarks_text .= $type_text . ':' . $this->splicingStr($log_data, $save_data);
            }

            $return_data[] = [
                'type_text' => static::$t->_($log_type_data[$item['type']]),
                'date_text' => static::$t->_('job_log_date') . ':' . date('Y-m-d H:i:s', strtotime($item['created_at'])),
                'created_user' => static::$t->_('job_log_save_user') . ':' . $item['created_uid'] . ' ' . $item['staff_info_name'],
                'remarks_text' => $remarks_text
            ];
        }
        return $return_data;
    }

    /**
     * 拼接日志信息
     * @param $log_data
     * @param $save_log
     * @return string
     */
    private function splicingStr($log_data, $save_log)
    {
        $str = '';
        foreach ($log_data as $key => $val) {
            if ($key == 'working_day_rest_type') {
                $before_working_day_rest_type = $this->convert_working_day_rest_type_arr($log_data['working_day_rest_type']);
                $after_working_day_rest_type  = $this->convert_working_day_rest_type_arr($save_log['working_day_rest_type']);

                $str .= ($before_working_day_rest_type != '' ? $before_working_day_rest_type : '``') . ' -> ';
                $str .= $after_working_day_rest_type != '' ? $after_working_day_rest_type : '``';
                $str .= '  ';
            }elseif ($key == 'position_type') {
                    $before_position_type_text = $this->getPositionTypeText($log_data['position_type']);
                    $after_position_type_text  = $this->getPositionTypeText($save_log['position_type']);
                    $str .= ' ' . static::$t->_('position_type') . ' : ';
                    $str .= ($before_position_type_text != '' ? $before_position_type_text : '``') . ' -> ';
                    $str .= $after_position_type_text != '' ? $after_position_type_text : '``';
                    $str .= '  ';

            }elseif ($key == 'cost_type') {
                $before_cost_type_text = $this->getCostTypeText($log_data['cost_type']);
                $after_cost_type_text  = $this->getCostTypeText($save_log['cost_type']);
                $str .= ' ' . static::$t->_('cost_type') . ' : ';
                $str .= ($before_cost_type_text != '' ? $before_cost_type_text : '``') . ' -> ';
                $str .= $after_cost_type_text != '' ? $after_cost_type_text : '``';
                $str .= '  ';

            } else {
                $str .= ($log_data[$key] != '' || is_numeric($log_data[$key]) ? $log_data[$key] : '``') . ' -> ';
                $str .= $save_log[$key] != '' || is_numeric($save_log[$key]) ? $save_log[$key] : '``';
                $str .= '  ';
            }
        }
        return $str;
    }

    //职位性质
    public function getPositionTypeText($type)
    {
        if(!isset(HrJobDepartmentRelationModel::$position_type_list[$type])) {
            return '';
        }
        return static::$t->_('position_type_' . $type);
    }

    //成本类型
    public function getCostTypeText($type)
    {
        if(!isset(HrJobDepartmentRelationModel::$cost_type_list[$type])) {
            return '';
        }
        return static::$t->_('cost_type_' . $type);
    }

    /**
     * 工作天数轮休规则数据转换
     * @param $working_day_rest_type
     * @return string
     */
    function convert_working_day_rest_type_arr($working_day_rest_type)
    {
        if (empty($working_day_rest_type)) {
            return '';
        }
        $working_day_rest_type_list = [];
        foreach ($working_day_rest_type as $value) {
            $working_day_rest_type_list[] = static::$t->_('working_day_rest_type_' . $value);
        }
        $working_day_rest_type_str = implode(',', $working_day_rest_type_list);
        return $working_day_rest_type_str;
    }
}
