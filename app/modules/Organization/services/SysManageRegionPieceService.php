<?php

namespace App\Modules\Organization\Services;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\RestClient;
use App\Library\Validation\ValidationException;
use App\Models\backyard\FranchiseeManagePieceModel;
use App\Models\backyard\FranchiseeManageRegionModel;
use App\Models\backyard\HrOrganizationDepartmentFranchiseePieceRelationModel;
use App\Models\backyard\HrOrganizationDepartmentFranchiseeRegionRelationModel;
use App\Models\backyard\HrStaffInfoPositionModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentPieceRelationModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentRegionRelationModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;
use app\modules\Organization\models\SysDeptOperateLogsModel;
use App\Modules\Organization\Models\SysManagePieceModel;
use App\Modules\Organization\Models\SysManageRegionModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Models\backyard\SettingEnvModel;
use App\Modules\User\Models\BiStaffInfoPositionModel;
use App\Repository\HrStaffRepository;
use App\Models\fle\SysManageRegionModel as FleSysManageRegionModel;
use App\Models\fle\SysManagePieceModel as FleSysManagePieceModel;
use App\Models\fle\FranchiseeManagePieceModel as FleFranchiseeManagePieceModel;
use App\Models\fle\FranchiseeManageRegionModel as FleFranchiseeManageRegionModel;

class SysManageRegionPieceService extends BaseService
{

    public static $region_ids_th = [3, 6, 31, 1, 25];
    public static $department_net_work_area_id_th = 32;//Network Area
    public static $department_net_work_bulky_area_id_th = 483;//Network Bulky Area
    public static $department_cdc_network_operations_th = 1183;//CDC (Network Operations)
    public static $department_shop_operations_th = 51; //Shop Operations
    public static $region_bulky_area_6_id = 38;//Bulky Area 6

    public static $department_net_work_operations_my = 320;
    public static $department_big_distribution_centre_my = 15084;
    public static $department_retail_business_development_my = 350; //Retail Business Development

    public static $department_philippines_shop_ph = 123; //Flash Philippines Shop-1

    public static $franchisee_region_manager_edit_path = '/svc/franchisee/region_manager/edit';
    public static $franchisee_piece_manager_edit_path = '/svc/franchisee/piece_manager/edit';
    public static $franchisee_region_manager_edit_type = 'region_manager_edit';
    public static $franchisee_piece_manager_edit_type = 'piece_manager_edit';

    //大区列表
    //network operations type=1
    //shop progjet type=2 查网点
    public function getRegionList($params, $staff_info_id) {
        $department_id = $params['department_id'] ?? 0;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            region_relation.department_id,
            region_relation.region_id,
            manage_region.id,
            manage_region.name as label,
            manage_region.type,
            manage_region.manager_id,
            manage_region.manager_name,
            manage_region.manager_position_state
        ');

        $builder->from(['region_relation' => HrOrganizationDepartmentRegionRelationModel::class]);
        $builder->leftJoin(SysManageRegionModel::class, 'region_relation.region_id = manage_region.id','manage_region');
        $builder->where('region_relation.department_id = :department_id:', ['department_id' => $department_id]);
        $builder->andWhere('region_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('region_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        //如果是负责人的话 可以看该部门下所有的大区片区网点
        $region_ids = [];
        $department_service = new DepartmentService();
        $result = ($department_service)->validateDepartmentManager($department_id, $staff_info_id);
        if(!$result) {
            //查询大区权限
            $region_ids = (DepartmentRelationService::getInstance())->getStaffRegionScopeList($staff_info_id);

            if ($region_ids == OrganizationDepartmentEnums::NO_AUTH) {
                return [];
            }

            if ($region_ids != OrganizationDepartmentEnums::ALL_AUTH){
                $builder->inWhere('region_relation.region_id', $region_ids);
            }
        }

        $areas_range_list = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);

        $region_list = $builder->getQuery()->execute()->toArray();

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);

        $hr_staff_service = new HrStaffInfoService();
        $staff_info_ids = array_column($region_list,'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);
        $countryCode = strtoupper(env('country_code', 'TH'));
        foreach ($region_list as $key => $value) {
            $work_count = $this->getRegionPieceWorkCount($value['id'],0);
            $region_list[$key]['staff_info_id'] = $staffs[$value['manager_id']]['staff_info_id'] ?? '';//负责人工号
            $region_list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';//负责人姓名
            $region_list[$key]['job_title'] = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $region_list[$key]['work_count'] = $work_count;//在职人数
            $region_list[$key]['hasChildren'] = true;
            $region_list[$key]['current_type'] = 3;
            if($value['type'] == 2) {
                $region_list[$key]['next_type'] = 5;//请求网点
            } else {
                $region_list[$key]['next_type'] = 4;//请求片区
                $region_list[$key]['is_show_manager_position_state'] = in_array($countryCode, DepartmentService::$nw_manager_country_list) ? 1 : 0;
            }
	        $region_list[$key]['budget_sum'] = '';
	        $region_list[$key]['budget_subordinates_sum'] = '';

            if(array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                $region_list[$key]['is_edit_organization_info'] = in_array($value['region_id'], $areas_range_list['regions']);
            } else {
                $region_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
            }
            $region_list[$key]['region_piece_type'] = OrganizationDepartmentEnums::TYPE_REGION;//正常大区
        }

        return $region_list;
    }

    //片区列表
    public function getPieceList($params, $staff_info_id = 0)
    {
        $region_id     = $params['region_id'] ?? 0;
        $department_id = $params['department_id'] ?? 0;

        $builder = $this->modelsManager->createBuilder();

        $builder->columns('
            piece_relation.department_id,
            piece_relation.region_id,
            piece_relation.piece_id,
            manage_piece.id,
            manage_piece.name as label,
            manage_piece.manage_region_id,
            manage_piece.manager_id,
            manage_piece.manager_name,
            manage_piece.manager_position_state
        ');

        $builder->from(['piece_relation' => HrOrganizationDepartmentPieceRelationModel::class]);
        $builder->leftJoin(SysManagePieceModel::class, 'piece_relation.piece_id = manage_piece.id', 'manage_piece');
        $builder->where('piece_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('piece_relation.level_state = :level_state:', ['level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL]);
        $builder->andWhere('piece_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $builder->andWhere('piece_relation.region_id = :region_id:', ['region_id' => $region_id]);
        $builder->andWhere('piece_relation.department_id = :department_id:', ['department_id' => $department_id]);

        $piece_ids = [];
        $department_service = new DepartmentService();
        $result = $department_service->validateDepartmentManager($department_id, $staff_info_id);
        if(!$result) {
            //查片区权限
            $piece_ids = (DepartmentRelationService::getInstance())->getStaffPieceScopeList($staff_info_id);

            if ($piece_ids == OrganizationDepartmentEnums::NO_AUTH) {
                return [];
            }

            if ($piece_ids != OrganizationDepartmentEnums::ALL_AUTH){
                $builder->andWhere('piece_relation.piece_id IN ({piece_ids:array})', ['piece_ids' => $piece_ids]);
            }
        }

        $areas_range_list = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);

        $piece_list = $builder->getQuery()->execute()->toArray();

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);

        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($piece_list,'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);
        $countryCode = strtoupper(env('country_code', 'TH'));
        $region_detail = $this->getRegionDetail($region_id);
        $region_type = $region_detail['type'] ?? 0;
        foreach ($piece_list as $key => $value) {
            $work_count = $this->getRegionPieceWorkCount($value['manage_region_id'],$value['id']);
            $piece_list[$key]['staff_info_id'] = $staffs[$value['manager_id']]['staff_info_id'] ?? '';//负责人工号
            $piece_list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';//负责人姓名
            $piece_list[$key]['job_title'] = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $piece_list[$key]['work_count'] = $work_count;
            $piece_list[$key]['next_type'] = 5;//网点
            $piece_list[$key]['hasChildren'] = true;
            $piece_list[$key]['current_type'] = 4;
            if(in_array($countryCode, DepartmentService::$nw_manager_country_list) && $region_type == 1) {
                $piece_list[$key]['is_show_manager_position_state'] = 1;
            } else {
                $piece_list[$key]['is_show_manager_position_state'] = 0;
            }
	        $piece_list[$key]['budget_sum'] = '';
	        $piece_list[$key]['budget_subordinates_sum'] = '';

            if (array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                if (in_array($value['region_id'], $areas_range_list['regions'])) { //配置管辖大区：那么员工可编辑管辖范围里的大区及其下片区、网点
                    $piece_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
                } else {
                    $piece_list[$key]['is_edit_organization_info'] = in_array($value['piece_id'], $areas_range_list['pieces']);
                }
            } else {
                $piece_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
            }
            $piece_list[$key]['region_piece_type'] = OrganizationDepartmentEnums::TYPE_PIECE;
        }
        return $piece_list;
    }

    //大区/片区在职人数
    public function getRegionPieceWorkCount($region_id,$piece_id) {
        //获取网点列表
        $store_builder = $this->modelsManager->createBuilder();
        $store_builder->columns('id');
        $store_builder->from(SysStoreModel::class);
        $store_builder->where('manage_region = :manage_region:',['manage_region' => $region_id]);
        if($piece_id > 0) {
            $store_builder->andWhere('manage_piece = :manage_piece:',['manage_piece' => $piece_id]);
        }

        $store_id_list = $store_builder->getQuery()->execute()->toArray();
        $store_id_arr = array_column($store_id_list,'id');
        
        if(!empty($store_id_arr)) {
            $staff_builder = $this->modelsManager->createBuilder();
            $staff_builder->columns('staff_info_id');
            $staff_builder->from(HrStaffInfoModel::class);
            $staff_builder->where('formal in (1,4) and state=1');
            $staff_builder->inWhere('sys_store_id',$store_id_arr);
            $result = $staff_builder->getQuery()->execute()->count();
        } else {
            $result = 0;
        }

        return $result;
    }

    public function getRegionDetail($region_id) {
        $detail = SysManageRegionModel::findFirst([
            'conditions' => 'id = :region_id: and deleted = 0',
            'bind' => [
                'region_id' => $region_id,
            ]
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 大区详情 fle
     * @param $region_id
     * @return array
     */
    public function getRegionDetailByFle($region_id): array
    {
        $detail = FleSysManageRegionModel::findFirst([
            'conditions' => 'id = :region_id: and deleted = 0',
            'bind'       => [
                'region_id' => $region_id,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    public function getPieceDetail($piece_id) {
        $detail = SysManagePieceModel::findFirst([
            'conditions' => 'id = :piece_id: and deleted = 0',
            'bind' => [
                'piece_id' => $piece_id,
            ]
        ]);
        $detail = !empty($detail) ? $detail->toArray() : [];
        if(!empty($detail)) {
            $region_detail = $this->getRegionDetail($detail['manage_region_id']);
            $detail['manager_region_type'] = $region_detail['type'] ?? 0;
            return $detail;
        }
        return [];
    }

    /**
     * 片区详情 fle
     * @param $piece_id
     * @return array
     */
    public function getPieceDetailByFle($piece_id): array
    {
        $detail = FleSysManagePieceModel::findFirst([
            'conditions' => 'id = :piece_id: and deleted = 0',
            'bind'       => [
                'piece_id' => $piece_id,
            ],
        ]);
        $detail = !empty($detail) ? $detail->toArray() : [];
        if (!empty($detail)) {
            $region_detail                 = $this->getRegionDetailByFle($detail['manage_region_id']);
            $detail['manager_region_type'] = $region_detail['type'] ?? 0;
            return $detail;
        }
        return [];
    }

    public function sync_manage_region_manager_ms($param)
    {
        $operator_id = $param['operator_id'] ?? 10001;
        if ($operator_id == -1) {
            $operator_id = 10001;
        }
        $params = [
            'id'                     => $param['region_id'],
            'manager_id'             => $param['manager_id'],
            'operator_id'            => $operator_id,
            'manager_position_state' => $param['manager_position_state'] ?? null,
        ];
        $api    = new RestClient('nws');
        $return    = $api->execute(RestClient::METHOD_POST, '/svc/region/staff/update/region_manager',
            $params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] = true;
            return $return;
        }
        $this->logger->error('sync_manage_region_manager_ms,操作内容' . json_encode($params) . ',接口返回：' . json_encode($return));
        $return['error'] =  ($return['message'] ?? static::$t->_('retry_later'));
        return $return;
    }

    public function sync_manage_piece_manager_ms($param)
    {
        $operator_id = $param['operator_id'] ?? 10001;
        if ($operator_id == -1) {
            $operator_id = 10001;
        }
        $params = [
            'id'                     => $param['piece_id'],
            'manager_id'             => $param['manager_id'],
            'operator_id'            => $operator_id,
            'manager_position_state' => $param['manager_position_state'] ?? null,
        ];
        $api    = new RestClient('nws');
        $return = $api->execute(RestClient::METHOD_POST, '/svc/region/staff/update/piece_manager',
            $params, ['Accept-Language' => self::$language]);
        if (isset($return['code']) && $return['code'] == 1) {
            $return['result'] = true;
            return $return;
        }
        $this->logger->error('sync_manage_piece_manager_ms,操作内容' . json_encode($params) . ',接口返回：' . json_encode($return));
        $return['error'] = $return['message'] ?? static::$t->_('retry_later');
        return $return;
    }

    public function updateManagerRegionPositionState($param) {
        $manage_region_id = $param['region_id'] ?? ''; //大区id
        $manager_id = $param['manager_id']; //负责人id
        $user = $param['user'];//登录用户信息
        $conditions = 'type = 1 and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id:';
        $bind = [
            'manager_id'=> $manager_id,
        ];
        if(!empty($manage_region_id)) {
            $conditions = 'type = 1 and deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and id != :region_id:';
            $bind = [
                'manager_id'=> $manager_id,
                'region_id' => $manage_region_id
            ];
        }

        $region_list = SysManageRegionModel::find([
            'conditions' => $conditions,
            'bind' => $bind
        ]);
        $region_list = !empty($region_list) ? $region_list->toArray() : [];
        foreach ($region_list as $key => $value) {
            $sync_param = [
                'region_id' => $value['id'],
                'manager_id' => $manager_id,
                'operator_id' => $user['id'],
                'manager_position_state' => 2
            ];
            $result = $this->sync_manage_region_manager_ms($sync_param);
            $this->getDI()->get('logger')->info('updateManageRegionPositionState，操作人工号:' . $user['id'] . ',操作内容' . json_encode($sync_param) . ',接口返回：' . json_encode($result));
        }
        return true;
    }

    public function updateManagerPiecePositionState($param) {
        $manage_piece_id = $param['piece_id'] ?? ''; //片区id
        $manager_id = $param['manager_id'] ?? ''; //负责人id
        $user = $param['user'];//登录用户信息
        $region_list = SysManageRegionModel::find([
            'conditions' => 'type = 1 and deleted = 0'
        ]);
        $region_list = !empty($region_list) ? $region_list->toArray() : [];
        $region_ids = array_column($region_list, 'id');
        $conditions = 'deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and manage_region_id in ({region_ids:array})';
        $bind = [
            'manager_id' => $manager_id,
            'region_ids' => $region_ids
        ];

        if(!empty($manage_piece_id)) {
            $conditions = 'deleted = 0 and manager_position_state = 1 and manager_id = :manager_id: and manage_region_id in ({region_ids:array}) and id != :piece_id:';
            $bind = [
                'manager_id' => $manager_id,
                'region_ids' => $region_ids,
                'piece_id' => $manage_piece_id
            ];
        }

        $piece_list = SysManagePieceModel::find([
            'conditions' => $conditions,
            'bind' => $bind
        ]);
        $piece_list = !empty($piece_list) ? $piece_list->toArray() : [];
        foreach ($piece_list as $key => $value) {
            $sync_param = [
                'piece_id' => $value['id'],
                'manager_id' => $manager_id,
                'operator_id' => $user['id'],
                'manager_position_state' => 2
            ];
            $result = $this->sync_manage_piece_manager_ms($sync_param);
            $this->getDI()->get('logger')->info('updateManagerPiecePositionState，操作人工号:' . $user['id'] . ',操作内容' . json_encode($sync_param) . ',接口返回：' . json_encode($result));
        }
        return true;
    }

    //获取所有大区
    public function getRegionAllList() {
        $builder = $this->modelsManager->createBuilder();
        return $builder->columns('id,name,type,manager_id,manager_name,manager_position_state')
                            ->from(SysManageRegionModel::class)
                            ->where('deleted = 0')
                            ->getQuery()
                            ->execute()
                            ->toArray();
    }

    //获取所有片区
    public function getPieceAllList() {
        $builder = $this->modelsManager->createBuilder();
        return $builder->columns('id,name,manage_region_id,manager_id,manager_name,manager_position_state')
                            ->from(SysManagePieceModel::class)
                            ->where('deleted = 0')
                            ->getQuery()
                            ->execute()
                            ->toArray();
    }

    /**
     * 获取大区片区等列表
     * @param $type
     * @param $department_id
     * @return mixed
     */
    public function getManageCommonList($parmas, $staff_info_id = 0) {

        //获取管辖范围
        DepartmentRelationService::$departmentJurisdiction = DepartmentRelationService::getInstance()->getDepartmentJurisdiction($staff_info_id);

        $data['store_list'] =  $data['piece_list'] = $data['region_list'] = [];

        //部门一级查询
        if (empty($parmas['region_id']) && empty($parmas['piece_id']) && empty($parmas['franchisee_region_id']) && empty($parmas['franchisee_piece_id'])) {
            //获取大区
            $data['region_list'] = $this->getRegionList($parmas, $staff_info_id);

            //获取片区
            $data['piece_list'] = $this->getPieceList($parmas, $staff_info_id);

            //获取网点
            $data['store_list'] = (new StoreService())->getStoreList($parmas, $staff_info_id);

            //加盟商大区列表
            $data['franchisee_region_list'] = $this->getFranchiseeRegionList($parmas, $staff_info_id);
            //加盟商片区列表
            $data['franchisee_piece_list'] = $this->getFranchiseePieceList($parmas, $staff_info_id);

            return $data;
        }

        //大区一级查询
        if (!empty($parmas['region_id']) && empty($parmas['piece_id'])) {
            //获取片区
            $data['piece_list'] = $this->getPieceList($parmas, $staff_info_id);
            //获取网点
            $data['store_list'] = (new StoreService())->getStoreList($parmas, $staff_info_id);

            return $data;
        }

        if (!empty($parmas['franchisee_region_id']) && empty($parmas['franchisee_piece_id'])) {
            //加盟商片区列表
            $data['franchisee_piece_list'] = $this->getFranchiseePieceList($parmas, $staff_info_id);
            return $data;
        }

        //其他等级查询
        $data['store_list'] = (new StoreService())->getStoreList($parmas, $staff_info_id);
        return $data;

    }

    /**
     * 加盟商大区列表
     * @param $params
     * @param $staff_info_id
     * @return array
     */
    public function getFranchiseeRegionList($params, $staff_info_id): array
    {
        $department_id = $params['department_id'] ?? 0;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('
            region_relation.department_id,
            region_relation.region_id as franchisee_region_id,
            manage_region.id,
            manage_region.name as label,
            manage_region.manager_id,
            manage_region.manager_name,
            manage_region.manager_phone,
            manage_region.assistant_manager_id,
            manage_region.assistant_manager_name,
            manage_region.assistant_manager_phone
        ');

        $builder->from(['region_relation' => HrOrganizationDepartmentFranchiseeRegionRelationModel::class]);
        $builder->leftJoin(FranchiseeManageRegionModel::class, 'region_relation.region_id = manage_region.id','manage_region');
        $builder->where('region_relation.department_id = :department_id:', ['department_id' => $department_id]);
        $builder->andWhere('region_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('region_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $department_service = new DepartmentService();
        $result = ($department_service)->validateDepartmentManager($department_id, $staff_info_id);

        if(!$result) {
            //查询大区权限
            $region_ids = (DepartmentRelationService::getInstance())->getStaffFranchiseeRegionScopeList($staff_info_id);

            if ($region_ids == OrganizationDepartmentEnums::NO_AUTH) {
                return [];
            }

            if ($region_ids != OrganizationDepartmentEnums::ALL_AUTH) {
                $builder->inWhere('region_relation.region_id', $region_ids);
            }
        }

        $region_list = $builder->getQuery()->execute()->toArray();

        $areas_range_list = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);

        $hr_staff_service = new HrStaffInfoService();
        $staff_info_ids = array_column($region_list,'manager_id');
        $assistant_manager_ids = array_column($region_list, 'assistant_manager_id');
        $staff_info_ids = array_merge($staff_info_ids, $assistant_manager_ids);
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        foreach ($region_list as $key => $value) {
            $region_list[$key]['staff_info_id']   = $staffs[$value['manager_id']]['staff_info_id'] ?? ''; //负责人工号
            $region_list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';          //负责人姓名
            $region_list[$key]['job_title']       = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $region_list[$key]['assistant_id']   = $staffs[$value['assistant_manager_id']]['staff_info_id'] ?? ''; //助理工号
            $region_list[$key]['assistant_name'] = $staffs[$value['assistant_manager_id']]['name'] ?? '';          //助理姓名
            $region_list[$key]['assistant_job_title']    = $staffs[$value['assistant_manager_id']]['job_title_name'] ?? '';//助理职位

            $region_list[$key]['work_count']   = 0;//在职人数
            $region_list[$key]['hasChildren']  = OrganizationDepartmentEnums::HAS_CHILDREN_YES;
            $region_list[$key]['current_type'] = OrganizationDepartmentEnums::CURRENT_TYPE_REGION;
            $region_list[$key]['next_type']    = OrganizationDepartmentEnums::NEXT_TYPE_PIECE;//请求片区

            $region_list[$key]['is_show_manager_position_state'] = OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;
            $region_list[$key]['budget_sum']                     = '';
            $region_list[$key]['budget_subordinates_sum']        = '';

            if (array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                $region_list[$key]['is_edit_organization_info'] = in_array($value['franchisee_region_id'], $areas_range_list['franchisee_regions']);
            } else {
                $region_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
            }
            $region_list[$key]['region_piece_type'] = OrganizationDepartmentEnums::TYPE_FRANCHISEE_REGION;//加盟商大区
        }

        return $region_list;
    }

    /**
     * 加盟商片区列表
     * @param $params
     * @param $staff_info_id
     * @return array
     */
    public function getFranchiseePieceList($params, $staff_info_id): array
    {
        $region_id     = $params['franchisee_region_id'] ?? 0;
        $department_id = $params['department_id'] ?? 0;

        $builder = $this->modelsManager->createBuilder();

        $builder->columns('
            piece_relation.department_id,
            piece_relation.region_id as franchisee_region_id,
            piece_relation.piece_id as franchisee_piece_id,
            manage_piece.id,
            manage_piece.name as label,
            manage_piece.manage_region_id,
            manage_piece.manager_id,
            manage_piece.manager_name,
            manage_piece.manager_phone
        ');

        $builder->from(['piece_relation' => HrOrganizationDepartmentFranchiseePieceRelationModel::class]);
        $builder->leftJoin(FranchiseeManagePieceModel::class, 'piece_relation.piece_id = manage_piece.id', 'manage_piece');
        $builder->where('piece_relation.state = :state:', ['state' => OrganizationDepartmentEnums::STATE_NORMAL]);
        $builder->andWhere('piece_relation.level_state = :level_state:', ['level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL]);
        $builder->andWhere('piece_relation.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);

        $builder->andWhere('piece_relation.region_id = :region_id:', ['region_id' => $region_id]);
        $builder->andWhere('piece_relation.department_id = :department_id:', ['department_id' => $department_id]);

        $department_service = new DepartmentService();
        $result = $department_service->validateDepartmentManager($department_id, $staff_info_id);
        if(!$result) {
            //查片区权限
            $piece_ids = (DepartmentRelationService::getInstance())->getStaffFranchiseePieceScopeList($staff_info_id);

            if ($piece_ids == OrganizationDepartmentEnums::NO_AUTH) {
                return [];
            }

            if ($piece_ids != OrganizationDepartmentEnums::ALL_AUTH){
                $builder->inWhere('piece_relation.piece_id', $piece_ids);
            }
        }

        $piece_list = $builder->getQuery()->execute()->toArray();

        $areas_range_list = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);

        [
            $organization_edit_info_roles_id_arr,
            $organization_edit_sap_info_roles_id_arr,
            $staff_position
        ] = $department_service->getOrganizationEditPermission($staff_info_id);

        $hr_staff_service = new HrStaffInfoService();

        $staff_info_ids = array_column($piece_list,'manager_id');
        $staffs = $hr_staff_service->getHrStaffInfo($staff_info_ids);

        foreach ($piece_list as $key => $value) {
            $piece_list[$key]['staff_info_id']   = $staffs[$value['manager_id']]['staff_info_id'] ?? ''; //负责人工号
            $piece_list[$key]['staff_info_name'] = $staffs[$value['manager_id']]['name'] ?? '';          //负责人姓名
            $piece_list[$key]['job_title']       = $staffs[$value['manager_id']]['job_title_name'] ?? '';//职位

            $piece_list[$key]['work_count']   = 0;
            $piece_list[$key]['next_type']    = OrganizationDepartmentEnums::NEXT_TYPE_STORE;//网点
            $piece_list[$key]['hasChildren']  = OrganizationDepartmentEnums::HAS_CHILDREN_NO;
            $piece_list[$key]['current_type'] = OrganizationDepartmentEnums::CURRENT_TYPE_PIECE;

            $piece_list[$key]['is_show_manager_position_state'] = OrganizationDepartmentEnums::SHOW_MANAGER_POSITION_STATE_NO;
            $piece_list[$key]['budget_sum']                     = '';
            $piece_list[$key]['budget_subordinates_sum']        = '';

            if (array_intersect($organization_edit_info_roles_id_arr, $staff_position)) {
                if(in_array($value['franchisee_region_id'], $areas_range_list['franchisee_regions'])) {
                    $piece_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
                } else {
                    $piece_list[$key]['is_edit_organization_info'] = in_array($value['franchisee_piece_id'], $areas_range_list['franchisee_pieces']);
                }
            } else {
                $piece_list[$key]['is_edit_organization_info'] = OrganizationDepartmentEnums::EDIT_ORGANIZATION_INFO_YES;
            }
            $piece_list[$key]['region_piece_type'] = OrganizationDepartmentEnums::TYPE_FRANCHISEE_PIECE; //加盟商片区
        }
        return $piece_list;
    }

    /**
     * 更新加盟商大区负责人/助理
     * @param $params
     * @return array|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateFranchiseeRegionManager($params)
    {
        $region_id    = $params['id'];
        $manager_id   = $params['manager_id'];
        $assistant_id = $params['assistant_id'] ?? null;
        $user         = $params['user'];//登录用户信息
        $operator_id  = $user['id'];    //登录人工号

        $region_detail = $this->getFranchiseeRegionDetail($region_id);
        if (empty($region_detail)) {
            throw new ValidationException('Franchisee Region Error', ErrCode::$VALIDATE_ERROR);
        }

        //验证工号是否正确
        $department_service = new DepartmentService();
        $manager_id_error   = $department_service->validateStaffInfoId($manager_id);
        if ($manager_id_error) {
            throw new ValidationException(static::$t->_($manager_id_error), ErrCode::$VALIDATE_ERROR);
        }

        if (!empty($assistant_id)) {
            $assistant_error = $department_service->validateStaffInfoId($assistant_id);
            if ($assistant_error) {
                throw new ValidationException(static::$t->_($assistant_error), ErrCode::$VALIDATE_ERROR);
            }
        }
        $before = [
            'manager_id'             => $region_detail['manager_id'] ?? '',
            'manager_name'           => $region_detail['manager_name'] ?? '',
            'assistant_manager_id'   => $region_detail['assistant_manager_id'] ?? null,
            'assistant_manager_name' => $region_detail['assistant_manager_name'] ?? '',
        ];

        $api_params = [
            'id'                 => $region_id,
            'managerId'          => $manager_id,
            'assistantManagerId' => $assistant_id,
            'operatorId'         => $operator_id,
        ];
        $result = $this->updateFranchiseeRegionPieceManager([
            'type'       => self::$franchisee_region_manager_edit_type,
            'api_params' => $api_params,
        ]);

        if (empty($result)) {
            throw new BusinessException('error', ErrCode::$SYSTEM_ERROR);
        }

        $countryCode = strtoupper(env('country_code', GlobalEnums::TH_COUNTRY_CODE));

        //同步直线上级变更--添加负责人数据管辖
        if (in_array($countryCode,
                DepartmentService::$nw_manager_country_list) && $manager_id != $region_detail['manager_id']) {
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'franchisee_region_id' => $region_id,
                    'manager_id'           => $manager_id,
                    'operator_id'          => $operator_id,
                    'update_type'          => 5,
                ],
            ]);
            $hris_ac->execute();
        }

        $after_region_detail = $this->getFranchiseeRegionDetail($region_id);
        $after = [
            'manager_id'             => $after_region_detail['manager_id'] ?? '',
            'manager_name'           => $after_region_detail['manager_name'] ?? '',
            'assistant_manager_id'   => $after_region_detail['assistant_manager_id'] ?? null,
            'assistant_manager_name' => $after_region_detail['assistant_manager_name'] ?? '',
        ];

        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $before, $after, $region_id, SysDeptOperateLogsModel::TYPE_FRANCHISEE_REGION);
        return $result;
    }

    /**
     * 更新加盟商片区负责人
     * @param $params
     * @return array|mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateFranchiseePieceManager($params)
    {
        $piece_id    = $params['id'];
        $manager_id  = $params['manager_id'];
        $user        = $params['user'];//登录用户信息
        $operator_id = $user['id'];

        $piece_detail = $this->getFranchiseePieceDetail($piece_id);
        if (empty($piece_detail)) {
            throw new ValidationException('Franchisee Piece Error', ErrCode::$VALIDATE_ERROR);
        }

        //验证工号是否正确
        $department_service = new DepartmentService();
        $manager_id_error   = $department_service->validateStaffInfoId($manager_id);
        if ($manager_id_error) {
            throw new ValidationException(static::$t->_($manager_id_error), ErrCode::$VALIDATE_ERROR);
        }

        $before = [
            'manager_id'   => $piece_detail['manager_id'] ?? '',
            'manager_name' => $piece_detail['manager_name'] ?? '',
        ];

        $api_params = [
            'id'         => $piece_id,
            'managerId'  => $manager_id,
            'operatorId' => $operator_id,
        ];

        $result = $this->updateFranchiseeRegionPieceManager([
            'type'       => self::$franchisee_piece_manager_edit_type,
            'api_params' => $api_params,
        ]);

        if (empty($result)) {
            throw new BusinessException('error', ErrCode::$SYSTEM_ERROR);
        }

        $countryCode = strtoupper(env('country_code', GlobalEnums::TH_COUNTRY_CODE));
        //同步直线上级变更--添加负责人数据管辖
        if (in_array($countryCode,
                DepartmentService::$nw_manager_country_list) && $manager_id != $piece_detail['manager_id']) {
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'franchisee_piece_id' => $piece_id,
                    'manager_id'          => $manager_id,
                    'operator_id'         => $operator_id,
                    'update_type'         => 6,
                ],
            ]);
            $hris_ac->execute();
        }

        $after_region_detail = $this->getFranchiseePieceDetail($piece_id);
        $after               = [
            'manager_id'   => $after_region_detail['manager_id'] ?? '',
            'manager_name' => $after_region_detail['manager_name'] ?? '',
        ];

        (new DeptPcCodeService())->saveDeptOperateLog($user['id'], $user['name'], $before, $after, $piece_id, SysDeptOperateLogsModel::TYPE_FRANCHISEE_PIECE);
        return $result;
    }

    /**
     * 修改加盟商大区片区负责人请求
     * @param $params
     * @return array|mixed
     */
    public function updateFranchiseeRegionPieceManager($params)
    {
        $api_params = $params['api_params'] ?? [];
        $type       = $params['type'] ?? '';
        switch ($type) {
            case self::$franchisee_region_manager_edit_type:
                $path = self::$franchisee_region_manager_edit_path;
                break;
            case self::$franchisee_piece_manager_edit_type:
                $path = self::$franchisee_piece_manager_edit_path;
                break;
            default:
                $path = '';
        }

        $headers[] = "Content-type: application/json";
        $headers[] = "Accept: application/json";
        $headers[] = "Accept-Language: " . $this->getLanguage('zh-CN');
        $client    = new RestClient('fle_fra');
        $result    = $client->execute(RestClient::METHOD_POST, $path, $api_params, $headers, false);
        $this->logger->info([
            'function' => 'updateFranchiseeRegionPieceManager',
            'params'   => $params,
            'url'      => $path,
            'result'   => $result,
        ]);
        return $result;
    }

    /**
     * 加盟商大区详情
     * @param $region_id
     * @return array
     */
    public function getFranchiseeRegionDetail($region_id): array
    {
        $detail = FleFranchiseeManageRegionModel::findFirst([
            'conditions' => 'id = :region_id: and deleted = :deleted:',
            'bind'       => [
                'region_id' => $region_id,
                'deleted'   => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }

    /**
     * 加盟商片区详情
     * @param $piece_id
     * @return array
     */
    public function getFranchiseePieceDetail($piece_id): array
    {
        $detail = FleFranchiseeManagePieceModel::findFirst([
            'conditions' => 'id = :piece_id: and deleted = :deleted:',
            'bind'       => [
                'piece_id' => $piece_id,
                'deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
        ]);
        return !empty($detail) ? $detail->toArray() : [];
    }
}