<?php
/**
 * 职位体系-职组模块
 */

namespace App\Modules\Organization\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobGroupModel;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Models\HrJobCompetencyModel;
use App\Modules\Organization\Models\SysDepartmentByModel;
use App\Modules\Organization\Services\JobJdService as JobJdService;


class JobGroupService extends BaseService
{
    public static $validate_group_add_params_v2 = [
        'name'           => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'competency_ids' => 'Required|ArrLenGe:1|>>>:competency_ids', //专业胜任力胜任力ids
    ];
    public static $validate_group_add_params_v3 = [//子职组
        'name' => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'pid'  => 'Required|IntGt:0|>>>:pid error',  //父职组id

    ];
    public static $validate_group_edit_params_v2 = [
        'id'             => 'Required|IntGt:0|>>>:id error', //职组ID
        'name'           => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'competency_ids' => 'Required|ArrLenGe:1|>>>:competency_ids', //专业胜任力胜任力ids
    ];
    public static $validate_group_edit_params_v3 = [//子职组
        'id'   => 'Required|IntGt:0|>>>:id error', //职组ID
        'name' => 'Required|StrLenGeLe:1,100|>>>:job_name error', //组名称
        'pid'  => 'Required|IntGt:0|>>>:pid error',  //父职组id

    ];

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取职位列表
     * @return mixed
     */
    public function getJobGroupOption()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name,pid,functional_competency_ids as competency_ids');
        $builder->from(HrJobGroupModel::class);
        $builder->andWhere('is_del=:is_del:', ['is_del' => 0]);
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        $group_list = [];
        $job_competency_list = [];
        $job_competency_list = JobCompetencyService::getInstance()->getJobCompetencyOption();

        $job_competency_list = array_column($job_competency_list['type2'], 'id') ?? [];

        foreach ($list as $key => $value) {
            if (0 == $value['pid']) {
                $group_list[$value['id']] = $value;
                //处理胜任力id
                if (!empty($value['competency_ids'])) {
                    $value['competency_ids'] = explode(',', $value['competency_ids']);
                    $competency_list_1 = [];
                    foreach ($value['competency_ids'] as $competency_k => $competency_v) {
                        if (in_array($competency_v,$job_competency_list)) {
                            $competency_list_1[]=$competency_v;
                        }
                    }
                    $value['competency_ids'] = implode(',',$competency_list_1)   ;

                    $group_list[$value['id']]['competency_ids'] = $value['competency_ids'] ?? '';

                }
                unset($list[$key]);
            }
        }

        if (!empty($list)) {
            foreach ($list as $child_group) {
                unset($child_group['competency_ids']);
                $group_list[$child_group['pid']]['child_list'][] = $child_group;

            }
        }

        $group_list = array_merge($group_list);

        return $group_list ? $group_list : [];
    }


    /**
     * 获取职组列表
     * @return mixed
     */
    public function getJobGroupListOption(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset    = $page_size * ($page_num - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {

            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'id',
                'name',
                'pid',
                'functional_competency_ids as competency_ids',
                'add_userid',
                'update_userid',
                'created_at',
                'updated_at'
            ]);
            $builder->from(['main' => HrJobGroupModel::class]);

            $builder->andWhere('pid=:pid:', ['pid' => 0]);
            $builder->andWhere('is_del=:is_del:', ['is_del' => 0]);

            $count = $builder->getQuery()->execute()->count();

            if ($count > 0) {
                if(!isset($condition['is_download']) || !$condition['is_download']) {
                    $builder->limit($page_size, $offset);
                }

                $list = $builder->getQuery()->execute()->toArray();

                if (empty($list)) {
                    return [];
                }
                $pid_arr       = [];
                $pids          = '';
                $pid_arr       = array_column($list, 'id');
                $job_group_num = $this->getJobNum($pid_arr);

                //计算父职组在职人数  根据group id 查询职位id

                $group_id_res = $this->getJobByGroupId($pid_arr);

                //根据职位id 查询在职
                if (isset($group_id_res['job_ids']) && $group_id_res['job_ids']) {
                    $staff_info = JobJdService::getInstance()->getStaffNums($group_id_res['job_ids']);//职位的在职人数-职组列表1

                    if (!empty($staff_info)) {
                        $staff_info = array_column($staff_info, 'num', 'job_title');
                    }

                    //jd对应职位下员工数
                    $group_job_num = [];
                    if(isset($group_id_res['group_ids'])){
                        foreach ($group_id_res['group_ids'] as $k1 => $v1) {
                            foreach ($v1 as $k2 => $v2) {
                                $v1[$k2] = $staff_info[$v2] ?? 0;
                            }
                            $group_job_num[$k1] = array_sum($v1);
                        }
                    }

                }

                //==========
                $job_competency_list = [];
                $job_competency_list = JobCompetencyService::getInstance()->getJobCompetencyOption();

                $job_competency_list = array_column($job_competency_list['type2'], 'id') ?? [];

                foreach ($list as $key => $value) {
                    $job_group_list[$value['id']]             = $value; //将查询出来的一级
                    $job_group_list[$value['id']]['jd_count'] = $job_group_num[$value['id']] ?? 0;

                    $job_group_list[$value['id']]['serving_officer_count'] = $group_job_num[$value['id']] ?? 0;//员工数量;

                    //处理胜任力id
                    if (!empty($value['competency_ids'])) {
                        $value['competency_ids'] = explode(',', $value['competency_ids']);
                        $competency_list_1 = [];
                        foreach ($value['competency_ids'] as $competency_k => $competency_v) {
                            if (in_array($competency_v,$job_competency_list)) {
                                $competency_list_1[]=$competency_v;
                            }
                        }
                        $job_group_list[$value['id']]['created_at']           = date('Y-m-d H:i:s', strtotime($value['created_at']) + get_sys_time_offset() * 3600);
                        $job_group_list[$value['id']]['updated_at']           = date('Y-m-d H:i:s', strtotime($value['updated_at']) + get_sys_time_offset() * 3600);

                        $value['competency_ids'] = implode(',',$competency_list_1)   ;
                        $job_group_list[$value['id']]['competency_ids'] = $value['competency_ids'] ?? '';

                    }
                }
                $pids = implode(',', $pid_arr);

                $child_list = HrJobGroupModel::find([
                    'columns'    => 'id, name, pid,add_userid,update_userid,created_at,updated_at',
                    'conditions' => "is_del = 0 and pid in ($pids) "
                ])->toArray();


                if (!empty($child_list)) {
                    $child_group_id      = array_column($child_list, 'id');
                    $job_child_group_num = $this->getChildJobNum($child_group_id);

                    //计算子职组在职人数 根据group id 查询职位id

                    $child_group_id_res = $this->getJobByChildGroupId($child_group_id);

                    //根据职位id 查询在职
                    if (isset($child_group_id_res['child_job_ids']) && $child_group_id_res['child_job_ids']) {
                        $child_staff_info = JobJdService::getInstance()->getStaffNums($child_group_id_res['child_job_ids']);//职位的在职人数-职组列表2

                        if (!empty($child_staff_info)) {
                            $child_staff_info = array_column($child_staff_info, 'num', 'job_title');
                        }

                        //jd对应职位下员工数
                        $child_group_job_num = [];

                        foreach ($child_group_id_res['group_child_ids'] as $child_k1 => $child_v1) {

                            foreach ($child_v1 as $child_k2 => $child_v2) {
                                $child_v1[$child_k2] = $child_staff_info[$child_v2];
                            }
                            $child_group_job_num[$child_k1] = array_sum($child_v1);
                        }
                    }


                    foreach ($child_list as $job_group) {
                        $job_group['jd_count']              = $job_child_group_num[$job_group['id']] ?? 0;
                        $job_group['serving_officer_count'] = $child_group_job_num[$job_group['id']] ?? 0;//员工数量;
                        $job_group['created_at']  =  date('Y-m-d H:i:s', strtotime($value['created_at']) + get_sys_time_offset() * 3600);
                        $job_group['updated_at']  =  date('Y-m-d H:i:s', strtotime($value['updated_at']) + get_sys_time_offset() * 3600);
                        $job_group_list[$job_group['pid']]['child_list'][] = $job_group; //按树形排列
                    }
                }

                $job_group_list = array_merge($job_group_list);
            }
            $data['items'] = $job_group_list ?? [];

            $data['pagination']['total_count'] = $count;


        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('jd列表异常信息:' . $real_message);
        }
        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR) {
            $code    = ErrCode::$SUCCESS;
            $message = $real_message;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
        //=========================


    }

    /**
     * 创建职组
     * */
    public function addJobGroupOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $name_is_repeat = $this->checkNameIsRepeat($param);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('job_group_name_exists'));
            }
            //校验
            if (is_array($param['competency_ids'])) {

                if (count($param['competency_ids']) != count(array_unique($param['competency_ids']))) {
                    throw new \Exception('专业胜任力重复');

                }
                $param['competency_ids'] = implode(',', $param['competency_ids']);

            }

            $jobGroupModel = new HrJobGroupModel();
            $bool          = $jobGroupModel->save(['name' => trim($param['name']), 'functional_competency_ids' => $param['competency_ids'] ?? '', 'add_userid' => $param['uid'], 'update_userid' => $param['uid'], 'created_uname' => $param['uname'], 'updated_uname' => $param['uname']]);
            if ($bool === false) {
                $messages = $jobGroupModel->getMessages();
                throw new \Exception('职组创建失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('职组创建异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 检查职组名称是否重复
     * @param $job_name
     * @return bool
     */
    private function checkNameIsRepeat($param, $id = 0)
    {

        if ($id) { //编辑操作
            $res = HrJobGroupModel::findFirst([
                'name = :name: and id <> :id:',
                'bind' => ['name' => $param['name'], 'id' => $param['id']],
            ]);

        } else { //新增操作
            $res = HrJobGroupModel::findFirst([
                'name = :name:',
                'bind' => ['name' => $param['name']],
            ]);
        }
        if ($res) {
            return true;
        }
        return false;
    }

    /**
     * 检查职组是父职组
     * @param $job_name
     * @return bool
     */
    private function checkIdIsExist($id)
    {

        $res = HrJobGroupModel::findFirst([
            'id = :id: and pid = :pid: and is_del = :is_del:',
            'bind' => ['id' => $id, 'pid' => 0, 'is_del' => 0],
        ]);

        if ($res) {
            return true;
        }
        return false;
    }


    public function findOneById($id)
    {
        if(empty($id)){
            return [];
        }
        $res = HrJobGroupModel::findFirst(['id = :id: and is_del = :is_del:', 'bind' => ['id' => $id, 'is_del' => 0]]);
        return $res ? $res->toArray() : [];
    }

    /**
     * 修改职组
     * */

    public function editJobGroupOption($param)
    {
        //判断是否子职组
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //检验是否存在
            $jobGroupModel = HrJobGroupModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $param['id']],
            ]);
            if (empty($jobGroupModel)) {
                throw new \Exception('job group not exists');
            }

            $name_is_repeat = $this->checkNameIsRepeat($param, 1);

            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('job_group_name_exists'));
            }

            if (isset($param['pid']) && $param['pid']) {//子职组
                $pid_is_exist = $this->checkIdIsExist($param['pid']);
                if (!$pid_is_exist) {
                    throw new ValidationException('父职组不存在');
                }
                $save_data = ['name' => $param['name'], 'update_userid' => $param['uid'] ?? 0];
            } else {//职组
                //校验
                $pid_is_exist = $this->checkIdIsExist($param['id']);
                if (!$pid_is_exist) {
                    throw new ValidationException('不是父职组');
                }
                if (is_array($param['competency_ids'])) {

                    if (count($param['competency_ids']) != count(array_unique($param['competency_ids']))) {
                        throw new \Exception('专业胜任力重复');
                    }
                    $param['competency_ids'] = implode(',', $param['competency_ids']);

                }


                $save_data = ['name' => trim($param['name']), 'functional_competency_ids' => $param['competency_ids'] ?? '', 'update_userid' => $param['uid'] ?? 0, 'updated_uname' => $param['uname'],'updated_at' => gmdate('Y-m-d H:i:s')];
            }
            $bool = $jobGroupModel->save($save_data);

            if ($bool === false) {
                $messages = $jobGroupModel->getMessages();
                throw new \Exception('职组编辑失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('职组编辑异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];

    }

    /**
     * 新建子职组
     * */

    public function addChildJobGroupOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $name_is_repeat = $this->checkNameIsRepeat($param);

            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('job_group_name_exists'));
            }
            $pid_is_exist = $this->checkIdIsExist($param['pid']);
            if (!$pid_is_exist) {
                throw new \Exception('父职组不存在');
            }
            $jobGroupModel = new HrJobGroupModel();
            $bool          = $jobGroupModel->save(['name' => trim($param['name']), 'pid' => $param['pid'], 'add_userid' => $param['uid'], 'created_uname' => $param['uname'],'update_userid' => $param['uid'] ?? 0, 'updated_uname' => $param['uname']]);
            if ($bool === false) {
                $messages = $jobGroupModel->getMessages();
                throw new \Exception('子职组创建失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('职组创建异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 职组详情
     * */

    public function jobGroupDetailOption($id)
    {
        $job_group_detail = HrJobGroupModel::findFirst([
            'columns' => 'name, pid, functional_competency_ids',
            'id = :id:',
            'bind'    => ['id' => $id]])
            ->toArray();
        if (empty($job_group_detail)) {
            return [];
        }
        //一级职组根据胜任力id查询胜任力
        if ($job_group_detail['pid'] == 0) {
            $job_group_detail['functional_competency_ids'] = explode(',', $job_group_detail['functional_competency_ids']);

            $jobCompetency = HrJobCompetencyModel::find([
                'columns'    => 'id, name, description',
                'conditions' => 'is_del =:is_del: and id in ({ids:array})',
                'bind'       => ['is_del' => 0, 'ids' => $job_group_detail['functional_competency_ids']]
            ])->toArray();

            $job_group_detail['job_competency_name'] = $jobCompetency;
        } else {//子职组
            $job_group_name                        = HrJobGroupModel::findFirst([
                'columns' => 'name',
                'id = :id:',
                'bind'    => ['id' => $job_group_detail['pid']]])
                ->toArray();
            $job_group_detail['father_group_name'] = $job_group_name['name'];
        }
        unset($job_group_detail['functional_competency_ids'], $job_group_detail['pid']);

        return $job_group_detail ? $job_group_detail : [];


    }

    /**
     * 删除职组
     * */

    public function delJobGroupById($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //检验该职组力是否存在
            $JobGroupModel = HrJobGroupModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $param['id']],
            ]);

            if (empty($JobGroupModel)) {

                throw new \Exception('job group not exists');
            }


            $isHasJob = HrJobDepartmentRelationModel::findFirst([
                'group_id = :group_id: or group_child_id =:group_id:',
                'bind' => ['group_id' => $param['id']]
            ]);

            if (!empty($isHasJob)) {
                throw new ValidationException(self::$t->_('not_del_group'));

            }
            //添加校验是否存在子职组
            $isHasChildGroup =  HrJobGroupModel::findFirst([
                'pid = :pid: and is_del = :is_del:',
                'bind' => ['pid' => $param['id'], 'is_del' => 0],
            ]);

            if(!empty($isHasChildGroup)){
                throw new ValidationException(self::$t->_('job_exists_subjobfamily_error'));

            }

            // 更新职位表
            $bool = $JobGroupModel->save(['is_del' => 1]);
            if ($bool === false) {
                $messages = $JobGroupModel->getMessages();
                throw new \Exception('职组删除失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('职组删除异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 根据group id 查询职位id
     * */
    private function getJobByGroupId($group_id)
    {
        $job_res = $this->modelsManager->createBuilder()
            ->columns(['group_id', 'job_id'])
            ->from(HrJobDepartmentRelationModel::class)
            ->inWhere('group_id', $group_id)
            ->getQuery()->execute()->toArray();

        if (empty($job_res)) {
            return [];
        }

        $res['job_ids'] = array_column($job_res, 'job_id');

        foreach ($job_res as $key => $value) {
            $res['group_ids'][$value['group_id']][] = $value['job_id'];
        }
        if(is_array($res['group_ids'])){
            foreach ($res['group_ids'] as $k1 =>$v1){
                $res['group_ids'][$k1] =array_unique($v1);
            }
        }

        return $res ?? [];
    }

    /**
     * 根据group id 查询职位id
     * */
    private function getJobByChildGroupId($group_id)
    {
        $job_res = $this->modelsManager->createBuilder()
            ->columns(['group_child_id', 'job_id'])
            ->from(HrJobDepartmentRelationModel::class)
            ->inWhere('group_child_id', $group_id)
            ->getQuery()->execute()->toArray();
        if (empty($job_res)) {
            return [];
        }

        $res['child_job_ids'] = array_column($job_res, 'job_id');

        foreach ($job_res as $key => $value) {
            $res['group_child_ids'][$value['group_child_id']][] = $value['job_id'];
        }

        if(is_array($res['group_child_ids'])){
            foreach ($res['group_child_ids'] as $k1 =>$v1){
                $res['group_child_ids'][$k1] =array_unique($v1);
            }
        }

        return $res ?? [];
    }


    /**
     * 根据group id 查询职位数
     * */
    private function getJobNum($group_id)
    {
        $res = $this->modelsManager->createBuilder()
            ->columns(['group_id', 'count(*) as num'])
            ->from(['hjd' => HrJobDepartmentRelationModel::class])
            ->join(SysDepartmentByModel::class, 'sd.id = hjd.department_id', 'sd')
            ->inWhere('group_id', $group_id)
            ->andWhere('sd.deleted = 0')
            ->groupBy(['group_id'])
            ->getQuery()->execute()->toArray();
        if (empty($res)) {
            return [];
        }
        $res = array_column($res, 'num', 'group_id');

        return $res ?? [];
    }


    /**
     * 根据子职组 group id 查询职位数
     * */
    private function getChildJobNum($group_id)
    {
        $res = $this->modelsManager->createBuilder()
            ->columns(['group_child_id', 'count(*) as num'])
            ->from(HrJobDepartmentRelationModel::class)
            ->inWhere('group_child_id', $group_id)
            ->groupBy(['group_child_id'])
            ->getQuery()->execute()->toArray();
        if (empty($res)) {
            return [];
        }
        $res = array_column($res, 'num', 'group_child_id');

        return $res ?? [];
    }


    /**
     * 获取职位列表
     * @return mixed
     */
    public function getJobGroups()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,name,pid,functional_competency_ids as competency_ids');
        $builder->from(HrJobGroupModel::class);
        $builder->andWhere('is_del=:is_del:', ['is_del' => 0]);
        $list = $builder->getQuery()->execute()->toArray();
        if (empty($list)) {
            return [];
        }
        return $list ? $list : [];
    }

    /**
     * 导出职组列表
     * @param $params
     * @return array
     * @throws \App\Library\Exception\BusinessException
     */
    function exportJobGroupList($params, $locale){
        try {
            $data = $this->getJobGroupListOption($params);//职组导出
            $competency_data = JobCompetencyService::getInstance()->getJobCompetencyOption();
            $competency_list = [];
            foreach($competency_data as $key=>$val){
                if(!empty($val)){
                    foreach($val as $k=>$v){
                        $competency_list[$v['id']] = $v;
                    }
                }
            }
            //越南语表头
            if($locale==Enums::VN_LOCALE) {
                $header = [
                    Enums::VI_EXPORT_JOB_GROUP_ID,                 //职组ID
                    Enums::VI_EXPORT_JOB_GROUP_NAME,               //职组名称
                    Enums::VI_EXPORT_JOB_TITLE_MANAGEMENT_COUNT,   //职位数量
                    Enums::VI_EXPORT_IN_JOB_NUMBER,                //在职人数
                    Enums::VI_EXPORT_PROFESSIONAL_COMPETENCE,      //专业胜任力
                    Enums::VI_EXPORT_PROFESSIONAL_COMPETENCE_DESC, //专业胜任力描述
                ];
            } else {
                $header = [
                    static::$t->_('job_group_id'),                 //职组ID
                    static::$t->_('job_group_name'),               //职组名称
                    //static::$t->_('job_group_child_export'),    //子职组
                    static::$t->_('job_title_management_count'),   //职位管理数量
                    static::$t->_('in_job_number'),                //在职人数
                    static::$t->_('professional_competence'),      //专业胜任力
                    static::$t->_('professional_competence_desc'), //专业胜任力描述
                ];
            }

            $new_data = [];
            if (!empty($data['data']['items'])) {
                foreach ($data['data']['items'] as $key => $val) {
                    $competency_ids = [];
                    if(!empty($val['competency_ids'])){
                        $competency_ids = explode(',', $val['competency_ids']);
                        foreach($competency_ids as $id){
                            $new_data[] = [
                                $val['id'],
                                $val['name'],
                                $val['jd_count'],
                                $val['serving_officer_count'],
                                $competency_list[$id]['name'],
                                $competency_list[$id]['description'],
                            ];
                        }
                    }else {
                        $new_data[] = [
                            $val['id'],
                            $val['name'],
                            $val['jd_count'],
                            $val['serving_officer_count'],
                            '',
                            ''
                        ];
                    }
                }
            }

            $file_name = "job_group_" . date('Y-m-d H:i:s');
            $result = $this->exportExcel($header, $new_data, $file_name);
        }catch (\Exception $e){
            $message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('exportJobGroupList-failed:' . $message);
        }

        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }
        return $result;
    }

}