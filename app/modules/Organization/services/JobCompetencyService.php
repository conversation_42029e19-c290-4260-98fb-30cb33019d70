<?php
/**
 * 职位体系-胜任力模块
 */

namespace App\Modules\Organization\Services;


use App\Library\Enums;
use App\Library\ErrCode;
use App\Modules\Organization\Models\HrJobCompetencyModel;
use App\Library\Validation\ValidationException;

class JobCompetencyService extends BaseService
{
    //创建胜任力
    public static $validate_competency_add_params = [
        'name'        => 'Required|StrLenGeLe:1,50|>>>:name error', //胜任力
        'type'        => 'Required|IntIn:1,2,3', //胜任力类型
        'description' => 'StrLenGeLe:0,500|>>>:description error', //描述
    ];
    //更新胜任力
    public static $validate_competency_edit_params = [
        'id'          => 'Required|IntGt:0|>>>:id error',
        'name'        => 'Required|StrLenGeLe:1,50|>>>:name error', //胜任力Required|StrLenGeLe:1,100|>>>:job_name error
        'type'        => 'Required|IntIn:1,2,3', //胜任力类型
        'description' => 'StrLenGeLe:0,500|>>>:description error', //描述
    ];
    public static $validate_competency_del_params = [
        'id' => 'Required|IntGt:0|>>>:id error',
    ];


    public static $job_competency_type = [1 => '领导胜任力', 2 => '专业胜任力', 3 => '核心胜任力'];

    const JOB_COMPETENCY_TYPE_1 = 'job_competency_type_1';
    const JOB_COMPETENCY_TYPE_2 = 'job_competency_type_2';
    const JOB_COMPETENCY_TYPE_3 = 'job_competency_type_3';
    static $job_competency_types = [
        1 => self::JOB_COMPETENCY_TYPE_1,
        2 => self::JOB_COMPETENCY_TYPE_2,
        3 => self::JOB_COMPETENCY_TYPE_3,
    ];

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 获取胜任力
     * @return mixed
     * */
    public function getJobCompetencyOption()
    {
        $list = HrJobCompetencyModel::find([
            'columns'    => "id,name, type,description",
            'conditions' => 'is_del=0'
        ])->toArray();

        $job_competency_list = [];
        if (empty($list)) {
            return [];
        }
        foreach ($list as $key => $value) {//分组
            foreach (self::$job_competency_type as $k1 => $v1) {
                if ($value['type'] == $k1) {
                    $job_competency_list['type' . $k1][] = $value;
                }
            }
        }

        return $job_competency_list ? $job_competency_list : [];
    }

    /**
     * 获取胜任力映射关系列表
     * 返回结构：[id=>[name=>'',description=>'']]
     */
    public function getJobCompetencyMapList()
    {
        $list = HrJobCompetencyModel::find([
            'columns'    => "id,name, description",
            'conditions' => 'is_del=0'
        ])->toArray();

        if (empty($list)) return [];

        return array_column($list, null, 'id');
    }

    /**
     * 添加胜任力
     * @return mixed
     * */
    public function addJobCompetencyOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $name_is_repeat = $this->checkNameIsRepeat($param);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('competency_name_exists'));
            }

            $jobCompetencyModel = new HrJobCompetencyModel();
            $bool               = $jobCompetencyModel->save(['name' => trim($param['name']), 'type' => $param['type'], 'description' => $param['description']]);
            if ($bool === false) {
                $messages = $jobCompetencyModel->getMessages();
                throw new \Exception('胜任力创建失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('胜任力更新异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 编辑胜任力
     * */
    public function editJobCompetencyOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //检验该胜任力是否存在
            $JobCompetencyModel = HrJobCompetencyModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $param['id']],
            ]);
            if (empty($JobCompetencyModel)) {
                throw new \Exception('competency not exists');
            }

            // 校验名称是否重复
            $name_is_repeat = $this->checkNameIsRepeat($param, 1);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('competency_name_exists'));
            }

            // 更新表
            $bool = $JobCompetencyModel->save(['name' => trim($param['name']), 'type' => $param['type'], 'description' => $param['description'],'updated_at'=> gmdate('Y-m-d H:i:s')]);
            if ($bool === false) {
                $messages = $JobCompetencyModel->getMessages();
                throw new \Exception('胜任力更新失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('胜任力创建异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }

    /**
     * 检查胜任力名称是否重复
     * @param $job_name
     * @return bool
     */
    private function checkNameIsRepeat($param, $competency_id = 0)
    {

        if ($competency_id) { //编辑操作
            $res = HrJobCompetencyModel::findFirst([
                'name = :name: and id <> :id:',
                'bind' => ['name' => $param['name'], 'id' => $param['id']],
            ]);
        } else { //新增操作
            $res = HrJobCompetencyModel::findFirst([
                'name = :name:',
                'bind' => ['name' => $param['name']],
            ]);
        }
        if ($res) {
            return true;
        }
        return false;
    }

    /**
     * 删除胜任力
     * 逻辑删除
     * */
    public function delJobCompetencyOption($param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //检验该胜任力是否存在
            $JobCompetencyModel = HrJobCompetencyModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $param['id']],
            ]);
            if (empty($JobCompetencyModel)) {
                throw new \Exception('job Competency not exists');
            }


            // 更新职位表
            $bool = $JobCompetencyModel->save(['is_del' => 1]);
            if ($bool === false) {
                $messages = $JobCompetencyModel->getMessages();
                throw new \Exception('胜任力删除失败， ' . json_encode($messages, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('胜任力删除异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }

    /**
     * 胜任力 分页列表 数据
     * @param array $condition
     * @return array
     */
    public function getJobCompetencyList(array $condition)
    {
        $pageSize = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $pageNum  = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset   = $pageSize * ($pageNum - 1);

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $pageNum,
                'per_page'     => $pageSize,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'id',
                'name',
                'type',
                'description',
                'created_at',
                'updated_at',
            ]);
            $builder->from(['main' => HrJobCompetencyModel::class]);
            //组合搜索条件
            if (isset($condition['type']) && !empty($condition['type'])) {
                $builder->andWhere('type = :type:', ['type' => $condition['type']]);
            }
            if (isset($condition['name']) && !empty($condition['name'])) {
                $builder->andWhere('(name LIKE :name:)', ['name' => "{$condition['name']}%"]);
            }
            $builder->andWhere('is_del=:is_del:', ['is_del' => 0]);

            $count           = $builder->getQuery()->execute()->count();
            $competency_type = [
                '1' => self::$t->_('competency_type_1'),
                '2' => self::$t->_('competency_type_2'),
                '3' => self::$t->_('competency_type_3'),
            ];

            if ($count > 0) {
                if (!isset($condition['is_download']) || !$condition['is_download']) {
                    $builder->limit($pageSize, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                if (!empty($items)) {
                    foreach ($items as $key => &$value) {
                        $value['type_name'] = $competency_type[$competency_type[$value['type']]] ?? '';
                        $value['updated_at'] =date('Y-m-d H:i:s', strtotime($value['updated_at']) + get_sys_time_offset() * 3600);
                        $value['created_at'] =date('Y-m-d H:i:s', strtotime($value['created_at']) + get_sys_time_offset() * 3600);
                    }
                }
            }

            $data['items'] = $items ?? [];

            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();

        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('胜任力列表异常信息:' . $real_message);
        }
        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR) {
            $code    = ErrCode::$SUCCESS;
            $message = $real_message;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出胜任力列表
     * @param $params
     * @return array
     */
    function exportJobCompetencyList($params, $locale){
        try {
            $data = $this->getJobCompetencyList($params);

            //越南语表头
            if($locale==Enums::VN_LOCALE) {
                $header = [
                    Enums::VI_EXPORT_COMPENTENCE_NAME, //胜任力名称
                    Enums::VI_EXPORT_COMPENTENCE_TYPE,    //胜任力类型
                    Enums::VI_EXPORT_PROFESSIONAL_COMPETENCE_DESC, //专业胜任力描述
                ];
            }else {
                $header = [
                    static::$t->_('competence_name'), //胜任力名称
                    static::$t->_('competence_type'),    //胜任力类型
                    static::$t->_('professional_competence_desc'), //专业胜任力描述
                ];
            }

            $new_data = [];
            if (!empty($data['data']['items'])) {
                foreach ($data['data']['items'] as $key => $val) {
                    $new_data[] = [
                        $val['name'],
                        static::$t->_(self::$job_competency_types[$val['type']]),
                        $val['description']
                    ];
                }
            }

            $file_name = "job_competency_" . date('Y-m-d H:i:s');
            $result = $this->exportExcel($header, $new_data, $file_name);
        }catch (\Exception $e){
            $message = $e->getMessage();
            $logger = $this->getDI()->get('logger');
            $logger->warning('exportJobjdlist-failed:' . $message);
        }

        if ($result['code'] == ErrCode::$SUCCESS) {
            $result['message'] = 'success';
        } else {
            $result['code'] = ErrCode::$SYSTEM_ERROR;
            $result['message'] = 'error';
            $result['data'] = '';
        }
        return $result;
    }

}