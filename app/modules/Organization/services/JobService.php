<?php
/**
 * 职位体系-职位模块
 */

namespace App\Modules\Organization\Services;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use app\library\Enums\JobTitleEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\backyard\HrJobDepartmentRelationChangeModel;
use App\Models\backyard\SettingEnvModel;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Hc\Models\HrInterviewModel;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Hc\Services\HcShareBudgetService;
use App\Modules\Hc\Services\SysService;
use App\Modules\Organization\Models\HrJdModel;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Models\HrJobGroupModel;
use App\Modules\Organization\Models\HrJobLogModel;
use App\Modules\Organization\Models\HrJobTitleByModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentByModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\TalentReview\Services\TeamAnalysisService;
use App\Modules\Transfer\Models\HrHcModel;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use App\Library\Enums\StaffInfoEnums;

class JobService extends BaseService
{
    public const EXPORT_MAX_ROW_COUNT = 10000;
    public const IMPORT_MAX_ROW_COUNT = 100;
    public const MIN_VALID_PLAN_HC_NUMBER = 0;
    public const MAX_VALID_PLAN_HC_NUMBER = 50000;
    public const ADD_IMPORT_RESULT_COLUMN_NUMBER = 5;
    public const ADD_IMPORT_RESULT_COLUMN_NUMBER_NEW = 6;

    private static $instance;

    //新建职位
    public static $validate_add = [
        'job_name' => 'Required|StrLenGeLe:1,100|>>>:job_name max length limit 100',
    ];

    //编辑职位
    public static $validate_edit = [
        'job_id'      => 'Required|IntGt:0|>>>:job_id error',
        'job_name'    => 'Required|StrLenGeLe:1,100|>>>:job_name max length limit 100',
    ];

    //职位关联
    public static $validate_bind = [
        'job_id'                 => 'Required|IntGt:0|>>>:job_id error', //职位ID
        'department_id'          => 'Required|IntGt:0|>>>:department_id error', //部门/组织ID
        'job_level'              => 'Required|Str|>>>:job_level error', //职级
        'group_id'               => 'Required|IntGt:0|>>>:group_id error', //职组ID
//        'work_place_type'        => 'Required|IntIn:1,2,3,4,5|>>>:work_place_type error', //工作地点类型 1：网点，2：职能总部，3：业务总部,4:分拨，5：门店
        'report_job_id'          => 'Required|IntGt:0|>>>:report_job_id error', //汇报职位ID
        'jd_id'                  => 'Required|IntGt:0|>>>:jd_id error', //岗位ID
        'job_requirements_jybj'  => 'Required|StrLenGeLe:1,5000|>>>:job_requirements_jybj max length 5000 ', //职位要求-教育背景
        'job_requirements_zyjl'  => 'Required|StrLenGeLe:1,5000|>>>:job_requirements_zyjl max length 5000', //职位要求-专业经历
        'job_competency_zy'      => 'Required|ArrLenGe:1|>>>:job_competency_zy error', //专业胜任力
    ];

    //职位关联列表
    public static $validate_bind_detail = [
        'bind_id' => 'Required|IntGt:0|>>>:bind_id error',
    ];

    //职位关联详情导出
    public static $validate_bind_detail_export = [
        'bind_id'                => 'Required|IntGt:0|>>>:bind_id error',
        'export_field'           => 'Required|StrLenGeLe:1,500', //获取导出字段
    ];

    //职位关联列表
    public static $validate_bind_list = [
        'job_id'        => 'IntGt:0|>>>:job_id error',  //职位ID
        'report_job_id' => 'IntGt:0|>>>:report_job_id error', //汇报职位ID
        'department_id' => 'IntGt:0|>>>:department_id error', //部门ID
        'group_id'      => 'IntGt:0|>>>:group_id error', //职组ID
        'jd_id'         => 'IntGt:0|>>>:jd_id error', //岗位ID
    ];

    //导入历史记录
    public static $validate_import_list = [
        'page_size' => 'Required|IntGt:0',
        'page_num' => 'Required|IntGt:0',
    ];


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getOptionsDefault()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //todo 获取job(职位）列表
            $data['job_data'] = $this->getJobOption();
            //todo 获取职位 职级
            $data['job_level'] = Enums::$job_level;
            //todo 获取职组数据
            $data['job_group_data'] = JobGroupService::getInstance()->getJobGroupOption();
            //todo 获取 jd（岗位）列表
            $data['job_jd_data'] = JobJdService::getInstance()->getJobJdOption();
            //todo 获取胜任力选项列表
            $data['competency_data'] = JobCompetencyService::getInstance()->getJobCompetencyOption();

            //工作天数&轮休规则
            $working_day_rest_type = Enums::$working_day_rest_type;
            if (in_array(get_country_code(),
                [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::ID_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE])) {
                $working_day_rest_type = Enums::$all_working_day_rest_type;
            }

            foreach ($working_day_rest_type as $key => $value) {
                $data['working_day_rest_day'][] = ['id' => (string)$key, 'name' => static::$t->_($value)];
            }

            $positionCostData = $this->getPositionCostList();
            $data['position_type'] = $positionCostData['position_type'];
            $data['cost_type'] = $positionCostData['cost_type'];


        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-getOptionsDefault-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    public function getCPosition()
    {
        return array_values(array_filter((new SettingEnvModel())->getSetVal('c_position', ',')));
    }

    /**
     * 获取有效的职位性质
     * @return array
     */
    public function getEffectivePositionType(): array
    {
        $result = [];
        foreach (HrJobDepartmentRelationModel::$position_type_list as $key => $value) {
            if (empty($key)) {
                continue;
            }
            $result[] = ['value' => intval($key), 'key' => static::$t->_($value)];
        }
        return $result;
    }


    //获取职位性质，成本类型 枚举
    public function getPositionCostList()
    {
        $data = [];
        foreach (HrJobDepartmentRelationModel::$position_type_list as $key => $value) {
            $data['position_type'][] = ['value' => (string)$key, 'label' => static::$t->_($value)];
        }


        foreach (HrJobDepartmentRelationModel::$cost_type_list as $key => $value) {
            $data['cost_type'][] = ['value' => (string)$key, 'label' => static::$t->_($value)];
        }

        return $data;
    }

    public function getPositionCostListToRpc($locale, $params)
    {
        BaseService::setLanguage($locale['locale']);
        $data = $this->getPositionCostList();
        return [
            'code'    => ErrCode::$SUCCESS,
            'message' => 'success',
            'data'    => $data,
        ];
    }

    /**
     * 职位搜索
     * @param $params
     * @return array
     */
    public function searchJobTitle($params): array
    {
        $search_name    = $params['search_name'] ?? '';
        $page_size      = $params['page_size'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;
        $status         = $params['status'] ?? 0;
        $searchNameType = $params['search_name_type'] ?? JobTitleEnums::SEARCH_NAME_TYPE_NAME;

        $columns = 'id,job_name as name';
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(HrJobTitleByModel::class);

        if ($status) {
            $builder->andWhere('status = :status:', ['status' => $status]);
            //精确查找
            $realBind['status'] = $status;
            $realCondition[] = 'status = :status:';
        }
        $realJobTitle = [];
        if (!empty($search_name)) {
            $filter               = 'job_name like :job_name:';
            $bind                 = [
                'job_name' => '%' . $search_name . '%',
            ];
            if ($searchNameType == JobTitleEnums::SEARCH_NAME_TYPE_ALL) {
                $filter     .= ' OR id = :id:';
                $bind['id'] = $search_name;
            }
            $builder->andWhere($filter, $bind);

            //精确查找
            $realBind['job_name'] = $search_name;
            $realCondition[]      = 'job_name = :job_name:';
            $jobTitle             = HrJobTitleByModel::findFirst([
                'columns'    => $columns,
                'conditions' => implode(' AND ', $realCondition),
                'bind'       => $realBind,
            ]);
            if ($jobTitle) {
                $realJobTitle[] = $jobTitle->toArray();
                $builder->andWhere('id != :rela_id:', ['rela_id' => $jobTitle->id]);
            }
        }

        $builder->orderBy('job_name ASC');
        $builder->limit($page_size);
        $list = $builder->getQuery()->execute()->toArray();

        $data['job_list'] = array_merge($realJobTitle,$list);

        return $data;
    }

    public function getRelationJobsByDepartId($departmentId)
    {
        $code = ErrCode::$SUCCESS;
        $msg = "success";
        $data = [];
        try {
            $allJobs = $this->getJobOption();
            $relationJobs = $this->getJobsForCurrentDepartment($departmentId);
            $relationMap = array_column($relationJobs, null, 'id');
            $relationJob = $noRelationJob = [];
            // is_relation用于前端展示，部门与职位是否绑定：1是，0否
            foreach($allJobs as $v){
                if(! empty($relationMap[$v['id']])) {
                    $relationJob[] = ['id' => $v['id'], 'name' => $relationMap[$v['id']]['name'], 'is_relation' => 1];
                    continue;
                }
                $noRelationJob[] = ['id' => $v['id'], 'name' => $v['name'], 'is_relation' => 0];
            }
            $data['relation_job'] = $relationJob;
            $data['no_relation_job'] = $noRelationJob;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $msg = static::$t->_('retry_later');
            $this->getDI()->get('logger')->warning('职位体系-getRelationJobsByDepartId-异常信息: ' . $e->getMessage());
        }
        return ['code' => $code, 'message' => $msg, 'data' => $data];
    }

    /**
     * 获取职位列表筛选项
     * @return mixed
     */
    public function getJobOption()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,job_name as name');
        $builder->from(HrJobTitleByModel::class);
        $builder->andWhere('status = :status:', ['status' => 1]);
        $list = $builder->getQuery()->execute()->toArray();
        return $list ? $list : [];
    }

    /**
     * 添加职位
     * @param $job_name
     * @return array
     */
    public function addJob(string $job_name, array $user)
    {
        $job_name = trim($job_name);
        $code     = ErrCode::$SUCCESS;
        $message  = $real_message = '';
        $data     = [];

        try {

            $name_is_repeat = self::checkNameIsRepeat($job_name);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('job_name_exists'));
            }
            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            $jobModel = new HrJobTitleByModel();
            if ($jobModel->save(['job_name' => $job_name]) == false) {
                $messages = $jobModel->getMessages();
                $save_err = '';
                foreach ($messages as $message) {
                    $save_err .= $message . PHP_EOL;
                }
                $db->rollback();
                throw new \Exception('职位创建失败，sql错误信息： ' . $save_err);
            }
            //添加日志
            $save_log_result = (new JobLogService())->saveLog([
                'log_data'  => ['job_name' => ''],
                'save_data' => ['job_name' => $job_name],
                'type'      => HrJobLogModel::JOB_LOG_TYPE_NEW_JOB,
                'hr_log_id' => 0,
                'user'      => $user,
                'hr_job_id' => $jobModel->id,
            ]);
            if(!$save_log_result){
                $db->rollback();
                throw new \Exception('添加变更记录失败：' . json_encode(['job_name'=>$job_name]) );
            }
            $db->commit();

            ThirdApiService::addJobTitle($jobModel->id,$job_name);

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-addJob-异常信息: ' . $real_message);
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 修改职位名称
     * @param $job_name
     * @return array
     */
    public function editJob(int $job_id, string $job_name_new, array $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';
        $data         = [];
        $job_name_new = trim($job_name_new);

        try {

            //检验职位是否存在
            $jobModel = HrJobTitleByModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $job_id],
            ]);
            if (empty($jobModel)) {
                throw new \Exception('not exists');
            }
            $job_name = $jobModel->job_name;
            // 校验名称是否重复
            $name_is_repeat = self::checkNameIsRepeat($job_name_new, $job_id);
            if ($name_is_repeat) {
                throw new ValidationException(self::$t->_('job_name_exists'));
            }
            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            // 更新职位表
            if ($jobModel->save(['job_name' => $job_name_new , 'updated_at' => gmdate('Y-m-d H:i:s')]) == false) {
                $messages = $jobModel->getMessages();
                $save_err = '';
                foreach ($messages as $message) {
                    $save_err .= $message . PHP_EOL;
                }
                $db->rollback();
                throw new \Exception('职位名称修改失败，sql错误信息： ' . $save_err);
            }
            //添加日志
            $save_log_result = (new JobLogService())->saveLog([
                'log_data'  => ['job_name' => $job_name],
                'save_data' => ['job_name' => $job_name_new],
                'type'      => HrJobLogModel::JOB_LOG_TYPE_MODIFY_JOB,
                'hr_log_id' => 0,
                'user'      => $user,
                'hr_job_id' => $job_id,
            ]);
            if(!$save_log_result){
                $db->rollback();
                throw new \Exception('添加变更记录失败：' . json_encode(['job_name'=>$job_name]).json_encode(['job_name'=>$job_name_new]) );
            }
            $db->commit();

            ThirdApiService::updateJobTitle($job_id,$job_name_new);


        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-editJob-异常信息: ' . $real_message);
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 职位关联列表
     * @param array $condition
     * @return array
     */
    public function bindJobList(array $condition , $staff_info_id = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $page_size = empty($condition['page_size']) ? 20 : $condition['page_size'];
        $page_num  = empty($condition['page']) ? 1 : $condition['page'];
        $offset    = $page_size * ($page_num - 1);
        $data      = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            //获取count数
            $builder = $this->getListCond($condition, $staff_info_id); //职位关联关系列表-列表总数
            $builder->columns('count(relation.id) as cou');
            $count   = $builder->getQuery()->getSingleResult()->cou ?? 0;

            //获取职位关联列表
            $builder = $this->getListCond($condition , $staff_info_id); //职位关联关系列表-列表查询
            if ($count > 0) {
                $builder->columns('relation.*');
                $builder->orderBy('relation.updated_at desc');
                $builder->limit($page_size, $offset);

                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->buildJobBindList($items);
            }

            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = intval($count);
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-bindJobList-异常信息: '.$real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出 职位关联 列表
     * @param array $condition
     * @return array
     */
    public function exportBindJobList(array $condition, $staff_info_id = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //获取builder
            $builder = $this->getListCond($condition , $staff_info_id); //职位关联关系列表-列表导出
            $builder->columns('relation.*');
            $builder->limit(10000);
            $data_list = $builder->getQuery()->execute()->toArray();
            if (empty($data_list)) {
                throw new ValidationException('no valid data');
            }

            //获取转义相关数据
            [$job_id_name_map, $jd_id_name_map, $department_id_name_map, $job_group_id_name_map] = $this->getTitleTransferMap($data_list);

            //获取部门列表map
            $department_list = SysDepartmentModel::find([
                'columns' => "id,name,ancestry,ancestry_v3,type,level,company_id,company_name,country_code",
            ])->toArray();
            if (!empty($department_list)) {
                $department_list = array_column($department_list, null, 'id');
            }

            if (BudgetService::getInstance()->isConfiguredBudget()) {
                $key = 'hc_budget';
            } else {
                $key = 'job_plan_hc_nums';
            }

            $excel_header = [
                static::$t->_('job_id'),               //职位ID
                static::$t->_('job_name'),             //职位名称
                static::$t->_('job_public_name'),      // 公开职位名称
                static::$t->_('department_country'),      // 国家
                static::$t->_('job.department_id'),      // 部门ID
                static::$t->_('job.department_name'),    // 部门名称
                static::$t->_('department.group_ceo'),
                static::$t->_('department.c_level'),
                static::$t->_('department.bu'),
                static::$t->_('department.level_1'),
                static::$t->_('department.level_2'),
                static::$t->_('department.level_3'),
                static::$t->_('department.level_4'),
                static::$t->_('job_group_id'),          // 职组ID
                static::$t->_('job_group'),             // 职组
                static::$t->_('job_group_child_id'),    // 子职组ID
                static::$t->_('job_group_child'),       // 子职组
                static::$t->_('job_level'),             // 职级
//                static::$t->_('job_work_place_type'),   // 工作地点类型
                static::$t->_('job_report_job_id'),     // 汇报职位ID
                static::$t->_('job_report_job_name'),   // 汇报职位
                static::$t->_('job_jd_id'),           // JDID
                static::$t->_('job_jd_name'),           // JD名称
                static::$t->_('job_jd_description'),    // JD描述
                static::$t->_('jd_desc_supply'),        // JD描述备注
                static::$t->_('job_requirements_jybj'),    //职位要求-教育背景
                static::$t->_('job_requirements_zyjl'),    //职位要求-专业经历
                static::$t->_('job_requirements_other'),    //职位要求_其他
                static::$t->_('working_day_rest_type'), // 工作天数轮休规则
                static::$t->_($key),                    // 计划hc
                static::$t->_('job_work_hc_nums'),      // 在职hc
                static::$t->_('job_surplus_hc_nums'),   // 剩余hc
                static::$t->_('position_type'),         // 职位性质
                static::$t->_('cost_type'),             // 成本类型
            ];
            $config = [
                'path' => sys_get_temp_dir() . '/',
            ];
            $excel = new \Vtiful\Kernel\Excel($config);

            //此处会自动创建一个工作表
            $fileObject = $excel->fileName('oa-joblist-' . time() . '.xlsx');
            $fileObject->header($excel_header);


            //获取国家简称
            $country_code_list  = EnumsService::getInstance()->getCountryListV2();
            $country_code_list  = !empty($country_code_list['country_list']) ? $country_code_list['country_list'] : [];
            $isConfiguredBudget = BudgetService::getInstance()->isConfiguredBudget();
            $c_position = $this->getCPosition();

            foreach ($data_list as $item) {
                $item['job_name']             = $job_id_name_map[$item['job_id']] ?? ''; //职位名称
                $item['group_name']           = $job_group_id_name_map[$item['group_id']] ?? ''; //职组名称
                $item['group_child_name']     = $job_group_id_name_map[$item['group_child_id']] ?? ''; //子职组名称
                $item['job_level']            = self::genJobLevleText($item['job_level']); //职级
                $item['report_job_name']      = $job_id_name_map[$item['report_job_id']] ?? ''; //汇报职位名称
                $item['jd_name']              = $jd_id_name_map[$item['jd_id']] ?? ''; //JD名称
//                $item['work_place_type_name'] = $item['work_place_type'] ? static::$t->_('work_place_type_' . $item['work_place_type']) : '';
                $item['working_day_rest_type'] = !empty($item['working_day_rest_type']) ? self::getWorkingDayRestType($item['working_day_rest_type']) : '';


                //获取在职hc数量和剩余hc数量
                $item['work_hc_nums']    = self::getStaffnums($item['department_id'], $item['job_id']);//在职人数-列表导出
                $surplus_hc_num          = $item['plan_hc_nums'] - $item['work_hc_nums'];
                $item['surplus_hc_nums'] = $surplus_hc_num > 0 ? $surplus_hc_num : 0; //剩余hc人数

                $department_level = $this->getDepartmentLevel($item['department_id'], $department_list);
                if ($isConfiguredBudget) {
                    $planHcNums = $item['plan_hc_nums'];
                } else {
                    $planHcNums = $item['is_plan'] == HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE
                        ? $item['plan_hc_nums'] : "";
                }

                $position_type_text = isset(HrJobDepartmentRelationModel::$position_type_list[$item['position_type']]) && !empty($item['position_type']) ? static::$t->_(HrJobDepartmentRelationModel::$position_type_list[$item['position_type']]) : '';
                $cost_type_text = isset(HrJobDepartmentRelationModel::$cost_type_list[$item['cost_type']]) && !empty($item['cost_type']) ? static::$t->_(HrJobDepartmentRelationModel::$cost_type_list[$item['cost_type']]) : '';


                //部门ID
                $department_id = $item['department_id'] ?? 0;
                $country_code  = $department_list[$department_id]['country_code'] ?? '';


                if (in_array($item['job_id'], $c_position)) {
                    $item['job_level'] = '';
                }

                //组装excel数据
                $excel_result[] = [
                    $item['job_id'],
                    $item['job_name'],
                    $item['public_job_name'],
                    $country_code_list[$country_code] ?? $country_code,               //国家
                    $department_id,                                                   //部门ID
                    $department_list[$department_id]['name'] ?? '',                   //部门名称
                    $department_level[0] ?? '-',
                    $department_level[1] ?? '-',
                    $department_level[2] ?? '-',
                    $department_level[3] ?? '-',
                    $department_level[4] ?? '-',
                    $department_level[5] ?? '-',
                    $department_level[6] ?? '-',
                    !empty($item['group_id']) ? $item['group_id'] : '-',
                    $item['group_name'],
                    !empty($item['group_child_id']) ? $item['group_child_id'] : '-',
                    $item['group_child_name'],
                    $item['job_level'],
//                    $item['work_place_type_name'],
                    $item['report_job_id'],
                    $item['report_job_name'],
                    !empty($item['jd_id']) ? $item['jd_id'] : '-',
                    $item['jd_name'],
                    replace_html_tags(htmlspecialchars_decode($item['jd_desc'],ENT_QUOTES)),
                    replace_html_tags(htmlspecialchars_decode($item['jd_desc_supply'],ENT_QUOTES)),
                    replace_html_tags(htmlspecialchars_decode($item['job_requirements_jybj'],ENT_QUOTES)),
                    replace_html_tags(htmlspecialchars_decode($item['job_requirements_zyjl'],ENT_QUOTES)),
                    replace_html_tags(htmlspecialchars_decode($item['job_requirements_other'],ENT_QUOTES)),
                    $item['working_day_rest_type'],
                    $planHcNums,
                    $item['work_hc_nums'],
                    $item['surplus_hc_nums'],
                    $position_type_text,
                    $cost_type_text,
                ];

                $fileObject->data($excel_result);
                unset($excel_result);
            }
            $filePath = $fileObject->output();
            $file_arr = OssHelper::uploadFile($filePath);

            $data['file_url'] = $file_arr ? $file_arr['object_url'] : '';
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-exportBindJobList-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * @description 关联职位
     * @param array $params
     * @param array $user
     * @return array
     */
    public function bindJob(array $params, array $user)
    {

        if (BudgetService::getInstance()->isConfiguredBudget()) {
           $data = $this->bindJobCommonV2($params, $user);
        } else {
            $data = $this->bindJobCommon($params, $user);
        }
        return $data;
    }

    /**
     * @description 关联职位
     * @param array $params
     * @param array $user
     * @return array
     */
    public function bindJobCommonV2(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检验职位是否存在
            $jobModel = HrJobTitleByModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $params['job_id']],
            ]);
            if (empty($jobModel)) {
                throw new ValidationException('job not exists，job_id:' . $params['job_id']);
            }
            // 职组下的胜任力为必填项，如果没有则报错
            $jobGroupData = JobGroupService::getInstance()->findOneById($params['group_id']);
            $functionalCompetencyIds = empty($jobGroupData['functional_competency_ids']) ? [] : explode(',', $jobGroupData['functional_competency_ids']);
            if($functionalCompetencyIds && $params['job_competency_zy']){
                $insertFunctionalCompetencyIds = [];
                foreach($params['job_competency_zy'] as $val){
                    $insertFunctionalCompetencyIds[] = $val['id'];
                }
                foreach($functionalCompetencyIds as $val){
                    if(in_array($val, $insertFunctionalCompetencyIds)) {
                        continue;
                    }
                    throw new ValidationException(static::$t->_('jobgroup_functional_competency_not_empty'));
                }
            }

            if(empty($params['id'])) {
                //检验职位绑定关系是否存在
                $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                    'department_id = :department_id: and job_id = :job_id:',
                    'bind' => ['department_id' => $params['department_id'], 'job_id' => $params['job_id']],
                ]);
                if(!empty($jobRelationModel)) {
                    throw new ValidationException(static::$t->_('department_job_relation_isset'));
                }
            } else {
                //检验职位绑定关系是否存在
                $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $params['id']],
                ]);

                if(empty($jobRelationModel)) {
                    throw new ValidationException(static::$t->_('data_error_prohibit_resubmission'));
                }

                if($jobRelationModel->department_id != $params['department_id'] || $jobRelationModel->job_id != $params['job_id']) {
                    throw new ValidationException(static::$t->_('data_error_prohibit_resubmission'));
                }
            }

            $log_data = [];
            $save_data_add = [];
            $log_data_add = [];
            $log_type = HrJobLogModel::JOB_LOG_TYPE_NEW_RELATION;

            if (empty($jobRelationModel)) {
                //不存在绑定关系，首次关联，记录下创建人信息
                $jobRelationModel = new  HrJobDepartmentRelationModel();
                $save_data_add    = ['created_uid'   => $user['id'],
                                     'created_uname' => $user['name'],
                                     'created_at'    => gmdate('Y-m-d H:i:s'),
                                     'updated_at'    => gmdate('Y-m-d H:i:s'),
                ];
                $budgetCount = 0;
                $log_data_add = ['plan_hc_nums' => 'null'];
            } else {
                //添加日志
                //查询老数据
                $log_data = $this->bindJobDetail($jobRelationModel->id);
                $log_data = $log_data['data'];
                $log_type = HrJobLogModel::JOB_LOG_TYPE_EDIT_RELATION;
                $budgetCount = $jobRelationModel->plan_hc_nums;
            }
            $working_day_rest_type = implode(',', $params['working_day_rest_type']);

            //获取是否共享
            //[修改校验][仅预算人数调整时，才校验]修改后HC预算-在职人数-停职人数-待离职人数-待入职人数-招聘中人数-审批中HC-待转岗<0时，不允许修改
            //[同步预算]如共享并且修改预算人数，需要同步的修改其他共享职位的预算人数
            $shareService = HcShareBudgetService::getInstance()->init();
            $isShare = $shareService->isShare($params['department_id'], $params['job_id']);

            //不是共享,但是部门在共享部门里
            if (!$isShare && $log_type == HrJobLogModel::JOB_LOG_TYPE_NEW_RELATION && empty($params['confirm']) &&
                in_array($params['department_id'], $shareService->getAllShareDepartment()) ) {

                //给个提示“该部门下职位存在共享”
                return ['code' => $code, 'message' => 'success', 'data' => ['need_notice_share' => 1]];
            }

            $errMsg = $this->checkRecruit($params['department_id'], $params['job_id'], intval($params['plan_hc_nums']));
            if (!empty($errMsg)) {
                throw new ValidationException(static::$t->_($errMsg));
            }

            //组织变更数据
            //$this->saveHcChangeLog($params['department_id'], $params['job_id'], $budgetCount, intval($params['plan_hc_nums']), $user['id']);

            $save_data = [
                'department_id'          => $params['department_id'] ?? '0',
                'job_id'                 => $params['job_id'] ?? '0',
                'job_level'              => $params['job_level'] ?? '',
                'public_job_name'        => isset($params['public_job_name']) ? trim($params['public_job_name']) : '',
                'group_id'               => $params['group_id'] ?? '0',
                'group_child_id'         => isset($params['group_child_id']) && $params['group_child_id'] ? $params['group_child_id'] : '0',
                'work_place_type'        => $params['work_place_type'] ?? '0',
                'report_job_id'          => $params['report_job_id'] ?? '0',
                'jd_id'                  => $params['jd_id'] ?? '0',
                'jd_desc'                => isset($params['jd_desc']) ? htmlspecialchars($params['jd_desc'],ENT_QUOTES) : '',
                'jd_desc_supply'         => isset($params['jd_desc_supply']) ? htmlspecialchars($params['jd_desc_supply'],ENT_QUOTES) : '',
                'plan_hc_nums'           => intval($params['plan_hc_nums']),
                'job_requirements_jybj'  => isset($params['job_requirements_jybj']) ? htmlspecialchars($params['job_requirements_jybj'],ENT_QUOTES) : '',
                'job_requirements_zyjl'  => isset($params['job_requirements_zyjl']) ? htmlspecialchars($params['job_requirements_zyjl'],ENT_QUOTES) : '',
                'job_requirements_other' => isset($params['job_requirements_other']) ? htmlspecialchars($params['job_requirements_other'],ENT_QUOTES) : '',
                'job_competency_zy'      => isset($params['job_competency_zy']) && $params['job_competency_zy'] ? json_encode($params['job_competency_zy']) : '',
                'job_competency_ld'      => isset($params['job_competency_ld']) && $params['job_competency_ld'] ? json_encode($params['job_competency_ld']) : '',
                'job_competency_hx'      => isset($params['job_competency_hx']) && $params['job_competency_hx'] ? json_encode($params['job_competency_hx']) : '',
                'updated_uid'            => $user['id'],
                'updated_uname'          => $user['name'],
                'updated_at'             => gmdate('Y-m-d H:i:s'),
                'working_day_rest_type'  => $working_day_rest_type,
                'upload_file'             => $params['upload_file'] ? json_encode($params['upload_file'], JSON_UNESCAPED_UNICODE): '',
                'position_type'          => !empty($params['position_type'])? $params['position_type'] : 0,
                'cost_type'              => !empty($params['cost_type'])? $params['cost_type'] : 0,
            ];

            $save_data = array_merge($save_data, $save_data_add);
            if (!$jobRelationModel->save($save_data)) {
                $messages = $jobRelationModel->getMessages();
                $save_err = '';
                foreach ($messages as $message) {
                    $save_err .= $message . PHP_EOL;
                }

                throw new \Exception('职位关联失败，sql错误信息： ' . $save_err);
            }

            //增加 log
            $jobServer = new JobLogService();

            //查询老数据
            $save_data = $this->bindJobDetail($jobRelationModel->id);
            if (!empty($params['edit_reason'])) {
                $save_data_array = array_merge($save_data['data'], ['edit_reason' => $params['edit_reason']]);
            } else {
                $save_data_array = $save_data['data'];
            }
            $save_log_result = $jobServer->saveLog([
                'log_data'       => array_merge($log_data, $log_data_add),
                'save_data'      => $save_data_array,
                'type'           => $log_type,
                'hr_log_id'      => $jobRelationModel->id,
                'user'           => $user,
                'hr_job_id'      => $save_data['data']['job_id'],
                'log_field_data' => ['job_name', 'department_name', 'working_day_rest_type', 'plan_hc_nums', 'edit_reason', 'position_type', 'cost_type'],
                'department_id'  => $save_data['data']['department_id'],
            ]);
            if(!$save_log_result){
                throw new \Exception('添加变更记录失败：' . json_encode($log_data).json_encode($save_data['data']) );
            }

            //新增或编辑HC预算人数时,!(HC预算值不变且非共享职位) == HC预算值改变 || 共享职位
            //同步预算人数
            if ($isShare || intval($params['plan_hc_nums']) != intval($budgetCount)) {

                //同步预算 & 写入变更日志
                $syncParams = [
                    'department_id'       => $params['department_id'],
                    'job_title_id'        => $params['job_id'],
                    'sync_budget_count'   => $params['plan_hc_nums'],
                    'user'                => $user,
                    'origin_budget_count' => $log_data['plan_hc_nums'],
                ];
                $this->syncHcBudgetCountV2($syncParams);
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage(). $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-bindJob-异常信息:' . $real_message . ', request:' . json_encode($params));
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * @description 保存变更数据，后续发邮件
     * @param $department_id
     * @param $job_id
     * @param $origin_hc_budget
     * @param $hc_budget
     * @param $operator_id
     */
    public function saveHcChangeLog($department_id, $job_id, $origin_hc_budget, $hc_budget, $operator_id)
    {
        $shareService = HcShareBudgetService::getInstance()->init();
        $isShare = $shareService->isShare($department_id, $job_id);

        if ($isShare) {
            $shareGroup = $shareService->getShareGroup($department_id, $job_id);
            foreach ($shareGroup as $shareDepartmentId => $shareJobTitleIds) { //共享组

                foreach ($shareJobTitleIds as $shareJobTitleId) { //共享职位

                    if ($shareDepartmentId != $department_id && $shareJobTitleId != $job_id) { //非当前部门、职位的数据要获取下变更前数据
                        $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                            'department_id = :department_id: and job_id = :job_id:',
                            'bind' => ['department_id' => $shareDepartmentId, 'job_id' => $shareJobTitleId],
                        ]);
                        if (empty($jobRelationModel)) {
                            continue;
                        }
                        $origin_hc_budget_cnt = $jobRelationModel->plan_hc_nums;
                    } else {
                        $origin_hc_budget_cnt = $origin_hc_budget;
                    }

                    $model = new HrJobDepartmentRelationChangeModel();
                    $model->department_id    = $shareDepartmentId;
                    $model->job_id           = $shareJobTitleId;
                    $model->hc_budget        = $hc_budget;
                    $model->hc_origin_budget = $origin_hc_budget_cnt;
                    $model->operator_id      = $operator_id;
                    $model->change_time      = gmdate('Y-m-d H:i:s', time());
                    $model->save();
                }
            }
        } else {
            $model = new HrJobDepartmentRelationChangeModel();
            $model->department_id    = $department_id;
            $model->job_id           = $job_id;
            $model->hc_budget        = $hc_budget;
            $model->hc_origin_budget = $origin_hc_budget;
            $model->operator_id      = $operator_id;
            $model->change_time      = gmdate('Y-m-d H:i:s', time());
            $model->save();
        }
    }

    /**
     * @description 校验招聘人数
     * @param $department_id
     * @param $job_title_id
     * @param $budgetInfo
     * @return string
     */
    public function checkRecruit($department_id, $job_title_id, $budgetInfo): string
    {
        if (empty($department_id) || empty($job_title_id)) {
            return '';
        }

        //获取当前预算数
        $curBudgetCnt = BudgetService::getInstance()->getBudgetCount($department_id, $job_title_id);
        $shareService  = HcShareBudgetService::getInstance()->init();
        $isShare = $shareService->isShare($department_id, $job_title_id);
        if ($curBudgetCnt == $budgetInfo && !$isShare) { //预算值没有调整 & 非共享
            return '';
        }

        //共享的[1. 前后变化 2. 前后不变 (部分变更)]
        //3. 非共享，前后变化
        $budgetService = BudgetService::getInstance();
        if ($shareService->isShare($department_id, $job_title_id)) { //是共享
            $hasStaffCount = $hcStaffCount = 0;
            $shareGroup    = $shareService->getShareGroup($department_id, $job_title_id);
            foreach ($shareGroup as $shareDepartmentId => $shareJobTitle_ids) {
                $checkParams = [
                    'department_id' => $shareDepartmentId,
                    'job_id'        => $shareJobTitle_ids,
                ];
                [$tmpHasStaffCount, $tmpHcStaffCount] = $budgetService->checkBudget($checkParams);
                $hasStaffCount += $tmpHasStaffCount;
                $hcStaffCount  += $tmpHcStaffCount;
            }
        } else { //非共享的
            $checkParams = [
                'department_id' => $department_id,
                'job_id'        => [$job_title_id],
            ];
            [$hasStaffCount, $hcStaffCount] = $budgetService->checkBudget($checkParams);
        }

        //修改并且存在【待入职+招聘中+已提交的HC】人数，HC预算剩余数量大于预算人数
        //HC预算剩余数量 = 预算人数 - 在职人数 - 待入职人数 - 招聘中人数 - 已提交HC总人数(待审批) - 待转岗
        if ($hcStaffCount > 0 && $hasStaffCount > $budgetInfo) {
            return 'err_msg_exist_hc';
        }
        return '';
    }

    /**
     * @description 关联职位
     * @param array $params
     * @param array $user
     * @return array
     */
    public function bindJobCommon(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检验职位是否存在
            $jobModel = HrJobTitleByModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $params['job_id']],
            ]);
            if (empty($jobModel)) {
                throw new ValidationException('job not exists，job_id:' . $params['job_id']);
            }
            // 职组下的胜任力为必填项，如果没有则报错
            $jobGroupData = JobGroupService::getInstance()->findOneById($params['group_id']);
            $functionalCompetencyIds = empty($jobGroupData['functional_competency_ids']) ? [] : explode(',', $jobGroupData['functional_competency_ids']);
            if($functionalCompetencyIds && $params['job_competency_zy']){
                $insertFunctionalCompetencyIds = [];
                foreach($params['job_competency_zy'] as $val){
                    $insertFunctionalCompetencyIds[] = $val['id'];
                }
                foreach($functionalCompetencyIds as $val){
                    if(in_array($val, $insertFunctionalCompetencyIds)) {
                        continue;
                    }
                    throw new ValidationException(static::$t->_('jobgroup_functional_competency_not_empty'));
                }
            }

            if(empty($params['id'])) {
                //检验职位绑定关系是否存在
                $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                    'department_id = :department_id: and job_id = :job_id:',
                    'bind' => ['department_id' => $params['department_id'], 'job_id' => $params['job_id']],
                ]);
                if(!empty($jobRelationModel)) {
                    throw new ValidationException(static::$t->_('department_job_relation_isset'));
                }
            } else {
                //检验职位绑定关系是否存在
                $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                    'id = :id:',
                    'bind' => ['id' => $params['id']],
                ]);

                if(empty($jobRelationModel)) {
                    throw new ValidationException(static::$t->_('data_error_prohibit_resubmission'));
                }

                if($jobRelationModel->department_id != $params['department_id'] || $jobRelationModel->job_id != $params['job_id']) {
                    throw new ValidationException(static::$t->_('data_error_prohibit_resubmission'));
                }
            }

            $log_data = [];
            $save_data_add = [];
            $log_type = HrJobLogModel::JOB_LOG_TYPE_NEW_RELATION;
            $syncHcBudgetNums = null;

            if (empty($jobRelationModel)) {
                //不存在绑定关系，首次关联，记录下创建人信息
                $jobRelationModel = new  HrJobDepartmentRelationModel();
                $save_data_add    = ['created_uid'   => $user['id'],
                                     'created_uname' => $user['name'],
                                     'created_at'    => gmdate('Y-m-d H:i:s'),
                                     'updated_at'    => gmdate('Y-m-d H:i:s'),
                ];
            } else {
                //添加日志
                //查询老数据
                $log_data = $this->bindJobDetail($jobRelationModel->id);
                $log_data = $log_data['data'];
                $log_type = HrJobLogModel::JOB_LOG_TYPE_EDIT_RELATION;
            }

            $working_day_rest_type = implode(',', $params['working_day_rest_type']);

            //[1]创建页面
            //[1.1]不制定，HC计划人数为空
            //[1.2]制定,存在预算
            //[1.2.1]存在共享预算，(部门、职位), >>同步预算<<
            //[1.2.2]存在非共享预算
            //[1.2.2,1]单条预算(部门、职位、网点/总部),如果计划HC人数 < HC预算人数, >>同步预算<<
            //[1.2.2,2]多条预算(部门、职位、多个网点),提示"该职位在多个工作地点下存在多条预算，计划HC更新失败。"
            //[1.3]制定，不存在预算，需要同步(来源前端)，>>同步预算<<
            //
            //[2]编辑页面
            //[2.1]不制定，HC计划人数为空
            //[2.2]制定,存在预算
            //[2.2.1]存在共享预算，(部门、职位),如果如果计划HC人数 < HC预算人数, >>同步预算<<
            //[2.2.2]存在非共享预算
            //[2.2.2,1]单条预算(部门、职位、网点/总部),如果计划HC人数 < HC预算人数, >>同步预算<<
            //[2.2.2,2]多条预算(部门、职位、多个网点),提示"该职位在多个工作地点下存在多条预算，计划HC更新失败。"
            //[2.3]制定，不存在预算，仅更新HC计划人数
            if (HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE == $params['is_plan']) { //制定
                $isPlan = HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE;

                //获取并比较计划HC与HC预算人数
                $budgetInfo = BudgetService::getInstance()->getBudget($params['department_id'], $params['job_id']);
                if ($budgetInfo['budget_multi_rows']) {
                    throw new ValidationException(self::$t['error_message_multi_rows']);
                }
                $planHcNums = $params['plan_hc_nums'] ?? 0;

                if (isset($params['is_sync_hc_budget']) && HrJobDepartmentRelationModel::SYNC_HC_BUDGET_STATE_DO_SYNC == $params['is_sync_hc_budget']) {
                    //创建 & 有/没有预算
                    $syncHcBudgetNums = $params['plan_hc_nums'];
                } else {

                    //编辑 & 有预算 & 计划HC人数 < HC预算人数
                    if (!isset($params['is_sync_hc_budget']) && $budgetInfo['budget_count'] > 0 && $planHcNums < $budgetInfo['budget_count']) {
                        $syncHcBudgetNums = $params['plan_hc_nums'];
                    }
                }
            } else { //暂不制定
                $isPlan = HrJobDepartmentRelationModel::PLAN_STATE_NOT_FORMULATE;
            }


            $save_data = [
                'department_id'          => $params['department_id'] ?? '0',
                'job_id'                 => $params['job_id'] ?? '0',
                'job_level'              => $params['job_level'] ?? '',
                'public_job_name'        => isset($params['public_job_name']) ? trim($params['public_job_name']) : '',
                'group_id'               => $params['group_id'] ?? '0',
                'group_child_id'         => isset($params['group_child_id']) && $params['group_child_id'] ? $params['group_child_id'] : '0',
                'work_place_type'        => $params['work_place_type'] ?? '0',
                'report_job_id'          => $params['report_job_id'] ?? '0',
                'jd_id'                  => $params['jd_id'] ?? '0',
                'jd_desc'                => isset($params['jd_desc']) ? htmlspecialchars($params['jd_desc'],ENT_QUOTES) : '',
                'jd_desc_supply'         => isset($params['jd_desc_supply']) ? htmlspecialchars($params['jd_desc_supply'],ENT_QUOTES) : '',
                'plan_hc_nums'           => empty($params['plan_hc_nums']) ? 0 : intval($params['plan_hc_nums']),
                'is_plan'                => $isPlan,
                'job_requirements_jybj'  => isset($params['job_requirements_jybj']) ? htmlspecialchars($params['job_requirements_jybj'],ENT_QUOTES) : '',
                'job_requirements_zyjl'  => isset($params['job_requirements_zyjl']) ? htmlspecialchars($params['job_requirements_zyjl'],ENT_QUOTES) : '',
                'job_requirements_other' => isset($params['job_requirements_other']) ? htmlspecialchars($params['job_requirements_other'],ENT_QUOTES) : '',
                'job_competency_zy'      => isset($params['job_competency_zy']) && $params['job_competency_zy'] ? json_encode($params['job_competency_zy']) : '',
                'job_competency_ld'      => isset($params['job_competency_ld']) && $params['job_competency_ld'] ? json_encode($params['job_competency_ld']) : '',
                'job_competency_hx'      => isset($params['job_competency_hx']) && $params['job_competency_hx'] ? json_encode($params['job_competency_hx']) : '',
                'updated_uid'            => $user['id'],
                'updated_uname'          => $user['name'],
                'updated_at'             => gmdate('Y-m-d H:i:s'),
                'working_day_rest_type'  => $working_day_rest_type,
                'position_type'          => !empty($params['position_type'])? $params['position_type'] : 0,
                'cost_type'              => !empty($params['cost_type'])? $params['cost_type'] : 0,
            ];

            $save_data = array_merge($save_data, $save_data_add);
            if (!$jobRelationModel->save($save_data)) {
                $messages = $jobRelationModel->getMessages();
                $save_err = '';
                foreach ($messages as $message) {
                    $save_err .= $message . PHP_EOL;
                }

                throw new \Exception('职位关联失败，sql错误信息： ' . $save_err);
            }

            //增加 log
            $jobServer = new JobLogService();

            //查询老数据
            $save_data = $this->bindJobDetail($jobRelationModel->id);
            $save_log_result = $jobServer->saveLog([
                'log_data'       => $log_data,
                'save_data'      => $save_data['data'],
                'type'           => $log_type,
                'hr_log_id'      => $jobRelationModel->id,
                'user'           => $user,
                'hr_job_id'      => $save_data['data']['job_id'],
                'log_field_data' => ['job_name', 'department_name', 'working_day_rest_type', 'plan_hc_nums', 'position_type', 'cost_type'],
                'department_id'  => $save_data['data']['department_id'],
            ]);
            if(!$save_log_result){
                throw new \Exception('添加变更记录失败：' . json_encode($log_data).json_encode($save_data['data']) );
            }

            if (!empty($budgetInfo) && isset($budgetInfo['share_state']) && $budgetInfo['share_state']['is_hc_share']) { //追加共享职位的同步操作
                $info = $budgetInfo['share_state'];

                //除当前职位的其他职位，要同步制定计划状态跟HC计划人数
                if (!empty($info['share_job_title'])) {
                    //获取全部计划HC人数
                    $relations = HrJobDepartmentRelationModel::find([
                        'department_id = :department_id: and job_id in({job_id:array})',
                        'bind' => [
                            'department_id' => $params['department_id'],
                            'job_id'        => $info['share_job_title'],
                        ],
                    ]);

                    foreach ($relations as $relation) {
                        if (HrJobLogModel::JOB_LOG_TYPE_NEW_RELATION == $log_type && $relation->job_id == $params['job_id']) {
                            $oldPlanHcNums = 0;
                        } else {
                            $oldPlanHcNums = $relation->plan_hc_nums;
                        }

                        $relation->is_plan      = HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE;
                        $relation->plan_hc_nums = $params['plan_hc_nums'];
                        if (!$relation->update()) {
                            $messages = $relation->getMessages();
                            $save_err = '';
                            foreach ($messages as $message) {
                                $save_err .= $message.PHP_EOL;
                            }

                            throw new \Exception('共享职位关联更新失败，sql错误信息： '.$save_err);
                        }
                        $save_log_result = $jobServer->saveLog([
                            'log_data'       => ['plan_hc_nums' => $oldPlanHcNums],
                            'save_data'      => ['plan_hc_nums' => $params['plan_hc_nums']],
                            'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                            'hr_log_id'      => $relation->id,
                            'user'           => $user,
                            'hr_job_id'      => $relation->job_id,
                            'log_field_data' => ['plan_hc_nums'],
                            'department_id'  => $relation->department_id,
                        ]);
                        if (!$save_log_result) {
                            throw new \Exception('添加变更记录失败：'.json_encode($log_data).json_encode($save_data['data']));
                        }
                    }
                }
            } else {
                $save_log_result = $jobServer->saveLog([
                    'log_data'       => $log_data,
                    'save_data'      => $save_data['data'],
                    'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                    'hr_log_id'      => $jobRelationModel->id,
                    'user'           => $user,
                    'hr_job_id'      => $save_data['data']['job_id'],
                    'log_field_data' => ['plan_hc_nums'],
                    'department_id'  => $save_data['data']['department_id'],
                ]);
                if (!$save_log_result) {
                    throw new \Exception('添加变更记录失败：'.json_encode($log_data).json_encode($save_data['data']));
                }
            }

            //制定计划HC人数 & 符合同步HC预算条件时, 同步HC预算
            if ($isPlan == HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE &&
                !is_null($syncHcBudgetNums) && isset($budgetInfo['share_state']['share_job_title'])) {
                $result = $this->syncHcBudgetCount($params['department_id'], $budgetInfo['share_state']['share_job_title'], $params['plan_hc_nums']);
                if (!$result) {
                    throw new \Exception("职位关联失败");
                }

            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-bindJob-异常信息:' . $real_message . ', request:' . json_encode($params));
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 解除关联职位关系
     */
    public function unbindJob(int $bind_id,array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            //检验职位是否存在
            $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $bind_id],
            ]);
            if (empty($jobRelationModel)) {
                throw new \Exception('职位关联关系不存在，bind_id:' . $bind_id);
            }
            if (BudgetService::getInstance()->isConfiguredBudget()) {

                //该职位已配置共享预算，请联系HR产品解除
                $budgetInfo = BudgetService::getInstance()->getBudgetV2($jobRelationModel->department_id, $jobRelationModel->job_id);
                if ($budgetInfo['is_hc_share']) { //共享
                    throw new ValidationException(static::$t->_('err_msg_exist_share_position'));
                }

                //招聘中人数 、待入职人数 、 已提交的HC人数、转岗转入
                $hcSurplusCount      = BudgetService::getInstance()->getSurplusCount($jobRelationModel->department_id, [$jobRelationModel->job_id]);
                $hcPendingEntryCount = BudgetService::getInstance()->getPendingEntryCount($jobRelationModel->department_id, [$jobRelationModel->job_id]);
                $hcBudgetAdoptCount  = BudgetService::getInstance()->getBudgetAdoptCount($jobRelationModel->department_id, [$jobRelationModel->job_id]);
                $transferEntry       = BudgetService::getInstance()->getJobTransferEntryCount($jobRelationModel->department_id, [$jobRelationModel->job_id]);
                if ($hcSurplusCount > 0 || $hcPendingEntryCount > 0 || $hcBudgetAdoptCount > 0 || $transferEntry > 0) {
                    //有招聘流程中的HC，请联系TA处理后再操作
                    throw new ValidationException(static::$t->_('err_msg.origanization_exist_hc'), ErrCode::$VALIDATE_ERROR);
                }
            }

            //增加 log
            $log_data = $this->bindJobDetail($bind_id);
            //todo 校验该部门该职位下是否有在职员工
            $staff_nums = self::getStaffnums($jobRelationModel->department_id, $jobRelationModel->job_id);//在职人数-解除关联
            if ($staff_nums > 0) {
                throw new ValidationException(static::$t->_('job_unbind_exists_staff_error'));

            }
            if ($jobRelationModel->delete() == false) {
                $messages   = $jobRelationModel->getMessages();
                $delete_err = '';
                foreach ($messages as $message) {
                    $delete_err .= $message . PHP_EOL;
                }
                throw new \Exception('职位关联关系删除失败，sql错误信息： ' . $delete_err);

            }

            $save_log_result = (new JobLogService())->saveLog([
                'log_data'      => $log_data['data'],
                'save_data'     => [],
                'type'          => HrJobLogModel::JOB_LOG_TYPE_REMOVE_RELATION,
                'hr_log_id'     => $bind_id,
                'user'          => $user,
                'hr_job_id'     => $log_data['data']['job_id'],
                'department_id' => $log_data['data']['department_id'],
            ]);
            if (!$save_log_result) {
                throw new \Exception('添加变更记录失败：' . json_encode($log_data['data']));
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-unbindJob-异常信息: ' . $real_message);
        }


        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 关联职位
     *
     * @param $job_name
     * @return array
     */
    public function bindJobDetail(int $bind_id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {

            //检验职位是否存在
            $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $bind_id],
            ]);
            if (empty($jobRelationModel)) {
                throw new \Exception('职位关联关系不存在，bind_id:' . $bind_id);
            }
            //获取职位关联详情
            $data = $jobRelationModel->toArray();

            //todo 补充职位关联详情返回字段信息

            /**获取职位、汇报职位数据**/
            $jobData = HrJobTitleByModel::find([
                'id in ({ids:array})',
                'bind' => ['ids' => [$data['job_id'], $data['report_job_id']]],
            ])->toArray();

            $job_id_name_map = [];
            if (!empty($jobData)) {
                $job_id_name_map = array_column($jobData, 'job_name', 'id');
            }
            $data['job_name']        = $job_id_name_map[$data['job_id']] ?? '';
            $data['report_job_id']   = $data['report_job_id'] ? $data['report_job_id'] : null;
            $data['report_job_name'] = $job_id_name_map[$data['report_job_id']] ?? '';
            if (empty($data['job_name'])) {
                throw new \Exception("职位名称获取失败，职位ID：{$data['job_id']}");
            }
            if ($data['report_job_id'] && empty($data['report_job_name'])) {
                throw new \Exception("汇报职位名称获取失败，职位ID：{$data['report_job_id']}");
            }


            /**获取组织/部门名称**/
            $departmentModel = SysDepartmentModel::findFirst([
                'id = :id:',
                'bind' => ['id' => $data['department_id']],
            ]);
            if (empty($departmentModel)) {
                throw new \Exception('部门获取失败，部门ID：' . $data['department_id']);
            }
            $department_detail       = $departmentModel->toArray();
            $data['department_name'] = $department_detail['name'];

            /**获取职组名称、子职组名称**/
            $jobGroup    = HrJobGroupModel::find([
                'id in ({ids:array})',
                'bind' => ['ids' => [$data['group_id'], $data['group_child_id']]],
            ])->toArray();
            $jobGroupMap = [];
            if (!empty($jobGroup)) {
                $jobGroupMap = array_column($jobGroup, 'name', 'id');
            }
            $data['group_name']       = $jobGroupMap[$data['group_id']] ?? ''; // 职组名称
            $data['group_child_name'] = $jobGroupMap[$data['group_child_id']] ?? ''; //子职组名称
            $data['group_id']         = $data['group_id'] ? $data['group_id'] : null; //职组ID
            $data['group_child_id']   = $data['group_child_id'] ? $data['group_child_id'] : null; //子职组ID

            if ($data['group_id'] && empty($data['group_name'])) {
                throw new \Exception("职组名称获取失败，职组ID：{$data['group_id']}");
            }


            /**获取工作地点类型名称**/
            $data['work_place_type']      = $data['work_place_type'] ? $data['work_place_type'] : null;
            $data['work_place_type_name'] = $data['work_place_type'] ? static::$t->_('work_place_type_' . $data['work_place_type']) : '';

            //职位性质
            $data['position_type_text'] = !empty($data['position_type']) && isset(HrJobDepartmentRelationModel::$position_type_list[$data['position_type']]) ? static::$t->_(HrJobDepartmentRelationModel::$position_type_list[$data['position_type']]) : '';
            //成本类型
            $data['cost_type_text'] = !empty($data['cost_type']) && isset(HrJobDepartmentRelationModel::$cost_type_list[$data['cost_type']]) ? static::$t->_(HrJobDepartmentRelationModel::$cost_type_list[$data['cost_type']]) : '';


            $data['position_type'] = !empty($data['position_type']) ? (string)$data['position_type'] : '';
            $data['cost_type'] = !empty($data['cost_type']) ? (string)$data['cost_type'] : '';


            /**获取jd名称和描述**/
            $data['jd_id']   = $data['jd_id'] ? $data['jd_id'] : null;
            $data['jd_name'] = '';
            if ($data['jd_id']) {
                $jdModel = HrJdModel::findFirst([
                    'job_id = :jd_id:',
                    'bind' => ['jd_id' => $data['jd_id']],
                ]);
                if (empty($jdModel)) {
                    throw new \Exception('部门岗位失败，岗位ID：' . $data['jd_id']);
                }
                $jd_detail       = $jdModel->toArray();
                $data['jd_name'] = $jd_detail['job_name'];
            }

            /**获取在职hc数量和剩余hc数量**/
            $data['work_hc_nums']    = self::getStaffnums($data['department_id'], $data['job_id']);//在职人数-详情页
            $surplus_hc_nums         = $data['plan_hc_nums'] - $data['work_hc_nums'];
            $data['surplus_hc_nums'] = $surplus_hc_nums > 0 ? $surplus_hc_nums : 0;

            /** 获取胜任力名称和描述 **/
            //胜任力ID和胜任力名称、描述映射关系数据（[id=>[name=>'',description=>'']])
            $JobCompetencyMapList = JobCompetencyService::getInstance()->getJobCompetencyMapList();
            if (empty($JobCompetencyMapList)) {
                throw new \Exception('胜任力表数据为空');
            }
            //专业胜任力 返回信息
            $data['job_competency_zy'] = self::buildJobCompetencyData($data['job_competency_zy'], $JobCompetencyMapList);
            if (!empty($data['job_competency_zy'])) {
                $bind_job_competency_zy = array_column($data['job_competency_zy'], 'level', 'id');
                /**
                 * 兼容一级职组下的专业胜任力增加或删除导致和已存储的胜任力数据不一致的问题处理
                 * 实时根据职组获取的专业胜任力为返回数据，将已绑定的胜任力ID和level字段信息同步到最新的数据中
                 */
                //获取职组下最新的胜任力数据
                $job_group_competency_data = JobGroupService::getInstance()->jobGroupDetailOption($data['group_id']);
                $current_job_competency_zy = $job_group_competency_data['job_competency_name'] ?? []; //当前该职组下的专业胜任力数据
                if (!empty($current_job_competency_zy)) {
                    //将当前职位绑定的专业胜任力level信息同步到最新的职组下的专业胜任力数据中
                    array_walk($current_job_competency_zy, function (&$val, $key, $bind_job_competency_zy) {
                        $val['level'] = $bind_job_competency_zy[$val['id']] ?? '';
                    }, $bind_job_competency_zy);
                    //返回最新的专业胜任力数据
                    $data['job_competency_zy'] = $current_job_competency_zy;
                }

            }


            //领导胜任力 返回信息
            $data['job_competency_ld'] = self::buildJobCompetencyData($data['job_competency_ld'], $JobCompetencyMapList);
            //核心胜任力 返回信息
            $data['job_competency_hx'] = self::buildJobCompetencyData($data['job_competency_hx'], $JobCompetencyMapList);

            //html标签处理
            $data['jd_desc']                = htmlspecialchars_decode($data['jd_desc'],ENT_QUOTES);
            $data['jd_desc_supply']         = htmlspecialchars_decode($data['jd_desc_supply'],ENT_QUOTES);
            $data['job_requirements_jybj']  = htmlspecialchars_decode($data['job_requirements_jybj'],ENT_QUOTES);
            $data['job_requirements_zyjl']  = htmlspecialchars_decode($data['job_requirements_zyjl'],ENT_QUOTES);
            $data['job_requirements_other'] = htmlspecialchars_decode($data['job_requirements_other'],ENT_QUOTES);

            //工作天数&轮休规则
            $data['working_day_rest_type'] = !empty($data['working_day_rest_type']) ? explode(',', $data['working_day_rest_type']) : [];

            //hc计划人数
            $data['plan_hc_nums'] = intval($data['plan_hc_nums']);

            if (BudgetService::getInstance()->isConfiguredBudget()) {
                $data['hc_budget_nums'] = $data['plan_hc_nums'];
                $data['upload_file']     = json_decode($data['upload_file'], true);
            } else {
                //hc预算人数
                $budgetInfo = BudgetService::getInstance()->getBudget($data['department_id'], $data['job_id']);
                $data['hc_budget_nums'] = $budgetInfo['budget_count'];

                if ($data['is_plan'] == HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE && $data['plan_hc_nums'] <= $data['hc_budget_nums']
                ) {
                    $data['is_sync_hc_budget'] = HrJobDepartmentRelationModel::SYNC_HC_BUDGET_STATE_DO_SYNC;
                } else {
                    $data['is_sync_hc_budget'] = HrJobDepartmentRelationModel::SYNC_HC_BUDGET_STATE_DO_NOT_SYNC;
                }
                $data['is_plan']        = intval($data['is_plan']);
                //部门-职位对应的预算是多条
                $data['hc_budget_multi_rows'] = $budgetInfo['budget_multi_rows'];
            }
            if (in_array($data['job_id'], $this->getCPosition())) {
                $data['job_level'] = null;
            }

        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-bindJobDetail-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出职位详情word
     * @param array $bind_detial
     */
    public function exportBindJobDetail(array $bind_detial)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];

        try {
            //todo 获取 word 模板内容
            $view = new \Phalcon\Mvc\View();
            $view->setViewsDir(APP_PATH . '/views');
            $view->setVars($bind_detial);

            $view->start();
            $view->disableLevel(
                [
                    \Phalcon\Mvc\View::LEVEL_LAYOUT      => false,
                    \Phalcon\Mvc\View::LEVEL_MAIN_LAYOUT => false,
                ]
            );

            // 代码里审批日志用的倒序
            $lang = self::$language ? strtolower(self::$language) : 'th';
            $view->render("organization", "job_detail_" . $lang);
            $view->finish();
            $word_content = $view->getContent();
            //todo 下载word
//        $word_name = empty($bind_detial['job_name']) ? 'job_info' : $bind_detial['job_name'] . '.doc'; // 生成的 文件名
//
//        header('Content-Description: File Transfer');
//        header('Content-Transfer-Encoding: binary');
//        header('Cache-Control: public, must-revalidate, max-age=0');
//        header('Pragma: public');
//        header('Content-Type: application/msword');
//        header('Content-Disposition: attachment; filename="' . $word_name . '"');
//
//        exit($word_content);

            $word_path = sys_get_temp_dir() . '/job_detail_'.time() . '.doc';
            // echo $word_path;
            $res = file_put_contents($word_path, $word_content);
            if (!$res) {
                throw new \Exception('文件生成失败' . $word_path);
            }
            $file             = OssHelper::uploadFile($word_path);
            $data['file_url'] = $file['object_url'] ?? '';
            if (is_file($word_path)) unlink($word_path);

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('职位体系-exportBindJobDetail-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }


    /**
     * 检查职位名称是否重复
     * @param $job_name
     * @return bool
     */
    private static function checkNameIsRepeat(string $job_name, int $job_id = 0)
    {

        if ($job_id) { //编辑操作
            $res = HrJobTitleByModel::findFirst([
                'job_name = :name: and id <> :id: and status = 1',
                'bind' => ['name' => $job_name, 'id' => $job_id],
            ]);
        } else { //新增操作
            $res = HrJobTitleByModel::findFirst([
                'job_name = :name: and status = 1',
                'bind' => ['name' => $job_name],
            ]);
        }
        if ($res) {
            return true;
        }
        return false;
    }

    /**
     * 获取指定部门下指定职位的在职人数
     * @param $department_id
     * @param $job_id
     * @return mixed
     */
    private static function getStaffnums(int $department_id, int $job_id)
    {
        //当前部门在职人数
        return HrStaffInfoModel::count([
            'formal IN ({formal:array}) and state IN ({state:array}) and is_sub_staff = :is_sub_staff: and node_department_id = :node_department_id: and 
            job_title = :job_title: and working_country = :country:',
            'bind' => [
                'formal'             => [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE],
                'state'              => [StaffInfoEnums::STAFF_STATE_IN, StaffInfoEnums::STAFF_STATE_STOP],
                'is_sub_staff'       => StaffInfoEnums::IS_SUB_STAFF_NO,
                'node_department_id' => $department_id,
                'job_title'          => $job_id,
                'country'            => (new \App\Models\backyard\HrStaffInfoModel)->getWorkingCountryCode(),
            ],
        ]);
    }

    /**
     * 补充胜职位关联的任力字段信息
     * @param string $job_competency_data 绑定的胜任力 json数据
     * @param array $competency_list_map 胜任力map列表数据
     * @return array|mixed|string
     */
    private static function buildJobCompetencyData(string $job_competency_data, array $competency_list_map)
    {
        $job_competency_data = json_decode($job_competency_data, true);
        if (empty($job_competency_data)) return [];

        //补充绑定的胜任力姓名和描述等信息
        foreach ($job_competency_data as &$item) {
            $item['name']        = $competency_list_map[$item['id']]['name'] ?? '';
            $item['description'] = $competency_list_map[$item['id']]['description'] ?? '';
        }

        return $job_competency_data;
    }

    /**
     * 处理 职位关联列表字段
     * @param $job_bind_list
     */
    private function buildJobBindList($job_bind_list)
    {
        $c_position = $this->getCPosition();
        [$job_id_name_map, $jd_id_name_map, $department_id_name_map, $job_group_id_name_map] = $this->getTitleTransferMap($job_bind_list);

        foreach ($job_bind_list as &$item) {
            $item['job_name']             = $job_id_name_map[$item['job_id']] ?? '';               //职位名称
            $item['department_name']      = $department_id_name_map[$item['department_id']] ?? ''; //部门名称
            $item['group_name']           = $job_group_id_name_map[$item['group_id']] ?? '';       //职组名称
            $item['group_child_name']     = $job_group_id_name_map[$item['group_child_id']] ?? ''; //子职组名称
            $item['job_level']            = self::genJobLevleText($item['job_level']);             //职级
            $item['report_job_name']      = $job_id_name_map[$item['report_job_id']] ?? '';        //汇报职位名称
            $item['jd_name']              = $jd_id_name_map[$item['jd_id']] ?? '';                 //JD名称
            $item['work_place_type_name'] = $item['work_place_type'] ? static::$t->_('work_place_type_'.$item['work_place_type']) : '';
            $item['created_at']           = date('Y-m-d H:i:s',
                strtotime($item['created_at']) + get_sys_time_offset() * 3600);
            $item['updated_at']           = date('Y-m-d H:i:s',
                strtotime($item['updated_at']) + get_sys_time_offset() * 3600);
            /**获取在职hc数量**/
            $item['work_hc_nums']               = self::getStaffnums($item['department_id'], $item['job_id']); //在职hc人数-职位管理
            $item['working_day_rest_type_text'] = !empty($item['working_day_rest_type']) ? self::getWorkingDayRestType($item['working_day_rest_type']) : '';
            if (BudgetService::getInstance()->isConfiguredBudget()) {
                $item['plan_hc_nums'] = $item['plan_hc_nums'] ?? "";
            } else {
                $item['plan_hc_nums'] = HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE == $item['is_plan'] ? $item['plan_hc_nums'] : "";
            }
            if (in_array($item['job_id'], $c_position)) {
                $item['job_level'] = '';
            }
        }

        return $job_bind_list;
    }

    /**
     * 处理 职位关联列表字段
     * @param $job_bind_list
     */
    private function buildJobBindExportResponse($job_bind_list)
    {
        [$job_id_name_map, $jd_id_name_map, $department_id_name_map, $job_group_id_name_map] = $this->getTitleTransferMap($job_bind_list);

        foreach ($job_bind_list as &$item) {
            $item['job_name']             = $job_id_name_map[$item['job_id']] ?? ''; //职位名称
            $item['department_name']      = $department_id_name_map[$item['department_id']] ?? '';
            $item['group_name']           = $job_group_id_name_map[$item['group_id']] ?? '';       //职组名称
            $item['group_child_name']     = $job_group_id_name_map[$item['group_child_id']] ?? ''; //子职组名称
            $item['job_level']            = self::genJobLevleText($item['job_level']);            //职级
            $item['report_job_name']      = $job_id_name_map[$item['report_job_id']] ?? '';       //汇报职位名称
            $item['jd_name']              = $jd_id_name_map[$item['jd_id']] ?? '';                //JD名称
            $item['work_place_type_name'] = $item['work_place_type'] ? static::$t->_('work_place_type_'.$item['work_place_type']) : '';
            $item['created_at']           = date('Y-m-d H:i:s',
                strtotime($item['created_at']) + get_sys_time_offset() * 3600);
            $item['updated_at']           = date('Y-m-d H:i:s',
                strtotime($item['updated_at']) + get_sys_time_offset() * 3600);
            /**获取在职hc数量**/
            $item['work_hc_nums']               = self::getStaffnums($item['department_id'], $item['job_id']);
            $item['working_day_rest_type_text'] = !empty($item['working_day_rest_type']) ? self::getWorkingDayRestType($item['working_day_rest_type']) : '';
        }

        return $job_bind_list;
    }

    /**
     * @description 获取参数转义
     * @param array $dataList
     * @return array
     */
    public function getTitleTransferMap(array $dataList): array
    {
        if (empty($dataList)) {
            return [
                [],
                [],
                [],
                [],
            ];
        }

        //职位列表map
        $job_ids = array_merge(array_column($dataList, 'job_id'), array_column($dataList, 'report_job_id'));
        $job_ids = array_values(array_unique(array_filter($job_ids)));
        if (!empty($job_ids)) {
            $job_list = HrJobTitleByModel::find([
                'conditions' => 'id in({job_title_ids:array})',
                'bind' => [
                    'job_title_ids' => $job_ids,
                ],
                'columns' => 'id,job_name',
            ])->toArray();
            if (!empty($job_list)) {
                $job_id_name_map = array_column($job_list, 'job_name', 'id');
            }
        }

        //JD列表map
        $jd_ids = array_values(array_unique(array_filter(array_column($dataList, 'jd_id'))));
        if (!empty($jd_ids)) {
            $jd_list = HrJdModel::find([
                'conditions' => 'job_id in({jd_ids:array})',
                'bind' => [
                    'jd_ids' => $jd_ids,
                ],
                'columns' => 'job_id,job_name',
            ])->toArray();
            if (!empty($jd_list)) {
                $jd_id_name_map = array_column($jd_list, 'job_name', 'job_id');
            }
        }

        //部门列表map
        $department_ids = array_column($dataList, 'department_id');
        if (!empty($department_ids)) {
            $department_list = SysDepartmentModel::find([
                'conditions' => 'id in({dept_ids:array})',
                'bind' => [
                    'dept_ids' => $department_ids,
                ],
                'columns' => 'id,name',
            ])->toArray();
            if (!empty($department_list)) {
                $department_id_name_map = array_column($department_list, 'name', 'id');
            }
        }

        //职组列表map
        $group_ids = array_merge(array_column($dataList, 'group_id'), array_column($dataList, 'group_child_id'));
        $group_ids = array_values(array_unique(array_filter($group_ids)));
        if (!empty($group_ids)) {
            $job_group_list = HrJobGroupModel::find([
                'conditions' => 'id in({group_ids:array})',
                'bind' => [
                    'group_ids' => $group_ids,
                ],
                'columns' => 'id,name',
            ])->toArray();
            if (!empty($job_group_list)) {
                $job_group_id_name_map = array_column($job_group_list, 'name', 'id');
            }
        }

        return [
            $job_id_name_map ?? [],
            $jd_id_name_map ?? [],
            $department_id_name_map ?? [],
            $job_group_id_name_map ?? [],
        ];
    }

    /**
     * 获取职位关联关系列表筛选
     * @param array $condition
     * @param string $column
     * @return mixed
     */
    protected function getListCond(array $condition, $staff_info_id = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['relation'=> HrJobDepartmentRelationModel::class]);
        $builder->leftJoin(SysDepartmentByModel::class, 'department.id = relation.department_id','department');
        $builder->andWhere('department.deleted = :department_deleted:', ['department_deleted' => GlobalEnums::IS_NO_DELETED]);

        //职位ID
        if (isset($condition['job_id']) && $condition['job_id']) {
            $builder->andWhere('relation.job_id = :job_id:', ['job_id' => $condition['job_id']]);
        }

        //汇报职位ID
        if (isset($condition['report_job_id']) && $condition['report_job_id']) {
            $builder->andWhere('relation.report_job_id = :report_job_id:', ['report_job_id' => $condition['report_job_id']]);
        }
        //部门ID
        $search_department_ids = [];
        if (isset($condition['department_id']) && $condition['department_id']) {
            $search_department_ids[] = $condition['department_id'];

            if (isset($condition['is_contain_sub_dept']) && $condition['is_contain_sub_dept']) { //包含子部门
                $department_service = new DepartmentService();
                $deptIdsContainSubDept = $department_service->getChildrenListByDepartmentIdV2($condition['department_id'], true);
                $search_department_ids = array_merge($search_department_ids, $deptIdsContainSubDept);
            }
        }

        //获取我管辖的部门
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($staff_info_id, false,false,true);

        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            $builder->andWhere('relation.department_id = :department_id:', ['department_id' => OrganizationDepartmentEnums::NO_DEPARTMENT]);
        } else if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            //合并，取交集
            if (!empty($search_department_ids)) {
                $department_ids = array_intersect($dominion_department_ids, $search_department_ids);
            } else {
                $department_ids = $dominion_department_ids;
            }

            if (empty($department_ids)) {
                $builder->andWhere('relation.department_id = :department_id:', ['department_id' => 0]);
            } else {
                $builder->andWhere('relation.department_id IN ({department_id:array})', ['department_id' => array_values($department_ids)]);
            }
        } else {
            if (!empty($search_department_ids)) {
                $builder->andWhere('relation.department_id IN ({department_id:array})', ['department_id' => $search_department_ids]);
            }
        }

        //职组ID
        if (isset($condition['group_id']) && $condition['group_id']) {
            $builder->andWhere('relation.group_id = :group_id:', ['group_id' => $condition['group_id']]);
        }
        //职级
        if (isset($condition['job_level']) &&  is_numeric($condition['job_level'])) {
            $c_position = $this->getCPosition();
            $c_position && $builder->andWhere('relation.job_id not in ({hidden_job_ids:array})', ['hidden_job_ids' => $c_position]);
            $builder->andWhere('relation.job_level like :job_level:', ['job_level' => '%'.$condition['job_level'].'%']);
        }
        //岗位ID
        if (isset($condition['jd_id']) && $condition['jd_id']) {
            $builder->andWhere('relation.jd_id = :jd_id:', ['jd_id' => $condition['jd_id']]);
        }

        //工作天数&轮休规则
        if (isset($condition['working_day_rest_type']) && $condition['working_day_rest_type']) {
            $builder->andWhere('relation.working_day_rest_type like :working_day_rest_type:',
                ['working_day_rest_type' => '%'.$condition['working_day_rest_type'].'%']);
        }

        //职位性质
        if (!empty($condition['cost_type'])) {
            $builder->andWhere('relation.cost_type in ({cost_type:array}) ', ['cost_type' => $condition['cost_type']]);

        }

        //成本类型
        if (!empty($condition['position_type'])) {
            $builder->andWhere('relation.position_type in ({position_type:array}) ', ['position_type' => $condition['position_type']]);

        }

        return $builder;
    }

    /**
     * 将职级数字字符串拼接上字母前缀后返回
     * @param $job_level_value_str 职级value字符串
     * @return string
     */
    private static function genJobLevleText($job_level_value_str)
    {
        $job_level = explode(',', $job_level_value_str);
        array_walk($job_level, function (&$val) {
            $val = "F" . $val;
        });

        return implode(',', $job_level);
    }

    //工作天数轮休规则翻译
    private static function getWorkingDayRestType($working_day_rest_type_str)
    {
        $working_day_rest_type = explode(',', $working_day_rest_type_str);
        array_walk($working_day_rest_type, function (&$val) {
            $val = static::$t->_('working_day_rest_type_'.$val);
        });

        return implode(',', $working_day_rest_type);
    }

    /**
     * 获取当前部门的职位列表
     * @param $departmentId
     * @return array
     */
    public function getJobsForCurrentDepartment($departmentId)
    {
        if (empty($departmentId)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('distinct(relation.job_id) as id, job.job_name as name');
        $builder->from(['department' => SysDepartmentByModel::class]);
        $builder->leftjoin(HrJobDepartmentRelationModel::class, 'department.id = relation.department_id', 'relation');
        $builder->leftjoin(HrJobTitleByModel::class, 'job.id = relation.job_id', 'job');
        $builder->where('department.deleted = 0 and job.status=1 and department.id = :department_id:',
            ['department_id' => $departmentId]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 模糊搜索职位
     * @param array $params 查询条件
     * @return mixed
     */
    public function searchJob($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id,job_name as name');
        $builder->from(HrJobTitleByModel::class);
        $builder->where('status = :status:', ['status' => 1]);
        if (!empty($params['job_name'])) {
            $builder->andWhere('job_name LIKE :job_name:', ['job_name' => '%' . $params['job_name'] . '%']);
        }
        $builder->limit(50);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 返回总数
     * @param $condition
     * @return int
     */
    public function getDataExportTotal($condition): int
    {
        $builder = $this->getListCond($condition, $condition['staff_info_id'] ?? 0); //职位关联关系列表-导出总数
        return (int) $builder->columns('COUNT(relation.id) AS t_count')->getQuery()->getSingleResult()->t_count ?? 0;
    }

    /**
     * @description 获取导出数据
     * @return array
     */
    public function getExportData($condition, $staff_info_id = 0)
    {
        $export_data = [];

        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $offset    = $page_size * ($page_num - 1);

        $builder = $this->getListCond($condition , $condition['staff_info_id'] ?? 0); //职位关联关系列表-导出数据

        //导出的基本数据
        $builder->columns([
            'relation.id',
            'relation.department_id',
            'relation.job_id',
            'relation.plan_hc_nums',
            'relation.is_plan',
            'department.name as department_name',
        ]);
        $builder->orderBy('relation.id DESC');
        $builder->limit($page_size, $offset);
        $export_data_list = $builder->getQuery()->execute()->toArray();

        //职位列表
        $jobIds = array_column($export_data_list, 'job_id');
        $jobTitleList = HrJobTitleByModel::find([
            'conditions' => 'id in ({ids:array}) ',
            'bind' => ['ids' => $jobIds],
            'column' => 'id,job_name',
        ])->toArray();
        $jobTitleList = array_column($jobTitleList, 'job_name', 'id');

        if (BudgetService::getInstance()->isConfiguredBudget()) {
            foreach ($export_data_list as $v) {
                $export_data[] = [
                    $v['department_id'],
                    $v['department_name'] ?? '',
                    $v['job_id'],
                    $jobTitleList[$v['job_id']] ?? '',
                    $v['plan_hc_nums'],
                ];
            }
        } else {
            foreach ($export_data_list as $v) {
                $export_data[] = [
                    $v['department_id'],
                    $v['department_name'] ?? '',
                    $v['job_id'],
                    $jobTitleList[$v['job_id']] ?? '',
                    $v['is_plan'] == HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE ?  $v['plan_hc_nums'] : "",
                    '',
                ];
            }
        }

        return $export_data;
    }

    /**
     * @description 返回导出excel header
     * @return array
     */
    public function getExportExcelHeaderFields($lang = null): array
    {
        if (BudgetService::getInstance()->isConfiguredBudget()) {
            $key = 'hc_budget';
        } else {
            $key = 'plan_num';
        }
        if (empty($lang)) {
            return [
                //部门ID
                static::$t->_('csr_field_staff_department_id'),
                //部门名称
                static::$t->_('job.department_name'),
                //职位ID
                static::$t->_('job_id'),
                //职位名称
                static::$t->_('hc_job_title'),
                //计划HC数量
                static::$t->_($key),
                //修改原因
                static::$t->_('hc_budget.edit_reason'),
            ];
        } else {
            $tmp = BaseService::getTranslation($lang);
            return [
                //部门ID
                $tmp['csr_field_staff_department_id'],
                //部门名称
                $tmp['job.department_name'],
                //职位ID
                $tmp['job_id'],
                //职位名称
                $tmp['hc_job_title'],
                //计划HC数量
                $tmp[$key],
                //修改原因
                $tmp['hc_budget.edit_reason'],
            ];
        }

    }

    /**
     * @description 获取上传历史列表
     * @param $params
     * @param int $user_id
     * @return array
     */
    public function importHistoryList($params, $user_id): array
    {
        $params['type'] = Enums\ImportCenterEnums::TYPE_HC_PLAN_NUMBER;
        $params['user'] = $user_id;
        return ImportCenterService::getInstance()->getImportList($params);
    }

    /**
     * 批量上传HC计划人数
     * @return array
     * @throws ValidationException
     */
    public function batchUploadPlanHcNum($excel_data, $user_info, $importTask)
    {
        $code             = ErrCode::$SUCCESS;
        $message          = $real_message = $real_trace = '';
        $share_data       = $correct_data = $error_data = [];

        //验证条数
        if (count($excel_data) > self::IMPORT_MAX_ROW_COUNT) {
            throw new ValidationException(self::$t['data_exceeds_the_limit']);
        }
        //获取语言环境
        $tmp = BaseService::getTranslation($importTask->language);

        //excel数据转data
        if (BudgetService::getInstance()->isConfiguredBudget()) {
            $resultColumnNum = self::ADD_IMPORT_RESULT_COLUMN_NUMBER_NEW;
            $data = $this->excelDataToArrayV2($excel_data);
        } else {
            $resultColumnNum = self::ADD_IMPORT_RESULT_COLUMN_NUMBER;
            $data = $this->excelDataToArray($excel_data);
        }

        //数据校验
        [$correct_data, $error_data] = $this->validationImportData($data, $user_info);

        //$correct_data和$error_data的key必须和$result_data对齐
        if (!empty($correct_data) || !empty($error_data)) {
            foreach ($correct_data as $index => $cv) {
                $excel_data[$index][$resultColumnNum] = $tmp->_('excel_result_success');
            }
            foreach ($error_data as $err_index => $errMsgArray) {
                if (!empty($errMsgArray)) {
                    $errMsgList = array_map(function ($v) use ($tmp) {
                        return $tmp->_($v);
                    }, $errMsgArray['error_message']);
                    $excel_data[$err_index][$resultColumnNum] = join(",", $errMsgList);
                }
            }
        }

        if (empty($correct_data)) { // 一条正确的数据都没有
            return [
                'code'    => $code,
                'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                'data'    => [
                    'excel_data'  => $excel_data,
                    'all_num'     => count($excel_data),
                    'success_num' => 0,
                    'failed_num'  => count($error_data),
                ],
            ];
        }

        $db = $this->getDI()->get('db_backyard');
        try {
            $db->begin();

            //excel数据转data
            if (BudgetService::getInstance()->isConfiguredBudget()) {
                //获取共享配置
                $shareService = HcShareBudgetService::getInstance()->init();
                $jobServer    = new JobLogService();

                foreach ($correct_data as $value) {
                    if (empty($value)) {
                        continue;
                    }
                    $row = $value['data'];

                    //部门存在职位共享
                    $isShare = $shareService->isShare($row['department_id'], $row['job_id']);
                    if ($isShare) {
                        //要将 对应职位 更新计划HC + 记录 + 预算同步日志

                        $shareGroup = $shareService->getShareGroup($row['department_id'], $row['job_id']);
                        foreach ($shareGroup as $shareDepartmentId => $shareJobTitleIds) {

                            //批量更新计划HC人数
                            $jobDepartmentRelation = HrJobDepartmentRelationModel::find([
                                'department_id = :department_id: and job_id in({job_id:array})',
                                'bind' => [
                                    'department_id' => $shareDepartmentId,
                                    'job_id'        => $shareJobTitleIds,
                                ],
                                'for_update'    => true,
                            ]);
                            if (empty($jobDepartmentRelation->toArray())) {
                                continue;
                            }
                            foreach ($jobDepartmentRelation as $relation) {
                                $save_log_result = $jobServer->saveLog([
                                    'log_data'       => ['plan_hc_nums' => $relation->plan_hc_nums, 'edit_reason' => ''],
                                    'save_data'      => ['plan_hc_nums' => $row['plan_hc_num'], 'edit_reason' => $row['edit_reason']],
                                    'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                                    'hr_log_id'      => $relation->id,
                                    'user'           => $user_info,
                                    'hr_job_id'      => $relation->job_id,
                                    'log_field_data' => ['plan_hc_nums', 'edit_reason'],
                                    'department_id'  => $relation->department_id,
                                ]);
                                if (!$save_log_result) {
                                    $this->logger->warning("添加变更记录失败：" .json_encode(['plan_hc_nums' => $relation->plan_hc_nums]).json_encode($row['plan_hc_num']));
                                }

                                $model = new HrJobDepartmentRelationChangeModel();
                                $model->department_id    = $shareDepartmentId;
                                $model->job_id           = $relation->job_id;
                                $model->hc_budget        = $row['plan_hc_num'];
                                $model->hc_origin_budget = $relation->plan_hc_nums;
                                $model->operator_id      = $user_info['id'];
                                $model->change_time      = gmdate('Y-m-d H:i:s', time());
                                $model->save();

                                //[3]同步预算
                                BudgetService::getInstance()->saveSingleBudget($shareDepartmentId, $relation->job_id, $row['plan_hc_num']);
                            }
                            $jobDepartmentRelation->update(['plan_hc_nums' => $row['plan_hc_num']]);

                            //$this->logger->info(sprintf("%s %s所对应的plan_hc_nums被更新为 %d", $row['department_name'],
                            //    $row['job_name'], $row['plan_hc_num']));
                        }
                    } else {
                        //批量更新计划HC人数
                        $jobDepartmentRelation = HrJobDepartmentRelationModel::findFirst([
                            'department_id = :department_id: and job_id = :job_id:',
                            'bind' => [
                                'department_id' => $row['department_id'],
                                'job_id'        => $row['job_id'],
                            ],
                            'for_update'    => true,
                        ]);
                        $save_log_result = $jobServer->saveLog([
                            'log_data'       => ['plan_hc_nums' => $jobDepartmentRelation->plan_hc_nums, 'edit_reason' => ''],
                            'save_data'      => ['plan_hc_nums' => $row['plan_hc_num'], 'edit_reason' => $row['edit_reason']],
                            'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                            'hr_log_id'      => $jobDepartmentRelation->id,
                            'user'           => $user_info,
                            'hr_job_id'      => $jobDepartmentRelation->job_id,
                            'log_field_data' => ['plan_hc_nums', 'edit_reason'],
                            'department_id'  =>  $row['department_id'],
                        ]);
                        if (!$save_log_result) {
                            $this->logger->warning("添加变更记录失败：" .json_encode(['plan_hc_nums' => $jobDepartmentRelation->plan_hc_nums]).json_encode($row['plan_hc_num']));
                        }

                        $model = new HrJobDepartmentRelationChangeModel();
                        $model->department_id    = $row['department_id'];
                        $model->job_id           = $jobDepartmentRelation->job_id;
                        $model->hc_budget        = $row['plan_hc_num'];
                        $model->hc_origin_budget = $jobDepartmentRelation->plan_hc_nums;
                        $model->operator_id      = $user_info['id'];
                        $model->change_time      = gmdate('Y-m-d H:i:s', time());
                        $model->save();

                        //[3]同步预算
                        BudgetService::getInstance()->saveSingleBudget($row['department_id'], $jobDepartmentRelation->job_id, $row['plan_hc_num']);

                        //更新
                        $jobDepartmentRelation->update(['plan_hc_nums' => $row['plan_hc_num']]);

                        $this->logger->info(sprintf("%s %s所对应的plan_hc_nums被更新为 %d", $row['department_name'],
                            $row['job_name'], $row['plan_hc_num']));
                    }
                }
            } else {
                //获取共享配置
                //$shareList['department_ids']
                //$shareList['position_ids']
                //$shareList['department_position']
                $shareList = SysService::getInstance()->getShareDepartmentPosition();
                $jobServer = new JobLogService();

                foreach ($correct_data as $key => $value) {
                    if (empty($value)) {
                        continue;
                    }
                    $row = $value['data'];

                    //部门存在职位共享
                    if (in_array($row['department_id'], $shareList['department_ids'])) {
                        //该部门对应的共享职位
                        //指定部门下指定职位共享
                        //要将 对应职位 更新计划HC + 记录 + 预算同步日志
                        $shareJobTitleList = $shareList['department_position'][$row['department_id']] ?? [];
                        if (!empty($shareJobTitleList) && in_array($row['job_id'], $shareJobTitleList)) {

                            //获取共享预算人数
                            $model = BudgetService::getInstance()->getBudgetModel($row['department_id'], $shareJobTitleList);

                            //更新计划HC人数成功并且计划HC人数小于预算的时候更新HC预算
                            if ($this->handleSharePlanHc($row, $shareJobTitleList) &&
                                !empty($model) && $row['plan_hc_num'] <= $model->count && $model->count > 0
                            ) {
                                $this->syncHcBudgetCount($row['department_id'], $shareJobTitleList, $row['plan_hc_num']);
                            }
                            continue;
                        }
                    }

                    //批量更新计划HC人数
                    $jobDepartmentRelation = HrJobDepartmentRelationModel::findFirst([
                        'department_id = :department_id: and job_id = :job_id:',
                        'bind' => [
                            'department_id' => $row['department_id'],
                            'job_id'        => $row['job_id'],
                        ],
                        'for_update'    => true,
                    ]);

                    if (empty($jobDepartmentRelation)) {
                        continue;
                    }
                    $oldPlanHcNums = $jobDepartmentRelation->plan_hc_nums;
                    $jobDepartmentRelation->is_plan      = HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE;
                    $jobDepartmentRelation->plan_hc_nums = $row['plan_hc_num'];
                    $jobDepartmentRelation->update();

                    $save_log_result = $jobServer->saveLog([
                        'log_data'       => ['plan_hc_nums' => $oldPlanHcNums],
                        'save_data'      => ['plan_hc_nums' => $row['plan_hc_num']],
                        'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                        'hr_log_id'      => $jobDepartmentRelation->id,
                        'user'           => $user_info,
                        'hr_job_id'      => $row['job_id'],
                        'log_field_data' => ['plan_hc_nums'],
                        'department_id'  =>  $row['department_id'],
                    ]);
                    if (!$save_log_result) {
                        $this->logger->warning("添加变更记录失败：" .json_encode(['plan_hc_nums' => $oldPlanHcNums]).json_encode($row['plan_hc_num']));
                    }

                    $this->logger->info(sprintf("%s %s所对应的plan_hc_nums被更新为 %d", $row['department_name'],
                        $row['job_name'], $row['plan_hc_num']));


                    //获取共享预算人数
                    $model = BudgetService::getInstance()->getBudget($row['department_id'], $row['job_id']);
                    if (!empty($model) && $model['budget_multi_rows']) { //存在多条预算
                        continue;
                    }

                    //更新计划HC人数成功并且计划HC人数小于预算的时候更新HC预算
                    if (!empty($model) && $row['plan_hc_num'] <= $model['budget_count'] && $model['budget_count'] > 0
                    ) {
                        $this->syncHcBudgetCount($row['department_id'], [$row['job_id']], $row['plan_hc_num']);
                    }
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $real_trace   = $e->getTraceAsString();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('batchUploadPlanHcNum error:' . $real_message . json_encode($real_trace));
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [
                'excel_data'  => $excel_data,
                'all_num'     => count($excel_data),
                'success_num' => count(array_column($correct_data, 'data')),
                'failed_num'  => count(array_column($error_data, 'error_message')),
            ],
        ];
    }

    /**
     * @description 同步HC预算人数
     * @param $department_id
     * @param $job_id
     * @param $count
     * @return bool
     */
    public function syncHcBudgetCount($department_id, $job_id, $count): bool
    {
        //这里同步预算要跟HC申请时，对HC预算的校验吻合
        //否则，就会出现存在预算但是无法申请的情况
        //
        //1. 如果非共享，则对HC预算的校验是部门、网点、职位3个维度的
        //2. 如果共享，则对HC预算的校验是共享部门下、的共享职位2个维度的
        $model = BudgetService::getInstance()->getBudgetModel($department_id, $job_id);
        if ($model) {
            //只更新预算的人数，不会涉及维度问题
            $model->count = $count;
            if (!$model->update()) {
                $this->logger->warning('同步预算失败，,sql错误信息： ' . json_encode($model->getMessages()));
                return false;
            }
        } else {
            //插入数据涉及维度问题
            foreach ($job_id as $v) {
                $bidgetService = BudgetService::getInstance();
                $shareInfo = $bidgetService->checkShareJobState($department_id, $v);
                if (empty($shareInfo['is_hc_share'])) { //非共享
                    $bidgetService->createBudget($department_id, $v, "-1", $count);
                } else { //共享
                    $bidgetService->createBudget($department_id, $v, "", $count);
                }
            }
        }
        return true;
    }

    /**
     * @description 同步HC预算人数
     * @param $params
     * @return bool
     * @throws \Exception
     */
    public function syncHcBudgetCountV2($params): bool
    {
        $department_id       = $params['department_id'];
        $job_title_id        = $params['job_title_id'];
        $sync_budget_count   = $params['sync_budget_count'];
        $user                = $params['user'];
        $origin_budget_count = $params['origin_budget_count'];

        $jobServer    = new JobLogService();
        $shareService = HcShareBudgetService::getInstance()->init();
        $isShare      = $shareService->isShare($department_id, $job_title_id);
        if ($isShare) {
            //追加HC预算变更日志
            $shareConfig = $shareService->getShareGroup($department_id, $job_title_id);
            foreach ($shareConfig as $shareDepartmentId => $budgetShareJobTitleIds) {

                //更新共享组关联关系
                $relations = HrJobDepartmentRelationModel::find([
                    'conditions' => 'department_id = :department_id: and job_id in({job_id:array})',
                    'bind'       => [
                        'department_id' => $shareDepartmentId,
                        'job_id'        => $budgetShareJobTitleIds,
                    ],
                ]);
                foreach ($relations as $relation) {

                    if ($shareDepartmentId == $department_id && $job_title_id == $relation->job_id) {
                        $oldPlanHcNums = !empty($origin_budget_count) ? $origin_budget_count : 0;
                    } else {
                        $oldPlanHcNums = $relation->plan_hc_nums;
                    }

                    //[1]保存预算变更日志
                    $model = new HrJobDepartmentRelationChangeModel();
                    $model->department_id    = $shareDepartmentId;
                    $model->job_id           = $relation->job_id;
                    $model->hc_budget        = $sync_budget_count;
                    $model->hc_origin_budget = !empty($oldPlanHcNums) ? $oldPlanHcNums : 0; ;
                    $model->operator_id      = $user['id'];
                    $model->change_time      = gmdate('Y-m-d H:i:s', time());
                    $model->save();

                    //[2]同步预算
                    BudgetService::getInstance()->saveSingleBudget($shareDepartmentId, $relation->job_id, $sync_budget_count);

                    //[3]更新关联关系中的plan_hc_nums
                    //排掉当前编辑部门-职位的关联关系
                    if ($shareDepartmentId == $department_id && $job_title_id == $relation->job_id) {
                        continue;
                    }
                    $relation->plan_hc_nums = $sync_budget_count;
                    if (!$relation->update()) {
                        $messages = $relation->getMessages();
                        $save_err = '';
                        foreach ($messages as $message) {
                            $save_err .= $message.PHP_EOL;
                        }
                        throw new \Exception('共享职位关联更新失败，sql错误信息： '.$save_err);
                    }

                    $insertParams = [
                        'log_data'       => ['plan_hc_nums' => $oldPlanHcNums, 'edit_reason' => ''],
                        'save_data'      => ['plan_hc_nums' => $sync_budget_count, 'edit_reason' => $params['edit_reason']],
                        'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                        'hr_log_id'      => $relation->id,
                        'user'           => $user,
                        'hr_job_id'      => $relation->job_id,
                        'log_field_data'  => ['plan_hc_nums', 'edit_reason'],
                        'department_id'  => $relation->department_id,
                    ];
                    $save_log_result = $jobServer->saveLog($insertParams);
                    if (!$save_log_result) {
                        throw new \Exception('添加变更记录失败：'.json_encode($insertParams));
                    }
                }
            }
        } else {

            $model = new HrJobDepartmentRelationChangeModel();
            $model->department_id    = $department_id;
            $model->job_id           = $job_title_id;
            $model->hc_budget        = $sync_budget_count;
            $model->hc_origin_budget = !empty($origin_budget_count) ? $origin_budget_count : 0;
            $model->operator_id      = $user['id'];
            $model->change_time      = gmdate('Y-m-d H:i:s', time());
            $model->save();
            BudgetService::getInstance()->saveSingleBudget($department_id, $job_title_id, $sync_budget_count);
        }

        return true;
    }

    /**
     * @description
     * @return array
     * @throws ValidationException
     */
    public function excelDataToArray($excel_data)
    {
        //excel转字段
        $data_key = [
            0 => 'department_id',   //部门ID
            1 => 'department_name', //部门名称
            2 => 'job_id',          //职位ID
            3 => 'job_name',        //职位名称
            4 => 'plan_hc_num',     //计划HC数量
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'],
                        ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        foreach ($data as $key => &$value) {
            $value['department_id'] = intval($value['department_id']);
            $value['job_id']        = intval($value['job_id']);
            $value['plan_hc_num']   = $value['plan_hc_num'] != "" ? intval($value['plan_hc_num']): "";
        }
        return $data;
    }

    /**
     * @description
     * @return array
     * @throws ValidationException
     */
    public function excelDataToArrayV2($excel_data)
    {
        //excel转字段
        $data_key = [
            0 => 'department_id',   //部门ID
            1 => 'department_name', //部门名称
            2 => 'job_id',          //职位ID
            3 => 'job_name',        //职位名称
            4 => 'plan_hc_num',     //HC预算
            5 => 'edit_reason',     //修改原因
        ];
        $data = [];
        foreach ($excel_data as $k => $v) {
            foreach ($data_key as $index => $key) {
                if (!isset($index)) {
                    throw new ValidationException(self::$t['file_data_index_error'],
                        ErrCode::$STANDARD_IMPORT_EXCEL_DATA_INDEX_ERROR);
                }
                $data[$k][$key] = trim($v[$index]);
            }
        }
        $data = array_values($data);
        //数据清洗
        foreach ($data as $key => &$value) {
            $value['department_id'] = intval($value['department_id']);
            $value['job_id']        = intval($value['job_id']);
            $value['plan_hc_num']   = $value['plan_hc_num'] != "" ? intval($value['plan_hc_num']): "";
        }
        return $data;
    }

    /**
     * @description 校验导入数据
     * @return array
     * @throws ValidationException
     */
    public function validationImportData($data, $user_info)
    {
        $departmentIds = array_column($data, 'department_id');
        $jobIds = array_column($data, 'job_id');

        if (empty($departmentIds) || empty($jobIds)) {
            throw new ValidationException('no valid data');
        }
        $departmentInfo = SysDepartmentModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $departmentIds],
            'columns'    => "id,deleted",
        ])->toArray();
        $departmentInfoArr = array_column($departmentInfo, 'id');
        $departmentInfo = array_column($departmentInfo, 'deleted','id');

        $jobInfo = HrJobTitleByModel::find([
            'conditions' => 'id IN ({ids:array})',
            'bind'       => ['ids' => $jobIds],
            'columns'    => "id,status",
        ])->toArray();
        $jobInfoArr = array_column($jobInfo, 'id');
        $jobInfo = array_column($jobInfo, 'status','id');

        $hrJobDepartmentRelateInfo = HrJobDepartmentRelationModel::find([
            'conditions' => 'department_id IN ({department_ids:array}) and job_id IN({job_ids:array})',
            'bind'       => ['department_ids' => $departmentIds, 'job_ids' => $jobIds],
            'columns'    => "concat(department_id,'-',job_id) unique_key,department_id,job_id,plan_hc_nums",
        ])->toArray();
        $hrJobDepartmentRelateInfo = array_column($hrJobDepartmentRelateInfo, null,'unique_key');

        //获取我管辖的部门
        $departmentRelation = DepartmentRelationService::getInstance();
        $dominion_department_ids = $departmentRelation->getStaffDepartmentScopeList($user_info['id'], false, false, true);

        $correct_data = $error_data = [];

        foreach ($data as $key => $value) {

            $correct_data[$key] = $error_data[$key] = [];
            if (empty($value['department_id']) || empty($value['job_id']) || $value['plan_hc_num'] === "") {
                $error_data[$key]['error_message'][] = 'empty_string'; //存在空值
            }

            if (!is_int($value['department_id']) || !is_int($value['job_id']) || !is_int($value['plan_hc_num'])) {
                $error_data[$key]['error_message'][] = 'error_message_check_value_type'; //请检查输入数据类型
            }
            
            if (!isset($value['department_id'], $departmentInfoArr)) {
                $error_data[$key]['error_message'][] = 'error_message_not_exist_department'; //不存在的部门ID
            } else {

                //存在部门ID，但是部门已删除
                if (isset($departmentInfo[$value['department_id']]) && $departmentInfo[$value['department_id']] == GlobalEnums::IS_DELETED) {
                    $error_data[$key]['error_message'][] = 'error_message_department_invalid'; //部门ID已删除
                }
            }

            //验证部门数据是否在管辖范围内
            if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
                $error_data[$key]['error_message'][] = 'error_message_not_exist_jurisdiction'; //不在管辖范围内，无权操作
            }

            if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH && !in_array($value['department_id'], $dominion_department_ids)) {
                $error_data[$key]['error_message'][] = 'error_message_not_exist_jurisdiction'; //不在管辖范围内，无权操作
            }

            if (!isset($value['job_id'], $jobInfoArr)) {
                $error_data[$key]['error_message'][] = 'error_message_not_exist_job_title'; //不存在职位ID
            } else {

                //存在职位ID，但是职位已失效
                if (isset($jobInfo[$value['job_id']]) && $jobInfo[$value['job_id']] == HrJobTitleByModel::STATUS_NOT_VALID) {
                    $error_data[$key]['error_message'][] = 'error_message_job_title_invalid'; //职位ID已失效
                }
            }

            //部门职位关联表唯一健值
            //格式: 部门-职位
            $departmentJobRelationKey = sprintf("%d-%d", $value['department_id'], $value['job_id']);
            if (!isset($hrJobDepartmentRelateInfo) || !in_array($departmentJobRelationKey, array_keys($hrJobDepartmentRelateInfo))) {
                $error_data[$key]['error_message'][] = 'error_message_not_exist_relate'; //不存在指定部门、职位的关联关系
            }

            if (BudgetService::getInstance()->isConfiguredBudget()) {

                if ($value['plan_hc_num'] < self::MIN_VALID_PLAN_HC_NUMBER || $value['plan_hc_num'] > self::MAX_VALID_PLAN_HC_NUMBER) {
                    $error_data[$key]['error_message'][] = 'error_message_not_in_valid_range'; //HC预算数量超出有效值范围
                }

                if (empty($value['edit_reason'])) {
                    $error_data[$key]['error_message'][] = 'error_message_not_exist_edit_reason'; //不存在编辑原因
                }
                $editReasonLength = iconv_strlen($value['edit_reason'], 'UTF-8');
                if ($editReasonLength < 0 || $editReasonLength > 500) {
                    $error_data[$key]['error_message'][] = 'error_message_not_valid_length'; //编辑原因长度在 0 ～ 500之间
                }

                //校验
                $errMsg = JobService::getInstance()->checkRecruit($value['department_id'], $value['job_id'], $value['plan_hc_num']);
                if (!empty($errMsg)) {
                    $error_data[$key]['error_message'][] = $errMsg;
                }
                //$this->saveHcChangeLog($value['department_id'], $value['job_id'], $hrJobDepartmentRelateInfo[$departmentJobRelationKey]['plan_hc_nums'], $value['plan_hc_num'], $user_info['id']);
            } else {
                //计划HC数量超出有效值范围
                if ($value['plan_hc_num'] < self::MIN_VALID_PLAN_HC_NUMBER || $value['plan_hc_num'] > self::MAX_VALID_PLAN_HC_NUMBER) {
                    $error_data[$key]['error_message'][] = 'error_message_not_in_valid_range'; //计划HC数量有效值范围 0 ～ 50000
                }

                //获取HC预算人数
                $budgetInfo = BudgetService::getInstance()->getBudget($value['department_id'], $value['job_id']);
                if ($budgetInfo['budget_multi_rows']) {
                    $error_data[$key]['error_message'][] = 'error_message_multi_rows'; //多条HC预算
                }
            }
            $value['user_info'] = $user_info;

            if (empty($error_data[$key])) {
                $correct_data[$key]['data'] = $value;
                $correct_data[$key]['error_message'][] = "success";
            }
        }
        return [
            $correct_data,
            $error_data,
        ];
    }

    /**
     * @description 更新共享职位的计划HC人数
     * @param $params
     * @param $shareJobTitleList
     * @return bool
     */
    public function handleSharePlanHc($params, $shareJobTitleList)
    {
        $jobDepartmentRelation = HrJobDepartmentRelationModel::find([
            'department_id = :department_id: and job_id IN({job_ids:array})',
            'bind' => [
                'department_id' => $params['department_id'],
                'job_ids'       => $shareJobTitleList,
            ],
            "for_update" => true,
        ]);
        if (empty($jobDepartmentRelation)) {
            return false;
        }

        $jobServer = new JobLogService();

        foreach ($jobDepartmentRelation as $item) {

            $log_data = $this->bindJobDetail($item->id);
            $log_data = $log_data['data'];

            $item->is_plan      = HrJobDepartmentRelationModel::PLAN_STATE_FORMULATE; //强制更新为制定
            $item->plan_hc_nums = $params['plan_hc_num'];
            if (!$item->update()) {
                $this->logger->warning("更新hc计划人数失败：" . $item->id);
                continue;
            }

            $save_data = $this->bindJobDetail($item->id);
            $save_data = $save_data['data'];

            $save_log_result = $jobServer->saveLog([
                'log_data'       => ['plan_hc_nums' => $log_data['plan_hc_nums']],
                'save_data'      => ['plan_hc_nums' => $save_data['plan_hc_nums']],
                'type'           => HrJobLogModel::JOB_LOG_TYPE_MODIFY_PLAN_HC,
                'hr_log_id'      => $item->id,
                'user'           => $params['user_info'],
                'hr_job_id'      => $save_data['job_id'],
                'log_field_data' => ['plan_hc_nums'],
                'department_id'  => $save_data['department_id'],
            ]);
            if (!$save_log_result) {
                $this->logger->warning("添加变更记录失败：" .json_encode($log_data).json_encode($save_data));
            }
        }
        return true;
    }

    /**
     * 获取部门树（权限）
     * @param $params
     * @return array
     */
    public function getDepartmentTree($params): array
    {
        $staff_info_id = $params['staff_info_id'];

        $list = SysDepartmentModel::find([
            'conditions' => "deleted = 0",
            'columns'    => ["id", "ancestry", "name as label", "type", "'false' as is_disable"],
            'order'      => 'name ASC',
        ])->toArray();

        foreach ($list as $key => $value) {
            $list[$key]['is_disable'] = !($value['is_disable'] == 'false');
        }

        //权限范围控制
        $dominion_department_ids = $this->getStaffDepartmentList($staff_info_id);
        //无权限直接返回
        if ($dominion_department_ids == OrganizationDepartmentEnums::NO_AUTH) {
            return [];
        }

        if ($dominion_department_ids != OrganizationDepartmentEnums::ALL_AUTH) {
            if (empty($list) || empty($dominion_department_ids)) {
                return [];
            }
            $ids = array_column($dominion_department_ids, 'department_id');
            foreach ($list as $k => $v) {
                if (!in_array($v['id'], $ids)) {
                    unset($list[$k]);
                } else {
                    $list[$k]['is_disable'] = (bool)$dominion_department_ids[$v['id']]['is_disable'];
                }
            }
        }

        return array_values(list_to_tree($list, 'id', 'ancestry', 'children', ''));
    }

    /**
     * 获取部门列表权限
     * @param $staff_info_id
     * @return array|string
     */
    public function getStaffDepartmentList($staff_info_id)
    {
        $departmentRelation = DepartmentRelationService::getInstance();
        $areas_range_list   = $departmentRelation->getDepartmentJurisdiction($staff_info_id, true);

        if ($areas_range_list === OrganizationDepartmentEnums::ALL_AUTH) {
            return OrganizationDepartmentEnums::ALL_AUTH;
        }

        if (empty($areas_range_list)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        $department_ids = $areas_range_list['departments'] ?? [];
        //顶级部门直接返回所有
        if (in_array(GlobalEnums::TOP_DEPARTMENT_ID, $department_ids)) {
            return OrganizationDepartmentEnums::ALL_AUTH;
        }

        $list = [];
        if (!empty($department_ids)) {
            $department_list = SysDepartmentModel::find([
                'conditions' => 'id IN ({id:array}) AND deleted = :deleted:',
                'columns'    => "ancestry_v3,id",
                'bind'       => [
                    'id'      => array_values(array_unique($department_ids)),
                    'deleted' => GlobalEnums::IS_NO_DELETED,
                ],
            ])->toArray();

            foreach ($department_list as $v) {
                $department_ancestry_ids = explode('/', $v['ancestry_v3']);
                foreach ($department_ancestry_ids as $a_v) {
                    $list[$a_v] = [
                        'department_id' => $a_v,
                        'is_disable'      => in_array($a_v, $department_ids) ? false : true,
                    ];
                }
                $list[$v['id']] = ['department_id' => $v['id'], 'is_disable' => false];
            }
        }
        return $list;
    }

    /**
     * @description 是否存在共享
     * @param $params
     * @param $user
     * @return array
     */
    public function checkIsExistShare($params, $user): array
    {
        //获取共享信息
        $shareInfo = BudgetService::getInstance()->checkShareJobStateV2($params['department_id'], $params['job_title_id']);
        if (!$shareInfo['is_hc_share']) {
            return [
                'share_department_id' => null,
                'share_job_title_id'  => null,
                'share_label'         => '',
            ];
        }

        //获取共享职位信息
        $service      = new HrJobTitleService();
        $jobTitleInfo = $service->getJobInfoByIds($shareInfo['share_job_title']);
        $jobTitleInfo = array_column($jobTitleInfo, 'job_name', 'id');

        //解析为职位名
        $jobNameList    = array_map(function ($item) use ($jobTitleInfo) {
            return $jobTitleInfo[$item] ?? '';
        }, $shareInfo['share_job_title']);
        $jobNameList    = array_values(array_filter($jobNameList));
        $jobNameListStr = join(',', $jobNameList);

        //获取部门信息
        $departmentService     = new DepartmentRepository();
        $departmentInfo        = $departmentService->getDepartmentByIds($shareInfo['share_department']);
        $departmentInfoList    = array_column($departmentInfo, 'name', 'id');

        $shareContentLabel = [];
        foreach ($shareInfo['share_department'] as $department_id) {
            $shareContentLabel[] = static::$t->_('job.share_content', [
                'share_department' => $departmentInfoList[$department_id] ?? '',
                'share_job_title' => $jobNameListStr,
            ]);
        }
        $shareLabel = static::$t->_('job.share_budget', ['share_content' => join(';', $shareContentLabel)]);

        return [
            'share_department_id' => $shareInfo['share_department'],
            'share_job_title_id'  => $shareInfo['share_job_title'],
            'share_label'         => $shareLabel,
        ];
    }

    /**
     * @description 校验是否存在HC
     * @param $params
     * @param $user
     * @return array
     */
    public function checkIsExistHc($params, $user): array
    {
        //获取共享状态
        $budgetInfo = BudgetService::getInstance()->getBudgetV2($params['department_id'], $params['job_title_id']);
        if ($budgetInfo && $budgetInfo['is_hc_share']) {
            $result['is_exist_share'] = OrganizationDepartmentEnums::EXIST_SHARE_POSITIONS;
            $result['is_exist_hc']    = OrganizationDepartmentEnums::DO_NOT_EXIST_HC;
            return $result;
        }

        $result['is_exist_hc'] = OrganizationDepartmentEnums::DO_NOT_EXIST_HC;
        //未删除的、招聘中、审批中的HC、待转入
        $pendingHc = BudgetService::getInstance()->getSurplusCount($params['department_id'], [$params['job_title_id']]);
        if (!empty($pendingHc)) {
            $result['is_exist_share'] = OrganizationDepartmentEnums::DO_NOT_EXIST_SHARE_POSITIONS;
            $result['is_exist_hc']    = OrganizationDepartmentEnums::EXIST_HC;
            return $result;
        }

        $budgetAdoptCount = BudgetService::getInstance()->getBudgetAdoptCount($params['department_id'], [$params['job_title_id']]);
        if (!empty($budgetAdoptCount)) {
            $result['is_exist_share'] = OrganizationDepartmentEnums::DO_NOT_EXIST_SHARE_POSITIONS;
            $result['is_exist_hc']    = OrganizationDepartmentEnums::EXIST_HC;
            return $result;
        }

        //待入职的HC
        $pendingEntry = BudgetService::getInstance()->getPendingEntryCount($params['department_id'], [$params['job_title_id']]);
        if (!empty($pendingEntry)) {
            $result['is_exist_share'] = OrganizationDepartmentEnums::DO_NOT_EXIST_SHARE_POSITIONS;
            $result['is_exist_hc']    = OrganizationDepartmentEnums::EXIST_HC;
            return $result;
        }
        $jobTransferCnt = BudgetService::getInstance()->getJobTransferEntryCount($params['department_id'], [$params['job_title_id']]);
        if (!empty($jobTransferCnt)) {
            $result['is_exist_share'] = OrganizationDepartmentEnums::DO_NOT_EXIST_SHARE_POSITIONS;
            $result['is_exist_hc']    = OrganizationDepartmentEnums::EXIST_HC;
            return $result;
        }

        return $result;
    }

    /**
     * @description
     * @param $params
     * @return array
     */
    public function calcChangeData($params)
    {
        if (empty($params)) {
            return [];
        }
        $originBudget = $currentBudget = $operatorId = 0;
        switch (count($params)) {
            case 0:
                break;
            case 1:
                $data          = current($params);
                $originBudget  = $data['hc_origin_budget'];
                $currentBudget = $data['hc_budget'];
                $operatorId    = $data['operator_id'];
                break;
            default:
                $originData    = array_pop($params);
                $lastData      = array_shift($params);
                $originBudget  = $originData['hc_origin_budget'];
                $currentBudget = $lastData['hc_budget'];
                $operatorId    = $lastData['operator_id'];
                break;
        }
        return [
            'origin_budget'  => $originBudget,
            'current_budget' => $currentBudget,
            'operator_id'    => $operatorId,
        ];
    }

    /**
     * 下载模板：导出部门职位信息
     * @param array $condition
     * @param int $staff_info_id
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function exportPositionTypeTemp(array $condition, $staff_info_id = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            //获取builder
            $builder = $this->getListCond($condition , $staff_info_id); //职位关联关系列表-列表导出
            $builder->columns('relation.*');
            $data_list = $builder->getQuery()->execute()->toArray();

            //获取转义相关数据
            [$job_id_name_map, $jd_id_name_map, $department_id_name_map, $job_group_id_name_map] = $this->getTitleTransferMap($data_list);


            $excel_header = [
                static::$t->_('job_department_id'),    //部门ID
                static::$t->_('job.department_name'),  //部门名称
                static::$t->_('job_id'),               //职位ID
                static::$t->_('job_name'),             //职位名称
                static::$t->_('position_type'),        //职位性质
                static::$t->_('cost_type'),            //成本类型
            ];
            $config = [
                'path' => sys_get_temp_dir() . '/',
            ];
            $excel = new \Vtiful\Kernel\Excel($config);

            //此处会自动创建一个工作表
            $fileObject = $excel->fileName('Org_job_department_type' . date('YmdHis') . '.xlsx');
            $fileObject->header($excel_header);

            foreach ($data_list as $item) {
                $item['job_name']             = $job_id_name_map[$item['job_id']] ?? ''; //职位名称
                $item['department_name']             = $department_id_name_map[$item['department_id']] ?? ''; //职位名称
                $positionTypeText = !empty($item['position_type']) ? static::$t->_('position_type_' . $item['position_type']) : '';//职位性质
                $costTypeText = !empty($item['cost_type']) ? static::$t->_('cost_type_' . $item['cost_type']) : '';//成本类型
                //组装excel数据
                $excel_result[] = [
                    $item['department_id'],
                    $item['department_name'] ,
                    $item['job_id'],
                    $item['job_name'],
                    $positionTypeText,
                    $costTypeText,
                ];

                $fileObject->data($excel_result);
                unset($excel_result);
            }
            $filePath = $fileObject->output();
            $file_arr = OssHelper::uploadFile($filePath);

            $data['file_url'] = $file_arr ? $file_arr['object_url'] : '';
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('职位体系-exportPositionTypeTemp-异常信息: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * @description 获取上传职位性质历史列表
     * @param $params
     * @param int $user_id
     * @return array
     */
    public function importPositionHistoryList($params, $user_id): array
    {
        $params['type'] = Enums\ImportCenterEnums::TYPE_JOB_POSITION;
        $params['user'] = $user_id;
        return ImportCenterService::getInstance()->getImportList($params);
    }

    /**
     * 批量修改 职位性质，成本类型
     * @param $excel_data
     * @param $user_info
     * @param $import_task
     * @return array
     */
    public function importPositionType($excel_data, $user_info, $import_task)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = $real_trace = '';

        $positionCostTextData = $this->getPositionCostTextAll();

        $t = BaseService::getTranslation($import_task->language);

        $allNum = $success = $failed = 0;

        //excel数据转data
        $result = [];
        foreach ($excel_data as $index => $value) {
            $departmentId = isset($value[0]) ? trim($value[0]) : '';
            $jobId = isset($value[2]) ? trim($value[2]) : '';
            $positionType = isset($value[4]) ? trim($value[4]) : '';
            $costType = isset($value[5]) ? trim($value[5]) : '';

            if (empty($value) || (empty($departmentId) && empty($jobId) && empty($positionType) && empty($costType))) {
                continue;
            }
            $allNum++;
            //部门id 职位id 不能为空
            if (empty($departmentId) || empty($jobId)) {
                $value[6] = $t->_('department_job_not_empty');
                $result[] = $value;
                $failed++;
                continue;
            }

            //职位性质、成本类型不能为空
            if (empty($positionType) || empty($costType)) {
                $value[6] = $t->_('position_cost_not_empty');
                $result[] = $value;
                $failed++;
                continue;
            }

            if(empty($positionCostTextData['position_type'])) {
                $value[6] = $t->_('position_type_error');
                $result[] = $value;
                $failed++;
                continue;
            }

            if(empty($positionCostTextData['cost_type'])) {
                $value[6] = $t->_('cost_type_error');
                $result[] = $value;
                $failed++;
                continue;
            }

            if(!isset($positionCostTextData['position_type'][$positionType])) {
                $value[6] = $t->_('position_type_error');
                $result[] = $value;
                $failed++;
                continue;
            }

            if(!isset($positionCostTextData['cost_type'][$costType])) {
                $value[6] = $t->_('cost_type_error');
                $result[] = $value;
                $failed++;
                continue;
            }

            $relationData = $this->getJobDepartmentData(['department_id' => $departmentId, 'job_id' => $jobId]);

            if(empty($relationData)) {
                $value[6] = $t->_('department_job_not_relation');
                $result[] = $value;
                $failed++;
                continue;
            }

            $updateData['id'] = $relationData['id'];
            $updateData['position_type'] = $positionCostTextData['position_type'][$positionType];
            $updateData['cost_type'] = $positionCostTextData['cost_type'][$costType];
            $updateData['department_id'] = $departmentId;
            $updateData['job_id'] = $jobId;

            $res = $this->updatePositionCost($updateData, $user_info);

            if(!$res) {
                $value[6] = 'fail';
                $result[] = $value;
                $failed++;
                continue;
            }

            $value[6] = 'success';
            $result[] = $value;
            $success++;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [
                'excel_data'  => $result,
                'all_num'     => $allNum,
                'success_num' => $success,
                'failed_num'  => $failed,
            ],
        ];
    }

    /**
     * 获取 三个语言的文案
     * @return array
     */
    public function getPositionCostTextAll()
    {
        $data = [];
        $lang = ['th', 'zh', 'en'];
        foreach ($lang as $oneLang) {
            $t = BaseService::getTranslation($oneLang);
            foreach (HrJobDepartmentRelationModel::$position_type_list as $key => $value) {
                if($key == HrJobDepartmentRelationModel::POSITION_TYPE_0) {
                    continue;
                }
                $data['position_type'][$t->_($value)] = (string)$key;
            }


            foreach (HrJobDepartmentRelationModel::$cost_type_list as $key => $value) {
                if($key == HrJobDepartmentRelationModel::COST_TYPE_0) {
                    continue;
                }
                $data['cost_type'][$t->_($value)] = (string)$key;
            }
        }

        return $data;
    }

    /**
     * 获取部门职位关联关系
     * @param $params
     * @param $columns
     * @return array
     */
    public function getJobDepartmentData($params, $columns = ['*'])
    {
        if(empty($params)) {
            return [];
        }

        $conditions = '1 = 1';
        $bind = [];

        if(empty($params)) {
            return [];
        }
        if(!empty($params['id'])) {
            $conditions .= ' and id = :id:';
            $bind['id'] = $params['id'];
        }

        if(!empty($params['department_id'])) {
            $conditions .= ' and department_id = :department_id:';
            $bind['department_id'] = $params['department_id'];
        }

        if(!empty($params['job_id'])) {
            $conditions .= ' and job_id = :job_id:';
            $bind['job_id'] = $params['job_id'];
        }

        $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
            'columns'    => $columns,
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);

        return empty($jobRelationModel) ? [] : $jobRelationModel->toArray();
    }

    /**
     * 更新 职位性质 成本类型
     * @param $params
     * @param $user_info
     * @return bool
     */
    public function updatePositionCost($params, $user_info)
    {
        if(empty($params)) {
            return false;
        }
        $id = $params['id'];

        $update['position_type'] = $params['position_type'];
        $update['cost_type'] = $params['cost_type'];

        $db = $this->getDI()->get('db_backyard');
        $db->begin();
        $beforeData = $this->getJobDepartmentData(['id' => $id]);

        $update_ret = $db->updateAsDict(
            (new HrJobDepartmentRelationModel())->getSource(),
            $update,
            ["conditions" => "id = " . $id]
        );

        if(!$update_ret) {
            $db->rollback();
            return false;
        }

        $save_log_result = (new JobLogService())->saveLog([
            'log_data'       => ['position_type' => $beforeData['position_type'], 'cost_type' => $beforeData['cost_type']],
            'save_data'      => ['position_type' => $params['position_type'], 'cost_type' => $params['cost_type']],
            'type'           => HrJobLogModel::JOB_LOG_TYPE_EDIT_RELATION,
            'hr_log_id'      => $id,
            'user'           => $user_info,
            'hr_job_id'      => $params['job_id'],
            'log_field_data' => ['position_type', 'cost_type'],
            'department_id'  => $params['department_id'],
        ]);

        if(!$save_log_result) {
            $db->rollback();
            return false;
        }

        $db->commit();
        return true;
    }
}