<?php

namespace App\Modules\Organization\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Models\backyard\HrOrganizationDepartmentFranchiseePieceRelationModel;
use App\Models\backyard\HrOrganizationDepartmentFranchiseeRegionRelationModel;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentPieceRelationModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentRegionRelationModel;
use App\Modules\Organization\Models\HrOrganizationDepartmentStoreRelationModel;
use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Transfer\Models\SettingEnvModel;
use App\Modules\User\Models\BiStaffInfoPositionModel;
use App\Repository\HrStaffRepository;

class DepartmentRelationService extends BaseService
{
    private static $instance;

    //全局规则
    public static $departmentJurisdiction = null;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 过滤部门数据
     * @param $data
     * @param array $department_ids
     * @return array
     */
    public function formatDepartmentList($data, $department_ids = [])
    {
        if (empty($data) || empty($department_ids)) {
            return [];
        }

        foreach ($data as $k => $v) {
            if (!in_array($v['id'], $department_ids)) {
                unset($data[$k]);
            }
        }

        return $data;
    }

    /**
     * 获取部门是否存在下挂/部门/片区/网点
     * @param $department_ids
     * @return array
     */
    public function getDepartmenthasChildren($department_ids)
    {
        if (!$department_ids) {
            return [];
        }

        $department_ids = array_values($department_ids);

        //获取大区关联的部门
        $department_region_ids = HrOrganizationDepartmentRegionRelationModel::find([
            'conditions' => '
                department_id IN ({department_id:array}) 
                AND  state = :state: 
                AND is_deleted = :is_deleted:',
            'columns'    => 'department_id',
            'bind'       => [
                'department_id' => $department_ids,
                'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                'is_deleted'    => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'department_id',
        ])->toArray();
        $department_region_ids = array_column($department_region_ids, 'department_id');

        //获取片区区关联的部门
        $department_piece_ids = HrOrganizationDepartmentPieceRelationModel::find([
            'conditions' => '
                department_id IN ({department_id:array}) 
                AND  state = :state: 
                AND level_state = :level_state:
                AND is_deleted = :is_deleted:',
            'columns'    => 'department_id',
            'bind'       => [
                'department_id' => $department_ids,
                'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state'   => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'    => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'department_id',
        ])->toArray();
        $department_piece_ids = array_column($department_piece_ids, 'department_id');

        //获取网点关联的部门
        $department_store_ids = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => '
                department_id IN ({department_id:array}) 
                AND  state = :state: 
                AND level_state = :level_state:
                AND is_deleted = :is_deleted:',
            'columns'    => 'department_id',
            'bind'       => [
                'department_id' => $department_ids,
                'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state'   => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'    => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'department_id',
        ])->toArray();
        $department_store_ids = array_column($department_store_ids, 'department_id');

        $department_franchisee_region_ids = HrOrganizationDepartmentFranchiseeRegionRelationModel::find([
            'conditions' => '
                department_id IN ({department_id:array}) 
                AND  state = :state: 
                AND is_deleted = :is_deleted:',
            'columns'    => 'department_id',
            'bind'       => [
                'department_id' => $department_ids,
                'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                'is_deleted'    => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'department_id',
        ])->toArray();
        $department_franchisee_region_ids = array_column($department_franchisee_region_ids, 'department_id');

        $department_franchisee_piece_ids = HrOrganizationDepartmentFranchiseePieceRelationModel::find([
            'conditions' => '
                department_id IN ({department_id:array}) 
                AND  state = :state: 
                AND level_state = :level_state:
                AND is_deleted = :is_deleted:',
            'columns'    => 'department_id',
            'bind'       => [
                'department_id' => $department_ids,
                'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state'   => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'    => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'department_id',
        ])->toArray();
        $department_franchisee_piece_ids = array_column($department_franchisee_piece_ids, 'department_id');

        return array_unique(array_merge($department_region_ids, $department_piece_ids, $department_store_ids,
            $department_franchisee_region_ids, $department_franchisee_piece_ids));
    }

    /**
     * 获取用户角色权限范围
     * @param $staff_info_id
     * @return array | string
     */
    public function getStaffDepartmentScopeList($staff_info_id, $is_store = true, $is_upper = true, $is_job = false)
    {
        if (!$staff_info_id) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id, $is_job);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $department_ids = $areas_range_list['departments'] ?? [];

        //顶级部门直接返回所有
        if (in_array(GlobalEnums::TOP_DEPARTMENT_ID, $department_ids)) {
            return OrganizationDepartmentEnums::ALL_AUTH;
        }

        if ($is_store) {
            $store_department_ids = $this->getStaffDepartmentScopeIds($areas_range_list);
            $department_ids       = array_merge($department_ids, $store_department_ids);
        }

        if (empty($department_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        if ($is_upper) {
            //获取管辖部门内人
            if (!empty($department_ids)) {
                $department_list = SysDepartmentModel::find([
                    'conditions' => 'id IN ({id:array}) AND deleted = :deleted:',
                    'columns'    => 'ancestry_v3,id',
                    'bind'       => [
                        'id'      => array_values(array_unique($department_ids)),
                        'deleted' => GlobalEnums::IS_NO_DELETED,
                    ],
                ])->toArray();

                $department_ids = [];
                foreach ($department_list as $v) {
                    $department_ids = array_merge($department_ids, explode('/', $v['ancestry_v3']));
                }
            }
        }

        return array_values(array_unique($department_ids));
    }

    /**
     * 获取我管辖的部门
     * 包含以下场景
     * 直接管辖的部门
     * 管辖的网点的上级管理部门
     * 关系部门的上级部门
     * @param $staff_info_id
     */
    public function getStaffDepartmentScopeIds($areas_range_list)
    {
        $department_ids = [];

        //查看管辖大区的部门
        if (!empty($areas_range_list['regions'])) {
            $region_department_ids = $this->getInfoIdsByRegionIds($areas_range_list['regions']);
            $department_ids        = array_merge($department_ids, $region_department_ids);
        }

        //查看管辖片区的部门
        if (!empty($areas_range_list['pieces'])) {
            $piece_department_ids = $this->getInfoIdsByPiecesIds($areas_range_list['pieces']);
            $department_ids       = array_merge($department_ids, $piece_department_ids);
        }

        //查看管辖网点的部门
        if (!empty($areas_range_list['stores'])) {
            $store_department_ids = $this->getInfoIdsByStoreIds($areas_range_list['stores']);
            $department_ids       = array_merge($department_ids, $store_department_ids);
        }

        //查看管辖类型的部门
        if (!empty($areas_range_list['store_categories'])) {
            $category_department_ids = $this->getInfoIdsByCategory($areas_range_list['store_categories']);
            $department_ids          = array_merge($department_ids, $category_department_ids);
        }

        //加盟商大区关联部门
        if(!empty($areas_range_list['franchisee_regions'])) {
            $franchisee_region_department_ids = $this->getInfoIdsByFranchiseeRegionIds($areas_range_list['franchisee_regions']);
            $department_ids        = array_merge($department_ids, $franchisee_region_department_ids);
        }

        if(!empty($areas_range_list['franchisee_pieces'])) {
            $franchisee_piece_department_ids = $this->getInfoIdsByFranchiseePiecesIds($areas_range_list['franchisee_pieces']);
            $department_ids       = array_merge($department_ids, $franchisee_piece_department_ids);
        }

        return array_values(array_unique($department_ids));
    }

    /**
     * 获取大区管辖范围对应的部门ids
     * @return array
     */
    public function getInfoIdsByRegionIds($region_ids, $field = 'department_id')
    {
        $relation_list = HrOrganizationDepartmentRegionRelationModel::find([
            'conditions' => 'region_id IN ({region_id:array}) AND  state = :state: AND is_deleted = :is_deleted:',
            'columns'    => $field,
            'bind'       => [
                'region_id'  => array_values($region_ids),
                'state'      => OrganizationDepartmentEnums::STATE_NORMAL,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 根据 部门id 获取 挂着 大区的部门ID
     * @return array
     */
    public function getRegionRelationByDeptIds($departmentIds, $field = 'department_id')
    {
        if(empty($departmentIds)) {
            return [];
        }
        $relation_list = HrOrganizationDepartmentRegionRelationModel::find([
            'conditions' => 'department_id IN ({department_ids:array}) AND  state = :state: AND is_deleted = :is_deleted:',
            'columns'    => $field,
            'bind'       => [
                'department_ids'  => array_values($departmentIds),
                'state'      => OrganizationDepartmentEnums::STATE_NORMAL,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 获取片区管辖范围对应的部门ids
     * @return array
     */
    public function getInfoIdsByPiecesIds($piece_ids, $field = 'department_id')
    {
        if (empty($piece_ids)) {
            return [];
        }

        $relation_list = HrOrganizationDepartmentPieceRelationModel::find([
            'conditions' => '
                piece_id IN ({piece_id:array}) 
                AND  state = :state: 
                AND level_state = :level_state:
                AND is_deleted = :is_deleted:
            ',
            'columns'    => $field,
            'bind'       => [
                'piece_id'    => array_values($piece_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 获取网点对应的部门信息
     * @param array $store_ids
     * @param array $storeCategory
     * @return array
     */
    public function getInfoIdsByStoreIds($store_ids = [], $field = 'department_id')
    {
        if (empty($store_ids)) {
            return [];
        }

        $bind = [
            'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
            'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
            'is_deleted'  => GlobalEnums::IS_NO_DELETED,
        ];

        $conditions = ' state = :state: AND level_state = :level_state: AND is_deleted = :is_deleted:';

        if (!in_array(OrganizationDepartmentEnums::ALL_STORE, $store_ids)) {
            $conditions       = 'store_id IN ({store_id:array}) AND '.$conditions;
            $bind['store_id'] = $store_ids;
        }

        $relation_list = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => $conditions,
            'columns'    => $field,
            'bind'       => $bind,
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 获取网点对应的部门信息
     * @param array $store_ids
     * @param array $storeCategory
     * @return array
     */
    public function getInfoIdsByCategory($category = [], $field = 'department_id')
    {
        if (empty($category)) {
            return [];
        }

        $relation_list = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => '
                category IN ({category:array}) 
                AND  state = :state: 
                AND level_state = :level_state:
                AND is_deleted = :is_deleted:
            ',
            'columns'    => $field,
            'bind'       => [
                'category'    => array_values($category),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 查询权限范围
     * @return array|string
     */
    public function getDepartmentJurisdiction($staff_info_id, $js_job = false)
    {
        if ($js_job) {
            $staff_key = 'organization_job_title_staffs';
            $role_key  = 'organization_job_title_roles';
        } else {
            $staff_key = 'organization_department_staffs';
            $role_key  = 'organization_department_roles';
        }

        //获取用户角色
        $envModel = new SettingEnvModel();

        //获取配置工号
        $env_staff_ids = explode(',', $envModel->getSetVal($staff_key));

        //所有权限
        if (in_array($staff_info_id, $env_staff_ids)) {
            return OrganizationDepartmentEnums::ALL_AUTH;
        }


        $staff_info_department_ids = [];

        //判断当前人是不是部门负责人或者助理
        $dep_list_arr = SysDepartmentModel::find([
            'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and deleted = :deleted:',
            'columns'    => ['ancestry_v3', 'id'],
            'bind'       => [
                'deleted'      => GlobalEnums::IS_NO_DELETED,
                'manager_id'   => $staff_info_id,
                'assistant_id' => $staff_info_id,
            ],
        ])->toArray();

        $sysDepartmentModel = new SysDepartmentModel();

        if (!empty($dep_list_arr)) {
            $staff_info_department_ids = [];
            //获取负责的子部门
            foreach ($dep_list_arr as $k => $v) {
                $department_List = $sysDepartmentModel->find([
                    'conditions' => '(ancestry_v3 like :chain: or ancestry_v3 = :chain_id:) and deleted = :deleted:',
                    'bind'       => [
                        'chain'    => $v['ancestry_v3'].'/%',
                        'chain_id' => $v['ancestry_v3'],
                        'deleted'  => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'id',
                ])->toArray();

                $staff_info_department_ids = array_merge($staff_info_department_ids,
                    array_column($department_List, 'id'));
            }
        }

        //获取角色是否配置
        $env_role_ids       = explode(',', $envModel->getSetVal($role_key));
        $staff_role_list    = (new HrStaffRepository())->getStaffRoleList($staff_info_id);
        $staff_role_ids     = array_column($staff_role_list, 'position_category');
        $intersect_role_ids = array_intersect($env_role_ids, $staff_role_ids);

        //子部门+管辖范围数据
        $departments = $res = [];

        if (!empty($intersect_role_ids)) {
            $res         = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id);
            $departments = $res['departments'] ?? [];
        }

        if (!empty($staff_info_department_ids)) {
            $departments = array_merge($departments, $staff_info_department_ids);
        }

        $res['departments'] = $departments;
        return $res;
    }

    /**
     * 获取我管辖的大区
     * @param $staff_info_id
     * @return array | string
     */
    public function getStaffRegionScopeList($staff_info_id)
    {
        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $region_ids = [];

        //查看管辖大区
        if (!empty($areas_range_list['regions'])) {
            $region_rel_ids = $this->getInfoIdsByRegionIds($areas_range_list['regions'], 'region_id');
            $region_ids     = array_merge($region_ids, $region_rel_ids);
        }

        //查看管辖片区的大区
        if (!empty($areas_range_list['pieces'])) {
            $piece_rel_ids = $this->getInfoIdsByPiecesIds($areas_range_list['pieces'], 'region_id');
            $region_ids    = array_merge($region_ids, $piece_rel_ids);
        }

        //查看管辖网点的大区
        if (!empty($areas_range_list['stores'])) {
            $store_rel_ids = $this->getInfoIdsByStoreIds($areas_range_list['stores'], 'region_id');
            $region_ids    = array_merge($region_ids, $store_rel_ids);
        }

        //查看管辖部门类型的编号
        if (!empty($areas_range_list['store_categories'])) {
            $category_rel_ids = $this->getInfoIdsByCategory($areas_range_list['store_categories'], 'region_id');
            $region_ids       = array_merge($region_ids, $category_rel_ids);
        }

        if (empty($region_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        return array_values(array_unique($region_ids));
    }

    /**
     * 获取我管辖的片区
     * @param $staff_info_id
     * @return array | string
     */
    public function getStaffPieceScopeList($staff_info_id)
    {
        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $piece_ids = [];

        //通过大区查询关系片区
        if (!empty($areas_range_list['regions'])) {
            $piece_rel_ids = $this->getPieceIdsByByRegionIds($areas_range_list['regions']);
            $piece_ids     = array_merge($piece_ids, $piece_rel_ids);
        }

        if (!empty($areas_range_list['pieces'])) {
            $piece_rel_ids = $this->getInfoIdsByPiecesIds($areas_range_list['pieces'], 'piece_id');
            $piece_ids     = array_merge($piece_ids, $piece_rel_ids);
        }

        //查看管辖网点的部门
        if (!empty($areas_range_list['stores'])) {
            $store_rel_ids = $this->getInfoIdsByStoreIds($areas_range_list['stores'], 'piece_id');
            $piece_ids     = array_merge($piece_ids, $store_rel_ids);
        }

        //查看管辖类型的部门
        if (!empty($areas_range_list['store_categories'])) {
            $category_rel_ids = $this->getInfoIdsByCategory($areas_range_list['store_categories'], 'piece_id');
            $piece_ids        = array_merge($piece_ids, $category_rel_ids);
        }

        if (empty($piece_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        return array_values(array_unique($piece_ids));
    }

    /**
     * 获取我管辖的网点
     * @param $staff_info_id
     * @return array
     */
    public function getStaffStoreScopeList($staff_info_id)
    {
        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $store_ids = $areas_range_list['stores'] ?? [];

        //顶级部门直接返回所有
        if (in_array(OrganizationDepartmentEnums::ALL_STORE, $store_ids)) {
            return OrganizationDepartmentEnums::ALL_AUTH;
        }

        $dep_list_arr = SysDepartmentModel::find([
            'conditions' => '(manager_id = :manager_id: or assistant_id = :assistant_id:) and deleted = :deleted:',
            'columns'    => ['id', 'ancestry_v3'],
            'bind'       => [
                'deleted'      => GlobalEnums::IS_NO_DELETED,
                'manager_id'   => $staff_info_id,
                'assistant_id' => $staff_info_id,
            ],
        ])->toArray();

        if (!empty($dep_list_arr)) {
            $staff_info_department_ids = [];
            foreach ($dep_list_arr as $k => $v) {
                $department_List = SysDepartmentModel::find([
                    'conditions' => '(ancestry_v3 like :chain: or ancestry_v3 = :chain_id:) and deleted = :deleted:',
                    'bind'       => [
                        'chain'    => $v['ancestry_v3'] . '/%',
                        'chain_id' => $v['ancestry_v3'],
                        'deleted'  => GlobalEnums::IS_NO_DELETED,
                    ],
                    'columns'    => 'id',
                ])->toArray();

                $staff_info_department_ids = array_merge($staff_info_department_ids,
                    array_column($department_List, 'id'));
            }

            $staff_info_department_ids = array_merge(array_column($dep_list_arr, 'id'), $staff_info_department_ids);

            $relation_list = HrOrganizationDepartmentStoreRelationModel::find([
                'conditions' => 'department_id IN ({department_id:array}) AND level_state = :level_state: AND  state = :state: AND is_deleted = :is_deleted:',
                'bind'       => [
                    'department_id' => array_values(array_unique($staff_info_department_ids)),
                    'state'         => OrganizationDepartmentEnums::STATE_NORMAL,
                    'level_state'   => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                    'is_deleted'    => GlobalEnums::IS_NO_DELETED,
                ],
            ])->toArray();

            $stores = array_column($relation_list, 'store_id');

            $store_ids = array_merge($store_ids, $stores);
        }

        //查看管辖大区的网点
        if (!empty($areas_range_list['regions'])) {
            $store_rel_ids = $this->getStoreIdsByByRegionIds($areas_range_list['regions']);
            $store_ids     = array_merge($store_ids, $store_rel_ids);
        }

        if (!empty($areas_range_list['pieces'])) {
            $store_rel_ids = $this->getStoreIdsByByPieceIds($areas_range_list['pieces']);
            $store_ids     = array_merge($store_ids, $store_rel_ids);
        }

        //查看管辖类型的部门
        if (!empty($areas_range_list['store_categories'])) {
            $category_rel_ids = $this->getInfoIdsByCategory($areas_range_list['store_categories'], 'store_id');
            $store_ids        = array_merge($store_ids, $category_rel_ids);
        }

        if (empty($store_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        return array_values(array_unique($store_ids));
    }

    /**
     * 获取大区管辖范围对应的部门ids
     * @return array
     */
    public function getPieceIdsByByRegionIds($region_ids)
    {
        $relation_list = HrOrganizationDepartmentPieceRelationModel::find([
            'conditions' => '
                region_id IN ({region_id:array}) 
                AND level_state = :level_state:
                AND  state = :state: 
                AND is_deleted = :is_deleted:
            ',
            'columns'    => 'piece_id',
            'bind'       => [
                'region_id'   => array_values($region_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'piece_id',
        ])->toArray();

        return $relation_list ? array_column($relation_list, 'piece_id') : [];
    }

    /**
     * 获取大区管辖范围对应的部门ids
     * @return array
     */
    public function getStoreIdsByByRegionIds($region_ids)
    {
        $relation_list = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => '
                region_id IN ({region_id:array}) 
                AND level_state = :level_state:
                AND  state = :state: 
                AND is_deleted = :is_deleted:
            ',
            'columns'    => 'store_id',
            'bind'       => [
                'region_id'   => array_values($region_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'store_id',
        ])->toArray();

        return $relation_list ? array_column($relation_list, 'store_id') : [];
    }

    /**
     * 获取大区管辖范围对应的部门ids
     * @return array
     */
    public function getStoreIdsByByPieceIds($piece_ids)
    {
        $relation_list = HrOrganizationDepartmentStoreRelationModel::find([
            'conditions' => '
                piece_id IN ({piece_id:array}) 
                AND level_state = :level_state:
                AND  state = :state: 
                AND is_deleted = :is_deleted:
            ',
            'columns'    => 'store_id',
            'bind'       => [
                'piece_id'    => array_values($piece_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'store_id',
        ])->toArray();

        return $relation_list ? array_column($relation_list, 'store_id') : [];
    }

    /**
     * 获取管辖的加盟商大区
     * @param $staff_info_id
     * @return array|string|null
     */
    public function getStaffFranchiseeRegionScopeList($staff_info_id)
    {
        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $region_ids = [];

        if (!empty($areas_range_list['franchisee_regions'])) {
            $region_rel_ids = $this->getInfoIdsByFranchiseeRegionIds($areas_range_list['franchisee_regions'], 'region_id');
            $region_ids     = array_merge($region_ids, $region_rel_ids);
        }

        if (!empty($areas_range_list['franchisee_pieces'])) {
            $piece_rel_ids = $this->getInfoIdsByFranchiseePiecesIds($areas_range_list['franchisee_pieces'], 'region_id');
            $region_ids    = array_merge($region_ids, $piece_rel_ids);
        }

        if (empty($region_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        return array_values(array_unique($region_ids));
    }

    /**
     * 获取我管辖的加盟商片区
     * @param $staff_info_id
     * @return array | string
     */
    public function getStaffFranchiseePieceScopeList($staff_info_id)
    {
        if (self::$departmentJurisdiction === null) {
            $areas_range_list = $this->getDepartmentJurisdiction($staff_info_id);
        } else {
            $areas_range_list = self::$departmentJurisdiction;
        }

        if (!is_array($areas_range_list)) {
            return $areas_range_list;
        }

        $piece_ids = [];

        if (!empty($areas_range_list['franchisee_regions'])) {
            $piece_rel_ids = $this->getFranchiseePieceIdsByByRegionIds($areas_range_list['franchisee_regions']);
            $piece_ids     = array_merge($piece_ids, $piece_rel_ids);
        }

        if (!empty($areas_range_list['franchisee_pieces'])) {
            $piece_rel_ids = $this->getInfoIdsByFranchiseePiecesIds($areas_range_list['franchisee_pieces'], 'piece_id');
            $piece_ids     = array_merge($piece_ids, $piece_rel_ids);
        }

        if (empty($piece_ids)) {
            return OrganizationDepartmentEnums::NO_AUTH;
        }

        return array_values(array_unique($piece_ids));
    }

    /**
     * 获取加盟商大区管辖范围对应的字段ids
     * @param $region_ids
     * @param string $field
     * @return array
     */
    public function getInfoIdsByFranchiseeRegionIds($region_ids, string $field = 'department_id'): array
    {
        if(empty($region_ids)) {
            return [];
        }
        $relation_list = HrOrganizationDepartmentFranchiseeRegionRelationModel::find([
            'conditions' => 'region_id IN ({region_id:array}) AND  state = :state: AND is_deleted = :is_deleted:',
            'columns'    => $field,
            'bind'       => [
                'region_id'  => array_values($region_ids),
                'state'      => OrganizationDepartmentEnums::STATE_NORMAL,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 获取加盟商片区管辖范围对应的字段ids
     * @param $piece_ids
     * @param string $field
     * @return array
     */
    public function getInfoIdsByFranchiseePiecesIds($piece_ids, string $field = 'department_id'): array
    {
        if (empty($piece_ids)) {
            return [];
        }

        $relation_list = HrOrganizationDepartmentFranchiseePieceRelationModel::find([
            'conditions' => 'piece_id IN ({piece_id:array}) AND state = :state: AND level_state = :level_state: AND is_deleted = :is_deleted:',
            'columns'    => $field,
            'bind'       => [
                'piece_id'    => array_values($piece_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => $field,
        ])->toArray();

        return $relation_list ? array_column($relation_list, $field) : [];
    }

    /**
     * 获取加盟商大区管辖范围对应的片区ids
     * @param $region_ids
     * @return array
     */
    public function getFranchiseePieceIdsByByRegionIds($region_ids): array
    {
        if(empty($region_ids)) {
            return [];
        }
        $relation_list = HrOrganizationDepartmentFranchiseePieceRelationModel::find([
            'conditions' => 'region_id IN ({region_id:array}) AND level_state = :level_state: AND  state = :state: AND is_deleted = :is_deleted:',
            'columns'    => 'piece_id',
            'bind'       => [
                'region_id'   => array_values($region_ids),
                'state'       => OrganizationDepartmentEnums::STATE_NORMAL,
                'level_state' => OrganizationDepartmentEnums::LEVEL_STATE_NORMAL,
                'is_deleted'  => GlobalEnums::IS_NO_DELETED,
            ],
            'group'      => 'piece_id',
        ])->toArray();

        return $relation_list ? array_column($relation_list, 'piece_id') : [];
    }
}