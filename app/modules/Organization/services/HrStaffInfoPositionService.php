<?php

namespace App\Modules\Organization\Services;

use App\Modules\Organization\Models\HrStaffInfoPositionModel;
use App\Modules\Transfer\Services\JobTransferService;

class HrStaffInfoPositionService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getStaffPosition($staff_info_ids) {

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id,position_category');
        $builder->from(HrStaffInfoPositionModel::class);
        $builder->inWhere('staff_info_id',$staff_info_ids);
        $list = $builder->getQuery()->execute()->toArray();

        $staff_position_list = [];
        foreach ($list as $key => $value) {
            $staff_position_list[$value['staff_info_id']]['position_category'][] = $value['position_category'];
            $staff_position_list[$value['staff_info_id']]['position_category_name'][] = static::$t->_('role_'.$value['position_category']);
        }

        return $staff_position_list;
    }
}