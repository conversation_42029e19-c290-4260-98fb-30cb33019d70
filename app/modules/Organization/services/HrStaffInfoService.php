<?php

namespace App\Modules\Organization\Services;

use App\Library\Enums;
use app\models\backyard\ToolStaffInfoModel;
use App\Modules\Organization\Models\HrJobTitleModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\SysDepartmentModel;

class HrStaffInfoService extends BaseService
{
    public function getHrStaffInfo($staff_info_ids) {
        if(empty($staff_info_ids)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id,name,job_title,sys_store_id,sys_department_id,node_department_id,state,formal');
        $builder->from(HrStaffInfoModel::class);
        $builder->inWhere('staff_info_id',$staff_info_ids);
        $list = $builder->getQuery()->execute()->toArray();

        $hr_job_title_service = new HrJobTitleService();
        $hr_job_title = $hr_job_title_service->getHrJobTitleList();

        foreach ($list as $key => $value) {
            $list[$key]['job_title_name'] = $hr_job_title[$value['job_title']]['job_name'] ?? '';
        }

        return array_column($list,null,'staff_info_id');
    }

    //网点主管列表  DC/SP(1/2)网点负责人为该网点正主管[16]branch supervisor，若无正主管则为空
    public function getStoreBranchSupervisor() {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id,name,job_title,sys_store_id,sys_department_id,node_department_id,job_title_level,job_title_grade');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('job_title = 16 and state = 1');
        $list = $builder->getQuery()->execute()->toArray();

        $hr_job_title_service = new HrJobTitleService();
        $hr_job_title = $hr_job_title_service->getHrJobTitleList();

        $staff_info_ids = array_column($list,'staff_info_id');

        $hr_staff_position_service = new HrStaffInfoPositionService();
        $staff_position = $hr_staff_position_service->getStaffPosition($staff_info_ids);

        foreach ($list as $key => $value) {
            $list[$key]['job_title_name'] = $hr_job_title[$value['job_title']]['job_name'] ?? '';
            $roles_name = implode(',', $staff_position[$value['staff_info_id']]['position_category_name'] ?? '');
            $list[$key]['roles_name'] = $roles_name;
            $list[$key]['job_title_level_name'] = Enums::$staff_level_grade[$value['job_title_level']] ?? '';
        }

        return array_column($list,null,'sys_store_id');
    }

    /**
     * 根据员工工号组获取员工信息
     * @param array $staffIds 员工工号组
     * @return array
     */
    public function getStaffInfoByIds(array $staffIds, $columns = '*')
    {
        if (!$staffIds) {
            return [];
        }
        $list = HrStaffInfoModel::find([
            'conditions' => 'staff_info_id in ({ids:array})',
            'bind'       => ['ids' => $staffIds],
            'columns'    => $columns,
        ]);
        return !empty($list) ? $list->toArray() : [];
    }

    public function staffInfo($staff_info_id)
    {
        return HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'      => ['staff_info_id' => $staff_info_id],
        ]);
    }

    /**
     * 获取工具号员工信息
     * @param int $staffInfoId 员工工号
     * @return array 返回工具号名称、部门名称、职位名称
     * <AUTHOR>
     * @date 2025-07-15
     */
    public function getToolStaffInfo($staffInfoId): array
    {
        if (!$staffInfoId) {
            return [];
        }

        // 获取工具号员工基本信息
        $toolStaffInfo = (new ToolStaffInfoModel())->getToolStaffInfo($staffInfoId);

        if (empty($toolStaffInfo)) {
            return [];
        }

        $result = [
            'name'            => $toolStaffInfo['name'] ?? '',
            'nick_name'       => '',
            'department_name' => '',
            'job_title_name'  => '',
        ];

        // 获取部门名称
        if (!empty($toolStaffInfo['node_department_id'])) {
            $departmentModel = SysDepartmentModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $toolStaffInfo['node_department_id']],
                'columns' => 'name'
            ]);

            $result['department_name'] = $departmentModel->name ?? '';
        }

        if (!empty($toolStaffInfo['job_title'])) {
            //查询员工职位信息
            $obbTitleModel = HrJobTitleModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $toolStaffInfo['job_title']
                ]
            ]);

            $result['job_title_name'] = $obbTitleModel->job_name ?? '';
        }

        return $result;
    }
}
