<?php
/**
 * 职位体系-职位列表
 */
namespace App\Modules\Organization\Controllers;

use app\library\Enums\JobTitleEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Services\JobService;
use app\modules\Organization\services\JobTitleService;

class JobTitleController extends BaseController
{
    /**
     * 添加职位
     * 从jobController里迁移
     * @Token
     * @Permission(action='organization.job_manage.jobtitle.add')
     */
    public function addAction()
    {
        $params['job_name'] = $this->request->get('job_name', 'trim', '');

        try {
            Validation::validate($params, [
                'job_name' => 'Required|StrLenGeLe:1,100|>>>:' . $this->t->_('job_name_length_limit'),
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->addJob($params['job_name'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 编辑职位
     * 从jobController里迁移
     * @Token
     * @Permission(action='organization.job_manage.jobtitle.edit')
     */
    public function editAction()
    {
        $params['job_id']   = $this->request->get('job_id', 'int', 0);
        $params['job_name'] = $this->request->get('job_name', 'trim', '');

        try {
            Validation::validate($params, [
                'job_id'   => 'Required|IntGt:0',
                'job_name' => 'Required|StrLenGeLe:1,100|>>>:' . $this->t->_('job_name_length_limit'),
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->editJob($params['job_id'], $params['job_name'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 职位列表
     * @Token
     * @Permission(action='organization.job_manage.jobtitle.list')
     */
    public function listAction()
    {
        $params['job_id']   = $this->request->get('job_id', 'int', 0);
        $params['status']   = $this->request->get('status', 'int', 0);
        $params['pageSize'] = $this->request->get('pageSize', 'int', 20);
        $params['pageNum']  = $this->request->get('pageNum', 'int', 1);

        try {
            Validation::validate($params, [
                'job_id'   => 'Required|Int',
                'status'   => 'Required|IntIn:' . JobTitleEnums::STATUS_ALL . ',' . JobTitleEnums::STATUS_ENABLE . ',' . JobTitleEnums::STATUS_DISABLE,
                'pageSize' => 'Required|Int',
                'pageNum'  => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = JobTitleService::getInstance()->getList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 职位列表导出
     * @Token
     * @Permission(action='organization.job_manage.jobtitle.export_list')
     */
    public function exportListAction()
    {
        $params['job_id'] = $this->request->get('job_id', 'int', 0);
        $params['status'] = $this->request->get('status', 'int', 0);

        try {
            Validation::validate($params, [
                'job_id' => 'Required|Int',
                'status'    => 'Required|IntIn:'.JobTitleEnums::STATUS_ALL.',' .JobTitleEnums::STATUS_ENABLE . ',' . JobTitleEnums::STATUS_DISABLE,
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = JobTitleService::getInstance()->exportList($params,$this->user);

        if ($data['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', ['file_url' => $data['data']]);
        }

        return $this->returnJson($data['code'], $data['data']);
    }
}