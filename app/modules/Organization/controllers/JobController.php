<?php
/**
 * 职位体系-职位管理
 */

namespace App\Modules\Organization\Controllers;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use app\library\Enums\JobTitleEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\ImportCenterService;
use App\Models\backyard\SysDepartmentModel;
use App\Modules\Hc\Services\BudgetService;
use App\Modules\Organization\Models\HrJobDepartmentRelationModel;
use App\Modules\Organization\Services\JobJdService;
use App\Modules\Organization\Services\JobService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class JobController extends BaseController
{

    /**
     * 职位管理 - 基本信息 默认值
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = JobService::getInstance()->getOptionsDefault();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 职位管理 模糊搜索职位
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchJobOptionAction()
    {
        $params = $this->request->get();

        $validation = [
            'search_name' => 'StrLenGeLe:0,255|>>>:search_name max length limit 255',
            'page_size'   => 'IntLe:1000|>>>:page_size error',
        ];
        
        $params['page_size'] = empty($params['page_size']) ? 150 : $params['page_size'];

        if (!empty($params['status'])) {
            $validation['status'] = 'IntIn:' . JobTitleEnums::STATUS_ENABLE.','.JobTitleEnums::STATUS_DISABLE;
        }

        Validation::validate($params, $validation);

        $list = JobService::getInstance()->searchJobTitle($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 获取部门的职位
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getRelationJobsByDepartIdAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ['department_id' => 'Required|IntGt:0|>>>:department_id error']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobService::getInstance()->getRelationJobsByDepartId($params['department_id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 添加职位
     *
     * @Permission(action='organization.job_manage.job.add')
     * @return Response|ResponseInterface
     */
    public function addAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, JobService::$validate_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->addJob($params['job_name'],$this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 编辑职位
     *
     * @Permission(action='organization.job_manage.job.edit')
     * @return Response|ResponseInterface
     */
    public function editAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, JobService::$validate_edit);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->editJob($params['job_id'], $params['job_name'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 关联/编辑职位
     *
     * @Permission(action='organization.job_manage.job.bind')
     * @return Response|ResponseInterface
     */
    public function bindAction()
    {
        $params = $this->request->get();
        try {
            $working_day_rest_type_str = implode(',', array_keys(Enums::$all_working_day_rest_type));
            if (BudgetService::getInstance()->isConfiguredBudget()) {
                $validate = [
                    'working_day_rest_type'    => 'Required|Arr|>>>:working_day_rest_type error',
                    'working_day_rest_type[*]' => 'Required|IntIn:'.$working_day_rest_type_str.'|>>>:working_day_rest_type error',
                    'plan_hc_nums'             => 'Required|IntGeLe:0,50000|>>>:'. BaseService::getTranslation($this->locale)->t('hc_nums_limit'), //计划hc数量
                ];

                $jobRelationModel = HrJobDepartmentRelationModel::findFirst([
                    'department_id = :department_id: and job_id = :job_id:',
                    'bind' => ['department_id' => $params['department_id'], 'job_id' => $params['job_id']],
                ]);
                if (!empty($jobRelationModel) && $jobRelationModel->plan_hc_nums != $params['plan_hc_nums']) {
                    $validate['edit_reason'] = 'Required|StrLenGeLe:1,500|>>>:edit_reason max length limit 500'; //hc预算编辑原因
                }

            } else {
                $validate = [
                    'working_day_rest_type'    => 'Required|Arr|>>>:working_day_rest_type error',
                    'working_day_rest_type[*]' => 'Required|IntIn:'.$working_day_rest_type_str.'|>>>:working_day_rest_type error',
                    'plan_hc_nums'             => 'IfIntEq:is_plan,1|IntGeLe:0,50000|>>>:'. BaseService::getTranslation($this->locale)->t('hc_plan_nums_limit'), //计划hc数量
                ];
            }

            //添加限制,导出最大3万,会报错
            $validate['job_requirements_other'] = 'StrLenGeLe:0,20000|>>>:'.$this->t->_('fields_max_length_20000',['field' => $this->t->_('job_requirements_other')]); //其他-20000
            $validate['jd_desc_supply']         = 'StrLenGeLe:0,20000|>>>:'.$this->t->_('fields_max_length_20000',['field' => $this->t->_('jd_desc_supply')]); //jd描述备注-20000

            Validation::validate($params, array_merge(JobService::$validate_bind, $validate));
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobService::getInstance()->bindJob($params,$this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取职位关联详情
     *
     * @Permission(action='organization.job_manage.job.view_detail')
     * @return Response|ResponseInterface
     */
    public function bindDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, JobService::$validate_bind_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->bindJobDetail($params['bind_id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 解除职位关联
     *
     * @Permission(action='organization.job_manage.job.bind')
     * @return Response|ResponseInterface
     */
    public function unbindAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, JobService::$validate_bind_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->unbindJob($params['bind_id'],$this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获取职位关联列表
     *
     * @Permission(action='organization.job_manage.job.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/24962
     * @return Response|ResponseInterface
     */
    public function bindJobListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_bind_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['job_level'] = $this->request->get('job_level');
        $res = JobService::getInstance()->bindJobList($params, $this->user['id'] ?? 0);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 导出职位关联列表
     *
     * @Permission(action='organization.job_manage.job.export_list')
     * @return Response|ResponseInterface
     */
    public function exportBindJobListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_bind_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('exportBindJobList:' . $this->user['id']);
        if (JobService::getInstance()->checkLock($lock_key)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'Please do not click repeatedly!'];
        } else {
            JobService::getInstance()->setLock($lock_key, 1, 3);
        }
        $res = JobService::getInstance()->exportBindJobList($params , $this->user['id'] ?? 0);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    /**
     * 导出职位关联详情
     * @Permission(action='organization.job_manage.job.view_detail')
     *
     * @return Response|ResponseInterface
     */
    public function exportBindJobDetailAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, JobService::$validate_bind_detail_export);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params = $this->request->get();
        // 加锁处理
        $lock_key = md5('exportBindJobDetail:'.$params['bind_id'] . $this->user['id']);
        if (JobService::getInstance()->checkLock($lock_key)) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'Please do not click repeatedly!');

        } else {
            JobService::getInstance()->setLock($lock_key, 1, 3);
        }
        $res = JobService::getInstance()->bindJobDetail($params['bind_id']);

        if ($res['code'] != ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message']);
        }
        //过滤掉不需要的数据
        $export_field = explode(",",$params['export_field']);

        if(!in_array('all',$export_field)){
            $export_field = array_flip($export_field);
            $res['data'] = array_intersect_key($res['data'],$export_field);
        }


        $res = JobService::getInstance()->exportBindJobDetail($res['data']);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }

    }

    /**
     * @description 导出计划人数
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63397
     * @Permission(action='organization.job_manage.job.plan_hc_import')
     * @return Response|ResponseInterface
     */
    public function exportPlanNumListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_bind_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('export_plan_num_list_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params) {
            return DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::ORGANIZATION_JOB_DEPARTMENT_INFO, $params);
        }, $lock_key, 5);

        return $this->returnJson($res['code'] ?? ErrCode::$SYSTEM_ERROR, $res['message'] ?? $this->t->_('retry_later'), $res['data'] ?? []);
    }

    /**
     * @description 导入计划人数
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63399
     * @Permission(action='organization.job_manage.job.plan_hc_import')
     * @return Response|ResponseInterface
     */
    public function importPlanNumAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'));

            }
            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xls', 'xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'));
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }
            //todo：不能有空行

            //验证条数
            if (count($excel_data) > 100) {
                throw new ValidationException($this->t->_('data_exceeds_the_limit'));
            }

            $type = ImportCenterEnums::TYPE_HC_PLAN_NUMBER;
            $lock_key = md5('standard_import_plan_hc_num' . $this->user['id']);
            $res = $this->atomicLock(function () use ($file, $type) {
                return ImportCenterService::getInstance()->addImportTask($file, $this->user, $type);
            }, $lock_key, 10);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('导入HC计划人数失败: ' . $res['message']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * @description 导入计划HC历史记录
     * @api https://yapi.flashexpress.pub/project/133/interface/api/63400
     * @Permission(action='organization.job_manage.job.plan_hc_import')
     * @return Response|ResponseInterface
     */
    public function importHistoryListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_import_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->importHistoryList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     *jd列表
     * @Permission(action='organization.job_manage.job.list')
     *
     * @return Response|ResponseInterface
     */
    public function getJobJdListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'pageNum'  => 'Required|Int',
                'pageSize' => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobJdService::getInstance()->getJobJdListOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     * 部门树
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getDepartmentTreeAction()
    {
        $staff_info_id = $this->user['id'];
        $list          = JobService::getInstance()->getDepartmentTree(['staff_info_id' => $staff_info_id]);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * @description 是否存在共享，如存在则返回共享信息
     * @Token
     */
    public function checkIsExistShareAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'department_id' => 'Required|Int',
            'job_title_id'  => 'Required|Int',
        ]);

        $res = JobService::getInstance()->checkIsExistShare($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @description 是否有招聘中、待入职、审批中的HC和转岗中的HC
     * @Token
     */
    public function checkIsExistHcAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'department_id' => 'Required|Int',
                'job_title_id'  => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->checkIsExistHc($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 下载模板：导出部门职位信息
     * @Permission(action='organization.job_manage.job.import_job_natrue')
     *
     * @return array|Response|ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function exportPositionTypeTempAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_bind_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('exportPositionTypeTemp:' . $this->user['id']);
        if (JobService::getInstance()->checkLock($lock_key)) {
            return ['code' => ErrCode::$SYSTEM_ERROR, 'message' => 'Please do not click repeatedly!'];
        } else {
            JobService::getInstance()->setLock($lock_key, 1, 3);
        }
        $res = JobService::getInstance()->exportPositionTypeTemp($params , $this->user['id'] ?? 0);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * @description 上传职位性质历史
     * @Permission(action='organization.job_manage.job.import_job_natrue')
     * @return Response|ResponseInterface
     */
    public function importPositionTypeHistoryAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobService::$validate_import_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobService::getInstance()->importPositionHistoryList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * @description 上传职位性质
     * @Permission(action='organization.job_manage.job.plan_hc_import')
     * @return Response|ResponseInterface
     */
    public function importPositionTypeAction()
    {
        try {
            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'));

            }
            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('file_format_error'));
            }
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            $excel_data = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();

            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data)) {
                throw new ValidationException($this->t->_('data_empty_or_read_data_failed'));
            }

            $type = ImportCenterEnums::TYPE_JOB_POSITION;
            $lock_key = md5('importPositionType' . $this->user['id']);
            $res = $this->atomicLock(function () use ($file, $type) {
                return ImportCenterService::getInstance()->addImportTask($file, $this->user, $type);
            }, $lock_key, 10);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $e) {
            $res['code'] = ErrCode::$SYSTEM_ERROR;
            $res['message'] = $e->getMessage();
            $res['data'] = [];
            $this->logger->warning('上传职位性质: ' . $res['message']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 搜索关联的 JD
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchRelateJdAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'job_title_id' => 'Required|Int|>>>:job_title_id err',
        ]);

        $list = JobJdService::getInstance()->searchRelateJd($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }
}
