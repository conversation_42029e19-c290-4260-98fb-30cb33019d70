<?php
/**
 * 职位体系-JD(岗位）管理
 */

namespace App\Modules\Organization\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Services\JobJdService;

class JobjdController extends BaseController
{
    /**
     *jd列表
     * @Permission(action='organization.job_manage.jd.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function getJobJdListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'pageNum'  => 'Required|Int',
                'pageSize' => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobJdService::getInstance()->getJobJdListOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     *新建jd
     * @Permission(action='organization.job_manage.jd.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function addJobJdAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, JobJdService::$validate_jd_add_params_v1);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['uid'] = $this->user['id'];
        $params['uname'] = $this->user['name'];

        $res = JobJdService::getInstance()->addJobJdOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     *编辑jd
     * @Permission(action='organization.job_manage.jd.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function updateJobJdAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, JobJdService::$validate_jd_edit_params_v1);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['uid'] = $this->user['id'];
        $params['uname'] = $this->user['name'];

        $res = JobJdService::getInstance()->updateJobJdOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *删除jd
     * @Permission(action='organization.job_manage.jd.del')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function delJobJdAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ['id' => 'Required|Int'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobJdService::getInstance()->delJobJdOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 导出JD列表
     * @Permission(action='organization.job_manage.jd.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    function exportJobjdlistAction(){
        $params = $this->request->get();
        $params['is_download'] = 1;
        try {
            $lock_key = md5('job_jd_export_' . $this->user['id']);
            $result = $this->atomicLock(function () use ($params) {
                return JobJdService::getInstance()->exportJobjdlist($params, $this->locale);
            }, $lock_key, 300);
            return $this->returnJson($result['code'], $result['message'], $result['data']);
        }catch (\Exception $e){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }

    /**
     * 模糊搜索jd
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function searchJobJdAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'search_name' => 'StrLenGeLe:0,100|>>>:search_name max length limit 100',
            'job_group_id' => 'IntGt:0|>>>:job group id error',
            'page_size'   => 'IntLe:100|>>>:page_size error',
        ]);

        $list = JobJdService::getInstance()->searchJobJodList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }

    /**
     * 根据 JD ID 获取职组信息
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function getJdDetailAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'job_id' => 'Required|Int|>>>:jd_id err',
        ]);

        $list = JobJdService::getInstance()->getJdDetail($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $list);
    }
}
