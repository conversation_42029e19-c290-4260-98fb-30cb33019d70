<?php
/**
 * 职位体系-变更记录
 */

namespace App\Modules\Organization\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Services\JobLogService;

class JoblogController extends BaseController
{


    /**
     * 获取职位关联列表
     *
     * @Permission(action='organization.job_manage.joblog.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function bindJobLogListAction()
    {
        $params = $this->request->get();
        $params = array_filter($params);
        try {
            Validation::validate($params, JobLogService::$validate_bind_log_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['user'] = $this->user;
        $res = JobLogService::getInstance()->bindJobLogList($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

}
