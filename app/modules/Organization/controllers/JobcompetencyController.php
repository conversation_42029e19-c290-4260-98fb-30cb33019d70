<?php
/**
 * 职位体系-胜任力管理
 */

namespace App\Modules\Organization\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Services\JobCompetencyService;

class JobCompetencyController extends BaseController
{

    /**
     *胜任力列表
     * @Permission(action='organization.job_manage.competency.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function getJobCompetencyListAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, [
                'pageNum'  => 'Required|Int',
                'pageSize' => 'Required|Int',
            ]);
            $result = JobCompetencyService::getInstance()->getJobCompetencyList($params);

            if ($result['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, $result['message'], $result['data']);
            }
            return $this->returnJson($result['code'], $result['message']);

        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );

            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg' => $e->getMessage()]);
        }
    }

    /**
     *胜任力列表
     * @Token
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function getJobCompetencyOptionAction()
    {
        try {
            $result = JobCompetencyService::getInstance()->getJobCompetencyOption();

            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);

        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );

            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg' => $e->getMessage()]);
        }
    }

    /**
     *新建胜任力
     * Permission(action='organization.job_manage.competency.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function addJobCompetencyAction()
    {

        try {
            $params = $this->request->get();
            Validation::validate($params, JobCompetencyService::$validate_competency_add_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobCompetencyService::getInstance()->addJobCompetencyOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     *编辑胜任力
     * @Permission(action='organization.job_manage.competency.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function editJobCompetencyAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, JobCompetencyService::$validate_competency_edit_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobCompetencyService::getInstance()->editJobCompetencyOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *删除胜任力
     * @Permission(action='organization.job_manage.competency.detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function delJobCompetencyAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, JobCompetencyService::$validate_competency_del_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobCompetencyService::getInstance()->delJobCompetencyOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 胜任力列表导出
     * @Permission(action='organization.job_manage.competency.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    function exportJobCompetencyListAction(){
        $params = $this->request->get();
        $params['is_download'] = 1;

        try {
            $lock_key = md5('job_competency_export_' . $this->user['id']);
            $result = $this->atomicLock(function () use ($params) {
                return JobCompetencyService::getInstance()->exportJobCompetencyList($params, $this->locale);
            }, $lock_key, 300);
            return $this->returnJson($result['code'], $result['message'], $result['data']);
        }catch (\Exception $e){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }

}
