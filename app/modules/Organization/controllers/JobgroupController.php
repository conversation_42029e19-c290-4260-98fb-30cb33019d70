<?php
/**
 * 职位体系-职组管理
 */

namespace App\Modules\Organization\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;

use App\Modules\Organization\Services\JobGroupService;

class JobgroupController extends BaseController
{
    /**
     * 职组列表
     * @Permission(action='organization.job_manage.jobgroup.list')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function getJobGroupListAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'pageNum'  => 'Required|Int',
                'pageSize' => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = JobGroupService::getInstance()->getJobGroupListOption($params); //职组列表
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     *新建职组
     * @Permission(action='organization.job_manage.jobgroup.add')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function addJobGroupAction()
    {

        try {
            $params = $this->request->get();
            Validation::validate($params, JobGroupService::$validate_group_add_params_v2);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['uid'] = $this->user['id'];
        $params['uname'] = $this->user['name'];
        $res           = JobGroupService::getInstance()->addJobGroupOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }

    /**
     *新建子职组
     * @Permission(action='organization.job_manage.jobgroup.add_child')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function addChildJobGroupAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, JobGroupService::$validate_group_add_params_v3);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['uid'] = $this->user['id'];
        $params['uname'] = $this->user['name'];
        $res           = JobGroupService::getInstance()->addChildJobGroupOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *编辑职组
     * @Permission(action='organization.job_manage.jobgroup.edit')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function editJobGroupAction()
    {
        try {
            $params = $this->request->get();
            if (isset($params['pid']) && !empty($params['pid'])) {//子职组修改
                Validation::validate($params, JobGroupService::$validate_group_edit_params_v3);
            } else {
                Validation::validate($params, JobGroupService::$validate_group_edit_params_v2);
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['uid'] = $this->user['id'];
        $params['uname'] = $this->user['name'];
        $res           = JobGroupService::getInstance()->editJobGroupOption($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *删除职组（校验职位）
     * @Permission(action='organization.job_manage.jobgroup.del')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */

    public function delJobGroupAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, ['id' => 'Required|IntGt:0|>>>:id error']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = JobGroupService::getInstance()->delJobGroupById($params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     *
     *职组详情
     * @Permission(action='organization.job_manage.jobgroup.view_detail')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * */
    public function jobGroupDetailAction()
    {

        try {
            $param = $this->request->get();
            Validation::validate($param, ['id' => 'Required|IntGt:0|>>>:id error']);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        try {
            $result = JobGroupService::getInstance()->jobGroupDetailOption($param['id']);

            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);

        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );

            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg' => $e->getMessage()]);
        }
    }

    /**
     * 导出职组列表
     * @Permission(action='organization.job_manage.jobgroup.export')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    function exportJobGroupListAction(){
        $params['is_download'] = 1;

        try {
            $lock_key = md5('job_group_export_' . $this->user['id']);
            $result = $this->atomicLock(function () use ($params) {
                return JobGroupService::getInstance()->exportJobGroupList($params, $this->locale);
            }, $lock_key, 300);
            return $this->returnJson($result['code'], $result['message'], $result['data']);
        }catch (\Exception $e){
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
    }


}
