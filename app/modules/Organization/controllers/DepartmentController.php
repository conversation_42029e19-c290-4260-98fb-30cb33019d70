<?php

namespace App\Modules\Organization\Controllers;

use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Organization\Models\DeptPcCode;
use app\modules\Organization\models\SysDeptOperateLogsModel;
use App\Modules\Organization\Services\DepartmentService;
use App\Modules\Organization\Services\DeptPcCodeService;
use App\Modules\Organization\Services\HrStaffInfoService;
use App\Modules\Organization\Services\StoreService;
use App\Modules\Organization\Services\SysManageRegionPieceService;
use App\Modules\TalentReview\Services\PermissionsService as TalentReviewPermissionsService;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\ListService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;
use Phalcon\Logger;


class DepartmentController extends BaseController
{
    /**
     * 公司列表
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getCompanyListAction()
    {
        try{
            $department_service = new DepartmentService();
            $company_list       = $department_service->getLeftTreeV2($this->user['id'] ?? 0);
            $result['items']    = $company_list;
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);

        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 详情
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function DetailAction() {
        try{
            $id = $this->request->get('id');
            $department_service = new DepartmentService();
            $detail       = $department_service->getDetail($id);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $detail);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 设置组织名称/负责人 等信息
     * @Permission(action='organization.department.create.clevel')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException|BusinessException
     */
    public function addGroupAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_company_add_params_v2);
        $params['user'] = $this->user;
        $department_service = new DepartmentService();
        $result = $department_service->addGroup($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 设置组织名称/负责人 等信息
     * @Permission(action='organization.department.update_cevel')
     *
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function editGroupAction()
    {
        $params = array_filter($this->request->get());
        Validation::validate($params, DepartmentService::$validate_company_edit_params_v2);
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_info;
        $department_service = new DepartmentService();
        $result = $department_service->editGroup($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 设置组织名称/负责人 等信息
     * @Permission(action='organization.department.update_clevel_sap')
     *
     * @return Response|ResponseInterface
     * @throws BusinessException|ValidationException
     */
    public function editGroupSapAction()
    {
        $params = array_filter($this->request->get());
        Validation::validate($params, DepartmentService::$validate_company_edit_params_v2);
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_sap;
        $department_service = new DepartmentService();
        $result = $department_service->editGroup($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 部门列表
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getDepartmentListAction()
    {
        try {

            try {
                $params = array_filter($this->request->get());
                Validation::validate($params, DepartmentService::$validate_department_list_params_v2);
            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }

            $department_service = new DepartmentService();
            //部门列表
            $department_list = $department_service->getDepartmentListV2($params['id'], $params['type'] , $this->user['id'] ?? 0);
            $result['items'] = $department_list;
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 导出指定组织下全部部门列表数据（包括下属组织下的部门）
     * @Permission(action='organization.department.orginazation_export')
     *
     * @return Response|ResponseInterface
     */
    public function exportByOrginazationAction()
    {
        try{
            $id = $this->request->get('id');
            $type = $this->request->get('type');// 2:公司，3：group组织
            $department_service = new DepartmentService();
            $cond = ['id'=>$id,'type'=>$type];
            //$res = $department_service->exportDepartmentList($cond);
            $res = $department_service->exportDepartmentListV2($cond, $this->user['id'] ?? 0);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }
            return $this->returnJson($res['code'], $res['data']);
        } catch (\Exception $e){
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 导出指定部门下列表数据（包括下属部门） 废弃
     * @Permission(action='organization.department.department_export')
     *
     * @return Response|ResponseInterface
     */
    public function exportByDepartmentAction()
    {
        try{
            $department_id = $this->request->get('department_id');
            $department_service = new DepartmentService();
            //部门列表
            $cond = ['department_id'=>$department_id];
            //$res = $department_service->exportDepartmentList($cond);
            $res = $department_service->exportDepartmentListV2($cond , $this->user['id'] ?? 0);
            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }
            return $this->returnJson($res['code'], 'error',$res['data']);
        } catch (\Exception $e){
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 查看组织架构图
     * @Permission(action='organization.department.orginazation_structures')
     *
     * @return Response|ResponseInterface
     */
    public function getOrginazationStructuresAction()
    {
        try {
            $id = $this->request->get('id');
            $type = $this->request->get('type');// 2:公司，3：group组织

            $department_service    = new DepartmentService();
            $cond = ['id'=>$id,'type'=>$type];
            //$department_structures = $department_service->getDepartmentStructersData($cond);
            $department_structures = $department_service->getDepartmentStructersDataV2($cond, $this->user['id'] ?? 0);
            $result['items']       = $department_structures;
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 查看部门架构图
     * @Permission(action='organization.department.department_structures')
     *
     * @return Response|ResponseInterface
     */
    public function getDeapartmentStructuresAction()
    {
        try {

            $department_id = $this->request->get('department_id');//部门ID
            $department_service = new DepartmentService();
            $cond = ['department_id'=>$department_id];
            //$department_structures = $department_service->getDepartmentStructersData($cond);
            $department_structures = $department_service->getDepartmentStructersDataV2($cond , $this->user['id'] ?? 0);
            $result['items']       = $department_structures;
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 获取编辑页面-上级部门列表数据
     * @Token
     * @return Response|ResponseInterface
     */
    public function getEditPageParentDepartmentAction()
    {
        try {
            $department_id = $this->request->get('department_id');
            if (empty($department_id)) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Department ID is empty', []);
            }
            $department_service     = new DepartmentService();
            $parent_department_list = $department_service->getAvailableParentDepartmentV2($department_id);
            $result['items']        = $parent_department_list;
            return $this->returnJson(ErrCode::$SUCCESS, '', $result);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }

    }

    /**
     * 大区列表
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getManageRegionListAction()
    {
        $department_id = $this->request->get('id', 'int', 0);//部门id
        if ($department_id == 0) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Department ID Error', []);
        }
        $r_p_service = new SysManageRegionPieceService();
        $type        = 1;

        //泰国 51 请求网点 type = 2
        //泰国 CDC (Network Operations) 请求 网点类型是 4
        //菲律宾 123 请求网点 type = 2
//        $shop_department_id = 51;
        $countryCode = strtoupper(env('country_code', 'TH'));
//        switch ($countryCode) {
//            case 'TH':
//                $shop_department_id = 51;
//                $bulky_department_id = 483;
//                break;
//            case 'PH':
//                $shop_department_id = 123;
//                break;
//            case 'MY':
//                $shop_department_id = 350;
//                break;
//        }
//
//        if ($department_id == $shop_department_id) {
//            $type = 2;
//        } else if (isset($bulky_department_id) && $department_id == $bulky_department_id) {
//            $type = 3;
//        }
        //泰国
        if($countryCode == GlobalEnums::TH_COUNTRY_CODE) {
            if($department_id == SysManageRegionPieceService::$department_shop_operations_th) { //Shop Operations
                $type = 2;
            }
            if($department_id == SysManageRegionPieceService::$department_net_work_bulky_area_id_th) { //Network Bulky Area
                $type = 3;
            }
            if($department_id == SysManageRegionPieceService::$department_cdc_network_operations_th) { //CDC (Network Operations)
                $type = 4;
            }
        }
        //菲律宾
        if($countryCode == GlobalEnums::PH_COUNTRY_CODE) {
            if($department_id == SysManageRegionPieceService::$department_philippines_shop_ph) {
                $type = 2;
            }
        }
        //马来
        if($countryCode == GlobalEnums::MY_COUNTRY_CODE) {
            if($department_id == SysManageRegionPieceService::$department_retail_business_development_my) {
                $type = 2;
            }
        }

        $list            = $r_p_service->getRegionList($type, $department_id);
        $result['items'] = $list;
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * 片区列表
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getManagePieceListAction()
    {
        $region_id = $this->request->get('id', 'int', 0);//大区id

        if ($region_id == 0) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Region ID Error', []);
        }

        $r_p_service     = new SysManageRegionPieceService();
        $list            = $r_p_service->getPieceList($region_id);
        $result['items'] = $list;
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * 网点列表接口
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getStoreListAction()
    {
        $id           = $this->request->get('id', 'int', 0);
        $current_type = $this->request->get('current_type', 'int', 0);
        if ($id == 0) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'ID Param Error', []);
        }

        if ($current_type == 0) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Current Type Param Error', []);
        }

        $department_id = 0;
        $region_id     = 0;
        $piece_id      = 0;
        switch ($current_type) {
            case 1:
                $department_id = $id;
                break;
            case 3:
                $region_id = $id;
                break;
            case 4:
                $piece_id = $id;
                break;
        }

        $store_service   = new StoreService();
        $countryCode = strtoupper(env('country_code', 'TH'));
        switch ($countryCode) {
            case 'PH':
                $store_list      = $store_service->getStoreList_PH($department_id, $region_id, $piece_id);
                break;
            case 'MY':
                $store_list      = $store_service->getStoreList_MY($department_id, $region_id, $piece_id);
                break;
            default:
                $store_list      = $store_service->getStoreList($department_id, $region_id, $piece_id);
        }
        $result['items'] = $store_list;
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * 创建部门
     * @Permission(action='organization.department.create')
     *
     * @return Response|ResponseInterface
     * @throws BusinessException|ValidationException
     */
    public function createDepartmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_add_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $result = $department_service->createOrganization($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 创建Bu
     * @Permission(action='organization.department.create.bu')
     *
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createBuAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_add_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $result = $department_service->createOrganization($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * 部门编辑
     * @Permission(action='organization.department.update')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateDepartmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_edit_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_info;
        $result = $department_service->updateOrganization($params);
        if($result === true) {
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
        } else {
            return $this->returnJson($result['code'], $result['msg'], $result['data']);
        }
    }

    /**
     * 部门编辑 - sap
     * @Permission(action='organization.department.update_sap')
     *
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateDepartmentSapAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_edit_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_sap;
        $result = $department_service->updateOrganization($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * Bu编辑
     * @Permission(action='organization.department.update_bu')
     *
     * @return Response|ResponseInterface
     */
    public function updateBuAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_edit_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_info;
        $result = $department_service->updateOrganization($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }

    /**
     * Bu编辑 -sap
     * @Permission(action='organization.department.update_bu_sap')
     *
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function updateBuSapAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_edit_params);

        $department_service = new DepartmentService();
        $params['user'] = $this->user;
        $params['edit_type'] = DepartmentService::$organization_edit_sap;
        $result = $department_service->updateOrganization($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $result);
    }


    /**
     * 部门删除
     * @Permission(action='organization.department.del')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function delDepartmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_del_params);
        $department_service = new DepartmentService();
        $params['type']     = [
            Enums\OrganizationDepartmentEnums::ORGANIZATION_BU_DEPARTMENT_TYPE,
            Enums\OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_DEPARTMENT_TYPE,
        ];
        $params['user']     = $this->user;
        $result             = $department_service->DepartmentDel($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * Bu删除
     * @Permission(action='organization.department.del.bu')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function delBuAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_del_params);
        $department_service = new DepartmentService();
        $params['type']     = [Enums\OrganizationDepartmentEnums::ORGANIZATION_BU_TYPE];
        $params['user']     = $this->user;
        $result             = $department_service->DepartmentDel($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * Clevel删除
     * @Permission(action='organization.department.del.clevel')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function delClevelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, DepartmentService::$validate_department_del_params);
        $department_service = new DepartmentService();
        $params['type']     = [Enums\OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_TYPE];
        $params['user']     = $this->user;
        $result             = $department_service->DepartmentDel($params);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     * 是否显示部门删除按钮
     * @Token
     * @return Response|ResponseInterface
     */
    public function isShowOrganizationDelAction()
    {
        $params        = $this->request->get();
        $department_id = $params['department_id'];
        if (empty($department_id)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Department ID is empty', []);
        }
        $department_service = new DepartmentService();
        $is_del = $department_service->isDelDepartment(['department_id' => $department_id]);
        return $this->returnJson(ErrCode::$SUCCESS, '', ['is_show' => $is_del]);
    }

    /**
     * 获取用户信息
     * @Permission(action='organization.department.view')
     *
     * @return Response|ResponseInterface
     */
    public function getHrStaffInfoAction()
    {
        $staff_info_id = $this->request->get('staff_info_id');

        if (empty($staff_info_id)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Staff ID is empty', []);
        }
        //验证工号是否正确
        $staff_info_id_error = $this->validate_staff_id($staff_info_id, $hr_staff_info);
        if ($staff_info_id_error) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $staff_info_id_error, []);
        }
        $return = [
            'staff_info' => $hr_staff_info[$staff_info_id],
        ];

        return $this->returnJson(ErrCode::$SUCCESS, '', $return);
    }

    /**
     * 大区修改负责人
     * @Permission(action='organization.department.update')
     * @return Response|ResponseInterface
     */
    public function updateRegionManagerAction() {
        try {
            $params = $this->request->get();

            $validate_params = [
                'id'   => 'Required|IntGt:0',
                'manager_id' => 'Required|IntGt:0',
                'manager_position_state' => 'IntIn:1,2', //1正职 2代理
            ];
            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $confirm = $params['confirm'] ?? 0;
        $id = $params['id'];
        $manager_id = $params['manager_id'];
        $operator_id = $this->user['id']; //登陆人id
        //验证工号是否正确
        $manager_id_error = $this->validate_staff_id($manager_id);
        if ($manager_id_error) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $manager_id_error, []);
        }

        $region_detail = (new SysManageRegionPieceService())->getRegionDetail($id);
        if(empty($region_detail)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Manager ID error', []);
        }
        $before = [
            'manager_id' => $region_detail['manager_id'] ?? 0,
            'manager_name' => $region_detail['manager_name'] ?? '',
            'manager_position_state' => $region_detail['manager_position_state'] ?? 0
        ];

        $countryCode = strtoupper(env('country_code', GlobalEnums::TH_COUNTRY_CODE));
        $manager_position_state = $params['manager_position_state'] ?? null;
        if(!empty($manager_id) && in_array($countryCode, DepartmentService::$nw_manager_country_list) && $region_detail['type'] == 1 && $manager_position_state == 1 && $confirm != 1) {
            //验证正职负责人是否是有多个
            $validate_result = (new DepartmentService())->validate_manager_position_state(['region_id' => $id, 'manager_id' => $manager_id]);
            if($validate_result['code'] == 0) {
                $msg = $this->t['department_manager_position_msg'] . "<br/>" . $validate_result['msg'];
                return $this->returnJson(ErrCode::$SUCCESS, $msg, ['confirm' => 1]);
            }
        }

        $api_params = [
            'region_id' => (string)$id,
            'manager_id' => $manager_id,
            'operator_id' => $operator_id,
            'manager_position_state' => $params['manager_position_state'] ?? null
        ];
        $return = (new SysManageRegionPieceService())->sync_manage_region_manager_ms($api_params);
        if (isset($return['error'])) {
            $this->logger->error("updateRegionManagerAction-request_params:" . json_encode($api_params) . ', response:' . json_encode($return));
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $return['error']);
        }

        //同步直线上级变更
        if(in_array($countryCode, DepartmentService::$nw_manager_country_list) && $region_detail['type'] == 1 && $manager_id != $region_detail['manager_id']) {
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'region_id' => $id,
                    'manager_id'  => $manager_id,
                    'operator_id' => $operator_id,
                    'update_type' => 2
                ]
            ]);
            $res = $hris_ac->execute();
        }

        //如果设置当前组织为正职，则其他负责人都变更成代理
        if(in_array($countryCode, DepartmentService::$nw_manager_country_list) && $region_detail['type'] == 1 && $manager_position_state == 1) {
            $update_department_param = [
                'manager_id' => $manager_id,
                'user' => $this->user,
                'department_id' => ''
            ];
            (new DepartmentService())->updateOtherOrganizationManagerPositionState($update_department_param);
            $region_param = [
                'region_id' => $id,
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new SysManageRegionPieceService())->updateManagerRegionPositionState($region_param);
            $piece_param = [
                'piece_id' => '',
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new SysManageRegionPieceService())->updateManagerPiecePositionState($piece_param);
            $store_param = [
                'store_id' => '',
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new StoreService())->updateStorePositionState($store_param);
        }

        $region = (new SysManageRegionPieceService())->getRegionDetailByFle($id);
        $after = [
            'manager_id' => $region['manager_id'] ?? 0,
            'manager_name' => $region['manager_name'] ?? '',
            'manager_position_state' => $region['manager_position_state'] ?? 0
        ];
        (new DeptPcCodeService())->saveDeptOperateLog($this->user['id'], $this->user['name'], $before, $after, $id, SysDeptOperateLogsModel::TYPE_REGION);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $return['result']);
    }

    /**
     * 片区修改负责人
     * @Permission(action='organization.department.update')
     * @return Response|ResponseInterface
     */
    public function updatePieceManagerAction() {
        try {
            $params = $this->request->get();
            $validate_params = [
                'id'   => 'Required|IntGt:0',
                'manager_id' => 'Required|IntGt:0',
                'manager_position_state' => 'IntIn:1,2', //1正职 2代理
            ];
            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $confirm = $params['confirm'] ?? 0;
        $id = $params['id'];
        $manager_id = $params['manager_id'];
        $operator_id = $this->user['id']; //登陆人id
        //验证工号是否正确
        $manager_id_error = $this->validate_staff_id($manager_id);
        if ($manager_id_error) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $manager_id_error, []);
        }

        $piece_detail = (new SysManageRegionPieceService())->getPieceDetail($id);
        if(empty($piece_detail)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'Piece ID error', []);
        }

        $before = [
            'manager_id' => $piece_detail['manager_id'] ?? 0,
            'manager_name' => $piece_detail['manager_name'] ?? '',
            'manager_position_state' => $piece_detail['manager_position_state'] ?? 0
        ];

        $countryCode = strtoupper(env('country_code', 'TH'));
        $manager_position_state = $params['manager_position_state'] ?? null;
        if(!empty($manager_id) && in_array($countryCode, DepartmentService::$nw_manager_country_list) && $piece_detail['manager_region_type'] == 1 && $manager_position_state == 1 && $confirm != 1) {
            //验证正职负责人是否是有多个
            $validate_result = (new DepartmentService())->validate_manager_position_state(['piece_id' => $id, 'manager_id' => $manager_id]);
            if($validate_result['code'] == 0) {
                $msg = $this->t['department_manager_position_msg'] . "<br/>" . $validate_result['msg'];
                return $this->returnJson(ErrCode::$SUCCESS, $msg, ['confirm' => 1]);
            }
        }

        $api_params = [
            'piece_id' => (string)$id,
            'manager_id' => $manager_id,
            'operator_id' => $operator_id,
            'manager_position_state' => $params['manager_position_state'] ?? null
        ];
        $return = (new SysManageRegionPieceService())->sync_manage_piece_manager_ms($api_params);
        if (isset($return['error'])) {
            $this->getDI()->get('logger')->error("updatePieceManagerAction-request_params:" . json_encode($api_params) . ', response:' . json_encode($return));
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $return['error']);
        }

        //同步直线上级变更
        if(in_array($countryCode, DepartmentService::$nw_manager_country_list) && $piece_detail['manager_region_type'] == 1 && $manager_id != $piece_detail['manager_id']) {
            //变更
            $hris_ac = new ApiClient('hris', '', 'oa_sync_staff_manager_update');
            $hris_ac->setParams([
                [
                    'piece_id' => $id,
                    'manager_id'  => $manager_id,
                    'operator_id' => $operator_id,
                    'update_type' => 3
                ]
            ]);
            $res = $hris_ac->execute();
        }
        //如果设置当前组织为正职，则其他负责人都变更成代理
        if(in_array($countryCode, DepartmentService::$nw_manager_country_list) && $piece_detail['manager_region_type'] == 1 && $manager_position_state == 1) {
            $update_department_param = [
                'manager_id' => $manager_id,
                'user' => $this->user,
                'department_id' => ''
            ];
            (new DepartmentService())->updateOtherOrganizationManagerPositionState($update_department_param);
            $region_param = [
                'region_id' => '',
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new SysManageRegionPieceService())->updateManagerRegionPositionState($region_param);
            $piece_param = [
                'piece_id' => $id,
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new SysManageRegionPieceService())->updateManagerPiecePositionState($piece_param);
            $store_param = [
                'store_id' => '',
                'manager_id' => $manager_id,
                'user' => $this->user
            ];
            (new StoreService())->updateStorePositionState($store_param);
        }

        $piece = (new SysManageRegionPieceService())->getPieceDetailByFle($id);
        $after = [
            'manager_id' => $piece['manager_id'] ?? 0,
            'manager_name' => $piece['manager_name'] ?? '',
            'manager_position_state' => $piece['manager_position_state'] ?? 0
        ];
        (new DeptPcCodeService())->saveDeptOperateLog($this->user['id'], $this->user['name'], $before, $after, $id, SysDeptOperateLogsModel::TYPE_PIECE);

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $return['result']);
    }

    /**
     * 网点修改负责人
     * @Permission(action='organization.department.update')
     * @return Response|ResponseInterface
     * @throws BusinessException
     */
    public function updateStoreManagerAction() {
        try {
            $params = $this->request->get();
            $validate_params = [
                'id'   => 'Required|StrLenGe:1',
                'manager_id' => 'Required|IntGt:0',
                'manager_position_state' => 'IntIn:1,2', //1正职 2代理
            ];
            Validation::validate($params, $validate_params);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['user'] = $this->user;

        $result = (new StoreService())->updateStoreManager($params);
        if($result !== true) {
            return $this->returnJson($result['code'], $result['msg'], $result['data']);
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', []);
    }

    /**
     * 验证工号是否正确，错误直接返回错误提示
     *
     * @param $staff_id
     * @param $hr_staff_info
     * @return array|Response|ResponseInterface
     */
    private function validate_staff_id($staff_id, &$hr_staff_info = [])
    {
        //验证工号是否正确
        $hr_service    = new HrStaffInfoService();
        $hr_staff_info = $hr_service->getHrStaffInfo([$staff_id]);
        if (empty($hr_staff_info)) {
            return $this->t['department.staff_id_error1'];
        }

        if ($hr_staff_info[$staff_id]['state'] != 1) {
            return $this->t['department.staff_id_error2'];
        }

        if ($hr_staff_info[$staff_id]['formal'] != 1) {
            return $this->t['department.staff_id_error3'];
        }

        return '';


    }

    /**
     * sap枚举值
     * @Token
     */
    public function sysInfoAction() {
        $sap_company_list = \App\Modules\Common\Models\EnvModel::getEnvByCode("sap_company_ids");
        $sap_cost_center_list = \App\Modules\Common\Models\EnvModel::getEnvByCode("sap_cost_centers");

        $batch_create_department_key = 'batch_create_department_' . $this->locale;
        $batch_create_department_file = \App\Modules\Common\Models\EnvModel::getEnvByCode($batch_create_department_key);
        $item = [
            'sap_company_list' => explode(',', $sap_company_list),
            'sap_cost_center_list' => explode(',', $sap_cost_center_list),
            'batch_create_department_file' => $batch_create_department_file,
        ];

        return $this->returnJson(ErrCode::$SUCCESS, 'ok', $item);
    }

    /**
     * 变更记录
     * @Token
     */
    public function getDeptOperateLogAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required', 'type' => 'Required|StrLenGe:1']);

        $dept_pc_code_service = new DeptPcCodeService();
        $where                = ['id' => $params['id'], 'type' => $params['type']];
        $list                 = $dept_pc_code_service->getOperateLogList($where);
        $result['items']      = $list;
        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * 按照网点名称模糊搜索
     * @Permission(action='organization.department.view')
     * @return Response|ResponseInterface
     */
    public function searchStoreListAction() {
        try{
            $params = $this->request->get();
            try {
                $validate_list_search = [
                    'pageSize' => 'IntGt:0', //每页条数
                    'pageNum' => 'IntGt:0', //页码
                    'search_name' => 'StrLenGeLe:0,200', //网点名称
                ];

                $params = BaseService::handleParams($params, ['search_name', 'pageSize', 'pageNum']);
                Validation::validate($params, $validate_list_search);
            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }

            $store_service = new StoreService();
            $data = $store_service->search_store_list($params, $this->user['id'] ?? 0);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 按照部门名称模糊搜索
     * @Permission(action='organization.department.view')
     * @return Response|ResponseInterface
     */
    public function searchDepartmentListAction() {
        try{
            $params = $this->request->get();
            try {
                $validate_list_search = [
                    'pageSize' => 'IntGt:0', //每页条数
                    'pageNum' => 'IntGt:0', //页码
                    'search_name' => 'StrLenGeLe:0,200', //网点名称
                ];

                $params = BaseService::handleParams($params, ['search_name', 'pageSize', 'pageNum']);
                Validation::validate($params, $validate_list_search);
            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }

            $department_service = new DepartmentService();
            $data = $department_service->search_department_list($params , $this->user['id'] ?? 0);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 导出网点数据
     * @Permission(action='organization.department.orginazation_store_export')
     *
     * @return Response|ResponseInterface
     */
    public function exportStoreAction() {
        try {
            $store_service = new StoreService();

            $res = $store_service->exportStore($this->user['id'] ?? 0);

            if ($res['code'] == ErrCode::$SUCCESS) {
                return $this->returnJson(ErrCode::$SUCCESS, 'ok', $res['data']);
            }

            return $this->returnJson($res['code'], $res['data']);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 批量创建部门
     * @Permission(action='organization.department.batch_create_department')
     *
     * @return Response|ResponseInterface
     */
    public function batchCreateDepartmentAction() {
        try {
            $excel_file = $this->request->getUploadedFiles();

            $file_extension = $excel_file[0]->getExtension();
            if (!in_array($file_extension ,['xls', 'xlsx'])){
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'error', ['msg' => 'File format error']);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setSkipRows(1)
                ->getSheetData();

            $params = [
                'operator_id' => $this->user['id'],
                'operator_name' => $this->user['name'],
                'file_name' => $_FILES['file']['name'] ?? '',
                'excel_data' => $excel_data,
            ];
            $department_service = new DepartmentService();
            $data = $department_service->batch_create_department($params);

            return $this->returnJson($data['code'], $data['msg'], $data['data']);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 批量创建部门log
     * @Permission(action='organization.department.batch_create_department')
     *
     * @return Response|ResponseInterface
     */
    public function getBatchCreatDepartmentLogAction() {
        try {
            $params = $this->request->get();
            try {
                $validate_list_search = [
                    'pageSize' => 'IntGt:0', //每页条数
                    'pageNum' => 'IntGt:0', //页码
                ];

                $params = BaseService::handleParams($params, ['search_name', 'pageSize', 'pageNum']);
                Validation::validate($params, $validate_list_search);
            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }

            $store_service = new DepartmentService();
            $data = $store_service->get_batch_create_department_log_list($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * 网点搜索根据网点分类
     * @Token
     * @return Response|ResponseInterface
     */
    public function searchStoreCategoryListAction() {
        try{
            $params = $this->request->get();
            try {
                $validate_list_search = [
                    'search_name' => 'StrLenGeLe:0,200', //网点名称
                    'id' => 'Int', //部门id
                ];

                $params = BaseService::handleParams($params, ['search_name']);
                Validation::validate($params, $validate_list_search);
            } catch (ValidationException $e) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
            }

            $store_service = new StoreService();
            $data = $store_service->search_store_category_list($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', $data);
        } catch (\Exception $e) {
            //错误日志
            $log = array(
                'file'  => $e->getFile(),
                'line'  => $e->getLine(),
                'code'  => $e->getCode(),
                'msg'   => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            );
            $this->getDI()->get('logger')->error(json_encode($log));
            return $this->returnJson(0, 'error', ['msg'=>$e->getMessage()]);
        }
    }

    /**
     * Notes: 部门选择组件获取部门
     * @Token
     * @return Response|ResponseInterface
     */
    public function getSubDepartmentByIdAction()
    {
        $params = $this->request->get();
        $data   = (new DepartmentService())->getSubDepartmentById($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * * @Token
     * @return Response|ResponseInterface
     */
    public function getDepartmentByKeyAction()
    {
        $paramIn = $this->request->get();
        $data = (new DepartmentService())->search($paramIn);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 大区片区网点列表
     * 部门请求  department_id 有值   region_id为空   piece_id为空
     * 大区请求  department_id 有值   region_id有值   piece_id为空
     * 片区请求  department_id 有值   region_id有值   piece_id有值
     * @Permission(action='organization.department.view')
     * @return Response|ResponseInterface
     */
    public function getManageCommonListAction()
    {
        $parmas['department_id']        = $this->request->get('department_id', 'int', 0);
        $parmas['region_id']            = $this->request->get('region_id', 'int', 0);
        $parmas['piece_id']             = $this->request->get('piece_id', 'int', 0);
        $parmas['franchisee_region_id'] = $this->request->get('franchisee_region_id', 'int', 0);
        $parmas['franchisee_piece_id']  = $this->request->get('franchisee_piece_id', 'int', 0);
        $staff_info_id                  = $this->user['id'] ?? 0;

        $result['items'] = (new SysManageRegionPieceService())->getManageCommonList($parmas, $staff_info_id);

        return $this->returnJson(ErrCode::$SUCCESS, '', $result);
    }

    /**
     * 加盟商大区负责人修改
     * @Permission(action='organization.department.update')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateFranchiseeRegionManagerAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id'           => 'Required|IntGt:0',    //加盟商大区id
            'manager_id'   => 'Required|IntGt:0',    //负责人工号
        ]);

        $params['user'] = $this->user;

        $service = new SysManageRegionPieceService();
        $result  = $service->updateFranchiseeRegionManager($params);
        if($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', []);
        }
        return $this->returnJson(ErrCode::$SUCCESS, $result['message'], []);
    }

    /**
     * 加盟商片区负责人修改
     * @Permission(action='organization.department.update')
     * @return Response|ResponseInterface
     * @throws BusinessException
     * @throws ValidationException
     */
    public function updateFranchiseePieceManagerAction()
    {
        $params = $this->request->get();
        Validation::validate($params, [
            'id'         => 'Required|IntGt:0',    //加盟商大区id
            'manager_id' => 'Required|IntGt:0',    //负责人工号
        ]);

        $params['user'] = $this->user;

        $service = new SysManageRegionPieceService();
        $result  = $service->updateFranchiseePieceManager($params);
        if($result['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', []);
        }
        return $this->returnJson(ErrCode::$SUCCESS, $result['message'], []);
    }
}
