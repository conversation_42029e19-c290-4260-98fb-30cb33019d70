<?php


namespace app\modules\Organization\models;


use App\Library\BaseModel;

class SysDeptOperateLogsModel extends BaseModel
{
    const TYPE_DEPT = 'dept';
    const TYPE_REGION = 'region';
    const TYPE_PIECE = 'piece';
    const TYPE_STORE = 'store';
    const TYPE_FRANCHISEE_REGION = 'franchisee_region';
    const TYPE_FRANCHISEE_PIECE = 'franchisee_piece';

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('sys_dept_operate_logs');
    }

}