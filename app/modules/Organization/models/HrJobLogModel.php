<?php

namespace App\Modules\Organization\Models;

use App\Models\Base;

class HrJobLogModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_job_log');
    }

    //1.新增职位 2.修改职位 3.新建关联 4. 编辑关联 5.解除关联 6.修改HC
    const JOB_LOG_TYPE_NEW_JOB = 1;
    const JOB_LOG_TYPE_MODIFY_JOB = 2;
    const JOB_LOG_TYPE_NEW_RELATION = 3;
    const JOB_LOG_TYPE_EDIT_RELATION = 4;
    const JOB_LOG_TYPE_REMOVE_RELATION = 5;
    const JOB_LOG_TYPE_MODIFY_PLAN_HC = 6;

}