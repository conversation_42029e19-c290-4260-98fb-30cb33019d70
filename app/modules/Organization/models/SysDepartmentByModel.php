<?php

namespace App\Modules\Organization\Models;

use App\Models\Base;

class SysDepartmentByModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('sys_department');
    }


    /**
     * 根据部门id找对应公司id=包括删除的
     * @param $department_id
     * @return int
     */
    public static function getCompanyIdByDepartmentId($department_id)
    {
        $item = self::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind' => ['id' => $department_id]
            ]
        );
        $company_id = null;
        if (!empty($item)) {
            $company_id = $item->company_id;
        }
        return $company_id ?? 0;
    }


    /**
     * 根据公司id找到对应公司名字=包括删除的
     * @param $company_id
     * @return string
     */
    public static function getCompanyNameByCompanyId($company_id)
    {
        if (empty($company_id)) {
            return '';
        }

        $item = self::findFirst(
            [
                'conditions' => 'id = :id:',
                'bind' => ['id' => $company_id]
            ]
        );
        $company_name = '';
        if (!empty($item)) {
            $company_name = $item->name;
        }
        return $company_name ?? '';
    }

}