<?php

namespace App\Modules\Organization\Models;

use App\Models\Base;

class HrJobTitleByModel extends Base
{

    //职位有效状态
    public const STATUS_VALID = 1; //有效
    public const STATUS_NOT_VALID = 2; //无效

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_job_title');
    }

}