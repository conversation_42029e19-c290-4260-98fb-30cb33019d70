<?php

namespace App\Modules\Organization\Models;

use App\Models\Base;

class HrJobDepartmentRelationModel extends Base
{
    //制定计划状态
    public const PLAN_STATE_NOT_FORMULATE = 0;              // 没制定
    public const PLAN_STATE_FORMULATE = 1;                  // 制定

    //是否同步HC预算人数
    public const SYNC_HC_BUDGET_STATE_DO_NOT_SYNC = 0;      // 不同步
    public const SYNC_HC_BUDGET_STATE_DO_SYNC = 1;          // 同步

    //职位性质
    const POSITION_TYPE_0 = 0;//
    const POSITION_TYPE_1 = 1;//一线操作
    const POSITION_TYPE_2 = 2;//一线职能
    const POSITION_TYPE_3 = 3;//总部职能

    public static $position_type_list = [
        self::POSITION_TYPE_0 => 'position_type_0',
        self::POSITION_TYPE_1 => 'position_type_1',
        self::POSITION_TYPE_2 => 'position_type_2',
        self::POSITION_TYPE_3 => 'position_type_3',
    ];

    //成本类型
    const COST_TYPE_0 = 0;//未填写
    const COST_TYPE_1 = 1;//运营
    const COST_TYPE_2 = 2;//销售
    const COST_TYPE_3 = 3;//管理

    const COST_TYPE_4 = 4;//研发

    public static $cost_type_list = [
        self::COST_TYPE_0 => 'cost_type_0',
        self::COST_TYPE_1 => 'cost_type_1',
        self::COST_TYPE_2 => 'cost_type_2',
        self::COST_TYPE_3 => 'cost_type_3',
        self::COST_TYPE_4 => 'cost_type_4',
    ];

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_job_department_relation');
    }

    /**
     * 获取bi库下旧的部门职位职级关联关系数据
     * @return mixed
     */
    public function doJobLevelRelation(){
        $sql = "select department_id,job_title_id,GROUP_CONCAT(job_title_grade) levels ,create_at from hr_department_job_title_level_grade GROUP BY department_id,job_title_id";
        $data = $this->getDI()->get('db_rbi')->query($sql)->fetchAll(\Phalcon\Db::FETCH_ASSOC);
        $total = 0;
        $success = 0;
        echo '处理中...'.PHP_EOL;

        foreach ($data as $item){
            $level_ls = explode(',',$item['levels']);
            $new_levels = '';
            foreach ($level_ls as $level){
                $new_levels .= self::newLevelMap($level).',';
            }
            $new_levels = trim($new_levels,',');
            $insert_sql = "insert ignore into hr_job_department_relation(`department_id`,`job_id`,`job_level`,`created_at`, `updated_at`) value ('{$item['department_id']}','{$item['job_title_id']}','{$new_levels}','{$item['create_at']}','{$item['create_at']}')";
            $res = $this->getDI()->get('db_backyard')->execute($insert_sql);
            $total ++;
            if(!$res){
                $this->getDI()->get('logger')->warning('职位体系-迁移旧职位、部门、职级 绑定关系失败数据: ' . $insert_sql);
            }else{
                $success ++;
            }

        }
        echo "处理完成，共处理$total 条，成功{$success}条";

    }

    /**
     * 根据历史职级获取新职级
     * @param $old_level
     * @return string
     */
    private static function newLevelMap($old_level){
       if(empty($old_level)) return '0';
       switch ($old_level) {
           case 1:
           case 2:
                return '12';
                 break;
           case 3:
               return '13';
               break;
           case 4:
               return '14';
               break;
           case 5:
           case 6:
               return '15';
               break;
           case 7:
           case 8:
               return '16';
               break;
           case 9:
           case 10:
               return '17';
               break;
           case 11:
           case 12:
               return '18';
               break;
           case 13:
               return '19';
               break;
           case 14:
               return '21';
               break;
           case 15:
               return '23';
               break;
           case 16:
               return '24';
               break;
       }

    }
}