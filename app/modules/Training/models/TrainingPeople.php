<?php

namespace  App\Modules\Training\Models;

use App\Models\Base;

class TrainingPeople extends Base
{

    /**
     * id
     * @var int
     */
    private $id;

    /**
     * 员工id
     * @var int
     */
    public $staff_id;

    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('training_people');
    }


    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_backyard');
        }
        return parent::refresh();
    }

}
