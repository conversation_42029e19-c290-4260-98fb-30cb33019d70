<?php

namespace App\Modules\Training\Services;

use App\Library\ApiClient;
use App\Library\Validation\ValidationException;
use App\Modules\Training\Models\CorruptionSuspects;


class CorruptionService extends BaseService
{

    private static $instance;

    /**
     * 构造函数
     * ActivityService constructor.
     */
    public function __construct()
    {
    }

    /**
     * 类实例
     * @return CorruptionService
     */
    public static function getInstance(): CorruptionService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     *
     * @throws ValidationException
     */
    public function sendRequest($action, $params)
    {
        $ret = new ApiClient('ard_api_svc', '', $action, static::$language);
        $ret->setParams([$params]);
        $res = $ret->execute();
        if (isset($res['error'])) {
            throw new ValidationException($res['error']);
        }
        return $res['result'];
    }
}