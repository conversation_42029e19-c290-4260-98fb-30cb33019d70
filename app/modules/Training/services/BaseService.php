<?php

namespace App\Modules\Training\Services;

use App\Modules\Contract\Models\SysDepartmentModel;
use App\Modules\Contract\Models\SysManageRegionModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\Training\Models\TrainingPool;
use App\Modules\User\Models\HrJobTitleModel;
use App\Modules\User\Services\StaffService;

class BaseService extends \App\Library\BaseService
{

    /**
     *
     * 培训类型 枚举
     */
    const TRAINING_TYPE_1 = 1;
    const TRAINING_TYPE_2 = 2;
    const TRAINING_TYPE_3 = 3;
    const TRAINING_TYPE_4 = 4;
    const TRAINING_TYPE_5 = 5;
    const TRAINING_TYPE_6 = 6;
    const TRAINING_TYPE_7 = 7;
    const TRAINING_TYPE_8 = 8;
    const TRAINING_TYPE_9 = 9;
    const TRAINING_TYPE_10 = 10;
    const TRAINING_TYPE_11 = 11;
    const TRAINING_TYPE_12 = 12;

    /**
     * 培训类型 解释
     */
    const TRAINING_TYPE_DESC = [
        self::TRAINING_TYPE_1 => '员工入职安全培训证书',
        self::TRAINING_TYPE_2 => '管理人员安全培训证书',
        self::TRAINING_TYPE_3 => '主管安全培训证书',
        self::TRAINING_TYPE_4 => '安全员技能培训证书',
        self::TRAINING_TYPE_5 => '高级技术人员安全培训',
        self::TRAINING_TYPE_6 => '安全卫生组织培训',
        self::TRAINING_TYPE_7 => '基本消防知识培训',
        self::TRAINING_TYPE_8 => '消防演习培训',
        self::TRAINING_TYPE_9 => '叉车安全操作资格',
        self::TRAINING_TYPE_10 => '建筑内电路维修资格',
        self::TRAINING_TYPE_11 => '紧急急救知识培训',
        self::TRAINING_TYPE_12 => '机台操作安全培训'
    ];

    /**
     * 培训机构 枚举
     *
     */
    const TRAINING_ORGANIZATION_1 = 1;
    const TRAINING_ORGANIZATION_2 = 2;
    const TRAINING_ORGANIZATION_3 = 3;

    const TRAINING_ORGANIZATION_DESC = [
        self::TRAINING_ORGANIZATION_1 => 'EHSMAN',
        self::TRAINING_ORGANIZATION_2 => 'EHS部门',
        self::TRAINING_ORGANIZATION_3 => '外部政府机构',
    ];


    /**
     * 培训任务 创建培训任务菜单
     * @return array[]
     */
    public function taskMenuList()
    {
        $menus = [
            'train_list' => [], // 培训类型
            'train_organization' => [], // 培训机构
            'train_place' => [], // 培训地点
            'train_status' => [[
                'id' => 1, 'name' => static::$t->_('training_status_1')
            ], [
                'id' => 2, 'name' => static::$t->_('training_status_2')
            ], [
                'id' => 3, 'name' => static::$t->_('training_status_3')
            ], [
                'id' => 4, 'name' => static::$t->_('training_status_4')
            ]] ,// 培训状态
            'task_status' => [[
                'id' => 1, 'name' => static::$t->_('task_status_1')
            ], [
                'id' => 2, 'name' => static::$t->_('task_status_2')
            ], [
                'id' => 3, 'name' => static::$t->_('task_status_3')
            ], [
                'id' => 4, 'name' => static::$t->_('task_status_4')
            ]] // 任务状态
        ];
        // 培训类型
        foreach (self::TRAINING_TYPE_DESC as $k => $v) {
            $menus['train_list'][] = [
                'id' => $k,
                'name' => static::$t->_('training_type_' . $k)
            ];
        }
        // 培训机构
        foreach (self::TRAINING_ORGANIZATION_DESC as $k => $v) {
            $menus['train_organization'][] = [
                'id' => $k,
                'name' => static::$t->_('training_organization_' . $k)
            ];
        }

        $menus['train_place'] = $this->getStoreList();

        foreach (TaskService::TRAINING_DESC as $K => $v) {
            $menus['train_mode'][] = [
                'id' => $K,
                'name' => static::$t->_('training_mode_' . $K),
            ];
        }

        return $menus;
    }



    /**
     * 网点名称模糊搜索网点
     * @return array
     */
    public function getStoreList()
    {
        $builder = $this->modelsManager->createBuilder()
            ->columns('id as store_id,name')
            ->from(SysStoreModel::class)
            ->where('state = :state:', ['state' => 1]);
        $list = $builder->getQuery()->execute()->toArray();
        $head_office = ['store_id' => '-1', 'name' => 'head office'];

        array_unshift($list, $head_office);
        return $list;
    }

    /**
     * 菜单选项
     *
     * @param $trainingType int 培训类型
     * @return array[]
     *
     */
    public function menuList($trainingType)
    {
        $menus = [
            'regions' => [], // 大区
            'pieces' => [], // 片区
            'job_title' => [], // 职位
            'departments' => [], // 部门
//            'job_title_level' => [], // 职等
            'train_status' => [], // 培训状态
            'store_list' => [], // 网点列表
            'job_title_grade' => [], // 职级
        ];

        $menus['regions'] = $this->manageRegions($trainingType);
        $menus['pieces'] = $this->managePieces(array_column($menus['regions'], 'id'));
        $menus['store_list'] = $this->storeList($trainingType);
        $menus['job_title'] = $this->jobTitleList($trainingType);
        $menus['departments'] = $this->departmentList($trainingType);
//        $menus['job_title_level'] = $this->jobLevelList($trainingType);
        $menus['job_title_grade'] = $this->jobGradeList($trainingType);
        // 培训状态
        $menus['train_status'] = [[
            'id' => 1, 'name' => static::$t->_('training_status_1')
        ], [
            'id' => 4, 'name' => static::$t->_('training_status_4')
        ]];
        return $menus;
    }

    /**
     * 管理大区
     *
     * @param $trainingType
     *
     */
    private function manageRegions($trainingType)
    {
        $regionIds = $this->searchColumnForPool($trainingType);

        return $regionIds ? SysManageRegionModel::find([
            'columns' => 'id, name',
            'conditions' => 'id in ({ids:array}) ',
            'bind' => [
                'ids' => $regionIds,
            ]
        ])->toArray() : [];

    }


    /**
     * 管理片区
     *
     * @param $regionIds
     *
     */
    private function managePieces($regionIds)
    {
        return $regionIds ? SysManagePieceModel::find([
            'columns' => 'id, manage_region_id, name',
            'conditions' => 'manage_region_id in ({regionIds:array})',
            'bind' => [
                'regionIds' => $regionIds,
            ]
        ])->toArray() : [];

    }

    /**
     * 职位
     *
     * @param $trainingType
     *
     */
    private function jobTitleList($trainingType)
    {
        $jobTitles = $this->searchColumnForPool($trainingType, 'job_title');
        return $jobTitles ? HrJobTitleModel::find([
            'columns' => 'id,job_name',
            'conditions' => ' id in ({ids:array}) ',
            'bind' => ['ids' => $jobTitles]
        ])->toArray() : [];

    }

    /**
     * 所属网点
     *
     * @param $trainingType
     *
     */
    private function storeList($trainingType)
    {
        $storeIds = $this->searchColumnForPool($trainingType, 'sys_store_id');
        $storeList = $storeIds ? SysStoreModel::find([
            'columns' => 'id, name',
            'conditions' => ' id in ({sys_store_id:array}) ',
            'bind' => [
                'sys_store_id' => $storeIds
            ]
        ])->toArray() : [];

        return array_merge([['id' => -1, 'name' => 'head office']], $storeList);
    }

    /**
     *
     * @param $trainingType
     * @return array
     *
     */
    private function departmentList($trainingType)
    {
        $departmentIds = $this->searchColumnForPool($trainingType, 'node_department_id');
        $nodeDepartments = $departmentIds ? SysDepartmentModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind' => [
                'ids' => $departmentIds
            ]
        ])->toArray() : [];

        $departmentIds = [];
        foreach ($nodeDepartments as $nodeDepartment) {
            $tmpIds = explode('/', $nodeDepartment['ancestry_v2']);
            $departmentIds = array_values(array_unique(array_merge($departmentIds, $tmpIds)));
        }

        return $departmentIds ?  $this->departments($departmentIds, 1) : [];


    }

    private function departments($departmentIds, $level = 1, $ancestry = 0)
    {
        if ($ancestry) {

            $departments = SysDepartmentModel::find([
                'columns' => 'id, name, level, ancestry_v2',
                'conditions' => ' id in ({ids:array}) and level = :level: and ancestry = :ancestry: ',
                'bind' => [
                    'ids' => $departmentIds,
                    'ancestry' => $ancestry,
                    'level' => $level
                ]
            ])->toArray();
        } else {

            $departments = SysDepartmentModel::find([
                'columns' => 'id, name, level, ancestry_v2',
                'conditions' => ' id in ({ids:array}) and level = :level: ',
                'bind' => [
                    'ids' => $departmentIds,
                    'level' => $level
                ]
            ])->toArray();
        }

        if ($departments) {
            $level++;
            foreach ($departments as $k => $department) {
                $departments[$k]['children'] = $this->departments($departmentIds, $level, $department['id']);
            }
        }

        return $departments;
    }

    private function jobGradeList($trainingType)
    {
        $grades = $this->searchColumnForPool($trainingType, 'job_title_grade');

        $results = [];
        foreach ($grades as $grade) {
            $results[] = ['id' => $grade, 'name' => 'F' . $grade];
        }

        return $results;
    }


    /**
     *
     * @param $trainingType
     *
     */
    private function jobLevelList($trainingType)
    {
        $jobLevels = [[
            'id' => 1, 'name' => 'Staff'
        ], [
            'id' => 2, 'name' => 'Supervisor'
        ], [
            'id' => 3, 'name' => 'Manager'
        ], [
            'id' => 4, 'name' => 'Executive'
        ]];

        $levelIds = $this->searchColumnForPool($trainingType, 'job_title_level');
        $results = [];
        foreach ($jobLevels as $jobLevel) {
            if (in_array($jobLevel['id'], $levelIds)) {
                $results[] = $jobLevel;
            }
        }

        return $results;
    }


    /**
     * @param $trainingType
     * @param string $column
     * @return array
     *
     */
    private function searchColumnForPool($trainingType, $column = 'manager_region')
    {
        $values = TrainingPool::find([
            'columns' => ' distinct ' . $column,
            'conditions' => ' training_type = :training_type: ',
            'bind' => [
                'training_type' => $trainingType
            ]
        ])->toArray();

        return array_values(array_filter(array_column($values, $column)));
    }


    /**
     * 根据大区 片区 获取网点列表
     *
     * @param $pieceId
     * @param $regionId
     *
     */
    public function storeListByPieceAndRegion($pieceId, $regionId)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['id,name']);
        $builder->from(['s'=>SysStoreModel::class]);
        $builder->where('state = 1');
        if ($pieceId) {
            $builder->andWhere('manage_piece = :manage_piece:',['manage_piece' => $pieceId]);
        }
        if ($regionId) {
            $builder->andWhere('manage_region = :manage_region:',['manage_region' => $regionId]);
        }
        $list = $builder->getQuery()->execute();
        $list = $list->toArray();

        return$list;

    }


    public function searchList($params)
    {
        $conditions = [];
        $binds = [];

        $conditions[] = ' training_type = :training_type: ';
        $binds['training_type'] = $params['training_type'];

        if (isset($params['staff_id']) && $params['staff_id']) {
            $conditions[] = ' staff_id = :staff_id: ';
            $binds['staff_id'] = $params['staff_id'];
        }

        if (isset($params['region_id']) && $params['region_id']) {
            $conditions[] = ' manager_region = :manager_region: ';
            $binds['manager_region'] = $params['region_id'];
        }

        if (isset($params['piece_id']) && $params['piece_id']) {
            $conditions[] = ' manager_piece = :manager_piece: ';
            $binds['manager_piece'] = $params['piece_id'];
        }

        if (isset($params['store_id']) && $params['store_id']) {
            $conditions[] = ' sys_store_id = :sys_store_id: ';
            $binds['sys_store_id'] = $params['store_id'];
        }


        if (isset($params['job_title']) && $params['job_title']) {
            $conditions[] = ' job_title = :job_title: ';
            $binds['job_title'] = $params['job_title'];
        }

        if (isset($params['job_title_level']) && $params['job_title_level']) {
            $conditions[] = ' job_title_level = :job_title_level: ';
            $binds['job_title_level'] = $params['job_title_level'];
        }

        if (isset($params['department_id']) && $params['department_id']) {
            $conditions[] = ' node_department_id = :node_department_id: ';
            $binds['node_department_id'] = $params['department_id'];
        }

    }

}