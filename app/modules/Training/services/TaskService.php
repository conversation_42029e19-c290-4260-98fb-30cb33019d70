<?php

namespace App\Modules\Training\Services;


use App\Library\ApiClient;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Training\Models\TrainingOperateLog;
use App\Modules\Training\Models\TrainingPeople;
use App\Modules\Training\Models\TrainingPool;
use App\Modules\Training\Models\TrainingTask;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\User\Services\UserService;
use App\Library\Enums;

class TaskService extends BaseService
{

    /**
     *  操作类型
     */
    const OPERATE_LOG_ACTION_CREATE = 1;
    const OPERATE_LOG_ACTION_EDIT = 2;
    const OPERATE_LOG_ACTION_AUDIT = 3;
    const OPERATE_LOG_ACTION_DEL = 4;

    /**
     *  操作类型 解释
     */
    const OPERATE_LOG_ACTION_DESC = [
        self::OPERATE_LOG_ACTION_CREATE => '创建',
        self::OPERATE_LOG_ACTION_EDIT => '编辑',
        self::OPERATE_LOG_ACTION_AUDIT => '审核',
        self::OPERATE_LOG_ACTION_DEL => '删除',
    ];


    const TASK_STATUS_1 = 1;
    const TASK_STATUS_2 = 2;
    const TASK_STATUS_3 = 3;
    const TASK_STATUS_4 = 4;

    const TASK_JOIN_1 = 1;
    const TASK_JOIN_2 = 2;
    const TASK_JOIN_3 = 3;
    /**
     * 参与状态
     */
    const TASK_JOIN_DESC = [
        self::TASK_JOIN_1 => '待定',
        self::TASK_JOIN_2 => '确认参加',
        self::TASK_JOIN_3 => '确认拒绝',
    ];

    const REJECTION_TYPE_1 = 1;
    const REJECTION_TYPE_2 = 2;
    const REJECTION_TYPE_3 = 3;
    const REJECTION_TYPE_4 = 4;
    const REJECTION_TYPE_5 = 5;
    /**
     * 拒绝类型
     */
    const REJECTION_TYPE_DESC = [
        self::REJECTION_TYPE_1 => '网点受到影响(网点爆仓)',
        self::REJECTION_TYPE_2 => '事假年假',
        self::REJECTION_TYPE_3 => '出了事故',
        self::REJECTION_TYPE_4 => '待离职',
        self::REJECTION_TYPE_5 => '其他',
    ];

    /**
     *
     * 培训状态
     */
    const TASK_STATUS_DESC = [
        self::TASK_STATUS_1 => '待培训',
        self::TASK_STATUS_2 => '待审核',
        self::TASK_STATUS_3 => '已完成',
        self::TASK_STATUS_4 => '已删除',
    ];


    const PEOPLE_STATUS_1 = 1;
    const PEOPLE_STATUS_9 = 9;
    const PEOPLE_STATUS_DESC = [
        self::PEOPLE_STATUS_1 => '有效',
        self::PEOPLE_STATUS_9 => '删除',
    ];

    const POOL_STATUS_1 = 1;
    const POOL_STATUS_2 = 2;
    const POOL_STATUS_3 = 3;
    const POOL_STATUS_4 = 4;
    const POOL_STATUS_DESC = [
        self::POOL_STATUS_1 => '未培训',
        self::POOL_STATUS_2 => '等待培训',
        self::POOL_STATUS_3 => '已经培训',
        self::POOL_STATUS_4 => '未通过',
    ];

    /**
     *
     * 培训方式
     *
     * 线下 ， 线上
     */
    const TRAINING_MODE_1 = 1;
    const TRAINING_MODE_2 = 2;

    const TRAINING_DESC = [
        self::TRAINING_MODE_1 => '线下培训',
        self::TRAINING_MODE_2 => '线上培训',
    ];

    /**
     * 培训消息推送
     */
    const PUSH_ADD  = 1;
    const PUSH_EDIT = 2;
    const PUSH_DEL  = 3;
    const PUSH_DEL_PEOPLE = 4;
    const PUSH_OPERATE_DESC = [
        self::PUSH_ADD => '任务初始化消息',
        self::PUSH_EDIT => '任务变更消息',
        self::PUSH_DEL => '任务取消消息',
        self::PUSH_DEL_PEOPLE => '删除人员'
    ];

    /**
     * @param $params
     *
     */
    public function staffList($params)
    {
        $conditions = [];
        $bind = [];
        if (isset($params['training_type']) && $params['training_type']) {
            $conditions[] = " training_type = :training_type: ";
            $bind['training_type'] = $params['training_type'];
        }
        if (isset($params['staff_id']) && $params['staff_id']) {
            $conditions[] = "  staff_id = :staff_id: ";
            $bind['staff_id'] = $params['staff_id'];
        }

        if (isset($params['region_id']) && $params['region_id']) {
            $conditions[] = "  manager_region = :region_id: ";
            $bind['region_id'] = $params['region_id'];
        }

        if (isset($params['piece_id']) && $params['piece_id']) {
            $conditions[] = "  manager_piece = :piece_id: ";
            $bind['piece_id'] = $params['piece_id'];
        }

        if (isset($params['job_title']) && $params['job_title']) {
            $conditions[] = "  job_title in ({job_title:array}) ";
            $bind['job_title'] = $params['job_title'];
        }

//        if (isset($params['job_title_grade']) && $params['job_title_grade']) {
//            $conditions[] = "  job_title_grade = :job_title_grade: ";
//            $bind['job_title_grade'] = $params['job_title_grade'];
//        }

        if (isset($params['department_id']) && $params['department_id']) {
            $conditions[] = "  ( node_department_id in ({department_id:array}) or sys_department_id in ({department_id:array}) ) ";
            $bind['department_id'] = $params['department_id'];
        }

        if (isset($params['status']) && $params['status']) {
            if (is_array($params['status']) && $params['status']) {
                $conditions[] = " training_status in ({status:array}) ";
                $bind['status'] = $params['status'];
            } else {
                $conditions[] = " training_status = :status: ";
                $bind['status'] = $params['status'];
            }
        }

        if (isset($params['store_id']) && $params['store_id']) {
            $conditions[] = " sys_store_id in ({store_id:array}) ";
            $bind['store_id'] = $params['store_id'];
        }

        if (isset($params['hire_start_date']) && $params['hire_start_date']
            &&
            isset($params['hire_end_date']) && $params['hire_end_date']
        ) {
            $conditions[] = "  hire_date >= :start_date: and hire_date <= :end_date: ";
            $bind['start_date'] = $params['hire_start_date'];
            $bind['end_date'] = $params['hire_end_date'];
        }

        $count = TrainingPool::count([
            'conditions' => implode(" and ", $conditions),
            'bind' => $bind,
        ]);

        $list = TrainingPool::find([
            'columns' => 'staff_id',
            'conditions' => implode(" and ", $conditions),
            'bind' => $bind,
            'limit' => $params['page_size'],
            'offset' => $params['page_size'] * ($params['page'] - 1)
        ])->toArray();
        $staffIds = array_column($list, 'staff_id');
        $staffs = [];
        if ($staffIds) {
            $staffs = HrStaffInfoModel::find([
                'columns' => ' staff_info_id as id, name ',
                'conditions' => ' staff_info_id in ({staffIds:array})',
                'bind' => [
                    'staffIds' => $staffIds
                ]
            ])->toArray();

        }

        return ['list' => $staffs, 'count' => $count];
    }


    /**
     * 创建任务
     *
     * @param $params
     * @param $userinfo
     */
    public function createTask($params, $userinfo)
    {
        // 创建任务编号
        $db = $this->getDI()->get('db_backyard');
        $date = gmdate('Y-m-d H:i:s');
        try {
            // 添加培训任务
            $db->begin();
            $taskNo = $this->genTMNo();
            $trainingTask = new TrainingTask();
            $trainingTask->task_no = $taskNo;
            $trainingTask->create_id = $userinfo['id'];
            $trainingTask->training_type = $params['training_type'];
            $trainingTask->training_organization = $params['training_organization'];
            $trainingTask->lecturer = $params['lecturer'];
            $trainingTask->training_place_id = $params['training_place_id'];
            $trainingTask->time_long_hour = $params['time_long_hour'];
            $trainingTask->time_long_minute = $params['time_long_minute'];
            $trainingTask->training_mode = $params['training_mode'];
            if ($params['training_place_id']) {
                if ($params['training_place_id'] == -1) {
                    $trainingTask->training_place = 'head office';
                } else {
                    $storeInfo = SysStoreModel::findFirst([
                        'conditions' => 'id = :store_id:',
                        'bind' => ['store_id' => $params['training_place_id']]
                    ]);

                    $trainingTask->training_place = $storeInfo->name;
                }
            } else {
                $trainingTask->training_place = $params['training_place'] ?? '';
            }
            $trainingTask->online_link = $params['online_link'] ?? '';
            $trainingTask->online_id = $params['online_id'] ?? '';
            $trainingTask->online_password = $params['online_password'] ?? '';

            $trainingTask->start_time = $params['start_time'];
            $trainingTask->end_time = $params['end_time'];
            $trainingTask->updated_time = $date;
            $trainingTask->status = self::TASK_STATUS_1;
            $trainingTask->save();

            foreach ($params['staff_ids'] as $staff_id) {
                // 培训任务人员管理表更新
                $model = $this->getTrainingPeople($taskNo, $staff_id);
                if (!$model) {
                   $model = new TrainingPeople();
                }
                $model->task_no = $taskNo;
                $model->operator_id = $userinfo['id'];
                $model->training_type = $params['training_type'];
                $model->staff_id = $staff_id;
                $model->status = self::PEOPLE_STATUS_1;
                $model->save();

                // 培训人员池状态更新
                $poolModel = $this->getTrainingPool($staff_id, $params['training_type']);
                $poolModel->training_status = self::POOL_STATUS_2;
                $poolModel->training_files = '';
                $poolModel->updated_time = $date;
                $poolModel->save();
            }

            //发送消息通知
            $t = self::getTranslation(strtolower(env('country_code', 'th')));
            $this->sendPush([
                'staff_info_ids'    => implode(',', $params['staff_ids']),
                'title'             => str_ireplace('{name}', $t->_('training_type_' . $params['training_type']), $t->_('train.msg4')),
                'content'           => 1,
                'related_id'        => $trainingTask->id,
                'operate'           => self::PUSH_ADD,
            ]);

            $this->addTaskOperateLog($taskNo, $userinfo['id'], self::OPERATE_LOG_ACTION_CREATE, [
                'training_type' => $params['training_type'],
                'training_organization' => $params['training_organization'],
                'lecturer' => $params['lecturer'],
                'training_place_id' => $params['training_place_id'],
                'training_place' => $trainingTask->training_place,
                'start_time' => $params['start_time'],
                'training_mode' => $params['training_mode'],
                'online_link' => $params['online_link'],
                'online_id' => $params['online_id'],
                'online_password' => $params['online_password'],
                'end_time' => $params['end_time'],
                'staff_ids' => $params['staff_ids'] ? json_encode($params['staff_ids'], JSON_UNESCAPED_UNICODE) : '{}',
            ]);

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }

        return true;
    }

    /**
     *
     * 删除任务
     *
     * @param $taskNo
     * @param $userinfo
     *
     */
    public function delTask($taskNo, $userinfo)
    {
        $db = $this->getDI()->get('db_backyard');
        $date = gmdate('Y-m-d H:i:s');

        try {
            $db->begin();

            $trainingTask = TrainingTask::findFirst([
                'conditions' => ' task_no = :task_no: ',
                'bind' => [
                    'task_no' => $taskNo
                ],
                'for_update' => true
            ]);
            if (!$trainingTask) {
                throw new \Exception('no task data');
            }
            //二期新加，开始前7天之后的时间不可以删除
            if (strtotime($trainingTask->start_time . ' -2 days') < time()) {
                throw new ValidationException('could not delete !');
            }
            // 培训任务
            $trainingTask->handler_id = $userinfo['id'];
            $trainingTask->handler_time = gmdate('Y-m-d H:i:s');
            $trainingTask->status = self::TASK_STATUS_4;
            $trainingTask->version ++;
            $trainingTask->updated_time = $date;
            $trainingTask->save();

            // 培训任务-全部人员
            $peoples = TrainingPeople::find([
                'conditions' => ' task_no = :task_no: and status = 1',
                'bind' => [
                    'task_no' => $taskNo
                ],
                'for_update' => true
            ]);
            $staff_ids = [];
            foreach ($peoples as $people) {
                $people->operator_id = $userinfo['id'];
                $people->status = self::PEOPLE_STATUS_9;
                $people->save();
                $staff_ids[] = $people->staff_id;

                $poolModel = TrainingPool::findFirst([
                    'conditions' => ' training_type = :type: and staff_id = :staff_id: ',
                    'bind' => [
                        'type' => $trainingTask->training_type,
                        'staff_id' => $people->staff_id
                    ],
                    'for_update' => true
                ]);
                if ($poolModel) {
                    // 兼容测试数据， 有些不存在；
                    $poolModel->training_status = self::POOL_STATUS_1;
                    $poolModel->updated_time = gmdate('Y-m-d H:i:s');
                    $poolModel->save();
                }

            }

            //发送消息通知
            if (!empty($staff_ids)) {
                $t = self::getTranslation(strtolower(env('country_code', 'th')));
                $this->sendPush([
                    'staff_info_ids' => implode(',', $staff_ids),
                    'title' => str_ireplace('{name}', $t->_('training_type_' . $trainingTask->training_type),
                        $t->_('train.msg8')),
                    'content' => $trainingTask->version,
                    'related_id' => $trainingTask->id,
                    'operate' => self::PUSH_DEL,
                ]);
            }

            $this->addTaskOperateLog($trainingTask->task_no, $userinfo['id'], self::OPERATE_LOG_ACTION_DEL, [
                'staff_ids' => '{}',
                'version' => $trainingTask->version,
                'training_type' => $trainingTask->training_type,
                'training_place_id' => $trainingTask->training_place_id,
                'training_organization' => $trainingTask->training_organization,
                'training_place' => $trainingTask->training_place,
                'lecturer' => $trainingTask->lecturer,
                'start_time' => $trainingTask->start_time,
                'end_time' => $trainingTask->end_time,
                'training_mode' => $trainingTask->training_mode,
                'online_link' => $trainingTask->online_link,
                'online_id' => $trainingTask->online_id,
                'online_password' => $trainingTask->online_password,
            ]);
            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return true;
    }


    /**
     * 编辑任务
     *
     * @param $params
     * @param $userInfo
     *
     * @return bool
     * @throws \Exception
     */
    public function updateTask($params, $userInfo)
    {
        date_default_timezone_set("Asia/Bangkok");//设置时区为泰国时间
        $this->getDI()->get('logger')->info('training updateTask params:'.json_encode($params,JSON_UNESCAPED_UNICODE));

        $db     = $this->getDI()->get('db_backyard');
        $date   = gmdate('Y-m-d H:i:s');

        try {
            $db->begin();
            $trainingTask = TrainingTask::findFirst([
                'conditions' => ' task_no = :task_no: ',
                'bind' => [
                    'task_no' => $params['task_no']
                ],
                'for_update' => true
            ]);
            if (!$trainingTask) {

                throw new \Exception('no task data');
            }

            //二期新加，开始前7天之后的时间不可以删除
            if (strtotime($trainingTask->start_time . ' -2 days') < time()) {
                throw new ValidationException('could not edit !');
            }

            //编辑任务的内容为【开始时间】【预计培训时长】【结束时间】【培训地点】中的任一项时，通知消息到员工的BY和KIT
            $this->getDI()->get('logger')->info('training updateTask current_training_place_id:'.$trainingTask->training_place_id.' after_training_place_id:'.$params['training_place_id']. '  current_training_place:'.$trainingTask->training_place .'after_training_place_id:'.$params['training_place']);

            $pushUpdate = false;
            if (($params['training_place_id'] != '0' && $trainingTask->training_place_id != $params['training_place_id']) ||
                ($params['training_place_id'] == '0' && $trainingTask->training_place != $params['training_place']) ||
                strtotime($trainingTask->start_time) != strtotime($params['start_time']) ||
                strtotime($trainingTask->end_time) != strtotime($params['end_time']) ||
                $trainingTask->time_long_hour != $params['time_long_hour'] ||
                $trainingTask->time_long_minute != $params['time_long_minute'] ||
                $trainingTask->training_mode != $params['training_mode'] ||
                $trainingTask->online_link != $params['online_link'] ||
                $trainingTask->online_id != $params['online_id'] ||
                $trainingTask->online_password != $params['online_password']
            ) {
                $this->getDI()->get('logger')->info('training updateTask push update!!!');
                $pushUpdate = true;
            }

            if (!empty($params['training_place_id']) && $trainingTask->training_place_id != $params['training_place_id']) {
                if ($params['training_place_id'] == -1) {
                    $trainingTask->training_place = 'head office';
                } else {
                    $storeInfo = SysStoreModel::findFirst([
                        'conditions' => 'id = :store_id:',
                        'bind' => ['store_id' => $params['training_place_id']]
                    ]);

                    $trainingTask->training_place = $storeInfo->name;
                }
            } elseif (empty($params['training_place_id'])) {
                $trainingTask->training_place = $params['training_place'];
            }

            $peoples = TrainingPeople::find([
                'conditions' => ' task_no = :task_no: and status = 1',
                'bind' => [
                    'task_no' => $params['task_no']
                ],
                'for_update' => true
            ])->toArray();

            // 培训任务
            $trainingTask->handler_id = $userInfo['id'];
            $trainingTask->handler_time = $date;
            $trainingTask->training_organization = $params['training_organization'];
            $trainingTask->lecturer = $params['lecturer'];
            $trainingTask->training_place_id = $params['training_place_id'];
            $trainingTask->start_time = $params['start_time'];
            $trainingTask->end_time = $params['end_time'];
            $trainingTask->time_long_hour = $params['time_long_hour'];
            $trainingTask->time_long_minute = $params['time_long_minute'];
            $trainingTask->training_mode = $params['training_mode'];
            $trainingTask->online_link = $params['online_link'] ?? '';
            $trainingTask->online_id = $params['online_id'] ?? '';
            $trainingTask->online_password = $params['online_password'] ?? '';
            $trainingTask->save();

            $currentStaffIds = array_column($peoples, 'staff_id');
            // 删除之前存在本次不存在的工号
            $delStaffIds = array_diff($currentStaffIds, $params['staff_ids']);
            //新添加的人员id
            $addIds = array_diff($params['staff_ids'], $currentStaffIds);
            //删除人员中未拒绝参加的人员
            $delStaff = [];
            foreach ($delStaffIds as $delStaffId) {
                // 删除 培训人员
                $delPeople = TrainingPeople::findFirst([
                    'conditions' => ' task_no = :task_no: and staff_id = :staff_id: ',
                    'bind' => [
                        'task_no' => $params['task_no'],
                        'staff_id' => $delStaffId
                    ],
                    'for_update' => true
                ]);
                if ($delPeople->is_join != self::TASK_JOIN_3) {
                    $delStaff[] = $delStaffId;
                }
                $delPeople->operator_id = $userInfo['id'];
                $delPeople->status = self::PEOPLE_STATUS_9;
                $delPeople->save();

                // 培训池 编辑状态
                $traningPool = TrainingPool::findFirst([
                    'conditions' => ' training_type = :type: and staff_id = :staff_id: ',
                    'bind' => [
                        'type' => $delPeople->training_type,
                        'staff_id' => $delStaffId
                    ],
                    'for_update' => true
                ]);
                $traningPool->training_status = self::POOL_STATUS_1;
                $traningPool->updated_time = $date;
                $traningPool->save();
            }

            foreach ($params['staff_ids'] as $staff_id) {
                // 新增或者更新
                // 培训任务人员管理表更新
                $model = $this->getTrainingPeople($params['task_no'], $staff_id);
                if (!$model) {
                    $model = new TrainingPeople();
                    $model->task_no = $trainingTask->task_no;
                }
//                $model->task_no = $params['task_no'];
                $model->operator_id = $userInfo['id'];
                $model->training_type = $trainingTask->training_type;
                $model->staff_id = $staff_id;
                $model->status = self::PEOPLE_STATUS_1;
                $model->is_join = self::TASK_JOIN_1;
                $model->save();

                // 培训人员池状态更新
                $poolModel = $this->getTrainingPool($staff_id, $trainingTask->training_type);
                $poolModel->training_status = self::POOL_STATUS_2;
                $poolModel->updated_time = $date;
                $poolModel->save();
            }

            //编辑任务的内容为【开始时间】【预计培训时长】【结束时间】【培训地点】中的任一项时，通知消息到员工的BY和KIT
            $t      = self::getTranslation(strtolower(env('country_code', 'th')));
            if ($pushUpdate) {
                //旧的未删除员工，发送变更通知
                $trainingTask->version ++;
                $trainingTask->save();
                $ids = array_intersect($currentStaffIds, $params['staff_ids']);
                $this->getDI()->get('logger')->info('training updateTask push update staffs:'.json_encode($ids,JSON_UNESCAPED_UNICODE));
                $this->sendPush([
                    'staff_info_ids'    => implode(',', $ids),
                    'title'             => str_ireplace('{name}', $t->_('training_type_' . $trainingTask->training_type), $t->_('train.msg5')),
                    'content'           => $trainingTask->version,
                    'related_id'        => $trainingTask->id,
                    'operate'           => self::PUSH_EDIT,
                ]);
            }

            //给被删除的非拒绝人员发送任务取消通知
            if ( ! empty($delStaff)) {
                $this->sendPush([
                    'staff_info_ids'    => implode(',', $delStaff),
                    'title'             => str_ireplace('{name}', $t->_('training_type_' . $trainingTask->training_type), $t->_('train.msg6')),
                    'content'           => $trainingTask->version,
                    'related_id'        => $trainingTask->id,
                    'operate'           => self::PUSH_DEL_PEOPLE,
                ]);
            }

            //新加的员工，发送任务通知
            if (!empty($addIds)) {
                $this->sendPush([
                    'staff_info_ids' => implode(',', $addIds),
                    'title' => str_ireplace('{name}', $t->_('training_type_' . $trainingTask->training_type), $t->_('train.msg4')),
                    'content' => $trainingTask->version,
                    'related_id' => $trainingTask->id,
                    'operate' => self::PUSH_ADD,
                ]);
            }

            $this->addTaskOperateLog($trainingTask->task_no, $userInfo['id'], self::OPERATE_LOG_ACTION_EDIT, [
                'training_type' => $trainingTask->training_type,
                'training_organization' => $params['training_organization'],
                'lecturer' => $params['lecturer'],
                'training_place_id' => $params['training_place_id'],
                'training_place' => $trainingTask->training_place,
                'start_time' => $params['start_time'],
                'end_time' => $params['end_time'],
                'training_mode' => $params['training_mode'],
                'online_link' => $params['online_link'],
                'online_id' => $params['online_id'],
                'online_password' => $params['online_password'],
                'version' => $trainingTask->version,
                'staff_ids' => $params['staff_ids'] ? json_encode($params['staff_ids'], JSON_UNESCAPED_UNICODE) : '{}',
            ]);

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            throw $exception;
        }

        return true;
    }

    /**
     * 创建任务-批量导入验证
     *
     * @param $params
     * @param $userinfo
     */
    public function batchImportStaffCheck($excel_data, $training_type, $userinfo)
    {
        $excel_data_staff_ids = array_values(array_filter(array_column($excel_data, 0)));
        if (count($excel_data_staff_ids) != count(array_unique($excel_data_staff_ids))) {
            throw new ValidationException(static::$t->_('batchimportstaff_1'), ErrCode::$VALIDATE_ERROR);
        }
        $staffs                = HrStaffInfoModel::find([
            'columns'    => ' staff_info_id, name ',
            'conditions' => ' staff_info_id in ({staff_id:array}) and state = :state:',
            'bind'       => [
                'staff_id' => $excel_data_staff_ids,
                'state' => Enums\StaffInfoEnums::STAFF_STATE_IN,
            ],
        ])->toArray();
        $staffs                = array_column($staffs, 'name', 'staff_info_id');

        $training_pool = TrainingPool::find([
            'conditions' => ' staff_id in ({staff_id:array}) and  training_type = :type: ',
            'bind'       => [
                'type' => $training_type,
                'staff_id' => $excel_data_staff_ids,
            ],
        ])->toArray();
        $training_pool = array_column($training_pool, 'staff_id', 'staff_id');

        $_error_num            = 0;
        $success_staff_ids_arr = [];
        $i                     = 0;
        foreach ($excel_data as $k => $v) {
            $staff_id = trim($v[0]);
            if (empty($staff_id)){
                continue;
            }
            if (!isset($staffs[$staff_id]) && !$staffs[$staff_id]) {
                $_error_num++;
                $excel_data[$k][2] = static::$t->_('batchimportstaff_3');
                continue;
            }

            // 培训人员池状态更新
            if (!isset($training_pool[$staff_id]) && !$training_pool[$staff_id]) {
                $_error_num++;
                $excel_data[$k][2] = static::$t->_('batchimportstaff_4');
                continue;
            }
            $success_staff_ids_arr[$i]['id']   = (string)$staff_id;
            $success_staff_ids_arr[$i]['name'] = $staffs[$staff_id];
            $i++;
        }
        $success_num             = $success_staff_ids_arr ? count($success_staff_ids_arr) : 0;
        $vendor["error_num"]     = $_error_num;
        $vendor["count"]         = $_error_num + $success_num;
        $vendor["success_num"]   = $success_num;
        $vendor["success_staff"] = $success_staff_ids_arr;

        if ($_error_num > 0) {
            // 生成上传结果文件
            $file_name           = date('YmdHis').'_'.$userinfo['id'].'_import_update_result.xlsx';
            $excel_header_column = [
                static::$t->_('staff_info_id'),
                static::$t->_('staff_name'),
                static::$t->_('remark'),
            ];
            $res                 = $this->exportExcel($excel_header_column, $excel_data, $file_name);
            if ($res['code'] == ErrCode::$SUCCESS) {
                $vendor["upload_result_file"]['file_url'] = $res['data'];
                $code                                     = ErrCode::$SUCCESS;
                $message                                  = static::$t->_('excel_result_success');
            } else {
                $code    = ErrCode::$SYSTEM_ERROR;
                $message = static::$t->_('retry_later');
            }
        }else{
            $code                                     = ErrCode::$SUCCESS;
            $message                                  = static::$t->_('excel_result_success');
        }
        return [
            'code'    => $code ?? ErrCode::$SYSTEM_ERROR,
            'message' => $message ?? static::$t->_('retry_later'),
            'data'    => $vendor ?? [],
        ];
    }


    /**
     * @param $taskNo
     * @param $staffId
     * @return \Phalcon\Mvc\Model
     *
     */
    private function getTrainingPeople($taskNo, $staffId)
    {
        return TrainingPeople::findFirst([
            'conditions' => ' task_no = :task_no: and staff_id = :staff_id: ',
            'bind' => [
                'task_no' => $taskNo,
                'staff_id' => $staffId
            ]
        ]);
    }

    private function getTrainingPool($staffId, $trainingType)
    {
        return TrainingPool::findFirst([
            'conditions' => ' staff_id = :staff_id: and training_type = :type: ',
            'bind' => [
                'staff_id' => $staffId,
                'type' => $trainingType
            ]
        ]);
    }

    /**
     *
     * 创建培训任务编码
     * @param null $startDay
     * @param null $endDay
     * @return string
     *
     */
    private function genTMNo($startDay = null, $endDay = null)
    {
        $startDay = $startDay ?? gmdate('Y-m-d');
        $endDay = $endDay ?? gmdate('Y-m-d', strtotime("+1 days"));
        $count = TrainingTask::count([
            'conditions' => ' created_time >= :start_day: and created_time <= :end_day: ',
            'bind' => [
                'start_day' => $startDay,
                'end_day' => $endDay,
            ]
        ]);
        if ($startDay && $endDay) {

            $count++;
            return  'TM'. date('Ymd', strtotime($startDay)) . str_pad($count, 4, 0,STR_PAD_LEFT);
        }
        return  'TM'. date('Ymd') . str_pad($count+1, 4, 0,STR_PAD_LEFT);

    }


    /**
     *
     * 培训任务列表
     * @param $params
     */
    public function taskList($params)
    {

        $conditions = [];
        $bind = [];
        if(!empty($params['training_type']) && is_numeric($params['training_type'])) {
            $conditions[] = ' training_type = :training_type: ';
            $bind['training_type'] = $params['training_type'];
        } elseif (!empty($params['training_type']) && is_array($params['training_type'])) {
            $training_type = array_values($params['training_type']);
            $conditions[] = ' training_type IN({training_type:array})';
            $bind['training_type'] = $training_type;
        }

        if (isset($params['status']) && $params['status']) {
            if ($params['status'] == 1) {
                $conditions[] = ' status in ({status:array})';
                $bind['status'] = [self::TASK_STATUS_1, self::TASK_STATUS_2];
            } else if ($params['status'] == 2) {
                $conditions[] = ' status in ({status:array})';
                $bind['status'] = [self::TASK_STATUS_3, self::TASK_STATUS_4];
            }
        }
        if (isset($params['start_time']) && $params['start_time'] && isset($params['end_time']) && $params['end_time']) {
            $conditions[] = ' start_time >= :start_time: and start_time <= :end_time: ';
            $bind['start_time'] = $params['start_time'];
            $bind['end_time'] = $params['end_time'];
        }

        if (isset($params['training_organization']) && $params['training_organization']) {
            $conditions[] = ' training_organization = :training_organization:';
            $bind['training_organization'] = $params['training_organization'];
        }
        if (isset($params['operator_id']) && $params['operator_id']) {
            $conditions[] = ' create_id = :operator_id:';
            $bind['operator_id'] = $params['operator_id'];
        }

        if (isset($params['training_status']) && $params['training_status']) {
            $conditions[] = ' status = :training_status:';
            $bind['training_status'] = $params['training_status'];
        }

        $count = TrainingTask::count([
            'conditions' => implode(' and ', $conditions),
            'bind' => $bind
        ]);

        $list = TrainingTask::find([
            'columns' => 'task_no, create_id, handler_id, audited_id, training_type, training_organization, lecturer, 
            training_place, start_time, status, created_time, handler_time, audited_time, training_mode, online_link, online_id, online_password',
            'conditions' => implode(' and ', $conditions),
            'bind' => $bind,
            'limit' => $params['page_size'],
            'offset' => $params['page_size'] * ($params['page'] - 1)
        ])->toArray();

        $staffIds = array_values(array_unique(array_column($list, 'create_id')));
        $staffIds = array_values(array_unique(array_merge($staffIds, array_column($list, 'handler_id'))));
        $staffIds = array_values(array_unique(array_merge($staffIds, array_column($list, 'audited_id'))));
        $staffs = [];
        if ($staffIds) {
            $staffs = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, name',
                'conditions' => ' staff_info_id in ({staff_ids:array})',
                'bind' => [
                    'staff_ids' => $staffIds
                ]
            ])->toArray();

            $staffs = array_column($staffs, null, 'staff_info_id');
        }

        $edit_permissions = 0;
        if (isset($params['node_department_id']) && $params['node_department_id']) {
            $department      = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind'       => ['id' => $params['node_department_id']],
            ]);
            $ancestry_v3     = $department ? $department->ancestry_v3 : '';
            $ancestry_v3_arr = explode('/', $ancestry_v3);
            // 159 = 泰国 EHS Training部门
            if (in_array(159, $ancestry_v3_arr) && get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
                $edit_permissions = 1;
            }
        }

        $results = [];

        foreach ($list as $item) {
            $results[] = [
                'task_no' => $item['task_no'],
                'edit_permissions' => $edit_permissions,
                'training_type' => $item['training_type'],
                'training_type_text' => static::$t->_('training_type_' .  $item['training_type']),
                'start_time' => $item['start_time'],
                'training_organization' => $item['training_organization'],
                'training_organization_text' => static::$t->_('training_organization_' . $item['training_organization']) ,
                'lecturer' => $item['lecturer'],
                'training_place' => $item['training_place'],
                'status' => $item['status'],
                'status_text' => static::$t->_('task_status_' . $item['status']),
                'create_id' => $item['create_id'],
                'create_name' => isset($staffs[$item['create_id']]) ? $staffs[$item['create_id']]['name'] : '',
                'handler_id' => $item['handler_id'],
                'handler_name' => isset($staffs[$item['handler_id']]) ? $staffs[$item['handler_id']]['name'] : '',
                'handler_time' => date('Y-m-d H:i:s', strtotime($item['handler_time'] . " + 7 hours ")) ,
                'audited_id' => $item['audited_id'],
                'audited_name' => isset($staffs[$item['audited_id']]) ? $staffs[$item['audited_id']]['name'] : '',
                'training_mode' => $item['training_mode'],
                'training_mode_text' => static::$t->_('training_mode_' . $item['training_mode']),
                'online_link' => $item['online_link'],
                'online_id' => $item['online_id'],
                'online_password' => $item['online_password'],
                'audited_time' => date('Y-m-d H:i:s', strtotime($item['audited_time'] . " + 7 hours ")) ,
                'created_time' => date('Y-m-d H:i:s', strtotime($item['created_time'] . " + 7 hours ")),
            ];
        }

        return ['list' => $results, 'count' => $count];
    }


    public function exportList($params)
    {
        $conditions = [];
        $bind = [];
        if(isset($params['training_type']) && $params['training_type']) {
            $conditions[] = ' training_type = :training_type: ';
            $bind['training_type'] = $params['training_type'];
        }

        if (isset($params['status']) && $params['status']) {
            if ($params['status'] == 1) {
                $conditions[] = ' status in ({status:array})';
                $bind['status'] = [self::TASK_STATUS_1, self::TASK_STATUS_2];
            } else if ($params['status'] == 2) {
                $conditions[] = ' status in ({status:array})';
                $bind['status'] = [self::TASK_STATUS_3, self::TASK_STATUS_4];
            }
        }
        if (isset($params['start_time']) && $params['start_time'] && isset($params['end_time']) && $params['end_time']) {
            $conditions[] = ' start_time >= :start_time: and start_time <= :end_time: ';
            $bind['start_time'] = $params['start_time'];
            $bind['end_time'] = $params['end_time'];
        }

        if (isset($params['training_organization']) && $params['training_organization']) {
            $conditions[] = ' training_organization = :training_organization:';
            $bind['training_organization'] = $params['training_organization'];
        }
        if (isset($params['operator_id']) && $params['operator_id']) {
            $conditions[] = '  create_id = :operator_id:';
            $bind['operator_id'] = $params['operator_id'];
        }

        if (isset($params['training_status']) && $params['training_status']) {
            $conditions[] = ' status = :training_status:';
            $bind['training_status'] = $params['training_status'];
        }

        $list = TrainingTask::find([
            'columns' => 'task_no,  create_id, handler_id, audited_id, training_type, training_organization, lecturer, training_place, start_time, status, created_time, training_mode, online_id, online_link, online_password',
            'conditions' => implode(' and ', $conditions),
            'bind' => $bind
        ])->toArray();

        $staffIds = array_values(array_unique(array_column($list, 'create_id')));
        $staffIds = array_values(array_unique(array_merge($staffIds, array_column($list, 'handler_id'))));
        $staffIds = array_values(array_unique(array_merge($staffIds, array_column($list, 'audited_id'))));
        $staffs = [];
        if ($staffIds) {
            $staffs = HrStaffInfoModel::find([
                'columns' => 'staff_info_id, name',
                'conditions' => ' staff_info_id in ({staff_ids:array})',
                'bind' => [
                    'staff_ids' => $staffIds
                ]
            ])->toArray();

            $staffs = array_column($staffs, null, 'staff_info_id');
        }

        $file ='training_staffs_' . date('YmdHis') . '.xlsx';

        $header = [
            static::$t->_('task_number'), // 任务编号
            static::$t->_('training_type'), // 培训类型
            static::$t->_('training_start_time'), // 培训开始时间
            static::$t->_('training_institution'), // 培训机构
            static::$t->_('training_trainer'), // 培训讲师
            static::$t->_('training_mode'), // 培训方式
            static::$t->_('training_location_link'), // 培训地点
            static::$t->_('training_task_status'), // 培训状态
            static::$t->_('creator'), // 创建人
            static::$t->_('create_time'), // 创建时间
            static::$t->_('handler'),
            static::$t->_('processing_time'),
            static::$t->_('re_field_finished_at'),
        ];

        $rows = [];
        foreach ($list as $item) {

            if ($item['training_mode'] == self::TRAINING_MODE_2) {
                $trainingLocaltionLink = sprintf(static::$t->_("training_location_link_tmp"), $item['online_id'], $item['online_password'], $item['online_link']) ;
            } else {
                $trainingLocaltionLink = $item['training_place'];
            }

           $rows[] = [
                $item['task_no'],
                static::$t->_('training_type_' .  $item['training_type']),
                date('Y-m-d H:i:s', strtotime($item['start_time'] . " + 7 hours ")),
                self::TRAINING_ORGANIZATION_DESC[$item['training_organization']],
                $item['lecturer'],
                static::$t->_("training_mode_" . $item['training_mode']),
                $trainingLocaltionLink,
                static::$t->_('task_status_' . $item['status']),
                (isset($staffs[$item['create_id']]) ? $staffs[$item['create_id']]['name'] : ($item['create_id'] == 10000 ? 'SuperAdmin' : '')) . "(" . $item['create_id'] . ")",
                date('Y-m-d H:i:s', strtotime($item['created_time'] . " + 7 hours ")),
                (isset($staffs[$item['handler_id']]) ? $staffs[$item['handler_id']]['name'] : ($item['handler_id'] == 10000 ? 'SuperAdmin' : '')) . "(" . $item['handler_id'] . ")",
                date('Y-m-d H:i:s', strtotime($item['handler_time'] . " + 7 hours ")),
                $item['status'] == self::TASK_STATUS_3 ?  date('Y-m-d H:i:s', strtotime($item['audited_time'] . " + 7 hours ")) : "/"
           ];
        }

        $result =
        $this->exportExcel($header, $rows, $file);

        return $result['data'];
    }



    /**
     * 任务详情
     *
     * @param $taskNo
     */
    public function taskInfo($taskNo)
    {
        $taskInfo = TrainingTask::findFirst([
            'conditions' => ' task_no = :task_no: ',
            'bind' => [
                'task_no' => $taskNo
            ]
        ]);
        $taskInfo = $taskInfo ? $taskInfo->toArray() : [];

        $result = [];
        if ($taskInfo) {
            $peoples = TrainingPeople::find([
                'conditions' => ' task_no = :task_no: and status = 1',
                'bind' => [
                    'task_no' => $taskNo
                ]
            ])->toArray();

            $result['id'] = $taskInfo['id'];
            $result['task_no'] = $taskInfo['task_no'];
            $result['training_type'] = $taskInfo['training_type'];
            $result['training_type_text'] = static::$t->_('training_type_' . $taskInfo['training_type']);
            $result['training_organization'] = $taskInfo['training_organization'];
            $result['training_organization_text'] = self::TRAINING_ORGANIZATION_DESC[$taskInfo['training_organization']];
            $result['start_time'] = $taskInfo['start_time'];
            $result['end_time'] = $taskInfo['end_time'];
            $result['hours'] = $taskInfo['time_long_hour'];
            $result['minute'] = $taskInfo['time_long_minute'];
            $result['lecturer'] = $taskInfo['lecturer'];
            $result['training_place_id'] = (string) $taskInfo['training_place_id'];
            $result['training_place'] = $taskInfo['training_place'];

            $result['training_mode'] = $taskInfo['training_mode'];
            $result['online_link'] = $taskInfo['online_link'];
            $result['online_id'] = $taskInfo['online_id'];
            $result['online_password'] = $taskInfo['online_password'];

            // 所有的员工IDs
            $staffIds = array_values(array_filter(array_column($peoples, 'staff_id')));

            $operateLog = TrainingOperateLog::findFirst([
                'conditions' => ' task_no = :task_no: and action_type = :action_type: ',
                'bind' => [
                    'task_no' => $taskNo,
                    'action_type' => self::OPERATE_LOG_ACTION_AUDIT,
                ]
            ]);
            $operateLog = $operateLog ? $operateLog->toArray() : [];
            $failStaffIds = [];
            if ($operateLog) {
                $failStaffIds = json_decode($operateLog['staff_ids'], true);
            }

            $result['pass_peoples'] = [];
            $result['fail_peoples'] = [];
            $join = array_column($peoples, null, 'staff_id');
            $result['peoples'] = [];
            $result['join_2_num'] = 0;//确定参加人数
            $result['join_3_num'] = 0;//确定拒绝人数
            $result['join_1_num'] = 0;//待定人数
            if ($staffIds) {
                $staffs = HrStaffInfoModel::find([
                    'columns' => 'staff_info_id, name',
                    'conditions' => ' staff_info_id in ({staff_ids:array}) ',
                    'bind' => [
                        'staff_ids' => $staffIds
                    ]
                ])->toArray();

                $staffs = array_column($staffs, null, 'staff_info_id');
                $fiveDayBefore = strtotime($taskInfo['start_time'] . ' -1 days');
                $join_order = [1 => 90, 2 => 99, 3 => 50];//排序需要
                $text = [1 => 'train.msg15', 2 => 'train.confirm', 3 => 'train.refuse'];//翻译
                foreach ($staffs as $staffId => $staff) {
                    $join_status = $join[$staffId]['is_join'];
                    $staff['is_join'] = $join_status;
                    $staff['reason_rejection'] = $join[$staffId]['reason_rejection'] ?? '';
                    $_rejection_type = $join[$staffId]['rejection_type'] ?? '';
                    $staff['rejection_type'] = $_rejection_type;
                    $staff['rejection_type_name'] = $_rejection_type ? static::$t->_('rejection_type_'.$_rejection_type) : '';

                    //开始时间5天前以后未回复，默认拒绝
                    if ($join_status == 1 && time() >= $fiveDayBefore) {
                        $staff['is_join'] = 3;
                    }
                    if (in_array($staffId, $failStaffIds)) {

                        $result['fail_peoples'][] = $staff;
                    } else {

                        $result['pass_peoples'][] = $staff;
                    }
                    $result['join_' . $staff['is_join'] . '_num'] ++;
                    $staff['join_text'] = static::$t->_($text[$staff['is_join']]);
                    $staff['order'] = $join_order[$staff['is_join']];
                    $result['peoples'][] = $staff;
                }
                //排序：确定参加 》 待定 》 拒绝参加
                $array1 = array_column($result['peoples'], 'order');
                array_multisort($array1, SORT_DESC, SORT_NUMERIC, $result['peoples']);
            }
        }


        return $result;
    }


    /**
     * 审核任务
     *
     * @param $taskNo
     * @param $staffIds
     * @param $userInfo
     * @param $files
     *
     */
    public function reviewTask($taskNo, $staffIds, $userInfo, $files)
    {
        $db = $this->getDI()->get('db_backyard');

        try {
            $db->begin();
            $trainingTask = TrainingTask::findFirst([
                'conditions' => ' task_no = :task_no: ',
                'bind' => [
                    'task_no' => $taskNo
                ],
                'for_update' => true
            ]);
            if (!$trainingTask) {

                throw new \Exception('no task data');
            }

            // 培训任务状态 -》 已完成
            $trainingTask->status = self::TASK_STATUS_3;
            $trainingTask->audited_id = $userInfo['id'];
            $trainingTask->audited_time = gmdate('Y-m-d H:i:s');
            $trainingTask->updated_time = gmdate('Y-m-d H:i:s');
            $trainingTask->training_files = $files;
            $trainingTask->save();

            $peoples = TrainingPeople::find([
                'conditions' => ' task_no = :task_no: and status = 1',
                'bind' => [
                    'task_no' => $taskNo
                ],
                'for_update' => true
            ])->toArray();
            $allStaffIds = array_column($peoples, 'staff_id');
            $peoples = array_column($peoples, null, 'staff_id');

            $trainingPools = TrainingPool::find([
                'conditions' => ' training_type = :type: and staff_id in ({staff_ids:array})  ',
                'bind' => [
                    'type' => $trainingTask->training_type,
                    'staff_ids' => $allStaffIds
                ],
                'for_update' => true
            ]);
            // 培训人员池 状态更新
            foreach ($trainingPools as $trainingPool) {
                if (in_array($trainingPool->staff_id, $staffIds)) {
                    // 不通过
                    $trainingPool->training_status = self::POOL_STATUS_4;
                } else if ($peoples[$trainingPool->staff_id]['is_join'] == self::TASK_JOIN_2) {
                    // 通过
                    $trainingPool->training_status = self::POOL_STATUS_3;
                    $trainingPool->training_files = $files;
                }
                $trainingPool->updated_time = gmdate('Y-m-d H:i:s');
                $trainingPool->save();
            }

            $this->addTaskOperateLog($trainingTask->task_no, $userInfo['id'], self::OPERATE_LOG_ACTION_AUDIT, [
                'training_type' => $trainingTask->training_type,
                'training_organization' => $trainingTask->training_organization,
                'lecturer' => $trainingTask->lecturer,
                'training_place_id' => $trainingTask->training_place_id,
                'training_place' => $trainingTask->training_place,
                'start_time' => $trainingTask->start_time,
                'end_time' => $trainingTask->end_time,
                'staff_ids' => $staffIds ? json_encode($staffIds, JSON_UNESCAPED_UNICODE) : '{}',
            ]);

            $db->commit();
        } catch (\Exception $exception) {

            $db->rollBack();
            throw $exception;
        }

        return true;
    }


    /**
     *
     *
     * @param $taskNo
     */
    public function operateLog($taskNo)
    {
        $operateLogs = TrainingOperateLog::find([
            'conditions' => ' task_no = :task_no: ',
            'bind' => ['task_no' => $taskNo],
            'order' => ' created_time desc '
        ])->toArray();

        $operatorIds = array_column($operateLogs, 'operator_id');
        if (empty($operatorIds)) {
            return [];
        }
        $operators = HrStaffInfoModel::find([
            'conditions' => ' staff_info_id in ({staff_ids:array}) ',
            'bind' => ['staff_ids' => $operatorIds]
        ])->toArray();
        $operators = array_column($operators, null, 'staff_info_id');

        $logs = [];
        foreach ($operateLogs as $operateLog) {
            $logs[] = [
                'operator_id' => ( isset($operators[$operateLog['operator_id']]) ?  $operators[$operateLog['operator_id']]['name'] : ($operateLog['operator_id'] == 10000 ? 'SuperAdmin' : '')) . ' ' .   $operateLog['operator_id'],
                'operator_name' => static::$t->_('operate_log_action_' . $operateLog['action_type']),
                'operator_time' => date('Y-m-d H:i:s', strtotime($operateLog['created_time'] . " + 7 hours"))
            ];
        }

        return $logs;
    }


    /**
     *
     * 添加操作日志记录
     *
     * @param $taskNo
     * @param $operatorId
     * @param $actionType
     * @param array $fields
     */
    public function addTaskOperateLog($taskNo, $operatorId, $actionType, $fields = [])
    {
        $operateLog = new TrainingOperateLog();
        $operateLog->task_no = $taskNo;
        $operateLog->action_type = $actionType;
        $operateLog->operator_id = $operatorId;
        foreach ($fields as $field => $value) {
            $operateLog->$field = $value;
        }

        $operateLog->save();
    }


    /**
     * svc获取任务信息
     * @param array $params
     * @throws \Exception
     * @return array
     */
    public function svcGetTaskInfo(array $params): array {

        $task = TrainingTask::findFirst([
            'id = :id:',
            'bind' => [
                'id' => $params['id']
            ],
            'columns' => 'id,task_no,training_type,training_place,start_time,end_time,status,time_long_hour,time_long_minute'
        ]);

        if (empty($task)) {
            throw new \Exception(static::$t->_('no_task'));
        }

        $task = $task->toArray();
        $taskInfo = TrainingOperateLog::findFirst([
            'task_no = :task_no: AND version = :version:',
            'bind' => [
                'task_no'   => $task['task_no'],
                'version'   => $params['version'],
            ],
            'columns' => 'start_time,training_place,task_no,training_type,start_time,end_time,training_mode,online_link,online_id,online_password',
        ]);

        if (empty($task)) {
            throw new \Exception(static::$t->_('no_task'));
        }

        $taskInfo                   = $taskInfo->toArray();
        $taskInfo['id']             = $task['id'];
        $taskInfo['status']         = $task['status'];
        $taskInfo['training_mode_text']  = static::$t->_('training_mode_' . $taskInfo['training_mode']);
        $taskInfo['time_long_hour'] = $task['time_long_hour'];
        $taskInfo['time_long_minute'] = $task['time_long_minute'];
        $taskInfo['training_name']  = static::$t->_('training_type_' . $taskInfo['training_type']);
        $expired                    = strtotime($taskInfo['start_time'] . ' -1 days');
        $taskInfo['expired_date']   = date('Y-m-d', $expired);
        $taskInfo['expired_at']     = date('H:i:s', $expired);
        [$taskInfo['start_date'], $taskInfo['start_at']]    = explode(' ', $taskInfo['start_time']);
        [$taskInfo['end_date'], $taskInfo['end_at']]        = explode(' ', $taskInfo['end_time']);
        $taskInfo['training_status'] = 1;

        //是否参与
        $is_join = TrainingPeople::findFirst([
            'task_no = :task_no: AND staff_id = :staff_id:',
            'bind' => [
                'task_no'   => $taskInfo['task_no'],
                'staff_id'  => $params['staff_id'],
            ],
            'columns' => 'is_join',
        ]);

        if ($params['operate'] != self::PUSH_ADD) {
            $taskInfo['training_status'] = $params['operate'];
            $taskInfo['origin_start_date'] = '';
            $taskInfo['origin_start_at'] = '';
            $taskInfo['origin_training_place'] = '';
            $log = TrainingOperateLog::findFirst([
                'task_no = :task_no: AND version < :version:',
                'bind' => [
                    'task_no'   => $taskInfo['task_no'],
                    'version'   => $params['version'],
                ],
                'order' => 'created_time desc',
                'columns' => 'start_time, training_place, training_mode',
            ]);
            if (!empty($log)) {
                [$taskInfo['origin_start_date'], $taskInfo['origin_start_at']] = explode(' ', $log->start_time);
                $taskInfo['origin_training_place'] = $log->training_place;
                $taskInfo['origin_training_mode'] = $log->training_mode;
                $taskInfo['origin_training_mode_text'] = static::$t->_('training_mode_' . $log->training_mode) ;
            }
        }

        if ($params['operate'] == self::PUSH_DEL) {
            $taskInfo['start_date'] = $taskInfo['origin_start_date'];
            $taskInfo['start_at'] = $taskInfo['origin_start_at'];
            $taskInfo['training_place'] = $taskInfo['origin_training_place'];
        }

        $staff = (new UserService())->getUserById($params['staff_id']);

        unset($taskInfo['status'], $taskInfo['start_time'], $taskInfo['end_time'], $taskInfo['task_no']);

        return [
            'status'        => empty($is_join) ? 1 : $is_join->is_join,//是否参与
            'staff_name'    => empty($staff) ? '' : $staff->name,//人员名称
            'training_info' => $taskInfo,//任务详情
        ];
    }


    /**
     * 发送任务消息
     * @param array $data
     * @throws \Exception
     * @return void
     */
    public function sendPush(array $data): void {

        $data['category'] = 37;

        $api = new ApiClient('bi', '', 'trainingmessage.message_to_oa_training', static::$language);
        $api->setParams([$data]);
        $ret = $api->execute();
        if (!isset($ret['result']) || $ret['result']['code'] != 1) {
            throw new \Exception('发送培训任务消息失败，请求参数：' . json_encode($data) . '，接口返回: ' . json_encode($ret));
        }
    }


    /**
     * 回复是否参加培训
     * @param array $params
     * @throws \Exception
     * @return void
     * @throws \Throwable
     */
    public function peopleJoin(array $params): void {
        $taskInfo = TrainingTask::findFirst($params['id']);
        if (empty($taskInfo)) {
            throw new \Exception(static::$t->_('no_task!'));
        }

        $taskInfo = $taskInfo->toArray();
        if ($taskInfo['version'] != $params['version'] || $taskInfo['status'] == self::TASK_STATUS_4) {
            throw new \Exception(static::$t->_('train.msg14'));
        }

        try {
            //是否参与
            $join = TrainingPeople::findFirst([
                'task_no = :task_no: AND staff_id = :staff_id: AND  status = :status:',
                'bind' => [
                    'task_no' => $taskInfo['task_no'],
                    'staff_id' => $params['staff_id'],
                    'status' => self::PEOPLE_STATUS_1
                ]
            ]);

            if (empty($join)) {
                throw new \Exception(static::$t->_('train.msg14'));
            }

            if (strtotime($taskInfo['start_time'] . ' -1 days') < time()) {
                throw new \Exception(static::$t->_('train.msg14'));
            }

            if ($join->is_join != 1) {
                throw new ValidationException(static::$t->_('train.msg13'));
            }

            $join->is_join = $params['select_type'];
            if ($params['reason_rejection']){
                $join->reason_rejection = $params['reason_rejection'] ?? '';
            }
            if ($params['rejection_type']){
                $join->rejection_type = $params['rejection_type'] ?? 0;
            }
            $join->save();
        } catch (\Throwable $t) {
            throw $t;
        }
    }


    /**
     *  查询超时任务id
     * @return array
     * @throws \Exception
     */
    public function overtime() {

        $time_s = gmdate('Y-m-d H:i:s', strtotime('-1 days') + get_sys_time_offset() * 3600);
        $time_e = gmdate('Y-m-d H:i:s', strtotime('-2 days') + get_sys_time_offset() * 3600);

        $overtimeTasks = TrainingTask::find([
            'conditions' => 'start_time <= :start_time_start: and start_time >= :start_time_end:',
            'bind' => [
                'start_time_start' => $time_s,
                'start_time_end' => $time_e,
            ],
            'columns' => 'id',
            'order' => 'id',
        ]);

        if (empty($overtimeTasks)) {
           return true;
        }
        $related_id = $overtimeTasks->toArray();

        if(empty($related_id)){
            return true;
        }
        $data['related_id'] = $related_id;

        $api = new ApiClient('bi', '', 'trainingmessage.message_to_oa_training_overdue', static::$language);
        $api->setParams([$data]);
        $ret = $api->execute();
        if (!isset($ret['result']) || $ret['result']['code'] != 1) {
            throw new \Exception('发送培训任务消息失败，请求参数：' . json_encode($data) . '，接口返回: ' . json_encode($ret));
        }
    }


    /**
     * 导入
     *
     * @param $data
     *
     */
    public function importTask($data) {
        $staffService = new StaffService();
        $staffId = $data[3];
        $staff = $staffService->searchStaffs([
            ['hsi.staff_info_id = :staff_id:', ["staff_id" => $staffId]]
        ]);
        if ($staff) {
            $staff = $staff[0];
            $trainingType = $this->findTrainingTypeCode($data[2]);
            if ($trainingType) {
                $trainingPool = $this->getTrainingPool($staffId, $trainingType);
                // 添加到培训池中去
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = $trainingType;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->training_status = 2;
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();

                $start_time = date("Y-m-d", strtotime($data[5]))  . " 00:00:00";
                $end_time = date("Y-m-d", strtotime($data[6])) . " 23:59:59";
                // 查询任务是否已经创建
                $trainingTask = TrainingTask::findFirst([
                    'conditions' => ' lecturer = :lecturer: and training_type = :training_type: and start_time = :start_time: and end_time = :end_time: ',
                    'bind' => [
                        'lecturer' => $data[8],
                        'training_type' => $trainingType,
                        'start_time' => $start_time,
                        'end_time' => $end_time
                    ]
                ]);
                if (!$trainingTask) {
                    $trainingTask = new TrainingTask();
                    $trainingTask->task_no = $this->genTMNo($start_time, $end_time);
                    $trainingTask->training_type = $trainingType;
                    $trainingTask->training_organization = $this->findTrainingOrganization($data[7]);
                    $trainingTask->lecturer = $data[8];
                    $trainingTask->training_place_id = $this->getStoreIdByName(trim($data[9]));
                    $trainingTask->training_place = trim($data[9]);
                    $trainingTask->start_time  = $start_time;
                    $trainingTask->end_time  = $end_time;
                    $trainingTask->status = self::TASK_STATUS_3;
                    $trainingTask->create_id = $this->getStaffIdByName($data[11]);
                    $trainingTask->handler_id = $this->getStaffIdByName($data[11]);
                    $trainingTask->audited_id = $this->getStaffIdByName($data[11]);
                    $trainingTask->created_time = $start_time;
                    $trainingTask->save();
                }

                $taskNo = $trainingTask->task_no;
                // 添加培训人员表

                $trainingPeople = TrainingPeople::findFirst([
                    'conditions' => ' task_no = :task_no: and staff_id =  :staff_id: ',
                    'bind' => [
                        'task_no' => $taskNo,
                        'staff_id' => $staffId
                    ]
                ]);
                if (!$trainingPeople) {
                    $trainingPeople = new TrainingPeople();
                    $trainingPeople->staff_id = $staffId;
                    $trainingPeople->task_no = $taskNo;
                    $trainingPeople->operator_id = $this->getStaffIdByName($data[11]);
                    $trainingPeople->training_type = $trainingType;
                    $trainingPeople->status = self::PEOPLE_STATUS_1;
                    $trainingPeople->save();
                }
            }
            echo "添加成功： " . $taskNo . " \r\n";
        }
        unset($staffService);
    }


    /**
     *
     * @param $name
     *
     */
    private function getStaffIdByName($name)
    {
        if ($name) {
            $staffInfo = HrStaffInfoModel::findFirst([
                'conditions' => ' name = :name: ',
                'bind' => [
                    'name' => trim($name)
                ]
            ]);

            return $staffInfo ? $staffInfo->staff_info_id : 0;
        }

        return 0;
    }

    /**
     *
     * @param $storeName
     *
     */
    private function getStoreIdByName($storeName)
    {
        if ($storeName) {
            $sysStore = SysStoreModel::findFirst([
                'conditions' => ' name = :name: ',
                'bind' => [
                    'name' => $storeName
                ]
            ]);

            return $sysStore ? $sysStore->id : '';
        }

        return '';
    }

    /**
     *
     * @param $trainingOrganization
     *
     */
    private function findTrainingOrganization($trainingOrganization)
    {
        $map = [
            'EHS' => self::TRAINING_ORGANIZATION_2,
            'Safety' => self::TRAINING_ORGANIZATION_2,
            'EHS Training' => self::TRAINING_ORGANIZATION_2,
        ];


        return $map[trim($trainingOrganization)];
    }


    /**
     *
     *
     * @param $trainingName
     * @return int|string
     *
     */
    private function findTrainingTypeCode($trainingName)
    {
        foreach (self::TRAINING_TYPE_DESC as $k => $name) {
            if ($name == trim($trainingName)) {
                return $k;
            }
        }

        return 0;

    }

    /**
     *  超时修改默认消息
     * @return array
     * @throws \Exception
     */
    public function defaultMsgRule($start_date, $end_date)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('tt.id,tt.task_no,tp.staff_id');
        $builder->from(['tp' => TrainingPeople::class]);
        $builder->leftJoin(TrainingTask::class, 'tp.task_no = tt.task_no', 'tt');
        $builder->where(' tt.start_time >= :start_date: and tt.start_time <= :end_date: and tp.status = 1 AND tp.is_join = 1 ',
            [
                'start_date' => $start_date,
                'end_date'   => $end_date,
            ]);
        $peoples = $builder->getQuery()->execute()->toArray();
        if (!empty($peoples)) {
            $data['related_id'] = $peoples;
            $api                = new ApiClient('bi', '', 'trainingmessage.message_to_oa_training_overdue',
                static::$language);
            $api->setParams([$data]);
            $ret = $api->execute();
            if (!isset($ret['result']) || $ret['result']['code'] != ErrCode::$SUCCESS) {
                throw new \Exception(' default_msg_rule 发送培训任务消息失败，请求参数：'.json_encode($data,
                        JSON_UNESCAPED_UNICODE).'，接口返回: '.json_encode($ret));
            }

            $ids     = array_column($peoples, 'task_no');
            $peoples = TrainingPeople::find([
                'conditions' => ' task_no in ({task_no:array}) and status = :status: AND is_join = :is_join:',
                'bind'       => [
                    'task_no' => $ids,
                    'is_join' => self::TASK_JOIN_1,
                    'status'  => self::TASK_STATUS_1,
                ],
            ]);
            foreach ($peoples as $v) {
                $v->is_join          = self::TASK_JOIN_3;
                $v->reason_rejection = 'system';
                $v->rejection_type   = self::REJECTION_TYPE_5;
                $v->save();
            }
            $this->logger->info(' default_msg_rule task_no :'.json_encode($ids, JSON_UNESCAPED_UNICODE));
        } else {
            $this->logger->info(' default_msg_rule 无数据可执行');
        }
        return $peoples;
    }


}