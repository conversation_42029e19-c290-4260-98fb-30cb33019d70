<?php

namespace App\Modules\Training\Services;

use App\Library\ErrCode;
use App\Library\OssHelper;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Hc\Models\SysStoreModel;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\Training\Models\TrainingPeople;
use App\Modules\Training\Models\TrainingPool;
use App\Modules\Training\Models\TrainingTask;
use App\Modules\User\Models\HrStaffInfoModel;

class StaffService extends BaseService
{

    public $addWheres = [];

    /**
     *
     * 同步到backyard 权量同步
     */
    public function syncAllSTaffs()
    {
        //员工入职安全培训证书 1
        //安全卫生组织培训 6
        //基本消防知识培训 7
        //消防演习培训 8
        //建筑内电路维修资格 10
        //紧急急救知识培训 11
        //机台操作安全培训 12
        foreach ([
                     self::TRAINING_TYPE_1,
                     self::TRAINING_TYPE_6,
                     self::TRAINING_TYPE_7,
                     self::TRAINING_TYPE_8,
                     self::TRAINING_TYPE_10,
                     self::TRAINING_TYPE_11,
                     self::TRAINING_TYPE_12,
                 ] as $trainingType) {
            $this->trainingTypes($trainingType);
        }

        // 管理人员安全培训证书  2
        $this->trainingType2();
        // 主管安全培训证书 3
        $this->trainingType3();
        // 安全员技能培训证书 4
        $this->trainingType4();
        // 叉车安全操作资格  9
        $this->trainingType9();

    }

    /**
     * 获取培训池中是否存在员工的培训
     *
     * @param $trainingType
     * @param $staffId
     * @return \Phalcon\Mvc\Model
     *
     */
    private function getTrainingPool($trainingType, $staffId)
    {
        return TrainingPool::findFirst([
            'conditions' => ' staff_id = :staff_id: and training_type = :training_type: ',
            'bind' => [
                'staff_id' => $staffId,
                'training_type' => $trainingType
            ]
        ]);
    }

    private function trainingTypes($trainingType = self::TRAINING_TYPE_1)
    {
        $page = 1;
        $this->getDI()->get('logger')->info('training_type_' . $trainingType . ' sync begin.');
        do {
            $staffs = $this->searchStaffs([], $page);
            foreach ($staffs as $staff) {
                $trainingPool = $this->getTrainingPool($trainingType, $staff['staff_info_id']);
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = $trainingType;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->job_title_grade = $staff['job_title_grade_v2'];
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();
            }
            $page++;
        } while ($staffs);

        $this->getDI()->get('logger')->info('training_type_' . $trainingType . ' sync done.');

    }


    private function trainingType2()
    {
        $page = 1;
        $this->getDI()->get('logger')->info('training_type_2 sync begin.');
        do {
            $staffs = $this->searchStaffs([
                ['hsi.job_title_grade_v2 in ({job_title_grade:array})  ' , ['job_title_grade' => [15, 16]]]
            ], $page);
            foreach ($staffs as $staff) {
                $trainingPool =$this->getTrainingPool(self::TRAINING_TYPE_2, $staff['staff_info_id']);
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = self::TRAINING_TYPE_2;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->job_title_grade = $staff['job_title_grade_v2'];
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();
            }
            $page++;
        } while ($staffs);

        $this->getDI()->get('logger')->info('training_type_2 sync done.');

    }

    private function trainingType3()
    {
        $page = 1;
        $this->getDI()->get('logger')->info('training_type_3 sync begin.');
        do {
            $staffs = $this->searchStaffs([
                ['hsi.job_title_grade_v2 >= :job_title_grade:' , ['job_title_grade' => 17]]
            ], $page);
            foreach ($staffs as $staff) {
                $trainingPool = $this->getTrainingPool(self::TRAINING_TYPE_3, $staff['staff_info_id']);
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = self::TRAINING_TYPE_3;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->job_title_grade = $staff['job_title_grade_v2'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();
            }
            $page++;
        } while ($staffs);

        $this->getDI()->get('logger')->info('training_type_3 sync done.');

    }


    private function trainingType4()
    {
        $page = 1;
        $this->getDI()->get('logger')->info('training_type_4 sync begin.');
        do {
            $staffs = $this->searchStaffs([
                ['hsi.sys_department_id = :sys_department:', ['sys_department' => 43]]
            ], $page);
            foreach ($staffs as $staff) {
                $trainingPool = $this->getTrainingPool(self::TRAINING_TYPE_4, $staff['staff_info_id']);
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = self::TRAINING_TYPE_4;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->job_title_grade = $staff['job_title_grade_v2'];
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();
            }
            $page++;
        } while ($staffs);

        $this->getDI()->get('logger')->info('training_type_4 sync done.');

    }

    private function trainingType9()
    {

        $page = 1;
        $this->getDI()->get('logger')->info('training_type_9 sync begin.');
        do {
            $staffs = $this->searchStaffs([
                ['hsi.job_title in ({job_title:array})', ['job_title' => [992, 918, 745, 546]]]
            ], $page);
            foreach ($staffs as $staff) {
                $trainingPool = $this->getTrainingPool(self::TRAINING_TYPE_9, $staff['staff_info_id']);
                if (!$trainingPool) {
                    $trainingPool = new TrainingPool();
                    $trainingPool->staff_id = $staff['staff_info_id'];
                    $trainingPool->training_type = self::TRAINING_TYPE_9;
                }
                $trainingPool->sys_store_id = $staff['sys_store_id'];
                $trainingPool->manager_region = $staff['manage_region'];
                $trainingPool->manager_piece = $staff['manage_piece'];
                $trainingPool->job_title = $staff['job_title'];
                $trainingPool->job_title_level = $staff['job_title_level'];
                $trainingPool->node_department_id = $staff['node_department_id'];
                $trainingPool->sys_department_id = $staff['sys_department_id'];
                $trainingPool->job_title_grade = $staff['job_title_grade_v2'];
                $trainingPool->hire_date = date("Y-m-d", strtotime($staff['hire_date']));
                $trainingPool->save();
            }
            $page++;
        } while ($staffs);

        $this->getDI()->get('logger')->info('training_type_9 sync done.');

    }


    /**
     * @param $conditions
     * @param $page
     * @param $pageSize
     */
    public function searchStaffs($conditions = [], $page = 1, $pageSize = 100)
    {
        // 全部的在职 停职
        $builder = $this->modelsManager->createBuilder()
            ->columns('hsi.staff_info_id, 
            hsi.sys_store_id, 
            ss.manage_region, 
            ss.manage_piece, 
            hsi.sys_department_id,
            hsi.node_department_id,
            hsi.job_title,
            hsi.job_title_level,
            hsi.job_title_grade_v2,
            hsi.hire_date
            ')
            ->from(['hsi' => HrStaffInfoModel::class])
            ->leftJoin(SysStoreModel::class, 'hsi.sys_store_id = ss.id', 'ss')
            ->where(' hsi.state in ({state:array}) and wait_leave_state = 0 ', ['state' => [ 1, 3]]);

        if ($conditions) foreach ($conditions as $condition) {
            $builder->andWhere($condition[0], $condition[1]);
        }
        if ($this->addWheres) foreach ($this->addWheres as $addWhere) {
            $builder->andWhere($addWhere[0], $addWhere[1]);
        }

        $offset = $pageSize * ($page-1);
        $builder->orderBy('hsi.id');
        $builder->limit($pageSize,$offset);

        return $builder->getQuery()->execute()->toArray();
    }


    public function buildQuery($params)
    {
        $conditions = [];
        $bind = [];
        if (isset($params['training_type']) && $params['training_type']) {
            $conditions[] = ' training_type = :training_type: ';
            $bind['training_type'] = $params['training_type'];
        }

        if (isset($params['staff_id']) && $params['staff_id']) {
            $conditions[] = ' staff_id = :staff_id: ';
            $bind['staff_id'] = $params['staff_id'];
        }

        if (isset($params['store_id']) && $params['store_id']) {
            $conditions[] = ' sys_store_id = :store_id: ';
            $bind['store_id'] = $params['store_id'];
        }

        if (isset($params['job_title']) && $params['job_title']) {
            $conditions[] = ' job_title = :job_title: ';
            $bind['job_title'] = $params['job_title'];
        }

        if (isset($params['training_status']) && $params['training_status']) {
            $conditions[] = ' training_status = :training_status: ';
            $bind['training_status'] = $params['training_status'];
        }

        return [$conditions, $bind];
    }


    public function staffsList($params)
    {
        list($conditions, $bind) = $this->buildQuery($params);
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['pool' => TrainingPool::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'pool.staff_id = staff.staff_info_id', 'staff');
        foreach ($conditions as $cond_k => $cond_v) {
            // 提取参数
            if (preg_match('/:([a-zA-Z_-]+):/', $cond_v, $match_result_arr)) {
                $_field_var_key = $match_result_arr[1] ?? '';
                if (isset($bind[$_field_var_key])) {
                    $builder->andWhere('pool.'.trim($cond_v), [$_field_var_key => $bind[$_field_var_key]]);
                }
            }
        }

        if (isset($params['department_id']) && $params['department_id']) {
            $builder->andWhere(' (pool.node_department_id = :department_id: or pool.sys_department_id = :department_id: ) ',
                ['department_id' => $params['department_id']]);
        }
        if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
            $builder->andWhere(' pool.hire_date <= :end_time: and pool.hire_date >= :start_time: ',
                ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
        }


        if (isset($params['staff_state']) && $params['staff_state']) {
            $staff_state = explode(',', $params['staff_state']);
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $staff_state)) {
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state: and staff.state = :staff_state: or (staff.state IN ({state:array}))',
                    ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES, 'state' => $staff_state,'staff_state' => StaffInfoEnums::STAFF_STATE_IN]);
            } else {
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state: and staff.state IN ({state:array})',
                    ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO, 'state' => $staff_state]);
            }
        }
        $builder->columns('COUNT(DISTINCT pool.id) AS total');
        $count = (int)$builder->getQuery()->getSingleResult()->total;
        $builder->columns([
            'pool.id',
            'pool.staff_id',
            'pool.job_title_level',
            'pool.training_files',
            'pool.sys_department_id',
            'pool.job_title_grade',
            'pool.job_title',
            'pool.manager_region',
            'pool.manager_piece',
            'pool.sys_store_id',
            'pool.node_department_id',
            'pool.training_type',
            'pool.training_status',
            'pool.hire_date',
            'pool.updated_time',
            'staff.name',
            'staff.wait_leave_state',
            'staff.state',
        ]);
        $builder->limit($params['page_size'], $params['page_size'] * ($params['page'] - 1));
        $staffList = $builder->getQuery()->execute()->toArray();
        $result = [];
        if ($staffList) {
            // 名字
            $staffIds = array_column($staffList, 'staff_id');

            // 职位
            $jobTitleIds = array_column($staffList, 'job_title');
            $jobTitles = HrJobTitleModel::find([
                'columns' => 'id, job_name',
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $jobTitleIds]
            ])->toArray();
            $jobTitles = array_column($jobTitles, null, 'id');

            // 大区
            $regionIds = array_column($staffList, 'manager_region');
            $regions = SysManageRegionModel::find([
                'columns' => 'id, name',
                'conditions' => ' id in ({ids:array}) ',
                'bind' => ['ids' => $regionIds]
            ])->toArray();
            $regions = array_column($regions, null, 'id');

            // 片区
            $pieceIds = array_column($staffList, 'manager_piece');
            $pieces = SysManageRegionModel::find([
                'columns' => 'id, name',
                'conditions' => ' id in ({ids:array}) ',
                'bind' => ['ids' => $pieceIds]
            ])->toArray();
            $pieces = array_column($pieces, null, 'id');

            $storeIds = array_column($staffList, 'sys_store_id');
            $stores = SysStoreModel::find([
                'columns' => 'id, name',
                'conditions' => ' id in ({ids:array})',
                'bind' => ['ids' => $storeIds]
            ])->toArray();
            $stores = array_column($stores, null, 'id');

            $departmentIds = array_column($staffList, 'node_department_id');
            $departments = SysDepartmentModel::find([
                'columns' => ' id, name',
                'conditions' => ' id in ({ids:array}) ',
                'bind' => [
                    'ids' => $departmentIds
                ]
            ])->toArray();
            $departments = array_column($departments, null, 'id');

            // 培训任务
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('tp.staff_id, tp.training_type, tt.lecturer, tt.training_organization, tt.training_place, tt.end_time, tt.training_mode, tt.online_link, tt.online_id, tt.online_password');
            $builder->from(['tp' => TrainingPeople::class]);
            $builder->leftJoin(TrainingTask::class, 'tp.task_no = tt.task_no', 'tt');
            $builder->where(' tp.staff_id in ({staff_ids:array}) and tp.training_type = :type: and tp.status = 1 ', [
                'staff_ids' => $staffIds,
                'type' => $params['training_type']
            ]);
            $peoples = $builder->getQuery()->execute()->toArray();
            $peoples = array_column($peoples, null, 'staff_id');


            foreach ($staffList as $item) {
                $_state = $item['state'] ?? '';
                if ($_state == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                    // 待离职
                    $_state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
                }

                $training_files = json_decode($item['training_files'], true);
                $result[] = [
                    'id'                    => $item['id'],
                    'staff_id'              => $item['staff_id'],
                    'staff_name'            => $item['name'] ?? '',
                    'staff_state'           => $_state ?? '',
                    'staff_state_name'      => !empty($_state) ? static::$t->_(StaffInfoEnums::$staff_state[$_state]) : '',
                    'training_type'         => $item['training_type'],
                    'training_type_text'    => static::$t->_('training_type_'.$item['training_type']),
                    'job_title_id'          => $item['job_title'],
                    'job_title'             => isset($jobTitles[$item['job_title']]) ? $jobTitles[$item['job_title']]['job_name'] : '',
                    'manager_region'        => $item['manager_region'],
                    'manager_piece'         => $item['manager_piece'],
                    'region'                => isset($regions[$item['manager_region']]) ? $regions[$item['manager_region']]['name'] : '',
                    'piece'                 => isset($pieces[$item['manager_piece']]) ? $pieces[$item['manager_piece']]['name'] : '',
                    'sys_store_id'          => $item['sys_store_id'],
                    'store_name'            => isset($stores[$item['sys_store_id']]) ? $stores[$item['sys_store_id']]['name'] : '',
                    'sys_department_id'     => $item['sys_department_id'],
                    'node_department_id'    => $item['node_department_id'],
                    'department_name'       => isset($departments[$item['node_department_id']]) ? $departments[$item['node_department_id']]['name'] : '',
                    'training_organization' => isset($peoples[$item['staff_id']]) ? self::TRAINING_ORGANIZATION_DESC[$peoples[$item['staff_id']]['training_organization']] : '',
                    'lecturer'              => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['lecturer'] : '',
                    'training_place'        => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['training_place'] : '',
                    'training_status'       => $item['training_status'],
                    'end_time'              => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['end_time'] : '/',
                    'pass_time'             => $item['training_status'] == TaskService::POOL_STATUS_3 ? $item['updated_time'] : '/',
                    'training_status_text'  => static::$t->_('training_status_'.$item['training_status']),
                    'training_mode'         => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['training_mode'] : '',
                    'training_mode_text'    => isset($peoples[$item['staff_id']]) ? static::$t->_('training_mode_'.$peoples[$item['staff_id']]['training_mode']) : '',
                    'online_link'           => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['online_link'] : '',
                    'online_id'             => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['online_id'] : '',
                    'online_password'       => isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['online_password'] : '',
                    'hire_date'             => $item['hire_date'],
                    'identity_file'         => $training_files['identity_file'] ?? '',
                    'signature_file'        => $training_files['signature_file'] ?? '',
                    'certificate_file'      => $training_files['certificate_file'] ?? '',
                    'other_file'            => $training_files['other_file'] ?? '',
                ];

            }


        }
        return ['count' => $count, 'list' => $result];
    }

    /**
     *
     *
     * @param $params
     *
     * @throws ValidationException
     */
    public function exportStaffsList($params)
    {
        list($conditions, $bind) = $this->buildQuery($params);

        if (empty($bind['staff_id'])) {
            ini_set('memory_limit', '1024M');
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['pool' => TrainingPool::class]);
        $builder->leftjoin(HrStaffInfoModel::class, 'pool.staff_id = staff.staff_info_id', 'staff');
        $builder->columns([
            'pool.staff_id',
            'pool.job_title',
            'pool.manager_region',
            'pool.manager_piece',
            'pool.sys_store_id',
            'pool.node_department_id',
            'pool.training_type',
            'pool.training_status',
            'pool.hire_date',
            'pool.updated_time',
            'staff.name',
            'staff.wait_leave_state',
            'staff.state',
        ]);

        foreach ($conditions as $cond_k => $cond_v) {
            // 提取参数
            if (preg_match('/:([a-zA-Z_-]+):/', $cond_v, $match_result_arr)) {
                $_field_var_key = $match_result_arr[1] ?? '';
                if (isset($bind[$_field_var_key])) {
                    $builder->andWhere('pool.'.trim($cond_v), [$_field_var_key => $bind[$_field_var_key]]);
                }
            }
        }
        if (isset($params['department_id']) && $params['department_id']) {
            $builder->andWhere(' (pool.node_department_id = :department_id: or pool.sys_department_id = :department_id: ) ',
                ['department_id' => $params['department_id']]);
        }
        if (isset($params['start_time']) && isset($params['end_time']) && $params['start_time'] && $params['end_time']) {
            $builder->andWhere(' pool.hire_date <= :end_time: and pool.hire_date >= :start_time: ',
                ['start_time' => $params['start_time'], 'end_time' => $params['end_time']]);
        }

        if (isset($params['staff_state']) && $params['staff_state']) {
            $staff_state = explode(',', $params['staff_state']);
            if (in_array(StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE, $staff_state)) {
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state: and staff.state = 1 or (staff.state IN ({state:array}))',
                    ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES, 'state' => $staff_state]);
            } else {
                $builder->andWhere('staff.wait_leave_state = :wait_leave_state: and staff.state IN ({state:array})',
                    ['wait_leave_state' => StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_NO, 'state' => $staff_state]);
            }
        }

        $staffList = $builder->getQuery()->execute()->toArray();
        if (empty($staffList)) {
            throw new ValidationException('Data does not exist', ErrCode::$VALIDATE_ERROR);
        }

        $staffIds = [];
        if (count($staffList) <= 5000) {
            $staffIds = array_slice(array_column($staffList, 'staff_id'), 0, 5000);
        }

        // 职位
        $jobTitleIds = array_column($staffList, 'job_title');
        $jobTitles = HrJobTitleModel::find([
            'columns' => 'id, job_name',
            'conditions' => ' id in ({ids:array})',
            'bind' => ['ids' => $jobTitleIds]
        ])->toArray();
        $jobTitles = array_column($jobTitles, null, 'id');

        // 大区
        $regionIds = array_column($staffList, 'manager_region');
        $regions = SysManageRegionModel::find([
            'columns' => 'id, name',
            'conditions' => ' id in ({ids:array}) ',
            'bind' => ['ids' => $regionIds]
        ])->toArray();
        $regions = array_column($regions, null, 'id');

        // 片区
        $pieceIds = array_column($staffList, 'manager_piece');
        $pieces = SysManageRegionModel::find([
            'columns' => 'id, name',
            'conditions' => ' id in ({ids:array}) ',
            'bind' => ['ids' => $pieceIds]
        ])->toArray();
        $pieces = array_column($pieces, null, 'id');

        $storeIds = array_column($staffList, 'sys_store_id');
        $stores = SysStoreModel::find([
            'columns' => 'id, name',
            'conditions' => ' id in ({ids:array})',
            'bind' => ['ids' => $storeIds]
        ])->toArray();
        $stores = array_column($stores, null, 'id');

        $departmentIds = array_column($staffList, 'node_department_id');
        $departments = SysDepartmentModel::find([
            'columns' => ' id, name',
            'conditions' => ' id in ({ids:array}) ',
            'bind' => [
                'ids' => $departmentIds
            ]
        ])->toArray();
        $departments = array_column($departments, null, 'node_department_id');

        // 培训任务
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('tp.staff_id, tp.training_type, tt.lecturer, tt.training_organization, tt.training_place, tt.end_time, tt.training_mode, tt.online_link, tt.online_id, tt.online_password');
        $builder->from(['tp' => TrainingPeople::class]);
        $builder->leftJoin(TrainingTask::class, 'tp.task_no = tt.task_no', 'tt');
        $builder->where('tp.training_type = :type: and tp.status = 1 ', ['type' => $params['training_type']]);

        if (!empty($staffIds)) {
            $builder->andWhere('tp.staff_id in ({staff_ids:array})', ['staff_ids' => $staffIds,]);
        }

        $peoples = $builder->getQuery()->execute()->toArray();
        $peoples = array_column($peoples, null, 'staff_id');

        $file = 'training_staffs_' . date('YmdHis') . '.xlsx';

        $header = [
            static::$t->_('job_number'),
            static::$t->_('staff_name'),
            static::$t->_('department.manager_job_title'),
            static::$t->_('affiliated_area'),
            static::$t->_('affiliated_district'),
            static::$t->_('affiliated_branch'),
            static::$t->_('affiliated_department'),
            static::$t->_('training_type'),
            static::$t->_('training_institution'),
            static::$t->_('training_trainer'),
            static::$t->_('training_mode'), // 培训方式
            static::$t->_('training_location_link'), // 培训地点/链接
            static::$t->_('training_status'),
            static::$t->_('training_completion_time'),
            static::$t->_('last_pass_training_time'),
            static::$t->_('employment_date'),
            static::$t->_('access_data_confidential_scope_200'),// 在职状态
        ];

        $rows = [];
        foreach ($staffList as $k => $item) {
            $_state = $item['state'] ?? '';
            if ($_state == StaffInfoEnums::STAFF_STATE_IN && $item['wait_leave_state'] == StaffInfoEnums::STAFF_WAIT_LEAVE_STATE_YES) {
                // 待离职
                $_state = StaffInfoEnums::STAFF_STAFF_WAIT_LEAVE;
            }

            $trainingLocaltionLink = '';
            if (isset($peoples[$item['staff_id']])) {
                if ($peoples[$item['staff_id']]['training_mode'] == TaskService::TRAINING_MODE_2) {
                    $trainingLocaltionLink = sprintf(static::$t->_("training_location_link_tmp"), $peoples[$item['staff_id']]['online_id'], $peoples[$item['staff_id']]['online_password'], $peoples[$item['staff_id']]['online_link']) ;
                } else {
                    $trainingLocaltionLink = $peoples[$item['staff_id']]['training_place'];
                }
            }

            $rows[] = [
                $item['staff_id'],
                $item['name'],
                isset($jobTitles[$item['job_title']]) ? $jobTitles[$item['job_title']]['job_name'] : '',
                isset($regions[$item['manager_region']]) ? $regions[$item['manager_region']]['name'] : '',
                isset($pieces[$item['manager_piece']]) ? $pieces[$item['manager_region']]['name'] : '',
                isset($stores[$item['sys_store_id']]) ? $stores[$item['sys_store_id']]['name'] : '',
                isset($departments[$item['node_department_id']]) ? $departments[$item['node_department_id']]['name'] : '',
                static::$t->_('training_type_' . $item['training_type']),
                isset($peoples[$item['staff_id']]) ? self::TRAINING_ORGANIZATION_DESC[$peoples[$item['staff_id']]['organization'] ?? ''] ?? '' : '' ,
                isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['lecturer'] : '',
                isset($peoples[$item['staff_id']]) ? static::$t->_("training_mode_" . $peoples[$item['staff_id']]['training_mode']) : '',
                $trainingLocaltionLink,
                static::$t->_('training_status_' . $item['training_status']),
                $item['training_status'] == TaskService::POOL_STATUS_3 ? $item['updated_time'] : '/',
                isset($peoples[$item['staff_id']]) ? $peoples[$item['staff_id']]['end_time'] : '/',
                $item['hire_date'],
                !empty($_state) ? static::$t->_(StaffInfoEnums::$staff_state[$_state]) : '',
            ];
        }

        $result = $this->exportExcel($header, $rows, $file);

        return ['object_url' => $result['data']];
    }

    /**
     *
     * 员工培训详情
     *
     * @param $staffId
     */
    public function viewStaff($staffId)
    {
        $staffInfo = HrStaffInfoModel::findFirst([
            'conditions' => ' staff_info_id = :staff_id: ',
            'bind' => [
                'staff_id' => $staffId
            ]
        ]);

        $result = [];
        if ($staffInfo) {
            $staffInfo = $staffInfo->toArray();

            $department = SysDepartmentModel::findFirst([
                'conditions' => ' id = :id: ',
                'bind' => ['id' => $staffInfo['node_department_id']]
            ]);

            $trainingPools = TrainingPool::find([
                'conditions' => ' staff_id = :staff_id: ',
                'bind' => [
                    'staff_id' => $staffId
                ]
            ])->toArray();

            $result = [
                'staff_id' => $staffId,
                'name' => $staffInfo['name'],
                'hire_date' => $staffInfo['hire_date'],
                'department' => $department ? $department->name : '',
                'training_list' => []
            ];

            foreach ($trainingPools as $trainingPool) {
                $result['training_list'][] = [
                    'training_type_text' => static::$t->_('training_type_' . $trainingPool['training_type']),
                    'training_status' => static::$t->_('training_status_' . $trainingPool['training_status']),
                    'pass_time' => $trainingPool['training_status'] == TaskService::TASK_STATUS_3 ? $trainingPool['updated_time'] : '/'
                ];
            }

        }

        return $result;


    }



}