<?php

namespace App\Modules\Training\Controllers;

use App\Library\ErrCode;
use App\Library\Enums\StaffInfoEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Training\Services\StaffService;
use App\Modules\Training\Services\TaskService;

class StaffController extends BaseController
{


    public function initialize()
    {

        parent::initialize(); // TODO: Change the autogenerated stub
    }


    /**
     *
     * 员工培训管理 - 员工培训记录
     *
     * @Permission(action='training.person.info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function listAction()
    {
        try {

            $trainingType = $this->request->get('training_type');
            $staffId = $this->request->get('staff_id');
            $storeId = $this->request->get('store_id');
            $departmentId = $this->request->get('department_id');
            $jobTitle= $this->request->get('job_title');
            $trainingStatus = $this->request->get('training_status');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');
            $staffState = $this->request->get('staff_state');
            $page = $this->request->get('page');
            $pageSize = $this->request->get('page_size');

            $params = [
                'training_type' => $trainingType,
                'page' => $page,
                'page_size' => $pageSize,
            ];
            $conditions = [
                'training_type' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC)),
                'page' => 'Required|Int',
                'page_size' => 'Required|Int',
            ];
            if ($staffId) {
                $params['staff_id'] = $staffId;
                $conditions['staff_id'] = 'Int';
            }
            if ($storeId) {
                $params['store_id'] = $storeId;
                $conditions['store_id'] = 'Str';
            }
            if ($departmentId) {
                $params['department_id'] = $departmentId;
                $conditions['department_id'] = 'Int';
            }
            if ($jobTitle) {
                $params['job_title'] = $jobTitle;
                $conditions['job_title'] = 'Int';
            }
            if ($trainingStatus) {
                $params['training_status'] = $trainingStatus;
                $conditions['training_status'] = 'IntIn:' . implode(',', array_keys(TaskService::POOL_STATUS_DESC));
            }
            if ($startTime && $endTime) {
                $params['start_time'] = $startTime;
                $params['end_time'] = $endTime;
                $conditions['start_time'] = 'Date';
                $conditions['end_time'] = 'Date';
            }


            Validation::validate($params, $conditions);

            $staffService = new StaffService();
            $staffList = $staffService->staffsList([
                'training_type' => $trainingType,
                'staff_id' => $staffId,
                'store_id' => $storeId,
                'department_id' => $departmentId,
                'job_title' => $jobTitle,
                'training_status' => $trainingStatus,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'staff_state' => $staffState,
                'page' => $page,
                'page_size' => $pageSize,
            ]);

        } catch (ValidationException $validationException) {

            $this->logger->info('training_staff_list ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {
         
            $this->getDI()->get('logger')->error('training_staff_list' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $staffList);
    }

    /**
     *
     * 员工培训管理 - 员工培训记录
     *
     * @Permission(action='training.person.info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     *
     */
    public function viewAction()
    {
        try {
            $staffId = $this->request->get('staff_id');
            Validation::validate([
                'staff_id' => $staffId
            ], [
                'staff_id' => 'Required|Int'
            ]);

            $staffService = new StaffService();
            $info = $staffService->viewStaff($staffId);

        } catch (ValidationException $validationException) {

            $this->logger->info('training_staff_view ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {

            $this->logger->error('training_staff_view' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $info);

    }

    /**
     *
     * 员工培训管理 - 员工列表
     *
     * @Permission(action='training.person.info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     *
     */
    public function exportListAction()
    {
        try {
            $trainingType = $this->request->get('training_type');
            $staffId = $this->request->get('staff_id');
            $storeId = $this->request->get('store_id');
            $departmentId = $this->request->get('department_id');
            $jobTitle= $this->request->get('job_title');
            $trainingStatus = $this->request->get('training_status');
            $staffState = $this->request->get('staff_state');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');

            $params = [
                'training_type' => $trainingType
            ];
            $validations = [
                'training_type' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC))
            ];

            if ($staffId) {
                $params['staff_id'] = $staffId;
                $validations['staff_id'] = 'Int';
            }
            if ($storeId) {
                $params['store_id'] = $storeId;
                $validations['store_id'] = 'Str';
            }
            if ($departmentId) {
                $params['department_id'] = $departmentId;
                $validations['department_id'] = 'Int';
            }
            if ($jobTitle) {
                $params['job_title'] = $jobTitle;
                $validations['job_title'] = 'Int';
            }

            if ($trainingStatus) {
                $params['training_status'] = $trainingStatus;
                $validations['training_status'] = 'IntIn:' . implode(',', array_keys(TaskService::POOL_STATUS_DESC));
            }
            if ($startTime && $endTime) {
                $params['start_time'] = $startTime;
                $params['end_time'] = $endTime;
                $validations['start_time'] = 'Date';
                $validations['end_time'] = 'Date';
            }

            Validation::validate($params, $validations);

            $lock_key = md5('training.staff.export.all_list_'.$this->user['id']);
            $result = $this->atomicLock(function() use ($trainingType, $staffId, $storeId, $departmentId, $jobTitle, $trainingStatus, $staffState, $startTime, $endTime){
                $staffService = new StaffService();
                return   $staffService->exportStaffsList([
                    'training_type' => $trainingType,
                    'staff_id' => $staffId,
                    'store_id' => $storeId,
                    'department_id' => $departmentId,
                    'job_title' => $jobTitle,
                    'training_status' => $trainingStatus,
                    'staff_state' => $staffState,
                    'start_time' => $startTime,
                    'end_time' => $endTime,
                ]);

            }, $lock_key, 60);

        } catch (ValidationException $validationException) {

            $this->logger->info('training_staff_export ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {

            $this->logger->error('training_staff_export' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
    }

    /**
     *
     * 员工培训管理 - 在职状态下拉
     *
     * @Permission(action='training.person.info')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface*
     *
     */
    public function staffStateListAction()
    {
        //在职状态
        foreach (StaffInfoEnums::$staff_state as $key=>$value) {
            $data[] = ['value'=>(string)$key, 'label' => $this->t->_($value)];
        }
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

}
