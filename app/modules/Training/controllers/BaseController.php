<?php

namespace App\Modules\Training\Controllers;

use App\Library\BaseController as Controller;


/**
 *
 *
 * Class BaseController
 * @package App\Modules\Training\Controllers
 *
 */
class BaseController extends Controller
{
    //模块自己控制
    public function onConstruct()
    {
    }

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();
        //项目作为接口，返回数据，禁用视图
        $this->view->disable(false);
    }
}