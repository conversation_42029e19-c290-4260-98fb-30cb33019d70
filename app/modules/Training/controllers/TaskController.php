<?php

namespace App\Modules\Training\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Training\Services\StaffService;
use App\Modules\Training\Services\TaskService;

class TaskController extends BaseController
{

    public function initialize()
    {

        parent::initialize(); // TODO: Change the autogenerated stub
    }

    /**
     * 创建培训任务 菜单选项
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function menusListAction()
    {
        try {

            $trainingType = $this->request->get('training_type');
            Validation::validate([
                'training_type' => $trainingType
            ], [
                'training_type' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC))
            ]);

            $taskService = new TaskService();
            $menus = $taskService->menuList($trainingType);
        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_menulist ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {

            $this->logger->error('training_task_menulist' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
        return $this->returnJson(ErrCode::$SUCCESS, '',$menus);
    }

    /**
     * 创建培训任务 菜单选项
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function taskMenusListAction()
    {
        try {
            $taskService = new TaskService();
            $menus = $taskService->taskMenuList();
        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_menulist ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {

            $this->logger->error('training_task_menulist' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }
        return $this->returnJson(ErrCode::$SUCCESS, '',$menus);
    }

    /**
     * 根据piece region 取网点列表
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function storeListAction()
    {
        try {
            $pieceId = $this->request->get('piece_id', 'int', 0);
            $regionId = $this->request->get('region_id', 'int', 0);

            Validation::validate([
                'piece_id' => $pieceId,
                'region_id' => $regionId
            ], [
                'piece_id' => 'Required|Int',
                'region_id' => 'Required|Int'
            ]);

            $taskService = new TaskService();
            $menus = $taskService->storeListByPieceAndRegion($pieceId, $regionId);
        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_storeList ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('training_task_storeList' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '',$menus);
    }

    /**
     * 培训任务添加 员工搜索
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     *
     */
    public function staffsListAction()
    {
        try {
            $trainingType = $this->request->get('training_type', 'int', 0);
            $staffId = $this->request->get('staff_id', 'int', 0);
            $regionId = $this->request->get('region_id', 'int', 0);
            $pieceId = $this->request->get('piece_id', 'int', 0);
            $storeId = $this->request->get('store_id');
            $jobTitle = $this->request->get('job_title');
//            $jobTitleGrade = $this->request->get('job_title_grade', 'int', 0);
            $departmentId = $this->request->get('department_id');
            $status = $this->request->get('status', 'int', 0);
            $hireStartDate = $this->request->get('hire_start_date', 'trim', '');
            $hireEndDate = $this->request->get('hire_end_date', 'trim', '');
            $page = $this->request->get('page');
            $pageSize = $this->request->get('page_size');

            $params = [
                'training_type' => $trainingType,
                'page' => $page,
                'page_size' => $pageSize,
            ];
            $validations = [
                'training_type' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC)),
                'page' => 'Required|Int',
                'page_size' => 'Required|Int',
            ];


            if ($status) {
                $params['status'] = $status;
                $conditions['status'] = 'IntIn:0,1,4';
            }
            if ($storeId) {
                $params['store_id'] = $storeId;
                $conditions['store_id'] = 'Arr';
            }
            if ($jobTitle) {
                $params['job_title'] = $jobTitle;
                $conditions['job_title'] = 'Arr';
            }
            if ($departmentId) {
                $params['department_id'] = $departmentId;
                $validations['department_id'] = 'Arr';
            }

            Validation::validate($params, $validations);



            $taskServer = new TaskService();
            $list = $taskServer->staffList([
                'training_type' => $trainingType,
                'staff_id' => $staffId,
                'region_id' => $regionId,
                'piece_id' => $pieceId,
                'store_id' => $storeId,
                'job_title' => $jobTitle,
//                'job_title_grade' => $jobTitleGrade,
                'department_id' => $departmentId,
                'status' => $status ?? [1,4],
                'hire_start_date' => $hireStartDate,
                'hire_end_date' => $hireEndDate,
                'page' => $page,
                'page_size' => $pageSize,
            ]);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_staffsList ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {
            $this->logger->error('training_task_staffsList' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));

        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 培训任务添加-创建培训任务
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function createTaskAction()
    {
        try {
            $trainingType = $this->request->get('training_type');
            $trainingOrganization = $this->request->get('training_organization');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');
            $lecturer = $this->request->get('lecturer');
            $trainingPlaceId = $this->request->get('training_place_id', 'string');
            $trainingPlace = $this->request->get('training_place', 'string');
            $staffIds = $this->request->get('staff_ids');
            $time_long_hour = $this->request->get('hours');
            $time_long_minute = $this->request->get('minute');
            $trainingMode = $this->request->get('training_mode');
            $onLineLink = $this->request->get('online_link');
            $onLineId = $this->request->get('online_id');
            $onLinePassword = $this->request->get('online_password');

            Validation::validate([
                'training_type' => $trainingType,
                'training_organization' => $trainingOrganization,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'lecturer' => $lecturer,
                'staff_ids' => $staffIds,
                'time_long_hour' => $time_long_hour,
                'time_long_minute' => $time_long_minute,
                'trainingMode' => $trainingMode,
            ], [
                'training_type' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC)),
                'training_organization' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_ORGANIZATION_DESC)),
                'start_time' => 'Required|DateTime',
                'end_time' => 'Required|DateTime',
                'lecturer' => 'Required|StrLenGeLe:1,50',
                'staff_ids' => 'Required|ArrLenGeLe:1,200',
                'time_long_hour' => 'Required|IntGe:0',
                'time_long_minute' => 'Required|IntGeLe:0,59',
                'trainingMode' => 'Required|IntIn:' .  implode(',', array_keys(TaskService::TRAINING_DESC)),
            ]);
            if ($trainingMode == TaskService::TRAINING_MODE_1) {
                Validation::validate([
                    'training_place_id' => $trainingPlaceId,
                    'training_place' => $trainingPlace,
                ], [
                    'training_place_id' => 'Required|Str',
                    'training_place' => 'Required|StrLenGeLe:0,100',
                ]);
            } else {
                Validation::validate([
                    'online_link' => $onLineLink,
                    'online_id' => $onLineId,
                    'online_password' => $onLinePassword,
                ], [
                    'online_link' => 'Required|Str',
                    'online_id' => 'StrLenGeLe:0,100',
                    'online_password' => 'StrLenGeLe:0,100',
                ]);
            }

            $taskService = new TaskService();
            $taskService->createTask([
                'training_type' => $trainingType,
                'training_organization' => $trainingOrganization,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'lecturer' => $lecturer,
                'training_place_id' => $trainingPlaceId,
                'training_place' => $trainingPlace,
                'staff_ids' => $staffIds,
                'time_long_hour' => $time_long_hour,
                'time_long_minute' => $time_long_minute,
                'training_mode' => $trainingMode,
                'online_link' => $onLineLink,
                'online_id' => $onLineId,
                'online_password' => $onLinePassword
            ], $this->user);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_createTask ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {


            $this->logger->error('training_task_createTask' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));

        }

        return $this->returnJson(ErrCode::$SUCCESS, '', []);
    }

    /**
     * 培训任务添加-培训任务批量校验员工-excel
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function batchImportStaffAction()
    {
        try {
            $training_type = $this->request->get('training_type');
            $excel_file    = $this->request->getUploadedFiles();
            if (empty($excel_file)) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('excel_file_empty'));
            }
            $file_extension = $excel_file[0]->getExtension();
            if (!in_array($file_extension, ['xlsx'])) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('file_format_error'));
            }
            $config = ['path' => ''];
            $excel  = new \Vtiful\Kernel\Excel($config);
            // 读取文件
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setSkipRows(0)
                ->setType([
                ])
                ->getSheetData();
            if (empty($excel_data)) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('excel_file_empty'));
            }
            if (count($excel_data) > 200) {
                return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('excel_exceed'));
            }
            $lock_key = md5('batchImportStaffAction'.$this->user['id']);
            $res = $this->atomicLock(function() use ($excel_data,$training_type){
                return (new TaskService())->batchImportStaffCheck($excel_data, $training_type, $this->user);
            }, $lock_key, 30);
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * 培训任务管理 - 删除培训任务
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function delTaskAction()
    {
        try {
            $taskNo = $this->request->get('task_no');
            Validation::validate([
                'task_no' => $taskNo
            ], [
                'task_no' => 'Required|StrLenGeLe:1,25'
            ]);

            $taskService = new TaskService();
            $taskService->delTask($taskNo, $this->user);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_delTask ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('training_task_delTask' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', []);

    }

    /**
     *
     * 培训任务管理 - 编辑培训任务
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function updateTaskAction()
    {
        try {
            $taskNo = $this->request->get('task_no');
            $trainingOrganization = $this->request->get('training_organization');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');
            $lecturer = $this->request->get('lecturer');
            $trainingPlaceId = $this->request->get('training_place_id', 'string');
            $trainingPlace = $this->request->get('training_place', 'string');
            $staffIds = $this->request->get('staff_ids');
            $time_long_hour = $this->request->get('hours');
            $time_long_minute = $this->request->get('minute');
            $trainingMode = $this->request->get('training_mode');
            $onLineLink = $this->request->get('online_link');
            $onLineId = $this->request->get('online_id');
            $onLinePassword = $this->request->get('online_password');

            Validation::validate([
                'task_no' => $taskNo,
                'training_organization' => $trainingOrganization,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'lecturer' => $lecturer,
                'training_place_id' => $trainingPlaceId,
                'training_place' => $trainingPlace,
                'staff_ids' => $staffIds,
                'time_long_hour' => $time_long_hour,
                'time_long_minute' => $time_long_minute,
                'trainingMode' => $trainingMode,
            ], [
                'task_no' => 'Required|StrLenGeLe:1,25',
                'training_organization' => 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_ORGANIZATION_DESC)),
                'start_time' => 'Required|DateTime',
                'end_time' => 'Required|DateTime',
                'lecturer' => 'Required|StrLenGeLe:1,50',
                'training_place_id' => 'Required|Str',
                'training_place' => 'Required|StrLenGeLe:0,100',
                'staff_ids' => 'Required|ArrLenGeLe:1,200',
                'time_long_hour' => 'Required|IntGe:0',
                'time_long_minute' => 'Required|IntGeLe:0,59',
                'trainingMode' => 'Required|IntIn:' .  implode(',', array_keys(TaskService::TRAINING_DESC)),
            ]);

            if ($trainingMode == TaskService::TRAINING_MODE_1) {
                Validation::validate([
                    'training_place_id' => $trainingPlaceId,
                    'training_place' => $trainingPlace,
                ], [
                    'training_place_id' => 'Required|Str',
                    'training_place' => 'Required|StrLenGeLe:0,100',
                ]);
            } else {
                Validation::validate([
                    'online_link' => $onLineLink,
                    'online_id' => $onLineId,
                    'online_password' => $onLinePassword,
                ], [
                    'online_link' => 'Required|Str',
                    'online_id' => 'Required|StrLenGeLe:0,100',
                    'online_password' => 'Required|StrLenGeLe:0,100',
                ]);
            }
            $taskService = new TaskService();
            $taskService->updateTask([
                'task_no' => $taskNo,
                'training_organization' => $trainingOrganization,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'lecturer' => $lecturer,
                'training_place_id' => $trainingPlaceId,
                'training_place' => $trainingPlace,
                'staff_ids' => $staffIds,
                'time_long_hour' => $time_long_hour,
                'time_long_minute' => $time_long_minute,
                'training_mode' => $trainingMode,
                'online_link' => $onLineLink,
                'online_id' => $onLineId,
                'online_password' => $onLinePassword
            ], $this->user);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_updateTask ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {

            $this->logger->error('training_task_updateTask' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', []);

    }



    /**
     * 培训任务管理 - 培训列表
     *
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function taskListAction()
    {

        try {
            $trainingType = $this->request->get('training_type');
            $status = $this->request->get('status');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');
            $trainingOrganization = $this->request->get('training_organization');
            $operatorId = $this->request->get('operator_id');
            $trainingStatus = $this->request->get('training_status');
            $page = $this->request->get('page');
            $pageSize = $this->request->get('page_size');

            $params = [
                'status' => $status,
                'page' => $page,
                'page_size' => $pageSize,
            ];
            $validations = [
                'status' => 'Required|IntIn:1,2',
                'page' => 'Required|Int',
                'page_size' => 'Required|Int',
            ];

            if ( ! empty($trainingType)) {
                $params['training_type'] = $trainingType;
                $validations['training_type'] = 'Arr';
                $validations['training_type[*]'] = 'IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC));
            }

            if ($trainingOrganization) {
                $params['training_organization'] = $trainingOrganization;
                $validations['training_organization'] = 'IntIn:' . implode(',', array_keys(StaffService::TRAINING_ORGANIZATION_DESC));
            }
            if ($startTime && $endTime) {
                $params['start_time'] = $startTime;
                $params['end_time'] = $endTime;
                $validations['start_time'] = 'Date';
                $validations['end_time'] = 'Date';
            }
            if ($operatorId) {
                $params['operator_id'] = $operatorId;
                $validations['operator_id'] = 'Int';
            }
            if ($trainingStatus) {
                $params['training_status'] = $trainingStatus;
                $validations['training_status'] = 'IntIn:' . implode(',', array_keys(TaskService::TASK_STATUS_DESC));
            }
            Validation::validate($params, $validations);

            $taskService = new TaskService();
            $list = $taskService->taskList([
                'training_type' => $trainingType,
                'status' => $status,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'training_organization' => $trainingOrganization,
                'operator_id' => $operatorId,
                'training_status' => $trainingStatus,
                'node_department_id' => $this->user['node_department_id'],
                'page' => $page,
                'page_size' => $pageSize,
            ]);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_listTask ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {

            $this->logger->error('training_task_listTask' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));

        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     *
     * 导出任务列表
     *
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exportListAction()
    {
        try {
            $trainingType = $this->request->get('training_type');
            $status = $this->request->get('status');
            $startTime = $this->request->get('start_time');
            $endTime = $this->request->get('end_time');
            $trainingOrganization = $this->request->get('training_organization');
            $operatorId = $this->request->get('operator_id');
            $trainingStatus = $this->request->get('training_status');

            $params = [
                'status' => $status
            ];
            $validations = [
                'status' => 'Required|IntIn:1,2'
            ];

            if ($trainingType) {
                $params['training_type'] = $trainingType;
                $validations['training_type'] = 'Required|IntIn:' . implode(',', array_keys(StaffService::TRAINING_TYPE_DESC));
            }

            if ($trainingOrganization) {
                $params['training_organization'] = $trainingOrganization;
                $validations['training_organization'] = 'IntIn:' . implode(',', array_keys(StaffService::TRAINING_ORGANIZATION_DESC));
            }
            if ($startTime && $endTime) {
                $params['start_time'] = $startTime;
                $params['end_time'] = $endTime;
                $validations['start_time'] = 'Date';
                $validations['end_time'] = 'Date';
            }
            if ($operatorId) {
                $params['operator_id'] = $operatorId;
                $validations['operator_id'] = 'Int';
            }
            if ($trainingStatus) {
                $params['training_status'] = $trainingStatus;
                $validations['training_status'] = 'IntIn:' . implode(',', array_keys(TaskService::TASK_STATUS_DESC));
            }
            Validation::validate($params, $validations);

            $taskService = new TaskService();

            $result = $taskService->exportList([
                'training_type' => $trainingType,
                'status' => $status,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'training_organization' => $trainingOrganization,
                'operator_id' => $operatorId,
                'training_status' => $trainingStatus,
            ]);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_listTask ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {

            $this->logger->error('training_task_listTask' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', ['object_url' => $result]);
    }

    /**
     * 培训任务管理 - 培训列表
     *
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function taskInfoAction()
    {
        try {
            $taskNo = $this->request->get('task_no');

            Validation::validate([
                'task_no' => $taskNo
            ], [
                'task_no' => 'Required|Str'
            ]);

            $taskService = new TaskService();
            $taskInfo = $taskService->taskInfo($taskNo);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_taskInfo ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {
            $this->logger->error('training_task_taskInfo' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $taskInfo);
    }

    /**
     *
     * 培训任务管理 - 审核任务
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function reviewTaskAction()
    {
        try {
            $taskNo = $this->request->get('task_no');
            $staffIds = $this->request->get('staff_ids');
            $identity_file = $this->request->get('identity_file');//身份资料
            $certificate_file = $this->request->get('certificate_file');//培训证书
            $signature_file = $this->request->get('signature_file');//签字表
            $other_file = $this->request->get('other_file');//其他资料

            $params = [
                'task_no' => $taskNo,
                'identity_file' => $identity_file,
                'signature_file' => $signature_file,
            ];

            $validators = [
                'task_no' => 'Required|StrLenGeLe:1,25',
                'identity_file' => 'Required|Url',
                'signature_file' => 'Required|Url',
            ];

            if ( ! empty($certificate_file)) {
                $params['certificate_file'] = $certificate_file;
                $validators['certificate_file'] = 'Url';
            }

            if ( ! empty($other_file)) {
                $params['other_file'] = $other_file;
                $validators['other_file'] = 'Url';
            }

            Validation::validate($params, $validators);
            if (!$staffIds) {
                $staffIds = [];
            }

            //培训文件
            $files = json_encode([
                'identity_file' => $identity_file,
                'signature_file' => $signature_file,
                'certificate_file' => $certificate_file,
                'other_file' => $other_file,
            ], JSON_UNESCAPED_UNICODE);

            $taskService = new TaskService();
            $taskService->reviewTask($taskNo, $staffIds, $this->user, $files);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_reiview ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        } catch (\Exception $exception) {

            $this->logger->error('training_task_reiview' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', []);
    }


    /**
     *
     * 培训任务管理 - 操作记录
     *
     *
     * @Permission(action='training.task.create')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function operatorLogAction()
    {

        try {
            $taskNo = $this->request->get('task_no');

            Validation::validate([
                'task_no' => $taskNo
            ], [
                'task_no' => 'Required|StrLenGeLe:1,30'
            ]);

            $taskService = new TaskService();
            $operatorLogs = $taskService->operateLog($taskNo);

        } catch (ValidationException $validationException) {
            $this->logger->info('training_task_reiview ' .
                ' code ' . $validationException->getCode() .
                ' file ' . $validationException->getFile() .
                ' line ' . $validationException->getLine() .
                ' message ' . $validationException->getMessage() .
                ' trace ' . $validationException->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $validationException->getMessage());
        }  catch (\Exception $exception) {
            $this->logger->error('training_task_reiview' .
                ' code ' . $exception->getCode() .
                ' file ' . $exception->getFile() .
                ' line ' . $exception->getLine() .
                ' message ' . $exception->getMessage() .
                ' trace ' . $exception->getTraceAsString()
            );

            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t->_('retry_later'));
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $operatorLogs);
    }

}