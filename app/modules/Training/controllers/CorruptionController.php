<?php


namespace App\Modules\Training\Controllers;

use App\Library\Enums\CorruptionEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Services\BaseService;
use App\Modules\Training\Services\CorruptionService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 贪污案件管理
 */
class CorruptionController extends BaseController
{

    /**
     * @var array|string
     */
    private $params;

    public function initialize()
    {
        parent::initialize();
        $this->params                          = trim_array($this->request->get());
        $this->params['operate_staff_info_id'] = $this->user['id'];
    }

    /**
     * 获取所有枚举值
     * @Token
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71492
     */
    public function getBasicInfoAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.getBasicInfo', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 待处理数据list接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71487
     */
    public function pendingListAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.pendingList', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 待处理数据list导出接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function pendingListExportAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.pendingListExport', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 已处理列表接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71482
     */
    public function processedListAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.processedList', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 已处理列表导出接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function processedListExportAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.processedListExport', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 创建案件接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71477
     */
    public function createAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.create', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 案件撤销接口
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71472
     */
    public function revokeAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.revoke', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 获取案件详情
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71467
     */
    public function detailAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.detail', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 案件定级上报接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71462
     */
    public function addGradAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addGrad', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 审讯计划上报
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71457
     */
    public function addPlanAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addPlan', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 审讯记录上报
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api:https://yapi.flashexpress.pub/project/133/interface/api/71452
     */
    public function addInterrogationRecordAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addInterrogationRecord', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }


    /**
     * 结案上报
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api：https://yapi.flashexpress.pub/project/133/interface/api/71447
     */
    public function corruptionCloseAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.corruptionClose', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 报警授权
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api：https://yapi.flashexpress.pub/project/133/interface/api/71442
     */
    public function authorizationAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.authorization', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 报警信息上报
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api：https://yapi.flashexpress.pub/project/133/interface/api/71432
     */
    public function addPoliceAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addPolice', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 追偿信息上报
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @api：https://yapi.flashexpress.pub/project/133/interface/api/71427
     */
    public function addRecoverAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addRecover', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 赔偿信息上报
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     * @api：https://yapi.flashexpress.pub/project/133/interface/api/71422
     */
    public function addReparationAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addReparation', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 修改案件负责人
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71417
     */
    public function modifyPrincipalStaffAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.modifyPrincipalStaff', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 模糊查询犯罪嫌疑人
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     */
    public function searchSuspectsAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.searchSuspects', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * 模糊查询案件负责人
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     */
    public function searchStaffAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.searchStaff', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * V1.3 新增
     * 修改定级接口
     * @Permission(action='training.corruption')
     * @return void
     * @throws ValidationException
     */
    public function editGradeAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.editGrade', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * V1.3新增
     * 取消报警接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function canclePoliceAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.canclePolice', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * V1.4新增
     * 取消报警接口
     * @Permission(action='training.corruption')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addPlanContinueAction()
    {
        $result = CorruptionService::getInstance()->sendRequest('corruption.addPlanContinue', $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

    /**
     * @Permission(action='training.corruption')
     * 后续新增接口统一入口
     * @param $name
     * @param $args
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function __call($name, $args)
    {
        // 去掉 Action字样
        $bi_method = 'corruption.' . substr($name, 0, -6);
        $result    = CorruptionService::getInstance()->sendRequest($bi_method, $this->params);
        return $this->returnJson($result['code'], $result['msg'], $result['data']);
    }

}