<?php
/**
 * 支票模块
 */

namespace App\Modules\Cheque\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Cheque\Services\ChequeService;
use App\Modules\Cheque\Services\BaseService;
use App\Library\Enums\ChequeEnums;
use App\Modules\Cheque\Services\ChequeFlowService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class ChequeController extends BaseController
{


    /**
     * 支票管理-支票枚举接口
     * @Token
     * @Date: 9/5/22 4:27 PM
     * @return  Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62957
     * @author: peak pan
     **/
    public function getOptionsDefaultAction()
    {
        $res = ChequeService::getInstance()->getCreatePageBaseInfoDefaultData();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票管理-初始化支票创建数据
     * @Token
     * @Date: 9/5/22 4:27 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62961
     * @author: peak pan
     **/
    public function getAddDefaultAction()
    {
        $res = ChequeService::getInstance()->getAddDefault($this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-购买公司、购买银行、银行账号级联数据
     *
     * @Token
     * @Date  9/8/22 4:58 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62962
     * @author: peak pan
     */
    public function getCompanyListAction()
    {
        $res = ChequeService::getInstance()->getCompanyList($this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票管理-我的押金列表支票管理-支票申请-列表
     * @Permission(action='cheque.cheque.list')
     * @Date 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62943
     **/

    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_cheque_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->getList($params, $this->user['id'], ChequeEnums::LIST_TYPE_APPLY);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票申请-查看
     * @Permission(action='cheque.cheque.view')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62964
     **/
    public function viewAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_cheque_view);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ChequeService::getInstance()->getDetail($param['id'], $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票申请-撤回
     * @Permission(action='cheque.cheque.add')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/63034
     **/
    public function withdrawAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_cheque_withdraw);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = (new ChequeFlowService())->cancel($data['id'], $data['note'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 支票管理-支票申请-添加
     * @Permission(action='cheque.cheque.add')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62946
     **/
    public function addAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->addApply($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票审核-列表
     * @Permission(action='cheque.cheque.audit_list')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62951
     **/
    public function auditListAction()
    {

        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_audit_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->getList($params, $this->user['id'], ChequeEnums::LIST_TYPE_AUDIT);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);


    }


    /**
     * 支票管理-支票审核-查看
     * @Permission(action='cheque.cheque.audit_view')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62964
     **/
    public function auditViewAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_cheque_view);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ChequeService::getInstance()->getDetail($param['id'], $this->user['id']);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票管理-支票审核-审核
     * @Permission(action='cheque.cheque.audit_approve')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62959
     **/
    public function auditApproveAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_approve);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = (new ChequeFlowService())->approve($params['id'], $params['note'] ?? '',
            $this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票审核-驳回
     * @Permission(action='cheque.cheque.audit_approve')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62960
     **/
    public function rejectAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_reject);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = (new ChequeFlowService())->reject($params['id'], $params['note'], $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-数据查询-查询列表
     * @Permission(action='cheque.cheque.select_list')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62963
     **/
    public function selectListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_audit_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->getList($params, $this->user['id'], 0);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-数据查询-查看
     * @Permission(action='cheque.cheque.select_view')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62965
     **/
    public function selectViewAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_cheque_view);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ChequeService::getInstance()->getDetail($param['id'], $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-数据查询-导出
     * @Permission(action='cheque.cheque.select_export')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/62950
     **/
    public function selectExportAction()
    {
        $param           = $this->request->get();
        $param           = BaseService::handleParams($param, []);
        $param['export'] = true;
        try {
            Validation::validate($param, BaseService::$validate_audit_list_export);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('cheque_select_export_download_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($param) {

            return ChequeService::getInstance()->downloadCheque($param, $this->user);
        }, $lock_key, 20);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-数据查询-新增支票号
     * @Permission(action='cheque.cheque.select_add')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/63004
     **/
    public function selectAddAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_select_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->addBatchApply($data, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 支票管理-支票申请-编辑
     * @Permission(action='cheque.cheque.edit')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/63004
     **/
    public function editAction()
    {
        $data = $this->request->get();
        try {
            Validation::validate($data, BaseService::$validate_add);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->addApply($data, $this->user);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票管理-支票台账-查询列表
     * @Permission(action='cheque.cheque.account_list')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62980
     **/
    public function accountListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_account_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ChequeService::getInstance()->getAccountList($params, $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 支票管理-支票台账-导出
     * @Permission(action='cheque.cheque.account_export')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63002
     **/
    public function accountExportAction()
    {
        $param = $this->request->get();
        $param = BaseService::handleParams($param, []);
        try {
            Validation::validate($param, BaseService::$validate_account_list_export);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5('cheque_account_export_download_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($param) {
            return ChequeService::getInstance()->downloadAccount($param, $this->user);
        }, $lock_key, 20);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票台账-查看
     * @Permission(action='cheque.cheque.account_view')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62981
     **/
    public function accountViewAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_detail_param);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = ChequeService::getInstance()->getAccountDetail($param);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票台账-作废
     * @Permission(action='cheque.cheque.account_abolish')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62998
     **/
    public function accountAbolishAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_account_abolish);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $param['type'] = ChequeEnums::CHECKS_NUMBER_TYPE_VOID;
        $res           = ChequeService::getInstance()->accountEdit($param, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);

    }


    /**
     * 支票管理-支票台账-替换
     * @Permission(action='cheque.cheque.account_replace')
     *
     * @Date: 9/5/22 4:28 PM
     * @return Response|ResponseInterface
     * @author: peak pan
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62999
     **/
    public function accountReplaceAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_account_replace);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $param['type'] = ChequeEnums::CHECKS_NUMBER_TYPE_REPLACE;
        $res           = ChequeService::getInstance()->accountEdit($param, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票管理 -我的支付-支票号查找
     * @Token
     * @Date: 9/5/22 4:48 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63001
     * @author: peak pan
     */
    public function getSearchChequeNumberAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_search_cheque_number);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->getPaySearchAccount($params);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票台账-替换-支票号查找
     * @Token
     * @Date: 9/5/22 4:48 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63000
     * @author: peak pan
     */
    public function getSearchAccountAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_search_account);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ChequeService::getInstance()->getSearchAccount($params);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 根据支票申请号  查询支票的信息  使用数据查询
     * @Token
     * @Date: 9/17/22 9:21 PM
     * @return Response|ResponseInterface
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/63003
     * @author: peak pan
     */

    public function getSearchApplyAction()
    {
        $params = $this->request->get();

        try {
            Validation::validate($params, BaseService::$validate_search_apply_no);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ChequeService::getInstance()->getSearchApply($params, $this->user);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 支票台账表单展示-关联申请单号
     * @Token
     * @Date: 9/17/22 9:21 PM
     * @return Response|ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63079
     * @author: peak pan
     */

    public function getSearchApplyNoAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_search_apply_no);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = ChequeService::getInstance()->getSearchApplyList($params);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 支票管理-支票台账-释放
     * @Permission(action='cheque.account.release')
     * @return Response|ResponseInterface
     * @yapi  https://yapi.flashexpress.pub/project/133/interface/api/62998
     **/
    public function releaseAction()
    {
        $param = $this->request->get();
        Validation::validate($param, BaseService::$validate_account_release);
        $res           = ChequeService::getInstance()->release($param, $this->user);
        return $this->returnJson($res['code'], $res['message']);
    }



}
