<?php

namespace App\Modules\Cheque\Services;

use App\Library\Enums;
use App\Library\Enums\ChequeEnums;
use App\Library\ErrCode;
use App\Models\oa\ChequeApplyModel;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Library\Enums\GlobalEnums;
use App\Modules\Workflow\Models\WorkflowRequestModel;



class ChequeFlowService extends AbstractFlowService
{

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 支票审批 - 通过操作
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function approve($id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $work_req = $this->getRequest($id);
            if (empty($work_req)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $main_model = ChequeApplyModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if (empty($main_model)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            // 只有待审核的，方可审核
            if ($main_model->status != ChequeEnums::CHECKS_STATUS_ENUMS_ONE) {
                throw new ValidationException(static::$t->_('apply_apply_already_audit'), ErrCode::$VALIDATE_ERROR);
            }
            $update_main_data = [];
            // 审批
            $ws = new WorkflowServiceV2();

            $result = $ws->doApprove($work_req, $user, $this->getWorkflowParams($user), $note);


            // 全部审批通过
            if (!empty($result->approved_at)) {
                $update_main_data['status']      = ChequeEnums::CHECKS_STATUS_ENUMS_THREE;
                $update_main_data['approved_at'] = $result->approved_at;
                $update_main_data['updated_at']  = date('Y-m-d H:i:s', time());
            }

            if (!empty($update_main_data)) {

                // 更新业务主表数据
                $main_bool = $main_model->i_update($update_main_data);
                if ($main_bool === false) {
                    throw new BusinessException('支票管理-支票审核-审核通过- 主表操作失败' . '可能的原因是: ' . get_data_object_error_msg($main_model), ErrCode::$ORDINARY_PAYMENT_APPROL_MAIN_UPDATE_ERROR);
                }

                $this->logger->info('支票管理-支票审核-审核通过 -主表更新后数据: ' . json_encode($main_model->toArray(), JSON_UNESCAPED_UNICODE));
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('cheque-approve-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }


    /**
     * 支票申请 - 驳回
     * @param int $id 工作流申请id
     * @param string $note 驳回原因
     * @param array $user 用户数据
     * @return array
     */
    public function reject($id, $note, $user)
    {
        $code     = ErrCode::$SUCCESS;
        $message  = $real_message = '';
        $db       = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            $work_req = $this->getRequest($id);

            if (empty($work_req)) {
                throw new BusinessException('获取工作流批次失败', ErrCode::$ORDINARY_PAYMENT_GET_WORK_REQUEST_ERROR);
            }

            $cheque_apply_model = ChequeApplyModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id],
            ]);

            if(empty($cheque_apply_model)){
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            //只有待审核的，方可驳回
            if ($cheque_apply_model->status != ChequeEnums::CHECKS_STATUS_ENUMS_ONE) {
                throw new ValidationException(static::$t->_('apply_apply_already_audit'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowParams($user), $note);

            $bool = $cheque_apply_model->i_update([
                'status'      => ChequeEnums::CHECKS_STATUS_ENUMS_TWO,
                'updated_at'  => date('Y-m-d H:i:s'),
                'rejected_at' => $result->rejected_at
            ]);

            if ($bool === false) {
                throw new BusinessException('支票申请- 申请驳回操作失败'. ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_model), ErrCode::$ORDINARY_PAYMENT_REJECT_MAIN_ERROR);
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('支票申请-申请驳回失败:' . $real_message);
        }
        if (!empty($message)) {
            $db->rollback();
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
        ];
    }

    /**
     * 获取审批流信息
     * @param int $id 工作流申请id
     * @return object
     */
    public function getRequest($id)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id: and is_abandon = :is_abandon:',
                'bind'  => ['type' => Enums::WF_CHEQUE_APPLY_BIZ_TYPE, 'id' => $id, 'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO]
            ]
        );
    }


    /**
     * 撤销
     * @Date: 9/25/22 9:35 PM
     * @param $id  撤回的id
     * @param $note  原因
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function cancel($id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            $work_req = $this->getRequest($id);

            if (empty($work_req)) {
                throw new ValidationException(static::$t->_('work_flow_request_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            $item = ChequeApplyModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id]
            ]);

            if (empty($item)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            if ($item->status != ChequeEnums::CHECKS_STATUS_ENUMS_ONE) {
                throw new ValidationException(static::$t->_('apply_apply_already_audit'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doCancel($work_req, $user, $this->getWorkflowParams($user), $note);

            $bool = $item->i_update([
                'status'     => ChequeEnums::CHECKS_STATUS_ENUMS_FOUR,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            if ($bool === false) {
                throw new BusinessException('撤回失败'. ' 可能的原因是: ' . get_data_object_error_msg($item), ErrCode::$CHEQUE_CONTRACT_WITHDRAWAL_FAILED_ERROR);
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('支票撤回失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data' => []
        ];
    }


    /**
     * 创建审批流
     * @Date: 9/25/22 9:35 PM
     * @param object $model 条件对象
     * @param array $user 条件
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($model, $user)
    {
        $data['id']       = $model->id;
        $data['name']     = $model->apply_no . '审批申请';
        $data['biz_type'] = Enums::WF_CHEQUE_APPLY_BIZ_TYPE;
        $data['flow_id']  = $this->getFlowId();
        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowParams($user));
    }


    /**
     * 定义审批流id
     * @param $model  主对象数据
     * @return bool int
     * @author: peak pan
     **/
    public function getFlowId($model = null)
    {
        return Enums::CHEQUE_EXAMINE_FLOW_ID;
    }


    /**
     * 组装审批流数据
     * @Date: 9/25/22 9:35 PM
     * @param array $user 条件
     * @return array
     * @author: peak pan
     **/
    public function getWorkflowParams($user)
    {
        $user_info = HrStaffInfoModel::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id:',
            'bind'       => ['staff_info_id' => $user['id']]
        ]);

        if (empty($user_info)) {
            return [];
        }
        $user             = $user_info->toArray();
        $cost_store_type  = $user_info->sys_store_id == Enums::HEAD_OFFICE_STORE_FLAG ? Enums::PAYMENT_COST_STORE_TYPE_01 : Enums::PAYMENT_COST_STORE_TYPE_02;
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        $currency    = $default_currency['symbol'];
        return [
            'currency'           => $currency,
            'submitter_id'       => $user['id'], // 申请人用，审批流创建人
            'department_id'      => $user['sys_department_id'],//申请人一级部门ID
            'node_department_id' => $user['node_department_id'],//申请人部门id
            'cost_store_type'    => $cost_store_type,//1 :总部,2:网点
            'store_id'           => $user['sys_store_id'],//申请人所属网点id
            'create_staff_id'    => $user['id'],//发起人工号
            'company_id'         => $user['cost_company_id'],   //费用所属公司id，v10533改为费用所属公司判断
            'is_have_welfare'    => false, //是否包含(付款分类=员工福利费 且 费用类型=其他)
        ];
    }


}
