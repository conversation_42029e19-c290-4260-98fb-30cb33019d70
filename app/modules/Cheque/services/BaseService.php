<?php

namespace App\Modules\Cheque\Services;


use App\Library\Enums\GlobalEnums;


class BaseService extends \App\Library\BaseService
{

    //添加
    public static $validate_add = [
        'apply_id'                     => 'Required|IntGt:1|>>>:apply_id error',
        'apply_name'                   => 'StrLenGeLe:0,255|>>>:apply_name  error',
        'apply_email'                  => 'StrLenGeLe:0,50',
        'apply_node_department_id'     => 'IntGe:1|>>>:apply_node_department_id error',
        'apply_company_id'             => 'IntGe:1|>>>:apply_company_id error',
        'reason'                       => 'Required|StrLenGeLe:1,1000|>>>:reason error',
        'attachments'                  => 'Arr|ArrLenGeLe:0,10|>>>:main attachment list  error[Upload up to 10]',
        'apply_detail'                   => 'Required|Arr|ArrLenGeLe:0,10|>>>:main apply_detail list  error[max tr 10]',
        'apply_detail[*]'              => 'Required|Obj',
        'apply_detail[*].company_name' => 'Required|StrLenGeLe:1,128|>>>:company_name error',
        'apply_detail[*].bank_id'      => 'Required|IntGe:1|>>>:bank_id  error',
        'apply_detail[*].bank_account' => 'Required|StrLenGeLe:1,128|>>>:bank_account error',
        'apply_detail[*].quantity'     => 'Required|IntGeLe:1,1000|>>>:quantity error',
        'apply_detail[*].reason'       => 'StrLenLe:500|>>>:apply_detail.reason error',
        'apply_detail[*].cheque_amount'  => 'FloatGeLe:0,*****************0.00|>>>:cheque_amount  error',

    ];

    //列表查询
    public static $validate_cheque_list = [
        'apply_no'          => 'StrLenGeLe:0,30|>>>:apply_no  error',
        'apply_id'          => 'StrLenGeLe:0,60|>>>:apply_id  error',
        'status'            => 'IntIn:1,2,3,4|>>>: status  error',
        'start_date'          => 'Date|>>>:start_date error',
        'end_date'          => 'Date|>>>:end end_date error',
        'start_approved_date' => 'Date|>>>:start_return_date error',
        'end_approved_date' => 'Date|>>>:end_return_date error',
        'pageNum'           => 'IntGe:1|>>>:page error', //当前页码
        'pageSize'          => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    //详情查看
    public static $validate_cheque_view = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];


    //撤回
    public static $validate_cheque_withdraw = [
        'id'   => 'Required|IntGe:1|>>>:id error',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];


    //归还审核列表
    public static $validate_audit_list = [
        'apply_no'          => 'StrLenGeLe:0,30|>>>:apply_no  error',
        'apply_id'          => 'StrLenGeLe:0,60|>>>:apply_id  error',
        'status'            => 'IntIn:1,2,3,4|>>>: status  error',
        'start_date'          => 'Date|>>>:start_date error',
        'end_date'          => 'Date|>>>:end end_date error',
        'start_approved_date' => 'Date|>>>:start_approved_date error',
        'end_approved_date' => 'Date|>>>:end_return_date error',
        'pageNum'           => 'IntGe:1|>>>:page error', //当前页码
        'pageSize'          => 'IntGe:1|>>>:page_size error', //每页条数
    ];


    // 导出
    public static $validate_audit_list_export = [
        'apply_no'          => 'StrLenGeLe:0,30|>>>:apply_no  error',
        'apply_id'          => 'StrLenGeLe:0,60|>>>:apply_id  error',
        'status'            => 'IntIn:1,2,3,4|>>>: status  error',
        'start_date'          => 'Date|>>>:start_date error',
        'end_date'          => 'Date|>>>:end end_date error',
        'start_approved_date' => 'Date|>>>:start_approved_date error',
        'end_approved_date' => 'Date|>>>:end_return_date error',
    ];

    //支票管理-支票台账-替换-支票号查找
    public static $validate_search_account = [
        'cheque_code'     => 'Required|StrLenGeLe:1,30|>>>:cheque_code  error',
    ];


    //台账导出
    public static $validate_account_list = [
        'cheque_code'     => 'StrLenGeLe:0,30|>>>:cheque_code  error',
        'apply_id'        => 'StrLenGeLe:0,255|>>>:apply_id  error',
        'use_status'      => 'IntIn:1,2,3,4|>>>: use_status  error',
        'exchange_status' => 'IntIn:1,2,3|>>>: exchange_status  error',
        'apply_no'        => 'StrLenGeLe:0,32|>>>:apply_no  error',
        'pageNum'         => 'IntGe:1|>>>:page error', //当前页码
        'pageSize'        => 'IntGe:1|>>>:page_size error', //每页条数
    ];

    //台账导出
    public static $validate_account_list_export = [
        'apply_no'        => 'StrLenGeLe:0,32|>>>:apply_no  error',
        'apply_id'        => 'StrLenGeLe:0,60|>>>:apply_id  error',
        'use_status'      => 'IntIn:1,2,3,4|>>>: use_status  error',
        'exchange_status' => 'IntIn:1,2,3|>>>: exchange_status  error',
    ];

    //支票管理-支票审核-审核
    public static $validate_approve = [
        'id'   => 'Required|IntGe:1',
        'note' => 'StrLenGeLe:0,1000'
    ];

    //支票管理-支票审核-驳回
    public static $validate_reject = [
        'id'   => 'Required|IntGe:1',
        'note' => 'Required|StrLenGeLe:1,1000'
    ];

    //支票管理-数据查询-新增支票号
    public static $validate_select_add = [
        'detail_id'                       => 'Required|IntGe:1|>>>:detail_id  error',
        'apply_no'                        => 'Required|StrLenGeLe:0,32|>>>:apply_no  error',
        'cheque_amount'                   => 'Required|FloatGeLe:0,*****************.00|>>>:cheque_amount  error',
        'cheque_detail'                   => 'Required|Arr|ArrLenGeLe:0,20|>>>:main cheque_detail list  error[max tr 20]',
        'cheque_detail[*]'                => 'Required|Obj',
        'cheque_detail[*].start_number'   => 'Required|Numbers|>>>:start_number error',
        'cheque_detail[*].batch_quantity' => 'Required|IntGeLe:1,100|>>>:batch_quantity  error',
        'cheque_detail[*].valid_days'     => 'Required|IntGeLe:1,999|>>>:valid_days error'
    ];

    //作废
    public static $validate_account_abolish = [
        'id'          => 'Required|IntGe:1',
        'reason'      => 'Required|StrLenGeLe:1,1000',
        'attachments' => 'Arr|ArrLenGeLe:0,10|>>>:main attachment list  error[Upload up to 10]',
    ];

    //替换
    public static $validate_account_replace = [
        'id'          => 'Required|IntGe:1',
        'cheque_code' => 'Required|StrLen:10|>>>:start_number error',
        'reason'      => 'Required|StrLenGeLe:1,1000',
        'attachments' => 'Arr|ArrLenGeLe:0,10|>>>:main attachment list  error[Upload up to 10]',
    ];

    // 待回复征询列表
    public static $validate_reply_list = [
        'is_reply' => 'Required|IntIn:0,1|>>>:is_reply is_reply error'
    ];

    // 详情查看校验
    public static $validate_detail_param = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    //搜素支票申请单
    public static $validate_search_apply_no = [
        'apply_no'        => 'Required|StrLenGeLe:0,32|>>>:apply_no  error'

    ];

    //支票管理 -我的支付-支票号查找
    public static $validate_search_cheque_number = [
        'cheque_code'     => 'Required|StrLenGeLe:1,30|>>>:cheque_code  error',
        'currency'        => 'Required|IntIn:'.GlobalEnums::VALIDATE_CURRENCY_PARAMS.'|>>>: currency error',
    ];

    //释放
    public static $validate_account_release = [
        'ids'          => 'Required|Arr|ArrLenGeLe:1,10000|>>>: ids error',
        'release_date' => 'Date|>>>:release_date error',
    ];

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must = [])
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }


}
