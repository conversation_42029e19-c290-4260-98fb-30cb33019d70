<?php

namespace App\Modules\Cheque\Services;

use App\Library\Enums\InventoryCheckEnums;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankListModel;
use App\Modules\Material\Models\MaterialWmsPlantaskModel;
use App\Modules\Pay\Models\Payment;
use App\Modules\Pay\Models\PaymentCheck;
use App\Modules\Pay\Models\PaymentPay;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\User\Models\AttachModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Common\Services\EnumsService;
use App\Library\Enums\ChequeEnums;
use App\Models\oa\ChequeApplyModel;
use App\Models\oa\ChequeApplyDetailModel;
use App\Models\oa\ChequeBatchModel;
use App\Models\oa\ChequeAccountRecordModel;
use App\Models\oa\ChequeAccountModel;
use App\Library\Enums\GlobalEnums;
use App\Models\oa\ChequeAccountBusinessRelModel;
use App\Library\Enums\BankFlowEnums;
use webcommon\tools\strtool;

class ChequeService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 支票添加获取当前登录数据
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getAddDefault($user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        try {
            $data['apply_no']   = static::genSerialNo(ChequeEnums::CHECKS_CHECK_NUMBER_PREFIX, ChequeEnums::CHECKS_CHECK_NUMBER_APPLY_NO_KEY);
            $data['apply_id']   = $user['id'];
            $data['apply_name'] = $user['name'];
            $hr_staff_info      = (new UserService())->getUserByIdInRbi($user['id']);

            if (empty($hr_staff_info)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            //根据所属部门获取公司信息
            $department_obj                     = DepartmentModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => [
                    'id' => $hr_staff_info->node_department_id,
                ],
            ]);
            $data['apply_company_id']           = empty($department_obj->company_id) ? '' : $department_obj->company_id;
            $data['apply_company_name']         = $department_obj->company_name ?? '';
            $data['apply_node_department_id']   = $hr_staff_info->node_department_id ?? '';
            $data['apply_node_department_name'] = $department_obj->name ?? '';
            $data['apply_email']                = $hr_staff_info->email ?? '';
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('支票添加获取当前登录数据异常:' . $e->getMessage());
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data
        ];
    }


    /**
     * 获取公司和银行下的账号
     * @Date: 9/25/22 9:35 PM
     * @param array $bank_company_arr 公司集合
     * @param int $bank_id 银行
     * @param int $company_name 公司名称
     * @return  array
     * @author: peak pan
     **/
    public function getBankAccountList($bank_company_arr, $bank_id, $company_name)
    {
        $account_arr = [];
        foreach ($bank_company_arr as $company_item) {
            if ($company_item['company_name'] == $company_name && $bank_id == $company_item['bank_id']) {
                $account['account_item']       = $company_item['account'];
                $account['currency']           = $company_item['currency'];
                $account['account_is_deleted'] = $company_item['is_deleted'];
                $account_arr[]                 = $account;
            }
        }
        return $account_arr;
    }


    /**
     * 获取银行 公司 银行账号级联数据
     * @return array
     */
    public function getCompanyList()
    {
        $code         = ErrCode::$SUCCESS;
        $message      = 'success';
        $company_list = [];
        try {
            $bank_company_arr = BankAccountModel::find([
                'columns' => ['company_name', 'bank_id', 'account', 'currency', 'is_deleted']
            ])->toArray();

            if (empty($bank_company_arr)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $bank_list = BankListModel::find([
                'columns' => ['id', 'bank_name', 'is_deleted']
            ])->toArray();

            if (empty($bank_list)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $bank_arr    = array_column($bank_list, 'bank_name', 'id');
            $company_arr = array_unique(array_column($bank_company_arr, 'company_name'));

            if (!empty($bank_arr) && !empty($company_arr)) {
                foreach ($company_arr as $key => $company_item) {
                    $item['company_item'] = $company_item;
                    $out_bank             = [];
                    $item['bank']         = [];

                    foreach ($bank_company_arr as $k => $value) {
                        if ($value['company_name'] == $company_item) {
                            $bank_r = [];
                            if (!in_array($value['bank_id'], $out_bank)) {
                                $bank_r['bank_item']       = $bank_arr[$value['bank_id']];
                                $bank_r['bank_id']         = $value['bank_id'];
                                $account_list_array        = $this->getBankAccountList($bank_company_arr, $value['bank_id'], $company_item);
                                $bank_r['bank_is_deleted'] = in_array(GlobalEnums::IS_NO_DELETED, array_unique(array_column($account_list_array, 'account_is_deleted'))) ? (string)GlobalEnums::IS_NO_DELETED : (string)GlobalEnums::IS_DELETED;

                                $bank_r['account_list'] = $account_list_array;
                                $item['bank'][]         = $bank_r;
                                $out_bank[$k]           = $value['bank_id'];
                            }
                        }
                    }
                    $company_list[] = $item;
                }
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取银行、公司、银行账号级联数据:' . $e->getMessage());
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $company_list
        ];
    }


    /**
     * 分页处理
     * @Date  9/7/22 12:56 PM
     * @param array $condition 查询器对象
     * @param int $uid 用户数据
     * @param int $type 分类
     * @return  array
     * @author: peak pan
     */
    public function getList(array $condition, int $uid = 0, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => ChequeEnums::TOTAL_COUNT,
            ],
        ];

        try {
            if (isset($condition['start_date]']) && isset($condition['end_date]']) && $condition['start_date]'] > $condition['end_date]']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['start_approved_date']) && isset($condition['end_approved_date']) && $condition['start_approved_date'] > $condition['end_approved_date']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            $condition['uid'] = $uid;
            $builder          = $this->modelsManager->createBuilder();
            $columns          = [
                'main.id',
                'main.apply_no',
                'main.apply_id',
                'main.apply_name',
                'main.status',
                'main.created_at'
            ];
            $builder->from(['main' => ChequeApplyModel::class]);
            //组合搜索条件
            $builder = $this->getChequeCondition($builder, $condition, $type);
            $count   = (int)$builder->columns('COUNT(DISTINCT main.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                $builder->groupBy('main.id');
                if (!in_array($type, [ChequeEnums::LIST_TYPE_AUDIT, ChequeEnums::LIST_TYPE_CONSULTED_REPLY])) {
                    $builder->orderBy('main.id DESC');
                }
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票-分页处理-列表异常信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 导出数据
     * @Date  9/7/22 12:56 PM
     * @param array $condition 查询器对象
     * @param int $type 分类
     * @return  array
     * @author: peak pan
     */
    public function getExportList(array $condition, int $type = 0)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => ChequeEnums::TOTAL_COUNT,
            ],
        ];

        try {
            if (isset($condition['start_date]']) && isset($condition['end_date]']) && $condition['start_date]'] > $condition['end_date]']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }

            if (isset($condition['start_approved_date']) && isset($condition['end_approved_date']) && $condition['start_approved_date'] > $condition['end_approved_date']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            $builder = $this->modelsManager->createBuilder();
            $columns = [
                'main.id',
                'main.apply_no',
                'main.apply_id',
                'main.apply_name',
                'main.apply_email',
                'main.cost_node_department_name',
                'main.apply_company_name',
                'main.reason',
                'main.status',
                'cad.id as cheque_apply_id',
                'cad.company_name',
                'cad.bank_name',
                'cad.bank_account',
                'cad.quantity',
                'cad.reason as detail_reason',
                'cad.operated_at',
                'cad.cheque_amount',
                'cad.currency',
                'cad.pay_status',
                'cad.bank_flow_at',
                'cad.id as detail_id'
            ];

            $builder->from(['main' => ChequeApplyModel::class]);
            //组合搜索条件
            $builder = $this->getChequeCondition($builder, $condition, $type);
            $count   = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleExportItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票申请-导出数据列表-数据失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 格式化数据
     * @Date  9/7/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['status_name'] = static::$t[ChequeEnums::$checks_status[$item['status']]];
            $item['created_at']  = date('Y-m-d', strtotime($item['created_at']));
        }
        return $items;
    }


    /**
     * 导出数据处理
     * @Date  9/7/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleExportItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        $attachments_ids = array_values(array_filter(array_column($items, 'id')));
        if (!empty($attachments_ids)) {
            $attach_arr      = AttachModel::find(
                [
                    'conditions' => ' oss_bucket_key in({oss_bucket_key:array}) and  oss_bucket_type = :oss_bucket_type:',
                    'bind'       => [
                        'oss_bucket_key'  => $attachments_ids,
                        'oss_bucket_type' => Enums::OSS_PAYMENT_STORE_TYPE_CHEQUE_APPLY_ADD,
                    ]
                ]
            )->toArray();
            $attach_arr_list = [];
            if (!empty($attach_arr)) {
                $rs = [];
                foreach ($attach_arr as $item_att) {
                    if (in_array($item_att['oss_bucket_key'], $rs)) {
                        $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                    } else {
                        $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                    }
                }
                $attach_arr_list = $rs;
            }
        }
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        foreach ($items as &$item) {
            $item['operated_at'] = date('Y-m-d', strtotime($item['operated_at']));
            $item['status_name'] = static::$t[ChequeEnums::$checks_status[$item['status']]];
            $item['currency']    = $default_currency['symbol'];
            $item['attachments'] = !empty($attach_arr_list[$item['id']]) ? implode(',', $attach_arr_list[$item['id']]) : '';
            $item['bank_flow_at'] = !empty($item['bank_flow_at']) ? date('Y-m-d', strtotime($item['bank_flow_at'])) : '';
            $item['pay_status'] =  static::$t->_(ChequeEnums::$cheque_apply_detail_status[$item['pay_status']]);
        }
        return $items;
    }


    /**
     * 台账格式化数据
     * @Date  9/7/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleAccountItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['use_status_name']         = !empty($item['use_status']) ? static::$t[ChequeEnums::$checks_number_use_status[$item['use_status']]] : '';
            $item['exchange_status_name']    = !empty($item['exchange_status']) ? static::$t[ChequeEnums::$checks_exchange_status[$item['exchange_status']]] : '';
            $item['ticket_signing_end_date'] = !empty($item['ticket_signing_end_date']) ? $item['ticket_signing_end_date'] : '';
            $item['ticket_signing_date']     = !empty($item['ticket_signing_date']) ? $item['ticket_signing_date'] : '';
            $item['currency_name']           = static::$t[GlobalEnums::$currency_item[$item['currency']]];
            $item['release_status_name'] = static::$t[ChequeEnums::$cheque_account_release_arr[$item['release_status']]];
            $item['release_at'] = !empty($item['release_at']) ? date('Y-m-d', strtotime($item['release_at'])) : '';
            $item['exchange_date'] = $item['exchange_date'] ?? '';

        }
        return $items;
    }


    /**
     * 台账导出格式化数据
     * @Date  9/7/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleExportAccountItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        $attachments_ids = array_values(array_filter(array_column($items, 'record_id')));
        $attach_arr_list = [];
        if (!empty($attachments_ids)) {

            $attach_arr = AttachModel::find(
                [
                    'conditions' => ' oss_bucket_key in({oss_bucket_key:array}) and  oss_bucket_type in({oss_bucket_type:array})',
                    'bind'       => [
                        'oss_bucket_key'  => $attachments_ids,
                        'oss_bucket_type' => [Enums::OSS_REPLACE_INFO_TYPE_CHEQUE_APPLY_ADD, Enums::OSS_ABOLISH_INFO_TYPE_CHEQUE_APPLY_ADD],
                    ]
                ]
            )->toArray();

            if (!empty($attach_arr)) {
                $rs = [];
                foreach ($attach_arr as $item_att) {
                    if (in_array($item_att['oss_bucket_key'], $rs)) {
                        $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                    } else {
                        $rs[$item_att['oss_bucket_key']][] = !empty($item_att) ? gen_file_url($item_att) : '';
                    }
                }
                $attach_arr_list = $rs;
            }
        }

        $payment_check_data =[];
        $cheque_code_ids = array_values(array_column($items, 'id'));

        if (!empty($cheque_code_ids)) {
            $payment_check_data = $this->batchChequeAccountBusinessRel($cheque_code_ids);
        }
        $payment_data = false;
        foreach ($items as &$item) {
            $item['use_status_name']         = !empty($item['use_status']) ? static::$t[ChequeEnums::$checks_number_use_status[$item['use_status']]] : '';
            $item['exchange_status_name']    = !empty($item['exchange_status']) ? static::$t[ChequeEnums::$checks_exchange_status[$item['exchange_status']]] : '';
            $item['attachments']             = !empty($attach_arr_list[$item['record_id']]) ? implode(',', $attach_arr_list[$item['record_id']]) : '';
            $item['record_id']               = !empty($item['record_id']) ? $item['record_id'] : '';
            $item['ticket_signing_end_date'] = !empty($item['ticket_signing_end_date']) ? $item['ticket_signing_end_date'] : '';
            $item['last_cheque_code']        = (!empty($item['last_cheque_code']) && $item['type'] == ChequeEnums::CHECKS_NUMBER_TYPE_REPLACE) ? $item['last_cheque_code'] : '';
            $item['replace_reason']          = $item['type'] == ChequeEnums::CHECKS_NUMBER_TYPE_REPLACE ? $item['replace_reason'] : '';
            $item['abolish_reason']          = $item['type'] == ChequeEnums::CHECKS_NUMBER_TYPE_VOID ? $item['abolish_reason'] : '';
            $item['created_at']              = !empty($item['created_at']) ? date('Y-m-d', strtotime($item['created_at'])) : '';
            $item['release_at']              = !empty($item['release_at']) ? date('Y-m-d', strtotime($item['release_at'])) : '';
            $item['apply_created_at']        = !empty($item['apply_created_at']) ? date('Y-m-d', strtotime($item['apply_created_at'])) : '';
            $item['info_end_date']           = date('Y-m-d', (strtotime($item['info_end_date']) + 86400 * $item['valid_days']));
            if (!empty($payment_check_data) && in_array($item['id'], array_keys($payment_check_data))) {
                $payment_data = true;
            }
            $item['oa_biz_no']     = $payment_data ? $payment_check_data[$item['id']]['oa_biz_no'] : '';
            $item['payee_name']    = $payment_data ? $payment_check_data[$item['id']]['payee_name'] : '';
            $item['payee_account'] = $payment_data ? $payment_check_data[$item['id']]['payee_account'] : '';
            $item['supplier_no']   = $payment_data ? $payment_check_data[$item['id']]['supplier_no'] : '';
            $item['release_status_name'] = static::$t[ChequeEnums::$cheque_account_release_arr[$item['release_status']]];
        }

        return $items;
    }


    /**
     * 列表复合支票申请搜索条件 支票
     * @Date: 9/25/22 9:35 PM
     * @param object $builder 处理的对象
     * @param array $condition 查询条件
     * @param int $type 类型
     * @return  array
     * @author: peak pan
     **/
    private function getChequeCondition(object $builder, array $condition, int $type)
    {
        $apply_no            = $condition['apply_no'] ?? '';
        $apply_id            = $condition['apply_id'] ?? '';
        $status              = $condition['status'] ?? '';
        $start_date          = $condition['start_date'] ?? '';
        $end_date            = $condition['end_date'] ?? '';
        $start_approved_date = $condition['start_approved_date'] ?? '';
        $end_approved_date   = $condition['end_approved_date'] ?? '';
        $flag                = $condition['flag'] ?? GlobalEnums::AUDIT_TAB_PENDING;

        if ($type == ChequeEnums::LIST_TYPE_APPLY) {
            $builder->andWhere('main.apply_id = :uid:', ['uid' => $condition['uid']]);
        } else if ($type == ChequeEnums::LIST_TYPE_AUDIT) {
            //支票审核
            $builder = (new WorkflowServiceV2())->getBizWorkflowOrderListByProcessState($builder, $flag, [Enums::WF_CHEQUE_APPLY_BIZ_TYPE], $condition['uid'], 'main');
        } else if ($type == ChequeEnums::LIST_TYPE_DATA_EXPORT) {
            $builder->leftjoin(ChequeApplyDetailModel::class, 'cad.cheque_id = main.id', 'cad');
        } else if ($type == ChequeEnums::LIST_TYPE_CONSULTED_REPLY) {
            $biz_table_info = ['table_alias' => 'main'];
            $builder        = (new FYRService())->getBizOrderConsultationReplyListByProcessState($builder, $condition['is_reply'], [Enums::WF_CHEQUE_APPLY_BIZ_TYPE], $condition['uid'], $biz_table_info);
        }

        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }
        if (!empty($start_date) && !empty($end_date)) {
            $builder->betweenWhere('main.apply_date', $start_date, $end_date);
        }

        if (!empty($start_approved_date) && !empty($end_approved_date)) {
            $start_approved_date .= ' 00:00:00';
            $end_approved_date   .= ' 23:59:59';
            $builder->betweenWhere('main.approved_at ', $start_approved_date, $end_approved_date);
        }

        if (!empty($status)) {
            $builder->andWhere('main.status = :status:', ['status' => $status]);
        }

        if (!empty($apply_id)) {
            $builder->andWhere('(main.apply_id = :apply_id: or main.apply_name = :apply_id:)', ['apply_id' => $apply_id]);
        }

        return $builder;
    }


    /**
     * 列表复合台账搜索条件 支票
     * @Date: 9/25/22 9:35 PM
     * @param object $builder 处理的对象
     * @param array $condition 查询条件
     * @return  object
     * @author: peak pan
     **/
    private function getAccountCondition(object $builder, array $condition)
    {
        $cheque_code     = $condition['cheque_code'] ?? '';
        $apply_id        = $condition['apply_id'] ?? '';
        $use_status      = $condition['use_status'] ?? '';
        $exchange_status = $condition['exchange_status'] ?? '';
        $apply_no        = $condition['apply_no'] ?? '';
        $release_status = $condition['release_status'] ?? '';

        if ($condition['type'] == ChequeEnums::LIST_TYPE_DATA_EXPORT) {
            $builder->leftjoin(ChequeAccountRecordModel::class, 'car.account_id = main.id', 'car');
            $builder->leftjoin(ChequeApplyModel::class, 'ca.id = main.cheque_apply_id', 'ca');
            $builder->leftjoin(ChequeApplyDetailModel::class, 'cad.id = main.apply_detail_id', 'cad');
        }

        if (!empty($cheque_code)) {
            $builder->andWhere('main.cheque_code = :cheque_code:', ['cheque_code' => $cheque_code]);
        }

        if (!empty($apply_no)) {
            $builder->andWhere('main.apply_no = :apply_no:', ['apply_no' => $apply_no]);
        }

        if (!empty($use_status)) {
            $builder->andWhere('main.use_status = :use_status:', ['use_status' => $use_status]);
        }

        if (!empty($exchange_status)) {
            $builder->andWhere('main.exchange_status = :exchange_status:', ['exchange_status' => $exchange_status]);
        }

        if (!empty($release_status)) {
            $builder->andWhere('main.release_status = :release_status:', ['release_status' => $release_status]);
        }

        if (!empty($apply_id)) {
            $builder->andWhere('(main.apply_id = :apply_id: or main.apply_name = :apply_id:)', ['apply_id' => $apply_id]);
        }

        return $builder;
    }


    /**
     * 支票申请导出
     * @Date: 9/25/22 9:35 PM
     * @param array $params 查询条件
     * @return  mixed
     * @author: peak pan
     **/
    public function downloadCheque(array $params)
    {
        $export_data = $this->exportApplyData($params);
        $cheque_apply_arr = $export_data['data'] ?? [];
        return $this->getApplyExport($cheque_apply_arr);

    }

    /**
     * 台账导出
     * @Date: 9/25/22 9:35 PM
     * @param array $user 用户数据
     * @param array $params 查询条件
     * @return  mixed
     * @author: peak pan
     **/
    public function downloadAccount(array $params, array $user)
    {
        $export_data = $this->exportData($params, $user);
        $account_array = $export_data['data'] ?? [];
        return $this->getAccountExport($account_array);

    }


    /**
     * 获取详情
     * @Date: 9/25/22 9:35 PM
     * @param int $uid 用户数据
     * @param int $id 查询条件
     * @return array
     * @author: peak pan
     **/
    public function getDetail(int $id, int $uid = 0)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {

            $cheque_apply = ChequeApplyModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
                'columns'    => ['id', 'apply_no', 'apply_id', 'apply_name', 'apply_email', 'apply_company_id', 'apply_company_name', 'cost_node_department_id as apply_node_department_id', 'cost_node_department_name  as apply_node_department_name', 'reason']
            ]);

            if (empty($cheque_apply)) {
                throw new BusinessException('获取支票台账信息失败', ErrCode::$CHEQUE_GET_CHEQUE_APPLY_INFO_ERROR);
            }

            $data['cheque_apply'] = $cheque_apply->toArray();

            $attachments = AttachModel::find(
                [
                    'oss_bucket_key = :oss_bucket_key: and oss_bucket_type = :oss_bucket_type:',
                    'bind' => [
                        'oss_bucket_key'  => $id,
                        'oss_bucket_type' => Enums::OSS_PAYMENT_STORE_TYPE_CHEQUE_APPLY_ADD,
                    ]
                ]
            )->toArray();

            $data['cheque_apply']['attachments'] = !empty($attachments) ? $attachments : [];

            $apply_detail = ChequeApplyDetailModel::find([
                'conditions' => 'cheque_id = :cheque_id:',
                'bind'       => ['cheque_id' => $id],
                'columns'    => ['id', 'cheque_id', 'company_name', 'bank_id', 'bank_name', 'bank_account', 'quantity', 'reason', 'cheque_amount', 'currency', 'pay_status', 'bank_flow_at']
            ])->toArray();

            if (!empty($apply_detail)) {
                foreach ($apply_detail as &$detail) {
                    $detail['bank_flow_at']    = !empty($detail['bank_flow_at']) ? date('Y-m-d', strtotime($detail['bank_flow_at'])) : '';
                    $detail['pay_status_name'] = static::$t->_(ChequeEnums::$cheque_apply_detail_status[$detail['pay_status']]);

                }
            }

            $data['apply_detail'] = $apply_detail;

            $data['ask_id']    = [];
            $data['auth_logs'] = [];
            if (!empty($id)) {
                $req               = (new ChequeFlowService())->getRequest($id);
                if (!empty($req)) {
                    $data['auth_logs'] = (new WorkflowServiceV2())->getAuditLogs($req);
                }
                //待回复征询ID
                $ask            = (new FYRService())->getRequestToByReplyAsk($req, $uid);
                $data['ask_id'] = !empty($ask) ? $ask->id : '';
            }

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('支票-获取详情失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];


    }


    /**
     * 获取台账详情
     * @Date: 9/25/22 9:35 PM
     * @param array $param 查询条件
     * @return array
     * @author: peak pan
     **/
    public function getAccountDetail(array $param)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $replace = [];
            $abolish = [];
            $account = ChequeAccountModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $param['id']]
            ]);

            if (empty($account)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            $record = ChequeAccountRecordModel::findFirst([
                'conditions' => 'account_id = :account_id:',
                'bind'       => ['account_id' => $account->id]
            ]);

            $batch = ChequeBatchModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $account->batch_id]
            ]);

            $detail = ChequeApplyDetailModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $account->apply_detail_id]
            ]);
            if (empty($detail)) {
                throw new ValidationException(static::$t->_('cheque_detail_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            if (!empty($batch)) {
                //替换
                if (!empty($record)) {
                    $replace = AttachModel::find(
                        [
                            'conditions' => ' oss_bucket_key = :oss_bucket_key: and  oss_bucket_type = :oss_bucket_type:',
                            'bind'       => [
                                'oss_bucket_key'  => $record->id,
                                'oss_bucket_type' => Enums::OSS_REPLACE_INFO_TYPE_CHEQUE_APPLY_ADD,
                            ]
                        ]
                    )->toArray();
                    //作废
                    $abolish = AttachModel::find(
                        [
                            'conditions' => ' oss_bucket_key = :oss_bucket_key: and  oss_bucket_type = :oss_bucket_type:',
                            'bind'       => [
                                'oss_bucket_key'  => $record->id,
                                'oss_bucket_type' => Enums::OSS_ABOLISH_INFO_TYPE_CHEQUE_APPLY_ADD,
                            ]
                        ]
                    )->toArray();

                }

            }

            $data['basics_info'] = [
                'apply_no'     => $account->apply_no,
                'created_id'   => $account->created_id,
                'created_name' => $account->created_name,
                'created_at'   => date('Y-m-d', strtotime($account->created_at)),
            ];

            $data['apply_info']  = [
                'company_name' => $account->company_name,
                'bank_id'      => $account->bank_id,
                'bank_number'  => $account->bank_number,
                'bank_account' => $account->bank_account,
                'quantity'     => $account->quantity,
                'reason'       => $account->reason,
                'cheque_amount'   => $detail->cheque_amount ?? '',
                'pay_status'      => $detail->pay_status,
                'pay_status_name' => static::$t->_(ChequeEnums::$cheque_apply_detail_status[$detail->pay_status]),
                'bank_flow_id'    => $detail->bank_flow_id ?? '',
                'bank_flow_at'    => $detail->bank_flow_at ?? '',
            ];
            $data['cheque_info'] = [
                'start_number' => $batch->start_number,
                'quantity'     => $batch->quantity,
                'valid_days'   => $batch->valid_days,
                'expire_at'    => date('Y-m-d', strtotime(date('Y-m-d', strtotime($batch->created_at))) + $batch->valid_days * 86400)
            ];
            $data['use_info']    = [
                'cheque_code'     => $account->cheque_code,
                'use_status'      => static::$t[ChequeEnums::$checks_number_use_status[$account->use_status]],
                'exchange_status' => static::$t[ChequeEnums::$checks_exchange_status[$account->exchange_status]],
                'total_amount'    => $account->total_amount,
                'currency'        => static::$t[GlobalEnums::$currency_item[$account->currency]]
            ];

            if (!empty($record->id)) {
                if ($record->type == ChequeEnums::CHECKS_NUMBER_TYPE_VOID) {
                    $data['abolish_info'] = [
                        'account_id'       => $record->account_id,
                        'cheque_code'      => $record->cheque_code,
                        'last_account_id'  => $record->last_account_id,
                        'last_cheque_code' => $record->last_cheque_code,
                        'reason'           => $record->reason,
                        'created_at'       => date('Y-m-d', strtotime($record->created_at)),
                        'attachments'      => $abolish ?? []
                    ];
                }
                if ($record->type == ChequeEnums::CHECKS_NUMBER_TYPE_REPLACE) {
                    $data['replace_info'] = [
                        'account_id'       => $record->last_account_id,
                        'cheque_code'      => $record->last_cheque_code ?? '',
                        'last_account_id'  => $record->last_account_id,
                        'last_cheque_code' => $record->cheque_code ?? '',
                        'reason'           => $record->reason,
                        'created_at'       => date('Y-m-d', strtotime($record->created_at)),
                        'attachments'      => $replace ?? []
                    ];
                }
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('支票-台账-获取台账详情出错原因是:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 台账作废、替换
     * @Date: 9/25/22 9:35 PM
     * @param array $params 条件
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function accountEdit(array $params, array $user)
    {
        $code            = ErrCode::$SUCCESS;
        $message         = $real_message = '';
        $db              = $this->getDI()->get('db_oa');
        $db->begin();
        $log_title = '作废';
        try {
            $account = ChequeAccountModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            if (empty($account)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            if ($params['type'] == ChequeEnums::CHECKS_NUMBER_TYPE_VOID && !$this->getCountryView()) {
                $oa_biz_no_arr = ChequeAccountBusinessRelModel::find([
                    'columns'    => 'oa_biz_no',
                    'conditions' => 'cheque_account_id = :cheque_account_id:',
                    'bind'       => ['cheque_account_id' => $params['id']]
                ])->toArray();
                $oa_biz_ids    = array_values(array_unique(array_filter(array_column($oa_biz_no_arr, 'oa_biz_no'))));
                if (!empty($oa_biz_ids)) {
                    $payment = Payment::findFirst([
                        'conditions' => 'no in ({no:array}) and pay_status = :pay_status:',
                        'bind'       => ['no' => $oa_biz_ids, 'pay_status' => Enums::PAYMENT_PAY_STATUS_PAY],
                    ]);
                    if (!empty($payment)) {
                        throw new ValidationException(static::$t->_('cheque_account_cannot_voided'), ErrCode::$VALIDATE_ERROR);
                    }


                }

            }
            $current_time = date('Y-m-d H:i:s', time());

            if ($account->use_status != ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO && $account->exchange_status != ChequeEnums::CHECKS_EXCHANGE_STATUS_ING && !$this->getCountryView() ) {
                throw new ValidationException(static::$t->_('cheque_account_status_in_save'), ErrCode::$VALIDATE_ERROR);
            }

            $data['use_status']      = ChequeEnums::CHECKS_NUMBER_USE_STATUS_THREE;
            $data['exchange_status'] = ChequeEnums::CHECKS_EXCHANGE_STATUS_NO;
            $record_data['type']             = $params['type'];
            $record_data['account_id']       = $account->id;
            $record_data['cheque_code']      = $account->cheque_code;
            $record_data['last_account_id']  = $account->id;
            $record_data['last_cheque_code'] = $account->cheque_code;

            if ($params['type'] == ChequeEnums::CHECKS_NUMBER_TYPE_REPLACE) {
                $log_title = '替换';
                //开始替换
                $cheque_code_fly = ChequeAccountModel::findFirst([
                    'conditions' => 'cheque_code = :cheque_code:',
                    'bind'       => ['cheque_code' => $params['cheque_code']],
                ]);

                if (empty($cheque_code_fly)) {
                    throw new ValidationException(static::$t->_('cheque_not_empty'), ErrCode::$VALIDATE_ERROR);
                }

                if ($cheque_code_fly->use_status != ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE && $cheque_code_fly->exchange_status != ChequeEnums::CHECKS_EXCHANGE_STATUS_ING) {
                    throw new ValidationException(static::$t->_('cheque_account_status_in_save_err'), ErrCode::$VALIDATE_ERROR);
                }
                $cheque_code['use_status']              = ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO;
                $cheque_code['exchange_status']         = ChequeEnums::CHECKS_EXCHANGE_STATUS_ING;
                $cheque_code['total_amount']            = $account->total_amount;
                $cheque_code['ticket_signing_date']     = $account->ticket_signing_date;
                $cheque_code['ticket_signing_end_date'] = $account->ticket_signing_end_date;
                $cheque_code['updated_at']              = $current_time;
                $cheque_code_bool                       = $cheque_code_fly->i_update($cheque_code);
                if ($cheque_code_bool === false) {
                    throw new BusinessException('支票管理-支票台账(accountEdit) 替换的支票号修改失败' . json_encode(['save_data' => $data], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($cheque_code_fly) , ErrCode::$CHEQUE_ACCOUNT_EMPTY_DATA_ERROR);
                }

                $data['use_status']      = ChequeEnums::CHECKS_NUMBER_USE_STATUS_FOUR;
                $data['exchange_status'] = ChequeEnums::CHECKS_EXCHANGE_STATUS_NO;

                $cheque_account_record = ChequeAccountRecordModel::findFirst([
                    'columns'    => 'id,main_account_id,main_cheque_code',
                    'conditions' => ' last_account_id = :last_account_id:',
                    'bind'       => ['last_account_id' => $params['id']],
                    'order'      => 'id desc',
                    'limit'      => ChequeEnums::CHEQUE_LIMIT
                ]);
                if (!empty($cheque_account_record)) {
                    //如果原始被驳回不取上次的id
                    $cheque_account = ChequeAccountModel::findFirst([
                        'conditions' => ' id = :id:',
                        'bind'       => ['id' => $params['id']],
                    ]);

                    $cheque_account_business_rel = ChequeAccountBusinessRelModel::findFirst([
                        'conditions' => ' cheque_account_id = :cheque_account_id:',
                        'bind'       => ['cheque_account_id' => $params['id']],
                    ]);

                    if (!empty($cheque_account) && !empty($cheque_account_business_rel)) {
                        $cheque_account_record->main_account_id  = '';
                        $cheque_account_record->main_cheque_code = '';
                    }
                }
                $record_data['last_account_id']  = $cheque_code_fly->id;
                $record_data['last_cheque_code'] = $cheque_code_fly->cheque_code;

                $record_data['main_account_id']  = !empty($cheque_account_record->main_account_id) ? $cheque_account_record->main_account_id : $account->id;
                $record_data['main_cheque_code'] = !empty($cheque_account_record->main_cheque_code) ? $cheque_account_record->main_cheque_code : $account->cheque_code;

            }

            $data['updated_at'] = $current_time;
            $cheque_bool        = $account->i_update($data);

            if ($cheque_bool === false) {
                throw new BusinessException('支票管理-支票台账(accountEdit)' .$log_title. json_encode(['save_data' => $data], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($account), ErrCode::$CHEQUE_GET_ACCOUNT_EDIT_UPDATE_ERROR);
            }

            $record_data['reason']        = $params['reason'] ?? '';
            $record_data['operator_id']   = $user['id'];
            $record_data['operator_name'] = $user['name'];
            $record_data['created_at']    = $current_time;
            $record                       = new ChequeAccountRecordModel();
            $bool                         = $record->create($record_data);
            if ($bool === false) {
                throw new BusinessException('支票管理-支票台账-作废操作记录失败(accountEdit) = '.$log_title. json_encode(['data' => $record_data], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($record), ErrCode::$CHEQUE_GET_ACCOUNT_EDIT_SAVE_LOG_ERROR);
            }


            if (!empty($params['attachments'])) {
                $attach_bool = $this->addAttachments($params, ChequeEnums::$OSS_REPLACE_TYPE[$params['type']], $record->id);
                if ($attach_bool === false) {
                    throw new BusinessException('支票管理-支票台账-作废操作记录附件创建失败： ' . json_encode($params['attachment'], JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($attach_bool), ErrCode::$CHEQUE_GET_ACCOUNT_EDIT_SAVE_LOG_ATTACHMENTS_ERROR);
                }
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('台账作废、替换数据异常原因是:' .$log_title. $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];

    }

    /**
     * 添加
     * @Date: 9/25/22 9:35 PM
     * @param array $data 条件
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function addApply(array $data, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');
        $db->begin();
        try {

            //判断当前银行账户是否已经关闭
            $bank_account_array = array_values(array_unique(array_column($data['apply_detail'], 'bank_account')));
            if (!empty($bank_account_array)) {
                $bank_account = BankAccountModel::findFirst(
                    ['conditions' => 'account in ({account:array}) and is_deleted = :is_deleted:',
                     'bind'       => ['account' => $bank_account_array, 'is_deleted' => GlobalEnums::IS_DELETED]
                    ]
                );

                if (!empty($bank_account)) {
                    throw new ValidationException(static::$t->_('cheque_bank_account_is_deleted'), ErrCode::$CHEQUE_ACCOUNT_EMPTY_DATA_ERROR);
                }
            }
            $apply_data['apply_no']                  = $data['apply_no'];
            $apply_data['apply_id']                  = $data['apply_id'];
            $apply_data['apply_name']                = $data['apply_name'];
            $apply_data['status']                    = ChequeEnums::CHECKS_STATUS_ENUMS_ONE;
            $apply_data['use_status']                = ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_NO;
            $apply_data['apply_date']                = date('Y-m-d', time());
            $apply_data['apply_email']               = $data['apply_email'];
            $apply_data['apply_company_id']          = $data['apply_company_id'];
            $apply_data['apply_company_name']        = $data['apply_company_name'];
            $apply_data['cost_node_department_id']   = $data['apply_node_department_id'];
            $apply_data['cost_node_department_name'] = $data['apply_node_department_name'];
            $apply_data['reason']                    = $data['reason'] ?? '';
            $apply_data['created_at']                = date('Y-m-d H:i:s', time());
            $apply_data['updated_at']                = date('Y-m-d H:i:s', time());

            $cheque_apply_model = new ChequeApplyModel();
            if (!empty($data['id'])) {
                //重新提交
                $cheque_apply_model = $this->recommit($data, $apply_data);
            } else {
                $bool = $cheque_apply_model->create($apply_data);
                if ($bool === false) {
                    throw new BusinessException('支票-主表数据创建失败 = ' . json_encode(['apply_data' => $apply_data], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_model), ErrCode::$CHEQUE_CHEQUE_APPLY_ADD_ERROR);
                }
            }
            if (!empty($data['apply_detail'])) {
                $cheque_apply_detail_model = new ChequeApplyDetailModel();
                $cheque_apply_detail_arr   = $this->getFormatData($data, $cheque_apply_model, $user);
                $cheque_apply_detail_bool  = $cheque_apply_detail_model->batch_insert($cheque_apply_detail_arr);
                if ($cheque_apply_detail_bool === false) {
                    throw new BusinessException('支票详情(cheque_apply_detail)-数据创建失败： ' . json_encode($cheque_apply_detail_arr, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_detail_model), ErrCode::$CHEQUE_CHEQUE_APPLY_ADD_DETAIL_ERROR);
                }
            }

            $tmpList = [];
            if (!empty($data['attachments'])) {
                $attach_bool = $this->addAttachments($data, Enums::OSS_PAYMENT_STORE_TYPE_CHEQUE_APPLY_ADD, $cheque_apply_model->id);
                if ($attach_bool === false) {
                    throw new BusinessException('支票详情附件创建失败： ' . json_encode($tmpList, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($attach_bool), ErrCode::$CHEQUE_GET_ACCOUNT_EDIT_SAVE_LOG_ATTACHMENTS_ERROR);
                }
            }

            //注入审批流
            $flow_bool = (new ChequeFlowService)->createRequest($cheque_apply_model, $user);
            if ($flow_bool === false) {
                throw new BusinessException('支票创建-添加', ErrCode::$CHEQUE_CREATE_REQUEST_ADD_ERROR);
            }
            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if ($real_message) {
            $this->logger->warning('支票申请-添加数据:' . $real_message);
        }

        if (!empty($message)) {
            $db->rollback();
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? []
        ];
    }


    /**
     * 支票管理-数据查询-新增支票号
     * @Date: 9/25/22 9:35 PM
     * @param array $data 条件
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function addBatchApply(array $data, array $user)
    {
        $code         = ErrCode::$SUCCESS;
        $message      = $real_message = '';
        $current_time = date('Y-m-d H:i:s', time());
        $db           = $this->getDI()->get('db_oa');
        $db->begin();
        $redis         = $this->getDI()->get('redis');
        $detail_id_key = md5($data['detail_id']);

        try {

            $exist_key = $redis->get($detail_id_key);
            if (!empty($exist_key)) {
                throw new ValidationException(static::$t->_('cheque_not_added_repeatedly'), ErrCode::$VALIDATE_ERROR);
            } else {
                $redis->setex($detail_id_key, ChequeEnums::CHEQUE_REPEATED_SUBMISSION, json_encode(['key' => $detail_id_key, 'detail_id' => $data['detail_id']]));
            }

            $account_list = $this->getChequeAccount($data['cheque_detail']);

            $cheque_apply_detail = ChequeApplyDetailModel::findFirst(
                ['conditions' => 'id = :id:',
                 'bind'       => ['id' => $data['detail_id']]
                ]
            );

            if (!empty($cheque_apply_detail)) {
                if ($cheque_apply_detail->use_status == ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_YES) {
                    throw new ValidationException(static::$t->_('cheque_not_added_repeatedly'), ErrCode::$VALIDATE_ERROR);
                }
                $apply_detail['cheque_amount'] = $data['cheque_amount'];
                $apply_detail['use_status']    = ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_YES;
                $apply_detail['operated_at']   = $current_time;
                $apply_detail['updated_at']    = $current_time;
                $bool                          = $cheque_apply_detail->i_update($apply_detail);
                if ($bool === false) {
                    throw new BusinessException('支票-主表数据创建失败(cheque_apply_detail) = ' . json_encode(['apply_data' => $apply_detail], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_detail), ErrCode::$CHEQUE_CHEQUE_APPLY_DETAIL_ADD_ERROR);
                }
            }
            $cheque_account_model = new ChequeAccountModel();
            $apply_data           = [];
            foreach ($data['cheque_detail'] as $detail) {
                $apply_data['apply_id']        = $cheque_apply_detail->cheque_id;
                $apply_data['apply_detail_id'] = $data['detail_id'] ?? '';
                $apply_data['start_number']    = $detail['start_number'] ?? '';
                $apply_data['quantity']        = $detail['batch_quantity'] ?? '';
                $apply_data['valid_days']      = $detail['valid_days'] ?? '';
                $apply_data['created_id']      = $user['id'];
                $apply_data['created_name']    = $user['name'];
                $apply_data['created_at']      = $current_time;
                $apply_data['updated_at']      = $current_time;
                $cheque_batch_model            = new ChequeBatchModel();
                $bool                          = $cheque_batch_model->create($apply_data);
                if ($bool === false) {
                    throw new BusinessException('支票-主表数据创建失败(cheque_batch) = ' . json_encode(['data' => $apply_data], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($cheque_batch_model), ErrCode::$CHEQUE_APPLY_CHEQUE_BATCH_ADD_ERROR);
                }

                $account_model      = $this->getAccountFormatData($account_list, $data, $detail, $user, $cheque_batch_model);
                $account_model_bool = $cheque_account_model->batch_insert($account_model);

                if ($account_model_bool === false) {
                    throw new BusinessException('支票批量添加支票号-数据创建失败： ' . json_encode($account_model, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($cheque_account_model), ErrCode::$CHEQUE_APPLY_CHEQUE_ACCOUNT_ADD_ERROR);
                }
            }
            //修改cheque_apply_detail如果为全部已使用修改总状态已使用
             $this->saveChequeApplyDetail($cheque_apply_detail->cheque_id);


            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $redis->del($detail_id_key);
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('支票管理-数据查询-新增支票号:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $result ?? []
        ];
    }


    /**
     * 若支票全部被使用，则修改支票申请状态为已使用
     * @Date: 9/29/22 2:54 PM
     * @param int $cheque_id cheque_apply表主键id
     * @throws ValidationException
     * @throws BusinessException
     * @author: peak pan
     **/
    public function saveChequeApplyDetail($cheque_id)
    {
        $cheque_apply_detail_empty = ChequeApplyDetailModel::findFirst(
            ['conditions' => 'cheque_id = :cheque_id: and use_status = :use_status:',
             'bind'       => ['cheque_id' => $cheque_id, 'use_status' => ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_NO]
            ]
        );
        if (empty($cheque_apply_detail_empty)) {
            $cheque_apply_empty = ChequeApplyModel::findFirst(
                ['conditions' => 'id = :id:',
                 'bind'       => ['id' => $cheque_id]
                ]
            );
            if (empty($cheque_apply_empty)) {
                throw new ValidationException(static::$t->_('cheque_not_added_repeatedly'), ErrCode::$VALIDATE_ERROR);
            }
            $cheque_apply['use_status'] = ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_YES;
            $cheque_apply['updated_at'] = date('Y-m-d H:i:s', time());
            $cheque_apply_bool          = $cheque_apply_empty->i_update($cheque_apply);
            if ($cheque_apply_bool === false) {
                throw new BusinessException('支票-主表数据修改失败(save_cheque_apply_detail) = ' . json_encode(['apply_data' => $cheque_apply], JSON_UNESCAPED_UNICODE). ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_empty), ErrCode::$CHEQUE_CHEQUE_APPLY_DETAIL_ADD_ERROR);
            }
        }

    }

    /**
     * 处理支票号
     * @Date: 9/25/22 9:35 PM
     * @param array $data 条件
     * @return array
     * @throws ValidationException
     * @author: peak pan
     **/
    public function getChequeAccount($data)
    {
        $code_arr        = [];
        $cheque_code_arr = [];
        foreach ($data as $item) {
            if (strlen($item['start_number']) != ChequeEnums::START_NUMBER_COUNT) {
                throw new ValidationException(static::$t->_('cheque_add_batch_start_number_error'), ErrCode::$VALIDATE_ERROR);
            }
            $cheque_code                     = $this->getChequeCode($item);
            $code_arr[$item['start_number']] = $cheque_code;
            $cheque_code_arr                 = array_merge($cheque_code_arr, $cheque_code);
        }
        if (count($cheque_code_arr) != count(array_unique($cheque_code_arr))) {
            throw new ValidationException(static::$t->_('cheque_add_batch_start_number_last_repeat'), ErrCode::$VALIDATE_ERROR);
        }

        if ($this->chequeAccountRepeat(array_values(array_filter($cheque_code_arr)))) {
            throw new ValidationException(static::$t->_('cheque_add_batch_last_data_repeat'), ErrCode::$VALIDATE_ERROR);
        }
        return $code_arr;
    }

    /**
     * 查询支票号
     * @Date: 9/25/22 9:35 PM
     * @param array $cheque_code_arr 条件
     * @return bool
     * @author: peak pan
     **/
    public function chequeAccountRepeat($cheque_code_arr)
    {
        $item = ChequeAccountModel::find(
            [
                'conditions' => 'cheque_code IN ({cheque_code:array})',
                'bind'       => ['cheque_code' => $cheque_code_arr]
            ]
        )->toArray();
        if (!empty($item)) {
            return true;
        }
        return false;
    }


    /**
     * 生成支票号
     * @Date: 9/25/22 9:35 PM
     * @param array $data 条件
     * @return array
     * @throws ValidationException
     * @author: peak pan
     **/
    public function getChequeCode($data)
    {
        $start_num       = $data['start_number'];
        $quantity        = $data['batch_quantity'];
        $code_number     = range(ChequeEnums::CODE_NUMBER_START, $quantity);
        $cheque_code_arr = [];
        foreach ($code_number as $item) {
            $cheque_code = sprintf("%0" . strlen($start_num) . "d", bcadd($item, $start_num - ChequeEnums::CODE_NUMBER_START));
            if (strlen($cheque_code) != ChequeEnums::START_NUMBER_COUNT) {
                throw new ValidationException(static::$t->_('cheque_account_end_count_err'), ErrCode::$VALIDATE_ERROR);
            }
            $cheque_code_arr[] = $cheque_code;
        }
        return $cheque_code_arr;
    }


    /**
     * 数据格式化
     * @Date: 9/25/22 9:35 PM
     * @param array $data 数据封装
     * @param object $model 对象
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    private function getFormatData($data, $model, $user)
    {
        $default_currency = (new EnumsService())->getSysCurrencyInfo();
        $currency    = (int)$default_currency['code'];

        $data_item = [];
        foreach ($data['apply_detail'] as $k => $item) {
            $data_item[] = [
                'cheque_id'     => $model->id,
                'use_status'    => ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_NO,
                'company_name'  => $item['company_name'],
                'bank_id'       => $item['bank_id'],
                'bank_name'     => $item['bank_name'],
                'bank_account'  => $item['bank_account'],
                'quantity'      => (int)$item['quantity'],
                'reason'        => $item['reason'] ?? '',
                'cheque_amount' => $item['cheque_amount'] ?? '0.00',
                'currency'      => !empty($item['currency']) ? $item['currency'] : $currency,
                'operator_id'   => $user['id'],
                'operator_name' => $user['name'],
                'operated_at'   => date('Y-m-d H:i:s', time()),
                'created_at'    => date('Y-m-d H:i:s', time()),
                'updated_at'    => date('Y-m-d H:i:s', time()),
            ];
        }
        return $data_item;
    }


    /**
     * 数据格式化
     * @Date: 9/25/22 9:35 PM
     * @param array $account_list 数据处理
     * @param array $data 入参数据
     * @param array $detail 用户
     * @param array $user 用户
     * @param object $cheque_batch_model 对象数据
     * @return array
     * @author: peak pan
     **/
    private function getAccountFormatData(array $account_list, array $data, array $detail, array $user, object $cheque_batch_model)
    {
        $account = [];
        $init_value   = null;//初始值
        $total_amount = 0;//初始值
        $account_list_arr = $account_list[$detail['start_number']];
        foreach ($account_list_arr as $account_info) {
            $account[] = [
                'batch_id'                => $cheque_batch_model->id,
                'cheque_apply_id'         => $data['cheque_apply_id'],
                'apply_detail_id'         => $data['detail_id'],
                'cheque_code'             => $account_info,
                'use_status'              => ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE,
                'exchange_status'         => ChequeEnums::CHECKS_EXCHANGE_STATUS_ING,
                'ticket_signing_date'     => $init_value,//签票日期（支付模块 支付日期)
                'ticket_signing_end_date' => $init_value,//签票到期日(签票日期+有效天数)
                'exchange_date'           => $init_value,//承兑期日
                'currency'                => $data['currency'] ?? '',
                'apply_no'                => $data['apply_no'],//支票申请单号
                'apply_id'                => $data['apply_id'],//申请人工号
                'apply_name'              => $data['apply_name'],//申请人姓名
                'valid_days'              => $detail['valid_days'],//有效天数
                'total_amount'            => $total_amount,//支付模块的支票总额(oa申请单号回写金额 如果是多个支票 是金额的合计)
                'company_name'            => $data['company_name'],//购买公司
                'bank_id'                 => $data['bank_id'],//购买银行ID
                'bank_number'             => $data['bank_name'],//购买银行账号
                'bank_account'            => $data['bank_account'],//购买银行账号
                'quantity'                => $data['quantity'],//购买张数
                'reason'                  => $data['reason'],
                'oa_biz_no'               => '',//oa申请单号
                'oa_type'                 => $init_value,//业务模块
                'payee_name'              => '',//收款人姓名
                'payee_account'           => '',//收款人账户
                'supplier_no'             => '',//供应商号
                'use_time'                => date('Y-m-d H:i:s', time()),
                'created_id'              => $user['id'],//创建人ID
                'created_name'            => $user['name'],//创建人名称
                'created_at'              => date('Y-m-d H:i:s', time()),
                'updated_at'              => date('Y-m-d H:i:s', time())
            ];
        }
        return $account;
    }


    /**
     * 批量添加附件
     * @Date: 9/25/22 9:35 PM
     * @param array $data 入参数据
     * @param string $oss_bucket_key
     * @param string $oss_bucket_type
     * @return array
     * @author: peak pan
     **/
    public function addAttachments($data, $oss_bucket_type, $oss_bucket_key)
    {
            $attach  = new AttachModel();
            $tmpList = [];
            foreach ($data['attachments'] as $attachment) {
                $tmp                    = [];
                $tmp['oss_bucket_type'] = $oss_bucket_type;
                $tmp['oss_bucket_key']  = $oss_bucket_key;
                $tmp['sub_type']        = ChequeEnums::EXPORT_TYPE_ZERO;
                $tmp['bucket_name']     = $attachment['bucket_name'];
                $tmp['object_key']      = $attachment['object_key'];
                $tmp['file_name']       = $attachment['file_name'];
                $tmpList[]              = $tmp;
            }
            $attach_bool = $attach->batchInsert($tmpList);
            return $attach_bool;
    }


    /**
     * 枚举
     * @return array
     */
    public function getCreatePageBaseInfoDefaultData()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {

            $checks_status = ChequeEnums::$checks_status_arr;
            foreach ($checks_status as &$item_) {
                $item_['value'] = (string)$item_['value'];
                $item_['label'] = static::$t->_($item_['label']);
            }
            $data['cheque_apply_status'] = $checks_status;


            $use_status = ChequeEnums::$checks_use_status_arr;
            foreach ($use_status as &$item_us_status) {
                $item_us_status['value'] = (string)$item_us_status['value'];
                $item_us_status['label'] = static::$t->_($item_us_status['label']);
            }
            $data['use_status'] = $use_status;


            $cheque_account_use_status = ChequeEnums::$checks_number_use_status_arr;

            foreach ($cheque_account_use_status as &$item_cheque_account) {
                $item_cheque_account['value'] = (string)$item_cheque_account['value'];
                $item_cheque_account['label'] = static::$t->_($item_cheque_account['label']);
            }
            $data['cheque_account_use_status'] = $cheque_account_use_status;


            //承兑状态 1待承兑 2已承兑 3未承兑
            $cheque_exchange_status = ChequeEnums::$checks_exchange_status_arr;
            foreach ($cheque_exchange_status as &$item_status) {
                $item_status['value'] = (string)$item_status['value'];
                $item_status['label'] = static::$t->_($item_status['label']);
            }
            $data['cheque_exchange_status'] = $cheque_exchange_status;

            //1 作废 2 替换
            $cheque_number_type = ChequeEnums::$checks_number_type_arr;
            foreach ($cheque_number_type as &$item_type) {
                $item_type['value'] = (string)$item_type['value'];
                $item_type['label'] = static::$t->_($item_type['label']);
            }
            $data['cheque_number_type'] = $cheque_number_type;

            //支票释放
            $release_arr = [];
            $cheque_account_release_arr = ChequeEnums::$cheque_account_release_arr;
            foreach ($cheque_account_release_arr as $key=>$release) {
                $res['value'] = (string)$key;
                $res['label'] = static::$t->_($release);
                $release_arr[] = $res;
            }
            $data['cheque_account_release_arr'] = $release_arr;

            $data['cost_company_list'] = (new PurchaseService())->getCooCostCompany();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning(' 支票-枚举获取值异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    /**
     * 申请单导出
     * @Date: 9/25/22 9:35 PM
     * @param array $condition 条件
     * @return array
     * @author: peak pan
     **/
    public function exportApplyData(array $condition)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $row_values  = [[]];
        try {
            $limit_size            = ChequeEnums::CHECKS_DOWNLOAD_LIMIT;
            $condition['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = $limit_size;
            $data_count            = $this->getExportListCount($condition);
            if ($data_count > $limit_size) {

                throw new ValidationException(static::$t->_('export_data_download_limit_max'), ErrCode::$VALIDATE_ERROR);
            }
            $list        = $this->getExportList($condition, ChequeEnums::LIST_TYPE_DATA_EXPORT);
            $total_count = $list['data']['pagination']['total_count'];
            if ($total_count) {
                $row_values = $list['data']['items'];
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票申请导出:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $row_values,
        ];

    }

    /**
     * 台账数据处理
     * @Date: 9/25/22 9:35 PM
     * @param array $condition 条件
     * @param array $user 用户信息
     * @return array
     * @author: peak pan
     **/
    public function exportData(array $condition, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $row_values  = [[]];
        try {
            $limit_size            = ChequeEnums::CHECKS_DOWNLOAD_LIMIT;
            $condition['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = $limit_size;
            $condition['type']     = ChequeEnums::LIST_TYPE_DATA_EXPORT;

            $data_count = $this->getExportAccountListCount($condition, $user['id']);
            if ($data_count > $limit_size) {
                throw new ValidationException(static::$t->_('export_data_download_limit_max'), ErrCode::$VALIDATE_ERROR);
            }

            $list = $this->getExportAccountList($condition, $user['id']);

            $total_count = $list['data']['pagination']['total_count'];
            if ($total_count) {
                $row_values = $list['data']['items'];
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票台账导出:' . $message );
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $row_values,
        ];

    }

    /**
     * 导出数据到xls
     * @Date: 10/15/22 11:04 PM
     * @param array $row_values 数据
     * @return array
     * ValidationException
     * @author: peak pan
     **/
    public function getApplyExport(array $row_values)
    {
        $is_view = $this->getCountryView();
       try {
            $apply_export_arr = [];
            foreach ($row_values as $value) {
                $apply_export_arr[] = [
                    $value['apply_no'],//申请单号
                    $value['apply_id'],//申请人工号
                    $value['apply_name'],//申请人姓名
                    $value['apply_email'],
                    $value['cost_node_department_name'],
                    $value['apply_company_name'],
                    $value['reason'],
                    $value['attachments'],
                    $value['status_name'],
                    $value['cheque_apply_id'],
                    $value['company_name'],
                    $value['bank_name'],
                    $value['bank_account'],
                    $value['quantity'],
                    $value['detail_reason'],
                    $value['operated_at'],
                    $value['cheque_amount'],
                    $value['currency'],
                    $is_view ? $value['pay_status'] : '',
                    $is_view ? $value['bank_flow_at'] : '',
                    $value['detail_id'],
                ];
            }
            $header            = [
                static::$t->_('cheque_account_apply_no'), //支票购买申请单号
                static::$t->_('cheque_apply_id'), //申请人工号
                static::$t->_('cheque_apply_name'), //申请人姓名
                static::$t->_('cheque_apply_email'), //申请人邮箱
                static::$t->_('cheque_cost_node_department_name'), //申请人所属部门
                static::$t->_('cheque_apply_company_name'), //申请人所属公司
                static::$t->_('cheque_account_reason'), //申请原因
                static::$t->_('cheque_account_attachments'), //附件
                static::$t->_('cheque_status'), //申请状态
                static::$t->_('cheque_cheque_apply_id'), //行ID
                static::$t->_('cheque_account_company_name'), //购买公司
                static::$t->_('cheque_account_bank_number'), //购买银行
                static::$t->_('cheque_account_bank_account'), //银行账号
                static::$t->_('cheque_account_quantity'), //购买张数
                static::$t->_('cheque_apply_detail_reason'), //支票申请明细申请原因
                static::$t->_('cheque_operated_at'), //操作时间
                static::$t->_('cheque_cheque_amount'),//支票费用
                static::$t->_('cheque_currency'),//币种
                $is_view ? static::$t->_('cheque_pay_status') : '', //支付状态
                $is_view ? static::$t->_('cheque_bank_flow_at') : '', //流水日期
                static::$t->_('cheque_apply_detail_id'),//工本费行号
            ];
            $file_name         = 'cheque_export_' . date('YmdHis');
            $result            = $this->exportExcel($header, $apply_export_arr, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];

            return $result;
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票申请带数据封装导出:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }


    /**
     * 导出数据到xls
     * @param array $row_values 数据
     * @return array
     */
    public function getAccountExport(array $row_values)
    {
        $country = get_country_code();
        $is_ph_view = $country == GlobalEnums::PH_COUNTRY_CODE ? true : false ;
        $is_my_view = $country == GlobalEnums::MY_COUNTRY_CODE ? true : false ;
        try {
            $account_export_arr = [];
            $middle = $header_middle = [];
            foreach ($row_values as $value) {

                if ($is_ph_view) {
                    $middle = [
                        $value['release_status_name'] ?? '',//释放状态
                        $value['release_at'] ?? '',//释放日期
                        $value['exchange_date'] ?? '',//承兑日期
                    ];
                } elseif ($is_my_view) {
                    $middle = [
                        $value['exchange_date'] ?? '',//承兑日期
                    ];
                }
                $account_export_arr[] = array_merge(
                    [
                    $value['cheque_code'],//支票号
                    $value['valid_days'],//有效期天数
                    $value['info_end_date'],//到期日
                    $value['use_status_name'],//使用状态
                    $value['exchange_status_name'],//承兑状态
                    $value['total_amount'],//支票金额
                    ],
                    $middle,
                    [
                    $value['last_cheque_code'],//替换支票号
                    $value['replace_reason'],//替换原因
                    $value['abolish_reason'],//作废原因
                    $value['attachments'],//附件是作废的或是替换的附件
                    $value['created_at'],//操作时间
                    $value['apply_no'],//支票购买申请单号
                    $value['apply_id'],//操作人工号
                    $value['apply_name'],//操作人姓名
                    $value['apply_created_at'],//操作时间
                    $value['apply_key_id'],//序号
                    $value['company_name'],//购买公司
                    $value['bank_name'],//购买银行
                    $value['bank_account'],//银行账号
                    $value['quantity'],//购买张数
                    $value['reason'],//申请原因
                    $value['oa_biz_no'],//OA申请单号
                    $value['payee_name'],//收款人姓名
                    $value['payee_account'],//收款人账户
                    $value['supplier_no'],//供应商号
                ]);
            }
            if ($is_ph_view) {
                $header_middle = [
                    static::$t->_('cheque_account_release_status') ?? '', //释放状态
                    static::$t->_('cheque_account_release_at') ?? '', //释放日期
                    static::$t->_('cheque_account_exchange_date') ?? '', //承兑日期
                ];
            } elseif ($is_my_view) {
                $header_middle = [
                    static::$t->_('cheque_account_exchange_date') ?? '', //承兑日期
                ];
            }

            $header            = array_merge(
                [static::$t->_('cheque_account_cheque_code'), //支票号
                static::$t->_('cheque_account_valid_days'), //有效天数
                static::$t->_('cheque_account_ticket_signing_end_date'), //到期日
                static::$t->_('cheque_account_use_status'), //使用状态
                static::$t->_('cheque_account_exchange_status'), //承兑状态
                static::$t->_('cheque_account_total_amount')],
                $header_middle,
                [static::$t->_('cheque_account_replace_cheque_code'), //替换支票号
                static::$t->_('cheque_account_replace_reason'), //替换原因
                static::$t->_('cheque_account_abolish_reason'), //作废原因
                static::$t->_('cheque_account_attachments'), //附件
                static::$t->_('cheque_account_created_at'), //操作时间
                static::$t->_('cheque_account_apply_no'), //支票购买申请单号
                static::$t->_('cheque_account_created_id'), //操作人工号
                static::$t->_('cheque_account_created_name'), //操作人姓名
                static::$t->_('cheque_account_created_at'), //操作时间
                static::$t->_('cheque_account_id'), //序号
                static::$t->_('cheque_account_company_name'), //购买公司
                static::$t->_('cheque_account_bank_number'), //购买银行
                static::$t->_('cheque_account_bank_account'), //银行账号
                static::$t->_('cheque_account_quantity'), //购买张数
                static::$t->_('cheque_account_reason'), //申请原因
                static::$t->_('cheque_account_oa_biz_no'), //OA申请单号
                static::$t->_('cheque_account_payee_name'), //收款人姓名
                static::$t->_('cheque_account_payee_account'), //收款人账户
                static::$t->_('cheque_account_supplier_no'), //供应商号
            ]);
            $file_name         = 'cheque_account_export_' . date('YmdHis', time());
            $result            = $this->exportExcel($header, $account_export_arr, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];

            return $result;
        }  catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票台账带数据封装导出:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }


    /**
     * 分页处理
     * @Date  9/7/22 12:56 PM
     * @param array $condition 查询器对象
     * @return  array
     * @author: peak pan
     */
    public function getAccountList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => ChequeEnums::TOTAL_COUNT,
            ],
        ];
        try {
            $builder = $this->modelsManager->createBuilder();

            $columns = [
                'main.id',
                'main.cheque_code',
                'main.apply_no',
                'main.use_status',
                'main.exchange_status',
                'main.ticket_signing_date',
                'main.ticket_signing_end_date',
                'main.total_amount',
                'main.currency',
                'main.exchange_date',
                'main.release_status',
                'main.release_at',
            ];
            $builder->from(['main' => ChequeAccountModel::class]);
            //组合搜索条件
            $builder = $this->getAccountCondition($builder, $condition);

            $builder->orderBy('main.id DESC');
            $count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                $builder->columns($columns);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleAccountItems($items, $condition);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票列表获取信息:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 分页处理
     * @Date  9/7/22 12:56 PM
     * @param array $condition 查询器对象
     * @return  array
     * @author: peak pan
     */
    public function getExportAccountList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => ChequeEnums::TOTAL_COUNT,
            ],
        ];
        try {
            $builder = $this->modelsManager->createBuilder();
            $columns = [
                'main.id',
                'car.id as record_id',
                'main.cheque_code',
                'main.valid_days',
                'main.created_at as info_end_date',
                'main.use_status',
                'main.exchange_status',
                'car.cheque_code as last_cheque_code',
                'car.reason as replace_reason',
                'car.reason as abolish_reason',
                'car.created_at',
                'car.type',
                'main.apply_no',
                'main.apply_id',
                'main.apply_name',
                'cad.created_at as apply_created_at',
                'cad.id as apply_key_id',
                'main.company_name',
                'main.bank_number as bank_name',
                'main.bank_account',
                'main.quantity',
                'main.reason',
                'main.oa_biz_no',
                'main.payee_name',
                'main.payee_account',
                'main.supplier_no',
                'main.total_amount',
                'main.exchange_date',
                'main.release_status',
                'main.release_at',
            ];
            $builder->from(['main' => ChequeAccountModel::class]);
            //组合搜索条件
            $builder = $this->getAccountCondition($builder, $condition);

            $builder->orderBy('main.id DESC');
            $count = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;

            if ($count) {
                $builder->columns($columns);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleExportAccountItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('支票导出列表异常信息:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 支票管理 -我的支付-支票号查找
     * @param array $params 条件查询
     * @return array
     */
    public function getPaySearchAccount($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $account = ChequeAccountModel::find([
            'columns'    => 'id,cheque_code,currency,bank_number',
            'conditions' => 'use_status = :use_status: and  currency = :currency: and cheque_code like :cheque_code: ',
            'bind'       => ['cheque_code' => $params['cheque_code'] . '%', 'use_status' => ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE, 'currency' => $params['currency']]
        ])->toArray();


        if (!empty($account)) {
            foreach ($account as &$item) {
                $item['currency'] = static::$t[GlobalEnums::$currency_item[$item['currency']]];
            }
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $account,
        ];

    }

    /**
     * 替换搜索支票号
     * @param array $params 条件查询
     * @return array
     */
    public function getSearchAccount($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $account = ChequeAccountModel::find([
            'columns'    => 'id,cheque_code',
            'conditions' => 'use_status = :use_status: and cheque_code like :cheque_code: ',
            'bind'       => ['cheque_code' => $params['cheque_code'] . '%', 'use_status' => ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE]
        ])->toArray();

        if (!empty($account)) {
            foreach ($account as &$item) {
                $item['id'] = $item['cheque_code'];
            }
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $account,
        ];

    }


    /**
     * 根据申请号查询数据
     * @param array $params 条件查询
     * @param array $user 用户信息
     * @return array
     */
    public function getSearchApply($params, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [];
        $cheque_apply = ChequeApplyModel::find([
            'columns'    => 'id,apply_no,apply_id,apply_name',
            'conditions' => 'status = :status: and use_status = :use_status: and apply_no like :apply_no: ',
            'bind'       => ['apply_no' => $params['apply_no'] . '%', 'status' => ChequeEnums::CHECKS_STATUS_ENUMS_THREE, 'use_status' => ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_NO]
        ])->toArray();

        if (!empty($cheque_apply)) {
            $cheque_apply_ids   = array_column($cheque_apply, 'id');
            $apply_detail = ChequeApplyDetailModel::find([
                'columns'    => 'id,cheque_id,company_name,bank_id,bank_name,bank_account,quantity,reason,cheque_amount,currency',
                'conditions' => 'use_status = :use_status: and cheque_id in ({cheque_id:array}) ',
                'bind'       => ['cheque_id' => $cheque_apply_ids, 'use_status' => ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_NO]
            ])->toArray();

            if (!empty($apply_detail)) {
                $res              = [];
                $item_detail      = [];
                $default_currency = (new EnumsService())->getSysCurrencyInfo();
                foreach ($apply_detail as $value) {
                    $value['currency_name']     = $default_currency['symbol'];
                    $value['detail_id']         = $value['id'];
                    $value['cheque_apply_id']   = $value['cheque_id'];
                    $value['cheque_amount']     = empty($value['cheque_amount']) || $value['cheque_amount'] == '0.00' ? '' : $value['cheque_amount'];
                    $res[$value['cheque_id']][] = $value;
                }
                foreach ($cheque_apply as $item) {
                    if (empty($res[$item['id']])) {
                        continue;
                    }
                    $item_detail['apply_no']     = $item['apply_no'];
                    $item_detail['apply_id']     = $user['id'];
                    $item_detail['apply_name']   = $user['name'];
                    $item_detail['apply_detail'] = $res[$item['id']];
                    $data[]                      = $item_detail;
                }
            }
        }
        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     *  支票台账表单展示-关联申请单号
     * @param array $params 条件
     * @return array
     */
    public function getSearchApplyList($params)
    {
        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $account = ChequeApplyModel::find([
            'columns'    => 'id,apply_no',
            'conditions' => ' apply_no like :apply_no: ',
            'bind'       => ['apply_no' => $params['apply_no'] . '%']
        ])->toArray();

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $account,
        ];
    }


    /**
     * 支票使用修改
     * @Date: 9/28/22 12:24 PM
     * @param string $cheque_code 当前支票号
     * @param array $check_code_arr    业务模块数据，支付详情一个支票号可以重复
     * @param array $cheque_account_list    支票号对应的台账数据
     * @param object  $db          对象
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     */
    public function saveChequeAccount(string $cheque_code, array $check_code_arr, array $cheque_account_list, $db)
    {
        $current_check_code = $check_code_arr[0];
        // 比对状态
        if ($cheque_account_list[$cheque_code]['use_status'] != ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE) {
            throw new ValidationException(static::$t->_('cheque_status_not_err'), ErrCode::$VALIDATE_ERROR);
        }
        // 比对币种
        if ($cheque_account_list[$cheque_code]['currency'] != $current_check_code['currency']) {
            throw new ValidationException(static::$t->_('cheque_currency_not_agreement'), ErrCode::$VALIDATE_ERROR);
        }

        $current_time = date('Y-m-d H:i:s', time());

        // 修改台账 ，不管支付模块使用几次一个支票号，这里只会修改这个对应的支票，但是金额是相加的
        $bool = $db->updateAsDict(
            (new ChequeAccountModel)->getSource(),
            [
                'use_status'              => ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO,
                'ticket_signing_date'     => $current_check_code['check_date'],//签票日期
                'ticket_signing_end_date' => date('Y-m-d', strtotime($current_check_code['check_date']) + $cheque_account_list[$cheque_code]['valid_days'] * 86400),//签票到期日
                'exchange_date'           => $current_check_code['date'],//承兑期日
                'total_amount'            => bc_add_batch(array_column($check_code_arr, 'amount'), 2),//支付模块的支票总额
                'oa_biz_no'               => '',//oa申请单号
                'oa_type'                 => 0,//业务模块
                'payee_name'              => $current_check_code['payee_name'] ?? '',//收款人姓名
                'payee_account'           => $current_check_code['payee_account'] ?? '',//收款人账户
                'supplier_no'             => '',//供应商号
                'use_time'                => $current_time,
                'updated_at'              => $current_time,
            ],
            [
                'conditions' => ' cheque_code = ? ',
                'bind'       => [$cheque_code]
            ]
        );
        if ($bool === false) {
            throw new BusinessException('支票-支付管理-我的支付-修改台账支票号失败' . json_encode(['current_check_code' => $current_check_code, 'cheque_code' => $cheque_code], JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_ACCOUNT_UPDATE_ERROR);
        }
    }


    /**
     * 第三支付人选择 是否支付=否/第二级支付人驳回给第一支付/申请人撤回
     * @Date: 9/28/22 12:24 PM
     * @param int $payment_id payment主键id
     * @param object $db 数据对象
     * @return array
     * @author: peak pan
     **/

    public function revokeChequeAccount(int $payment_id, object $db)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        try {
            $payment_check_obj = PaymentCheck::find([
                'conditions' => ' payment_id = :payment_id: and  cheque_account_id > :cheque_account_id: and  is_deleted = :is_deleted:',
                'bind'       => ['payment_id' => $payment_id, 'cheque_account_id' => ChequeEnums::CHEQUE_ACCOUNT_ID, 'is_deleted' => GlobalEnums::IS_NO_DELETED]
            ])->toArray();

            if (!empty($payment_check_obj)) {
                //批量修改
                $cheque_account_ids   = array_values(array_filter(array_unique(array_column($payment_check_obj, 'cheque_account_id'))));
                if (!empty($cheque_account_ids)) {
                    $cheque_account_array = ChequeAccountModel::find([
                        'conditions' => ' id in ({id:array})',
                        'bind'       => ['id' => $cheque_account_ids]
                    ])->toArray();

                    $payment_check_ids = array_values(array_unique(array_filter(array_column($payment_check_obj, 'id'))));
                    if (!empty($payment_check_ids)) {
                        $account_business_rel = ChequeAccountBusinessRelModel::find([
                            'conditions' => 'payment_id =:payment_id: and  payment_check_id in ({payment_check_id:array})',
                            'bind'       => ['payment_check_id' => $payment_check_ids, 'payment_id' => $payment_id]
                        ]);
                        $account_business_rel_arr = $account_business_rel->toArray();

                        if (!empty($account_business_rel_arr)) {
                            $this->logger->info('支付管理-我的支付-待处理-驳回  关联关系清空log' . json_encode($account_business_rel_arr, JSON_UNESCAPED_UNICODE));
                            $account_business_rel_del = $account_business_rel->delete();
                            if ($account_business_rel_del === false) {
                                throw new BusinessException('支付管理-我的支付-待处理-驳回 关系删除失败 ;' . json_encode($payment_check_ids, JSON_UNESCAPED_UNICODE) . ' ;可能的原因是: ' . get_data_object_error_msg($account_business_rel), ErrCode::$CHEQUE_DEL_CHEQUE_ACCOUNT_BUSINESS_REL_ERROR);
                            }
                        }
                    }
                }
                $cheque_account_abolish = [];
                $cheque_account_replace = [];
                $account_ids            = [];
                if (!empty($cheque_account_array)) {
                    foreach ($cheque_account_array as $key => $cheque_account_item) {
                        if ($cheque_account_item['use_status'] == ChequeEnums::CHECKS_NUMBER_USE_STATUS_FOUR) {
                            $cheque_account_abolish[$key] = $cheque_account_item['id'];
                        } else if ($cheque_account_item['use_status'] == ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO) {
                            $cheque_account_replace[$key] = $cheque_account_item['id'];
                        }
                    }
                    if (!empty($cheque_account_abolish)) {

                        $max_account_ids = ChequeAccountRecordModel::find([
                            'columns'    => 'max(id) as id',
                            'conditions' => ' main_account_id in ({main_account_id:array})',
                            'bind'       => ['main_account_id' => array_values($cheque_account_abolish)],
                            'group'      => ['main_account_id']
                        ])->toArray();

                        if (!empty($max_account_ids)) {
                            $save_account_ids = ChequeAccountRecordModel::find([
                                'columns'    => 'last_account_id',
                                'conditions' => ' id in ({id:array})',
                                'bind'       => ['id' => array_values(array_unique(array_column($max_account_ids, 'id')))],
                            ])->toArray();

                            if (!empty($save_account_ids)) {
                                $cheque_account_last = ChequeAccountModel::find([
                                    'conditions' => ' id in ({id:array}) and use_status = :use_status:',
                                    'bind'       => ['id' => array_values(array_unique(array_column($save_account_ids, 'last_account_id'))), 'use_status' => ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO]
                                ])->toArray();

                                if ($cheque_account_last) {
                                    $account_ids = array_values(array_unique(array_column($cheque_account_last, 'id')));
                                }
                            }
                        }
                    }
                }

                $ids = implode(',', array_unique(array_filter(array_merge($cheque_account_replace, $account_ids))));
                if (!empty($ids)) {
                    $init_value   = null;//初始值
                    $total_amount = 0;//初始值
                    $current_time          = date('Y-m-d H:i:s', time());
                    $update_cheque_account = $db->updateAsDict(
                        (new ChequeAccountModel)->getSource(),
                        [
                            'use_status'              => ChequeEnums::CHECKS_NUMBER_USE_STATUS_ONE,
                            'ticket_signing_date'     => $init_value,//签票日期
                            'ticket_signing_end_date' => $init_value,//签票到期日
                            'exchange_date'           => $init_value,//承兑期日
                            'total_amount'            => $total_amount,//支付模块的支票总额
                            'oa_biz_no'               => '',//oa申请单号
                            'oa_type'                 => $init_value,//业务模块
                            'payee_name'              => '',//收款人姓名
                            'payee_account'           => '',//收款人账户
                            'supplier_no'             => '',//供应商号
                            'use_time'                => $current_time,
                            'updated_at'              => $current_time
                        ],
                        [
                            'conditions' => " id IN ($ids)",
                        ]

                    );

                    if (!$update_cheque_account) {
                        throw new BusinessException('更新支票号失败', ErrCode::$CHEQUE_ACCOUNT_UPDATE_ERROR);
                    }

                }
            }
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('第三支付人选择 是否支付=否/第二级支付人驳回给第一支付/申请人撤回 数据异常:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }


    /**
     * 重新提交
     *
     * @Date: 10/22/22 3:15 PM
     * @param array $data 提交的数据
     * @param array $apply_data 需要修改的数据
     * @return object
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit(array $data, array $apply_data)
    {

        $cheque_apply_model       = ChequeApplyModel::findFirst(
            ['conditions' => 'id = :id:',
             'bind'       => ['id' => $data['id']]
            ]
        );

        if (empty($cheque_apply_model)) {
            throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
        }

        $req = (new ChequeFlowService())->getRequest($data['id']);

        if (empty($req)) {
            throw new BusinessException('支票-没有找到审批流信息' . $data['id'], ErrCode::$CHEQUE_FLOW_SERVICE_WORKFLOW_REQUEST_ERROR);
        }


        $bool = $cheque_apply_model->i_update($apply_data);
        if ($bool === false) {
            throw new BusinessException('支票-重新提交-修改数据失败' . json_encode(['apply_data' => $apply_data], JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($cheque_apply_model), ErrCode::$CHEQUE_CHEQUE_APPLY_ADD_ERROR);
        }

        $cheque_apply_obj = ChequeApplyDetailModel::find(
            [
                'conditions' => 'cheque_id = :cheque_id:',
                'bind'       => ['cheque_id' => $data['id']],
            ]
        );
        if (!empty($cheque_apply_obj->toArray())) {
            $cheque_apply_del = $cheque_apply_obj->delete();
            if ($cheque_apply_del === false) {
                throw new BusinessException('删除之前的申请失败 ;' . $data['id'] . ' ;可能的原因是: ' . get_data_object_error_msg($cheque_apply_obj), ErrCode::$CHEQUE_FLOW_SERVICE_WORKFLOW_REQUEST_ERROR);
            }
        }

        //如果再次提交删除之前的附件
        $old_attach_model = AttachModel::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key:',
            'bind'       => ['oss_bucket_type' => Enums::OSS_PAYMENT_STORE_TYPE_CHEQUE_APPLY_ADD,
                             'oss_bucket_key'  => $data['id']]
        ]);
        if (!empty($old_attach_model->toArray())) {
            $bool_attach_del = $old_attach_model->delete();
            if ($bool_attach_del === false) {
                throw new BusinessException('支票-再次提交删除之前的附件失败 ;' . $data['id']. ' ;可能的原因是: ' . get_data_object_error_msg($old_attach_model), ErrCode::$CHEQUE_FLOW_SERVICE_WORKFLOW_REQUEST_ERROR);
            }
        }

        $req->is_abandon = GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->updated_at = date('Y-m-d H:i:s', time());
        if ($req->save() === false) {
            throw new BusinessException('支票-从新提交关闭原审批流失败 ;' . $data['id']. ';可能的原因是: ' . get_data_object_error_msg($req), ErrCode::$CHEQUE_FLOW_SERVICE_WORKFLOW_REQUEST_ERROR);
        }
        return $cheque_apply_model;

    }


    /**
     * 支票获取导出条件下的总数 主要用于导出数据
     * @param array $condition 条件
     * @return int
     */
    public function getExportListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => ChequeApplyModel::class]);
        $builder = $this->getChequeCondition($builder, $condition, ChequeEnums::LIST_TYPE_DATA_EXPORT);
        return (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
    }


    /**
     * 支票号获取导出条件下的总数 主要用于导出数据
     * @param array $condition 条件
     * @return int
     */
    public function getExportAccountListCount(array $condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => ChequeAccountModel::class]);
        $builder = $this->getAccountCondition($builder, $condition, ChequeEnums::LIST_TYPE_DATA_EXPORT);
        return (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
    }



    /**
     * 批次处理
     * @param array $data 数据
     * @return  array
     **/
    public function batchChequeAccountBusinessRel(array $data)
    {
        $cheque_account_data = array_chunk(array_unique($data), 1000);
        $cheque_account_arr              = [];
        foreach ($cheque_account_data as $cheque_code_ids) {
            $account_business_rel = ChequeAccountBusinessRelModel::find(
                [
                    'columns'    => 'cheque_account_id, payment_id, GROUP_CONCAT(distinct oa_biz_no) as oa_biz_no, GROUP_CONCAT(distinct payee_name) as payee_name , GROUP_CONCAT(distinct payee_account) as payee_account , GROUP_CONCAT(distinct supplier_no) as supplier_no',
                    'conditions' => 'cheque_account_id in ({cheque_account_id:array})',
                    'bind'       => [
                        'cheque_account_id' => $cheque_code_ids
                    ],
                    'group' => 'cheque_account_id'

                ]
            )->toArray();

            if (!empty($account_business_rel)) {
                $cheque_account_arr = array_merge($cheque_account_arr, $account_business_rel);
            }
        }
        $account_arr = [];
        if (!empty($cheque_account_arr)) {
            $account_arr = array_column($cheque_account_arr, null, 'cheque_account_id');
            foreach ($account_arr as &$item) {
                $item['oa_biz_no'] = implode(',',array_filter(explode(',',$item['oa_biz_no'])));
                $item['payee_name'] = implode(',',array_filter(explode(',',$item['payee_name'])));
                $item['payee_account'] = implode(',',array_filter(explode(',',$item['payee_account'])));
                $item['supplier_no'] = implode(',',array_filter(explode(',',$item['supplier_no'])));
            }
        }
        return $account_arr;
    }


    /**
     * 批次处理
     * @return  int
     **/
    public function batchAccountToChequeAccountBusiness()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ca.id,pc.payment_id,pc.payment_id,pc.payment_pay_id,ca.oa_type,ca.oa_biz_no,ca.payee_name,ca.payee_account,ca.supplier_no,pc.id  as pc_id');
        $builder->from(['ca' => ChequeAccountModel::class]);
        $builder->leftjoin(PaymentCheck::class, 'pc.ticket_no = ca.cheque_code', 'pc');
        $builder->where("ca.oa_biz_no <> '' and  pc.is_deleted = :is_deleted:", ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $cheque_account_arr = $builder->getQuery()->execute()->toArray();

        $i = 0;
        if (!empty($cheque_account_arr)) {
            foreach ($cheque_account_arr as $item) {
                $cheque_account_business_rel_model = ChequeAccountBusinessRelModel::findFirst(
                    ['conditions' => 'cheque_account_id = :cheque_account_id: and payment_check_id = :payment_check_id: and oa_biz_no = :oa_biz_no:',
                     'bind'       => [
                         'cheque_account_id' => $item['id'],
                         'payment_check_id'  => $item['pc_id'],
                         'oa_biz_no'         => $item['oa_biz_no'],
                     ]
                    ]
                );
                if (empty($cheque_account_business_rel_model)) {
                    //不存在 开始添加
                    $rel = [];
                    $cheque_account_business_rel = new ChequeAccountBusinessRelModel();
                    $rel['cheque_account_id']    = $item['id'];
                    $rel['payment_id']           = $item['payment_id'];
                    $rel['payment_check_id']     = $item['pc_id'];
                    $rel['oa_biz_no']            = $item['oa_biz_no'];
                    $rel['oa_type']              = $item['oa_type'];
                    $rel['payee_name']           = $item['payee_name'];
                    $rel['payee_account']        = $item['payee_account'];
                    $rel['supplier_no']          = $item['supplier_no'];
                    $rel['created_at']           = date('Y-m-d H:i:s', time());
                    if (!$cheque_account_business_rel->create($rel)) {
                        throw new BusinessException('写入 cheque_account_business_rel表 失败: ' . json_encode($rel, JSON_UNESCAPED_UNICODE) . '; 原因可能是: ' . get_data_object_error_msg($cheque_account_business_rel), ErrCode::$BUSINESS_ERROR);
                    }
                    $i++;
                }
            }
        }
        return $i;
    }



    /**
    * 获取在不同的国家显示不同的详情数据
    * @return bool
    **/
    public function getCountryView()
    {
        $country_code = get_country_code();
        return in_array($country_code, [GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE]) ? true : false;
    }


    /**
     * 台账释放
     * @param array $params 条件
     * @return array
     **/
    public function release(array $params)
    {
        $code            = ErrCode::$SUCCESS;
        $message         = $real_message = '';
        try {
            $account = ChequeAccountModel::find([
                'conditions' => 'id in ({ids:array}) and  use_status = :use_status:',
                'bind'       => ['ids' => $params['ids'], 'use_status' =>  ChequeEnums::CHECKS_APPLY_INFORMATION_USE_STATUS_YES],
            ])->toArray();
            if (empty($account)) {
                throw new ValidationException(static::$t->_('cheque_account_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            ;
            if (in_array(ChequeEnums::CHEQUE_ACCOUNT_RELEASE_DONE, array_column($account, 'release_status'))) {
                throw new ValidationException(static::$t->_('pay_data_list_upload_cheque_not_release'), ErrCode::$VALIDATE_ERROR);
            }

            $ids = implode(',', $params['ids']);
            //批量修改
            $db           = $this->getDI()->get('db_oa');
            $all_update_success = $db->updateAsDict(
                (new ChequeAccountModel)->getSource(),
                [
                    'release_status' => ChequeEnums::CHEQUE_ACCOUNT_RELEASE_DONE,
                    'release_at' => $params['release_date'],
                    'updated_at' => date('Y-m-d H:i:s', time())
                ],
                [
                    'conditions' => "id in ($ids)",
                ]
            );
            if ($all_update_success === false) {
                throw new BusinessException('支票管理-支票台账 -释放 数据是：'. json_encode(['ids' => $params['ids']], JSON_UNESCAPED_UNICODE), ErrCode::$CHEQUE_GET_ACCOUNT_EDIT_UPDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('支票管理-支票台账 -释放数据异常 原因是:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message
        ];

    }




    /**
    * 根据台账数据 查询关联的支票对应的业务数据
    * @param array $data
    * @param int $source 1 行关联上传  0  头部批量上传
    * @throws ValidationException
    * @return  array
    **/

    public function chequeBusinessGroup(array $data, int $source = 0)
    {
        if (!empty($source)) {
            //支票支付普通付款和采购
            $cheque_code = array_values($data);
            $module_info = array_combine($data, $data);
        } else {
            //支票支付租房付款
            $cheque_code = array_values(array_unique(array_column($data, 'oa_biz_order_number')));
            $module_info = array_column($data, null, 'oa_biz_order_number');
        }
        $res = [];
        if (!empty($cheque_code)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('cabr.oa_type,cabr.oa_biz_no,ca.cheque_code,ca.use_status,ca.exchange_status');
            $builder->from(['ca' => ChequeAccountModel::class]);
            $builder->leftjoin(ChequeAccountBusinessRelModel::class, 'cabr.cheque_account_id = ca.id', 'cabr');
            $builder->inWhere('ca.cheque_code', $cheque_code);
            $builder->groupBy('ca.cheque_code');
            $cheque_account_arr = $builder->getQuery()->execute()->toArray();

            foreach ($cheque_account_arr as $cheque_account) {
                if ($cheque_account['use_status'] != ChequeEnums::CHECKS_NUMBER_USE_STATUS_TWO || $cheque_account['exchange_status'] != ChequeEnums::CHECKS_EXCHANGE_STATUS_ING) {
                    throw new ValidationException(static::$t->_('cheque_code_status_err'), ErrCode::$VALIDATE_ERROR);
                }
                $res[$cheque_account['oa_type']]['module_info'][] = $module_info[$cheque_account['cheque_code']];
                $res[$cheque_account['oa_type']]['type_id']       = BankFlowEnums::$oa_type_model[$cheque_account['oa_type']];
            }
        }
        return $res;
    }



    /**
     * 根据传入的支票号返回业务单号
     * @param array $cheque_code_arr
     * @param $is_ordinary_payment 1 返回业务单为key的数据 0 返回查询数组
     * @return  array
     **/

    public function chequeCodeByRel(array $cheque_code_arr, $is_ordinary_payment = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('cabr.oa_type,cabr.oa_biz_no,ca.cheque_code,sum(ca.total_amount) as total_amount');
        $builder->from(['ca' => ChequeAccountModel::class]);
        $builder->leftjoin(ChequeAccountBusinessRelModel::class, 'cabr.cheque_account_id = ca.id', 'cabr');
        $builder->inWhere('ca.cheque_code', $cheque_code_arr);
        $builder->groupBy('ca.cheque_code');
        $cheque_account_arr = $builder->getQuery()->execute()->toArray();
        if (!empty($is_ordinary_payment)) {
            return $cheque_account_arr;
        } else {
            return array_column($cheque_account_arr, null, 'oa_biz_no');
        }
    }


    /**
     * 根据传入的业务单号返回barcode
     * @param string $apply_no
     * @throws ValidationException
     * @return  array
     **/

    public function applyNoBychequeRel(string $apply_no)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('cabr.oa_type,cabr.oa_biz_no,ca.cheque_code');
        $builder->from(['ca' => ChequeAccountModel::class]);
        $builder->leftjoin(ChequeAccountBusinessRelModel::class, 'cabr.cheque_account_id = ca.id', 'cabr');
        $builder->andWhere('cabr.oa_biz_no = :oa_biz_no:', ['oa_biz_no' => $apply_no]);
        $cheque_account_arr = $builder->getQuery()->execute()->toArray();
        return array_column($cheque_account_arr,'cheque_code' ,'oa_biz_no');
    }




}
