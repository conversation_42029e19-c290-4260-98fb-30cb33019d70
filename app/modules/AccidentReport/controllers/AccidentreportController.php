<?php

namespace App\Modules\AccidentReport\Controllers;

use App\Library\ApiClient;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\AccidentReport\Services\CommonService;
use App\Modules\Contract\Services\BaseService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AccidentreportController extends BaseController{

    /**
     * Notes: 获取事故列表
     * User: TB
     * Date: 2021/6/29
     * Time: 14:34
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function listAction(){
        $params = $this->request->get();
        try {
            $params = BaseService::handleParams($params, CommonService::$not_must_params);
            Validation::validate($params, CommonService::$validate_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = CommonService::getInstance()->getReportList($params,$this->user);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * Notes: 获取子任务列表
     * User: TB
     * Date: 2021/7/5
     * Time: 17:24
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function subListAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'    =>  'Required|Int',
                'pid'   =>  'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = CommonService::getInstance()->getSubList($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * Notes: 获取操作项
     * User: TB
     * Date: 2021/7/5
     * Time: 20:20
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function operationAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'    =>  'Required|Int',
                'pid'   =>  'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = CommonService::getInstance()->checkPower($params,$this->user);

        if($data === FALSE){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('accident_report_error_6'), []);
        }

        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }

    /**
     * Notes: 提交操作
     * User: TB
     * Date: 2021/7/6
     * Time: 15:54
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function submitOperationAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'        =>  'Required|Int',
                'pid'       =>  'Required|Int',
                'remark'    =>  'Required|Str|StrLenLe:500',
                'state'     =>  'Required|Int|IntIn:1,2',
                'checklist' =>  'Arr'
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        if(CommonService::getInstance()->checkPower($params,$this->user) === false){//校验操作权限
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('accident_report_error_6'), []);
        }

        if ($params['state'] != 2 && CommonService::getInstance()->checkComment($params,$this->user) === false){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('accident_report_error_8'), []);
        }

        $res = CommonService::getInstance()->submitOperation($params,$this->user);

        if ($res === true){
            return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('accident_report_error_9'), []);
        }else{
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $res ?: '', []);
        }
    }

    /**
     * Notes: 获取上报详情
     * User: TB
     * Date: 2021/7/7
     * Time: 14:36
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function getDetailAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'        =>  'Required|Int',
                'pid'       =>  'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = CommonService::getInstance()->getDetail($params,$this->user);

        if (is_array($res)){
            return $this->returnJson(ErrCode::$SUCCESS, '', $res);
        }else{
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $res ?: $this->t->_('accident_report_error_9'), []);
        }
    }

    /**
     * Notes: 提交评论
     * User: TB
     * Date: 2021/7/7
     * Time: 20:36
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function submitCommentAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'        =>  'Required|Int',
                'pid'       =>  'Required|Int',
                'arc_id'    =>  'Int',
                'arc_sub_id'=>  'Int',
                'content'   =>  'Required|Str|StrLenLe:500',
                'attachments'=> 'Arr',
                'plan'      =>  'Arr',
                'other'     =>  'Str|StrLenLe:500',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        if (!(CommonService::getInstance()->checkPower($params,$this->user)) && !(CommonService::getInstance()->checkPower($params,$this->user,'ccto'))){
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $this->t->_('accident_report_error_10'), []);
        }

        $res = CommonService::getInstance()->saveComment($params,$this->user);

        if ($res === true){
            return $this->returnJson(ErrCode::$SUCCESS, '', []);
        }else{
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $res ?: $this->t->_('accident_report_error_9'), []);
        }
    }

    /**
     * Notes: 获取评论列表
     * User: TB
     * Date: 2021/7/8
     * Time: 16:20
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function commentListAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'        =>  'Required|Int',
                'pid'       =>  'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = CommonService::getInstance()->commentList($params,$this->user);

        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * Notes: 获取二级评论列表
     * User: TB
     * Date: 2021/7/8
     * Time: 16:20
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function subCommentListAction(){
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'id'        =>  'Required|Int',
                'pid'       =>  'Required|Int',
                'arc_id'    =>  'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = CommonService::getInstance()->subCommentList($params,$this->user);

        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * Notes: 获取部门信息
     * User: TB
     * Date: 2021/9/22
     * Time: 20:23
     *
     * @return Response|ResponseInterface
     */
    public function getDepartmentAction(){
        $res = CommonService::getInstance()->getDepartment();

        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * Notes: 事故上报调用BI-svc
     * User: TB
     * Date: 2023/6/20
     * Time: 11:00
     *
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function accidentForBiAction(){
        $param = $this->request->get();
        $param['userInfo'] = $this->user;
        $t = $this->t;

        $validations = [
            'action' => "Required|Str|>>>:" . $t->_('access_data_request_param_error'),
        ];
        Validation::validate($param, $validations);
        $action = $param['action'];
        $api_client_sys = 'bi';
        if(get_country_code() == GlobalEnums::MY_COUNTRY_CODE) {
            //马来走新的项目地址
            $api_client_sys = 'ard_api_svc';
        }
        $ac = new ApiClient($api_client_sys, '', "accident.$action", $this->locale);
        $ac->setParams([$param]);
        $acResult = $ac->execute();
        if (isset($acResult['result'])) {
            return $this->returnJson($acResult['result']['code'], $acResult['result']['msg'], $acResult['result']['data'] ?? []);
        } else {
            $this->logger->error("事故上报BI-svc调用失败 参数:" . json_encode($param) . ";结果:" . json_encode($acResult));
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $t->_('retry_later'));
        }
    }
}