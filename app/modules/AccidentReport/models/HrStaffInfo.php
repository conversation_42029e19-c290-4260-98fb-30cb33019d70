<?php

namespace App\Modules\AccidentReport\Models;

use App\Models\Base;

class HrStaffInfo extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('hr_staff_info');
    }

    public static function getUserInfo($staffInfoId, $columns = '*')
    {
        if(empty($staffInfoId)){
            return null;
        }
        $userInfo = self::findFirst([
            "conditions" => "staff_info_id = :staff_info_id:",
            "bind"       => ['staff_info_id' => $staffInfoId],
            "columns"    => $columns
        ]);
        return  $userInfo ? $userInfo->toArray() : null;
    }

    /**
     * Notes: 获取员工组织名称 网点/部门名
     * User: TB
     * Date: 2021/7/7
     * Time: 15:07
     * @param $staffInfoId
     * @return string|null
     */
    public static function getOrganization($staffInfoId){
        if(empty($staffInfoId)){
            return null;
        }
        $userInfo = self::getUserInfo($staffInfoId,'sys_department_id,sys_store_id');
        if (!empty($userInfo['sys_store_id']) && $userInfo['sys_store_id'] != -1){
            $storeInfo = SysStore::findFirst(['conditions'=>'id = :id:','bind'=>['id'=>$userInfo['sys_store_id']]])->toArray();
            $name = $storeInfo['name'];
        }else{
            $departmentInfo = SysDepartment::findFirst($userInfo['sys_department_id'])->toArray();
            $name = $departmentInfo['name'];
        }
        return $name;
    }
}