<?php

namespace App\Modules\AccidentReport\Services;

use App\Library\Enums;
use App\Modules\AccidentReport\Models\AccidentReport;
use App\Modules\AccidentReport\Models\AccidentReportComment;
use App\Modules\AccidentReport\Models\AccidentReportSub;
use App\Modules\AccidentReport\Models\HrStaffInfo;
use App\Modules\AccidentReport\Models\HrStaffInfoPosition;
use App\Modules\AccidentReport\Models\SettingEnv;
use App\Modules\AccidentReport\Models\SysStore;
use App\Modules\Contract\Services\ListService;
use App\Modules\Organization\Models\SysDepartmentModel;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Models\RoleModel;

class CommonService extends BaseService
{
    private static $instance;

    //参数校验规则
    public static $validate_list = [
        'status' => 'Required|IntIn:1,2',
        'accident_type' => 'Required|IntIn:1,2',
        'pageSize'  =>  'Required|Int|IntGe:1',
        'pageNum'  =>  'Required|Int|IntGe:1',
        'report_date_start' => 'DateTime',
        'report_date_end' => 'DateTime',
        'report_site'=>'Str',
        'accident_network'=>'Str',
        'task_id'=>'Str',
    ];

    //非必填参数
    public static $not_must_params = [
        'report_date_start',
        'report_date_end',
        'report_site',
        'accident_network',
        'task_id',
    ];

    /**
     * @return ListService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * Notes: 获取事故处理列表
     * User: TB
     * Date: 2021/7/5
     * Time: 12:06
     * @param $p
     * @param $user
     * @param int $type
     * @return array
     */
    public function getReportList($p, $user)
    {
        try {
            $pageSize = empty($p['pageSize']) ? 20 : MAX($p['pageSize'], 1);
            $pageNum = empty($p['pageNum']) ? 1 : MAX($p['pageNum'], 1);
            $offset = $pageSize * ($pageNum - 1);
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                CONCAT("EV",b.id) AS main_id
                ,a.id
                ,a.pid
                ,b.created_at
                ,IFNULL(b.report_store_name,"-") AS report_store_name
                ,b.relevant_store
                ,b.occurrence_time
                ,b.influence_scope
                ,b.loss_type
                ,b.report_desc
                ,a.state'
                )
                ->from(['a' => AccidentReportSub::class])
                ->leftJoin(AccidentReport::class, 'a.pid = b.id', 'b');
                //->groupBy('b.id')

            if ($p['status'] == 1) {//处理状态
                $builder->andWhere('b.state = 0');
                $builder->orderBy('a.created_at ASC');
            } elseif ($p['status'] == 2) {
                $builder->andWhere('b.state = 1');
                $builder->orderBy('a.updated_at DESC');
            }

            if ($p['accident_type'] == 1){//处理人
                $accident_type = $this->getAcceptorType($user,'acceptor');
                $builder->andWhere('a.acceptor IN({acceptor:array})',['acceptor'=>$accident_type]);
                if (empty($accident_type)){
                    goto return_tag;
                }
            }elseif ($p['accident_type'] == 2){//抄送人
                $accident_type = $this->getAcceptorType($user,'ccto');
                $builder->andWhere('a.ccto IN({ccto:array})',['ccto'=>$accident_type]);
                if (empty($accident_type)){
                    goto return_tag;
                }
            }

            if (isset($p['report_date_start']) && !empty($p['report_date_start'])) {//上报日期
                $builder->andWhere('a.created_at >= :start_time:', ['start_time' => $p['report_date_start']]);
            }
            if (isset($p['report_date_end']) && !empty($p['report_date_end'])) {//上报日期
                $builder->andWhere('a.created_at <= :end_time:', ['end_time' => $p['report_date_end']]);
            }

            if (isset($p['report_site']) && !empty($p['report_site'])) {//上报网点
                $builder->andWhere('b.report_store_id = :report_store_id:', ['report_store_id' => $p['report_site']]);
            }

            if (isset($p['accident_network']) && !empty($p['accident_network'])){//事故相关网点
                $builder->andWhere('FIND_IN_SET(:relevant_store:,b.relevant_store)', ['relevant_store' => $p['accident_network']]);
            }

            if (isset($p['task_id']) && !empty($p['task_id'])){//任务id
                $builder->andWhere('a.pid = :pid:', ['pid' => intval(str_replace('EV','',strtoupper($p['task_id'])))]);
            }



            $count = $builder->getQuery()->execute()->count();
            $builder->limit($pageSize, $offset);
            $items = $builder->getQuery()->execute()->toArray();
            //print_r($builder->getQuery()->getSql());die;


            foreach ($items as $k => $v) {
                //影响范围
                foreach (explode(',', $v['influence_scope']) as $vv) {
                    $v['influence_scope_str'][] = static::$t->_('accident_report_influence_scope_' . $vv);
                }

                //损失类型
                foreach (explode(',', $v['loss_type']) as $vv) {
                    $v['loss_type_str'][] = static::$t->_('accident_report_loss_type_' . $vv);
                }

                //子任务数
                $v['all_num'] = AccidentReportSub::count([
                    'pid = ?0',
                    'bind' => [$v['pid']]
                ]);

                //未完成子任务数
                $v['unfinished_num'] = AccidentReportSub::count([
                    'pid = ?0 AND state = 0',
                    'bind' => [$v['pid']]
                ]);

                //事故关联网点
                $v['relevant_store_name'] = array_column(SysStore::find([
                    'conditions' => 'id IN ({ids:array})',
                    'bind' => ['ids' => explode(',', $v['relevant_store'])],
                    'columns' => 'name'
                ])->toArray(), 'name');

                //等待处理时长
                $v['H'] = intval((strtotime(date('Y-m-d H:i:s')) - strtotime($v['created_at'])) / 3600);
                $v['min'] = round(((strtotime(date('Y-m-d H:i:s')) - strtotime($v['created_at'])) % 3600) / 60);
                $v['waiting_time'] = "{$v['H']}H {$v['min']}min";

                if ($p['status'] == 1 && $p['accident_type'] == 1 && $v['state'] == 0){
                    $v['show_operation'] = 1;
                }else{
                    $v['show_operation'] = 0;
                }


                $list[] = [
                    'task_id'               => $v['main_id'],
                    'id'                    => $v['id'],
                    'pid'                   => $v['pid'],
                    'report_time'           => $v['created_at'],
                    'waiting_time'          => $v['waiting_time'],
                    'all_num'               => $v['all_num'],
                    'unfinished_num'        => $v['unfinished_num'],
                    'report_store_name'     => $v['report_store_name'],
                    'relevant_store_name'   => $v['relevant_store_name'],
                    'occurrence_time'       => $v['occurrence_time'],
                    'influence'             => $v['influence_scope_str'],
                    'report_desc'           => $v['report_desc'],
                    'show_operation'        => $v['show_operation'],
                ];
            }
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$user,'msg'=>$e->getMessage()]));
        }

        return_tag:

        return [
            'items' => $list ?? [],
            'pagination' => [
                'pageNum' => $pageNum,
                'pageSize' => $pageSize,
                'total' => $count ?? 0,
            ]
        ];
    }

    /**
     * Notes: 获取处理人/抄送人类型
     * User: TB
     * Date: 2021/7/5
     * Time: 12:05
     * @param $user
     * @param $key
     * @param $operate
     * @return array|bool
     */
    public function getAcceptorType($user,$key,$operate=false){
        try {
            $config = json_decode(SettingEnv::findFirst([
                'conditions' => 'code = :code:',
                'bind' => ['code' => "accident_report_{$key}_config"],
            ])->toArray()['set_val'],true);
            $roles = $this->getStaffRoles($user['id']);

            //系统管理员角色 超级管理员角色 快递总经理（固定工号） 有查看全部数据的权限
            if (in_array(14,$roles) || (in_array(99,$roles) && $operate == false) || in_array($user['id'],[21848])){
                return array_keys($config);
            }

            $ids = [];
            foreach ($config as $k=>$v){
                if (in_array($user['id'],$v['staff_id'])){//如果有工号配置
                    $ids[] = $k;
                    continue;
                }

                foreach ($v['departments'] as $kk=>$vv){//部门配置
                    if ($user['department_id'] == $kk) {
                        if (empty($vv)){//部门下没有角色限制
                            $ids[] = $k;
                            continue 2;
                        }
                        foreach ($roles as $role){//遍历判断符合的角色
                            if (in_array($role,$vv)){
                                $ids[] = $k;
                                continue 3;
                            }
                        }
                    }
                }

                //仅角色配置
                foreach ($roles as $role){//遍历判断符合的角色
                    if (in_array($role,$v['roles'])){
                        $ids[] = $k;
                        continue 2;
                    }
                }
            }
        }catch (\Exception $e){
            $this->logger->error(json_encode(['key'=>$key,'user'=>$user,'msg'=>$e->getMessage()]));
        }

        return $ids ?: [];
    }

    /**
     * Notes: 获取员工角色
     * User: TB
     * Date: 2021/7/5
     * Time: 12:05
     * @param $staff_id
     * @return array
     */
    public function getStaffRoles($staff_id) {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('staff_info_id,position_category');
            $builder->from(HrStaffInfoPosition::class);
            $builder->andWhere('staff_info_id = :staff_id:',['staff_id'=>$staff_id]);
            $list = array_column($builder->getQuery()->execute()->toArray(),'position_category');
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$staff_id,'msg'=>$e->getMessage()]));
        }

        return $list ?? [];
    }

    /**
     * Notes: 获取员工部门
     * User: TB
     * Date: 2021/7/5
     * Time: 12:05
     * @param $staff_id
     * @return array
     */
    public function getStaffDepartment($staff_id) {
        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('staff_info_id,sys_department_id');
            $builder->from(HrStaffInfo::class);
            $builder->andWhere('staff_info_id = :staff_id:',['staff_id'=>$staff_id]);
            $list = array_column($builder->getQuery()->execute()->toArray(),'sys_department_id');
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$staff_id,'msg'=>$e->getMessage()]));
        }

        return $list[0] ?? 0;
    }

    /**
     * Notes: 获取子任务列表
     * User: TB
     * Date: 2021/7/5
     * Time: 17:26
     * @param $p
     * @return array
     */
    public function getSubList($p){
        try {
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                a.acceptor
                ,a.operator
                ,a.operator_name
                ,a.state
                ,a.remark
                ,IFNULL(MAX(b.created_at),"-") AS newest_time')
                ->from(['a' => AccidentReportSub::class])
                ->leftJoin(AccidentReportComment::class, 'a.pid = b.ar_id AND a.id = b.ars_id', 'b')
                ->andWhere('a.pid = :pid:',['pid'=>$p['pid']])
                ->groupBy('a.id')
                ->orderBy('a.created_at DESC , a.id ASC');
            $count = $builder->getQuery()->execute()->count();

            if (!empty($p['pageNum'])){
                $pageSize = empty($p['pageSize']) ? 20 : MAX($p['pageSize'], 1);
                $pageNum = MAX($p['pageNum'], 1);
                $offset = $pageSize * ($pageNum - 1);
                $builder->limit($pageSize, $offset);
            }

            $acceptorConfig = json_decode(SettingEnv::findFirst([
                'conditions' => 'code = :code:',
                'bind' => ['code' => "accident_report_acceptor_config"],
            ])->toArray()['set_val'],true);
            $departments = array_column($this->getDepartment(),'name','id');
            $roles = array_column($this->getRoles(),NULL,'id');

            $items = $builder->getQuery()->execute()->toArray();

            foreach ($items as $v){
                $departmentArr = [];
                $acceptorStr = '';
                $acceptor = $acceptorConfig[$v['acceptor']];
                foreach ($acceptor['departments'] as $kk=>$vv){
                    $roleArr = [];
                    foreach ($vv as $vvv){
                        if (static::$language == 'en'){
                            $departmentArr[$departments[$kk]][] = $roles[$vvv]['description_en'];
                        }elseif (static::$language == 'th'){
                            $departmentArr[$departments[$kk]][] = $roles[$vvv]['description_th'];
                        }else{
                            $departmentArr[$departments[$kk]][] = $roles[$vvv]['description_zh'];
                        }
                    }
                }
                foreach ($departmentArr as $departmentStr=>$roleArr){
                    $acceptorStr .= " {$departmentStr}(".implode(',',array_unique($roleArr)).") ,";
                }
                foreach ($acceptor['staff_id'] as $staffId){
                    $_staff_department = $departments[$this->getStaffDepartment($staffId)] ?? '';
                    $acceptorStr .= $_staff_department . "({$staffId})";
                }
                $list[] = [
                    'acceptor'      =>  trim($acceptorStr,','),
                    'state'         =>  $v['state'] ? static::$t->_('accident_report_sub_state_1') : static::$t->_('accident_report_sub_state_0'),
                    'operator'      =>  $v['operator'] ? "{$v['operator']}({$v['operator_name']})" : '-',
                    'operator_state'=>  $v['state'] ? static::$t->_('accident_report_sub_state_'.$v['state']) : '-',
                    'remark'        =>  $v['remark'] ?: '-',
                    'newest_time'   =>  $v['newest_time'],
                ];
            }

        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'msg'=>$e->getMessage()]));
        }

        return [
            'items' => $list ?? [],
            'pagination' => [
                'pageNum' => $pageNum ?? $p['pageNum'] ?? 1,
                'pageSize' => $pageSize ?? $p['pageSize'] ?? 20,
                'total' => $count ?? 0,
            ]
        ];
    }

    /**
     * Notes: 验证是否可操作
     * User: TB
     * Date: 2021/7/5
     * Time: 21:25
     * @param $p
     * @param $u
     * @return array|false
     */
    public function checkPower($p,$u,$type = 'acceptor'){
        try {
            $acceptor_type = $this->getAcceptorType($u,$type,true);
            if (empty($acceptor_type)){
                return false;
            }
            $data = AccidentReportSub::findFirst([
                'conditions' => 'id = :id: AND pid = :pid: AND '.$type.' IN({'.$type.':array})',
                'bind' => ['id' => $p['id'],'pid' => $p['pid'] , "$type" => $acceptor_type],
            ]);

            if ($data){
                $data = $data->toArray();
                $item = json_decode($data['items'],true);
                foreach ($item as $k=>$v){
                    if (is_array($v)){
                        $many_cases = 1;
                        $sub_items = [];
                        foreach ($v as $vv){
                            $sub_items[] = [
                                'label' => static::$t->_('accident_report_sub_items_'.$vv),
                                'value' => $vv,
                            ];
                        }
                        $items[] = [
                            'label'     => static::$t->_('accident_report_sub_cases_items_'.$k),
                            'value'     => $k,
                            'sub_item'  => $sub_items,
                        ];
                    }else{
                        $many_cases = 0;
                        $items[] = [
                            'label' => static::$t->_('accident_report_sub_items_'.$v),
                            'value' => $v,
                        ];
                    }
                }
                $data['items'] = $items ?? [];
                return [
                    'many_cases' => $many_cases ?? 0,
                    'items' =>  $items ?? [],
                    'state' =>  [
                        ['label'=>static::$t->_('accident_report_sub_state_1'),'value'=>1],
                        ['label'=>static::$t->_('accident_report_sub_state_2'),'value'=>2],
                    ]
                ];
            }
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return FALSE;
    }

    /**
     * Notes: 提交操作
     * User: TB
     * Date: 2021/7/6
     * Time: 16:59
     * @param $p
     * @param $u
     * @return bool
     */
    public function submitOperation($p,$u){
        try {
            $data = AccidentReportSub::findFirst([
                'conditions' => 'id = :id: AND pid = :pid:',
                'bind' => ['id' => $p['id'],'pid' => $p['pid']],
            ]);

            if (empty($data)){
                return static::$t->_('accident_report_error_1');
            }

            if ($data->state != 0){
                return static::$t->_('accident_report_error_2');
            }

            //已完成时校验待处理项
            if ($p['state'] == 1) {
                /* 因为增加了二维情况，所以暂时去掉了待处理是想的校验
                $items = explode(',', $data->items);
                foreach ($items as $v) {
                    if (!in_array($v, $p['checklist'])) {
                        return static::$t->_('请确保应处理事项已完成');
                    }
                }*/
            }

            //开启事物
            $db = $this->getDI()->get('db_backyard');
            $db->begin();
            $data->state = $p['state'];
            $data->remark = $p['remark'];
            $data->updated_at = date('Y-m-d H:i:s');
            $data->operator = $u['id'];
            $data->operator_name = $u['name'];
            $data->submit_items = json_encode($p['checklist']);
            $flag = $data->update(); // 更新子任务
            if(!$flag){
                $db->rollback();
                return static::$t->_('accident_report_error_3');
            }

            //判断全部子任务是否都完成，是则更新主任务
            $builder = $this->modelsManager->createBuilder()
                ->columns('a.id')
                ->from(['a' => AccidentReportSub::class])
                ->andWhere('a.pid = :pid: AND a.state = 0',['pid'=>$p['pid']]);
            $count = $builder->getQuery()->execute()->count();
            if ($count == 0){
                $mainTask = AccidentReport::findFirst($p['pid']);
                $mainTask->state = 1;
                $flag = $mainTask->update();
                if(!$flag){
                    $db->rollback();
                    return static::$t->_('accident_report_error_4');
                }
            }

            $db->commit();
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return TRUE;
    }

    /**
     * Notes: 验证是否评论/回复
     * User: TB
     * Date: 2021/7/6
     * Time: 17:00
     * @param $p
     * @param $u
     * @return bool
     */
    public function checkComment($p,$u){
        try {
            $data = AccidentReportComment::findFirst([
                'conditions' => 'ar_id = :ar_id: AND ars_id = :ars_id: AND reviewer = :reviewer:',
                'bind' => ['ar_id' => $p['pid'],'ars_id' => $p['id'] , 'reviewer' => $u['id']],
            ]);
            if ($data){ return TRUE; }
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return FALSE;
    }

    /**
     * Notes: 获取事故详情
     * User: TB
     * Date: 2021/7/7
     * Time: 16:26
     * @param $p
     * @param $u
     * @return array|false
     */
    public function getDetail($p,$u){
        try {
            $reportInfo = AccidentReport::findFirst($p['pid'])->toArray();
            $organizationName = HrStaffInfo::getOrganization($reportInfo['report_staff_id']);

            //事故关联网点
            $relevant_store = array_column(SysStore::find([
                'conditions' => 'id IN ({ids:array})',
                'bind' => ['ids' => explode(',', $reportInfo['relevant_store'])],
                'columns' => 'name'
            ])->toArray(), 'name');

            //影响范围
            foreach (explode(',', $reportInfo['influence_scope']) as $vv) {
                $influence_scope_str[] = static::$t->_('accident_report_influence_scope_' . $vv);
            }

            //影响范围
            foreach (explode(',', $reportInfo['loss_type']) as $vv) {
                $loss_type[] = static::$t->_('accident_report_loss_type_' . $vv);
            }

            $content = [
                [
                    'key'   =>  static::$t->_('accident_report_detail_title_1'),
                    'val'   =>  "EV{$reportInfo['id']}",
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_2'),
                    'val'   =>  $reportInfo['occurrence_time'],
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_3'),
                    'val'   =>  $reportInfo['report_store_name'] ?: '-',
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_4'),
                    'val'   =>  implode(',',$relevant_store) ?: '-',
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_5'),
                    'val'   =>  implode(',',$influence_scope_str) ?: '-',
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_6'),
                    'val'   =>  implode(',',$loss_type) ?: '-',
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_7'),
                    'val'   =>  $reportInfo['report_desc'],
                ],[
                    'key'   =>  static::$t->_('accident_report_detail_title_8'),
                    'val'   =>  json_decode($reportInfo['attachments'],true),
                ],
            ];


            $list = [
                'poster'    =>  "{$reportInfo['report_staff_id']}({$reportInfo['report_staff_name']})",
                'department'=>  $organizationName,
                'post_time' =>  $reportInfo['created_at'],
                'content'   =>  $content,
            ];
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return $list ?? FALSE;
    }

    /**
     * Notes: 提交评论
     * User: TB
     * Date: 2021/7/7
     * Time: 21:27
     * @param $p
     * @param $u
     * @return bool
     */
    public function saveComment($p,$u){
        try {

            $content = [
                'content'   =>  $p['content']
            ];

            if (empty($p['arc_id'])){
                if (isset($p['plan']) && !empty($p['plan'])){
                    $content['plan'] = $p['plan'];
                }

                if (isset($p['other']) && !empty($p['other'])){
                    $content['other'] = $p['other'];
                }
            }

            $data = [
                'ar_id'             =>  $p['pid'],
                'ars_id'            =>  $p['id'],
                'arc_id'            =>  $p['arc_id'] ?? 0,
                'arc_sub_id'        =>  $p['arc_sub_id'] ?? 0,
                'content'           =>  json_encode($content),
                'reviewer'          =>  $u['id'],
                'reviewer_name'     =>  $u['name'],
                'organization_name' =>  HrStaffInfo::getOrganization($u['id']),
                'attachments'       => empty($p['arc_id']) ? json_encode($p['attachments']) : NULL,
                'created_at'        =>  date('Y-m-d H:i:s'),
            ];

            $bool = (new AccidentReportComment())->i_create($data);
            if ($bool === false) {
                return static::$t->_('accident_report_error_5');
            }
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return TRUE;
    }

    /**
     * Notes:  获取评论列表
     * User: TB
     * Date: 2021/7/8
     * Time: 16:19
     * @param $p
     * @param $u
     * @return array
     */
    public function commentList($p,$u){
        try {
            $builder = $this->modelsManager->createBuilder()
                ->columns('a.*')
                ->from(['a' => AccidentReportComment::class])
                ->andWhere('a.ar_id = :pid:',['pid'=>$p['pid']])
                ->andWhere('a.arc_id = 0 AND a.arc_sub_id = 0')
                ->orderBy('a.id ASC');

            $pageSize = empty($p['pageSize']) ? 20 : MAX($p['pageSize'], 1);
            $pageNum = empty($p['pageNum']) ? 1 : MAX($p['pageNum'], 1);
            $offset = $pageSize * ($pageNum - 1);
            $builder->limit($pageSize, $offset);

            $items = $builder->getQuery()->execute()->toArray();

            foreach ($items AS $v){
                $v['content'] = json_decode($v['content'],true);
                $content = [
                    [
                        'key'   =>  static::$t->_('accident_report_comment_1'),
                        'val'   =>  [
                            'label'         =>  $v['content']['content'],
                            'attachment'    =>  json_decode($v['attachments'],TRUE),
                        ]
                    ]
                ];

                //评接下一步计划
                if (isset($v['content']['plan'])){
                    $plan = [];
                    foreach ($v['content']['plan'] as $vv){
                        $plan[] = [
                            'progress' => $vv['todo'],
                            'date' => $vv['finish_at'],
                        ];
                    }
                    $content[] = [
                        'key'   =>  static::$t->_('accident_report_comment_2'),
                        'val'   =>  $plan,
                    ];
                }

                if (isset($v['content']['other'])){
                    $content[] = [
                        'key'   =>  static::$t->_('accident_report_comment_3'),
                        'val'   =>  $v['content']['other'] ?: '-',
                    ];
                }


                $list[] = [
                    'id'        =>  $v['id'],
                    'poster'    =>  "{$v['reviewer']}({$v['reviewer_name']})",
                    'department'=>  $v['organization_name'],
                    'post_time' =>  $v['created_at'],
                    'content'   =>  $content,
                    'reply'     =>  self::subCommentList(['pid'=>$p['pid'],'arc_id'=>$v['id']],$u),
                ];
            }



            $nextPage = count($list ?? []) == $pageSize ? 1 : 0;
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return [
            'items' => $list ?? [],
            'pagination' => [
                'pageNum' => $pageNum,
                'pageSize' => $pageSize,
                'nextPage' => $nextPage,
            ]
        ];
    }

    /**
     * Notes:  获取二级评论列表
     * User: TB
     * Date: 2021/7/8
     * Time: 16:19
     * @param $p
     * @param $u
     * @return array
     */
    public function subCommentList($p,$u){
        try {
            $builder = $this->modelsManager->createBuilder()
                ->columns('
                    a.id
                    ,a.reviewer
                    ,a.reviewer_name
                    ,a.content
                    ,a.created_at
                    ,b.reviewer as by_reviewer
                    ,b.reviewer_name as by_reviewer_name
                ')
                ->from(['a' => AccidentReportComment::class])
                ->leftJoin(AccidentReportComment::class,'b.id = IF(a.arc_sub_id = 0,a.arc_id,a.arc_sub_id)','b')
                ->andWhere('a.ar_id = :pid:',['pid'=>$p['pid']])
                ->andWhere('a.arc_id = :arc_id:',['arc_id'=>$p['arc_id']])
                ->orderBy('a.id ASC');

            $pageSize = empty($p['pageSize']) ? 20 : MAX($p['pageSize'], 1);
            $pageNum = empty($p['pageNum']) ? 1 : MAX($p['pageNum'], 1);
            $offset = $pageSize * ($pageNum - 1);
            $builder->limit($pageSize, $offset);

            $items = $builder->getQuery()->execute()->toArray();

            $list = [];
            foreach ($items AS $v){
                $list[] = [
                    'id'        =>  $v['id'],
                    'reply_per' =>  "{$v['reviewer']}({$v['reviewer_name']})",
                    'by_reply'  =>  "{$v['by_reviewer']}({$v['by_reviewer_name']})",
                    'reply_time'=>  $v['created_at'],
                    'content'   =>  json_decode($v['content'],true)['content'],
                ];
            }



            $nextPage = count($list) == $pageSize ? 1 : 0;
        }catch (\Exception $e){
            $this->logger->error(json_encode(['params'=>$p,'user'=>$u,'msg'=>$e->getMessage()]));
        }

        return [
            'items' => $list ?? [],
            'pagination' => [
                'pageNum' => $pageNum,
                'pageSize' => $pageSize,
                'nextPage' => $nextPage,
            ]
        ];
    }

    /**
     * Notes: 获取部门信息
     * User: TB
     * Date: 2021/9/22
     * Time: 20:23
     * @return array
     */
    public function getDepartment(){
        try {
            // 提取员工部门/公司名称
            $sys_department = SysDepartmentModel::find(['columns' => ['id', 'name']])->toArray();
        } catch (\Exception $e){
            $this->logger->error(json_encode(['msg'=>$e->getMessage()]));
        }

        return $sys_department ?: [];
    }

    /**
     * Notes: 获取角色信息
     * User: TB
     * Date: 2021/9/30
     * Time: 00:45
     * @return array
     */
    public function getRoles(){
        try {
            // 提取员工部门/公司名称
            $roles = RoleModel::find()->toArray();
        } catch (\Exception $e){
            $this->logger->error(json_encode(['msg'=>$e->getMessage()]));
        }

        return $roles ?: [];
    }


}
