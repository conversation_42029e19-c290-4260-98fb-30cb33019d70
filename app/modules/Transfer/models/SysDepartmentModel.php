<?php

namespace App\Modules\Transfer\Models;

use App\Models\Base;

class SysDepartmentModel extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_rbackyard');
        // setSource()方法指定数据库表
        $this->setSource('sys_department');
    }

    /**
     * 获取network及其子部门
     */
    public function getNetworkDepartmentIds(): array
    {
        return self::getDepartmentIdsList('dept_network_management_id');
    }

    /**
     * 获取shop及其子部门
     */
    public function getShopDepartmentIds(): array
    {
        return self::getDepartmentIdsList("dept_shop_management_id");
    }

    /**
     * 获取hub及其子部门
     */
    public function getHubDepartmentIds(): array
    {
        return self::getDepartmentIdsList("dept_hub_management_id");
    }

    /**
     * 获取B hub及其子部门
     */
    public function getBhubDepartmentIds(): array
    {
        return self::getDepartmentIdsList("flash_freight_hub_management_id");
    }
	
	/**
	 * @description:  获取 network bulky 部门
	 *
	 * @param null
	 *
	 * @return     :
	 * <AUTHOR> L.J
	 * @time       : 2021/12/8 17:06
	 */
	public function getNetworkBulkyDepartmentIds(): array
	{
		return self::getDepartmentIdsList('dept_network_bulky_id');
	}
	

    /**
     * 根据部门链获取部门及其子部门
     * @param $code
     * @return array
     */
    private function getDepartmentIdsList($code): array
    {
        //[1]获取部门ID
        $deptId = SettingEnvModel::findFirst([
            'conditions' => "code = :code:",
            'bind'       => [
                'code' => $code,
            ],
        ]);
        if (empty($deptId)) {
            return [];
        }

        //[2]获取部门的部门链
        $deptInfo = $this->findFirst($deptId->set_val);
        if (empty($deptInfo)) {
            return [];
        }

        $departmentIdsArr = $this->find([
            'conditions' => 'ancestry_v3 like :chain: or ancestry_v3 = :chain_id:',
            'bind'       => [
                'chain'     => $deptInfo->ancestry_v3 . '/%',
                'chain_id'  => $deptInfo->ancestry_v3
            ],
            'columns'    => 'id'
        ])->toArray();
        return array_column($departmentIdsArr, 'id') ?? [];
    }
}