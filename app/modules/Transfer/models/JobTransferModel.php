<?php

namespace App\Modules\Transfer\Models;

use App\Models\Base;

class JobTransferModel extends Base
{
    /**
     * id
     * @var int
     */
    private $id;

    /**
     * 被转岗员工id
     * @var int
     */
    private $staff_id;

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return int
     */
    public function getStaffId(): int
    {
        return $this->staff_id;
    }

    /**
     * @param int $staff_id
     */
    public function setStaffId(int $staff_id): void
    {
        $this->staff_id = $staff_id;
    }

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        // setSource()方法指定数据库表
        $this->setSource('job_transfer');
    }
}