<?php

namespace App\Modules\AdministrationTicket\Services;

use App\Library\Enums\StaffInfoEnums;
use App\Library\RoleEnums;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Transfer\Services\SysService;
use App\Modules\User\Models\AttachModel;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\AdministrationTicketEnums;
use App\Models\backyard\AdministrationQuestionTypeModel;
use App\Models\backyard\AdministrationOrderModel;
use App\Models\backyard\AdministrationLogModel;
use App\Modules\Deposit\Services\DepositService;
use App\Modules\Common\Services\StaffService;
use App\Modules\User\Models\StaffPermissionModel;
use App\Repository\BySettingEnvRepository;
use App\Repository\DepartmentRepository;
use App\Repository\HrStaffRepository;
use Exception;

class TicketListService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取员工行政工单的数据权限条件
     * 使用缓存
     * @param $staffInfo
     * @return array|bool|mixed
     */
    protected function getStaffPermissionUseCache($staffInfo)
    {
        $redis     = $this->getDI()->get('redis');
        $redis_key = 'TicketPermission:'.md5(json_encode($staffInfo));
        //这里先读缓存
        $cache = $redis->get($redis_key);
        if ($cache) {
            return json_decode($cache, true);
        }

        // 如果不是th ph 则直接返回
        if (!in_array(get_country_code(), [Enums\GlobalEnums::TH_COUNTRY_CODE, Enums\GlobalEnums::PH_COUNTRY_CODE])) {
            return true;
        }

        if (get_country_code() == Enums\GlobalEnums::TH_COUNTRY_CODE) {
            $result = $this->getStaffPermission($staffInfo);
        }
        if (get_country_code() == Enums\GlobalEnums::PH_COUNTRY_CODE) {
            $result = $this->getStaffPermissionPh($staffInfo);
        }
        $redis->setex($redis_key, 60 * 5, json_encode($result, JSON_UNESCAPED_UNICODE));
        return $result;
    }

    /**
     * https://flashexpress.feishu.cn/docx/JyeydhmWMoZQOlxV1Yjc6rfAnBd
     * 获取员工行政工单的数据权限条件 - 适用于PH国家
     * @param array $staffInfo
     * @return array|bool
     */
    public function getStaffPermissionPh(array $staffInfo)
    {
        //角色
        $roleList = (new HrStaffRepository())->getStaffRoleList($staffInfo['id']);
        $roleIds  = !empty($roleList) ? array_column($roleList, 'position_category') : [];

        // 1.1 HRIS管理员[41] 超级管理员[99] HR系统管理员[115] 系统管理员[14]
        if (array_intersect($roleIds, [
            RoleEnums::ROLE_HRIS,
            RoleEnums::ROLE_SUPER_ADMIN,
            RoleEnums::ROLE_HR_SYSTEM_ADMIN,
            RoleEnums::ROLE_SYSTEM_ADMIN,
        ])) {
            return true;
        }
        //1.2 HRBP[68] 查看自己管辖范围内的员工提交的工单数据
        if (in_array(RoleEnums::ROLE_HRBP, $roleIds)) {
            return StaffService::getInstance()->staffManageRangeCondition($staffInfo['id']);
        }
        //获取配置文件
        $multiSetVal = BySettingEnvRepository::multiSetVal(['dept_cpo_id','wpd_manage_department_id']);

        //2.1 cpo 组织下员工  查看"问题所在网点"为“Head office”的工单数据
        $departmentRepository = new DepartmentRepository();
        $aboutCPODepartment   = $departmentRepository->getDepartmentSubListByIds($multiSetVal['dept_cpo_id'] ?? 0);
        if (in_array($staffInfo['node_department_id'], array_column($aboutCPODepartment, 'id'))) {
            return ['conditions' => 'store_id = :source_store_id:', 'bind' => ['source_store_id' => '-1']];
        }

        //2.2 Warehouse Procurement Department组织以及下级组织下的员工
        $aboutCPODepartment   = $departmentRepository->getDepartmentSubListByIds( GlobalEnums::PH_WAREHOUSE_PROCUREMENT_DEPARTMENT);
        if (in_array($staffInfo['node_department_id'], array_column($aboutCPODepartment, 'id'))) {
            // 查看Network Management一级部门以及子部门员工提交的数据（未来支持配置更多的部门以及子部门）
            $manageDepartmentId = $multiSetVal['wpd_manage_department_id'] ? explode(',',
                $multiSetVal['wpd_manage_department_id']) : [];
            if (!empty($manageDepartmentId)) {
                $manageAllDepartment = [];
                foreach ($manageDepartmentId as $v) {
                    $manageAllDepartment = array_merge($manageAllDepartment,
                        $departmentRepository->getDepartmentSubListByIds($v));
                }
                $manageAllDepartment = array_column($manageAllDepartment, 'id');
                return [
                    'conditions' => 'node_department_id in ({node_department_id:array})',
                    'bind'       => [
                        'node_department_id' => array_values($manageAllDepartment) ?: ['-100'],
                    ],
                ];
            }
        }
        //2.3 其他组织下员工
        //负责的部门及子部门
        $managerDepartment    = $departmentRepository->getDepartmentAllListByManagerId($staffInfo['id']);
        $managerDepartmentIds = array_column($managerDepartment, 'id');
        //负责的 大区 片区 网点 to 网点
        $managerAllStoreIds = SysService::getInstance()->getManagerAllStoreIds($staffInfo['id']);
        //所在一级部门及子部门  & 负责的组织以及下级组织
        $selfDepartment              = $departmentRepository->getDepartmentSubListByIds($staffInfo['sys_department_id']);
        $selfAndManagerDepartmentIds = array_merge(array_column($selfDepartment, 'id'), $managerDepartmentIds);
        return [
            'conditions' => 'sys_store_id in ({sys_store_id:array}) OR node_department_id in ({node_department_id:array})',
            'bind'       => [
                'node_department_id' => array_values($selfAndManagerDepartmentIds) ?: ['-100'],
                'sys_store_id'       => $managerAllStoreIds ?: ['-100'],
            ],
        ];
    }

    /**
     * https://flashexpress.feishu.cn/docx/CPeidBehAom9FYxS9L2ckbKrnNg
     * 获取员工行政工单的数据权限条件
     * @param $staffInfo
     * @return array|bool
     */
    protected function getStaffPermission($staffInfo)
    {
        if (get_country_code() != Enums\GlobalEnums::TH_COUNTRY_CODE) {
            return true;
        }
        
        //角色
        $roleList = (new HrStaffRepository())->getStaffRoleList($staffInfo['id']);
        $roleIds  = !empty($roleList) ? array_column($roleList, 'position_category') : [];

        // 1.1 HRIS管理员[41] 超级管理员[99] HR系统管理员[115] 系统管理员[14]
        if (array_intersect($roleIds, [
            RoleEnums::ROLE_HRIS,
            RoleEnums::ROLE_SUPER_ADMIN,
            RoleEnums::ROLE_HR_SYSTEM_ADMIN,
            RoleEnums::ROLE_SYSTEM_ADMIN,
        ])) {
            return true;
        }
        //1.2 HRBP[68] 查看自己管辖范围内的员工提交的工单数据
        if (in_array(RoleEnums::ROLE_HRBP, $roleIds)) {
            return StaffService::getInstance()->staffManageRangeCondition($staffInfo['id']);
        }
        //获取配置文件
        $multiSetVal = BySettingEnvRepository::multiSetVal([
            'dept_cpo_id',
            'dept_hub_management_id',
            'dept_hub_admin_hq_id',
            'dept_flash_freight_hub_dep_id',
        ]);

        //2.1 cpo 组织下员工  查看"问题所在网点"为“Head office”的工单数据
        $departmentRepository = new DepartmentRepository();
        $aboutCPODepartment   = $departmentRepository->getDepartmentSubListByIds($multiSetVal['dept_cpo_id'] ?? 0);
        if (in_array($staffInfo['node_department_id'], array_column($aboutCPODepartment, 'id'))) {
            return ['conditions' => 'store_id = :source_store_id:', 'bind' => ['source_store_id' => '-1']];
        }

        //负责的部门及子部门
        $managerDepartment    = $departmentRepository->getDepartmentAllListByManagerId($staffInfo['id']);
        $managerDepartmentIds = array_column($managerDepartment, 'id');

        //2.2 Hub Management组织及子部门以及Flash Freight Hub组织及子部门
        $aboutHubManagementDepartment              = $departmentRepository->getDepartmentSubListByIds($multiSetVal['dept_hub_management_id'] ?? 0);
        $aboutFlashFreightHubDepartment            = $departmentRepository->getDepartmentSubListByIds($multiSetVal['dept_flash_freight_hub_dep_id'] ?? 0);
        $hubManagementAndFlashFreightHubDepartment = array_column(array_merge($aboutHubManagementDepartment,
            $aboutFlashFreightHubDepartment), 'id');

        //负责的 大区 片区 网点 to 网点
        $managerAllStoreIds = SysService::getInstance()->getManagerAllStoreIds($staffInfo['id']);
        if (in_array($staffInfo['node_department_id'], $hubManagementAndFlashFreightHubDepartment)) {
            // 2.2.1 特殊组织（Hub Admin (HQ)）下员工：
            //查看Hub Management一级部门以及子部门&Flash Freight Hub一级部门以及子部门  下员工提交的工单
            if ($staffInfo['node_department_id'] == $multiSetVal['dept_hub_admin_hq_id'] ?? 0) {
                return [
                    'conditions' => 'node_department_id in ({node_department_id:array}) ',
                    'bind'       => ['node_department_id' => $hubManagementAndFlashFreightHubDepartment],
                ];
            }
            //2.2.2 其他员工：
            //查看自己所属网点下员工提交的工单+负责的组织以及下级组织内员工提交的数据（如果是组织负责人的话）
            return [
                'conditions' => 'sys_store_id in ({sys_store_id:array}) OR node_department_id in ({node_department_id:array})',
                'bind'       => [
                    'sys_store_id'       => array_values(array_merge([$staffInfo['organization_type'] == 1 ? $staffInfo['organization_id'] : 0],
                        $managerAllStoreIds)),
                    'node_department_id' => $managerDepartmentIds ?: ['-100'],
                ],
            ];
        }

        //3.1 其他组织下员工
        //所在一级部门及子部门  & 负责的组织以及下级组织
        $selfDepartment              = $departmentRepository->getDepartmentSubListByIds($staffInfo['sys_department_id']);
        $selfAndManagerDepartmentIds = array_merge(array_column($selfDepartment, 'id'), $managerDepartmentIds);
        return [
            'conditions' => 'sys_store_id in ({sys_store_id:array}) OR node_department_id in ({node_department_id:array})',
            'bind'       => [
                'node_department_id' => array_values($selfAndManagerDepartmentIds) ?: ['-100'],
                'sys_store_id'       => $managerAllStoreIds ?: ['-100'],
            ],
        ];
    }



    /**
     * 分页处理
     * @Date  11/25/22 12:56 PM
     * @param array $condition 查询器对象
     * @return  array
     * @author: peak pan
     */
    public function getList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['start_created_at]']) && isset($condition['end_created_at]']) && $condition['start_created_at]'] > $condition['end_created_at]']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            $builder = $this->modelsManager->createBuilder();
            $columns = [
                'main.id',
                'main.order_code',
                'main.created_at',
                'main.created_staff_id',
                'main.created_staff_name',
                'main.question_type_id',
                'main.store_id',
                'main.first_deal_staff_name',
                'main.first_deal_staff_time',
                'main.first_deal_staff_id',
                'main.last_deal_staff_id',
                'main.last_deal_staff_name',
                'main.last_deal_staff_time',
                'main.updated_at',
                'main.status'
            ];
            $builder->from(['main' => AdministrationOrderModel::class]);
            $builder->innerjoin(HrStaffInfoModel::class, 'main.created_staff_id = staff.staff_info_id', 'staff');
            //组合搜索条件
            $builder = $this->getTicketCondition($builder, $condition);
            $count   = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                $builder->orderBy('main.id DESC');
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('行政工单-分页处理-列表异常信息:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 导出数据
     * @Date 11/25/22 12:56 PM
     * @param array $condition 查询器对象
     * @return  array
     * @author: peak pan
     */
    public function getExportList(array $condition)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num  = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset    = $page_size * ($page_num - GlobalEnums::DEFAULT_PAGE_NUM);

        $code    = ErrCode::$SUCCESS;
        $message = 'success';
        $data    = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            if (isset($condition['start_created_at]']) && isset($condition['end_created_at]']) && $condition['start_created_at]'] > $condition['end_created_at]']) {
                throw new ValidationException(static::$t->_('start_and_date_error'), ErrCode::$VALIDATE_ERROR);
            }
            $builder = $this->modelsManager->createBuilder();
            $columns = [
                'main.order_code',
                'main.created_at',
                'main.created_staff_name',
                'main.created_staff_id',
                'main.created_department_name',
                'main.created_store_name',
                'main.question_type_id',
                'main.store_id',
                'main.info',
                'main.status',
                'main.first_deal_staff_name',
                'main.first_deal_staff_id',
                'main.first_deal_staff_time',
                'main.last_deal_staff_name',
                'main.last_deal_staff_id',
                'main.updated_at',
                'main.created_job_title_name',
                'main.close_staff_id',
                'main.close_time',
                'main.last_deal_staff_time'
            ];

            $builder->from(['main' => AdministrationOrderModel::class]);
            $builder->innerjoin(HrStaffInfoModel::class, 'main.created_staff_id = staff.staff_info_id', 'staff');
            //组合搜索条件
            $builder = $this->getTicketCondition($builder, $condition);
            $count   = (int)$builder->columns('COUNT(main.id) AS total')->getQuery()->getSingleResult()->total;
            if ($count) {
                $builder->columns($columns);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleExportItems($items);
            }
            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = $count;

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('行政工单-导出数据失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    /**
     * 回复数据
     * @Date  11/25/22 12:56 PM
     * @param int $order_id 查询条件
     * @return  array
     * @author: peak pan
     */
    public function replyList($order_id)
    {
        $administration_log_arr = AdministrationLogModel::find([
            'columns'    => 'id,created_staff_id,created_staff_name,created_type,mark,pics,created_at',
            'conditions' => ' order_id = :order_id: ',
            'bind'       => ['order_id' => $order_id],
            'order'      => 'id ASC',
        ])->toArray();

        if (empty($administration_log_arr)) {
            return [];
        } else {
            return $this->handleReply($administration_log_arr);
        }
    }

    /**
     * 回复列表格式化数据
     * @Date  11/25/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleReply(array $items)
    {
        if (empty($items)) {
            return [];
        }
        foreach ($items as &$item) {
            $item['pics'] = json_decode($item['pics'], true) ?? '';
        }
        return $items;
    }


    /**
     * 格式化数据
     * @Date  11/25/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        $store_ids = array_values(array_unique(array_filter(array_column($items, 'store_id'))));
        if (!empty($store_ids)) {
            $store_arr = SysStoreModel::find([
                'columns'    => 'id,name',
                'conditions' => ' id in ({id:array}) ',
                'bind'       => ['id' => $store_ids]

            ])->toArray();
            if (!empty($store_arr)) {
                $store_data = array_column($store_arr, 'name', 'id');
            }
        }

        $question_type_arr = AdministrationQuestionTypeModel::find([
            'conditions' => 'is_deleted = :is_deleted:',
            'bind'       => [
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns'    => 'id, t_key'
        ])->toArray();

        if (!empty($question_type_arr)) {

            $question_type_arr = array_column($question_type_arr, 't_key', 'id');
        }
        foreach ($items as &$item) {
            $item['status_name']           = static::$t[AdministrationTicketEnums::$administration_ticket_status[$item['status']]];
            $item['first_deal_staff_name'] = $item['first_deal_staff_name'] ?? '';
            $item['first_deal_staff_time'] = $item['first_deal_staff_time'] ?? '';
            $item['last_deal_staff_name']  = $item['last_deal_staff_name'] ?? '';
            $item['last_deal_staff_time']  = $item['last_deal_staff_time'] ?? '';
            $item['store_name']            = $store_data[$item['store_id']] ?? '';
            if ($item['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $item['store_name']  = Enums::PAYMENT_HEADER_STORE_NAME;
            }
            $item['question_type_name']    = !empty($item['question_type_id']) ? static::$t->_($question_type_arr[$item['question_type_id']]) : '';
            $item['first_deal_staff_id'] = !empty($item['first_deal_staff_id']) ? $item['first_deal_staff_id'] : '';
            $item['last_deal_staff_id'] = !empty($item['last_deal_staff_id']) ? $item['last_deal_staff_id'] : '';

            if(!empty($item['close_staff_id'])){
                $item['updated_at']            = $item['close_time'] ?? '';
            }
        }
        return $items;
    }


    /**
     * 导出数据处理
     * @Date  11/25/22 12:56 PM
     * @param array $items 数据
     * @return  array
     * @author: peak pan
     */
    private function handleExportItems(array $items)
    {
        if (empty($items)) {
            return [];
        }
        //问题所在网点
        $store_data = [];
        $store_ids  = array_values(array_unique(array_filter(array_column($items, 'store_id'))));
        if (!empty($store_ids)) {
            $store_arr = SysStoreModel::find([
                'columns'    => 'id,name',
                'conditions' => ' id in ({id:array}) ',
                'bind'       => ['id' => $store_ids]

            ])->toArray();
            if (!empty($store_arr)) {
                $store_data = array_column($store_arr, 'name', 'id');
            }
        }
        $staff_info_ids  = array_values(array_unique(array_filter(array_column($items, 'created_staff_id'))));
        $staff_info_data = [];

        $question_type_ids  = array_values(array_unique(array_filter(array_column($items, 'question_type_id'))));
        $question_type_data = [];
        if (!empty($question_type_ids)) {
            $question_type_arr = AdministrationQuestionTypeModel::find([
                'conditions' => ' id in ({id:array})',
                'bind'       => ['id' => $question_type_ids],
                'columns'    => 'id, t_key'
            ])->toArray();

            if (!empty($question_type_arr)) {
                $question_type_data = array_column($question_type_arr, 't_key', 'id');
            }
        }
        foreach ($items as &$item) {
            $item['store_name']            = $store_data[$item['store_id']] ?? '';
            if ($item['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $item['store_name']  = Enums::PAYMENT_HEADER_STORE_NAME;
            }
            $item['job_title']             = $item['created_job_title_name'] ?? '';
            $item['status_name']           = static::$t[AdministrationTicketEnums::$administration_ticket_status[$item['status']]];
            $item['question_type_name']    = !empty($question_type_data[$item['question_type_id']]) ? static::$t->_($question_type_data[$item['question_type_id']]) : '';
            $item['first_deal_staff_name'] = $item['first_deal_staff_name'];
            $item['first_deal_staff_time'] = $item['first_deal_staff_time'];
            $item['last_deal_staff_name']  = $item['last_deal_staff_name'];
            $item['last_deal_staff_time']  = $item['last_deal_staff_time'];
            $item['first_deal_staff_id']   = !empty($item['first_deal_staff_id']) ? $item['first_deal_staff_id'] : '';
            $item['last_deal_staff_id']    = !empty($item['last_deal_staff_id']) ? $item['last_deal_staff_id'] : '';
            if(!empty($item['close_staff_id'])){
                $item['updated_at']            = $item['close_time'] ?? '';
            }

        }
        return $items;
    }

    /**
     * 行政工单-列表-查询条件
     * @Date:11/25/22 9:35 PM
     * @param object $builder 处理的对象
     * @param array $condition 查询条件
     * @return  object
     **/
    private function getTicketCondition(object $builder, array $condition)
    {
        $order_code       = $condition['order_code'] ?? '';
        $question_type_id = $condition['question_type_id'] ?? '';
        $created_staff_id = $condition['created_staff_id'] ?? '';
        $status           = $condition['status'] ?? '';
        $start_created_at = $condition['start_created_at'] ?? '';
        $end_created_at   = $condition['end_created_at'] ?? '';
        $store_id         = $condition['store_id'] ?? '';


        if (!empty($order_code)) {
            $builder->andWhere('main.order_code like :order_code:', ['order_code' => $order_code . '%']);
        }

        if (!empty($question_type_id)) {
            $question_type_id = array_values($question_type_id);
            $builder->inWhere('main.question_type_id', $question_type_id);
        }

        if (!empty($created_staff_id)) {
            $builder->andWhere('main.created_staff_id like :created_staff_id: or main.created_staff_name like :created_staff_id:', ['created_staff_id' => '%' . $created_staff_id . '%']);
        }

        if (!empty($store_id)) {
            $builder->andWhere('main.store_id = :store_id:', ['store_id' => $store_id]);
        }

        if (!empty($status)) {
            $status = array_values($status);
            $builder->inWhere('main.status', $status);
        }
        //数据权限
        $permission = $this->getStaffPermissionUseCache($condition['staffInfo']);
        $this->logger->info(['getTicketConditionPermission' => $permission, 'condition' => $condition]);
        if (!empty($permission['conditions']) && !empty($permission['bind'])) {
            $builder->andWhere($permission['conditions'], $permission['bind']);
        }

        if (!empty($start_created_at) && !empty($end_created_at)) {
            $start_created_at .= ' 00:00:00';
            $end_created_at   .= ' 23:59:59';
            $builder->betweenWhere('main.created_at ', $start_created_at, $end_created_at);
        }

        return $builder;
    }


    /**
     * 行政工单-列表-查看
     * @Date:11/25/22 9:35 PM
     * @param int $id 查询条件
     * @return array
     * @author: peak pan
     **/
    public function getDetail(int $id)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {

            $administration_order = AdministrationOrderModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $id],
                'columns'    => ['id', 'order_code', 'question_type_id', 'store_id', 'line_id', 'created_staff_id', 'created_staff_name', 'created_department_name', 'created_store_id', 'mobile', 'info', 'status', 'created_job_title_name', 'close_time', 'close_staff_id', 'close_reason']
            ]);


            if (empty($administration_order)) {
                throw new ValidationException(static::$t->_('administration_order_empty_data'), ErrCode::$VALIDATE_ERROR);
            }
            $order_data = $administration_order->toArray();

            $store_data = [];
            if (!empty($order_data['order_data']) || !empty($order_data['created_store_id'])) {
                $ids       = array_values(array_filter(array_unique([$order_data['store_id'], $order_data['created_store_id']])));
                $store_arr = SysStoreModel::find([
                    'columns'    => 'id, name',
                    'conditions' => ' id in ({id:array}) ',
                    'bind'       => ['id' => $ids]
                ])->toArray();

                if (!empty($store_arr)) {
                    $store_data = array_column($store_arr, null, 'id');
                }
            }

            $order_data['store_name']         = $store_data[$order_data['store_id']]['name'] ?? '';
            $order_data['created_store_name'] = $store_data[$order_data['created_store_id']]['name'] ?? '';
            if ($order_data['store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $order_data['store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }

            if ($order_data['created_store_id'] == Enums::HEAD_OFFICE_STORE_FLAG) {
                $order_data['created_store_name'] = Enums::PAYMENT_HEADER_STORE_NAME;
            }

            $order_data['question_type_name'] = '';
            if (!empty($order_data['question_type_id'])) {
                $question_type_arr = AdministrationQuestionTypeModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => [
                        'id' => $order_data['question_type_id']
                    ],
                    'columns'    => 'id, t_key'
                ]);

                if (!empty($question_type_arr)) {
                    $order_data['question_type_name'] = static::$t->_($question_type_arr['t_key']);
                }
            }
            $data['administration_order'] = $order_data;

            $data['administration_reply'] = $this->replyList($order_data['id']);


            $staff_info_arr = [];
            $mark_msg = '';
            if(!empty($order_data['close_staff_id'])){
                $hr_staff_info = HrStaffInfoModel::findFirst([
                    'conditions' => 'staff_info_id = :staff_info_id:',
                    'bind'       => [
                        'staff_info_id' => $order_data['close_staff_id']
                    ],
                    'columns'    => 'id, name, staff_info_id'
                ]);
                if (!empty($hr_staff_info)) {
                    $staff_info_arr = $hr_staff_info->toArray();
                }
                $mark_msg = static::$t->_('close_administration_order_msg');
            }
            $order_close_data['created_at'] = $order_data['close_time'] ?? '';
            $order_close_data['created_staff_id'] = $order_data['close_staff_id'] ?? '';
            $order_close_data['created_staff_name'] = $staff_info_arr['name'] ?? '';
            $order_close_data['mark'] = $mark_msg;
            $order_close_data['close_reason'] = $order_data['close_reason'] ?? '';
            $data['administration_close'] = $order_close_data;
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('行政工单-列表-查看-获取详情失败:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];

    }


    /**
     * 行政工单-列表-关闭工单
     * @Date: 11/25/22 9:35 PM
     * @param array $params 条件
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function closeTicket(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {
            $administration_order = AdministrationOrderModel::find([
                'conditions' => ' id in ({id:array})  and status =:status: ',
                'bind'       => ['id' => $params['ids'], 'status' => AdministrationTicketEnums::ADMINISTRATION_TICKET_STATUS_CLOSE],
            ])->toArray();
            if (!empty($administration_order)) {
                throw new ValidationException(static::$t->_('administration_order_status_close_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            $current_time = date('Y-m-d H:i:s', time());
            $ids          = implode(',', array_filter(array_unique($params['ids'])));

            $bool = $db->updateAsDict(
                (new AdministrationOrderModel)->getSource(),
                [
                    'status'         => AdministrationTicketEnums::ADMINISTRATION_TICKET_STATUS_CLOSE,
                    'close_time'     => $current_time,
                    'close_reason'   => $params['close_reason'],
                    'close_staff_id' => $user['id'],
                    'updated_at'     => $current_time,
                ],
                [
                    'conditions' => " id IN ($ids)"
                ]
            );

            if ($bool === false) {
                throw new BusinessException('行政工单-列表-关闭工单操作记录失败' . json_encode($params, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($db), ErrCode::$ADMINISTRATION_TICKET_SAVE_ERROR);
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('行政工单-列表-关闭工单:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }


    /**
     *  行政工单-列表-导出
     * @Date: 11/25/22 9:35 PM
     * @param array $params 查询条件
     * @return  mixed
     * @author: peak pan
     **/
    public function downloadTicket(array $params)
    {
        $export_data = $this->exportData($params);
        $ticket_arr  = $export_data['data'] ?? [];
        return $this->getApplyExport($ticket_arr);
    }

    /**
     * 枚举
     * @return array
     */
    public function getDefaultData()
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data    = [];
        try {
            $ticket_status_arr =[];
            $administration_ticket_status = AdministrationTicketEnums::$administration_ticket_status;
            foreach ($administration_ticket_status as $key => $item) {
                $ticket_item['value']       = (string)$key;
                $ticket_item['label']       = static::$t->_($item);
                $ticket_status_arr[] = $ticket_item;
            }
            $data['ticket_status'] = $ticket_status_arr;

            $question_type_arr = AdministrationQuestionTypeModel::find([
                'conditions' => 'is_deleted = :is_deleted:',
                'bind'       => [
                    'is_deleted' => GlobalEnums::IS_NO_DELETED
                ],
                'columns'    => 'id as value,t_key AS label'
            ])->toArray();

            foreach ($question_type_arr as &$question) {
                $question['value'] = (string)$question['value'];
                $question['label'] = static::$t->_($question['label']);
            }
            $data['question_type_arr'] = $question_type_arr;

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
            $this->logger->warning('行政工单-枚举获取值异常: ' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }


    /**
     * 行政工单-列表-导出
     * @Date: 11/25/22 9:35 PM
     * @param array $condition 条件
     * @return array
     * @author: peak pan
     **/
    public function exportData(array $condition)
    {
        $code       = ErrCode::$SUCCESS;
        $message    = $real_message = '';
        $row_values = [[]];
        try {
            $limit_size            = AdministrationTicketEnums::TICKET_DOWNLOAD_LIMIT;
            $condition['pageNum']  = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = $limit_size;
            $data_count            = $this->getExportListCount($condition);
            if ($data_count > $limit_size) {
                throw new ValidationException(static::$t->_('export_data_download_limit_max_ticket'), ErrCode::$VALIDATE_ERROR);
            }
            $list        = $this->getExportList($condition);
            $total_count = $list['data']['pagination']['total_count'];
            if ($total_count) {
                $row_values = $list['data']['items'];
            }

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('行政工单列表导出:' . $message . $e->getTraceAsString());
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $row_values,
        ];

    }

    /**
     * 行政工单-列表-导出数据到xls
     * @Date: 10/15/22 11:04 PM
     * @param array $row_values 数据
     * @return array
     * @author: peak pan
     **/
    public function getApplyExport(array $row_values)
    {
        try {
            $apply_export_arr = [];
            foreach ($row_values as $value) {
                $apply_export_arr[] = [
                    $value['order_code'],
                    $value['created_at'],
                    $value['created_staff_name'],
                    $value['created_staff_id'],
                    $value['created_department_name'],
                    $value['job_title'],
                    $value['created_store_name'],
                    $value['question_type_name'],
                    $value['store_name'],
                    $value['info'],
                    $value['status_name'],
                    $value['first_deal_staff_name'],
                    $value['first_deal_staff_id'],
                    $value['first_deal_staff_time'],
                    $value['last_deal_staff_name'],
                    $value['last_deal_staff_id'],
                    $value['last_deal_staff_time'],
                    $value['updated_at']
                ];
            }
            $header            = [
                static::$t->_('administration_ticket_order_code'),//工单ID
                static::$t->_('administration_ticket_created_at'),//提交时间
                static::$t->_('administration_ticket_created_staff_name'),//提交人
                static::$t->_('administration_ticket_created_staff_id'),//提交人工号
                static::$t->_('administration_ticket_created_department_name'),//部门
                static::$t->_('administration_ticket_job_title'),//职位
                static::$t->_('administration_ticket_created_store_name'),//所属网点
                static::$t->_('administration_ticket_question_type_name'),//问题类型
                static::$t->_('administration_ticket_store_name'),//问题所在网点
                static::$t->_('administration_ticket_info'),//问题详情
                static::$t->_('administration_ticket_status_name'),//处理状态
                static::$t->_('administration_ticket_first_deal_staff_name'),//首次回复人
                static::$t->_('administration_ticket_first_deal_staff_id'),//首次回复人工号
                static::$t->_('administration_ticket_first_deal_staff_time'),//首次回复时间
                static::$t->_('administration_ticket_last_deal_staff_name'),//最后回复人
                static::$t->_('administration_ticket_last_deal_staff_id'),//最后回复人工号
                static::$t->_('administration_ticket_close_time'), //最后回复时间
                static::$t->_('administration_ticket_updated_at'),//最后更新时间
            ];
            $file_name         = 'administration_ticket_export_' . date('YmdHis');
            $result            = $this->exportExcel($header, $apply_export_arr, $file_name);
            $result['message'] = $result['code'] == ErrCode::$SUCCESS ? 'success' : $result['data'];

            return $result;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->warning('行政工单列表导出数据封装导出:' . $message . $e->getTraceAsString());
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }


    /**
     * 行政工单-列表-导出 数据计算
     * @param array $condition 条件
     * @return int
     */
    public function getExportListCount($condition)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => AdministrationOrderModel::class]);
        $builder->innerjoin(HrStaffInfoModel::class, 'main.created_staff_id = staff.staff_info_id', 'staff');
        $builder = $this->getTicketCondition($builder, $condition);
        return (int)$builder->columns('COUNT(main.id ) as total')->getQuery()->getSingleResult()->total;
    }


    /**
     * 查询网点列表
     * @Date: 11/25/22 2:25 PM
     * @param array $params 条件
     * @return array
     **@author: peak pan
     */
    public function getStoreData(array $params)
    {
        return DepositService::getInstance()->getStoreData($params);
    }


    /**
     * 行政工单修改
     * @Date: 11/25/22 9:35 PM
     * @param array $params 条件
     * @param array $user 用户
     * @return array
     * @author: peak pan
     **/
    public function handle(array $params, array $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_backyard');
        $db->begin();
        try {

            $current_time         = date('Y-m-d H:i:s', time());
            $administration_order = AdministrationOrderModel::findFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $params['id']],
            ]);
            if (empty($administration_order)) {
                throw new ValidationException(static::$t->_('administration_order_empty_data'), ErrCode::$VALIDATE_ERROR);
            }

            if ($administration_order->status == AdministrationTicketEnums::ADMINISTRATION_TICKET_STATUS_CLOSE) {
                throw new ValidationException(static::$t->_('administration_order_status_close_error'), ErrCode::$VALIDATE_ERROR);
            }

            $tmpList = [];
            if (!empty($params['attachments'])) {
                foreach ($params['attachments'] as $attachment) {
                    $tmp             = [];
                    $tmp['pic_name'] = $attachment['file_name'];
                    $tmp['pic_path'] = $attachment['object_url'];
                    $tmpList[]       = $tmp;
                }
            }

            $ticket_data['order_id']           = $params['id'] ?? '';
            $ticket_data['created_staff_id']   = $user['id'];
            $ticket_data['created_staff_name'] = $user['name'];
            $ticket_data['created_type']       = AdministrationTicketEnums::TICKET_CREATED_TYPE_ANSWER;
            $ticket_data['mark']               = $params['mark'];
            $ticket_data['pics']               = !empty($tmpList) ? json_encode($tmpList, JSON_UNESCAPED_UNICODE) : '';
            $ticket_data['created_at']         = $current_time;
            $ticket_data['is_deleted']         = GlobalEnums::IS_NO_DELETED;
            $record                            = new AdministrationLogModel();
            $bool                              = $record->create($ticket_data);
            if ($bool === false) {
                throw new BusinessException('行政工单-列表-处理工单操作记录失败(handle) = ' . json_encode([$ticket_data], JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($record), ErrCode::$ADMINISTRATION_TICKET_LOG_ADD_ERROR);
            }

            //如果是第一次回复
            if ($administration_order->status == AdministrationTicketEnums::ADMINISTRATION_TICKET_STATUS_ING) {
                if (empty($administration_order->first_deal_staff_id)) {
                    $ticket_order['first_deal_staff_id']   = $user['id'];
                    $ticket_order['first_deal_staff_name'] = $user['name'];
                    $ticket_order['first_deal_staff_time'] = $current_time;
                }

                $ticket_order['status']                = AdministrationTicketEnums::ADMINISTRATION_TICKET_STATUS_REPLY;
            }
            $ticket_order['last_deal_staff_id']   = $user['id'];
            $ticket_order['last_deal_staff_name'] = $user['name'];
            $ticket_order['last_deal_staff_time'] = $current_time;
            $ticket_order['updated_at']           = $current_time;
            $ticket_order_bool                    = $administration_order->i_update($ticket_order);
            if ($ticket_order_bool === false) {
                throw new BusinessException('行政工单-列表-处理工单修改失败' . json_encode($ticket_order, JSON_UNESCAPED_UNICODE) . ' 可能的原因是: ' . get_data_object_error_msg($administration_order), ErrCode::$ADMINISTRATION_ORDER_SAVE_ERROR);
            }

            if (!empty($params['attachments'])) {
                $attach_bool = $this->addAttachments($params, Enums::OSS_ADMINISTRATION_TICKET_HANDLE_ADD, $record->id);
                if ($attach_bool === false) {
                    throw new BusinessException('行政工单-列表-处理工单操作记录附件创建失败： ' . json_encode($params['attachment'], JSON_UNESCAPED_UNICODE), ErrCode::$ADMINISTRATION_TICKET_ATTACHMENTS_ADD_ERROR);
                }
            }
            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('行政工单-列表-处理工单数据异常原因是:' . $real_message);
        }
        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];

    }

    /**
     * 批量添加附件
     * @Date: 11/25/22 9:35 PM
     * @param array $data 入参数据
     * @param string $oss_bucket_key
     * @param string $oss_bucket_type
     * @return array
     * @author: peak pan
     **/
    public function addAttachments($data, $oss_bucket_type, $oss_bucket_key)
    {
        $attach  = new AttachModel();
        $tmpList = [];
        foreach ($data['attachments'] as $attachment) {
            $tmp                    = [];
            $tmp['oss_bucket_type'] = $oss_bucket_type;
            $tmp['oss_bucket_key']  = $oss_bucket_key;
            $tmp['sub_type']        = 0;
            $tmp['bucket_name']     = $attachment['bucket_name'];
            $tmp['object_key']      = $attachment['object_key'];
            $tmp['file_name']       = $attachment['file_name'];
            $tmpList[]              = $tmp;
        }
        $attach_bool = $attach->batchInsert($tmpList);
        return $attach_bool;
    }


    /**
     * 脚本自动处理工单权限
     * @throws Exception
     */
    public function autoEditPermission(){

        $last_updated_at = date('Y-m-d H:i:s');

        // [1] 读取 by 库 setting_evn中的配置信息
        $env_config = BySettingEnvRepository::multiSetVal([
            'administrative_work_order_staff_id',
            'administrative_work_order_job_title',
            'administrative_work_order_department_id',
        ]);
        // [2] 读取拥有该职位的所有用户id、读取所属部门下的所有员工信息
        $all_staff_ids = [];
        $env_staff_ids = !empty($env_config['administrative_work_order_staff_id']) ? explode(',',
            $env_config['administrative_work_order_staff_id']) : [];
        if (!empty($env_staff_ids)) {
            $all_staff_ids = array_merge($all_staff_ids, $env_staff_ids);
        }

        $dept_ids        = !empty($env_config['administrative_work_order_department_id']) ? explode(',',
            $env_config['administrative_work_order_department_id']) : [];
        $job_list        = !empty($env_config['administrative_work_order_job_title']) ? explode(',',
            $env_config['administrative_work_order_job_title']) : [];
        $staff_info_list = $this->getStaffList($dept_ids, $job_list);
        if (!empty($staff_info_list)) {
            $staff_ids     = array_column($staff_info_list, 'staff_info_id');
            $all_staff_ids = array_merge($all_staff_ids, $staff_ids);
        }

        if (empty($all_staff_ids)) {
            $this->logger->info('脚本自动处理工单权限 无数据');
            return true;
        }
        //获取员工权限
        $staff_permission_arr = StaffPermissionModel::find(
            [
                'conditions' => 'staff_id in ({staff_id:array})',
                'bind'       => [
                    'staff_id' => array_values($all_staff_ids),
                ],
                'columns'    => ['id', 'staff_id', 'permission_ids'],
            ])->toArray();
        $staff_permission_arr = array_column($staff_permission_arr, null, 'staff_id');
        /**
         * [3] 工号汇总 挨个查询 是否有
         * [
         *      935 => '行政工单',  // 主菜单
         *      936 => '行政工单-列表、搜索',
         *      937 => '行政工单-查看',
         *      938 => '行政工单-导出',
         *      939 => '行政工单-关闭工单', // todo 脚本不处理 关闭的权限
         *      940 => '行政工单-处理工单'
         * ]
         */
        $all_staff_ids  = array_values(array_unique($all_staff_ids));
        $permission_ids = [935, 936, 937, 938, 940];
        $add_permission = [];
        foreach ($all_staff_ids as $key => $staff_id) {
            //没有权限，需要给加上
            if (empty($staff_permission_arr[$staff_id])) {
                // 插入
                $add_permission[] = [
                    'staff_id'        => $staff_id,
                    'permission_ids'  => implode(',', $permission_ids),
                    'is_granted'      => 1,
                    'last_updated_at' => $last_updated_at,
                ];
                continue;
            }

            // 拿到该用户拥有的权限
            $all_permission_ids = explode(',', $staff_permission_arr[$staff_id]['permission_ids']);
            $diff_permission    = array_diff($permission_ids, $all_permission_ids);
            if (!empty($diff_permission)) {
                $permission_v_ids = implode(',', array_merge($all_permission_ids, $diff_permission));
                $sql              = "UPDATE staff_permission SET `permission_ids` = '{$permission_v_ids}', `last_updated_at` = '{$last_updated_at}' WHERE staff_id = {$staff_id}";
                $bool             = $this->getDI()->get('db_oa')->execute($sql);
                if ($bool === false) {
                    throw new Exception("权限更新失败: staff_id = $staff_id, permission = $permission_v_ids");
                }
                $this->logger->info("权限更新成功: staff_id = $staff_id, permission = $permission_v_ids");
            }
        }

        // 批量插入
        if (!empty($add_permission)) {
            $res = (new StaffPermissionModel())->batch_insert($add_permission);
            if ($res === false) {
                throw new Exception('本批次批量新增失败: 员工共 ' . count($add_permission) . '个, 明细: ' . json_encode($add_permission,
                        JSON_UNESCAPED_UNICODE));
            }
            $this->logger->info(['autoEditPermission' => $add_permission]);
        }
        return true;
    }


    /**
     * 根据部门id 或者 职位 id 获取员工信息
     * @param array $dept_ids
     * @param array $job_ids
     * @return array
     */
    private function getStaffList(array $dept_ids, array $job_ids): array
    {
        if (empty($dept_ids) && empty($job_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['hsi.id', 'hsi.staff_info_id']);
        $builder->from(['hsi' => HrStaffInfoModel::class]);
        $builder->inWhere('hsi.formal', [StaffInfoEnums::FORMAL_IN, StaffInfoEnums::FORMAL_TRAINEE]);
        $builder->andWhere('hsi.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
        $builder->andWhere('hsi.is_sub_staff = :is_sub_staff:', ['is_sub_staff' => StaffInfoEnums::IS_SUB_STAFF_NO]);
        // 部门id 、职位id同时存在
        if (!empty($dept_ids) && !empty($job_ids)) {
            $builder->andWhere('hsi.node_department_id IN ({node_department_ids:array}) OR hsi.job_title IN ({job_ids:array})',
                [
                    'node_department_ids' => $dept_ids,
                    'job_ids'             => $job_ids,
                ]
            );
        }
        // 只存在部门id
        if (!empty($dept_ids) && empty($job_ids)) {
            $builder->andWhere('hsi.node_department_id IN ({node_department_ids:array})',
                ['node_department_ids' => $dept_ids]);
        }

        // 只是存在职位id
        if (empty($dept_ids) && !empty($job_ids)) {
            $builder->andWhere('hsi.job_title IN ({job_ids:array})', ['job_ids' => $job_ids]);
        }

        return $builder->getQuery()->execute()->toArray();
    }






}
