<?php

namespace App\Modules\AdministrationTicket\Services;

class BaseService extends \App\Library\BaseService
{
    //列表查询
    public static $validate_ticket_list = [
        'order_code'       => 'StrLenGeLe:0,32|>>>:order_code  error',
        'question_type_id' => 'Arr|ArrLenGeLe:0,3|>>>: question_type_id  error',
        'created_staff_id' => 'StrLenGeLe:0,50|>>>:created_staff_id  error',
        'status'           => 'Arr|ArrLenGeLe:0,3|>>>: status  error',
        'start_created_at' => 'Date|>>>:start_created_at error',
        'end_created_at'   => 'Date|>>>:end_created_at error',
        'pageNum'          => 'IntGe:1|>>>:pageNum error',
        'pageSize'         => 'IntGe:1|>>>:pageSize error'
    ];

    //导出工单
    public static $validate_administration_ticket_export = [
        'order_code'       => 'StrLenGeLe:0,32|>>>:order_code  error',
        'question_type_id' => 'Arr|ArrLenGeLe:0,3|>>>: question_type_id  error',
        'created_staff_id' => 'StrLenGeLe:0,50|>>>:created_staff_id  error',
        'status'           => 'Arr|ArrLenGeLe:0,3|>>>: status  error',
        'start_created_at' => 'Date|>>>:start_created_at error',
        'end_created_at'   => 'Date|>>>:end_created_at error'
    ];

    //回复列表
    public static $validate_reply_list = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    //详情查看
    public static $validate_administration_ticket_view = [
        'id' => 'Required|IntGe:1|>>>:id error',
    ];

    //回复
    public static $validate_handle = [
        'id'          => 'Required|IntGe:1|>>>:id error',
        'mark'        => 'Required|StrLenGeLe:1,500|>>>:mark error',
        'attachments' => 'Arr|ArrLenGeLe:0,5|>>>:main attachment list  error[Upload up to 5]',
    ];

    //批量关闭工单
    public static $validate_administration_ticket_close = [
        'ids'    => 'Required|Arr|ArrLenGeLe:1,1000||>>>:ids error',
        'close_reason' => 'StrLenGeLe:0,1000|>>>:reason error',
    ];

    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must = [])
    {
        $params = array_filter($params);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }


}
