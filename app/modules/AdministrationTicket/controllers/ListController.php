<?php
/**
 * 行政工单
 */

namespace App\Modules\AdministrationTicket\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\AdministrationTicket\Services\TicketListService;
use App\Modules\AdministrationTicket\Services\BaseService;
use App\Util\RedisKey;



class ListController extends BaseController
{

    /**
     * 行政工单-枚举接口接口
     * @Token
     * @Date: 11/24/22 8:49 PM
     * @return  \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63583
     * @author: peak pan
     **/
    public function getOptionsDefaultAction()
    {
        $res = TicketListService::getInstance()->getDefaultData();
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }


    /**
     * 行政工单-列表查询接口
     * @Permission(action='administration_ticket.list.ticket_list')
     * @Date 11/24/22 4:28 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63588
     **/

    public function ticketListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_ticket_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $params['staffInfo'] = $this->user;
        $res = TicketListService::getInstance()->getList($params, $this->user['id']);

        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);

    }

    /**
     * 搜索网点
     * @Token
     * @Date: 11/24/22 2:49 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63593
     **@author: peak pan
     */
    public function getSearchStoreAction()
    {
        $params = $this->request->get();
        $res    = TicketListService::getInstance()->getStoreData($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 行政工单-列表-查看
     * @Permission(action='administration_ticket.list.view')
     * @Date: 11/24/22 4:28 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63589
     **/
    public function viewAction()
    {
        $param = $this->request->get();
        try {
            Validation::validate($param, BaseService::$validate_administration_ticket_view);
        } catch (ValidationException $e) {
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
        $res = TicketListService::getInstance()->getDetail($param['id']);
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);

    }


    /**
     * 行政工单-列表-处理工单
     * @Permission(action='administration_ticket.list.handle')
     * @Date: 11/24/22 4:28 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63592
     **/
    public function handleAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, BaseService::$validate_handle);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = TicketListService::getInstance()->handle($params, $this->user);
        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], []);
    }


    /**
     * 行政工单-列表-关闭工单
     * @Permission(action='administration_ticket.list.close')
     * @Date: 11/24/22 4:28 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63591
     **/
    public function closeAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, []);
        try {
            Validation::validate($params, BaseService::$validate_administration_ticket_close);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = TicketListService::getInstance()->closeTicket($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
    }

    /**
     * 行政工单-列表-导出
     * @Permission(action='administration_ticket.list.export')
     * @Date: 11/25/22 4:28 PM
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @author: peak pan
     * @yapi https://yapi.flashexpress.pub/project/133/interface/api/63590
     **/
    public function exportAction()
    {
        $param           = $this->request->get();
        $param           = BaseService::handleParams($param, []);
        $param['export'] = true;
        $param['staffInfo'] = $this->user;
        try {
            Validation::validate($param, BaseService::$validate_administration_ticket_export);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        // 加锁处理
        $lock_key = md5(RedisKey::ADMINISTRATION_TICKET_EXPORT_LOCK.'_' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($param) {

            return TicketListService::getInstance()->downloadTicket($param);
        }, $lock_key, 10);

        return $this->returnJson($res['code'] ?? ErrCode::$SUCCESS, $res['message'] ?? '', $res['data'] ?? []);
    }

}
