<?php

namespace App\Modules\Vendor\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\User\Services\UserService;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\DetailService;
use App\Modules\Vendor\Services\ListService;
use App\Modules\Vendor\Services\VendorFlowService;
use App\Modules\Workflow\Services\FYRService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AuditController extends BaseController
{
    /**
     * 审批/搜索列表
     * @Permission(action='vendor.audit.approve')
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['uid'] = $this->user['id'];
        $list = ListService::getInstance()->getList($params, BaseService::SUB_MODULE_TYPE_AUDIT);
        if ($list['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $list['data']);
        }
        return $this->returnJson($list['code'], $list['message']);
    }

    /**
     * 审批详情
     * @Permission(action='vendor.audit.approve')
     * @return mixed
     */
    public function detailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail = DetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_AUDIT,$this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $detail);
    }

    /**
     * 通过
     * @Permission(action='vendor.audit.approve')
     * @return mixed
     */
    public function approvalAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_approval params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$approval_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = VendorFlowService::getInstance()->approve($data['vendor_id'], $data['remark'], $this->user);

        $this->logger->info('vendor_approval result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] === ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 驳回
     * @Permission(action='vendor.audit.approve')
     * @return mixed
     */
    public function rejectedAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_rejected params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$rejected_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = VendorFlowService::getInstance()->reject($data['vendor_id'], $data['remark'], $this->user);

        $this->logger->info('vendor_rejected result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        if ($res['code'] === ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商意见征询
     *
     * @Token
     * @Date: 2021-09-26 09:44
     * @return Response|ResponseInterface
     * @author: peak pan
     */
    public function askAction()
    {
        $data=$this->request->get();
        $biz_id = $this->request->get('vendor_id');
        $note = $this->request->get('note','trim');
        $to_staffs = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');
        try {
            Validation::validate($data, BaseService::$ask_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $request = ListService::getInstance()->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

}
