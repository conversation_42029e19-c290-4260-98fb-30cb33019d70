<?php

namespace App\Modules\Vendor\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\GradeChangeService;
use App\Modules\Vendor\Services\GradeDetailService;
use App\Modules\Vendor\Services\GradeListService;
use Exception;

class VendorGradeController extends BaseController
{
    /**
     * 供应商分级注册变更
     * @Permission(action='vendor.grade.register')
     * @return mixed
     */
    public function registerAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_register params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$grade_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = GradeChangeService::getInstance()->register($data, $this->user);

        $this->logger->info('vendor_register result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级注销变更
     * @Permission(action='vendor.grade.cancellation')
     * @return mixed
     */
    public function cancellationAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_cancellation params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$grade_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = GradeChangeService::getInstance()->cancellation($data, $this->user);

        $this->logger->info('vendor_cancellation result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级拉黑变更
     * @Permission(action='vendor.grade.block')
     * @return mixed
     */
    public function blockAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_block params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$grade_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = GradeChangeService::getInstance()->block($data, $this->user);

        $this->logger->info('vendor_block result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级认证变更
     * @Permission(action='vendor.grade.authentication')
     * @return mixed
     */
    public function authenticationAction()
    {
        $data = $this->request->get();

        $this->logger->info('vendor_authentication params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$grade_authentication_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = GradeChangeService::getInstance()->authentication($data, $this->user);

        $this->logger->info('vendor_authentication result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级认暂停更
     * @Permission(action='vendor.grade.suspend')
     * @return mixed
     */
    public function suspendAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_suspend params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$grade_suspend_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = GradeChangeService::getInstance()->suspend($data, $this->user);

        $this->logger->info('vendor_suspend result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级管理列表
     * @Permission(action='vendor.grade.view')
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GradeListService::$not_must_params);
        try {
            Validation::validate($params, GradeListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = GradeListService::getInstance()->getList($params, BaseService::SUB_MODULE_TYPE_INFO);

        return $this->returnJson($list['code'], $list['message'], $list['data']);
    }

    /**
     * 供应商分级管理详情
     * @Permission(action='vendor.grade.view')
     * @return mixed
     */
    public function detailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }


    /**
     * 供应商分级管理列表导出
     * @Permission(action='vendor.grade.view')
     *
     * @return mixed
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GradeListService::$not_must_params);
        try {
            Validation::validate($params, GradeListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('supplier_list_grade_download_' . $this->user['id']);
        $result   = $this->atomicLock(function () use ($params) {
            return GradeListService::getInstance()->getExportData($params, $this->user);
        }, $lock_key, 20);

        return $this->returnJson($result['code'] ?? ErrCode::$SYSTEM_ERROR, $result['message'] ?? $this->t->_('retry_later'), ['file_url' => $result['data'] ?? '']);
    }

    /**
     * 供应商等级注册查看
     * @Permission(action='vendor.grade.register')
     * @return mixed
     * */
    public function registerDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }

    /**
     * 供应商等级暂停查看
     * @Permission(action='vendor.grade.suspend')
     * @return mixed
     * */
    public function suspendDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }

    /**
     * 供应商等级认证查看
     * @Permission(action='vendor.grade.authentication')
     * @return mixed
     * */
    public function authenticationDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }

    /**
     * 供应商等级注销查看
     * @Permission(action='vendor.grade.cancellation')
     * @return mixed
     * */
    public function cancellationDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }

    /**
     * 供应商等级拉黑查看
     * @Permission(action='vendor.grade.block')
     * @return mixed
     * */
    public function blockDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }


}
