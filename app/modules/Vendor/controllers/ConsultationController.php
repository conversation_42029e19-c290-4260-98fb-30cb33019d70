<?php

namespace App\Modules\Vendor\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\DetailService;
use App\Modules\Vendor\Services\ListService;
use App\Modules\Vendor\Services\GradeDetailService;
use App\Modules\Vendor\Services\GradeListService;

use App\Modules\Workflow\Services\FYRService;

class ConsultationController extends BaseController
{
    /**
     * 审批详情
     * @Token
     */
    public function detailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail = DetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_AUDIT,$this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $detail);
    }

    /**
    * 供应商回复list
    * @Token
    * @Date: 2021-09-26 09:45
    * @author: peak pan
    **/
    public function replyListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $list = ListService::getInstance()->replyList($params, $this->user['id'], BaseService::SUB_MODULE_TYPE_REPLY);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
    * 回复征询
    * @Token
    * @Date: 2021-09-26 10:06
    * @author: peak pan
    **/
    public function replyAction()
    {
        $ask_id = $this->request->get('ask_id','int');
        $note = $this->request->get('note','trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 供应商回复list
     * @Token
     **/
    public function gradeReplyListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $list = GradeListService::getInstance()->replyList($params, $this->user['id'], BaseService::SUB_MODULE_TYPE_REPLY);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * 供应商分级管理详情
     * @Token
     * @return mixed
     */
    public function gradeDetailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_AUDIT, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }
}
