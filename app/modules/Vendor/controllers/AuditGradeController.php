<?php

namespace App\Modules\Vendor\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\User\Services\UserService;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\GradeDetailService;
use App\Modules\Vendor\Services\GradeListService;
use App\Modules\Vendor\Services\VendorGradeFlowService;
use App\Modules\Workflow\Services\FYRService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AuditGradeController extends BaseController
{
    /**
     * 审批/搜索列表
     * @Permission(action='vendor.audit.grade_approve')
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, GradeListService::$not_must_params);
        try {
            Validation::validate($params, GradeListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['uid'] = $this->user['id'];
        $list          = GradeListService::getInstance()->getList($params, BaseService::SUB_MODULE_TYPE_AUDIT);
        return $this->returnJson($list['code'], $list['message'], $list['data']);

    }

    /**
     * 审批详情
     * @Permission(action='vendor.audit.grade_approve')
     * @return mixed
     */
    public function detailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail    = GradeDetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_AUDIT, $this->user['id']);
        return $this->returnJson($detail['code'], $detail['message'], $detail['data']);
    }

    /**
     * 通过
     * @Permission(action='vendor.audit.grade_approve')
     * @return mixed
     */
    public function approvalAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_approval params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$approval_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = VendorGradeFlowService::getInstance()->approve($data['vendor_id'], $data['remark'], $this->user);

        $this->logger->info('vendor_approval result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 驳回
     * @Permission(action='vendor.audit.grade_approve')
     * @return mixed
     */
    public function rejectedAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_rejected params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        try {
            Validation::validate($data, BaseService::$rejected_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = VendorGradeFlowService::getInstance()->reject($data['vendor_id'], $data['remark'], $this->user);

        $this->logger->info('vendor_rejected result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商分级管理意见征询
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function askAction()
    {
        $data        = $this->request->get();
        $biz_id      = $this->request->get('vendor_id');
        $note        = $this->request->get('note', 'trim');
        $to_staffs   = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');
        try {
            Validation::validate($data, BaseService::$ask_params_validate);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $request   = GradeListService::getInstance()->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result    = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 回复征询
     * @Token
     **/
    public function replyAction()
    {
        $ask_id      = $this->request->get('ask_id', 'int');
        $note        = $this->request->get('note', 'trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

}
