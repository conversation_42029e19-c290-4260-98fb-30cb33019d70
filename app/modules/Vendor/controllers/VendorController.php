<?php

namespace App\Modules\Vendor\Controllers;

use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Vendor\Services\AddService;
use App\Modules\Vendor\Services\BaseService;
use App\Modules\Vendor\Services\DetailService;
use App\Modules\Vendor\Services\DeleteService;
use App\Modules\Vendor\Services\ListService;
use App\Modules\Vendor\Services\UpdateService;
use Exception;

class VendorController extends BaseController
{
    /**
     * 供应商添加
     * @Permission(action='contract.vendor.create')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function addAction()
    {
        $data = $this->request->get();
        $this->logger->info('vendor_add params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        BaseService::commonParamsValidate($data);

        $res = AddService::getInstance()->one($data, $this->user);
        $this->logger->info('vendor_add result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商列表
     * @Permission(action='contract.vendor.search')
     * @return mixed
     */
    public function listAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = ListService::getInstance()->getList($params, BaseService::SUB_MODULE_TYPE_INFO, false, $limit = 20);
        if ($list['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $list['data']);
        }
        return $this->returnJson($list['code'], $list['message']);    }

    /**
     * 供应商详情
     * @Permission(action='contract.vendor.view')
     * @return mixed
     */
    public function detailAction()
    {
        $vendor_id = $this->request->get('vendor_id');
        $detail = DetailService::getInstance()->getDetail($vendor_id, BaseService::SUB_MODULE_TYPE_INFO,$this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $detail);
    }

    /**
     * 供应商编辑
     * @Permission(action='contract.vendor.edit')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function updateAction()
    {
        $data = $this->request->get();

        $this->logger->info('vendor_update params: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        BaseService::commonParamsValidate($data, true);

        $res = UpdateService::getInstance()->one($data['vendor_id'], $data, $this->user);
        $this->logger->info('vendor_update result: ' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 枚举配置接口
     * @Token
     * @return mixed
     */
    public function staticAction()
    {
        $res = (new BaseService())->getStaticItem();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商列表导出
     * @Permission(action='vendor.vendor.export')
     *
     * @return mixed
     * @throws Exception
     */
    public function exportAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_params);
        try {
            Validation::validate($params, ListService::$validate_list_search);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('supplier_list_download_' . $this->user['id']);
        $result = $this->atomicLock(function() use ($params){
            return ListService::getInstance()->getExportData($params, $this->user);
        }, $lock_key, 20);

        return $this->returnJson($result['code'], $result['message'], ['file_url' => $result['data']]);
    }

    /**
     * 供应商信息查询
     * @Token
     * @return mixed
     * @throws ValidationException
     */

    public function searchVendorAction(){
        $params = $this->request->get();
        $params = BaseService::handleParams($params, ListService::$not_must_search_vendor);
        $params = trim_array($params);
        Validation::validate($params, ListService::$validate_search_vendor);
        //默认值
        $application_module_id = !empty($params['application_module_id']) ? $params['application_module_id'] : VendorEnums::VENDOR_MODULE_OTHER;
        $source = !empty($params['source']) ? $params['source'] : VendorEnums::VENDOR_SEARCH_OTHER;
        //查询
        $res = ListService::getInstance()->searchVendorTypeList($params['search_name'], $application_module_id, '', $source);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 供应商删除(16465删除这个接口)
     * @Permission(action='contract.vendor.delete')
     * @return mixed
     */
    public function deleteAction(){
        try{
            $vendor_id = $this->request->get('vendor_id');
            DeleteService::getInstance()->deleteData($vendor_id,$this->user['id']);
            return $this->returnJson(ErrCode::$SUCCESS, 'success');
        }catch(Exception $e){
            return $this->returnJson($e->getCode(), $e->getMessage());
        }
    }

    /**
     * @Token
     * 费用公司枚举
     * */
    public function getCompanyAction()
    {
        $data = DetailService::getInstance()->getCooCompany();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 供应商作废
     * @Permission(action='vendor.vendor.invalid')
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     * @date 2023/4/27
     */
    public function invalidAction()
    {
        $params = $this->request->get();
        Validation::validate($params, UpdateService::$validate_valid);
        $res = UpdateService::getInstance()->invalid($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
