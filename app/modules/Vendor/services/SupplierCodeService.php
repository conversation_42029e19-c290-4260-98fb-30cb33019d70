<?php
/**
 * Created by PhpStorm.
 * Date: 2022/3/21
 * Time: 20:49
 */

namespace App\Modules\Vendor\Services;


use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Modules\Material\Models\MaterialAssetCodeSendModel;
use App\Modules\Material\Services\AssetCodeService;
use App\Modules\Vendor\Models\SupplierCodeModel;

class SupplierCodeService extends BaseService
{
    private $retry_max = 5;
    private static $instance;

    /**
     *
     * @return SupplierCodeService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 生成编码
     *
     * @param $supplier_number
     * @return string
     */
    public function createSupplierCode($supplier_number){

        return 'OA'.$supplier_number;
    }

    /**
     * 获取编码流水号
     *
     * @param int $num
     * @param int $retry
     * @return array
     * @throws BusinessException
     */
    public function createSupplierNumber(int $num,int $retry=1){
        if ($retry>$this->retry_max){
            throw new BusinessException('create_supplier_number_failed',ErrCode::$CREATE_SERIAL_CODE_ERROR);
        }

        $supplier_code = SupplierCodeModel::find()->toArray();
        $supplier_code = $supplier_code[0]??[];
        $db = $this->getDI()->get('db_oa');
        $supplier_code_model = new SupplierCodeModel();
        //本次流水号
         $supplier_num= $supplier_code['code_number'] + $num;

        //占用流水号
        $update_success = $db->updateAsDict(
            $supplier_code_model ->getSource(),
            [
                'code_number' => $supplier_num
            ],
            [
                'conditions'=>'id=? and code_number=?',
                'bind' => [$supplier_code['id'],$supplier_code['code_number']]
            ]
        );
        if (!$update_success){
            $this->createSupplierNumber($num,$retry+1);
        }
        //返回本批流水号
        $supplier_number_array = [];
        for ($i=$supplier_code['code_number']+1;$i<=$supplier_num;$i++){
            $supplier_number_array[] = sprintf("%05d",$i);
        }
        return $supplier_number_array;
    }

}