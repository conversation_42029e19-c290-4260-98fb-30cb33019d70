<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorModifyLogModel;
use App\Modules\Vendor\Models\VendorPaymentDetailModel;
use App\Models\oa\VendorPurchaseRelationModel;
use App\Modules\Vendor\Models\VendorRecordLogModel;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;
use App\Modules\Purchase\Services\OrderService;

class UpdateService extends BaseService
{
    // 编辑更新如下字段, 则需审批

    protected static $need_audit_pay_fields=[
        'bank_pay_name',
        'bank_account_name',
        'bank_no',
    ];

    public static $not_must_params = [
        'company_website',
        'artificial_person',
        'company_phone',
        'attachment_arr',
        'sap_supplier_no'
    ];

    protected static $record_pay_fields=[
        'bank_pay_name',
        'bank_account_name',
        'bank_no',
    ];

    public static $validate_valid = [
        'vendor_id' => 'StrLenGeLe:1,32',//供应商编号
        'vendor_reason' => 'StrLenGeLe:1,2000',//供应商备注
    ];

    private static $instance;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $vendor_id
     * @param $updateData
     * @param $user
     * @return array
     */
    public function one($vendor_id, $updateData, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            unset($updateData['id']);

            //增加页面禁止修改的字段
            unset($updateData['status']);
            unset($updateData['audit_stage']);
            unset($updateData['last_audit_time']);
            unset($updateData['audit_approved_count']);
            unset($updateData['approval_version_no']);
            $updateData['purchase_currency'] = empty($updateData['purchase_currency']) ? 0 : $updateData['purchase_currency'];

            $db = $this->getDI()->get('db_oa');
            $db->begin();

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $vendor_id]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_INFO_GET_ERROR);
            }

            if ($vendor->grade_status == VendorEnums::VENDOR_GRADE_STATUS_INVALID) {//作废不可编辑
                throw new ValidationException(self::$t['vendor_info.040'], ErrCode::$VALIDATE_ERROR);
            }

            //当前不可有等级状态审批
            if (Enums::WF_STATE_PENDING == $vendor->grade_approve_status) {
                throw new ValidationException(self::$t['vendor_info.007'], ErrCode::$VALIDATE_ERROR);
            }

            //查询行数据
            $pay_details = $vendor->getDetails()->toArray();
            $pay_details = array_column($pay_details, null, 'id');

            $vendor_info = $vendor->toArray();
            $pays_data = $updateData['pays'];

            /**
             * 【10945】OA系统 - 供应商管理信息优化
             * 可编辑的权限：
             *  1.审批结束&&已驳回下重新提交的情况
             *  2.审批结束&&审批通过&&是指定工号内的才能编辑
             *
             * 【20865】供应商编辑权限优化
             *  审批结束&&已驳回下重新提交的情况 - 不变
             *  审批结束&&审批通过&&以下三种情况满足其一
             *      员工属于store_access_staff_id配置的员工，则所有供应商信息均可以编辑
             *      员工属于供应商编辑-采购人员配置的员工，则所有供应商信息均可以编辑
             *      员工为当前供应商的创建人，则可以编辑当前供应商
             */
            $can_edit_staffs = $this->getAuditPassCanEditStaffs();
            $can_edit_staffs_arr = $can_edit_staffs ? explode(",", $can_edit_staffs) : [];
            //供应商编辑-采购人员
            $vendor_edit_purchase_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('vendor_edit_purchase_staff_ids');
            if ($vendor_info['audit_stage'] != self::WF_AUDIT_STATE_END || !in_array($vendor_info['status'], [Enums::WF_STATE_REJECTED, Enums::WF_STATE_APPROVED])
                || ($vendor_info['status'] == Enums::WF_STATE_APPROVED && !(in_array($user['id'], $can_edit_staffs_arr) || in_array($user['id'], $vendor_edit_purchase_staff_ids) || $user['id'] == $vendor_info['create_id']))) {
                throw new ValidationException(static::$t->_('audit_processing_not_allowed_edit'), ErrCode::$VALIDATE_ERROR);
            }
            if (strstr($updateData['vendor_name'], '&')) {
                throw new ValidationException(self::$t['vendor_info.003'], ErrCode::$VALIDATE_ERROR);
            }

            // 验证供应商名称是否唯一
            $exist_vendor_info = $this->getVendorByName($updateData['vendor_name']);
            if (!empty($exist_vendor_info) && ($exist_vendor_info['vendor_name'] != $updateData['vendor_name'] || $exist_vendor_info['vendor_id'] != $updateData['vendor_id'])) {
                throw new ValidationException(self::$t['vendor_info.001'], ErrCode::$VALIDATE_ERROR);
            }

            //10945【OA】供应商管理信息优化 增加了支付方式，如果是银行转帐或者两种支付方式都有时查询开户行,银行账号是否已存在
            // 查询开户行,银行账号是否已存在
            //多条判重
            if (!empty($pays_data)) {
                foreach ($pays_data as $pay_item) {
                    $exist_vendor_bank = $this->getVendorByBank($pay_item['bank_no']);
                    if (!empty($exist_vendor_bank)) {
                        if (isset($pay_item['id']) && $pay_item['bank_no'] == $exist_vendor_bank[0]['bank_no'] && $pay_item['id'] == $exist_vendor_bank[0]['id']) {
                            //跳过自身
                            continue;
                        }

                        throw new ValidationException(self::$t['vendor_info.002'], ErrCode::$VALIDATE_ERROR);
                    }
                }
            }

            // 应用模块
            $application_module_arr = $updateData['application_module_arr'];
            $product = $updateData['product_category'];

            // 资质附件
            $attachment_arr = $updateData['attachment_arr'];
            // bir2303 附件
            $bir2303_arr = $updateData['bir2303_arr'];

            // 注册文件(公司/个人)
            $registration_file = $updateData['registration_file'];

            // 银行对账单信头
            $bank_statement_letterhead_file = $updateData['bank_statement_letterhead_file'];

            // 所得税表头(公司/个人)
            $income_tax_form_header_file = $updateData['income_tax_form_header_file'];

            // SST登记表/SST登记信
            $sst_registration_form_file = $updateData['sst_registration_form_file'];

            //修改日志
            $attachment_edit = $updateData['attachment_edit'] ?? [];
            unset($updateData['application_module_arr']);
            unset($updateData['attachment_arr']);
            unset($updateData['attachment_edit']);
            unset($updateData['pays']);
            unset($updateData['product_category']);
            unset($updateData['registration_file'], $updateData['bank_statement_letterhead_file'], $updateData['income_tax_form_header_file'], $updateData['sst_registration_form_file']);


            //采购模块下 必填校验
            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr)) {
                if (empty($updateData['purchase_type']) || empty($product)) {
                    throw new ValidationException(self::$t['vendor_info.006'], ErrCode::$VALIDATE_ERROR);
                }
                //供应商引入理由
                if (empty($updateData['is_purchase_reason'])) {
                    throw new ValidationException(self::$t['vendor_info.008'], ErrCode::$VALIDATE_ERROR);
                }
            }
            if (empty($updateData['purchase_type'])) {
                $updateData['purchase_type'] = 0;
            }

            if ((!empty($updateData['purchase_amount'])) && (!is_numeric($updateData['purchase_amount']) || $updateData['purchase_amount'] < 0)) {
                throw new ValidationException(self::$t['vendor_info.009'], ErrCode::$VALIDATE_ERROR);
            }
            $purchase_amount_arr = explode('.', $updateData['purchase_amount']);


            if (strlen($purchase_amount_arr[0]) > self::INTEGER_AMOUNT_LENGTH || (isset($purchase_amount_arr[1]) && strlen($purchase_amount_arr[1]) > self::DECIMAL_AMOUNT_LENGTH) || (strlen($purchase_amount_arr[0]) > 1 && substr($purchase_amount_arr[0], 0, 1) == 0)) {
                throw new ValidationException(self::$t['vendor_info.009'], ErrCode::$VALIDATE_ERROR);
            }

            // 主表数据维护
            // 1. 是否涉及敏感字段修改
            $is_involved_sensitive_fields_modified = false;
            foreach (self::$need_audit_pay_fields as $field_i) {
                //行数据
                foreach ($pays_data as $pay_k => $pay_v) {
                    if (!isset($pay_v['id']) || !isset($pay_details[$pay_v['id']])) {//修改的时候新增行数据走审批
                        $is_involved_sensitive_fields_modified = true;
                        break;
                    }
                    if ($field_i == 'bank_no' && strcmp((string)$pay_v[$field_i], (string)$pay_details[$pay_v['id']][$field_i]) != 0) {
                        $is_involved_sensitive_fields_modified = true;
                        break;
                    }
                    if ($pay_v[$field_i] != $pay_details[$pay_v['id']][$field_i]) {
                        $is_involved_sensitive_fields_modified = true;
                        break;
                    }
                }
            }

            // 2. 是否需审批(仅在需审批时，主表数据才需删除重建)
            // 需要审批的场景: 1. 涉及敏感字段修改 且 终态审批已通过; 2. 驳回态的任一提交，均需重新发起审批
            $is_need_audit = false;
            if ($vendor_info['status'] == Enums::WF_STATE_REJECTED) {
                $is_need_audit = true;
            } else if ($is_involved_sensitive_fields_modified && $vendor_info['status'] == Enums::WF_STATE_APPROVED) {
                //【10945】OA系统 - 供应商管理信息优化需求修改为审批通过后特定的工号修改敏感信息需要走审批流
                $is_need_audit = true;
            }

            /**
             * 9650 新增需求 :
             * 如果上述条件不满足的话需进行以下判断
             * 如果审批中,但修改了应用模块,且修改前没有没选采购,修改后选了采购; 或 修改前选了采购,修改后没选采购;需要重建审批(审批中的审批流没有走采购审批,或审批流无需走采购)
             * 如果审批通过,未修改敏感字段,修改了应用模块,且修改前没有没选采购,修改后选了采购,只需走采购审批
             * */
            //判断是否修改应用模块 0.未修改;1.修改前没有采购,修改后选中采购;2.修改前有采购,修改后没有采购
            $module_add_purchase = 0;
            $vendor_modules = $this->getVendorModule($vendor_info['vendor_id']);
            $modules = array_column($vendor_modules, 'application_module_id');
            if (!in_array(1, $modules) && in_array(1, $application_module_arr)) {
                $module_add_purchase = 1;
            }
            if (in_array(1, $modules) && !in_array(1, $application_module_arr)) {
                $module_add_purchase = 2;
            }
            $only_purchase_audit = 0;//是否只需要采购审批 1.需要 0.不需要
            if ($is_need_audit == false) {
                if ($vendor_info['status'] == Enums::WF_STATE_PENDING && $module_add_purchase != 0) {
                    $is_need_audit = true;
                } elseif (Enums::WF_STATE_APPROVED && $module_add_purchase == 1) {
                    //【10945】OA系统 - 供应商管理信息优化需求修改为审批通过后特定的工号修改敏感信息需要走审批流
                    $is_need_audit = true;
                    $only_purchase_audit = 1;
                }
            }

            //如果
            // 未开始审批 或 终审通过 或 仅终审通过一次后的驳回, 如下字段不可修改
            // 供应商名称、供应商归属地、公司性质、证件类型、证件号码
            if (
                $vendor_info['audit_stage'] == self::WF_AUDIT_STATE_UN_STARTED
                ||
                $vendor_info['status'] == Enums::WF_STATE_APPROVED
                ||
                (
                    $vendor_info['audit_approved_count'] == 1
                    &&
                    $vendor_info['status'] == Enums::WF_STATE_REJECTED
                )
            ) {
                // $updateData['vendor_name'] = $vendor_info['vendor_name'];
                $updateData['ownership'] = $vendor_info['ownership'];
                $updateData['company_nature'] = $vendor_info['company_nature'];
                $updateData['certificate_type'] = $vendor_info['certificate_type'];
                $updateData['identification_no'] = $vendor_info['identification_no'];
            }

            // 日志数据收集
            $after_data = [];
            $before_data = $vendor_info;
            $before_data['application_module_arr'] = [];
            $before_data['attachment_arr'] = [];
            $before_data['bir2303_arr'] = [];
            $before_data['registration_file'] = [];
            $before_data['bank_statement_letterhead_file'] = [];
            $before_data['income_tax_form_header_file'] = [];
            $before_data['sst_registration_form_file'] = [];

            $updateData['updated_at'] = date('Y-m-d H:i:s');
            $updateData['updated_id'] = $user['id'];
            //使用公司
            $cost_company_id = array_column($updateData['use_company'], 'cost_company_id');
            $updateData['use_company_id'] = implode(',', $cost_company_id);
            $del_ids = array_diff(array_keys($pay_details), array_column($pays_data, 'id'));
            unset($updateData['use_company']);
            if (!empty($del_ids)) {
                $is_need_audit = true;
            }
            $del_ids = array_values($del_ids);

            if ($is_need_audit) {
                $updateData['status'] = Enums::WF_STATE_PENDING;
                $updateData['create_id'] = $vendor_info['create_id'];
                $updateData['create_name'] = $vendor_info['create_name'];
                $updateData['created_at'] = $vendor_info['created_at'];
                $updateData['last_audit_time'] = $vendor_info['last_audit_time'];
                $updateData['last_auditor_id'] = $vendor_info['last_auditor_id'];
                $updateData['audit_rejected_count'] = $vendor_info['audit_rejected_count'];
                $updateData['audit_approved_count'] = $vendor_info['audit_approved_count'];
                $updateData['approval_version_no'] = $vendor_info['approval_version_no'];
                $updateData['audit_stage'] = self::WF_AUDIT_STATE_UN_STARTED;
                $updateData['only_purchase_audit'] = $only_purchase_audit;//只走采购审批
            } else {
                // 更新数据
                $updateData['only_purchase_audit'] = $only_purchase_audit;
                $cost_company_ids = OrderService::getInstance()->getEnvByCode('vendor_sap_company_ids');
                $cost_company_ids = (string)$cost_company_ids;
                //供应商sap号不存在且存在sap费用公司 触发发送sap
                if (in_array($cost_company_ids, $cost_company_id) && empty($updateData['sap_supplier_no'])) {
                    $updateData['is_send_sap'] = 1;
                }

                //判断是否同步金碟 kingdee_company_id
                $updateData['is_send_kingdee'] = $this->isSendKingDee($cost_company_id);
            }

            //付款信息处理 新增 删除修改
            $pay_insert_data = [];
            $pay_log_data = [];
            $pay_record_field = self::$record_pay_fields;
            foreach ($pays_data as $pay_key => $pay_value) {
                unset($pay_value['payment_method_text']);
                unset($pay_value['bank_no_is_required']);

                $temp = [];
                $pay_value['updated_at'] = date('Y-m-d H:i:s');

                //新增
                if (!isset($pay_value['id'])) {
                    $pay_value['main_id'] = $vendor->id;
                    $pay_value['created_at'] = date('Y-m-d H:i:s');
                    $pay_insert_data[] = $pay_value;
                    foreach ($pay_record_field as $field) {
                        $temp[] = [
                            'id' => '',
                            'field' => $field,
                            'before' => '',
                            'after' => $pay_value[$field],
                            'payment_method' => $pay_value['payment_method']
                        ];
                    }
                    if (!empty($temp)) {
                        $pay_log_data[] = $temp;
                    }
                    continue;
                }

                //查询
                $detail_model = VendorPaymentDetailModel::getFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $pay_value['id']]
                ]);
                foreach ($pay_record_field as $field) {
                    if (strcmp((string)$pay_value[$field], (string)$pay_details[$pay_value['id']][$field]) != 0) {
                        $temp[] = [
                            'id' => $pay_value['id'],
                            'field' => $field,
                            'before' => $pay_details[$pay_value['id']][$field],
                            'after' => $pay_value[$field],
                            'payment_method' => $pay_value['payment_method']
                        ];
                    }
                }

                if (!empty($temp)) {
                    $pay_log_data[] = $temp;
                }
                unset($pay_value['id']);
                $detail_bool = $detail_model->update($pay_value);
                if ($detail_bool === false) {
                    throw new BusinessException('供应商编辑 - 付款信息表更新失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
                }
            }

            if (!empty($del_ids)) {
                foreach ($del_ids as $del_id) {
                    $temp = [];
                    foreach ($pay_record_field as $field) {
                        $temp[] = [
                            'id' => $del_id,
                            'field' => $field,
                            'before' => $pay_details[$del_id][$field],
                            'after' => '',
                            'payment_method' => $pay_details[$del_id]['payment_method']
                        ];
                    }
                    if (!empty($temp)) {
                        $pay_log_data[] = $temp;
                    }
                }
                $detail_delete_bool = VendorPaymentDetailModel::find([
                    'conditions' => 'id IN ({ids:array})',
                    'bind' => ['ids' => $del_ids]
                ])->delete();

                if ($detail_delete_bool === false) {
                    throw new BusinessException('供应商编辑 - 付款信息表删除失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
                }
            }

            if (!empty($pay_insert_data)) {
                $pay_model = new VendorPaymentDetailModel();
                $pay_bool = $pay_model->batch_insert($pay_insert_data);
                if ($pay_bool === false) {
                    throw new BusinessException('供应商修改- 付款信息创建失败 = ' . json_encode($pay_insert_data, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                }
            }
            //员工不属于store_access_staff_id配置的员工，不可编辑SAP供应商号码、金蝶供应商号码
            if (!in_array($user['id'], $can_edit_staffs_arr)) {
                unset($updateData['sap_supplier_no'], $updateData['kingdee_supplier_no']);
            }
            $update_res = $vendor->i_update($updateData);
            if ($update_res === false) {
                throw new BusinessException('供应商编辑 - 主表更新失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }
            $after_data = $vendor->toArray();

            // 应用模块关系删除重建
            $exist_application_modules = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $vendor_info['vendor_id']]
            ]);

            foreach ($exist_application_modules as $module) {
                if ($module->delete() === false) {
                    throw new BusinessException('供应商编辑 - 应用模块关系删除失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
                }

                $before_data['application_module_arr'][] = (int)$module->application_module_id;
            }

            if (!empty($application_module_arr)) {
                foreach ($application_module_arr as $am_id) {
                    $module_data = [
                        'vendor_id' => $vendor_info['vendor_id'],
                        'application_module_id' => $am_id
                    ];

                    $rel_model = new VendorApplicationModuleRelModel();
                    $rel_bool = $rel_model->i_create($module_data);
                    if ($rel_bool === false) {
                        throw new BusinessException('供应商编辑 - 应用模块关系重建失败 = ' . json_encode($module_data, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                    }
                }
            }

            //主供产品/服务类型写入
            //删除旧的产品关系表
            $del_bool = VendorPurchaseRelationModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $vendor_id]
            ])->delete();
            if ($del_bool === false) {
                throw new BusinessException('供应商编辑 - 主供/服务关系删除失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }

            if (!empty($product)) {
                //获取主供产品枚举
                $supply_products = (EnumsService::getInstance()->getSettingEnvValueMap('vendor_purchase_products'))['vendor_supply_products'] ?? [];
                $supply_products = array_column($supply_products, 'value');
                $relation_data = [];
                foreach ($product as $product_id) {
                    $relation_data[] = [
                        'vendor_id' => $vendor_id,
                        'purchase_product_type' => in_array($product_id, $supply_products) ? VendorEnums::VENDOR_PURCHASE_CATE_SUPPLY : VendorEnums::VENDOR_PURCHASE_CATE_SERVICE,
                        'product_category' => $product_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                $relation_model = new VendorPurchaseRelationModel();
                $relation_bool = $relation_model->batch_insert($relation_data);
                if ($relation_bool === false) {
                    throw new BusinessException('供应商提交 - 主供服务关系创建失败 = ' . get_data_object_error_msg($relation_model), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                }
            }

            // 原附件删除重建
            $exist_attaches = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND deleted = :deleted: and sub_type != :sub_type:',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                    'oss_bucket_key' => $vendor_info['id'],
                    'deleted' => Enums\GlobalEnums::IS_NO_DELETED,
                    'sub_type' => Enums::OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION
                ],
            ]);
            if (!empty($exist_attaches)) {
                foreach ($exist_attaches as $attache) {
                    if ($attache->delete() === false) {
                        throw new BusinessException('供应商编辑 - 附件删除失败', ErrCode::$VENDOR_UPDATE_INFO_ERROR);
                    }
                    $attache_item = [
                        'file_name' => $attache->file_name,
                        'bucket_name' => $attache->bucket_name,
                        'object_key' => $attache->object_key,
                        'oss_bucket_type' => $attache->oss_bucket_type,
                        'oss_bucket_key' => $attache->oss_bucket_key,
                    ];
                    if ($attache->sub_type == Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS;
                        $before_data['attachment_arr'][] = $attache_item;
                    } elseif ($attache->sub_type == Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303;
                        $before_data['bir2303_arr'][] = $attache_item;
                    } elseif ($attache->sub_type == Enums::OSS_SUB_TYPE_REGISTRATION_FILE) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_REGISTRATION_FILE;
                        $before_data['registration_file'][] = $attache_item;
                    } elseif ($attache->sub_type == Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE;
                        $before_data['bank_statement_letterhead_file'][] = $attache_item;
                    } elseif ($attache->sub_type == Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE;
                        $before_data['income_tax_form_header_file'][] = $attache_item;
                    } elseif ($attache->sub_type == Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE) {
                        $attache_item['sub_type'] = Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE;
                        $before_data['sst_registration_form_file'][] = $attache_item;
                    }
                }
            }

            // 资质附件
            if (!empty($attachment_arr)) {
                foreach ($attachment_arr as $k => $file) {
                    $attachment_arr[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $model->id ?? $vendor_info['id'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            } else {
                $attachment_arr = [];
            }

            // bir2303附件
            if (!empty($bir2303_arr)) {
                foreach ($bir2303_arr as $k => $file) {
                    $bir2303_arr[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $model->id ?? $vendor_info['id'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            } else {
                $bir2303_arr = [];
            }

            // 注册文件(公司/个人)
            if (!empty($registration_file)) {
                foreach ($registration_file as $k => $file) {
                    $registration_file[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_REGISTRATION_FILE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $vendor->id,
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            }

            // 银行对账单信头
            if (!empty($bank_statement_letterhead_file)) {
                foreach ($bank_statement_letterhead_file as $k => $file) {
                    $bank_statement_letterhead_file[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $vendor->id,
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            }

            // 所得税表头(公司/个人)
            if (!empty($income_tax_form_header_file)) {
                foreach ($income_tax_form_header_file as $k => $file) {
                    $income_tax_form_header_file[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $vendor->id,
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            }

            // SST登记表/SST登记信
            if (!empty($sst_registration_form_file)) {
                foreach ($sst_registration_form_file as $k => $file) {
                    $sst_registration_form_file[$k] = [
                        'sub_type' => Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key' => $vendor->id,
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                    ];
                }
            }

            // 附件信息入库
            $file_arr = array_merge($attachment_arr, $bir2303_arr, $registration_file, $bank_statement_letterhead_file, $income_tax_form_header_file, $sst_registration_form_file);

            $this->logger->info(['vendor_edit_batch_file_data' => $file_arr]);

            if (!empty($file_arr)) {
                $attach = new AttachModel();
                $attach_bool = $attach->batch_insert($file_arr);
                if ($attach_bool === false) {
                    throw new BusinessException('供应商编辑 - 附件批量创建失败', ErrCode::$VENDOR_ATTACHMENT_CREATE_ERROR);
                }
            }

            // 写入日志表
            $after_data['application_module_arr'] = $application_module_arr;
            $after_data['attachment_arr'] = $attachment_arr;
            $after_data['bir2303_arr'] = $bir2303_arr;
            $after_data['registration_file'] = $registration_file;
            $after_data['bank_statement_letterhead_file'] = $bank_statement_letterhead_file;
            $after_data['income_tax_form_header_file'] = $income_tax_form_header_file;
            $after_data['sst_registration_form_file'] = $sst_registration_form_file;

            $log = [
                'vendor_id' => $vendor_id,
                'before_data' => json_encode($before_data, JSON_UNESCAPED_UNICODE),
                'after_data' => json_encode($after_data, JSON_UNESCAPED_UNICODE),
                'create_id' => $user['id']
            ];
            if ((new VendorModifyLogModel())->i_create($log) === false) {
                throw new BusinessException('供应商修改 - 日志创建失败 = ' . json_encode($log, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_LOG_CREATE_ERROR);
            }

            if (!empty($pay_log_data)) {
                $pay_log_data = json_encode($pay_log_data, JSON_UNESCAPED_UNICODE);
                $pay_log = [
                    'vendor_id' => $vendor_id,
                    'pay_log' => $pay_log_data,
                    'attachment_edit' => json_encode($attachment_edit, JSON_UNESCAPED_UNICODE),
                    'edit_desc' => $updateData['edit_desc'] ?? '',
                    'create_id' => $user['id'],
                    'created_at' => date('Y-m-d H:i:s')

                ];
                $record_model = new VendorRecordLogModel();
                if ($record_model->i_create($pay_log) === false) {
                    throw new BusinessException('供应商修改 - 付款日志创建失败 = ' . json_encode($log, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_LOG_CREATE_ERROR);
                }
            }

            // 需审批
            if ($is_need_audit) {
                $flow_obj = new VendorFlowService();
                $req = $flow_obj->getRequest($vendor->id);
                if (empty($req)) {//不存在审批流的老版本单子走新建审批流
                    $flow_bool = $flow_obj->createRequest($vendor->id, $user);
                } else {
                    $flow_bool = $flow_obj->recommit($vendor, $user);
                }
                if ($flow_bool === false) {
                    throw new BusinessException('供应商编辑 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }
        $data['updated_at'] = date('Y-m-d H:i:s');
//        $data['attachment'] = $this->handle_oss_file($data['attachment_arr']);
        return $data;
    }

    /**
     * 供应商作废
     * @param $params
     * @return array
     * @date 2023/4/27
     */
    public function invalid($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //查询供应商信息
            $vendor_info = Vendor::findFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $params['vendor_id']]
            ]);
            if (empty($vendor_info)) {
                throw new ValidationException(static::$t->_('vendor_invalid_vendor_not_exist'), ErrCode::$VALIDATE_ERROR);
            }
            //允许作废的条件
            $allow_condition = $vendor_info->status == Enums::WF_STATE_APPROVED && $vendor_info->grade_status == VendorEnums::VENDOR_GRADE_STATUS_NORMAL && $vendor_info->grade_approve_status != Enums::WF_STATE_PENDING;
            //验证供应商是否可以作废
            if (!$allow_condition) {
                throw new ValidationException(static::$t->_('vendor_invalid_vendor_not_allow_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            $this->logger->info('vendor-invalid-params:' . json_encode($params, JSON_UNESCAPED_UNICODE));
            //修改供应商状态为作废
            $vendor_info->grade_status = VendorEnums::VENDOR_GRADE_STATUS_INVALID;
            $vendor_info->invalid_reason = $params['invalid_reason'];
            $vendor_info->invalid_at = date('Y-m-d H:i:s');
            if ($vendor_info->save() == false) {
                throw new BusinessException('vendor invalid save error , 可能的原因是' . get_data_object_error_msg($vendor_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getCode() . $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-invalid-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

}
