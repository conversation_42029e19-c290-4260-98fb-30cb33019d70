<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorModifyLogModel;
use App\Modules\Vendor\Models\VendorPaymentDetailModel;
use App\Models\oa\VendorPurchaseRelationModel;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;


class AddService extends BaseService
{
    public static $not_must_params = [
        'company_website',
        'artificial_person',
        'company_phone',
        'attachment_arr',
        'sap_supplier_no'
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    public function one($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();

            // 查询供应商名称是否重复
            if (strstr($data['vendor_name'], '&')) {
                throw new ValidationException(self::$t['vendor_info.003'], ErrCode::$VALIDATE_ERROR);
            }
            $exist_vendor_info = $this->getVendorByName($data['vendor_name']);
            if (!empty($exist_vendor_info)) {
                throw new ValidationException(self::$t['vendor_info.001'], ErrCode::$VALIDATE_ERROR);
            }
            //10945【OA】供应商管理信息优化 增加了支付方式，如果是银行转帐或者两种支付方式都有时查询开户行,银行账号是否已存在
            // 查询开户行,银行账号是否已存在
            //付款信息
            $pays_arr = $data['pays'];
            if (!empty($pays_arr)) {
                foreach ($pays_arr as $pay_item) {
                    $exist_vendor_bank = $this->getVendorByBank($pay_item['bank_no']);
                    if (!empty($exist_vendor_bank)) {
                        throw new ValidationException(self::$t['vendor_info.002'], ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
            //预计年采购金额校验
            if (!empty($data['purchase_amount']) && (!is_numeric($data['purchase_amount']) || $data['purchase_amount'] < 0)) {
                throw new ValidationException(self::$t['vendor_info.009'], ErrCode::$VALIDATE_ERROR);
            }
            $purchase_amount_arr = explode('.', $data['purchase_amount']);

            if (strlen($purchase_amount_arr[0]) > self::INTEGER_AMOUNT_LENGTH || (isset($purchase_amount_arr[1]) && strlen($purchase_amount_arr[1]) > self::DECIMAL_AMOUNT_LENGTH) || (strlen($purchase_amount_arr[0]) > 1 && substr($purchase_amount_arr[0], 0, 1) == 0)) {
                throw new ValidationException(self::$t['vendor_info.009'], ErrCode::$VALIDATE_ERROR);
            }

            $data = $this->handleData($data, $user);

            // 应用模块
            $application_module_arr = $data['application_module_arr'];
            $product = $data['product_category'];
            // 附件
            $attachment_arr = $data['attachment_arr'];
            //bir2303 附件
            $bir2303_arr = $data['bir2303_arr'];

            // 注册文件(公司/个人)
            $registration_file = $data['registration_file'];

            // 银行对账单信头
            $bank_statement_letterhead_file = $data['bank_statement_letterhead_file'];

            // 所得税表头(公司/个人)
            $income_tax_form_header_file = $data['income_tax_form_header_file'];

            // SST登记表/SST登记信
            $sst_registration_form_file = $data['sst_registration_form_file'];

            unset($data['application_module_arr'], $data['attachment_arr'], $data['bir2303_arr'], $data['pays'], $data['product_category']);
            unset($data['registration_file'], $data['bank_statement_letterhead_file'], $data['income_tax_form_header_file'], $data['sst_registration_form_file']);

            //采购模块下 必填校验
            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr)) {
                if (empty($data['purchase_type']) || empty($product)) {
                    throw new ValidationException(self::$t['vendor_info.006'], ErrCode::$VALIDATE_ERROR);
                }
                //供应商引入理由
                if (empty($data['is_purchase_reason'])) {
                    throw new ValidationException(self::$t['vendor_info.008'], ErrCode::$VALIDATE_ERROR);
                }
            }
            if (empty($data['purchase_type'])) {
                $data['purchase_type'] = 0;
            }
            //初始化供应商等级为未注册 供应商等级状态为草稿
            $data['grade'] = VendorEnums::VENDOR_GRADE_NOT_REGISTER;
            $data['grade_status'] = VendorEnums::VENDOR_GRADE_STATUS_DRAFT;
            // 主表写入
            $model = new Vendor();
            $bool = $model->i_create($data);
            if ($bool === false) {
                throw new BusinessException(static::$t->_('vendor_create_failed'), ErrCode::$VENDOR_CREATE_ERROR);
            }

            if (!empty($pays_arr)) {
                foreach ($pays_arr as &$pay_item) {
                    $pay_item['main_id'] = $model->id;
                    $pay_item['created_at'] = date('Y-m-d H:i:s');
                    $pay_item['updated_at'] = date('Y-m-d H:i:s');
                    unset($pay_item['bank_no_is_required']);
                }
                $pay_model = new VendorPaymentDetailModel();
                $pay_bool = $pay_model->batch_insert($pays_arr);
                if ($pay_bool === false) {
                    throw new BusinessException('供应商提交 - 付款信息创建失败 = ' . json_encode($pays_arr, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                }
            }

            // 应用模块关系写入
            if (!empty($application_module_arr)) {
                foreach ($application_module_arr as $am_id) {
                    $module_data = [
                        'vendor_id' => $model->vendor_id,
                        'application_module_id' => $am_id
                    ];

                    $rel_model = new VendorApplicationModuleRelModel();
                    $rel_bool = $rel_model->i_create($module_data);
                    if ($rel_bool === false) {
                        throw new BusinessException('供应商提交 - 应用模块关系创建失败 = ' . json_encode($module_data, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                    }
                }
            }

            //主供产品/服务类型写入
            if (!empty($product)) {
                //获取主供产品枚举
                $supply_products = (EnumsService::getInstance()->getSettingEnvValueMap('vendor_purchase_products'))['vendor_supply_products'];
                $supply_products = array_column($supply_products, 'value');
                $relation_data = [];
                foreach ($product as $product_id) {
                    $relation_data[] = [
                        'vendor_id' => $model->vendor_id,
                        'purchase_product_type' => in_array($product_id, $supply_products) ? VendorEnums::VENDOR_PURCHASE_CATE_SUPPLY : VendorEnums::VENDOR_PURCHASE_CATE_SERVICE,
                        'product_category' => $product_id,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }
                $relation_model = new VendorPurchaseRelationModel();
                $relation_bool = $relation_model->batch_insert($relation_data);
                if ($relation_bool === false) {
                    throw new BusinessException('供应商提交 - 主供服务关系创建失败 = ' . get_data_object_error_msg($relation_model), ErrCode::$VENDOR_APPLICATION_MODULE_CREATE_ERROR);
                }
            }

            // 所有附件
            $file_arr = [];

            // 资质附件上传
            if (!empty($attachment_arr)) {
                foreach ($attachment_arr as $k => $file) {
                    $attachment_arr[$k]['sub_type'] = Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS;
                    $attachment_arr[$k]['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_VENDOR_BACK;
                    $attachment_arr[$k]['oss_bucket_key'] = $model->id;
                    if (isset($attachment_arr[$k]['object_url'])) {
                        unset($attachment_arr[$k]['object_url']);
                    }

                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            } else {
                $attachment_arr = [];
            }

            //bir2303文件上传
            if (!empty($bir2303_arr)) {
                foreach ($bir2303_arr as $k => $file) {
                    $bir2303_arr[$k]['sub_type'] = Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303;
                    $bir2303_arr[$k]['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_VENDOR_BACK;
                    $bir2303_arr[$k]['oss_bucket_key'] = $model->id;
                    if (isset($bir2303_arr[$k]['object_url'])) {
                        unset($bir2303_arr[$k]['object_url']);
                    }

                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            }

            // 注册文件(公司/个人)
            if (!empty($registration_file)) {
                foreach ($registration_file as $file) {
                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_REGISTRATION_FILE,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            }

            // 银行对账单信头
            if (!empty($bank_statement_letterhead_file)) {
                foreach ($bank_statement_letterhead_file as $file) {
                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            }

            // 所得税表头(公司/个人)
            if (!empty($income_tax_form_header_file)) {
                foreach ($income_tax_form_header_file as $file) {
                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            }

            // SST登记表/SST登记信
            if (!empty($sst_registration_form_file)) {
                foreach ($sst_registration_form_file as $file) {
                    $file_arr[] = [
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'sub_type' => Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE,
                        'oss_bucket_key' => $model->id,
                        'file_name' => $file['file_name'],
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                    ];
                }
            }

            $this->logger->info(['vendor_add_batch_file_data' => $file_arr]);

            if (!empty($file_arr)) {
                $attach = new AttachModel();
                $attach_bool = $attach->batch_insert($file_arr);
                if ($attach_bool === false) {
                    throw new BusinessException('供应商提交 - 附件批量创建失败', ErrCode::$VENDOR_ATTACHMENT_CREATE_ERROR);
                }
            }

            // 写入日志表
            $last_data = $model->toArray();
            $last_data['pays'] = $pays_arr;
            $last_data['application_module_arr'] = $application_module_arr;
            $last_data['attachment_arr'] = $attachment_arr;
            $log = [
                'vendor_id' => $last_data['vendor_id'],
                'before_data' => '',
                'after_data' => json_encode($last_data, JSON_UNESCAPED_UNICODE),
                'create_id' => $user['id']
            ];
            if ((new VendorModifyLogModel())->i_create($log) === false) {
                throw new BusinessException('供应商提交 - 日志创建失败 = ' . json_encode($log, JSON_UNESCAPED_UNICODE), ErrCode::$VENDOR_LOG_CREATE_ERROR);
            }

            // 注入审批流
            $flow_bool = (new VendorFlowService())->createRequest($model->id, $user);
            if ($flow_bool === false) {
                throw new BusinessException(static::$t->_('供应商提交 - 审批流创建失败'), ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = ErrCode::$MYSQL_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-create-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * @param $data
     * @param $user
     * @return array
     */
    private function handleData($data, $user)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        if (empty($data['id'])) {
            unset($data['id']);
        }
        $data['vendor_id']            = static::getVendorNo($data['ownership']);
        $data['status']               = Enums::WF_STATE_PENDING;
        $data['audit_stage']          = self::WF_AUDIT_STATE_UN_STARTED;
        $data['created_at']           = date('Y-m-d H:i:s');
        $data['updated_at']           = date('Y-m-d H:i:s');
        $data['last_audit_time']      = date('Y-m-d H:i:s');
        $data['create_id']            = $user['id'] ?? 0;
        $data['create_name']          = $user['name'] ?? '';
        $data['audit_rejected_count'] = 0;
        $data['audit_approved_count'] = 0;
        $data['approval_version_no']  = 0;
        $user_info                    = $this->getUserMetaFromBi($data['create_id']);


        $cost_company_id                   = array_column($data['use_company'], 'cost_company_id');
        $data['use_company_id']            = implode(',', $cost_company_id);
        $data['create_node_department_id'] = $user_info['node_department_id'] ?? '';
        $data['purchase_purchase_currency'] = empty($data['purchase_currency']) ? 0 : $data['purchase_currency'];

        unset($data['use_company']);

        return $data;
    }
}
