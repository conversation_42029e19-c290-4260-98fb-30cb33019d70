<?php
/**
 * Created by PhpStorm.
 * Date: 2021/6/28
 * Time: 19:18
 */

namespace App\Modules\Vendor\Services;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Reimbursement\Models\RequestSapLog;

/**
 * sap系统 接口Service
 */
class SapsService extends BaseService
{

    private static $instance;
    private static $apiPaths;
    private static $user;
    private static $passWord;



    


    private function __construct()
    {
        self::$apiPaths = env('sap_interface_url', '');
        self::$user     = env('sap_user_id', '_BYDHOST');
        self::$passWord = env('sap_pwd', 'Welcome1');
    }

    /**
     * @return  SapsService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 发送请
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function httpRequestXml($method, $postData)
    {
        try {
            $curl      = curl_init();
            $header[]  = "Content-type: text/xml";
            $basic_key = self::$user . ":" . self::$passWord;
            $header[]  = "Authorization: Basic " . base64_encode($basic_key); //添加头，在name和pass处填写对应账号密码
            if (get_runtime_env() != 'dev') {
                curl_setopt($curl, CURLOPT_PROXY, env('proxy_ip')); //代理服务器地址
                curl_setopt($curl, CURLOPT_PROXYPORT, env('proxy_port')); //代理服务器端口
            }
            curl_setopt($curl, CURLOPT_URL, self::$apiPaths . $method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 180);
            curl_setopt($curl, CURLOPT_TIMEOUT, 300);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                $this->getDI()->get('logger')->warning('sap-vendor-failed:' . curl_error($curl));

            }
            curl_close($curl);// 根据头大小去获取头信息内容

            return $responseText;
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $data    = [];
        } catch (\Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->getDI()->get('logger')->warning('SAP-postRequest-failed:' . $message);
        }


    }

    /**
     *     作用：将xml转为array
     */
    public function xmlToArray($xml)
    {

        return json_decode(json_encode(simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA)), true);
    }

    /**
     *     作用：array转xml*/
    protected function arrayToXml($arr)
    {
        $xml = "<xml>";
        foreach ($arr as $key => $val) {
            if (is_numeric($val)) {
                $xml .= "<" . $key . ">" . $val . "</" . $key . ">";

            } else
                $xml .= "<" . $key . "><![CDATA[" . $val . "]]></" . $key . ">";
        }
        $xml .= "</xml>";
        return $xml;
    }

    /**
     *泰国sap供应商创建
     *
     * */
    public function createdSAP($data)
    {
        $supplier_cate = '';
        if($data['supplier_cate']){
            $supplier_cate = '<a3ok:SupplierCategory>'.$data['supplier_cate'].'</a3ok:SupplierCategory>';
        }
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SupplierBundleMaintainRequest_sync_V1>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
      <Supplier actionCode="01" >
		<!--供应商编号-->
            <InternalID>' . $data['sap_supplier_no'] . '</InternalID>
            <!--默认true-->
            <SupplierIndicator>true</SupplierIndicator>
            <!--供应商名称:-->
            <FirstLineName>' . $data['vendor_short_name'] . '</FirstLineName>
            <!--供应商状态:1是准备中2是有效-->
            <LifeCycleStatusCode>2</LifeCycleStatusCode>

		  <!--付款信息:-->
            <PaymentData actionCode="01" paymentFormListCompleteTransmissionIndicator="true">
                 <!--固定FEX04:-->
               <CompanyID>' . $data['cost_company'] . '</CompanyID>
               <!--科目确定组-->
               <AccountDeterminationCreditorGroupCode>' . $data['group_code'] . '</AccountDeterminationCreditorGroupCode>
            </PaymentData>
            <!--常用地址:-->
            <AddressInformation actionCode="01">
               <!--Optional:-->
               <Address actionCode="01">
                  <!--Optional:-->
                  <PostalAddress>
                     <!--国家:-->
                     <CountryCode>'.$data['ownership_code'].'</CountryCode>
                      </PostalAddress>
                 </Address>
             </AddressInformation>
             <!--税务信息:-->
            <WithholdingTaxClassification actionCode="01">
               <!--国家-->
               <CountryCode>'.get_country_code().'</CountryCode>
               <!--类型默认2:-->
               <TaxTypeCode listID="'.get_country_code().'">2</TaxTypeCode>
               <!--税率类型、税种-->
               <TaxRateTypeCode listID= "2">'.$data['tax_rate_code'].'</TaxRateTypeCode> 
            </WithholdingTaxClassification>'
            .$supplier_cate.
         '</Supplier>
         
      </glob:SupplierBundleMaintainRequest_sync_V1>
   </soapenv:Body>
</soapenv:Envelope>';


        $this->logger->info('vendor-sap-post-data:' . $post_xml);
        $return_xml = $this->httpRequestXml($method = '/sap/managesupplierin1', $post_xml);
        $this->logger->info('vendor-sap-return-data:' . $return_xml);

        preg_match_all("/\<Supplier\>(.*?)\<\/Supplier\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];

        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }

        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_arr['UUID'] ?? '', 'order_code' => $data['vendor_id'], 'type' => 3, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }

    /**
     *菲律宾sap供应商创建
     *
     * */
    public function createdSapPh($data)
    {
        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:a3ok="http://sap.com/xi/AP/CustomerExtension/BYD/A3OK2">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SupplierBundleMaintainRequest_sync_V1>
         <BasicMessageHeader>
         </BasicMessageHeader>
         
          <Supplier actionCode="01" >
		<!--供应商编号-->
            <InternalID>' . $data['sap_supplier_no'] . '</InternalID>
            <!--默认true-->
            <SupplierIndicator>true</SupplierIndicator>
            <!--供应商名称:-->
            <FirstLineName>' . $data['vendor_short_name'] . '</FirstLineName>
            <!--供应商状态:1是准备中2是有效-->
            <LifeCycleStatusCode>2</LifeCycleStatusCode>

		  <!--付款信息:-->
            <PaymentData actionCode="01" paymentFormListCompleteTransmissionIndicator="true">
                 <!--固定FEX04:-->
               <CompanyID>' . $data['cost_company'] . '</CompanyID>
               <!--科目确定组-->
               <AccountDeterminationCreditorGroupCode>' . $data['group_code'] . '</AccountDeterminationCreditorGroupCode>
            </PaymentData>
	        <!--常用地址:-->
            <AddressInformation actionCode="01">
               <!--Optional:-->
               <Address actionCode="01">
                  <!--Optional:-->
                  <PostalAddress>
                     <!--国家:-->
                     <CountryCode>'.$data['ownership_code'].'</CountryCode>
                      </PostalAddress>
                 </Address>
             </AddressInformation>
             <!--税务信息:-->
            <WithholdingTaxClassification actionCode="01">
               <!--国家-->
               <CountryCode>PH</CountryCode>
               <!--类型默认4:-->
               <TaxTypeCode listID="PH" listAgencyID="http://**********-one-off.sap.com/YCB5ESA8Y_">4</TaxTypeCode>
               <!--税率类型、税种-->
               <TaxRateTypeCode listID= "YCB5ESA8Y_4">40</TaxRateTypeCode> 
            </WithholdingTaxClassification>

            
	       <!--供应商类型-->
           <a3ok:SupplierCategory>101</a3ok:SupplierCategory>
     
         </Supplier>
         
      </glob:SupplierBundleMaintainRequest_sync_V1>
   </soapenv:Body>
</soapenv:Envelope>';

        $this->logger->info('vendor-sap-post-data:' . $post_xml);
        $return_xml = $this->httpRequestXml($method = '/sap/managesupplierin1', $post_xml);
        $this->logger->info('vendor-sap-return-data:' . $return_xml);

        preg_match_all("/\<Supplier\>(.*?)\<\/Supplier\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];

        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }

        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_arr['UUID'] ?? '', 'order_code' => $data['vendor_id'], 'type' => 3, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;


    }


}