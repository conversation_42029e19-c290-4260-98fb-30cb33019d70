<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class DeleteService extends BaseService
{
    private static $instance;
    private $logger;

    private function __construct()
    {
        $this->logger = $this->getDI()->get('logger');
    }

    /**
     * @return DeleteService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $vendor_id
     * @return array|mixed
     * @throws ValidationException
     */
    public function deleteData($vendor_id,$uid)
    {
        //工号是否有权限
        $staff_id = EnvModel::getEnvByCode('store_access_staff_id');
        $staff_id= explode(',',$staff_id);
        if(!in_array($uid,$staff_id)){
            throw new \LogicException(static::$t->_('no_access_del_vendor'), ErrCode::$VENDOR_DELETE_RESULT_ERROR);

        }

        $vendor = Vendor::getFirst([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor_id]
        ]);
        $vendor_module = VendorApplicationModuleRelModel::find([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor_id]
        ]);
        if (empty($vendor)) {
            $this->logger->warning('vendor-delete-failed:供应商信息不存在,code=' . ErrCode::$VENDOR_DELETE_NO_DATA_ERROR . ',vendor_id=' . $vendor_id);
            throw new \LogicException(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_NO_DATA_ERROR);
        }

        if (!in_array($vendor->status, [2, 3])) {
            $this->logger->warning('vendor-delete-failed:供应商未审批通过或驳回,code=' . ErrCode::$VENDOR_DELETE_STATUS_ERROR . ',vendor_id=' . $vendor_id);
            throw new ValidationException(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_STATUS_ERROR);
        }
        if ($vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
            $this->logger->info('vendor-delete-failed:供应商等级审批中,code=' . ErrCode::$VENDOR_DELETE_STATUS_ERROR . ',vendor_id=' . $vendor_id);
            throw new ValidationException(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_STATUS_ERROR);
        }

        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try{
            if (!$vendor->delete()) {
                $this->logger->warning('vendor-delete-failed:删除失败,code=' . ErrCode::$VENDOR_DELETE_RESULT_ERROR . ',vendor_id=' . $vendor_id);
                throw new \LogicException(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_RESULT_ERROR);
            }
            if (!empty($vendor_module) && !$vendor_module->delete()) {
                $this->logger->warning('vendor-delete-failed:删除失败,code=' . ErrCode::$VENDOR_DELETE_RESULT_ERROR . ',vendor_id=' . $vendor_id);
                throw new \LogicException(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_RESULT_ERROR);
            }
            $db->commit();
        }catch(\Exception $e){
            $db->rollback();
            $this->logger->error('vendor-delete-failed:删除失败,vendor_id=' . $vendor_id.',error_message=:'.$e->getMessage().',error_code:'.$e->getCode().',error_trace:'.$e->getTraceAsString());
            throw new \Exception(static::$t->_('retry_later'), ErrCode::$VENDOR_DELETE_RESULT_ERROR);
        }
        return true;
    }

}
