<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorHistoryApprovalVersionModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Purchase\Services\OrderService;
use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Resultset;

class VendorFlowService extends AbstractFlowService
{
    private static $instance;

    /**
     * @return VendorFlowService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 审批 - 通过操作
     *
     * @param $main_id
     * @param $note
     * @param $user
     * @param $update_data
     * @return array
     */
    public function approve($main_id, $note, $user, $update_data = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $logger = $this->getDI()->get('logger');
        try {
            $db->begin();

            $main_model = Vendor::getFirst([
                'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $main_id]
            ]);

            if (empty($main_model)) {
                throw new BusinessException('审批通过操作 - 供应商数据不存在', ErrCode::$VENDOR_INFO_GET_ERROR);
            }

            $work_req = $this->getRequest($main_model->id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$VENDOR_WORKFLOW_GET_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_APPROVED) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_REJECTED) {
                throw new ValidationException(static::$t->_('workflow_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            // 审批
            $ws = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowBizParams($main_model, $user), $note);

            // 更新主表数据
            $update_main_data = [
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_id' => $user['id'],
                'last_auditor_id' => $user['id'],
            ];

            // 终审通过
            if (!empty($result->approved_at)) {
                $update_main_data['status'] = Enums::WF_STATE_APPROVED;
                $update_main_data['audit_stage'] = BaseService::WF_AUDIT_STATE_END;
                $update_main_data['last_audit_time'] = $result->approved_at;
                $update_main_data['audit_approved_count'] = $main_model->audit_approved_count + 1;
                $update_main_data['approval_version_no'] = $main_model->approval_version_no + 1;

                //选了flash express 且sap_supplier_no为空传输sap
                $cost_company_id = OrderService::getInstance()->getEnvByCode('vendor_sap_company_ids');
                $company_id = explode(',', $main_model->use_company_id);
                if (empty($main_model->sap_supplier_no) && in_array($cost_company_id, $company_id)) {
                    $update_main_data['is_send_sap'] = 1;
                }

                //判断是否同步金碟 kingdee_company_ids
                $update_main_data['is_send_kingdee'] = UpdateService::getInstance()->isSendKingDee($company_id);

                //供应商等级 如果是未注册则变为注册
                if (0 == $main_model->audit_approved_count && VendorEnums::VENDOR_GRADE_NOT_REGISTER == $main_model->grade) {
                    $update_main_data['grade'] = VendorEnums::VENDOR_GRADE_REGISTER;
                    $update_main_data['grade_status'] = VendorEnums::VENDOR_GRADE_STATUS_NORMAL;
                    $update_main_data['grade_change_at'] = date('Y-m-d H:i:s');
                }

            } else {
                $update_main_data['audit_stage'] = BaseService::WF_AUDIT_STATE_PROCESSING;
                $update_main_data['last_audit_time'] = date('Y-m-d H:i:s');
            }

            // 更新业务主表数据
            $main_bool = $main_model->i_update($update_main_data);
            if ($main_bool === false) {
                throw new BusinessException('供应商主表更新失败', ErrCode::$VENDOR_UPDATE_AUDIT_STATUS_ERROR);
            }

            $approval_version_data = $main_model->toArray();
            $approval_version_data['src_id'] = $approval_version_data['id'];
            $approval_version_data['archive_time'] = date('Y-m-d H:i:s');
            $approval_version_data['archiver_id'] = $user['id'];

            // 提取关联的应用模块
            $exist_application_modules = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $main_model->vendor_id],
                'columns' => ['application_module_id']
            ])->toArray();
            $exist_application_modules = $exist_application_modules ? array_column($exist_application_modules, 'application_module_id') : [];
            $approval_version_data['application_module_ids'] = !empty($exist_application_modules) ? json_encode(array_map('intval', $exist_application_modules)) : '';

            // 提取关联的附件
            $exist_attaches = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND deleted = 0',
                'bind' => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                    'oss_bucket_key' => $main_model->id
                ],
                'columns' => ['oss_bucket_type', 'oss_bucket_key', 'bucket_name', 'object_key', 'file_name']
            ])->toArray();
            $approval_version_data['attachment_files'] = !empty($exist_attaches) ? json_encode($exist_attaches, JSON_UNESCAPED_UNICODE) : '';

            // 终审通过, 供应商数据归档
            if ($update_main_data['audit_stage'] == BaseService::WF_AUDIT_STATE_END) {
                unset($approval_version_data['id']);
                if ((new VendorHistoryApprovalVersionModel())->i_create($approval_version_data) === false) {
                    throw new BusinessException('供应商审批通过的版本表写入失败', ErrCode::$VENDOR_UPDATE_APPROVAL_VERSION_INFO_ERROR);
                }
            }

            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger->warning('供应商审批 - 通过操作 - 异常:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 审批 - 驳回
     *
     * @param $main_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($main_id, $note, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            $main_model = Vendor::getFirst([
                'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $main_id]
            ]);

            if (empty($main_model)) {
                throw new BusinessException('审批驳回操作 - 供应商数据不存在', ErrCode::$VENDOR_INFO_GET_ERROR);
            }

            $work_req = $this->getRequest($main_model->id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$VENDOR_WORKFLOW_GET_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_APPROVED) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->status == Enums::WF_STATE_REJECTED) {
                throw new ValidationException(static::$t->_('workflow_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowBizParams($main_model, $user), $note);

            if ($result === false) {
                throw new BusinessException('审批流驳回操作失败', ErrCode::$VENDOR_WORKFLOW_REJECTED_ERROR);
            }

            $bool = $main_model->i_update([
                'status' => Enums::WF_STATE_REJECTED,
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_id' => $user['id'],
                'audit_stage' => BaseService::WF_AUDIT_STATE_END,
                'last_audit_time' => date('Y-m-d H:i:s'),
                'last_auditor_id' => $user['id'],
                'audit_rejected_count' => $main_model->audit_rejected_count + 1,
            ]);

            if ($bool === false) {
                throw new BusinessException('供应商主表更新失败', ErrCode::$VENDOR_UPDATE_AUDIT_STATUS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('供应商审批 - 驳回操作 - 异常:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 申请 - 撤回
     *
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
    }

    /**
     * 获取审批流信息
     *
     * @param $main_id
     * @return Model
     */
    public function getRequest($main_id)
    {
        return $this->getRequestByBiz($main_id, Enums::WF_VENDOR_BIZ_TYPE);
    }

    /**
     * 业务申请 - 注入审批流
     *
     * @param $biz_id
     * @param $user
     * @return Resultset|Model
     * @throws BusinessException
     * @throws ValidationException
     */
    public function createRequest($biz_id, $user)
    {
        $model = Vendor::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $biz_id]
        ]);

        $data['id'] = $model->id;
        $data['name'] = '供应商审批申请(' . $model->vendor_id . ')';
        $data['biz_type'] = Enums::WF_VENDOR_BIZ_TYPE;
        $data['flow_id'] = $this->getFlowId($model);

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowBizParams($model, $user));
    }

    /**
     * 业务自定义 - 审批流需要数据
     *
     * @param $main_model
     * @param $user
     * @return array
     */
    public function getWorkflowBizParams($main_model, $user)
    {
        //查询应用模块
        $vendor_module = VendorApplicationModuleRelModel::find([
            'conditions' => 'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $main_model->vendor_id]
        ]);
        $modules = [];
        if (!empty($vendor_module)) {
            $vendor_module_arr = $vendor_module->toArray();
            foreach ($vendor_module_arr as $k => $v) {
                $modules[] = $v['application_module_id'];
            }
        }
        //是否新增采购模块(且为修改其他敏感信息)
        return [
            'submitter_id' => $main_model->create_id,
            'vendor_modules' => $modules,
            'only_purchase_audit' => $main_model->only_purchase_audit,
            'node_department_id' => $main_model->create_node_department_id,
        ];
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model = null)
    {
        $country_code = get_country_code();

        $use_company_id = explode(',', $model->use_company_id);
        $company_list = EnumsService::getInstance()->getSysDepartmentCompanyIds();

        // FlashExpress的
        if (in_array($company_list['FlashExpress'], $use_company_id)) {
            $flow_id = Enums::WF_VENDOR_WF_ID;
        } else if (
            $country_code == GlobalEnums::TH_COUNTRY_CODE
            &&
            (in_array($company_list['FlashHomeOperation'], $use_company_id) || in_array($company_list['FlashHomeHolding'], $use_company_id))
        ) {
            // 泰国 Flash Home 相关公司的独立审批流
            $flow_id = Enums::WF_VENDOR_FLASH_HOME_ID;
        } else {
            // 其他公司的
            $flow_id = Enums::WF_VENDOR_WF_F_ID;
        }

        return $flow_id;
    }

    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     * @throws ValidationException
     */
    public function recommit($item, $user)
    {
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException('没有找到request, biz_id=' . $item->id, ErrCode::$BUSINESS_ERROR);
        }

        //老的改成被遗弃
        $req->is_abandon = Enums\GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        if ($req->save() === false) {
            throw new BusinessException('request 废弃状态save失败, request_id=' . $req->id . '原因可能是:' . get_data_object_error_msg($req), ErrCode::$BUSINESS_ERROR);
        }

        return $this->createRequest($item->id, $user);
    }

    /**
     * 获取当前业务审批流信息
     *
     * @param $id
     * @param $bizType
     * @return Model
     */
    public function getRequestByBiz($id, $bizType)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id:',
                'bind' => ['type' => $bizType, 'id' => $id],
                'order' => 'id desc'
            ]
        );
    }

}
