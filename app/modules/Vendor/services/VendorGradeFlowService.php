<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Models\oa\VendorGradeLogModel;
use App\Modules\Vendor\Models\VendorHistoryApprovalVersionModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Services\AbstractFlowService;
use App\Modules\Workflow\Services\WorkflowServiceV2;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Purchase\Services\OrderService;

class VendorGradeFlowService extends AbstractFlowService
{
    private static $instance;

    /**
     * @return VendorGradeFlowService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 审批 - 通过操作
     * @param $main_id
     * @param $note
     * @param $user
     * @param $update_data
     * @return array
     */
    public function approve($main_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            $main_model = Vendor::getFirst([
                'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $main_id]
            ]);

            if (empty($main_model)) {
                throw new BusinessException('审批通过操作 - 供应商数据不存在', ErrCode::$VENDOR_INFO_GET_ERROR);
            }

            $work_req = $this->getRequest($main_model->id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$VENDOR_WORKFLOW_GET_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_APPROVED) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_REJECTED) {
                throw new ValidationException(static::$t->_('workflow_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            // 审批
            $ws     = new WorkflowServiceV2();
            $result = $ws->doApprove($work_req, $user, $this->getWorkflowBizParams($main_model, $user), $note);

            // 终审通过
            if (!empty($result->approved_at)) {
                // 更新主表数据
                $update_main_data = [
                    'grade_approve_at'     => date('Y-m-d H:i:s'),
                    'grade_change_at'      => date('Y-m-d H:i:s'),
                    'grade_approve_status' => Enums::WF_STATE_APPROVED,
                    'grade_action'         => 0
                ];

                //变更日志
                $grade_change_log = [
                    'vendor_id'  => $main_id,
                    'create_id'  => $main_model->grade_create_id,
                    'created_at' => date('Y-m-d H:i:s'),
                ];

                // 分级审批状态或等级变更到对应等级或者状态
                switch ($main_model->grade_action) {
                    case VendorEnums::VENDOR_ACTION_REGISTER:
                        $update_main_data['grade']   = VendorEnums::VENDOR_GRADE_REGISTER;
                        $grade_change_log['type']    = VendorEnums::VENDOR_GRADE_TYPE_1;
                        $grade_change_log['content'] = json_encode([
                            'change_before'             => $main_model->grade,
                            'change_after'              => VendorEnums::VENDOR_GRADE_REGISTER,
                            'change_description'        => $main_model->change_description,
                            'authentication_attachment' => []

                        ], JSON_UNESCAPED_UNICODE);
                        break;
                    case VendorEnums::VENDOR_ACTION_AUTHENTICATION:
                        //查询认证附件
                        $authentication_attachments  = AttachModel::find([
                            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND sub_type = :sub_type: AND deleted = :deleted: ',
                            'bind'       => [
                                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                                'oss_bucket_key'  => $main_model->id,
                                'sub_type'        => Enums::OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION,
                                'deleted'         => Enums\GlobalEnums::IS_NO_DELETED
                            ],
                            'columns'    => ['file_name', 'bucket_name', 'object_key', 'sub_type']
                        ])->toArray();
                        $update_main_data['grade']   = VendorEnums::VENDOR_GRADE_AUTHENTICATION;
                        $grade_change_log['type']    = VendorEnums::VENDOR_GRADE_TYPE_1;
                        $grade_change_log['content'] = json_encode([
                            'change_before'             => $main_model->grade,
                            'change_after'              => VendorEnums::VENDOR_GRADE_AUTHENTICATION,
                            'change_description'        => $main_model->change_description,
                            'authentication_attachment' => $authentication_attachments

                        ], JSON_UNESCAPED_UNICODE);

                        break;
                    case VendorEnums::VENDOR_ACTION_CANCELLATION:
                        $update_main_data['grade']   = VendorEnums::VENDOR_GRADE_CANCELLATION;
                        $grade_change_log['type']    = VendorEnums::VENDOR_GRADE_TYPE_1;
                        $grade_change_log['content'] = json_encode([
                            'change_before'             => $main_model->grade,
                            'change_after'              => VendorEnums::VENDOR_GRADE_CANCELLATION,
                            'change_description'        => $main_model->change_description,
                            'authentication_attachment' => []

                        ], JSON_UNESCAPED_UNICODE);
                        break;
                    case VendorEnums::VENDOR_ACTION_BLOCK:
                        $update_main_data['grade']   = VendorEnums::VENDOR_GRADE_BLOCK;
                        $grade_change_log['type']    = VendorEnums::VENDOR_GRADE_TYPE_1;
                        $grade_change_log['content'] = json_encode([
                            'change_before'             => $main_model->grade,
                            'change_after'              => VendorEnums::VENDOR_GRADE_BLOCK,
                            'change_description'        => $main_model->change_description,
                            'authentication_attachment' => []

                        ], JSON_UNESCAPED_UNICODE);
                        break;
                    case VendorEnums::VENDOR_ACTION_NORMA:
                        $update_main_data['grade_status'] = VendorEnums::VENDOR_GRADE_STATUS_NORMAL;
                        $grade_change_log['type']         = VendorEnums::VENDOR_GRADE_TYPE_2;
                        $grade_change_log['content']      = json_encode([
                            'change_before'             => $main_model->grade_status,
                            'change_after'              => VendorEnums::VENDOR_GRADE_STATUS_NORMAL,
                            'change_description'        => $main_model->change_description,
                            'authentication_attachment' => []

                        ], JSON_UNESCAPED_UNICODE);
                        break;
                    case VendorEnums::VENDOR_ACTION_SUSPEND:
                        $update_main_data['grade_status'] = VendorEnums::VENDOR_GRADE_STATUS_SUSPEND;
                        $update_main_data['suspend_end_time'] = date('Y-m-d H:i:s', (time() + ($main_model->suspend_time) * 30 * 86400));

                        $grade_change_log['type']         = VendorEnums::VENDOR_GRADE_TYPE_2;
                        $grade_change_log['content']      = json_encode([
                            'change_before'  => $main_model->grade_status,
                            'change_after'   => VendorEnums::VENDOR_GRADE_STATUS_SUSPEND,
                            'change_description'        => $main_model->change_description,
                            'pause_duration' => $main_model->suspend_time

                        ], JSON_UNESCAPED_UNICODE);
                        break;
                    default;
                }

                //记录等级或状态变更记录
                $grade_obj  = new VendorGradeLogModel();
                $grade_bool = $grade_obj->i_create($grade_change_log);
                if ($grade_bool === false) {
                    throw new BusinessException('供应商等级变更日志更新失败' . get_data_object_error_msg($grade_obj), ErrCode::$VENDOR_UPDATE_AUDIT_STATUS_ERROR);
                }

            }
            // 更新业务主表数据
            $main_bool = $main_model->i_update($update_main_data);
            if ($main_bool === false) {
                throw new BusinessException('供应商主表更新失败' . get_data_object_error_msg($main_model), ErrCode::$VENDOR_UPDATE_AUDIT_STATUS_ERROR);
            }


            $db->commit();
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('供应商分级管理审批 - 通过操作 - 异常:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 审批 - 驳回
     * @param $main_id
     * @param $note
     * @param $user
     * @return array
     */
    public function reject($main_id, $note, $user)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db      = $this->getDI()->get('db_oa');

        try {
            $db->begin();
            $main_model = Vendor::getFirst([
                'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $main_id]
            ]);

            if (empty($main_model)) {
                throw new BusinessException('审批驳回操作 - 供应商数据不存在', ErrCode::$VENDOR_INFO_GET_ERROR);
            }

            $work_req = $this->getRequest($main_model->id);
            if (empty($work_req->id)) {
                throw new BusinessException('获取工作流失败', ErrCode::$VENDOR_WORKFLOW_GET_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_CANCEL) {
                throw new ValidationException(static::$t->_('workflow_has_been_withdrawal'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_APPROVED) {
                throw new ValidationException(static::$t->_('workflow_has_been_approval'), ErrCode::$VALIDATE_ERROR);
            }

            if ($main_model->grade_approve_status == Enums::WF_STATE_REJECTED) {
                throw new ValidationException(static::$t->_('workflow_has_been_rejected'), ErrCode::$VALIDATE_ERROR);
            }

            $result = (new WorkflowServiceV2())->doReject($work_req, $user, $this->getWorkflowBizParams($main_model, $user), $note);

            if ($result === false) {
                throw new BusinessException('审批流驳回操作失败', ErrCode::$VENDOR_WORKFLOW_REJECTED_ERROR);
            }

            $bool = $main_model->i_update([
                'grade_approve_status' => Enums::WF_STATE_REJECTED,
                'grade_at'             => date('Y-m-d H:i:s'),
                'grade_action'         => 0,
            ]);

            if ($bool === false) {
                throw new BusinessException('供应商主表更新失败', ErrCode::$VENDOR_UPDATE_AUDIT_STATUS_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('供应商审批 - 驳回操作 - 异常:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];
    }

    /**
     * 申请 - 撤回
     * @param $id
     * @param $note
     * @param $user
     * @return array
     */
    public function cancel($id, $note, $user)
    {
    }

    /**
     * 获取审批流信息
     * @param $main_id
     * @return \Phalcon\Mvc\Model
     */
    public function getRequest($main_id)
    {
        return $this->getRequestByBiz($main_id, Enums::WF_VENDOR_GRADE_BIZ_TYPE);
    }

    /**
     * 业务申请 - 注入审批流
     * @param $biz_id
     * @param $user
     * @param string $module_add_purchase
     * @return \Phalcon\Mvc\Model\Resultset|\Phalcon\Mvc\Phalcon\Mvc\Model
     * @throws BusinessException
     */
    public function createRequest($biz_id, $user)
    {
        $model            = Vendor::findFirst([
            'id = :id:',
            'bind' => ['id' => $biz_id]
        ]);
        $data['id']       = $model->id;
        $data['name']     = '供应商分级管理审批申请(' . $model->vendor_id . ')';
        $data['biz_type'] = Enums::WF_VENDOR_GRADE_BIZ_TYPE;
        $data['flow_id']  = $this->getFlowId();

        return (new WorkflowServiceV2())->createRequest($data, $user, $this->getWorkflowBizParams($model, $user));
    }

    /**
     * 业务自定义 - 审批流需要数据
     * @param $main_model
     * @param $user
     * @return array
     */
    public function getWorkflowBizParams($main_model, $user)
    {


        return [
            'submitter_id'  => $main_model->grade_create_id,
            'purchase_type' => $main_model->purchase_type,
        ];
    }

    /**
     * @inheritDoc
     */
    public function getFlowId($model = null)
    {
        return Enums::WF_VENDOR_WF_GRADE_ID;
    }

    /**
     * @param $item
     * @param $user
     * @return mixed
     * @throws BusinessException
     */
    public function recommit($item, $user)
    {
        $req = $this->getRequest($item->id);
        if (empty($req)) {
            throw new BusinessException("没有找到req=" . $item->id);
        }

        //老的改成被遗弃
        $req->is_abandon = Enums\GlobalEnums::WORKFLOW_ABANDON_STATE_YES;
        $req->save();

        return $this->createRequest($item->id, $user);
    }

    /**
     * 获取当前业务审批流信息
     * @param $id
     * @param $bizType
     * @return \Phalcon\Mvc\Model
     */
    public function getRequestByBiz($id, $bizType)
    {
        return WorkflowRequestModel::findFirst(
            [
                'biz_type = :type: and biz_value= :id:',
                'bind'  => ['type' => $bizType, 'id' => $id],
                'order' => 'id desc'
            ]
        );
    }

}
