<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Models\oa\VendorGradeLogModel;
use App\Models\oa\VendorPurchaseRelationModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class GradeDetailService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return GradeDetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $vendor_id
     * @param $module_type
     * @return array|mixed
     */
    public function getDetail($vendor_id, $module_type, $uid)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $vendor  = [];
        try {
            $vendor = Vendor::getFirst([
                'vendor_id = :vendor_id:',
                'bind' => ['vendor_id' => $vendor_id]
            ]);

            if (empty($vendor)) {
                throw new BusinessException('没有查询到主表数据', ErrCode::$VENDOR_INFO_GET_ERROR);

            }
            $vendor_payment_detail = $vendor->getDetails();
            $vendor                = $vendor->toArray();
            $vendor_payment_detail = $vendor_payment_detail->toArray();
            //存在首次为注册不走审批流情况不做非空判断
            $req = (new VendorGradeFlowService())->getRequest($vendor['id']);

            // 获取应用模块
            $applications                     = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $vendor['vendor_id']],
            ])->toArray();
            $vendor['application_module_arr'] = $applications ? array_column($applications, 'application_module_id') : [];

            $use_company = [];
            if (!empty($vendor['use_company_id'])) {
                $coo_company_list = (new PurchaseService())->getCooCostCompany();
                $use_company_arr  = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');

                $use_company_id = explode(',', $vendor['use_company_id']);

                foreach ($use_company_id as $company) {
                    $use_company[] = [
                        'cost_company_id'   => $company,
                        'cost_company_name' => $use_company_arr[$company] ?? ''
                    ];

                }
            }
            //获取主供/服务类型 产品
            $relation_products = VendorPurchaseRelationModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $vendor_id]
            ])->toArray();

            $vendor['product_category'] = array_column($relation_products, 'product_category');
            $vendor['purchase_type']    = ($vendor['purchase_type'] == 0) ? '' : $vendor['purchase_type'];

            $vendor['use_company'] = $use_company;

            // 获取附件
            $attaches = AttachModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND deleted = :deleted:',
                'bind'       => [
                    'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                    'oss_bucket_key'  => $vendor['id'],
                    'deleted'         => Enums\GlobalEnums::IS_NO_DELETED
                ],
                'columns'    => ['file_name', 'bucket_name', 'object_key', 'sub_type']
            ])->toArray();
            // 资质附件
            $vendor['attachment_arr'] = [];
            // bir2303附件
            $vendor['bir2303_arr'] = [];
            //认证附件
            $vendor['authentication_attachment'] = [];

            // 注册文件(公司/个人)
            $vendor['registration_file'] = [];

            // 银行对账单信头
            $vendor['bank_statement_letterhead_file'] = [];

            // 所得税表头(公司/个人)
            $vendor['income_tax_form_header_file'] = [];

            // SST登记表/SST登记信
            $vendor['sst_registration_form_file'] = [];

            if (!empty($attaches)) {
                foreach ($attaches as $item) {
                    if ($item['sub_type'] == Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS) {
                        $vendor['attachment_arr'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303) {
                        $vendor['bir2303_arr'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION) {
                        $vendor['authentication_attachment'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_REGISTRATION_FILE) {
                        $vendor['registration_file'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE) {
                        $vendor['bank_statement_letterhead_file'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE) {
                        $vendor['income_tax_form_header_file'][] = $item;
                    } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE) {
                        $vendor['sst_registration_form_file'][] = $item;
                    }
                }
            }

            // 审批日志
            $vendor['auth_logs'] = [];
            //待回复征询ID
            $vendor['ask_id'] = '';
            if (!empty($req)) {
                $vendor['auth_logs'] = $this->getAuditLogs($req, false);
                $ask                 = (new FYRService())->getRequestToByReplyAsk($req, $uid);
                $vendor['ask_id']    = $ask ? $ask->id : '';
            }
            //供应商状态变更记录
            $vendor['grade_status_logs'] = $this->getGradeStatusLogs($vendor_id);

            //供应商等级变更记录
            $vendor['grade_logs'] = $this->getGradeLogs($vendor_id);


            $vendor                = $this->handleData($vendor);
            $vendor_payment_detail = $this->handleDetailData($vendor_payment_detail);
            $vendor['pays']        = $vendor_payment_detail;
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-detail-failed:' . $real_message);
        }

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? 'success',
            'data'    => $vendor ?? []
        ];
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();

        $data['company_nature_title']   = $static_item['company_nature_item'][$data['company_nature']] ? static::$t->_($static_item['company_nature_item'][$data['company_nature']]) : '';
        $data['certificate_type_title'] = $static_item['certificate_type_item'][$data['certificate_type']] ? static::$t->_($static_item['certificate_type_item'][$data['certificate_type']]) : '';
        $data['ownership_title']        = $static_item['ownership_item'][$data['ownership']] ? static::$t->_($static_item['ownership_item'][$data['ownership']]) : '';
        $data['ownership_title']        = $static_item['ownership_item'][$data['ownership']] ? static::$t->_($static_item['ownership_item'][$data['ownership']]) : '';
        $data['attachment']             = empty($data['attachment']) ? '' : explode(',', $data['attachment']);
        $data['purchase_type']          = empty($data['purchase_type']) ? '' : (int)$data['purchase_type'];
        $data['purchase_amount']        = (empty($data['purchase_amount']) && 0 != $data['purchase_amount']) ? '' : $data['purchase_amount'];
        $data['purchase_currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['purchase_currency']] ?? '');
        $data['purchase_currency']      = empty($data['purchase_currency']) ? '' : $data['purchase_currency'];
        $data['suspend_time']           = empty($data['suspend_time']) ? '' : (int)$data['suspend_time'];
        $data['grade_final_state']      = empty($data['grade_final_state']) ? '' : (int)$data['grade_final_state'];
        return $data;
    }

    /**
     * @param $data
     * @return array
     */
    private function handleDetailData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();
        foreach ($data as &$item) {

            $item['payment_method']      = (int)$item['payment_method'];
            $item['payment_method_text'] = static::$t->_(Enums\VendorEnums::$payment_method[$item['payment_method']]);
        }

        return $data;
    }

    /**
     * 获取审批日志
     * @param $req
     * @param bool $if_download
     * @return array
     */
    protected function getAuditLogs($req, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req, true);

        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k => $v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        return $auth_logs;
    }

    /**
     * 费用公司枚举
     * */
    public function getCooCompany()
    {
        $coo_company_list = [];
        $coo_company_list = (new PurchaseService())->getCooCostCompany();
        return $coo_company_list;
    }

    public function getGradeLogs($vendor_id)
    {
        $record_log = VendorGradeLogModel::find([
            'conditions' => 'vendor_id = :vendor_id: and type = :type:',
            'bind'       => ['vendor_id' => $vendor_id, 'type' => VendorEnums::VENDOR_GRADE_TYPE_1],
            'order'      => 'id desc',

        ])->toArray();
        if (empty($record_log)) {
            return [];
        }
        $lang       = static::$t;
        $grade_item = VendorEnums::$vendor_grade_item;

        foreach ($record_log as &$item) {
            $staff_info                        = $this->getUserMetaFromBi($item['create_id']);
            $item['content']                   = json_decode($item['content'], true);
            $item['staff_name']                = $staff_info['create_name'] ?? '';
            $item['english_name']              = $staff_info['name_en'] ?? '';
            $item['staff_department']          = $staff_info['create_department'] ?? '';
            $item['job_title']                 = $staff_info['create_job_title_name'] ?? '';
            $item['change_before']             = $lang[$grade_item[$item['content']['change_before']] ?? ''] ?? '';
            $item['change_after']              = $lang[$grade_item[$item['content']['change_after']] ?? ''] ?? '';
            $item['change_description']        = $item['content']['change_description'] ?? '';
            $item['authentication_attachment'] = $item['content']['authentication_attachment'];
            unset($item['content']);
            unset($item['vendor_id']);
            unset($item['type']);
        }

        return $record_log;
    }

    public function getGradeStatusLogs($vendor_id)
    {
        $record_log = VendorGradeLogModel::find([
            'conditions' => 'vendor_id = :vendor_id: and type = :type:',
            'bind'       => ['vendor_id' => $vendor_id, 'type' => VendorEnums::VENDOR_GRADE_TYPE_2],
            'order'      => 'id desc',

        ])->toArray();
        if (empty($record_log)) {
            return [];
        }
        $lang                = static::$t;
        $grade_status_item   = VendorEnums::$vendor_grade_status_item;
        $pause_duration_item = VendorEnums::$vendor_suspend_item;

        foreach ($record_log as &$item) {
            $staff_info               = $this->getUserMetaFromBi($item['create_id']);
            $item['content']          = json_decode($item['content'], true);
            $item['staff_name']       = $staff_info['create_name'] ?? '';
            $item['english_name']     = $staff_info['name_en'] ?? '';
            $item['staff_department'] = $staff_info['create_department'] ?? '';
            $item['job_title']        = $staff_info['create_job_title_name'] ?? '';

            $item['change_before']      = $lang[$grade_status_item[$item['content']['change_before']] ?? ''] ?? '';
            $item['change_after']       = $lang[$grade_status_item[$item['content']['change_after']] ?? ''] ?? '';
            $item['change_description'] = $item['content']['change_description'] ?? '';

            $item['pause_duration'] = $lang[$pause_duration_item[$item['content']['pause_duration']] ?? ''] ?? '';
            unset($item['content']);
            unset($item['vendor_id']);
            unset($item['type']);
        }

        return $record_log;
    }
}
