<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\KingDeeEnums;
use App\Library\Enums\VendorEnums;
use App\Library\RedisClient;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\User\Models\DepartmentModel;
use App\Modules\User\Services\UserService;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Modules\Vendor\Models\VendorPaymentDetailModel;
use App\Util\RedisExpire;
use App\Util\RedisKey;
use App\Library\ErrCode;
use App\Modules\Vendor\Models\SupplierCertificateTypeModel;
use App\Modules\Vendor\Models\SupplierCompanyNatureModel;
use App\Modules\Vendor\Models\SupplierOwnershipModel;
use App\Models\oa\SupplierBankModel;
use App\Modules\Vendor\Models\VendorApplicationModuleModel;

class BaseService extends \App\Library\BaseService
{
    public static $validate_update = [
        'vendor_id' => 'Required|StrLenGeLe:6,32',
    ];
    //预计年采购金额 整数 小数 校验
    const INTEGER_AMOUNT_LENGTH = 12;
    const DECIMAL_AMOUNT_LENGTH = 2;

    // 子模块类型: 便于对取数逻辑的控制
    public const SUB_MODULE_TYPE_INFO = 1;
    public const SUB_MODULE_TYPE_AUDIT = 2;
    public const SUB_MODULE_TYPE_REPLY = 3;

    // 审批阶段
    public const WF_AUDIT_STATE_UN_STARTED = 1;// 未开始审批
    public const WF_AUDIT_STATE_PROCESSING = 2;// 审批中
    public const WF_AUDIT_STATE_END = 3;// 审批结束

    // 供应商归属地
    public const SUPPLIER_OWNER_SHIP_PH = 5;//菲律宾
    public static $validate_currency = [
        'vendor_name' => 'Required|StrLenGeLe:1,100',
        'vendor_short_name' => 'Required|StrLenGeLe:1,40',
        'company_website' => 'StrLenGe:0',
        'ownership' => 'Required|IntGe:1',
        'company_nature' => 'Required|IntGe:1',
        'certificate_type' => [
            'IfIntEq:company_nature,1|IntIn:3,4',
            'IfIntEq:company_nature,2|IntIn:1,2',
            'IfIntNotIn:company_nature,1,2|IntGe:1'
        ],
        'identification_no' => 'Required|StrLenGeLe:1,50',
        'artificial_person' => 'StrLenGe:0',
        'company_phone' => 'StrLenGe:0',
        'company_address' => 'Required|StrLenGeLe:1,500',
        'sap_supplier_no' => 'StrLenGeLe:0,128',
        'application_module_arr' => 'Required|ArrLenGeLe:1,5',
        'purchase_currency' => 'StrLenGeLe:0,128',
        'purchase_amount' => 'StrLenGeLe:0,15',
        'purchase_remark' => 'StrLenGeLe:0,500',

        // 销售和服务税登记号码
        'sales_and_service_tax_number' => 'Required|StrLenGeLe:0,30',

        // 注册文件(公司/个人)
        'registration_file' => 'Required|Arr',

        // 银行对账单信头
        'bank_statement_letterhead_file' => 'Required|Arr',

        // 所得税表头(公司/个人)
        'income_tax_form_header_file' => 'Required|Arr',

        // SST登记表/SST登记信
        'sst_registration_form_file' => 'Required|Arr',

        'product' => 'ArrLenGeLe:0,200',

        'attachment_arr' => 'Required|ArrLenGeLe:1,10',
        'attachment_arr[*].file_name' => "Required|StrLenGeLe:2,300",
        'attachment_arr[*].bucket_name' => "Required|StrLenGeLe:2,300",
        'attachment_arr[*].object_key' => "Required|StrLenGeLe:2,300",
        'use_company' => 'Required|ArrLenGeLe:1,100',
        'use_company[*].cost_company_id'   => "Required|StrLenGeLe:1,300",
        'use_company[*].cost_company_name' => "Required|StrLenGeLe:1,300",

        'tax_number' => [
            'IfIntEq:ownership,'.self::SUPPLIER_OWNER_SHIP_PH.'|Required|StrLenGeLe:1,30',
            'IfIntNe:ownership,'.self::SUPPLIER_OWNER_SHIP_PH.'|StrLenGeLe:0,30',
        ],
        'bir2303_arr' => [
            'IfIntEq:ownership,'.self::SUPPLIER_OWNER_SHIP_PH.'|Required|ArrLenGeLe:1,5',
            'IfIntNe:ownership,'.self::SUPPLIER_OWNER_SHIP_PH.'|ArrLenGeLe:0,5',
        ],
        'bir2303_arr[*].file_name' => "StrLenGeLe:0,300",
        'bir2303_arr[*].bucket_name' => "StrLenGeLe:0,300",
        'bir2303_arr[*].object_key' => "StrLenGeLe:0,300",
        'pays'=>'Required|ArrLenGeLe:1,11',
        'pays[*].contact' => 'Required|StrLenGeLe:1,50',
        'pays[*].contact_phone' => 'Required|StrLenGeLe:1,50',
        'pays[*].contact_email' => 'Required|StrLenGeLe:1,100',
        'pays[*].payment_method' => 'Required|IntIn:'.Enums\VendorEnums::PAYMENT_METHOD_BANK.','.Enums\VendorEnums::PAYMENT_METHOD_THIRD_PAY,//支付方式：1银行转账,2第三方支付,3两种支付方式都有
        'pays[*].bank_no' => 'Required|StrLenGeLe:0,50',//银行卡号
        'pays[*].bank_account_name' => 'Required|StrLenGeLe:1,128',//银行账户名称
        'pays[*].bank_pay_name' => 'Required|StrLenGeLe:1,10',//第三方支付方

        'pays[*].swift_code' => 'Required|StrLenLe:30',//Swift Code

    ];

    // 驳回接口的参数校验
    public static $rejected_params_validate = [
        'vendor_id' => 'Required|StrLenGeLe:5,32',
        'remark' => 'Required|StrLenGeLe:1,1000',
    ];
    // 参数校验
    public static $ask_params_validate = [
        'vendor_id' => 'Required|StrLenGeLe:5,32',
        'note' => 'Required|StrLenGeLe:1,10000',
        'to_staff' => 'Required|Arr',
    ];

    // 通过接口的参数校验
    public static $approval_params_validate = [
        'vendor_id' => 'Required|StrLenGeLe:5,32',
        'remark' => 'StrLenGeLe:0,1000',
    ];

    //分级管理 注册注销拉黑 变更参数校验
    public static $grade_params_validate = [
        'vendor_id'          => 'Required|StrLenGeLe:5,32',
        'change_description' => 'Required|StrLenGeLe:1,500'
    ];

    //分级管理 认证 变更参数校验
    public static $grade_authentication_params_validate = [
        'vendor_id'                                => 'Required|StrLenGeLe:5,32',
        'change_description'                       => 'Required|StrLenGeLe:1,500',
        'authentication_attachment'                => 'Required|ArrLenGeLe:1,10',
        'authentication_attachment[*].file_name'   => "StrLenGeLe:0,300",
        'authentication_attachment[*].bucket_name' => "StrLenGeLe:0,300",
        'authentication_attachment[*].object_key'  => "StrLenGeLe:0,300",
    ];
    //分级管理 认证 变更参数校验
    public static $grade_suspend_params_validate = [
        'vendor_id'          => 'Required|StrLenGeLe:5,32',
        'change_description' => 'Required|StrLenGeLe:1,500',
        'suspend_time'       => 'Required|IntIn:1,2,3,4,5,6',

    ];


    /**
     * 供应商信息新增 / 编辑 公共校验
     *
     * @param array $params
     * @param bool $is_update
     * @return bool
     * @throws ValidationException
     */
    public static function commonParamsValidate(array $params, bool $is_update = false)
    {
        $validate = self::$validate_currency;
        if ($is_update === true) {
            $validate = array_merge(self::$validate_update, $validate);
        }


        // MY 的新增 需校验
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            // 应用模块 含 采购时, 如下附件 必填
            if (in_array(VendorEnums::VENDOR_PURCHASE, $params['application_module_arr'])) {
                // 公司注册文件/个人注册文件
                $validate['registration_file'] = 'Required|ArrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'registration_file']);
                $validate['registration_file[*]'] = 'Obj';
                $validate['registration_file[*].file_name'] = 'Required|StrLenGe:1';
                $validate['registration_file[*].bucket_name'] = 'Required|StrLenGe:1';
                $validate['registration_file[*].object_key'] = 'Required|StrLenGe:1';
            }

            // 银行对账单信头 必填
            $validate['bank_statement_letterhead_file'] = 'Required|ArrLenGe:1|>>>:' . static::$t->_('params_error', ['param' => 'bank_statement_letterhead_file']);
            $validate['bank_statement_letterhead_file[*]'] = 'Obj';
            $validate['bank_statement_letterhead_file[*].file_name'] = 'Required|StrLenGe:1';
            $validate['bank_statement_letterhead_file[*].bucket_name'] = 'Required|StrLenGe:1';
            $validate['bank_statement_letterhead_file[*].object_key'] = 'Required|StrLenGe:1';
        }

        // 静态规则校验
        Validation::validate($params, $validate);

        // 支付信息校验
        $third_payer_config = EnumsService::getInstance()->getVendorThirdPayer();
        $third_payer_config = array_column($third_payer_config, 'bank_no_is_required', 'value');
        foreach ($params['pays'] as $payer) {
            // 开户行/第三方支付方 银行账号是否必填验证: 未配置默认必填
            $bank_no_is_required = $third_payer_config[$payer['bank_pay_name']] ?? 1;
            if ($bank_no_is_required == 1 && empty($payer['bank_no'])) {
                throw new ValidationException(static::$t->_('vendor_submit_error_01', ['payer_name' => $payer['bank_pay_name']]), ErrCode::$VALIDATE_ERROR);
            }

            // 联系人不允许含0
            if (stristr($payer['contact'], '0') !== false) {
                throw new ValidationException(static::$t->_('vendor_info.005'), ErrCode::$VALIDATE_ERROR);
            }
        }

        // 银行账号不可重复
        $bank_nos = array_column($params['pays'], 'bank_no');
        if (count($bank_nos) != count(array_unique($bank_nos))) {
            throw new ValidationException(static::$t->_('vendor_info.004'), ErrCode::$VALIDATE_ERROR);
        }

        // MY 的新增 需校验
        if ($country_code == GlobalEnums::MY_COUNTRY_CODE && $is_update == false) {
            // 当公司性质 为 公司时, 证件类型 只能是 公司注册号码(原统一社会信用代码证)
            if ($params['company_nature'] == 2 && $params['certificate_type'] != 2) {
                throw new ValidationException(static::$t->_('vendor_info.011'), ErrCode::$VALIDATE_ERROR);
            }
        }

        return true;
    }

    /**
     * @param $params
     * @param $not_must
     * @return mixed
     */
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    public function handle_oss_file($oss_file_arr)
    {
        if (empty($oss_file_arr) || !is_array($oss_file_arr)) {
            return '';
        }
        $oss_key_arr = [];
        foreach ($oss_file_arr as $item) {
            $oss_key_arr[] = implode('@', $item);
        }
        return implode(',', $oss_key_arr);
    }

    /**
     * @param $path
     * @return array|string
     */
    public function getRemotePath($path)
    {
        if (empty($path)) {
            return [];
        }
        $path_arr = [];
        if (is_array($path)) {
            foreach ($path as $val) {
                $file_arr = $this->getOssFileName($val);
                $path_arr[] = [
                    'file_name' => $file_arr['file_name'],
                    'bucket_name' => $file_arr['bucket_name'],
                    'object_key' => $file_arr['object_key'],
                ];
            }
        } else {
            $file_arr = $this->getOssFileName($path);
            $path_arr[] = [
                'file_name' => $file_arr['file_name'],
                'bucket_name' => $file_arr['bucket_name'],
                'object_key' => $file_arr['object_key'],
            ];
        }

        return $path_arr;
    }

    /**
     * @param $oss_file
     * @return array|mixed
     */
    private function getOssFileName($oss_file)
    {
        if (empty($oss_file) || strpos($oss_file, '@') == false) {
            return [
                'file_name' => '',
                'bucket_name' => '',
                'object_key' => '',
            ];
        }
        list($file_name, $bucket, $object_key) = explode('@', $oss_file);

        return [
            'file_name' => $file_name,
            'bucket_name' => $bucket,
            'object_key' => $object_key,
        ];
    }


    /**
     * 获取合同编号
     * @return string
     */
    /**
     * @param $type
     * @return string
     */
    public static function getVendorNo($type)
    {
        $owner_keys = self::getOwnerKey($type);
        $cache_key = $owner_keys['cache_key'];
        $prefix = $owner_keys['prefix'];
        if (self::getVendorCounter($cache_key)) {           //有计数器
            $no = self::incrVendorCounter($cache_key);
        } else {                                    //没有计数器（一直都没有，有但是存在宕机）
            $no = self::setVendorCounter($cache_key, $prefix);
        }
        return $prefix . '-' . date('ymd') . sprintf('%04s', $no);
    }

    /**
     * 判断计数器是否存在
     * @param $cache_key
     * @return mixed
     */
    private static function getVendorCounter($cache_key)
    {
        return RedisClient::getInstance()->getClient()->exists($cache_key);
    }

    /**
     * 计数器不存在的情况下
     * @param $cache_key
     * @param $prefix
     * @return false|int|string
     */
    private static function setVendorCounter($cache_key, $prefix)
    {
        $no = 0;
        $vendor = Vendor::getFirst([
            'columns' => 'vendor_id',
            "vendor_id like '{$prefix}%'",
            'order' => 'id DESC'
        ]);
        if (!empty($vendor->vendor_id)) {   //数据库里有，取一下值
            $no = substr($vendor->vendor_id, -4, 4);
        }
        ++$no;
        RedisClient::getInstance()->getClient()->setex($cache_key, RedisExpire::ONE_DAY, $no);

        return $no;
    }

    /**
     * @param $type
     * @return array
     */
    private static function getOwnerKey($type)
    {
        $cache_key = $prefix = '';
        switch ($type) {
            case Enums::VENDOR_OWNERSHIP_CHINA:
                $cache_key = RedisKey::VENDOR_CREATE_CHINA_COUNTER;
                $prefix = Enums::VENDOR_PREFIX_CHINA;
                break;
            case Enums::VENDOR_OWNERSHIP_THAILAND:
                $cache_key = RedisKey::VENDOR_CREATE_THAILAND_COUNTER;
                $prefix = Enums::VENDOR_PREFIX_THAILAND;
                break;
            default:
                $cache_key = RedisKey::VENDOR_CREATE_DEFAULT_COUNTER;
                $prefix = RedisKey::VENDOR_CREATE_DEFAULT_COUNTER_PREFIX;
                break;
        }

        return [
            'cache_key' => $cache_key,
            'prefix' => $prefix,
        ];
    }

    /**
     * 计数器存在的情况下
     * @param $cache_key
     * @return mixedsss
     */
    private static function incrVendorCounter($cache_key)
    {
        return RedisClient::getInstance()->getClient()->incrBy($cache_key, 1);
    }

    /**
     * 获取静态配置
     */
    public function getStaticItem()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $ownership_item = SupplierOwnershipModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
                'order' => 'id ASC'
            ])->toArray();

            $company_nature_item = SupplierCompanyNatureModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
                'order' => 'id ASC'
            ])->toArray();

            $certificate_type_item = SupplierCertificateTypeModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
                'order' => 'id ASC'
            ])->toArray();

            $application_module_item = VendorApplicationModuleModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
                'order' => 'id ASC'
            ])->toArray();

            $vendor_purchase_products = EnumsService::getInstance()->getSettingEnvValueMap('vendor_purchase_products');
            $supply_product           = $vendor_purchase_products['vendor_supply_products'];
            $service_product          = $vendor_purchase_products['vendor_service_products'];

            $t = static::$t;
            foreach ($ownership_item as $value) {
                $data['ownership_item'][] = [
                    'value' => $value['id'],
                    'label' => $t[$value['translation_code']],
                ];
            }

            foreach ($company_nature_item as $value) {
                $data['company_nature_item'][] = [
                    'value' => $value['id'],
                    'label' => $t[$value['translation_code']],
                ];
            }

            foreach ($certificate_type_item as $value) {
                $data['certificate_type_item'][] = [
                    'value' => $value['id'],
                    'label' => $t[$value['translation_code']],
                ];
            }

            foreach ($application_module_item as $value) {
                $data['application_module_item'][] = [
                    'value' => $value['id'],
                    'label' => $t[$value['translation_code']],
                ];
            }

            foreach (Enums::$contract_status as $key => $status) {
                if (Enums::CONTRACT_STATUS_CANCEL == $key) continue;
                $data['contract_status_item'][] = [
                    'value' => $key,
                    'label' => $t[$status],
                ];
            }
            //支付方式
            foreach (Enums\VendorEnums::$payment_method as $key => $status) {
                if (Enums\VendorEnums::PAYMENT_METHOD_BOTH == $key) continue;
                $data['payment_method_item'][] = [
                    'value' => $key,
                    'label' => $t[$status],
                ];
            }
            //供应商等级枚举
            foreach (VendorEnums::$vendor_grade_item as $key => $value) {
                $data['vendor_grade_item'][] = [
                    'value' => $key,
                    'label' => $t[$value]
                ];
            }

            //供应商等级状态枚举
            foreach (VendorEnums::$vendor_grade_status_item as $key => $value) {
                $data['grade_status_item'][] = [
                    'value' => $key,
                    'label' => $t[$value]
                ];
            }

            //供应商主供/服务枚举

            foreach ($supply_product as &$supply_item) {
                $supply_item['label'] = $t[$supply_item['label']];
            }

            foreach ($service_product as &$service_item){
                $service_item['label'] = $t[$service_item['label']];
            }
            $data['product_cate_item'] = [
                'main_product'    => $supply_product,
                'service_product' => $service_product
            ];

            //供应商采购类别枚举
            foreach (VendorEnums::$vendor_purchase_type_item as $key => $value) {
                $data['purchase_type_item'][] = [
                    'value' => $key,
                    'label' => $t[$value]
                ];
            }

            //暂停时长
            foreach (VendorEnums::$vendor_suspend_item as $key => $value) {
                $data['suspend_item'][] = [
                    'value' => $key,
                    'label' => $t[$value]
                ];
            }


            $data['grade_approve_item'] = $data['contract_status_item'];

            //第三方支付方
            $data['third_payer_item'] = EnumsService::getInstance()->getVendorThirdPayer();
            //审批通过后可以编辑供应商信息的工号
            $data['audit_pass_can_edit_staffs'] = $this->getAuditPassCanEditStaffs();
            //供应商编辑-采购人员
            $data['vendor_edit_purchase_staff_ids'] = EnumsService::getInstance()->getSettingEnvValueIds('vendor_edit_purchase_staff_ids');
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

            $this->getDI()->get('logger')->warning('获取供应商静态配置失败:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取审批通过后可以编辑供应商信息的工号
     * @return string
     */
    public function getAuditPassCanEditStaffs()
    {
        return EnvModel::getEnvByCode('store_access_staff_id','');
    }

    /**
     * 获取国家/地区
     *
     * @param int $is_del
     * @return array
     */
    public function getCountryReginItem(int $is_del = 0)
    {
        $conditions = '';
        $bind = [];
        if (in_array($is_del, [0, 1])) {
            $conditions = 'is_del = :is_del:';
            $bind['is_del'] = $is_del;
        }
        $ownership_item = SupplierOwnershipModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ['id', 'translation_code', 'is_del'],
            'order' => 'id ASC',
        ])->toArray();
        return array_column($ownership_item, null, 'id');
    }

    /**
     * 获取静态配置翻译key
     */
    public function getStaticTranslationCodeItem()
    {
        $data = [];

        try {
            $ownership_item = $this->getCountryReginItem(GlobalEnums::IS_NO_DELETED);
            $data['ownership_item'] = array_column($ownership_item, 'translation_code', 'id');

            $company_nature_item = SupplierCompanyNatureModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
            ])->toArray();
            $data['company_nature_item'] = $company_nature_item ? array_column($company_nature_item, 'translation_code', 'id') : [];

            $certificate_type_item = SupplierCertificateTypeModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
            ])->toArray();
            $data['certificate_type_item'] = $certificate_type_item ? array_column($certificate_type_item, 'translation_code', 'id') : [];

            $application_module_item = VendorApplicationModuleModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'translation_code'],
            ])->toArray();
            $data['application_module_item'] = $application_module_item ? array_column($application_module_item, 'translation_code', 'id') : [];

            $bank_item = SupplierBankModel::find([
                'condition' => 'is_del = 0',
                'columns' => ['id', 'bank_code'],
            ])->toArray();
            $data['bank_item'] = $bank_item ? array_column($bank_item, 'bank_code', 'id') : [];

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->warning('获取供应商静态配置翻译key失败:' . $e->getMessage());
        }
        return $data;
    }

    /**
     * 根据供应商名称查询
     * @param string $vendor_name
     * @return mixed
     */
    public function getVendorByName(string $vendor_name)
    {
        $data = [];

        try {
            if (empty($vendor_name)) {
                return $data;
            }

            $data = Vendor::getFirst([
                'conditions' => 'vendor_name = :vendor_name: and grade_status != :grade_status:',
                'bind' => [
                    'vendor_name' => $vendor_name,
                    'grade_status' => VendorEnums::VENDOR_GRADE_STATUS_INVALID
                ]
            ]);

            $data = !empty($data) ? $data->toArray() : [];

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('根据名称获取供应商信息:' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 根据供应商名称查询
     *
     * @param $bank_no
     * @return mixed
     */
    public function getVendorByBank($bank_no)
    {
        $data = [];
        try {
            $bank_no = trim($bank_no);
            if (!empty($bank_no)) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns(['main.vendor_id,detail.bank_no,detail.id']);
                $builder->from(['main' => Vendor::class]);
                $builder->leftJoin(VendorPaymentDetailModel::class, 'main.id = detail.main_id', 'detail');
                $builder->andWhere('main.status IN ({status:array})', ['status' => [Enums::WF_STATE_PENDING, Enums::WF_STATE_APPROVED]]);
                $builder->andWhere('main.grade_status != :grade_status:', ['grade_status' => VendorEnums::VENDOR_GRADE_STATUS_INVALID]);
                $builder->andWhere('detail.bank_no = :bank_no:',['bank_no' => $bank_no]);
                $data = $builder->getQuery()->execute()->toArray();
            }
        } catch (\Exception $e) {
            $this->logger->error('根据开户行获取供应商信息:' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 根据供应商名称查询
     * @param $vendor_id
     * @return mixed
     */
    public function getVendorModule($vendor_id)
    {
        $data = [];
        try {
            $data = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id: ',
                'bind' => ['vendor_id' => $vendor_id]
            ]);

            $data = !empty($data) ? $data->toArray() : [];

        } catch (\Exception $e) {
            $this->getDI()->get('logger')->error('供应商应用模块获取失败:' . $e->getMessage());
        }

        return $data;
    }

    /**
     * 去bi里面取相关数据
     * @param $userId
     * @return array
     */
    public function getUserMetaFromBi($userId)
    {

        $model = (new UserService)->getUserByIdInRbi($userId);
        if (empty($model)) {
            return [];
        }
        $department_id = $model->sys_department_id;
        $node_department_id = $model->node_department_id;

        $t = DepartmentModel::findFirst([
            "conditions" => "id = :id:",
            "bind" => [
                "id" => empty($node_department_id) ? $department_id : $node_department_id,
            ]
        ]);
        $jt = $model->getJobTitle();

        return [
            'create_id' => $model->staff_info_id ?? "",
            'create_name' => $this->getNameAndNickName($model->name ?? "",$model->nick_name??""),
            'name_en'     => $model->name_en,
            'create_department' => !empty($t) ? $t->name : "",
            'create_department_id' => empty($node_department_id) ? $department_id : $node_department_id,
            'department_id' => $department_id,
            'node_department_id' => $node_department_id, //前端用来判断是否去掉广告
            'create_job_title_name' => $jt ? $jt->job_name : '',
            'apply_email' => $model->email ?? "",

        ];

    }

    /**
     * 判断是否同步金碟
     * @param $company_id
     * @return int
     */
    public function isSendKingDee($company_id)
    {
        $kingdee_company_id_arr = EnumsService::getInstance()->getSettingEnvValueIds('kingdee_company_ids');
        if (!empty($kingdee_company_id_arr)) {
            foreach ($company_id as $k => $v) {
                if (in_array($v, $kingdee_company_id_arr)) {
                    $is_send_kingdee = KingDeeEnums::IS_SEND_KING_DEE_1;
                    break;
                }

            }

        }
        return $is_send_kingdee ?? 0;
    }

}
