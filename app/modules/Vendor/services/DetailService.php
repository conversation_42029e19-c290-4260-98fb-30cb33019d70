<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;
use App\Models\oa\VendorPurchaseRelationModel;
use App\Modules\Vendor\Models\VendorRecordLogModel;
use App\Modules\Workflow\Services\FYRService;
use App\Modules\Workflow\Services\WorkflowServiceV2;

class DetailService extends BaseService
{
    private static $instance;
    protected static $pay_field_arr= [
        'bank_pay_name'=>'field_bank_pay_name_text',
        'bank_account_name'=>'field_bank_account_name_text',
        'bank_no'=>'field_bank_no_text',
    ];

    private function __construct()
    {
    }

    /**
     * @return DetailService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $vendor_id
     * @param $module_type
     * @return array|mixed
     */
    public function getDetail($vendor_id, $module_type, $uid)
    {
        $vendor = Vendor::getFirst([
            'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor_id]
        ]);

        if (empty($vendor)) {
            return [];
        }
        $vendor_payment_detail = $vendor->getDetails();
        $vendor = $vendor->toArray();
        $vendor_payment_detail=$vendor_payment_detail->toArray();

        $req = (new VendorFlowService())->getRequest($vendor['id']);

        //待回复征询ID
        $ask = (new FYRService())->getRequestToByReplyAsk($req,$uid);
        $vendor['ask_id'] = $ask ? $ask->id:'';

        // 获取应用模块
        $applications = VendorApplicationModuleRelModel::find([
            'conditions' => 'vendor_id = :vendor_id:',
            'bind' => ['vendor_id' => $vendor['vendor_id']],
        ])->toArray();
        $vendor['application_module_arr'] = $applications ? array_column($applications, 'application_module_id') : [];

        $use_company = [];
        if (!empty($vendor['use_company_id'])) {
            $coo_company_list = (new PurchaseService())->getCooCostCompany();
            $use_company_arr  = array_column($coo_company_list, 'cost_company_name', 'cost_company_id');

            $use_company_id = explode(',', $vendor['use_company_id']);

            foreach ($use_company_id as $company) {
                $use_company[] = [
                    'cost_company_id'   => $company,
                    'cost_company_name' => $use_company_arr[$company] ?? ''
                ];

            }
        }
        //获取主供/服务类型 产品
        $relation_products = VendorPurchaseRelationModel::find([
            'conditions' => 'vendor_id = :vendor_id:',
            'bind'       => ['vendor_id' => $vendor_id]
        ])->toArray();

        $vendor['product_category']       = array_column($relation_products, 'product_category');
        $vendor['purchase_type'] = ($vendor['purchase_type'] == 0) ? '' : $vendor['purchase_type'];

        $vendor['use_company'] = $use_company;

        // 获取附件
        $attaches = AttachModel::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND deleted = 0',
            'bind' => [
                'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                'oss_bucket_key' => $vendor['id']
            ],
            'columns' => ['id', 'file_name' ,'bucket_name', 'object_key', 'sub_type']
        ])->toArray();
        // 资质附件
        $vendor['attachment_arr'] = [];
        // bir2303附件
        $vendor['bir2303_arr'] = [];

        // 注册文件(公司/个人)
        $vendor['registration_file'] = [];

        // 银行对账单信头
        $vendor['bank_statement_letterhead_file'] = [];

        // 所得税表头(公司/个人)
        $vendor['income_tax_form_header_file'] = [];

        // SST登记表/SST登记信
        $vendor['sst_registration_form_file'] = [];

        if (!empty($attaches)) {
            foreach ($attaches as $item) {
                if ($item['sub_type'] == Enums::OSS_SUB_TYPE_VENDOR_BACK_QUALIFICATIONS) {
                    $vendor['attachment_arr'][] = $item;
                } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_VENDOR_BACK_BIR_2303) {
                    $vendor['bir2303_arr'][] = $item;
                } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_REGISTRATION_FILE) {
                    $vendor['registration_file'][] = $item;
                } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_BANK_STATEMENT_LETTERHEAD_FILE) {
                    $vendor['bank_statement_letterhead_file'][] = $item;
                } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_INCOME_TAX_FORM_HEADER_FILE) {
                    $vendor['income_tax_form_header_file'][] = $item;
                } else if ($item['sub_type'] == Enums::OSS_SUB_TYPE_SST_REGISTRATION_FORM_FILE) {
                    $vendor['sst_registration_form_file'][] = $item;
                }
            }
        }

        // 审批日志
        $vendor['auth_logs'] = [];
        if (!empty($req)) {
            $vendor['auth_logs'] = $this->getAuditLogs($req, false);
        }
        //查询修改日志
        $record_log= $this->getUpdateLogs($vendor_id);
        $vendor['edit_logs']= $record_log??[];

        $vendor = $this->handleData($vendor);
        $vendor_payment_detail = $this->handleDetailData($vendor_payment_detail);
        $vendor['pays'] = $vendor_payment_detail;

        return $vendor;
    }

    /**
     * @param $data
     * @return array
     */
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }

        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();

        $data['company_nature_title'] = $static_item['company_nature_item'][$data['company_nature']] ? static::$t->_($static_item['company_nature_item'][$data['company_nature']]) : '';
        $data['certificate_type_title'] = $static_item['certificate_type_item'][$data['certificate_type']] ? static::$t->_($static_item['certificate_type_item'][$data['certificate_type']]) : '';
        $data['ownership_title'] = $static_item['ownership_item'][$data['ownership']] ? static::$t->_($static_item['ownership_item'][$data['ownership']]) : '';
        $data['ownership_title'] = $static_item['ownership_item'][$data['ownership']] ? static::$t->_($static_item['ownership_item'][$data['ownership']]) : '';
        $data['attachment'] = empty($data['attachment']) ? '' : explode(',', $data['attachment']);
        $data['purchase_type'] = empty($data['purchase_type']) ? '' : (int)$data['purchase_type'];
        $data['purchase_amount'] = (empty($data['purchase_amount']) && 0 != $data['purchase_amount']) ? '' : $data['purchase_amount'];
        $data['purchase_currency_text'] = static::$t->_(GlobalEnums::$currency_item[$data['purchase_currency']] ?? '');
        $data['purchase_currency'] = empty($data['purchase_currency']) ? '' : $data['purchase_currency'];


        return $data;
    }

    /**
     * @param $data
     * @return array
     */
    private function handleDetailData($data)
    {
        if (empty($data) || !is_array($data)) {
            return [];
        }


        // 第三方支付方配置
        $third_payer_item = EnumsService::getInstance()->getVendorThirdPayer();
        $third_payer_item = array_column($third_payer_item, 'bank_no_is_required', 'value');

        foreach ($data as &$item){
            $item['payment_method'] = (int)$item['payment_method'];
            $item['payment_method_text'] = static::$t->_(Enums\VendorEnums::$payment_method[$item['payment_method']]);

            // 银行账号是否必填, 默认必填, 个别第三方支付方会配置未非必填
            $item['bank_no_is_required'] = $third_payer_item[$item['bank_pay_name']] ?? '1';
        }

        return $data;
    }

    /**
     * 获取审批日志
     * @param $req
     * @param bool $if_download
     * @return array
     */
    protected function getAuditLogs($req, $if_download = false)
    {
        $auth_logs = (new WorkflowServiceV2())->getAuditLogs($req);

        // 下载的时候不要申请
        if ($if_download) {
            $temp = [];
            foreach ($auth_logs as $k=>$v) {
                // 如果申请的就跳过
                if ($v['action'] == 0) {
                    continue;
                }

                $temp[] = $v;
            }

            $auth_logs = $temp;
        }

        return $auth_logs;
    }
    /**
     * 费用公司枚举
     * */
    public function getCooCompany()
    {
        $coo_company_list= [];
        $coo_company_list = (new PurchaseService())->getCooCostCompany();
        return $coo_company_list;
    }

    public function getUpdateLogs($vendor_id)
    {
        $record_log = VendorRecordLogModel::find([
            'conditions' => 'vendor_id = :vendor_id:',
            'bind'       => ['vendor_id' => $vendor_id]
        ])->toArray();
        if (empty($record_log)) {
            return [];
        }
        $lang = static::$t;
        $pay_field_arr = self::$pay_field_arr;
        // 获取静态配置code 与 翻译key
        $static_item = self::getStaticTranslationCodeItem();
        foreach ($record_log as &$item){
            $item['pay_log'] = json_decode($item['pay_log'],true);
            foreach ($item['pay_log'] as &$item_1){
                foreach ($item_1 as &$item_2){

                    if (isset($item_2['payment_method']) && $item_2['payment_method'] == Enums\VendorEnums::PAYMENT_METHOD_BANK && $item_2['field'] == 'bank_pay_name') {
                        $item_2['before']=  !empty($static_item['bank_item'][$item_2['before']]) ? $lang[$static_item['bank_item'][$item_2['before']]] :$item_2['before'];
                        $item_2['after']=  !empty($static_item['bank_item'][$item_2['after']]) ? $lang[$static_item['bank_item'][$item_2['after']]] :$item_2['after'];
                    }
                    if($item_2['field'] == 'bank_pay_name'&&is_numeric($item_2['before'])){
                        $item_2['before']=  !empty($static_item['bank_item'][$item_2['before']]) ? $lang[$static_item['bank_item'][$item_2['before']]]  : $item_2['before'];

                    }
                    $item_2['field'] =  $lang->_($pay_field_arr[$item_2['field']]);

                    unset($item_2['payment_method']);
               }
           }

            $item['attachment_edit'] = json_decode($item['attachment_edit'],true);
            $item['attachment_edit'] = empty($item['attachment_edit'])?[]:$item['attachment_edit'];

            $item=  array_merge($item,$this->getUserMetaFromBi($item['create_id']));
        }

        return $record_log;
    }
}
