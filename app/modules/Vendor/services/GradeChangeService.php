<?php

namespace App\Modules\Vendor\Services;

use App\Library\Enums;
use App\Library\Enums\VendorEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;

use App\Library\Validation\ValidationException;
use App\Modules\User\Models\AttachModel;
use App\Modules\Vendor\Models\Vendor;
use App\Modules\Vendor\Models\VendorApplicationModuleRelModel;


class GradeChangeService extends BaseService
{
    public static $not_must_params = [
        'company_website',
        'artificial_person',
        'company_phone',
        'attachment_arr',
        'sap_supplier_no'
    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return GradeChangeService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     *  注册变更 （从认证/注销到注册）
     * @param $data
     * @param $user
     * @return array
     * */
    public function register($data, $user)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //校验当前 是否存在供应商信息待审批

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_INFO_GET_ERROR);
            }
            if (0 == $vendor->purchase_type) {
                throw new ValidationException(static::$t->_('vendor_purchase_type_is_required'), ErrCode::$VALIDATE_ERROR);
            }
            //供应商待审批中不可变更
            if ($vendor->status == Enums::WF_STATE_PENDING || $vendor->grade_status != VendorEnums::VENDOR_GRADE_STATUS_NORMAL || $vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('vendor_grade_status_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            //校验当前等级是否是 认证或注销
            if (!in_array($vendor->grade, [VendorEnums::VENDOR_GRADE_AUTHENTICATION, VendorEnums::VENDOR_GRADE_CANCELLATION])) {
                throw new ValidationException(static::$t->_('vendor_grade_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取应用模块
            $applications           = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']],
            ])->toArray();
            $application_module_arr = $applications ? array_column($applications, 'application_module_id') : [];

            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr) && empty($vendor->is_purchase_reason)) {
                throw new ValidationException(static::$t->_('vendor_info.010'), ErrCode::$VALIDATE_ERROR);
            }
            //更新主库  并记录变更日志
            $update_data = [
                'grade_action'         => VendorEnums::VENDOR_ACTION_REGISTER,
                'grade_final_state'    => VendorEnums::VENDOR_ACTION_REGISTER,
                'grade_approve_status' => Enums::WF_STATE_PENDING,
                'change_description'   => $data['change_description'],
                'grade_create_id'      => $user['id'],
            ];
            $update_bool = $vendor->i_update($update_data);
            if ($update_bool === false) {
                throw new BusinessException('供应商等级变更为注册 - 主表更新失败' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($vendor), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }
            //创建审批流
            $flow_obj = new VendorGradeFlowService();
            //由于存在历史数据没有经过审批流
            $req = $flow_obj->getRequest($vendor->id);
            if (empty($req)) {//首次创建分级审批流
                $flow_bool = $flow_obj->createRequest($vendor->id, $user);
            } else {
                $flow_bool = $flow_obj->recommit($vendor, $user);
            }

            if ($flow_bool === false) {
                throw new BusinessException('供应商等级变更为注册 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();


        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-register-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];


    }

    /**
     * 认证变更
     * @param $data
     * @param $user
     * @return array
     * */
    public function authentication($data, $user)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //校验当前 是否存在供应商信息待审批

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_INFO_GET_ERROR);
            }
            if (0 == $vendor->purchase_type) {
                throw new ValidationException(static::$t->_('vendor_purchase_type_is_required'), ErrCode::$VALIDATE_ERROR);
            }

            //供应商待审批中不可变更
            if ($vendor->status == Enums::WF_STATE_PENDING || $vendor->grade_status != VendorEnums::VENDOR_GRADE_STATUS_NORMAL || $vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('vendor_grade_status_is_wrong'), ErrCode::$VALIDATE_ERROR);

            }

            //校验当前等级是否是 注册
            if ($vendor->grade != VendorEnums::VENDOR_GRADE_REGISTER) {
                throw new ValidationException(static::$t->_('vendor_grade_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取应用模块
            $applications           = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']],
            ])->toArray();
            $application_module_arr = $applications ? array_column($applications, 'application_module_id') : [];

            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr) && empty($vendor->is_purchase_reason)) {
                throw new ValidationException(static::$t->_('vendor_info.010'), ErrCode::$VALIDATE_ERROR);
            }

            //更新主库  并记录变更日志
            $update_data = [
                'grade_action'         => VendorEnums::VENDOR_ACTION_AUTHENTICATION,
                'grade_final_state'    => VendorEnums::VENDOR_ACTION_AUTHENTICATION,
                'grade_approve_status' => Enums::WF_STATE_PENDING,
                'change_description'   => $data['change_description'],
                'grade_create_id'      => $user['id'],
            ];
            $update_bool = $vendor->i_update($update_data);
            if ($update_bool === false) {
                throw new BusinessException('供应商等级变更为认证 - 主表更新失败' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($vendor), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }

            if (!empty($data['authentication_attachment'])) {
                $authentication_attachments = [];
                foreach ($data['authentication_attachment'] as $k => $file) {
                    $authentication_attachments[$k] = [
                        'sub_type'        => Enums::OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION,
                        'oss_bucket_type' => Enums::OSS_BUCKET_TYPE_VENDOR_BACK,
                        'oss_bucket_key'  => $vendor->id,
                        'bucket_name'     => $file['bucket_name'],
                        'object_key'      => $file['object_key'],
                        'file_name'       => $file['file_name'],
                    ];
                }

                $bool = $db->updateAsDict(
                    (new AttachModel())->getSource(),
                    [
                        'deleted' => Enums\GlobalEnums::IS_DELETED
                    ],
                    [
                        'conditions' => "oss_bucket_key = {$vendor->id}  AND oss_bucket_type = " . Enums::OSS_BUCKET_TYPE_VENDOR_BACK . " AND sub_type = " . Enums::OSS_SUB_TYPE_VENDOR_BACK_GRADE_AUTHENTICATION,
                    ]

                );

                if ($bool === false) {
                    throw new BusinessException('供应商认证 - 附件批量软删除失败' . $vendor->id . ' 可能的原因是: ' . get_data_object_error_msg($db), ErrCode::$VENDOR_ATTACHMENT_CREATE_ERROR);
                }

                if (!empty($authentication_attachments)) {
                    $attach      = new AttachModel();
                    $attach_bool = $attach->batchInsert($authentication_attachments);
                    if ($attach_bool === false) {
                        throw new BusinessException('供应商认证 - 附件批量创建失败' . json_encode($authentication_attachments, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($attach), ErrCode::$VENDOR_ATTACHMENT_CREATE_ERROR);
                    }
                }
            }

            //创建审批流
            $flow_obj = new VendorGradeFlowService();
            $req      = $flow_obj->getRequest($vendor->id);
            if (empty($req)) {//首次创建分级审批流
                $flow_bool = $flow_obj->createRequest($vendor->id, $user);
            } else {
                $flow_bool = $flow_obj->recommit($vendor, $user);
            }
            if ($flow_bool === false) {
                throw new BusinessException('供应商等级变更为认证 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-authentication-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];


    }

    /**
     * 注销变更
     * @param $data
     * @param $user
     * @return array
     * */
    public function cancellation($data, $user)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //校验当前 是否存在供应商信息待审批

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }
            if (0 == $vendor->purchase_type) {
                throw new ValidationException(static::$t->_('vendor_purchase_type_is_required'), ErrCode::$VALIDATE_ERROR);
            }

            //供应商待审批中不可变更
            if ($vendor->status == Enums::WF_STATE_PENDING || $vendor->grade_status != VendorEnums::VENDOR_GRADE_STATUS_NORMAL || $vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('vendor_grade_status_is_wrong'), ErrCode::$VALIDATE_ERROR);

            }

            //校验当前等级是否是 注册或认证
            if (!in_array($vendor->grade, [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION])) {
                throw new ValidationException(static::$t->_('vendor_grade_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取应用模块
            $applications           = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']],
            ])->toArray();
            $application_module_arr = $applications ? array_column($applications, 'application_module_id') : [];

            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr) && empty($vendor->is_purchase_reason)) {
                throw new ValidationException(static::$t->_('vendor_info.010'), ErrCode::$VALIDATE_ERROR);
            }

            //更新主库  并记录变更日志
            $update_data = [
                'grade_action'         => VendorEnums::VENDOR_ACTION_CANCELLATION,
                'grade_final_state'    => VendorEnums::VENDOR_ACTION_CANCELLATION,
                'grade_approve_status' => Enums::WF_STATE_PENDING,
                'change_description'   => $data['change_description'],
                'grade_create_id'      => $user['id'],
            ];
            $update_bool = $vendor->i_update($update_data);
            if ($update_bool === false) {
                throw new BusinessException('供应商等级变更为注销 - 主表更新失败' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($vendor), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }

            //创建审批流
            $flow_obj = new VendorGradeFlowService();
            $req      = $flow_obj->getRequest($vendor->id);
            if (empty($req)) {//首次创建分级审批流
                $flow_bool = $flow_obj->createRequest($vendor->id, $user);
            } else {
                $flow_bool = $flow_obj->recommit($vendor, $user);
            }
            if ($flow_bool === false) {
                throw new BusinessException('供应商等级注销变更 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-cancellation-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];


    }

    /**
     * 拉黑变更
     * @param $data
     * @param $user
     * @return array
     * */
    public function block($data, $user)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //校验当前 是否存在供应商信息待审批

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_INFO_GET_ERROR);
            }
            if (0 == $vendor->purchase_type) {
                throw new ValidationException(static::$t->_('vendor_purchase_type_is_required'), ErrCode::$VALIDATE_ERROR);
            }


            //供应商待审批中不可变更
            if ($vendor->status == Enums::WF_STATE_PENDING || $vendor->grade_status != VendorEnums::VENDOR_GRADE_STATUS_NORMAL || $vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('vendor_grade_status_is_wrong'), ErrCode::$VALIDATE_ERROR);

            }

            //校验当前等级是否是 注册或认证 注销
            if (!in_array($vendor->grade, [VendorEnums::VENDOR_GRADE_REGISTER, VendorEnums::VENDOR_GRADE_AUTHENTICATION, VendorEnums::VENDOR_GRADE_CANCELLATION])) {
                throw new ValidationException(static::$t->_('vendor_grade_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }

            // 获取应用模块
            $applications           = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']],
            ])->toArray();
            $application_module_arr = $applications ? array_column($applications, 'application_module_id') : [];

            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr) && empty($vendor->is_purchase_reason)) {
                throw new ValidationException(static::$t->_('vendor_info.010'), ErrCode::$VALIDATE_ERROR);
            }

            //更新主库  并记录变更日志
            $update_data = [
                'grade_action'         => VendorEnums::VENDOR_ACTION_BLOCK,
                'grade_final_state'    => VendorEnums::VENDOR_ACTION_BLOCK,
                'grade_approve_status' => Enums::WF_STATE_PENDING,
                'change_description'   => $data['change_description'],
                'grade_create_id'      => $user['id'],
            ];
            $update_bool = $vendor->i_update($update_data);
            if ($update_bool === false) {
                throw new BusinessException('供应商等级变更为拉黑 - 主表更新失败' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($vendor), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }

            //创建审批流
            $flow_obj = new VendorGradeFlowService();
            $req      = $flow_obj->getRequest($vendor->id);
            if (empty($req)) {//首次创建分级审批流
                $flow_bool = $flow_obj->createRequest($vendor->id, $user);
            } else {
                $flow_bool = $flow_obj->recommit($vendor, $user);
            }
            if ($flow_bool === false) {
                throw new BusinessException('供应商等级注拉黑变更 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-block-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];


    }

    /**
     *  变更为暂停
     * @param $data
     * @param $user
     * @return array
     * */
    public function suspend($data, $user)
    {

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            //校验当前 是否存在供应商信息待审批

            $vendor = Vendor::getFirst([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']]
            ]);
            if (empty($vendor)) {
                throw new BusinessException(static::$t->_('vendor_get_info_failed_when_update'), ErrCode::$VENDOR_INFO_GET_ERROR);
            }
            if (0 == $vendor->purchase_type) {
                throw new ValidationException(static::$t->_('vendor_purchase_type_is_required'), ErrCode::$VALIDATE_ERROR);
            }

            //供应商待审批中不可变更
            if ($vendor->status == Enums::WF_STATE_PENDING || $vendor->grade_status != VendorEnums::VENDOR_GRADE_STATUS_NORMAL || $vendor->grade_approve_status == Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('vendor_grade_status_is_wrong'), ErrCode::$VALIDATE_ERROR);

            }

            //校验当前等级是否是 认证或注册
            if (!in_array($vendor->grade, [VendorEnums::VENDOR_GRADE_AUTHENTICATION, VendorEnums::VENDOR_GRADE_REGISTER])) {
                throw new ValidationException(static::$t->_('vendor_grade_is_wrong'), ErrCode::$VALIDATE_ERROR);
            }
            // 获取应用模块
            $applications           = VendorApplicationModuleRelModel::find([
                'conditions' => 'vendor_id = :vendor_id:',
                'bind'       => ['vendor_id' => $data['vendor_id']],
            ])->toArray();
            $application_module_arr = $applications ? array_column($applications, 'application_module_id') : [];

            if (in_array(VendorEnums::VENDOR_PURCHASE, $application_module_arr) && empty($vendor->is_purchase_reason)) {
                throw new ValidationException(static::$t->_('vendor_info.010'), ErrCode::$VALIDATE_ERROR);
            }

            //更新主库  并记录变更日志
            $update_data = [
                'grade_action'         => VendorEnums::VENDOR_ACTION_SUSPEND,
                'grade_final_state'    => VendorEnums::VENDOR_ACTION_SUSPEND,
                'grade_approve_status' => Enums::WF_STATE_PENDING,
                'change_description'   => $data['change_description'],
                'grade_create_id'      => $user['id'],
                'suspend_time'         => $data['suspend_time'],
            ];
            $update_bool = $vendor->i_update($update_data);
            if ($update_bool === false) {
                throw new BusinessException('供应商等级状态变更为暂停 - 主表更新失败' . json_encode($update_data, JSON_UNESCAPED_UNICODE) . '可能原因：.' . get_data_object_error_msg($vendor), ErrCode::$VENDOR_UPDATE_INFO_ERROR);
            }
            //创建审批流
            $flow_obj = new VendorGradeFlowService();
            $req      = $flow_obj->getRequest($vendor->id);
            if (empty($req)) {//首次创建分级审批流
                $flow_bool = $flow_obj->createRequest($vendor->id, $user);
            } else {
                $flow_bool = $flow_obj->recommit($vendor, $user);
            }
            if ($flow_bool === false) {
                throw new BusinessException('供应商等级状态变更为暂停 - 审批流创建失败', ErrCode::$VENDOR_WORKFLOW_CREATE_ERROR);
            }

            $db->commit();

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('vendor-grade-suspend-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => []
        ];


    }


}
