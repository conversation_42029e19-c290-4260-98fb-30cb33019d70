<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Enums\WorkflowManageEnums;
use App\Library\Enums\WorkflowNodeEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;

class ValidateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return ValidateService
     */
    public static function getInstance(): ValidateService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 基础配置校验
     * @return void
     * @throws ValidationException
     */
    public function validateBasicConfig($params)
    {
        $validateRules = [
            'relateObjectId'     => 'Required|Int',
            'auditName'          => 'Required|Str',
            'approvalRepeat'     => 'Required|Int',
            'approvalEqualApply' => 'Required|Int',
        ];
        Validation::validate($params, $validateRules);

        //获取超时类型配置
        $formConfig            = ConfigService::getInstance()->getFormConfig($params['relateObjectId']);
        $overtimeTypeConfig    = array_column($formConfig, 'code');
        $overtimeValidateRules = [
            'overtimeType'   => 'Required|IntIn:' . join(',', $overtimeTypeConfig),
            'overtimeDays'   => 'IfIntIn:overtimeType,' . join(',',
                    [
                        WorkflowManageEnums::WF_OVERTIME_TYPE_OVERALL,
                        WorkflowManageEnums::WF_OVERTIME_TYPE_EACH_NODE,
                    ]) . '|Required|IntGeLe:1,' . WorkflowManageEnums::OVERTIME_CONFIG_MAX_DAYS,
            'overtimePolicy' => 'IfIntIn:overtimeType,' . join(',',
                    [
                        WorkflowManageEnums::WF_OVERTIME_TYPE_OVERALL,
                        WorkflowManageEnums::WF_OVERTIME_TYPE_EACH_NODE,
                    ]) . '|Required|IntIn:' . join(',', [
                    WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_PASS,
                    WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT,
                    WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
                ]),
        ];

        if (ConfigService::getInstance()->isExistOvertimeForm($params['relateObjectId'])) {
            $validaForm = ConfigService::getInstance()->getOptionalForm($params['relateObjectId']);
            $overtimeValidateRules['overtimeColumn'] = 'IfIntEq:overtimeType,' . WorkflowManageEnums::WF_OVERTIME_TYPE_FORM . '|Required|StrIn:'.
                join(',', array_column($validaForm, 'value'));
        }

        $validateMessage       = '%s|>>>:missing required parameter %s or exists invalid value';
        foreach ($overtimeValidateRules as $key => $value) {
            $overtimeValidateRules[$key] = sprintf($validateMessage, $value, $key);
        }
        Validation::validate($params, $overtimeValidateRules);
    }
}