<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\Enums;
use App\Library\Validation\ValidationException;
use App\Modules\WorkflowManagement\Models\WorkflowBranchConditionModel;

class ParseNodeRelationService extends BaseService
{
    /**
     * 实例
     * @var void
     */
    private static $instance = '';

    /**
     * 单一实例
     * @return ParseNodeRelationService
     */
    public static function getInstance(): ParseNodeRelationService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 解析
     * @param array $groups
     * @return array
     */
    public function parse(array $groups = []): array
    {
        $totalCnt   = 0;

        //获取全部的条件
        //$allConditions = WorkflowBranchConditionModel::find([
        //    'conditions' => 'deleted = 0',
        //])->toArray();
        //$allConditions = array_column($allConditions, null, 'id');

        //获取全部的operator
        //$groupConditions = [];
        foreach ($groups as $group) {
            $conditions = [];
            foreach ($group as $item) {

                $cond = $item['conditionType'];
                $operator = $item['operator'];
                $curParamName = '$p' . ++$totalCnt;

                //计算公式
                $formula = $this->parseFormula($operator['operator_type'], $totalCnt, $item);

                $conditions[] = [
                    'formula' => $formula,
                    'sign' => $curParamName,
                    'type' => $cond['type'],
                ];
            }
            if (count($conditions) > 1) { //同一条件组内取"并"
                $conds    = array_column($conditions, 'formula');
                $condsStr = implode(' && ', $conds);

                //条件对应的类型
                $type = array_column($conditions, 'type', 'sign');
                $group_conditions[] = [
                    'formula' => $condsStr,
                    'map'     => $type
                ];
            } else {
                $current = current($conditions);
                $group_conditions[] = [
                    'formula' => $current['formula'],
                    'map' => [
                        $current['sign'] => $current['type']
                    ]
                ];
            }
        }

        if (count($group_conditions) > 1) { //存在多个条件组
            $conds  = array_column($group_conditions, 'formula');
            $formulaStr = implode(' || ', $conds);

            $maps = array_column($group_conditions, 'map');
            $mapping = [];
            foreach ($maps as $v) {
                $mapping = array_merge($mapping, $v);
            }

            $return = [
                'formula' => $formulaStr,
                'map'     => $mapping
            ];
        } else { //只有一个条件组
            $current = current($group_conditions);
            $return = [
                'formula' => $current['formula'],
                'map' => [
                    $current['sign'] => $current['type']
                ]
            ];
        }

        return $return;
    }

    /**
     * 解析
     * @param array $groups
     * @return array
     */
    public function parseEx(array $groups = []): array
    {
        if (empty($groups)) {
            return  [
                'formula' => [],
                'map'     => []
            ];
        }

        $totalCnt   = 0;

        //获取全部的条件
        $allConditions = WorkflowBranchConditionModel::find([
            'conditions' => 'deleted = 0',
        ])->toArray();
        $allConditions = array_column($allConditions, null, 'id');

        //获取全部的operator
        foreach ($groups as $group) {
            $conditions = [];
            foreach ($group as $item) {

                $cond = $item['conditionType'];
                $operator = $item['operator'];
                $curParamName = '$p' . ++$totalCnt;

                //计算公式
                $formula = $this->parseFormula($operator['operator_type'], $totalCnt, $item);

                $conditions[] = [
                    'formula' => $formula,
                    'sign' => $curParamName,
                    'type' => $allConditions[$cond['type']]['method'],
                ];
            }
            if (count($conditions) > 1) { //同一条件组内取"并"
                $conds    = array_column($conditions, 'formula');
                $condsStr = implode(' && ', $conds);

                //条件对应的类型
                $type = array_column($conditions, 'type', 'sign');
                $group_conditions[] = [
                    'formula' => $condsStr,
                    'map'     => $type
                ];
            } else {
                $current = current($conditions);
                $group_conditions[] = [
                    'formula' => $current['formula'],
                    'map' => [
                        $current['sign'] => $current['type']
                    ]
                ];
            }
        }
        if (count($group_conditions) > 1) { //存在多个条件组
            $conds  = array_column($group_conditions, 'formula');
            $formulaStr = implode(' || ', $conds);

            $maps = array_column($group_conditions, 'map');
            $mapping = [];
            foreach ($maps as $v) {
                $mapping = array_merge($mapping, $v);
            }

            $return = [
                'formula' => $formulaStr,
                'map'     => $mapping
            ];
        } else { //只有一个条件组
            $current = current($group_conditions);
            $return = [
                'formula' => $current['formula'],
                'map'     => $current['map'],
            ];
        }
        return $return;
    }

    /**
     * 解析
     * @param array $groups
     * @return array
     * @throws ValidationException
     */
    public function parseExV2(array $groups = []): array
    {
        if (empty($groups)) {
            return  [
                'formula' => "",
                'map'     => []
            ];
        }

        $totalCnt   = 0;

        //获取全部的条件
        $allConditions = WorkflowBranchConditionModel::find([
            'conditions' => 'deleted = 0',
        ])->toArray();
        $allConditions = array_column($allConditions, null, 'id');

        //获取全部的operator
        foreach ($groups as $group) {
            $conditions = [];
            foreach ($group as $item) {

                $cond = $item['conditionType'];
                $curParamName = '$p' . ++$totalCnt;

                //计算公式
                $formula = $this->parseFormula($item['operatorType'], $totalCnt, $item);

                //方便检查分支条件数据是否完整

                if (!isset($allConditions[$cond]['method'])) {
                    $errorMessage =  sprintf('Plash check table`workflow_branch_condition` ,id = %d ,method not exists!!!', $cond);
                    if (RUNTIME != 'pro') {
                        throw new ValidationException($errorMessage);
                    } else {
                        $this->logger->error($errorMessage);
                        throw new \Exception('Miss Branch Conditions,'. static::$t->_("please_contact_system_adminstrator"));
                    }
                }
                $conditions[] = [
                    'formula'   => $formula,
                    'sign'      => $curParamName,
                    'type'      => $allConditions[$cond]['method'],
                ];
            }
            if (count($conditions) > 1) { //同一条件组内取"并"
                $conds    = array_column($conditions, 'formula');
                $condsStr = implode(' && ', $conds);

                //条件对应的类型
                $type = array_column($conditions, 'type', 'sign');
                $group_conditions[] = [
                    'formula' => $condsStr,
                    'map'     => $type
                ];
            } else {
                $current = current($conditions);
                $group_conditions[] = [
                    'formula' => $current['formula'],
                    'map' => [
                        $current['sign'] => $current['type']
                    ]
                ];
            }
        }
        if (count($group_conditions) > 1) { //存在多个条件组
            $conds  = array_column($group_conditions, 'formula');
            $formulaStr = implode(' || ', $conds);

            $maps = array_column($group_conditions, 'map');
            $mapping = [];
            foreach ($maps as $v) {
                $mapping = array_merge($mapping, $v);
            }

            $return = [
                'formula' => $formulaStr,
                'map'     => $mapping
            ];
        } else { //只有一个条件组
            $current = current($group_conditions);
            $return = [
                'formula' => $current['formula'],
                'map'     => $current['map'],
            ];
        }
        return $return;
    }

    /**
     * 计算公式
     * @param $operator_type
     * @param $totalCnt
     * @param $value
     * @return string
     * @throws ValidationException
     */
    public function parseFormula($operatorType, $totalCnt, $item): string
    {
        if (in_array($operatorType, [Enums::OPERATE_IN, Enums::OPERATE_NOT_IN])) {

            if (isset($item['val'][0]) && is_array($item['val'][0]) && array_key_exists('value', $item['val'][0])) {
                $value = array_column($item['val'], 'value');
            } else {
                $value = $item['val'];
            }
        } else {

            if (isset($item['val']) && (is_array($item['val']) && array_key_exists('key', $item['val']) || isset($item['val']['value']))) {
                $value = $item['val']['value']; //兼容处理  有可能没有 value
            } else {
                $value = $item['val'] ?? ''; //兼容处理  有可能没有 value
            }
        }

        switch ($operatorType) {
            case Enums::OPERATE_EQ: //等于
                if (is_string($value)) {
                    $formula = sprintf("\$p%s %s '%s'", $totalCnt, '==', $value);
                } else {
                    $formula = sprintf("\$p%s %s %s", $totalCnt, '==', $value);
                }
                break;
            case Enums::OPERATE_GT: //大于
                $formula = sprintf("\$p%s %s %s", $totalCnt, '>', $value);
                break;
            case Enums::OPERATE_GE: //大于等于
                $formula = sprintf("\$p%s %s %s", $totalCnt, '>=', $value);
                break;
            case Enums::OPERATE_LT: //小于
                $formula = sprintf("\$p%s %s %s", $totalCnt, '<', $value);
                break;
            case Enums::OPERATE_LE: //小于等于
                $formula = sprintf("\$p%s %s %s", $totalCnt, '<=', $value);
                break;
            case Enums::OPERATE_NE: //不等于
                $formula = sprintf("\$p%s %s %s", $totalCnt, '!=', $value);
                break;
            case Enums::OPERATE_IN: //包含
                $value   = is_numeric($value[0]) ? implode(',', $value): sprintf('"%s"', implode('","', $value));
                $formula = sprintf("(is_array(\$p%s) ? count(array_intersect(\$p%s, [%s])) > 0: in_array(\$p%s, [%s]))", $totalCnt, $totalCnt, $value, $totalCnt, $value);
                break;
            case Enums::OPERATE_NOT_IN: //不包含
                $value   = is_numeric($value[0]) ? implode(',', $value): sprintf('"%s"', implode('","', $value));
                $formula = sprintf("(is_array(\$p%s) ? count(array_intersect(\$p%s, [%s])) == 0: !in_array(\$p%s, [%s]))", $totalCnt, $totalCnt, $value, $totalCnt, $value);
                break;
            case Enums::OPERATE_BELONG_TO:
                if (empty($value)) {
                    throw new ValidationException('Please check data');
                }
                //目前只有部门存在"属于"逻辑，暂时特殊处理
                $formula = sprintf("in_array(\$p%s, [#%s|%s#])", $totalCnt, 'DEPARTMENT', $value);
                break;
            case Enums::OPERATE_NOT_BELONG_TO:
                if (empty($value)) {
                    throw new ValidationException('Please check data');
                }
                //目前只有部门存在"属于"逻辑，暂时特殊处理
                $formula = sprintf("!in_array(\$p%s, [#%s|%s#])", $totalCnt, 'DEPARTMENT', $value);
                break;
            default:
                $formula = '';
                break;
        }
        return $formula;
    }

    /**
     * 校验条件
     * @param array $groups
     * @return array
     * @throws ValidationException
     */
    public function checkBranchConditions(array $groups = []): array
    {
        if (empty($groups)) {
            return [];
        }

        //反解析条件
        $service = ReverseParseService::getInstance()->init()->setParams($groups['form']);
        [$form, $label] = $service->reverseCondition();

        //获取无效的条件
        $invalid = $service->checkInvalidCondition();

        if (empty($invalid)) {
            return [];
        }

        return array_map(function ($item) use ($label, $groups) {
            return array_merge($item, ['reverse_label' => $label, 'uuid' => $groups['id']]);
        }, $invalid);
    }
}