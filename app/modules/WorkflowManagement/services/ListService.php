<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\ApiClient;
use App\Library\Enums;
use App\Library\Enums\WorkflowManageEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNoticeModel;
use App\Modules\WorkflowManagement\Models\WorkflowPermissionModel;


class ListService extends BaseService
{
    /**
     * 实例
     * @var void
     */
    private static $instance = '';

    private static $validate_list = [
        'name' => "IfStrNe:name,''|StrLenGe:0",
        'relate_type_id' => "IfStrNe:relate_type_id,|IntGe:0",
        'state' => "IfStrNe:state,|IntGe:0",
        'page_size' => 'Required|IntGt:0',
        'page_num' => 'Required|IntGt:0',
    ];

    private static $validate_save = [
        'basic_configure' => 'Required',
        'workflow_configure' => 'Required',
    ];

    private static $validate_edit = [
        'id' => 'Required',
    ];

    /**
     * 单一实例
     * @return ListService
     */
    public static function getInstance(): ListService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @return array
     */
    public static function getValidateList(): array
    {
        return self::$validate_list;
    }

    /**
     * @return string[]
     */
    public static function getValidateSave(): array
    {
        return self::$validate_save;
    }

    /**
     * @return string[]
     */
    public static function getValidateEdit(): array
    {
        return self::$validate_edit;
    }

    /**
     * 获取审批流列表
     * @param $params
     * @param $id
     * @return array
     */
    public function getWorkflowList($params, $id): array
    {
        $name         = $params['name'] ?? '';
        $relateTypeId = $params['relate_type_id'];
        $state        = isset($params['state']) && $params['state'] ? $params['state'] : 1;
        $pageNum      = $params['page_num'] ?? 1;
        $pageSize     = $params['page_size'] ?? 20;
        $offset       = $pageSize * ($pageNum - 1);

        //获取已经对接的审批流
        $validWorkflow = SysService::getInstance()->getWorkflowRelateType();

        //获取有权限的
        $permission = AdminService::getInstance()->getWorkflowPermissionByStaffId($id);
        $staffWorkflowType = array_column($permission, 'relate_type');
        $staffWorkflowTypeView = array_column($permission, 'is_view','relate_type');
        $staffWorkflowTypeEdit = array_column($permission, 'is_edit','relate_type');

        if (empty($staffWorkflowType)) {
            return [
                'item' => [],
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page' => $pageSize,
                    'total_count' => 0,
                ]
            ];
        }

        $workflowName = array_column($validWorkflow, 'name','code');

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => ByWorkflowModel::class]);
        $builder->where('is_view = :is_view:', ['is_view' => ByWorkflowModel::IS_VIEW]);
        if (!empty($name)) {
            $builder->andWhere('name LIKE :name: ', ['name' => '%' . $name . '%']);
        }

        if (!empty($relateTypeId)) {
            $builder->andWhere('relate_type = :type:', ['type' => $relateTypeId]);
        }

        if (isset($state)) {
            $builder->andWhere('state = :state:', ['state' => $state]);
        }
        $builder->inWhere('relate_type', $staffWorkflowType);

        $count = $builder->columns('count(1) as cou')->getQuery()->getSingleResult();
        $builder->columns('id
            ,name
            ,relate_type as biz_type
            ,description
            ,created_at
            ,updated_at
            ,state
            ,version');
        $builder->orderBy('id desc');
        $builder->limit($pageSize, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        $add_hour = $this->config->application->add_hour;

        foreach ($list as $k => $v) {
            $list[$k]['updated_at'] = date('Y-m-d H:i:s', strtotime($v['updated_at']) + $add_hour * 3600);
            $list[$k]['biz_type_text'] = $workflowName[$v['biz_type']];
            $list[$k]['is_view'] = $staffWorkflowTypeView[$v['biz_type']] == 1;
            $list[$k]['is_edit'] = $staffWorkflowTypeEdit[$v['biz_type']] == 1;
        }

        $return['item'] = $list;
        $return['pagination'] = [
            'current_page' => intval($pageNum),
            'per_page'     => intval($pageSize),
            'total_count'  => intval($count->cou),
        ];
        return $return;
    }

    /**
     * 软删除审批流
     * @param $params
     * @param $id
     * @return bool
     */
    public function delWorkflowList($params, $id): bool
    {
        $flowId = $params['id'] ?? 0;

        $workflowInfo = WorkflowModel::findFirst([
            'conditions' => 'id = :workflow_id: and deleted = 0',
            'bind'       => ['workflow_id' => $flowId]
        ]);
        if (!empty($workflowInfo) && $workflowInfo instanceof WorkflowModel) {
            $workflowInfo->setDeleted(Enums::WORKFLOW_DELETED);
            $workflowInfo->save();
        }

        return true;
    }

    /**
     * 获取员工离职异常提醒列表
     * @param $params
     * @param $id
     * @return array
     */
    public function getStaffResignNoticeList($params, $id): array
    {
        $exceptionType = $params['exception_type'];
        $relateTypeId = $params['relate_type_id'];
        $state = isset($params['state']) && $params['state'] ? $params['state']: 1;
        $pageNum = $paramIn['page_num'] ?? 1;
        $pageSize = $paramIn['page_size'] ?? 20;
        $offset = $pageSize * ($pageNum - 1);

        //获取翻译
        $ac = new ApiClient('by', '', 'getAuditType', static::$language);
        $ac->setParams([[]]);
        $res = $ac->execute();

        $code = $res['result']['code'] ?? $res['code'];
        $data = $res['result']['data'] ?? [];
        if ($code == 1) {
            $workflowName = array_column($data, 'label', 'type');
        } else {
            //获取已经对接的审批流
            $validWorkflow = SysService::getInstance()->getWorkflowRelateType();
            $workflowName = array_column($validWorkflow, 'name','code');
        }

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['j' => ByWorkflowNoticeModel::class]);
        $builder->where('1=1');

        if (isset($relateTypeId) && $relateTypeId) {
            $builder->andWhere('relate_type = :type:', ['type' => $relateTypeId]);
        }

        if (isset($exceptionType) && $exceptionType) {
            $builder->andWhere('exception_type = :exception_type:', ['exception_type' => $exceptionType]);
        }

        $builder->columns('count(1) as cou');
        $count = $builder->getQuery()->getSingleResult();
        $count = $count['cou'] ?? 0;

        $builder->columns('id
            ,relate_type
            ,exception_type
            ,exception_time
            ,resign_staff_id
            ,created_at
            ,updated_at');
        $builder->orderBy('id desc');
        $builder->limit($pageSize, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        foreach ($list as $k => $v) {
            $list[$k]['relate_type_text'] = $workflowName[$v['relate_type']];
            $list[$k]['exception_type_text'] = self::$t->_('exception_type_' . $v['exception_type']);
        }

        return [$list, $count];
    }
}