<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\backyard\WorkflowNodeBaseOvertimeConfigModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNodeBaseModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNodeBaseMultiTypeCcModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNodeBaseMultiTypeModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNodeCcBaseModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowNodeRelateBaseModel;
use App\Modules\WorkflowManagement\Models\WorkflowBaseConfigModel;
use App\Modules\WorkflowManagement\Models\WorkflowConfigModel;
use App\Modules\WorkflowManagement\Models\WorkflowPermissionModel;
use Phalcon\Mvc\Model;

//OA版本保存审批流配置
class ParseService extends ParseBaseService
{
    /**
     * @var ParseService
     */
    private static $instance;

    public $config;

    /**
     * 单一实例
     * @return ParseService
     */
    public static function getInstance(): ParseService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 保存审批流
     * @param $request
     * @param $uid
     * @return bool
     * @throws BusinessException
     * @throws ValidationException
     * @throws \Exception
     */
    public function doSaveWorkflowV2($request, $uid): bool
    {
        //获取参数
        if (empty($request)) {
            throw new BusinessException('缺少必须的参数', ErrCode::$VALIDATE_ERROR);
        }
        $this->logger->info('可视化审批流 - 编辑 - 请求参数:' . json_encode($request, JSON_UNESCAPED_UNICODE).' 操作用户==>'. $uid);

        //校验权限
        $permission = WorkflowPermissionModel::findFirst([
            'conditions' => 'relate_type = :relate_type: and staff_info_id = :staff_info_id: and is_edit= 1 and deleted = 0',
            'bind' => [
                'relate_type'   => $request['basicConfigure']['relateObjectId'],
                'staff_info_id' => $uid,
            ],
        ]);
        if (empty($permission)) {
            throw new ValidationException(self::$t->_('staff_permission_is_forbidden'));
        }

        //取得审批流配置数据
        $this->config = $config['basic'] = self::parseConfigDataV2($request['basicConfigure'], $request);

        //重构数据
        $config['workflow'] = self::parseWorkflowDataV2($request['workflowConfigure']);

        //保存审批流
        self::doSave($config, $uid);

        return true;
    }

    /**
     * 校验审批流异常条件
     * @param $workflow
     * @return array
     * @throws ValidationException
     */
    public function checkWorkflow($workflow, $id): array
    {
        if (empty($workflow)) {
            return [];
        }
        if (empty($workflow['flow_request'])) {
            return [];
        }
        $data = json_decode($workflow['flow_request'], true);
        if (empty($data)) {
            return [];
        }
        return self::checkWorkflowData($data['workflowConfigure']);
    }

    /**
     * 保存审批流
     * @param $params
     * @return int
     * @throws BusinessException
     */
    protected function saveWorkflow($params): int
    {
        $workflowName = $params['auditName'] ?? '';
        $relateType   = $params['relateObjectId'] ?? '';
        $request      = $params['request'] ?? '';
        $version      = $params['version'] ?? '';
        $desc         = $params['description'] ?? '';

        //获取当前类型的审批流
        $flow = ByWorkflowModel::findFirst([
            'conditions' => "relate_type = :type: and is_view = :is_view:",
            'bind'       => [
                'type'    => $relateType,
                'is_view' => ByWorkflowModel::IS_VIEW,
            ],
        ]);

        if (empty($flow)) {
            $wf = new ByWorkflowModel();
            $wf->setName($workflowName);
            $wf->setRelateType($relateType);
            $wf->setDescription($desc);
            $wf->setState(1);  //启用状态
            $wf->setFlowRequest($request);
            $wf->setVersion($version);
            $wf->save();
            $flowId = $wf->getId();
        } else {
            if ($flow instanceof ByWorkflowModel) {
                if ($flow->getRelateType() != $relateType) { //不能直接修改关联类型
                    throw new BusinessException("can't modify relate type", ErrCode::$SYSTEM_ERROR);
                }
                $flow->setName($workflowName);
                $flow->setFlowRequest($request);
                $flow->setVersion($version);
	            $flow->setUpdatedAt(gmdate('Y-m-d H:i:s'));
                $flow->update();
            }
        }
        return isset($flow) && $flow->getId() ? $flow->getId() : ($flowId ?? 0);
    }

    /**
     * 保存节点
     * @param int $flowId
     * @param array $params
     * @param string $version
     * @return array
     * @throws BusinessException
     */
    protected function saveNodes(int $flowId, array $params, string $version): array
    {
        //获取节点数据
        $nodes = $params;
        $map   = [];
        if (empty($nodes)) { //节点不能为空
            throw new BusinessException("Node could not be empty!", ErrCode::$SYSTEM_ERROR);
        }

        if (!is_array($nodes)) {
            return $map;
        }

        foreach ($nodes as $node) {
            $nodeName = $node['type'] == Enums::BACKEND_NODE_T_FINAL ? '结束节点': $node['name'];

            //保存节点
            $nodeModel = new ByWorkflowNodeBaseModel();
            $nodeModel->setFlowId($flowId);
            $nodeModel->setName($nodeName);
            $nodeModel->setType($node['type']);
            $nodeModel->setVersion($version);
            $nodeModel->setNodeMark($node['node_mark']);

            if (isset($node['node']) && $node['node'] && count($node['node']) > 1) { //一个审批节点存在多个逻辑
                $nodeModel->setAuditorType(Enums::NODE_AUDITOR_TYPE_COMPLEX);
            } else {
                $nodeInfo = current($node['node']);
                $nodeModel->setAuditorType($nodeInfo['auditor_type']);
                if (isset($nodeInfo['auditor_level'])) {
                    $nodeModel->setAuditorLevel($nodeInfo['auditor_level']);
                }

                if (isset($nodeInfo['auditor_id']) && $nodeInfo['auditor_id']) {
                    $nodeModel->setAuditorId($nodeInfo['auditor_id']);
                }
            }
            if (in_array($node['type'], [Enums::BACKEND_NODE_T_OR_SIGN, Enums::BACKEND_NODE_T_COUNTERSIGN])) {
                $nodeModel->setApprovalPolicy($node['approval_policy']);
            }
            $nodeModel->setSpecifyApprover($node['specify_approver'] ?? '');
            $nodeModel->setIsTermination($node['is_termination'] ?? 0);
            $nodeModel->setTerminationStaffId($node['termination_staff_id'] ?? '');
            $nodeModel->setCanEditField($node['can_edit_field'] ?? '');
            $nodeModel->setDeleted(0);
            $nodeModel->setCreatedAt(date("Y-m-d H:s:i", time()));
            $nodeModel->setUpdatedAt(date("Y-m-d H:s:i", time()));
            if (isset($node['node']) && $node['node'] && count($node['node']) > 1) { //一个审批节点存在多个逻辑
                $nodeModel->setAuditorType(Enums::NODE_AUDITOR_TYPE_COMPLEX);
            }
            $nodeModel->save();

            //节点id与节点key的映射
            $map[$node['id']] = $nodeModel->getId();

            //保存节点下多个逻辑
            if (isset($node['node']) && $node['node'] && count($node['node']) > 1) { //一个审批节点存在多个逻辑
                foreach ($node['node'] as $v) {
                    $nodeMultiTypeModel = new ByWorkflowNodeBaseMultiTypeModel();
                    $nodeMultiTypeModel->setNodeId($nodeModel->getId());
                    $nodeMultiTypeModel->setAuditorType($v['auditor_type']);

                    if (isset($v['auditor_level']) && $v['auditor_level']) {
                        $nodeMultiTypeModel->setAuditorLevel($v['auditor_level']);
                    }

                    if (isset($v['auditor_id']) && $v['auditor_id']) {
                        $nodeMultiTypeModel->setAuditorId(is_array($v['auditor_id'])
                            ? implode(',', $v['auditor_id'])
                            : $v['auditor_id']);
                    }
                    $nodeMultiTypeModel->save();
                }
            }

            //保存抄送节点相关
            if(isset($node['node_cc']) && $node['node_cc']) {
                //保存CC节点
                $nodeCcModel = new ByWorkflowNodeCcBaseModel();
                $nodeCcModel->setFlowId($flowId);
                $nodeCcModel->setCodeBaseId($map[$node['id']]);
                $nodeCcModel->setVersion($version);

                $nodeCcModel->setType(isset($node['passType']) && $node['passType'] ? $nodeCcModel->type_2 : $nodeCcModel->type_1 ); //如果type 存在 并且 为true  则是 审批通过后发送 否则是触发节点是发送

                if (isset($node['node_cc']) && $node['node_cc'] && count($node['node_cc']) > 1) { //一个审批节点存在多个逻辑
                    $nodeCcModel->setAuditorType(Enums::NODE_AUDITOR_TYPE_COMPLEX);
                } else {
                    $nodeCcInfo = current($node['node_cc']);
                    $nodeCcModel->setAuditorType($nodeCcInfo['auditor_type']);

                    if (isset($nodeCcInfo['auditor_level']) && $nodeCcInfo['auditor_level']) {
                        $nodeCcModel->setAuditorLevel($nodeCcInfo['auditor_level']);
                    }

                    if (isset($nodeCcInfo['auditor_id']) && $nodeCcInfo['auditor_id']) {
                        $nodeCcModel->setAuditorId($nodeCcInfo['auditor_id']);
                    }
                }
                $nodeCcModel->setApprovalPolicy($node['approval_policy']);
                if( count($node['node_cc']) > 1){
                    $nodeCcModel->setAuditorType(Enums::NODE_AUDITOR_TYPE_COMPLEX);
                }
                $nodeCcModel->save();

                if( count($node['node_cc']) > 1){
                    foreach ($node['node_cc'] as $v) {
                        $nodeMultiTypeCcModel = new ByWorkflowNodeBaseMultiTypeCcModel();
                        $nodeMultiTypeCcModel->setNodeId($nodeCcModel->getId());
                        $nodeMultiTypeCcModel->setAuditorType($v['auditor_type']);

                        if (isset($v['auditor_level']) && $v['auditor_level']) {
                            $nodeMultiTypeCcModel->setAuditorLevel($v['auditor_level']);
                        }

                        if (isset($v['auditor_id']) && $v['auditor_id']) {
                            $nodeMultiTypeCcModel->setAuditorId(is_array($v['auditor_id'])
                                ? implode(',', $v['auditor_id'])
                                : $v['auditor_id']);
                        }
                        $nodeMultiTypeCcModel->save();
                    }
                }
            }

            if (isset($node['overtime']) && $node['overtime']) {
                $overtimeCnf = $node['overtime'];
                $nodeOtCnfModel = new WorkflowNodeBaseOvertimeConfigModel();
                $nodeOtCnfModel->setFlowId($flowId);
                $nodeOtCnfModel->setNodeId($map[$node['id']]);
                $nodeOtCnfModel->setVersion($version);

                if ($overtimeCnf['is_switch_open'] == Enums\WorkflowManageEnums::WF_OVERTIME_SWITCH_OPEN) {

                    if (isset($overtimeCnf['overtime_sub_type'])) {
                        $nodeOtCnfModel->setOvertimeSubType($overtimeCnf['overtime_sub_type']);
                    }

                    if (isset($overtimeCnf['overtime_column'])) {
                        $nodeOtCnfModel->setOvertimeColumn($overtimeCnf['overtime_column']);
                    }

                    if (isset($overtimeCnf['overtime_days'])) {
                        $nodeOtCnfModel->setOvertimeDays($overtimeCnf['overtime_days']);
                    }

                    if (isset($overtimeCnf['overtime_policy'])) {
                        $nodeOtCnfModel->setOvertimePolicy($overtimeCnf['overtime_policy']);
                    }

                    if (isset($overtimeCnf['handover_policy'])) {
                        $nodeOtCnfModel->setHandoverPolicy($overtimeCnf['handover_policy']);
                    }

                    if (isset($overtimeCnf['handover_config'])) {
                        $nodeOtCnfModel->setHandoverConfig($overtimeCnf['handover_config']);
                    }

                    if (isset($overtimeCnf['handover_audit_days'])) {
                        $nodeOtCnfModel->setHandoverAuditDays($overtimeCnf['handover_audit_days']);
                    }

                    if (isset($overtimeCnf['handover_overtime_policy'])) {
                        $nodeOtCnfModel->setHandoverOvertimePolicy($overtimeCnf['handover_overtime_policy']);
                    }

                    if (isset($overtimeCnf['is_handover_termination'])) {
                        $nodeOtCnfModel->setIsHandoverTermination($overtimeCnf['is_handover_termination']);
                    }

                    if (isset($overtimeCnf['handover_termination_staff_ids'])) {
                        $nodeOtCnfModel->setHandoverTerminationStaffIds($overtimeCnf['handover_termination_staff_ids']);
                    }
                }
                $nodeOtCnfModel->setIsSwitchOpen($overtimeCnf['is_switch_open']);
                $nodeOtCnfModel->save();
            }
        }
        return $map;
    }

    /**
     * 保存节点关联关系
     * @param $flowId
     * @param array $nodeRelations
     * @param array $map
     * @param $version
     * @throws BusinessException
     */
    protected function saveNodeRelations($flowId, array $nodeRelations, array $map, $version)
    {
        //获取节点数据
        if (empty($nodeRelations)) { //节点不能为空
            throw new BusinessException("Node relations could not be empty!", ErrCode::$SYSTEM_ERROR);
        }
        $nodeRelationHasSave = [];
        $nodeUniqueKey = array_keys($map);
        $unProcessFromNodeList = [];
        $unProcessToNodeList = [];

        foreach ($nodeRelations as $v) {

            //如果下一个节点是条件节点
            if (!in_array($v['to_node_id'], $nodeUniqueKey)) {
                $unProcessFromNodeList[] = $v;
                continue;
            }

            //如果前一个节点是条件节点
            if (!in_array($v['from_node_id'], $nodeUniqueKey)) {
                $unProcessToNodeList[] = $v;
                continue;
            }

            //保存节点关系
            $code = implode(',', array_values($v['map']));
            $uniqueKey = md5($v['from_node_id'] . $v['to_node_id'] . $v['formula'] . $code . $v['sort']);
            if (in_array($uniqueKey, $nodeRelationHasSave)) {
                continue;
            }

            $insertData = [
                'flow_id'      => $flowId,
                'version'      => $version,
                'from_node_id' => $map[$v['from_node_id']],
                'to_node_id'   => $map[$v['to_node_id']],
                'formula'      => $v['formula'],
                'map'          => $code,
                'sort'         => $v['sort'],
            ];

            $this->doSaveNodeRelations($insertData);

            $nodeRelationHasSave[] = $uniqueKey;
        }

        //补齐下一个节点是多个的情况
        if (!empty($unProcessFromNodeList) && !empty($unProcessToNodeList)) {
            //去重
            $unProcessFromNodeList = array_values(array_column($unProcessFromNodeList, null, 'unique_key'));
            $unProcessToNodeList   = array_values(array_column($unProcessToNodeList, null, 'unique_key'));
            foreach ($unProcessFromNodeList as $itemFrom) {
                foreach ($unProcessToNodeList as $itemTo) {
                    //下一个节点为条件的，与上一个节点为条件的对接为同一个节点的情况，才能保存
                    //符合条件情况
                    // A From_node_id: 1111, To_node_id: 1112(条件节点ID)
                    // B From_node_id: 1112(条件节点ID), To_node_id: 1113(非条件节点ID)
                    //
                    //不符合条件情况
                    // A From_node_id: 1111, To_node_id: 1112(条件节点ID)
                    // B From_node_id: 1113(条件节点ID), To_node_id: 1114(非条件节点ID)
                    if ($itemFrom['to_node_id'] != $itemTo['from_node_id']) {
                        continue;
                    }

                    $insertData = [
                        'flow_id'      => $flowId,
                        'version'      => $version,
                        'from_node_id' => $map[$itemFrom['from_node_id']],
                        'to_node_id'   => $map[$itemTo['to_node_id']],
                        'formula'      => $itemTo['formula'],
                        'map'          => implode(',', array_values($itemTo['map'])),
                        'sort'         => $itemTo['sort'],
                    ];
                    $this->doSaveNodeRelations($insertData);
                }
            }
        } else if (!empty($unProcessFromNodeList) || !empty($unProcessToNodeList)) {
            $this->logger->warning("please check data!!! , flow id =" . $flowId);
        }
    }

    /**
     * @description 保存节点关系
     * @param array $insertData
     * @return void
     */
    private function doSaveNodeRelations(array $insertData = [])
    {
        //节点流转出现闭环
        if ($insertData['from_node_id'] === $insertData['to_node_id']) {
            $this->logger->warning("节点流转出现闭环!" . json_encode($insertData));
            throw new BusinessException(self::$t->_('retry_later'));
        }

        $model = new ByWorkflowNodeRelateBaseModel();
        $model->setFlowId($insertData['flow_id']);
        $model->setFromNodeId($insertData['from_node_id']);
        $model->setToNodeId($insertData['to_node_id']);
        $model->setVersion($insertData['version']);

        if (isset($insertData['formula']) && $insertData['formula']) {
            $model->setValuateFormula($insertData['formula']);
        }

        if (isset($insertData['map']) && $insertData['map']) {
            $model->setValuateCode($insertData['map']);
            $model->setRemark('');
        }
        $model->setSort($insertData['sort']);
        $model->save();
    }

    /**
     * 保存审批流配置
     * @param array $params
     * @throws \Exception
     */
    protected function saveConfigure(array $params)
    {
        //基础配置
        $auditType = $params['relateObjectId'];
        $version = $params['version'];
        $config = $params['config'];

        //获取全部配置
        $basicConfig = WorkflowConfigModel::find([
            'conditions' => 'id >= 15 and deleted = 0',
        ])->toArray();
        $basicConfigArr = array_column($basicConfig, 'name', 'id');

        //获取审批流id
        $flowBase = ByWorkflowModel::findFirst([
            'conditions' => "type = 1 and relate_type = :type: and is_view = :is_view:",
            'bind' => [
                'type'    => $auditType,
                'is_view' => ByWorkflowModel::IS_VIEW,
            ]
        ]);
        if (empty($flowBase)) {
            throw new \Exception('no valid workflow');
        }

        if (isset($config) && $config && is_array($config)) {

            foreach ($config as $v) {
                $model = new WorkflowBaseConfigModel();
                $model->setVersion($version);
                $model->setName($basicConfigArr[$v['type']]);
                $model->setWorkflowRelateTypeId($flowBase->id);
                $model->setConfigureType($v['type']);
                $model->setConfigureValue($v['value']);
                if (isset($v['extend']) && $v['extend']) {
                    $model->setExtendParams($v['extend']);
                }
                $model->save();
            }
        }
    }
}