<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\Enums;
use App\Library\Enums\WorkflowNodeEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\WorkflowManagement\Models\WorkflowOperateLogsModel;
use Exception;

abstract class ParseBaseService extends BaseService
{
    const DEFAULT_SORT = 10;
    const MAX_LENGTH = 16;
    const PLACEHOLDER = 'x';

    const NEED_PASS = 1; //跳过
    const NOT_NEED_PASS = 0; //无需跳过
    const NODE_AUDITOR_TYPE_DEFAULT = 0; //审批人类型无（一般给开始结束节点使用）
    const APPROVAL_POLICY_HANDOVER_SPEC_STAFF = 2; //审批异常处理策略，转交指定员工

    public $config; //全局配置

    /**
     * 保存审批流配置
     * @param array $params
     * @return mixed
     */
    abstract protected function saveConfigure(array $params);

    /**
     * 保存节点
     * @param int $flowId 审批流ID
     * @param array $params 审批流参数
     * @param string $version 版本号
     * @return mixed
     */
    abstract protected function saveNodes(int $flowId, array $params, string $version);

    /**
     * 保存节点关系
     * @param int $flowId 审批流ID
     * @param array $params 审批流参数
     * @param array $map 节点映射关系
     * @return mixed
     */
    abstract protected function saveNodeRelations(int $flowId, array $params, array $map, $version);

    /**
     * 保存节点关系
     * @param array $params
     * @return mixed
     */
    abstract protected function saveWorkflow(array $params);

    /**
     * 获取数据
     * @param $configure
     * @return array
     * @throws ValidationException
     */
    public function parseWorkflowDataV2($configure): array
    {
        //1. 解构前端传入数据 & 校验结构
        $structure = $this->decodeV2($configure, [], $configure['childNode']);

        //2. 根据获取到的新结构，解析出节点、节点关系数据
        [$nodeList, $nodeRelateList] = $this->parse($structure);

        //3. 解析节点、节点关系
        foreach ($nodeList as $k => $node) {
            $nodeList[$k] = $this->parseNodePropertyV2($node);
        }

        //4. 解析节点关系中的条件
        foreach ($nodeRelateList as $k => $nodeRelate) {
            $nodeRelateList[$k] = $this->parseNodeRelateProperty($nodeRelate);
        }

        //5. 合并条件
        $list = [];
        $nodeRelateList = $this->mergeNodeRelate(
            array_keys($nodeList),
            $nodeRelateList,
            array_search(0, array_column($nodeList, 'type','id')),
            null,
            [],
            0,
            $list
        );

        //6. 重新计算优先级
        $nodeRelateList = $this->reCalcPriority($nodeRelateList, $list);

        return [
            'node'   => $nodeList,
            'relate' => $nodeRelateList
        ];
    }

    /**
     * 解析审批条件返回失效的审批条件
     * @throws ValidationException
     */
    public function checkWorkflowData($configure): array
    {
        //1. 解构前端传入数据 & 校验结构
        $structure = $this->decodeV2($configure, [], $configure['childNode']);

        //2. 根据获取到的新结构，解析出分支条件数据
        $conditionsList = $this->extractConditions($structure);

        //3. 解析分支条件
        $invalidList = [];
        foreach ($conditionsList as $conditions) {
            $invalidInfo = ParseNodeRelationService::getInstance()->checkBranchConditions($conditions ?? []);
            if (!empty($invalidInfo)) {
                $invalidList = array_merge($invalidList, $invalidInfo);
            }
        }
        return $invalidList;
    }

    /**
     * 合并节点关系
     * @param array $node_list 节点列表
     * @param array $node_relate_list 节点关系列表
     * @param string $start_node_id 开始节点
     * @param array|null $last_relate 上一个节点关系
     * @param array $result 最终结果
     * @param int $max_length
     * @param $list
     * @return array
     */
    public function mergeNodeRelate(array $node_list,
                                    array $node_relate_list,
                                    string $start_node_id,
                                    array $last_relate = null,
                                    array $result = [],
                                    int $level, &$list): array
    {
        //根据$start_node_id 去查下一个节点
        $relateMap = array_column($node_relate_list, 'from_node_id', 'unique_key');
        if (empty($relateMap)) {
            return [];
        }

        //有下一个节点
        $uniqueKey = array_search($start_node_id, $relateMap);

        if (empty($uniqueKey)) {
            return $result;
        }

        //找出全部的下一级节点来
        $relateNextList = array_map(function ($v) use ($start_node_id) {
            return $v['from_node_id'] == $start_node_id ? $v['to_node_id'] : '';
        }, $node_relate_list);
        $relateNextList = array_filter($relateNextList);

        $currentLevel = $level;

        //下一个节点是条件分支
        if (is_array($relateNextList) && count($relateNextList) > 1) {

            if (count($relateNextList) >= self::MAX_LENGTH) {
                throw new ValidationException('超出最大范围');
            }

            foreach ($relateNextList as $key => $toNodeId) {

                if (!empty($last_relate)) { //存在条件
                    $currentRelate = $node_relate_list[$key];

                    //替换本级条件的公式 + 变量映射
                    $currentRelateValueCount = count($last_relate['map']) + count($currentRelate['map']);
                    $currentMap = [];
                    $currentFormula = $currentRelate['formula'];
                    uksort($currentRelate['map'], function($a, $b) {
                        // 提取 $p 后的数字（例如 $p17 → 17）
                        $numA = intval(substr($a, 2)); // 从第3字符开始（跳过 "$p"）
                        $numB = intval(substr($b, 2));
                        return $numB - $numA; // 降序
                    });

                    foreach ($currentRelate['map'] as $k => $v) {
                        $newKey = 'p' . $currentRelateValueCount;
                        $reg = sprintf('/((?<=\$)(%s)*?(?=[,|\s|\)]))+/', substr($k, 1));
                        $currentFormula = preg_replace($reg, $newKey, $currentFormula);
                        $currentMap["$" . $newKey] = $v;
                        $currentRelateValueCount--;
                    }

                    //合并条件后的新的排序，序号为整形，数越大代表的优先级越高
                    //限制：横向最大条件数10个
                    //计算规则，

                    $newSort = $this->itohex($last_relate['sort']) . $this->itohex($currentRelate['sort']);
                    $newSort = str_pad($newSort, self::MAX_LENGTH, self::PLACEHOLDER, STR_PAD_RIGHT);
                    $list[] = $newSort;

                    //上一级条件与本级条件的合并
                    if (isset($last_relate['formula']) && $last_relate['formula'] && isset($currentFormula) && $currentFormula) {
                        $newFormula = sprintf("(%s) && (%s)", $last_relate['formula'], $currentFormula);
                    } else {
                        if (isset($last_relate['formula']) && $last_relate['formula']) {
                            $newFormula = $last_relate['formula'];
                        } else if (isset($currentFormula) && $currentFormula) {
                            $newFormula = $currentFormula;
                        } else {
                            $newFormula = "";
                        }
                    }

                    //对于新的map排序，由于KEY的值是 $p + 数字的组合
                    //所以新的排序使用 ksort(xxx, SORT_NATURAL)
                    $newMap = array_merge($currentMap, $last_relate['map']);
                    ksort($newMap, SORT_NATURAL);

                    $newRelate = [
                        'formula' => $newFormula,
                        'map' => $newMap,
                        'sort' => $newSort,
                        'from_node_id' => $last_relate['from_node_id'],
                        'to_node_id' => $currentRelate['to_node_id'],
                        'unique_key' => sprintf("%s-%s", $last_relate['from_node_id'], $currentRelate['to_node_id']),
                    ];
                    $result = $this->mergeNodeRelate($node_list, $node_relate_list, $toNodeId, $newRelate, $result, $currentLevel + 1, $list);
                } else {

                    $newSort = $this->itohex($node_relate_list[$key]['sort']);
                    $newSort = str_pad($newSort, self::MAX_LENGTH, self::PLACEHOLDER, STR_PAD_RIGHT);
                    $list[] = $newSort;

                    if (is_numeric($newSort) && $newSort == 0) {
                        $node_relate_list[$key]['sort'] = 10;
                    } else {
                        $node_relate_list[$key]['sort'] = $newSort;
                    }

                    $result = $this->mergeNodeRelate($node_list, $node_relate_list, $toNodeId, $node_relate_list[$key], $result, $currentLevel + 1, $list);
                }
            }
        } else { //下一个节点只有一条线的情况
            $nextRelateNode = null;
            if (!empty($last_relate)) {
                //通过可视化配置条件一定会至少出现2条线（多条线走外层if），这里只能是条件后面接审批节点
                $last_relate['to_node_id'] = $node_relate_list[$uniqueKey]['to_node_id'];
                $result[] = $last_relate;
            } else if (!in_array($node_relate_list[$uniqueKey]['to_node_id'], $node_list)) {
                //下一个节点不在审批节点里面，那么需要继续查找下一级节点
                $nextRelateNode = $node_relate_list[$uniqueKey];
            } else {
                $result[] = $node_relate_list[$uniqueKey];
            }
            $result = $this->mergeNodeRelate($node_list, $node_relate_list, $node_relate_list[$uniqueKey]['to_node_id'], $nextRelateNode, $result, $currentLevel + 1, $list);
        }

        return $result;
    }

    /**
     *
     * @param $value
     * @return string
     */
    public function itohex($value)
    {
        if (is_int($value)) {
            return dechex($value / 10);
        } else {
            return trim($value, self::PLACEHOLDER);
        }
    }

    /**
     * 重新计算优先级
     * @param $nodeRelateList
     * @param $list
     * @return mixed
     */
    public function reCalcPriority($nodeRelateList, $list)
    {
        foreach ($list as $k => $v) {
            $list[$k] = str_replace(self::PLACEHOLDER, '0', $v);
        }
        sort($list);
        $list = array_flip(array_values($list));

        foreach ($nodeRelateList as &$v) {

            $sort = str_replace(self::PLACEHOLDER, '0', $v['sort']);
            if (isset($v['sort']) && isset($list[$sort])) {
                $v['sort'] = ($list[$sort] + 1) * 100;
            }
        }
        return $nodeRelateList;
    }

    /**
     * 获取审批数据
     * @param array $params
     * @param array $configure
     * @return array
     * @throws ValidationException
     */
    protected function parseConfigData(array $params, array $configure): array
    {
        //校验传入参数
        Validation::validate($params, [
            'relateObjectId' => 'Required|Int|>>>: missing parameter `relateObjectId`',
            'auditName' => 'Required|Str|>>>: missing parameter `auditName`',
            'approvalRepeat' => 'Required|Int|>>>: missing parameter `approvalRepeat`',
            'approvalEqualApply' => 'Required|Int|>>>: missing parameter `approvalEqualApply`',
        ]);

        $result['audit_name'] = $params['auditName'] ?? '';
        $result['relate_object_id'] = $params['relateObjectId'] ?? 0;
        $result['request'] = json_encode($configure, JSON_UNESCAPED_UNICODE);

        //审批人重复
        if (isset($params['approvalEqualApply'])) {
            $config[] = [
                'type' => 15,
                'value' => $params['approvalEqualApply'] ?? 1
            ];
        }

        //审批人重复
        if (isset($params['approvalRepeat'])) {
            $config[] = [
                'type' => 16,
                'value' => $params['approvalRepeat'] ?? 1
            ];
        }
        $result['config'] = $config ?? [];

        return $result;
    }

    /**
     * 获取审批数据
     * @param array $params
     * @param array $configure
     * @return array
     * @throws ValidationException
     */
    public function parseConfigDataV2(array $params, array $configure): array
    {
        //校验传入参数
        ValidateService::getInstance()->validateBasicConfig($params);

        $result['auditName']      = $params['auditName'] ?? '';
        $result['relateObjectId'] = $params['relateObjectId'] ?? 0;
        $result['request']        = json_encode($configure, JSON_UNESCAPED_UNICODE);
        $result['overtimeType']   = $params['overtimeType'] ?? Enums\WorkflowManageEnums::WF_OVERTIME_TYPE_NONE;

        //审批人重复
        if (isset($params['approvalEqualApply'])) {
            $config[] = [
                'type' => Enums\WorkflowManageEnums::WF_OA_APPROVAL_VALUE,
                'value' => !empty($params['approvalEqualApply']) ? $params['approvalEqualApply'] : 1
            ];
        }

        //审批人重复
        if (isset($params['approvalRepeat'])) {
            $config[] = [
                'type' => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_PASS,
                'value' => !empty($params['approvalRepeat']) ? $params['approvalRepeat'] : 1
            ];
        }

        //超时审批类型
        if (isset($params['overtimeType'])) {
            $config[] = [
                'type'  => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_TYPE,
                'value' => !empty($params['overtimeType']) ? $params['overtimeType'] : Enums\WorkflowManageEnums::WF_OVERTIME_TYPE_NONE,
            ];
        } else {
            $config[] = [
                'type'  => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_TYPE,
                'value' => Enums\WorkflowManageEnums::WF_OVERTIME_TYPE_NONE,
            ];
        }

        //超时审批时长
        if (isset($params['overtimeDays'])) {
            $config[] = [
                'type'  => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_DAYS,
                'value' => !empty($params['overtimeDays']) ? $params['overtimeDays'] : 0,
            ];
        } else {
            $config[] = [
                'type'  => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_DAYS,
                'value' => 0,
            ];
        }

        //超时审批后操作
        if (isset($params['overtimePolicy'])) {
            $config[] = [
                'type' => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_POLICY,
                'value' => !empty($params['overtimePolicy']) ? $params['overtimePolicy'] : Enums\WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED
            ];
        } else {
            $config[] = [
                'type'  => Enums\WorkflowManageEnums::WF_OA_AUTO_CNF_OT_POLICY,
                'value' => Enums\WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
            ];
        }

        //超时审批后操作
        if (isset($params['overtimeColumn']) && ConfigService::getInstance()->isExistOvertimeForm($result['relateObjectId'])) {
            $config[] = [
                'type'   => Enums\WorkflowManageEnums::WF_OA_OT_FORM,
                'value'  => 0,
                'extend' => !empty($params['overtimeColumn']) ? $params['overtimeColumn'] : 0,
            ];
        } else {
            $config[] = [
                'type'   => Enums\WorkflowManageEnums::WF_OA_OT_FORM,
                'value'  => 0,
                'extend' => 0,
            ];
        }
        $result['config'] = $config ?? [];

        return $result;
    }

    /**
     * 解构审批流数据
     * @doc https://l8bx01gcjr.feishu.cn/docs/doccnlbyZfuWF2JbEzsARD4bslc
     *
     * @description  由于结构复杂，先将结构进行拆解优化,将结构优化为一个树形结构
     * @param $params
     * @param array $transplant
     * @param array $finalNode
     * @return array
     */
    public function decodeV2($params, array $transplant = [], array $finalNode = []): array
    {
        //获取节点
        $node = [
            'id'        => $params['id'],
            'name'      => $params['name'] ?? '',
            'nodeType'  => $params['nodeType'],
            'form'      => $params['form'] ?? [],
            'priority'  => $params['priority'] ?? '',
        ];

        if (!empty($params['branchNode']) && is_array($params['branchNode']) &&
            !empty($params['childNode']) && is_array($params['childNode'])) { //同时存在branchNode & childNode

            //优先查找childNode
            if (isset($transplant)) {
                $childTree = $this->decodeV2($params['childNode'], $transplant, $finalNode);
            } else {
                $childTree = $this->decodeV2($params['childNode'], [], $finalNode);
            }

            //再找branchNode
            $branchTree = [];
            foreach ($params['branchNode'] as $item) {
                $branchTree[] = $this->decodeV2($item, $childTree, $finalNode);
            }
            $node['tree'] = $branchTree;

            if ($node['nodeType'] == Enums::NODE_TYPE_BRANCH) {
                return $branchTree;
            } else {
                return $node;
            }
            //return $node;
        } else if (!empty($params['branchNode']) || !empty($params['childNode'])) { //只存在childNode | branchNode

            $childTree  = [];
            $branchTree = [];
            if (isset($params['childNode'])) {
                $childTree = $this->decodeV2($params['childNode'], $transplant, $finalNode);
                $node['tree'] = $childTree;
            }

            if (isset($params['branchNode'])) {
                foreach ($params['branchNode'] as $item) {
                    $branchTree[] = $this->decodeV2($item, $transplant, $finalNode);
                }
                $node['tree'] = $branchTree;
            }

            if ($node['nodeType'] == Enums::NODE_TYPE_BRANCH) {
                return isset($params['childNode']) ? $childTree: $branchTree;
            } else {
                return $node;
            }
        } else {

            if (isset($transplant) && $transplant && (
                isset($transplant['nodeType']) && in_array($transplant['nodeType'], [Enums::NODE_TYPE_BRANCH,Enums::NODE_TYPE_COND,Enums::NODE_TYPE_FINAL]) ||
                !isset($transplant['nodeType']) && is_array($transplant)
                )) { //最后补充上结束节点

                //去掉其中的nodeType == 2的节点，干扰后续计算优先级
                $params['tree'] = $transplant;
            } else if (isset($transplant) && $transplant && in_array($transplant['nodeType'], [Enums::NODE_TYPE_AUDIT,Enums::NODE_TYPE_CC])) { //如果
                $params['tree'] = $this->decodeV2($transplant, [], $finalNode);
            }
            return $params;
        }
    }


    /**
     * 解析审批节点
     * @param $params
     * @param array $nodeList
     * @param string $lastId
     * @return array
     */
    public function parse($params, array $nodeList = [], string $lastId = ''): array
    {
        if (isset($params['id']) && $params['id']) { //除了条件分支，包括开始节点、审批节点、抄送节点、结束节点
            //节点节点关系
            if (in_array($params['nodeType'], [Enums::NODE_TYPE_INIT, Enums::NODE_TYPE_AUDIT, Enums::NODE_TYPE_CC, Enums::NODE_TYPE_FINAL])) {
                $nodeList[$params['id']] = [
                    'id'        => $params['id'],
                    'name'      => $params['name'] ?? '',
                    'nodeType'  => $params['nodeType'],
                    'form'      => $params['form'] ?? [],
                ];
            }

            if (!empty($lastId) && $params['nodeType'] !== Enums::NODE_TYPE_COND) {
                $relateList[$lastId . '-' . $params['id']] = [
                    'from' => $lastId,
                    'to'   => $params['id'],
                    'sort' => self::DEFAULT_SORT,
                    'form' => []
                ];
            }

            if (isset($params['tree'])) {

                [$node, $relate] = $this->parse($params['tree'], $nodeList, $params['id']);
                $nodeList = array_merge($nodeList, $node);
                $nodeList = array_column($nodeList, null, 'id');
                $relateList = array_merge($relateList ?? [], $relate);
            }
        } else { //条件分支
            $total = count($params);

            foreach ($params as $key => $item) {

                $uniqueKey = $lastId . '-' . $item['id'];

                $relateList[$uniqueKey] = [
                    'from' => $lastId,
                    'to'  => $item['id'],
                    'sort' => ($total - $key) * self::DEFAULT_SORT,
                    'form' => $item['form'] ?? []
                ];
                [$node, $relate] = $this->parse($item, $nodeList, $lastId);
                $nodeList = array_merge($nodeList, $node);
                $relateList = array_merge($relateList ?? [], $relate);
            }
        }
        return [$nodeList, $relateList ?? []];
    }

    /**
     * 从结构中解析出分支条件
     * @param $params
     * @return array
     */
    public function extractConditions($params): array
    {
        if (isset($params['id']) && $params['id']) {
            if (isset($params['tree'])) {
                $relate = $this->extractConditions($params['tree']);
                $relateList = array_merge($relateList ?? [], $relate);
            }
        } else { //条件分支
            foreach ($params as $item) {
                if (!empty($item['form'])) {
                    $relateList[] = [
                        'form' => $item['form'],
                        'id'   => $item['id'],
                    ];
                }
                $relate = $this->extractConditions($item);
                $relateList = array_merge($relateList ?? [], $relate);
            }
        }
        return $relateList ?? [];
    }

    /**
     * 分析节点属性
     *
     * 节点字段定义
     * https://flashexpress.feishu.cn/docs/doccnlbyZfuWF2JbEzsARD4bslc
     *
     * @param $params
     * @return array
     * @throws ValidationException
     */
    public function parseNodePropertyV2($params): array
    {
        //[1]获取请求参数
        $uuid               = $params['id'] ?? '';
        $name               = $params['name'] ?? '';
        $nodeType           = $params['nodeType'] ?? '';
        $formGroups         = $params['form'] ?? [];
        $nodeApplyArr       = []; //节点类型数组
        $nodeCcLArr         = []; //抄送类型数组
        $nodeOvertimeConfig = []; //节点超时配置

        //[1.1]获取详细配置数据
        //抄送 false=同意、驳回都需要抄送  true=仅同意后需要抄送
        $ccPolicy            = $formGroups['ccPolicy'] ?? false;
        $noHandlerPolicy     = $formGroups['noHanderPolicy'] ?? Enums\WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_PASS;
        $noHandlerConfig     = $formGroups['noHanderConfig'] ?? 0;
        $approvalMode        = $formGroups['approvalMode'] ?? 0;
        $nodeMark            = $formGroups['nodeMark'] ?? WorkflowNodeEnums::NODE_MARK_APPROVAL_NODE;
        $canEditField        = $formGroups['canEditField'] ?? WorkflowNodeEnums::NODE_EDIT_FIELD_STATE_CAN_NOT;
        $fieldIdList         = $formGroups['fieldIdList'] ?? [];

        //是否配置跳过 0=没有配置跳过 1=已配置跳过
        $isTermination       = isset($formGroups['isTermination']) && $formGroups['isTermination'] ? self::NEED_PASS: self::NOT_NEED_PASS;
        $terminationStaffIds = $formGroups['terminationStaffIds'] ?? [];

        //[2]解析数据
        $node = [
            'id'       => $uuid,
            'name'     => $name,
            'passType' => $ccPolicy,
        ];
        if (isset($nodeType) && $nodeType !== Enums::NODE_TYPE_FINAL && (empty($uuid) || !isset($params['name']) || !isset($params['form']))) {
            throw new ValidationException('节点配置错误,缺少必要参数' . $uuid ?? "");
        }

        //解析节点组
        //节点类型 0-开始节点 1-或签审批节点 2-会签审批节点 99-结束节点
        if ($nodeType == Enums::NODE_TYPE_INIT) { //起始节点
            $nodeApplyArr            = [['auditor_type' => self::NODE_AUDITOR_TYPE_DEFAULT]]; //兼容审批节点多节点类型
            $node['type']            = Enums::BACKEND_NODE_T_INIT;                            //节点类型
            $node['approval_policy'] = $noHandlerPolicy;                                      //审批人为空异常处理类型
            $node['node_mark']       = Enums::NODE_MARK_DEFAULT;
        } else {
            if ($nodeType == Enums::NODE_TYPE_AUDIT) {
                //审批节点
                $nodeApplyArr = $this->getNode($formGroups);
                $node['name'] = empty($name) ? '审批人' : $node['name'];

                //审批策略 1-自动通过 2-转交指定成员 3-自动转交上级 4-自动驳回
                $node['type']            = $approvalMode == Enums::FRONTEND_NODE_T_COUNTERSIGN ? Enums::BACKEND_NODE_T_COUNTERSIGN : Enums::BACKEND_NODE_T_OR_SIGN;
                $node['approval_policy'] = $noHandlerPolicy;
                if ($noHandlerPolicy == self::APPROVAL_POLICY_HANDOVER_SPEC_STAFF) {
                    $node['specify_approver'] = $noHandlerConfig['value'] ?? 0;
                }
                $node['is_termination']       = $isTermination;
                $node['termination_staff_id'] = $isTermination ? implode(',', array_column($terminationStaffIds, 'value')) : '';

                //节点是否可编辑 & 可编辑的字段
                $node['can_edit_field']       = FormService::getInstance()->generateEditField($canEditField, $fieldIdList);

                //在审批节点上追加抄送节点
                $nodeCcLArr = $this->getNode($formGroups, 'formCc');

                //审批节点默认标记审批
                $node['node_mark'] = !empty($nodeMark) ? $nodeMark : Enums::NODE_MARK_APPROVAL;

                //是否开启超时时效
                //是否开启超时时效 0=关闭, 1=开启
                $isOpenOvertime = $formGroups['isOpenOvertime'] ?? Enums\WorkflowManageEnums::WF_OVERTIME_SWITCH_CLOSED;

                //超时配置
                if ($this->config['overtimeType'] == Enums\WorkflowManageEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE) {
                    $nodeOvertimeConfig = [
                        'is_switch_open' => $isOpenOvertime,
                    ];
                    //开关开启
                    if ($isOpenOvertime == Enums\WorkflowManageEnums::WF_OVERTIME_SWITCH_OPEN) {
                        $nodeOvertimeConfig['overtime_sub_type'] = $overtimeSubType = $formGroups['overtimeSubType'];             //超时类型 1=设置表单字段为超时时间，2=最大审批自然日（天数）
                        $overtimeColumn                          = $formGroups['overtimeColumn'];                                 //超时字段（接口提供）
                        $overtimeDays                            = $formGroups['overtimeDays'];                                   //超时审批天数 1 ～ 31
                        $overtimePolicy                          = $formGroups['overtimePolicy'];                                 //超时后处理方式 1=自动通过 4=自动驳回 5=超时关闭 6=超时转交
                        $handoverPolicy                          = $formGroups['handoverPolicy'];                                 //转交逻辑 1=转交指定工号 2=转交指定2级上级
                        $handoverConfig                          = $formGroups['handoverConfig'];                                 //转交指定工号
                        $handoverAuditDays                       = $formGroups['handoverAuditDays'];                              //转交超时审批天数 1 ～ 31
                        $handoverOvertimePolicy                  = $formGroups['handoverOvertimePolicy'];                         //转交超时超时处理方式 1=自动通过 4=自动驳回 5=超时关闭
                        $isHandoverTermination                   = $formGroups['isHandoverTermination'];                          //是否开启转交跳过特殊审批人 true/false
                        $handoverTerminationStaffIds             = $formGroups['handoverTerminationStaffIds'];                    //转交跳过特殊审批人

                        if ($overtimeSubType == Enums\WorkflowManageEnums::WF_OVERTIME_SUB_TYPE_FORM_COLUMN) {
                            $nodeOvertimeConfig['overtime_column'] = $overtimeColumn;
                        }
                        $nodeOvertimeConfig['overtime_days']   = $overtimeDays;
                        $nodeOvertimeConfig['overtime_policy'] = $overtimePolicy;

                        //超时转交
                        if ($overtimePolicy == Enums\WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_HANDLE) {
                            $nodeOvertimeConfig['handover_policy']          = $handoverPolicy;
                            $nodeOvertimeConfig['handover_audit_days']      = $handoverAuditDays;
                            $nodeOvertimeConfig['handover_overtime_policy'] = $handoverOvertimePolicy;
                            $nodeOvertimeConfig['is_handover_termination']  = $isHandoverTermination;

                            if ($handoverPolicy == Enums\WorkflowManageEnums::WF_HANDLE_POLICY_SPEC_STAFF) {
                                $nodeOvertimeConfig['handover_config'] = !empty($handoverConfig) ? implode(',',
                                    array_column($handoverConfig, 'value')) : '';
                            }

                            if ($isHandoverTermination == Enums\WorkflowManageEnums::WF_OT_HANDLE_TERMINATION_OPEN) {
                                $nodeOvertimeConfig['handover_termination_staff_ids'] = !empty($handoverTerminationStaffIds) ? implode(',',
                                    array_column($handoverTerminationStaffIds, 'value')) : '';
                            }
                        }
                    }
                }
            } else {
                if ($nodeType == Enums::NODE_TYPE_CC) { //抄送节点
                    $nodeApplyArr      = $this->getNode($formGroups, 'formCc');
                    $node['name']      = empty($name) ? '抄送人' : $node['name'];
                    $node['type']      = Enums::BACKEND_NODE_T_CC;
                    $node['node_mark'] = Enums::NODE_MARK_DEFAULT;
                } else {
                    if ($nodeType == Enums::NODE_TYPE_FINAL) { //结束节点
                        $nodeApplyArr            = [['auditor_type' => self::NODE_AUDITOR_TYPE_DEFAULT]];
                        $node['type']            = Enums::BACKEND_NODE_T_FINAL;
                        $node['approval_policy'] = $noHandlerPolicy;
                        $node['node_mark']       = Enums::NODE_MARK_DEFAULT;
                    }
                }
            }
        }

        $node['node']      = $nodeApplyArr;
        $node['node_cc']   = $nodeCcLArr;
        $node['overtime']  = $nodeOvertimeConfig;
        return $node;
    }

    /**
     * @description 获取节点
     * @param $formGroups
     * @param string $targetColumn
     * @return array
     * @throws ValidationException
     */
    private function getNode($formGroups, string $targetColumn = 'formGroups'): array
    {
        $formData = $formGroups[$targetColumn];
        if (isset($formData) && $formData && is_array($formData)) {
            foreach ($formData as $group) {
                if (isset($group['tabType']) && $group['tabType'] == Enums::NODE_TYPE_SUBMITTER) {
                    $nodeArr[] = $this->parseNodeBySubmitter($group);
                } else {
                    $nodeArr[] = $this->parseNodeByForm($group);
                }
            }
        }
        return $nodeArr ?? [];
    }

    /**
     * 解析节点关系
     * @param $node
     * @return array
     * @throws ValidationException
     */
    public function parseNodeRelateProperty($node): array
    {
        $fromNodeId = $node['from'] ?? "";
        $toNodeId   = $node['to'] ?? "";
        $sort       = $node['sort'] ?? 10;
        $form       = $node['form'] ?? [];

        if (empty($fromNodeId) || empty($toNodeId)) {
            throw new ValidationException("System Internal Error, Invalid uuid", ErrCode::$SYSTEM_ERROR);
        }

        $base = [
            'sort'         => $sort,
            'from_node_id' => $fromNodeId,
            'to_node_id'   => $toNodeId,
            'unique_key'   => $fromNodeId . '-' . $toNodeId
        ];
        $conditions = ParseNodeRelationService::getInstance()->parseExV2($form);
        return array_merge($conditions, $base);
    }

    /**
     * 保存审批流
     * @param array $config
     * @param string $uid
     * @return void
     * @throws Exception
     */
    protected function doSave(array $config, string $uid): void
    {
        if (empty($config)) {
            throw new Exception('please check data');
        }

        //开启事务
        $db = $this->getDI()->get('db_backyard');
        try {
            $db->begin();

            $version = $this->getWorkflowVersionSerialNo();

            $basicConfig = array_merge($config['basic'], ['version' => $version]);
            
	        $this->logger->info('可视化审批流 - 创建审批流配置 - 参数:' . json_encode($config, JSON_UNESCAPED_UNICODE).' 操作用户==>'.$uid.' 版本号==>'.$version);
            //[1]创建审批流
            //创建审批流配置
            $flowId = $this->saveWorkflow($basicConfig);

            //[2]保存审批流操作日志
            $this->saveWorkflowOperateLog($config, $uid);

            //[3]创建审批节点
            $this->logger->info('可视化审批流 - Node - 参数:' . json_encode($config['workflow']['node'], JSON_UNESCAPED_UNICODE).' 操作用户==>'.$uid.' 版本号==>'.$version);
            $map = $this->saveNodes($flowId, $config['workflow']['node'], $version);
            
	        $this->logger->info('可视化审批流 - 审批节点 - 参数:' . json_encode($map, JSON_UNESCAPED_UNICODE).' 操作用户==>'.$uid.' 版本号==>'.$version);
	        
            //[4]创建审批节点关联关系
            $this->saveNodeRelations($flowId, $config['workflow']['relate'], $map, $version);

            //[5]保存配置
            $this->saveConfigure($basicConfig);

            $db->commit();
        } catch (Exception $e) {
            $db->rollback();
            $this->getDI()->get('logger')->warning('doSave----结果' . $e->getMessage() . $e->getTraceAsString());
        }
    }

    /**
     * 保存审批流操作日志
     * @param $config
     * @param $uid
     */
    public function saveWorkflowOperateLog($config, $uid)
    {
        //获取参数
        $request    = $config['basic']['request'] ?? "";
        $workflowId  = $config['basic']['relate_object_id'] ?? "";

        //save workflow operate logs
        $model = new WorkflowOperateLogsModel();
        $model->setWorkflowId($workflowId);
        $model->setOperator($uid);
        $model->setRequestBody($request);
        $model->setBefore('');
        $model->setAfter('');
        $model->save();
    }

    /**
     * 获取申请权限
     * @param $nodeList
     * @return array
     * @throws ValidationException
     */
    public function parseApplyPermission($nodeList)
    {
        $applyNode = current($nodeList);
        $form = $applyNode['form'] ?? [];

        if (!(isset($form['sponsorType']) && in_array($form['sponsorType'], [0, 1]))) {
            throw new ValidationException("节点缺少必要参数", ErrCode::$VALIDATE_ERROR);
        }

        if ($form['sponsorType'] == 0) { //全部

            $conditions = [
                'formula' => 1,
                'map' => []
            ];
        } else { //部分

            $conditions = ParseNodeRelationService::getInstance()->parse($form['formGroups']);
        }
        return $conditions;
    }

    /**
     * 解析节点
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function parseNodeBySubmitter($params): array
    {
        Validation::validate($params, [
            'approvalType' => 'Required|Int|>>>:' . "missing parameter `approvalType`",
            'val'          => 'Required|>>>:' . "missing parameter `val`",
        ]);

        $NodeParse = ParseNodeService::getInstance();
        switch ($params['approvalType']) {
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_STAFF: //指定员工
                $auditor = $NodeParse->parseSpecifyStaff($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_SUPERIOR: //指定上级
                $auditor = $NodeParse->parseSpecifySuperior($params['val']);
                break;
            case Enums::NODE_AUDITOR_TYPE_ORG_MANAGER: //部门负责人
                $auditor = $NodeParse->parseOrgManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_ROLE: //指定角色
                $auditor = $NodeParse->parseSpecifyRole($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_ORG_AREA_MANAGER: //大区/片区/网点负责人
                $auditor = $NodeParse->parseStoreManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPEC_ORG_MGR: //指定职能管理
                $auditor = $NodeParse->parseFuncManagement($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR: //指定部门负责人
                $auditor = $NodeParse->parseSpecOrgManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_BU_CL_MGR: //BU/Clevel负责人
                $auditor = $NodeParse->parseBuClevelManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_POSITION: //指定职位
                $auditor = $NodeParse->parseJobTitle($params);
                break;
            default:
                $auditor = [];
                break;
        }
        return $auditor;
    }

    /**
     * 解析节点
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function parseNodeByForm($params): array
    {
        Validation::validate($params, [
            'approvalType' => 'Required|Int|>>>:' . "missing parameter `approvalType`",
        ]);

        $NodeParse = ParseNodeService::getInstance();
        switch ($params['approvalType']) {
            case Enums::NODE_AUDITOR_TYPE_ORG_MANAGER: //部门负责人
                $detail = $NodeParse->parseOrgManagerByForm($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR: //指定部门负责人
                $detail = $NodeParse->parseSpecOrgManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_BU_CL_MGR: //BU/Clevel负责人
                $detail = $NodeParse->parseFormBuClevelManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_SUPERIOR: //指定上级
                $detail = $NodeParse->parseFromSpecifySuperior($params['val']);
                break;
            case Enums::NODE_AUDITOR_TYPE_ORG_AREA_MANAGER: //大区/片区/网点负责人
                $detail = $NodeParse->parseFromStoreManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_SPECIFY_ROLE: //指定角色
                $detail = $NodeParse->parseFromSpecifyRole($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_HASH_TABLE: //分拨经理（作废）
                $detail = $NodeParse->parseFromSpecifyStaffByHashtable($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_HUB_STANDARDIZATION_AND_ADMIN: //分拨标准化or行政
                $detail = $NodeParse->parseFromHubStandardizationAndHubAdmin($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_HUB_AREA_MANAGER: //分拨大区经理
                $detail = $NodeParse->parseFromHubAreaManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_HUB_STORE_MANAGER: //分拨经理
                $detail = $NodeParse->parseFromHubStoreManager($params);
                break;
            case Enums::NODE_AUDITOR_TYPE_STAFF_SELF: //员工本人
                $detail = $NodeParse->parseFromStaffSelf($params);
                break;
            default:
                $detail = [];
                break;
        }
        return $detail;
    }
}