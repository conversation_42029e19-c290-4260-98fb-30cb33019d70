<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\Enums;
use App\Library\Enums\WorkflowManageEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Models\backyard\RolesModel;
use App\Modules\Hc\Models\SysDepartmentModel;

class ParseNodeService extends BaseService
{
    /**
     * 实例
     * @var void
     */
    private static $instance = '';

    /**
     * 单一实例
     * @return ParseNodeService
     */
    public static function getInstance(): ParseNodeService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取指定工号
     * @param $params
     * @return array
     */
    public function parseSpecifyStaff($params): array
    {
        //参数校验
        //Validation::validate($params, [
        //    'val[*].value' => 'Required|Int|>>>:' . "missing parameter `value` (staff info id)",
        //    'val[*].key' => 'Required|Str|>>>:' . "missing parameter `name` (staff name)",
        //]);
        $auditor = $params['val'] ?? [];
        $auditor = array_column($auditor, 'value');

        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_DESIGNATE_OTHER,
            'auditor_id' => $auditor ? implode(',', $auditor): "",
        ];
    }

    /**
     * 指定上级
     * @param int $params
     * @return array
     */
    public function parseSpecifySuperior(int $params): array
    {
        return [
            'auditor_type'    => WorkflowManageEnums::WF_NODE_MANAGER,
            'auditor_level'   => $params ?? 1,
        ];
    }
    /**
     * 根据表单找指定上级
     * @param int $params
     * @return array
     */
    public function parseFromSpecifySuperior(int $params): array
    {
        return [
            'auditor_type'    => WorkflowManageEnums::WF_NODE_MANAGER_FROM,
            'auditor_level'   => $params ?? 1,
        ];
    }

    /**
     * 根据申请人查找组织负责人
     * type=1 申请人所属部门负责人
     * 1-所属部门负责人 2-一级部门负责人 3-二级部门负责人 4-三级部门负责人 5-四级部门负责人 101-所属BU负责人
     * 150-所属C-level负责人
     *
     * type=2 指定部门负责人
     *
     * @param array $params
     * @return array
     */
    public function parseOrgManager(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|Int|>>>:' . "parseOrgManager missing parameter `val`",
        ]);

        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_DEPARTMENT_MANAGER_V3,
            'auditor_level' => $params['val'] ?? ""
        ];
    }

    /**
     * 根据申请人查找指定组织负责人
     *
     * @param array $params
     * @return array
     */
    public function parseSpecOrgManager(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|Int|>>>:' . "parseSpecOrgManager missing parameter `val`",
        ]);

        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_SPEC_DEPARTMENT_MANAGER,
            'auditor_level' => $params['val'] ?? ""
        ];
    }

    /**
     * 根据申请人查找BU/Clevel负责人
     *
     * @param array $params
     * @return array
     */
    public function parseBuClevelManager(array $params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER,
        ];
    }

    /**
     * 根据申请人查找指定职位
     *
     * @param array $params
     * @return array
     */
    public function parseJobTitle(array $params): array
    {
        //校验传入参数
        //校验传入参数
        Validation::validate($params['val'], [
            'val'            => 'Required|IntIn:1,2,3,4|>>>:[parseJobTitle] invalid type',
            'position'       => 'Required|obj|>>>:[parseJobTitle] missing parameter position',
            //'position.value' => 'Required|IntGt:0|>>>:[parseJobTitle] position id is missing',
            'department'     => 'IfIntEq:val,4|Required|IntGt:0|>>>:[parseJobTitle] department id is missing or invalid',
        ]);

        //职位限定类型
        $transferType = $params['val']['val'] ?? 0;
        $jobTitle = $params['val']['position']['value'] ?? ($params['val']['position']['id'] ?? 0);
        $specDepartment = $params['val']['department'] ?? 0;

        switch ($transferType) {
            case WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_NO_LIMIT: //不限
                $config = [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_JOB_TITLE,
                    'auditor_id' => $jobTitle ?? ""
                ];
                break;
            case WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_DEPARTMENT: //跟申请人部门一致
                $config = [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_JOB_TITLE_SAME_DEPT,
                    'auditor_level' => $jobTitle ?? ""
                ];
                break;
            case WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_LEVEL_DEPARTMENT: //指定职位-与申请人所在一级部门相同
                $config = [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_JOB_TITLE_SAME_FIRST_DEPT,
                    'auditor_level' => $jobTitle ?? ""
                ];
                break;
            case WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SPEC_DEPARTMENT:
                $config = [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_DEPARTMENT_JOB_TITLE_FORM,
                    'auditor_level' => $specDepartment ?? "",
                    'auditor_id' => $jobTitle ?? ""
                ];
                break;
            default:
                $config = [];
                break;
        }

        return $config;
    }


    /**
     * 根据Form查找组织负责人
     * @param array $params
     * @return array
     */
    public function parseOrgManagerByForm(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|Int|>>>:' . "parseOrgManagerByForm missing parameter `val`",
        ]);

        //存在多传入值
        //则在可视化需要指定参数
        //在backyard-api Enums::WF_NODE_DEPARTMENT_MANAGER_MUL_BY_FORM、Enums::WF_NODE_HRBP_MUL_FROM传入多个网点参数
        //auditor_id 使用指定第几个参数
        if (in_array($params['formColumn'], [Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE, Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER])) {

            //如果是多级的，auditor_id
            return [
                'auditor_type'  => WorkflowManageEnums::WF_NODE_DEPARTMENT_MANAGER_MUL_BY_FORM,
                'auditor_level' => $params['val'] ?? '',
                'auditor_id'    => $params['formColumn'] == Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE ? 1 : 2,
            ];
        } else {
            return [
                'auditor_type'  => WorkflowManageEnums::WF_NODE_DEPARTMENT_MANAGER_V3_BY_FORM,
                'auditor_level' => $params['val'] ?? '',
            ];
        }


    }

    /**
     * 指定HRBP角色
     * @param array $params
     * @return array
     */
    public function parseSpecifyRole($params): array
    {
        //获取有效的角色值
        $roles    = RolesModel::getRolesForViewableWorkflow();
        $rolesStr = implode(',', $roles);

        //校验传入参数
        Validation::validate($params, [
            'val' => sprintf('Required|IntIn:%s|>>>:'."parseSpecifyRole missing parameter `val` or roles do not valid",
                $rolesStr),
        ]);

        switch ($params['val']) {
            case RolesModel::ROLE_HRBP:
                $auditorType = WorkflowManageEnums::WF_NODE_HRBP;
                break;
            case RolesModel::ROLE_HR_SERVICE:
                $auditorType = WorkflowManageEnums::WF_NODE_HR_SERVICE;
                break;
            default:
                $auditorType  = WorkflowManageEnums::WF_NODE_ROLE;
                $auditorLevel = $params['val'];
                break;
        }

        return [
            'auditor_type'  => $auditorType,
            'auditor_level' => $auditorLevel ?? null,
        ];
    }

    /**
     * 根据表单指定HRBP角色
     * @param array $params
     * @return array
     */
    public function parseFromSpecifyRole($params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|Int|>>>:'.'parseSpecifyRole missing parameter `val`',
        ]);

        //存在多传入值
        //则在可视化需要指定参数
        //在backyard-api Enums::WF_NODE_HRBP_MUL_FROM传入多个网点参数
        //需根据audit_level 使用指定第几个参数
        if (in_array($params['formColumn'], [
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE,
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER,
        ])) {

            //如果是多级的，那么需要将级别存入audit_level
            $level = $this->getMultiLevelParameters($params['formColumn']);
            return [
                'auditor_type'  => WorkflowManageEnums::WF_NODE_HRBP_MUL_FROM,
                'auditor_level' => $level,
            ];
        } else if (in_array($params['val'], [RolesModel::ROLE_HRBP, RolesModel::ROLE_HR_SERVICE])) {
            if ($params['val'] == RolesModel::ROLE_HRBP) {
                return [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_HRBP_FROM,
                ];
            } else {
                return [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_HR_SERVICE_FROM,
                ];
            }
        } else {
            return [
                'auditor_type' => WorkflowManageEnums::WF_NODE_HRBP_FROM,
            ];
        }
    }

    /**
     * @description 根据表单获取多级连续参数中的指定参数
     * @param $column
     * @return int
     */
    private function getMultiLevelParameters($column): int
    {
        if ($column == Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE) {
            return Enums\WorkflowNodeEnums::LEVEL_FIRST;
        } else if ($column == Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER) {
            return Enums\WorkflowNodeEnums::LEVEL_SECOND;
        } else {
            return Enums\WorkflowNodeEnums::LEVEL_DEFAULT;
        }
    }

    /**
     * 根据表单指定分拨经理
     * @param array $params
     * @return array
     */
    public function parseFromSpecifyStaffByHashtable($params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_HASHTABLE_FROM,
        ];
    }

    /**
     * 根据表单指定Hub Standardization&Hub Admin
     * @param array $params
     * @return array
     */
    public function parseFromHubStandardizationAndHubAdmin($params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_HUB_SUB_DEPARTMENT_BY_NAME,
        ];
    }

    public function parseFromHubAreaManager($params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_HUB_AREA_MANAGER,
        ];
    }

    /**
     * 根据表单指定分拨经理
     * @param array $params
     * @return array
     */
    public function parseFromHubStoreManager($params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_HUB_STORE_MANAGER,
        ];
    }

    /**
     * 根据表单-员工本人
     * @param array $params
     * @return array
     */
    public function parseFromStaffSelf($params): array
    {
        return [
            'auditor_type' => WorkflowManageEnums::WF_NODE_STAFF_SELF_FROM,
        ];
    }

    /**
     * 根据申请人查找组织区域负责人
     * 1=网点负责人 2=片区负责人 3=大区负责人
     * @param int $params
     * @return array
     */
    public function parseStoreManager(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|IntIn:1,2,3|>>>:' . "parseStoreManager missing parameter `val`",
        ]);

        if ($params['val'] == 3) { //大区负责人
            return [
                'auditor_type' => WorkflowManageEnums::WF_NODE_AM_BY_ORG,
            ];

        } else if ($params['val'] == 2) { //片区负责人
            return [
                'auditor_type' => WorkflowManageEnums::WF_NODE_DM_BY_ORG,
            ];
        } else { //网点负责人
            return [
                'auditor_type' => WorkflowManageEnums::WF_NODE_STORE_MANAGER,
            ];
        }
    }

    /**
     * 根据表单查找组织区域负责人
     * 1=网点负责人 2=片区负责人 3=大区负责人
     * @param array $params
     * @return array
     * @throws ValidationException
     */
    public function parseFromStoreManager(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val' => 'Required|IntIn:1,2,3|>>>:'.'parseStoreManager missing parameter `val`',
        ]);

        //存在多传入值
        //则在可视化需要指定参数
        //在backyard-api Enums::WF_NODE_DM_BY_ORG_FROM、Enums::WF_NODE_AM_BY_ORG_FROM传入多个网点参数
        //需根据audit_level 使用指定第几个参数
        if (in_array($params['formColumn'], [
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE,
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER,
        ])) {

            //如果是多级的，那么需要将级别存入audit_level
            $level = $this->getMultiLevelParameters($params['formColumn']);
            if ($params['val'] == 3) { //大区负责人
                $auditType = WorkflowManageEnums::WF_NODE_AM_BY_ORG_MUL_FROM;
            } else {
                if ($params['val'] == 2) { //片区负责人
                    $auditType = WorkflowManageEnums::WF_NODE_DM_BY_ORG_MUL_FROM;
                } else { //网点负责人
                    $auditType = WorkflowManageEnums::WF_NODE_STORE_MANAGER_MUL_FROM;
                }
            }
            return [
                'auditor_type'  => $auditType,
                'auditor_level' => $level,
            ];
        } else {
            if ($params['val'] == 3) { //大区负责人
                return [
                    'auditor_type' => WorkflowManageEnums::WF_NODE_AM_BY_ORG_FROM,
                ];
            } else {
                if ($params['val'] == 2) { //片区负责人
                    return [
                        'auditor_type' => WorkflowManageEnums::WF_NODE_DM_BY_ORG_FROM,
                    ];
                } else { //网点负责人
                    return [
                        'auditor_type' => WorkflowManageEnums::WF_NODE_STORE_MANAGER_FROM,
                    ];
                }
            }
        }
    }

    /**
     * 根据申请人查找BU/Clevel负责人
     *
     * @param array $params
     * @return array
     */
    public function parseFormBuClevelManager(array $params): array
    {
        if (in_array($params['formColumn'], [
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_BEFORE,
            Enums\WorkflowNodeEnums::APPROVAL_FROM_OPTIONS_TRANSFER_AFTER,
        ])) {
            $level = $this->getMultiLevelParameters($params['formColumn']);

            return [
                'auditor_type'  => WorkflowManageEnums::WF_NODE_BU_C_LEVEL_FROM,
                'auditor_level' => $level,
            ];
        } else {
            return [
                'auditor_type' => WorkflowManageEnums::WF_NODE_FIRST_BU_DEPARTMENT_MANAGER_BY_FORM,
            ];
        }
    }

    /**
     * 职能管理
     * @param array $params
     * @return array
     */
    public function parseFuncManagement(array $params): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val'   => 'Required|Int|>>>:' . "parseFuncManagement missing parameter `val`",
        ]);

        return [
            'auditor_type'  => WorkflowManageEnums::WF_NODE_BP_HEAD,
            'auditor_level' => $params['val']
        ];
    }
}