<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Enums\WorkflowManageEnums;

class ReverseParseDataService extends BaseService
{
    private $branch_conditions = null; //审批条件，如: 申请人所属部门等
    private $branch_options = null; //审批条件可执行操作，如: 等于、不等于、包含等
    private $node_conditions = null; //节点条件，如: 指定工号、指定上级等
    private $node_form = null; //节点Form条件，即：根据表单Form字段，结合节点条件一起组合成查找审批人逻辑
    private $roles_list = null; //可配置角色
    private $staff_manage_type = null; //职能管理分类
    private $total_department = null; //全部部门
    private $total_region = null; //全部大区
    private $total_piece = null; //全部片区
    private $total_job_title = null; //全部职位
    private $total_store = null; //全部网点

    public function init(): ReverseParseDataService
    {
        //set全部分支条件
        $this->setBranchConditions(BranchService::getInstance()->getAllCondition());
        //set全部节点条件
        $this->setNodeConditions(NodeService::getInstance()->getAllWorkflowNodeConditions());
        //set全部操作，等于、不等于、包含、不包含等
        $this->setBranchOptions(BranchService::getInstance()->getAllOperation());
        //set全部角色
        $this->setRolesList(SysService::getInstance()->getRolesList(['type' => WorkflowManageEnums::SCOPE_ALL]));
        //set职能管理
        $this->setStaffManageType(SysService::getInstance()->getManageType());
        //set部门
        $this->setTotalDepartment(SysService::getInstance()->getAllDepartment());
        //set全部表单项
        $this->setNodeForm(NodeService::getInstance()->getAllForm());
        //set全部大区
        $this->setTotalRegion(SysService::getInstance()->getAllRegion());
        //set全部片区
        $this->setTotalPiece(SysService::getInstance()->getAllPiece());
        //set全部职位
        $this->setTotalJobTitle(SysService::getInstance()->getAllJobTitle());
        //set全部网点
        $this->setTotalStore(SysService::getInstance()->getAllStore());
        return $this;
    }

    /**
     * @description 获取审批条件
     * @return array
     */
    public function getBranchConditions(): array
    {
        return $this->branch_conditions;
    }

    /**
     * @description 保存审批条件
     * @param null $branch_conditions
     */
    private function setBranchConditions($branch_conditions): void
    {
        $this->branch_conditions = $branch_conditions;
    }

    /**
     * @description 获取审批条件可进行操作
     * @return array
     */
    public function getBranchOptions(): array
    {
        return $this->branch_options;
    }

    /**
     * @description 保存审批条件可进行操作
     * @param array $branch_options
     */
    private function setBranchOptions(array $branch_options): void
    {
        $this->branch_options = $branch_options;
    }

    /**
     * @description 获取审批节点条件
     * @return array
     */
    public function getNodeConditions(): ?array
    {
        return $this->node_conditions;
    }

    /**
     * @description 保存审批节点条件
     * @param array $node_conditions
     */
    private function setNodeConditions(array $node_conditions): void
    {
        $this->node_conditions = $node_conditions;
    }

    /**
     * @description 获取审批节点可选角色条件
     * @return array
     */
    public function getRolesList(): ?array
    {
        return $this->roles_list;
    }

    /**
     * @description 保存审批节点可选角色条件
     * @param array $roles_list
     */
    private function setRolesList(array $roles_list): void
    {
        $this->roles_list = $roles_list;
    }

    /**
     * @description 获取职能管理
     * @return array
     */
    public function getStaffManageType(): ?array
    {
        return $this->staff_manage_type;
    }

    /**
     * @description 保存职能管理
     * @param array $staff_manage_type
     */
    private function setStaffManageType(array $staff_manage_type): void
    {
        $this->staff_manage_type = $staff_manage_type;
    }

    /**
     * @description 获取全部部门(columns=id,name)
     * @return array
     */
    public function getTotalDepartment(): ?array
    {
        return $this->total_department;
    }

    /**
     * @description 保存全部部门(columns=id,name)
     * @param array $total_department
     */
    private function setTotalDepartment(array $total_department): void
    {
        $this->total_department = $total_department;
    }

    /**
     * @return null
     */
    public function getNodeForm()
    {
        return $this->node_form;
    }

    /**
     * @param null $node_form
     */
    private function setNodeForm($node_form): void
    {
        $this->node_form = $node_form;
    }

    /**
     * @return null
     */
    public function getTotalRegion()
    {
        return $this->total_region;
    }

    /**
     * @param null $total_region
     */
    public function setTotalRegion($total_region): void
    {
        $this->total_region = $total_region;
    }

    /**
     * @return null
     */
    public function getTotalPiece()
    {
        return $this->total_piece;
    }

    /**
     * @param null $total_piece
     */
    public function setTotalPiece($total_piece): void
    {
        $this->total_piece = $total_piece;
    }

    /**
     * @return null
     */
    public function getTotalJobTitle()
    {
        return $this->total_job_title;
    }

    /**
     * @param null $total_job_title
     */
    public function setTotalJobTitle($total_job_title): void
    {
        $this->total_job_title = $total_job_title;
    }

    /**
     * @return null
     */
    public function getTotalStore()
    {
        return $this->total_store;
    }

    /**
     * @param null $total_store
     */
    public function setTotalStore($total_store): void
    {
        $this->total_store = $total_store;
    }
}