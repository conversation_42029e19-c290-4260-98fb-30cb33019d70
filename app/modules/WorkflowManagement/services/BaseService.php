<?php

namespace App\Modules\WorkflowManagement\Services;


use App\Library\RedisClient;

class BaseService extends \App\Library\BaseService
{
    /**
     * 获取审批流版本
     * @return string
     */
    public function getWorkflowVersionSerialNo(): string
    {
        return date("YmdHi") . str_pad($this->getRandomIncrSalt('workflow_version_id', 3600), 7, 0, STR_PAD_LEFT);
    }

    public function getRandomIncrSalt($key, $expire)
    {
        $redis = RedisClient::getInstance()->getClient();
        $SCRIPT = <<<LUA
local key = KEYS[1]
local ttl = ARGV[1]
local salt = 0
if (redis.call('EXISTS', key) == 1) then
    return redis.call('INCR', key)
else
    salt = redis.call('INCR', key)
    redis.call('EXPIRE', key, ttl)
end
return salt
LUA;
        return $redis->eval($SCRIPT, [$key, $expire], 1);
    }
}