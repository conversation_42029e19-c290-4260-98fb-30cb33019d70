<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Enums;

class BaseNodeService extends BaseService
{

    /**
     * 等于操作
     */
    public static function getOperatorEqual(): array
    {
        return [
            'operator_name' => self::$t->_('view_equal_to'),//'等于',
            'operator_type' => Enums::OPERATE_EQ,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 包含操作
     */
    public static function getOperatorContain(): array
    {
        return [
            'operator_name' => self::$t->_('view_contains'),//'包含',
            'operator_type' => Enums::OPERATE_IN,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_MULTI, //1-单选 2-多选
        ];
    }

    /**
     * 不等于操作
     */
    public static function getOperatorNotEqual(): array
    {
        return [
            'operator_name' => self::$t->_('view_no_equal_to'),//'不等于',
            'operator_type' => Enums::OPERATE_NE,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 不包含操作
     */
    public static function getOperatorNotContain(): array
    {
        return [
            'operator_name' => self::$t->_('view_not_included'),//'不包含',
            'operator_type' => Enums::OPERATE_NOT_IN,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_MULTI, //1-单选 2-多选
        ];
    }

    /**
     * 大于操作
     */
    public static function getOperatorGt(): array
    {
        return [
            'operator_name' => self::$t->_('view_greater_than'),// '大于',
            'operator_type' => Enums::OPERATE_GT,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 大于等于操作
     */
    public static function getOperatorGe(): array
    {
        return [
            'operator_name' => self::$t->_('view_greater_equal_to'),//'大于等于',
            'operator_type' => Enums::OPERATE_GE,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 小于操作
     */
    public static function getOperatorLt(): array
    {
        return [
            'operator_name' => self::$t->_('view_less_than'),//'小于',
            'operator_type' => Enums::OPERATE_LT,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 小于等于操作
     */
    public static function getOperatorLe(): array
    {
        return [
            'operator_name' => self::$t->_('view_less_than_equal'),//'小于等于',
            'operator_type' => Enums::OPERATE_LE,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 属于操作
     */
    public static function getOperatorBelongTo(): array
    {
        return [
            'operator_name' => self::$t->_('view_part_of'),//'属于',
            'operator_type' => Enums::OPERATE_BELONG_TO,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 不属于操作
     */
    public static function getOperatorNotBelongTo(): array
    {
        return [
            'operator_name' => self::$t->_('view_not_part_of'),//'不属于',
            'operator_type' => Enums::OPERATE_NOT_BELONG_TO,
            'multiple'      => Enums\WorkflowBranchEnums::OPERATION_SINGLE, //1-单选 2-多选
        ];
    }

    /**
     * 获取全部的条件类型
     * @return array
     */
    public static function getAllOperation(): array
    {
        return [
            self::getOperatorEqual(),
            self::getOperatorContain(),
            self::getOperatorNotEqual(),
            self::getOperatorNotContain(),
            self::getOperatorGt(),
            self::getOperatorGe(),
            self::getOperatorLt(),
            self::getOperatorLe(),
            self::getOperatorBelongTo(),
            self::getOperatorNotBelongTo(),
        ];
    }
}