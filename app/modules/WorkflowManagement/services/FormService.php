<?php

namespace app\modules\WorkflowManagement\services;

use App\Library\Enums\WorkflowNodeEnums;
use App\Models\backyard\WorkflowFormModel;

/**
 * 表单service
 */
class FormService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return \App\Modules\WorkflowManagement\Services\FormService
     */
    public static function getInstance(): FormService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public function getEditFieldByFlowId($flow_id, $columns = ''): array
    {
        if (empty($flow_id)) {
            return [];
        }
        $conditions['flow_id'] = $flow_id;
        if (empty($columns)) {
            $columns = 'id as key,column_name as label';
        }
        return $this->getQueryBuilder($conditions, $columns)->toArray();
    }

    public function getAllEditField()
    {
        return $this->getQueryBuilder([], 'id,column,column_type')->toArray();
    }

    private function getQueryBuilder($conditions = [], $columns = '')
    {
        $curConditions = 'deleted = 0';
        $curBind       = [];
        if (!empty($conditions['flow_id'])) {
            $curConditions      .= ' and flow_id = :flow_id:';
            $curBind['flow_id'] = $conditions['flow_id'];
        }
        return WorkflowFormModel::find([
            'conditions' => $curConditions,
            'bind'       => $curBind,
            'columns'    => $columns,
        ]);
    }

    /**
     * @description 组织可编辑字段
     * @param $canEditField
     * @param $fieldIdList
     * @return string
     */
    public function generateEditField($canEditField, $fieldIdList): string
    {
        if ($canEditField == WorkflowNodeEnums::NODE_EDIT_FIELD_STATE_CAN_NOT) { //如果不可编辑，则返回为空
            return '';
        }

        if (!is_array($fieldIdList) || count($fieldIdList) <1) {
            return '';
        }

        //获取全部表单项
        $editFieldList        = $this->getAllEditField();
        $editFieldListArr     = array_column($editFieldList, 'column', 'id');
        $editFieldListTypeArr = array_column($editFieldList, 'column_type', 'id');

        $jsonResult = [];
        foreach ($fieldIdList as $item) {
            $columnType = $editFieldListTypeArr[$item];
            if (empty($editFieldListArr[$item])) {
                $this->logger->warning(sprintf('editFieldList item {%s} not exist', $item));
                continue;
            }
            $jsonResult[$columnType][] = $editFieldListArr[$item];
        }
        return json_encode($jsonResult, true);
    }
}