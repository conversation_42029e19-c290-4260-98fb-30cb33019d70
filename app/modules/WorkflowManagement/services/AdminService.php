<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Validation\ValidationException;
use App\Models\backyard\HrStaffInfoModel;
use App\Modules\Hc\Models\HrJobTitleModel;
use App\Modules\WorkflowManagement\Models\WorkflowPermissionModel;

class AdminService extends BaseService
{
    /**
     * 实例
     * @var void
     */
    private static $instance = '';

    private static $validate_list = [
        'page_size' => 'Required|IntGt:0',
        'page_num' => 'Required|IntGt:0',
    ];

    private static $validate_save = [
        'staff_info_id' => 'Required|IntGt:0',
        'permission' => 'Required|Arr|ArrLenGeLe:1,100',
    ];

    private static $validate_edit = [
        'staff_info_id' => 'Required|IntGt:0',
    ];

    /**
     * 单一实例
     * @return AdminService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @return array
     */
    public static function getValidateList(): array
    {
        return self::$validate_list;
    }

    /**
     * @return string[]
     */
    public static function getValidateSave(): array
    {
        return self::$validate_save;
    }

    /**
     * @return string[]
     */
    public static function getValidateEdit(): array
    {
        return self::$validate_edit;
    }

    /**
     * 获取审批流列表
     * @param $params
     * @param $id
     * @return array
     */
    public function list($params, $id): array
    {
        $staffInfoId = $params['staff_info_id'] ?? '';
        $pageNum = $params['page_num'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;
        $offset = $pageSize * ($pageNum - 1);

        //获取已经对接的审批流
        $validWorkflow = SysService::getInstance()->getWorkflowRelateType();
        $validWorkflowArr = array_column($validWorkflow, 'code');
        $workflowName = array_column($validWorkflow, 'name','code');

        //获取list
        $builder = $this->generateBuilder([
            'staff_info_id' => $staffInfoId,
            'workflow_id' => $validWorkflowArr
        ],'staff_info_id,group_concat(relate_type) as relate_type');
        $builder->groupBy("staff_info_id");
        $builder->orderBy('id desc');
        $builder->limit($pageSize, $offset);
        $list = $builder->getQuery()->execute()->toArray();

        //获取对应count
        $builder = $this->generateBuilder([
            'staff_info_id' => $staffInfoId,
            'workflow_id' => $validWorkflowArr
        ],'count(distinct(staff_info_id)) as cou');
        $count = $builder->getQuery()->getSingleResult()->cou;

        //获取员工姓名、职位
        $staffInfoIds = array_column($list, 'staff_info_id');
        if (empty($staffInfoIds)) {
            return [
                'item' => [],
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page' => $pageSize,
                    'total_count' => 0,
                ]
            ];
        }
        $staffList = HrStaffInfoModel::find([
            'columns'    => ['staff_info_id', 'name', 'job_title'],
            'conditions' => 'staff_info_id in ({staff_info_id:array})',
            'bind'       => ['staff_info_id' => $staffInfoIds],
        ])->toArray();

        $staffName = array_column($staffList, 'name', 'staff_info_id');
        $jobTitleIds = array_column($staffList, 'job_title');
        $jobTitleIdsMap = array_column($staffList, 'job_title', 'staff_info_id');

        if (!empty($jobTitleIds)) {
            $job_list = HrJobTitleModel::find([
                'columns'    => ['job_name', 'id'],
                'conditions' => 'id in ({job_title_ids:array})',
                'bind'       => ['job_title_ids' => $jobTitleIds],
            ])->toArray();
            if (!empty($job_list)) {
                $jobIdNameMap = array_column($job_list, 'job_name', 'id');
            }
        }

        foreach ($list as $k => $v) {
            $list[$k]['staff_name'] = $staffName[$v['staff_info_id']] ?? '--';
            $jobTitle = $jobTitleIdsMap[$v['staff_info_id']] ?? '';
            $list[$k]['job_title'] = $jobTitle;
            $list[$k]['job_title_name'] = $jobIdNameMap[$jobTitle] ?? '--';

            if (!empty($v['relate_type'])) {
                $relateTypeList = explode(',', $v['relate_type']);
                $countRelateTypeList = count(array_values(array_unique($relateTypeList)));

                $countStr = sprintf('等 %d个', $countRelateTypeList);

                if ($countRelateTypeList > 3) {
                    $relateTypeList =  array_slice ($relateTypeList,0,3);
                }
                $workflowNameList = array_map(function ($v) use($workflowName) {
                    return $workflowName[$v];
                }, $relateTypeList);

                $typeStr = !empty($workflowNameList)? join(',', $workflowNameList): '';

                $list[$k]['relate_type_text'] = $countRelateTypeList > 3 ? $typeStr . $countStr :$typeStr;
            }
        }
        $return['item'] = $list;
        $return['pagination'] = [
            'current_page' => $pageNum,
            'per_page' => $pageSize,
            'total_count' => intval($count),
        ];
        return $return;
    }

    /**
     * 组织builder
     * @param $params
     * @param $columns
     * @return mixed
     */
    private function generateBuilder($params, $columns)
    {
        $staffInfoId = $params['staff_info_id'];
        $validWorkflow = $params['workflow_id'];

        //查询列表
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(['j' => WorkflowPermissionModel::class]);
        $builder->where('1=1');
        if (!empty($staffInfoId)) {
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $staffInfoId]);
        }

        if (!empty($validWorkflowArr)) {
            $builder->inWhere('relate_type', $validWorkflow);
        }
        $builder->andWhere('is_view = 1 or is_edit = 1');
        $builder->andWhere('deleted = 0');

        return $builder;
    }

    /**
     * 添加
     * @param $params
     * @param $id
     * @return mixed
     * @throws ValidationException
     */
    public function add($params, $id)
    {
        $staffInfoId = $params['staff_info_id'] ?? '';
        $permissionList = $params['permission'] ?? '';

        //获取权限
        $info = $this->getWorkflowPermissionByStaffId($staffInfoId);
        if (!empty($info)) {
            throw new ValidationException(static::$t->_('the_staff_no_is_repeat'));
        }

        if (empty($permissionList) || !is_array($permissionList)) {
            throw new ValidationException(static::$t->_('err_msg_at_least_add_one'));
        }

        $is_not_null = false;
        foreach ($permissionList as $item) {
            if ($item['is_edit'] || $item['is_view']) {
                $is_not_null = true;
                break;
            }
        }

        if ($is_not_null === false) {
            throw new ValidationException(static::$t->_('err_msg_at_least_add_one'));
        }

        foreach ($permissionList as $p) {
            $model = new WorkflowPermissionModel();
            $model->staff_info_id = $staffInfoId;
            $model->relate_type = $p['value'];
            $model->is_edit = $p['is_edit'] ? 1: 0;
            $model->is_view = $p['is_view'] ? 1: 0;
            $model->save();
        }
        return true;
    }


    /**
     * 添加
     * @param $params
     * @param $id
     * @return mixed
     * @throws ValidationException
     */
    public function edit($params, $id)
    {
        $staffInfoId = $params['staff_info_id'] ?? '';
        $permissionList = $params['permission'] ?? '';

        //获取权限
        $permission = WorkflowPermissionModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and deleted = 0 and (is_view = 1 or is_edit = 1)",
            'bind' => [
                'staff_info_id' => $staffInfoId
            ]
        ]);
        if (empty($permission)) {
            throw new ValidationException('This staff do not have view/edit permission!');
        }

        if (empty($permissionList)) {
            throw new ValidationException(static::$t->_('err_msg_at_least_add_one'));
        }

        //全部软删除
        foreach ($permission as $item) {
            $item->deleted = 1;
            $item->update();
        }

        foreach ($permissionList as $p) {
            $model = new WorkflowPermissionModel();
            $model->staff_info_id = $staffInfoId;
            $model->relate_type = $p['value'];
            $model->is_edit = $p['is_edit'] ? 1: 0;
            $model->is_view = $p['is_view'] ? 1: 0;
            $model->save();
        }
        return true;
    }

    /**
     * 删除
     * @param $params
     * @param $id
     * @return bool
     */
    public function delete($params, $id)
    {
        $staffInfoId = $params['staff_info_id'] ?? '';

        //获取权限
        $permission = WorkflowPermissionModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and deleted = 0 and (is_view = 1 or is_edit = 1)",
            'bind' => [
                'staff_info_id' => $staffInfoId
            ]
        ]);
        if (empty($permission)) {
            return true;
        }

        foreach ($permission as $p) {
            $p->deleted = 1;
            $p->update();
        }

        return true;
    }

    /**
     * 获取详情
     * @param $params
     * @param $id
     * @return array
     */
    public function info($params, $id)
    {
        $staffInfoId = $params['staff_info_id'] ?? '';

        $res['staff_info_id'] = $staffInfoId;

        //获取权限
        $permission = $this->getWorkflowPermissionByStaffId($staffInfoId);

        $validWorkflow = SysService::getInstance()->getWorkflowRelateType();
        $workflow = array_column($validWorkflow, 'name_w', 'code');

        foreach ($permission as $k => $item) {
            $permission[$k]['title'] = $workflow[$item['relate_type']] ?? '';
            $permission[$k]['is_view'] = boolval($item['is_view']);
            $permission[$k]['is_edit'] = boolval($item['is_edit']);
            $permission[$k]['value'] = intval($item['relate_type']);
        }

        $res['permission'] = $permission;

        return $res;
    }

    /**
     * 获取审批类型列表
     * @return array
     */
    public function getAuditList()
    {
        $validWorkflow = SysService::getInstance()->getWorkflowRelateType();

        if (empty($validWorkflow)) {
            return ['list' => []];
        }

        $result = array_map(function ($v) {
            return [
                'key' => $v['name'],
                'value' => $v['code'],
            ];
        }, $validWorkflow);

        return ['list' => $result];
    }

    /**
     * 获取拥有的权限
     * @param $staffInfoId
     * @return array
     */
    public function getWorkflowPermissionByStaffId($staffInfoId): array
    {
        return WorkflowPermissionModel::find([
            'conditions' => "staff_info_id = :staff_info_id: and deleted = 0 and (is_view = 1 or is_edit = 1)",
            'bind' => [
                'staff_info_id' => $staffInfoId
            ]
        ])->toArray();
    }
}