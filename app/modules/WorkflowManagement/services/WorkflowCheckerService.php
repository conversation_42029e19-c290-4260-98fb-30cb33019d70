<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Validation\ValidationException;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;

class WorkflowCheckerService extends BaseService
{
    const FEISHU_INVALID_CONDITIONS_MSG_KEY = '871a9e80-ac68-423f-bf69-55776e488904';

    /**
     * @throws ValidationException
     */
    public function check()
    {
        $workflowList = ByWorkflowModel::find([
            'conditions' => 'is_view = 1',
            'columns' => 'id,relate_type,flow_request,name',
        ])->toArray();
        if (empty($workflowList)) {
            return;
        }
        $parseService = ParseService::getInstance();
        foreach ($workflowList as $workflow) {
            $invalidList = $parseService->checkWorkflow($workflow, $workflow['relate_type']);

            if (empty($invalidList)) {
                continue;
            }
            $seenUuids                   = [];
            $uniqueInvalidConditionsList = [];
            foreach ($invalidList as $item) {
                if (!in_array($item['uuid'], $seenUuids)) {
                    $uniqueInvalidConditionsList[] = $item;         // 添加到去重数组中
                    $seenUuids[]                   = $item['uuid']; // 记录已出现过的 uuid
                }
            }
            if (empty($uniqueInvalidConditionsList)) {
                continue;
            }
            foreach ($uniqueInvalidConditionsList as $item) {
                send_feishu_text_msg($this->format_content($item, $workflow['name']), self::FEISHU_INVALID_CONDITIONS_MSG_KEY);
            }
        }
    }

    /**
     * 组织飞书消息内容
     * @param $invalidData
     * @param $workflowName
     * @return string
     */
    private function format_content($invalidData, $workflowName): string
    {
        $content  = sprintf("国家: %s", get_country_code()) . PHP_EOL;
        $content  .= sprintf("环境: %s", env('runtime')) . PHP_EOL;
        $content  .= sprintf("审批名称: %s", $workflowName) . PHP_EOL;
        $content  .= sprintf("条件: %s", $invalidData['reverse_label']) . PHP_EOL;
        $content  .= sprintf("组: %s", $invalidData['group_index']) . PHP_EOL;
        $content  .= sprintf("失效条件: %s", $invalidData['condition_name']) . PHP_EOL;
        $content  .= sprintf("失效值: %s", $invalidData['invalid_value']) . PHP_EOL;
        return $content;
    }
}