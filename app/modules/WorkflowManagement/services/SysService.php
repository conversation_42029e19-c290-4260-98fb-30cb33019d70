<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\WorkflowManageEnums;
use App\Models\backyard\RolesModel;
use App\Models\backyard\SettingEnvModel;
use App\Models\backyard\SysDepartmentModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreTypeModel;
use App\Modules\AccidentReport\Models\SysDepartment;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\User\Models\HrStaffInfoModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Modules\WorkflowManagement\Models\HcmStaffManageTypeModel;
use App\Modules\WorkflowManagement\Models\HrJobTitleModel;

class SysService extends BaseService
{
    private static $instance;

    private static $validate_condition = [
        'id' => 'Required',
    ];

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return \App\Modules\WorkflowManagement\Services\SysService
     */
    public static function getInstance(): SysService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取条件校验
     * @return array
     */
    public static function getValidateCond(): array
    {
        return self::$validate_condition;
    }

    /**
     * 获取关联审批类型
     */
    public function getWorkflowRelateType(): array
    {
	    //查询列表
        $result = ByWorkflowModel::find([
            'conditions' => 'is_view = 1 and state = 1 and type = 1',
            'columns'    => ['relate_type AS code', 'name AS name_w', 'id','t_key'],
        ])->toArray();
	    foreach ($result as $k => &$v) {
		    $v['name'] = self::$t->_($v['t_key']);
	    }
	    return $result;
    }

    /**
     * 查询指定员工
     * @param $params
     * @return mixed
     */
    public function getStaffList($params)
    {
        $condition = $params['condition'] ?? "";

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('staff_info_id as value,name as key');
        $builder->from(HrStaffInfoModel::class);
        $builder->where('state = 1 and formal = 1');

        if (!empty($condition)) {
            $builder->andWhere('name LIKE :name:', ['name' => $condition . '%']);
            $builder->orWhere("staff_info_id = :staff_info_id:", ['staff_info_id' => $condition]);
        }

        $builder->orderBy('staff_info_id DESC');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取全部角色
     * @param array $param
     * @return array
     */
    public function getRolesList(array $param = []): array
    {
        $type = $param['type'] ?? 1;

        $rpc = (new ApiClient('hris','', 'role_list'));
        $rpc->setParams(['']);
        $ret = $rpc->execute();
        $return = [];

        if (isset($ret['result']) && $ret['result']) {
            foreach ($ret['result'] as $item) {

                if ($type == WorkflowManageEnums::SCOPE_PART) { //type=1 全部数据 type=2 部分数据
                    if (!in_array($item['role_id'], RolesModel::getRolesForViewableWorkflow())) {
                        continue;
                    }
                }
                $return[] = [
                    'value' => $item['role_id'],
                    'key'   => in_array(self::$language, ['zh-CN', 'zh']) ? ($item['role_name_zh'] ?? "")
                        : (self::$language == 'en' ? $item['role_name_en'] ?? "" : $item['role_name_th'] ?? ''),
                ];
            }
        }

        return $return ?? [];
    }

    /**
     * 获取全部角色
     * @param $params
     * @return array
     */
    public function getJobTitleList($params): array
    {
        $name = $params['query'] ?? "";

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id as value, job_name as key');
        $builder->from(HrJobTitleModel::class);
        $builder->where('status = 1');

        if (!empty($name)) {
            $builder->andWhere('job_name LIKE :name:', ['name' => $name . '%']);
        }
        $builder->orderBy('id DESC');

        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取全部网点
     * @param $params
     * @return array
     */
    public function getStoreList($params): array
    {
        $name = $params['query'] ?? "";

        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id as value, name as key');
        $builder->from(SysStoreModel::class);
        $builder->where('state = 1');

        if (!empty($name)) {
            $builder->andWhere('name LIKE :name:', ['name' => $name . '%']);
        }
        $builder->orderBy('id ASC');
        $response = $builder->getQuery()->execute()->toArray();

        $headOffice = [
            'value' => Enums::HEAD_OFFICE_STORE_FLAG,
            'key'   => Enums::PAYMENT_HEADER_STORE_NAME,
        ];
        $response[] = $headOffice;

        return $response;
    }

    /**
     * 获取部门下拉项
     * @param $params
     * @return array
     */
    public function getDepartmentLevel($params = []): array
    {
        return [
            [
                'code' => 0,
                'title' => self::$t->_('principal_of_the_organisation'), // 所属组织负责人
            ],[
                'code' => 1,
                'title' => self::$t->_('principal_of_a_division'), //一级组织负责人
            ],[
                'code' => 2,
                'title' => self::$t->_('principal_of_a_department'), // '二级组织负责人',
            ],[
                'code' => 3,
                'title' => self::$t->_('principal_of_a_sub_department'),// '三级组织负责人',
            ],[
                'code' => 4,
                'title' => self::$t->_('principal_of_a_sub_department2'), // '四级组织负责人',
            ],[
                'code' => 101,
                'title' => self::$t->_('principal_of_a_bu'),//'所属BU负责人',
            ],[
                'code' => 150,
                'title' => self::$t->_('principal_of_a_clevel'),//'所属C-level负责人',
            ]
        ];
    }

    /**
     * 获取审批流异常类型
     * @return array[]
     */
    public function getWorkflowExceptionType(): array
    {
        return [
            [
                'code' => 1,
                'title' => self::$t->_('exception_type_1'),
            ],[
                'code' => 2,
                'title' => self::$t->_('exception_type_2'),
            ],[
                'code' => 3,
                'title' => self::$t->_('exception_type_3'),
            ]
        ];
    }

    /**
     * 获取审批流配置
     * @return array[]
     */
    public function getWorkflowConfigure($params): array
    {
        //获取审批流id
        $flowId = $params['id'] ?? 0;

        //获取审批流请求
        $flowInfo = ByWorkflowModel::findFirst([
            'conditions' => 'id = :flow_id: and is_view = :is_view: ',
            'bind'       => [
                'flow_id'  => $flowId,
                'is_view' => ByWorkflowModel::IS_VIEW,
            ],
        ]);
        if (!empty($flowInfo)) {
            $flowInfo = $flowInfo->toArray();
        }

        //合并配置
        $commonOvertimeConfig     = ConfigService::getInstance()->getCommonConfig();
        $individualOvertimeConfig = ConfigService::getInstance()->getIndividualConfig($flowInfo['relate_type']);
        return $this->merge_arrays($commonOvertimeConfig, $individualOvertimeConfig);
    }

    function merge_arrays($array1, $array2)
    {
        foreach ($array2 as $key => $value) {
            $key = intval($key);
            if (is_array($value) && isset($array1[$key]) && is_array($array1[$key])) {
                $array1[$key] = $this->merge_arrays($array1[$key], $value);
            } else {
                $array1[$key] = $value;
            }
        }
        return $array1;
    }

    /**
     * 获取职能分类
     * @return array[]
     */
    public function getManageType()
    {
        $data = HcmStaffManageTypeModel::find([
            'columns' => 'id, name',
        ])->toArray();

        $list = array_map(function ($v) {
            return ['key' => $v['id'], 'value' => $v['name']];
        }, $data);

        return $list ?? [];
    }

    /**
     * @description 获取全部部门
     * @return array
     */
    public function getAllDepartment(): array
    {
        $allDeptList = SysDepartmentModel::find([
            'columns' => ['id, name'],
        ])->toArray();
        return array_column($allDeptList, 'name', 'id');
    }

    /**
     * @description 获取全部大区
     * @return array
     */
    public function getAllRegion(): array
    {
        $regionList = SysManageRegionModel::find([
            'columns' => ['id, name'],
        ])->toArray();
        return array_column($regionList, 'name', 'id');
    }

    /**
     * @description 获取全部片区
     * @return array
     */
    public function getAllPiece(): array
    {
        $pieceList = SysManagePieceModel::find([
            'columns' => ['id, name'],
        ])->toArray();
        return array_column($pieceList, 'name', 'id');
    }

    /**
     * @description 获取全部职位
     * @return array
     */
    public function getAllJobTitle(): array
    {
        $jobTitleList = HrJobTitleModel::find([
            'columns' => ['id, job_name'],
        ])->toArray();
        return array_column($jobTitleList, 'job_name', 'id');
    }

    /**
     * @description 获取全部网点
     * @return array
     */
    public function getAllStore(): array
    {
        $allDeptList = SysStoreModel::find([
            'columns' => ['id, name'],
            'conditions' => 'category != 6',
        ])->toArray();
        return array_column($allDeptList, 'name', 'id');
    }

    /**
     * @description 获取停职原因
     * @return array
     */
    public function getSuspensionReason(): array
    {
        $redis    = $this->getDI()->get('redis');
        $redisKey = 'wf_branch_getSuspensionReason'.static::$language;
        $cache    = $redis->get($redisKey);
        if (!empty($cache)) {
            return json_decode($cache, true);
        }

        $ac         = new ApiClient('hcm_rpc', '', 'getSuspensionReason', self::$language);
        $api_params = [[]];
        $ac->setParams($api_params);
        $result = $ac->execute();

        $redis->setex($redisKey, 60 * 5, json_encode($result['result']['data'], JSON_UNESCAPED_UNICODE));

        return $result['result']['data'] ?? [];
    }

    /**
     * @description 获取雇佣类型枚举
     */
    public function getHireTypeEnums($filer = []): array
    {
        $model          = new SettingEnvModel();
        $hire_type_enum = $model->getValByCode('hire_type_enum');
        $hire_type_enum = explode(',', ($hire_type_enum ? $hire_type_enum : "1,2,3,4,5"));
        $return_data    = [];

        if (!empty($filer)) {
            $hire_type_enum = array_values(array_diff($hire_type_enum, $filer));
        }

        foreach ($hire_type_enum as $k => $v) {
            $return_data[$k]["value"] = intval($v);
            $return_data[$k]["key"]   = self::$t->_('hire_type_' . $v);
        }
        return array_values($return_data);
    }

    public function getRegionList($params)
    {
//        $name = $params['query'] ?? "";
//
//        $builder = $this->modelsManager->createBuilder();
//        $builder->columns('id as value, name as key');
//        $builder->from(SysManageRegionModel::class);
//        $builder->where('deleted = 0');
//
//        if (!empty($name)) {
//            $builder->andWhere('name LIKE :name:', ['name' => $name . '%']);
//        }
//        $builder->limit(10);
//        $builder->orderBy('id DESC');
//
//        return $builder->getQuery()->execute()->toArray();
        $regions = $this->getAllRegion();
        return array_map(function ($k, $v) {
            return ['key' => $k, 'value' => $v];
        }, array_values($regions), array_keys($regions));
    }

    public function getPieceList()
    {
//        $name = $params['query'] ?? "";
//
//        $builder = $this->modelsManager->createBuilder();
//        $builder->columns('id as value, name as key');
//        $builder->from(SysManagePieceModel::class);
//        $builder->where('deleted = 0');
//
//        if (!empty($name)) {
//            $builder->andWhere('name LIKE :name:', ['name' => $name . '%']);
//        }
//        $builder->limit(10);
//        $builder->orderBy('id DESC');
//
//        return $builder->getQuery()->execute()->toArray();
        $pieces = $this->getAllPiece();
        return array_map(function ($k, $v) {
            return ['key' => $k, 'value' => $v];
        }, array_values($pieces), array_keys($pieces));
    }
}
