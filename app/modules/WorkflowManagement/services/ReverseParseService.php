<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Enums;
use App\Library\Enums\WorkflowBranchOptionsCnfEnums;
use App\Library\Enums\WorkflowManageEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\AccidentReport\Models\SysDepartment;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\Hc\Models\SysManagePieceModel;
use App\Modules\Hc\Models\SysManageRegionModel;
use App\Modules\Organization\Models\SysStoreModel;
use App\Modules\WorkflowManagement\Models\HrJobTitleModel;

class ReverseParseService extends BaseService
{
    private $structure = null; //审批流结构
    private $parseData = null; //引入全局参数
    private $config = null; //审批流配置

    /**
     * @var ReverseParseService
     */
    private static $instance;

    /**
     * 单一实例
     * @return ReverseParseService
     */
    public static function getInstance(): ReverseParseService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @description 设置参数
     * @param array $params
     * @param array $config
     * @return $this
     */
    public function setParams(array $params = [], array $config = []): ReverseParseService
    {
        $this->setStructure($params);
        $this->setConfig($config);
        return $this;
    }

    /**
     * @description 获取审批流结构
     * @return null
     */
    public function getStructure()
    {
        return $this->structure;
    }

    /**
     * @description 保存审批流结构
     * @param null $structure
     */
    public function setStructure($structure): void
    {
        $this->structure = $structure;
    }

    /**
     * @description 获取数据对象
     * @return ReverseParseDataService
     */
    public function getParseData():? ReverseParseDataService
    {
        return $this->parseData;
    }

    /**
     * @description 保存数据
     * @param null $parseData
     */
    private function setParseData($parseData): void
    {
        $this->parseData = $parseData;
    }

    /**
     * @return null
     */
    public function getConfig()
    {
        return $this->config;
    }

    /**
     * @param null $config
     */
    public function setConfig($config): void
    {
        $this->config = $config;
    }

    /**
     * @description 初始化数据
     * @return $this
     */
    public function init(): ReverseParseService
    {
        if (empty($this->getParseData())) {
            $this->setParseData((new ReverseParseDataService())->init());
        }
        return $this;
    }

    /**
     * @description 反转义完整审批流
     * @return array
     * @throws ValidationException
     */
    public function reverse(): array
    {
        return $this->genWorkflow($this->getStructure());
    }

    /**
     * @description 反转义审批节点、抄送节点
     * @return array
     * @throws ValidationException
     */
    public function reverseNode(): array
    {
        return $this->genAuditForm($this->getStructure());
    }

    /**
     * @description 反转义审批条件节点
     * @return array
     * @throws ValidationException
     */
    public function reverseCondition(): array
    {
        return $this->genCondForm($this->getStructure());
    }

    public function checkInvalidCondition(): array
    {
        $branchConditions = $this->getParseData()->getBranchConditions(); //获取全部条件
        $branchConditionsName = array_column($branchConditions, 'name', 'type');
        $branchConditionsCnf = array_column($branchConditions, 'option_cnf', 'type');

        $result = [];
        foreach ($this->getStructure() as $index => $conditionGroup) { //多个条件或
            foreach ($conditionGroup as $condition) { //多个条件与
                //解析form体
                $branchType    = $condition['conditionType'];
                $operationType = $condition['operatorType'];
                $val           = $condition['val'];
                if (!isset($branchConditionsCnf[$branchType], $branchConditionsCnf[$branchType]['api_type'])) {
                    continue;
                }
                $validValue = $this->getValidValue($branchType, $operationType, $val);
                $emptyValueName = $this->doApiCheck($branchConditionsCnf[$branchType]['api_type'], $validValue);
                if (!empty($emptyValueName)) {
                    $result[] = [
                        'group_index' => $index + 1,
                        'condition_name' => $branchConditionsName[$branchType] ?? '',
                        'invalid_value' => join(',', $emptyValueName),
                    ];
                }
            }

        }
        return $result;
    }

    /**
     * 递归反解析审批流
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function genWorkflow($params): array
    {
        //节点基础结构
        $node = [
            'id'        => $params['id'],
            'name'      => $params['name'] ?? '',
            'nodeType'  => $params['nodeType'],
            'warning'   => $params['warning'] ?? '',
        ];

        if (!empty($node['nodeType']) && $node['nodeType'] == Enums::NODE_TYPE_INIT) { //申请节点
            $node['form'] = [
                'formGroups' => [],
                'sponsorType' => 0,
            ];
            $node['label'] = "";
        }

        if (!empty($node['nodeType']) && $node['nodeType'] == Enums::NODE_TYPE_FINAL) { //结束节点
            $node['form'] = [];
            $node['label'] = "";
        }

        if (!empty($node['nodeType']) && $node['nodeType'] == Enums::NODE_TYPE_COND) { //条件节点
            $node['isDefault'] = $params['isDefault'] ?? 0;
            $node['priority']  = $params['priority'] ?? '';
            [$form, $label] = $this->genCondForm($params['form']);

            $node['form'] = $form;
            $node['label'] = $label;
        }

        if (!empty($node['nodeType']) && in_array($node['nodeType'], [Enums::NODE_TYPE_AUDIT, Enums::NODE_TYPE_CC])) { //审批节点
            [$nodeForm, $nodeLabel] = $this->genAuditForm($params['form']);

            $node['form'] = $nodeForm;
            $node['label'] = $nodeLabel;
        }

        if (!empty($params['childNode'])) {
            $childTree = $this->genWorkflow($params['childNode']);
            $node['childNode'] = $childTree;
        }

        if (!empty($params['branchNode'])) {
            $branch = [];
            foreach ($params['branchNode'] as $item) {
                $branch[] = $this->genWorkflow($item);
            }
            $node['branchNode'] = $branch;
        }
        return $node;
    }

    /**
     * 反解析条件节点中的Form字段
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function genCondForm($params): array
    {
        if (empty($params) || !is_array($params)) {
            return [[], ""];
        }
        if (in_array([], $params, true)) { //存在空的条件组
            //throw new ValidationException(static::$t->_('err_msg_exist_empty_condition_group'));
            $this->logger->error(static::$t->_('err_msg_exist_empty_condition_group'));
        }

        //获取全部条件
        $branchInfo = $this->getParseData()->getBranchConditions();
        $branchInfo = array_column($branchInfo, 'name', 'type');

        //获取全部操作
        $operationInfo  = $this->getParseData()->getBranchOptions();
        $operationInfo  = array_column($operationInfo, 'operator_name', 'operator_type');
        $condGroupLabel = [];
        $cond           = [];

        foreach ($params as $key => $conditionGroup) { //多个条件或
            $condGroup    = [];
            $condGroupArr = [];

            foreach ($conditionGroup as $condition) { //多个条件与
                //解析form体
                $branchType    = $condition['conditionType'];
                $operationType = $condition['operatorType'];
                [$val, $valLabel] = $this->transferStructure([
                    'branchType'    => $branchType,
                    'operationType' => $operationType,
                    'val'           => $condition['val'],
                ]);

                //form体数据
                $condGroup[] = [
                    'conditionType' => $branchType,
                    'operatorType'  => $operationType,
                    'val'           => $val,
                ];

                //form反解析后Label
                $condGroupArr[] = sprintf("%s : %s %s", $branchInfo[$branchType] ?? '',
                    $operationInfo[$operationType] ?? '',
                    $valLabel);
            }
            $operatorFlag     = self::$t['view_and'];
            $condGroupLabel[] = join(" {$operatorFlag} ", $condGroupArr);
            $cond[$key]       = $condGroup;
        }

        if (!empty($condGroupLabel)) {
            $operatorFlag   = self::$t['view_or'];
            $condGroupLabel = join(" {$operatorFlag} ", $condGroupLabel);
        }

        return [$cond, $condGroupLabel ?? ''];
    }

    /**
     * 反解析审批人、抄送人节点中的Form字段
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function genAuditForm($params): array
    {
        if (empty($params) || !is_array($params)) {
            return [];
        }

        [$formGroup, $nodeFormLabel] = $this->reverseNodeConditions($params['formGroups']);
        [$formCc, $ccNodeFormLabel]  = $this->reverseNodeConditions($params['formCc']);

        if (!empty($ccNodeFormLabel) && !empty($nodeFormLabel)) {
            $label = sprintf("%s; CC: %s", $nodeFormLabel, $ccNodeFormLabel);
        } else if (!empty($nodeFormLabel)) {
            $label = $nodeFormLabel;
        } else {
            $label = sprintf("CC: %s", $ccNodeFormLabel);
        }

        $form = [
            'formGroups'          => $formGroup ?? [],
            'formCc'              => $formCc ?? [],
            'ccPolicy'            => $params['ccPolicy'] ?? ($params['passType'] ?? false),
            'noHanderPolicy'      => $params['noHanderPolicy'] ?? ($params['auditor_empty'] ?? ''),
            'noHanderConfig'      => $params['noHanderConfig'] ?? ($params['staffInfo'] ?? ''),
            'approvalMode'        => (string)($params['approvalMode'] ?? $params['multi_audit_typ']),
            'isTermination'       => $params['isTermination'] ?? false,
            'terminationStaffIds' => $params['terminationStaffIds'] ?? [],
            'nodeMark'            => !empty($params['nodeMark']) ? $params['nodeMark'] : Enums::NODE_MARK_APPROVAL,
            'canEditField'        => $params['canEditField'] ?? Enums\WorkflowNodeEnums::NODE_EDIT_FIELD_STATE_CAN_NOT,
            'fieldIdList'         => $params['fieldIdList'] ?? [],
        ];

        if (!empty($this->getConfig())) {
            $configs = $this->getConfig();
            $overtimeConfig = [];
            if ($configs['overtimeType'] == WorkflowManageEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE) {

                if (isset($params['isOpenOvertime'])) {
                    $overtimeConfig = ConfigService::getInstance()->generateOvertimeConfig($params);
                } else {
                    $overtimeConfig['isOpenOvertime'] = WorkflowManageEnums::WF_OVERTIME_SWITCH_CLOSED;
                }
            }
            $form = array_merge($form, $overtimeConfig);
        }

        return [$form, $label];
    }

    /**
     * 反解析
     * @param $params
     * @return array
     * @throws ValidationException
     */
    private function reverseNodeConditions($params): array
    {
        if (empty($params)) {
            return [[],[]];
        }

        $reverseResult = [];
        //获取全部申请人、表单查找逻辑
        $allCond    = $this->getParseData()->getNodeConditions();
        $allCond    = array_column($allCond, null, 'key');
        $allCondArr = array_column($allCond, 'label', 'key');

        //表单column
        $nodeFormCond = $this->getParseData()->getNodeForm();
        $totalColumns = array_merge(...array_column($nodeFormCond, 'formList'));
        $totalColumns = array_column($totalColumns, 'label', 'key');

        //获取指定角色
        $roleList = $this->getParseData()->getRolesList();

        //获取全部职能
        $manageTypeList = $this->getParseData()->getStaffManageType();

        $roleList       = array_column($roleList, 'key', 'value');
        $manageTypeList = array_column($manageTypeList, 'value', 'key');
        $allDeptList    = $this->getParseData()->getTotalDepartment();

        foreach ($params as $item) {
            $tabType      = $item['tabType'];
            $approvalType = $item['approvalType'];
            $formColumn   = $item['formColumn'];
            if (isset($allCond[$approvalType]['options'])) {
                $option = array_column($allCond[$approvalType]['options'], 'label', 'key');
            } else {
                $option = [];
            }

            switch ($approvalType) {
                case Enums::NODE_AUDITOR_TYPE_SPECIFY_STAFF: //指定员工
                    $val = array_map(function ($v) {
                        return [
                            'key' => $v['key'] ?? $v['name'],
                            'value' => $v['value'] ?? $v['staff_info_id'],
                        ];
                    }, $item['val']);
                    $staffIds = array_column($val, 'value');
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', implode(',', $staffIds));
                    break;
                case Enums::NODE_AUDITOR_TYPE_SPECIFY_SUPERIOR: //指定上级
                    $val = $item['val']['id'] ?? $item['val'];
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $option[$val] ?? "");
                    break;
                case Enums::NODE_AUDITOR_TYPE_ORG_MANAGER: //组织负责人
                    $val = $item['val'];
                    if (isset($val['setType'])) {
                        if ($val['setType'] == 1) {
                            $approvalType = Enums::NODE_AUDITOR_TYPE_ORG_MANAGER;
                        } else if ($val['setType'] == 2) {
                            $approvalType = Enums::NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR;
                        } else {
                            $approvalType = Enums::NODE_AUDITOR_TYPE_BU_CL_MGR;
                        }
                        $val = $val['val']['value'] ?? ($val['val']['id'] ?? "");
                    }
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $option[$val] ?? "");
                    break;
                case Enums::NODE_AUDITOR_TYPE_SPECIFY_ROLE: //指定角色
                    $val = $item['val'];
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $roleList[$val] ?? '');
                    break;
                case Enums::NODE_AUDITOR_TYPE_ORG_AREA_MANAGER: //组织区域负责人
                    $val = $item['val'];
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $option[$val] ?? "");
                    break;
                case Enums::NODE_AUDITOR_TYPE_SPEC_DEP_ORG_MGR://指定组织负责人
                    $val = $item['val'];
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $allDeptList[$val] ?? "");
                    break;
                case Enums::NODE_AUDITOR_TYPE_SPEC_ORG_MGR:
                    $val = $item['val'];
                    $reverseLabel = sprintf("%s: %s", $allCondArr[$approvalType] ?? '', $manageTypeList[$val]);
                    break;
                case Enums::NODE_AUDITOR_TYPE_SPECIFY_POSITION:
                    [$val, $reverseLabel] = $this->transferSpecifyPosition($item['val'], $approvalType, $option);
                    break;
                case Enums::NODE_AUDITOR_TYPE_HUB_STANDARDIZATION_AND_ADMIN:
                case Enums::NODE_AUDITOR_TYPE_HUB_AREA_MANAGER:
                case Enums::NODE_AUDITOR_TYPE_BU_CL_MGR:
                case Enums::NODE_AUDITOR_TYPE_HASH_TABLE:
                case Enums::NODE_AUDITOR_TYPE_HUB_STORE_MANAGER:
                    $val = [];
                    $reverseLabel = $allCondArr[$approvalType] ?? '';
                    break;
                default:
                    $val = [];
                    $reverseLabel = "";
                    break;
            }

            $reverseResult[] = [
                'tabType'      => $tabType,
                "approvalType" => $approvalType,
                "val"          => $val,
                "formColumn"   => $formColumn ?? '',
            ];
            if ($tabType == Enums::NODE_TYPE_FORM && isset($totalColumns[$formColumn])) {
                $reverseLabel = sprintf("[%s] %s", $totalColumns[$formColumn], $reverseLabel);
            }

            $label[] = $reverseLabel;
        }
        if (!empty($label)) {
            $label = implode(',', $label);
        }

        return [$reverseResult, $label ?? ""];
    }

    /**
     * @description 反解析具体内容
     * @param array $paramIn
     * @return array
     */
    private function transferStructure($paramIn = []): array
    {
        $branchType       = $paramIn['branchType'];
        $operationType    = $paramIn['operationType'];
        $val              = $paramIn['val'];
        $branchConditions = $this->getParseData()->getBranchConditions(); //获取全部条件
        $value            = $this->getValidValue($branchType, $operationType, $val);

        //获取转义
        $label = $this->getReverseLabel($branchType, $value, array_column($branchConditions[$branchType]['option'], 'key', 'value'));
        return [$val, $label];
    }

    /**
     * @param $branchType
     * @param $operationType
     * @param $val
     * @return array|string[]
     */
    private function getValidValue($branchType, $operationType, $val)
    {
        $branchConditions    = $this->getParseData()->getBranchConditions(); //获取全部条件
        $branchConditionsCnf = array_column($branchConditions, 'option_cnf', 'type');

        if (in_array($operationType, [Enums::OPERATE_IN, Enums::OPERATE_NOT_IN])) {
            if (isset($val[0]) && is_array($val[0]) && array_key_exists('value', $val[0])) {
                $value = array_column($val, 'value');
            } else {
                $value = $val;
            }
        } else {
            $branchCnfDetail = $branchConditionsCnf[$branchType] ?? [];
            if (isset($branchCnfDetail['api_type']) && $branchCnfDetail['api_type'] == WorkflowBranchOptionsCnfEnums::API_TYPE_STORE) {
                $value = $val['value'] ?? '';
            } else {
                if (is_array($val) && array_key_exists('key', $val)) {
                    $value = $val; //兼容处理  有可能没有 value
                } else {
                    $value = $val ?? ''; //兼容处理  有可能没有 value
                }
            }
            $value = [$value];
        }
        return $value;
    }

    /**
     * @description 获取反解析字符串
     * @param $approvalType
     * @param $items
     * @param $options
     * @return string
     */
    private function getReverseLabel($approvalType, $items, $options): string
    {
        $reverseLabel = "";

        //条件详情
        $branchCnf = $this->getParseData()->getBranchConditions();
        $branchCnf = array_column($branchCnf, 'option_cnf', 'type');

        //当前条件详情
        $currentBranchDetail = $branchCnf[$approvalType] ?? [];
        $selectType = $currentBranchDetail['access_option_method'];
        $apiType = $currentBranchDetail['api_type'] ?? 0;

        switch ($selectType) {
            case WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API:
                $reverseLabel = $this->handleApiReverse($apiType, $items);
                break;
            case WorkflowBranchOptionsCnfEnums::OPTION_TYPE_SPEC_OPTION_LIST:
                $reverseLabel = $this->handleOptionReverse($items, $options);
                break;
            case WorkflowBranchOptionsCnfEnums::OPTION_TYPE_NULL:
                $reverseLabel = $this->handleInputReverse($approvalType, $items);
                break;
            default:
                $this->logger->warning("getReverseLabel has new select_type: {$selectType}");
                break;
        }
        return $reverseLabel ? implode(',', $reverseLabel): "";
    }

    /**
     * @description 处理通过接口获取下拉选项的情况，反解析同样通过接口
     * @param $apiType
     * @param $items
     * @return array
     */
    private function handleApiReverse($apiType, $items): array
    {
        switch ($apiType) {
            case WorkflowBranchOptionsCnfEnums::API_TYPE_DEPARTMENT:
                $departmentArr = $this->getParseData()->getTotalDepartment();
                $label = array_map(function ($v) use ($departmentArr) {
                    return $departmentArr[$v] ?? "";
                }, $items);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_JOB_TITLE:
                $jobTitleArr = $this->getParseData()->getTotalJobTitle();
                $label = array_map(function ($v) use ($jobTitleArr) {
                    return $jobTitleArr[$v] ?? "";
                }, $items);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_STORE:
                $storeArr = $this->getParseData()->getTotalStore();
                $label = array_map(function ($v) use ($storeArr) {
                    return $storeArr[$v] ?? "";
                }, $items);
                if (in_array(-1, $items)) {
                    $label[] = Enums::PAYMENT_HEADER_STORE_NAME;
                }
                $label = array_values(array_filter($label));
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_ROLES:
                $roleList = $this->getParseData()->getRolesList();
                $options = array_column($roleList, 'key', 'value');
                $label = array_map(function ($v) use($options) {
                    return $options[$v] ?? "";
                }, $items);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_REGION:
                $regionList = $this->getParseData()->getTotalRegion();
                $label = array_map(function ($v) use($regionList) {
                    return $regionList[$v] ?? "";
                }, $items);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_PIECE:
                $pieceList = $this->getParseData()->getTotalPiece();
                $label = array_map(function ($v) use($pieceList) {
                    return $pieceList[$v] ?? "";
                }, $items);
                break;
            default:
                break;
        }
        return $label ?? [];
    }

    /**
     * @description 处理通过接口获取下拉选项的情况，反解析同样通过接口
     * @param $apiType
     * @param $items
     * @return array
     */
    private function doApiCheck($apiType, $items): array
    {
        switch ($apiType) {
            case WorkflowBranchOptionsCnfEnums::API_TYPE_DEPARTMENT:
                $deletedIds = SysDepartmentModel::find([
                    'conditions' => 'deleted = 1 and id in ({ids:array})',
                    'bind' => [
                        'ids' => $items,
                    ],
                    'columns' => 'id'
                ])->toArray();
                $deletedIds = array_column($deletedIds, 'id');
                $departmentArr = $this->getParseData()->getTotalDepartment();
                $label = array_map(function ($v) use ($departmentArr) {
                    return $departmentArr[$v] ?? "";
                }, $deletedIds);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_JOB_TITLE:
                $jobTitleIds = HrJobTitleModel::find([
                    'conditions' => 'status = 2 and id in ({ids:array})',
                    'bind' => [
                        'ids' => $items,
                    ],
                    'columns' => 'id'
                ])->toArray();
                $jobTitleIds = array_column($jobTitleIds, 'id');
                $jobTitleArr = $this->getParseData()->getTotalJobTitle();
                $label = array_map(function ($v) use ($jobTitleArr) {
                    return $jobTitleArr[$v] ?? "";
                }, $jobTitleIds);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_STORE:
                $storeIds = SysStoreModel::find([
                    'conditions' => 'state = 2 and id in ({ids:array})',
                    'bind' => [
                        'ids' => $items,
                    ],
                    'columns' => 'id'
                ])->toArray();
                $storeIds = array_column($storeIds, 'id');
                $storeArr = $this->getParseData()->getTotalStore();
                $label = array_map(function ($v) use ($storeArr) {
                    return $storeArr[$v] ?? "";
                }, $storeIds);
                if (in_array(-1, $storeIds)) {
                    $label[] = Enums::PAYMENT_HEADER_STORE_NAME;
                }
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_REGION:
                $regionIds = SysManageRegionModel::find([
                    'conditions' => 'deleted = 2 and id in ({ids:array})',
                    'bind' => [
                        'ids' => $items,
                    ],
                    'columns' => 'id'
                ])->toArray();
                $regionIds = array_column($regionIds, 'id');
                $regionList = $this->getParseData()->getTotalRegion();
                $label = array_map(function ($v) use($regionList) {
                    return $regionList[$v] ?? "";
                }, $regionIds);
                break;
            case WorkflowBranchOptionsCnfEnums::API_TYPE_PIECE:
                $pieceIds = SysManagePieceModel::find([
                    'conditions' => 'deleted = 2 and id in ({ids:array})',
                    'bind' => [
                        'ids' => $items,
                    ],
                    'columns' => 'id'
                ])->toArray();
                $pieceIds = array_column($pieceIds, 'id');
                $pieceList = $this->getParseData()->getTotalPiece();
                $label = array_map(function ($v) use($pieceList) {
                    return $pieceList[$v] ?? "";
                }, $pieceIds);
                break;
            default:
                break;
        }
        return $label ?? [];
    }

    /**
     * @description 处理带下拉框的反解析
     * @param $items
     * @param $options
     * @return array|string[]
     */
    private function handleOptionReverse($items, $options): array
    {
        return array_map(function ($v) use($options) {
            return $options[$v] ?? "";
        }, $items);
    }

    /**
     * @description 处理input框的反解析
     * @param $approvalType
     * @param $items
     * @return array
     */
    private function handleInputReverse($approvalType, $items): array
    {
        $allConditions = $this->getParseData()->getBranchConditions();
        $totalConditionsWithInput = BranchService::getInstance()->getTotalConditionsWithInput($allConditions);

        if (in_array($approvalType, $totalConditionsWithInput)) {
            $label = $items;
        } else {
            $this->logger->warning("handleInputReverse invalid {$approvalType}");
        }
        return $label ?? [];
    }

    /**
     * 转义职位
     * @param $params
     * @param $approvalType
     * @param $option
     * @return array
     * @throws ValidationException
     */
    private function transferSpecifyPosition($params, $approvalType, $option): array
    {
        //校验传入参数
        Validation::validate($params, [
            'val'            => 'Required|IntIn:1,2,3,4|>>>:' . self::$t->_('validation_err_msg_invalid_job_title_type'),
            'position'       => 'Required|obj|>>>:[parseJobTitle] missing parameter position',
            //'position.value' => 'Required|IntGt:0|>>>:[parseJobTitle] position id is missing',
            'department'     => 'IfIntEq:val,4|Required|IntGt:0|>>>:[parseJobTitle] department id is missing or invalid',
        ]);

        //获取审批类型
        $allCond                = $this->getParseData()->getNodeConditions();
        $allCondArr             = array_column($allCond, 'label', 'key');
        $approvalTypeTitle      = $allCondArr[$approvalType] ?? '';

        //获取部门
        $allDeptList            = $this->getParseData()->getTotalDepartment();
        $departmentId           = $params['department'] ?? 0;
        $departmentTitle        = $allDeptList[$departmentId] ?? '';

        //获取职位
        $jobTitleType           = $params['val'];
        $jobTitleName           = $params['position']['key'] ?? ($params['position']['name'] ?? '');
        $jobTitleTransferConfig = $option[$jobTitleType] ?? '';

        if (isset($params['position']['id'])) {
            $params['position']['value'] = $params['position']['id'];
            $params['position']['key'] = $params['position']['name'];
            unset($params['position']['id']);
            unset($params['position']['name']);
        }

        //职位类型为 不限 或 与申请人所在部门相同 或 与申请人在相同1级部门下时
        if (in_array($jobTitleType, [WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_NO_LIMIT,
            WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_DEPARTMENT,
            WorkflowManageEnums::SPECIFY_POSITION_QUALIFY_COND_SAME_LEVEL_DEPARTMENT
        ])) {

            $result = sprintf("%s: %s(%s)", $approvalTypeTitle, $jobTitleName, $jobTitleTransferConfig);
        } else {
            $result = sprintf("%s: %s(%s%s)", $approvalTypeTitle, $jobTitleName, $jobTitleTransferConfig, $departmentTitle);
        }
        return [$params, $result];
    }
}