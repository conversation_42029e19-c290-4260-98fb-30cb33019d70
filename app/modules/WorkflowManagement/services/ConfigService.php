<?php


namespace App\Modules\WorkflowManagement\Services;


use App\Library\Enums\WorkflowManageEnums;
use App\Library\Validation\ValidationException;
use App\Models\backyard\SettingEnvModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;

class ConfigService extends BaseService
{
    /**
     * 实例
     * @var void
     */
    private static $instance = '';

    /**
     * 单一实例
     * @return ConfigService
     */
    public static function getInstance(): ConfigService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 获取审批流
     * @param $audit_type
     * @return mixed
     * @throws ValidationException
     */
    public function getConfigWorkflow($audit_type)
    {
        $workflow = ByWorkflowModel::findFirst([
            'conditions' => "relate_type = :relate_type:",
            'bind'       => [
                "relate_type" => $audit_type,
            ],
        ]);

        if (empty($workflow) || empty($workflow->flow_request)) {
            return null;
        }

        $return = json_decode($workflow->flow_request, true);
        $error = json_last_error();
        if (!empty($error)) {
            throw new ValidationException(static::$t->_('4008') , $error);
        }

        return $return;
    }

    /**
     * 获取审批超时配置-根据超时字段超时-可选超时字段
     */
    public function getFormConfig($relate_type)
    {
        $defaultConfig = [
            1 => [
                'code'  => WorkflowManageEnums::WF_OVERTIME_TYPE_NONE,
                'title' => self::$t->_('wf_cnf_17_1'), //无超时无超时
            ],
            2 => [
                'code'  => WorkflowManageEnums::WF_OVERTIME_TYPE_OVERALL,
                'title' => self::$t->_('wf_cnf_17_2'), //统一设置整体流程超时时间
            ],
            3 => [
                'code'  => WorkflowManageEnums::WF_OVERTIME_TYPE_EACH_NODE,
                'title' => self::$t->_('wf_cnf_17_3'), //统一设置每个审批节点超时时间
            ],
            4 => [
                'code'  => WorkflowManageEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE,
                'title' => self::$t->_('wf_cnf_17_4'), //单独设置每个审批节点超时时间
            ],
        ];
        if (in_array($relate_type, [
            ByWorkflowModel::APPROVAL_TYPE_RN,
            ByWorkflowModel::APPROVAL_TYPE_CANCEL_CONTRACT,
            ByWorkflowModel::APPROVAL_TYPE_JT,
            ByWorkflowModel::APPROVAL_TYPE_JT_STAGE_TWO,
        ])) {
            //离职、转岗不可以单独配置每个节点的超时时间
            unset($defaultConfig[4]);
        }
        $defaultConfig = array_values($defaultConfig);

        if ($this->isExistOvertimeForm($relate_type)) {
            //获取可选form list
            $optionalForm = $this->getOptionalForm($relate_type);

            $defaultConfig[] = [
                'code'        => WorkflowManageEnums::WF_OVERTIME_TYPE_FORM,
                'title'       => self::$t->_('wf_cnf_17_5'), //设置表单中日期为整体流程超时时间
                'form_option' => $optionalForm,
            ];
        }
        return $defaultConfig;
    }

    /**
     * 是否存在超时字段
     * @param $relate_type
     * @return bool
     */
    public function isExistOvertimeForm($relate_type): bool
    {
        return in_array($relate_type, array_keys($this->getOptionalFormConfig()));
    }

    /**
     * 获取当前审批可选form字段
     * @param $relate_type
     * @return array
     */
    public function getOptionalForm($relate_type): array
    {
        $configList = $this->getOptionalFormConfig();
        return $configList[$relate_type] ?? [];
    }

    /**
     * 获取可选form配置
     * @return array[]
     */
    public function getOptionalFormConfig(): array
    {
        return [
            ByWorkflowModel::APPROVAL_TYPE_RN => [
                [
                    'label' => self::$t->_('form_leave_date'),
                    'value' => 'leave_date',
                ],
            ],
            ByWorkflowModel::APPROVAL_TYPE_CANCEL_CONTRACT => [
                [
                    'label' => self::$t->_('form_company_termination_contract_date'),
                    'value' => 'leave_date',
                ],
            ],
            ByWorkflowModel::APPROVAL_TYPE_JT => [
                [
                    'label' => self::$t->_('form_after_date'),
                    'value' => 'after_date',
                ],
            ],
            ByWorkflowModel::APPROVAL_TYPE_JT_STAGE_TWO => [
                [
                    'label' => self::$t->_('form_after_date'),
                    'value' => 'after_date',
                ],
            ],
            ByWorkflowModel::APPROVAL_TYPE_COMPANY_TERMINATION_CONTRACT => [
                [
                    'label' => self::$t->_('form_company_termination_contract_date'),
                    'value' => 'leave_date',
                ],
            ],
            ByWorkflowModel::APPROVAL_TYPE_IC_RENEWAL => [
                [
                    'label' => self::$t->_('form_waiting_leave_date'),
                    'value' => 'leave_date',
                ],
            ],
        ];
    }

    /**
     * 获取节点超时配置
     * @return array
     */
    public function getSingleNodeOvertimeConfig($relate_type)
    {
        if ($this->isExistOvertimeForm($relate_type)) {
            $overtimeSubType = [
                [
                    'label' => self::$t->_('config_overtime_sub_type_1'),
                    'value' => WorkflowManageEnums::WF_OVERTIME_SUB_TYPE_FORM_COLUMN,
                ],
                [
                    'label' => self::$t->_('config_overtime_sub_type_2'),
                    'value' => WorkflowManageEnums::WF_OVERTIME_SUB_TYPE_AUDIT_DAYS,
                ],
            ];
            $overtimeColumn = $this->getOptionalForm($relate_type);
        } else {
            $overtimeSubType = [
                [
                    'label' => self::$t->_('config_overtime_sub_type_2'),
                    'value' => WorkflowManageEnums::WF_OVERTIME_SUB_TYPE_AUDIT_DAYS,
                ],
            ];
            $overtimeColumn = [];
        }

        return [
            //超时类型 1=设置表单字段为超时时间，2=最大审批自然日（天数）
            'overtimeSubType' => $overtimeSubType,
            //转交逻辑 1=转交指定工号 2=转交指定2级上级
            'handoverPolicy' => [
                [
                    'label' => self::$t->_('handover_policy_3'),
                    'value' => WorkflowManageEnums::WF_HANDLE_POLICY_SPEC_STAFF,
                ],
                [
                    'label' => self::$t->_('handover_policy_6'),
                    'value' => WorkflowManageEnums::WF_HANDLE_POLICY_UPPER_MANAGER,
                ],
            ],
            //超时字段
            'overtimeColumn' => $overtimeColumn,
            //超时处理方式
            'overtimePolicy' => [
                [
                    'label' => self::$t->_('overtime_policy_1'),
                    'value' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_PASS,
                ],
                [
                    'label' => self::$t->_('overtime_policy_4'),
                    'value' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT,
                ],
                [
                    'label' => self::$t->_('overtime_policy_5'),
                    'value' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
                ],
                [
                    'label' => self::$t->_('overtime_policy_6'),
                    'value' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_HANDLE,
                ],
            ],
            //转交超时处理方式
            'handover_overtime_policy' => $this->allPolicy(),
        ];
    }

    /**
     * 获取有值的超时配置
     * @param $params
     * @return array
     */
    public function generateOvertimeConfig($params): array
    {
        $overtimeConfig = [];
        foreach ($this->getOvertimeValidateColumns() as $overtimeValidateColumn) {
            if (isset($params[$overtimeValidateColumn])) {
                $overtimeConfig[$overtimeValidateColumn] = $params[$overtimeValidateColumn];
            }
        }
        return $overtimeConfig;
    }

    //获取超时配置字段
    private function getOvertimeValidateColumns(): array
    {
        return [
            'isOpenOvertime',
            'overtimeSubType',
            'overtimeColumn',
            'overtimeDays',
            'overtimePolicy',
            'handoverPolicy',
            'handoverConfig',
            'handoverAuditDays',
            'handoverOvertimePolicy',
            'isHandoverTermination',
            'handoverTerminationStaffIds',
        ];
    }

    /**
     * 获取通用配置
     * @return array
     */
    public function getCommonConfig(): array
    {
        return [
            15 => [
                [
                    'code' => 1,
                    'title' => self::$t->_('wf_cnf_15_1'), //自动通过
                ],[
                    'code' => 2,
                    'title' => self::$t->_('wf_cnf_15_2'), //审批人处理（手动处理）
                ],
            ],
            16 => [
                [
                    'code' => 1,
                    'title' => self::$t->_('wf_cnf_16_1'), //审批人重复，后节点系统自动处理
                ],[
                    'code' => 2,
                    'title' => self::$t->_('wf_cnf_16_2'), //仅连续重复，后节点系统自动处理
                ],[
                    'code' => 3,
                    'title' => self::$t->_('wf_cnf_15_2'), //审批人处理（手动处理）
                ],
            ],
        ];
    }

    /**
     * 获取自定义配置
     * @param $relate_type
     * @return array
     */
    public function getIndividualConfig($relate_type): array
    {
        $settingEnvModel = new SettingEnvModel();
        $auditTypeList   = $settingEnvModel->getSetVal('workflow_overtime_types', ',');

        //在配置里的审批流类型，才可以配置超时
        if (!in_array($relate_type, $auditTypeList)) {
            return [];
        }
        $workflowOvertimeForm = $this->getFormConfig($relate_type);
        $overtimePolicy = $this->getOvertimePolicyConfig($relate_type);
        return [
            17 => $workflowOvertimeForm,
            18 => $overtimePolicy,
        ];
    }

    private function getOvertimePolicyConfig($relate_type)
    {
        return [
            WorkflowManageEnums::WF_OVERTIME_TYPE_NONE            => [],
            WorkflowManageEnums::WF_OVERTIME_TYPE_OVERALL         => $this->partPolicy(),
            WorkflowManageEnums::WF_OVERTIME_TYPE_EACH_NODE       => $this->allPolicy(),
            WorkflowManageEnums::WF_OVERTIME_TYPE_INDIVIDUAL_NODE => [],
            WorkflowManageEnums::WF_OVERTIME_TYPE_FORM            => $this->partPolicy(),
        ];
    }

    private function allPolicy(): array
    {
        return [
            [
                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
                'title' => self::$t->_('wf_cnf_18_5'), //超时关闭
            ],
            //超时自动审批通过逻辑过于复杂
            [
                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_PASS,
                'title' => self::$t->_('wf_cnf_18_1'), //超时自动通过
            ],
            [
                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT,
                'title' => self::$t->_('wf_cnf_18_4'), //超时自动驳回
            ],
        ];
    }

    private function partPolicy(): array
    {
        return [
            [
                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
                'title' => self::$t->_('wf_cnf_18_5'), //超时关闭
            ],
            //超时自动审批通过逻辑过于复杂
//            [
//                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_PASS,
//                'title' => self::$t->_('wf_cnf_18_1'), //超时自动通过
//            ],
            [
                'code' => WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_REJECT,
                'title' => self::$t->_('wf_cnf_18_4'), //超时自动驳回
            ],
        ];
    }
}