<?php

namespace app\modules\WorkflowManagement\services;

use App\Library\Enums\WorkflowBranchOptionsCnfEnums;

class BranchOptionsConfigService extends BaseNodeService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return BranchOptionsConfigService
     */
    public static function getInstance(): BranchOptionsConfigService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取通用下拉配置
     * @return array
     */
    public function getNormalOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type'            => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_SPEC_OPTION_LIST,
            //如通过API获取数据，接口地址
            'access_url'           => '',
        ];
    }

    /**
     * 获取数字输入框配置
     * @return array
     */
    public function getInputNumberOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type'            => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_INPUT_NUMBER,
            //获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_NULL,
            //如通过API获取数据，接口地址
            'access_url'           => '',
        ];
    }

    /**
     * 获取部门下拉配置
     * @return array
     */
    public function getDepartmentOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type' => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_CASCADER,
            //下拉框获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type' => WorkflowBranchOptionsCnfEnums::API_TYPE_DEPARTMENT,
            //如通过API获取数据，接口地址
            'access_url' => '',
        ];
    }

    /**
     * 获取职位下拉配置
     * @return array
     */
    public function getJobTitleOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type'            => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type'             => WorkflowBranchOptionsCnfEnums::API_TYPE_JOB_TITLE,
            //如通过API获取数据，接口地址
            'access_url'           => WorkflowBranchOptionsCnfEnums::ACCESS_URL_JOB_TITLE,
        ];
    }

    /**
     * 获取网点下拉配置
     * @return array
     */
    public function getStoreOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type'            => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type'             => WorkflowBranchOptionsCnfEnums::API_TYPE_STORE,
            //如通过API获取数据，接口地址
            'access_url'           => WorkflowBranchOptionsCnfEnums::ACCESS_URL_STORE,
            //设置提交参数类型，例如: 'object'等
            'submit_params_type'   => WorkflowBranchOptionsCnfEnums::SUBMIT_PARAM_TYPE_OBJECT,
        ];
    }

    /**
     * 获取员工角色下拉配置
     * @return array
     */
    public function getStaffRolesOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type'            => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type'             => WorkflowBranchOptionsCnfEnums::API_TYPE_ROLES,
            //如通过API获取数据，接口地址
            'access_url'           => WorkflowBranchOptionsCnfEnums::ACCESS_URL_ROLES,
            //设置提交参数类型
            'submit_params_type'   => WorkflowBranchOptionsCnfEnums::SUBMIT_PARAM_TYPE_OBJECT,
        ];
    }

    /**
     * 获取大区下拉配置
     * @return array
     */
    public function getRegionOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type' => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //下拉框获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type' => WorkflowBranchOptionsCnfEnums::API_TYPE_REGION,
            //如通过API获取数据，接口地址
            'access_url' => WorkflowBranchOptionsCnfEnums::ACCESS_URL_REGION,
        ];
    }

    /**
     * 获取片区下拉配置
     * @return array
     */
    public function getPieceOptionsCnf(): array
    {
        return [
            //组件类型
            'unit_type' => WorkflowBranchOptionsCnfEnums::UNIT_TYPE_SELECT,
            //下拉框获取option方式
            'access_option_method' => WorkflowBranchOptionsCnfEnums::OPTION_TYPE_API,
            //Api类型
            'api_type' => WorkflowBranchOptionsCnfEnums::API_TYPE_PIECE,
            //如通过API获取数据，接口地址
            'access_url' => WorkflowBranchOptionsCnfEnums::ACCESS_URL_PIECE,
        ];
    }
}