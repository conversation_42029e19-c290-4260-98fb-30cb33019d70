<?php

namespace App\Modules\WorkflowManagement\Services;

use App\Library\Enums;
use App\Library\Enums\WorkflowManageEnums;
use App\Library\Validation\ValidationException;
use App\Modules\Hc\Models\SysDepartmentModel;
use App\Modules\WorkflowManagement\Models\ByWorkflowModel;
use App\Modules\WorkflowManagement\Models\WorkflowPermissionModel;

class ReverseService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 获取实例
     * @return ReverseParseService
     */
    public static function getInstance(): ReverseService
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获取审批流详情
     * @param $params
     * @param $id
     * @return array
     * @throws ValidationException
     */
    public function getWorkflowDetail($params, $id): array
    {
        //获取参数
        $flowId = $params['id'];

        //获取审批流请求
        $flowInfo = ByWorkflowModel::findFirst([
            'conditions' => 'id = :flow_id: and is_view = :is_view: ',
            'bind'       => [
                'flow_id'  => $flowId,
                'is_view' => ByWorkflowModel::IS_VIEW,
            ],
        ]);
        if (!empty($flowInfo)) {
            $flowInfo = $flowInfo->toArray();
        }

        //校验权限
        $permission = WorkflowPermissionModel::findFirst([
            'conditions' => 'relate_type = :relate_type: and staff_info_id = :staff_info_id: and (is_view = 1 or is_edit = 1) and deleted = 0',
            'bind' => [
                'relate_type' => $flowInfo['relate_type'],
                'staff_info_id' => $id,
            ],
        ]);
        if (empty($permission)) {
            throw new ValidationException(self::$t->_('staff_permission_is_forbidden'));
        }

        if (!empty($flowInfo['flow_request'])) {
            $info = json_decode($flowInfo['flow_request'], true);

            //反解析基础信息
            $basicConfig = $this->genBasic($info['basicConfigure'], ['id' => $flowId]);

            //反解析审批流
            $workflowStructure = $info['workflowConfigure'];
            $workflow          = ReverseParseService::getInstance()->init()->setParams($workflowStructure,
                $basicConfig)->reverse();
            $return            = [
                'basicConfigure'    => $basicConfig,
                'workflowConfigure' => $workflow,
            ];
        } else {
            $return = '{"basicConfigure":{"relateObjectId":"%d","id":"%d","auditName":"","approvalRepeat":1,"approvalEqualApply":1,"overtimeType":1},"workflowConfigure":{"id":"e9ddb225-f11e-41f3-9f7b-0e030e336990","name":"发起人","nodeType":1,"warning":{"status":true,"msg":"请选择发起人"},"form":{"formGroups":[],"sponsorType":0},"label":"","childNode":{"id":"db6ba91e-2cf5-4cd8-8aa7-243ea4ccb354","name":"","nodeType":99,"warning":{"status":true,"msg":""},"form":[],"label":""}}}';
            $return = sprintf($return, $flowInfo['relate_type'], $flowId);
            $return = json_decode($return, true);
        }

        return $return ?? [];
    }

    /**
     * 保存节点并且返回解析
     * @param $params
     * @param $user
     * @return string
     * @throws ValidationException
     */
    public function saveNode($params, $user): string
    {
        $service = ReverseParseService::getInstance()->init()->setParams($params['form']);
        if ($params['nodeType'] == Enums::NODE_TYPE_COND) { //条件节点
            [$form, $label] = $service->reverseCondition();
        } else { //审批节点
            [$form, $label] = $service->reverseNode();
        }
        return $label;
    }

    /**
     * 反解析基础信息
     * @param $workflow
     * @param array $params
     * @return array
     */
    public function genBasic($workflow, $params = []): array
    {
        $id = $workflow['id'] ?? $params['id'];
        return [
            'relateObjectId'     => (string)$workflow['relateObjectId'],
            'auditName'          => $workflow['auditName'],
            'approvalRepeat'     => $workflow['approvalRepeat'],
            'approvalEqualApply' => $workflow['approvalEqualApply'],
            'overtimeType'       => $workflow['overtimeType'] ?? WorkflowManageEnums::WF_OVERTIME_TYPE_NONE,
            'overtimeDays'       => $workflow['overtimeDays'] ?? 1,
            'overtimePolicy'     => $workflow['overtimePolicy'] ?? WorkflowManageEnums::WF_HANDLING_AFTER_OT_AUTO_CLOSED,
            'overtimeColumn'     => $workflow['overtimeColumn'] ?? null,
            'id'                 => $id,
        ];
    }
}