<?php

namespace App\Modules\WorkflowManagement\Models;

use App\Library\BaseModel;

class WorkflowOperateLogsModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('workflow_operate_logs');
    }

    private $id;
    private $workflow_id;
    private $operator;
    private $type;
    private $request_body;
    private $before;
    private $after;
    private $created_at;
    private $updated_at;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getWorkflowId()
    {
        return $this->workflow_id;
    }

    /**
     * @param mixed $workflow_id
     */
    public function setWorkflowId($workflow_id): void
    {
        $this->workflow_id = $workflow_id;
    }

    /**
     * @return mixed
     */
    public function getOperator()
    {
        return $this->operator;
    }

    /**
     * @param mixed $operator
     */
    public function setOperator($operator): void
    {
        $this->operator = $operator;
    }

    /**
     * @return mixed
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * @param mixed $type
     */
    public function setType($type): void
    {
        $this->type = $type;
    }

    /**
     * @return mixed
     */
    public function getRequestBody()
    {
        return $this->request_body;
    }

    /**
     * @param mixed $request_body
     */
    public function setRequestBody($request_body): void
    {
        $this->request_body = $request_body;
    }

    /**
     * @return mixed
     */
    public function getBefore()
    {
        return $this->before;
    }

    /**
     * @param mixed $before
     */
    public function setBefore($before): void
    {
        $this->before = $before;
    }

    /**
     * @return mixed
     */
    public function getAfter()
    {
        return $this->after;
    }

    /**
     * @param mixed $after
     */
    public function setAfter($after): void
    {
        $this->after = $after;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }
}