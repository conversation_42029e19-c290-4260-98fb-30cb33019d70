<?php

namespace App\Modules\WorkflowManagement\Models;

use App\Library\BaseModel;

class ByWorkflowNodeCcBaseModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('workflow_node_cc_base');
    }

    private $id; //
    private $flow_id; //
    private $version; //
	private $type;
    private $node_base_id; //
    private $auditor_type; //
    private $auditor_level; //
    private $auditor_id; //
    private $approval_policy; //
    private $specify_approver; //
    private $deleted; //
    private $created_at; //
    private $updated_at; //
	
	public $type_1 = 1; //节点触发时处理
	public $type_2 = 2; //节点通过后处理
    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getFlowId()
    {
        return $this->flow_id;
    }

    /**
     * @param mixed $flow_id
     */
    public function setFlowId($flow_id): void
    {
        $this->flow_id = $flow_id;
    }

    /**
     * @return mixed
     */
    public function getCodeBaseId()
    {
        return $this->node_base_id;
    }

    /**
     * @param mixed $code
     */
    public function setCodeBaseId($node_base_id): void
    {
        $this->node_base_id = $node_base_id;
    }




    /**
     * @return mixed
     */
    public function getAuditorType()
    {
        return $this->auditor_type;
    }

    /**
     * @param mixed $auditor_type
     */
    public function setAuditorType($auditor_type): void
    {
        $this->auditor_type = $auditor_type;
    }

    /**
     * @return mixed
     */
    public function getAuditorLevel()
    {
        return $this->auditor_level;
    }

    /**
     * @param mixed $auditor_level
     */
    public function setAuditorLevel($auditor_level): void
    {
        $this->auditor_level = $auditor_level;
    }

    /**
     * @return mixed
     */
    public function getAuditorId()
    {
        return $this->auditor_id;
    }

    /**
     * @param mixed $auditor_id
     */
    public function setAuditorId($auditor_id): void
    {
        $this->auditor_id = $auditor_id;
    }

    /**
     * @return mixed
     */
    public function getApprovalPolicy()
    {
        return $this->approval_policy;
    }

    /**
     * @param mixed $approval_policy
     */
    public function setApprovalPolicy($approval_policy): void
    {
        $this->approval_policy = $approval_policy;
    }

    /**
     * @return mixed
     */
    public function getSpecifyApprover()
    {
        return $this->specify_approver;
    }

    /**
     * @param mixed $specify_approver
     */
    public function setSpecifyApprover($specify_approver): void
    {
        $this->specify_approver = $specify_approver;
    }

    /**
     * @return mixed
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function setDeleted($deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }

    /**
     * @return mixed
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param mixed $version
     */
    public function setVersion($version): void
    {
        $this->version = $version;
    }
	
	
	/**
	 * @return mixed
	 */
	public function getType()
	{
		return $this->type;
	}
	
	/**
	 * @param mixed $type
	 */
	public function setType($type): void
	{
		$this->type = $type;
	}
	
}