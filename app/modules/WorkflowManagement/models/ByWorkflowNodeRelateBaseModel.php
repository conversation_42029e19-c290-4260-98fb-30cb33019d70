<?php

namespace App\Modules\WorkflowManagement\Models;

use App\Library\BaseModel;

class ByWorkflowNodeRelateBaseModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('workflow_node_relate_base');
    }

    private $id;
    private $flow_id;
    private $version;
    private $code;
    private $from_node_id;
    private $to_node_id;
    private $valuate_formula;
    private $valuate_code;
    private $remark;
    private $sort;
    private $deleted;
    private $created_at;
    private $updated_at;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getFlowId()
    {
        return $this->flow_id;
    }

    /**
     * @param mixed $flow_id
     */
    public function setFlowId($flow_id): void
    {
        $this->flow_id = $flow_id;
    }

    /**
     * @return mixed
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @param mixed $code
     */
    public function setCode($code): void
    {
        $this->code = $code;
    }

    /**
     * @return mixed
     */
    public function getFromNodeId()
    {
        return $this->from_node_id;
    }

    /**
     * @param mixed $from_node_id
     */
    public function setFromNodeId($from_node_id): void
    {
        $this->from_node_id = $from_node_id;
    }

    /**
     * @return mixed
     */
    public function getToNodeId()
    {
        return $this->to_node_id;
    }

    /**
     * @param mixed $to_node_id
     */
    public function setToNodeId($to_node_id): void
    {
        $this->to_node_id = $to_node_id;
    }

    /**
     * @return mixed
     */
    public function getValuateFormula()
    {
        return $this->valuate_formula;
    }

    /**
     * @param mixed $valuate_formula
     */
    public function setValuateFormula($valuate_formula): void
    {
        $this->valuate_formula = $valuate_formula;
    }

    /**
     * @return mixed
     */
    public function getValuateCode()
    {
        return $this->valuate_code;
    }

    /**
     * @param mixed $valuate_code
     */
    public function setValuateCode($valuate_code): void
    {
        $this->valuate_code = $valuate_code;
    }

    /**
     * @return mixed
     */
    public function getRemark()
    {
        return $this->remark;
    }

    /**
     * @param mixed $remark
     */
    public function setRemark($remark): void
    {
        $this->remark = $remark;
    }

    /**
     * @return mixed
     */
    public function getSort()
    {
        return $this->sort;
    }

    /**
     * @param mixed $sort
     */
    public function setSort($sort): void
    {
        $this->sort = $sort;
    }

    /**
     * @return mixed
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function setDeleted($deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return mixed
     */
    public function getCreatedAt()
    {
        return $this->created_at;
    }

    /**
     * @param mixed $created_at
     */
    public function setCreatedAt($created_at): void
    {
        $this->created_at = $created_at;
    }

    /**
     * @return mixed
     */
    public function getUpdatedAt()
    {
        return $this->updated_at;
    }

    /**
     * @param mixed $updated_at
     */
    public function setUpdatedAt($updated_at): void
    {
        $this->updated_at = $updated_at;
    }


    /**
     * @return mixed
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param mixed $version
     */
    public function setVersion($version): void
    {
        $this->version = $version;
    }
}