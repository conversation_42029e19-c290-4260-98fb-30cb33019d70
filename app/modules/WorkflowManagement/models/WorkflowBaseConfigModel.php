<?php


namespace App\Modules\WorkflowManagement\Models;


use App\Library\BaseModel;

class WorkflowBaseConfigModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('workflow_basic_config');
    }

    private $id;
    private $name;
    private $workflow_relate_type_id;
    private $workflow_relate_node_id;
    private $configure_type;
    private $configure_value;
    private $extend_params;
    private $deleted;
    private $version;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name): void
    {
        $this->name = $name;
    }

    /**
     * @return mixed
     */
    public function getWorkflowRelateTypeId()
    {
        return $this->workflow_relate_type_id;
    }

    /**
     * @param mixed $workflow_relate_type_id
     */
    public function setWorkflowRelateTypeId($workflow_relate_type_id): void
    {
        $this->workflow_relate_type_id = $workflow_relate_type_id;
    }

    /**
     * @return mixed
     */
    public function getWorkflowRelateNodeId()
    {
        return $this->workflow_relate_node_id;
    }

    /**
     * @param mixed $workflow_relate_node_id
     */
    public function setWorkflowRelateNodeId($workflow_relate_node_id): void
    {
        $this->workflow_relate_node_id = $workflow_relate_node_id;
    }

    /**
     * @return mixed
     */
    public function getConfigureType()
    {
        return $this->configure_type;
    }

    /**
     * @param mixed $configure_type
     */
    public function setConfigureType($configure_type): void
    {
        $this->configure_type = $configure_type;
    }

    /**
     * @return mixed
     */
    public function getConfigureValue()
    {
        return $this->configure_value;
    }

    /**
     * @param mixed $configure_value
     */
    public function setConfigureValue($configure_value): void
    {
        $this->configure_value = $configure_value;
    }

    /**
     * @return mixed
     */
    public function getDeleted()
    {
        return $this->deleted;
    }

    /**
     * @param mixed $deleted
     */
    public function setDeleted($deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return mixed
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * @param mixed $version
     */
    public function setVersion($version): void
    {
        $this->version = $version;
    }

    /**
     * @return mixed
     */
    public function getExtendParams()
    {
        return $this->extend_params;
    }

    /**
     * @param mixed $extend_params
     */
    public function setExtendParams($extend_params): void
    {
        $this->extend_params = $extend_params;
    }
}