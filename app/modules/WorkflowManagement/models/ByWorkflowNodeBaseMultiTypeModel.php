<?php


namespace App\Modules\WorkflowManagement\Models;


use App\Library\BaseModel;

class ByWorkflowNodeBaseMultiTypeModel extends BaseModel
{
    public function initialize()
    {
        $this->setConnectionService('db_backyard');
        $this->setSource('workflow_node_base_multi_type');
    }

    private $id;

    private $node_id;

    private $auditor_type;

    private $auditor_level;

    private $auditor_id;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id): void
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getNodeId()
    {
        return $this->node_id;
    }

    /**
     * @param mixed $node_id
     */
    public function setNodeId($node_id): void
    {
        $this->node_id = $node_id;
    }

    /**
     * @return mixed
     */
    public function getAuditorType()
    {
        return $this->auditor_type;
    }

    /**
     * @param mixed $auditor_type
     */
    public function setAuditorType($auditor_type): void
    {
        $this->auditor_type = $auditor_type;
    }

    /**
     * @return mixed
     */
    public function getAuditorLevel()
    {
        return $this->auditor_level;
    }

    /**
     * @param mixed $auditor_level
     */
    public function setAuditorLevel($auditor_level): void
    {
        $this->auditor_level = $auditor_level;
    }

    /**
     * @return mixed
     */
    public function getAuditorId()
    {
        return $this->auditor_id;
    }

    /**
     * @param mixed $auditor_id
     */
    public function setAuditorId($auditor_id): void
    {
        $this->auditor_id = $auditor_id;
    }
}