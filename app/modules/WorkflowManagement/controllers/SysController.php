<?php


namespace App\Modules\WorkflowManagement\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\WorkflowManagement\Services\BranchService;
use App\Modules\WorkflowManagement\Services\ListService;
use app\modules\WorkflowManagement\services\NodeService;
use app\modules\WorkflowManagement\services\SysService;


class SysController extends BaseController
{
    /**
     * 审批类型列表
     * @Token
     */
    public function getRelateApprovalTypeListAction()
    {
        $list = SysService::getInstance()->getWorkflowRelateType();

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 指定员工查询
     * @Token
     */
    public function getStaffListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getStaffList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取全部角色
     * @Token
     */
    public function getRolesListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getRolesList($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取全部职位
     * @Token
     */
    public function getJobTitleListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getJobTitleList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取全部分支条件 & 申请人节点配置
     * @Token
     */
    public function getWorkflowBranchCondAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, SysService::getValidateCond());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = BranchService::getInstance()->getWorkflowBranchCond($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 获取审批节点可配置逻辑
     * @Token
     */
    public function getWorkflowFormCondAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, SysService::getValidateCond());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = NodeService::getInstance()->getWorkflowFormCond($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取全部网点
     * @Token
     */
    public function getStoreListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getStoreList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取指定部门级别
     * @Token
     */
    public function getDepartmentLevelAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getDepartmentLevel($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取审批流异常类型
     * @Token
     */
    public function getWorkflowExceptionTypeAction()
    {
        $list = SysService::getInstance()->getWorkflowExceptionType();

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取审批流配置
     * @Token
     */
    public function getWorkflowConfigureAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, SysService::getValidateCond());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $list = SysService::getInstance()->getWorkflowConfigure($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * 获取职能列表
     * @Token
     */
    public function getManageTypeAction()
    {
        $list = SysService::getInstance()->getManageType();

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取指定部门级别
     * @Token
     */
    public function getRegionListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getRegionList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取指定部门级别
     * @Token
     */
    public function getPieceListAction()
    {
        $params = $this->request->get();

        $list = SysService::getInstance()->getPieceList($params);

        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
}