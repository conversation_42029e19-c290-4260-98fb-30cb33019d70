<?php


namespace App\Modules\WorkflowManagement\Controllers;


use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use app\modules\WorkflowManagement\services\ListService;
use App\Modules\WorkflowManagement\Services\ParseService;
use App\Modules\WorkflowManagement\Services\ReverseService;
use App\Util\RedisKey;

class ManageController extends BaseController
{
    /**
     * 获取审批列表接口
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26518
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::getValidateList());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->getWorkflowList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取审批流设置 & 审批流 & 高级设置
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26614
     */
    public function getWorkflowAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::getValidateEdit());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ReverseService::getInstance()->getWorkflowDetail($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 编辑审批流
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/26810
     * @throws ValidationException
     * @throws \App\Library\Exception\BusinessException
     */
    public function saveWorkflowAction()
    {
        $params = $this->request->get();
	    $key  = RedisKey::WORKFLOW_MANAGEMENT_SAVE_COUNTER . $this->user['id'];
        try {
            Validation::validate($params, ListService::getValidateSave());

            //保存
            $res = $this->atomicLock(function() use ($params){
                return ParseService::getInstance()->doSaveWorkflowV2($params, $this->user['id']);
            }, $key, 5, false);

            if ($res === false) {
                return $this->returnJson(ErrCode::$FREQUENT_VISIT_ERROR, $this->t['accident_report_error_9'], '');
            }

        } catch (ValidationException $e) {
	        ParseService::getInstance()->unLock($key);
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (\Exception $ex) {
            ParseService::getInstance()->unLock($key);
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $ex->getMessage());
        }
	    
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 删除审批流
     * @throws ValidationException
     * @Token
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, ListService::getValidateEdit());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ListService::getInstance()->delWorkflowList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取员工离职异常列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/44401
     */
    public function getStaffResignNoticeListAction()
    {
        $params = $this->request->get();
        $res = ListService::getInstance()->getStaffResignNoticeList($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 保存节点
     * @Token
     */
    public function saveNodeAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, [
                'uuid' => 'Required|Str',
                'nodeType' => 'Required|Int',
            ]);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = ReverseService::getInstance()->saveNode($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }
}