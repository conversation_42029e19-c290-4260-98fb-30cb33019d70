<?php

namespace App\Modules\WorkflowManagement\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\WorkflowManagement\Services\AdminService;

class AdminController extends BaseController
{
    /**
     * 获取列表
     * @Token
     */
    public function listAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AdminService::getValidateList());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }
        $res = AdminService::getInstance()->list($params, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 添加子管理员
     * @Token
     */
    public function addAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AdminService::getValidateSave());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('workflow_management_admin_submit_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return AdminService::getInstance()->add($params, $this->user);
        }, $lock_key);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 编辑子管理员
     * @Token
     */
    public function editAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AdminService::getValidateSave());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('workflow_management_admin_edit_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return AdminService::getInstance()->edit($params, $this->user);
        }, $lock_key);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 删除子管理员
     * @Token
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AdminService::getValidateEdit());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('workflow_management_admin_delete_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return AdminService::getInstance()->delete($params, $this->user);
        }, $lock_key);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, $res['message'], $res['data']);
        }

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取子管理员权限
     * @Token
     */
    public function infoAction()
    {
        $params = $this->request->get();
        try {
            Validation::validate($params, AdminService::getValidateEdit());
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = AdminService::getInstance()->info($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取审批类型列表
     * @Token
     */
    public function getAuditListAction()
    {
        $params = $this->request->get();

        $res = AdminService::getInstance()->getAuditList($params, $this->user);

        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }
}