<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/28
 * Time: 11:22
 */

namespace App\Modules\WithholdingTax\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\WithholdingTax\Services\WithholdingTaxService;


class WithholdingTaxController extends BaseController
{
    /**
     * 新增查询--列表
     * @Permission(action='withholding_tax.add.list')
     *
     * @return mixed
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WithholdingTaxService::$validate_list_search);

        $res = WithholdingTaxService::getInstance()->getList($params, $this->user['id'],
            WithholdingTaxService::LIST_TYPE_1);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     *
     * 模版下载
     *
     * @Token
     *
     */
    public function getTemplateAction()
    {
        $res = WithholdingTaxService::getInstance()->getTemplate();
        return $this->returnJson(ErrCode::$SUCCESS, '', $res);
    }

    /**
     * 上传文件
     * @Permission(action='withholding_tax.add.upload')
     * @throws ValidationException
     */
    public function uploadAction()
    {
        // 参数校验
        $params = $this->request->get();

        // 文件格式校验
        if (!$this->request->hasFiles()) {
            throw new ValidationException($this->t->_('bank_flow_not_found_file'));
        }

        $file      = $this->request->getUploadedFiles()[0];
        $extension = $file->getExtension();
        if (!in_array($extension, ['xlsx', 'xls'])) {
            throw new ValidationException($this->t->_('bank_flow_upload_file_type_error'));
        }

        $config       = ['path' => ''];
        $excel        = new \Vtiful\Kernel\Excel($config);
        $columns_type = [
            4  => \Vtiful\Kernel\Excel::TYPE_STRING,// tax id
            20 => \Vtiful\Kernel\Excel::TYPE_STRING,// 手机号
        ];

        // 读取上传文件数据
        $excelData = $excel->openFile($file->getTempName())
            ->openSheet()
            ->setType($columns_type)
            ->getSheetData();
        // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
        if (empty($excelData) || empty($excelData[0]) || empty($excelData[2])) {
            throw new ValidationException($this->t->_('bank_flow_data_empty'));
        }

        $lock_key = md5('withholding_tax_upload_' . $this->user['id']);

        $res = $this->atomicLock(function () use ($params, $excelData) {
            return WithholdingTaxService::getInstance()->uploadData($params, $excelData, $this->user['id']);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

    /**
     * 发送短信
     * @Permission(action='withholding_tax.add.send_msg')
     */
    public function sendMsgAction()
    {
        $params = $this->request->get();

        Validation::validate($params, [
            'task_id' => 'Required|Str',
        ]);

        $res = WithholdingTaxService::getInstance()->send_msg($params['task_id']);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询--列表
     * @Permission(action='withholding_tax.list.datalist')
     */
    public function dataListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WithholdingTaxService::$validate_list_search);

        $res = WithholdingTaxService::getInstance()->getList($params, 0, WithholdingTaxService::LIST_TYPE_2);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 数据查询--导出
     * @Permission(action='withholding_tax.list.exportlist')
     */
    public function exportListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, WithholdingTaxService::$validate_list_search);

        $lock_key = md5('withholding_tax_export' . $this->user['id']);
        $res      = $this->atomicLock(function () use ($params) {
            return WithholdingTaxService::getInstance()->getList($params, 0, WithholdingTaxService::LIST_TYPE_2, true);
        }, $lock_key, 30);

        if (isset($res['code'])) {
            $code    = $res['code'];
            $message = $res['message'];
            $data    = $res['data'];
        } else {
            $code    = ErrCode::$FREQUENT_VISIT_ERROR;
            $message = $this->t['sys_processing'];
            $data    = [];
        }

        return $this->returnJson($code, $message, $data);
    }

}
