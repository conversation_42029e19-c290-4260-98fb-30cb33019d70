<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/28
 * Time: 14:20
 */

namespace App\Modules\WithholdingTax\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;

use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\EnvModel;
use App\Modules\Common\Services\CommonService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\WithholdingTax\Models\WithholdingTax;
use App\Modules\WithholdingTax\Models\WithholdingTaxTask;
use App\Modules\WithholdingTax\Models\WithholdingTaxDetail;
use Exception;
use Mpdf\Mpdf;


class WithholdingTaxService extends BaseService
{
    const LIST_TYPE_1 = 1; //新增查询列表
    const LIST_TYPE_2 = 2; //数据查询列表
    const WITHHOLDING_TAX_MAX_UPLOAD_TOTAL = 6000;
    const NO_LENGTH_MAX = 20;
    const TAX_ID_LENGTH = 13;
    const TEL_LENGTH = 10;

    const SUPPLIER_NAME_LENGTH = 255;   //supplier name
    const SUPPLIER_ADDRESS_LENGTH = 500;//地址

    const AMOUNT_LENGTH = 12;    //金额
    const TAX_LENGTH = 12;       //税
    const SUBMITTER_LENGTH = 255;//提交人
    const TYPE_PND_LENGTH = 10;  //pnd
    const WHT_TYPE = 100;
    const WITHHOLDING_EMAIL = 500;
    const UPLOAD_DATA_KEY = 'oa:withholding_tax_upload_key';
    const DOWN_LIMIT_COUNT = 40000;

    const WT_TYPE_1 = 1; //wt 类型
    const WT_TYPE_2 = 2;


    const MSG_CONTENT = 'Flash Express นำส่ง ใบหัก ณ ที่จ่าย ของคุณสามารถดาวน์โหลดที่';
    protected static $ho_arr   = ['-1', '0'];
    protected static $wt_arr   = ['1', '2', '3', '4'];
    protected static $item_tip = [
        0  => 'No.',
        2  => 'Supplier Name',
        3  => 'Supplier Address',
        4  => 'Tax ID',
        17 => 'Submitted by',
        18 => 'WT',
        19 => 'TYPE PND',
        20 => 'Tel',
        22 => 'Capital letters',
    ];

    protected static $wht1_item_tip = [
        5 => 'WHT Type',
        6 => 'Payment Date',
        7 => 'Amount',
        8 => 'Withholding Tax',
    ];

    protected static $wht2_item_tip = [
        10 => 'WHT Type',
        11 => 'Payment Date',
        12 => 'Amount',
        13 => 'Withholding Tax',
    ];

    private static $instance;

    public static $validate_list_search = [
        'create_id'        => 'StrLenGeLe:3,100|>>>:create_id param error',
        'apply_start_date' => 'date|>>>:apply_start_date param error',
        'apply_end_date'   => 'date|>>>:apply_end_date param error',
        'apply_no'         => 'StrLenGeLe:0,20|>>>:apply_no param error',
        'supplier_name'    => 'StrLenGeLe:0,255|>>>:supplier_name param error',
        'tax_code'         => 'StrLenGeLe:0,13|>>>:tax_code param error',
        'submitter'        => 'StrLenGeLe:0,255|>>>:tax_code param error',
        'page'             => 'IntGe:1|>>>:page param error',
        'page_num'         => 'IntGe:1|>>>:page_num param error',
        'contract_no'      => 'StrLenGeLe:1,20|>>>:contract_no param error',
    ];

    public static $file_header_main = [
        'no.',
        'oa number',
        'supplier name',
        'supplier address',
        'tax id',
        'wht type 1',
        'wht type 2',
        'ho',
        'title',
        'submitted by',
        'wt',
        'type pnd',
        'tel',
        'mail',
        'capital letters',
    ];

    public static $file_header_detail = [
        'wht type',
        'payment date',
        'amount',
        'withholding tax',
        'rate',
        'wht type',
        'payment date',
        'amount',
        'withholding tax',
        'rate',
    ];


    private function __construct()
    {
    }

    /**
     * @return WithholdingTaxService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 列表查询
     * */
    public function getList($condition, int $uid = 0, int $type = 0, $is_export = false)
    {
        $condition['uid']  = $uid;
        $page_size         = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num          = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset            = $page_size * ($page_num - 1);
        $condition['flag'] = $condition['flag'] ?? 0;

        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $data = [
            'items'      => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page'     => $page_size,
                'total_count'  => 0,
            ],
        ];

        try {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['a' => WithholdingTax::class]);
            if ($is_export) {
                $builder->leftjoin(WithholdingTaxDetail::class, 'a.no=b.main_no', 'b');
            }
            $builder = $this->getCondition($builder, $condition, $uid, $type);

            $builder->orderBy('a.id DESC');

            $count_info = $builder->columns('COUNT(a.id) as t_count')->getQuery()->getSingleResult();
            $count      = $count_info->t_count ?? 0;

            if ($count > 0 || $is_export) {
                if ($is_export) {
                    $builder->columns([
                        'a.id',
                        'a.no',
                        'a.oa_number',
                        'a.supplier_name',
                        'a.supplier_address',
                        'a.tax_code',
                        'a.ho',
                        'a.title',
                        'a.submitter',
                        'a.wt',
                        'a.pnd_type',
                        'a.mobile',
                        'a.email',
                        'a.is_send',
                        'a.submit_date',
                        'a.short_url',
                        'a.send_time',
                        'a.capital_letters',
                        'b.wt_type',
                        'b.tax_type',
                        'b.pay_time',
                        'b.amount',
                        'b.wt_amount',
                        'b.tax_rate',
                    ]);
                    $builder->limit(self::DOWN_LIMIT_COUNT);
                } else {
                    $builder->columns(['a.*']);
                    $builder->limit($page_size, $offset);
                }

                $items = $builder->getQuery()->execute()->toArray();
                if ($is_export) {
                    $items = $this->handleExportItems($items);
                    return [
                        'code'    => $code,
                        'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                        'data'    => $this->exportDataExcel($items ?? []),
                    ];
                }

                $items = $this->handleItems($items);
            }


            $data['items']                     = $items ?? [];
            $data['pagination']['total_count'] = (int)$count;
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('代扣税管理 - 代扣税管理列表:' . $real_message);
        }

        // 无权限的情况,返回正常结构的空数据,按成功处理
        if ($code == ErrCode::$STORE_RENTING_PAYMENT_PAY_AUTH_FAIL_ERROR) {
            $code = ErrCode::$SUCCESS;
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => $data,
        ];
    }

    public function getCondition($builder, $condition, $uid, $type)
    {
        $apply_start_date = $condition['apply_start_date'] ?? '';
        $apply_end_date   = $condition['apply_end_date'] ?? '';
        $no               = $condition['apply_no'] ?? '';
        $supplier_name    = $condition['supplier_name'] ?? '';
        $tax_code         = $condition['tax_code'] ?? '';
        $submitter        = $condition['submitter'] ?? '';
        $mobile           = $condition['mobile'] ?? '';

        if (!empty($apply_start_date)) {
            $builder->andWhere('a.submit_date >= :apply_start_date:', ['apply_start_date' => $apply_start_date]);
        }

        if (!empty($apply_end_date)) {
            $builder->andWhere('a.submit_date <= :apply_end_date:', ['apply_end_date' => $apply_end_date]);
        }
        if (!empty($no)) {
            $builder->andWhere('a.no = :no: ', ['no' => $no]);
        }

        if (!empty($supplier_name)) {
            $builder->andWhere('a.supplier_name LIKE :supplier_name: ', ['supplier_name' => "%{$supplier_name}%"]);
        }

        if (!empty($tax_code)) {
            $builder->andWhere('a.tax_code = :tax_code: ', ['tax_code' => $tax_code]);
        }

        if (!empty($submitter)) {
            $builder->andWhere('a.submitter LIKE :submitter:', ['submitter' => "%{$submitter}%"]);
        }

        if (!empty($mobile)) {
            $builder->andWhere('a.mobile = :mobile: ', ['mobile' => $mobile]);
        }

        if ($type == self::LIST_TYPE_1) {
            $builder->andWhere('a.create_id = :uid: ', ['uid' => $uid]);
        }

        return $builder;
    }

    protected function handleItems($items)
    {
        if (empty($items)) {
            return [];
        }

        $nos = array_column($items, 'no');

        $details = WithholdingTaxDetail::find([
            'conditions' => 'main_no in ({nos:array})',
            'bind'       => ['nos' => $nos],
        ])->toArray();

        foreach ($items as &$item) {
            $item['wt_type_1'] = [];
            $item['wt_type_2'] = [];
            foreach ($details as $detail) {
                if ($item['no'] == $detail['main_no']) {
                    if (self::WT_TYPE_1 == $detail['wt_type']) {
                        $item['wt_type_1'] = $detail;
                    }
                    if (self::WT_TYPE_2 == $detail['wt_type']) {
                        $item['wt_type_2'] = $detail;
                    }
                }
            }
        }

        return $items;
    }

    protected function handleExportItems($items)
    {
        if (empty($items)) {
            return [];
        }

        $new_data = [];
        foreach ($items as $item) {
            $item['send_content'] = '';
            if (!empty($item['short_url'])) {
                $item['send_content'] = WithholdingTaxService::MSG_CONTENT . $item['short_url'];
            }


            if (!isset($new_data[$item['id']])) {
                $new_data[$item['id']] = $item;
            }


            if (self::WT_TYPE_1 == $item['wt_type']) {
                $new_data[$item['id']]['wt_type_1'] = [
                    'tax_type'  => $item['tax_type'],
                    'pay_time'  => $item['pay_time'],
                    'amount'    => $item['amount'],
                    'wt_amount' => $item['wt_amount'],
                    'tax_rate'  => $item['tax_rate'],
                ];
            }


            if (self::WT_TYPE_2 == $item['wt_type']) {
                $new_data[$item['id']]['wt_type_2'] = [
                    'tax_type'  => $item['tax_type'],
                    'pay_time'  => $item['pay_time'],
                    'amount'    => $item['amount'],
                    'wt_amount' => $item['wt_amount'],
                    'tax_rate'  => $item['tax_rate'],
                ];
            }
        }
        unset($items);
        return $new_data;
    }

    public function uploadData($params, $excel_data, $uid = 0)
    {
        $affair_start = false;
        $code         = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data         = [
            'data_count'    => 0,
            'success_count' => 0,
            'fail_count'    => 0,
        ];
        $transaction  = false;

        try {
            //检查数据合法 提取数据 导入数据
            // 1. 校验文件格式
            $file_header_main   = array_shift($excel_data);
            $file_header_detail = array_shift($excel_data);

            // 处理文件行格式: 过滤空白行
            $tmp_file_data = [];
            foreach ($excel_data as $k => $row) {
                $row     = array_map('trim', $row);
                $row_str = implode('', $row);
                if (empty($row_str)) {
                    continue;
                }

                $tmp_file_data[] = $row;
            }
            $file_data = $tmp_file_data;
            unset($tmp_file_data);
            $file_row_count = count($file_data);

            $file_check_main = $this->taxFileCheck($file_header_main, self::$file_header_main);

            $file_check_detail = $this->taxFileCheck($file_header_detail, self::$file_header_detail);

            if (!$file_check_main || !$file_check_detail) {
                throw new ValidationException(self::$t->_("withholding_tax_template_error"));
            }
            // 2. 校验文件总条数
            if ($file_row_count > self::WITHHOLDING_TAX_MAX_UPLOAD_TOTAL) {
                throw new ValidationException(self::$t->_("withholding_tax_template_count_error"));
            }
            // oa number excel 内验重
            $oa_numbers = array_filter(array_column($file_data, '1'));

            if (count($oa_numbers) != count(array_unique($oa_numbers))) {
                throw new ValidationException(self::$t->_('withholding_tax_oa_number_repeat_error'));
            }


            //单号重复校验
            $order_nos = array_column($file_data, '0');

            if (count($order_nos) != count(array_unique($order_nos))) {
                throw new ValidationException(self::$t->_("withholding_tax_template_no_repeat_error"));
            }

            //单号查询校验

            $no_res = WithholdingTax::find([
                'conditions' => 'no in ({nos:array})',
                'bind'       => ['nos' => $order_nos],
                'columns'    => 'id',
            ]);
            if (!empty($no_res->toArray())) {
                throw new ValidationException(self::$t->_("withholding_tax_template_no_repeat_error_2"));//已发短信
            }

            $insert_main_data   = [];
            $insert_detail_data = [];
            $no_pass_data       = [];
            //校验数据
            foreach ($file_data as $key => $value) {
                $flag = false;
                $msg  = '';
                if (empty($value[5]) && empty($value[10])) {
                    throw new ValidationException("WHT Type 1 and WHT Type 2 is empty");
                }

                //非空判断
                foreach (self::$item_tip as $k => $v) {
                    if (empty($value[$k])) {
                        $flag = true;
                        $msg  .= $v . 'error ;';
                    }
                }
                //非空
                if (!empty($value[5])) {
                    foreach (self::$wht1_item_tip as $k => $v) {
                        if (empty($value[$k])) {
                            $flag = true;
                            $msg  .= $v . 'error ;';
                        }
                    }
                }

                if (!empty($value[10])) {
                    foreach (self::$wht2_item_tip as $k => $v) {
                        if (empty($value[$k])) {
                            $flag = true;
                            $msg  .= $v . 'error ;';
                        }
                    }
                }

                if (!empty($value[16]) && (strlen($value[16]) > self::SUBMITTER_LENGTH)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_title_length_error') . ';';
                }

                if (!empty($value[21]) && (strlen($value[21]) > self::WITHHOLDING_EMAIL)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_email_length_error') . ';';
                }

                if (!empty($value[9]) && (strlen($value[9]) > 5)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_rate_length_error') . ';';
                }
                if (!empty($value[14]) && (strlen($value[14]) > 5)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_rate_length_error') . ';';
                }
                //no长度
                if (strlen($value[0]) > self::NO_LENGTH_MAX) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_no_length_error') . ';';
                }
                if (!empty($value[1]) && (strlen($value[1]) > self::NO_LENGTH_MAX)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_oa_number_length_error') . ';';
                }

                if (strlen($value[2]) > self::SUPPLIER_NAME_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('supplier_name_length_error') . ';';
                }

                if (strlen($value[3]) > self::SUPPLIER_ADDRESS_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('supplier_address_length_error') . ';';
                }
                if (!empty($value[5]) && strlen($value[5]) > self::WHT_TYPE) {
                    $flag = true;
                    $msg  .= static::$t->_('wht_type_length_error') . ';';
                }

                if (!empty($value[10]) && strlen($value[10]) > self::WHT_TYPE) {
                    $flag = true;
                    $msg  .= static::$t->_('wht_type_length_error') . ';';
                }

                if (strlen($value[7]) > self::AMOUNT_LENGTH || strlen($value[12]) > self::AMOUNT_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('amount_length_error') . ';';
                }

                //验证是否为数字,避免带逗号的字符入库成错误的金额
                if (!empty($value[7]) && !is_numeric($value[7])) {
                    $flag = true;
                    $msg  .= static::$t->_('amount_format_error') . ';';
                }
                if (!empty($value[8]) && !is_numeric($value[8])) {
                    $flag = true;
                    $msg  .= static::$t->_('amount_format_error') . ';';
                }
                if (!empty($value[12]) && !is_numeric($value[12])) {
                    $flag = true;
                    $msg  .= static::$t->_('amount_format_error') . ';';
                }
                if (!empty($value[13]) && !is_numeric($value[13])) {
                    $flag = true;
                    $msg  .= static::$t->_('amount_format_error') . ';';
                }

                if (!empty($value[8])) {
                    $value[8] = sprintf("%.2f", $value[8]);
                }
                if (!empty($value[13])) {
                    $value[13] = sprintf("%.2f", $value[13]);
                }

                if (strlen($value[8]) > self::TAX_LENGTH || strlen($value[13]) > self::TAX_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_tax_length_error') . ';';
                }


                if (strlen($value[17]) > self::SUBMITTER_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('submitter_length_error') . ';';
                }

                if (strlen($value[19]) > self::TYPE_PND_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('pnd_length_error') . ';';
                }

                //tax id 13 位
                if (strlen($value[4]) != self::TAX_ID_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_tax_id_length_error') . ';';
                }
                //ho -1或0
                if (!in_array($value[15], self::$ho_arr, true)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_ho_error') . ';';
                }
                //wt 1,2,3,4
                if (!in_array($value[18], self::$wt_arr, true)) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_wt_error') . ';';
                }

                //tel 10 位
                if (strlen($value[20]) != self::TEL_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_mobile_length_error') . ';';
                }

                if (!is_numeric($value[20])) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_mobile_type_error') . ';';
                }
                if (strlen($value[22]) > self::SUPPLIER_ADDRESS_LENGTH) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_capital_letters_type_error') . ';';
                }

                $dmy_rule = self::DATE_FORMAT;

                if (!empty($value[6]) && !preg_match($dmy_rule, $value[6])) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_payment_date_error') . ';';
                }
                if (!empty($value[11]) && !preg_match($dmy_rule, $value[11])) {
                    $flag = true;
                    $msg  .= static::$t->_('withholding_payment_date_error') . ';';
                }


                if ($flag) {
                    $value[23] = $msg;
                }

                //入库数据提取
                if (!$flag) {
                    $insert_main_data[] = [
                        'no'               => $value[0],
                        'oa_number'        => $value[1],
                        'supplier_name'    => $value[2],
                        'supplier_address' => $value[3],
                        'tax_code'         => $value[4],
                        'ho'               => $value[15],
                        'title'            => $value[16],
                        'submitter'        => $value[17],
                        'wt'               => $value[18],
                        'pnd_type'         => $value[19],
                        'mobile'           => $value[20],
                        'email'            => $value[21],
                        'capital_letters'  => $value[22],
                        'is_send'          => 0,
                        'submit_date'      => date('Y-m-d'),
                        'create_id'        => $uid,
                        'created_at'       => date('Y-m-d H:i:s'),
                        'updated_at'       => date('Y-m-d H:i:s'),
                    ];


                    if (!empty($value[5])) {
                        $insert_detail_data[] = [
                            'main_no'    => $value[0],
                            'wt_type'    => 1,
                            'tax_type'   => $value[5],
                            'pay_time'   => $value[6],
                            'amount'     => round($value[7], 2),
                            'wt_amount'  => round($value[8], 2),
                            'tax_rate'   => $value[9],
                            'created_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                    if (!empty($value[10])) {
                        $insert_detail_data[] = [
                            'main_no'    => $value[0],
                            'wt_type'    => 2,
                            'tax_type'   => $value[10],
                            'pay_time'   => $value[11],
                            'amount'     => round($value[12], 2),
                            'wt_amount'  => round($value[13], 2),
                            'tax_rate'   => $value[14],
                            'created_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                }
                $file_data[$key] = $value;
            }

            //  数据入库

            if ($insert_main_data && count($file_data) == count($insert_main_data)) {
                $redis                      = $this->getDI()->get("redis");
                $withholding_tax_upload_key = self::UPLOAD_DATA_KEY . $uid . time();
                $redis->setex($withholding_tax_upload_key, 3600,
                    json_encode(['main_data' => $insert_main_data, 'detail_data' => $insert_detail_data]));

                $message = 'success';
            } else {
                $code     = ErrCode::$BUSINESS_ERROR;
                $fail_url = $this->exportFailExcel($file_data);
            }
        } catch (ValidationException $e) {
            $code           = ErrCode::$VALIDATE_ERROR;
            $message        = $e->getMessage();
            $file_row_count = 0;
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('代扣税上传 - 上传异常: ' . $real_message);
        }
        $success_count  = 0;
        $fail_count     = 0;
        $file_row_count = $file_row_count ?? 0;
        if ($file_row_count > 0) {
            $fail_count    = $file_row_count - count($insert_main_data);
            $success_count = count($insert_main_data);
        }

        $data = [
            'data_count'    => $file_row_count,
            'success_count' => $success_count,
            'fail_count'    => $fail_count ?? 0,
            'fail_url'      => $fail_url ?? '',
            'task_id'       => $withholding_tax_upload_key ?? '',
        ];

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }


    protected function taxFileCheck(array $file_header, array $file_tpl_header)
    {
        if (empty($file_tpl_header)) {
            return false;
        }
        $file_header     = array_map(function ($v) {
            return strtolower(trim($v));
        }, $file_header);
        $file_header     = implode('', $file_header);
        $file_tpl_header = implode('', $file_tpl_header);

        return $file_header == $file_tpl_header;
    }

    public function exportFailExcel($rows)
    {
        $file_name = 'withholding_tax_fail_list_' . date('YmdHis"');

        $header = [
            'No.',
            'OA Number',
            'Supplier Name',
            'Supplier Address',
            'Tax ID',
            'WHT Type 1',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'WHT Type 2',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'HO',
            'Title',
            'Submitted by',
            'WT',
            'TYPE PND',
            'Tel',
            'Mail',
            'Capital letters',
            'Fail Msg',
        ];
        array_unshift($rows, [
            '',
            '',
            '',
            '',
            '',
            'WHT Type 1',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'WHT Type 2',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
        ]);

        return $this->exportNewExcel($header, $rows, $file_name);
    }

    public function send_msg($withholding_tax_upload_key)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            $redis = $this->getDI()->get("redis");

            $data = $redis->get($withholding_tax_upload_key);
            if (empty($data)) {
                throw new ValidationException('data error[key:' . $withholding_tax_upload_key . ']',
                    ErrCode::$VALIDATE_ERROR);
            }

            $data = json_decode($data, true);

            if (!empty($data) && !empty($data['main_data']) && !empty($data['detail_data'])) {
                $withholding_tax_task_model = new WithholdingTaxTask();

                $task = $withholding_tax_task_model->save([
                    'status'     => 0,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ]);
                if ($task === false) {
                    throw new BusinessException('代扣税上传 - task表写入失败 - ' . json_encode($data, JSON_UNESCAPED_UNICODE),
                        ErrCode::$SYSTEM_ERROR);
                }

                foreach ($data['main_data'] as &$main_tax) {
                    $main_tax['task_id'] = $withholding_tax_task_model->id;
                }

                $withholding_tax_model = new WithholdingTax();
                if ($withholding_tax_model->batch_insert($data['main_data']) === false) {
                    throw new BusinessException('代扣税上传 - 主表批量写入失败 - ' . json_encode($data['main_data'],
                            JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
                }

                $withholding_tax_detail_model = new WithholdingTaxDetail();

                if ($withholding_tax_detail_model->batch_insert($data['detail_data']) === false) {
                    throw new BusinessException('代扣税上传 - 副表批量写入失败 - ' . json_encode($data['detail_data'],
                            JSON_UNESCAPED_UNICODE), ErrCode::$SYSTEM_ERROR);
                }

                $redis->del($withholding_tax_upload_key);

                $db->commit();
            }
        } catch (ValidationException $e) {
            $code    = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (Exception $e) {
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('withholding-tax-send-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => [],
        ];
    }

    /**
     * 生成pdf
     *
     * @param $data
     * @return mixed|string
     */
    public function createPdf($data)
    {
        $oss_html_path = EnumsService::getInstance()->getSettingEnvValue('withholding_tax_pdf_template');

        // 整理变量数据
        $post_data = [
            'pdfName'      => time(),
            'templateUrl'  => $oss_html_path,
            'data'         => $this->getParamsFromContract($data),
            'downLoadData' => [],
            "pdfOptions"   => [
                "displayHeaderFooter" => true,
                "footerTemplate"      => '',
                "headerTemplate"      => '',
            ],
        ];

        // 生成pdf文件
        $data = CommonService::getInstance()->newPostRequest(env('pdf_rpc_endpoint',
                '') . '/api/pdf/createPdfByJvppeteer', json_encode($post_data));

        $this->logger->info("调用pdf生成接口返回数据 =>" . json_encode($data, JSON_UNESCAPED_UNICODE));
        if ($data['code'] == ErrCode::$SUCCESS) {
            $data = $data['data'] ?? [];
        }

        return $data['object_url'] ?? '';
    }

    public function getParamsFromContract($data)
    {
        $data['pay_time_1']  = $data['detail'][0]['pay_time'] ?? '';
        $data['pay_time_2']  = $data['detail'][1]['pay_time'] ?? '';
        $data['pay_time']    = empty($data['pay_time_1']) ? $data['pay_time_2'] : $data['pay_time_1'];
        $data['tax_type_2']  = $data['detail'][1]['tax_type'] ?? '';
        $data['amount_1']    = $data['detail'][0]['amount'] ?? '';
        $data['wt_amount_1'] = $data['detail'][0]['wt_amount'] ?? '';
        $data['amount_2']    = $data['detail'][1]['amount'] ?? '';
        $data['wt_amount_2'] = $data['detail'][1]['wt_amount'] ?? '';
        return $data;
    }

    public function getTemplate()
    {
        $url = EnvModel::getEnvByCode('withholding_tax_template');
        return $url ?? '';
    }

    /**
     * @param $header
     * @param $rows
     * @param string $fileName
     * @return array
     * @throws Exception\BusinessException
     *
     */
    public function exportNewExcel($header, $rows, $fileName = 'excel.xlsx')
    {
        $fileName = 'excel.xlsx';
        if (!strstr($fileName, '.xlsx')) {
            $fileName = $fileName . '.xlsx';
        }
        $config = [
            'path' => sys_get_temp_dir(),
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        //合并单元格
        $fileObject = $excel->fileName($fileName)
            ->mergeCells('A1:A2', 'Merge cells')
            ->mergeCells('B1:B2', 'Merge cells')
            ->mergeCells('C1:C2', 'Merge cells')
            ->mergeCells('D1:D2', 'Merge cells')
            ->mergeCells('E1:E2', 'Merge cells')
            ->mergeCells('F1:J1', 'Merge cells')
            ->mergeCells('K1:O1', 'Merge cells')
            ->mergeCells('P1:P2', 'Merge cells')
            ->mergeCells('Q1:Q2', 'Merge cells')
            ->mergeCells('R1:R2', 'Merge cells')
            ->mergeCells('S1:S2', 'Merge cells')
            ->mergeCells('T1:T2', 'Merge cells')
            ->mergeCells('U1:U2', 'Merge cells')
            ->mergeCells('V1:V2', 'Merge cells')
            ->mergeCells('W1:W2', 'Merge cells')
            ->mergeCells('X1:X2', 'Merge cells');


        $fileHandle = $fileObject->getHandle();
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $alignStyle = $format
            ->bold()
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->fontColor(0x000000)
            ->toResource();
        $filePath   = $fileObject->header($header)->data($rows)->setRow('A1:X1', 21, $alignStyle)->output();
        $file       = OssHelper::uploadFile($filePath);
        return $file['object_url'];
    }

    /**
     * 代扣税导出表头样式
     *
     * @param $header
     * @param $rows
     * @param string $fileName
     * @return array
     * @throws Exception\BusinessException
     *
     */
    public function exportWithExcel($header, $rows, $fileName = 'excel.xlsx')
    {
        $fileName = 'excel.xlsx';
        if (!strstr($fileName, '.xlsx')) {
            $fileName = $fileName . '.xlsx';
        }
        $config = [
            'path' => sys_get_temp_dir(),
        ];
        $excel  = new \Vtiful\Kernel\Excel($config);
        // 此处会自动创建一个工作表
        //合并单元格
        $fileObject = $excel->fileName($fileName)
            ->mergeCells('A1:A2', 'Merge cells')
            ->mergeCells('B1:B2', 'Merge cells')
            ->mergeCells('C1:C2', 'Merge cells')
            ->mergeCells('D1:D2', 'Merge cells')
            ->mergeCells('E1:E2', 'Merge cells')
            ->mergeCells('F1:J1', 'Merge cells')
            ->mergeCells('K1:O1', 'Merge cells')
            ->mergeCells('P1:P2', 'Merge cells')
            ->mergeCells('Q1:Q2', 'Merge cells')
            ->mergeCells('R1:R2', 'Merge cells')
            ->mergeCells('S1:S2', 'Merge cells')
            ->mergeCells('T1:T2', 'Merge cells')
            ->mergeCells('U1:U2', 'Merge cells')
            ->mergeCells('V1:V2', 'Merge cells')
            ->mergeCells('W1:W2', 'Merge cells')
            ->mergeCells('X1:X2', 'Merge cells')
            ->mergeCells('Y1:Y2', 'Merge cells');


        $fileHandle = $fileObject->getHandle();
        $format     = new \Vtiful\Kernel\Format($fileHandle);
        $alignStyle = $format
            ->bold()
            ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
            ->fontColor(0x000000)
            ->toResource();
        $filePath   = $fileObject->header($header)->data($rows)->setRow('A1:Y1', 24, $alignStyle)->output();
        $file       = OssHelper::uploadFile($filePath);
        return $file['object_url'];
    }

    public function exportDataExcel($data)
    {
        $file_name = 'withholding_tax_data_list_' . date('YmdHis"');
        $rows      = [];
        foreach ($data as $key => $value) {
            $rows[] = [
                $value['no'],
                $value['oa_number'],
                $value['supplier_name'],
                $value['supplier_address'],
                $value['tax_code'],
                $value['wt_type_1']['tax_type'] ?? '',
                $value['wt_type_1']['pay_time'] ?? '',
                $value['wt_type_1']['amount'] ?? '',
                $value['wt_type_1']['wt_amount'] ?? '',
                $value['wt_type_1']['tax_rate'] ?? '',
                $value['wt_type_2']['tax_type'] ?? '',
                $value['wt_type_2']['pay_time'] ?? '',
                $value['wt_type_2']['amount'] ?? '',
                $value['wt_type_2']['wt_amount'] ?? '',
                $value['wt_type_2']['tax_rate'] ?? '',
                $value['ho'],
                $value['title'],
                $value['submitter'],
                $value['wt'],
                $value['pnd_type'],
                $value['mobile'],
                $value['email'],
                $value['capital_letters'],
                $value['send_time'],
                $value['send_content'],

            ];
        }

        $header = [
            'No.',
            'OA Number',
            'Supplier Name',
            'Supplier Address',
            'Tax ID',
            'WHT Type 1',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'WHT Type 2',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'HO',
            'Title',
            'Submitted by',
            'WT',
            'TYPE PND',
            'Tel',
            'Mail',
            'Capital letters',
            'Send time',
            'Send content',
        ];
        array_unshift($rows, [
            '',
            '',
            '',
            '',
            '',
            'WHT Type 1',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
            'WHT Type 2',
            'Payment Date',
            'Amount',
            'Withholding Tax',
            'Rate',
        ]);
        return $this->exportWithExcel($header, $rows, $file_name);
    }


}