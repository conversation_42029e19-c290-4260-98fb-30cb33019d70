<?php
/**
 * Created by PhpStorm.
 * Date: 2021/10/28
 * Time: 11:11
 */

namespace App\Modules\WithholdingTax\Models;

use App\Models\Base;

class WithholdingTaxDetail extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('withholding_tax_detail');
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }
}