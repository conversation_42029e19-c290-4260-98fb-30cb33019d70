<?php
/**
 * Created by PhpStorm.
 * Date: 2021/4/27
 * Time: 19:31
 */

namespace App\Modules\OrdinaryPayment\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Contract\Services\BaseService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentFlowService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentListService;
use App\Modules\OrdinaryPayment\Services\OrdinaryPaymentDetailService;
use App\Modules\User\Services\UserService;
use App\Modules\Workflow\Services\FYRService;

class ConsultationController extends BaseController
{
    /**
     * 待回复征询列表
     * @Token
     */
    public function listAction()
    {
        try {
            $params = $this->request->get();
            Validation::validate($params, OrdinaryPaymentListService::$validate_reply_list);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $list   = OrdinaryPaymentListService::getInstance()->getList($params, $this->user, OrdinaryPaymentListService::LIST_TYPE_CONSULTED_REPLY);
        if ($list['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $list['data']);
        }

        return $this->returnJson($list['code'], $list['message']);
    }

    /**
     *回复详情
     * @Token
     */
    public function detailAction()
    {
        try {
            $data = $this->request->get();
            Validation::validate($data, BaseService::$validate_detail);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }


        $res = OrdinaryPaymentDetailService::getInstance()->getAuditDetail($data['id'], $this->user['id']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson(ErrCode::$SUCCESS, '', $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 回复征询
     *
     * @Token
     */
    public function replyAction()
    {
        $ask_id      = $this->request->get('ask_id', 'int');
        $note        = $this->request->get('note', 'trim');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $ask_id,
            'note' => $note,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->replyValidation($check_data);

        $result = FYRService::getInstance()->createReply($ask_id, $note, $this->user, $attachments);
        return $this->returnJson($result['code'], $result['message']);
    }

    /**
     * 征询
     * @Token
     */
    public function askAction()
    {
        $biz_id      = $this->request->get('id', 'int');
        $note        = $this->request->get('note', 'trim');
        $to_staffs    = $this->request->get('to_staff');
        $attachments = $this->request->get('attachments');

        // 执行参数校验
        $check_data = [
            'id' => $biz_id,
            'note' => $note,
            'to_staff' => $to_staffs,
            'attachments' => $attachments
        ];
        FYRService::getInstance()->askValidation($check_data);

        $request  = OrdinaryPaymentFlowService::getInstance()->getRequest($biz_id);
        $to_staffs = (new UserService())->getUserInfos($to_staffs);
        $result = FYRService::getInstance()->createAsk($request, $note, $to_staffs, $this->user, $attachments);

        return $this->returnJson($result['code'], $result['message']);
    }
}