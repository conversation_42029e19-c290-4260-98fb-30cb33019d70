<?php

namespace App\Modules\OrdinaryPayment\Models;

use App\Models\Base;

class OrdinaryPaymentDetail extends Base
{
    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('ordinary_payment_detail');
        $this->hasOne(
            'ordinary_payment_id',
            OrdinaryPayment::class,
            'id',
            [
                "alias" => "OrdinaryPayment",
            ]
        );
    }





    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * @param null $parameters
     * @return mixed
     */
    public static function find($parameters = null)
    {
        return parent::find($parameters);
    }


}
