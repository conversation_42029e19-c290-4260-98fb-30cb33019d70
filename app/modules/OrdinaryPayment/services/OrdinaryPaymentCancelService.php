<?php

namespace App\Modules\OrdinaryPayment\Services;

class OrdinaryPaymentCancelService extends BaseService
{

    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
}
