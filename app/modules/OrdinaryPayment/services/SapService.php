<?php
/**
 * Created by PhpStorm.
 * Date: 2021/9/15
 * Time: 14:35
 */

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Modules\Purchase\Services\SapsService;
use App\Modules\Reimbursement\Models\RequestSapLog;
use App\Modules\Reimbursement\Services\SapService as reSapService;
use Exception;

class SapService extends BaseService
{
    const PND3 =1;
    const PND53=2;
    private static $instance;
    private static $apiPaths;
    private static $user;
    private static $passWord;



    private function __construct(){
        self::$apiPaths = env('sap_interface_url','https://my602255.sapbyd.cn/sap/bc/srt/scs');
        self::$user = env('sap_user_id_2','_BYDHOST');
        self::$passWord = env('sap_pwd_2','Welcome1');
    }
    /**
     * @return  \App\Modules\OrdinaryPayment\Services\SapService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }
    /**
     * 发送请
     * @param String $method
     * @param Array $postData
     * @return Array
     */
    public function httpRequestXml($method, $postData)
    {
        $responseText = '';

        $curl = curl_init();
        try {
            $this->logger->info(['sap_http_xml_request_params' => [
                'method' => $method,
                'post_data' => $postData
            ]]);

            $header[] = "Content-type: text/xml";
            $basic_key = self::$user . ":" . self::$passWord;
            $header[] = "Authorization: Basic " . base64_encode($basic_key); //添加头，在name和pass处填写对应账号密码
            curl_setopt($curl, CURLOPT_URL, self::$apiPaths . $method);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 180);
            curl_setopt($curl, CURLOPT_TIMEOUT, 600);
            $responseText = curl_exec($curl);

            $this->logger->info(['sap_http_xml_response_result' => $responseText]);

            $curl_errno = curl_errno($curl);
            if (!empty($curl_errno)) {
                throw new Exception('curl exec errno=' . $curl_errno . '; error=' . curl_error($curl), ErrCode::$SYSTEM_ERROR);
            }

        } catch (Exception $e) {
            $this->getDI()->get('logger')->warning(['sap-postRequest-failed' => $e->getMessage()]);
        }

        curl_close($curl);

        return $responseText;
    }

    /**
     * 普通付款同步sap
     *
     * @param $data
     * @return array|mixed
     */
    public function ordinary_payment_to_sap($data)
    {
        $item_xml = '';
        $i = 0;

        foreach ($data['items'] as $key => $item) {
            if (substr($item['ledger_account_name'], 0, 1) != 6) {//首字母含6pccode 为空
                $item['cost_center_name'] = '';
                $acc_code = '<AccountingCodingBlockTypeCode>ACC</AccountingCodingBlockTypeCode>';
            } else {
                $acc_code = '<AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>';
            }

            $with_tax = '';
            if (GlobalEnums::PH_COUNTRY_CODE == get_country_code() && !empty($item['wht_tax_code'])) {
                $with_tax = '<WithholdingTaxationCharacteristicsCode listID="PH" listAgencyID="http://**********-one-off.sap.com/YCB5ESA8Y_">' . $item['wht_tax_code'] . '</WithholdingTaxationCharacteristicsCode>';
            } else {
                $with_tax = '<WithholdingTaxationCharacteristicsCode listID="' . get_country_code() . '">' . $item['wht_tax_code'] . '</WithholdingTaxationCharacteristicsCode>';
            }

            $i++;
            $item_id = 10 * $i;
            $item_xml .= '<Item actionCode="01">
                <ItemID>' . $item_id . '</ItemID>
               <BusinessTransactionDocumentItemTypeCode>' . $item['item_type_code'] . '</BusinessTransactionDocumentItemTypeCode>
               <!--数量:-->
               <Quantity unitCode="EA">1</Quantity>
               <!--单位代码:-->
               <QuantityTypeCode>EA</QuantityTypeCode>
              <!--服务描述:-->
               <SHORT_Description languageCode="EN">' . $item['voucher_description'] . '</SHORT_Description>
                 <!--Optional:-->
               <Product actionCode="01"> 
               <!--现金折扣指标:-->
                   <CashDiscountDeductibleIndicator>false</CashDiscountDeductibleIndicator>
                  <!--Optional:-->
                  <ProductCategoryIDKey>
                     <!--产品类别:-->
                     <ProductCategoryInternalID>' . $item['finance_category_id'] . '</ProductCategoryInternalID>
                  </ProductCategoryIDKey>      
                  <!--1物料，2服务-->
                  <ProductKey>
                     <ProductTypeCode>1</ProductTypeCode>
                  </ProductKey>
               </Product>
               <!--净价:-->
               <NetUnitPrice>
                  <Amount currencyCode="' . $data['currency'] . '">' . $item['amount_no_tax'] . '</Amount>
                  <BaseQuantity>1.0</BaseQuantity>
                  <!--Optional:-->
                  <BaseQuantityTypeCode>EA</BaseQuantityTypeCode>
               </NetUnitPrice>
               <AccountingCodingBlockDistribution ActionCode="01" AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true">
                  <AccountingCodingBlockAssignment ActionCode="01">'
                . $acc_code .
                '<GeneralLedgerAccountAliasCode>' . $item['ledger_account_name'] . '</GeneralLedgerAccountAliasCode>
                     <CostCentreID>' . $item['cost_center_name'] . '</CostCentreID>
                  </AccountingCodingBlockAssignment>
               </AccountingCodingBlockDistribution>
               <!--产品税:-->
               <ProductTax actionCode="01">
                  <!--税务明细-税务代码:-->
                  <ProductTaxationCharacteristicsCode listID="' . get_country_code() . '">' . $item['vat_code'] . '</ProductTaxationCharacteristicsCode>
                  <!--代扣代缴税说明-代扣代缴税代码:-->'
                . $with_tax .
                '<!--Optional:-->
                  <CountryCode>' . get_country_code() . '</CountryCode>
               </ProductTax>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">
   <soapenv:Header/>
   <soapenv:Body>
      <glob:SupplierInvoiceBundleMaintainRequest_sync>
         <BasicMessageHeader/>
         <SupplierInvoice actionCode="01" itemListCompleteTransmissionIndicator="true">
            <BusinessTransactionDocumentTypeCode>004</BusinessTransactionDocumentTypeCode>
            <!--发票描述:-->
            <MEDIUM_Name>' . $data['order_no'] . '</MEDIUM_Name>
            <!--发票日期:-->
            <Date>' . $data['ticket_date'] . '</Date>
            <!--接收日期:-->
            <ReceiptDate>' . $data['apply_date'] . '</ReceiptDate>
            <!--过账日期:-->
            <TransactionDate>' . $data['send_time'] . '</TransactionDate>
            <!--Optional:-->
            <DocumentItemGrossAmountIndicator>false</DocumentItemGrossAmountIndicator>
            <!--Optional:-->
            <ManualEntryERSIndicator>false</ManualEntryERSIndicator>
            <!--总金额:-->
            <GrossAmount currencyCode="' . $data['currency'] . '">' . $data['amount_total_have_tax'] . '</GrossAmount>
            <!--总税额:-->
            <TaxAmount currencyCode="' . $data['currency'] . '">' . $data['amount_total_vat'] . '</TaxAmount>
            <Status>
               <!--状态是3，单据没有问题才会自动过账:-->
               <DataEntryProcessingStatusCode>3</DataEntryProcessingStatusCode>
            </Status>
            <!--外部参考号:-->
            <CustomerInvoiceReference actionCode="01">
               <BusinessTransactionDocumentReference>
                  <ID>' . $data['order_no'] . '</ID>
                  <!--固定值:-->
                  <TypeCode>28</TypeCode>
               </BusinessTransactionDocumentReference>
            </CustomerInvoiceReference>
            <!--买方公司:-->
            <BuyerParty actionCode="01">
               <!--Optional:-->
               <PartyKey>
                  <!--固定值:-->
                  <PartyTypeCode>200</PartyTypeCode>
                  <!--Optional:-->
                  <PartyID>' . $data['cost_company_id'] . '</PartyID>
               </PartyKey>
            </BuyerParty>
            <!--供应商:-->
            <SellerParty actionCode="01">
               <PartyKey>
                  <!--固定值:-->
                  <PartyTypeCode>147</PartyTypeCode>
                  <!--Optional:-->
                  <PartyID>' . $data['sap_supplier_no'] . '</PartyID>
               </PartyKey>
            </SellerParty>'
            . $item_xml .
            '</SupplierInvoice>
      </glob:SupplierInvoiceBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        $this->logger->info(['ordinary-payment-sap-post-data' => $post_xml]);
        $return_xml = $this->httpRequestXml('/sap/managesupplierinvoicein', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein
        $this->logger->info(['ordinary-payment-sap-return-data' => $return_xml]);

        preg_match_all("/\<SupplierInvoice\>(.*?)\<\/SupplierInvoice\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];

        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);

        $return_arr['log'] = [];
        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();
        $sap_log = [
            'uuid' => $return_arr['UUID'] ?? '',
            'order_code' => $data['order_no'],
            'type' => 2,
            'request_data' => $post_xml,
            'response_data' => $return_xml ?? '',
            'create_at' => date('Y-m-d H:i:s')
        ];
        if ($logModel->save($sap_log) === false) {
            $this->logger->warning(['sap_log_save_error' => get_data_object_error_msg($logModel), 'sap_log_data' => $sap_log]);
        }

        return $return_arr;
    }

    /**
     * 泰国普通付款同步sap
     * */
    public function  ordinary_payment_to_th_sap($data, $ledger_info,$wht_type_arr)
    {
        try {
            if (empty($data)) {
                return [];
            }


            $is_has_vat    = false;
            $is_has_wht    = false;

            $deductible_tax_amount = 0;

            foreach ($data['items'] as $k => $v) {
                $v['tax_not_diff']          = bcdiv((bcmul($v['amount_no_tax'],100) - bcmul($v['amount_wht'],100)), 100, 2);//不含税金额 减 WHT税额
                $v['payable_amount']        = bcdiv((bcmul($v['amount_have_tax'],100) - bcmul($v['amount_wht'],100)), 100, 2);//含税金额-wht
                $v['vat_tax_diff']          = bcdiv((bcmul($v['amount_vat'],100) - $v['deductible_tax_amount']), 100, 2);//vat-可抵扣
                $v['ledger_account'] = $ledger_info[$v['ledger_account_id']];
                $v['wht_type_name']  = $wht_type_arr[$v['wht_category']]['name'] ?? '';
                $deductible_tax_amount +=  $v['deductible_tax_amount'];
                $v['voucher_description'] =str_replace('&','',$v['voucher_description']);

                if ($v['deductible_tax_amount'] > 0) {
                    $is_has_vat = true;
                }
                if ($v['amount_wht'] > 0) {
                    $is_has_wht = true;
                }
                if(substr($v['ledger_account'], 0, 1 )!=6){//首字母含6pccode 为空
                    $v['cost_center_name'] = '';
                }
                $data['items'][$k] = $v;
            }
            $data['deductible_tax_amount'] =bcdiv($deductible_tax_amount,100,2);
            if ($is_has_vat && !$is_has_wht) {//1

                return $this->sap_scene_one($data);

            } else if (!$is_has_vat && !$is_has_wht) {//2

                return $this->sap_scene_two($data);

            } else if (!$is_has_vat && $is_has_wht) {//3

                return $this->sap_scene_three($data);

            } else if ($is_has_vat && $is_has_wht) {//4

                return $this->sap_scene_four($data);
            }

            return [];

        } catch (Exception $e) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('reimbursement_sap_service_exception: ' . $e->getMessage());
            return [];
        }


    }
    /**
     * 场景1 有vat无wht
     * */
    public function sap_scene_one($data)
    {
        $item     = '';
        $tax_item = '';
        $i        = -2;
        foreach ($data['items'] as $k => $v) {
            $i = $i + 3;
            $tax = '';
            if($v['deductible_tax_amount']!=0){
                $tax ='<Tax>				
		    <TaxCountryCode>'.get_country_code().'</TaxCountryCode>			
		    <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">Z10</ProductTaxationCharacteristicsCode>			
		    <TaxJurisdictionCode></TaxJurisdictionCode>			
		   </Tax>';
            }
            $item     .= '<Item>
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['tax_not_diff'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
                <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>'
             .$tax.
            '</Item>
           <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 1) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['vat_tax_diff'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
			   <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>					
            </Item>     
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 2) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
     
			    <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>					
            </Item>';

        }

        $tax_item = '<TaxItem>	
   <ObjectNodeSenderTechnicalID>1</ObjectNodeSenderTechnicalID>	
   <OriginalEntryDocumentItemReferenceItemID>' . ($i + 3) . '</OriginalEntryDocumentItemReferenceItemID>	
   <TaxCountryCode>'.get_country_code().'</TaxCountryCode>	
   <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
   <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">Z10</ProductTaxationCharacteristicsCode>	
   <TaxJurisdictionCode></TaxJurisdictionCode>	
   <BusinessTransactionCurrencyAmount currencyCode="'.$data['currency'].'">' . $data['deductible_tax_amount'] . '</BusinessTransactionCurrencyAmount>	
  </TaxItem>';
        $postXml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">		
   <soapenv:Header/>					
   <soapenv:Body>					
      <glob:JournalEntryBundleCreateRequest>					
         <BasicMessageHeader>					
         </BasicMessageHeader>					
         <!-- 维护日记账分录 -->					
         <JournalEntry>					
            <!-- 公司代码 泰国公司固定FEX01 -->					
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>					
            <!-- 任意值，原始单据编号  传OA系统原始单据号，供后续查询分析使用 -->					
            <OriginalEntryDocumentReferenceID>' . $data['order_no'] . '</OriginalEntryDocumentReferenceID>					
            <!-- 固定值，通信系统 -->					
            <OriginalEntryDocumentReferenceBusinessSystemID>BYDHOST</OriginalEntryDocumentReferenceBusinessSystemID>					
            <!-- 过账日期 -->					
            <AccountingBusinessTransactionDate>' . $data['apply_date'] . '</AccountingBusinessTransactionDate>					
            <!-- 固定值，原始单据类型 -->					
            <AccountingBusinessTransactionTypeCode>701</AccountingBusinessTransactionTypeCode>					
             <!--  输入额外参考信息 -->					
            <OriginalEntryDocumentExternalID>' . $data['extra_message'] . '</OriginalEntryDocumentExternalID>					
            <!-- 固定值，日记账分录类型 -->					
            <AccountingDocumentTypeCode>00107</AccountingDocumentTypeCode>					
             <!-- 输入凭证摘要 -->					
		 <Note>' . $data['note'] . '</Note>' . $item . $tax_item .
            '</JournalEntry>					
      </glob:JournalEntryBundleCreateRequest>					
   </soapenv:Body>					
</soapenv:Envelope>';

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managejournalentryin', $postXml);

        preg_match_all("/\<JournalEntryCreateConfirmationBundleJournalEntry\>(.*?)\<\/JournalEntryCreateConfirmationBundleJournalEntry\>/s", $xml, $re_data);

        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_data['UUID'] ?? '','order_code'=>$data['order_no'], 'type' => 2, 'request_data' => $postXml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

        return $return_data;
    }

    /**
     * 场景2 无vat无wht
     * */
    public function sap_scene_two($data)
    {
        $item = '';
        $i    = -1;
        foreach ($data['items'] as $k => $v) {
            $i    = $i + 2;
            $item .= '<Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
			   <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>					
            </Item>
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 1) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
         
            <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>					
            </Item>';
        }
        $item .= '';


        $postXml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">		
   <soapenv:Header/>					
   <soapenv:Body>					
      <glob:JournalEntryBundleCreateRequest>					
         <BasicMessageHeader>					
         </BasicMessageHeader>					
         <!-- 维护日记账分录 -->					
         <JournalEntry>					
            <!-- 公司代码 泰国公司固定FEX01 -->					
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>					
            <!-- 任意值，原始单据编号  传OA系统原始单据号，供后续查询分析使用 -->					
            <OriginalEntryDocumentReferenceID>' . $data['order_no'] . '</OriginalEntryDocumentReferenceID>					
            <!-- 固定值，通信系统 -->					
            <OriginalEntryDocumentReferenceBusinessSystemID>BYDHOST</OriginalEntryDocumentReferenceBusinessSystemID>					
            <!-- 过账日期 -->					
            <AccountingBusinessTransactionDate>' . $data['apply_date'] . '</AccountingBusinessTransactionDate>					
            <!-- 固定值，原始单据类型 -->					
            <AccountingBusinessTransactionTypeCode>701</AccountingBusinessTransactionTypeCode>					
             <!--  输入额外参考信息 -->					
            <OriginalEntryDocumentExternalID>' . $data['extra_message'] . '</OriginalEntryDocumentExternalID>					
            <!-- 固定值，日记账分录类型 -->					
            <AccountingDocumentTypeCode>00107</AccountingDocumentTypeCode>					
             <!-- 输入凭证摘要 -->					
		 <Note>' . $data['note'] . '</Note>' . $item .
            '</JournalEntry>					
      </glob:JournalEntryBundleCreateRequest>					
   </soapenv:Body>					
</soapenv:Envelope>';

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managejournalentryin', $postXml);

        preg_match_all("/\<JournalEntryCreateConfirmationBundleJournalEntry\>(.*?)\<\/JournalEntryCreateConfirmationBundleJournalEntry\>/s", $xml, $re_data);


        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }

        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }
        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_data['UUID'] ?? '','order_code'=>$data['order_no'], 'type' => 2, 'request_data' => $postXml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

        return $return_data;

    }
    /**
     * 场景3 无vat有wht
     * */
    public function sap_scene_three($data)
    {
        $item = '';
        $i    = -3;
        foreach ($data['items'] as $k => $v) {
            $i    = $i + 4;
            $item .= '<Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
                <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description']), 0, 40)) . '</Note>					
            </Item>
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 1) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['amount_wht'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>					
            </Item>       
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 2) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
     
			    <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description']), 0, 40)) . '</Note>					
            </Item> 
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 3) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['amount_wht'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>					
            </Item>';
        }


        $postXml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">					
   <soapenv:Header/>					
   <soapenv:Body>					
      <glob:JournalEntryBundleCreateRequest>					
         <BasicMessageHeader>					
         </BasicMessageHeader>					
         <!-- 维护日记账分录 -->					
         <JournalEntry>					
            <!-- 公司代码 泰国公司固定FEX01 -->					
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>					
            <!-- 任意值，原始单据编号  传OA系统原始单据号，供后续查询分析使用 -->					
            <OriginalEntryDocumentReferenceID>' . $data['order_no'] . '</OriginalEntryDocumentReferenceID>					
            <!-- 固定值，通信系统 -->					
            <OriginalEntryDocumentReferenceBusinessSystemID>BYDHOST</OriginalEntryDocumentReferenceBusinessSystemID>					
            <!-- 过账日期 -->					
            <AccountingBusinessTransactionDate>' . $data['apply_date'] . '</AccountingBusinessTransactionDate>					
            <!-- 固定值，原始单据类型 -->					
            <AccountingBusinessTransactionTypeCode>701</AccountingBusinessTransactionTypeCode>					
             <!--  输入额外参考信息 -->					
            <OriginalEntryDocumentExternalID>' . $data['extra_message'] . '</OriginalEntryDocumentExternalID>					
            <!-- 固定值，日记账分录类型 -->					
            <AccountingDocumentTypeCode>00107</AccountingDocumentTypeCode>					
             <!-- 输入凭证摘要 -->					
		 <Note>' . $data['note'] . '</Note>' . $item .
            '</JournalEntry>					
      </glob:JournalEntryBundleCreateRequest>					
   </soapenv:Body>					
</soapenv:Envelope>';

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managejournalentryin', $postXml);
        preg_match_all("/\<JournalEntryCreateConfirmationBundleJournalEntry\>(.*?)\<\/JournalEntryCreateConfirmationBundleJournalEntry\>/s", $xml, $re_data);

        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }

        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }
        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_data['UUID'] ?? '','order_code'=>$data['order_no'], 'type' => 2, 'request_data' => $postXml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

        return $return_data;
    }
    /**
     * 场景1 有vat有wht
     * */
    public function sap_scene_four($data)
    {
        $item     = '';
        $tax_item = '';
        $i        = -4;
        foreach ($data['items'] as $k => $v) {

            $i = $i + 5;
            $tax = '';
            if($v['deductible_tax_amount']!=0){
                $tax ='<Tax>				
		    <TaxCountryCode>'.get_country_code().'</TaxCountryCode>			
		    <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">Z10</ProductTaxationCharacteristicsCode>			
		    <TaxJurisdictionCode></TaxJurisdictionCode>			
		   </Tax>';
            }
            $item     .= '<Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . $i . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['tax_not_diff'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
                <!--文本描述-->					
               <Note>' . $v['voucher_description'] . '</Note>'
            	.$tax.
            '</Item>
             <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 1) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['vat_tax_diff'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description']), 0, 40)) . '</Note>					
            </Item>
           <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 2) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>' . $v['ledger_account'] . '</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . $v['amount_wht'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
               <AccountingCodingBlock>					
                  <!--Optional:-->					
                  <CostCentreID>' . $v['cost_center_name'] . '</CostCentreID>					
			</AccountingCodingBlock>
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>'
                .$tax.
                '</Item>                    
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 3) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['payable_amount'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
     
			    <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description']), 0, 40)) . '</Note>					
            </Item>
            <Item>					
            	<OriginalEntryDocumentItemReferenceItemID>' . ($i + 4) . '</OriginalEntryDocumentItemReferenceItemID>				
               <!--总账科目：******** - Bank Clearing account -->					
               <GeneralLedgerAccountAliasCode>********</GeneralLedgerAccountAliasCode>					
               <!--贷方金额（负数）-->					
               <BusinessTransactionCurrencyAmount' . ' currencyCode="' . $data['currency'] . '">' . -$v['amount_wht'] . '</BusinessTransactionCurrencyAmount>      
               <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
			   <!--文本描述-->					
               <Note>' . trim(substr(($v['voucher_description'] . '+' . $v['wht_type_name']), 0, 40)) . '</Note>					
            </Item>';
        }
        $tax_item = '<TaxItem>	
   <ObjectNodeSenderTechnicalID>1</ObjectNodeSenderTechnicalID>	
   <OriginalEntryDocumentItemReferenceItemID>' . ($i + 5) . '</OriginalEntryDocumentItemReferenceItemID>	
   <TaxCountryCode>'.get_country_code().'</TaxCountryCode>	
   <CurrencyCode>' . $data['currency'] . '</CurrencyCode>			
   <ProductTaxationCharacteristicsCode listID="'.get_country_code().'">Z10</ProductTaxationCharacteristicsCode>	
   <TaxJurisdictionCode></TaxJurisdictionCode>	
   <BusinessTransactionCurrencyAmount currencyCode="' . $data['currency'] . '">' . $data['deductible_tax_amount'] . '</BusinessTransactionCurrencyAmount>	
  </TaxItem>';

        $postXml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">					
   <soapenv:Header/>					
   <soapenv:Body>					
      <glob:JournalEntryBundleCreateRequest>					
         <BasicMessageHeader>					
         </BasicMessageHeader>					
         <!-- 维护日记账分录 -->					
         <JournalEntry>					
            <!-- 公司代码 泰国公司固定FEX01 -->					
            <CompanyID>' . $data['cost_company_id'] . '</CompanyID>					
            <!-- 任意值，原始单据编号  传OA系统原始单据号，供后续查询分析使用 -->					
            <OriginalEntryDocumentReferenceID>' . $data['order_no'] . '</OriginalEntryDocumentReferenceID>					
            <!-- 固定值，通信系统 -->					
            <OriginalEntryDocumentReferenceBusinessSystemID>BYDHOST</OriginalEntryDocumentReferenceBusinessSystemID>					
            <!-- 过账日期 -->					
            <AccountingBusinessTransactionDate>' . $data['apply_date'] . '</AccountingBusinessTransactionDate>					
            <!-- 固定值，原始单据类型 -->					
            <AccountingBusinessTransactionTypeCode>701</AccountingBusinessTransactionTypeCode>					
             <!--  输入额外参考信息 -->					
            <OriginalEntryDocumentExternalID>' . $data['extra_message'] . '</OriginalEntryDocumentExternalID>					
            <!-- 固定值，日记账分录类型 -->					
            <AccountingDocumentTypeCode>00107</AccountingDocumentTypeCode>					
             <!-- 输入凭证摘要 -->					
		 <Note>' . $data['note'] . '</Note>' . $item . $tax_item .
            '</JournalEntry>					
      </glob:JournalEntryBundleCreateRequest>					
   </soapenv:Body>					
</soapenv:Envelope>';

        $xml = SapsService::getInstance()->httpRequestXml($method = '/sap/managejournalentryin', $postXml);

        preg_match_all("/\<JournalEntryCreateConfirmationBundleJournalEntry\>(.*?)\<\/JournalEntryCreateConfirmationBundleJournalEntry\>/s", $xml, $re_data);

        $return_data = [];
        if (isset($re_data[0][0]) && !empty($re_data[0][0])) {
            $return_data = SapsService::getInstance()->xmlToArray($re_data[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $xml, $log);
        $return_data['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_data['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();

        $logModel->save(['uuid' => $return_data['UUID'] ?? '','order_code'=>$data['order_no'],'order_code'=>$data['order_no'], 'type' => 2, 'request_data' => $postXml, 'response_data' => $xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);

        return $return_data;

    }

    /**
     * 新版泰国普通付款
     * 需求https://l8bx01gcjr.feishu.cn/docs/doccnepmDrez6tyVpKM9TdukQCf#
     * */

    public function ordinary_payment_th_to_sap($data)
    {
        $item_xml = '';
        $i = 0;
        $tax_amount = 0;
        foreach ($data['items'] as $key => $item) {
            $tax_amount += $item['deductible_tax_amount'];
            if (substr($item['ledger_account_name'], 0, 1) != 6) {//首字母含6pccode 为空
                $item['cost_center_name'] = '';
                $acc_code = '<AccountingCodingBlockTypeCode>ACC</AccountingCodingBlockTypeCode>';
            } else {
                $acc_code = '<AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>';
            }

            $item_xml .= '<Item actionCode="01">
            	 <!--类型:002 发票项目-->
               <BusinessTransactionDocumentItemTypeCode>002</BusinessTransactionDocumentItemTypeCode>
               <!--数量:-->
               <Quantity unitCode="EA">1</Quantity>
               <!--单位代码:-->
               <QuantityTypeCode>EA</QuantityTypeCode>
               <!--描述:-->
               <SHORT_Description languageCode="ZH">' . $item['voucher_description'] . '</SHORT_Description>
              <NetAmount currencyCode="THB">' . $item['amount_no_tax'] . '</NetAmount>
               <!--净价:-->
               <NetUnitPrice>
                  <Amount currencyCode="THB">' . $item['amount_no_tax'] . '</Amount>
                   <!--默认为1:-->
                  <BaseQuantity>1.0</BaseQuantity>
                  <BaseQuantityTypeCode>EA</BaseQuantityTypeCode>
               </NetUnitPrice>
               <AccountingCodingBlockDistribution ActionCode="01" AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true">
                  <AccountingCodingBlockAssignment ActionCode="01">
                     <!--账户分配类型:ACC - 分配科目-->
                     ' . $acc_code . '
                     <!--总账科目-->
                     <GeneralLedgerAccountAliasCode>' . $item['ledger_account_name'] . '</GeneralLedgerAccountAliasCode>
                      <!--成本中心-->
                     <CostCentreID>' . $item['cost_center_name'] . '</CostCentreID>
                  </AccountingCodingBlockAssignment>
               </AccountingCodingBlockDistribution>
               <!--产品税:-->
               <ProductTax actionCode="01">
                  <!--税务明细-税务代码:-->
                  <ProductTaxationCharacteristicsCode listID="TH">' . $item['vat_code'] . '</ProductTaxationCharacteristicsCode>
                  <!--代扣代缴税说明-代扣代缴税代码:-->
                <WithholdingTaxationCharacteristicsCode listID="TH">' . $item['wht_code'] . '</WithholdingTaxationCharacteristicsCode>
                  <CountryCode>TH</CountryCode>
               </ProductTax>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">	
   <soapenv:Header/>	
   <soapenv:Body>	
      <glob:SupplierInvoiceBundleMaintainRequest_sync>	
         <BasicMessageHeader>	
         </BasicMessageHeader>
        <SupplierInvoice actionCode="01" itemListCompleteTransmissionIndicator="true">	
            <BusinessTransactionDocumentTypeCode>004</BusinessTransactionDocumentTypeCode>	
            <!--发票描述:-->	
            <MEDIUM_Name>' . $data['order_no'] . '</MEDIUM_Name>	
            <!--发票日期:-->	
            <Date>' . $data['ticket_date'] . '</Date>	
            <!--接收日期:-->	
            <ReceiptDate>' . $data['apply_date'] . '</ReceiptDate>	
            <!--过账日期:-->	
            <TransactionDate>' . $data['send_time'] . '</TransactionDate>	
            <!--Optional:-->	
            <DocumentItemGrossAmountIndicator>false</DocumentItemGrossAmountIndicator>	
            <!--Optional:-->	
            <ManualEntryERSIndicator>false</ManualEntryERSIndicator>	
            <!--总金额:-->	
            <GrossAmount currencyCode="THB">' . $data['amount_total_have_tax'] . '</GrossAmount>	
            <!--总税额:-->	
            <TaxAmount currencyCode="THB">' . $data['amount_total_vat'] . '</TaxAmount>	
            <Status>	
               <!--状态: 3是过账 不传值准备状态 -->	
               <DataEntryProcessingStatusCode>3</DataEntryProcessingStatusCode>	
            </Status>	
            <!--外部参考号:-->	
            <CustomerInvoiceReference actionCode="01">	
               <BusinessTransactionDocumentReference>	
                  <ID>' . $data['order_no'] . '</ID>	
                  <!--固定值:-->	
                  <TypeCode>28</TypeCode>	
               </BusinessTransactionDocumentReference>	
            </CustomerInvoiceReference>	
            <!--买方公司:-->	
            <BuyerParty actionCode="01">	
               <!--Optional:-->	
               <PartyKey>	
                  <!--固定值:-->	
                  <PartyTypeCode>200</PartyTypeCode>	
                  <!--Optional:-->	
                  <PartyID>' . $data['cost_company_id'] . '</PartyID>	
               </PartyKey>	
            </BuyerParty>	
            <!--供应商:-->	
            <SellerParty actionCode="01">	
               <PartyKey>	
                  <!--固定值:-->	
                  <PartyTypeCode>147</PartyTypeCode>	
                  <!--Optional:-->	
                  <PartyID>' . $data['sap_supplier_no'] . '</PartyID>	
               </PartyKey>	
            </SellerParty>	
            <!--统驭科目-->	
            <ReconciliationAccount></ReconciliationAccount>'
            . $item_xml .
            '</SupplierInvoice>
      </glob:SupplierInvoiceBundleMaintainRequest_sync>
   </soapenv:Body>
</soapenv:Envelope>';

        $return_xml = $this->httpRequestXml('/sap/managesupplierinvoicein', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein

        $this->logger->info(['ordinary-payment-sap-return-data' => $return_xml]);

        preg_match_all("/\<SupplierInvoice\>(.*?)\<\/SupplierInvoice\>/s", $return_xml, $SiteLogisticsTask);
        $return_arr = [];

        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);
        $return_arr['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();
        $logModel->save(['uuid' => $return_arr['UUID'] ?? '', 'order_code' => $data['order_no'], 'type' => 2, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }


    /**
     * 泰国税率
     * 代扣代缴税代码
     * */
    protected function th_wht_tax_code($wht_cate,$wht_rate)
    {
        $wht_tax_code = '';
        switch ($wht_rate) {
            case 1:
                if (self::PND3 == $wht_cate) {
                    $wht_tax_code = '018';

                } else if (self::PND53 == $wht_cate) {
                    $wht_tax_code = '059';

                }

                break;
            case 2:

                if (self::PND3 == $wht_cate) {
                    $wht_tax_code = '030';

                } else if (self::PND53 == $wht_cate) {
                    $wht_tax_code = '071';

                }
                break;

            case 3:

                if (self::PND3 == $wht_cate) {
                    $wht_tax_code = '019';

                } else if (self::PND53 == $wht_cate) {
                    $wht_tax_code = '060';

                }
                break;
            case 5:
                if (self::PND3 == $wht_cate) {
                    $wht_tax_code = '027';

                } else if (self::PND53 == $wht_cate) {
                    $wht_tax_code = '068';

                }
                break;

            case 15:
                if (self::PND53 == $wht_cate) {
                    $wht_tax_code = '062';

                }
                break;
            case 25:
                break;
            default:
                $wht_tax_code = '';


        }

        return $wht_tax_code;
    }

    /**
     * 泰国
     * vat 税率转换
     * */
    protected function vat_code_th($vat_rate,$deductible_vat_tax)
    {
        switch ($vat_rate) {
            case 0:
                $vate_code = '001';
                break;
            case 7:
                if (0 == $deductible_vat_tax) {
                    $vate_code = '012';

                } elseif('8.82'== $deductible_vat_tax) {
                    $vate_code = 'Z20';
                }

                break;
            default:
                $vate_code = '';
        }
        return $vate_code;

    }

    public function vat_code($vat_rate,$deductible_tax_amount){
        $vat_code = '';
        switch (get_country_code()) {
            case 'TH':
                $vat_code= $this->vat_code_th($vat_rate,$deductible_tax_amount);

                break;
            case 'PH':
                $vat_code = $this->vat_code_ph($vat_rate);

                break;
            case 'MY':
                $vat_code= reSapService::getInstance()->vat_code_my($vat_rate);

                break;
            default;

        }

        return $vat_code;
    }

    public function wht_code($wht_cate,$wht_rate){
        $wht_tax_code = '';
        switch (get_country_code()) {
            case 'TH':
                $wht_tax_code= $this->th_wht_tax_code($wht_cate,$wht_rate);

                break;
            case 'PH':
                $wht_tax_code = $this->wht_tax_code($wht_rate);

                break;
            case 'MY':

                break;
            default;

        }
        return $wht_tax_code;
    }


    /**
     * 菲律宾
     * vat 税率转换
     * */
    protected function vat_code_ph($vat_rate)
    {
        switch ($vat_rate) {
            case 0:
                $vate_code = '012';
                break;
            case 12:
                $vate_code = '010';

                break;
            default:
                $vate_code = '';
        }
        return $vate_code;

    }

    /**
     * 菲律宾税率
     * 代扣代缴税代码
     * */
    protected function wht_tax_code($wht_rate)
    {
        switch ($wht_rate) {
            case 1:
                $vate_tax_code = '6200';
                break;
            case 2:
                $vate_tax_code = '6100';
                break;
            case 5:
                $vate_tax_code = '6300';
                break;
            case 10:
                $vate_tax_code = '6500';
                break;
            case 15:
                $vate_tax_code = '6600';
                break;
            case 25:
                $vate_tax_code = '6400';
                break;
            default:
                $vate_tax_code = '';
        }

        return $vate_tax_code;
    }

    /**
     * 代理支付-sap应付发票
     * @param array $request_data 参数组
     * @return array|mixed
     */
    public function agency_payment_payable_invoices_to_sap($request_data)
    {
        $country_code = get_country_code();
        //AccountingCodingBlockDistribution-AccountingCodingBlockAssignment-AccountingCodingBlockTypeCode 成本分摊-账户分配类型
        if (substr($request_data['items']['ledger_account_name'], 0, 1) != 6) {
            //首字母含6pccode 为空
            $request_data['items']['cost_center_name'] = '';
            $acc_code = '<AccountingCodingBlockTypeCode>ACC</AccountingCodingBlockTypeCode>';
        } else {
            $acc_code = '<AccountingCodingBlockTypeCode>CC</AccountingCodingBlockTypeCode>';
        }

        //ProductTax-ProductTaxationCharacteristicsCode 税务明细-税务代码
        if (GlobalEnums::PH_COUNTRY_CODE == $country_code && !empty($item['wht_tax_code'])) {
            $with_tax = '<WithholdingTaxationCharacteristicsCode listID="PH" listAgencyID="http://**********-one-off.sap.com/YCB5ESA8Y_">' . $request_data['items']['wht_tax_code'] . '</WithholdingTaxationCharacteristicsCode>';
        } else {
            $with_tax = '<WithholdingTaxationCharacteristicsCode listID="' . $country_code . '">' . $request_data['items']['wht_tax_code'] . '</WithholdingTaxationCharacteristicsCode>';
        }
        $item_xml = '<Item actionCode="01">
                <ItemID>10</ItemID>
               <BusinessTransactionDocumentItemTypeCode>002</BusinessTransactionDocumentItemTypeCode>
               <!--数量:-->
               <Quantity unitCode="EA">1</Quantity>
               <!--单位代码:-->
               <QuantityTypeCode>EA</QuantityTypeCode>
              <!--服务描述:-->
               <SHORT_Description languageCode="EN">' . $request_data['items']['voucher_description'] . '</SHORT_Description>
                 <!--Optional:-->
               <Product actionCode="01"> 
               <!--现金折扣指标:-->
                   <CashDiscountDeductibleIndicator>false</CashDiscountDeductibleIndicator>
                  <!--Optional:-->
                  <ProductCategoryIDKey>
                     <!--产品类别:-->
                     <ProductCategoryInternalID>' . $request_data['items']['finance_category_code'] . '</ProductCategoryInternalID>
                  </ProductCategoryIDKey>      
                  <!--1物料，2服务-->
                  <ProductKey>
                     <ProductTypeCode>1</ProductTypeCode>
                  </ProductKey>
               </Product>
               <!--净价:-->
               <NetUnitPrice>
                  <Amount currencyCode="' . $request_data['currency'] . '">' . $request_data['items']['amount_total_no_tax'] . '</Amount>
                  <BaseQuantity>1.0</BaseQuantity>
                  <!--Optional:-->
                  <BaseQuantityTypeCode>EA</BaseQuantityTypeCode>
               </NetUnitPrice>
               <AccountingCodingBlockDistribution ActionCode="01" AccountingCodingBlockAssignmentListCompleteTransmissionIndicator="true">
                  <AccountingCodingBlockAssignment ActionCode="01">' . $acc_code . '<GeneralLedgerAccountAliasCode>' . $request_data['items']['ledger_account_name'] . '</GeneralLedgerAccountAliasCode>
                     <CostCentreID>' . $request_data['items']['cost_center_name'] . '</CostCentreID>
                  </AccountingCodingBlockAssignment>
               </AccountingCodingBlockDistribution>
               <!--产品税:-->
               <ProductTax actionCode="01">
                  <!--税务明细-税务代码:-->
                  <ProductTaxationCharacteristicsCode listID="' . $country_code . '">' . $request_data['items']['tax_code'] . '</ProductTaxationCharacteristicsCode>
                  <!--代扣代缴税说明-代扣代缴税代码:-->'
                  . $with_tax .
                  '<!--Optional:-->
                  <CountryCode>' . $country_code . '</CountryCode>
               </ProductTax>
            </Item>';

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global" xmlns:glob1="http://sap.com/xi/AP/Globalization">
           <soapenv:Header/>
           <soapenv:Body>
              <glob:SupplierInvoiceBundleMaintainRequest_sync>
                 <BasicMessageHeader/>
                 <SupplierInvoice actionCode="01" itemListCompleteTransmissionIndicator="true">
                    <BusinessTransactionDocumentTypeCode>004</BusinessTransactionDocumentTypeCode>
                    <!--发票描述:-->
                    <MEDIUM_Name>' . $request_data['ticket_desc'] . '</MEDIUM_Name>
                    <!--发票日期:-->
                    <Date>' . $request_data['apply_date'] . '</Date>
                    <!--接收日期:-->
                    <ReceiptDate>' . $request_data['apply_date'] . '</ReceiptDate>
                    <!--过账日期:-->
                    <TransactionDate>' . $request_data['approved_at'] . '</TransactionDate>
                    <!--Optional:-->
                    <DocumentItemGrossAmountIndicator>false</DocumentItemGrossAmountIndicator>
                    <!--Optional:-->
                    <ManualEntryERSIndicator>false</ManualEntryERSIndicator>
                    <!--总金额:-->
                    <GrossAmount currencyCode="' . $request_data['currency'] . '">' . $request_data['payable_amount'] . '</GrossAmount>
                    <!--总税额:-->
                    <TaxAmount currencyCode="' . $request_data['currency'] . '">' . $request_data['amount_total_vat'] . '</TaxAmount>
                    <Status>
                       <!--状态是3，单据没有问题才会自动过账:-->
                       <DataEntryProcessingStatusCode>3</DataEntryProcessingStatusCode>
                    </Status>
                    <!--外部参考号:-->
                    <CustomerInvoiceReference actionCode="01">
                       <BusinessTransactionDocumentReference>
                          <ID>' . $request_data['batch_no'] . '</ID>
                          <!--固定值:-->
                          <TypeCode>28</TypeCode>
                       </BusinessTransactionDocumentReference>
                    </CustomerInvoiceReference>
                    <!--买方公司:-->
                    <BuyerParty actionCode="01">
                       <!--Optional:-->
                       <PartyKey>
                          <!--固定值:-->
                          <PartyTypeCode>200</PartyTypeCode>
                          <!--Optional:-->
                          <PartyID>' . $request_data['cost_company_id'] . '</PartyID>
                       </PartyKey>
                    </BuyerParty>
                    <!--供应商:-->
                    <SellerParty actionCode="01">
                       <PartyKey>
                          <!--固定值:-->
                          <PartyTypeCode>147</PartyTypeCode>
                          <!--Optional:-->
                          <PartyID>' . $request_data['sap_supplier_no'] . '</PartyID>
                       </PartyKey>
                    </SellerParty>'
                    . $item_xml .
                '</SupplierInvoice>
              </glob:SupplierInvoiceBundleMaintainRequest_sync>
           </soapenv:Body>
        </soapenv:Envelope>';
        $this->logger->info(['agency-payment-payable_invoices-sap-post-data' => $post_xml]);

        $return_xml = $this->httpRequestXml('/sap/managesupplierinvoicein', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein
        $this->logger->info(['agency-payment-payable_invoices-sap-return-data' => $return_xml]);

        preg_match_all("/\<SupplierInvoice\>(.*?)\<\/SupplierInvoice\>/s", $return_xml, $SiteLogisticsTask);

        $return_arr = [];
        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);
        $return_arr['log'] = '';

        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }

        $logModel = new RequestSapLog();
        $logModel->save(['uuid' => $return_arr['UUID'] ?? '', 'order_code' => $request_data['batch_no'], 'type' => 10, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }

    /**
     * 代理支付-sap总账凭证
     * @param array $request_data 参数组
     * @return array|mixed
     */
    public function agency_payment_general_ledger_voucher_to_sap($request_data)
    {
        //借方
        $borrower_item_xml = '';
        foreach ($request_data['borrower'] as $detail) {
            $borrower_item_xml .= '<Item>
                <!--1借方 2贷方:-->
                <DebitCreditCode>1</DebitCreditCode>
                <!--总账科目:-->
                <ChartOfAccountsItemCode>' . $detail['ledger_account_name'] . '</ChartOfAccountsItemCode>
                <!--项目文本-->
                <Note>' . $detail['no'] . '</Note>
                <!--金额-->
                <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $detail['amount_total_actually'] . '</TransactionCurrencyAmount>
                <!--成本中心:-->
                <OverheadCostsLedgerAccountItem>
                    <!--成本中心编号:-->
                    <CostCentreID>' . $detail['cost_center_code'] . '</CostCentreID>
                </OverheadCostsLedgerAccountItem>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
            <soapenv:Header/>
            <soapenv:Body>
                <glob:AccountingEntryBundleMaintainRequest_sync>
                    <BasicMessageHeader></BasicMessageHeader>
                    <!--日记账凭证:-->
                    <AccountingEntry>
                        <!--留空或任意填写:-->
                        <ObjectNodeSenderTechnicalID></ObjectNodeSenderTechnicalID>
                        <!--留空或任意填写:-->
                        <ExternalID></ExternalID>
                        <!--公司:-->
                        <CompanyID>' . $request_data['cost_company_id'] . '</CompanyID>
                        <!--抬头文本:-->
                        <Note>' . $request_data['batch_no'] . '</Note>
                        <!--日记账分录类型:-->
                        <AccountingDocumentTypeCode>00047</AccountingDocumentTypeCode>
                        <!--过账日期:-->
                        <PostingDate>'.$request_data['approved_at'].'</PostingDate>
                        <!--结账步骤:-->
                        <AccountingClosingStepCode>010</AccountingClosingStepCode>
                        <!--屏幕变式:-->
                        <BusinessTransactionTypeCode>601</BusinessTransactionTypeCode>
                        <!--交易货币:-->
                        <TransactionCurrencyCode>' . $request_data['currency'] . '</TransactionCurrencyCode>
                        <!--借方1:-->
                        ' . $borrower_item_xml . '
                        <!--贷方:-->
                        <Item>
                            <!--1借方 2贷方:-->
                            <DebitCreditCode>2</DebitCreditCode>
                            <!--总账科目:-->
                            <ChartOfAccountsItemCode>' . $request_data['lender']['ledger_account_name'] . '</ChartOfAccountsItemCode>
                            <!--项目文本-->
                            <Note>' . $request_data['batch_no'] . ',' . $request_data['cost_type_text'] . '</Note>
                            <!--金额-->
                            <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $request_data['lender']['amount_total_actually'] . '</TransactionCurrencyAmount>
                            <!--成本中心:-->
                            <OverheadCostsLedgerAccountItem>
                                <!--成本中心编号:-->
                                <CostCentreID>' . $request_data['lender']['cost_center_name'] . '</CostCentreID>
                            </OverheadCostsLedgerAccountItem>
                        </Item>
                        <!--账套:-->
                        <SetOfBooks>
                            <SetOfBooksID>001</SetOfBooksID>
                        </SetOfBooks>
                    </AccountingEntry>
                </glob:AccountingEntryBundleMaintainRequest_sync>
            </soapenv:Body>
        </soapenv:Envelope>';
        $this->logger->info(['agency-payment-general_ledger_voucher-sap-post-data' => $post_xml]);

        $return_xml = $this->httpRequestXml('/sap/ManageAccountingEntryIn', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein
        $this->logger->info(['agency-payment-general_ledger_voucher-sap-return-data' => $return_xml]);

        //获取ID
        preg_match_all("/\<AccountingEntry\>(.*?)\<\/AccountingEntry\>/s", $return_xml, $SiteLogisticsTask);

        $return_arr = [];
        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        $return_id = $return_arr['ID'] ?? '';

        //只有日志里的明细行里的SeverityCode没有3的才算真正成功
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);
        $return_arr['log'] = '';
        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }
        if ($return_arr['log']) {
            $severity_code = array_column($return_arr['log']['Item'], 'SeverityCode');
            if (in_array(3, $severity_code)) {
                $return_arr = [];
            }
        }

        $logModel = new RequestSapLog();
        $logModel->save(['uuid' => $return_id, 'order_code' => $request_data['batch_no'], 'type' => 11, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }

    /**
     * 普通付款支付-sap总账凭证
     * @param array $request_data 参数组
     * @param int $request_sap_type 13普通付款总账凭证、16普通付款关联预提单总账凭证
     * @return array|mixed
     */
    public function ordinary_payment_general_ledger_voucher_to_sap($request_data, $request_sap_type = 13)
    {
        //借方
        $borrower_item_xml = '';
        foreach ($request_data['borrower'] as $detail) {
            $borrower_item_xml .= '<Item>
                <!--1借方 2贷方:-->
                <DebitCreditCode>1</DebitCreditCode>
                <!--总账科目:-->
                <ChartOfAccountsItemCode>' . $detail['ledger_account_name'] . '</ChartOfAccountsItemCode>
                <!--项目文本-->
                <Note>' . $detail['voucher_description'] . '</Note>
                <!--金额-->
                <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $detail['amount_no_tax'] . '</TransactionCurrencyAmount>
                <!--成本中心:-->
                <OverheadCostsLedgerAccountItem>
                    <!--成本中心编号:-->
                    <CostCentreID>' . $detail['cost_center_code'] . '</CostCentreID>
                </OverheadCostsLedgerAccountItem>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
            <soapenv:Header/>
            <soapenv:Body>
                <glob:AccountingEntryBundleMaintainRequest_sync>
                    <BasicMessageHeader></BasicMessageHeader>
                    <!--日记账凭证:-->
                    <AccountingEntry>
                        <!--留空或任意填写:-->
                        <ObjectNodeSenderTechnicalID></ObjectNodeSenderTechnicalID>
                        <!--留空或任意填写:-->
                        <ExternalID></ExternalID>
                        <!--公司:-->
                        <CompanyID>' . $request_data['cost_company_id'] . '</CompanyID>
                        <!--抬头文本:-->
                        <Note>' . $request_data['apply_no'] . '</Note>
                        <!--日记账分录类型:-->
                        <AccountingDocumentTypeCode>00047</AccountingDocumentTypeCode>
                        <!--过账日期:-->
                        <PostingDate>'.$request_data['approved_at'].'</PostingDate>
                        <!--结账步骤:-->
                        <AccountingClosingStepCode>010</AccountingClosingStepCode>
                        <!--屏幕变式:-->
                        <BusinessTransactionTypeCode>601</BusinessTransactionTypeCode>
                        <!--交易货币:-->
                        <TransactionCurrencyCode>' . $request_data['currency'] . '</TransactionCurrencyCode>
                        <!--借方1:-->
                        ' . $borrower_item_xml . '
                        <!--贷方:-->
                        <Item>
                            <!--1借方 2贷方:-->
                            <DebitCreditCode>2</DebitCreditCode>
                            <!--总账科目:-->
                            <ChartOfAccountsItemCode>' . $request_data['lender']['ledger_account_name'] . '</ChartOfAccountsItemCode>
                            <!--项目文本-->
                            <Note>' . $request_data['lender']['voucher_description'] . '</Note>
                            <!--金额-->
                            <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $request_data['lender']['amount_total_actually'] . '</TransactionCurrencyAmount>
                            <!--成本中心:-->
                            <OverheadCostsLedgerAccountItem>
                                <!--成本中心编号:-->
                                <CostCentreID>' . $request_data['lender']['cost_center_code'] . '</CostCentreID>
                            </OverheadCostsLedgerAccountItem>
                        </Item>
                        <!--账套:-->
                        <SetOfBooks>
                            <SetOfBooksID>001</SetOfBooksID>
                        </SetOfBooks>
                    </AccountingEntry>
                </glob:AccountingEntryBundleMaintainRequest_sync>
            </soapenv:Body>
        </soapenv:Envelope>';

        $this->logger->info(['ordinary_payment_general_ledger_voucher_to_sap-post-data' => $post_xml]);
        $return_xml = $this->httpRequestXml('/sap/ManageAccountingEntryIn', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein

        //获取ID
        preg_match_all("/\<AccountingEntry\>(.*?)\<\/AccountingEntry\>/s", $return_xml, $SiteLogisticsTask);

        $return_arr = [];
        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        $return_id = $return_arr['ID'] ?? '';

        //只有日志里的明细行里的SeverityCode没有3的才算真正成功
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);
        $return_arr['log'] = '';
        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }
        $return_data_error = 'info';
        if ($return_arr['log']) {
            $severity_code = array_column($return_arr['log']['Item'], 'SeverityCode');
            if (in_array(3, $severity_code)) {
                $return_arr = [];
                //总账失败推送异常预警
                $return_data_error = 'warning';
            }
        }

        $this->logger->$return_data_error(['ordinary_payment_general_ledger_voucher_to_sap-return-data' => $return_xml]);

        $logModel = new RequestSapLog();
        $logModel->save(['uuid' => $return_id, 'order_code' => $request_sap_type == 16 ? $request_data['ordinary_payment_no'] : $request_data['apply_no'], 'type' => $request_sap_type, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }

    /**
     * 预提单-sap总账凭证
     * @param array $request_data 参数组
     * @param int $request_sap_type 14预提单总账凭证、15预提单红冲凭证
     * @return array|mixed
     */
    public function budget_withholding_general_ledger_voucher_to_sap(array $request_data, int $request_sap_type)
    {
        //借方
        $borrower_item_xml = '';
        foreach ($request_data['borrower'] as $detail) {
            $borrower_item_xml .= '<Item>
                <!--1借方 2贷方:-->
                <DebitCreditCode>1</DebitCreditCode>
                <!--总账科目:-->
                <ChartOfAccountsItemCode>' . $detail['ledger_account_name'] . '</ChartOfAccountsItemCode>
                <!--项目文本-->
                <Note>' . $detail['voucher_description'] . '</Note>
                <!--金额-->
                <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $detail['provision_amount'] . '</TransactionCurrencyAmount>
                <!--成本中心:-->
                <OverheadCostsLedgerAccountItem>
                    <!--成本中心编号:-->
                    <CostCentreID>' . $detail['cost_center_code'] . '</CostCentreID>
                </OverheadCostsLedgerAccountItem>
            </Item>';
        }

        $post_xml = '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:glob="http://sap.com/xi/SAPGlobal20/Global">
            <soapenv:Header/>
            <soapenv:Body>
                <glob:AccountingEntryBundleMaintainRequest_sync>
                    <BasicMessageHeader></BasicMessageHeader>
                    <!--日记账凭证:-->
                    <AccountingEntry>
                        <!--留空或任意填写:-->
                        <ObjectNodeSenderTechnicalID></ObjectNodeSenderTechnicalID>
                        <!--留空或任意填写:-->
                        <ExternalID></ExternalID>
                        <!--公司:-->
                        <CompanyID>' . $request_data['cost_company_id'] . '</CompanyID>
                        <!--抬头文本:-->
                        <Note>' . $request_data['apply_no'] . '</Note>
                        <!--日记账分录类型:-->
                        <AccountingDocumentTypeCode>00047</AccountingDocumentTypeCode>
                        <!--过账日期:-->
                        <PostingDate>'.$request_data['approved_at'].'</PostingDate>
                        <!--结账步骤:-->
                        <AccountingClosingStepCode>010</AccountingClosingStepCode>
                        <!--屏幕变式:-->
                        <BusinessTransactionTypeCode>601</BusinessTransactionTypeCode>
                        <!--交易货币:-->
                        <TransactionCurrencyCode>' . $request_data['currency'] . '</TransactionCurrencyCode>
                        <!--借方1:-->
                        ' . $borrower_item_xml . '
                        <!--贷方:-->
                        <Item>
                            <!--1借方 2贷方:-->
                            <DebitCreditCode>2</DebitCreditCode>
                            <!--总账科目:-->
                            <ChartOfAccountsItemCode>' . $request_data['lender']['ledger_account_name'] . '</ChartOfAccountsItemCode>
                            <!--项目文本-->
                            <Note>' . $request_data['lender']['voucher_description'] . '</Note>
                            <!--金额-->
                            <TransactionCurrencyAmount currencyCode="' . $request_data['currency'] . '">' . $request_data['lender']['provision_amount'] . '</TransactionCurrencyAmount>
                            <!--成本中心:-->
                            <OverheadCostsLedgerAccountItem>
                                <!--成本中心编号:-->
                                <CostCentreID>' . $request_data['lender']['cost_center_code'] . '</CostCentreID>
                            </OverheadCostsLedgerAccountItem>
                        </Item>
                        <!--账套:-->
                        <SetOfBooks>
                            <SetOfBooksID>001</SetOfBooksID>
                        </SetOfBooks>
                    </AccountingEntry>
                </glob:AccountingEntryBundleMaintainRequest_sync>
            </soapenv:Body>
        </soapenv:Envelope>';

        $this->logger->info(['budget_withholding_general_ledger_voucher_to_sap-post-data' => $post_xml]);
        $return_xml = $this->httpRequestXml('/sap/ManageAccountingEntryIn', $post_xml);//https://my602255.sapbyd.cn/sap/bc/srt/scs/sap/managesupplierinvoicein

        //获取ID
        preg_match_all("/\<AccountingEntry\>(.*?)\<\/AccountingEntry\>/s", $return_xml, $SiteLogisticsTask);

        $return_arr = [];
        if (isset($SiteLogisticsTask[0][0]) && !empty($SiteLogisticsTask[0][0])) {
            $return_arr = SapsService::getInstance()->xmlToArray($SiteLogisticsTask[0][0]);
        }
        $return_id = $return_arr['ID'] ?? '';

        //只有日志里的明细行里的SeverityCode没有3的才算真正成功
        preg_match_all("/\<Log\>(.*?)\<\/Log\>/s", $return_xml, $log);
        $return_arr['log'] = '';
        if (isset($log[0][0]) && !empty($log[0][0])) {
            $return_arr['log'] = SapsService::getInstance()->xmlToArray($log[0][0]);
        }
        $return_data_error = 'info';
        if ($return_arr['log']) {
            $severity_code = array_column($return_arr['log']['Item'], 'SeverityCode');
            if (in_array(3, $severity_code)) {
                $return_arr = [];
                //总账失败推送异常预警
                $return_data_error = 'warning';
            }
        }

        $this->logger->$return_data_error(['budget_withholding_general_ledger_voucher_to_sap-return-data' => $return_xml]);

        $logModel = new RequestSapLog();
        $logModel->save(['uuid' => $return_id, 'order_code' => $request_data['apply_no'], 'type' => $request_sap_type, 'request_data' => $post_xml, 'response_data' => $return_xml ?? '', 'create_at' => date('Y-m-d H:i:s')]);
        return $return_arr;
    }
}