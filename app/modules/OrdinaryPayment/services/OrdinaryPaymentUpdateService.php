<?php

namespace App\Modules\OrdinaryPayment\Services;

use App\Library\Enums;
use App\Library\Enums\SysConfigEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use Phalcon\Mvc\Model\Transaction\Failed as TxFailed;

class OrdinaryPaymentUpdateService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 普通付款 - 支付
     *
     * @param array $pay_data
     * @param array $user
     * @param int $is_from
     * @return array
     */
    public function pay(array $pay_data, array $user, $is_from = 1)
    {
        $code    = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $main_id = $pay_data['id'];

        unset($pay_data['id']);

        //根据选择支付状态整理要处理字段
        $pay_data    = $this->handlePayData($pay_data,$user);
        $db = $this->getDI()->get('db_oa');

        try {
            $db->begin();

            $pay_staff_id = $this->getPayAuthStaffIdItem();
            if (!in_array($user['id'], $pay_staff_id) && $is_from == 1) {
                throw new ValidationException("no pay auth", ErrCode::$ORDINARY_PAYMENT_PAY_AUTH_ERROR);
            }

            $main_model = OrdinaryPayment::getFirst([
                'conditions' => 'id = :id:',
                'bind'       => ['id' => $main_id],
            ]);

            if (empty($main_model)) {
                throw new BusinessException("付款支付 - 获取付款申请信息失败", ErrCode::$ORDINARY_PAYMENT_GET_INFO_ERROR);
            }
            // 审核通过&待支付 状态可编辑，其他禁止操作
            if ($main_model->approval_status != Enums::PAYMENT_APPLY_STATUS_APPROVAL || $main_model->pay_status != Enums::PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException("current status cannot be pay ", ErrCode::$ORDINARY_PAYMENT_PAY_NOT_ALLOW_EDIT_ERROR);
            }
            if ($is_from == 1 && $main_model->is_pay_module == 1){
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }
            //todo 支付信息入库
            $pay_model = OrdinaryPaymentExtend::getFirst([
                'ordinary_payment_id = :ordinary_payment_id:',
                'bind' => ['ordinary_payment_id' => $main_id],
            ]);
            if (empty($pay_model)) {
                $pay_data['ordinary_payment_id'] = $main_id;
                $extend_model = new OrdinaryPaymentExtend();
                $extend_res   = $extend_model->i_create($pay_data);
                if ($extend_res === false) {
                    throw new BusinessException('付款支付 -付款信息创建失败： ' . json_encode($pay_data, JSON_UNESCAPED_UNICODE), ErrCode::$ORDINARY_PAYMENT_CREATE_EXTEND_ERROR);
                }
            } else {
                $bool = $pay_model->i_update($pay_data);
                if ($bool === false) {
                    throw new BusinessException("付款支付 - 支付信息修改失败", ErrCode::$ORDINARY_PAYMENT_PAY_EXTEND_DATA_INSERT_ERROR);
                }

            }

            //todo 主表支付状态 同步变更
            $main_data = [
                'pay_status' => $pay_data['is_pay'],
            ];

            $bool = $main_model->i_update($main_data);
            if ($bool === false) {
                throw new BusinessException("付款支付 - 普通付款支付状态同步主表失败", ErrCode::$ORDINARY_PAYMENT_PAY_STATUS_SYNC_ERROR);
            }
            /**
             * v12855
             * 如果开启了支付模块,支付模块请求的支付否(撤回操作),需要释放预算, 本模块支付不再释放
             * 为了兼容开启支付模块后任然处于本模块支付流程中的数据, 使用is_pay_module判断,如果=1 限制只有支付模块撤回可以释放,如果=0则本模块可以释放
             *
             * 如果没有开启支付模块, 本模块依然释放预算
             */
            $pay_module_status = EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_ORDINARY_PAYMENT, $main_model->cost_company_id);
            if($pay_data['is_pay'] == Enums::PAYMENT_PAY_STATUS_NOTPAY){
                //开启支付模块后的支付模块来源请求,且已经进入支付模块的数据(为了兼容),可以释放
                if ($pay_module_status == 1 && $is_from == 2 && $main_model->is_pay_module == 1) {
                    OrdinaryPaymentFlowService::getInstance()->removeBindBudgetWithholdingInfo($main_model, $user);
                }
                //开启支付模块后的本模块来源请求,且未进入支付模块的数据(为了兼容),可以释放
                if ($pay_module_status == 1 && $is_from == 1 && $main_model->is_pay_module == 0) {
                    OrdinaryPaymentFlowService::getInstance()->removeBindBudgetWithholdingInfo($main_model, $user);
                }
                //未开启支付模块(没开启支付模块只有本模块一个入口,无需判断来源), 可以释放
                if ($pay_module_status != 1) {
                    OrdinaryPaymentFlowService::getInstance()->removeBindBudgetWithholdingInfo($main_model, $user);
                }
            }

            $db->commit();

            //支付完成，删除所有支付人的待审核数
            $this->delUnReadNumsKeyByStaffIds($this->getPayAuthStaffIdItem());

        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code    = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code         = $e->getCode();
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (TxFailed $e) {
            //数据库错误，不可对外抛出
            $code         = ErrCode::$MYSQL_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code         = ErrCode::$SYSTEM_ERROR;
            $message      = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->warning('ordinary_payment_pay-update-failed:' . $real_message);
        }

        return [
            'code'    => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data'    => [],
        ];
    }

    /**
     * 普通付款 - 支付提交数据处理
     * @param array $data
     * @param array $user
     * @return array
     */
    private function handlePayData(array $data,array $user)
    {
        if (empty($data)) {
            return [];
        }
        $data['pay_staff_id'] = $user['id'] ?? 0;
        // 未付款
        if ($data['is_pay'] == Enums::PAYMENT_PAY_STATUS_NOTPAY) {
            unset($data['pay_bk_name']);
            unset($data['pay_bk_account']);
            unset($data['pay_signer_name']);
            unset($data['pay_bk_flow_date']);
        }

        $data['pay_at'] = date('Y-m-d H:i:s');

        return $data;
    }
}
