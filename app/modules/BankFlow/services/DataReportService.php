<?php
/**
 * 流水数据报表
 */

namespace App\Modules\BankFlow\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankListModel;
use App\Library\OssHelper;

class DataReportService extends BaseService
{
    private static $instance;

    // 流水币种
    const FLOW_CURRENCY_THB = 1;
    const FLOW_CURRENCY_USD = 2;
    const FLOW_CURRENCY_CNY = 3;
    const FLOW_CURRENCY_MYR = 6;
    const FLOW_CURRENCY_PHP = 4;

    // 报表分类 balanceSummary
    const REPORT_TYPE_FUNDS_EXPENSE = 1;// 资金日报 - 收支表
    const REPORT_TYPE_FUNDS_BALANCE_SUMMARY = 2;// 资金日报 - 余额汇总表

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * @param $header
     * @param $rows
     * @param string $fileName
     * @param int $report_type
     * @return array
     * @throws Exception\BusinessException
     */
    public function genExcel($header, $rows, $fileName = 'excel.xlsx', int $report_type = 0)
    {
        if (!strstr($fileName,'.xlsx')){
            $fileName = $fileName.'.xlsx';
        }
        $config = [
            'path' => sys_get_temp_dir(),
        ];

        $excel = new \Vtiful\Kernel\Excel($config);

        // 资金日报 - 收支表
        if ($report_type == self::REPORT_TYPE_FUNDS_EXPENSE) {
            // 此处会自动创建一个工作表
            $fileObject = $excel->fileName($fileName)->mergeCells('A1:D1', 'Merge cells');

            // 设置样式
            $fileHandle = $fileObject->getHandle();
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $boldStyle = $format->bold()
                ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
                ->toResource();

            $filePath = $fileObject->header($header)
                ->data($rows)
                ->setRow('A1:D1', 20, $boldStyle)
                ->setRow('A2:A2', 20, $boldStyle)
                ->setColumn('A:D', 20)
                ->output();
        } else if ($report_type == self::REPORT_TYPE_FUNDS_BALANCE_SUMMARY) {
            // 此处会自动创建一个工作表
            $fileObject = $excel->fileName($fileName)->mergeCells('A1:I1', 'Merge cells');

            // 设置样式
            $fileHandle = $fileObject->getHandle();
            $format    = new \Vtiful\Kernel\Format($fileHandle);
            $common_style = $format->bold()
                ->align(\Vtiful\Kernel\Format::FORMAT_ALIGN_CENTER, \Vtiful\Kernel\Format::FORMAT_ALIGN_VERTICAL_CENTER)
                ->toResource();

            $filePath = $fileObject->header($header)
                ->data($rows)
                ->setRow('A1:I1', 20, $common_style)
                ->setRow('A2:I2', 20, $common_style)
                ->setColumn('A:I', 10)
                ->output();
        } else {
            // 此处会自动创建一个工作表
            $fileObject = $excel->fileName($fileName);
            $filePath = $fileObject->header($header)
                ->data($rows)
                ->output();
        }

        $file = OssHelper::uploadFile($filePath);
        $url = $file['object_url'];

        // 最后的最后，输出文件
        return ['code'=>1,'data'=> $url];
    }

    /**
     * 获取所有费用类型的支出数据
     * @param string $trade_date
     * @param bool $is_download
     * @param string $company_name 银行账号公司名称条件
     * @return mixed
     */
    public function getExpenseData(string $trade_date, bool $is_download = false,$company_name='')
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data = [];

        try {
            // 1. 获取全部费用类型
            $all_expense = InitFlowService::getInstance()->getExpenseTypeList('all_list');
            $all_expense = array_column($all_expense, 'label', 'value');
            unset($all_expense[0]);
            // 12855 新增银行账号公司名称条件
            $bank_account_id_arr = [];
            if (!empty($company_name)){
                $bank_accounts = BankAccountModel::find([
                    'conditions' => "company_name = :company_name: and is_deleted=0",
                    'bind' => ['company_name' => $company_name],
                    'columns' => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
            }
            // 2. 获取指定日期的流水数据
            $conditions = 'date = :date:';
            $bind = ['date' => $trade_date];
            if (!empty($bank_account_id_arr)){
                $conditions .= ' and bank_account_id in ({bank_account_id:array})';
                $bind['bank_account_id'] = $bank_account_id_arr;
            }
            $flow_item = BankFlowModel::find([
                'conditions' => $conditions,
                'bind' => $bind,
                'columns' => ['SUM(real_amount) AS real_amount', 'currency', 'bank_flow_expense_id'],
                'group' => 'bank_flow_expense_id,currency'
            ])->toArray();
            $expense_currency_item = [];
            foreach ($flow_item as $flow) {
                $expense_currency_item[$flow['bank_flow_expense_id'].'_'.$flow['currency']] = $flow['real_amount'];
            }
            unset($flow_item);

            // 3. 构造指定数据结构
            $data = [];
            foreach ($all_expense as $key => $val) {
                $expense_info = $data[$key] ?? [];
                $thb_total = $expense_info['thb_total_amount'] ?? 0;
                $usd_total = $expense_info['usd_total_amount'] ?? 0;
                $cny_total = $expense_info['cny_total_amount'] ?? 0;
                $myr_total = $expense_info['myr_total_amount'] ?? 0;
                $php_total = $expense_info['php_total_amount'] ?? 0;

                // THB 1
                $thb_k = $key.'_'.self::FLOW_CURRENCY_THB;

                // USD 2
                $usd_k = $key.'_'.self::FLOW_CURRENCY_USD;

                // CNY 3
                $cny_k = $key.'_'.self::FLOW_CURRENCY_CNY;

                // MYR 6
                $myr_k = $key.'_'.self::FLOW_CURRENCY_MYR;

                // PHP 4
                $php_k = $key.'_'.self::FLOW_CURRENCY_PHP;

                if (!empty($expense_currency_item[$thb_k])) {
                    $thb_total += $expense_currency_item[$thb_k];
                } else if (!empty($expense_currency_item[$usd_k])) {
                    $usd_total += $expense_currency_item[$usd_k];
                } else if (!empty($expense_currency_item[$cny_k])) {
                    $cny_total += $expense_currency_item[$cny_k];
                } else if (!empty($expense_currency_item[$myr_k])) {
                    $myr_total += $expense_currency_item[$myr_k];
                } else if (!empty($expense_currency_item[$php_k])) {
                    $php_total += $expense_currency_item[$php_k];
                }

                $data[$key] = [
                    'expense_type_name' => $val,
                    'thb_total_amount' => $thb_total,
                    'usd_total_amount' => $usd_total,
                    'cny_total_amount' => $cny_total,
                    'myr_total_amount' => $myr_total,
                    'php_total_amount' => $php_total,
                ];
            }

            if ($is_download) {
                $data = array_values(array_map(function ($item) {
                    return  [
                        $item['expense_type_name'],
                        number_format($item['thb_total_amount'], '2'),
                        number_format($item['usd_total_amount'], '2'),
                        number_format($item['cny_total_amount'], '2'),
                        number_format($item['myr_total_amount'], '2'),
                        number_format($item['php_total_amount'], '2'),
                    ];

                }, $data));
            } else {
                $data = array_values(array_map(function ($item) {
                    $item['thb_total_amount'] = number_format($item['thb_total_amount'], '2');
                    $item['usd_total_amount'] = number_format($item['usd_total_amount'], '2');
                    $item['cny_total_amount'] = number_format($item['cny_total_amount'], '2');
                    $item['myr_total_amount'] = number_format($item['myr_total_amount'], '2');
                    $item['php_total_amount'] = number_format($item['php_total_amount'], '2');
                    return $item;
                }, $data));
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 资金日报 - 支出表获取失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 导出所有费用类型的支出数据
     * @param string $trade_date
     * @param $company_name
     * @return mixed
     */
    public function exportExpenseData(string $trade_date,$company_name='')
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $oss_file_url = '';

        try {
            $header = [
                self::$t['bank_flow_funds_report_table_name'],
            ];

            $first_rows[] = [
                self::$t['bank_flow_funds_report_expense_type'],//收支类型
                self::$t['bank_flow_funds_report_thb_total_amount'],//当日发生额合计（THB）
                self::$t['bank_flow_funds_report_usd_total_amount'],//当日发生额合计（USD
                self::$t['bank_flow_funds_report_cny_total_amount'],//当日发生额合计（CNY）
                self::$t['bank_flow_funds_report_myr_total_amount'],//当日发生额合计（MYR）
            ];

            $list = $this->getExpenseData($trade_date, true,$company_name);
            if (!empty($list['data'])) {
                $file_name = 'ExportExpenseData_' . time() . '.xlsx';
                $data = array_merge($first_rows, $list['data']);
                $oss_res = $this->genExcel($header, $data, $file_name, self::REPORT_TYPE_FUNDS_EXPENSE);
                if (!empty($oss_res['code']) && $oss_res['code'] == ErrCode::$SUCCESS) {
                    $oss_file_url = $oss_res['data'];
                } else {
                    $this->logger->warning('银行流水 - 资金日报 - 收支表 OSS 上传异常: ' . json_encode($oss_res, JSON_UNESCAPED_UNICODE));
                }
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->error('银行流水 - 资金日报 - 收支表导出异常: ' . $e->getMessage());
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'file_url' => $oss_file_url
            ]
        ];
    }

    /**
     * 获取余额汇总表数据
     * @param string $trade_date
     * @param bool $is_download
     * @param string $company_name
     * @return mixed
     */
    public function getBalanceSummaryData(string $trade_date, bool $is_download = false, $company_name = '')
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data = [];

        try {
            // 1. 获取全部银行账号
            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'acct.id',
                'acct.company_name',
                'bank.bank_name',
                'acct.account',
                'acct.currency',
                'acct.left_amount',
                'acct.left_amount_date'
            ]);

            $builder->from(['bank' => BankListModel::class]);
            $builder->leftjoin(BankAccountModel::class, 'acct.bank_id = bank.id', 'acct');
            $builder->where('bank.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            $builder->andWhere('acct.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
            if (!empty($company_name)) {
                $builder->andWhere('acct.company_name = :company_name:', ['company_name' => $company_name]);
            }
            $bank_account_item = $builder->getQuery()->execute()->toArray();

            // 2. 系统汇率配置 (获取USD/CNY 面向 THB(系统默认币种) 的汇率所需)
            GlobalEnums::init();
            $currency_symbol_item = GlobalEnums::$currency_symbol_map;
            $exchange_rate_item = GlobalEnums::$exchange_rate_map;
            $sys_default_currency = GlobalEnums::$sys_default_currency;// 系统默认币种枚举, 数值形式: 示例 1
            $sys_default_currency_symbol = GlobalEnums::$sys_default_currency_symbol;// 系统默认币种缩写符号, 字符串形式: 示例 THB

            // 2. 获取交易日期最近的账号余额(银行流水 - 资金日报 - 查询所有账号指定日期的余额数据)
            // 2.1 获取各账号最近的交易日期对应的流水
            $account_max_flow_ids = BankFlowModel::find([
                'conditions' => 'date <= :trade_date:',
                'bind' => ['trade_date' => $trade_date],
                'columns' => ['MAX(id) AS id'],
                'group' => 'bank_account_id',
                'order' => 'date DESC , id DESC'
            ])->toArray();

            // 2.2 获取交易日期最近的流水信息
            $flow_balance_item = [];
            if (!empty($account_max_flow_ids)) {
                $account_max_flow_ids = array_column($account_max_flow_ids, 'id');

                $flow_balance_item = BankFlowModel::find([
                    'conditions' => 'id IN ({ids:array})',
                    'bind' => ['ids' => $account_max_flow_ids],
                    'columns' => ['bank_account_id', 'date', 'bank_left_amount', 'currency'],
                ])->toArray();

                $flow_balance_item = array_column($flow_balance_item, null, 'bank_account_id');
            }

            // 泰铢(系统默认币种)账户合计余额
            $thb_acct_total_amount = 0;

            // 美元账户合计余额
            $usd_acct_total_amount = 0;

            // 人民币账户合计余额
            $cny_acct_total_amount = 0;

            // 各币种余额汇总
            $all_acct_total_amount = 0;

            foreach ($bank_account_item as $key => $acct) {
                // 账号的币种
                $currency_symbol = $currency_symbol_item[$acct['currency']] ?? '';

                // 账号币种与系统默认币种的汇率
                $exchange_rate = $exchange_rate_item[$currency_symbol . '_TO_' . $sys_default_currency_symbol] ?? '';
                $acct['exchange_rate'] = $exchange_rate;

                $acct_flow_info = $flow_balance_item[$acct['id']] ?? [];

                // 交易日期余额
                $acct['left_amount'] = !empty($acct_flow_info) ? $acct_flow_info['bank_left_amount'] : $acct['left_amount'];

                // 余额更新日期
                $acct['left_amount_date'] = !empty($acct_flow_info) ? $acct_flow_info['date'] : $acct['left_amount_date'];

                // 转换为THB(系统默认币种)金额
                $acct['thb_amount'] = bcmul($acct['left_amount'], $exchange_rate, 2);

                // 各币种账户的余额分别合计
                switch ($acct['currency']) {
                    case $sys_default_currency:
                        $thb_acct_total_amount += $acct['left_amount'];
                        break;
                    case self::FLOW_CURRENCY_USD:
                        $usd_acct_total_amount += $acct['left_amount'];
                        break;
                    case self::FLOW_CURRENCY_CNY:
                        $cny_acct_total_amount += $acct['left_amount'];
                        break;
                }

                $all_acct_total_amount += $acct['thb_amount'];

                $acct['id'] = $key + 1;
                $acct['currency'] = $currency_symbol;
                $bank_account_item[$key] = $acct;
            }

            // 4. 构造账号金额 与 合计金额 的数据结构
            $balance_total_item = [
                // 泰铢账户合计余额
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_' . strtolower($sys_default_currency_symbol). '_acct_balance_total_amount'],
                    'currency' => 'In ' . $sys_default_currency_symbol,
                    'left_amount' => $thb_acct_total_amount,
                    'left_amount_date' => '',
                    'exchange_rate' => '',
                    'thb_amount' => ''
                ],

                // 美元账户合计余额
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_usd_acct_balance_total_amount'],
                    'currency' => 'In USD',
                    'left_amount' => $usd_acct_total_amount,
                    'left_amount_date' => '',
                    'exchange_rate' => '',
                    'thb_amount' => ''
                ],

                // 人民币账户合计余额
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_cny_acct_balance_total_amount'],
                    'currency' => 'In CNY',
                    'left_amount' => $cny_acct_total_amount,
                    'left_amount_date' => '',
                    'exchange_rate' => '',
                    'thb_amount' => ''
                ],

                // 余额汇总 THB(系统默认币种)
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_balance_total_amount'],
                    'currency' => 'In ' . $sys_default_currency_symbol,
                    'left_amount' => '',
                    'left_amount_date' => '',
                    'exchange_rate' => $exchange_rate_item[$sys_default_currency_symbol . '_TO_' . $sys_default_currency_symbol],
                    'thb_amount' => $all_acct_total_amount
                ],

                // 余额汇总 USD
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_balance_total_amount'],
                    'currency' => 'In USD',
                    'left_amount' => '',
                    'left_amount_date' => '',
                    'exchange_rate' => $exchange_rate_item['USD_TO_' . $sys_default_currency_symbol],
                    'thb_amount' => $all_acct_total_amount
                ],

                // 余额汇总 CNY
                [
                    'id' => '',
                    'company_name' => '',
                    'bank_name' => '',
                    'account' => self::$t['bank_flow_balance_total_amount'],
                    'currency' => 'In CNY',
                    'left_amount' => '',
                    'left_amount_date' => '',
                    'exchange_rate' => $exchange_rate_item['CNY_TO_' . $sys_default_currency_symbol],
                    'thb_amount' => $all_acct_total_amount
                ]
            ];

            $data = array_merge($bank_account_item, $balance_total_item);
            foreach ($data as $k => $v) {
                $v['left_amount'] = number_format(is_numeric($v['left_amount']) ? $v['left_amount'] : 0, 2);
                $v['thb_amount'] = number_format(is_numeric($v['thb_amount']) ? $v['thb_amount'] : 0, 2);

                if ($is_download) {
                    $data[$k] = [
                        $v['id'],
                        $v['company_name'],
                        $v['bank_name'],
                        $v['account'],
                        $v['currency'],
                        $v['left_amount'],
                        $v['exchange_rate'],
                        $v['thb_amount'],
                        $v['left_amount_date'],
                    ];
                } else {
                    $data[$k] = $v;
                }
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('银行流水 - 资金日报 - 余额汇总表获取失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 导出所有费用类型的支出数据
     * @param string $trade_date
     * @param string $company_name
     * @return mixed
     */
    public function exportBalanceSummaryData(string $trade_date, $company_name = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $oss_file_url = '';

        try {
            $header = [
                self::$t['bank_flow_funds_balance_summary_report_title_' . strtolower(get_country_code())] . "（{$trade_date}）",
            ];

            $first_rows[] = [
                self::$t['bank_flow_funds_balance_summary_no'],//序号
                self::$t['bank_flow_funds_balance_summary_company_name'],//公司名称
                self::$t['bank_flow_funds_balance_summary_bank_name'],//银行名称
                self::$t['bank_flow_funds_balance_summary_bank_acct'],//银行账号
                self::$t['bank_flow_funds_balance_summary_currency'],//币种
                self::$t['bank_flow_funds_balance_summary_balance'],//银行余额
                self::$t['bank_flow_funds_balance_summary_exchange_rate'],//集团报表汇率
                self::$t['bank_flow_funds_balance_summary_thb_amount'],//THB金额
                self::$t['bank_flow_funds_balance_summary_balance_time'],//余额更新时间
            ];

            $list = $this->getBalanceSummaryData($trade_date, true, $company_name);
            if (!empty($list['data'])) {
                $file_name = 'ExportBalanceSummaryData_' . time() . '.xlsx';
                $data = array_merge($first_rows, $list['data']);
                $oss_res = $this->genExcel($header, $data, $file_name, self::REPORT_TYPE_FUNDS_BALANCE_SUMMARY);
                if (!empty($oss_res['code']) && $oss_res['code'] == ErrCode::$SUCCESS) {
                    $oss_file_url = $oss_res['data'];
                } else {
                    $this->logger->warning('银行流水 - 资金日报 - 余额汇总表 OSS 上传异常: ' . json_encode($oss_res, JSON_UNESCAPED_UNICODE));
                }
            } else {
                throw new BusinessException('余额汇总表导出异常, 余额数据=' . json_encode($list, JSON_UNESCAPED_UNICODE));
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('银行流水 - 资金日报 - 余额汇总表导出异常: ' . $e->getMessage());

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->error('银行流水 - 资金日报 - 余额汇总表导出异常: ' . $e->getMessage());
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => [
                'file_url' => $oss_file_url
            ]
        ];
    }


}
