<?php

namespace App\Modules\BankFlow\Services;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankFlowParcelClaimModel as ParcelClaimModel;

class AddService extends BaseService
{
    public static $not_must_params = [

    ];

    private static $instance;

    private function __construct()
    {
    }

    /**
     * @return AddService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 添加数据
     * @Date: 2021-09-16 15:02
     * @param $data
     * @return array
     **@author: peak pan
     */
    public function add($data)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            $db = $this->getDI()->get('db_oa');
            $db->begin();
            
            $data = $this->handleData($data);

            if (is_string($data)) {
                throw new ValidationException($data, ErrCode::$VALIDATE_ERROR);
            }
            $model = new ParcelClaimModel();
            $bool = $model->i_create($data);
            if ($bool === false) {
                $messages = $model->getMessages();
                throw new BusinessException('ms同步理赔数据失败' . implode(",", $messages), ErrCode::$BANK_FLOW_PARCEL_CLAIM_ADD_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {      //校验错误，可对外抛出
            $code = $e->getCode();
            if ($code == ErrCode::$BANK_FLOW_PARCEL_CLAIM_FORM_ATTACHE_ERROR) {
                $code = ErrCode::$SUCCESS;
            }
            $message = $e->getMessage();
        } catch (BusinessException $e) {        //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {               //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage().'---'.$e->getLine().'---'.$e->getTraceAsString();
        }

        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            if (in_array($code,[ErrCode::$BANK_FLOW_PARCEL_CLAIM_ADD_ERROR,ErrCode::$SYSTEM_ERROR])) {
                $logger->error('bank_flow_parcel_claim-create-failed:' . $real_message);
            } else {
                $logger->warning('bank_flow_parcel_claim-create-failed:' . $real_message);
            }
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $result ?? []
        ];
    }

    /**
    * 格式化数据和校验
    * @Date: 2021-09-16 14:23
    * @author: peak pan
    * @return:
    **/
    private function handleData($data)
    {
        if (empty($data) || !is_array($data)) {
            return 'data empty';
        }
        if ($this->isNotEndParcelClaim($data['pno'])) {
            throw new ValidationException('同步的数据已经存在一个相同的运单号了，请不要重复同步', ErrCode::$BANK_FLOW_PARCEL_CLAIM_FORM_ATTACHE_ERROR);
        }
        $data['pno'] = $data['pno'];
        $data['claim_type'] = $data['claim_type'];
        $data['pay_status'] =$data['pay_status']?$data['pay_status']:2;
        $data['currency'] = Enums\BankFlowEnums::CURRENCY_MONEY_SYMBOL;
        $data['real_amount'] = $data['claim_type']==Enums\BankFlowEnums::OUTLET_COMPENSATION?$data['real_claim_money']/100:0;
        $data['claim_money'] = $data['claim_type']==Enums\BankFlowEnums::CUSTOMER_COMPENSATION?$data['real_claim_money']/100:0;
        $data['status'] = Enums::CONTRACT_STATUS_APPROVAL;
        $data['is_cancel'] =Enums\BankFlowEnums::PK_IS_CANCEL;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = $data['created_at'];
        return $data;
    }


    /**
    * 查找库是否存在相同的运单号
    * @Date: 2021-09-16 14:12
    * @author: peak pan
    * @return:
    **/
    protected function isNotEndParcelClaim($pno)
    {
        $item = ParcelClaimModel::getFirst([
            'pno = :pno:',
            'bind' => ['pno' => $pno]
        ]);

        return $item?true:false;
    }

}
