<?php

namespace App\Modules\BankFlow\Services;

use App\Library\CInterface\BankFlowModelInterface;
use App\Library\CInterface\ParcelClaimModelInterface;
use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\PayEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowGetDetailModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Modules\BankFlow\Models\BankFlowPayDetailModel;
use App\Modules\Budget\Models\BudgetObjectProduct;
use App\Modules\Budget\Services\BudgetService;
use App\Modules\Budget\Services\LedgerAccountService;
use App\Modules\Cheque\Services\ChequeService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Loan\Models\Loan;
use App\Modules\Loan\Models\LoanPayBank;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentDetail;
use App\Modules\OrdinaryPayment\Models\OrdinaryPaymentExtend;
use App\Modules\Payment\Models\PaymentStoreRenting;
use App\Modules\Payment\Models\PaymentStoreRentingDetail;
use App\Modules\Purchase\Models\PurchaseApply;
use App\Modules\Purchase\Models\PurchaseOrder;
use App\Modules\Purchase\Models\PurchasePayment;
use App\Modules\Purchase\Models\PurchasePaymentReceipt;
use App\Modules\Reimbursement\Models\Detail;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Modules\ReserveFund\Models\ReserveFundApply;
use App\Modules\Salary\Models\PaySalaryApply;
use App\Modules\Wages\Models\WagesModel;
use App\Modules\BankFlow\Models\BankFlowParcelClaimModel;
use App\Modules\Wages\Services\AddService;
use App\Modules\Pay\Services\PayFlowService as PayPayFlowService;
use App\Library\Enums\BankFlowEnums;
use App\Models\oa\ChequeApplyDetailModel;
use App\Models\oa\PaymentStoreRentingDetailModel;
use App\Models\oa\OrdinaryPaymentModel;
use App\Models\oa\PurchasePaymentModel;


class PayFlowService extends BaseService
{
    // 交易日期最大时间段 默认 31 天
    public static $trade_date_max_between_days = 31;
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 获得导出模板的头，及字段
     * @return array
     */
    public function getImportTplAndField()
    {
        $field = [
            'oa_type',        //所属模块
            'no',             //费用单号
            'diff_amount',    //差异金额
            'diff_info',      //差异说明
        ];

        $header = [
            self::$t->_('bank_flow_field_oa_type'),
            self::$t->_('bank_flow_field_no'),
            self::$t->_("bank_flow_field_diff_amount"),
            self::$t->_("bank_flow_field_diff_info")
        ];
        return ['header' => $header, 'field' => $field];
    }


    /**
     * 获得导入模板
     * @return array
     */
    public function getImportTpl()
    {
        $header = $this->getImportTplAndField()['header'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $tpl_file_name = 'OA_TEMPLATE_' . date('Ymd') . '.xlsx';
            $res = $this->exportExcel($header, [], $tpl_file_name);
            $data = $res['data'];
        } catch (\Exception $e) {
            $this->logger->error('getImportTpl error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取导入后的数据
     * @param $data
     * @param $user
     * @return array
     */
    public function import($data, $user)
    {
        ini_set('memory_limit', '1024M');

        $code = ErrCode::$SUCCESS;
        $message = '';
        $res = [];
        $now = date("Y-m-d H:i:s");
        $field = $this->getImportTplAndField()['field'];
        try {
            $noToLine = [];
            $noToDiffAmount = [];
            $noToDiffInfo = [];
            $typeIdToNos = [];
            $nos = [];
            foreach ($data as $k => $v) {
                $tmp = array_combine($field, array_slice($v, 0, 4));
                $tmp['no'] = trim($tmp['no']);
                $line = $k + 2;   //第一行是表头，数组从0开始，所以+2
                if (empty($tmp)) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_error", ['line' => $line]));
                }
                $typeId = BankFlowEnums::$name_to_oa_type_id[$tmp['oa_type']] ?? 0;

                if (empty($typeId)) {
                    throw new ValidationException(
                        self::$t->_("bank_flow_line_data_not_found_oa_type", ['line' => $line])
                    );
                }
                if (isset($noToLine[$tmp['no']])) {
                    throw new ValidationException(
                        self::$t->_("bank_flow_line_data_repeat", ['line' => $line, 'where' => $noToLine[$tmp['no']]])
                    );
                }
                //非马来和菲律宾不可使用支票关联数据
                if (!in_array(get_country_code(), [GlobalEnums::MY_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE]) && in_array($typeId, [BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER])) {
                    throw new ValidationException(self::$t->_('bank_flow_my_or_ph_not_data'));
                }

                if (in_array($typeId, [BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY, BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT])) {
                    //租房付款和支票详情关联上传单号必须有下划线
                    if (!strstr($tmp['no'], '_')) {
                        throw new ValidationException(self::$t->_('bank_flow_dataupload_mast_glide_line'), ErrCode::$VALIDATE_ERROR);
                    }
                    //租房付款和支票详情关联上传单号只能有一个下划线
                    if (count(explode('_', $tmp['no'])) > 2) {
                        throw new ValidationException(self::$t->_('bank_flow_dataupload_multiple_glide_line'), ErrCode::$VALIDATE_ERROR);
                    }
                }

                //费用单号不能有下划线
                if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER && strstr($tmp['no'], '_')) {
                    throw new ValidationException(self::$t->_('bank_flow_dataupload_not_glide_line'), ErrCode::$VALIDATE_ERROR);
                }

                if (empty($typeIdToNos[$typeId])) {
                    $typeIdToNos[$typeId] = [];
                }
                $nos[]  = $tmp['no'];
                $noToLine[$tmp['no']] = $line;
                $typeIdToNos[$typeId][] = $tmp['no'];
                $noToDiffAmount[$tmp['no']] = $tmp['diff_amount'];
                $noToDiffInfo[$tmp['no']] = $tmp['diff_info'];
            }

            if (count($data) != count(array_unique($nos))) {
                throw new ValidationException(self::$t->_('bank_flow_dataupload_no_repeat'));
            }

            $res = $this->checkData($typeIdToNos, $noToLine,$noToDiffAmount,$noToDiffInfo);
            foreach ($res as &$item) {
                $item['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                $item['updated_id'] = $user['id'];
                $item['updated_at'] = $now;
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
            $res = [];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
            $this->logger->notice('pay_flow import error=' . $message);
            $message = static::$t->_('retry_later');
            $res = [];
        }
        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * 校验每行数据
     * @param $typeId
     * @param $no
     * @param $line
     * @param bool $is_submit 如果是提交，多返回一个model
     * @return array
     * @throws ValidationException
     */
    public function checkLineData($typeId, $no, $line, $is_submit = false)
    {
        $model = $this->getModelByTypeId($typeId, $line);
        $model = $model->getModelByNo($no);
        if (empty($model)) {
            throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line]));
        }

        $formatData = $model->getFormatData();
        if ($formatData['status'] != Enums::CONTRACT_STATUS_APPROVAL || $formatData['pay_status'] != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new ValidationException(self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line]));
        }
        $formatData['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$formatData['currency']]);
        if ($is_submit) {
            $formatData['model'] = $model;
        }
        return $formatData;
    }


    public function checkData($typeIdToNos, $noToLine,$noToDiffAmount,$noToDiffInfo)
    {
        $res = [];
        //是否需要包含已支付数据
        $has_pay = EnumsService::getInstance()->getSettingEnvValue('bank_flow_link_oa_prepaid') == 1;
        foreach ($typeIdToNos as $typeId => $nos) {
            $tmp = [];
            //如果是支票支付普通付款和采购需要分组处理
            if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER) {
                $group_arr =     ChequeService::getInstance()->chequeBusinessGroup($nos, 1);
            } else {
                $group_arr = [['module_info' =>$nos, 'type_id' => $typeId]];
            }

            //分组支票支付包含普通付款和采购
            foreach ($group_arr as $group) {
                $typeId = $group['type_id'];
                $nos = $group['module_info'];
                $model  = $this->getModelByTypeId($typeId);
                $models = $model->getModelByNos($nos, $has_pay, true);
                if ((is_object($models) && empty($models->toArray())) || (is_array($models) && !empty($models['msg']))) {
                    $msg = $models['msg'] ?? 'bank_flow_data_not_found';
                    throw new ValidationException(self::$t->_($msg), ErrCode::$VALIDATE_ERROR);
                }
                foreach ($models as $model) {
                    //对支票支付普通付款需要做特殊处理，普通付款可以是一个支票对应对个详情，也可以是多个支票对应一个普通付款
                    if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT) {
                        $tt = (new OrdinaryPaymentModel())->getFormatData($model);
                    } else {
                        $tt = $model->getFormatData();
                    }

                    $tt['diff_amount'] = $noToDiffAmount[$tt['no']] ?? '';
                    $tt['diff_info']   = $noToDiffInfo[$tt['no']] ?? '';
                    $tmp[]             = $tt;
                }
            }
            //如果不相等，就有不符合的找到报错
            if (count($tmp) != count($nos)) {
                $noToId = array_column($tmp, 'oa_value', 'no');
                foreach ($nos as $no) {
                    if (empty($noToId[$no])) {
                        throw new ValidationException(
                            self::$t->_(
                                "bank_flow_line_data_not_found_no",
                                ['no' => $no]
                            )
                        );
                    }
                }
            }
            $res = array_merge($res, $tmp);
        }
        return $res;
    }


    public function checkDataOnSubmit($typeIdToNos,$noToDiffAmount,$noToDiffInfo)
    {
        $res = [];
        //是否需要包含已支付数据
        $has_pay = EnumsService::getInstance()->getSettingEnvValue('bank_flow_link_oa_prepaid') == 1;

        foreach ($typeIdToNos as $typeId => $nos) {
            //如果是支票支付普通付款和采购需要分组处理
            if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_OTHER) {
                $group_arr = ChequeService::getInstance()->chequeBusinessGroup($nos, 1);
            } else {
                $group_arr = [['module_info' => $nos, 'type_id' => $typeId]];
            }


            //行关联的时候如果是租房付款支票号不能相同
            if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT) {
                $cheque_code_arr = array_map(function ($value) {
                    return explode('_', $value)[0];
                }, $nos);
                if (count($nos) != count(array_unique($cheque_code_arr))) {
                    throw new ValidationException(self::$t->_('bank_flow_cheque_code_not_repeat'), ErrCode::$VALIDATE_ERROR);
                }
            }

            $tmp = [];
            //分组支票支付包含普通付款和采购
            foreach ($group_arr as $group) {
                $typeId = $group['type_id'];
                $nos    = $group['module_info'];
                $model  = $this->getModelByTypeId($typeId);
                $models = $model->getModelByNos($nos, $has_pay, true);
                foreach ($models as $model) {
                    if ($typeId == BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT) {
                        $group_data = (new OrdinaryPaymentModel())->getFormatData($model);
                    } else {
                        $group_data                   = $model->getFormatData();
                    }
                    $group_data['diff_amount']    = $noToDiffAmount[$group_data['no']] ?? '';
                    $group_data['diff_info']      = $noToDiffInfo[$group_data['no']] ?? '';
                    $tmp[]                = $group_data;
                }
            }
            //如果不相等，就有不符合的找到报错
            if (count($tmp) != count($nos)) {
                $noToId = array_column($tmp, 'oa_value', 'no');
                foreach ($nos as $no) {
                    if (empty($noToId[$no])) {
                        throw new ValidationException(
                            self::$t->_(
                                "bank_flow_line_data_not_found_no",
                                ['no' => $no]
                            )
                        );
                    }
                }
            }

            if (empty($res[$typeId])) {
                $res[$typeId] = [];
            }
            $res[$typeId] = array_merge($res[$typeId], $tmp);
        }
        return $res;
    }


    /**
     * 根据模块id返回模块model  == 必须实现BankFlowModelInterface
     *
     * @param $typeId
     * @param $line integer 是否有行号
     * @return Loan
     * @throws ValidationException*@throws BusinessException
     * @throws BusinessException
     */
    public function getModelByTypeId($typeId, $line = 0)
    {
        static $arr = [];
        if (isset($arr[$typeId])) {
            return $arr[$typeId];
        }
        switch ($typeId) {
            case BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN:
                $model = new Loan();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT:
                $model = new Reimbursement();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT:
                $model = new PurchasePayment();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING:
                $model = new PaymentStoreRenting();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT:
                $model = new OrdinaryPayment();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND:
                $model = new ReserveFundApply();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE:
                $model = new WagesModel();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_SALARY:
                $model = new PaySalaryApply();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PARCEL_CLAIM:
                $model = new BankFlowParcelClaimModel();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_APPLY:
                $model = new ChequeApplyDetailModel();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT:
                $model = new PaymentStoreRentingDetailModel();
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_ORDINARY_PAYMENT:
                $model = new OrdinaryPaymentModel();//支票处理普通付款
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT_PURCHASE:
                $model = new PurchasePaymentModel();//支票处理采购
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_AGENCY_PAYMENT:
                $model = new AgencyPaymentDetailModel();//代理支付
                break;
            default:
                if (empty($line)) {
                    throw new ValidationException(self::$t->_("bank_flow_data_not_found_oa_type"));
                } else {
                    throw new ValidationException(
                        self::$t->_("bank_flow_line_data_not_found_oa_data", ['line' => $line])
                    );
                }
                break;
        }

        if (!$model instanceof BankFlowModelInterface && !$model instanceof ParcelClaimModelInterface) {
            throw new BusinessException('not implement BankFlowModelInterface');
        }
        $arr[$typeId] = $model;
        return $model;
    }

    /**
     * 银行流水关联系统单号
     * @param $params
     * @param $user
     * @return array
     */
    public function link($params, $user)
    {
        ini_set('memory_limit', '1024M');

        $code = ErrCode::$SUCCESS;
        $message = '';
        $res = [];
        $res['res'] = false;
        $res['diff_amount'] = 0;
        $res['msg'] = '';

        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $params['bank_flow_id']]
                ]
            );
            //为空，或者不是付款，或者已确认
            if (empty($flow) || $flow->type != BankFlowEnums::BANK_FLOW_TYPE_VALUE_PAY) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }

            GlobalEnums::init();

            $pay_amount = $flow->pay_amount;
            $currency = $flow->currency;
            $currency_text = self::$t->_(GlobalEnums::$currency_item[$currency]);
            $t_amount = 0;

            $now = date("Y-m-d H:i:s");

            $data = [];

            $noToLine = [];
            $noToDiffAmount = [];
            $noToDiffInfo = [];
            $typeIdToNos = [];

            $bankOaBatchKey = [
                'bank_flow_id',
                'oa_type',
                'oa_value',
                'no',
                'amount',
                'currency',
                'real_amount',
                'created_id',
                'created_at',
                'updated_id',
                'updated_at',
                'date',
                'bank_account_id',
                'diff_amount',
                'diff_info'
            ];

            foreach ($params['data'] as $k => $v) {
                $line = $k + 1;
                if (isset($noToLine[$v['no']])) {
                    throw new ValidationException(
                        self::$t->_("bank_flow_line_data_repeat", ['line' => $line, 'where' => $noToLine[$v['no']]])
                    );
                }
                if (empty($typeIdToNos[$v['oa_type']])) {
                    $typeIdToNos[$v['oa_type']] = [];
                }
                $typeIdToNos[$v['oa_type']][] = $v['no'];
                $noToLine[$v['no']] = $line;
                $noToDiffAmount[$v['no']] = $v['diff_amount'] ?? '';
                $noToDiffInfo[$v['no']] = $v['diff_info'] ?? '';
            }
            $type_id_to_format_data = $this->checkDataOnSubmit($typeIdToNos, $noToDiffAmount, $noToDiffInfo);
            $this->logger->info('银行流水 - 批量关联系统单号 - checkDataOnSubmit结果: ' . json_encode($type_id_to_format_data, JSON_UNESCAPED_UNICODE));
            //是否不同货币额
            $is_diff_currency = false;
            //v17471 只有非'已支付'状态的单据才需要改支付状态为'已支付', '已支付'状态的只做业务关联
            $to_be_paid = $to_be_pay_no = [];
            $country = get_country_code();
            foreach ($type_id_to_format_data as $type_id => $format_data) {
                foreach ($format_data as $item) {
                    $item['updated_id'] = $user['id'];
                    $item['updated_at'] = $now;
                    $item['created_id'] = $user['id'];
                    $item['created_at'] = $now;
                    $item['bank_flow_id'] = $params['bank_flow_id'];
                    $item['real_amount'] = GlobalEnums::getAmountByDefaultRate(
                        $item['amount'],
                        $item['currency'],
                        $currency
                    );
                    if($item['currency'] != $currency){
                        $is_diff_currency = true;
                    }

                    $t_amount = bcadd($t_amount, $item['real_amount'], 2);
                    $item['date'] = $flow->date;
                    $item['bank_account_id'] = $flow->bank_account_id;

                    //差异金额
                    if(!empty($item['diff_amount'])){
                        $item['diff_amount'] = bcadd('0', $item['diff_amount'], 2);
                    }else{
                        $item['diff_amount'] = null;
                    }
                    $item['diff_info'] = $item['diff_info'] ?? '';
                    $data[] = array_only($item, $bankOaBatchKey);

                    //如果是菲律宾，上传的订单类型为支票支付普通付款，采购和租房，主订单支付状态必须是已支付状态
                    if ($country == GlobalEnums::PH_COUNTRY_CODE && in_array($item['oa_type'], array_keys(BankFlowEnums::$cheque_account_type_relationship))) {
                        if ($item['pay_status'] != Enums::PAYMENT_PAY_STATUS_PAY) {
                            throw new ValidationException('bank_flow_main_order_pay_status_err', ErrCode::$VALIDATE_ERROR);
                        } else {
                            $to_be_paid[$type_id][]   = intval($item['oa_value']);
                        }
                    } else {
                        if ($item['pay_status'] != Enums::PAYMENT_PAY_STATUS_PAY) {
                            $to_be_paid[$type_id][]   = intval($item['oa_value']);
                            $to_be_pay_no[$type_id][] = $item['no'];
                        }
                    }
                }
            }

            $this->logger->info('银行流水 - 批量关联系统单号 - is_diff_currency: ' . $is_diff_currency);

            if ($is_diff_currency) {
                //有差额，并且是校验，就直接返回。
                $res['diff_amount'] = bcsub($pay_amount, $t_amount, 2);
                //如果不是0.00，并且是校验
                if ($res['diff_amount'] != '0.00' && $params['is_check']) {
                    $res['diff_amount'] = abs($res['diff_amount']);
                    $res['msg'] = self::$t->_(
                        "bank_flow_not_eq_oa_error",
                        [
                            'amount' => number_format($res['diff_amount'], 2),
                            'currency' => $currency_text
                        ]
                    );
                    throw new ValidationException('have diff amount', ErrCode::$BANK_FLOW_HAVE_DIFF_AMOUNT);
                }
            } else {

                foreach ($data as $k=>$v) {
                    $line = $k+1;
                    //如果差异金额不为null，则必须填写差异说明
                    if (isset($v['diff_amount']) && strlen($v['diff_info']) == 0) {
                        throw new ValidationException(
                            self::$t->_("bank_flow_line_data_have_diff_amount_no_info", ['line' => $line])
                        );
                    }
                    //这肯定是相同的货币额类型，所以不用转换
                    $t_amount  = bcadd($t_amount,$v['diff_amount'],2);
                }
                $res['diff_amount'] = bcsub($pay_amount, $t_amount, 2);
                if ($res['diff_amount'] != '0.00') {
                    $res['diff_amount'] = abs($res['diff_amount']);
                    $res['msg']         = self::$t->_(
                        "bank_flow_not_eq_oa_error_on_same_currency",
                        [
                            'amount'   => number_format($res['diff_amount'], 2),
                            'currency' => $currency_text
                        ]
                    );
                    //直接报错
                    throw new ValidationException($res['msg']);
                }
            }

            $this->logger->info('银行流水 - 批量关联系统单号 - 待批量入库数据: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

            $flow_oa = new BankFlowOaModel();
            $bank_flow_oa_arr = [];
            foreach ($data as $flow_oa_data) {
                if ($flow_oa_data['oa_type'] != BankFlowEnums::BANK_FLOW_OA_TYPE_CHEQUE_ACCOUNT) {
                    $bank_flow_oa_arr[] = $flow_oa_data;
                }
            }

            if (!empty($bank_flow_oa_arr)) {
                $flow_oa_batch_insert_res = $flow_oa->batch_insert($bank_flow_oa_arr);
                $this->logger->info('银行流水 - 批量关联系统单号 - 批量入库结果: ' . $flow_oa_batch_insert_res);
                if ($flow_oa_batch_insert_res === false) {
                    throw new BusinessException('银行流水 - 批量关联系统单号 - 批量入库失败[bank_flow_oa]', ErrCode::$BUSINESS_ERROR);
                }
            }

            $bankFlowData = [];
            $bankFlowData['create_id'] = $user['id'];
            $bankFlowData['create_name'] = $this->getNameAndNickName($user['name'], $user['nick_name']);
            $bankFlowData['create_department_name'] = $user['department'];
            $bankFlowData['create_job_title_name'] = $user['job_title'];
            $bankFlowData['bank_id'] = $flow->bank_id;
            $bankFlowData['bank_name'] = $flow->bank_name;
            $bankFlowData['bank_account'] = $flow->bank_account;
            $bankFlowData['date'] = $flow->date;
            $bankFlowData['ticket_no'] = $flow->ticket_no ?? '';
            $bankFlowData['data']['id'] = $flow->id;
            $bankFlowData['data']['date'] = $flow->date;

            $bankFlowData['order_info'] = $data;
            $bankFlowData['link_data'] = $params;
            $bankFlowData['is_link'] =  1;//区分是行上的link还是头上的批量
            $this->logger->info('银行流水 - 批量关联系统单号 - 业务侧待更新数据: ' . json_encode($bankFlowData, JSON_UNESCAPED_UNICODE));
            $this->logger->info('银行流水 - 批量关联系统单号 - 本次需要支付的单据: ' . json_encode($to_be_paid, JSON_UNESCAPED_UNICODE));
            //批量支付
            if (empty($to_be_paid)) {
                throw new ValidationException(self::$t->_('payment_pay_status_err'), ErrCode::$VALIDATE_ERROR);
            }
            $model = '';
            foreach ($to_be_paid as $typeId => $ids) {
                $this->logger->info('银行流水 - 批量关联系统单号 - 业务侧待更新数据对应的ids: ' . json_encode($ids, JSON_UNESCAPED_UNICODE));
                $model = $this->getModelByTypeId($typeId);
                $model->batch_link($ids, $bankFlowData);
            }

            //批量更新支付模块状态
            if (EnumsService::getInstance()->getSystemPayModuleStatus()) {
                $pay_flow_service = new \App\Modules\Pay\Services\PayFlowService();
                foreach ($to_be_pay_no as $type_id => $nos) {
                    $this->logger->info('银行流水 - 批量关联系统单号 - 业务侧待更新数据对应的nos: ' . json_encode($nos, JSON_UNESCAPED_UNICODE));
                    // 支付模块批量更新
                    $pay_flow_service->autoUpdatePayStatus($user, $type_id, $nos);
                }
            }

            //保留confirm_status=2,表示关联过系统单号
            $flow->confirm_status = BankFlowEnums::BANK_FLOW_CONFIRMED_STATUS;
            $flow->save();
            $db->commit();
            $res['res'] = true;
        } catch (ValidationException $e) {
            $db->rollback();
            //如果是有差额，返回code=1给前端，让前端帮忙提示窗口
            $code = $e->getCode();
            if ($code == ErrCode::$BANK_FLOW_HAVE_DIFF_AMOUNT) {
                $code = ErrCode::$SUCCESS;
            } else {
                $code = ErrCode::$VALIDATE_ERROR;
                $message = $e->getMessage();
            }
        } catch (BusinessException $e) {
            $db->rollback();
            $this->logger->warning(
                'bank_flow link error (BusinessException) : ' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString()
            );
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error(
                'bank_flow link error (Exception) : ' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString()
            );

            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $res];
    }


    /**
     * 银行流水关联系统单号列表
     * @param $params
     * @param $user
     * @return array
     */
    public function getOAList($bank_flow_id)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }

            $data = BankFlowOaModel::find(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            )->toArray();


            foreach ($data as &$item) {
                $item['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$item['currency']]);
                $item['is_cancel'] = (boolean)$item['is_cancel'];
            }
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow getOAList error==' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString(
                )
            );
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }


    /**
     * 银行流水关联系统单号列表
     * @param $id
     * @param $bank_flow_id
     * @param $user
     * @return array
     */
    public function cancelOA($id, $bank_flow_id, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = false;
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //必须是没撤销的
            $item = BankFlowOaModel::findFirst([
                'conditions' => 'id = :id: and bank_flow_id = :bank_flow_id: and is_cancel=0',
                'bind' => ['id' => $id, 'bank_flow_id' => $bank_flow_id]
            ]);
            if (empty($item)) {
                throw new ValidationException(self::$t->_('bank_flow_oa_not_found'), ErrCode::$VALIDATE_ERROR);
            }

            //16325通过pay关联的系统单号不允许撤回
            if ($item->created_id == Enums\StaffInfoEnums::SUPER_ADMIN_STAFF_ID) {
                throw new ValidationException(self::$t->_('bank_flow_oa_cannot_cancel'), ErrCode::$VALIDATE_ERROR);
            }

            $model = $this->getModelByTypeId($item->oa_type);
            $model = $model->getModelByNo($item->no);
            if (empty($model)) {
                throw new ValidationException(self::$t->_('bank_flow_data_not_found_oa_type'), ErrCode::$VALIDATE_ERROR);
            }
            $model->cancel($user);
            //撤回支付模块状态
            if (EnumsService::getInstance()->getSystemPayModuleStatus()) {
                (new PayPayFlowService())->cancelPay($item->oa_type, $item->no, $item);
            }
            $item->is_cancel = 1;
            $item->updated_id = $user['id'];
            $item->updated_at = date("Y-m-d H:i:s");
            $item->save();

            $data = true;

            $db->commit();
        } catch (ValidationException $e) {
            $db->rollback();
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning('bank_flow getOAList error==' . $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString());
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }

    /**
     * 获得银行流水 费用明细列表
     * @param $bank_flow_id
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getDetailRemarkList($bank_flow_id, $pageNum, $pageSize)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];

        try {
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }
            $count = BankFlowPayDetailModel::count(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id],
                ]
            );
            $items = BankFlowPayDetailModel::find(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id],
                    'limit' => $pageSize,
                    'offset' => ($pageNum - 1) * $pageSize
                ]
            )->toArray();
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page' => $pageSize,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow getDetailRemarkList error==' . $e->getMessage() . '---' . $e->getLine(
                ) . '---' . $e->getTraceAsString()
            );
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }


    /**
     * 获得明细备注导出模板的头，及字段
     * @return array
     */
    public function getDetailRemarkTplAndField()
    {
        $field = [
            'exv_id',                  //EXV号
            'apply_amount',            //申请金额
            'no',                      //差额报销单号
            'diff_amount',             //差额金额
            'apply_staff_id',          //申请人工号
            'store',                   //网点
            'link_name',               //关联方名称
        ];

        $header = [
            self::$t->_('bank_flow_field_exv_id'),
            self::$t->_('bank_flow_field_apply_amount'),
            self::$t->_('bank_flow_field_diff_no'),
            self::$t->_('bank_flow_field_diff_amount'),
            self::$t->_('global.applicant.id'),
            self::$t->_('global.branch'),
            self::$t->_('bank_flow_field_link_name')
        ];
        return ['header' => $header, 'field' => $field];
    }


    /**
     * 获得明细备注导出模板
     * @return array
     */
    public function getDetailRemarkTpl()
    {
        $header = $this->getDetailRemarkTplAndField()['header'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $res = $this->exportExcel($header, []);
            $data = $res['data'];
        } catch (\Exception $e) {
            $this->logger->error('getDetailRemarkTpl error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 导入明细备注
     * @param $bank_flow_id
     * @param $data
     * @param $user
     * @return array
     */
    public function importDetailRemark($bank_flow_id, $data, $user)
    {
        $db = $this->getDI()->get('db_oa');
        $filed = $this->getDetailRemarkTplAndField()['field'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $res = false;
        try {
            $db->begin();

            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            $exv_ids = array_column($data, 0);
            if (count($exv_ids) != count(array_unique($exv_ids))) {
                throw new ValidationException(self::$t->_("bank_flow_detail_remark_exv_id_repeat_1"));//上传数据中，EXV号XXXXX重复，请检查文档
            }
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }


            $batchData = [];
            $exvIdToLine = [];
            $exvIds = [];
            $amount = 0;
            $now = date("Y-m-d H:i:s");

            foreach ($data as $k => $v) {
                $tmp = array_combine($filed, $v);
                $line = $k + 2;
                if (empty($tmp)) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_error", ['line' => $line]));
                }

                //exv_id必填
                if(empty($tmp['exv_id'])){
                    throw new ValidationException(self::$t->_("bank_flow_line_data_error", ['line' => $line]));
                }

                if (isset($exvIdToLine[$tmp['exv_id']])) {
                    throw new ValidationException(
                        self::$t->_(
                            "bank_flow_line_data_repeat",
                            ['line' => $line, 'where' => $exvIdToLine[$tmp['exv_id']]]
                        )
                    );
                }
                $exvIdToLine[$tmp['exv_id']] = $line;
                $exvIds[] = $tmp['exv_id'];

                $amount = bcadd($amount, $tmp['apply_amount'], 2);

                $tmp['created_id'] = $user['id'];
                $tmp['created_at'] = $now;
                $tmp['bank_flow_id'] = $bank_flow_id;

                $batchData[] = $tmp;
            }

            if (bccomp($flow->pay_amount, $amount, 2) != 0 && bccomp($flow->get_amount, $amount, 2) != 0) {
                throw new ValidationException(self::$t->_("bank_flow_amount_not_equal_detail_remark"));
            }


            $payDetailList = $flow->getPayDetail();
            if (!empty($payDetailList)) {
                $payDetailList->delete();
            }

            $temp = BankFlowPayDetailModel::find(
                [
                    'conditions' => 'exv_id in ({ids:array})',
                    'bind' => ['ids' => $exvIds]
                ]
            )->toArray();
            if (!empty($temp)) {
                $exv_id_tips = '';
                //查询银行账号及信息
                $bank_flow_arr = BankFlowModel::find(
                    [
                        'conditions' => 'id in ({ids:array})',
                        'bind'       => ['ids' => array_column($temp, 'bank_flow_id')],
                        'columns'    => 'id,bank_account,bank_left_amount,date'
                    ]
                )->toArray();
                $isset_exv_ids = array_column($temp, 'exv_id', 'bank_flow_id');

                foreach ($bank_flow_arr as $bank_flow_item) {
                    $exv_id_tips .= 'EXV No:' . $isset_exv_ids[$bank_flow_item['id']] . ',Bank Account:' . $bank_flow_item['bank_account'] .'Balance:'.$bank_flow_item['bank_left_amount']. ',Time:' . $bank_flow_item['date'];
                }
                throw new ValidationException(self::$t->_("bank_flow_detail_remark_exv_id_repeat").$exv_id_tips);
            }

            $model = new BankFlowPayDetailModel();
            $flag = $model->batch_insert($batchData);
            if ($flag === false) {
                $str = '';
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $str .= $message . ";";
                }
                throw new BusinessException('批量插入银行流水付款备注失败==' . $str);
            }
            $db->commit();
            $res = true;
        } catch (ValidationException $e) {
            $db->rollback();
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('importDetailRemark error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $res
        ];
    }


    public function getExpenseList($type, $condition, $is_export = false)
    {
        $page_size = empty($condition['pageSize']) ? 20 : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? 1 : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            $condition['company_name'] = $condition['company_name']??'';
            $condition['bank_account_id'] = $condition['bank_account_id']??'';
            //银行账号
            if (empty($condition['bank_account_id']) && !empty($condition['company_name']) ){
                $bank_accounts = BankAccountModel::find([
                    'conditions' => 'company_name = :company_name:',
                    'bind' => ['company_name' => $condition['company_name']],
                    'columns' => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
                $condition['bank_account_id'] = $bank_account_id_arr;
            }
            $builder = $this->modelsManager->createBuilder();
            $builder->from(['bfo' => BankFlowOaModel::class]);
            $builder->join(BankFlowModel::class, 'bfo.bank_flow_id = bf.id', 'bf');
            $builder->andWhere('bfo.oa_type = :type:', ['type' => $type]);
            if (!empty($condition['bank_account_id'])) {
                if (is_array($condition['bank_account_id'])){
                    $builder->andWhere(
                        'bfo.bank_account_id in ({bank_account_id:array})',
                        ['bank_account_id' => $condition['bank_account_id']]
                    );
                }else{
                    $builder->andWhere(
                        'bfo.bank_account_id = :bank_account_id:',
                        ['bank_account_id' => intval($condition['bank_account_id'])]
                    );
                }
            }
            if (isset($condition['start_date'])) {
                $builder->andWhere('bfo.date >= :start_date:', ['start_date' => $condition['start_date']]);
            }
            if (isset($condition['end_date'])) {
                $builder->andWhere('bfo.date <= :end_date:', ['end_date' => $condition['end_date']]);
            }
            $builder = $this->getCondition($builder, $type);
            $count = $builder->getQuery()->execute()->count();
            if (!$is_export) {
                $builder->limit($page_size, $offset);
            }
            $items = $builder->getQuery()->execute()->toArray();
            $items = $this->handleItems($items, $type);
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $page_num,
                    'per_page' => $page_size,
                    'total_count' => $count,
                ]
            ];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $logger = $this->getDI()->get('logger');
            $logger->error('bank_flow-getExpenseList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    public function getCondition($builder, $type)
    {
        //单号,付款日期,付款账号，支票号，费用类型,是否撤销,币种,付款金额
        $columns = 'bfo.no,bfo.date,bf.bank_account,bf.ticket_no,bf.bank_flow_expense_id,bfo.is_cancel,bf.currency,bf.pay_amount';

        switch ($type) {
            case BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN:
                //申请人工号，费用部门，费用中心名字，申请日期，金额，说明
                //收款人姓名，收款人账号，收款人开户银行。
                $columns .= ',m.create_id,m.create_department_name,m.cost_center_name,m.create_date,m.amount,m.event_info,
                lpb.name as receive_name,lpb.account as receive_account,lpb.bank_type as receive_bank';
                $builder->leftjoin(Loan::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(LoanPayBank::class, 'm.id=lpb.loan_id', 'lpb');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT:
                $columns .= ',m.bank_name as receive_name,m.bank_account as receive_account,m.bank_type as receive_bank,
                m.amount,m.real_amount,m.loan_amount,m.apply_id as create_id,m.cost_department_name as create_department_name,d.cost_store_n_name as create_store_name,
                d.cost_center_code,d.start_at,d.end_at,d.budget_id,d.product_id,d.ledger_account_id,d.info as event_info,d.invoices_ids,d.tax,d.tax_not,d.rate,d.amount as detail_amount,d.is_deduct,d.id,d.wht_tax,d.wht_tax_amount,
                d.deductible_vat_tax as deduct_rate,d.deductible_tax_amount as deduct_amount,d.payable_amount';
                $builder->leftjoin(Reimbursement::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(Detail::class, 'd.re_id = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT:
                $columns .= ',m.bank_account_name as receive_name,m.cur_amount,m.real_amount,m.receipt_amount,m.not_tax_amount,m.vat7_amount,m.wht_amount,m.ponos,m.create_department_name,m.cost_store_name as create_store_name,m.vendor,m.remark,
                m.bank_name as receive_bank,m.bank_no as receive_account,m.swift_code,m.bank_account_name,m.currency as apply_currency,
                d.id,d.ticket_date,d.budget_id,d.product_id,d.product_name,d.ledger_account_id,d.total,d.not_tax_price,d.total_price,d.tax_ratio,d.tax_total_price,d.wht_type,d.wht_ratio,d.wht_amount as detail_wht_amount,d.real_amount as detail_real_amount,
                d.is_deduct,d.deductible_vat_tax as deduct_rate,d.deductible_vat_amount as deduct_amount';
                $builder->leftjoin(PurchasePayment::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(PurchasePaymentReceipt::class, 'd.ppid = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING:
                $columns .= ',d.id,d.is_deduct,d.bank_account_name as receive_name,d.bank_account_no as receive_account,d.bank_name as receive_bank,d.store_name,d.cost_start_date,d.cost_end_date,d.cost_type,d.amount,
                d.vat_rate,d.vat_amount,d.amount_has_tax,d.wht_category,d.wht_tax_rate,d.wht_amount,d.actually_amount,
                m.tax_total_amount,m.actually_total_amount,m.vat_total_amount,m.wht_total_amount,m.total_amount,m.create_id,m.cost_center_department_name,m.cost_center_name';
                $builder->leftjoin(PaymentStoreRenting::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(PaymentStoreRentingDetail::class, 'd.store_renting_id = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT:
                $columns .= ',d.id,d.is_deduct,d.cost_store_name,d.cost_center_name,d.cost_start_date,d.cost_end_date,d.budget_id,d.product_id,d.ledger_account_id,d.amount_no_tax,d.amount_vat,d.wht_category,d.wht_rate,d.amount_wht,d.amount_have_tax,
                d.deductible_vat_tax as deduct_rate,d.deductible_tax_amount as deduct_amount,
                de.supplier_bk_account_name as receive_name,de.supplier_bk_account as receive_account,de.supplier_bk_name as receive_bank,
                m.amount_total_have_tax,m.amount_total_actually,m.amount_discount,m.amount_total_no_tax,m.amount_total_vat,m.amount_total_wht,m.apply_id,m.cost_department_name';

                $builder->leftjoin(OrdinaryPayment::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(OrdinaryPaymentDetail::class, 'd.ordinary_payment_id = m.id', 'd');
                $builder->leftjoin(OrdinaryPaymentExtend::class, 'de.ordinary_payment_id = m.id', 'de');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND:
                $columns .= ',m.payee_username as receive_name,m.payee_account as receive_account,m.payee_bank as receive_bank,
                m.create_id,m.create_department_name,m.create_store_name,m.apply_date as create_date,m.amount,m.apply_reason as event_info';
                $builder->leftjoin(ReserveFundApply::class, 'bfo.oa_value = m.id', 'm');
                break;
            default:
                throw new BusinessException('not found type');
                break;
        }
        $builder->columns($columns);
        return $builder;
    }


    /**
     * 处理每行数据
     * @param $items
     * @param $type
     * @return array
     */
    public function handleItems($items, $type)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }

        $expenseList      = InitFlowService::getInstance()->getExpenseTypeList("pay_list");
        $expenseIdToLabel = array_column($expenseList, 'label', 'value');

        $ledgerIdToName = [];
        $budgets        = [];
        $products       = [];

        $myLang = strtolower(
            substr(static::$language, -2)
        );

        if (!in_array($myLang, ['en', 'th', 'cn'])) {
            $myLang = 'en';
        }

        if (in_array(
            $type,
            [
                BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
                BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
                BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT
            ]
        )) {
            $budgetIds = array_values(array_filter(array_column($items, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }

            $productIds = array_values(array_filter(array_column($items, 'product_id')));
            if ($productIds) {
                $products = BudgetObjectProduct::find(
                    [
                        'conditions' => ' id in ({ids:array})',
                        'bind'       => ['ids' => $productIds]
                    ]
                )->toArray();
                $products = array_column($products, null, 'id');
            }

            //核算科目名字
            $ledger_account_ids = array_values(array_filter(array_column($items, 'ledger_account_id')));
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'], 'name', 'id');
                }
            }
        }

        // 网点租房付款费用类型配置
        $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        foreach ($items as &$item) {
            // 币种
            $item['currency_text'] = static::$t[GlobalEnums::$currency_item[$item['currency']]];
            //费用类型
            $item['bank_flow_expense_text'] = '';
            if (!empty($expenseIdToLabel[$item['bank_flow_expense_id']])) {
                $item['bank_flow_expense_text'] = $expenseIdToLabel[$item['bank_flow_expense_id']];
            }
            $item['is_cancel_text'] = '';
            if ($item['is_cancel']) {
                $item['is_cancel_text'] = 'Y';
            }

            if (isset($item['is_deduct'])) {
                $item['is_deduct_text'] = '';
                if ($item['is_deduct']) {
                    $item['is_deduct_text'] = 'Y';
                }
            }

            $item['receive_name']    = $item['receive_name'] ?? '';
            $item['receive_account'] = $item['receive_account'] ?? '';
            $item['receive_bank']    = $item['receive_bank'] ?? '';

            if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN || $type == BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND) {
                //申请金额
                $item['apply_amount'] = bcdiv($item['amount'], 1000, 2);
                //可抵扣税率
                $item['deduct_rate'] = '0%';
                //可抵扣税金额
                $item['deduct_amount'] = '0.00';
            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT) {
                //含税金额总计
                $item['amount'] = bcdiv($item['amount'], 1000, 2);
                //实付金额总计
                $item['real_amount'] = bcdiv($item['real_amount'], 1000, 2);
                //冲减借款金额总计
                $item['loan_amount']   = bcdiv($item['loan_amount'], 1000, 2);
                $item['tax_not']       = bcdiv($item['tax_not'], 1000, 2);
                $item['tax']           = bcdiv($item['tax'], 1000, 2);
                $item['detail_amount'] = bcdiv($item['detail_amount'], 1000, 2);

                $item['re_budget_text'] = '';
                if (isset($budgets) && isset($budgets[$item['budget_id']])) {
                    $item['re_budget_text'] = $budgets[$item['budget_id']]['name_' . $myLang];
                }

                $item['product_name'] = '';
                if ($item['product_id']) {
                    $item['product_name'] = isset($products) && isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . $myLang] : $item['product_name'];
                }
                //核算科目
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';

                //发生期间
                $item['happen_at'] = $item['start_at'] . '-' . $item['end_at'];

                $item['create_store_name'] = $item['create_store_name'] ?? '';


                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }
                //wht 税率税额
                $item['wht_tax']        = empty($item['wht_tax']) ? '0%' : $item['wht_tax'] . '%';
                $item['wht_tax_amount'] = bcdiv($item['wht_tax_amount'], 1000, 2);

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT) {
                $item['budget_text'] = '';
                if (isset($budgets) && isset($budgets[$item['budget_id']])) {
                    $item['budget_text'] = $budgets[$item['budget_id']]['name_' . $myLang];
                }
                $item['product_name'] = '';
                if ($item['product_id']) {
                    $item['product_name'] = isset($products) && isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . $myLang] : $item['product_name'];
                }
                //核算科目
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';

                //本次付款金额
                $item['cur_amount'] = bcdiv($item['cur_amount'], 1000, 2);
                //总实付金额
                $item['real_amount'] = bcdiv($item['real_amount'], 1000, 2);
                //发票金额总计
                $item['receipt_amount'] = bcdiv($item['receipt_amount'], 1000, 2);
                //不含税金额总计
                $item['not_tax_amount'] = bcdiv($item['not_tax_amount'], 1000, 2);
                //VAT金额总计
                $item['vat7_amount'] = bcdiv($item['vat7_amount'], 1000, 2);
                //WHT金额总计
                $item['wht_amount'] = bcdiv($item['wht_amount'], 1000, 2);
                //不含税单价
                $item['not_tax_price'] = bcdiv($item['not_tax_price'], 10000, 4);
                //不含税金额
                $item['total_price'] = bcdiv($item['total_price'], 1000, 2);
                //vat7金额
                $item['tax_ratio'] = bcdiv($item['tax_ratio'], 1000, 2);
                //vat7税率
                $item['vat7_rate'] = '0';
                if (!empty($item['total_price'])) {
                    $item['vat7_rate'] = '' . (round($item['tax_ratio'] / $item['total_price'], 2) * 100) . '%';
                }
                //含税金额
                $item['tax_total_price'] = bcdiv($item['tax_total_price'], 1000, 2);
                $item['wht_type_text']   = $wht_cat_map[$item['wht_type']] ?? '';
                $item['wht_ratio']       .= "%";
                //wht金额
                $item['detail_wht_amount'] = bcdiv($item['detail_wht_amount'], 1000, 2);
                //总实付金额
                $item['detail_real_amount'] = bcdiv($item['detail_real_amount'], 1000, 2);

                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }
                $item['apply_currency_text'] = static::$t[GlobalEnums::$currency_item[$item['apply_currency']]];

                $item['order_total'] = $item['total'];
            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING) {
                $item['vat_rate']       .= '%';
                $item['wht_tax_rate']   .= '%';
                $item['cost_type_text'] = static::$t->_($store_rent_payment_cost_enums[$item['cost_type']]);
                $item['happen_at']      = $item['cost_start_date'] . '-' . $item['cost_end_date'];

                //可抵扣税率
                $item['deduct_rate'] = '0%';

                //可抵扣税金额
                $item['deduct_amount'] = '0.00';

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT) {
                $item['budget_text']       = isset($budgets[$item['budget_id']]) ? $budgets[$item['budget_id']]['name_' . $myLang] : '';
                $item['product_name']      = isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . $myLang] : '';
                $item['wht_category_text'] = $wht_cat_map[$item['wht_category']] ?? 0;
                //核算科目
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';


                $item['vat_rate'] = '0';
                if (!empty($item['amount_no_tax']) && !empty($item['amount_vat'])) {
                    $item['vat_rate'] = round($item['amount_vat'] / $item['amount_no_tax'] * 100);
                }
                $item['vat_rate'] .= '%';

                $item['wht_rate'] .= '%';

                $item['happen_at'] = $item['cost_start_date'] . '-' . $item['cost_end_date'];
                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }
            }
        }
        return $items;
    }


    public function exportExpenseList($type, $condition)
    {
        ini_set('memory_limit', '512M');

        $list = $this->getExpenseList($type, $condition, true);
        if ($list['code'] != ErrCode::$SUCCESS) {
            return $list;
        }

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = '';

        try {
            $items = $list['data']['items'];
            $exportData = [];
            $tmp = $this->getHeaderAndFieldByType($type);
            $header = $tmp['header'];
            $fields = $tmp['field'];
            foreach ($items as $item) {
                $tmp = [];
                foreach ($fields as $field) {
                    $tmp[] = $item[$field] ?? '';
                }
                $exportData[] = $tmp;
            }


            $biz_type_id_item = array_flip(BankFlowEnums::$name_to_oa_type_id);
            $file_name = $biz_type_id_item[$type] ?? 'expense_export';
            $file_name .=  '_' . date('Ymd') . '.xlsx';

            $res = $this->exportExcel($header, $exportData, $file_name);
            $data = $res['data'];
            $code = $res['code'];
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('bank_flow-exportExpenseList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    public function getHeaderAndFieldByType($type)
    {
        $header = [];
        $field = [];

        $lang = static::$t;
        switch ($type) {
            case BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN:
                $field = [
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'receive_name',     //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回
                    'create_id',        //申请人工号,
                    'create_department_name',   //费用所属部门
                    'cost_center_name', //费用成本中心
                    'create_date',      //申请日期,
                    'apply_amount',     //申请金额
                    'event_info',       //说明
                ];
                foreach ($field as $item) {
                    $header[] = $lang->_("bank_flow_export_field_" . $item);
                }
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT:
                $field = [
                    'is_deduct_text',   //可抵扣是否确认
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'receive_name',     //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回
                    'amount',            //含税金额总计
                    'loan_amount',       //冲减借款金额总计
                    'real_amount',       //实付金额总计
                    'create_id',         //申请人工号,
                    'create_department_name',   //费用所属部门
                    'create_store_name',   //费用所属网点
                    'cost_center_name',    //费用成本中心

                    'happen_at',           //费用发生期间
                    're_budget_text',      //报销实质
                    'product_name',        //费用明细/费用类型,
                    'ledger_account_text', //核算科目
                    'event_info',          //说明
                    'invoices_ids',        //发票编号
                    'tax_not',             //不含税金额,
                    'tax',                 //税额,
                    'rate',                //税率
                    'wht_tax',             //wht税率
                    'wht_tax_amount',       //wht税额
                    'detail_amount',       //含税金额
                    'deduct_rate',         //可抵扣税率,
                    'deduct_amount',       //可抵扣税金额
                ];
                foreach ($field as $k=>$item) {
                    $header[] = $lang->_("bank_flow_export_field_" . $item);
                    //列表里的是这个
                    if($item =='ledger_account_text'){
                        $field[$k] = 'ledger_account_name';
                    }
                }
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT:
                $field = [
                    'is_deduct_text',   //可抵扣是否确认
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'bank_account_name', //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'receive_name',     //收款人信息
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回
                    'apply_currency_text',//申请单币种
                    'cur_amount',       //本次付款金额
                    'real_amount',      //总实付金额
                    'receipt_amount',   //发票金额总计
                    'not_tax_amount',   //不含税金额总计
                    'vat7_amount',      //vat7金额总计
                    'wht_amount',       //wht金额总计
                    'ponos',            //po单号
                    'create_department_name',   //费用所属部门
                    'create_store_name', //费用所属网点
                    'vendor',           //供应商名称
                    'remark',           //备注
                    'ticket_date',      //发票日期
                    'budget_text',      //预算分类
                    'product_name',      //产品名称
                    'ledger_account_text',  //核算科目
                    'order_total',      //订单数量
                    'not_tax_price',    //不含税单价
                    'total_price',      //不含税金额
                    'vat7_rate',        //vat7税率
                    'tax_ratio',        //vat7金额
                    'tax_total_price',  //含税金额
                    'wht_type_text',    //wht类别
                    'wht_ratio',        //wht税率
                    'detail_wht_amount',       //wht金额
                    'detail_real_amount',    //实付金额
                    'deduct_rate',      //可抵扣税率
                    'deduct_amount'     //可抵扣金额
                ];
                foreach ($field as $k=>$item) {
                    //这个是翻译不对，需要换成real_amount总实付金额总计
                    if($item == 'detail_real_amount'){
                        $item = 'real_amount';
                    }
                    $header[] = $lang->_("bank_flow_export_field_" . $item);
                    //列表里的是这个
                    if($item =='ledger_account_text'){
                        $field[$k] = 'ledger_account_name';
                    }
                }
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING:

                $field = [
                    'is_deduct_text',   //可抵扣是否确认
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'receive_name',     //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回

                    'tax_total_amount', //含税金额总计
                    'actually_total_amount',//实付金额总计
                    'vat_total_amount',     //vat7金额总计
                    'wht_total_amount',     //wht金额总计
                    'total_amount',      //不含税金额
                    'create_id',         //申请人工号
                    'cost_center_department_name',  //费用所属部门
                    'store_name',       //网点名称
                    'happen_at',        //费用发生期间
                    'cost_type_text',   //费用类型
                    'amount',           //不含税金额（含PND）
                    'vat_rate',         //vat税率
                    'vat_amount',       //vat金额
                    'amount_has_tax',   //含税金额
                    'wht_category',   //wht类型
                    'wht_tax_rate',     //wht税率
                    'wht_amount',       //wht金额
                    'actually_amount',  //实付金额

                    'deduct_rate',      //可抵扣税率
                    'deduct_amount'     //可抵扣金额
                ];


                $header = [
                    $lang->_("bank_flow_export_field_is_deduct_text"),
                    $lang->_("bank_flow_export_field_no"),
                    $lang->_("bank_flow_export_field_date"),
                    $lang->_("bank_flow_export_field_bank_account"),
                    $lang->_("bank_flow_export_field_ticket_no"),
                    $lang->_("bank_flow_export_field_receive_name"),
                    $lang->_("bank_flow_export_field_receive_account"),
                    $lang->_("bank_flow_export_field_receive_bank"),
                    $lang->_("bank_flow_export_field_pay_amount"),
                    $lang->_("bank_flow_export_field_currency_text"),
                    $lang->_("bank_flow_export_field_bank_flow_expense_text"),
                    $lang->_("bank_flow_export_field_is_cancel_text"),
                    $lang->_("payment_store_renting_tax_total_amount"),
                    $lang->_("payment_store_renting_actually_total_amount"),
                    $lang->_("payment_store_renting_vat_total_amount"),
                    $lang->_("payment_store_renting_wht_total_amount"),
                    $lang->_("payment_store_renting_no_total_amount"),
                    $lang->_('global.applicant.id'),
                    $lang->_('bank_flow_export_field_create_department_name'),
                    $lang->_('payment_store_renting_store_name'),
                    $lang->_('bank_flow_export_field_happen_at'),
                    $lang->_('payment_store_renting_cost_type'),
                    $lang->_('csr_field_no_tax'),
                    static::$t->_('payment_store_renting_vat_rate'),
                    static::$t->_('payment_store_renting_vat_amount'),
                    static::$t->_('payment_store_renting_amount_has_tax'),
                    static::$t->_('payment_store_renting_wht_category'),
                    static::$t->_('payment_store_renting_wht_category_tax'),  // WHT税率
                    static::$t->_('payment_store_renting_wht_amount'),  // WHT金额
                    static::$t->_('payment_store_renting_actually_amount'),  // 实付金额
                    $lang->_('bank_flow_export_field_deduct_rate'),
                    $lang->_('bank_flow_export_field_deduct_amount'),
                ];


                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT:
                $field = [
                    'is_deduct_text',   //可抵扣是否确认
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'receive_name',     //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回


                    'amount_total_have_tax',   //含税金额总计
                    'amount_total_actually',   //实付金额总计
                    'amount_discount',  //折扣
                    'amount_total_no_tax',  //不含税金额总计
                    'amount_total_vat', //vat金额总计
                    'amount_total_wht', //wht金额总计
                    'apply_id', //申请人工号
                    'cost_department_name', //费用所属部门
                    'cost_store_name',  //费用所属网点
                    'cost_center_name', //费用成本中心
                    'happen_at',        //发生期间
                    'budget_text',      //预算实质
                    'product_name',     //费用明细
                    'ledger_account_name',   //核算科目
                    'amount_no_tax',    //不含税金额
                    'vat_rate',         //vat税率
                    'amount_vat',       //vat金额
                    'wht_category_text',    //wht类别
                    'wht_rate',         //wht税率,
                    'amount_wht',       //wht金额
                    'amount_have_tax',  //含税金额
                    'deduct_rate',      //可抵扣税率
                    'deduct_amount'     //可抵扣金额
                ];


                $header = [
                    $lang->_("bank_flow_export_field_is_deduct_text"),
                    $lang->_("bank_flow_export_field_no"),
                    $lang->_("bank_flow_export_field_date"),
                    $lang->_("bank_flow_export_field_bank_account"),
                    $lang->_("bank_flow_export_field_ticket_no"),
                    $lang->_("bank_flow_export_field_receive_name"),
                    $lang->_("bank_flow_export_field_receive_account"),
                    $lang->_("bank_flow_export_field_receive_bank"),
                    $lang->_("bank_flow_export_field_pay_amount"),
                    $lang->_("bank_flow_export_field_currency_text"),
                    $lang->_("bank_flow_export_field_bank_flow_expense_text"),
                    $lang->_("bank_flow_export_field_is_cancel_text"),

                    $lang->_("payment_store_renting_tax_total_amount"),
                    $lang->_("payment_store_renting_actually_total_amount"),
                    static::$t->_('purchase.discount'), // 折扣
                    $lang->_("payment_store_renting_no_total_amount"),
                    $lang->_("payment_store_renting_vat_total_amount"),
                    $lang->_("payment_store_renting_wht_total_amount"),
                    $lang->_('global.applicant.id'),
                    $lang->_('bank_flow_export_field_create_department_name'),
                    $lang->_('bank_flow_export_field_create_store_name'),
                    $lang->_('bank_flow_export_field_cost_center_name'),
                    $lang->_('bank_flow_export_field_happen_at'),

                    $lang->_('bank_flow_export_field_budget_text'),
                    $lang->_('bank_flow_export_field_product_name'),
                    $lang->_('bank_flow_export_field_ledger_account_text'),
                    $lang->_('csr_field_no_tax'),
                    static::$t->_('payment_store_renting_vat_rate'),
                    static::$t->_('payment_store_renting_vat_amount'),
                    static::$t->_('payment_store_renting_wht_category'),
                    static::$t->_('payment_store_renting_wht_category_tax'),  // WHT税率
                    static::$t->_('payment_store_renting_wht_amount'),  // WHT金额
                    static::$t->_('payment_store_renting_amount_has_tax'),
                    $lang->_('bank_flow_export_field_deduct_rate'),
                    $lang->_('bank_flow_export_field_deduct_amount')
                ];
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND:
                $field = [
                    'no',               //申请单号
                    'date',             //银行转账日期,
                    'bank_account',     //付款银行账号
                    'ticket_no',        //支票号
                    'receive_name',     //收款人名称
                    'receive_account',  //收款人账号
                    'receive_bank',     //收款人开户银行
                    'pay_amount',       //付款金额
                    'currency_text',    //币种
                    'bank_flow_expense_text',//费用类型
                    'is_cancel_text',    //是否撤回
                    'create_id',        //申请人工号,
                    'create_department_name',   //费用所属部门
                    'create_store_name', //费用所属网点
                    'create_date',      //申请日期
                    'apply_amount',     //申请金额
                    'event_info',       //说明
                ];
                foreach ($field as $item) {
                    $header[] = $lang->_("bank_flow_export_field_" . $item);
                }
                break;
            default:
                throw new BusinessException('not found type');
                break;
        }
        return ['header' => $header, 'field' => $field];
    }


    /**
     * 批量更改可抵扣确认
     * @param $type
     * @param $params
     * @param $user
     * @return array
     */
    public function confirmExpenseList($type, $params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = false;
        $db = $this->getDI()->get('db_oa');
        try {
            $db->begin();

            //这枚有限制type,不能抵扣确认的是直接返回true的，而且有接口权限限制
            $model = $this->getModelByTypeId($type);
            $ids = $params['ids'];
            $is_deduct = intval($params['is_deduct']);
            foreach ($ids as $k => $v) {
                $ids[$k] = intval($v);
            }
            $model->batch_confirm($ids, ['is_deduct' => $is_deduct]);

            $db->commit();
            $this->logger->info(
                '银行流水-批量确认-模块='.$type."===params===".json_encode($params,JSON_UNESCAPED_UNICODE)."==user==".json_encode($user.JSON_UNESCAPED_UNICODE)
            );
            $data = true;
        } catch (ValidationException $e) {
            $db->rollback();
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow confirmExpenseList error==' . $e->getMessage() . '---' . $e->getLine(
                ) . '---' . $e->getTraceAsString()
            );
            $message = static::$t->_('retry_later');
        }
        return ['code' => $code, 'message' => $message, 'data' => $data];
    }


    public function getExpenseListByDate($start, $end, $is_export = false,$company_name='')
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $res = [];

        try {
            ini_set('memory_limit', '1024M');

            $conditions = 'date between :start: and :end:';
            $bind = ['start' => $start, 'end' => $end];
            $bank_account_id_arr = [];
            if (!empty($company_name)){
                $bank_accounts = BankAccountModel::find([
                    'conditions' => 'company_name = :company_name:',
                    'bind' => ['company_name' => $company_name],
                    'columns' => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
            }
            if (!empty($bank_account_id_arr)){
                $conditions .= ' and bank_account_id in ({bank_account_id:array})';
                $bind['bank_account_id'] = $bank_account_id_arr;
            }
            $flows = BankFlowModel::find([
                'columns' => 'type,bank_account_id,bank_account,real_amount,bank_left_amount,bank_flow_expense_id',
                'conditions' => $conditions,
                'bind' => $bind
            ])->toArray();

            $header = [];
            $first = [];
            $gets = []; //收款行
            $pays = []; //付款行
            $bank_account_ids = [];
            $data = [];
            $header[0] = static::$t->_("bank_flow_account");

            foreach ($flows as $flow) {
                $header[$flow['bank_account_id']] = $flow['bank_account'];
                if (empty($first[$flow['bank_account_id']])) {
                    $first[$flow['bank_account_id']] = bcsub($flow['bank_left_amount'], $flow['real_amount'], 2);
                }
                $bank_account_ids[$flow['bank_account_id']] = 1;
                if (empty($data[$flow['bank_account_id'] . "_" . $flow['type'] . "_" . $flow['bank_flow_expense_id']])) {
                    $data[$flow['bank_account_id'] . "_" . $flow['type'] . "_" . $flow['bank_flow_expense_id']] = 0;
                }
                $data[$flow['bank_account_id'] . "_" . $flow['type'] . "_" . $flow['bank_flow_expense_id']] += abs(
                    $flow['real_amount']
                );
            }

            $expenses = InitFlowService::getInstance()->getExpenseTypeList('all_list');

            //放到最后，没有费用类型的
            if ($expenses[0]['value'] == 0) {
                $expenses[] = $expenses[0];
                unset($expenses[0]);
            }

            $lineData = [];

            //期初
            foreach ($header as $k => $v) {
                if (empty($k)) {
                    $lineData[] = static::$t->_("bank_flow_first");
                    continue;
                }
                $lineData[] = $first[$k];
            }


            //导出要title
            if ($is_export) {
                if ($start == $end) {
                    $res[] = [static::$t->_("bank_flow_day_expense_name") . '(' . date("Y/m/d") . ')'];
                } else {
                    $res[] = [static::$t->_("bank_flow_month_expense_name") . '(' . date("Y/m/d") . ')'];
                }
            }
            //表头
            $res[] = array_values($header);
            $res[] = $lineData;

            $getSum = [];
            $paySum = [];

            foreach ($expenses as $expense) {
                $t_pay = [];
                $t_get = [];

                if ($expense['is_get']) {
                    if ($expense['value'] == 0) {
                        $t_get[] = static::$t->_("bank_flow_wait_get");
                    } else {
                        $t_get[] = $expense['label'];
                    }
                }

                if ($expense['is_pay']) {
                    if ($expense['value'] == 0) {
                        $t_pay[] = static::$t->_("bank_flow_wait_pay");
                    } else {
                        $t_pay[] = $expense['label'];
                    }
                }

                foreach ($header as $k => $v) {
                    //过滤第一列
                    if (empty($k)) {
                        continue;
                    }

                    $t_get[$k] = 0.00;
                    $t_pay[$k] = 0.00;

                    if ($expense['is_get']) {
                        if (isset($data[$k . "_1_" . $expense['value']])) {
                            $t_get[$k] = $data[$k . "_1_" . $expense['value']];
                            if (empty($getSum[$k])) {
                                $getSum[$k] = 0;
                            }
                            $getSum[$k] += $data[$k . "_1_" . $expense['value']];
                        }
                    }

                    if ($expense['is_pay']) {
                        if (isset($data[$k . "_2_" . $expense['value']])) {
                            $t_pay[$k] = $data[$k . "_2_" . $expense['value']];
                            if (empty($paySum[$k])) {
                                $paySum[$k] = 0;
                            }
                            $paySum[$k] += $data[$k . "_2_" . $expense['value']];
                        }
                    }
                }


                if ($expense['is_get']) {
                    $gets[] = array_values($t_get);
                }

                if ($expense['is_pay']) {
                    $pays[] = array_values($t_pay);
                }
            }

            //收款
            $res = array_merge($res, $gets);

            //借方合计
            $lineData = [];
            foreach ($header as $k => $v) {
                if (empty($k)) {
                    $lineData[] = static::$t->_("bank_flow_get_sum");
                    continue;
                }
                $lineData[] = $getSum[$k] ?? 0.00;
            }
            $res[] = $lineData;


            //付款
            $res = array_merge($res, $pays);

            //贷方合计
            $lineData = [];
            foreach ($header as $k => $v) {
                if (empty($k)) {
                    $lineData[] = static::$t->_("bank_flow_pay_sum");
                    continue;
                }
                $lineData[] = $paySum[$k] ?? 0.00;
            }
            $res[] = $lineData;


            //期末
            $lineData = [];
            foreach ($header as $k => $v) {
                if (empty($k)) {
                    $lineData[] = static::$t->_("bank_flow_end");
                    continue;
                }
                $lineData[] = ($first[$k] ?? 0.00) + ($getSum[$k] ?? 0.00) - ($paySum[$k] ?? 0.00);
            }
            $res[] = $lineData;

            foreach ($res as $k => $v) {
                //第一行=账号，不要
                if ($k == 0) {
                    continue;
                }

                //导出还有第一行
                if($is_export && $k==1){
                    continue;
                }

                foreach ($v as $kk => $vv) {
                    if (is_numeric($vv)) {
                        $res[$k][$kk] = number_format($vv, 2);
                    }
                }
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->error('getExpenseListByDate error=' . $e->getMessage());
            $message = static::$t->_('retry_later');
        }

        if(empty($flows)){
            return [
                'code' => $code,
                'message' => $message,
                'data' => []
            ];
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $res
        ];
    }


    public function exportExpenseListByDate($start_date, $end_date, $company_name = '')
    {
        $res = $this->getExpenseListByDate($start_date, $end_date, true, $company_name);
        if ($res['code'] != ErrCode::$SUCCESS) {
            return $res;
        }

        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        $res = $res['data'];
        $name = $res[0];
        $header = $res[1];
        unset($res[0]);
        unset($res[1]);

        $res = array_values($res);

        $data = '';
        try {
            $res = $this->exportExcel($header, $res, $name);
            $data = $res['data'];
        } catch (\Exception $e) {
            $this->logger->error('exportExpenseListByDate error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }


    /**
     * 获得可抵扣金额，保留2位小数 (原来bcmul就是字符串)
     * @param $amount
     * @return string
     */
    public function getDeductAmount($amount)
    {
        return ''.
            round(
                $amount * (BankFlowEnums::BANK_FLOW_DEDUCT_RATE / 100) * (BankFlowEnums::BANK_FLOW_DEDUCT_DEFAULT),
                2
            );
    }

    public function getAllExpenseList($condition, $type)
    {


        //单号,付款日期,付款账号，支票号，费用类型,是否撤销,币种,付款金额
        $columns = 'bfo.no,bfo.date,bfo.diff_amount,bfo.diff_info,bf.bank_account,bf.ticket_no,bf.bank_flow_expense_id,bfo.is_cancel,bf.currency,bf.pay_amount';
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['bfo' => BankFlowOaModel::class]);
        $builder->join(BankFlowModel::class, 'bfo.bank_flow_id = bf.id', 'bf');
        $builder->andWhere('bfo.oa_type = :type:', ['type' => $type]);
        if (!empty($condition['bank_account_id'])) {
            if (is_array($condition['bank_account_id'])) {
                $builder->andWhere(
                    'bfo.bank_account_id in ({bank_account_id:array})',
                    ['bank_account_id' => $condition['bank_account_id']]
                );
            } else {
                $builder->andWhere(
                    'bfo.bank_account_id = :bank_account_id:',
                    ['bank_account_id' => intval($condition['bank_account_id'])]
                );
            }
        }
        if (isset($condition['start_date'])) {
            $builder->andWhere('bfo.date >= :start_date:', ['start_date' => $condition['start_date']]);
        }
        if (isset($condition['end_date'])) {
            $builder->andWhere('bfo.date <= :end_date:', ['end_date' => $condition['end_date']]);
        }
        switch ($type) {

            case BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN:
                //申请人工号，费用部门，费用中心名字，申请日期，金额，说明
                //收款人姓名，收款人账号，收款人开户银行。
                $columns .= ',m.create_id,m.create_department_name,m.cost_center_name,m.create_date,m.amount,m.event_info,
                lpb.name as receive_name,lpb.account as receive_account,lpb.bank_type as receive_bank';
                $builder->leftjoin(Loan::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(LoanPayBank::class, 'm.id=lpb.loan_id', 'lpb');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT:
                $columns .= ',m.bank_name as receive_name,m.bank_account as receive_account,m.bank_type as receive_bank,
                m.amount,m.real_amount,m.loan_amount,m.payable_amount_all,m.apply_id as create_id,m.cost_department_name as create_department_name,d.cost_store_n_name as create_store_name,
                d.cost_center_code,d.start_at,d.end_at,d.budget_id,d.product_id,d.ledger_account_id,d.info as event_info,d.invoices_ids,d.tax as vat_amount,d.tax_not as amount_no_tax,d.rate as vat_rate,d.amount as amount_has_tax,d.wht_type,d.wht_tax as wht_tax_rate,d.wht_tax_amount as wht_amount,d.is_deduct,d.id,d.voucher_description,
                d.deductible_vat_tax as deduct_rate,d.deductible_tax_amount as deduct_amount,d.payable_amount';
                $builder->leftjoin(Reimbursement::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(Detail::class, 'd.re_id = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT:
                $columns .= ',m.bank_account_name as receive_name,m.create_id,m.is_link_pa,m.pa_id,m.po_id,m.cur_amount,m.real_amount,m.receipt_amount,m.not_tax_amount,m.vat7_amount,m.ponos,d.cost_store_name as create_store_name,d.cost_center_name,m.vendor,m.remark,
                m.bank_name as receive_bank,m.bank_no as receive_account,m.swift_code,m.bank_account_name,d.ticket_amount_not_tax as amount_no_tax,d.vat7_rate as vat_rate,d.ticket_amount as amount_has_tax,
                d.id,d.ticket_date as happen_at,d.budget_id,d.product_id,d.product_name,d.ledger_account_id,d.total,d.not_tax_price,d.total_price,d.tax_ratio,d.tax_total_price,d.wht_type,d.wht_ratio as wht_tax_rate,d.wht_amount,d.real_amount as detail_real_amount,
                d.is_deduct,d.deductible_vat_tax as deduct_rate,d.deductible_vat_amount as deduct_amount';
                $builder->leftjoin(PurchasePayment::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(PurchasePaymentReceipt::class, 'd.ppid = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING:
                $columns .= ',d.id,d.is_deduct,d.bank_account_name as receive_name,d.bank_account_no as receive_account,d.bank_name as receive_bank,d.store_name as create_store_name ,d.cost_start_date,d.cost_end_date,d.cost_type,d.amount as amount_no_tax,
                d.vat_rate,d.vat_amount,d.amount_has_tax,d.wht_category as wht_type_text,d.wht_tax_rate,d.wht_amount,d.actually_amount as detail_real_amount,d.ledger_account_id,
                m.tax_total_amount,m.actually_total_amount,m.vat_total_amount,m.wht_total_amount,m.total_amount,m.create_id,m.cost_center_department_name as create_department_name,m.cost_center_name,m.remark as event_info';
                $builder->leftjoin(PaymentStoreRenting::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(PaymentStoreRentingDetail::class, 'd.store_renting_id = m.id', 'd');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT:
                $columns .= ',d.id,d.is_deduct,d.cost_store_name as create_store_name,d.cost_center_name,d.cost_start_date,d.cost_end_date,d.budget_id,d.product_id,d.ledger_account_id,d.amount_no_tax,d.amount_vat as vat_amount,d.wht_category,d.wht_rate as wht_tax_rate,d.amount_wht as wht_amount,
                d.amount_have_tax as amount_has_tax,
                d.deductible_vat_tax as deduct_rate,d.deductible_tax_amount as deduct_amount,
                de.supplier_bk_account_name as receive_name,de.supplier_bk_account as receive_account,de.supplier_bk_name as receive_bank,d.voucher_description,
                m.amount_discount,m.apply_id as create_id,m.cost_department_name as create_department_name,m.remark as event_info,m.amount_total_actually';

                $builder->leftjoin(OrdinaryPayment::class, 'bfo.oa_value = m.id', 'm');
                $builder->leftjoin(OrdinaryPaymentDetail::class, 'd.ordinary_payment_id = m.id', 'd');
                $builder->leftjoin(OrdinaryPaymentExtend::class, 'de.ordinary_payment_id = m.id', 'de');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND:
                $columns .= ',m.payee_username as receive_name,m.payee_account as receive_account,m.payee_bank as receive_bank,
                m.create_id,m.create_department_name,m.create_store_name,m.apply_date as create_date,m.amount,m.apply_reason as event_info';
                $builder->leftjoin(ReserveFundApply::class, 'bfo.oa_value = m.id', 'm');
                break;
            case BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE:
                $columns .= ',m.apply_id as create_id,m.apply_department_name as create_department_name,m.apply_store_name as create_store_name,m.apply_center_code,m.term_start,m.term_end,m.item_type,m.item_detail as event_info,m.amount,m.amount_without_tax as amount_no_tax,m.tax as vat_amount,m.tax_rate as vat_rate,m.wht_category as wht_type_text,m.wht_amount,m.wht_tax_rate,m.wht_amount,m.actually_amount as detail_real_amount,
                m.apply_date as create_date,m.amount';
                $builder->leftjoin(WagesModel::class, 'bfo.oa_value = m.id', 'm');
                break;
            default:
                throw new BusinessException('not found type');
                break;
        }
        $builder->columns($columns);
        $items = $builder->getQuery()->execute()->toArray();

        return $items ?? [];
    }


    /**
     * 处理每行数据
     * @param $items
     * @param $type
     * @return array
     */
    public function handleExportItems($items, $type)
    {
        if (empty($items) || !is_array($items)) {
            return [];
        }
        $new_data         = [];
        $expenseList      = InitFlowService::getInstance()->getExpenseTypeList("pay_list");
        $expenseIdToLabel = array_column($expenseList, 'label', 'value');

        $ledgerIdToName = [];
        $budgets        = [];
        $products       = [];

        $myLang = strtolower(
            substr(static::$language, -2)
        );

        if (!in_array($myLang, ['en', 'th', 'cn'])) {
            $myLang = 'en';
        }
        //匹配不到枚举的返回空
        $vat7Arr = EnumsService::getInstance()->getVatRateValueItem();

        if (in_array(
            $type,
            [
                BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
                BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
                BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
                BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING

            ]
        )) {
            $budgetIds = array_values(array_filter(array_column($items, 'budget_id')));
            if ($budgetIds) {
                $budgetService = new BudgetService();
                $budgets       = $budgetService->budgetObjectList($budgetIds);
            }

            $productIds = array_values(array_filter(array_column($items, 'product_id')));
            if ($productIds) {
                $products = BudgetObjectProduct::find(
                    [
                        'conditions' => ' id in ({ids:array})',
                        'bind'       => ['ids' => $productIds]
                    ]
                )->toArray();
                $products = array_column($products, null, 'id');
            }

            //核算科目名字
            $ledger_account_ids = array_values(array_filter(array_column($items, 'ledger_account_id')));
            if (!empty($ledger_account_ids)) {
                $res = LedgerAccountService::getInstance()->getList($ledger_account_ids);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $ledgerIdToName = array_column($res['data'], 'name', 'id');
                }
            }
        }

        // 网点租房付款费用类型配置
        $store_rent_payment_cost_enums = EnumsService::getInstance()->getStoreRentPaymentCostTypeItem();

        $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);

        foreach ($items as &$item) {

            // 币种
            $item['currency_text'] = static::$t[GlobalEnums::$currency_item[$item['currency']]];
            //费用类型
            $item['bank_flow_expense_text'] = '';
            if (!empty($expenseIdToLabel[$item['bank_flow_expense_id']])) {
                $item['bank_flow_expense_text'] = $expenseIdToLabel[$item['bank_flow_expense_id']];
            }
            $item['is_cancel_text'] = '';
            if ($item['is_cancel']) {
                $item['is_cancel_text'] = 'Y';
            }

            if (isset($item['is_deduct'])) {
                $item['is_deduct_text'] = '';
                if ($item['is_deduct']) {
                    $item['is_deduct_text'] = 'Y';
                }
            }

            $item['receive_name']    = $item['receive_name'] ?? '';
            $item['receive_account'] = $item['receive_account'] ?? '';
            $item['receive_bank']    = $item['receive_bank'] ?? '';

            if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN || $type == BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND) {
                //申请金额
                $item['detail_real_amount'] = bcdiv($item['amount'], 1000, 2);
                //可抵扣税率
                $item['deduct_rate'] = '0%';
                //可抵扣税金额
                $item['deduct_amount']    = '0.00';
                $item['line_real_amount'] = bcdiv($item['amount'], 1000, 2);

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT) {

                //含税金额总计
                $item['amount'] = bcdiv($item['amount'], 1000, 2);

                //实付金额总计
                $item['detail_real_amount'] = bcdiv($item['real_amount'], 1000, 2);

                //冲减借款金额总计
                $item['loan_amount']    = bcdiv($item['loan_amount'], 1000, 2);
                $item['amount_no_tax']  = bcdiv($item['amount_no_tax'], 1000, 2);
                $item['amount_has_tax'] = bcdiv($item['amount_has_tax'], 1000, 2);
                $item['vat_amount']     = bcdiv($item['vat_amount'], 1000, 2);

                $item['cost_type_text'] = '';
                if (isset($budgets) && isset($budgets[$item['budget_id']])) {
                    $item['cost_type_text'] = $budgets[$item['budget_id']]['name_' . $myLang];
                }
                $item['wht_tax_rate'] = empty($item['wht_tax_rate']) ? 0 : $item['wht_tax_rate'] . '%';

                $item['wht_type_text'] = $wht_cat_map[$item['wht_type']] ?? '';

                $item['product_name'] = '';
                if ($item['product_id']) {
                    $item['product_name'] = isset($products) && isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . $myLang] : $item['product_name'];
                }
                //核算科目
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';

                //发生期间
                $item['happen_at'] = $item['start_at'] . '-' . $item['end_at'];

                $item['create_store_name'] = $item['create_store_name'] ?? '';
                $item['wht_amount']        = empty($item['wht_amount']) ? '0.00' : bcdiv($item['wht_amount'], 1000, 2);
                $item['line_real_amount'] = empty($item['payable_amount']) ? '0.00' : bcdiv($item['payable_amount'], 1000, 2);

                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }
                //先除1000,转为0.06, 兼容以前的逻辑(除1000可以直接用于计算),再乘100转为前端的枚举

                $item['vat_rate'] = (string)bcdiv($item['vat_rate'] ?? 0, 1000, 4);
                $item['vat_rate'] = (string)bcmul($item['vat_rate'], 100, 2);
                $item['vat_rate'] = rtrim(rtrim($item['vat_rate'], '0'), '.');

                if (!in_array($item['vat_rate'], $vat7Arr)) {
                    $item['vat_rate'] = '';
                } else {
                    $item['vat_rate'] .= '%';
                }

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING) {
                $item['vat_rate']       .= '%';
                $item['wht_tax_rate']   .= '%';
                $item['cost_type_text'] = static::$t->_($store_rent_payment_cost_enums[$item['cost_type']]);
                $item['happen_at']      = $item['cost_start_date'] . '-' . $item['cost_end_date'];

                //可抵扣税率
                $item['deduct_rate'] = '0%';

                //可抵扣税金额
                $item['deduct_amount']       = '0.00';
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';
                $item['line_real_amount']    = $item['detail_real_amount'];

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT) {

                $item['cost_type_text'] = isset($budgets[$item['budget_id']]) ? $budgets[$item['budget_id']]['name_' . $myLang] : '';

                $item['product_name']  = isset($products[$item['product_id']]) ? $products[$item['product_id']]['name_' . $myLang] : '';
                $item['wht_type_text'] = $wht_cat_map[$item['wht_category']] ?? 0;
                //核算科目
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';


                $item['vat_rate'] = '0';
                if (!empty($item['amount_no_tax']) && !empty($item['amount_vat'])) {
                    $item['vat_rate'] = round($item['vat_amount'] / $item['amount_no_tax'] * 100);
                }
                $item['vat_rate'] .= '%';

                $item['wht_tax_rate'] .= '%';

                $item['happen_at'] = $item['cost_start_date'] . '-' . $item['cost_end_date'];
                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }

                // 实付金额
                $item['detail_real_amount'] = $item['amount_total_actually'];
                $item['line_real_amount']   = bcsub($item['amount_has_tax'], $item['wht_amount'], 2);
            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_WAGE) {
                $item['happen_at']        = $item['term_start'] . '-' . $item['term_end'];
                $item['wht_tax_rate']     .= '%';
                $data['cost_type_text']   = static::$t->_('item_type.'.$item['item_type']);
                $item['line_real_amount'] = $item['detail_real_amount'];

            } else if ($type == BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT) {
                if ($item['is_link_pa']) {
                    $apply                          = PurchaseApply::findFirst(
                        [
                            'id =:id:',
                            'bind' => ['id' => $item['pa_id']]
                        ]
                    );
                    $apply                          = empty($apply) ? [] : $apply->toArray();
                    $item['create_department_name'] = $apply['cost_department_name'] ?? null;
                } else {
                    $order                          = PurchaseOrder::findFirst(
                        [
                            'id =:po_id:',
                            'bind' => ['po_id' => $item['po_id']]
                        ]
                    );
                    $order                          = empty($order) ? [] : $order->toArray();
                    $item['create_department_name'] = $order['cost_department_name'] ?? null;
                }
                $item['line_real_amount'] = bcdiv(bcsub($item['amount_has_tax'], $item['wht_amount']), 1000, 2);

                $item['cost_type_text']      = isset($budgets[$item['budget_id']]) ? $budgets[$item['budget_id']]['name_' . $myLang] : '';
                $item['ledger_account_name'] = $ledgerIdToName[$item['ledger_account_id']] ?? '';
                if (bcsub($item['amount_no_tax'], $item['amount_has_tax']) > 0) {
                    $item['vat_amount'] = bcdiv(bcsub($item['amount_no_tax'], $item['amount_has_tax']), 1000, 2);
                } else {
                    $item['vat_amount'] = '0.00';
                }
                $item['amount_no_tax']      = !empty($item['amount_no_tax']) ? bcdiv($item['amount_no_tax'], 1000, 2) : '0.00';
                $item['amount_has_tax']     = !empty($item['amount_has_tax']) ? bcdiv($item['amount_has_tax'], 1000, 2) : '0.00';
                $item['vat_rate']           .= '%';
                $item['wht_tax_rate']       .= '%';
                $item['wht_type_text']      = $wht_cat_map[$item['wht_type']] ?? '';
                $item['wht_amount']         = !empty($item['wht_amount']) ? bcdiv($item['wht_amount'], 1000, 2) : '0.00';
                $item['detail_real_amount'] = !empty($item['detail_real_amount']) ? bcdiv($item['detail_real_amount'], 1000, 2) : '0.00';

                //可抵扣税率
                $item['deduct_rate'] = !empty($item['deduct_rate']) ? $item['deduct_rate'] . '%' : '0%';
                //可抵扣税金额
                $item['deduct_amount'] = !empty($item['deduct_amount']) ? bcdiv($item['deduct_amount'], 1000, 2) : '0.00';
                //可抵扣金额不等于0,则为Y
                if ($item['deduct_amount'] > 0) {
                    $item['is_deduct_text'] = 'Y';
                }
            }

            $new_data[] = [
                $item['is_deduct_text'] ?? '',   //可抵扣是否确认
                $item['no'],               //申请单号
                $item['date'],             //银行转账日期,
                $item['bank_account'],     //付款银行账号
                $item['ticket_no'],        //支票号
                $item['receive_name'],     //收款人名称
                $item['receive_account'],  //收款人账号
                $item['receive_bank'],     //收款人开户银行
                $item['pay_amount'],       //付款金额
                $item['currency_text'],    //币种
                $item['bank_flow_expense_text'],//费用类型
                $item['is_cancel_text'],    //是否撤回
                $item['create_id'],//申请人工号
                $item['create_department_name'],//费用所属部门
                $item['create_store_name'] ?? '',//费用所属网点
                $item['cost_center_name'] ?? '',//费用所属中心
                $item['happen_at'] ?? '',//费用发生期间
                $item['cost_type_text'] ?? '',//报销实质/费用实质/付款分类
                $item['product_name'] ?? '',//费用明细/费用类型/产品名称
                $item['ledger_account_name'] ?? '',//核算科目
                $item['voucher_description'] ?? '',//凭证描述
                $item['event_info'] ?? '',//说明
                $item['amount_no_tax'] ?? '',//不含税金额
                $item['vat_rate'] ?? '',//vat7税率
                $item['vat_amount'] ?? '',//vat7金额
                $item['amount_has_tax'] ?? '',//含税金额
                $item['wht_type_text'] ?? '',//wht类别
                $item['wht_tax_rate'] ?? '',//wht税率
                $item['wht_amount'] ?? '',//wht金额
                $item['line_real_amount'] ?? '',//行实付金额
                $item['loan_amount'] ?? '',//冲减借款金额总计
                $item['amount_discount'] ?? '',//折扣
                $item['detail_real_amount'] ?? '',//实付金额
                $item['deduct_rate'] ?? '',//可抵扣税率
                $item['deduct_amount'] ?? '',//可抵扣税金额（确认的可抵扣税额 前面对应Y）
                $item['diff_amount'],//差异金额
                $item['diff_info'],//差异备注


            ];
        }
        return $new_data ?? [];
    }

    /**
     * bank_flow condition
     * */

    protected function getBankFlowCondition($builder,$params,$bank_account_id_arr){

        $columns = 'bf.id AS flow_id,
                bf.bank_name,
                bf.bank_account,
                bf.date,
                bf.time,
                bf.ticket_no,
                bf.get_amount,
                bf.pay_amount,
                bf.bank_left_amount,
                bf.currency,
                bf.trade_desc,
                bf.bank_flow_expense_id,
                bf.updated_at,
                bf.updated_staff_id,
                bf.updated_staff_name,
                bf.edit_status,
                bf.confirm_status,';

        switch ($params['export']) {
            case self::EXPORT_BANK_FLOW_TYPE_PAY_REMARK:

                $builder->from(['bfp' => BankFlowPayDetailModel::class]);
                $builder->leftjoin(BankFlowModel::class, 'bfp.bank_flow_id = bf.id', 'bf');
                $builder->andWhere('bf.type = :type:', ['type' => $params['flow_type']]);
                $columns .= 'bfp.exv_id,
                bfp.apply_amount,
                bfp.no,
                bfp.diff_amount,
                bfp.apply_staff_id,
                bfp.store,
                bfp.link_name,
                bfp.created_at,
                bfp.created_id';
                break;
            case self::EXPORT_BANK_FLOW_TYPE_GET_REMARK:

                $builder->from(['bfg' => BankFlowGetDetailModel::class]);
                $builder->leftjoin(BankFlowModel::class, 'bfg.bank_flow_id = bf.id', 'bf');
                $builder->andWhere('bf.type = :type:', ['type' => $params['flow_type']]);
                $columns .= 'bfg.no,
                bfg.ms_client,
                bfg.store,
                bfg.pay_type,
                bfg.fbi_id,
                bfg.link_name,
                bfg.client_code,
                bfg.created_at,
                bfg.created_id';
                break;
            case self::EXPORT_BANK_FLOW_TYPE_PAY_ORDER:

                $builder->from(['bfo' => BankFlowOaModel::class]);
                $builder->leftjoin(BankFlowModel::class, 'bfo.bank_flow_id = bf.id', 'bf');
                $builder->andWhere('bf.type = :type:', ['type' => 2]);

                $columns .= 'bfo.no,
                bfo.amount,
                bfo.currency,
                bfo.diff_amount,
                bfo.diff_info,
                bfo.is_cancel,
                bfo.updated_at,
                bfo.updated_id';
                break;
            default;
                break;
        }
        if (!empty($bank_account_id_arr)) {
            $builder->andWhere(
                'bf.bank_account_id in ({acct_arr:array})',
                ['acct_arr' => $bank_account_id_arr]
            );
        }


        if (!empty($params['trade_start_date'])) {
            $builder->andWhere('bf.date >= :trade_start_date:', ['trade_start_date' => $params['trade_start_date']]);
        }

        if (!empty($params['trade_end_date'])) {
            $builder->andWhere('bf.date <= :trade_end_date:', ['trade_end_date' => $params['trade_end_date']]);

        }

        if (!empty($params['ticket_no'])) {
            $builder->andWhere('bf.ticket_no = :ticket_no:', ['ticket_no' => $params['ticket_no']]);

        }

        if (isset($params['bank_flow_expense_id'])) {
            $builder->andWhere('bf.bank_flow_expense_id = :bank_flow_expense_id:', ['bank_flow_expense_id' => $params['bank_flow_expense_id']]);

        }
        if (!empty($params['trade_desc'])) {
            $bind['trade_desc'] = "%{$params['trade_desc']}%";
            $builder->andWhere('bf.trade_desc LIKE :trade_desc:', ['trade_desc' => "%{$params['trade_desc']}%"]);
        }
        $builder->columns($columns);
        $builder->limit(80000, 0);//条数安全限制
        return $builder;
    }

    public function exportDataList($params)
    {
        $real_message = '';
        try {
            // 交易日期校验
            if ($params['trade_start_date'] > $params['trade_end_date']) {
                throw new \Exception('param error[trade_start_date]');
            } else if ((strtotime($params['trade_end_date']) - strtotime($params['trade_start_date'])) > ListService::$trade_date_max_between_days * 86400) {
                $error_msg = self::$t['bank_flow.list_query_trade_date_error'];
                $error_msg = str_replace('{max_days}', ListService::$trade_date_max_between_days, $error_msg);
                throw new \Exception($error_msg);
            }

            // 查全部
            if (isset($params['bank_flow_expense_id']) && $params['bank_flow_expense_id'] == '') {
                unset($params['bank_flow_expense_id']);
            }

            // 用户传入的银行账号
            $bank_account_id_arr = !empty($params['bank_account_id']) ? [$params['bank_account_id']] : [];

            // 优先判断公司名称
            if (!empty($params['company_name'])) {
                $bank_accounts = BankAccountModel::find([
                    'conditions' => 'bank_id = :bank_id: and company_name = :company_name:',
                    'bind'       => ['bank_id' => $params['bank_id'], 'company_name' => $params['company_name']],
                    'columns'    => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
            } else if (empty($params['bank_account_id'])) {
                // 账号为空, 则获取该银行所有账号
                $bank_accounts       = BankAccountModel::find([
                    'conditions' => 'bank_id = :bank_id:',
                    'bind'       => ['bank_id' => $params['bank_id']],
                    'columns'    => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
            }

            $builder = $this->modelsManager->createBuilder();

            $builder =  self::getBankFlowCondition($builder,$params,$bank_account_id_arr);

            $items = $builder->getQuery()->execute()->toArray();

        } catch (\Exception $e) {

            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水_' . $params['export'] . ' - 导出异常: ' . $real_message);
        }
        return $items ?? [];

    }

    protected function handelOrderListData($items)
    {
        GlobalEnums::init();
        $currency_item = GlobalEnums::$currency_symbol_map;

        $translation = self::$t;
        $new_data = [];
        foreach ($items as $key =>$item){
            // 金额字段, 千分位展示
            $item['get_amount'] = number_format($item['get_amount'], 2);
            $item['pay_amount'] = number_format($item['pay_amount'], 2);
            $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);
            // 币种
            $item['currency'] = $currency_item[$item['currency']] ?? '';
            // 费用类型
            $item['bank_flow_expense_name'] = $translation[BankFlowEnums::get_expense_type_prefix().$item['bank_flow_expense_id']] ?? '';
            $new_data[]=[
                $item['bank_name'],
                $item['bank_account'],
                $item['date'],
                $item['ticket_no'],
                $item['get_amount'],
                $item['pay_amount'],
                $item['bank_left_amount'],
                $item['trade_desc'],
                $item['bank_flow_expense_name'],
                explode('_', $item['no'])[0] ?? '',
                $item['amount'],
                $item['currency'],
                $item['diff_amount'],
                $item['diff_info'],
                $item['updated_id'],
                $item['updated_at'],
                ($item['is_cancel']==1)?'Y':'N',
            ];

        }
        return $new_data;
    }

    public function exportAllExpenseList($condition)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            ini_set('memory_limit', '1024M');

            // 交易日期校验
            if ($condition['start_date'] > $condition['end_date']) {
                throw new ValidationException('param error[start_date]');
            } else if ((strtotime($condition['end_date']) - strtotime($condition['start_date'])) > self::$trade_date_max_between_days * 86400) {
                $error_msg = self::$t['bank_flow.list_query_trade_date_error'];
                $error_msg = str_replace('{max_days}',self::$trade_date_max_between_days, $error_msg);
                throw new ValidationException($error_msg);
            }
            $condition['company_name'] = $condition['company_name']??'';
            $condition['bank_account_id'] = $condition['bank_account_id']??'';
            //银行账号
            if (empty($condition['bank_account_id']) && !empty($condition['company_name']) ){
                $bank_accounts = BankAccountModel::find([
                    'conditions' => 'company_name = :company_name:',
                    'bind' => ['company_name' => $condition['company_name']],
                    'columns' => ['id']
                ])->toArray();
                $bank_account_id_arr = array_column($bank_accounts, 'id');
                $condition['bank_account_id'] = $bank_account_id_arr;
            }

            $header = [
                static::$t->_('csr_field_is_deduct_text'),   //可抵扣是否确认
                static::$t->_('csr_field_no'),               //申请单号
                static::$t->_('csr_field_date'),             //银行转账日期,
                static::$t->_('csr_field_flow_bank_account'),     //付款银行账号
                static::$t->_('csr_field_ticket_no'),        //支票号
                static::$t->_('csr_field_receive_name'),     //收款人信息
                static::$t->_('csr_field_receive_account'),  //收款人账号
                static::$t->_('csr_field_receive_bank'),     //收款人开户银行
                static::$t->_('csr_field_payment_pay_amount'),       //付款金额
                static::$t->_('csr_field_currency_text'),    //币种
                static::$t->_('csr_field_bank_flow_expense_text'),//费用类型
                static::$t->_('csr_field_is_cancel_text'),    //是否撤回

                static::$t->_('csr_field_create_id'),    //申请人工号
                static::$t->_('csr_field_create_department_name'),   //费用所属部门
                static::$t->_('csr_field_create_store_name'), //费用所属网点
                static::$t->_('csr_field_cost_center'), //费用所属中心
                static::$t->_('csr_field_happen_at'),       //费用发生期间
                static::$t->_('csr_field_cost_type_text'),       //报销实质/费用实质/付款分类
                static::$t->_('csr_field_product_name'),       //费用明细/费用类型/产品名称
                static::$t->_('csr_field_ledger_account_name'),       //核算科目
                static::$t->_('csr_field_voucher_description'),       //凭证描述
                static::$t->_('csr_field_event_info'),       //说明
                static::$t->_('csr_field_total_price'),      //不含税金额
                static::$t->_('csr_field_vat_rate'),        //vat税率
                static::$t->_('csr_field_vat_tax_amount'),        //vat金额
                static::$t->_('csr_field_tax_total_price'),  //含税金额
                static::$t->_('csr_field_wht_type_text'),    //wht类别
                static::$t->_('csr_field_wht_ratio'),        //wht税率
                static::$t->_('csr_field_detail_wht_amount'),       //wht金额
                static::$t->_('csr_field_real_pay_amount'),//行实付金额
                static::$t->_('csr_field_loan_amount'),       //冲减借款金额总计
                static::$t->_('csr_field_amount_discount'),       //折扣
                static::$t->_('csr_field_detail_real_amount'),    //实付金额
                static::$t->_('csr_field_deduct_rate'),      //可抵扣税率
                static::$t->_('csr_field_deduct_amount'),   //可抵扣税金额（确认的可抵扣税额 前面对应Y）
                static::$t->_('csr_field_not_tax_price'),    //差异金额
                static::$t->_('csr_field_diff_remark'), //差异备注
            ];

            $file_name = 'bank_flow_expense_all_list_'.date('Ymd"').'.xlsx';

            $items_1 = $this->getAllExpenseList($condition, 1);
            $items_2 = $this->getAllExpenseList($condition, 2);
            $items_3 = $this->getAllExpenseList($condition, 3);
            $items_4 = $this->getAllExpenseList($condition, 4);
            $items_5 = $this->getAllExpenseList($condition, 5);
            $items_6 = $this->getAllExpenseList($condition, 6);
            $items_7  = $this->getAllExpenseList($condition, 7);


            $new_data = [];
            if (!empty($items_1)) {
                $items_1  = $this->handleExportItems($items_1, 1);
                $new_data = array_merge($new_data, $items_1);
            }
            if (!empty($items_2)) {
                $items_2 = $this->handleExportItems($items_2, 2);

                $new_data = array_merge($new_data, $items_2);

            }
            if (!empty($items_3)) {
                $items_3  = $this->handleExportItems($items_3, 3);
                $new_data = array_merge($new_data, $items_3);
            }
            if (!empty($items_4)) {
                $items_4  = $this->handleExportItems($items_4, 4);
                $new_data = array_merge($new_data, $items_4);

            }
            if (!empty($items_5)) {
                $items_5 = $this->handleExportItems($items_5, 5);

                $new_data = array_merge($new_data, $items_5);

            }
            if (!empty($items_6)) {
                $items_6 = $this->handleExportItems($items_6, 6);

                $new_data = array_merge($new_data, $items_6);

            }

            if (!empty($items_7)) {
                $items_7 = $this->handleExportItems($items_7, 7);

                $new_data = array_merge($new_data, $items_7);

            }

            $excel_data = $this->exportExcel($header,$new_data,$file_name);
            $data = !empty($excel_data['data']) ? $excel_data['data'] : '';

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->error('bank_flow-exportExpenseList-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }



    public function exportOrderList($params)
    {
        $file_name = 'bank_order_list' . date('Ymd') . '.xlsx';
        $header    = [
            static::$t->_('csr_field_flow_bank_name'),//'银行',
            static::$t->_('csr_field_flow_bank_account'),  //'银行账号',
            static::$t->_('csr_field_flow_date'), //'交易日期',
            static::$t->_('csr_field_flow_ticket_no'),//'支票号码',
            static::$t->_('csr_field_flow_get_amount'),//'收款',
            static::$t->_('csr_field_flow_pay_amount'),//'付款',
            static::$t->_('csr_field_flow_bank_left_amount'),//'余额',
            static::$t->_('csr_field_flow_trade_desc'),//'描述',
            static::$t->_('csr_field_flow_bank_flow_expense_name'),//'费用类型',
            static::$t->_('csr_field_flow_no'),//单号,
            static::$t->_('csr_field_flow_amount'),//金额,
            static::$t->_('csr_field_flow_currency'),//币种
            static::$t->_('csr_filed_flow_diff_amount'),//差异金额,
            static::$t->_('csr_field_flow_diff_info'),//差异原因,
            static::$t->_('csr_field_flow_updated_id'),//操作人
            static::$t->_('csr_field_flow_updated_at'),//操作时间
            static::$t->_('csr_field_flow_is_cancel'),//是否撤回
        ];

        $items     = $this->exportDataList($params);
        $items = $this->handelOrderListData($items);

        $excel_data = $this->exportExcel($header, $items, $file_name);

        return [
            'code' => $excel_data['code'],
            'message' => $excel_data['code'] == ErrCode::$SUCCESS ? 'success' : 'error',
            'data' => $excel_data['data']
        ];
    }

    /**
     * 导出明细备注
     * */
    public function exportRemarkList($params)
    {
        $file_name = 'bank_pay_remark_list' . date('Ymd') . '.xlsx';
        $header    = [
            static::$t->_('csr_field_flow_bank_name'),//'银行',
            static::$t->_('csr_field_flow_bank_account'),  //'银行账号',
            static::$t->_('csr_field_flow_date'), //'交易日期',
            static::$t->_('csr_field_flow_ticket_no'),//'支票号码',
            static::$t->_('csr_field_flow_get_amount'),//'收款',
            static::$t->_('csr_field_flow_pay_amount'),//'付款',
            static::$t->_('csr_field_flow_bank_left_amount'),//'余额',
            static::$t->_('csr_field_flow_trade_desc'),//'描述',
            static::$t->_('csr_field_flow_bank_flow_expense_name'),//'费用类型',
            static::$t->_('csr_field_flow_evx_no'),//evx号,
            static::$t->_('csr_field_flow_apply_amount'),//申请金额,
            static::$t->_('csr_field_flow_diff_no'),//差额报销单号
            static::$t->_('csr_filed_flow_diff_amount'),//差异金额,
            static::$t->_('csr_field_flow_apply_staff_id'),//申请人工号,
            static::$t->_('csr_field_flow_store_name'),//网点,
            static::$t->_('csr_field_flow_link_name'),//关联方名称,
            static::$t->_('csr_field_flow_get_no'),//账单号,
            static::$t->_('csr_field_flow_ms_client'),//MS客户
            static::$t->_('csr_field_flow_store_name'),//网点,
            static::$t->_('csr_field_flow_pay_type'),//支付类型,
            static::$t->_('csr_field_flow_fbi_id'), //FBI汇款ID
            static::$t->_('csr_field_flow_link_name'),//关联方名称,
            static::$t->_('csr_field_flow_client_code'),//客户代码,
            static::$t->_('csr_field_flow_updated_id'),//操作人
            static::$t->_('csr_field_flow_updated_at'),//操作时间
        ];
        //查询模版1数据
        $params['export'] = self::EXPORT_BANK_FLOW_TYPE_PAY_REMARK;

        $items_pay = $this->exportDataList($params);

        //查询模版2数据
        $params['export'] = self::EXPORT_BANK_FLOW_TYPE_GET_REMARK;
        $items_get        = $this->exportDataList($params);
        if (!empty($items_pay)) {
            $items_pay = $this->handelPayRemarkListData($items_pay);
        }
        if (!empty($items_get)) {
            $items_get = $this->handelGetRemarkListData($items_get);
        }

        $items = array_merge($items_pay, $items_get);

        $return = $this->exportExcel($header, $items, $file_name);

        $return['message'] = !empty($return['data']) ? 'success' : 'error';
        return $return;
    }
    /**
     * 处理导出明细数据
     * */
    public function handelPayRemarkListData($items){

        GlobalEnums::init();
        $currency_item = GlobalEnums::$currency_symbol_map;

        $translation = self::$t;
        $new_data = [];
        foreach ($items as $key =>$item){
            // 金额字段, 千分位展示
            $item['get_amount'] = number_format($item['get_amount'], 2);
            $item['pay_amount'] = number_format($item['pay_amount'], 2);
            $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);
            // 币种
            $item['currency'] = $currency_item[$item['currency']] ?? '';
            // 费用类型
            $item['bank_flow_expense_name'] = $translation[BankFlowEnums::get_expense_type_prefix().$item['bank_flow_expense_id']] ?? '';
            $new_data[]=[
                $item['bank_name'],
                $item['bank_account'],
                $item['date'],
                $item['ticket_no'],
                $item['get_amount'],
                $item['pay_amount'],
                $item['bank_left_amount'],
                $item['trade_desc'],
                $item['bank_flow_expense_name'],
                $item['exv_id'],
                $item['apply_amount'],
                $item['no'],
                $item['diff_amount'],
                $item['apply_staff_id'],
                $item['store'],
                $item['link_name'],
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                $item['created_id'],
                $item['created_at'],
            ];

        }
        return $new_data;

    }


    /**
     * 处理导出明细数据
     * */
    public function handelGetRemarkListData($items){

        GlobalEnums::init();
        $currency_item = GlobalEnums::$currency_symbol_map;

        $translation = self::$t;
        $new_data = [];
        foreach ($items as $key =>$item){
            // 金额字段, 千分位展示
            $item['get_amount'] = number_format($item['get_amount'], 2);
            $item['pay_amount'] = number_format($item['pay_amount'], 2);
            $item['bank_left_amount'] = number_format($item['bank_left_amount'], 2);
            // 币种
            $item['currency'] = $currency_item[$item['currency']] ?? '';
            // 费用类型
            $item['bank_flow_expense_name'] = $translation[BankFlowEnums::get_expense_type_prefix().$item['bank_flow_expense_id']] ?? '';
            $new_data[]=[
                $item['bank_name'],
                $item['bank_account'],
                $item['date'],
                $item['ticket_no'],
                $item['get_amount'],
                $item['pay_amount'],
                $item['bank_left_amount'],
                $item['trade_desc'],
                $item['bank_flow_expense_name'],
                '',
                '',
                '',
                '',
                '',
                '',
                '',
                $item['no'],
                $item['ms_client'],
                $item['store'],
                $item['pay_type'],
                $item['fbi_id'],
                $item['link_name'],
                $item['client_code'],
                $item['created_id'],
                $item['created_at'],
            ];

        }
        return $new_data;

    }



}


