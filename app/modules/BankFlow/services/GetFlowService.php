<?php

namespace App\Modules\BankFlow\Services;


use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankFlowGetDetailModel;
use App\Modules\BankFlow\Models\BankFlowModel;

class GetFlowService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }


    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 获得银行流水 费用明细列表
     * @param $bank_flow_id
     * @param $pageNum
     * @param $pageSize
     * @return array
     */
    public function getDetailRemarkList($bank_flow_id, $pageNum, $pageSize)
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $data = [];
        try {
            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }
            $count = BankFlowGetDetailModel::count(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id],
                ]
            );
            $items = BankFlowGetDetailModel::find(
                [
                    'conditions' => 'bank_flow_id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id],
                    'limit' => $pageSize,
                    'offset' => ($pageNum - 1) * $pageSize
                ]
            )->toArray();
            $data = [
                'items' => $items,
                'pagination' => [
                    'current_page' => $pageNum,
                    'per_page' => $pageSize,
                    'total_count' => $count,
                ]
            ];
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $this->logger->warning(
                'bank_flow get=getDetailRemarkList error==' . $e->getMessage() . '---' . $e->getLine(
                ) . '---' . $e->getTraceAsString()
            );
            $message = static::$t->_('retry_later');
        }

        return ['code' => $code, 'message' => $message, 'data' => $data];
    }


    /**
     * 获得明细备注导出模板的头，及字段
     * @return array
     */
    public function getDetailRemarkTplAndField()
    {
        $field = [
            'no',                      //账单号
            'bill_date',               //账单日期
            'ms_client',               //ms客户
            'store',                   //网点
            'pay_type',                //支付类型
            'fbi_id',                  //fbi汇款id
            'link_name',               //关联方名称
            'client_code',             //客户代码
            'amount',                  //金额
        ];

        $header = [
            self::$t->_('bank_flow_field_bill_no'),
            self::$t->_('bank_flow_field_bill_date'),
            self::$t->_('bank_flow_field_ms_client'),
            self::$t->_('global.branch'),
            self::$t->_('bank_flow_field_pay_type'),
            self::$t->_('bank_flow_field_fbi_id'),
            self::$t->_('bank_flow_field_link_name'),
            self::$t->_('bank_flow_field_client_code'),
            self::$t->_('bank_flow_field_amount')
        ];
        return ['header' => $header, 'field' => $field];
    }


    /**
     * 获得明细备注导出模板
     * @return array
     */
    public function getDetailRemarkTpl()
    {
        $header = $this->getDetailRemarkTplAndField()['header'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $tmp_file_name = 'income_template_' . date('Ymd') . '.xlsx';
            $res = $this->exportExcel($header, [], $tmp_file_name);
            $data = $res['data'];
        } catch (\Exception $e) {
            $this->logger->error('getDetailRemarkTpl error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 导入明细备注
     * @param $bank_flow_id
     * @param $data
     * @param $user
     * @return array
     */
    public function importDetailRemark($bank_flow_id, $data, $user)
    {
        $db = $this->getDI()->get('db_oa');
        $filed = $this->getDetailRemarkTplAndField()['field'];
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $res = false;
        try {
            $db->begin();

            $flow = BankFlowModel::findFirst(
                [
                    'conditions' => 'id = :bank_flow_id:',
                    'bind' => ['bank_flow_id' => $bank_flow_id]
                ]
            );
            if (empty($flow)) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }

            if ($flow->type != 1) {
                throw new ValidationException(self::$t->_("bank_flow_not_found"));
            }


            $batchData = [];
            $now = date("Y-m-d H:i:s");

            foreach ($data as $k => $v) {
                $tmp = array_combine($filed, $v);
                $line = $k + 2;
                if (empty($tmp)) {
                    throw new ValidationException(self::$t->_("bank_flow_line_data_error", ['line' => $line]));
                }

                // 账单日期非必填, 如有值, 则需校验日期格式: 支持格式: YYYY-MM-DD
                if (!empty($tmp['bill_date'])) {
                    if (preg_match('/^[0-9]{10}$/', $tmp['bill_date'])) {
                        $tmp['bill_date'] = date('Y-m-d', $tmp['bill_date']);
                    } else if (!preg_match('/^\d{4}-\d{1,2}-\d{1,2}$/', $tmp['bill_date'])) {
                        throw new ValidationException(self::$t->_("bank_flow_field_bill_date_format_error", ['line' => $line]));
                    }
                }

                $tmp['created_id'] = $user['id'];
                $tmp['created_at'] = $now;
                $tmp['bank_flow_id'] = $bank_flow_id;
                $batchData[] = $tmp;
            }

            $payDetailList = $flow->getGetDetail();
            if (!empty($payDetailList)) {
                $payDetailList->delete();
            }

            $model = new BankFlowGetDetailModel();
            $flag = $model->batch_insert($batchData);
            if ($flag === false) {
                $str = '';
                $messages = $model->getMessages();
                foreach ($messages as $message) {
                    $str .= $message . ";";
                }
                throw new BusinessException('批量插入银行流水收款流水备注失败==' . $str);
            }
            $db->commit();
            $res = true;
        } catch (ValidationException $e) {
            $db->rollback();
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->error('get=importDetailRemark error===' . $e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $res
        ];
    }


}