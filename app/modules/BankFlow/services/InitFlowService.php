<?php
namespace App\Modules\BankFlow\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowEnumModel;
use App\Modules\BankFlow\Models\BankFlowExpenseModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankListModel;
use App\Modules\BankFlow\Models\BankFlowAttachmentModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Common\Services\ImportCenterService;
use App\Repository\oa\BankAccountRepository;
use App\Repository\oa\BankFlowOaRepository;
use App\Repository\oa\BankFlowRepository;
use App\Repository\oa\BankListRepository;
use GuzzleHttp\Exception\GuzzleException;

class InitFlowService extends BaseService
{
    // 银行流水上传限制条数
    const BANK_FLOW_MAX_UPLOAD_TOTAL = 100000;// 一次最多10w, 超过则需业务拆分文件
    const BANK_FLOW_SYNC_MAX_UPLOAD_TOTAL = 10000;// 同步导入一次最多1w, 超过则走异步


    // 流水源文件是否记入日志: true 源文件上传oss，写入write_log
    const BANK_FLOW_SOURCE_FILE_LOG = true;

    // 流水导入的有效交易日期初始日期Effective 7个月前的
    const FLOW_IMPORT_EFFECTIVE_TRADE_DATE = 7 * 30;

    public $user_info = [];

    private static $instance;

    // 流水数据上传校验参数

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 银行列表
     * @return array
     */
    public function getBankList()
    {
        // 银行列表
        $bank_list = BankListModel::find([
            'columns' => ['id AS value', 'bank_name AS label'],
            'conditions' => 'is_deleted = :is_deleted:',
            'bind' => ['is_deleted' => GlobalEnums::IS_NO_DELETED]
        ])->toArray();
        return $bank_list;
    }

    /**
     * 银行和账号列表 tree
     * 说明: 不需要翻译
     */
    public function getBankAndAcctList()
    {
        // 银行列表
        $bank_list = BankListModel::find([
            'conditions' => 'is_deleted = 0',
            'columns' => ['id AS value', 'bank_name AS label']
        ])->toArray();

        // 账号列表
        $acct_list = BankAccountModel::find([
            'conditions' => 'is_deleted = 0',
            'columns' => ['id AS value', 'account AS label', 'bank_id']
        ])->toArray();

        foreach ($bank_list as $k => $bank) {
            foreach ($acct_list as $acct) {
                if ($acct['bank_id'] == $bank['value']) {
                    unset($acct['bank_id']);
                    $bank['account_item'][] = $acct;
                }
            }

            $bank_list[$k] = $bank;
        }

        return $bank_list;
    }
    /**
     * 银行账号公司去重
     */
    public function getBankCompany()
    {
        // 账号列表
        $acct_list = BankAccountModel::find([
            'conditions' => 'is_deleted = 0',
            'columns' => ['DISTINCT company_name']
        ])->toArray();
        $company_list = [];
        foreach ($acct_list as $acct) {
            $company_list[] = [
                'value' => $acct['company_name'],
                'label' => $acct['company_name'],
            ];
        }
        return $company_list;
    }

    /**
     * 费用类型列表
     * @param string $data_scenes
     * @param bool $is_batch
     * @return mixed
     */
    public function  getExpenseTypeList(string $data_scenes, bool $is_batch = false)
    {
        if (!in_array($data_scenes, ['pay_list', 'get_list', 'pay_edit', 'get_edit', 'all_list'])) {
            return [];
        }

        // 枚举应用场景
        $conditions = ['is_deleted = :is_deleted:'];
        $bind = ['is_deleted' => 0];

        switch ($data_scenes) {
            case 'pay_list': // 付款列表
            case 'pay_edit': // 付款编辑
                $conditions[] = 'is_pay = :is_pay:';
                $bind['is_pay'] = 1;

                break;
            case 'get_list': // 收款列表
            case 'get_edit': // 收款编辑
                $conditions[] = 'is_get = :is_get:';
                $bind['is_get'] = 1;

                break;
            default:
                // 不区分收付款, 取所有 all_list
                $data_scenes = 'all_list';
        }

        if ($is_batch) {
            $conditions[] = 'is_batch = :is_batch:';
            $bind['is_batch'] = 1;
        }

        $conditions_where = implode(' AND ', $conditions);
        $item = BankFlowExpenseModel::find([
            'conditions' => $conditions_where,
            'bind' => $bind,
            'columns' => ['id AS value', 'name_lang_key AS label','is_get','is_pay']
        ])->toArray();

        $translation = self::$t;
        foreach ($item as $k => $v) {
            $v['label'] = $translation[$v['label']];
            $item[$k] = $v;
        }

        // 列表页, 增加空值选项
        if (in_array($data_scenes, ['pay_list', 'get_list', 'all_list'])) {
            $null_value_item = [
                'value' => '0',
                'label' => self::$t[BankFlowEnums::get_expense_type_prefix().'0'],
                'is_get'=>1,
                'is_pay'=>1
            ];

            array_unshift($item, $null_value_item);
        }

        return $item;
    }

    /**
     * 获取枚举列表
     */
    public function getEnums()
    {
        // 所有银行账号列表
        $item['bank_account'] = BankAccountModel::getList([
            'conditions' => 'is_deleted = 0',
            'columns' => ['id AS value', 'account AS label'],
        ]);

        // 其他枚举列表
        $enums = BankFlowEnumModel::find([
            'conditions' => 'is_deleted = 0',
            'columns' => ['item', 'value', 'value_lang_key AS label']
        ])->toArray();

        $translation = self::$t;
        foreach ($enums as $val) {
            $_type = $val['item'];
            unset($val['item']);

            $val['label'] = $translation[$val['label']];
            $item[$_type][] = $val;
        }

        return $item;
    }

    /**
     * 流水异步导入任务添加
     *
     * @param object $file_obj
     * @param array $params
     * @param array $user_info
     * @param string $bank_account
     * @return mixed
     * @throws BusinessException
     * @throws GuzzleException
     * @throws ValidationException
     */
    protected function addImportTask(object $file_obj, array $params, array $user_info, string $bank_account)
    {
        // 文件生成OSS链接
        $file_path = sys_get_temp_dir() . '/' . date('YmdHis') . '_' . mt_rand(10, 10000) . ' ' . $file_obj->getName();
        $file_obj->moveTo($file_path);
        $oss_result = OssHelper::uploadFile($file_path);
        $this->logger->info(['excel_file_data_oss_result' => $oss_result]);
        if (empty($oss_result['object_url'])) {
            throw new ValidationException(static::$t->_('file_upload_error'), ErrCode::$VALIDATE_ERROR);
        }

        return ImportCenterService::getInstance()->addImportCenter($user_info, $oss_result['object_url'], ImportCenterEnums::TYPE_BANK_FLOW_INIT_UPLOAD, $params, $bank_account);
    }

    /**
     * 流水上传
     *
     * @param array $params
     * @param array $file_data
     * @param array $user_info
     * @param string $call_channel
     * @param $file_obj
     *
     * @return mixed
     * @throws GuzzleException
     */
    public function flowUpload(array $params, array $file_data, array $user_info, string $call_channel = 'api', $file_obj = null)
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';

        if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
            $import_method = ImportCenterEnums::IMPORT_METHOD_ASYNC;
        } else {
            $import_method = ImportCenterEnums::IMPORT_METHOD_SYNC;
        }

        try {
            $this->user_info = $user_info;

            // 取银行 / 账号
            $bank_acct_info = $this->modelsManager->createBuilder()
                ->from(['bank' => BankListModel::class])
                ->leftjoin(BankAccountModel::class, 'bank.id = acct.bank_id', 'acct')
                ->columns([
                    'bank.id AS bank_id',
                    'bank.bank_name',
                    'acct.id AS bank_account_id',
                    'acct.account AS bank_account',
                    'acct.currency',
                    'acct.left_amount',
                    'acct.company_name',
                    'acct.left_amount_date'
                ])
                ->andWhere('bank.id = :bank_id:', ['bank_id' => $params['bank_id']])
                ->andWhere('bank.is_deleted = 0')
                ->andWhere('acct.id = :bank_account_id:', ['bank_account_id' => $params['bank_account_id']])
                ->andWhere('acct.is_deleted = 0')
                ->getQuery()
                ->getSingleResult();
            if (empty($bank_acct_info)) {
                throw new ValidationException(self::$t->_('bank_flow_bank_acct_error'), ErrCode::$VALIDATE_ERROR);
            }
            $bank_acct_info = $bank_acct_info->toArray();

            // 1. 校验文件格式
            $file_header = array_shift($file_data);

            // 1.1 校验文件模板
            $file_check_res = $this->flowFileCheck($file_header, $bank_acct_info['bank_id'], $bank_acct_info['company_name']);
            if (!$file_check_res) {
                throw new ValidationException(self::$t->_('bank_flow_bank_template_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 处理文件行格式: 过滤空白行
            $tmp_file_data = [];
            foreach ($file_data as $k => $row) {
                $row = array_map('trim', $row);
                $row_str = implode('', $row);
                if (empty($row_str)) {
                    continue;
                }

                $tmp_file_data[] = $row;
            }

            $file_data = $tmp_file_data;
            unset($tmp_file_data);
            $file_row_count = count($file_data);
            // 1.2 校验文件总条数
            if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_API && $file_row_count > self::BANK_FLOW_MAX_UPLOAD_TOTAL) {
                throw new ValidationException(self::$t->_('bank_flow_upload_max_count_error', ['total' => self::BANK_FLOW_MAX_UPLOAD_TOTAL]), ErrCode::$VALIDATE_ERROR);
            }

            // 接口上传时, 大于1w条, 则创建异步任务
            if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_API && $file_row_count > self::BANK_FLOW_SYNC_MAX_UPLOAD_TOTAL) {
                $import_method = ImportCenterEnums::IMPORT_METHOD_ASYNC;
                $this->addImportTask($file_obj, $params, $user_info, $bank_acct_info['bank_account']);

            } else {
                // 处理文件数据
                // 2. v16197 马来CIMB和HSBC先倒序排序,再做校验和入库
                $country_code = get_country_code();
                if ($country_code == GlobalEnums::MY_COUNTRY_CODE && in_array($params['bank_id'], [BankFlowEnums::BANK_CODE_CIMB, BankFlowEnums::BANK_CODE_HSBC_MY])) {
                    $file_data = array_reverse($file_data);
                }

                // 3. 提取文件通用数据
                $common_data = $this->getFlowCommonData($file_data, $bank_acct_info, $file_header);

                // 4. 银行不正确,请检查!
                $bank_names = array_unique(array_column($common_data, 'bank_name'));
                if (count($bank_names) == 1 && $bank_names[0] === $bank_acct_info['bank_name']) {

                } else {
                    throw new ValidationException(self::$t->_('bank_flow_bank_name_error'), ErrCode::$VALIDATE_ERROR);
                }

                // 4.1 银行账号校验
                $bank_accounts = array_unique(array_column($common_data, 'bank_account'));
                if (count($bank_accounts) == 1 && $bank_accounts[0] === $bank_acct_info['bank_account']) {

                } else {
                    throw new ValidationException(self::$t->_('bank_flow_bank_acct_error'), ErrCode::$VALIDATE_ERROR);
                }

                // 4.2 校验文件期初与本批首条数据金额
                // 公式: 银行账号的当前余额 - 文件中第一行付款金额合计 +文件中第一行收款金额合计 =文件中第一行余额金额
                $check_row_data = $common_data[0];
                $check_message = "文件首行余额";

                //银行账户余额
                $account_left_amount = empty($bank_acct_info['left_amount']) ? 0 : $bank_acct_info['left_amount'];
                $init_balance_amount = (string)round(($account_left_amount - $check_row_data['pay_amount'] + $check_row_data['get_amount']), 2);
                $first_row_bank_left_amount = (string)$check_row_data['bank_left_amount'];
                $balance_amount_check_res = bccomp($init_balance_amount, $first_row_bank_left_amount, 2) == 0;

                $balance_amount_check_log = "银行流水上传 - 上传人: {$user_info['id']};".PHP_EOL;
                $balance_amount_check_log .= "期初金额计算结果: $init_balance_amount, $check_message: $first_row_bank_left_amount; 两个余额是否相等: $balance_amount_check_res;".PHP_EOL;
                $balance_amount_check_log .= "账号信息: ".json_encode($bank_acct_info, JSON_UNESCAPED_UNICODE)."; 上传文件首行信息: ".json_encode($check_row_data, JSON_UNESCAPED_UNICODE).PHP_EOL;
                $this->logger->info($balance_amount_check_log);
                if (!$balance_amount_check_res) {
                    throw new ValidationException(self::$t->_('bank_flow_upload_beginning_amount_error'), ErrCode::$VALIDATE_ERROR);
                }

                // 4.3 每行的期末余额校验
                $last_row = [];
                foreach ($common_data as $key => $curr_row) {
                    if (!empty($last_row) && (bccomp((string)round($last_row['bank_left_amount'] + $curr_row['get_amount'] - $curr_row['pay_amount'],
                                2), $curr_row['bank_left_amount'], 2) != 0)) {
                        throw new ValidationException(static::$t->_('bank_flow_upload_ending_balance_error', ['row_num' => $key + 1]), ErrCode::$VALIDATE_ERROR);
                    }

                    $last_row = $curr_row;
                }

                // 5. 数据分批入库
                $db = $this->getDI()->get('db_oa');
                $db->begin();
                $bank_flow_model = new BankFlowModel();

                $common_data_chunk = array_chunk($common_data, 5000);
                foreach ($common_data_chunk as $flow_chunk) {
                    if ($bank_flow_model->batch_insert($flow_chunk) === false) {
                        throw new BusinessException('银行流水上传 - 批量写入失败 - ' . json_encode($flow_chunk,JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_UPLOAD_ERROR);
                    }

                    if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
                        sleep(3);
                    }
                }
                unset($common_data_chunk);

                // 6. 更新该银行账号的余额
                //其他是交易日期正序排列所以需要更新账户余额为最后一行数据
                $end_row_data = array_pop($common_data);
                $bank_account_model = BankAccountModel::findFirst($bank_acct_info['bank_account_id']);
                $bank_account_update = [
                    'left_amount' => $end_row_data['bank_left_amount'],
                    'left_amount_date' => $end_row_data['date'],
                    'updated_id' => $user_info['id'] ?? 0,
                ];
                if ($bank_account_model->i_update($bank_account_update) === false) {
                    throw new BusinessException('银行流水上传 - 银行账号余额更新失败 - ' . json_encode($bank_account_model->toArray(),JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_UPLOAD_ERROR);
                }

                $db->commit();

                // 7. 流水源文件数据日志
                $oss_file_url = '';
                $file_name = 'BankFlowUpload_' . date('Ymd') . '.xlsx';
                $oss_upload_res = $this->exportExcel($file_header, $file_data, $file_name);
                if (!empty($oss_upload_res['code']) && $oss_upload_res['code'] == 1) {
                    $oss_file_url = $oss_upload_res['data'];
                }

                $this->logger->info("银行流水上传 - [上传人{$user_info['id']} | {$user_info['name']}], 银行流水文件oss地址: " . $oss_file_url);
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('银行流水上传 - 上传异常: ' . $real_message);
        }

        if (!empty($message) && isset($db)) {
            $db->rollback();
        }

        $file_row_count = $file_row_count ?? 0;

        // api 和 task 调用, 返回的字段不同
        $data = [
            'data_count' => $file_row_count,
            'success_count' => $code == ErrCode::$SUCCESS && $import_method == ImportCenterEnums::IMPORT_METHOD_SYNC ? $file_row_count : 0,
            'error_hint' => $code == ErrCode::$SUCCESS && $import_method == ImportCenterEnums::IMPORT_METHOD_SYNC ? '' : $message,
            'import_method' => $import_method
        ];

        if ($call_channel == ImportCenterEnums::CALL_METHOD_METHOD_TASK) {
            $data['success_count'] = $code == ErrCode::$SUCCESS ? $file_row_count : 0;
            $data['error_count'] = $code == ErrCode::$SUCCESS ? 0 : $file_row_count;
            $data['result_file_url'] = $oss_file_url ?? '';
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 上传银行文件格式校验
     * @param array $file_header
     * @param string $bank_id
     * @param string $account_company_name  银行账号的公司名称
     * @return mixed
     */
    protected function flowFileCheck(array $file_header, string $bank_id, string $account_company_name)
    {
        $file_tpl_header = $this->getFlowFileHeaderDefinition($bank_id, $account_company_name);
        if (empty($file_tpl_header)) {
            return false;
        }
        $file_header = array_map( function($v) {
            return strtolower(trim($v));
        }, $file_header);


        // 防止原文件某些列动态增加, 导出和配置的模板列匹配不上
//        $file_header = array_slice($file_header, 0, count($file_tpl_header));

        $file_header = implode('', $file_header);
        $file_tpl_header = implode('', $file_tpl_header);

        return $file_header == $file_tpl_header;
    }

    /**
     * 获取银行流水文件头部定义
     * @param string $bank_id
     * @param string $account_company_name 银行账号的公司名称
     * @return mixed
     */
    protected function getFlowFileHeaderDefinition(string $bank_id, string $account_company_name)
    {
        $th_bay_not_flash_pay_condition = get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $bank_id == BankFlowEnums::BANK_CODE_BAY && $account_company_name != BankFlowEnums::BANK_ACCOUNT_NAME_PAY;

        if (in_array(strtolower($bank_id),BankFlowEnums::get_common_bank_id()) || $th_bay_not_flash_pay_condition) {
            $bank_id = BankFlowEnums::get_common_template_id();
        }
        return BankFlowEnums::get_bank_flow_file_header()[$bank_id] ?? [];
    }

    /**
     * 提取文件数据
     * @param array $file_data
     * @param array $bank_info
     * @param array $file_header
     * @return array|mixed
     * @throws ValidationException
     */
    protected function getFlowCommonData(array $file_data, array $bank_info, array $file_header)
    {
        if (empty($file_data)) {
            return [];
        }

        $common_data = [];
        $country_code = get_country_code();
        if ($country_code == GlobalEnums::TH_COUNTRY_CODE) {
            switch (strtolower($bank_info['bank_name'])) {
                // TTB 银行取默认值的字段(原TMB)
                case 'ttb':
                    $common_data = $this->getTmbBankFileData($file_data, $bank_info);
                    break;
                case 'hsbc':
                    // HSBC 银行取默认值的字段
                    $common_data = $this->getHsbcBankFileData($file_data, $bank_info);
                    break;
                case 'kbank':
                    // K_BANK 银行取默认值的字段
                    $common_data = $this->getKBankFileData($file_data, $bank_info);
                    break;
                case 'scb':
                    // SCB 银行取默认值的字段
                    $common_data = $this->getScbBankFileData($file_data, $bank_info);
                    break;
                case 'bay':
                    if ($bank_info['company_name'] == BankFlowEnums::BANK_ACCOUNT_NAME_PAY) {
                        $common_data = $this->getBayBankFileData($file_data, $bank_info);
                    } else {
                        $common_data = $this->getCommonBankFileData($file_data, $bank_info);
                    }
                    break;
                case 'ktb':
                    $common_data = $this->getKtbBankFileData($file_data, $bank_info);
                    break;
                default:
                    //通用模版 默认字段
                    $common_data = $this->getCommonBankFileData($file_data, $bank_info);
                    break;
            }
        } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE) {
            switch (strtolower($bank_info['bank_id'])) {
                case 1:
                    // union bank 银行取默认值的字段
                    $common_data = $this->getUnionBankFileData($file_data, $bank_info);
                    break;
                case 3:
                    // security 银行取默认值的字段
                    $common_data = $this->getSecurityBankFileData($file_data, $bank_info);
                    break;
                case 4:
                    // bdo 银行取默认值的字段
                    $common_data = $this->getBdoBankFileData($file_data, $bank_info);
                    break;
                default:
                    //通用模版 默认字段
                    $common_data = $this->getHandleCommonBankFileData($file_data, $bank_info);
                    break;
            }
        } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
            //解析马来国家的各个银行对应的模版文件
            switch ($bank_info['bank_id']) {
                case BankFlowEnums::BANK_CODE_CIMB:
                    // CIMB 银行取默认值的字段
                    $common_data = $this->getCimbBankFileData($file_data, $bank_info, $file_header);
                    break;
                case BankFlowEnums::BANK_CODE_MAY_BANK;
                    //Maybank 银行取默认值的字段
                    $common_data = $this->getMayBankFileData($file_data, $bank_info, $file_header);
                    break;
                case BankFlowEnums::BANK_CODE_HSBC_MY:
                    // HSBC MY 银行取默认值的字段
                    $common_data = $this->getHsbcMyBankFileData($file_data, $bank_info, $file_header);
                    break;
                case BankFlowEnums::BANK_CODE_OCBC_MY:
                    // OCBC MY 银行取默认值的字段
                    $common_data = $this->getOcbcMyBankFileData($file_data, $bank_info, $file_header);
                    break;
                default:
                    //通用模版 默认字段
                    $common_data = $this->getHandleCommonBankFileData($file_data, $bank_info);
                    break;
            }
        }

        return $common_data;
    }

    /**
     * TTB 银行流水数据提取(原TMB)
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getTmbBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[6]) ? $row[6] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[5]) ? $row[5] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[1];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_('bank_flow_upload_init_date_error', ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]), ErrCode::$VALIDATE_ERROR);
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_format_error'), ErrCode::$VALIDATE_ERROR);
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 交易时间
            $time = format_excel_time($row[11]);
            $time = $time ? $time : '00:00:00';

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $row[0],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $row[4],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-' . $pay_amount,
                'bank_left_amount' => !empty($row[7]) ? $row[7] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => trim($row[9] . ', ' . $row[14], '"'),
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * k-bank 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getKBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[5]) ? $row[5] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[4]) ? $row[4] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, '-') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $timestamp = strtotime($row_date);
                    if ($timestamp) {
                        $date = date('Y-m-d', $timestamp);
                    }
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $time = '';
            $row_time = $row[1];
            if (!empty($row_time)) {
                if (stripos($row_time, ':') === false) {
                    $time = date('H:i:s', $row_time);
                } else {
                    $time_section_count = substr_count($row_time, ':');
                    switch ($time_section_count) {
                        case 1:
                            $time = $row_time.':0';
                            break;
                        case 2:
                            $time = $row_time;
                            break;
                    }
                }
            }

            $time = $time ? $time : '0:0:0';

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $row[3],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[6]) ? $row[6] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[2],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * SCB 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getScbBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[7]) ? $row[7] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[6]) ? $row[6] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[1];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $time = '';
            $row_time = $row[2];
            if (!empty($row_time)) {
                if (stripos($row_time, ':') === false) {
                    $time = date('H:i:s', $row_time);
                } else {
                    $time_section_count = substr_count($row_time, ':');
                    switch ($time_section_count) {
                        case 1:
                            $time = $row_time.':0';
                            break;
                        case 2:
                            $time = $row_time;
                            break;
                    }
                }
            }

            $time = $time ? $time : '0:0:0';

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $row[0],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $row[5],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[9]) ? $row[9] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[10],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * Bay 银行 flash pay账号 流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getBayBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            $get_amount = empty($row[5]) ? 0 : $row[5];
            $pay_amount = empty($row[4]) ? 0 : $row[4];
            $type = !empty($get_amount) ? 1 : 2;
            if (!is_numeric($get_amount) || !is_numeric($pay_amount)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_amount_error"));
            }

            $date = '';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') !== false) {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                } elseif (is_numeric($row_date) && strlen($row_date)==10) {
                    $date = date('Y-m-d', $row_date);
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            //支票号
            $ticket_no = $row[1].$row[8];
            //描述
            $trade_desc = $row[2].$row[7];
            //交易时间
            $time = '0:0:0';
            if (preg_match('/\d{2}:\d{2}:\d{2}/', $row[10])) {
                $time = $row[10];
            } elseif (is_numeric($row[10])) {
                $time = date('H:i:s', $row[10]);
            }

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $ticket_no,
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[6]) ? $row[6] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $trade_desc,
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * Ktb 银行 流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getKtbBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 金额处理 正数为收款,负数为付款
            if (empty($row[5]) || !is_numeric($row[5])) {
                throw new ValidationException(self::$t->_("bank_flow_upload_amount_error"));
            }
            // 余额
            if (!is_numeric($row[7])) {
                throw new ValidationException(self::$t->_("bank_flow_upload_balance_error"));
            }
            $get_amount = $pay_amount = 0;
            if ($row[5]>0) {
                $type = 1;
                $get_amount = $row[5];
            } else {
                $type = 2;
                $pay_amount = $row[5];
            }

            $date = '';
            $time = '00:00:00';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') !== false) {
                    $date_and_time = explode(' ', $row_date);
                    // 日期
                    $date_arr = explode('/',$date_and_time[0]);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                    // 时间
                    $time = $date_and_time[1]??'0:0:0';
                } elseif (is_numeric($row_date) && strlen($row_date)==10) {
                    // 10位时间戳
                    $date = date('Y-m-d',$row_date);
                    $time = date('H:i:s',$row_date);
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            //支票号
            $ticket_no = $row[1].$row[4];
            //描述
            $trade_desc = $row[2].$row[3];

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $ticket_no,
                'get_amount' => $get_amount,
                'pay_amount' => abs($pay_amount),
                'real_amount' => $type == 1 ? $get_amount : $pay_amount,
                'bank_left_amount' => !empty($row[7]) ? $row[7] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $trade_desc,
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }
    /**
     * Hsbc 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getHsbcBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[22]) ? $row[22] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[23]) ? $row[23] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[25];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $time = '';
            $row_time = $row[24];
            if (!empty($row_time)) {
                if (stripos($row_time, ':') === false) {
                    $time = date('H:i:s', $row_time);
                } else {
                    $time_section_count = substr_count($row_time, ':');
                    switch ($time_section_count) {
                        case 1:
                            $time = $row_time.':0';
                            break;
                        case 2:
                            $time = $row_time;
                            break;
                    }
                }
            }

            $time = $time ? $time : '0:0:0';

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => !empty($row[1]) ? str_replace('-', '', $row[1]) : '',
                'date' => $date,
                'time' => $time,
                'ticket_no' => '',
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[26]) ? $row[26] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[18],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * 流水 - 上传附件(支持多个)
     * @param array $params
     * @param array $user_info
     * @return mixed
     */
    public function uploadAttachments(array $params, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data = [];

        try {
            $flow_model = BankFlowModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['flow_id']]
            ]);
            if (empty($flow_model)) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            // 取出当前流水的所有附件
            $exist_attachment_item_model = BankFlowAttachmentModel::find([
                'conditions' => 'bank_flow_id = :bank_flow_id: AND is_deleted = :is_deleted:',
                'bind' => ['bank_flow_id' => $flow_model->id, 'is_deleted' => 0],
            ]);

            // 当前提交的所有附件
            $attachment_arr = !empty($params['attachment_arr']) ? array_column($params['attachment_arr'], null, 'object_key') : [];

            // 判断新提交的附件：哪些是新增的，哪些是删除的
            // 1. 删除的
            if (!empty($exist_attachment_item_model)) {
                foreach ($exist_attachment_item_model as $model) {
                    // 库有&提交的无：则删
                    if (!array_key_exists($model->object_key, $attachment_arr)) {
                        $model->is_deleted = 1;
                        $model->deleted_id = $user_info['id'];
                        $model->deleted_at = date('Y-m-d H:i:s');

                        if ($model->save() === false) {
                            throw new BusinessException('银行流水 - 已有附件删除失败 = ' . json_encode($model->toArray(),JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_ATTACHMENT_REMOVE_ERROR);
                        }

                        $this->getDI()->get('logger')->info('银行流水 - 附件删除成功: ' . json_encode($model->toArray(),JSON_UNESCAPED_UNICODE));

                    } else {
                        // 库有&提交的有：无需变动
                        unset($attachment_arr[$model->object_key]);
                    }
                }
            }

            // 2. 新增的, 附件生成
            $this->getDI()->get('logger')->info('银行流水 - 待添加的附件: ' . json_encode($attachment_arr,JSON_UNESCAPED_UNICODE));

            if (!empty($attachment_arr)) {
                $attachment_item = [];
                foreach ($attachment_arr as $k => $file) {
                    $attachment_item[] = [
                        'bank_flow_id' => $flow_model->id,
                        'bucket_name' => $file['bucket_name'],
                        'object_key' => $file['object_key'],
                        'file_name' => $file['file_name'],
                        'created_id' => $user_info->id ?? 0,
                        'created_at' => date('Y-m-d H:i:s'),
                    ];
                }

                $attach = new BankFlowAttachmentModel();
                $attach_bool = $attach->batch_insert($attachment_item);
                if ($attach_bool === false) {
                    throw new BusinessException('银行流水 - 附件批量添加失败 = ' . json_encode($attachment_arr,JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_ATTACHMENT_UPLOAD_ERROR);
                }
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 附件添加失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 流水 - 上传附件列表
     * @param array $params
     * @return mixed
     */
    public function getFlowAttachmentList(array $params)
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data = [];

        try {
            $flow_model = BankFlowModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['flow_id']]
            ]);
            if (empty($flow_model)) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            // 附件
            $data = BankFlowAttachmentModel::find([
                'conditions' => 'bank_flow_id = :bank_flow_id: AND is_deleted = :is_deleted:',
                'bind' => ['bank_flow_id' => $flow_model->id, 'is_deleted' => 0],
                'columns' => ['id AS attachment_id', 'bank_flow_id AS flow_id', 'bucket_name', 'object_key', 'file_name']
            ])->toArray();

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 附件列表获取失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 流水 - 删除附件
     * @param array $params
     * @param array $user_info
     * @return mixed
     */
    public function removeAttachment(array $params, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $real_message = $message = '';
        $data = [];

        try {
            // 附件
            $attachment_model = BankFlowAttachmentModel::findFirst([
                'conditions' => 'id = :id: AND bank_flow_id = :bank_flow_id: AND is_deleted = :is_deleted:',
                'bind' => ['id' => $params['attachment_id'], 'bank_flow_id' => $params['flow_id'], 'is_deleted' => 0],
            ]);
            if (empty($attachment_model)) {
                throw new ValidationException(self::$t->_("bank_flow.attachment_info_null"));
            }

            $attachment_model->is_deleted = 1;
            $attachment_model->deleted_id = $user_info['id'] ?? 0;
            $attachment_model->deleted_at = date('Y-m-d H:i:s');
            if ($attachment_model->save() === false) {
                throw new BusinessException('银行流水 - 附件删除失败 = ' . json_encode($attachment_model->toArray(),JSON_UNESCAPED_UNICODE), ErrCode::$BANK_FLOW_ATTACHMENT_REMOVE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 附件列表获取失败: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 获取流水上传时，有效交易日期的初始值
     */
    protected function getFlowImportInitEffectiveDate()
    {
        return date('Y-m-d', time() - self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE * 86400);
    }

    /**
     * 通用模版 银行流水数据提取
     *（bay bbl tbank boc）
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getCommonBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();

        $today_date = date('Y-m-d');
        $time = '00:00:00';
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[4]) ? $row[4] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[3]) ? $row[3] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[1];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    // 手动录入的日期格式: YYYY/MM/DD
                    $date_arr = explode('/', $row_date);

                    // 当月/日1位数时，补充前导0: 兼容日期1位数时逻辑判断失效的情况
                    $date_arr[1] = mb_strlen($date_arr[1]) == 1 ? '0'.$date_arr[1] : $date_arr[1];
                    $date_arr[2] = mb_strlen($date_arr[2]) == 1 ? '0'.$date_arr[2] : $date_arr[2];

                    $date = $date_arr[0].'-'.$date_arr[1].'-'.$date_arr[2];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/^\d{4}-\d{1,2}-\d{1,2}$/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => !empty($row[0]) ? str_replace('-', '', $row[0]) : '',
                'date' => $date,
                'time' => $time,
                'ticket_no' => '',
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[5]) ? $row[5] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[6],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * 菲律宾-union bank 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getUnionBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[6]) ? $row[6] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[5]) ? $row[5] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, 'T') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('T', $row_date);
                    $date = date('Y-m-d',strtotime($date_arr[0]));
                }
            }
            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }


            $time = '';
            $row_time = $row[1];
            if (!empty($row_time)) {
                if (stripos($row_time, 'T') === false) {
                    $time = date('H:i:s', $row_time);
                } else {
                    $time_arr = explode('T', $row_time);
                    $time = date('H:i:s',strtotime($time_arr[1]));
                }
            }
            $time = $time ? $time : '0:0:0';
            $extra = [
                'reference_number'=>$row[8],'remarks'=>$row[9],'remarks_1'=>$row[9],'remarks_2'=>$row[10],
                'branch'=>$row[11],'sender_name'=>$row[12],'sender_address'=>$row[13],'sender_bank'=>$row[14],
                'credited_amount'=>$row[15],'original_amount'=>$row[16],'net_amount'=>$row[17],'remittance_ref_1'=>$row[18],
                'remittance_ref_2'=>$row[19],'remittance_ref_3'=>$row[20], 'remittance_ref_4'=>$row[21],'remittance_ref_5'=>$row[22],
                'reversed_transaction'=>$row[23],'reversal_transaction_ref_no'=>$row[24],'biller_name'=>$row[25],
                'payment_channel'=>$row[26],'bills_payment_ref_1'=>$row[27],'bills_payment_ref_2'=>$row[28],
                'bills_payment_ref_3'=>$row[29],'bills_payment_ref_4'=>$row[30], 'bills_payment_ref_5'=>$row[30],
                'bills_payment_ref_6'=>$row[31],'bills_payment_ref_7'=>$row[32],'bills_payment_ref_8'=>$row[33],
                'bills_payment_ref_9'=>$row[34], 'bills_payment_ref_10'=>$row[35]
            ];
            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => $time,
                'ticket_no' => $row[4],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[7]) ? $row[7] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[3],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
                'extra' => json_encode($extra),
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * 菲律宾-security bank 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getSecurityBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            //第一列空就算为最后一行, 不继续
            if (empty($row[0])){
                break;
            }

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[3]) ? $row[3] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[2]) ? $row[2] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[1].'-'.$date_arr[0];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 余额
            if (empty($row[4])){
                $bank_left_amount = 0;
            }elseif(stripos($row[4],'CR')){
                $bank_left_amount = str_ireplace('CR','',$row[4]);
                //带CR的千位符也得去掉
                $bank_left_amount = str_ireplace(',','',$bank_left_amount);
            }elseif(stripos($row[4],'DR')){
                $bank_left_amount = str_ireplace('DR','',$row[4]);
                //带CR的千位符也得去掉
                $bank_left_amount = str_ireplace(',','',$bank_left_amount);
            }else{
                $bank_left_amount = $row[4];
            }
            $extra = ['narrative'=>$row[5]];
            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => '0:0:0',
                'ticket_no' => '',
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => $bank_left_amount,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[1],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
                'extra' => json_encode($extra),
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * 菲律宾-bdo bank 银行流水数据提取
     *
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getBdoBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {

            $row = array_map('trim', $row);

            //第一列包含"Report Date"的行, 是最后一行, 且这行不要
            $first = str_replace(' ','',strtolower($row[0]));
            if (stripos($first,'reportdate') !== false){
                break;
            }

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[4]) ? $row[4] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[3]) ? $row[3] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[0];
            if (!empty($row_date)) {
                if (stripos($row_date, '/') === false) {
                    $date = date('Y-m-d', $row_date);
                } else {
                    $date_arr = explode('/', $row_date);
                    $date = $date_arr[2].'-'.$date_arr[0].'-'.$date_arr[1];
                }
            }

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $extra = ['branch'=>$row[1]];
            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => '0:0:0',
                'ticket_no' => $row[6],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[5]) ? $row[5] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[2],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
                'extra' => json_encode($extra),
            ];
        }
        unset($file_data);
        return $common_data;
    }

    /**
     * 菲律宾 、马来
     * 采用手工模版上传银行流水的模版数据提取方法
     * @param array $file_data
     * @param array $bank_info
     * @return mixed
     * @throws ValidationException
     */
    protected function getHandleCommonBankFileData(array $file_data, array $bank_info)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);

            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[5]) ? $row[5] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[4]) ? $row[4] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            $date = '';
            $row_date = $row[1];
            if (!empty($row_date)) {
                $date = $this->handleUploadFileDate($row_date);
            }
            //手工模板校验日期格式
            $validations['date'] = 'Required|Date|>>>:' . self::$t['flow_date_not_ymd'];
            Validation::validate(['date'=>$date], $validations);

            // 银行日期强校验
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            if ($date > $today_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            $common_data[] = [
                'type' => $type,
                'bank_id' => $bank_info['bank_id'],
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_info['bank_account_id'],
                'bank_account' => $bank_info['bank_account'],
                'date' => $date,
                'time' => '0:0:0',
                'ticket_no' => $row[2],
                'get_amount' => $get_amount,
                'pay_amount' => $pay_amount,
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,
                'bank_left_amount' => !empty($row[6]) ? $row[6] : 0,
                'currency' => $bank_info['currency'],
                'trade_desc' => $row[7],
                'created_staff_id' => $staff_id,
                'created_staff_name' => $staff_name,
                'created_at' => $curr_time,
                'updated_staff_id' => $staff_id,
                'updated_staff_name' => $staff_name,
                'updated_at' => $curr_time,
            ];
        }

        unset($file_data);
        return $common_data;
    }

    /**
     * 上传日期格式的兼容处理
     * @param string $file_src_date 日期
     * @return false|string
     */
    private function handleUploadFileDate(string $file_src_date)
    {
        if (preg_match('/\d{10}/', $file_src_date)) {
            // 时间戳
            $date = date('Y-m-d', $file_src_date);
        } else if (preg_match('/\d{8}/', $file_src_date) ){
            //年月日
            $date = date('Y-m-d', strtotime($file_src_date));
        } else if (stripos($file_src_date, '/') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        } else if (stripos($file_src_date, '-') > 0) {
            $date = date('Y-m-d', strtotime($file_src_date));
        } else {
            $date = $file_src_date;
        }
        return $date;
    }

    /**
     * 马来-CIMB bank 银行流水数据提取
     *
     * @param array $file_data 文件内容
     * @param array $bank_info 选择的银行信息
     * @param array $file_header 标题
     * @return mixed
     * @throws ValidationException
     */
    protected function getCimbBankFileData(array $file_data, array $bank_info, array $file_header)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);
            $date = '';
            //交易日期
            $row_date = $row[2];
            if (!empty($row_date) && preg_match('/^(\d{7}|\d{8})$/', $row_date)) {
                if (strlen($row_date) == 8) {
                    $date = substr($row_date,4,4).'-'.substr($row_date, 2,2).'-'.substr($row_date,0,2);
                } else {
                    $date = substr($row_date,3,4).'-'.substr($row_date, 1,2).'-0'.substr($row_date,0,1);
                }
            }
            //文件日期不能晚于导入当日
            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 银行日期强校验,不能早于当日210天 
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            //交易金额
            $amount = is_numeric($row[7]) ? $row[7] : 0;
            $pay_amount = 0;//付款金额
            $get_amount = 0;//收款金额
            $type = 1;
            if ($row[8] =='D') {
                //付款金额
                $pay_amount = $amount;
                $type = 2;
            } else if ($row[8] == 'C') {
                //收款金额
                $get_amount = $amount;
                $type = 1;
            }

            //交易时间
            $time = '00:00:00';
            //组装excel原文件内容
            $extra = [];
            foreach ($file_header as $k=>$value) {
                $extra[strtolower($value)] = $row[$k] ?? '';
            }
            $common_data[] = [
                'type' => $type,//1收入，2支出
                'bank_id' => $bank_info['bank_id'],//银行id
                'bank_name' => $bank_info['bank_name'],//银行名字
                'bank_account_id' => $bank_info['bank_account_id'],//银行账号id
                'bank_account' => $bank_info['bank_account'],//银行账号
                'date' => $date,//交易日期
                'time' => $time,//交易时间
                'ticket_no' => $row[15]??'',//支票号码
                'get_amount' => $get_amount,//收款金额
                'pay_amount' => $pay_amount,//付款金额
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,//实际金额，带上type，有正负
                'bank_left_amount' => !empty($row[9]) ? $row[9] : 0,//银行账号余额
                'currency' => $bank_info['currency'],//币种
                'trade_desc' => $row[4],//描述
                'created_staff_id' => $staff_id,//创建人
                'created_staff_name' => $staff_name,//创建人名字
                'created_at' => $curr_time,//创建时间
                'updated_staff_id' => $staff_id,//更新人
                'updated_staff_name' => $staff_name,//更新人名字
                'updated_at' => $curr_time,//更新时间
                'extra' => json_encode($extra),//额外数据-excel文件原内容
            ];
        }
        unset($file_data);
        return $common_data;
    }

    /**
     * 马来-Maybank 银行流水数据提取
     *
     * @param array $file_data 文件内容
     * @param array $bank_info 选择的银行信息
     * @param array $file_header 标题
     * @return mixed
     * @throws ValidationException
     */
    protected function getMayBankFileData(array $file_data, array $bank_info, array $file_header)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);
            $date = '';
            //交易日期
            $row_date = $row[12];
            if (!empty($row_date) && stripos($row_date, GlobalEnums::MY_COUNTRY_CODE) !== false) {
                $date = trim(explode( GlobalEnums::MY_COUNTRY_CODE,$row_date)[0]);
                $date = str_replace('/',' ',$date);
                $date = date('Y-m-d', strtotime($date));
            }
            //文件日期不能晚于导入当日
            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 银行日期强校验,不能早于当日210天 
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            //交易时间
            $time = '';
            $row_time = $row[13];
            if (!empty($row_time) && stripos($row_time, GlobalEnums::MY_COUNTRY_CODE) !== false) {
                $time = trim(explode( GlobalEnums::MY_COUNTRY_CODE,$row_time)[0]).':00';
            }

            //交易金额
            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[17]) ? $row[17] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[16]) ? $row[16] : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            //组装excel原文件内容
            $extra = [];
            foreach ($file_header as $k=>$value) {
                $extra[strtolower($value)] = $row[$k] ?? '';
            }
            $common_data[] = [
                'type' => $type,//1收入，2支出
                'bank_id' => $bank_info['bank_id'],//银行id
                'bank_name' => $bank_info['bank_name'],//银行名字
                'bank_account_id' => $bank_info['bank_account_id'],//银行账号id
                'bank_account' => $bank_info['bank_account'],//银行账号
                'date' => $date,//交易日期
                'time' => $time,//交易时间
                'ticket_no' => '',//支票号码
                'get_amount' => $get_amount,//收款金额
                'pay_amount' => $pay_amount,//付款金额
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,//实际金额，带上type，有正负
                'bank_left_amount' => !empty($row[22]) ? $row[22] : 0,//银行账号余额
                'currency' => $bank_info['currency'],//币种
                'trade_desc' => $row[14],//描述
                'created_staff_id' => $staff_id,//创建人
                'created_staff_name' => $staff_name,//创建人名字
                'created_at' => $curr_time,//创建时间
                'updated_staff_id' => $staff_id,//更新人
                'updated_staff_name' => $staff_name,//更新人名字
                'updated_at' => $curr_time,//更新时间
                'extra' => json_encode($extra),//额外数据-excel文件原内容
            ];
        }
        unset($file_data);
        return $common_data;
    }

    /**
     * 马来-HSBC 银行流水数据提取
     *
     * @param array $file_data 文件内容
     * @param array $bank_info 选择的银行信息
     * @param array $file_header 标题
     * @return mixed
     * @throws ValidationException
     */
    protected function getHsbcMyBankFileData(array $file_data, array $bank_info, array $file_header)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);
            $date = '';
            //交易日期
            $row_date = $row[24];
            if (!empty($row_date) && stripos($row_date, '/') !== false) {
                $row_date_arr = explode('/', $row_date);
                $date = $row_date_arr[2].'-'.$row_date_arr[1].'-'.$row_date_arr[0];
                $date = date('Y-m-d', strtotime($date));
            }
            //文件日期不能晚于导入当日
            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 银行日期强校验,不能早于当日210天 
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            //交易时间
            $time = '00:00:00';

            //交易金额
            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[21]) ? $row[21] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[22]) ? abs($row[22]) : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            //组装excel原文件内容
            $extra = [];
            foreach ($file_header as $k=>$value) {
                $extra[strtolower($value)] = $row[$k] ?? '';
            }
            $common_data[] = [
                'type' => $type,//1收入，2支出
                'bank_id' => $bank_info['bank_id'],//银行id
                'bank_name' => $bank_info['bank_name'],//银行名字
                'bank_account_id' => $bank_info['bank_account_id'],//银行账号id
                'bank_account' => $bank_info['bank_account'],//银行账号
                'date' => $date,//交易日期
                'time' => $time,//交易时间
                'ticket_no' => '',//支票号码
                'get_amount' => $get_amount,//收款金额
                'pay_amount' => $pay_amount,//付款金额
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,//实际金额，带上type，有正负
                'bank_left_amount' => !empty($row[23]) ? $row[23] : 0,//银行账号余额
                'currency' => $bank_info['currency'],//币种
                'trade_desc' => $row[17],//描述
                'created_staff_id' => $staff_id,//创建人
                'created_staff_name' => $staff_name,//创建人名字
                'created_at' => $curr_time,//创建时间
                'updated_staff_id' => $staff_id,//更新人
                'updated_staff_name' => $staff_name,//更新人名字
                'updated_at' => $curr_time,//更新时间
                'extra' => json_encode($extra),//额外数据-excel文件原内容
            ];
        }
        unset($file_data);
        return $common_data;
    }

    /**
     * 马来-OCBC 银行流水数据提取
     *
     * @param array $file_data 文件内容
     * @param array $bank_info 选择的银行信息
     * @param array $file_header 标题
     * @return mixed
     * @throws ValidationException
     */
    protected function getOcbcMyBankFileData(array $file_data, array $bank_info, array $file_header)
    {
        $common_data = [];
        $staff_id = $this->user_info['id'] ?? 0;
        $staff_name = $this->user_info['name'] ?? 0;

        $curr_time = date('Y-m-d H:i:s');
        $init_effective_trade_date = $this->getFlowImportInitEffectiveDate();
        $today_date = date('Y-m-d');
        foreach ($file_data as $row) {
            $row = array_map('trim', $row);
            $date = '';
            //交易日期
            $row_date = $row[12];
            if (!empty($row_date) && preg_match('/^\d{8}$/', $row_date)) {
                $date = date('Y-m-d', strtotime($row_date));
            }
            //文件日期不能晚于导入当日
            if ($date > $today_date || !preg_match('/\d{4}-\d{2}-\d{2}/', $date)) {
                throw new ValidationException(self::$t->_("bank_flow_upload_date_format_error"));
            } else if ($bank_info['left_amount_date'] && $date < $bank_info['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_upload_date_error', ['left_amount_date' => $bank_info['left_amount_date']]), ErrCode::$VALIDATE_ERROR);
            }

            // 银行日期强校验,不能早于当日210天 
            if ($date < $init_effective_trade_date) {
                throw new ValidationException(self::$t->_("bank_flow_upload_init_date_error", ['days' => self::FLOW_IMPORT_EFFECTIVE_TRADE_DATE]));
            }

            //交易时间
            $time = '00:00:00';

            //交易金额
            // 收付款金额数据根据数值型判断: 收付款金额列不会存在同时为0 或 同时非0的情况
            $get_amount = is_numeric($row[14]) ? $row[14] : 0;// 收款(收入)
            $pay_amount = is_numeric($row[13]) ? abs($row[13]) : 0;// 付款(支出)
            $type = !empty($get_amount) ? 1 : 2; // 1-收款; 2-付款

            //组装excel原文件内容
            $extra = [];
            foreach ($file_header as $k=>$value) {
                $extra[strtolower($value)] = $row[$k] ?? '';
            }
            $common_data[] = [
                'type' => $type,//1收入，2支出
                'bank_id' => $bank_info['bank_id'],//银行id
                'bank_name' => $bank_info['bank_name'],//银行名字
                'bank_account_id' => $bank_info['bank_account_id'],//银行账号id
                'bank_account' => $bank_info['bank_account'],//银行账号
                'date' => $date,//交易日期
                'time' => $time,//交易时间
                'ticket_no' => '',//支票号码
                'get_amount' => $get_amount,//收款金额
                'pay_amount' => $pay_amount,//付款金额
                'real_amount' => $type == 1 ? $get_amount : '-'.$pay_amount,//实际金额，带上type，有正负
                'bank_left_amount' => !empty($row[3]) ? $row[3] : 0,//银行账号余额
                'currency' => $bank_info['currency'],//币种
                'trade_desc' => $row[17],//描述
                'created_staff_id' => $staff_id,//创建人
                'created_staff_name' => $staff_name,//创建人名字
                'created_at' => $curr_time,//创建时间
                'updated_staff_id' => $staff_id,//更新人
                'updated_staff_name' => $staff_name,//更新人名字
                'updated_at' => $curr_time,//更新时间
                'extra' => json_encode($extra),//额外数据-excel文件原内容
            ];
        }
        unset($file_data);
        return $common_data;
    }

    /**
     * 生成手工导入的模板
     * @return mixed
     */
    public function getManualFlowTemplate()
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            $country_code = get_country_code();
            $header = [];
            if ($country_code == GlobalEnums::TH_COUNTRY_CODE){
                $header = [
                    self::$t->_('bank_flow_manual_field_account_type'),
                    self::$t->_('bank_flow_manual_field_time'),
                    self::$t->_('bank_flow_manual_field_opening_balance'),
                    self::$t->_('bank_flow_manual_field_withdrawal'),
                    self::$t->_('bank_flow_manual_field_deposit'),
                    self::$t->_('bank_flow_manual_field_balance'),
                    self::$t->_('bank_flow_manual_field_remark'),
                ];
            } elseif ($country_code == GlobalEnums::PH_COUNTRY_CODE){
                $header = [
                    self::$t->_('bank_flow_manual_field_ph_account_type'),
                    self::$t->_('bank_flow_manual_field_ph_time'),
                    self::$t->_('bank_flow_manual_field_ph_cheque'),
                    self::$t->_('bank_flow_manual_field_ph_opening_balance'),
                    self::$t->_('bank_flow_manual_field_ph_withdrawal'),
                    self::$t->_('bank_flow_manual_field_ph_deposit'),
                    self::$t->_('bank_flow_manual_field_ph_balance'),
                    self::$t->_('bank_flow_manual_field_ph_remark'),
                ];
            } elseif ($country_code == GlobalEnums::MY_COUNTRY_CODE) {
                $header = [
                    self::$t->_('bank_flow_manual_field_ph_account_type'),
                    self::$t->_('bank_flow_manual_field_ph_time'),
                    self::$t->_('bank_flow_manual_field_ph_cheque'),
                    self::$t->_('bank_flow_manual_field_ph_opening_balance'),
                    self::$t->_('bank_flow_manual_field_ph_withdrawal'),
                    self::$t->_('bank_flow_manual_field_ph_deposit'),
                    self::$t->_('bank_flow_manual_field_ph_balance'),
                    self::$t->_('bank_flow_manual_field_ph_remark'),
                ];
            }

            $tmp_file_name = 'manual_template_' . date('Ymd') . '.xlsx';
            $res = $this->exportExcel($header, [], $tmp_file_name);
            if (empty($res['data'])) {
                throw new ValidationException(self::$t->_("bank_flow_manual_template_gen_fail"), ErrCode::$VALIDATE_ERROR);
            } else {
                $data = $res['data'];
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        }  catch (\Exception $e) {
            $this->logger->error($e->getMessage());
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 删除/批量删除
     * @param array $data 请求参数组
     * @param array $user 当前登陆者信息
     * @param bool $is_batch false删除（勾选条数）、true批量删除
     * @return array
     */
    public function delete($data, $user, $is_batch = false)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';

        try {
            //是否有删除权限的管理员
            $can_delete_staff = EnumsService::getInstance()->getSettingEnvValueIds('sys_module_bank_flow_delete_staff_ids');
            if (!in_array($user['id'], $can_delete_staff)) {
                throw new ValidationException(self::$t->_('bank_account_delete_not_administrator'), ErrCode::$VALIDATE_ERROR);
            }
            if ($is_batch) {
                //批量删除
                $validate_params = [
                    'bank_account_id' => 'Required|IntGe:1|>>>:param error[bank_account_id]',
                    'trade_start_date' => 'Required|Date|>>>:param error[trade_start_date]',
                    'trade_end_date' => 'Required|Date|>>>:param error[trade_end_date]',
                    'updated_at_start_date' => 'Required|Date|>>>:param error[updated_at_start_date]',
                    'updated_at_end_date' => 'Required|Date|>>>:param error[updated_at_end_date]',
                ];
                Validation::validate($data, $validate_params);
                $conditions_array = ListService::getInstance()->getFlowConditions($data, BaseService::BANK_FLOW_TYPE_ALL);
                $flow_data = BankFlowModel::find([
                    'conditions' => $conditions_array[0],
                    'bind' => $conditions_array[1]
                ]);
                $bank_flow_data = $flow_data->toArray();
                if (empty($bank_flow_data)) {
                    throw new ValidationException(static::$t->_('bank_flow_delete_search_no_data'), ErrCode::$VALIDATE_ERROR);
                }

                //查询是否关联oa单号
                $bank_flow_ids = array_column($bank_flow_data, 'id');
                $child_bank_flow_ids = array_chunk($bank_flow_ids, 2000);
                $flow_oa_bank_flow_ids = [];
                foreach ($child_bank_flow_ids as $key => $child) {
                    $flow_oa_data = BankFlowOaRepository::getInstance()->getBankFlowOa($child);
                    if ($flow_oa_data) {
                        $flow_ids = array_unique(array_column($flow_oa_data, 'bank_flow_id'));
                        $flow_oa_bank_flow_ids = array_merge($flow_oa_bank_flow_ids, $flow_ids);
                    }
                }
                if (!empty($flow_oa_bank_flow_ids)) {
                    $flow_ids = implode(',', $flow_oa_bank_flow_ids);
                    throw new ValidationException(self::$t->_('bank_flow_oa_exist', ['flow_ids' => $flow_ids]), ErrCode::$VALIDATE_ERROR);
                }

                //执行删除
                if ($flow_data->delete() == false) {
                    throw new BusinessException('银行流水删除失败, 可能的原因是=' . get_data_object_error_msg($flow_data), ErrCode::$BUSINESS_ERROR);
                }
            } else {
                //删除
                Validation::validate($data, ['id_batch' => 'Required|Arr|ArrLenGe:1', 'id_batch[*]' => 'Required|IntGe:1']);

                //查询是否关联oa单号
                $flow_oa_data = BankFlowOaRepository::getInstance()->getBankFlowOa($data['id_batch']);
                if ($flow_oa_data) {
                    $flow_ids = array_unique(array_column($flow_oa_data, 'bank_flow_id'));
                    $flow_ids = implode(',', $flow_ids);
                    throw new ValidationException(self::$t->_('bank_flow_oa_exist', ['flow_ids' => $flow_ids]), ErrCode::$VALIDATE_ERROR);
                }
                //查询所有
                $flow_data = BankFlowRepository::getInstance()->getBankFlow($data['id_batch']);
                $this->logger->info('init_flow_delete 操作人=' . $user['id'] . '参数是=' . json_encode($data, JSON_UNESCAPED_UNICODE) . '; 本次要删除的数据是: ' . json_encode($flow_data->toArray(), JSON_UNESCAPED_UNICODE));
                //执行删除
                if ($flow_data->delete() == false) {
                    throw new BusinessException('银行流水删除失败, 可能的原因是=' . get_data_object_error_msg($flow_data), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if ($real_message) {
            $this->logger->warning('init_flow_delete_error:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];
    }

    /**
     * 流水上传-更新余额日期
     * @param array $data 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function update($data, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            //查询银行账号是否存在
            $bank_account = BankAccountRepository::getInstance()->getBankAccount(['id' => $data['id'], 'is_deleted' => GlobalEnums::IS_NO_DELETED]);
            if (!$bank_account) {
                throw new ValidationException(self::$t->_('bank_account_save_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            //只能选择比现有余额日期之后的日期
            if ($bank_account->left_amount_date >= $data['left_amount_date']) {
                throw new ValidationException(self::$t->_('bank_flow_update_left_amount_date_error'), ErrCode::$VALIDATE_ERROR);
            }

            $now = date('Y-m-d H:i:s');
            //更新账户信息
            $bank_account->left_amount_date = $data['left_amount_date'];
            $bank_account->remark = $data['remark'];
            $bank_account->updated_id = $user['id'];
            $bank_account->updated_at = $now;
            $bool = $bank_account->save();
            if ($bool === false) {
                throw new BusinessException('流水上传-更新余额日期-失败，变更信息组：' . json_encode($data, JSON_UNESCAPED_UNICODE) . ' 可能的原因是:' . get_data_object_error_msg($bank_account), ErrCode::$BUSINESS_ERROR);
            }

            //获取银行信息
            $bank_info = BankListRepository::getInstance()->getBankById($bank_account->bank_id);
            if (empty($bank_info)) {
                throw new ValidationException(self::$t->_('bank_account_bank_id_not_exist'), ErrCode::$VALIDATE_ERROR);
            }

            //插入付款流水
            $bank_flow_data = [
                'type' => BankFlowEnums::BANK_FLOW_TYPE_PAY_OUTLAY,
                'bank_id' => $bank_account->bank_id,
                'bank_name' => $bank_info['bank_name'],
                'bank_account_id' => $bank_account->id,
                'bank_account' => $bank_account->account,
                'date' => $data['left_amount_date'],
                'time' => '00:00:00',
                'ticket_no' => '',
                'get_amount' => 0.00,
                'pay_amount' => 0.00,
                'real_amount' => 0.00,
                'bank_left_amount' => $bank_account->left_amount,
                'currency' => $bank_account->currency,
                'trade_desc' => $data['remark'],
                'bank_flow_expense_id' => 0,
                'edit_status' => BankFlowEnums::BANK_FLOW_UNCONFIRMED_STATUS,//编辑状态，1待编辑
                'confirm_status' => BankFlowEnums::BANK_FLOW_UNCONFIRMED_STATUS,//确认状态，1待确认
                'updated_staff_id' => $user['id'],
                'updated_staff_name' => $user['name'],
                'created_staff_id' => $user['id'],
                'created_staff_name' => $user['name'],
                'created_at' => $now,
                'updated_at' => $now,
                'extra' => null
            ];
            $bank_flow = new BankFlowModel();
            $bool = $bank_flow->i_create($bank_flow_data);
            if ($bool === false) {
                throw new BusinessException('流水上传-更新余额日期-生成一条付款流水银行流水-失败，流水信息组：' . json_encode($bank_flow_data, JSON_UNESCAPED_UNICODE) . ' 可能的原因是:' . get_data_object_error_msg($bank_flow), ErrCode::$BUSINESS_ERROR);
            }

            $db->commit();
        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if ($message) {
            $db->rollback();
        }

        if ($real_message) {
            $this->logger->warning('init_flow_update_error:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => []
        ];

    }

}
