<?php

namespace App\Modules\BankFlow\Services;

use App\Library\Enums\BankFlowEnums;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Models\BankFlowEnumModel;
use App\Modules\BankFlow\Models\BankFlowExpenseModel;
use App\Modules\BankFlow\Models\BankFlowModel;
use App\Modules\BankFlow\Models\BankFlowMetaModel;
use App\Modules\BankFlow\Models\BankFlowPayDetailModel;
use App\Modules\BankFlow\Models\BankFlowGetDetailModel;

class DetailService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取流水管理-编辑时的参数验证
     * @return array
     * @date 2022/8/25
     */
    public function getValidateExpenseTypeEdit()
    {
        $validate_base = [
            'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
            'bank_flow_expense_id' => 'Required|IntGe:1|>>>:param error[bank_flow_expense_id]',
        ];
        $validate_th = [];
        if (get_country_code() == GlobalEnums::TH_COUNTRY_CODE) {
            $validate_th = [
                'from_bank_acct_id' => 'IfIntEq:bank_flow_expense_id,1|Required|IntGe:1|>>>:param error[from_bank_acct_id]',
                'to_bank_acct_id' => 'IfIntEq:bank_flow_expense_id,1|Required|IntGe:1|>>>:param error[to_bank_acct_id]',
                'refund_type' => 'IfIntEq:bank_flow_expense_id,32|Required|IntGe:1|>>>:param error[refund_type]',
                'refund_type_mark' => 'IfIntEq:bank_flow_expense_id,32|StrLenGeLe:0,100|>>>:param error[refund_type_mark]',
                'store_name' => 'IfIntEq:bank_flow_expense_id,39|Required|StrLenGeLe:1,100|>>>:param error[store_name]',
                'jp_type' => 'IfIntEq:bank_flow_expense_id,36|Required|IntGe:1|>>>:param error[jp_type]',
                'store_auto_type' => 'IfIntEq:bank_flow_expense_id,37|Required|IntGe:1|>>>:param error[store_auto_type]',
            ];
        }
        $validate_final = array_merge($validate_base, $validate_th);
        return $validate_final;
    }
    /**
     * @param int $flow_id
     * @param int $flow_type
     * @return mixed
     */
    public function getFlowDetail(int $flow_id, int $flow_type)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        try {
            // 不同流水类型的取值范围
            switch ($flow_type) {
                case parent::BANK_FLOW_TYPE_GET:
                    $type = 1;
                    break;
                case parent::BANK_FLOW_TYPE_PAY:
                    $type = 2;
                    break;
                default:
                    $type = 0;
            }

            $flow_info = BankFlowModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $flow_id],
                'columns' => [
                    'id AS flow_id',
                    'type',
                    'bank_name',
                    'bank_account',
                    'date',
                    'time',
                    'ticket_no',
                    'get_amount',
                    'pay_amount',
                    'bank_left_amount',
                    'currency',
                    'trade_desc',
                    'bank_flow_expense_id',
                ],
            ]);
            if (empty($flow_info)) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            if ($flow_info->type != $type) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            $flow_info = $flow_info->toArray();

            GlobalEnums::init();
            $currency_item = GlobalEnums::$currency_symbol_map;

            if (empty($flow_info['bank_flow_expense_id'])) {
                $flow_info['bank_flow_expense_id'] = '';
                $flow_info['bank_flow_expense_name'] = '';
            } else {
                $flow_info['bank_flow_expense_name'] = self::$t[BankFlowEnums::get_expense_type_prefix().$flow_info['bank_flow_expense_id']] ?? '';
            }

            $flow_info['currency_label'] = $currency_item[$flow_info['currency']] ?? '';

            // 获取扩展的字段
            $meta_item = BankFlowMetaModel::find([
                'conditions' => 'bank_flow_id = :bank_flow_id: and item not in ({items:array})',
                'bind' => ['bank_flow_id' => $flow_id,'items'=>BankFlowEnums::$bank_flow_meta_not_in],
                'columns' => ['item', 'val']
            ])->toArray();
            $meta_item = !empty($meta_item) ? array_column($meta_item, 'val', 'item') : [];

            if (empty($meta_item)) {
                // 获取该费用类型关联的扩展字段
                $bank_flow_expense_model = BankFlowExpenseModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind' => ['id' => $flow_info['bank_flow_expense_id']],
                    'columns' => ['relation_item']
                ]);

                if (!empty($bank_flow_expense_model->relation_item)) {
                    $relation_item = explode(',', $bank_flow_expense_model->relation_item);
                    foreach ($relation_item as $item) {
                        $meta_item[$item] = '';
                    }
                }
            }

            if (!empty($meta_item)) {
                // 获取自定义枚举名称
                $enums_setting = BankFlowEnumModel::find([
                    'conditions' => 'item IN ({items:array})',
                    'bind' => ['items' => array_keys($meta_item)],
                    'columns' => ['item', 'value_lang_key', 'value'],
                ])->toArray();

                // 获取银行账号
                $bank_acct_setting = BankAccountModel::find([
                    'columns' => ['id', 'account'],
                ])->toArray();
                $bank_acct_setting = !empty($bank_acct_setting) ? array_column($bank_acct_setting, 'account', 'id') : [];

                foreach ($meta_item as $meta_key => $meta_val) {
                    // 银行账号枚举
                    if (in_array($meta_key, ['from_bank_acct_id', 'to_bank_acct_id'])) {
                        $meta_item[$meta_key.'_label'] = $bank_acct_setting[$meta_val] ?? ' ';
                        continue;
                    }

                    // 普通枚举
                    $meta_item[$meta_key.'_label'] = '';

                    foreach ($enums_setting as $enum) {
                        if ($meta_key == $enum['item']) {
                            if ($meta_val == $enum['value']) {
                                $meta_item[$meta_key.'_label'] = self::$t[$enum['value_lang_key']] ?? '';
                            }
                        }
                    }
                }
            }

            unset($flow_info['type']);
            $data = array_merge($flow_info, $meta_item);

        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 详情获取异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

    /**
     * 编辑流水的费用类型相关字段
     * @param array $params
     * @param array $user_info
     * @param int $flow_type
     * @return mixed
     */
    public function saveFlowExpenseInfo(array $params, int $flow_type, array $user_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];

        $db = $this->getDI()->get('db_oa');
        $db->begin();

        try {
            // 不同流水类型的取值范围
            $type = 0;
            $flow_scenes = '';
            switch ($flow_type) {
                case parent::BANK_FLOW_TYPE_GET:
                    $type = 1;
                    $flow_scenes = 'get_edit';
                    break;
                case parent::BANK_FLOW_TYPE_PAY:
                    $type = 2;
                    $flow_scenes = 'pay_edit';
                    break;
            }

            // 验证费用类型
            $expense_type_item = InitFlowService::getInstance()->getExpenseTypeList($flow_scenes);
            $expense_type_item = !empty($expense_type_item) ? array_column($expense_type_item, 'value') : [];
            if (!in_array($params['bank_flow_expense_id'], $expense_type_item)) {
                throw new ValidationException(self::$t->_("bank_flow.expense_type_error"));
            }

            $flow_model = BankFlowModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $params['flow_id']],
            ]);
            if (empty($flow_model)) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            if ($flow_model->type != $type) {
                throw new ValidationException(self::$t->_("bank_flow.info_null"));
            }

            $bank_flow_expense_id = $flow_model->bank_flow_expense_id;
            // 更新流水表
            $flow_model->bank_flow_expense_id = $params['bank_flow_expense_id'];
            $flow_model->edit_status = 2;
            $flow_model->updated_staff_id = $user_info['id'] ?? 0;
            $flow_model->updated_staff_name = $user_info['name'] ?? '';
            $flow_model->updated_at = date('Y-m-d H:i:s');
            if ($flow_model->save() === false) {
                throw new BusinessException('银行流水费用类型编辑保存失败', ErrCode::$BANK_FLOW_EXPENSE_TYPE_SAVE_ERROR );
            }

            // 删除旧的费用类型扩展字段
            $exist_expand_item_model = BankFlowMetaModel::find([
                'conditions' => 'bank_flow_id = :bank_flow_id: and item not in ({items:array})',
                'bind' => ['bank_flow_id' => $params['flow_id'],'items'=>BankFlowEnums::$bank_flow_meta_not_in]
            ]);
            if (!empty($exist_expand_item_model)) {
                foreach ($exist_expand_item_model as $item) {
                    if ($item->delete() === false) {
                        throw new BusinessException('银行流水费用类型编辑保存, 已有费用类型扩展字段删除失败', ErrCode::$BANK_FLOW_EXPENSE_TYPE_SAVE_EXIST_EXPAND_DEL_ERROR );
                    }
                }
            }

            // 提取指定费用类型的扩展字段
            $flow_expense_type = BankFlowExpenseModel::findFirst($params['bank_flow_expense_id']);
            if (!empty($flow_expense_type->relation_item)) {
                $relation_item = explode(',', $flow_expense_type->relation_item);

                // 写入新的费用类型扩展字段
                $add_expand_data = [];
                foreach ($relation_item as $field) {
                    $add_expand_data[] = [
                        'bank_flow_id' => $params['flow_id'],
                        'item' => $field,
                        'val' => $params[$field] ?? '',
                        'created_id' => $user_info['id'] ?? 0,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                }

                $bank_flow_meta_model = new BankFlowMetaModel();
                if ($bank_flow_meta_model->batch_insert($add_expand_data) === false) {
                    throw new BusinessException('银行流水费用类型编辑保存, 费用类型扩展字段批量添加失败', ErrCode::$BANK_FLOW_EXPENSE_TYPE_SAVE_EXPAND_INSERT_ERROR );
                }
            }

            //费用类型不满足当前流水模板删除
            $expense_type_arr = ListService::getInstance()->expense_template_relation();


            if (isset($expense_type_arr[$bank_flow_expense_id]) && isset($expense_type_arr[$params['bank_flow_expense_id']]) && $expense_type_arr[$bank_flow_expense_id] != $expense_type_arr[$params['bank_flow_expense_id']]) {

                //查询原有流水
                switch ($expense_type_arr[$bank_flow_expense_id]) {
                    case 1:
                        $flow_detail = BankFlowPayDetailModel::find(
                            [
                                'conditions' => 'bank_flow_id = ?0',
                                'bind'       => [$params['flow_id']]
                            ]);
                        break;
                    case 2:
                        $flow_detail = BankFlowGetDetailModel::find(
                            [
                                'conditions' => 'bank_flow_id = ?0',
                                'bind'       => [$params['flow_id']]
                            ]);


                        break;
                    default:

                        break;
                }
                if (!empty($flow_detail)) {
                    $flow_detail->delete();
                }
            }
            

            $db->commit();
        } catch (ValidationException $e) {
            $code = ErrCode::$VALIDATE_ERROR;
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->getDI()->get('logger')->warning('银行流水 - 费用类型编辑异常: ' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data
        ];
    }

}
