<?php

namespace App\Modules\BankFlow\Services;

use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\EnumsService;

class BaseService extends \App\Library\BaseService
{
    // 银行流水类型
    const BANK_FLOW_TYPE_ALL = 1;//全部
    const BANK_FLOW_TYPE_GET = 2;//收款
    const BANK_FLOW_TYPE_PAY = 3;//付款
    // 银行流水导出类型
    const EXPORT_BANK_FLOW_TYPE_ALL        = 1;//全部
    const EXPORT_BANK_FLOW_TYPE_PAY_REMARK = 2;//导出付款明细备注
    const EXPORT_BANK_FLOW_TYPE_GET_REMARK = 3;//导出收款明细备注

    const EXPORT_BANK_FLOW_TYPE_PAY_ORDER  = 4;//付款导出系统单号


    const BANK_PAY_FLOW_TYPE = 2;//流水支出2
    const BANK_GET_FLOW_TYPE = 1;//流水收入1
    const BANK_PAY_FLOW_TEMPLATE = 1;//模版1
    const BANK_GET_FLOW_TEMPLATE = 2;//模版2

    // 非必须参数
    public static $not_must_params = [
        '_url',
        'PHPSESSID'
    ];
    public static $bank_flow_list_params = [
        'bank_id' => 'IntGe:0|>>>:param error[bank_id]',
        'bank_account_id' => 'IntGe:0|>>>:param error[bank_account_id]',
        'trade_start_date' => 'Required|Date|>>>:param error[trade_start_date]',
        'trade_end_date' => 'Required|Date|>>>:param error[trade_end_date]',
        'ticket_no' => 'StrLenGe:0|>>>:param error[ticket_no]',
        'trade_desc' => 'StrLenGe:0|>>>:param error[trade_desc]',
        'bank_flow_expense_id' => 'IntGe:0|>>>:param error[bank_flow_expense_id]',
        'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
    ];

    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 过滤空值/null值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams(array $params, array $not_must)
    {
        $params = array_filter($params, function($v, $k) {
            return $v !== '' && $v !== null;
        }, ARRAY_FILTER_USE_BOTH);

        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }

        foreach ($not_must as $value) {
            if (isset($params[$value])) {
                unset($params[$value]);
            }
        }

        return $params;
    }

    /**
     * 数据权限验证
     *
     * @param array $user
     * @return bool
     * @throws ValidationException
     */
    public function checkStaffDataPermissions(array $user)
    {
        $country_code = get_country_code();
        if (!in_array($country_code, [GlobalEnums::TH_COUNTRY_CODE, GlobalEnums::PH_COUNTRY_CODE, GlobalEnums::MY_COUNTRY_CODE])) {
            return true;
        }

        $staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('bank_statement_management_data_permissions_staffs');
        if (!in_array($user['id'], $staff_ids)) {
            throw new ValidationException(static::$t->_('no_data_permission_error'), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }
}
