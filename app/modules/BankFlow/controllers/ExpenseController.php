<?php

namespace App\Modules\BankFlow\Controllers;


use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Services\PayFlowService;
use App\Modules\Reimbursement\Services\BaseService;

class ExpenseController extends BaseController
{


    /**
     * 借款 - 列表
     * @Permission(action='bank_flow.day_expense.loan.list')
     */
    public function getLoanListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN, $params);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 借款 - 导出
     * @Permission(action='bank_flow.day_expense.loan.export')
     */
    public function exportLoanListAction()
    {
        $params = $this->getExportParams();

        $lock_key = md5('day_expense.loan.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_LOAN, $params);
        }, $lock_key, 30);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 获得列表参数
     * @return array
     * @throws ValidationException
     */
    private function getListParams()
    {
        $params = $this->request->get();
        $validate = [
            'start_date' => 'Required|Date',
            'end_date' => 'Required|Date',
            'bank_account_id' => 'Str',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
            'pageSize' => 'Int',
            'pageNum' => 'Int'
        ];
        Validation::validate($params, $validate);
        return array_only($params, array_keys($validate));
    }


    /**
     * 获得导出参数
     * @return array
     * @throws ValidationException
     */
    private function getExportParams()
    {
        $params = $this->request->get();
        $validate = [
            'start_date' => 'Required|Date',
            'end_date' => 'Required|Date',
            'bank_account_id' => 'Str',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
        ];
        Validation::validate($params, $validate);
        return array_only($params, array_keys($validate));
    }


    /**
     * 获得可抵扣确认参数
     * @return array
     * @throws ValidationException
     */
    private function getConfirmParams()
    {
        $params = $this->request->get();
        $validate = [
            'ids' => 'Required|Arr|ArrLenGe:1',
            'ids[*]' => 'Required|IntGe:0',
            'is_deduct' => 'Required|IntIn:0,1',
        ];
        Validation::validate($params, $validate);
        return array_only($params, array_keys($validate));
    }


    /**
     * 备用金 - 列表
     * @Permission(action='bank_flow.day_expense.reserve.list')
     */
    public function getReserveListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND,
            $params
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 备用金 - 导出
     * @Permission(action='bank_flow.day_expense.reserve.export')
     */
    public function exportReserveListAction()
    {
        $params = $this->getExportParams();

        $lock_key = md5('day_expense.reserve.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_RESERVE_FUND, $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 报销 - 列表
     * @Permission(action='bank_flow.day_expense.reimbursement.list')
     */
    public function getReimbursementListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
            $params
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 报销 - 导出
     * @Permission(action='bank_flow.day_expense.reimbursement.export')
     */
    public function exportReimbursementListAction()
    {
        $params = $this->getExportParams();
        $lock_key = md5('day_expense.reimbursement.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT, $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 报销 - 可抵扣确认
     * @Permission(action='bank_flow.day_expense.reimbursement.confirm')
     */
    public function confirmReimbursementAction()
    {
        $params = $this->getConfirmParams();
        $res = PayFlowService::getInstance()->confirmExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_REIMBURSEMENT,
            $params,
            $this->user
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购付款申请单 - 列表
     * @Permission(action='bank_flow.day_expense.purchase.list')
     */
    public function getPurchaseListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
            $params
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购付款申请单 - 导出
     * @Permission(action='bank_flow.day_expense.purchase.export')
     */
    public function exportPurchaseListAction()
    {
        $params = $this->getExportParams();
        $lock_key = md5('day_expense.purchase.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT, $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 采购付款申请单 - 可抵扣确认
     * @Permission(action='bank_flow.day_expense.purchase.confirm')
     */
    public function confirmPurchaseAction()
    {
        $params = $this->getConfirmParams();
        $res = PayFlowService::getInstance()->confirmExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PURCHASE_PAYMENT,
            $params,
            $this->user
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     *  网点租房付款- 列表
     * @Permission(action='bank_flow.day_expense.payment_store_renting.list')
     */
    public function getPaymentStoreRentingListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING,
            $params
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 网点租房付款 - 导出
     * @Permission(action='bank_flow.day_expense.payment_store_renting.export')
     */
    public function exportPaymentStoreRentingListAction()
    {
        $params = $this->getExportParams();
        $lock_key = md5('day_expense.payment_store_renting.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING, $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 网点租房付款 - 可抵扣确认
     * @Permission(action='bank_flow.day_expense.payment_store_renting.confirm')
     */
    public function confirmPaymentStoreRentingAction()
    {
        $params = $this->getConfirmParams();
        $res = PayFlowService::getInstance()->confirmExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PAYMENT_STORE_RENTING,
            $params,
            $this->user
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     *  网点租房付款- 列表
     * @Permission(action='bank_flow.day_expense.ordinary_payment.list')
     */
    public function getOrdinaryPaymentListAction()
    {
        $params = $this->getListParams();
        $res = PayFlowService::getInstance()->getExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
            $params
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 网点租房付款 - 导出
     * @Permission(action='bank_flow.day_expense.ordinary_payment.export')
     */
    public function exportOrdinaryPaymentListAction()
    {
        $params = $this->getExportParams();
        $lock_key = md5('day_expense.ordinary_payment.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseList(Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT, $params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * 网点租房付款 - 可抵扣确认
     * @Permission(action='bank_flow.day_expense.ordinary_payment.confirm')
     */
    public function confirmOrdinaryPaymentAction()
    {
        $params = $this->getConfirmParams();
        $res = PayFlowService::getInstance()->confirmExpenseList(
            Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_ORDINARY_PAYMENT,
            $params,
            $this->user
        );
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     *  费用日报-列表
     * @Permission(action='bank_flow.day_expense.day.list')
     */
    public function getDayListAction()
    {
        $params = $this->request->get();
        $validate = [
            'date' => 'Required|Date',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
        ];
        Validation::validate($params, $validate);
        $res = PayFlowService::getInstance()->getExpenseListByDate($params['date'], $params['date'],false,$params['company_name']);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     *  费用月报-列表
     * @Permission(action='bank_flow.day_expense.month.list')
     * @throws ValidationException
     */
    public function getMonthListAction()
    {
        $params = $this->request->get();
        $validate = [
            'month' => 'Required|Regexp:/^\d{4}-((0([1-9]))|(1(0|1|2)))$/',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
        ];
        Validation::validate($params, $validate);
        $month = $params['month'];
        $start_date = $month . "-01";
        $end_date = date("Y-m-t", strtotime($start_date));
        $res = PayFlowService::getInstance()->getExpenseListByDate($start_date, $end_date, false,
            $params['company_name'] ?? '');
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     *  费用日报-导出
     * @Permission(action='bank_flow.day_expense.day.export')
     */
    public function exportDayListAction()
    {
        $params = $this->request->get();
        $validate = [
            'date' => 'Required|Date',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
        ];
        Validation::validate($params, $validate);
        $lock_key = md5('day_expense.day.export'.$this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportExpenseListByDate($params['date'], $params['date'],$params['company_name']);
        }, $lock_key, 30);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     *  费用月报-列表
     * @Permission(action='bank_flow.day_expense.month.export')
     * @throws ValidationException
     */
    public function exportMonthListAction()
    {
        $params = $this->request->get();
        $validate = [
            'month' => 'Required|Regexp:/^\d{4}-((0([1-9]))|(1(0|1|2)))$/',
            'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
        ];

        Validation::validate($params, $validate);
        $month = $params['month'];
        $start_date = $month . "-01";
        $end_date = date("Y-m-t", strtotime($start_date));
        $lock_key = md5('day_expense.month.export' . $this->user['id']);
        $res = $this->atomicLock(function() use ($start_date, $end_date, $params) {
            return PayFlowService::getInstance()->exportExpenseListByDate($start_date, $end_date, $params['company_name'] ?? '');
        }, $lock_key, 30);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 费用汇总 - 导出
     * @Permission(action='bank_flow.day_expense.summary.export')
     *
     * @throws ValidationException
     */
    public function exportAllListAction()
    {
        $params = $this->getExportParams();
        $lock_key = md5('day_expense.all.export'.$this->user['id']);

        $res = $this->atomicLock(function() use ($params){
            return PayFlowService::getInstance()->exportAllExpenseList($params);
        }, $lock_key, 30);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

}