<?php
namespace App\Modules\BankFlow\Controllers;

use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Enums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Services\BaseService;
use App\Modules\BankFlow\Services\DetailService;
use App\Modules\BankFlow\Services\GetFlowService;
use App\Modules\BankFlow\Services\ListService;
use App\Modules\BankFlow\Services\PayFlowService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\ImportCenterService;
use App\Modules\Common\Services\StoreService;
use App\Util\RedisKey;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class GetFlowController extends BaseController
{

    /**
     * 收款流水 - 列表
     * @Permission(action='bank_flow.manage.get.list')
     */
    public function getListAction()
    {
        // 数据权限校验
        ListService::getInstance()->checkStaffDataPermissions($this->user);

        // 参数校验
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $data = ListService::getInstance()->getFlowList($params, BaseService::BANK_FLOW_TYPE_GET);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 银行流水管理-付款-明细备注-获得列表
     * @Permission(action='bank_flow.manage.get.view')
     */
    public function getDetailRemarkListAction(){
        $bank_flow_id = $this->request->get('bank_flow_id');
        $pageNum =  intval($this->request->get('pageNum'));
        $pageSize = intval($this->request->get('pageSize'));
        if($pageNum<1){
            $pageNum = 1;
        }
        if($pageSize<1){
            $pageSize = 1;
        }
        $res = GetFlowService::getInstance()->getDetailRemarkList($bank_flow_id,$pageNum,$pageSize);
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }


    /**
     * @Token
     */
    public function getDetailRemarkTemplateAction(){
        $res = GetFlowService::getInstance()->getDetailRemarkTpl();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }



    /**
     * 银行流水管理-收款-明细备注-上传
     * @Permission(action='bank_flow.manage.get.edit_detail')
     */
    public function uploadDetailRemarkAction(){
        $excelData = [];
        try {
            $bank_flow_id = $this->request->get("bank_flow_id");

            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_("bank_flow_not_found_file"));
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_("bank_flow_not_found_file"));
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取测试文件
            $excelData = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType([
                    1 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,// 交易日期
                ])
                ->setSkipRows(1)
                ->getSheetData();
            if (empty($excelData) || empty($excelData[0])) {
                throw new ValidationException($this->t->_('bank_flow_data_empty'));
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }


        $lock_key = md5('bank_flow_get_upload_detail_remark' . $bank_flow_id . '_' . $this->user['id']);
        $res = $this->atomicLock(function() use ($excelData,$bank_flow_id){
            return GetFlowService::getInstance()->importDetailRemark($bank_flow_id,$excelData, $this->user);
        }, $lock_key);

        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }
        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 收款流水 - 导出
     * @Permission(action='bank_flow.manage.get.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/27654
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportFlowAction()
    {
        // 数据权限校验
        ListService::getInstance()->checkStaffDataPermissions($this->user);

        // 参数校验
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $lock_key = md5(RedisKey::GET_FLOW_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function() use ($params){
            $data_count = ListService::getInstance()->getFlowCount($params, BaseService::BANK_FLOW_TYPE_GET);
            if ($data_count > BankFlowEnums::BANK_FLOW_ASYNC_MAX_COUNT) {
                //大于这个数量, 异步也不导出
                throw new ValidationException($this->t->_('bank_pay_flow_exceeds_the_limit'), ErrCode::$VALIDATE_ERROR);
            }
            if ($data_count > BankFlowEnums::BANK_FLOW_SYNC_MAX_COUNT) {
                // 大于指定数量, 添加异步任务 导出
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::DOWNLOAD_CENTER_BANK_FLOW_GAT_EXPORT, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                // 小于等于指定数量, 同步导出
                $params['pageSize'] = BankFlowEnums::BANK_FLOW_SYNC_MAX_COUNT;
                $params['pageNum'] = 1;
                $result = ListService::getInstance()->exportFlow($params, BaseService::BANK_FLOW_TYPE_GET);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($result['data']['file_url']) ? $result['data']['file_url'] : ''
                ];
            }
            return $result;
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }



    /**
     * 银行流水管理-收款-批量费用类型-上传
     * @Permission(action='bank_flow.manage.get.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/27662
     */
    public function uploadExpenseAction(){
        $excelData = [];
        try {
            ini_set('memory_limit', '1024M');

            // 文件类型: 必填
            // 1 - 未关联系统单号
            // 2 - 关联系统单号
            $params = $this->request->get();
            if (!isset($params['file_type']) || !in_array($params['file_type'], BankFlowEnums::$flow_upload_file_type_item)) {
                throw new ValidationException($this->t->_("bank_flow_file_type_error"));
            }

            // 收款流水 不可 关联系统单号
            if ($params['file_type'] == BankFlowEnums::FLOW_UPLOAD_FILE_TYPE_ASSOCIATED_ORDER) {
                throw new ValidationException($this->t->_('bank_flow_expense_batch_update_error_01'), ErrCode::$VALIDATE_ERROR);
            }

            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_("bank_flow_not_found_file"));
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_("bank_flow_not_found_file"));
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            // 读取测试文件
            $excelData = $excel->openFile($file->getTempName())
                ->openSheet()
                ->setType(ImportCenterEnums::$task_excel_columns_type_config[ImportCenterEnums::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE])
                ->setSkipRows(1)
                ->getSheetData();
            if (empty($excelData) || empty($excelData[0])) {
                throw new ValidationException($this->t->_('bank_flow_data_empty'));
            }
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $params['excel_data'] = $excelData;
        $lock_key = md5('bank_flow_get_import_expense_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params, $file) {
            return ListService::getInstance()->importFlowExpense($params, BankFlowEnums::BANK_FLOW_TYPE_GET_INCOME, $this->user, 'api', $file);
        }, $lock_key, 20);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 收款流水 - 详情
     * @Permission(action='bank_flow.manage.get.view')
     */
    public function getDetailAction()
    {
        try {
            // 参数校验
            $flow_id = $this->request->get('flow_id');
            $params = [
                'flow_id' => $flow_id
            ];

            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = DetailService::getInstance()->getFlowDetail($flow_id, BaseService::BANK_FLOW_TYPE_GET);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 收款流水 - 费用信息编辑
     * @Permission(action='bank_flow.manage.get.edit')
     */
    public function saveExpenseInfoAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $this->logger->info('银行收款流水 - 费用类型编辑 - 请求参数:' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $params = BaseService::handleParams($params, BaseService::$not_must_params);
            $params = array_filter($params);
            $validate = DetailService::getInstance()->getValidateExpenseTypeEdit();
            Validation::validate($params, $validate);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $res = DetailService::getInstance()->saveFlowExpenseInfo($params, BaseService::BANK_FLOW_TYPE_GET, $this->user);

        $this->logger->info('银行收款流水 - 费用类型编辑 - 返回参数:' . json_encode($res, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 网点名称搜索
     * @Permission(action='bank_flow.manage.get.edit')
     */
    public function storeSearchAction()
    {
        try {
            // 参数校验
            $store_name = $this->request->get('store_name');
            $params = ['store_name' => $store_name];

            $validate_params = [
                'store_name' => 'Required|StrLenGe:1|>>>:param error[store_name]',
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = (new StoreService())->getSysStoreListByCondition($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 导出明细备注
     * @Token
     * */
    public function exportRemarkListAction()
    {
        // 数据权限校验
        BaseService::getInstance()->checkStaffDataPermissions($this->user);

        ini_set('memory_limit', '1024M');

        // 参数校验
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $params['flow_type'] = BaseService::BANK_GET_FLOW_TYPE;

        $lock_key = md5(RedisKey::GET_FLOW_EXPORT_REMARK_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return PayFlowService::getInstance()->exportRemarkList($params);
        }, $lock_key, 30);

        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行流水 - 流水管理 - 收款流水 - 异步上传费用类型结果查询
     * @Permission(action='bank_flow.manage.get.edit')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88139
     * @return mixed
     */
    public function asyncUploadExpenseResultAction()
    {
        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_BANK_FLOW_GET_FLOW_UPLOAD_EXPENSE, $this->user['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


}