<?php

namespace App\Modules\BankFlow\Controllers;

use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\BankFlowEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Modules\BankFlow\Services\BaseService;
use App\Modules\BankFlow\Services\ListService;
use App\Modules\BankFlow\Services\InitFlowService;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\ImportCenterService;
use App\Repository\oa\BankAccountRepository;
use App\Util\RedisKey;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class InitFlowController extends BaseController
{
    /**
     * 银行和账号列表
     *
     * @Token
     */
    public function getBankAcctListAction()
    {
        try {
            $data = InitFlowService::getInstance()->getBankAndAcctList();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $e) {
            $this->logger->error('银行流水 - 获取银行和账号列表异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 费用类型列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/27378
     * @return Response|ResponseInterface
     *
     */
    public function getExpenseTypeListAction()
    {
        try {
            $data_scenes = $this->request->get('data_scenes');

            $data = InitFlowService::getInstance()->getExpenseTypeList($data_scenes ?? 'all_list');
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $e) {
            $this->logger->error('银行流水 - 获取费用类型异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 银行账号所有公司去重列表
     *
     * @Token
     */
    public function getCompanyListAction()
    {
        try {
            $data = InitFlowService::getInstance()->getBankCompany();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $e) {
            $this->logger->error('银行流水 - 获取银行账号公司列表异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 枚举列表
     *
     * @Token
     */
    public function getEnumListAction()
    {
        try {
            $data = InitFlowService::getInstance()->getEnums();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $e) {
            $this->logger->error('银行流水 - 获取枚举异常: ' . $e->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $this->t['retry_later']);
        }
    }

    /**
     * 列表
     * @Permission(action='bank_flow.upload.list')
     *
     */
    public function getListAction()
    {
        // 数据权限校验
        ListService::getInstance()->checkStaffDataPermissions($this->user);

        // 参数校验
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $data = ListService::getInstance()->getFlowList($params, BaseService::BANK_FLOW_TYPE_ALL);

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 流水上传
     * @Permission(action='bank_flow.upload.import')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/27574
     * @return Response|ResponseInterface
     * @throws Exception
     */
    public function uploadAction()
    {
        try {
            ini_set('memory_limit', '1024M');

            // 参数校验
            $params = $this->request->get();
            $this->logger->info('银行流水上传 - 请求参数:' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $params = BaseService::handleParams($params, BaseService::$not_must_params);

            $validate_params = [
                'bank_id' => 'Required|IntGe:1|>>>:param error[bank_id]',
                'bank_account_id' => 'Required|IntGe:1|>>>:param error[bank_account_id]',
            ];

            Validation::validate($params, $validate_params);

            // 泰国BAY银行,不是flash pay的账号使用公共模板
            $bank_account_info = BankAccountModel::findFirst($params['bank_account_id']);
            if (!$bank_account_info) {
                throw new ValidationException($this->t->_('bank_account_id_find_error'), ErrCode::$VALIDATE_ERROR);
            }
            $bank_account_name = $bank_account_info->company_name;
            $th_bay_not_flash_pay_condition = get_country_code() == GlobalEnums::TH_COUNTRY_CODE && $params['bank_id'] == BankFlowEnums::BANK_CODE_BAY && $bank_account_name != BankFlowEnums::BANK_ACCOUNT_NAME_PAY;
            // 获取银行的Excel列属性设置,获取各个模板的读取起始行
            $header_index = 0;
            if (in_array($params['bank_id'], BankFlowEnums::get_common_bank_id()) || $th_bay_not_flash_pay_condition) {
                $common_template_id = BankFlowEnums::get_common_template_id();
                $columns_type = BankFlowEnums::get_flow_file_columns_type()[$common_template_id] ?? [];
                $header_index = BankFlowEnums::get_data_begin_index()[$common_template_id] ?? 0;
            } else {
                $columns_type = BankFlowEnums::get_flow_file_columns_type()[$params['bank_id']] ?? [];
                $header_index = BankFlowEnums::get_data_begin_index()[$params['bank_id']] ?? 0;
            }

            if (empty($columns_type)) {
                throw new ValidationException($this->t->_("bank_flow_bank_name_error"), ErrCode::$VALIDATE_ERROR);
            }

            // 文件格式校验
            if (!$this->request->hasFiles()) {
                throw new ValidationException($this->t->_('bank_flow_not_found_file'), ErrCode::$VALIDATE_ERROR);
            }

            $file = $this->request->getUploadedFiles()[0];
            $extension = $file->getExtension();
            if (!in_array($extension, ['xlsx'])) {
                throw new ValidationException($this->t->_('bank_flow_upload_file_type_error'), ErrCode::$VALIDATE_ERROR);
            }

            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);

            // 读取上传文件数据
            try {
                $excel_data = $excel->openFile($file->getTempName())
                    ->openSheet()
                    ->setType($columns_type)
//                ->setSkipRows()
                    ->getSheetData();
            } catch (Exception $e) {
                throw new ValidationException($this->t->_('read_file_content_error'), ErrCode::$VALIDATE_ERROR);
            }

            // 读取文件的起始位置 默认0行是表头 1行是数据
            $header_index_next = $header_index + 1;
            // 读取每行单元格数据时，可指定每个单元格数据类型进行读取
            if (empty($excel_data) || empty($excel_data[$header_index]) || empty($excel_data[$header_index_next])) {
                throw new ValidationException($this->t->_('bank_flow_upload_file_illegal_format'), ErrCode::$VALIDATE_ERROR);
            }

            // 删除起始行之前的数据
            array_splice($excel_data, 0, $header_index);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        // 导入: 1. 文件格式校验; 2. 文件数据提取 3. 金额校验
        $lock_key = md5('bank_flow_upload_' . $params['bank_account_id']);
        $res = $this->atomicLock(function () use ($params, $excel_data, $file) {
            return InitFlowService::getInstance()->flowUpload($params, $excel_data, $this->user, 'api', $file);
        }, $lock_key, 30);

        $this->logger->info('银行流水上传 - 返回参数:' . json_encode($res, JSON_UNESCAPED_UNICODE));
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 流水附件 - 列表
     * @Permission(action='bank_flow.upload.view_attachment')
     */
    public function getAttachmentListAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $params = BaseService::handleParams($params, BaseService::$not_must_params);

            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = InitFlowService::getInstance()->getFlowAttachmentList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 流水附件 - 上传
     * @Permission(action='bank_flow.upload.edit_attachment')
     */
    public function attachmentUploadAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $this->logger->info('银行流水上传 - 附件添加 - 请求参数:' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $params = BaseService::handleParams($params, BaseService::$not_must_params);
            $params['attachment_arr'] = $params['attachment_arr'] ?? [];

            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
                'attachment_arr' => 'Required|ArrLenGeLe:0,1000',
                'attachment_arr[*].file_name' => "StrLenGeLe:2,300",
                'attachment_arr[*].bucket_name' => "StrLenGeLe:2,300",
                'attachment_arr[*].object_key' => "StrLenGeLe:2,300",
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = InitFlowService::getInstance()->uploadAttachments($params, $this->user);

        $this->logger->info('银行流水上传 - 附件添加 - 返回参数:' . json_encode($data, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 流水附件 - 删除
     * @Permission(action='bank_flow.upload.edit_attachment')
     */
    public function attachmentRemoveAction()
    {
        try {
            // 参数校验
            $params = $this->request->get();
            $this->logger->info('银行流水上传 - 附件删除 - 请求参数:' . json_encode($params, JSON_UNESCAPED_UNICODE));

            $params = BaseService::handleParams($params, BaseService::$not_must_params);

            $validate_params = [
                'flow_id' => 'Required|IntGe:1|>>>:param error[flow_id]',
                'attachment_id' => 'IntGe:0|>>>:param error[attachment_id]',
            ];

            Validation::validate($params, $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = InitFlowService::getInstance()->removeAttachment($params, $this->user);

        $this->logger->info('银行流水上传 - 附件删除 - 返回参数:' . json_encode($data, JSON_UNESCAPED_UNICODE));

        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 导出流水
     *
     * @Token
     *
     */
    public function exportListAction()
    {
        // 参数校验
        ini_set('memory_limit', '1024M');
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $lock_key = md5('pay_flow_export_list_' . $this->user['id']);

        $res = $this->atomicLock(function () use ($params) {
            return ListService::getInstance()->getFlowList($params, BaseService::BANK_FLOW_TYPE_ALL, false, true);
        }, $lock_key, 30);

        return $this->returnJson($res['code'], '', $res['data']);
    }


    /**
     * 批量上传模板下载(手工确定的模板)
     * @Permission(action='bank_flow.upload.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/45961
     * @return Response|ResponseInterface
     */
    public function downloadManualTemplateAction()
    {
        $res = InitFlowService::getInstance()->getManualFlowTemplate();
        if ($res['code'] == ErrCode::$SUCCESS) {
            return $this->returnJson($res['code'], $res['message'], $res['data']);
        }

        return $this->returnJson($res['code'], $res['message']);
    }

    /**
     * 下载 (权限跟随列表)
     * @Permission(action='bank_flow.upload.list')
     */
    public function downloadListAction()
    {
        // 数据权限校验
        ListService::getInstance()->checkStaffDataPermissions($this->user);

        // 参数校验
        $params = $this->request->get();
        $params = BaseService::handleParams($params, BaseService::$not_must_params);
        Validation::validate($params, BaseService::$bank_flow_list_params);

        $lock_key = md5(RedisKey::INIT_FLOW_EXPORT_LOCK . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            $data_count = ListService::getInstance()->getFlowCount($params, BaseService::BANK_FLOW_TYPE_ALL);
            if ($data_count > BankFlowEnums::BANK_FLOW_ASYNC_MAX_COUNT) {
                //大于这个数量, 异步也不导出
                throw new ValidationException($this->t->_('bank_pay_flow_exceeds_the_limit'), ErrCode::$VALIDATE_ERROR);
            }
            if ($data_count > BankFlowEnums::BANK_FLOW_SYNC_MAX_COUNT) {
                // 大于指定数量, 添加异步任务 导出
                $result = DownloadCenterService::getInstance()->addDownloadCenter($this->user['id'], DownloadCenterEnum::DOWNLOAD_CENTER_BANK_FLOW_INIT_DOWN, $params);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                    'file_url' => ''
                ];
            } else {
                // 小于等于指定数量, 同步导出
                $params['pageSize'] = BankFlowEnums::BANK_FLOW_SYNC_MAX_COUNT;
                $params['pageNum'] = 1;
                $result = ListService::getInstance()->getFlowList($params, BaseService::BANK_FLOW_TYPE_ALL, false, true);
                // 生成文件
                $export_data = ListService::getInstance()->handleFlowListHeader($result['data']['items'] ?? []);
                $result['data'] = [
                    'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                    'file_url' => is_string($export_data['data']) ? $export_data['data'] : ''
                ];
            }
            return $result;
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 流水上传-删除
     * @Permission(action='bank_flow.upload.delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75672
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        $res = InitFlowService::getInstance()->delete($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 银行流水 - 异步上传 - 结果查询(最近一次)
     * @Permission(action='bank_flow.upload.import')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88133
     * @return mixed
     * @throws ValidationException
     */
    public function asyncUploadResultAction()
    {
        $params = $this->request->get();
        $validate_params = [
            'account' => 'Required|StrLenGe:1|>>>:' . $this->t->_('params_error', ['param' => 'account']),
        ];
        Validation::validate($params, $validate_params);

        $res = ImportCenterService::getInstance()->getBarcodeImportResult(ImportCenterEnums::TYPE_BANK_FLOW_INIT_UPLOAD, 0, $params['account']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 
     * 流水上传-批量删除删除
     * @Permission(action='bank_flow.upload.batch_delete')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88283
     */
    public function batchDeleteAction()
    {
        $params = $this->request->get();
        $res = InitFlowService::getInstance()->delete($params, $this->user, true);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 流水上传-更新余额日期
     * @Permission(action='bank_flow.upload.update')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87959
     */
    public function updateAction()
    {
        $params = $this->request->get();
        $validate_params = ['id' => 'Required|IntGe:1', 'left_amount_date' => 'Required|Date', 'remark' => 'StrLenGeLe:0,300'];
        Validation::validate($params, $validate_params);
        $res = InitFlowService::getInstance()->update($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 流水上传-更新余额日期-获取银行账号信息
     * @Permission(action='bank_flow.upload.update')
     * @return Response|ResponseInterface
     * @throws ValidationException
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87917
     */
    public function getAccountInfoAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['id' => 'Required|IntGe:1']);
        $bank_info = BankAccountRepository::getInstance()->getBankAccount($params);
        $data = [];
        if ($bank_info) {
            $bank_info = $bank_info->toArray();
            $data = [
                'left_amount' => $bank_info['left_amount'],
                'left_amount_date' => $bank_info['left_amount_date'],
                'currency' => $bank_info['currency'],
                'currency_text' => $this->t->_(GlobalEnums::$currency_item[$bank_info['currency']] ?? ''),
            ];
        }
        return $this->returnJson(ErrCode::$SUCCESS, '', $data);
    }
}