<?php
// 资金日报

namespace App\Modules\BankFlow\Controllers;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\BankFlow\Services\DataReportService;

class FundsController extends BaseController
{
    /**
     * 费用支出数据查询
     * @Permission(action='bank_flow.day_money.pay_and_get.list')
     */
    public function expenseViewAction()
    {
        try {
            // 参数校验
            $trade_date = $this->request->get('trade_date');
            $company_name = $this->request->get('company_name','trim','');

            $validate_params = [
                'trade_date' => 'Required|date|>>>:param error[trade_date]',
                'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
            ];

            Validation::validate(['trade_date' => $trade_date,'company_name'=>$company_name], $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = DataReportService::getInstance()->getExpenseData($trade_date, false,$company_name);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 费用支出数据导出
     * @Permission(action='bank_flow.day_money.pay_and_get.export')
     *
     */
    public function expenseExportAction()
    {
        try {
            // 参数校验
            $trade_date = $this->request->get('trade_date');
            $company_name = $this->request->get('company_name','trim','');

            $validate_params = [
                'trade_date' => 'Required|date|>>>:param error[trade_date]',
                'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
            ];

            Validation::validate(['trade_date' => $trade_date,'company_name'=>$company_name], $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('bank_flow_expense_export_' . $trade_date . '_' . $this->user['id']);
        $data = $this->atomicLock(function() use ($trade_date,$company_name){
            return DataReportService::getInstance()->exportExpenseData($trade_date,$company_name);
        }, $lock_key, 10);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }


    /**
     * 余额汇总数据查询
     * @Permission(action='bank_flow.day_money.collect.list')
     *
     */
    public function balanceSummaryViewAction()
    {
        try {
            // 参数校验
            $trade_date = $this->request->get('trade_date');
            $company_name = $this->request->get('company_name','trim','');

            $validate_params = [
                'trade_date' => 'Required|date|>>>:param error[trade_date]',
                'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
            ];

            Validation::validate(['trade_date' => $trade_date,'company_name'=>$company_name], $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $data = DataReportService::getInstance()->getBalanceSummaryData($trade_date, false,$company_name);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 余额汇总数据导出
     * @Permission(action='bank_flow.day_money.collect.export')
     *
     */
    public function balanceSummaryExportAction()
    {
        try {
            // 参数校验
            $trade_date = $this->request->get('trade_date');
            $company_name = $this->request->get('company_name','trim','');

            $validate_params = [
                'trade_date' => 'Required|date|>>>:param error[trade_date]',
                'company_name' => 'StrLenGe:0|>>>:param error[company_name]',
            ];

            Validation::validate(['trade_date' => $trade_date,'company_name'=>$company_name], $validate_params);

        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        }

        $lock_key = md5('bank_flow_balance_summary_data_export_' . $trade_date . '_' . $this->user['id']);
        $data = $this->atomicLock(function() use ($trade_date,$company_name){
            return DataReportService::getInstance()->exportBalanceSummaryData($trade_date,$company_name);
        }, $lock_key, 10);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

}
