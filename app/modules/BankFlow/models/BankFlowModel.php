<?php

namespace App\Modules\BankFlow\Models;
use App\Models\Base;

class BankFlowModel extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('bank_flow');


        $this->hasMany(
            'id',
            BankFlowPayDetailModel::class,
            'bank_flow_id',
            [
                "alias" => "PayDetail",
            ]
        );


        $this->hasMany(
            'id',
            BankFlowGetDetailModel::class,
            'bank_flow_id',
            [
                "alias" => "GetDetail",
            ]
        );

    }
}