<?php

namespace App\Modules\BankFlow\Models;
use App\Models\Base;

class BankAccountModel extends Base
{

    // initialize 在请求期间仅会被自动调用一次，目的是为模型实例进行初始化
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('bank_account');
    }

    // 获取银行账号列表
    public static function getList($conditions)
    {
        return parent::find($conditions)->toArray();
    }

}