<?php

namespace App\Modules\BankFlow\Models;

use App\Library\CInterface\ParcelClaimModelInterface;
use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\Base;

class BankFlowParcelClaimModel extends Base implements ParcelClaimModelInterface
{
    public $pno;

    public $claim_type;

    public $pay_status;

    public $real_amount;

    public $claim_money;

    public $status;

    public $created_at;

    public $updated_at;

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        // setSource()方法指定数据库表
        $this->setSource('bank_flow_parcel_claim');
    }

    /**
     * @param $condition
     * @return mixed
     */
    public static function getFirst($condition)
    {
        return parent::findFirst($condition);
    }

    /**
     * 查询类
     * @Date: 2021-09-16 17:43
     * @return:
     **@author: peak pan
     */
    public function getModelByNos(array $no, bool $has_pay = false, bool $is_row = false)
    {
        if (!is_array($no) || empty($no)) {
            return [];
        }
        return self::find(
            [
                'conditions' => 'pno in ({pno:array}) and status=:status: and pay_status=:pay_status: and  is_cancel=:is_cancel: ',
                'bind' => [
                    'pno' => $no,
                    'status' => Enums::CONTRACT_STATUS_APPROVAL,
                    'pay_status' => Enums::LOAN_PAY_STATUS_PAY,
                    'is_cancel'  => Enums\BankFlowEnums::PK_IS_CANCEL,
                ]
            ]
        );
    }

    /**
     * 格式化返回
     * @Date: 2021-09-16 17:44
     * @return:
     **@author: peak pan
     */
    public function getFormatData()
    {
        return [
            'oa_value' => $this->id,
            'oa_type' => Enums\BankFlowEnums::BANK_FLOW_OA_TYPE_PARCEL_CLAIM,
            'no' => $this->pno,
            'amount' => $this->claim_type==Enums\BankFlowEnums::CUSTOMER_COMPENSATION?$this->claim_money:$this->real_amount,
            'currency' => Enums\BankFlowEnums::CURRENCY_MONEY_SYMBOL,//泰铢
            'status'   => $this->status,
            'is_cancel'   => $this->is_cancel,
            'pay_status' => $this->pay_status
        ];
    }

    public function refresh()
    {
        if ($this->getWriteConnection()->isUnderTransaction()) {
            $this->setReadConnectionService('db_oa');
        }
        return parent::refresh();
    }

    /**
     * 单次查找运单号
     * @Date: 2021-09-17 14:01
     * @return:
     **@author: peak pan
     */
    public function getModelByNo(string $no)
    {
        return self::findFirst(
            [
                'conditions' => 'pno = :pno:',
                'bind' => ['pno' => $no]
            ]
        );
    }

    /**
    * 提交处理
    * @Date: 2021-09-17 14:02
    * @author: peak pan
    * @return:
    **/
    public function link(array $data)
    {
        //判断现有的状态
        if (empty($this) || $this->status != Enums::CONTRACT_STATUS_APPROVAL || $this->pay_status != Enums::LOAN_PAY_STATUS_PENDING) {
            throw new BusinessException('not found salary or salary status is error');
        }

        $item = [];
        $item['payer_bank'] = $data['bank_name'];
        $item['payer_no'] = $data['bank_account'];
        $item['pay_date'] = $data['date'];
        $item['payed_at'] = date("Y-m-d H:i:s");
        $item['updated_at'] = date("Y-m-d H:i:s");
        $item['pay_status'] = Enums::LOAN_PAY_STATUS_PAY;    //是否已付款
        $item['pay_remark'] = $data['ticket_no'];
        $item['payer_id'] = $data['create_id'];
        $item['pay_from'] = 2;

        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("薪资发放-支付失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    public function batch_link($ids, $data){
        $sql = 'update bank_flow_parcel_claim set 
                         is_cancel=1,
                         updated_at="' . date("Y-m-d H:i:s") . '"
                         where id in (' . implode(',', $ids).')';
        $bool = $this->getDI()->get('db_oa')->execute($sql);
        if ($bool === false) {
            throw new BusinessException('赔付更新失败==' . $sql);
        }
        return true;
    }

    /**
    * 撤回处理
    * @Date: 2021-09-17 14:02
    * @author: peak pan
    * @return:
    **/
    public function cancel(array $user)
    {
        //判断现有的状态
        if (empty($this) || $this->pay_status != Enums::LOAN_PAY_STATUS_PAY) {
            throw new BusinessException('not found parcel_claim pay_status is error');
        }
        $item = [];
        $item['status'] = Enums::CONTRACT_STATUS_APPROVAL;
        $item['is_cancel'] = 0;
        $item['updated_at'] = date("Y-m-d H:i:s");
        $bool = $this->i_update($item);
        if ($bool === false) {
            throw new BusinessException("赔款-撤销失败", ErrCode::$CONTRACT_UPDATE_ERROR);
        }
        return true;
    }

    public function batch_confirm($ids, $data)
    {
        return true;
        // TODO: Implement batch_confirm() method.
    }

    /**
     * 支付模块修改收款方银行信息 -> 同步更新业务侧收款方银行信息
     *
     * @param array $data
     * @param array $user
     * @return bool
     */
    public function syncUpdatePayeeInfo(array $data, array $user = [])
    {
        return true;
    }

}
