<?php
namespace App\Modules\AgencyPayment\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\AgencyPayment\Services\BaseService;
use App\Library\Validation\ValidationException;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 代理支付-数据查询
 * Class PayController
 * @package App\Modules\AgencyPayment\Controllers
 */
class DataController extends BaseController
{
    /**
     * 列表
     * @Permission(action='agency_payment.data.list')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84653
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        $res = AgencyPaymentService::getInstance()->getList($params, $this->user, AgencyPaymentService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='agency_payment.data.export')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84686
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        // 加锁处理
        $lock_key = md5('agency_payment_data_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->listExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_DATA, AgencyPaymentService::LIST_TYPE_DATA);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='agency_payment.data.detail')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84737
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->detail($params, $this->user, AgencyPaymentService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细
     * @Permission(action='agency_payment.data.detail')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84752
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细 - 导出
     * @Permission(action='agency_payment.data.detail')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84764
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_data_detail_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_DATA_DETAIL_EXPORT);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 删除
     * @Permission(action='agency_payment.data.delete')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84716
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->delete($params['id'], $this->user, AgencyPaymentService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 补充附件
     * @Permission(action='agency_payment.data.attachment')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84722
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function attachmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_attachment);
        $res = AgencyPaymentService::getInstance()->attachment($params, $this->user, AgencyPaymentService::LIST_TYPE_DATA);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行
     * @Permission(action='agency_payment.data.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87605
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailSummaryList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行 - 导出
     * @Permission(action='agency_payment.data.detail')
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87608
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_data_detail_summary_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailSummaryExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}