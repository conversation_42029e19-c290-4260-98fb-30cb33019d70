<?php
namespace App\Modules\AgencyPayment\Controllers;

use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\AgencyPayment\Services\BaseService;
use App\Library\Validation\ValidationException;

/**
 * 代理支付-我的申请
 * Class ApplyController
 * @package App\Modules\AgencyPayment\Controllers
 */
class ApplyController extends BaseController
{
    /**
     * 代理支付默认配置项
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84674
     * @return Response|ResponseInterface
     */
    public function getOptionsDefaultAction()
    {
        $res = AgencyPaymentService::getInstance()->getOptionsDefault();
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 代理支付-费用部门列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84839
     * @return Response|ResponseInterface
     */
    public function getCostSysDepartmentListAction()
    {
        $res = AgencyPaymentService::getInstance()->getCostSysDepartmentList($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 列表
     * @Permission(action='agency_payment.apply.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84656
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        $res = AgencyPaymentService::getInstance()->getList($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='agency_payment.apply.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84656
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        // 加锁处理
        $lock_key = md5('agency_payment_apply_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->listExport($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 暂存（系统性）
     * @Permission(action='agency_payment.apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84776
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function stagingPaymentAction()
    {
        $res = AgencyPaymentService::getInstance()->stagingPayment($this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 新增 - 取消 - 暂存（系统性）数据删除
     * @Permission(action='agency_payment.apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84779
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function stagingPaymentDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->stagingPaymentDel($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 计算
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84785
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function calculateAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->calculate($params['id']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 新增
     * @Permission(action='agency_payment.apply.add')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84788
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function addAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5('agency_payment_add_' . $params['id']);
        Validation::validate($params, AgencyPaymentService::$validate_add);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->save($params, $this->user);
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 付款明细-批量新增
     * @Permission(menu='agency_payment.apply')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84848
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAddAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_detail_add);
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5('agency_payment_add_detail_' . $params['id']);
        $res = $this->atomicLock(function() use ($params, $excel_file) {
            return AgencyPaymentService::getInstance()->detailAdd($params, $this->user, $excel_file);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }


    /**
     * 付款明细-删除/批量删除
     * @Permission(menu='agency_payment.apply')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84851
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailDelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_detail_del);
        $res = AgencyPaymentService::getInstance()->detailDel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查看
     * @Permission(action='agency_payment.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84728
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->detail($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细
     * @Permission(action='agency_payment.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84743
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细 - 导出
     * @Permission(action='agency_payment.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84758
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_apply_detail_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailExport($params, $this->user);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 编辑
     * @Permission(action='agency_payment.apply.edit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84791
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function editAction()
    {
        $params = trim_array($this->request->get());
        $lock_key = md5('agency_payment_edit_' . $params['id']);
        Validation::validate($params, AgencyPaymentService::$validate_add);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->save($params, $this->user, 'edit');
        }, $lock_key, 10);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 撤回
     * @Permission(action='agency_payment.apply.cancel')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84710
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function cancelAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_cancel_reject);
        $res = AgencyPaymentService::getInstance()->cancel($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除
     * @Permission(action='agency_payment.apply.delete')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84707
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function deleteAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->delete($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 补充附件
     * @Permission(action='agency_payment.apply.attachment')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84719
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function attachmentAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_attachment);
        $res = AgencyPaymentService::getInstance()->attachment($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行
     * @Permission(action='agency_payment.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87590
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailSummaryList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行 - 导出
     * @Permission(action='agency_payment.apply.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87593
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_apply_detail_summary_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailSummaryExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}