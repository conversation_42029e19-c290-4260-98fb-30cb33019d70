<?php
namespace App\Modules\AgencyPayment\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\Validation\Validation;
use App\Modules\AgencyPayment\Services\AgencyPaymentAuditService;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\AgencyPayment\Services\BaseService;
use App\Library\Validation\ValidationException;

/**
 * 代理支付-我的审核
 * Class AuditController
 * @package App\Modules\AgencyPayment\Controllers
 */
class AuditController extends BaseController
{
    /**
     * 列表
     * @Permission(action='agency_payment.audit.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84671
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        $res = AgencyPaymentService::getInstance()->getList($params, $this->user, AgencyPaymentService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='agency_payment.audit.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84698
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);

        // 加锁处理
        $lock_key = md5('agency_payment_audit_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->listExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_AUDIT, AgencyPaymentService::LIST_TYPE_AUDIT);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='agency_payment.audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84731
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->detail($params, $this->user, AgencyPaymentService::LIST_TYPE_AUDIT);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细
     * @Permission(action='agency_payment.audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84749
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细 - 导出
     * @Permission(action='agency_payment.audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84761
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_audit_detail_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_AUDIT_DETAIL_EXPORT);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 审核 - 通过
     * @Permission(action='agency_payment.audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84857
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function passAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentAuditService::$validate_pass);
        $res = AgencyPaymentAuditService::getInstance()->pass($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 审核 - 驳回
     * @Permission(action='agency_payment.audit.audit')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84854
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function rejectAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentAuditService::$validate_reject);
        $res = AgencyPaymentAuditService::getInstance()->reject($params, $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行
     * @Permission(action='agency_payment.audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87599
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailSummaryList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行 - 导出
     * @Permission(action='agency_payment.audit.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87593
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_audit_detail_summary_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailSummaryExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}