<?php
namespace App\Modules\AgencyPayment\Controllers;

use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Modules\AgencyPayment\Services\AgencyPaymentPayService;
use App\Modules\AgencyPayment\Services\AgencyPaymentService;
use App\Modules\AgencyPayment\Services\BaseService;
use App\Library\Validation\ValidationException;

/**
 * 代理支付-代理支付
 * Class PayController
 * @package App\Modules\AgencyPayment\Controllers
 */
class PayController extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->checkPermission();
    }

    /**
     * 检测是否是支付人
     * @throws ValidationException
     */
    public function checkPermission()
    {
        AgencyPaymentPayService::getInstance()->checkPermission($this->user);
    }

    /**
     * 列表
     * @Permission(action='agency_payment.pay.list')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84659
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        $res = AgencyPaymentService::getInstance()->getList($params, $this->user, AgencyPaymentService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 导出
     * @Permission(action='agency_payment.pay.export')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84695
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function exportAction()
    {
        $params = trim_array($this->request->get());
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_list);
        // 加锁处理
        $lock_key = md5('agency_payment_pay_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->listExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_PAY, AgencyPaymentService::LIST_TYPE_PAY);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 查看
     * @Permission(action='agency_payment.pay.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84734
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentService::getInstance()->detail($params, $this->user, AgencyPaymentService::LIST_TYPE_PAY);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细
     * @Permission(action='agency_payment.pay.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84755
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款明细 - 导出
     * @Permission(action='agency_payment.pay.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84767
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_pay_detail_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailExport($params, $this->user, DownloadCenterEnum::AGENCY_PAYMENT_PAY_DETAIL_EXPORT);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 批量支付
     * @Permission(action='agency_payment.pay.pay')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84884
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function payAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, AgencyPaymentService::$validate_id);
        try {
            $excel_file = $this->request->getUploadedFiles();
        } catch (\Exception $e) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, $e->getMessage());
        }
        $lock_key = md5('agency_payment_pay_pay_' . $params['id']);
        $res = $this->atomicLock(function() use ($params, $excel_file) {
            return AgencyPaymentPayService::getInstance()->batchPay($params, $this->user, $excel_file);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }

    /**
     * 完成支付
     * @Permission(action='agency_payment.pay.pay')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/84881
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function donePayAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        $res = AgencyPaymentPayService::getInstance()->updatePayInfo($params['id'], $this->user);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }


    /**
     * 付款信息-付款汇总行
     * @Permission(action='agency_payment.pay.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87611
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryListAction()
    {
        $params = $this->request->get();
        $params = BaseService::handleParams($params, AgencyPaymentService::$not_must_params);
        Validation::validate($params, AgencyPaymentService::$validate_detail_list);
        $res = AgencyPaymentService::getInstance()->detailSummaryList($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 付款信息-付款汇总行 - 导出
     * @Permission(action='agency_payment.pay.detail')
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87614
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws ValidationException
     */
    public function detailSummaryExportAction()
    {
        $params = $this->request->get();
        Validation::validate($params, AgencyPaymentService::$validate_id);
        // 加锁处理
        $lock_key = md5('agency_payment_pay_detail_summary_list_export_' . $this->user['id']);
        $res = $this->atomicLock(function () use ($params) {
            return AgencyPaymentService::getInstance()->detailSummaryExport($params);
        }, $lock_key, 30);
        return $this->returnJson($res['code'] ?? ErrCode::$FREQUENT_VISIT_ERROR, $res['message'] ?? $this->t['sys_processing'], $res['data'] ?? []);
    }
}