<?php
namespace App\Modules\AgencyPayment\Services;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\SysConfigEnums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Third\Services\ByWorkflowService;

class AgencyPaymentAuditService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //通过
    public static $validate_pass = [
        'id' => 'Required|IntGt:0',
        'reason' => 'StrLenGeLe:0,500',
    ];

    //驳回
    public static $validate_reject = [
        'id' => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,500',
    ];

    /**
     * 通过
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function pass($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $this->logger->info('代理支付-驳回，审批人信息：【 ' . json_encode($user, JSON_UNESCAPED_UNICODE) . ' 】，参数组：【 ' . json_encode($params, JSON_UNESCAPED_UNICODE)) . '】';
            $agency_payment_info = $this->validationAudit($params);
            //调取by审批接口，by审核通过成功，记录通过信息
            $reason = $params['reason'] ?? '';
            $by_workflow = new ByWorkflowService();
            $result = $by_workflow->audit([
                'serial_no' => $agency_payment_info->workflow_no,
                'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
                'reason' => $reason,
                'status' => ByWorkflowEnums::BY_OPERATE_PASS,
                'operator_id' => $user['id'],
            ]);
            //by审批通过且最终节点审批才需要做下面的逻辑
            $now_time = date('Y-m-d H:i:s');
            if (!empty($result) && !empty($result['is_final']) && $result['is_final'] == 1) {
                $pass_params = [
                    'status' => Enums::WF_STATE_APPROVED,
                    'reason' => $reason,
                    'approved_at' => $now_time,
                    'updated_at' => $now_time
                ];
                // 接口传输单据 || 审批通过时判断费用所属公司同步数据到支付模块开关是否开启，开启则需要标记进入了支付模块
                if ($agency_payment_info->source_type == AgencyPaymentEnums::SOURCE_TYPE_API || EnumsService::getInstance()->getPayModuleStatus(SysConfigEnums::SYS_MODULE_AGENCY_PAYMENT, $agency_payment_info->cost_company_id)) {
                    $pass_params['is_pay_module'] = PayEnums::BIZ_DATA_IS_PAY_MODULE_YES;
                    $pass_params['is_push_pay_module'] = AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_DEFAULT;
                }
                $bool = $agency_payment_info->i_update($pass_params);
                if ($bool === false) {
                    throw new BusinessException('代理支付-通过失败: 待处理数据: '. json_encode($pass_params, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
                }
            }
            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $pass_log = [
                'biz_type' => Enums::WF_AGENCY_PAYMENT_BIZ_TYPE,
                'biz_value' => $agency_payment_info->id,
                'staff_id' => $agency_payment_info->apply_id,
                'approval_id' => $user['id'],
                'status' => Enums::WF_STATE_APPROVED,
                'approval_time' => $now_time,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $by_workflow_audit_log->i_create($pass_log);
            if ($bool === false) {
                throw new BusinessException('代理支付-通过-记录by审批日志失败: 待处理数据: '. json_encode($pass_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->error('agency-payment-audit-pass failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool ?? false
        ];
    }

    /**
     * 检测支付单据是否可审批
     * @param array $params 请求参数组
     * @return mixed
     * @throws ValidationException
     */
    public function validationAudit($params)
    {
        $agency_payment_info = AgencyPaymentService::getInstance()->getAgencyPaymentInfoById($params['id']);
        if ($agency_payment_info->status != Enums::WF_STATE_PENDING) {
            throw new ValidationException(static::$t->_('agency_payment_audit_status_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $agency_payment_info;
    }

    /**
     * 驳回
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function reject($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $this->logger->info('代理支付-驳回，审批人信息：【 ' . json_encode($user, JSON_UNESCAPED_UNICODE) . ' 】，参数组：【 ' . json_encode($params, JSON_UNESCAPED_UNICODE)) . '】';
            $agency_payment_info = $this->validationAudit($params);
            //调取by驳回接口，by驳回成功，记录驳回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit([
                'serial_no' => $agency_payment_info->workflow_no,
                'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
                'reason' => $params['reason'],
                'status' => ByWorkflowEnums::BY_OPERATE_REJECT,
                'operator_id' => $user['id'],
            ]);
            //by审批成功，驳回单据
            $now_time = date('Y-m-d H:i:s');
            $reject_params = [
                'reason' => $params['reason'],
                'status' => Enums::WF_STATE_REJECTED,
                'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY,
                'rejected_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $agency_payment_info->i_update($reject_params);
            if ($bool === false) {
                throw new BusinessException('代理支付-驳回失败: 待处理数据: '. json_encode($reject_params, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }

            //明细行的支付状态修改为未支付
            $all_update_success = $db->updateAsDict(
                (new AgencyPaymentDetailModel())->getSource(),
                [
                    'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY,
                    'is_pay' => AgencyPaymentEnums::IS_PAY_NO,
                    'updated_at' => $now_time
                ],
                [
                    'conditions' => "agency_payment_id = {$params['id']}",
                ]
            );
            if ($all_update_success === false) {
                throw new BusinessException('代理支付-驳回-更改明细行支付状态为未支付失败：'. json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }

            //记录审批操作记录
            $by_workflow_audit_log = new ByWorkflowAuditLogModel();
            $reject_log = [
                'biz_type' => Enums::WF_AGENCY_PAYMENT_BIZ_TYPE,
                'biz_value' => $agency_payment_info->id,
                'staff_id' => $agency_payment_info->apply_id,
                'approval_id' => $user['id'],
                'status' => Enums::WF_STATE_REJECTED,
                'approval_time' => $now_time,
                'created_at' => $now_time,
                'updated_at' => $now_time
            ];
            $bool = $by_workflow_audit_log->i_create($reject_log);
            if ($bool === false) {
                throw new BusinessException('代理支付-驳回-记录by审批日志失败: 待处理数据: '. json_encode($reject_log, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($by_workflow_audit_log), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('agency-payment-audit-reject failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool ?? false
        ];
    }
}
