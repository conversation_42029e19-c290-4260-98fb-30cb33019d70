<?php
namespace App\Modules\AgencyPayment\Services;

use App\Library\Enums\AgencyPaymentEnums;
use App\Modules\Common\Services\EnumsService;

class BaseService extends \App\Library\BaseService
{
    /**
     * 过滤空值 和 非必要参数
     * @param array $params
     * @param array $not_must
     * @return mixed
     */
    public static function handleParams($params, $not_must)
    {
        if (empty($not_must) || !is_array($not_must)) {
            return $params;
        }
        foreach ($not_must as $value) {
            if (isset($params[$value]) && empty($params[$value])) {
                unset($params[$value]);
            }
        }
        return $params;
    }

    /**
     * 获取支付人
     * @return array
     */
    public static function getPayStaffs()
    {
        return EnumsService::getInstance()->getSettingEnvValueIds('agency_payment_pay_staff_ids');
    }

    /**
     * 获取费用类型
     * @return array
     */
    public static function getCostType()
    {
        return EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_cost_type');
    }

    /**
     * 获取费用类型枚举
     * @return array
     */
    public static function getCostTypeEnums()
    {
        $items = [];
        $cost_type_list = array_keys(self::getCostType());
        foreach ($cost_type_list as $cost_type) {
            $items[$cost_type] = 'agency_payment_cost_type.' . $cost_type;
        }
        return $items;
    }

    /**
     * 获取费用类型枚举
     * @return array
     */
    public static function getCostTypeEnumsList(): array
    {
        $items = [];
        $cost_type_list = array_keys(self::getCostType());
        foreach ($cost_type_list as $cost_type) {
            $items[] = [
                'value' => $cost_type,
                'key'   => static::$t->_('agency_payment_cost_type.' . $cost_type),
            ];
        }
        return $items;
    }

    /**
     * 将日月年日期格式转化为年与日
     * @param string $file_src_date 日期
     * @param string $is_format 日期格式化
     * @return string
     */
    public function handleUploadFileDate($file_src_date, $is_format = 'd-m-Y')
    {
        if (preg_match('/\d{10}/', $file_src_date)) {
            // 时间戳
            $file_src_date = date($is_format, $file_src_date);
        } elseif (preg_match(AgencyPaymentEnums::DATE_RULE, $file_src_date)) {
            if ($is_format != 'd-m-Y') {
                $file_src_date = str_replace('/', '-', $file_src_date);
                $date_arr = explode('-', $file_src_date);
                $file_src_date = $date_arr[2] . '-' . $date_arr[1] . '-' . $date_arr[0];
            }
        } else {
            $file_src_date = '';
        }
        return $file_src_date;
    }

    /**
     * 费用类型、费用所属公司，在系统配置的代理支付同步sap的公司ID和费用类型，那么明细行的费用部门和费用网点必须传输
     * @param integer $cost_company_id 费用所属公司id
     * @param integer $cost_type 费用类型：1个人代理，2众包费用，3加盟商提现，4仓管小时工等
     * @return bool true必须、false非必须
     */
    public function checkDetailIsRequired($cost_company_id, $cost_type)
    {
        if (empty($cost_company_id) || empty($cost_type)) {
            return false;
        }
        $agency_payment_sap_company_cost_type = EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_sap_company_cost_type');
        //未设置 || 未设置某公司 || 某公司下未设置费用类型均非必填
        if (empty($agency_payment_sap_company_cost_type) || empty($agency_payment_sap_company_cost_type[$cost_company_id]) || !in_array($cost_type, explode(',', $agency_payment_sap_company_cost_type[$cost_company_id]))) {
            return false;
        }
        return true;
    }
}