<?php
namespace App\Modules\AgencyPayment\Services;

use App\Library\Enums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Models\oa\AgencyPaymentModel;
use App\Modules\Pay\Services\PayService;
use GuzzleHttp\Exception\GuzzleException;

class AgencyPaymentPayService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 检测是否是支付人
     * @param array $user 当前登陆者信息组
     * @throws ValidationException
     * @return bool
     */
    public function checkPermission($user)
    {
        $agency_payment_pay_staff_ids = BaseService::getPayStaffs();
        if (!in_array($user['id'], $agency_payment_pay_staff_ids)) {
            throw new ValidationException(static::$t->_('agency_payment_cannot_pay'), ErrCode::$VALIDATE_ERROR);
        }
        return true;
    }

    /**
     * 推送代理支付到支付模块
     * @param object $agency_payment_info 代理支付批次对象
     * @return array
     */
    public function pushPayModule($agency_payment_info)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_details = $agency_payment_info->getDetails();
            foreach ($agency_payment_details as $agency_payment_detail) {
                PayService::getInstance()->saveOne($agency_payment_detail);
            }
            $agency_payment_info->is_push_pay_module = AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_SUCCESS;
            $agency_payment_info->updated_at = date('Y-m-d H:i:s');
            $bool = $agency_payment_info->save();
            if ($bool === false) {
                throw new BusinessException('代理支付-推送单据到支付模块失败 = ' . json_encode(['is_push_pay_module' => AgencyPaymentEnums::AGENCY_PAYMENT_IS_PUSH_PAY_MODULE_SUCCESS], JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($message)) {
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->error('agency-payment-pushPayModule failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => []
        ];
    }

    /**
     * 代理支付 - 批量支付
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param array $excel_file excel文件内容
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function batchPay($params, $user, $excel_file)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $excel_header_column = $excel_data = $new_excel_data = $detail_data =  [];
        $start_time = get_curr_micro_time();
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            $agency_payment_info = $this->validateAgencyPayment($params['id'], 2);
            //流入支付模块的单据不可在本模块的支付接口里支付
            if ($agency_payment_info->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_YES) {
                throw new ValidationException(static::$t->_('payment_has_entered_the_payment_module'), ErrCode::$VALIDATE_ERROR);
            }
            [$excel_header_column, $excel_data] = $this->checkBatchPayData($agency_payment_info, $excel_file, $user);
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-detail-batchPay-failed:' . $real_message);
        }
        if (!in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR])) {
            //非验证类或系统错误的提示，需要生成下载模版
            $success_num = 0;
            $result_row = AgencyPaymentEnums::DETAIL_BATCH_PAY_EXCEL_RESULT;
            foreach ($excel_data as $item) {
                if ($code != ErrCode::$SUCCESS && strpos($item[$result_row], 'success') !== false) {
                    $item[$result_row] = '';
                } elseif (strpos($item[$result_row], 'success') !== false) {
                    $success_num++;
                }
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, '付款明细-批量支付 -' . date('YmdHis'));
            //内容包含模版第二行表头，所以需要总数-1
            $all_num = count($new_excel_data)-1;
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => $all_num - $success_num
            ];
            $code = ErrCode::$SUCCESS;
            $end_time = get_exec_time($start_time);
            $this->logger->info('付款明细-批量支付[' . $all_num . ']条预计耗时:[ ' . $end_time . ' ]');
        }
        return [
            'code' => $code,
            'message' => in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR]) ? $message : 'success',
            'data' => $data ?? [],
        ];
    }

    /**
     * 检测支付批次信息是否合理
     * @param integer $agency_payment_id 支付批次ID
     * @param integer $type 操作类型，1完成支付，2付款明细批量支付
     * @return mixed
     * @throws ValidationException
     */
    private function validateAgencyPayment($agency_payment_id, $type = 1)
    {
        //获取批次信息
        $agency_payment_info = AgencyPaymentService::getInstance()->getAgencyPaymentInfoById($agency_payment_id);
        //非已通过 || 非待支付、支付中的单据不可变更支付状态
        if ($agency_payment_info->status != Enums::WF_STATE_APPROVED || !in_array($agency_payment_info->pay_status, [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING])) {
            throw new ValidationException(static::$t->_($type == 1 ? 'agency_payment_pay_status_invalid' : 'agency_payment_detail_pay_error_000'), ErrCode::$VALIDATE_ERROR);
        }
        return $agency_payment_info;
    }


    /**
     * 检测数据合理性
     * @param object $agency_payment_info 支付批次对象信息
     * @param array $excel_file excel文件内容
     * @param array $user 当前登陆者信息组
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function checkBatchPayData($agency_payment_info, $excel_file, $user)
    {
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,//OA唯一号
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,//是否支付
                    2 => \Vtiful\Kernel\Excel::TYPE_STRING,//支付银行
                    3 => \Vtiful\Kernel\Excel::TYPE_STRING,//支付银行账号
                    4 => \Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//银行流水日期
                    5 => \Vtiful\Kernel\Excel::TYPE_STRING,//未支付原因
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('agency_payment_detail_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        //弹出excel标题第二行信息
        $second_header = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('agency_payment_detail_pay_empty'), ErrCode::$VALIDATE_ERROR);
        }
        //跳过空行
        foreach ($excel_data as $line => $row) {
            if (empty(implode(',', array_filter($row)))) {
                //空行
                unset($excel_data[$line]);
            }
        }
        //单次操作付款明细条数限制
        if (count($excel_data) > AgencyPaymentEnums::DETAIL_ONE_ADD_MAX_NUM) {
            throw new ValidationException(static::$t->_('agency_payment_detail_import_num_limit', ['max' => AgencyPaymentEnums::DETAIL_ONE_ADD_MAX_NUM]), ErrCode::$VALIDATE_ERROR);
        }

        $excel_data = $this->excelToData($excel_data);
        //获取单据付款明细行
        $agency_payment_details = $agency_payment_info->getDetails()->toArray();
        $detail_nos = array_column($agency_payment_details, null, 'no');

        //开始解析excel
        $excel_data_nos = array_count_values(array_filter(array_column($excel_data, 'no')));
        foreach ($excel_data as $key => &$item) {
            $error_msg = [];
            //申请单号,必填
            if (empty($item['no'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_001');
            } elseif (($excel_data_nos[$item['no']] ?? 0) > 1) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_001_01');
            } elseif (!isset($detail_nos[$item['no']])) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_002');
            }
            //是否支付：下拉框，必填Y、N
            if (empty($item['is_pay']) || !in_array($item['is_pay'], ['Y', 'N'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_003');
            }
            //支付银行：非必填，最多50字符
            if ($item['pay_bk_name'] && mb_strlen($item['pay_bk_name']) > 50) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_004');
            }
            //支付银行账号：非必填，最多50字符
            if ($item['pay_bk_account'] && mb_strlen($item['pay_bk_account']) > 50) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_005');
            }
            //银行流水日期：非必填，按照dd-mm-yyyy或者dd/mm/yyyy进行解析
            $item['pay_bank_flow_date'] = $this->handleUploadFileDate($item['pay_bank_flow_date']);
            if ($item['pay_bank_flow_date'] && !preg_match(AgencyPaymentEnums::DATE_RULE, $item['pay_bank_flow_date'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_006');
            }
            //未支付原因：当是否支付为“N”的时候，必填，最多500字符。
            if ($item['is_pay'] == 'N' && (empty($item['pay_remark']) || mb_strlen($item['pay_remark']) > 500)) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_007');
            }
            //未支付原因：当是否支付为“Y”的时候，该字段不允许填
            if ($item['is_pay'] == 'Y' && !empty($item['pay_remark'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_pay_error_008');
            }

            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                $item[AgencyPaymentEnums::DETAIL_BATCH_PAY_EXCEL_RESULT] = 'failed' . implode(';', array_unique($error_msg));
            } else {
                $pay_bank_flow_date = $this->handleUploadFileDate($item['pay_bank_flow_date'], 'Y-m-d');
                $agency_payment_detail_info = $detail_nos[$item['no']];
                $detail_data = [
                    'is_pay' => $item['is_pay'] == 'Y' ? AgencyPaymentEnums::IS_PAY_YES : AgencyPaymentEnums::IS_PAY_NO,
                    'pay_status' => $item['is_pay'] == 'Y' ? AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY : AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY,
                    'pay_bk_name' => $item['pay_bk_name'],
                    'pay_bk_account' => $item['pay_bk_account'],
                    'pay_bank_flow_date' => $pay_bank_flow_date ? $pay_bank_flow_date : null,
                    'pay_remark' => $item['pay_remark']
                ];
                $res = $this->pay($agency_payment_detail_info['id'], $detail_data, $user);
                if ($res['code'] == ErrCode::$SUCCESS) {
                    $item[AgencyPaymentEnums::DETAIL_BATCH_PAY_EXCEL_RESULT] = 'success';
                } else {
                    $item[AgencyPaymentEnums::DETAIL_BATCH_PAY_EXCEL_RESULT] = 'failed' . $res['message'];
                }
            }
        }
        return [$excel_header_column, array_merge([$second_header], $excel_data)];
    }

    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'no',//申请单号（OA唯一号）
            'is_pay',//是否支付，1是，2否
            'pay_bk_name',//支付银行
            'pay_bk_account',//支付银行账号
            'pay_bank_flow_date',//银行流水日期
            'pay_remark',//未支付原因
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                $data[$line][$key] = trim($info[$index]);
            }
        }
        return $data;
    }


    /**
     * 支付
     * @param integer $id 付款明细行id
     * @param array $data 支付参数组
     * @param array $user 支付人信息组
     * @param int $is_from $is_from 1本模块，2是付款模块 跟其他模块保持统一冗余参数
     * @param  array $item_arr 修改的数据
     * @return array
     */
    public function pay($id, $data, $user, $is_from = PayEnums::IS_FROM_SELF, $item_arr = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $real_message = '';
        try {
            $agency_payment_detail = AgencyPaymentDetailModel::findFirst([
                'conditions' => 'id = :id:',
                'bind' => ['id' => $id]
            ]);
            //单据是否存在
            if (empty($agency_payment_detail)) {
                throw new ValidationException(static::$t->_('agency_payment_detail_not_found'), ErrCode::$VALIDATE_ERROR);
            }
            //从支付模块过来的单据支付，非待支付不可再被重复支付
            if ($is_from == PayEnums::IS_FROM_PAY && $agency_payment_detail->pay_status != AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING) {
                throw new ValidationException(static::$t->_('repeated_payment_error_hint', ['serial_no' => $agency_payment_detail->no]), ErrCode::$VALIDATE_ERROR);
            }
            //更新付款明细行支付信息
            $data = $this->handleData($data, $user);
            //由于本模块可重复支付覆盖更新，所以要记录文件日志
            if ($is_from == PayEnums::IS_FROM_SELF) {
                $this->logger->info('代理支付-操作单据：' . $agency_payment_detail->no . ', 操作人：' . $user['id'] . ', 操作前：' . json_encode($agency_payment_detail->toArray(), JSON_UNESCAPED_UNICODE) . ', 操作后：' . json_encode($data, JSON_UNESCAPED_UNICODE));
            }
            $bool = $agency_payment_detail->i_update($data);
            if ($bool === false) {
                throw new BusinessException('代理支付-支付失败: 待处理数据: '. json_encode($data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_detail), ErrCode::$BUSINESS_ERROR);
            }

            //三级审批人- 已支付 || 申请人在支付模块撤回 || 一级支付人拒绝支付（仅针对泰国）传递了回调标识则需要调取异步通知
            $need_call_notify_url = $item_arr['send_crowd'] ?? false;
            if ($need_call_notify_url) {
                AgencyPaymentApiService::getInstance()->callNotifyUrl($agency_payment_detail);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-pay-update-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool ?? false
        ];
    }

    /**
     * 格式化支付信息
     * @param array $data 请求参数组
     * @param array $user 支付人信息组
     * @return array
     */
    private function handleData($data, $user)
    {
        return [
            'pay_status' => $data['pay_status'],
            'is_pay' => $data['is_pay'] ?? 0,
            'pay_staff_id' => $user['id'],
            'pay_bk_name' => $data['pay_bk_name'],
            'pay_bk_account' => $data['pay_bk_account'],
            'pay_bank_flow_date' => $data['pay_bank_flow_date'] ?? null,
            'pay_at' => date('Y-m-d H:i:s'),
            'pay_remark' => $data['pay_remark'] ?? '',
            'updated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 更新支付批次支付状态等支付信息
     * @param integer $agency_payment_id 支付批次ID
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function updatePayInfo($agency_payment_id, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = '';
        $real_message = '';
        try {
            $agency_payment_info = $this->validateAgencyPayment($agency_payment_id);
            $agency_payment_details = $agency_payment_info->getDetails()->toArray();
            $detail_pay_status = array_unique(array_column($agency_payment_details, 'pay_status'));

            //批次支付状态获取逻辑
            $pay_status = $agency_payment_info->pay_status;
            if (count($detail_pay_status) == 1) {
                //当明细行的支付状态只有一只种情况
                if ($detail_pay_status[0] == AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING) {
                    throw new ValidationException(static::$t->_('agency_payment_pay_status_invalid'), ErrCode::$VALIDATE_ERROR);
                } elseif ($detail_pay_status[0] == AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY) {
                    //当每一行的支付状态为“已支付”，则单据头状态为“已支付”
                    $pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY;
                } elseif ($detail_pay_status[0] == AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY) {
                    //当每一行的支付状态为“未支付”，则单据头状态为“未支付”
                    $pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY;
                }
            } else {
                //当行的支付状态大于等于2种
                if (in_array(AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, $detail_pay_status)) {
                    //且支付状态包含“待支付”，则单据头状态为“支付中”
                    $pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING;
                } else {
                    //行的支付状态只有“未支付”和“已支付”，则单据头状态为“部分支付”
                    $pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_DEPART;
                }
            }

            $now = date('Y-m-d H:i:s');
            $update_pay_data = [
                'pay_status' => $pay_status,
                'updated_at' => $now
            ];
            //本业务模块单据支付需记录完成支付人信息，支付模块的单据无需记录
            if ($agency_payment_info->is_pay_module == PayEnums::BIZ_DATA_IS_PAY_MODULE_NO && $user) {
                $update_pay_data['pay_staff_id'] = $user['id'];
                $update_pay_data['pay_at'] = $now;
            }
            $bool = $agency_payment_info->i_update($update_pay_data);
            if ($bool === false) {
                throw new BusinessException('代理支付-支付失败: 待处理数据: '. json_encode($update_pay_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            //业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-updatePayInfo-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool ?? false
        ];
    }

    /**
     * 获取待支付数
     * @param array $user 当前登陆者信息组
     * @return int
     */
    public function getPayPendingCount($user)
    {
        try {
            $this->checkPermission($user);
            return AgencyPaymentModel::count([
                'conditions' => 'status = :status: and pay_status in ({pay_status:array}) and is_pay_module = :is_pay_module:',
                'bind' => ['status' => Enums::WF_STATE_APPROVED,
                    'pay_status' => [
                        AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING,
                        AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING,
                    ],
                    'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO
                ]
            ]);
        } catch (ValidationException $e) {
            return 0;
        }
    }
}
