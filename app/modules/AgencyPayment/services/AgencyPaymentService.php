<?php
namespace App\Modules\AgencyPayment\Services;

use App\Library\Enums;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\AgencyPaymentEnums;
use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\MaterialEnums;
use App\Library\Enums\PayEnums;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Models\oa\AgencyPaymentDetailSummaryModel;
use App\Models\oa\SysAttachmentModel;
use App\Modules\Common\Services\DownloadCenterService;
use App\Modules\Common\Services\EnumsService;
use App\Modules\Reimbursement\Services\ListService;
use App\Modules\User\Services\StaffService;
use App\Modules\User\Services\UserService;
use App\Repository\DepartmentRepository;
use App\Repository\StoreRepository;
use App\Util\RedisKey;
use GuzzleHttp\Exception\GuzzleException;
use App\Models\oa\AgencyPaymentDetailModel;
use App\Models\oa\AgencyPaymentModel;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Modules\Purchase\Services\BaseService as PurchaseService;
use App\Modules\Third\Services\ByWorkflowService;

class AgencyPaymentService extends BaseService
{
    const LIST_TYPE_APPLY = 1;//我的申请
    const LIST_TYPE_AUDIT = 2;//我的审核
    const LIST_TYPE_PAY = 3;//代理支付
    const LIST_TYPE_DATA = 4;//数据查询

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    //导出类型与列表类型映射关系
    public static $list_type = [
        DownloadCenterEnum::AGENCY_PAYMENT_APPLY => self::LIST_TYPE_APPLY,
        DownloadCenterEnum::AGENCY_PAYMENT_APPLY_DETAIL_EXPORT => 0,
        DownloadCenterEnum::AGENCY_PAYMENT_AUDIT => self::LIST_TYPE_AUDIT,
        DownloadCenterEnum::AGENCY_PAYMENT_AUDIT_DETAIL_EXPORT => 0,
        DownloadCenterEnum::AGENCY_PAYMENT_PAY => self::LIST_TYPE_PAY,
        DownloadCenterEnum::AGENCY_PAYMENT_PAY_DETAIL_EXPORT => 0,
        DownloadCenterEnum::AGENCY_PAYMENT_DATA => self::LIST_TYPE_DATA,
        DownloadCenterEnum::AGENCY_PAYMENT_DATA_DETAIL_EXPORT => 0,
    ];

    //列表非必需
    public static $not_must_params = [
        'status',
        'pay_status',
        'apply_date_start',
        'apply_date_end',
        'cost_company_id',
        'cost_type',
        'type',
        'pageSize',
        'pageNum'
    ];

    //列表验证器
    public static $validate_list = [
        'batch_no' => 'StrLenGeLe:0,100',//结算批次号
        'payee_name' => 'StrLenGeLe:0,255',//收款人
        'status' => 'Arr',//申请状态
        'status[*]' => 'IntGe:0',
        'pay_status' => 'Arr',//支付状态
        'pay_status[*]' => 'IntGt:0',
        'apply_date_start' => 'Date',//申请日期起始
        'apply_date_end' => 'Date',//申请日期截止
        'cost_company_id' => 'Arr',//费用所属公司
        'cost_company_id[*]' => 'IntGt:0',
        'cost_type' => 'Arr',//费用类型
        'cost_type[*]' => 'IntGt:0',
        'out_no' => 'StrLenGeLe:0,100',//外部结算号
        'type' => 'IntIn:' . AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE . ',' . AgencyPaymentEnums::LIST_TYPE_HAD_HANDLE, //列表类型：待/已处理
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //撤回、驳回
    public static $validate_cancel_reject = [
        'id' => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:1,500',
    ];

    //通过
    public static $validate_pass = [
        'id' => 'Required|IntGt:0',
        'reason' => 'Required|StrLenGeLe:0,500',
    ];

    //ID
    public static $validate_id = [
        'id' => 'Required|IntGt:0',
    ];

    //补充附件
    public static $validate_attachment = [
        'id' => 'Required|IntGt:0',
        'attachment_arr' => 'Required|ArrLenGe:0',
        'attachment_arr[*]' => 'Obj',
        'attachment_arr[*].bucket_name'=>'Required|StrLenGeLe:1,63',
        'attachment_arr[*].object_key'=>'Required|StrLenGeLe:1,100',
        'attachment_arr[*].file_name'=>'Required|StrLenGeLe:1,200',
    ];

    //付款明细-保存
    public static $validate_detail_add = [
        'id' => 'Required|IntGt:0',
        'cost_company_id' => 'Required|IntGt:0',
        'cost_type' => 'Required|IntGt:0',
    ];

    //付款明细
    public static $validate_detail_list = [
        'id' => 'Required|IntGt:0',
        'pageSize' => 'IntGt:0',//每页条数
        'pageNum' => 'IntGt:0', //页码
    ];

    //付款明细-删除
    public static $validate_detail_del = [
        'id' => 'Required|IntGt:0',
        'detail_id' => 'IntGe:0'
    ];

    //新增/编辑-草稿/提交
    public static $validate_add = [
        'id' => 'Required|IntGt:0',
        'is_submit' => 'Required|IntIn:0,1',
        'cost_company_id' => 'IfIntEq:is_submit,1|Required|IntGt:0',
        'cost_company_name' => 'IfIntEq:is_submit,1|Required|StrLenGeLe:1,50',
        'cost_sys_department_id' => 'IfIntEq:is_submit,1|Required|IntGt:0',
        'cost_department_name' => 'IfIntEq:is_submit,1|Required|StrLenGeLe:1,50',
        'cost_store_type' => 'IfIntEq:is_submit,1|Required|IntIn:' . Enums::PAYMENT_COST_STORE_TYPE_01 . ',' . Enums::PAYMENT_COST_STORE_TYPE_02,
        'mark' => 'StrLenGeLe:0,500',
        'pay_method' => 'IfIntEq:is_submit,1|Required|IntIn:' . Enums::PAYMENT_METHOD_CASH . ',' . Enums::PAYMENT_METHOD_BANK_TRANSFER . ',' . Enums::PAYMENT_METHOD_CHECK,
        'currency' => 'IfIntEq:is_submit,1|Required|IntGe:0',
        'cost_type' => 'IfIntEq:is_submit,1|Required|IntGt:0',
        'attachment_arr' => 'ArrLenGeLe:0,20',
        'attachment_arr[*]' => 'Obj',
        'attachment_arr[*].bucket_name'=>'Required|StrLenGeLe:1,63',
        'attachment_arr[*].object_key'=>'Required|StrLenGeLe:1,100',
        'attachment_arr[*].file_name'=>'Required|StrLenGeLe:1,200',
        'amount_total_no_tax' => 'IfIntEq:is_submit,1|Required|FloatGe:0',
        'amount_total_vat' => 'IfIntEq:is_submit,1|Required|FloatGe:0',
        'amount_total_wht' => 'IfIntEq:is_submit,1|Required|FloatGe:0',
        'amount_total_actually' => 'IfIntEq:is_submit,1|Required|FloatGe:0',
    ];

    /**
     * 代理支付默认配置项
     * @return array
     */
    public function getOptionsDefault()
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            //申请状态、支付状态、付款方式、币种、费用总部/网点、费用类型
            $enums = [
                'status' => AgencyPaymentEnums::$agency_payment_status,
                'pay_status' => AgencyPaymentEnums::$agency_payment_pay_status,
                'pay_method' => GlobalEnums::$payment_method_item,
                'currency' => GlobalEnums::$currency_item,
                'cost_store_type' => Enums::$payment_cost_store_type,
                'cost_type' => self::getCostTypeEnums(),
            ];
            foreach ($enums as $key => $item) {
                foreach ($item as $k => $v) {
                    $data[$key][] = [
                        'value' => (string)$k,
                        'label' => static::$t->_($v),
                    ];
                }
            }
            //费用所属公司
            $data['cost_company'] = (new PurchaseService())->getCooCostCompany();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取代理支付默认配置项异常信息: ' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取代理支付-费用部门列表
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getCostSysDepartmentList($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            // 是否允许自由选择费用所属部门
            $data['is_free_change'] = false;
            // 可自由选择全量费用所属部门的工号配置
            $change_cost_staff_ids = EnumsService::getInstance()->getSettingEnvValueIds('agency_payment_change_cost_department_staff_ids');
            // 申请人 隶属 上述配置时, 可自由选择费用所属部门
            if (in_array($user['id'], $change_cost_staff_ids)) {
                $data['is_free_change'] = true;
                //可以选择所有
                $data['department_list'] = StaffService::getInstance()->departmentList();
            } else {
                //产品要求参考报销逻辑获取费用部门列表
                $department_id = empty($user['node_department_id']) ? $user['sys_department_id'] : $user['node_department_id'];
                $data['department_list'] = ListService::getInstance()->getMyDeptList(['id' => $user['id'], 'department_id' => $department_id]);
            }
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning('获取代理支付-费用部门列表异常信息: ' . $e->getMessage());
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 列表
     * @param array $condition 参数组
     * @param array $user 登陆者信息组
     * @param int $type 列表来源
     * @return array
     */
    public function getList($condition, $user, $type = self::LIST_TYPE_APPLY)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $count = 0;
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //我的审核-待处理
            $list_type = $condition['type'] ?? AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE;
            if ($type == self::LIST_TYPE_AUDIT && $list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE) {
                $other_params = $this->getWorkflowNo($condition, $user);
                if ($other_params['total_count'] && $other_params['workflow_no']) {
                    $condition['workflow_no'] = $other_params['workflow_no'];
                    $count = $other_params['total_count'];
                } else {
                    return [
                        'code' => $code,
                        'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
                        'data' => $data,
                    ];
                }
            }

            //获取列表
            $count = $count ? $count : $this->getListCount($condition, $user, $type);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('main.id, main.batch_no, main.apply_id, main.apply_name, main.cost_company_name, main.apply_date, main.cost_type, main.amount_total_actually, main.status, main.pay_status, main.currency');
                $builder->from(['main' => AgencyPaymentModel::class]);
                //组合搜索条件
                $builder = $this->getCondition($builder, $condition, $user, $type);
                if (!($type == self::LIST_TYPE_AUDIT && $list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE)) {
                    $builder->limit($page_size, $offset);
                }
                $builder->orderby('main.id desc');
                $builder->groupBy('main.id');
                $items = $builder->getQuery()->execute()->toArray();
                $items = $this->handleListItems($items);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-agency-payment-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取特定条件下的总数
     * @param array $condition 筛选条件组
     * @param array $user 登陆者信息组
     * @param int $type 列表来源
     * @param bool $is_export 是否是导出
     * @return int
     */
    public function getListCount($condition, $user, $type, $is_export = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['main' => AgencyPaymentModel::class]);
        $columns = $is_export ? 'count(main.id) AS count' : 'count(DISTINCT main.id) AS count';
        $builder->columns($columns);
        $builder = $this->getCondition($builder, $condition, $user, $type, $is_export);
        return (int)$builder->getQuery()->getSingleResult()->count;
    }

    /**
     * 组装查询条件
     * @param object $builder 查询器
     * @param array $condition 查询条件
     * @param array $user 登陆者信息组
     * @param int $type 列表来源
     * @param bool $is_export 是否是导出
     * @return mixed
     */
    public function getCondition($builder, $condition, $user, $type, $is_export = false)
    {
        $user_id = $user['id'] ?? 0;//申请人或审核人
        $batch_id = $condition['id'] ?? 0;//批次id
        $batch_no = $condition['batch_no'] ?? ''; //结算批次
        $status = $condition['status'] ?? [];//申请状态
        $pay_status = $condition['pay_status'] ?? [];//支付状态
        $apply_date_start = $condition['apply_date_start'] ?? '';//申请日期起始
        $apply_date_end = $condition['apply_date_end'] ?? '';//申请日期截止
        $cost_company_id = $condition['cost_company_id'] ?? [];//费用所属公司
        $cost_type = $condition['cost_type'] ?? [];//费用类型
        $out_no = $condition['out_no'] ?? '';//外部结算号
        $payee_name = $condition['payee_name'] ?? '';//收款人
        $list_type = $condition['type'] ?? AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE;
        $workflow_no = $condition['workflow_no'] ?? [];//by审批流-流水号
        if (!empty($batch_id)) {
            $builder->andWhere('main.id = :batch_id:', ['batch_id' => $batch_id]);
        }
        //区分不同入口过来的列表基础查询条件
        if ($type == self::LIST_TYPE_APPLY) {
            //我的申请-需要按申请人查询
            $builder->andWhere('main.apply_id = :apply_id:', ['apply_id' => $user_id]);
            //非暂存的
            $builder->andWhere('main.status > :status: OR (main.status = :status: and main.source_type = :source_type:)', ['status' => AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT, 'source_type' => AgencyPaymentEnums::SOURCE_TYPE_ADD]);
        } elseif ($type == self::LIST_TYPE_AUDIT) {
            //我的审核-需要按照审批人查询
            if ($list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE) {
                //待处理 - 待审核
                $builder->andWhere('main.status = :status:', ['status' => Enums::WF_STATE_PENDING]);
                if (!empty($workflow_no)) {
                    $builder->inWhere('main.workflow_no', $workflow_no);
                }
            } elseif ($list_type == AgencyPaymentEnums::LIST_TYPE_HAD_HANDLE) {
                //已处理 - 驳回、通过
                $builder->leftJoin(ByWorkflowAuditLogModel::class, 'main.id = audit.biz_value', 'audit');
                $builder->andWhere('audit.approval_id = :approval_id:', ['approval_id' => $user_id]);
                $builder->andWhere('audit.biz_type = :biz_type:', ['biz_type' => Enums::WF_AGENCY_PAYMENT_BIZ_TYPE]);
                $builder->inWhere('audit.status', [Enums::WF_STATE_REJECTED, Enums::WF_STATE_APPROVED]);
            }
        } elseif ($type == self::LIST_TYPE_PAY) {
            //代理支付 && 已通过 && 非流进支付模块的
            $builder->andWhere('main.status = :status: and main.is_pay_module = :is_pay_module:', ['status' => Enums::WF_STATE_APPROVED, 'is_pay_module' => PayEnums::BIZ_DATA_IS_PAY_MODULE_NO]);
            if ($list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE) {
                //待处理 - 待支付、支付中
                $builder->inWhere('main.pay_status ', [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING]);
            } elseif ($list_type == AgencyPaymentEnums::LIST_TYPE_HAD_HANDLE) {
                //已处理 - 已支付、未支付、部分支付
                $builder->inWhere('main.pay_status ', [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_DEPART]);
            }
        } elseif ($type == self::LIST_TYPE_DATA) {
            //非暂存的
            $builder->andWhere('main.status > :status: OR (main.status = :status: and main.source_type = :source_type:)', ['status' => AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT, 'source_type' => AgencyPaymentEnums::SOURCE_TYPE_ADD]);
        }

        //结算批次
        if (!empty($batch_no)) {
            $builder->andWhere('main.batch_no LIKE :batch_no:', ['batch_no' => '%' . $batch_no . '%']);
        }
        //申请状态
        if (!empty($status)) {
            $builder->inWhere('main.status', $status);
        }
        //支付状态
        if (!empty($pay_status)) {
            $builder->inWhere('main.pay_status', $pay_status);
        }
        //申请日期
        if (!empty($apply_date_start)) {
            $builder->andWhere('main.apply_date >= :apply_date_start:', ['apply_date_start' => $apply_date_start]);
        }
        if (!empty($apply_date_end)) {
            $builder->andWhere('main.apply_date <= :apply_date_end:', ['apply_date_end' => $apply_date_end]);
        }
        //费用所属公司
        if (!empty($cost_company_id)) {
            $builder->inWhere('main.cost_company_id', $cost_company_id);
        }
        //费用类型
        if (!empty($cost_type)) {
            $builder->inWhere('main.cost_type', $cost_type);
        }

        //如果按照明细行搜索需要关联明细表
        if (!empty($out_no) || !empty($payee_name) || $is_export) {
            $builder->leftJoin(AgencyPaymentDetailModel::class, 'detail.agency_payment_id = main.id', 'detail');
            if ($out_no) {
                $builder->andWhere('detail.out_no = :out_no:', ['out_no' => $out_no]);
            }
            if ($payee_name) {
                $builder->andWhere('detail.payee_staff_id LIKE :payee_name: or detail.bank_account_name LIKE :payee_name:', ['payee_name' => '%' . $payee_name . '%']);
            }
        }
        return $builder;
    }

    /**
     * 获取审核人名下待审核记录总数以及by审批流-流水号组
     * @param array $condition 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function getWorkflowNo($condition, $user)
    {
        //第一步在oa库查特定条件下的workflow_no号列
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('main.workflow_no');
        $builder->from(['main' => AgencyPaymentModel::class]);
        $builder = $this->getCondition($builder, $condition, $user, self::LIST_TYPE_AUDIT);
        $search_list = $builder->getQuery()->execute()->toArray();

        // 第二步调取by接口获取当前登陆人要审批的审批流水号组
        $total_count = 0;
        $workflow_no = [];
        if (!empty($search_list)) {
            $page_size = empty($condition['pageSize']) ? MaterialEnums::PAGE_SIZE : $condition['pageSize'];
            $page_num  = empty($condition['pageNum']) ? MaterialEnums::PAGE_NUM : $condition['pageNum'];
            $workflow_no = array_column($search_list, 'workflow_no');
            $by_list_params = [
                'serial_no' => $workflow_no,
                'biz_type' => [ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT],
                'approval_id' => $user['id'],
                'state' => [ByWorkflowEnums::BY_OPERATE_WAIT_AUDIT],
                'page_num' => $page_num,
                'page_size' => $page_size,
            ];
            $result = (new ByWorkflowService())->getList($by_list_params);
            $total_count = !empty($result['total_count']) ? $result['total_count'] : 0;
            if (!empty($result['list'])) {
                //第三步查询到该审批人明细存在要审批的数据，关联上业务单据然后展示
                $workflow_no = array_values(array_column($result['list'], 'serial_no'));
            }
        }
        return [
            'total_count' => $total_count,
            'workflow_no' => $workflow_no
        ];
    }

    /**
     * 格式化列表
     * @param array $items 列表
     * @param bool $is_export 是否是导出
     * @return array
     */
    private function handleListItems($items, $is_export = false)
    {
        if (empty($items)) {
            return $items;
        }

        //申请状态
        $agency_payment_status = AgencyPaymentEnums::$agency_payment_status;
        //支付状态
        $agency_payment_pay_status = AgencyPaymentEnums::$agency_payment_pay_status;
        //费用类型
        $cost_type_enums = self::getCostTypeEnums();
        if ($is_export) {
            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            //费用一级部门
            $cost_sys_department_ids = array_values(array_unique(array_filter(array_column($items, 'cost_sys_department_id'))));
            $department_list = (new DepartmentRepository())->getDepartmentByIds($cost_sys_department_ids, 2);
            $row_values = [];
            foreach ($items as $item) {
                $row_values[] = [
                    $item['batch_no'],
                    $item['apply_id'],
                    $item['apply_name'],
                    $item['apply_date'],
                    static::$t->_($agency_payment_status[$item['status']] ?? ''),
                    static::$t->_($agency_payment_pay_status[$item['pay_status']] ?? ''),
                    $item['cost_company_name'],
                    static::$t->_($cost_type_enums[$item['cost_type']] ?? ''),
                    self::$t->_(GlobalEnums::$currency_item[$item['currency']] ?? ''),
                    $item['no'],
                    $item['payee_staff_id'],
                    $item['bank_account_name'],
                    $item['bank_name'],
                    $item['bank_account'],
                    $item['cost_start_date'],
                    $item['cost_end_date'],
                    $item['due_date'],
                    number_format($item['amount_no_tax'], 2),
                    number_format($item['amount_total_vat'], 2),
                    number_format($item['payable_amount'], 2),
                    $wht_cat_map[$item['wht_category']] ?? '',
                    number_format($item['amount_total_wht'], 2),
                    number_format($item['amount_total_actually'], 2),
                    static::$t->_($agency_payment_pay_status[$item['detail_pay_status']] ?? ''),
                    $item['remark'],
                    $item['out_no'],
                    $item['payee_id_no'],//收款人证件号码
                    $item['payee_mobile'],//收款人联系电话
                    $item['payee_address'],//收款人居住地址
                    $item['cost_department_name'],//费用部门名称
                    $department_list && isset($department_list[$item['cost_sys_department_id']]) ? $department_list[$item['cost_sys_department_id']]['name'] : '',//费用一级部门
                    $item['cost_store_name'],//费用网点
                    $item['cost_center_code'],//成本中心
                    static::$t->_(PayEnums::$payment_is_pay_key[$item['is_pay']] ?? ''),
                    $item['pay_bk_name'],
                    $item['pay_bk_account'],
                    $item['pay_bank_flow_date'] ? substr($item['pay_bank_flow_date'], 0 ,10) : '',
                    $item['pay_remark'],
                ];
            }
            $items = $row_values;
        } else {
            foreach ($items as &$item) {
                $item['status_text'] = static::$t->_($agency_payment_status[$item['status']] ?? '');
                $item['pay_status_text'] = static::$t->_($agency_payment_pay_status[$item['pay_status']] ?? '');
                $item['cost_type_text'] = static::$t->_($cost_type_enums[$item['cost_type']] ?? '');
                $item['amount_total_actually'] = number_format($item['amount_total_actually'], 2);
                $item['currency_text'] = static::$t->_(GlobalEnums::$currency_item[$item['currency']] ?? '');
            }
        }
        return $items;
    }

    /**
     * 查看-付款明细-导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者
     * @param int $export_type 导出类型
     * @param int $type 列表类型
     * @return array|mixed
     * @throws GuzzleException
     */
    public function listExport($params, $user, $export_type = DownloadCenterEnum::AGENCY_PAYMENT_APPLY, $type = self::LIST_TYPE_APPLY)
    {
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDataExportTotal($params, $user, $type) > AgencyPaymentEnums::DOWNLOAD_LIMIT) {
            $result = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], $export_type, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url' => ''
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $result = $this->getSyncExportData($params, $user, $type);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url' => $result['data']
            ];
        }
        return $result;
    }

    /**
     * 获取导出数据的总量
     * @param array $condition 请求参数组
     * @param array $user 用户信息组
     * @param int $type 导出来源
     * @return mixed
     */
    public function getDataExportTotal($condition, $user, $type = self::LIST_TYPE_APPLY)
    {
        $total_count = 0;
        try {
            $condition['user_id'] = $user['id'];
            //我的审核-待处理
            $list_type = $condition['type'] ?? AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE;
            if ($type == self::LIST_TYPE_AUDIT && $list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE) {
                $other_params = $this->getWorkflowNo($condition, $user);
                if ($other_params['total_count'] && $other_params['workflow_no']) {
                    $condition['workflow_no'] = $other_params['workflow_no'];
                } else {
                    return $total_count;
                }
            }
            $total_count = $this->getListCount($condition, $user, $type, true);
        } catch (\Exception $e) {
            $this->logger->error('代理支付-获取导出数据的总量异常:' . $e->getMessage());
        }

        return $total_count;
    }

    /**
     * 数据同步导出
     * @param array $condition 请求参数组
     * @param array $user 用户信息组
     * @param int $type 导出来源
     * @return array
     * @throws GuzzleException
     */
    public function getSyncExportData($condition, $user, $type = self::LIST_TYPE_APPLY)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = '';

        try {
            // 同步下载, 最多1w条
            $condition['pageNum'] = GlobalEnums::DEFAULT_PAGE_NUM;
            $condition['pageSize'] = AgencyPaymentEnums::DOWNLOAD_LIMIT;

            // 获取数据
            $excel_data = $this->getExportData($condition, $user, $type);

            // 获取表头
            $header = $this->getExportExcelHeaderFields();

            // 生成Excel
            $file_name = 'Agency_payment_' . date('YmdHis') . '.xlsx';
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code    = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('代理支付-数据同步导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }

    /**
     * 获取列表导出数据
     * @param array $condition 参数组
     * @param array $user 登陆者信息组
     * @param int $type 列表来源
     * @return array
     */
    public function getExportData($condition, $user, $type)
    {
        $page_size = empty($condition['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $condition['pageSize'];
        $page_num = empty($condition['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $condition['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $items = [];
        //我的审核-待处理
        $list_type = $condition['type'] ?? AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE;
        if ($type == self::LIST_TYPE_AUDIT && $list_type == AgencyPaymentEnums::LIST_TYPE_WAIT_HANDLE) {
            $other_params = $this->getWorkflowNo($condition, $user);
            if ($other_params['total_count'] && $other_params['workflow_no']) {
                $condition['workflow_no'] = $other_params['workflow_no'];
            } else {
                return $items;
            }
        }

        //获取导出列表
        $builder = $this->modelsManager->createBuilder();
        $columns = 'main.batch_no, main.apply_id, main.apply_name, main.apply_date, main.status, main.pay_status, main.cost_company_name, main.cost_type, main.currency, detail.no, ';
        $columns .= 'detail.payee_staff_id, detail.bank_account_name, detail.bank_name, detail.bank_account, detail.cost_start_date, detail.cost_end_date, detail.due_date, detail.amount_no_tax, detail.amount_total_vat, detail.payable_amount,';
        $columns .= 'detail.wht_category, detail.amount_total_wht, detail.amount_total_actually, detail.pay_status AS detail_pay_status, detail.remark, detail.out_no, detail.payee_id_no, detail.payee_mobile, detail.payee_address, detail.is_pay, detail.pay_bk_name, detail.pay_bk_account, detail.pay_bank_flow_date, detail.pay_remark,';
        $columns .= 'detail.cost_department_name, detail.cost_sys_department_id, detail.cost_store_name, detail.cost_center_code';
        $builder->columns($columns);
        $builder->from(['main' => AgencyPaymentModel::class]);
        //组合搜索条件
        $builder = $this->getCondition($builder, $condition, $user, $type, true);
        $builder->limit($page_size, $offset);
        $builder->orderby('main.id desc');
        $items = $builder->getQuery()->execute()->toArray();
        return $this->handleListItems($items, true);
    }

    /**
     * 获取代理支付导出的Excel表头
     * @return array
     */
    public function getExportExcelHeaderFields()
    {
        return [
            static::$t->_('agency_payment.batch_no'),//结算批次号
            static::$t->_('csr_field_create_id'),//申请人工号
            static::$t->_('csr_field_create_name'),//申请人姓名
            static::$t->_('global.apply.date'),//申请日期
            static::$t->_('global.apply.status.text'),//申请状态
            static::$t->_('global_pay_status'),//支付状态
            static::$t->_('expense_company'),//费用所属公司
            static::$t->_('expense_type'), //费用类型
            static::$t->_('re_field_currency_text'), //币种
            static::$t->_('agency_payment.detail_no'),//OA唯一号
            static::$t->_('agency_payment.detail_payee_staff_id'),//收款人ID
            static::$t->_('agency_payment.bank_account_name'),//收款人姓名
            static::$t->_('agency_payment.detail_bank_name'),//收款人银行
            static::$t->_('pay_field_bank_account'),//收款账号
            static::$t->_('payment_store_renting_cost_start_date'),//费用开始日期
            static::$t->_('payment_store_renting_cost_end_date'),//费用结束日期
            static::$t->_('re_filed_should_pay_date'),//应付日期
            static::$t->_('csr_field_no_tax'),//不含税金额
            static::$t->_('purchase_order_field_vat7'),//vat税额
            static::$t->_('agency_payment.payable_amount'),//应付金额
            static::$t->_('re_field_wht_type'),//WHT类别
            static::$t->_('re_field_wht_tax_amount'),//WHT税额
            static::$t->_('csr_field_detail_real_amount'),//实付金额
            static::$t->_('agency_payment.detail_pay_status'), //行支付状态
            static::$t->_('agency_payment.detail_remark'), //行备注
            static::$t->_('agency_payment.detail_out_no'), //外部单号
            static::$t->_('agency_payment.payee_id_no'), //收款人证件号码
            static::$t->_('agency_payment.payee_mobile'), //收款人联系电话
            static::$t->_('agency_payment.payee_address'), //收款人居住地址
            static::$t->_('global.cost.department.name'), //费用部门
            static::$t->_('re_filed_apply_cost_first_department'),//费用一级部门
            static::$t->_('global.cost.store.name'), //费用网点
            static::$t->_('sap_cost_center'), //成本中心
            static::$t->_('payment_export_is_pay'),//是否支付
            static::$t->_('payment_export_pay_bank_name'),//支付银行
            static::$t->_('agency_payment.detail_pay_bk_name'),//支付账号
            static::$t->_('purchase_payment_field_real_pay_at'),//银行流水日期
            static::$t->_('payment_export_not_pay_reason'),//未支付原因
        ];
    }

    /**
     * 获取代理支付批次信息
     * @param integer $id ID
     * @return mixed
     * @throws ValidationException
     */
    public function getAgencyPaymentInfoById($id)
    {
        $agency_payment_info = AgencyPaymentModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        if (empty($agency_payment_info)) {
            throw new ValidationException(static::$t->_('agency_payment_not_exists'), ErrCode::$VALIDATE_ERROR);
        }
        return $agency_payment_info;
    }

    /**
     * 检测操作的但据是否是自己的
     * @param integer $id ID
     * @param array $user 登陆者信息组
     * @return mixed
     * @throws ValidationException
     */
    private function checkIsSelf($id, $user)
    {
        $agency_payment_info = $this->getAgencyPaymentInfoById($id);
        if ($user && $agency_payment_info->apply_id != $user['id']) {
            throw new ValidationException(static::$t->_('agency_payment_permission_error'), ErrCode::$VALIDATE_ERROR);
        }
        return $agency_payment_info;
    }

    /**
     * 撤回
     * @param array $params 请求参数组
     * @param array $user 登陆者信息组
     * @return array
     */
    public function cancel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_info = $this->checkIsSelf($params['id'], $user);
            if ($agency_payment_info->status != Enums::WF_STATE_PENDING) {
                throw new ValidationException(static::$t->_('agency_payment_status_not_pending'), ErrCode::$VALIDATE_ERROR);
            }

            //调取by接口，by撤销成功，记录撤回信息
            $by_workflow = new ByWorkflowService();
            $by_workflow->audit([
                'serial_no' => $agency_payment_info->workflow_no,
                'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
                'reason' => $params['reason'],
                'status' => ByWorkflowEnums::BY_OPERATE_CANCEL,
                'operator_id' => $user['id'],
            ]);

            //单据撤回
            $date = date('Y-m-d H:i:s');
            $agency_payment_info->status = Enums::WF_STATE_CANCEL;
            $agency_payment_info->pay_status = AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY;
            $agency_payment_info->reason = $params['reason'];
            $agency_payment_info->canceled_at = $date;
            $agency_payment_info->updated_at = $date;
            $bool = $agency_payment_info->save();
            if ($bool === false) {
                throw new BusinessException('代理支付-我的申请-撤回代理支付批次失败 = ' . json_encode(['id' => $params['id'], 'user_id' => $user['id']], JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }

            //明细行的支付状态修改为未支付
            $all_update_success = $db->updateAsDict(
                (new AgencyPaymentDetailModel())->getSource(),
                [
                    'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY,
                    'is_pay' => AgencyPaymentEnums::IS_PAY_NO,
                    'updated_at' => $date
                ],
                [
                    'conditions' => "agency_payment_id = {$params['id']}",
                ]
            );
            if ($all_update_success === false) {
                throw new BusinessException('代理支付-我的申请-撤回-更改明细行支付状态为未支付失败：'. json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            $db->rollback();
        }
        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-cancel-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool,
        ];
    }

    /**
     * 删除
     * @param integer $id ID
     * @param array $user 登陆者信息组
     * @param int $type 删除来源
     * @return array
     */
    public function delete($id, $user, $type = self::LIST_TYPE_APPLY)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_info = $this->getAgencyPaymentInfoById($id);
            //我的申请-删除-只能操作自己的
            if ($agency_payment_info->apply_id != $user['id'] && $type == self::LIST_TYPE_APPLY) {
                throw new ValidationException(static::$t->_('agency_payment_permission_error'), ErrCode::$VALIDATE_ERROR);
            }
            if ($agency_payment_info->status != AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT) {
                throw new ValidationException(static::$t->_('agency_payment_status_not_wait_submit'), ErrCode::$VALIDATE_ERROR);
            }
            //数据查询-删除-只有有权限的人才可删除
            if ($type == self::LIST_TYPE_DATA && !in_array($user['id'], EnumsService::getInstance()->getSettingEnvValueIds('store_access_staff_id'))) {
                throw new ValidationException(static::$t->_('no_access_del_vendor'), ErrCode::$VALIDATE_ERROR);
            }
            $this->delDb($agency_payment_info);
            //事物提交
            $db->commit();
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-delete-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool,
        ];
    }

    /**
     * 删除批次库操作
     * @param object $agency_payment_info 支付批次对象信息
     * @return mixed
     * @throws BusinessException
     */
    private function delDb($agency_payment_info)
    {
        $bool = $agency_payment_info->delete();
        if ($bool === false) {
            throw new BusinessException('代理支付-我的申请-删除代理支付批次失败 = ' . json_encode(['id' => $agency_payment_info->id], JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
        }
        //删除付款明细
        $bool = $agency_payment_info->getDetails()->delete();
        if ($bool === false) {
            throw new BusinessException('代理支付-我的申请-删除代理支付明细失败 = ' . json_encode(['id' => $agency_payment_info->id], JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
        }
        //删除附件
        $bool = $agency_payment_info->getAttachments()->delete();
        if ($bool === false) {
            throw new BusinessException('代理支付-我的申请-删除代理支付明细失败 = ' . json_encode(['id' => $agency_payment_info->id], JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
        }
        return $bool;
    }

    /**
     * 补充附件
     * @param array $params 请求参数组
     * @param array $user 登陆者信息组
     * @param int $type 删除来源
     * @return array
     */
    public function attachment($params, $user, $type = self::LIST_TYPE_APPLY)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        try {
            $id = $params['id'];
            $agency_payment_info = $this->getAgencyPaymentInfoById($id);
            //我的申请-只能操作自己的
            if ($agency_payment_info->apply_id != $user['id'] && $type == self::LIST_TYPE_APPLY) {
                throw new ValidationException(static::$t->_('agency_payment_permission_error'), ErrCode::$VALIDATE_ERROR);
            }
            //待提交、已驳回、已撤回 || 已通过未支付 不可补充附件
            if (in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT, Enums::WF_STATE_REJECTED, Enums::WF_STATE_CANCEL]) || ($agency_payment_info->status == Enums::WF_STATE_APPROVED && $agency_payment_info->pay_status == AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_NOTPAY)) {
                throw new ValidationException(static::$t->_('agency_payment_attachment_invalid'), ErrCode::$VALIDATE_ERROR);
            }
            //删除原有附件
            $bool = SysAttachmentModel::find([
                'conditions' => 'oss_bucket_type = :oss_bucket_type: and deleted = :deleted: and oss_bucket_key = :oss_bucket_key:',
                'bind' => ['oss_bucket_type' => Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_SUPPLEMENT_ATTACHMENT, 'deleted' => GlobalEnums::IS_NO_DELETED, 'oss_bucket_key' => $id]
            ])->delete();
            if ($bool === false) {
                throw new BusinessException('代理支付-补充附件-删除原附件失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
            $attach_arr = [];
            foreach ($params['attachment_arr'] as $k => $file) {
                $tmp = [];
                $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_SUPPLEMENT_ATTACHMENT;
                $tmp['oss_bucket_key'] = $id;
                $tmp['sub_type'] = 0;
                $tmp['bucket_name'] = $file['bucket_name'];
                $tmp['object_key'] = $file['object_key'];
                $tmp['file_name'] = $file['file_name'];
                $tmp['created_at'] = date('Y-m-d H:i:s');
                $attach_arr[] = $tmp;
            }
            // 新增附件
            if ($attach_arr) {
                $attach = new SysAttachmentModel();
                $bool = $attach->batch_insert($attach_arr);
                if ($bool === false) {
                    throw new BusinessException('代理支付-补充附件添加失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . '---' . $e->getLine() . '---' . $e->getTraceAsString();
        }

        if (!empty($real_message)) {
            $this->logger->warning('代理支付-补充附件:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $bool,
        ];
    }

    /**
     * 获取批次下的付款明细总数
     * @param integer $id 批次id
     * @return mixed
     */
    public function getDetailCountById($id)
    {
        return AgencyPaymentDetailModel::count([
            'conditions' => 'agency_payment_id = :agency_payment_id:',
            'bind' => ['agency_payment_id' =>$id]
        ]);
    }

    /**
     * 详情
     * @param array $params 请求参数组
     * @param array $user 登陆者信息组
     * @param int $type 详情来源
     * @return array
     */
    public function detail($params, $user, $type = self::LIST_TYPE_APPLY)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $detail = [];
        try {
            $agency_payment_info = $this->getAgencyPaymentInfoById($params['id']);
            //我的申请-只可查看自己的
            if ($agency_payment_info->apply_id != $user['id'] && $type == self::LIST_TYPE_APPLY) {
                throw new ValidationException(static::$t->_('agency_payment_permission_error'), ErrCode::$VALIDATE_ERROR);
            }

            $detail = $agency_payment_info->toArray();
            $detail['apply_department_id'] = $detail['apply_department_id'] ? $detail['apply_department_id'] : '';
            $detail['cost_company_id'] = $detail['cost_company_id'] ? $detail['cost_company_id'] : '';
            $detail['pay_method'] = $detail['pay_method'] ? $detail['pay_method'] : '';
            $detail['cost_type'] = $detail['cost_type'] ? $detail['cost_type'] : '';
            $detail['cost_store_type'] = $detail['cost_store_type'] ? $detail['cost_store_type'] : '';
            $detail['cost_sys_department_id'] = $detail['cost_sys_department_id'] ? $detail['cost_sys_department_id'] : '';
            $detail['currency'] = $detail['currency'] ? $detail['currency'] : '';
            $detail['cost_store_type_text'] = static::$t->_(Enums::$payment_cost_store_type[$detail['cost_store_type']]);//费用总部/网点
            $detail['pay_method_text'] = static::$t->_(GlobalEnums::$payment_method_item[$detail['pay_method']] ?? ''); // 付款方式
            $detail['currency_text'] = self::$t->_(GlobalEnums::$currency_item[$detail['currency']] ?? '');//币种
            $detail['cost_type_text'] = static::$t->_(self::getCostTypeEnums()[$detail['cost_type']] ?? '');//费用类型
            $detail['detail_count'] = $this->getDetailCountById($detail['id']);
            //附件信息
            $attachment_list = $agency_payment_info->getAttachments()->toArray();
            foreach ($attachment_list as $file) {
                if ($file['oss_bucket_type'] == Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_ATTACHMENT) {
                    //附件
                    $detail['attachment_arr'][] = $file;
                } elseif ($file['oss_bucket_type'] == Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_SUPPLEMENT_ATTACHMENT) {
                    //补充附件
                    $detail['attachment_supplement_list'][] = $file;
                }
            }
            $detail['auth_logs'] = $this->getAuditLogs($detail, $user);
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $detail,
        ];
    }

    /**
     * 付款明细
     * @param array $params 请求参数组
     * @return array
     */
    public function detailList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $offset = $page_size * ($page_num - 1);
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //获取列表
            $agency_payment_id = $params['id'];
            $count = $this->getDetailCountById($agency_payment_id);
            if ($count > 0) {
                $builder = $this->modelsManager->createBuilder();
                $builder->columns('*');
                $builder->from(AgencyPaymentDetailModel::class);
                //组合搜索条件
                $builder->where('agency_payment_id = :agency_payment_id:', ['agency_payment_id' => $agency_payment_id]);
                $builder->limit($page_size, $offset);
                $items = $builder->getQuery()->execute()->toArray();
            }
            $data['items'] = $items ?? [];
            $wht_cat_map = EnumsService::getInstance()->getWhtRateCategoryMap(0);
            foreach ($data['items'] as &$item) {
                $item['wht_category_text'] = $wht_cat_map[$item['wht_category']] ?? 0;
                $item['pay_status_text'] = static::$t->_(AgencyPaymentEnums::$agency_payment_pay_status[$item['pay_status']] ?? '');
            }
            $data['pagination']['total_count'] = $count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-agency-payment-detail-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取审批日志
     * @param array $detail 详情
     * @param array $user 当前登陆者信息组
     * @return array
     */
    private function getAuditLogs($detail, $user)
    {
        //待提交单据无审批日志
        if ($detail['status'] == AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT) {
            return [];
        }
        //调取by获取审批流日志
        $auth_logs = (new ByWorkflowService())->log(['serial_no' => $detail['workflow_no'] , 'operator_id' => $user['id'], 'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT]);

        //未进入支付模块的单据需要拼接支付审批日志 && 审批通过的单据
        if ($detail['is_pay_module'] == PayEnums::BIZ_DATA_IS_PAY_MODULE_NO && $detail['status'] == Enums::WF_STATE_APPROVED) {
            //待支付，支付中
            if (in_array($detail['pay_status'], [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_ING])) {
                $pay_status = Enums::PAYMENT_PAY_STATUS_PENDING;
                //获取配置的支付人
                $pay_staff_id = self::getPayStaffs();
            } else {
                //已支付，部分支付，未支付
                $pay_status = in_array($detail['pay_status'], [AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PAY, AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_DEPART]) ? Enums::PAYMENT_PAY_STATUS_PAY : Enums::PAYMENT_PAY_STATUS_NOTPAY;
                //最终支付人
                if ($detail['pay_staff_id']) {
                    $pay_staff_id = [$detail['pay_staff_id']];
                } else {
                    //若最终支付人是空的则意味着是脚本异步变更的头支付状态，非人工点击，产品要求从明细行里取更新状态最新的一条数据的支付人为最终支付人
                    $last_pay_info = AgencyPaymentDetailModel::findFirst([
                        'conditions' => 'agency_payment_id = :agency_payment_id: and pay_staff_id > :pay_staff_id:',
                        'bind' => ['agency_payment_id' => $detail['id'], 'pay_staff_id' => 0],
                        'order' => 'updated_at DESC'
                    ]);
                    $pay_staff_id = $last_pay_info ? [$last_pay_info->pay_staff_id] : [];
                }
            }
            $pay_status_text = static::$t->_(Enums::$payment_pay_status[$pay_status]);

            //存在支付人信息，开始拼接支付信息到审批流日志里
            if ($pay_staff_id) {
                $pay_logs = [
                    'state_txt' => $pay_status_text,
                    'status_code' => '',
                    'action_time' => $detail['pay_at'] ?? '',
                    'approval_type' => 0,
                    'process_state' => 1
                ];
                $us = new UserService();
                foreach ($pay_staff_id as $staff_id) {
                    $one_user_info = $us->getUserById($staff_id);
                    if (!empty($one_user_info)) {
                        $pay_logs['approval_info'][] = [
                            'staff_id' => $staff_id,
                            'staff_name' => $this->getNameAndNickName($one_user_info->name, $current->nick_name ?? ''),
                            'position' => $one_user_info->getJobTitle()->name ?? '',
                            'department' => $one_user_info->getDepartment()->name ?? '',
                            'sort' => 20,
                            'state_txt' => $pay_status_text,
                            'status_code' => '',
                            'audit_info' => ''
                        ];
                    }
                }
                array_push($auth_logs['stream'], $pay_logs);
            }
        }
        return $auth_logs;
    }

    /**
     * 查看-付款明细-导出
     * @param array $params 请求参数组
     * @param array $user 当前登陆者
     * @param int $export_type 导出类型
     * @return array|mixed
     * @throws GuzzleException
     */
    public function detailExport($params, $user, $export_type = DownloadCenterEnum::AGENCY_PAYMENT_APPLY_DETAIL_EXPORT)
    {
        // 大于指定数量, 添加异步任务 导出
        if ($this->getDetailCountById($params['id']) > AgencyPaymentEnums::DOWNLOAD_LIMIT) {
            $result = DownloadCenterService::getInstance()->addDownloadCenter($user['id'], $export_type, $params);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_ASYNC,
                'file_url' => ''
            ];
        } else {
            // 小于等于指定数量, 同步导出
            $result = $this->getSyncExportData($params, $user, 0);
            $result['data'] = [
                'export_method' => DownloadCenterEnum::EXPORT_METHOD_SYNC,
                'file_url' => $result['data']
            ];
        }
        return $result;
    }

    /**
     * 暂存（系统性）
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function stagingPayment($user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $staging_data = [];
        try {
            $agency_payment_model = new AgencyPaymentModel();
            $now = date('Y-m-d H:i:s');
            $staging_data = [
                'batch_no' => static::genSerialNo('OAIC', RedisKey::AGENCY_PAYMENT_CREATE_COUNTER, 4, date('ymd')),
                'apply_date' => date('Y-m-d'),
                'apply_id' => $user['id'],
                'apply_name' => $this->getNameAndNickName($user['name'] ?? '', $user['nick_name'] ?? ''),
                'status' => AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING,
                'apply_department_id' => $user['node_department_id'],
                'apply_department_name' => '',
                'cost_company_id' => '',
                'cost_company_name' => '',
                'created_at' => $now,
                'updated_at' => $now
            ];
            $department_info = (new DepartmentRepository())->getDepartmentDetail($user['node_department_id']);
            $staging_data['apply_department_name'] = $department_info['name'] ?? '';
            //获取员工所属公司
            $cost_company_list = (new PurchaseService())->getCooCostCompany();
            $cost_company_kv = array_column($cost_company_list,'cost_company_name','cost_company_id');
            if ($department_info && key_exists($department_info['company_id'], $cost_company_kv)) {
                $staging_data['cost_company_id'] = $department_info['company_id'];
                $staging_data['cost_company_name'] = $cost_company_kv[$department_info['company_id']];
            }
            $bool = $agency_payment_model->i_create($staging_data);
            if ($bool === false) {
                throw new BusinessException('代理支付批次-暂存失败 = ' . json_encode($staging_data, JSON_UNESCAPED_UNICODE) . '; 可能存在的问题: ' . get_data_object_error_msg($agency_payment_model), ErrCode::$BUSINESS_ERROR);
            }
            $staging_data['id'] = $agency_payment_model->id;
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-stagingPayment-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $staging_data
        ];
    }

    /**
     * 新增-取消-暂存（系统性）- 删除
     * @param integer $id 批次id
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function stagingPaymentDel($id, $user = [])
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = true;
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_info = $this->checkIsSelf($id, $user);
            //取消 && 自己的 && 暂存的数据
            if ($agency_payment_info->status == AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING) {
                $bool = $this->delDb($agency_payment_info);
            }
            //事物提交
            $db->commit();
        } catch (\Exception $e) {
            $db->rollback();
            $this->logger->warning('agency_payment-stagingPayment-delete-failed:' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool
        ];
    }

    /**
     * 计算某批次下所有汇总金额信息
     * @param integer $id 批次id
     * @return array
     * @throws ValidationException
     */
    public function calculateTotalAmount($id)
    {
        $agency_payment_info = $this->getAgencyPaymentInfoById($id);
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('SUM(amount_no_tax) as amount_total_no_tax, SUM(amount_total_vat) as amount_total_vat, SUM(payable_amount) as payable_amount, SUM(amount_total_wht) as amount_total_wht, SUM(amount_total_actually) as amount_total_actually');
        $builder->from(AgencyPaymentDetailModel::class);
        //组合搜索条件
        $builder->where('agency_payment_id = :agency_payment_id:', ['agency_payment_id' => $agency_payment_info->id]);
        $amount_data =  $builder->getQuery()->getSingleResult()->toArray();
        $amount_data['amount_total_no_tax'] = $amount_data['amount_total_no_tax'] ?? 0.00;
        $amount_data['amount_total_vat'] = $amount_data['amount_total_vat'] ?? 0.00;
        $amount_data['payable_amount'] = $amount_data['payable_amount'] ?? 0.00;
        $amount_data['amount_total_wht'] = $amount_data['amount_total_wht'] ?? 0.00;
        $amount_data['amount_total_actually'] = $amount_data['amount_total_actually'] ?? 0.00;
        return $amount_data;
    }

    /**
     * 计算
     * @param integer $id 批次id
     * @return array
     */
    public function calculate($id)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $data = [];
        try {
            $data = $this->calculateTotalAmount($id);
            $data['amount_total_no_tax'] = $data['amount_total_no_tax'];//不含税总金额
            $data['amount_total_vat'] = $data['amount_total_vat'];//VAT总金额
            $data['payable_amount'] = $data['payable_amount'];//应付总金额（含VAT含WHT）
            $data['amount_total_wht'] = $data['amount_total_wht'];//WHT总金额
            $data['amount_total_actually'] = $data['amount_total_actually'];//实付总金额（含VAT不含WHT）
            $data['detail_count'] = $this->getDetailCountById($id);//总明细数：总行数
        } catch (ValidationException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-apply-calculate-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 新增/编辑-草稿/提交
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param string $type 操作类型
     * @return array
     */
    public function save($params, $user, $type = 'add')
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        //开启事务
        $db = $this->getDI()->get('db_oa');
        $db->begin();
        try {
            $agency_payment_info = $this->checkIsSelf($params['id'], $user);
            //新增-暂存、待提交可保存；编辑-待提交可保存
            if (($type == 'add' && !in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT])) || ($type == 'edit' && $agency_payment_info->status != AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT)) {
                throw new ValidationException(static::$t->_('agency_payment_status_not_wait_submit'), ErrCode::$VALIDATE_ERROR);
            }
            //提交类型
            $is_submit = $params['is_submit'];
            $now = date('Y-m-d H:i:s');
            //提交操作逻辑验证;
            $amount_data = $this->calculateTotalAmount($agency_payment_info->id);
            if ($is_submit == AgencyPaymentEnums::APPLY_IS_SUBMIT_YES) {
                //明细行必须要数据
                $detail_count = $this->getDetailCountById($agency_payment_info->id);
                if (!$detail_count) {
                    throw new ValidationException(static::$t->_('agency_payment_detail_zero'), ErrCode::$VALIDATE_ERROR);
                }
                //需要判断前端提交过来的金额与库里计算的金额一致
                if(bccomp($params['amount_total_no_tax'], $amount_data['amount_total_no_tax']) != 0 || bccomp($params['amount_total_vat'], $amount_data['amount_total_vat']) != 0 || bccomp($params['amount_total_wht'], $amount_data['amount_total_wht']) != 0 || bccomp($params['amount_total_actually'], $amount_data['amount_total_actually']) != 0) {
                    throw new ValidationException(static::$t->_('agency_payment_amount_error'), ErrCode::$VALIDATE_ERROR);
                }
                //一个单据里的收款人ID，不可存在重叠的费用开始期间和费用结束期间，否则右上角toast提示：收款人ID存在多个重叠的费用开始期间和费用结束期间，请检查之后再提交。注意：当收款人ID为空时，不认为是重复的
                $agency_payment_detail = $agency_payment_info->getDetails()->toArray();
                //明细行的费用部门和费用网点是否必须传输
                $detail_is_required = $this->checkDetailIsRequired($params['cost_company_id'], $params['cost_type']);
                //代理支付明细表-付款汇总行
                $agency_payment_detail_summary_data = [];
                foreach ($agency_payment_detail as $key => $item) {
                    if ($detail_is_required && (empty($item['cost_department_name']) || empty($item['cost_store_name']) || empty($item['cost_center_code']))) {
                        //如果单据头上的费用所属公司和费用类型为代理支付传输sap的公司和费用，那么每个付款明细行的费用部门，费用网点，成本中心必须有值
                        throw new ValidationException(static::$t->_('agency_payment_detail_cost_department_store_invalid'), ErrCode::$VALIDATE_ERROR);
                    }
                   $item['key'] = $key;
                   $this->compareUploadData($item, $agency_payment_detail, false);

                   $agency_payment_detail_summary_key = $item['cost_department_id'] . '_' . $item['cost_store_id'] . '_' . $item['cost_center_code'];
                   if (isset($agency_payment_detail_summary_data[$agency_payment_detail_summary_key])) {
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_vat'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_vat'], $item['amount_total_vat'], 2);
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_wht'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_wht'], $item['amount_total_wht'], 2);
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_no_tax'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_no_tax'], $item['amount_no_tax'], 2);
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['payable_amount'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['payable_amount'], $item['payable_amount'], 2);
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_actually'] = bcadd($agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['amount_total_actually'], $item['amount_total_actually'], 2);
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key]['num'] += 1;
                   } else {
                       $agency_payment_detail_summary_data[$agency_payment_detail_summary_key] = [
                           'agency_payment_id' => $agency_payment_info->id,
                           'amount_total_vat' => $item['amount_total_vat'],//vat金额总计
                           'amount_total_wht' => $item['amount_total_wht'],//wht金额总计
                           'amount_total_no_tax' => $item['amount_no_tax'],//不含税总金额
                           'payable_amount' => $item['payable_amount'],//应付总金额
                           'amount_total_actually' => $item['amount_total_actually'],//实付总金额
                           'num' => 1,//数量
                           'cost_department_id' => $item['cost_department_id'],//费用部门ID
                           'cost_department_name' => $item['cost_department_name'],//费用部门名称
                           'cost_sys_department_id' => $item['cost_sys_department_id'],//费用一级部门ID
                           'cost_store_id' => $item['cost_store_id'],//费用网点ID
                           'cost_store_name' => $item['cost_store_name'],//费用网点名称
                           'cost_center_code' => $item['cost_center_code'],//费用成本中心
                           'created_at' => $now
                       ];
                   }
                }
                //提交冗余费用类型，后期用于回调不同业务的接口回传
                $db->updateAsDict(
                    (new AgencyPaymentDetailModel())->getSource(),
                    ['cost_type' => $params['cost_type'] ?? 0, 'updated_at' => $now],
                    ["conditions" => "id IN (".implode(',', array_column($agency_payment_detail, 'id')).")"]
                );

                //代理支付明细表-付款汇总行入库
                (new AgencyPaymentDetailSummaryModel())->batch_insert($agency_payment_detail_summary_data);
            }

            $agency_payment_info->cost_company_id = $params['cost_company_id'] ?? 0;//费用所属公司id
            $agency_payment_info->cost_company_name = $params['cost_company_name'] ?? '';//费用所属公司名称
            $agency_payment_info->cost_sys_department_id = $params['cost_sys_department_id'] ?? 0;//费用部门id
            $agency_payment_info->cost_department_name = $params['cost_department_name'] ?? '';//费用部门名称
            $agency_payment_info->cost_store_type = $params['cost_store_type'] ?? 0;//费用总部/网点
            $agency_payment_info->pay_method = $params['pay_method'] ?? Enums::PAYMENT_METHOD_BANK_TRANSFER;//付款方式
            $agency_payment_info->currency = $params['currency'] ?? 0;//币种
            $agency_payment_info->mark = $params['mark'] ?? '';
            $agency_payment_info->cost_type = $params['cost_type'] ?? 0;//费用类型
            $agency_payment_info->amount_total_no_tax = $amount_data['amount_total_no_tax'];//不含税总金额
            $agency_payment_info->amount_total_vat = $amount_data['amount_total_vat'];//VAT总金额
            $agency_payment_info->amount_total_wht = $amount_data['amount_total_wht'];//WHT总金额
            $agency_payment_info->amount_total_actually = $amount_data['amount_total_actually'];//实付总金额
            $agency_payment_info->payable_amount = $amount_data['payable_amount'];//应付总金额
            $agency_payment_info->status = ($is_submit == AgencyPaymentEnums::APPLY_IS_SUBMIT_YES) ? AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING : AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT;//申请状态
            $agency_payment_info->updated_at = $now;
            //待审核才需要调取by创建审批
            if ($agency_payment_info->status == Enums::WF_STATE_PENDING) {
                $source_type = $params['source_type'] ?? AgencyPaymentEnums::SOURCE_TYPE_ADD;
                $by_workflow = new ByWorkflowService();
                $by_add_result = $by_workflow->add([
                    'submitter_id' => $user['id'],
                    'summary_data' => [],
                    'biz_type' => ByWorkflowEnums::BY_BIZ_TYPE_AGENCY_PAYMENT,
                    'audit_params' => [
                        'company_id' => $params['cost_company_id'],
                        'department_id' => $params['cost_sys_department_id'],
                        'source_type' => $source_type == AgencyPaymentEnums::SOURCE_TYPE_API ? 1 :2,//接口传输1是，2否
                        'cost_type' => $params['cost_type'],
                        'payable_amount' => $amount_data['payable_amount'],//应付总金额（含VAT含WHT）
                        'amount_total_actually' => $amount_data['amount_total_actually'],//实付总金额（含VAT不含WHT）
                    ],
                ]);
                $agency_payment_info->workflow_no = $by_add_result['serial_no'];
            }
            $bool = $agency_payment_info->save();
            if ($bool === false) {
                throw new BusinessException('代理支付-我的申请-草稿/提交失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }
            //删除原有附件
            $bool = $agency_payment_info->getAttachments()->delete();
            if ($bool === false) {
                throw new BusinessException('代理支付-我的申请-删除原附件失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE) . '; 可能存在的原因：' . get_data_object_error_msg($agency_payment_info), ErrCode::$BUSINESS_ERROR);
            }
            //新增附件
            if ($params['attachment_arr']) {
                $attach_arr = [];
                foreach ($params['attachment_arr'] as $k => $file) {
                    $tmp = [];
                    $tmp['oss_bucket_type'] = Enums::OSS_BUCKET_TYPE_AGENCY_PAYMENT_ATTACHMENT;
                    $tmp['oss_bucket_key'] = $agency_payment_info->id;
                    $tmp['sub_type'] = 0;
                    $tmp['bucket_name'] = $file['bucket_name'];
                    $tmp['object_key'] = $file['object_key'];
                    $tmp['file_name'] = $file['file_name'];
                    $tmp['created_at'] = $now;
                    $attach_arr[] = $tmp;
                }
                $attach = new SysAttachmentModel();
                $bool = $attach->batch_insert($attach_arr);
                if ($bool === false) {
                    throw new BusinessException('代理支付-我的申请-充附件添加失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
            $db->commit();
        } catch (ValidationException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }

        if (!empty($real_message)) {
            $this->logger->warning('agency_payment-apply-save-failed:' . $real_message);
        }
        if (!empty($message)) {
            //事务回滚
            $db->rollback();
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool
        ];
    }

    /**
     * 付款明细-批量新增
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @param array $excel_file excel文件内容
     * @return array
     * @throws BusinessException
     * @throws GuzzleException
     */
    public function detailAdd($params, $user, $excel_file)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $excel_header_column = $excel_data = $new_excel_data = $detail_data =  [];
        $start_time = get_curr_micro_time();
        try {
            set_time_limit(0);
            ini_set('memory_limit', '1024M');
            //是否上传了文件
            if (empty($excel_file)) {
                throw new ValidationException(static::$t->_('material_asset_add_file_error'), ErrCode::$VALIDATE_ERROR);
            }
            $type = $params['type'] ?? 'add';
            $agency_payment_info = $this->checkIsSelf($params['id'], $user);
            //新增-暂存、待提交可保存；编辑-待提交可保存
            if (($type == 'add' && !in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT])) || ($type == 'edit' && $agency_payment_info->status != AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT)) {
                throw new ValidationException(static::$t->_('agency_payment_status_not_wait_submit'), ErrCode::$VALIDATE_ERROR);
            }
            //检测文档合理性
            $agency_payment_info->cost_company_id = $params['cost_company_id'];
            $agency_payment_info->cost_type = $params['cost_type'];
            [$excel_header_column, $excel_data, $detail_data] = $this->checkBatchSaveData($agency_payment_info, $excel_file);
            //付款明细入库操作
            if ($detail_data) {
                $agency_payment_detail_model = new AgencyPaymentDetailModel();
                $bool = $agency_payment_detail_model->batch_insert($detail_data);
                if ($bool === false) {
                    throw new BusinessException('付款明细-批量新增失败 = ' . json_encode($detail_data, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
                }
            }
        } catch (ValidationException $e) {
            //校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
            $real_message = $message;
        } catch (\Exception $e) {
            //系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        }
        if (!empty($real_message)) {
             $this->logger->warning('agency_payment-apply-detail-save-failed:' . $real_message);
        }
        if (!in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR])) {
            //非验证类或系统错误的提示，需要生成下载模版
            $success_num = 0;
            $result_row = AgencyPaymentEnums::DETAIL_BATCH_SAVE_EXCEL_RESULT;
            foreach ($excel_data as $item) {
                if ($code != ErrCode::$SUCCESS && strpos($item[$result_row], 'success') !== false) {
                    $item[$result_row] = '';
                } elseif (strpos($item[$result_row], 'success') !== false) {
                    $success_num++;
                }
                $new_excel_data[] = array_values($item);
            }
            $result = $this->exportExcel($excel_header_column, $new_excel_data, '付款明细-批量新增 -' . date('YmdHis'));
            //内容包含模版第二行表头，所以需要总数-1
            $all_num = count($new_excel_data)-1;
            $data = [
                'url' => $result['data'],
                'all_num' => $all_num,
                'success_num' => $success_num,
                'failed_num' => $all_num - $success_num
            ];
            $code = ErrCode::$SUCCESS;
            $end_time = get_exec_time($start_time);
            $this->logger->info('付款明细-批量新增[' . $all_num . ']条预计耗时:[ ' . $end_time . ' ]');
        }
        return [
            'code' => $code,
            'message' => in_array($code, [ErrCode::$VALIDATE_ERROR, ErrCode::$SYSTEM_ERROR]) ? $message : 'success',
            'data' => $data ?? [],
        ];
    }

    /**
     * 检测数据合理性
     * @param object $agency_payment_info 支付批次对象信息
     * @param array $excel_file excel文件内容
     * @return array
     * @throws BusinessException
     * @throws ValidationException
     */
    public function checkBatchSaveData($agency_payment_info, $excel_file)
    {
        try {
            $config = ['path' => ''];
            $excel = new \Vtiful\Kernel\Excel($config);
            $excel_data = $excel->openFile($excel_file[0]->getTempName())
                ->openSheet()
                ->setType([
                    0 => \Vtiful\Kernel\Excel::TYPE_STRING,//收款人ID
                    1 => \Vtiful\Kernel\Excel::TYPE_STRING,//收款人姓名
                    2 => \Vtiful\Kernel\Excel::TYPE_STRING,//收款银行
                    3 => \Vtiful\Kernel\Excel::TYPE_STRING,//收款账号
                    4 => \Vtiful\Kernel\Excel::TYPE_STRING,//费用部门名称
                    5 => \Vtiful\Kernel\Excel::TYPE_STRING,//费用网点名称
                    7 => \Vtiful\Kernel\Excel::TYPE_STRING,//VAT税率
                    10 => \Vtiful\Kernel\Excel::TYPE_STRING,//WHT税率
                    12 =>\Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//费用开始日期
                    13 =>\Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//费用结束日期
                    14 =>\Vtiful\Kernel\Excel::TYPE_TIMESTAMP,//应付日期
                    16 =>\Vtiful\Kernel\Excel::TYPE_STRING,//外部结算号
                    17 =>\Vtiful\Kernel\Excel::TYPE_STRING,//收款人证件号
                    18 =>\Vtiful\Kernel\Excel::TYPE_STRING,//收款人联系电话
                    19 =>\Vtiful\Kernel\Excel::TYPE_STRING,////收款人居住地址
                ])
                ->getSheetData();
        } catch (\Exception $e) {
            throw new ValidationException(static::$t->_('agency_payment_detail_file_error'), ErrCode::$VALIDATE_ERROR);
        }
        //弹出excel标题第一行信息
        $excel_header_column = array_shift($excel_data);
        //弹出excel标题第二行信息
        $second_header = array_shift($excel_data);
        if (empty($excel_data)) {
            throw new ValidationException(static::$t->_('agency_payment_detail_empty'), ErrCode::$VALIDATE_ERROR);
        }
        //跳过空行
        foreach ($excel_data as $line => $row) {
            if (empty(implode(',', array_filter($row)))) {
                //空行
                unset($excel_data[$line]);
            }
        }
        //单次操作付款明细条数限制
        $excel_data_count = count($excel_data);
        if ($excel_data_count > AgencyPaymentEnums::DETAIL_ONE_ADD_MAX_NUM) {
            throw new ValidationException(static::$t->_('agency_payment_detail_import_num_limit', ['max' => AgencyPaymentEnums::DETAIL_ONE_ADD_MAX_NUM]), ErrCode::$VALIDATE_ERROR);
        }

        //单笔批次下付款明细条数限制
        $detail_db_count = $this->getDetailCountById($agency_payment_info->id);
        if (($excel_data_count + $detail_db_count) > AgencyPaymentEnums::DETAIL_MAX_NUM) {
            throw new ValidationException(static::$t->_('agency_payment_detail_max_num', ['max' => AgencyPaymentEnums::DETAIL_MAX_NUM]), ErrCode::$VALIDATE_ERROR);
        }
        $excel_data = $this->excelToData($excel_data);

        // 金额详情: vat/sst、wht 严格校验
        $wht_category_item = EnumsService::getInstance()->getWhtRateCategoryMap(1, true);
        $vat_config = EnumsService::getInstance()->getVatRateValueItem();
        $wht_config = EnumsService::getInstance()->getWhtRateMap();
        $vat_sst_name = EnumsService::getInstance()->getVatSStRateName();
        $agency_payment_finance_tax_allowance = EnumsService::getInstance()->getSettingEnvValueMap('agency_payment_finance_tax_allowance');
        //明细行的费用部门和费用网点是否必须传输
        $detail_is_required = $this->checkDetailIsRequired($agency_payment_info->cost_company_id, $agency_payment_info->cost_type);

        //费用部门
        $cost_department_name = array_column($excel_data, 'cost_department_name');
        $department_list = (new DepartmentRepository())->getDepartmentByNames($cost_department_name);

        //费用网点
        $cost_store_name = array_column($excel_data, 'cost_store_name');
        $store_list = (new StoreRepository())->getStoreByNames($cost_store_name);

        $detail_data = [];
        $now = date('Y-m-d H:i:s');
        foreach ($excel_data as $key => &$item) {
            $error_msg = [];
            //收款人ID：非必填，最多50字符
            if ($item['payee_staff_id'] && mb_strlen($item['payee_staff_id']) > 50) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_001');
            }
            //收款人姓名：非必填，最多100字符
            if ($item['bank_account_name'] && mb_strlen($item['bank_account_name']) > 100) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_002');
            }
            //收款人银行：非必填，最多255字符
            if ($item['bank_name'] && mb_strlen($item['bank_name']) > 255) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_003');
            }
            //收款人账号：非必填，最多50字符
            if ($item['bank_account'] && mb_strlen($item['bank_account']) > 50) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_004');
            }

            //费用部门名称-必填时费用部门必须有值、填写了费用部门需是（非删除的部门）
            $one_department_info = $department_list[$item['cost_department_name']] ?? [];
            if (($detail_is_required && empty($item['cost_department_name'])) || ($item['cost_department_name'] && empty($one_department_info))) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_016');
            }

            //费用网点名称-必填时费用网点必须有值、填写了费用网点（激活的网点）或 Head office
            if (strtolower($item['cost_store_name']) == strtolower(GlobalEnums::STORE_HEADER_OFFICE_NAME)) {
                $one_store_info = ['id' => GlobalEnums::STORE_HEADER_OFFICE_ID, 'name' => $item['cost_store_name'], 'sap_pc_code' => $one_department_info ? $one_department_info['sap_cost_center'] : ''];
            } else {
                $one_store_info = $store_list[$item['cost_store_name']] ?? [];
            }
            if (($detail_is_required && empty($item['cost_store_name'])) || ($item['cost_store_name'] && empty($one_store_info))) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_017');
            }

            //必填时-找不到成本中心，请联系财务维护网点上的成本中心
            if ($detail_is_required && empty($one_store_info['sap_pc_code'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_018');
            }

            //不含税金额：必填，必须＞0,最多2位小数
            if (bccomp($item['amount_no_tax'], 0, 2) !== 1 || !preg_match(AgencyPaymentEnums::AMOUNT_RULE, $item['amount_no_tax'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_005');
            }
            //vat\sst税率;VAT类别税率(兼容含有%字符串的问题)
            $vat_rate = $item['vat_rate'];
            if (!empty($vat_rate) && strpos($vat_rate,'%')) {
                $vat_rate = str_replace('%', '', $item['vat_rate']);
                $vat_rate = is_numeric($vat_rate) ? (float) sprintf('%.6f', $vat_rate / 100) : 0;
            }
            $vat_rate = is_numeric($vat_rate) ? (float) sprintf('%.2f', $vat_rate * 100) : '';
            $vat_rate = (string) $vat_rate;
            $item['vat_rate'] = $vat_rate . '%';
            if (!in_array($vat_rate, $vat_config)) {
                // VAT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                $error_msg[] = static::$t->_('vat_sst_rate_error_hint', ['VAT_SST' => $vat_sst_name]);
            }

            //VAT税额
            if (bccomp($item['amount_total_vat'], 0, 2) === -1 || !preg_match(AgencyPaymentEnums::AMOUNT_RULE, $item['amount_total_vat'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_006', ['vat_wht' => 'vat']);
            }
            // 提取WHT类别
            if (!array_key_exists($item['wht_category'], $wht_category_item)) {
                // WHT类别错误，请填写：PND3或PND53或/
                $error_msg[] = static::$t->_('payment_upload_error_007');
            }

            // 提取WHT税率;(兼容含有%字符串的问题)
            $wht_rate = $item['wht_rate'];
            if (!empty($wht_rate) && strpos($wht_rate,'%')) {
                $wht_rate = str_replace('%', '', $wht_rate);
                $wht_rate = is_numeric($wht_rate) ? (float) sprintf('%.6f', $wht_rate / 100) : 0;
            }
            $wht_rate = is_numeric($wht_rate) ? (float) sprintf('%.2f', $wht_rate * 100) : '';
            $wht_rate = (string) $wht_rate;
            $item['wht_rate'] = $wht_rate . '%';
            if (empty($wht_config[$wht_category_item[$item['wht_category']]]['rate_list'][$wht_rate])) {
                // WHT税率错误，类别为PND3，请填写：2%或3%或5%；类别为PND53，请填写：1%或2%或3%或5%；类别为/，请填写：0%
                $error_msg[] = static::$t->_('payment_upload_error_008');
            }
            //wht税额
            if (bccomp($item['amount_total_wht'], 0, 2) === -1 || !preg_match(AgencyPaymentEnums::AMOUNT_RULE, $item['amount_total_wht'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_006', ['vat_wht' => 'wht']);
            }
            //费用开始日期：非必填，按照dd-mm-yyyy或者dd/mm/yyyy进行解析
            $item['cost_start_date'] = $this->handleUploadFileDate($item['cost_start_date']);
            if ($item['cost_start_date'] && !preg_match(AgencyPaymentEnums::DATE_RULE, $item['cost_start_date'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_007');
            }
            //费用结束日期：非必填，按照dd-mm-yyyy或者dd/mm/yyyy进行解析
            $item['cost_end_date'] = $this->handleUploadFileDate($item['cost_end_date']);
            if ($item['cost_end_date'] && !preg_match(AgencyPaymentEnums::DATE_RULE, $item['cost_end_date'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_008');
            }
            //费用结束日期必须晚于等于费用开始日期
            $cost_start_date = $this->handleUploadFileDate($item['cost_start_date'], 'Y-m-d');
            $cost_end_date = $this->handleUploadFileDate($item['cost_end_date'], 'Y-m-d');
            if ($cost_start_date > $cost_end_date) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_009');
            }
            //同个表格里的收款人ID相同时，收款人姓名必须相同，否则在对应的行后面提示：相同收款人ID，姓名必须保持一致。注意：如果2个明细行，相同收款人ID但是姓名不同，则这2个明细行都认为有问题，不可导入到系统里
            //同个表格里的收款人ID，其费用开始期间和费用结束期间不可有重复，否则在对应的行后面提示：相同收款人ID的费用期间重复，不可提交。注意：如果2个明细行，费用期间有重复，则这2个明细行都认为有问题，不可导入到系统里
            $tmp_data = [
                'key' => $key,
                'payee_staff_id' => $item['payee_staff_id'],
                'bank_account_name' => $item['bank_account_name'],
                'cost_start_date' => $cost_start_date,
                'cost_end_date' => $cost_end_date,
            ];
            $this->compareUploadData($tmp_data, $excel_data, true, $error_msg);
            //应付日期：非必填，按照dd-mm-yyyy或者dd/mm/yyyy进行解析
            $item['due_date'] = $this->handleUploadFileDate($item['due_date']);
            if ($item['due_date'] && !preg_match(AgencyPaymentEnums::DATE_RULE, $item['due_date'])) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_010');
            }
            $due_date = $this->handleUploadFileDate($item['due_date'], 'Y-m-d');
            //外部结算号：非必填，最多100字符，超过100字符
            if ($item['out_no'] && mb_strlen($item['out_no']) > 100) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_011');
            }
            //收款人证件号：非必填，若填写了值，则超过了100字符，则提示收款人证件号最多100字符
            if ($item['payee_id_no'] && mb_strlen($item['payee_id_no']) > 100) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_019');
            }
            //收款人联系电话：非必填，若填写了值，则超过了100字符，则提示收款人联系电话最多100字符
            if ($item['payee_mobile'] && mb_strlen($item['payee_mobile']) > 100) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_020');
            }
            //收款人居住地址：非必填，若填写了值，则超过了500字符，则提示收款人居住地址最多500字符
            if ($item['payee_address'] && mb_strlen($item['payee_address']) > 500) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_021');
            }
            //超过允差值需要拦截提交
            //填写的WHT金额和系统自动计算的WHT金额差异较大，请检查！ |WHT金额-（系统自动计算：不含税金额*WHT税率）|的绝对值 > 配置的WHT允差
            $set_wht_val = $agency_payment_finance_tax_allowance['wht'] ?? 0;
            $wht_diff = abs(bcsub(round($item['amount_no_tax'] * $wht_rate / 100, 2), $item['amount_total_wht'], 2));
            //填写的VAT金额和系统自动计算的VAT金额差异较大，请检查！ |VAT金额-（系统自动计算：不含税金额*VAT税率）|的绝对值 > 配置的VAT允差
            $set_vat_val = $agency_payment_finance_tax_allowance['vat'] ?? 0;
            $vat_diff = abs(bcsub(round($item['amount_no_tax'] * $vat_rate / 100, 2), $item['amount_total_vat'], 2));
            if (($set_wht_val && bccomp($wht_diff, $set_wht_val, 2) === 1) || ($set_vat_val && bccomp($vat_diff, $set_vat_val, 2) === 1)) {
                $error_msg[] = static::$t->_('agency_payment_detail_import_error_012');
            }

            //存在错误信息，则要赋予备注信息
            if ($error_msg) {
                $item[AgencyPaymentEnums::DETAIL_BATCH_SAVE_EXCEL_RESULT] = 'failed' . implode(';', array_unique($error_msg));
            } else {
                $item[AgencyPaymentEnums::DETAIL_BATCH_SAVE_EXCEL_RESULT] = 'success';
                $payable_amount = bcadd($item['amount_no_tax'], $item['amount_total_vat'], 2);
                $no = str_replace('_', '', static::genSerialNo($agency_payment_info->batch_no, RedisKey::AGENCY_PAYMENT_CREATE_DETAIL_COUNTER, 5, '_'));
                //费用一级部门
                $first_level_department_info = $one_department_info ? (new DepartmentRepository())->getFirstLevelDepartmentByAncestryV3($one_department_info['ancestry_v3']) : [];
                $detail_data[] = [
                    'agency_payment_id' => $agency_payment_info->id,
                    'no' => $no,
                    'out_no' => $item['out_no'],
                    'payment_batch_no' => '',
                    'payee_staff_id' => $item['payee_staff_id'],
                    'payee_id_no' => $item['payee_id_no'],//收款人证件号码
                    'payee_mobile' => $item['payee_mobile'],//收款人联系电话
                    'payee_address' => $item['payee_address'],//收款人居住地址
                    'bank_account_name' => $item['bank_account_name'],
                    'bank_name' => $item['bank_name'],
                    'bank_account' => $item['bank_account'],
                    'vat_rate' => $vat_rate,
                    'amount_total_vat' => $item['amount_total_vat'],
                    'wht_category' => $wht_category_item[$item['wht_category']],
                    'wht_rate' => $wht_rate,
                    'amount_total_wht' => $item['amount_total_wht'],
                    'amount_no_tax' => $item['amount_no_tax'],
                    'payable_amount' => $payable_amount,//应付金额 = 不含税金额 + vat金额
                    'amount_total_actually' => bcsub($payable_amount, $item['amount_total_wht'], 2),//实付金额 =应付金额 - WHT税额
                    'cost_start_date' => $cost_start_date ? $cost_start_date : null,
                    'cost_end_date' => $cost_end_date ? $cost_end_date : null,
                    'due_date' => $due_date ? $due_date : null,
                    'remark' => $item['remark'],
                    'pay_status' => AgencyPaymentEnums::AGENCY_PAYMENT_PAY_STATUS_PENDING,
                    'is_pay' => 0,
                    'pay_staff_id' => 0,
                    'pay_bk_name' => '',
                    'pay_bk_account' => '',
                    'pay_bank_flow_date' => null,
                    'pay_at' => null,
                    'pay_remark' => '',
                    'cost_type' => $agency_payment_info->cost_type,
                    'cost_department_id' => $one_department_info ? $one_department_info['id'] : 0,//费用部门ID
                    'cost_department_name' => $item['cost_department_name'],//费用部门名称
                    'cost_sys_department_id' => $first_level_department_info ? $first_level_department_info['id'] : 0,//费用一级部门id
                    'cost_store_id' => $one_store_info ? $one_store_info['id'] : '',//费用网点ID
                    'cost_store_name' => $item['cost_store_name'],//费用网点名称
                    'cost_center_code' => $one_store_info ? $one_store_info['sap_pc_code'] : '',//费用成本中心
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }
        return [$excel_header_column, array_merge([$second_header], $excel_data), $detail_data];
    }

    /**
     * 同个表格里的收款人ID，其费用开始期间和费用结束期间不可有重复
     * @param array $curr_data 当前判断行
     * @param array $upload_excel_data excel中的所有行
     * @param boolean $is_upload true:明细，false提交
     * @param array $error_msg 错误信息组
     * @throws ValidationException
     */
    private function compareUploadData($curr_data, $upload_excel_data, $is_upload = true, &$error_msg = [])
    {
        // 删除列表中的当前待验证的项
        unset($upload_excel_data[$curr_data['key']]);
        //明细导入文件上传
        if ($is_upload)  {
            foreach ($upload_excel_data as $data) {
                if (empty($data['payee_staff_id'])) {
                    continue;
                }
                //一个单据里的收款人ID，不可存在重叠的费用开始期间和费用结束期间，否则右上角toast提示：收款人ID存在多个重叠的费用开始期间和费用结束期间，请检查之后再提交。注意：当收款人ID为空时，不认为是重复的
                if ($data['payee_staff_id'] == $curr_data['payee_staff_id']) {
                    //同个表格里的收款人ID相同时，收款人姓名必须相同
                    if ($curr_data['bank_account_name'] && $curr_data['bank_account_name'] != $data['bank_account_name']) {
                        $error_msg[] = static::$t->_('agency_payment_detail_import_error_002_01');
                    }
                    //同个表格里的收款人ID，其费用开始期间和费用结束期间不可有重复
                    $data['cost_start_date'] = $data['cost_start_date'] ? date('Y-m-d', strtotime($data['cost_start_date'])) : null;
                    $data['cost_end_date'] = $data['cost_end_date'] ? date('Y-m-d', strtotime($data['cost_end_date'])) : null;
                    if (($curr_data['cost_start_date'] && ($curr_data['cost_start_date'] >= $data['cost_start_date']) && ($curr_data['cost_start_date'] <= $data['cost_end_date'])) || ($curr_data['cost_end_date'] && ($curr_data['cost_end_date'] >= $data['cost_start_date']) && ($curr_data['cost_end_date'] <= $data['cost_end_date']))) {
                        $error_msg[] = static::$t->_('agency_payment_detail_import_error_013');
                    }
                }
            }
        } else {
            //提交
            foreach ($upload_excel_data as $data) {
                //一个单据里的收款人ID，不可存在重叠的费用开始期间和费用结束期间
                if ($data['payee_staff_id'] && $data['payee_staff_id'] == $curr_data['payee_staff_id']) {
                    if (($curr_data['cost_start_date'] && ($curr_data['cost_start_date'] >= $data['cost_start_date']) && ($curr_data['cost_start_date'] <= $data['cost_end_date'])) || ($curr_data['cost_end_date'] && ($curr_data['cost_end_date'] >= $data['cost_start_date']) && ($curr_data['cost_end_date'] <= $data['cost_end_date']))) {
                        throw new ValidationException(static::$t->_('agency_payment_detail_import_error_013'), ErrCode::$VALIDATE_ERROR);
                    }
                }
                //一个单据里，以收款人姓名+收款账号的维度下，不可存在重叠的费用开始期间和费用结束日期
                if (($curr_data['bank_account'] == $data['bank_account'] && $curr_data['bank_account_name'] != $data['bank_account_name'])
                    || ($curr_data['bank_account_name'] == $data['bank_account_name'] && $curr_data['bank_account'] == $data['bank_account'])
                ) {
                    if (($curr_data['cost_start_date'] && ($curr_data['cost_start_date'] >= $data['cost_start_date']) && ($curr_data['cost_start_date'] <= $data['cost_end_date'])) || ($curr_data['cost_end_date'] && ($curr_data['cost_end_date'] >= $data['cost_start_date']) && ($curr_data['cost_end_date'] <= $data['cost_end_date']))) {
                        throw new ValidationException(static::$t->_('agency_payment_detail_import_error_015'), ErrCode::$VALIDATE_ERROR);
                    }
                }
            }
        }
    }

    /**
     * 转excel索引
     * @param array $excel_data excel数据
     * @return array
     */
    public function excelToData($excel_data)
    {
        //excel转字段
        $data_key = [
            'payee_staff_id',//收款人ID
            'bank_account_name',//收款人姓名
            'bank_name',//收款人银行
            'bank_account',//收款人账号
            'cost_department_name',//费用部门名称
            'cost_store_name',//费用网点名称
            'amount_no_tax',//不含税金额
            'vat_rate',//VAT税率
            'amount_total_vat',//vat金额
            'wht_category',//WHT类别
            'wht_rate',//WHT税率
            'amount_total_wht',//WHT税额
            'cost_start_date',//费用开始日期
            'cost_end_date',//费用结束日期
            'due_date',//应付日期
            'remark',//备注
            'out_no',//外部结算号
            'payee_id_no',//收款人证件号码
            'payee_mobile',//收款人联系电话
            'payee_address',//收款人居住地址
        ];
        $data = [];
        foreach ($excel_data as $line => $info) {
            foreach ($data_key as $index => $key) {
                //转string,不然会变成科学计数法
                if ($key == 'amount_no_tax' || $key == 'amount_total_vat' || $key == 'amount_total_wht') {
                    $info[$index] = (string)$info[$index];
                }
                //备注截取500
                if ($key == 'remark') {
                    $info[$index] = mb_strlen($info[$index]) > 500 ? mb_substr($info[$index], 0, 500, 'UTF-8') : $info[$index];
                }
                $data[$line][$key] = trim($info[$index]);
            }
        }
        return $data;
    }

    /**
     * 付款明细-删除/批量删除
     * @param array $params 请求参数组
     * @param array $user 当前登陆者信息组
     * @return array
     */
    public function detailDel($params, $user)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $bool = false;
        try {
            //删除明细行 && 自己的 && 暂存或待提交的数据
            $agency_payment_info = $this->checkIsSelf($params['id'], $user);
            if (!in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT])) {
                throw new ValidationException(static::$t->_('agency_payment_status_not_wait_submit'), ErrCode::$VALIDATE_ERROR);
            }
            if (isset($params['detail_id']) && $params['detail_id']) {
                //单个删除
                $detail_info = AgencyPaymentDetailModel::findFirst([
                    'conditions' => 'id = :id: and agency_payment_id = :agency_payment_id:',
                    'bind' => ['id' => $params['detail_id'], 'agency_payment_id' => $params['id']]
                ]);
                if (empty($detail_info)) {
                    throw new ValidationException(static::$t->_('agency_payment_detail_not_found'), ErrCode::$VALIDATE_ERROR);
                }
                $bool = $detail_info->delete();
            } else {
                //批量删除
                $bool = $agency_payment_info->getDetails()->delete();
            }
            if ($bool === false) {
                throw new BusinessException('代理支付-我的申请-付款明细行删除失败 = ' . json_encode($params, JSON_UNESCAPED_UNICODE), ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage();
        } catch (\Exception $e) {
            $real_message = $e->getMessage();
        }
        if ($real_message) {
            $this->logger->warning('agency_payment-detailDel-failed:' . $real_message);
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $bool
        ];
    }

    /**
     * 付款信息-付款汇总行
     * @param array $params 请求参数组
     * @return array
     */
    public function detailSummaryList($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = $real_message = '';
        $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
        $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
        $data = [
            'items' => [],
            'pagination' => [
                'current_page' => $page_num,
                'per_page' => $page_size,
                'total_count' => 0,
            ],
        ];
        try {
            //获取列表
            $agency_payment_id = $params['id'];
            $agency_payment_info = $this->getAgencyPaymentInfoById($agency_payment_id);
            if (in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT])) {
                //申请状态：-1暂存（系统性），0待提交
                $builder = $this->modelsManager->createBuilder();
                $builder->from(AgencyPaymentDetailModel::class);
                $builder->where('agency_payment_id = :agency_payment_id:', ['agency_payment_id' => $agency_payment_id]);
                $builder->groupBy('cost_department_id, cost_store_id, cost_center_code');
                $builder->columns('count(id) as total');
                $group_total = $builder->getQuery()->execute()->toArray();
                $total_count = count($group_total);
            } else {
                $builder = $this->modelsManager->createBuilder();
                $builder->from(AgencyPaymentDetailSummaryModel::class);
                $builder->where('agency_payment_id = :agency_payment_id:', ['agency_payment_id' => $agency_payment_id]);
                $total_count = (int)$builder->columns('count(id) as total')->getQuery()->getSingleResult()->total;
            }
            if ($total_count) {
                $items = $this->getDetailSummaryList($agency_payment_info, $params);
            }
            $data['items'] = $items ?? [];
            $data['pagination']['total_count'] = $total_count;
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $real_message = $e->getMessage() . $e->getTraceAsString();
        }
        if (!empty($real_message)) {
            $this->logger->warning('get-agency-payment-detailSummaryList-list-failed:' . $real_message);
        }
        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $data,
        ];
    }

    /**
     * 获取付款信息-付款汇总行列表
     * @param object $agency_payment_info 支付批次对象
     * @param array $params 请求参数组
     * @param bool $is_export 是否导出，true是，false否
     * @return array
     */
    private function getDetailSummaryList($agency_payment_info, $params, $is_export = false)
    {
        //获取列表
        if (in_array($agency_payment_info->status, [AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_STAGING, AgencyPaymentEnums::AGENCY_PAYMENT_STATUS_WAIT_SUBMIT])) {
            //申请状态：-1暂存（系统性），0待提交
            $builder = $this->modelsManager->createBuilder();
            $builder->from(AgencyPaymentDetailModel::class);
            $builder->columns('cost_department_id, cost_department_name, cost_store_id, cost_store_name, cost_center_code, COUNT(id) as num, SUM(amount_no_tax) as amount_total_no_tax, SUM(amount_total_vat) as amount_total_vat, SUM(payable_amount) as payable_amount, SUM(amount_total_wht) as amount_total_wht, SUM(amount_total_actually) as amount_total_actually');
            $builder->groupBy('cost_department_id, cost_store_id, cost_center_code');
            $builder->orderBy(null);
        } else {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(AgencyPaymentDetailSummaryModel::class);
            $builder->columns('cost_department_name, cost_store_name, cost_center_code, num, amount_total_no_tax, amount_total_vat, payable_amount, amount_total_wht, amount_total_actually');
        }
        $builder->where('agency_payment_id = :agency_payment_id:', ['agency_payment_id' => $agency_payment_info->id]);
        if ($is_export) {
            $items = $builder->getQuery()->execute()->toArray();
            $row_values = [];
            foreach ($items as $item) {
                $row_values[] = [
                    $agency_payment_info->batch_no,
                    $agency_payment_info->apply_id,
                    $agency_payment_info->apply_name,
                    $agency_payment_info->apply_date,
                    $item['cost_department_name'],
                    $item['cost_store_name'],
                    $item['cost_center_code'],
                    $item['num'],
                    number_format($item['amount_total_no_tax'], 2),
                    number_format($item['amount_total_vat'], 2),
                    number_format($item['payable_amount'], 2),
                    number_format($item['amount_total_wht'], 2),
                    number_format($item['amount_total_actually'], 2),
                ];
            }
            $items = $row_values;
        } else {
            $page_size = empty($params['pageSize']) ? GlobalEnums::DEFAULT_PAGE_SIZE : $params['pageSize'];
            $page_num = empty($params['pageNum']) ? GlobalEnums::DEFAULT_PAGE_NUM : $params['pageNum'];
            $offset = $page_size * ($page_num - 1);
            $builder->limit($page_size, $offset);
            $items = $builder->getQuery()->execute()->toArray();
        }
        return $items;
    }

    /**
     * 查看-付款明细-导出
     * @param array $params 请求参数组
     * @return array|mixed
     * @throws GuzzleException
     */
    public function detailSummaryExport($params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = '';
        try {
            //表头
            $header = [
                static::$t->_('agency_payment.batch_no'),//结算批次号
                static::$t->_('csr_field_create_id'),//申请人工号
                static::$t->_('csr_field_create_name'),//申请人姓名
                static::$t->_('global.apply.date'),//申请日期
                static::$t->_('global.cost.department.name'), //费用部门
                static::$t->_('global.cost.store.name'), //费用网点
                static::$t->_('sap_cost_center'), //成本中心
                static::$t->_('purchase_payment_invoice_head_6'),//数量
                static::$t->_('ordinarypayment.amount_total_no_tax'),//不含税金额总计,
                static::$t->_('ordinarypayment.amount_total_vat'),//vat金额总计,
                static::$t->_('agency_payment.total_payable_amount'),//应付总金额
                static::$t->_('ordinarypayment.amount_total_wht'),//wht金额总计
                static::$t->_('agency_payment.amount_total_actually'),//实付总金额
            ];

            $agency_payment_info = $this->getAgencyPaymentInfoById($params['id']);
            $excel_data = $this->getDetailSummaryList($agency_payment_info, $params, true);

            // 生成Excel
            $file_name = 'Agency_payment_detail_summary_' . date('YmdHis') . '.xlsx';
            $result = $this->exportExcel($header, $excel_data, $file_name);
            if (isset($result['code']) && $result['code'] == ErrCode::$SUCCESS && !empty($result['data'])) {
                $data = $result['data'];
            } else {
                throw new ValidationException(static::$t->_('excel_file_gen_fail'), ErrCode::$VALIDATE_ERROR);
            }
        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage() . $e->getTraceAsString();
            $this->logger->error('代理支付-查看-付款明细-导出异常:' . $message);
        }

        return [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
        ];
    }
}
