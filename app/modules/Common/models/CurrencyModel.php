<?php

namespace App\Modules\Common\Models;


use App\Models\Base;

class CurrencyModel extends Base
{

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('currency');
    }

    /**
     * 获取所有币种
     * @return mixed
     */
    public static function getAllCurrency()
    {
        static $all_currency = null;
        if (is_null($all_currency)) {
            $all_currency = self::find()->toArray();
        }
        return $all_currency;
    }
}