<?php

namespace App\Modules\Common\Models;

use App\Models\Base;

class EnvModel extends Base
{

    public function initialize()
    {
        parent::initialize();

        $this->setConnectionService('db_oa');
        $this->setSource('setting_env');
    }

    public static function getEnvByCode($code, $default = '')
    {
        $item = static::findFirst(
            [
                'conditions' => 'code=:code:',
                'bind' => ['code' => $code]
            ]
        );

        if (empty($item)) {
            return $default;
        }
        return $item->val;
    }
}
