<?php

namespace App\Modules\Common\Models;


use App\Models\Base;

class ContractCompanyModel extends Base
{

    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('contract_company');
    }

    public static function getCompanyByCode($code = '')
    {
        if (empty($code)) {
            $list = static::find(['is_delete' => 0]);
            $result = !empty($list) ? $list->toArray() : [];
        } else {
            $item = static::findFirst(
                [
                    'conditions' => 'company_code=:id: and is_delete=0',
                    'bind' => ['id' => $code]
                ]
            );

            $result = !empty($item) ? $item->company_name : '';
        }

        return $result;
    }
}