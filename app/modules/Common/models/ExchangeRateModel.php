<?php

namespace App\Modules\Common\Models;


use App\Library\Enums\GlobalEnums;
use App\Models\Base;
use App\Library\RedisClient;

class ExchangeRateModel extends Base
{
    public function initialize()
    {
        $this->setConnectionService('db_oa');
        $this->setSource('setting_exchange_rate');
    }

    /**
     * 获取汇率配置列表
     * @param int $base
     * @return mixed
     */
    public static function getRateSetting($base = GlobalEnums::CURRENCY_THB)
    {
        static $config = [];

        if (!empty($config[$base])) {
            return $config[$base];
        }

        return $config[$base] = $setting_list = static::find(
            [
                'conditions' => 'base_currency = :base:',
                'bind' => ['base' => $base]
            ]
        )->toArray();
    }

    /**
     * 获取某一币种的汇率列表 或 某两个币种间的汇率
     * @param int $base
     * @param int $code
     * @return array | integer
     */
    public static function getRateByCodeId($base = GlobalEnums::CURRENCY_THB, $code = 0)
    {
        // 所有配置
        $setting_list = self::getRateSetting($base);

        // 取某基础币种对应的汇率列表
        if (empty($code)) {
            $result = $setting_list;

        } else {
            // 取两个币种之间的汇率
            $setting_list = array_column($setting_list, null, 'format_currency');
            $result = $setting_list[$code]['rate'] ?? 0;
        }

        return $result;
    }
}