<?php
/**
 * 系统开放小工具
 */

namespace App\Modules\Common\Services;

use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Library\BaseService;
use App\Library\RedisClient;
use App\Modules\Purchase\Services\FfwService;

class ToolServer extends BaseService
{
    // 认证码缓存key
    const AUTH_CODE_CACHE_KEY_PREFIX = 'tool_cache_auth_code_';

    // 认证码缓存时间 5 分钟
    const AUTH_CODE_CACHE_PERIOD = 300;

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 生成临时认证码
     */
    public function getAuthCode()
    {
        $random_code = md5(get_generate_random_string(20));
        if (!$this->setCache(static::AUTH_CODE_CACHE_KEY_PREFIX . $random_code, 1, static::AUTH_CODE_CACHE_PERIOD)) {
            throw new ValidationException(static::$t->_('common_tool_auth_code_gen_error'), ErrCode::$VALIDATE_ERROR);
        }

        return $random_code;
    }

    /**
     * 认证码验证
     *
     */
    public function checkAuthCode()
    {
        $auth_code = $this->request->getHeader('TOOL_AUTH_CODE');
        if (mb_strlen($auth_code) != 32) {
            throw new ValidationException(static::$t->_('common_tool_auth_code_null'), ErrCode::$VALIDATE_ERROR);
        }

        if (!$this->getCache(static::AUTH_CODE_CACHE_KEY_PREFIX . $auth_code)) {
            throw new ValidationException(static::$t->_('common_tool_auth_code_wrong'), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 删除Redis缓存
     *
     * @param string $cache_key
     * @param string $type
     * @return array
     * @throws ValidationException
     */
    public function delRedisCache(string $cache_key, string $type = 'string')
    {
        // 先获取缓存
        $cache_data = $this->getRedisCache($cache_key, $type);

        // 删除
        if (!empty($cache_data['data']['cache_value']) && !$this->delCache($cache_key)) {
            throw new ValidationException(static::$t->_('common_tool_del_cache_hint_2', ['cache_k' => $cache_key]), ErrCode::$VALIDATE_ERROR);
        }

        return [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => [
                'deleted_cache_key' => $cache_key,
                'deleted_cache_val' => $cache_data['data']['cache_value']
            ]
        ];
    }

    /**
     * 查看Redis缓存
     *
     * @param string $cache_key
     * @param string $type
     * @return array
     * @throws ValidationException
     */
    public function getRedisCache(string $cache_key, string $type = 'string')
    {
        switch ($type) {
            case 'string':
                $cache_val = $this->getCache($cache_key);
                break;
            case 'set':
                $cache_val = RedisClient::getInstance()->getClient()->smembers($cache_key);
                break;
            case 'list':
                $cache_val = RedisClient::getInstance()->getClient()->lrange($cache_key, 0, -1);
                break;
            case 'hash':
                $cache_val = RedisClient::getInstance()->getClient()->hgetall($cache_key);
                break;
            default:
                $cache_val = '';
        }

        if ($cache_val === false) {
            throw new ValidationException(static::$t->_('common_tool_get_cache_hint_1', ['cache_k' => $cache_key]), ErrCode::$VALIDATE_ERROR);
        }

        return [
            'code' => ErrCode::$SUCCESS,
            'message' => 'success',
            'data' => [
                'cache_key' => $cache_key,
                'cache_value' => $cache_val
            ]
        ];
    }

    /**
     * 获取入库通知单详情
     *
     * @param string $mach_code
     * @param string $inbound_no
     * @return array
     */
    public function getInbound(string $mach_code, string $inbound_no)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';

        try {
            $post_str = (new FfwService($mach_code))->buildRequestParam(['inboundSn' => $inbound_no]);
            $data = (new FfwService($mach_code))->postRequest('Inbound/getInBoundDetail', $post_str);

            // 暂无数据，请填写正确的入库通知单编号
            if (empty($data['data'])) {
                throw new ValidationException(static::$t->_('purchase_payment_inbound_data_null', ['inbound_no' => $inbound_no]), ErrCode::$VALIDATE_ERROR);
            }

            // 该入库通知单未完成，请完成后再申请付款
            if ($data['data'][0]['status'] != 50) {
                throw new ValidationException(static::$t->_('purchase_payment_inbound_not_finished', ['inbound_no' => $inbound_no]), ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch(\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = $e->getMessage();
        }

        return [
            'code' => $data['code'] ?? $code,
            'message' => $data['message'] ?? $message,
            'data' => $data['data'] ?? []
        ];
    }

}
