<?php

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Library\Enums\ImportCenterEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\OssHelper;
use App\Library\Validation\ValidationException;
use App\Models\oa\ImportTaskModel;
use Exception;
use GuzzleHttp\Exception\GuzzleException;

/**
 * 导入中心
 * Class ImportCenterService
 * @package App\Modules\Common\Services
 * @date 2022/9/6
 */
class ImportCenterService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    /**
     * 单例
     * @return ImportCenterService
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 导入结果查询
     *
     * @param int $type 导入类型-使用枚举
     * @param int $staff_id 非必填  如果有值 按照$staff_id和type 唯一查询
     * @param string $other_condition 其他维度的约束条件
     * @return array
     */
    public function getBarcodeImportResult(int $type, int $staff_id = 0, string $other_condition = '')
    {
        $code = ErrCode::$SUCCESS;
        $message = static::$t->_('success');

        // 初始化返回值
        $data = [
            'staff_info_id' => '',
            'staff_name' => '',
            'all_num' => 0, // 总数量
            'success_num' => 0, // 成功数量
            'failed_num' => 0, // 失败数量
            'failed_reason' => '',
            'url' => '', //结果文件
            'tasking_exist' => false // 是否存在进行中的任务
        ];

        try {
            $where     = 'type = :type: and is_deleted = :is_deleted: and status in ({status_arr:array})';
            $value     = [
                'type'       => $type,
                'status_arr' => [ImportCenterEnums::STATUS_DONE, ImportCenterEnums::STATUS_FAILED],
                'is_deleted' => ImportCenterEnums::NOT_DELETED
            ];
            $value_ing = [
                'type'       => $type,
                'status_arr' => [ImportCenterEnums::STATUS_WAITING, ImportCenterEnums::STATUS_DOING],
                'is_deleted' => ImportCenterEnums::NOT_DELETED
            ];
            if (!empty($staff_id)) {
                $where     = $where . ' and staff_info_id = :staff_info_id:';
                $value     = array_merge($value, ['staff_info_id' => $staff_id]);
                $value_ing =  array_merge($value_ing, ['staff_info_id' => $staff_id]);
            }

            if (!empty($other_condition)) {
                $where     = $where . ' AND other_condition = :other_condition:';
                $value     = array_merge($value, ['other_condition' => $other_condition]);
                $value_ing =  array_merge($value_ing, ['other_condition' => $other_condition]);
            }

            // 查询是否存在进行中的任务
            $tasking_info = ImportTaskModel::findFirst([
                'conditions' => $where,
                'bind' => $value_ing,
            ]);
            if ($tasking_info) {
                $data['staff_info_id'] = $tasking_info->staff_info_id;
                $data['staff_name'] = $tasking_info->staff_name;
                $data['tasking_exist'] = true;
            }

            //满足类型为8 的时候 如果上传的数据在处理中  不显示上次处理的结果，只有在处理完毕后显示当前结果
            if (empty($staff_id) || !$data['tasking_exist']) {
                // 查询最近一次下载结果
                $task_model = ImportTaskModel::findFirst([
                    'columns'    => 'staff_info_id, staff_name, success_num, error_num, result_file_url, failed_reason',
                    'conditions' => $where,
                    'bind'       => $value,
                    'order'      => 'id desc',
                ]);
                if ($task_model) {
                    $data['staff_info_id'] = $task_model->staff_info_id;
                    $data['staff_name'] = $task_model->staff_name;
                    $data['all_num']     = $task_model->success_num + $task_model->error_num;
                    $data['success_num'] = (int)$task_model->success_num;
                    $data['failed_num']  = (int)$task_model->error_num;
                    $data['failed_reason'] = $task_model->failed_reason;
                    $data['url']         = $task_model->result_file_url;
                }
            }

        } catch (Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('get-import-center-result-failed: message=' . $e->getMessage() . '; trace_info=' . $e->getTraceAsString());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 添加导入任务
     *
     * @param array $staff_info 用户信息
     * @param string $file_url 导入文件oss地址
     * @param int $type 导入类型-使用枚举 ImportCenterEnums
     * @param array $params 业务参数
     * @param string $other_condition 任务的其他约束条件
     * @return bool
     * @throws ValidationException
     */
    public function addImportCenter(array $staff_info, string $file_url, int $type, $params = [], $other_condition = '')
    {
        // 验证是否已存在(防止重复插入)
        $where = 'type = :type: and is_deleted = :is_deleted: and status in ({status_arr:array})';
        $value = [
            'type'       => $type,
            'status_arr' => [ImportCenterEnums::STATUS_WAITING, ImportCenterEnums::STATUS_DOING],
            'is_deleted' => ImportCenterEnums::NOT_DELETED
        ];
        //耗材领用批量审批/资产领用申请批量审核/资产领用批量申请/网点租房付款-我的申请-批量导入数据等作用到操作人身上任务唯一，其它作用到功能上唯一
        $user_operate_unique_type = [
            ImportCenterEnums::TYPE_MATERIAL_WMS_AUDIT_UPDATE,
            ImportCenterEnums::TYPE_MATERIAL_ASSET_AUDIT,
            ImportCenterEnums::TYPE_MATERIAL_ASSET_OUT_STORAGE_ADD,
            ImportCenterEnums::TYPE_JOB_POSITION,
            ImportCenterEnums::TYPE_PAYMENT_STORE_RENTING_IMPORT,
            ImportCenterEnums::TYPE_MATERIAL_WMS_BATCH_ADD,
        ];
        if (in_array($type, $user_operate_unique_type)) {
            $where = $where . ' and staff_info_id = :staff_info_id:';
            $value['staff_info_id'] = $staff_info['id'];
        }

        // 其他约束条件(非模块级 或 当前用户的)
        if (!empty($other_condition)) {
            $where .=  ' AND other_condition = :other_condition:';
            $value['other_condition'] = $other_condition;
        }

        $detail = ImportTaskModel::findFirst([
            'conditions' => $where,
            'bind'       => $value,
        ]);
        if ($detail) {
            // 导出任务正在执行，请稍等
            throw new ValidationException(static::$t->_('import_center_error_task_exist'), ErrCode::$VALIDATE_ERROR);
        }

        // 保存下载任务
        $model = new ImportTaskModel();
        $model->type = $type;
        $model->status = ImportCenterEnums::STATUS_WAITING;
        $model->task_remark = isset(ImportCenterEnums::$task_remark[$type]) ? static::$t->_(ImportCenterEnums::$task_remark[$type]) : '';
        $model->file_url = $file_url;
        $model->args_json = !empty($params) ? base64_encode(json_encode($params)) : '';
        $model->created_at = date('Y-m-d H:i:s');
        $model->updated_at = date('Y-m-d H:i:s');
        $model->staff_info_id = $staff_info['id'];
        $model->staff_name = $staff_info['name'] ?? '';
        $model->language = static::$language; // 当前语言环境
        $model->other_condition = $other_condition;

        if ($model->save() === false) {
            $msg = '导入任务生成失败：' . get_data_object_error_msg($model);
            $this->logger->error(['params' => $params, 'message' => $msg]);

            // 导入任务生成失败，请稍后重试
            throw new ValidationException(static::$t->_('import_center_error_save_error'), ErrCode::$VALIDATE_ERROR);
        }

        return true;
    }

    /**
     * 新增导入任务
     * @param $file
     * @param $user
     * @param int $type 类型 1.导入新增 2.导入修改 3.导入计划HC数 6.薪资抵扣
     * @param bool $is_oss_file 是否为oss文件
     * @return array
     * @throws GuzzleException
     * @date 2022/7/18
     */
    public function addImportTask($file, $user, $type, bool $is_oss_file = false)
    {
        $code    = ErrCode::$SUCCESS;
        $message = '';
        try {
            // 文件生成OSS链接
            if (!$is_oss_file) {
                $file_path = sys_get_temp_dir() . '/' . $file->getName();
                $file->moveTo($file_path);
                $oss_result = OssHelper::uploadFile($file_path);
                if (empty($oss_result['object_url'])) {
                    throw new ValidationException(self::$t['barcode_file_upload_error'], ErrCode::$VALIDATE_ERROR);
                }
                $fileUri = $oss_result['object_url'];
            } else {
                $fileUri = $file;
            }
            // 导入中心
            $bool = ImportCenterService::getInstance()->addImportCenter($user, $fileUri, $type);
            if (!$bool) {
                throw new ValidationException(self::$t['add_import_center_error'], ErrCode::$VALIDATE_ERROR);
            }

        } catch (ValidationException $e) {
            // 校验错误，可对外抛出
            $code = $e->getCode();
            $message = $e->getMessage();
        } catch (BusinessException $e) {
            // 业务错误（数据错误等），不可对外抛出
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning($e->getMessage());
        } catch (Exception $e) {
            // 系统错误，不可对外抛出
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->warning($e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $code == ErrCode::$SUCCESS ? 'success' : $message,
            'data' => $code == ErrCode::$SUCCESS
        ];
    }

    /**
     * @description 下载列表
     * @param $params
     * @return array
     */
    public function getImportList($params): array
    {
        $page_size   = $params['page_size'] ?? GlobalEnums::DEFAULT_PAGE_SIZE;
        $page_num    = $params['page_num'] ?? GlobalEnums::DEFAULT_PAGE_NUM;
        $total_count = 0;
        $item_list   = [];
        try {
            $offset = $page_size * ($page_num - 1);
            $builder_list = $this->getImportListBuilder($params);
            $builder_count = $this->getImportListBuilder($params);

            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
            $total_count = !empty($totalCount) ? $totalCount->count : 0;

            $builder_list->columns("*");
            $builder_list->limit($page_size, $offset);
            $list = $builder_list->orderBy('import_task.created_at desc')->getQuery()->execute()->toArray();

            foreach ($list as $key => $value) {
                $item_list[] = [
                    'id'              => $value['id'],
                    'staff_info_id'   => $value['staff_info_id'],
                    'file_url'        => $value['file_url'],
                    'type'            => $value['type'],
                    'created_at'      => $value['created_at'],
                    'finish_at'       => $value['finished_at'],
                    'success_num'     => $value['success_num'],
                    'error_num'       => $value['error_num'],
                    'result_file_url' => $value['result_file_url'],
                    'status'          => $value['status'],
                    'status_label'    => static::$t->_('import_statue.' . $value['status']),
                ];
            }
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'items'      => $item_list,
            'pagination' => [
                'page_num'    => intval($page_num),
                'page_size'   => intval($page_size),
                'total_count' => intval($total_count),
            ],
        ];
    }

    /**
     * @description 构建导入列表Builder
     * @param $params
     * @return mixed
     */
    private function getImportListBuilder($params)
    {
        $user = $params['user'];

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['import_task' => ImportTaskModel::class]);
        $builder->where('import_task.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($user)) {
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $user]);
        }
        if (isset($params['type'])) {
            $builder->andWhere('type = :type:', ['type' => $params['type']]);
        }
        return $builder;
    }

    /**
     * @throws BusinessException
     * @throws Exception
     */
    public function downloadResultFile($id)
    {
        $first = ImportTaskModel::findFirst($id);
        if (empty($first)) {
            throw new BusinessException('not  found  task');
        }
        if ($first->status == ImportCenterEnums::STATUS_DOING) {
            throw new BusinessException(static::$t->_('task running'));
        }
        if (empty($first->result_file_url)) {
            throw new BusinessException(static::$t->_('async_import_task_not_result'));
        }
        return $first->result_file_url;
    }

    /**
     * 删除任务
     * @throws BusinessException
     */
    public function delete($id, $operator_id)
    {
        $first = ImportTaskModel::findFirst($id);
        if (empty($first)) {
            throw new BusinessException('not  found  task');
        }
        $first->is_deleted  = ImportCenterEnums::DELETED;
        $first->save();
        $this->logger->info(sprintf("importCenter delete: %d, operator_id:%d", $id, $operator_id));
    }

    /**
     * 获取待处理的导入任务
     *
     * @param array $type_list
     * @return mixed
     */
    public function getPendingTask(array $type_list = [])
    {
        return ImportTaskModel::findFirst([
            'conditions' => 'type IN ({type_list:array}) AND status = :status: AND is_deleted = :is_deleted:',
            'bind' => [
                'type_list' => $type_list,
                'status' => ImportCenterEnums::STATUS_WAITING,
                'is_deleted' => ImportCenterEnums::NOT_DELETED
            ],
            'order' => 'id ASC',
        ]);
    }
}
