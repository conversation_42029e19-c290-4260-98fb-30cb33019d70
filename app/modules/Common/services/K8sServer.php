<?php
/**
 * 适应k8s部署的服务健康检查: 关键服务是否可用
 */

namespace App\Modules\Common\Services;

use Exception;
use App\Library\BaseService;
use App\Models\oa\SettingEnvModel AS OAEnvModel;
use App\Models\backyard\SettingEnvModel AS BySettingEnvModel;
use App\Models\bi\SysDepartmentModel AS BiSysDepartmentModel;
use App\Models\fle\SysDepartmentModel AS FleSysDepartmentModel;

class K8sServer extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 检查服务可用性
     * 1. Redis
     * 2. MySQL
     * 3. ......
     */
    public function checkServerOnline()
    {
        try {
            $this->checkRedis();
            $this->checkMysql();
        } catch (Exception $e){
            throw $e;
        }
    }

    // Redis 可用性检查
    protected function checkRedis()
    {
        $redis = $this->getDI()->get("redis");
        $redis->set('oa_k8s_redis_state', 1, 60);
        if (!$redis->get('oa_k8s_redis_state')) {
            throw new Exception('redis unavailable error');
        }
    }

    // MySQL 可用性检查
    protected function checkMysql()
    {
        // 连接OA数据库
        OAEnvModel::findFirst(['columns'=>'id']);

        // 连接backyard数据库
        BySettingEnvModel::findFirst(['columns'=>'code']);

        // 连接bi数据库
        BiSysDepartmentModel::findFirst(['columns'=>'id']);

        // 连接fle数据库
        FleSysDepartmentModel::findFirst(['columns'=>'id']);
    }

}
