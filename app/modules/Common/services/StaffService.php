<?php

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Repository\StaffDepartmentAreasStoreRepository;

class StaffService extends BaseService
{

    private static $instance = null;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }


    /**
     * 获取员工的管线范围的SQL拼接
     * @param $staff_info_id
     * @param string $table_alias
     * @return array
     */
    public function staffManageRangeCondition($staff_info_id, string $table_alias = 'staff'): array
    {
        $areas_range = (new StaffDepartmentAreasStoreRepository())->getStaffManageDepartmentsAreasStores($staff_info_id);

        $department_ids = empty($areas_range['department_ids']) ? ['-100'] : $areas_range['department_ids'];//给个不存在的部门
        $stores_ids     = empty($areas_range['stores_ids']) ? ['-100'] : $areas_range['stores_ids'];        //给个不存在的网点


        $conditions = [];
        $bind       = [];

        $conditions[]           = "( %s% node_department_id IN ({department_ids:array}) and %s% sys_store_id = '-1' )";
        $bind['department_ids'] = $department_ids;

        if (in_array('-2', $stores_ids)) {
            $conditions[] = " %s% sys_store_id != '-1' ";
        } else {
            $conditions[]      = " %s% sys_store_id IN ({store_ids:array}) ";
            $bind['store_ids'] = $stores_ids;
        }

        return [
            'conditions' => str_replace('%s%', !empty($table_alias) ? $table_alias.'.' : $table_alias,
                implode(' OR ', $conditions)),
            'bind'       => $bind,
        ];
    }

}
