<?php
/**
 * 审批待办统计
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\WorkflowPendingEnums;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use App\Models\oa\SmsPendingWhitelistModel;
use App\Models\oa\WorkflowRequestNodeFyrMiddleModel;
use App\Modules\Workflow\Models\WorkflowRequestModel;
use App\Modules\Workflow\Models\WorkflowRequestNodeAt;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Modules\Transfer\Models\AuditApprovalModel;

class WorkflowPendingService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取审批流待审批人及待审批数
     * @param int $timeout_min 超时分钟
     * @param int $whitelist_strategy 白名单待审批数处理策略, 1-不含白名单；2-只取白名单；3-忽略白名单(即所有)
     * @return mixed
     */
    public function getWorkflowPendingData(int $timeout_min = 30, int $whitelist_strategy = 1)
    {
        $code = ErrCode::$SUCCESS;
        $msg = 'success';
        $data = [];

        try {
            // 取数 OA 系统维护的待办
            $oa_pending_data = $this->getPendingDataFromOA($timeout_min, $whitelist_strategy);
            $oa_pending_data = array_column($oa_pending_data, null, 'staff_id');

            // 取数 BY 系统维护的审批流待办
            // BY 系统配置的审批流，审批功能在 OA的
            // v10942
            $by_pending_data = $this->getPendingDataFromBY($timeout_min, $whitelist_strategy);
            $this->logger->info('OA 审批待办取数结果[From BY]: ' . json_encode($by_pending_data, JSON_UNESCAPED_UNICODE));

            // OA 与 BY 待审批数归并: 相同工号的数据累计, 差集的补充
            foreach ($by_pending_data as $by_data) {
                if (!empty($oa_pending_data[$by_data['staff_id']])) {
                    $oa_pending_data[$by_data['staff_id']]['count'] += $by_data['count'];
                } else {
                    $oa_pending_data[$by_data['staff_id']] = $by_data;
                }
            }

            $data = array_values($oa_pending_data);
            unset($oa_pending_data, $by_pending_data);

            $this->logger->info('OA + BY 审批待办取数结果: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $msg = '获取审批人待办数异常[sms]: ' . $e->getMessage();
            $this->logger->error($msg);
        }

        return [
            'code' => $code,
            'message' => $msg,
            'data' => $data
        ];
    }

    /**
     * 获取 OA 系统待办 计数
     * @param int $timeout_min 超时分钟
     * @param int $whitelist_strategy 白名单员工的统计策略, 1-不含白名单；2-只取白名单；3-忽略白名单(即所有)
     * @return mixed
     */
    protected function getPendingDataFromOA(int $timeout_min = 30, int $whitelist_strategy = 1)
    {
        $data = [];

        try {
            // 获取白名单工号
            $whitelist_staff_ids = [];
            if (in_array($whitelist_strategy, [WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST, WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST])) {
                $whitelist_staff_ids = $this->getWhitelist();

                if ($whitelist_strategy == WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST && empty($whitelist_staff_ids)) {
                    throw new ValidationException('白名单为空', ErrCode::$VALIDATE_ERROR);
                }

                $whitelist_staff_ids = array_map('intval', array_column($whitelist_staff_ids, 'staff_id'));
            }

            // 获取审批待办数: 指定时间前未审批的计数
            $audit_pending_list = $this->getAuditPendingFromOA($whitelist_staff_ids, $whitelist_strategy, $timeout_min);
            $this->logger->info('OA 审批待办取数结果: ' . json_encode($audit_pending_list, JSON_UNESCAPED_UNICODE));
            $audit_pending_list = array_column($audit_pending_list, 'count', 'staff_id');

            // 获取被征询待办(征询待回复): 实时取 被征询待回复的计数(单据审批中 + 未回复)
            $reply_pending_list = $this->getReplyPendingCountFromOA($whitelist_staff_ids, $whitelist_strategy);
            $this->logger->info('OA 征询回复待办取数结果: ' . json_encode($reply_pending_list, JSON_UNESCAPED_UNICODE));
            $reply_pending_list = array_column($reply_pending_list, 'count', 'staff_id');

            // 所有待办人
            $pending_staff_list = array_unique(array_filter(array_merge(array_keys($audit_pending_list), array_keys($reply_pending_list))));

            // 审批待办 和 回复待办 计数 合并: 审批待办 + 回复待办
            foreach ($pending_staff_list as $staff_id) {
                $audit_pending_count = $audit_pending_list[$staff_id] ?? 0;
                $reply_pending_count = $reply_pending_list[$staff_id] ?? 0;

                $data[] = [
                    'staff_id' => $staff_id,
                    'count' => bcadd($audit_pending_count, $reply_pending_count)
                ];
            }

            $this->logger->info('OA 待办取数汇总结果[待审批+待回复]: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        } catch (ValidationException $e) {
            $msg = '获取 OA 待办数异常[sms]: ' . $e->getMessage();
            $this->logger->info($msg);

        } catch (\Exception $e) {
            $msg = '获取 OA 待办数异常[sms]: ' . $e->getMessage();
            $this->logger->error($msg);
        }

        return $data;
    }

    /**
     * 获取OA 审批待办数
     *
     * @param array $whitelist_staff_ids
     * @param int $whitelist_strategy
     * @param int $timeout_min
     * @return array
     */
    protected function getAuditPendingFromOA(array $whitelist_staff_ids = [], int $whitelist_strategy, int $timeout_min = 30)
    {
        // 超时起始时间
        $start_time = date('Y-m-d H:i:s', time() - $timeout_min * 60);

        //*// 取所有审批待办数 = 待审批未征询 + 待审批已征询
        // 1. 获取超时待审批人数据 - 所有审批待办数 - 过滤掉已废弃的审批流
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'node.auditor_id AS staff_id',
            'COUNT(node.auditor_id) AS count',
            'GROUP_CONCAT(request.id) AS request_ids'
        ]);

        $builder->from(['node' => WorkflowRequestNodeAuditorModel::class]);
        $builder->leftjoin(WorkflowRequestModel::class, 'request.id = node.request_id', 'request');
        $builder->where('request.state = :request_state: AND request.is_abandon = :is_abandon:', [
            'request_state' => Enums::WF_STATE_PENDING,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO
        ]);
        $builder->andWhere('node.audit_status = :audit_status:', ['audit_status' => Enums::WF_STATE_PENDING]);
        $builder->andWhere('node.created_at <= :created_at:', ['created_at' => $start_time]);

        // 暂时隐去: 剔除某些业务待办取数的逻辑
        $builder->notInWhere('request.biz_type', WorkflowPendingEnums::$no_statistics_required_biz_item);

        if (!empty($whitelist_staff_ids)) {
            switch ($whitelist_strategy) {
                case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST:
                    $builder->notInWhere('node.auditor_id', $whitelist_staff_ids);
                    break;
                case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST:
                    $builder->inWhere('node.auditor_id', $whitelist_staff_ids);
                    break;
            }
        }

        $builder->groupBy('node.auditor_id');
        $audit_pending_list = $builder->getQuery()->execute()->toArray();//*/
        $this->logger->info('OA 审批待办取数结果[含已征询]: ' . json_encode($audit_pending_list, JSON_UNESCAPED_UNICODE));

        //*// 计算上述待审批中已发起征询的数(即 审批人无需再关注的待审批单据数)
        // 2. 获取超时前已发起征询的数据 - 审批人待审批已征询单据数

        if (!empty($audit_pending_list)) {
            // 待审批的审批流ID集合
            $pending_request_ids = array_unique(array_filter(explode(',', implode(',', array_column($audit_pending_list, 'request_ids')))));

            // 获取上述审批流中已发起征询的干系审批人
            $fyr_builder = $this->modelsManager->createBuilder();
            $fyr_builder->columns([
                'GROUP_CONCAT(fyr_middel.node_fyr_auditor_ids) AS related_auditor_ids',
                'fyr_middel.request_id',
            ]);
            $fyr_builder->from(['fyr_middel' => WorkflowRequestNodeFyrMiddleModel::class]);
            $fyr_builder->inWhere('fyr_middel.request_id', $pending_request_ids);
            $fyr_builder->groupBy('fyr_middel.request_id');
            $fyr_list = $fyr_builder->getQuery()->execute()->toArray();

            // 待审批人的已征询数
            $fyr_auditor_count_map = [];
            foreach ($fyr_list as $value) {
                $related_auditor_ids = array_filter(array_unique(explode(',', $value['related_auditor_ids'])));
                if (empty($related_auditor_ids)) {
                    continue;
                }

                foreach ($related_auditor_ids as $auditor_id) {
                    if (isset($fyr_auditor_count_map[$auditor_id])) {
                        $fyr_auditor_count_map[$auditor_id] += 1;
                    } else {
                        $fyr_auditor_count_map[$auditor_id] = 1;
                    }
                }
            }

            $this->logger->info('OA 征询待办取数结果[待审批已征询]: ' . json_encode($fyr_auditor_count_map, JSON_UNESCAPED_UNICODE));

            // 3. 计算审批人待处理数: 待审批数 - 已征询待审批数
            foreach ($audit_pending_list as $pending_key => $pending_value) {
                // 审批人非审批征询干系人
                $fyr_count = $fyr_auditor_count_map[$pending_value['staff_id']] ?? null;
                if (is_null($fyr_count)) {
                    continue;
                }

                // 审批人是审批征询干系人, 待处理总数 - 征询单据数
                $_pending_count = $pending_value['count'] - $fyr_count;
                if ($_pending_count <= 0) {
                    unset($audit_pending_list[$pending_key]);
                    continue;
                }

                // 重置审批人待处理数
                $pending_value['count'] = $_pending_count;
                $audit_pending_list[$pending_key] = $pending_value;
            }

            $fyr_list = null;
            $fyr_auditor_count_map = null;

            $audit_pending_list = array_map(function ($val) {
                unset($val['request_ids']);
                return $val;
            }, $audit_pending_list);

            $audit_pending_list = array_values($audit_pending_list);
        }//*/

        return $audit_pending_list;
    }

    /**
     * 获取OA 审批中 待回复的计数
     *
     * @param array $whitelist_staff_ids
     * @param int $whitelist_strategy
     * @return array
     */
    protected function getReplyPendingCountFromOA(array $whitelist_staff_ids = [], int $whitelist_strategy)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'reply.staff_id AS staff_id',
            'COUNT(reply.staff_id) AS count',
        ]);

        $builder->from(['request' => WorkflowRequestModel::class]);
        $builder->leftjoin(WorkflowRequestNodeAt::class, 'request.id = reply.request_id', 'reply');
        $builder->where('request.state = :pending_state: AND request.is_abandon = :is_abandon: AND reply.is_reply = :is_reply:', [
            'pending_state' => Enums::WF_STATE_PENDING,
            'is_abandon' => GlobalEnums::WORKFLOW_ABANDON_STATE_NO,
            'is_reply' => GlobalEnums::CONSULTED_REPLY_STATE_PENDING
        ]);

        if (!empty($whitelist_staff_ids)) {
            switch ($whitelist_strategy) {
                case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST:
                    $builder->notInWhere('reply.staff_id', $whitelist_staff_ids);
                    break;
                case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST:
                    $builder->inWhere('reply.staff_id', $whitelist_staff_ids);
                    break;
            }
        }

        $builder->groupBy('reply.staff_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取 BY 系统审批流待审批人及待审批数
     * @param int $timeout_min 超时分钟
     * @param int $whitelist_strategy 白名单待审批数处理策略, 1-不含白名单；2-只取白名单；3-忽略白名单(即所有)
     * @return mixed
     */
    protected function getPendingDataFromBY(int $timeout_min = 30, int $whitelist_strategy = 1)
    {
        $data = [];

        try {
            // 获取白名单工号
            if (in_array($whitelist_strategy, [WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST, WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST])) {
                $whitelist_staff_ids = $this->getWhitelist();

                if ($whitelist_strategy == WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST && empty($whitelist_staff_ids)) {
                    throw new ValidationException('白名单为空', ErrCode::$VALIDATE_ERROR);
                }
            }

            // 超时起始时间
            $start_time = date('Y-m-d H:i:s', strtotime(gmdate('Y-m-d H:i:s', time())) - $timeout_min * 60);

            // BY 取数 created_at 是 零时区
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('count(approval_id) as count, approval_id AS staff_id');
            $builder->from(AuditApprovalModel::class);
            $builder->inWhere('biz_type', WorkflowPendingEnums::$by_pending_audit_type_list);
            $builder->andWhere('state = :state:', ['state' => Enums::WF_STATE_PENDING]);
            $builder->andWhere('deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
            // 暂留, 后期可能会启用该逻辑
            // $builder->andWhere('created_at > :start_time:', ['start_time' => gmdate('Y-m-d H:i:s', time() - 86400 * 90)]);
            $builder->andWhere('created_at <= :time:', ['time' => $start_time]);

            if (!empty($whitelist_staff_ids)) {
                $whitelist_staff_ids = array_map('intval', array_column($whitelist_staff_ids, 'staff_id'));
                switch ($whitelist_strategy) {
                    case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_FILTER_WHITELIST:
                        $builder->notInWhere('approval_id', $whitelist_staff_ids);
                        break;
                    case WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST:
                        $builder->inWhere('approval_id', $whitelist_staff_ids);
                        break;
                }
            }

            $builder->groupBy('approval_id');
            $data = $builder->getQuery()->execute()->toArray();

            $this->logger->info('BY 审批待办取数结果: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

        } catch (ValidationException $e) {
            $msg = '获取 BY 审批人待审批数异常[sms]: ' . $e->getMessage();
            $this->logger->info($msg);

        } catch (\Exception $e) {
            $msg = '获取 BY 审批人待审批数异常[sms]: ' . $e->getMessage();
            $this->logger->error($msg);
        }

        return $data;
    }

    /**
     * 获取白名单列表
     */
    public function getWhitelist()
    {
        return SmsPendingWhitelistModel::find([
            'conditions' => 'is_del = :is_del:',
            'bind' => ['is_del' => GlobalEnums::IS_NO_DELETED],
        ])->toArray();
    }

}
