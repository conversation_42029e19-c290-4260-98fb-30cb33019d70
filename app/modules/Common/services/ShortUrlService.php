<?php
/**
 * Created by PhpStorm.
 * Date: 2021/11/2
 * Time: 15:47
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;

class ShortUrlService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ShortUrlService
     * */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 生成短链接
     * 文档地址：https://yapi.flashexpress.pub/project/310/interface/api/22630
     * 说明详见文档
     *
     * @param string $long_url
     * @return mixed
     */
    public function getShortUrl(string $long_url = '')
    {
        $short_url = '';

        $this->logger->info('getShortUrl, long_url=' . $long_url);
        if (empty($long_url)) {
            return $short_url;
        }

        $api_url = env('api_short_url', 'http://192.168.0.230:8002/yourls-api.php?signature=5f64fd12d0');
        $url = $api_url . '&action=shorturl&format=json&url=' . urlencode($long_url);
        $header = ['content-type: application/json'];
        $result = curl_request($url, null, 'get', $header, false, 20);

        $this->logger->info('getShortUrl, result=' . $result);

        if (!empty($result)) {
            $data = json_decode($result, true);
            if (isset($data['statusCode']) && $data['statusCode'] == '200') {
                $short_url = $data['shorturl'];
            }
        } else {
            $this->logger->notice('getShortUrl, 短链生成失败');
        }

        return $short_url;
    }

}
