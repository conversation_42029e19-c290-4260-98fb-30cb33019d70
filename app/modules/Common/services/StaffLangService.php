<?php
/**
 * 获取员工使用by系统的语言包
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums\GlobalEnums;
use App\Models\fle\StaffAccountModel;

class StaffLangService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据用户ID获取消息翻译模板
     * @param integer $user_id 员工工号
     * @param string $template_name 翻译key
     * @param array $bind 翻译里的变量要替换的值
     * @return string
     */
    public function getMsgTemplateByUserId($user_id, $template_name, $bind = [])
    {
        try {
            $lang = $this->getLatestMobileLang($user_id);
            $lang = substr($lang, 0, 2);
            if ($lang == 'zh') {
                $lang = 'zh-CN';
            }
            if (!in_array($lang, ['en', "th", "zh-CN",], 1)) {
                $lang = 'en';
            }
            $this->logger->info('user_id===' . $user_id . "===lang=" . $lang);
            $t = $this->getTranslation($lang);
            if (in_array('kpi', $bind)) {
                return $t;
            }
            return $t->_($template_name, $bind);
        } catch (\Exception $e) {
            $this->logger->warning('getMsgTemplateByUserId===' . $e->getMessage());
            return '';
        }
    }

    /**
     * 获得该用户移动设备最新的语言环境
     * @param integer $staff_info_id 员工工号
     * @param string $default 如果获取失败,默认语言
     * @return mixed|string
     */
    public function getLatestMobileLang($staff_info_id, $default = 'th')
    {
        try {
            static $langArr = [];
            if (empty($langArr[$staff_info_id])) {
                $sql = "select accept_language from staff_account where staff_info_id =:staff_info_id and equipment_type in (1,3) order by updated_at desc";
                $arr = $this->getDI()->get('db_fle')->fetchOne($sql, \PDO::FETCH_ASSOC, ["staff_info_id" => (int)$staff_info_id]);
                if (empty($arr) || empty($arr['accept_language'])) {
                    $langArr[$staff_info_id] = $default;
                } else {
                    $langArr[$staff_info_id] = substr($arr['accept_language'], 0, 2);
                }
            }
            return $langArr[$staff_info_id];
        } catch (\Exception $e) {
            $this->logger->warning('getLatestMobileLang===' . $e->getMessage());
            return $default;
        }
    }
}
