<?php
/**
 * 短信发送
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\ApiClient;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;

class SmsService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 短信发送
     *
     * 接口文档地址: https://yapi.flashexpress.pub/project/60/interface/api/672
     *
     * @param array $sms_params
     * 示例：
     * [
     *      // 必填, 手机号
     *     'mobile' => $mobile,
     *
     *      // 必填, 短信内容 (如果使用阿里云国内服务，该参数传递JSON格式的模板参数，例如：{"code":1234})
     *     'msg' => $content,
     *
     *     // 选填, 默认0(短信类型)
     *     // 0:普通短信; 1:验证码短信; 2：提醒短信; 3 ：推广短信
     *     // 优先级: 验证码短信>提醒短信>普通短信>推广短信
     *     'type' => 0,
     *
     *      // 选填, 默认th, 语言编码, 暂时没做处理, 不用管
     *     'code' => 'th',
     *
     *      // 选填, 默认0, 延迟发送的秒数
     *     "delay" => 0,
     *
     *      // 选填, 短信服务商, 默认0, 不用管
     *     'service_provider' => 0,
     *
     *      // 选填, 发件人签名: 默认值是express
     *     'sendname'=> '',
     *
     *      // 选填, 国家码（这个手机号属于哪个国家）
     *      // 菲律宾的服务就是PH；可选项：CN中国, TH泰国 ,KH柬埔寨,ID印度尼西亚,LA老挝,MY马来西亚,PH菲律宾,VN越南
     *      // 字符串类型，默认：域名指向的国家
     *      // 注：nation为CN时，短信服务默认为阿里云
     *     'nation' => '',
     *
     *      // 选填, 模板ID/模板Code。默认：空；字符串类型 (目前该参数仅对阿里云服务有效) 形式：SMS_000000
     *      'template' => '',
     * ];
     *
     * 说明: 可以只传mobile  msg
     *
     * @param string $src 调用来源, src 请使用不含空格的英文。 建议使用下划线分隔英文单词的形式(标记哪个业务在发短信)
     *
     * @return mixed
     */
    public function send(array $sms_params = [], string $src = 'oa')
    {
        try {
            if (empty($sms_params['mobile'] || empty($sms_params['msg']))) {
                throw new ValidationException('参数错误[mobile/msg]' . json_encode($sms_params, JSON_UNESCAPED_UNICODE),
                    ErrCode::$VALIDATE_ERROR);
            }

            // 验证非法手机号的基本规则
            // 1.手机号为同一字符构成
            $first_char              = mb_substr($sms_params['mobile'], 0, 1);
            $first_char_repeat_count = mb_substr_count($sms_params['mobile'], $first_char);
            if ($first_char_repeat_count == mb_strlen($sms_params['mobile'])) {
                throw new ValidationException("手机号码错误[{$sms_params['mobile']}]", ErrCode::$VALIDATE_ERROR);
            }

            $sms_params['type']  = $sms_params['type'] ?? 0;
            $sms_params['code']  = $sms_params['code'] ?? 'th';
            $sms_params['delay'] = $sms_params['delay'] ?? 0;

            $this->logger->info('sms send rpc request: ' . json_encode($sms_params, JSON_UNESCAPED_UNICODE));

            // 短信接口请求
            $rpc_client = new ApiClient('sms', '', 'send', 'th', $src);
            $rpc_client->setParams([$sms_params]);
            $result = $rpc_client->execute();
            $this->logger->info('sms send rpc response: ' . json_encode($result, JSON_UNESCAPED_UNICODE));

            if (isset($result['result']) && !empty($result['result'])) {
                return true;
            }

            if (isset($result['error']) && !empty($result['error'])) {
                throw new BusinessException("call sms send rpc fail: code = {$result['error']['code']}, msg = {$result['error']['message']}",
                    ErrCode::$BUSINESS_ERROR);
            }
        } catch (ValidationException $e) {
            $this->logger->info($e->getMessage());
        } catch (BusinessException $e) {
            $this->logger->warning($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage() . $e->getTraceAsString());
        }

        return false;
    }

}
