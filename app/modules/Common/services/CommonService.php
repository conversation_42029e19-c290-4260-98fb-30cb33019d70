<?php

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;
use Exception;

class CommonService extends BaseService
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 发送请求
     * @param string $path
     * @param string $postData
     * @return array
     */
    public function newPostRequest($path, $postData)
    {
        $this->logger->info('curl_request_log: path: ' . $path . '; params:' . $postData);

        $message = '';
        $data    = [];

        $curl     = curl_init();
        try {
            $header[] = "Content-type: application/json;charset=UTF-8";
            curl_setopt($curl, CURLOPT_URL, $path);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // SSL certificate
            curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($curl, CURLOPT_HEADER, 0);
            curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($curl, CURLOPT_POST, true); // post
            curl_setopt($curl, CURLOPT_POSTFIELDS, $postData); // post data
            curl_setopt($curl, CURLOPT_TIMEOUT, 30);
            $responseText = curl_exec($curl);
            if (curl_errno($curl)) {
                throw new Exception('curl errno: ' . curl_error($curl));
            }

            $data = json_decode($responseText, true);
            $this->logger->info('curl_response_log: result =>' . $responseText);

            $code = $data['code'] ?? ErrCode::$VALIDATE_ERROR;
            if ($code != ErrCode::$SUCCESS) {
                throw new ValidationException($data['msg'] ?? static::$t->_('response_data_null'), $code);
            }
            $data = $data['result'] ?? [];
        } catch (ValidationException $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->logger->notice('curl_request_failed: ' . $message);

        } catch (Exception $e) {
            $code    = $e->getCode();
            $message = $e->getMessage();
            $this->logger->warning('curl_request_error: ' . $message);
        }

        curl_close($curl);

        return [
            'code'    => $code ?? ErrCode::$SUCCESS,
            'message' => $message ?? '',
            'data'    => $data,
        ];
    }
}
