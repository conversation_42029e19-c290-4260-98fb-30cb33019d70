<?php


namespace App\Modules\Common\Services;


use App\Library\BaseService;
use App\Library\Enums;
use App\Library\Enums\ContractEnums;

class ContractService extends BaseService
{
    /**
     * 获取其他合同使用的枚举
     * @date 2021/9/27
     */
    public function getContractEnums(){
        $contract_lang_enum = Enums::$contract_lang;
        //只有老挝显示老挝语,其他国家为配置 法务-老挝语 的审批节点,选择会报错
        if (get_country_code()!='LA'){
            unset($contract_lang_enum[Enums::CONTRACT_LANG_LA]);
        }
        // 非印尼和越南不限时对应语言
        if (get_country_code() == 'ID'){
            unset($contract_lang_enum[Enums::CONTRACT_LANG_TH]);
        } else {
            unset($contract_lang_enum[Enums::CONTRACT_LANG_ID]);
        }
        if (get_country_code()=='VN'){
            unset($contract_lang_enum[Enums::CONTRACT_LANG_TH]);
        } else {
            unset($contract_lang_enum[Enums::CONTRACT_LANG_VN]);
        }

        if (get_country_code() != Enums\GlobalEnums::TH_COUNTRY_CODE) {
            unset($contract_lang_enum[Enums::CONTRACT_LANG_EN_TH]);
            unset($contract_lang_enum[Enums::CONTRACT_LANG_EN_ZH]);
            unset($contract_lang_enum[Enums::CONTRACT_LANG_TH_ZH]);
        }

        $contract_lang = $this->getAmityFormat($contract_lang_enum);
        $contract_pay_method = $this->getAmityFormat(Enums::$pay_method);
        $contract_balance_days = $this->getAmityFormat(Enums::$contract_balance_days);
        $contract_valuation_type = $this->getAmityFormat(Enums::$contract_valuation_type);
        $plat_form = $this->getFormat(ContractEnums::$plat_form_customer);

        return [
            'contract_lang'=>$contract_lang,
            'contract_pay_method'=>$contract_pay_method,
            'contract_balance_days'=>$contract_balance_days,
            'contract_valuation_type'=>$contract_valuation_type,
            'platform' => $plat_form,
            //共同所属公司
            'contract_company' => $this->getAmityFormat(EnumsService::getInstance()->getContractCompanyItem(), false),
            'is_group_contract_items' => $this->getAmityFormat(ContractEnums::$is_group_contract_items),
            'is_vendor_items' => $this->getAmityFormat(ContractEnums::$is_vendor_items),
            'franchisee_type' => $this->getAmityFormat(ContractEnums::$franchisee_type_enums, true, false),
            'sign_type' => $this->getAmityFormat(ContractEnums::$contract_sign_type, true, false),
        ];
    }

    /**
     * 获取前端友好的格式
     * @param $enums
     * @param bool $translation 是否需要翻译
     * @param bool $string_code 是否把code转成string
     * @return array
     * @date 2021/9/27
     */
    public function getAmityFormat($enums, $translation = true, $string_code = true)
    {
        $amity_data = [];
        foreach ($enums as $k => $translation_k) {
            $amity_data[] = [
                'label' => $translation ? self::$t[$translation_k] : $translation_k,
                'code' => $string_code ? (string)$k : $k
            ];
        }
        return $amity_data;
    }

    /**
     * 枚举格式化无翻译
     * @param $enums
     * @return array
     */
    public function getFormat($enums)
    {
        foreach ($enums as $k => $v) {
            $plat_form[] = [
                'label' => $v,
                'code'  => $k
            ];
        }

        return $plat_form ?? [];
    }
}