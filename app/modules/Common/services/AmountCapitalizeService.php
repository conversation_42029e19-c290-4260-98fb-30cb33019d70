<?php
/**
 * Created by PhpStorm.
 * Date: 2021/11/3
 * Time: 11:25
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\ErrCode;
use App\Library\Validation\ValidationException;

class AmountCapitalizeService extends BaseService
{

    private static $instance;

    private function __construct()
    {

    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }


    /**
     * 将数值金额转换为中文大写金额
     * @param $amount float 金额(支持到分)
     * @param $type   int   补整类型,0:到角补整;1:到元补整
     * @return mixed 中文大写金额
     */
    public function convertAmountToCn($amount, $type = 1)
    {
        // 判断输出的金额是否为数字或数字字符串
        if (!is_numeric($amount)) {
            return "要转换的金额只能为数字!";
        }

        // 金额为0,则直接输出"零元整"
        if ($amount == 0) {
            return "人民币零元整";
        }

        // 金额不能为负数
        if ($amount < 0) {
            return "要转换的金额不能为负数!";
        }

        // 金额不能超过万亿,即12位
        if (strlen($amount) > 12) {
            return "要转换的金额不能为万亿及更高金额!";
        }

        // 预定义中文转换的数组
        $digital = array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
        // 预定义单位转换的数组
        $position = array('仟', '佰', '拾', '亿', '仟', '佰', '拾', '万', '仟', '佰', '拾', '元');

        // 将金额的数值字符串拆分成数组
        $amountArr = explode('.', $amount);

        // 将整数位的数值字符串拆分成数组
        $integerArr = str_split($amountArr[0], 1);

        // 将整数部分替换成大写汉字
        $result           = '人民币';
        $integerArrLength = count($integerArr);     // 整数位数组的长度
        $positionLength   = count($position);         // 单位数组的长度
        for ($i = 0; $i < $integerArrLength; $i++) {
            // 如果数值不为0,则正常转换
            if ($integerArr[$i] != 0) {
                $result = $result . $digital[$integerArr[$i]] . $position[$positionLength - $integerArrLength + $i];
            } else {
                // 如果数值为0, 且单位是亿,万,元这三个的时候,则直接显示单位
                if (($positionLength - $integerArrLength + $i + 1) % 4 == 0) {
                    $result = $result . $position[$positionLength - $integerArrLength + $i];
                }
            }
        }

        // 如果小数位也要转换
        if ($type == 0) {
            // 将小数位的数值字符串拆分成数组
            $decimalArr = str_split($amountArr[1], 1);
            // 将角替换成大写汉字. 如果为0,则不替换
            if ($decimalArr[0] != 0) {
                $result = $result . $digital[$decimalArr[0]] . '角';
            }
            // 将分替换成大写汉字. 如果为0,则不替换
            if ($decimalArr[1] != 0) {
                $result = $result . $digital[$decimalArr[1]] . '分';
            }
        } else {
            $result = $result . '整';
        }
        return $result;

    }

    /**
     * 将数值金额转换为泰文大写金额
     * @param $amount float 金额(支持到分)
     * @param $type
     * @return mixed 中文大写金额
     * */
    public function convertAmountToTh($amount)
    {
        $result = '';
        // 判断输出的金额是否为数字或数字字符串
        try{
            if (!is_numeric($amount)) {
                throw new ValidationException('"要转换的金额只能为数字!"', ErrCode::$VALIDATE_ERROR);
            }

            if ($amount == 0) {
                return "ศูนย์";
            }

            // 金额不能为负数
            if ($amount < 0) {
                throw new ValidationException('"要转换的金额不能为负数!"', ErrCode::$VALIDATE_ERROR);

            }

            // 金额不能超过万亿,即12位
            if (strlen($amount) > 12) {
                throw new ValidationException('"要转换的金额不能为万亿及更高金额!"', ErrCode::$VALIDATE_ERROR);

            }

            // 预定义泰语转换的数组
            $digital = array('ศูนย์', 'หนึ่ง', 'สอง', 'สาม', 'สี่', 'ห้า', 'หก', 'เจ็ด', ' แปด', 'เก้า');
            // 预定义单位转换的数组
            $position = array('พัน', 'ร้อย', 'สิบ', 'ร้อยล้าน', 'พัน', 'ร้อย', 'สิบ', 'หมื่น', 'พัน', 'ร้อย', 'สิบ', '');
            // 将金额的数值字符串拆分成数组
            $amountArr = explode('.', $amount);

            // 将整数位的数值字符串拆分成数组
            $integerArr = str_split($amountArr[0], 1);
            $result     = '';

            $integerArrLength = count($integerArr);     // 整数位数组的长度
            $positionLength   = count($position);         // 单位数组的长度
            for ($i = 0; $i < $integerArrLength; $i++) {
                // 如果数值不为0,则正常转换
                if ($integerArr[$i] != 0) {
                    $result = $result . $digital[$integerArr[$i]] . $position[$positionLength - $integerArrLength + $i];
                } else {
                    // 如果数值为0, 且单位是亿,万,元这三个的时候,则直接显示单位
                    if (($positionLength - $integerArrLength + $i + 1) % 4 == 0) {
                        $result = $result . $position[$positionLength - $integerArrLength + $i];
                    }
                }
            }

            if (isset($amountArr[1])) {
                $decimalArr = str_split($amountArr[1], 1);
                // 将角替换成大写汉字. 如果为0,则不替换

                $result = $result . 'จุด' . $digital[$decimalArr[0]];

                if ($decimalArr[1] != 0) {
                    $result = $result . $digital[$decimalArr[1]];
                }
            }

        }catch (ValidationException $e){
            $this->logger->info($e->getMessage());
        } catch (\Exception $e) {
            $this->logger->warning($e->getMessage() . $e->getTraceAsString());
        }


        return $result;

    }
}

