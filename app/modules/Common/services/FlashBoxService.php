<?php
/**
 * Flash Box
 */

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Modules\Common\Models\MailReplyFromCeoModel;
use App\Modules\Common\Models\MailToCeoModel;
use App\Modules\Common\Models\CeoMailStaffProblemOrderModel;
use App\Modules\Common\Models\CeoMailProblemCategoryModel;
use App\Modules\Organization\Models\HrStaffInfoModel;
use App\Modules\Organization\Models\HrJobTitleModel;
use App\Modules\Organization\Models\SysStoreModel;

class FlashBoxService extends BaseService
{
    private static $instance;

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取所有问题工单
     */
    protected function getStaffAllProblemOrders()
    {
        // 获取系统所有问题工单
        $sys_orders = CeoMailStaffProblemOrderModel::find()->toArray();

        // 获取所有员工反馈工单
        $staff_orders = MailToCeoModel::find()->toArray();

        // 以工单号为维度, 提取该工单下所有提问
        $ask_order_list = [];
        foreach ($staff_orders as $so_k => $so_v) {
            if (empty($so_v['problem_no'])) {
                continue;
            }

            // 普通文本
            $content = '';
            if (!empty($so_v['content'])) {
                $content = $so_v['content'] . "; \n";
            }

            // 是否有图片
            if (!empty($so_v['img_url'])) {
                $content .= $so_v['img_url'] . "; \n";
            }

            // 是否文件
            if (!empty($so_v['file_url'])) {
                $content .= $so_v['file_url'];
            }

            $_v = [
                'type'          => 'ask',
                'staff_id'      => $so_v['staff_id'],
                'order_content' => '[ask] ' . $so_v['staff_id'] . ' - ' . trim($content),
                'create_time'   => $so_v['create_time'],
            ];

            $ask_order_list[$so_v['problem_no']][] = $_v;
        }
        unset($staff_orders);

        // 获取所有管理员回复工单
        $reply_orders = MailReplyFromCeoModel::find()->toArray();
        foreach ($reply_orders as $ro_k => $ro_v) {
            if (empty($ro_v['problem_no'])) {
                continue;
            }

            // 普通文本
            $content = '';
            if (!empty($ro_v['content'])) {
                $content = $ro_v['content'] . "; \n";
            }

            // 是否有图片
            if (!empty($ro_v['img_url'])) {
                $content .= $ro_v['img_url'] . "; \n";
            }

            // 是否文件
            if (!empty($ro_v['file_url'])) {
                $content .= $ro_v['file_url'];
            }

            $_v = [
                'type'          => 'reply',
                'staff_id'      => $ro_v['staff_id'],
                'order_content' => '[reply] ' . $ro_v['staff_id'] . ' - ' . trim($content),
                'create_time'   => $ro_v['create_time'],
            ];

            $ask_order_list[$ro_v['problem_no']][] = $_v;
        }
        unset($reply_orders);

        // 数据整合: Excel所需数据
        // 系统工单分类配置
        $problem_category_item  = $this->getOrderCategory();
        $before_seven_days_time = date('Y-m-d H:i:s', (time() - get_sys_time_offset() * 86400));

        // 获取工单员工网点/职位
        $staff_id_item        = array_column($sys_orders, 'staff_id');
        $staff_base_info_item = $this->getStaffBaseInfoByIds($staff_id_item);
        $staff_base_info_item = array_column($staff_base_info_item, null, 'staff_id');

        // 回复状态
        $reply_status_item = [
            0 => '待回复',
            1 => '已回复',
            2 => '已完成',
            3 => '超时',
        ];

        $excel_list = [];
        foreach ($sys_orders as $key => $value) {
            $value['staff_name']            = $value['staff_name'] . '(' . $value['staff_id'] . ')';
            $value['problem_category_name'] = $problem_category_item[$value['sys_category_id']] ?? '';

            // 已评价 == 已完成
            // 超时未反馈 == 已完成
            if ($value['is_evaluate'] || $value['problem_status'] == 3) {
                $value['problem_status'] = 2;
            }

            // 如果问题工单超过7天, 系统未回复, 列表展示为超时状态
            if ($value['problem_status'] == 0 && $value['latest_feedback_time'] <= $before_seven_days_time) {
                $value['problem_status'] = 3;
            }

            // 问题工单状态
            $value['problem_status_text'] = $reply_status_item[$value['problem_status']];

            // 0 值处理
            $value['first_reply_staff_id'] = $value['first_reply_staff_id'] ? $value['first_reply_staff_id'] : '';
            $value['last_deal_staff_id']   = $value['last_deal_staff_id'] ? $value['last_deal_staff_id'] : '';
            $value['first_reply_time']     = $value['first_reply_time'] ? $value['first_reply_time'] : '';
            $value['last_deal_time']       = $value['last_deal_time'] ? $value['last_deal_time'] : '';
            $value['staff_score']          = $value['staff_score'] ? $value['staff_score'] : '';

            // 间隔时长格式转换
            $value['first_reply_interval_time'] = $this->timeSecondsFormatConversion($value['first_reply_interval_time']);

            // 回复期限
            $value['create_time']    = $value['latest_feedback_time'];
            $value['reply_deadline'] = date('Y-m-d H:i:s', strtotime($value['create_time']) + 7 * 86400);

            // 员工所在网点
            $value['store_name'] = '';

            // 员工职位
            $value['job_title'] = '';
            if (isset($staff_base_info_item[$value['staff_id']])) {
                $_staff_base_info = $staff_base_info_item[$value['staff_id']];

                $value['store_name'] = $_staff_base_info['store_name'];
                $value['job_title']  = $_staff_base_info['job_title'];

                // 若手机号是脱敏 或 空值, 则获取新的手机号
                if (empty($value['staff_mobile']) && !empty($_staff_base_info['staff_mobile'])) {
                    $value['staff_mobile'] = $_staff_base_info['staff_mobile'];
                }
            }

            // 工单详情
            $problem_detail     = '';
            $problem_order_list = $ask_order_list[$value['problem_no']] ?? [];
            if (!empty($problem_order_list)) {
                // 排序: 创建时间正序
                $problem_order_list = array_sort($problem_order_list, 'create_time', SORT_ASC);

                // 提取一问一答内容拼接, 换行间隔
                $problem_detail = implode("\n", array_column($problem_order_list, 'order_content'));
            }

            $_value = [
                $value['create_time'],
                $value['staff_name'],
                $value['department_name'],
                $value['store_name'],
                $value['job_title'],
                $value['staff_mobile'],
                $value['problem_category_name'],
                $problem_detail,
                $value['problem_status_text'],
                $value['first_reply_staff_id'],
                $value['first_reply_time'],
                $value['first_reply_interval_time'],
                $value['last_deal_staff_id'],
                $value['last_deal_time'],
                $value['reply_deadline'],
                $value['staff_score'],
                $value['staff_evaluate'],
                $value['staff_evaluate_remark'],
            ];

            $excel_list[] = $_value;
        }
        unset($sys_orders);

        return $excel_list;
    }

    /**
     * 获取所有工单分类
     */
    protected function getOrderCategory()
    {
        $category_name = 'category_name_zh';
        $data          = CeoMailProblemCategoryModel::find([
            'columns' => ['id AS category_id', "$category_name AS category_name", 'parent_id'],
        ])->toArray();

        $result = [];
        foreach ($data as $value) {
            $parent_id = $value['parent_id'];

            if ($parent_id == 0) {
                $result[$value['category_id']] = $value['category_name'];
            } else {
                // 查询父类分类名称
                $parent_category_name = CeoMailProblemCategoryModel::findFirst([
                    'conditions' => 'id = :id:',
                    'bind'       => ['id' => $parent_id],
                    'columns'    => ["$category_name AS category_name"],
                ]);
                $parent_category_name = $parent_category_name ? $parent_category_name->category_name : '';

                $result[$value['category_id']] = $parent_category_name . ' - ' . $value['category_name'];
            }
        }

        return $result;
    }

    /**
     * 时长转换: 秒 -> xx h xx min xx s
     * @param int $seconds
     * @return string $string '10h25min30s'
     */
    protected function timeSecondsFormatConversion(int $seconds)
    {
        if (empty($seconds)) {
            return '';
        }

        $hour   = floor($seconds / 3600);
        $minute = floor(($seconds - $hour * 3600) / 60);
        $second = $seconds - $hour * 3600 - $minute * 60;

        $string = '';
        if ($hour > 0) {
            $string .= $hour . 'h ';
        }

        if ($minute > 0) {
            $string .= $minute . 'm ';
        }

        if ($second) {
            $string .= $second . 's ';
        }

        return trim($string);
    }


    /**
     * 批量 获取员工网点-职位 列表
     * @param array $staff_id_list
     * @return array $result
     */
    protected function getStaffBaseInfoByIds(array $staff_id_list)
    {
        try {
            if (empty($staff_id_list)) {
                return [];
            }

            $builder = $this->modelsManager->createBuilder();
            $builder->columns([
                'staff.staff_info_id AS staff_id',
                'staff.mobile AS staff_mobile',
                'store.name AS store_name',
                'job.job_name AS job_title',
            ]);

            $builder->from(['staff' => HrStaffInfoModel::class]);
            $builder->leftjoin(SysStoreModel::class, 'staff.sys_store_id = store.id', 'store');
            $builder->leftjoin(HrJobTitleModel::class, 'staff.job_title = job.id', 'job');
            $builder->inWhere('staff.staff_info_id', $staff_id_list);

            return $builder->getQuery()->execute()->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 生成Excel
     */
    public function genExcel()
    {
        ini_set('memory_limit', '1024M');

        // Excel表头配置
        $header = [
            '提交时间',     // self::$t['ceo_mail_export_001'], // 提交时间
            '员工工号',     // self::$t['ceo_mail_export_002'], // 员工工号
            '部门',       // self::$t['ceo_mail_export_003'], // 部门
            '网点',       // self::$t['ceo_mail_export_020'], // 网点
            '职位',       // self::$t['ceo_mail_export_021'], // 职位
            '电话',       // self::$t['ceo_mail_export_004'], // 电话
            '问题类型',     // self::$t['ceo_mail_export_005'], // 问题类型
            '问题描述',     // self::$t['ceo_mail_export_006'], // 问题描述
            '回复状态',     // self::$t['ceo_mail_export_016'], // 回复状态
            '首次回复人',    // self::$t['ceo_mail_export_007'], // 首次回复人
            '首次回复时间',   // self::$t['ceo_mail_export_008'], // 首次回复时间
            '首次回复历经时长', // self::$t['ceo_mail_export_015'], // 首次回复历经时长
            '最后处理人',    // self::$t['ceo_mail_export_009'], // 最后处理人
            '最后处理时间',   // self::$t['ceo_mail_export_010'], // 最后处理时间
            '回复期限',     // self::$t['ceo_mail_export_011'], // 回复期限
            '评价结果',     // self::$t['ceo_mail_export_012'], // 评价结果
            '评价内容',     // self::$t['ceo_mail_export_013'], // 评价内容
            '评价备注',     // self::$t['ceo_mail_export_014'], // 评价备注
        ];

        // 工单数据
        $excel_data = $this->getStaffAllProblemOrders();
        $file_name  = "FlashBox_" . date("YmdHis");
        return $this->exportExcel($header, $excel_data, $file_name);
    }

}
