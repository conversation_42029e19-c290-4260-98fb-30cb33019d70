<?php


namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Repository\DepartmentRepository;
use App\Modules\Organization\Services\DepartmentService as OrganizationDepartmentService;

class DepartmentService extends BaseService
{

    /**
     * 可选组织列表
     * @param array $params 查询条件
     * @return array
     */
    public function getDepartmentById(array $params)
    {
        return (new DepartmentRepository())->getSubDepartmentById($params);
    }

    /**
     * 搜索组织列表
     * @param array $params 查询条件
     * @return array
     */
    public function searchDepartment(array $params)
    {
        return (new OrganizationDepartmentService())->search($params);
    }

    /**
     * 根据部门ID获取金蝶费用所属中心
     * @param array $department_id 部门ID
     * @return mixed|string
     */
    public function getDepartmentKingDeePcCode($department_id)
    {
        $department_info = (new DepartmentRepository())->getDepartmentDetail($department_id);
        return $department_info['kingdee_cost_center'] ?? '';
    }

    /**
     * 根据金蝶成本中心关键字模糊筛选成本列表
     * @param string $kingdee_cost_center 金蝶成本中心
     * @return array
     */
    public function getKingDeePcCodeList($kingdee_cost_center)
    {
        return (new DepartmentRepository())->getKingDeePcCodeList($kingdee_cost_center, 'name, kingdee_cost_center');
    }
}
