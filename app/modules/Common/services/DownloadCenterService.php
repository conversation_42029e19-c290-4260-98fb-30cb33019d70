<?php

namespace App\Modules\Common\Services;

use App\Library\BaseService;
use App\Library\Enums\DownloadCenterEnum;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Models\ExcelTaskModel;
use Exception;

class DownloadCenterService extends BaseService
{
    private static $instance;
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 下载列表
     * @param $params
     * @return array
     */
    public function getDownloadList($params)
    {
        $page_size = $params['pageSize'] ?? 20;
        $page_num = $params['pageNum'] ?? 1;
        $total_count = 0;
        $item_list = [];
        try {
            $offset = $page_size * ($page_num - 1);
            $builder_list = $this->getDownloadListBuilder($params);
            $builder_count = $this->getDownloadListBuilder($params);

            $totalCount = $builder_count->columns('COUNT(1) AS count')->getQuery()->execute()->getFirst();
            $total_count = !empty($totalCount) ? $totalCount->count : 0;

            $builder_list->limit($page_size, $offset);
            $list = $builder_list->orderBy('excel_task.created_at desc')->getQuery()->execute()->toArray();

            foreach ($list as $key => $value) {
                $item_list[] =[
                    'id' => $value['id'],
                    'staff_info_id' => $value['staff_info_id'],
                    'file_name' => $value['file_name'],
                    'file_download_path' => !empty($value['file_download_path']) ? $value['file_download_path'] : '',
                    'status' => $value['status'],
                    'status_text' => static::$t->_('download_center_status_' . $value['status']),
                    'type' => $value['type'],
                    'created_at' => $value['created_at'],
                    'finish_at' => $value['finish_at']
                ];
            }

        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return [
            'items' => $item_list,
            'pagination' => [
                'page_num' => $page_num,
                'page_size' => $page_size,
                'total_count' => $total_count,
            ]
        ];
    }

    private function getDownloadListBuilder($params)
    {
        $user = $params['user'];
        $day = date('Y-m-d 23:59:59', strtotime('-30 day'));
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['excel_task' => ExcelTaskModel::class]);
        $builder->where('excel_task.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $user['id']]);
        $builder->andWhere('created_at > :created_at:', ['created_at' => $day]);

        return $builder;
    }

    /**
     * 删除下载
     * @param $params
     * @return bool
     * @throws BusinessException
     */
    public function delDownload($params)
    {
        try {
            $user_id = $params['user']['id'];
            $del_id = $params['id'];
            $down_detail = ExcelTaskModel::findFirst([
                'conditions' => 'id = :id: and staff_info_id = :user_id:',
                'bind' => ['id' => $del_id, 'user_id' => $user_id],
            ]);

            if (empty($down_detail)) {
                throw new BusinessException(static::$t->_('download_center_error_1'), ErrCode::$BUSINESS_ERROR);//未找到要删除的数据
            }

            if ($down_detail->is_deleted == GlobalEnums::IS_DELETED) {
                throw new BusinessException(static::$t->_('download_center_error_2'), ErrCode::$BUSINESS_ERROR);//该数据已经是删除状态
            }

            $down_detail->is_deleted = GlobalEnums::IS_DELETED;
            $down_detail->updated_at = date('Y-m-d H:i:s');
            if ($down_detail->save() === false) {
                $msg = '删除已下载内容失败：' . get_data_object_error_msg($down_detail);
                $this->logger->error(['params' => $params, 'message' => $msg]);

                throw new BusinessException(static::$t->_('download_center_error_3'), ErrCode::$BUSINESS_ERROR);//删除失败，请稍后重试
            }

            return true;
        } catch (BusinessException $e) {
            throw new BusinessException($e->getMessage(), $e->getCode());

        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'message' => $e->getMessage(),
                'file' => $e->getLine(),
                'line' => $e->getLine()
            ]);
        }

        return  false;
    }

    /**
     * 添加下载任务
     *
     * @param int $staff_info_id 用户id
     * @param int $excel_type 业务类型
     * @param array $params 业务参数
     * @return mixed
     */
    public function addDownloadCenter(int $staff_info_id, int $excel_type, array $params)
    {
        $code = ErrCode::$SUCCESS;
        $message = 'success';
        $data = [];

        try {
            // 验证待下载的业务类型配置: 含文件名, 任务名称
            $excel_setting_info = DownloadCenterEnum::$download_center_excel_setting_item[$excel_type] ?? [];
            if (empty($excel_setting_info)) {
                throw new ValidationException(static::$t->_('download_center_biz_type_not_setting'), ErrCode::$VALIDATE_ERROR);
            }

            // 验证是否已存在(防止重复插入)
            $detail = ExcelTaskModel::findFirst([
                'conditions' => 'staff_info_id = :staff_info_id: AND status = :status: AND is_deleted = :is_deleted: AND type = :type:',
                'bind' => [
                    'staff_info_id' => $staff_info_id,
                    'status' => DownloadCenterEnum::TASK_STATUS_PENDING,
                    'is_deleted' => GlobalEnums::IS_NO_DELETED,
                    'type' => $excel_type
                ]
            ]);

            if ($detail) {
                // 下载任务正在执行，请稍等
                throw new ValidationException(static::$t->_('download_center_error_task_exist'), ErrCode::$VALIDATE_ERROR);
            }

            // 下载时的系统语言
            $params['language'] = $params['language'] ?? static::$language;

            // 下载文件命名
            $excel_setting_info['file_name'] = str_replace('{YmdHis}', date('YmdHis'), $excel_setting_info['file_name']);

            // 保存下载任务
            $excel_task_model = new ExcelTaskModel();
            $excel_task_model->executable_path = $this->config->application->appDir . 'cli.php';
            $excel_task_model->action_name = $excel_setting_info['task_name'];
            $excel_task_model->file_name = $excel_setting_info['file_name'];
            $excel_task_model->type = $excel_type;
            $excel_task_model->staff_info_id = $staff_info_id;
            $excel_task_model->args_json = base64_encode(json_encode($params));
            $excel_task_model->created_at = date('Y-m-d H:i:s');
            $excel_task_model->updated_at = date('Y-m-d H:i:s');

            if ($excel_task_model->save() === false) {
                $msg = '下载任务生成失败, 可能的原因是: ' . get_data_object_error_msg($excel_task_model);
                $msg .= '; 接口请求参数: ' . json_encode($params, JSON_UNESCAPED_UNICODE);
                $msg .= '; 待存储数据: ' . json_encode($excel_task_model->toArray(), JSON_UNESCAPED_UNICODE);
                $this->logger->error($msg);

                // 下载任务生成失败，请稍后重试
                throw new ValidationException(static::$t->_('download_center_error_save_error'), ErrCode::$BUSINESS_ERROR);
            }

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage();

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = static::$t->_('retry_later');
            $this->logger->warning('下载中心 - 添加下载任务异常 - ' . $e->getMessage());

        } catch (\Exception $e) {
            $code = ErrCode::$SYSTEM_ERROR;
            $message = static::$t->_('retry_later');
            $this->logger->error('下载中心 - 添加下载任务异常 - ' . $e->getMessage());
        }

        return [
            'code' => $code,
            'message' => $message,
            'data' => $data
        ];
    }

    /**
     * 获取待执行的任务(最早入队的)
     * @param int $task_type
     * @return mixed
     */
    public function getFirstPendingTask(int $task_type)
    {
        return ExcelTaskModel::findFirst([
            'conditions' => 'type = :type: AND is_deleted = :is_deleted: AND status = :status:',
            'bind' => [
                'type' => $task_type,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'status' => DownloadCenterEnum::TASK_STATUS_PENDING
            ],
            'order' => 'id ASC'
        ]);
    }

    /**
     * 更新任务
     * @param object $task_model
     * @param int $task_status
     * @param string $file_download_path
     *
     * @return mixed
     */
    public function saveTask(object $task_model, int $task_status = DownloadCenterEnum::TASK_STATUS_SUCCESS, string $file_download_path = '')
    {
        $task_model->file_download_path = $file_download_path;
        $task_model->status = $task_status;
        $task_model->finish_at = date('Y-m-d H:i:s');
        $task_model->updated_at = date('Y-m-d H:i:s');
        return $task_model->save();
    }

    /**
     * 获取多任务类型待执行的任务(最早入队的)
     * @param array $task_type
     * @return mixed
     */
    public function getMorePendingTask(array $task_type)
    {
        return ExcelTaskModel::find([
            'conditions' => 'type in({type:array}) AND is_deleted = :is_deleted: AND status = :status:',
            'bind' => [
                'type' => $task_type,
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
                'status' => DownloadCenterEnum::TASK_STATUS_PENDING
            ],
            'order' => 'id ASC'
        ]);
    }
}
