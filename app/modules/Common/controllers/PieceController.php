<?php
namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Repository\backyard\PieceRepository;
use Phalcon\Http\ResponseInterface;

/**
 * 管理片区表-控制器层
 * Class PieceController
 * @package App\Modules\Common\Controllers
 */
class PieceController extends BaseController
{
    /**
     * 获取可用的片区列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74447
     * @return \Phalcon\Http\Response|ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());
        $list = PieceRepository::getInstance()->searchPiece($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
}