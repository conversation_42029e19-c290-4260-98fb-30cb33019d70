<?php

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\ImportCenterService;
use Exception;

/**
 * 导入中心
 * Class ImportCenterController
 * @package App\Modules\Common\Controllers
 * @date 2022/9/6
 */
class ImportCenterController extends BaseController
{
    /**
     * @description 获取上传历史列表
     * 请勿追加权限
     * 如需权限，请单独提供接口，复用获取列表方法
     *
     * @throws ValidationException
     * @Token
     */
    public function importHistoryListAction()
    {
        $params         = $this->request->get();
        try {
            $page_validation = [
                'type'      => 'Required|IntGe:1|>>>:type params error',      //导入类型
                'page_num'  => 'Required|IntGe:1|>>>:page_num params error',  //当前页码
                'page_size' => 'Required|IntGe:1|>>>:page_size params error', //每页个数
            ];
            Validation::validate($params, $page_validation);
            $params['user'] = $this->user['id'];
            $result = ImportCenterService::getInstance()->getImportList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 导出结果文件
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     * @throws \App\Library\Exception\BusinessException
     */
    public function downloadAction()
    {
        $id = $this->request->get('id');
        if (empty($id)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'param error');
        }
        $url = ImportCenterService::getInstance()->downloadResultFile($id);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', ['url' => $url]);

    }

    /**
     * 删除
     * @Token
     * @throws \App\Library\Exception\BusinessException
     */
    public function deleteAction()
    {
        $id         = $this->request->get('id');
        if (empty($id)) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, 'param error');
        }
        ImportCenterService::getInstance()->delete($id, $this->user['id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'ok');
    }
}