<?php
/**
 * 省/市/区级联
 */

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Shop\Services\OrderService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class AreaController extends BaseController
{
    /**
     * 省/市/区级联
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/80037
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchAction()
    {
        // 参数校验
        $params = $this->request->get();
        $validate_params = [
            'level' => 'IntGeLe:1,3|>>>:params error[level]',
            'code' => 'Str', //上级code, level=1时, code 可为空
        ];
        Validation::validate($params, $validate_params);

        $data = OrderService::getInstance()::getAreaList($params);
        return $this->returnJson($data['code'], $data['message'], $data['data']['list']);
    }

}
