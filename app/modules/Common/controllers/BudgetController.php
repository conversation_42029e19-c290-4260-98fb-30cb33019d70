<?php
/**
 * 预算相关的公共接口
 */

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\Enums\GlobalEnums;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Budget\Services\BudgetService;
use App\Library\ErrCode;
use App\Modules\Setting\Services\AccountingRuleService;
use \Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class BudgetController extends BaseController
{
    /**
     * 预算科目搜索
     *
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75702
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchObjectListAction()
    {
        $params = $this->request->get();
        $params = trim_array($params);
        Validation::validate($params, ['name' => 'Required|StrLenGe:1']);

        $data = (new BudgetService())->getBudgetObjectListByName($params['name'], $params['is_deleted'] ?? GlobalEnums::IS_NO_DELETED);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 预算科目明细列表
     *
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75802
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchBudgetProductListAction()
    {
        $params = $this->request->get();
        Validation::validate($params, ['budget_id' => 'Required|IntGe:1']);

        $data = (new BudgetService())->getBudgetProductListByBudgetId($params['budget_id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 会计科目搜索
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/75807
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchSubjectsListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['keyword' => 'Required|StrLenGe:0|>>>:param error[keyword]']);
        $data = AccountingRuleService::getInstance()->getAccountingSubjectsList($params['keyword']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取会计科目信息
     * @Token
     *
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76577
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getSubjectsInfoAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'budget_id' => 'Required|IntGt:0|>>>:param error[budget_id]',// 必填
            'product_no' => 'Required|StrLenGeLe:0,10|>>>:param error[product_no]',// 非必填: 可为空字符串
            'organization_type' => 'Required|IntIn:0,1,2|>>>:param error[organization_type]',// 必填
            'cost_department_id' => 'Required|IntGt:0|>>>:param error[cost_department_id]',// 必填
            'organization_type_is_common' => 'Required|IntIn:0,1|>>>:param error[organization_type_is_common]'// 必填
        ]);
        $data = AccountingRuleService::getInstance()->getAccountingSubjectsInfo($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data ? $data : (object)[]);
    }

    /**
     * 预算科目 - 获取特定条件下的预算科目
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/90404
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getBudgetsAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['name' => 'Required|StrLenGe:0']);
        $res    = (new BudgetService())->getBudgets($params);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }
}
