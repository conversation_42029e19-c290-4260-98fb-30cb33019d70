<?php
/**
 * 全局通用的静态枚举配置 V1.0.0 2021.04
 */

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Modules\Common\Services\ContractService;
use App\Modules\Common\Services\EnumsService;
use App\Library\ErrCode;
use App\Modules\Purchase\Services\BaseService;
use \Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class EnumsController extends BaseController
{
    /**
     * @Token
     */
    public function getAction()
    {
        // 获取指定类型的枚举配置: 为空则获取所有全局枚举配置
        $enums_types = $this->request->get('enums_types');
        if (!empty($enums_types) && !is_array($enums_types)) {
            $enums_types = json_decode($enums_types, true);
        }

        $data = EnumsService::getInstance()->getGlobalEnumsSetting(is_array($enums_types) ? $enums_types : []);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }
    /**
     * @Token
     */
    public function get_contracte_numsAction()
    {
        //获取其他合同所需枚举
        $data = (new ContractService())->getContractEnums();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }
    /**
    * 处理类
    * @Token
    * @Date: 2021-09-15 13:54
    * @author: peak pan
    * @return:
    **/
    public function getCountryListAction()
    {
        // 获取指定类型的枚举配置: 为空则获取所有全局枚举配置
        $data = EnumsService::getInstance()->getCountryListV2();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 获取支付信息 - 付款银行 和 账号
     * @Token
     */
    public function getPaymentBankAndAccountAction()
    {
        $data = EnumsService::getInstance()->getPaymentBankAndAccountList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取网点支援费用预算科目ID
     *
     * 说明: 启用新配置结构, 该接口停用
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49963
     * @return Response|ResponseInterface
     */
    public function getStoreApplySupportBudgetIdsAction()
    {
        $data = EnumsService::getInstance()->getSettingEnvValueIds('store_apply_support_budget_ids');
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 获取可自由选择费用所属部门的部门ID
     *
     * 补充: 经与前端确认, 目前仅应用在普通付款模块
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/49969
     * @return Response|ResponseInterface
     */
    public function getChangeCostDepartmentIdsAction()
    {
        $data = EnumsService::getInstance()->getSettingEnvValueIds('change_cost_department_ids');
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 获取税率相关配置
     * vat/sst | wht | 可抵扣税率
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/54259
     */
    public function taxAction()
    {
        try {
            $data = EnumsService::getInstance()->getTaxRateConfig();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $exception) {
            $this->logger->error('获取税的配置异常 - ' . $exception->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server abnormal: ' . $exception->getMessage());
        }
    }

    /**
     * 获取发票类型枚举
     *
     * @Token
     */
    public function invoiceTypeAction()
    {
        try {
            $data = EnumsService::getInstance()->getInvoiceTypeItem();
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
        } catch (Exception $exception) {
            $this->logger->error('获取发票类型的配置异常 - ' . $exception->getMessage());
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server abnormal: ' . $exception->getMessage());
        }
    }

    /**
     * 银行列表
     * @Token
     * @return mixed
     */
    public function bankAction()
    {
        $module = $this->request->get('module');
        $res    = EnumsService::getInstance()->getBankList($module);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 公司列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71142
     *
     * @return mixed
     */
    public function getCompanyListAction()
    {
        $res = EnumsService::getInstance()->getCompanyList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 发票抬头列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/71242
     *
     * @return mixed
     */
    public function getInvoiceHeaderListAction()
    {
        $res = EnumsService::getInstance()->getInvoiceHeaderList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 获取系统币种
     * @Token
     * @return mixed
     */
    public function getSysCurrencyAction()
    {
        $res = EnumsService::getInstance()->getSysCurrencyList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }


    /**
     * 报销-油费额度配置枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88490
     * @return Response|ResponseInterface
     */
    public function getReimbursementFuelQuotaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementFuelQuotaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-境内住宿配置相关枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88502
     * @return Response|ResponseInterface
     */
    public function getReimbursementDomesticAccommodationAreaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementDomesticAccommodationAreaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-境内住宿额度配置相关枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88520
     * @return Response|ResponseInterface
     */
    public function getReimbursementDomesticAccommodationQuotaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementDomesticAccommodationQuotaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-境外住宿区域配置相关枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88538
     * @return Response|ResponseInterface
     */
    public function getReimbursementOverseasAccommodationAreaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementOverseasAccommodationAreaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-境外住宿额度配置相关枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88541
     * @return Response|ResponseInterface
     */
    public function getReimbursementOverseasAccommodationQuotaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementOverseasAccommodationQuotaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 报销-租车费额度配置相关枚举
     * @Token
     * @apidoc https://yapi.flashexpress.pub/project/133/interface/api/88544
     * @return Response|ResponseInterface
     */
    public function getReimbursementCarRentalQuotaEnumsAction()
    {
        $res = EnumsService::getInstance()->getReimbursementCarRentalQuotaEnums();
        return $this->returnJson(ErrCode::$SUCCESS, $this->t->_('success'), $res);
    }

    /**
     * 获取COO公司枚举公共方法
     * 后续各模块统一使用
     * @Token
     */
    public function getCooCostCompanyAction() {
        $data =  (new BaseService())->getCooCostCompany();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }
}
