<?php
namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Modules\Organization\Services\JobService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class JobController extends BaseController
{
    /**
     * 获取职位列表
     *
     * @return Response|ResponseInterface
     */
    public function getJobListAction()
    {
        $list = (new JobService())->getJobOption();
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 模糊搜索职位
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/64882
     * @return Response|ResponseInterface
     */
    public function searchJobAction()
    {
        $params = $this->request->get();
        $list = JobService::getInstance()->searchJob($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
}