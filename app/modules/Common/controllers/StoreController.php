<?php

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\StoreService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class StoreController extends BaseController
{
    /**
     * 网点搜索
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/83264
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchStoreListAction()
    {
        $params = trim_array($this->request->get());

        // 网点类型 + 名称
        $validate_params = [
            'category' => 'Required|>>>:params error[category]',
            'name' => 'Required|>>>:params error[name]',
        ];

        Validation::validate($params, $validate_params);

        $params['page_num'] = GlobalEnums::DEFAULT_PAGE_SIZE;
        $list = (new StoreService())->searchStoreList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * 网点名称 + 全局搜索
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/86564
     * @return Response|ResponseInterface
     */
    public function searchListByNameAction()
    {
        $params = trim_array($this->request->get());

        $params['page_num'] = GlobalEnums::DEFAULT_PAGE_SIZE;
        $list = (new StoreService())->searchAllStoreList($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }

    /**
     * @Token
     * 添加是否含有head office 参数
     * @return Response|ResponseInterface
     */
    public function getStoreListAction()
    {

        $header= $this->request->get('header');
        $param = $this->request->get();
        $param['store_category_item'] = $param['store_category_item'] ?? [];

        $flag = false;
        if ($header) {
            $flag = true;
        }
        $list['items'] = (new StoreService())->getAllStoreList($flag,$param);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }


    public function getStoreListByDepartmentIdAction()
    {
        $id = intval($this->request->get("id"));

        $list = (new StoreService())->getSysStoreListByDepartmentId($id);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取收获地址列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/61507
     * @return Response|ResponseInterface
     */
    public function getAddressListAction()
    {
        $params = $this->request->get();
        $list = (new StoreService())->getAddressList($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取某网点的大区、片区信息
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88814
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStoreRegionAndPieceAction()
    {
        $params = trim_array($this->request->get());
        // 网点编号
        Validation::validate($params, ['store_id' => 'Required|StrLenGeLe:1,10|>>>:params error[store_id]']);

        $data = (new StoreService())->getStoreRegionAndPiece($params['store_id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }


    /**
     * 搜索网点主管
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/89102
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchStoreManagerAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['name' => 'Required|>>>:params error[name]',]);
        $list = (new StoreService())->searchStoreManager($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $list);
    }
}