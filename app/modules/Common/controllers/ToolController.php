<?php
/**
 * 系统开放小工具
 */

namespace App\Modules\Common\Controllers;

use App\Library\Validation\Validation;
use App\Library\ErrCode;
use App\Library\BaseController;
use App\Modules\Common\Services\ToolServer;

class ToolController extends BaseController
{
    /**
     * 获取认证code
     *
     */
    public function getAuthCodeAction()
    {
        $auth_code = ToolServer::getInstance()->getAuthCode();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', ['auth_code' => $auth_code]);
    }

    /**
     * 删除指定缓存
     */
    public function delCacheAction()
    {
        // 校验认证码是否有效
        ToolServer::getInstance()->checkAuthCode();

        // 获取缓存key
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'k' => 'Required|StrLenGeLe:3,128|>>>:params error[k]',
            'type' => 'Required|StrIn:string,list,set,hash|>>>:params error[type=string/list/set/hash]',
        ]);

        $res = ToolServer::getInstance()->delRedisCache($params['k'], $params['type']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 删除指定缓存
     */
    public function getCacheAction()
    {
        // 校验认证码是否有效
        ToolServer::getInstance()->checkAuthCode();

        // 获取缓存key
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'k' => 'Required|StrLenGeLe:3,128|>>>:params error[k]',
            'type' => 'Required|StrIn:string,list,set,hash|>>>:params error[type=string/list/set/hash]',
        ]);

        $res = ToolServer::getInstance()->getRedisCache($params['k'], $params['type']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

    /**
     * 查询 某货主下的入库单
     */
    public function searchInboundAction()
    {
        // 校验认证码是否有效
        ToolServer::getInstance()->checkAuthCode();

        // 获取缓存key
        $params = trim_array($this->request->get());
        Validation::validate($params, [
            'mach_code' => 'Required|StrLenGe:1|>>>:params error[mach_code]',
            'inbound_no' => 'Required|StrLenGe:1|>>>:params error[inbound_no]',
        ]);

        $res = ToolServer::getInstance()->getInbound($params['mach_code'], $params['inbound_no']);
        return $this->returnJson($res['code'], $res['message'], $res['data']);
    }

}
