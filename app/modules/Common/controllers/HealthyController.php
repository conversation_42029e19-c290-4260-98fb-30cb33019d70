<?php
/**
 * 系统健康监测
 */

namespace App\Modules\Common\Controllers;

use App\Library\Enums\SettingEnums;
use App\Library\Exception\BusinessException;
use App\Library\Validation\ValidationException;
use App\Modules\Setting\Services\SystemService;
use Exception;
use App\Library\ErrCode;
use App\Library\BaseController;
use App\Modules\Common\Services\K8sServer;

class HealthyController extends BaseController
{
    /**
     * 应用健康检查
     */
    public function indexAction()
    {
        return $this->returnJson(ErrCode::$SUCCESS, 'ok', null);
    }


    /**
     * 服务健康检查
     */
    public function checkServerAction()
    {
        try {
            K8sServer::getInstance()->checkServerOnline();
            return $this->returnJson(ErrCode::$SUCCESS, 'ok', null);
        } catch (Exception $exception) {
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'server abnormal: ' . $exception->getMessage());
        }
    }

    /**
     * 拉取翻译
     * 说明: 后端任务调用, 提示语无需走词库翻译
     */
    public function pullLangAction()
    {
        $host_name = gethostname();

        $code = ErrCode::$SUCCESS;
        $message = "success[{$host_name}]";

        try {
            // 访问权限基本校验，通过header头固定签名校验
            if ($this->request->getHeader('HTTP_PULL_LANG_AUTH_CODE') != SettingEnums::HTTP_PULL_LANGUAGE_AUTH_CODE) {
                throw new ValidationException('unauthorized access', ErrCode::$VALIDATE_ERROR);
            }

            SystemService::getInstance()->pullCurrentHostLanguage();

            $this->logger->info('oa_pai_auto_pull_lang: ' . $message);

        } catch (ValidationException $e) {
            $code = $e->getCode();
            $message = $e->getMessage() . "[$host_name]";
            $this->logger->notice('oa_pai_auto_pull_lang: ' . $message);

        } catch (BusinessException $e) {
            $code = $e->getCode();
            $message = $e->getMessage() . "[$host_name]";
            $this->logger->warning('oa_pai_auto_pull_lang: ' . $message);

        } catch (Exception $e) {
            $code = $e->getCode();
            $message = $e->getMessage() . "[$host_name]";
            $this->logger->error('oa_pai_auto_pull_lang: ' . $message);
        }

        return $this->returnJson($code, $message);
    }

}
