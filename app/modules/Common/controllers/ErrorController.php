<?php


namespace App\Modules\Common\Controllers;


use App\Library\BaseController;
use App\Library\Response;

class ErrorController extends BaseController
{

    /**
     * 404
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function notFoundAction()
    {
        return $this->returnJson(0,'NOT_FOUND',[], Response::NOT_FOUND);
    }

    /**
     * 401
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function unauthorizedAction()
    {
        return $this->returnJson(0,'UNAUTHORIZED',[], Response::UNAUTHORIZED);
    }

    /**
     * 403
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function forbiddenAction()
    {
        return $this->returnJson(0,'FORBIDDEN',[], Response::FORBIDDEN);
    }

    /**
     * 400
     * @param $e
     * @return \Phalcon\Http\Response|\Phalcon\Http\ResponseInterface
     */
    public function exceptionAction($e)
    {
        return $this->returnJson(0,$e->getMessage(),[], Response::BAD_REQUEST);
    }

}