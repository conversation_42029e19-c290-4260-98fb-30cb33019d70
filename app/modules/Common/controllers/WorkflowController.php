<?php
/**
 * 审批流相关公共接口
 */

namespace App\Modules\Common\Controllers;

use App\Library\ErrCode;
use App\Library\BaseController;
use App\Modules\Setting\Services\WorkflowPayProgressMarkService;

class WorkflowController extends BaseController
{
    /**
     * 获取支付模块-进度标记列表
     * @Token
     */
    public function getPayProgressMarkListAction()
    {
        $data = WorkflowPayProgressMarkService::getInstance()->getEnumsList();
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

}
