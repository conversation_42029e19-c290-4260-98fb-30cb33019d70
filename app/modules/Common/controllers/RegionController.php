<?php
namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\Enums\GlobalEnums;
use App\Library\ErrCode;
use App\Repository\backyard\RegionRepository;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 管理大区表-控制器层
 * Class RegionController
 * @package App\Modules\Common\Controllers
 */
class RegionController extends BaseController
{
    /**
     * 获取可用的大区列表
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/74452
     * @return Response|ResponseInterface
     */
    public function listAction()
    {
        $params = trim_array($this->request->get());

        if (isset($params['is_all']) && $params['is_all']) {
            $page_size = GlobalEnums::DEFAULT_MAX_PAGE_SIZE;
        } else {
            $page_size = GlobalEnums::DEFAULT_PAGE_SIZE;
        }

        $list = RegionRepository::getInstance()->searchRegion($params, $page_size);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
}