<?php

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DownloadCenterService;
use Exception;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

/**
 * 下载中心
 * Class DownloadCenterController
 * @package App\Modules\Common\Controllers
 */
class DownloadCenterController extends BaseController
{
    /**
     * 下载中心列表
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function downloadListAction()
    {
        $params = $this->request->get();
        try {
            $page_validation = [
                'pageNum' => 'Required|IntGe:1|>>>:pageNum params error', //当前页码
                'pageSize' => 'Required|IntGe:1|>>>:pageSize params error', //每页条数
            ];
            Validation::validate($params, $page_validation);
            $params['user'] = $this->user;
            $result = DownloadCenterService::getInstance()->getDownloadList($params);
            return $this->returnJson(ErrCode::$SUCCESS, 'success', $result);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }

    /**
     * 删除指定下载文件
     *
     * @Token
     * @return Response|ResponseInterface
     */
    public function delDownloadAction()
    {
        $params = $this->request->get();
        try {
            $validation = [
                'id' => 'Required|Int|>>>:id params error',
            ];
            Validation::validate($params, $validation);
            $params['user'] = $this->user;
            $result = DownloadCenterService::getInstance()->delDownload($params);
            if ($result) {
                return $this->returnJson(ErrCode::$SUCCESS, 'success', []);
            } else {
                return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error', []);
            }
        } catch (BusinessException $e) {
            return $this->returnJson(ErrCode::$BUSINESS_ERROR, $e->getMessage(), []);
        } catch (ValidationException $e) {
            return $this->returnJson(ErrCode::$VALIDATE_ERROR, $e->getMessage());
        } catch (Exception $e) {
            $this->logger->error([
                'params' => $params,
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->returnJson(ErrCode::$SYSTEM_ERROR, 'error');
        }
    }
}