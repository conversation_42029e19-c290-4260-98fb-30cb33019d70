<?php

namespace App\Modules\Common\Controllers;

use App\Library\ErrCode;
use App\Library\BaseController;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Modules\Common\Services\DepartmentService;
use Phalcon\Http\Response;
use Phalcon\Http\ResponseInterface;

class DepartmentController extends BaseController
{
    /**
     * 可选组织列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69842
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function departmentListAction()
    {
        $params = $this->request->get();
        $validate_list = ['id' => 'IntGe:0|>>>:id params error'];
        Validation::validate($params, $validate_list);
        $res    = (new DepartmentService())->getDepartmentById($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 查询组织列表
     *
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/69847
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchDepartmentListAction()
    {
        $params = $this->request->get();
        $validate_search = ['name' => 'StrLenGe:0|>>>:name params error'];
        Validation::validate($params, $validate_search);
        $data   = (new DepartmentService())->searchDepartment($params);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $data);
    }

    /**
     * 根据部门ID获取金蝶费用所属中心
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76122
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getDepartmentKingDeePcCodeAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['department_id' => 'Required|IntGt:0|>>>:id params error']);
        $res = (new DepartmentService())->getDepartmentKingDeePcCode($params['department_id']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }

    /**
     * 根据金蝶成本中心关键字模糊筛选成本列表
     * @api https://yapi.flashexpress.pub/project/133/interface/api/76647
     * @Token
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getKingDeePcCodeListAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['kingdee_cost_center' => 'Required|StrLenGeLe:1,20|>>>:kingdee_cost_center params error']);
        $res = (new DepartmentService())->getKingDeePcCodeList($params['kingdee_cost_center']);
        return $this->returnJson(ErrCode::$SUCCESS, 'success', $res);
    }
}
