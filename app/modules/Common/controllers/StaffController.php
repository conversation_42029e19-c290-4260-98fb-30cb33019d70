<?php

namespace App\Modules\Common\Controllers;

use App\Library\BaseController;
use App\Library\ErrCode;
use App\Library\Validation\Validation;
use App\Library\Validation\ValidationException;
use App\Repository\HrStaffRepository;
use Phalcon\Http\ResponseInterface;

class StaffController extends BaseController
{
    /**
     * 搜索员工
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/87524
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function searchStaffAction()
    {
        $params = trim_array($this->request->get());

        $list = (new HrStaffRepository())->onlySearchStaff($params);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }

    /**
     * 获取某个员工的基本信息
     * @Token
     * @api https://yapi.flashexpress.pub/project/133/interface/api/88892
     * @return Response|ResponseInterface
     * @throws ValidationException
     */
    public function getStaffBaseInfoAction()
    {
        $params = trim_array($this->request->get());
        Validation::validate($params, ['staff_info_id' => 'Required|IntGt:0']);
        $list = (new HrStaffRepository())->getStaffBaseInfo($params['staff_info_id']);
        return $this->returnJson(ErrCode::$SUCCESS, '', $list);
    }
}