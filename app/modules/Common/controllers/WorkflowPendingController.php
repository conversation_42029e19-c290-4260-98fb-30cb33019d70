<?php
/**
 * 审批流待办数据获取
 */

namespace App\Modules\Common\Controllers;

use App\Library\ErrCode;
use App\Library\ApiClient;
use App\Library\BaseService;
use App\Library\BaseController;
use App\Library\Validation\ValidationException;
use App\Library\Enums\WorkflowPendingEnums;
use App\Modules\Common\Services\WorkflowPendingService;
use App\Modules\User\Services\SmsWhitelistService;

class WorkflowPendingController extends BaseController
{
    /**
     * 获取白名单在当前系统的审批流待办数据
     * 说明: 后端接口之间调用, 校验提示语无需翻译
     */
    public function getWhitelistDataAction()
    {
        // 访问权限基本校验，通过header头固定签名校验
        if ($this->request->getHeader('HTTP_PENDING_AUTH_CODE') != WorkflowPendingEnums::HTTP_GET_DATA_AUTH_CODE) {
            throw new ValidationException('not certified access', ErrCode::$VALIDATE_ERROR);
        }

        // 取指定分钟前的数据
        $data = WorkflowPendingService::getInstance()->getWorkflowPendingData(WorkflowPendingEnums::PENDING_DATA_TIMEOUT_MIN, WorkflowPendingEnums::GET_PENDING_DATA_STRATEGY_ONLY_WHITELIST);
        return $this->returnJson($data['code'], $data['message'], $data['data']);
    }

    /**
     * 同步短信白名单(其他国家接收来自泰国的白名单数据)
     * 说明: 后端接口之间调用, 校验提示语无需翻译
     */
    public function syncStaffWhitelistAction()
    {
        // 访问权限基本校验，通过header头固定签名校验
        if ($this->request->getHeader('HTTP_PENDING_AUTH_CODE') != WorkflowPendingEnums::HTTP_SYNC_WHITELIST_DATA_AUTH_CODE) {
            throw new ValidationException('not certified access', ErrCode::$VALIDATE_ERROR);
        }

        $base64_encode_data = $this->request->get('data');
        if (empty($base64_encode_data)) {
            throw new ValidationException('params error[data is null]', ErrCode::$VALIDATE_ERROR);
        }

        $data = SmsWhitelistService::getInstance()->saveSyncData($base64_encode_data);
        return $this->returnJson($data['code'], $data['message']);
    }

    /**
     * 待办人push
     * 说明: 后端接口之间调用, 校验提示语无需翻译
     */
    public function pushAction()
    {
        $params = $this->request->get();

        // 访问权限基本校验，通过header头固定签名校验
        if (empty($params['sign']) || $this->request->getHeader('HTTP_PENDING_AUTH_CODE') != $params['sign']) {
            throw new ValidationException('invalid access[1]', ErrCode::$VALIDATE_ERROR);
        }

        // 验签
        $api_private_key = md5(WorkflowPendingEnums::WORKFLOW_PENDING_PUSH_API_PRIVATE_KEY);
        $api_sign_params = build_params_sign($params, $api_private_key);
        if ($api_sign_params['sign'] != $params['sign']) {
            throw new ValidationException('invalid access[2]', ErrCode::$VALIDATE_ERROR);
        }

        // 防重
        $base_service = new BaseService();
        $lock_key = md5('workflow_pending_push_api_' . $params['sign']);
        if ($base_service->checkLock($lock_key)) {
            throw new ValidationException('invalid access[3]', ErrCode::$VALIDATE_ERROR);
        }

        // 正常push
        $push_data = unserialize(base64_decode($params['push_data']));
        $push_data['message_scheme'] = 'flashbackyard://fe/html?url=' . urlencode(env('by_url_prefix_new') . '/oa-push-transfer');
        $this->logger->info(['from_outer_push_request' => $params, 'push_data' => $push_data]);

        $ret = new ApiClient('bi_svc', '', 'push_to_staff');
        $ret->setParams([$push_data]);
        $res = $ret->execute();
        if ($res && isset($res['result']) && $res['result'] == true) {
            $code = ErrCode::$SUCCESS;
            $message = 'success';
        } else {
            $code = ErrCode::$BUSINESS_ERROR;
            $message = 'failure';
        }

        // 已发
        $base_service->setLock($lock_key, 1, 300);
        return $this->returnJson($code, $message);
    }
}
