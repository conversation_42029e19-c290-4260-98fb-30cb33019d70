<?php


namespace App\Plugins;

use App\Library\Exception\AuthenticationException;
use App\Library\Exception\AuthorizationException;
use App\Modules\Contract\Services\ElectronicSignService;
use App\Modules\User\Services\UserService;
use App\Traits\TokenTrait;
use Exception;
use Phalcon\Events\Event;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\User\Plugin;

class SecurityPlugin extends Plugin
{
    use TokenTrait;

    public function beforeExecuteRoute(Event $event, Dispatcher $dispatcher)
    {
        $annotations = $this->annotations->getMethod(
            $dispatcher->getControllerClass(),
            method_exists($dispatcher->getControllerClass(),
                $dispatcher->getActiveMethod()) ? $dispatcher->getActiveMethod() : '__call'
        );

        if ($annotations->has('Token')) {
            if (!$this->checkToken()) {
                throw new AuthenticationException('unauthorized');
            }
        }

        if ($annotations->has('Permission')) {
            if (!$this->checkToken()) {
                throw new AuthenticationException('unauthorized');
            }
            $permission = $annotations->get('Permission');

            // 1. 优先判断action级别权限
            $permission_type = 'action';
            $permission_key  = $permission->getNamedParameter('action');
            if (is_null($permission_key)) {
                // 2. 若未设置action，则判断menu级别权限
                $permission_type = 'menu';
                $permission_key  = $permission->getNamedParameter('menu');
            }

            if ($dispatcher->getModuleName() == 'school') {
                $permission_key .= $dispatcher->getParam('school_controller') . '.' . $dispatcher->getParam('school_action');
            }

            if (!$this->checkPermission($permission_key, $permission_type)) {
                throw new AuthorizationException('forbidden');
            }
        }

        // 电子合同签约流程
        if ($annotations->has('SignToken')) {
            if (!$this->checkSignToken()) {
                throw new AuthenticationException('unauthorized');
            }
        }
    }

    /**
     * @return bool
     */
    public function checkToken()
    {
        try {
            $this->response->setHeader('traceid', molten_get_traceid());

            if ($this->request->isEmptyToken()) {
                return false;
            }
            $tokenStr = $this->request->getToken();
            $token    = $this->parseToken($tokenStr);

            // token有效性验证
            if (!$this->validateToken($token)) {
                return false;
            }

            $uid = $token->getClaim('uid');
            $this->request->setUid($uid);
            $this->response->setHeader('UID', $uid);

            // token缓存状态验证
            $platform = $token->getClaim('platform');
            (new UserService())->checkUserToken($uid, $tokenStr, $platform);

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * @return bool
     */
    public function checkSignToken()
    {
        try {
            $this->response->setHeader('traceid', molten_get_traceid());

            if ($this->request->isEmptyToken()) {
                throw new Exception('sign_token 为空');
            }

            // 完整token
            $tokenStr = $this->request->getToken();

            // 解析自定义token结构: 0-真实token; 1-链接自带的甲方标识
            $token_item = explode('^_^', $tokenStr);
            $jwt_token  = $token_item[0] ?? '';
            $sign_key   = $token_item[1] ?? '';
            if (empty($jwt_token) || empty($sign_key)) {
                throw new Exception('jwt_token 或 sign_key 为空');
            }

            $token = $this->parseToken($jwt_token);
            // token有效性验证
            if (!$this->validateToken($token)) {
                throw new Exception('sign_token verify 不通过');
            }

            // 链接标识 是否 与 token中的一致(避免token与访问者身份互串)
            $uid = $token->getClaim('uid');
            if ($uid != $sign_key) {
                throw new Exception('sign_token中的uid 与 sign_key 不一致');
            }

            $this->request->setSignKey($uid);
            $this->response->setHeader('UID', $uid);

            // 验证 sign_token 缓存,
            ElectronicSignService::getInstance()->checkSignToken($uid);

            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * @param $permission_key
     * @param string $permission_type
     * @return bool
     */
    public function checkPermission($permission_key, $permission_type = 'action')
    {
        $us              = new UserService();
        $userPermissions = $us->getUserPermissionKeys($this->request->getUid(), $permission_type);

        if (!in_array($permission_key, $userPermissions, true)) {
            return false;
        }

        return true;
    }
}