<?php


namespace App\Plugins;


use Phalcon\Events\Event;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\User\Plugin;

class DispatchPlugin extends Plugin
{
    /**
     * @param Event $event
     * @param Dispatcher $dispatcher
     */
    public function beforeDispatch(Event $event, Dispatcher $dispatcher)
    {
        $modules = include APP_PATH . "/config/modules.php";
        $module = $dispatcher->getModuleName();
        if ($module) {
            $metadata = $modules[$module]['metadata'];
            $dispatcher->setNamespaceName($metadata['controllersNamespace']);
        }
    }

    /**
     * @param Event $event
     * @param Dispatcher $dispatcher
     * @param array $forward
     */
    public function beforeForward(Event $event, Dispatcher $dispatcher, array $forward)
    {
        if (isset($forward['module'])) {
            $modules = include APP_PATH . "/config/modules.php";
            $metadata = $modules[$forward['module']]['metadata'];
            $controllersNamespace = $metadata['controllersNamespace'];
            $dispatcher->setModuleName($forward['module']);
        } else {
            $controllersNamespace = $forward['namespace'];
        }

        $dispatcher->setNamespaceName($controllersNamespace);
    }

}