<?php

namespace App\Plugins;

use App\Library\Exception\AuthenticationException;
use App\Library\Exception\AuthorizationException;
use App\Library\Exception\BusinessException;
use App\Library\Response;
use App\Library\Validation\ValidationException;
use Exception;
use Phalcon\Events\Event;
use Phalcon\Mvc\Dispatcher;
use Phalcon\Mvc\Dispatcher\Exception as DispatchException;
use Phalcon\Mvc\User\Plugin;

/**
 * NotFoundPlugin
 *
 * Handles not-found controller/actions
 */
class ExceptionPlugin extends Plugin
{
    /**
     * This action is executed before perform any action in the application
     *
     * @param Event $event
     * @param Dispatcher $dispatcher
     * @param Exception $exception
     * @return void
     */
	public function beforeException(Event $event, Dispatcher $dispatcher, Exception $exception)
	{
	    // 接口响应的状态码和信息, 可暴露给用户的
        $code = $exception->getCode();
        $message = $exception->getMessage();
        $data = null;

        $logger_level  = '';
        $is_internal_error = false;

        if ($exception instanceof DispatchException) {
            if (in_array($code, [Dispatcher::EXCEPTION_HANDLER_NOT_FOUND, Dispatcher::EXCEPTION_ACTION_NOT_FOUND])) {
                $http_code = Response::NOT_FOUND;
            } else {
                $http_code = Response::BAD_REQUEST;
                $logger_level = 'info';
            }

            $message = RUNTIME == 'dev' ? $message : Response::$codes[$http_code];

        } else if ($exception instanceof AuthenticationException) {
            $http_code = Response::UNAUTHORIZED;
            $message = !empty($message) ? $message : Response::$codes[$http_code];

        } else if ($exception instanceof AuthorizationException) {
            $http_code = Response::FORBIDDEN;
            $message = !empty($message) ? $message : Response::$codes[$http_code];

        } else if ($exception instanceof ValidationException) {
            $http_code = Response::OK;
            $logger_level = 'info';

        } else if ($exception instanceof BusinessException) {
            $http_code = Response::OK;
            $logger_level = 'warning';
            $is_internal_error = true;

        } else {
            $http_code = Response::INTERNAL_SERVER_ERROR;
            $logger_level = 'error';
            $is_internal_error = true;
        }

        // 系统内部错误
        if ($is_internal_error) {
            $message = RUNTIME == 'dev' ? $message : Response::$codes[Response::INTERNAL_SERVER_ERROR];
            $data = RUNTIME == 'dev' ? $exception->getTraceAsString() : null;
        }

        // 接口返回信息
        $api_return_info = [
            'code'    => $code,
            'message' => $message,
            'data'    => $data,
            'tid'     => molten_get_traceid(),
        ];

        // 指定的异常类型记录相应级别的日志
        if (!empty($logger_level)) {
            $logger_content = [
                'request_info' => $this->request->get(),
                'response_info' => [
                    'api_return_info' => $api_return_info,
                    'exception_info' => [
                        'message' => $exception->getMessage(),
                        'file' => $exception->getFile(),
                        'line' => $exception->getLine(),
                        'trace' => $exception->getTraceAsString()
                    ]
                ]
            ];

            $this->logger->$logger_level(['exception_plugin_log' => $logger_content]);
        }

        $this->response->setStatusCode($http_code);
        $this->response->setJsonContent($api_return_info);
        $this->response->send();
        exit();
	}
}
