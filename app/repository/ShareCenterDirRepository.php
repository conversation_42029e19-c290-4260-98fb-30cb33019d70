<?php

namespace App\Repository;

use App\Modules\Share\Models\ShareCenterDir;

/**
 * 信息共享中心文件夹相关
 * Class ShareCenterDirRepository
 * @package App\Repository
 */
class ShareCenterDirRepository extends BaseRepository
{
    /**
     *
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getShareCenterDirList(string $levelCode,string $dirName,string $curLanguage)
    {
        if (!empty($dirName)) {
            $fields = [
                'zh-cn' => 'name_cn',
                'en'    => 'name_en',
                'th'    => 'name_th',
            ];
            $field  = $fields[strtolower($curLanguage)];
            return ShareCenterDir::find([
                'conditions' => "is_delete = :is_delete: AND {$field} LIKE :{$field}:",
                'bind'       => [
                    'is_delete' => ShareCenterDir::NOT_DELETE,
                    $field      => $dirName.'%',
                ],
                'order'      => 'is_default DESC, id ASC',
            ]);
        }

        if ($levelCode) {
            return ShareCenterDir::find([
                'conditions' => 'is_delete = :is_delete: AND level_code LIKE :level_code:',
                'bind'       => [
                    'is_delete'  => ShareCenterDir::NOT_DELETE,
                    'level_code' => $levelCode.'%',
                ],
                'order'      => 'is_default DESC, id ASC',
            ]);
        }

        return ShareCenterDir::find([
            'conditions' => 'is_delete = :is_delete:',
            'bind'       => [
                'is_delete' => ShareCenterDir::NOT_DELETE,
            ],
            'order'      => 'is_default DESC, id ASC',
        ]);
    }

    /**
     * Notes: 获取一个文件夹
     * @param string $levelCode
     * @return ShareCenterDir|false
     */
    public function getInfoByLevelCode(string $levelCode)
    {
        return ShareCenterDir::findFirst([
            'conditions' => 'level_code = :level_code: AND is_delete = :is_delete:',
            'bind'       => [
                'level_code' => $levelCode,
                'is_delete'  => ShareCenterDir::NOT_DELETE,
            ],
        ]);
    }

    /**
     * Notes: 获取文件夹和其下级文件夹
     * @param string $levelCode
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getDirListByLevelCode(string $levelCode)
    {
        return ShareCenterDir::find([
            'conditions' => 'level_code LIKE :level_code: AND is_delete = :is_delete:',
            'bind'       => [
                'level_code' => $levelCode.'%',
                'is_delete'  => ShareCenterDir::NOT_DELETE,
            ],
        ]);
    }

    /**
     * Notes: 添加文件夹名称查重
     * @param string $nameEn
     * @param string $nameTh
     * @param string $nameCn
     * @param $level
     * @return ShareCenterDir|false
     */
    public function getInfoByName(string $nameEn, string $nameTh, string $nameCn, $level)
    {
        return ShareCenterDir::findFirst([
            'conditions' => 'name_en = :name_en: AND name_th = :name_th: AND name_cn = :name_cn: AND level = :level: AND is_delete = :is_delete:',
            'bind'       => [
                'name_en'   => $nameEn,
                'name_th'   => $nameTh,
                'name_cn'   => $nameCn,
                'level'     => $level,
                'is_delete' => ShareCenterDir::NOT_DELETE,
            ],
        ]);
    }

    /**
     * Notes: 越南添加文件夹名称查重
     * @param string $nameEn
     * @param string $nameVn
     * @param string $nameCn
     * @param $level
     * @return ShareCenterDir|false
     */
    public function getInfoByNameVN(string $nameEn, string $nameVn, string $nameCn, $level)
    {
        return ShareCenterDir::findFirst([
            'conditions' => 'name_en = :name_en: AND name_vn = :name_vn: AND name_cn = :name_cn: AND level = :level: AND is_delete = :is_delete:',
            'bind'       => [
                'name_en'   => $nameEn,
                'name_vn'   => $nameVn,
                'name_cn'   => $nameCn,
                'level'     => $level,
                'is_delete' => ShareCenterDir::NOT_DELETE,
            ],
        ]);
    }
}