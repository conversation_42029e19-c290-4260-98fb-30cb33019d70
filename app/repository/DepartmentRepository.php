<?php

namespace App\Repository;

use App\Library\Enums\GlobalEnums;
use App\Library\Enums\OrganizationDepartmentEnums;
use App\Models\backyard\SysDepartmentModel;
use Exception;

/**
 * 部门相关方法
 * Class DepartmentRepository
 * @package App\Repository
 */
class DepartmentRepository extends BaseRepository
{
    /**
     * 获取指定状态部门
     * @return array
     */
    public function getDepartmentList()
    {
        return SysDepartmentModel::find([
            'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
            'conditions' => 'deleted = 0',
        ])->toArray();
    }

    /**
     * 获取所有部门
     *
     * @param string $columns 获取指定字段列表
     * @return array
     */
    public function getAllDepartmentList(string $columns = '')
    {
        if (empty($columns)) {
            $columns = 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name,deleted';
        }

        return SysDepartmentModel::find([
            'columns' => $columns,
        ])->toArray();
    }

    /**
     * 获取指定id部门列表
     *
     * @param array $department_ids
     * @param int $deleted 0-未删除(默认); 1-已删除; 2-全部
     * @return array
     */
    public function getDepartmentByIds($department_ids = [], $deleted = 0)
    {
        $department_list = [];
        $department_ids = array_values(array_unique(array_filter($department_ids)));
        if (!empty($department_ids)) {
            $conditions = "id IN ({ids:array})";
            $bind = ['ids' => $department_ids];

            if (in_array($deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
                $conditions .= " AND deleted = :deleted:";
                $bind['deleted'] = $deleted;
            }

            $department_list = SysDepartmentModel::find([
                'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name,deleted,sap_company_id',
                'conditions' => $conditions,
                'bind' => $bind,
            ])->toArray();

            $department_list = array_column($department_list, null, 'id');
        }
        return $department_list;
    }

    /**
     * 根据部门id获取当前部门及子部门数据
     * @param $department_id
     * @param int $deleted 0-未删除(默认); 1-已删除; 2-全部
     * @return array
     */
    public function getDepartmentSubListByIds($department_id, $deleted = 0) {
        $detail = $this->getDepartmentDetail($department_id, $deleted);
        $department_list = [];
        if(!empty($detail)) {
            $department_list = SysDepartmentModel::find([
                'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
                "conditions" => ($deleted == 2 ? '' : "deleted = {$deleted} and ") . "ancestry_v3 like :ancestry_v3: or id = :id:",
                'bind' => [
                    "ancestry_v3" => $detail['ancestry_v3'] . '/%',
                    'id' => $department_id
                ],
            ])->toArray();
        }
        return $department_list;
    }

    /**
     * 根据工号查找负责部门列表 不包括子部门数据
     * @param $manager_id
     * @return array
     */
    public function getDepartmentListByManagerId($manager_id, $deleted = 0) {
        return SysDepartmentModel::find([
            'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
            "conditions" => ($deleted == 2 ? '' : "deleted = {$deleted} and ") ." manager_id = :manager_id:",
            'bind' => [
                "manager_id" => $manager_id
            ],
        ])->toArray();
    }

    /**
     * 根据manager_id获取负责的部门列表及子部门
     * @param $manager_id
     * @return array
     */
    public function getDepartmentAllListByManagerId($manager_id)
    {
        $department_list = [];
        try {
            if (!empty($manager_id)) {
                $list = SysDepartmentModel::find([
                    'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
                    "conditions" => "deleted = 0 and manager_id = :manager_id:",
                    'bind' => [
                        "manager_id" => $manager_id
                    ],
                ])->toArray();

                if (!empty($list)) {

                    $list_department_ids = array_column($list, 'id');
                    $where = ['id in ({ids:array})'];
                    $bind_where['ids'] = $list_department_ids;
                    foreach ($list as $key => $value) {
                        $where[] = ' ancestry_v3 like :ancestry_v3_'.$key.': ';
                        $bind_where["ancestry_v3_".$key] = $value['ancestry_v3'] . '/%';
                    }

                    if(!empty($where) && !empty($bind_where)) {
                        $where_str = implode(" or ", $where);
                        $department_list = SysDepartmentModel::find([
                            'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name',
                            "conditions" => "deleted = 0 and (" . $where_str . ")",
                            'bind' => $bind_where,
                        ])->toArray();
                    }
                }
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['manager_id' => $manager_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $department_list;
    }

    /**
     * 部门详情
     * @param $department_id
     * @param int $deleted 0-未删除(默认); 1-已删除; 2-全部
     * @return array
     */
    public function getDepartmentDetail($department_id, $deleted = 0) {
        $department = [];
        try {
            if(!empty($department_id)) {
                $detail = SysDepartmentModel::findFirst([
                    'columns' => 'id,name,ancestry,ancestry_v3,type,level,manager_id,manager_name,manager_phone,manager_position_state,assistant_id,assistant_name,company_id,company_name,sap_company_id,kingdee_cost_center',
                    "conditions" => ($deleted == 2 ? '' : "deleted = {$deleted} and ") . "id = :department_id:",
                    "bind" => [
                        "department_id" => $department_id
                    ]
                ]);
                $department = !empty($detail) ? $detail->toArray() : [];
            }
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['department_id' => $department_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $department;
    }

    /**
     * 工号是否是部门负责人
     * @param $staff_info_id
     * @return bool
     */
    public function departmentManagerExists($staff_info_id) {
        $count = 0;
        try {
            $count = SysDepartmentModel::count([
                "deleted = 0 and manager_id = :manager_id:",
                'bind' => [
                    "manager_id" => $staff_info_id
                ],
            ]);
        } catch (Exception $e) {
            $this->getDI()->get('logger')->error([
                'params' => ['staff_info_id' => $staff_info_id],
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }

        return $count > 0 ? true : false;
    }

    /**
     * Notes: 获取某个部门的直属下级部门
     * @param $departmentId
     * @param string $columns
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getAncestryDepartmentsById($departmentId, $columns = '*')
    {
        return SysDepartmentModel::find([
            'conditions' => 'ancestry = :ancestry: and deleted = '. GlobalEnums::IS_NO_DELETED,
            'bind'       => [
                'ancestry' => $departmentId,
            ],
            'columns'    => $columns,
        ]);
    }

    /**
     * Notes: 根据上级ID获取其下级部门的ID，并且用上级ID分好组
     *
     * @param $ancestryDepId
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getAncestryDepartmentsByAncestryId($ancestryDepId)
    {
        return SysDepartmentModel::find([
            'conditions' => 'ancestry in ({ancestry:array}) and deleted = '.GlobalEnums::IS_NO_DELETED,
            'bind'       => [
                'ancestry' => $ancestryDepId,
            ],
            'columns'    => 'GROUP_CONCAT(id) as ids,ancestry',
            'group'      => 'ancestry',
        ]);
    }

    /**
     * Notes: 部门搜索，id like查询
     * @param  $searchKey
     * @param $is_deleted
     * @param string $columns
     * @param int $limit
     * @param string $order
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getListLikeId($searchKey, $is_deleted, $columns = '*', $limit = 20, $order = 'name asc')
    {
        return SysDepartmentModel::find([
            'conditions' => 'id like :cond:' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = 0' : ''),
            'bind'       => [
                'cond' => '%'.$searchKey.'%',
            ],
            'columns'    => $columns,
            'limit'      => $limit,
            'order'      => $order,
        ]);
    }

    /**
     * Notes: 部门搜索，name like查询
     * @param $searchKey
     * @param $is_deleted
     * @param string $columns
     * @param int $limit
     * @param string $order
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getListLikeName($searchKey, $is_deleted, $columns = '*', $limit = 20, $order = 'name asc')
    {
        return SysDepartmentModel::find([
            'conditions' => 'name like :cond:' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = 0' : ''),
            'bind'       => [
                'cond' => '%'.$searchKey.'%',
            ],
            'columns'    => $columns,
            'limit'      => $limit,
            'order'      => $order,
        ]);
    }

    /**
     * Notes: 根据部门ID获取部门信息
     * @param $departmentIds
     * @param $columns
     * @param $is_deleted
     * @return \Phalcon\Mvc\Model\ResultsetInterface
     */
    public function getDepByIds($departmentIds, $columns = '*', $is_deleted = GlobalEnums::IS_NO_DELETED)
    {
        return SysDepartmentModel::find([
            'conditions' => 'id in ({id:array})' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = 0' : ''),
            'bind'       => [
                'id' => $departmentIds,
            ],
            'columns'    => $columns,
        ]);
    }

    /**
     * Notes: 获取最顶级的部门 （id=999）
     * @return SysDepartmentModel|false
     */
    public function getTopDepartment()
    {
        return SysDepartmentModel::findFirst([
            'conditions' => 'ancestry is null and deleted = 0',
        ]);
    }

    /**
     * 获取员工管辖区域
     * @return void
     */
    public function getSubDepartmentById(array $paramIn = []): array
    {
        $id = $paramIn['id'] ?? 0;
        $is_deleted = isset($paramIn['is_deleted']) ? $paramIn['is_deleted'] : GlobalEnums::IS_NO_DELETED;
        if (empty($id)) {
            return [
                [
                    'id'            => (string)GlobalEnums::TOP_DEPARTMENT_ID,
                    'name'          => GlobalEnums::TOP_DEPARTMENT_NAME,
                    'deleted' => GlobalEnums::IS_NO_DELETED,
                    'isHasChildren' => true
                ]
            ];
        } else {
            //获取当前id对应的部门信息
            $current_dept_info_bind['id'] = $id;
            if ($is_deleted == GlobalEnums::IS_NO_DELETED) {
                $current_dept_info_bind['is_deleted'] = GlobalEnums::IS_NO_DELETED;
            }
            $currentDeptInfo = SysDepartmentModel::findFirst([
                'conditions' => 'id = :id:' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = :is_deleted:' : ''),
                'bind'       => $current_dept_info_bind,
                'columns'    => 'id,name,deleted'
            ]);
            if (empty($currentDeptInfo)) {
                return [];
            }

            //指定id对应的子部门
            $child_info_bind['ancestry'] = $id;
            if ($is_deleted == GlobalEnums::IS_NO_DELETED) {
                $child_info_bind['is_deleted'] = GlobalEnums::IS_NO_DELETED;
            }
            $childIdInfo = SysDepartmentModel::find([
                'conditions' => 'ancestry = :ancestry:' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = :is_deleted:' : ''),
                'bind'       => $child_info_bind,
                'columns'    => 'id,name,deleted'
            ])->toArray();
            $childIds    = array_column($childIdInfo, 'id');
            if (empty($childIds)) {
                return [];
            }

            $child_detail_bind['ancestry'] = $childIds;
            if ($is_deleted == GlobalEnums::IS_NO_DELETED) {
                $child_detail_bind['is_deleted'] = GlobalEnums::IS_NO_DELETED;
            }
            $childDetail = SysDepartmentModel::find([
                'conditions' => 'ancestry in ({ancestry:array})' . ($is_deleted == GlobalEnums::IS_NO_DELETED ? ' and deleted = :is_deleted:' : ''),
                'bind'       => $child_detail_bind,
                'columns'    => 'GROUP_CONCAT(id) as ids,ancestry',
                'group'      => 'ancestry'
            ])->toArray();
            $childDetail = array_column($childDetail, 'ids', 'ancestry');

            foreach ($childIdInfo as &$v) {
                $v['ancestry_name'] = $currentDeptInfo->name ?? '';
                $v['ancestry']      = (string)$id;
                $v['isHasChildren'] = isset($childDetail[$v['id']]);
            }
            return $childIdInfo;
        }
    }

    /**
     * 获取公司列表
     * @return array
     */
    public function getCompanyList()
    {
        return SysDepartmentModel::find([
                'conditions' => 'type = :type: AND deleted = :deleted:',
                'bind'       => [
                    'type' => OrganizationDepartmentEnums::ORGANIZATION_BU_TYPE,
                    'deleted' => GlobalEnums::IS_NO_DELETED
                ],
                'columns'    => ['id', 'name'],
                'order' => 'name ASC'
            ])->toArray();
    }

    /**
     * 根据金蝶成本中心关键字模糊筛选成本列表
     * @param string $searchKey 金蝶成本中心
     * @param string $columns 查询的字段
     * @param int $limit 限制条数
     * @param string $order 排序规则
     * @return mixed
     */
    public function getKingDeePcCodeList($searchKey, $columns = '*', $limit = GlobalEnums::DEFAULT_PAGE_SIZE, $order = 'id asc')
    {
        return SysDepartmentModel::find([
            'conditions' => 'kingdee_cost_center like :cond: and deleted = :deleted:',
            'bind' => [
                'deleted' => GlobalEnums::IS_NO_DELETED,
                'cond' => '%' . $searchKey . '%',
            ],
            'columns' => $columns,
            'limit' => $limit,
            'order' => $order,
        ])->toArray();
    }

    /**
     * 获取部门链中的一级部门
     * @param string $ancestry_v3
     * @return array
     */
    public function getFirstLevelDepartmentByAncestryV3(string $ancestry_v3 = '',$deleted = 0)
    {
        $department = [];

        $ancestry_v3_item = array_filter(explode('/', $ancestry_v3));
        if (!empty($ancestry_v3_item)) {
            $detail = SysDepartmentModel::findFirst([
                'columns' => 'id, name, ancestry_v3, type, level, manager_id, assistant_id',
                'conditions' => 'id IN ({dept_ids:array}) AND level = :first_level: AND type IN ({dept_types:array})  ' .  ($deleted == 2 ? '' : " AND deleted = $deleted") ,
                'bind' => [
                    'dept_ids' => array_values($ancestry_v3_item),
                    'first_level' => OrganizationDepartmentEnums::ORGANIZATION_LEVEL_1,
                    'dept_types' => [OrganizationDepartmentEnums::ORGANIZATION_BU_DEPARTMENT_TYPE, OrganizationDepartmentEnums::ORGANIZATION_CLEVEL_DEPARTMENT_TYPE],
                ]
            ]);

            $department = !empty($detail) ? $detail->toArray() : [];
        }
        return $department;
    }

    /**
     * 根据多个部门获取部门下的子部门 包含当前部门
     * @param array $department_ids
     * @return array
     **/
    public function getManyDepartmentSonList(array $department_ids = [])
    {
        if (empty($department_ids)) {
            return [];
        }
        $all_dept_list      = $this->getDepartmentList();
        $all_dept_list = array_column($all_dept_list, 'ancestry_v3', 'id');
        $dept_ids = $sub_dept_ids = [];
        foreach ($department_ids as $dept) {
            if(empty($all_dept_list[$dept])) {
                continue;
            }
            $dept_ids[]   = (int)$dept;
            $sub_dept_ids = get_sub_department_ids($all_dept_list[$dept], $all_dept_list);
            $dept_ids     = array_merge($dept_ids, $sub_dept_ids);
        }
        return array_values(array_unique($dept_ids));
    }

    /**
     * 获取指定级别的部门负责人
     *
     * @param int $level
     * @return array
     */
    public function getDepartmentManagerIds(int $level = 1)
    {
        $manager_ids = SysDepartmentModel::find([
            'conditions' => 'level = :level: AND type IN ({types:array}) AND deleted = :deleted:',
            'bind' => ['level' => $level, 'types' => [1, 2, 3], 'deleted' => GlobalEnums::IS_NO_DELETED],
            'columns' => ['manager_id']
        ])->toArray();

        return !empty($manager_ids) ? array_values(array_unique(array_filter(array_column($manager_ids, 'manager_id')))) : [];
    }

    /**
     * 获取指定名称部门列表
     * @param array $department_names 部门名称
     * @param int $deleted 0-未删除(默认); 1-已删除; 2-全部
     * @return array
     */
    public function getDepartmentByNames($department_names = [], $deleted = 0)
    {
        $department_list = [];
        $department_names = array_values(array_unique(array_filter($department_names)));
        if (!empty($department_names)) {
            $conditions = 'name IN ({names:array})';
            $bind = ['names' => $department_names];

            if (in_array($deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
                $conditions .= ' AND deleted = :deleted:';
                $bind['deleted'] = $deleted;
            }

            $department_list = SysDepartmentModel::find([
                'columns' => 'id, name, ancestry_v3, sap_cost_center',
                'conditions' => $conditions,
                'bind' => $bind,
            ])->toArray();

            $department_list = array_column($department_list, null, 'name');
        }
        return $department_list;
    }
}
