<?php

namespace App\Repository;

use App\Library\Enums\GlobalEnums;
use App\Models\backyard\HoldStaffManageModel;

/**
 * 员工相关基础方法
 * Class HrStaffRepository
 * @package App\Repository
 */
class HoldStaffManageRepository extends BaseRepository
{

    /**
     * 获取hold的员工数据
     * @param array $params
     * @param array $field
     * @return array
     */
    public function getHoldStaffList(array $params, array $field = []): array
    {
        if (empty($params) || empty($params['staff_info_ids'])) {
            return [];
        }

        $columns = !empty($field) ? implode(', ', $field) : "*";
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(HoldStaffManageModel::class);
        $builder->andWhere('is_delete =  :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);
        $builder->inWhere('staff_info_id', $params['staff_info_ids']);

        if (!empty($params['release_state'])) {
            $builder->andWhere('release_state = :release_state:', ['release_state' => $params['release_state']]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

}