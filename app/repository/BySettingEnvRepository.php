<?php

namespace App\Repository;

use App\Modules\Common\Models\BySettingEnvModel;

/**
 ** @package App\Repository
 */
class BySettingEnvRepository extends BaseRepository
{
    /**
     * 获取多个by setting env 配置
     * @param array $codes
     * @param bool $useIndex
     * @return array
     */
    public static function multiSetVal(array $codes, bool $useIndex = true): array
    {
        $result = [];
        if (!empty($codes)) {
            $result = BySettingEnvModel::find([
                'columns'    => 'code,set_val',
                "conditions" => "code IN  ({codes:array})",
                'bind'       => [
                    "codes" => $codes,
                ],
            ])->toArray();

            if ($useIndex) {
                $result = array_column($result, 'set_val', 'code');
            }
        }
        return $result;
    }

}