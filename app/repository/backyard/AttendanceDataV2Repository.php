<?php

namespace App\Repository\backyard;

use App\Models\backyard\AttendanceDataV2Model;
use App\Repository\BaseRepository;

class AttendanceDataV2Repository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取指定员工指定日期范围的打卡天数
     * @param $staff_id
     * @param $start_date
     * @param $end_date
     * @return array
     */
    public function getDaysByStaffRangeDate($staff_id, $start_date, $end_date)
    {
        if (empty($staff_id) || empty($start_date) || empty($end_date)) {
            return 0;
        }

        $result = AttendanceDataV2Model::findFirst([
            'conditions' => 'staff_info_id = :staff_info_id: AND stat_date BETWEEN :start_date: AND :end_date:',
            'bind' => ['staff_info_id' => $staff_id, 'start_date' => $start_date, 'end_date' => $end_date],
            'columns'    => [
                'staff_info_id',
                'SUM(attendance_time) AS total_attendance_time'
            ],
        ]);

        return !empty($result->total_attendance_time) ? ($result->total_attendance_time / 10) : 0;
    }

}
