<?php

namespace App\Repository\backyard;

use App\Models\backyard\StaffResignModel;
use App\Repository\BaseRepository;

/**
 * by离职申请
 * Class StaffResignRepository
 * @package App\Repository\oa
 * @date 2023/5/11
 */
class StaffResignRepository extends BaseRepository
{
    //待审批
    public static $audit_status_pending = 1;
    //审批通过
    public static $audit_status_approved = 2;
    //超时
    public static $audit_status_timeout = 5;
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取不是审批通过状态的id
     * @param $staff_resign_ids
     * @return mixed
     * @date 2023/5/11
     */
    public function getNotApproveIds($staff_resign_ids)
    {
        $staff_resign = StaffResignModel::find([
            'columns' => 'resign_id',
            'conditions' => 'resign_id in ({resign_ids:array}) AND status not in ({status:array})',
            'bind' => [
                'resign_ids' => array_values($staff_resign_ids),
                'status' => [self::$audit_status_approved, self::$audit_status_timeout]
            ]
        ])->toArray();
        return $staff_resign;
    }

    /**
     * 获取单条数据
     * @param $staff_resign_id
     * @return array
     * @date 2023/5/11
     */
    public function getOneById($staff_resign_id)
    {
        $staff_resign = StaffResignModel::findFirst([
            'columns' => 'resign_id, status',
            'conditions' => 'resign_id = :resign_id:',
            'bind' => [
                'resign_id' => $staff_resign_id
            ]
        ]);
        $staff_resign_info = $staff_resign ? $staff_resign->toArray() : [];
        return $staff_resign_info;
    }

    /**
     * 通过员工id获取有效的离职数据
     * @param $staff_ids
     * @return array
     */
    public function getDataByStaffIds($staff_ids)
    {
        if (empty($staff_ids)) {
            return [];
        }
        //查所有员工的最新id
        $resign_ids = StaffResignModel::find([
            'columns' => 'MAX(resign_id) as resign_id',
            'conditions' => 'submitter_id in ({submitter_ids:array}) AND status in ({status:array})',
            'bind' => [
                'submitter_ids' => array_values($staff_ids),
                'status' => [self::$audit_status_pending, self::$audit_status_approved, self::$audit_status_timeout]
            ],
            'group' => 'submitter_id'
        ])->toArray();
        if (empty($resign_ids)) {
            return [];
        }
        //用最新id查离职申请数据
        $resign_ids = array_column($resign_ids, 'resign_id');
        return StaffResignModel::find([
            'conditions' => 'resign_id in ({resign_ids:array}) AND status in ({status:array})',
            'bind' => [
                'resign_ids' => array_values($resign_ids),
                'status' => [self::$audit_status_pending, self::$audit_status_approved, self::$audit_status_timeout]
            ]
        ])->toArray();
    }
}
