<?php

namespace App\Repository\backyard;

use App\Models\backyard\HrJobDepartmentRelationModel;
use App\Models\backyard\HrStaffInfoModel;
use App\Repository\BaseRepository;

class HrJobDepartmentRelationRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }


    /**
     * 获取部门职位对应的职位性质
     * @param $department_id
     * @param $job_id
     * @return mixed
     */
    public function getDepartmentJobInfo($department_id, $job_id)
    {
        $model = HrJobDepartmentRelationModel::findFirst([
            'conditions' => 'department_id = :department_id: AND job_id = :job_id:',
            'bind'       => ['department_id' => $department_id, 'job_id' => $job_id],
            'columns'    => ['position_type'],
        ]);

        return $model ? $model->toArray() : [];
    }

    /**
     * 获取员工职位性质
     *
     * @param $staff_id
     * @return mixed
     */
    public function getStaffPositionInfo($staff_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['rel.position_type', 'staff.job_title_grade_v2']);
        $builder->from(['staff' => HrStaffInfoModel::class]);
        $builder->leftjoin(HrJobDepartmentRelationModel::class, 'rel.department_id = staff.node_department_id AND rel.job_id = staff.job_title', 'rel');
        $builder->where('staff.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_id]);
        $model = $builder->getQuery()->execute()->getFirst();
        return !empty($model) ? $model->toArray() : [];
    }

}
