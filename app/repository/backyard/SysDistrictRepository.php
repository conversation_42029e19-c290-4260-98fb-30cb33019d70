<?php
namespace App\Repository\backyard;

use App\Models\backyard\SysDistrictModel;
use App\Repository\BaseRepository;

/**
 * 区信息
 * Class SysDistrictRepository
 * @package App\Repository\backyard
 */
class SysDistrictRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据编号获取区信息
     *
     * @param string $code 编号
     * @return array
     */
    public function getDistrictDetail($code)
    {
        $detail = [];
        if (!empty($code)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SysDistrictModel::class);
            $builder->where('code = :code:', ['code' => $code]);
            $info = $builder->getQuery()->getSingleResult();
            $detail = !empty($info) ? $info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取指定编号的区信息
     * @param array $codes
     * @return array
     */
    public function getListByCodes($codes = [])
    {
        $list = [];
        if (!empty($codes)) {
            $list = SysDistrictModel::find([
                'columns' => 'code,name,postal_code',
                'conditions' => 'code in ({code:array})',
                'bind' => [
                    'code' => $codes,
                ],
            ])->toArray();

            $list = array_column($list, null, 'code');
        }
        return $list;
    }

    /**
     * 获取指定编号的区信息
     * @param string $code
     * @return array
     */
    public function getInfoByCode(string $code)
    {
        $info = [];
        if (!empty($code)) {
            $info = SysDistrictModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $code,],
                'columns'    => ['code', 'name'],

            ]);

            $info = !empty($info) ? $info->toArray() : [];
        }
        return $info;
    }

}
