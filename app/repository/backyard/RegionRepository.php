<?php
namespace App\Repository\backyard;

use App\Library\Enums\GlobalEnums;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\BaseRepository;

/**
 * 管理大区表-服务层
 * Class RegionRepository
 * @package App\Repository\backyard
 */
class RegionRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return RegionRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取大区列表
     * @param array $params 查询条件组
     * @param int $page_size 默认条数
     * @return mixed
     */
    public function searchRegion($params, $page_size = GlobalEnums::DEFAULT_PAGE_SIZE)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, name');
        $builder->from(SysManageRegionModel::class);
        $builder->where('deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($params['name'])) {
            $builder->andWhere('name LIKE :name:', ['name' => '%' . $params['name'] . '%']);
        }
        $builder->limit($page_size);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据大区ID获取大区信息
     * @param integer $id 大区ID
     * @param integer $deleted 是否已经删除 0否，1是 2全部
     * @return mixed
     */
    public function getRegionInfoById($id, $deleted = GlobalEnums::IS_NO_DELETED)
    {
        $detail = [];
        if (!empty($id)) {
            $conditions = 'id = :id:';
            $bind['id'] = $id;
            if (in_array($deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
                $conditions     .= ' and deleted = :deleted:';
                $bind['deleted'] = $deleted;
            }
            $region_info = SysManageRegionModel::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);

            $detail = !empty($region_info) ? $region_info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取指定大区列表
     * @param array $ids
     * @return array
     */
    public function getListByIds($ids = [])
    {
        $list = [];
        if (!empty($ids)) {
            $list = SysManageRegionModel::find([
                'conditions' => 'id IN ({ids:array}) AND deleted = :deleted:',
                'bind' => ['ids' => array_values($ids), 'deleted' => GlobalEnums::IS_NO_DELETED],
                'columns' => ['id', 'name']
            ])->toArray();
            $list = array_column($list, 'name', 'id');
        }
        return $list;
    }

    /**
     * 获取指定工号管理大区下的网点
     * @param $manager_id
     * @return array
     */
    public function getStoreListByManagerId($manager_id)
    {
        $list = [];
        if (!empty($manager_id)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('store.id AS store_id');
            $builder->from(['region' => SysManageRegionModel::class]);
            $builder->innerJoin(SysStoreModel::class, 'region.id = store.manage_region', 'store');
            $builder->where('region.manager_id = :manager_id:', ['manager_id' => $manager_id,]);
            $list = $builder->getQuery()->execute()->toArray();
        }

        return $list;
    }

}
