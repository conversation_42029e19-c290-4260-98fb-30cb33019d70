<?php
namespace App\Repository\backyard;

use App\Library\Enums\GlobalEnums;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysStoreModel;
use App\Repository\BaseRepository;

/**
 * 管理片区表-服务层
 * Class PieceRepository
 * @package App\Repository\backyard
 */
class PieceRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 单例
     * @return PieceRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取片区列表
     * @param array $params 查询条件组
     * @param int $page_size 默认条数
     * @return mixed
     */
    public function searchPiece($params, $page_size = GlobalEnums::DEFAULT_PAGE_SIZE)
    {
        $is_all = $params['is_all'] ?? 0; //是否取全量数据：1-是；0-否
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, name');
        $builder->from(SysManagePieceModel::class);
        $builder->where('deleted = :deleted:', ['deleted' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($params['name'])) {
            $builder->andWhere('name LIKE :name:', ['name' => '%' . $params['name'] . '%']);
        }
        if (!empty($params['region_id'])) {
            $builder->andWhere('manage_region_id = :manage_region_id:', ['manage_region_id' => $params['region_id']]);
        }
        if (!$is_all) {
            $builder->limit($page_size);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据片区ID获取片区信息
     * @param integer $id 片区ID
     * @param integer $deleted 是否已经删除 0否，1是 2全部
     * @return mixed
     */
    public function getPieceInfoById($id, $deleted = GlobalEnums::IS_NO_DELETED)
    {
        $detail = [];
        if (!empty($id)) {
            $conditions = 'id = :id:';
            $bind['id'] = $id;
            if (in_array($deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
                $conditions     .= ' and deleted = :deleted:';
                $bind['deleted'] = $deleted;
            }
            $piece_info = SysManagePieceModel::findFirst([
                'conditions' => $conditions,
                'bind'       => $bind,
            ]);

            $detail = !empty($piece_info) ? $piece_info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取指定工号管理片区下的网点
     * @param $manager_id
     * @return mixed
     */
    public function getStoreListByManagerId($manager_id)
    {
        $list = [];
        if (!empty($manager_id)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->columns('store.id AS store_id');
            $builder->from(['piece' => SysManagePieceModel::class]);
            $builder->innerJoin(SysStoreModel::class,
                'piece.manage_region_id = store.manage_region AND piece.id = store.manage_piece', 'store');
            $builder->where('piece.manager_id = :manager_id:', ['manager_id' => $manager_id,]);
            $list = $builder->getQuery()->execute()->toArray();
        }

        return $list;
    }

}
