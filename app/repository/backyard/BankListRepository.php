<?php
namespace App\Repository\backyard;

use App\Models\backyard\BankListModel;
use App\Repository\BaseRepository;

/**
 * 省信息
 * Class BankListRepository
 * @package App\Repository\backyard
 */
class BankListRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }


    /**
     * 获取银行名称列表
     *
     * @return array
     */
    public function getAllList()
    {
        $list = BankListModel::find([
            'columns' => 'bank_id, bank_name',
        ])->toArray();

        return array_column($list, null, 'bank_id');
    }
}
