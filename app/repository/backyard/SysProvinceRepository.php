<?php
namespace App\Repository\backyard;

use App\Models\backyard\SysProvinceModel;
use App\Repository\BaseRepository;

/**
 * 省信息
 * Class SysProvinceRepository
 * @package App\Repository\backyard
 */
class SysProvinceRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }


    /**
     * 根据编号获取省信息
     *
     * @param string $code 编号
     * @return array
     */
    public function getProvinceDetail($code)
    {
        $detail = [];
        if (!empty($code)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SysProvinceModel::class);
            $builder->where('code = :code:', ['code' => $code]);
            $info = $builder->getQuery()->getSingleResult();
            $detail = !empty($info) ? $info->toArray() : [];
        }
        return $detail;
    }


    /**
     * 获取指定编号的省份信息
     * @param array $codes
     * @return array
     */
    public function getListByCodes($codes = [])
    {
        $list = [];
        if (!empty($codes)) {
            $list = SysProvinceModel::find([
                'columns' => 'code,name',
                'conditions' => 'code in ({code:array})',
                'bind' => [
                    'code' => $codes,
                ],
            ])->toArray();

            $list = array_column($list, null, 'code');
        }
        return $list;
    }

    /**
     * 获取指定编号的省份信息
     * @param string $code
     * @return array
     */
    public function getInfoByCode(string $code)
    {
        $info = [];
        if (!empty($code)) {
            $info = SysProvinceModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $code,],
                'columns'    => ['code', 'name'],
            ]);

            $info = !empty($info) ? $info->toArrAy() : [];
        }

        return $info;
    }

}
