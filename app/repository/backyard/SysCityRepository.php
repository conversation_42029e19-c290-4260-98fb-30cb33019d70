<?php
namespace App\Repository\backyard;

use App\Models\backyard\SysCityModel;
use App\Repository\BaseRepository;


/**
 * 市信息
 * Class SysCityRepository
 * @package App\Repository\backyard
 */
class SysCityRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据编号获取市信息
     *
     * @param string $code 编号
     * @return array
     */
    public function getCityDetail($code)
    {
        $detail = [];
        if (!empty($code)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SysCityModel::class);
            $builder->where('code = :code:', ['code' => $code]);
            $info = $builder->getQuery()->getSingleResult();
            $detail = !empty($info) ? $info->toArray() : [];
        }
        return $detail;
    }

    /**
     * 获取指定编号的市信息
     * @param array $codes
     * @return array
     */
    public function getListByCodes($codes = [])
    {
        $list = [];
        if (!empty($codes)) {
            $list = SysCityModel::find([
                'columns' => 'code,name',
                'conditions' => 'code in ({code:array})',
                'bind' => [
                    'code' => $codes,
                ],
            ])->toArray();

            $list = array_column($list, null, 'code');
        }
        return $list;
    }

    /**
     * 获取指定编号的市信息
     * @param string $code
     * @return array
     */
    public function getInfoByCode(string $code)
    {
        $info = [];
        if (!empty($code)) {
            $info = SysCityModel::findFirst([
                'conditions' => 'code = :code:',
                'bind'       => ['code' => $code,],
                'columns'    => ['code', 'province_code', 'name'],
            ]);

            $info = $info ? $info->toArray() : [];
        }

        return $info;
    }

}
