<?php

namespace App\Repository\backyard;

use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Modules\Reimbursement\Models\HrStaffApplySupportStore;
use App\Repository\BaseRepository;

class HrStaffApplySupportStoreRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据单号获取符合状态的支援单
     * @param $serial_no_list
     * @return array
     */
    public function getNormalSupportNoList($serial_no_list)
    {
        if (empty($serial_no_list)) {
            return [];
        }

        // 符合条件的支援单
        $list = HrStaffApplySupportStore::find([
            'conditions' => 'serial_no IN ({serial_no:array}) AND status = :status: AND support_status in ({support_status:array})',
            'bind'       => [
                'status'         => ByWorkflowEnums::BY_OPERATE_PASS,
                'support_status' => [
                    ReimbursementEnums::STORE_SUPPORT_STATUS_2,
                    ReimbursementEnums::STORE_SUPPORT_STATUS_3,
                ],
                'serial_no'      => array_values($serial_no_list),
            ],
            'columns'    => ['serial_no'],
        ])->toArray();

        return array_column($list, 'serial_no');
    }

    /**
     * 根据单号获取支援单列表
     * @param $serial_no_list
     * @return array
     */
    public function getListBySupportNoList($serial_no_list)
    {
        if (empty($serial_no_list)) {
            return [];
        }

        // 符合条件的支援单
        return HrStaffApplySupportStore::find([
            'conditions' => 'serial_no IN ({serial_no:array})',
            'bind'       => ['serial_no' => array_values($serial_no_list),],
            'columns'    => [
                'staff_info_id',
                'staff_store_id',
                'serial_no AS support_serial_no',
                'job_title_id',
                'store_id AS support_store_id',
                'employment_begin_date',
                'employment_end_date',
                'employment_days',
                'status',
                'support_status',
                'is_stay',
                'transportation_mode'
            ],
            'order'      => 'serial_no ASC',
        ])->toArray();
    }

}
