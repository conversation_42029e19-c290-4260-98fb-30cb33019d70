<?php

namespace App\Repository\backyard;

use App\Library\Enums\ByWorkflowEnums;
use App\Library\Enums\ReimbursementEnums;
use App\Modules\Reimbursement\Models\BusinessTripModel;
use App\Repository\BaseRepository;

class BusinessTripRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $serial_no
     * @return array
     */
    public function getInfoBySerialNo($serial_no)
    {
        if (empty($serial_no)) {
            return [];
        }

        // 符合条件的支援单
        $model = BusinessTripModel::findFirst([
            'conditions' => 'serial_no = :serial_no:',
            'bind'       => ['serial_no' => $serial_no,],
            'columns'    => [
                'apply_user',
                'serial_no',
                'start_time',
                'days_num',
            ],
        ]);

        return !empty($model) ? $model->toArray() : [];
    }

    /**
     * @param $serial_no_list
     * @return array
     */
    public function getListBySerialNoList($serial_no_list)
    {
        $serial_no_list = array_values(array_filter($serial_no_list));
        if (empty($serial_no_list)) {
            return [];
        }

        // 符合条件的支援单
        return BusinessTripModel::find([
            'conditions' => 'serial_no IN ({serial_no_list:array})',
            'bind'       => ['serial_no_list' => array_values($serial_no_list),],
            'columns'    => [
                'apply_user',
                'serial_no',
                'start_time',
                'end_time',
                'days_num',
                'departure_city',
                'departure_city_code',
                'destination_city',
                'destination_city_code',
                'business_trip_type',
                'destination_country',
                'reason_application',
            ],
        ])->toArray();
    }

}
