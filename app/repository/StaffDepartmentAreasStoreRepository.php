<?php

namespace App\Repository;

use App\Models\backyard\SysStoreModel;

/**
 * 数据权限范围 部门/大区/片区/网点
 * Class StaffDepartmentAreasStoreRepository
 * @package App\Repository
 */
class StaffDepartmentAreasStoreRepository extends BaseRepository
{
    public static $TYPE_1 = 1; //管辖范围
    public static $TYPE_2 = 2; //职级白名单管辖范围

    /**
     * @description:获取指定工号的 管辖范围
     * @param int $staffId 用户 id
     * @param int $type    1 管辖范围 2 职级白名单管辖范围
     * @return   array  :  [
     *                     'stores_ids'     =>  array_values(array_unique($mange_store_ids)),
     *                     'department_ids' => $mange_department_ids,
     *                     ]
     * <AUTHOR> L.J
     * @time       : 2022/10/12 18:20
     */
    public function getStaffManageDepartmentsAreasStores($staff_info_id, $type = 1 ) {
        $return_data = [];
        if (empty($staff_info_id)) {
            return $return_data;
        }
        //获取管辖范围
        $relations = (new HrStaffRepository())->getStaffJurisdiction($staff_info_id, $type);
        //获取管辖的字段信息
        $mange_store_ids      = $relations['stores'] ?? [];           //管辖网点
        $mange_piece_ids      = $relations['pieces'] ?? [];           //管辖片区
        $mange_region_ids     = $relations['regions'] ?? [];          //管辖大区
        $mange_department_ids = $relations['departments'] ?? [];      //管辖部门
        $store_category_ids   = $relations['store_categories'] ?? []; //管辖网点类型

        if (!in_array('-2', $mange_store_ids)) {
            //管辖片区
            if (!empty($mange_piece_ids)) {
                $store_ids       = $this->getStoreListByPieceNew($mange_piece_ids);
                $mange_store_ids = array_merge($mange_store_ids, $store_ids);
            }

            //管辖大区
            if (!empty($mange_region_ids)) {
                $store_ids       = $this->getStoreListByRegionNew($mange_region_ids);
                $mange_store_ids = array_merge($mange_store_ids, $store_ids);
            }

            if (!empty($store_category_ids)) {
                $store_ids       = $this->getStoreListByCategory($store_category_ids);
                $mange_store_ids = array_merge($mange_store_ids, $store_ids);
            }
        }

        return [
            'stores_ids'     => array_values(array_unique($mange_store_ids)),
            'department_ids' => $mange_department_ids,
        ];

    }

    /**
     * 获取片区下所有网点
     * @param array $manage_piece
     * @return array
     */
    public function getStoreListByPieceNew(array $manage_piece)
    {
        if (empty($manage_piece)) {
            return [];
        }
        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_piece in ({manage_piece_ids:array})",
            'bind' => [
                'manage_piece_ids' => $manage_piece
            ],
            'columns' => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

    /**
     * 获取指定大区下所有网点
     * @param array $manage_region
     * @return array
     */
    public function getStoreListByRegionNew(array $manage_region)
    {
        if (empty($manage_region)) {
            return [];
        }

        $store_ids = SysStoreModel::find([
            'conditions' => "state = 1 and manage_region in ({manage_region_ids:array})",
            'bind' => [
                'manage_region_ids' => $manage_region
            ],
            'columns' => 'id',
        ])->toArray();

        return array_column($store_ids, 'id');
    }

        /**
         * 根据网点类型获取网点id
         * @param array $category_ids
         * @return array
         */
        public
        function getStoreListByCategory( array $category_ids ) {
            if (empty($category_ids)) {
                return [];
            }
            $store_ids = SysStoreModel::find([
                                                 'conditions' => 'state = 1 and category in ({category_ids:array})',
                                                 'bind'       => [
                                                     'category_ids' => $category_ids
                                                 ],
                                                 'columns'    => 'id',
                                             ])->toArray();

            return array_column($store_ids, 'id');
        }
}