<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\ReimbursementBankChargesAreaQuotaModel;
use App\Repository\BaseRepository;


class ReimbursementBankChargesAreaQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取指定区域的额度配置
     * @return mixed
     */
    public function getAll()
    {
        $list = ReimbursementBankChargesAreaQuotaModel::find([
            'conditions' => 'is_deleted = :is_deleted:',
            'bind'       => ['is_deleted' => GlobalEnums::IS_NO_DELETED,],
            'columns'    => ['UPPER(sorting_area) AS sorting_area', 'month_amount'],
        ])->toArray();

        return array_column($list, 'month_amount', 'sorting_area');
    }

}
