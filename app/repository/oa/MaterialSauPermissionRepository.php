<?php

namespace App\Repository\oa;

use App\Models\oa\MaterialSauPermissionModel;
use App\Repository\BaseRepository;

/**
 * Class MaterialSauPermissionRepository
 * @package App\Repository\oa
 */
class MaterialSauPermissionRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据barcode查询信息
     * @param $sau_ids
     * @return array
     */
    public function getJobIdsBySau($sau_ids)
    {
        if (empty($sau_ids)) {
            return [];
        }
        return MaterialSauPermissionModel::find([
            'columns' => 'sau_id,job_id',
            'conditions' => 'sau_id in ({sau_ids:array})',
            'bind' => ['sau_ids' => array_values($sau_ids)]
        ])->toArray();
    }
}
