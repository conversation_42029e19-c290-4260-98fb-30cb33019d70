<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\WarehouseThreadModel;
use App\Repository\BaseRepository;

/**
 * 仓库管理-仓库线索
 * Class WarehouseThreadRepository
 *
 * @package App\Repository\oa
 */
class WarehouseThreadRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 仅查询特定条件下的线索
     * @param array $params 查询参数组
     * @param string $sort 排序
     * @param string $columns 字段
     * @return mixed
     */
    public function onlySearchThread(array $params, $sort = 'no ASC', string $columns = '*')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(WarehouseThreadModel::class);

        //需求ID
        $requirement_id = $params['requirement_id'] ?? 0;
        if (!empty($requirement_id)) {
            $builder->andWhere('requirement_id = :requirement_id:', ['requirement_id' => $requirement_id]);
        }

        //线索状态：1-待确认、2-确认中、3-待报价、4-报价审核中、5-待签约、6-签约流程中、7-待付款、8-待入驻、9-已入驻、10-被淘汰、11-待关联、12-已作废
        $not_status = $params['not_status'] ?? 0;
        if (!empty($not_status)) {
            $builder->andWhere('status != :not_status:', ['not_status' => $not_status]);
        }

        //线索状态：1-待确认、2-确认中、3-待报价、4-报价审核中、5-待签约、6-签约流程中、7-待付款、8-待入驻、9-已入驻、10-被淘汰、11-待关联、12-已作废
        $status = $params['status'] ?? 0;
        if (!empty($status)) {
            if (is_array($status)) {
                $builder->inWhere('status', $status);
            } else {
                $builder->andWhere('status = :status:', ['status' => $status]);
            }
        }

        $builder->orderBy($sort);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取指定ID的线索列表
     * @param array $thread_ids
     * @return mixed
     */
    public function getListByIds(array $thread_ids)
    {
        if (empty($thread_ids)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'id',
            'no',
            'warehouse_address',
        ]);
        $builder->from(WarehouseThreadModel::class);
        $builder->inWhere('id', $thread_ids);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取ID线索
     * @param  $thread_id
     * @return mixed
     */
    public function getOneById($thread_id)
    {
        if (empty($thread_id)) {
            return null;
        }

        return WarehouseThreadModel::findFirst([
            'conditions' => 'id = :id:',
            'bind'       => ['id' => $thread_id],
        ]);
    }



}
