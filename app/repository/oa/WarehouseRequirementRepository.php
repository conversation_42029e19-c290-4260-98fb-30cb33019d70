<?php
namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\WarehouseRequirementModel;
use App\Repository\BaseRepository;

/**
 * 仓库管理-仓库需求
 * Class WarehouseRequirementRepository
 *
 * @package App\Repository\oa
 */
class WarehouseRequirementRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 仅查询特定条件下的需求
     * @param array $params 查询参数组
     * @param string $columns 字段
     * @return mixed
     */
    public function onlySearchRequirement(array $params, string $columns = 'id, no, store_name')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(WarehouseRequirementModel::class);

        //支持通过需求ID和需求网点名称模糊搜索
        $name = $params['name'] ?? '';
        if (!empty($name)) {
            $builder->andWhere('no like :name: or store_name like :name:', ['name' => '%' . $name . '%']);
        }

        //需求状态：1-待寻找、2-待确认、3-待报价、4-报价审核中、5-待签约、6-待付款、7-待入驻、8-已入驻、9-已作废
        $status = $params['status'] ?? 0;
        if (!empty($status)) {
            $builder->andWhere('status = :status:', ['status' => $status]);
        }

        $builder->orderBy('no ASC');
        $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 判断原仓库id是否有关联的待续约/待签约状态的需求
     * @param $original_warehouse_id
     * @param $warehouse_type
     * @param array $status
     * @return bool
     */
    public function isExistByOriginalWarehouseIdAndStatus($original_warehouse_id, $warehouse_type, array $status = [])
    {
        if (empty($original_warehouse_id) || empty($warehouse_type) || empty($status)) {
            return false;
        }

        $model = WarehouseRequirementModel::findFirst([
            'conditions' => 'original_warehouse_id = :original_warehouse_id: AND status IN ({status:array}) AND warehouse_type = :warehouse_type:',
            'bind'       => [
                'original_warehouse_id' => $original_warehouse_id,
                'status'                => $status,
                'warehouse_type'        => $warehouse_type,
            ],
        ]);

        return !empty($model);
    }

    /**
     * 获取仓库类型是续约 且 状态是待续约/待续签的需求所关联的原仓库ID列表
     * @param $warehouse_type
     * @param array $status
     * @return bool
     */
    public function getOriginalWarehouseIdListByStatus($warehouse_type, array $status = [])
    {
        $list = [];
        if (empty($warehouse_type) || empty($status)) {
            return $list;
        }

        $list = WarehouseRequirementModel::find([
            'conditions' => 'warehouse_type = :warehouse_type: AND status IN ({status:array})',
            'bind'       => [
                'warehouse_type' => $warehouse_type,
                'status'         => $status,
            ],
            'columns'    => ['original_warehouse_id'],
        ])->toArray();

        return array_column($list, 'original_warehouse_id');
    }

}
