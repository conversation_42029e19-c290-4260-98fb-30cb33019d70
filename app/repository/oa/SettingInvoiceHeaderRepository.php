<?php

namespace App\Repository\oa;

use App\Models\oa\SettingInvoiceHeaderModel;
use <PERSON>alcon\Mvc\Model\ResultsetInterface;
use App\Repository\BaseRepository;

/**
 * OA发票抬头管理
 * Class SettingInvoiceHeaderRepository
 * @package App\Repository
 */
class SettingInvoiceHeaderRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取发票抬头列表
     *
     * @return array
     */
    public function getList()
    {
        return SettingInvoiceHeaderModel::find([
            'columns' => ['id', 'header_name', 'related_company_ids'],
            'order' => 'header_name ASC'
        ])->toArray();
    }

    /**
     * 获取发票抬头信息 By 发票名称
     *
     * @param string $header_name
     * @return SettingInvoiceHeaderModel
     */
    public function getInfoByHeaderName(string $header_name)
    {
        return SettingInvoiceHeaderModel::findFirst([
            'conditions' => 'header_name = :header_name:',
            'bind' => ['header_name' => $header_name]
        ]);
    }

    /**
     * 获取发票抬头信息 By 发票ID
     *
     * @param int $id
     * @return SettingInvoiceHeaderModel
     */
    public function getInfoById(int $id)
    {
        return SettingInvoiceHeaderModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
    }

}
