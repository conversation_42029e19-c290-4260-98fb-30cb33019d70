<?php
namespace App\Repository\oa;
use App\Models\oa\MaterialSetReturnRepairTypeModel;
use App\Repository\BaseRepository;

/**
 * Class MaterialSetReturnTypeRepository
 * @package App\Repository\oa
 */
class MaterialSetReturnTypeRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取指定退回类型信息
     * @param array $type_ids 类型ID
     * @return array
     */
    public function getTypeListByIds($type_ids = [])
    {
        $type_list = [];
        if (!empty($type_ids)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialSetReturnRepairTypeModel::class);
            $builder->inWhere('id', $type_ids);
            $builder->columns('id, name_zh, name_en, name_local');
            $type_list = $builder->getQuery()->execute()->toArray();
            $type_list = array_column($type_list, null, 'id');
        }
        return $type_list;
    }
}
