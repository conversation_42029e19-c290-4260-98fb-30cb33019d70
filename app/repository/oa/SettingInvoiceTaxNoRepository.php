<?php

namespace App\Repository\oa;

use App\Models\oa\SettingInvoiceTaxNoModel;
use App\Models\oa\WorkflowSubNodeModel;
use App\Repository\BaseRepository;

/**
 * 发票税务号
 * Class SettingInvoiceTaxNoRepository
 * @package App\Repository\oa
 */
class SettingInvoiceTaxNoRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 SettingInvoiceTaxNoRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    /**
     * 根据税号获取信息
     * @date 2023/9/6
     * @param string $invoice_tax_no 发票税号
     * @return array
     */
    public function getInfoByNo($invoice_tax_no)
    {
        if (empty($invoice_tax_no)) {
            return [];
        }
        $info = SettingInvoiceTaxNoModel::findFirst([
            'conditions' => 'invoice_tax_no = :invoice_tax_no:',
            'bind' => [
                'invoice_tax_no' => $invoice_tax_no,
            ]
        ]);
        return $info ? $info->toArray() : [];
    }

    /**
     * 根据多个税号获取信息
     * @date 2023/9/6
     * @param array $invoice_tax_nos 发票税号数组
     * @return array
     */
    public function getDataByNos($invoice_tax_nos)
    {
        if (empty($invoice_tax_nos)) {
            return [];
        }
        return SettingInvoiceTaxNoModel::find([
            'conditions' => 'invoice_tax_no in ({invoice_tax_nos:array})',
            'bind' => [
                'invoice_tax_nos' => $invoice_tax_nos,
            ]
        ]);
    }
}
