<?php

namespace App\Repository\oa;

use App\Models\oa\SysModuleModel;
use App\Models\oa\WorkflowBusinessModel;
use App\Models\oa\WorkflowCarbonCopyModel;
use App\Models\oa\WorkflowModel;
use App\Repository\BaseRepository;

/**
 * 审批流模块
 * Class WorkflowRepository
 * @package App\Repository\oa
 */
class WorkflowRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过审批流名称模糊搜索
     * @param string $workflow_name
     * @return mixed
     */
    public function getWorkflowNameItems($workflow_name = '')
    {
        if (empty($workflow_name)) {
            $module_data = WorkflowModel::find([
                'columns' => 'id as flow_id, name as workflow_name',
                'limit' => 100,
            ])->toArray();
        } else {
            $module_data = WorkflowModel::find([
                'columns' => 'id as flow_id, name as workflow_name',
                'conditions' => ' name like :workflow_name: ',
                'bind' => [
                    'workflow_name' => $workflow_name . '%'
                ],
                'limit' => 100,
            ])->toArray();
        }

        return $module_data;
    }

    /**
     * 获取审批流数据
     * @param $id
     * @return array
     */
    public function getWorkflowById($id)
    {
        if (empty($id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('flow.id, module.name_key as module_name_key, module.send_submitter_reject_sms');
        $builder->from(['flow' => WorkflowModel::class]);
        $builder->leftJoin(SysModuleModel::class, 'flow.module_id = module.id', 'module');
        $builder->where('flow.id = :id:', ['id' => $id]);
        $result = $builder->getQuery()->getSingleResult();
        return $result ? $result->toArray() : [];

    }

    /** 
     * @param array $flow_ids 审批流id
     * @return mixed
     */
    public function getWorkflowData(array $flow_ids = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns(['w.id, w.name, w.module_id, w.business_id, m.name_key as module_name_key, b.name_key as business_name_key']);
        $builder->from(['w' => WorkflowModel::class]);
        $builder->leftJoin(SysModuleModel::class, 'w.module_id = m.id', 'm');
        $builder->leftJoin(WorkflowBusinessModel::class, 'w.business_id = b.id', 'b');
        if (!empty($flow_ids)) {
            $builder->inWhere('w.id', $flow_ids);
        }
        return $builder->getQuery()->execute()->toArray();
    }
}
