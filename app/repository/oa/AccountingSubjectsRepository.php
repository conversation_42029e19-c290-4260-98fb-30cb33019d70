<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\AccountingSubjectsModel;
use App\Repository\BaseRepository;

/**
 * 子公司会计科目
 * Class AccountingSubjectsRepository
 * @package App\Repository\oa
 */
class AccountingSubjectsRepository extends BaseRepository
{
    // 默认的科目名称字段
    protected static $name_field = 'subjects_name_local';

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance($sys_lang = null)
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        if (!is_null($sys_lang)) {
            self::$name_field = get_lang_field_name('subjects_name_', $sys_lang, 'local', 'zh');
        }

        return self::$instance;
    }


    /**
     * 获取会计科目: By id
     *
     * @param int $id
     * @return array
     */
    public function getOneById(int $id)
    {
        return AccountingSubjectsModel::findFirst([
            'conditions' => 'id = :id: AND is_deleted = :is_deleted:',
            'bind' => ['id' => $id, 'is_deleted' => GlobalEnums::IS_NO_DELETED],
            'columns' => ['id AS subjects_id', 'subjects_code', self::$name_field . ' AS subjects_name'],
        ]);
    }

    /**
     * 根据会计科目名称或编号搜索
     *
     * @param string $keyword
     * @param int $limit 0 表示不限制条数
     * @return array
     */
    public function getListByKeyword(string $keyword = '', int $limit = 0)
    {
        $conditions = 'is_deleted = :is_deleted:';
        $bind = ['is_deleted' => GlobalEnums::IS_NO_DELETED];

        if (!empty($keyword)) {
            $conditions .= ' AND (subjects_code LIKE :subjects_code: OR ' . self::$name_field . ' Like :subjects_name:)';
            $bind['subjects_code'] = "%$keyword%";
            $bind['subjects_name'] = "%$keyword%";
        }

        $find_condition = [
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ['id', 'subjects_code', self::$name_field . ' AS subjects_name'],
        ];

        if ($limit > 0) {
            $find_condition['limit'] = $limit;
        }

        return AccountingSubjectsModel::find($find_condition)->toArray();
    }

    /**
     * 获取指定id会计科目列表
     * @param array $ids 科目ID
     * @param int $is_deleted 0-未删除(默认); 1-已删除; 2-全部
     * @return array
     */
    public function getListByIds($ids = [], $is_deleted = 2)
    {
        $where = [
            'columns' => ['id', 'subjects_code', self::$name_field . ' AS subjects_name', 'is_deleted']
        ];
        if (!empty($ids)) {
            $ids = array_values(array_unique(array_filter($ids)));
            $where['conditions'] = 'id IN ({ids:array})';
            $where['bind']['ids'] = $ids;
        }
        if (in_array($is_deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
            $where['conditions'] .= ($where['conditions'] ? ' AND ' : '') . 'is_deleted = :is_deleted:';
            $where['bind']['is_deleted'] = $is_deleted;
        }
        $list = AccountingSubjectsModel::find($where)->toArray();
        return array_column($list, null, 'id');
    }
}
