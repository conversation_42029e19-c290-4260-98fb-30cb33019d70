<?php

namespace App\Repository\oa;

use App\Modules\Budget\Models\BudgetObjectLedger;
use App\Modules\Budget\Models\LedgerAccount;
use App\Repository\BaseRepository;

/**
 * Class LedgerAccountRepository
 * @package App\Repository\oa
 */
class LedgerAccountRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankAccountRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据科目ID获取科目列表
     * @param array $ids 科目ID组
     * @return array
     */
    public function getLedgerAccountByIds($ids = [])
    {
        $list = [];
        if (!empty($ids)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(LedgerAccount::class);
            $builder->inWhere('id', $ids);
            $builder->columns('*');
            $list = $builder->getQuery()->execute()->toArray();
            $list = array_column($list, null, 'id');
        }
        return $list;
    }

    /**
     * 获取预算科目对应的核算科目
     * @param array $budget_ids 预算科目ID组
     * @return array
     */
    public function getBudgetLedgerAccount(array $budget_ids)
    {
        if (empty($budget_ids)) {
            return [];
        }
        $budget_ids = array_values(array_unique(array_filter($budget_ids)));
        $builder    = $this->modelsManager->createBuilder();
        $builder->columns('main.budget_id, ledger.account');
        $builder->from(['main' => BudgetObjectLedger::class]);
        $builder->leftJoin(LedgerAccount::class, 'ledger.id = main.ledger_id', 'ledger');
        $builder->inWhere('main.budget_id', $budget_ids);
        $data = $builder->getQuery()->execute()->toArray();
        return array_column($data, 'account', 'budget_id');
    }

}
