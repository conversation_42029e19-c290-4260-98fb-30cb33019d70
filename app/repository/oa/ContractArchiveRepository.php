<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Library\Enums\ContractEnums;
use App\Library\Enums\StaffInfoEnums;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Contract\Models\Contract;
use App\Modules\Contract\Models\ContractArchive;
use App\Modules\Contract\Models\ContractStoreRentingModel;
use App\Repository\BaseRepository;

/**
 * 归档合同
 * Class ContractArchiveRepository
 * @package App\Repository
 */
class ContractArchiveRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ContractArchiveRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 查询所有未归档-其他合同和GPMD合同
     * @return mixed
     */
    public function getNoArchiveContractArchive()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder->columns(['ca.cno, c.template_id, ca.create_id, ca.approved_at, hr.node_department_id, ca.contract_type']);
        $builder->leftJoin(Contract::class, 'c.cno = ca.cno', 'c');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'ca.create_id = hr.staff_info_id', 'hr');
        $builder->where('ca.status = :status:', ['status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]);
        $builder->inWhere('ca.contract_type', [ContractEnums::CONTRACT_TYPE_OTHER, ContractEnums::CONTRACT_TYPE_PLATFORM]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询所有未归档-租房合同
     * @return mixed
     */
    public function getNoArchiveContractArchiveRenting()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder->columns(['ca.cno, 0 as template_id, renting.contract_leader_id as create_id, ca.approved_at, hr.node_department_id, ca.contract_type']);
        $builder->leftJoin(ContractStoreRentingModel::class, 'renting.contract_id = ca.cno', 'renting');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'renting.contract_leader_id = hr.staff_info_id', 'hr');
        $builder->where('ca.status = :status:', ['status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]);
        $builder->andWhere('ca.contract_type = :contract_type:', ['contract_type' => ContractEnums::CONTRACT_TYPE_STORING]);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询其他合同没有进入归档表的数据
     * @return mixed
     */
    public function getNoArchiveContract()
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => Contract::class]);
        $builder->columns(['c.cno, c.template_id, c.create_id, c.approved_at, hr.node_department_id, ' . ContractEnums::CONTRACT_TYPE_OTHER . ' as contract_type']);
        $builder->leftJoin(ContractArchive::class, 'c.cno = ca.cno', 'ca');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = hr.staff_info_id', 'hr');
        $builder->where('c.status = :status:', ['status' => Enums::CONTRACT_STATUS_APPROVAL]);
        $builder->andWhere('ca.id is null');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询超时未归档的归档表数据,且员工在职或待离职 其他合同和GPMD合同 这两种都是用create_id统计
     * @param $time_out_date
     * @return mixed
     */
    public function getTimeOutContractArchive($time_out_date)
    {
        //查其他合同和GPMD合同
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder->columns('ca.create_id, count(ca.id) as count');
        $builder->leftJoin(Contract::class, 'c.cno = ca.cno', 'c');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'ca.create_id = hr.staff_info_id', 'hr');
        $builder->where('ca.status = :status:', ['status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]);
        $builder->andWhere('ca.approved_at <= :approved_at:', ['approved_at' => $time_out_date]);
        $builder->inWhere('ca.contract_type', [ContractEnums::CONTRACT_TYPE_OTHER, ContractEnums::CONTRACT_TYPE_PLATFORM]);
        $builder->andWhere('hr.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
        $builder->groupBy('ca.create_id');
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 查询超时未归档的归档表数据,且员工在职或待离职 租房付款合同 这种类型用contract_leader_id统计
     * @param $time_out_date
     * @return mixed
     */
    public function getTimeOutContractArchiveRenting($time_out_date)
    {
        //查其他合同和GPMD合同
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ca' => ContractArchive::class]);
        $builder->columns('renting.contract_leader_id as create_id, count(ca.id) as count');
        $builder->leftJoin(ContractStoreRentingModel::class, 'renting.contract_id = ca.cno', 'renting');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'renting.contract_leader_id = hr.staff_info_id', 'hr');
        $builder->where('ca.status = :status:', ['status' => ContractEnums::CONTRACT_ARCHIVE_STATUS_PENDING]);
        $builder->andWhere('ca.approved_at <= :approved_at:', ['approved_at' => $time_out_date]);
        $builder->andWhere('ca.contract_type = :contract_type:', ['contract_type' => ContractEnums::CONTRACT_TYPE_STORING]);
        $builder->andWhere('hr.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
        $builder->groupBy('renting.contract_leader_id');
        return $builder->getQuery()->execute()->toArray();
    }


    /**
     * 查询超时未进入归档表的其他合同数据,且员工在职或待离职
     * @param $time_out_date
     * @return mixed
     */
    public function getTimeOutContract($time_out_date)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['c' => Contract::class]);
        $builder->columns('c.create_id, count(c.id) as count');
        $builder->leftJoin(ContractArchive::class, 'c.cno = ca.cno', 'ca');
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'c.create_id = hr.staff_info_id', 'hr');
        $builder->where('c.status = :status:', ['status' => Enums::CONTRACT_STATUS_APPROVAL]);
        $builder->andWhere('c.approved_at <= :approved_at:', ['approved_at' => $time_out_date]);
        $builder->andWhere('hr.state = :state:', ['state' => StaffInfoEnums::STAFF_STATE_IN]);
        $builder->andWhere('ca.id is null');
        $builder->groupBy('c.create_id');
        return $builder->getQuery()->execute()->toArray();
    }
}
