<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Modules\Workflow\Models\WorkflowRequestNodeAuditorModel;
use App\Repository\BaseRepository;

/**
 * 审批节点审批情况
 * Class WorkflowRequestNodeAuditorRepository
 * @package App\Repository\oa
 */
class WorkflowRequestNodeAuditorRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return WorkflowRequestNodeAuditorRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过request_id查询驳回的节点id
     * @param $request_id
     * @return mixed
     */
    public function getRejectNodeId($request_id)
    {
        $request_node_auditor = WorkflowRequestNodeAuditorModel::findFirst([
            'columns' => 'flow_node_id',
            'conditions' => 'request_id = :request_id: and audit_status = :audit_status:',
            'bind' => [
                'request_id' => $request_id,
                'audit_status' => Enums::WF_STATE_REJECTED
            ],
        ]);
        $node_id = $request_node_auditor ? $request_node_auditor->flow_node_id : 0;
        return $node_id;
    }
}
