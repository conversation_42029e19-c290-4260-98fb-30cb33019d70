<?php

namespace App\Repository\oa;

use App\Library\Enums\SettingEnums;
use App\Models\oa\DataPermissionStaffManageDepartmentListModel;
use App\Models\oa\DataPermissionStaffModuleRelModel;
use App\Repository\BaseRepository;

/**
 * 数据配置-通用数据配置-管辖人与管辖范围明细表
 * Class DataPermissionStaffManageDepartmentListRepository
 * @package App\Repository\oa
 */
class DataPermissionStaffManageDepartmentListRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取管辖人管辖范围(属于某权限分类下某业务模块的管辖部门列表)
     *
     * @param int $permission_category_id 权限分类ID
     * @param int $staff_id 管辖人工号
     * @param int $module_id 业务模块ID
     * @return array
     */
    public function getListByCategoryStaffModuleId(int $permission_category_id, int $staff_id, int $module_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'list.department_id',
            'list.is_include_sub'
        ]);

        $builder->from(['rel' => DataPermissionStaffModuleRelModel::class]);
        $builder->leftjoin(DataPermissionStaffManageDepartmentListModel::class, 'rel.id = list.staff_module_rel_id','list');
        $builder->where('rel.permission_category_id = :permission_category_id:', ['permission_category_id' => $permission_category_id]);
        $builder->andWhere('rel.staff_info_id = :staff_info_id:', ['staff_info_id' => $staff_id]);
        $builder->andWhere('FIND_IN_SET(:module_id:, rel.module_ids)', ['module_id' => $module_id]);
        $builder->andWhere('rel.configure_status = :configure_status:', ['configure_status' => SettingEnums::CONFIGURE_STATE_2]);
        return $builder->getQuery()->execute()->toArray();
    }

}
