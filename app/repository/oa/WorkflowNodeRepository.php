<?php

namespace App\Repository\oa;

use App\Library\Enums\OAWorkflowEnums;
use App\Models\oa\WorkflowNodeModel;
use App\Repository\BaseRepository;

/**
 * 审批流模块
 * Class WorkflowNodeRepository
 * @package App\Repository\oa
 */
class WorkflowNodeRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowNodeRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取审批流节点详情
     * @param $node_id
     * @return array
     */
    public function getNodeById($node_id)
    {
        $node_info = WorkflowNodeModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $node_id],
        ]);
        return $node_info ? $node_info->toArray() : [];
    }

    /**
     * 查询审批流的结束节点
     * @param $flow_id
     * @return array
     */
    public function getEndNodeByFlowId($flow_id)
    {
        $node_info = WorkflowNodeModel::findFirst([
            'conditions' => 'flow_id = :flow_id: and type = :type:',
            'bind' => [
                'flow_id' => $flow_id,
                'type' => OAWorkflowEnums::WORKFLOW_NODE_TYPE_END
            ],
        ]);
        return $node_info ? $node_info->toArray() : [];
    }
}
