<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Modules\BankFlow\Models\BankAccountModel;
use App\Repository\BaseRepository;

/**
 * Class BankAccountRepository
 * @package App\Repository\oa
 */
class BankAccountRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankAccountRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取银行列表
     * @param $params
     * @return object | false
     */
    public function getBankAccount($params)
    {
        $conditions = [];
        $bind = [];
        if (!empty($params['account'])) {
            $conditions[] = 'account = :account:';
            $bind['account'] = $params['account'];
        }

        if (!empty($params['id'])) {
            $conditions[] = 'id = :id:';
            $bind['id'] = $params['id'];
        }

        if (isset($params['is_deleted'])) {
            $conditions[] = 'is_deleted = :is_deleted:';
            $bind['is_deleted'] = $params['is_deleted'];
        }

        if (empty($conditions)) {
            return false;
        }

        $conditions = implode(' AND ', $conditions);
        return BankAccountModel::findFirst([
            'conditions' => $conditions,
            'bind' => $bind
        ]);
    }
}
