<?php

namespace App\Repository\oa;

use App\Models\oa\WorkflowCarbonCopyModel;
use App\Repository\BaseRepository;

/**
 * 审批流抄送
 * Class WorkflowNodeRepository
 * @package App\Repository\oa
 */
class WorkflowCarbonCopyRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowCarbonCopyRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取审批流节点详情
     * @param $id
     * @return array | object
     */
    public function getInfoById($id)
    {
        if (empty($id) || !is_numeric($id)) {
            return [];
        }
        $info = WorkflowCarbonCopyModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
        return $info;
    }
}
