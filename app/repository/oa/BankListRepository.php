<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Modules\BankFlow\Models\BankListModel;
use App\Repository\BaseRepository;

/**
 * Class BankListRepository
 * @package App\Repository\oa
 * @date 2023/8/30
 */
class BankListRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankListRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取银行列表
     * @param array $bank_ids
     * @return array
     */
    public function getBankList($bank_ids = [])
    {
        $find_params = [];
        $find_params['columns'] = ['id', 'bank_name', 'is_deleted'];
        if (!empty($bank_ids)) {
            $find_params['conditions'] = 'id in ({ids:array})';
            $find_params['bind'] = ['ids' => $bank_ids];
        }
        return BankListModel::find($find_params)->toArray();
    }

    /**
     * 获取银行信息
     * @param $id
     * @return array
     */
    public function getBankById($id)
    {
        if (empty($id)) {
            return [];
        }
        $bank_info = BankListModel::findFirst([
            'columns' => ['id', 'bank_name', 'is_deleted'],
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        return $bank_info ? $bank_info->toArray() : [];
    }
}
