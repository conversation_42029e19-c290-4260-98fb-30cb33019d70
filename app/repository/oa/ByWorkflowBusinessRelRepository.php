<?php

namespace App\Repository\oa;

use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\oa\ByWorkflowBusinessRelModel;
use App\Repository\BaseRepository;

/**
 * OA业务与BY审批流关系表
 * Class ByWorkflowBusinessRelRepository
 * @package App\Repository\oa
 */
class ByWorkflowBusinessRelRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 添加 OA审批业务 与 BY审批编号关系
     *
     * @param $biz_model
     * @param $oa_biz_type
     * @param $by_workflow_no
     * @return bool
     * @throws BusinessException
     */
    public function addRel($biz_model, $oa_biz_type, $by_workflow_no)
    {
        // OA审批业务 与 BY审批流关系
        $by_workflow_business_rel_model = new ByWorkflowBusinessRelModel();
        $by_workflow_rel_data = [
            'biz_value' => $biz_model->id,
            'biz_type' => $oa_biz_type,
            'workflow_no' => $by_workflow_no,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        if ($by_workflow_business_rel_model->i_create($by_workflow_rel_data) === false) {
            throw new BusinessException('by_workflow_business_rel写入失败' . get_data_object_error_msg($by_workflow_business_rel_model), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }


}
