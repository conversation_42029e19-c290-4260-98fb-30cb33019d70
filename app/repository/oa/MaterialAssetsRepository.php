<?php
namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Modules\Material\Models\MaterialAssetsModel;
use App\Repository\BaseRepository;

/**
 * Class MaterialAssetsRepository
 * @package App\Repository\oa
 */
class MaterialAssetsRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 搜索资产台账
     * @param array $params 请求参数
     * @return mixed
     */
    public function searchAsset($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(MaterialAssetsModel::class);
        $builder->where('is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //使用员工是否过滤空，默认不过滤
        $is_staff_empty = $params['is_staff_empty'] ?? 0;
        if (!empty($is_staff_empty)) {
            $builder->andWhere('staff_id != :staff_id_empty:', ['staff_id_empty' => 0]);
        }
        //物料分类表ID
        $category_id  = $params['category_id'] ?? [];
        if (!empty($category_id)) {
            if (is_array($category_id)) {
                $builder->inWhere('category_id', $category_id);
            } else {
                $builder->andWhere('category_id = :category_id:', ['category_id' => $category_id]);
            }
        }

        //barcode
        $bar_code = $params['bar_code'] ?? [];
        if (!empty($bar_code)) {
            if (is_array($bar_code)) {
                $builder->inWhere('bar_code', $bar_code);
            } else {
                $builder->andWhere('bar_code = :bar_code:', ['bar_code' => $bar_code]);
            }
        }

        //使用状态
        $status = $params['status'] ?? [];
        if (!empty($status)) {
            if (is_array($status)) {
                $builder->inWhere('status', $status);
            } else {
                $builder->andWhere('status = :status:', ['status' => $status]);
            }
        }

        //非某些状态的
        $not_status = $params['not_status'] ?? [];
        if (!empty($not_status)) {
            if (is_array($not_status)) {
                $builder->notInWhere('status', $not_status);
            } else {
                $builder->andWhere('status != :status:', ['status' => $not_status]);
            }
        }

        //所属公司ID
        $company_id = $params['company_id'] ?? [];
        if (!empty($company_id)) {
            if (is_array($company_id)) {
                $builder->inWhere('company_id', $company_id);
            } else {
                $builder->andWhere('company_id = :company_id:', ['company_id' => $company_id]);
            }
        }

        //使用人工号
        $staff_id = $params['staff_id'] ?? [];
        if (!empty($staff_id)) {
            if (is_array($staff_id)) {
                $builder->inWhere('staff_id', $staff_id);
            } else {
                $builder->andWhere('staff_id = :staff_id:', ['staff_id' => $staff_id]);
            }
        }
        //职位ID
        $job_id = $params['job_id'] ?? [];
        if (!empty($job_id)) {
            if (is_array($job_id)) {
                $builder->inWhere('job_id', $job_id);
            } else {
                $builder->andWhere('job_id = :job_id:', ['job_id' => $job_id]);
            }
        }
        //使用网点
        $sys_store_id = $params['sys_store_id'] ?? [];
        if (!empty($sys_store_id)) {
            if (is_array($sys_store_id)) {
                $builder->inWhere('sys_store_id', $sys_store_id);
            } else {
                $builder->andWhere('sys_store_id = :sys_store_id:', ['sys_store_id' => $sys_store_id]);
            }
        }
        //使用部门ID
        $node_department_id = $params['node_department_id'] ?? [];
        if (!empty($node_department_id)) {
            if (is_array($node_department_id)) {
                $builder->inWhere('node_department_id', $node_department_id);
            } else {
                $builder->andWhere('node_department_id = :node_department_id:', ['node_department_id' => $node_department_id]);
            }
        }
        //资产编码搜索
        $asset_code = $params['asset_code'] ?? [];
        if (!empty($asset_code)) {
            if (is_array($asset_code)) {
                $builder->inWhere('asset_code', $asset_code);
            } else {
                $builder->andWhere('asset_code = :asset_code:', ['asset_code' => $asset_code]);
            }
        }
        //资产ID
        $asset_id = $params['asset_id'] ?? [];
        if (!empty($asset_id)) {
            if (is_array($asset_id)) {
                $builder->inWhere('id', $asset_id);
            } else {
                $builder->andWhere('id = :asset_id:', ['asset_id' => $asset_id]);
            }
        }
        //物料类型
        $category_type = $params['category_type'] ?? 0;
        if (!empty($category_type)) {
            $builder->andWhere('category_type = :category_type:', ['category_type' => $category_type]);
        }
        //资产使用方向
        $use = isset($params['use']) ? $params['use'] : 0;
        if (!empty($use)) {
            $builder->andWhere('use = :use:', ['use' => $use]);
        }
        return $builder->getQuery()->execute()->toArray();
    }
}
