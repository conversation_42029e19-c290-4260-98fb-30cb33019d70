<?php

namespace App\Repository\oa;

use App\Models\oa\ContractElectronicSignInfoModel;
use App\Repository\BaseRepository;

/**
 * Class ContractElectronicSignInfoRepository
 * @package App\Repository
 */
class ContractElectronicSignInfoRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取电子合同签约信息 by electronic_key
     *
     * @param string $electronic_key
     * @return mixed
     */
    public function getSignInfoByElectronicKey(string $electronic_key)
    {
        return ContractElectronicSignInfoModel::findFirst([
            'conditions' => 'electronic_key = :electronic_key:',
            'bind' => ['electronic_key' => $electronic_key]
        ]);
    }

}
