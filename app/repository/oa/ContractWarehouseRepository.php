<?php

namespace App\Repository\oa;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\oa\ContractWarehouseModel;
use App\Repository\BaseRepository;

/**
 * 合同管理-仓库管理
 * Class ContractWarehouseRepository
 *
 * @package App\Repository\oa
 */
class ContractWarehouseRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据仓库ID获取仓库信息
     *
     * @param string $warehouse_id
     * @return mixed
     */
    public function getOneByWarehouseId(string $warehouse_id)
    {
        if (empty($warehouse_id)) {
            return null;
        }

        return ContractWarehouseModel::findFirst([
            'conditions' => 'warehouse_id = :warehouse_id:',
            'bind' => ['warehouse_id' => $warehouse_id],
        ]);
    }

    /**
     * 根据主键ID获取仓库信息
     *
     * @param int $id
     * @return mixed
     */
    public function getOneById(int $id)
    {
        return ContractWarehouseModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
    }

    /**
     * 根据名称获取仓库信息
     *
     * @param string $name
     * @return mixed
     */
    public function getOneByName(string $name)
    {
        return ContractWarehouseModel::findFirst([
            'conditions' => 'warehouse_name = :warehouse_name: AND warehouse_status != :warehouse_status:',
            'bind' => ['warehouse_name' => $name, 'warehouse_status' => ContractEnums::WAREHOUSE_STATUS_DEACTIVATED],
        ]);
    }

    /**
     * 根据仓库ID前缀获取最新的仓库信息
     *
     * @param string $warehouse_id
     * @return mixed
     */
    public function getLatestByWarehouseId(string $warehouse_id)
    {
        return ContractWarehouseModel::findFirst([
            'conditions' => 'warehouse_id LIKE :warehouse_id:',
            'bind' => ['warehouse_id' => "$warehouse_id%"],
            'order' => 'id DESC'
        ]);
    }

    /**
     * 根据经纬度获取位置相近的仓库列表(使用中/闲置的)
     *
     * @param string $same_latitude
     * @param string $same_longitude
     * @param string $warehouse_id
     * @return mixed
     */
    public function getListBySameLocation(string $same_latitude, string $same_longitude, string $warehouse_id = '')
    {
        $conditions = 'warehouse_latitude LIKE :latitude: AND warehouse_longitude LIKE :longitude: AND warehouse_status IN ({status:array})';
        $bind = [
            'latitude' => "$same_latitude%",
            'longitude' => "$same_longitude%",
            'status' => [ContractEnums::WAREHOUSE_STATUS_USING, ContractEnums::WAREHOUSE_STATUS_VACANT]
        ];

        if (!empty($warehouse_id)) {
            $conditions .= ' AND warehouse_id != :warehouse_id:';
            $bind['warehouse_id'] = $warehouse_id;
        }

        return ContractWarehouseModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ['warehouse_id', 'warehouse_latitude', 'warehouse_longitude'],
            'order' => 'id DESC'
        ])->toArray();
    }

    /**
     * 根据仓库ID 或 关键词 模糊搜索仓库列表(使用中的)
     *
     * @param array $params
     * @param int $page_size
     * @return mixed
     */
    public function getSearchList(array $params, int $page_size = 20)
    {
        $conditions = 'warehouse_status = :status:';
        $bind = ['status' => ContractEnums::WAREHOUSE_STATUS_USING];

        if (isset($params['warehouse_id'])) {
            $conditions .= ' AND warehouse_id LIKE :warehouse_id:';
            $bind['warehouse_id'] = "%{$params['warehouse_id']}%";

        } else if (isset($params['keywords'])) {
            $conditions .= ' AND (warehouse_id LIKE :keywords: OR warehouse_name LIKE :keywords:)';
            $bind['keywords'] = "%{$params['keywords']}%";
        }

        return ContractWarehouseModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => [
                'warehouse_id',
                'warehouse_name',
                'real_area',
                'warehouse_latitude',
                'warehouse_longitude',
                'province_code',
                'city_code',
                'district_code'
            ],
            'limit' => $page_size,
            'order' => 'id DESC'
        ])->toArray();
    }

    /**
     * 获取指定状态的仓库数量
     *
     * @param int $warehouse_status
     * @return mixed
     */
    public function vacantWarehouseCount(int $warehouse_status)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->from(ContractWarehouseModel::class);
        $builder->where('warehouse_status = :warehouse_status:', ['warehouse_status' => $warehouse_status]);
        return (int)$builder->columns('COUNT(id) AS total')->getQuery()->getSingleResult()->total;
    }

    /**
     * 仅查询特定条件下的仓库
     * @param array $params 查询参数组
     * @param string $columns 字段
     * @return mixed
     */
    public function onlySearchWarehouse(array $params, string $columns = 'id, warehouse_id, warehouse_name')
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(ContractWarehouseModel::class);

        //支持通过仓库ID和仓库名称模糊搜索
        $name = $params['name'] ?? '';
        if (!empty($name)) {
            $builder->andWhere('warehouse_id like :name: or warehouse_name like :name:', ['name' => '%' . $name . '%']);
        }
        $builder->orderBy('warehouse_id ASC');
        $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 根据名称获取仓库信息列表
     *
     * @param array $name_list
     * @return mixed
     */
    public function getListByName(array $name_list)
    {
        $name_list = array_unique(array_filter($name_list));
        if (empty($name_list)) {
            return [];
        }

        return ContractWarehouseModel::find([
            'conditions' => 'warehouse_name IN ({name_list:array})',
            'bind'       => ['name_list' => array_values($name_list)],
            'columns'    => ['warehouse_name', 'warehouse_id'],
        ])->toArray();
    }

    /**
     * 根据仓库ID获取仓库信息
     *
     * @param array $warehouse_ids
     * @return mixed
     */
    public function getListByWarehouseIds(array $warehouse_ids)
    {
        $warehouse_ids = array_filter($warehouse_ids);
        if (empty($warehouse_ids)) {
            return [];
        }

        $list = ContractWarehouseModel::find([
            'conditions' => 'warehouse_id IN ({warehouse_ids:array})',
            'bind'       => ['warehouse_ids' => array_values(array_unique($warehouse_ids))],
            'columns'    => ['id', 'warehouse_id', 'warehouse_name'],
        ])->toArray();
        return array_column($list, null, 'warehouse_id');
    }

}
