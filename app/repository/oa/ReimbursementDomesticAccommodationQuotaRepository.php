<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementDomesticAccommodationQuotaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementDomesticAccommodationQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $position_type
     * @param $area_type
     * @param $job_level
     * @return mixed
     */
    public function getOneByType($position_type, $area_type, $job_level)
    {
        return ReimbursementDomesticAccommodationQuotaModel::findFirst([
            'conditions' => 'position_type = :position_type: AND area_type = :area_type: AND job_level = :job_level:',
            'bind'       => [
                'position_type' => $position_type,
                'area_type'     => $area_type,
                'job_level'     => $job_level,
            ],
        ]);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementDomesticAccommodationQuotaModel::findFirst($id);
    }

}
