<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementDomesticAccommodationAreaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementDomesticAccommodationAreaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $province_code
     * @param string $city_code
     * @return mixed
     */
    public function getOneByAreaCode($province_code, string $city_code = '')
    {
        return ReimbursementDomesticAccommodationAreaModel::findFirst([
            'conditions' => 'province_code = :province_code: AND city_code = :city_code:',
            'bind'       => [
                'province_code' => $province_code,
                'city_code'     => $city_code,
            ],
        ]);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementDomesticAccommodationAreaModel::findFirst($id);
    }

}
