<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementTripTimeLimitModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementTripTimeLimitRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取默认配置规则
     *
     * @param int $id
     * @return mixed
     */
    public function getOneById(int $id = 1)
    {
        return ReimbursementTripTimeLimitModel::findFirst($id);
    }

}
