<?php

namespace App\Repository\oa;

use App\Modules\BankFlow\Models\BankFlowModel;
use App\Repository\BaseRepository;

/**
 * Class BankFlowRepository
 * @package App\Repository\oa
 * @date 2023/8/30
 */
class BankFlowRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankFlowRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取银行流水关联业务单号的数据
     * @param $ids
     * @return array[object]
     */
    public function getBankFlow($ids)
    {
        return BankFlowModel::find([
            'conditions' => ' id in ({ids:array})',
            'bind' => [
                'ids' => array_values($ids)
            ]
        ]);
    }

    public function getBankFlowByAccountId($account_id)
    {
        return BankFlowModel::findFirst([
            'conditions' => ' bank_account_id = :bank_account_id:',
            'bind' => [
                'bank_account_id' => $account_id
            ]
        ]);
    }
}
