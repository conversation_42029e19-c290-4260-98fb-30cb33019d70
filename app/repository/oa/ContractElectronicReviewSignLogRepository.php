<?php

namespace App\Repository\oa;

use App\Library\Enums\ContractEnums;
use App\Models\oa\ContractElectronicReviewSignLogModel;
use App\Repository\BaseRepository;

/**
 * Class ContractElectronicReviewSignLogRepository
 * @package App\Repository
 */
class ContractElectronicReviewSignLogRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取BD复核已通过的页码
     *
     * @param string $contract_no
     * @param string $sign_version
     * @param int $user_id
     * @return mixed
     */
    public function getReviewedSignPageTotal(string $contract_no, string $sign_version, int $user_id)
    {
        return ContractElectronicReviewSignLogModel::count([
            'conditions' => 'electronic_no = :electronic_no: AND sign_version = :sign_version: AND staff_id = :staff_id: AND review_status = :review_status:',
            'bind' => [
                'electronic_no' => $contract_no,
                'sign_version' => $sign_version,
                'staff_id' => $user_id,
                'review_status' => ContractEnums::ELECTRONIC_CONTRACT_BD_REVIEW_SIGN_STATUS_PASS
            ]
        ]);
    }

}
