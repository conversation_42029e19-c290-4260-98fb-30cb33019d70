<?php

namespace App\Repository\oa;

use App\Models\oa\AccountingRuleDepartmentListModel;
use App\Repository\BaseRepository;

/**
 * 子公司会计规则的关联部门
 * Class AccountingRuleDepartmentListRepository
 * @package App\Repository\oa
 */
class AccountingRuleDepartmentListRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取会计规则关联的部门列表
     *
     * @param array $rule_ids
     * @return array
     */
    public function getListByRuleIds(array $rule_ids)
    {
        if (empty($rule_ids)) {
            return [];
        }

        return AccountingRuleDepartmentListModel::find([
            'conditions' => 'accounting_rule_id IN ({rule_ids:array})',
            'bind' => ['rule_ids' => $rule_ids],
        ])->toArray();
    }

}
