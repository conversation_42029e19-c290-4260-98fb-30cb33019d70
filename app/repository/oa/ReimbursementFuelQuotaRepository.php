<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementFuelQuotaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementFuelQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementFuelQuotaModel::findFirst($id);
    }

    /**
     * @param $position_type
     * @param $expenses_type
     * @param $oil_type
     * @return mixed
     */
    public function getOneByType($position_type, $expenses_type, $oil_type)
    {
        return ReimbursementFuelQuotaModel::findFirst([
            'conditions' => 'position_type = :position_type: AND expenses_type = :expenses_type: AND oil_type = :oil_type:',
            'bind'       => [
                'position_type' => $position_type,
                'expenses_type' => $expenses_type,
                'oil_type'      => $oil_type,
            ],
        ]);
    }

}
