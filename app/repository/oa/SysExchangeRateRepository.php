<?php

namespace App\Repository\oa;

use App\Models\oa\SysExchangeRateModel;
use App\Repository\BaseRepository;

/**
 *
 */
class SysExchangeRateRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $selling_date
     * @return mixed
     */
    public function getCurrencyListBySellingDate($selling_date)
    {
        $list = SysExchangeRateModel::find([
            'conditions' => 'selling_date = :selling_date:',
            'bind'       => ['selling_date' => $selling_date],
            'columns'    => ['currency_symbol'],
        ])->toArray();

        return array_column($list, 'currency_symbol');
    }

    /**
     * 根据币种和日期获取相应的汇率
     *
     * @param $currency
     * @param $selling_date
     * @return mixed
     */
    public function getExchangeRateByCurrencyAndSellingDate($currency, $selling_date)
    {
        return SysExchangeRateModel::findFirst([
                'conditions' => 'currency = :currency: AND selling_date <= :selling_date:',
                'bind'       => ['currency' => $currency, 'selling_date' => $selling_date],
                'columns'    => ['exchange_rate'],
                'order'      => 'selling_date DESC',
            ])->exchange_rate ?? null;
    }


}
