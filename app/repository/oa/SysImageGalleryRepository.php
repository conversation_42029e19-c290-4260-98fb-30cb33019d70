<?php

namespace App\Repository\oa;

use App\Models\oa\SysImageGalleryModel;
use App\Repository\BaseRepository;

class SysImageGalleryRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取图片库文件列表
     *
     * @param array $object_keys
     * @return mixed
     */
    public function getList(array $object_keys = [])
    {
        if (empty($object_keys)) {
            return [];
        }

        return SysImageGalleryModel::find([
            'conditions' => 'object_key IN ({object_key:array})',
            'bind' => ['object_key' => $object_keys]
        ]);
    }
}
