<?php
namespace App\Repository\oa;
use App\Models\oa\MaterialSetReturnRepairGroupModel;
use App\Repository\BaseRepository;

/**
 * Class MaterialSetReturnGroupRepository
 * @package App\Repository\oa
 */
class MaterialSetReturnGroupRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取组详情
     * @param integer $id 组ID
     * @return array
     */
    public function getDetail($id)
    {
        if (empty($id)) {
            return [];
        }
        $info = MaterialSetReturnRepairGroupModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => [
                'id' => $id
            ],
        ]);
        return $info ? $info->toArray() : [];
    }

    /**
     * 获取指定退回组信息
     * @param array $group_ids 退回组ID
     * @return array
     */
    public function getGroupListByIds($group_ids = [])
    {
        $group_list = [];
        if (!empty($group_ids)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(MaterialSetReturnRepairGroupModel::class);
            $builder->inWhere('id', $group_ids);
            $builder->columns('id, name_zh, name_en, name_local, email, mobile, address');
            $group_list = $builder->getQuery()->execute()->toArray();
            $group_list = array_column($group_list, null, 'id');
        }
        return $group_list;
    }
}
