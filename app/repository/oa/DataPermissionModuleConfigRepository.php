<?php

namespace App\Repository\oa;

use App\Models\oa\DataPermissionManagePowerListModel;
use App\Models\oa\DataPermissionModuleConfigModel;
use App\Models\oa\DataPermissionModuleConfigPowerModel;
use App\Repository\BaseRepository;

/**
 * 系统配置管理-数据配置
 * Class DataPermissionModuleConfigRepository
 * @package App\Repository\oa
 */
class DataPermissionModuleConfigRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取所有数据配置模块
     */
    public function getAllModuleConfig()
    {
        return DataPermissionModuleConfigModel::find()->toArray();
    }

    /**
     * 获取某业务模块下指定管辖人的管辖范围
     *
     * @param int $module_id 业务模块ID
     * @param array $staff_ids 管辖人工号列表
     * @return array
     */
    public function getDepartmentListByStaffIds(int $module_id, array $staff_ids)
    {
        $result = [];

        $builder = $this->modelsManager->createBuilder();
        $columns = 'power_list.department_id, power_list.is_include_sub, power_list.staff_info_id';
        $builder->columns($columns);
        $builder->from(['config' => DataPermissionModuleConfigModel::class]);
        $builder->leftjoin(DataPermissionModuleConfigPowerModel::class, 'config.id = power.cid', 'power');
        $builder->leftjoin(DataPermissionManagePowerListModel::class, 'power.id = power_list.pid', 'power_list');
        $builder->where('config.module_id = :module_id:', ['module_id' => $module_id]);
        $builder->inWhere('power_list.staff_info_id', $staff_ids);
        $list = $builder->getQuery()->execute()->toArray();
        foreach ($list as $v) {
            $result[$v['staff_info_id']][] = $v;
        }

        return $result;
    }
}
