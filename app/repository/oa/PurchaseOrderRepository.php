<?php

namespace App\Repository\oa;

use App\Modules\Purchase\Models\PurchaseOrder;
use App\Repository\BaseRepository;

/**
 * 采购订单表
 * Class PurchaseOrderRepository
 * @package App\Repository\oa
 */
class PurchaseOrderRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 PurchaseOrderRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getOrderDataByNos($nos)
    {
        return PurchaseOrder::find([
            'columns' => 'id, pono, status, cost_company',
            'conditions' => ' pono in ({ponos:array})',
            'bind' => [
                'ponos' => $nos,
            ],
        ])->toArray();
    }
}
