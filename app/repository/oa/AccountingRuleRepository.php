<?php

namespace App\Repository\oa;

use App\Models\oa\AccountingRuleModel;
use App\Models\oa\AccountingRuleDepartmentListModel;
use App\Repository\BaseRepository;

/**
 * 子公司会计科目规则
 * Class AccountingRuleRepository
 * @package App\Repository\oa
 */
class AccountingRuleRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取规则主数据
     *
     * @param int $id
     * @return mixed
     */
    public function getOneById(int $id)
    {
        return AccountingRuleModel::findFirst($id);
    }

    /**
     * 获取预算科目相关的规则配置
     *
     * @param array $budget_ids 预算科目ID集合
     * @return array
     */
    public function getRuleListByBudgetIds(array $budget_ids)
    {
        if (empty($budget_ids)) {
            return [];
        }

        return AccountingRuleModel::find([
            'conditions' => 'budget_object_id IN ({budget_ids:array})',
            'bind' => ['budget_ids' => array_values($budget_ids)],
            'columns' => [
                'id',
                'budget_object_id AS budget_id',
                'budget_object_product_no AS product_no',
                'cost_department_ids',
                'organization_type',
                'accounting_subjects_id AS subjects_id',
            ]
        ])->toArray();
    }
}
