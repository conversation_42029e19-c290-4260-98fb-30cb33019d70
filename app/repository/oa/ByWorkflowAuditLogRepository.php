<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Library\ErrCode;
use App\Library\Exception\BusinessException;
use App\Models\oa\ByWorkflowAuditLogModel;
use App\Repository\BaseRepository;

/**
 * OA业务的BY审批审批日志
 * Class ByWorkflowAuditLogRepository
 * @package App\Repository\oa
 */
class ByWorkflowAuditLogRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 添加 OA审批业务的BY审批日志
     *
     * @param $oa_biz_type
     * @param $oa_biz_id
     * @param $created_id
     * @param $approval_id
     * @param $audit_status
     * @return bool
     * @throws BusinessException
     */
    public function addAuditLog($oa_biz_type, $oa_biz_id, $created_id, $approval_id, $audit_status)
    {
        // OA审批业务 与 BY审批流关系
        $log_model = new ByWorkflowAuditLogModel();
        $log_data = [
            'biz_type' => $oa_biz_type,
            'biz_value' => $oa_biz_id,
            'staff_id' => $created_id,
            'approval_id' => $approval_id,
            'status' => $audit_status,
            'approval_time' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        if ($log_model->i_create($log_data) === false) {
            throw new BusinessException('by_workflow_audit_log写入失败' . get_data_object_error_msg($log_model), ErrCode::$BUSINESS_ERROR);
        }

        return true;
    }


}
