<?php

namespace App\Repository\oa;

use App\Library\Enums\ContractEnums;
use App\Library\Enums\GlobalEnums;
use App\Models\oa\ContractStoreRentingRemindModel;
use App\Repository\BaseRepository;

/**
 * 合同提醒
 * Class ContractStoreRentingNotRemindRepository
 * @package App\Repository\oa
 * @date 2023/7/17
 */
class ContractStoreRentingRemindRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ContractStoreRentingRemindRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取不提醒网点列表
     * @return object
     * @date 2023/7/17
     */
    public function getPendingData()
    {
        return ContractStoreRentingRemindModel::find([
            'conditions' => 'task_status = :task_status:',
            'bind' => [
                'task_status' => ContractEnums::TASK_STATUS_TODO
            ]
        ]);
    }
}
