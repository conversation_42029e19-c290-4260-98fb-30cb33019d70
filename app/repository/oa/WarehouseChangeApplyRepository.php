<?php

namespace App\Repository\oa;

use App\Models\oa\WarehouseChangeApplyModel;
use App\Repository\BaseRepository;

/**
 * 仓库管理-仓库变更申请
 * Class WarehouseChangeApplyRepository
 *
 * @package App\Repository\oa
 */
class WarehouseChangeApplyRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据仓库ID获取仓库信息
     *
     * @param string $warehouse_id
     * @return mixed
     */
    public function getOneByWarehouseId(string $warehouse_id)
    {
        return ContractWarehouseModel::findFirst([
            'conditions' => 'warehouse_id = :warehouse_id:',
            'bind' => ['warehouse_id' => $warehouse_id],
        ]);
    }

    /**
     * 根据主键ID获取仓库信息
     *
     * @param int $id
     * @return mixed
     */
    public function getOneById(int $id)
    {
        return WarehouseChangeApplyModel::findFirst([
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id],
        ]);
    }

}
