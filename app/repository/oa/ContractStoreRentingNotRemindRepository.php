<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\ContractStoreRentingNotRemindModel;
use App\Repository\BaseRepository;

/**
 * 合同不提醒网点
 * Class ContractStoreRentingNotRemindRepository
 * @package App\Repository\oa
 * @date 2023/7/17
 */
class ContractStoreRentingNotRemindRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ContractStoreRentingNotRemindRepository
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据网点ID获取数据
     * @param $store_id
     * @return array
     */
    public function getInfoByStoreId($store_id)
    {
        $info = ContractStoreRentingNotRemindModel::findFirst([
            'conditions' => 'store_id = :store_id: and is_deleted = :is_deleted:',
            'bind' => [
                'store_id' => $store_id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ]);

        return $info ? $info->toArray() : [];
    }

    /**
     * 根据ID获取数据
     * @param $id
     * @return object | array
     */
    public function getInfoById($id)
    {
        $info = ContractStoreRentingNotRemindModel::findFirst([
            'conditions' => 'id = :id: and is_deleted = :is_deleted:',
            'bind' => [
                'id' => $id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ]);

        return $info ? $info : [];
    }

    /**
     * 获取不提醒网点列表
     * @return mixed
     * @date 2023/7/17
     */
    public function getData()
    {
        return ContractStoreRentingNotRemindModel::find([
            'columns' => 'store_id',
            'conditions' => 'is_deleted = :is_deleted:',
            'bind' => [
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ]
        ])->toArray();
    }
}
