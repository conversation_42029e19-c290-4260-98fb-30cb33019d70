<?php

namespace App\Repository\oa;

use App\Models\oa\SysModuleModel;
use App\Repository\BaseRepository;

/**
 * 系统模块
 * Class SysModuleRepository
 * @package App\Repository\oa
 */
class SysModuleRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 SysModuleRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过ids查询数据
     * @param $ids
     * @return mixed
     */
    public function getModuleByIds($ids)
    {
        return SysModuleModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind' => [
                'ids' => $ids
            ]
        ])->toArray();
    }

    /**
     * 查询500条数据
     * @return mixed
     */
    public function getModule()
    {
        return SysModuleModel::find([
            'limit' => 500
        ])->toArray();
    }


    /**
     * 获取是否启用通用数据权限状态的业务模块列表
     *
     * @param int $status 通用数据权限启用状态 0-未启用; 1-已启用
     * @return mixed
     */
    public function getModuleByCommonDataPermissionStatus($status = 1)
    {
        return SysModuleModel::find([
            'conditions' => 'common_data_permission_status = :common_data_permission_status: ',
            'bind' => ['common_data_permission_status' => $status],
            'columns' => ['id', 'name_remark AS name']
        ])->toArray();
    }

    /**
     * 获取是否启用通用数据权限状态的业务模块数量
     *
     * @param int $status 通用数据权限启用状态 0-未启用; 1-已启用
     * @return mixed
     */
    public function getModuleTotalByCommonDataPermissionStatus($status = 1)
    {
        return SysModuleModel::count([
            'conditions' => 'common_data_permission_status = :common_data_permission_status: ',
            'bind' => ['common_data_permission_status' => $status],
        ]);
    }

    /**
     * 根据模块key获取模块信息
     *
     * @param string $key
     * @return mixed
     */
    public function getOneByKey(string $key)
    {
        return SysModuleModel::findFirst([
            'conditions' => 'name_key = :name_key: ',
            'bind' => ['name_key' => $key],
        ]);
    }

}
