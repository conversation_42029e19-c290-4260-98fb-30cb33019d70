<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementCarRentalQuotaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementCarRentalQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $position_type
     * @param $job_level
     * @return mixed
     */
    public function getOneByType($position_type, $job_level)
    {
        return ReimbursementCarRentalQuotaModel::findFirst([
            'conditions' => 'position_type = :position_type: AND job_level = :job_level:',
            'bind'       => [
                'position_type' => $position_type,
                'job_level'     => $job_level,
            ],
        ]);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementCarRentalQuotaModel::findFirst($id);
    }

}
