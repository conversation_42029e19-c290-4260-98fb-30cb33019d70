<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Models\oa\BudgetWithholdingModel;
use App\Repository\BaseRepository;

/**
 * 预算管理-费用预提
 * Class BudgetWithholdingRepository
 * @package App\Repository\oa
 */
class BudgetWithholdingRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankListRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取特定条件下的预算预提单
     * @param array $params 筛选条件组
     * @return mixed
     */
    public function getBudgetWithholdingListByParams(array $params)
    {
        $status             = $params['status'] ?? Enums::WF_STATE_APPROVED;//预提状态：1-待审核、2-已驳回、3-已通过、 4-已撤回、5-已关闭
        $no                 = $params['no'] ?? '';                          //预提单号
        $cost_store_type    = $params['cost_store_type'] ?? 0;              //费用总部/网点：1总部、2网点
        $budget_id          = $params['budget_id'] ?? 0;                    //预算科目ID
        $cost_department_id = $params['cost_department_id'] ?? 0;           //费用所属部门ID
        $builder            = $this->modelsManager->createBuilder();
        $builder->columns('*');
        $builder->from(BudgetWithholdingModel::class);
        $builder->where('status = :status:', ['status' => $status]);
        //预提单号
        if (!empty($no)) {
            $builder->andWhere('no = :no:', ['no' => $no]);
        }
        //费用总部/网点
        if (!empty($cost_store_type)) {
            $builder->andWhere('cost_store_type = :cost_store_type:', ['cost_store_type' => $cost_store_type]);
        }
        //预算科目ID
        if (!empty($budget_id)) {
            $builder->andWhere('budget_id = :budget_id:', ['budget_id' => $budget_id]);
        }
        //费用所属部门ID
        if (!empty($cost_department_id)) {
            if (is_array($cost_department_id)) {
                $builder->inWhere('cost_department_id', $cost_department_id);
            } else {
                $builder->andWhere('cost_department_id = :cost_department_id:', ['cost_department_id' => $cost_department_id]);
            }
        }
        $builder->orderBy('id ASC');
        return $builder->getQuery()->execute()->toArray();
    }
}
