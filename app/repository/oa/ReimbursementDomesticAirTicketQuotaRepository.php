<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementDomesticAirTicketQuotaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementDomesticAirTicketQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取默认的配置
     *
     * @param int $id
     * @return mixed
     */
    public function getOneById(int $id = 1)
    {
        return ReimbursementDomesticAirTicketQuotaModel::findFirst($id);
    }

}
