<?php

namespace App\Repository\oa;

use App\Models\oa\DataPermissionStaffModuleRelModel;
use App\Repository\BaseRepository;

/**
 * 数据配置-通用数据配置-管辖人与管辖范围关系表
 * Class DataPermissionStaffModuleRelRepository
 * @package App\Repository\oa
 */
class DataPermissionStaffModuleRelRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取管辖人配置信息
     *
     * @param int $id
     * @return mixed
     */
    public function getOne(int $id)
    {
        return DataPermissionStaffModuleRelModel::findFirst($id);
    }

    /**
     * 获取管辖人配置统计
     *
     * @param array $params
     * @return mixed
     */
    public function getTotalBySearch(array $params = [])
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('COUNT(id) AS total');
        $builder->from(DataPermissionStaffModuleRelModel::class);
        $builder->where('permission_category_id = :permission_category_id:', ['permission_category_id' => $params['permission_category_id']]);

        if (!empty($params['staff_info_id'])) {
            $builder->andWhere('staff_info_id = :staff_info_id:', ['staff_info_id' => $params['staff_info_id']]);
        }

        if (!empty($params['node_department_id'])) {
            $builder->andWhere('node_department_id = :node_department_id:', ['node_department_id' => $params['node_department_id']]);
        }

        if (!empty($params['configure_status'])) {
            $builder->andWhere('configure_status = :configure_status:', ['configure_status' => $params['configure_status']]);
        }

        return (int)$builder->getQuery()->getSingleResult()->total;
    }

    /**
     * 获取指定权限分类下指定员工的配置列表
     *
     * @param int $permission_category_id
     * @param int $staff_id
     * @return array
     */
    public function getListByPermissionCategoryIdAndStafffId(int $permission_category_id, int $staff_id)
    {
        return DataPermissionStaffModuleRelModel::find([
            'conditions' => 'permission_category_id = :permission_category_id: AND staff_info_id = :staff_info_id:',
            'bind' => ['permission_category_id' => $permission_category_id, 'staff_info_id' => $staff_id],
            'columns' => ['id', 'module_ids']
        ])->toArray();
    }

    /**
     * 获取管辖人模块配置详情
     *
     * @param int $id 配置详情ID
     * @param int $permission_category_id 所属权限分类ID
     * @return mixed
     */
    public function getDetail(int $id, int $permission_category_id)
    {
        return DataPermissionStaffModuleRelModel::findFirst([
            'conditions' => 'id = :id: AND permission_category_id = :permission_category_id:',
            'bind' => ['id' => $id, 'permission_category_id' => $permission_category_id],
        ]);
    }
}
