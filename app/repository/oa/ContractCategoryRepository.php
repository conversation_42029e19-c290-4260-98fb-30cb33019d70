<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\ContractCategoryModel;
use Phalcon\Mvc\Model\ResultsetInterface;
use App\Repository\BaseRepository;

/**
 * 其他合同分类相关方法
 * Class ContractCategoryRepository
 * @package App\Repository
 */
class ContractCategoryRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据父类ID获取子类别列表
     * @param $ancestry_id
     * @return array
     */
    public function getSubCategoryIdsByAncestryId($ancestry_id)
    {
        $list =  ContractCategoryModel::find([
            'conditions' => 'ancestry_id = :ancestry_id: AND is_deleted = :is_deleted:',
            'bind'       => [
                'ancestry_id' => $ancestry_id,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
        ])->toArray();

        return array_column($list, 'id');
    }
}
