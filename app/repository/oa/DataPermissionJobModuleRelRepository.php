<?php

namespace App\Repository\oa;

use App\Models\oa\DataPermissionJobModuleRelModel;
use App\Models\oa\DataPermissionCategoryModel;
use App\Repository\BaseRepository;

/**
 * 数据配置-通用数据配置-职位与业务模块关系表
 * Class DataPermissionJobModuleRelRepository
 * @package App\Repository\oa
 */
class DataPermissionJobModuleRelRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取职位配置模型
     *
     * @param int $id 职位模块配置ID
     * @return mixed
     */
    public function getOne(int $id)
    {
        return DataPermissionJobModuleRelModel::findFirst($id);
    }

    /**
     * 获取职位配置列表
     *
     * @param int $permission_category_id 权限分类ID
     * @param array $job_ids 职位ID
     *
     * @return mixed
     */
    public function getListByCategoryJobIds(int $permission_category_id, $job_ids = [])
    {
        return DataPermissionJobModuleRelModel::find([
            'conditions' => 'permission_category_id = :permission_category_id: AND job_id IN ({job_ids:array})',
            'bind' => ['permission_category_id' => $permission_category_id, 'job_ids' => array_values($job_ids)],
        ]);
    }

    /**
     * 获取职位配置搜索列表
     *
     * @param int $permission_category_id 权限分类ID
     * @param string $job_name 职位名称
     * @return mixed
     */
    public function getListBySearch(int $permission_category_id, string $job_name = '')
    {
        $conditions = 'permission_category_id = :permission_category_id:';
        $bind = ['permission_category_id' => $permission_category_id];

        if (!empty($job_name)) {
            $conditions .= ' AND job_name LIKE :job_name:';
            $bind['job_name'] = "$job_name%";
        }

        return DataPermissionJobModuleRelModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => ['id', 'permission_category_id', 'job_id', 'job_name', 'module_ids'],
            'order' => 'id DESC'
        ])->toArray();
    }

    /**
     * 获取职位模块配置详情
     *
     * @param int $id 配置详情ID
     * @param int $permission_category_id 所属权限分类ID
     * @return mixed
     */
    public function getDetail(int $id, int $permission_category_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns([
            'rel.id',
            'rel.permission_category_id',
            'cate.category_name AS permission_category_name',
            'rel.job_id',
            'rel.job_name',
            'rel.module_ids'
        ]);

        $builder->from(['rel' => DataPermissionJobModuleRelModel::class]);
        $builder->leftjoin(DataPermissionCategoryModel::class, 'rel.permission_category_id = cate.id','cate');
        $builder->where('rel.id = :id:', ['id' => $id]);
        $builder->andWhere('rel.permission_category_id = :permission_category_id:', ['permission_category_id' => $permission_category_id]);

        $result = $builder->getQuery()->getSingleResult();
        return !empty($result) ? $result->toArray() : [];
    }

    /**
     * 获取指定职位配置(属于某权限分类下某职位的某业务模块列表)
     *
     * @param int $permission_category_id 权限分类ID
     * @param int $job_id 职位ID
     * @param int $module_id 业务模块ID
     * @return array
     */
    public function getListByCategoryJobModuleId(int $permission_category_id, int $job_id, int $module_id)
    {
        return DataPermissionJobModuleRelModel::find([
            'conditions' => 'permission_category_id = :permission_category_id: AND job_id = :job_id: AND FIND_IN_SET(:module_id:, module_ids)',
            'bind' => ['permission_category_id' => $permission_category_id, 'job_id' => $job_id, 'module_id' => $module_id],
            'columns' => ['id']
        ])->toArray();

    }

}
