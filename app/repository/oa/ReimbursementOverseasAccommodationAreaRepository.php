<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementOverseasAccommodationAreaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementOverseasAccommodationAreaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $country_id
     * @return mixed
     */
    public function getOneByCountryId($country_id)
    {
        return ReimbursementOverseasAccommodationAreaModel::findFirst([
            'conditions' => 'country_id = :country_id:',
            'bind'       => [
                'country_id' => $country_id,
            ],
        ]);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementOverseasAccommodationAreaModel::findFirst($id);
    }

}
