<?php

namespace App\Repository\oa;

use App\Models\oa\WorkflowNodeModel;
use App\Modules\Workflow\Models\WorkflowModel;
use App\Modules\Workflow\Models\WorkflowNodeRelateModel;
use App\Repository\BaseRepository;

/**
 * 审批流模块
 * Class WorkflowNodeRelateRepository
 * @package App\Repository\oa
 */
class WorkflowNodeRelateRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowNodeRelateRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过flow_id查询审批流流转数据
     * @param $flow_id
     * @return mixed
     * @date 2023/6/1
     */
    public function getWorkflowNodeRelateByFlowId($flow_id)
    {
        $builder = $this->modelsManager->createBuilder();
        $columns = 'wn.id, wn.name, wn.type, wn.auditor_type, wnr.valuate_formula, wnr.from_node_id, wnr.to_node_id, wnr.valuate_code, wnr.sort, wn.node_audit_type';
        $builder->columns($columns);
        $builder->from(['wnr' => WorkflowNodeRelateModel::class]);
        $builder->leftjoin(WorkflowNodeModel::class, 'wnr.from_node_id = wn.id', 'wn');
        $builder->where('wnr.flow_id = :flow_id:', ['flow_id' => $flow_id]);
        $builder->orderby('wnr.sort desc, wnr.id asc');
        $items = $builder->getQuery()->execute()->toArray();
        return $items;
    }
}
