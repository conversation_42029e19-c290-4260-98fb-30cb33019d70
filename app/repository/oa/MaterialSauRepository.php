<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\MaterialSauPurchaseStaffsModel;
use App\Models\oa\MiniHrStaffInfoModel;
use App\Modules\Material\Models\MaterialSauModel;
use App\Repository\BaseRepository;

/**
 * Class MaterialSauRepository
 * @package App\Repository\oa
 */
class MaterialSauRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     *
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 根据barcode查询信息
     * @param $barcode
     * @return array
     */
    public function getInfoByBarcode($barcode)
    {
        if (empty($barcode)) {
            return [];
        }
        $info = MaterialSauModel::findFirst([
            'conditions' => 'barcode = :barcode: AND is_deleted = :is_deleted:',
            'bind' => [
                'barcode' => $barcode,
                'is_deleted' => GlobalEnums::IS_NO_DELETED
            ],
        ]);
        return $info ? $info->toArray() : [];
    }

    /**
     * 搜索标准型号
     * @param array $params 请求参数
     * @return mixed
     */
    public function searchBarcode($params)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('ms.barcode, ms.name_zh, ms.name_en, ms.name_local');
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->where('ms.is_deleted = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //名称搜索
        if (!empty($params['name'])) {
            $builder->andWhere('ms.' . get_lang_field_name('name_', $params['lang']) . ' like :name:', ['name' => '%' . $params['name'] . '%']);
        }
        //barcode模糊搜索
        if (!empty($params['barcode_like'])) {
            $builder->andWhere('ms.barcode like :barcode:', ['barcode' => '%' . $params['barcode_like'] . '%']);
        }
        //资产类型
        if (!empty($params['category_type'])) {
            $builder->andWhere('ms.category_type = :category_type:', ['category_type' => $params['category_type']]);
        }
        //启用状态
        if (!empty($params['status'])) {
            $builder->andWhere('ms.status = :status:', ['status' => $params['status']]);
        }
        $builder->limit($params['limit'] ?? GlobalEnums::DEFAULT_PAGE_SIZE);
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取一批标准型号下的采购员
     * @param array $sau_ids 标准型号ID组
     * @param boolean $is_group 是否分号组
     * @return mixed
     */
    public function getBarcodePurchaseStaffs($sau_ids, $is_group = true)
    {
        if (empty($sau_ids)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->from(['staff' => MaterialSauPurchaseStaffsModel::class]);
        $builder->leftJoin(MiniHrStaffInfoModel::class, 'staff.staff_id = hr.staff_info_id', 'hr');
        $builder->columns('staff.sau_id, staff.staff_id, hr.name');
        $builder->inWhere('staff.sau_id', $sau_ids);
        $list = $builder->getQuery()->execute()->toArray();
        if ($is_group) {
            $group_list = [];
            foreach ($list as $item) {
                $group_list[$item['sau_id']]['staffs'][] = $item['name'] . '（' . $item['staff_id'] . '）';
                $group_list[$item['sau_id']]['purchase_staff_ids'][] = $item['staff_id'];
                $group_list[$item['sau_id']]['purchase_staff'][] = ['staff_info_id' => $item['staff_id'], 'name' => $item['name']];
            }
            $list = $group_list;
        }
        return $list;
    }

    /**
     * 获取某个barcode下的采购员工号组
     * @param string $barcode barcode
     * @return array
     */
    public function getBarcodePurchaseStaffIds($barcode)
    {
        if (empty($barcode)) {
            return [];
        }

        $builder = $this->modelsManager->createBuilder();
        $builder->from(['ms' => MaterialSauModel::class]);
        $builder->leftJoin(MaterialSauPurchaseStaffsModel::class, 'staff.sau_id = ms.id', 'staff');
        $builder->columns('staff.staff_id');
        $builder->where('ms.barcode = :barcode:', ['barcode' => $barcode]);
        $list = $builder->getQuery()->execute()->toArray();
        return array_column($list, 'staff_id');
    }
}
