<?php

namespace App\Repository\oa;

use App\Models\oa\ChequeAccountModel;
use App\Repository\BaseRepository;

/**
 * Class ChequeAccountRepository
 * @package App\Repository\oa
 * @date 2023/8/30
 */
class ChequeAccountRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 ChequeAccountRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }


    /**
     * 根据银行账号查询一条支票信息
     * @param $account
     * @return mixed
     */
    public function getChequeByAccount($account)
    {
        return ChequeAccountModel::findFirst([
            'conditions' => 'bank_account = :bank_account:',
            'bind' => [
                'bank_account' => $account
            ]
        ]);
    }

    /**
     * 根据支票号批量查询支票信息
     *
     * @param array $cheque_codes
     * @return mixed
     */
    public function getChequeListByChequeCodes(array $cheque_codes)
    {
        if (empty($cheque_codes)) {
            return [];
        }

        return ChequeAccountModel::find([
            'conditions' => 'cheque_code IN ({cheque_codes:array})',
            'bind' => [
                'cheque_codes' => $cheque_codes
            ],
        ])->toArray();
    }
}
