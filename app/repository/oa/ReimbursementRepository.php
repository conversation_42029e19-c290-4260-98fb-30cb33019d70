<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Modules\Reimbursement\Models\Reimbursement;
use App\Repository\BaseRepository;

/**
 * Class ReimbursementRepository
 * @package App\Repository\oa
 */
class ReimbursementRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 ReimbursementRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取报销超时未支付单据
     * @param string $days 超时日期
     * @return array
     */
    public function getTimeOutData($days)
    {
        return Reimbursement::find([
            'conditions' => ' approved_at < :approved_at: AND status = :status: AND pay_status = :pay_status:',
            'bind' => [
                'approved_at' => $days . ' 00:00:00',
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING
            ]
        ])->toArray();
    }

    /**
     * 获取审批已通过但待支付的报销列表
     *
     * @param array $nos 报销单号列表
     * @return array
     */
    public function getPendingPayListByNos(array $nos = [])
    {
        if (empty($nos)) {
            return [];
        }

        return Reimbursement::find([
            'conditions' => 'no IN ({nos:array}) AND status = :status: AND pay_status = :pay_status:',
            'bind' => [
                'nos' => $nos,
                'status' => Enums::WF_STATE_APPROVED,
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING
            ]
        ])->toArray();
    }
}
