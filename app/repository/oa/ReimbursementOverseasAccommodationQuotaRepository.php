<?php

namespace App\Repository\oa;

use App\Models\oa\ReimbursementOverseasAccommodationQuotaModel;
use App\Repository\BaseRepository;

/**
 *
 */
class ReimbursementOverseasAccommodationQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * @param $position_type
     * @param $accommodation_type
     * @return mixed
     */
    public function getOneByType($position_type, $accommodation_type)
    {
        return ReimbursementOverseasAccommodationQuotaModel::findFirst([
            'conditions' => 'position_type = :position_type: AND accommodation_type = :accommodation_type:',
            'bind'       => [
                'position_type'      => $position_type,
                'accommodation_type' => $accommodation_type,
            ],
        ]);
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getOneById($id)
    {
        return ReimbursementOverseasAccommodationQuotaModel::findFirst($id);
    }

}
