<?php

namespace App\Repository\oa;

use App\Models\oa\DataPermissionCategoryModel;
use App\Repository\BaseRepository;

/**
 * 数据配置-通用数据配置-数据权限分类
 * Class DataPermissionCategoryRepository
 * @package App\Repository\oa
 */
class DataPermissionCategoryRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取权限分类信息
     *
     * @param int $id 分类ID
     * @return array
     */
    public function getInfo(int $id)
    {
        $result = DataPermissionCategoryModel::findFirst($id);
        return !empty($result) ? $result->toArray() : [];
    }

    /**
     * 获取所有权限分类列表
     *
     * @param string $priority_weight_order 权重排序方式 ASC 正序; DESC 倒序
     * @return array
     */
    public function getAllList($priority_weight_order = 'ASC')
    {
        $order = $priority_weight_order == 'ASC' ? 'ASC' : 'DESC';

        return DataPermissionCategoryModel::find([
            'columns' => ['id', 'category_flag', 'category_name', 'rule_desc', 'priority_desc', 'is_can_configured'],
            'order' => "priority_weight $order",
        ])->toArray();
    }

    /**
     * 获取不同配置维度的权限分类ID列表
     *
     * @param int $configure_reference 1-职位; 2-管辖人
     * @return array
     */
    public function getListByConfigureReference($configure_reference = 1)
    {
        $list = DataPermissionCategoryModel::find([
            'conditions' => 'configure_reference = :configure_reference:',
            'bind' => ['configure_reference' => $configure_reference],
        ])->toArray();
        return array_column($list, 'id');
    }

}
