<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\ReimbursementBankChargesStoreQuotaModel;
use App\Repository\BaseRepository;


class ReimbursementBankChargesStoreQuotaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取指定网点的额度配置
     * @param array $store_ids
     * @return mixed
     */
    public function getListByStoreIds(array $store_ids)
    {
        if (empty($store_ids)) {
            return [];
        }

        $list = ReimbursementBankChargesStoreQuotaModel::find([
            'conditions' => 'store_id IN ({store_ids:array}) AND is_deleted = :is_deleted:',
            'bind'       => [
                'store_ids'  => array_values($store_ids),
                'is_deleted' => GlobalEnums::IS_NO_DELETED,
            ],
            'columns'    => ['store_id', 'month_amount'],
        ])->toArray();

        return array_column($list, 'month_amount', 'store_id');
    }

}
