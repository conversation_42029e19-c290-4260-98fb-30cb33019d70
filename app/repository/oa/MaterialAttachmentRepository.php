<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Modules\Material\Models\MaterialAttachmentModel;
use <PERSON>alcon\Mvc\Model\ResultsetInterface;
use App\Repository\BaseRepository;

/**
 * OA资产、耗材系统图片附件上传
 * Class MaterialAttachmentRepository
 * @package App\Repository
 */
class MaterialAttachmentRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 SysAttachmentRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取某业务单据的相关附件
     *
     * @param int $biz_id 业务单据ID
     * @param int $oss_bucket_type 业务类型定义
     * @param int $sub_type 业务子类型定义
     * @return array
     */
    public function getAttachmentsByBizParams(int $biz_id, int $oss_bucket_type, int $sub_type = 0): array
    {
        return MaterialAttachmentModel::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key = :oss_bucket_key: AND sub_type = :sub_type: AND deleted = :deleted:',
            'bind' => [
                'oss_bucket_type' => $oss_bucket_type,
                'oss_bucket_key' => $biz_id,
                'sub_type' => $sub_type,
                'deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => ['bucket_name', 'object_key', 'file_name']
        ])->toArray();
    }

    /**
     * 批量获取某业务下多单据的相关附件
     *
     * @param array $biz_ids 业务单据ID组
     * @param int $oss_bucket_type 业务类型定义
     * @param int $sub_type 业务子类型定义
     * @return array
     */
    public function getAttachments(array $biz_ids, int $oss_bucket_type, int $sub_type = 0)
    {
        return MaterialAttachmentModel::find([
            'conditions' => 'oss_bucket_type = :oss_bucket_type: AND oss_bucket_key in({oss_bucket_keys:array}) AND sub_type = :sub_type: AND deleted = :deleted:',
            'bind' => [
                'oss_bucket_type' => $oss_bucket_type,
                'oss_bucket_keys' => $biz_ids,
                'sub_type' => $sub_type,
                'deleted' => GlobalEnums::IS_NO_DELETED
            ],
            'columns' => 'id, oss_bucket_key, bucket_name, object_key, file_name'
        ])->toArray();
    }

    /**
     * 批量获取某业务下多单据的相关附件并按照每笔单据维度返回附件信息组
     *
     * @param array $biz_ids 业务单据ID组
     * @param int $oss_bucket_type 业务类型定义
     * @param int $sub_type 业务子类型定义
     * @return array
     */
    public function getAttachmentsListById(array $biz_ids, int $oss_bucket_type, int $sub_type = 0)
    {
        $attachment_result = [];
        $attachment_list = $this->getAttachments($biz_ids, $oss_bucket_type, $sub_type);
        if (!empty($attachment_list)) {
            foreach ($attachment_list as $attachment) {
                if (isset($attachment_result[$attachment['oss_bucket_key']])) {
                    array_push($attachment_result[$attachment['oss_bucket_key']], $attachment);
                } else {
                    $attachment_result[$attachment['oss_bucket_key']][] = $attachment;
                }
            }
        }
        return $attachment_result;
    }
}
