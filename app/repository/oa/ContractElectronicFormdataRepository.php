<?php

namespace App\Repository\oa;

use App\Models\oa\ContractElectronicFormdataModel;
use App\Repository\BaseRepository;

/**
 * Class ContractElectronicFormdataRepository
 * @package App\Repository
 */
class ContractElectronicFormdataRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取电子合同表单数据
     *
     * @param string $no
     * @return mixed
     */
    public function getFormDataByNo(string $no)
    {
        if (empty($no)) {
            return [];
        }

        $model = ContractElectronicFormdataModel::findFirst([
            'conditions' => 'electronic_no = :electronic_no:',
            'bind' => ['electronic_no' => $no]
        ]);

        return !empty($model->form_data) ? json_decode($model->form_data, true) : [];
    }




}
