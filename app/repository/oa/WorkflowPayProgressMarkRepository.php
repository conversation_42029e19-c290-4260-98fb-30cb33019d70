<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\WorkflowPayProgressMarkModel;
use App\Repository\BaseRepository;

class WorkflowPayProgressMarkRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取单条配置
     * @param string $translation_key
     * @return mixed
     */
    public function getOneByTranslationkey(string $translation_key)
    {
        return WorkflowPayProgressMarkModel::findFirst([
            'conditions' => 'translation_key = :translation_key:',
            'bind' => ['translation_key' => $translation_key]
        ]);
    }

    /**
     * 获取最大ID
     */
    public function getMaxId()
    {
        return WorkflowPayProgressMarkModel::findFirst([
            'columns' => 'MAX(id) AS max_id'
        ])->max_id ?? 0;
    }

    /**
     * 获取配置列表
     *
     * @param string $keywords
     * @param int $is_deleted
     * @return array
     */
    public function getList(string $keywords, $is_deleted = GlobalEnums::IS_NO_DELETED)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, translation_key, remark');
        $builder->from(WorkflowPayProgressMarkModel::class);
        if (in_array($is_deleted, [GlobalEnums::IS_NO_DELETED, GlobalEnums::IS_DELETED])) {
            $builder->andWhere('is_deleted = :is_deleted:', ['is_deleted' => $is_deleted]);
        }

        if (!empty($keywords)) {
            $builder->andWhere('remark LIKE :remark:', ['remark' => "%{$keywords}%"]);
        }

        return $builder->getQuery()->execute()->toArray();
    }
}
