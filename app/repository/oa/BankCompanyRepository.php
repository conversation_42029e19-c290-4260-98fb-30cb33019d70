<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\BankCompanyModel;
use App\Modules\BankFlow\Models\BankListModel;
use App\Repository\BaseRepository;

/**
 * Class BankCompanyRepository
 * @package App\Repository\oa
 * @date 2023/8/30
 */
class BankCompanyRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankCompanyRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取公司列表
     * @param array $company_ids
     * @return array
     */
    public function getBankCompanyList($company_ids = [])
    {
        $find_params = [];
        $find_params['columns'] = ['id', 'company_name', 'company_code', 'is_deleted'];
        if (!empty($bank_ids)) {
            $find_params['conditions'] = 'id in ({ids:array})';
            $find_params['bind'] = ['ids' => $company_ids];
        }
        return BankCompanyModel::find($find_params)->toArray();
    }

    /**
     * 获取公司信息
     * @param $id
     * @return array
     */
    public function getBankCompanyById($id)
    {
        if (empty($id)) {
            return [];
        }
        $bank_company_info = BankCompanyModel::findFirst([
            'columns' => ['id', 'company_name', 'company_code', 'is_deleted'],
            'conditions' => 'id = :id:',
            'bind' => ['id' => $id]
        ]);
        return $bank_company_info ? $bank_company_info->toArray() : [];
    }
}
