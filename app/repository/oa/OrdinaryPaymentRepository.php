<?php

namespace App\Repository\oa;

use App\Library\Enums;
use App\Library\Enums\PayEnums;
use App\Modules\OrdinaryPayment\Models\OrdinaryPayment;
use App\Repository\BaseRepository;

/**
 * Class OrdinaryPaymentRepository
 * @package App\Repository\oa
 */
class OrdinaryPaymentRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 OrdinaryPaymentRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取报销超时未支付单据
     * @param string $days 超时日期
     * @return array
     */
    public function getTimeOutData($days)
    {
        return OrdinaryPayment::find([
            'conditions' => ' approved_at < :approved_at: AND approval_status = :approval_status: AND pay_status = :pay_status:',
            'bind' => [
                'approved_at' => $days . ' 00:00:00',
                'approval_status' => Enums::WF_STATE_APPROVED,
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING
            ]
        ])->toArray();
    }

    /**
     * 获取审批已通过但待支付的普通付款列表
     *
     * @param array $nos 单号列表
     * @return array
     */
    public function getPendingPayListByNos(array $nos = [])
    {
        if (empty($nos)) {
            return [];
        }

        return OrdinaryPayment::find([
            'conditions' => 'apply_no IN ({apply_nos:array}) AND approval_status = :approval_status: AND pay_status = :pay_status:',
            'bind' => [
                'apply_nos' => $nos,
                'approval_status' => Enums::WF_STATE_APPROVED,
                'pay_status' => PayEnums::PAYMENT_MODULE_PAY_STATUS_PENDING
            ]
        ])->toArray();
    }

}
