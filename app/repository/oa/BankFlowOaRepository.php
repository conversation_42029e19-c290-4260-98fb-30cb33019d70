<?php

namespace App\Repository\oa;

use App\Library\Enums\BankFlowEnums;
use App\Modules\BankFlow\Models\BankFlowOaModel;
use App\Repository\BaseRepository;

/**
 * Class BankFlowOaRepository
 * @package App\Repository\oa
 * @date 2023/8/30
 */
class BankFlowOaRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 BankFlowOaRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取银行流水关联业务单号的数据
     * @param $bank_flow_ids
     * @return array
     */
    public function getBankFlowOa($bank_flow_ids)
    {
        return BankFlowOaModel::find([
            'conditions' => ' bank_flow_id in ({bank_flow_ids:array}) and is_cancel = :is_cancel:',
            'bind' => [
                'bank_flow_ids' => array_values($bank_flow_ids),
                'is_cancel' => BankFlowEnums::BANK_FLOW_OA_IS_CANCEL_NO
            ]
        ])->toArray();
    }
}
