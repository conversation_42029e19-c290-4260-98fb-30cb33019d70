<?php

namespace App\Repository\oa;

use App\Models\oa\WorkflowBusinessModel;
use App\Repository\BaseRepository;

/**
 * 审批业务
 * Class WorkflowBusinessRepository
 * @package App\Repository\oa
 */
class WorkflowBusinessRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowBusinessRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过ids查询数据
     * @param $ids
     * @return mixed
     */
    public function getBusinessByIds($ids)
    {
        return WorkflowBusinessModel::find([
            'conditions' => ' id in ({ids:array}) ',
            'bind' => [
                'ids' => $ids
            ]
        ])->toArray();
    }

}
