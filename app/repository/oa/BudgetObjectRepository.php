<?php

namespace App\Repository\oa;

use App\Library\Enums\GlobalEnums;
use App\Models\oa\BudgetObjectModel;
use App\Modules\Budget\Models\BudgetObject;
use App\Modules\Budget\Models\BudgetObjectOrder;
use App\Modules\Budget\Services\BudgetService;
use App\Repository\BaseRepository;

/**
 * OA预算科目
 * Class BudgetObjectRepository
 * @package App\Repository\oa
 */
class BudgetObjectRepository extends BaseRepository
{
    // 默认的科目名称字段
    protected static $name_field = 'name_th';

    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance($sys_lang = null)
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        if (!is_null($sys_lang)) {
            self::$name_field = get_lang_field_name('name_', $sys_lang, 'th', 'cn');
        }

        return self::$instance;
    }

    /**
     * 获取预算科目: By id
     *
     * @param int $id
     * @param int $is_deleted 0删除 1未删除 2全部
     * @return array
     */
    public function getOneById(int $id, int $is_deleted = GlobalEnums::IS_NO_DELETED)
    {
        $bind       = ['id' => $id];
        $conditions = 'id = :id: ';
        if ($is_deleted == GlobalEnums::IS_NO_DELETED) {
            $bind['is_delete'] = GlobalEnums::IS_NO_DELETED;
            $conditions        .= 'AND is_delete = :is_delete:';
        }
        return BudgetObjectModel::findFirst([
            'conditions' => $conditions,
            'bind'       => $bind,
        ]);
    }

    /**
     * 根据名称搜索
     *
     * @param string $name
     * @param string $base_columns
     * @param integer $is_deleted 0未删除、1已删除、2全部
     * @return array
     */
    public function getListByName(string $name = '', string $base_columns = 'id', int $is_deleted = GlobalEnums::IS_NO_DELETED)
    {
        $conditions = '1 = 1';
        $bind = [];
        if ($is_deleted == GlobalEnums::IS_NO_DELETED) {
            $conditions = 'is_delete = :is_delete:';
            $bind = ['is_delete' => GlobalEnums::IS_NO_DELETED];
        }

        if (!empty($name)) {
            $conditions .= ' AND ' . self::$name_field . ' Like :name_word:';
            $bind['name_word'] = "%$name%";
        }

        return BudgetObjectModel::find([
            'conditions' => $conditions,
            'bind' => $bind,
            'columns' => [$base_columns, self::$name_field . ' AS object_name']
        ])->toArray();
    }

    /**
     * 获得特定筛选条件下的预算科目列表
     * @param array $params 筛选条件组
     * @param bool $is_only_ids 是否只返回ID，默认false
     * @return array
     */
    public function getListByParams($params, $is_only_ids = false)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id');
        $builder->from(BudgetObjectModel::class);
        $builder->where('is_delete = :is_delete:', ['is_delete' => GlobalEnums::IS_NO_DELETED]);
        if (!empty($params['is_purchase'])) {
            $builder->andWhere('is_purchase = :is_purchase:', ['is_purchase' => $params['is_purchase']]);
        }
        $list = $builder->getQuery()->execute()->toArray();
        if ($is_only_ids) {
            return array_column($list, 'id');
        }
        return $list;
    }

    /**
     * 获取预算科目: By ids
     *
     * @param array $ids
     * @param string $columns 字段
     * @return array
     */
    public function getDataByIds(array $ids, string $columns = '*')
    {
        $ids = array_filter($ids);
        if (empty($ids)) {
            return [];
        }

        return BudgetObjectModel::find([
            'columns'    => $columns,
            'conditions' => 'id in ({ids:array})',
            'bind'       => ['ids' => array_values($ids)],
        ])->toArray();
    }

    /**
     * 获取特定条件下的预算科目
     * @param array $params 筛选条件组
     * @param string $lang 语言
     * @return mixed
     */
    public function getBudgetListByParams(array $params, string $lang = 'cn')
    {
        $builder = $this->modelsManager->createBuilder();
        $name = (new BudgetService())->get_lang_column($lang);
        $builder->columns("o.id, o.level_code, o.{$name} as name, o.name_th, o.name_en, o.name_cn, o.is_budget");
        $builder->from(['o' => BudgetObject::class]);
        $builder->join(BudgetObjectOrder::class, 'o.level_code = ot.level_code', 'ot');
        $builder->where('o.is_delete = :is_deleted: and ot.is_delete = :is_deleted:', ['is_deleted' => GlobalEnums::IS_NO_DELETED]);
        //按照订单类型
        if (!empty($params['order_type'])) {
            $builder->andWhere('ot.type = :order_type:', ['order_type' => $params['order_type']]);
        }
        //是否参与预算管控0不1是
        if (isset($params['is_budget'])) {
            $builder->andWhere('o.is_budget = :is_budget:', ['is_budget' => $params['is_budget']]);
        }
        //选择项支持模糊搜索，根据当前系统语言展示对应的预算科目名称
        if (!empty($params['name'])) {
            $builder->andWhere("o.{$name} like :name:", ['name' => '%' . $params['name'] . '%']);
        }
        // 获取条数: 默认 20 条
        if (!empty($params['pageSize'])) {
            $builder->limit($params['pageSize'], 0);
        }
        return $builder->getQuery()->execute()->toArray();
    }
}
