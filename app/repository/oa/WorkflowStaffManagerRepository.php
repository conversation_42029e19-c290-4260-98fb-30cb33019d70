<?php

namespace App\Repository\oa;

use App\Models\oa\StaffManageGroupModel;
use App\Models\oa\WorkflowModel;
use App\Repository\BaseRepository;

/**
 * 审批流分组管理
 * Class WorkflowRepository
 * @package App\Repository\oa
 */
class WorkflowStaffManagerRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 通过分组ids获取信息
     * @param array $group_ids
     * @return array
     */
    public function getGroupByIds($group_ids = [])
    {
        $group_list = [];
        if (!empty($group_ids)) {
            $group_ids = array_values(array_unique($group_ids));
            $group_list = StaffManageGroupModel::find([
                'columns' => 'id, name',
                'conditions' => 'id in({ids:array})',
                'bind' => [
                    'ids' => $group_ids
                ],
            ])->toArray();

            $group_list = array_column($group_list, null, 'id');
        }
        return $group_list;
    }

}
