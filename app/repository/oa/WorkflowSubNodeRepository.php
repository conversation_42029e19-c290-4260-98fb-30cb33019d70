<?php

namespace App\Repository\oa;

use App\Models\oa\WorkflowSubNodeModel;
use App\Repository\BaseRepository;

/**
 * 审批流子节点
 * Class WorkflowSubNodeRepository
 * @package App\Repository\oa
 */
class WorkflowSubNodeRepository extends BaseRepository
{
    private static $instance;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * 获取 WorkflowNodeRepository 实例
     */
    public static function getInstance()
    {
        if (!isset(self::$instance) || !(self::$instance instanceof self)) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    /**
     * 获取审批流节点详情
     * @param $node_id
     * @return array
     */
    public function getSubNodeByNodeId($node_id)
    {
        return WorkflowSubNodeModel::find([
            'conditions' => 'parent_node_id = :parent_node_id:',
            'bind' => ['parent_node_id' => $node_id],
        ])->toArray();
    }
}
