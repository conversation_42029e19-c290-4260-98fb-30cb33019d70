<?php

namespace App\Repository;

use App\Library\Enums\StaffInfoEnums;
use App\Models\backyard\HrJobTitleModel;

/**
 * 职位相关方法
 * Class HrJobTitleRepository
 * @package App\Repository
 */
class HrJobTitleRepository extends BaseRepository
{
    /**
     * 所有职位列表
     * @return array
     */
    public function getJobTitleList()
    {
        $list = HrJobTitleModel::find([
            'columns' => 'id,job_name',
            "conditions" => "status = :status:",
            'bind' => [
                "status" => 1,
            ],
        ])->toArray();

        return $list;
    }

    /**
     * 获取某职位信息
     *
     * @param int $job_title_id
     * @param bool $is_status_open
     * @return array
     */
    public function getJobTitleInfo(int $job_title_id, $is_status_open = true)
    {
        $conditions = 'id = :id:';
        $bind = ['id' => $job_title_id];
        if ($is_status_open) {
            $conditions .= ' AND status = :status:';
            $bind['status'] =  StaffInfoEnums::HR_JOB_STATUS_OPEN;
        }

        return HrJobTitleModel::findFirst([
            'columns' => 'id, job_name',
            'conditions' => $conditions,
            'bind' => $bind,
        ]);
    }

    /**
     * 获取指定id职位列表
     * @param array $job_title_ids
     * @param bool $is_status_open 是否只查询开启状态的
     * @return array
     */
    public function getJobTitleByIds($job_title_ids = [], $is_status_open = true)
    {
        $job_title_list = [];
        if (!empty($job_title_ids)) {
            $job_title_ids = array_values(array_unique($job_title_ids));
            //判断是否要查询有效的
            if ($is_status_open) {
                $conditions = 'status = :status: and id in ({ids:array})';
                $bind = [
                    'status' => 1,
                    'ids' => $job_title_ids
                ];
            } else {
                $conditions = 'id in ({ids:array})';
                $bind = [
                    'ids' => $job_title_ids
                ];
            }
            $job_title_list = HrJobTitleModel::find([
                'columns' => 'id,job_name',
                'conditions' => $conditions,
                'bind' => $bind,
            ])->toArray();

            $job_title_list = array_column($job_title_list, null, 'id');
        }

        return $job_title_list;
    }

    /**
     * 职位搜索
     * @param $search_name
     * @return array
     */
    public function searchJobTitle($search_name)
    {
        $job_title_list = [];
        if (!empty($search_name)) {
            $job_title_list = HrJobTitleModel::find([
                'columns' => 'id,job_name',
                "conditions" => "status = :status: and job_name like :search_name:",
                'bind' => [
                    "status" => 1,
                    "search_name" => "%" . $search_name . "%"
                ],
                'limit' => 10,
                'order' => 'job_name asc',
            ])->toArray();
        }
        return $job_title_list;
    }

}