<?php

namespace App\Repository;

use App\Library\Enums;
use App\Models\backyard\HeadquartersAddressModel;
use App\Models\backyard\SysStoreModel;
use App\Models\backyard\SysManagePieceModel;
use App\Models\backyard\SysManageRegionModel;
use App\Models\backyard\SysStoreTypeModel;
use App\Models\fle\StorePickupVirtualRelationModel;

/**
 * 网点相关方法
 * Class StoreRepository
 * @package App\Repository
 */
class StoreRepository extends BaseRepository
{
    /**
     * 获取指定网点id的网点数据
     * @param array $store_ids
     * @param int $state 网点状态 1:激活, 2:未激活【默认只要激活的】
     * @return array
     */
    public function getStoreListByIds($store_ids = [], $state = Enums::STORE_STATE_ACTIVE)
    {
        $store_list = [];
        if (!empty($store_ids)) {
            $columns = 'id,name,manager_id,manager_name,manager_phone,manager_position_state,manage_region,manage_piece,province_code,city_code,district_code,category,detail_address,postal_code,state,use_state,sorting_no,lat,lng';

            $builder = $this->modelsManager->createBuilder();
            $builder->from(SysStoreModel::class);
            $builder->inWhere('id', $store_ids);
            if (!empty($state)) {
                $builder->andWhere('state = :state:', ['state' => $state]);
            }
            $builder->columns($columns);
            $store_list = $builder->getQuery()->execute()->toArray();
            $store_list = array_column($store_list, null, 'id');
        }
        return $store_list;
    }

    /**
     * 网点搜索
     * @param $search_name
     * @return array
     */
    public function searchStoreList($search_name) {
        $store_list = [];
        if(!empty($search_name)) {
            $store_list = SysStoreModel::find([
                'columns' => 'id,name',
                "conditions" => "name like :search_name: and state = 1",
                'bind' => [
                    "search_name" => "%" . $search_name . "%"
                ],
                'limit' => 10,
                'order' => 'name asc',
            ])->toArray();
        }
        return $store_list;
    }

    /**
     * 根据网点id获取网点信息，激活状态下的
     *
     * @param string $store_id 网点id
     * @param int $state 网点状态 1:激活, 2:未激活【默认只要激活的】
     * @return array
     */
    public function getStoreDetail($store_id, $state = Enums::STORE_STATE_ACTIVE)
    {
        $detail = [];
        if (!empty($store_id)) {
            $builder = $this->modelsManager->createBuilder();
            $builder->from(SysStoreModel::class);

            $builder->where('id = :id:', ['id' => $store_id]);
            if (!empty($state)) {
                $builder->andWhere('state = :state:', ['state' => $state]);
            }
            $store_info = $builder->getQuery()->getSingleResult();
            $detail = !empty($store_info) ? $store_info->toArray() : [];
        }
        return $detail;
    }


    /**
     * 根据网点id查找片区负责人
     * @param string $store_id 网点id
     * @return array
     */
    public function findDmHead(string $store_id)
    {
        if (empty($store_id)) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('smp.manager_id');
        $builder->from(['ss' => SysStoreModel::class]);
        $builder->leftJoin(SysManagePieceModel::class, 'smp.id = ss.manage_piece', 'smp');
        $builder->where('ss.id = :store_id: and ss.state = :state: and smp.manager_id > 0 ', [
            'store_id' => $store_id,
            'state'    => Enums::STORE_STATE_ACTIVE
        ]);
        $return_data = $builder->getQuery()->execute()->toArray();
        return array_column($return_data, 'manager_id');
    }


    /**
     * 根据网点查找大区负责人
     * @param $store_id
     * @return array
     */
    public function findAMEx($store_id)
    {
        if (!$store_id) {
            return [];
        }
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('smr.manager_id');
        $builder->from(['ss' => SysStoreModel::class]);
        $builder->leftjoin(SysManageRegionModel::class, 'smr.id = ss.manage_region', 'smr');
        $builder->where('ss.id = :store_id: and ss.state = :state: and smr.manager_id > 0 ', [
            'store_id' => $store_id,
            'state'    => Enums::STORE_STATE_ACTIVE,
        ]);
        $return_data = $builder->getQuery()->execute()->toArray();
        return array_column($return_data, 'manager_id');

    }

    /**
     * 获取网点类型
     * @param integer $filter_id 网点类型ID
     * @return mixed
     */
    public function getSysStoreType($filter_id = 0)
    {
        $builder = $this->modelsManager->createBuilder();
        $builder->columns('id, code');
        $builder->from(SysStoreTypeModel::class);
        if (!empty($filter_id)) {
            $builder->andWhere('id != :id:', ['id' => $filter_id]);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 按照特定的条件获取网点信息
     *
     * @param array $params 请求参数组
     * @param string $columns
     * @return mixed
     */
    public function searchSysStore(array $params, string $columns = 'id, name, sap_pc_code, category')
    {
        $state = $params['state'] ?? Enums::STORE_STATE_ACTIVE;

        $builder = $this->modelsManager->createBuilder();
        $builder->columns($columns);
        $builder->from(SysStoreModel::class);

        if (!empty($state)) {
            $builder->andWhere('state = :state:', ['state' => $state]);
        }

        //按照网点编号搜索
        $id = $params['store_ids'] ?? [];
        if (!empty($id)) {
            if (is_array($id)) {
                $builder->inWhere('id', $id);
            } else {
                $builder->andWhere('id = :id:', ['id' => $id]);
            }
        }

        //网点类型
        $category = $params['category'] ?? [];
        if (!empty($category)) {
            if (is_array($category)) {
                $builder->inWhere('category', $category);
            } else {
                $builder->andWhere('category = :category:', ['category' => $category]);
            }
        }

        // 名称搜索
        if (!empty($params['name'])) {
            $builder->andWhere('name LIKE :name:', ['name' => "{$params['name']}%"]);
        }

        //不含某些网点
        if (!empty($params['not_in_store_ids'])) {
            $builder->notInWhere('id', $params['not_in_store_ids']);
        }

        //不含某些网点类型下的网点，营业状态正常
        if (!empty($params['not_in_category'])) {

            $builder->andWhere('IF(category NOT IN (' . implode(',', $params['not_in_category']) .'),use_state = 1,use_state >=0)');
        }

        //支持通过网点名称以及网点编码右模糊搜索
        if (!empty($params['name_or_id'])) {
            $builder->andWhere('name LIKE :name_or_id: or id LIKE :name_or_id:', ['name_or_id' => "{$params['name_or_id']}%"]);
        }

        // 获取条数: 默认 20 条
        if (!empty($params['page_num'])) {
            $builder->limit($params['page_num'], 0);
        }
        return $builder->getQuery()->execute()->toArray();
    }

    /**
     * 获取指定总部地址id的数据
     * @param array $ids
     * @return array
     */
    public function getHeaderListByIds($ids = [])
    {
        $list = [];
        if (!empty($ids)) {
            $list = HeadquartersAddressModel::find([
                'columns' => 'id, office_name as name, province_code, city_code, district_code, address as detail_address',
                "conditions" => 'id IN ({ids:array})',
                'bind' => [
                    'ids' => $ids,
                ],
            ])->toArray();

            $list = array_column($list, null, 'id');
        }
        return $list;
    }

    /**
     * 获取指定名称网点列表
     * @param array $store_names 网点名称
     * @param int $state 状态 0 全部 1:激活, 2:未激活
     * @return array
     */
    public function getStoreByNames($store_names = [], $state = Enums::STORE_STATE_ACTIVE)
    {
        $store_list = [];
        $store_names = array_values(array_unique(array_filter($store_names)));
        if (!empty($store_names)) {
            $conditions = 'name IN ({names:array})';
            $bind = ['names' => $store_names];

            if (in_array($state, [Enums::STORE_STATE_ACTIVE, Enums::STORE_STATE_NOT_ACTIVE])) {
                $conditions .= ' AND state = :state:';
                $bind['state'] = $state;
            }

            $store_list = SysStoreModel::find([
                'columns' => 'id, name, sap_pc_code',
                'conditions' => $conditions,
                'bind' => $bind,
            ])->toArray();

            $store_list = array_column($store_list, null, 'name');
        }
        return $store_list;

    }
    
    /**
     * 获取虚拟网点
     * @return mixed
     */
    public function getVirtualStoreIds()
    {
        $virtualRelationInfo = StorePickupVirtualRelationModel::find([
            'columns'    => 'DISTINCT(pickup_virtual_store_id) as pickup_virtual_store_id',
            'conditions' => 'deleted =  0',
        ])->toArray();

        if (empty($virtualRelationInfo)) {
            return [];
        }
        return array_column($virtualRelationInfo, 'pickup_virtual_store_id');
    }

    /**
     * 获取所负责的网点
     *
     * @param $manager_id
     * @return array
     */
    public function getListByManagerId($manager_id)
    {
        $list = [];
        if (!empty($manager_id)) {
            $list = SysStoreModel::find([
                'conditions' => 'manager_id = :manager_id:',
                'bind'       => ['manager_id' => $manager_id],
                'columns'    => ['id AS store_id'],
            ])->toArray();
        }
        return $list;
    }

}
