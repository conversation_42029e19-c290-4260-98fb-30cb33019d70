CREATE TABLE `loan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lno` varchar(255) DEFAULT NULL COMMENT '借款单号-2020040001',
  `lname` varchar(255) DEFAULT NULL COMMENT '借款标题，根据created_at获得。后台没用',
  `create_name` varchar(255) DEFAULT NULL COMMENT '申请人-姓名',
  `create_id` int(11) DEFAULT NULL COMMENT '申请人-工号',
  `create_phone` varchar(255) DEFAULT NULL COMMENT '申请人-电话',
  `create_email` varchar(255) DEFAULT NULL COMMENT '申请人-邮箱',
  `create_department_id` int(11) DEFAULT NULL COMMENT '申请人-部门ID',
  `create_department_name` varchar(255) DEFAULT NULL COMMENT '申请人-部门名字',
  `create_node_department_id` int(11) DEFAULT '0' COMMENT '申请人-二级部门',
  `create_node_department_name` varchar(255) DEFAULT NULL COMMENT '申请人-二级部门名字',
  `create_company_id` int(11) DEFAULT NULL COMMENT '申请人-所属公司ID',
  `create_company_name` varchar(255) DEFAULT NULL COMMENT '申请人-所属公司名字',
  `create_date` date DEFAULT NULL COMMENT '申请人-发起时间',
  `cost_center_id` int(11) DEFAULT NULL COMMENT '申请人-所属成本中心ID',
  `cost_center_name` varchar(255) DEFAULT NULL COMMENT '申请人-所属成功中名字',
  `create_job_title_id` int(11) DEFAULT NULL COMMENT '申请人-所属职位id',
  `create_job_title_name` varchar(255) DEFAULT NULL COMMENT '申请人-所属职位名字',
  `type` tinyint(1) DEFAULT NULL COMMENT '申请事由：1-差旅费，2-采购，3-长期驻外，4-公务活动，5-其他',
  `type_other` varchar(255) DEFAULT NULL COMMENT '其他，5-30个字',
  `pay_type` tinyint(1) DEFAULT NULL COMMENT '付款类型：1-现金，2-银行转账',
  `currency` tinyint(1) DEFAULT NULL COMMENT '币种，1-泰铢，2美元',
  `event_name` varchar(255) DEFAULT NULL COMMENT '事项名称',
  `amount` bigint(20) DEFAULT NULL COMMENT '金额',
  `finished_at` date DEFAULT NULL COMMENT '工作预计完成时间',
  `back_at` date DEFAULT NULL COMMENT '预计还款时间',
  `event_info` varchar(500) DEFAULT NULL COMMENT '事项说明',
  `status` tinyint(1) DEFAULT NULL COMMENT '审核状态-1待审核，2拒绝（驳回），3同意，4撤回',
  `pay_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '支付状态-1待支付，2已支付，3未支付',
  `is_finished` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1不是终止状态，2是终止状态（待定）',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `cancel_reason` varchar(255) DEFAULT NULL COMMENT '撤销原因',
  `refuse_reason` varchar(255) DEFAULT NULL COMMENT '驳回原因',
  `create_job_title_level` tinyint(2) DEFAULT NULL COMMENT '职等',
  `approved_at` datetime DEFAULT NULL COMMENT '审核通过时间',
  `rejected_at` datetime DEFAULT NULL COMMENT '审核驳回时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lno` (`lno`(20)) USING BTREE COMMENT 'lno唯一索引',
  KEY `idx_create_id` (`create_id`) USING BTREE COMMENT 'create_id索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='借款主表';


CREATE TABLE `loan_pay` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_id` int(11) DEFAULT NULL COMMENT '借款-ID',
  `is_sign` tinyint(1) DEFAULT NULL COMMENT '是否签收=1是，2否',
  `not_sign_reason` varchar(255) DEFAULT NULL COMMENT '不签收原因',
  `pay_type` tinyint(1) DEFAULT NULL COMMENT '付款方式-1现金，2TMB，3SCB',
  `sign_name` varchar(255) DEFAULT NULL COMMENT '签收人',
  `pay_date` date DEFAULT NULL COMMENT '付款日期',
  `sign_date` date DEFAULT NULL COMMENT '签收日期',
  `mark` varchar(255) DEFAULT NULL COMMENT '备注',
  `pay_bank_name` varchar(255) DEFAULT NULL COMMENT '银行名字',
  `pay_bank_account` varchar(255) DEFAULT NULL COMMENT '银行账号',
  `create_id` int(11) DEFAULT NULL COMMENT '审核人id',
  `create_name` varchar(255) DEFAULT NULL COMMENT '审核人姓名',
  `create_department_name` varchar(255) DEFAULT NULL COMMENT '审核人部门名称（二级或一级）',
  `create_job_title_name` varchar(255) DEFAULT NULL COMMENT '审核人职位名称',
  PRIMARY KEY (`id`),
  KEY `idx_loan_id` (`loan_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='借款-支付信息';

CREATE TABLE `loan_pay_bank` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_id` int(11) DEFAULT NULL COMMENT 'loan表id',
  `name` varchar(255) DEFAULT NULL COMMENT '收款人姓名',
  `account` varchar(255) DEFAULT NULL COMMENT '收款人账号',
  `bank_type` varchar(255) DEFAULT NULL COMMENT '收款人开户银行',
  PRIMARY KEY (`id`),
  KEY `idx_loan_id` (`loan_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='借款人如果选择银行转账。就保存银行信息';


CREATE TABLE `loan_travel` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_id` int(11) NOT NULL COMMENT '对应借款-id',
  `travel_id` int(11) NOT NULL COMMENT '对应差旅id',
  `serial_no` varchar(32) NOT NULL COMMENT '对应差旅serial_no',
  `reason` varchar(255) DEFAULT NULL COMMENT '申请理由',
  `transport` varchar(255) DEFAULT NULL COMMENT '交通工具',
  `transport_text` varchar(255) DEFAULT NULL COMMENT '交通工具有个其他。4',
  `is_single` tinyint(1) DEFAULT '1' COMMENT '1单程，2往返',
  `start_city` varchar(255) DEFAULT NULL COMMENT '出发城市',
  `end_city` varchar(255) DEFAULT NULL COMMENT '目标城市',
  `start_date` date DEFAULT NULL COMMENT '出发日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `days` varchar(255) DEFAULT NULL COMMENT '出差天数（怕有小数）',
  `pic` varchar(1000) DEFAULT NULL COMMENT '图片',
  `mark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint(1) DEFAULT NULL COMMENT '1审核中，2审批通过，3审批驳回 （大概率没用，过来的都得是通过的）',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '提交时间（导出pdf有）',
  PRIMARY KEY (`id`),
  KEY `idx_loan_id` (`loan_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='借款-差旅信息';


INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (36, 'menu.loan', '借款管理', '借款管理', 0, 1, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (37, 'menu.loan.apply', '借款申请', '借款申请列表', 36, 1, 2, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (38, 'menu.loan.audit', '借款审核', '借款审核列表', 36, 1, 3, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (39, 'action.loan.apply.search', '借款申请查询', '借款申请查询', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (40, 'action.loan.apply.apply', '借款申请添加', '借款申请添加', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (41, 'action.loan.apply.view', '借款申请详情', '借款申请详情', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (42, 'action.loan.apply.edit', '借款申请支付', '借款审核支付', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (43, 'action.loan.apply.download', '借款申请下载', '借款申请下载', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (44, 'action.loan.apply.cancel', '借款申请撤回', '借款申请撤回', 37, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (45, 'action.loan.audit.pay', '借款审核付款', '借款审核付款', 38, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (46, 'action.loan.audit.search', '借款审核查询', '借款审核查询', 38, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (47, 'action.loan.audit.audit', '借款审核', '借款审核', 38, 2, 1, NULL);
INSERT INTO `permission`(`id`, `key`, `name`, `description`, `ancestry`, `type`, `sort`, `created_at`) VALUES (48, 'action.loan.audit.view', '借款审核查看', '借款审核查看', 38, 2, 1, NULL);




INSERT INTO `translations` VALUES ('zh-CN', 'loan_name', '%year%年%month%月员工借款单', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_name', '%year%.%month% employee loan bill', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_name', '%year%.%month%ฟอร์มยื่นกู้พนักงาน', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'traffic_tools.1', '飞机', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'traffic_tools.1', 'airport', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'traffic_tools.1', 'เครื่องบิน', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'traffic_tools.2', '火车', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'traffic_tools.2', 'train', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'traffic_tools.2', 'รถไฟ', NULL, NULL, 1);


INSERT INTO `translations` VALUES ('zh-CN', 'traffic_tools.3', '汽车', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'traffic_tools.3', 'car', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'traffic_tools.3', 'รถ', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'traffic_is_single.1', '单程', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'traffic_is_single.1', 'single trip', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'traffic_is_single.1', 'ไปเดียว', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'traffic_is_single.2', '往返', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'traffic_is_single.2', 'round trip', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'traffic_is_single.2', 'ไปกลับ', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_pay_status.1', '待支付', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_pay_status.1', 'to be paid', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_pay_status.1', 'รอชำระ', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_pay_status.2', '已支付', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_pay_status.2', 'paid', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_pay_status.2', 'ชำระแล้ว', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_pay_status.3', '未支付', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_pay_status.3', 'unpaid', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_pay_status.3', 'ยังไม่ได้ชำระ', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_status.1', '待审核', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_status.1', 'under review', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_status.1', 'ระหว่างพิจารณา', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_status.2', '已驳回', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_status.2', 'dismissed', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_status.2', 'ตีกลับแล้ว', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_status.3', '已通过', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_status.3', 'passed', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_status.3', 'อนุมัติ', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_status.4', '已撤回', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_status.4', 'withdrawn', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_status.4', 'เรียกคืนแล้ว', NULL, NULL, 1);


INSERT INTO `translations` VALUES ('zh-CN', 'purchase_require_amount', '采购金额单次需<=2000泰铢', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'purchase_require_amount', 'Single purchase amount required <= 2000 baht', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'purchase_require_amount', 'ยอดจัดซื้อต้องน้อยกว่า 2000 บาท', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'purchase_require_times', '每人每月采购借款最多3次，已到上限', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'purchase_require_times', 'Each person purchases loans up to 3 times per month, which has reached the upper limit', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'purchase_require_times', 'ผู้กู้เงินจัดซื้อ กู้ได้มากสุด 3 ครั้งต่อเดือน', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_travel_has_benn_exist', '您的出差申请已借过款，不可选择“差旅”。', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_travel_has_benn_exist', 'Your travel application has been borrowed, so cannot choose \'travel expense\'.', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_travel_has_benn_exist', 'ท่านได้ยื่นกู้ทำงานนอกสถานที่แล้ว ไม่สามารถเลือก “ค่าเดินทาง”ได้', NULL, NULL, 1);

INSERT INTO `translations` VALUES ('zh-CN', 'loan_travel_status_error', '您没有审批通过的出差申请，不可选择“差旅”。', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'loan_travel_status_error', 'You don\'t have the approved travel application, so cannot choose  \'travel expense\'.', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'loan_travel_status_error', 'ท่านไม่ผ่านอนุมัติในการยื่นทำงานนอกสถานที่ ไม่สามารถเลือก “ค่าเดินทาง”ได้', NULL, NULL, 1);


INSERT INTO `translations` VALUES ('zh-CN', 'flow_audit_action.6', '撤销', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('en', 'flow_audit_action.6', 'Withdrawn', NULL, NULL, 1);
INSERT INTO `translations` VALUES ('th', 'flow_audit_action.6', 'เรียกคืนแล้ว', NULL, NULL, 1);

--- php app/cli translation gen