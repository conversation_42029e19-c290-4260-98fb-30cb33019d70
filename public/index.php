<?php

use App\Library\Response;
use Phalcon\Mvc\Application;

define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');

try {

    /**
     * Use composer autoloader to load vendor classes
     */
    if (file_exists(BASE_PATH . "/vendor/autoload.php")) {
        include BASE_PATH . "/vendor/autoload.php";
    }

    /**
     * 加载环境变量
     */
    $dotenv = new Dotenv\Dotenv(BASE_PATH);
    $dotenv->load();

    defined('RUNTIME') or define('RUNTIME',env('runtime'));

    date_default_timezone_set(env('default_timezone','Asia/Bangkok'));

    /**
     * Read services
     */
    include APP_PATH . '/config/services.php';

    /**
     * Handle the request
     */
    $application = new Application($di);

    $modules = require APP_PATH . '/config/modules.php';
    /**
     * 多模块
     */
    $application->registerModules($modules);

    /**
     * Handle routes
     */
    include APP_PATH . '/config/router.php';


    /**
     * Include Autoloader
     */
    include APP_PATH . '/config/loader.php';

    /**
     *
     */
    $application->handle()->send();
} catch (\Throwable $e) {
    $result = [
        'code' => 0,
        'message' => RUNTIME == 'dev' ? $e->getMessage() : 'Something went wrong, if the error continue please contact us.',
        'data' => RUNTIME == 'dev' ? $e->getMessage() . $e->getTraceAsString() : null,
    ];

    $di = $application->getDI();
    //错误日志
    $log = array(
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'code' => $e->getCode(),
        'msg' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
    );

    $exception_logger = $di->get('logger');

    // TODO 暂且方案: 处理未注册的Module 报错, 后续优化框架
    if (stripos($log['msg'], "isn't registered") !== false) {
        $exception_logger->info(json_encode($log, JSON_UNESCAPED_UNICODE));
        $result['data'] = null;
        $code = Response::NOT_FOUND;

    } else {
        $exception_logger->error(json_encode($log, JSON_UNESCAPED_UNICODE));
        $code = Response::INTERNAL_SERVER_ERROR;
    }

    $response = $application->response;
    $response->setStatusCode($code);
    $response->setJsonContent($result);
    $response->send();
    exit();
}
